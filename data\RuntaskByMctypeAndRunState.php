<?php
ini_set('display_errors',1);            //错误信息  
ini_set('display_startup_errors',1);    //php启动错误信息  
error_reporting(-1);                    //打印出所有的 错误信息  1
set_time_limit(0);
//var_dump($argv);
include_once( "sys.conf.php" );
//echo $dsn;
try {
    $pdo = new PDO($wdsn, $wuser, $wpassword);
    $pdo->setAttribute(PDO::ATTR_ERRMODE,PDO::ERRMODE_EXCEPTION);
	//echo "ok ";
} catch (PDOException $e) {
    die("数据库连接失败".$e->getMessage());
}

if(isset($argv[1]))
{
 $_REQUEST['mc_type']=$argv[1];
}
$mc_type=0;
if(isset($_REQUEST['mc_type']))
{
   $mc_type=$_REQUEST['mc_type'];
}
if(!in_array($mc_type,array(0,1,2,3,4,5)))
{
   echo "错误的mc_type，请检查后再试。<br/>";
   exit;
}
$status=0;
if(isset($argv[2]))
{
    $status=$argv[2];
}
$sql = "update app_version set TaskRun=? where mc_type = ? limit 1;";
$stmt = $pdo->prepare($sql);
$stmt->execute(array($status,$mc_type));