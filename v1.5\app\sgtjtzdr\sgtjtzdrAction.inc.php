<?php
class sgtjtzdrAction extends AbstractAction
{
	public function __construct()
	{
    	parent::__construct();
	}

	public function checkSession()
	{
	}

	

	function drZhidaojia_dr($params){

		$sql = "select * from sg_zhidaojia_dr where ndate like '%$date%'";
		$zdj = $this->_dao->query($sql);
		if(!empty($zdj))
		{
			exit;
		}

		$sql = "insert into sg_zhidaojia_dr(pinzhong,cityname,price,ndate,adminId,createtime) values('1','西安','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('1','成都','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('1','重庆','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('1','郑州','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('1','兰州','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('2','西安','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('2','成都','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('2','重庆','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('2','郑州','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('2','兰州','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('3','西安','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('3','成都','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('3','重庆','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('3','郑州','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('3','兰州','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW())";
		// echo $sql."<br><br>";
		$this->_dao->execute($sql);
	}

	public function random($length)
    {
        $hash = 'CR-';
        $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789abcdefghijklmnopqrstuvwxyz';
        $max = strlen($chars) - 1;
        mt_srand(( double ) microtime() * 1000000);
        for ($i = 0; $i < $length; $i ++) {
            $hash .= $chars [mt_rand(0, $max)];
        }
        return $hash;
	}
	
	function index($params)
	{
		

		$GUID = $params['GUID'];
		$mode = $params['mode'];
		$this->assign('mode', $mode);
		if($GUID==""){
			alert('GUID不能为空');
            exit;
		}
		$user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
		if(empty($user_infos)){
			alert('用户不存在');
            exit;
		}
		$this->assign('guid', $GUID);
	
		$this->assign('date', date("Y-m-d"));
		
		$total = $this->_dao->getOne( "SELECT COUNT(id) as c FROM sg_data_table_log where uptype = '15' and isdel='0'");
		$page = $params['page'] == '' ? 1 : $params['page'];
		$url = "sgtjtzdr.php";
		$per = 25;
		$start = ( $page - 1 ) * $per;
		unset( $params['page'] );
		$pagebar = pagebar( $url, $params, $per, $page, $total );
		$this->assign( "pagebar", $pagebar );
		$sql = "select * from sg_data_table_log where uptype = '15' and isdel='0' order by createtime desc limit $start, $per";
		$log_info = $this->_dao->query($sql);
		$createuser = array();
		foreach( $log_info as $v ){
			if(!in_array($v['createuser'],$createuser)){
				$createuser[] = $v['createuser'];
			}
		}
		$createuser_str =  implode("','",$createuser);
		$sql = "select id,truename from adminuser where id in ('$createuser_str')";
		$adminuser_info = $this->homeDao->query($sql);
		$admin_name = array();
		foreach( $adminuser_info as $v ){
			$admin_name[$v['id']] = $v['truename'];
		}
		$this->assign('log_info', $log_info);
		$this->assign('date', date("Y-m-d"));
		$this->assign('admin_name', $admin_name);
	}

	//普通文件上传
	function uploadFile($params){
		$upfile = $_FILES['file'];
		$GUID = $params['GUID'];
		$mode = $params['mode'];
		$uptype = $params['uptype'];
		$response = array(
			'Success'  => 1,
			'Message'  => '',
			'GUID'     => $GUID,
			'mode'     => $mode,
		);
		// 不同页面上传类型控制
		switch($params['filetype'])
		{
			case 'excel':
				$uptypes=array(
					'application/vnd.ms-excel',
					'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
				);
				$filetype = "Excel文件";
			break;
			case 'word':
				$uptypes=array(
					'application/msword',
					'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
				);
				$filetype = "Word文件";
			break;
			default:
				$uptypes=array(
					'application/vnd.ms-excel',
					'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
				);
				$filetype = "Excel文件";
			break;
		}

		if($GUID==""){
			$response['Success'] = 0;
			$response['Message'] = 'GUID不能为空';
		}
		if($response['Success']){
			$user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
			if(empty($user_infos)){
				$response['Success'] = 0;
				$response['Message'] = '用户不存在';
			}
			if($response['Success']){
				if ($upfile['error']==0)
				{
					if (is_uploaded_file($upfile['tmp_name'])) {
						if (in_array($upfile['type'], $uptypes)) {
							$extName = strtolower(end(explode('.', $upfile ['name'])));
							$filename = "SG".$uptype."-".$this->random(5); // 设置随机数长度
							$extName=strtolower(end(explode('.', $upfile['name']))); // 取得扩展名
							$name1=date('YmdHis').".".$extName;
							$dir = SGUPLOADFILE;
							if (!file_exists($dir)) {
								if (mkdir($dir, 0777)) {
									$dest =$dir."/".$filename.$name1;
									if (move_uploaded_file($upfile ['tmp_name'], $dest)) {
										
											$par = array(
												"tmp" => $filename.$name1,
												"filename" => $upfile["name"],
												"date" => $params['date'],
												"GUID" => $GUID,
												"uptype" => $uptype,
												"mode" => $mode,
												"sheet" => "0",
											);
											$this->drcbrblg($par);
										
									} else {
										$response['Success'] = 0;
										$response['Message'] = '上传失败，目录权限不足';
									}
								} else {
									$response['Success'] = 0;
									$response['Message'] = '目录不存在，上传失败';
								}
							} else {
								$dest = $dir."/".$filename.$name1;
								if (move_uploaded_file($upfile ['tmp_name'], $dest)) {
										$par = array(
											"tmp" => $filename.$name1,
											"filename" => $upfile["name"],
											"date" => $params['date'],
											"GUID" => $GUID,
											"uptype" => $uptype,
											"mode" => $mode,
											"sheet" => "0",
										);
										$this->drcbrblg($par);
								} else {
									$response['Success'] = 0;
									$response['Message'] = '上传失败，目录权限不足';
								}
							}
						} else {
							$response['Success'] = 0;
							$response['Message'] = '上传失败，检查文件是否是'.$filetype;
						}
					} else {
						$response['Success'] = 0;
						$response['Message'] = '上传失败';
						clearstatcache(); //清除文件缓存信息
					}
				}
				else
				{
					$response['Success'] = 0;
					$response['Message'] = '上传失败';
				}
			}
		}
		echo $this->pri_JSON($response);
		exit;
	}

	//15导入陕钢调价通知
	function drcbrblg($params)
	{

		//西安	咸阳	商洛	铜川	西安	榆林	银川	延安	韩城	渭北	渭南	运城	临汾	太原	三门峡	安康	庆阳	宝鸡	平凉

		$sgsheet=array(
			'F6'=>array('螺纹钢','HRB400E','φ18-22','西咸片区','西安'),
			'G6'=>array('螺纹钢','HRB400E','φ18-22','西咸片区','咸阳'),
			'H6'=>array('螺纹钢','HRB400E','φ18-22','商洛片区','商洛'),
			'I6'=>array('螺纹钢','HRB400E','φ18-22','铜川片区','铜川'),
			'J6'=>array('螺纹钢','HRB400E','φ18-22','工程事业部','西安'),
			'K6'=>array('螺纹钢','HRB400E','φ18-22','榆林片区','榆林'),
			'L6'=>array('螺纹钢','HRB400E','φ18-22','榆林片区','银川'),
			'M6'=>array('螺纹钢','HRB400E','φ18-22','延安片区','延安'),
			'N6'=>array('螺纹钢','HRB400E','φ18-22','环韩城片区','韩城'),
			'O6'=>array('螺纹钢','HRB400E','φ18-22','环韩城片区','渭北'),
			'P6'=>array('螺纹钢','HRB400E','φ18-22','环韩城片区','渭南'),
			'Q6'=>array('螺纹钢','HRB400E','φ18-22','环韩城片区','运城'),
			'R6'=>array('螺纹钢','HRB400E','φ18-22','环韩城片区','临汾'),
			'S6'=>array('螺纹钢','HRB400E','φ18-22','环韩城片区','太原'),
			'T6'=>array('螺纹钢','HRB400E','φ18-22','环韩城片区','三门峡'),
			'U6'=>array('螺纹钢','HRB400E','φ18-22','安康片区','安康'),
			'V6'=>array('螺纹钢','HRB400E','φ18-22','庆阳片区','庆阳'),
			'W6'=>array('螺纹钢','HRB400E','φ18-22','宝平片区','宝鸡'),
			'X6'=>array('螺纹钢','HRB400E','φ18-22','宝平片区','平凉'),

			'F7'=>array('线材','HPB300','φ8-φ10','西咸片区','西安'),
			'G7'=>array('线材','HPB300','φ8-φ10','西咸片区','咸阳'),
			'H7'=>array('线材','HPB300','φ8-φ10','商洛片区','商洛'),
			'I7'=>array('线材','HPB300','φ8-φ10','铜川片区','铜川'),
			'J7'=>array('线材','HPB300','φ8-φ10','工程事业部','西安'),
			'K7'=>array('线材','HPB300','φ8-φ10','榆林片区','榆林'),
			'L7'=>array('线材','HPB300','φ8-φ10','榆林片区','银川'),
			'M7'=>array('线材','HPB300','φ8-φ10','延安片区','延安'),
			'N7'=>array('线材','HPB300','φ8-φ10','环韩城片区','韩城'),
			'O7'=>array('线材','HPB300','φ8-φ10','环韩城片区','渭北'),
			'P7'=>array('线材','HPB300','φ8-φ10','环韩城片区','渭南'),
			'Q7'=>array('线材','HPB300','φ8-φ10','环韩城片区','运城'),
			'R7'=>array('线材','HPB300','φ8-φ10','环韩城片区','临汾'),
			'S7'=>array('线材','HPB300','φ8-φ10','环韩城片区','太原'),
			'T7'=>array('线材','HPB300','φ8-φ10','环韩城片区','三门峡'),
			'U7'=>array('线材','HPB300','φ8-φ10','安康片区','安康'),
			'V7'=>array('线材','HPB300','φ8-φ10','庆阳片区','庆阳'),
			'W7'=>array('线材','HPB300','φ8-φ10','宝平片区','宝鸡'),
			'X7'=>array('线材','HPB300','φ8-φ10','宝平片区','平凉'),

			'F8'=>array('盘螺','HRB400E','φ8-φ10','西咸片区','西安'),
			'G8'=>array('盘螺','HRB400E','φ8-φ10','西咸片区','咸阳'),
			'H8'=>array('盘螺','HRB400E','φ8-φ10','商洛片区','商洛'),
			'I8'=>array('盘螺','HRB400E','φ8-φ10','铜川片区','铜川'),
			'J8'=>array('盘螺','HRB400E','φ8-φ10','工程事业部','西安'),
			'K8'=>array('盘螺','HRB400E','φ8-φ10','榆林片区','榆林'),
			'L8'=>array('盘螺','HRB400E','φ8-φ10','榆林片区','银川'),
			'M8'=>array('盘螺','HRB400E','φ8-φ10','延安片区','延安'),
			'N8'=>array('盘螺','HRB400E','φ8-φ10','环韩城片区','韩城'),
			'O8'=>array('盘螺','HRB400E','φ8-φ10','环韩城片区','渭北'),
			'P8'=>array('盘螺','HRB400E','φ8-φ10','环韩城片区','渭南'),
			'Q8'=>array('盘螺','HRB400E','φ8-φ10','环韩城片区','运城'),
			'R8'=>array('盘螺','HRB400E','φ8-φ10','环韩城片区','临汾'),
			'S8'=>array('盘螺','HRB400E','φ8-φ10','环韩城片区','太原'),
			'T8'=>array('盘螺','HRB400E','φ8-φ10','环韩城片区','三门峡'),
			'U8'=>array('盘螺','HRB400E','φ8-φ10','安康片区','安康'),
			'V8'=>array('盘螺','HRB400E','φ8-φ10','庆阳片区','庆阳'),
			'W8'=>array('盘螺','HRB400E','φ8-φ10','宝平片区','宝鸡'),
			'X8'=>array('盘螺','HRB400E','φ8-φ10','宝平片区','平凉'),

//郑州		洛阳		南阳		汉中	巴中	广元	绵阳	南充	达州	成都	重庆	万州	陇南	兰州	天水	陇西

			'F11'=>array('螺纹钢','HRB400E','φ18-22','郑州片区','郑州'),
			
			'H11'=>array('螺纹钢','HRB400E','φ18-22','郑州片区','洛阳'),
			
			'J11'=>array('螺纹钢','HRB400E','φ18-22','郑州片区','南阳'),
			
			'L11'=>array('螺纹钢','HRB400E','φ18-22','环汉中片区','汉中'),
			'M11'=>array('螺纹钢','HRB400E','φ18-22','环汉中片区','巴中'),
			'N11'=>array('螺纹钢','HRB400E','φ18-22','广元片区','广元'),
			'O11'=>array('螺纹钢','HRB400E','φ18-22','绵阳片区','绵阳'),
			'P11'=>array('螺纹钢','HRB400E','φ18-22','南充片区','南充'),
			'Q11'=>array('螺纹钢','HRB400E','φ18-22','南充片区','达州'),
			'R11'=>array('螺纹钢','HRB400E','φ18-22','成都片区','成都'),
			'S11'=>array('螺纹钢','HRB400E','φ18-22','重庆片区','重庆'),
			'T11'=>array('螺纹钢','HRB400E','φ18-22','重庆片区','万州'),
			'U11'=>array('螺纹钢','HRB400E','φ18-22','陇南片区','陇南'),
			'V11'=>array('螺纹钢','HRB400E','φ18-22','甘青藏片区','兰州'),
			'W11'=>array('螺纹钢','HRB400E','φ18-22','天水片区','天水'),
			'X11'=>array('螺纹钢','HRB400E','φ18-22','天水片区','陇西'),

			'F12'=>array('线材','HPB300','φ8-φ10','郑州片区','郑州'),
			
			'H12'=>array('线材','HPB300','φ8-φ10','郑州片区','洛阳'),
			
			'J12'=>array('线材','HPB300','φ8-φ10','郑州片区','南阳'),
			
			'L12'=>array('线材','HPB300','φ8-φ10','环汉中片区','汉中'),
			'M12'=>array('线材','HPB300','φ8-φ10','环汉中片区','巴中'),
			'N12'=>array('线材','HPB300','φ8-φ10','广元片区','广元'),
			'O12'=>array('线材','HPB300','φ8-φ10','绵阳片区','绵阳'),
			'P12'=>array('线材','HPB300','φ8-φ10','南充片区','南充'),
			'Q12'=>array('线材','HPB300','φ8-φ10','南充片区','达州'),
			'R12'=>array('线材','HPB300','φ8-φ10','成都片区','成都'),
			'S12'=>array('线材','HPB300','φ8-φ10','重庆片区','重庆'),
			'T12'=>array('线材','HPB300','φ8-φ10','重庆片区','万州'),
			'U12'=>array('线材','HPB300','φ8-φ10','陇南片区','陇南'),
			'V12'=>array('线材','HPB300','φ8-φ10','甘青藏片区','兰州'),
			'W12'=>array('线材','HPB300','φ8-φ10','天水片区','天水'),
			'X12'=>array('线材','HPB300','φ8-φ10','天水片区','陇西'),

			'F13'=>array('盘螺','HRB400E','φ8-φ10','郑州片区','郑州'),
			
			'H13'=>array('盘螺','HRB400E','φ8-φ10','郑州片区','洛阳'),
			
			'J13'=>array('盘螺','HRB400E','φ8-φ10','郑州片区','南阳'),
			
			'L13'=>array('盘螺','HRB400E','φ8-φ10','环汉中片区','汉中'),
			'M13'=>array('盘螺','HRB400E','φ8-φ10','环汉中片区','巴中'),
			'N13'=>array('盘螺','HRB400E','φ8-φ10','广元片区','广元'),
			'O13'=>array('盘螺','HRB400E','φ8-φ10','绵阳片区','绵阳'),
			'P13'=>array('盘螺','HRB400E','φ8-φ10','南充片区','南充'),
			'Q13'=>array('盘螺','HRB400E','φ8-φ10','南充片区','达州'),
			'R13'=>array('盘螺','HRB400E','φ8-φ10','成都片区','成都'),
			'S13'=>array('盘螺','HRB400E','φ8-φ10','重庆片区','重庆'),
			'T13'=>array('盘螺','HRB400E','φ8-φ10','重庆片区','万州'),
			'U13'=>array('盘螺','HRB400E','φ8-φ10','陇南片区','陇南'),
			'V13'=>array('盘螺','HRB400E','φ8-φ10','甘青藏片区','兰州'),
			'W13'=>array('盘螺','HRB400E','φ8-φ10','天水片区','天水'),
			'X13'=>array('盘螺','HRB400E','φ8-φ10','天水片区','陇西'),



		);
		$file = SGUPLOADFILE."/".$params["tmp"];
		$filename = mb_convert_encoding($params["filename"],"GB2312","UTF-8");
		$date = $params["date"];
		$GUID = $params['GUID'];
		$uptype = $params['uptype'];
		$mode = $params['mode'];//1=客户端，2=安卓，3=iOS
		$response = array(
			'Success'  => 1,
			'Message'  => '',
		);
		if($GUID==""){
			$response["Success"] = 0;
			$response['Message'] = 'GUID不能为空';
		}
		if($response["Success"])
		{
			$user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
			if(empty($user_infos)){
				$response["Success"] = 0;
				$response['Message'] = '用户不存在';
			}
			$date = mysql_real_escape_string($params["date"]);
			// if(empty($date))
			// {
			// 	$response["Success"] = 0;
			// 	$response['Message'] = '年份出错';
			// }
			if($response["Success"])
			{
				require_once "../PHPExcel/PHPExcel.php";
				$type = pathinfo($file); 
				$type = strtolower($type["extension"]);
				if ($type=='xlsx') { 
					$type='Excel2007'; 
				}elseif($type=='xls') { 
					$type = 'Excel5';
				}
				$sheetList = explode(',', $params["sheet"]);
				if(trim($sheetList[0])=="")
				{
					$response["Success"] = 0;
					$response['Message'] = '未选择工作表';
				}
				if($response["Success"])
				{
					$objReader = PHPExcel_IOFactory::createReader($type);//判断使用哪种格式
					$objReader ->setReadDataOnly(true); //只读取数据,会智能忽略所有空白行,这点很重要！！！
					$objPHPExcel = $objReader->load($file); //加载Excel文件
					$sheetNames = $objPHPExcel->getSheetNames();//所有的工作表名
					$sheetCount = $objPHPExcel->getSheetCount();//获取sheet工作表总个数
					// print_r($sheetNames);
					// echo $sheetCount;
					$flag = true;
					// print_r($sheetList);
					$tmpsheetName = array();
					foreach($sheetList as $sheet_index)
					{
						$sheet = $objPHPExcel->getSheet($sheet_index);
						// $sheet_array = $objPHPExcel->getSheet($sheet_index)->toArray();
						//获取当前工作表最大行数
						//$rows = $sheet->getHighestRow();
						//获取当前工作表最大列数,返回的是最大的列名，如：B 
						//$cols = $sheet->getHighestColumn();

						//echo $cols;exit;
						$basesql = "insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,dta_3,dta_4,dta_5,dta_6,createtime,createuser) values";
						$sqlstr='';
						foreach($sgsheet as $key=> $sginfo)
						{
							$zcb1 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getFormattedValue()));
							if(empty($sqlstr))
							{
								$sqlstr= "('SGtjtz','陕钢调价通知','$date','$sginfo[0]','$sginfo[1]','$sginfo[2]','$sginfo[3]','$sginfo[4]','$zcb1',NOW(),'".$user_infos['Uid']."')";
							}
							else
							{
								$sqlstr.= ",('SGtjtz','陕钢调价通知','$date','$sginfo[0]','$sginfo[1]','$sginfo[2]','$sginfo[3]','$sginfo[4]','$zcb1',NOW(),'".$user_infos['Uid']."')";
							}
                          
						}
						$sqlstr.=';'; 
						$tmpsheetName[] = mb_convert_encoding($sheetNames[$sheet_index],"GB2312","UTF-8");

						//$valsql = "('SGtjtz','陕钢调价通知','$date','$lgbycl','$lgljcl','$zgbycl','$zgljcl','$dwcb1','$zcb1','$dwcb2','$zcb2',NOW(),'".$user_infos['Uid']."')";
						$this->_dao->execute("delete from sg_data_table where dta_ym='$date' and dta_type='SGtjtz'");
						$this->_dao->execute($basesql.$sqlstr);
					}
					$sheetName = implode(',',$tmpsheetName);
					$this->_dao->execute("insert into sg_data_table_log(uptype,filepath,filename,sheetname,createtime,createuser,uptypedate) value('$uptype','$file','$filename','$sheetName',NOW(),'".$user_infos['Uid']."','$date')");
					$response['Success'] = "1";
					$response['Message'] = "导入成功";
				}
			}
		}
		echo $this->pri_JSON($response);
		exit;
	}
	//读取工作表并返回
	function readSheet($file, $year)
	{
		ini_set('memory_limit', '512M');
		require_once "../PHPExcel/PHPExcel.php";
		$type = pathinfo($file); 
		$type = strtolower($type["extension"]);
		if ($type=='xlsx') { 
			$type='Excel2007'; 
		}elseif($type=='xls') { 
			$type = 'Excel5';
		}
		
		$objReader = PHPExcel_IOFactory::createReader($type);//判断使用哪种格式
		$objReader ->setReadDataOnly(true); //只读取数据,会智能忽略所有空白行,这点很重要！！！
		$objPHPExcel = $objReader->load($file); //加载Excel文件
		// $sheets = $objPHPExcel->getSheetNames();
		$cnt = 0;
		foreach ($objPHPExcel->getWorksheetIterator() as $sheet) {
			if ($sheet->getSheetState() === 'hidden') {
				$hiddenSheet[$cnt] = $sheet->getTitle();
			}else{
				$sheetName[$cnt] = $sheet->getTitle();
			}
			$cnt++;
		}
		return $sheetName;
	}



	private function pri_JSON($array)
	{
		$this->pri_arrayRecursive($array, 'urlencode', true);
		$json = json_encode($array);
		return iconv("GB2312", "UTF-8", urldecode($json));
	}

	private function pri_arrayRecursive(&$array, $function, $apply_to_keys_also = false)
	{
		static $recursive_counter = 0;
		if (++$recursive_counter > 1000) {
			die('possible deep recursion attack');
		}
		foreach ($array as $key => $value) {
			if (is_array($value)) {
				$this->pri_arrayRecursive($array[$key], $function, $apply_to_keys_also);
			} else {
				$array[$key] = $function($value);
			}
			if ($apply_to_keys_also && is_string($key)) {
				$new_key = $function($key);
				if ($new_key != $key) {
					$array[$new_key] = $array[$key];
					unset($array[$key]);
				}
			}
		}
		$recursive_counter--;
	}

	//数组转码
	public function array_iconv($str, $in_charset="utf-8", $out_charset="gb2312")
	{
		if (is_array($str)) {
			foreach ($str as $k => $v) {
				$str[$k] = $this->array_iconv($v);
			}
			return $str;
		} else {
			if (is_string($str)) {
				return mb_convert_encoding($str, $out_charset, $in_charset);
			} else {
				return $str;
			}
		}
	}

	public function isHan($str)
	{
		if($str=="")
		{
			return false;
		}
		else
		{
			if (preg_match_all("/^([\x81-\xfe][\x40-\xfe])+$/", $str, $match))
			{
				//全是中文
				return true;
			}
			else
			{
				//不全是中文
				return false;
			}
		}			
	}

	//去除所有空格、换行等
	public function trimAll($str)
	{
		$oldchar=array(" ","　","\t","\n","\r");
		$newchar=array("","","","","");
		return str_replace($oldchar,$newchar,"$str");
	}
}

?>