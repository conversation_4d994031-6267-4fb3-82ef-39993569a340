<?php
//周报数据
$GLOBALS['weeklydata'] =  array(
    array(//第一行
	    array(
		    "value" => "序号",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "单位",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "项目",
		    "colspan" => "2",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "本周",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "上周",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "环比",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "环比影响",
		    "colspan" => "2",
		    "rowspan" => "1",
		)
    ),
    array(//第二行
	    array(
		    "value" => "一、产量",
		    "colspan" => "1",
		    "rowspan" => "4",
		),
	    array(
		    "value" => "万吨",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "钢坯产量",
		    "colspan" => "1",
		    "rowspan" => "2",
		),
	    array(
		    "value" => "龙钢公司",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		)
    ),
    array(//第三行
	    array(
		    "value" => "万吨",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "汉钢公司",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		)
    ),
    array(//第四行
	    array(
		    "value" => "万吨",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "钢材产量",
		    "colspan" => "1",
		    "rowspan" => "2",
		),
	    array(
		    "value" => "龙钢公司",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		)
    ),
    array(//第五行
	    array(
		    "value" => "万吨",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "汉钢公司",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		)
    ),
    array(//第六行
	    array(
		    "value" => "二、销量",
		    "colspan" => "1",
		    "rowspan" => "2",
		),
	    array(
		    "value" => "万吨",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "钢材结算销量",
		    "colspan" => "1",
		    "rowspan" => "2",
		),
	    array(
		    "value" => "龙钢公司",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "2",
		)
    ),
    array(//第七行
	    array(
		    "value" => "万吨",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "汉钢公司",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		)
    ),
    array(//第八行
	    array(
		    "value" => "三、售价",
		    "colspan" => "1",
		    "rowspan" => "5",
		),
	    array(
		    "value" => "元/吨",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "二级售价（含税）",
		    "colspan" => "1",
		    "rowspan" => "2",
		),
	    array(
		    "value" => "龙钢公司",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		)
    ),
    array(//第九行
	    array(
		    "value" => "元/吨",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "汉钢公司",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		)
    ),
    array(//第10行
	    array(
		    "value" => "元/吨",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "一级售价（不含税不含运费）",
		    "colspan" => "1",
		    "rowspan" => "3",
		),
	    array(
		    "value" => "龙钢公司",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "2",
		)
    ),
    array(//第11行
	    array(
		    "value" => "元/吨",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "汉钢公司",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		)
    ),
    array(//第12行
	    array(
		    "value" => "元/吨",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "陕钢-加权",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		)
    ),
    array(//第13行
	    array(
		    "value" => "四、成本",
		    "colspan" => "1",
		    "rowspan" => "7",
		),
	    array(
		    "value" => "元/吨",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "生产成本",
		    "colspan" => "1",
		    "rowspan" => "3",
		),
	    array(
		    "value" => "龙钢公司",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "2",
		)
    ),
    array(//第14行
	    array(
		    "value" => "元/吨",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "汉钢公司",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		)
    ),
    array(//第15行
	    array(
		    "value" => "元/吨",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "陕钢-加权",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		)
    ),
    array(//第16行
	    array(
		    "value" => "万吨",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "吨钢期间费用",
		    "colspan" => "1",
		    "rowspan" => "2",
		),
	    array(
		    "value" => "龙钢公司",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "2",
		)
    ),
    array(//第17行
	    array(
		    "value" => "万吨",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "汉钢公司",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		)
    ),
    array(//第18行
	    array(
		    "value" => "万吨",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "完全成本",
		    "colspan" => "1",
		    "rowspan" => "2",
		),
	    array(
		    "value" => "龙钢公司",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		)
    ),
    array(//第19行
	    array(
		    "value" => "万吨",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "汉钢公司",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		)
    ),
    array(//第20行
	    array(
		    "value" => "六、其他",
		    "colspan" => "1",
		    "rowspan" => "4",
		),
	    array(
		    "value" => "元/吨",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "检斤检尺差异",
		    "colspan" => "1",
		    "rowspan" => "2",
		),
	    array(
		    "value" => "龙钢公司",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		)
    ),
    array(//第21行
	    array(
		    "value" => "元/吨",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "汉钢公司",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		)
    ),
    array(//第22行
	    array(
		    "value" => "元/吨",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "税金及附加等",
		    "colspan" => "1",
		    "rowspan" => "2",
		),
	    array(
		    "value" => "龙钢公司",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		)
    ),
    array(//第23行
	    array(
		    "value" => "元/吨",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "汉钢公司",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		)
    ),
    array(//第24行
	    array(
		    "value" => "七、利润",
		    "colspan" => "1",
		    "rowspan" => "4",
		),
	    array(
		    "value" => "元/吨",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "吨钢利润",
		    "colspan" => "1",
		    "rowspan" => "2",
		),
	    array(
		    "value" => "龙钢公司",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		)
    ),
    array(//第25行
	    array(
		    "value" => "元/吨",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "汉钢公司",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		)
    ),
    array(//第26行
	    array(
		    "value" => "万元",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "利润总额",
		    "colspan" => "1",
		    "rowspan" => "2",
		),
	    array(
		    "value" => "龙钢公司",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		)
    ),
    array(//第27行
	    array(
		    "value" => "万元",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "汉钢公司",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		)
    ),
    array(//第28行
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "万吨",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "产量",
		    "colspan" => "1",
		    "rowspan" => "2",
		),
	    array(
		    "value" => "龙钢公司",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		)
    ),
    array(//第29行
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "万吨",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "汉钢公司",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		)
    ),
    array(//第30行
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "万元",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "期间费用计划",
		    "colspan" => "1",
		    "rowspan" => "2",
		),
	    array(
		    "value" => "龙钢公司",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		)
    ),
    array(//第31行
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "万元",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "汉钢公司",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		),
	    array(
		    "value" => "",
		    "colspan" => "1",
		    "rowspan" => "1",
		)
    ),
);

class sgfinancialweeklyreportAction extends AbstractAction{
   public function __construct(){
    parent::__construct();    
	}

	//涨跌的函数
	protected function zhangdie($data, $type='0')
	{
			if ($data>0) {
					$data = '<font color=red>↑'.abs($data).'</font>';
			} elseif ($data<0) {
					$data = '<font color=green>↓'.abs($data).'</font>';
			} elseif ($data==0) {
					if ($type=='-') {
							$data = '<font color=black>―</font>';
					} else {
							$data = '<font color=black>'.$data.'</font>';
					}
			}
			return $data;
	}

	//获取周期的日期
	protected function get_cycle_time_by_day( $date ){
		$return_array = array();
		//周一和上周一
		$workday = $date;
		$week = date('N', strtotime($workday));  
		if(1==$week){
			$weekday = date('Y-m-d',strtotime( $workday.'-0 monday')); 
			$lastweekday = date('Y-m-d',strtotime( $workday.'-1 monday'));
		}else{
			$weekday = date('Y-m-d',strtotime( $workday.'-1 monday')); 
			$lastweekday = date('Y-m-d',strtotime( $workday.'-2 monday'));
		}
		//上周五和上上周五
		$weekday = date('Y-m-d',strtotime( $weekday.'-3 days'));
		$lastweekday = date('Y-m-d',strtotime( $lastweekday.'-3 days'));
		//本周四和上周四
		$weekendday = date('Y-m-d',strtotime( $weekday.'+6 days'));
		$lastweekendday = date('Y-m-d',strtotime( $lastweekday.'+6 days'));

		$this_month_day = date("Y-m-01",strtotime( $weekendday ) );
		$last_month_day = date("Y-m-01",strtotime( $this_month_day."-1 days" ) );

		$this_year_start = date("Y-01-01",strtotime( $weekendday ) );
		$last_year_start = date("Y-01-01",strtotime( $weekendday ."-1 years") );
		$last_year_today = date("Y",strtotime( $workday ."-1 years") ).date("-m-d",strtotime( $workday ) );
		$last_year_this_month_day = date("Y",strtotime( $workday ."-1 years") ).date("-m-01",strtotime( $workday ) );
		$last_year_next_month_day = date("Y-m-01",strtotime( $last_year_this_month_day ."+1 month") ) ;
		$return_array = array(
				"today" => $date,
				"weekday" => $weekday,
				"lastweekday" => $lastweekday,
				"this_month_day" => $this_month_day,
				"last_month_day" => $last_month_day,
				"this_year_start" => $this_year_start,
				"last_year_start" => $last_year_start,
				"weekendday" => $weekendday,
				"lastweekendday" => $lastweekendday
			);
		return $return_array;

	}

	public function index($params)
	{
		$date = $params['date'];
		if( empty( $date ) ){
			$date = date("Y-m-d");
		}
		$time_array = $this -> get_cycle_time_by_day( $date );
		//print_r( $time_array );
		$weeklydata = $GLOBALS['weeklydata'];
		$weeklydata[0][3]['value'] = date("n月", strtotime( $time_array['last_month_day'] ) );
		$weeklydata[0][7]['value'] = date("n月", strtotime( $time_array['this_month_day'] ) )."<span color='red'>日报累计</span>";

		$sg_hg_var_type = "SG_CBYBB_HG";
		$sg_lg_var_type = "SG_CBYBB_LG";
		$sheetname = "炼钢";
		$weeklydata_1_4 = $this -> _dao -> get_chenben_month_report( date("Y-m",strtotime($time_array['last_month_day']) ), $sheetname );
		$weeklydata[1][4]['value'] = round( $weeklydata_1_4[$sg_lg_var_type], 2 );
		$weeklydata[2][2]['value'] = round( $weeklydata_1_4[$sg_hg_var_type], 2 );

		$sheetname = "轧钢";
		$weeklydata_3_3 = $this -> _dao -> get_chenben_month_report( date("Y-m",strtotime($time_array['last_month_day']) ), $sheetname );
		$weeklydata[3][3]['value'] = round( $weeklydata_3_3[$sg_lg_var_type], 2 );
		$weeklydata[4][2]['value'] = round( $weeklydata_3_3[$sg_hg_var_type], 2 );

		$weeklydata[5][4]['value'] = 'erp';
		$weeklydata[6][2]['value'] = 'erp';
		$weeklydata[7][4]['value'] = 'erp';
		$weeklydata[8][2]['value'] = 'erp';

		$sheetname = "轧钢";
		$dta_1 = '六、成本费用';
		$weeklydata_12_4 = $this -> _dao -> get_detail_month_report( date("Y-m",strtotime($time_array['last_month_day']) ), $sheetname, $dta_1 );
		$weeklydata[12][4]['value'] = round( $weeklydata_12_4[$sg_lg_var_type], 2 );
		$weeklydata[13][2]['value'] = round( $weeklydata_12_4[$sg_hg_var_type], 2 );
		$weeklydata[14][2]['value'] = round( ($weeklydata_3_3[$sg_lg_var_type] * $weeklydata_12_4[$sg_lg_var_type] + $weeklydata_3_3[$sg_hg_var_type] * $weeklydata_12_4[$sg_hg_var_type]) /( $weeklydata_3_3[$sg_lg_var_type] + $weeklydata_3_3[$sg_hg_var_type] ) , 2 );

		$dta_type = "SG_SXFYLR";
		$sanxiangfeiyong = $this -> _dao -> get_sanxiangfeiyong( date("Y-m",strtotime($time_array['last_month_day']) ), $dta_type );
		$this_sanxiangfeiyong = $this -> _dao -> get_sanxiangfeiyong( date("Y-m",strtotime($time_array['this_month_day']) ), $dta_type );
		$lastweek_sanxiangfeiyong = $this -> _dao -> get_sanxiangfeiyong( date("Y-m",strtotime($time_array['lastweekendday']) ), $dta_type );
		$weeklydata[15][3]['value'] = round( ($sanxiangfeiyong['dta_1'] + $sanxiangfeiyong['dta_2'] + $sanxiangfeiyong['dta_3'] ) /$weeklydata_1_4[$sg_lg_var_type] , 2 );
		$weeklydata[16][2]['value'] = round( ($sanxiangfeiyong['dta_4'] + $sanxiangfeiyong['dta_5'] + $sanxiangfeiyong['dta_6'] ) /$weeklydata_1_4[$sg_hg_var_type] , 2 );

		$weeklydata[17][3]['value'] = round( $weeklydata[12][4]['value'] + $weeklydata[15][3]['value'] , 2 );
		$weeklydata[18][2]['value'] = round( $weeklydata[13][2]['value'] + $weeklydata[16][2]['value'] , 2 );

		$weeklydata[19][4]['value'] = 20;
		$weeklydata[20][2]['value'] = 40;
		$weeklydata[21][3]['value'] = 20;
		$weeklydata[22][2]['value'] = 40;

		$weeklydata[19][5]['value'] = 20;
		$weeklydata[20][3]['value'] = 40;
		$weeklydata[21][4]['value'] = 20;
		$weeklydata[22][3]['value'] = 40;

		$weeklydata[19][6]['value'] = 20;
		$weeklydata[20][4]['value'] = 40;
		$weeklydata[21][5]['value'] = 20;
		$weeklydata[22][4]['value'] = 40;

		$weeklydata[19][7]['value'] = round( $weeklydata[19][5]['value'] - $weeklydata[19][6]['value'], 2 );
		$weeklydata[20][5]['value'] = round( $weeklydata[20][3]['value'] - $weeklydata[20][4]['value'], 2 );
		$weeklydata[21][6]['value'] = round( $weeklydata[21][4]['value'] - $weeklydata[21][5]['value'], 2 );
		$weeklydata[22][5]['value'] = round( $weeklydata[22][3]['value'] - $weeklydata[22][4]['value'], 2 );

		$weeklydata[19][8]['value'] = 20;
		$weeklydata[20][6]['value'] = 40;
		$weeklydata[21][7]['value'] = 20;
		$weeklydata[22][6]['value'] = 40;

		$weeklydata[27][4]['value'] = $weeklydata[1][4]['value'];
		$weeklydata[28][3]['value'] = $weeklydata[2][2]['value'];

		$weeklydata[29][4]['value'] = round( $sanxiangfeiyong['dta_1'] + $sanxiangfeiyong['dta_2'] + $sanxiangfeiyong['dta_3'] , 2 );
		$weeklydata[30][3]['value'] = round( $sanxiangfeiyong['dta_4'] + $sanxiangfeiyong['dta_5'] + $sanxiangfeiyong['dta_6'] , 2 );

		$dayly_dta_type = "SGcbrblg";
		$dayly_dta_vartype = "龙钢成本日报";
		$dayly_report = $this -> _dao -> get_dayly_report( $time_array['this_month_day'], $dayly_dta_type, $dayly_dta_vartype );
		$weeklydata[1][8]['value'] = round( $dayly_report['dta_2'], 2 );
		$weeklydata[3][7]['value'] = round( $dayly_report['dta_4'], 2 );
		$weeklydata[12][8]['value'] = round( $dayly_report['dta_7'], 2 );

		$weekly_lg_dta_type = "SGcbzblg";
		$weekly_lg_lg_dta_vartype = "龙钢成本周报-炼钢";
		$dta_1 = '本周产量';
		$weekly_lg_lg_bzcl_report = $this -> _dao -> get_weekly_report( $time_array['weekday'], date("Y-m-d"), $weekly_lg_dta_type, $weekly_lg_lg_dta_vartype, $dta_1 );
		$weeklydata[1][5]['value'] = round( $weekly_lg_lg_bzcl_report['dta_2'], 2 );
		$last_weekly_lg_lg_bzcl_report = $this -> _dao -> get_weekly_report( $time_array['lastweekday'], $time_array['lastweekendday'], $weekly_lg_dta_type, $weekly_lg_lg_dta_vartype, $dta_1 );
		$weeklydata[1][6]['value'] = round( $last_weekly_lg_lg_bzcl_report['dta_2'], 2 );

		$weekly_dta_type = "SGcbzbhg";
		$weekly_dta_vartype = "汉钢成本周报-炼钢";
		$dta_1 = '本月累计产量';
		$weekly_report = $this -> _dao -> get_weekly_report( $time_array['weekday'], date("Y-m-d"), $weekly_dta_type, $weekly_dta_vartype, $dta_1 );
		$weeklydata[2][6]['value'] = round( $weekly_report['dta_2'], 2 );

		$weekly_lg_lg_dta_vartype = "龙钢成本周报-轧钢";
		$dta_1 = '本周产量';
		$weekly_lg_zg_bzcl_report = $this -> _dao -> get_weekly_report( $time_array['weekday'], date("Y-m-d"), $weekly_lg_dta_type, $weekly_lg_lg_dta_vartype, $dta_1 );
		$weeklydata[3][4]['value'] = round( $weekly_lg_zg_bzcl_report['dta_2'], 2 );
		$last_weekly_lg_zg_bzcl_report = $this -> _dao -> get_weekly_report( $time_array['lastweekday'], $time_array['lastweekendday'], $weekly_lg_dta_type, $weekly_lg_lg_dta_vartype, $dta_1 );
		$weeklydata[3][5]['value'] = round( $last_weekly_lg_zg_bzcl_report['dta_2'], 2 );

		$dta_1 = '钢材汇总成本（含同兴）';
		$weekly_lg_zg_bzcl_report = $this -> _dao -> get_weekly_report( $time_array['weekday'], date("Y-m-d"), $weekly_lg_dta_type, $weekly_lg_lg_dta_vartype, $dta_1 );
		$weeklydata[12][5]['value'] = round( $weekly_lg_zg_bzcl_report['dta_4'], 2 );
		$last_weekly_lg_zg_bzcl_report = $this -> _dao -> get_weekly_report( $time_array['lastweekday'], $time_array['lastweekendday'], $weekly_lg_dta_type, $weekly_lg_lg_dta_vartype, $dta_1 );
		$weeklydata[12][6]['value'] = round( $last_weekly_lg_zg_bzcl_report['dta_4'], 2 );

		$dta_1 = '本周产量';
		$weekly_report = $this -> _dao -> get_weekly_report( $time_array['weekday'], date("Y-m-d"), $weekly_dta_type, $weekly_dta_vartype, $dta_1 );
		$weeklydata[2][3]['value'] = round( $weekly_report['dta_2'], 2 );
		$last_weekly_report = $this -> _dao -> get_weekly_report( $time_array['lastweekday'], $time_array['lastweekendday'], $weekly_dta_type, $weekly_dta_vartype, $dta_1 );
		$weeklydata[2][4]['value'] = round( $last_weekly_report['dta_2'], 2 );

		$dayly_dta_type = "SGcbzbhg";
		$dayly_dta_vartype = "汉钢成本周报-轧钢";
		$dta_1 = '本月累计产量';
		$weekly_report = $this -> _dao -> get_weekly_report( $time_array['weekday'], date("Y-m-d"), $dayly_dta_type, $dayly_dta_vartype, $dta_1 );
		$weeklydata[4][6]['value'] = round( $weekly_report['dta_2'], 2 );
		$dta_1 = '本周产量';
		$weekly_report = $this -> _dao -> get_weekly_report( $time_array['weekday'], date("Y-m-d"), $dayly_dta_type, $dayly_dta_vartype, $dta_1 );
		$weeklydata[4][3]['value'] = round( $weekly_report['dta_2'], 2 );
		$last_weekly_report = $this -> _dao -> get_weekly_report( $time_array['lastweekday'], $time_array['lastweekendday'], $dayly_dta_type, $dayly_dta_vartype, $dta_1 );
		$weeklydata[4][4]['value'] = round( $last_weekly_report['dta_2'], 2 );

		$dayly_dta_vartype = "汉钢成本周报-轧钢";
		$dta_1 = '六、成本费用';
		$weekly_report = $this -> _dao -> get_weekly_report( $time_array['weekday'], date("Y-m-d"), $dayly_dta_type, $dayly_dta_vartype, $dta_1 );
		$weeklydata[13][6]['value'] = round( $weekly_report['dta_10'], 2 );
		$weeklydata[13][3]['value'] = round( $weekly_report['dta_5'], 2 );
		$last_weekly_report = $this -> _dao -> get_weekly_report( $time_array['lastweekday'], $time_array['lastweekendday'], $dayly_dta_type, $dayly_dta_vartype, $dta_1 );
		$weeklydata[13][4]['value'] = round( $last_weekly_report['dta_5'], 2 );

		$weeklydata[14][3]['value'] = round( ($weeklydata[3][4]['value'] * $weeklydata[12][5]['value'] + $weeklydata[4][3]['value'] * $weeklydata[13][3]['value']) /( $weeklydata[3][4]['value'] + $weeklydata[4][3]['value'] ) , 2 );
		$weeklydata[14][4]['value'] = round( ($weeklydata[3][5]['value'] * $weeklydata[12][6]['value'] + $weeklydata[4][4]['value'] * $weeklydata[13][4]['value']) /( $weeklydata[3][5]['value'] + $weeklydata[4][4]['value'] ) , 2 );
		$weeklydata[14][6]['value'] = round( ($weeklydata[3][7]['value'] * $weeklydata[12][8]['value'] + $weeklydata[4][6]['value'] * $weeklydata[13][6]['value']) /( $weeklydata[3][7]['value'] + $weeklydata[4][6]['value'] ) , 2 );

		$date_this_month_daynum = date( "t",strtotime($time_array['this_month_day']) );
		$date_last_month_daynum = date( "t",strtotime($time_array['last_month_day']) );
		$date_last_week_month_daynum = date( "t",strtotime($time_array['lastweekendday']) );
		$this_month_now_day = date( "j",strtotime($time_array['weekendday'])  );
		$weeklydata[27][5]['value'] = $weeklydata[1][5]['value'];
		$weeklydata[27][6]['value'] = $weeklydata[1][6]['value'];
		$weeklydata[28][4]['value'] = $weeklydata[2][3]['value'];
		$weeklydata[28][5]['value'] = $weeklydata[2][4]['value'];
		$weeklydata[29][5]['value'] = round( ($this_sanxiangfeiyong['dta_1'] + $this_sanxiangfeiyong['dta_2'] + $this_sanxiangfeiyong['dta_3'])*7/$date_this_month_daynum , 2 );
		$weeklydata[29][6]['value'] = round( ($lastweek_sanxiangfeiyong['dta_1'] + $lastweek_sanxiangfeiyong['dta_2'] + $lastweek_sanxiangfeiyong['dta_3'])*7/$date_last_week_month_daynum , 2 );
		$weeklydata[30][4]['value'] = round( ($this_sanxiangfeiyong['dta_4'] + $this_sanxiangfeiyong['dta_5'] + $this_sanxiangfeiyong['dta_6'])*7/$date_this_month_daynum , 2 );
		$weeklydata[30][5]['value'] = round( ($lastweek_sanxiangfeiyong['dta_4'] + $lastweek_sanxiangfeiyong['dta_5'] + $lastweek_sanxiangfeiyong['dta_6'])*7/$date_last_week_month_daynum , 2 );
		$weeklydata[29][8]['value'] = round( ($this_sanxiangfeiyong['dta_1'] + $this_sanxiangfeiyong['dta_2'] + $this_sanxiangfeiyong['dta_3'])*$this_month_now_day/$date_this_month_daynum , 2 );
		$weeklydata[30][7]['value'] = round( ($this_sanxiangfeiyong['dta_4'] + $this_sanxiangfeiyong['dta_5'] + $this_sanxiangfeiyong['dta_6'])*$this_month_now_day/$date_this_month_daynum , 2 );

		$weeklydata[15][4]['value'] = round( $weeklydata[29][5]['value'] / $weeklydata[27][5]['value'], 2 );
		$weeklydata[15][5]['value'] = round( $weeklydata[29][6]['value'] / $weeklydata[27][6]['value'], 2 );
		$weeklydata[16][3]['value'] = round( $weeklydata[30][4]['value'] / $weeklydata[28][4]['value'], 2 );
		$weeklydata[16][4]['value'] = round( $weeklydata[30][5]['value'] / $weeklydata[28][5]['value'], 2 );
		$weeklydata[17][4]['value'] = round( $weeklydata[12][5]['value'] + $weeklydata[15][4]['value'], 2 );
		$weeklydata[17][5]['value'] = round( $weeklydata[12][6]['value'] + $weeklydata[15][5]['value'], 2 );
		$weeklydata[18][3]['value'] = round( $weeklydata[13][3]['value'] + $weeklydata[16][3]['value'], 2 );
		$weeklydata[18][4]['value'] = round( $weeklydata[13][4]['value'] + $weeklydata[16][4]['value'], 2 );
		
		//环比
		$weeklydata[1][7]['value'] = round( $weeklydata[1][5]['value'] - $weeklydata[1][6]['value'], 2 );
		$weeklydata[2][5]['value'] = round( $weeklydata[2][3]['value'] - $weeklydata[2][4]['value'], 2 );
		$weeklydata[3][6]['value'] = round( $weeklydata[3][4]['value'] - $weeklydata[3][5]['value'], 2 );
		$weeklydata[4][5]['value'] = round( $weeklydata[4][3]['value'] - $weeklydata[4][4]['value'], 2 );
		$weeklydata[5][7]['value'] = round( $weeklydata[5][5]['value'] - $weeklydata[5][6]['value'], 2 );
		$weeklydata[6][5]['value'] = round( $weeklydata[6][3]['value'] - $weeklydata[6][4]['value'], 2 );
		$weeklydata[7][7]['value'] = round( $weeklydata[7][5]['value'] - $weeklydata[7][6]['value'], 2 );
		$weeklydata[8][5]['value'] = round( $weeklydata[8][3]['value'] - $weeklydata[8][4]['value'], 2 );
		$weeklydata[9][6]['value'] = round( $weeklydata[9][4]['value'] - $weeklydata[9][5]['value'], 2 );
		$weeklydata[10][5]['value'] = round( $weeklydata[10][3]['value'] - $weeklydata[10][4]['value'], 2 );
		$weeklydata[11][5]['value'] = round( $weeklydata[11][3]['value'] - $weeklydata[11][4]['value'], 2 );
		$weeklydata[12][7]['value'] = round( $weeklydata[12][5]['value'] - $weeklydata[12][6]['value'], 2 );
		$weeklydata[13][5]['value'] = round( $weeklydata[13][3]['value'] - $weeklydata[13][4]['value'], 2 );
		$weeklydata[14][5]['value'] = round( $weeklydata[14][3]['value'] - $weeklydata[14][4]['value'], 2 );
		$weeklydata[15][6]['value'] = round( $weeklydata[15][4]['value'] - $weeklydata[15][5]['value'], 2 );
		$weeklydata[16][5]['value'] = round( $weeklydata[16][3]['value'] - $weeklydata[16][4]['value'], 2 );
		$weeklydata[17][6]['value'] = round( $weeklydata[17][4]['value'] - $weeklydata[17][5]['value'], 2 );
		$weeklydata[18][5]['value'] = round( $weeklydata[18][3]['value'] - $weeklydata[18][4]['value'], 2 );
		$weeklydata[18][5]['value'] = round( $weeklydata[18][3]['value'] - $weeklydata[18][4]['value'], 2 );
		$weeklydata[23][7]['value'] = round( $weeklydata[23][5]['value'] - $weeklydata[23][6]['value'], 2 );
		$weeklydata[24][5]['value'] = round( $weeklydata[24][3]['value'] - $weeklydata[24][4]['value'], 2 );
		$weeklydata[25][6]['value'] = round( $weeklydata[25][4]['value'] - $weeklydata[25][5]['value'], 2 );
		$weeklydata[26][5]['value'] = round( $weeklydata[26][3]['value'] - $weeklydata[26][4]['value'], 2 );
		$weeklydata[27][7]['value'] = round( $weeklydata[27][5]['value'] - $weeklydata[27][6]['value'], 2 );
		$weeklydata[28][6]['value'] = round( $weeklydata[28][4]['value'] - $weeklydata[28][5]['value'], 2 );
		$weeklydata[29][7]['value'] = round( $weeklydata[29][5]['value'] - $weeklydata[29][6]['value'], 2 );
		$weeklydata[30][6]['value'] = round( $weeklydata[30][4]['value'] - $weeklydata[30][5]['value'], 2 );

		//环比影响
		$weeklydata[5][9]['value'] = round( $weeklydata[5][7]['value'] * $weeklydata[23][5]['value'], 2 );
		$weeklydata[6][7]['value'] = round( $weeklydata[6][5]['value'] * $weeklydata[24][3]['value'], 2 );
		$weeklydata[5][10]['value'] = round( $weeklydata[5][9]['value'] + $weeklydata[6][7]['value'], 2 );
		$weeklydata[9][8]['value'] = round( $weeklydata[9][6]['value'] * $weeklydata[5][6]['value'], 2 );
		$weeklydata[10][7]['value'] = round( $weeklydata[10][5]['value'] * $weeklydata[6][4]['value'], 2 );
		$weeklydata[9][9]['value'] = round( $weeklydata[9][8]['value'] + $weeklydata[10][7]['value'], 2 );
		$weeklydata[12][9]['value'] = round( $weeklydata[12][7]['value'] * $weeklydata[5][6]['value'], 2 );
		$weeklydata[13][7]['value'] = round( $weeklydata[13][5]['value'] * $weeklydata[6][4]['value'], 2 );
		$weeklydata[12][10]['value'] = round( $weeklydata[12][9]['value'] + $weeklydata[13][7]['value'], 2 );
		$weeklydata[15][8]['value'] = round( $weeklydata[15][6]['value'] * $weeklydata[5][6]['value'], 2 );
		$weeklydata[16][7]['value'] = round( $weeklydata[16][5]['value'] * $weeklydata[6][4]['value'], 2 );
		$weeklydata[15][9]['value'] = round( $weeklydata[15][8]['value'] + $weeklydata[16][7]['value'], 2 );
		$weeklydata[23][9]['value'] = round( $weeklydata[23][5]['value'] - $weeklydata[23][6]['value'], 2 );
		$weeklydata[24][7]['value'] = round( $weeklydata[24][3]['value'] - $weeklydata[24][4]['value'], 2 );
		$weeklydata[25][8]['value'] = round( $weeklydata[25][4]['value'] - $weeklydata[25][5]['value'], 2 );
		$weeklydata[26][7]['value'] = round( $weeklydata[26][3]['value'] - $weeklydata[26][4]['value'], 2 );

		//处理erp数据
		$erp_dta_type = "TEST_ERP_XSSJ";
		$dayly_dta_vartype = "汉钢成本周报-轧钢";
		$dta_1 = '钢材月销售累计';
		$dta_7 = '龙钢';
		$erp_last_month_lg_report = $this -> _dao -> get_erp_month_report( date("Y-m",strtotime($time_array['last_month_day']) ), $erp_dta_type, $dta_1, $dta_7 );
		$dta_7 = '汉钢';
		$erp_last_month_hg_report = $this -> _dao -> get_erp_month_report( date("Y-m",strtotime($time_array['last_month_day']) ), $erp_dta_type, $dta_1, $dta_7 );
		$weeklydata[5][4]['value'] = round( $erp_last_month_lg_report['dta_5'], 2 );
		$weeklydata[6][2]['value'] = round( $erp_last_month_hg_report['dta_5'], 2 );
		$weeklydata[7][4]['value'] = round( $erp_last_month_lg_report['dta_6']/$erp_last_month_lg_report['dta_5'], 2 );
		$weeklydata[8][2]['value'] = round( $erp_last_month_hg_report['dta_6']/$erp_last_month_hg_report['dta_5'], 2 );

		$dta_7 = '龙钢';
		$erp_this_week_lg_report = $this -> _dao -> get_erp_day_report( $time_array['weekday'], $time_array['weekendday'], $erp_dta_type, $dta_1, $dta_7 );
		$erp_last_week_lg_report = $this -> _dao -> get_erp_day_report( $time_array['lastweekday'], $time_array['lastweekendday'], $erp_dta_type, $dta_1, $dta_7 );
		$dta_7 = '汉钢';
		$erp_this_week_hg_report = $this -> _dao -> get_erp_day_report( $time_array['weekday'], $time_array['weekendday'], $erp_dta_type, $dta_1, $dta_7 );
		$erp_last_week_hg_report = $this -> _dao -> get_erp_day_report( $time_array['lastweekday'], $time_array['lastweekendday'], $erp_dta_type, $dta_1, $dta_7 );

		$weeklydata[5][5]['value'] = round( $erp_this_week_lg_report['dta_5'], 2 );
		$weeklydata[6][3]['value'] = round( $erp_this_week_hg_report['dta_5'], 2 );
		$weeklydata[7][5]['value'] = round( $erp_this_week_lg_report['dta_6']/$erp_this_week_lg_report['dta_5'], 2 );
		$weeklydata[8][3]['value'] = round( $erp_this_week_hg_report['dta_6']/$erp_this_week_hg_report['dta_5'], 2 );

		$weeklydata[5][6]['value'] = round( $erp_last_week_lg_report['dta_5'], 2 );
		$weeklydata[6][4]['value'] = round( $erp_last_week_hg_report['dta_5'], 2 );
		$weeklydata[7][6]['value'] = round( $erp_last_week_lg_report['dta_6']/$erp_last_week_lg_report['dta_5'], 2 );
		$weeklydata[8][4]['value'] = round( $erp_last_week_hg_report['dta_6']/$erp_last_week_hg_report['dta_5'], 2 );

		$weeklydata[5][7]['value'] = round( $weeklydata[5][5]['value'] - $weeklydata[5][6]['value'], 2 );
		$weeklydata[6][5]['value'] = round( $weeklydata[6][3]['value'] - $weeklydata[6][4]['value'], 2 );
		$weeklydata[7][7]['value'] = round( $weeklydata[7][5]['value'] - $weeklydata[7][6]['value'], 2 );
		$weeklydata[8][5]['value'] = round( $weeklydata[8][3]['value'] - $weeklydata[8][4]['value'], 2 );

		$dta_7 = '龙钢';
		$erp_this_month_lg_report = $this -> _dao -> get_erp_day_report( $time_array['this_month_day'], $time_array['weekendday'], $erp_dta_type, $dta_1, $dta_7 );
		$dta_7 = '汉钢';
		$erp_this_month_hg_report = $this -> _dao -> get_erp_day_report( $time_array['this_month_day'], $time_array['weekendday'], $erp_dta_type, $dta_1, $dta_7 );
		$weeklydata[5][8]['value'] = round( $erp_this_month_lg_report['dta_5'], 2 );
		$weeklydata[6][6]['value'] = round( $erp_this_month_hg_report['dta_5'], 2 );
		$weeklydata[7][8]['value'] = round( $erp_this_month_lg_report['dta_6']/$erp_this_month_lg_report['dta_5'], 2 );
		$weeklydata[8][6]['value'] = round( $erp_this_month_hg_report['dta_6']/$erp_this_month_hg_report['dta_5'], 2 );

		//处理运费数据
		$yunfei_dta_vartype = "TEST_ERP_ZHYF";
		$dta_1 = '龙钢';
		$last_month_yunfei_lg = $this -> _dao -> get_zhyf_by_time( $time_array['last_month_day'], date("Y-m-d",strtotime($time_array['this_month_day']."-1 days") ), $yunfei_dta_vartype, $dta_1  );
		$weeklydata[9][3]['value'] = round( $last_month_yunfei_lg['dta_2'], 2 );
		$this_week_yunfei_lg = $this -> _dao -> get_zhyf_by_time( $time_array['weekday'], $time_array['weekendday'], $yunfei_dta_vartype, $dta_1  );
		$weeklydata[9][4]['value'] = round( $this_week_yunfei_lg['dta_2'], 2 );
		$last_week_yunfei_lg = $this -> _dao -> get_zhyf_by_time( $time_array['lastweekday'], $time_array['lastweekendday'], $yunfei_dta_vartype, $dta_1  );
		$weeklydata[9][5]['value'] = round( $last_week_yunfei_lg['dta_2'], 2 );
		$weeklydata[9][6]['value'] = round( $weeklydata[9][4]['value'] - $weeklydata[9][5]['value'], 2 );
		$this_month_yunfei_lg = $this -> _dao -> get_zhyf_by_time( $time_array['this_month_day'], $time_array['weekendday'], $yunfei_dta_vartype, $dta_1  );
		$weeklydata[9][7]['value'] = round( $this_month_yunfei_lg['dta_2'], 2 );

		$dta_1 = '汉钢';
		$last_month_yunfei_hg = $this -> _dao -> get_zhyf_by_time( $time_array['last_month_day'], date("Y-m-d",strtotime($time_array['this_month_day']."-1 days") ), $yunfei_dta_vartype, $dta_1  );
		$weeklydata[10][2]['value'] = round( $last_month_yunfei_hg['dta_2'], 2 );
		$this_week_yunfei_hg = $this -> _dao -> get_zhyf_by_time( $time_array['weekday'], $time_array['weekendday'], $yunfei_dta_vartype, $dta_1  );
		$weeklydata[10][3]['value'] = round( $this_week_yunfei_hg['dta_2'], 2 );
		$last_week_yunfei_hg = $this -> _dao -> get_zhyf_by_time( $time_array['lastweekday'], date("Y-m-d",strtotime($time_array['lastweekendday']."-1 days") ), $yunfei_dta_vartype, $dta_1  );
		$weeklydata[10][4]['value'] = round( $last_week_yunfei_hg['dta_2'], 2 );
		$weeklydata[10][5]['value'] = round( $weeklydata[10][3]['value'] - $weeklydata[10][4]['value'], 2 );
		$this_month_yunfei_hg = $this -> _dao -> get_zhyf_by_time( $time_array['this_month_day'], $time_array['weekendday'], $yunfei_dta_vartype, $dta_1  );
		$weeklydata[10][6]['value'] = round( $this_month_yunfei_hg['dta_2'], 2 );

		$weeklydata[11][2]['value'] = round( ($weeklydata[5][4]['value'] * $weeklydata[9][3]['value'] + $weeklydata[6][2]['value'] * $weeklydata[10][2]['value']) / ( $weeklydata[5][4]['value'] + $weeklydata[6][2]['value'] ), 2 );
		$weeklydata[11][3]['value'] = round( ($weeklydata[5][5]['value'] * $weeklydata[9][4]['value'] + $weeklydata[6][3]['value'] * $weeklydata[10][3]['value']) / ( $weeklydata[5][5]['value'] + $weeklydata[6][3]['value'] ), 2 );
		$weeklydata[11][4]['value'] = round( ($weeklydata[5][6]['value'] * $weeklydata[9][5]['value'] + $weeklydata[6][4]['value'] * $weeklydata[10][4]['value']) / ( $weeklydata[5][6]['value'] + $weeklydata[6][4]['value'] ), 2 );
		$weeklydata[11][5]['value'] = round( ($weeklydata[11][3]['value'] - $weeklydata[11][4]['value'] ), 2 );
		$weeklydata[11][6]['value'] = round( ($weeklydata[5][8]['value'] * $weeklydata[9][7]['value'] + $weeklydata[6][6]['value'] * $weeklydata[10][6]['value']) / ( $weeklydata[5][8]['value'] + $weeklydata[6][6]['value'] ), 2 );

		$weeklydata[23][4]['value'] = round( $weeklydata[9][3]['value'] - $weeklydata[17][3]['value'] - $weeklydata[19][4]['value'] - $weeklydata[21][3]['value'] - 26, 2 );
		$weeklydata[23][5]['value'] = round( $weeklydata[9][4]['value'] - $weeklydata[17][4]['value'] - $weeklydata[19][5]['value'] - $weeklydata[21][4]['value'] - 26, 2 );
		$weeklydata[23][6]['value'] = round( $weeklydata[9][5]['value'] - $weeklydata[17][5]['value'] - $weeklydata[19][6]['value'] - $weeklydata[21][5]['value'] - 26, 2 );
		$weeklydata[23][7]['value'] = round( $weeklydata[23][5]['value'] - $weeklydata[23][6]['value'], 2 );
		$weeklydata[23][8]['value'] = round( $weeklydata[9][7]['value'] - $weeklydata[17][7]['value'] - $weeklydata[19][8]['value'] - $weeklydata[21][7]['value'] - 26, 2 );

		$weeklydata[24][2]['value'] = round( $weeklydata[10][2]['value'] - $weeklydata[18][2]['value'] - $weeklydata[20][2]['value'] - $weeklydata[22][2]['value'] - 26, 2 );
		$weeklydata[24][3]['value'] = round( $weeklydata[10][3]['value'] - $weeklydata[18][3]['value'] - $weeklydata[20][3]['value'] - $weeklydata[22][3]['value'] - 26, 2 );
		$weeklydata[24][4]['value'] = round( $weeklydata[10][4]['value'] - $weeklydata[18][4]['value'] - $weeklydata[20][4]['value'] - $weeklydata[21][4]['value'] - 26, 2 );
		$weeklydata[24][5]['value'] = round( $weeklydata[24][3]['value'] - $weeklydata[24][4]['value'], 2 );
		$weeklydata[24][6]['value'] = round( $weeklydata[10][6]['value'] - $weeklydata[18][6]['value'] - $weeklydata[20][6]['value'] - $weeklydata[21][6]['value'] - 26, 2 );

		$weeklydata[25][3]['value'] = round( $weeklydata[5][4]['value'] * $weeklydata[23][4]['value'], 2 );
		$weeklydata[25][4]['value'] = round( $weeklydata[5][5]['value'] * $weeklydata[23][5]['value'], 2 );
		$weeklydata[25][5]['value'] = round( $weeklydata[5][6]['value'] * $weeklydata[23][6]['value'], 2 );
		$weeklydata[25][6]['value'] = round( $weeklydata[25][4]['value'] - $weeklydata[25][5]['value'], 2 );
		$weeklydata[25][7]['value'] = round( $weeklydata[5][8]['value'] * $weeklydata[23][8]['value'], 2 );

		$weeklydata[26][2]['value'] = round( $weeklydata[6][2]['value'] * $weeklydata[24][2]['value'], 2 );
		$weeklydata[26][3]['value'] = round( $weeklydata[6][3]['value'] * $weeklydata[24][3]['value'], 2 );
		$weeklydata[26][4]['value'] = round( $weeklydata[6][4]['value'] * $weeklydata[24][4]['value'], 2 );
		$weeklydata[26][5]['value'] = round( $weeklydata[26][3]['value'] - $weeklydata[26][4]['value'], 2 );
		$weeklydata[26][6]['value'] = round( $weeklydata[6][6]['value'] * $weeklydata[24][6]['value'], 2 );

		$this->assign('weeklydata', $weeklydata);

		$this->assign('mode', $params['mode']?$params['mode']:1);
		$style_url= dirname(DC_URL.$_SERVER['PHP_SELF']);
		$this->assign( "style_url", $style_url );
		$this->assign('params', $params);
	}
}


?>