<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );

class memberlogController extends AbstractController{
  
    public function __construct(){
        parent::__construct();
        $this->_action->setDao( new memberlogDao("DRCW") );  //drc
        $this->_action->t1Dao=new memberlogDao("MAIN");  //
        $this->_action->homeDao=new memberlogDao("91R");
    }

    public function v_index(){
        $this->_action->index($this->_request);
    }

    public function v_list(){
        $this->_action->loglist($this->_request);
    }

    public function v_detail(){
        $this->_action->detail($this->_request);
    }

}
?>