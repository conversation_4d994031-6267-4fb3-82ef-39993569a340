﻿<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>钢之家会议数据大屏</title>
<link rel="stylesheet" href="css/meeting/index.css">
<link rel="stylesheet" href="css/meeting/public.css">
<script src="js/meeting/layui/layui.all.js"></script>
<link rel="stylesheet" href="js/meeting/layui/css/layui.css">
<style>
/* 
.allnav div{
    width: 100%;
    height: 500px;
} */
.nav li{width: 50%;}
</style>
</head>
<body>

<!-- <div class="zuo" id="zuodian"></div>
<div class="you" id="youdian"></div> -->

<header>
<div class="header">
	<h1><span class="font1" id="maintitle"><img src="images/gzj_logo.png">2024年钢铁产业链发展形势会议</span><span class="font2" id="sjdate">2023.10.27-2023.10.29</span></h1>
</div>
</header>

<section>

       
    <div class="center" id="mokuai1" style="display:none">
		
        <div class="center-left " style="width: 86%;height: 76%; margin: 0px auto;margin-top: 3%;">

            <div class="left-top rightTop" style="height: 100%;">
                <div class="title" id="chart5title" style="font-size: 18px;margin-bottom: 20px;">会议报到情况（<{$numData.1.value}>人）</div>
                 <div class="bottom-b">
                    <div  id="chart5" class="allnav"></div>
                    <div  id="chart5Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                    <div class="panel-footer"></div>
                </div>                    
            </div>
           

        </div>
        
    </div>
    <div class="center" id="mokuai2">
		
            <div class="center-left fl">

                <div class="left-top rightTop">
                    <div class="title" id="chart1title">职务分类</div>
					 <div class="bottom-b">
                        <div  id="chart1" class="allnav"></div>
						<div  id="chart1Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                        <div class="panel-footer"></div>
                    </div>                    
                </div>
                <div class="left-cen rightTop">
                    <div class="title" id="chart2title">性别</div>
                     <div class="bottom-b">
                        <div  id="chart2" class="allnav"></div>
						<div  id="chart2Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                        <div class="panel-footer"></div>
                    </div>
                </div>

            </div>
			 <div class="center-cen fl" >
                <div class="no">
                    <div class="no-hd">
                      <ul>
                        <li><{$numData.0.value}>人</li>
                        <li><{$numData.1.value}>人</li>
                      </ul>
                    </div>
                    <div class="no-bd">
                      <ul>
                        <li>报名总人数(共<{$provinceData}>省)</li>
                        <li>报道人数</li>
                      </ul>
                    </div>
                  </div>
                  
              
			  <iframe id="mapFrame" name="mapFrame" src="" style="width:100%;height:100%;border:0"></iframe>
			
             </div>
			<div class="center-right fr">

					<div class="right-top rightTop">
						<div class="title" id="chart3title">企业类型</div>
						 <div class="right-top-top">
							<div id="chart3" class="allnav"></div>
							<div  id="chart3Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                            <div class="panel-footer"></div>
                        </div>
					</div>
					<div class="right-cen">
						<div class="title" id="chart4title">参会人数统计</div>
						<div class="right-cen-cent">
							<div id="chart4" class="allnav"></div>
							<div  id="chart4Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                            <div class="panel-footer"></div>
                        </div>
					</div>
			</div>
		
			
			
  </div>


</section>

<nav>

<div class="nav"> 
	<ul style="width: 50%;margin: 0px auto;"> 
		<li>会议数据统计</li> 
        <li>会议报到情况</li> 
    
	</ul> 
</div>

</nav>
<script>
    var istry='<{$istry}>';
    var mtid='<{$mtid}>';
</script>
<script src="js/meeting/jquery.min.js"></script>
<script src="js/meeting/axios.min.js"></script>
<script src="js/meeting/echarts.min.js"></script>
    
<script src="js/meeting/fontscroll.js"></script>
<script src="js/meeting/echarts.js?v=2023"></script>
<script src="js/meeting/util.js"></script>
    
 <script>
        $(function() {
            $('.myscroll').myScroll({
                speed: 60, //数值越大，速度越慢
                rowHeight: 38 //li的高度
            });
        });

        $(document).ready(function() {
            var whei = $(window).width()
            $("html").css({
                fontSize: whei / 22
            });
            $(window).resize(function() {
                var whei = $(window).width();
                $("html").css({
                    fontSize: whei / 22
                })
            });
        });
    </script>

    <script>
      
	$(function(){  
		
});

        //顶部时间
        function getTime() {
            var myDate = new Date();
            var myYear = myDate.getFullYear(); //获取完整的年份(4位,1970-????)
            var myMonth = myDate.getMonth() + 1; //获取当前月份(0-11,0代表1月)
            var myToday = myDate.getDate(); //获取当前日(1-31)
            var myDay = myDate.getDay(); //获取当前星期X(0-6,0代表星期天)
            var myHour = myDate.getHours(); //获取当前小时数(0-23)
            var myMinute = myDate.getMinutes(); //获取当前分钟数(0-59)
            var mySecond = myDate.getSeconds(); //获取当前秒数(0-59)
            var week = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
            var nowTime;

            nowTime = myYear + '-' + fillZero(myMonth) + '-' + fillZero(myToday) + '&nbsp;&nbsp;' + fillZero(myHour) + ':' + fillZero(myMinute) + ':' + fillZero(mySecond) + '&nbsp;&nbsp;' + week[myDay] + '&nbsp;&nbsp;';
            //console.log(nowTime);
            $('#time').html(nowTime+'企业管理处');
			//$('#lrcsdate').html(myYear+"年"+myMonth+"月"+myToday+"日");
        };

        function fillZero(str) {
            var realNum;
            if (str < 10) {
                realNum = '0' + str;
            } else {
                realNum = str;
            }
            return realNum;
        }
        setInterval(getTime, 1000);
    </script>
</body>
</html>