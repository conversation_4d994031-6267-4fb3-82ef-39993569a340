<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );

class sgxymxController extends AbstractController{
  
	public function __construct(){
		parent::__construct();
		$this->_action->setDao( new sgxymxDao("DRCW") );  //drc
		$this->_action->t1Dao=new sgxymxDao("MAIN");  //
		$this->_action->homeDao=new sgxymxDao("91R");
		$this->_action->gcDao=new sgxymxDao("GC");
	}

	public function _dopre()
	{
		$this->_action->checkSession();
	}
	function v_index()
	{
		$this->_action->index( $this->_request );
	}
	function v_xspq()
	{
		$this->_action->xspq( $this->_request );
	}
	function v_gdbj()
	{
		$this->_action->gdbj( $this->_request );
	}


	function do_uploadFile()
	{
		$this->_action->uploadFile( $this->_request );
	}


	

}
?>