<?php
include_once(FRAME_LIB_DIR . "/action/AbstractAction.inc.php");
class  xg_price_ycInfoAction extends AbstractAction
{
    public function __construct()
    {
        parent::__construct();
    }

    public function uploadzgx($params){

        $mc_type = $params['mc_type'];
        //echo "aaa";
        //print_R($user);
        $sdate = date("Y-01");
        $edate = date("Y-m");

        $this->assign("sdate", $sdate);
        $this->assign("edate", $edate);
        $this->assign("mc_type", $mc_type);
        $this->assign("params", $params);

    }

    public function importexcelzgx($params){
        $moban = $params['moban'];
        if ($_FILES['file']["error"] == "0") {
            //include_once("/usr/local/www/libs/PHPExcel/PHPExcel.php");

            $file = $_FILES['file']['tmp_name'];
            /*$type = pathinfo($_FILES['file']['name']);
            $type = strtolower($type["extension"]);
            if ($type == 'xlsx') {
                $type = 'Excel2007';
            } elseif ($type == 'xls') {
                $type = 'Excel5';
            }*/

            //$objReader = PHPExcel_IOFactory::createReader($type);//判断使用哪种格式
            //$objReader->setReadDataOnly(true); //只读取数据,会智能忽略所有空白行,这点很重要！！！
            //$objPHPExcel = $objReader->load($file); //加载Excel文件

            $fileType=\PhpOffice\PhpSpreadsheet\IOFactory::identify($file);//自动获取文件的类型提供给phpexcel用
            $objReader=\PhpOffice\PhpSpreadsheet\IOFactory::createReader($fileType);//获取文件读取操作对象
            $objReader->setReadDataOnly(true); //只读取数据,会智能忽略所有空白行,这点很重要！！！
            $objPHPExcel=$objReader->load($file);//加载文件

            $sheet =  $objPHPExcel->getActiveSheet();
            //获取当前工作表最大行数
            $rows = $sheet->getHighestRow();
            //获取当前工作表最大列数,返回的是最大的列名
            //$cols = $sheet->getHighestColumn();

            $variety = "";
            $data = array();
            for ($k = 3; $k <= $rows; $k++) {
                $arr = array();
                $A = $sheet->getCell("A" . $k)->getValue();
                if($moban=="1"){
                    if(strpos($A,"公司")==true || strpos($A,"集团")==true || strpos($A,"基地")==true){

                    }else{
                        $variety = $sheet->getCell("A" . $k)->getValue();
                    }
                }else{
                    if(strpos($A,"企业")==true || trim($A)=="" || trim($A)=="企业"){
                        continue;
                    }
                    if(strpos($A,"公司")==true || strpos($A,"集团")==true || strpos($A,"基地")==true || trim($A)=="合计"){

                    }else{
                        $variety = $sheet->getCell("A" . $k)->getValue();
                        continue;
                    }
                }


                $arr['variety'] = $variety;
                if($moban=="1"){
                    $arr['A'] = $sheet->getCell("A" . $k)->getValue();
                }else {
                    if (trim($A) == "合计") {
                        $arr['A'] = $variety;
                    } else {
                        $arr['A'] = $sheet->getCell("A" . $k)->getValue();
                    }
                }
                $arr['B'] = $sheet->getCell("B" . $k)->getValue();
                $arr['C'] = $sheet->getCell("C" . $k)->getValue();
                $arr['D'] = $sheet->getCell("D" . $k)->getValue();
                $arr['E'] = $sheet->getCell("E" . $k)->getValue();
                $arr['F'] = $sheet->getCell("F" . $k)->getValue();
                $arr['G'] = $sheet->getCell("G" . $k)->getValue();
                $data[] = $arr;
            }

            if(empty($data)){
                $res["Code"] = 1;
            }else{

                $GUID = $params['GUID'];
                $user = $this->getUserInfo($GUID);

                $dta_type = "XG_ZGXMONTHDATA";
                $sdate = date("Y-m-01",strtotime($params['date']."-01"));
                $edate = $params['date']."-01";
                if($params['bigtype']=="2"){
                    $dta_type = "XG_ZGXYEARDATA";
                    $sdate = date("Y-m-01",strtotime($params['date']."-01"));
                }
                $this->xg->execute("set names 'utf8';");
                $sql = "update data_table_base set isdel=1 where type=4 and uptype='".$params['bigtype']."' and sdate='".$sdate."' ";
                $this->xg->execute($sql);

                $sql = "INSERT INTO data_table_base SET type=4,uptype='".$params['bigtype']."',dta_type='".$dta_type."' ";
                $sql .= ",sdate='".$sdate."',edate='".$edate."' ";
                $sql .= ",createtime=now(),createuser='".$user['Uid']."',createusername='".$user['TrueName']."' ";
                $this->xg->execute($sql);
                $baseid = $this->xg->insert_id();

                foreach($data as $key=>$tmp){
                    $sql = "INSERT INTO data_table SET baseid='".$baseid."',dta_type='".$dta_type."' ";
                    $sql .= ",dta_ym='".$sdate."',createtime=now(),createuser='".$user['Uid']."' ";
                    $sql .= ",dta1='".$tmp['variety']."',dta2='".$tmp['A']."',dta3='".$tmp['B']."',dta4='".$tmp['C']."' ";
                    $sql .= ",dta5='".$tmp['D']."',dta6='".$tmp['E']."',dta7='".$tmp['F']."',dta8='".$tmp['G']."' ";
                    $this->xg->execute($sql);
                }

                $res["Code"] = 0;
            }
        }
        else
        {
            $res["Code"] = 1;
        }
        echo json_encode($res);
    }

    public function getzgxlist($params){
        $sdate = $params['sdate']."-01";
        $edate = $params['edate']."-01";
        $bigtype = $params['bigtype'];

        $page = $params['page'];
        $limit = $params['limit'];

        if ($page < 1) {
            $page = 1;
        }
        $start = ($page - 1) * $limit;
        $where = "  and uptype in ('1','2')";

        if($sdate){
            $where .= " and sdate>='".$sdate."' ";
        }

        if($bigtype){
            $where .= " and uptype='".$bigtype."' ";
        }

        if($edate){
            $where .= " and sdate<='".$edate."' ";
        }

        $total = $this->xg->getZgxListTotal($where);
        $dataInfo = $this->xg->getZgxListInfo($where, $start, $limit);

        foreach ($dataInfo as &$articleInfo) {
            if($articleInfo['uptype']=="1"){
                $articleInfo['uptype'] = date("n",strtotime($articleInfo['sdate']))."月数据明细";
            }else{
                $articleInfo['uptype'] = date("Y年n月",strtotime($articleInfo['sdate']))."累计明细";
            }

        }

        $code = 0;
        if ($dataInfo) {
            $code = 0;
        }
        $return_array = array(
            "code" => $code,
            "data" => $dataInfo,
            "count" => $total
        );
        echo json_encode($return_array);
    }

    public function deletezgx($params){
        $id = $params['id'];
        $return_array = array(
            "code" => 1,
            "msg" => "操作成功！",
        );
        if ($id) {

            //删除
            $sql = "update data_table_base set ";
            $sql .= "isdel=1 where id='" . $id . "' ";
            $this->xg->execute($sql);

        } else {
            $return_array = array(
                "code" => 0,
                "msg" => "操作失败！",
            );
        }
        echo json_encode($return_array);
    }

    public function zgxdetail($params){
        $id = $params['id'];
        $data = $this->xg->getZgxDetail($id);
        //print_r($data);
        $this->assign("data", $data);
    }

    public function huizong_duibiao($params){

        $params['date'] = $params['date']=="" ? date("Y-m",strtotime("-1 month")) : $params['date'];

        $params['mode'] = $params['mode']=="" ? 1 : $params['mode'];
        $params['bigtype'] = $params['bigtype']=="" ? 1 : $params['bigtype'];
        $bigtype = $params['bigtype'];

        if($bigtype==1){
            $date = date("Y-m-01",strtotime($params['date']."-01"));
            $uptype = 3;//3 月、4 年 手动录入的前一年数据 热轧商品卷、冷轧卷
        }else{
            $date = date("Y-m-01",strtotime($params['date']."-01"));
            $uptype = 4;//3 月、4 年 手动录入的前一年数据 热轧商品卷、冷轧卷
        }

        $date_y = date("Y-01-01",strtotime($params['date']."-01"));
        $date_year = date("Y",strtotime("-1 day".$date_y));

        //手动录入的对标品种数据
        $this->xg->execute("set names 'utf8';");
        $sql = "select id from data_table_base where type=4 and isdel=0 and uptype='".$uptype."' and sdate='".$date."' limit 1 ";
        $baseid = $this->xg->getOne($sql);

        $sql = "select * from data_table where baseid='".$baseid."' ";
        $duibiao = $this->xg->getRow($sql);
        //手动录入的对标绩效指标数据
        $sql = "select id from data_table_base where type=4 and isdel=0 and uptype=5 and sdate='".$date_y."' limit 1 ";
        $baseid = $this->xg->getOne($sql);

        $sql = "select * from data_table where baseid='".$baseid."' ";
        $duibiao2 = $this->xg->getRow($sql);

        $variety_arr = array("特厚板","厚板","中板","中厚宽钢带","热轧薄宽钢带","冷轧薄宽钢带","线材(盘条)","钢筋");
        $com_arr = array("新余钢铁有限责任公司"=>"新钢","湘潭钢铁集团有限公司"=>"湘钢","涟源钢铁集团有限公司"=>"涟钢","湖南华菱涟源钢铁有限公司"=>"涟钢","方大特钢科技股份有限公司"=>"方大");

        $info = array();

        $sql = "select id from data_table_base where type=4 and isdel=0 and uptype='".$bigtype."' and sdate='".$date."' limit 1 ";
        $baseid = $this->xg->getOne($sql);

        $sql = "select dta1,dta2,dta3,dta4,dta5,dta6 from data_table where baseid='".$baseid."' ";
        $list = $this->xg->query($sql);
        //print_r($list);
        foreach ($list as $key=>$tmp){
            $dta1 = trim($tmp['dta1']);
            $dta1 = mb_ereg_replace('^(　| )+','', $dta1);
            if(in_array($dta1,$variety_arr)){

                $dta2 = trim($tmp['dta2']);
                $dta2 = mb_ereg_replace('^(　| )+','', $dta2);
                if(isset($com_arr[$dta2])){
                    $comname = $com_arr[$dta2];
                    $info[$dta1][$comname] = $tmp;
                }
            }
        }
        //echo "<pre>";
        //print_r($info);
        //dta3 销量 、dta4 平均价格
        $data = array();
        $arr = array();
        $arr[0] = "1、中厚板";
        $arr[1] = $info['特厚板']['新钢']['dta3']+$info['厚板']['新钢']['dta3']+$info['中板']['新钢']['dta3'];
        $arr[2] = $arr[1]==0 ? 0 : round(($info['特厚板']['新钢']['dta3']*$info['特厚板']['新钢']['dta4']+$info['厚板']['新钢']['dta3']*$info['厚板']['新钢']['dta4']+$info['中板']['新钢']['dta3']*$info['中板']['新钢']['dta4'])/$arr[1],3);

        $arr[3] = $info['特厚板']['湘钢']['dta3']+$info['厚板']['湘钢']['dta3']+$info['中板']['湘钢']['dta3'];
        $arr[4] = $arr[3]==0 ? 0 : round(($info['特厚板']['湘钢']['dta3']*$info['特厚板']['湘钢']['dta4']+$info['厚板']['湘钢']['dta3']*$info['厚板']['湘钢']['dta4']+$info['中板']['湘钢']['dta3']*$info['中板']['湘钢']['dta4'])/$arr[3],3);

        $arr[5] = $arr[6] = $arr[7] = $arr[8] = "";
        $arr[9] = $arr[2] - $arr[4];
        $arr[10] = "";
        $arr[11] = "湘钢";
        $data[] = $arr;

        $arr = array();
        $arr[0] = "特厚板";
        $arr[1] = $info['特厚板']['新钢']['dta3'];
        $arr[2] = $info['特厚板']['新钢']['dta4'];

        $arr[3] = $info['特厚板']['湘钢']['dta3'];
        $arr[4] = $info['特厚板']['湘钢']['dta4'];
        $arr[5] = $arr[6] = $arr[7] = $arr[8] = $arr[9] = $arr[10] = $arr[11] = "";
        $data[] = $arr;

        $arr = array();
        $arr[0] = "厚板";
        $arr[1] = $info['厚板']['新钢']['dta3'];
        $arr[2] = $info['厚板']['新钢']['dta4'];

        $arr[3] = $info['厚板']['湘钢']['dta3'];
        $arr[4] = $info['厚板']['湘钢']['dta4'];
        $arr[5] = $arr[6] = $arr[7] = $arr[8] = $arr[9] = $arr[10] = $arr[11] = "";
        $data[] = $arr;

        $arr = array();
        $arr[0] = "中板";
        $arr[1] = $info['中板']['新钢']['dta3'];
        $arr[2] = $info['中板']['新钢']['dta4'];

        $arr[3] = $info['中板']['湘钢']['dta3'];
        $arr[4] = $info['中板']['湘钢']['dta4'];
        $arr[5] = $arr[6] = $arr[7] = $arr[8] = $arr[9] = $arr[10] = $arr[11] = "";
        $data[] = $arr;

        $arr = array();
        $arr[0] = "2、热轧商品卷";
        $arr[1] = $info['中厚宽钢带']['新钢']['dta3']+$info['热轧薄宽钢带']['新钢']['dta3'];
        $arr[2] = $arr[1]==0 ? 0 : round(($info['中厚宽钢带']['新钢']['dta3']*$info['中厚宽钢带']['新钢']['dta4']+$info['热轧薄宽钢带']['新钢']['dta3']*$info['热轧薄宽钢带']['新钢']['dta4'])/$arr[1],3);

        $arr[3] = $arr[4] = "";

        $arr[5] = $info['中厚宽钢带']['涟钢']['dta3']+$info['热轧薄宽钢带']['涟钢']['dta3'];
        $arr[6] = $arr[5]==0 ? 0 : round(($info['中厚宽钢带']['涟钢']['dta3']*$info['中厚宽钢带']['涟钢']['dta4']+$info['热轧薄宽钢带']['涟钢']['dta3']*$info['热轧薄宽钢带']['涟钢']['dta4'])/$arr[5],3);

        $arr[7] = $arr[8]= "";
        $arr[9] = $arr[2] - $arr[6];
        $arr[10] = "";
        $arr[11] = "涟钢";
        $data[] = $arr;

        $duibiao['dta3'] = $arr[6];

        $arr = array();
        $arr[0] = "中厚宽钢带";
        $arr[1] = $info['中厚宽钢带']['新钢']['dta3'];
        $arr[2] = $info['中厚宽钢带']['新钢']['dta4'];

        $arr[3] = $arr[4] = "";

        $arr[5] = $info['中厚宽钢带']['涟钢']['dta3'];
        $arr[6] = $info['中厚宽钢带']['涟钢']['dta4'];
        $arr[7] = $arr[8] = $arr[9] = $arr[10] = $arr[11] = "";
        $data[] = $arr;

        $arr = array();
        $arr[0] = "热轧薄宽钢带";
        $arr[1] = $info['热轧薄宽钢带']['新钢']['dta3'];
        $arr[2] = $info['热轧薄宽钢带']['新钢']['dta4'];

        $arr[3] = $arr[4] = "";
        $arr[5] = $info['热轧薄宽钢带']['涟钢']['dta3'];
        $arr[6] = $info['热轧薄宽钢带']['涟钢']['dta4'];
        $arr[7] = $arr[8] = $arr[9] = $arr[10] = $arr[11] = "";
        $data[] = $arr;

        $arr = array();
        $arr[0] = "3、 冷轧卷";
        $arr[1] = $info['冷轧薄宽钢带']['新钢']['dta3'];
        $arr[2] = $info['冷轧薄宽钢带']['新钢']['dta4'];
        $arr[3] = $arr[4] = "";

        $arr[5] = $info['冷轧薄宽钢带']['涟钢']['dta3'];
        $arr[6] = $info['冷轧薄宽钢带']['涟钢']['dta4'];

        $arr[7] = $arr[8] = "";
        $arr[9] = $arr[2] - $arr[6];
        $arr[10] = "";
        $arr[11] = "涟钢";
        $data[] = $arr;

        $duibiao['dta13'] = $arr[6];

        $arr = array();
        $arr[0] = "冷轧薄宽钢带";
        $arr[1] = $info['冷轧薄宽钢带']['新钢']['dta3'];
        $arr[2] = $info['冷轧薄宽钢带']['新钢']['dta4'];
        $arr[3] = $arr[4] = "";

        $arr[5] = $info['冷轧薄宽钢带']['涟钢']['dta3'];
        $arr[6] = $info['冷轧薄宽钢带']['涟钢']['dta4'];
        $arr[7] = $arr[8] = $arr[9] = $arr[10] = $arr[11] = "";
        $data[] = $arr;

        $arr = array();
        $arr[0] = "4、线材";
        $arr[1] = $info['线材(盘条)']['新钢']['dta3'];
        $arr[2] = $info['线材(盘条)']['新钢']['dta4'];
        $arr[3] = $arr[4] = $arr[5] = $arr[6] = "";

        $arr[7] = $info['线材(盘条)']['方大']['dta3'];
        $arr[8] = $info['线材(盘条)']['方大']['dta4'];

        $arr[9] = $arr[2] - $arr[8];
        $arr[10] = "";
        $arr[11] = "方大";
        $data[] = $arr;

        $arr = array();
        $arr[0] = "5、棒材";
        $arr[1] = $info['钢筋']['新钢']['dta3'];
        $arr[2] = $info['钢筋']['新钢']['dta4'];

        $arr[3] = $arr[4] = $arr[5] = $arr[6] = "";

        $arr[7] = $info['钢筋']['方大']['dta3'];
        $arr[8] = $info['钢筋']['方大']['dta4'];

        $arr[9] = $arr[2] - $arr[8];
        $arr[10] = "";
        $arr[11] = "方大";
        $data[] = $arr;

        $arr = array();
        $arr[0] = "6、热轧商品卷<br>（剔除硅优钢）";
        $arr[1] = $duibiao['dta1'];
        $arr[2] = $duibiao['dta2']=="" ? "" : round($duibiao['dta2']);
        $arr[3] = $arr[4] = $arr[5] = $arr[6] = $arr[7] = $arr[8] = "";
        $arr[9] = ($duibiao['dta2']=="" || $duibiao['dta3']=="") ? "" : round($duibiao['dta2']-$duibiao['dta3'],2);
        $arr[10] = "";
        $arr[11] = "涟钢";
        $data[] = $arr;

        $arr = array();
        $arr[0] = "7、冷轧卷<br>（剔除硅优钢）";
        $arr[1] = $duibiao['dta11'];
        $arr[2] = $duibiao['dta12']=="" ? "" : round($duibiao['dta12']);
        $arr[3] = $arr[4] = $arr[5] = $arr[6] = $arr[7] = $arr[8] = "";
        $arr[9] = ($duibiao['dta12']=="" || $duibiao['dta13']=="") ? "" : round($duibiao['dta12']-$duibiao['dta13'],2);
        $arr[10] = "";
        $arr[11] = "涟钢";
        $data[] = $arr;

        $data2 = array();
        $arr_sum = array();
        $key_duibiao = 1;
        foreach ( $data as $key=>$tmp){
            if(strpos($tmp[0],"、")){
                $tmp[0] = str_replace("6、热轧商品卷<br>","热卷",$tmp[0]);
                $tmp[0] = str_replace("7、冷轧卷<br>","冷卷",$tmp[0]);

                $arr = array();
                $arr[0] = $tmp[0];
                $arr[1] = $tmp[11];
                $arr[2] = $duibiao2["dta".$key_duibiao]=="" ? 0 : round($duibiao2["dta".$key_duibiao]);
                $arr[3] = $tmp[1]=="" ? "" : round($tmp[1]/10000,2);
                //$arr[3] = $tmp[1];

                $arr[4] = $tmp[9]=="" ? "" : round($tmp[9]);
                $arr[5] = ($tmp[1]=="" || $tmp[9]=="") ? 0 : round($tmp[9]*$tmp[1]/10000);
                //$arr[5] = $tmp[9]*$tmp[1];

                $arr[6] = $tmp[9]=="" ? "" : round($tmp[9]-$duibiao2["dta".$key_duibiao]);
                $arr[7] = $tmp[9]=="" ? "" : round(($tmp[9]-$duibiao2["dta".$key_duibiao])*$tmp[1]/10000);

                $data2[] = $arr;

                $arr_sum[0] = "小计";
                $arr_sum[1] = "";
                $arr_sum[2] += $tmp[1]=="" ? 0 : $arr[2]*$tmp[1]/10000;
                $arr_sum[3] += $tmp[1]=="" ? 0 : $tmp[1]/10000;
                $arr_sum[4] = 0;
                $arr_sum[5] += $tmp[1]=="" ? 0 : $tmp[9]*$tmp[1]/10000;
                $arr_sum[6] = 0;
                $arr_sum[7] += $tmp[1]=="" ? 0 : ($tmp[9]-$arr[2])*$tmp[1]/10000;

                if(strpos($tmp[0],"棒材")){
                    //echo "111";
                    $arr_sum[2] = $arr_sum[3]==0 ? 0 : round($arr_sum[2]/$arr_sum[3]);
                    $arr_sum[3] = round($arr_sum[3],2);
                    $arr_sum[4] = $arr_sum[3]==0 ? 0 : round($arr_sum[5]/$arr_sum[3]);
                    $arr_sum[5] = round($arr_sum[5]);
                    $arr_sum[6] = $arr_sum[2]==0 ? 0 : round($arr_sum[4]-$arr_sum[2]);
                    $arr_sum[7] = round($arr_sum[7]);

                    $data2[] = $arr_sum;
                }

                $key_duibiao++;
            }


        }
        //print_r($data2);

        $this->assign("data", $data);
        $this->assign("data2", $data2);
        $this->assign("date_year", $date_year);
        $this->assign("params", $params);
    }

    public function insert_duibiao($params){
        //echo "<pre>";
        //print_r($params);
        $GUID = $params['GUID'];
        $params['date'] = $params['date']=="" ? date("Y-m") : $params['date'];

        if($params['bigtype']=="3" || $params['bigtype']=="1"){
            $params['bigtype'] = 3 ;
        }else{
            $params['bigtype'] = 4 ;
        }

        $bigtype = $params['bigtype'];

        if($bigtype==3){
            $date = date("Y-m-01",strtotime($params['date']."-01"));
            $date_month = date("Y年n月当月",strtotime($date));
        }else{
            $date = date("Y-m-01",strtotime($params['date']."-01"));
            $date_month = date("Y年n月累计",strtotime($date));
        }

        $date_y = date("Y-01-01",strtotime($params['date']."-01"));
        $date_year = date("Y",strtotime("-1 day".$date_y));


        $sql = "select id from data_table_base where type=4 and isdel=0 and uptype='".$bigtype."' and sdate='".$date."' limit 1 ";
        $baseid = $this->xg->getOne($sql);

        $sql = "select * from data_table where baseid='".$baseid."' ";
        $info = $this->xg->getRow($sql);

        //上一年售价对标绩效指标
        $sql = "select id from data_table_base where type=4 and isdel=0 and uptype=5 and sdate='".$date_y."' limit 1 ";
        //echo $sql;
        $baseid = $this->xg->getOne($sql);

        $sql = "select * from data_table where baseid='".$baseid."' ";
        $info2 = $this->xg->getRow($sql);

        $this->assign("GUID", $GUID);
        $this->assign("info", $info);
        $this->assign("info2", $info2);
        $this->assign("date_year", $date_year);
        $this->assign("date_month", $date_month);
        $this->assign("params", $params);
    }

    public function save_duibiao($params){

        $dta_ym = $params['dta_ym'];
        $bigtype = $params['bigtype'];

        $GUID = $params['GUID'];
        $user = $this->getUserInfo($GUID);

        if($bigtype==3){
            $date = date("Y-m-01",strtotime($dta_ym."-01"));
        }else{
            $date = date("Y-m-01",strtotime($dta_ym."-01"));
        }
        $date_y = date("Y-01-01",strtotime($dta_ym."-01"));

        //删除
        $sql = "update data_table_base set isdel=1 where type=4 and uptype='".$bigtype."' and sdate='" . $date . "' ";
        $sql2 = "update data_table_base set isdel=1 where type=4 and uptype=5 and sdate='" . $date_y . "' ";//售价对标绩效指标
        $this->xg->execute($sql);
        $this->xg->execute($sql2);

        //插入数据
        $sql = "INSERT INTO data_table_base SET type=4,uptype='".$bigtype."',dta_type='ZGX_DUIBIAO' ";
        $sql .= ",sdate='".$date."',edate='".$date."' ";
        $sql .= ",createtime=now(),createuser='".$user['Uid']."',createusername='".$user['TrueName']."' ";
        $this->xg->execute("set names 'utf8';");
        $this->xg->execute($sql);
        $baseid = $this->xg->insert_id();


        $sql = "INSERT INTO data_table SET baseid='".$baseid."',dta_type='ZGX_DUIBIAO' ";
        $sql .= ",dta_ym='".$date."',createtime=now(),createuser='".$user['Uid']."' ";
        $sql .= ",dta1='".$params['dta1']."',dta2='".$params['dta2']."',dta3='".$params['dta3']."',dta4='".$params['dta4']."',dta5='".$params['dta5']."' ";
        $sql .= ",dta11='".$params['dta11']."',dta12='".$params['dta12']."',dta13='".$params['dta13']."',dta14='".$params['dta14']."',dta15='".$params['dta15']."' ";
        $sql .= ",dta21='".$params['dta21']."',dta22='".$params['dta22']."',dta23='".$params['dta23']."',dta24='".$params['dta24']."',dta25='".$params['dta25']."' ";
        $sql .= ",dta26='".$params['dta26']."',dta27='".$params['dta27']."' ";
        $this->xg->execute($sql);

        //插入售价对标绩效指标数据
        $sql = "INSERT INTO data_table_base SET type=4,uptype=5,dta_type='ZGX_DUIBIAO' ";
        $sql .= ",sdate='".$date_y."',edate='".$date_y."' ";
        $sql .= ",createtime=now(),createuser='".$user['Uid']."',createusername='".$user['TrueName']."' ";
        $this->xg->execute($sql);
        $baseid = $this->xg->insert_id();


        $sql = "INSERT INTO data_table SET baseid='".$baseid."',dta_type='ZGX_DUIBIAO' ";
        $sql .= ",dta_ym='".$date."',createtime=now(),createuser='".$user['Uid']."' ";
        $sql .= ",dta1='".$params['dta21']."',dta2='".$params['dta22']."',dta3='".$params['dta23']."',dta4='".$params['dta24']."',dta5='".$params['dta25']."' ";
        $sql .= ",dta6='".$params['dta26']."',dta7='".$params['dta27']."' ";
        $this->xg->execute($sql);


        $resultArray = array(
            "code" => 1,
            "msg" => "保存成功"
        );

        echo json_encode($resultArray);
        exit;
    }

    public function huizong_gcduibiao($params){
        $params['date'] = $params['date']=="" ? date("Y-m",strtotime("-1 month")) : $params['date'];
        $params['mode'] = $params['mode']=="" ? 1 : $params['mode'];
        $date = date("Y-m-01",strtotime($params['date']."-01"));
        $date_y = date("Y-01-01",strtotime($params['date']."-01"));
        $date_year = date("Y",strtotime("-1 day".$date_y));

        $params['bigtype'] = $params['bigtype']=="" ? 1 : $params['bigtype'];
        $bigtype = $params['bigtype'];
        $datatype = $params['datatype'];

        if($bigtype==1){
            $date_month = date("Y年n月",strtotime($params['date']."-01"));
        }else{
            $date_month = date("Y年1-n月",strtotime($params['date']."-01"));
        }

        //手动录入的对标绩效指标数据
        $sql = "select id from data_table_base where type=4 and isdel=0 and uptype=5 and sdate='".$date_y."' limit 1 ";
        $baseid = $this->xg->getOne($sql);

        $sql = "select * from data_table where baseid='".$baseid."' ";
        $duibiao = $this->xg->getRow($sql);
        //print_r($duibiao);

        $variety_arr = array(
            "按用途分：建筑用特厚板"=>"中厚板",
            "按用途分：建筑用厚板"=>"中厚板",
            "按用途分：建筑用中板"=>"中厚板",
            "其他用途特厚板"=>"中厚板",
            "其他用途厚板"=>"中厚板",
            "其他用途中板"=>"中厚板",

            "工程机械用特厚板"=>"中厚板",
            "工程机械用厚板"=>"中厚板",
            "工程机械用中板"=>"中厚板",

            "造船用特厚板"=>"中厚板",
            "造船用厚板"=>"中厚板",
            "造船用中板"=>"中厚板",

            "锅炉和压力容器用特厚板"=>"中厚板",
            "锅炉和压力容器用厚板"=>"中厚板",
            "锅炉和压力用中板"=>"中厚板",

            "桥梁用特厚板"=>"中厚板",
            "桥梁用厚板"=>"中厚板",
            "桥梁用中板"=>"中厚板",

            "管线用特厚板"=>"中厚板",
            "管线用厚板"=>"中厚板",
            "管线用中板"=>"中厚板",

            "按用途分：建筑用中厚宽钢带"=>"热卷",
            "工程机械用中厚宽钢带"=>"热卷",
            "管线用中厚宽钢带"=>"热卷",
            "汽车用中厚宽钢带"=>"热卷",
            "集装箱用中厚宽钢带"=>"热卷",
            "桥梁用中厚宽钢带"=>"热卷",
            "造船用中厚宽钢带"=>"热卷",
            "其他用途中厚宽钢带"=>"热卷",
            "按用途分：工程机械用热轧薄宽钢带"=>"热卷",
            "建筑用热轧薄宽钢带"=>"热卷",
            "轻工家电用热轧薄宽钢带"=>"热卷",
            "焊管用热轧薄宽钢带"=>"热卷",
            "集装箱用热轧薄宽钢带"=>"热卷",
            "造船用热轧薄宽钢带"=>"热卷",
            "汽车用热轧薄宽钢带"=>"热卷",
            "其他用途热轧薄宽钢带"=>"热卷",

            "按用途分：汽车用冷轧薄宽钢带"=>"冷卷",
            "家电用冷轧薄宽钢带"=>"冷卷",
            "建筑用冷轧薄宽钢带"=>"冷卷",
            "其他用途冷轧薄宽钢带"=>"冷卷",
            "工程机械用冷轧薄宽钢带"=>"冷卷",
            "钢质家居用冷轧薄宽钢带"=>"冷卷",
            "农机用冷轧薄宽钢带"=>"冷卷",
            "不锈冷轧薄宽钢带"=>"冷卷",

            "按用途分：工程及建筑结构用线材(含盘螺)"=>"线材",
            "制品原料用高碳钢线材（硬线）"=>"线材",
            "拉拔用线材（软线）"=>"线材",
            "冷墩铆螺用线材"=>"线材",
            "其他用途用线材"=>"线材",
            "弹簧用线材"=>"线材",
            "电焊条用线材"=>"线材",

            "其中：三级"=>"钢筋",
            "其他"=>"钢筋",
            "四级HRB500,全部规格"=>"钢筋",
            "五级HRB600,全部规格"=>"钢筋"
        );
        $com_arr = array("新余钢铁有限责任公司"=>"新钢","湘潭钢铁集团有限公司"=>"湘钢","涟源钢铁集团有限公司"=>"涟钢","湖南华菱涟源钢铁有限公司"=>"涟钢","方大特钢科技股份有限公司"=>"方大");

        //详细数据
        $this->xg->execute("set names 'utf8';");
        $sql = "select id from data_table_base where type=4 and isdel=0 and uptype='".$bigtype."' and sdate='".$date."' limit 1 ";
        $baseid = $this->xg->getOne($sql);

        $sql = "select dta1,dta2,dta3,dta4,dta5,dta6 from data_table where baseid='".$baseid."' ";
        $list = $this->xg->query($sql);
        //print_r($list);
        $info = array();
        foreach ($list as $key=>$tmp){

            $dta1 = trim($tmp['dta1']);
            $dta1 = mb_ereg_replace('^(　| )+','', $dta1);
            if(isset($variety_arr[$dta1])){

                $dta2 = trim($tmp['dta2']);
                $dta2 = mb_ereg_replace('^(　| )+','', $dta2);
                if(isset($com_arr[$dta2])){

                    $dta2 = $com_arr[$dta2];
                    //echo $dta1."=".$dta2."=".$tmp['dta3']."=".$tmp['dta4']."<br>";
                    $dta1 = $variety_arr[$dta1];

                    if($datatype=="3"){
                        $info[$dta1][$dta2]['dta3'] += $tmp['dta7'];
                        $info[$dta1][$dta2]['dta4'] += $tmp['dta8'] * $tmp['dta7'];
                    }else if($datatype=="2"){
                        $info[$dta1][$dta2]['dta3'] += $tmp['dta5'];
                        $info[$dta1][$dta2]['dta4'] += $tmp['dta6'] * $tmp['dta5'];
                    }else {
                        $info[$dta1][$dta2]['dta3'] += $tmp['dta3'];
                        $info[$dta1][$dta2]['dta4'] += $tmp['dta4'] * $tmp['dta3'];
                    }
                }
            }
        }
        //echo "<pre>";
        //print_r($info);

        //dta3 销量 、dta4 总价格
        $data = array();
        $arr = array();
        $arr[0] = "中厚板";
        $arr[1] = $duibiao['dta1'];
        $arr[2] = $info['中厚板']['湘钢']['dta3']==0 ? 0 : $info['中厚板']['湘钢']['dta4']/$info['中厚板']['湘钢']['dta3'];
        $arr[3] = $arr[4] = $arr[5] = "";
        $arr[6] = $info['中厚板']['新钢']['dta3']==0 ? 0 : $info['中厚板']['新钢']['dta4']/$info['中厚板']['新钢']['dta3'];
        $arr[7] = $arr[6]-$arr[2];
        $arr[8] = round($info['中厚板']['新钢']['dta3']/10000,2);
        $arr[9] = $arr[7]-$arr[1];
        $arr[10] = round($arr[8]*$arr[9]);

        $arr[2] = round($arr[2]);
        $arr[6] = round($arr[6]);
        $arr[7] = round($arr[7]);
        $arr[9] = round($arr[9]);
        $data[] = $arr;

        $arr = array();
        $arr[0] = "热卷";
        $arr[1] = $duibiao['dta2'];
        $arr[2] = "";
        $arr[3] = $info['热卷']['涟钢']['dta3']==0 ? 0 : $info['热卷']['涟钢']['dta4']/$info['热卷']['涟钢']['dta3'];
        $arr[4] = $arr[5] = "";
        $arr[6] = $info['热卷']['新钢']['dta3']==0 ? 0 : $info['热卷']['新钢']['dta4']/$info['热卷']['新钢']['dta3'];
        $arr[7] = $arr[6]-$arr[3];
        $arr[8] = round($info['热卷']['新钢']['dta3']/10000,2);
        $arr[9] = $arr[7]-$arr[1];
        $arr[10] = round($arr[8]*$arr[9]);

        $arr[3] = round($arr[3]);
        $arr[6] = round($arr[6]);
        $arr[7] = round($arr[7]);
        $arr[9] = round($arr[9]);
        $data[] = $arr;

        $arr = array();
        $arr[0] = "冷卷";
        $arr[1] = $duibiao['dta3'];
        $arr[2] = "";
        $arr[3] = $info['冷卷']['涟钢']['dta3']==0 ? 0 : $info['冷卷']['涟钢']['dta4']/$info['冷卷']['涟钢']['dta3'];
        $arr[4] = $arr[5] = "";
        $arr[6] = $info['冷卷']['新钢']['dta3']==0 ? 0 : $info['冷卷']['新钢']['dta4']/$info['冷卷']['新钢']['dta3'];
        $arr[7] = $arr[6]-$arr[3];
        $arr[8] = round($info['冷卷']['新钢']['dta3']/10000,2);
        $arr[9] = $arr[7]-$arr[1];
        $arr[10] = round($arr[8]*$arr[9]);

        $arr[3] = round($arr[3]);
        $arr[6] = round($arr[6]);
        $arr[7] = round($arr[7]);
        $arr[9] = round($arr[9]);
        $data[] = $arr;

        $arr = array();
        $arr[0] = "线材";
        $arr[1] = $duibiao['dta4'];
        $arr[2] = "";
        $arr[3] = "";
        $arr[4] = $info['线材']['方大']['dta3']==0 ? 0 : $info['线材']['方大']['dta4']/$info['线材']['方大']['dta3'];
        $arr[5] = "";
        $arr[6] = $info['线材']['新钢']['dta3']==0 ? 0 : $info['线材']['新钢']['dta4']/$info['线材']['新钢']['dta3'];
        $arr[7] = $arr[6]-$arr[4];
        $arr[8] = round($info['线材']['新钢']['dta3']/10000,2);
        $arr[9] = $arr[7]-$arr[1];
        $arr[10] = round($arr[8]*$arr[9]);

        $arr[4] = round($arr[4]);
        $arr[6] = round($arr[6]);
        $arr[7] = round($arr[7]);
        $arr[9] = round($arr[9]);
        $data[] = $arr;

        $arr = array();
        $arr[0] = "钢筋";
        $arr[1] = $duibiao['dta5'];
        $arr[2] = "";
        $arr[3] = "";
        $arr[4] = $info['钢筋']['方大']['dta3']==0 ? 0 : $info['钢筋']['方大']['dta4']/$info['钢筋']['方大']['dta3'];
        $arr[5] = "";
        $arr[6] = $info['钢筋']['新钢']['dta3']==0 ? 0 : $info['钢筋']['新钢']['dta4']/$info['钢筋']['新钢']['dta3'];
        $arr[7] = $arr[6]-$arr[4];
        $arr[8] = round($info['钢筋']['新钢']['dta3']/10000,2);
        $arr[9] = $arr[7]-$arr[1];
        $arr[10] = round($arr[8]*$arr[9]);

        $arr[4] = round($arr[4]);
        $arr[6] = round($arr[6]);
        $arr[7] = round($arr[7]);
        $arr[9] = round($arr[9]);
        $data[] = $arr;

        $arr_sum = array();
        foreach($data as $key=>$tmp){
            $arr_sum[0] = "主体钢材小计";
            $arr_sum[1] += $tmp[1]*$tmp[8];
            $arr_sum[2] = "";
            $arr_sum[3] = "";
            $arr_sum[4] = "";
            $arr_sum[5] = "";
            $arr_sum[6] = "";
            $arr_sum[7] += $tmp[7]*$tmp[8];
            $arr_sum[8] += $tmp[8];
            $arr_sum[9] = "";
            $arr_sum[10] += $tmp[10];
        }
        $arr_sum[1] = $arr_sum[8]==0 ? 0 : round($arr_sum[1]/$arr_sum[8]);
        $arr_sum[7] = $arr_sum[8]==0 ? 0 : round($arr_sum[7]/$arr_sum[8]);
        $arr_sum[9] = $arr_sum[8]==0 ? 0 : round($arr_sum[10]/$arr_sum[8]);
        $data[] = $arr_sum;

        $arr = array();
        $arr[0] = "无取向硅钢";
        $arr[1] = $arr[2] = $arr[3] = $arr[4] = $arr[5] = $arr[6] = $arr[7] = $arr[8] = $arr[9] = $arr[10] = "";
        $data[] = $arr;

        $arr = array();
        $arr[0] = "优特钢生产卷";
        $arr[1] = $arr[2] = $arr[3] = $arr[4] = $arr[5] = $arr[6] = $arr[7] = $arr[8] = $arr[9] = $arr[10] = "";
        $data[] = $arr;

        $this->assign("data", $data);
        $this->assign("date_year", $date_year);
        $this->assign("date_month", $date_month);
        $this->assign("params", $params);
    }

    public function huizong_gcduibiao_xbc($params){
        $params['date'] = $params['date']=="" ? date("Y-m",strtotime("-1 month")) : $params['date'];
        $params['mode'] = $params['mode']=="" ? 1 : $params['mode'];
        $date = date("Y-m-01",strtotime($params['date']."-01"));
        $params['bigtype'] = $params['bigtype']=="" ? 1 : $params['bigtype'];
        $bigtype = $params['bigtype'];

        if($bigtype==1){
            $date_month = date("Y年n月",strtotime($params['date']."-01"));
        }else{
            $date_month = date("Y年1-n月",strtotime($params['date']."-01"));
        }

        $variety_arr = array(
            "按用途分：工程及建筑结构用线材(含盘螺)"=>"线材",
            "制品原料用高碳钢线材（硬线）"=>"线材",
            "拉拔用线材（软线）"=>"线材",
            "冷墩铆螺用线材"=>"线材",
            "其他用途用线材"=>"线材",
            "弹簧用线材"=>"线材",
            "电焊条用线材"=>"线材",

            "其中：三级"=>"钢筋",
            "其他"=>"钢筋",
            "四级HRB500,全部规格"=>"钢筋",
            "五级HRB600,全部规格"=>"钢筋"
        );

        $variety_arr2 = array(
            "按用途分：工程及建筑结构用线材(含盘螺)"=>"工程",
            "制品原料用高碳钢线材（硬线）"=>"制品",
            "拉拔用线材（软线）"=>"拉拔",
            "冷墩铆螺用线材"=>"冷墩",
            "其他用途用线材"=>"其他线材",
            "弹簧用线材"=>"弹簧",
            "电焊条用线材"=>"电焊条",

            "其中：三级"=>"三级",
            "其他"=>"其他",
            "四级HRB500,全部规格"=>"四级",
            "五级HRB600,全部规格"=>"五级"
        );
        $com_arr = array("新余钢铁有限责任公司"=>"新钢","湘潭钢铁集团有限公司"=>"湘钢","涟源钢铁集团有限公司"=>"涟钢","湖南华菱涟源钢铁有限公司"=>"涟钢","方大特钢科技股份有限公司"=>"方大");

        //详细数据
        $this->xg->execute("set names 'utf8';");
        $sql = "select id from data_table_base where type=4 and isdel=0 and uptype='".$bigtype."' and sdate='".$date."' limit 1 ";
        $baseid = $this->xg->getOne($sql);

        $sql = "select dta1,dta2,dta3,dta4,dta5,dta6 from data_table where baseid='".$baseid."' ";
        $list = $this->xg->query($sql);
        //print_r($list);
        $info = array();
        $qita = 0;
        foreach ($list as $key=>$tmp){

            $dta1 = trim($tmp['dta1']);
            $dta1 = mb_ereg_replace('^(　| )+','', $dta1);
            if($dta1=="线材(盘条)"){
                $qita = 0;
            }
            if(isset($variety_arr[$dta1])){

                $dta2 = trim($tmp['dta2']);
                $dta2 = mb_ereg_replace('^(　| )+','', $dta2);
                if($dta1==$dta2){
                    //行业销售量，行业价格
                    $dta1_ = $variety_arr2[$dta1];
                    if($dta1_=="五级"){
                        $qita = 1;
                    }
                    if($dta1_=="其他" && $qita==0){
                        continue;
                    }
                    $info[$dta1_][$dta1_]['dta3'] += (float)$tmp['dta3'];
                    $info[$dta1_][$dta1_]['dta4'] += (float)$tmp['dta3']*(float)$tmp['dta4'];
                }
                if(isset($com_arr[$dta2])){

                    $dta2 = $com_arr[$dta2];

                    $dta1_ = $variety_arr2[$dta1];
                    $dta1 = $variety_arr[$dta1];

                    //分品种
                    $info[$dta1_][$dta2]['dta3'] += (float)$tmp['dta3'];
                    $info[$dta1_][$dta2]['dta4'] += (float)$tmp['dta4']*(float)$tmp['dta3'];

                    //大品种合计
                    $info[$dta1][$dta2]['dta3'] += (float)$tmp['dta3'];
                    $info[$dta1][$dta2]['dta4'] += (float)$tmp['dta4']*(float)$tmp['dta3'];
                }
            }
        }
        //echo "<pre>";
        //print_r($info);
        $sum8 = 0;
        $sum9 = 0;
        //dta3 销量 、dta4 总价格
        $data = array();
        $arr = array();
        $arr[0] = "三级";
        $arr[1] = $info['三级']['新钢']['dta3'];
        $arr[2] = $info['三级']['新钢']['dta3']==0 ? 0 : $info['三级']['新钢']['dta4']/$info['三级']['新钢']['dta3'];
        $arr[3] = $info['钢筋']['新钢']['dta3']==0 ? 0 : $info['三级']['新钢']['dta3']/$info['钢筋']['新钢']['dta3'];
        $arr[4] = $info['三级']['方大']['dta3'];
        $arr[5] = $info['三级']['方大']['dta3']==0 ? 0 : $info['三级']['方大']['dta4']/$info['三级']['方大']['dta3'];
        $arr[6] = $info['钢筋']['方大']['dta3']==0 ? 0 : $info['三级']['方大']['dta3']/$info['钢筋']['方大']['dta3'];
        $arr[7] = $arr[2]-$arr[5];
        $arr[8] = $info['三级']['三级']['dta3'];
        $arr[9] = $info['三级']['三级']['dta3']==0 ? 0 : $info['三级']['三级']['dta4']/$info['三级']['三级']['dta3'];

        $sum8 += $arr[8];
        $sum9 += $arr[8]*$arr[9];

        $arr[2] = round($arr[2]);
        $arr[3] = round($arr[3]*100,1)."%";
        $arr[5] = round($arr[5]);
        $arr[6] = round($arr[6]*100,1)."%";
        $arr[9] = round($arr[9]);
        $data[] = $arr;

        $arr = array();
        $arr[0] = "四级HRB500,全部规格";
        $arr[1] = $info['四级']['新钢']['dta3'];
        $arr[2] = $info['四级']['新钢']['dta3']==0 ? 0 : $info['四级']['新钢']['dta4']/$info['四级']['新钢']['dta3'];
        $arr[3] = $info['钢筋']['新钢']['dta3']==0 ? 0 : $info['四级']['新钢']['dta3']/$info['钢筋']['新钢']['dta3'];
        $arr[4] = $info['四级']['方大']['dta3'];
        $arr[5] = $info['四级']['方大']['dta3']==0 ? 0 : $info['四级']['方大']['dta4']/$info['四级']['方大']['dta3'];
        $arr[6] = $info['钢筋']['方大']['dta3']==0 ? 0 : $info['四级']['方大']['dta3']/$info['钢筋']['方大']['dta3'];
        $arr[7] = $arr[2]-$arr[5];
        $arr[8] = $info['四级']['四级']['dta3'];
        $arr[9] = $info['四级']['四级']['dta3']==0 ? 0 : $info['四级']['四级']['dta4']/$info['四级']['四级']['dta3'];

        $sum8 += $arr[8];
        $sum9 += $arr[8]*$arr[9];

        $arr[2] = round($arr[2]);
        $arr[3] = round($arr[3]*100,1)."%";
        $arr[5] = round($arr[5]);
        $arr[6] = round($arr[6]*100,1)."%";
        $arr[9] = round($arr[9]);
        $data[] = $arr;

        $arr = array();
        $arr[0] = "五级HRB600,全部规格";
        $arr[1] = $info['五级']['新钢']['dta3'];
        $arr[2] = $info['五级']['新钢']['dta3']==0 ? 0 : $info['五级']['新钢']['dta4']/$info['五级']['新钢']['dta3'];
        $arr[3] = $info['钢筋']['新钢']['dta3']==0 ? 0 : $info['五级']['新钢']['dta3']/$info['钢筋']['新钢']['dta3'];
        $arr[4] = $info['五级']['方大']['dta3'];
        $arr[5] = $info['五级']['方大']['dta3']==0 ? 0 : $info['五级']['方大']['dta4']/$info['五级']['方大']['dta3'];
        $arr[6] = $info['钢筋']['方大']['dta3']==0 ? 0 : $info['五级']['方大']['dta3']/$info['钢筋']['方大']['dta3'];
        $arr[7] = $arr[2]-$arr[5];
        $arr[8] = $info['五级']['五级']['dta3'];
        $arr[9] = $info['五级']['五级']['dta3']==0 ? 0 : $info['五级']['五级']['dta4']/$info['五级']['五级']['dta3'];

        $sum8 += $arr[8];
        $sum9 += $arr[8]*$arr[9];

        $arr[2] = round($arr[2]);
        $arr[3] = round($arr[3]*100,1)."%";
        $arr[5] = round($arr[5]);
        $arr[6] = round($arr[6]*100,1)."%";
        $arr[9] = round($arr[9]);
        $data[] = $arr;

        $arr = array();
        $arr[0] = "其他";
        $arr[1] = $info['其他']['新钢']['dta3'];
        $arr[2] = $info['其他']['新钢']['dta3']==0 ? 0 : $info['其他']['新钢']['dta4']/$info['其他']['新钢']['dta3'];
        $arr[3] = $info['钢筋']['新钢']['dta3']==0 ? 0 : $info['其他']['新钢']['dta3']/$info['钢筋']['新钢']['dta3'];
        $arr[4] = $info['其他']['方大']['dta3'];
        $arr[5] = $info['其他']['方大']['dta3']==0 ? 0 : $info['其他']['方大']['dta4']/$info['其他']['方大']['dta3'];
        $arr[6] = $info['钢筋']['方大']['dta3']==0 ? 0 : $info['其他']['方大']['dta3']/$info['钢筋']['方大']['dta3'];
        $arr[7] = $arr[2]-$arr[5];
        $arr[8] = $info['其他']['其他']['dta3'];
        $arr[9] = $info['其他']['其他']['dta3']==0 ? 0 : $info['其他']['其他']['dta4']/$info['其他']['其他']['dta3'];

        $sum8 += $arr[8];
        $sum9 += $arr[8]*$arr[9];

        $arr[2] = round($arr[2]);
        $arr[3] = round($arr[3]*100,1)."%";
        $arr[5] = round($arr[5]);
        $arr[6] = round($arr[6]*100,1)."%";
        $arr[9] = round($arr[9]);
        $data[] = $arr;

        $arr = array();
        $arr[0] = "1、钢筋小计";
        $arr[1] = $info['钢筋']['新钢']['dta3'];
        $arr[2] = $info['钢筋']['新钢']['dta3']==0 ? 0 : $info['钢筋']['新钢']['dta4']/$info['钢筋']['新钢']['dta3'];
        $arr[3] = "1.00";
        $arr[4] = $info['钢筋']['方大']['dta3'];
        $arr[5] = $info['钢筋']['方大']['dta3']==0 ? 0 : $info['钢筋']['方大']['dta4']/$info['钢筋']['方大']['dta3'];
        $arr[6] = "1.00";
        $arr[7] = $arr[2]-$arr[5];
        $arr[8] = $sum8;
        $arr[9] = $sum8==0 ? 0 : round($sum9/$sum8);

        $arr[2] = round($arr[2]);
        $arr[5] = round($arr[5]);
        $arr[7] = round($arr[7]);
        $data[] = $arr;

        $arr = array();
        $arr[0] = "工程及建筑结构用线材(含盘螺)";
        $arr[1] = $info['工程']['新钢']['dta3'];
        $arr[2] = $info['工程']['新钢']['dta3']==0 ? 0 : $info['工程']['新钢']['dta4']/$info['工程']['新钢']['dta3'];
        $arr[3] = $info['线材']['新钢']['dta3']==0 ? 0 : $info['工程']['新钢']['dta3']/$info['线材']['新钢']['dta3'];
        $arr[4] = $info['工程']['方大']['dta3'];
        $arr[5] = $info['工程']['方大']['dta3']==0 ? 0 : $info['工程']['方大']['dta4']/$info['工程']['方大']['dta3'];
        $arr[6] = $info['线材']['方大']['dta3']==0 ? 0 : $info['工程']['方大']['dta3']/$info['线材']['方大']['dta3'];
        $arr[7] = $arr[2]-$arr[5];
        $arr[8] = $info['工程']['工程']['dta3'];
        $arr[9] = $info['工程']['工程']['dta3']==0 ? 0 : $info['工程']['工程']['dta4']/$info['工程']['工程']['dta3'];

        $sum8 = 0;
        $sum9 = 0;
        $sum8 += $arr[8];
        $sum9 += $arr[8]*$arr[9];

        $arr[2] = round($arr[2]);
        $arr[3] = round($arr[3]*100,1)."%";
        $arr[5] = round($arr[5]);
        $arr[6] = round($arr[6]*100,1)."%";
        $arr[9] = round($arr[9]);
        $data[] = $arr;

        $arr = array();
        $arr[0] = "制品原料用高碳钢线材（硬线）";
        $arr[1] = $info['制品']['新钢']['dta3'];
        $arr[2] = $info['制品']['新钢']['dta3']==0 ? 0 : $info['制品']['新钢']['dta4']/$info['制品']['新钢']['dta3'];
        $arr[3] = $info['线材']['新钢']['dta3']==0 ? 0 : $info['制品']['新钢']['dta3']/$info['线材']['新钢']['dta3'];
        $arr[4] = $info['制品']['方大']['dta3'];
        $arr[5] = $info['制品']['方大']['dta3']==0 ? 0 : $info['制品']['方大']['dta4']/$info['制品']['方大']['dta3'];
        $arr[6] = $info['线材']['方大']['dta3']==0 ? 0 : $info['制品']['方大']['dta3']/$info['线材']['方大']['dta3'];
        $arr[7] = $arr[2]-$arr[5];
        $arr[8] = $info['制品']['制品']['dta3'];
        $arr[9] = $info['制品']['制品']['dta3']==0 ? 0 : $info['制品']['制品']['dta4']/$info['制品']['制品']['dta3'];

        $sum8 += $arr[8];
        $sum9 += $arr[8]*$arr[9];

        $arr[2] = round($arr[2]);
        $arr[3] = round($arr[3]*100,1)."%";
        $arr[5] = round($arr[5]);
        $arr[6] = round($arr[6]*100,1)."%";
        $arr[9] = round($arr[9]);
        $data[] = $arr;

        $arr = array();
        $arr[0] = "拉拔用线材（软线）";
        $arr[1] = $info['拉拔']['新钢']['dta3'];
        $arr[2] = $info['拉拔']['新钢']['dta3']==0 ? 0 : $info['拉拔']['新钢']['dta4']/$info['拉拔']['新钢']['dta3'];
        $arr[3] = $info['线材']['新钢']['dta3']==0 ? 0 : $info['拉拔']['新钢']['dta3']/$info['线材']['新钢']['dta3'];
        $arr[4] = $info['拉拔']['方大']['dta3'];
        $arr[5] = $info['拉拔']['方大']['dta3']==0 ? 0 : $info['拉拔']['方大']['dta4']/$info['拉拔']['方大']['dta3'];
        $arr[6] = $info['线材']['方大']['dta3']==0 ? 0 : $info['拉拔']['方大']['dta3']/$info['线材']['方大']['dta3'];
        $arr[7] = $arr[2]-$arr[5];
        $arr[8] = $info['拉拔']['拉拔']['dta3'];
        $arr[9] = $info['拉拔']['拉拔']['dta3']==0 ? 0 : $info['拉拔']['拉拔']['dta4']/$info['拉拔']['拉拔']['dta3'];

        $sum8 += $arr[8];
        $sum9 += $arr[8]*$arr[9];

        $arr[2] = round($arr[2]);
        $arr[3] = round($arr[3]*100,1)."%";
        $arr[5] = round($arr[5]);
        $arr[6] = round($arr[6]*100,1)."%";
        $arr[9] = round($arr[9]);
        $data[] = $arr;

        $arr = array();
        $arr[0] = "冷墩铆螺用线材";
        $arr[1] = $info['冷墩']['新钢']['dta3'];
        $arr[2] = $info['冷墩']['新钢']['dta3']==0 ? 0 : $info['冷墩']['新钢']['dta4']/$info['冷墩']['新钢']['dta3'];
        $arr[3] = $info['线材']['新钢']['dta3']==0 ? 0 : $info['冷墩']['新钢']['dta3']/$info['线材']['新钢']['dta3'];
        $arr[4] = $info['冷墩']['方大']['dta3'];
        $arr[5] = $info['冷墩']['方大']['dta3']==0 ? 0 : $info['冷墩']['方大']['dta4']/$info['冷墩']['方大']['dta3'];
        $arr[6] = $info['线材']['方大']['dta3']==0 ? 0 : $info['冷墩']['方大']['dta3']/$info['线材']['方大']['dta3'];
        $arr[7] = $arr[2]-$arr[5];
        $arr[8] = $info['冷墩']['冷墩']['dta3'];
        $arr[9] = $info['冷墩']['冷墩']['dta3']==0 ? 0 : $info['冷墩']['冷墩']['dta4']/$info['冷墩']['冷墩']['dta3'];

        $sum8 += $arr[8];
        $sum9 += $arr[8]*$arr[9];

        $arr[2] = round($arr[2]);
        $arr[3] = round($arr[3]*100,1)."%";
        $arr[5] = round($arr[5]);
        $arr[6] = round($arr[6]*100,1)."%";
        $arr[9] = round($arr[9]);
        $data[] = $arr;

        $arr = array();
        $arr[0] = "电焊条用线材";
        $arr[1] = $info['电焊条']['新钢']['dta3'];
        $arr[2] = $info['电焊条']['新钢']['dta3']==0 ? 0 : $info['电焊条']['新钢']['dta4']/$info['电焊条']['新钢']['dta3'];
        $arr[3] = $info['线材']['新钢']['dta3']==0 ? 0 : $info['电焊条']['新钢']['dta3']/$info['线材']['新钢']['dta3'];
        $arr[4] = $info['电焊条']['方大']['dta3'];
        $arr[5] = $info['电焊条']['方大']['dta3']==0 ? 0 : $info['电焊条']['方大']['dta4']/$info['电焊条']['方大']['dta3'];
        $arr[6] = $info['线材']['方大']['dta3']==0 ? 0 : $info['电焊条']['方大']['dta3']/$info['线材']['方大']['dta3'];
        $arr[7] = $arr[2]-$arr[5];
        $arr[8] = $info['电焊条']['电焊条']['dta3'];
        $arr[9] = $info['电焊条']['电焊条']['dta3']==0 ? 0 : $info['电焊条']['电焊条']['dta4']/$info['电焊条']['电焊条']['dta3'];

        $sum8 += $arr[8];
        $sum9 += $arr[8]*$arr[9];

        $arr[2] = round($arr[2]);
        $arr[3] = round($arr[3]*100,1)."%";
        $arr[5] = round($arr[5]);
        $arr[6] = round($arr[6]*100,1)."%";
        $arr[9] = round($arr[9]);
        $data[] = $arr;

        $arr = array();
        $arr[0] = "弹簧用线材";
        $arr[1] = $info['弹簧']['新钢']['dta3'];
        $arr[2] = $info['弹簧']['新钢']['dta3']==0 ? 0 : $info['弹簧']['新钢']['dta4']/$info['弹簧']['新钢']['dta3'];
        $arr[3] = $info['线材']['新钢']['dta3']==0 ? 0 : $info['弹簧']['新钢']['dta3']/$info['线材']['新钢']['dta3'];
        $arr[4] = $info['弹簧']['方大']['dta3'];
        $arr[5] = $info['弹簧']['方大']['dta3']==0 ? 0 : $info['弹簧']['方大']['dta4']/$info['弹簧']['方大']['dta3'];
        $arr[6] = $info['线材']['方大']['dta3']==0 ? 0 : $info['弹簧']['方大']['dta3']/$info['线材']['方大']['dta3'];
        $arr[7] = $arr[2]-$arr[5];
        $arr[8] = $info['弹簧']['弹簧']['dta3'];
        $arr[9] = $info['弹簧']['弹簧']['dta3']==0 ? 0 : $info['弹簧']['弹簧']['dta4']/$info['弹簧']['弹簧']['dta3'];

        $sum8 += $arr[8];
        $sum9 += $arr[8]*$arr[9];

        $arr[2] = round($arr[2]);
        $arr[3] = round($arr[3]*100,1)."%";
        $arr[5] = round($arr[5]);
        $arr[6] = round($arr[6]*100,1)."%";
        $arr[9] = round($arr[9]);
        $data[] = $arr;

        $arr = array();
        $arr[0] = "其他用途用线材";
        $arr[1] = $info['其他线材']['新钢']['dta3'];
        $arr[2] = $info['其他线材']['新钢']['dta3']==0 ? 0 : $info['其他线材']['新钢']['dta4']/$info['其他线材']['新钢']['dta3'];
        $arr[3] = $info['线材']['新钢']['dta3']==0 ? 0 : $info['其他线材']['新钢']['dta3']/$info['线材']['新钢']['dta3'];
        $arr[4] = $info['其他线材']['方大']['dta3'];
        $arr[5] = $info['其他线材']['方大']['dta3']==0 ? 0 : $info['其他线材']['方大']['dta4']/$info['其他线材']['方大']['dta3'];
        $arr[6] = $info['线材']['方大']['dta3']==0 ? 0 : $info['其他线材']['方大']['dta3']/$info['线材']['方大']['dta3'];
        $arr[7] = $arr[2]-$arr[5];
        $arr[8] = $info['其他线材']['其他线材']['dta3'];
        $arr[9] = $info['其他线材']['其他线材']['dta3']==0 ? 0 : $info['其他线材']['其他线材']['dta4']/$info['其他线材']['其他线材']['dta3'];

        $sum8 += $arr[8];
        $sum9 += $arr[8]*$arr[9];

        $arr[2] = round($arr[2]);
        $arr[3] = round($arr[3]*100,1)."%";
        $arr[5] = round($arr[5]);
        $arr[6] = round($arr[6]*100,1)."%";
        $arr[9] = round($arr[9]);
        $data[] = $arr;

        $arr = array();
        $arr[0] = "2、线材(盘条)小计";
        $arr[1] = $info['线材']['新钢']['dta3'];
        $arr[2] = $info['线材']['新钢']['dta3']==0 ? 0 : $info['线材']['新钢']['dta4']/$info['线材']['新钢']['dta3'];
        $arr[3] = "1.00";
        $arr[4] = $info['线材']['方大']['dta3'];
        $arr[5] = $info['线材']['方大']['dta3']==0 ? 0 : $info['线材']['方大']['dta4']/$info['线材']['方大']['dta3'];
        $arr[6] = "1.00";
        $arr[7] = $arr[2]-$arr[5];
        $arr[8] = $sum8;
        $arr[9] = $sum8==0 ? 0 : round($sum9/$sum8);

        $arr[2] = round($arr[2]);
        $arr[5] = round($arr[5]);
        $arr[7] = round($arr[7]);
        $data[] = $arr;



        $this->assign("data", $data);
        $this->assign("date_month", $date_month);
        $this->assign("params", $params);
    }

    public function huizong_gcduibiao_lz($params){
        $params['date'] = $params['date']=="" ? date("Y-m",strtotime("-1 month")) : $params['date'];
        $params['mode'] = $params['mode']=="" ? 1 : $params['mode'];
        $date = date("Y-m-01",strtotime($params['date']."-01"));
        $params['bigtype'] = $params['bigtype']=="" ? 1 : $params['bigtype'];
        $bigtype = $params['bigtype'];

        if($bigtype==1){
            $date_month = date("Y年n月",strtotime($params['date']."-01"));
        }else{
            $date_month = date("Y年1-n月",strtotime($params['date']."-01"));
        }

        $variety_arr = array(
            "按用途分：汽车用冷轧薄宽钢带"=>"冷卷",
            "家电用冷轧薄宽钢带"=>"冷卷",
            "建筑用冷轧薄宽钢带"=>"冷卷",
            "其他用途冷轧薄宽钢带"=>"冷卷",
            "工程机械用冷轧薄宽钢带"=>"冷卷",
            "钢质家居用冷轧薄宽钢带"=>"冷卷",
            "农机用冷轧薄宽钢带"=>"冷卷",
            "不锈冷轧薄宽钢带"=>"冷卷",
        );

        $variety_arr2 = array(
            "按用途分：汽车用冷轧薄宽钢带"=>"汽车",
            "家电用冷轧薄宽钢带"=>"家电",
            "建筑用冷轧薄宽钢带"=>"建筑",
            "其他用途冷轧薄宽钢带"=>"其他",
            "工程机械用冷轧薄宽钢带"=>"工程",
            "钢质家居用冷轧薄宽钢带"=>"家居",
            "农机用冷轧薄宽钢带"=>"农机",
            "不锈冷轧薄宽钢带"=>"不锈",
        );

        $com_arr = array("新余钢铁有限责任公司"=>"新钢","湘潭钢铁集团有限公司"=>"湘钢","涟源钢铁集团有限公司"=>"涟钢","湖南华菱涟源钢铁有限公司"=>"涟钢","方大特钢科技股份有限公司"=>"方大");

        //详细数据
        $this->xg->execute("set names 'utf8';");
        $sql = "select id from data_table_base where type=4 and isdel=0 and uptype='".$bigtype."' and sdate='".$date."' limit 1 ";
        $baseid = $this->xg->getOne($sql);

        $sql = "select dta1,dta2,dta3,dta4,dta5,dta6 from data_table where baseid='".$baseid."' ";
        $list = $this->xg->query($sql);
        //print_r($list);
        $info = array();
        foreach ($list as $key=>$tmp){

            $dta1 = trim($tmp['dta1']);
            $dta1 = mb_ereg_replace('^(　| )+','', $dta1);
            if(isset($variety_arr[$dta1])){

                $dta2 = trim($tmp['dta2']);
                $dta2 = mb_ereg_replace('^(　| )+','', $dta2);
                if($dta1==$dta2){
                    //行业销售量，行业价格
                    $dta1_ = $variety_arr2[$dta1];
                    $info[$dta1_][$dta1_]['dta3'] += (float)$tmp['dta3'];
                    $info[$dta1_][$dta1_]['dta4'] += (float)$tmp['dta3']*(float)$tmp['dta4'];
                }
                if(isset($com_arr[$dta2])){

                    $dta2 = $com_arr[$dta2];
                    $dta1_ = $variety_arr2[$dta1];
                    $dta1 = $variety_arr[$dta1];

                    $info[$dta1_][$dta2]['dta3'] += (float)$tmp['dta3'];
                    $info[$dta1_][$dta2]['dta4'] += (float)$tmp['dta4']*(float)$tmp['dta3'];

                    $info[$dta1][$dta2]['dta3'] += (float)$tmp['dta3'];
                    $info[$dta1][$dta2]['dta4'] += (float)$tmp['dta4']*(float)$tmp['dta3'];
                }
            }
        }
        $info['冷卷']['新钢']['dta3'] -= $info['不锈']['新钢']['dta3'];
        $info['冷卷']['新钢']['dta4'] -= $info['不锈']['新钢']['dta4'];
        $info['冷卷']['涟钢']['dta3'] -= $info['不锈']['涟钢']['dta3'];
        $info['冷卷']['涟钢']['dta4'] -= $info['不锈']['涟钢']['dta4'];

        $info['其他']['其他']['dta3'] -= $info['不锈']['不锈']['dta3'];
        $info['其他']['其他']['dta4'] -= $info['不锈']['不锈']['dta4'];

        //echo "<pre>";
        //print_r($info);

        $sum9 = 0;
        $sum10 = 0;
        $sum11 = 0;
        $sum12 = 0;

        //dta3 销量 、dta4 总价格
        $data = array();
        $arr = array();
        $arr[0] = "按用途分：汽车用冷轧薄宽钢带";
        $arr[1] = $info['汽车']['新钢']['dta3'];
        $arr[2] = $info['汽车']['新钢']['dta3']==0 ? 0 : $info['汽车']['新钢']['dta4']/$info['汽车']['新钢']['dta3'];
        $arr[3] = $info['冷卷']['新钢']['dta3']==0 ? 0 : $info['汽车']['新钢']['dta3']/$info['冷卷']['新钢']['dta3']*100;
        $arr[4] = $info['汽车']['涟钢']['dta3'];
        $arr[5] = $info['汽车']['涟钢']['dta3']==0 ? 0 : $info['汽车']['涟钢']['dta4']/$info['汽车']['涟钢']['dta3'];
        $arr[6] = $info['冷卷']['涟钢']['dta3']==0 ? 0 : $info['汽车']['涟钢']['dta3']/$info['冷卷']['涟钢']['dta3']*100;
        $arr[7] = $arr[2]-$arr[5];
        $arr[8] = $arr[3]-$arr[6];
        $arr[9] = $info['冷卷']['涟钢']['dta3']==0 ? 0 : $arr[8]*($arr[5]-$info['冷卷']['涟钢']['dta4']/$info['冷卷']['涟钢']['dta3']);
        $arr[10] = $arr[3]*$arr[7];
        $arr[11] = $info['汽车']['汽车']['dta3'];
        $arr[12] = $info['汽车']['汽车']['dta3']==0 ? 0 : $info['汽车']['汽车']['dta4']/$info['汽车']['汽车']['dta3'];

        $arr[2] = round($arr[2]);
        $arr[3] = round($arr[3],1)."%";
        $arr[5] = round($arr[5]);
        $arr[6] = round($arr[6],1)."%";
        $arr[8] = round($arr[8],2)."%";
        $arr[9] = round($arr[9]/100);
        $arr[10] = round($arr[10]/100);
        $arr[12] = round($arr[12]);
        $data[] = $arr;

        $sum9 += $arr[9];
        $sum10 += $arr[10];
        $sum11 += $arr[11];
        $sum12 += $arr[11]*$arr[12];

        $arr = array();
        $arr[0] = "家电用冷轧薄宽钢带";
        $arr[1] = $info['家电']['新钢']['dta3'];
        $arr[2] = $info['家电']['新钢']['dta3']==0 ? 0 : $info['家电']['新钢']['dta4']/$info['家电']['新钢']['dta3'];
        $arr[3] = $info['冷卷']['新钢']['dta3']==0 ? 0 : $info['家电']['新钢']['dta3']/$info['冷卷']['新钢']['dta3']*100;
        $arr[4] = $info['家电']['涟钢']['dta3'];
        $arr[5] = $info['家电']['涟钢']['dta3']==0 ? 0 : $info['家电']['涟钢']['dta4']/$info['家电']['涟钢']['dta3'];
        $arr[6] = $info['冷卷']['涟钢']['dta3']==0 ? 0 : $info['家电']['涟钢']['dta3']/$info['冷卷']['涟钢']['dta3']*100;
        $arr[7] = $arr[2]-$arr[5];
        $arr[8] = $arr[3]-$arr[6];
        $arr[9] = $info['冷卷']['涟钢']['dta3']==0 ? 0 : $arr[8]*($arr[5]-$info['冷卷']['涟钢']['dta4']/$info['冷卷']['涟钢']['dta3']);
        $arr[10] = $arr[3]*$arr[7];
        $arr[11] = $info['家电']['家电']['dta3'];
        $arr[12] = $info['家电']['家电']['dta3']==0 ? 0 : $info['家电']['家电']['dta4']/$info['家电']['家电']['dta3'];

        $arr[2] = round($arr[2]);
        $arr[3] = round($arr[3],1)."%";
        $arr[5] = round($arr[5]);
        $arr[6] = round($arr[6],1)."%";
        $arr[8] = round($arr[8],2)."%";
        $arr[9] = round($arr[9]/100);
        $arr[10] = round($arr[10]/100);
        $arr[12] = round($arr[12]);
        $data[] = $arr;

        $sum9 += $arr[9];
        $sum10 += $arr[10];
        $sum11 += $arr[11];
        $sum12 += $arr[11]*$arr[12];

        $arr = array();
        $arr[0] = "建筑用冷轧薄宽钢带";
        $arr[1] = $info['建筑']['新钢']['dta3'];
        $arr[2] = $info['建筑']['新钢']['dta3']==0 ? 0 : $info['建筑']['新钢']['dta4']/$info['建筑']['新钢']['dta3'];
        $arr[3] = $info['冷卷']['新钢']['dta3']==0 ? 0 : $info['建筑']['新钢']['dta3']/$info['冷卷']['新钢']['dta3']*100;
        $arr[4] = $info['建筑']['涟钢']['dta3'];
        $arr[5] = $info['建筑']['涟钢']['dta3']==0 ? 0 : $info['建筑']['涟钢']['dta4']/$info['建筑']['涟钢']['dta3'];
        $arr[6] = $info['冷卷']['涟钢']['dta3']==0 ? 0 : $info['建筑']['涟钢']['dta3']/$info['冷卷']['涟钢']['dta3']*100;
        $arr[7] = $arr[2]-$arr[5];
        $arr[8] = $arr[3]-$arr[6];
        $arr[9] = $info['冷卷']['涟钢']['dta3']==0 ? 0 : $arr[8]*($arr[5]-$info['冷卷']['涟钢']['dta4']/$info['冷卷']['涟钢']['dta3']);
        $arr[10] = $arr[3]*$arr[7];
        $arr[11] = $info['建筑']['建筑']['dta3'];
        $arr[12] = $info['建筑']['建筑']['dta3']==0 ? 0 : $info['建筑']['建筑']['dta4']/$info['建筑']['建筑']['dta3'];

        $arr[2] = round($arr[2]);
        $arr[3] = round($arr[3],1)."%";
        $arr[5] = round($arr[5]);
        $arr[6] = round($arr[6],1)."%";
        $arr[8] = round($arr[8],2)."%";
        $arr[9] = round($arr[9]/100);
        $arr[10] = round($arr[10]/100);
        $arr[12] = round($arr[12]);
        $data[] = $arr;

        $sum9 += $arr[9];
        $sum10 += $arr[10];
        $sum11 += $arr[11];
        $sum12 += $arr[11]*$arr[12];

        $arr = array();
        $arr[0] = "钢质家居用冷轧薄宽钢带";
        $arr[1] = $info['家居']['新钢']['dta3'];
        $arr[2] = $info['家居']['新钢']['dta3']==0 ? 0 : $info['家居']['新钢']['dta4']/$info['家居']['新钢']['dta3'];
        $arr[3] = $info['冷卷']['新钢']['dta3']==0 ? 0 : $info['家居']['新钢']['dta3']/$info['冷卷']['新钢']['dta3']*100;
        $arr[4] = $info['家居']['涟钢']['dta3'];
        $arr[5] = $info['家居']['涟钢']['dta3']==0 ? 0 : $info['家居']['涟钢']['dta4']/$info['家居']['涟钢']['dta3'];
        $arr[6] = $info['冷卷']['涟钢']['dta3']==0 ? 0 : $info['家居']['涟钢']['dta3']/$info['冷卷']['涟钢']['dta3']*100;
        $arr[7] = $arr[2]-$arr[5];
        $arr[8] = $arr[3]-$arr[6];
        $arr[9] = $info['冷卷']['涟钢']['dta3']==0 ? 0 : $arr[8]*($arr[5]-$info['冷卷']['涟钢']['dta4']/$info['冷卷']['涟钢']['dta3']);
        $arr[10] = $arr[3]*$arr[7];
        $arr[11] = $info['家居']['家居']['dta3'];
        $arr[12] = $info['家居']['家居']['dta3']==0 ? 0 : $info['家居']['家居']['dta4']/$info['家居']['家居']['dta3'];

        $arr[2] = round($arr[2]);
        $arr[3] = round($arr[3],1)."%";
        $arr[5] = round($arr[5]);
        $arr[6] = round($arr[6],1)."%";
        $arr[8] = round($arr[8],2)."%";
        $arr[9] = round($arr[9]/100);
        $arr[10] = round($arr[10]/100);
        $arr[12] = round($arr[12]);
        $data[] = $arr;

        $sum9 += $arr[9];
        $sum10 += $arr[10];
        $sum11 += $arr[11];
        $sum12 += $arr[11]*$arr[12];

        $arr = array();
        $arr[0] = "工程机械用冷轧薄宽钢带";
        $arr[1] = $info['工程']['新钢']['dta3'];
        $arr[2] = $info['工程']['新钢']['dta3']==0 ? 0 : $info['工程']['新钢']['dta4']/$info['工程']['新钢']['dta3'];
        $arr[3] = $info['冷卷']['新钢']['dta3']==0 ? 0 : $info['工程']['新钢']['dta3']/$info['冷卷']['新钢']['dta3']*100;
        $arr[4] = $info['工程']['涟钢']['dta3'];
        $arr[5] = $info['工程']['涟钢']['dta3']==0 ? 0 : $info['工程']['涟钢']['dta4']/$info['工程']['涟钢']['dta3'];
        $arr[6] = $info['冷卷']['涟钢']['dta3']==0 ? 0 : $info['工程']['涟钢']['dta3']/$info['冷卷']['涟钢']['dta3']*100;
        $arr[7] = $arr[2]-$arr[5];
        $arr[8] = $arr[3]-$arr[6];
        $arr[9] = $info['冷卷']['涟钢']['dta3']==0 ? 0 : $arr[8]*($arr[5]-$info['冷卷']['涟钢']['dta4']/$info['冷卷']['涟钢']['dta3']);
        $arr[10] = $arr[3]*$arr[7];
        $arr[11] = $info['工程']['工程']['dta3'];
        $arr[12] = $info['工程']['工程']['dta3']==0 ? 0 : $info['工程']['工程']['dta4']/$info['工程']['工程']['dta3'];

        $arr[2] = round($arr[2]);
        $arr[3] = round($arr[3],1)."%";
        $arr[5] = round($arr[5]);
        $arr[6] = round($arr[6],1)."%";
        $arr[8] = round($arr[8],2)."%";
        $arr[9] = round($arr[9]/100);
        $arr[10] = round($arr[10]/100);
        $arr[12] = round($arr[12]);
        $data[] = $arr;

        $sum9 += $arr[9];
        $sum10 += $arr[10];
        $sum11 += $arr[11];
        $sum12 += $arr[11]*$arr[12];

        $arr = array();
        $arr[0] = "农机用冷轧薄宽钢带";
        $arr[1] = $info['农机']['新钢']['dta3'];
        $arr[2] = $info['农机']['新钢']['dta3']==0 ? 0 : $info['农机']['新钢']['dta4']/$info['农机']['新钢']['dta3'];
        $arr[3] = $info['冷卷']['新钢']['dta3']==0 ? 0 : $info['农机']['新钢']['dta3']/$info['冷卷']['新钢']['dta3']*100;
        $arr[4] = $info['农机']['涟钢']['dta3'];
        $arr[5] = $info['农机']['涟钢']['dta3']==0 ? 0 : $info['农机']['涟钢']['dta4']/$info['农机']['涟钢']['dta3'];
        $arr[6] = $info['冷卷']['涟钢']['dta3']==0 ? 0 : $info['农机']['涟钢']['dta3']/$info['冷卷']['涟钢']['dta3']*100;
        $arr[7] = $arr[2]-$arr[5];
        $arr[8] = $arr[3]-$arr[6];
        $arr[9] = $info['冷卷']['涟钢']['dta3']==0 ? 0 : $arr[8]*($arr[5]-$info['冷卷']['涟钢']['dta4']/$info['冷卷']['涟钢']['dta3']);
        $arr[10] = $arr[3]*$arr[7];
        $arr[11] = $info['农机']['农机']['dta3'];
        $arr[12] = $info['农机']['农机']['dta3']==0 ? 0 : $info['农机']['农机']['dta4']/$info['农机']['农机']['dta3'];

        $arr[2] = round($arr[2]);
        $arr[3] = round($arr[3],1)."%";
        $arr[5] = round($arr[5]);
        $arr[6] = round($arr[6],1)."%";
        $arr[8] = round($arr[8],2)."%";
        $arr[9] = round($arr[9]/100);
        $arr[10] = round($arr[10]/100);
        $arr[12] = round($arr[12]);
        $data[] = $arr;

        $sum9 += $arr[9];
        $sum10 += $arr[10];
        $sum11 += $arr[11];
        $sum12 += $arr[11]*$arr[12];

        $arr = array();
        $arr[0] = "其他用途冷轧薄宽钢带";
        $arr[1] = $info['其他']['新钢']['dta3'];
        $arr[2] = $info['其他']['新钢']['dta3']==0 ? 0 : $info['其他']['新钢']['dta4']/$info['其他']['新钢']['dta3'];
        $arr[3] = $info['冷卷']['新钢']['dta3']==0 ? 0 : $info['其他']['新钢']['dta3']/$info['冷卷']['新钢']['dta3']*100;
        $arr[4] = $info['其他']['涟钢']['dta3'];
        $arr[5] = $info['其他']['涟钢']['dta3']==0 ? 0 : $info['其他']['涟钢']['dta4']/$info['其他']['涟钢']['dta3'];
        $arr[6] = $info['冷卷']['涟钢']['dta3']==0 ? 0 : $info['其他']['涟钢']['dta3']/$info['冷卷']['涟钢']['dta3']*100;
        $arr[7] = $arr[2]-$arr[5];
        $arr[8] = $arr[3]-$arr[6];
        $arr[9] = $info['冷卷']['涟钢']['dta3']==0 ? 0 : $arr[8]*($arr[5]-$info['冷卷']['涟钢']['dta4']/$info['冷卷']['涟钢']['dta3']);
        $arr[10] = $arr[3]*$arr[7];
        $arr[11] = $info['其他']['其他']['dta3'];
        $arr[12] = $info['其他']['其他']['dta3']==0 ? 0 : $info['其他']['其他']['dta4']/$info['其他']['其他']['dta3'];

        $arr[2] = round($arr[2]);
        $arr[3] = round($arr[3],1)."%";
        $arr[5] = round($arr[5]);
        $arr[6] = round($arr[6],1)."%";
        $arr[8] = round($arr[8],2)."%";
        $arr[9] = round($arr[9]/100);
        $arr[10] = round($arr[10]/100);
        $arr[12] = round($arr[12]);
        $data[] = $arr;

        $sum9 += $arr[9];
        $sum10 += $arr[10];
        $sum11 += $arr[11];
        $sum12 += $arr[11]*$arr[12];

        $arr = array();
        $arr[0] = "冷卷合计";
        $arr[1] = $info['冷卷']['新钢']['dta3'];
        $arr[2] = $info['冷卷']['新钢']['dta3']==0 ? 0 : $info['冷卷']['新钢']['dta4']/$info['冷卷']['新钢']['dta3'];
        $arr[3] = "1.00";
        $arr[4] = $info['冷卷']['涟钢']['dta3'];
        $arr[5] = $info['冷卷']['涟钢']['dta3']==0 ? 0 : $info['冷卷']['涟钢']['dta4']/$info['冷卷']['涟钢']['dta3'];
        $arr[6] = "1.00";
        $arr[7] = round($arr[2]-$arr[5],2);
        $arr[8] = "0.00";
        $arr[9] = $sum9;
        $arr[10] = $sum10;
        $arr[11] = $sum11;
        $arr[12] = $sum11==0 ? 0 : round($sum12/$sum11);

        $arr[2] = round($arr[2]);
        $arr[5] = round($arr[5]);
        $data[] = $arr;



        $this->assign("data", $data);
        $this->assign("date_month", $date_month);
        $this->assign("params", $params);
    }

    public function huizong_gcduibiao_rz($params){
        $params['date'] = $params['date']=="" ? date("Y-m",strtotime("-1 month")) : $params['date'];
        $params['mode'] = $params['mode']=="" ? 1 : $params['mode'];
        $date = date("Y-m-01",strtotime($params['date']."-01"));
        $params['bigtype'] = $params['bigtype']=="" ? 1 : $params['bigtype'];
        $bigtype = $params['bigtype'];

        if($bigtype==1){
            $date_month = date("Y年n月",strtotime($params['date']."-01"));
        }else{
            $date_month = date("Y年1-n月",strtotime($params['date']."-01"));
        }

        $variety_arr = array(
            "按用途分：建筑用中厚宽钢带"=>"热卷",
            "工程机械用中厚宽钢带"=>"热卷",
            "管线用中厚宽钢带"=>"热卷",
            "汽车用中厚宽钢带"=>"热卷",
            "集装箱用中厚宽钢带"=>"热卷",
            "桥梁用中厚宽钢带"=>"热卷",
            "造船用中厚宽钢带"=>"热卷",
            "其他用途中厚宽钢带"=>"热卷",
            "按用途分：工程机械用热轧薄宽钢带"=>"热卷",
            "建筑用热轧薄宽钢带"=>"热卷",
            "轻工家电用热轧薄宽钢带"=>"热卷",
            "焊管用热轧薄宽钢带"=>"热卷",
            "集装箱用热轧薄宽钢带"=>"热卷",
            "造船用热轧薄宽钢带"=>"热卷",
            "汽车用热轧薄宽钢带"=>"热卷",
            "其他用途热轧薄宽钢带"=>"热卷",
        );

        $variety_arr2 = array(
            "按用途分：建筑用中厚宽钢带"=>"建筑",
            "工程机械用中厚宽钢带"=>"工程",
            "管线用中厚宽钢带"=>"管线",
            "汽车用中厚宽钢带"=>"汽车",
            "集装箱用中厚宽钢带"=>"集装箱",
            "桥梁用中厚宽钢带"=>"桥梁",
            "造船用中厚宽钢带"=>"造船",
            "其他用途中厚宽钢带"=>"其他",
            "按用途分：工程机械用热轧薄宽钢带"=>"工程",
            "建筑用热轧薄宽钢带"=>"建筑",
            "轻工家电用热轧薄宽钢带"=>"轻工",
            "焊管用热轧薄宽钢带"=>"焊管",
            "集装箱用热轧薄宽钢带"=>"集装箱",
            "造船用热轧薄宽钢带"=>"造船",
            "汽车用热轧薄宽钢带"=>"汽车",
            "其他用途热轧薄宽钢带"=>"其他",
        );

        $com_arr = array("新余钢铁有限责任公司"=>"新钢","湘潭钢铁集团有限公司"=>"湘钢","涟源钢铁集团有限公司"=>"涟钢","湖南华菱涟源钢铁有限公司"=>"涟钢","方大特钢科技股份有限公司"=>"方大","其中：太原钢铁(集团)有限公司"=>"太钢");

        //详细数据
        $this->xg->execute("set names 'utf8';");
        $sql = "select id from data_table_base where type=4 and isdel=0 and uptype='".$bigtype."' and sdate='".$date."' limit 1 ";
        $baseid = $this->xg->getOne($sql);

        $sql = "select dta1,dta2,dta3,dta4,dta5,dta6 from data_table where baseid='".$baseid."' ";
        $list = $this->xg->query($sql);
        //print_r($list);
        $info = array();
        $debug_table = "<table border='1'>";
        $debug_table .= "<tr><td></td><td>销售量</td><td>平均价格</td></tr>";
        foreach ($list as $key=>$tmp){
            $dta1 = trim($tmp['dta1']);
            $dta1 = mb_ereg_replace('^(　| )+','', $dta1);
            if(isset($variety_arr[$dta1])){

                $dta2 = trim($tmp['dta2']);
                $dta2 = mb_ereg_replace('^(　| )+','', $dta2);
                if($dta1==$dta2){
                    //行业销售量，行业价格
                    $dta1_ = $variety_arr2[$dta1];
                    $info[$dta1_][$dta1_]['dta3'] += (float)$tmp['dta3'];
                    $info[$dta1_][$dta1_]['dta4'] += (float)$tmp['dta3']*(float)$tmp['dta4'];
                    $debug_table .= "<tr><td>".$dta1."</td><td>".$tmp['dta3']."</td><td>".$tmp['dta4']."</td></tr>";
                }
                if(isset($com_arr[$dta2])){

                    $dta2 = $com_arr[$dta2];
                    $dta1_ = $variety_arr2[$dta1];
                    $dta1 = $variety_arr[$dta1];

                    $info[$dta1_][$dta2]['dta3'] += (float)$tmp['dta3'];
                    $info[$dta1_][$dta2]['dta4'] += (float)$tmp['dta4']*(float)$tmp['dta3'];

                    $info[$dta1][$dta2]['dta3'] += (float)$tmp['dta3'];
                    $info[$dta1][$dta2]['dta4'] += (float)$tmp['dta4']*(float)$tmp['dta3'];
                }
            }
        }
        $debug_table .="</table>";
        if($params['debug']=="1"){
            echo $debug_table;
            exit;
        }

        $info['建筑']['建筑']['dta3'] -= $info['建筑']['太钢']['dta3'];
        $info['建筑']['建筑']['dta4'] -= $info['建筑']['太钢']['dta4'];

        $info['工程']['工程']['dta3'] -= $info['工程']['太钢']['dta3'];
        $info['工程']['工程']['dta4'] -= $info['工程']['太钢']['dta4'];

        $info['管线']['管线']['dta3'] -= $info['管线']['太钢']['dta3'];
        $info['管线']['管线']['dta4'] -= $info['管线']['太钢']['dta4'];

        $info['汽车']['汽车']['dta3'] -= $info['汽车']['太钢']['dta3'];
        $info['汽车']['汽车']['dta4'] -= $info['汽车']['太钢']['dta4'];

        $info['集装箱']['集装箱']['dta3'] -= $info['集装箱']['太钢']['dta3'];
        $info['集装箱']['集装箱']['dta4'] -= $info['集装箱']['太钢']['dta4'];

        $info['桥梁']['桥梁']['dta3'] -= $info['桥梁']['太钢']['dta3'];
        $info['桥梁']['桥梁']['dta4'] -= $info['桥梁']['太钢']['dta4'];

        $info['造船']['造船']['dta3'] -= $info['造船']['太钢']['dta3'];
        $info['造船']['造船']['dta4'] -= $info['造船']['太钢']['dta4'];

        $info['其他']['其他']['dta3'] -= $info['其他']['太钢']['dta3'];
        $info['其他']['其他']['dta4'] -= $info['其他']['太钢']['dta4'];
        //echo "<pre>";
        //print_r($info);

        $sum9 = 0;
        $sum10 = 0;
        $sum11 = 0;
        $sum12 = 0;

        //dta3 销量 、dta4 总价格
        $data = array();
        $arr = array();
        $arr[0] = "按用途分：建筑用钢卷";
        $arr[1] = $info['建筑']['新钢']['dta3'];
        $arr[2] = $info['建筑']['新钢']['dta3']==0 ? 0 : $info['建筑']['新钢']['dta4']/$info['建筑']['新钢']['dta3'];
        $arr[3] = $info['热卷']['新钢']['dta3']==0 ? 0 : $info['建筑']['新钢']['dta3']/$info['热卷']['新钢']['dta3']*100;
        $arr[4] = $info['建筑']['涟钢']['dta3'];
        $arr[5] = $info['建筑']['涟钢']['dta3']==0 ? 0 : $info['建筑']['涟钢']['dta4']/$info['建筑']['涟钢']['dta3'];
        $arr[6] = $info['热卷']['涟钢']['dta3']==0 ? 0 : $info['建筑']['涟钢']['dta3']/$info['热卷']['涟钢']['dta3']*100;
        $arr[7] = $arr[2]-$arr[5];
        $arr[8] = $arr[3]-$arr[6];
        $arr[9] = $info['热卷']['涟钢']['dta3']==0 ? 0 : $arr[8]*($arr[5]-$info['热卷']['涟钢']['dta4']/$info['热卷']['涟钢']['dta3'])/100;
        $arr[10] = $arr[3]*$arr[7]/100;
        $arr[11] = $info['建筑']['建筑']['dta3'];
        $arr[12] = $info['建筑']['建筑']['dta3']==0 ? 0 : $info['建筑']['建筑']['dta4']/$info['建筑']['建筑']['dta3'];

        $sum9 += $arr[9];
        $sum10 += $arr[10];
        $sum11 += $arr[11];
        $sum12 += $arr[11]*$arr[12];

        $arr[2] = round($arr[2],2);
        $arr[3] = round($arr[3],1)."%";
        $arr[5] = round($arr[5],2);
        $arr[6] = round($arr[6],1)."%";
        $arr[7] = round($arr[7]);
        $arr[8] = round($arr[8],2)."%";
        $arr[9] = round($arr[9]);
        $arr[10] = round($arr[10]);
        $arr[12] = round($arr[12]);
        $data[] = $arr;


        $arr = array();
        $arr[0] = "工程机械用钢卷";
        $arr[1] = $info['工程']['新钢']['dta3'];
        $arr[2] = $info['工程']['新钢']['dta3']==0 ? 0 : $info['工程']['新钢']['dta4']/$info['工程']['新钢']['dta3'];
        $arr[3] = $info['热卷']['新钢']['dta3']==0 ? 0 : $info['工程']['新钢']['dta3']/$info['热卷']['新钢']['dta3']*100;
        $arr[4] = $info['工程']['涟钢']['dta3'];
        $arr[5] = $info['工程']['涟钢']['dta3']==0 ? 0 : $info['工程']['涟钢']['dta4']/$info['工程']['涟钢']['dta3'];
        $arr[6] = $info['热卷']['涟钢']['dta3']==0 ? 0 : $info['工程']['涟钢']['dta3']/$info['热卷']['涟钢']['dta3']*100;
        $arr[7] = $arr[2]-$arr[5];
        $arr[8] = $arr[3]-$arr[6];
        $arr[9] = $info['热卷']['涟钢']['dta3']==0 ? 0 : $arr[8]*($arr[5]-$info['热卷']['涟钢']['dta4']/$info['热卷']['涟钢']['dta3'])/100;
        $arr[10] = $arr[3]*$arr[7]/100;
        $arr[11] = $info['工程']['工程']['dta3'];
        $arr[12] = $info['工程']['工程']['dta3']==0 ? 0 : $info['工程']['工程']['dta4']/$info['工程']['工程']['dta3'];

        $sum9 += $arr[9];
        $sum10 += $arr[10];
        $sum11 += $arr[11];
        $sum12 += $arr[11]*$arr[12];

        $arr[2] = round($arr[2],2);
        $arr[3] = round($arr[3],1)."%";
        $arr[5] = round($arr[5],2);
        $arr[6] = round($arr[6],1)."%";
        $arr[7] = round($arr[7]);
        $arr[8] = round($arr[8],2)."%";
        $arr[9] = round($arr[9]);
        $arr[10] = round($arr[10]);
        $arr[12] = round($arr[12]);
        $data[] = $arr;


        $arr = array();
        $arr[0] = "汽车用钢卷";
        $arr[1] = $info['汽车']['新钢']['dta3'];
        $arr[2] = $info['汽车']['新钢']['dta3']==0 ? 0 : $info['汽车']['新钢']['dta4']/$info['汽车']['新钢']['dta3'];
        $arr[3] = $info['热卷']['新钢']['dta3']==0 ? 0 : $info['汽车']['新钢']['dta3']/$info['热卷']['新钢']['dta3']*100;
        $arr[4] = $info['汽车']['涟钢']['dta3'];
        $arr[5] = $info['汽车']['涟钢']['dta3']==0 ? 0 : $info['汽车']['涟钢']['dta4']/$info['汽车']['涟钢']['dta3'];
        $arr[6] = $info['热卷']['涟钢']['dta3']==0 ? 0 : $info['汽车']['涟钢']['dta3']/$info['热卷']['涟钢']['dta3']*100;
        $arr[7] = $arr[2]-$arr[5];
        $arr[8] = $arr[3]-$arr[6];
        $arr[9] = $info['热卷']['涟钢']['dta3']==0 ? 0 : $arr[8]*($arr[5]-$info['热卷']['涟钢']['dta4']/$info['热卷']['涟钢']['dta3'])/100;
        $arr[10] = $arr[3]*$arr[7]/100;
        $arr[11] = $info['汽车']['汽车']['dta3'];
        $arr[12] = $info['汽车']['汽车']['dta3']==0 ? 0 : $info['汽车']['汽车']['dta4']/$info['汽车']['汽车']['dta3'];

        $sum9 += $arr[9];
        $sum10 += $arr[10];
        $sum11 += $arr[11];
        $sum12 += $arr[11]*$arr[12];

        $arr[2] = round($arr[2],2);
        $arr[3] = round($arr[3],1)."%";
        $arr[5] = round($arr[5],2);
        $arr[6] = round($arr[6],1)."%";
        $arr[7] = round($arr[7]);
        $arr[8] = round($arr[8],2)."%";
        $arr[9] = round($arr[9]);
        $arr[10] = round($arr[10]);
        $arr[12] = round($arr[12]);
        $data[] = $arr;

        $arr = array();
        $arr[0] = "集装箱用钢卷";
        $arr[1] = $info['集装箱']['新钢']['dta3'];
        $arr[2] = $info['集装箱']['新钢']['dta3']==0 ? 0 : $info['集装箱']['新钢']['dta4']/$info['集装箱']['新钢']['dta3'];
        $arr[3] = $info['热卷']['新钢']['dta3']==0 ? 0 : $info['集装箱']['新钢']['dta3']/$info['热卷']['新钢']['dta3']*100;
        $arr[4] = $info['集装箱']['涟钢']['dta3'];
        $arr[5] = $info['集装箱']['涟钢']['dta3']==0 ? 0 : $info['集装箱']['涟钢']['dta4']/$info['集装箱']['涟钢']['dta3'];
        $arr[6] = $info['热卷']['涟钢']['dta3']==0 ? 0 : $info['集装箱']['涟钢']['dta3']/$info['热卷']['涟钢']['dta3']*100;
        $arr[7] = $arr[2]-$arr[5];
        $arr[8] = $arr[3]-$arr[6];
        $arr[9] = $info['热卷']['涟钢']['dta3']==0 ? 0 : $arr[8]*($arr[5]-$info['热卷']['涟钢']['dta4']/$info['热卷']['涟钢']['dta3'])/100;
        $arr[10] = $arr[3]*$arr[7]/100;
        $arr[11] = $info['集装箱']['集装箱']['dta3'];
        $arr[12] = $info['集装箱']['集装箱']['dta3']==0 ? 0 : $info['集装箱']['集装箱']['dta4']/$info['集装箱']['集装箱']['dta3'];

        $sum9 += $arr[9];
        $sum10 += $arr[10];
        $sum11 += $arr[11];
        $sum12 += $arr[11]*$arr[12];

        $arr[2] = round($arr[2],2);
        $arr[3] = round($arr[3],1)."%";
        $arr[5] = round($arr[5],2);
        $arr[6] = round($arr[6],1)."%";
        $arr[7] = round($arr[7]);
        $arr[8] = round($arr[8],2)."%";
        $arr[9] = round($arr[9]);
        $arr[10] = round($arr[10]);
        $arr[12] = round($arr[12]);
        $data[] = $arr;

        $arr = array();
        $arr[0] = "造船用钢卷";
        $arr[1] = $info['造船']['新钢']['dta3'];
        $arr[2] = $info['造船']['新钢']['dta3']==0 ? 0 : $info['造船']['新钢']['dta4']/$info['造船']['新钢']['dta3'];
        $arr[3] = $info['热卷']['新钢']['dta3']==0 ? 0 : $info['造船']['新钢']['dta3']/$info['热卷']['新钢']['dta3']*100;
        $arr[4] = $info['造船']['涟钢']['dta3'];
        $arr[5] = $info['造船']['涟钢']['dta3']==0 ? 0 : $info['造船']['涟钢']['dta4']/$info['造船']['涟钢']['dta3'];
        $arr[6] = $info['热卷']['涟钢']['dta3']==0 ? 0 : $info['造船']['涟钢']['dta3']/$info['热卷']['涟钢']['dta3']*100;
        $arr[7] = $arr[2]-$arr[5];
        $arr[8] = $arr[3]-$arr[6];
        $arr[9] = $info['热卷']['涟钢']['dta3']==0 ? 0 : $arr[8]*($arr[5]-$info['热卷']['涟钢']['dta4']/$info['热卷']['涟钢']['dta3'])/100;
        $arr[10] = $arr[3]*$arr[7]/100;
        $arr[11] = $info['造船']['造船']['dta3'];
        $arr[12] = $info['造船']['造船']['dta3']==0 ? 0 : $info['造船']['造船']['dta4']/$info['造船']['造船']['dta3'];

        $sum9 += $arr[9];
        $sum10 += $arr[10];
        $sum11 += $arr[11];
        $sum12 += $arr[11]*$arr[12];

        $arr[2] = round($arr[2],2);
        $arr[3] = round($arr[3],1)."%";
        $arr[5] = round($arr[5],2);
        $arr[6] = round($arr[6],1)."%";
        $arr[7] = round($arr[7]);
        $arr[8] = round($arr[8],2)."%";
        $arr[9] = round($arr[9]);
        $arr[10] = round($arr[10]);
        $arr[12] = round($arr[12]);
        $data[] = $arr;


        $arr = array();
        $arr[0] = "桥梁用钢卷";
        $arr[1] = $info['桥梁']['新钢']['dta3'];
        $arr[2] = $info['桥梁']['新钢']['dta3']==0 ? 0 : $info['桥梁']['新钢']['dta4']/$info['桥梁']['新钢']['dta3'];
        $arr[3] = $info['热卷']['新钢']['dta3']==0 ? 0 : $info['桥梁']['新钢']['dta3']/$info['热卷']['新钢']['dta3']*100;
        $arr[4] = $info['桥梁']['涟钢']['dta3'];
        $arr[5] = $info['桥梁']['涟钢']['dta3']==0 ? 0 : $info['桥梁']['涟钢']['dta4']/$info['桥梁']['涟钢']['dta3'];
        $arr[6] = $info['热卷']['涟钢']['dta3']==0 ? 0 : $info['桥梁']['涟钢']['dta3']/$info['热卷']['涟钢']['dta3']*100;
        $arr[7] = $arr[2]-$arr[5];
        $arr[8] = $arr[3]-$arr[6];
        $arr[9] = $info['热卷']['涟钢']['dta3']==0 ? 0 : $arr[8]*($arr[5]-$info['热卷']['涟钢']['dta4']/$info['热卷']['涟钢']['dta3'])/100;
        $arr[10] = $arr[3]*$arr[7]/100;
        $arr[11] = $info['桥梁']['桥梁']['dta3'];
        $arr[12] = $info['桥梁']['桥梁']['dta3']==0 ? 0 : $info['桥梁']['桥梁']['dta4']/$info['桥梁']['桥梁']['dta3'];

        $sum9 += $arr[9];
        $sum10 += $arr[10];
        $sum11 += $arr[11];
        $sum12 += $arr[11]*$arr[12];

        $arr[2] = round($arr[2],2);
        $arr[3] = round($arr[3],1)."%";
        $arr[5] = round($arr[5],2);
        $arr[6] = round($arr[6],1)."%";
        $arr[7] = round($arr[7]);
        $arr[8] = round($arr[8],2)."%";
        $arr[9] = round($arr[9]);
        $arr[10] = round($arr[10]);
        $arr[12] = round($arr[12]);
        $data[] = $arr;


        $arr = array();
        $arr[0] = "轻工家电用钢卷";
        $arr[1] = $info['轻工']['新钢']['dta3'];
        $arr[2] = $info['轻工']['新钢']['dta3']==0 ? 0 : $info['轻工']['新钢']['dta4']/$info['轻工']['新钢']['dta3'];
        $arr[3] = $info['热卷']['新钢']['dta3']==0 ? 0 : $info['轻工']['新钢']['dta3']/$info['热卷']['新钢']['dta3']*100;
        $arr[4] = $info['轻工']['涟钢']['dta3'];
        $arr[5] = $info['轻工']['涟钢']['dta3']==0 ? 0 : $info['轻工']['涟钢']['dta4']/$info['轻工']['涟钢']['dta3'];
        $arr[6] = $info['热卷']['涟钢']['dta3']==0 ? 0 : $info['轻工']['涟钢']['dta3']/$info['热卷']['涟钢']['dta3']*100;
        $arr[7] = $arr[2]-$arr[5];
        $arr[8] = $arr[3]-$arr[6];
        $arr[9] = $info['热卷']['涟钢']['dta3']==0 ? 0 : $arr[8]*($arr[5]-$info['热卷']['涟钢']['dta4']/$info['热卷']['涟钢']['dta3'])/100;
        $arr[10] = $arr[3]*$arr[7]/100;
        $arr[11] = $info['轻工']['轻工']['dta3'];
        $arr[12] = $info['轻工']['轻工']['dta3']==0 ? 0 : $info['轻工']['轻工']['dta4']/$info['轻工']['轻工']['dta3'];

        $sum9 += $arr[9];
        $sum10 += $arr[10];
        $sum11 += $arr[11];
        $sum12 += $arr[11]*$arr[12];

        $arr[2] = round($arr[2],2);
        $arr[3] = round($arr[3],1)."%";
        $arr[5] = round($arr[5],2);
        $arr[6] = round($arr[6],1)."%";
        $arr[7] = round($arr[7]);
        $arr[8] = round($arr[8],2)."%";
        $arr[9] = round($arr[9]);
        $arr[10] = round($arr[10]);
        $arr[12] = round($arr[12]);
        $data[] = $arr;


        $arr = array();
        $arr[0] = "焊管用钢卷";
        $arr[1] = $info['焊管']['新钢']['dta3'];
        $arr[2] = $info['焊管']['新钢']['dta3']==0 ? 0 : $info['焊管']['新钢']['dta4']/$info['焊管']['新钢']['dta3'];
        $arr[3] = $info['热卷']['新钢']['dta3']==0 ? 0 : $info['焊管']['新钢']['dta3']/$info['热卷']['新钢']['dta3']*100;
        $arr[4] = $info['焊管']['涟钢']['dta3'];
        $arr[5] = $info['焊管']['涟钢']['dta3']==0 ? 0 : $info['焊管']['涟钢']['dta4']/$info['焊管']['涟钢']['dta3'];
        $arr[6] = $info['热卷']['涟钢']['dta3']==0 ? 0 : $info['焊管']['涟钢']['dta3']/$info['热卷']['涟钢']['dta3']*100;
        $arr[7] = $arr[2]-$arr[5];
        $arr[8] = $arr[3]-$arr[6];
        $arr[9] = $info['热卷']['涟钢']['dta3']==0 ? 0 : $arr[8]*($arr[5]-$info['热卷']['涟钢']['dta4']/$info['热卷']['涟钢']['dta3'])/100;
        $arr[10] = $arr[3]*$arr[7]/100;
        $arr[11] = $info['焊管']['焊管']['dta3'];
        $arr[12] = $info['焊管']['焊管']['dta3']==0 ? 0 : $info['焊管']['焊管']['dta4']/$info['焊管']['焊管']['dta3'];

        $sum9 += $arr[9];
        $sum10 += $arr[10];
        $sum11 += $arr[11];
        $sum12 += $arr[11]*$arr[12];

        $arr[2] = round($arr[2],2);
        $arr[3] = round($arr[3],1)."%";
        $arr[5] = round($arr[5],2);
        $arr[6] = round($arr[6],1)."%";
        $arr[7] = round($arr[7]);
        $arr[8] = round($arr[8],2)."%";
        $arr[9] = round($arr[9]);
        $arr[10] = round($arr[10]);
        $arr[12] = round($arr[12]);
        $data[] = $arr;


        $arr = array();
        $arr[0] = "管线用钢卷";
        $arr[1] = $info['管线']['新钢']['dta3'];
        $arr[2] = $info['管线']['新钢']['dta3']==0 ? 0 : $info['管线']['新钢']['dta4']/$info['管线']['新钢']['dta3'];
        $arr[3] = $info['热卷']['新钢']['dta3']==0 ? 0 : $info['管线']['新钢']['dta3']/$info['热卷']['新钢']['dta3']*100;
        $arr[4] = $info['管线']['涟钢']['dta3'];
        $arr[5] = $info['管线']['涟钢']['dta3']==0 ? 0 : $info['管线']['涟钢']['dta4']/$info['管线']['涟钢']['dta3'];
        $arr[6] = $info['热卷']['涟钢']['dta3']==0 ? 0 : $info['管线']['涟钢']['dta3']/$info['热卷']['涟钢']['dta3']*100;
        $arr[7] = $arr[2]-$arr[5];
        $arr[8] = $arr[3]-$arr[6];
        $arr[9] = $info['热卷']['涟钢']['dta3']==0 ? 0 : $arr[8]*($arr[5]-$info['热卷']['涟钢']['dta4']/$info['热卷']['涟钢']['dta3'])/100;
        $arr[10] = $arr[3]*$arr[7]/100;
        $arr[11] = $info['管线']['管线']['dta3'];
        $arr[12] = $info['管线']['管线']['dta3']==0 ? 0 : $info['管线']['管线']['dta4']/$info['管线']['管线']['dta3'];

        $sum9 += $arr[9];
        $sum10 += $arr[10];
        $sum11 += $arr[11];
        $sum12 += $arr[11]*$arr[12];

        $arr[2] = round($arr[2],2);
        $arr[3] = round($arr[3],1)."%";
        $arr[5] = round($arr[5],2);
        $arr[6] = round($arr[6],1)."%";
        $arr[7] = round($arr[7]);
        $arr[8] = round($arr[8],2)."%";
        $arr[9] = round($arr[9]);
        $arr[10] = round($arr[10]);
        $arr[12] = round($arr[12]);
        $data[] = $arr;

        $arr = array();
        $arr[0] = "其他用途钢卷";
        $arr[1] = $info['其他']['新钢']['dta3'];
        $arr[2] = $info['其他']['新钢']['dta3']==0 ? 0 : $info['其他']['新钢']['dta4']/$info['其他']['新钢']['dta3'];
        $arr[3] = $info['热卷']['新钢']['dta3']==0 ? 0 : $info['其他']['新钢']['dta3']/$info['热卷']['新钢']['dta3']*100;
        $arr[4] = $info['其他']['涟钢']['dta3'];
        $arr[5] = $info['其他']['涟钢']['dta3']==0 ? 0 : $info['其他']['涟钢']['dta4']/$info['其他']['涟钢']['dta3'];
        $arr[6] = $info['热卷']['涟钢']['dta3']==0 ? 0 : $info['其他']['涟钢']['dta3']/$info['热卷']['涟钢']['dta3']*100;
        $arr[7] = $arr[2]-$arr[5];
        $arr[8] = $arr[3]-$arr[6];
        $arr[9] = $info['热卷']['涟钢']['dta3']==0 ? 0 : $arr[8]*($arr[5]-$info['热卷']['涟钢']['dta4']/$info['热卷']['涟钢']['dta3'])/100;
        $arr[10] = $arr[3]*$arr[7]/100;
        $arr[11] = $info['其他']['其他']['dta3'];
        $arr[12] = $info['其他']['其他']['dta3']==0 ? 0 : $info['其他']['其他']['dta4']/$info['其他']['其他']['dta3'];

        $sum9 += $arr[9];
        $sum10 += $arr[10];
        $sum11 += $arr[11];
        $sum12 += $arr[11]*$arr[12];

        $arr[2] = round($arr[2],2);
        $arr[3] = round($arr[3],1)."%";
        $arr[5] = round($arr[5],2);
        $arr[6] = round($arr[6],1)."%";
        $arr[7] = round($arr[7]);
        $arr[8] = round($arr[8],2)."%";
        $arr[9] = round($arr[9]);
        $arr[10] = round($arr[10]);
        $arr[12] = round($arr[12]);
        $data[] = $arr;

        $arr = array();
        $arr[0] = "热卷合计";
        $arr[1] = $info['热卷']['新钢']['dta3'];
        $arr[2] = $info['热卷']['新钢']['dta3']==0 ? 0 : $info['热卷']['新钢']['dta4']/$info['热卷']['新钢']['dta3'];
        $arr[3] = "1.00";
        $arr[4] = $info['热卷']['涟钢']['dta3'];
        $arr[5] = $info['热卷']['涟钢']['dta3']==0 ? 0 : $info['热卷']['涟钢']['dta4']/$info['热卷']['涟钢']['dta3'];
        $arr[6] = "1.00";
        $arr[7] = round($arr[2]-$arr[5],2);
        $arr[8] = "0.00";
        $arr[9] = $sum9;
        $arr[10] = $sum10;
        $arr[11] = $sum11;
        $arr[12] = $sum11==0 ? 0 : round($sum12/$sum11);

        $arr[2] = round($arr[2]);
        $arr[5] = round($arr[5]);
        $arr[9] = round($arr[9],2);
        $arr[10] = round($arr[10],2);
        $data[] = $arr;



        $this->assign("data", $data);
        $this->assign("date_month", $date_month);
        $this->assign("params", $params);
    }

    public function huizong_gcduibiao_zhb($params){
        $params['date'] = $params['date']=="" ? date("Y-m",strtotime("-1 month")) : $params['date'];
        $params['mode'] = $params['mode']=="" ? 1 : $params['mode'];
        $date = date("Y-m-01",strtotime($params['date']."-01"));
        $params['bigtype'] = $params['bigtype']=="" ? 1 : $params['bigtype'];
        $bigtype = $params['bigtype'];
        $datatype = $params['datatype'];

        if($bigtype==1){
            $date_month = date("Y年n月",strtotime($params['date']."-01"));
        }else{
            $date_month = date("Y年1-n月",strtotime($params['date']."-01"));
        }

        $variety_arr = array(
            "按用途分：建筑用特厚板"=>"中厚板",
            "按用途分：建筑用厚板"=>"中厚板",
            "按用途分：建筑用中板"=>"中厚板",
            "其他用途特厚板"=>"中厚板",
            "其他用途厚板"=>"中厚板",
            "其他用途中板"=>"中厚板",

            "工程机械用特厚板"=>"中厚板",
            "工程机械用厚板"=>"中厚板",
            "工程机械用中板"=>"中厚板",

            "造船用特厚板"=>"中厚板",
            "造船用厚板"=>"中厚板",
            "造船用中板"=>"中厚板",

            "锅炉和压力容器用特厚板"=>"中厚板",
            "锅炉和压力容器用厚板"=>"中厚板",
            "锅炉和压力用中板"=>"中厚板",

            "桥梁用特厚板"=>"中厚板",
            "桥梁用厚板"=>"中厚板",
            "桥梁用中板"=>"中厚板",

            "管线用特厚板"=>"中厚板",
            "管线用厚板"=>"中厚板",
            "管线用中板"=>"中厚板",

            "不锈钢特厚板"=>"中厚板",
            "不锈钢厚板"=>"中厚板",
            "不锈钢中板"=>"中厚板"
        );

        $variety_arr2 = array(
            "按用途分：建筑用特厚板"=>"建筑",
            "按用途分：建筑用厚板"=>"建筑",
            "按用途分：建筑用中板"=>"建筑",

            "其他用途特厚板"=>"其他",
            "其他用途厚板"=>"其他",
            "其他用途中板"=>"其他",

            "工程机械用特厚板"=>"工程",
            "工程机械用厚板"=>"工程",
            "工程机械用中板"=>"工程",

            "造船用特厚板"=>"造船",
            "造船用厚板"=>"造船",
            "造船用中板"=>"造船",

            "锅炉和压力容器用特厚板"=>"锅炉",
            "锅炉和压力容器用厚板"=>"锅炉",
            "锅炉和压力用中板"=>"锅炉",

            "桥梁用特厚板"=>"桥梁",
            "桥梁用厚板"=>"桥梁",
            "桥梁用中板"=>"桥梁",

            "管线用特厚板"=>"管线",
            "管线用厚板"=>"管线",
            "管线用中板"=>"管线",

            "不锈钢特厚板"=>"不锈",
            "不锈钢厚板"=>"不锈",
            "不锈钢中板"=>"不锈"
        );

        $com_arr = array("新余钢铁有限责任公司"=>"新钢","湘潭钢铁集团有限公司"=>"湘钢","涟源钢铁集团有限公司"=>"涟钢","湖南华菱涟源钢铁有限公司"=>"涟钢","方大特钢科技股份有限公司"=>"方大","攀钢集团有限公司"=>"攀钢");

        //详细数据
        $this->xg->execute("set names 'utf8';");
        $sql = "select id from data_table_base where type=4 and isdel=0 and uptype='".$bigtype."' and sdate='".$date."' limit 1 ";
        $baseid = $this->xg->getOne($sql);

        $sql = "select dta1,dta2,dta3,dta4,dta5,dta6,dta7,dta8 from data_table where baseid='".$baseid."' ";
        $list = $this->xg->query($sql);
        //print_r($list);
        $info = array();
        foreach ($list as $key=>$tmp){

            $dta1 = trim($tmp['dta1']);
            $dta1 = mb_ereg_replace('^(　| )+','', $dta1);
            if(isset($variety_arr[$dta1])){

                $dta2 = trim($tmp['dta2']);
                $dta2 = mb_ereg_replace('^(　| )+','', $dta2);
                if($dta1==$dta2){
                    //行业销售量，行业价格
                    $dta1_ = $variety_arr2[$dta1];
                    if($datatype=="3") {
                        $info[$dta1_][$dta1_]['dta3'] += (float)$tmp['dta7'];
                        $info[$dta1_][$dta1_]['dta4'] += (float)$tmp['dta7'] * (float)$tmp['dta8'];
                    }else if($datatype=="2"){
                        $info[$dta1_][$dta1_]['dta3'] += (float)$tmp['dta5'];
                        $info[$dta1_][$dta1_]['dta4'] += (float)$tmp['dta5'] * (float)$tmp['dta6'];
                    }else {
                        $info[$dta1_][$dta1_]['dta3'] += (float)$tmp['dta3'];
                        $info[$dta1_][$dta1_]['dta4'] += (float)$tmp['dta3'] * (float)$tmp['dta4'];
                    }
                }
                if(isset($com_arr[$dta2])){

                    $dta2 = $com_arr[$dta2];
                    $dta1_ = $variety_arr2[$dta1];
                    $dta1 = $variety_arr[$dta1];

                    if($datatype=="3"){
                        $info[$dta1_][$dta2]['dta3'] += (float)$tmp['dta7'];
                        $info[$dta1_][$dta2]['dta4'] += (float)$tmp['dta8']*(float)$tmp['dta7'];

                        //$info[$dta1_][$dta2]['3'] += (float)$tmp['dta3'];
                        //$info[$dta1_][$dta2]['4'] += (float)$tmp['dta4']*(float)$tmp['dta3'];

                        $info[$dta1][$dta2]['dta3'] += (float)$tmp['dta7'];
                        $info[$dta1][$dta2]['dta4'] += (float)$tmp['dta8']*(float)$tmp['dta7'];
                    }else if($datatype=="2"){
                        $info[$dta1_][$dta2]['dta3'] += (float)$tmp['dta5'];
                        $info[$dta1_][$dta2]['dta4'] += (float)$tmp['dta6']*(float)$tmp['dta5'];

                        //$info[$dta1_][$dta2]['3'] += (float)$tmp['dta3'];
                        //$info[$dta1_][$dta2]['4'] += (float)$tmp['dta4']*(float)$tmp['dta3'];

                        $info[$dta1][$dta2]['dta3'] += (float)$tmp['dta5'];
                        $info[$dta1][$dta2]['dta4'] += (float)$tmp['dta6']*(float)$tmp['dta5'];
                    }else {
                        $info[$dta1_][$dta2]['dta3'] += (float)$tmp['dta3'];
                        $info[$dta1_][$dta2]['dta4'] += (float)$tmp['dta4']*(float)$tmp['dta3'];

                        //$info[$dta1_][$dta2]['3'] += (float)$tmp['dta3'];
                        //$info[$dta1_][$dta2]['4'] += (float)$tmp['dta4']*(float)$tmp['dta3'];

                        $info[$dta1][$dta2]['dta3'] += (float)$tmp['dta3'];
                        $info[$dta1][$dta2]['dta4'] += (float)$tmp['dta4']*(float)$tmp['dta3'];
                    }

                }
            }
        }
        $info['中厚板']['新钢']['dta3'] -= $info['不锈']['新钢']['dta3'];
        $info['中厚板']['新钢']['dta4'] -= $info['不锈']['新钢']['dta4'];
        $info['中厚板']['湘钢']['dta3'] -= $info['不锈']['湘钢']['dta3'];
        $info['中厚板']['湘钢']['dta4'] -= $info['不锈']['湘钢']['dta4'];

        $info['其他']['其他']['dta3'] -= $info['不锈']['不锈']['dta3'];
        $info['其他']['其他']['dta4'] -= $info['不锈']['不锈']['dta4'];

        $info['建筑']['建筑']['dta3'] -= $info['建筑']['攀钢']['dta3'];
        $info['建筑']['建筑']['dta4'] -= $info['建筑']['攀钢']['dta4'];

        $info['工程']['工程']['dta3'] -= $info['工程']['攀钢']['dta3'];
        $info['工程']['工程']['dta4'] -= $info['工程']['攀钢']['dta4'];

        $info['造船']['造船']['dta3'] -= $info['造船']['攀钢']['dta3'];
        $info['造船']['造船']['dta4'] -= $info['造船']['攀钢']['dta4'];

        $info['锅炉']['锅炉']['dta3'] -= $info['锅炉']['攀钢']['dta3'];
        $info['锅炉']['锅炉']['dta4'] -= $info['锅炉']['攀钢']['dta4'];

        $info['桥梁']['桥梁']['dta3'] -= $info['桥梁']['攀钢']['dta3'];
        $info['桥梁']['桥梁']['dta4'] -= $info['桥梁']['攀钢']['dta4'];

        $info['管线']['管线']['dta3'] -= $info['管线']['攀钢']['dta3'];
        $info['管线']['管线']['dta4'] -= $info['管线']['攀钢']['dta4'];

        $info['其他']['其他']['dta3'] -= $info['其他']['攀钢']['dta3'];
        $info['其他']['其他']['dta4'] -= $info['其他']['攀钢']['dta4'];
        //echo "<pre>";
        //print_r($info['其他']);
        $sum9 = 0;
        $sum10 = 0;
        $sum11 = 0;
        $sum12 = 0;

        //dta3 销量 、dta4 总价格
        $data = array();
        $arr = array();
        $arr[0] = "建筑用中板";
        $arr[1] = $info['建筑']['新钢']['dta3'];
        $arr[2] = $info['建筑']['新钢']['dta3']==0 ? 0 : $info['建筑']['新钢']['dta4']/$info['建筑']['新钢']['dta3'];
        $arr[3] = $info['中厚板']['新钢']['dta3']==0 ? 0 : $info['建筑']['新钢']['dta3']/$info['中厚板']['新钢']['dta3']*100;
        $arr[4] = $info['建筑']['湘钢']['dta3'];
        $arr[5] = $info['建筑']['湘钢']['dta3']==0 ? 0 : $info['建筑']['湘钢']['dta4']/$info['建筑']['湘钢']['dta3'];
        $arr[6] = $info['中厚板']['湘钢']['dta3']==0 ? 0 : $info['建筑']['湘钢']['dta3']/$info['中厚板']['湘钢']['dta3']*100;
        $arr[7] = $arr[2]-$arr[5];
        $arr[8] = $arr[3]-$arr[6];
        $arr[9] = $info['中厚板']['湘钢']['dta3']==0 ? 0 : $arr[8]*($arr[5]-$info['中厚板']['湘钢']['dta4']/$info['中厚板']['湘钢']['dta3'])/100;
        $arr[10] = $arr[3]*$arr[7]/100;
        $arr[11] = $info['建筑']['建筑']['dta3'];
        $arr[12] = $info['建筑']['建筑']['dta3']==0 ? 0 : $info['建筑']['建筑']['dta4']/$info['建筑']['建筑']['dta3'];

        $sum9 += $arr[9];
        $sum10 += $arr[10];
        $sum11 += $arr[11];
        $sum12 += $arr[11]*$arr[12];

        $arr[2] = round($arr[2]);
        $arr[3] = round($arr[3],1)."%";
        $arr[5] = round($arr[5]);
        $arr[6] = round($arr[6],1)."%";
        $arr[7] = round($arr[7]);
        $arr[8] = round($arr[8],2)."%";
        $arr[9] = round($arr[9]);
        $arr[10] = round($arr[10]);
        $arr[12] = round($arr[12]);
        $data[] = $arr;


        $arr = array();
        $arr[0] = "工程机械用中板";
        $arr[1] = $info['工程']['新钢']['dta3'];
        $arr[2] = $info['工程']['新钢']['dta3']==0 ? 0 : $info['工程']['新钢']['dta4']/$info['工程']['新钢']['dta3'];
        $arr[3] = $info['中厚板']['新钢']['dta3']==0 ? 0 : $info['工程']['新钢']['dta3']/$info['中厚板']['新钢']['dta3']*100;
        $arr[4] = $info['工程']['湘钢']['dta3'];
        $arr[5] = $info['工程']['湘钢']['dta3']==0 ? 0 : $info['工程']['湘钢']['dta4']/$info['工程']['湘钢']['dta3'];
        $arr[6] = $info['中厚板']['湘钢']['dta3']==0 ? 0 : $info['工程']['湘钢']['dta3']/$info['中厚板']['湘钢']['dta3']*100;
        $arr[7] = $arr[2]-$arr[5];
        $arr[8] = $arr[3]-$arr[6];
        $arr[9] = $info['中厚板']['湘钢']['dta3']==0 ? 0 : $arr[8]*($arr[5]-$info['中厚板']['湘钢']['dta4']/$info['中厚板']['湘钢']['dta3'])/100;
        $arr[10] = $arr[3]*$arr[7]/100;
        $arr[11] = $info['工程']['工程']['dta3'];
        $arr[12] = $info['工程']['工程']['dta3']==0 ? 0 : $info['工程']['工程']['dta4']/$info['工程']['工程']['dta3'];

        $sum9 += $arr[9];
        $sum10 += $arr[10];
        $sum11 += $arr[11];
        $sum12 += $arr[11]*$arr[12];

        $arr[2] = round($arr[2]);
        $arr[3] = round($arr[3],1)."%";
        $arr[5] = round($arr[5]);
        $arr[6] = round($arr[6],1)."%";
        $arr[7] = round($arr[7]);
        $arr[8] = round($arr[8],2)."%";
        $arr[9] = round($arr[9]);
        $arr[10] = round($arr[10]);
        $arr[12] = round($arr[12]);
        $data[] = $arr;


        $arr = array();
        $arr[0] = "造船用中板";
        $arr[1] = $info['造船']['新钢']['dta3'];
        $arr[2] = $info['造船']['新钢']['dta3']==0 ? 0 : $info['造船']['新钢']['dta4']/$info['造船']['新钢']['dta3'];
        $arr[3] = $info['中厚板']['新钢']['dta3']==0 ? 0 : $info['造船']['新钢']['dta3']/$info['中厚板']['新钢']['dta3']*100;
        $arr[4] = $info['造船']['湘钢']['dta3'];
        $arr[5] = $info['造船']['湘钢']['dta3']==0 ? 0 : $info['造船']['湘钢']['dta4']/$info['造船']['湘钢']['dta3'];
        $arr[6] = $info['中厚板']['湘钢']['dta3']==0 ? 0 : $info['造船']['湘钢']['dta3']/$info['中厚板']['湘钢']['dta3']*100;
        $arr[7] = $arr[2]-$arr[5];
        $arr[8] = $arr[3]-$arr[6];
        $arr[9] = $info['中厚板']['湘钢']['dta3']==0 ? 0 : $arr[8]*($arr[5]-$info['中厚板']['湘钢']['dta4']/$info['中厚板']['湘钢']['dta3'])/100;
        $arr[10] = $arr[3]*$arr[7]/100;
        $arr[11] = $info['造船']['造船']['dta3'];
        $arr[12] = $info['造船']['造船']['dta3']==0 ? 0 : $info['造船']['造船']['dta4']/$info['造船']['造船']['dta3'];

        $sum9 += $arr[9];
        $sum10 += $arr[10];
        $sum11 += $arr[11];
        $sum12 += $arr[11]*$arr[12];

        $arr[2] = round($arr[2]);
        $arr[3] = round($arr[3],1)."%";
        $arr[5] = round($arr[5]);
        $arr[6] = round($arr[6],1)."%";
        $arr[7] = round($arr[7]);
        $arr[8] = round($arr[8],2)."%";
        $arr[9] = round($arr[9]);
        $arr[10] = round($arr[10]);
        $arr[12] = round($arr[12]);
        $data[] = $arr;


        $arr = array();
        $arr[0] = "锅炉和压力用中板";
        $arr[1] = $info['锅炉']['新钢']['dta3'];
        $arr[2] = $info['锅炉']['新钢']['dta3']==0 ? 0 : $info['锅炉']['新钢']['dta4']/$info['锅炉']['新钢']['dta3'];
        $arr[3] = $info['中厚板']['新钢']['dta3']==0 ? 0 : $info['锅炉']['新钢']['dta3']/$info['中厚板']['新钢']['dta3']*100;
        $arr[4] = $info['锅炉']['湘钢']['dta3'];
        $arr[5] = $info['锅炉']['湘钢']['dta3']==0 ? 0 : $info['锅炉']['湘钢']['dta4']/$info['锅炉']['湘钢']['dta3'];
        $arr[6] = $info['中厚板']['湘钢']['dta3']==0 ? 0 : $info['锅炉']['湘钢']['dta3']/$info['中厚板']['湘钢']['dta3']*100;
        $arr[7] = $arr[2]-$arr[5];
        $arr[8] = $arr[3]-$arr[6];
        $arr[9] = $info['中厚板']['湘钢']['dta3']==0 ? 0 : $arr[8]*($arr[5]-$info['中厚板']['湘钢']['dta4']/$info['中厚板']['湘钢']['dta3'])/100;
        $arr[10] = $arr[3]*$arr[7]/100;
        $arr[11] = $info['锅炉']['锅炉']['dta3'];
        $arr[12] = $info['锅炉']['锅炉']['dta3']==0 ? 0 : $info['锅炉']['锅炉']['dta4']/$info['锅炉']['锅炉']['dta3'];

        $sum9 += $arr[9];
        $sum10 += $arr[10];
        $sum11 += $arr[11];
        $sum12 += $arr[11]*$arr[12];

        $arr[2] = round($arr[2]);
        $arr[3] = round($arr[3],1)."%";
        $arr[5] = round($arr[5]);
        $arr[6] = round($arr[6],1)."%";
        $arr[7] = round($arr[7]);
        $arr[8] = round($arr[8],2)."%";
        $arr[9] = round($arr[9]);
        $arr[10] = round($arr[10]);
        $arr[12] = round($arr[12]);
        $data[] = $arr;


        $arr = array();
        $arr[0] = "桥梁用中板";
        $arr[1] = $info['桥梁']['新钢']['dta3'];
        $arr[2] = $info['桥梁']['新钢']['dta3']==0 ? 0 : $info['桥梁']['新钢']['dta4']/$info['桥梁']['新钢']['dta3'];
        $arr[3] = $info['中厚板']['湘钢']['dta3']==0 ? 0 : $info['桥梁']['新钢']['dta3']/$info['中厚板']['新钢']['dta3']*100;
        $arr[4] = $info['桥梁']['湘钢']['dta3'];
        $arr[5] = $info['桥梁']['湘钢']['dta3']==0 ? 0 : $info['桥梁']['湘钢']['dta4']/$info['桥梁']['湘钢']['dta3'];
        $arr[6] = $info['中厚板']['湘钢']['dta3']==0 ? 0 : $info['桥梁']['湘钢']['dta3']/$info['中厚板']['湘钢']['dta3']*100;
        $arr[7] = $arr[2]-$arr[5];
        $arr[8] = $arr[3]-$arr[6];
        $arr[9] = $info['中厚板']['湘钢']['dta3']==0 ? 0 : $arr[8]*($arr[5]-$info['中厚板']['湘钢']['dta4']/$info['中厚板']['湘钢']['dta3'])/100;
        $arr[10] = $arr[3]*$arr[7]/100;
        $arr[11] = $info['桥梁']['桥梁']['dta3'];
        $arr[12] = $info['桥梁']['桥梁']['dta3']==0 ? 0 : $info['桥梁']['桥梁']['dta4']/$info['桥梁']['桥梁']['dta3'];

        $sum9 += $arr[9];
        $sum10 += $arr[10];
        $sum11 += $arr[11];
        $sum12 += $arr[11]*$arr[12];

        $arr[2] = round($arr[2]);
        $arr[3] = round($arr[3],1)."%";
        $arr[5] = round($arr[5]);
        $arr[6] = round($arr[6],1)."%";
        $arr[7] = round($arr[7]);
        $arr[8] = round($arr[8],2)."%";
        $arr[9] = round($arr[9]);
        $arr[10] = round($arr[10]);
        $arr[12] = round($arr[12]);
        $data[] = $arr;


        $arr = array();
        $arr[0] = "管线用中板";
        $arr[1] = $info['管线']['新钢']['dta3'];
        $arr[2] = $info['管线']['新钢']['dta3']==0 ? 0 : $info['管线']['新钢']['dta4']/$info['管线']['新钢']['dta3'];
        $arr[3] = $info['中厚板']['新钢']['dta3']==0 ? 0 : $info['管线']['新钢']['dta3']/$info['中厚板']['新钢']['dta3']*100;
        $arr[4] = $info['管线']['湘钢']['dta3'];
        $arr[5] = $info['管线']['湘钢']['dta3']==0 ? 0 : $info['管线']['湘钢']['dta4']/$info['管线']['湘钢']['dta3'];
        $arr[6] = $info['中厚板']['湘钢']['dta3']==0 ? 0 : $info['管线']['湘钢']['dta3']/$info['中厚板']['湘钢']['dta3']*100;
        $arr[7] = $arr[2]-$arr[5];
        $arr[8] = $arr[3]-$arr[6];
        $arr[9] = $info['中厚板']['湘钢']['dta3']==0 ? 0 : $arr[8]*($arr[5]-$info['中厚板']['湘钢']['dta4']/$info['中厚板']['湘钢']['dta3'])/100;
        $arr[10] = $arr[3]*$arr[7]/100;
        $arr[11] = $info['管线']['管线']['dta3'];
        $arr[12] = $info['管线']['管线']['dta3']==0 ? 0 : $info['管线']['管线']['dta4']/$info['管线']['管线']['dta3'];

        $sum9 += $arr[9];
        $sum10 += $arr[10];
        $sum11 += $arr[11];
        $sum12 += $arr[11]*$arr[12];

        $arr[2] = round($arr[2]);
        $arr[3] = round($arr[3],1)."%";
        $arr[5] = round($arr[5]);
        $arr[6] = round($arr[6],1)."%";
        $arr[7] = round($arr[7]);
        $arr[8] = round($arr[8],2)."%";
        $arr[9] = round($arr[9]);
        $arr[10] = round($arr[10]);
        $arr[12] = round($arr[12]);
        $data[] = $arr;

        $arr = array();
        $arr[0] = "其他用途中板";
        $arr[1] = $info['其他']['新钢']['dta3'];
        $arr[2] = $info['其他']['新钢']['dta3']==0 ? 0 : $info['其他']['新钢']['dta4']/$info['其他']['新钢']['dta3'];
        $arr[3] = $info['中厚板']['新钢']['dta3']==0 ? 0 : $info['其他']['新钢']['dta3']/$info['中厚板']['新钢']['dta3']*100;
        $arr[4] = $info['其他']['湘钢']['dta3'];
        $arr[5] = $info['其他']['湘钢']['dta3']==0 ? 0 : $info['其他']['湘钢']['dta4']/$info['其他']['湘钢']['dta3'];
        $arr[6] = $info['中厚板']['湘钢']['dta3']==0 ? 0 : $info['其他']['湘钢']['dta3']/$info['中厚板']['湘钢']['dta3']*100;
        $arr[7] = $arr[2]-$arr[5];
        $arr[8] = $arr[3]-$arr[6];
        $arr[9] = $info['中厚板']['湘钢']['dta3']==0 ? 0 : $arr[8]*($arr[5]-$info['中厚板']['湘钢']['dta4']/$info['中厚板']['湘钢']['dta3'])/100;
        $arr[10] = $arr[3]*$arr[7]/100;
        $arr[11] = $info['其他']['其他']['dta3'];
        $arr[12] = $info['其他']['其他']['dta3']==0 ? 0 : $info['其他']['其他']['dta4']/$info['其他']['其他']['dta3'];

        $sum9 += $arr[9];
        $sum10 += $arr[10];
        $sum11 += $arr[11];
        $sum12 += $arr[11]*$arr[12];

        $arr[2] = round($arr[2]);
        $arr[3] = round($arr[3],1)."%";
        $arr[5] = round($arr[5]);
        $arr[6] = round($arr[6],1)."%";
        $arr[7] = round($arr[7]);
        $arr[8] = round($arr[8],2)."%";
        $arr[9] = round($arr[9]);
        $arr[10] = round($arr[10]);
        $arr[12] = round($arr[12]);
        $data[] = $arr;


        $arr = array();
        $arr[0] = "中厚板合计";
        $arr[1] = $info['中厚板']['新钢']['dta3'];
        $arr[2] = $info['中厚板']['新钢']['dta3']==0 ? 0 : $info['中厚板']['新钢']['dta4']/$info['中厚板']['新钢']['dta3'];
        $arr[3] = "1.00";
        $arr[4] = $info['中厚板']['湘钢']['dta3'];
        $arr[5] = $info['中厚板']['湘钢']['dta3']==0 ? 0 : $info['中厚板']['湘钢']['dta4']/$info['中厚板']['湘钢']['dta3'];
        $arr[6] = "1.00";
        $arr[7] = round($arr[2]-$arr[5],2);
        $arr[8] = "0.00";
        $arr[9] = $sum9;
        $arr[10] = $sum10;
        $arr[11] = $sum11;
        $arr[12] = $sum11==0 ? 0 : round($sum12/$sum11);

        $arr[2] = round($arr[2]);
        $arr[5] = round($arr[5]);
        $arr[9] = round($arr[9],2);
        $arr[10] = round($arr[10],2);
        $data[] = $arr;


        $this->assign("data", $data);
        $this->assign("date_month", $date_month);
        $this->assign("params", $params);
    }

    public function huizong_hyduibiao($params){

        $params['date'] = $params['date']=="" ? date("Y-m",strtotime("-1 month")) : $params['date'];
        $params['mode'] = $params['mode']=="" ? 1 : $params['mode'];
        $params['bigtype'] = $params['bigtype']=="" ? 1 : $params['bigtype'];
        $bigtype = $params['bigtype'];

        if($bigtype==1){
            $date = date("Y-m-01",strtotime($params['date']."-01"));
        }else{
            $date = date("Y-m-01",strtotime($params['date']."-01"));
        }

        $variety_arr = array("特厚板","厚板","中板","不锈钢特厚板","不锈钢厚板","不锈钢中板",
            "中厚宽钢带","热轧薄宽钢带",
            "冷轧薄宽钢带","不锈冷轧薄宽钢带",
            "线材(盘条)","钢筋");
        $com_arr = array(
            "新余钢铁有限责任公司"=>"新钢",
            "湘潭钢铁集团有限公司"=>"湘钢",
            "涟源钢铁集团有限公司"=>"涟钢",
            "湖南华菱涟源钢铁有限公司"=>"涟钢",
            "方大特钢科技股份有限公司"=>"方大",
            "其中：太原钢铁(集团)有限公司"=>"太原钢铁",
            "攀钢集团有限公司"=>"攀钢"
        );



        //详细数据
        $this->xg->execute("set names 'utf8';");
        $sql = "select id from data_table_base where type=4 and isdel=0 and uptype='".$bigtype."' and sdate='".$date."' limit 1 ";
        $baseid = $this->xg->getOne($sql);

        $sql = "select dta1,dta2,dta3,dta4,dta5,dta6,dta7,dta8 from data_table where baseid='".$baseid."' ";
        $list = $this->xg->query($sql);
        //print_r($list);
        $info = array();
        foreach ($list as $key=>$tmp){
            $tmp['dta3'] = (float)$tmp['dta3'];
            $tmp['dta4'] = (float)$tmp['dta4'];
            $tmp['dta5'] = (float)$tmp['dta5'];
            $tmp['dta6'] = (float)$tmp['dta6'];
            $tmp['dta7'] = (float)$tmp['dta7'];
            $tmp['dta8'] = (float)$tmp['dta8'];
            $dta1 = trim($tmp['dta1']);
            $dta1 = mb_ereg_replace('^(　| )+','', $dta1);
            if(in_array($dta1,$variety_arr)){

                $dta2 = trim($tmp['dta2']);
                $dta2 = mb_ereg_replace('^(　| )+','', $dta2);
                if(isset($com_arr[$dta2])){
                    $comname = $com_arr[$dta2];
                    $info[$dta1][$comname] = $tmp;
                }

                if($dta1==$dta2){
                    $info[$dta1][$dta2] = $tmp;
                }
            }
        }
        //echo "<pre>";
        //print_r($info);

        $data = array();

        $arr = array();
        $arr[0] = "1、中厚板线";
        $arr[1] = $info['特厚板']['特厚板']['dta5'];
        $arr[1] += $info['厚板']['厚板']['dta5'];
        $arr[1] += $info['中板']['中板']['dta5'];
        $arr[1] -= $info['不锈钢特厚板']['不锈钢特厚板']['dta5'];
        $arr[1] -= $info['不锈钢厚板']['不锈钢厚板']['dta5'];
        $arr[1] -= $info['不锈钢中板']['不锈钢中板']['dta5'];
        $arr[1] -= $info['中板']['攀钢']['dta5'];

        $arr[2] = $info['特厚板']['特厚板']['dta5']*$info['特厚板']['特厚板']['dta6'];
        $arr[2] += $info['厚板']['厚板']['dta5']*$info['厚板']['厚板']['dta6'];
        $arr[2] += $info['中板']['中板']['dta5']*$info['中板']['中板']['dta6'];
        $arr[2] -= $info['不锈钢特厚板']['不锈钢特厚板']['dta5']*$info['不锈钢特厚板']['不锈钢特厚板']['dta6'];
        $arr[2] -= $info['不锈钢厚板']['不锈钢厚板']['dta5']*$info['不锈钢厚板']['不锈钢厚板']['dta6'];
        $arr[2] -= $info['不锈钢中板']['不锈钢中板']['dta5']*$info['不锈钢中板']['不锈钢中板']['dta6'];
        $arr[2] -= $info['中板']['攀钢']['dta5']*$info['中板']['攀钢']['dta6'];
        $arr[2] = $arr[1]==0 ? 0 : round($arr[2]/$arr[1]);

        $arr[3] = $info['特厚板']['特厚板']['dta7'];
        $arr[3] += $info['厚板']['厚板']['dta7'];
        $arr[3] += $info['中板']['中板']['dta7'];
        $arr[3] -= $info['不锈钢特厚板']['不锈钢特厚板']['dta7'];
        $arr[3] -= $info['不锈钢厚板']['不锈钢厚板']['dta7'];
        $arr[3] -= $info['不锈钢中板']['不锈钢中板']['dta7'];
        $arr[3] -= $info['中板']['攀钢']['dta7'];

        $arr[4] = $info['特厚板']['特厚板']['dta7']*$info['特厚板']['特厚板']['dta8'];
        $arr[4] += $info['厚板']['厚板']['dta7']*$info['厚板']['厚板']['dta8'];
        $arr[4] += $info['中板']['中板']['dta7']*$info['中板']['中板']['dta8'];
        $arr[4] -= $info['不锈钢特厚板']['不锈钢特厚板']['dta7']*$info['不锈钢特厚板']['不锈钢特厚板']['dta8'];
        $arr[4] -= $info['不锈钢厚板']['不锈钢厚板']['dta7']*$info['不锈钢厚板']['不锈钢厚板']['dta8'];
        $arr[4] -= $info['不锈钢中板']['不锈钢中板']['dta7']*$info['不锈钢中板']['不锈钢中板']['dta8'];
        $arr[4] -= $info['中板']['攀钢']['dta7']*$info['中板']['攀钢']['dta8'];
        $arr[4] = $arr[3]==0 ? 0 : round($arr[4]/$arr[3]);

        $arr[5] = $info['特厚板']['新钢']['dta5'];
        $arr[5] += $info['厚板']['新钢']['dta5'];
        $arr[5] += $info['中板']['新钢']['dta5'];
        $arr[6] = $info['特厚板']['新钢']['dta5']*$info['特厚板']['新钢']['dta6'];
        $arr[6] += $info['厚板']['新钢']['dta5']*$info['厚板']['新钢']['dta6'];
        $arr[6] += $info['中板']['新钢']['dta5']*$info['中板']['新钢']['dta6'];
        $arr[6] = $arr[5]==0 ? 0 : round($arr[6]/$arr[5]);

        $arr[7] = $info['特厚板']['新钢']['dta7'];
        $arr[7] += $info['厚板']['新钢']['dta7'];
        $arr[7] += $info['中板']['新钢']['dta7'];
        $arr[8] = $info['特厚板']['新钢']['dta7']*$info['特厚板']['新钢']['dta8'];
        $arr[8] += $info['厚板']['新钢']['dta7']*$info['厚板']['新钢']['dta8'];
        $arr[8] += $info['中板']['新钢']['dta7']*$info['中板']['新钢']['dta8'];
        $arr[8] = $arr[7]==0 ? 0 : round($arr[8]/$arr[7]);

        $arr[9] = $arr[6]-$arr[2];
        $arr[10] = $arr[8]>0 ? ($arr[8]-$arr[4]) : 0;
        $arr[11] = $arr[1]+$arr[3];
        $arr[12] = $arr[11]==0 ? 0 : round(($arr[1]*$arr[2]+$arr[3]*$arr[4])/$arr[11]);

        $arr[13] = $arr[5]+$arr[7];
        $arr[14] = $arr[13]==0 ? 0 : round(($arr[5]*$arr[6]+$arr[7]*$arr[8])/$arr[13]);

        $arr[15] = $arr[14]-$arr[12];
        $arr[16] = "";
        $data[] = $arr;


        $arr = array();
        $arr[0] = "特厚板";
        $arr[1] = $info['特厚板']['特厚板']['dta5'];
        $arr[2] = $info['特厚板']['特厚板']['dta6'];
        $arr[3] = $info['特厚板']['特厚板']['dta7'];
        $arr[4] = $info['特厚板']['特厚板']['dta8'];
        $arr[5] = $info['特厚板']['新钢']['dta5'];
        $arr[6] = $info['特厚板']['新钢']['dta6'];
        $arr[7] = $info['特厚板']['新钢']['dta7'];
        $arr[8] = $info['特厚板']['新钢']['dta8'];
        $arr[9] = $arr[10] = "";
        $arr[11] = $info['特厚板']['特厚板']['dta3'];
        $arr[12] = $info['特厚板']['特厚板']['dta4'];
        $arr[13] = $arr[14] = $arr[15] = $arr[16] = "";
        $data[] = $arr;

        $arr = array();
        $arr[0] = "厚板";
        $arr[1] = $info['厚板']['厚板']['dta5'];
        $arr[2] = $info['厚板']['厚板']['dta6'];
        $arr[3] = $info['厚板']['厚板']['dta7'];
        $arr[4] = $info['厚板']['厚板']['dta8'];
        $arr[5] = $info['厚板']['新钢']['dta5'];
        $arr[6] = $info['厚板']['新钢']['dta6'];
        $arr[7] = $info['厚板']['新钢']['dta7'];
        $arr[8] = $info['厚板']['新钢']['dta8'];
        $arr[9] = $arr[10] = "";
        $arr[11] = $info['厚板']['厚板']['dta3'];
        $arr[12] = $info['厚板']['厚板']['dta4'];
        $arr[13] = $arr[14] = $arr[15] = $arr[16] = "";
        $data[] = $arr;

        $arr = array();
        $arr[0] = "中板";
        $arr[1] = $info['中板']['中板']['dta5'];
        $arr[2] = $info['中板']['中板']['dta6'];
        $arr[3] = $info['中板']['中板']['dta7'];
        $arr[4] = $info['中板']['中板']['dta8'];
        $arr[5] = $info['中板']['新钢']['dta5'];
        $arr[6] = $info['中板']['新钢']['dta6'];
        $arr[7] = $info['中板']['新钢']['dta7'];
        $arr[8] = $info['中板']['新钢']['dta8'];
        $arr[9] = $arr[10] = "";
        $arr[11] = $info['中板']['中板']['dta3'];
        $arr[12] = $info['中板']['中板']['dta4'];
        $arr[13] = $arr[14] = $arr[15] = $arr[16] = "";
        $data[] = $arr;

        $arr = array();
        $arr[0] = "不锈钢特厚板";
        $arr[1] = $info['不锈钢特厚板']['不锈钢特厚板']['dta5'];
        $arr[2] = $info['不锈钢特厚板']['不锈钢特厚板']['dta6'];
        $arr[3] = $info['不锈钢特厚板']['不锈钢特厚板']['dta7'];
        $arr[4] = $info['不锈钢特厚板']['不锈钢特厚板']['dta8'];
        $arr[5] = $arr[6] = $arr[7] = $arr[8] = $arr[9] = $arr[10] = "";
        $arr[11] = $info['不锈钢特厚板']['不锈钢特厚板']['dta3'];
        $arr[12] = $info['不锈钢特厚板']['不锈钢特厚板']['dta4'];
        $arr[13] = $arr[14] = $arr[15] = $arr[16] = "";
        $data[] = $arr;

        $arr = array();
        $arr[0] = "不锈钢厚板";
        $arr[1] = $info['不锈钢厚板']['不锈钢厚板']['dta5'];
        $arr[2] = $info['不锈钢厚板']['不锈钢厚板']['dta6'];
        $arr[3] = $info['不锈钢厚板']['不锈钢厚板']['dta7'];
        $arr[4] = $info['不锈钢厚板']['不锈钢厚板']['dta8'];
        $arr[5] = $arr[6] = $arr[7] = $arr[8] = $arr[9] = $arr[10] = "";
        $arr[11] = $info['不锈钢厚板']['不锈钢厚板']['dta3'];
        $arr[12] = $info['不锈钢厚板']['不锈钢厚板']['dta4'];
        $arr[13] = $arr[14] = $arr[15] = $arr[16] = "";
        $data[] = $arr;

        $arr = array();
        $arr[0] = "不锈钢中板";
        $arr[1] = $info['不锈钢中板']['不锈钢中板']['dta5'];
        $arr[2] = $info['不锈钢中板']['不锈钢中板']['dta6'];
        $arr[3] = $info['不锈钢中板']['不锈钢中板']['dta7'];
        $arr[4] = $info['不锈钢中板']['不锈钢中板']['dta8'];
        $arr[5] = $arr[6] = $arr[7] = $arr[8] = $arr[9] = $arr[10] = "";
        $arr[11] = $info['不锈钢中板']['不锈钢中板']['dta3'];
        $arr[12] = $info['不锈钢中板']['不锈钢中板']['dta4'];
        $arr[13] = $arr[14] = $arr[15] = $arr[16] = "";
        $data[] = $arr;

        $arr = array();
        $arr[0] = "攀钢";
        $arr[1] = $info['中板']['攀钢']['dta5'];
        $arr[2] = $info['中板']['攀钢']['dta6'];
        $arr[3] = $arr[4] = "";
        $arr[5] = $arr[6] = $arr[7] = $arr[8] = $arr[9] = $arr[10] = "";
        $arr[11] = $arr[12] = $arr[13] = $arr[14] = $arr[15] = $arr[16] = "";
        $data[] = $arr;

        $arr = array();
        $arr[0] = "2、热轧商品卷";
        $arr[1] = $info['中厚宽钢带']['中厚宽钢带']['dta5'];
        $arr[1] += $info['热轧薄宽钢带']['热轧薄宽钢带']['dta5'];
        $arr[1] -= $info['中厚宽钢带']['太原钢铁']['dta5'];
        $arr[1] -= $info['热轧薄宽钢带']['太原钢铁']['dta5'];

        $arr[2] = $info['中厚宽钢带']['中厚宽钢带']['dta5']*$info['中厚宽钢带']['中厚宽钢带']['dta6'];
        $arr[2] += $info['热轧薄宽钢带']['热轧薄宽钢带']['dta5']*$info['热轧薄宽钢带']['热轧薄宽钢带']['dta6'];
        $arr[2] -= $info['中厚宽钢带']['太原钢铁']['dta5']*$info['中厚宽钢带']['太原钢铁']['dta6'];
        $arr[2] -= $info['热轧薄宽钢带']['太原钢铁']['dta5']*$info['热轧薄宽钢带']['太原钢铁']['dta6'];
        $arr[2] = $arr[1]==0 ? 0 : round($arr[2]/$arr[1]);

        $arr[3] = $info['中厚宽钢带']['中厚宽钢带']['dta7'];
        $arr[3] += $info['热轧薄宽钢带']['热轧薄宽钢带']['dta7'];
        $arr[3] -= $info['中厚宽钢带']['太原钢铁']['dta7'];
        $arr[3] -= $info['热轧薄宽钢带']['太原钢铁']['dta7'];

        $arr[4] = $info['中厚宽钢带']['中厚宽钢带']['dta7']*$info['中厚宽钢带']['中厚宽钢带']['dta8'];
        $arr[4] += $info['热轧薄宽钢带']['热轧薄宽钢带']['dta7']*$info['热轧薄宽钢带']['热轧薄宽钢带']['dta8'];
        $arr[4] -= $info['中厚宽钢带']['太原钢铁']['dta7']*$info['中厚宽钢带']['太原钢铁']['dta8'];
        $arr[4] -= $info['热轧薄宽钢带']['太原钢铁']['dta7']*$info['热轧薄宽钢带']['太原钢铁']['dta8'];
        $arr[4] = $arr[3]==0 ? 0 : round($arr[4]/$arr[3]);

        $arr[5] = $info['中厚宽钢带']['新钢']['dta5'];
        $arr[5] += $info['热轧薄宽钢带']['新钢']['dta5'];
        $arr[6] = $info['中厚宽钢带']['新钢']['dta5']*$info['中厚宽钢带']['新钢']['dta6'];
        $arr[6] += $info['热轧薄宽钢带']['新钢']['dta5']*$info['热轧薄宽钢带']['新钢']['dta6'];
        $arr[6] = $arr[5]==0 ? 0 : round($arr[6]/$arr[5]);

        $arr[7] = $info['中厚宽钢带']['新钢']['dta7'];
        $arr[7] += $info['热轧薄宽钢带']['新钢']['dta7'];
        $arr[8] = $info['中厚宽钢带']['新钢']['dta7']*$info['中厚宽钢带']['新钢']['dta8'];
        $arr[8] += $info['热轧薄宽钢带']['新钢']['dta7']*$info['热轧薄宽钢带']['新钢']['dta8'];
        $arr[8] = $arr[7]==0 ? 0 : round($arr[8]/$arr[7]);

        $arr[9] = $arr[6]-$arr[2];
        $arr[10] = $arr[8]>0 ? ($arr[8]-$arr[4]) : 0;
        $arr[11] = $arr[1]+$arr[3];
        $arr[12] = $arr[11]==0 ? 0 : round(($arr[1]*$arr[2]+$arr[3]*$arr[4])/$arr[11]);

        $arr[13] = $arr[5]+$arr[7];
        $arr[14] = $arr[13]==0 ? 0 : round(($arr[5]*$arr[6]+$arr[7]*$arr[8])/$arr[13]);

        $arr[15] = $arr[14]-$arr[12];
        $arr[16] = "";
        $data[] = $arr;

        $arr = array();
        $arr[0] = "中厚宽钢带<br>(不含太原钢铁)";
        $arr[1] = $info['中厚宽钢带']['中厚宽钢带']['dta5'];
        $arr[1] -= $info['中厚宽钢带']['太原钢铁']['dta5'];

        $arr[2] = $info['中厚宽钢带']['中厚宽钢带']['dta5']*$info['中厚宽钢带']['中厚宽钢带']['dta6'];
        $arr[2] -= $info['中厚宽钢带']['太原钢铁']['dta5']*$info['中厚宽钢带']['太原钢铁']['dta6'];
        $arr[2] = $arr[1]==0 ? 0 : round($arr[2]/$arr[1]);

        $arr[3] = $info['中厚宽钢带']['中厚宽钢带']['dta7'];
        $arr[3] -= $info['中厚宽钢带']['太原钢铁']['dta7'];

        $arr[4] = $info['中厚宽钢带']['中厚宽钢带']['dta7']*$info['中厚宽钢带']['中厚宽钢带']['dta8'];
        $arr[4] -= $info['中厚宽钢带']['太原钢铁']['dta7']*$info['中厚宽钢带']['太原钢铁']['dta8'];
        $arr[4] = $arr[3]==0 ? 0 : round($arr[4]/$arr[3]);

        $arr[5] = $info['中厚宽钢带']['新钢']['dta5'];
        $arr[6] = $info['中厚宽钢带']['新钢']['dta5']*$info['中厚宽钢带']['新钢']['dta6'];
        $arr[6] = $arr[5]==0 ? 0 : round($arr[6]/$arr[5]);

        $arr[7] = $info['中厚宽钢带']['新钢']['dta7'];
        $arr[8] = $info['中厚宽钢带']['新钢']['dta7']*$info['中厚宽钢带']['新钢']['dta8'];
        $arr[8] = $arr[7]==0 ? 0 : round($arr[8]/$arr[7]);

        $arr[9] = $arr[10] = "";
        $arr[11] = $arr[12] = $arr[13] = $arr[14] = $arr[15] = $arr[16] = "";
        $data[] = $arr;

        $arr = array();
        $arr[0] = "热轧薄宽钢带<br>(不含太原钢铁)";
        $arr[1] = $info['热轧薄宽钢带']['热轧薄宽钢带']['dta5'];
        $arr[1] -= $info['热轧薄宽钢带']['太原钢铁']['dta5'];

        $arr[2] = $info['热轧薄宽钢带']['热轧薄宽钢带']['dta5']*$info['热轧薄宽钢带']['热轧薄宽钢带']['dta6'];
        $arr[2] -= $info['热轧薄宽钢带']['太原钢铁']['dta5']*$info['热轧薄宽钢带']['太原钢铁']['dta6'];
        $arr[2] = $arr[1]==0 ? 0 : round($arr[2]/$arr[1]);

        $arr[3] = $info['热轧薄宽钢带']['热轧薄宽钢带']['dta7'];
        $arr[3] -= $info['热轧薄宽钢带']['太原钢铁']['dta7'];

        $arr[4] = $info['热轧薄宽钢带']['热轧薄宽钢带']['dta7']*$info['热轧薄宽钢带']['热轧薄宽钢带']['dta8'];
        $arr[4] -= $info['热轧薄宽钢带']['太原钢铁']['dta7']*$info['热轧薄宽钢带']['太原钢铁']['dta8'];
        $arr[4] = $arr[3]==0 ? 0 : round($arr[4]/$arr[3]);

        $arr[5] = $info['热轧薄宽钢带']['新钢']['dta5'];
        $arr[6] += $info['热轧薄宽钢带']['新钢']['dta5']*$info['热轧薄宽钢带']['新钢']['dta6'];
        $arr[6] = $arr[5]==0 ? 0 : round($arr[6]/$arr[5]);

        $arr[7] = $info['热轧薄宽钢带']['新钢']['dta7'];
        $arr[8] += $info['热轧薄宽钢带']['新钢']['dta7']*$info['热轧薄宽钢带']['新钢']['dta8'];
        $arr[8] = $arr[7]==0 ? 0 : round($arr[8]/$arr[7]);

        $arr[9] = $arr[10] = "";
        $arr[11] = $arr[12] = $arr[13] = $arr[14] = $arr[15] = $arr[16] = "";
        $data[] = $arr;

        $arr = array();
        $arr[0] = "3、 冷轧卷";
        $arr[1] = $info['冷轧薄宽钢带']['冷轧薄宽钢带']['dta5'];
        $arr[1] -= $info['不锈冷轧薄宽钢带']['不锈冷轧薄宽钢带']['dta5'];

        $arr[2] = $info['冷轧薄宽钢带']['冷轧薄宽钢带']['dta5']*$info['冷轧薄宽钢带']['冷轧薄宽钢带']['dta6'];
        $arr[2] -= $info['不锈冷轧薄宽钢带']['不锈冷轧薄宽钢带']['dta5']*$info['不锈冷轧薄宽钢带']['不锈冷轧薄宽钢带']['dta6'];
        $arr[2] = $arr[1]==0 ? 0 : round($arr[2]/$arr[1]);

        $arr[3] = $info['冷轧薄宽钢带']['冷轧薄宽钢带']['dta7'];
        $arr[3] -= $info['不锈冷轧薄宽钢带']['不锈冷轧薄宽钢带']['dta7'];

        $arr[4] = $info['冷轧薄宽钢带']['冷轧薄宽钢带']['dta7']*$info['冷轧薄宽钢带']['冷轧薄宽钢带']['dta8'];
        $arr[4] -= $info['不锈冷轧薄宽钢带']['不锈冷轧薄宽钢带']['dta7']*$info['不锈冷轧薄宽钢带']['不锈冷轧薄宽钢带']['dta8'];
        $arr[4] = $arr[3]==0 ? 0 : round($arr[4]/$arr[3]);

        $arr[5] = $info['冷轧薄宽钢带']['新钢']['dta5'];
        $arr[5] -= $info['不锈冷轧薄宽钢带']['新钢']['dta5'];
        $arr[6] = $info['冷轧薄宽钢带']['新钢']['dta5']*$info['冷轧薄宽钢带']['新钢']['dta6'];
        $arr[6] -= $info['不锈冷轧薄宽钢带']['新钢']['dta5']*$info['不锈冷轧薄宽钢带']['新钢']['dta6'];
        $arr[6] = $arr[5]==0 ? 0 : round($arr[6]/$arr[5]);

        $arr[7] = $info['冷轧薄宽钢带']['新钢']['dta7'];
        $arr[7] -= $info['不锈冷轧薄宽钢带']['新钢']['dta7'];
        $arr[8] = $info['冷轧薄宽钢带']['新钢']['dta7']*$info['冷轧薄宽钢带']['新钢']['dta8'];
        $arr[8] -= $info['不锈冷轧薄宽钢带']['新钢']['dta7']*$info['不锈冷轧薄宽钢带']['新钢']['dta8'];
        $arr[8] = $arr[7]==0 ? 0 : round($arr[8]/$arr[7]);

        $arr[9] = $arr[6]-$arr[2];
        $arr[10] = $arr[8]>0 ? ($arr[8]-$arr[4]) : 0;
        $arr[11] = $arr[1]+$arr[3];
        $arr[12] = $arr[11]==0 ? 0 : round(($arr[1]*$arr[2]+$arr[3]*$arr[4])/$arr[11]);

        $arr[13] = $arr[5]+$arr[7];
        $arr[14] = $arr[13]==0 ? 0 : round(($arr[5]*$arr[6]+$arr[7]*$arr[8])/$arr[13]);

        $arr[15] = $arr[14]-$arr[12];
        $arr[16] = "";
        $data[] = $arr;

        $arr = array();
        $arr[0] = "其中：不锈<br>冷轧薄宽钢带";
        $arr[1] = $info['不锈冷轧薄宽钢带']['不锈冷轧薄宽钢带']['dta5'];
        $arr[2] = $info['不锈冷轧薄宽钢带']['不锈冷轧薄宽钢带']['dta6'];
        $arr[3] = $info['不锈冷轧薄宽钢带']['不锈冷轧薄宽钢带']['dta7'];
        $arr[4] = $info['不锈冷轧薄宽钢带']['不锈冷轧薄宽钢带']['dta8'];

        $arr[5] = $info['不锈冷轧薄宽钢带']['新钢']['dta5'];
        $arr[6] = $info['不锈冷轧薄宽钢带']['新钢']['dta6'];
        $arr[7] = $info['不锈冷轧薄宽钢带']['新钢']['dta7'];
        $arr[8] = $info['不锈冷轧薄宽钢带']['新钢']['dta8'];

        $arr[9] = $arr[10] = $arr[11] = $arr[12] = $arr[13] = $arr[14] = $arr[15] = $arr[16] = "";
        $data[] = $arr;

        $arr = array();
        $arr[0] = "其中：柳钢<br>冷轧薄宽钢带";
        $arr[1] = $arr[2] = $arr[3] = $arr[4] = $arr[5] = $arr[6] = $arr[7] = $arr[8] = "";
        $arr[9] = $arr[10] = $arr[11] = $arr[12] = $arr[13] = $arr[14] = $arr[15] = $arr[16] = "";
        $data[] = $arr;

        $arr = array();
        $arr[0] = "冷轧薄宽钢带";
        $arr[1] = $info['冷轧薄宽钢带']['冷轧薄宽钢带']['dta5'];
        $arr[2] = $info['冷轧薄宽钢带']['冷轧薄宽钢带']['dta6'];
        $arr[3] = $info['冷轧薄宽钢带']['冷轧薄宽钢带']['dta7'];
        $arr[4] = $info['冷轧薄宽钢带']['冷轧薄宽钢带']['dta8'];

        $arr[5] = $info['冷轧薄宽钢带']['新钢']['dta5'];
        $arr[6] = $info['冷轧薄宽钢带']['新钢']['dta6'];
        $arr[7] = $info['冷轧薄宽钢带']['新钢']['dta7'];
        $arr[8] = $info['冷轧薄宽钢带']['新钢']['dta8'];

        $arr[9] = $arr[10] = $arr[11] = $arr[12] = $arr[13] = $arr[14] = $arr[15] = $arr[16] = "";
        $data[] = $arr;

        $arr = array();
        $arr[0] = "4、钢筋";
        $arr[1] = $info['钢筋']['钢筋']['dta5'];
        $arr[2] = $info['钢筋']['钢筋']['dta6'];
        $arr[3] = $info['钢筋']['钢筋']['dta7'];
        $arr[4] = $info['钢筋']['钢筋']['dta8'];

        $arr[5] = $info['钢筋']['新钢']['dta5'];
        $arr[6] = $info['钢筋']['新钢']['dta6'];
        $arr[7] = $info['钢筋']['新钢']['dta7'];
        $arr[8] = $info['钢筋']['新钢']['dta8'];

        $arr[9] = $arr[6]>0 ? ($arr[6]-$arr[2]) : 0;
        $arr[10] = $arr[8]>0 ? ($arr[8]-$arr[4]) : 0;
        $arr[11] = $arr[1]+$arr[3];
        $arr[12] = $arr[11]==0 ? 0 : round(($arr[1]*$arr[2]+$arr[3]*$arr[4])/$arr[11]);

        $arr[13] = $arr[5]+$arr[7];
        $arr[14] = $arr[13]==0 ? 0 : round(($arr[5]*$arr[6]+$arr[7]*$arr[8])/$arr[13]);

        $arr[15] = $arr[14]>0 ? ($arr[14]-$arr[12]) : 0;
        $arr[16] = "";
        $data[] = $arr;

        $arr = array();
        $arr[0] = "5、线材(盘条)";
        $arr[1] = $info['线材(盘条)']['线材(盘条)']['dta5'];
        $arr[2] = $info['线材(盘条)']['线材(盘条)']['dta6'];
        $arr[3] = $info['线材(盘条)']['线材(盘条)']['dta7'];
        $arr[4] = $info['线材(盘条)']['线材(盘条)']['dta8'];

        $arr[5] = $info['线材(盘条)']['新钢']['dta5'];
        $arr[6] = $info['线材(盘条)']['新钢']['dta6'];
        $arr[7] = $info['线材(盘条)']['新钢']['dta7'];
        $arr[8] = $info['线材(盘条)']['新钢']['dta8'];

        $arr[9] = $arr[6]>0 ? ($arr[6]-$arr[2]) : 0;
        $arr[10] = $arr[8]>0 ? ($arr[8]-$arr[4]) : 0;
        $arr[11] = $arr[1]+$arr[3];
        $arr[12] = $arr[11]==0 ? 0 : round(($arr[1]*$arr[2]+$arr[3]*$arr[4])/$arr[11]);

        $arr[13] = $arr[5]+$arr[7];
        $arr[14] = $arr[13]==0 ? 0 : round(($arr[5]*$arr[6]+$arr[7]*$arr[8])/$arr[13]);

        $arr[15] = $arr[14]>0 ? ($arr[14]-$arr[12]) : 0;
        $arr[16] = "";
        $data[] = $arr;

        $arr_sum = array();
        $arr_sum[0] = "合计";
        $arr_sum[4] = $arr_sum[3] = $arr_sum[2] = $arr_sum[1] = 0;
        $arr_sum[8] = $arr_sum[7] = $arr_sum[6] = $arr_sum[5] = 0;
        $arr_sum[12] = $arr_sum[11] = $arr_sum[10] = $arr_sum[9] = 0;
        $arr_sum[16] = $arr_sum[15] = $arr_sum[14] = $arr_sum[13] = 0;

        foreach($data as $key=>$tmp){
            if(strpos($tmp[0],"、")){
                if($bigtype==2) {
                    $arr_sum[1] += $tmp[1];
                    $arr_sum[3] += $tmp[3];

                    $arr_sum[11] +=$tmp[11];
                }

                $arr_sum[5] +=$tmp[5];
                $arr_sum[7] +=$tmp[7];
                $arr_sum[9] +=$tmp[9]*$tmp[5];
                $arr_sum[10] +=$tmp[10]*$tmp[7];

                $arr_sum[13] +=$tmp[13];
                $arr_sum[14] +=$tmp[14]*$tmp[13];
                $arr_sum[15] +=$tmp[15]*$tmp[13];
            }
        }
        $arr_sum[9] = $arr_sum[5]==0 ? 0 : round($arr_sum[9]/$arr_sum[5],2);
        $arr_sum[10] = $arr_sum[7]==0 ? 0 : round($arr_sum[10]/$arr_sum[7]);
        $arr_sum[14] = $arr_sum[13]==0 ? 0 : round($arr_sum[14]/$arr_sum[13]);
        $arr_sum[15] = $arr_sum[13]==0 ? 0 : round($arr_sum[15]/$arr_sum[13]);
        $data[] = $arr_sum;

        $this->assign("data", $data);
        $this->assign("params", $params);
    }


    public function scjgbg($params){
        //echo "<pre>";
        //print_r($params);
        $GUID = $params['GUID'];
        $params['date'] = $params['date']=="" ? date("Y-m") : $params['date'];
        $date = date("Y-m-01",strtotime($params['date']."-01"));
        $date_month = date("n",strtotime($date));

        $date_next = date("Y-m-01",strtotime("+32 day".$date));//下个月的1号，当月有31天 +1month可能有问题
        $date_pre = date("Y-m-01",strtotime("-1 day".$date));


        $sql = "select id from data_table_base where type=4 and isdel=0 and uptype=6 and sdate='".$date."' limit 1 ";
        $baseid = $this->xg->getOne($sql);

        $sql = "select * from data_table where baseid='".$baseid."' ";
        $info = $this->xg->getRow($sql);

        //上一月录入的价格标杆
        $sql = "select id from data_table_base where type=4 and isdel=0 and uptype=6 and sdate='".$date_pre."' limit 1 ";
        $baseid = $this->xg->getOne($sql);

        $sql = "select * from data_table where baseid='".$baseid."' ";
        $info2 = $this->xg->getRow($sql);

        $info['dta1_zd'] = $this->zhangdie_($info['dta1'],$info2['dta1']);
        $info['dta2_zd'] = $this->zhangdie_($info['dta2'],$info2['dta2']);
        $info['dta3_zd'] = $this->zhangdie_($info['dta3'],$info2['dta3']);
        $info['dta4_zd'] = $this->zhangdie_($info['dta4'],$info2['dta4']);
        $info['dta5_zd'] = $this->zhangdie_($info['dta5'],$info2['dta5']);
        $info['dta6_zd'] = $this->zhangdie_($info['dta6'],$info2['dta6']);
        $info['dta7_zd'] = $this->zhangdie_($info['dta7'],$info2['dta7']);
        $info['dta8_zd'] = $this->zhangdie_($info['dta8'],$info2['dta8']);
        $info['dta9_zd'] = $this->zhangdie_($info['dta9'],$info2['dta9']);

        $info3 = array();
        //shpi_material_pzp  WHERE vid=0 国产矿指数 主库的 dateday weiprice
        $sql = "select avg(weiprice) from shpi_material_pzp where vid=0 and dateday>='".$date."' and dateday<='".$date_next."' ";
        $info3['dta1'] = $this->maindao->getOne($sql);
        $sql = "select avg(weiprice) from shpi_material_pzp where vid=0 and dateday>='".$date_pre."' and dateday<='".$date."' ";
        $info3['dta1_zd'] = $this->maindao->getOne($sql);
        $info3['dta1_zd'] = round($info3['dta1']-$info3['dta1_zd']);
        $info3['dta1_zd'] = $this->zhangdie_($info3['dta1_zd'],0);
        $info3['dta1'] = round($info3['dta1']);

        //shpi_mj_pzp WHERE vid=1 AND type=1 低硫主焦煤指数(坑口价) 主库 dateday weiprice
        $sql = "select avg(weiprice) from shpi_mj_pzp where vid=1 AND type=1 and dateday>='".$date."' and dateday<='".$date_next."' ";
        $info3['dta2'] = $this->maindao->getOne($sql);
        $sql = "select avg(weiprice) from shpi_mj_pzp where vid=1 AND type=1 and dateday>='".$date_pre."' and dateday<='".$date."' ";
        $info3['dta2_zd'] = $this->maindao->getOne($sql);
        $info3['dta2_zd'] = round($info3['dta2']-$info3['dta2_zd']);
        $info3['dta2_zd'] = $this->zhangdie_($info3['dta2_zd'],0);
        $info3['dta2'] = round($info3['dta2']);

        //SteelCaiGou_Info ftime the_price_tax
        //河钢一级冶金焦价格 where 1 and is_show=2 and the_price_tax!='' and the_price_tax!='0' AND gcid=364 and onlyid='14f501702ec44df127de2fbb701a2d21'
        //沙钢重废   where 1 and is_show=2 and the_price_tax!='' and the_price_tax!='0' AND gcid=120 and onlyid='333f16b4de4d5be66455c5d444be7450'
        //硅锰   where 1 and is_show=2 and the_price_tax!='' and the_price_tax!='0' AND gcid=364 and onlyid='85dee82d6323af2a2ed74c3b5a1819ed'
        //硅铁   where 1 and is_show=2 and the_price_tax!='' and the_price_tax!='0' AND gcid=364 and onlyid='923c6ed2ffddb35c274140fb86df8c54'
        $where = " 1 and is_show=2 and the_price_tax!='' and the_price_tax!='0' AND gcid=364 and onlyid='14f501702ec44df127de2fbb701a2d21' ";
        $sql = "select avg(the_price_tax) from SteelCaiGou_Info where ".$where." and ftime>='".$date."' and ftime<='".$date_next."' ";
        $info3['dta3'] = $this->gcdao->getOne($sql);
        $sql = "select avg(the_price_tax) from SteelCaiGou_Info where ".$where." and ftime>='".$date_pre."' and ftime<='".$date."' ";
        $info3['dta3_zd'] = $this->gcdao->getOne($sql);
        $info3['dta3_zd'] = round($info3['dta3']-$info3['dta3_zd']);
        $info3['dta3_zd'] = $this->zhangdie_($info3['dta3_zd'],0);
        $info3['dta3'] = round($info3['dta3']);

        $where = " 1 and is_show=2 and the_price_tax!='' and the_price_tax!='0' AND gcid=120 and onlyid='333f16b4de4d5be66455c5d444be7450' ";
        $sql = "select avg(the_price_tax) from SteelCaiGou_Info where ".$where." and ftime>='".$date."' and ftime<='".$date_next."' ";
        $info3['dta4'] = $this->gcdao->getOne($sql);
        $sql = "select avg(the_price_tax) from SteelCaiGou_Info where ".$where." and ftime>='".$date_pre."' and ftime<='".$date."' ";
        $info3['dta4_zd'] = $this->gcdao->getOne($sql);
        $info3['dta4_zd'] = round($info3['dta4']-$info3['dta4_zd']);
        $info3['dta4_zd'] = $this->zhangdie_($info3['dta4_zd'],0);
        $info3['dta4'] = round($info3['dta4']);

        $where = " 1 and is_show=2 and the_price_tax!='' and the_price_tax!='0' AND gcid=364 and onlyid='85dee82d6323af2a2ed74c3b5a1819ed' ";
        $sql = "select avg(the_price_tax) from SteelCaiGou_Info where ".$where." and ftime>='".$date."' and ftime<='".$date_next."' ";
        $info3['dta5'] = $this->gcdao->getOne($sql);
        $sql = "select avg(the_price_tax) from SteelCaiGou_Info where ".$where." and ftime>='".$date_pre."' and ftime<='".$date."' ";
        $info3['dta5_zd'] = $this->gcdao->getOne($sql);
        $info3['dta5_zd'] = round($info3['dta5']-$info3['dta5_zd']);
        $info3['dta5_zd'] = $this->zhangdie_($info3['dta5_zd'],0);
        $info3['dta5'] = round($info3['dta5']);

        $where = " 1 and is_show=2 and the_price_tax!='' and the_price_tax!='0' AND gcid=364 and onlyid='923c6ed2ffddb35c274140fb86df8c54' ";
        $sql = "select avg(the_price_tax) from SteelCaiGou_Info where ".$where." and ftime>='".$date."' and ftime<='".$date_next."' ";
        $info3['dta6'] = $this->gcdao->getOne($sql);
        $sql = "select avg(the_price_tax) from SteelCaiGou_Info where ".$where." and ftime>='".$date_pre."' and ftime<='".$date."' ";
        $info3['dta6_zd'] = $this->gcdao->getOne($sql);
        $info3['dta6_zd'] = round($info3['dta6']-$info3['dta6_zd']);
        $info3['dta6_zd'] = $this->zhangdie_($info3['dta6_zd'],0);
        $info3['dta6'] = round($info3['dta6']);



        $this->assign("GUID", $GUID);
        $this->assign("info", $info);
        $this->assign("info3", $info3);
        $this->assign("date_month", $date_month);
        $this->assign("params", $params);
    }

    public function save_scjgbg($params){

        $dta_ym = $params['dta_ym'];

        $GUID = $params['GUID'];
        $user = $this->getUserInfo($GUID);

        $date = date("Y-m-01",strtotime($dta_ym."-01"));


        //删除
        $sql = "update data_table_base set isdel=1 where type=4 and uptype=6 and sdate='" . $date . "' ";
        $this->xg->execute($sql);

        //插入数据
        $sql = "INSERT INTO data_table_base SET type=4,uptype=6,dta_type='ZGX_SCJGBG' ";
        $sql .= ",sdate='".$date."',edate='".$date."' ";
        $sql .= ",createtime=now(),createuser='".$user['Uid']."',createusername='".$user['TrueName']."' ";
        $this->xg->execute("set names 'utf8';");
        $this->xg->execute($sql);
        $baseid = $this->xg->insert_id();


        $sql = "INSERT INTO data_table SET baseid='".$baseid."',dta_type='ZGX_SCJGBG' ";
        $sql .= ",dta_ym='".$date."',createtime=now(),createuser='".$user['Uid']."' ";
        $sql .= ",dta1='".$params['dta1']."',dta2='".$params['dta2']."',dta3='".$params['dta3']."',dta4='".$params['dta4']."',dta5='".$params['dta5']."' ";
        $sql .= ",dta6='".$params['dta6']."',dta7='".$params['dta7']."',dta8='".$params['dta8']."',dta9='".$params['dta9']."' ";
        $this->xg->execute($sql);

        $resultArray = array(
            "code" => 1,
            "msg" => "保存成功"
        );

        echo json_encode($resultArray);
        exit;
    }

    public function uploadyrl($params){

        $mc_type = $params['mc_type'];
        //echo "aaa";
        //print_R($user);
        $sdate = date("Y-01");
        $edate = date("Y-m");

        $this->assign("sdate", $sdate);
        $this->assign("edate", $edate);
        $this->assign("mc_type", $mc_type);
        $this->assign("params", $params);

    }

    public function importexcelyrl($params){

        if ($_FILES['file']["error"] == "0") {
            //include_once("/usr/local/www/libs/PHPExcel/PHPExcel.php");

            $file = $_FILES['file']['tmp_name'];
            /*$type = pathinfo($_FILES['file']['name']);
            $type = strtolower($type["extension"]);
            if ($type == 'xlsx') {
                $type = 'Excel2007';
            } elseif ($type == 'xls') {
                $type = 'Excel5';
            }*/

            //$objReader = PHPExcel_IOFactory::createReader($type);//判断使用哪种格式
            //$objReader->setReadDataOnly(true); //只读取数据,会智能忽略所有空白行,这点很重要！！！
            //$objPHPExcel = $objReader->load($file); //加载Excel文件

            $fileType=\PhpOffice\PhpSpreadsheet\IOFactory::identify($file);//自动获取文件的类型提供给phpexcel用
            $objReader=\PhpOffice\PhpSpreadsheet\IOFactory::createReader($fileType);//获取文件读取操作对象
            $objReader->setReadDataOnly(true); //只读取数据,会智能忽略所有空白行,这点很重要！！！
            $objPHPExcel=$objReader->load($file);//加载文件

            $sheet =  $objPHPExcel->getActiveSheet();
            //获取当前工作表最大行数
            $rows = $sheet->getHighestRow();
            //获取当前工作表最大列数,返回的是最大的列名
            //$cols = $sheet->getHighestColumn();

            $variety = "";
            $comname = "";
            $data = array();
            for ($k = 4; $k <= $rows; $k++) {
                $arr = array();
                $A = $sheet->getCell("A" . $k)->getValue();
                $B = $sheet->getCell("B" . $k)->getValue();
                if($A!="" && $A!=null){
                    $variety = $sheet->getCell("A" . $k)->getValue();
                }
                if(($B!="" && $B!=null) || $A=="合计"){
                    $comname = $sheet->getCell("B" . $k)->getValue();
                }

                //$arr['A'] = $sheet->getCell("A" . $k)->getValue();
                //$arr['B'] = $sheet->getCell("B" . $k)->getValue();
                $arr['A'] = $variety;
                $arr['B'] = $comname;
                $arr['C'] = $sheet->getCell("C" . $k)->getValue();
                $arr['D'] = $sheet->getCell("D" . $k)->getCalculatedValue();
                $arr['E'] = $sheet->getCell("E" . $k)->getCalculatedValue();
                $arr['F'] = $sheet->getCell("F" . $k)->getCalculatedValue();
                $arr['G'] = $sheet->getCell("G" . $k)->getValue();
                $arr['H'] = $sheet->getCell("H" . $k)->getValue();
                $arr['I'] = $sheet->getCell("I" . $k)->getCalculatedValue();
                $arr['J'] = $sheet->getCell("J" . $k)->getCalculatedValue();
                $arr['K'] = $sheet->getCell("K" . $k)->getCalculatedValue();
                $arr['L'] = $sheet->getCell("L" . $k)->getValue();
                $arr['M'] = $sheet->getCell("M" . $k)->getValue();

                $arr['D'] = $arr['D']==0 ? "" : round($arr['D']);
                $arr['E'] = $arr['E']==0 ? "" : round($arr['E']);
                $arr['F'] = $arr['F']==0 ? "" : round($arr['F']);
                $arr['I'] = $arr['I']==0 ? "" : round($arr['I']);
                $arr['J'] = $arr['J']==0 ? "" : round($arr['J']);
                $arr['K'] = $arr['K']==0 ? "" : round($arr['K']);
                $data[] = $arr;
            }

            if(empty($data)){
                $res["Code"] = 1;
            }else{

                $GUID = $params['GUID'];
                $user = $this->getUserInfo($GUID);

                $dta_type = "XG_YRLCGJG";
                $sdate = date("Y-m-01",strtotime($params['date']."-01"));
                $edate = $params['date']."-01";

                $this->xg->execute("set names 'utf8';");
                $sql = "update data_table_base set isdel=1 where type=8 and uptype='".$params['bigtype']."' and sdate='".$sdate."' ";
                $this->xg->execute($sql);

                $sql = "INSERT INTO data_table_base SET type=8,uptype='".$params['bigtype']."',dta_type='".$dta_type."' ";
                $sql .= ",sdate='".$sdate."',edate='".$edate."' ";
                $sql .= ",createtime=now(),createuser='".$user['Uid']."',createusername='".$user['TrueName']."' ";
                $this->xg->execute($sql);
                $baseid = $this->xg->insert_id();

                foreach($data as $key=>$tmp){
                    $sql = "INSERT INTO data_table SET baseid='".$baseid."',dta_type='".$dta_type."' ";
                    $sql .= ",dta_ym='".$sdate."',createtime=now(),createuser='".$user['Uid']."' ";
                    $sql .= ",dta1='".$tmp['A']."',dta2='".$tmp['B']."',dta3='".$tmp['C']."' ";
                    $sql .= ",dta4='".$tmp['D']."',dta5='".$tmp['E']."',dta6='".$tmp['F']."',dta7='".$tmp['G']."' ";
                    $sql .= ",dta8='".$tmp['H']."',dta9='".$tmp['I']."',dta10='".$tmp['J']."',dta11='".$tmp['K']."' ";
                    $sql .= ",dta12='".$tmp['L']."',dta13='".$tmp['M']."' ";
                    $this->xg->execute($sql);
                }

                $res["Code"] = 0;
            }
        }
        else
        {
            $res["Code"] = 1;
        }
        echo json_encode($res);
    }

    public function getyrllist($params){
        $sdate = $params['sdate']."-01";
        $edate = $params['edate']."-01";
        $bigtype = $params['bigtype'];

        $page = $params['page'];
        $limit = $params['limit'];

        if ($page < 1) {
            $page = 1;
        }
        $start = ($page - 1) * $limit;
        $where = "  and uptype in ('1','2','3','4')";

        if($sdate){
            $where .= " and sdate>='".$sdate."' ";
        }

        if($bigtype){
            $where .= " and uptype='".$bigtype."' ";
        }

        if($edate){
            $where .= " and sdate<='".$edate."' ";
        }

        $total = $this->xg->getYrlListTotal($where);
        $dataInfo = $this->xg->getYrlListInfo($where, $start, $limit);

        foreach ($dataInfo as &$articleInfo) {
            if($articleInfo['uptype']=="1"){
                $articleInfo['uptype'] = date("Y年n月",strtotime($articleInfo['sdate']))."炼焦煤长协采购价格";
            }else if($articleInfo['uptype']=="2"){
                $articleInfo['uptype'] = date("Y年n月",strtotime($articleInfo['sdate']))."炼焦煤贸易采购价格";
            }else if($articleInfo['uptype']=="3"){
                $articleInfo['uptype'] = date("Y年n月",strtotime($articleInfo['sdate']))."焦炭长协采购价格";
            }else if($articleInfo['uptype']=="4"){
                $articleInfo['uptype'] = date("Y年n月",strtotime($articleInfo['sdate']))."焦炭贸易采购价格";
            }

        }

        $code = 0;
        if ($dataInfo) {
            $code = 0;
        }
        $return_array = array(
            "code" => $code,
            "data" => $dataInfo,
            "count" => $total
        );
        echo json_encode($return_array);
    }

    public function deleteyrl($params){
        $id = $params['id'];
        $return_array = array(
            "code" => 1,
            "msg" => "操作成功！",
        );
        if ($id) {

            //删除
            $sql = "update data_table_base set ";
            $sql .= "isdel=1 where id='" . $id . "' ";
            $this->xg->execute($sql);

        } else {
            $return_array = array(
                "code" => 0,
                "msg" => "操作失败！",
            );
        }
        echo json_encode($return_array);
    }

    public function yrldetail($params){
        $id = $params['id'];
        $type = $params['type'];
        $data = $this->xg->getZgxDetail($id);
        //print_r($data);
        $this->assign("type", $type);
        $this->assign("data", $data);
        $this->assign("params", $params);
    }

    public function updateyrldetail($params){

        $id = $params['id'];
        $dta1 = $params['dta1'];
        $GUID = $params['GUID'];
        $user = $this->getUserInfo($GUID);

        $base = $this->xg->get_capital_return_input_base($params);

        $this->xg->execute("set names 'utf8';");
        $sql = "update data_table_base set isdel=1 where id='".$id."' ";
        $this->xg->execute($sql);

        $sql = "INSERT INTO data_table_base SET type='".$base['type']."',uptype='".$base['uptype']."',dta_type='".$base['dta_type']."' ";
        $sql .= ",sdate='".$base['sdate']."',edate='".$base['edate']."' ";
        $sql .= ",createtime=now(),createuser='".$user['Uid']."',createusername='".$user['TrueName']."' ";
        $this->xg->execute($sql);
        $baseid = $this->xg->insert_id();

        foreach($dta1 as $key=>$tmp){
            $sql = "INSERT INTO data_table SET baseid='".$baseid."',dta_type='".$base['dta_type']."' ";
            $sql .= ",dta_ym='".$base['sdate']."',createtime=now(),createuser='".$user['Uid']."' ";

            $sql .= ",dta1='".$tmp."',dta2='".$params['dta2'][$key]."',dta3='".$params['dta3'][$key]."' ";
            $sql .= ",dta4='".$params['dta4'][$key]."',dta5='".$params['dta5'][$key]."',dta6='".$params['dta6'][$key]."',dta7='".$params['dta7'][$key]."' ";
            $sql .= ",dta8='".$params['dta8'][$key]."',dta9='".$params['dta9'][$key]."',dta10='".$params['dta10'][$key]."',dta11='".$params['dta11'][$key]."' ";
            $sql .= ",dta12='".$params['dta12'][$key]."',dta13='".$params['dta13'][$key]."' ";
            $this->xg->execute($sql);
        }


        $resultArray = array(
            "code" => 1,
            "id" => $baseid,
            "msg" => "保存成功"
        );

        echo json_encode($resultArray);
        exit;
    }

    public function huizong_yrl_jgjkb($params){

        $params['sdate'] = $params['sdate']=="" ? date("Y-01") : $params['sdate'];
        $params['edate'] = $params['edate']=="" ? date("Y-m") : $params['edate'];
        $params['mode'] = $params['mode']=="" ? 1 : $params['mode'];
        $params['bigtype'] = $params['bigtype']=="" ? 1 : $params['bigtype'];
        $sdate = date("Y-m-01",strtotime($params['sdate']."-01"));
        $edate = date("Y-m-01",strtotime($params['edate']."-01"));
        //本月末
        $edate2 = date("Y-m-01",strtotime("+31 day".$edate));
        //表格标题
        //$date_month = date("Y年n-",strtotime($sdate)).date("n月焦炭价格监控表",strtotime($edate));
        if($params['bigtype']=="1"){
            $date_month = "炼焦煤价格监控表";
        }else{
            $date_month = "焦炭价格监控表";
        }

        $date_arr = array();
        $sdate2 = $sdate;
        $date_arr[$sdate2] =$sdate2;
        while (true){
            if($sdate == $edate || $sdate>$edate){
                break;
            }
            $sdate2 = date("Y-m-01",strtotime("+31 day".$sdate2));
            $date_arr[$sdate2] =$sdate2;
            if($sdate2 == $edate){
                break;
            }
        }

        //print_r($date_arr);exit;

        //焦炭价格指数
        $sql = "select price,dateday from shpi_material where topicture='3' and dateday>='".$sdate."' and dateday<'".$edate2."'  ";
        $jtzs_arr = $this->maindao->query($sql);
        $jtzs2 = array();
        foreach ($jtzs_arr as $key=>$tmp){
            $dateday = date("Y-m-01",strtotime($tmp['dateday']));
            $jtzs2[$dateday][] = $tmp['price'];
        }
        $jtzs = array();
        foreach ($jtzs2 as $key=>$tmp ){
            $jtzs[$key] = array_sum($tmp)/count($tmp);
            $jtzs[$key] = round($jtzs[$key]);
        }

        //国产主焦指数
        $sql = "select weiprice as price,dateday from shpi_mj_pzp where vid=1 AND type=1 and dateday>='".$sdate."' and dateday<'".$edate2."'  ";
        $jmzs_arr = $this->maindao->query($sql);
        $jmzs2 = array();
        foreach ($jmzs_arr as $key=>$tmp){
            $dateday = date("Y-m-01",strtotime($tmp['dateday']));
            $jmzs2[$dateday][] = $tmp['price'];
        }
        $jmzs = array();
        foreach ($jmzs2 as $key=>$tmp ){
            $jmzs[$key] = array_sum($tmp)/count($tmp);
            $jmzs[$key] = round($jmzs[$key]);
        }

        //详细数据
        $this->xg->execute("set names 'utf8';");
        $sql = "select dta1,dta2,dta3,dta10,uptype,sdate from data_table_base,data_table where";
        $sql .= " data_table_base.id=data_table.baseid and type=8 and isdel=0 and sdate>='".$sdate."' and sdate<='".$edate."' ";
        $list = $this->xg->query($sql);
        //print_r($list);

        $info = array();
        $key_arr1 = array("Ⅰ主焦煤盘江柏果（土城）","Ⅰ主焦煤盘江柏果");
        $key_arr2 = array("高硫山西焦煤介休、万安","高硫主焦煤山西焦煤介休");
        $key_arr3 = array("Ⅱ#1/3煤南昌鑫拓邹城");
        $key_arr4 = array("二级焦平煤-京宝商酒务");
        $key_arr5 = array("二级焦江能上塘");
        foreach ($list as $key=>$tmp){

            $uptype = $tmp['uptype'];
            $sdate = $tmp['sdate'];
            $dta1 = trim($tmp['dta1']);
            $dta2 = trim($tmp['dta2']);
            $dta3 = trim($tmp['dta3']);
            $dta1 = mb_ereg_replace('^(　| )+','', $dta1);
            $dta2 = mb_ereg_replace('^(　| )+','', $dta2);
            $dta3 = mb_ereg_replace('^(　| )+','', $dta3);

            $k = $dta1.$dta2.$dta3;

            if(in_array($k,$key_arr1)){
                $k = "Ⅰ主焦煤盘江土城";
            }
            if(in_array($k,$key_arr2)){
                $k = "高硫主焦煤山西焦煤介休、万安";
            }
            if(in_array($k,$key_arr3)){
                $k = "Ⅱ#1/3焦煤南昌鑫拓邹城";
            }

            if(in_array($k,$key_arr4)){
                $k = "二级焦平煤（京宝）商酒务";
            }
            if(in_array($k,$key_arr5)){
                $k = "二级焦江能（新高）上塘";
            }

            $info[$uptype][$k][$sdate] = $tmp['dta10'];
        }
        //echo "<pre>";
        //print_r($info);

        $data_tr = "<tr style='font-weight: bold;text-align: center;'>";
        $data_tr .= "<td>品种</td>";
        $data_tr .= "<td>价格</td>";
        foreach ($date_arr as $tmp) {
            $data_tr .= "<td>" . date("n月", strtotime($tmp)) . "</td>";
        }
        $data_tr .= "<td>备注</td>";
        $data_tr .= "</tr>";

        if($params['bigtype']=="1"){
            //Ⅰ主焦煤
            $key1 = "Ⅰ主焦煤平煤平顶山东";
            $key2 = "Ⅰ主焦煤盘江土城";

            $data_tr .= "<tr>";
            $data_tr .= "<td rowspan='5'>Ⅰ主焦煤</td>";
            $data_tr .= "<td>长协价格</td>";
            foreach ($date_arr as $tmp) {
                $data_tr .= "<td>" . $info[1][$key1][$tmp] . "</td>";
            }
            $data_tr .= "<td>平煤到厂含税价</td>";
            $data_tr .= "</tr>";

            $data_tr .= "<tr>";
            $data_tr .= "<td>贸易价格</td>";
            foreach ($date_arr as $tmp) {
                $data_tr .= "<td>" . $info[2][$key2][$tmp] . "</td>";
            }
            $data_tr .= "<td>盘江土城到厂含税价</td>";
            $data_tr .= "</tr>";

            $data_tr .= "<tr>";
            $data_tr .= "<td>市场价格</td>";
            foreach ($date_arr as $tmp) {
                $data_tr .= "<td>" . $jmzs[$tmp] . "</td>";
            }
            $data_tr .= "<td>国产主焦指数</td>";
            $data_tr .= "</tr>";

            $data_tr .= "<tr>";
            $data_tr .= "<td>长协与贸易价差</td>";
            foreach ($date_arr as $tmp) {
                $data_tr .= "<td>" . $this->zhangdie2_($info[1][$key1][$tmp] , $info[2][$key2][$tmp]) . "</td>";
            }
            $data_tr .= "<td></td>";
            $data_tr .= "</tr>";

            $data_tr .= "<tr>";
            $data_tr .= "<td>长协与市场价差</td>";
            foreach ($date_arr as $tmp) {
                $data_tr .= "<td>" . $this->zhangdie2_($info[1][$key1][$tmp] , $jmzs[$tmp]) . "</td>";
            }
            $data_tr .= "<td></td>";
            $data_tr .= "</tr>";

            //Ⅱ主焦煤
            $key1 = "Ⅱ主焦煤山西焦煤阳泉曲";
            $key2 = "Ⅱ主焦煤山西焦煤介休、万安";

            $data_tr .= "<tr>";
            $data_tr .= "<td rowspan='5'>Ⅱ主焦煤</td>";
            $data_tr .= "<td>长协价格</td>";
            foreach ($date_arr as $tmp) {
                $data_tr .= "<td>" . $info[1][$key1][$tmp] . "</td>";
            }
            $data_tr .= "<td>山西焦煤阳泉曲到厂含税价</td>";
            $data_tr .= "</tr>";

            $data_tr .= "<tr>";
            $data_tr .= "<td>贸易价格</td>";
            foreach ($date_arr as $tmp) {
                $data_tr .= "<td>" . $info[2][$key2][$tmp] . "</td>";
            }
            $data_tr .= "<td>山西焦煤介休、万安到厂含税价</td>";
            $data_tr .= "</tr>";

            $data_tr .= "<tr>";
            $data_tr .= "<td>市场价格</td>";
            foreach ($date_arr as $tmp) {
                $data_tr .= "<td>" . $jmzs[$tmp] . "</td>";
            }
            $data_tr .= "<td>国产主焦指数</td>";
            $data_tr .= "</tr>";

            $data_tr .= "<tr>";
            $data_tr .= "<td>长协与贸易价差</td>";
            foreach ($date_arr as $tmp) {
                $data_tr .= "<td>" . $this->zhangdie2_($info[1][$key1][$tmp] , $info[2][$key2][$tmp]) . "</td>";
            }
            $data_tr .= "<td></td>";
            $data_tr .= "</tr>";

            $data_tr .= "<tr>";
            $data_tr .= "<td>长协与市场价差</td>";
            foreach ($date_arr as $tmp) {
                $data_tr .= "<td>" . $this->zhangdie2_($info[1][$key1][$tmp] , $jmzs[$tmp]) . "</td>";
            }
            $data_tr .= "<td></td>";
            $data_tr .= "</tr>";

            //高硫主焦煤
            $key1 = "高硫主焦煤平煤平顶山东";
            $key2 = "高硫主焦煤山西焦煤介休、万安";

            $data_tr .= "<tr>";
            $data_tr .= "<td rowspan='5'>高硫主焦煤</td>";
            $data_tr .= "<td>长协价格</td>";
            foreach ($date_arr as $tmp) {
                $data_tr .= "<td>" . $info[1][$key1][$tmp] . "</td>";
            }
            $data_tr .= "<td>平煤平顶山东到厂含税价</td>";
            $data_tr .= "</tr>";

            $data_tr .= "<tr>";
            $data_tr .= "<td>贸易价格</td>";
            foreach ($date_arr as $tmp) {
                $data_tr .= "<td>" . $info[2][$key2][$tmp] . "</td>";
            }
            $data_tr .= "<td>山西焦煤介休、万安到厂含税价</td>";
            $data_tr .= "</tr>";

            $data_tr .= "<tr>";
            $data_tr .= "<td>市场价格</td>";
            foreach ($date_arr as $tmp) {
                $data_tr .= "<td>" . $jmzs[$tmp] . "</td>";
            }
            $data_tr .= "<td>国产主焦指数</td>";
            $data_tr .= "</tr>";

            $data_tr .= "<tr>";
            $data_tr .= "<td>长协与贸易价差</td>";
            foreach ($date_arr as $tmp) {
                $data_tr .= "<td>" . $this->zhangdie2_($info[1][$key1][$tmp] , $info[2][$key2][$tmp]) . "</td>";
            }
            $data_tr .= "<td></td>";
            $data_tr .= "</tr>";

            $data_tr .= "<tr>";
            $data_tr .= "<td>长协与市场价差</td>";
            foreach ($date_arr as $tmp) {
                $data_tr .= "<td>" . $this->zhangdie2_($info[1][$key1][$tmp] , $jmzs[$tmp]) . "</td>";
            }
            $data_tr .= "<td></td>";
            $data_tr .= "</tr>";

            //Ⅰ#1/3焦煤
            $key1 = "Ⅰ#1/3焦煤淮河能源潘集西";
            $key2 = "Ⅰ#1/3焦煤山东七五官桥";

            $data_tr .= "<tr>";
            $data_tr .= "<td rowspan='5'>Ⅰ#1/3焦煤</td>";
            $data_tr .= "<td>长协价格</td>";
            foreach ($date_arr as $tmp) {
                $data_tr .= "<td>" . $info[1][$key1][$tmp] . "</td>";
            }
            $data_tr .= "<td>淮河能源到厂含税价</td>";
            $data_tr .= "</tr>";

            $data_tr .= "<tr>";
            $data_tr .= "<td>贸易价格</td>";
            foreach ($date_arr as $tmp) {
                $data_tr .= "<td>" . $info[2][$key2][$tmp] . "</td>";
            }
            $data_tr .= "<td>山东七五到厂含税价</td>";
            $data_tr .= "</tr>";

            $data_tr .= "<tr>";
            $data_tr .= "<td>市场价格</td>";
            foreach ($date_arr as $tmp) {
                $data_tr .= "<td>" . $jmzs[$tmp] . "</td>";
            }
            $data_tr .= "<td>国产主焦指数</td>";
            $data_tr .= "</tr>";

            $data_tr .= "<tr>";
            $data_tr .= "<td>长协与贸易价差</td>";
            foreach ($date_arr as $tmp) {
                $data_tr .= "<td>" . $this->zhangdie2_($info[1][$key1][$tmp] , $info[2][$key2][$tmp]) . "</td>";
            }
            $data_tr .= "<td></td>";
            $data_tr .= "</tr>";

            $data_tr .= "<tr>";
            $data_tr .= "<td>长协与市场价差</td>";
            foreach ($date_arr as $tmp) {
                $data_tr .= "<td>" . $this->zhangdie2_($info[1][$key1][$tmp] , $jmzs[$tmp]) . "</td>";
            }
            $data_tr .= "<td></td>";
            $data_tr .= "</tr>";

            //Ⅱ#1/3焦煤
            $key1 = "Ⅱ#1/3焦煤山东能源高庄（官桥）";
            $key2 = "Ⅱ#1/3焦煤南昌鑫拓邹城";

            $data_tr .= "<tr>";
            $data_tr .= "<td rowspan='5'>Ⅱ#1/3焦煤</td>";
            $data_tr .= "<td>长协价格</td>";
            foreach ($date_arr as $tmp) {
                $data_tr .= "<td>" . $info[1][$key1][$tmp] . "</td>";
            }
            $data_tr .= "<td>山东能源高庄（官桥）到厂含税价</td>";
            $data_tr .= "</tr>";

            $data_tr .= "<tr>";
            $data_tr .= "<td>贸易价格</td>";
            foreach ($date_arr as $tmp) {
                $data_tr .= "<td>" . $info[2][$key2][$tmp] . "</td>";
            }
            $data_tr .= "<td>南昌鑫拓到厂含税价</td>";
            $data_tr .= "</tr>";

            $data_tr .= "<tr>";
            $data_tr .= "<td>市场价格</td>";
            foreach ($date_arr as $tmp) {
                $data_tr .= "<td>" . $jmzs[$tmp] . "</td>";
            }
            $data_tr .= "<td>国产主焦指数</td>";
            $data_tr .= "</tr>";

            $data_tr .= "<tr>";
            $data_tr .= "<td>长协与贸易价差</td>";
            foreach ($date_arr as $tmp) {
                $data_tr .= "<td>" . $this->zhangdie2_($info[1][$key1][$tmp] , $info[2][$key2][$tmp]) . "</td>";
            }
            $data_tr .= "<td></td>";
            $data_tr .= "</tr>";

            $data_tr .= "<tr>";
            $data_tr .= "<td>长协与市场价差</td>";
            foreach ($date_arr as $tmp) {
                $data_tr .= "<td>" . $this->zhangdie2_($info[1][$key1][$tmp] , $jmzs[$tmp]) . "</td>";
            }
            $data_tr .= "<td></td>";
            $data_tr .= "</tr>";
        }
        else
        {
            //一级焦
            $key1 = "一级焦淮北临涣青町";
            $key2 = "一级焦新昌南景德镇南";

            $data_tr .= "<tr>";
            $data_tr .= "<td rowspan='5'>一级焦</td>";
            $data_tr .= "<td>长协价格</td>";
            foreach ($date_arr as $tmp) {
                $data_tr .= "<td>" . $info[3][$key1][$tmp] . "</td>";
            }
            $data_tr .= "<td>淮北临焕到厂含税价</td>";
            $data_tr .= "</tr>";

            $data_tr .= "<tr>";
            $data_tr .= "<td>贸易价格</td>";
            foreach ($date_arr as $tmp) {
                $data_tr .= "<td>" . $info[4][$key2][$tmp] . "</td>";
            }
            $data_tr .= "<td>新昌南到厂含税价</td>";
            $data_tr .= "</tr>";

            $data_tr .= "<tr>";
            $data_tr .= "<td>市场价格</td>";
            foreach ($date_arr as $tmp) {
                $data_tr .= "<td>" . $jtzs[$tmp] . "</td>";
            }
            $data_tr .= "<td>焦炭价格指数</td>";
            $data_tr .= "</tr>";

            $data_tr .= "<tr>";
            $data_tr .= "<td>长协与贸易价差</td>";
            foreach ($date_arr as $tmp) {
                $data_tr .= "<td>" . $this->zhangdie2_($info[3][$key1][$tmp] , $info[4][$key2][$tmp]) . "</td>";
            }
            $data_tr .= "<td></td>";
            $data_tr .= "</tr>";

            $data_tr .= "<tr>";
            $data_tr .= "<td>长协与市场价差</td>";
            foreach ($date_arr as $tmp) {
                $data_tr .= "<td>" . $this->zhangdie2_($info[3][$key1][$tmp] , $jtzs[$tmp]) . "</td>";
            }
            $data_tr .= "<td></td>";
            $data_tr .= "</tr>";

            //二级焦
            $key3 = "二级焦平煤（京宝）商酒务";
            $key4 = "二级焦江能（新高）上塘";

            $data_tr .= "<tr>";
            $data_tr .= "<td rowspan='5'>二级焦</td>";
            $data_tr .= "<td>长协价格</td>";
            foreach ($date_arr as $tmp) {
                $data_tr .= "<td>" . $info[3][$key3][$tmp] . "</td>";
            }
            $data_tr .= "<td>平煤（商酒务）到厂含税价</td>";
            $data_tr .= "</tr>";

            $data_tr .= "<tr>";
            $data_tr .= "<td>贸易价格</td>";
            foreach ($date_arr as $tmp) {
                $data_tr .= "<td>" . $info[4][$key4][$tmp] . "</td>";
            }
            $data_tr .= "<td>江能（新高）到厂含税价</td>";
            $data_tr .= "</tr>";

            $data_tr .= "<tr>";
            $data_tr .= "<td>市场价格</td>";
            foreach ($date_arr as $tmp) {
                $data_tr .= "<td>" . $jtzs[$tmp] . "</td>";
            }
            $data_tr .= "<td>焦炭价格指数</td>";
            $data_tr .= "</tr>";

            $data_tr .= "<tr>";
            $data_tr .= "<td>长协与贸易价差</td>";
            foreach ($date_arr as $tmp) {
                $data_tr .= "<td>" . $this->zhangdie2_($info[3][$key3][$tmp] , $info[4][$key4][$tmp]) . "</td>";
            }
            $data_tr .= "<td></td>";
            $data_tr .= "</tr>";

            $data_tr .= "<tr>";
            $data_tr .= "<td>长协与市场价差</td>";
            foreach ($date_arr as $tmp) {
                $data_tr .= "<td>" . $this->zhangdie2_($info[3][$key3][$tmp] , $jtzs[$tmp]) . "</td>";
            }
            $data_tr .= "<td></td>";
            $data_tr .= "</tr>";

        }


        $this->assign("data", $data_tr);
        $this->assign("date_month", $date_month);
        $this->assign("params", $params);
    }

    //返回涨跌变化
    function zhangdie_($int1,$int2){
        $int1 = trim($int1) == "" ? 0 : $int1;
        $int2 = trim($int2) == "" ? 0 : $int2;
        $int = $int1 - $int2;
        if($int<0){
            $intstr = "<font color=green>".$int."</font>";
        }elseif($int>0){
            $intstr = "<font color=red>".$int."</font>";
        }elseif($int=="" || $int==0){
            $intstr = "0";
        }else{
            $intstr = "<font >".$int."</font>";
        }
        return $intstr;
    }

    function zhangdie2_($int,$int2){
        $zd = round($int-$int2);
        if($int=="" || $int2==""){
            $zd = "-";
        }

        return $zd;
    }

}
?>