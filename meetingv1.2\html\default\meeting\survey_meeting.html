﻿<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">


<title>钢之家会议数据大屏</title>
<link rel="stylesheet" href="css/meeting/index.css">
<link rel="stylesheet" href="css/meeting/public.css">
<script src="js/meeting/layui/layui.all.js"></script>
<link rel="stylesheet" href="js/meeting/layui/css/layui.css">

<style>
.header h1 {
   
     width:100%;
     max-width: 1380px;
     background-repeat: no-repeat;
background-position: center;
background-size: cover;
height: 88px;
justify-content: center;
align-items: center;

}
.header h2 {
   width:100%;
   max-width: 1380px;
   background-repeat: no-repeat;
background-position: center;
background-size: cover;
height: 88px;
justify-content: center;
align-items: center;
margin: 0px auto;
}
.nav ul{
    display: flex;
  justify-content: center;
  list-style-type: none; /* 移除列表的标记，如果需要的话 */
  padding: 0; 
}
.nav li.active{height: 40px;line-height: 32px;}
.jq22-content span{color: #00FFFF;}
.jq22-content{width: 96%;margin: 0px auto;margin-top: 20px;}
.jq33-content span{color: #00FFFF;}
.jq33-content{width: 96%;margin: 0px auto;margin-top: 10px;}
.progressbar{margin-top: 1px;width: 80%;float: left;}
.jq22-content .left{width: 560px;float: left;line-height: 30px;}
.jq22-content .right{float: right;height: 30px;width: calc(100% - 560px);}
.span{display: block;margin-top: 3px;}
.right2{float: right;height: 40px;width: calc(100% - 240px);}
.header:before{
        background: none;
    }
    .header:after{
        background: none;
    }
    .progressbar{width: 50%;}
    /* .left{width: 500px;float: left;height: 40px;}
    .right{float: right;height: 40px;width: calc(100% - 500px);} */
    .header h1{background: none;}

    .center .center-left .left-top{height: 45%;}
    .center .center-left .left-cen{height:54%}
    .center .center-right{width: 32%;}
    .center .center-left{width: 32.5%;}

    .center .center-right .right-top{height: 45%;}
    .center .center-right .right-cen{height:54%}
    .title{font-size: 15px;text-align: left;width: 96%;margin: 0px auto;padding-top: 5px;}
    #chart6title{margin-bottom: 10px;}
    .center{top:55px}
    .header h1 span.font1{padding:  5px 0px 0px;}
    .header h1{height: 45px;}
    .header{height: 0px;}
    .header h1 span.font1{font-size: 28px;}
    .nav{height:40px ;}
    .nav li{width: 33%;height: 40px; line-height: 40px;}


    .leftk{width: 100%;float: left;height: 30px;}
    .rightk{height: 30px;width:100%;margin-bottom: 10px;float: left;}

    .jq33-content .left{width: 560px;float: left;height: 20px;}
    .header h2 span{padding:  10px 0px 0px;font-size: 26px;font-weight: bold;}
    .header h2{height: 36px;color: #01C4F7;text-align: center;}
    .datetitle{font-size: 18px;text-align: center;padding-top: 0px;}
</style>
</head>
<body>

<!-- <div class="zuo" id="zuodian"></div>
<div class="you" id="youdian"></div> -->

<header>
<div class="header">
	<h1><span class="font1" id="maintitle"><img src="images/gzj_logo.png"><font id="surveytitle"><{$meetname.MeetingName}></font></span></h1>
    <h2><span class="font1">上期“强源助企”产融服务基地 问卷调查</span></h2>
    <div style="height: 40px;" ><div class="datetitle title " ><{$meetingdate}></div></div>
</div>
</header>

<section>

    <div class="center" id="mokuai4" style="display: none;top:100px;">
		
        <div class="center-left fl" style="width: 48%;margin-left: 1%;margin-right: 1%;">

            <div class="left-top rightTop div21">
                <div class="title" id="chart21title"></div>
                 <div class="bottom-b">
                    <div  id="chart21" class="allnav"></div>
                    <div  id="chart21Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                    <div class="panel-footer"></div>
                </div>                    
            </div>
            <div class="left-cen rightTop div22">
                <div class="title" id="chart22title"></div>
                 <div class="bottom-b">
                    <div  id="chart22" class="allnav"></div>
                    <div  id="chart22Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                    <div class="panel-footer"></div>
                </div>
            </div>

        </div>

        <div class="center-left fl" style="width: 48%;margin-left: 1%;margin-right: 1%;">

            <div class="left-top rightTop div23">
                <div class="title" id="chart23title"></div>
                 <div class="bottom-b">
                    <div  id="chart23" class="allnav"></div>
                    <div  id="chart23Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                    <div class="panel-footer"></div>
                </div>                    
            </div>
            <div class="left-cen rightTop div24">
                <div class="title" id="chart24title"></div>
                 <div class="bottom-b">
                    <div  id="chart24" class="allnav"></div>
                    <div  id="chart24Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                    <div class="panel-footer"></div>
                </div>
            </div>

        </div>

    </div>

</section>

<nav>

</nav>
<script>
    var istry='<{$istry}>';
    var mtid='<{$params.mtid}>';
    var isphone=0;
    var id='<{$params.id}>';
    
</script>
<script src="js/meeting/jquery.min.js"></script>
<script src="js/meeting/axios.min.js"></script>
<script src="js/meeting/echarts.min.js"></script>
<script src="js/meeting/surveyecharts2.js?v=20240622412"></script>
<script src="js/meeting/fontscroll.js"></script>
<script src="js/meeting/util.js"></script>

<script src="js/meeting/jquery.lineProgressbar.js"></script> 

    <script>
      
	$(function(){  
		
});

        //顶部时间
        function getTime() {
            var myDate = new Date();
            var myYear = myDate.getFullYear(); //获取完整的年份(4位,1970-????)
            var myMonth = myDate.getMonth() + 1; //获取当前月份(0-11,0代表1月)
            var myToday = myDate.getDate(); //获取当前日(1-31)
            var myDay = myDate.getDay(); //获取当前星期X(0-6,0代表星期天)
            var myHour = myDate.getHours(); //获取当前小时数(0-23)
            var myMinute = myDate.getMinutes(); //获取当前分钟数(0-59)
            var mySecond = myDate.getSeconds(); //获取当前秒数(0-59)
            var week = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
            var nowTime;

            nowTime = myYear + '-' + fillZero(myMonth) + '-' + fillZero(myToday) + '&nbsp;&nbsp;' + fillZero(myHour) + ':' + fillZero(myMinute) + ':' + fillZero(mySecond) + '&nbsp;&nbsp;' + week[myDay] + '&nbsp;&nbsp;';
            //console.log(nowTime);
            $('#time').html(nowTime+'企业管理处');
			//$('#lrcsdate').html(myYear+"年"+myMonth+"月"+myToday+"日");
        };

        function fillZero(str) {
            var realNum;
            if (str < 10) {
                realNum = '0' + str;
            } else {
                realNum = str;
            }
            return realNum;
        }
        setInterval(getTime, 1000);
    </script>
</body>
</html>