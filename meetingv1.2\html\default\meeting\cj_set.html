
<!DOCTYPE html>
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta charset="utf-8">
	<title>抽奖推送+清空</title>
	<link rel="stylesheet" href="css/choujiang/main.css?t=0913"/>
	<script type="text/javascript" src="js/choujiang/jquery.2.1.4.js"></script>
	<script type="text/javascript" src="js/choujiang/ajax.js"></script>
	<script type="text/javascript" src="js/choujiang/base64.js"></script>
	<link rel="stylesheet" href="css/choujiang/index.css?t=0913"/>
	<style type="text/css">
	body{background-image: url(images/choujiang/429/bodybg3.jpg);}
	.demo{ display: flex;justify-content: flex-start;width:980px; margin: 30px auto; text-align:left;height: 86px; padding:10px 10px 0 10px;}
	.btn{ background:none; cursor: pointer; color:#cc0000; text-shadow:0 1px 0 rgba(255,255,255,0.6); font:bold 20px/50px "Microsoft Yahei"; border:none transparent; outline:none;}
	.bt{ text-align:center;background:url("images/choujiang/btbj.png") no-repeat; width:154px; height:60px; margin:0 auto;}
	table td{font-size: 18px;color: #ffffff;}
	table td input{font-size: 18px;color: #333333;width: 300px;height: 25px;line-height: 25px;border: 1px solid #51555C ;border-radius: 10px; outline:none;}
	table td button{background: #ffff00;width: 120px;height: 30px;line-height: 30px;border-radius: 10px;outline: 0;border: 1px solid #ffff00;color: #FF1A08;font-weight: bold;font-size: 18px;margin-left: 35%;margin-top: 20px;}
	table td button:active{background: #FFB900;}
	.wrapper{margin-top: 150px;}
	h2.top_title_meeting2{
		width: 70%;
		word-wrap: break-word;
		margin: 0px auto;
		height: auto;
		font-size: 38px;
	}
	</style>
<script type="text/javascript">
	var mtid="<{$mtid}>";
	function qkong(k){
		if(window.confirm("确定清空中奖数据吗?")){
			console.log(localStorage[k+'temp'+mtid]);
			if(localStorage[k+'temp'+mtid]!=null){
				localStorage.removeItem(k+"temp"+mtid);
			}
			if(localStorage[k+'qxcj'+mtid]!=null){
				localStorage.removeItem(k+"qxcj"+mtid);
			}
			console.log(localStorage[k+'djc'+mtid]);
			if(localStorage[k+'djc'+mtid]!=null){
				localStorage.removeItem(k+"djc"+mtid);
			}
			//qksql(k);
		}
	}


	function qkongall(){
		if(window.confirm("确定清空所有奖项中奖数据吗?")){
			for(var i=0; i<4;i++){
				if(localStorage[i+'temp'+mtid]!=null){
					localStorage.removeItem(i+"temp"+mtid);
				}
				if(localStorage[i+'qxcj'+mtid]!=null){
					localStorage.removeItem(i+"qxcj"+mtid);
				}
				console.log(localStorage[i+'djc'+mtid]);
				if(localStorage[i+'djc'+mtid]!=null){
					localStorage.removeItem(i+"djc"+mtid);
				}
				// $.ajax({
				// 	type: "post",
				// 	url: "meeting.php?action=clearCancerAward",
				// 	data: {mtid:mtid,awardtype:i},
				// 	dataType: "jsonp",
				// 	success: function (response) {
				// 		if (response.Success == 1) {
				// 			console.log("已清空");
				// 		} else {
				// 			if(response.Message=='no'){
				// 				console.log('会议已结束');
				// 			}
				// 		}
				// 	},
				// 	error: function () {
				// 		console.log("失败");
				// 	}
            	// });
			}
			alert("清空成功");
			
		}
	}

	function qksql(k){
		// $.ajax({
        //         type: "post",
        //         url: "meeting.php?action=clearCancerAward",
        //         data: {mtid:mtid,awardtype:k},
        //         dataType: "jsonp",
        //         success: function (response) {
        //             if (response.Success == 1) {
        //                 alert("已清空");
        //             } else {
		// 				if(response.Message=='no')
        //                 	alert('会议已结束');
        //             }
        //         },
        //         error: function () {
        //            alert("失败");
        //         }
        //     });
			
	}





	function tuis(k){
		if(localStorage[k+'temp'+mtid]!=null && localStorage[k+'temp'+mtid]!="[]"){
			var storedNames=localStorage[k+'temp'+mtid];
			storedNames=JSON.parse(storedNames);
			var arr = new Array( );
			for(var i=0;i<storedNames.length;i++ ){
				var writer={"uid":storedNames[i]["uid"],"PersonSign":storedNames[i]['PersonSign'],"Mid":storedNames[i]['Mid'],"awardname":storedNames[i]['awardname']}; 
				arr.push(writer);
			}
			arr=JSON.stringify(arr);
			$.ajax({
                type: "post",
                url: "meeting.php?action=pushaward",
                data: {mtid:mtid,awardtype:k,Status:1, awarddata:Base64.encode(arr)},
                dataType: "jsonp",
                success: function (response) {
                    if (response.Success == 1) {
                        alert(response.Message);
                    } else {
                        alert(response.Msg);
                    }
                },
                error: function () {
                   alert("失败");
                }
            });
				
		}else{
			alert("无数据");
		}
	}


	function tuisall(){
		for(var t=0; t<4;t++){
			if(localStorage[t+'temp'+mtid]!=null && localStorage[t+'temp'+mtid]!="[]"){
				var storedNames=localStorage[t+'temp'+mtid];
				storedNames=JSON.parse(storedNames);
				var arr = new Array( );
				for(var i=0;i<storedNames.length;i++ ){
					var writer={"uid":storedNames[i]["uid"],"PersonSign":storedNames[i]['PersonSign'],"Mid":storedNames[i]['Mid'],"awardname":storedNames[i]['awardname']}; 
					arr.push(writer);
				}
				arr=JSON.stringify(arr);
				$.ajax({
					type: "post",
					url: "meeting.php?action=pushaward",
					data: {mtid:mtid,awardtype:t,Status:1, awarddata:Base64.encode(arr)},
					dataType: "jsonp",
					success: function (response) {
						if (response.Success == 1) {
							console.log(response.Message);
						} else {
							console.log(response.Msg);
						}
					},
					error: function () {
						console.log("失败");
					}
				});
					
			}
		}
		alert("推送成功");
	}





	function ObjStory(uid,PersonSign) //声明对象
	{
		this.uid = uid;
		this.PersonSign= PersonSign;
	}

	
</script>


</head>
<body>
	<div class="kong"></div>
	<h2 class="top_title_meeting2"><{$meetingName}></h2>
	<br>
	<!-- <h2 class="top_title_meeting2"><{$jxtitle}></h2> -->
	
	<div class="wrapper">
		<div id="main">
			<div class="demo" >
				<div class="bt"><input onclick="tuisall();" type="button" class="btn" id="start" value="推送全部"></div>
				<!-- <div class="bt"><input onclick="tuis(0);" type="button" class="btn" id="start" value="推送特等奖"></div> -->
				<div class="bt"><input onclick="tuis(1);" type="button" class="btn" id="start" value="推送一等奖"></div>
				<div class="bt"><input onclick="tuis(2);" type="button" class="btn" id="start" value="推送二等奖"></div>
				<div class="bt"><input onclick="tuis(3);" type="button" class="btn" id="start" value="推送三等奖"></div>
				<!-- <div class="bt"><input onclick="tuis(4);" type="button" class="btn" id="start" value="推送四等奖"></div> -->
			</div>
			<div class="demo" >
				<div class="bt"><input onclick="qkongall();" type="button" class="btn" id="start" value="清空全部"></div>
				<!-- <div class="bt"><input onclick="qkong(0);" type="button" class="btn" id="start" value="清空特等奖"></div> -->
				<div class="bt"><input onclick="qkong(1);" type="button" class="btn" id="start" value="清空一等奖"></div>
				<div class="bt"><input onclick="qkong(2);" type="button" class="btn" id="start" value="清空二等奖"></div>
				<div class="bt"><input onclick="qkong(3);" type="button" class="btn" id="start" value="清空三等奖"></div>
				<!-- <div class="bt"><input onclick="qkong(4);" type="button" class="btn" id="start" value="清空四等奖"></div> -->
			</div>
			
		</div>
	</div>
</body>
</html>