<?php
ini_set('display_errors',1);            //错误信息  
ini_set('display_startup_errors',1);    //php启动错误信息  
error_reporting(-1);                    //打印出所有的 错误信息  1
set_time_limit(0);
include_once( "sys.conf.php" );
if($argv[1])
{
 $_REQUEST['mc_type']=$argv[1];
}
$mc_type=0;
if($_REQUEST['mc_type'])
{
   $mc_type=$_REQUEST['mc_type'];
}

if(!in_array($mc_type,array(0,1,2,3,4,5)))
{
   echo "错误的mc_type，请检查后再试。<br/>";
   exit;
}

	//$wdsn = 'mysql:dbname=steelhome_t1;host=***********;port=4306';//可写库
	//$wuser = 'root2';
	//$wpassword = '123456';
	//采用预处理+事务处理执行SQL操作
	//1.连接数据库
	try {
		$wpdo = new PDO($wdsn, $wuser, $wpassword);
		$wpdo->setAttribute(PDO::ATTR_ERRMODE,PDO::ERRMODE_EXCEPTION);
		echo "ok ";
	} catch (PDOException $e) {
		die("数据库连接失败".$e->getMessage());
	}
	$sql="update app_version set VersionNo=round(VersionNo+0.01,2) where  VersionType=2 and  mc_type='".$mc_type."' and Status=1 limit 1";
	$sth1 = $wpdo->prepare($sql);
	$sth1->execute();


?>