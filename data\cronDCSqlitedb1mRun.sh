#! /bin/bash
#shell_env_path="/etc/steelconf/env/shell_env.php"
#php_exec_path="/usr/local/php8.1/bin/php"
#if [ -e $shell_env_path ]
#then
#        shell_env=`cat $shell_env_path`
#	echo $shell_env
#	if [ $shell_env -eq 0 ]
#	then
#		php_exec_path="/www/server/php/81/bin/php"
#	fi
#fi
source "/usr/local/www/shell_scripts/php_exec_path.sh"
path="/usr/local/www/dc.steelhome.cn/data/"
#php_exec_path="/usr/local/php8.1/bin/php"
#php_exec_path="/www/server/php/81/bin/php"
cd $path
echo $path
mctype=$1
if [ -z "$1" ]; then
    mctype=0
fi
lockfile="/tmp/cron_"$mctype"_1m_taskrun_lockfile.loc";
echo $lockfile
canrunfile="/tmp/cron_"$mctype"_1m_taskrun_canrunfile.loc";
echo $canrunfile
if [ -e $lockfile ]
then
        nowtime=`date +%s`
        time=`cat $lockfile`
        t1=$(($nowtime - $time))

        if [ $t1 -lt 3600 ]
        then
                echo $t1
                exit 1;
        else
                unlink $lockfile
        fi
fi

echo `date +"%s"` > $lockfile
cat $lockfile
$php_exec_path $path"canrunfile.php" $mctype > $canrunfile
cat $canrunfile
if [ -e $canrunfile ]
then
        readfile=`cat $canrunfile`
echo $readfile
echo 'hello'
	if [ $readfile -eq 0 ]
	then
	#参数 商户号 状态2 执行中 状态1 执行完成 状态0 未执行	
	$php_exec_path  $path"RuntaskByMctypeAndRunState.php" $mctype 2	
	$php_exec_path  $path"createsqlite_pc.php" $mctype
        $php_exec_path  $path"createsqlite_android.php" $mctype
        $php_exec_path  $path"createsqlite_ios.php" $mctype
	#$php_exec_path  /usr/local/www/www.steelhome.cn/data/createsqlite_android.php $mctype
	#$php_exec_path  /usr/local/www/www.steelhome.cn/data/createsqlite_ios.php $mctype
	if [ $mctype -eq 0 ]
	then
		#$php_exec_path  /usr/local/www/www.steelhome.cn/data/createsqlite_pc_english.php $mctype
                $php_exec_path  $path"createsqlite_pc_english.php" $mctype
	fi
	$php_exec_path  $path"updatevesion.php" $mctype
	$php_exec_path  $path"RuntaskByMctypeAndRunState.php" $mctype 1
	else
		echo "不执行"
#		  unlink $canrunfile
	fi
fi
 unlink $canrunfile
 unlink $lockfile
