<?php 
// require_once('../../../../steelconf_v3/debug.php');
class dgxundjAction extends AbstractAction
{ 
    public function __construct()
    {
        parent::__construct();
    } 
    public function index($params)
    {   // echo '11';exit;
			$sql="select * from app_session_temp where  GUID ='".$params['GUID']."'  order by  LoginDate DESC  limit 1";
			$res=$this->_dao->getRow($sql);
			$Mid=$res['Mid'];
			$uid=$res['Uid'];
			$type=$params['Type'];
			$username=$res['UserName'];
			$SignCS=$res['SignCS'];
			$sql="select * from app_license where Mid='$Mid'   and  mc_type=1 order by CreateDate desc limit 1";
			$res=$this->_dao->getRow($sql);		
			$id=$res['ID'];
			$sql="select * from app_license_privilege  where liD ='".$id."' and privilege!='' and mc_type='1' ";
			$res=$this->_dao->query($sql); 

			foreach($res as $key => $value){
				$res[$key]['order']='10';
				// echo "<pre/>";echo '排序';print_r($value['orderno']);
				// echo "<pre/>";echo '权利';print_r($value['privilege']);
					
					
				$r=explode(',',$value['privilege']);
				$d=explode(',',$value['orderno']);
				// echo "<pre/>";echo '分割';print_r($d[$type-1]);
				if( $r[$type-1]==1){
					//判断领导的顺序进行排序 给数组一个新字段
					$res[$key]['order']=$d[$type-1];
			
				}
				// echo "<pre/>";echo '分割';print_r($r);exit;
					
			}
			$order=array();
			foreach($res as $re){
				$order[]=$re['order'];
			}
			array_multisort($order,SORT_ASC,$res);//安照order来排序
					
			
			
			foreach($res as $key =>$v){
				$r=explode(',',$v['privilege']);
				if(!array_key_exists($r[$type-1],$GLOBALS['power'])) continue;
				$p_arr[$v['username']]=$r;
				//$use[]=$v['username'];
				$adminname_uid[$v['username']]=$v['uid'];
				$adminname[$v['username']]=$v['truename'];
			} 
			
			//$use=array_unique($use);
			//$use_str="'".implode("','",$use)."'";
			//$res=$this->maindao->query("select * from steelhome.adminuser where username in($use_str)");
			//echo '<pre/>';
			//print_r($this->maindao);
			//foreach($res as $key => $v){
			//	$adminname_uid[$v['username']]=$v['id'];
			//	$adminname[$v['username']]=$v['truename'];
			//}
			
			//print_r($use);exit;
		
			if($params['curdate']!=''){
				$date=$params['curdate'];
			}else{
		       $date = date("Y-m-d");
			}
		
		$xunday1 ='';
		$nodatatitle = '暂无';
		if(date("d",strtotime($date))<11){
			$xunday1 = '01';
			$xunday2='10';
			$nodatatitle .= date('Y年n月', strtotime($date))."上旬带钢旬定价";
			
			$this_xun_end = date("Y-m-$xunday2",strtotime($date));
		}elseif(date("d",strtotime($date))<21){
			$xunday1 = '11';
			$xunday2='20';
			$nodatatitle .= date('Y年n月', strtotime($date))."中旬带钢旬定价";
			
			$this_xun_end = date("Y-m-$xunday2",strtotime($date));
		}else{
			$xunday1 = '21';
			$BeginDate=date('Y-m-01', strtotime($date)); 
		
			$xunday2=date('Y-m-d', strtotime("$BeginDate +1 month -1 day"));
			$nodatatitle .= date('Y年n月', strtotime($date))."下旬带钢旬定价";
			
			$this_xun_end = $xunday2;
		}
		$this_xun_start = date("Y-m-$xunday1",strtotime($date));
		
		
			
		
				
			
			$sql="select * from ng_price_model where modeltype=$type and date>='$this_xun_start' and  date<='$this_xun_end' order by id desc limit 1";	
			//echo $sql;
			$modeldb=$this->drcW->getrow($sql);
			//echo "<pre/>";print_r($modeldb);exit;
			if(!empty($modeldb)){
			$modeldb['modelcontent']=html_entity_decode($modeldb['modelcontent'],ENT_QUOTES,'UTF-8');//echo "<pre/>";print_r($modeldb);
			
			
			
			
			
			//add
			
			
			$sql="select modelid from ng_price_model_detail where modelid='".$modeldb['id']."'  and modelprice_type=$type  order by id desc limit 1";
			
			$isexit=$this->drcW->getone($sql);
		 
			if(empty($isexit)){
				if($params['curdate']!=''){
					
					$showtime=date("Y-m",strtotime($params['curdate']));
					$showxun=date("d",strtotime($params['curdate']));
				}else{
					$showtime=date("Y-m");
			        $showxun=date("d");
				}
				
			if($showxun > "10" && $showxun < "21"){
				$showxun="中";
				$lastxunsd="01";
				$lastxuned="10";
				$lastM=$showtime;
			
			}else if($showxun <="31" && $showxun>"20"){
				$showxun="下";
				$lastxunsd="11";
				$lastxuned="20";
				$lastM=$showtime;
			}else{
					if($params['curdate']!=''){
				$lastM1=date('Y-m', strtotime($params['curdate']));
				$lastM= date("Y-m",strtotime("$lastM1 -1 month"));
				$showxun="上";
				$lastxunsd="21";
				$lastxuned=date('t',strtotime('last month'));
					}else{
				$lastM=date('Y-m', strtotime("m -1 month"));
				$showxun="上";
				$lastxunsd="21";
				$lastxuned=date('t',strtotime('last month'));
					}
			}
			
			$s="00:00:00";
			$e="23:59:59";
			$date1=$lastM."-".$lastxunsd." ".$s;
			$date2=$lastM."-".$lastxuned." ".$e;
			
			$dates=$lastM."-".$lastxunsd;
			
			$curdate=$params['curdate'];
			$ddl=$this->bcsc_xs($showtime,$curdate);
			
			
			$arr=$this->djjy($lastM."-".$lastxunsd,$lastM."-".$lastxuned,$ddl,$curdate);//定价建议
			//print_r($arr);exit;
			//echo $the_price_tax;
			
		//echo 	$changerate_tax;exit;		
			$moxdj=$arr['moxdj'];
			//print_r($moxdj);exit;
			$zd=$arr['zd'];
			$ngyg=$moxdj-$zd;
			
			//echo '----';exit;
			//exit;
			
			//echo $modeldb['modelcontent'];exit;
			$ins_xundj="insert into ng_price_model_detail(modelid,modelprice,modelprice_updown,modeloldprice,modelprice_type,modelpirce_name,modelpirce_name_sort,Mid,uid,GUID,Ismakepricy_men,Ischecked,date,createtime) value('".$modeldb['id']."','".$moxdj."','".$zd."','".$ngyg."','5','2.0mmQ235热轧带钢基价','1','-1','-1','','0','1','$dates',NOW())";
			$this->drcW->execute($ins_xundj);
		    $num='';
		   
				 if($arr['changerate_tax']>0){
				 	$num='上调'.ABS((float)$arr['changerate_tax']).'元/吨';
				 	
				 }
				 if($arr['changerate_tax']==0){
				 	$num='持平';
				 
				 }
				 if($arr['changerate_tax']<0){
				 	$num='下调'.ABS((float)$arr['changerate_tax']).'元/吨';
				 	
				 }
			//echo 	 ABS($arr['changerate_tax']);exit;
			$modeldb['modelcontent'].="<h4 style='text-align:left'>预计本旬中天2.5mm*235Q235带钢价格为".$arr['the_price_tax']."元，较前旬$num,根据当前接单进度，建议本旬定价：</h4>";
			
			$content=htmlentities($modeldb['modelcontent'],ENT_QUOTES,'UTF-8');
			$sql="update ng_price_model set modelcontent ='$content' where id='".$modeldb['id']."'  and modeltype =$type  order by id desc limit 1";
			//echo $sql;exit;
			$this->drcW->execute($sql);
			}
			
			
			
			
			//end
			
			
			
			
			
			
			
			$uidsavebtn=false;
			$uidleadersavebtn=false;
			foreach($p_arr as $key => $v){
				$master[$adminname_uid[$key]]=$v[($type-1)];
				$thisuid=$adminname_uid[$key];
				$result=$this->drcW->query("select id,modelid,modelprice,modelprice_updown,ABS(modelprice_updown) as abs_modelprice_updown,modelpirce_name,date,uid,modeloldprice from ng_price_model_detail where modelprice_type=$type and modelid=".$modeldb["id"]." and uid='".$thisuid."'   order by id  desc limit 1");
		 
				if(empty($result)){
						$result=$this->drcW->query("select  0 as id,'' as modelid,'' as modelprice,'' as modelprice_updown,'' as abs_modelprice_updown,modelpirce_name, '' as date,'".$thisuid."' as uid,modeloldprice  from ng_price_model_detail where modelprice_type=$type and modelid=".$modeldb["id"]." and uid='-1'  order by id  desc limit 1");	 
				}
				
				if($v[($type-1)]==1){
					$master_leader[]=array(
						'uid'=>$adminname_uid[$key],
						'username'=>$key,
						'power'=>$v[($type-1)],
						'truename'=>$adminname[$key],
						'res'=>$result
						);
					if($adminname_uid[$key]==$uid){
						$uidleadersavebtn=true;
					}
				}else if($v[($type-1)]==2){
					$master_price[]=array(
						'uid'=>$adminname_uid[$key],
						'username'=>$key,
						'power'=>$v[($type-1)],
						'truename'=>$adminname[$key],
						'res'=>$result
						);
					if($adminname_uid[$key]==$uid){
						$uidsavebtn=true;
					}
				}
			}
			
			
			
			//权限为3，市场部人员让他与最后一个分管领导操作相同，这里把登录的uid换成最后一个分管领导的
			$record=$this->drcW->query("SELECT * FROM  `Ng_RecordManage` where modelid='".$modeldb["id"]."'");
			$last=	end($master_leader);
			if(!empty($record)&&$master[$uid]==1&&$uid!=$last['uid']){
				if($params['show']!='1'){
					$url = "//".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']."&show=1";
					gourl($url);
				}
			
			}
			if($master[$uid]==3){
			
				$last=	end($master_leader);
			
				$uid=$last['uid'];
				$sql="select * from app_session_temp where  Uid ='".$uid."'  order by  LoginDate DESC  limit 1";
				$m=$this->_dao->getRow($sql);
				$Mid=$m['Mid'];
				$uidleadersavebtn=true;
			}
			
				if($master[$uid]==4||$master[$uid]==''){
			
				$youke=1;
				$this->assign( "youke", $youke );//决策人进来查看 传的值
			}
			
				
			//---
			foreach($master_price as $k){
				$uid_arr[]=$k['uid'];
			}
			foreach($master_leader as $k2){
				$uid_arr2[]=$k2['uid'];
			}
			$privilege=$master[$uid];//当前用户权限
			//------------------
			//如果当前用户权限检测为市场部，他将拥有最后一个分管领导的功能
				
			//echo '1'.$privilege;
			//----------------------
			
			
			
			foreach($master_price as $k){
				$uid_arr[]=$k['uid'];
			}
			foreach($master_leader as $k2){
				$uid_arr2[]=$k2['uid'];
			}
			$privilege=$master[$uid];//当前用户权限
		    $this->assign( "params", $params);
			$this->assign( "uid", $uid);
			$this->assign( "mid", $Mid );
			$this->assign( "modeldb", $modeldb );//echo "<pre/>";print_r($modeldb);
			$this->assign( "uidsavebtn", $uidsavebtn );
			$this->assign( "uidleadersavebtn", $uidleadersavebtn );
			$this->assign( "uid_arr", $uid_arr );
			
			
			$outlook='0';
			foreach($uid_arr as $key=>$value){
				$sql="select * from  ng_price_model_detail where uid='$value'
				and modelid='".$modeldb["id"]."' and modelprice_type =$type";
				
				$out=$this->drcW->query($sql);
				// echo "<pre/>";print_r($out);
				if(empty($out)){
					$outlook='1';
					break;
				}
			}
			$this->assign( "outlook", $outlook);//第一层限制 决策未决策 分管领导不能操作 0表示全部决策 1 表示尚有未决策
			//echo	$outlook;
			$shunxu=array_keys($uid_arr2,$uid,true);
			
			$outlook2='0';
			if($shunxu[0]!=0){
				for($i=0;$i<$shunxu[0];$i++){
						
					$sql="select * from  ng_price_model_detail where uid='$uid_arr2[$i]'
					and modelid='".$modeldb["id"]."' and modelprice_type =$type";
					$out=$this->drcW->query($sql);
					// echo "<pre/>";print_r($out);
					if(empty($out)){
						$outlook2='1';
						break;
					}
				}
			}
			
			$nextmarket='0';
			if($shunxu[0]!=(count($uid_arr2)-1)){
				// echo $uid_arr2[$shunxu[0]+1];
				// echo'<br/>'; print_r ($uid_arr2);
				$uidpre=$uid_arr2[$shunxu[0]+1];
			
				$sql="select * from  ng_price_model_detail where uid='$uidpre'
				and modelid='".$modeldb["id"]."' and modelprice_type =$type ";
				$out=$this->drcW->query($sql);//如果为空则后一个人未决策
				if(empty($out)){
					$nextmarket='1';//
				}
			}
	    $shunxu2=0;
		    if($shunxu[0]!=0){
			  $shunxu2=$shunxu[0]-1;//查前面的的人有没有决策
			 }else{
				 $shunxu2=0;
				 }   
				$sql5="select * from  ng_price_model_detail where uid='$uid_arr2[$shunxu2]'
                                     and modelid='".$modeldb["id"]."' and modelprice_type =$type ";
             $preout=$this->drcW->query($sql5);
				
				 $master_leader2=array();
				 if($outlook2=='1'&&empty($preout)){
				   for($i=0;$i<$shunxu[0];$i++){
					$master_leader2[$i]= $master_leader[$i];
				   }
			    }else{
				   for($i=0;$i<=$shunxu[0];$i++){
					$master_leader2[$i]= $master_leader[$i];
				   }
		     	}
			// echo $uid_a;
				
			// for($i=0;$i<=$shunxu[0];$i++){
			// $master_leader2[$i]= $master_leader[$i];
			
			// }
			// echo $shunxu[0];
				
			// echo "<pre/>";print_r($master_leader2);
			
			// exit;
			$this->assign( "outlook2", $outlook2);
			
			
			$this->assign( "uid_arr2", $uid_arr2 );
			$this->assign( "master_price", $master_price );//echo "<pre/>";print_r($modeldb);
			
			
			if(in_array($uid,$uid_arr2)){
				 $master_leader3=array();
				if($shunxu[0]=='0'){//第一个特殊处理
					
					// if($outlook=='1'){//表示有决策人未决策
						
					// }else{
				// echo $outlook;
					
                       for($i=0;$i<count($uid_arr2);$i++){
						     $sql="select * from  ng_price_model_detail where uid='$uid_arr2[$i]'
                                     and modelid='".$modeldb["id"]."' and modelprice_type =$type ";
				         $out=$this->drcW->query($sql);
						 
				         
// 				         $sql="select * from  ng_price_model_detail where uid='$uid'
// 				         and modelid='".$modeld b["id"]."' and modelprice_type =$type ";
// 				         $out2=$this->drcW->query($sql);
				         		
						 
// 				         echo $sql;
				        
				         
						  if(empty($out)&&$uid_arr2[$i]!=$uid){//修改1
							  
							  break;
						  }
						     if($outlook!='1'){
						    $master_leader3[$i]= $master_leader[$i];
						   }
						
						 
// 						  echo '1';
					   } 
					   
					   
					   
					   
					   
					   
					   	// echo '5555'; echo $nextmarket;
						
							     $sql="select * from  ng_price_model_detail where uid='$uid'
                                     and modelid='".$modeldb["id"]."' and modelprice_type =$type ";
				           $out=$this->drcW->query($sql);
						
						
						
						 if($nextmarket!='1'&&($shunxu[0]!=(count($uid_arr2)-1))){//1 后面那个未决策 0 后面的哪个人已经决策
						 // if(!empty($out)){
						 	
							  if($params['show']!=1){
								  // echo '1111';
								$url = "//".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']."&show=1";
								goUrl($url);
								
							}
						 }
						 // }
						 
						 
						 if($outlook=='1'&&empty($out)){//修改
						 	//echo '1111';
							   if($params['show']!=1){
							   // echo '33333';
								 $url = "//".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']."&show=1";
								 goUrl($url);
								
					         }
							 
						 }
						 
						 
					// }
					
					
					
				     $this->assign( "master_leader", $master_leader3 );//echo "<pre/>";print_r($master_leader3);//分管领导
				
				
				}else{//第二个人之后
				
			  
					  	  // echo "<pre/>";print_r($master_leader2);	
						   $sql4="select * from  ng_price_model_detail where uid='$uid'
                                     and modelid='".$modeldb["id"]."' and modelprice_type =$type ";
						  
						  $out=$this->drcW->query($sql4);
						  
						  if(!empty($out)){
							  
							  	 for($i=0;$i<=$shunxu[0];$i++){
						// $sql="select * from  ng_price_model_detail where uid='$uid_arr2[$i]'
                         // and createtime>='$this_xun_start' and  createtime<='$this_xun_end' and modelprice_type =$type ";
				         // $out=$this->drc->query($sql);   
				     $master_leader2[$i]= $master_leader[$i];
					 	// if(empty($out)){break;}
				                   } 
							  
						  }
						  
						  
						//  echo '111';
						  
					 if($outlook2=='1'&&empty($out)){//表示前面的人未决策
					   if(empty($preout)){
                           if($params['show']!=1){
							// echo '22222';
							$url = "//".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']."&show=1";
							goUrl($url);
						      }
					    }
					 }else{//表示前面的人已经决策
						 //判断在自己后面的人有没有决策
						// echo '5555'; echo $nextmarket;
						 if($nextmarket!='1'&&($shunxu[0]!=(count($uid_arr2)-1))){//1 后面那个未决策 0 后面的哪个人已经决策
						 
							  if($params['show']!=1){
								  // echo '1111';
								$url = "//".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']."&show=1";
								goUrl($url);
								
							}
							
							for($i=0;$i<=count($uid_arr2);$i++){
								$sql="select * from  ng_price_model_detail where uid='$uid_arr2[$i]'
                                    and modelid='".$modeldb["id"]."' and modelprice_type =$type ";
				                $out=$this->drcW->query($sql);
						
						  if(empty($out)&&$uid_arr2[$i]!=$uid){
							  break;
						  }
								
								
								$master_leader2[$i]=$master_leader[$i];
							}
							
							
						 }
					 }
					 
					 
					  $this->assign( "master_leader", $master_leader2);
					  	 // echo "<pre/>";print_r($master_leader2);	
				}
			
			}else{
				$master_leader4=array();
				// echo '444';
				//决策人的判断
				$outjc='0';
				for($i=0;$i<count($uid_arr2);$i++){
					
				  $sql="select * from  ng_price_model_detail where uid='$uid_arr2[$i]'
                  and modelid='".$modeldb["id"]."' and modelprice_type =$type ";
				$out=$this->drcW->query($sql);   
				
				if(!empty($out)){//分管领导有一个决策了 就跳show=1页面
					
					$outjc='1';
					break;
				}
			
				}
				if($outjc=='1'){//xiugai 
					
					 if($params['show']!=1){
							$url = "//".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']."&show=1";
							goUrl($url);
						}
						
						
				 }	
                   $j=0;	
             	for($i=0;$i<=count($uid_arr2);$i++){
					
				  $sql="select * from  ng_price_model_detail where uid='$uid_arr2[$i]'
                  and modelid='".$modeldb["id"]."' and modelprice_type =$type ";
				  $out=$this->drcW->query($sql);  
                  		  
						  
						  if(!empty($out)){
							    $master_leader4[$j]= $master_leader[$i];
						      $j++;
							  }	//放在上面会显示领导未提交	
				}
               // echo "<pre/>";print_r($master_leader4);
				  
			  $this->assign( "master_leader", $master_leader4);//决策人进来查看 传的值
			
			 }
			
			
			//print_r($master_leader3);
			//$this->assign( "master_leader", $master_leader );
			$this->assign( "city_arr", $GLOBALS['city'] );
			$this->assign( "save_city",array('1'=>' ') );
			$this->assign( "privilege", $privilege );
			$this->assign( "type", $type );
			$this->assign( "citynum", count($GLOBALS['save_city']) );
			 $sql="select * from ng_price_model_detail where modelprice_type=$type and modelid=".$modeldb["id"]." and uid=-1  order by id desc limit 1  "; 
			$res=$this->drcW->query($sql);
			/*  //add
			$sql_Ismakepricy_men ="select Ismakepricy_men  from ng_price_model_detail where Ismakepricy_men=1 and modelprice_type=$type and modelid='".$modeldb["id"]."'";
			//echo $sql_Ismakepricy_men;
			$res_Ismakepricy_men =$this->drcW->query($sql_Ismakepricy_men);
		 
			foreach ($res_Ismakepricy_men  as $key=>$v){
			 if($v['Ismakepricy_men']==1){
			 	$params['show']=1;
		     }else{
		     	$params['show']=0;
		     }
			}
			 $this->assign( "params", $params ); */ 
			// print_r($params);
			//end
			for($i=0;$i<count($res);$i++){				
				$res[$i]['abs_modelprice_updown']=$res[$i]['modelprice_updown'];
				if($res[$i]['modelprice_updown']){
					$res[$i]['abs_modelprice_updown']=abs($res[$i]['modelprice_updown']);
				}
			}
			$this->assign( "modeldetail", $res );

		$sql="select Ismakepricy from ng_price_model where modeltype=$type  order by id desc limit 1";	
		$style_url=dirname("//".$_SERVER['SERVER_NAME'].$_SERVER['PHP_SELF']);
		$this->assign( "style_url", $style_url );
		
		$res=$this->drcW->getOne($sql);
	
		if($res=='1'){
			$this->assign("is_makepricy","1");
			
		}else{
			$this->assign("is_makepricy","0");
		}
		if($res=='1'&&$params['show']!='1'){			
			$url = "http://".$_SERVER['HTTP_HOST'].$_SERVER['PHP_SELF']."?".$_SERVER['QUERY_STRING']."&show=1";
			gourl($url);
		}
		
		
			}else{
				
				$this->assign("nodata","1");
				$this->assign("nodata1",$nodatatitle);
				$this->assign( "iswj", 1);
				
			}
			
			if($modeldb['is_wangqi']!='1') {
				 
				 
				$up_wq='update ng_price_model set  is_wangqi=1 where id="'.$modeldb['id'].'" and modeltype="'.$type.'"   ';
				 
				$this->drcW->execute($up_wq);
			}
			$this->assign( "isdgx", 0);
		
				if($modeldb['pricycontent']==''&&$_GET['save_dgx']!=1){
					//$url = $_SERVER['HTTP_REFERER']."&iswj=1&save_dgx=1";
					$url = "https://".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']."&save_dgx=1";
						
					include '/usr/local/www/libs/phpQuery/phpQuery.php';
					phpQuery::newDocumentFile($url);
					$content=pq("html")->html();
					$up_wq='update ng_price_model set pricycontent ="'.htmlentities($content,ENT_QUOTES,"UTF-8").'" where id="'.$modeldb['id'].'" and modeltype="'.$type.'" and is_wangqi=1  ';
					$this->drcW->execute($up_wq);
					$this->assign( "isdgx", 1);
				}
				
			
	
			
			
			
			
    } 
	
	public function save_price($params){
		//echo "<pre/>";print_r($params);//exit;
		$uid=$params['uid'];
		
		$uid_arr=$params['arr'];//决策人
		$uid_arr2=$params['arr2'];//分管领导
		print_r($uid_arr2);
		
		$UserType='0';
		if(in_array($uid,$uid_arr)){
			$UserType='1';
		
		}elseif(in_array($uid,$uid_arr2)){
			$UserType='2';
				
		}
		
		
		$mid=$params['Mid'];
		$type=$params['type'];
		$price=$params['p'];
		$city_price=$params['price'];
	
		$oldprice=$params['oldprice'];
		$updown=$params['updown'];
		$modeldbs=$params['modeldb'];
		// print_r();exit;
	//$params['privilege']==1 ?  $privilege=1: $privilege=0;
		$endmarket='0';
		// end($uid_arr2)==$uid?$privilege=1:$privilege=0;
		if(end($uid_arr2)==$uid){
			$privilege=1;
			//$endmarket='1';
		}else{
			$endmarket='1';
			$privilege=0;
		
		}
		
		foreach($price as $key=>$i){
				$citykey[]=$key;
		}
		$sql="select date from ng_price_model where id=$modeldbs";
		$dates=$this->drcW->getone($sql);
		
		
		$sql="select * from ng_price_model_detail where modelid=$modeldbs and mid=$mid and uid=$uid and modelprice_type=$type  order by id desc limit 1";
		$res=$this->drcW->query($sql);
		//add价格名称默认使用系统的
		$sql="select modelpirce_name from ng_price_model_detail where modelid=$modeldbs and uid=-1 and modelprice_type=$type  order by id desc limit 1";
		$modelpirce_name=$this->drcW->getone($sql);
		
		//end
		if(empty($res)){
			$sql="insert into ng_price_model_detail (modelid,uid,Mid,modelprice_type,Ismakepricy_men,modelprice,modeloldprice,modelprice_updown,modelpirce_name,modelpirce_name_sort,createtime,UserType,date) values ";
			for($i=1;$i<=count($price);$i++){
				$sql.="($modeldbs,$uid,$mid,$type,$privilege,'".$price[$i]."','".$oldprice[$i]."','";
				if($updown[$i]=='-1'){
					$sql.=-(($city_price[$i]/10)*10);
				}
				else{
					$sql.=(($city_price[$i]/10)*10);
				}
				//$sql.="','".$GLOBALS['save_city'][$citykey[$i-1]]."','".date("Y-m-d H:i:s",time())."'),";
			       $sql.="','$modelpirce_name','$i',NOW(),'".$UserType."','".$dates."'),";
			}
			$sql=substr($sql,0,-1);
			$this->drcW->execute($sql);
		}
		else{
			//echo "1111111111<pre/>";print_r($params);exit;
			
				for($i=1;$i<=count($price);$i++){
					$sql="update ng_price_model_detail set  UserType='".$UserType."', Ismakepricy_men='".$privilege."' , modelprice='".$price[$i]."' ,modeloldprice='".$oldprice[$i]."' ,modelprice_updown='";
					if($updown[$i]=='-1'){
						$sql.=-(($city_price[$i]/10)*10);
					}
					else{
						$sql.=(($city_price[$i]/10)*10);
					}
					$sql.="'	 where modelid=$modeldbs and	mid=$mid and uid=$uid and modelprice_type=$type and modelpirce_name='$modelpirce_name'  order by id desc limit 1 ";

					$this->drcW->execute($sql);							
				}
			}
			$url = "http://".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']."&show=1&iswj=1&save_dgx=1";
			include '/usr/local/www/libs/phpQuery/phpQuery.php';
			phpQuery::newDocumentFile($url);
			$content=pq("html")->html();
			$up_wq='update ng_price_model set pricycontent ="'.htmlentities($content,ENT_QUOTES,"UTF-8").'" where id="'.$modeldbs.'" and modeltype="'.$type.'"  ';
			$this->drcW->execute($up_wq);
		if($params['saveAll']){ 
			
			/* $date = date("Y-m-d H:i:s");
			$xunday1 ='';
			$nodatatitle = '暂无';
			if(date("d",strtotime($date))<11){
				$xunday1 = '01';
				$nodatatitle .= date("Y年m月")."上旬45#Ф50mm碳素结构钢数据";
			}elseif(date("d",strtotime($date))<21){
				$xunday1 = '11';
				$nodatatitle .= date("Y年m月")."中旬45#Ф50mm碳素结构钢数据";
			}else{
				$xunday1 = '21';
				$nodatatitle .= date("Y年m月")."下旬45#Ф50mm碳素结构钢数据";
			}
			$this_xun_start = date("Y-m-$xunday1 02:00:00",strtotime($date));
			$this_xun_end = date("Y-m-d H:i:s",strtotime($date));
			
			
			$endmarket='0';
			$sql="select * from app_license_privilege  where  privilege!='' and mc_type='1' ";
			$res=$this->_dao->query($sql);//res值得排序即是分管领导的排序
			
			foreach($res as $key => $value){
				$r=explode(',',$value['privilege']);
				$r=explode(',',$value['privilege']);
				if( $r[3]==1){
					echo $value['uid']."<br/>";
					$uids=$value['uid'];
					$sql2="select * from  ng_price_model_detail where uid='$uids'
					and modelid='$modeldbs' and modelprice_type=$type";
					$out=$this->drcW->query($sql2);
					// echo "<pre/>"; print_r($out);
					if(empty($out)){
						$endmarket='1';
						break;
					}
				}
			} */
			
			
			if($endmarket!='1'){
			
			$url = "https://".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']."&show=1&save_dgx=1";
			include '/usr/local/www/libs/phpQuery/phpQuery.php';
			phpQuery::newDocumentFile($url);
			$content=pq("html")->html();
			$sql='update ng_price_model set pricycontent ="'.htmlentities($content,ENT_QUOTES,"UTF-8").'",ismakepricy=1 ,makepricytime=NOW()
					where id="'.$modeldbs.'" and modeltype="'.$type.'"  order by id desc limit 1 ';
			$this->drcW->execute($sql);
			goUrl($url);}else{
			 $url = $_SERVER['HTTP_REFERER'];
			 goUrl($url);
			
			
			
	}
		}
		if($params['saveone']){ //echo '12';
			//$url = $_SERVER['HTTP_REFERER']."&show=1";
			$url = $_SERVER['HTTP_REFERER'];
			goUrl($url);
		}
	}
	public function bcsc_xs($showtime,$curdate)
	{  	if($curdate!=''){
		
		$d=date("d",strtotime($curdate));
		$m=date($curdate);
	    }else{
		$d=date("d");
		$m=date('Y-m-d');
	    }
	    
		if($d !="1"){
			//本月数据
			$month=$showtime;
			$startdate=$showtime.'-'."01";
			$enddate=date("Y-m-$d",strtotime($showtime));
			//echo $enddate;
			//前月数据
			
			
		 	$last_showtime=date("Y-m",strtotime("$m -1 month"));
			$lastdate_s=date("Y-m-01",strtotime("$m -1 month"));
			$lastdate_e=date("Y-m-$d",strtotime("$m -1 month")); 
			
	
			// if($showxun=="上"){
			// $lastdate_e=date("Y-m-11",strtotime("m -1 month"));
			// }else if ($showxun=="中"){
			// $lastdate_e=date("Y-m-21",strtotime("m -1 month"));
			// }else{
			//print_r("$showxun");
			// $lastdate_e=date("Y-m-t",strtotime("m -1 month"));
			// }
			// echo $lastdate_e;
	
		}
		if($d=="1"){
	
			$month=date("Y-m",strtotime("$m -1 month"));
			//print_r($month);exit;
			$startdate=date("Y-m-01",strtotime("$m -2 month"));
			$enddate=date("Y-m-$d",strtotime("$m -1 month"));
			$last_showtime=date("Y-m",strtotime("$m -2 month"));
			$lastdate_s=date("Y-m-01",strtotime("$m -2 month"));
			$lastdate_e=date("Y-m-$d",strtotime($month) -3600*24);
			$this->assign("last_date",$month);
			$this->assign("last_tdate",date("Y-m",strtotime("$m -2 month")));
	
		}
	
		$sql="select Value from ng_data_table where DataMark ='Ngdg0011' and dta_ym='".$month."'";
		$res_jhl=$this->drcW->query($sql);
		//print_r($sql);
		//$sql_xsl="select sum(Value) from ng_data_table where DataMark ='Ngdg0012' and dta_ym >='".$startdate."' and dta_ym <='".$enddate."'";
		$sql_xsl="select Value from ng_data_table where DataMark ='Ngdg0012' and dta_ym >'".$startdate."' and dta_ym <'".$enddate."' order by  dta_ym desc limit 1";
		$res_xsl=$this->drcW->getRow($sql_xsl);
		//print_r($sql_xsl);exit;
		$sql_last="select Value from ng_data_table where DataMark ='Ngdg0011' and dta_ym='".$last_showtime."'";
		$last_jhl=$this->drcW->query($sql_last);
		//print_r($sql_last);
		//print_r($sql_last);
		// $last_xsl="select sum(Value) from ng_data_table where DataMark ='Ngdg0012' and dta_ym >='".$lastdate_s."' and dta_ym <='".$lastdate_e."'";
		// $last_xslr=$this->drc->query($last_xsl);
		$last_xsl="select Value from ng_data_table where DataMark ='Ngdg0012' and dta_ym >'".$lastdate_s."' and dta_ym <'".$lastdate_e."' order by  dta_ym desc limit 1";
		$last_xslr=$this->drcW->getRow($last_xsl);
		//print_r($last_xsl);exit;
		$jhl=$res_jhl[0]['Value'];
		$jhl_last=$last_jhl[0]['Value'];
		$xsl=$res_xsl['Value'];
		//print_r($jhl);
		//print_r($xsl);
		$xsl_last=$last_xslr['Value'];
	
	
		$this->assign("res_jhl",$jhl);
		$this->assign("res_xsl",$xsl);
		$this->assign("last_jhl",$jhl_last);
		$this->assign("last_xslr",$xsl_last);
		if($jhl!=0) {
			$ddl=round(($xsl/$jhl*100),2);
		} else {
			$ddl=0;
		}
		if($jhl_last!=0) {
			$last_ddl=round($xsl_last/$jhl_last*100,2);
		} else {
			$last_ddl=0;
		}
		if($jhl_last!=0)
		$zf_jhl=round($jhl/$jhl_last*100-100,2);
		else {
			$zf_jhl=0;
		}
		if($xsl_last!=0)
		$zf_xsl=round($xsl/$xsl_last*100-100,2);
		else {
			$zf_xsl=0;
		}
		$this->assign("ddl",$ddl);
		$this->assign("last_ddl",$last_ddl);
		$this->assign("zf_jhl",$zf_jhl);
		$this->assign("zf_xsl",$zf_xsl);
		$this->assign("date",$d);
	
		return $ddl;
	}
	public function djjy($start,$end,$ddl,$curdate){
		//print_r("00000");exit;查询本期报价
		if($curdate!=''){
			
		
		}else{
		
			$curdate=date("Y-m-d");
		}
		$sql = "SELECT the_price_tax,changerate_tax FROM   `steelprice_info`  where variety='热轧带钢'  and gcid='1163' and ftime>= '".$end."' and ftime <= '".$curdate."'  and is_show=2 order by id desc limit 1";
		//echo $start;echo $end;
	
		$res_tjg=$this->gcdao->query($sql);
		//print_r($res_tjg);exit;
		$the_price_tax=$res_tjg[0]['the_price_tax'];
		$changerate_tax=$res_tjg[0]['changerate_tax'];
		//$date=date("Y-m-d",(strtotime($end) - 3600*24));
		$date1=$start." "."00:00:00";
		$date2=$end." "."23:59:59";
		//print_r($d1);exit;
		//$the_price_tax='';
		//$changerate_tax='';如果本期报价为空的话带入算法预测
		if($the_price_tax==""){
			//echo '11';exit;
			$sql="select round(avg(price),0) as price from marketconditions where mastertopid='1260193' and mconmanagedate > '".$date1."' and mconmanagedate < '".$date2."'";
			//print_r($sql);exit;
			$res_tjg=$this->maindao2->getRow($sql);
			//print_r($res_tjg);exit;
			//$res_tjg=end($res_tjg_arr);
			//前一旬本期价格
			//$sql_id="select * from steelprice_base where `steel_id`='122' and `steel_name`='淮钢'ORDER BY `id` DESC";
			$sql_last = "SELECT the_price_tax,changerate_tax FROM   `steelprice_info`  where variety='热轧带钢'  and gcid='1163' and ftime< '".$end."' and ftime >= '".$start."' and is_show=2  order by id desc limit 1";
			
			// print_r($sql_last);exit;
			$res_tjg_last=$this->gcdao->query($sql_last);
			// print_r($res_tjg_last);
			// echo $res_tjg_last[0]['the_price_tax'];
			$the_price_tax_last=floor($res_tjg_last[0]['the_price_tax']);
				
			$the_price_tax=floor(($res_tjg['price']/100)+0.7)*100;
			//$the_price_tax=$the_price_tax*100;
			//print_r($the_price_tax);exit;
				
			//print_r($res_tjg);exit;		
			//print_r($res_tjg_last);exit;
			//print_r($the_price_tax_last);exit;
			//print_r($res_tjg_last);
			$changerate_tax=$the_price_tax-$the_price_tax_last;
		}
		$arr=$this->bxundj($changerate_tax,$ddl,$curdate);
		$arr['the_price_tax']=$the_price_tax;
		$arr['changerate_tax']=$changerate_tax;
		$this->assign("the_price_tax",$the_price_tax);
		$this->assign("changerate_tax",$changerate_tax);
		return $arr;
	}
	public function bxundj($changerate_tax,$ddl,$curdate){
	//echo $changerate_tax.'----'.$ddl.'----'.$curdate;
		// $date;
	if($curdate!=''){
		$d=date("d",strtotime($curdate));
		$t=date("Y-m-d",strtotime($curdate));
	    }else{
		$d=date("d");
		$t=date("Y-m-d");
	    }
	   
		if($d<11){
			
			$date=date("Y-m-21",strtotime("$t -1 month"));
			$dateend=date("Y-m-31",strtotime("$t -1 month"));
			$zd=$changerate_tax;
		}
		if($d<21 &&$d>10){
			$date=date("Y-m-01",strtotime($t));
			$dateend=date("Y-m-10",strtotime("$t"));
			if($ddl<30){
				$zd=(float)$changerate_tax-20;
			}
			if($ddl<50 && $ddl>=30){
				$zd=$changerate_tax;
			}
			if( $ddl>=50){
				$zd=(float)$changerate_tax+20;
			}
				 
		}
		if($d<=31 && $d>20){
				
			$date=date("Y-m-11",strtotime($t));
			$dateend=date("Y-m-20",strtotime("$t"));
			if($ddl<60){
	
				//	print_r(date("d"));
				$zd=$changerate_tax-50;
				//print_r($ddl);
			}
			if($ddl<70 && $ddl>=60){
				$zd=$changerate_tax-20;
			}
			if( $ddl>=70 && $ddl<80 ){
				$zd=$changerate_tax;
			}
			if($ddl>=80){
				$zd=$changerate_tax+20;
			}
		}
		$sql="select Value from ng_data_table where DataMark ='Ngdg0002' and dta_ym >='".$date."'  and dta_ym <='".$dateend."' order by dta_ym desc limit 1";
		//echo $sql;
		$ngyg=$this->drcW->getone($sql);
		//print_r($res);
		
		$moxdj=$zd+$ngyg;
		$this->assign("moxdj",$moxdj);
		$this->assign("zd",$zd);
		$dj_arr=array('moxdj'=>$moxdj,'zd'=>$zd);
		return $dj_arr;
	}
	
} 

?>