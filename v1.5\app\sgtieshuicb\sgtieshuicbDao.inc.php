<?php
class  sgt<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> extends Dao{
	public function __construct($writer){
		 parent::__construct( $writer );
	}
	//通过guid取得用户信息
	public function get_userinfo_byguid($GUID){
		$sql = "select * from app_session_temp where  GUID ='$GUID'  order by  LoginDate DESC  limit 1";
		return $this->getRow($sql);
	}
	
	//获取用户权限和用户名
	public function get_license_privilege($Uid){
		$sql = "select * from app_license,app_license_privilege where  app_license.id=app_license_privilege.lid and app_license_privilege.Uid='$Uid' and app_license_privilege.mc_type=2 limit 1";
		return $this->getRow($sql);
	}

	 //获取
	public function get_ChengBenParameter($where=""){
		$sql = "select * from sg_ChengBenParameter where 1 {$where} order by ndate DESC,id DESC limit 1";
        //echo $sql;
		return $this->getRow($sql);
	}
	
	public function get_ChengBenParameter2($where=""){
		$sql = "select * from sg_ChengBenParameter2 where 1 {$where} order by ndate DESC,id DESC limit 1";
        //echo $sql;
		return $this->getRow($sql);
	}
	
	public function get_ChengBenParameter3($where=""){
		$sql = "select * from sg_ChengBenParameter3 where 1 {$where} order by ndate DESC,id DESC limit 1";
        //echo $sql;
		return $this->getRow($sql);
	}
	
	public function get_ChengBenParameter4($where=""){
		$sql = "select * from sg_ChengBenParameter4 where 1 {$where} order by ndate DESC,id DESC limit 1";
        //echo $sql;
		return $this->getRow($sql);
	}
	
	public function getAll($type,$start,$per){
		$sql = "select * from sg_ChengBenParameter where Type='".$type."' and isdel=0 order by ndate DESC,id DESC limit $start,$per";
        //echo $sql;
		return $this->query($sql);
	}
	
	public function getAll2($type,$start,$per){
		$sql = "select * from sg_ChengBenParameter2 where Type='".$type."' and isdel=0 order by ndate DESC,id DESC limit $start,$per";
        //echo $sql;
		return $this->query($sql);
	}
	
	public function getAll3($type,$start,$per){
		$sql = "select * from sg_ChengBenParameter3 where Type='".$type."' and isdel=0 order by ndate DESC,id DESC limit $start,$per";
        //echo $sql;
		return $this->query($sql);
	}

	public function getAll4($type,$start,$per){
		$sql = "select * from sg_ChengBenParameter4 where Type='".$type."' and isdel=0 order by ndate DESC,id DESC limit $start,$per";
        //echo $sql;
		return $this->query($sql);
	}

	//获取sg_ChengBenParameter表信息
	public function get_sg_ChengBenParameter($Date,$Type,$mc_type){
		$sql = "select * from sg_ChengBenParameter where  ndate <='$Date' and  Type ='$Type' and isdel ='0'  and mc_type='".$mc_type."' order by ndate desc,id desc  limit 1";
		//echo $sql;
		return $this->getRow($sql);
	}
	//获取
	public function get_sg_ChengBenParameter2($Date,$Type,$mc_type){
		$sql = "select * from sg_ChengBenParameter2 where  ndate <='$Date' and  Type ='$Type' and isdel ='0'  and mc_type='".$mc_type."' order by ndate desc,id desc  limit 1";
		//echo $sql;
		return $this->getRow($sql);
	}
	//获取ng_ChengBenParameter3表信息
	public function get_sg_ChengBenParameter3($Date,$Type,$mc_type){
		$sql = "select * from sg_ChengBenParameter3 where  ndate <='$Date' and  Type ='$Type' and isdel ='0'  and mc_type='".$mc_type."' order by ndate desc,id desc  limit 1";
		//echo $sql;
		return $this->getRow($sql);
	}
	//获取ng_ChengBenParameter表信息
	public function get_sg_ChengBenParameter4($Date,$Type){
		$sql = "select * from sg_ChengBenParameter4 where  ndate <='$Date' and  type ='$Type' and isdel ='0' order by ndate desc,id desc  limit 1";
		//echo $sql;
		return $this->getRow($sql);
	}
	//获取今日市场价格
	public function get_marketconditions_price_6($topictures,$mconmanagedate){
		$sql = "select price,topicture,mconmanagedate from marketconditions where topicture in (" . $topictures . ")  AND mconmanagedate >'" . $mconmanagedate . " 00:00:00' AND mconmanagedate <'" . $mconmanagedate . "  23:59:59'  order by topicture asc";
		$result = $this->query( $sql );
		
		foreach ( $result as $key => $value ){
			if (strstr ( $value ['price'], "-" )){
				$avgprice = explode ( "-", $value ['price'] );
				$value ['price'] = round ( ($avgprice ['0'] + $avgprice ['1']) / 2, 2 );
			}
			$tprice [$value['topicture']] = $value ['price'];
		}
		return $tprice;
	}
	
	//获取今日市场价格
	public function get_marketconditions_price_7($topictures,$mconmanagedate){
		$sql = "select price,mastertopid,mconmanagedate from marketconditions where mastertopid in (" . $topictures . ")  AND mconmanagedate >'" . $mconmanagedate . " 00:00:00' AND mconmanagedate <'" . $mconmanagedate . "  23:59:59'  order by mastertopid asc ";
		$result = $this->query( $sql );
		foreach ( $result as $key => $value ){
			if (strstr ( $value ['price'], "-" )){
				$avgprice = explode ( "-", $value ['price'] );
				$value ['price'] = round ( ($avgprice ['0'] + $avgprice ['1']) / 2, 2 );
			}
			$tprice [$value['mastertopid']] = $value ['price'];
		}
		return $tprice;
	}

	public function getpricebypricestr($pricestr,$date)
	{
		//echo $time;
		$idnumber=explode(',',$pricestr);
		//echo count($idnumber);
		$six=$seven=array();
		foreach($idnumber  as $id ){
			if (strlen ( $id ) == 6){//判断id 的字符长度 
				if(!in_array($id ,$six)){
					$six[]= $id ; 			
				} 							
			} 
			if (strlen ($id ) == 7) {
				if(!in_array($id,$seven)){
					$seven[]= $id;
				} 
			}
		}
		$sixid_str=implode("','",$six);
 		$sevenid_str=implode("','",$seven);
		$mconmanagedate.="(mconmanagedate>='".$date." 00:00:00' and mconmanagedate<='".$date." 23:59:59')";
		if($sixid_str!=''){
			//if(strtotime('2022-03-22')<strtotime($date))
			//{
				$PriceListSQL=" select marketconditions.price,marketconditions.topicture, marketconditions.mconmanagedate from marketconditions
				where $mconmanagedate and  marketconditions.topicture in ('$sixid_str')";
				
			// }
			// else
			// {
			// 	$PriceListSQL=" select marketconditions.pricemk price,marketconditions.topicture, marketconditions.mconmanagedate from marketconditions
			// 	where $mconmanagedate and  marketconditions.topicture in ('$sixid_str')";
			// }
			
 		}
 		if($sevenid_str!=''){
			
 		  	$PriceListSQL=" select marketconditions.price,marketconditions.mastertopid as topicture, marketconditions.mconmanagedate from marketconditions
 		  	where $mconmanagedate and  marketconditions.mastertopid in ('$sevenid_str')";
			
 		}
 		if($sixid_str!=''&&$sevenid_str!=''){
			//if(strtotime('2022-03-22')<strtotime($date))
		   //{ 
			   $PriceListSQL=" select marketconditions.price,marketconditions.topicture, marketconditions.mconmanagedate from marketconditions where $mconmanagedate and  marketconditions.topicture in ('$sixid_str') UNION( select marketconditions.price,marketconditions.mastertopid as topicture,marketconditions.mconmanagedate from marketconditions where $mconmanagedate  and marketconditions.mastertopid in('$sevenid_str'))";
	 		//}
		   //else
		   //{
			//$PriceListSQL=" select marketconditions.pricemk price,marketconditions.topicture, marketconditions.mconmanagedate from marketconditions where $mconmanagedate and  marketconditions.topicture in ('$sixid_str') UNION( select marketconditions.price,marketconditions.mastertopid as topicture,marketconditions.mconmanagedate from marketconditions where $mconmanagedate  and marketconditions.mastertopid in('$sevenid_str'))";
		  
			//} 
		}
		//echo $PriceListSQL;
		$PriceList= $this->query( $PriceListSQL ); 
		$dataarr =array();//数据数组
		$datearr=array();//日期数组
		foreach($PriceList  as $v )
		{
			if(strstr($v['price'],"-")){
				$avgprice = explode("-",$v['price']);
				$v['price'] = round(($avgprice['0']+$avgprice['1'])/2,2);
			}
			$dataarr[$v['topicture']]=  $v['price'];

		}
		return $dataarr;
	}
	//获取废钢指数
	public function get_shpi_material($topicturestr,$Date){
		$idnumber=explode(',',$topicturestr);
		$sql = "select mindex,price,dateday,topicture from shpi_material where topicture in ('".implode("','",$idnumber)."') and dateday='" . $Date . "'";
		$PriceList= $this->query( $sql ); 
		$dataarr =array();//数据数组
		foreach($PriceList  as $v )
		{
			$dataarr[$v['topicture']]=  round($v['price']);
		}
		return $dataarr;
	}
	//获取研究中心贴现率
	public function get_drc_hbh_txl($Date){
		$sql = "SELECT * FROM busbankrate WHERE   `rdate` <='$Date' order by rdate desc  limit 1";
		return $this->getRow($sql);
	}
	//获取研究中心汇率
	public function get_drc_rmb_hl($Date){
		$sql = "SELECT * FROM rmbrate WHERE   `rdate` <='$Date' order by rdate desc  limit 1";
		return $this->getRow($sql);
	}
	//获取钢之家税率
	public function get_master_cntaxrate($Date,$rtype){
		$sql = "SELECT rvalue FROM cntaxrate WHERE   `rdate` <='$Date' and status=1 and rtype='$rtype' and isdel=0 order by rdate desc  limit 1";
		return $this->getOne($sql);
	}
	//根据ng_ModifyChengBenParameterTask表信息看成本指数是否可以计算
	public function get_can_calc(){
		$sql = "select * from sg_ModifyChengBenParameterTask where  status !=2 and  type =1 order by pid desc ";
		return $this->getRow($sql);
	}
	public function get_can_calc1(){
		$sql = "select * from sg_ModifyChengBenParameterTask where  status !=2 and  type =2 order by pid desc ";
		return $this->getRow($sql);
	}
	//获取研究中心汇率
	public function get_drc_sg_data_table($Date,$where){
		$sql = "SELECT dta_6 FROM sg_data_table WHERE   `dta_ym` <='$Date' and dta_1='螺纹钢' and dta_type='SGtjtz' $where  order by dta_ym desc  limit 1";
		//echo $sql;
		return $this->getOne($sql);
	}
	public function gethjdianfei_bytype($date,$type)
    {
        $type_arr = array(
            "6"=>array("city"=>"江苏"),
            "7"=>array("city"=>"四川"),
            "21"=>array("city"=>"江苏"),
            "23"=>array("city"=>"江苏"),
            "26"=>array("city"=>"四川")
        );
        $sql="SELECT dta_4,round(dta_6,4) dta_6,round(dta_8,4) dta_8 FROM `data_table` WHERE dta_type = 'Java-qydlgdjg' AND dta_1 = '".$type_arr[$type]['city']."' and dta_ym<='".$date."' and dta_ym>='".date('Y-m-d',strtotime($date)-86400*180) ."'  ORDER BY  `dta_ym` DESC LIMIT 1";
        $GYJSDJ= $this->getRow( $sql );
        return $GYJSDJ;
    }
	

}