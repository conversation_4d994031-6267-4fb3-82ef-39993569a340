<?php
//header('Content-Type:text/html;charset=gb18030');
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );

class dbmarketpriceController extends AbstractController{
  
  public function __construct(){
    parent::__construct();
	$this->_action->setDao( new dbmarketpriceDao("91R") );
	//$this->_action->drcdao=new dbmarketpriceDao( 'DRCW',"DRC" );
	$this->_action->drcdao=new dbmarketpriceDao( "DRC" );
	$this->_action->t1Dao=new dbmarketpriceDao("MAIN");
  }

	public function v_index(){
		$this->_action->index( $this->_request );
	}
	 
	public function v_cgzxrb(){
		$this->_action->cgzxrb( $this->_request );
	}
	
	
}
?>