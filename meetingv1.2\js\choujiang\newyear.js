function qkong(k){
	if(window.confirm("确定清空中奖数据吗?")){
		console.log(localStorage[k+'temp'+mtid]);
		if(localStorage[k+'temp'+mtid]!=null){
			localStorage.removeItem(k+"temp"+mtid);
		}
		if(localStorage[k+'qxcj'+mtid]!=null){
			localStorage.removeItem(k+"qxcj"+mtid);
		}
		console.log(localStorage[k+'djc'+mtid]);
		if(localStorage[k+'djc'+mtid]!=null){
			localStorage.removeItem(k+"djc"+mtid);
		}
	}
}


function qkongall(){
	if(window.confirm("确定清空所有奖项中奖数据吗?")){
		for(var i=1; i<6;i++){
			if(localStorage[i+'temp'+mtid]!=null){
				localStorage.removeItem(i+"temp"+mtid);
			}
			if(localStorage[i+'qxcj'+mtid]!=null){
				localStorage.removeItem(i+"qxcj"+mtid);
			}
			console.log(localStorage[i+'djc'+mtid]);
			if(localStorage[i+'djc'+mtid]!=null){
				localStorage.removeItem(i+"djc"+mtid);
			}
		}
		alert("清空成功");
	}
}


function tuis(k){
	if(localStorage[k+'temp'+mtid]!=null && localStorage[k+'temp'+mtid]!="[]"){
		var storedNames=localStorage[k+'temp'+mtid];
		storedNames=JSON.parse(storedNames);
		var arr = new Array( );
		for(var i=0;i<storedNames.length;i++ ){
			var writer={"uid":storedNames[i]["uid"],"PersonSign":storedNames[i]['PersonSign'],"Mid":storedNames[i]['Mid'],"awardname":storedNames[i]['awardname']}; 
			arr.push(writer);
		}
		var awardStatus=1;
		if(k==2||k==3){
			awardStatus=0;
		}
		arr=JSON.stringify(arr);
		$.ajax({
			type: "post",
			url: "meeting.php?action=pushaward",
			data: {mtid:mtid,awardtype:k,Status:awardStatus, awarddata:Base64.encode(arr)},
			dataType: "jsonp",
			success: function (response) {
				if (response.Success == 1) {
					alert(response.Message);
				} else {
					alert(response.Message);
				}
			},
			error: function () {
			   alert("失败");
			}
		});
			
	}else{
		alert("无数据");
	}
}


function tuisall(){
	for(var t=1; t<6;t++){
		if(localStorage[t+'temp'+mtid]!=null && localStorage[t+'temp'+mtid]!="[]"){
			var storedNames=localStorage[t+'temp'+mtid];
			storedNames=JSON.parse(storedNames);
			var arr = new Array( );
			for(var i=0;i<storedNames.length;i++ ){
				var writer={"uid":storedNames[i]["uid"],"PersonSign":storedNames[i]['PersonSign'],"Mid":storedNames[i]['Mid'],"awardname":storedNames[i]['awardname']}; 
				arr.push(writer);
			}
			arr=JSON.stringify(arr);
			var awardStatus=1
			if(t==2||t==3){
				awardStatus=0;
			}
			$.ajax({
				type: "post",
				url: "meeting.php?action=pushaward",
				data: {mtid:mtid,awardtype:t,Status:awardStatus, awarddata:Base64.encode(arr)},
				dataType: "jsonp",
				success: function (response) {
					if (response.Success == 1) {
						console.log(response.Message);
					} else {
						console.log(response.Message);
					}
				},
				error: function () {
					console.log("失败");
				}
			});
				
		}
	}
	alert("推送成功");
}