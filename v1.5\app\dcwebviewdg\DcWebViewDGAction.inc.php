<?php 
// require_once('../../../../steelconf_v3/debug.php');
require_once ("/etc/steelconf/config/isholiday.php");
class DcWebViewDGAction extends AbstractAction
{ 
    public function __construct()
    {
        parent::__construct();
    } 
	 public function index($params)
    {	
	//echo "1111";
	
		// $firstday = date('Y-m-01');
		// $lastday = date('d', strtotime("$firstday +1 month -1 day"));//上个月最后一天
		// $showtime=date("Y-m");
		// $showxun=date("d");
		// $lastxunsd="20";//前一旬的开始日
		// $lastxuned=$lastday;//前一旬的结束日
		$today=date("Y-m-d");
		if($params['curdate'])
		{
			if(checkdate(date("m",strtotime($params['curdate'])),date("d",strtotime($params['curdate'])),date("Y",strtotime($params['curdate']))))
			{
				$today=$params['curdate'];
			}
			
			
		}
		$today1=$today;
		$showtime=date("Y-m",strtotime($today));
		$showxun=date("d",strtotime($today));
		
		//$showtime=date("Y-m");
		//$showxun=date("d");
		
		if($showxun > "10" && $showxun < "21"){
			$showxun="中";
			$lastxunsd="01";
			$lastxuned="10";
			$lastM=$showtime;
			$datezd='11';
			$today=date("Y-m-",strtotime($today))."11";
		
		}else if($showxun <="31" && $showxun>"20"){
			$showxun="下";
			$lastxunsd="11";
			$lastxuned="20";
			$lastM=$showtime;
			$datezd='21';
			$today=date("Y-m-",strtotime($today))."21";
		}else{
			$lastM=date('Y-m', strtotime(" -1 month",strtotime($today)));
			$showxun="上";
			$lastxunsd="21";
			$lastxuned=date('t',strtotime('last month',strtotime($today)));
			$today=date("Y-m-",strtotime($today))."01";
             $datezd='01';
		}
		
		$s="00:00:00";
		$e="23:59:59";
		$date1=$lastM."-".$lastxunsd." ".$s;
		$date2=$lastM."-".$lastxuned." ".$e;
       
	   
	   //file_put_contents('/tmp/xiangbin.txt','bcsc_xs 开始'.date('y-m-d h:i:s',time())."\n",FILE_APPEND);
		$ddl=$this->bcsc_xs($showtime,$today);
	
	
		$this->bxunshijg($date1,$date2);//本旬市场价格
		
		
		
		$showtime_last=date("Y-m",strtotime("-1 month",strtotime($today)));
		$this->assign("showtime",date("Y年n月",strtotime($showtime)));
		$this->assign("showtime_last",date("Y年n月",strtotime($showtime_last)));
		$this->assign("showxun",$showxun);
		$this->assign("lastxunsd",$lastxunsd);
		$this->assign("lastxuned",$lastxuned);
		$this->assign("lastM", date("Y年n月",strtotime($lastM)));
		//$this->assign("d",$da_arr);
		//$arr=$this->djjy($lastM."-".$lastxunsd,$lastM."-".$lastxuned,$ddl);//定价建议
		//print_r($arr);exit;
       // 保存数据
	   if($_GET['issave']==1)
	   {
	    $sql="insert into `ng_price_model`(date,modeltype,createtime) values('".date("Y-m-",strtotime($today)).$datezd."','5',NOW())";
	
	   $this->ngdao->execute($sql);
	   }
		 //$modelid=$this->ngdao->insert_id();
		
		// $moxdj=$arr['moxdj'];
		// $zd=$arr['zd'];
		// $ngyg=$moxdj-$zd;
		
		// $ins_xundj="insert into ng_price_model_detail(modelid,modelprice,modelprice_updown,modeloldprice,modelprice_type,	modelpirce_name,modelpirce_name_sort,Mid,uid,GUID,Ismakepricy_men,Ischecked,createtime) value('".$modelid."','".$moxdj."','".$zd."','".$ngyg."','5','2.0mmQ235热轧带钢基价','1','-1','-1','','0','1','".date("Y-m-d h:m:s")."')";
		// $this->ngdao->execute($ins_xundj);
	  $this->assign("params",$params);
	  
    } 

	//保存数据
	public function savexundj($params)
	{
		//print_r($params);exit;
		
		$today=date("Y-m-d");
		if($params['curdate'])
		{
			if(checkdate(date("m",strtotime($params['curdate'])),date("d",strtotime($params['curdate'])),date("Y",strtotime($params['curdate']))))
			{
				$today=$params['curdate'];
			}
			
			
		}
		
		
		
		$showxun=date("d",strtotime($today));
		if($showxun > "10" && $showxun < "21"){
			$showxun="中";	
		}else if($showxun <="31" && $showxun>"20"){
			$showxun="下";		
		}else{
			$showxun="上";
		}
		$title="旬带钢旬定价";
		$model=date("Y年n月",strtotime($today)).$showxun;
		$modeltitle=$model.$title;
		//$url = $_SERVER['SERVER_NAME'].$_SERVER['REQUEST_URI'];
		//$url = $_SERVER['HTTP_REFERER']."&is_title=1";
		//$url=$_SERVER['HTTP_REFERER'];
	
		$url = DC_URL.DCURL."/dcwebviewdg.php?view=index&issave=1&curdate=".$today;
	   
		//print_r( $_SERVER['SCRIPT_FILENAME']."view=index");
		include '/usr/local/www/libs/phpQuery/phpQuery.php';
		phpQuery::newDocumentFile($url);
		$html=pq("body")->html();
		//print_r($url);
		$modelcontent=htmlentities($html, ENT_QUOTES,"UTF-8");
		
		
		$sql="update `ng_price_model` set modeltitle='".$modeltitle."',modelcontent='".$modelcontent."'  where modeltype='5' order by id desc limit 1";
		$this->ngdao->execute($sql);
		echo "执行完成";
		//print_r($html);
		//print_r( $html);

	}
	public function bcsc_xs($showtime,$today)
	{
	$d=date("d",strtotime($today));	
	if($d !="1"){
		//本月数据
		$month=$showtime;	
		$startdate=$showtime.'-'."01";
		$enddate=date("Y-m-d",strtotime($today));

		//前月数据
		$last_showtime=date("Y-m",strtotime(" -1 month",strtotime($today)));
		$lastdate_s=date("Y-m-01",strtotime(" -1 month",strtotime($today)));
		$lastdate_e=date("Y-m-d",strtotime(" -1 month",strtotime($today)));
		
	}
	if($d=="1"){
		
		$month=date("Y-m",strtotime(" -1 month",strtotime($today)));
		//print_r($month);exit;
		$startdate=date("Y-m-01",strtotime(" -1 month",strtotime($today)));
		$enddate=date("Y-m-d",strtotime($today));
		$last_showtime=date("Y-m",strtotime(" -2 month",strtotime($today)));
		$lastdate_s=date("Y-m-01",strtotime(" -2 month",strtotime($today)));
		$lastdate_e=date("Y-m-d",strtotime(" -1 month",strtotime($today)));
		$this->assign("last_date", date("Y年n月",strtotime($month)));
		$this->assign("last_tdate",date("Y年n月",strtotime(" -2 month",strtotime($today))));
		
		$yf[]=date("n",strtotime(" -2 month",strtotime($today)));
		$yf[]=date("n",strtotime(" -1 month",strtotime($today)));
		$yf[]=date("n",strtotime($today));
		
	}
	else
	{
	    $yf[]=date("n",strtotime(" -1 month",strtotime($today)));
		$yf[]=date("n",strtotime($today));
		$yf[]=date("n",strtotime(" +1 month",strtotime($today)));
			
	}
		
		$sql="select Value from ng_data_table where DataMark ='Ngdg0011' and dta_ym='".$month."'";
		$res_jhl=$this->ngdao->query($sql);
		//$sql_xsl="select sum(Value) from ng_data_table where DataMark ='Ngdg0012' and dta_ym >='".$startdate."' and dta_ym <='".$enddate."'";
		$sql_xsl="select Value from ng_data_table where DataMark ='Ngdg0012' and dta_ym >'".$startdate."' and dta_ym <'".$enddate."' order by  dta_ym desc limit 1";
		$res_xsl=$this->ngdao->getRow($sql_xsl);
		//print_r($sql_xsl);exit;
		$sql_last="select Value from ng_data_table where DataMark ='Ngdg0011' and dta_ym='".$last_showtime."'";
		$last_jhl=$this->ngdao->query($sql_last);
	
		$last_xsl="select Value from ng_data_table where DataMark ='Ngdg0012' and dta_ym >'".$lastdate_s."' and dta_ym <'".$lastdate_e."' order by  dta_ym desc limit 1";
		$last_xslr=$this->ngdao->getRow($last_xsl);
	
		$jhl=$res_jhl[0]['Value'];
		$jhl_last=$last_jhl[0]['Value'];
		$xsl=$res_xsl['Value'];
		//print_r($jhl);
		//print_r($xsl);
		$xsl_last=$last_xslr['Value'];
		
		
		$this->assign("res_jhl",$jhl);
		$this->assign("res_xsl",$xsl);
		$this->assign("last_jhl",$jhl_last);
		$this->assign("last_xslr",$xsl_last);
		$ddl=$jhl?round(($xsl/$jhl*100),2):0;

		//print_r($ddl);
		$last_ddl=$jhl_last?round($xsl_last/$jhl_last*100,2):0;
		$zf_jhl=$jhl_last?round($jhl/$jhl_last*100-100,2):0;
		$zf_xsl=$xsl_last?round($xsl/$xsl_last*100-100,2):0;
		
		$this->assign("ddl",$ddl);
		$this->assign("last_ddl",$last_ddl);
		$this->assign("zf_jhl",$zf_jhl);
		$this->assign("zf_xsl",$zf_xsl);
		$this->assign("date",$d);
		
		
		
		$this->assign("yf",$yf);
	
		$sql_lastlast="select Value from ng_data_table where DataMark ='Ngdg0013' and dta_ym='".$month."'";
		$lastlast_jhl=$this->ngdao->getRow($sql_lastlast);
		//echo  $sql_lastlast;
		$this->assign("lastlast_jhl",$lastlast_jhl['Value']);
		
		//$lastlast_xsl="select Value from ng_data_table where DataMark ='Ngdg0014' and dta_ym >='".date("Y-m-01",strtotime(" +1 month",strtotime($startdate)))."' and dta_ym <'".date("Y-m-d",strtotime(" +1 month",strtotime($enddate)))."' order by  dta_ym desc limit 1";
		
		
		$lastlast_xsl="select Value from ng_data_table where DataMark ='Ngdg0014' and dta_ym >='".date("Y-m-01",strtotime($startdate))."' and dta_ym <'".($d=="1"?date("Y-m-01",strtotime($enddate)):date("Y-m-01",strtotime(" +1 month",strtotime($enddate))))."' order by  dta_ym desc limit 1";
		
		//echo $lastlast_xsl;  
		$lastlast_xslr=$this->ngdao->getRow($lastlast_xsl);
		$this->assign("lastlast_xslr",$lastlast_xslr['Value']);
		// echo  $dy.$sy.$xy;
		$lastlast_ddl=$lastlast_jhl['Value']?round($lastlast_xslr['Value']/$lastlast_jhl['Value']*100,2):0;
		$this->assign("lastlast_ddl",$lastlast_ddl);
		return $ddl;
	}
	
	public function bxunshijg($date1,$date2){
		

		$bxtjg_hz="('1260191','1260193','1260151','1261451','1261452','1260198')";//等12XXXXX有值要将 下面的3改为4
		

		
		$tmp_d1=date("j",strtotime($date1));
		//echo $tmp_d1;
		$titles=array();
		if($tmp_d1==11)
		{
			$date_start=date("Y-m-21 00:00:00",strtotime("-1 month ",strtotime($date2)));
			$date3=date("Y-m-01 00:00:00",strtotime($date2));
			$titles[]=date("n",strtotime("-1 month ",strtotime($date2)))."月下旬";
			$titles[]=date("n",strtotime($date1))."月上旬";
			$titles[]=date("n",strtotime($date1))."月中旬";
			$titles2=$titles;
			$titles2[]=date("n",strtotime($date1))."月下旬";
			
			$title1=date("Y年n月",strtotime("-1 month ",strtotime($date2)))."下旬至".date("Y年n月",strtotime($date2))."中旬";
		}
		else if($tmp_d1==21)
		{
			
			$date_start=date("Y-m-01 00:00:00",strtotime($date1));

			$date3=date("Y-m-11 00:00:00",strtotime($date1));
			$titles[]=date("n",strtotime($date1))."月上旬";
			$titles[]=date("n",strtotime($date1))."月中旬";
			$titles[]=date("n",strtotime($date1))."月下旬";
			$titles2=$titles;
			//echo $date2."555".date("n",strtotime("-1 month ",strtotime($date2)))."月上旬"; 
			$titles2[]=date("n",strtotime("+1 month ",strtotime($date1)))."月上旬";
			$title1=date("Y年n月",strtotime($date1))."上旬至".date("Y年n月",strtotime($date1))."下旬";
		}
		else
		{
			$date_start=date("Y-m-11 00:00:00",strtotime("-1 month ",strtotime($date2)));
			$date3=date("Y-m-21 00:00:00",strtotime("-1 month ",strtotime($date2)));
			$titles[]=date("n",strtotime("-1 month ",strtotime($date2)))."月中旬";
			$titles[]=date("n",strtotime("-1 month ",strtotime($date2)))."月下旬";
			$titles[]=date("n",strtotime($date1))."月上旬";
			$titles2=$titles;
			$titles2[]=date("n",strtotime($date1))."月中旬";
			$title1=date("Y年n月",strtotime("-1 month ",strtotime($date2)))."中旬至".date("Y年n月",strtotime($date2))."上旬";
		}
		
		//print_r($titles);
		
		$tjg_hzsql="select mconmanagedate,mastertopid,pricemk as price from marketconditions where mastertopid in ".$bxtjg_hz."and mconmanagedate > '".$date_start."' and mconmanagedate < '".$date2."'";
		
		$res_tjghz=$this->maindao->query($tjg_hzsql);

		foreach($res_tjghz as $tmp){
			
			if($tmp['mconmanagedate']<$date3)
			 {      
				 if($tmp['mconmanagedate']<'2018-06-20 00:00:00'&&$tmp['mastertopid']=='1260193')
				 {
					 $dg1[1260198][0][]=$tmp['price'];
				 }
				$dg1[$tmp['mastertopid']][0][]=$tmp['price'];
			}
			else if($tmp['mconmanagedate']>$date3&&$tmp['mconmanagedate']<$date1)
			{
				if($tmp['mconmanagedate']<'2018-06-20 00:00:00'&&$tmp['mastertopid']=='1260193')
				 {
					 $dg1[1260198][1][]=$tmp['price'];
				 }
				$dg1[$tmp['mastertopid']][1][]=$tmp['price'];
			}
			else
			{
				if($tmp['mconmanagedate']<'2018-06-20 00:00:00'&&$tmp['mastertopid']=='1260193')
				 {
					 $dg1[1260198][2][]=$tmp['price'];
				 }
				$dg1[$tmp['mastertopid']][2][]=$tmp['price'];
			}	
			

			
		}
		
		$sql_xsl="select Value,dta_ym from ng_data_table where DataMark ='Ngdg0001'   and dta_ym >'".$date_start."' and dta_ym <'".$date2."' ";
		$res_tjghz=$this->ngdao->query($sql_xsl);
		
		//echo $date3;
		foreach($res_tjghz as $tmp){
			//echo $tmp['Value'];
			if(strtotime($tmp['dta_ym'])<strtotime($date3))
			 {     
				$dg1['Ngdg0001'][0][]=$tmp['Value'];
			}
			else if(strtotime($tmp['dta_ym'])>=strtotime($date3)&&strtotime($tmp['dta_ym'])<strtotime($date1))
			{
				//echo $tmp['dta_ym'];
				$dg1['Ngdg0001'][1][]=$tmp['Value'];
			}
			else
			{
				//echo  $tmp['Value'];
				$dg1['Ngdg0001'][2][]=$tmp['Value'];
			}
		}			
		$sql_xsl="select Value,dta_ym from ng_data_table where DataMark ='Ngdg0004'   and dta_ym >'".date('Y-m-d',strtotime("-1 day ",strtotime($date_start)))."' and dta_ym <'".$date2."' ";
		$res_tjghz=$this->ngdao->query($sql_xsl);
		
		
		foreach($res_tjghz as $tmp){
			//echo $tmp['Value'];
			if(strtotime($tmp['dta_ym'])<strtotime($date3))
			 {     
				$dg1['Ngdg0004'][0][]=$tmp['Value'];
			}
			else if(strtotime($tmp['dta_ym'])>=strtotime($date3)&&strtotime($tmp['dta_ym'])<strtotime($date1))
			{
				
				$dg1['Ngdg0004'][1][]=$tmp['Value'];
			}
			else
			{
				//echo  $tmp['Value'];
				$dg1['Ngdg0004'][2][]=$tmp['Value'];
			}
		}		
		
	
		foreach($dg1 as $key=>$val)
		{
			$avg_lists[$key][0]='--'; 
			$avg_lists[$key][1]='--'; 
			$avg_lists[$key][2]='--'; 
			foreach($val as $key1=>$val1)
			{
			
			$avg_lists[$key][$key1]=round(array_sum($val1)/count($val1));
			}
            
          		
		}
		
		//钢之家预测
		$sql="select * from NgGZJForcast where Type='9' and CDate >='".$date1."' and  CDate <='".$date2."' order by CDate desc limit 1";
		
		$info=$this->maindao->getRow($sql);
		if($info)
		{
			if(abs($info['ZhangDie'])<DELTA){
				$info['ZhangDie']="持平";
			}else if($info['ZhangDie']>DELTA){
				$info['ZhangDie']='上涨'.$info['ZhangDie'].'元/吨';
			}else{
				$info['ZhangDie']="下跌".abs($info['ZhangDie']).'元/吨';
			}
			//print_r($info);
			
		}
		else
		{
			$info['Detail']="无";
		}
		$this->assign("info",$info);
		//钢之家预测
		
		
		
		
		
		
	
		$this->assign("titles",$titles);
		$this->assign("titles2",$titles2);
		$this->assign("avg_lists",$avg_lists);
		//print_r($avg_lists);
		//print_r($dg1);
		
		//$this->assign("d",$d);
		//数据插入定价表内
		//$ins_hzsql="insert into ng_price_model_detail set()";
		//$this->assign("res_tjghz_data",$res_tjghz_data);
		//$this->assign("start",$d[0]);
		//$this->assign("end",$d[count($d)-1]);
		$this->assign("title1",$title1);
		$this->gctj_hz($date_start,$date2,$date3,$date1);
	}
		//钢厂调价汇总
	public function gctj_hz($start_date,$end_date,$date3,$date1){
		
		$steel_id = "('151',154)";
      $steel_id_arr = array('151','154');
      $onlyid = "('c28fa7a993daeeea1d02fb9dac5dd52a','3b9aae95dd69a5450b444d60acb997a3')";
		 $sql = "select steel_id, steel_name, variety, specification, material, changerate_tax, the_price_tax, run_date, onlyid from steelhome_gc.steelprice_base join steelhome_gc.steelprice_info where steelprice_base.id = steelprice_info.sb_id and run_date >= '".date('Y-m-d',strtotime("-5 day ",strtotime($start_date)))."' and run_date <= '".date('Y-m-d',strtotime("+1 day ",strtotime($end_date)))."' and steelprice_base.is_show = 2 and steelprice_info.is_show = 2 and steelprice_info.onlyid in $onlyid and steel_id in $steel_id and changerate_tax!='' and the_price_tax!='' order by steel_id";
    $result = $this->gcdao->query($sql);
	//echo "<pre>";
	//print_r($sql);
	// print_r($result);
	 $changerate_tax=array();
    foreach ($result as $key => $value) {
		
		    if(strtotime($value['run_date'])<=strtotime($start_date))
			 {     
				$arr[$value['steel_id']][0]=$value;
			}
			else if(strtotime($value['run_date'])>strtotime($start_date)&&strtotime($value['run_date'])<=strtotime($date3))
			{
				$arr[$value['steel_id']][1]=$value;
			}
			else if(strtotime($value['run_date'])>strtotime($date3)&&strtotime($value['run_date'])<=strtotime($date1))
			{
				$arr[$value['steel_id']][2]=$value;
			}
			else
			{
				$arr[$value['steel_id']][3]=$value;
			}	
		
			// $arr[$value['steel_id']][$value['run_date']] = $value;
			// $arr[$value['steel_id']][$value['run_date']]['run_date'] = date("Y-m-d",strtotime($value['run_date']));
			// $arr[$value['steel_id']][$value['run_date']]['changerate_tax'] = $value['changerate_tax'];
			
			
    }
	$day=date("d",strtotime($end_date));
	//echo $day;
	$shijian=$day>19?date("Y-m-26 00:00:00",strtotime($end_date)):date("Y-m-26 00:00:00",strtotime("-1 month ",strtotime($end_date)));
	
	$steel_id = "('1193')";
      $steel_id_arr = array('1193');
      $onlyid = "('2f350a3b6beaf6f3f64404b90755b8d6')";
		 $sql = "select * from steelhome_gc.steelprice_base join steelhome_gc.steelprice_info where steelprice_base.id = steelprice_info.sb_id and run_date='".$shijian."' and steelprice_base.is_show = 2 and steelprice_info.is_show = 2 and steelprice_info.onlyid in $onlyid and steel_id in $steel_id and changerate_tax!='' and the_price_tax!='' order by steel_id";
   
	$result = $this->gcdao->getRow($sql);
	if($day<11)
	{
		$arr[1193][0]=$result['last_price_tax'];
		$arr[1193][1]=$result['the_price_tax'];
		$arr[1193][2]=$result['the_price_tax'];
		$arr[1193][3]=$result['the_price_tax'];
	}
	else if($day<21)
	{
		$arr[1193][0]=$result['last_price_tax'];
		$arr[1193][1]=$result['last_price_tax'];
		$arr[1193][2]=$result['last_price_tax'];
		$arr[1193][3]=$result['the_price_tax'];
	}
	else
	{
		$arr[1193][0]=$result['last_price_tax'];
		$arr[1193][1]=$result['last_price_tax'];
		$arr[1193][2]=$result['the_price_tax'];
		$arr[1193][3]=$result['the_price_tax'];
	}
	
		$this->assign("arr", $arr);

	}
	
	
	/*
	public function djjy($start,$end,$ddl){
		//print_r("00000");exit;
		$sql = "SELECT the_price_tax,changerate_tax FROM   `steelprice_info`  where variety='热轧带钢'  and gcid='15100' and ftime>= '".$end."' and ftime <= '".date("Y-m-d")."'";
		//print_r($sql);exit;
		$res_tjg=$this->gcdao->query($sql);
		$the_price_tax=$res_tjg[0]['the_price_tax'];
		$changerate_tax=$res_tjg[0]['changerate_tax'];
		//$date=date("Y-m-d",(strtotime($end) - 3600*24));
		$date1=$start." "."00:00:00";
		$date2=$end." "."23:59:59";
		//print_r($d1);exit;
		if($the_price_tax==""){
			$sql="select round(avg(price),0) as price from marketconditions where mastertopid='1260193' and mconmanagedate > '".$date1."' and mconmanagedate < '".$date2."'";
			//print_r($sql);exit;
			$res_tjg=$this->maindao->getRow($sql);
			//$res_tjg=end($res_tjg_arr);
			//前一旬本期价格
			//$sql_id="select * from steelprice_base where `steel_id`='122' and `steel_name`='淮钢'ORDER BY `id` DESC";
			$sql_last = "SELECT the_price_tax,changerate_tax FROM   `steelprice_info`  where variety='热轧带钢'  and gcid='151' and ftime< '".$end."' and ftime >= '".$start."'";
			//print_r($sql_last);exit;
			$res_tjg_last=$this->gcdao->query($sql_last);
			$the_price_tax_last=$res_tjg_last[0]['the_price_tax'];
			
			$the_price_tax=(($res_tjg['price']/100)+0.7)*100;
			//print_r($the_price_tax);
			
			//print_r($res_tjg);exit;
			$changerate_tax=$the_price_tax-$the_price_tax_last;
		}
		$arr=$this->bxundj($changerate_tax,$ddl);
		
		$this->assign("the_price_tax",$the_price_tax);
		$this->assign("changerate_tax",$changerate_tax);
		return $arr;
	}
	
	public function bxundj($changerate_tax,$ddl){
		
		$date;
		if(date("d")<11){
			$date=date("Y-m-21",strtotime("m -1 month"));
			$zd=$changerate_tax;
		}
		if(date("d")<21 && date("d")>10){
			$date=date("Y-m-01");
			if($ddl<30){
				$zd=$changerate_tax-20;
			}
			if($ddl<50 && $ddl>=30){
				$zd=$changerate_tax;
			}
			if( $ddl>=50){
				$zd=$changerate_tax+20;
			}
			
		}
		if(date("d")<=31 && date("d")>20){
			
			$date=date("Y-m-11");
			if($ddl<60){
				
			//	print_r(date("d"));
				$zd=$changerate_tax-50;
				//print_r($ddl);
			}
			if($ddl<70 && $ddl>=60){
				$zd=$changerate_tax-20;
			}
			if( $ddl>=70 && $ddl<80 ){
				$zd=$changerate_tax;
			}
			if($ddl>=80){
				$zd=$changerate_tax+20;
			}
		}
		$sql="select Value from ng_data_table where DataMark ='Ngdg0002' and dta_ym ='".$date."'";
		$res=$this->ngdao->query($sql);
		$ngyg=$res[0]['Value'];
		$moxdj=$zd+$ngyg;
		$this->assign("moxdj",$moxdj);
		$this->assign("zd",$zd);
		$dj_arr=array('moxdj'=>$moxdj,'zd'=>$zd);
		return $dj_arr;
	}*/

} 

?>