.page-break {
    page-break-before: always;  /* 强制分页 */
}

*{ font-family:Microsoft Yahei; line-height:24px; font-size:16px; margin:0; padding:0;}
.main{ width:96%; margin:10px auto;}
h1{ font-size:40px; line-height:60px; font-weight:bold; text-align:center;}
h2{ font-size:36px; line-height:58px; font-weight:bold; width: 96%;margin: 5px auto;text-indent:32px;}
h3{ font-size:34px; line-height:58px; font-weight:bold;  width:96%; margin:2px auto;text-indent: 32px;}
h4{ font-size:34px; line-height:55px; font-weight:bold;}
table{border-collapse:collapse; width:96%; margin:0 auto;}
table *{ font-size:14px;margin-top: 5px;}
table td{ 
    border-bottom-color:#000000; 
    border-bottom-style:solid; 
    border-bottom-width:0.75pt; 
    border-left-color:#000000; 
    border-left-style:solid; 
    border-left-width:0.75pt; 
    border-right-color:#000000; 
    border-right-style:solid; 
    border-right-width:0.75pt; 
    border-top-color:#000000; 
    border-top-style:solid; 
    border-top-width:0.75pt; 
    padding:5px;
    vertical-align:middle;
    margin:0pt; 
    text-align:center;
    border-collapse:collapse;
}
table thead td{ background:#E6E8EB;border-collapse:collapse;}
p{ width:96%; margin:0 auto;line-height: 58px;font-size: 36px;text-align: justify !important;}
span{ width:96%; margin:0 auto;line-height: 28px;text-align: justify !important;}
tbody tr:hover td{ background:#F3F4F6;}

.data_photo{ display:flex; flex-wrap: wrap;}
.data_photo div{ width:50%; display:flex;}
.data_photo table{ border-style: none !important;}
.data_photo table td{ border-style: none !important;}
.data_photo div img{ margin:20px auto;}
/* 添加打印优化样式 */
@media print {
    .main {
        width: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    table {
        width: 100% !important;
        page-break-inside: avoid;
    }

    img {
        max-width: 100%;
        height: auto%;
        /*page-break-before: always; !* 强制分页前显示图片 *!*/
        page-break-inside: avoid; /* 避免图片在分页时被压缩或截断 */
    }

    .no-print {
        display: none !important;
    }

    /* h1 { font-size: 24px; }
    h2 { font-size: 20px; }
    h3 { font-size: 18px; }
    h4 { font-size: 16px; } */

    /* 确保表格不会被分页截断 */
    tr { page-break-inside: avoid; }

    /* 优化页面边距 */
    @page {
        margin: 1cm;
    }
}