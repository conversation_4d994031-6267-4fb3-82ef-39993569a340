<?php
require_once("/usr/local/www/libs/vendor/autoload.php");
include_once( "/etc/steelconf/env/dc_steelhome_env.php" );
//基本配置
define( "BASE_DIR", dirname( __FILE__ ) );
define( "FRAME_DIR", BASE_DIR . "/../../libs/frame" );
define( "FRAME_LIB_DIR", FRAME_DIR . "/libs" );
define( "APP_DIR", BASE_DIR . "/app" );
define( "WEB_DIR", "/admincpv2" );
define( "COMMON_JS_DIR",  WEB_DIR . "/js" );
define( "DEBUG_MODE", true );
define( "FRAME_VERSION", "1.0" );

// 配置SMARTY

define( "SMARTY_MODULE_DIR", FRAME_LIB_DIR . "/smarty" );
define( "SMARTY_COMPILE_DIR" , COMPILE_DIR . "/meetingv1.2" );
define( "SMARTY_CACHE_DIR", SMARTY_MODULE_DIR . "/cache" );
define( "SMARTY_TEMPLATE_DIR", BASE_DIR . "/html" );

include_once( "/etc/steelconf/sthframe/steelhome_db_config.php" );
include_once( "/etc/steelconf/sthframe/steelhome_smsdb_config.php" );
/*
$HOST_NAME_91W = "***********";
$HOST_PORT_91W = "4306";
$DBSE_NAME_91W = "steelhome";
$USER_NAME_91W = "root2";
$USER_PAWD_91W = "123456";
*/

// 配置MYSQL 默认连接

define( "HOST_NAME_91W", $HOST_NAME_91W );
define( "HOST_PORT_91W", $HOST_PORT_91W );
define( "USER_NAME_91W", $USER_NAME_91W );
define( "USER_PASSWORD_91W", $USER_PAWD_91W );
define( "DATABASE_NAME_91W", $DBSE_NAME_91W );

/** 98的短信数据库 3306  **/
define( "HOST_NAME_98SMW", $HOST_NAME_98SMR);
define( "HOST_PORT_98SMW", $HOST_PORT_98SMR);
define( "USER_NAME_98SMW", $USER_NAME_98SMR);
define( "USER_PASSWORD_98SMW", $USER_PAWD_98SMR);
define( "DATABASE_NAME_98SMW", $DBSE_NAME_98SMR);

$GLOBALS['MEET_ID'] = array("17","18","23","88624","24","27");//钢之家指定的管理员ID
define( "STEELHOME_SITE", "https://www.steelhome.cn/" ); //接入域名

//配置memcache
define( "USE_MEMCACHE", false );
//define( "MEMCACHE_SERVER", $MEMCACHE_SERVER );
//define( "MEMCACHE_PORT", $MEMCACHE_PORT );

define( "WEBOA_DOMIN", "http://************" );

include_once( BASE_DIR . "/project.conf.php" );
//include_once($_SERVER['DOCUMENT_ROOT']."/admin/message/checksession.php");

?>
