<?php
include_once( FRAME_LIB_DIR ."/action/AbstractAction.inc.php" );
$GLOBALS['newarray']=array();
$GLOBALS['power']=array('决策人'=>'1','建议人'=>'2','市场部'=>'3','游客'=>'4');
$GLOBALS['privilege']=array(
    "碳结钢日定价权限",
    "碳结钢旬定价权限",
    "螺纹日定价权限",
    "带钢日定价权限",
    "带钢旬定价权限",
    "中板日定价权限",
    "中板旬定价权限",
);


class  xgquanxAction extends AbstractAction
{
    public function __construct()
    {
        parent::__construct();
    }


    public function index($params)
    {

        $GUID = $params['GUID'];
        $mode = $params['mode'];//1=客户端，2=安卓，3=iOS
        if($GUID==""){
            goURL("xgquanx.php?view=show");
            exit;
        }
        $user_info=$this->t1dao->get_userinfo_byguid($GUID);

        if(empty($user_info)){
            alert('用户不存在');
            exit;
        }

        $mc_type=$params['mc_type']!=''?$params['mc_type']:$user_info['mc_type'];

        $uid=$user_info['Uid'];
        $SignCS=$user_info['SignCS'];
        $MID=$user_info['Mid'];
        $UserName=$user_info['UserName'];

        if($params['isxg']==1){
            $MID=951;
        }else if($params['isxg']==2){
            $MID=512;
        }

        $where="";
        if($mc_type==3){
            $where.=" AND (app_license_popedom_item.itemname='权限分配' or app_license_popedom_item.itemname='二级权限分配') ";
        }else{
            $where.=" AND app_license_popedom_item.itemname='权限分配'  ";
        }
        

        $data=$this->t1dao->getRow("SELECT app_license_detail.ID FROM app_license LEFT JOIN app_license_detail ON app_license.ID=app_license_detail.LID WHERE app_license_detail.SignCS='$SignCS' AND app_license.MID='$MID' AND app_license.mc_type='$mc_type' AND app_license_detail.UseUser='$UserName'");

        //$data=$this->t1dao->getRow("select * from app_license_detail where  SignCS ='$SignCS' and  mc_type='$mc_type' and Status=1 order by  CreateDate DESC  limit 1");
        $lid=$data['ID'];

        //权限分配的创建人
        $quanxsql="SELECT app_license_popedom_item.itemname,app_license_popedom_item.id FROM app_license_popedom LEFT JOIN app_license_popedom_item ON app_license_popedom.popid=app_license_popedom_item.id LEFT JOIN app_license_popedom_type ON  app_license_popedom_type.id=app_license_popedom_item.typeid WHERE  app_license_popedom.uid='$uid'  and app_license_popedom.isdel='0' and app_license_popedom_type.status=1 AND app_license_popedom_type.mc_type='$mc_type' AND app_license_popedom_type.isdel=0 AND app_license_popedom_item.status=1 AND app_license_popedom_item.mc_type='$mc_type' AND app_license_popedom_item.isdel=0  $where ";
        $quanxList=$this->t1dao->AQUERY($quanxsql);


        if(empty($quanxList) ){
            goURL("xgquanx.php?view=show");
            exit;
        }

        //获取用户信息
        $licenses = $this->t1dao->get_userinfo_bymid($MID,$mc_type);
        $LID=$licenses['ID'];

        $where="";
        $power2=0;
        $all_relation=array();
        if(!isset($quanxList['权限分配']) && isset($quanxList['二级权限分配']) && $mc_type==3){
            $power2=1;
            $all_relation=$this->t1dao->AQUERY("select id,userid from app_adminuser_relation where addid='".$uid."' and isdel=0 group by userid ");
        }

        $alluser=$this->maindao->query("SELECT username,truename,logonflag,xglogonflag FROM  adminuser WHERE mid='".$MID."'  and username!='' and state=1 and id!='453344' $where order by id desc ");
        $userlist=array();
        $statuslist=array();
        foreach($alluser as $key=>$value){
            $userlist[$value['username']]=$value['truename'];
            if($mc_type==3 || $mc_type==2){
                $statuslist[$value['username']]=$value['logonflag']==0?"关闭":"开启";
            }else if($mc_type==4){
                $statuslist[$value['username']]=$value['xglogonflag']==0?"关闭":"开启";
            }
            
        }

        //$ydetail = $this->t1dao->query("SELECT app_license_detail.*,app_session_temp.GUID,app_session_temp.Uid FROM app_license_detail LEFT JOIN (select * from app_session_temp where mc_type='$mc_type' and Mid=$MID )as app_session_temp ON  app_license_detail.UseUser=app_session_temp.UserName WHERE   app_license_detail.LID=$LID AND app_license_detail.mc_type='$mc_type' AND app_license_detail.status=1 GROUP BY app_license_detail.ID ORDER BY app_license_detail.ID DESC");
        //$ndetail = $this->t1dao->query("SELECT app_license_detail.*,app_session_temp.GUID,app_session_temp.Uid FROM app_license_detail LEFT JOIN (select * from app_session_temp where mc_type='$mc_type' and Mid=$MID )as app_session_temp ON  app_license_detail.UseUser=app_session_temp.UserName WHERE  app_license_detail.LID=$LID AND app_license_detail.mc_type='$mc_type' AND app_license_detail.status=0 GROUP BY app_license_detail.ID ORDER BY app_license_detail.ID DESC");

        $userlist2=$this->maindao->AQUERY("SELECT username,id FROM  adminuser WHERE mid='".$MID."'  and username!='' and state=1  order by id desc ");
        $ydetail = $this->t1dao->query("SELECT * FROM app_license_detail  WHERE   app_license_detail.LID=$LID AND app_license_detail.mc_type='$mc_type' AND app_license_detail.status=1 GROUP BY app_license_detail.ID ORDER BY app_license_detail.ID DESC");
		$ndetail = $this->t1dao->query("SELECT * FROM app_license_detail  WHERE  app_license_detail.LID=$LID AND app_license_detail.mc_type='$mc_type' AND app_license_detail.status=0 GROUP BY app_license_detail.ID ORDER BY app_license_detail.ID DESC");
       
        foreach($ydetail as $k=>$v){
            $v["Uid"]=$userlist2[$v["UseUser"]];
            if(($power2==1 && !in_array($v["Uid"],$all_relation)) || $v["Uid"]=="" || $v["UseUser"]==""){
                unset($ydetail[$k]);
            }else{
                $ydetail[$k]=$v;
            }
        }
        foreach($ndetail as $k=>$v){
            $v["Uid"]=$userlist2[$v["UseUser"]];
            if(($power2==1 && !in_array($v["Uid"],$all_relation)) || $v["Uid"]=="" || $v["UseUser"]==""){
                unset($ndetail[$k]);
            }else{
                $ndetail[$k]=$v;
            }
        }
        sort($ydetail);
        sort($ndetail);
        $this->assign("statuslist",$statuslist);
        $this->assign("userlist",$userlist);
        $this->assign("licenses",$licenses);
        $this->assign("ydetail",$ydetail);
        $this->assign("ndetail",$ndetail);
        $this->assign("GUID",$GUID);
        $this->assign("mode",$mode);
        $this->assign("mc_type",$mc_type);
        $this->assign("MID",$MID);
        $this->assign("XGDCURL",XGDCURL);
        $this->assign("SGDCURL",SGDCURL.'/web');
        $this->assign("user_info",$user_info);
        $this->assign("params",$params);
        $this->assign("power2",$power2);
    }



    public function getcopy($params){
        $ID=$params['ID'];

        $detail = $this->t1dao->getRow("SELECT app_license_detail.LicenseKeyD,app_license_detail.UseUser,app_license.LicenseKeyMain,app_license.MID FROM app_license_detail,app_license where app_license_detail.LID=app_license.ID AND app_license_detail.ID='$ID' ");
        echo  json_encode($detail);

    }




    public function show($params)
    {
    }
    public function updateStatus($params){
        $ID=$params['ID'];
        $LID=$params['LID'];
        $ischeck=$params['ischeck'];
        $mc_type=$params['mc_type'];

        if($params['status']=='update'){
            $this->t1dao->execute("UPDATE app_license_detail SET IsUpdate='$ischeck' WHERE ID='$ID'");
            $data[0]=array(
                "ID"=>"$ID",
                "IsUpdate"=>"$ischeck"
            );

        }else if($params['status']=='status'){
            $this->t1dao->execute("UPDATE app_license_detail SET Status='$ischeck' WHERE ID='$ID'");
            $data[0]=array(
                "ID"=>"$ID",
                "Status"=>"$ischeck"
            );
        }


        $info[0]=array(
            "tablename"=>"app_license_detail",
            "type"=>"update",
            "pk"=> "ID",
            "data"=>$data
        );

        $info[0]=array(
            "tablename"=>"app_license_detail",
            "type"=>"update",
            "pk"=> "ID",
            "data"=>$data
        );
        $infos=json_encode($info);
        $this->synchronize($params['GUID'],$infos,$mc_type);

        //$user_info=$this->t1dao->get_userinfo_byguid($params['GUID']);
        $MID=$params['MID'];

        $userlist=$this->maindao->AQUERY("SELECT username,truename FROM  adminuser WHERE mid='".$MID."'  and username!='' and state=1  order by id desc ");


        // $ydetail = $this->t1dao->query("SELECT app_license_detail.*,app_session_temp.GUID,app_session_temp.Uid ,app_session_temp.TrueName FROM app_license_detail LEFT JOIN (select * from app_session_temp where mc_type=$mc_type and Mid=$MID )as app_session_temp ON  app_license_detail.UseUser=app_session_temp.UserName WHERE app_license_detail.LID=$LID AND app_license_detail.mc_type=$mc_type AND app_license_detail.status=1 GROUP BY app_license_detail.ID ORDER BY app_license_detail.ID DESC");
        //	$ndetail = $this->t1dao->query("SELECT app_license_detail.*,app_session_temp.GUID,app_session_temp.Uid ,app_session_temp.TrueName FROM app_license_detail LEFT JOIN (select * from app_session_temp where mc_type=$mc_type and Mid=$MID )as app_session_temp ON  app_license_detail.UseUser=app_session_temp.UserName WHERE app_license_detail.LID=$LID AND app_license_detail.mc_type=$mc_type AND app_license_detail.status=0 GROUP BY app_license_detail.ID ORDER BY app_license_detail.ID DESC");


        $userlist2=$this->maindao->AQUERY("SELECT username,id FROM  adminuser WHERE mid='".$MID."'  and username!='' and state=1  order by id desc ");
        $ydetail = $this->t1dao->query("SELECT * FROM app_license_detail  WHERE   app_license_detail.LID=$LID AND app_license_detail.mc_type='$mc_type' AND app_license_detail.status=1 GROUP BY app_license_detail.ID ORDER BY app_license_detail.ID DESC");
        $ndetail = $this->t1dao->query("SELECT * FROM app_license_detail  WHERE  app_license_detail.LID=$LID AND app_license_detail.mc_type='$mc_type' AND app_license_detail.status=0 GROUP BY app_license_detail.ID ORDER BY app_license_detail.ID DESC");


        foreach($ydetail as $key=>$value){
            $ydetail[$key]['Remarks']=$value['Remarks'];
            $ydetail[$key]['TrueName']=$userlist[$value['UseUser']];
            $ydetail[$key]["Uid"]=$userlist2[$value["UseUser"]];
        }
        foreach($ndetail as $key=>$value){
            $ndetail[$key]['Remarks']=$value['Remarks'];
            $ndetail[$key]['TrueName']=$userlist[$value['UseUser']];
            $ndetail[$key]["Uid"]=$userlist2[$value["UseUser"]];
        }
        $Result['Success']=1;
        $Result['ydetail']=$ydetail;
        $Result['ndetail']=$ndetail;
        echo  json_encode($Result);
    }


    function closeuser($params){
        $Result['Success']=0;
        if($params["id"]){
            $user_info=$this->maindao->getRow("select * from adminuser where id='".$params["id"]."'");
            if($user_info["ismaster"]==1){
                $Result['Success']=0;
                $Result['Msg']="主账号不可关闭";
                echo  json_encode($Result);exit;
            }else{
                if($params["mc_type"]==4){
                    $sql="select count(*) from app_license_popedom where uid='".$params["id"]."' and creatuser!='".$params["uid"]."' and mc_type='".$params["mc_type"]."' and isdel=0";
                    $count=$this->t1dao->getOne($sql);
                    if($count>0){
                        $Result['Success']=0;
                        $Result['Msg']="用户权限涉及其他模块，请联系管理员取消授权后，才能关闭用户！";
                        echo  json_encode($Result);exit;
                    }else{
                        $this->maindao->execute("update adminuser set xglogonflag='0' where id='".$params["id"]."'");
                        $Result['Success']=1;
                    }
                }else{
                    $this->maindao->execute("update adminuser set logonflag='0' where id='".$params["id"]."'");
                    $Result['Success']=1;
                }
                
            }
        }
        echo  json_encode($Result);
    }


    function openuser($params){
        $Result['Success']=0;
        if($params["id"]){
            if($params["mc_type"]==4){
                $this->maindao->execute("update adminuser set xglogonflag='1' where id='".$params["id"]."'");
            }else{
                $this->maindao->execute("update adminuser set logonflag='1' where id='".$params["id"]."'");
            }
            $Result['Success']=1;
        }
        echo  json_encode($Result);
    }

    public function setpower($params){
        $GUID=$params['GUID'];
        $mode = $params['mode'];//1=客户端，2=安卓，3=iOS
        $UseUser=$params['UseUser'];
        $uid=$params['Uid'];

        if($GUID==""){
            alert('GUID不能为空');
            goback();
            exit;
        }

        //获取用户信息
        $user_infos = $this->t1dao->get_userinfo_byguid($GUID);
        $userid= $user_infos['Uid'];
        $MID=$user_infos['Mid'];

        if($params['isxg']==1){
            $MID=951;
        }else if($params['isxg']==2){
            $MID=512;
        }

        if(empty($user_infos)){
            alert('用户不存在');
            goback();
            exit;
        }

        $mc_type= $params['mc_type']!='' ? $params['mc_type'] : $user_infos['mc_type'];

        //子序列号信息
        if($params['ID']){
            $ID=$params['ID'];
            $detail_infos = $this->t1dao->get_detail_byguid($ID);
            $SignCS=$detail_infos['SignCS'];
        }


        //设备用户信息
        if($uid==""){
            $uid=$this->maindao->getOne("SELECT id FROM  adminuser WHERE mid='$MID'  and username='$UseUser' and state=1  order by id desc limit 1");
        }

        //$user_SignCS_infos = $this->t1dao->get_userinfo_bySignCS($SignCS,$mc_type,$MID);
        //$uid=$user_SignCS_infos['Uid'];
        //$user_guid=$user_SignCS_infos['GUID'];

        //权限分配的创建人
        $quanxsql="SELECT app_license_popedom.uid,app_license_popedom.creatuser FROM app_license_popedom LEFT JOIN app_license_popedom_item ON app_license_popedom.popid=app_license_popedom_item.id LEFT JOIN app_license_popedom_type ON  app_license_popedom_type.id=app_license_popedom_item.typeid WHERE  app_license_popedom.uid='$userid' and app_license_popedom.isdel='0' and app_license_popedom_type.status=1 AND app_license_popedom_type.mc_type='$mc_type' AND app_license_popedom_type.isdel=0 AND app_license_popedom_item.status=1 AND app_license_popedom_item.mc_type='$mc_type' AND app_license_popedom_item.isdel=0 AND app_license_popedom_item.itemname='权限分配' ";
        $quanxList=$this->t1dao->getRow($quanxsql);
        $creatuser=$quanxList['creatuser'];

        if($uid==$userid || $creatuser==$uid || $creatuser=='-1'){
            $power= 1;
        }else{
            $power= 0;
        }

        $power2= 0;
        $where="";
        if($mc_type==3){
            $userpopedom = $this->t1dao->get_self_popedom($userid,$mc_type);
            
            if(!in_array("权限分配",$userpopedom) && in_array("二级权限分配",$userpopedom)){
                $nopower= 0;
                $power2= 1;
                $popedomids=array();
                foreach($userpopedom as $key=>$value){
                    if($value!="二级权限分配"){
                        $popedomids[]=$key;
                    }
                }
                $where.=" and app_license_popedom_item.id in ('".implode("','",$popedomids)."')";      
            }
        }

        //权限选项
        $popedomsql="SELECT app_license_popedom_item.typeid,app_license_popedom_item.id,app_license_popedom_type.typename,app_license_popedom_item.itemname FROM app_license_popedom_type LEFT JOIN app_license_popedom_item ON app_license_popedom_type.id=app_license_popedom_item.typeid  WHERE app_license_popedom_type.status=1 AND app_license_popedom_type.mc_type='$mc_type' AND app_license_popedom_type.isdel=0 AND app_license_popedom_item.status=1 AND app_license_popedom_item.mc_type='$mc_type' AND app_license_popedom_item.isdel=0 $where";
        $popedomList=$this->t1dao->query($popedomsql);

        //获取拥有的权限
        $allpopedom = $this->t1dao->get_popedom_byid($ID,$uid,$mc_type);

        $qxList=array();
        foreach($popedomList as $pkey=>$pvalue){
            $arr['itemname']=$pvalue['itemname'];
            $arr['id']=$pvalue['id'];
            $arr['on']=in_array($pvalue['id'],$allpopedom)?'1':'0';
            $qxList[$pvalue['typename']]['data'][]=$arr;
            $qxList[$pvalue['typename']]['count']=count($qxList[$pvalue['typename']]['data']);
            $qxList[$pvalue['typename']]['id']=$pvalue['typeid'];
            if($arr['on']=='0' && !isset($qxList[$pvalue['typename']]['all']) ){
                $qxList[$pvalue['typename']]['all']=1;
            }
        }


        $this->assign("GUID",$GUID);
        $this->assign("qxList",$qxList);
        $this->assign("LID",$ID);
        $this->assign("mode",$mode);
        $this->assign("mc_type",$mc_type);
        $this->assign("uid",$uid);
        $this->assign("SignCS",$SignCS);
        $this->assign("userid",$userid);
        $this->assign("UseUser",$UseUser);
        $this->assign("power",$power);
        $this->assign("params",$params);
        $this->assign("power2",$power2);
        
    }


    public function changearray($arr){
        $newarray=array();
        foreach($arr as $key=>$val){
            if(is_array($val)){
                $newarray=array_merge($newarray,$val);
            }else{
            }
        }
        return $newarray;
    }

    public function doedit($params){
        $qxList=$params['qx'];
        $GUID=$params['GUID'];
        $uid=$params['uid'];
        $user_guid=$params['user_guid'];
        $LID=$params['LID'];
        $mc_type=$params['mc_type'];
        $adminid=$params['userid'];
        //获取用户信息
        $admin_infos = $this->t1dao->get_userinfo_byguid($GUID);
        $adminusername=$admin_infos['TrueName'];
        $signcs=$admin_infos['SignCS'];
        //获取信息
        $userList=$this->maindao->getRow("SELECT * FROM  adminuser WHERE id='$uid' ");
        $UserName=$userList['username'];
        $TrueName=$userList['truename'];

        $IP=$this->getip();
        //$user_infos = $this->t1dao->get_userinfo_byguid($user_guid);
        //$UserName=$user_infos['UserName'];
        //$TrueName=$user_infos['TrueName'];

        $addpopedom=array();
        $editpopedom=array();
        $addlog=array();

        $qxList=$this->changearray($qxList);


        foreach($qxList as $k=>$qk){
            $qxs=explode('|',$qk);
            $popid=$qxs[0];
            $qv=$qxs[1];

            $popedoms=$this->t1dao->getRow("SELECT * FROM app_license_popedom WHERE  uid='$uid' AND mc_type='$mc_type' AND popid='$popid'");

            if(empty($popedoms)){
                $insertsql="INSERT INTO app_license_popedom SET lid='$LID',uid='$uid', username='$UserName',truename='$TrueName',popid='$popid',creattime=NOW(),creatuser='$adminid',mc_type='$mc_type'";
                $this->t1dao->execute($insertsql);
                $popedomid = $this->t1dao->insert_id();

                $addpopedom[]=array(
                    "id"=>$popedomid,
                    "lid"=>"$LID",
                    "uid"=>"$uid",
                    "username"=>"$UserName",
                    "truename"=>"$TrueName",
                    "popid"=>"$popid",
                    "creatuser"=>"$adminid",
                    "mc_type"=>"$mc_type",
                    "creattime"=>date('Y-m-d H:i:s'),
                    "isdel"=>"0"
                );

                $addlog[]=array(
                    "opcontent"=>"用户:".$TrueName."，分配权限：".$qv,
                    "optype"=>"9"
                );
            }else{
                if($popedoms['isdel']=='1'){
                    $id=$popedoms['id'];
                    $updatesql="UPDATE  app_license_popedom SET updateadminid='$adminid',updateadminname='$adminusername',updatetime=NOW(),isdel=0 WHERE id='$id'";
                    $this->t1dao->execute($updatesql);
                    $editpopedom[]=array(
                        "id"=>"$id",
                        "isdel"=>"0",
                        "updateadminid"=>"$adminid",
                        "updateadminname"=>"$adminusername",
                        "updatetime"=>date('Y-m-d H:i:s')
                    );
                    $addlog[]=array(
                        "opcontent"=>"用户:".$TrueName."，分配权限：".$qv,
                        "optype"=>"9"
                    );
                }
            }
        }

        $where="";
        if($mc_type==3 && $params['power2']==1){
            $allqx=$params['allqx'];
            $where.=" AND app_license_popedom.popid in ('".implode("','",$allqx)."') ";
        }

        $yx_item=$qxList;
        $qx_popedoms=$this->t1dao->query("SELECT app_license_popedom_item.itemname,app_license_popedom.* FROM app_license_popedom left join app_license_popedom_item on app_license_popedom.popid=app_license_popedom_item.id left join app_license_popedom_type on app_license_popedom_type.id=app_license_popedom_item.typeid  WHERE  app_license_popedom.uid='$uid' AND app_license_popedom.mc_type='$mc_type' AND app_license_popedom.popid not in ('".implode("','",$yx_item)."') AND app_license_popedom.isdel=0 AND app_license_popedom_item.isdel=0 AND app_license_popedom_type.isdel=0 $where");
        if($qx_popedoms){
            foreach($qx_popedoms as $key=>$value){
                $addlog[]=array(
                    "opcontent"=>"用户:".$TrueName."，取消权限：".$value['itemname'],
                    "optype"=>"9"
                );
                $editpopedom[]=array(
                    "id"=>"$value[id]",
                    "isdel"=>"1",
                    "deladminid"=>"$adminid",
                    "deltime"=>date('Y-m-d H:i:s')
                );
            }

            $this->t1dao->execute("UPDATE app_license_popedom SET isdel=1,deladminid='$adminid',deltime=NOW() WHERE   uid='$uid' AND mc_type='$mc_type' and popid not in ('".implode("','",$yx_item)."') AND isdel=0 $where");
        }

        if(!empty($addlog)){
            foreach($addlog as $ak=>$av){
                $this->t1dao->execute("INSERT INTO dc_oplog SET guid='$GUID',signcs='$signcs',truename='$TrueName',userid='$uid',opcontent='$av[opcontent]',optype=9,create_time=NOW(),ip='$IP',mc_type='$mc_type'");
            }
            $this->dosysLog($GUID,$addlog,$mc_type);
        }

        if(!empty($addpopedom)){
            $addinfo[0]=array(
                "tablename"=>"app_license_popedom",
                "type"=>"insert",
                "pk"=> "id",
                "data"=>$addpopedom
            );
            $addinfos=json_encode($addinfo);
            $data=$this->synchronize($GUID,$addinfos,$mc_type);
        }

        if(!empty($editpopedom)){
            $editinfo[0]=array(
                "tablename"=>"app_license_popedom",
                "type"=>"update",
                "pk"=> "id",
                "data"=>$editpopedom
            );
            $editinfos=json_encode($editinfo);
            $data=$this->synchronize($GUID,$editinfos,$mc_type);
        }

        $Message['Success']=1;
        echo json_encode($Message);
        //alert("设置成功！");
        //goURL("xgquanx.php?view=index&GUID=".$GUID."&mode=".$params['mode']."&mc_type=".$mc_type);
        exit;
    }


    public function unbind($params){
        $ID=$params['ID'];
        $LID=$params['LID'];
        $mc_type=$params['mc_type'];
        $data=$this->synchronize($params['GUID'],$infos,$mc_type);
        $user_info=$this->t1dao->get_userinfo_byguid($params['GUID']);
        $uid=$user_info['Uid'];
        //设备解绑
        $this->t1dao->execute("UPDATE app_license_detail SET SignCS='' WHERE ID='$ID'");
        $data[0]=array(
            "ID"=>"$ID",
            "SignCS"=>""
        );

        $info[0]=array(
            "tablename"=>"app_license_detail",
            "type"=>"update",
            "pk"=> "ID",
            "data"=>$data
        );
        $infos=json_encode($info);
        $data=$this->synchronize($params['GUID'],$infos,$mc_type);

        //$user_info=$this->t1dao->get_userinfo_byguid($params['GUID']);
        $MID=$params['MID'];

        $where="";
        if($mc_type==3){
            $where.=" AND (app_license_popedom_item.itemname='权限分配' or app_license_popedom_item.itemname='二级权限分配') ";
        }else{
            $where.=" AND app_license_popedom_item.itemname='权限分配'  ";
        }
        //权限分配的创建人
        $quanxsql="SELECT app_license_popedom_item.itemname,app_license_popedom_item.id FROM app_license_popedom LEFT JOIN app_license_popedom_item ON app_license_popedom.popid=app_license_popedom_item.id LEFT JOIN app_license_popedom_type ON  app_license_popedom_type.id=app_license_popedom_item.typeid WHERE  app_license_popedom.uid='$uid'  and app_license_popedom.isdel='0' and app_license_popedom_type.status=1 AND app_license_popedom_type.mc_type='$mc_type' AND app_license_popedom_type.isdel=0 AND app_license_popedom_item.status=1 AND app_license_popedom_item.mc_type='$mc_type' AND app_license_popedom_item.isdel=0  $where ";
        $quanxList=$this->t1dao->AQUERY($quanxsql);

        $where="";
        $power2=0;
        $all_relation=array();
        if(!isset($quanxList['权限分配']) && isset($quanxList['二级权限分配']) && $mc_type==3){
            $power2=1;
            $all_relation=$this->t1dao->AQUERY("select id,userid from app_adminuser_relation where addid='".$uid."' and isdel=0 group by userid ");
        }

        $alluser=$this->maindao->query("SELECT username,truename,logonflag,xglogonflag FROM  adminuser WHERE mid='".$MID."'  and username!='' and state=1  order by id desc ");
        $userlist=array();
        $statuslist=array();
        foreach($alluser as $key=>$value){
            $userlist[$value['username']]=$value['truename'];
            if($mc_type==3 || $mc_type==2){
                $statuslist[$value['username']]=$value['logonflag']==0?"关闭":"开启";
            }else if($mc_type==4){
                $statuslist[$value['username']]=$value['xglogonflag']==0?"关闭":"开启";
            }
        }


        //$userlist=$this->maindao->AQUERY("SELECT username,truename FROM  adminuser WHERE mid='".$MID."'  and username!='' and state=1  order by id desc ");
        
        
        //$ydetail = $this->t1dao->query("SELECT app_license_detail.*,app_session_temp.GUID,app_session_temp.Uid  FROM app_license_detail LEFT JOIN (select * from app_session_temp where mc_type=$mc_type and Mid=$MID)as app_session_temp ON  app_license_detail.UseUser=app_session_temp.UserName WHERE app_license_detail.LID=$LID AND app_license_detail.mc_type=$mc_type AND app_license_detail.status=1 GROUP BY app_license_detail.ID ORDER BY app_license_detail.ID DESC");
        //$ndetail = $this->t1dao->query("SELECT app_license_detail.*,app_session_temp.GUID,app_session_temp.Uid  FROM app_license_detail LEFT JOIN (select * from app_session_temp where mc_type=$mc_type and Mid=$MID)as app_session_temp ON  app_license_detail.UseUser=app_session_temp.UserName WHERE app_license_detail.LID=$LID AND app_license_detail.mc_type=$mc_type AND app_license_detail.status=0 GROUP BY app_license_detail.ID ORDER BY app_license_detail.ID DESC");

        $userlist2=$this->maindao->AQUERY("SELECT username,id FROM  adminuser WHERE mid='".$MID."'  and username!='' and state=1  order by id desc ");
        $ydetail = $this->t1dao->query("SELECT * FROM app_license_detail  WHERE   app_license_detail.LID=$LID AND app_license_detail.mc_type='$mc_type' AND app_license_detail.status=1 GROUP BY app_license_detail.ID ORDER BY app_license_detail.ID DESC");
		$ndetail = $this->t1dao->query("SELECT * FROM app_license_detail  WHERE  app_license_detail.LID=$LID AND app_license_detail.mc_type='$mc_type' AND app_license_detail.status=0 GROUP BY app_license_detail.ID ORDER BY app_license_detail.ID DESC");
       
        foreach($ydetail as $k=>$v){
            $v["Uid"]=$userlist2[$v["UseUser"]];
            $v["Status"]= $statuslist[$v["UseUser"]];
            $v["TrueName"]=$userlist[$v["UseUser"]];
            
            if(($power2==1 && !in_array($v["Uid"],$all_relation)) || $v["Uid"]=="" || $v["UseUser"]==""){
                unset($ydetail[$k]);
            }else{
                $ydetail[$k]=$v;
            }
        }
        foreach($ndetail as $k=>$v){
            $v["Uid"]=$userlist2[$v["UseUser"]];
            $v["Status"]= $statuslist[$v["UseUser"]];
            $v["TrueName"]= $userlist[$v["UseUser"]];
            if(($power2==1 && !in_array($v["Uid"],$all_relation)) || $v["Uid"]=="" || $v["UseUser"]==""){
                unset($ndetail[$k]);
            }else{
                $ndetail[$k]=$v;
            }
        }
        sort($ndetail);
        sort($ydetail);
        $Message['Success']=1;
        $Message['ydetail']=$ydetail;
        $Message['ndetail']=$ndetail;
        echo json_encode($Message);
    }



    public function reset($params){
        $ID=$params['ID'];
        $LID=$params['LID'];
        $mc_type=$params['mc_type'];

        //重置
        $this->t1dao->execute("update  app_license_detail set SignCS='',UserDate='',UseUser='',Remarks='' where ID='$ID'");
        $data[0]=array(
            "ID"=>"$ID",
            "UseUser"=>"",
            "SignCS"=>"",
            "Remarks"=>""
        );

        $info[0]=array(
            "tablename"=>"app_license_detail",
            "type"=>"update",
            "pk"=> "ID",
            "data"=>$data
        );
        $infos=json_encode($info);
        $data=$this->synchronize($params['GUID'],$infos,$mc_type);
        $user_info=$this->t1dao->get_userinfo_byguid($params['GUID']);
        $uid=$user_info['Uid'];

        $MID=$params['MID'];


        $where="";
        if($mc_type==3){
            $where.=" AND (app_license_popedom_item.itemname='权限分配' or app_license_popedom_item.itemname='二级权限分配') ";
        }else{
            $where.=" AND app_license_popedom_item.itemname='权限分配'  ";
        }
        //权限分配的创建人
        $quanxsql="SELECT app_license_popedom_item.itemname,app_license_popedom_item.id FROM app_license_popedom LEFT JOIN app_license_popedom_item ON app_license_popedom.popid=app_license_popedom_item.id LEFT JOIN app_license_popedom_type ON  app_license_popedom_type.id=app_license_popedom_item.typeid WHERE  app_license_popedom.uid='$uid'  and app_license_popedom.isdel='0' and app_license_popedom_type.status=1 AND app_license_popedom_type.mc_type='$mc_type' AND app_license_popedom_type.isdel=0 AND app_license_popedom_item.status=1 AND app_license_popedom_item.mc_type='$mc_type' AND app_license_popedom_item.isdel=0  $where ";
        $quanxList=$this->t1dao->AQUERY($quanxsql);

        $where="";
        $power2=0;
        $all_relation=array();
        if(!isset($quanxList['权限分配']) && isset($quanxList['二级权限分配']) && $mc_type==3){
            $power2=1;
            $all_relation=$this->t1dao->AQUERY("select id,userid from app_adminuser_relation where addid='".$uid."' and isdel=0 group by userid ");
        }

        $alluser=$this->maindao->query("SELECT username,truename,logonflag,xglogonflag FROM  adminuser WHERE mid='".$MID."'  and username!='' and state=1  order by id desc ");
        $userlist=array();
        $statuslist=array();
        foreach($alluser as $key=>$value){
            $userlist[$value['username']]=$value['truename'];
            if($mc_type==3 || $mc_type==2){
                $statuslist[$value['username']]=$value['logonflag']==0?"关闭":"开启";
            }else if($mc_type==4){
                $statuslist[$value['username']]=$value['xglogonflag']==0?"关闭":"开启";
            }
        }

        
        //$userlist=$this->maindao->AQUERY("SELECT username,truename FROM  adminuser WHERE mid='".$MID."'  and username!='' and state=1  order by id desc ");
        

        //$ydetail = $this->t1dao->query("SELECT app_license_detail.*,app_session_temp.GUID,app_session_temp.Uid  FROM app_license_detail LEFT JOIN (select * from app_session_temp where mc_type=$mc_type and Mid=$MID )as app_session_temp ON  app_license_detail.UseUser=app_session_temp.UserName WHERE app_license_detail.LID=$LID AND app_license_detail.mc_type=$mc_type AND app_license_detail.status=1 GROUP BY app_license_detail.ID ORDER BY app_license_detail.ID DESC");
        //$ndetail = $this->t1dao->query("SELECT app_license_detail.*,app_session_temp.GUID,app_session_temp.Uid  FROM app_license_detail LEFT JOIN (select * from app_session_temp where mc_type=$mc_type and Mid=$MID )as app_session_temp ON  app_license_detail.UseUser=app_session_temp.UserName WHERE app_license_detail.LID=$LID AND app_license_detail.mc_type=$mc_type AND app_license_detail.status=0 GROUP BY app_license_detail.ID ORDER BY app_license_detail.ID DESC");

        $userlist2=$this->maindao->AQUERY("SELECT username,id FROM  adminuser WHERE mid='".$MID."'  and username!='' and state=1  order by id desc ");
        $ydetail = $this->t1dao->query("SELECT * FROM app_license_detail  WHERE   app_license_detail.LID=$LID AND app_license_detail.mc_type='$mc_type' AND app_license_detail.status=1 GROUP BY app_license_detail.ID ORDER BY app_license_detail.ID DESC");
        $ndetail = $this->t1dao->query("SELECT * FROM app_license_detail  WHERE  app_license_detail.LID=$LID AND app_license_detail.mc_type='$mc_type' AND app_license_detail.status=0 GROUP BY app_license_detail.ID ORDER BY app_license_detail.ID DESC");

        foreach($ydetail as $k=>$v){
            $v["Uid"]=$userlist2[$v["UseUser"]];
            $v["Status"]= $statuslist[$v["UseUser"]];
            $v["TrueName"]=$userlist[$v["UseUser"]];
            
            if(($power2==1 && !in_array($v["Uid"],$all_relation)) || $v["Uid"]=="" || $v["UseUser"]==""){
                unset($ydetail[$k]);
            }else{
                $ydetail[$k]=$v;
            }
        }
        foreach($ndetail as $k=>$v){
            $v["Uid"]=$userlist2[$v["UseUser"]];
            $v["Status"]= $statuslist[$v["UseUser"]];
            $v["TrueName"]=$userlist[$v["UseUser"]];
            if(($power2==1 && !in_array($v["Uid"],$all_relation)) || $v["Uid"]=="" || $v["UseUser"]==""){
                unset($ndetail[$k]);
            }else{
                $ndetail[$k]=$v;
            }
        }


        sort($ydetail);
        sort($ndetail);

        $Message['Success']=1;
        $Message['ydetail']=$ydetail;
        $Message['ndetail']=$ndetail;
        echo json_encode($Message);
    }


    public function adduser($params){

        $LID=$params['ID'];
        $LLIST = $this->t1dao-> getRow("select * from app_license where ID = '$LID' ");


        $mc_type=$params['mc_type'];
        $LicenseKeyD=$this->makelicense();

        $accounts=$this->maindao->query("SELECT * FROM  adminuser WHERE mid='".$params['MID']."'  and username!='' and state=1  order by username asc");

        $this->assign("accounts",$accounts);
        $this->assign("LLIST",$LLIST);
        $this->assign("mc_type",$mc_type);
        $this->assign("LicenseKeyD",$LicenseKeyD);
        $this->assign("params",$params);
    }




    public function makelicense(){
        // 前5位
        $firse = rand(10000, 99999);
        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $second = "";
        for ($i = 0; $i < 3; $i++){
            $second .= $chars[ mt_rand(0, strlen($chars) - 1) ];
        }
        $three = rand(1000000, 9999999);
        $foue = rand(10000, 99999);
        $li = $firse . "-" . $second . "-" . $three . "-" . $foue;

        $t1 = $this->t1dao-> getRow("select * from app_license where LicenseKeyMain = '$li' ");
        $t2 = $this->t1dao-> getRow("select * from app_license_detail where LicenseKeyD = '$li' ");

        if($t1 || $t2){
            $this->makelicense();
        }else{
            return $li;
        }
    }

    public function getUser($params){
        $MID=$params['MID'];
        $username=$params['username'];

        $isexit=$this->maindao->getOne("SELECT id  FROM adminuser WHERE username='$username' and username!='' and state=1");
        if($isexit>0){
            $message['Success']=0;
        }else{
            $message['Success']=1;
        }
        echo json_encode($message);
    }



    public function doadd($params){

        $type=$params['type'];
        $GUID=$params['GUID'];

        if($GUID==""){
            alert('GUID不能为空');
            goback();
            exit;
        }
        $user_infos = $this->t1dao->get_userinfo_byguid($GUID);
        if(empty($user_infos)){
            alert('用户不存在');
            goback();
            exit;
        }

        //张康账号才能加
        if($user_infos['Uid']!='147803'){
            alert('无权限');
            goback();
            exit;
        }


        $adminid= $user_infos['Uid'];
        $adminname=$user_infos['UserName'];

        $array = array("LID","Remarks","mc_type","LicenseKeyD");
        $list = $this -> getdata($array, $params);
        $IP=$this->getip();


        if($type=='1'){
            $Userid=$params['selectUser'];
            $userList=$this->maindao->getRow("SELECT *  FROM adminuser WHERE  id=$Userid");
            $UseUser=$userList['username'];
            $truename=$userList['truename'];
            $passwordmd5=$userList['passwordmd5'];
            $mobil=$userList['mobil'];
        }else if($type=='2'){
            $passwordmd5=md5(trim($params['password']));
            $UseUser=trim($params['UseUser']);
            $truename=trim($params['truename']);
            $insertsql="insert into adminuser set state=1,username='".$UseUser."',truename='".$truename."',password='".$params['password']."',passwordmd5='$passwordmd5',mid='".$params['MID']."'";
            $this->maindao->execute($insertsql);
            $Userid = $this->maindao->insert_id();
        }
            $this->t1dao->execute("insert into app_adminuser_relation set userid=$Userid, addid=$adminid,mc_type='".$params['mc_type']."',createdate=NOW()");

        $idata[0]=array(
            "id"=>$Userid,
            "truename"=>"$truename",
            "username"=>"$UseUser",
            "passwordmd5"=>"$passwordmd5",
            "state"=>"1",
            "mobil"=>"$mobil",
            "mid"=>"$params[MID]"
        );

        $iinfo[0]=array(
            "tablename"=>"adminuser",
            "type"=>"insert",
            "pk"=> "id",
            "data"=>$idata
        );
        // $iinfos=json_encode($iinfo);
        // $this->synchronize($GUID,$iinfos,$params['mc_type']);


        $insertsql="INSERT INTO app_license_detail SET $list,UseUser='$UseUser',CreateDate=NOW(),SignCS='',UserIp='$IP',UserDate=NOW(),CreateUser='$adminname',Status='1',IsUpdate='1',ExportData='1'";
        $this->t1dao->execute($insertsql);
        $ID = $this->t1dao->insert_id();

        $LicenseNums=$this->t1dao->getOne("SELECT LicenseNums FROM app_license WHERE ID='".$params['LID']."'");
        $nums=$this->t1dao->getOne("SELECT count(*) FROM app_license_detail WHERE LID='".$params['LID']."'");


        if($nums > $LicenseNums){
            $LicenseNums++;
            $this->t1dao->execute("UPDATE app_license SET LicenseNums='$LicenseNums' WHERE ID='".$params['LID']."'");
        }



        $data[0]=array(
            "ID"=>$ID,
            "LID"=>"$params[LID]",
            "UseUser"=>"$UseUser",
            "Remarks"=>"$params[Remarks]",
            "CreateDate"=>date('Y-m-d H:i:s'),
            "UserDate"=>date('Y-m-d H:i:s'),
            "mc_type"=>"$params[mc_type]",
            "LicenseKeyD"=>"$params[LicenseKeyD]",
            "UserIp"=>"$IP",
            "Status"=>"1",
            "IsUpdate"=>"1",
            "ExportData"=>"1",
            "CreateUser"=>"$adminname",
            "IsEnglish"=>"0",
            "SignCS"=>""
        );

        $iinfo[1]=array(
            "tablename"=>"app_license_detail",
            "type"=>"insert",
            "pk"=> "ID",
            "data"=>$data
        );
        $infos=json_encode($iinfo);
        $data=$this->synchronize($GUID,$infos,$params['mc_type']);

        $logMes[0]=array(
            "opcontent"=>"添加新用户".$UseUser,
            "optype"=>"9"
        );
        $this->dosysLog($GUID,$logMes,$params['mc_type']);

        alert("添加成功");
        goURL("xgquanx.php?view=index&GUID=".$GUID."&mode=".$params['mode']."&mc_type=".$params['mc_type']."&isxg=".$params['isxg']);
        exit;

    }


    public function getdata($array, $params){
        $data = array();
        foreach($array as $a){
            $data[] = $a . "='" . $params[$a] . "'";
        }
        $data = implode(",", $data);
        return $data;
    }

    public function getip(){
        if (!empty($_SERVER['HTTP_CLIENT_IP']))
            $ip = $_SERVER['HTTP_CLIENT_IP'];
        else if (!empty($_SERVER['HTTP_X_FORWARDED_FOR']))
            $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
        else
            $ip = $_SERVER['REMOTE_ADDR'];
        return $ip;
    }

    public function ajaxgetindexinfo($params){
        $page = $params['page'] == '' ? 1 : $params['page'];
        $per = 10;
        $start = ( $page - 1 ) * $per;
        $data = $this->drc_test->getAll($params['type'],$start,$per);
        $content = "";
        foreach($data as $tmp){
            $content.='<tr>
				<td>'.$tmp['ndate'].'</td>
				<td>'.$tmp['CreateDate'].'</td>
				<td>'.$tmp['adminname'].'</td>
				<td><a onclick="update('.$tmp['id'].');">修改</a></td>
			</tr>';
        }
        echo $content;
        exit;
    }


    public function edit($params){

        $list=$this->t1dao->getRow("SELECT app_license_detail.*,app_license.MID,app_license.LicenseKeyMain FROM app_license_detail LEFT JOIN app_license ON app_license.ID=app_license_detail.LID WHERE app_license_detail.ID='$params[ID]'");
        $userlist=$this->maindao->getRow("SELECT id,truename,xglogonflag,logonflag FROM  adminuser WHERE mid='".$list['MID']."'  and username='$list[UseUser]' and username!='' and state=1  order by id desc limit 1");
        //$list['password']=$userlist['password'];
        $list['userid']=$userlist['id'];
        $list['truename']=$userlist['truename'];
        if($params['mc_type']==4){
            $list['logonflag']=$userlist['xglogonflag'];
        }else if($params['mc_type']==2 || $params['mc_type']==3){
            $list['logonflag']=$userlist['logonflag'];
        }

        $accounts=$this->maindao->query("SELECT * FROM  adminuser WHERE mid='".$list['MID']."'  and username!='' and state=1  order by username asc");

        $this->assign("accounts",$accounts);
        $this->assign("list",$list);
        $this->assign("params",$params);
    }


    public function update($params){
        //print_r($params);exit;

        $ID=$params['ID'];
        $uid=$params['userid'];
        $Remarks=$params['Remarks'];
        $type=$params['type'];
        if($type=='1'){
            $Userid=$params['selectUser'];
            $userList=$this->maindao->getRow("SELECT *  FROM adminuser WHERE  id=$Userid");
            $UseUser=$userList['username'];
            $truename=$userList['truename'];
            $passwordmd5=$userList['passwordmd5'];
            $mobil=$userList['mobil'];
        }else if($type=='2'){
            $passwordmd5=md5(trim($params['password2']));
            $UseUser=trim($params['UseUser1']);
            $truename=trim($params['truename']);

            $isexit=$this->maindao->getRow("SELECT id  FROM adminuser WHERE  username='$UseUser' and username!='' and state=1");
            if($isexit>0){
                echo"<script>alert('此账号已注册，请重新输入或选择已有账号！');history.go(-1);</script>";
                exit;
            }

            $insertsql="insert into adminuser set state=1,username='".$UseUser."',truename='".$truename."',passwordmd5='$passwordmd5',mid='".$params['MID']."'";
            $this->maindao->execute($insertsql);
            $Userid = $this->maindao->insert_id();
        }else{

            $userArr=$this->maindao->getRow("SELECT *  FROM adminuser WHERE  id=$uid");
            if(trim($params['password'])!=''){
                $passwordmd5=md5(trim($params['password']));
                $udata[0]['passwordmd5']=$passwordmd5;
                $insertsql1="update adminuser set passwordmd5='$passwordmd5',password='$params[password]' where id='$uid'";
                $this->maindao->execute($insertsql1);
                $plogMes[]=array(
                    "opcontent"=>"修改用户".$userArr['username']."密码",
                    "optype"=>"9"
                );
            }
            if(trim($userArr['truename'])!=trim($params['truename2'])){
                $truename=trim($params['truename2']);
                $udata[0]['truename']="$truename";
                $insertsql2="update adminuser set truename='$truename' where id='$uid'";
                $this->maindao->execute($insertsql2);
                $plogMes[]=array(
                    "opcontent"=>"修改用户".$userArr['username']."姓名为".$truename,
                    "optype"=>"9"
                );
            }

            //$this->maindao->execute("update adminuser set isclient='$params[isclient]' where id='$uid'");
        }

        if($type=='1' || $type=='2'){
            $idata[0]=array(
                "id"=>$Userid,
                "truename"=>"$truename",
                "username"=>"$UseUser",
                "passwordmd5"=>"$passwordmd5",
                "state"=>"1",
                "mobil"=>"$mobil",
                "mid"=>"$params[MID]"
            );

            $iinfo[0]=array(
                "tablename"=>"adminuser",
                "type"=>"insert",
                "pk"=> "id",
                "data"=>$idata
            );
            $infos=json_encode($iinfo);
            $this->synchronize($params['GUID'],$infos,$params['mc_type']);

            $logMes[0]=array(
                "opcontent"=>"子序列号ID".$ID."修改用户为".$UseUser,
                "optype"=>"9"
            );
            $this->dosysLog($params['GUID'],$logMes,$params['mc_type']);

            $updatesql="UPDATE app_license_detail SET Remarks='$Remarks',UseUser='$UseUser' WHERE ID='$ID'";
            $this->t1dao->execute($updatesql);

            $ddata[0]=array(
                "ID"=>"$ID",
                "Remarks"=>"$Remarks",
                "UseUser"=>"$UseUser"
            );
            $dinfo[0]=array(
                "tablename"=>"app_license_detail",
                "type"=>"update",
                "pk"=> "ID",
                "data"=>$ddata
            );

            $dinfos=json_encode($dinfo);
            $this->synchronize($params['GUID'],$dinfos,$params['mc_type']);

        }else{
            if(!empty($udata)){
                $udata[0]['id']=$uid;
                $uinfo[0]=array(
                    "tablename"=>"adminuser",
                    "type"=>"update",
                    "pk"=> "id",
                    "data"=>$udata
                );
                $uinfos=json_encode($uinfo);
                $this->synchronize($params['GUID'],$uinfos,$params['mc_type']);
                $this->dosysLog($params['GUID'],$plogMes,$params['mc_type']);
            }

            $updatesql="UPDATE app_license_detail SET Remarks='$Remarks' WHERE ID='$ID'";
            $this->t1dao->execute($updatesql);

            $ddata[0]=array(
                "ID"=>"$ID",
                "Remarks"=>"$Remarks",
            );
            $dinfo[0]=array(
                "tablename"=>"app_license_detail",
                "type"=>"update",
                "pk"=> "ID",
                "data"=>$ddata
            );

            $dinfos=json_encode($dinfo);
            $this->synchronize($params['GUID'],$dinfos,$params['mc_type']);

        }

        alert("修改成功");
        if($params['view']!=""){
            goURL("xgquanx.php?view=".$params['view']."&GUID=".$params['GUID']."&mode=".$params['mode']."&mc_type=".$params['mc_type']."&isxg=".$params['isxg']);
        }else{
            goURL("xgquanx.php?view=index&GUID=".$params['GUID']."&mode=".$params['mode']."&mc_type=".$params['mc_type']."&isxg=".$params['isxg']);
        }

        exit;
    }


    public function synchronize($GUID,$dtjson,$mc_type){

        if($mc_type=='3' || $mc_type=='2' || $mc_type=='4'){
            if($mc_type=='3'){
                $loginurl=XGDCURL."/api/gzjSysLogin";
                $logininfo['username']='testuser';
                $logininfo['pwd']='123456';
                $logininfo['mc_type']='3';

            }else if($mc_type=='2'){
                $loginurl=SGDCURL."/web/systemapi.php?action=gzjSysLogin";
                $logininfo['username']='testuser';
                $logininfo['pwd']='123456';
                $logininfo['mc_type']='2';
            }else if($mc_type=='4'){
                $loginurl=XGDCURL."/api/gzjSysLogin";
                $logininfo['username']='testuser';
                $logininfo['pwd']='123456';
                $logininfo['mc_type']='4';

            }

            $mess=$this->http_post( $loginurl,  http_build_query($logininfo));
            $content=$mess['content'];
            $dd=json_decode($this->clearBom($content),true);
            $token=$dd['Token'];

            if($mc_type=='3'){
                $tburl=XGDCURL."/api/syncdb?mc_type=3&GUID=1";
                $tjinfo['dtjson']=$dtjson;
                $tjinfo['token']=$token;

            }else if($mc_type=='2'){
                $tburl=SGDCURL."/web/systemapi.php?action=syncdb&mc_type=2&GUID=1";
                $tjinfo['dtjson']=$dtjson;
                $tjinfo['token']=$token;
                $ffurl=SGDCURL."/web/systemapi.php?action=syncdb&mc_type=2&GUID=1&token=".$token."&dtjson=".$dtjson;
            }else if($mc_type=='4'){
                $tburl=XGDCURL."/api/syncdb?mc_type=4&GUID=1";
                $tjinfo['dtjson']=$dtjson;
                $tjinfo['token']=$token;
    
            }


            if($dd['Success']==1){
                $data=$this->http_post( $tburl,  urldecode(http_build_query($tjinfo)));
                $contents=$data['content'];
                $ff=json_decode($this->clearBom($contents),true);
                $Message=$ff['Message'];
                return $Message;
            }else{
                $Message=$dd['Message'];
                return $Message;
            }
        }
    }






    function http_post($url,$param,$post_file=false){
        $oCurl = curl_init();

        if(stripos($url,"https://")!==FALSE){
            curl_setopt($oCurl, CURLOPT_SSL_VERIFYPEER, FALSE);
            curl_setopt($oCurl, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($oCurl, CURLOPT_SSLVERSION, 1); //CURL_SSLVERSION_TLSv1
        }
        if(PHP_VERSION_ID >= 50500 && class_exists('\CURLFile')){
            $is_curlFile = true;
        }else {
            $is_curlFile = false;
            if (defined('CURLOPT_SAFE_UPLOAD')) {
                curl_setopt($oCurl, CURLOPT_SAFE_UPLOAD, false);
            }
        }

        if($post_file) {
            if($is_curlFile) {
                foreach ($param as $key => $val) {
                    if(isset($val["tmp_name"])){
                        $param[$key] = new \CURLFile(realpath($val["tmp_name"]),$val["type"],$val["name"]);
                    }else if(substr($val, 0, 1) == '@'){
                        $param[$key] = new \CURLFile(realpath(substr($val,1)));
                    }
                }
            }
            $strPOST = $param;
        }else{
            //$strPOST = json_encode($param);
            $strPOST = $param;
        }

        curl_setopt($oCurl, CURLOPT_URL, $url);
        curl_setopt($oCurl, CURLOPT_RETURNTRANSFER, 1 );
        curl_setopt($oCurl, CURLOPT_POST,true);
        curl_setopt($oCurl, CURLOPT_POSTFIELDS,$strPOST);
        curl_setopt($oCurl, CURLOPT_VERBOSE, 1);
        curl_setopt($oCurl, CURLOPT_HEADER, 1);

        // $sContent = curl_exec($oCurl);
        // $aStatus  = curl_getinfo($oCurl);

        $sContent = $this->execCURL($oCurl);

        curl_close($oCurl);

        return $sContent;
    }

    function execCURL($ch){
        $response = curl_exec($ch);
        $error    = curl_error($ch);
        $result   = array( 'header' => '',
            'content' => '',
            'curl_error' => '',
            'http_code' => '',
            'last_url' => '');

        if ($error != ""){
            $result['curl_error'] = $error;
            return $result;
        }

        $header_size = curl_getinfo($ch,CURLINFO_HEADER_SIZE);
        $result['header'] = str_replace(array("\r\n", "\r", "\n"), "<br/>", substr($response, 0, $header_size));
        $result['content'] = substr( $response, $header_size );
        $result['http_code'] = curl_getinfo($ch,CURLINFO_HTTP_CODE);
        $result['last_url'] = curl_getinfo($ch,CURLINFO_EFFECTIVE_URL);
        $result["base_resp"] = array();
        $result["base_resp"]["ret"] = $result['http_code'] == 200 ? 0 : $result['http_code'];
        $result["base_resp"]["err_msg"] = $result['http_code'] == 200 ? "ok" : $result["curl_error"];

        return $result;
    }


    function post_data($url, $param, $is_file = false, $return_array = true) {
        set_time_limit ( 0 );
        if (! $is_file && is_array ( $param )) {
            $param = $this->JSON ( $param );
        }
        if ($is_file) {
            $header [] = "content-type: multipart/form-data; charset=UTF-8";
        } else {
            $header [] = "content-type: application/json; charset=UTF-8";
        }
        $ch = curl_init ();
        if (class_exists ( '/CURLFile' )) { // php5.5璺焢hp5.6涓?殑CURLOPT_SAFE_UPLOAD鐨勯粯璁ゅ?间笉鍚?
            curl_setopt ( $ch, CURLOPT_SAFE_UPLOAD, true );
        } else {
            if (defined ( 'CURLOPT_SAFE_UPLOAD' )) {
                curl_setopt ( $ch, CURLOPT_SAFE_UPLOAD, false );
            }
        }
        curl_setopt ( $ch, CURLOPT_URL, $url );
        curl_setopt ( $ch, CURLOPT_CUSTOMREQUEST, "POST" );
        curl_setopt ( $ch, CURLOPT_SSL_VERIFYPEER, FALSE );
        curl_setopt ( $ch, CURLOPT_SSL_VERIFYHOST, FALSE );
        curl_setopt ( $ch, CURLOPT_HTTPHEADER, $header );
        curl_setopt ( $ch, CURLOPT_USERAGENT, 'Mozilla/4.0 (compatible; MSIE 5.01; Windows NT 5.0)' );
        curl_setopt ( $ch, CURLOPT_FOLLOWLOCATION, 1 );
        curl_setopt ( $ch, CURLOPT_AUTOREFERER, 1 );
        curl_setopt ( $ch, CURLOPT_POSTFIELDS, $param );
        curl_setopt ( $ch, CURLOPT_RETURNTRANSFER, true );
        $res = curl_exec ( $ch );
        $flat = curl_errno ( $ch );
        if ($flat) {
            $data = curl_error ( $ch );
            //addWeixinLog ( $flat, 'post_data flat' );
            //addWeixinLog ( $data, 'post_data msg' );
        }
        curl_close ( $ch );
        $return_array && $res = json_decode ( $this->clearBom($res), true );
        return $res;
    }


    /*function clearBom($str,$remove = true) {
        $charset[1] = substr($str, 0, 1);
        $charset[2] = substr($str, 1, 1);
        $charset[3] = substr($str, 2, 1);
        if (ord($charset[1]) == 239 && ord($charset[2]) == 187 && ord($charset[3]) == 191) {
            return substr($str, 3);
        }
        else{
            return $str;
        }
    }*/


    public function clearBom($str){
        $bom = chr(239).chr(187).chr(191);
        return str_replace($bom ,'',$str);
    }

    public function send_post($url, $info) {
        $info = http_build_query($info);
        $options = array(
            'http' => array(
                'method' => 'POST',
                'header' => 'Content-type:application/x-www-form-urlencoded',
                'content' => $info
            )
        );
        $context = stream_context_create($options);
        $result = file_get_contents($url, false, $context);
        return $result;
    }



    public function request_post($url) {//url为必传  如果该地址不需要参数就不传
        $postUrl = $url;
        $curlPost=$post_data;
        $ch = curl_init();//初始化curl
        curl_setopt($ch, CURLOPT_URL,$postUrl);//抓取指定网页
        curl_setopt($ch, CURLOPT_HEADER, 0);//设置header
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);//要求结果为字符串且输出到屏幕上
        curl_setopt($ch, CURLOPT_POST, 1);//post提交方式
        curl_setopt($ch, CURLOPT_POSTFIELDS, $curlPost);
        $data = curl_exec($ch);//运行curl
        curl_close($ch);

        return $data;
    }




    public function loglist($params){
        $this->assign("params",$params);
        $this->assign("XGDCURL",XGDCURL);
        $this->assign("SGDCURL",SGDCURL.'/web');
        $userid=$params['Uid'];
        $this->assign("userid",$userid);
        $mc_type=$params['mc_type'];
        $SignCS=$params['SignCS'];
        $GUID=$params['GUID'];
        $SGUID=$params['SGUID'];
        $isxlh= $params['isxlh']=='1' ? $params['isxlh']:'0';
        $this->assign("isxlh",$isxlh);
        $this->assign("isxg",$params['isxg']);
        $this->assign("SGUID",$SGUID);
        $this->assign("mode",$params['mode']);
        $this->assign("mc_type",$params['mc_type']);
        $where="";
        if($isxlh=='1'){
            // AND app_member_logs.GUID='$SGUID'
            // $where.=" AND app_member_logs.SignCS='$SignCS'";
            $where.=" and app_session_temp.Uid='$userid'";
        }else{
            $user_info=$this->t1dao->get_userinfo_byguid($GUID);

            $uid=$user_info['Uid'];
            $SignCS=$user_info['SignCS'];
            $Mid=$user_info['Mid'];
            $UserName=$user_info['UserName'];


            $sql="SELECT app_license_detail.ID FROM app_license LEFT JOIN app_license_detail ON app_license.ID=app_license_detail.LID WHERE app_license_detail.SignCS='$SignCS' AND app_license.MID='$Mid' AND app_license.mc_type='$mc_type' AND app_license_detail.UseUser='$UserName'";
            $detail_arr=$this->t1dao->getRow($sql);
            $lid= empty($detail_arr)?'0':$detail_arr['ID'];


            //权限分配的创建人
            $quanxsql="SELECT app_license_popedom.uid,app_license_popedom.creatuser FROM app_license_popedom LEFT JOIN app_license_popedom_item ON app_license_popedom.popid=app_license_popedom_item.id LEFT JOIN app_license_popedom_type ON  app_license_popedom_type.id=app_license_popedom_item.typeid WHERE  app_license_popedom.uid='$uid'   and app_license_popedom.isdel='0' and app_license_popedom_type.status=1 AND app_license_popedom_type.mc_type='$mc_type' AND app_license_popedom_type.isdel=0 AND app_license_popedom_item.status=1 AND app_license_popedom_item.mc_type='$mc_type' AND app_license_popedom_item.isdel=0 AND app_license_popedom_item.itemname='权限分配' ";

            $quanxList=$this->t1dao->getRow($quanxsql);

            // AND app_member_logs.GUID='$GUID'  AND app_member_logs.GUID='$GUID'
            if(empty($quanxList)){
                //$where.="AND app_member_logs.SignCS='$SignCS'";
                $where.=" and app_session_temp.Uid='$uid'";
            }
        }
        /*
            $user_info=$this->t1dao->get_userinfo_byguid($params['GUID']);
            $uid=$user_info['Uid'];
            $SignCS=$user_info['SignCS'];



            $data=$this->t1dao->getRow("select * from app_license_detail where  SignCS ='$SignCS' and  mc_type='$mc_type' and Status=1 order by  CreateDate DESC  limit 1");
            $lid=$data['ID'];


            //权限分配的创建人
            $quanxsql="SELECT app_license_popedom.uid,app_license_popedom.creatuser FROM app_license_popedom LEFT JOIN app_license_popedom_item ON app_license_popedom.popid=app_license_popedom_item.id LEFT JOIN app_license_popedom_type ON  app_license_popedom_type.id=app_license_popedom_item.typeid WHERE  app_license_popedom.uid='$uid' and app_license_popedom.lid='$lid' and app_license_popedom_type.status=1 AND app_license_popedom_type.mc_type='$mc_type' AND app_license_popedom_type.isdel=0 AND app_license_popedom_item.status=1 AND app_license_popedom_item.mc_type='$mc_type' AND app_license_popedom_item.isdel=0 AND app_license_popedom_item.itemname='权限分配' ";
            $quanxList=$this->t1dao->getRow($quanxsql);
            if(empty($quanxList)){
                $where.=" and app_member_logs.SignCS='$SignCS'";
            }
        }else{
            $where.=" and app_member_logs.SignCS='".$params['SignCS']."'";
        }*/

        $total = $this ->t1dao->getOne("SELECT Count(*) as c FROM app_member_logs  LEFT JOIN app_session_temp ON app_session_temp.GUID=app_member_logs.GUID  WHERE   app_session_temp.GUID=app_member_logs.GUID  and app_member_logs.mc_type=$mc_type and app_session_temp.mc_type=$mc_type $where");
        $page = $params['page'] == '' ? 1 : $params['page'];
        $per = 15;
        $url = "xgquanx.php";
        unset($params['page']);
        $start = ($page - 1) * $per;
        $wool=$this->t1dao->getone("select MaxLineNum from app_version where mc_type='$mc_type'");


        // echo "SELECT app_member_logs.*,app_session_temp.TrueName  FROM app_member_logs LEFT JOIN app_session_temp  ON app_session_temp.GUID=app_member_logs.GUID   WHERE   app_session_temp.GUID=app_member_logs.GUID and app_member_logs.mc_type=$mc_type  and app_session_temp.mc_type=$mc_type $where  ORDER BY app_member_logs.ActionDate DESC  LIMIT $start, $per";

        $list=$this->t1dao->query("SELECT app_member_logs.*,app_session_temp.TrueName  FROM app_member_logs LEFT JOIN app_session_temp  ON app_session_temp.GUID=app_member_logs.GUID   WHERE   app_session_temp.GUID=app_member_logs.GUID and app_member_logs.mc_type=$mc_type  and app_session_temp.mc_type=$mc_type $where  ORDER BY app_member_logs.ActionDate DESC  LIMIT $start, $per");


        foreach ($list as $key => $value) {


            if($list[$key]['whereFrom']=='1'){
                $list[$key]['whereFrom']='pc';

            }else if($list[$key]['whereFrom']=='2'){
                $list[$key]['whereFrom']='Android平板';

            }else if($list[$key]['whereFrom']=='3'){
                $list[$key]['whereFrom']='Android';

            }else if($list[$key]['whereFrom']=='4'){
                $list[$key]['whereFrom']='ipad';

            }else if($list[$key]['whereFrom']=='5'){
                $list[$key]['whereFrom']='iphone';
            }else{
                $list[$key]['whereFrom']='';
            }
            if($list[$key]['Mid']=='0'){
                $list[$key]['Mid']='';
            }
        }

        //$pagebar = pagebar($url, $params, $per, $page, $total);





        $pagebar=$this->getpagelabelnew($total,$per,$page,"xgquanx.php?view=loglist&Uid=$userid&GUID=".$GUID."&mc_type=".$mc_type."&mode=".$mode."&SGUID=".$SGUID."&isxlh=".$isxlh."&SignCS=".$SignCS);

        $this -> assign("pagebar", $pagebar);
        $this -> assign("loglist", $list);
        $this -> assign("SignCS", $SignCS);
        $this -> assign("GUID", $GUID);
        //用户权限
        //get_userinfo_byguid

    }




    public function powerlist($params){

        //$GUID = $params['GUID'];
        //$mode = $params['mode'];//1=客户端，2=安卓，3=iOS
        //$mode=1;
        //$date = date("Y-m-d");
        $mc_type=$params['mc_type'];


        /* $user_info=$this->t1dao->get_userinfo_byguid($params['GUID']);
         $uid=$user_info['Uid'];
         $SignCS=$user_info['SignCS'];
         $Mid=$user_info['Mid'];
         $UserName=$user_info['UserName'];


         if($Mid!='1'){
             goURL("xgquanx.php?view=show");
             exit;
         }*/


        /*    $sql="SELECT app_license_detail.ID FROM app_license LEFT JOIN app_license_detail ON app_license.ID=app_license_detail.LID WHERE app_license_detail.SignCS='$SignCS' AND app_license.MID='$Mid' AND app_license.mc_type='$mc_type' AND app_license_detail.UseUser='$UserName'";
            $detail_arr=$this->t1dao->getRow($sql);
            $lid= empty($detail_arr)?'0':$detail_arr['ID'];


            //权限分配的创建人
            $quanxsql="SELECT app_license_popedom.uid,app_license_popedom.creatuser FROM app_license_popedom LEFT JOIN app_license_popedom_item ON app_license_popedom.popid=app_license_popedom_item.id LEFT JOIN app_license_popedom_type ON  app_license_popedom_type.id=app_license_popedom_item.typeid WHERE  app_license_popedom.uid='$uid' and app_license_popedom.lid='$lid' and app_license_popedom.isdel='0' and app_license_popedom_type.status=1 AND app_license_popedom_type.mc_type='$mc_type' AND app_license_popedom_type.isdel=0 AND app_license_popedom_item.status=1 AND app_license_popedom_item.mc_type='$mc_type' AND app_license_popedom_item.isdel=0 AND app_license_popedom_item.itemname='权限分配' ";

            $quanxList=$this->t1dao->getRow($quanxsql);
            */

        if($params['power']!='A'){
            goURL("xgquanx.php?view=show");
            exit;
        }

        //权限选项
        $popedomsql="SELECT app_license_popedom_type.id as typeid,app_license_popedom_type.status as tstatus,app_license_popedom_item.status as istatus,app_license_popedom_item.id,app_license_popedom_type.typename,app_license_popedom_item.itemname FROM app_license_popedom_type LEFT JOIN (SELECT * FROM app_license_popedom_item WHERE  app_license_popedom_item.mc_type='$mc_type' AND app_license_popedom_item.isdel=0) AS app_license_popedom_item ON app_license_popedom_type.id=app_license_popedom_item.typeid  WHERE  app_license_popedom_type.mc_type='$mc_type' AND app_license_popedom_type.isdel=0 ";
        $popedomList=$this->t1dao->query($popedomsql);

        $qxList=array();
        foreach($popedomList as $pkey=>$pvalue){

            $qxList[$pvalue['typename']]['data'][]=$pvalue;
            $qxList[$pvalue['typename']]['count']=count($qxList[$pvalue['typename']]['data']);
            $qxList[$pvalue['typename']]['typeid']=$pvalue['typeid'];
            $qxList[$pvalue['typename']]['tstatus']=$pvalue['tstatus'];

        }


        $this->assign("params",$params);
        $this->assign("qxList",$qxList);


    }



    public function addtype($params){
        if($params['submit']==2){
            $typeid=$params['typeid'];
            $list=$this->t1dao->getRow("SELECT * FROM app_license_popedom_type WHERE id='$typeid'");
            $this->assign("list",$list);
        }
        $this->assign("params",$params);
    }

    public function doaddtype($params){
        $status= $params['status'];
        if($status==''){
            $status=0;
        }

        if($params['power']!="A"){
            alert('无权限');
            goback();
            exit;
        }


        $userid=$params['userid'];

        $typename=urldecode($params['typenames']);
        $typename=$typename;

        if($params['submit']=='1'){
            $insertsql="INSERT INTO app_license_popedom_type SET typename='".$typename."',status='$status',creattime =NOW(),creatuser='$userid',mc_type='".$params['mc_type']."'";
            $this->t1dao->execute($insertsql);
            $typeid = $this->t1dao->insert_id();

            if($params['mc_type']==3 ||$params['mc_type']==4){
                $data[0]=array(
                    "id"=>$typeid,
                    "typename"=>"$typename",
                    "creattime"=>date('Y-m-d H:i:s'),
                    "mc_type"=>"$params[mc_type]",
                    "status"=>"1",
                    "creatuser"=>"$userid",
                    "updateadminid"=>"0",
                    "updateadminname"=>"0",
                    "updatetime"=>date("Y-m-d H:i:s"),
                    "isdel"=>"0"
                );

                $info[0]=array(
                    "tablename"=>"app_license_popedom_type",
                    "type"=>"insert",
                    "pk"=> "id",
                    "data"=>$data
                );
                $infos=json_encode($info);
                $rr=$this->synchronize(1,$infos,$params['mc_type']);

                $logMes[]=array(
                    "opcontent"=>"添加权限类型".$typename,
                    "optype"=>"9"
                );
                $this->dosysLog(1,$logMes,$params['mc_type']);
            }

            $Result['Success']=1;
            $Result['msg']="添加成功";
            echo  json_encode($Result);
            exit;

        }else if($params['submit']=='2'){
            $typeid=$params['typeid'];
            $typeexit=$this->t1dao->getOne("SELECT id FROM app_license_popedom_type WHERE typename='$typename' AND  id!='$typeid' AND isdel=0");

            if($typeexit==1){
                $Message['Success']=0;
                $Message['msg']="权限类型已存在,勿重复";
                echo  json_encode($Message);
                exit;
            }else{
                $insertsql="UPDATE app_license_popedom_type SET typename='".$typename."',status='$status',updateadminid='$userid',updatetime =NOW() WHERE id='$typeid'";
                $this->t1dao->execute($insertsql);

                if($params['mc_type']==3 || $params['mc_type']==4){
                    $data[0]=array(
                        "id"=>"$typeid",
                        "updatetime"=>date("Y-m-d H:i:s"),
                        "updateadminid"=>"$userid",
                        "status"=>"$status",
                        "typename"=>"$typename"
                    );
                    $info[0]=array(
                        "tablename"=>"app_license_popedom_type",
                        "type"=>"update",
                        "pk"=> "id",
                        "data"=>$data
                    );

                    $infos=json_encode($info);
                    $data=$this->synchronize(1,$infos,$params['mc_type']);

                    $logMes[]=array(
                        "opcontent"=>"修改权限类型".$typename,
                        "optype"=>"9"
                    );
                    $this->dosysLog(1,$logMes,$params['mc_type']);
                }

                $Result['Success']=1;
                $Result['msg']="修改成功";
                echo  json_encode($Result);
                exit;
            }
        }
    }

    public function delpopedom($params){

        if($params['power']!="A"){
            alert('无权限');
            goback();
            exit;
        }

        $userid= $params['userid'];

        if($params['type']==1){
            $tablename="app_license_popedom_type";
            $this->t1dao->execute("UPDATE app_license_popedom_type SET isdel=1,deltime=NOW(),deladminid='$userid' WHERE id='$params[id]'");

            $popedomList=$this->t1dao->getRow("select * from app_license_popedom_type WHERE id='$params[id]'");
            $logMes[]=array(
                "opcontent"=>"删除权限类型".$popedomList['typename'],
                "optype"=>"9"
            );


        }else if($params['type']==2){
            $tablename="app_license_popedom_item";
            $this->t1dao->execute("UPDATE app_license_popedom_item SET isdel=1,deltime=NOW(),deladminid='$userid' WHERE id='$params[id]'");

            $popedomList=$this->t1dao->getRow("select * from app_license_popedom_item WHERE id='$params[id]'");
            $logMes[]=array(
                "opcontent"=>"删除权限选项".$popedomList['itemname'],
                "optype"=>"9"
            );

        }


        $data[0]=array(
            "id"=>"$params[id]",
            "deltime"=>date("Y-m-d H:i:s"),
            "isdel"=>"1",
            "deladminid"=>"$userid"
        );
        $info[0]=array(
            "tablename"=>$tablename,
            "type"=>"update",
            "pk"=> "id",
            "data"=>$data
        );
        $infos=json_encode($info);
        $result=$this->synchronize(1,$infos,$params['mc_type']);
        $this->dosysLog(1,$logMes,$params['mc_type']);



        $Message['Success']=1;
        $Message['msg']=$result;
        echo  json_encode($Message);
        exit;
    }


    public function additem($params){

        $mc_type=$params['mc_type'];
        $popedomtypes=$this->t1dao->AQUERY("SELECT id,typename FROM app_license_popedom_type WHERE  app_license_popedom_type.mc_type='$mc_type' AND app_license_popedom_type.isdel=0 ");
        $this->assign("popedomtypes",$popedomtypes);

        if($params['submit']==1){
        }else if($params['submit']==2){
            $id=$params['id'];
            $list=$this->t1dao->getRow("SELECT * FROM app_license_popedom_item WHERE id='$id' ");
            $this->assign("list",$list);
        }
        $this->assign("params",$params);
    }


    public function doadditem($params){

        $status= $params['status'];
        if($status==''){
            $status=0;
        }
        if($params['power']!="A"){
            $Message['Success']=0;
            $Message['msg']="无权限";
            echo  json_encode($Message);
            exit;
        }
        $typeid=$params['typeid'];
        $itemname=urldecode($params['itemnames']);
        $itemname=$itemname;
        $mc_type=$params['mc_type'];
        $userid= $params['userid'];

        if($params['submit']=='1'){
            $typeexit=$this->t1dao->getOne("SELECT isdel FROM app_license_popedom_type WHERE id='$typeid'");
            if($typeexit==1){
                $Message['Success']=0;
                $Message['msg']="权限类型不存在，重新选择";
                echo  json_encode($Message);
                exit;
            }

            $isexit=$this->t1dao->getOne("SELECT id FROM app_license_popedom_item WHERE typeid='$typeid' and itemname='$itemname' and mc_type='$mc_type' and isdel=0");
            if($isexit>0){
                $Message['Success']=0;
                $Message['msg']="已存在，请勿重复添加";
                echo  json_encode($Message);
                exit;
            }else{
                $insertsql="INSERT INTO app_license_popedom_item SET typeid='$typeid',itemname='".$itemname."',status='$status',creatuser='$userid',creattime=NOW(),mc_type='".$mc_type."'";
                $this->t1dao->execute($insertsql);
                $itemid = $this->t1dao->insert_id();

                $data[0]=array(
                    "id"=>"$itemid",
                    "typeid"=>"$typeid",
                    "itemname"=>"$itemname",
                    "creattime"=>date('Y-m-d H:i:s'),
                    "mc_type"=>"$mc_type",
                    "status"=>"$status",
                    "creatuser"=>"$userid",
                    "updateadminid"=>"0",
                    "updateadminname"=>"0",
                    "updatetime"=>date("Y-m-d H:i:s"),
                    "isdel"=>"0"
                );

                $info[0]=array(
                    "tablename"=>"app_license_popedom_item",
                    "type"=>"insert",
                    "pk"=> "id",
                    "data"=>$data
                );

                $infos=json_encode($info);
                $data=$this->synchronize(1,$infos,$mc_type);

                //权限日志
                $logMes[]=array(
                    "opcontent"=>"添加权限选项".$itemname,
                    "optype"=>"9"
                );
                $this->dosysLog(1,$logMes,$mc_type);

                $Message['Success']=1;
                $Message['msg']="添加成功";
                echo  json_encode($Message);
            }
        }else if($params['submit']=='2'){
            $id=$params['id'];

            $typeexit=$this->t1dao->getOne("SELECT isdel FROM app_license_popedom_type WHERE id='$typeid'");
            if($typeexit==1){
                $Message['Success']=0;
                $Message['msg']="权限类型不存在，重新选择";
                echo  json_encode($Message);
                exit;
            }

            $isexit=$this->t1dao->getOne("SELECT id FROM app_license_popedom_item WHERE typeid='$typeid' and itemname='$itemname' and id!='$id' and mc_type='$mc_type' and isdel=0");

            if($isexit>0){
                $Message['Success']=0;
                $Message['msg']="已存在，请勿重复添加";
                echo  json_encode($Message);
                exit;
            }else{
                $updatesql="UPDATE app_license_popedom_item SET typeid='$typeid',status='$status',itemname='".$itemname."',updateadminid='$userid' WHERE id='$id'";
                $this->t1dao->execute($updatesql);

                $data[0]=array(
                    "id"=>"$id",
                    "typeid"=>"$typeid",
                    "itemname"=>"$itemname",
                    "status"=>"$status",
                    "updateadminid"=>"$userid",
                    "updateadminname"=>"0",
                    "updatetime"=>date("Y-m-d H:i:s")
                );

                $info[0]=array(
                    "tablename"=>"app_license_popedom_item",
                    "type"=>"update",
                    "pk"=> "id",
                    "data"=>$data
                );

                $infos=json_encode($info);
                $data=$this->synchronize(1,$infos,$mc_type);

                //权限日志
                $logMes[]=array(
                    "opcontent"=>"修改权限选项".$itemname,
                    "optype"=>"9"
                );
                $this->dosysLog(1,$logMes,$mc_type);


                $Message['Success']=1;
                $Message['msg']="修改成功";
                echo  json_encode($Message);
            }
        }
    }



    public function dosysLog($GUID,$loglist,$mc_type){
        $admin_infos= $this->t1dao->get_userinfo_byguid($GUID);
        $signcs=$admin_infos['SignCS'];
        $userid=$admin_infos['Uid'];
        $Mid=$admin_infos['Mid'];
        $truename=$admin_infos['TrueName'];
        $ip=$this->getip();

        foreach($loglist as $key=>$value){
            $data[]=array(
                "guid"=>"$GUID",
                "signcs"=>"$signcs",
                "userid"=>"$userid",
                "truename"=>"$truename",
                "opcontent"=>"$value[opcontent]",
                "optype"=>"$value[optype]",
                "ip"=>"$ip",
                "mc_type"=>"$mc_type",
                "mid"=>"$Mid",
                "create_time"=>date("Y-m-d H:i:s")
            );
        }

        $addinfo[0]=array(
            "tablename"=>"syslog",
            "type"=>"insert",
            "pk"=> "id",
            "data"=>$data
        );
        $addinfos=json_encode($addinfo);
        $this->synchronize($GUID,$addinfos,$mc_type);
    }



    public function sgloglist($params){
        $this->assign("params",$params);
        $this->assign("XGDCURL",XGDCURL);
        $mc_type=$params['mc_type'];
        $SignCS=$params['SignCS'];
        $GUID=$params['GUID'];
        $SGUID=$params['SGUID'];
        $isxlh= $params['isxlh']=='1' ? $params['isxlh']:'0';
        $this->assign("isxlh",$isxlh);
        $this->assign("SGUID",$SGUID);
        $this->assign("GUID",$GUID);
        $this->assign("mode",$params['mode']);
        $this->assign("mc_type",$params['mc_type']);

        $user_info=$this->t1dao->get_userinfo_byguid($SGUID);
        $uid=$user_info['Uid'];

        $where="";
        if($isxlh=='1'){
            $where.=" and createuser='$uid'";
        }else{

            $user_info=$this->t1dao->get_userinfo_byguid($GUID);
            $uid=$user_info['Uid'];

            $sql="SELECT app_license_detail.ID FROM app_license LEFT JOIN app_license_detail ON app_license.ID=app_license_detail.LID WHERE app_license_detail.SignCS='".$user_info['SignCS']."' AND app_license.MID='".$user_info['Mid']."' AND app_license.mc_type='$mc_type' AND app_license_detail.UseUser='".$user_info['UserName']."'";
            $detail_arr=$this->t1dao->getRow($sql);
            $lid= empty($detail_arr)?'0':$detail_arr['ID'];

            //权限分配
            $quanxsql="SELECT app_license_popedom.uid,app_license_popedom.creatuser FROM app_license_popedom LEFT JOIN app_license_popedom_item ON app_license_popedom.popid=app_license_popedom_item.id LEFT JOIN app_license_popedom_type ON  app_license_popedom_type.id=app_license_popedom_item.typeid WHERE  app_license_popedom.uid='$uid'  and app_license_popedom.isdel='0' and app_license_popedom_type.status=1 AND app_license_popedom_type.mc_type='$mc_type' AND app_license_popedom_type.isdel=0 AND app_license_popedom_item.status=1 AND app_license_popedom_item.mc_type='$mc_type' AND app_license_popedom_item.isdel=0 AND app_license_popedom_item.itemname='权限分配' ";
            $quanxList=$this->t1dao->getRow($quanxsql);
            if (!$quanxList) {
                $where.=" and createuser='$uid'";
            }
        }

        $total = $this->_dao->getOne( "SELECT COUNT(id) as c FROM sg_data_table_log where isdel='0' $where ");
        $page = $params['page'] == '' ? 1 : $params['page'];
        $url = "xgquanx.php";
        $per = 20;
        $start = ( $page - 1 ) * $per;
        unset( $params['page'] );

        ///echo $where;

        $sql = "select * from sg_data_table_log where isdel='0' $where order by createtime desc limit $start, $per";
        $log_info = $this->_dao->query($sql);

        $all_userid =array();
        foreach ($log_info as $key=>$info) {
            if (!in_array($info['createuser'], $all_userid)) {
                $all_userid[] = $info['createuser'];
            }
        }
        $all_userids=implode(',',$all_userid);

        $names=$this->t1dao->AQUERY("select Uid,TrueName from app_session_temp where Uid in($all_userids) group by Uid");

        foreach ($log_info as $key=>$info) {
            $log_info[$key]['truename'] = $names[$info['createuser']];

            $names = explode(',', $info['sheetname']);
            foreach($names as $ke => $sname) {
                $names[$ke] = "<a href='javascript:chakanexcel(\"".$info['id']."\", \"".$sname."\");'>$sname</a>";
            }
            $log_info[$key]['sheetname'] = implode(',', $names);


        }

        $this->assign( "log_info",$log_info);
        $pagebar = pagebar( $url, $params, $per, $page, $total );
        $this->assign( "pagebar", $pagebar );

    }




    function catdata($params)
    {
        $GUID = $params['GUID'];
        $mode = $params['mode'];
        $this->assign('mode', $mode);
        if($GUID==""){
            alert('GUID不能为空');
            exit;
        }
        $user_infos = $this->_dao->get_userinfo_byguid($GUID);
        if(empty($user_infos)){
            alert('用户不存在');
            exit;
        }
        $id = $params['id'];
        $sheet = $params['sheet'];
        $http_type = '//'.$_SERVER['HTTP_HOST'];
        $sql = "select filepath from sg_data_table_log where id = '".$id."'";
        $filepath = $this->_dao->getOne($sql);
        $filepath = explode('/public',$filepath);
        $file_url = $http_type.$filepath[1];



        $this->assign('file_url', $file_url);
        $this->assign('sheet', $sheet);
        $this->assign('GUID', $GUID);
        $this->assign('date', date("Y-m-d"));

    }


    private function getpagelabelnew($amount,$pagenum,$page,$url){
        $pagemax=($amount%$pagenum==0)?round($amount/$pagenum,0):(floor($amount/$pagenum)+1);

        //echo $amount." ".$pagenum." ".$page." ".$pagemax." ".$url;
        $label="<div class='flex justify-between flex-1 sm:hidden'>";
        if($page==1 || $pagemax==0){
            $label.="<span class='relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default leading-5 rounded-md'>
                    
			上一页
		</span>";
        }else{
            $label.="<a href='$url&page=".($page-1)."' class='relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 leading-5 rounded-md hover:text-gray-500 focus:outline-none focus:shadow-outline-blue focus:border-blue-300 active:bg-gray-100 active:text-gray-700 transition ease-in-out duration-150'>

			上一页
			</a>";
        }

        if($page==$pagemax || $pagemax==0){
            $label.="<span class='relative inline-flex items-center px-4 py-2 ml-3 text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default leading-5 rounded-md'>下一页</span>";
        }else{
            $label.="<a  href='$url&page=".($page+1)."'class='relative inline-flex items-center px-4 py-2 ml-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 leading-5 rounded-md hover:text-gray-500 focus:outline-none focus:shadow-outline-blue focus:border-blue-300 active:bg-gray-100 active:text-gray-700 transition ease-in-out duration-150'>

			下一页
			</a>";
        }
        $label.="</div>";
        $ye=10;
        $label.="<div class='hidden sm:flex-1 sm:flex sm:items-center sm:justify-between'>
		<div>
		<p class='text-sm text-gray-700 leading-5'>
		Showing
		<span class='font-medium'>16</span>
		to
		<span class='font-medium'>30</span>
		of
		<span class='font-medium'>41</span>
		results
		</p>
		</div>";

        if($page==1 || $pagemax==0) {//第一页
            $label.="<div><span class='relative z-0 inline-flex shadow-sm rounded-md'><span aria-disabled='true' aria-label='&amp;laquo; Previous'>
				<span class='relative inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default rounded-l-md leading-5' aria-hidden='true'>
					<svg class='w-5 h-5' fill='currentColor' viewBox='0 0 20 20'>
						<path fill-rule='evenodd' d='M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z' clip-rule='evenodd'></path>
					</svg>
				</span>
			</span>";
        }else {
            $label.="<div><span class='relative z-0 inline-flex shadow-sm rounded-md'><a href='$url&page=".($page-1)."' rel='prev' class='relative inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-l-md leading-5 hover:text-gray-400 focus:z-10 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue active:bg-gray-100 active:text-gray-500 transition ease-in-out duration-150' aria-label='&amp;laquo; Previous' ><svg class='w-5 h-5' fill='currentColor' viewBox='0 0 20 20'>
			<path fill-rule='evenodd' d='M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z' clip-rule='evenodd' />
		</svg></a>";
        }
        if($pagemax==0){
            $label.="<span aria-current='page'><span class='relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default leading-5'>1</span></span>";
        }else{
            if($page==1){
                $label.="<span aria-current='page'><span class='relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default leading-5'>1</span></span>";
            }else{
                $label.="<a href='$url&page=1' class='relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-gray-700 bg-white border border-gray-300 leading-5 hover:text-gray-500 focus:z-10 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue active:bg-gray-100 active:text-gray-700 transition ease-in-out duration-150' aria-label='Go to page 1'>1</a>";
            }
            $label1="<span class='relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-gray-700 bg-white border border-gray-300 leading-5 hover:text-gray-500 focus:z-10 focus:outline-none focus:border-blue-300  transition ease-in-out duration-150'>...</span>";

            for($i=2;$i<$pagemax;$i++){
                $label2="<span aria-current='page'><span class='relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default leading-5'>$i</span></span>";
                $label3="<a href='$url&page=$i' class='relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-gray-700 bg-white border border-gray-300 leading-5 hover:text-gray-500 focus:z-10 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue active:bg-gray-100 active:text-gray-700 transition ease-in-out duration-150' aria-label='Go to page $i'>$i</a>";

                if($page<$ye-1){//前10，
                    if( $i>$ye ) continue;
                    if($i==$ye){
                        $label.=$label1;
                    }elseif($i==$page){
                        $label.=$label2;
                    }else{
                        $label.=$label3;
                    }
                }else if($pagemax-$page<$ye-1){//后10
                    if( $i<$pagemax-$ye) continue;
                    if($i==$pagemax-$ye){
                        $label.=$label1;
                    }elseif($i==$page){
                        $label.=$label2;
                    }else{
                        $label.=$label3;
                    }
                }else{//中间数
                    if( $i<$page-2 || $i>$page+$ye-2) continue;
                    if($i==$page-2 || $i==$page+$ye-2){
                        $label.=$label1;
                    }elseif($i==$page){
                        $label.=$label2;
                    }else{
                        $label.=$label3;
                    }
                }

            }
        }

        if($pagemax>1) {
            if($pagemax!=$page)
                $label.="<a href='$url&page=$pagemax'  class='relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-gray-700 bg-white border border-gray-300 leading-5 hover:text-gray-500 focus:z-10 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue active:bg-gray-100 active:text-gray-700 transition ease-in-out duration-150' aria-label='Go to page $pagemax'>$pagemax</a>"; //最后一页
            else
                $label.="<span aria-current='page'><span class='relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default leading-5'>$page</span></span>";
        }
        if($page==$pagemax || $pagemax==0){
            $label.="<span aria-disabled='true' aria-label='Next &amp;raquo;'>
				<span class='relative inline-flex items-center px-2 py-2 -ml-px text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default rounded-r-md leading-5' aria-hidden='true'>
					<svg class='w-5 h-5' fill='currentColor' viewBox='0 0 20 20'>
						<path fill-rule='evenodd' d='M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z' clip-rule='evenodd' />
					</svg>
				</span>
			</span>";
        } else {$label.="<a href='".$url."&page=".($page+1)."' rel='next' class='relative inline-flex items-center px-2 py-2 -ml-px text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-r-md leading-5 hover:text-gray-400 focus:z-10 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue active:bg-gray-100 active:text-gray-500 transition ease-in-out duration-150' aria-label='Next &amp;raquo;'>
				<svg class='w-5 h-5' fill='currentColor' viewBox='0 0 20 20'>
					<path fill-rule='evenodd' d='M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z' clip-rule='evenodd'></path>
				</svg></a>";
        }

        $label.="</span></div>";

        return "<div align='right' style='margin-top:5px; font-size:12px'> <nav role='navigation' aria-label='Pagination Navigation' class='flex items-center justify-between'>".$label."</nav></div>";
    }



    function getAdminuserXGLogin($params){
        $username=$params['username'];
        $passwordmd5=$params['passwordmd5'];
        $ip=$this->getip();
        $isSuccess = 1;
        if(trim($username)!="" && trim($passwordmd5)!=""){
            $userList=$this->maindao->getRow("SELECT * FROM  adminuser WHERE username='".$username."' and passwordmd5='".$passwordmd5."' ");
        }
        $return_arr = array(
            'Success' => $isSuccess,
            'Results' => $userList,
        );
        echo json_encode($return_arr);
    }

    function getAdminuserLogin($params){
        $username=$params['username'];
        $passwordmd5=$params['passwordmd5'];
        $ip=$this->getip();
        $isSuccess = 1;
        $start =  strtotime('-1 day', time());
        $failedloginList=$this->maindao->getRow("SELECT count,createtime FROM  sys_common_failedlogin WHERE username='".$username."' and ip='".$ip."' ");
        
        if(!empty($failedloginList) && $failedloginList['count']>=6 && strtotime($failedloginList['createtime'])>$start ){
            $isSuccess = -1;
        }else{
            if(!empty($failedloginList) && $failedloginList['count']>=6){
                $failedloginList['count']=0;
            }
            if($failedloginList['createtime']<date("Y-m-d 00:00:00")){
                $failedloginList['count']=0;
            }
            if(trim($username)!="" && trim($passwordmd5)!=""){
                $userList=$this->maindao->getRow("SELECT * FROM  adminuser WHERE username='".$username."' and passwordmd5='".$passwordmd5."' ");
            }

            $arrtem = $params;
            $actionstr="";
            foreach ($arrtem as $k => $v) {
                $actionstr .= "&" . $k . "=" . $v;
            }

            if (empty($userList)) {
                $count=1;
                if(!empty($failedloginList)){
                    $count=$failedloginList['count']+1;
                    $this->maindao->execute( "UPDATE sys_common_failedlogin SET count='".$count."', createtime=NOW() where id='".$failedloginList['id']."'");
                }else{
                    $this->maindao->execute( "INSERT INTO sys_common_failedlogin SET count='".$count."',username='".$username."',ip='".$this->getip()."', createtime=NOW()");
                }
                $this->t1dao->execute( "INSERT INTO steelhome_t1.app_logs SET username='".$username."',Mid='0', Uid='0',  ActionName='Login',Actionstr='$actionstr', ActionDate=NOW(),ActionIp='".$ip."', SystemType='', SystemVersion='',MessageId='', MessageTitle='数字化转型登录', MessageDesc='登录失败' , mc_type='0'");
                if($count>=6){
                    $isSuccess = -1;
                }else{
                    $isSuccess = 0;
                }
            }else{
               
                if(!empty($failedloginList)){
                    $this->maindao->execute( "UPDATE sys_common_failedlogin SET count='0' where id='".$failedloginList['id']."'");
                }
                $this->t1dao->execute( "INSERT INTO steelhome_t1.app_logs SET  username='".$username."',Mid='".$userList['mid']."', Uid='".$userList['id']."',  ActionName='Login',Actionstr='$actionstr', ActionDate=NOW(),ActionIp='".$ip."', SystemType='', SystemVersion='',MessageId='', MessageTitle='数字化转型登录', MessageDesc='登录成功' , mc_type='0'");
                
                $userList["truename"]= $userList["truename"];
            }
        }
        
        $return_arr = array(
            'Success' => $isSuccess,
            'Results' => $userList,
        );
        echo json_encode($return_arr);
    }






    public function userindex($params)
    {

        $GUID = $params['GUID'];

        //echo $GUID;exit;
        $mode = $params['mode'];//1=客户端，2=安卓，3=iOS
        if($GUID==""){
            goURL("xgquanx.php?view=show");
            exit;
        }
        $user_info=$this->t1dao->get_userinfo_byguid($GUID);

        if(empty($user_info)){
            alert('用户不存在');
            exit;
        }




        $mc_type=$params['mc_type']!=''?$params['mc_type']:$user_info['mc_type'];

        $uid=$user_info['Uid'];
        $SignCS=$user_info['SignCS'];
        $MID=$user_info['Mid'];
        $UserName=$user_info['UserName'];


        if($params['isxg']==1){
            $MID=951;
        }else if($params['isxg']==2){
            $MID=512;
        }else if($params['isxg']==3){
            $MID=186;
        }
        $where="";
        if($params['mc_type']==3){
            $where.=" AND (app_license_popedom_item.itemname='权限分配' or app_license_popedom_item.itemname='二级权限分配') ";
        }else{
            $where.=" AND app_license_popedom_item.itemname='权限分配'  ";
        }

        //权限分配的创建人
        $quanxsql="SELECT app_license_popedom_item.itemname,app_license_popedom_item.id FROM app_license_popedom LEFT JOIN app_license_popedom_item ON app_license_popedom.popid=app_license_popedom_item.id LEFT JOIN app_license_popedom_type ON  app_license_popedom_type.id=app_license_popedom_item.typeid WHERE  app_license_popedom.uid='$uid'  and app_license_popedom.isdel='0' and app_license_popedom_type.status=1 AND app_license_popedom_type.mc_type='$mc_type' AND app_license_popedom_type.isdel=0 AND app_license_popedom_item.status=1 AND app_license_popedom_item.mc_type='$mc_type' AND app_license_popedom_item.isdel=0 $where ";
        $quanxList=$this->t1dao->AQUERY($quanxsql);


        if(empty($quanxList) ){
            goURL("xgquanx.php?view=show");
            exit;
        }



        $where="";
        if($params['searchword']!=""){
            if (preg_match("/([\x81-\xfe][\x40-\xfe])/", $params['searchword'], $match)) {
                $where.=" and truename like'%".$params['searchword']."%' ";
            }else{
                $where.=" and (truename like'%".$params['searchword']."%' or username like'%".$params['searchword']."%') ";
            }
        }


        if(!isset($quanxList['权限分配']) && isset($quanxList['二级权限分配']) && $params['mc_type']==3){
            $all_relation=$this->t1dao->AQUERY("select id,userid from app_adminuser_relation where addid='".$uid."' and isdel=0 group by userid ");
            $where.=" and id in ('".implode("','",$all_relation)."') ";
        }

        $License=$this->t1dao->getRow("SELECT * FROM app_license WHERE MID='$MID' and mc_type='$mc_type'");

        if($mc_type==4){
            $userList=$this->maindao->query("SELECT * FROM  adminuser WHERE isxgclient='1' and mid='$MID' and state=1 and xglogonflag=1 $where order by id asc ");
            $userList2=$this->maindao->query("SELECT * FROM  adminuser WHERE isxgclient='1' and mid='$MID' and state=1 and xglogonflag=0 $where order by id asc ");
        }else{
            $userList=$this->maindao->query("SELECT * FROM  adminuser WHERE isclient='1' and mid='$MID' and state=1 and logonflag=1 and id!='453344' $where order by id asc ");
            $userList2=$this->maindao->query("SELECT * FROM  adminuser WHERE isclient='1' and mid='$MID' and state=1 and logonflag=0 $where order by id asc ");
        }
       
        $this->assign('userList', $userList);
        $this->assign('userList2', $userList2);
        $this->assign("GUID",$GUID);
        $this->assign("uid",$uid);
        $this->assign("mode",$mode);
        $this->assign("mc_type",$mc_type);
        $this->assign("MID",$MID);
        $this->assign("XGDCURL",XGDCURL);
        $this->assign("SGDCURL",SGDCURL.'/web');
        $this->assign("user_info",$user_info);
        $this->assign("params",$params);
        $this->assign("licenses",$License);

    }


    public function setuser($params){
        $LID=$params['ID'];
        $LLIST = $this->t1dao-> getRow("select * from app_license where ID = '$LID' ");

        $mc_type=$params['mc_type'];

        if($mc_type==4){
            $accounts=$this->maindao->query("SELECT * FROM  adminuser WHERE mid='".$params['MID']."'  and username!='' and state=1 and isxgclient=0 order by id asc");
        }else{
            $accounts=$this->maindao->query("SELECT * FROM  adminuser WHERE mid='".$params['MID']."'  and username!='' and state=1 and isclient=0 order by id asc");
        }
       

        //echo "SELECT * FROM  adminuser WHERE mid='".$params['MID']."'  and username!='' and state=1 and isclient=0 order by username asc";

        $this->assign("accounts",$accounts);
        $this->assign("LLIST",$LLIST);
        $this->assign("mc_type",$mc_type);
        $this->assign("params",$params);
    }




    public function dosetuser($params){

        $type=$params['type'];
        $GUID=$params['GUID'];


        if($GUID==""){
            $return_arr = array(
                'Success' => 0,
                'Msg' => 'GUID不能为空',
            );
            echo json_encode($return_arr);exit;
        }
        $user_infos = $this->t1dao->get_userinfo_byguid($GUID);
        if(empty($user_infos)){
            $return_arr = array(
                'Success' => 0,
                'Msg' => '用户不存在',
            );
            echo json_encode($return_arr);exit;
        }

        //张康账号才能加
        /*if($user_infos['Uid']!='147803'){
            $return_arr = array(
                'Success' => 0,
                'Msg' =>  iconv('GB2312','UTF-8','无权限'),
            );
            echo json_encode($return_arr);exit;
        }*/



        $adminid= $user_infos['Uid'];
        $adminname=$user_infos['UserName'];

        $array = array("LID","mc_type");
        $list = $this -> getdata($array, $params);
        $IP=$this->getip();
        $Remarks=trim($params['Remarks']);


        if($type=='1'){
            $Userid=$params['selectUser'];
            $userList=$this->maindao->getRow("SELECT *  FROM adminuser WHERE  id=$Userid");
            $UseUser=$userList['username'];
            $truename=$userList['truename'];
            $passwordmd5=$userList['passwordmd5'];
            $mobil=$userList['mobil'];

                
                if($params['mc_type']==4){
                    $this->maindao->execute("update adminuser set isxgclient='".$params['isclient']."',xglogonflag=1 where id='$Userid'");
                }else{
                    $this->maindao->execute("update adminuser set isclient='".$params['isclient']."',logonflag=1 where id='$Userid'");
                }



            }else if($type=='2'){
                $passwordmd5=md5(trim($params['password']));
                $UseUser=trim($params['UseUser']);
                $truename= trim($params['truename']);
                if($params['mc_type']==4){
                    $isql=",xglogonflag=1,logonflag=0,isxgclient='".$params['isclient']."' ";
                }else{
                    $isql=",logonflag=1,xglogonflag=0,isclient='".$params['isclient']."' ";
                }


                $insertsql="insert into adminuser set state=1,username='".$UseUser."',truename='".$truename."',password='".$params['password']."',passwordmd5='$passwordmd5',mid='".$params['MID']."' $isql";
                $this->maindao->execute($insertsql);
                $Userid = $this->maindao->insert_id();
            }
            $this->t1dao->execute("insert into app_adminuser_relation set userid=$Userid, addid=$adminid,mc_type='".$params['mc_type']."',createdate=NOW()");

        $idata[0]=array(
            "id"=>$Userid,
            "truename"=>"$truename",
            "username"=>"$UseUser",
            "passwordmd5"=>"$passwordmd5",
            "state"=>"1",
            "mobil"=>"$mobil",
            "mid"=>"$params[MID]",
            "isclient"=>"$params[isclient]"
        );

        $iinfo[0]=array(
            "tablename"=>"adminuser",
            "type"=>"insert",
            "pk"=> "id",
            "data"=>$idata
        );
        // $iinfos=json_encode($iinfo);
        // $this->synchronize($GUID,$iinfos,$params['mc_type']);

        $isexit=$this->t1dao->getOne("select count(*) from app_license_detail where Status='1' and  UseUser='$UseUser' and mc_type='$params[mc_type]'");
        if($isexit>0){

        }else{
            $LicenseKeyD=$this->makelicense();
            $this->t1dao->execute("INSERT INTO app_license_detail SET $list,Remarks='$Remarks',LicenseKeyD='$LicenseKeyD',UseUser='$UseUser',CreateDate=NOW(),SignCS='',UserIp='$IP',UserDate=NOW(),CreateUser='$adminname',Status='1',IsUpdate='1',ExportData='1'");
            $ID = $this->t1dao->insert_id();

            $LicenseKeyD2=$this->makelicense();
            $this->t1dao->execute("INSERT INTO app_license_detail SET $list,Remarks='$Remarks',LicenseKeyD='$LicenseKeyD2',UseUser='$UseUser',CreateDate=NOW(),SignCS='',UserIp='$IP',UserDate=NOW(),CreateUser='$adminname',Status='1',IsUpdate='1',ExportData='1'");
            $ID2 = $this->t1dao->insert_id();


            $LicenseKeyD3=$this->makelicense();
            $this->t1dao->execute("INSERT INTO app_license_detail SET $list,Remarks='$Remarks',LicenseKeyD='$LicenseKeyD3',UseUser='$UseUser',CreateDate=NOW(),SignCS='',UserIp='$IP',UserDate=NOW(),CreateUser='$adminname',Status='1',IsUpdate='1',ExportData='1'");
            $ID3 = $this->t1dao->insert_id();

            $LicenseNums=$this->t1dao->getOne("SELECT LicenseNums FROM app_license WHERE ID='".$params['LID']."'");
            $nums=$this->t1dao->getOne("SELECT count(*) FROM app_license_detail WHERE LID='".$params['LID']."'");


            if($nums > $LicenseNums){
                $LicenseNums=$LicenseNums+3;
                $this->t1dao->execute("UPDATE app_license SET LicenseNums='$LicenseNums' WHERE ID='".$params['LID']."'");
            }



            $data[0]=array(
                "ID"=>$ID,
                "LID"=>"$params[LID]",
                "UseUser"=>"$UseUser",
                "Remarks"=>"$params[Remarks]",
                "CreateDate"=>date('Y-m-d H:i:s'),
                "UserDate"=>date('Y-m-d H:i:s'),
                "mc_type"=>"$params[mc_type]",
                "LicenseKeyD"=>"$LicenseKeyD",
                "UserIp"=>"$IP",
                "Status"=>"1",
                "IsUpdate"=>"1",
                "ExportData"=>"1",
                "CreateUser"=>"$adminname",
                "IsEnglish"=>"0",
                "SignCS"=>""
            );
            $data[1]=array(
                "ID"=>$ID,
                "LID"=>"$params[LID]",
                "UseUser"=>"$UseUser",
                "Remarks"=>"$params[Remarks]",
                "CreateDate"=>date('Y-m-d H:i:s'),
                "UserDate"=>date('Y-m-d H:i:s'),
                "mc_type"=>"$params[mc_type]",
                "LicenseKeyD"=>"$LicenseKeyD2",
                "UserIp"=>"$IP",
                "Status"=>"1",
                "IsUpdate"=>"1",
                "ExportData"=>"1",
                "CreateUser"=>"$adminname",
                "IsEnglish"=>"0",
                "SignCS"=>""
            );
            $data[2]=array(
                "ID"=>$ID,
                "LID"=>"$params[LID]",
                "UseUser"=>"$UseUser",
                "Remarks"=>"$params[Remarks]",
                "CreateDate"=>date('Y-m-d H:i:s'),
                "UserDate"=>date('Y-m-d H:i:s'),
                "mc_type"=>"$params[mc_type]",
                "LicenseKeyD"=>"$LicenseKeyD3",
                "UserIp"=>"$IP",
                "Status"=>"1",
                "IsUpdate"=>"1",
                "ExportData"=>"1",
                "CreateUser"=>"$adminname",
                "IsEnglish"=>"0",
                "SignCS"=>""
            );

            $iinfo[1]=array(
                "tablename"=>"app_license_detail",
                "type"=>"insert",
                "pk"=> "ID",
                "data"=>$data
            );
        }

        $qdata[0]=array(
            "id"=>$Userid,
            "isclient"=>"$params[isclient]"
        );

        $iinfo[2]=array(
            "tablename"=>"adminuser",
            "type"=>"update",
            "pk"=> "id",
            "data"=>$qdata
        );


        $infos=json_encode($iinfo);
        $data=$this->synchronize($GUID,$infos,$params['mc_type']);

        $logMes[0]=array(
            "opcontent"=>"添加新用户".$UseUser,
            "optype"=>"9"
        );
        $this->dosysLog($GUID,$logMes,$params['mc_type']);
        $return_arr = array(
            'Success' => 1,
            'Msg' =>  '分配成功',
        );
        echo json_encode($return_arr);exit;
    }

    public function addLicenseKeyD($params){
        $GUID=$params['GUID'];
        if($GUID==""){
            $return_arr = array(
                'Success' => 0,
                'Msg' =>'GUID不能为空',
            );
            echo json_encode($return_arr);exit;
        }
        $user_infos = $this->t1dao->get_userinfo_byguid($GUID);
        if(empty($user_infos)){
            $return_arr = array(
                'Success' => 0,
                'Msg' => '用户不存在',
            );
            echo json_encode($return_arr);exit;
        }
        $adminid= $user_infos['Uid'];
        $adminname=$user_infos['UserName'];
        $uid=$params['Uid'];

        $userList=$this->maindao->getRow("SELECT * FROM  adminuser WHERE id='$uid' ");

        $mc_type=$params['mc_type'];
        $LID=$params['LID'];
        $IP=$this->getip();



        $details=$this->t1dao->getRow("select * from app_license_detail where UseUser='' and mc_type='$mc_type' and LID='$LID' and Status=1 limit 1");
        if(!empty($details)){
            $LicenseKeyD=$details['LicenseKeyD'];
            $ID=$details['ID'];
            $this->t1dao->execute("UPDATE app_license_detail SET Remarks='$userList[truename]',UseUser='$userList[username]' WHERE ID='$ID'");

            $ddata[0]=array(
                "ID"=>"$ID",
                "Remarks"=>"$userList[truename]",
                "UseUser"=>"$userList[username]"
            );
            $dinfo[0]=array(
                "tablename"=>"app_license_detail",
                "type"=>"update",
                "pk"=> "ID",
                "data"=>$ddata
            );

            $dinfos=json_encode($dinfo);
            $this->synchronize($params['GUID'],$dinfos,$params['mc_type']);


        }else{
            $LicenseKeyD=$this->makelicense();
            $this->t1dao->execute("INSERT INTO app_license_detail SET LID='$LID',mc_type='$mc_type',LicenseKeyD='$LicenseKeyD',UseUser='$userList[username]',CreateDate=NOW(),SignCS='',UserIp='$IP',UserDate=NOW(),CreateUser='$adminname',Status='1',IsUpdate='1',ExportData='1'");
            $ID = $this->t1dao->insert_id();

            $LicenseNums=$this->t1dao->getOne("SELECT LicenseNums FROM app_license WHERE ID='$LID'");
            $nums=$this->t1dao->getOne("SELECT count(*) FROM app_license_detail WHERE LID='$LID'");
            if($nums > $LicenseNums){
                $LicenseNums++;
                $this->t1dao->execute("UPDATE app_license SET LicenseNums='$LicenseNums' WHERE ID='$LID'");
            }

            $data[0]=array(
                "ID"=>$ID,
                "LID"=>"$LID",
                "UseUser"=>"$userList[username]",
                "Remarks"=>"$userList[truename]",
                "CreateDate"=>date('Y-m-d H:i:s'),
                "UserDate"=>date('Y-m-d H:i:s'),
                "mc_type"=>"$mc_type",
                "LicenseKeyD"=>"$LicenseKeyD",
                "UserIp"=>"$IP",
                "Status"=>"1",
                "IsUpdate"=>"1",
                "ExportData"=>"1",
                "CreateUser"=>"$adminname",
                "IsEnglish"=>"0",
                "SignCS"=>""
            );

            $iinfo[0]=array(
                "tablename"=>"app_license_detail",
                "type"=>"insert",
                "pk"=> "ID",
                "data"=>$data
            );
            $infos=json_encode($iinfo);
            $data=$this->synchronize($GUID,$infos,$params['mc_type']);
        }



        $return_arr = array(
            'Success' => 1,
            'LicenseKeyD' => $LicenseKeyD,
            'ID' => $ID,
        );
        echo json_encode($return_arr);

    }



    public function setpower2($params){
        $GUID=$params['GUID'];
        $mode = $params['mode'];//1=客户端，2=安卓，3=iOS
        $uid=$params['Uid'];

        if($GUID==""){
            alert('GUID不能为空');
            goback();
            exit;
        }

        //获取用户信息
        $user_infos = $this->t1dao->get_userinfo_byguid($GUID);
        $userid= $user_infos['Uid'];
        $MID=$user_infos['Mid'];

        if($params['isxg']==1){
            $MID=951;
        }else if($params['isxg']==2){
            $MID=512;
        }



        if(empty($user_infos)){
            alert('用户不存在');
            goback();
            exit;
        }

        $mc_type= $params['mc_type']!='' ? $params['mc_type'] : $user_infos['mc_type'];


        //权限分配的创建人
        $quanxsql="SELECT app_license_popedom.uid,app_license_popedom.creatuser FROM app_license_popedom LEFT JOIN app_license_popedom_item ON app_license_popedom.popid=app_license_popedom_item.id LEFT JOIN app_license_popedom_type ON  app_license_popedom_type.id=app_license_popedom_item.typeid WHERE  app_license_popedom.uid='$userid' and app_license_popedom.isdel='0' and app_license_popedom_type.status=1 AND app_license_popedom_type.mc_type='$mc_type' AND app_license_popedom_type.isdel=0 AND app_license_popedom_item.status=1 AND app_license_popedom_item.mc_type='$mc_type' AND app_license_popedom_item.isdel=0 AND app_license_popedom_item.itemname='权限分配' ";
        $quanxList=$this->t1dao->getRow($quanxsql);
        $creatuser=$quanxList['creatuser'];

        if($uid==$userid || $creatuser==$uid || $creatuser=='-1'){
            $power= 1;
        }else{
            $power= 0;
        }


        if(empty($quanxList)){
            $nopower= 1;
        }else{
            $nopower= 0;
        }

        $power2= 0;
        if($mc_type==3){
            $userpopedom = $this->t1dao->get_self_popedom($userid,$mc_type);
            $where="";
            if(!in_array("权限分配",$userpopedom) && in_array("二级权限分配",$userpopedom)){
                $nopower= 0;
                $power2= 1;
                $popedomids=array();
                foreach($userpopedom as $key=>$value){
                    if($value!="二级权限分配"){
                        $popedomids[]=$key;
                    }
                }
                $where.=" and app_license_popedom_item.id in ('".implode("','",$popedomids)."')";      
            }
        }
        

        //权限选项
        $popedomsql="SELECT app_license_popedom_item.typeid,app_license_popedom_item.id,app_license_popedom_type.typename,app_license_popedom_item.itemname FROM app_license_popedom_type LEFT JOIN app_license_popedom_item ON app_license_popedom_type.id=app_license_popedom_item.typeid  WHERE app_license_popedom_type.status=1 AND app_license_popedom_type.mc_type='$mc_type' AND app_license_popedom_type.isdel=0 AND app_license_popedom_item.status=1 AND app_license_popedom_item.mc_type='$mc_type' AND app_license_popedom_item.isdel=0 $where";
        $popedomList=$this->t1dao->query($popedomsql);

        //获取拥有的权限
        $allpopedom = $this->t1dao->get_popedom_byid($ID,$uid,$mc_type);

        if($mc_type==4){
            $allpopedomuser = $this->t1dao->get_createpopedom_byid($ID,$uid,$mc_type,$userid);
        }
       
        $qxList=array();
        foreach($popedomList as $pkey=>$pvalue){
            $arr['itemname']=$pvalue['itemname'];
            $arr['id']=$pvalue['id'];
            $arr['on']=in_array($pvalue['id'],$allpopedom)?'1':'0';
            
           

            if($mc_type==4){
                $arr['ischeck']=isset($allpopedomuser[$pvalue['id']])?0:1;

                if( $arr['ischeck']==0){
                    $qxList[$pvalue['typename']]['checknum']=$qxList[$pvalue['typename']]['checknum']+1;
                }
            }

            $qxList[$pvalue['typename']]['data'][]=$arr;
            $qxList[$pvalue['typename']]['count']=count($qxList[$pvalue['typename']]['data']);
            $qxList[$pvalue['typename']]['id']=$pvalue['typeid'];
            if($arr['on']=='0' && !isset($qxList[$pvalue['typename']]['all']) ){
                $qxList[$pvalue['typename']]['all']=1;
            }
        }


        $this->assign("GUID",$GUID);
        $this->assign("qxList",$qxList);
        $this->assign("mode",$mode);
        $this->assign("mc_type",$mc_type);
        $this->assign("uid",$uid);
        $this->assign("userid",$userid);
        $this->assign("power",$power);
        $this->assign("nopower",$nopower);
        $this->assign("params",$params);
        $this->assign("power2",$power2);
    }


    public function setpower3($params){
        $GUID=$params['GUID'];
        $mode = $params['mode'];//1=客户端，2=安卓，3=iOS
        $uid=$params['Uid'];

        if($GUID==""){
            alert('GUID不能为空');
            goback();
            exit;
        }

        //获取用户信息
        $user_infos = $this->t1dao->get_userinfo_byguid($GUID);
        $userid= $user_infos['Uid'];
        $MID=$user_infos['Mid'];

        if($params['isxg']==1){
            $MID=951;
        }else if($params['isxg']==2){
            $MID=512;
        }



        if(empty($user_infos)){
            alert('用户不存在');
            goback();
            exit;
        }

        $mc_type= $params['mc_type']!='' ? $params['mc_type'] : $user_infos['mc_type'];

        $license=$this->t1dao->getRow("select * from app_license where MID='".$MID."' and mc_type='$mc_type'");

        //echo "select * from app_license where MID='".$MID."' and mc_type='$mc_type'";
        //print_r($license);

        $this ->assign("params", $params);
        $pianqu = $this->_dao->aquery("select pqid,pianqu from sg_PianQu");
        $user = $this->maindao->getRow("select id, username,truename  from adminuser where id='".$uid."'  ");
        $info = $this->t1dao->getRow("select * from app_license_privilege where `lid` = '".$license['ID']."' and `uid` ='".$uid."' and `mc_type`='".$mc_type."' order by id desc limit 1");

        $privilege_arr=explode(",",$info['privilege']);
        $ordernu_arr=explode(",",$info['orderno']);

        // echo '<pre>';
        // print_r($ordernu_arr);
        // print_r($privilege_arr);

        // print_r($GLOBALS['power']);
        $this->assign("GLOBALS_power",$GLOBALS['power']);
        $this->assign("pianqu",$pianqu);
        $this->assign("info",$info);
        $this->assign("license",$license);
        $this->assign("ordernu_arr",$ordernu_arr);
        $this->assign("privilege_arr",$privilege_arr);
    }



    public function dosetpower3($params){

        for($i=0;$i<count($GLOBALS['privilege']);$i++){
            $privilege_a.=($params['privilege'][$i]==''?0:$params['privilege'][$i]).",";
            $order.=($params['order'][$i]==''?0:$params['order'][$i]).",";
        }
        $privilege_a=substr($privilege_a, 0, -1);//权限值


        $orderno=substr($order, 0, -1);//序号值

        $user = $this->maindao->getRow("select id, username,truename,mid  from adminuser where id='".$params['uid']."'  ");

        $offset=array_keys(explode(',',$privilege_a),'1',true);
        if($offset){
            $zz=array(
                "1,([0-9]*),([0-9]*),([0-9]*),([0-9]*),([0-9]*),([0-9]*)$",
                "([0-9]*),1,([0-9]*),([0-9]*),([0-9]*),([0-9]*),([0-9]*)$",
                "([0-9]*),([0-9]*),1,([0-9]*),([0-9]*),([0-9]*),([0-9]*)$",
                "([0-9]*),([0-9]*),([0-9]*),1,([0-9]*),([0-9]*),([0-9]*)$",
                "([0-9]*),([0-9]*),([0-9]*),([0-9]*),1,([0-9]*),([0-9]*)$",
                "([0-9]*),([0-9]*),([0-9]*),([0-9]*),([0-9]*),1,([0-9]*)$",
                "([0-9]*),([0-9]*),([0-9]*),([0-9]*),([0-9]*),([0-9]*),1$",

            );
            $order=explode(',',$orderno);
            //$xgqx=array();
            foreach($offset as $item){
                //echo "SELECT  id, uid,truename FROM `app_license_privilege` WHERE privilege  REGEXP '".$zz[$item]."' and lid='" . $params['id'] . "' and mc_type='".$params['mc_type']."' and uid !='".$params['uid']."'";

                //echo "SELECT  id, uid,truename FROM `app_license_privilege` WHERE privilege  REGEXP '".$zz[$item]."' and orderno  REGEXP '".str_replace("1",$order[$item],$zz[$item])."' and lid='" . $params['id'] . "' and mc_type='".$params['mc_type']."' and uid !='".$params['uid']."'";

                $jc1 = $this->t1dao->getRow("SELECT  id, uid,truename FROM `app_license_privilege` WHERE privilege  REGEXP '".$zz[$item]."' and orderno  REGEXP '".str_replace("1",$order[$item],$zz[$item])."' and lid='" . $params['id'] . "' and mc_type='".$params['mc_type']."' and uid !='".$params['uid']."'");
                if($jc1){
                    $key1.=$jc1['id'].",";
                    $value1.=$item.",";
                    $tsy.=$GLOBALS['privilege'][$item]."分管领导是". $jc1['truename'].",序号为".$order[$item]."\\n";
                }
            }
            if($tsy){
                $tsy.="分管领导序号不能重复";
            }
            if($key1){
                $key1=substr($key1, 0, -1);
                $value1=substr($value1, 0, -1);
                //$orderno.=substr($orderno, 0, -1);
            }
        }

        if($tsy){
            $Message['Success']=2;
            $Message['Msg']=$tsy;
            echo json_encode($Message);
            exit;
        }



        $cb_privilege_a=$params['cb_privilege_0'];//权限值

        //end by xr

        $info = $this -> t1dao -> getRow("select * from app_license_privilege where lid = '" . $params['id'] . "' and uid ='".$params['uid']."' and mc_type='".$params['mc_type']."'");
        $pianqu = $params['pianqu'];
        if($info){
            $this ->t1dao -> execute("update app_license_privilege set cb_privilege='".$cb_privilege_a."', username='".$user['username']."',truename='".$user['truename']."',privilege='".$privilege_a."',orderno='".$orderno."',pqid='".$pianqu."' where lid = '".$params['id']."' and uid ='".$params['uid']."' and mc_type='".$params['mc_type']."'");

            if($params['mc_type']=='2'){
                $prdata[0]=array(
                    "id"=>$info['id'],
                    "cb_privilege"=>$cb_privilege_a,
                    "truename"=>$user['truename'],
                    "username"=>$user['username'],
                    "privilege"=>$privilege_a,
                    "orderno"=>$orderno,
                    "pqid"=>$pianqu
                );
                $prinfo[0]=array(
                    "tablename"=>"app_license_privilege",
                    "type"=>"update",
                    "pk"=> "id",
                    "data"=>$prdata
                );
                $prinfos=json_encode($prinfo);
                $this->synchronize(1,$prinfos,$params['mc_type']);

            }

        }else{
            $this->t1dao -> execute("insert into app_license_privilege set cb_privilege='".$cb_privilege_a."',pqid='".$pianqu."', privilege='".$privilege_a."',orderno='".$orderno."' ,username='".$user['username']."',truename='".$user['truename']."',lid = '" . $params['id'] . "' , uid ='".$params['uid']."' , mc_type='".$params['mc_type']."'");

            $prid = $this->t1dao->getOne("SELECT LAST_INSERT_ID();",0);
            if($params['mc_type']=='2'){
                $prdata[0]=array(
                    "id"=>$prid,
                    "cb_privilege"=>$cb_privilege_a,
                    "truename"=>$user['truename'],
                    "username"=>$user['username'],
                    "privilege"=>$privilege_a,
                    "orderno"=>$orderno,
                    "pqid"=>$pianqu,
                    "lid"=>$params['id'],
                    "uid"=>$params['uid'],
                    "mc_type"=>$params['mc_type'],

                );
                $prinfo[0]=array(
                    "tablename"=>"app_license_privilege",
                    "type"=>"insert",
                    "pk"=> "id",
                    "data"=>$prdata
                );
                $prinfos=json_encode($prinfo);
                $this->synchronize(1,$prinfos,$params['mc_type']);
            }

        }

        $Message['Success']=1;
        echo json_encode($Message);
        exit;

    }


    public function dosetpower($params){
        $qxList=$params['qx'];
        $GUID=$params['GUID'];
        $uid=$params['uid'];
        $LID=0;
        $mc_type=$params['mc_type'];
        $adminid=$params['userid'];
        //获取用户信息
        $admin_infos = $this->t1dao->get_userinfo_byguid($GUID);
        $adminusername=$admin_infos['TrueName'];
        $signcs=$admin_infos['SignCS'];
        //获取信息
        $userList=$this->maindao->getRow("SELECT * FROM  adminuser WHERE id='$uid' ");
        $UserName=$userList['username'];
        $TrueName=$userList['truename'];
        $IP=$this->getip();
        $addpopedom=array();
        $editpopedom=array();
        $addlog=array();

        $qxList=$this->changearray($qxList);


        foreach($qxList as $k=>$qk){
            $qxs=explode('|',$qk);
            $popid=$qxs[0];
            $qv=$qxs[1];

            $popedoms=$this->t1dao->getRow("SELECT * FROM app_license_popedom WHERE  uid='$uid' AND mc_type='$mc_type' AND popid='$popid'");

            if(empty($popedoms)){
                $insertsql="INSERT INTO app_license_popedom SET lid='$LID',uid='$uid', username='$UserName',truename='$TrueName',popid='$popid',creattime=NOW(),creatuser='$adminid',mc_type='$mc_type'";
                $this->t1dao->execute($insertsql);
                $popedomid = $this->t1dao->insert_id();

                $addpopedom[]=array(
                    "id"=>$popedomid,
                    "lid"=>"$LID",
                    "uid"=>"$uid",
                    "username"=>"$UserName",
                    "truename"=>"$TrueName",
                    "popid"=>"$popid",
                    "creatuser"=>"$adminid",
                    "mc_type"=>"$mc_type",
                    "creattime"=>date('Y-m-d H:i:s'),
                    "isdel"=>"0"
                );

                $addlog[]=array(
                    "opcontent"=>"用户:".$TrueName."，分配权限：".$qv,
                    "optype"=>"9"
                );
            }else{
                if($popedoms['isdel']=='1'){
                    $id=$popedoms['id'];
                    $updatesql="UPDATE  app_license_popedom SET updateadminid='$adminid',updateadminname='$adminusername',updatetime=NOW(),isdel=0 WHERE id='$id'";
                    $this->t1dao->execute($updatesql);
                    $editpopedom[]=array(
                        "id"=>"$id",
                        "isdel"=>"0",
                        "updateadminid"=>"$adminid",
                        "updateadminname"=>"$adminusername",
                        "updatetime"=>date('Y-m-d H:i:s')
                    );
                    $addlog[]=array(
                        "opcontent"=>"用户:".$TrueName."，分配权限：".$qv,
                        "optype"=>"9"
                    );
                }
            }
        }


        $where="";
        if($mc_type==3 && $params['power2']==1){
            $allqx=$params['allqx'];
            $where.=" AND app_license_popedom.popid in ('".implode("','",$allqx)."') ";
        }

        $yx_item=$qxList;
        $qx_popedoms=$this->t1dao->query("SELECT app_license_popedom_item.itemname,app_license_popedom.* FROM app_license_popedom left join app_license_popedom_item on app_license_popedom.popid=app_license_popedom_item.id left join app_license_popedom_type on app_license_popedom_type.id=app_license_popedom_item.typeid  WHERE  app_license_popedom.uid='$uid' AND app_license_popedom.mc_type='$mc_type' AND app_license_popedom.popid not in ('".implode("','",$yx_item)."') AND app_license_popedom.isdel=0 AND app_license_popedom_item.isdel=0 AND app_license_popedom_type.isdel=0 $where");
        
        if($qx_popedoms){
            foreach($qx_popedoms as $key=>$value){
                $addlog[]=array(
                    "opcontent"=>"用户:".$TrueName."，取消权限：".$value['itemname'],
                    "optype"=>"9"
                );
                $editpopedom[]=array(
                    "id"=>"$value[id]",
                    "isdel"=>"1",
                    "deladminid"=>"$adminid",
                    "deltime"=>date('Y-m-d H:i:s')
                );
            }

            $this->t1dao->execute("UPDATE app_license_popedom SET isdel=1,deladminid='$adminid',deltime=NOW() WHERE   uid='$uid' AND mc_type='$mc_type' and popid not in ('".implode("','",$yx_item)."') AND isdel=0 $where");
        }

        if(!empty($addlog)){
            foreach($addlog as $ak=>$av){
                $this->t1dao->execute("INSERT INTO dc_oplog SET guid='$GUID',signcs='$signcs',truename='$TrueName',userid='$uid',opcontent='$av[opcontent]',optype=9,create_time=NOW(),ip='$IP',mc_type='$mc_type'");
            }
            $this->dosysLog($GUID,$addlog,$mc_type);
        }

        if(!empty($addpopedom)){
            $addinfo[0]=array(
                "tablename"=>"app_license_popedom",
                "type"=>"insert",
                "pk"=> "id",
                "data"=>$addpopedom
            );
            $addinfos=json_encode($addinfo);
            $data=$this->synchronize($GUID,$addinfos,$mc_type);
        }

        if(!empty($editpopedom)){
            $editinfo[0]=array(
                "tablename"=>"app_license_popedom",
                "type"=>"update",
                "pk"=> "id",
                "data"=>$editpopedom
            );
            $editinfos=json_encode($editinfo);
            $data=$this->synchronize($GUID,$editinfos,$mc_type);
        }

        $Message['Success']=1;
        echo json_encode($Message);
        //alert("设置成功！");
        //goURL("xgquanx.php?view=index&GUID=".$GUID."&mode=".$params['mode']."&mc_type=".$mc_type);
        exit;

    }

    public function edituser($params){
        $uid=$params["Uid"];
        $mc_type=$params["mc_type"];

        $userlist=$this->maindao->getRow("SELECT * FROM  adminuser WHERE id='$uid'");
        $list=$this->t1dao->query("SELECT app_license_detail.*,app_license.MID,app_license.LicenseKeyMain FROM app_license_detail LEFT JOIN app_license ON app_license.ID=app_license_detail.LID WHERE app_license_detail.UseUser='$userlist[username]' and app_license_detail.mc_type='$mc_type' order by app_license_detail.ID ASC ");
        $accounts=$this->maindao->query("SELECT * FROM  adminuser WHERE mid='".$userlist['mid']."'  and username!='' and state=1  order by username asc");

        $license=$this->t1dao->getRow("select * from app_license where MID='".$userlist['mid']."' and mc_type='$mc_type'");

        $cronconfig=$this->t1dao->query("SELECT * FROM  xinsteel_cronconfig WHERE userid='$uid' order by id desc ");

        $this->assign("cronconfig",$cronconfig);
        $this->assign("accounts",$accounts);
        $this->assign("list",$list);
        $this->assign("userlist",$userlist);
        $this->assign("params",$params);
        $this->assign("license",$license);
    }

    public function addcronconfig($params){
        $cronconfig_url=array(
            array("name"=>"管理看板","value"=>XGDCURL."/allgz/gzcxview?mc_type=3","selected"=>"false"),
        );

        $cronconfig_url=$this->pri_JSON($cronconfig_url);
        $this->assign("cronconfig_url",$cronconfig_url);

        if($params["id"]){
            $cronconfig=$this->t1dao->getRow("SELECT * FROM  xinsteel_cronconfig WHERE id='".$params["id"]."' ");

            $selectdays2=$cronconfig["frequency"]==2?$cronconfig["dayweeks"]:"";
            $selectdays1=$cronconfig["frequency"]==3?$cronconfig["dayweeks"]:"";

            $this->assign("cronconfig",$cronconfig);
            $this->assign("selectdays1",$selectdays1);
            $this->assign("selectdays2",$selectdays2);
        }
        $this->assign("params",$params);
        $days=array();
        for($i=1;$i<=31;$i++){
            $days[]=array("name"=>$i."号","value"=>$i,"selected"=>"false");
        }
        $days=$this->pri_JSON($days);
        $this->assign("days",$days);

    }

    private function pri_JSON($array){
        $this->pri_arrayRecursive($array, 'urlencode', true);
        $json = json_encode($array);
        return urldecode($json);
    }

    private function pri_arrayRecursive(&$array, $function, $apply_to_keys_also = false)
    {
        static $recursive_counter = 0;
        if (++$recursive_counter > 1000) {
            die('possible deep recursion attack');
        }
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $this->pri_arrayRecursive($array[$key], $function, $apply_to_keys_also);
            } else {
                $array[$key] = $function($value);
            }
            if ($apply_to_keys_also && is_string($key)) {
                $new_key = $function($key);
                if ($new_key != $key) {
                    $array[$new_key] = $array[$key];
                    unset($array[$key]);
                }
            }
        }
        $recursive_counter--;
    }


    public function doaddcronconfig($params){
        $GUID=$params['GUID'];
        $mode = $params['mode'];//1=客户端，2=安卓，3=iOS
        //获取用户信息
        $user_infos = $this->t1dao->get_userinfo_byguid($GUID);
        $userid= $user_infos['Uid'];


        $uid=$params["Uid"];
        $title=$params["title"];
        $desc=$params["desc"];

        $params["url"]=htmlspecialchars_decode($params["url"]);

        if($params["frequency"]==2){
            $dayweeks=$params["dayweeks2"];
        }else if($params["frequency"]==3){
            $dayweeks=$params["dayweeks1"];
        }



        if($params["id"]!=""){
            $this->t1dao->execute("update xinsteel_cronconfig set url='".$params["url"]."',frequency='".$params["frequency"]."',dayweeks='".$dayweeks."',crontime='".$params["crontime"]."',title='$title',`desc`='$desc' where id='$params[id]'");
        }else{
            $this->t1dao->execute("insert into xinsteel_cronconfig set userid='$uid',url='".$params["url"]."',frequency='".$params["frequency"]."',dayweeks='".$dayweeks."',crontime='".$params["crontime"]."',title='".$title."',`desc`='".$desc."',create_time=NOW(),create_userid='$userid' ");
        }
        echo 1;
    }



    public function resetuser($params){
        $ID=$params['ID'];
        $LID=$params['LID'];
        $mc_type=$params['mc_type'];

        //重置
        $this->t1dao->execute("update  app_license_detail set SignCS='',UserDate='',UseUser='',Remarks='' where ID='$ID'");
        $data[0]=array(
            "ID"=>"$ID",
            "UseUser"=>"",
            "SignCS"=>"",
            "Remarks"=>""
        );

        $info[0]=array(
            "tablename"=>"app_license_detail",
            "type"=>"update",
            "pk"=> "ID",
            "data"=>$data
        );
        $infos=json_encode($info);
        $data=$this->synchronize($params['GUID'],$infos,$mc_type);
        $Message['Success']=1;
        echo json_encode($Message);
    }

    public function delcronconfig($params){
        $ID=$params['ID'];
        $LID=$params['LID'];
        $mc_type=$params['mc_type'];

        $this->t1dao->execute("delete from xinsteel_cronconfig  where id='$ID'");
        $Message['Success']=1;
        echo json_encode($Message);
    }



    public function unbinduser($params){
        $ID=$params['ID'];
        $LID=$params['LID'];
        $mc_type=$params['mc_type'];

        //设备解绑
        $this->t1dao->execute("UPDATE app_license_detail SET SignCS='' WHERE ID='$ID'");
        $data[0]=array(
            "ID"=>"$ID",
            "SignCS"=>""
        );

        $info[0]=array(
            "tablename"=>"app_license_detail",
            "type"=>"update",
            "pk"=> "ID",
            "data"=>$data
        );
        $infos=json_encode($info);
        $data=$this->synchronize($params['GUID'],$infos,$mc_type);
        $Message['Success']=1;
        echo json_encode($Message);
    }


    public function doedit_password($params){
        $userid=$params['userid'];
        $username=$params['username'];
        $newpassword=$params['newpassword'];
        $oldpassword=$params['oldpassword'];
        $password=$params['password'];

        $user=$this->maindao->getrow("select * from adminuser where id=$userid and username='$username'");
        if($user){
            if($user["passwordmd5"]!=$oldpassword){
                $Message['Success']=0;
                $Message['Message']="账户旧密码错误";
            }else{
                $this->maindao->execute("update adminuser set passwordmd5='$newpassword',password='$password' where id=$userid");
                $Message['Success']=1;
                $Message['Message']="密码修改成功";
            }
        }else{
            $Message['Success']=0;
            $Message['Message']="用户不存在";
        }
        echo json_encode($Message);exit;
    }
}
?>