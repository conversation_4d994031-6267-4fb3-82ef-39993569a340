<?php
$GLOBALS ['arr_area_1_xc']=array(
	"华东地区#'上海','杭州','南京','济南','合肥','福州','南昌','苏州','无锡','马鞍山','厦门','泰安'",
	"中南地区#'广州','深圳','南宁','柳州','武汉','郑州','长沙','海口','三亚'",
	"华北地区#'北京','天津','太原','石家庄'",
	"东北地区#'沈阳','哈尔滨','长春','大连'",
	"西部地区#'重庆','成都','昆明','贵阳','西安','兰州','银川','西宁'",				
	);
$GLOBALS ['arr_area_1_lw']=array(
	"华东地区#'上海','杭州','南京','济南','合肥','福州','南昌','苏州','无锡','徐州','南通','马鞍山','厦门','莱芜','泰安'",
	"中南地区#'广州','深圳','南宁','柳州','武汉','郑州','长沙','海口','三亚'",
	"华北地区#'北京','天津','太原','石家庄'",
	"东北地区#'沈阳','哈尔滨','长春','大连'",
	"西部地区#'重庆','成都','昆明','贵阳','西安','兰州','银川','西宁'",				
);

	class sgqhtbAction extends AbstractAction{
		public function __construct(){
		parent::__construct();    
	}

	function get_style($value){
		if($value == 0){
			$value = '--';
		}elseif( $value > 0 ){
			$value = '<font color="red">↑'.$value.'</font>';
		}elseif( $value < 0 ){
			$value = '<font color="green">↓'.abs($value).'</font>';
		}
		return $value;
	}

	function getjsonarr($data) {
		$data = html_entity_decode($data);
		$data = str_replace(" ","+",$data);
		$data_arr = json_decode($data, true);
		return $data_arr;
	}

	public function jiaotan($params)
	{
		$this->assign("mode",$params['mode']);
		$datadate=$params["datadate"];

		if($datadate=='')
		{
			$datadate = date('Y-m-d',time());  
			
		}
		$startdate = date('Y-m-d',strtotime("-1month",strtotime($datadate)));  
		$startdate1 = date('Y-m-d',strtotime("-6month",strtotime($datadate)));
		 
		//226家独立焦化厂产能利用率（%）
		$dljqcnlyl=$this->gcdao->get_sc_KaiGongLvHuiZong($startdate,$datadate);
		$this->assign("dljqcnlyl",$dljqcnlyl);

		// 本周日均产量（万吨）
		// $sql = "SELECT Date FROM sc_KaiGongLvHuiZong where 1 and Date<='$datadate' group by Date  DESC LIMIT 2";
		// $dates = $this->gcdao->getones($sql);
		// $ndate = $dates[0];
		// $data=$this->gcdao->get_operating($ndate);
		// $Totalc2=0;
		// $Totalk2=0;
		// foreach($data as $key=>$tmp2){
		// 	if($tmp2['st_type']=='1' || $tmp2['st_type']=='3'){
		// 		// $Totalc2 +=$tmp2['channeng'];
		// 		$Totalk2 +=$tmp2['channeng'] * $tmp2['kaigonglv'];
		// 	}
		// }
		// // $kaigonglv2 = round($Totalk2 /$Totalc2,2);
		// // $bq_value = ($Totalc2 * $kaigonglv2)/100/365;
		// $bq_value = round($Totalk2/100/365,2);
		// //获取前一次
		// $olddate = $dates[1];
		// $data=$this->gcdao->get_operating($olddate);
		// $Totalc2=0;
		// $Totalk2=0;
		// foreach($data as $key=>$tmp2){
		// 	if($tmp2['st_type']=='1' || $tmp2['st_type']=='3'){
		// 		// $Totalc2 +=$tmp2['channeng'];
		// 		$Totalk2 +=$tmp2['channeng'] * $tmp2['kaigonglv'];
		// 	}
		// }
		// // $kaigonglv2 = round($Totalk2 /$Totalc2,2);
		// $sq_value = round($Totalk2/100/365,2);
		// $bzrjcl = $this->get_shuju(round($bq_value,2),round($sq_value,2),$ndate);
		/* 直接从汇总表里面取数据 */
		$sql = "select TotalChanNeng2,KaiGonglv2,Date from sc_KaiGongLvHuiZong where AreaCode=0 and Date<='".$datadate."' order by Date desc limit 2";
		$sc_KaiGongLvHuiZong = $this->gcdao->query($sql);
		$bq_value = round($sc_KaiGongLvHuiZong[0]['TotalChanNeng2']*$sc_KaiGongLvHuiZong[0]['KaiGonglv2']/100/365,2);
		$sq_value = round($sc_KaiGongLvHuiZong[1]['TotalChanNeng2']*$sc_KaiGongLvHuiZong[1]['KaiGonglv2']/100/365,2);
		$bzrjcl = $this->get_shuju($bq_value,$sq_value,$sc_KaiGongLvHuiZong[0]['Date']);
		$this->assign("bzrjcl",$bzrjcl);
		// echo"<pre>";print_r($sc_KaiGongLvHuiZong);

        //全国焦炭产量（万吨）
		$qgjtcl=$this->_dao->get_L2Data($startdate1,$datadate," L2id=327 ");
		$this->assign("qgjtcl",$qgjtcl);
		
		//全国高炉开工率（%）
		$glcnlyl=$this->gcdao->get_data_table($startdate,$datadate," dta_type='GC_KGL' and dta_1='全国' ");
		$this->assign("glcnlyl",$glcnlyl);

		//唐山高炉开工率（%）
		$tsglcnlyl=$this->gcdao->get_data_table($startdate,$datadate," dta_type = 'GNCX_A' ");
		$this->assign("tsglcnlyl",$tsglcnlyl);

		//全国生铁产量（万吨）
		$qgstcl=$this->_dao->get_L2Data($startdate1,$datadate," L2id=188 ");
		$this->assign("qgstcl",$qgstcl);

		//100家独立焦化厂库存（万吨）
		$dljqkc=$this->gcdao->get_sc_othercom_stock_hz($startdate,$datadate,"  AreaCode=0 ");
		$this->assign("dljqkc",$dljqkc);

		//80家钢厂库存
		$bsjgckc=$this->gcdao->get_MeiJiaoNeedAndStockHuiZong($startdate,$datadate,"   `Area` =7 AND  `PinZhong` =1 ");
		$this->assign("bsjgckc",$bsjgckc);

		// 港口焦炭库存（万吨）
		$gkkc=$this->gcdao->get_gkkc($startdate,$datadate,"  db.DataType='Java-SGGKJTKC' and  dta_1='合计' ");
		$this->assign("gkkc",$gkkc);

		//焦炭库存合计
		$date_arr = array();
		$date_arr[0] = $dljqkc['Date'];
		$date_arr[1] = $bsjgckc['Date'];
		$date_arr[2] = $gkkc['Date'];
		$date_min = min($date_arr);
		if( strtotime($date_min)<strtotime($dljqkc['Date']) ){
			$dljqkc=$this->gcdao->get_sc_othercom_stock_hz($startdate,$date_min,"  AreaCode=0 ");
		}
		if( strtotime($date_min)<strtotime($bsjgckc['Date']) ){
			$bsjgckc=$this->gcdao->get_MeiJiaoNeedAndStockHuiZong($startdate,$date_min,"   `Area` =7 AND  `PinZhong` =1 ");
		}
		if( strtotime($date_min)<strtotime($gkkc['Date']) ){
			$gkkc=$this->gcdao->get_gkkc($startdate,$date_min,"  db.DataType='Java-SGGKJTKC' and  dta_1='合计' ");
		}
		$bq_value = $dljqkc['kc'] + $bsjgckc['kc'] + $gkkc['kc'];
		$sq_value = $dljqkc['sq_kc'] + $bsjgckc['sq_kc'] + $gkkc['sq_kc'];
		$jtkchj = $this->get_shuju($bq_value,$sq_value,$date_min);
		$this->assign("jtkchj",$jtkchj);

		$jqdjlr=$this->gcdao->get_JiaoQiLiLunDunLiRun($startdate,$datadate);
		$this->assign("jqdjlr",$jqdjlr);

		/*
		// 陕钢焦炭库存数据接口
		$post = array(
			'date' => $datadate
		); 
		$post_content = http_build_query($post);
		$options = array(
			'http' => array(
				'method' => 'POST',
				'header' => 'Content-type:application/x-www-form-urlencoded',
				'content' => $post_content,
			)
		);
		$url = SGDCURL."/web/sggetdata.php?action=getJtku";
		$sginfo = file_get_contents($url, false, stream_context_create($options));
        */
		$url = SGDCURL."/web/sggetdata.php?action=getJtku&date=".$datadate;
		$sginfo = file_get_contents($url, false, stream_context_create(array('ssl' => array('verify_peer' => false, 'verify_peer_name' => false))));

		$sginfo_arr = $this->getjsonarr($sginfo);
		$sgku = $this->get_shuju($sginfo_arr['bqkc'],$sginfo_arr['sqkc'],$sginfo_arr['date']);
		$this->assign("sgku",$sgku);

		// 焦炭主力合约基差
			//日期一致
		$datearr[0] = $this->gcdao->getOne( "select dta_ym from steelhome_drc.data_table where dta_ym<='$datadate' and dta_type = 'SHQHDAY_9' AND dta_maxValStatus=1 order by dta_ym desc limit 1" );
		$datearr[1] = $this->_dao->getOne( "select mconmanagedate from marketconditions where mconmanagedate<='$datadate 23:59:59' and mastertopid='D283114' order by mconmanagedate desc limit 1" );
		$datearr[1] = date("Y-m-d", strtotime($datearr[1]));
		$datemin = min($datearr);
		$market = $this->get_marketprice("D283114",$datemin);
		$jtzlhy=$this->gcdao->get_data_table2($startdate1,$datemin,"steelhome_drc.data_table","dta_ym","dta_6"," dta_type = 'SHQHDAY_9' AND dta_maxValStatus=1 ");
		$jtzlhyjc = $this->get_shuju(round($market["D283114"]['price']-$jtzlhy['value'],2),round($market["D283114"]['oldprice']-$jtzlhy['sq_value'],2),$datemin);
		$this->assign("jtzlhyjc",$jtzlhyjc);
		// echo 1850-1870-1969;

		// 主力合约煤焦比
		$jmhy = $this->gcdao->get_data_table2($startdate1,$datadate,"steelhome_drc.data_table","dta_ym","dta_6","  dta_type = 'SHQHDAY_19' AND dta_maxValStatus=1 ");
			//日期一致
		$date_arr = array();
		$date_arr[0] = $jmhy["Date"];
		$date_arr[1] = $jtzlhy["Date"];
		$date_min = min($date_arr);
		if( strtotime($date_min)<strtotime($jmhy['Date']) ){
			$jmhy = $this->gcdao->get_data_table2($startdate1,$date_min,"steelhome_drc.data_table","dta_ym","dta_6","  dta_type = 'SHQHDAY_19' AND dta_maxValStatus=1 ");
		}
		if( strtotime($date_min)<strtotime($jtzlhy['Date']) ){
			$jtzlhy=$this->gcdao->get_data_table2($startdate1,$date_min,"steelhome_drc.data_table","dta_ym","dta_6"," dta_type = 'SHQHDAY_9' AND dta_maxValStatus=1 ");
		}
		$zlhymjb =  $this->get_bi($jmhy,$jtzlhy);
		$this->assign("zlhymjb",$zlhymjb);
		// print_r($date_arr);

		// 合约价差
		$heyue_01=$this->gcdao->get_data_table2($startdate1,$datadate,"steelhome_drc.data_table","dta_ym","dta_6"," dta_type = 'SHQHDAY_9' AND right(dta_1, 2) ='01' ");
		$heyue_05=$this->gcdao->get_data_table2($startdate1,$datadate,"steelhome_drc.data_table","dta_ym","dta_6"," dta_type = 'SHQHDAY_9' AND right(dta_1, 2) ='05' ");
		$heyue_09=$this->gcdao->get_data_table2($startdate1,$datadate,"steelhome_drc.data_table","dta_ym","dta_6"," dta_type = 'SHQHDAY_9' AND right(dta_1, 2) ='09' ");
			//日期一致
		$date_arr = array();
		$date_arr[0] = $heyue_01["Date"];
		$date_arr[1] = $heyue_05["Date"];
		$date_arr[3] = $heyue_09["Date"];
		$date_min = min($date_arr);
		if( strtotime($date_min)<strtotime($heyue_01['Date']) ){
			$heyue_01=$this->gcdao->get_data_table2($startdate1,$date_min,"steelhome_drc.data_table","dta_ym","dta_6"," dta_type = 'SHQHDAY_9' AND right(dta_1, 2) ='01' ");
		}
		if( strtotime($date_min)<strtotime($heyue_05['Date']) ){
			$heyue_05=$this->gcdao->get_data_table2($startdate1,$date_min,"steelhome_drc.data_table","dta_ym","dta_6"," dta_type = 'SHQHDAY_9' AND right(dta_1, 2) ='05' ");
		}
		if( strtotime($date_min)<strtotime($heyue_09['Date']) ){
			$heyue_09=$this->gcdao->get_data_table2($startdate1,$date_min,"steelhome_drc.data_table","dta_ym","dta_6"," dta_type = 'SHQHDAY_9' AND right(dta_1, 2) ='09' ");
		}
		$heyue0901 = $this->get_cha($heyue_09,$heyue_01);
		$heyue0105 = $this->get_cha($heyue_01,$heyue_05);
		$heyue0509 = $this->get_cha($heyue_05,$heyue_09);
		$this->assign("heyue0901",$heyue0901);
		$this->assign("heyue0105",$heyue0105);
		$this->assign("heyue0509",$heyue0509);

		$this->assign("datadate",$datadate);
		$this->assign("data_ch",date("Y年n月j日", strtotime($datadate)));
	}

	function get_cha($heyue1,$heyue2){
		$value = $heyue1['value'] - $heyue2['value'];
		$sq_value = $heyue1['sq_value'] - $heyue2['sq_value'];
		$valuezd1 = $value - $sq_value;
		$valuezd = $this->get_style($value - $sq_value);
		$value = $sq_value!=0 ?  round($valuezd1/abs($sq_value)*100,2):0;
		$valuezdf = $this->get_style( $value );
		$shuju = array(
			'value' => $value,
			'valuezd' => $valuezd,
			'valuezdf' => $valuezdf,
			'Date' => $heyue1['Date'],
		);
		return $shuju;
	}

	function get_bi($date1,$date2){
		$value = $date2['value']!= 0 ?  round($date1['value'] / $date2['value'],2) : "0";
		$sq_value = $date2['sq_value']!= 0 ? round($date1['sq_value'] / $date2['sq_value'],2): "0";
		$valuezd1 = $value - $sq_value;
		$valuezd = $this->get_style($value - $sq_value);
		$value = $sq_value!=0 ? round($valuezd1/abs($sq_value)*100,2):0;
		$valuezdf = $this->get_style( $value );
		$shuju = array(
			'value' => $value,
			'valuezd' => $valuezd,
			'valuezdf' => $valuezdf,
			'Date' => $date1['Date'],
		);
		return $shuju;
	}

	function get_he($date1,$date2){
		$value = round($date1['value'] + $date2['value'],2);
		$sq_value = round($date1['sq_value'] + $date2['sq_value'],2);
		$valuezd1 = $value - $sq_value;
		$valuezd = $this->get_style($value - $sq_value);
		$value = $sq_value!= 0 ? round($valuezd1/abs($sq_value)*100,2):0;
		$valuezdf = $this->get_style( $value );
		$shuju = array(
			'value' => $value,
			'sq_value' => $sq_value,
			'valuezd' => $valuezd,
			'valuezdf' => $valuezdf,
			'Date' => $date1['Date'],
		);
		return $shuju;
	}

	function get_shuju($bq,$sq,$date){
		$valuezd = $this->get_style(round($bq-$sq,2));
		$value = $sq!= 0 ? round(($bq-$sq)/abs($sq)*100,2):0;
		$valuezdf = $this->get_style( $value );
		$shuju = array(
			'value'=>$bq,
			'sq_value'=>$sq,
			'valuezd'=>$valuezd ,
			'valuezdf'=>$valuezdf,
			'Date'=>$date
		);
		return $shuju;
	}

	function getWeekMyActionAndEnd($date, $first = 1){
		//当前日期
		$time = strtotime($date);
		$sdefaultDate = date("Y-m-d", $time);
		//$first =1 表示每周星期一为开始日期 0表示每周日为开始日期
		$w = date('w', strtotime($sdefaultDate)); //获取当前周的第几天 周日是 0 周一到周六是 1 - 6
		$days = $w ? $w - $first : 6;   //获取本周开始日期，如果$w是0，则表示周日，减去 6 天
		$week_start = date('Y-m-d', strtotime("{$sdefaultDate} -{$days} days"));
		$week_end = date('Y-m-d', strtotime("{$week_start} +6 days"));   //本周结束日期
		return array("week_start" => $week_start, "week_end" => $week_end);
	}

	function getPrevStock($arrs, $sc_id, $smallType)
	{
		$stock = array();
		foreach($arrs as $line){
			if($line['sc_id'] == $sc_id && 
			  $line['SmallType'] == $smallType){
				$stock['Stock'] = $line['Stock'];
				$stock['TotalLine'] = $line['TotalLine'];
				$stock['UsingLine'] = $line['UsingLine'];
				break;
			}
			
		}
		return $stock;
	}

	function getCurrentInfo($arrs, $sc_id, $smallType){
		$currentInfo = array();
		foreach($arrs as $line){
			if($line['sc_id'] == $sc_id && 
			  $line['SmallType'] == $smallType){
				$currentInfo['Stock'] = $line['Stock'];
				$currentInfo['TotalLine'] = $line['TotalLine'];
				$currentInfo['UsingLine'] = $line['UsingLine'];
				break;
			}
			
		}
		return $currentInfo;
	}

	function luowen($params){
		$this->assign("mode",$params['mode']);
		$datadate=$params["datadate"];
		if($datadate=='')
		{
			$datadate = date('Y-m-d',time());  
			
		}
		$startdate = date('Y-m-d',strtotime("-1month",strtotime($datadate)));  
		$startdate1 = date('Y-m-d',strtotime("-6month",strtotime($datadate)));  
		$pricelist="562023,532023,072023,372023,678411";
		$shuju=$this->getpricebypricestr($pricelist,$startdate,$datadate);
		$this->assign("shuju",$shuju);

		// 期货主力
		$qhzl=$this->gcdao->get_data_table2($startdate1,$datadate,"steelhome_drc.data_table","dta_ym","dta_6"," dta_type = 'SHQHDAY_4' AND dta_maxValStatus=1 ");
		$this->assign("qhzl",$qhzl);
	    //全国高炉开工率（%）
		$glcnlyl=$this->gcdao->get_data_table($startdate,$datadate," dta_type='GC_KGL' and dta_1='全国' ");
		$this->assign("glcnlyl",$glcnlyl);
		//唐山高炉开工率（%）
		$tsglcnlyl=$this->gcdao->get_data_table($startdate,$datadate,"  dta_type = 'GNCX_A' ");
		$this->assign("tsglcnlyl",$tsglcnlyl);
	   
		$qgdlcnkgl=$this->gcdao->get_sc_operating_area_hz($startdate,$datadate,"  dta_vartype = '5' and CityId='-1' ");
		$this->assign("qgdlcnkgl",$qgdlcnkgl);
		$hddlcnkgl=$this->gcdao->get_sc_operating_area_hz($startdate,$datadate,"  dta_vartype = '5' and CityId='华东地区' ");
		$this->assign("hddlcnkgl",$hddlcnkgl);
		$xndlcnkgl=$this->gcdao->get_sc_operating_area_hz($startdate,$datadate,"  dta_vartype = '5' and CityId='西南地区' ");
		$this->assign("xndlcnkgl",$xndlcnkgl);

		// 全国螺纹钢产量
		$luowen = $this->gcdao->get_area_hz(7,$datadate);
		$bq_luowen = $luowen['bq']['shuju'];
		$sq_luowen = $luowen['sq']['shuju'];
		$qglwgcl = $this->get_shuju(round($bq_luowen['-1']['dta_8']*7,2),round($sq_luowen['-1']['dta_8']*7,2),$luowen['bq']['date']);
		$this->assign("qglwgcl",$qglwgcl);
		//全国线材产量
		$xiancai = $this->gcdao->get_area_hz(6,$datadate);
		$bq_xiancai = $xiancai['bq']['shuju'];
		$sq_xiancai = $xiancai['sq']['shuju'];
		$qgxccl = $this->get_shuju(round($bq_xiancai['-1']['dta_8']*7,2),round($sq_xiancai['-1']['dta_8']*7,2),$xiancai['bq']['date']);
		$this->assign("qgxccl",$qgxccl);
		// echo"<pre>";print_r($bq_xiancai);
		// 全国建筑钢材产量
		$qgjzgccl = $this->get_he($qglwgcl,$qgxccl);
		$this->assign("qgjzgccl",$qgjzgccl);
		//中西部建筑钢材产量
		$bq_zhongxi = round(($bq_luowen['中南地区']['dta_8']+$bq_luowen['西北地区']['dta_8']+$bq_luowen['西南地区']['dta_8']+$bq_xiancai['中南地区']['dta_8']+$bq_xiancai['西北地区']['dta_8']+$bq_xiancai['西南地区']['dta_8'])*7,2);
		$sq_zhongxi = round(($sq_luowen['中南地区']['dta_8']+$sq_luowen['西北地区']['dta_8']+$sq_luowen['西南地区']['dta_8']+$sq_xiancai['中南地区']['dta_8']+$sq_xiancai['西北地区']['dta_8']+$sq_xiancai['西南地区']['dta_8'])*7,2);
		$zxbjzgc =  $this->get_shuju($bq_zhongxi,$sq_zhongxi,$luowen['bq']['date']);
		$this->assign("zxbjzgc",$zxbjzgc);

		//全国主要钢厂建筑钢材库存
		  //日期一致
		$sql = "select Date from KuCunHZ where Date < '$datadate' and type=1 and area=0 order by Date desc limit 1";
		$date_arr[0] = $this->gcdao->getOne($sql);
		$sql = "select Date from KuCunHZ where Date < '$datadate' and type=2 and area=0 order by Date desc limit 1";
		$date_arr[1] = $this->gcdao->getOne($sql);
		$datemin = min($date_arr);
		$lugkc = $this->gcdao->get_data_table2($startdate,$datemin,"KuCunHZ","Date","Value"," type=1 and area=0 ");
		$xckc = $this->gcdao->get_data_table2($startdate,$datemin,"KuCunHZ","Date","Value"," type=2 and area=0 ");
		$qgzygcjckc = $this->get_he($lugkc,$xckc,$datemin);
		$this->assign("qgzygcjckc",$qgzygcjckc);
		
		$week = $this->getWeekMyActionAndEnd($luowen['bq']['date']);
		$shichang = $this->gcdao->query("select value,time from steelhome_drc.kucun_hz WHERE type = '5' and time<='".$week['week_end']." 23:59:59' order by time desc limit 3");
		$gangchang = $this->gcdao->query("select Value,Date from KuCunHZ WHERE type=1 and area=0 and Date<='".$week['week_end']." 23:59:59' order by Date desc limit 3");

		//全国螺纹钢表观需求=全国螺纹钢产量+（本周国内主要市场螺纹钢库存+本周国内钢厂螺纹钢库存）-（上周国内主要市场螺纹钢库存+上周国内钢厂螺纹钢库存）
		$bq_bxuq = round($bq_luowen['-1']['dta_8']*7,2) + ($shichang[0]['value'] + $gangchang[0]['Value']) - ($shichang[1]['value'] + $gangchang[1]['Value']);
		$sq_bxuq = round($sq_luowen['-1']['dta_8']*7,2) + ($shichang[1]['value'] + $gangchang[1]['Value']) - ($shichang[2]['value'] + $gangchang[2]['Value']);
		$bxuq = $this->get_shuju($bq_bxuq,$sq_bxuq,$luowen['bq']['date']);
		$this->assign("bxuq",$bxuq);

		// 中西部市场库存
		$date_arr = array();
		$date_arr[0] = $this->gcdao->getone("select time from steelhome_drc.kucun_hz where type=4 order by time desc limit 1");
		$date_arr[0] = $this->gcdao->getone("select time from steelhome_drc.kucun_hz where type=5 order by time desc limit 1");
		$hzdate = date('Y-m-d',strtotime(min($date_arr)));
		$bqweek = $this->getWeekMyActionAndEnd($hzdate);
		$sqweek = $this->getWeekMyActionAndEnd(date("Y-m-d",strtotime("-7 day".$hzdate)));
		$arr_citys = "";
		foreach($GLOBALS ['arr_area_1_lw'] as $vv1){
			  $arr_areas = explode("#",$vv1);
			  if( $arr_areas[0]=="中南地区" || $arr_areas[0]=="西部地区" ){
				  if($arr_citys == ""){
					  $arr_citys = $arr_areas[1];
				  }else{
					  $arr_citys .= ",".$arr_areas[1];
				  }
			  }
		}
		$arr_city = explode("','",$arr_citys);
		$varietynames = "'线材','螺纹钢'";
		$bq_kc_arr = $this->gcdao->get_sc_kc($arr_citys,$varietynames,$bqweek['week_start'],$bqweek['week_end']);
		if( empty($bq_kc_arr) ){
			$bqweek = $sqweek;
			$bq_kc_arr = $this->gcdao->get_sc_kc($arr_citys,$varietynames,$bqweek['week_start'],$bqweek['week_end']);
			$sqweek = $this->getWeekMyActionAndEnd(date("Y-m-d",strtotime("-14 day".$hzdate)));
		}
		$sq_kc_arr = $this->gcdao->get_sc_kc($arr_citys,$varietynames,$sqweek['week_start'],$sqweek['week_end']);
		$bq_value = 0;
		$sq_value = 0;
		foreach( $bq_kc_arr as $k=>$v ){
			$bq_value += $v['the_stock'];
		}
		foreach( $sq_kc_arr as $k=>$v ){
			$sq_value += $v['the_stock'];
		}
		$zxsckc = $this->get_shuju($bq_value,$sq_value,$hzdate);
		$this->assign("zxsckc",$zxsckc);
		// echo $arr_citys;

		// 中西部钢厂库存
		$date_arr = array();
		$date_arr = $this->gcdao->getones("select Date from KuCunHZ where type=1 and Area=0 order by Date desc limit 2");
		$bq_value = $this->gcdao->getone("select sum(Value) from KuCunHZ where type in('1','2') and Area in ('4','5','6') and Date='$date_arr[0]'");
		$sq_value = $this->gcdao->getone("select sum(Value) from KuCunHZ where type in('1','2') and Area in ('4','5','6') and Date='$date_arr[1]'");
		$zxgckc = $this->get_shuju(round($bq_value,2),round($sq_value,2),$date_arr[0]);
		/* 下面取得是录入界面数据，不好 */
		// $pz_type = "jiancai";
		// $xpz_arr = array("0" => "线材(含盘螺)","1" => "螺纹钢");
		// $kc_type1 = "1";//大品种
		// $kc_type2[0] = "1";//小品种1
		// $kc_type2[1] = "2";//小品种2
		// $area_zn = $this->gcdao->get_steelbyarea2("中南地区",$pz_type);
		// $area_xb = $this->gcdao->get_steelbyarea2("西北地区",$pz_type);
		// $area_xn = $this->gcdao->get_steelbyarea2("西南地区",$pz_type);
		// $area_arr = array("中南地区" =>$area_zn,"西北地区" =>$area_xb,"西南地区" =>$area_xn);
		// $ids = $this->gcdao->get_steelids($pz_type);
		// $sdate = $bqweek['week_start'];
		// $edate = $bqweek['week_end'];
		// $ssdate = $sqweek['week_start'];
		// $sedate = $sqweek['week_end'];
		// $prevStocks = $this->gcdao->get_prevStocks($ids, $ssdate,$sedate,$kc_type1);
		// foreach($kc_type2 as $type2)
		// {
		// 	$currentInfos[] = $this->gcdao->get_currentInfos($ids,$sdate,$edate,$type2);
		// }
		// $actime = $currentInfos[0][0]['actime'];
		// foreach($area_arr as &$arr){
		// 	foreach($arr as &$arr_info){
		// 		$i = 0;
		// 		$sum_kc = 0;
		// 		foreach($kc_type2 as $type2) 
		// 		{
		// 			$scid = $arr_info['steelid'];
		// 			$smallType = $type2;
		// 			$prevStock_info = $this->getPrevStock($prevStocks, $scid, $smallType);
		// 			$currentInfo = $this->getCurrentInfo($currentInfos[$i], $scid, $type2);
		// 			$arr_info['smallType'][$i] = $type2;
		// 			$arr_info['prevStock'][$i] = $prevStock_info["Stock"];
		// 			if($currentInfo['TotalLine']!=""){
		// 				$arr_info['TotalLine'][$i] = $currentInfo['TotalLine'];
		// 				$sum_kc += $currentInfo['TotalLine'];
		// 			}else{
		// 				$arr_info['TotalLine'][$i] = $prevStock_info["TotalLine"];
		// 			}
		// 			if($currentInfo['UsingLine']!=""){
		// 				$arr_info['UsingLine'][$i] = $currentInfo['UsingLine'];
		// 			}else{
		// 				$arr_info['UsingLine'][$i] = $prevStock_info["UsingLine"];
		// 			}
		// 			$arr_info['Stock'][$i] = $currentInfo['Stock'];
		// 			if($currentInfo['Stock']!=""&&$prevStock_info["Stock"]!=""&&$prevStock_info["Stock"]!=0){
		// 				$arr_info['zjf'][$i] = round($currentInfo['Stock']/abs($prevStock_info["Stock"])*100-100,1);
		// 			}
		// 			$i++;
		// 		}
		// 		$arr_info['sum_kc'] = $sum_kc;
		// 	}
		// }
		// $bq_gckc = 0;
		// $sq_gckc = 0;
		// foreach($area_arr as $k_area=> $v_area){
		// 	foreach($v_area as $k=> $v){
		// 		$gckc += $v['Stock'][0];
		// 		$gckc += $v['Stock'][1];
		// 		$sq_gckc += $v['prevStock'][0];
		// 		$sq_gckc += $v['prevStock'][1];
		// 	}
		// }
		// $zxgckc = $this->get_shuju($gckc,$sq_gckc,date("Y-m-d",strtotime($actime)));
		$this->assign("zxgckc",$zxgckc);
		// echo"<pre>";print_r($area_arr);

		// 中西部市场+钢厂库存
		$scgcku = $this->get_he($zxsckc,$zxgckc);
		$this->assign("scgcku",$scgcku);

		// 现货钢厂利润
		$datearr[0] = $this->gcdao->getOne( "select ndate from steelhome_drc.sg_HangYeChengBenIndex where ndate<='$datadate' and    type='75' and mc_type=2 order by ndate desc limit 1" );
		$datearr[1] = $this->_dao->getOne( "select mconmanagedate from marketconditions where mconmanagedate<='$datadate 23:59:59' and topicture='M12043' order by mconmanagedate desc limit 1" );
		$datearr[1] = date("Y-m-d", strtotime($datearr[1]));
		$datemin = min($datearr);
		$market = $this->get_marketprice("M12043",$datemin);
		$gcxhlr=$this->gcdao->get_data_table2($startdate1,$datemin,"steelhome_drc.sg_HangYeChengBenIndex","ndate","chenben_tax"," type='75' and mc_type=2 ");
		$gcxhlrjc = $this->get_shuju(round($market["M12043"]['price']-$gcxhlr['value'],2),round($market["M12043"]['oldprice']-$gcxhlr['sq_value'],2),$datemin);
		$this->assign("gcxhlrjc",$gcxhlrjc);

		// 华东电炉钢厂利润
		$datearr[0] = $this->gcdao->getOne( "select ndate from steelhome_drc.sg_HangYeChengBenIndex where ndate<='$datadate' and type='163' and mc_type=2 order by ndate desc limit 1" );
		$datearr[1] = $this->_dao->getOne( "select mconmanagedate from marketconditions where mconmanagedate<='$datadate 23:59:59' and topicture='072023' order by mconmanagedate desc limit 1" );
		$datearr[1] = date("Y-m-d", strtotime($datearr[1]));
		$datemin = min($datearr);
		$market = $this->get_marketprice("072023",$datemin);
		$hddlgclr=$this->gcdao->get_data_table2($startdate1,$datemin,"steelhome_drc.sg_HangYeChengBenIndex","ndate","chenben_tax"," type='163' and mc_type=2 ");
		$hddlgclrjc = $this->get_shuju(round($market["072023"]['price']-$hddlgclr['value'],2),round($market["072023"]['oldprice']-$hddlgclr['sq_value'],2),$datemin);
		$this->assign("hddlgclrjc",$hddlgclrjc);

		// 虚拟钢厂利润 = 螺纹钢主力合约收盘价 -（铁矿石主力合约收盘价*1.65+焦炭主力合约收盘价*0.45+1300）
		$luowen = $this->gcdao->get_data_table2($startdate,$datadate,"steelhome_drc.data_table","dta_ym","dta_6"," dta_type = 'SHQHDAY_4' AND dta_maxValStatus=1 ");
		$tiekuang = $this->gcdao->get_data_table2($startdate,$datadate,"steelhome_drc.data_table","dta_ym","dta_6"," dta_type = 'SHQHDAY_20' AND dta_maxValStatus=1 ");
		$jiaotan = $this->gcdao->get_data_table2($startdate,$datadate,"steelhome_drc.data_table","dta_ym","dta_6","  dta_type = 'SHQHDAY_9' AND dta_maxValStatus=1 ");
		$bq_xuli = round($luowen['value'] -( $tiekuang['value']*1.65 + $jiaotan['value']*0.45+1300 ),2);
		$sq_xuli = round($luowen['sq_value'] -( $tiekuang['sq_value']*1.65 + $jiaotan['sq_value']*0.45+1300 ),2);
		$xlgclr = $this->get_shuju($bq_xuli,$sq_xuli,$luowen['Date']);
		$this->assign("xlgclr",$xlgclr);

		
		$xalwgkc=$this->gcdao->get_sckccity($startdate,$datadate,"  si.varietyid='G2' and  cityname='西安' ");
		$this->assign("xalwgkc",$xalwgkc);
		$cdlwgkc=$this->gcdao->get_sckccity($startdate,$datadate,"  si.varietyid='G2' and  cityname='成都' ");
		$this->assign("cdlwgkc",$cdlwgkc);
		$lzlwgkc=$this->gcdao->get_sckccity($startdate,$datadate,"  si.varietyid='G2' and  cityname='兰州' ");
		$this->assign("lzlwgkc",$lzlwgkc);

		$sclwgkc=$this->gcdao->get_sckcarea($startdate,$datadate," type=5 ");//螺纹钢
		$scxckc=$this->gcdao->get_sckcarea($startdate,$datadate," type=4 ");//线材

		//上海基差
		$shjc = $this->_dao->get_JiChaInfo($startdate,$datadate," Type='1' and  PriceCode='072023'");
		$this->assign("shjc",$shjc);
		//西安基差
		$xajc = $this->_dao->get_JiChaInfo($startdate,$datadate," Type='1' and  PriceCode='562023'");
		$this->assign("xajc",$xajc);
		//螺坯价差
			//日期一致
		$datearr = array();
		$datearr[0] = $this->gcdao->getOne( "select dta_ym from steelhome_drc.data_table where dta_ym<='$datadate' and dta_type = 'SHQHDAY_4' AND dta_maxValStatus=1 order by dta_ym desc limit 1" );
		$datearr[1] = $this->_dao->getOne( "select mconmanagedate from marketconditions where mconmanagedate<='$datadate 23:59:59' and topicture='678411' order by mconmanagedate desc limit 1" );
		$datemin = min($datearr);
		$lwzlhy = $this->gcdao->get_data_table2($startdate,$datemin,"steelhome_drc.data_table","dta_ym","dta_6","  dta_type = 'SHQHDAY_4' AND dta_maxValStatus=1 ");
		$market_678411 = $this->get_marketprice('678411',$datemin);
		$lpjc = $this->get_shuju( round($lwzlhy['value']-$market_678411['678411']['price'],2) , round($lwzlhy['sq_value']-$market_678411['678411']['oldprice'],2) , $lwzlhy["Date"] );
		$this->assign("lpjc",$lpjc);
	  
		//全国主要市场建筑钢材库存
		$sckc['Date']=$sclwgkc['Date'];
		$sckc['kc']=$sclwgkc['kc']+$scxckc['kc'];
		$sckc['sq_kc']=$sclwgkc['sq_kc']+$scxckc['sq_kc'];
		$sckc['kczd1']=$sclwgkc['kczd1']+$scxckc['kczd1'];
		$sckc['kczd']=$this->get_style($sclwgkc['kczd1']+$scxckc['kczd1']);
		$value = abs($sckc['kc']-$sckc['kczd1']) != 0 ? Round($sckc['kczd1']/abs($sckc['kc']-$sckc['kczd1'])*100,2):0;
		$sckc['kczdf']=$this->get_style( $value );
		$this->assign("sckc",$sckc);

		//市场库存+钢厂库存（螺纹钢+线材）
		$sc_gc = $this->get_shuju($sckc['kc']+$qgzygcjckc['value'],$sckc['sq_kc']+$qgzygcjckc['sq_value'],$sckc['Date']);
		// $sc_gc = $this->gcdao->get_kucun_hz($startdate,$datadate); //此处数据更新不及时
		/* 这里光计算的螺纹钢 */
		// $bq_sg = $shichang[0]['value'] + $gangchang[0]['Value'];
		// $sq_sg = $shichang[1]['value'] + $gangchang[1]['Value'];
		// $sc_gc = $this->get_shuju($bq_sg,$sq_sg,date("Y-m-d",strtotime($shichang[0]['time'])));
		$this->assign("sc_gc",$sc_gc);
		
		$gclwgkc=$this->gcdao->get_gckcarea($startdate,$datadate," Type=1 and Area =0  ");//螺纹钢
		$gcxckc=$this->gcdao->get_gckcarea($startdate,$datadate," Type=2 and Area =0 ");//线材
	  
		$gckc['Date']=$gclwgkc['Date'];
		$gckc['kc']=$gclwgkc['kc']+$gcxckc['kc'];
		$gckc['kczd']= floatval( $gclwgkc['kczd'] )+ floatval($gcxckc['kczd']);
		$value = abs($gckc['kc']-$gckc['kczd']) != 0 ? $gckc['kczd']/abs($gckc['kc']-$gckc['kczd'])*100 : 0;
		$gckc['kczdf']=Round( $value ,2);
		$this->assign("gckc",$gckc);

		//合约价差
		$heyue_01=$this->gcdao->get_data_table2($startdate1,$datadate,"steelhome_drc.data_table","dta_ym","dta_6"," dta_type = 'SHQHDAY_4' AND right(dta_1, 2) ='01' ");
		$heyue_05=$this->gcdao->get_data_table2($startdate1,$datadate,"steelhome_drc.data_table","dta_ym","dta_6"," dta_type = 'SHQHDAY_4' AND right(dta_1, 2) ='05' ");
		$heyue_10=$this->gcdao->get_data_table2($startdate1,$datadate,"steelhome_drc.data_table","dta_ym","dta_6"," dta_type = 'SHQHDAY_4' AND right(dta_1, 2) ='10' ");
			//日期一致
		$date_arr = array();
		$date_arr[0] = $heyue_01["Date"];
		$date_arr[1] = $heyue_05["Date"];
		$date_arr[3] = $heyue_10["Date"];
		$date_min = min($date_arr);
		if( strtotime($date_min)<strtotime($heyue_01['Date']) ){
			$heyue_01=$this->gcdao->get_data_table2($startdate1,$date_min,"steelhome_drc.data_table","dta_ym","dta_6"," dta_type = 'SHQHDAY_4' AND right(dta_1, 2) ='01' ");
		}
		if( strtotime($date_min)<strtotime($heyue_05['Date']) ){
			$heyue_05=$this->gcdao->get_data_table2($startdate1,$date_min,"steelhome_drc.data_table","dta_ym","dta_6"," dta_type = 'SHQHDAY_4' AND right(dta_1, 2) ='05' ");
		}
		if( strtotime($date_min)<strtotime($heyue_10['Date']) ){
			$heyue_10=$this->gcdao->get_data_table2($startdate1,$date_min,"steelhome_drc.data_table","dta_ym","dta_6"," dta_type = 'SHQHDAY_4' AND right(dta_1, 2) ='10' ");
		}
		$heyue1001 = $this->get_cha($heyue_10,$heyue_01);
		$heyue0105 = $this->get_cha($heyue_01,$heyue_05);
		$heyue0510 = $this->get_cha($heyue_05,$heyue_10);
		$this->assign("heyue1001",$heyue1001);
		$this->assign("heyue0105",$heyue0105);
		$this->assign("heyue0510",$heyue0510);

		$this->assign("datadate",$datadate);
		$this->assign("data_ch",date("Y年n月j日", strtotime($datadate)));
	}

	function tiekuang($params)
	{
		$this->assign("mode",$params['mode']);
		$datadate=$params["datadate"];
		if($datadate=='')
		{
			$datadate = date('Y-m-d',time());  
			
		}
		$startdate = date('Y-m-d',strtotime("-1month",strtotime($datadate)));  
		$startdate1 = date('Y-m-d',strtotime("-6month",strtotime($datadate)));  

		//普氏指数（美元）
		$pricelist="H98640";
		$shuju=$this->getpricebypricestr($pricelist,$startdate,$datadate);
		$this->assign("shuju",$shuju);

		//青岛港61.5%PB粉矿（元/吨)
		$qdg_PB = $this->_dao->get_marketconditions($startdate,$datadate," mastertopid='1886103' ");
		$this->assign("qdg_PB",$qdg_PB);
		//期货主力合约价格
		$qhzlhy=$this->gcdao->get_data_table2($startdate,$datadate,"steelhome_drc.data_table","dta_ym","dta_6"," dta_type = 'SHQHDAY_20' AND dta_maxValStatus=1 ");
		$this->assign("qhzlhy",$qhzlhy);
		//澳洲铁矿石发货总量（万吨）
		$azfhl=$this->gcdao->get_data_table2($startdate,$datadate,"steelhome_drc.data_table","dta_ym","dta_3"," dta_type = 'TKS_FH_DG_SG' ");
		$this->assign("azfhl",$azfhl);
		//澳洲铁矿石发中国量（万吨）
		$azfwzg=$this->gcdao->get_data_table2($startdate,$datadate,"steelhome_drc.data_table","dta_ym","dta_2"," dta_type = 'TKS_FH_DG_SG' ");
		$this->assign("azfwzg",$azfwzg);
		//巴西铁矿石发货总量（万吨）
		$bxfhl=$this->gcdao->get_data_table2($startdate,$datadate,"steelhome_drc.data_table","dta_ym","dta_4"," dta_type = 'TKS_FH_DG_SG' ");
		$this->assign("bxfhl",$bxfhl);

		//全国46港到港总量（万吨）
		$qgdgl_46=$this->gcdao->get_data_table2($startdate,$datadate,"steelhome_drc.data_table","dta_ym","dta_7"," dta_type = 'TKS_FH_DG_SG' and dta_ym>='2020-05-10' ");
		$this->assign("qgdgl_46",$qgdgl_46);
		//全国46钢日均疏港量（万吨）
		$qgsgl_46=$this->gcdao->get_data_table2($startdate,$datadate,"steelhome_drc.data_table","dta_ym","dta_3"," dta_type= 'JKKCTJ_1' and `dta_1`='全国港口日均疏港量' ");
		$this->assign("qgsgl_46",$qgsgl_46);
		//全国高炉开工率（%）
		$glcnlyl=$this->gcdao->get_data_table($startdate,$datadate," dta_type='GC_KGL' and dta_1='全国' ");
		$this->assign("glcnlyl",$glcnlyl);
		//唐山高炉开工率（%）
		$tsglcnlyl=$this->gcdao->get_data_table($startdate,$datadate,"  dta_type = 'GNCX_A' ");
		$this->assign("tsglcnlyl",$tsglcnlyl);
		//全国生铁产量（万吨）
		$qgstcl=$this->gcdao->get_data_table2($startdate1,$datadate,"steelhome_drc.data_table","dta_ym","dta_2"," dta_type = 'B' AND dta_vartype = '生铁' and  dta_1='合计' ");
		$this->assign("qgstcl",$qgstcl);
		//全国46港铁矿石库存（万吨）
		$qgtkkc=$this->gcdao->get_data_table2($startdate,$datadate,"steelhome_drc.data_table","dta_ym","dta_3"," dta_type = 'JKKCTJ_1' and dta_1='合计' ");
		$this->assign("qgtkkc",$qgtkkc);
		//海外7港铁矿石库存（万吨）
		$hwtkkc=$this->gcdao->get_data_table2($startdate1,$datadate,"steelhome_drc.DataTableBaseInfo as db left join steelhome_drc.data_table as dt on db.id=dt.baseid","date","dta_2"," db.DataType='Java-qgtkskc' ");
		$this->assign("hwtkkc",$hwtkkc);
		//钢厂库存可用天数
		//块矿溢价（美元/干吨度）
		$kkyj=$this->_dao->get_marketconditions($startdate,$datadate," topicture='H98600' ");
		$this->assign("kkyj",$kkyj);
		//61.5%PB粉-56.5%超特粉（元）= 188611（价格id）-1886141（价格id）
		//保持两个价格id日期一致
		$idstr = "188611,1886141";
		$marketdate = $this->get_marketdate($idstr,$startdate,$datadate);
		$market_info = $this->get_marketprice($idstr,$marketdate);
		$PB_cha =  $this->get_shuju( round($market_info['188611']['price']-$market_info['1886141']['price'],2) , round($market_info['188611']['oldprice']-$market_info['1886141']['oldprice'],2)  , $marketdate );
		$this->assign("PB_cha",$PB_cha);

		//主力合约基差
			//日期一致
		$datearr[0] = $this->gcdao->getOne( "select dta_ym from steelhome_drc.data_table where dta_ym<='$datadate' and dta_type = 'SHQHDAY_20' AND dta_maxValStatus=1 order by dta_ym desc limit 1" );
		$datearr[1] = $this->_dao->getOne( "select mconmanagedate from marketconditions where mconmanagedate<='$datadate 23:59:59' and topicture='188611' order by mconmanagedate desc limit 1" );
		$datemin = min($datearr);
		$qhzlhy=$this->gcdao->get_data_table2($startdate,$datemin,"steelhome_drc.data_table","dta_ym","dta_6"," dta_type = 'SHQHDAY_20' AND dta_maxValStatus=1 ");
		$market = $this->get_marketprice("188611",$datemin);
		$bq_zlhyjc = round($market['188611']['price']/0.92-$qhzlhy['value'],2);
		$sq_zlhyjc = round($market['188611']['oldprice']/0.92-$qhzlhy['sq_value'],2);
		$zlhyjc =  $this->get_shuju($bq_zlhyjc,$sq_zlhyjc,$qhzlhy["Date"]);
		$this->assign("zlhyjc",$zlhyjc);

		//螺矿比
		$lwgzlhy = $this->gcdao->get_data_table2($startdate1,$datadate,"steelhome_drc.data_table","dta_ym","dta_6"," dta_type = 'SHQHDAY_4' AND dta_maxValStatus=1 ");
		$date_arr = array();
		$date_arr[0] = $lwgzlhy["Date"];
		$date_arr[1] = $qhzlhy["Date"];
		$date_min = min($date_arr);
		if( strtotime($date_min)<strtotime($lwgzlhy['Date']) ){
			$lwgzlhy = $this->gcdao->get_data_table2($startdate1,$date_min,"steelhome_drc.data_table","dta_ym","dta_6"," dta_type = 'SHQHDAY_4' AND dta_maxValStatus=1 ");
		}
		if( strtotime($date_min)<strtotime($qhzlhy['Date']) ){
			$qhzlhy=$this->gcdao->get_data_table2($startdate,$date_min,"steelhome_drc.data_table","dta_ym","dta_6"," dta_type = 'SHQHDAY_20' AND dta_maxValStatus=1 ");
		}
		$lkb =  $this->get_bi($lwgzlhy,$qhzlhy);
		$this->assign("lkb",$lkb);

		//合约价差
		$heyue_01=$this->gcdao->get_data_table2($startdate1,$datadate,"steelhome_drc.data_table","dta_ym","dta_6"," dta_type = 'SHQHDAY_20' AND right(dta_1, 2) ='01' ");
		$heyue_05=$this->gcdao->get_data_table2($startdate1,$datadate,"steelhome_drc.data_table","dta_ym","dta_6"," dta_type = 'SHQHDAY_20' AND right(dta_1, 2) ='05' ");
		$heyue_09=$this->gcdao->get_data_table2($startdate1,$datadate,"steelhome_drc.data_table","dta_ym","dta_6"," dta_type = 'SHQHDAY_20' AND right(dta_1, 2) ='09' ");
			//日期一致
		$date_arr = array();
		$date_arr[0] = $heyue_01["Date"];
		$date_arr[1] = $heyue_05["Date"];
		$date_arr[3] = $heyue_09["Date"];
		$date_min = min($date_arr);
		if( strtotime($date_min)<strtotime($heyue_01['Date']) ){
			$heyue_01=$this->gcdao->get_data_table2($startdate1,$date_min,"steelhome_drc.data_table","dta_ym","dta_6"," dta_type = 'SHQHDAY_20' AND right(dta_1, 2) ='01' ");
		}
		if( strtotime($date_min)<strtotime($heyue_05['Date']) ){
			$heyue_05=$this->gcdao->get_data_table2($startdate1,$date_min,"steelhome_drc.data_table","dta_ym","dta_6"," dta_type = 'SHQHDAY_20' AND right(dta_1, 2) ='05' ");
		}
		if( strtotime($date_min)<strtotime($heyue_09['Date']) ){
			$heyue_09=$this->gcdao->get_data_table2($startdate1,$date_min,"steelhome_drc.data_table","dta_ym","dta_6"," dta_type = 'SHQHDAY_20' AND right(dta_1, 2) ='09' ");
		}
		$heyue0901 = $this->get_cha($heyue_09,$heyue_01);
		$heyue0105 = $this->get_cha($heyue_01,$heyue_05);
		$heyue0509 = $this->get_cha($heyue_05,$heyue_09);
		$this->assign("heyue0901",$heyue0901);
		$this->assign("heyue0105",$heyue0105);
		$this->assign("heyue0509",$heyue0509);

		$this->assign("datadate",$datadate);
		$this->assign("data_ch",date("Y年n月j日", strtotime($datadate)));
	}

	function getpreweek($qdate,$dta_vartype){
		$sql = "select curStartDate as stime,curEndDate as etime,prevStartDate as stime2,prevEndDate as etime2 from sc_operating_base WHERE curStartDate<='$qdate'  and dta_vartype='$dta_vartype' and isComplete=1 order by curStartDate desc limit 1";
		$days = $this->gcdao->getrow($sql);
		return $days;
	}

	function dtaTypeArray($array,$type){
		$bq_array = array();
		foreach($array as $bq_key=>$bq_value) {
			$gc_id = $bq_value['dta_steelid'];
			$operating = explode(",",$bq_value['operating']);
			// 去除重复的数据（钢厂ID可能重复）
			if (!isset($bq_array[$gc_id]) && in_array($type,$operating)) {
				$bq_array[$gc_id] = $bq_value;
			}
		}
		return $bq_array;
	}

	function getchanneng($bq_array,$dta_vartype){
		$channeng_arr = array();
        $canNeng_total = array();
        $scCanNeng_total = array();
		foreach($bq_array as $bq_key=>$bq_value) {
			// 数据库里面是西南地区，和西北地区 都属于 西部地区
			// if( $bq_value['area'] == "西南地区" || $bq_value['area'] == "西北地区" ){ 
			// 	$bq_value['area'] = "西部地区"; 
			// }

			$canNeng_total['全国'] += intval( $bq_value['dta_2'] );//设备总数
			$scCanNeng_total['全国'] += intval( $bq_value['dta_3'] );//生产设备总数

			$area = $bq_value['area'];
			$canNeng_total[$area] += intval( $bq_value['dta_2'] );//总产能
			$scCanNeng_total[$area] += intval( $bq_value['dta_3'] );//生产产能

		}
		$channeng_arr['canNeng'] = $canNeng_total;
		$channeng_arr['scCanNeng'] = $scCanNeng_total;
		return($channeng_arr);
	}

	public function getkgl($channeng){
		$canNeng = $channeng['canNeng'];
		$scCanNeng = $channeng['scCanNeng'];
		$kgl_arr = array();
		foreach($canNeng as $canNeng_k => $canNeng_v){
			$kgl_arr[$canNeng_k] =  $scCanNeng[$canNeng_k]/$canNeng_v*100 ;
		}
		// echo"<pre>";print_r($scCanNeng);
		return($kgl_arr);
	}

	function numformat($data){
        if($data==''){
            return "―";
        }else{
            return (float)$data;
        }
	}

	function feigang($params)
	{
		$this->assign("mode",$params['mode']);
		$datadate=$params["datadate"];
		if($datadate=='')
		{
			$datadate = date('Y-m-d',time());  
			
		}
		$startdate = date('Y-m-d',strtotime("-1month",strtotime($datadate)));  
		$startdate1 = date('Y-m-d',strtotime("-6month",strtotime($datadate)));  

		// 全国废钢价格指数（元）
		$qgfg=$this->_dao->get_data_table2($startdate,$datadate,"shpi_material","dateday","price"," topicture='4' ");
		$this->assign("qgfg",$qgfg);
		//华北废钢价格指数（元）
		$hbfg=$this->_dao->get_data_table2($startdate,$datadate,"shpi_material","dateday","price"," topicture='11' ");
		$this->assign("hbfg",$hbfg);
		//华东废钢价格指数（元）
		$hdfg=$this->_dao->get_data_table2($startdate,$datadate,"shpi_material","dateday","price"," topicture='10' ");
		$this->assign("hdfg",$hdfg);
		//西南废钢价格指数（元）
		$xnfg=$this->_dao->get_data_table2($startdate,$datadate,"shpi_material","dateday","price"," topicture='14' ");
		$this->assign("xnfg",$xnfg);

		/* 需求 started */
		$qdate = $datadate;
		$dta_vartype = 5;
		//获取本期与上期开始结束时间
		$dates = $this->getpreweek($qdate,$dta_vartype);
		$stime = $dates['stime'];$etime = $dates['etime'];
		$stime2 = $dates['stime2'];$etime2 = $dates['etime2'];
		$date_ch = date("n月j日",strtotime($etime));
		// 本期研究中心开工率数据
        $bq_cn_array = $this->gcdao->get_canNengLYL($stime,$etime,$dta_vartype);
		$bq_array = $this->dtaTypeArray($bq_cn_array,$dta_vartype);// 去除重复后的数组
		// echo count($bq_cn_array).','.count($bq_array);
		// 上期研究中心开工率数据
		$sq_cn_array = $this->gcdao->get_canNengLYL($stime2,$etime2,$dta_vartype);
		$sq_array = $this->dtaTypeArray($sq_cn_array,$dta_vartype);// 去除重复后的数组
		//获取产能
		$bq_cn = $this->getchanneng($bq_array,$dta_vartype);
		$sq_cn = $this->getchanneng($sq_array,$dta_vartype);
		//获取开工率
		$bqkgl_arr = $this->getkgl($bq_cn);
		$sqkgl_arr = $this->getkgl($sq_cn);
		$arr_title = array (
			'1'=>'全国',
			'2'=>'华北地区',
			'3'=>'华东地区',
			'4'=>'中南地区',
			'5'=>'东北地区',
			'6'=>'西南地区',
			'7'=>'西北地区'
		);
		$xq_shuju = array();
		foreach($arr_title as $title_key=>$title_value){
			$bq_kgl = $this->numformat( round($bqkgl_arr[$title_value],2) );
			$kgl_zd1 = round($bqkgl_arr[$title_value],2) - round($sqkgl_arr[$title_value],2) ;
			$kgl_zd = $this->get_style(round($bqkgl_arr[$title_value],2) - round($sqkgl_arr[$title_value],2));
			$value = round($sqkgl_arr[$title_value],2)!= 0 ? Round($kgl_zd1/round($sqkgl_arr[$title_value],2)*100,2):0;
			$kgl_zdf=$this->get_style( $value );
			$xq_shuju[$title_key]=array("value"=>$bq_kgl,"valuezd"=>$kgl_zd,"valuezdf"=>$kgl_zdf,"Date"=>$etime );
		}
		$this->assign("xq_shuju",$xq_shuju);
		/* 需求 ended */

		//成本
		//江苏电炉螺纹钢成本
		$jsdlcb=$this->gcdao->get_data_table2($startdate1,$datadate,"steelhome_drc.sg_HangYeChengBenIndex","ndate","chenben_tax"," type='163' and mc_type=2 ");
		//成都电炉螺纹钢成本
		$cddlcb=$this->gcdao->get_data_table2($startdate1,$datadate,"steelhome_drc.sg_HangYeChengBenIndex","ndate","chenben_tax"," type='135' and mc_type=2 ");
		//江苏铁水成本
		$jstscb=$this->gcdao->get_data_table2($startdate1,$datadate,"steelhome_drc.sg_HangYeChengBenIndex","ndate","chenben_tax"," type='160' and mc_type=2 ");
		//江苏沿江含税成本
		$jsyjhscb=$this->gcdao->get_data_table2($startdate1,$datadate,"steelhome_drc.sg_HangYeChengBenIndex","ndate","chenben_tax"," type='45' and mc_type=2 ");
		// print_r($cddlcb);
		$this->assign("jsdlcb",$jsdlcb);
		$this->assign("cddlcb",$cddlcb);
		$this->assign("jstscb",$jstscb);

		// 利润
		$datearr[0] = $jsdlcb['Date'];
		$datearr[1] = $cddlcb['Date'];
		$datearr[2] = $jstscb['Date'];
		$datearr[3] = $jsyjhscb['Date'];
		$idstr = "072023,532023";
		$datearr[4] = $this->get_marketdate($idstr,$startdate,$datadate);
		$datemin = min($datearr);
        $market = array();
        $jsdllr = array();
        $cddllr = array();
        $jsclclr = array();
		if ($datemin) {
            $market = $this->get_marketprice($idstr,$datemin);
            // 江苏电炉螺纹钢利润
            $jsdllr = $this->get_shuju(round($market["072023"]['price']-$jsdlcb['value'],2),round($market["072023"]['oldprice']-$jsdlcb['sq_value'],2),$datemin);
            // 成都电炉螺纹钢利润
            $cddllr = $this->get_shuju(round($market["532023"]['price']-$cddlcb['value'],2),round($market["532023"]['oldprice']-$cddlcb['sq_value'],2),$datemin);
            // 江苏长流程螺纹钢利润
            $jsclclr = $this->get_shuju(round($market["072023"]['price']-$jsyjhscb['value'],2),round($market["072023"]['oldprice']-$jsyjhscb['sq_value'],2),$datemin);
        }

		// print_r($jsdllr);
		$this->assign("jsdllr",$jsdllr);
		$this->assign("cddllr",$cddllr);
		$this->assign("jsclclr",$jsclclr);

		//上海螺废差（元/吨）
		$idstr = "072023,118210";
		$marketdate = $this->get_marketdate($idstr,$startdate,$datadate);
        $shlfc = array();
		if ( $marketdate ) {
            $market_info = $this->get_marketprice($idstr,$marketdate);
            $shlfc = $this->get_shuju(round($market_info['072023']['price']-$market_info['118210']['price'],2),round($market_info['072023']['oldprice']-$market_info['118210']['oldprice'],2),$marketdate);
        }

		$this->assign("shlfc",$shlfc);
		// echo"<pre>";print_r($market_info);

		// 成都螺废差
		$idstr = "532023,758210";
		$marketdate = $this->get_marketdate($idstr,$startdate,$datadate);
		$market_info = $this->get_marketprice($idstr,$marketdate);
		$cdlfc = $this->get_shuju(round($market_info['532023']['price']-$market_info['758210']['price'],2),round($market_info['532023']['oldprice']-$market_info['758210']['oldprice'],2),$marketdate);
		$this->assign("cdlfc",$cdlfc);
		// echo"<pre>";print_r($market_info);

		$this->assign("datadate",$datadate);
		$this->assign("data_ch",date("Y年n月j日", strtotime($datadate)));
	} 

	function tiehejin($params)
	{
		$this->assign("mode",$params['mode']);
		$datadate=$params["datadate"];
		if($datadate=='')
		{
			$datadate = date('Y-m-d',time());  
			
		}
		$startdate = date('Y-m-d',strtotime("-1month",strtotime($datadate)));  
		$startdate1 = date('Y-m-d',strtotime("-6month",strtotime($datadate)));  

		//港口锰矿库存（万吨）
		$gkmkkc=$this->gcdao->get_data_table2($startdate,$datadate,"steelhome_drc.data_table","dta_ym","dta_2"," dta_type = 'JKKC_M' and dta_1='总计' ");
		$this->assign("gkmkkc",$gkmkkc);
		//全国铁合金月产量（万吨）
		$thjytj=$this->gcdao->get_data_table2($startdate1,$datadate,"steelhome_drc.data_table","dta_ym","dta_2"," dta_type = 'A' and  dta_1='铁合金' ");
		$this->assign("thjytj",$thjytj);
		// 全国硅锰月产量（万吨） 
		$gmytj=$this->gcdao->get_data_table3($startdate1,$datadate,"","date","dta_1"," 1 ");
		$this->assign("gmytj",$gmytj);
		// 主要钢厂螺纹钢硅锰需求（万吨）
		$gmxq=$this->gcdao->get_data_table2($startdate1,$datadate,"steelhome_drc.data_table","dta_ym","dta_2"," dta_type = 'A' and  dta_1='钢筋' ");
		$gmxq['value'] = round($gmxq['value']*0.028,2);
		$gmxq['sq_value'] = round($gmxq['sq_value']*0.028,2);
		$gmxq['valuezd1']=$gmxq['value']-$gmxq['sq_value'];
		$gmxq['valuezd']=$this->get_style($gmxq['value']-$gmxq['sq_value']);
		$value = abs($gmxq['sq_value'])!= 0 ? Round($gmxq['valuezd1']/abs($gmxq['sq_value'])*100,2) : 0;
  		$gmxq['valuezdf']=$this->get_style( $value );
		$this->assign("gmxq",$gmxq);

		//河钢月采购量
		$sql = "select id from SteelCaiGou_base where steel_id='364' and VarietyId='thj,072,c702' and  post_date>='$startdate1  00:00:00' and post_date<='$datadate  23:59:59' order by post_date desc limit 2";
		$baseids = $this->gcdao->getones($sql);
		$baseids_str = implode("','",$baseids);
		$sql = "select sb_id,the_price_tax,num,ftime from SteelCaiGou_Info where sb_id in ('$baseids_str ')";
		$SteelCaiGou_Info = $this->gcdao->query($sql);
		$SteelCaiGou = array();
		foreach( $SteelCaiGou_Info as $k => $v ){
			$SteelCaiGou[$v['sb_id']] = $v;
		}
		$hgycgl = $this->get_shuju( $SteelCaiGou[$baseids[0]]['num'], $SteelCaiGou[$baseids[1]]['num'], $SteelCaiGou[$baseids[0]]['ftime'] );
		$this->assign("hgycgl",$hgycgl);
		// 河钢招标价格
		$hgzbjg = $this->get_shuju( $SteelCaiGou[$baseids[0]]['the_price_tax'], $SteelCaiGou[$baseids[1]]['the_price_tax'], $SteelCaiGou[$baseids[0]]['ftime'] );
		$this->assign("hgzbjg",$hgzbjg);

		// 内蒙硅锰出厂利润 = id 418910  - 硅锰成本FeMn68Si18
		 //日期一致
		$datearr[0] = $this->_dao->getOne( "select Date from ng_TieHeJinChengBenIndex where Date<='$datadate' and type='2' order by Date desc limit 1" );
		$datearr[1] = $this->_dao->getOne( "select mconmanagedate from marketconditions where mconmanagedate<='$datadate 23:59:59' and topicture='418910' order by mconmanagedate desc limit 1" );
		$datemin = min($datearr);
		$market_info = $this->get_marketprice('418910',$datemin);
		$gmcb = $this->_dao->getOnes( "select indexValue from ng_TieHeJinChengBenIndex where Date<='$datemin 23:59:59' and type='2' order by Date desc limit 2");
		$nmggmcclr = $this->get_shuju(round($market_info['418910']['price']-$gmcb[0],2),round($market_info['418910']['oldprice']-$gmcb[1],2),date('Y-m-d',strtotime($datemin)));
		$this->assign("nmggmcclr",$nmggmcclr);

		// 内蒙6517出厂价  南非锰矿（Mn38块）价格
		$pricelist="418910,N99510";
		$shuju=$this->getpricebypricestr($pricelist,$startdate,$datadate);
		$this->assign("shuju",$shuju);

		// 内蒙硅锰6517基差 = 418910（价格id）- 锰硅期货主力合约收盘价
		$mkqhzl = $this->gcdao->get_data_table2($startdate,$datadate,"steelhome_drc.data_table","dta_ym","dta_6","dta_type = 'SHQHDAY_23' AND dta_maxValStatus=1 ");
		$market_info = $this->get_marketprice('418910',$mkqhzl["Date"]);
		$mggmjc = $this->get_shuju(round($market_info['418910']['price']-$mkqhzl['value'],2),round($market_info['418910']['oldprice']-$mkqhzl['sq_value'],2),$mkqhzl["Date"]);
		$this->assign("mggmjc",$mggmjc);

		//南北方价差
		$idstr = "418910,548910";
		$marketdate = $this->get_marketdate($idstr,$startdate,$datadate);
		$market_info = $this->get_marketprice($idstr,$marketdate);
		$nbfjc = $this->get_shuju(round($market_info['418910']['price']-$market_info['548910']['price'],2),round($market_info['418910']['oldprice']-$market_info['548910']['oldprice'],2),$marketdate);
		$this->assign("nbfjc",$nbfjc);

		//硅锰硅铁价差
		$idstr = "418910,W28710";
		$marketdate = $this->get_marketdate($idstr,$startdate,$datadate);
		$market_info = $this->get_marketprice($idstr,$marketdate);
		$gmgtjc = $this->get_shuju(round($market_info['418910']['price']-$market_info['W28710']['price'],2),round($market_info['418910']['oldprice']-$market_info['W28710']['oldprice'],2),$marketdate);
		$this->assign("gmgtjc",$gmgtjc);

		$this->assign("datadate",$datadate);
		$this->assign("data_ch",date("Y年n月j日", strtotime($datadate)));
	}
	
	function lwlqhtb($params)
	{
		$datadate=$params["datadate"];
		if($datadate=='')
		{
			$datadate = date('Y-m-d',time());  
			
		}
		$startdate = date('Y-m-d',strtotime("-1month",strtotime($datadate)));  
		$startdate1 = date('Y-m-d',strtotime("-6month",strtotime($datadate)));  
		$pricelist="562023,532023,072023,372023,678411";
		$shuju=$this->getpricebypricestr($pricelist,$datadate,$datadate);
		$this->assign("shuju",$shuju);

	    //螺纹钢
		$sql = "SELECT * FROM steelhome_drc.`data_table` WHERE  `dta_type`='SHQHDAY_4'  and `dta_ym`>='$date1 00:00:00' and `dta_ym`<='$date 23:59:59' and `dta_maxValStatus`='1' order by dta_ym desc limit 1 ";//本期
		$arr = $this->gcdao->getrow ( $sql );


	    //全国高炉开工率（%）
		$glcnlyl=$this->gcdao->get_data_table($startdate,$datadate," dta_type='GC_KGL' and dta_1='全国' ");
		$this->assign("glcnlyl",$glcnlyl);
		//唐山高炉开工率（%）
		$tsglcnlyl=$this->gcdao->get_data_table($startdate,$datadate,"  dta_type = 'GNCX_A' ");
		$this->assign("tsglcnlyl",$tsglcnlyl);
	   
		$qgdlcnkgl=$this->gcdao->get_sc_operating_area_hz($startdate,$datadate,"  dta_vartype = '5' and CityId='-1' ");
		$this->assign("qgdlcnkgl",$qgdlcnkgl);
		$hddlcnkgl=$this->gcdao->get_sc_operating_area_hz($startdate,$datadate,"  dta_vartype = '5' and CityId='华东地区' ");
		$this->assign("hddlcnkgl",$hddlcnkgl);
		$xndlcnkgl=$this->gcdao->get_sc_operating_area_hz($startdate,$datadate,"  dta_vartype = '5' and CityId='西南地区' ");
		$this->assign("xndlcnkgl",$xndlcnkgl);
		
		$xalwgkc=$this->gcdao->get_sckccity($startdate,$datadate,"  si.varietyid='G2' and  cityname='西安' ");
		$this->assign("xalwgkc",$xalwgkc);
		$cdlwgkc=$this->gcdao->get_sckccity($startdate,$datadate,"  si.varietyid='G2' and  cityname='成都' ");
		$this->assign("cdlwgkc",$cdlwgkc);
		$lzlwgkc=$this->gcdao->get_sckccity($startdate,$datadate,"  si.varietyid='G2' and  cityname='兰州' ");
		$this->assign("lzlwgkc",$lzlwgkc);

		$sclwgkc=$this->gcdao->get_sckcarea($startdate,$datadate," type=5 ");//螺纹钢
		$scxckc=$this->gcdao->get_sckcarea($startdate,$datadate," type=4 ");//线材
	  
		$sckc['Date']=$sclwgkc['Date'];
		$sckc['kc']=$sclwgkc['kc']+$scxckc['kc'];
		$sckc['kczd']=$sclwgkc['kczd']+$scxckc['kczd'];
		$sckc['kczdf']=Round($sckc['kczd']/abs($sckc['kc']-$sckc['kczd'])*100,2);

		$this->assign("sckc",$sckc);
		
		$gclwgkc=$this->gcdao->get_gckcarea($startdate,$datadate," Type=1 and Area =0  ");//螺纹钢
		$gcxckc=$this->gcdao->get_gckcarea($startdate,$datadate," Type=2 and Area =0 ");//线材
	  
		$gckc['Date']=$gclwgkc['Date'];
		$gckc['kc']=$gclwgkc['kc']+$gcxckc['kc'];
		$gckc['kczd']=$gclwgkc['kczd']+$gcxckc['kczd'];
		$gckc['kczdf']=Round($gckc['kczd']/abs($gckc['kc']-$gckc['kczd'])*100,2);

		$this->assign("gckc",$gckc);

	}

	function get_marketdate($pricestr,$startdate,$datadate){
		$idnumber=explode(',',$pricestr);
		$date_arr = array();
		foreach($idnumber as $k => $v){
			if (strlen ( $v ) == 6){//判断id 的字符长度 
				$sql = "select mconmanagedate from marketconditions where topicture='".$v."' and mconmanagedate>='".$startdate." 00:00:00' and mconmanagedate<='".$datadate." 23:59:59' order by mconmanagedate desc limit 1";
			}
			if (strlen ($v ) == 7) {
				$sql = "select mconmanagedate from marketconditions  where mastertopid='".$v."' and mconmanagedate>='".$startdate." 00:00:00' and mconmanagedate<='".$datadate." 23:59:59' order by mconmanagedate desc limit 1";
			}
			$mconmanagedate =  $this->_dao->getOne( $sql );
			$date_arr[] = date("Y-m-d",strtotime($mconmanagedate));
		}
		return min($date_arr);
	}

	function get_marketprice($pricestr,$date){
		$idnumber=explode(',',$pricestr);
		$six=$seven=array();
		foreach($idnumber  as $id ){
			if (strlen ( $id ) == 6){//判断id 的字符长度 
				if(!in_array($id ,$six)){
					$six[]= $id ; 			
				} 							
			} 
			if (strlen ($id ) == 7) {
				if(!in_array($id,$seven)){
					$seven[]= $id;
				} 
			}
		}
		$sixid_str=implode("','",$six);
		$sevenid_str=implode("','",$seven);
		$mconmanagedate="(mconmanagedate>='".$date." 00:00:00' and mconmanagedate<='".$date." 23:59:59')";
		$PriceList1 = array();
		$PriceList2 = array();
		if($sixid_str!=''){
			$PriceListSQL=" select marketconditions.price,marketconditions.oldprice,marketconditions.topicture, marketconditions.mconmanagedate from marketconditions
			where    $mconmanagedate and  marketconditions.topicture in ('$sixid_str') order by  marketconditions.mconmanagedate desc limit ".(count($six));
			$PriceList1 = $this->_dao->query( $PriceListSQL );
		 //    echo $PriceListSQL;
		}
		if($sevenid_str!=''){
			$PriceListSQL=" select marketconditions.price,marketconditions.oldprice,marketconditions.mastertopid as topicture, marketconditions.mconmanagedate from marketconditions
			where $mconmanagedate and  marketconditions.mastertopid in ('$sevenid_str') order by  marketconditions.mconmanagedate desc limit ".(count($seven));
			$PriceList2 = $this->_dao->query( $PriceListSQL );
		}
		$PriceList = array_merge($PriceList1,$PriceList2);
		foreach($PriceList  as $k => $v )
		{
			$price = array(); 
			$oldprice = array();
			$price = explode("-", $v['price']);
			$oldprice = explode("-", $v['oldprice']);
			if( count($price,0)>1 ){
				$PriceList[$k]['price'] = round(($price[0]+$price[1])/2,2);
			}
			if( count($oldprice,0)>1 ){
				$PriceList[$k]['oldprice'] = round(($oldprice[0]+$oldprice[1])/2,2);
			}
		}
		// echo"<pre>";print_r($PriceList);
		$Price1 = array();
		foreach($PriceList  as $v )
		{
			$Price1[$v['topicture']] = $v;
		}
		return $Price1;
	}

	function getpricebypricestr($pricestr,$stime,$etime)
	{
		//echo $time;
		    $idnumber=explode(',',$pricestr);
			//echo count($idnumber);
			$six=$seven=array();
			foreach($idnumber  as $id ){
				if (strlen ( $id ) == 6){//判断id 的字符长度 
					if(!in_array($id ,$six)){
						$six[]= $id ; 			
					} 							
				} 
				if (strlen ($id ) == 7) {
					if(!in_array($id,$seven)){
						$seven[]= $id;
					} 
				}
			}
		  $sixid_str=implode("','",$six);
 		  $sevenid_str=implode("','",$seven);
		$mconmanagedate.="(mconmanagedate>='".$stime." 00:00:00' and mconmanagedate<='".$etime." 23:59:59')";
		 if($sixid_str!=''){
 		  	$PriceListSQL=" select marketconditions.price,marketconditions.oldprice,marketconditions.topicture, marketconditions.mconmanagedate from marketconditions
			   where    $mconmanagedate and  marketconditions.topicture in ('$sixid_str') order by  marketconditions.mconmanagedate desc limit ".(count($six)*2);
			//    echo $PriceListSQL;
 		  }
 		  if($sevenid_str!=''){
 		  	$PriceListSQL=" select marketconditions.price,marketconditions.oldprice,marketconditions.mastertopid as topicture, marketconditions.mconmanagedate from marketconditions
 		  	where $mconmanagedate and  marketconditions.mastertopid in ('$sevenid_str') order by  marketconditions.mconmanagedate desc limit ".(count($seven)*2);
 		  }
 		  if($sixid_str!=''&&$sevenid_str!=''){
	 		  $PriceListSQL=" select marketconditions.price,marketconditions.oldprice,marketconditions.topicture, marketconditions.mconmanagedate from marketconditions  
	 		  where    $mconmanagedate and  marketconditions.topicture in ('$sixid_str')  order by  marketconditions.mconmanagedate desc limit ".(count($six)*2)." UNION( select marketconditions.price,marketconditions.oldprice,marketconditions.mastertopid as topicture,marketconditions.mconmanagedate from marketconditions 
	 		  where $mconmanagedate  and marketconditions.mastertopid in('$sevenid_str')) order by  marketconditions.mconmanagedate desc limit ".(count($seven)*2);
 		  }
		  //echo $PriceListSQL;
		   $PriceList= $this->_dao->query( $PriceListSQL ); 

		   foreach($PriceList  as $k => $v )
		   {
			   $price = array(); 
			   $oldprice = array();
			   $price = explode("-", $v['price']);
			   $oldprice = explode("-", $v['oldprice']);
			   if( count($price,0)>1 ){
				   $PriceList[$k]['price'] = round(($price[0]+$price[1])/2,2);
			   }
			   if( count($oldprice,0)>1 ){
				   $PriceList[$k]['oldprice'] = round(($oldprice[0]+$oldprice[1])/2,2);
			   }
		   }
		// echo"<pre>";print_r($PriceList);exit;

		   $dataarr =array();//数据数组
		   $datearr=array();//日期数组
		   
		   $pricelistarr=array();
		   $pricelistarr1=array();
		   foreach($PriceList  as $v )
		   {
			 $date=date('Y-m-d',strtotime($v[mconmanagedate]));
			 if(!in_array($v['topicture'],$pricelistarr1))
			 {
				$pricelistarr1[] = $v['topicture'];
				$pricelistarr[$v['topicture']]['price']=$v['price'];

				$pricelistarr[$v['topicture']]['pricezd1']=$v['price']-$v['oldprice'];
				$pricelistarr[$v['topicture']]['pricezd']=$this->get_style($v['price']-$v['oldprice']);
				$pricelistarr[$v['topicture']]['pricezdf']=$this->get_style(Round($pricelistarr[$v['topicture']]['pricezd1']/abs($v['oldprice'])*100,2));
				$pricelistarr[$v['topicture']]['Date']=date("Y-m-d",strtotime($v['mconmanagedate']));
			 }

			//  if(strstr($v['price'],"-")){
			// 	$avgprice = explode("-",$v['price']);
			// 	$v['price'] = round(($avgprice['0']+$avgprice['1'])/2,2);
			// }
			//  $dataarr[$date][$v['topicture']]=  $v['price'];

		   }
		//    echo"<pre>";print_r($pricelistarr);
		   //$data['date']=$datearr;
		   //$data['data']=$dataarr;
	
		  return $pricelistarr;
	}

	 

     



	// private function formatpage($page){
	// 	$page=(int)$page;
	// 	if($page-1<0) $page=1;
	// 	return $page;
	// }

	// private function getcitybyprovince($provinceid){
	// 	$idlen=strlen($provinceid);
	// 	$ret=array();
	// 	foreach ($GLOBALS['channeng_placesMap'] as $key=>$value) {
	// 		if($key!=$provinceid."0000"&&strlen($key)==4+$idlen&&substr($key,0,$idlen)==$provinceid&&$key%100==0) $ret[]=$value;
	// 	}
	// 	return $ret;
	// }

	// private function getallprovince(){
	// 	$ret=array();
	// 	$province=array();
	// 	foreach ($GLOBALS['channeng_placesMap'] as $key=>$value) {
	// 		if(substr($key,-4,-1)=="0000") {
	// 			$key=str_replace("0000","",$key);
	// 			$value=str_replace("省","",$value);
	// 			$ret[$key]=$value;
	// 		}
	// 	}
	// 	//$this->P($ret);
	// 	return $province;
	// }

	function P($arr){
		if($_GET[debug]==1){
		print"<pre>";print_r($arr);print"</pre>";
		}
	}

	public function index($params)
	{

		$this->lwlqhtb($params);
		
		$mode=$_GET['mode'];
		$datadate=$params["datadate"];
		$name='';
		$where="";

		$this->assign("marketarr",$marketarr);
		$this->assign("market",$market);
        
        
		if($datadate=='')
		{
			$datadate = date('Y-m-d',time());  
			
		}
		$startdate = date('Y-m-d',strtotime("-1month",strtotime($datadate)));  
		$startdate1 = date('Y-m-d',strtotime("-6month",strtotime($datadate)));  
		 
		//226家独立焦化厂产能利用率（%）
		$dljqcnlyl=$this->gcdao->get_sc_KaiGongLvHuiZong($startdate,$datadate);
		$this->assign("dljqcnlyl",$dljqcnlyl);

        //全国焦炭产量（万吨）
		$qgjtcl=$this->_dao->get_L2Data($startdate1,$datadate,"  L2id=327 ");
		$this->assign("qgjtcl",$qgjtcl);
		
		
		//全国高炉开工率（%）
		$glcnlyl=$this->gcdao->get_data_table($startdate,$datadate," dta_type='GC_KGL' and dta_1='全国' ");
		$this->assign("glcnlyl",$glcnlyl);
		//唐山高炉开工率（%）
		$tsglcnlyl=$this->gcdao->get_data_table($startdate,$datadate,"  dta_type = 'GNCX_A' ");
		$this->assign("tsglcnlyl",$tsglcnlyl);

		//全国生铁产量（万吨）
		$qgstcl=$this->_dao->get_L2Data($startdate1,$datadate,"  L2id=188 ");
		$this->assign("qgstcl",$qgstcl);

		//100家独立焦化厂库存（万吨）
		$dljqkc=$this->gcdao->get_sc_othercom_stock_hz($startdate,$datadate,"  AreaCode=0 ");
		$this->assign("dljqkc",$dljqkc);
		//80家钢厂库存
		$bsjgckc=$this->gcdao->get_MeiJiaoNeedAndStockHuiZong($startdate,$datadate,"   `Area` =7 AND  `PinZhong` =1 ");
		$this->assign("bsjgckc",$bsjgckc);

		
		$gkkc=$this->gcdao->get_gkkc($startdate,$datadate,"  db.DataType='Java-SGGKJTKC' and  dta_1='合计' ");
		$this->assign("gkkc",$gkkc);

		$jqdjlr=$this->gcdao->get_JiaoQiLiLunDunLiRun($startdate,$datadate);
		$this->assign("jqdjlr",$jqdjlr);
		  
		 
			// if(strtotime($startdate)>strtotime($enddate))
			// {      
			// 	alert( "结束日期不能早于开始日期！" );
			// 	$params['enddate'] = '';  
			// 	$params['startdate'] = '';  
			// 	goURL( "sgqhtb.php?".http_build_query($params , '' , '&') );
			// 	exit;
			// }
			
			
			// if(strtotime($startdate)<strtotime($enddate." -1 year"))
			// {      
			// 	alert( "时间段不能超过一年" );


			// 	$params['enddate'] = '';  
			// 	$params['startdate'] = ''; 
				
			// 	goURL( "dbmaketprice.php?".http_build_query($params , '' , '&') );
			// 	exit;
			// }

         
		  $infolist=$shuju['data'];
		  $datearr=$shuju['date'];

		  foreach($datearr  as $v )
		  {
			$datearray[$v]=date('n月j日',strtotime($v));
		  }

		  //print_r($datearr);
		$this->P($infor);
		
		$this->assign("datadate",$datadate);
		$this->assign("data_ch",date("Y年n月j日", strtotime($datadate)));
		$this->assign("type",$type);
		$this->assign("sumcount",$sumcount);
		$this->assign("sumcn",$sumcn);
		$this->assign("datearr",$datearray);
		$this->assign("infolist",$infolist);
		$this->assign("mode",$mode);
		$this->assign("mode",$mode);
		$this->assign("GUID",$GUID);
		$this->assign("amount",count($infor));
		$this->assign("name",$name);
	}

}
?>