<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );

class sgcwrbController extends AbstractController{
  
    public function __construct(){
        parent::__construct();
        $this->_action->setDao( new sgcwrbDao("DRCW") );  //drc
        $this->_action->t1Dao=new sgcwrbDao("MAIN");  //
        $this->_action->homeDao=new sgcwrbDao("91R");
        $this->_action->gcDao=new sgcwrbDao("GC");
    }

    public function _dopre()
    {
        $this->_action->checkSession();
    }

    public function v_index(){
        $this->_action->index( $this->_request );
    }

    public function v_xsjzpj(){
        $this->_action->xsjzpj( $this->_request );
    }
    // 销售计划完成情况
    public function v_xsjh(){
        $this->_action->xsjh( $this->_request );
    }
    // 采购节奏评价
    public function v_cgjzpj(){
        $this->_action->cgjzpj( $this->_request );
    }

    public function v_lr(){
        $this->_action->lr( $this->_request );
    }

    public function do_add(){
        $this->_action->doadd( $this->_request );
    }

    public function do_del(){
        $this->_action->dodel( $this->_request );
    }
}
?>