<?php


class sglrAction extends AbstractAction
{

    public function __construct()
    {
        parent::__construct();
    }

    public function checkSession()
    {
    }


    public function lhIndex($params){
      
        $sdate=$params['sdate'];
        $edate=$params['edate'];
        if($edate==""){
            $edate=date("Y-m-d");
            $sdate=date("Y-m-d",strtotime("-1 year",strtotime($edate)));
        }
        $type="'5','10'";
        $info= $this->_dao->getlrinfo($type,$sdate,$edate);
        
        echo json_encode($info);exit;
       // print_r( $info);
       
    }


}

?>