<?php
header('Content-Type:text/html;charset=utf8');
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );

class sgxpcdjkbController extends AbstractController{
  
  public function __construct(){
    parent::__construct();
	$this->_action->setDao( new sgxpcdjkbDao("91R") );
	$this->_action->gcdao = new sgxpcdjkbDao('GC') ;
	//$this->_action->t1Dao=new dbmarketpriceDao("MAIN");
  }

	public function v_index(){
		$this->_action->index( $this->_request );
	}

}
?>