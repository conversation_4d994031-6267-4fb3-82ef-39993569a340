<?php
$GLOBALS['marketarr'] = array(
    '1' => '沈阳',
    '2' => '鞍山',
    '3' => '大连',
    '4' => '长春',
    '5' => '哈尔滨'
);
$GLOBALS['pricelist'] = array(
    '1' => '4531123,4531121,4531122,453112,4542122,4542123,454212,4544123,4544121,454412,4530126,4530121,453012,4511038,4511031,4511039,451103,451333A,4513326,4513123,451312,4520231,4520235,4520437,4520233,4520232,452023',
    '2' => '4631123,4631122,4631121,463112,4642122,464212,4630122,4630124,463012',
    '3' => '4731122,4731121,4731124,473112,4742122,4742121,474212,4730122,4730125,473018,4711031,4711032,471103,4713122,4713123,4713121,471312,4720231,4720236,4720238,4720237,4720233,472023',
    '4' => '5030123,5030121,503012,5011062,5011034,5011031,501103,5013123,5013323,501312,5020231,5020439,5020431,502043',
    '5' => '5130121,513012,5111036,5111031,5111032,511103,5113131,5113121,511312,5120231,5120235,5120234,512023'
);

class dbmarketpriceAction extends AbstractAction
{
    public function __construct()
    {
        parent::__construct();
    }

    public function index($params)
    {
        $GUID = $params['GUID'];
        $action = $params['action'];        //接口名称
        $SignCS = $params['SignCS'];
        if (!empty($params['mc_type'])) $mc_type = $params['mc_type'];
        else $mc_type = 0;
        $ip = $this->getIP();

        //$selectprovince=$params['state'];
        //$selectcity=$params['city'];
        $mode = $_GET['mode'];
        $startdate = $params["startdate"];
        $enddate = $params["enddate"];
        //echo $mode;
        $name = '';
        $where = "";
        $marketarr = $GLOBALS['marketarr'];
        $pricelist = $GLOBALS['pricelist'];
        $market = $params['market'] ? $params['market'] : '1';

        $this->assign("marketarr", $marketarr);
        $this->assign("market", $market);

        //判断是不是测试账号
        if (array_key_exists($GUID, $GLOBALS["testaccountarr"]) && $SignCS != '' && (in_array($ip, $GLOBALS["testip"]) || $GLOBALS["nowtest"] == 1)) {
            $user = $GLOBALS["testaccountuser"];
        } else {
            $user = $this->t1Dao->getUser($GUID, $SignCS, $mc_type);
        }
        $mid = $user['Mid'];
        $uid = $user['Uid'];


        if ($startdate == '') {
            $enddate = date('Y-m-d', time());

            $PriceListSQL = " select managedate from marketrecord
			where (managedate>='" . date('Y-m-d', strtotime("-2months", strtotime($enddate))) . " 00:00:00' and managedate<='" . $enddate . " 23:59:59') and varietyid='gc,013,179,c03' and  cityid='db,0045'  and  isimport=0 order by managedate desc limit 14";
            $PriceList = $this->_dao->query($PriceListSQL);

            if ($PriceList[13]['managedate']) {
                $startdate = date('Y-m-d', strtotime($PriceList[13]['managedate']));
            } else {
                $startdate = date('Y-m-d', strtotime("-14days", strtotime($enddate)));
            }

        }

        if (strtotime($startdate) > strtotime($enddate)) {
            alert("结束日期不能早于开始日期！");
            $params['enddate'] = '';
            $params['startdate'] = '';
            goURL("dbmarketprice.php?" . http_build_query($params, '', '&'));
            exit;
        }


        if (strtotime($startdate) < strtotime($enddate . " -1 year")) {
            alert("时间段不能超过一年");


            $params['enddate'] = '';
            $params['startdate'] = '';

            goURL("dbmaketprice.php?" . http_build_query($params, '', '&'));
            exit;
        }

        if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($params['dc'])) {
            //print"<pre>";print_r($params);exit;
            $arr['startdate'] = $startdate;
            $arr['enddate'] = $enddate;
            $this->export($arr);
            exit;
        }

        $shuju = $this->getpricebypricestr($pricelist[$market], $startdate, $enddate);

        //print_r($shuju);
        $infolist = $shuju['data'];
        $datearr = $shuju['date'];

        foreach ($datearr as $v) {
            $datearray[$v] = date('n月j日', strtotime($v));
        }

        $arrtem = $params;
        $actionstr = "";
        foreach ($arrtem as $k => $v) {
            $actionstr .= "&" . $k . "=" . $v;
        }
        $this->t1Dao->WriteLog($mid, $uid, $SignCS, $action, $actionstr, $ip, "", "", "查看数据定制-东北5个市场各品种网价和实际成交价格", '', '', $mc_type);

        //print_r($datearr);
        $this->P($infolist);
        $this->assign("startdate", $startdate);
        $this->assign("enddate", $enddate);
        $this->assign("type", $type);
        $this->assign("sumcount", $sumcount);
        $this->assign("sumcn", $sumcn);
        $this->assign("datearr", $datearray);
        $this->assign("infolist", $infolist);
        $this->assign("mode", $mode);
        $this->assign("GUID", $GUID);
        $this->assign("amount", count($infolist));
        $this->assign("name", $name);
    }

    /**
     * 函数名称: getIP
     * 函数功能: 取得手机IP
     * 输入参数: none
     * 函数返回值: 成功返回string
     * 其它说明: 说明
     */
    private function getIP()
    {
        $ip = getenv('REMOTE_ADDR');
        $ip_ = getenv('HTTP_X_FORWARDED_FOR');
        if (($ip_ != "") && ($ip_ != "unknown")) {
            $ip = $ip_;
        }
        $ip = explode(",", $ip);
        return $ip[0];
    }


    function getpricebypricestr($pricestr, $stime, $etime)
    {
        //echo $time;
        $idnumber = explode(',', $pricestr);
        //echo count($idnumber);
        $six=$seven=array();
        foreach ($idnumber as $id) {
            if (strlen($id) == 6) {//判断id 的字符长度
                if (!in_array($id, $six)) {
                    $six[] = $id;
                }
            }
            if (strlen($id) == 7) {
                if (!in_array($id, $seven)) {
                    $seven[] = $id;
                }
            }
        }
        $sixid_str = implode("','", $six);
        $sevenid_str = implode("','", $seven);
        $mconmanagedate .= "(mconmanagedate>='" . $stime . " 00:00:00' and mconmanagedate<='" . $etime . " 23:59:59')";
        if ($sixid_str != '') {
            $PriceListSQL = " select marketconditions.pricemk price,marketconditions.topicture, marketconditions.mconmanagedate from marketconditions
 		  	where    $mconmanagedate and  marketconditions.topicture in ('$sixid_str')";
        }
        if ($sevenid_str != '') {
            $PriceListSQL = " select marketconditions.price,marketconditions.mastertopid as topicture, marketconditions.mconmanagedate from marketconditions
 		  	where $mconmanagedate and  marketconditions.mastertopid in ('$sevenid_str')";
        }
        if ($sixid_str != '' && $sevenid_str != '') {
            $PriceListSQL = " select marketconditions.pricemk price,marketconditions.topicture, marketconditions.mconmanagedate from marketconditions  
	 		  where    $mconmanagedate and  marketconditions.topicture in ('$sixid_str') 
	 		  UNION( select marketconditions.price,marketconditions.mastertopid as topicture,marketconditions.mconmanagedate from marketconditions 
	 		  where $mconmanagedate  and marketconditions.mastertopid in('$sevenid_str'))";
        }
        //echo $PriceListSQL;
        $PriceList = $this->_dao->query($PriceListSQL);
        $dataarr = array();//数据数组
        $datearr = array();//日期数组
        foreach ($PriceList as $v) {
            $date = date('Y-m-d', strtotime($v['mconmanagedate']));
            if (!in_array($date, $datearr)) {
                $datearr[] = $date;
            }

            if (strstr($v['price'], "-")) {
                $avgprice = explode("-", $v['price']);
                $v['price'] = round(($avgprice['0'] + $avgprice['1']) / 2, 2);
            }
            $dataarr[$date][$v['topicture']] = $v['price'];

        }

        $data['date'] = $datearr;
        $data['data'] = $dataarr;

        return $data;
    }

    public function export($params)
    {
        //echo "6666";
        $startdate = $params["startdate"];
        $enddate = $params["enddate"];
        $marketarr = $GLOBALS['marketarr'];
        $pricelist = $GLOBALS['pricelist'];

        $objPHPExcel = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        //$objPHPExcel = new PHPExcel();
        $styleArray1 = array(
            'font' => array('size' => 11),
            'alignment' => array('horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER),
        );
        $styleArray2 = array(
            'font' => array('size' => 11),
            'alignment' => array('vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER, 'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER),
        );
        $styleArray = array(
            'borders' => array(
                'allBorders' => array(
                    // 'style' => PHPExcel_Style_Border::BORDER_THICK,//边框是粗的
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,//细边框
                    //'color' => array('argb' => 'FFFF0000'),
                ),
            ),
        );
        $styleArray3 = array(
            'font' => array('size' => 11, 'bold' => true),
        );
        $shuju = $this->getpricebypricestr($pricelist[1], $startdate, $enddate);
        $infolist = $shuju['data'];
        $datearr = $shuju['date'];

        $objPHPExcel->createSheet();
        $objPHPExcel->setActiveSheetIndex(0);
        $objActSheet_SY = $objPHPExcel->getActiveSheet();
        $objActSheet_SY->setTitle( '沈阳');
        $objActSheet_SY->mergeCells('B1:E1');
        $objActSheet_SY->mergeCells('F1:H1');
        $objActSheet_SY->mergeCells('I1:K1');
        $objActSheet_SY->mergeCells('L1:N1');
        $objActSheet_SY->mergeCells('O1:R1');
        $objActSheet_SY->mergeCells('S1:V1');
        $objActSheet_SY->mergeCells('W1:AB1');

        $objActSheet_SY->mergeCells('B2:E2');
        $objActSheet_SY->mergeCells('F2:H2');
        $objActSheet_SY->mergeCells('I2:K2');
        $objActSheet_SY->mergeCells('L2:N2');
        $objActSheet_SY->mergeCells('O2:R2');
        $objActSheet_SY->mergeCells('S2:V2');
        $objActSheet_SY->mergeCells('W2:AB2');


        $objActSheet_SY->mergeCells('B3:D3');
        $objActSheet_SY->mergeCells('F3:G3');
        $objActSheet_SY->mergeCells('I3:J3');
        $objActSheet_SY->mergeCells('L3:M3');
        $objActSheet_SY->mergeCells('O3:Q3');
        $objActSheet_SY->mergeCells('S3:U3');
        $objActSheet_SY->mergeCells('W3:AA3');

        $objActSheet_SY->mergeCells('E3:E4');
        $objActSheet_SY->mergeCells('H3:H4');
        $objActSheet_SY->mergeCells('K3:K4');
        $objActSheet_SY->mergeCells('N3:N4');
        $objActSheet_SY->mergeCells('R3:R4');
        $objActSheet_SY->mergeCells('V3:V4');
        $objActSheet_SY->mergeCells('AB3:AB4');

        $objActSheet_SY->setCellValue('A1',  '沈阳');
        $objActSheet_SY->setCellValue('B1',  '热轧');
        $objActSheet_SY->setCellValue('F1',  '冷轧');
        $objActSheet_SY->setCellValue('I1',  '镀锌');
        $objActSheet_SY->setCellValue('L1',  '中厚板');
        $objActSheet_SY->setCellValue('O1',  '线材');
        $objActSheet_SY->setCellValue('S1',  '盘螺');
        $objActSheet_SY->setCellValue('W1',  '螺纹钢');

        $objActSheet_SY->setCellValue('A2',  '规格');
        $objActSheet_SY->setCellValue('B2',  '5.75*1500C');
        $objActSheet_SY->setCellValue('F2',  '1.0*1250C');
        $objActSheet_SY->setCellValue('I2',  '1.0*1250C 80g');
        // $objActSheet_SY -> setCellValue('L2',iconv('gb2312','utf-8','16*2200*10000上');
        $objActSheet_SY->setCellValue('L2',  '16*2200*10000');
        $objActSheet_SY->setCellValue('O2',  'HPB300 φ8-10');
        $objActSheet_SY->setCellValue('S2',  'HRB400E φ8-10');
        $objActSheet_SY->setCellValue('W2',  'HRB400E φ18-25');

        $objActSheet_SY->setCellValue('A3',  '');
        $objActSheet_SY->setCellValue('B3',  '网价');
        $objActSheet_SY->setCellValue('E3',  '主流成交价格');
        $objActSheet_SY->setCellValue('F3',  '网价');
        $objActSheet_SY->setCellValue('H3',  '主流成交价格');
        $objActSheet_SY->setCellValue('I3',  '网价');
        $objActSheet_SY->setCellValue('K3',  '主流成交价格');
        $objActSheet_SY->setCellValue('L3',  '网价');
        $objActSheet_SY->setCellValue('N3',  '主流成交价格');
        $objActSheet_SY->setCellValue('O3',  '网价');
        $objActSheet_SY->setCellValue('R3',  '主流成交价格');
        $objActSheet_SY->setCellValue('S3',  '网价');
        $objActSheet_SY->setCellValue('V3',  '主流成交价格');
        $objActSheet_SY->setCellValue('W3',  '网价');
        $objActSheet_SY->setCellValue('AB3',  '主流成交价格');

        $objActSheet_SY->setCellValue('A4',  '');
        $objActSheet_SY->setCellValue('B4',  '鞍钢');
        $objActSheet_SY->setCellValue('C4',  '本钢');
        $objActSheet_SY->setCellValue('D4',  '通钢');
        // $objActSheet_SY -> setCellValue('E4',iconv('gb2312','utf-8','价格');
        $objActSheet_SY->setCellValue('F4',  '鞍钢');
        $objActSheet_SY->setCellValue('G4',  '本钢');
        // $objActSheet_SY -> setCellValue('H4',iconv('gb2312','utf-8','价格');
        $objActSheet_SY->setCellValue('I4',  '鞍钢');
        $objActSheet_SY->setCellValue('J4',  '本钢');
        // $objActSheet_SY -> setCellValue('K4',iconv('gb2312','utf-8','价格');
        $objActSheet_SY->setCellValue('L4',  '鞍钢');
        $objActSheet_SY->setCellValue('M4',  '营口');
        // $objActSheet_SY -> setCellValue('N4',iconv('gb2312','utf-8','价格');
        $objActSheet_SY->setCellValue('O4',  '后英');
        $objActSheet_SY->setCellValue('P4',  '通钢');
        $objActSheet_SY->setCellValue('Q4',  '鞍钢');
        // $objActSheet_SY -> setCellValue('R4',iconv('gb2312','utf-8','价格');
        $objActSheet_SY->setCellValue('S4',  '鑫达');
        $objActSheet_SY->setCellValue('T4',  '鞍钢');
        $objActSheet_SY->setCellValue('U4',  '通钢');
        // $objActSheet_SY -> setCellValue('V4',iconv('gb2312','utf-8','价格');
        $objActSheet_SY->setCellValue('W4',  '新抚');
        $objActSheet_SY->setCellValue('X4',  '凌钢');
        $objActSheet_SY->setCellValue('Y4',  '鞍钢');
        $objActSheet_SY->setCellValue('Z4',  '北台');
        $objActSheet_SY->setCellValue('AA4',  '通钢');
        // $objActSheet_SY -> setCellValue('AB4',iconv('gb2312','utf-8','价格');
        $objActSheet_SY->getStyle("A1:AB1")->applyFromArray($styleArray3);
        $objActSheet_SY->getStyle("A2:AB2")->applyFromArray($styleArray3);
        $objActSheet_SY->getColumnDimension('E')->setWidth(16); //列宽
        $objActSheet_SY->getColumnDimension('H')->setWidth(16);
        $objActSheet_SY->getColumnDimension('K')->setWidth(16);
        $objActSheet_SY->getColumnDimension('N')->setWidth(16);
        $objActSheet_SY->getColumnDimension('R')->setWidth(16);
        $objActSheet_SY->getColumnDimension('V')->setWidth(16);
        $objActSheet_SY->getColumnDimension('AB')->setWidth(16);
        $n = 5;
        $count = 0;
        //echo '<pre/>';print_r();exit;
        foreach ($datearr as $key => $value) {

            $objActSheet_SY->setCellValue('A' . $n,  date('n月j日', strtotime($value)));
            $objActSheet_SY->setCellValue('B' . $n, $infolist[$value]['4531123']);
            $objActSheet_SY->setCellValue('C' . $n, $infolist[$value]['4531121']);
            $objActSheet_SY->setCellValue('D' . $n, $infolist[$value]['4531122']);
            $objActSheet_SY->setCellValue('E' . $n, $infolist[$value]['453112']);
            $objActSheet_SY->setCellValue('F' . $n, $infolist[$value]['4542122']);
            $objActSheet_SY->setCellValue('G' . $n, $infolist[$value]['4542123']);
            $objActSheet_SY->setCellValue('H' . $n, $infolist[$value]['454212']);
            $objActSheet_SY->setCellValue('I' . $n, $infolist[$value]['4544123']);
            $objActSheet_SY->setCellValue('J' . $n, $infolist[$value]['4544121']);
            $objActSheet_SY->setCellValue('K' . $n, $infolist[$value]['454412']);
            $objActSheet_SY->setCellValue('L' . $n, $infolist[$value]['4530126']);
            $objActSheet_SY->setCellValue('M' . $n, $infolist[$value]['4530121']);
            $objActSheet_SY->setCellValue('N' . $n, $infolist[$value]['453012']);
            $objActSheet_SY->setCellValue('O' . $n, $infolist[$value]['4511038']);
            $objActSheet_SY->setCellValue('P' . $n, $infolist[$value]['4511031']);
            $objActSheet_SY->setCellValue('Q' . $n, $infolist[$value]['4511039']);
            $objActSheet_SY->setCellValue('R' . $n, $infolist[$value]['451103']);
            $objActSheet_SY->setCellValue('S' . $n, $infolist[$value]['451333A']);
            $objActSheet_SY->setCellValue('T' . $n, $infolist[$value]['4513326']);
            $objActSheet_SY->setCellValue('U' . $n, $infolist[$value]['4513123']);
            $objActSheet_SY->setCellValue('V' . $n, $infolist[$value]['451312']);
            $objActSheet_SY->setCellValue('W' . $n, $infolist[$value]['4520231']);
            $objActSheet_SY->setCellValue('X' . $n, $infolist[$value]['4520235']);
            $objActSheet_SY->setCellValue('Y' . $n, $infolist[$value]['4520437']);
            $objActSheet_SY->setCellValue('Z' . $n, $infolist[$value]['4520233']);
            $objActSheet_SY->setCellValue('AA' . $n, $infolist[$value]['4520232']);
            $objActSheet_SY->setCellValue('AB' . $n, $infolist[$value]['452023']);
            $n++;
        }
        $n_num = $n - 1;
        $objActSheet_SY->getStyle("A1:AB" . $n_num)->applyFromArray($styleArray);
        $objActSheet_SY->getStyle("A1:AB" . $n_num)->applyFromArray($styleArray2);


        $shuju = $this->getpricebypricestr($pricelist[2], $startdate, $enddate);
        $infolist = $shuju['data'];
        $datearr = $shuju['date'];
        $objPHPExcel->createSheet();
        $objPHPExcel->setActiveSheetIndex(1);
        $objActSheet_AS = $objPHPExcel->getActiveSheet();
        $objActSheet_AS->setTitle( '鞍山');
        $objActSheet_AS->mergeCells('B1:E1');
        $objActSheet_AS->mergeCells('F1:G1');
        $objActSheet_AS->mergeCells('H1:J1');

        $objActSheet_AS->mergeCells('B2:E2');
        $objActSheet_AS->mergeCells('F2:G2');
        $objActSheet_AS->mergeCells('H2:J2');

        $objActSheet_AS->mergeCells('B3:D3');
        $objActSheet_AS->mergeCells('H3:I3');
        $objActSheet_AS->mergeCells('E3:E4');
        $objActSheet_AS->mergeCells('G3:G4');
        $objActSheet_AS->mergeCells('J3:J4');

        $objActSheet_AS->setCellValue('A1',  '鞍山');
        $objActSheet_AS->setCellValue('B1',  '热轧');
        $objActSheet_AS->setCellValue('F1',  '冷轧');
        $objActSheet_AS->setCellValue('H1',  '中厚板');

        $objActSheet_AS->setCellValue('A2',  '规格');
        $objActSheet_AS->setCellValue('B2',  '5.75*1500C');
        $objActSheet_AS->setCellValue('F2',  '1.0*1250C');
        // $objActSheet_AS -> setCellValue('H2',iconv('gb2312','utf-8','16*2200*10000上');
        $objActSheet_AS->setCellValue('H2',  '16*2200*10000');

        $objActSheet_AS->setCellValue('A3',  '');
        $objActSheet_AS->setCellValue('B3',  '网价');
        $objActSheet_AS->setCellValue('E3',  '主流成交价格');
        $objActSheet_AS->setCellValue('F3',  '网价');
        $objActSheet_AS->setCellValue('G3',  '主流成交价格');
        $objActSheet_AS->setCellValue('H3',  '网价');
        $objActSheet_AS->setCellValue('J3',  '主流成交价格');

        $objActSheet_AS->setCellValue('A4',  '');
        $objActSheet_AS->setCellValue('B4',  '鞍钢');
        $objActSheet_AS->setCellValue('C4',  '本钢');
        $objActSheet_AS->setCellValue('D4',  '通钢');
        // $objActSheet_AS -> setCellValue('E4',iconv('gb2312','utf-8','价格');
        $objActSheet_AS->setCellValue('F4',  '鞍钢');
        // $objActSheet_AS -> setCellValue('G4',iconv('gb2312','utf-8','价格');
        $objActSheet_AS->setCellValue('H4',  '鞍钢');
        $objActSheet_AS->setCellValue('I4',  '营口');
        // $objActSheet_AS -> setCellValue('J4',iconv('gb2312','utf-8','价格');

        $objActSheet_AS->getStyle("A1:J1")->applyFromArray($styleArray3);
        $objActSheet_AS->getStyle("A2:J2")->applyFromArray($styleArray3);
        $objActSheet_AS->getColumnDimension('E')->setWidth(16); //列宽
        $objActSheet_AS->getColumnDimension('G')->setWidth(16);
        $objActSheet_AS->getColumnDimension('J')->setWidth(16);
        $n = 5;
        $count = 0;
        //echo '<pre/>';print_r();exit;
        foreach ($datearr as $key => $value) {
            //4631123	4631122	4631121	463112	4642122	464212	4630122	4630124	463012

            $objActSheet_AS->setCellValue('A' . $n,  date('n月j日', strtotime($value)));
            $objActSheet_AS->setCellValue('B' . $n, $infolist[$value]['4631123']);
            $objActSheet_AS->setCellValue('C' . $n, $infolist[$value]['4631122']);
            $objActSheet_AS->setCellValue('D' . $n, $infolist[$value]['4631121']);
            $objActSheet_AS->setCellValue('E' . $n, $infolist[$value]['463112']);
            $objActSheet_AS->setCellValue('F' . $n, $infolist[$value]['4642122']);
            $objActSheet_AS->setCellValue('G' . $n, $infolist[$value]['464212']);
            $objActSheet_AS->setCellValue('H' . $n, $infolist[$value]['4630122']);
            $objActSheet_AS->setCellValue('I' . $n, $infolist[$value]['4630124']);
            $objActSheet_AS->setCellValue('J' . $n, $infolist[$value]['463012']);
            $n++;
        }
        $n_num = $n - 1;
        $objActSheet_AS->getStyle("A1:J" . $n_num)->applyFromArray($styleArray);
        $objActSheet_AS->getStyle("A1:J" . $n_num)->applyFromArray($styleArray2);


        //    大连	热轧				冷轧			中厚板			线材			盘螺				螺纹钢
        //    规格	5.75*1500C				1.0*1250C			16*2200*10000上			HPB300 φ8-10			HRB400E φ8-10				HRB400E φ18-25
        // 	   网价			主流成交价格	网价		主流成交价格	网价		主流成交价格	网价		主流成交价格	网价			主流成交价格	网价					主流成交价格
        // 	   鞍钢	本钢	通钢	价格	鞍钢	本钢	价格	鞍钢	营口	价格	通钢	新抚钢	价格	新抚钢	北台	通钢	价格	新抚	凌钢	鞍钢	北台	通钢	价格
        // 	   4731122	4731121	4731124	473112	4742122	4742121	474212	4730122	4730125	473018	4711031	4711032	471103	4713122	4713123	4713121	471312	4720231	4720236	4720238	4720237	4720233	472023

//*************************大连*********************************************************************************
        $shuju = $this->getpricebypricestr($pricelist[3], $startdate, $enddate);
        $infolist = $shuju['data'];
        $datearr = $shuju['date'];
        $objPHPExcel->createSheet();
        $objPHPExcel->setActiveSheetIndex(2);
        $objActSheet_DL = $objPHPExcel->getActiveSheet();
        $objActSheet_DL->setTitle( '大连');

        $objActSheet_DL->mergeCells('B1:E1');
        $objActSheet_DL->mergeCells('F1:H1');
        $objActSheet_DL->mergeCells('I1:K1');
        $objActSheet_DL->mergeCells('L1:N1');
        $objActSheet_DL->mergeCells('O1:R1');
        $objActSheet_DL->mergeCells('S1:X1');


        $objActSheet_DL->mergeCells('B2:E2');
        $objActSheet_DL->mergeCells('F2:H2');
        $objActSheet_DL->mergeCells('I2:K2');
        $objActSheet_DL->mergeCells('L2:N2');
        $objActSheet_DL->mergeCells('O2:R2');
        $objActSheet_DL->mergeCells('S2:X2');


        $objActSheet_DL->mergeCells('B3:D3');
        $objActSheet_DL->mergeCells('F3:G3');
        $objActSheet_DL->mergeCells('I3:J3');
        $objActSheet_DL->mergeCells('L3:M3');
        $objActSheet_DL->mergeCells('O3:Q3');
        $objActSheet_DL->mergeCells('S3:W3');

        $objActSheet_DL->mergeCells('E3:E4');
        $objActSheet_DL->mergeCells('H3:H4');
        $objActSheet_DL->mergeCells('K3:K4');
        $objActSheet_DL->mergeCells('N3:N4');
        $objActSheet_DL->mergeCells('R3:R4');
        $objActSheet_DL->mergeCells('X3:X4');

        $objActSheet_DL->setCellValue('A1',  '大连');
        $objActSheet_DL->setCellValue('B1',  '热轧');
        $objActSheet_DL->setCellValue('F1',  '冷轧');
        $objActSheet_DL->setCellValue('I1',  '中厚板');
        $objActSheet_DL->setCellValue('L1',  '线材');
        $objActSheet_DL->setCellValue('O1',  '盘螺');
        $objActSheet_DL->setCellValue('S1',  '螺纹钢');

        $objActSheet_DL->setCellValue('A2',  '规格');
        $objActSheet_DL->setCellValue('B2',  '5.75*1500C');
        $objActSheet_DL->setCellValue('F2',  '1.0*1250C');
        // $objActSheet_DL -> setCellValue('I2',iconv('gb2312','utf-8','16*2200*10000上');
        $objActSheet_DL->setCellValue('I2',  '16*2200*10000');
        $objActSheet_DL->setCellValue('L2',  'HPB300 φ8-10');
        $objActSheet_DL->setCellValue('O2',  'HRB400E φ8-10');
        $objActSheet_DL->setCellValue('S2',  'HRB400E φ18-25');

        $objActSheet_DL->setCellValue('A3',  '');
        $objActSheet_DL->setCellValue('B3',  '网价');
        $objActSheet_DL->setCellValue('E3',  '主流成交价格');
        $objActSheet_DL->setCellValue('F3',  '网价');
        $objActSheet_DL->setCellValue('H3',  '主流成交价格');
        $objActSheet_DL->setCellValue('I3',  '网价');
        $objActSheet_DL->setCellValue('K3',  '主流成交价格');
        $objActSheet_DL->setCellValue('L3',  '网价');
        $objActSheet_DL->setCellValue('N3',  '主流成交价格');
        $objActSheet_DL->setCellValue('O3',  '网价');
        $objActSheet_DL->setCellValue('R3',  '主流成交价格');
        $objActSheet_DL->setCellValue('S3',  '网价');
        $objActSheet_DL->setCellValue('X3',  '主流成交价格');

        $objActSheet_DL->setCellValue('B4',  '鞍钢');
        $objActSheet_DL->setCellValue('C4',  '本钢');
        $objActSheet_DL->setCellValue('D4',  '通钢');
        // $objActSheet_DL -> setCellValue('E4',iconv('gb2312','utf-8','价格');
        $objActSheet_DL->setCellValue('F4',  '鞍钢');
        $objActSheet_DL->setCellValue('G4',  '本钢');
        // $objActSheet_DL -> setCellValue('H4',iconv('gb2312','utf-8','价格');
        $objActSheet_DL->setCellValue('I4',  '鞍钢');
        $objActSheet_DL->setCellValue('J4',  '营口');
        // $objActSheet_DL -> setCellValue('K4',iconv('gb2312','utf-8','价格');
        $objActSheet_DL->setCellValue('L4',  '通钢');
        $objActSheet_DL->setCellValue('M4',  '新抚钢');
        // $objActSheet_DL -> setCellValue('N4',iconv('gb2312','utf-8','价格');
        $objActSheet_DL->setCellValue('O4',  '新抚钢');
        $objActSheet_DL->setCellValue('P4',  '北台');
        $objActSheet_DL->setCellValue('Q4',  '通钢');
        // $objActSheet_DL -> setCellValue('R4',iconv('gb2312','utf-8','价格');
        $objActSheet_DL->setCellValue('S4',  '新抚');
        $objActSheet_DL->setCellValue('T4',  '凌钢');
        $objActSheet_DL->setCellValue('U4',  '鞍钢');
        $objActSheet_DL->setCellValue('V4',  '北台');
        $objActSheet_DL->setCellValue('W4',  '通钢');
        // $objActSheet_DL -> setCellValue('X4',iconv('gb2312','utf-8','价格');

        $objActSheet_DL->getStyle("A1:X1")->applyFromArray($styleArray3);
        $objActSheet_DL->getStyle("A2:X2")->applyFromArray($styleArray3);
        $objActSheet_DL->getColumnDimension('E')->setWidth(16); //列宽
        $objActSheet_DL->getColumnDimension('H')->setWidth(16);
        $objActSheet_DL->getColumnDimension('K')->setWidth(16);
        $objActSheet_DL->getColumnDimension('N')->setWidth(16);
        $objActSheet_DL->getColumnDimension('E')->setWidth(16);
        $objActSheet_DL->getColumnDimension('X')->setWidth(16);

        $n = 5;
        $count = 0;
        //echo '<pre/>';print_r();exit;
        foreach ($datearr as $key => $value) {
            //4731122	4731121	4731124	473112	4742122	4742121	474212	4730122	4730125	473018	4711031	4711032	471312	4713122	4713123	4713121	471312	4720231	4720236	4720238	4720237	4720233	472023
            $objActSheet_DL->setCellValue('A' . $n,  date('n月j日', strtotime($value)));
            $objActSheet_DL->setCellValue('B' . $n, $infolist[$value]['4731122']);
            $objActSheet_DL->setCellValue('C' . $n, $infolist[$value]['4731121']);
            $objActSheet_DL->setCellValue('D' . $n, $infolist[$value]['4731124']);
            $objActSheet_DL->setCellValue('E' . $n, $infolist[$value]['473112']);
            $objActSheet_DL->setCellValue('F' . $n, $infolist[$value]['4742122']);
            $objActSheet_DL->setCellValue('G' . $n, $infolist[$value]['4742121']);
            $objActSheet_DL->setCellValue('H' . $n, $infolist[$value]['474212']);
            $objActSheet_DL->setCellValue('I' . $n, $infolist[$value]['4730122']);
            $objActSheet_DL->setCellValue('J' . $n, $infolist[$value]['4730125']);
            $objActSheet_DL->setCellValue('K' . $n, $infolist[$value]['473018']);
            $objActSheet_DL->setCellValue('l' . $n, $infolist[$value]['4711031']);
            $objActSheet_DL->setCellValue('M' . $n, $infolist[$value]['4711032']);
            $objActSheet_DL->setCellValue('N' . $n, $infolist[$value]['471103']);
            $objActSheet_DL->setCellValue('O' . $n, $infolist[$value]['4713122']);
            $objActSheet_DL->setCellValue('P' . $n, $infolist[$value]['4713123']);
            $objActSheet_DL->setCellValue('Q' . $n, $infolist[$value]['4713121']);
            $objActSheet_DL->setCellValue('R' . $n, $infolist[$value]['471312']);
            $objActSheet_DL->setCellValue('S' . $n, $infolist[$value]['4720231']);
            $objActSheet_DL->setCellValue('T' . $n, $infolist[$value]['4720236']);
            $objActSheet_DL->setCellValue('U' . $n, $infolist[$value]['4720238']);
            $objActSheet_DL->setCellValue('V' . $n, $infolist[$value]['4720237']);
            $objActSheet_DL->setCellValue('W' . $n, $infolist[$value]['4720233']);
            $objActSheet_DL->setCellValue('X' . $n, $infolist[$value]['472023']);
            $n++;
        }
        $n_num = $n - 1;
        $objActSheet_DL->getStyle("A1:X" . $n_num)->applyFromArray($styleArray);
        $objActSheet_DL->getStyle("A1:X" . $n_num)->applyFromArray($styleArray2);


//*************************长春*********************************************************************************
        $shuju = $this->getpricebypricestr($pricelist[4], $startdate, $enddate);
        $infolist = $shuju['data'];
        $datearr = $shuju['date'];
        $objPHPExcel->createSheet();
        $objPHPExcel->setActiveSheetIndex(3);
        $objActSheet_CC = $objPHPExcel->getActiveSheet();
        $objActSheet_CC->setTitle( '长春');

        $objActSheet_CC->mergeCells('B1:D1');
        $objActSheet_CC->mergeCells('E1:H1');
        $objActSheet_CC->mergeCells('I1:K1');
        $objActSheet_CC->mergeCells('L1:O1');

        $objActSheet_CC->mergeCells('B2:D2');
        $objActSheet_CC->mergeCells('E2:H2');
        $objActSheet_CC->mergeCells('I2:K2');
        $objActSheet_CC->mergeCells('L2:O2');


        $objActSheet_CC->mergeCells('B3:C3');
        $objActSheet_CC->mergeCells('E3:G3');
        $objActSheet_CC->mergeCells('I3:J3');
        $objActSheet_CC->mergeCells('L3:N3');

        $objActSheet_CC->mergeCells('D3:D4');
        $objActSheet_CC->mergeCells('H3:H4');
        $objActSheet_CC->mergeCells('K3:K4');
        $objActSheet_CC->mergeCells('O3:O4');

        $objActSheet_CC->setCellValue('A1',  '长春');
        $objActSheet_CC->setCellValue('B1',  '中厚板');
        $objActSheet_CC->setCellValue('E1',  '线材');
        $objActSheet_CC->setCellValue('I1',  '盘螺');
        $objActSheet_CC->setCellValue('L1',  '螺纹钢');

        $objActSheet_CC->setCellValue('A2',  '规格');
        $objActSheet_CC->setCellValue('B2',  '16*2200*10000');
        // $objActSheet_CC -> setCellValue('B2',iconv('gb2312','utf-8','16*2200*10000上');
        $objActSheet_CC->setCellValue('E2',  'HPB300 φ8-10');
        $objActSheet_CC->setCellValue('I2',  'HRB400E φ8-10');
        $objActSheet_CC->setCellValue('L2',  'HRB400E φ18-25');

        $objActSheet_CC->setCellValue('A3',  '');
        $objActSheet_CC->setCellValue('B3',  '网价');
        $objActSheet_CC->setCellValue('D3',  '主流成交价格');
        $objActSheet_CC->setCellValue('E3',  '网价');
        $objActSheet_CC->setCellValue('H3',  '主流成交价格');
        $objActSheet_CC->setCellValue('I3',  '网价');
        $objActSheet_CC->setCellValue('K3',  '主流成交价格');
        $objActSheet_CC->setCellValue('L3',  '网价');
        $objActSheet_CC->setCellValue('O3',  '主流成交价格');

        $objActSheet_CC->setCellValue('B4',  '鞍钢');
        $objActSheet_CC->setCellValue('C4',  '营口');
        // $objActSheet_CC -> setCellValue('D4',iconv('gb2312','utf-8','价格');
        $objActSheet_CC->setCellValue('E4',  '北台');
        $objActSheet_CC->setCellValue('F4',  '鑫达');
        $objActSheet_CC->setCellValue('G4',  '通钢');
        // $objActSheet_CC -> setCellValue('H4',iconv('gb2312','utf-8','价格');
        $objActSheet_CC->setCellValue('I4',  '鑫达');
        $objActSheet_CC->setCellValue('J4',  '西钢');
        // $objActSheet_CC -> setCellValue('K4',iconv('gb2312','utf-8','价格');
        $objActSheet_CC->setCellValue('L4',  '西钢');
        $objActSheet_CC->setCellValue('M4',  '乌钢');
        $objActSheet_CC->setCellValue('N4',  '通钢');
        // $objActSheet_CC -> setCellValue('O4',iconv('gb2312','utf-8','价格');

        $objActSheet_CC->getStyle("A1:O1")->applyFromArray($styleArray3);
        $objActSheet_CC->getStyle("A2:O2")->applyFromArray($styleArray3);
        $objActSheet_CC->getColumnDimension('D')->setWidth(16);
        $objActSheet_CC->getColumnDimension('H')->setWidth(16);
        $objActSheet_CC->getColumnDimension('K')->setWidth(16);
        $objActSheet_CC->getColumnDimension('O')->setWidth(16);

        $n = 5;
        $count = 0;
        //echo '<pre/>';print_r();exit;
        foreach ($datearr as $key => $value) {
            //5030123	5030121	503012	5011062	5011034	5011031	501103	5013123	5013323	501312	5020231	5020439	5020431	502043
            $objActSheet_CC->setCellValue('A' . $n,  date('n月j日', strtotime($value)));
            $objActSheet_CC->setCellValue('B' . $n, $infolist[$value]['5030123']);
            $objActSheet_CC->setCellValue('C' . $n, $infolist[$value]['5030121']);
            $objActSheet_CC->setCellValue('D' . $n, $infolist[$value]['503012']);
            $objActSheet_CC->setCellValue('E' . $n, $infolist[$value]['5011062']);
            $objActSheet_CC->setCellValue('F' . $n, $infolist[$value]['5011034']);
            $objActSheet_CC->setCellValue('G' . $n, $infolist[$value]['5011031']);
            $objActSheet_CC->setCellValue('H' . $n, $infolist[$value]['501103']);
            $objActSheet_CC->setCellValue('I' . $n, $infolist[$value]['5013123']);
            $objActSheet_CC->setCellValue('J' . $n, $infolist[$value]['5013323']);
            $objActSheet_CC->setCellValue('K' . $n, $infolist[$value]['501312']);
            $objActSheet_CC->setCellValue('L' . $n, $infolist[$value]['5020231']);
            $objActSheet_CC->setCellValue('M' . $n, $infolist[$value]['5020439']);
            $objActSheet_CC->setCellValue('N' . $n, $infolist[$value]['5020431']);
            $objActSheet_CC->setCellValue('O' . $n, $infolist[$value]['502043']);
            $n++;
        }
        $n_num = $n - 1;
        $objActSheet_CC->getStyle("A1:O" . $n_num)->applyFromArray($styleArray);
        $objActSheet_CC->getStyle("A1:O" . $n_num)->applyFromArray($styleArray2);


//*************************哈尔滨*********************************************************************************
        $shuju = $this->getpricebypricestr($pricelist[5], $startdate, $enddate);
        $infolist = $shuju['data'];
        $datearr = $shuju['date'];
        $objPHPExcel->createSheet();
        $objPHPExcel->setActiveSheetIndex(4);
        $objActSheet_HRB = $objPHPExcel->getActiveSheet();
        $objActSheet_HRB->setTitle( '哈尔滨');

        $objActSheet_HRB->mergeCells('B1:C1');
        $objActSheet_HRB->mergeCells('D1:G1');
        $objActSheet_HRB->mergeCells('H1:J1');
        $objActSheet_HRB->mergeCells('K1:N1');

        $objActSheet_HRB->mergeCells('B2:C2');
        $objActSheet_HRB->mergeCells('D2:G2');
        $objActSheet_HRB->mergeCells('H2:J2');
        $objActSheet_HRB->mergeCells('K2:N2');

        $objActSheet_HRB->mergeCells('D3:F3');
        $objActSheet_HRB->mergeCells('H3:I3');
        $objActSheet_HRB->mergeCells('K3:M3');

        $objActSheet_HRB->mergeCells('C3:C4');
        $objActSheet_HRB->mergeCells('G3:G4');
        $objActSheet_HRB->mergeCells('J3:J4');
        $objActSheet_HRB->mergeCells('N3:N4');


        $objActSheet_HRB->setCellValue('A1',  '哈尔滨');
        $objActSheet_HRB->setCellValue('B1',  '中厚板');
        $objActSheet_HRB->setCellValue('D1',  '线材');
        $objActSheet_HRB->setCellValue('H1',  '盘螺');
        $objActSheet_HRB->setCellValue('K1',  '螺纹钢');

        $objActSheet_HRB->setCellValue('A2',  '规格');
        // $objActSheet_HRB -> setCellValue('B2',iconv('gb2312','utf-8','16*2200*10000上');
        $objActSheet_HRB->setCellValue('B2',  '16*2200*10000');
        $objActSheet_HRB->setCellValue('D2',  'HPB300 φ8-10');
        $objActSheet_HRB->setCellValue('H2',  'HRB400E φ8-10');
        $objActSheet_HRB->setCellValue('K2',  'HRB400E φ18-25');

        $objActSheet_HRB->setCellValue('A3',  '');
        $objActSheet_HRB->setCellValue('B3',  '网价');
        $objActSheet_HRB->setCellValue('C3',  '主流成交价格');
        $objActSheet_HRB->setCellValue('D3',  '网价');
        $objActSheet_HRB->setCellValue('G3',  '主流成交价格');
        $objActSheet_HRB->setCellValue('H3',  '网价');
        $objActSheet_HRB->setCellValue('J3',  '主流成交价格');
        $objActSheet_HRB->setCellValue('K3',  '网价');
        $objActSheet_HRB->setCellValue('N3',  '主流成交价格');

        $objActSheet_HRB->setCellValue('B4',  '鞍钢');
        // $objActSheet_HRB -> setCellValue('C4',iconv('gb2312','utf-8','价格');
        $objActSheet_HRB->setCellValue('D4',  '后英');
        $objActSheet_HRB->setCellValue('E4',  '西钢');
        $objActSheet_HRB->setCellValue('F4',  '北台');
        // $objActSheet_HRB -> setCellValue('G4',iconv('gb2312','utf-8','价格');
        $objActSheet_HRB->setCellValue('H4',  '鑫达');
        $objActSheet_HRB->setCellValue('I4',  '西钢');
        // $objActSheet_HRB -> setCellValue('J4',iconv('gb2312','utf-8','价格');
        $objActSheet_HRB->setCellValue('K4',  '西钢');
        $objActSheet_HRB->setCellValue('L4',  '四平');
        $objActSheet_HRB->setCellValue('M4',  '建龙');
        // $objActSheet_HRB -> setCellValue('N4',iconv('gb2312','utf-8','价格');

        $objActSheet_HRB->getStyle("A1:N1")->applyFromArray($styleArray3);
        $objActSheet_HRB->getStyle("A2:N2")->applyFromArray($styleArray3);
        $objActSheet_HRB->getColumnDimension('C')->setWidth(16);
        $objActSheet_HRB->getColumnDimension('G')->setWidth(16);
        $objActSheet_HRB->getColumnDimension('J')->setWidth(16);
        $objActSheet_HRB->getColumnDimension('N')->setWidth(16);

        $n = 5;
        $count = 0;
        //echo '<pre/>';print_r();exit;
        foreach ($datearr as $key => $value) {
            //5130121	513012	5111036	5111031	5111032	511103	5113131	5113121	511312	5120231	5120235	5120234	512023
            $objActSheet_HRB->setCellValue('A' . $n, date('n月j日', strtotime($value)));
            $objActSheet_HRB->setCellValue('B' . $n, $infolist[$value]['5130121']);
            $objActSheet_HRB->setCellValue('C' . $n, $infolist[$value]['513012']);
            $objActSheet_HRB->setCellValue('D' . $n, $infolist[$value]['5111036']);
            $objActSheet_HRB->setCellValue('E' . $n, $infolist[$value]['5111031']);
            $objActSheet_HRB->setCellValue('F' . $n, $infolist[$value]['5111032']);
            $objActSheet_HRB->setCellValue('G' . $n, $infolist[$value]['511103']);
            $objActSheet_HRB->setCellValue('H' . $n, $infolist[$value]['5113131']);
            $objActSheet_HRB->setCellValue('I' . $n, $infolist[$value]['5113121']);
            $objActSheet_HRB->setCellValue('J' . $n, $infolist[$value]['511312']);
            $objActSheet_HRB->setCellValue('K' . $n, $infolist[$value]['5120231']);
            $objActSheet_HRB->setCellValue('L' . $n, $infolist[$value]['5120235']);
            $objActSheet_HRB->setCellValue('M' . $n, $infolist[$value]['5120234']);
            $objActSheet_HRB->setCellValue('N' . $n, $infolist[$value]['512023']);
            $n++;
        }
        $n_num = $n - 1;
        $objActSheet_HRB->getStyle("A1:N" . $n_num)->applyFromArray($styleArray);
        $objActSheet_HRB->getStyle("A1:N" . $n_num)->applyFromArray($styleArray2);


        $objPHPExcel->setActiveSheetIndex(0);
        $name = "东北5个市场各品种网价和实际成交价格" . $params['nian'] . '-' . $params['yue'];
        $filename = $name . '.xls';
        header("Pragma: public");
        header("Expires: 0");
        header("Cache-Control:must-revalidate, post-check=0, pre-check=0");
        header("Content-Type:application/force-download");
        header("Content-Type:application/vnd.ms-execl");
        header("Content-Type:application/octet-stream");
        header("Content-Type:application/download");
        header('Content-Disposition:attachment;filename=' . $filename);
        header("Content-Transfer-Encoding:binary");
        $objWriter = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($objPHPExcel, 'Xls');
        $objWriter->save('php://output');


    }


    public function cgzxrb($params)
    {
// 	铁矿石主力合约
// 	螺纹钢主力合约
// 	焦炭主力合约
// 	焦煤主力合约
// 	伦镍主力合约不知道是啥
// 	沪镍主力合约
// 	硅锰主力合约
// 	硅铁主力合约
        $mode = $params['mode'];
        $date = $params['date'];
        if ($date == '') {
            $date = date('Y-m-d', time());
        }

        $lastday = $this->get_xhdate($date);
        $szdate = date("Y-m-d", (strtotime($date) - 7 * 86400));
        $szdatelastday = date("Y-m-d", (strtotime($lastday) - 7 * 86400));

        //$monthstart=date("Y-m-01",strtotime($date));
        $monthstart = $this->jsrq($date);//上月日期 例如本月25，要取上月26

        //$yearstart=date("Y-01-01",strtotime($date));
        $yearstart = $this->jsyear($date);

        $GUID = $params['GUID'];
        $action = $params['action'];        //接口名称
        $SignCS = $params['SignCS'];
        if (!empty($params['mc_type'])) $mc_type = $params['mc_type'];
        else $mc_type = 0;
        $ip = $this->getIP();
        //判断是不是测试账号
        if (array_key_exists($GUID, $GLOBALS["testaccountarr"]) && $SignCS != '' && (in_array($ip, $GLOBALS["testip"]) || $GLOBALS["nowtest"] == 1)) {
            $user = $GLOBALS["testaccountuser"];
        } else {
            $user = $this->t1Dao->getUser($GUID, $SignCS, $mc_type);
        }
        $mid = $user['Mid'];
        $uid = $user['Uid'];


        $datatype = array("SHQHDAY_20", "SHQHDAY_4", "SHQHDAY_9", "SHQHDAY_19", "SHQHDAY_24", "SHQHDAY_23", "SHQHDAY_22");


        //print_r($this->drcdao);
        foreach ($datatype as $key => $value) {

            $bq = $this->drcdao->getqihuo($value, $date);//
            $sq = $this->drcdao->getqihuo($value, $szdate);//上周
            $monthjj = $this->drcdao->avgqihuo($value, $monthstart, $date);
            $yearjj = $this->drcdao->avgqihuo($value, $yearstart, $date);
            $bq = floatval( $bq );
            $sq = floatval( $sq );
            $result[$value]['bq'] = Round(floatval( $bq ), 1);
            if ($sq){
                $result[$value]['zd'] = Round(((Round($bq, 1) - Round($sq, 1)) / Round($sq, 1) * 100), 2);
            }else{
                $result[$value]['zd'] = "-";
            }
            $result[$value]['sq'] = $this->zhangdie($result[$value]['zd']);
            if (empty($bq)) {
                $result[$value]['bq'] = '-';
                $result[$value]['zd'] = '-';
                $result[$value]['sq'] = '-';
            }


            $result[$value]['monthjj'] = Round($monthjj, 1);
            $result[$value]['yearjj'] = Round($yearjj, 1);
        }

        $bq = $this->drcdao->getgfutures_shpi('LME期镍', $lastday);//
        $sq = $this->drcdao->getgfutures_shpi('LME期镍', $szdatelastday);//上周
        //echo $monthstart."|".$lastday;
        if (strtotime($monthstart) > strtotime($lastday)) {
            //$monthstart=$lastday;
            $monthjj = $this->drcdao->avggfutures_shpi('LME期镍', $lastday, $lastday);
        } else {
            $monthjj = $this->drcdao->avggfutures_shpi('LME期镍', $monthstart, $lastday);
        }


        $yearjj = $this->drcdao->avggfutures_shpi('LME期镍', $yearstart, $lastday);
        $result['LME']['bq'] = Round($bq, 1);
        if ($sq){
            $result['LME']['zd'] = Round(((Round($bq, 1) - Round($sq, 1)) / Round($sq, 1) * 100), 2);
        }else{
            $result['LME']['zd'] = "-";
        }
        $result['LME']['sq'] = $this->zhangdie($result['LME']['zd']);
        if (empty($bq)) {
            $result['LME']['bq'] = '-';
            $result['LME']['zd'] = '-';
            $result['LME']['sq'] = '-';
        }
        $result['LME']['monthjj'] = Round($monthjj, 1);
        $result['LME']['yearjj'] = Round($yearjj, 1);


        $this->assign("result", $result);
        $priceidstr = "1886103,F263302,076260,459310,5394101,4196101,4189101,5797036";//C163113 改为B963102

        $price_arrbq = $this->_dao->getpricebypricestr($priceidstr, $date);
        $price_arrsq = $this->_dao->getpricebypricestr($priceidstr, $szdate);

        $pricearr = explode(',', $priceidstr);
        foreach ($pricearr as $key => $value) {

            $monthjj = $this->_dao->average_price($value, $monthstart, $date);
            $yearjj = $this->_dao->average_price($value, $yearstart, $date);

            if (strtotime($date) < strtotime('2020-07-21') && $value == '5797036') {
                $price_arrbq[$value] = 12000;
                $price_arrsq[$value] = 11500;
            }

            if ($value == '4196101' || $value == '4189101') {
                $price_arrbq[$value] = $price_arrbq[$value] + 350;
                $price_arrsq[$value] = $price_arrsq[$value] + 350;
                $monthjj = $monthjj + 350;
                $yearjj = $yearjj + 350;

                if ($price_arrbq[$value] == 350) {
                    $price_arrbq[$value] = 0;
                }
                if ($price_arrsq[$value] == 350) {
                    $price_arrsq[$value] = 0;
                }
            }

            $pricelist[$value]['bq'] = $price_arrbq[$value];
            if ( $price_arrsq[$value] ){
                $pricelist[$value]['zd'] = Round(((Round($price_arrbq[$value], 1) - Round($price_arrsq[$value], 1)) / Round($price_arrsq[$value], 1) * 100), 2);
            }else{
                $pricelist[$value]['zd'] = "-";
            }
            $pricelist[$value]['sq'] = $this->zhangdie($pricelist[$value]['zd']);
            if (empty($price_arrbq[$value])) {
                $pricelist[$value]['bq'] = '-';
                $pricelist[$value]['zd'] = '-';
                $pricelist[$value]['sq'] = '-';
            }
            $pricelist[$value]['monthjj'] = Round($monthjj, 1);
            $pricelist[$value]['yearjj'] = Round($yearjj, 1);
        }

        $price_arrbq = $this->_dao->getpricebypricestr("B963102", $lastday);
        $price_arrsq = $this->_dao->getpricebypricestr("B963102", $szdatelastday);

        if (strtotime($monthstart) > strtotime($lastday)) {
            $monthjj = $this->_dao->average_price("B963102", $lastday, $lastday);
        } else {
            $monthjj = $this->_dao->average_price("B963102", $monthstart, $lastday);

        }
        //$monthjj=$this->_dao->average_price("B963102",$monthstart,$date);
        $yearjj = $this->_dao->average_price("B963102", $yearstart, $lastday);
        $pricelist['B963102']['bq'] = Round($price_arrbq['B963102'], 1);
        if ( $price_arrsq['B963102'] ){
            $pricelist['B963102']['zd'] = Round(((Round($price_arrbq['B963102'], 1) - Round($price_arrsq['B963102'], 1)) / Round($price_arrsq['B963102'], 1) * 100), 2);
        }else{
            $pricelist['B963102']['zd'] = "-";
        }
        $pricelist['B963102']['sq'] = $this->zhangdie($pricelist['B963102']['zd']);
        if (empty($pricelist['B963102'])) {
            $pricelist['B963102']['bq'] = '-';
            $pricelist['B963102']['zd'] = '-';
            $pricelist['B963102']['sq'] = '-';
        }
        $pricelist['B963102']['monthjj'] = Round($monthjj, 1);
        $pricelist['B963102']['yearjj'] = Round($yearjj, 1);


        $this->assign("pricelist", $pricelist);


        $sql = " select weipriceusb from shpi_material_pzp where dateday='" . $date . "'  and vid=3 ";
        $bq = $this->_dao->getone($sql);

        $sql = "select weipriceusb  from shpi_material_pzp where dateday='" . $szdate . "' and vid=3 order by dateday desc";
        $sq = $this->_dao->getone($sql);
        $monthjj = $this->_dao->getone(" select avg(weipriceusb) from shpi_material_pzp where dateday<='" . $date . "' and dateday>='" . $monthstart . "' and vid=3 ");
        $yearjj = $this->_dao->getone(" select avg(weipriceusb) from shpi_material_pzp where dateday<='" . $date . "' and dateday>='" . $yearstart . "' and  vid=3 ");

        $list[0][0] = $bq;
        if ($sq){
            $list[0][1] = $this->zhangdie(Round(($bq - $sq) / $sq * 100, 2));
            $list[0][4] = Round(($bq - $sq) / $sq * 100, 2);
        }else{
            $list[0][1] = "-";
            $list[0][4] = "-";
        }
        $list[0][2] = Round($monthjj, 1);
        $list[0][3] = Round($yearjj, 1);
        if (empty($bq)) {
            $list[0][0] = '-';
            $list[0][1] = '-';
            $list[0][4] = '-';
        }

        $sql = " select weiprice from shpi_pp where dateday='" . $date . "'  and bc_id=9 ";
        $bq = $this->_dao->getone($sql);

        $sql = "select weiprice  from shpi_pp where dateday='" . $szdate . "' and bc_id=9 order by dateday desc";
        $sq = $this->_dao->getone($sql);
        $monthjj = $this->_dao->getone(" select avg(weiprice) from shpi_pp where dateday<='" . $date . "' and dateday>='" . $monthstart . "' and bc_id=9 ");
        $yearjj = $this->_dao->getone(" select avg(weiprice) from shpi_pp where dateday<='" . $date . "' and dateday>='" . $yearstart . "' and  bc_id=9 ");

        $list[1][0] = $bq;
        if ( $sq){
            $list[1][1] = $this->zhangdie(Round(($bq - $sq) / $sq * 100, 2));
            $list[1][4] = Round(($bq - $sq) / $sq * 100, 2);
        }else{
            $list[1][1] = "-";
            $list[1][4] = "-";
        }
        $list[1][2] = Round($monthjj, 1);
        $list[1][3] = Round($yearjj, 1);
        if (empty($bq)) {
            $list[1][0] = '-';
            $list[1][1] = '-';
            $list[1][4] = '-';
        }

        $sql = " select Round(weiprice) from shpi_mj_pzp where dateday='" . $date . "'  and vid=0 AND type=1 ";
        $bq = $this->_dao->getone($sql);

        $sql = "select Round(weiprice)  from shpi_mj_pzp where dateday='" . $szdate . "' and vid=0 AND type=1 order by dateday desc";
        $sq = $this->_dao->getone($sql);
        $monthjj = $this->_dao->getone(" select avg(weiprice) from shpi_mj_pzp where dateday<='" . $date . "' and dateday>='" . $monthstart . "' and vid=0 AND type=1 ");
        $yearjj = $this->_dao->getone(" select avg(weiprice) from shpi_mj_pzp where dateday<='" . $date . "' and dateday>='" . $yearstart . "' and  vid=0 AND type=1 ");

        $list[2][0] = $bq;
        if ($sq){
            $list[2][1] = $this->zhangdie(Round(($bq - $sq) / $sq * 100, 2));
            $list[2][4] = Round(($bq - $sq) / $sq * 100, 2);
        }else{
            $list[2][1] = "-";
            $list[2][4] = "-";
        }
        $list[2][2] = Round($monthjj);
        $list[2][3] = Round($yearjj);
        if (empty($bq)) {
            $list[2][0] = '-';
            $list[2][1] = '-';
            $list[2][4] = '-';
        }

        $sql = " select Round(price) from shpi_material where dateday='" . $date . "'  and  topicture='3' ";
        $bq = $this->_dao->getone($sql);

        $sql = "select Round(price)  from shpi_material where dateday='" . $szdate . "' and  topicture='3' order by dateday desc";
        $sq = $this->_dao->getone($sql);
        $monthjj = $this->_dao->getone(" select avg(price) from shpi_material where dateday<='" . $date . "' and dateday>='" . $monthstart . "' and  topicture='3' ");
        $yearjj = $this->_dao->getone(" select avg(price) from shpi_material where dateday<='" . $date . "' and dateday>='" . $yearstart . "' and   topicture='3' ");

        $list[3][0] = $bq;
        if ($sq){
            $list[3][1] = $this->zhangdie(Round(($bq - $sq) / $sq * 100, 2));
            $list[3][4] = Round(($bq - $sq) / $sq * 100, 2);
        }else{
            $list[3][1] = "-";
            $list[3][4] = "-";
        }
        $list[3][2] = Round($monthjj);
        $list[3][3] = Round($yearjj);
        if (empty($bq)) {
            $list[3][0] = '-';
            $list[3][1] = '-';
            $list[3][4] = '-';
        }


        $sql = " select rd1 from rmbrate where rdate='" . $date . "'  limit 1 ";
        $bq = $this->drcdao->getone($sql);

        $sql = "select rd1  from rmbrate where rdate='" . $szdate . "'  order by rdate desc limit 1";
        $sq = $this->drcdao->getone($sql);
        $monthjj = $this->drcdao->getone(" select avg(rd1) from rmbrate where rdate<='" . $date . "' and rdate>='" . $monthstart . "'  ");
        $yearjj = $this->drcdao->getone(" select avg(rd1) from rmbrate where rdate<='" . $date . "' and rdate>='" . $yearstart . "' ");

        $list[4][0] = Round($bq / 100, 4);
        if ($sq){
            $list[4][1] = $this->zhangdie(Round((Round($bq / 100, 4) - Round($sq / 100, 4)) / Round($sq / 100, 4) * 100, 4));
            $list[4][4] = Round((Round($bq / 100, 4) - Round($sq / 100, 4)) / Round($sq / 100, 4) * 100, 4);
        }else{
            $list[4][1] = "-";
            $list[4][4] = "-";
        }
        $list[4][2] = Round($monthjj / 100, 4);
        $list[4][3] = Round($yearjj / 100, 4);
        if (empty($bq)) {
            $list[4][0] = '-';
            $list[4][1] = '-';
            $list[4][4] = '-';
        }

        $arrtem = $params;
        $actionstr = "";
        foreach ($arrtem as $k => $v) {
            $actionstr .= "&" . $k . "=" . $v;
        }
        if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($params['dc'])) {
            //print"<pre>";print_r($params);exit;
            $arr['date'] = $date;
            $arr['result'] = $result;
            $arr['pricelist'] = $pricelist;
            $arr['list'] = $list;
            $this->t1Dao->WriteLog($mid, $uid, $SignCS, $action, $actionstr, $ip, "", "", "查看数据定制-采购中心市场信息日报导出", '', '', $mc_type);
            // $arr['enddate']=$enddate;
            $this->exportcgzxxx($arr);
            exit;
        }

        $this->t1Dao->WriteLog($mid, $uid, $SignCS, $action, $actionstr, $ip, "", "", "查看数据定制-采购中心市场信息日报", '', '', $mc_type);

        $this->assign("list", $list);

        $this->assign("date", $date);

        $this->assign("datetitle", date("n月j日", strtotime($date)));
        $this->assign("mode", $mode);
        $this->assign("GUID", $GUID);
        $this->assign("amount", count($list));
        $this->assign("name", $name);


    }

// public function jsrq($date)//目的是因为php自身bug上个月-1 month是30天
// {
// 	$base = strtotime(date('Y-m',strtotime($date)) . '-01 00:00:01');
// 	$byr = date('j',strtotime($date));//本月第几天
// 	$bmonth_days = date('t',strtotime($date));//本月天数
// 	$smonthdate =date('Y-m-d',strtotime('-1 month', $base));
// 	$smonth_days = date('t',strtotime($smonthdate));//上月天数
// 	//echo $smonth_days2;
// 	if($bmonth_days>=$smonth_days&&$byr>$smonth_days)
// 	{
// 		$datenew=date('Y-m-01',strtotime($date));
// 	}
// 	else
// 	{
// 		$smonthdatenew=date('Y-m-'.$byr,strtotime($smonthdate));
// 		$datenew=date('Y-m-d',strtotime($smonthdatenew." +1 day"));
// 	}

// 	return  $datenew;
// }
    public function jsrq($date)//目的是因为php自身bug上个月-1 month是30天
    {
        $base = strtotime(date('Y-m', strtotime($date)) . '-01 00:00:01');
        $byr = date('j', strtotime($date));//本月第几天
        $smonthdate = date('Y-m-d', strtotime('-1 month', $base));
        if ($byr >= 26) {
            $datenew = date('Y-m-26', strtotime($date));
        } else {
            $smonthdatenew = date('Y-m-' . $byr, strtotime($smonthdate));
            $datenew = date('Y-m-26', strtotime($smonthdatenew));
        }
        //echo  $datenew;
        return $datenew;
    }

    public function jsyear($date)
    {

        // $byr = date('j',strtotime($date));//本年本月第几天
        // $byear_days = date('t',strtotime($date));//本年本月天数
        // $byear = date('Y',strtotime($date));//本年本月天数
        // $syeardate=date(($byear-1).'-m',strtotime($date));
        // $syear_days = date('t',strtotime($syeardate));
        //  if($byear_days>=$syear_days&&$byr>$syear_days)
        //  {
        // 	$datenew=date('Y-m-t',strtotime($syeardate));
        // 	$datenew=date('Y-m-d',strtotime($datenew." +1 day"));

        //  }
        //  else
        //  {
        // 	$smonthdatenew=date('Y-m-'.$byr,strtotime($syeardate));
        // 	$datenew=date('Y-m-d',strtotime($smonthdatenew." +1 day"));
        //  }
        //  return  $datenew;


        $byear = date('Y', strtotime($date));//本年本月天数
        if (strtotime($date) >= strtotime($byear . '-12-26')) {
            $datenew = $byear . '-12-26';

        } else {
            $datenew = ($byear - 1) . '-12-26';
        }
        return $datenew;

    }


    public function get_xhdate($today)//根据当前日获取上个钢之家工作日
    {
        //echo "xiangbin"; exit;
        $flag = 1;
        $lastday = $today;
        while (true) {
            $lastday = date('Y-m-d', strtotime('-' . $flag . ' day', strtotime($lastday)));
            //echo $lastday;
            if (file_get_contents(APP_URL_WWW.'/isholiday.php?date=' . $lastday) != "1") {
                break;
            }
        }
        $today_s = $today;
        $lastday_s = $lastday;
        return $lastday_s;
    }

    public function exportcgzxxx($params)
    {
        //echo "6666";
        $date = $params["date"];
        $result = $params['result'];
        $pricelist = $params['pricelist'];
        $list = $params['list'];
//    $enddate=$params["enddate"];
//    $marketarr=$GLOBALS['marketarr'];
//    $pricelist=$GLOBALS['pricelist'];

        $objPHPExcel = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $styleArray1 = array(
            'font' => array('size' => 11),
            'alignment' => array('horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER),
        );
        $styleArray2 = array(
            'font' => array('size' => 11),
            'alignment' => array('vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER, 'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER),
        );
        $styleArray = array(
            'borders' => array(
                'allBorders' => array(
                    // 'style' => PHPExcel_Style_Border::BORDER_THICK,//边框是粗的
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,//细边框
                    //'color' => array('argb' => 'FFFF0000'),
                ),
            ),
        );
        $styleArray3 = array(
            'font' => array('size' => 16, 'bold' => true),
        );

        $styleArray4 = array(
            'alignment' => array('vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER, 'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT),
        );
        $styleArray5 = array(
            'fill' => array(
                'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                'color' => array('rgb' => '0070C0')
            )
        );

        $phpColor = new \PhpOffice\PhpSpreadsheet\Style\Color();
        $phpColor->setRGB('FF0000');

        $phpColor1 = new \PhpOffice\PhpSpreadsheet\Style\Color();
        $phpColor1->setRGB('0070C0');
        $phpColor2 = new \PhpOffice\PhpSpreadsheet\Style\Color();
        $phpColor2->setRGB('FFFFFF');

        $phpColor3 = new \PhpOffice\PhpSpreadsheet\Style\Color();
        $phpColor3->setRGB('008000');
// $shuju=$this->getpricebypricestr($pricelist[1],$startdate,$enddate);
// $infolist=$shuju['data'];
// $datearr=$shuju['date'];

        $objPHPExcel->createSheet();
        $objPHPExcel->setActiveSheetIndex(0);
        $objActSheet_SY = $objPHPExcel->getActiveSheet();
        $objActSheet_SY->setTitle( '采购中心市场信息日报');
        $objActSheet_SY->mergeCells('B1:G1');
        $objActSheet_SY->mergeCells('B3:G3');
        $objActSheet_SY->mergeCells('B4:C4');

        $objActSheet_SY->mergeCells('B5:B12');
        $objActSheet_SY->mergeCells('B13:B27');
        $objActSheet_SY->mergeCells('C27:G27');

        $objActSheet_SY->setCellValue('B1',  '采购中心市场信息日报（' . date("n月j日", strtotime($date)) . '）');
        $objActSheet_SY->setCellValue('B2',  '编制：集团采购中心');
//$objActSheet_SY -> setCellValue('H2',iconv('gb2312','utf-8','日期：'.$date);
        $objActSheet_SY->setCellValue('B3',  '一.市场价格变动');

        $objActSheet_SY->setCellValue('B4',  '物资名称');
        $objActSheet_SY->setCellValue('B5',  '期货行情');
        $objActSheet_SY->setCellValue('B13',  '原材料市场价格');

        $objActSheet_SY->setCellValue('D4',  '今日收盘价/市场价');
        $objActSheet_SY->setCellValue('E4',  '同比上周% ');
        $objActSheet_SY->setCellValue('F4',  '本月均价% ');
        $objActSheet_SY->setCellValue('G4',  '本年均价');


// $objActSheet_SY -> setCellValue('AB4',iconv('gb2312','utf-8','价格');
        $objActSheet_SY->getStyle("B1:G1")->applyFromArray($styleArray3);
//$objActSheet_SY ->getStyle("A2:AB2")->applyFromArray($styleArray3);
        $objActSheet_SY->getColumnDimension('B')->setWidth(10); //列宽
        $objActSheet_SY->getColumnDimension('C')->setWidth(35);
        $objActSheet_SY->getColumnDimension('D')->setWidth(20);
        $objActSheet_SY->getColumnDimension('E')->setWidth(13);
        $objActSheet_SY->getColumnDimension('F')->setWidth(20);
        $objActSheet_SY->getColumnDimension('G')->setWidth(20);

// 铁矿石主力合约
// 螺纹钢主力合约
// 焦炭主力合约
// 焦煤主力合约
// 伦镍主力合约
// 沪镍主力合约
// 硅锰主力合约
// 硅铁主力合约
// 铁矿石62%普氏指数
// 铁矿石65%普氏指数
// 铁矿石现货价格（青岛港PB粉）
// 国内焦煤指数
// 进口焦煤指数
// 喷吹烟煤（靖江太和港）
// 焦炭指数
// 镍板（上海1#电解镍）
// 钼铁（辽宁FeMo60）
// 钒铁（四川FeV50-B）
// 高碳铬铁（内蒙古FeCr55C1000）
// 硅锰合金（内蒙古FeMn68Si18）
// 450超高功率电极

        $objActSheet_SY->setCellValue('C5',  '铁矿石主力合约');

        $objActSheet_SY->setCellValue('D5',  $result['SHQHDAY_20']['bq']);
        $objActSheet_SY->setCellValue('E5',  $result['SHQHDAY_20']['zd']);
        $this->zhangdieexcle($result['SHQHDAY_20']['zd'], $objActSheet_SY, 'E5', $phpColor, $phpColor3);
        $objActSheet_SY->setCellValue('F5',  $result['SHQHDAY_20']['monthjj']);
        $objActSheet_SY->setCellValue('G5',  $result['SHQHDAY_20']['yearjj']);

        $objActSheet_SY->setCellValue('C6',  '螺纹钢主力合约');
        $objActSheet_SY->setCellValue('D6',  $result['SHQHDAY_4']['bq']);
        $objActSheet_SY->setCellValue('E6',  $result['SHQHDAY_4']['zd']);
        $this->zhangdieexcle($result['SHQHDAY_4']['zd'], $objActSheet_SY, 'E6', $phpColor, $phpColor3);
        $objActSheet_SY->setCellValue('F6',  $result['SHQHDAY_4']['monthjj']);
        $objActSheet_SY->setCellValue('G6',  $result['SHQHDAY_4']['yearjj']);

        $objActSheet_SY->setCellValue('C7',  '焦炭主力合约');
        $objActSheet_SY->setCellValue('D7',  $result['SHQHDAY_9']['bq']);
        $objActSheet_SY->setCellValue('E7',  $result['SHQHDAY_9']['zd']);
        $this->zhangdieexcle($result['SHQHDAY_9']['zd'], $objActSheet_SY, 'E7', $phpColor, $phpColor3);
        $objActSheet_SY->setCellValue('F7',  $result['SHQHDAY_9']['monthjj']);
        $objActSheet_SY->setCellValue('G7',  $result['SHQHDAY_9']['yearjj']);


        $objActSheet_SY->setCellValue('C8',  '焦煤主力合约');
        $objActSheet_SY->setCellValue('D8',  $result['SHQHDAY_19']['bq']);
        $objActSheet_SY->setCellValue('E8',  $result['SHQHDAY_19']['zd']);
        $this->zhangdieexcle($result['SHQHDAY_19']['zd'], $objActSheet_SY, 'E8', $phpColor, $phpColor3);
        $objActSheet_SY->setCellValue('F8',  $result['SHQHDAY_19']['monthjj']);
        $objActSheet_SY->setCellValue('G8',  $result['SHQHDAY_19']['yearjj']);

        $objActSheet_SY->setCellValue('C9',  '伦镍主力合约');

        $objActSheet_SY->setCellValue('D9',  $result['LME']['bq']);
        $objActSheet_SY->setCellValue('E9',  $result['LME']['zd']);
        $this->zhangdieexcle($result['LME']['zd'], $objActSheet_SY, 'E9', $phpColor, $phpColor3);
        $objActSheet_SY->setCellValue('F9',  $result['LME']['monthjj']);
        $objActSheet_SY->setCellValue('G9',  $result['LME']['yearjj']);


        $objActSheet_SY->setCellValue('C10',  '沪镍主力合约');
        $objActSheet_SY->setCellValue('D10',  $result['SHQHDAY_24']['bq']);
        $objActSheet_SY->setCellValue('E10',  $result['SHQHDAY_24']['zd']);
        $this->zhangdieexcle($result['SHQHDAY_24']['zd'], $objActSheet_SY, 'E10', $phpColor, $phpColor3);
        $objActSheet_SY->setCellValue('F10',  $result['SHQHDAY_24']['monthjj']);
        $objActSheet_SY->setCellValue('G10',  $result['SHQHDAY_24']['yearjj']);

        $objActSheet_SY->setCellValue('C11',  '硅锰主力合约');
        $objActSheet_SY->setCellValue('D11',  $result['SHQHDAY_23']['bq']);
        $objActSheet_SY->setCellValue('E11',  $result['SHQHDAY_23']['zd']);
        $this->zhangdieexcle($result['SHQHDAY_23']['zd'], $objActSheet_SY, 'E11', $phpColor, $phpColor3);
        $objActSheet_SY->setCellValue('F11',  $result['SHQHDAY_23']['monthjj']);
        $objActSheet_SY->setCellValue('G11',  $result['SHQHDAY_23']['yearjj']);

        $objActSheet_SY->setCellValue('C12',  '硅铁主力合约');
        $objActSheet_SY->setCellValue('D12',  $result['SHQHDAY_22']['bq']);
        $objActSheet_SY->setCellValue('E12',  $result['SHQHDAY_22']['zd']);
        $this->zhangdieexcle($result['SHQHDAY_22']['zd'], $objActSheet_SY, 'E12', $phpColor, $phpColor3);
        $objActSheet_SY->setCellValue('F12',  $result['SHQHDAY_22']['monthjj']);
        $objActSheet_SY->setCellValue('G12',  $result['SHQHDAY_22']['yearjj']);

        $objActSheet_SY->setCellValue('C13',  '铁矿石62%普氏指数');
        $objActSheet_SY->setCellValue('D13',  $list[0][0]);
        $objActSheet_SY->setCellValue('E13',  $list[0][4]);
        $this->zhangdieexcle($list[0][4], $objActSheet_SY, 'E13', $phpColor, $phpColor3);
        $objActSheet_SY->setCellValue('F13',  $list[0][2]);
        $objActSheet_SY->setCellValue('G13',  $list[0][3]);

        $objActSheet_SY->setCellValue('C14',  '铁矿石65%普氏指数');
        $objActSheet_SY->setCellValue('D14',  $list[1][0]);
        $objActSheet_SY->setCellValue('E14',  $list[1][4]);
        $this->zhangdieexcle($list[1][4], $objActSheet_SY, 'E14', $phpColor, $phpColor3);
        $objActSheet_SY->setCellValue('F14',  $list[1][2]);
        $objActSheet_SY->setCellValue('G14',  $list[1][3]);


        $objActSheet_SY->setCellValue('C15',  '铁矿石现货价格（青岛港PB粉）');
        $objActSheet_SY->setCellValue('D15',  $pricelist['1886103']['bq']);
        $objActSheet_SY->setCellValue('E15',  $pricelist['1886103']['zd']);
        $this->zhangdieexcle($pricelist['1886103']['zd'], $objActSheet_SY, 'E15', $phpColor, $phpColor3);
        $objActSheet_SY->setCellValue('F15',  $pricelist['1886103']['monthjj']);
        $objActSheet_SY->setCellValue('G15',  $pricelist['1886103']['yearjj']);

        $objActSheet_SY->setCellValue('C16',  '国内焦煤指数');
        $objActSheet_SY->setCellValue('D16',  $list[2][0]);
        $objActSheet_SY->setCellValue('E16',  $list[2][4]);
        $this->zhangdieexcle($list[2][4], $objActSheet_SY, 'E16', $phpColor, $phpColor3);
        $objActSheet_SY->setCellValue('F16',  $list[2][2]);
        $objActSheet_SY->setCellValue('G16',  $list[2][3]);


        $objActSheet_SY->setCellValue('C17',  '进口焦煤指数');
        $objActSheet_SY->setCellValue('D17',  $pricelist['B963102']['bq']);
        $objActSheet_SY->setCellValue('E17',  $pricelist['B963102']['zd']);
        $this->zhangdieexcle($pricelist['B963102']['zd'], $objActSheet_SY, 'E17', $phpColor, $phpColor3);
        $objActSheet_SY->setCellValue('F17',  $pricelist['B963102']['monthjj']);
        $objActSheet_SY->setCellValue('G17',  $pricelist['B963102']['yearjj']);

        $objActSheet_SY->setCellValue('C18',  '喷吹烟煤（靖江太和港）');
        $objActSheet_SY->setCellValue('D18',  $pricelist['F263302']['bq']);
        $objActSheet_SY->setCellValue('E18',  $pricelist['F263302']['zd']);
        $this->zhangdieexcle($pricelist['F263302']['zd'], $objActSheet_SY, 'E18', $phpColor, $phpColor3);
        $objActSheet_SY->setCellValue('F18',  $pricelist['F263302']['monthjj']);
        $objActSheet_SY->setCellValue('G18',  $pricelist['F263302']['yearjj']);

        $objActSheet_SY->setCellValue('C19',  '焦炭指数');
        $objActSheet_SY->setCellValue('D19',  $list[3][0]);
        $objActSheet_SY->setCellValue('E19',  $list[3][4]);
        $this->zhangdieexcle($list[3][4], $objActSheet_SY, 'E19', $phpColor, $phpColor3);
        $objActSheet_SY->setCellValue('F19',  $list[3][2]);
        $objActSheet_SY->setCellValue('G19',  $list[3][3]);

        $objActSheet_SY->setCellValue('C20',  '镍板（上海1#电解镍）');
        $objActSheet_SY->setCellValue('D20',  $pricelist['076260']['bq']);
        $objActSheet_SY->setCellValue('E20',  $pricelist['076260']['zd']);
        $this->zhangdieexcle($pricelist['076260']['zd'], $objActSheet_SY, 'E20', $phpColor, $phpColor3);
        $objActSheet_SY->setCellValue('F20',  $pricelist['076260']['monthjj']);
        $objActSheet_SY->setCellValue('G20',  $pricelist['076260']['yearjj']);

        $objActSheet_SY->setCellValue('C21',  '钼铁（辽宁FeMo60）');
        $objActSheet_SY->setCellValue('D21',  $pricelist['459310']['bq']);
        $objActSheet_SY->setCellValue('E21',  $pricelist['459310']['zd']);
        $this->zhangdieexcle($pricelist['459310']['zd'], $objActSheet_SY, 'E21', $phpColor, $phpColor3);
        $objActSheet_SY->setCellValue('F21',  $pricelist['459310']['monthjj']);
        $objActSheet_SY->setCellValue('G21',  $pricelist['459310']['yearjj']);

        $objActSheet_SY->setCellValue('C22',  '钒铁（四川FeV50-B）');
        $objActSheet_SY->setCellValue('D22',  $pricelist['5394101']['bq']);
        $objActSheet_SY->setCellValue('E22',  $pricelist['5394101']['zd']);
        $this->zhangdieexcle($pricelist['5394101']['zd'], $objActSheet_SY, 'E22', $phpColor, $phpColor3);
        $objActSheet_SY->setCellValue('F22',  $pricelist['5394101']['monthjj']);
        $objActSheet_SY->setCellValue('G22',  $pricelist['5394101']['yearjj']);

        $objActSheet_SY->setCellValue('C23',  '高碳铬铁（内蒙古FeCr55C1000）');
        $objActSheet_SY->setCellValue('D23',  $pricelist['4196101']['bq']);
        $objActSheet_SY->setCellValue('E23',  $pricelist['4196101']['zd']);
        $this->zhangdieexcle($pricelist['4196101']['zd'], $objActSheet_SY, 'E23', $phpColor, $phpColor3);
        $objActSheet_SY->setCellValue('F23',  $pricelist['4196101']['monthjj']);
        $objActSheet_SY->setCellValue('G23',  $pricelist['4196101']['yearjj']);

        $objActSheet_SY->setCellValue('C24',  '硅锰合金（内蒙古FeMn68Si18）');
        $objActSheet_SY->setCellValue('D24',  $pricelist['4189101']['bq']);
        $objActSheet_SY->setCellValue('E24',  $pricelist['4189101']['zd']);
        $this->zhangdieexcle($pricelist['4189101']['zd'], $objActSheet_SY, 'E24', $phpColor, $phpColor3);
        $objActSheet_SY->setCellValue('F24',  $pricelist['4189101']['monthjj']);
        $objActSheet_SY->setCellValue('G24',  $pricelist['4189101']['yearjj']);


        $objActSheet_SY->setCellValue('C25',  '450超高功率电极');
        $objActSheet_SY->setCellValue('D25',  $pricelist['5797036']['bq']);
        $objActSheet_SY->setCellValue('E25',  $pricelist['5797036']['zd']);
        $this->zhangdieexcle($pricelist['5797036']['zd'], $objActSheet_SY, 'E25', $phpColor, $phpColor3);
        $objActSheet_SY->setCellValue('F25',  $pricelist['5797036']['monthjj']);
        $objActSheet_SY->setCellValue('G25',  $pricelist['5797036']['yearjj']);

        $objActSheet_SY->setCellValue('C26',  '汇率');
        $objActSheet_SY->setCellValue('D26',  $list[4][0]);
        $objActSheet_SY->setCellValue('E26',  $list[4][4]);
        $this->zhangdieexcle($list[4][4], $objActSheet_SY, 'E26', $phpColor, $phpColor3);
        $objActSheet_SY->setCellValue('F26',  $list[4][2]);
        $objActSheet_SY->setCellValue('G26',  $list[4][3]);

        $objActSheet_SY->setCellValue('C27',  '备注：高碳铬铁与硅锰合金均包含350元/吨运费。');

        $objActSheet_SY->getStyle("B1")->applyFromArray($styleArray2);

        $objActSheet_SY->getStyle("B3:G27")->applyFromArray($styleArray);
        $objActSheet_SY->getStyle("B3:G27")->applyFromArray($styleArray2);

        $objActSheet_SY->getStyle("B3")->applyFromArray($styleArray4);
        $objActSheet_SY->getStyle("C27")->applyFromArray($styleArray4);

        $objActSheet_SY->getStyle("B3")->applyFromArray($styleArray5);

        $objActSheet_SY->getStyle('B1')->getFont()->setColor($phpColor);

        $objActSheet_SY->getStyle('B2')->getFont()->setColor($phpColor1);

        $objActSheet_SY->getStyle('B3')->getFont()->setColor($phpColor2);
        $objActSheet_SY->getStyle('C27')->getFont()->setColor($phpColor);

        $objActSheet_SY->getStyle('B13')->getAlignment()->setWrapText(TRUE);

        $objPHPExcel->setActiveSheetIndex(0);
        $name = "采购中心市场信息日报" . $params['date'];
        $filename = $name . '.xls';
        header("Pragma: public");
        header("Expires: 0");
        header("Cache-Control:must-revalidate, post-check=0, pre-check=0");
        header("Content-Type:application/force-download");
        header("Content-Type:application/vnd.ms-execl");
        header("Content-Type:application/octet-stream");
        header("Content-Type:application/download");
        header('Content-Disposition:attachment;filename=' . $filename);
        header("Content-Transfer-Encoding:binary");
        $objWriter = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($objPHPExcel, 'Xls');
        $objWriter->save('php://output');


    }

    function zhangdieexcle($int, $obj, $weizhi, $phpColor, $phpColor3)
    {


        $intstr = "";
        if ($int < 0) {
            $obj->getStyle($weizhi)->getFont()->setColor($phpColor3);
        } elseif ($int > 0) {
            $obj->getStyle($weizhi)->getFont()->setColor($phpColor);
        }
    }

    function zhangdie($int)
    {
        $int = (float)$int;
        $intstr = "";
        if ($int < 0) {
            $intstr = "<font class='greenfont'>↓" . abs($int) . "</font>";
        } elseif ($int > 0) {
            $intstr = "<font class='redfont'>↑" . abs($int) . "</font>";
        } elseif ($int == "") {
            $intstr = "―";
        } else {
            $intstr = "<font >" . $int . "</font>";
        }
        return $intstr;
    }


    function P($arr)
    {
        if ($_GET['debug'] == 1) {
            print"<pre>";
            print_r($arr);
            print"</pre>";
        }
    }
}

?>