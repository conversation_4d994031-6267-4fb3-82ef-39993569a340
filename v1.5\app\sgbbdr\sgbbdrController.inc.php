<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );

class sgbbdrController extends AbstractController{
  
	public function __construct(){
		parent::__construct();
		$this->_action->setDao( new sgbbdrDao("DRCW") );  //drc
		$this->_action->t1Dao=new sgbbdrDao("MAIN");  //
		$this->_action->homeDao=new sgbbdrDao("91R");
		$this->_action->gcDao=new sgbbdrDao("GC");
	}

	public function _dopre()
	{
		$this->_action->checkSession();
	}

	
	function do_drPqxxTiBao(){
		$this->_action->drPqxxTiBao( $this->_request );  //定时程序，录入数据
	}

	function do_drZhidaojia_dr(){
		$this->_action->drZhidaojia_dr( $this->_request );  //定时程序，录入数据
	}

	function v_hcgcyxsupload()
	{
		$this->_action->hcgcyxsupload( $this->_request );
	}

	function v_sheet()
	{
		$this->_action->sheet( $this->_request );
	}
	
	function do_sheet()
	{
		$this->_action->dosheet( $this->_request );
	}


	function v_hcgczxsupload()
	{
		$this->_action->hcgczxsupload( $this->_request );
	}

	function v_sheetzxs()
	{
		$this->_action->sheetzxs( $this->_request );
	}
	
	function do_sheetzxs()
	{
		$this->_action->dosheetzxs( $this->_request );
	}



	function v_yjyybupload()
	{
		$this->_action->yjyybupload( $this->_request );
	}

	function v_sheetyjy()
	{
		$this->_action->sheetyjy( $this->_request );
	}
	
	function do_sheetyjy()
	{
		$this->_action->dosheetyjy( $this->_request );
	}

//start研究院日销售
function v_hcgcrxsupload()
{
	$this->_action->hcgcrxsupload( $this->_request );
}

function v_sheetrxs()
{
	$this->_action->sheetrxs( $this->_request );
}

function do_sheetrxs()
{
	$this->_action->dosheetrxs( $this->_request );
}

//end


//start烧结
function v_sjupload()
{
	$this->_action->sjupload( $this->_request );
}

function v_sheetsj()
{
	$this->_action->sheetsj( $this->_request );
}

function do_sheetsj()
{
	$this->_action->dosheetsj( $this->_request );
}

//end


	
	function do_uploadFileAll()
	{
		$this->_action->uploadFileAll( $this->_request );
	}


}
?>