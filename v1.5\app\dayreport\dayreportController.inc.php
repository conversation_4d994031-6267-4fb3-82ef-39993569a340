<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );

class dayreportController extends AbstractController{
  
  public function __construct(){
    parent::__construct();
	$this->_action->setDao( new dayreportDao("DRCW") );
	$this->_action->t1Dao=new dayreportDao("MAIN");
	$this->_action->homeDao=new dayreportDao("91R");
	$this->_action->gcDao=new dayreportDao("GC");
  }

	public function v_gc(){
		$this->_action->gc( $this->_request );
	}

	public function v_yl(){
		$this->_action->yl( $this->_request );
	}
}
?>