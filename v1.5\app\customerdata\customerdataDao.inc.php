<?php
class customerdataDao extends Dao{
	public function __construct( $writer ){
		parent::__construct( $writer );
	}

	public function get_uid($GUID,$SignCS,$mc_type)
	{
		$sql = "select Uid from app_session_temp where GUID='".$GUID."' and SignCS='".$SignCS."' and mc_type='".$mc_type."' order by LoginDate desc limit 1";
		$udata = $this->getOne($sql);

		return $udata;
	}

	public function get_uname($uid)
	{
		$uid = ltrim($uid,",");
		$uid = rtrim($uid, ",");

		$sql = "SELECT truename from adminuser where id in (".$uid.")";
		$uname = $this->getOnes($sql);
		
		return $uname;
	}

	public function getuser_mid($mid,$mc_type,$uid)
	{
		//$sql = "SELECT Uid,TrueName from app_session_temp where Mid='".$mid."' and mc_type='".$mc_type."' and Uid!='".$uid."' group by TrueName";
		$sql = "SELECT Uid,TrueName FROM app_session_temp, app_license_detail WHERE Mid='".$mid."' AND app_session_temp.mc_type='".$mc_type."' AND Uid!='".$uid."' AND app_license_detail.UseUser = app_session_temp.UserName AND STATUS =1 GROUP BY TrueName ORDER BY TrueName DESC";
		$users = $this->query($sql);
		
		return $users;
	}

	public function get_catalogue($Uid,$mc_type)
	{
		$sql = "select id,aod,parentid,cataloguename,hasnode,hasleaves,status,createtime,PowerID,isimport,importfile,importid from dc_customer_data_catalogue where Uid='".$Uid."' and isCatalogue=1 and mc_type='".$mc_type."' and isdel=0 order by aod";
		$caldata = $this->query($sql);
 
		return $caldata;
	}

	public function getdata_parentid($id,$uid,$mc_type)
	{
		$sql = "SELECT id,aod,cataloguename,D1ImageType,D1UnitName,D1UnitType,D1UnitConv,D1dtymd,status,createtime,PowerID from dc_customer_data_catalogue where parentid='".$id."' and isCatalogue=0 and Uid='".$uid."' and mc_type='".$mc_type."' and isdel=0 order by aod asc";
		$data = $this->query($sql);

		return $data;
	}

	public function get_parname($id)
	{
		$sql = "SELECT id,cataloguename,parentid,nodelevel,hasnode from dc_customer_data_catalogue where id='".$id."'";
		$pardata = $this->getRow($sql);

		return $pardata;
	}

	public function getcal_parentid($pid,$iscatalogue,$uid="",$mc_type="")
	{
		if($uid != "")
			$uid = " and Uid='".$uid."'";
		if($mc_type != "")
			$mc_type = " and mc_type='".$mc_type."'";

		$sql = "SELECT id,cataloguename from dc_customer_data_catalogue where parentid='".$pid."' and isdel=0 and isCatalogue='".$iscatalogue."'".$mc_type.$uid;
		$childdata = $this->query($sql);

		return $childdata;
	}
}
?>