<?php
class dcfocusimgAction extends AbstractAction{
	
	var $client = null;

  public function __construct(){
    parent::__construct();    

  }

	public function createimg($params)
	{
        $type=$params['type']?$params['type']:1;
        $cityid=$params['cityid']?$params['cityid']:'07';
        $cityname=$params['cityname']?$params['cityname']:'上海';
		//echo $type;
        if($type=='zdy')
        {   $priceidstr=$params['priceidstr'];
            $new_chartdata['lendtitle'] = explode(',',$params['lendtitle']);
            $new_chartdata['ImageTitle'] = $params['ImageTitle'];
        }
        elseif($type==1)
        {
            $priceidstr=$cityid."1103,".$cityid."2023";
            $new_chartdata['lendtitle'] = array('8高线','20螺纹钢');
            $new_chartdata['ImageTitle'] = $cityname.'市场建筑钢材';
        }
        elseif($type==2)
        {
            $priceidstr=$cityid."3112,".$cityid."4112,".$cityid."4212";
            $new_chartdata['lendtitle'] = array('5.5热','2.75热','1.0冷');
            $new_chartdata['ImageTitle'] = $cityname.'市场冷热板卷';
        }
        elseif($type==3)
        {
            $priceidstr=$cityid."3011,".$cityid."3012";
            $new_chartdata['lendtitle'] = array('8中板','20中板');
            $new_chartdata['ImageTitle'] = $cityname.'市场中厚板';
        }
        
        $priceidarr = explode(',', $priceidstr);
		$enddate = date('Y-m-d', time());
		$startdate = date('Y-m-d',strtotime("-1year", strtotime($enddate)));
		$shuju = $this->getpricebypricestr($priceidstr, $startdate, $enddate);
        //print_r($shuju);exit;
        
        $new_chartdata['dw'] = array('元/吨','元/吨','元/吨');
        
        $infolist = $shuju['data'];
        $datearr = $shuju['date'];
        
        $data=array();
        foreach($datearr as $k=>$v)
        {
            foreach($priceidarr as $k1=>$v1)
            {
                $data[$k1][$k]=isset($infolist[$v][$v1])?$infolist[$v][$v1]:"'-'";
            }
            
        }
        //print_r($data);exit;
		$new_chartdata['data'] = $data;
        $new_chartdata['xlabel'] = $datearr;
		$new_chartdata['isbig'] = 0;
		include_once(APP_DIR . "/echarts/EChartsNewAction.inc.php");
		$echarts = new EChartsNewAction();
        if($type!='zdy')
        {
            $fn_1='focus00'.$cityid.$type.'.jpg';
        }
        else
        {
            $fn_1=$params['constpicname'].'.jpg';
        }
        
		$new_chartdata['imgname'] = $fn_1;
		$new_chartdata['url'] = IMGURL . 'new/' . $fn_1;
		$new_chartdata['callbackname'] = $params['callbackname'];
		$new_chartdata['align'] = $params['align'];
        $new_chartdata['mc_type'] = 0;
        $new_chartdata['mode'] = 1;
		$new_chartdata['isstaticimg'] = 1; $new_chartdata['color']=$params['color']?$params['color']:2;
		//echo '<pre>';print_R($new_chartdata);
		$new_chartdata['ImageType'] = 2;
        //print_r($new_chartdata);exit;
		$echarts->dataview($new_chartdata, $this->t1dao);
		exit;

	}
    public function createindexpicfocus01($params)
	{

        $new_chartdata['lendtitle'] = array('钢材(SHSPI)','长材(SHSPI-L)','扁平材(SHSPI-F)');
        $new_chartdata['ImageTitle'] = '钢之家钢材基准价格指数';
        $arr=array(
            array(
                'table'=>'shpi_pi',
                'field'=>'1',
               'index_field' => 'wpindex',
                'value'=>'1'
            ),
            array(
                'table'=>'shpi_pp',
                'field'=>'bc_id',
               'index_field' => 'wpindex',
                'value'=>'1'
            ),
            array(
                'table'=>'shpi_pp',
                'field'=>'bc_id',
               'index_field' => 'wpindex',
                'value'=>'2'
            )
        );
        $enddate = date('Y-m-d', time());
		$startdate = date('Y-m-d',strtotime("-1year", strtotime($enddate)));
        $dataarr = array();//数据数组
        $datearr = array();//日期数组
        foreach($arr as $k=>$v){
            $sql="select dateday,$v[index_field] as mindex from $v[table] where $v[field] = '$v[value]' and dateday >='".$startdate."' and dateday <='".$enddate."' order by dateday asc";
            
            $IndexList = $this->_dao->query($sql);
            
            foreach ($IndexList as $v1) {
                $date = date('y-m-d', strtotime($v1['dateday']));
                if (!in_array($date, $datearr)) {
                    $datearr[] = $date;
                }
                $dataarr[$date][$k] = $v1['mindex'];
            }
       }

        $data=array();
        foreach($datearr as $k=>$v)
        {
            foreach($arr as $k1=>$v1){
               $data[$k1][$k]=isset($dataarr[$v][$k1])?$dataarr[$v][$k1]:'-';
            }
        }
        //print_r($data);exit;
		$new_chartdata['data'] = $data;
        $new_chartdata['xlabel'] = $datearr;
		$new_chartdata['isbig'] = 0;
		include_once(APP_DIR . "/echarts/EChartsNewAction.inc.php");
		$echarts = new EChartsNewAction();
        $fn_1='indexpicfocus01.jpg';
        if($params['constpicname'])
        {
            $fn_1=$params['constpicname'].'.jpg';
        }
        
		$new_chartdata['imgname'] = $fn_1;
		$new_chartdata['url'] = IMGURL . 'new/' . $fn_1;
		$new_chartdata['callbackname'] = $params['callbackname'];
		$new_chartdata['align'] = $params['align'];
        $new_chartdata['mc_type'] = 0;
        $new_chartdata['mode'] = 1;
		$new_chartdata['isstaticimg'] = 1; $new_chartdata['color']=$params['color']?$params['color']:2;;
		//echo '<pre>';print_R($new_chartdata);
		$new_chartdata['ImageType'] = 2;
        //print_r($new_chartdata);exit;
		$echarts->dataview($new_chartdata, $this->t1dao);
		exit;
    }
    public function createimgyl($params)
	{

        $new_chartdata['lendtitle'] = array('原料综合指数','铁矿石');
        $new_chartdata['ImageTitle'] = '钢之家钢铁原料价格指数(SHMPI)';
        $arr=array(
            array(
                'table'=>'shpi_material',
                'field'=>'topicture',
               'index_field' => 'mindex',
                'value'=>'1',
               'key' => '1',
            ),
            array(
                'table'=>'shpi_material_pzp',
                'field'=>'vid',
               'index_field' => 'weiindex',
                'value'=>'0',
               'key' => '2',
            ),
        );
        $enddate = date('Y-m-d', time());
		$startdate = date('Y-m-d',strtotime("-1year", strtotime($enddate)));
        $dataarr = array();//数据数组
        $datearr = array();//日期数组
        foreach($arr as $k=>$v){
            $sql="select dateday,$v[index_field] as mindex from $v[table] where $v[field] = '$v[value]' and dateday >='".$startdate."' and dateday <='".$enddate."' order by dateday asc";
            
            $IndexList = $this->_dao->query($sql);
            
            foreach ($IndexList as $v1) {
                $date = date('y-m-d', strtotime($v1['dateday']));
                if (!in_array($date, $datearr)) {
                    $datearr[] = $date;
                }
                $dataarr[$date][$k] = $v1['mindex'];
            }
       }

        $data=array();
        foreach($datearr as $k=>$v)
        {
            foreach($arr as $k1=>$v1){
               $data[$k1][$k]=isset($dataarr[$v][$k1])?$dataarr[$v][$k1]:'-';
            }
        }
        //print_r($data);exit;
		$new_chartdata['data'] = $data;
        $new_chartdata['xlabel'] = $datearr;
		$new_chartdata['isbig'] = 0;
		include_once(APP_DIR . "/echarts/EChartsNewAction.inc.php");
		$echarts = new EChartsNewAction();
        $fn_1='focusllzh_tks_yl.jpg';
        if($params['constpicname'])
        {
            $fn_1=$params['constpicname'].'.jpg';
        }
        
		$new_chartdata['imgname'] = $fn_1;
		$new_chartdata['url'] = IMGURL . 'new/' . $fn_1;
		$new_chartdata['callbackname'] = $params['callbackname'];
		$new_chartdata['align'] = $params['align'];
        $new_chartdata['mc_type'] = 0;
        $new_chartdata['mode'] = 1;
		$new_chartdata['isstaticimg'] = 1; $new_chartdata['color']=$params['color']?$params['color']:2;;
		//echo '<pre>';print_R($new_chartdata);
		$new_chartdata['ImageType'] = 2;
        //print_r($new_chartdata);exit;
		$echarts->dataview($new_chartdata, $this->t1dao);
		exit;
    }
    public function createimgllfocus2($params)
	{
        $new_chartdata['lendtitle'] = array('MB','钢之家');
        $new_chartdata['ImageTitle'] = '62%钢之家,MB铁矿石指数走势图';
        $enddate = date('Y-m-d', time());
		$startdate = date('Y-m-d',strtotime("-1year", strtotime($enddate)));
        $dataarr = array();//数据数组
        $datearr = array();//日期数组

        $sql="select marketconditions.price price,marketconditions.topicture, marketconditions.mconmanagedate from marketconditions
 		  	where    (mconmanagedate>='" . $startdate . " 00:00:00' and mconmanagedate<='" . $enddate . " 23:59:59') and  marketconditions.topicture ='I98640' order by marketconditions.mconmanagedate";
        $IndexList = $this->_dao->query($sql);
        foreach ($IndexList as $v1) {
            $date = date('y-m-d', strtotime($v1['mconmanagedate']));
            if (!in_array($date, $datearr)) {
                $datearr[] = $date;
            }
            $dataarr[$date][0] = $v1['price'];
        }
        

        $sql="SELECT vid,weipriceusb,dateday FROM shpi_material_pzp where vid='3' and dateday >='".$startdate. "'and dateday <='".$enddate."' order by dateday asc";
		$IndexList = $this->_dao->query($sql);
        foreach ($IndexList as $v1) {
            $date = date('y-m-d', strtotime($v1['dateday']));
            if (!in_array($date, $datearr)) {
                $datearr[] = $date;
            }
            $dataarr[$date][1] = $v1['weipriceusb'];
        }
        asort($datearr);
        $data=array();
        foreach($datearr as $k=>$v)
        {
            $data[0][]=isset($dataarr[$v][0])?$dataarr[$v][0]:"'-'";
            $data[1][]=isset($dataarr[$v][1])?$dataarr[$v][1]:"'-'";
        }
        //print_r()
        $new_chartdata['data'] = $data;
        $new_chartdata['xlabel'] = $datearr;
		$new_chartdata['isbig'] = 0;
		include_once(APP_DIR . "/echarts/EChartsNewAction.inc.php");
		$echarts = new EChartsNewAction();
        $fn_1='llindexfocus02.jpg';
		$new_chartdata['imgname'] = $fn_1;
		$new_chartdata['url'] = IMGURL . 'new/' . $fn_1;
		$new_chartdata['callbackname'] = $params['callbackname'];
		$new_chartdata['align'] = $params['align'];
        $new_chartdata['mc_type'] = 0;
        $new_chartdata['mode'] = 1;
		$new_chartdata['isstaticimg'] = 1; $new_chartdata['color']=$params['color']?$params['color']:2;;
		//echo '<pre>';print_R($new_chartdata);
		$new_chartdata['ImageType'] = 2;
        //print_r($new_chartdata);exit;
		$echarts->dataview($new_chartdata, $this->t1dao);
		exit;
    }
    public function createimgtksfocus03($params)
	{
        $new_chartdata['lendtitle'] = array('铁矿石库存走势图 ');
        $new_chartdata['ImageTitle'] = '中国主要港口铁矿石库存';
        $enddate = date('Y-m-d', time());
		$startdate = date('Y-m-d',strtotime("-1year", strtotime($enddate)));
        $dataarr = array();//数据数组
        $datearr = array();//日期数组

        $sql="select dta_3, dta_ym from steelhome_drc.data_table where dta_type = 'JKKCTJ_1'  and dta_1 = '合计' and dta_ym >='".$startdate."' and dta_ym <='".$enddate."' order by dta_ym asc";
        $IndexList = $this->gcdao->query($sql);
        foreach ($IndexList as $v1) {
            $date = date('y-m-d', strtotime($v1['dta_ym']));
            if (!in_array($date, $datearr)) {
                $datearr[] = $date;
            }
            $dataarr[0][] = $v1['dta_3'];
        }
        $new_chartdata['data'] = $dataarr;
        $new_chartdata['xlabel'] = $datearr;
		$new_chartdata['isbig'] = 0;
		include_once(APP_DIR . "/echarts/EChartsNewAction.inc.php");
		$echarts = new EChartsNewAction();
        $fn_1='tksindexfocus03.jpg';
		$new_chartdata['imgname'] = $fn_1;
		$new_chartdata['url'] = IMGURL . 'new/' . $fn_1;
		$new_chartdata['callbackname'] = $params['callbackname'];
		$new_chartdata['align'] = $params['align'];
        $new_chartdata['mc_type'] = 0;
        $new_chartdata['mode'] = 1;
		$new_chartdata['isstaticimg'] = 1; $new_chartdata['color']=$params['color']?$params['color']:2;;
		//echo '<pre>';print_R($new_chartdata);
		$new_chartdata['ImageType'] = 2;
        //print_r($new_chartdata);exit;
		$echarts->dataview($new_chartdata, $this->t1dao);
         exit;
    }
    public function createimgtksfocus04($params)
	{
        $new_chartdata['lendtitle'] = array('澳元兑美元走势图');
        $new_chartdata['ImageTitle'] = '澳元兑美元走势图';
        $enddate = date('Y-m-d', time());
		$startdate = date('Y-m-d',strtotime("-1year", strtotime($enddate)));
        $dataarr = array();//数据数组
        $datearr = array();//日期数组

        $sql="select rd1, rdate from steelhome_drc.usrate where rdate >='".$startdate."' and rdate <='".$enddate."'  and rd1!='' order by rdate asc";
        $IndexList = $this->gcdao->query($sql);
        foreach ($IndexList as $v1) {
            $date = date('y-m-d', strtotime($v1['rdate']));
            if (!in_array($date, $datearr)) {
                $datearr[] = $date;
            }
            $dataarr[0][] = $v1['rd1']*100;
        }
        $new_chartdata['data'] = $dataarr;
        $new_chartdata['xlabel'] = $datearr;
		$new_chartdata['isbig'] = 0;
		include_once(APP_DIR . "/echarts/EChartsNewAction.inc.php");
		$echarts = new EChartsNewAction();
        $fn_1='tksindexfocus04.jpg';
		$new_chartdata['imgname'] = $fn_1;
		$new_chartdata['url'] = IMGURL . 'new/' . $fn_1;
		$new_chartdata['callbackname'] = $params['callbackname'];
		$new_chartdata['align'] = $params['align'];
        $new_chartdata['mc_type'] = 0;
        $new_chartdata['mode'] = 1;
		$new_chartdata['isstaticimg'] = 1; $new_chartdata['color']=$params['color']?$params['color']:2;;
		//echo '<pre>';print_R($new_chartdata);
		$new_chartdata['ImageType'] = 2;
        //print_r($new_chartdata);exit;
		$echarts->dataview($new_chartdata, $this->t1dao);
         exit;
    }
    public function createimgtksfocus05($params)
	{
        //$priceidstr="188630,188611";
        $priceidstr="H98600";
        $new_chartdata['lendtitle'] = array('进口块矿溢价变化趋势图 ');
        $new_chartdata['ImageTitle'] = '进口块矿溢价变化趋势图';
        $priceidarr = explode(',', $priceidstr);
		$enddate = date('Y-m-d', time());
		$startdate = date('Y-m-d',strtotime("-1year", strtotime($enddate)));
		$shuju = $this->getpricebypricestr($priceidstr, $startdate, $enddate);
        //print_r($shuju);exit;
        
        //$new_chartdata['dw'] = array('元/吨','元/吨','元/吨');
        
        $infolist = $shuju['data'];
        $datearr = $shuju['date'];
        /*
        $array_hl = array();
        $sql="select rd1, rdate from steelhome_drc.rmbrate where rdate>='".$startdate." 00:00:00' and rdate<='".$enddate." 23:59:59' and rd1!='' order by rdate asc";
        $IndexList = $this->gcdao->query($sql);
        foreach ($IndexList as $v1) {
            $hl=$v1['rd1'];
            $rdate=$v1['rdate'];
            $date_key = date("y-m-d", strtotime($rdate));
            $array_hl[$date_key]=$hl/100;
        }
        //print_r($infolist);
        //print_r($array_hl);exit;
        $datearrnew=array();
         */
        $data=array();
        foreach($datearr as $k=>$v)
        {
            $datearrnew[]=$v;
            $data[0][]=$infolist[$v][$priceidarr[0]];
            // if(isset($infolist[$v][$priceidarr[0]])&&isset($infolist[$v][$priceidarr[1]])&& isset($array_hl[$v]))
            // {
            //     $datearrnew[]=$v;
            //     $data[0][]=round(($infolist[$v][$priceidarr[0]] - $infolist[$v][$priceidarr[1]])*$array_hl[$v]/(62*1.16));
            // }
            
        }
       
        //print_r($data);exit;
		$new_chartdata['data'] = $data;
        $new_chartdata['xlabel'] = $datearrnew;
		$new_chartdata['isbig'] = 0;
		include_once(APP_DIR . "/echarts/EChartsNewAction.inc.php");
		$echarts = new EChartsNewAction();
        $fn_1='tksindexfocus05.jpg';
		$new_chartdata['imgname'] = $fn_1;
		$new_chartdata['url'] = IMGURL . 'new/' . $fn_1;
		$new_chartdata['callbackname'] = $params['callbackname'];
		$new_chartdata['align'] = $params['align'];
        $new_chartdata['mc_type'] = 0;
        $new_chartdata['mode'] = 1;
		$new_chartdata['isstaticimg'] = 1; $new_chartdata['color']=$params['color']?$params['color']:2;;
		//echo '<pre>';print_R($new_chartdata);
		$new_chartdata['ImageType'] = 2;
        $new_chartdata['width'] = 528;
        $new_chartdata['height'] = 296;
        //print_r($new_chartdata);exit;
		$echarts->dataview($new_chartdata, $this->t1dao);
		exit;
    }



	function getpricebypricestr($pricestr, $stime, $etime)
    {
        //echo $time;
        $idnumber = explode(',', $pricestr);
        //echo count($idnumber);
        $six=$seven=array();
        foreach ($idnumber as $id) {
            if (strlen($id) == 6) {//判断id 的字符长度
                if (!in_array($id, $six)) {
                    $six[] = $id;
                }
            }
            if (strlen($id) == 7) {
                if (!in_array($id, $seven)) {
                    $seven[] = $id;
                }
            }
        }
        $sixid_str = implode("','", $six);
        $sevenid_str = implode("','", $seven);
        $mconmanagedate .= "(mconmanagedate>='" . $stime . " 00:00:00' and mconmanagedate<='" . $etime . " 23:59:59')";
        if ($sixid_str != '') {
            $PriceListSQL = " select marketconditions.price price,marketconditions.topicture, marketconditions.mconmanagedate from marketconditions
 		  	where    $mconmanagedate and  marketconditions.topicture in ('$sixid_str') order by marketconditions.mconmanagedate";
        }
        if ($sevenid_str != '') {
            $PriceListSQL = " select marketconditions.price,marketconditions.mastertopid as topicture, marketconditions.mconmanagedate from marketconditions
 		  	where $mconmanagedate and  marketconditions.mastertopid in ('$sevenid_str') order by marketconditions.mconmanagedate";
        }
        if ($sixid_str != '' && $sevenid_str != '') {
            $PriceListSQL = " (select marketconditions.price price,marketconditions.topicture, marketconditions.mconmanagedate from marketconditions  
	 		  where    $mconmanagedate and  marketconditions.topicture in ('$sixid_str') order by marketconditions.mconmanagedate)
	 		  UNION( select marketconditions.price,marketconditions.mastertopid as topicture,marketconditions.mconmanagedate from marketconditions 
	 		  where $mconmanagedate  and marketconditions.mastertopid in('$sevenid_str') order by marketconditions.mconmanagedate) ";
        }
        //echo $PriceListSQL;
        $PriceList = $this->_dao->query($PriceListSQL);
        $dataarr = array();//数据数组
        $datearr = array();//日期数组
        foreach ($PriceList as $v) {
            $date = date('y-m-d', strtotime($v['mconmanagedate']));
            if (!in_array($date, $datearr)) {
                $datearr[] = $date;
            }

            if (strstr($v['price'], "-")) {
                $avgprice = explode("-", $v['price']);
                $v['price'] = round(($avgprice['0'] + $avgprice['1']) / 2, 2);
            }
            $dataarr[$date][$v['topicture']] = $v['price'];

        }

        $data['date'] = $datearr;
        $data['data'] = $dataarr;

        return $data;
    }
    public function createimgbydatatable($params)
	{
        $new_chartdata['lendtitle'] = explode(',',$params['lendtitle']);
        $new_chartdata['ImageTitle'] =  $params['ImageTitle'];
        $enddate = date('Y-m-d', time());
		$startdate = date('Y-m-d',strtotime("-1year", strtotime($enddate)));
        $dataarr = array();//数据数组
        $datearr = array();//日期数组
        $filed=$params['filed'];
        $dta_type=$params['dta_type'];
        $dta_1=$params['dta_1'];
        $dtymd=$params['dtymd'];
        //$sql="select dta_3, dta_ym from steelhome_drc.data_table where dta_type = 'JKKCTJ_1'  and dta_1 = '合计' and dta_ym >='".$startdate."' and dta_ym <='".$enddate."' order by dta_ym asc";
        $sql="SELECT ". $filed.",dta_ym FROM steelhome_drc.`data_table` WHERE `dta_ym` >='".$startdate."' AND `dta_ym` <='".$enddate."' AND dta_type = '".$dta_type."' AND dta_1 = '".$dta_1."' order by dta_ym ASC ";
        $IndexList = $this->gcdao->query($sql);
        foreach ($IndexList as $v1) {
            if(empty($v1[$filed]))
            {
                continue;
            }
            $date =$dtymd!=3?date('y-m-d', strtotime($v1['dta_ym'])):date('y-m', strtotime($v1['dta_ym']));
            if (!in_array($date, $datearr)) {
                $datearr[] = $date;
            }
            $dataarr[0][] = $v1[$filed];
            //$dataarr[0][] = (float)$v1[$filed];
        }
        //print_r($dataarr);exit;
        $new_chartdata['data'] = $dataarr;
        $new_chartdata['xlabel'] = $datearr;
		$new_chartdata['isbig'] = 0;
		include_once(APP_DIR . "/echarts/EChartsNewAction.inc.php");
		$echarts = new EChartsNewAction();
        $fn_1=$params['constpicname'].'.jpg';
		$new_chartdata['imgname'] = $fn_1;
		$new_chartdata['url'] = IMGURL . 'new/' . $fn_1;
		$new_chartdata['callbackname'] = $params['callbackname'];
		$new_chartdata['align'] = $params['align'];
        $new_chartdata['mc_type'] = 0;
        $new_chartdata['mode'] = 1;
		$new_chartdata['isstaticimg'] = 1; $new_chartdata['color']=$params['color']?$params['color']:2;;
		//echo '<pre>';print_R($new_chartdata);
        //if($params['ImageType']==1)
        //{
            //$new_chartdata['color']=2;
       // }
		$new_chartdata['ImageType'] = $params['ImageType']?$params['ImageType']:2;
        //print_r($new_chartdata);exit;
		$echarts->dataview($new_chartdata, $this->t1dao);
         exit;
    }
    public function createimgbydatatablelist($params)
	{

        $new_chartdata['lendtitle'] = explode(',',$params['lendtitle']);
        $new_chartdata['ImageTitle'] =  $params['ImageTitle'];
        $enddate = date('Y-m-d', time());
		$startdate = date('Y-m-d',strtotime("-1year", strtotime($enddate)));
        $dataarr = array();//数据数组
        $datearr = array();//日期数组
        $filed='dta_2';
        $arr = explode(',', $params['dta_type']);
        $dta_1 = explode(',', $params['dta_1']);
        foreach($arr as $k=>$v){
            $sql="SELECT ". $filed.",dta_ym FROM steelhome_drc.`data_table` WHERE `dta_ym` >='".$startdate."' AND `dta_ym` <='".$enddate."' AND dta_type = '".$v."' AND dta_1 = '".$dta_1[$k]."' order by dta_ym ASC ";
            $IndexList = $this->gcdao->query($sql);
            foreach ($IndexList as $v1) {
                if(empty($v1[$filed]))
                {
                    continue;
                }
                $date = date('y-m-d', strtotime($v1['dta_ym']));
                if (!in_array($date, $datearr)) {
                    $datearr[] = $date;
                }
                $dataarr[$date][$k] = $v1[$filed];
            }
       }

        $data=array();
        foreach($datearr as $k=>$v)
        {
            foreach($arr as $k1=>$v1){
               $data[$k1][$k]=isset($dataarr[$v][$k1])?$dataarr[$v][$k1]:'-';
            }
        }
        //print_r($data);exit;
		$new_chartdata['data'] = $data;
        $new_chartdata['xlabel'] = $datearr;
		$new_chartdata['isbig'] = 0;
		include_once(APP_DIR . "/echarts/EChartsNewAction.inc.php");
		$echarts = new EChartsNewAction();
        $fn_1=$params['constpicname'].'.jpg';
		$new_chartdata['imgname'] = $fn_1;
		$new_chartdata['url'] = IMGURL . 'new/' . $fn_1;
		$new_chartdata['callbackname'] = $params['callbackname'];
		$new_chartdata['align'] = $params['align'];
        $new_chartdata['mc_type'] = 0;
        $new_chartdata['mode'] = 1;
		$new_chartdata['isstaticimg'] = 1; $new_chartdata['color']=$params['color']?$params['color']:2;;
		//echo '<pre>';print_R($new_chartdata);
		$new_chartdata['ImageType'] = 2;
        //print_r($new_chartdata);exit;
		$echarts->dataview($new_chartdata, $this->t1dao);
		exit;
    }

    public function createimgbyshpi_material($params)
	{

        $new_chartdata['lendtitle'] = explode(',',$params['lendtitle']);
        $new_chartdata['ImageTitle'] =  $params['ImageTitle'];
        $enddate = date('Y-m-d', time());
		$startdate = date('Y-m-d',strtotime("-1year", strtotime($enddate)));
        $dataarr = array();//数据数组
        $datearr = array();//日期数组
        $arr = explode(',', $params['priceidstr']);
        foreach($arr as $k=>$v){
            $sql="select dateday,mindex  from shpi_material where topicture = '$v' and dateday >='".$startdate."' and dateday <='".$enddate."' order by dateday asc";
            
            $IndexList = $this->_dao->query($sql);
            foreach ($IndexList as $v1) {
                $date = date('y-m-d', strtotime($v1['dateday']));
                if (!in_array($date, $datearr)) {
                    $datearr[] = $date;
                }
                $dataarr[$date][$k] = $v1['mindex'];
            }
       }

        $data=array();
        foreach($datearr as $k=>$v)
        {
            foreach($arr as $k1=>$v1){
               $data[$k1][$k]=isset($dataarr[$v][$k1])?$dataarr[$v][$k1]:'-';
            }
        }
        //print_r($data);exit;
		$new_chartdata['data'] = $data;
        $new_chartdata['xlabel'] = $datearr;
		$new_chartdata['isbig'] = 0;
		include_once(APP_DIR . "/echarts/EChartsNewAction.inc.php");
		$echarts = new EChartsNewAction();
        $fn_1=$params['constpicname'].'.jpg';
		$new_chartdata['imgname'] = $fn_1;
		$new_chartdata['url'] = IMGURL . 'new/' . $fn_1;
		$new_chartdata['callbackname'] = $params['callbackname'];
		$new_chartdata['align'] = $params['align'];
        $new_chartdata['mc_type'] = 0;
        $new_chartdata['mode'] = 1;
		$new_chartdata['isstaticimg'] = 1; $new_chartdata['color']=$params['color']?$params['color']:2;;
		//echo '<pre>';print_R($new_chartdata);
		$new_chartdata['ImageType'] = $params['ImageType']?$params['ImageType']:2;
        //print_r($new_chartdata);exit;
		$echarts->dataview($new_chartdata, $this->t1dao);
		exit;
    }
    public function createimgbyshpi_pp($params)
	{

        $new_chartdata['lendtitle'] = explode(',',$params['lendtitle']);
        $new_chartdata['ImageTitle'] =  $params['ImageTitle'];
        $enddate = date('Y-m-d', time());
		$startdate = date('Y-m-d',strtotime("-1year", strtotime($enddate)));
        $dataarr = array();//数据数组
        $datearr = array();//日期数组
        $arr = explode(',', $params['priceidstr']);
        foreach($arr as $k=>$v){
            $sql="select dateday,wpindex  from shpi_pp where bc_id = '$v' and dateday >='".$startdate."' and dateday <='".$enddate."' order by dateday asc";
            
            $IndexList = $this->_dao->query($sql);
            foreach ($IndexList as $v1) {
                $date = date('y-m-d', strtotime($v1['dateday']));
                if (!in_array($date, $datearr)) {
                    $datearr[] = $date;
                }
                $dataarr[$date][$k] = $v1['wpindex'];
            }
       }

        $data=array();
        foreach($datearr as $k=>$v)
        {
            foreach($arr as $k1=>$v1){
               $data[$k1][$k]=isset($dataarr[$v][$k1])?$dataarr[$v][$k1]:'-';
            }
        }
        //print_r($data);exit;
		$new_chartdata['data'] = $data;
        $new_chartdata['xlabel'] = $datearr;
		$new_chartdata['isbig'] = 0;
		include_once(APP_DIR . "/echarts/EChartsNewAction.inc.php");
		$echarts = new EChartsNewAction();
        $fn_1=$params['constpicname'].'.jpg';
		$new_chartdata['imgname'] = $fn_1;
		$new_chartdata['url'] = IMGURL . 'new/' . $fn_1;
		$new_chartdata['callbackname'] = $params['callbackname'];
		$new_chartdata['align'] = $params['align'];
        $new_chartdata['mc_type'] = 0;
        $new_chartdata['mode'] = 1;
		$new_chartdata['isstaticimg'] = 1; $new_chartdata['color']=$params['color']?$params['color']:2;;
		//echo '<pre>';print_R($new_chartdata);
		$new_chartdata['ImageType'] = 2;
        //print_r($new_chartdata);exit;
		$echarts->dataview($new_chartdata, $this->t1dao);
		exit;
    }
    public function createimgbyshpi_pzp($params)
	{

        $new_chartdata['lendtitle'] = explode(',',$params['lendtitle']);
        $new_chartdata['ImageTitle'] =  $params['ImageTitle'];
        $enddate = date('Y-m-d', time());
		$startdate = date('Y-m-d',strtotime("-1year", strtotime($enddate)));
        $dataarr = array();//数据数组
        $datearr = array();//日期数组
        $arr = explode(',', $params['priceidstr']);
        foreach($arr as $k=>$v){
            $sql="select dateday,wpindex  from shpi_pzp where bvm_id = '$v' and dateday >='".$startdate."' and dateday <='".$enddate."' order by dateday asc";
            
            $IndexList = $this->_dao->query($sql);
            foreach ($IndexList as $v1) {
                $date = date('y-m-d', strtotime($v1['dateday']));
                if (!in_array($date, $datearr)) {
                    $datearr[] = $date;
                }
                $dataarr[$date][$k] = $v1['wpindex'];
            }
       }

        $data=array();
        foreach($datearr as $k=>$v)
        {
            foreach($arr as $k1=>$v1){
               $data[$k1][$k]=isset($dataarr[$v][$k1])?$dataarr[$v][$k1]:'-';
            }
        }
        //print_r($data);exit;
		$new_chartdata['data'] = $data;
        $new_chartdata['xlabel'] = $datearr;
		$new_chartdata['isbig'] = 0;
		include_once(APP_DIR . "/echarts/EChartsNewAction.inc.php");
		$echarts = new EChartsNewAction();
        $fn_1=$params['constpicname'].'.jpg';
		$new_chartdata['imgname'] = $fn_1;
		$new_chartdata['url'] = IMGURL . 'new/' . $fn_1;
		$new_chartdata['callbackname'] = $params['callbackname'];
		$new_chartdata['align'] = $params['align'];
        $new_chartdata['mc_type'] = 0;
        $new_chartdata['mode'] = 1;
		$new_chartdata['isstaticimg'] = 1; $new_chartdata['color']=$params['color']?$params['color']:2;;
		//echo '<pre>';print_R($new_chartdata);
		$new_chartdata['ImageType'] = 2;
        //print_r($new_chartdata);exit;
		$echarts->dataview($new_chartdata, $this->t1dao);
		exit;
    }
    public function createimgfgfocus03($params)
	{
        
        $new_chartdata['lendtitle'] = array('螺纹钢','废钢','螺纹-废钢(右)');
        $new_chartdata['ImageTitle'] = '上海螺纹钢与张家港废钢价格对比';
       
		$enddate = date('Y-m-d', time());
		$startdate = date('Y-m-d',strtotime("-1year", strtotime($enddate)));
        $datearr = array();
        $sql="SELECT DATE,D1,D2,D3 FROM `L2Data` WHERE `L2Id` =542 and  DATE > '".$startdate."' and DATE < '".$enddate."'  order by DATE";
        $IndexList = $this->_dao->query($sql);
        $datearr=array();
        $data=array();
        foreach($IndexList as $k=>$v)
        {
            $date = date('y-m-d', strtotime($v['DATE']));
            if (!in_array($date, $datearr)) {
                $datearr[] = $date;
                $data[0][]=$v['D1'];
                $data[1][]=$v['D2'];
                $data[2][]=$v['D3'];
            }
            
        }
        $new_chartdata['zhou1'] = 0;
        $new_chartdata['zhou2'] = 0;
        $new_chartdata['zhou3'] = 1;
        $new_chartdata['dw'] = array('元/吨','元/吨','元/吨');
        //print_r($data);exit;
		$new_chartdata['data'] = $data;
        $new_chartdata['xlabel'] = $datearr;
		$new_chartdata['isbig'] = 0;
		include_once(APP_DIR . "/echarts/EChartsNewAction.inc.php");
		$echarts = new EChartsNewAction();
        $fn_1='fgindexfocus03.jpg';
		$new_chartdata['imgname'] = $fn_1;
		$new_chartdata['url'] = IMGURL . 'new/' . $fn_1;
		$new_chartdata['callbackname'] = $params['callbackname'];
		$new_chartdata['align'] = $params['align'];
        $new_chartdata['mc_type'] = 0;
        $new_chartdata['mode'] = 1;
		$new_chartdata['isstaticimg'] = 1; $new_chartdata['color']=$params['color']?$params['color']:2;;
		//echo '<pre>';print_R($new_chartdata);
		$new_chartdata['ImageType'] = 4;
        //print_r($new_chartdata);exit;
		$echarts->dataview($new_chartdata, $this->t1dao);
		exit;
    }
    public function createimgysfocus01($params)
	{
        
        $new_chartdata['lendtitle'] = array('LME 镍价');
        $new_chartdata['ImageTitle'] = 'LME 镍价走势图';
       
		$enddate = date('Y-m-d', time());
		$startdate = date('Y-m-d',strtotime("-1year", strtotime($enddate)));
        $sql="select mvalue,datetime from steelhome_drc.lme where bianma='010106' and datetime>='$startdate' and datetime<='$enddate' order by datetime ";
        $IndexList = $this->gcdao->query($sql);
        $datearr=array();
        $data=array();
        foreach($IndexList as $k=>$v)
        {
            $date = date('y-m-d', strtotime($v['datetime']));
            if (!in_array($date, $datearr)) {
                $datearr[] = $date;
                $data[0][]=$v['mvalue'];
            }
            
        }
		$new_chartdata['data'] = $data;
        $new_chartdata['xlabel'] = $datearr;
		$new_chartdata['isbig'] = 0;
		include_once(APP_DIR . "/echarts/EChartsNewAction.inc.php");
		$echarts = new EChartsNewAction();
        $fn_1='ysindexfocus01.jpg';
		$new_chartdata['imgname'] = $fn_1;
		$new_chartdata['url'] = IMGURL . 'new/' . $fn_1;
		$new_chartdata['callbackname'] = $params['callbackname'];
		$new_chartdata['align'] = $params['align'];
        $new_chartdata['mc_type'] = 0;
        $new_chartdata['mode'] = 1;
		$new_chartdata['isstaticimg'] = 1; $new_chartdata['color']=$params['color']?$params['color']:2;;
		//echo '<pre>';print_R($new_chartdata);
		$new_chartdata['ImageType'] = 2;
        //print_r($new_chartdata);exit;
		$echarts->dataview($new_chartdata, $this->t1dao);
		exit;
    }
    public function createimgbyshpi_img8($params)
	{
        $new_chartdata['lendtitle'] = explode(',',$params['lendtitle']);
        $new_chartdata['ImageTitle'] =  $params['ImageTitle'];
        $type=$params['type']?$params['type']:1;
        $enddate = date('Y-m-d', time());
		$startdate = date('Y-01-01',strtotime("-2year", strtotime($enddate)));
        $dataarr = array();//数据数组
        $datearr = array();//日期数组
        //$arr = explode(',', $params['priceidstr']);
        if($type==1)
        {
            $new_chartdata['dw'] = array('元/吨','元/吨','元/吨');
            $sql="select dateday ,price   from shpi_material where topicture='3' and dateday >='".$startdate."' and dateday <='".$enddate."' order by dateday asc";
        }
        elseif($type==2)
        {
            $new_chartdata['dw'] = array('元/吨','元/吨','元/吨');
            $sql="select dateday ,weiprice as price  from shpi_mj_pzp where vid=0 AND type=0 and dateday >='".$startdate."' and dateday <='".$enddate."' order by dateday asc";
        }
        elseif($type==3)
        {
            $new_chartdata['dw'] = array('%');
            $sql="select Date as dateday,KaiGonglv2 as price  from steelhome_gc.sc_KaiGongLvHuiZong where AreaCode=0 and Date >='".$startdate."' and Date <='".$enddate."' order by dateday asc";
        }
        elseif($type==4)
        {
            $new_chartdata['dw'] = array('%');
            $sql="select dta_ym as dateday,dta_2 as price  from steelhome_drc.data_table where dta_type='GC_KGL' and dta_1='全国' and dta_ym >='".$startdate."' and dta_ym <='".$enddate."' order by dateday asc";
        }
        elseif($type==5)
        {
            $new_chartdata['dw'] = array('元/吨','元/吨','元/吨');
            $sql="select dateday ,weiprice as price  from shpi_mj_pzp where vid=0 AND type=1 and dateday >='".$startdate."' and dateday <='".$enddate."' order by dateday asc";
        }
        elseif($type==6)
        {
            $new_chartdata['dw'] = array('元/吨','元/吨','元/吨');
            $sql="select dateday ,weiprice as price  from shpi_mj_pzp where vid=0 AND type=3 and dateday >='".$startdate."' and dateday <='".$enddate."' order by dateday asc";
        }
        elseif($type==7)//进口矿
        {
            $new_chartdata['dw'] = array('美元/吨');
            $sql="select dateday ,weipriceusb as price  from shpi_material_pzp where vid=3 and dateday >='".$startdate."' and dateday <='".$enddate."' order by dateday asc";
        }
        elseif($type==8)//废钢
        {
            $new_chartdata['dw'] = array('元/吨');
            $sql="select dateday ,price  from shpi_material where topicture='4' and dateday >='".$startdate."' and dateday <='".$enddate."' order by dateday asc";
        }
        elseif($type==9)//钢坯
        {
            $new_chartdata['dw'] = array('元/吨');
            $sql="select dateday ,round(weiprice) as price  from shpi_pp where bc_id=11 and dateday >='".$startdate."' and dateday <='".$enddate."' order by dateday asc";
        }
        if(in_array($type,array('1','2','5','6','7','8','9')))
        {
            $IndexList = $this->_dao->query($sql);
        }
        else
        {
            $IndexList = $this->gcdao->query($sql);
        }

       
        foreach ($IndexList as $v1) {
            $date = date('Y-m-d', strtotime($v1['dateday']));
            if (!in_array($date, $datearr)) {
                $datearr[] = $date;
                $dataarr[0][] = $v1['price'];
            }
            
        }
       
        //print_r($data);exit;
		$new_chartdata['data'] = $dataarr;
        $new_chartdata['xlabel'] = $datearr;
		$new_chartdata['isbig'] = 0;
		include_once(APP_DIR . "/echarts/EChartsNewAction.inc.php");
		$echarts = new EChartsNewAction();
        $fn_1=$params['constpicname'].'.jpg';
		$new_chartdata['imgname'] = $fn_1;
		$new_chartdata['url'] = IMGURL . 'new/' . $fn_1;
		$new_chartdata['callbackname'] = $params['callbackname'];
		$new_chartdata['align'] = $params['align'];
        $new_chartdata['mc_type'] = 0;
        $new_chartdata['mode'] = 1;
		$new_chartdata['isstaticimg'] = 1; //$new_chartdata['color']=2;
		//echo '<pre>';print_R($new_chartdata);
		$new_chartdata['ImageType'] = 8;
        $_GET['ImageType']="8";
        //$new_chartdata['ChartExt'] = array('isfestival'=>0,'Type'=>0,'DateStr'=>'2021,2023');
        //print_r($new_chartdata);exit;
		$echarts->dataview($new_chartdata, $this->t1dao);
		exit;
    }
    public function createimgbyhwgs($params)
	{

        $type=$params['type']?$params['type']:1;
        $enddate = date('Y-m-d', time());
		$startdate = date('Y-m-d',strtotime("-1year", strtotime($enddate)));
        $dataarr = array();//数据数组
        $datearr = array();//日期数组
        if($type==1)
        {
            $fn_1='hwgsfocus01.jpg';
            $new_chartdata['lendtitle'] = array('冷轧板卷','热轧板卷','螺纹钢');
            $new_chartdata['ImageTitle'] = '中国钢材出口报价';
            $sql = " select Price as value,Date as date,PinMing as type from steelhome_drc.ChinaGangCaiExportPriceInfo where Date>='$startdate' and Date <= '$enddate' and  PinMing in ('冷轧板卷','热轧板卷','螺纹钢') order by Date";
        }
        elseif($type==2)
        {
            $fn_1='hwgsfocus02.jpg';
            $new_chartdata['lendtitle'] = array('冷轧卷','热轧板卷(商品)','厚板');
            $new_chartdata['ImageTitle'] = '美国国内钢材基准价';
            $sql = "select dta_2 as value, dta_ym as date,dta_1 as type  from steelhome_drc.data_table where dta_type='GJGCJG_8'  and dta_ym>='$startdate' and dta_ym <= '$enddate' and dta_1 in ('冷轧卷','热轧板卷(商品)','厚板') order by dta_ym";
        }
        elseif($type==3)
        {
            $fn_1='hwgsfocus03.jpg';
            $new_chartdata['lendtitle'] = array('冷轧板卷','热轧板卷','镀锌板');
            $new_chartdata['ImageTitle'] = '欧盟区内钢材价格';
            $sql = "select dta_2 as value, dta_ym as date, dta_1 as type from steelhome_drc.data_table where dta_type='GJGCJG_11'  and dta_ym>='$startdate' and dta_ym <= '$enddate' and dta_1 in ('冷轧板卷','热轧板卷','镀锌板') order by dta_ym";
        }
        elseif($type==4)
        {
            $fn_1='hwgsfocus04.jpg';
            $new_chartdata['lendtitle'] = array('冷轧卷板','热轧卷板','中厚板');
            $new_chartdata['ImageTitle'] = '日本钢材出口报价';
            $sql = "select dta_3 as value, dta_ym as date, dta_1 as type from steelhome_drc.data_table where dta_type='GJGCJG_1' and dta_ym>='$startdate' and dta_ym <= '$enddate' and dta_1 in ('冷轧卷板','热轧卷板','中厚板') order by dta_ym";
        }
        $IndexList = $this->gcdao->query($sql);
        foreach ($IndexList as $v1) {
            if(empty($v1['value']))
            {
                continue;
            }
            $date = date('y-m-d', strtotime($v1['date']));
            if (!in_array($date, $datearr)) {
                $datearr[] = $date;
            }
            $dataarr[$date][array_search($v1['type'], $new_chartdata['lendtitle'])] = $v1['value'];
        }
        $data=array();
        foreach($datearr as $k=>$v)
        {
            foreach($new_chartdata['lendtitle'] as $k1=>$v1){
               $data[$k1][$k]=isset($dataarr[$v][$k1])?$dataarr[$v][$k1]:'-';
            }
        }
        //print_r($data);exit;
		$new_chartdata['data'] = $data;
        $new_chartdata['xlabel'] = $datearr;
		$new_chartdata['isbig'] = 0;
		include_once(APP_DIR . "/echarts/EChartsNewAction.inc.php");
		$echarts = new EChartsNewAction();
        //$fn_1=$params['constpicname'].'.jpg';
		$new_chartdata['imgname'] = $fn_1;
		$new_chartdata['url'] = IMGURL . 'new/' . $fn_1;
		$new_chartdata['callbackname'] = $params['callbackname'];
		$new_chartdata['align'] = $params['align'];
        $new_chartdata['mc_type'] = 0;
        $new_chartdata['mode'] = 1;
		$new_chartdata['isstaticimg'] = 1; $new_chartdata['color']=$params['color']?$params['color']:2;;
		//echo '<pre>';print_R($new_chartdata);
		$new_chartdata['ImageType'] = 2;
        //print_r($new_chartdata);exit;
		$echarts->dataview($new_chartdata, $this->t1dao);
		exit;
    }
    public function createimgbyzhzx($params)
	{

        $type=$params['type']?$params['type']:1;
        $enddate = date('Y-m-d', time());
		$startdate = date('Y-m-d',strtotime("-1year", strtotime($enddate)));
        $dataarr = array();//数据数组
        $datearr = array();//日期数组
        if($type==1)
        {
            $fn_1='zhzxfocus01.jpg';
            $new_chartdata['lendtitle'] = array('100美元兑人民币');
            $new_chartdata['ImageTitle'] = '美元对人民币汇率中间价';
            $sql = " select rd1 as value,rdate as date  from steelhome_drc.rmbrate where rdate>='$startdate' and rdate <= '$enddate'  order by rdate";
        }
        elseif($type==2)
        {
            $fn_1='zhzxfocus02.jpg';
            $new_chartdata['lendtitle'] = array('长三角');
            $new_chartdata['ImageTitle'] = '商业银行六个月大额承兑贴现率';
            $sql = "select rd1 as value, rdate as date from steelhome_drc.busbankrate where  rdate>='$startdate' and rdate <= '".$enddate." 23:59:59'  order by rdate";
        }
        elseif($type==3)
        {
            $fn_1='zhzxfocus03.jpg';
            $new_chartdata['lendtitle'] = array('收盘价');
            $new_chartdata['ImageTitle'] = '美元指数';
            $sql = "select rd2 as value, rdate as date from steelhome_drc.dolrate where  rdate>='$startdate' and rdate <= '".$enddate." 23:59:59'  order by rdate";
        }
        elseif($type==4)
        {
            $fn_1='zhzxfocus04.jpg';
            $new_chartdata['lendtitle'] = array('BDI');
            $new_chartdata['ImageTitle'] = '波罗地海干散货（BDI)';
            $sql = "select mvalue as value, datetime as date from steelhome_drc.gfutures_shpi where bianma=******** and mvalue !='' and mvalue_zd !='' and datetime>='$startdate' and datetime <= '".$enddate." 23:59:59'  order by datetime";
        }
        $IndexList = $this->gcdao->query($sql);
        foreach ($IndexList as $v1) {
            if(empty($v1['value']))
            {
                continue;
            }
            $date = date('y-m-d', strtotime($v1['date']));
            if (!in_array($date, $datearr)) {
                $datearr[] = $date;
            }
            $dataarr[0][$date] = $v1['value'];
        }
        //print_r($data);exit;
		$new_chartdata['data'] = $dataarr;
        $new_chartdata['xlabel'] = $datearr;
		$new_chartdata['isbig'] = 0;
		include_once(APP_DIR . "/echarts/EChartsNewAction.inc.php");
		$echarts = new EChartsNewAction();
        //$fn_1=$params['constpicname'].'.jpg';
		$new_chartdata['imgname'] = $fn_1;
		$new_chartdata['url'] = IMGURL . 'new/' . $fn_1;
		$new_chartdata['callbackname'] = $params['callbackname'];
		$new_chartdata['align'] = $params['align'];
        $new_chartdata['mc_type'] = 0;
        $new_chartdata['mode'] = 1;
		$new_chartdata['isstaticimg'] = 1; $new_chartdata['color']=$params['color']?$params['color']:2;;
		//echo '<pre>';print_R($new_chartdata);
		$new_chartdata['ImageType'] = 2;
        //print_r($new_chartdata);exit;
		$echarts->dataview($new_chartdata, $this->t1dao);
		exit;
    }
    public function createimgqhfocus($params)
	{
        $type=$params['type']?$params['type']:1;
        
        $enddate = date('Y-m-d', time());
		$startdate = date('Y-m-d',strtotime("-1year", strtotime($enddate)));
        $dataarr = array();//数据数组
        $datearr = array();//日期数组
        if($type==1||$type==11)
        {
            $new_chartdata['lendtitle'] = array('螺纹钢期货','上海20mmHRB400螺纹钢');
            $new_chartdata['ImageTitle'] = '螺纹钢期货与上海螺纹钢价格走势图';
            $dta_type='SHQHDAY_4';
            $priceid='072023';
        }
        elseif($type==2||$type==12)
        {
            $new_chartdata['lendtitle'] = array('热轧板卷期货','上海5.75*1500热轧板卷');
            $new_chartdata['ImageTitle'] = '热轧板卷期货与上海热扎板卷价格走势图';
            $dta_type='SHQHDAY_99';
            $priceid='073112';
        }
        elseif($type==3||$type==13)
        {
            $new_chartdata['lendtitle'] = array('铁矿石期货','青岛PB粉矿');
            $new_chartdata['ImageTitle'] = '铁矿石期货与青岛PB粉矿价格走势图';
            $dta_type='SHQHDAY_20';
            $priceid='1886103';
        }
        elseif($type==4||$type==14)
        {
            $new_chartdata['lendtitle'] = array('焦炭期货','天津港焦炭');
            $new_chartdata['ImageTitle'] = '焦炭期货与天津港焦炭价格走势图';
            $dta_type='SHQHDAY_9';
            $priceid='408311';
        }
        elseif($type==5||$type==15)
        {
            $new_chartdata['lendtitle'] = array('焦煤期货','山西地区焦煤');
            $new_chartdata['ImageTitle'] = '焦煤期货与山西地区焦煤价格走势图';
            $dta_type='SHQHDAY_19';
            $priceid='S26310';
        }
        elseif($type==6||$type==16)
        {
            $new_chartdata['lendtitle'] = array('焦煤期货','山西地区焦煤');
            $new_chartdata['ImageTitle'] = '焦煤期货与秦皇岛动力煤价格走势图';
            $dta_type='SHQHDAY_19';
            $priceid='996320';
        }
        elseif($type==7||$type==17)
        {
            $new_chartdata['lendtitle'] = array('硅铁期货','甘肃硅铁');
            $new_chartdata['ImageTitle'] = '硅铁期货与甘肃硅铁价格走势图';
            $dta_type='SHQHDAY_22';
            $priceid='578710';
        }
        elseif($type==8||$type==18)
        {
            $new_chartdata['lendtitle'] = array('硅锰期货','贵州硅锰 FeMn65Si17 ');
            $new_chartdata['ImageTitle'] = '硅锰期货与贵阳市场硅锰价格走势图';
            $dta_type='SHQHDAY_23';
            $priceid='548910';
        }

        $sql="SELECT `dta_6`,dta_ym FROM steelhome_drc.`data_table` WHERE  `dta_type`='".$dta_type."' and dta_ym>='".$startdate."' and dta_ym<='".$enddate."' and dta_maxValStatus=1";
        $IndexList = $this->gcdao->query($sql);
        foreach ($IndexList as $v1) {
            $date = date('y-m-d', strtotime($v1['dta_ym']));
            if (!in_array($date, $datearr)) {
                $datearr[] = $date;
            }
            $dataarr[$date][0] = $v1['dta_6'];
        }
        
        if(strlen($priceid)==6)
        {
            $sql="select marketconditions.price price,marketconditions.topicture, marketconditions.mconmanagedate from marketconditions
            where    (mconmanagedate>='" . $startdate . " 00:00:00' and mconmanagedate<='" . $enddate . " 23:59:59') and  marketconditions.topicture ='".$priceid."' order by marketconditions.mconmanagedate";
        }
        else
        {
            $sql="select marketconditions.price price,marketconditions.topicture, marketconditions.mconmanagedate from marketconditions
            where    (mconmanagedate>='" . $startdate . " 00:00:00' and mconmanagedate<='" . $enddate . " 23:59:59') and  marketconditions.mastertopid ='".$priceid."' order by marketconditions.mconmanagedate";
        }
        
            
        
        $IndexList = $this->_dao->query($sql);
        foreach ($IndexList as $v1) {
            $date = date('y-m-d', strtotime($v1['mconmanagedate']));
            if (!in_array($date, $datearr)) {
                $datearr[] = $date;
            }
            $dataarr[$date][1] = $v1['price'];
        }
        asort($datearr);
        $data=array();
        foreach($datearr as $k=>$v)
        {
            $data[0][]=isset($dataarr[$v][0])?$dataarr[$v][0]:"'-'";
            $data[1][]=isset($dataarr[$v][1])?$dataarr[$v][1]:"'-'";
        }
        //print_r()
        $new_chartdata['data'] = $data;
        $new_chartdata['xlabel'] = $datearr;
		$new_chartdata['isbig'] = 0;
		include_once(APP_DIR . "/echarts/EChartsNewAction.inc.php");
		$echarts = new EChartsNewAction();
        $fn_1='qhindexfocus0'.$type.'.jpg';
		$new_chartdata['imgname'] = $fn_1;
		$new_chartdata['url'] = IMGURL . 'new/' . $fn_1;
		$new_chartdata['callbackname'] = $params['callbackname'];
		$new_chartdata['align'] = $params['align'];
        $new_chartdata['mc_type'] = 0;
        $new_chartdata['mode'] = 1;

        
        if($type<10)
        {
            $new_chartdata['width'] = 456;
            $new_chartdata['height'] = 358;
        }
		$new_chartdata['isstaticimg'] = 1; $new_chartdata['color']=$params['color']?$params['color']:2;;
		//echo '<pre>';print_R($new_chartdata);
		$new_chartdata['ImageType'] = 2;
        //print_r($new_chartdata);exit;
		$echarts->dataview($new_chartdata, $this->t1dao);
		exit;
    }
}
?>
