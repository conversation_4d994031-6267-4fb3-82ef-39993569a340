<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );

class dcfocusimgController extends AbstractController{

  public function __construct(){
    parent::__construct();
        $this->_action->setDao(new dcfocusimgDao('91R'));
        $this->_action->t1dao=( new dcfocusimgDao( "MAIN" ) );
      $this->_action->gcdao = new dcfocusimgDao('GC');
      
  }
  public function do_createimg()
  {
        $this->_action->createimg($this->_request);
  }
  public function do_createindexpicfocus01()
  {
        $this->_action->createindexpicfocus01($this->_request);
  }
  public function do_createimgyl()
  {
        $this->_action->createimgyl($this->_request);
  }
  public function do_createimgllfocus2()
  {
        $this->_action->createimgllfocus2($this->_request);
  }
  public function do_createimgtksfocus03()
  {
        $this->_action->createimgtksfocus03($this->_request);
  }
  public function do_createimgtksfocus04()
  {
        $this->_action->createimgtksfocus04($this->_request);
  }
  public function do_createimgtksfocus05()
  {
        $this->_action->createimgtksfocus05($this->_request);
  }
  public function do_createimgbydatatable()
  {
        $this->_action->createimgbydatatable($this->_request);
  }
  public function do_createimgbydatatablelist()
  {
        $this->_action->createimgbydatatablelist($this->_request);
  }
  public function do_createimgbyshpi_material()
  {
        $this->_action->createimgbyshpi_material($this->_request);
  }
  public function do_createimgbyshpi_pp()
  {
        $this->_action->createimgbyshpi_pp($this->_request);
  }
  public function do_createimgbyshpi_pzp()
  {
        $this->_action->createimgbyshpi_pzp($this->_request);
  }
  public function do_createimgfgfocus03()
  {
        $this->_action->createimgfgfocus03($this->_request);
  }
  public function do_createimgysfocus01()
  {
        $this->_action->createimgysfocus01($this->_request);
  }
  public function do_createimgbyshpi_img8()
  {
        $this->_action->createimgbyshpi_img8($this->_request);
  }
  public function do_createimgbyhwgs()
  {
        $this->_action->createimgbyhwgs($this->_request);
  }
  public function do_createimgbyzhzx()
  {
        $this->_action->createimgbyzhzx($this->_request);
  }
  public function do_createimgqhfocus()
  {
        $this->_action->createimgqhfocus($this->_request);
  }
  
  
  
}
?>