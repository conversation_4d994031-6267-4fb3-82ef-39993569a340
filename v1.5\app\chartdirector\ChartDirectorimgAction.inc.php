<?php

class ChartDirectorimgAction extends AbstractAction{

  public $oadao;

  public function __construct(){
    parent::__construct();    
  }

  	private function isrn($arr,$isjia1=0)  //是否存在瑞年
	{
       $cunzai=2;
		foreach($arr as $year)
		{
			if($isjia1==1)
			{
				$year++;
			}
			if(($year%4==0&&$year%100!=0)||($year%400==0))
			{
				$cunzai=1;
			   break;
			}
		}
       return  $cunzai;
	}
	   public function  finddate($array, $date)
	{
      
		//$count = 0;
		foreach($array as $day)
		{
			//echo $day."<br/>";
			//$interval[$count] = abs(strtotime($date) - strtotime($day));
			$interval[] = abs(strtotime($date) - strtotime($day));
			//$count++;
		}

		asort($interval);
		$closest = key($interval);

		//echo $array[$closest];

		$data['date']=$array[$closest];
		$data['key']=$closest;
        return $data;
	}
  
	public function linechart($params,$mes,$dao) {  
		   $isEnglish=$params["isEnglish"];
		   if($isEnglish){
			   $mes=EN_DATASOURCEMES;
			   $junzhi="AVG";
		   }else{
			   $mes=CN_DATASOURCEMES;
			   $junzhi="均值";
		   }
	        
			require_once("/usr/local/www/libs/ChartDirector/lib/phpchartdir.php");
	        if($_GET['debug']==2){print"<pre>"; print_r($params);exit;}
	        //echo 'dddd';
			# The data for the line chart
			$n=count($params['data']);
			# The labels for the line chart
			//$labels=$params['xlabel'];
			$ytwoflag=$params['twoy'];
			$lendtitle=$params['lendtitle'];
			$yaxistitle=$params['yaxistitle'];
			$labels=$params['xlabel'];
			
			if($_GET['ImageType']=="8"&&$n==1)
			{    $params['datanew']=array();
				foreach($params['data'][0] as $o=>$d)
				{
					$params['datanew'][]=$d;
				}
				
				$params['data'][0]=$params['datanew'];
				
			}
			//print_R($params['data']);
			
            if($params['isjz']=="1")  //判断均值是否显示 add by zhangcun 2016/8/11
		   {
			  $avg="";
			  $avg_print=array();
              for($i=0;$i<$n;$i++)
			  {
				  $avg[$i]=round(array_sum($params['data'][$i])/count($params['data'][$i]),2);
				  $avg_print[$i]="  ".$junzhi.":".$avg[$i];
			  }
		   }
		   
		   //取配色方案
			$linecolor=$this->getcolor($params,$dao);
		
			
			if($shape=='big'){
			    //图片长宽
				$pic1_1=600;
				$pic1_2=350;
				 //图表长宽高
				$pic2_1=55;//左间距
				$pic2_2=68;//距离顶部
				$pic2_3=460;//宽
				$pic2_4=200;//长
				//钢之家数据来源那段话位置
				$pic3_1=415;//左间距
				$pic3_2=45;//距离顶部
				$pic3_font=10;//字体大小
			   //线条名的位置
				$pic4_1=50;//左间距
				$pic4_2=290;//距离顶部
				$pic4_font=10;//字体大小
				//轴上字大小
                $xy_font=10;
				//标题大小
				$title_font=15;
			}else if($shape=='small'){
			  
			}else{
				//图片长宽
				$pic1_1=485;
				$pic1_2=279;
				//图表长宽高
				$pic2_1=50;//左间距
				$pic2_2=50;//距离顶部
				$pic2_3=380;//宽
				$pic2_4=160;//长
				//钢之家数据来源那段话位置
				$pic3_1=370;//左间距
				$pic3_2=30;//距离顶部
				$pic3_font=8.5;//字体大小
				//线条名的位置
				$pic4_1=40;//左间距
				$pic4_2=230;//距离顶部
				$pic4_font=8.5;//字体大小
				//轴上字大小
				$xy_font=9;
				//标题大小
                $title_font=13;
			}





			$c = new XYChart($pic1_1, $pic1_2);//369*198
			$c->setDefaultFonts(CREATE_IMG_TTF_PATH);
			# Add a title to the chart using 18 pts Times Bold Italic font
			$c->addTitle($params['charttitle'], CREATE_IMG_TTF_PATH, $title_font);	
		
		    $plotarea = $c->setPlotArea($pic2_1, $pic2_2, $pic2_3,$pic2_4, 0xffffff, 0xffffff, 0xe9e9e9, 0xc0c0c0);
				
			//$plotarea = $c->setPlotArea(50, 90, 500, 200, 0xeeeeee, 0xffffff, 0xe9e9e9, 0xc0c0c0);
	
			if($params['isbg'] == "1"){}else{
 			// $plotarea->setBackground2(dirname(__FILE__)."/../../images/".BG_IMG);
			}
 			$textBoxObj = $c->addText($pic3_1, $pic3_2, $mes, CREATE_IMG_TTF_PATH, $pic3_font, 0x000000);
 			$textBoxObj->setAlignment(TopRight);
 			
			
			$legendObj = $c->addLegend($pic4_1, $pic4_2, false, CREATE_IMG_TTF_PATH, $pic4_font);
			$legendObj->setBackground(Transparent);
			# Set the x axis labels
			//$c->xAxis->setLabels($labels);
			//$c->xAxis->setLabelStep(2);
			# Set axis label style to 8pts Arial Bold
			$c->xAxis->setLabelStyle(CREATE_IMG_TTF_PATH, $xy_font,TextColor);
			$c->yAxis->setLabelStyle(CREATE_IMG_TTF_PATH, $xy_font);
			# Set axis line width to 2 pixels
			$c->xAxis->setWidth(1);
			$c->yAxis->setWidth(1);
			// $c->setUseYAxis2->setWidth(2);
			# Add a line layer to the chart
			$i=0;
			//exit;
			$dmin=array(); 
			$dmax=array();
			$datat=array();
			$minnum=array();
			$maxnum=array();

			for($i=0;$i<$n;$i++)
			{
				$layer="layer".$i;
				$$layer = $c->addLineLayer2();
				$$layer->setLineWidth(2);
				$datat=$params['data'][$i];
				$$layer->addDataSet($datat,$linecolor[$i], $this->getmytitle($lendtitle[$i].$avg_print[$i],$i));
			}

			foreach($params['data'] as $i=>$v)
		   {
				$dmin[$i]=$params['data'][$i][0];
				$dmax[$i]=$params['data'][$i][0];
				$minnum[$i]=0;
				$maxnum[$i]=0;
				foreach($v as $key=>$val)
			   {
                  if($val<$dmin[$i]){ 
					  $dmin[$i]=$val;
					  $minnum[$i]=$key;
				  }
				  if($val>$dmax[$i]){
					  $dmax[$i]=$val;
					  $maxnum[$i]=$key;
				  }
			   }
		   }
			//print"<pre>"; print_R($dmin);
			//print"<pre>"; print_R($dmax);
			//$countlabel=count($params['xlabel'])-1;
			//$stepcount=round($countlabel/6,0);
			//$stepcount=$countlabel/7;
			//$c->xAxis->setLabelStep($stepcount);
            //$c->xAxis->addMark($countlabel,0xffffff,$params['xlabel'][$countlabel]);
			if(count($labels)>=50){
			// $ii=count($labels)/4;
			// $c->xAxis->addlabel(0,$labels[0]);
			// for($i=0;$i<4;$i++){
	        //     $c->xAxis->addlabel(($i*$ii),$labels[($i*$ii)]);
			// }
			
			// $c->xAxis->addlabel(count($labels)-1,$labels[count($labels)-1]);
			$c->xAxis->setLabels($labels);
			$countlabel=count($labels);
			$stepcount=round($countlabel/4,0);
			$c->xAxis->setLabelStep($stepcount);
			}
			 elseif(count($labels)<=12&&$params['bar_datetype']['data']<=3&&$params['issametype']==1){
                foreach($labels as $id=>$val){
				    $c->xAxis->addLabel($id,$val);
				  }
	         }else{
				 $c->xAxis->setLabels($labels);
                 $countlabel=count($labels);
				 $stepcount=round($countlabel/4,0);
				 $c->xAxis->setLabelStep($stepcount);
			  /*$c->xAxis->setLabels($labels);
              $countlabel=count($labels);
			  //$stepcount=round($countlabel/6,0);
			  $stepcount=$countlabel/7;
			  $c->xAxis->setLabelStep($stepcount);
			  */
			}
			// 设置Y轴最大最小值
// 			$min = ceil((min($dmin)-0.10*min($dmin))/10)*10;
// 			$max = ceil((max($dmax)+0.10*max($dmax))/10)*10;
// 			$c->yAxis->setLinearScale($min,$max);
            /*正负数y轴上下限处理*/
	
	if(10*($this->getmax($dmax)-$this->getmin($dmin))>$this->getmax($dmax)){		
			if($this->getmin($dmin)>0){
	           $mina = 0.95*$this->getmin($dmin);
              }else{
	             $mina = 1.05*$this->getmin($dmin);
              }
            if($this->getmax($dmax)>0){
	           $maxa = 1.05*$this->getmax($dmax);
               }else{
	           $maxa = 0.95*$this->getmax($dmax);
              }
	}else{
	        $i=(float)$this->getmax($dmax);       //add by zhangcun  for 线图上下限问题 2016/9/19
			$j=(float)$this->getmin($dmin);
			$k=$i-$j;
			if($i-1>0&&$j-1>0){
				$min=(int)$j-(int)($k/5.0);
				$max=(int)$i+(int)($k/5.0);
				$mina=$min-1.0;
				$maxa=$max+1.0;
			}else{								//add by zhangcun 2017/7/19 注：对小于一的浮点数据重新处理，上下浮动五分之一，然后保留几位有效小数 
				$min=(float)$j-(float)($k/5.0);
				$max=(float)$i+(float)($k/5.0);
				$mina=round($min/1.0,$this->getwei($min));
				$maxa=round($max/1.0,$this->getwei($max));
			}
																				
			if($j>=0&&$mina<0) $mina=0;
			if($i<=0&&$maxa>0) $maxa=0;
	 }
            /*正负数y轴上下限处理*/
			$c->yAxis->setLinearScale($mina,$maxa);
			//add by zhangcun for 峰谷值 started  2016/11/11
			if($params['isfg']==1){
				$xstart=$pic2_1; //横轴起始点
				$ystart=$pic2_4+$pic2_2; //纵轴起始点
				$xlen=$pic2_3; //X轴长度
				$ylen=$pic2_4; //Y轴长度
				$jingdu=2;  //最小值最大值保留小数位

                               //一条线标记为黑色，多条线为了区分改为与线相同的颜色
							   //为方便调整，峰值和谷值略有不同，可以加数字进行微调
			  if($n==1) {
					$c->addText(round($xstart+$xlen*$minnum[0]/count($params['xlabel']),0),round($ystart-($ylen*($dmin[0]-$mina)/($maxa-$mina)),0),round($dmin[0],$jingdu),CREATE_IMG_TTF_PATH,9, 0x000000);
					$c->addText(round($xstart+$xlen*$maxnum[0]/count($params['xlabel']),0),round($ystart-($ylen*($dmax[0]-$mina)/($maxa-$mina)),0),round($dmax[0],$jingdu),CREATE_IMG_TTF_PATH,9, 0x000000);

			  }else{
				for($i=0;$i<$n;$i++){
					$c->addText(round($xstart+$xlen*$minnum[$i]/count($params['xlabel']),0),round($ystart-($ylen*($dmin[$i]-$mina)/($maxa-$mina)),0),round($dmin[$i],$jingdu),CREATE_IMG_TTF_PATH,9, $linecolor[$i]);
					$c->addText(round($xstart+$xlen*$maxnum[$i]/count($params['xlabel']),0),round($ystart-($ylen*($dmax[$i]-$mina)/($maxa-$mina)),0),round($dmax[$i],$jingdu),CREATE_IMG_TTF_PATH,9, $linecolor[$i]);
				}
			  }
			}
			//add by zhangcun for 峰谷值 ended  2016/11/11
			# Configure the bars within a group to touch each others (no gap)
			// $layer1->setBarGap(0.2, TouchBar);
			//print_r($yaxistitle);
			//$textBoxObj = $c->yAxis->setTitle(iconv("GB2312", "UTF-8",$yaxistitle[0]),CREATE_IMG_TTF_PATH);
			$textBoxObj = $c->yAxis->setTitle($params['dw'][0],CREATE_IMG_TTF_PATH);
			$textBoxObj->setAlignment(TopLeft2);
			
			# Output the chart
            header("Content-type: image/png");
            print($c->makeChart2(PNG));exit;			
			file_put_contents($params['url'],$c->makeChart2(PNG));
			//header("Content-type: image/png");
			//echo $c->makeChart2(PNG);
			//ob_clean();
			// print($c->makeChart2(PNG));
	}
	
	public function doublelinechart($params,$mes,$dao) {
	
	   $isEnglish=$params["isEnglish"];
	   if($isEnglish){
		   $mes=EN_DATASOURCEMES;
		   $junzhi="AVG";
	   }else{
		   $mes=CN_DATASOURCEMES;
		   $junzhi="均值";
	   }
		//echo BASE_DIR;
		$newdata = array();
		require_once("/usr/local/www/libs/ChartDirector/lib/phpchartdir.php");
		//print"<pre>";print_r($params);
		
		if($params['ImageType']=="8"){
             
			 //echo "5555";
			$ChartExtdecode=$params['ChartExt'];
			//print_r($params['ChartExt']);
            $DateArr_ImageType8=$ChartExtdecode['DateStr'];
			$date_ImageType8=explode(',',$DateArr_ImageType8);
			$date_ImageType8=array_unique($date_ImageType8);//去除重复年
			$setdatanum=explode(',',$ChartExtdecode['setdatanum']);
			if(empty($ChartExtdecode['setdatanum']))
			{
				$setdatanum=array(6,6);
			}
            $setdatanum[0]=$setdatanum[0]>50?50:$setdatanum[0];
			$setdatanum[1]=$setdatanum[1]>50?50:$setdatanum[1];

			$SetDatearr=explode(',',$ChartExtdecode['SetDate']);
			
			if(empty($ChartExtdecode['SetDate']))
			{
				$SetDatearr=array('01-01','06-30');
				//$SetDatearr=array('10-01','01-30');
			}
			if(strlen($SetDatearr[0])<3)
			{
				// $ytday=array('1'=>'31','2'=>'29','3'=>'31','4'=>'30','5'=>'31','6'=>'30','7'=>'31','8'=>'31','9'=>'30','10'=>'31','11'=>'30','12'=>'31');
				// $num1 = (int) $SetDatearr[1];
				// $SetDatearr=array($SetDatearr[0].'-01',$SetDatearr[1].'-'.$ytday[$num1]);
				$SetDatearr[0]=$SetDatearr[0].'-01';
				//print_r($SetDatearr); 
			}
			if(strlen($SetDatearr[1])<3)
			{
				$ytday=array('1'=>'31','2'=>'29','3'=>'31','4'=>'30','5'=>'31','6'=>'30','7'=>'31','8'=>'31','9'=>'30','10'=>'31','11'=>'30','12'=>'31');
				$num1 = (int) $SetDatearr[1];
				$SetDatearr[1]=$SetDatearr[1].'-'.$ytday[$num1];
				//print_r($SetDatearr); 
			}
			
			//print_r($SetDatearr);
			$SetDatearr[0]=date("m-d",strtotime('2020-'.$SetDatearr[0]));
			$SetDatearr[1]=date("m-d",strtotime('2020-'.$SetDatearr[1]));
			//echo "----------------";
			//print_r($SetDatearr);
			//echo "----------------";
			if($ChartExtdecode['Type']==3&&$ChartExtdecode['isfestival']!=3)
			{
				
				$nzdtype=true;

			   $leftdate=$this->isrn($date_ImageType8);
			   $rightdate=$this->isrn($date_ImageType8,1);
			   sort($date_ImageType8);
				// print_r($date_ImageType8);
				//$rndatearr=array("01-01","01-02","01-03","01-04","01-05","01-06","01-07","01-08","01-09","01-10","01-11","01-12","01-13","01-14","01-15","01-16","01-17","01-18","01-19","01-20","01-21","01-22","01-23","01-24","01-25","01-26","01-27","01-28","01-29","01-30","01-31","02-01","02-02","02-03","02-04","02-05","02-06","02-07","02-08","02-09","02-10","02-11","02-12","02-13","02-14","02-15","02-16","02-17","02-18","02-19","02-20","02-21","02-22","02-23","02-24","02-25","02-26","02-27","02-28","02-29","03-01","03-02","03-03","03-04","03-05","03-06","03-07","03-08","03-09","03-10","03-11","03-12","03-13","03-14","03-15","03-16","03-17","03-18","03-19","03-20","03-21","03-22","03-23","03-24","03-25","03-26","03-27","03-28","03-29","03-30","03-31","04-01","04-02","04-03","04-04","04-05","04-06","04-07","04-08","04-09","04-10","04-11","04-12","04-13","04-14","04-15","04-16","04-17","04-18","04-19","04-20","04-21","04-22","04-23","04-24","04-25","04-26","04-27","04-28","04-29","04-30","05-01","05-02","05-03","05-04","05-05","05-06","05-07","05-08","05-09","05-10","05-11","05-12","05-13","05-14","05-15","05-16","05-17","05-18","05-19","05-20","05-21","05-22","05-23","05-24","05-25","05-26","05-27","05-28","05-29","05-30","05-31","06-01","06-02","06-03","06-04","06-05","06-06","06-07","06-08","06-09","06-10","06-11","06-12","06-13","06-14","06-15","06-16","06-17","06-18","06-19","06-20","06-21","06-22","06-23","06-24","06-25","06-26","06-27","06-28","06-29","06-30","07-01","07-02","07-03","07-04","07-05","07-06","07-07","07-08","07-09","07-10","07-11","07-12","07-13","07-14","07-15","07-16","07-17","07-18","07-19","07-20","07-21","07-22","07-23","07-24","07-25","07-26","07-27","07-28","07-29","07-30","07-31","08-01","08-02","08-03","08-04","08-05","08-06","08-07","08-08","08-09","08-10","08-11","08-12","08-13","08-14","08-15","08-16","08-17","08-18","08-19","08-20","08-21","08-22","08-23","08-24","08-25","08-26","08-27","08-28","08-29","08-30","08-31","09-01","09-02","09-03","09-04","09-05","09-06","09-07","09-08","09-09","09-10","09-11","09-12","09-13","09-14","09-15","09-16","09-17","09-18","09-19","09-20","09-21","09-22","09-23","09-24","09-25","09-26","09-27","09-28","09-29","09-30","10-01","10-02","10-03","10-04","10-05","10-06","10-07","10-08","10-09","10-10","10-11","10-12","10-13","10-14","10-15","10-16","10-17","10-18","10-19","10-20","10-21","10-22","10-23","10-24","10-25","10-26","10-27","10-28","10-29","10-30","10-31","11-01","11-02","11-03","11-04","11-05","11-06","11-07","11-08","11-09","11-10","11-11","11-12","11-13","11-14","11-15","11-16","11-17","11-18","11-19","11-20","11-21","11-22","11-23","11-24","11-25","11-26","11-27","11-28","11-29","11-30","12-01","12-02","12-03","12-04","12-05","12-06","12-07","12-08","12-09","12-10","12-11","12-12","12-13","12-14","12-15","12-16","12-17","12-18","12-19","12-20","12-21","12-22","12-23","12-24","12-25","12-26","12-27","12-28","12-29","12-30","12-31");
				//$pndatearr=array("01-01","01-02","01-03","01-04","01-05","01-06","01-07","01-08","01-09","01-10","01-11","01-12","01-13","01-14","01-15","01-16","01-17","01-18","01-19","01-20","01-21","01-22","01-23","01-24","01-25","01-26","01-27","01-28","01-29","01-30","01-31","02-01","02-02","02-03","02-04","02-05","02-06","02-07","02-08","02-09","02-10","02-11","02-12","02-13","02-14","02-15","02-16","02-17","02-18","02-19","02-20","02-21","02-22","02-23","02-24","02-25","02-26","02-27","02-28","03-01","03-02","03-03","03-04","03-05","03-06","03-07","03-08","03-09","03-10","03-11","03-12","03-13","03-14","03-15","03-16","03-17","03-18","03-19","03-20","03-21","03-22","03-23","03-24","03-25","03-26","03-27","03-28","03-29","03-30","03-31","04-01","04-02","04-03","04-04","04-05","04-06","04-07","04-08","04-09","04-10","04-11","04-12","04-13","04-14","04-15","04-16","04-17","04-18","04-19","04-20","04-21","04-22","04-23","04-24","04-25","04-26","04-27","04-28","04-29","04-30","05-01","05-02","05-03","05-04","05-05","05-06","05-07","05-08","05-09","05-10","05-11","05-12","05-13","05-14","05-15","05-16","05-17","05-18","05-19","05-20","05-21","05-22","05-23","05-24","05-25","05-26","05-27","05-28","05-29","05-30","05-31","06-01","06-02","06-03","06-04","06-05","06-06","06-07","06-08","06-09","06-10","06-11","06-12","06-13","06-14","06-15","06-16","06-17","06-18","06-19","06-20","06-21","06-22","06-23","06-24","06-25","06-26","06-27","06-28","06-29","06-30","07-01","07-02","07-03","07-04","07-05","07-06","07-07","07-08","07-09","07-10","07-11","07-12","07-13","07-14","07-15","07-16","07-17","07-18","07-19","07-20","07-21","07-22","07-23","07-24","07-25","07-26","07-27","07-28","07-29","07-30","07-31","08-01","08-02","08-03","08-04","08-05","08-06","08-07","08-08","08-09","08-10","08-11","08-12","08-13","08-14","08-15","08-16","08-17","08-18","08-19","08-20","08-21","08-22","08-23","08-24","08-25","08-26","08-27","08-28","08-29","08-30","08-31","09-01","09-02","09-03","09-04","09-05","09-06","09-07","09-08","09-09","09-10","09-11","09-12","09-13","09-14","09-15","09-16","09-17","09-18","09-19","09-20","09-21","09-22","09-23","09-24","09-25","09-26","09-27","09-28","09-29","09-30","10-01","10-02","10-03","10-04","10-05","10-06","10-07","10-08","10-09","10-10","10-11","10-12","10-13","10-14","10-15","10-16","10-17","10-18","10-19","10-20","10-21","10-22","10-23","10-24","10-25","10-26","10-27","10-28","10-29","10-30","10-31","11-01","11-02","11-03","11-04","11-05","11-06","11-07","11-08","11-09","11-10","11-11","11-12","11-13","11-14","11-15","11-16","11-17","11-18","11-19","11-20","11-21","11-22","11-23","11-24","11-25","11-26","11-27","11-28","11-29","11-30","12-01","12-02","12-03","12-04","12-05","12-06","12-07","12-08","12-09","12-10","12-11","12-12","12-13","12-14","12-15","12-16","12-17","12-18","12-19","12-20","12-21","12-22","12-23","12-24","12-25","12-26","12-27","12-28","12-29","12-30","12-31");
				
				$rndatearr=array("01/01","01/02","01/03","01/04","01/05","01/06","01/07","01/08","01/09","01/10","01/11","01/12","01/13","01/14","01/15","01/16","01/17","01/18","01/19","01/20","01/21","01/22","01/23","01/24","01/25","01/26","01/27","01/28","01/29","01/30","01/31","02/01","02/02","02/03","02/04","02/05","02/06","02/07","02/08","02/09","02/10","02/11","02/12","02/13","02/14","02/15","02/16","02/17","02/18","02/19","02/20","02/21","02/22","02/23","02/24","02/25","02/26","02/27","02/28","02/29","03/01","03/02","03/03","03/04","03/05","03/06","03/07","03/08","03/09","03/10","03/11","03/12","03/13","03/14","03/15","03/16","03/17","03/18","03/19","03/20","03/21","03/22","03/23","03/24","03/25","03/26","03/27","03/28","03/29","03/30","03/31","04/01","04/02","04/03","04/04","04/05","04/06","04/07","04/08","04/09","04/10","04/11","04/12","04/13","04/14","04/15","04/16","04/17","04/18","04/19","04/20","04/21","04/22","04/23","04/24","04/25","04/26","04/27","04/28","04/29","04/30","05/01","05/02","05/03","05/04","05/05","05/06","05/07","05/08","05/09","05/10","05/11","05/12","05/13","05/14","05/15","05/16","05/17","05/18","05/19","05/20","05/21","05/22","05/23","05/24","05/25","05/26","05/27","05/28","05/29","05/30","05/31","06/01","06/02","06/03","06/04","06/05","06/06","06/07","06/08","06/09","06/10","06/11","06/12","06/13","06/14","06/15","06/16","06/17","06/18","06/19","06/20","06/21","06/22","06/23","06/24","06/25","06/26","06/27","06/28","06/29","06/30","07/01","07/02","07/03","07/04","07/05","07/06","07/07","07/08","07/09","07/10","07/11","07/12","07/13","07/14","07/15","07/16","07/17","07/18","07/19","07/20","07/21","07/22","07/23","07/24","07/25","07/26","07/27","07/28","07/29","07/30","07/31","08/01","08/02","08/03","08/04","08/05","08/06","08/07","08/08","08/09","08/10","08/11","08/12","08/13","08/14","08/15","08/16","08/17","08/18","08/19","08/20","08/21","08/22","08/23","08/24","08/25","08/26","08/27","08/28","08/29","08/30","08/31","09/01","09/02","09/03","09/04","09/05","09/06","09/07","09/08","09/09","09/10","09/11","09/12","09/13","09/14","09/15","09/16","09/17","09/18","09/19","09/20","09/21","09/22","09/23","09/24","09/25","09/26","09/27","09/28","09/29","09/30","10/01","10/02","10/03","10/04","10/05","10/06","10/07","10/08","10/09","10/10","10/11","10/12","10/13","10/14","10/15","10/16","10/17","10/18","10/19","10/20","10/21","10/22","10/23","10/24","10/25","10/26","10/27","10/28","10/29","10/30","10/31","11/01","11/02","11/03","11/04","11/05","11/06","11/07","11/08","11/09","11/10","11/11","11/12","11/13","11/14","11/15","11/16","11/17","11/18","11/19","11/20","11/21","11/22","11/23","11/24","11/25","11/26","11/27","11/28","11/29","11/30","12/01","12/02","12/03","12/04","12/05","12/06","12/07","12/08","12/09","12/10","12/11","12/12","12/13","12/14","12/15","12/16","12/17","12/18","12/19","12/20","12/21","12/22","12/23","12/24","12/25","12/26","12/27","12/28","12/29","12/30","12/31"); 
				$pndatearr=array("01/01","01/02","01/03","01/04","01/05","01/06","01/07","01/08","01/09","01/10","01/11","01/12","01/13","01/14","01/15","01/16","01/17","01/18","01/19","01/20","01/21","01/22","01/23","01/24","01/25","01/26","01/27","01/28","01/29","01/30","01/31","02/01","02/02","02/03","02/04","02/05","02/06","02/07","02/08","02/09","02/10","02/11","02/12","02/13","02/14","02/15","02/16","02/17","02/18","02/19","02/20","02/21","02/22","02/23","02/24","02/25","02/26","02/27","02/28","03/01","03/02","03/03","03/04","03/05","03/06","03/07","03/08","03/09","03/10","03/11","03/12","03/13","03/14","03/15","03/16","03/17","03/18","03/19","03/20","03/21","03/22","03/23","03/24","03/25","03/26","03/27","03/28","03/29","03/30","03/31","04/01","04/02","04/03","04/04","04/05","04/06","04/07","04/08","04/09","04/10","04/11","04/12","04/13","04/14","04/15","04/16","04/17","04/18","04/19","04/20","04/21","04/22","04/23","04/24","04/25","04/26","04/27","04/28","04/29","04/30","05/01","05/02","05/03","05/04","05/05","05/06","05/07","05/08","05/09","05/10","05/11","05/12","05/13","05/14","05/15","05/16","05/17","05/18","05/19","05/20","05/21","05/22","05/23","05/24","05/25","05/26","05/27","05/28","05/29","05/30","05/31","06/01","06/02","06/03","06/04","06/05","06/06","06/07","06/08","06/09","06/10","06/11","06/12","06/13","06/14","06/15","06/16","06/17","06/18","06/19","06/20","06/21","06/22","06/23","06/24","06/25","06/26","06/27","06/28","06/29","06/30","07/01","07/02","07/03","07/04","07/05","07/06","07/07","07/08","07/09","07/10","07/11","07/12","07/13","07/14","07/15","07/16","07/17","07/18","07/19","07/20","07/21","07/22","07/23","07/24","07/25","07/26","07/27","07/28","07/29","07/30","07/31","08/01","08/02","08/03","08/04","08/05","08/06","08/07","08/08","08/09","08/10","08/11","08/12","08/13","08/14","08/15","08/16","08/17","08/18","08/19","08/20","08/21","08/22","08/23","08/24","08/25","08/26","08/27","08/28","08/29","08/30","08/31","09/01","09/02","09/03","09/04","09/05","09/06","09/07","09/08","09/09","09/10","09/11","09/12","09/13","09/14","09/15","09/16","09/17","09/18","09/19","09/20","09/21","09/22","09/23","09/24","09/25","09/26","09/27","09/28","09/29","09/30","10/01","10/02","10/03","10/04","10/05","10/06","10/07","10/08","10/09","10/10","10/11","10/12","10/13","10/14","10/15","10/16","10/17","10/18","10/19","10/20","10/21","10/22","10/23","10/24","10/25","10/26","10/27","10/28","10/29","10/30","10/31","11/01","11/02","11/03","11/04","11/05","11/06","11/07","11/08","11/09","11/10","11/11","11/12","11/13","11/14","11/15","11/16","11/17","11/18","11/19","11/20","11/21","11/22","11/23","11/24","11/25","11/26","11/27","11/28","11/29","11/30","12/01","12/02","12/03","12/04","12/05","12/06","12/07","12/08","12/09","12/10","12/11","12/12","12/13","12/14","12/15","12/16","12/17","12/18","12/19","12/20","12/21","12/22","12/23","12/24","12/25","12/26","12/27","12/28","12/29","12/30","12/31");
				
				$leftdate=$leftdate==1?$rndatearr:$pndatearr;
				$rightdate=$rightdate==1?$rndatearr:$pndatearr;

				foreach($params['xlabel'] as $i=>$v){
					$v=str_replace('-','/',$v);
					//echo $v."<br>";
					$year=explode('/',$v);
				//	if(count($year)==1) {$nzd=1;break;}
					$date=substr($v,strlen($year[0])+1);
					if(!strstr($date,'/'))//为的是数据存为年月格式
					{
						$date=$date."/01";
						$yearorminthdata=true;
					}
					$datearr[$date]=1;
					$data[$year[0]][$date]=$params['data'][0][$i];
				}
				if($yearorminthdata)
				{
					$leftdate=array('01/01','02/01','03/01','04/01','05/01','06/01','07/01','08/01','09/01','10/01','11/01','12/01');
					$rightdate=$leftdate;
				}
				//$date=array_keys($datearr);
				//sort($date);
				$params['xlabel']=array_merge($leftdate,$rightdate);
				$params['data']=array();
				$params['lendtitle']=array();
				$i=0;
				foreach($date_ImageType8 as $i=>$v)
				{
					//$datanew=$data[$v]?$data[$v]:$data[($v-2000)];
					$datanew=$data[$v]?$data[$v]:$data[(($v-2000)>=10?($v-2000):"0".($v-2000))];


					if( $params['datanew1'][$datanew]&&false)
					{
						// print_r($params['datanew1'][$datanew]);
						// echo $datanew;
						// $params['data'][$i]=$params['datanew1'][$datanew];
					}
					else
					{
							foreach($leftdate as $o=>$d)//年折叠图左侧日期
							{
								if($datanew[$d]==""){
									$year=$v<2000?$v+2000:$v;
									if(strstr($leftdate[$o],'/')){
										$mdate=$year."/".$leftdate[$o];
									}else{
										$mdate=$year."/".$leftdate[$o]."/01";
									}
									if(strtotime($mdate)>time()) {
										$params['data'][$i][$d]="'-'";
										continue;
									}
									$params['data'][$i][$d]=$this->getbu($datanew,$leftdate,$o-1);
									if($params['data'][$i][$d]== "") {
										$params['data'][$i][$d]=$this->getbu2($datanew,$leftdate,$o+1);
									}
									if(empty($params['data'][$i][$d]))
									{
										$params['data'][$i][$d]="'-'";
									} 
									
								}
								else
								{
								
										$params['data'][$i][$d]=$datanew[$d];
									
								}

							}
					}

					//$datanew1=$data[($v+1)]?$data[($v+1)]:$data[(($v+1)-2000)];
					$datanew1=$data[($v+1)]?$data[($v+1)]:$data[(($v+1-2000)>=10?($v+1-2000):"0".($v+1-2000))];
					foreach($leftdate as $o=>$d)//年折叠图右侧日期
					{
						if($datanew1[$d]==""){
							$year=($v+1)<2000?($v+1)+2000:($v+1);
							if(strstr($leftdate[$o],'/')){
								$mdate=$year."/".$leftdate[$o];
							}else{
								$mdate=$year."/".$leftdate[$o]."/01";
							}
							//echo $mdate."<br/>";
							if(strtotime($mdate)>time()) {
								$params['datanew'][$i][$d]="'-'";
								continue;
							}
							$params['datanew'][$i][$d]=$this->getbu($datanew1,$leftdate,$o-1);
							if($params['datanew'][$i][$d]== "") {
								$params['datanew'][$i][$d]=$this->getbu2($datanew1,$leftdate,$o+1);
							} 
							if(empty($params['datanew'][$i][$d]))
							{
								$params['datanew'][$i][$d]="'-'";
							}
							
						}
						else
						{
						
								$params['datanew'][$i][$d]=$datanew1[$d];
							
						}
						$params['data'][$i][]=$params['datanew'][$i][$d];
					}
					// print_r($params['data'][$i]);
					// print_r($params['datanew'][$i]);


					// $params['data'][$i]=$params['data'][$i]+$params['datanew'][$i];

					// print_r($params['data'][$i]);


					$params['datanew1'][($v+1)]=$params['datanew'][$i];
					//print_r($params['datanew1']);  

					$params['lendtitle'][$i]=$v<2000?($v+2000)."-".($v+1+2000):$v."-".($v+1);
					$i++;
				}
				//print_r($params['data']);
				//exit;

			}
			else if($ChartExtdecode['isfestival'])
			{
				
				$nzdtypenew=true;
				sort($date_ImageType8);
				if($ChartExtdecode['Type']==1)
				{
					$date_ImageType8=array_unique($date_ImageType8);//去除重复年
					
					if(count($date_ImageType8)!=1)
					{
						$date_ImageType8_new=array();
                        for($i1=$date_ImageType8[0];$i1<=$date_ImageType8[1];$i1++)
						{
							$date_ImageType8_new[]=$i1;
						}

						$date_ImageType8=$date_ImageType8_new;

					}
                
				}
				//print_r($date_ImageType8);
				//print_r($params['xlabel']);
				//print_r($params['data']);
				$datearrlist=array();
				$data=array();
				
				if($ChartExtdecode['isfestival']!=3)
				{
					foreach($params['xlabel'] as $i=>$v){
						$v=str_replace('-','/',$v);
						//echo $v."<br>";
						$year=explode('/',$v);
					//	if(count($year)==1) {$nzd=1;break;}
						$date=substr($v,strlen($year[0])+1);
						if(!strstr($date,'/'))//为的是数据存为年月格式
						{
							$date=$date."/01";
							$yearorminthdata=true;
						}
						$datearr[$date]=1;
						$year=$year[0]<2000?$year[0]+2000:$year[0];
						$data[$i]=$params['data'][0][$i];
						$datearrlist[]=$year."/".$date;
					}

					$startleft=($setdatanum[0]*-1);  
					$startright=$setdatanum[1];
					//echo  $startleft."|||".$startright;
					for($i=$startleft;$i<=$startright;$i++)
					{
	
					  $num[]=$i;
					}
					//print_r($num);
					$params['xlabel']=$num;
					$params['data']=array();
					$params['lendtitle']=array();
					$i1=0;
					$lnchunkjiedatelist=array(
					'2001'=>'2001-01-24',
					'2002'=>'2002-02-12',
					'2003'=>'2003-02-01',
					'2004'=>'2004-01-22',
					'2005'=>'2005-02-09',
					'2006'=>'2006-01-29',
					'2007'=>'2007-02-18',
					'2008'=>'2008-02-07',
					'2009'=>'2009-01-26',
					'2010'=>'2010-02-14',
					'2011'=>'2011-02-03',
					'2012'=>'2012-01-23',
					'2013'=>'2013-02-01',
					'2014'=>'2014-01-31',
					'2015'=>'2015-02-18',
					'2016'=>'2016-02-08',
					'2017'=>'2017-01-28',
					'2018'=>'2018-02-16',
					'2019'=>'2019-02-05',
					'2020'=>'2020-01-25',
					'2021'=>'2021-02-12',
					'2022'=>'2022-02-01',
					'2023'=>'2023-01-22',
					'2024'=>'2024-02-01',
					'2025'=>'2025-01-29',
					'2026'=>'2026-02-17',
					'2027'=>'2027-02-06',
					'2028'=>'2028-01-26',
					'2029'=>'2029-02-13',
					'2030'=>'2030-02-03',
					'2031'=>'2031-01-23',
					'2032'=>'2032-02-11',
					'2033'=>'2033-01-31',
					'2034'=>'2034-02-19',
					'2035'=>'2035-02-08',
					'2036'=>'2036-01-28',
					'2037'=>'2037-02-15',
					'2038'=>'2038-02-04',
					'2039'=>'2039-01-24',
					'2040'=>'2040-02-12',
					'2041'=>'2041-02-01',
					'2042'=>'2042-01-22',
					'2043'=>'2043-02-01',
					'2044'=>'2044-01-03',
					'2045'=>'2045-02-17',
					'2046'=>'2046-02-06',
					'2047'=>'2047-01-26',
					'2048'=>'2048-02-14',
					'2049'=>'2049-02-02',
					'2050'=>'2050-01-23');
	
				   
	
					foreach($date_ImageType8 as $i=>$v)
					{
						if($ChartExtdecode['isfestival']==1)
						{
						$finddate=$lnchunkjiedatelist[$v];
						}
						else
						{
							$finddate=$v."/10/01";
						}
						//echo $finddate;
						$findatedata= $this->finddate($datearrlist,$finddate);
						//print_r($findatedata);
						if(empty($findatedata))
						{
							continue;
						}
						//echo $finddate."|||||";
						if($ChartExtdecode['isfestival']==1)
						{
							if(time()<strtotime($finddate)-86400*3)
							{
								continue;
							}
						}
						else
						{
							if(time()<strtotime($finddate))
							{
								continue;
							}
						}

						
						if(strtotime($findatedata['date'])>strtotime($finddate)&&$findatedata['key']==0)
						{
							
							continue;
						}
	                  
						
						//print_r($findatedata);
						if(strtotime($findatedata['date'])>strtotime($finddate))
						{
							$py=1;
						}
						else if (strtotime($findatedata['date'])<strtotime($finddate))
						{
							$py=-1;
							//echo "455156";
						}
						else
						{
							$py=0;
						}
						$findatekey=$findatedata['key'];
						foreach($num as $o=>$d)//
						{
							if($yearorminthdata)
							{
								$findatekeynew= $findatekey+$d;
                                $shuzhi=$data[$findatekeynew];
							}
							else
							{
									if($py>0)
									{
										//echo $findatekey."||||".$d."<br/>";
										$findatekeynew= $findatekey+$d;
										if($d>0)
										{
											$findatekeynew= $findatekey+$d-1;
			
											$shuzhi=$data[$findatekeynew];
										}
										else if($d<0)
										{
											$findatekeynew= $findatekey+$d;
											$shuzhi=$data[$findatekeynew];
										}
										else
										{
											$findatekeynew= $findatekey+$d;
											
											$shuzhi=($data[$findatekeynew]+$data[$findatekeynew-1])/2;
											if(empty($data[$findatekeynew])||empty($data[$findatekeynew-1]))
											{
												$shuzhi=$data[$findatekeynew]+$data[$findatekeynew-1];
											}
										}
									}
									else if($py<0)
									{
										if($d>0)
										{
											$findatekeynew= $findatekey+$d;
											$shuzhi=$data[$findatekeynew];
										}
										else if($d<0)
										{
											$findatekeynew= $findatekey+$d+1;
											$shuzhi=$data[$findatekeynew];
										}
										else
										{
											$findatekeynew= $findatekey+$d;
											$shuzhi=($data[$findatekeynew]+$data[$findatekeynew+1])/2;
											if(empty($data[$findatekeynew])||empty($data[$findatekeynew+1]))
											{
												$shuzhi=$data[$findatekeynew]+$data[$findatekeynew+1];
											}
										}
									}
									else
									{
										$findatekeynew=$findatekey+$d;
										$shuzhi=$data[$findatekeynew];
									}
						    }
							//echo "shenem<br/>";
							//echo $shuzhi."<br/>";	
							$shuzhi=(empty($shuzhi)?"'-'":$shuzhi);
							$params['data'][$i1][]=$shuzhi;
						}
						$params['lendtitle'][$i1]=$v<2000?($v+2000):$v;
						$i1++;
						//exit;
					}
					
					//print_r($params['data']);
				}
				else//指定日期年折叠图
				{
					$startleft=0;
					if(strtotime('2020-'.$SetDatearr[0])>strtotime('2020-'.$SetDatearr[1]))
					{
						$startleft=1;
					}
					//print_r($params['xlabel']);
					foreach($params['xlabel'] as $i=>$v){
						$v=str_replace('-','/',$v);
						//echo $v."<br>";
						$year=explode('/',$v);
					//	if(count($year)==1) {$nzd=1;break;}
						$date=substr($v,strlen($year[0])+1);
						if(!strstr($date,'/'))//为的是数据存为年月格式
						{
							$date=$date."/01";
							$yearorminthdata=true;
						}
						$datearr[$date]=1;
						// if($startleft>0)
						// {
						// 	//echo $year[0].'/'.$date.'||||||'.$SetDatearr[0]."<br/>";
						// 	 if(strtotime('2020/'.$date)>=strtotime('2020-'.$SetDatearr[0]))
						// 	 {
						// 		// echo $date."鬼啊<br/>";
						// 		$data[($year[0]+1)][$date]=$params['data'][0][$i];
						// 	 }
						// 	 else
						// 	 {
						// 		$data[$year[0]][$date]=$params['data'][0][$i];
						// 	 }
						// }
						// else
						// {
						// 	$data[$year[0]][$date]=$params['data'][0][$i];
						// }
						$data[$year[0]][$date]=$params['data'][0][$i];
					}
                    //print_r($data);
                    $rndatearr=array("01/01","01/02","01/03","01/04","01/05","01/06","01/07","01/08","01/09","01/10","01/11","01/12","01/13","01/14","01/15","01/16","01/17","01/18","01/19","01/20","01/21","01/22","01/23","01/24","01/25","01/26","01/27","01/28","01/29","01/30","01/31","02/01","02/02","02/03","02/04","02/05","02/06","02/07","02/08","02/09","02/10","02/11","02/12","02/13","02/14","02/15","02/16","02/17","02/18","02/19","02/20","02/21","02/22","02/23","02/24","02/25","02/26","02/27","02/28","02/29","03/01","03/02","03/03","03/04","03/05","03/06","03/07","03/08","03/09","03/10","03/11","03/12","03/13","03/14","03/15","03/16","03/17","03/18","03/19","03/20","03/21","03/22","03/23","03/24","03/25","03/26","03/27","03/28","03/29","03/30","03/31","04/01","04/02","04/03","04/04","04/05","04/06","04/07","04/08","04/09","04/10","04/11","04/12","04/13","04/14","04/15","04/16","04/17","04/18","04/19","04/20","04/21","04/22","04/23","04/24","04/25","04/26","04/27","04/28","04/29","04/30","05/01","05/02","05/03","05/04","05/05","05/06","05/07","05/08","05/09","05/10","05/11","05/12","05/13","05/14","05/15","05/16","05/17","05/18","05/19","05/20","05/21","05/22","05/23","05/24","05/25","05/26","05/27","05/28","05/29","05/30","05/31","06/01","06/02","06/03","06/04","06/05","06/06","06/07","06/08","06/09","06/10","06/11","06/12","06/13","06/14","06/15","06/16","06/17","06/18","06/19","06/20","06/21","06/22","06/23","06/24","06/25","06/26","06/27","06/28","06/29","06/30","07/01","07/02","07/03","07/04","07/05","07/06","07/07","07/08","07/09","07/10","07/11","07/12","07/13","07/14","07/15","07/16","07/17","07/18","07/19","07/20","07/21","07/22","07/23","07/24","07/25","07/26","07/27","07/28","07/29","07/30","07/31","08/01","08/02","08/03","08/04","08/05","08/06","08/07","08/08","08/09","08/10","08/11","08/12","08/13","08/14","08/15","08/16","08/17","08/18","08/19","08/20","08/21","08/22","08/23","08/24","08/25","08/26","08/27","08/28","08/29","08/30","08/31","09/01","09/02","09/03","09/04","09/05","09/06","09/07","09/08","09/09","09/10","09/11","09/12","09/13","09/14","09/15","09/16","09/17","09/18","09/19","09/20","09/21","09/22","09/23","09/24","09/25","09/26","09/27","09/28","09/29","09/30","10/01","10/02","10/03","10/04","10/05","10/06","10/07","10/08","10/09","10/10","10/11","10/12","10/13","10/14","10/15","10/16","10/17","10/18","10/19","10/20","10/21","10/22","10/23","10/24","10/25","10/26","10/27","10/28","10/29","10/30","10/31","11/01","11/02","11/03","11/04","11/05","11/06","11/07","11/08","11/09","11/10","11/11","11/12","11/13","11/14","11/15","11/16","11/17","11/18","11/19","11/20","11/21","11/22","11/23","11/24","11/25","11/26","11/27","11/28","11/29","11/30","12/01","12/02","12/03","12/04","12/05","12/06","12/07","12/08","12/09","12/10","12/11","12/12","12/13","12/14","12/15","12/16","12/17","12/18","12/19","12/20","12/21","12/22","12/23","12/24","12/25","12/26","12/27","12/28","12/29","12/30","12/31"); 
				
					if($yearorminthdata)
				   {
					   $day=date("j",strtotime('2020-'.$SetDatearr[0]));
					   if($day>1)
					   {
						 $day=date("n",strtotime('2020-'.$SetDatearr[0]));
						 $day++;
						 if($day<10)
						 {
							$SetDatearr[0]="0".$day."-01";
						 }
						 else
						 {
							$SetDatearr[0]=$day."-01";
						 }

					   }
					   else
					   {
						$SetDatearr[0]=date("m-01",strtotime('2020-'.$SetDatearr[0]));
					   }


					   $SetDatearr[1]=date("m-01",strtotime('2020-'.$SetDatearr[1]));


					   $rndatearr=array('01/01','02/01','03/01','04/01','05/01','06/01','07/01','08/01','09/01','10/01','11/01','12/01');
				   }
					//$closest = key($interval);
					//print_r($SetDatearr);
					$key1=array_search(str_replace('-','/',$SetDatearr[0]),$rndatearr);
					//echo $rndatearr[$key1];
					$key2=array_search(str_replace('-','/',$SetDatearr[1]),$rndatearr);
					//echo $rndatearr[$key2];
					//echo $key1."||||".$key2."<br/>";
					if($ChartExtdecode['Type']!=3)
					{
						if($key1<=$key2)
						{
							$leftdate=array_slice($rndatearr,$key1,($key2-$key1+1));
							//print_r($leftdate);
						}
						else
						{
							$leftdate1=array_slice($rndatearr,$key1);
							//print_r($leftdate1);
							$leftdate2=array_slice($rndatearr,0,($key2+1));
							$leftdate=array_merge($leftdate1,$leftdate2);
						}
					}
				  else
					{
						$leftdate=array_slice($rndatearr,$key1);
                        //print_r($leftdate);
						$leftdate_2=array_slice($rndatearr,0,$key2+1);
						//print_r($leftdate_2);

					}



					//print_r($leftdate);
					$params['xlabel']=array();
                    $params['xlabel']=$leftdate;
					$params['data']=array();
					$params['lendtitle']=array();
					$i1=0;
					foreach($date_ImageType8 as $i=>$v)
					{
						//$datanew=$data[$v]?$data[$v]:$data[($v-2000)];


						if($ChartExtdecode['Type']!=3)
						{
							$datanew=$data[$v]?$data[$v]:$data[(($v-2000)>=10?($v-2000):"0".($v-2000))];

							foreach($leftdate as $o=>$d)//年折叠图右侧日期
							{
								if($datanew[$d]==""){

									$year=$v<2000?$v+2000:$v;;
									if(strstr($leftdate[$o],'/')){
										$mdate=$year."/".$leftdate[$o];
									}else{
										$mdate=$year."/".$leftdate[$o]."/01";
									}
									if(strtotime($mdate)>time()) {
										$params['datanew'][$i][$d]="'-'";
										continue;
									}

									$params['datanew'][$i][$d]=$this->getbu($datanew,$leftdate,$o-1);
									if($params['datanew'][$i][$d]== "") {
										$params['datanew'][$i][$d]=$this->getbu2($datanew,$leftdate,$o+1);
									} 
									if(empty($params['datanew'][$i][$d]))
									{
										$params['datanew'][$i][$d]="'-'";
										//$params['datanew'][$i][$d]="'null'";
									}
									
								}
								else
								{
								
										$params['datanew'][$i][$d]=$datanew[$d];
									
								}
								$params['data'][$i][]=$params['datanew'][$i][$d];
							}
							$params['lendtitle'][$i]=$v<2000?($v+2000):$v;
						}
						else
						{
							$datanew=$data[$v]?$data[$v]:$data[(($v-2000)>=10?($v-2000):"0".($v-2000))];
							if( $params['datanew1'][$datanew]&&false)
							{
								// print_r($params['datanew1'][$datanew]);
								// echo $datanew;
								// $params['data'][$i]=$params['datanew1'][$datanew];
							}
							else
							{
									foreach($leftdate as $o=>$d)//年折叠图左侧日期
									{
										if($datanew[$d]==""){
											$year=$v<2000?$v+2000:$v;
											if(strstr($leftdate[$o],'/')){
												$mdate=$year."/".$leftdate[$o];
											}else{
												$mdate=$year."/".$leftdate[$o]."/01";
											}
											if(strtotime($mdate)>time()) {
												$params['data'][$i][$d]="'-'";
												continue;
											}
											$params['data'][$i][$d]=$this->getbu($datanew,$leftdate,$o-1);
											if($params['data'][$i][$d]== "") {
												$params['data'][$i][$d]=$this->getbu2($datanew,$leftdate,$o+1);
											}
											if(empty($params['data'][$i][$d]))
											{
												$params['data'][$i][$d]="'-'";
											} 
											
										}
										else
										{
										
												$params['data'][$i][$d]=$datanew[$d];
											
										}
	
									}
							}
	
							//$datanew1=$data[($v+1)]?$data[($v+1)]:$data[(($v+1)-2000)];
							$datanew1=$data[($v+1)]?$data[($v+1)]:$data[(($v+1-2000)>=10?($v+1-2000):"0".($v+1-2000))];
							//print_r($datanew1);
							foreach($leftdate_2 as $o=>$d)//年折叠图右侧日期
							{
								//echo $d."<br/>";
								if($datanew1[$d]==""){
									$year=($v+1)<2000?($v+1)+2000:($v+1);
									if(strstr($leftdate_2[$o],'/')){
										$mdate=$year."/".$leftdate_2[$o];
									}else{
										$mdate=$year."/".$leftdate_2[$o]."/01";
									}
									//echo $mdate."<br/>";
									if(strtotime($mdate)>time()) {
										//echo "---<br/>";
										$params['datanew'][$i][$d]="'-'";
										continue;
									}
									$params['datanew'][$i][$d]=$this->getbu($datanew1,$leftdate_2,$o-1);
									if($params['datanew'][$i][$d]== "") {
										$params['datanew'][$i][$d]=$this->getbu2($datanew1,$leftdate_2,$o+1);
									} 
									if(empty($params['datanew'][$i][$d]))
									{
										$params['datanew'][$i][$d]="'-'";
									}
									
								}
								else
								{
								
										$params['datanew'][$i][$d]=$datanew1[$d];
									
								}
								$params['data'][$i][]=$params['datanew'][$i][$d];
							}
							//echo $i."<br/>";
							 //print_r($params['data'][$i]);
							// print_r($params['datanew'][$i]);
	
	
							// $params['data'][$i]=$params['data'][$i]+$params['datanew'][$i];
	
							// print_r($params['data'][$i]);
	
	
							$params['datanew1'][($v+1)]=$params['datanew'][$i];
							//print_r($params['datanew1']);  
	
							$params['lendtitle'][$i]=$v<2000?($v+2000)."-".($v+1+2000):$v."-".($v+1);
						}
					    $i1++;

					}
                   
					if($ChartExtdecode['Type']==3)
					{
						if($yearorminthdata)
						{
							$nzdtype=true;
						}
						
					  $params['xlabel']=array_merge($leftdate,$leftdate_2);
					}
						
					// 	$i++;
					// }



				}
			}
			else
			{
			foreach($params['xlabel'] as $i=>$v){
				$v=str_replace('-','/',$v);
				//echo $v."<br>";
				$year=explode('/',$v);
				if(count($year)==1) {$nzd=1;break;}
				$date=substr($v,strlen($year[0])+1);
				$datearr[$date]=1;
				$data[$year[0]][$date]=$params['data'][0][$i];
			}
			$date=array_keys($datearr);
			sort($date);
			
			$params['xlabel']=$date;
			$params['data']=array();
			$params['lendtitle']=array();
			$i1=0;
			
			//echo '<pre>';print_r($data);//exit;
			foreach($data as $y=>$v){
				//echo $y."\t";
				// echo '<pre>';print_r($v);//exit;
				foreach($date as $o=>$d){
					if($v[$d]==""){
						$year=$y<2000?$y+2000:$y;
						if(strstr($date[$o],'/')){
							$mdate=$year."/".$date[$o];
						}else{
							$mdate=$year."/".$date[$o]."/01";
						}
						// echo date('Y-m-d H:i:s', time());exit;
						if(strtotime($mdate)>time()) {
							$params['data'][$i1][$d]="'-'";
							continue;
						}
						  
						//20210301 addbyxr 本期为空不补数据,上期为空的补数据
						$params['data'][$i1][$d]=$this->getbu2($v,$date,$o+1);
						  
						if($params['data'][$i1][$d]== "") {
							
							$params['data'][$i1][$d]="'-'";
							
						} 
						/* $params['data'][$i1][$d]=$this->getbu($v,$date,$o-1);
						  
						if($params['data'][$i1][$d]== "") {
							
							$params['data'][$i1][$d]=$this->getbu2($v,$date,$o+1);
							
						} 
						*/
					}else{
					
							$params['data'][$i1][$d]=$v[$d];
						
						   
					}
				}
				$params['lendtitle'][$i1]=$y<2000?$y+2000:$y;
				$i1++;
						   }
						 
						   
			} 

             //print_R($params);


			//$res=$this->yeardataarr($params);

			//$params='';
			//$params=$res;
			//$newdata=$res['newdata'];
			//$newtitle=$res['newtitle'];
			
			$newdata=$params['data'];
			$newtitle=$params['lendtitle'];
		
		}else{ 
			foreach ($params['data'] as $k=>$v){
				//if($k<=1)
				{
					$newdata[] = $v;
					$newtitle[] = $params['lendtitle'][$k];
				}
			}	
		} 
	
		$n = count($newdata);
		if($n==1){
			$this->linechart($params,$mes,$dao);
		}else{
			if($n==2&&$params['zhou1']==0&&$params['zhou2']==0)  $params['zhou2']=1;
			$ytwoflag=$params['twoy'];
			$lendtitle=$params['lendtitle'];
			$yaxistitle=$params['yaxistitle'];
			$labels=$params['xlabel'];
			$num=count($params['data']);
			# Create a XYChart object of size 540 x 375 pixels
			//$linecolor=array(0xfb0303,0xa260e6,0x575ae1,0x66dede,0x8B008B);
			//$linecolor=array(0xff0000,0x1007bd,0xffc000,0xa004c1,0x10c358);
			
			//取配色方案
			$linecolor=$this->getcolor($params,$dao);





			
			if($shape=='big'){
			    //图片长宽
				$pic1_1=600;
				$pic1_2=350;
				 //图表长宽高
				$pic2_1=55;//左间距
				$pic2_2=68;//距离顶部
				$pic2_3=460;//宽
				$pic2_4=200;//长
				//钢之家数据来源那段话位置
				$pic3_1=415;//左间距
				$pic3_2=45;//距离顶部
				$pic3_font=10;//字体大小
			   //线条名的位置
				$pic4_1=50;//左间距
				$pic4_2=290;//距离顶部
				$pic4_font=10;//字体大小
				//轴上字大小
                $xy_font=10;
				//标题大小
				$title_font=15;
			}else if($shape=='small'){
			  
			}else{
				//图片长宽
				$pic1_1=485;
				$pic1_2=279;
				//图表长宽高
				$pic2_1=50;//左间距
				$pic2_2=50;//距离顶部
				$pic2_3=380;//宽
				$pic2_4=160;//长
				//钢之家数据来源那段话位置
				$pic3_1=370;//左间距
				$pic3_2=30;//距离顶部
				$pic3_font=8.5;//字体大小
				//线条名的位置
				$pic4_1=40;//左间距
				$pic4_2=230;//距离顶部
				$pic4_font=8.5;//字体大小
				//轴上字大小
				$xy_font=10;
				//标题大小
                $title_font=13;
			}



			$c = new XYChart($pic1_1, $pic1_2);//图片大小
			$c->setDefaultFonts(CREATE_IMG_TTF_PATH);
			# Add a title to the chart using 18 pts Times Bold Italic font
			$c->addTitle($params['charttitle'], CREATE_IMG_TTF_PATH, $title_font);			
			$plotarea = $c->setPlotArea($pic2_1, $pic2_2, $pic2_3,$pic2_4, 0xffffff, 0xffffff, 0xe9e9e9, 0xc0c0c0);//图表的大小长宽
			if($params['isbg'] == "1"){}else{
 			 $plotarea->setBackground2(dirname(__FILE__)."/../../images/".BG_IMG);
			}
			 //$plotarea->setBackground2(dirname(__FILE__)."/../../images/".BG_IMG);
			 
			//钢之家数据来源那段话
 			$textBoxObj = $c->addText($pic3_1, $pic3_2, $mes, CREATE_IMG_TTF_PATH, $pic3_font, 0x000000);
 			$textBoxObj->setAlignment(TopRight);
			$legendObj = $c->addLegend($pic4_1, $pic4_2, false, CREATE_IMG_TTF_PATH, $pic4_font);//线条名的位置
			$legendObj->setBackground(Transparent);
			# Set the x axis labels
			//$c->xAxis->setLabels($labels);
			//$c->xAxis->setLabelStep(2);
			# Set axis label style to 8pts Arial Bold
			$c->xAxis->setLabelStyle(CREATE_IMG_TTF_PATH, $xy_font,TextColor);
			$c->yAxis->setLabelStyle(CREATE_IMG_TTF_PATH, $xy_font);//左轴
			$c->yAxis2->setLabelStyle(CREATE_IMG_TTF_PATH, $xy_font);//右轴
			# Set axis line width to 2 pixels
			$c->xAxis->setWidth(1);
			$c->yAxis->setWidth(1);
			// $c->setUseYAxis2->setWidth(2);
			# Add a line layer to the chart

			if($params['isjz']=="1"){
				for($i=0;$i<$num;$i++)
				{
					$avg[$i]=round(array_sum($params['data'][$i])/count($params['data'][$i]),2);
					$avg_print[$i]="  ".$junzhi.":".$avg[$i];
				}

			}
			
			$dmin=array();
			$dmax=array();
			$dminline=array();
			$dmaxline=array();
			$zhou=array();
			$ma=0;
			$ldw=$rdw="";
			if($params['ImageType']=="8"){
				foreach($params['lendtitle'] as $id=>$da){ 
					$zhou[$id+1]="0";
					$ma="-1";
				}
			}else{
				foreach($params as $id=>$da){ 
					if(strstr($id,"zhou")&&strlen($id)==5){
						//echo $id."=>".$da."<br>";
						$index=str_replace("zhou","",$id);
						$zhou[$index]=empty($da)?"0":$da;
						if($da==1&&$index-$num<=0) $ma++;
					}
				}
				if($ma>0&&$ma<$num) $ma=1;
				else if($ma==$num)  $ma=-1;
				else $ma=0;
			}
	
			
		
			//echo $num."<br>";echo $ma."<br>";exit;
			//print_r($params['dw']);
			foreach($zhou as $id=>$val){
				$layer="layer".$id;
				if($id>$num) {/*echo 3;*/continue;}
				$i=0;
				if($val==0||$ma==-1){ //echo " 0 ";
					$$layer = $c->addLineLayer2();
					$$layer->setLineWidth(2);
					//$datat=$datatemp[0];
					//  echo '<pre>';print_R($newdata[$id-1]);
					$datat=$newdata[$id-1];
					$dmin[$id]=$this->getmin($newdata[$id-1]);
					$dmax[$id]=$this->getmax($newdata[$id-1]);
                   
					if($params["isEnglish"]==1){
						$left="left";
						$right="right";
					}else{
						$left="左";
						$right="右";
					} 
					if($params['ImageType']=="8"){
						$$layer->addDataSet($datat,$linecolor[$id-1], $this->getmytitle($lendtitle[$id-1].$avg_print[$id-1],$id-1));
					}else{
						$$layer->addDataSet($datat,$linecolor[$id-1], $this->getmytitle($left."-".$lendtitle[$id-1].$avg_print[$id-1],$id-1));
					}

					
					# Configure the bars within a group to touch each others (no gap)
					// $layer1->setBarGap(0.2, TouchBar);
					//print_r($yaxistitle);
					//echo "<br>".($id-1)." 666<br>";
					$ldw=$ldw==""?$params['dw'][$id-1]:$ldw;
				}elseif($val==1){
					//echo $layer."<br>";
					if($params["isEnglish"]==1){
						$left="left";
						$right="right";
					}else{
						$left="左";
						$right="右";
					}
					$$layer = $c->addLineLayer2();
					$$layer->setLineWidth(2);
					//$datat=$datatemp[1];
					$datat=$newdata[$id-1];
					if($params['ImageType']=="8"){
						$$layer->addDataSet($datat,$linecolor[$id-1], $this->getmytitle($lendtitle[$id-1].$avg_print[$id-1],$id-1));
					}else{
						$$layer->addDataSet($datat,$linecolor[$id-1], $this->getmytitle($right."-".$lendtitle[$id-1].$avg_print[$id-1],$id-1));
					}
					//$dminline[$i]=min($datatemp[1]);
					//$dmaxline[$i]=max($datatemp[1]);
					$dminline[$id]=$this->getmin($newdata[$id-1]);
					$dmaxline[$id]=$this->getmax($newdata[$id-1]);
			
					$$layer->setUseYAxis2();
					
					$rdw=$rdw==""?$params['dw'][$id-1]:$rdw;
				}else{
					//echo $val."<br>";
				}
			}
			if(count($labels)>=50){
				$ii=count($labels)/4;//x轴刻度数量
				$c->xAxis->addlabel(0,$labels[0][0]);
				for($i=0;$i<4;$i++){
					$c->xAxis->addlabel((int)($i*$ii),$labels[(int)($i*$ii)]);
				}
				//print"<pre>";print_r($labels);
				$c->xAxis->addlabel(count($labels)-1,$labels[count($labels)-1]);
			}elseif(count($labels)<=12&&$params['bar_datetype']['data']<=3&&$params['issametype']==1){
				foreach($labels as $id=>$val){
					$c->xAxis->addLabel($id,$val);
				  }
			 }else{
				 $c->xAxis->setLabels($labels);
				 $countlabel=count($labels);
				 $stepcount=round($countlabel/4,0);
				 $c->xAxis->setLabelStep($stepcount);
			}
			if($this->getmin($dmin)>0){
				$mina = 0.95*$this->getmin($dmin);
			}else{
				$mina = 1.05*$this->getmin($dmin);
			}
			if($this->getmax($dmax)>0){
				$maxa = 1.05*$this->getmax($dmax);
			}else{
				$maxa = 0.95*$this->getmax($dmax);
			}
			
			$c->yAxis->setLinearScale($mina,$maxa);
            if(!empty($ldw)){
				$textBoxObj =$c->yAxis->setTitle("左(". $ldw .")",CREATE_IMG_TTF_PATH);
				$textBoxObj->setAlignment(TopLeft2);
			}
			
			
			if($ma==1){
				if($this->getmin($dminline)>0){
					$minb = 0.95*$this->getmin($dminline);
				}else{
					$minb = 1.05*$this->getmin($dminline);
				}
				if($this->getmax($dmaxline)>0){
					$maxb = 1.05*$this->getmax($dmaxline);
				}else{
					$maxb = 0.95*$this->getmax($dmaxline);
				}
				
				$c->yAxis2->setLinearScale($minb,$maxb);
				if(!empty($rdw)){
					$textBoxObj2 =$c->yAxis2->setTitle("右(".$rdw.")",CREATE_IMG_TTF_PATH);
					$textBoxObj2->setAlignment(TopRight2);
				}
				
			}
			//print_r($dmin);print_r($dmax);
			//echo "左 ".$mina." - ".$maxa."<br>";
			//print_r($dminline);print_r($dmaxline);
			//echo "右 ".$minb." - ".$maxb."<br>";
			# Output the chart	
            header("Content-type: image/png");
            print($c->makeChart2(PNG));exit;				
			file_put_contents($params['url'],$c->makeChart2(PNG));
			//header("Content-type: image/png");
			//echo $c->makeChart2(PNG);
			
			}	
		}

	public function barchart($params,$mes,$dao) {
			
		   $isEnglish=$params["isEnglish"];
		   if($isEnglish){
			   $mes=EN_DATASOURCEMES;
			   $junzhi="AVG";
		   }else{
			   $mes=CN_DATASOURCEMES;
			   $junzhi="均值";
		   }
		    require_once("/usr/local/www/libs/ChartDirector/lib/phpchartdir.php");
			# The data for the bar chart
			//==2012-10-22
			$n=count($params['data']);
			# The labels for the line chart
			//$labels=$params['xlabel'];
			$ytwoflag=$params['twoy'];
			$lendtitle=$params['lendtitle'];
			$yaxistitle=$params['yaxistitle'];
			//$labels=$params['xlabel'];
            $nnum=count($params['data'],1)-$n;   //统计柱的总数，方便分类进行处理    
			                                              // Added for 柱状图 by zhangcun started  2016/08/26   
/*         
		 if($nnum>=110)                          //对柱形图进行处理，如果数据超过50条图形会黑掉             
		{
            $dataw=array();
			$datew=array();

			  if($nnum<400)  $type=20;                      //对柱形图进行处理，根据数据量来决定取的类型
			  elseif($nnum<3000) $type=65;
			  else $type=261;

			  for($k=0;$k<$n;$k++)             //k代表有几条柱形图的线
			{    
			   $num=count($params['data'][$k]);    //num表示第k条线的元素个数
			   for($i=0;$i<$num;$i+=$type)
			   {
				  $mark=(int)($i/$type);
                  $dataw[$k][$mark]=0;
				  for($j=0;$j<$type;$j++)                 
				  {
					 if($i+$j>=$num) break;
					 $dataw[$k][$mark]+=$params['data'][$k][$i+$j];
				  }
				  $dataw[$k][$mark]=(int)($dataw[$k][$mark]/$j);
				  $datew[$mark]=$params['xlabel'][$i];
			   }
			}
		    
		} 
		else {
			$dataw=$params['data'];
			$datew=$params['xlabel'];
		}
*/
            $dataw=$params['data'];
	    $datew=$params['xlabel'];                                                        // Added for 柱状图 by zhangcun ended  2016/08/26
            $labels=$datew;

			//取配色方案
			$linecolor=$this->getcolor($params,$dao);





			
			if($shape=='big'){
			    //图片长宽
				$pic1_1=600;
				$pic1_2=350;
				 //图表长宽高
				$pic2_1=55;//左间距
				$pic2_2=68;//距离顶部
				$pic2_3=460;//宽
				$pic2_4=200;//长
				//钢之家数据来源那段话位置
				$pic3_1=415;//左间距
				$pic3_2=45;//距离顶部
				$pic3_font=10;//字体大小
			   //线条名的位置
				$pic4_1=50;//左间距
				$pic4_2=290;//距离顶部
				$pic4_font=10;//字体大小
				//轴上字大小
                $xy_font=10;
				//标题大小
				$title_font=15;
			}else if($shape=='small'){
			  
			}else{
				//图片长宽
				$pic1_1=485;
				$pic1_2=279;
				//图表长宽高
				$pic2_1=50;//左间距
				$pic2_2=50;//距离顶部
				$pic2_3=380;//宽
				$pic2_4=160;//长
				//钢之家数据来源那段话位置
				$pic3_1=370;//左间距
				$pic3_2=30;//距离顶部
				$pic3_font=8.5;//字体大小
				//线条名的位置
				$pic4_1=40;//左间距
				$pic4_2=230;//距离顶部
				$pic4_font=8.5;//字体大小
				//轴上字大小
				$xy_font=10;
				//标题大小
                $title_font=13;
			}


			// 2012-10-22
			# Create a XYChart object of size 540 x 375 pixels
			$c = new XYChart($pic1_1, $pic1_2);
			$c->setDefaultFonts(CREATE_IMG_TTF_PATH);
			# Add a title to the chart using 18 pts Times Bold Italic font
			$c->addTitle($params['charttitle'], CREATE_IMG_TTF_PATH, $title_font);

            if($params['isjz']=="1"){
				for($i=0;$i<count($params['data']);$i++)
				{
					$avg[$i]=round(array_sum($params['data'][$i])/count($params['data'][$i]),2);
					$avg_print[$i]="  ".$junzhi.":".$avg[$i];
				}
			}
			//print"<pre>";print_r($params);
			//print"<pre>";print_r($dataw);
			//print"<pre>";print_r($datew);

			# Set the plotarea at (50, 55) and of 440 x 280 pixels in size. Use a vertical
			# gradient color from light blue (f9f9ff) to blue (6666ff) as background. Set border
			# and grid lines to white (ffffff).
// 			$c->setPlotArea(50, 80, 460, 205, $c->linearGradientColor(0, 55, 0, 335, 0xffffff,
// 				0xe9e9e9), -1, 0xe9e9e9, 0xffffff);

           $plotarea = $c->setPlotArea($pic2_1, $pic2_2, $pic2_3,$pic2_4, 0xffffff, 0xffffff, 0xe9e9e9, 0xc0c0c0);
			
			if($params['isbg'] == "1"){}else{
 			$plotarea->setBackground2(dirname(__FILE__)."/../../images/".BG_IMG);
			}
			//$plotarea->setBackground2(dirname(__FILE__)."/../../images/".BG_IMG);
				
				
			//$legendObj = $c->addLegend(60, 20, false, CREATE_IMG_TTF_PATH, 10);
			# Add a legend box at (50, 28) using horizontal layout. Use 10pts Arial Bold as font,
			# with transparent background.
		
			$textBoxObj = $c->addText($pic3_1, $pic3_2, $mes, CREATE_IMG_TTF_PATH, $pic3_font, 0x000000);
			$textBoxObj->setAlignment(TopRight);
			$legendObj = $c->addLegend($pic4_1, $pic4_2, false, CREATE_IMG_TTF_PATH, $pic4_font);//线条名的位置
			$legendObj->setBackground(Transparent);

			# Set the x axis labels
			//$c->xAxis->setLabels($labels);

			# Draw the ticks between label positions (instead of at label positions)
			$c->xAxis->setTickOffset(0.05);

			# Set axis label style to 8pts Arial Bold
			$c->xAxis->setLabelStyle(CREATE_IMG_TTF_PATH, $xy_font,TextColor);
			$c->yAxis->setLabelStyle(CREATE_IMG_TTF_PATH, $xy_font);
			//$c->yAxis2->setLabelStyle(CREATE_IMG_TTF_PATH, 2);
			# Set axis line width to 2 pixels
			$c->xAxis->setWidth(1);
			$c->yAxis->setWidth(1);


			
			# Add axis title
			// $c->yAxis->setTitle(iconv("GB2312", "UTF-8","产量"),CREATE_IMG_TTF_PATH,15);
			
			# Add a multi-bar layer with 3 data sets
			$layer = $c->addBarLayer2(Side);
			$layer->setBorderColor(Transparent);
			//print"<pre>";print_r($params);
			for($i=0;$i<$n;$i++)
			{
				$dmin[$i]=min($dataw[$i]);
				$dmax[$i]=max($dataw[$i]);
				
			    $layer->addDataSet($dataw[$i],$linecolor[$i], $this->getmytitle($lendtitle[$i].$avg_print[$i],$i),CREATE_IMG_TTF_PATH);
			}

			$i=$this->getmin($dmin);
			$j=$this->getmax($dmax);
			if($i<0||$i-round(($j-$i)/4,1)>=0) {  
			  $ii=$i-round(($j-$i)/4,1); 
			}else{
              $ii=0;
			}

			if($j>=0||$j+round(($i-$j)/4,1)<=0) {  
			  $jj=$j+round(($j-$i)/4,1); 
			}else{
              $jj=0;
			}
			
			$c->yAxis->setLinearScale($ii,$jj);
			$textBoxObj = $c->yAxis->setTitle($params['dw'][0],CREATE_IMG_TTF_PATH);
			$textBoxObj->setAlignment(TopLeft2);
			
// 			$min = ceil((min($dmin)-0.10*min($dmin))/10)*10;
// 			$max = ceil((max($dmax)+0.10*max($dmax))/10)*10;
// 			//echo $min."------".$max;
// 			$c->yAxis->setLinearScale($min,$max);
			// $layer->addDataSet($data2, 0xff8800, "Server #3");
			if(count($labels)<=12) {
				if($params['bar_datetype']['data']<=3&&$params['issametype']==1){
                  foreach($labels as $id=>$val){
				    $c->xAxis->addLabel($id,$val);
				  }
				}
				elseif(count($labels)<=4){
			    foreach($labels as $id=>$val){
				   $c->xAxis->addLabel($id,$val);
				 }
				}else{
                   $c->xAxis->setLabels($labels);
                   $countlabel=count($datew);
			       $stepcount=(int)($countlabel/4);
			       $c->xAxis->setLabelStep($stepcount);
				}
			}else{
				$c->xAxis->setLabels($labels);
				$countlabel=count($datew);
				$stepcount=(int)($countlabel/4);
				$c->xAxis->setLabelStep($stepcount);
			}
			# Set bar border to transparent. Use glass lighting effect with light direction from
			# left.
			//$layer->setBarShape(CircleShape);
			# Configure the bars within a group to touch each others (no gap)
			//$layer->setBarGap(0.2, TouchBar);
			header("Content-type: image/png");
            print($c->makeChart2(PNG));exit;	
			file_put_contents($params['url'],$c->makeChart2(PNG));
			//header("Content-type: image/png");
			//print($c->makeChart2(PNG));
	}
    public function linebarchart($params,$mes,$dao)
	{     
		   $isEnglish=$params["isEnglish"];
		   if($isEnglish){
			   $mes=EN_DATASOURCEMES;
			   $junzhi="AVG";
		   }else{
			   $mes=CN_DATASOURCEMES;
			   $junzhi="均值";
		   }
		    require_once("/usr/local/www/libs/ChartDirector/lib/phpchartdir.php");
			# The data for the bar chart
			//==2012-10-22	
					
			$nline=count($params['data'][1]); //线
			$nbar=count($params['data'][0]);  //柱
			$datatemp[0]=$params['data'][1];  //线
			$datatemp[1]=$params['data'][0];  //柱
			$nnum=count($params['data'][0],1)-$nbar; //柱的总数量
			
			//print"<pre>";print_r($params);exit;
			//add by zhangcun 2017/7/31 for 右轴选择 功能：柱图能够选择左右轴，$pos代表柱图的坐标轴选择
			$amout=$nline+$nbar;
			$pos="left";
			$k=0;
			for($i=0;$i<$amout;$i++){
				$z="zhou".($i+1);
				if($params['isbar'][$i]==1) $k++;
				if($params['isbar'][$i]==1&&$params[$z]==1) {
					$pos="right";  continue;
				}
			}
			if($k==0) $pos="right";				//
			if($nline==0) $pos="left";
			//统计柱的总数，方便分类进行处理     Added for 柱线图 by zhangcun started  2016/08/23   
          
/*		    if($nnum>=110)                          //对柱形图进行处理，如果数据超过50条图形会黑掉             
		   {
            $dataw=array();
			$dataw2=array();
			$datew=array();

			  if($nnum<400)  $type=20;                      //对柱形图进行处理，根据数据量来决定取的类型
			  elseif($nnum<3000) $type=65;
			  else $type=261;

			  for($k=0;$k<$nbar;$k++)             //k代表有几条柱形图的线
			 {    
			   $num=count($params['data'][0][$k]);    //num表示第k类柱的元素个数
			   for($i=0;$i<$num;$i+=$type)
			   {
				  $mark=(int)($i/$type);
                  $dataw[$k][$mark]=0;
				  $dataw2[$k][$mark]=0;
				  for($j=0;$j<$type;$j++)                 
				  {
					 if($i+$j>=$num) break;
					 $dataw[$k][$mark]+=$datatemp[1][$k][$i+$j];
				  }
				  $dataw[$k][$mark]=(int)($dataw[$k][$mark]/$j);
				  $datew[$mark]=$params['xlabel'][$i];
			   }
			 }

			for($k=0;$k<$nline;$k++)
			{    
			   $num=count($params['data'][1][$k]);    //num表示第k条线的元素个数
			   for($i=0;$i<$num;$i+=$type)
			   {
				  $mark=(int)($i/$type);
				  $dataw2[$k][$mark]=0;
				  for($j=0;$j<$type;$j++)                 
				  {
					 if($i+$j>=$num) break;                        //当数据取完，就退出循环
					 $dataw2[$k][$mark]+=$datatemp[0][$k][$i+$j];
				  }
				  $dataw2[$k][$mark]=(int)($dataw2[$k][$mark]/$j);   //有可能中途结束，故用j表示实际数目
			   }
			}	   
			  $datatemp[1]=$dataw;
			  $datatemp[0]=$dataw2;
		}else{
			$datew=$params['xlabel'];
		}															// Added for 柱线图 by zhangcun ended  2016/08/23
*/			$datew=$params['xlabel'];

		    //print"<pre>";print_r($dataw);
			//print"<pre>";print_r($params);
			//print"<pre>";print_r($datew);

			if($params['isjz'] == "1") {                                             //判断均值是否显示 add by zhangcun 2016/08
			$avg="";
			$avg_print=array();
			foreach($datatemp as $kk=>$vv)
			  foreach($vv as $ii=>$arr)
				{
			      $avg[$kk]=round(array_sum($datatemp[$kk][$ii])/count($datatemp[$kk][$ii]),2);
			      $avg_print[$kk][$ii]="  ".$junzhi.":".$avg[$kk];
			    }
			}
		     //print"<pre>";print_r($params);print_r($avg);
			// print_r($params);
			# The labels for the line chart
			$ytwoflag=$params['twoy'];
			$lendtitle=$params['lendtitle'];
            $labels=$datew;

			//$linecolor=array(0xfb0303,0xa260e6,0x575ae1,0x66dede,0x8B008B);			
			//$linecolor1=array(0x1007bd,0x00d5f5,0xffc000);
		/*	if($nbar==0){
				$linecolor1 = array(0xff0000,0x1007bd,0xffc000,0xa004c1,0x10c358);
			}else{
				$linecolor1=array(0x1007bd,0xffc000,0x10c358,0x66dede,0x10c358);
			}
		*/
		    //取配色方案
			$linecolor=$this->getcolor($params,$dao);
			foreach ($params['isbar'] as $key => $value) {
				if($value==0){
					$linecolor1[]=$linecolor[$key];
				}else if($value==1){
					$linecolor2[]=$linecolor[$key];
				}
			}
		
			// $linecolor1线图先处理，$linecolor2柱状图处理

			
			if($shape=='big'){
			    //图片长宽
				$pic1_1=600;
				$pic1_2=350;
				 //图表长宽高
				$pic2_1=55;//左间距
				$pic2_2=68;//距离顶部
				$pic2_3=460;//宽
				$pic2_4=200;//长
				//钢之家数据来源那段话位置
				$pic3_1=415;//左间距
				$pic3_2=45;//距离顶部
				$pic3_font=10;//字体大小
			   //线条名的位置
				$pic4_1=50;//左间距
				$pic4_2=290;//距离顶部
				$pic4_font=10;//字体大小
				//轴上字大小
                $xy_font=10;
				//标题大小
				$title_font=15;
			}else if($shape=='small'){
			  
			}else{
				//图片长宽
				$pic1_1=485;
				$pic1_2=279;
				//图表长宽高
				$pic2_1=50;//左间距
				$pic2_2=50;//距离顶部
				$pic2_3=380;//宽
				$pic2_4=160;//长
				//钢之家数据来源那段话位置
				$pic3_1=370;//左间距
				$pic3_2=30;//距离顶部
				$pic3_font=8.5;//字体大小
				//线条名的位置
				$pic4_1=40;//左间距
				$pic4_2=230;//距离顶部
				$pic4_font=8.5;//字体大小
				//轴上字大小
				$xy_font=10;
				//标题大小
                $title_font=13;
			}
			//$linecolor1=array(0x66dede,0x8B008B,0x5aeb8e);
			// 2012-10-22
			# Create a XYChart object of size 540 x 375 pixels
			$c = new XYChart($pic1_1, $pic1_2);//图片大小
			$c->setDefaultFonts(CREATE_IMG_TTF_PATH);
			# Add a title to the chart using 18 pts Times Bold Italic font
			$c->addTitle($params['charttitle'], CREATE_IMG_TTF_PATH, $title_font);
			// $liukai;
			# Set the plotarea at (50, 55) and of 440 x 280 pixels in size. Use a vertical
			# gradient color from light blue (f9f9ff) to blue (6666ff) as background. Set border
			# and grid lines to white (ffffff).
		
			$plotarea = $c->setPlotArea($pic2_1, $pic2_2, $pic2_3,$pic2_4, 0xffffff, 0xffffff, 0xe9e9e9, 0xc0c0c0);//图表的大小长宽

			//$plotarea = $c->setPlotArea(50, 90, 500, 200, 0xeeeeee, 0xffffff, 0xe9e9e9, 0xc0c0c0);
			
			if($params['isbg'] == "1"){}else{
 			$plotarea->setBackground2(dirname(__FILE__)."/../../images/".BG_IMG);
			}
			//$plotarea->setBackground2(dirname(__FILE__)."/../../images/".BG_IMG);
			$textBoxObj = $c->addText($pic3_1, $pic3_2, $mes, CREATE_IMG_TTF_PATH, $pic3_font, 0x000000);
			$textBoxObj->setAlignment(TopRight);
				
			$legendObj = $c->addLegend($pic4_1, $pic4_2, false, CREATE_IMG_TTF_PATH, $pic4_font);
			# Add a legend box at (50, 28) using horizontal layout. Use 10pts Arial Bold as font,
			# with transparent background.
			$legendObj->setBackground(Transparent);
			# Set the x axis labels
			//$c->xAxis->setLabels($labels);
			# Draw the ticks between label positions (instead of at label positions)
			
			$c->xAxis->setTickOffset(0.05);
			//$c->yAxis->setTitle("blue");
			//$c->yAxis2->setTitle("red");
			
			# Set axis label style to 8pts Arial Bold
			$c->xAxis->setLabelStyle(CREATE_IMG_TTF_PATH, $xy_font,TextColor);
			$c->yAxis->setLabelStyle(CREATE_IMG_TTF_PATH, $xy_font);
			$c->yAxis2->setLabelStyle(CREATE_IMG_TTF_PATH, $xy_font);
			$dminline=array();
			$dmaxline=array();
			for($i=0;$i<$nline;$i++)
			{
				$layer="layer".$i;
				$$layer = $c->addLineLayer2();
				$$layer->setLineWidth(2);
				$datat=$datatemp[0][$i];
				$$layer->addDataSet($datat,$linecolor1[$i], $this->getmytitle($lendtitle[1][$i].$avg_print[0][$i],$i));
				$dminline[$i]=$this->getmin($datatemp[0][$i]);
				$dmaxline[$i]=$this->getmax($datatemp[0][$i]);
				
				if($nbar==!0){					
					if($this->getmin($dminline)>0){
						$minb = 0.95*$this->getmin($dminline);
					}else{
						$minb = 1.05*$this->getmin($dminline);
					}
					if($this->getmax($dmaxline)>0){
						$maxb = 1.05*$this->getmax($dmaxline);
					}else{
						$maxb = 0.95*$this->getmax($dmaxline);
					}
					if($pos=="right"){
						$c->yAxis->setLinearScale($minb,$maxb);
					}elseif($pos=="left"){
						$$layer->setUseYAxis2();
						$c->yAxis2->setLinearScale($minb,$maxb);
					}
				}
			  }
			//$m=$i;
			//$m++;

//$textBoxObj = $c->yAxis2->setTitle("ceshi",CREATE_IMG_TTF_PATH);
//$textBoxObj->setAlignment(TopRight2);
			for($i=0,$j=0,$unit="";$i<4;$i++)                                                         //add for 柱线图 by zhangcun 2016/8/29
		   {
				if($params['isbar'][$i]==1)
			   {
				   switch($j)
				   {
					 case 0: 
					   if($pos=="right"){
						   $textBoxObj = $c->yAxis2->setTitle("柱(".$params['dw'][$i].")",CREATE_IMG_TTF_PATH);
						   $textBoxObj->setAlignment(TopRight2);
                       }elseif($pos=="left"){
                          $textBoxObj = $c->yAxis->setTitle("柱(".$params['dw'][$i].")",CREATE_IMG_TTF_PATH);
						   $textBoxObj->setAlignment(TopLeft2);
					   }
				       $j=1;break;
					 default :  break;
				   }
			   }
			   else if($params['isbar'][$i]==0)
			   {
				   switch($j)
				   {
                     case 1:
					   if($pos=="right"){
						   $textBoxObj = $c->yAxis->setTitle("折(".$params['dw'][$i].")",CREATE_IMG_TTF_PATH);
						   $textBoxObj->setAlignment(TopLeft2);
				       }elseif($pos=="left"){
						   $textBoxObj = $c->yAxis2->setTitle("折(".$params['dw'][$i].")",CREATE_IMG_TTF_PATH);
						   $textBoxObj->setAlignment(TopRight2);
					   }
					   $i=10; break;
					 default : $unit=$params['dw'][$i]; break;
				   }			   
			   }

			   if($i==($nline-1)&&$j==0&&$nbar>0){
				       if($pos=="right"){
						   $textBoxObj = $c->yAxis->setTitle("折(".$unit.")",CREATE_IMG_TTF_PATH);
						   $textBoxObj->setAlignment(TopLeft2);
				       }elseif($pos=="left"){
						   $textBoxObj = $c->yAxis2->setTitle("折(".$unit.")",CREATE_IMG_TTF_PATH);
						   $textBoxObj->setAlignment(TopRight2);
					   }
			   }
			   elseif($i==($nline-1)&&$j==0&&$nbar==0) {
                       if($pos=="right"){
						   $textBoxObj = $c->yAxis->setTitle("折(".$unit.")",CREATE_IMG_TTF_PATH);
						   $textBoxObj->setAlignment(TopLeft2);
					   }elseif($pos=="left"){
						   $textBoxObj = $c->yAxis2->setTitle("折(".$unit.")",CREATE_IMG_TTF_PATH);
						   $textBoxObj->setAlignment(TopRight2);
					   }
			   }
		   }
           //print"<pre>";print_r($params);
			//柱状图
			$layerbar = $c->addBarLayer2(Side);
			$layerbar->setBorderColor(Transparent);
			
			/*if($nline==0){
				$linecolor2 = array(0xff0000,0x1007bd,0xffc000,0xa004c1,0x10c358);
			}else{
				$linecolor2=array(0xff0000,0xa004c1,0x10c358,0xffc000,0x1007bd);
			}*/
			// if($params['color']=="彩色"||$params['color']=="0"){
			// 	$linecolor2=array(0x4F81BD,0x9BBB59,0x8166A2,0xC0504D);
			// }
			// elseif($params['color']=="蓝色"||$params['color']=="1")
			// {
            //     $linecolor2=array(0xB6C3DC,0x7E9BC8,0x4978B1,0x3C6494);
			// }else{
            //     $linecolor2=array(0x4F81BD,0x9BBB59,0x8166A2,0xC0504D);
			// }
			//$linecolor2=array(0xfb0303,0xa260e6,0x575ae1);
			$dminbar=array();
			$dmaxbar=array();
			for($i=0;$i<$nbar;$i++)
			{
 				
				$dminbar[$i]=$this->getmin($datatemp[1][$i]);
				$dmaxbar[$i]=$this->getmax($datatemp[1][$i]);
				//print_r($dminbar);
				//print_r($dmaxbar);
				$layerbar->addDataSet($datatemp[1][$i],$linecolor2[$i], $lendtitle[0][$i].$avg_print[1][$i],CREATE_IMG_TTF_PATH);
				$layerbar->setBarGap(0.2, TouchBar);
				//$min = ceil((min($dminbar)-0.10*min($dminbar))/10)*10;
				//$max = ceil((max($dmaxbar)+0.10*max($dmaxbar))/10)*10;
				//$c->yAxis->setLinearScale($min,$max);
				if($this->getmin($dminbar)>0){
					$mina = 0.95*$this->getmin($dminbar);
				}else{
					$mina = 1.05*$this->getmin($dminbar);
				}
				if($this->getmax($dmaxbar)>0){
					$maxa = 1.05*$this->getmax($dmaxbar);
				}else{
					$maxa = 0.95*$this->getmax($dmaxbar);
				}
				if($pos=="right"){
					$layerbar->setUseYAxis2();
					$c->yAxis2->setLinearScale($mina,$maxa);
				}elseif($pos=="left"){
					$c->yAxis->setLinearScale($mina,$maxa);
				}
				if($nline!=0){
				}
				$m++;
			}
			//echo "位置[柱]：".$pos."<br>";
			//echo "线: ".$minb."-".$maxb."<br>";
			//echo "柱: ".$mina."-".$maxa."<br>";

			//$textBoxObj->setAlignment(TopLeft2);
			//print_r($datatemp[0][0]);
			//if($nline==0){
			//	$countlabel=count($datatemp[1][0]);
			//}else{
			//	$countlabel=count($datatemp[0][0]);
			//}
			
			//echo $countlabel;exit;
			//$stepcount=round($countlabel/6,0);
			//$c->xAxis->setLabelStep($stepcount);
			if(count($labels)<=12){
				if($params['bar_datetype']['data']<=3&&$params['issametype']==1){
                  foreach($labels as $id=>$val){
				    $c->xAxis->addLabel($id,$val);
				  }
				}
				elseif(count($labels)<=4){
			    foreach($labels as $id=>$val){
				   $c->xAxis->addLabel($id,$val);
				 }
				}else{ 
                   $c->xAxis->setLabels($labels);
                   $countlabel=count($datew);
			       $stepcount=round($countlabel/4,0);
				   
			       $c->xAxis->setLabelStep($stepcount);
				}
			}else{
				$c->xAxis->setLabels($labels);
				if($nline==0){
					$countlabel=count($datatemp[1][0]);
				}else{
					$countlabel=count($datatemp[0][0]);
				}
				$stepcount=round($countlabel/4,0);
				$c->xAxis->setLabelStep($stepcount);
			}
            header("Content-type: image/png");
            print($c->makeChart2(PNG));exit;	
			file_put_contents($params['url'],$c->makeChart2(PNG));
 			
	}

	public function piechart($params,$mes) 
	{  
	   $isEnglish=$params["isEnglish"];
	   if($isEnglish){
		   $mes=EN_DATASOURCEMES;
		   $junzhi="AVG";
	   }else{
		   $mes=CN_DATASOURCEMES;
		   $junzhi="均值";
	   }
	   //print"<pre>";print_r($params);
	   require_once("/usr/local/www/libs/ChartDirector/lib/phpchartdir.php");
	   $lendtitle=$params['lendtitle'];
	   //$params['xlabel']['10']='16/09';
	   //$colors=array(0x3370CC,0xE66B1A,0x44BB5C,0x8DEEEE);
	   $colors=array(0x3C57C4,0xE6421A,0x70CC33,0x764DB3);
	   //$colors=array(0xff332c ,0x1992fd ,0x2cc417,0xFEC34D,0xff9c01  );
	  foreach($lendtitle as $id=>$t)
	  {
		   $ii=explode("-",$t);
		   $mark[]=count($ii)-1;
		   $title[]=$ii[count($ii)-1];
      }
	           //数据转码处理，把gbk转换为utf-8格式，否则显示的是乱码（只限于每个扇区显示）
       foreach($title as $id=>$arr)  
	  {
		   $lendtitle[$id]= $arr;
	  }

	   foreach($params['data'] as $id=>$arr)   //取前一个月的数据，如果没数据就显示无
	  {
		 $val[] = $arr[count($arr)-1];
	  }
	   $val[0]=$val[0]-$val[1]-$val[2]-$val[3]; //第一个表示其他数据
	   foreach($lendtitle as $id=>$tt)
	  {
		  if($id!=0){
	          $labels[]=$tt." ".$val[$id];
		      $data[]=$val[$id];
	          }
	  }
	   $newtitle=explode("（",$lendtitle[0]);
	   //print"<pre>";print_r($newtitle);
	   $lendtitle=explode("(",$newtitle[0]);
	   //print"<pre>";print_r($lendtitle);
	   //$lendtitle="";
	   //$lendtitle=$newtitle[0];
	   $qita="其他";
	   if($isEnglish) $qita="Others";
	   $labels[]= $qita." ".$val[0];
	   $data[]=$val[0];
       //print"<pre>";print_r($labels);
	  if($params['isbg']==1) {
	  $c->setBackground($c->linearGradientColor(0, 0, 0, 100, 0x99ccff, 0xffffff), 0x888888);
	  }
	//   else{
	//   $c->setBackground($c->patternColor(dirname(__FILE__)."/../../images/".BG_IMG), 0x000000, 1);
	//   }
	   // 单位和时间添加

	   $unit= "(".$params['dw'][0].")";
	   $date=$params['xlabel'][count($params['xlabel'])-1];
	   $t=$params['date'];

	   if($this->compareAB($date,$params['xlabel'][count($params['xlabel'])-2],$t)==false) {
          $data="";
		  $labels="";
		  $unit="";
		  $c = new PieChart(600, 350);
		  $textBoxObj = $c->addTitle("该日期无数据", CREATE_IMG_TTF_PATH, 15);
	   }else{
	        $date=explode("/",$date);
		    foreach($date as $i=>$v){
				if($i==0) $date[$i]+=2000;
				else $date[$i]+=0;
			}
	        switch(count($date)){
			case 1: $riqi=$date[0]."年"; break;
			case 2: $riqi=$date[0]."年".$date[1]."月"; break;
            case 3: $riqi=$date[0]."年".$date[1]."月".$date[2]."日"; break;
		}
		
	    $c = new PieChart(600, 350);
	    //$riqi=iconv("GB2312","UTF-8", $riqi);
		$textBoxObj = $c->addTitle($riqi.$lendtitle[0].$unit, CREATE_IMG_TTF_PATH, 15);
	   }
       $c->setLabelStyle(CREATE_IMG_TTF_PATH,8,0x000000);
	   $c->setRoundedFrame();
	   //$c->setDropShadow();
	   # Set the center of the pie at (150, 150) and the radius to 80 pixels
	   $c->setPieSize(250, 170, 100);
	   # Set the sector label position to be 20 pixels from the pie. Use a join line to
	   # connect the labels to the sectors.
	   $c->setLabelPos(20, LineColor);

       //$c->set3D(25);
	   # Set the pie data and the pie labels
	   //if(count($params['data'])==1) $labels=iconv("GB2312","UTF-8", "总计 ".array_sum($val));
	   //print"<pre>";print_r($params);print_r($labels);
	   $c->setData($data, $labels);
	   # Set the sector colors
	   $c->setColors(0xffffff);
	   $c->setColors2(DataColor, $colors);
	   # Use local gradient shading, with a 1 pixel semi-transparent black (bb000000) border
	   $c->setSectorStyle(LocalGradientShading, 0xbb000000, 1);
	   $textBoxObj = $c->addText(250, 325, $mes, CREATE_IMG_TTF_PATH, 10, 0x000000);
	   # Output the chart
		header("Content-type: image/png");
		print($c->makeChart2(PNG));exit;
	   file_put_contents($params['url'],$c->makeChart2(PNG));
	   //print"<pre>";print_r($lendtitle);
	}

    public function pie2chart($params,$mes)
	{
	   $isEnglish=$params["isEnglish"];
	   if($isEnglish){
		   $mes=EN_DATASOURCEMES;
		   $junzhi="AVG";
	   }else{
		   $mes=CN_DATASOURCEMES;
		   $junzhi="均值";
	   }
		require_once("/usr/local/www/libs/ChartDirector/lib/phpchartdir.php");
	   $lendtitle=$params['lendtitle'];
	   $data = array(2, 15,63,20 );
       //$data = array(21, 18, 15, 12, 8, 24);
       //$labels = array("Labor", "Licenses", "Taxes", "Legal", "Facilities", "Production");
	   foreach($params['lendtitle'] as $id=>$title){
	   $labels[]=$title;
	   } 
	   //print"<pre>";print_r($labels);
	   $colors = array(0xB8860B, 0x76EE00, 0x836FFF, 0x54FF9F, 0xDAA520, 0x009900);
	   $c = new PieChart(550, 320);

	   if($params['isbg']==0) {
	   $c->setBackground($c->linearGradientColor(0, 0, 0, 100, 0x99ccff, 0xffffff), 0x888888);
	   }else{
	   $c->setBackground($c->patternColor(dirname(__FILE__)."/../../images/".BG_IMG), 0x000000, 1);
	   }
	   $c->setRoundedFrame();
	   $c->setDropShadow();
	   //$c->setLabelStyle(CREATE_IMG_TTF_PATH,7,0x000000);
	   //$c->setLabelStyle("宋体",9,0x20FF0000);
       $textBoxObj = $c->addTitle($params['charttitle'], CREATE_IMG_TTF_PATH, 15);
       $textBoxObj->setMargin2(0, 0, 16, 0);

	   $c->setPieSize(150, 150, 100);
       $c->set3D(25); 
	   $c->setData($data, $labels);
	   $c->setColors2(DataColor, $colors);	
	   $c->setSectorStyle(LocalGradientShading, 0xbb000000, 1);
	   //$c->setSectorStyle(LocalGradientShading);
	   //$c->setExplode(2, 40);

       $c->setLabelFormat("{={sector}+1}");
       $textBoxObj = $c->setLabelStyle(CREATE_IMG_TTF_PATH, 10);
       $textBoxObj->setBackground(Transparent, 0x444444);

	   $b = $c->addLegend(330, 175, true, CREATE_IMG_TTF_PATH, 10);
       $b->setAlignment(Left);

       $b->setBackground(Transparent, 0x444444);
       $b->setRoundedCorners();

       $b->setMargin(16);
       $b->setKeySpacing(0, 5);

       $b->setKeyBorder(SameAsMainColor);

       $b->setText(
       "<*block,valign=top*>{={sector}+1}.<*advanceTo=22*><*block,width=120*>{label}<*/*>".
       "<*block,width=40,halign=right*>{percent}<*/*>%");

              
       //$textBoxObj = $c->addText(515, $jl, iconv("GB2312", "UTF-8",$mes),        CREATE_IMG_TTF_PATH, 10, 0x000000);
	   //$textBoxObj->setAlignment(TopRight);
        header("Content-type: image/png");
		print($c->makeChart2(PNG));exit;
	   file_put_contents($params['url'],$c->makeChart2(PNG));
	}
	public function newpiechart($params,$mes,$dao)
	{   


	   $isEnglish=$params["isEnglish"];
	   if($isEnglish){
		   $mes=EN_DATASOURCEMES;
		   $junzhi="AVG";
	   }else{
		   $mes=CN_DATASOURCEMES;
		   $junzhi="均值";
	   } 
		require_once("/usr/local/www/libs/ChartDirector/lib/phpchartdir.php");
	   $lendtitle=$params['lendtitle'];

	   $avg="";
	   $avg_print=array();
	   for($i=0;$i<count($params['data']);$i++)
	   {
		   $data[$i]=round(array_sum($params['data'][$i])/count($params['data'][$i]),2);
	   }
		if($shape=='big'){
			//图片长宽
			$pic1_1=600;
			$pic1_2=350;
			//图表长宽高
			$pic2_1=290;//左间距
			$pic2_2=160;//距离顶部
			$pic2_3=100;//宽
		
			//钢之家数据来源那段话位置
			$pic3_1=415;//左间距
			$pic3_2=45;//距离顶部
			$pic3_font=10;//字体大小
		//线条名的位置
			$pic4_1=70;//左间距
			$pic4_2=305;//距离顶部
			$pic4_font=10;//字体大小
			//轴上字大小
			$xy_font=10;
			//标题大小
			$title_font=15;
		}else if($shape=='small'){
		
		}else{
			//图片长宽
			$pic1_1=485;
			$pic1_2=279;
			//图表长宽高
			$pic2_1=237;
			$pic2_2=135;
			$pic2_3=80;//大小
		
			//钢之家数据来源那段话位置
			$pic3_1=370;//左间距
			$pic3_2=30;//距离顶部
			$pic3_font=8.5;//字体大小
			//线条名的位置
			$pic4_1=60;//左间距
			$pic4_2=250;//距离顶部
			$pic4_font=8.5;//字体大小
			//轴上字大小
			$xy_font=10;
			//标题大小
			$title_font=13;
		}
		$colors=$this->getcolor($params,$dao);
       //$data = array(21, 18, 15, 12, 8, 24);
       //$labels = array("Labor", "Licenses", "Taxes", "Legal", "Facilities", "Production");
	   foreach($params['lendtitle'] as $id=>$title){
	   $labels[]= $title;
	   } 
	   //print"<pre>";print_r($labels);
	   $c = new PieChart($pic1_1, $pic1_2);

	   if($params['isbg']==0) {
	//    $c->setBackground($c->linearGradientColor(0, 0, 0, 100, 0x99ccff, 0xffffff), 0x888888);
	   }else{
	   $c->setBackground($c->patternColor(dirname(__FILE__)."/../../images/".BG_IMG), 0x000000, 1);
	   }
	   $c->setRoundedFrame();
	   // $c->setDropShadow();//背景阴影
	   //$c->setLabelStyle(CREATE_IMG_TTF_PATH,7,0x000000);
	   //$c->setLabelStyle("宋体",9,0x20FF0000);
       $textBoxObj = $c->addTitle($params['charttitle'], CREATE_IMG_TTF_PATH, $title_font);
       $textBoxObj->setMargin2(0, 0, 16, 0);
	   $c->setLabelStyle(CREATE_IMG_TTF_PATH,$pic4_font,0x000000);
	   $c->setRoundedFrame();
	   $c->setPieSize($pic2_1,$pic2_2,$pic2_3);
	//    $c->setLabelPos(20, LineColor);
    //    $c->set3D(25); 
	   $c->setData($data, $labels);
	   $c->setColors(0xffffff);
	   $c->setColors2(DataColor, $colors);	
	   $c->setSectorStyle(LocalGradientShading, 0xbb000000, 1);
	   //$c->setSectorStyle(LocalGradientShading);
	   //$c->setExplode(2, 40);
	 

    //    $c->setLabelFormat("{={sector}+1}");//扇形标注
       $c->setLabelFormat("{percent}%");//扇形标注
       $textBoxObj = $c->setLabelStyle(CREATE_IMG_TTF_PATH, 10);
	   
	//    $textBoxObj = $c->setLabelStyle("arialbd.ttf", 10);

       $textBoxObj->setBackground(Transparent, 0x444444);
	 

	   $b = $c->addLegend($pic4_1, $pic4_2, false, CREATE_IMG_TTF_PATH, $pic4_font);
       $b->setAlignment(Left);

       $b->setBackground(Transparent);
    //    $b->setRoundedCorners();

    //    $b->setMargin(16);
    //    $b->setKeySpacing(0, 5);

    //    $b->setKeyBorder(SameAsMainColor);

    //    $b->setText(
    //    "<*block,valign=top*>{={sector}+1}.<*advanceTo=22*><*block,width=120*>{label}<*/*>".
    //    "<*block,width=40,halign=right*>{percent}<*/*>%");

              
       //$textBoxObj = $c->addText(515, $jl, iconv("GB2312", "UTF-8",$mes),        CREATE_IMG_TTF_PATH, 10, 0x000000);
	   //$textBoxObj->setAlignment(TopRight);
        header("Content-type: image/png");
		print($c->makeChart2(PNG));exit;
	   file_put_contents($params['url'],$c->makeChart2(PNG));
	}

    public function stackedbarchart($params,$mes,$dao) //Created by zhangcun for 堆积柱图  2016/09/07
	{      
			$isEnglish=$params["isEnglish"];
		    if($isEnglish){
			   $mes=EN_DATASOURCEMES;
			   $junzhi="AVG";
		    }else{
			   $mes=CN_DATASOURCEMES;
			   $junzhi="均值";
		    }
            require_once("/usr/local/www/libs/ChartDirector/lib/phpchartdir.php");
			# The data for the bar chart
			$n=count($params['data']);
			# The labels for the line chart
			$ytwoflag=$params['twoy'];
			$lendtitle=$params['lendtitle'];
			$yaxistitle=$params['yaxistitle'];
            $nnum=count($params['data'],1)-$n;      //nnum表示柱的总数          
          
	/*	 if($nnum>=110*$n)                          //对柱形图进行处理，如果柱的个数超过110条图形会黑掉             
		{
            $dataw=array();
			$datew=array();

			  if($nnum<400*$n)  $type=20;                      //对柱形图进行处理，根据数据量来决定取的类型
			  elseif($nnum<3000*$n) $type=53;
			  else $type=261;

			  for($k=0;$k<$n;$k++)             //k代表有几条柱形图的线
			{    
			   $num=count($params['data'][$k]);    //num表示第k条线的元素个数
			   for($i=0;$i<$num;$i+=$type)
			   {
				  $mark=(int)($i/$type);
                  $dataw[$k][$mark]=0;
				  for($j=0;$j<$type;$j++)                 
				  {
					 if($i+$j>=$num) break;
					 $dataw[$k][$mark]+=$params['data'][$k][$i+$j];
				  }
				  $dataw[$k][$mark]=(int)($dataw[$k][$mark]/$j);
				  $datew[$mark]=$params['xlabel'][$i];
			   }
			}
		    
		} 
		else {
			$dataw=$params['data'];
			$datew=$params['xlabel'];
		}
       */   $dataw=$params['data'];
			$datew=$params['xlabel'];                                                        
            $labels=$datew;

				
			//取配色方案
			$linecolor=$this->getcolor($params,$dao);





				
			if($shape=='big'){
				//图片长宽
				$pic1_1=600;
				$pic1_2=350;
				//图表长宽高
				$pic2_1=55;//左间距
				$pic2_2=68;//距离顶部
				$pic2_3=460;//宽
				$pic2_4=200;//长
				//钢之家数据来源那段话位置
				$pic3_1=415;//左间距
				$pic3_2=45;//距离顶部
				$pic3_font=10;//字体大小
			//线条名的位置
				$pic4_1=50;//左间距
				$pic4_2=290;//距离顶部
				$pic4_font=10;//字体大小
				//轴上字大小
				$xy_font=10;
				//标题大小
				$title_font=15;
			}else if($shape=='small'){
			
			}else{
				//图片长宽
				$pic1_1=485;
				$pic1_2=279;
				//图表长宽高
				$pic2_1=50;//左间距
				$pic2_2=50;//距离顶部
				$pic2_3=380;//宽
				$pic2_4=160;//长
				//钢之家数据来源那段话位置
				$pic3_1=370;//左间距
				$pic3_2=30;//距离顶部
				$pic3_font=8.5;//字体大小
				//线条名的位置
				$pic4_1=40;//左间距
				$pic4_2=230;//距离顶部
				$pic4_font=8.5;//字体大小
				//轴上字大小
				$xy_font=10;
				//标题大小
				$title_font=13;
			}
			# Create a XYChart object of size 540 x 375 pixels
			$c = new XYChart($pic1_1, $pic1_2);
			$c->setDefaultFonts(CREATE_IMG_TTF_PATH);
			# Add a title to the chart using 18 pts Times Bold Italic font
			$c->addTitle($params['charttitle'], CREATE_IMG_TTF_PATH, $title_font);

            if($params['isjz']=="1"){
				for($i=0;$i<count($params['data']);$i++)
				{
					$avg[$i]=round(array_sum($params['data'][$i])/count($params['data'][$i]),2);
					$avg_print[$i]="  ".$junzhi.":".$avg[$i];
				}
			}
			//print"<pre>";print_r($params);
			//print"<pre>";print_r($dataw);
			//print"<pre>";print_r($datew);

			# Set the plotarea at (50, 55) and of 440 x 280 pixels in size. Use a vertical
			# gradient color from light blue (f9f9ff) to blue (6666ff) as background. Set border
			# and grid lines to white (ffffff).
			$plotarea = $c->setPlotArea($pic2_1, $pic2_2, $pic2_3,$pic2_4, 0xffffff, 0xffffff, 0xe9e9e9, 0xc0c0c0);
			
			if($params['isbg'] == "1"){}else{
 			$plotarea->setBackground2(dirname(__FILE__)."/../../images/".BG_IMG);
			}
			
			# Add a legend box at (50, 28) using horizontal layout. Use 10pts Arial Bold as font,
			# with transparent background.
		
			$textBoxObj = $c->addText($pic3_1, $pic3_2, $mes, CREATE_IMG_TTF_PATH, $pic3_font, 0x000000);
			$textBoxObj->setAlignment(TopRight);
			$legendObj = $c->addLegend($pic4_1, $pic4_2, false, CREATE_IMG_TTF_PATH, $pic4_font);//线条名的位置
			$legendObj->setBackground(Transparent);

			# Set the x axis labels
			//$c->xAxis->setLabels($labels);

			# Draw the ticks between label positions (instead of at label positions)
			$c->xAxis->setTickOffset(0.05);

			# Set axis label style to 8pts Arial Bold
			$c->xAxis->setLabelStyle(CREATE_IMG_TTF_PATH, $xy_font,TextColor);
			$c->yAxis->setLabelStyle(CREATE_IMG_TTF_PATH, $xy_font);
			# Set axis line width to 2 pixels
			$c->xAxis->setWidth(1);
			$c->yAxis->setWidth(1);
			
			# Add a multi-bar layer with data sets
			$layer = $c->addBarLayer2(Stack);
			//print_r($params);
			for($i=0;$i<$n;$i++)
			{
				//$dmin[$i]=min($dataw[$i]);
				//$dmax[$i]=max($dataw[$i]);
				
			    $layer->addDataSet($dataw[$i],$linecolor[$i],$this->getmytitle($lendtitle[$i].$avg_print[$i],$i),CREATE_IMG_TTF_PATH);
			}
			$layer->setBorderColor(Transparent);
			$textBoxObj = $c->yAxis->setTitle($params['dw'][0],CREATE_IMG_TTF_PATH);
			$textBoxObj->setAlignment(TopLeft2);
			
            //$countlabel=count($dataw[0]);
			//$stepcount=round($countlabel/6,0);
			//$c->xAxis->setLabelStep($stepcount);
			if(count($labels)<=12) {
				if($params['bar_datetype']['data']<=3&&$params['issametype']==1){
                  foreach($labels as $id=>$val){
				    $c->xAxis->addLabel($id,$val);
				  }
				}
				elseif(count($labels)<=4){
					foreach($labels as $id=>$val){
						$c->xAxis->addLabel($id,$val);
					}
				}else{
                   $c->xAxis->setLabels($labels);
                   $countlabel=count($datew);
			       $stepcount=round($countlabel/4,0);
			       $c->xAxis->setLabelStep($stepcount);
				}
			}else{
				$c->xAxis->setLabels($labels);
				$countlabel=count($datew);
				$stepcount=round($countlabel/4,0);
				$c->xAxis->setLabelStep($stepcount);
				// $ii=count($labels)/4;
				// $c->xAxis->addlabel(0,$labels[0]);
				// for($i=0;$i<4;$i++){
				// 	$c->xAxis->addlabel(($i*$ii),$labels[($i*$ii)]);
				// }
				
				// $c->xAxis->addlabel(count($labels)-1,$labels[count($labels)-1]);
			}

			# Set bar border to transparent. Use glass lighting effect with light direction from
			# left.
			//$layer->setBarShape(CircleShape);
			# Configure the bars within a group to touch each others (no gap)
			//$layer->setBarGap(0.2, TouchBar);
			header("Content-type: image/png");
		print($c->makeChart2(PNG));exit;
			file_put_contents($params['url'],$c->makeChart2(PNG));
	}
                       //Created by zhangcun for 堆积面积图  2016/09/07
    public function stackedareachart($params,$mes,$dao) 
	{
		   $isEnglish=$params["isEnglish"];
		   if($isEnglish){
			   $mes=EN_DATASOURCEMES;
			   $junzhi="AVG";
		   }else{
			   $mes=CN_DATASOURCEMES;
			   $junzhi="均值";
		   }
            require_once("/usr/local/www/libs/ChartDirector/lib/phpchartdir.php");
	        //print"<pre>"; print_r($params);exit;
	        //echo 'dddd';
			# The data for the line chart
			$n=count($params['data']);
			$lendtitle=$params['lendtitle'];
			$labels=$params['xlabel'];

            if($params['isjz']=="1")  //判断均值是否显示 add by zhangcun 2016/8/11
		   {
			  $avg="";
			  $avg_print=array();
              for($i=0;$i<$n;$i++)
			  {
				  $avg[$i]=round(array_sum($params['data'][$i])/count($params['data'][$i]),2);
				  $avg_print[$i]="  ".$junzhi.":".$avg[$i];
			  }
		   }
            //print"<pre>"; print_r($avg_print);
			//取配色方案
			$linecolor=$this->getcolor($params,$dao);





			
			if($shape=='big'){
			    //图片长宽
				$pic1_1=600;
				$pic1_2=350;
				 //图表长宽高
				$pic2_1=55;//左间距
				$pic2_2=68;//距离顶部
				$pic2_3=460;//宽
				$pic2_4=200;//长
				//钢之家数据来源那段话位置
				$pic3_1=415;//左间距
				$pic3_2=45;//距离顶部
				$pic3_font=10;//字体大小
			   //线条名的位置
				$pic4_1=50;//左间距
				$pic4_2=290;//距离顶部
				$pic4_font=10;//字体大小
				//轴上字大小
                $xy_font=10;
				//标题大小
				$title_font=15;
			}else if($shape=='small'){
			  
			}else{
				//图片长宽
				$pic1_1=485;
				$pic1_2=279;
				//图表长宽高
				$pic2_1=50;//左间距
				$pic2_2=50;//距离顶部
				$pic2_3=380;//宽
				$pic2_4=160;//长
				//钢之家数据来源那段话位置
				$pic3_1=370;//左间距
				$pic3_2=30;//距离顶部
				$pic3_font=8.5;//字体大小
				//线条名的位置
				$pic4_1=40;//左间距
				$pic4_2=230;//距离顶部
				$pic4_font=8.5;//字体大小
				//轴上字大小
				$xy_font=10;
				//标题大小
                $title_font=13;
			}
			$c = new XYChart($pic1_1, $pic1_2);
			//$c->setDefaultFonts(CREATE_IMG_TTF_PATH);
			# Add a title to the chart using 15 pts Times Bold Italic font
			$c->addTitle($params['charttitle'], CREATE_IMG_TTF_PATH,  $title_font);	
			$plotarea = $c->setPlotArea($pic2_1, $pic2_2, $pic2_3,$pic2_4, 0xffffff, 0xffffff, 0xe9e9e9, 0xc0c0c0);
				
			//$plotarea = $c->setPlotArea(50, 90, 500, 200, 0xeeeeee, 0xffffff, 0xe9e9e9, 0xc0c0c0);
			if (count($lendtitle)==4){
				$jl = 322;
			}else{
				$jl = 310;
			}

			if($params['isbg'] == "1"){}else{
 			$plotarea->setBackground2(dirname(__FILE__)."/../../images/".BG_IMG);
			}
			$textBoxObj = $c->addText($pic3_1, $pic3_2, $mes, CREATE_IMG_TTF_PATH, $pic3_font, 0x000000);
			$textBoxObj->setAlignment(TopRight);
			$legendObj = $c->addLegend($pic4_1, $pic4_2, false, CREATE_IMG_TTF_PATH, $pic4_font);//线条名的位置
			$legendObj->setBackground(Transparent);


			$c->xAxis->setTickOffset(0.05);

			# Set axis label style to 8pts Arial Bold
			$c->xAxis->setLabelStyle(CREATE_IMG_TTF_PATH, $xy_font,TextColor);
			$c->yAxis->setLabelStyle(CREATE_IMG_TTF_PATH, $xy_font);
			//$c->yAxis2->setLabelStyle(CREATE_IMG_TTF_PATH, 2);
			# Set axis line width to 2 pixels
			$c->xAxis->setWidth(1);
			$c->yAxis->setWidth(1);
			# Set the x axis labels
			//$c->xAxis->setLabels($labels);
			# Add a Area layer to the chart
			$i=0;
			//exit;
			//$dmin=array();
			//$dmax=array();
			$datat=array();
			$layer = $c->addAreaLayer2(Stack);
			for($i=0;$i<$n;$i++)
			{
				$datat[$i]=$params['data'][$i];
				$layer->addDataSet($datat[$i],$linecolor[$i], $this->getmytitle($lendtitle[$i].$avg_print[$i],$i));
			}

			//$countlabel=count($datat[0]);
			//$stepcount=round($countlabel/7,0);
			//$c->xAxis->setLabelStep($stepcount);
			if(count($labels)>=50){
			$ii=count($labels)/4;
			$c->xAxis->addlabel(0,$labels[0]);
			for($i=0;$i<4;$i++){
	            $c->xAxis->addlabel(($i*$ii),$labels[($i*$ii)]);
			}
			
			$c->xAxis->addlabel(count($labels)-1,$labels[count($labels)-1]);
			}
			 elseif(count($labels)<=12&&$params['bar_datetype']['data']<=3&&$params['issametype']==1){
                foreach($labels as $id=>$val){
				    $c->xAxis->addLabel($id,$val);
				  }
	         }else{
				 $c->xAxis->setLabels($labels);
                 $countlabel=count($labels);
				 $stepcount=round($countlabel/4,0);
				 $c->xAxis->setLabelStep($stepcount);
			}

			$textBoxObj = $c->yAxis->setTitle($params['dw'][0],CREATE_IMG_TTF_PATH);
			$textBoxObj->setAlignment(TopLeft2);
			
			# Output the chart
        header("Content-type: image/png");
		print($c->makeChart2(PNG));exit;			
			file_put_contents($params['url'],$c->makeChart2(PNG));
	}

	public function createchart($params, $type, $dao) 
	{  
	   $mc_type=$params["mc_type"];
	   if($type=="")
	   $type="line";
	   if($mc_type==1){
		   define("BG_IMG","150.jpg");
		   define("RES_NAME","南钢产销大数据平台");
	   }else if($mc_type==3){
		   define("BG_IMG","000.gif");
		   define("RES_NAME","新余钢铁绩效效益预测信息化系统       ");
	   }else{
		   define("BG_IMG","000.gif");
		   define("RES_NAME","钢之家数据中心 www.steelhome.cn/data");
	   }
	  
		define("CN_DATASOURCEMES","数据来源：".RES_NAME);
		define("EN_DATASOURCEMES","Source: SteelHome Database ");
	   switch($type)
	   {
	      case 'line':
		  $this->linechart($params,$mes,$dao);
		  break;
		  case 'bar':
		  $this->barchart($params,$mes,$dao);
		  break;
		  case 'doubleline':
		  $this->doublelinechart($params,$mes,$dao);
		  break;
		  case 'linebar':
		  $data=$this->filterlinebardata($params['data'],$params['ftp']);
		  $datatitle=$this->filterlinebarlendtitle($params['lendtitle'],$params['ftp']);
		  $params['data']=$data;
		  $params['lendtitle']=$datatitle;
		  //print_r( $params['lendtitle'])."aaaaaaaa";
		  $this->linebarchart($params,$mes,$dao);
		  break;
		  case 'stackedbar':
		  $this->stackedbarchart($params,$mes,$dao);
		  break;
		  case 'stackedarea':
		  $this->stackedareachart($params,$mes,$dao);
		  break;
		  case 'pie':
		  $this->newpiechart($params,$mes,$dao);
		  break;
		  default:
		  $this->linechart($params,$mes,$dao);
		  break;
	   }
	}
	public function getwei($str) //add by zhangcun 2017/7/19 for 获取小数有效位数
	{
		$wei=0;
		if($str-1.0>0) return $wei;
		for($i=0;$i<10;$i++){
			$str*=10.0;
			$wei++;
			if($str-1.0>0) return $wei;
		}
	}

	public function filterlinebardata($params,$ftp)
	{
		//print_r($ftp);
		//print_r($params);
		foreach ($params as $k=>$v){
			if($ftp[$k]==1){
				$data[0][] = $v;//柱状图
			}else{
				$data[1][] = $v;//折线图
			}
		}
		//$data[0]=array($params[0]);
		//$data[1]=array($params[1]);
		return $data;
	}
	public function filterlinebarlendtitle($params,$ftp)
	{
		foreach($params as $k=>$v){
			if($ftp[$k]==1){
				$data[0][] = $v;//柱状图
			}else{
				$data[1][] = $v;//折线图
			}	
		}
		//$data[0]=array($params[0]);
		//$data[1]=array($params[1]);
		return $data;
	}
	public function compareAB($a,$b,$t) //判断时间a,b是否有一个是t的前一年、前一月或者前一天
	{
		//$a="16/01";
		//$b='16/02';
		//$t='16/03';
	
		$i=explode("/",$a);
        $j=explode("/",$b);
		$k=explode("-",$t);
		if($i[0]<100) $i[0]+=2000; 
		if($j[0]<100) $j[0]+=2000; 
		if($k[0]<100) $k[0]+=2000; 
		$a=implode("-",$i);
        $b=implode("-",$j);
		//$t=implode("-",$k);

        //print"<pre>";print_r($a);echo "<br>";print_r($b);echo "<br>";print_r($t);echo "<br>";
        switch(count($i)){
         case "1":   
			       //$t=date("Y",strtotime($a)+3600*24*365);
		           //$b=date("Y",strtotime($b)+3600*24*365);
		           $t=date("Y",strtotime($k-1));
			       break;
		 case "2": 
			       //$a=date("Y-m",strtotime($a));
		           //$b=date("Y-m",strtotime($b));
				   if($k[1]!=1) {
					   $t=date("Y-m",strtotime($k[0]."-".($k[1]-1)));
				   }
				   else{
					   $t=date("Y-m",strtotime(($k[0]-1)."-12"));
				   }
				   break;
		 case "3":
			       //$a=date("Y-m-d",strtotime($a)+3600*24);
		           //$b=date("Y-m-d",strtotime($b)+3600*24);
		           $t=date("Y-m-d",strtotime($t)-3600*24);
				   break;
		}
        
	    //print"<pre>";print_r($a);echo "<br>";print_r($b);echo "<br>";print_r($t);echo "<br>";
		if($a==$t||$b==$t) return true;
        //else echo "Fail!";
		return false;
	}

	function getbu($array,$date,$k){
		for($i=$k;$i>=0;$i--){
			if($array[$date[$i]]!=""){
				return $array[$date[$i]];
			}
		}
		return "";
	}
  	function getbu2($array,$date,$k){
		for($i=$k;$i<=count($date);$i++){
			if($array[$date[$i]]!=""){
				return $array[$date[$i]];
			}
		}
		return "";
	}
	private function getmin($arr){
		if(is_array($arr[0])) return "";
		$min="aaa";
		foreach($arr as $id=>$val){
			if($min=="aaa") $min=$val;
			elseif($min-$val>0) $min=$val;
		}
		if($min=="aaa"){
			return "";
		}else{ 
			return $min;
		}
	}
	private function getmax($arr){
		if(is_array($arr[0])) return "";
		$max="bbb";
		foreach($arr as $id=>$val){
			if($max=="bbb") $max=$val;
			elseif($max-$val<0) $max=$val;
		}
		if($max=="bbb"){
			return "";
		}else{
			return $max;
		}
	}

	private function getmytitle($str,$ii,$len=70){
		$tmpstr = "";
		$mark=0;
		for($i = 0; $i < strlen($str); $i++) {
			if(ord(substr($str, $i, 1)) > 0xa0) {
				if($ii==0&&$i>$len&&$mark==0){
					$tmpstr .="\n";
					$mark=1;
				}
				$tmpstr .= substr($str, $i, 2);
				$i++;
			} else
				$tmpstr .= substr($str, $i, 1);
		}
		return $tmpstr;
	}


	private function yeardataarr($params){
		foreach($params['xlabel'] as $i=>$v){
			$v=str_replace('-','/',$v);
			//echo $v."<br>";
			$year=explode('/',$v);
			if(count($year)==1) {$nzd=1;break;}
			$date=substr($v,strlen($year[0])+1);
			$datearr[$date]=1;
			$data[$year[0]][$date]=$params['data'][0][$i];
		}
		$date=array_keys($datearr);
		sort($date);
	
		$params['xlabel']=$date;
		$params['data']=array();
		$params['lendtitle']=array();
		$i=0;
		
		foreach($data as $y=>$v){
			//echo $y."\t";
			//echo '<pre>';print_r($v);//exit;
			foreach($date as $o=>$d){
				if($v[$d]==""){
					$year=$y<2000?$y+2000:$y;
					if(strstr($date[$o],'/')){
						$mdate=$year."/".$date[$o];
					}else{
						$mdate=$year."/".$date[$o]."/01";
					}
					
					if(strtotime($mdate)>time()) {
						$params['data'][$i][$d]="'-'";
						continue;
					}
					
					   $params['data'][$i][$d]=$this->getbu($v,$date,$o-1);
					if($params['data'][$i][$d]== "") {
						$params['data'][$i][$d]=$this->getbu2($v,$date,$o+1);
						
					} 
					
				}else{
				
						$params['data'][$i][$d]=$v[$d];
					
					   
				}
			}

			$params['lendtitle'][$i]=$y<2000?$y+2000:$y;
			$yeat_data='';
			
			foreach($params['data'][$i] as $key=>$val){
				if($val!="'-'"){
					$yeat_data[]=$val;
				}
				
			}

			$newdata[] = $yeat_data;
			$newtitle[] = $params['lendtitle'][$i];
			$i++;
		}

		$params['newdata']=$newdata;
		$params['newtitle']=$newtitle;
		return $params;
	}



	private function getcolor($params,$dao){
		if($params['color']=="彩色"||$params['color']=="0"){
			// $linecolor=array(0x4F81BD,0xC0504D,0x9BBB59,0x8166A2);
			$linecolor=array(0x3C6494,0XA8423F,0X228B22,0XFFCC00,0XD87093,0X778899,0X00FFFF,0XFF4500,0XC0504D,0X9BBB59,0X8166A2);

		}
		elseif($params['color']=="蓝色"||$params['color']=="1")
		{
			// $linecolor=array(0x3C6494,0x4978B1,0x7E9BC8,0xB6C3DC);
			$linecolor=array(0X096594,0X3C6494,0X4978B1,0X708090,0X7E9BC8,0X9AC0CD,0XB6C3DC,0XF0FFFF,0XC0504D,0X9BBB59,0X8166A2);

		}else{
			// $linecolor=array(0x4F81BD,0xC0504D,0x9BBB59,0x8166A2);
			$linecolor=array(0X3C6494,0XA8423F,0X228B22,0XFFCC00,0XD87093,0X778899,0X00FFFF,0XFF4500,0XC0504D,0X9BBB59,0X8166A2);

		}
		// echo '<pre>';print_R($linecolor);
		$ip=$this->getIP();
		if(array_key_exists($_REQUEST['GUID'],$GLOBALS["testaccountarr"])&&$_REQUEST['SignCS']!=''&&(in_array($ip,$GLOBALS["testip"])||$GLOBALS["nowtest"]==1)){
			$user = $GLOBALS["testaccountuser"];
		}else{
			$user=$dao->getRow( "SELECT * FROM app_session_temp WHERE GUID = '".$_REQUEST['GUID']."' AND mc_type='$mc_type'" );
		}
		
		$zdycolors=$dao->getRow("select CustomColor1,CustomColor2,CustomColor3,CustomColor4,CustomColor from dc_custom_color where mid='$user[Mid]' and uid='$user[Uid]' and mc_type='$mc_type' limit 1");
		if(empty($zdycolors["CustomColor"])){ 
			$zdycolor=array($zdycolors["CustomColor1"],$zdycolors["CustomColor2"],$zdycolors["CustomColor3"],$zdycolors["CustomColor4"]);
		}else{
			$zdycolor = explode(",",$zdycolors["CustomColor"]);
		
		}
		//url中datajson有CustomColor，就全用CustomColor的
		$CustomColor_str = implode('',$params['CustomColor']);
		
		//end ------------------
		$lendtitlenum='';
		$params['ChartExt']=is_array($params['ChartExt'])?$params['ChartExt']:json_decode($params['ChartExt']);
		if($params['ChartExt']['Type']==2){
			$DateStr=explode(',',$params['ChartExt']['DateStr']);
			// sort($DateStr);
			if(count($params['lendtitle'])<count($DateStr)){

			//add $DateStr链接里面大于当前年份的在算一个计数
				$tk_num=0;
				
				foreach ($DateStr as $tky => $tkv) { 
					if(date('Y')<$tkv){
						$tk_num=$tk_num+1;
					}
				}
			//end
			$lendtitlenum=count($DateStr)-count($params['lendtitle'])-$tk_num;
			}
		}
		
		$cnum= count($zdycolor)-count($params['lendtitle']);//lendtitle个数代表数据个数
		
		if(empty($zdycolors["CustomColor"])&&empty($zdycolors["CustomColor1"])&&empty($zdycolors["CustomColor2"])&&empty($zdycolors["CustomColor3"])&&empty($zdycolors["CustomColor4"])){

		}else{ 
			foreach($zdycolor as $i=>$color){
				// if($i==(count($params['data'])-1)&&$_GET['ImageType']=="8"){//强制最新的哪条线红色
				// 	$color="#EA0000";
				// }
			if(!empty($CustomColor_str)){//CustomColor有的话所有线都有，没有都没有。有就不走配色方案，强行用链接的
				$linecolor[$i]="0X".$params['CustomColor'][$i];
			}else{
					if(strstr($color,"#")&&strlen($color)==7&&$color!="#FFFFFF"&&$_GET['ImageType']!="8"){ 
							 $color=str_replace('#','0X',$color);
							$linecolor[$i]=hexdec(base_convert($color,16,16));		
					}
	
					if($_GET['ImageType']=="8"&&empty($params['ChartExt'])&&$color!="#FFFFFF"){
						// $linecolor[$i-$cnum]=$zdycolor[$i];
						if(date('Y',strtotime($_GET['DateEnd']))-date('Y',strtotime($_GET['DateStart']))==(count($params['lendtitle'])-1)){ 
							$color=str_replace('#','0X',$color);
							$linecolor[$i]=hexdec(base_convert($color,16,16));
						
						}else if(($i-$cnum)>=0&&$cnum>=0){
							$zdycolor[$i]=str_replace('#','0X',$zdycolor[$i]);
							$linecolor[$i-$cnum]=hexdec(base_convert($zdycolor[$i],16,16));
						}
					
					
					}else if($_GET['ImageType']=="8"&&!empty($params['ChartExt'])&&$color!="#FFFFFF"){
						if($params['ChartExt']['Type']==2){
							if(empty($zdycolor[$i+$lendtitlenum])){
								$linecolor[$i+$lendtitlenum]=str_replace('#','0X',$linecolor[$i+$lendtitlenum]);
								$linecolor[$i]=hexdec(base_convert($linecolor[$i+$lendtitlenum],16,16));
							}else{
								$zdycolor=str_replace('#','0X',$zdycolor[$i+$lendtitlenum]);
								$linecolor[$i]=hexdec(base_convert($zdycolor[$i+$lendtitlenum],16,16));
							}
							
						}else{
							$color=str_replace('#','0X',$color);
							$linecolor[$i]=hexdec(base_convert($color,16,16));
						} 
						
					}
			}
				
			
			}
		}

	

		return $linecolor;
	}
	private function getIP() {
		$ip=getenv('REMOTE_ADDR');
		$ip_ = getenv('HTTP_X_FORWARDED_FOR');
		if (($ip_ != "") && ($ip_ != "unknown")) {
			$ip=$ip_;
		}
		$ip=explode(",",$ip);
		return $ip[0];
	  }
	  
}
?>