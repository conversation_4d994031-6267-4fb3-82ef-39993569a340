@charset "utf-8";
/* 此@media查询样式单位是以效果图宽360px为例定义的大小 */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button{
    -webkit-appearance: none !important;
}
.position {
    height: 34px;
    line-height: 34px;
    width: 100%;
    box-shadow: 0 5px 5px rgba(0, 0, 0, 0.1);
    text-indent: 32px;
    background: #ffffff url(../images/positionbg.jpg) no-repeat 0 center; z-index:88;;
    font-size: 13px !important;
}


html { font-size : 22.5px; width:100%; overflow-x:hidden;}
@media only screen and (min-width: 320px){
    html { font-size: 20px !important;}
}
@media only screen and (min-width: 360px){
    html { font-size: 22.5px !important;}
}
@media only screen and (min-width: 375px){
    html { font-size: 23.4375px !important;}
}
@media only screen and (min-width: 400px){
    html { font-size: 25px !important;}
}
@media only screen and (min-width: 414px){
    html { font-size: 25.875px !important;}
}
@media only screen and (min-width: 428px){
    html { font-size: 26.75px !important;}
}
@media only screen and (min-width: 480px){
    html { font-size: 30px !important;}
}
@media only screen and (min-width: 569px){
    html { font-size: 35px !important;}
}
@media only screen and (min-width: 640px){
    html { font-size: 40px !important;}
}
@media only screen and (min-width: 768px){
    html { font-size: 45px !important;}
}

input,select{ outline: none;}
input[type="text"],input[type="url"],input[type="submit"],input[type="button"],input[type="email"],input[type="date"],input[type="time"],input[type="tel"],input[type="password"]{ background-color:transparent; -webkit-appearance:none;}
input::-webkit-calendar-picker-indicator{ opacity:0;}/* 下拉小箭头 */
input[type="text"]:-webkit-autofill,input[type="password"]:-webkit-autofill{ box-shadow: 0 0 0px 1000px white inset;}
select{ background-color:transparent; -webkit-appearance:none;}
textarea{ -webkit-appearance:none;}
div{ overflow:hidden;}


*{ margin:0;padding:0;outline:none}
*:not(input,textarea){-webkit-touch-callout:inherit;-webkit-user-select:auto;}

a{text-decoration:none;-webkit-tap-highlight-color:transparent;}
a:hover{text-decoration:none}
button,input,select,textarea{font-size:100%;margin:0;padding:0;outline:0;}

textarea,input{resize:none;outline:0}
textarea{resize:none;-webkit-appearance:none}
ul,ol,li{list-style:none}

.nav_keleyi_com{  background:#ffffff url(../images/icon_date.png) no-repeat 0 center; width:100%; top:0px; z-index:99; color:#547294; font-weight:bold;}

.navbar{  top:0px; width:100%; background:#ffffff url(../images/icon_date.png) no-repeat 0 center; z-index:88; }
.navbar span{ font-weight:bold; float:left;color:#547294; }
div.refresh{ color:#547294; position:fixed; z-index:999; }

.content{position:relative;}
.news{ position:relative;}
.news:before{ content:''; display:block; position:absolute; top:0.7rem; width:1px;; height:100%;  background:#BFCCDB;}
.list:before{ content:''; display:block; position:absolute;  border-radius:50%; border:1px solid #547294; background:#ffffff;}
.list span:first-child{ color:#557395;}
.list span:last-child{ color:#666666;}
.list:after{ content:''; display:block; position:absolute; bottom:0; left:100px; width:100%; height:1px; border-bottom:1px dotted #A2B5CA;}

@media screen and (max-width:479px){

body{background:#ffffff; font:normal 0.6rem/1rem "Microsoft Yahei"; color:#333333;}
.content{ padding:0 0.5rem;}
    div.refresh{ right:20px; top:0.2rem;}
.navbar{ background:#ffffff url(../images/icon_date.png) no-repeat 0.5rem center;  background-size:0.6rem; text-indent:1.5rem;right:0; left:0; height:2rem; line-height:2rem; box-shadow:0 0.05rem 0.2rem rgba(0,0,0,0.2); font-size:0.65rem;margin-top: 1rem}
.nav_keleyi_com{ background-size:0.6rem; text-indent:1rem; height:2rem; line-height:2rem;  font-size:0.65rem;}
.news:before{ left:0.3rem; top:0.9rem;}
.list{ position:relative; line-height:1rem; padding:0.5rem 0;}
.list span{ display:block; float:left;}
.list span:first-child{ width:4rem;  text-indent:1rem;}
.list span:last-child{ width:11rem;}
.newstop .list:first-child{ margin-top:2rem;}
.list:before{ width:0.3rem; height:0.3rem; top:0.9rem; left:0.16rem; background:#547294; border:none;}
.list:after{  border-bottom:none;}

}

@media screen and (min-width: 480px) and (max-width: 767px){

    body{background:#ffffff; font:normal 15px/25px "Microsoft Yahei"; color:#333333;}
    .content{ padding:0 0.5rem;}
    div.refresh{ right:20px; top:0.2rem;}
    .navbar{ background:#ffffff url(../images/icon_date.png) no-repeat 0.5rem center;  background-size:0.6rem; text-indent:1.5rem;right:0; left:0; height:1rem; line-height:1rem; box-shadow:0 0.05rem 0.2rem rgba(0,0,0,0.2); font-size:0.35rem;margin-top: 1rem}
    .nav_keleyi_com{ background-size:0.6rem; text-indent:1rem; height:1rem; line-height:1rem;  font-size:0.35rem;}
    .news:before{ left:0.3rem; top:0.9rem;}
    .list{ position:relative; line-height:1rem; padding:0.25rem 0;}
    .list span{ display:block; float:left;}
    .list span:first-child{ width:3.5rem;  text-indent:1rem;}
    .list span:last-child{ width:11rem;}
    .newstop .list:first-child{ margin-top:0.5rem;}
    .list:before{ width:0.3rem; height:0.3rem; top:0.9rem; left:0.16rem; background:#547294; border:none;}
    .list:after{  border-bottom:none;}

}

@media screen and (min-width:768px){

body{  background:#ffffff; font:normal 13px/30px "Microsoft Yahei"; color:#333333;}
.content{ padding:10px 20px;}
    div.refresh{ right:20px; top:5px;}
.navbar{ background-size:16px; text-indent:30px;right:20px; left:20px; height:50px; line-height:50px;margin-top: 20px;}
.nav_keleyi_com{ background-size:16px; text-indent:30px;height:50px; line-height:50px;}
.news:before{ left:9px;}
    .list{ text-indent:28px; position:relative; padding:15px 0; line-height:30px;}
    .list span:first-child{ width:100px;  float:left;}
    .list span:nth-child(2){ display:inline-block;  width:88%; text-indent:0;margin-left: 10px;}
    .list:before{ width:9px; height:9px; left:4px; top:50%;  margin-top:-5px;}
}

























