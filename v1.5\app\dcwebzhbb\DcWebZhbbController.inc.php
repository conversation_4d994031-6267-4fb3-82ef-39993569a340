<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );

class DcWebZhbbController extends AbstractController{
  
  public function __construct(){
    parent::__construct();

	$this->_action->ngdao = new DcWebZhbbDao('DRCW') ;
	$this->_action->maindao = new DcWebZhbbDao('91R') ;//hlf/钢之家主数据库、正式库，测试无数据
	/*$this->_action->maindao_test = new DcWebZhbbDao('91R_TEST') ;*/
	$this->_action->gcdao=new DcWebZhbbDao('GC');//hlf/钢厂调价的本期价格及涨跌幅,正式库
	$this->_action->drcdao = new DcWebZhbbDao( 'DRC' );
  }

  public function _dopre(){
	  //echo "<pre/>";print_r($_SESSION);exit;
      //$this->_action->checkSession();
  }
	

  public function v_index() {
	 
	$this->_action->index($this->_request);
  }
  
   public function do_savezhbb(){
	   $this->_action->savezhbb($this->_request);
  }

}


?>