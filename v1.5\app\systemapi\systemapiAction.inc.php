<?php
class systemapiAction extends AbstractAction
{
    public function __construct()
    {
        parent::__construct();
    }

    public function checkSession()
    {
    }

    public function gzjSysLogin($request) {
        $response = [
            'Success' => 0,
            'ErrorType' => 0,
            'Message' => '登陆失败',
        ];
        $username = trim( $request['username'] );
        $pwd = trim( $request['pwd'] );
        $mc_type = trim( $request['mc_type'] );
        if ( $username != '' && $pwd != '' ) {
            $userinfo = $this->_dao->getRow("select * from sg_api_login_users where username='$username' and mc_type='$mc_type'");
            if ( !empty($userinfo) ) {
                if ( $userinfo['pwd'] == md5( $pwd ) ) {
                    $curTime = date('Y-m-d H:i:s');
                    $expired = date('Y-m-d H:i:s',  strtotime('+2 hour', strtotime($curTime)));
                    $ip = $this->getip();
                    $token = base64_encode( $userinfo['id'].'-'.$userinfo['username'].'-'.$ip );
                    $sesinfo = $this->_dao->getRow("select * from sg_api_login_sessions where token='$token' and LoginIp='$ip'");
                    if ( !empty($sesinfo) ) {
                        if ( strtotime( $sesinfo['ExpiredDate'] )< strtotime( $curTime ) ) {
                            $this->_dao->execute("update sg_api_login_sessions set ExpiredDate='$expired' where id='".$sesinfo['id']."'");
                        }
                    } else {
                        $this->_dao->execute("insert into sg_api_login_sessions set token='$token',Uid='".$userinfo['id']."',ExpiredDate='$expired',CreateTime='$curTime',LoginIp='$ip',mc_type='2' ");
                    }
                    $response['Success'] = 1;
                    unset( $response['ErrorType'] );
                    $response['Message'] = '登陆成功';
                    $response['Token'] = $token;
                    $response['ExpiredDate'] = $expired;
                } else $response['Message'] = '用户名或密码错误！';
            } else $response['Message'] = '用户名不存在！';
        } else $response['Message'] = '用户名和密码不能为空！';
    
        echo $this->pri_JSON($response);
        exit;
    }

    //数据同步接口
    public function syncdb($request) {
        $response = [
            'Success' => 0,
            'Message' => '登陆失败',
        ];
        $curTime = date('Y-m-d H:i:s');
        $token = trim( $request['token'] );
        $dtjson = $request['dtjson'];
        $ip = $this->getip();
        $checkses = $this->_dao->getRow("select * from sg_api_login_sessions where token='$token' and LoginIp='$ip' order by ExpiredDate");
        if ( !empty($checkses) ) {
            if (strtotime($checkses['ExpiredDate']) >= strtotime( $curTime )) {
                if ( $dtjson != '' ) {
                    $dtjson = htmlspecialchars_decode($dtjson);
                    $tmpjson = json_decode($dtjson, true);
                    foreach ( $tmpjson as $jsonval ) {
                        $type = $jsonval['type'];
                        $data = $jsonval['data'];
                        $tablename = 'sg_'.$jsonval['tablename'];
                        $pk = $jsonval['pk'];
                        $valsql = [];
                        $pkval = '';
                        if ( $type == 'insert' ) {
                            foreach ( $data as $key=>$val ) {
                                $inssql = "insert into $tablename set ";
                                $insval = "";
                                foreach ( $val as $fieldname=>$fieldvalue ) {
                                    $insval .= " $fieldname='".$fieldvalue."',";
                                }
                                if($insval!=""){
                                    $sql = $inssql.substr($insval, 0, -1);
                                    $this->_dao->execute($sql);
                                }
                            }

                        } else if ( $type == 'update' ) {
                            foreach ( $data as $key=>$val ) {
                                $upval = "";
                                foreach ( $val as $fieldname=>$fieldvalue ) {
                                    if ( $pk == $fieldname ) {
                                        $pkval = $fieldvalue;
                                    } else $upval .= " $fieldname='".$fieldvalue."',";
                                }
                                if($upval!=""){
                                    $this->_dao->execute("update $tablename set ".substr($upval, 0, -1)." where $pk='".$pkval."'");
                                }
                            }
                        } else if ( $type == 'delete' ) {
                            foreach ( $data as $key=>$val ) {
                                foreach ( $val as $fieldname=>$fieldvalue ) {
                                    if ( $pk == $fieldname ) {
                                        $pkval = $fieldvalue;
                                    }
                                }
                                $sql = "delete from $tablename where $pk='".$pkval."' limit 1";
                                $this->_dao->execute($sql);
                            }
                        } else if ( $type == 'replace' ) {
                            foreach ( $data as $key=>$val ) {
                                $vals = [];
                                $valsql = '';
                                foreach ( $val as $fieldname=>$fieldvalue ) {
                                    $valsql .= "$fieldname='$fieldvalue',";
                                }
                                $valsql = substr( $valsql, 0, -1 );
                                $this->_dao->execute( "replace into $tablename set $valsql" );
                            }
                        }
                        $response['Success'] = 1;
                        $response['Message'] = '同步完成';
                    }
                } else $response['Message'] = '参数错误';
            } else $response['Message'] = '登录已过期，请重新登录';
        } else $response['Message'] = '未登录或非法登录';
        echo $this->pri_JSON($response);
        exit;
    }

    //获取陕钢数据接口
    public function getdb($request) {
        $response = [
            'Success' => 0,
            'Message' => '登陆失败',
        ];
        $curTime = date('Y-m-d H:i:s');
        $token = trim( $request['token'] );
        $queryjson = $request['queryjson'];
        $DateStart = $request['DateStart'];
        $DateEnd = $request['DateEnd'];
        $ip = $request->ip();
        $mc_type = $request['mc_type'];
        $GUID = $request['GUID'];
        $checkses = ApiLoginSessions::where( [['token', $token], ['LoginIp', $ip]] )->get();
        if ( !$checkses->isEmpty() ) {
            if ( strtotime( $checkses->first()->ExpiredDate )>strtotime( $curTime ) ) {
                if ( $queryjson != '' ) {
                    $tmpjson = json_decode( $queryjson, true );
                    $result = [];
                    foreach ( $tmpjson as $k=>$tmpjson ) {
                        $dtid = $tmpjson['dtid'];
                        $tablename = $tmpjson['tablename'];
                        $sqlmain = base64_decode( str_replace( ' ', '+', $tmpjson['sqlmain'] ) );
                        $dbfield = $tmpjson['dbfield'];
                        $datefield = $tmpjson['datefield'];
                        $queryResult = DB::select( 'select '.$dbfield.' from '.$tablename.' '.$sqlmain );
                        $result[$k]['dtid'] = $dtid;
                        $result[$k]['data'] = $queryResult;
                    }
                    $response['Success'] = 1;
                    $response['Message'] = '查询成功';
                    $response['Results'] = $result;
                } else $response['Message'] = '参数错误';
            } else $response['Message'] = '登录已过期，请重新登录';
        } else $response['Message'] = '未登录或非法登录';
        echo $this->pri_JSON($response);
        exit;
    }

    private function pri_JSON($array)
    {
        $this->pri_arrayRecursive($array, 'urlencode', true);
        $json = json_encode($array);
        return urldecode($json);
    }

    private function pri_arrayRecursive(&$array, $function, $apply_to_keys_also = false)
    {
        static $recursive_counter = 0;
        if (++$recursive_counter > 1000) {
            die('possible deep recursion attack');
        }
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $this->pri_arrayRecursive($array[$key], $function, $apply_to_keys_also);
            } else {
                $array[$key] = $function($value);
            }
            if ($apply_to_keys_also && is_string($key)) {
                $new_key = $function($key);
                if ($new_key != $key) {
                    $array[$new_key] = $array[$key];
                    unset($array[$key]);
                }
            }
        }
        $recursive_counter--;
    }

    function getip()
    {
        static $ip = '';
        $ip = $_SERVER['REMOTE_ADDR'];
        if(isset($_SERVER['HTTP_CDN_SRC_IP'])) {
            $ip = $_SERVER['HTTP_CDN_SRC_IP'];
        } elseif (isset($_SERVER['HTTP_CLIENT_IP']) && preg_match('/^([0-9]{1,3}\.){3}[0-9]{1,3}$/', $_SERVER['HTTP_CLIENT_IP'])) {
            $ip = $_SERVER['HTTP_CLIENT_IP'];
        } elseif(isset($_SERVER['HTTP_X_FORWARDED_FOR']) AND preg_match_all('#\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}#s', $_SERVER['HTTP_X_FORWARDED_FOR'], $matches)) {
            foreach ($matches[0] AS $xip) {
                if (!preg_match('#^(10|172\.16|192\.168)\.#', $xip)) {
                    $ip = $xip;
                    break;
                }
            }
        }
        return $ip;
    }
}

?>