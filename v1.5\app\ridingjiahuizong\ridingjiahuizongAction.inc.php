<?php 
class  ridingjiahuizongAction extends AbstractAction
{
    public function __construct()
    {
        parent::__construct();
    } 
 	public function index($params)
    {
		
		$GUID = $params['GUID'];
		//$GUID = "6615bc4ac53d11e7b891001aa00de1ab";
		$mode = $params['mode'];//1=客户端，2=安卓，3=iOS
		$date = date("Y-m-d");
		$date2 = date("Y年n月j日");

        if($mode==1){
            $head_url = DC_URL."/v1.5/css/ridj_small.css";
        }else if($mode==3||$mode==5){
            $head_url = DC_URL."/v1.5/css/ridj_middle.css";
        }else if($mode==2||$mode==4){
            $head_url = DC_URL."/v1.5/css/ridj_large.css";
        }else{
            $head_url = DC_URL."/v1.5/css/ridj_middle.css";
        }

		file_put_contents("/tmp/zhbridj","\nstart==".time()."\n",FILE_APPEND);		
        
        //$web_site = DC_URL."/data/v1.5/web/";
        $web_site = DC_URL."/v1.5/web/";

        $arr = array(
                "0"=>array(
                        "url"=>"lwgdayprice.php?view=index&Type=3&GUID=$GUID&mode=$mode&curdate=$date&HuiZong=1",
                        "title"=>"一、螺纹钢日定价",
                        "type"=>"3"
                    ),
                "1"=>array(
                        "url"=>"dcwebview.php?view=index&Type=1&GUID=$GUID&mode=$mode&HuiZong=1",
                        'title'=>"二、碳结钢日定价",
                        "type"=>"1"
                    ),
                "2"=>array(
                        "url"=>"dcwebdgrdj.php?view=index&Type=4&GUID=$GUID&mode=$mode&HuiZong=1",
                        'title'=>"三、带钢日定价",
                        "type"=>"4"
                    ),
                "3"=>array(
                        "url"=>"zhbridj.php?view=index&Type=6&GUID=$GUID&mode=$mode&HuiZong=1",
                        'title'=>"四、流通板日定价",
                        "type"=>"6"
                    ),
            );

        $contents = "<h2>".$date2."南钢日定价汇总</h2><br>";
        foreach($arr as $tmp){
            $url = $web_site.$tmp['url'];
            $data = file_get_contents($url);
            $contents .= "<h3 style='font-weight:bold;'>".$tmp['title']."</h3>";
            if($data!="-1"){

                $model_info = $this->_dao->get_ng_price_model_info($tmp['type'],$date);	
                $modelcontent = html_entity_decode($model_info['HuiZong1']);

                $contents .=$modelcontent;
                $contents .=$data;
                $contents .="<br>";
            }else{
                $contents .="<h3>无权限</h3><br>";
            }
            file_put_contents("/tmp/zhbridj","url==".$url."\n",FILE_APPEND);	
            file_put_contents("/tmp/zhbridj",$tmp['title']."end==".time()."\n",FILE_APPEND);	

        }
        $contents .="<br>";
        $contents .="<br>";

        	
       
        $this->assign("contents",$contents);
        $this->assign("head_url",$head_url);
		
    }

	
	
} 
?>