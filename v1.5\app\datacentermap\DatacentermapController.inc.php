<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );
class DatacentermapController extends AbstractController{

  public function __construct(){
    parent::__construct();
    $this->_action->setDao( new DatacentermapDao( "MAIN" ) );
    //$this->_action->bizdao = new SystemDao('BIZ') ;
    //$this->_action->stedao = new DcsystemDao('HQ') ;
    //$this->_action->stedao = new DcsearchDao('DRC') ;
    $this->_action->ngdrc=new DatacentermapDao( "DRCW" );
    $this->_action->stedao = new DatacentermapDao('91R') ;
  }

  public function v_index(){
    $this->_action->index($this->_request);
  }

  public function do_getmap(){
    $this->_action->getmap($this->_request);
  }
  public function do_test(){
    $this->_action->test($this->_request);
  }
  public function v_index1(){
    $this->_action->index1($this->_request);
  }
  public function v_map(){
    $this->_action->map($this->_request);
  }
}

?>