<?php
include_once(FRAME_LIB_DIR . "/controller/AbstractController.inc.php");

class xg_price_ycController extends AbstractController
{

    private $_other_view_action = array("productInventory", "loadWord", "priceBenchmarking", "getAndSaveScrbData");

    public function __construct()
    {
        parent::__construct();

        $other_view_action = $this->_other_view_action;

        $view_action = $this->_request['view']=="" ? $this->_request['action'] : $this->_request['view'];

        if(in_array($view_action, $other_view_action)){
            include_once( APP_DIR . "/xg_price_yc/otherAction.php");
            $this->_action = new otherAction();
            if($this->_request['view']) $this->_templateName = "other/".$this->_request['view'].".html";
        } else {
            $this->_action->setDao(new xg_price_ycDao('DRCW'));
            $this->_action->maindao = new xg_price_ycDao('91R');
            $this->_action->gcdao = new xg_price_ycDao('GC');
            $this->_action->drcwdao = new xg_price_ycDao('DRCW');
            $this->_action->drcdao = new xg_price_ycDao('DRC');
            $this->_action->sms = new xg_price_ycDao("98SMR");
        }
        $this->_action->xg = new xg_price_ycDao("XINSTEEL");
        $this->_action->t1dao = new xg_price_ycDao("MAIN");
    }

    public function _dopre()
    {
        //$this->_action->checkSession();
    }

    public function v_productInventory(){
        $this->_action->productInventory($this->_request);
    }

    public function v_loadWord(){
        $this->_action->loadWord($this->_request);
    }

    public function v_priceBenchmarking(){
        $this->_action->priceBenchmarking($this->_request);
    }

    public function do_getAndSaveScrbData(){
        $this->_action->getAndSaveScrbData($this->_request);
    }

    public function v_day_price_yc()
    {
        $this->_action->day_price_yc($this->_request);
    }

    public function v_month_price_yc()
    {
        $this->_action->month_price_yc($this->_request);
    }

    /**
     * 旬预测
     * Created by zfy.
     * Date:2023/1/5 10:51
     */
    public function v_xun_price_yc()
    {
        $this->_action->xun_price_yc($this->_request);
    }

    /**
     * 预测
     * Created by zfy.
     * Date:2023/1/13 10:22
     */
    public function v_forecast_list()
    {
        $this->_action->forecast_list($this->_request);
    }

    public function do_ajax_forecast_list()
    {
        $this->_action->ajax_forecast_list($this->_request);
    }

    /**
     * 下载pdf
     * Created by zfy.
     * Date:2023/1/17 15:09
     */
    public function do_down_pdf()
    {
        $this->_action->down_pdf($this->_request);
    }

    //经营指标设置
    public function v_yj_manage()
    {
        $this->_action->yj_manage($this->_request);
    }

    public function v_edityj()
    {
        $this->_action->edityj($this->_request);
    }

    public function do_getyjlist()
    {
        $this->_action->getyjlist($this->_request);
    }

    public function do_deleteyj()
    {
        $this->_action->deleteyj($this->_request);
    }

    public function do_saveyj()
    {
        $this->_action->saveyj($this->_request);
    }

    public function v_msg_manage()
    {
        $this->_action->msg_manage($this->_request);
    }

    public function do_getmsglist()
    {
        $this->_action->getmsglist($this->_request);
    }

    public function do_deletemsg()
    {
        $this->_action->deletemsg($this->_request);
    }

    public function do_savemsg()
    {
        $this->_action->savemsg($this->_request);
    }

    public function do_updatemsg()
    {
        $this->_action->updatemsg($this->_request);
    }

    public function do_updatevariety()
    {
        $this->_action->updatevariety($this->_request);
    }

    public function v_setvarietyname()
    {
        $this->_action->setvarietyname($this->_request);
    }

    public function do_postmsg()
    {
        $this->_action->postmsg($this->_request);
    }

    public function do_msgsend()
    {
        $this->_action->msgsend($this->_request);
    }

    public function v_yjmessage()
    {
        $this->_action->yjmessage($this->_request);
    }

    public function do_get_openid()
    {
        $this->_action->get_openid($this->_request);
    }

    public function do_cancel()
    {
        $this->_action->cancel($this->_request);
    }

    public function v_uploadzgx()
    {
        $this->_action->uploadzgx($this->_request);
    }

    public function v_zgxdetail()
    {
        $this->_action->zgxdetail($this->_request);
    }

    public function do_importexcelzgx()
    {
        $this->_action->importexcelzgx($this->_request);
    }

    public function do_getzgxlist()
    {
        $this->_action->getzgxlist($this->_request);
    }

    public function do_deletezgx()
    {
        $this->_action->deletezgx($this->_request);
    }

    public function v_huizong_duibiao()
    {
        $this->_action->huizong_duibiao($this->_request);
    }

    public function v_huizong_gcduibiao()
    {
        $this->_action->huizong_gcduibiao($this->_request);
    }

    public function v_huizong_gcduibiao_xbc()
    {
        $this->_action->huizong_gcduibiao_xbc($this->_request);
    }

    public function v_huizong_gcduibiao_lz()
    {
        $this->_action->huizong_gcduibiao_lz($this->_request);
    }

    public function v_huizong_gcduibiao_rz()
    {
        $this->_action->huizong_gcduibiao_rz($this->_request);
    }

    public function v_huizong_gcduibiao_zhb()
    {
        $this->_action->huizong_gcduibiao_zhb($this->_request);
    }

    public function v_huizong_hyduibiao()
    {
        $this->_action->huizong_hyduibiao($this->_request);
    }

    public function v_insert_duibiao()
    {
        $this->_action->insert_duibiao($this->_request);
    }

    public function do_save_duibiao()
    {
        $this->_action->save_duibiao($this->_request);
    }

    public function v_scjgbg()
    {
        $this->_action->scjgbg($this->_request);
    }

    public function do_save_scjgbg()
    {
        $this->_action->save_scjgbg($this->_request);
    }

    //原燃料报表
    public function v_uploadyrl()
    {
        $this->_action->uploadyrl($this->_request);
    }

    public function v_yrldetail()
    {
        $this->_action->yrldetail($this->_request);
    }

    public function do_updateyrldetail()
    {
        $this->_action->updateyrldetail($this->_request);
    }

    public function do_importexcelyrl()
    {
        $this->_action->importexcelyrl($this->_request);
    }

    public function do_getyrllist()
    {
        $this->_action->getyrllist($this->_request);
    }

    public function do_deleteyrl()
    {
        $this->_action->deleteyrl($this->_request);
    }

    public function v_huizong_yrl_jgjkb()
    {
        $this->_action->huizong_yrl_jgjkb($this->_request);
    }

    public function v_data_report()
    {
        $this->_action->data_report($this->_request);
    }

    public function v_mysteel_compared_price()
    {
        $this->_action->mysteel_compared_price($this->_request);
    }

    /**
     * 价格录入
     * Created by zfy.
     * Date:2023/7/12 16:47
     */
    public function v_regional_price_input_manage()
    {
        $this->_action->regional_price_input_manage($this->_request);
    }

    public function do_get_regional_price_input_list()
    {
        $this->_action->get_regional_price_input_list($this->_request);
    }

    public function v_regional_price_input()
    {
        $this->_action->regional_price_input($this->_request);
    }

    public function do_regional_price_input()
    {
        $this->_action->do_regional_price_input($this->_request);
    }

    public function do_delete_regional_price()
    {
        $this->_action->delete_regional_price($this->_request);
    }

    /**
     * 配置录入
     * Created by zfy.
     * Date:2023/7/12 16:47
     */
    public function v_regional_price_setting_manage()
    {
        $this->_action->regional_price_setting_manage($this->_request);
    }

    public function v_regional_price_setting()
    {
        $this->_action->regional_price_setting($this->_request);
    }

    public function do_get_regional_price_setting_list()
    {
        $this->_action->get_regional_price_setting_list($this->_request);
    }

    public function do_regional_price_setting()
    {
        $this->_action->do_regional_price_setting($this->_request);
    }

    public function do_delete_regional_price_setting()
    {
        $this->_action->delete_regional_price_setting($this->_request);
    }

    public function v_regional_price_comparison()
    {
        $this->_action->regional_price_comparison($this->_request);
    }

    /**
     * excel导入管理
     * Created by zfy.
     * Date:2023/7/14 10:10
     */
    public function v_excel_import_manage()
    {
        $this->_action->excel_import_manage($this->_request);
    }

    public function do_import_common_excel()
    {
        $this->_action->import_common_excel($this->_request);
    }

    public function do_get_excel_table_list()
    {
        $this->_action->get_excel_table_list($this->_request);
    }

    public function do_delete_excel_table()
    {
        $this->_action->delete_excel_table($this->_request);
    }

    public function v_excel_table_detail()
    {
        $this->_action->excel_table_detail($this->_request);
    }

    public function v_by_pz_ship()
    {
        $this->_action->by_pz_ship($this->_request);
    }

    public function v_thj_pz_price()
    {
        $this->_action->thj_pz_price($this->_request);
    }
    public function v_tf_lf_jc()
    {
        $this->_action->tf_lf_jc($this->_request);
    }

    /**
     * 计划和产量数据表
     * Created by zfy.
     * Date:2023/7/18 8:45
     */
    public function v_plan_output_manage()
    {
        $this->_action->plan_output_manage($this->_request);
    }

    /**
     * 库存数据表
     * Created by zfy.
     * Date:2023/7/18 8:45
     */
    public function v_stock_data_manage()
    {
        $this->_action->stock_data_manage($this->_request);
    }

    /**
     * 资金回笼管理
     * Created by zfy.
     * Date:2023/7/19 8:44
     */
    public function v_capital_return_manage_list()
    {
        $this->_action->capital_return_manage_list($this->_request);
    }

    public function v_capital_return_manage()
    {
        $this->_action->capital_return_manage($this->_request);
    }

    public function do_get_capital_return_list(){
        $this->_action->get_capital_return_list($this->_request);
    }

    public function do_get_capital_return_input_list()
    {
        $this->_action->get_capital_return_input_list($this->_request);
    }

    public function v_capital_return_input()
    {
        $this->_action->capital_return_input($this->_request);
    }

    public function do_capital_return_input()
    {
        $this->_action->do_capital_return_input($this->_request);
    }

    public function do_delete_capital_return()
    {
        $this->_action->delete_capital_return($this->_request);
    }

    /**
     * 文件上传服务器管理
     * Created by zfy.
     * Date:2023/7/20 8:45
     */
    public function v_excel_upload_manage()
    {
        $this->_action->excel_upload_manage($this->_request);
    }

    public function do_excel_upload_input()
    {
        $this->_action->excel_upload_input($this->_request);
    }

    public function do_get_excel_upload_table_list()
    {
        $this->_action->get_excel_upload_table_list($this->_request);
    }

    public function do_delete_excel_upload_table()
    {
        $this->_action->delete_excel_upload_table($this->_request);
    }

    public function v_excel_upload_table_detail()
    {
        $this->_action->excel_upload_table_detail($this->_request);
    }

    public function v_excel_upload_sheet_select()
    {
        $this->_action->excel_upload_sheet_select($this->_request);
    }

    /**
     * 手持订单量
     * Created by zfy.
     * Date:2023/7/21 8:52
     */
    public function v_hand_order_manage()
    {
        $this->_action->hand_order_manage($this->_request);
    }


}