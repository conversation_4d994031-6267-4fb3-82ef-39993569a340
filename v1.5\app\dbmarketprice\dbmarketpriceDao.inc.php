<?php

class dbmarketprice<PERSON><PERSON> extends Dao
{
    public function __construct($writer)
    {
        parent::__construct($writer);
    }


    public function getpricebypricestr($pricestr, $date)
    {
        //echo $time;
        $idnumber = explode(',', $pricestr);
        //echo count($idnumber);
        $six = $seven = array();
        foreach ($idnumber as $id) {
            if (strlen($id) == 6) {//判断id 的字符长度
                if (!in_array($id, $six)) {
                    $six[] = $id;
                }
            }
            if (strlen($id) == 7) {
                if (!in_array($id, $seven)) {
                    $seven[] = $id;
                }
            }
        }
        $sixid_str = implode("','", $six);
        $sevenid_str = implode("','", $seven);
        $mconmanagedate .= "(mconmanagedate>='" . $date . " 00:00:00' and mconmanagedate<='" . $date . " 23:59:59')";
        if ($sixid_str != '') {
            $PriceListSQL = " select marketconditions.pricemk price,marketconditions.topicture, marketconditions.mconmanagedate from marketconditions
 		  	where $mconmanagedate and  marketconditions.topicture in ('$sixid_str')";
        }
        if ($sevenid_str != '') {
            $PriceListSQL = " select marketconditions.price,marketconditions.mastertopid as topicture, marketconditions.mconmanagedate from marketconditions
 		  	where $mconmanagedate and  marketconditions.mastertopid in ('$sevenid_str')";
        }
        if ($sixid_str != '' && $sevenid_str != '') {
            $PriceListSQL = " select marketconditions.pricemk price,marketconditions.topicture, marketconditions.mconmanagedate from marketconditions where $mconmanagedate and  marketconditions.topicture in ('$sixid_str') UNION( select marketconditions.price,marketconditions.mastertopid as topicture,marketconditions.mconmanagedate from marketconditions where $mconmanagedate  and marketconditions.mastertopid in('$sevenid_str'))";
        }
        //echo $PriceListSQL;
        $PriceList = $this->query($PriceListSQL);
        $dataarr = array();//数据数组
        $datearr = array();//日期数组
        foreach ($PriceList as $v) {
            if (strstr($v['price'], "-")) {
                $avgprice = explode("-", $v['price']);
                $v['price'] = round(($avgprice['0'] + $avgprice['1']) / 2, 2);
            }
            $dataarr[$v['topicture']] = $v['price'];

        }
        return $dataarr;
    }

    public function average_price($priceid, $s_date, $e_date)
    {

        $priceid = $priceid;
        $s_date = date("Y-m-d 00:00:00", strtotime($s_date));
        $e_date = date("Y-m-d 23:59:59", strtotime($e_date));

        if (strlen($priceid) == 6) {
            $sql = "select avg(price) from marketconditions where topicture = '$priceid' and mconmanagedate>='$s_date' and mconmanagedate<='$e_date'";
            $avg_price = $this->getOne($sql);
            $avg_price = round($avg_price, 2);
        } else if (strlen($priceid) == 7) {
            $sql = "select avg(price) from marketconditions where mastertopid = '$priceid' and mconmanagedate>='$s_date' and mconmanagedate<='$e_date'";
            $avg_price = $this->getOne($sql);
            $avg_price = round($avg_price, 2);
        }

        return $avg_price;
    }


    function avgqihuo($data_item, $startdate, $enddate)
    {
        $sql = "SELECT avg(dta_6) FROM `data_table` WHERE  `dta_type`='$data_item' and `dta_ym`<='$enddate' and  `dta_ym`>='$startdate' and `dta_maxValStatus`='1' ";
        $arr = $this->getone($sql);
        return $arr;
    }

    function getqihuo($data_item, $date)
    {
        $sql = "SELECT dta_6 FROM `data_table` WHERE  `dta_type`='$data_item' and `dta_ym`='$date' and `dta_maxValStatus`='1' ";
        $arr = $this->getone($sql);
        return $arr;
    }

    function avggfutures_shpi($data_item, $startdate, $enddate)
    {
        $sql = "SELECT avg(mvalue) FROM `gfutures_shpi` WHERE  `leibie`='$data_item' and `datetime`<='$enddate' and  `datetime`>='$startdate'  ";
        $arr = $this->getone($sql);
        return $arr;
    }

    function getgfutures_shpi($data_item, $date)
    {
        $sql = "SELECT mvalue FROM `gfutures_shpi` WHERE  leibie = '$data_item' and `datetime`='$date' ";
        $arr = $this->getone($sql);
        return $arr;
    }

    public function WriteLog($Mid, $Uid, $SignCS, $ActionName, $Actionstr, $ActionIp, $SystemType, $SystemVersion, $MessageTitle = '', $MessageId = '', $MessageDesc = '', $mc_type = 0)
    {
        $this->execute("INSERT INTO app_logs SET Mid='$Mid', Uid='$Uid', SignCS='$SignCS', ActionName='$ActionName',Actionstr='$Actionstr', ActionDate=NOW(),ActionIp='$ActionIp', SystemType='$SystemType', SystemVersion='$SystemVersion',MessageId='$MessageId', MessageTitle='$MessageTitle', MessageDesc='$MessageDesc' , mc_type='$mc_type'");
    }

    //取得已登陆用户
    public function getUser($GUID, $SignCS, $mc_type)
    {
        return $this->getRow("SELECT * FROM app_session_temp WHERE GUID = '$GUID' AND mc_type='$mc_type'");
    }


}

?>