<?php
/* Smarty version 4.1.0, created on 2023-10-31 18:03:35
  from '/www/wwwroot/dev.steelhome.cn/dc.steelhome.cn/meetingv1.2/html/default/meeting/index.html' */

/* @var Smarty_Internal_Template $_smarty_tpl */
if ($_smarty_tpl->_decodeProperties($_smarty_tpl, array (
  'version' => '4.1.0',
  'unifunc' => 'content_6540d0f719cc00_31199505',
  'has_nocache_code' => false,
  'file_dependency' => 
  array (
    '3f11f39f72f670e736932b4e2c077ad230e2fa16' => 
    array (
      0 => '/www/wwwroot/dev.steelhome.cn/dc.steelhome.cn/meetingv1.2/html/default/meeting/index.html',
      1 => 1698746613,
      2 => 'file',
    ),
  ),
  'includes' => 
  array (
  ),
),false)) {
function content_6540d0f719cc00_31199505 (Smarty_Internal_Template $_smarty_tpl) {
?><html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>钢之家数据大屏</title>
<link rel="stylesheet" href="css/meeting/index.css">
<link rel="stylesheet" href="css/meeting/public.css">
<?php echo '<script'; ?>
 src="js/meeting/layui/layui.all.js"><?php echo '</script'; ?>
>
<link rel="stylesheet" href="js/meeting/layui/css/layui.css">

</head>
<body>

<!-- <div class="zuo" id="zuodian"></div>
<div class="you" id="youdian"></div> -->

<header>
<div class="header">
	<h1><span class="font1" id="maintitle"><img src="images/gzj_logo.png">2024年钢铁产业链发展形势会议</span><span class="font2" id="sjdate">2023.10.27-2023.10.29</span></h1>
</div>
</header>

<section>

       

  <div class="center" id="mokuai2">
		
            <div class="center-left fl">

                <div class="left-top rightTop">
                    <div class="title" id="chart9title">职务分类</div>
					 <div class="bottom-b">
                        <div  id="chart9" class="allnav"></div>
						<div  id="chart9Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                    </div>                    
                </div>
                <div class="left-cen rightTop">
                    <div class="title" id="chart10title">年龄</div>
                     <div class="bottom-b">
                        <div  id="chart10" class="allnav"></div>
						<div  id="chart10Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                    </div>
                </div>

            </div>
			 <div class="center-cen fl" >
                <div style="width: 100%;height: 120px;color: #01C4F7;text-align: center;">
                       <div style="width: 50%;float: left;">
                            <p style="font-size: 36px;padding: 20px 20px;color:#00FFFF;"><?php echo $_smarty_tpl->tpl_vars['numData']->value[0]['value'];?>
</p>
                            <p style="font-size: 14px;padding: 5px 20px;">总人数</p>
                       </div>  
                       <div style="width: 50%;float: left;">
                            <p style="font-size: 36px;padding: 20px 20px;color:#00FFFF;"><?php echo $_smarty_tpl->tpl_vars['numData']->value[1]['value'];?>
</p>
                            <p style="font-size: 14px;padding: 5px 20px;">报道人数</p>
                       </div>     
                </div>
			  <iframe id="mapFrame" name="mapFrame" src="" style="width:100%;height:100%;border:0"></iframe>
			
             </div>
			<div class="center-right fr">

					<div class="right-top rightTop">
						<div class="title" id="chart11title">企业类型</div>
						 <div class="right-top-top">
							<div id="chart11" class="allnav"></div>
							<div  id="chart11Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
						</div>
					</div>
					<div class="right-cen">
						<div class="title" id="chart12title">参会人数统计</div>
						<div class="right-cen-cent">
							<div id="chart12" class="allnav"></div>
							<div  id="chart12Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
						</div>
					</div>
			</div>
		
			
			
  </div>


</section>

<nav>
<!-- 
<div class="nav"> 
		<ul> 
				 <li >价格指数</li> 
				 <li>钢材行情</li> 
				 <li>原燃料行情</li>
				 <li>调查数据</li> 
				 <li>期现基差</li> 
				 <li>成本利润</li>
                 <!-- <li>片区销售评价</li>   -->
		</ul> 
</div> -->

</nav>
<?php echo '<script'; ?>
>
    var istry='<?php echo $_smarty_tpl->tpl_vars['istry']->value;?>
';
<?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="js/meeting/jquery.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="js/meeting/axios.min.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="js/meeting/echarts.min.js"><?php echo '</script'; ?>
>
    
<?php echo '<script'; ?>
 src="js/meeting/fontscroll.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="js/meeting/echarts.js"><?php echo '</script'; ?>
>
<?php echo '<script'; ?>
 src="js/meeting/util.js"><?php echo '</script'; ?>
>
    
 <?php echo '<script'; ?>
>
        $(function() {
            $('.myscroll').myScroll({
                speed: 60, //数值越大，速度越慢
                rowHeight: 38 //li的高度
            });
        });

        $(document).ready(function() {
            var whei = $(window).width()
            $("html").css({
                fontSize: whei / 22
            });
            $(window).resize(function() {
                var whei = $(window).width();
                $("html").css({
                    fontSize: whei / 22
                })
            });
        });
    <?php echo '</script'; ?>
>

    <?php echo '<script'; ?>
>
      
	$(function(){  
		
});

        //顶部时间
        function getTime() {
            var myDate = new Date();
            var myYear = myDate.getFullYear(); //获取完整的年份(4位,1970-????)
            var myMonth = myDate.getMonth() + 1; //获取当前月份(0-11,0代表1月)
            var myToday = myDate.getDate(); //获取当前日(1-31)
            var myDay = myDate.getDay(); //获取当前星期X(0-6,0代表星期天)
            var myHour = myDate.getHours(); //获取当前小时数(0-23)
            var myMinute = myDate.getMinutes(); //获取当前分钟数(0-59)
            var mySecond = myDate.getSeconds(); //获取当前秒数(0-59)
            var week = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
            var nowTime;

            nowTime = myYear + '-' + fillZero(myMonth) + '-' + fillZero(myToday) + '&nbsp;&nbsp;' + fillZero(myHour) + ':' + fillZero(myMinute) + ':' + fillZero(mySecond) + '&nbsp;&nbsp;' + week[myDay] + '&nbsp;&nbsp;';
            //console.log(nowTime);
            $('#time').html(nowTime+'企业管理处');
			//$('#lrcsdate').html(myYear+"年"+myMonth+"月"+myToday+"日");
        };

        function fillZero(str) {
            var realNum;
            if (str < 10) {
                realNum = '0' + str;
            } else {
                realNum = str;
            }
            return realNum;
        }
        setInterval(getTime, 1000);
    <?php echo '</script'; ?>
>
</body>
</html><?php }
}
