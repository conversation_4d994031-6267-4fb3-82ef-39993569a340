<?php
class xinsteel_steelhome_priceAction extends AbstractAction
{
	public function __construct()
	{
    	parent::__construct();
	}

	public function smsDao($_dao){
	    $this->_dao = $_dao;
    }

	// 新钢项目中获取钢之家行情价格，返回指定价格id有价格的日期
	function get_steelhome_price_date($params) {
		// file_put_contents("/tmp/lntest.log",date("Y-m-d H:i:s")."获取日期start"."\n",FILE_APPEND);
		// echo "<pre>";print_r($this->homeDao);
		$priceId = $params["priceId"];
		$date = date("Y-m-d 23:59:59",strtotime($params['date']));
        if (strlen($priceId) == 6) {
            $jiageid = "topicture";
        } elseif(strlen($priceId) == 7) {
            $jiageid = "mastertopid";
		}
		$sdate = date("Y-m-d 00:00:00", strtotime('-1 month '.$date));
		$sql = "select mconmanagedate from marketconditions where $jiageid = '".$priceId."' and mconmanagedate <= '".$date."'  and mconmanagedate >= '".$sdate."' order by mconmanagedate desc limit 1";
		// echo $sql;
		$date_new = $this->homeDao->getOne($sql);
		// echo $date_new;
		$isSuccess = 1;
		if (empty($date_new)) {
			$isSuccess = 0;
		}
		$return_arr = array(
			'Success'=>$isSuccess,
			// 'Message'=>'获取指定价格id有价格的日期',
			'date'=>date("Y-m-d",strtotime($date_new)),
		);
		// echo "<pre>";print_r($date_arr); 
		// file_put_contents("/tmp/lntest.log",date("Y-m-d H:i:s")."获取日期end"."\n",FILE_APPEND);
		echo json_encode($return_arr);
	}

    // 新钢项目中获取钢之家行情价格，返回指定价格id
    function get_steelhome_price_byid($params)
    {
        $priceId = $params["priceId"];
        $date = date("Y-m-d 23:59:59", strtotime($params['date']));
        if (strlen($priceId) == 6) {
            $jiageid = "topicture";
        } elseif (strlen($priceId) == 7) {
            $jiageid = "mastertopid";
        }
        $sdate = date("Y-m-d 00:00:00", strtotime('-1 month ' . $date));
        $sql = "select mconmanagedate,price from marketconditions where $jiageid = '" . $priceId . "' and mconmanagedate <= '" . $date . "'  and mconmanagedate >= '" . $sdate . "' order by mconmanagedate desc limit 1";
        // echo $sql;
        $date_new = $this->homeDao->getRow($sql);
        // echo $date_new;
        $isSuccess = 1;
        if (empty($date_new)) {
            $isSuccess = 0;
        }
        $return_arr = array(
            'Success' => $isSuccess,
            'date' => date("Y-m-d", strtotime($date_new['mconmanagedate'])),
            'price' => $date_new['price'],
        );

        echo json_encode($return_arr);
        exit;
    }
	// 新钢项目中获取钢之家行情价格，返回给定价格id指定时间内对应的均价
	function get_steelhome_avgprice($params){
		// file_put_contents("/tmp/lntest.log",date("Y-m-d H:i:s")."获取均价start"."\n",FILE_APPEND);
		// echo "<pre>";print_r($params);
		$sdate = $params['startTime'];
		$edate = $params['endTime'];
		$priceId = html_entity_decode($params['priceId']);
		$priceId = str_replace(" ","+",$priceId);
		$jiagaid_arr = json_decode($priceId, true);
		// print_r($jiagaid_arr); exit;
		
		$idarr_6 = array();
        $idarr_7 = array();
        $pricearr_6 = array();
        $pricearr_7 = array();
        foreach ($jiagaid_arr as $v) {
            $strlen = strlen($v);
            if ($strlen == 6) {
                $idarr_6[] = $v;
            } elseif ($strlen == 7) {
                $idarr_7[] = $v;
			}
		}
        if (!empty($idarr_6)) {
			$idarrstr_6 = implode("','",$idarr_6);
			$sql = "select topicture,avg(price) as avgprice from marketconditions where mconmanagedate >= '".$sdate."' and mconmanagedate <= '".$edate."' and topicture in ('$idarrstr_6') group by topicture";
			// echo $sql;
            $conditions_info_6 = $this->homeDao->query($sql);
            foreach ($conditions_info_6 as $v) {
                $pricearr_6[$v['topicture']] = $v['avgprice'];
            }
        }
        if (!empty($idarr_7)) {
			$idarrstr_7 = implode("','",$idarr_7);
			$sql = "select mastertopid,avg(price) as avgprice from marketconditions where mconmanagedate >= '".$sdate."' and mconmanagedate <= '".$edate."' and mastertopid in ('$idarrstr_7') group by mastertopid";
            $conditions_info_7 = $this->homeDao->query($sql);
            foreach ($conditions_info_7 as $v) {
                $pricearr_7[$v['mastertopid']] = $v['avgprice'];
            }
		}
		$ps_priceid = 'H98640';
		if (in_array($ps_priceid,$idarr_6) && date("Y-m-d",strtotime($sdate)) == date("Y-m-d",strtotime($edate))) {
			$sql = "select price from marketconditions where mconmanagedate >= '".date("Y-m-d 00:00:00",strtotime($sdate.'-1 mon'))."' and mconmanagedate <= '".$edate."' and topicture = '$ps_priceid' order by mconmanagedate desc limit 1";
			// echo $sql;
			$pricearr_6['H98640'] = $this->homeDao->getOne($sql);
		}
		
		$pricearr = $pricearr_6 + $pricearr_7;
		if (empty($pricearr)) {
			$isSuccess = 0;
		}
		$isSuccess = 1;
		$return_arr = array(
			'Success'=>$isSuccess,
			// 'Message'=>'获取给定价格id指定时间内对应的均价',
			'Results'=>$pricearr,
        );
        // file_put_contents("/tmp/lntest.log",date("Y-m-d H:i:s")."获取均价end"."\n",FILE_APPEND);
        echo json_encode($return_arr);
        // echo "<pre>";print_r($pricearr);
    }


    function get_steelhome_avgprice2($params)
    {
        // file_put_contents("/tmp/lntest.log",date("Y-m-d H:i:s")."获取均价start"."\n",FILE_APPEND);
        // echo "<pre>";print_r($params);
        $sdate = $params['startTime'];
        $edate = $params['endTime'];
        $priceId = html_entity_decode($params['priceId']);
        $priceId = str_replace(" ", "+", $priceId);
        $jiagaid_arr = json_decode($priceId, true);
        // print_r($jiagaid_arr); exit;

        $idarr_6 = array();
        $idarr_7 = array();
        $pricearr_6 = array();
        $pricearr_7 = array();
        $datearr_6 = array();
        $datearr_7 = array();
        foreach ($jiagaid_arr as $v) {
            $strlen = strlen($v);
            if ($strlen == 6) {
                $idarr_6[] = $v;
            } elseif ($strlen == 7) {
                $idarr_7[] = $v;
            }
        }
        if (!empty($idarr_6)) {
            $idarrstr_6 = implode("','", $idarr_6);
            $sql = "select topicture,avg(price) as avgprice,mconmanagedate as date from marketconditions where mconmanagedate >= '" . $sdate . "' and mconmanagedate <= '" . $edate . "' and topicture in ('$idarrstr_6') group by topicture";
            // echo $sql;
            $conditions_info_6 = $this->homeDao->query($sql);
            foreach ($conditions_info_6 as $v) {
                $pricearr_6[$v['topicture']] = $v['avgprice']*100;
                $datearr_6[$v['topicture']] = date("Y-m-d",strtotime($v['date']));
            }
        }
        if (!empty($idarr_7)) {
            $idarrstr_7 = implode("','", $idarr_7);
            $sql = "select mastertopid,avg(price) as avgprice,mconmanagedate as date from marketconditions where mconmanagedate >= '" . $sdate . "' and mconmanagedate <= '" . $edate . "' and mastertopid in ('$idarrstr_7') group by mastertopid";
            $conditions_info_7 = $this->homeDao->query($sql);
            foreach ($conditions_info_7 as $v) {
                $pricearr_7[$v['mastertopid']] = $v['avgprice']*100;
                $datearr_7[$v['mastertopid']] = date("Y-m-d",strtotime($v['date']));
            }
        }
        $ps_priceid = 'H98640';
        if (in_array($ps_priceid, $idarr_6) && date("Y-m-d", strtotime($sdate)) == date("Y-m-d", strtotime($edate))) {
            $sql = "select price as avgprice from marketconditions where mconmanagedate >= '" . date("Y-m-d 00:00:00", strtotime($sdate . '-1 mon')) . "' and mconmanagedate <= '" . $edate . "' and topicture = '$ps_priceid' order by mconmanagedate desc limit 1";
            //  echo $sql;
            $pricearr_6['H98640'] = $this->homeDao->getOne($sql);
            $pricearr_6['H98640'] =$pricearr_6['H98640'] *100;
            $sql = "select mconmanagedate as date from marketconditions where mconmanagedate >= '" . date("Y-m-d 00:00:00", strtotime($sdate . '-1 mon')) . "' and mconmanagedate <= '" . $edate . "' and topicture = '$ps_priceid' order by mconmanagedate desc limit 1";
            $datearr_6['H98640'] = $this->homeDao->getOne($sql);
            $datearr_6['H98640'] = date("Y-m-d",strtotime($datearr_6['H98640']));
        }

        $pricearr = $pricearr_6 + $pricearr_7;
        $datearr = $datearr_6 + $datearr_7;
        if (empty($pricearr)) {
            $isSuccess = 0;
        }
        $isSuccess = 1;
        $return_arr = array(
            'Success' => $isSuccess,
            // 'Message'=>'获取给定价格id指定时间内对应的均价',
            'Results1' => $pricearr,
            'Results2' => $datearr,
        );
        // file_put_contents("/tmp/lntest.log",date("Y-m-d H:i:s")."获取均价end"."\n",FILE_APPEND);
        echo json_encode($return_arr);
        // echo "<pre>";print_r($pricearr);
    }

    /**
     * 新钢短信列表接口
     * Created by zfy.
     * Date:2021/3/10 10:33
     * @param $params
     */
    public function xinsteel_sms_list($params)
    {
        $smsid_list = $this->_dao->getDc_smsid_manage();
        //短信信息
        $sms_info = $this->smsDao->get_sms_message_list($params, implode(',', $smsid_list));
        //资讯摘要信息
        $news_info = $this->homeDao->get_news_list_by_date($params);
        $info = array_merge($sms_info,$news_info);
        $time_str = array();
        foreach ($info as &$item) {
            $item['MS_CONTENT'] = base64_encode($item['MS_CONTENT']);
            $item['time'] = date("H:i:s",strtotime($item['MPL_DATE']));
            if ($item['nid']){
                $item['url'] = APP_URL_NEWS."/wap/testlogin/news3/messageshow.php?newstype=n&nid=".$item['nid']."&fromapp=1&GUID=testaccount&loginStatus=1&needtitle=1";
            }else{
                $item['url'] = "";
            }
            $time_str[] = $item['time'];
        }
        //按时间倒序
        array_multisort($time_str,SORT_DESC,$info);
        $return_arr = array(
            'Success' => 1,
            'Results' => $info,
        );
        if ($params['return']==1){
            return $this->pri_JSON($return_arr);
        }else {
            echo $this->pri_JSON($return_arr);
        }
    }

    /**
     * 新钢每周市场要闻接口
     * Created by zfy.
     * Date:2021/3/15 17:07
     * @param $params
     */
    public function xinsteel_market_week_news_list($params){
        $title=iconv('UTF-8','GBK',$params['title']);
        $startdate=$params["startdate"];
        $enddate=$params["enddate"];
        $page=$this->formatpage($params["page"]);
        $pagenum=$params['perPage'];//一页显示的条数
        $limit=" limit ".(($page-1)*$pagenum).",".$pagenum;
        $type_name = array('1'=>"日","2"=>"周");
        $type = $params['Type'];//1=日数据  2=周数据
        $where=" Type='$type' ";
        if ($params['id']=='') {
            if ($title) {
                $where .= " and Title like '%$title%' ";
            }
            if ($startdate) {
                $where .= " and CDate>='$startdate' ";
            }
            if ($enddate) {
                $where .= " and CDate<='$enddate' ";
            }
        }else{
            $where .= " and id='".$params['id']."' ";
        }
        $total = $this->_dao->get_market_week_news_totals($where);
        $list = $this->_dao->get_market_week_news_list($where,$limit);
        $admin_info = $this->homeDao->get_admin_info();
        foreach($list as $k=>$v){
            $arr[$k+($page-1)*$pagenum+1]=$v;
        }

        $json_list = $arr;
        foreach ($json_list as $index => &$item) {
            $item['Title'] = base64_encode("每".$type_name[$type]."市场要闻（".$item['CDate']."）");
            $item['Detail'] = base64_encode($item['Detail']);
            $item['trueName'] = base64_encode($admin_info[$item['adminid']]);
        }
        $ret_arr['success'] = 1;
        $ret_arr['totals'] = $total;
        $ret_arr['result'] = $json_list;
        $ret_arr['message'] = base64_encode("成功");
        echo $this->pri_JSON($ret_arr);exit();
    }

    /**
     * 信息看板
     * Created by zfy.
     * Date:2021/5/18 10:15
     * @param $params
     */
    public function sms_list($params){
        $GUID = $params['GUID'];
        $action = $params['action'];        //接口名称
        $SignCS = $params['SignCS'];
        if (!empty($params['mc_type'])) $mc_type = $params['mc_type'];
        else $mc_type = 0;
        $ip = $this->getIP();

        //判断是不是测试账号
        if (array_key_exists($GUID, $GLOBALS["testaccountarr"]) && $SignCS != '' && (in_array($ip, $GLOBALS["testip"]) || $GLOBALS["nowtest"] == 1)) {
            $user = $GLOBALS["testaccountuser"];
        } else {
            $user = $this->t1Dao->getUser($GUID, $SignCS, $mc_type);
        }
        $mid = $user['Mid'];
        $uid = $user['Uid'];

        $sms_date = $params['sms_date'] == '' ? date('Y-m-d') : $params['sms_date'];
        $sms_list = $this->getSmsInterfaceData($sms_date);

        $arrtem = $params;
        $actionstr = "";
        foreach ($arrtem as $k => $v) {
            $actionstr .= "&" . $k . "=" . $v;
        }
        $this->t1Dao->WriteLog($mid, $uid, $SignCS, $action, $actionstr, $ip, "", "", "查看市场动态-信息看板", '', '', $mc_type);

        $this->assign("sms_list",$sms_list);
    }

    /**
     * 信息看板分页
     * Created by zfy.
     * Date:2021/5/18 15:01
     * @param $params
     */
    public function sms_list_page($params){
        if ($params['date']) {
            $date = date('Y-m-d', strtotime('-1 day', strtotime($params['date'])));
        } else {
            $date = date("Y-m-d");
        }
        $sms_list = $this->getSmsInterfaceData($date,1);
        echo json_encode($sms_list);
    }

    /**
     * 信息看板分页数据
     * Created by zfy.
     * Date:2021/5/18 15:12
     * @param $sms_date
     * @param int $isAjax
     * @return array
     */
    public function getSmsInterfaceData($sms_date,$isAjax = 0)
    {
        $sms_list = array();
        $params['sms_date'] = $sms_date == '' ? date('Y-m-d') : $sms_date;
        $params['return'] = 1;
        $body = $this->xinsteel_sms_list($params);
        $bodyStr = json_decode($body,true); //解析json字符串

        foreach ($bodyStr['Results'] as $key => $result) {
//            $sms_list[$key]['content'] = base64_decode($result['MS_CONTENT']);

            $ii = array();
            if ($isAjax==1) {
//                $ii['content'] = str_replace('&#8203;', '', iconv("GBK", "UTF-8", base64_decode($result['MS_CONTENT'], true)));
//                $date_str = iconv("GBK","UTF-8",date("n月j日", strtotime($result['MPL_DATE'])));
                $ii['content'] = str_replace('&#8203;', '', base64_decode($result['MS_CONTENT'], true));
                $date_str = date("n月j日", strtotime($result['MPL_DATE']) );
            }else {
                $ii['content'] = str_replace('&#8203;', '', base64_decode($result['MS_CONTENT'], true));
                $date_str = date("n月j日", strtotime($result['MPL_DATE']));
            }
            $ii['time'] = $result['time'];
            $ii['url'] = $result['url'];
            $sms_list['date_pos'] = date("Y-m-d", strtotime($result['MPL_DATE']));
            $sms_list['date'] = $date_str;
            $sms_list['data'][] = $ii;
        }
        if (count($sms_list) == 0) {
            $sms_list = $this->getSmsInterfaceData(date("Y-m-d", strtotime('-1 day', strtotime($sms_date))),$isAjax);
        }
//        echo "<pre>";print_r($sms_list);exit;
        return $sms_list;
    }

    public function xinsteel_news_list($params)
    {
        $GUID = $params['GUID'];
        //$GUID = "2c61a12a49c511ebbebc001d09719b40";
        $SignCS = $params['SignCS'];
        //$SignCS = "8b46e267268907263bbec91ec65915f4";
        $mode = $params['mode'];
        $mc_type = $params['mc_type'];
        $startdate=$params["startdate"];
        $enddate=$params["enddate"];

        $type = $params['type'];
        $AppMode = $params['AppMode'];

        /*研究报告   xgyjbg
         * 每周报告
         * 1 建筑钢材
         * 2 中厚板
         * 3 热轧板卷
         * 4 冷轧板卷
         * 5 电工钢
         * 6 铁矿石
         * 7 废钢
         * 8 焦炭
         * 9 铁合金
         *
         * 每月报告
         * 11 建筑钢材
         * 12 中厚板
         * 13 热轧板卷
         * 14 冷轧板卷
         * 15 电工钢
         * 16 铁矿石
         * 17 废钢
         * 18 焦炭
         * 19 铁合金
         * */



        $arr_title = array(
            "1"=>"研究报告 > 每周报告 > 建筑钢材",
            "2"=>"研究报告 > 每周报告 > 中厚板",
            "3"=>"研究报告 > 每周报告 > 热轧板卷",
            "4"=>"研究报告 > 每周报告 > 冷轧板卷",
            "5"=>"研究报告 > 每周报告 > 电工钢",
            "6"=>"研究报告 > 每周报告 > 铁矿石",
            "7"=>"研究报告 > 每周报告 > 废钢",
            "8"=>"研究报告 > 每周报告 > 焦炭",
            "9"=>"研究报告 > 每周报告 > 铁合金",

            "11"=>"研究报告 > 每月报告 > 建筑钢材",
            "12"=>"研究报告 > 每月报告 > 中厚板",
            "13"=>"研究报告 > 每月报告 > 热轧板卷",
            "14"=>"研究报告 > 每月报告 > 冷轧板卷",
            "15"=>"研究报告 > 每月报告 > 电工钢",
            "16"=>"研究报告 > 每月报告 > 铁矿石",
            "17"=>"研究报告 > 每月报告 > 废钢",
            "18"=>"研究报告 > 每月报告 > 焦炭",
            "19"=>"研究报告 > 每月报告 > 铁合金",

            "21"=>"信息看板 > 矿煤焦钢市场日报"
        );
        if($mc_type=="1"){
            $arr_title = array(
                "1"=>"预测分析 > 建筑钢材 > 每周报告",
                "2"=>"预测分析 > 中厚板 > 每周报告",
                "3"=>"预测分析 > 热轧板卷 > 每周报告",
                "4"=>"预测分析 > 冷轧板卷 > 每周报告",
                "5"=>"预测分析 > 电工钢 > 每周报告",
                "6"=>"预测分析 > 铁矿石 > 每周报告",
                "7"=>"预测分析 > 废钢 > 每周报告",
                "8"=>"预测分析 > 焦炭 > 每周报告",
                "9"=>"预测分析 > 铁合金 > 每周报告",

                "11"=>"预测分析 > 建筑钢材 > 每月报告",
                "12"=>"预测分析 > 中厚板 > 每月报告",
                "13"=>"预测分析 > 热轧板卷 > 每月报告",
                "14"=>"预测分析 > 冷轧板卷 > 每月报告",
                "15"=>"预测分析 > 电工钢 > 每月报告",
                "16"=>"预测分析 > 铁矿石 > 每月报告",
                "17"=>"预测分析 > 废钢 > 每月报告",
                "18"=>"预测分析 > 焦炭 > 每月报告",
                "19"=>"预测分析 > 铁合金 > 每月报告",

                "21"=>"预测分析 > 矿煤焦钢市场日报"
            );
        }
        $title = $arr_title[$type];

        $arr = array(
            "1"=>"建筑钢材%一周策略报告",
            "2"=>"中厚板%一周策略报告",
            "3"=>"热轧板卷%一周策略报告",
            "4"=>"冷轧板卷%一周策略报告",
            "5"=>"电工钢%一周策略报告",
            "6"=>"铁矿石%一周策略报告",
            "7"=>"废钢%一周策略报告",
            "8"=>"焦炭%一周策略报告",
            "9"=>"铁合金%一周策略报告",

            "11"=>"月%建筑钢材%策略报告",
            "12"=>"月%中厚板%策略报告",
            "13"=>"月%热轧板卷%策略报告",
            "14"=>"月%冷轧板卷%策略报告",
            "15"=>"月%电工钢%策略报告",
            "16"=>"月%铁矿石%策略报告",
            "17"=>"月%废钢%策略报告",
            "18"=>"月%焦炭%策略报告",
            "19"=>"月%铁合金%策略报告",

            "21"=>"矿煤焦钢市场日报"
        );
        $ntitle = $arr[$type];
        if($ntitle){
            $where = " and ntitle like '%".$ntitle."%' ";
        }else{
            $where = " and 1!=1 ";
        }

        $where .= $startdate=="" ? "" : " and ndate>='$startdate' ";
        $where .= $enddate=="" ? "" : " and ndate<='$enddate' ";

        if(strstr($ntitle, '策略报告')){
            $url = "&get_pdf=1&mode=".$mode;
        }


        $page=$this->formatpage($params["page"]);
        if($params['show']=="1"){
            $pagenum="1";//一页显示的条数
        }else{
            $pagenum="18";//一页显示的条数
        }

        $limit=" limit ".(($page-1)*$pagenum).",".$pagenum;

        $amount=$this->homeDao->get_news_lists($where); //echo $amount;
        //$amount = 55;

        $data = $this->homeDao->get_news_list($where,$limit);
        foreach($data as $key=>&$tmp){
            //$tmp['url'] = "https://tmobileapp.steelhome.cn/v1.4/news.php?action=GetNewsOrMrhqHtml&SignCS=".$SignCS."&GUID=".$GUID."&id=".$tmp['nid']."&AppMode=5&type=1&mode=1";

            $url2 = base64_encode("http://news.steelhome.cn/wap/testlogin/news3/messageshow.php?newstype=n&nid=".$tmp['nid']."&AppMode=".$AppMode."&fromapp=1&GUID=".$GUID."&loginStatus=1&needtitle=2".$url);
            $tmp['url'] = "xinsteel_steelhome_price.php?action=get_news_pdf&GUID=".$GUID."&mc_type=".$mc_type."&mode=".$mode."&SignCS=".$SignCS."&nid=".$tmp['nid']."&url2=".$url2;
            if($params['show']=="1"){
                gourl($tmp['url']);
                exit;
            }
        }

        $pagelabel=$this->getpagelabelnew($amount,$pagenum,$page,"xinsteel_steelhome_price.php?view=".$params['view']."&GUID=".$GUID."&mc_type=".$mc_type."&mode=".$mode."&type=".$type );

        //print_r($data);
        $this->assign("GUID",$GUID);
        $this->assign("title",$title);
        $this->assign("mode",$mode);
        $this->assign("mc_type",$mc_type);
        $this->assign("view",$params['view']);
        $this->assign("data",$data);
        $this->assign("params",$params);
        $this->assign("pagelabel",$pagelabel);
    }

    public function get_news_pdf($params){
        $nid = $params['nid'];
        $url2 = base64_decode($params['url2'],true);
        $mc_type = $params['mc_type'];
        $GUID = $params['GUID'];
        $SignCS = $params['SignCS'];
        $action = $params['action'];		//接口名称

        $user = $this->t1Dao->getUser($GUID,$SignCS,$mc_type);
        $mid = $user['Mid'];

        $title = $this->homeDao->getNews($nid);

        $this->t1Dao->WriteMemberLog($GUID, $SignCS,$mid,$user['ComName'],$title,$params["mode"],$mc_type, $action, $this->getIP());

        gourl($url2);
        //echo $url2;
        exit;



    }



    // 新钢项目中获取钢之家行情价格，返回给定价格id指定时间内对应的均价
    function get_steelhome_B1B2B3price($params)
    {
        // file_put_contents("/tmp/lntest.log",date("Y-m-d H:i:s")."获取均价start"."\n",FILE_APPEND);
        // echo "<pre>";print_r($params);
        $sdate = $params['startTime'];
        $edate = $params['endTime'];
        $priceId = $params['priceId'];
     
        $jiagaid_arr = explode("|",$priceId);
        

     
        if (!empty($jiagaid_arr)) {
            $jiagaid_str= implode("','", $jiagaid_arr);
            $sql = "select svm_vid as keyWordsid,aveprice as price,dateday as date from shpi_pzggp where svm_vid  in ('$jiagaid_str')  and (dateday >= '" . $sdate . "' and dateday <= '" . $edate . "') order by dateday asc";
            // echo $sql;
            $pricearr = $this->homeDao->query($sql);
        }
      
    
        if (empty($pricearr)) {
            $isSuccess = 0;
        }
        $isSuccess = 1;
        $return_arr = array(
            'Success' => $isSuccess,
            // 'Message'=>'获取给定价格id指定时间内对应的均价',
            'Results' => $pricearr,
        );
        // file_put_contents("/tmp/lntest.log",date("Y-m-d H:i:s")."获取均价end"."\n",FILE_APPEND);
        echo json_encode($return_arr);
        // echo "<pre>";print_r($pricearr);
    }
    // 新钢项目中获取钢之家行情价格，返回给定价格id指定时间内对应的均价
    function get_steelhome_TSCBprice($params)
    {
        // file_put_contents("/tmp/lntest.log",date("Y-m-d H:i:s")."获取均价start"."\n",FILE_APPEND);
        // echo "<pre>";print_r($params);
        $sdate = $params['startTime'];
        $edate = $params['endTime'];
        $priceId = $params['priceId'];
    
        $jiagaid_arr = explode("|",$priceId);
       


        if (!empty($jiagaid_arr)) {
            $jiagaid_str= implode("','", $jiagaid_arr);
            $sql = "select topicture as keyWordsid,price,dateday as date from shpi_material where topicture  in ('$jiagaid_str')  and (dateday >= '" . $sdate . "' and dateday <= '" . $edate . "') order by dateday asc";
            // echo $sql;
            $pricearr = $this->homeDao->query($sql);
        }
    
    
        if (empty($pricearr)) {
            $isSuccess = 0;
        }
        $isSuccess = 1;
        $return_arr = array(
            'Success' => $isSuccess,
            // 'Message'=>'获取给定价格id指定时间内对应的均价',
            'Results' => $pricearr,
        );
        // file_put_contents("/tmp/lntest.log",date("Y-m-d H:i:s")."获取均价end"."\n",FILE_APPEND);
        echo json_encode($return_arr);
        // echo "<pre>";print_r($pricearr);
    }

    function get_steelhome_TSCB1price($params)
    {
        // file_put_contents("/tmp/lntest.log",date("Y-m-d H:i:s")."获取均价start"."\n",FILE_APPEND);
        // echo "<pre>";print_r($params);
        $sdate = $params['startTime'];
        $edate = $params['endTime'];
        $priceId = $params['priceId'];
    
        $jiagaid_arr = explode("|",$priceId);
       


        if (!empty($jiagaid_arr)) {
            $jiagaid_str= implode("','", $jiagaid_arr);
            $sql = "select vid as keyWordsid,weiprice as price,dateday as date from shpi_material_pzp where vid  in ('$jiagaid_str')  and (dateday >= '" . $sdate . "' and dateday <= '" . $edate . "') order by dateday asc";
            // echo $sql;
            $pricearr = $this->homeDao->query($sql);
        }
    
   
        if (empty($pricearr)) {
            $isSuccess = 0;
        }
        $isSuccess = 1;
        $return_arr = array(
            'Success' => $isSuccess,
            // 'Message'=>'获取给定价格id指定时间内对应的均价',
            'Results' => $pricearr,
        );
        // file_put_contents("/tmp/lntest.log",date("Y-m-d H:i:s")."获取均价end"."\n",FILE_APPEND);
        echo json_encode($return_arr);
        // echo "<pre>";print_r($pricearr);
    }
    function get_steelhome_TSCB2price($params)
    {
        // file_put_contents("/tmp/lntest.log",date("Y-m-d H:i:s")."获取均价start"."\n",FILE_APPEND);
        // echo "<pre>";print_r($params);
        $sdate = $params['startTime'];
        $edate = $params['endTime'];
        $priceId = $params['priceId'];
    
        $jiagaid_arr = explode("|",$priceId);
       


        if (!empty($jiagaid_arr)) {
            $jiagaid_str= implode("','", $jiagaid_arr);
            $sql = "select vid as keyWordsid,weiprice as price,dateday as date from shpi_material_pzp where vid  in ('$jiagaid_str')  and (dateday >= '" . $sdate . "' and dateday <= '" . $edate . "') order by dateday asc";
            // echo $sql;
            $pricearr = $this->homeDao->query($sql);
        }
    
   
        if (empty($pricearr)) {
            $isSuccess = 0;
        }
        $isSuccess = 1;
        $return_arr = array(
            'Success' => $isSuccess,
            // 'Message'=>'获取给定价格id指定时间内对应的均价',
            'Results' => $pricearr,
        );
        // file_put_contents("/tmp/lntest.log",date("Y-m-d H:i:s")."获取均价end"."\n",FILE_APPEND);
        echo json_encode($return_arr);
        // echo "<pre>";print_r($pricearr);
    }
    function get_steelhome_TSCB3price($params)
    {
        // file_put_contents("/tmp/lntest.log",date("Y-m-d H:i:s")."获取均价start"."\n",FILE_APPEND);
        // echo "<pre>";print_r($params);
        $sdate = $params['startTime'];
        $edate = $params['endTime'];
        $priceId = $params['priceId'];
    
        $jiagaid_arr = explode("|",$priceId);
       


        if (!empty($jiagaid_arr)) {
            $jiagaid_str= implode("','", $jiagaid_arr);
            $sql = "select topicture as keyWordsid,price,dateday as date from shpi_material where topicture  in ('$jiagaid_str')  and (dateday >= '" . $sdate . "' and dateday <= '" . $edate . "') order by dateday asc";
            // echo $sql;
            $pricearr = $this->homeDao->query($sql);
        }
    
   
        if (empty($pricearr)) {
            $isSuccess = 0;
        }
        $isSuccess = 1;
        $return_arr = array(
            'Success' => $isSuccess,
            // 'Message'=>'获取给定价格id指定时间内对应的均价',
            'Results' => $pricearr,
        );
        // file_put_contents("/tmp/lntest.log",date("Y-m-d H:i:s")."获取均价end"."\n",FILE_APPEND);
        echo json_encode($return_arr);
        // echo "<pre>";print_r($pricearr);
    }
    function get_steelhome_TSCB4price($params)
    {
        // file_put_contents("/tmp/lntest.log",date("Y-m-d H:i:s")."获取均价start"."\n",FILE_APPEND);
        // echo "<pre>";print_r($params);
        $sdate = $params['startTime'];
        $edate = $params['endTime'];
        $priceId = $params['priceId'];
    
        $jiagaid_arr = explode("|",$priceId);
       


        if (!empty($jiagaid_arr)) {
            $jiagaid_str= implode("','", $jiagaid_arr);
            $sql = "select vid as keyWordsid,weiprice as price,dateday as date from shpi_mj_pzp where vid  in ('$jiagaid_str') and type=3  and (dateday >= '" . $sdate . "' and dateday <= '" . $edate . "') order by dateday asc";
            // echo $sql;
            $pricearr = $this->homeDao->query($sql);
        }
    
   
        if (empty($pricearr)) {
            $isSuccess = 0;
        }
        $isSuccess = 1;
        $return_arr = array(
            'Success' => $isSuccess,
            // 'Message'=>'获取给定价格id指定时间内对应的均价',
            'Results' => $pricearr,
        );
        // file_put_contents("/tmp/lntest.log",date("Y-m-d H:i:s")."获取均价end"."\n",FILE_APPEND);
        echo json_encode($return_arr);
        // echo "<pre>";print_r($pricearr);
    }
    function get_steelhome_TSCB5price($params)
    {
        // file_put_contents("/tmp/lntest.log",date("Y-m-d H:i:s")."获取均价start"."\n",FILE_APPEND);
        // echo "<pre>";print_r($params);
        $sdate = $params['startTime'];
        $edate = $params['endTime'];
        $priceId = $params['priceId'];
    
        $jiagaid_arr = explode("|",$priceId);
       


        if (!empty($jiagaid_arr)) {
            $jiagaid_str= implode("','", $jiagaid_arr);
            $sql = "select topicture as keyWordsid,price,dateday as date from shpi_material where topicture  in ('$jiagaid_str')  and (dateday >= '" . $sdate . "' and dateday <= '" . $edate . "') order by dateday asc";
            // echo $sql;
            $pricearr = $this->homeDao->query($sql);
        }
    
   
        if (empty($pricearr)) {
            $isSuccess = 0;
        }
        $isSuccess = 1;
        $return_arr = array(
            'Success' => $isSuccess,
            // 'Message'=>'获取给定价格id指定时间内对应的均价',
            'Results' => $pricearr,
        );
        // file_put_contents("/tmp/lntest.log",date("Y-m-d H:i:s")."获取均价end"."\n",FILE_APPEND);
        echo json_encode($return_arr);
        // echo "<pre>";print_r($pricearr);
    }
    function get_steelhome_TSCB6price($params)
    {
        // file_put_contents("/tmp/lntest.log",date("Y-m-d H:i:s")."获取均价start"."\n",FILE_APPEND);
        // echo "<pre>";print_r($params);
        $sdate = $params['startTime'];
        $edate = $params['endTime'];
        $priceId = $params['priceId'];
    
        $jiagaid_arr = explode("|",$priceId);
       


        if (!empty($jiagaid_arr)) {
            $jiagaid_str= implode("','", $jiagaid_arr);
            $sql = "select vid as keyWordsid,weiprice as price,dateday as date from shpi_mj_pzp where vid  in ('$jiagaid_str') and type=1  and (dateday >= '" . $sdate . "' and dateday <= '" . $edate . "') order by dateday asc";
            // echo $sql;
            $pricearr = $this->homeDao->query($sql);
        }
    
   
        if (empty($pricearr)) {
            $isSuccess = 0;
        }
        $isSuccess = 1;
        $return_arr = array(
            'Success' => $isSuccess,
            // 'Message'=>'获取给定价格id指定时间内对应的均价',
            'Results' => $pricearr,
        );
        // file_put_contents("/tmp/lntest.log",date("Y-m-d H:i:s")."获取均价end"."\n",FILE_APPEND);
        echo json_encode($return_arr);
        // echo "<pre>";print_r($pricearr);
    }
    private function getIP() {
        $ip=getenv('REMOTE_ADDR');
        $ip_ = getenv('HTTP_X_FORWARDED_FOR');
        if (($ip_ != "") && ($ip_ != "unknown")) {
            $ip=$ip_;
        }
        $ip=explode(",",$ip);
        return $ip[0];
    }

    private function formatpage($page){
        $page=(int)$page;
        if($page-1<0) $page=1;
        return $page;
    }

    private function getpagelabel($amount,$pagenum,$page,$url){
        $pagemax=($amount%$pagenum==0)?round($amount/$pagenum,0):(floor($amount/$pagenum)+1);
        //echo $amount." ".$pagenum." ".$page." ".$pagemax." ".$url;
        $label="";
        $ye=3;		//最多隔页显示的可点击页数
        if($pagemax==0) return "<div class='page tc oh mt20'><span class='disabled'>上一页</span> <span class='me'> 1 </span> <span class='disabled'>下一页</span></div>";			//如果没有数据，则直接返空
        if($page==1) $label="<span class='disabled'>上一页</span><span class='me'>1</span>"; //第一页
        else $label="<a href='$url&page=".($page-1)."' class='sxy'>上一页</a><a href='$url&page=1'>1</a>";
        for($i=2;$i<$pagemax;$i++){
            if($i-$page>$ye||$page-$i>$ye) continue;
            elseif($i-$page==$ye||$page-$i==$ye){
                $label.="...";
            }elseif($i==$page){
                $label.="<span class='me'>$i</span>";
            }else{
                $label.="<a href='".$url."&page=".$i."'>$i</a>";
            }
        }
        if($pagemax>1) {
            if($pagemax!=$page)
                $label.="<a href='$url&page=$pagemax'>$pagemax</a>"; //最后一页
            else
                $label.="<span class='me'>$page</span>";
        }
        if($page==$pagemax) $label.="<span class='disabled'>下一页</span>";
        else $label.="<a href='".$url."&page=".($page+1)."' class='sxy'>下一页</a>";
        return "<div class='page tc oh mt20'>".$label."</div>";
    }

    private function getpagelabelnew($amount, $pagenum, $page, $url)
    {
        $pagemax = ($amount % $pagenum == 0) ? round($amount / $pagenum, 0) : (floor($amount / $pagenum) + 1);

        //echo $amount." ".$pagenum." ".$page." ".$pagemax." ".$url;
        $label = "<div class='flex justify-between flex-1 sm:hidden'>";
        if ($page == 1 || $pagemax == 0) {
            $label .= "<span class='relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default leading-5 rounded-md'>
                    
			上一页
		</span>";
        } else {
            $label .= "<a href='$url&page=" . ($page - 1) . "' class='relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 leading-5 rounded-md hover:text-gray-500 focus:outline-none focus:shadow-outline-blue focus:border-blue-300 active:bg-gray-100 active:text-gray-700 transition ease-in-out duration-150'>

			上一页
			</a>";
        }

        if ($page == $pagemax || $pagemax == 0) {
            $label .= "<span class='relative inline-flex items-center px-4 py-2 ml-3 text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default leading-5 rounded-md'>下一页</span>";
        } else {
            $label .= "<a  href='$url&page=" . ($page + 1) . "'class='relative inline-flex items-center px-4 py-2 ml-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 leading-5 rounded-md hover:text-gray-500 focus:outline-none focus:shadow-outline-blue focus:border-blue-300 active:bg-gray-100 active:text-gray-700 transition ease-in-out duration-150'>

			下一页
			</a>";
        }
        $label .= "</div>";
        $ye = 10;
        $label .= "<div class='hidden sm:flex-1 sm:flex sm:items-center sm:justify-between'>
		<div>
		<p class='text-sm text-gray-700 leading-5'>
		Showing
		<span class='font-medium'>16</span>
		to
		<span class='font-medium'>30</span>
		of
		<span class='font-medium'>41</span>
		results
		</p>
		</div>";

        if ($page == 1 || $pagemax == 0) {//第一页
            $label .= "<div><span class='relative z-0 inline-flex shadow-sm rounded-md'><span aria-disabled='true' aria-label='&amp;laquo; Previous'>
				<span class='relative inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default rounded-l-md leading-5' aria-hidden='true'>
					<svg class='w-5 h-5' fill='currentColor' viewBox='0 0 20 20'>
						<path fill-rule='evenodd' d='M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z' clip-rule='evenodd'></path>
					</svg>
				</span>
			</span>";
        } else {
            $label .= "<div><span class='relative z-0 inline-flex shadow-sm rounded-md'><a href='$url&page=" . ($page - 1) . "' rel='prev' class='relative inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-l-md leading-5 hover:text-gray-400 focus:z-10 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue active:bg-gray-100 active:text-gray-500 transition ease-in-out duration-150' aria-label='&amp;laquo; Previous' ><svg class='w-5 h-5' fill='currentColor' viewBox='0 0 20 20'>
			<path fill-rule='evenodd' d='M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z' clip-rule='evenodd' />
		</svg></a>";
        }
        if ($pagemax == 0) {
            $label .= "<span aria-current='page'><span class='relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default leading-5'>1</span></span>";
        } else {
            if ($page == 1) {
                $label .= "<span aria-current='page'><span class='relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default leading-5'>1</span></span>";
            } else {
                $label .= "<a href='$url&page=1' class='relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-gray-700 bg-white border border-gray-300 leading-5 hover:text-gray-500 focus:z-10 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue active:bg-gray-100 active:text-gray-700 transition ease-in-out duration-150' aria-label='Go to page 1'>1</a>";
            }
            $label1 = "<span class='relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-gray-700 bg-white border border-gray-300 leading-5 hover:text-gray-500 focus:z-10 focus:outline-none focus:border-blue-300  transition ease-in-out duration-150'>...</span>";

            for ($i = 2; $i < $pagemax; $i++) {
                $label2 = "<span aria-current='page'><span class='relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default leading-5'>$i</span></span>";
                $label3 = "<a href='$url&page=$i' class='relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-gray-700 bg-white border border-gray-300 leading-5 hover:text-gray-500 focus:z-10 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue active:bg-gray-100 active:text-gray-700 transition ease-in-out duration-150' aria-label='Go to page $i'>$i</a>";

                if ($page < $ye - 1) {//前10，
                    if ($i > $ye) continue;
                    if ($i == $ye) {
                        $label .= $label1;
                    } elseif ($i == $page) {
                        $label .= $label2;
                    } else {
                        $label .= $label3;
                    }
                } else if ($pagemax - $page < $ye - 1) {//后10
                    if ($i < $pagemax - $ye) continue;
                    if ($i == $pagemax - $ye) {
                        $label .= $label1;
                    } elseif ($i == $page) {
                        $label .= $label2;
                    } else {
                        $label .= $label3;
                    }
                } else {//中间数
                    if ($i < $page - 2 || $i > $page + $ye - 2) continue;
                    if ($i == $page - 2 || $i == $page + $ye - 2) {
                        $label .= $label1;
                    } elseif ($i == $page) {
                        $label .= $label2;
                    } else {
                        $label .= $label3;
                    }
                }

            }
        }

        if ($pagemax > 1) {
            if ($pagemax != $page)
                $label .= "<a href='$url&page=$pagemax'  class='relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-gray-700 bg-white border border-gray-300 leading-5 hover:text-gray-500 focus:z-10 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue active:bg-gray-100 active:text-gray-700 transition ease-in-out duration-150' aria-label='Go to page $pagemax'>$pagemax</a>"; //最后一页
            else
                $label .= "<span aria-current='page'><span class='relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default leading-5'>$page</span></span>";
        }
        if ($page == $pagemax || $pagemax == 0) {
            $label .= "<span aria-disabled='true' aria-label='Next &amp;raquo;'>
				<span class='relative inline-flex items-center px-2 py-2 -ml-px text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default rounded-r-md leading-5' aria-hidden='true'>
					<svg class='w-5 h-5' fill='currentColor' viewBox='0 0 20 20'>
						<path fill-rule='evenodd' d='M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z' clip-rule='evenodd' />
					</svg>
				</span>
			</span>";
        } else {
            $label .= "<a href='" . $url . "&page=" . ($page + 1) . "' rel='next' class='relative inline-flex items-center px-2 py-2 -ml-px text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-r-md leading-5 hover:text-gray-400 focus:z-10 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue active:bg-gray-100 active:text-gray-500 transition ease-in-out duration-150' aria-label='Next &amp;raquo;'>
				<svg class='w-5 h-5' fill='currentColor' viewBox='0 0 20 20'>
					<path fill-rule='evenodd' d='M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z' clip-rule='evenodd'></path>
				</svg></a>";
        }

        $label .= "</span></div>";

        return "<div align='right' style='margin-top:5px; font-size:12px'> <nav role='navigation' aria-label='Pagination Navigation' class='flex items-center justify-between'>" . $label . "</nav></div>";
    }

    /**
     * 中文json打包+转码
     * Created by zfy.
     * Date:2021/1/27 10:49
     * @param $array
     * @return string
     */
    private function pri_JSON($array)
    {
        $this->pri_arrayRecursive($array, 'urlencode', true);
        $json = json_encode($array);
        return urldecode($json);
    }

    private function pri_arrayRecursive(&$array, $function, $apply_to_keys_also = false)
    {
        static $recursive_counter = 0;
        if (++$recursive_counter > 1000) {
            die('possible deep recursion attack');
        }
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $this->pri_arrayRecursive($array[$key], $function, $apply_to_keys_also);
            } else {
                $array[$key] = $function($value);
            }
            if ($apply_to_keys_also && is_string($key)) {
                $new_key = $function($key);
                if ($new_key != $key) {
                    $array[$new_key] = $array[$key];
                    unset($array[$key]);
                }
            }
        }
        $recursive_counter--;
    }

}

?>