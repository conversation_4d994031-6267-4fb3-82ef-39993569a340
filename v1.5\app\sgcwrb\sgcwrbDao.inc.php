<?php
class sgcwrbDao extends Dao{
  public function __construct( $writer ){
    parent::__construct( $writer );
  }

  public function getYB($dta_ym,$dta_9,$dta_1){
	$where = "";
	if (!empty($dta_1)) {
		$where .= " and dta_1 like '%$dta_1%'";
	}
	$sql = "select dta_type,dta_vartype,dta_7,dta_5 from sg_data_table where dta_type like '%SG_CBYBB_%' and dta_ym = '$dta_ym' and dta_9 = '$dta_9' $where";
	// echo $sql."<br>";
	$sginfo = $this->query($sql);
	return $sginfo;
  }

  public function getRB($dta_ym){
	$sql = "select dta_type,dta_1,dta_2,dta_3,dta_4,dta_5,dta_6 from sg_data_table where dta_type like '%SGcbrb%' and dta_ym = '$dta_ym'";
	$sginfo = $this->query($sql);
	$zt_chanling = array();
	foreach($sginfo as $v){
		$zt_chanling[$v['dta_type']]['dta_1'] = round($v['dta_1'],2);
		$zt_chanling[$v['dta_type']]['dta_2'] = round($v['dta_2'],2);
		$zt_chanling[$v['dta_type']]['dta_3'] = round($v['dta_3'],2);
		$zt_chanling[$v['dta_type']]['dta_4'] = round($v['dta_4'],2);
		$zt_chanling[$v['dta_type']]['dta_5'] = round($v['dta_5'],2);
		$zt_chanling[$v['dta_type']]['dta_6'] = round($v['dta_6'],2);
	}
	return $zt_chanling;
  }

  public function get_FG($value_zd, $table, $where, $date_zd, $date){
	$sql = "select $value_zd from $table $where and $date_zd = '$date'";  
	// echo $sql."<br>";
	return $this->getone($sql);
  }

  public function get_shpi_material($value_zd, $topicture_arr, $date){
	$topicture_str = implode("','",$topicture_arr);
	$sql = "select topicture,$value_zd from shpi_material where topicture in ('$topicture_str') and dateday = '$date'";  
	// echo $sql."<br>";
	$shpi_material_info = $this->query($sql);
	foreach ($shpi_material_info as $v) {
		$shpi_material[$v['topicture']] = $v['price'];
	}
	return $shpi_material;
  }

  public function bfjghz($date){
	$sql = "select dta_1, dta_2, dta_3 from sg_data_table where dta_type = 'SG_BFJGHZ' and dta_ym = '$date'";  
	// echo $sql."<br>";
	return $this->getrow($sql);
  }

  public function getSJCGIndex($ndate,$vtype){
	$sql = "select CityName,`index` from SJCGIndex where Date='$ndate' and Type='$vtype' and DType='0'  and Status=1";
	$SJCGIndex_info = $this->query($sql);
	return $SJCGIndex_info;
  }

  public function getsg_data($dta_type, $date){
	$sql = "select dta_2,dta_5 from sg_data_table where dta_type='$dta_type' and dta_ym = '$date'";
	// echo $sql."<br>";
	$info = $this->query($sql);
	return $info;
  }

  public function getsg_data2($dta_type, $sdate ,$edate){
	$sql = "select dta_2,sum(dta_5) as dta_5 from sg_data_table where dta_type='$dta_type' and dta_ym >= '$sdate' and dta_ym <= '$edate' group by dta_2";
	// echo $sql."<br>";
	$info = $this->query($sql);
	return $info;
  }

  public function getsg_data_xiaoshou($dta_type, $sdate ,$edate){
	$sql = "select dta_4,sum(dta_5) as dta_5 from sg_data_table where dta_type='$dta_type' and dta_ym >= '$sdate' and dta_ym <= '$edate' and dta_1='建材' and dta_7='合计' group by dta_4";
	// echo $sql."<br>";
	$info = $this->query($sql);
	return $info;
  }

  //通过guid取得用户信息
	public function get_userinfo_byguid($GUID){
		$sql = "select * from app_session_temp where  GUID ='$GUID'  order by  LoginDate DESC  limit 1";
		return $this->getRow($sql);
  	}
  
  //获取用户权限和用户名
	public function get_license_privilege($Uid){
		$sql = "select * from app_license,app_license_privilege where  app_license.id=app_license_privilege.lid and app_license_privilege.Uid='$Uid' and app_license_privilege.mc_type=1 limit 1";
		return $this->getRow($sql);
	}
}
?>