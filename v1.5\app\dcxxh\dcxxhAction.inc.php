
<?php
include_once (APP_DIR."/dcxxh/project.conf.php");
// include_once('/etc/steelconf/config/isholiday.php');

class dcxxhAction extends AbstractAction{
	
	var $client = null;

  public function __construct(){
    parent::__construct();    

  }



  
	public function index($params)
	{
		
	}
	public function error($params)
	{
		$msg=urldecode($params['msg']);
		$strurl=urldecode($params['strurl']);
		$etype=$params['etype'];
		$this->assign("msg",$msg);
		$this->assign("strurl",$strurl);
		$this->assign("etype",$etype);
		$this->assign("GUID",$_SESSION['GUID']?$_SESSION['GUID']:$params['GUID']);
		$this->assign("mc_type",$_SESSION['mc_type']?$_SESSION['mc_type']:$params['mc_type']);
	}
	public function screen($params)
	{
		$this->isgotoweb($params['mode']);
		$id=$params['id'];
		$GUID = $params['GUID'];
		$mc_type = $params['mc_type']?$params['mc_type']:0;
		if (empty($GUID)) {
			
			//alert("请登录");
			$new_url=DC_URL.'/v1.5/dcxxh.php?view=error&msg='.urlencode('系统检测到您还未登录，请先登录');
			echo file_get_contents($new_url);exit;
            header("Location:$new_url");
			exit;
		}
		$sql = "select * from app_session_temp where GUID='".$GUID."' and mc_type='".$mc_type."'";
		$userinfo = $this->t1dao->getRow($sql);
		if (empty($userinfo )) {
			$new_url=DC_URL.'/v1.5/dcxxh.php?view=error&msg='.urlencode('用户不存在');
			echo file_get_contents($new_url);exit;
            header("Location:$new_url");
			exit;
		}

		$sql="select * from DataScreenTempList where id='".$id."' and isdel=0  ";
		$sceninfo=$this->t1dao->getrow($sql);
		if(empty($sceninfo))
		{
			//echo "您还未创建页面，请先创建页面"; 
			$new_url=DC_URL.'/v1.5/dcxxh.php?view=error&msg='.urlencode('您访问的页面不存在，请核对后再试');
			echo file_get_contents($new_url);exit;
            header("Location:$new_url");
			exit;
			
		}
		$shareidarr=array();
		if($sceninfo['shareid'])
		{
			$shareidarr=explode(',',$sceninfo['shareid']);
		}
		if(!($sceninfo['mid']==$userinfo['Mid']||in_array($userinfo['Mid'],$shareidarr)))
		{
			//echo "您还未创建页面，请先创建页面"; 
			$new_url=DC_URL.'/v1.5/dcxxh.php?view=error&msg='.urlencode('您访问的页面不存在，请核对后再试');
			echo file_get_contents($new_url);exit;
            header("Location:$new_url");
			exit;
			
		}

		$sql="select * from DataScreenTemplate where parentid='".$id."' and isdel=0  order by sort ";
		$DataScreenTemplate=$this->t1dao->query($sql);

	
		if(empty($DataScreenTemplate))
		{
			//echo "您还未创建页面，请先创建页面"; 
			$new_url=DC_URL.'/v1.5/dcxxh.php?view=error&msg='.urlencode('您还未创建页面，请先创建页面');
			echo file_get_contents($new_url);exit;
            header("Location:$new_url");
			exit;
			
		}
		else
		{
			$labelcount=count($DataScreenTemplate);
			$TopName=$DataScreenTemplate[0]['TopName'];
			//$LabelName=$DataScreenTemplate[0]['LabelName'];
		}
		//$GUID='testaccount';
		$SignCS = "63cf7d3ea46b8d3ade7d6ceece903dd4";
		//$DateStart=date('Y-m-d',strtotime('-1 year'));
		$DateEnd=date('Y-m-d');
		$theme=$sceninfo['theme'];
		if(isset($params['theme']))
		{
			$theme=$params['theme'];
			$sceninfo['theme']=$params['theme'];
		}

		foreach ($DataScreenTemplate as $key => $value) 
		{
			if($value['type']>1)
			{
				for($i=1;$i<9;$i++)
				{
					if($value['D'.$i])
					{
						$manmonth=$value['labeltime'.$i]?$value['labeltime'.$i]:12;
						$DateStart=date('Y-m-d',strtotime('-'.$manmonth.' month'));
						$DataScreenTemplate[$key]['D'.$i]=DC_URL."/v1.5/" .base64_decode($value['D'.$i])."&GUID=".$GUID."&SignCS=".$SignCS."&DateStart=".$DateStart."&DateEnd=".$DateEnd."&theme=".$theme;
					}

				}

			}
		}

        $this->assign("labelcount",$labelcount);
		$this->assign("labelwidth",Round(100/$labelcount,3));
		$this->assign("TopName",$TopName);
		$this->assign("logo",$sceninfo['logourl']?'<img src="'.IMGURL.$sceninfo['logourl'].'" height="46px">':'');
		$this->assign("sjdate",date('Y.n.j'));
		$this->assign("sceninfo",$sceninfo);
		$this->assign("DataScreenTemplate",$DataScreenTemplate);
	}
	public function tagscreen($params)
	{
		$this->isgotoweb($params['mode']);
		$id=$params['id'];
		$GUID = $params['GUID'];
		$mc_type = $params['mc_type'];
		if (empty($GUID)) {
			//alert("请登录");
			$new_url=DC_URL.'/v1.5/dcxxh.php?view=error&msg='.urlencode('系统检测到您还未登录，请先登录');
			echo file_get_contents($new_url);exit;
            header("Location:$new_url");
			exit;
		}
		$sql = "select * from app_session_temp where GUID='".$GUID."' and mc_type='".$mc_type."'";
		$userinfo = $this->t1dao->getRow($sql);
		if (empty($userinfo )) {
			//alert("用户不存在");
			$new_url=DC_URL.'/v1.5/dcxxh.php?view=error&msg='.urlencode('用户不存在');
			echo file_get_contents($new_url);exit;
            header("Location:$new_url");
			exit;
		}

        $sql="select * from DataScreenTemplate where id='".$id."' and  mid='".$userinfo['Mid']."' and isdel=0 ";
		$DataScreenTemplate=$this->t1dao->query($sql);
		
		$sql="select * from DataScreenTempList where id='".$DataScreenTemplate[0]['parentid']."' and isdel=0 ";
		$sceninfo=$this->t1dao->getrow($sql);


		

	
		if(empty($DataScreenTemplate))
		{
			//echo "您访问的页面不存在，请核对后再试！"; 
			$new_url=DC_URL.'/v1.5/dcxxh.php?view=error&msg='.urlencode("您访问的页面不存在，请核对后再试！");
			echo file_get_contents($new_url);exit;
            header("Location:$new_url");
			
			exit;
			
		}
		else
		{
			$labelcount=count($DataScreenTemplate);
			$TopName=$DataScreenTemplate[0]['TopName'];
			//$LabelName=$DataScreenTemplate[0]['LabelName'];
		}
		//$GUID='testaccount';
		$SignCS = "63cf7d3ea46b8d3ade7d6ceece903dd4";
		//$DateStart=date('Y-m-d',strtotime('-1 year'));
		$DateEnd=date('Y-m-d');
		$theme=$sceninfo['theme'];
		$welcomediv='';
		foreach ($DataScreenTemplate as $key => $value) 
		{
			if($value['type']>1)
			{
				for($i=1;$i<9;$i++)
				{
					if($value['D'.$i])
					{
						$manmonth=$value['labeltime'.$i]?$value['labeltime'.$i]:12;
						$DateStart=date('Y-m-d',strtotime('-'.$manmonth.' month'));
						$DataScreenTemplate[$key]['D'.$i]=DC_URL."/v1.5/" .base64_decode($value['D'.$i])."&GUID=".$GUID."&SignCS=".$SignCS."&DateStart=".$DateStart."&DateEnd=".$DateEnd."&theme=".$theme;
					}

				}

			}
			else
			{
				//print_r($DataScreenTemplate[$key]['fontcontent']);
				$fontcontentarr=explode('<br/>',$DataScreenTemplate[$key]['fontcontent']);
				foreach($fontcontentarr as $k => $v)
				{
					if($k==0)
					{
						$welcomediv.='<h1 style="background: linear-gradient(180deg, #ffffff 30%, '.($value['fontcolor']?$value['fontcolor']:'#4391ec').' 100%);-webkit-background-clip: text;">'.$v.'</h1>';
						//$welcomediv.='<h1 >'.$v.'</h1>';
					}
					else
					{
						$welcomediv.='<h2 style="color:'.($value['fontcolor']?$value['fontcolor']:'#3786e8').';font-size:'.$value['fontsize'].'">'.$v.'</h2>';
					}

				}
			}
		}
        $this->assign("labelcount",$labelcount);
		$this->assign("labelwidth",Round(100/$labelcount,3));
		$this->assign("TopName",$TopName);
		$this->assign("logo",$sceninfo['logourl']?'<img src="'.IMGURL.$sceninfo['logourl'].'" height="46px">':'');
		$this->assign("sjdate",date('Y.n.j'));
		$this->assign("sceninfo",$sceninfo);
		$this->assign("DataScreenTemplate",$DataScreenTemplate);
		$this->assign("welcomediv",$welcomediv);
	}
	
	
	public function map($params)
	{
		
		$pzarr=$params['ProductType']==2?$GLOBALS['YL']:$GLOBALS['GC'];
		$this->pri_arrayRecursive($pzarr, 'gbk2utf8', true);
		$this->assign("pzarr",$pzarr);
		$classinfo = $params['skin']=='blue'?'theme-blue':'';
		$this->assign("classinfo",$classinfo);
	}

	public function isgotoweb($mode)
    {
		if ($mode == "2" || $mode == "4") {
			$new_url = DC_URL.'/v1.5/dcxxh.php?view=error&etype=2&msg='.urlencode('请去电脑端或者pad端查看大屏！');
			//header("Location:$new_url");
			//exit;
			echo file_get_contents($new_url);exit;
		}
	}

	public function xxhindex($params)
    {
		$this->isgotoweb($params['mode']);
		$this->set_userinfo($params);
		$uid = $_SESSION['Uid'];
		$mid = $_SESSION['Mid'];
		$mc_type = $_SESSION['mc_type'];
		$where =" and (userid = '".$uid."' or shareid like '%".$uid."%') and mc_type = '".$mc_type."'";
        if (!empty($params['MyTempName'])) {
			$where .=" and MyTempName like '%".$params['MyTempName']."%'";
		}
		$where .=" and isdel='0'";
		// echo $mc_type;exit;
        $DataScreenTempList = $this->t1dao->getDataScreenTempList2($where);
		if (empty($DataScreenTempList)) {
			$new_url=DC_URL.'/v1.5/dcxxh.php?view=error&msg='.urlencode('您暂未设置数据大屏，请先设置数据大屏').'&strurl='.urlencode("dcxxh.php?view=template_list");
			//ob_clean();
            //header("Location:$new_url");
			$url=DC_URL.'/v1.5/dcxxh.php?view=error&msg='.urlencode('您暂未设置数据大屏，请先设置数据大屏').'&strurl='.urlencode("dcxxh.php?view=template_list").'&GUID='.$_SESSION['GUID'].'&mc_type='.$_SESSION['mc_type'];;
            //echo $url;
            echo file_get_contents($url);exit;
			exit;
		}
        $this->assign("DataScreenTempList", $DataScreenTempList);
        $this->assign("params", $params);
		$this->assign("GUID", $_SESSION['GUID']);
		$this->assign("mc_type", $_SESSION['mc_type']);
	}

	public function template_list($params)
    {
		// 清空_SESSION
		$_SESSION = array();

		$this->set_userinfo($params);
		$uid = $_SESSION['Uid'];
		$mid = $_SESSION['Mid'];
        $url = "dcxxh.php";
        $per = 20;
        $page = $params['page'] == '' ? 1 : $params['page'];
        $start = ($page - 1) * $per;
		$where =" and userid = '".$uid."' and mc_type = '".$params['mc_type']."' ";
		// echo "<pre>";print_r($this->t1dao);
		$params['MyTempName'] = str_replace("'", "", $params['MyTempName']);
        if (!empty($params['MyTempName'])) {
			$where .="and MyTempName like '%".$params['MyTempName']."%'";
		}
		$where .="and isdel='0'";
        $total = $this->t1dao->getDataScreenTempListTotal($where);
        $DataScreenTempList = $this->t1dao->getDataScreenTempList($where, $start, $per);
		foreach ($DataScreenTempList as &$v) {
			$v['createtime'] = date("Y-m-d", strtotime($v['createtime']));
			if ($v['userid'] == $uid) {
				$v['power'] = "A";
			} else {
				$v['power'] = "B";
			}
		}
		// echo "<pre>";print_r($DataScreenTempList);
        unset($params['page']);
        $pagebar = $this->PagebarDiv($url, $params, $per, $page, $total);
        $this->assign("fenye", $pagebar);
        $this->assign("DataScreenTempList", $DataScreenTempList);
        $this->assign("params", $params);
		$this->assign("GUID", $_SESSION['GUID']);
		$this->assign("Mid", $_SESSION['Mid']);
		$this->assign("mc_type", $_SESSION['mc_type']);
    }

	public function template_edit($params)
    {
		$this->set_userinfo($params);
		$id = $params['id'];
		if (empty($id)) {
			$DataScreenTemp = array();
			$DataScreenTemp['theme'] = 1;
			$DataScreenTemp['shareid'] = array();
		} else {
			$DataScreenTemp = $this->t1dao->getDataScreenTempById($id);
			$DataScreenTemp['shareid'] = explode(",", $DataScreenTemp['shareid']);
		}
		$themearr = array("1"=>"炫酷蓝", "2"=>"优雅白");
		//共享使用
		$users = $this->t1dao->getuser_mid($_SESSION['Mid'],$_SESSION['mc_type'],$_SESSION['Uid']);
		if ($_SESSION['Mid'] == "1") {
			//复制使用
			$fzcoms = $this->getFzCom($_SESSION['mc_type']);
		}
		// echo "<pre>";print_r($DataScreenTemp);
        $this->assign("DataScreenTemp", $DataScreenTemp);
        $this->assign("themearr", $themearr);
        $this->assign("users", $users);
        $this->assign("fzcoms", $fzcoms);
        $this->assign("params", $params);
		$this->assign("GUID", $_SESSION['GUID']);
		$this->assign("mc_type", $_SESSION['mc_type']);
		$this->assign("Mid", $_SESSION['Mid']);
		$this->assign("IMGURL", IMGURL);
    }

	public function getuid($params)
    {
		$sql = "SELECT Uid,TrueName from app_session_temp where Mid='".$params['mid']."' and mc_type='".$params['mc_type']."' group by Uid";
		// echo $sql;exit;
		$userinfo = $this->t1dao->query($sql);
		// echo "<pre>";print_r($this->array_iconv('gbk','utf-8',$userinfo));
		//echo json_encode($this->array_iconv('gbk','utf-8',$userinfo));
		echo json_encode($userinfo);
	}

	function getFzCom($mc_type)
	{
		$sql = "SELECT Mid,ComName from app_session_temp where mc_type='".$mc_type."' and Mid!='1' group by Mid";
		$fzcoms = $this->t1dao->query($sql);
		return $fzcoms;
	}

	function random($length)
	{
		$hash = 'CR-';
		$chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789abcdefghijklmnopqrstuvwxyz';
		$max = strlen ( $chars ) - 1;
		mt_srand ( ( double ) microtime () * 1000000 );
		for($i = 0; $i < $length; $i ++)
		{
		$hash .= $chars [mt_rand ( 0, $max )];
		}
		return $hash;
	}

	public function template_doedit($params)
    {
		$action = $params['action'];        //接口名称
		$this->set_userinfo($params);
		// echo "<pre>";print_r($params);exit;
		//$params = $this->array_iconv('utf-8','gbk',$params);
		$params['shareid'] =is_array($params['shareid'])?implode(",",$params['shareid']):"";
		$ip = $this->getIP();
		$mid = $_SESSION['Mid'];
		$userid = $_SESSION['Uid'];
		$truename = $_SESSION['TrueName'];
		$mc_type = $_SESSION['mc_type'];
		$array = array("shareid","sort","MyTempName","theme","chtime");

		$arrtem = $params;
		$actionstr = "";
		foreach ($arrtem as $k => $v) {
			$actionstr .= "&" . $k . "=" . $v;
		}
		//图片上传
		if ($mc_type == "4") {
			if ($params['isupfilexg'] == "1") {
				$params['logourl'] = "dplogos/xinsteellogo.png";
				$array[] = "logourl";
			} else {
				$params['logourl'] = "";
				$array[] = "logourl";
			}
		}
		elseif ($mc_type == "5") {
			if ($params['isupfilexg'] == "1") {
				$params['logourl'] = "dplogos/shansteellogo.png";
				$array[] = "logourl";
			} else {
				$params['logourl'] = "";
				$array[] = "logourl";
			}
		}
		 else {
			$upfile = $_FILES ['upfile'];
			if ($upfile ['error'] == UPLOAD_ERR_OK && !empty($upfile)) {	
				// 取得扩展名
				$extName=strtolower(end(explode('.',$upfile['name'])));
				$filename = $this->random(5); // 设置随机数长度
				$name=date('YmdHis').".".$extName;
				$dest =IMGSRC."dplogos/".$filename.$name;
				//转存
				move_uploaded_file ( $upfile ['tmp_name'], $dest );
				$logourl = "dplogos/".$filename.$name;
				$params['logourl'] = $logourl;
				$array[] = "logourl";
			}
			if (!empty($params['dellogourl'])) {
				$params['logourl'] = "";
				$array[] = "logourl";
			}
		}
		// echo "<pre>";print_r($array);
		
		$data = $this->getdata( $array, $params );
		if (!empty($data)) {
			if (empty($params['id'])) {
				$sql = "insert into DataScreenTempList SET $data,mid='".$mid."',userid='".$userid."',truename='".$truename."',mc_type='".$mc_type."',createtime=now()";
				// echo $sql;
				$this->t1dao->execute($sql);

				$this->t1dao->WriteLog($mid, $userid, " ", $action, $actionstr, $ip, "", "", "设置大屏-新增", '', '', $mc_type);
			} else {
				$sql = "update DataScreenTempList set $data,updateadminid='".$userid."',updateadminname='".$truename."',updatetime=now() where id='".$params['id']."'";
				// echo $sql;
				$this->t1dao->execute($sql);

				$this->t1dao->WriteLog($mid, $userid, " ", $action, $actionstr, $ip, "", "", "设置大屏-修改", '', '', $mc_type);
			}
			// 复制使用 start
			$copyidarr = explode(",",$params['copyid']);
			// $copyid_oldarr = explode(",",$params['copyid_old']);
			// $copyid_newarr = array_diff($copyidarr, $copyid_oldarr);
			$copyid_newarr = $copyidarr;
			if (!empty($params['copyid'])) {
				$userinfo = $this->getUserInfoById($copyid_newarr, $mc_type);
				//获取大屏内标签
				$plateinfo = array();
				if (!empty($params['id'])) {
					$plateinfo = $this->getPlateByParentid($params['id']);
				}
				$sqlv_plate = "";
				foreach ($copyid_newarr as $copyid) {
					//复制大屏
					$maxsort = $this->t1dao->getOne("select max(sort) from DataScreenTempList where userid='".$copyid."' and mc_type='".$mc_type."' and isdel='0'");
					$params['sort'] = $maxsort + 1;
					$data2 = $this->getdata( $array, $params );
					$sql = "insert into DataScreenTempList SET $data2,mid='".$userinfo[$copyid]['Mid']."',userid='".$copyid."',truename='".$userinfo[$copyid]['TrueName']."',mc_type='".$mc_type."',createtime=now()";
					// echo $sql."<br>";
					$this->t1dao->execute($sql);
					//复制大屏内标签
					if (!empty($plateinfo)) {
						// $parentid = $this->t1dao->insert_id();
						$parentid = $this->t1dao->getOne("select id from DataScreenTempList where mc_type='".$mc_type."' and isdel='0' order by id desc limit 1");
						foreach ($plateinfo as $v) {
							if (!empty($sqlv_plate)) {
								$sqlv_plate .= ",";
							}
							$sqlv_plate .= "('".$userinfo[$copyid]['Mid']."','".$copyid."','".$userinfo[$copyid]['TrueName']."',now(),'".$v['sort']."','".$v['type']."','".$v['D1']."','".$v['D2']."','".$v['D3']."','".$v['D4']."','".$v['D5']."','".$v['D6']."','".$v['D7']."','".$v['D8']."','".$v['labeltime1']."','".$v['labeltime2']."','".$v['labeltime3']."','".$v['labeltime4']."','".$v['labeltime5']."','".$v['labeltime6']."','".$v['labeltime7']."','".$v['labeltime8']."','".$v['pic_key1']."','".$v['pic_key2']."','".$v['pic_key3']."','".$v['pic_key4']."','".$v['pic_key5']."','".$v['pic_key6']."','".$v['pic_key7']."','".$v['pic_key8']."','".$v['pc_title1']."','".$v['pc_title2']."','".$v['pc_title3']."','".$v['pc_title4']."','".$v['pc_title5']."','".$v['pc_title6']."','".$v['pc_title7']."','".$v['pc_title8']."','".$v['fontcontent']."','".$v['fontsize']."','".$v['fontcolor']."','".$v['TopName']."','".$v['LabelName']."','".$parentid."')";
						}
					}
					
				}
				if (!empty($sqlv_plate)) {
					$sql_plate = "INSERT INTO DataScreenTemplate(`mid`,`userid`,`truename`,`createtime`,`sort`,`type`,`D1`,`D2`,`D3`,`D4`,`D5`,`D6`,`D7`,`D8`,`labeltime1`,`labeltime2`,`labeltime3`,`labeltime4`,`labeltime5`,`labeltime6`,`labeltime7`,`labeltime8`,`pic_key1`,`pic_key2`,`pic_key3`,`pic_key4`,`pic_key5`,`pic_key6`,`pic_key7`,`pic_key8`,`pc_title1`,`pc_title2`,`pc_title3`,`pc_title4`,`pc_title5`,`pc_title6`,`pc_title7`,`pc_title8`,`fontcontent`,`fontsize`,`fontcolor`,`TopName`,`LabelName`,`parentid`) VALUES " . $sqlv_plate;
					// echo $sql_plate;
					$this->t1dao->execute($sql_plate);
				}
			}
			// 复制使用 end
		}
		echo '1';
    }
	public function getPlateByParentid($parentid)
    {
		$sql = "select * from DataScreenTemplate where parentid='".$parentid."' and isdel='0'";
		$plateinfo = $this->t1dao->query($sql);
		return $plateinfo;
	}

	public function getUserInfoById($idarr, $mc_type)
    {
		
		$sql = "select * from app_session_temp where mc_type='".$mc_type."' and Uid in ('".implode("','", $idarr)."') group by Uid ";
		// echo $sql;
		$userarr = $this->t1dao->query($sql);
		$userinfo = array();
		foreach ($userarr as $v) {
			$userinfo[$v['Uid']]['Uid'] = $v['Uid'];
			$userinfo[$v['Uid']]['Mid'] = $v['Mid'];
			$userinfo[$v['Uid']]['ComName'] = $v['ComName'];
			$userinfo[$v['Uid']]['UserName'] = $v['UserName'];
			$userinfo[$v['Uid']]['TrueName'] = $v['TrueName'];
		}
		return $userinfo;
	}

	public function template_del($params)
    {
		$action = $params['action'];        //接口名称
		$this->set_userinfo($params);
		$mid = $_SESSION['Mid'];
		$userid = $_SESSION['Uid'];
		$truename = $_SESSION['TrueName'];
		$mc_type = $_SESSION['mc_type'];
		$ip = $this->getIP();

		$sql = "update DataScreenTempList set isdel='1',deladminid='".$userid."',deltime=now() where id='".$params['id']."'";
		$this->t1dao->execute($sql);
		$sql = "update DataScreenTemplate set isdel='1',deladminid='".$userid."',deltime=now() where parentid='".$params['id']."'";
		$this->t1dao->execute($sql);

		$arrtem = $params;
		$actionstr = "";
		foreach ($arrtem as $k => $v) {
			$actionstr .= "&" . $k . "=" . $v;
		}
		$this->t1dao->WriteLog($mid, $userid, " ", $action, $actionstr, $ip, "", "", "设置大屏-删除", '', '', $mc_type);

		echo '1';
	}

	public function set_userinfo($params)
    {
		if (empty($_SESSION['Uid'])) {
			$GUID = $params['GUID'];
			$mc_type = $params['mc_type'];
			if (empty($GUID) || $mc_type == "") {
				// $new_url=DC_URL.'/v1.5/dcxxh.php?view=error&etype=2&msg='.urlencode('系统检测到您还未登录，请先登录');
				// header("Location:$new_url");
				$new_url=DC_URL.'/v1.5/dcxxh.php?view=error&etype=2&msg='.urlencode('系统检测到您还未登录，请先登录');
				echo file_get_contents($new_url);exit;
				exit;
			}
			$sql = "select * from app_session_temp where GUID='".$GUID."' and mc_type='".$mc_type."'";
			// echo $sql;
			$userinfo = $this->t1dao->getRow($sql);
			// echo "<pre>"; print_r($userinfo);
			if (empty($userinfo)) {
				$new_url=DC_URL.'/v1.5/dcxxh.php?view=error&etype=2&msg='.urlencode('用户不存在');
				//header("Location:$new_url");
				echo file_get_contents($new_url);exit;
				exit;
			}
			$_SESSION['GUID'] = $GUID;
			$_SESSION['mc_type'] = $mc_type;
			$_SESSION['Mid'] = $userinfo['Mid'];
			$_SESSION['ComName'] = $userinfo['ComName'];
			$_SESSION['Uid'] = $userinfo['Uid'];
			$_SESSION['UserName'] = $userinfo['UserName'];
			$_SESSION['TrueName'] = $userinfo['TrueName'];
			$_SESSION['mode'] = $params['mode'];
		}
		// echo "<pre>";print_r($_SESSION);
	}

	public function get_userinfo()
    {
		$userinfo = array();
		$userid = "1";
		$truename = "测试";
		$userinfo['userid'] = $userid;
		$userinfo['truename'] = $truename;
		return $userinfo;
	}

	public function label_list($params)
    {
		$this->set_userinfo($params);
        $where ="";
        if (!empty($params['MyTempName'])) {
			$where .="and MyTempName like '%".$params['MyTempName']."%'";
		}
		$where .="and parentid='".$params['parentid']."' and isdel='0'";//暂时查全部
        $getDataScreenTemplateList = $this->t1dao->getDataScreenTemplateList($where);
        // echo "<pre>";print_r($getDataScreenTemplateList);
        $this->assign("getDataScreenTemplateList", $getDataScreenTemplateList);
        $this->assign("GUID", $_SESSION['GUID']);
		$this->assign("mc_type", $_SESSION['mc_type']);
        $this->assign("params", $params);
    }

	public function openlabel($params)
    {
		$this->set_userinfo($params);
		$uid = $_SESSION['Uid'];
        $where ="";
		$where .="and parentid='".$params['parentid']."' and isdel='0'";//暂时查全部
        $getDataScreenTemplateList = $this->t1dao->getDataScreenTemplateList($where);
		// echo "<pre>";print_r($getDataScreenTemplateList);
		$tbstr = "";
		foreach ($getDataScreenTemplateList as $k=>$v) {
			if ($v['userid'] == $uid) {
				$style1 = '';
			} else {
				$style1 = 'style="pointer-events: none;color:gray;"';
			}
			$tbstr .= '<tr class="labeltr_'.$params['parentid'].'"><td></td><td></td>';
			$tbstr .= '<td>'.$v['sort'].'</td>';
			$tbstr .= '<td>'.$v['LabelName'].'</td>';
			$tbstr .= '<td>'.$v['TopName'].'</td>';
			if ($v['type'] == "1") {
				$tbstr .= '<td>欢迎页</td>';
			} else {
				$tbstr .= '<td>'.$v['type'].'分屏</td>';
			}
			$tbstr .= '<td>'.$v['truename'].'</td>';
			$tbstr .= '<td>'.date("Y-m-d", strtotime($v['createtime'])).'</td>';
			$tbstr .= '<td class="w180 operate">
			<a target="_blank" href="dcxxh.php?view=tagscreen&id='.$v['id'].'&GUID='.$params['GUID'].'&mc_type='.$params['mc_type'].'">
				<svg t="1587522217290" viewBox="0 0 1024 1024" version="1.1" p-id="4587" width="14"
					 height="14">
					<path d="M897.28 85.333333h-750.933333c-28.16 0-51.2 23.04-51.2 51.2v750.933334c0 28.16 23.04 51.2 51.2 51.2h276.906666c18.773333 0 34.133333-15.36 34.133334-34.133334s-15.36-34.133333-34.133334-34.133333H163.413333V153.6h716.8v275.626667c0 18.773333 15.36 34.133333 34.133334 34.133333s34.133333-15.36 34.133333-34.133333V136.533333c0-28.16-22.613333-51.2-51.2-51.2z" fill="#97B8D4" p-id="4588"/>
					<path d="M649.813333 315.733333h-324.266666c-18.773333 0-34.133333-15.36-34.133334-34.133333s15.36-34.133333 34.133334-34.133333h324.266666c18.773333 0 34.133333 15.36 34.133334 34.133333s-14.933333 34.133333-34.133334 34.133333zM564.48 452.266667h-238.933333c-18.773333 0-34.133333-15.36-34.133334-34.133334s15.36-34.133333 34.133334-34.133333h238.933333c18.773333 0 34.133333 15.36 34.133333 34.133333s-14.933333 34.133333-34.133333 34.133334zM479.146667 588.8h-153.6c-18.773333 0-34.133333-15.36-34.133334-34.133333s15.36-34.133333 34.133334-34.133334h153.6c18.773333 0 34.133333 15.36 34.133333 34.133334s-14.933333 34.133333-34.133333 34.133333zM724.48 544c-94.293333 0-170.666667 76.373333-170.666667 170.666667s76.373333 170.666667 170.666667 170.666666 170.666667-76.373333 170.666667-170.666666-76.373333-170.666667-170.666667-170.666667z m0 273.066667c-56.32 0-102.4-46.08-102.4-102.4s46.08-102.4 102.4-102.4 102.4 46.08 102.4 102.4-45.653333 102.4-102.4 102.4z" fill="#97B8D4" p-id="4589"/>
					<path d="M938.666667 928.853333c-13.226667 13.226667-34.986667 13.226667-48.213334 0l-72.533333-72.533333c-13.226667-13.226667-13.226667-34.986667 0-48.213333 13.226667-13.226667 34.986667-13.226667 48.213333 0l72.533334 72.533333c13.226667 13.226667 13.226667 34.56 0 48.213333z" fill="#97B8D4" p-id="4590"/>
				</svg>
			预览</a>
			<a href="javascript:labeledit(\''.$params['parentid'].'\',\''.$v['id'].'\');" '.$style1.'>
				<svg t="1587523006639" viewBox="0 0 1024 1024" version="1.1" p-id="4847" width="14"
					 height="14">
					<path d="M234.057143 753.371429c14.628571 0 29.257143-7.314286 43.885714-14.628571l7.314286 0-146.285714-146.285714 0 7.314286C131.657143 614.4 124.342857 629.028571 124.342857 643.657143L73.142857 804.571429 234.057143 753.371429 234.057143 753.371429 234.057143 753.371429 234.057143 753.371429zM746.057143 168.228571 702.171429 117.028571c-29.257143-29.257143-65.828571-29.257143-95.085714 0L563.2 168.228571l138.971429 138.971429 43.885714-43.885714C775.314286 234.057143 775.314286 190.171429 746.057143 168.228571L746.057143 168.228571 746.057143 168.228571 746.057143 168.228571zM658.285714 351.085714 519.314286 212.114286 190.171429 541.257143l138.971429 138.971429L658.285714 351.085714 658.285714 351.085714 658.285714 351.085714 658.285714 351.085714z"
						  p-id="4848" fill="#97B8D4"/>
					<path d="M0 877.714286l1024 0 0 73.142857L0 950.857143 0 877.714286 0 877.714286z"
						  p-id="4849" fill="#97B8D4"/>
				</svg>
			修改</a>
			<a href="javascript:labeldel(\''.$v['id'].'\');" '.$style1.'>
				<svg t="1587523135432" viewBox="0 0 1024 1024" version="1.1" p-id="5664" width="14"
					 height="14">
					<defs>
						<style type="text/css"/>
					</defs>
					<path d="M742.4 944H281.6c-49.4 0-89.6-43.1-89.6-96V368h64v480c0 17.3 11.7 32 25.6 32h460.8c13.9 0 25.6-14.7 25.6-32V368h64v480c0 52.9-40.2 96-89.6 96z"
						  p-id="5665" fill="#97B8D4"/>
					<path d="M384 368h64v416h-64zM592 368h64v416h-64zM64 224h896v64H64z" p-id="5666"
						  fill="#97B8D4"/>
					<path d="M768 288H256V160c0-52.9 43.1-96 96-96h320c52.9 0 96 43.1 96 96v128z m-448-64h384v-64c0-17.6-14.4-32-32-32H352c-17.6 0-32 14.4-32 32v64z"
						  p-id="5667" fill="#97B8D4"/>
				</svg>
			删除</a>
		</td>';
			$tbstr .= '</tr>';
		}
		// $tbstr = iconv("GB2312","UTF-8",$tbstr);
		echo $tbstr;

    }

	public function label_edit($params)
    {
		$this->set_userinfo($params);
		$id = $params['id'];
		$uid = $_SESSION['Uid'];
		$mc_type = $_SESSION['mc_type'];
		$sql = "select id, MyTempName from DataScreenTempList where userid='".$uid."' and mc_type='".$mc_type."' and isdel=0 order by sort asc";
		$TempListinfo = $this->t1dao->query($sql);
		$TempListarr = array();
		foreach ($TempListinfo as $v) {
			$TempListarr[$v['id']] = $v['MyTempName'];
		}
		$this->assign("TempListarr", $TempListarr);
        $DataScreenTemplate = $this->t1dao->getDataScreenTemplateById($id);
		$DataScreenTemplate['fontcontent'] = preg_replace("/<br\s*\/?>/i","\r\n",$DataScreenTemplate['fontcontent']);;
        $this->assign("DataScreenTemplate", $DataScreenTemplate);
        $this->assign("params", $params);
		$typearr = array("1","4","5","6","8");
		$fontsizearr = array("30","32","34","36","38","40");
		$this->assign("typearr", $typearr);
		$this->assign("fontsizearr", $fontsizearr);
		$this->assign("GUID", $_SESSION['GUID']);
		$this->assign("mc_type", $_SESSION['mc_type']);
		$this->assign("mode", $_SESSION['mode']);
		$this->assign("labeltimejson", $this->JSON($GLOBALS['LABELTIME']));
    }

	public function label_doedit($params)
    {
		$this->set_userinfo($params);
		// $params = $this->array_iconv('utf-8','gbk',$params);
		$mid = $_SESSION['Mid'];
		$userid = $_SESSION['Uid'];
		$truename = $_SESSION['TrueName'];
		$params['fontcontent'] = html_entity_decode($params['fontcontent']);
		$array = array("parentid","sort","type","TopName","LabelName","D1","D2","D3","D4","D5","D6","D7","D8","pic_key1","pic_key2","pic_key3","pic_key4","pic_key5","pic_key6","pic_key7","pic_key8","pc_title1","pc_title2","pc_title3","pc_title4","pc_title5","pc_title6","pc_title7","pc_title8","labeltime1","labeltime2","labeltime3","labeltime4","labeltime5","labeltime6","labeltime7","labeltime8","fontcontent","fontsize","fontcolor");
		// echo json_encode(html_entity_decode($params['fontcontent']));exit;
		$data = $this->getdata( $array, $params );
		if (!empty($data)) {
			if (empty($params['id'])) {
				$sql = "insert into DataScreenTemplate SET $data,mid='".$mid."',userid='".$userid."',truename='".$truename."',createtime=now()";
				$this->t1dao->execute($sql);
			} else {
				$sql = "update DataScreenTemplate set $data,updateadminid='".$userid."',updateadminname='".$truename."',updatetime=now() where id='".$params['id']."'";
				$this->t1dao->execute($sql);
			}
		}
		echo json_encode(array('success' => 1));exit;
    }

	
	public function getlabelnum($params)
    {
		$sql ="SELECT count(*) AS nums FROM DataScreenTemplate where parentid = '".$params['parentid']."' and isdel=0";
		$nums = $this->t1dao->getOne($sql);
		echo $nums;exit;
	}

	public function label_del($params)
    {
		$this->set_userinfo($params);
		$userid = $_SESSION['Uid'];
		$truename = $_SESSION['TrueName'];
		$sql = "update DataScreenTemplate set isdel='1',deladminid='".$userid."',deltime=now() where id='".$params['id']."'";
		$this->t1dao->execute($sql);
		echo '1';
	}
	public function fp_edit($params)
    {
		$this->set_userinfo($params);
		$pic_id = json_encode(array($params['pic_key']));
		$pic_data = $this->check_pic_tree();
		if (empty($pic_data)) {
			echo "暂无定制数据";exit;
		}
		// echo "<pre>";print_r($pic_data);
		$params['labeltime']=$params['labeltime']?$params['labeltime']:4;
		$pic_data = $this->JSON($pic_data);
		$this->assign("pic_data", $pic_data);
		$this->assign("pic_id", $pic_id);
		$this->assign("labeltimearr", $GLOBALS['LABELTIME']);
		$this->assign("labeltime", $params['labeltime']);
		$this->assign("params", $params);
		$this->assign("mode", $_SESSION['mode']);
	}
	public function look_pic($params)
    {
		$this->set_userinfo($params);
        $GUID = $_SESSION['GUID'];
        $SignCS = "63cf7d3ea46b8d3ade7d6ceece903dd4";

        $edate = date("Y-m-d");
        $sdate = date("Y-m-d", strtotime("- 1 year", strtotime($edate)));

		$url = $params['url'];
		$url = base64_decode($url);
		//$imageurl = DC_URL."/v1.5/" . $url . "&GUID=" . $GUID . "&SignCS=" . $SignCS. "&DateStart=" . $sdate . "&DateEnd=" . $edate. "&theme=2";
		$imageurl = DC_URL."/v1.5/" . $url . "&GUID=" . $GUID . "&SignCS=" . $SignCS. "&DateStart=" . $sdate . "&DateEnd=" . $edate. "&theme=2";
		// echo $imageurl;exit;
		$imgcontent = file_get_contents($imageurl);
		//$imgcontent = iconv("utf-8", "gbk", $imgcontent);
		$this->assign("imageurl", $imageurl);
		$this->assign("imgcontent", $imgcontent);
    }
	public function fp_doedit($params)
    {
		$this->set_userinfo($params);
		$mid = $_SESSION['Mid'];
		$userid = $_SESSION['Uid'];
		$truename = $_SESSION['TrueName'];
		$id = $params['id'];
		$type = $params['type'];
		$jsonList = html_entity_decode($params['json']);
		// $jsonList = iconv('gbk','utf-8',$jsonList);
		$lists = json_decode($jsonList, true);
		$picCount = count($lists);
		if ($picCount == 1) {
			foreach ($lists as $index => $item) {
				$json = $item['json'];
				$json = json_decode(base64_decode($json), true);
				echo json_encode($json['ImageType']);exit;
	
				$isbg = $json['isbg'] == "" ? 0 : $json['isbg'];
				$isjz = $json['isjz'] == "" ? 0 : $json['isjz'];
				$color = $json['color'] == "" ? 0 : $json['color'];
				$isfg = $json['isfg'] == "" ? 0 : $json['isfg'];
	
	
				$fields = array(
					"type"=>$type,
					// ""=>,
					// ""=>,
					// ""=>,
					// ""=>,
					// ""=>,
					// ""=>,
				);
				// 	'mokuaiid' => $request->mokuaiId,
				// 	'kanbanreportid' => $request->kanbanreportid,
				// 	'pic_key' => $item['key'],
				// 	'ntype' => $request->ntype,
				// 	'dtname' => $json['dtname'],
				// 	'dtnameshort' => $json['dtnameshort'],
				// 	'imageurl' => $item['url'],
				// 	'ImageType' => $json['ImageType'],
				// 	'DTIDJson' => base64_encode(json_encode($json['DTIDJson'])),
				// 	'aod' => $json['aod'],
				// 	'isbg' => $isbg,
				// 	'isjz' => $isjz,
				// 	'color' => $color,
				// 	'isfg' => $isfg,
				// 	'creattime' => NOW(),
				// 	'creatuser' => $userid,
				// 	'updateadminid' => $userid,
				// 	'updateadminname' => $true_name,
				// 	'updatetime' => NOW(),
				// 	'isdel' => 0,
				// 	'deladminid' => null,
				// 	'deltime' => null,
				// 	'mc_type' => $mc_type
			}
			
			echo json_encode($picCount);exit;
			echo json_encode(array('success' => 1), true);
		}
		// echo 1;
		// // return json_encode(array('success' => 1), true);
	}

	function JSON($array)
    {
        $this->arrayRecursive($array, 'urlencode', true);
        $json = json_encode($array);
        return urldecode($json);
    }

	function arrayRecursive(&$array, $function, $apply_to_keys_also = false)
    {
        static $recursive_counter = 0;
        if (++$recursive_counter > 1000) {
            die('possible deep recursion attack');
        }
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $this->arrayRecursive($array[$key], $function, $apply_to_keys_also);
            } else {
                $array[$key] = $function($value);
            }
            if ($apply_to_keys_also && is_string($key)) {
                $new_key = $function($key);
                if ($new_key != $key) {
                    $array[$new_key] = $array[$key];
                    unset($array[$key]);
                }
            }
        }
        $recursive_counter--;
    }

	function check_pic_tree()
    {

        //图片树形菜单
        $GUID = $_SESSION['GUID'];
        $mc_type = $_SESSION['mc_type'];
        $SignCS = "63cf7d3ea46b8d3ade7d6ceece903dd4";
        $url = "dcsystem.php?action=SearchListGet2&SignCS=" . $SignCS . "&GUID=" . $GUID . "&mc_type=".$mc_type."&isEnglish=0&needCompress=0&isSubs=1&parentid=0&querykey=";
        $pic_data = file_get_contents(DC_URL."/v1.5/" . $url);
        $pic_data = json_decode($pic_data, true);

        $pic_data = $pic_data['Results'];

        $data = array();
        $this->serialize_pic($data, $pic_data, "1");
		// echo "<pre>";print_r($data);
        return $data;
    }

	function serialize_pic(&$data, $SubResults, $id)
    {

		$mc_type = $_SESSION['mc_type'];
		$GUID = $_SESSION['GUID'];
        /*二级菜单*/
        if (!empty($SubResults)) {

            foreach ($SubResults as $k_sub => $v_sub) {

                $leaves = $v_sub['leaves'];
                $SubResults2 = $v_sub['SubResults'];

                $arr = array();
                $arr['id'] = $id . $v_sub['ID'];
                $arr['label'] = base64_decode($v_sub['cataloguename']);
                $arr['label'] = iconv("GBK", "UTF-8", $arr['label']);
                $arr['radioDisabled'] = true;
                $arr['isOpen'] = false;
                $arr['disabled'] = true;

                $children2 = array();
                if (!empty($SubResults2)) {
                    $this->serialize_pic($children2, $SubResults2, $arr['id']);
                } else {
                    //continue;
                }

                if (!empty($leaves)) {

                    foreach ($leaves as $k_leave => $v_leave) {
                        $arr2 = array();
                        $arr2['id'] = $arr['id'] . $v_leave['ID'];
                        // $arr2['key'] = $arr['id'] . $v_leave['ID'];
                        $arr2['id'] =  $v_leave['ID'];
                        $arr2['key'] = $v_leave['ID'];
                        $arr2['label'] = base64_decode($v_leave['dtname']);
                        $arr2['label'] = iconv("GBK", "UTF-8", $arr2['label']);

                        $arr2['json'] = base64_encode(json_encode($v_leave));

                        $url = "dcsearch.php?action=CommonSearch&mc_type=".$mc_type."&GUID=".$GUID."&mode=1&isEnglish=0&ChartsType=2&needCompress=0&lineNum=4&isjgraph=1";
                        $url2 = "dcsearch.php?action=CommonSearch&mc_type=".$mc_type."&GUID=".$GUID."&mode=1&isEnglish=0&ChartsType=1&needCompress=0";
                        foreach ($v_leave['DTIDJson'] as $item) {
                            if (isset($item['Type'])) {
                                $jsonArr = array('Type' => $item['Type'], 'DateStr' => $item['DateStr']);
                                $ChartExt = json_encode($jsonArr);
                                $url .= "&ChartExt=" . $ChartExt;
                                $url2 .= "&ChartExt=" . $ChartExt;
                                break;
                            }
                        }
                        //$url .="&DateStart=2019-12-03&DateEnd=2020-12-03";
                        $url .= "&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=" . $v_leave['ImageType'] . "&ImageTitle=" . $v_leave['dtname'];
                        $url .= "&DTIDJson=" . json_encode($v_leave['DTIDJson']);
                        $url .= "&isbg=" . $v_leave['isbg'] . "&isjz=" . $v_leave['isjz'] . "&isfg=" . $v_leave['isfg'] . "&color=" . $v_leave['color'] . "";
                        //$url .="&isbg=0&isjz=0&isfg=0&color=0";

						$url2 = 'dcsearch.php?action=CommonSystemSearch&dtype=2&ImageTitle=&IsImages=2&TablePages=1&TableRecords=10&SDID='.$v_leave['ID'].'&mc_type='.$mc_type.'&GUID='.$GUID.'&isEnglish=0&mode=1&ChartsType=1&ChartExt='.$ChartExt.'&iscustomerdb=';

                        $arr2['url'] = base64_encode($url);
                        $arr2['url2'] = base64_encode($url2);
                        $arr2['isOpen'] = true;

                        $children2[] = $arr2;
                    }
                }
                $arr['children'] = $children2;

                $data[] = $arr;
            }
        }

    }

	public function getdata( $array, $params ){
		$data = array();
		foreach( $array as $a ){
			$data[] = $a . "='" . $params[$a] . "'";
		}
		$data = implode( ",", $data );
		return $data;
	}

	function array_iconv ($in_charset,$out_charset,$arr) {
		foreach($arr as $k => &$v){
			if(is_array($v)){
				foreach($v as $kk => &$vv){
					$vv = iconv($in_charset,$out_charset,$vv);
				}
			}else{
				$v = iconv($in_charset,$out_charset,$v);
			}
		}
		return $arr;
	}

	function PagebarDiv($url, $param, $limit, $page, $total, $type = 0)
    {
        if ($total < 0) {
            return false;
        }
        if ($url == "") {
            return false;
        }
        $link = $url . "?";
        if (is_array($param)) {
            foreach ($param as $str_key => $str_value) {
                $link = $link . "$str_key=" . urlencode($str_value) . "&";
            }
        }
        $int_pages = ceil($total / $limit);
        if ($page < 1) {
            $page = 1;
        }
        if ($page > $int_pages) {
            $page = $int_pages;
        }

        $start_url = $link . "page=1";
        $end_url = $link . "page=$int_pages";
        $pre_url = $link . "page=" . ($page - 1);
        $next_url = $link . "page=" . ($page + 1);
        if ($page < 6) {
            $start_page = 1;
            $end_page = 7;
        } else {
            $start_page = $page - 5;
            $end_page = $page + 1;
        }
        if ($end_page > $int_pages) {
            $end_page = $int_pages;
        }
        $urls = null;
        /**  THE URL */
        for ($i = $start_page, $j = 0; $i <= $end_page; $i++, $j++) {
            $temp_url = $link . "page=$i";
            if ($i == $page) {
                $urls[$j] = "<strong>" . $i . "</strong>&nbsp;";
            } else {
                $urls[$j] = "<a href=\"$temp_url\">" . $i . "</a>&nbsp;";
            }
        }
        if (is_array($urls)) {
            $str_html = "<span>共&nbsp;" . $total . "&nbsp;条信息&nbsp;&nbsp;共&nbsp;" . $int_pages . "&nbsp;页&nbsp;&nbsp;</span><a href=\"$start_url\" text=\回第1页\ style='background: #2076B7;color: #ffffff;'>首页</a>&nbsp;&nbsp;";
            if ($page > 1) {
                $str_html = $str_html . "<a href=\"$pre_url\">上页</a>&nbsp;&nbsp;";
            } else {
                $str_html = $str_html . "<a href=''>上页</a>&nbsp;&nbsp";
            }
            if ($type == 0) {
                foreach ($urls as $sub_url) {
                    $str_html = $str_html . $sub_url;
                }
            }
            $str_html = $str_html . "&nbsp;";
            if ($page >= $int_pages) {
                $str_html = $str_html . "<a href=''>下页</a>&nbsp;&nbsp";
            } else {
                $str_html = $str_html . "<a href=\"$next_url\">下页</a>&nbsp;&nbsp";
            }
            if ($type == 0) {
                $str_html = $str_html . "<a href=\"$end_url\" text=\"到第$int_pages\" style='background: #2076B7;color: #ffffff;'>尾页</a> &nbsp;&nbsp;跳到 <input type=text size=4 onchange=\"value=value.replace(/[^\d]/g,'')\" onBlur=window.location.href='$pre_url&page='+this.value  /> 页 ";
                return $str_html;
            }
            if ($type == 1) {
                return $str_html . "<a href=\"$end_url\" text=\"到第$int_pages\" style='background: #2076B7;color: #ffffff;'>尾页</a>";
            }
        }
        return false;
    }



	public function getScreenData($params)
	{
		$edate = $params['date'];
		$pz = $params['pz']?$params['pz']:1;
		if(empty($edate))
		{
		  $edate=date('Y-m-d');

		  //$sql=" select max(dateday) from shpi_pp where bc_id='$pz'";
		  $sql=" select max(dateday) from shpi_pi ";
		  $maxdate=$this->_dao->getOne($sql);
		  $edate=$maxdate;
		}
        $sdate=date('Y-m-d',strtotime("-1 year",strtotime($edate)));	
		//钢材
        $sql = "select round(weiprice) weiprice,dateday from shpi_pi where dateday>='" . $sdate . "' and dateday<='" . $edate . "' order by dateday asc";
		//echo $sql;exit;
		$shpi_pi=$this->_dao->query($sql);
		
		foreach($shpi_pi as $k=>$v)
		{
			$data1[]=array('ydata'=>$v['weiprice'],'xdata'=>date('Y-m-d',strtotime($v['dateday'])));
			$max_min[]=$v['weiprice'];
		}
		
		//长材扁平材指数
		$sql=" select bc_id,dateday,round(weiprice) weiprice from shpi_pp where dateday<='$edate' and dateday>='$sdate' and bc_id in (1,2) order by dateday asc ";
		$shpi_pp=$this->_dao->query($sql);
		foreach($shpi_pp as $k=>$v)
		{
		      if($v['bc_id']==1)
			 {
				 $data2[]=array('ydata'=>$v['weiprice'],'xdata'=>date('Y-m-d',strtotime($v['dateday'])));
			 }
			 else
			 {
				 $data3[]=array('ydata'=>$v['weiprice'],'xdata'=>date('Y-m-d',strtotime($v['dateday'])));
			 }
			 
			$max_min[]=$v['weiprice'];
		}
		if($max_min)
		{
			$min=min($max_min);
			$max=max($max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			
			$infomax=$getmax_min_jg['max'];
			$infomin=$getmax_min_jg['min'];
			$infointerval=$getmax_min_jg['interval'];
		}
		else
		{
			$infomax='';
			$infomin='';
			$infointerval='';
		}
		$info['dataName']='钢材(SHSPI)指数(价格)';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$data1;
		$response['data']['SHPIData1']['data'][] =$info;
		
		$info['dataName']='长材(SHSPI-L)指数(价格)';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$data2;
		$response['data']['SHPIData1']['data'][] =$info;
		
		$info['dataName']='扁平材(SHSPI-F)指数(价格)';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$data3;
		$response['data']['SHPIData1']['data'][] =$info;

        $response['data']['SHPIData1']['max']=$infomax;
		$response['data']['SHPIData1']['min']=$infomin;
		$response['data']['SHPIData1']['interval']=$infointerval;


//--------------------------中国优特钢、不锈钢价格指数(SSPI)走势图start--------------------------------
		
		 $data1=array();
		 $data2=array();
		 $data3=array();
		 $max_min=array();
		 //$rightmax_min=array();
		// $sql=" select bc_id,dateday,wpindex,weiprice from shpi_pp where dateday<='$edate' and dateday>='$sdate' and bc_id in (3,4) order by dateday asc ";
		// $shpi_pp=$this->_dao->query($sql);
		
		$sql=" select typeid,dateday,mindex,price  from shpi_allarea_index where dateday <='$edate' and dateday>='$sdate' and typeid in (27,34,39) and isdel=0 and indextype='4'  order by dateday asc ";
		$shpi_allarea_index=$this->_dao->query($sql);
		
		foreach($shpi_allarea_index as $k=>$v)
		{
		      if($v['typeid']==39)
			 {
				 $data1[]=array('ydata'=>$v['price'],'xdata'=>date('Y-m-d',strtotime($v['dateday'])));
				 
			 }
			 else if($v['typeid']==27)
			 {
				 $data2[]=array('ydata'=>$v['price'],'xdata'=>date('Y-m-d',strtotime($v['dateday'])));
				 
			 }
			 else
			 {
				 $data3[]=array('ydata'=>$v['price'],'xdata'=>date('Y-m-d',strtotime($v['dateday'])));
				 //$rightmax_min[]=$v['weiprice'];
			 }
			 $max_min[]=$v['price'];
			
		}
		if($max_min)
		{
			$min=min($max_min);
			$max=max($max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			
			$infomax=$getmax_min_jg['max'];
			$infomin=$getmax_min_jg['min'];
			$infointerval=$getmax_min_jg['interval'];
		}
		else
		{
			$infomax='';
			$infomin='';
			$infointerval='';
		}
		
		$info['dataName']='200系';
		
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$data1;
		$response['data']['SHPIData2']['data'][] =$info;
		
		$info['dataName']='300系';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$data2;
		$response['data']['SHPIData2']['data'][] =$info;
		
		$info['dataName']='400系';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$data3;
		$response['data']['SHPIData2']['data'][] =$info;

        $response['data']['SHPIData2']['max']=$infomax;
		$response['data']['SHPIData2']['min']=$infomin;
		$response['data']['SHPIData2']['interval']=$infointerval;
		
		
		
		//--------------------------中国优特钢、不锈钢价格指数(SSPI)走势图end--------------------------------
		

		//--------------------------铁矿石start--------------------------------
		 $data1=array();
		 $data2=array();
		 $data3=array();
		 $leftmax_min=array();
		 $rightmax_min=array();
		$sql=" select vid,dateday,round(weiprice) weiprice,weipriceusb from shpi_material_pzp where dateday<='$edate' and dateday>='$sdate' and vid in (1,3) order by dateday asc ";
		$shpi_material_pzp=$this->_dao->query($sql);
		foreach($shpi_material_pzp as $k=>$v)
		{
		      if($v['vid']==1)
			 {
				 $data1[]=array('ydata'=>$v['weiprice'],'xdata'=>date('Y-m-d',strtotime($v['dateday'])));
				 $leftmax_min[]=$v['weiprice'];
			 }
			 else
			 {
				 $data2[]=array('ydata'=>$v['weipriceusb'],'xdata'=>date('Y-m-d',strtotime($v['dateday'])));
				 $rightmax_min[]=$v['weipriceusb'];
			 }
			 
			
		}
		if($leftmax_min)
		{
			$min=min($leftmax_min);
			$max=max($leftmax_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			
			$leftinfomax=$getmax_min_jg['max'];
			$leftinfomin=$getmax_min_jg['min'];
			$leftinfointerval=$getmax_min_jg['interval'];
		}
		else
		{
			$leftinfomax='';
			$leftinfomin='';
			$leftinfointerval='';
		}
		
		if($rightmax_min)
		{
			$min=min($rightmax_min);
			$max=max($rightmax_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			
			$rightinfomax=$getmax_min_jg['max'];
			$rightinfomin=$getmax_min_jg['min'];
			$rightinfointerval=$getmax_min_jg['interval'];
		}
		else
		{
			$rightinfomax='';
			$rightinfomin='';
			$rightinfointerval='';
		}
		
		$info['dataName']='65%国产铁矿石(SHCNDOI)指数(价格)';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$data1;
		$response['data']['SHPIData3']['data'][] =$info;
		
		$info['dataName']='62%进口铁矿石(SHCNIOI)指数(美元)';
		$info['dataType']='line';
		$info['isLeft']=0;
		$info['dataValue']=$data2;
		$response['data']['SHPIData3']['data'][] =$info;

        $response['data']['SHPIData3']['leftmax']=$leftinfomax;
		$response['data']['SHPIData3']['leftmin']=$leftinfomin;
		$response['data']['SHPIData3']['leftinterval']=$leftinfointerval;
		$response['data']['SHPIData3']['rightmax']=$rightinfomax;
		$response['data']['SHPIData3']['rightmin']=$rightinfomin;
		$response['data']['SHPIData3']['rightinterval']=$rightinfointerval;
		
		
		//--------------------------铁矿石end--------------------------------
		
		//--------------------------废钢钢之家(中国)铁合金价格指数(SHFPI)走势图strat--------------------------------
		
		$data1=array();
		 $data2=array();
		 $data3=array();
		 $max_min=array();
         $sql = "select topicture,dateday,mindex,price from shpi_material where dateday<='$edate' and dateday>='$sdate' and topicture in ('5','7','8') order by dateday asc ";
		//echo $sql;exit;
		$shpi_material=$this->_dao->query($sql);
        foreach($shpi_material as $k=>$v)
		{
			if($v['topicture']==5)
			{
				$data1[]=array('ydata'=>$v['price'],'xdata'=>date('Y-m-d',strtotime($v['dateday'])));
				$max_min[]=$v['price'];
			}
			else if($v['topicture']==7)
			{
				$data2[]=array('ydata'=>$v['price'],'xdata'=>date('Y-m-d',strtotime($v['dateday'])));
				$max_min[]=$v['price'];
			}
			else
			{
				$data3[]=array('ydata'=>$v['price'],'xdata'=>date('Y-m-d',strtotime($v['dateday'])));
				$max_min[]=$v['price'];
			}
		}
		
		if($max_min)
		{
			$min=min($max_min);
			$max=max($max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			
			$infomax=$getmax_min_jg['max'];
			$infomin=$getmax_min_jg['min'];
			$infointerval=$getmax_min_jg['interval'];
		}
		else
		{
			$infomax='';
			$infomin='';
			$infointerval='';
		}
		$info['dataName']='铁合金(SHFPI)指数(价格)';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$data1;
		$response['data']['SHPIData4']['data'][] =$info;
		
		$info['dataName']='硅铁(SHFPI-FESI)指数(价格)';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$data2;
		$response['data']['SHPIData4']['data'][] =$info;
		
		$info['dataName']='硅锰(SHFPI-MNSI)指数(价格)';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$data3;
		$response['data']['SHPIData4']['data'][] =$info;

        $response['data']['SHPIData4']['max']=$infomax;
		$response['data']['SHPIData4']['min']=$infomin;
		$response['data']['SHPIData4']['interval']=$infointerval;
		
		
		//--------------------------钢之家(中国)铁合金价格指数(SHFPI)走势图end--------------------------------
		
		//--------------------------钢之家焦炭与煤炭(SHCKI)基准价格指数走势图start--------------------------------
		
		
		$data1=array();
		 $data2=array();
		 $data3=array();
		 $leftmax_min=array();
		$rightmax_min=array();
         $sql = "select topicture,dateday,mindex,price from shpi_material where dateday<='$edate' and dateday>='$sdate' and topicture ='3' order by dateday asc ";
		//echo $sql;exit;
		$shpi_material=$this->_dao->query($sql);
        foreach($shpi_material as $k=>$v)
		{
			$data1[]=array('ydata'=>$v['price'],'xdata'=>date('Y-m-d',strtotime($v['dateday'])));
			$leftmax_min[]=$v['price'];
		}
		
		$sql = "select dateday,weiprice from shpi_mj_pzp where dateday<='$edate' and dateday>='$sdate' and vid=0 AND type=0 order by dateday asc ";
		//echo $sql;exit;
		$shpi_material=$this->_dao->query($sql);
        foreach($shpi_material as $k=>$v)
		{
			$data2[]=array('ydata'=>$v['weiprice'],'xdata'=>date('Y-m-d',strtotime($v['dateday'])));
			$rightmax_min[]=$v['weiprice'];
		}
		
		
		if($leftmax_min)
		{
			$min=min($leftmax_min);
			$max=max($leftmax_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			
			$leftinfomax=$getmax_min_jg['max'];
			$leftinfomin=$getmax_min_jg['min'];
			$leftinfointerval=$getmax_min_jg['interval'];
		}
		else
		{
			$leftinfomax='';
			$leftinfomin='';
			$leftinfointerval='';
		}
		
		if($rightmax_min)
		{
			$min=min($rightmax_min);
			$max=max($rightmax_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			
			$rightinfomax=$getmax_min_jg['max'];
			$rightinfomin=$getmax_min_jg['min'];
			$rightinfointerval=$getmax_min_jg['interval'];
		}
		else
		{
			$rightinfomax='';
			$rightinfomin='';
			$rightinfointerval='';
		}
		
		$info['dataName']='焦炭(SHCKI)指数(价格)';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$data1;
		$response['data']['SHPIData5']['data'][] =$info;
		
		$info['dataName']='煤炭(SHCNCI)指数(价格)';
		$info['dataType']='line';
		$info['isLeft']=0;
		$info['dataValue']=$data2;
		$response['data']['SHPIData5']['data'][] =$info;

        $response['data']['SHPIData5']['leftmax']=$leftinfomax;
		$response['data']['SHPIData5']['leftmin']=$leftinfomin;
		$response['data']['SHPIData5']['leftinterval']=$leftinfointerval;
		$response['data']['SHPIData5']['rightmax']=$rightinfomax;
		$response['data']['SHPIData5']['rightmin']=$rightinfomin;
		$response['data']['SHPIData5']['rightinterval']=$rightinfointerval;
		
		
		//--------------------------钢之家焦炭与煤炭(SHCKI)基准价格指数走势图end--------------------------------
		
		
		
		//--------------------------钢之家有色金属价格指数(SHNMPI)走势图start--------------------------------
		
		$data1=array();
		$data2=array();
		$data3=array();
		$data4=array();
		$leftmax_min=array();
		$rightmax_min=array();
		
		$sql = "select topicture,dateday,price from shpi_material where dateday<='$edate' and dateday>='$sdate' and topicture in(26,15,19,16) order by dateday asc ";
		//echo $sql;exit;
		$shpi_material=$this->_dao->query($sql);
        foreach($shpi_material as $k=>$v)
		{
			if($v['topicture']==26)
			{
				$data1[]=array('ydata'=>$v['price'],'xdata'=>date('Y-m-d',strtotime($v['dateday'])));
				$leftmax_min[]=$v['price'];
			}
			else if($v['topicture']==15)
			{
				$data2[]=array('ydata'=>$v['price'],'xdata'=>date('Y-m-d',strtotime($v['dateday'])));
				$rightmax_min[]=$v['price'];
			}
			else if($v['topicture']==19)
			{
				$data3[]=array('ydata'=>$v['price'],'xdata'=>date('Y-m-d',strtotime($v['dateday'])));
				$rightmax_min[]=$v['price'];
			}
			else
			{
				$data4[]=array('ydata'=>$v['price'],'xdata'=>date('Y-m-d',strtotime($v['dateday'])));
				$leftmax_min[]=$v['price'];
			}
			
		}
		if($leftmax_min)
		{
			$min=min($leftmax_min);
			$max=max($leftmax_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			
			$leftinfomax=$getmax_min_jg['max'];
			$leftinfomin=$getmax_min_jg['min'];
			$leftinfointerval=$getmax_min_jg['interval'];
		}
		else
		{
			$leftinfomax='';
			$leftinfomin='';
			$leftinfointerval='';
		}
		
		if($rightmax_min)
		{
			$min=min($rightmax_min);
			$max=max($rightmax_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			
			$rightinfomax=$getmax_min_jg['max'];
			$rightinfomin=$getmax_min_jg['min'];
			$rightinfointerval=$getmax_min_jg['interval'];
		}
		else
		{
			$rightinfomax='';
			$rightinfomin='';
			$rightinfointerval='';
		}
		
		$info['dataName']='有色金属(SHNMPI)指数(价格)';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$data1;
		$response['data']['SHPIData6']['data'][] =$info;
		
		$info['dataName']='电解铜(SHNMPI-CU)指数(价格)';
		$info['dataType']='line';
		$info['isLeft']=0;
		$info['dataValue']=$data2;
		$response['data']['SHPIData6']['data'][] =$info;
		
		
		$info['dataName']='电解镍(SHNMPI-NI)指数(价格)';
		$info['dataType']='line';
		$info['isLeft']=0;
		$info['dataValue']=$data3;
		$response['data']['SHPIData6']['data'][] =$info;
		
		$info['dataName']='铝锭(SHNMPI-AL)指数(价格)';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$data4;
		$response['data']['SHPIData6']['data'][] =$info;

        $response['data']['SHPIData6']['leftmax']=$leftinfomax;
		$response['data']['SHPIData6']['leftmin']=$leftinfomin;
		$response['data']['SHPIData6']['leftinterval']=$leftinfointerval;
		$response['data']['SHPIData6']['rightmax']=$rightinfomax;
		$response['data']['SHPIData6']['rightmin']=$rightinfomin;
		$response['data']['SHPIData6']['rightinterval']=$rightinfointerval;
		
		//--------------------------钢之家有色金属价格指数(SHNMPI)走势图end--------------------------------
		
		
		
		//--------------------------钢之家钢坯价格指数与成本指数对比start--------------------------------
		
		 /* $data1=array();
		 $data2=array();
		 $data3=array();
		 $max_min=array();
		$sql=" select bc_id,dateday,wpindex,weiprice from shpi_pp where dateday<='$edate' and dateday>='$sdate' and bc_id in (13,23) order by dateday asc ";
		$shpi_pp=$this->_dao->query($sql);
		foreach($shpi_pp as $k=>$v)
		{
		      if($v['bc_id']==13)
			 {
				 $data1[]=array('ydata'=>$v['wpindex'],'xdata'=>date('Y-m-d',strtotime($v['dateday'])));
				 $max_min[]=$v['wpindex'];
			 }
			 else
			 {
				 $data2[]=array('ydata'=>$v['wpindex'],'xdata'=>date('Y-m-d',strtotime($v['dateday'])));
				 $max_min[]=$v['wpindex'];
			 }
			 
			
		}
		if($max_min)
		{
			$min=min($max_min);
			$max=max($max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			
			$infomax=$getmax_min_jg['max'];
			$infomin=$getmax_min_jg['min'];
			$infointerval=$getmax_min_jg['interval'];
		}
		else
		{
			$infomax='';
			$infomin='';
			$infointerval='';
		}
		
		$info['dataName']='普碳方坯(SHCNSPI-Billet)指数(点)';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$data1;
		$response['data']['SHPIData6']['data'][] =$info;
		
		$info['dataName']='唐山普碳方坯成本指数(SHCNCOST-Billet)指数(点)';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$data2;
		$response['data']['SHPIData6']['data'][] =$info;

        $response['data']['SHPIData6']['max']=$infomax;
		$response['data']['SHPIData6']['min']=$infomin;
		$response['data']['SHPIData6']['interval']=$infointerval;
		 */
		//--------------------------钢之家钢坯价格指数与成本指数对比end--------------------------------
		
		
		
		
		
		
		//--------------------------钢之家废钢价格指数(SHFSPI)走势图start--------------------------------
		
		$data1=array();
		 $data2=array();
		 $data3=array();
		 $max_min=array();
         $sql = "select topicture,dateday,price from shpi_material where dateday<='$edate' and dateday>='$sdate' and topicture in ('4','10','11') order by dateday asc ";
		//echo $sql;exit;
		$shpi_material=$this->_dao->query($sql);
        foreach($shpi_material as $k=>$v)
		{
			if($v['topicture']==4)
			{
				$data1[]=array('ydata'=>$v['price'],'xdata'=>date('Y-m-d',strtotime($v['dateday'])));
				
			}
			else if($v['topicture']==10)
			{
				$data2[]=array('ydata'=>$v['price'],'xdata'=>date('Y-m-d',strtotime($v['dateday'])));
				
			}
			else 
			{
				$data3[]=array('ydata'=>$v['price'],'xdata'=>date('Y-m-d',strtotime($v['dateday'])));
				
			}
			$max_min[]=$v['price'];
		}
		
		if($max_min)
		{
			$min=min($max_min);
			$max=max($max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			
			$infomax=$getmax_min_jg['max'];
			$infomin=$getmax_min_jg['min'];
			$infointerval=$getmax_min_jg['interval'];
		}
		else
		{
			$infomax='';
			$infomin='';
			$infointerval='';
		}
		$info['dataName']='全国重废 指数(价格)';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$data1;
		$response['data']['SHPIData7']['data'][] =$info;
		
		$info['dataName']='华东重废 指数(价格)';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$data2;
		$response['data']['SHPIData7']['data'][] =$info;
		
		$info['dataName']='华北重废 指数(价格)';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$data3;
		$response['data']['SHPIData7']['data'][] =$info;

        $response['data']['SHPIData7']['max']=$infomax;
		$response['data']['SHPIData7']['min']=$infomin;
		$response['data']['SHPIData7']['interval']=$infointerval;
		
		//--------------------------钢之家废钢价格指数(SHFSPI)走势图end--------------------------------
		
		
		
		
		
		
        //--------------------------全球指数start--------------------------------
         $data1=array();
		 $data2=array();
		 $data3=array();
		 $max_min=array();
         // $sql = "select dta_1,dta_2,dta_ym from data_table where dta_type='GJGCJG_13' and  dta_1 in ('北美','欧洲','亚洲') and  dta_ym>='" . $sdate . "' and dta_ym<='" . $edate . "' order by dta_ym asc";
		// $data_table=$this->drcdao->query($sql);
		
		$sql = "select mvalue,leibie,datetime from gjshpi where   leibie in ('美洲','欧洲','亚洲') and  datetime>='" . $sdate . "' and datetime<='" . $edate . "' order by datetime asc";
		//echo $sql;exit;
		$data_table=$this->_dao->query($sql);
		
        foreach($data_table as $k=>$v)
		{
		      if($v['leibie']=='美洲')
			 {
				 $data1[]=array('ydata'=>$v['mvalue'],'xdata'=>date('Y-m-d',strtotime($v['datetime'])));
			 }
			 else if($v['leibie']=='欧洲')
			 {
				 $data2[]=array('ydata'=>$v['mvalue'],'xdata'=>date('Y-m-d',strtotime($v['datetime'])));
			 }
			 else
			 {
				 $data3[]=array('ydata'=>$v['mvalue'],'xdata'=>date('Y-m-d',strtotime($v['datetime'])));
			 }
			 
			$max_min[]=$v['mvalue'];
		}
		if($max_min)
		{
			$min=min($max_min);
			$max=max($max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			
			$infomax=$getmax_min_jg['max'];
			$infomin=$getmax_min_jg['min'];
			$infointerval=$getmax_min_jg['interval'];
		}
		else
		{
			$infomax='';
			$infomin='';
			$infointerval='';
		}
		$info['dataName']='美洲钢材(SHGSPI-America)指数(点)';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$data1;
		$response['data']['SHPIData8']['data'][] =$info;
		
		$info['dataName']='欧洲钢材(SHGSPI-Europe)指数(点)';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$data2;
		$response['data']['SHPIData8']['data'][] =$info;
		
		$info['dataName']='亚洲钢材(SHGSPI-Asia)指数(点)';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$data3;
		$response['data']['SHPIData8']['data'][] =$info;

        $response['data']['SHPIData8']['max']=$infomax;
		$response['data']['SHPIData8']['min']=$infomin;
		$response['data']['SHPIData8']['interval']=$infointerval;
	
		 //--------------------------全球指数end--------------------------------
		
		
		
		
	  
		$response['Success'] = 1;
		$response['Message']='获取成功';
		
		$response['timestamp'] =time();
		$response['data']['date'] =$edate;
		$response['data']['datemonth'] =date('n',strtotime($edate));
		$response['data']['dateday'] =date('j',strtotime($edate));

		ob_clean();
		echo $this->pri_JSON($response);
        //echo  json_encode($response);
	}
	
	


	public function getScreenData2($params){
		$edate = $params['date'];
		$topictures=array('072023','402023','282023','073112','403112','283112','074212','404212','284212','073012','403012','283012');
		if(empty($edate))
		{
		  $edate=date('Y-m-d');
		  $sql2 = "select mastertopid,`price`,mconmanagedate from marketconditions where    mconmanagedate<= '".$edate." 23:59:59' and topicture in  ('".implode("','",$topictures)."')  order by mconmanagedate desc limit 1 ";
		  $marketconditions2=$this->_dao->getRow($sql2);
		  $edate=date('Y-m-d',strtotime($marketconditions2['mconmanagedate']));
		}
        $sdate=date('Y-m-d',strtotime("-1 year",strtotime($edate)));

		/*
		螺纹钢  上海(072023) 天津(402023) 广州(282023)
		热轧板卷上海(073112) 天津(403112) 广州(283112)
		冷轧板卷上海(074212) 天津(404212) 广州(284212)
		中厚板  上海(073012) 天津(403012) 广州(283012)
		*/
		
		
		$sql = "select topicture,`price`,mconmanagedate from marketconditions where   mconmanagedate>= '".$sdate." 00:00:00' and mconmanagedate<= '".$edate." 23:59:59' and topicture in  ('".implode("','",$topictures)."')  order by mconmanagedate asc ";
		//echo $sql;exit;
		$marketconditions=$this->_dao->query($sql);

		$lwg_data1=array();
		$lwg_data2=array();
		$lwg_data3=array();
		$rzbj_data1=array();
		$rzbj_data2=array();
		$rzbj_data3=array();
		$lzbj_data1=array();
		$lzbj_data2=array();
		$lzbj_data3=array();
		$zhb_data1=array();
		$zhb_data2=array();
		$zhb_data3=array();
		$lwg_max_min=array();
		$rzbj_max_min=array();
		$lzbj_max_min=array();
		$zhb_max_min=array();
		$info=array();

		foreach($marketconditions as $mkey=>$mvalue){
			if($mvalue['topicture']=='072023'){//螺纹钢
				$lwg_data1[]=array('ydata'=>$mvalue['price'],'xdata'=>date('Y-m-d',strtotime($mvalue['mconmanagedate'])));
				$lwg_max_min[]=$mvalue['price'];
			}else if($mvalue['topicture']=='402023'){
				$lwg_data2[]=array('ydata'=>$mvalue['price'],'xdata'=>date('Y-m-d',strtotime($mvalue['mconmanagedate'])));
				$lwg_max_min[]=$mvalue['price'];
			}else if($mvalue['topicture']=='282023'){
				$lwg_data3[]=array('ydata'=>$mvalue['price'],'xdata'=>date('Y-m-d',strtotime($mvalue['mconmanagedate'])));
				$lwg_max_min[]=$mvalue['price'];
			}else if($mvalue['topicture']=='073112'){//热轧板卷
				$rzbj_data1[]=array('ydata'=>$mvalue['price'],'xdata'=>date('Y-m-d',strtotime($mvalue['mconmanagedate'])));
				$rzbj_max_min[]=$mvalue['price'];
			}else if($mvalue['topicture']=='403112'){
				$rzbj_data2[]=array('ydata'=>$mvalue['price'],'xdata'=>date('Y-m-d',strtotime($mvalue['mconmanagedate'])));
				$rzbj_max_min[]=$mvalue['price'];
			}else if($mvalue['topicture']=='283112'){
				$rzbj_data3[]=array('ydata'=>$mvalue['price'],'xdata'=>date('Y-m-d',strtotime($mvalue['mconmanagedate'])));
				$rzbj_max_min[]=$mvalue['price'];
			}else if($mvalue['topicture']=='074212'){//冷轧板卷
				$lzbj_data1[]=array('ydata'=>$mvalue['price'],'xdata'=>date('Y-m-d',strtotime($mvalue['mconmanagedate'])));
				$lzbj_max_min[]=$mvalue['price'];
			}else if($mvalue['topicture']=='404212'){
				$lzbj_data2[]=array('ydata'=>$mvalue['price'],'xdata'=>date('Y-m-d',strtotime($mvalue['mconmanagedate'])));
				$lzbj_max_min[]=$mvalue['price'];
			}else if($mvalue['topicture']=='284212'){
				$lzbj_data3[]=array('ydata'=>$mvalue['price'],'xdata'=>date('Y-m-d',strtotime($mvalue['mconmanagedate'])));
				$lzbj_max_min[]=$mvalue['price'];
			}else if($mvalue['topicture']=='073012'){//中厚板
				$zhb_data1[]=array('ydata'=>$mvalue['price'],'xdata'=>date('Y-m-d',strtotime($mvalue['mconmanagedate'])));
				$zhb_max_min[]=$mvalue['price'];
			}else if($mvalue['topicture']=='403012'){
				$zhb_data2[]=array('ydata'=>$mvalue['price'],'xdata'=>date('Y-m-d',strtotime($mvalue['mconmanagedate'])));
				$zhb_max_min[]=$mvalue['price'];
			}else if($mvalue['topicture']=='283012'){
				$zhb_data3[]=array('ydata'=>$mvalue['price'],'xdata'=>date('Y-m-d',strtotime($mvalue['mconmanagedate'])));
				$zhb_max_min[]=$mvalue['price'];
			}
		}


		if($lwg_max_min){
			$min=min($lwg_max_min);
			$max=max($lwg_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$lwg_infomax=$getmax_min_jg['max'];
			$lwg_infomin=$getmax_min_jg['min'];
			$lwg_infointerval=$getmax_min_jg['interval'];
		}else{
			$lwg_infomax='';
			$lwg_infomin='';
			$lwg_infointerval='';
		}

		$info['dataName']='上海 主流 螺纹钢';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$lwg_data1;
		$response['data']['PriceData1']['data'][] =$info;

		$info['dataName']='天津 主流 螺纹钢';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$lwg_data2;
		$response['data']['PriceData1']['data'][] =$info;

		$info['dataName']='广州 主流 螺纹钢';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$lwg_data3;
		$response['data']['PriceData1']['data'][] =$info;

		$response['data']['PriceData1']['max']=$lwg_infomax;
		$response['data']['PriceData1']['min']=$lwg_infomin;
		$response['data']['PriceData1']['interval']=$lwg_infointerval;


		if($rzbj_max_min){
			$min=min($rzbj_max_min);
			$max=max($rzbj_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$rzbj_infomax=$getmax_min_jg['max'];
			$rzbj_infomin=$getmax_min_jg['min'];
			$rzbj_infointerval=$getmax_min_jg['interval'];
		}else{
			$rzbj_infomax='';
			$rzbj_infomin='';
			$rzbj_infointerval='';
		}

		$info['dataName']='上海 主流 热轧板卷';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$rzbj_data1;
		$response['data']['PriceData2']['data'][] =$info;

		$info['dataName']='天津 主流 热轧板卷';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$rzbj_data2;
		$response['data']['PriceData2']['data'][] =$info;

		$info['dataName']='广州 主流 热轧板卷';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$rzbj_data3;
		$response['data']['PriceData2']['data'][] =$info;

		$response['data']['PriceData2']['max']=$rzbj_infomax;
		$response['data']['PriceData2']['min']=$rzbj_infomin;
		$response['data']['PriceData2']['interval']=$rzbj_infointerval;

		if($lzbj_max_min){
			$min=min($lzbj_max_min);
			$max=max($lzbj_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$lzbj_infomax=$getmax_min_jg['max'];
			$lzbj_infomin=$getmax_min_jg['min'];
			$lzbj_infointerval=$getmax_min_jg['interval'];
		}else{
			$lzbj_infomax='';
			$lzbj_infomin='';
			$lzbj_infointerval='';
		}


		$info['dataName']='上海 主流 冷轧板卷';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$lzbj_data1;
		$response['data']['PriceData3']['data'][] =$info;

		$info['dataName']='天津 主流 冷轧板卷';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$lzbj_data2;
		$response['data']['PriceData3']['data'][] =$info;

		$info['dataName']='广州 主流 冷轧板卷';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$lzbj_data3;
		$response['data']['PriceData3']['data'][] =$info;

		$response['data']['PriceData3']['max']=$lzbj_infomax;
		$response['data']['PriceData3']['min']=$lzbj_infomin;
		$response['data']['PriceData3']['interval']=$lzbj_infointerval;

		if($zhb_max_min){
			$min=min($zhb_max_min);
			$max=max($zhb_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$zhb_infomax=$getmax_min_jg['max'];
			$zhb_infomin=$getmax_min_jg['min'];
			$zhb_infointerval=$getmax_min_jg['interval'];
		}else{
			$zhb_infomax='';
			$zhb_infomin='';
			$zhb_infointerval='';
		}

		$info['dataName']='上海 主流 中厚板';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$zhb_data1;
		$response['data']['PriceData4']['data'][] =$info;

		$info['dataName']='天津 主流 中厚板';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$zhb_data2;
		$response['data']['PriceData4']['data'][] =$info;

		$info['dataName']='广州 主流 中厚板';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$zhb_data3;
		$response['data']['PriceData4']['data'][] =$info;

		$response['data']['PriceData4']['max']=$zhb_infomax;
		$response['data']['PriceData4']['min']=$zhb_infomin;
		$response['data']['PriceData4']['interval']=$zhb_infointerval;

		$response['Success'] = 1;
		$response['Message']='获取成功';
		
		$response['timestamp'] =time();
		$response['data']['date'] =$edate;
		$response['data']['datemonth'] =date('n',strtotime($edate));
		$response['data']['dateday'] =date('j',strtotime($edate));

		$data=$this->upData($response['data']);
		$response['data']=$data;
		//echo '<pre>';
		//print_r($data);

		ob_clean();
		echo $this->pri_JSON($response);
	}


	//补数据
	public function upData($data){

		foreach($data as $k1=>$v1){
			$date_arrays=array();
			$data_arrays=array();
			foreach($v1["data"] as $k2=>$v2){
				foreach($v2["dataValue"] as $k3=>$v3){
					$date_arrays[$v3["xdata"]]=$v3["xdata"];
					$data_arrays[$k2][$v3["xdata"]]=$v3["ydata"];
				}
				$data[$k1]["data"][$k2]["dataValue"]=array();
			}
			sort($date_arrays);
			
			foreach($date_arrays as $k=>$v){
				foreach($data_arrays as $ak=>$av){
					if($av[$v]){
						$data[$k1]["data"][$ak]["dataValue"][]=array("xdata"=>$v,"ydata"=>$av[$v]);
					}else{
						if($this->getbu($av,$v)!=""){
							$data[$k1]["data"][$ak]["dataValue"][]=array("xdata"=>$v,"ydata"=>$this->getbu($av,$v));
						}else{
							$data[$k1]["data"][$ak]["dataValue"][]=array("xdata"=>$v,"ydata"=>$this->getbu2($av,$v));
						}

					}
				}
			}
		}

		return $data;
	}



	//补数据
	public function upData2($data){
		//echo '<pre>';
		//print_r($data);
		$year="2022-";
		$date_arrays=array();
		$data_arrays=array();
		foreach($data as $k1=>$v1){
			foreach($v1["dataValue"] as $k3=>$v3){
				$date_arrays[$v3["xdata"]]=$v3["xdata"];
				if(substr_count($v3["xdata"],"-")==1){
					$data_arrays[$k1][$year.$v3["xdata"]]=$v3["ydata"];
				}else{
					$data_arrays[$k1][$v3["xdata"]]=$v3["ydata"];
				}
			}
			$data[$k1]["dataValue"]=array();
		}
		sort($date_arrays);


	
		
		foreach($date_arrays as $k=>$v){
			foreach($data_arrays as $ak=>$av){
				if(substr_count($v,"-")==1){
					$date=$year.$v;
				}else{
					$date=$v;
				}
				if($av[$v]){
					$data[$ak]["dataValue"][]=array("xdata"=>$v,"ydata"=>$av[$date]);
				}else{
					if($this->getbu($av,$v)!=""){
						$data[$ak]["dataValue"][]=array("xdata"=>$v,"ydata"=>$this->getbu($av,$date));
					}else{
						$rr=$this->getbu2($av,$date);
						$data[$ak]["dataValue"][]=array("xdata"=>$v,"ydata"=>$rr==""?"-":$rr);
					}
				}
			}
		}

		return $data;
	}


	//补数据
	public function upData3($data){
		//echo '<pre>';
		//print_r($data);
		$year="2022-";
		$date_arrays=array();
		$data_arrays=array();
		foreach($data as $k1=>$v1){
			foreach($v1["dataValue"] as $k3=>$v3){
				$date_arrays[$v3["xdata"]]=$v3["xdata"];
				if(substr_count($v3["xdata"],"-")==1){
					$data_arrays[$k1][$year.$v3["xdata"]]=$v3["ydata"];
				}else{
					$data_arrays[$k1][$v3["xdata"]]=$v3["ydata"];
				}
			}
			$data[$k1]["dataValue"]=array();
		}
		sort($date_arrays);


	
		
		foreach($date_arrays as $k=>$v){
			foreach($data_arrays as $ak=>$av){
				if(substr_count($v,"-")==1){
					$date=$year.$v;
				}else{
					$date=$v;
				}
				if($av[$v]){
					$data[$ak]["dataValue"][]=array("xdata"=>$v,"ydata"=>$av[$date]);
				}else{
					if($this->getbu($av,$v)!=""){
						$data[$ak]["dataValue"][]=array("xdata"=>$v,"ydata"=>$this->getbu($av,$date));
					}else{
						$rr=$this->getbu2($av,$date);
						$data[$ak]["dataValue"][]=array("xdata"=>$v,"ydata"=>$rr==""?"-":$rr);
					}
				}
			}
		}

		return $data;
	}
	
	//补数据
	public function upData4($data){
		$day="-01";
		$date_arrays=array();
		$data_arrays=array();
		foreach($data as $k1=>$v1){
			foreach($v1["dataValue"] as $k3=>$v3){
				$date_arrays[$v3["xdata"]]=$v3["xdata"];
				if(substr_count($v3["xdata"],"-")==1){
					$data_arrays[$k1][$v3["xdata"].$day]=$v3["ydata"];
				}else{
					$data_arrays[$k1][$v3["xdata"]]=$v3["ydata"];
				}
			}
			$data[$k1]["dataValue"]=array();
		}
		sort($date_arrays);



		
		foreach($date_arrays as $k=>$v){
			foreach($data_arrays as $ak=>$av){
				if(substr_count($v,"-")==1){
					$date=$v.$day;
				}else{
					$date=$v;
				}
				if($av[$v]){
					$data[$ak]["dataValue"][]=array("xdata"=>$v,"ydata"=>$av[$date]);
				}else{
					if($this->getbu($av,$v)!=""){
						$data[$ak]["dataValue"][]=array("xdata"=>$v,"ydata"=>$this->getbu($av,$date));
					}else{
						$data[$ak]["dataValue"][]=array("xdata"=>$v,"ydata"=>$this->getbu2($av,$date));
					}
				}
			}
		}

		return $data;
	}

	public function getbu($array,$k){

		$sdate=reset(array_keys($array));
		
		$stimestamp = strtotime($sdate);
		$etimestamp = strtotime($k);
		$lwg_all=array();
		// 计算日期段内有多少天
		$days = ($etimestamp - $stimestamp) / 86400 ;
		for($i =$days; $i >0 ; $i--){
			$date=date('Y-m-d', $stimestamp + (86400 * $i));
			if($array[$date]!=""){
				return $array[$date];
			}
		}
	}

  	public function getbu2($array,$k){
		$edate=end(array_keys($array));
		
		$stimestamp = strtotime($k);
		$etimestamp = strtotime($edate);
		$lwg_all=array();
		// 计算日期段内有多少天
		$days = ($etimestamp - $stimestamp) / 86400 + 1;
		
		for($i = 0; $i < $days; $i++){
			$date=date('Y-m-d', $stimestamp + (86400 * $i));
			if($array[$date]!=""){
				return $array[$date];
			}
		}
	}





	public function getScreenData3($params){
		$edate = $params['date'];
		$mastertopids=array('4486101','1182101','6784118','6785101','6683101','4463101','3563301','5663201','4187101','4189101','4593101','5394101','4562101','5662201','4062301','4062401');
		
		if(empty($edate))
		{
		  $edate=date('Y-m-d');

		  $sql2 = "select mastertopid,`price`,mconmanagedate from marketconditions where    mconmanagedate<= '".$edate." 23:59:59' and mastertopid in  ('".implode("','",$mastertopids)."')  order by mconmanagedate desc limit 1 ";
		  $marketconditions2=$this->_dao->getRow($sql2);
		  $edate=date('Y-m-d',strtotime($marketconditions2['mconmanagedate']));
		}
        $sdate=date('Y-m-d',strtotime("-1 year",strtotime($edate)));

		
		
		$sql = "select mastertopid,`price`,mconmanagedate from marketconditions where   mconmanagedate>= '".$sdate." 00:00:00' and mconmanagedate<= '".$edate." 23:59:59' and mastertopid in  ('".implode("','",$mastertopids)."')  order by mconmanagedate asc ";
		//echo $sql;exit;
		$marketconditions=$this->_dao->query($sql);

		$yl_data1=array();
		$yl_data2=array();
		$yl_data3=array();
		$yl_data4=array();
		$mj_data1=array();
		$mj_data2=array();
		$mj_data3=array();
		$mj_data4=array();
		$thj_data1=array();
		$thj_data2=array();
		$thj_data3=array();
		$thj_data4=array();
		$ys_data1=array();
		$ys_data2=array();
		$ys_data3=array();
		$ys_data4=array();
		$left_yl_max_min=array();
		$right_yl_max_min=array();
		$left_mj_max_min=array();
		$right_mj_max_min=array();
		$left_thj_max_min=array();
		$right_thj_max_min=array();
		$left_ys_max_min=array();
		$right_ys_max_min=array();
		$info=array();

		/*
		原料  河北(4486101) 张家港(1182101) 宏兴(6784118) 唐山(6785101)
		煤焦  临汾(6683101) 邯郸(4463101) 湘潭(3563301) 榆林(5663201)
		铁合金 内蒙古包头(4187101) 内蒙古(4189101) 辽宁(4593101) 四川(5394101)
		有色  电解铜(4562101) 铝锭(5662201) 铅锭(4062301) 锌锭(4062401)
		*/

		foreach($marketconditions as $mkey=>$mvalue){
			if($mvalue['mastertopid']=='4486101'){//原料
				$yl_data1[]=array('ydata'=>$mvalue['price'],'xdata'=>date('Y-m-d',strtotime($mvalue['mconmanagedate'])));
				$right_yl_max_min[]=$mvalue['price'];
			}else if($mvalue['mastertopid']=='1182101'){
				$yl_data2[]=array('ydata'=>$mvalue['price'],'xdata'=>date('Y-m-d',strtotime($mvalue['mconmanagedate'])));
				$left_yl_max_min[]=$mvalue['price'];
			}else if($mvalue['mastertopid']=='6784118'){
				$yl_data3[]=array('ydata'=>$mvalue['price'],'xdata'=>date('Y-m-d',strtotime($mvalue['mconmanagedate'])));
				
			}else if($mvalue['mastertopid']=='6785101'){
				$yl_data4[]=array('ydata'=>$mvalue['price'],'xdata'=>date('Y-m-d',strtotime($mvalue['mconmanagedate'])));
				$left_yl_max_min[]=$mvalue['price'];
			}else if($mvalue['mastertopid']=='6683101'){//煤焦
				$mj_data1[]=array('ydata'=>$mvalue['price'],'xdata'=>date('Y-m-d',strtotime($mvalue['mconmanagedate'])));
				$left_mj_max_min[]=$mvalue['price'];
			}else if($mvalue['mastertopid']=='4463101'){
				$mj_data2[]=array('ydata'=>$mvalue['price'],'xdata'=>date('Y-m-d',strtotime($mvalue['mconmanagedate'])));
				$left_mj_max_min[]=$mvalue['price'];
			}else if($mvalue['mastertopid']=='3563301'){
				$mj_data3[]=array('ydata'=>$mvalue['price'],'xdata'=>date('Y-m-d',strtotime($mvalue['mconmanagedate'])));
				$left_mj_max_min[]=$mvalue['price'];
			}else if($mvalue['mastertopid']=='5663201'){
				$mj_data4[]=array('ydata'=>$mvalue['price'],'xdata'=>date('Y-m-d',strtotime($mvalue['mconmanagedate'])));
				$right_mj_max_min[]=$mvalue['price'];
			}else if($mvalue['mastertopid']=='4187101'){//铁合金
				$thj_data1[]=array('ydata'=>$mvalue['price'],'xdata'=>date('Y-m-d',strtotime($mvalue['mconmanagedate'])));
				$left_thj_max_min[]=$mvalue['price'];
			}else if($mvalue['mastertopid']=='4189101'){
				$thj_data2[]=array('ydata'=>$mvalue['price'],'xdata'=>date('Y-m-d',strtotime($mvalue['mconmanagedate'])));
				$left_thj_max_min[]=$mvalue['price'];
			}else if($mvalue['mastertopid']=='4593101'){
				$thj_data3[]=array('ydata'=>$mvalue['price'],'xdata'=>date('Y-m-d',strtotime($mvalue['mconmanagedate'])));
				$right_thj_max_min[]=$mvalue['price'];
			}else if($mvalue['mastertopid']=='5394101'){
				$thj_data4[]=array('ydata'=>$mvalue['price'],'xdata'=>date('Y-m-d',strtotime($mvalue['mconmanagedate'])));
				$right_thj_max_min[]=$mvalue['price'];
			}else if($mvalue['mastertopid']=='4562101'){//有色
				$ys_data1[]=array('ydata'=>$mvalue['price'],'xdata'=>date('Y-m-d',strtotime($mvalue['mconmanagedate'])));
				$right_ys_max_min[]=$mvalue['price'];
			}else if($mvalue['mastertopid']=='5662201'){
				$ys_data2[]=array('ydata'=>$mvalue['price'],'xdata'=>date('Y-m-d',strtotime($mvalue['mconmanagedate'])));
				$left_ys_max_min[]=$mvalue['price'];
			}else if($mvalue['mastertopid']=='4062301'){
				$ys_data3[]=array('ydata'=>$mvalue['price'],'xdata'=>date('Y-m-d',strtotime($mvalue['mconmanagedate'])));
				$left_ys_max_min[]=$mvalue['price'];
			}else if($mvalue['mastertopid']=='4062401'){
				$ys_data4[]=array('ydata'=>$mvalue['price'],'xdata'=>date('Y-m-d',strtotime($mvalue['mconmanagedate'])));
				$left_ys_max_min[]=$mvalue['price'];
			}
		}


		$yl_data3_1=array();
		foreach($yl_data1 as $k1=>$v1){
			foreach($yl_data3 as $k3=>$v3){
				if($v3['xdata']==$v1['xdata']){
					$yl_data3_1[]=$v3;
					$left_yl_max_min[]=$v3['ydata'];
				}
			}
		}
		
	

		if($left_yl_max_min){
			$min=min($left_yl_max_min);
			$max=max($left_yl_max_min);
		
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$left_yl_infomax=$getmax_min_jg['max'];
			$left_yl_infomin=$getmax_min_jg['min'];
			$left_yl_infointerval=$getmax_min_jg['interval'];
		}else{
			$left_yl_infomax='';
			$left_yl_infomin='';
			$left_yl_infointerval='';
		}

		if($right_yl_max_min){
			$min=min($right_yl_max_min);
			$max=max($right_yl_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$right_yl_infomax=$getmax_min_jg['max'];
			$right_yl_infomin=$getmax_min_jg['min'];
			$right_yl_infointerval=$getmax_min_jg['interval'];
		}else{
			$right_yl_infomax='';
			$right_yl_infomin='';
			$right_yl_infointerval='';
		}

		$info['dataName']='河北唐山铁精粉Fe:66%价格（干基/承兑出厂）';
		$info['dataType']='line';
		$info['isLeft']=0;
		$info['dataValue']=$yl_data1;
		$response['data']['PriceData1']['data'][] =$info;

		$info['dataName']='张家港重废2类6-10mm';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$yl_data2;
		$response['data']['PriceData1']['data'][] =$info;

		$info['dataName']='宏兴方坯Q235价格';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$yl_data3_1;
		$response['data']['PriceData1']['data'][] =$info;

		$info['dataName']='唐山炼钢生铁L10（S:0.05-0.07%,P:0.18-0.20%)价格';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$yl_data4;
		$response['data']['PriceData1']['data'][] =$info;

		$response['data']['PriceData1']['leftmax']=$left_yl_infomax;
		$response['data']['PriceData1']['leftmin']=$left_yl_infomin;
		$response['data']['PriceData1']['leftinterval']=$left_yl_infointerval;

		$response['data']['PriceData1']['rightmax']=$right_yl_infomax;
		$response['data']['PriceData1']['rightmin']=$right_yl_infomin;
		$response['data']['PriceData1']['rightinterval']=$right_yl_infointerval;


		if($left_mj_max_min){
			$min=min($left_mj_max_min);
			$max=max($left_mj_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$left_mj_infomax=$getmax_min_jg['max'];
			$left_mj_infomin=$getmax_min_jg['min'];
			$left_mj_infointerval=$getmax_min_jg['interval'];
		}else{
			$left_mj_infomax='';
			$left_mj_infomin='';
			$left_mj_infointerval='';
		}

		if($right_mj_max_min){
			$min=min($right_mj_max_min);
			$max=max($right_mj_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$right_mj_infomax=$getmax_min_jg['max'];
			$right_mj_infomin=$getmax_min_jg['min'];
			$right_mj_infointerval=$getmax_min_jg['interval'];
		}else{
			$right_mj_infomax='';
			$right_mj_infomin='';
			$right_mj_infointerval='';
		}

		$info['dataName']='二级冶金焦-临汾(承兑/自提)A<13.5%,S<0.75%,Mt≤10%,M25>90%,CSR>55%';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$mj_data1;
		$response['data']['PriceData2']['data'][] =$info;

		$info['dataName']='焦煤-邯郸(承兑)A:9.5-10%,V:24-25%,S<1%,G>75,Mt:8%';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$mj_data2;
		$response['data']['PriceData2']['data'][] =$info;

		$info['dataName']='喷吹煤-湘潭(承兑/到厂价/非大矿)A<13%,V:3-8%,S<0.6-0.7%,可磨:80';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$mj_data3;
		$response['data']['PriceData2']['data'][] =$info;

		$info['dataName']='动力煤-榆林(现汇)Q:5800';
		$info['dataType']='line';
		$info['isLeft']=0;
		$info['dataValue']=$mj_data4;
		$response['data']['PriceData2']['data'][] =$info;

		$response['data']['PriceData2']['leftmax']=$left_mj_infomax;
		$response['data']['PriceData2']['leftmin']=$left_mj_infomin;
		$response['data']['PriceData2']['leftinterval']=$left_mj_infointerval;
		$response['data']['PriceData2']['rightmax']=$right_mj_infomax;
		$response['data']['PriceData2']['rightmin']=$right_mj_infomin;
		$response['data']['PriceData2']['rightinterval']=$right_mj_infointerval;

		if($left_thj_max_min){
			$min=min($left_thj_max_min);
			$max=max($left_thj_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$left_thj_infomax=$getmax_min_jg['max'];
			$left_thj_infomin=$getmax_min_jg['min'];
			$left_thj_infointerval=$getmax_min_jg['interval'];
		}else{
			$left_thj_infomax='';
			$left_thj_infomin='';
			$left_thj_infointerval='';
		}

		if($right_thj_max_min){
			$min=min($right_thj_max_min);
			$max=max($right_thj_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$right_thj_infomax=$getmax_min_jg['max'];
			$right_thj_infomin=$getmax_min_jg['min'];
			$right_thj_infointerval=$getmax_min_jg['interval'];
		}else{
			$right_thj_infomax='';
			$right_thj_infomin='';
			$right_thj_infointerval='';
		}


		$info['dataName']='内蒙包头FeSi75-B自然块，承兑出厂';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$thj_data1;
		$response['data']['PriceData3']['data'][] =$info;

		$info['dataName']='内蒙古FeMn68Sil8（P≤0.25%）';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$thj_data2;
		$response['data']['PriceData3']['data'][] =$info;

		$info['dataName']='辽宁FeMo60';
		$info['dataType']='line';
		$info['isLeft']=0;
		$info['dataValue']=$thj_data3;
		$response['data']['PriceData3']['data'][] =$info;

		$info['dataName']='四川FeV50-B';
		$info['dataType']='line';
		$info['isLeft']=0;
		$info['dataValue']=$thj_data4;
		$response['data']['PriceData3']['data'][] =$info;

		$response['data']['PriceData3']['leftmax']=$left_thj_infomax;
		$response['data']['PriceData3']['leftmin']=$left_thj_infomin;
		$response['data']['PriceData3']['leftinterval']=$left_thj_infointerval;

		$response['data']['PriceData3']['rightmax']=$right_thj_infomax;
		$response['data']['PriceData3']['rightmin']=$right_thj_infomin;
		$response['data']['PriceData3']['rightinterval']=$right_thj_infointerval;

		if($left_ys_max_min){
			$min=min($left_ys_max_min);
			$max=max($left_ys_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$left_ys_infomax=$getmax_min_jg['max'];
			$left_ys_infomin=$getmax_min_jg['min'];
			$left_ys_infointerval=$getmax_min_jg['interval'];
		}else{
			$left_ys_infomax='';
			$left_ys_infomin='';
			$left_ys_infointerval='';
		}

		if($right_ys_max_min){
			$min=min($right_ys_max_min);
			$max=max($right_ys_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$right_ys_infomax=$getmax_min_jg['max'];
			$right_ys_infomin=$getmax_min_jg['min'];
			$right_ys_infointerval=$getmax_min_jg['interval'];
		}else{
			$right_ys_infomax='';
			$right_ys_infomin='';
			$right_ys_infointerval='';
		}

		$info['dataName']='电解铜沈阳辽宁/内蒙古1#';
		$info['dataType']='line';
		$info['isLeft']=0;
		$info['dataValue']=$ys_data1;
		$response['data']['PriceData4']['data'][] =$info;

		$info['dataName']='铝锭西安宁夏A00';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$ys_data2;
		$response['data']['PriceData4']['data'][] =$info;

		$info['dataName']='铅锭天津青海 甘肃 河北1＃';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$ys_data3;
		$response['data']['PriceData4']['data'][] =$info;

		$info['dataName']='锌锭天津辽宁/甘肃0#';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$ys_data4;
		$response['data']['PriceData4']['data'][] =$info;

		$response['data']['PriceData4']['leftmax']=$left_ys_infomax;
		$response['data']['PriceData4']['leftmin']=$left_ys_infomin;
		$response['data']['PriceData4']['leftinterval']=$left_ys_infointerval;

		$response['data']['PriceData4']['rightmax']=$right_ys_infomax;
		$response['data']['PriceData4']['rightmin']=$right_ys_infomin;
		$response['data']['PriceData4']['rightinterval']=$right_ys_infointerval;

		$response['Success'] = 1;
		$response['Message']='获取成功';
		$response['timestamp'] =time();
		$response['data']['date'] =$edate;
		$response['data']['datemonth'] =date('n',strtotime($edate));
		$response['data']['dateday'] =date('j',strtotime($edate));

		//echo '<pre>';
		//print_r($marketconditions);

		ob_clean();
		echo $this->pri_JSON($response);
	}


	public function getScreenData4($params){
		$edate = $params['date'];
		if(empty($edate))
		{
		  $edate=date('Y-m-d');
		}
        $sdate=date('Y-m-d',strtotime("-1 year",strtotime($edate)));

		
		
		//1、国内主要钢厂高炉开工率   全国高炉-按产能开工率   全国高炉-按容积开工率
		$sql="select dta_2,dta_3,dta_ym from data_table WHERE dta_type='GC_KGL' and dta_1='全国' and dta_ym>='".$sdate."' and dta_ym<='".$edate."'  order by  dta_ym asc ";
		$data_table=$this->drcdao->query($sql);

		foreach($data_table as $key=>$value){
			$kgl_data1[]=array('ydata'=>$value['dta_2'],'xdata'=>$value['dta_ym']);
			$kgl_data2[]=array('ydata'=>$value['dta_3'],'xdata'=>$value['dta_ym']);
			$kgl_max_min[]=$value['dta_2'];
			$kgl_max_min[]=$value['dta_3'];
		}

		if($kgl_max_min){
			$min=min($kgl_max_min);
			$max=max($kgl_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$kgl_infomax=$getmax_min_jg['max'];
			$kgl_infomin=$getmax_min_jg['min'];
			$kgl_infointerval=$getmax_min_jg['interval'];
		}else{
			$kgl_infomax='';
			$kgl_infomin='';
			$kgl_infointerval='';
		}

		$info['dataName']='全国高炉-按产能开工率';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$kgl_data1;
		$response['data']['Data1']['data'][] =$info;

		$info['dataName']='全国高炉-按容积开工率';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$kgl_data2;
		$response['data']['Data1']['data'][] =$info;


		$response['data']['Data1']['max']=$kgl_infomax;
		$response['data']['Data1']['min']=$kgl_infomin;
		$response['data']['Data1']['interval']=$kgl_infointerval;



		//2、五大品种钢厂库存和市场库存合计走势图 （取两年数据）  钢材五大品种库存（周）    钢厂五大品种库存（周）
		$sdate2= date('Y-m-d',strtotime("-2 year",strtotime($edate)));
		$sql1="select time,svalue from (select time,sum(value)as svalue from kucun_hz where type in ('1','2','3','4','5')   and  time>= '".$sdate2." 00:00:00' and time<= '".$edate." 23:59:59' group by time) as tab WHERE 1 order by time asc ";
		$kucun_hz1=$this->drcdao->query($sql1);
		foreach($kucun_hz1 as $key=>$value){
			$kchz_data1[]=array('ydata'=>$value['svalue'],'xdata'=>date('Y-m-d',strtotime($value['time'])));
			$kchz_max_min[]=$value['svalue'];
		}


		$sql2="select  Value1,`Date` from   (SELECT Round(SUM( Value ),2) Value1 ,  `Date`  FROM  steelhome_gc.`KuCunHZ`  WHERE  `Type`  IN ( 1, 2, 3, 4, 5 )  AND  `Area` =0 and  `Date`>= '".$sdate2." 00:00:00' and `Date`<= '".$edate." 23:59:59' GROUP BY DATE)  tab where 1=1 order by `Date` asc";
		$kucun_hz2=$this->drcdao->query($sql2);
		
		foreach($kucun_hz2 as $key=>$value){
			$kchz_data2[]=array('ydata'=>$value['Value1'],'xdata'=>date('Y-m-d',strtotime($value['Date'])));
			$kchz_max_min[]=$value['Value1'];
		}
		
		
		if($kchz_max_min){
			$min=min($kchz_max_min);
			$max=max($kchz_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$kchz_infomax=$getmax_min_jg['max'];
			$kchz_infomin=$getmax_min_jg['min'];
			$kchz_infointerval=$getmax_min_jg['interval'];
		}else{
			$kchz_infomax='';
			$kchz_infomin='';
			$kchz_infointerval='';
		}

		$info['dataName']='钢材五大品种库存（周）';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$kchz_data1;
		$response['data']['Data2']['data'][] =$info;


		$info['dataName']='钢厂五大品种库存（周）';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$kchz_data2;
		$response['data']['Data2']['data'][] =$info;
		$response['data']['Data2']['data']=$this->upData2($response['data']['Data2']['data']);
		$response['data']['Data2']['max']=$kchz_infomax;
		$response['data']['Data2']['min']=$kchz_infomin;
		$response['data']['Data2']['interval']=$kchz_infointerval;




		//3、国内主要独立电炉企业开工率   按个数开工率  全国电炉-按产能开工率
		$sql="select dta_2,dta_3,dta_ym from data_table WHERE dta_type='DL_KGL' and dta_1='全国' and dta_ym>='".$sdate."' and dta_ym<='".$edate."' order by dta_ym asc ";
		$kgl_array=$this->drcdao->query($sql);

		foreach($kgl_array as $key=>$value){
			$dlkgl_data1[]=array('ydata'=>$value['dta_3'],'xdata'=>$value['dta_ym']);
			$dlkgl_data2[]=array('ydata'=>$value['dta_2'],'xdata'=>$value['dta_ym']);
			$dlkgl_max_min[]=$value["dta_2"];
			$dlkgl_max_min[]=$value["dta_3"];
		}
		if($dlkgl_max_min){
			$min=min($dlkgl_max_min);
			$max=max($dlkgl_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$dlkgl_infomax=$getmax_min_jg['max'];
			$dlkgl_infomin=$getmax_min_jg['min'];
			$dlkgl_infointerval=$getmax_min_jg['interval'];
		}else{
			$dlkgl_infomax='';
			$dlkgl_infomin='';
			$dlkgl_infointerval='';
		}


		$info['dataName']='按个数开工率';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$dlkgl_data1;
		$response['data']['Data3']['data'][] =$info;


		$info['dataName']='按产能开工率';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$dlkgl_data2;
		$response['data']['Data3']['data'][] =$info;

		$response['data']['Data3']['max']=$dlkgl_infomax;
		$response['data']['Data3']['min']=$dlkgl_infomin;
		$response['data']['Data3']['interval']=$dlkgl_infointerval;




		//4、煤焦焦炭库存
		$sql1="select jt_kc,ljm_kcw,Date from steelhome_gc.sc_othercom_stock_hz where AreaCode=0  and Date>='".$sdate."' and Date<='".$edate."' order by Date asc ";
		$sc_othercom_stock_hz=$this->drcdao->query($sql1);
		foreach($sc_othercom_stock_hz as $key=>$value){
			$jt_data1[]=array('ydata'=>$value['jt_kc'],'xdata'=>$value['Date']);
			$jt_data2[]=array('ydata'=>$value['ljm_kcw'],'xdata'=>$value['Date']);
			$left_jt_max_min[]=$value['jt_kc'];
			$right_jt_max_min[]=$value['ljm_kcw'];
		}

		if($left_jt_max_min){
			$min=min($left_jt_max_min);
			$max=max($left_jt_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$left_jt_infomax=$getmax_min_jg['max'];
			$left_jt_infomin=$getmax_min_jg['min'];
			$left_jt_infointerval=$getmax_min_jg['interval'];
		}else{
			$left_jt_infomax='';
			$left_jt_infomin='';
			$left_jt_infointerval='';
		}

		if($right_jt_max_min){
			$min=min($right_jt_max_min);
			$max=max($right_jt_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$right_jt_infomax=$getmax_min_jg['max'];
			$right_jt_infomin=$getmax_min_jg['min'];
			$right_jt_infointerval=$getmax_min_jg['interval'];
		}else{
			$right_jt_infomax='';
			$right_jt_infomin='';
			$right_jt_infointerval='';
		}
		
		$info['dataName']='100家独立焦企焦炭总库存(万吨)';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$jt_data1;
		$response['data']['Data4']['data'][] =$info;

		$info['dataName']='100家独立焦企炼焦煤库存(万吨)';
		$info['dataType']='line';
		$info['isLeft']=0;
		$info['dataValue']=$jt_data2;

		$response['data']['Data4']['data'][] =$info;
		$response['data']['Data4']['leftmax']=$left_jt_infomax;
		$response['data']['Data4']['leftmin']=$left_jt_infomin;
		$response['data']['Data4']['leftinterval']=$left_jt_infointerval;
		$response['data']['Data4']['rightmax']=$right_jt_infomax;
		$response['data']['Data4']['rightmin']=$right_jt_infomin;
		$response['data']['Data4']['rightinterval']=$right_jt_infointerval;



		//5、全国独立焦企产能利用率    全国独立焦企产能利用率
		$sql="select KaiGonglv2,Date from steelhome_gc.sc_KaiGongLvHuiZong  WHERE 1 and AreaCode=0 and Date>='".$sdate."' and Date<='".$edate."' order by Date asc  ";
		$sc_KaiGongLvHuiZong=$this->drcdao->query($sql);
		foreach($sc_KaiGongLvHuiZong as $key=>$value){
			$lyl_data[]=array('ydata'=>$value['KaiGonglv2'],'xdata'=>$value['Date']);
			$lyl_max_min[]=$value["KaiGonglv2"];
		}
		if($lyl_max_min){
			$min=min($lyl_max_min);
			$max=max($lyl_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$lyl_infomax=$getmax_min_jg['max'];
			$lyl_infomin=$getmax_min_jg['min'];
			$lyl_infointerval=$getmax_min_jg['interval'];
		}else{
			$lyl_infomax='';
			$lyl_infomin='';
			$lyl_infointerval='';
		}


		$info['dataName']='全国独立焦企产能利用率';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$lyl_data;
		$response['data']['Data5']['data'][] =$info;

		$response['data']['Data5']['max']=$lyl_infomax;
		$response['data']['Data5']['min']=$lyl_infomin;
		$response['data']['Data5']['interval']=$lyl_infointerval;


		//6、中国主要港口铁矿石库存（周）  2018 2019 2020 2021 
		$edate3=date('Y',strtotime($edate))."-12-31";

		$sdate3=date('Y',strtotime("-3 year",strtotime($edate3)))."-01-01";
		$sql="SELECT dta_3,dta_ym FROM `data_table` WHERE  dta_type = 'JKKCTJ_1' and dta_1='合计' and dta_ym>='".$sdate3."' and dta_ym<='".$edate3."'  ORDER BY `dta_ym` ASC";
		$kucun_tks=$this->drcdao->query($sql);
		
		foreach($kucun_tks as $key=>$value){
			$kucun_arr[date('Y',strtotime($value['dta_ym']))][]=array('ydata'=>$value['dta_3'],'xdata'=>date('m-d',strtotime($value['dta_ym'])));
			$kuncun_max_min[]=$value['dta_3'];
		}
		foreach($kucun_arr as $key=>$value){
			$info['dataName']=$key;
			$info['dataType']='line';
			$info['isLeft']=1;
			$info['dataValue']=$value;
			$response['data']['Data6']['data'][] =$info;
		}

		$response['data']['Data6']['data']=$this->upData2($response['data']['Data6']['data']);

		if($kuncun_max_min){
			$min=min($kuncun_max_min);
			$max=max($kuncun_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$kuncun_infomax=$getmax_min_jg['max'];
			$kuncun_infomin=$getmax_min_jg['min'];
			$kuncun_infointerval=$getmax_min_jg['interval'];
		}else{
			$kuncun_infomax='';
			$kuncun_infomin='';
			$kuncun_infointerval='';
		}
		
		$response['data']['Data6']['max']=$kuncun_infomax;
		$response['data']['Data6']['min']=$kuncun_infomin;
		$response['data']['Data6']['interval']=$kuncun_infointerval;



		//7、螺纹钢、中厚板和热轧板卷日均成交量走势图    螺纹钢日均成交量   热轧板卷日均成交量   中厚板日均成交量
		$sql="select EndDate,value,PinZhong from (SELECT db.EndDate as EndDate ,db.value as value,db.`PinZhong` as PinZhong FROM steelhome_gc.ChengJiaoLiangDiaoChaTotalInfo AS db LEFT JOIN steelhome_gc.ChengJiaoLiangDiaoChaWeeklyBaseInfo  AS dt ON db.EndDate = dt.EndDate where db.TYPE =2 AND db.`cityid` = '-1' and ((dt.`PinZhong`=1  and db.`PinZhong`=1) or (dt.`PinZhong`=2  and db.`PinZhong`=2) or (dt.`PinZhong`=4  and db.`PinZhong`=4)) and dt.isFinished=1 and db.EndDate>='".$sdate."' and db.EndDate<='".$edate."') as tab WHERE 1 order by EndDate asc";
		$ChengJiaoLiangDiaoChaTotalInfo=$this->drcdao->query($sql);

		foreach($ChengJiaoLiangDiaoChaTotalInfo as $key=>$value){
			if($value["PinZhong"]==1){
				$cjl_data1[]=array('ydata'=>$value['value'],'xdata'=>$value['EndDate']);
				$right_cjl_max_min[]=$value['value'];
			}else if($value["PinZhong"]==2){
				$cjl_data2[]=array('ydata'=>$value['value'],'xdata'=>$value['EndDate']);
				$left_cjl_max_min[]=$value['value'];
			}else if($value["PinZhong"]==4){
				$cjl_data3[]=array('ydata'=>$value['value'],'xdata'=>$value['EndDate']);
				$left_cjl_max_min[]=$value['value'];
			}
			
		}


		if($left_cjl_max_min){
			$min=min($left_cjl_max_min);
			$max=max($left_cjl_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$left_cjl_infomax=$getmax_min_jg['max'];
			$left_cjl_infomin=$getmax_min_jg['min'];
			$left_cjl_infointerval=$getmax_min_jg['interval'];
		}else{
			$left_cjl_infomax='';
			$left_cjl_infomin='';
			$left_cjl_infointerval='';
		}


		if($right_cjl_max_min){
			$min=min($right_cjl_max_min);
			$max=max($right_cjl_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$right_cjl_infomax=$getmax_min_jg['max'];
			$right_cjl_infomin=$getmax_min_jg['min'];
			$right_cjl_infointerval=$getmax_min_jg['interval'];
		}else{
			$right_cjl_infomax='';
			$right_cjl_infomin='';
			$right_cjl_infointerval='';
		}


		$info['dataName']='螺纹钢日均成交量';
		$info['dataType']='line';
		$info['isLeft']=0;
		$info['dataValue']=$cjl_data1;
		$response['data']['Data7']['data'][] =$info;


		$info['dataName']='热轧板卷日均成交量';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$cjl_data2;
		$response['data']['Data7']['data'][] =$info;


		$info['dataName']='中厚板日均成交量';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$cjl_data3;
		$response['data']['Data7']['data'][] =$info;
		$response['data']['Data7']['data']=$this->upData2($response['data']['Data7']['data']);
		$response['data']['Data7']['leftmax']=$left_cjl_infomax;
		$response['data']['Data7']['leftmin']=$left_cjl_infomin;
		$response['data']['Data7']['leftinterval']=$left_cjl_infointerval;
		$response['data']['Data7']['rightmax']=$right_cjl_infomax;
		$response['data']['Data7']['rightmin']=$right_cjl_infomin;
		$response['data']['Data7']['rightinterval']=$right_cjl_infointerval;

		
		//2、螺纹钢 线材 中厚板 热轧 冷轧计划量调查    螺纹钢计划量   线材计划量  中厚板计划量  热轧板卷计划量
		/*$edate4=date('Y-m',strtotime($edate));
		$sdate4=date('Y-m',strtotime("-1 year",strtotime($edate4)));
		$sql1="select dta_3,dta_5,dta_ym from steelhome_gc.sc_plansummary where dta_vartype =  'jc' and  dta_steelid =  '-1'  and dta_ym>='".$sdate4."' and dta_ym<='".$edate4."' order by dta_ym asc";
		$sc_plansummary=$this->drcdao->query($sql1);

		foreach($sc_plansummary as $key=>$value){
			if($value["dta_3"]!=""){
				$jhl_data1[]=array('ydata'=>$value['dta_3'],'xdata'=>$value['dta_ym']);
				$jhl_max_min[]=$value['dta_3'];
			}
			$jhl_data2[]=array('ydata'=>$value['dta_5'],'xdata'=>$value['dta_ym']);
			$jhl_max_min[]=$value['dta_5'];
		}

		$info['dataName']='螺纹钢计划量';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$jhl_data1;
		$response['data']['Data2']['data'][] =$info;


		$info['dataName']='线材计划量';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$jhl_data2;
		$response['data']['Data2']['data'][] =$info;


		$sql3="select s1,dta_ym from ( select sum(dta_1) as s1,dta_ym from steelhome_gc.sc_plansummary where dta_vartype =  'zhb' and  dta_steelid  !=  '-1'  and dta_ym>='".$sdate4."' and dta_ym<='".$edate4."' group by dta_ym) as tab where 1 order by dta_ym asc";
		$sc_plansummary3=$this->drcdao->query($sql3);
		foreach($sc_plansummary3 as $key=>$value){
			$jhl_data3[]=array('ydata'=>$value['s1'],'xdata'=>$value['dta_ym']);
			$jhl_max_min[]=$value['s1'];
		}
		$info['dataName']='中厚板计划量';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$jhl_data3;
		$response['data']['Data2']['data'][] =$info;

		$sql4="select s2,dta_ym from ( select sum(dta_2) as s2,dta_ym from steelhome_gc.sc_plansummary where dta_vartype =  'rz' and  dta_steelid  !=  '-1'  and dta_ym>='".$sdate4."' and dta_ym<='".$edate4."' group by dta_ym) as tab where 1 order by dta_ym asc";
		$sc_plansummary4=$this->drcdao->query($sql4);
		foreach($sc_plansummary4 as $key=>$value){
			$jhl_data4[]=array('ydata'=>$value['s2'],'xdata'=>$value['dta_ym']);
			$jhl_max_min[]=$value['s2'];
		}
		$info['dataName']='热轧板卷计划量';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$jhl_data4;
		$response['data']['Data2']['data'][] =$info;

		$sql5="select s2,dta_ym from ( select sum(dta_2) as s2,dta_ym from steelhome_gc.sc_plansummary where dta_vartype =  'lz' and  dta_steelid  !=  '-1'  and dta_ym>='".$sdate4."' and dta_ym<='".$edate4."' group by dta_ym) as tab where 1 order by dta_ym asc";
		$sc_plansummary5=$this->drcdao->query($sql5);
		foreach($sc_plansummary5 as $key=>$value){
			$jhl_data5[]=array('ydata'=>$value['s2'],'xdata'=>$value['dta_ym']);
			$jhl_max_min[]=$value['s2'];
		}
		$info['dataName']='冷轧板卷计划量';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$jhl_data5;
		$response['data']['Data2']['data'][] =$info;


		if($jhl_max_min){
			$min=min($jhl_max_min);
			$max=max($jhl_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$jhl_infomax=$getmax_min_jg['max'];
			$jhl_infomin=$getmax_min_jg['min'];
			$jhl_infointerval=$getmax_min_jg['interval'];
		}else{
			$jhl_infomax='';
			$jhl_infomin='';
			$jhl_infointerval='';
		}

		$response['data']['Data2']['data']=$this->upData4($response['data']['Data2']['data']);

		$response['data']['Data2']['max']=$jhl_infomax;
		$response['data']['Data2']['min']=$jhl_infomin;
		$response['data']['Data2']['interval']=$jhl_infointerval;*/

		



		//8、螺纹钢 线材 中厚板 热轧 冷轧消费量调查 
		$sql="select consumption,Date,Type from steelhome_gc.market_consumption where Type in(1,2,3,4,5) and Area=0  and  Date>='".$sdate."' and Date<='".$edate."' order by  Date asc";
		$market_consumption=$this->drcdao->query($sql);

		foreach($market_consumption as $key=>$value){
			if($value["Type"]==1){
				$xfl_data1[]=array('ydata'=>$value['consumption'],'xdata'=>$value['Date']);
			}else if($value["Type"]==2){
				$xfl_data2[]=array('ydata'=>$value['consumption'],'xdata'=>$value['Date']);
			}else if($value["Type"]==3){
				$xfl_data3[]=array('ydata'=>$value['consumption'],'xdata'=>$value['Date']);
			}else if($value["Type"]==4){
				$xfl_data4[]=array('ydata'=>$value['consumption'],'xdata'=>$value['Date']);
			}else if($value["Type"]==5){
				$xfl_data5[]=array('ydata'=>$value['consumption'],'xdata'=>$value['Date']);
			}
			$xfl_max_min[]=$value["consumption"];
		}
		if($xfl_max_min){
			$min=min($xfl_max_min);
			$max=max($xfl_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$xfl_infomax=$getmax_min_jg['max'];
			$xfl_infomin=$getmax_min_jg['min'];
			$xfl_infointerval=$getmax_min_jg['interval'];
		}else{
			$xfl_infomax='';
			$xfl_infomin='';
			$xfl_infointerval='';
		}
		
		$info['dataName']='螺纹钢';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$xfl_data1;
		$response['data']['Data8']['data'][] =$info;

		$info['dataName']='线材';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$xfl_data2;
		$response['data']['Data8']['data'][] =$info;


		$info['dataName']='中厚板';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$xfl_data3;
		$response['data']['Data8']['data'][] =$info;


		$info['dataName']='热轧板卷';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$xfl_data4;
		$response['data']['Data8']['data'][] =$info;

		$info['dataName']='冷轧板卷';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$xfl_data5;
		$response['data']['Data8']['data'][] =$info;
		$response['data']['Data8']['data']=$this->upData2($response['data']['Data8']['data']);
		$response['data']['Data8']['max']=$xfl_infomax;
		$response['data']['Data8']['min']=$xfl_infomin;
		$response['data']['Data8']['interval']=$xfl_infointerval;

		$response['Success'] = 1;
		$response['Message']='获取成功';
		
		$response['timestamp'] =time();
		$response['data']['date'] =$edate;
		$response['data']['datemonth'] =date('n',strtotime($edate));
		$response['data']['dateday'] =date('j',strtotime($edate));

		ob_clean();
		echo $this->pri_JSON($response);

	}





	public function getScreenData5($params){
		$edate = $params['date'];
		if(empty($edate))
		{
			$edate=date("Y-m-d",strtotime("-1 day"));
		}

		if (_isholiday($edate)) {				
			$coil = 1;
			while (true) {			
				$day_temp = date("Y-m-d", time() -  $coil * 24 * 3600);
				//echo "<br>".$day_temp;
				if (!_isholiday($day_temp)) {
					break;
				}
				$coil++;					
				//echo "<br>coil=".$coil;
			}
			$edate = date("Y-m-d",strtotime($edate) - $coil * 24 * 3600); 
		
		}

        $sdate=date('Y-m-d',strtotime("-1 year",strtotime($edate)));


		/*
		上海螺纹钢 HRB400 Φ20-22mm    		      072023
		福建甬金 冷轧不锈钢卷 304/2B 2.0*1219mm    3149124
		乐从热卷 Q235B 5.75mm*1500*C              293112
		吕梁准一级焦							  T283111
		青岛港PB粉矿Fe:61.5%					  1886103
		吕梁焦煤								  T263102
		内蒙包头FeSi75-B加工块					   4187102
		贵州硅锰								  5489101
		*/
		$mastertopids=array('072023','3149124','293112','T283111','1886103','T263102','4187102','5489101');

		$sql="select price,mconmanagedate,topicture from marketconditions WHERE 1 and mconmanagedate>= '".$sdate." 00:00:00' and mconmanagedate<= '".$edate." 23:59:59' and topicture in  ('072023','293112') order by mconmanagedate asc";
		$marketconditions=$this->_dao->query($sql);

		$sql1="select price,mconmanagedate,mastertopid from marketconditions WHERE 1 and mconmanagedate>= '".$sdate." 00:00:00' and mconmanagedate<= '".$edate." 23:59:59' and  mastertopid in  ('".implode("','",$mastertopids)."')  order by mconmanagedate asc";
		$marketconditions2=$this->_dao->query($sql1);


		

		foreach($marketconditions as $key=>$value){
			if($value["topicture"]=="072023"){
				$lwg_data1[]=array('ydata'=>$value['price'],'xdata'=>date('Y-m-d',strtotime($value['mconmanagedate'])));
				$lwg_arr[date('Y-m-d',strtotime($value['mconmanagedate']))]=$value['price'];
				$left_lwg_max_min[]=$value['price'];
			}else if($value["topicture"]=="293112"){
				$rj_data2[]=array('ydata'=>$value['price'],'xdata'=>date('Y-m-d',strtotime($value['mconmanagedate'])));
				$left_rj_max_min[]=$value['price'];
				$rj_arr[date('Y-m-d',strtotime($value['mconmanagedate']))]=$value['price'];
			}
		}


		foreach($marketconditions2 as $key=>$value){
			if($value["mastertopid"]=="3149124"){
				$bxg_data2[]=array('ydata'=>$value['price'],'xdata'=>date('Y-m-d',strtotime($value['mconmanagedate'])));
				$bxg_arr[date('Y-m-d',strtotime($value['mconmanagedate']))]=$value['price'];
				$left_bxg_max_min[]=$value['price'];
			}else if($value["mastertopid"]=="T283111"){
				$jt_data2[]=array('ydata'=>$value['price'],'xdata'=>date('Y-m-d',strtotime($value['mconmanagedate'])));
				$left_jt_max_min[]=$value['price'];
				$jt_arr[date('Y-m-d',strtotime($value['mconmanagedate']))]=$value['price'];
			}else if($value["mastertopid"]=="1886103"){
				$tks_data2[]=array('ydata'=>$value['price'],'xdata'=>date('Y-m-d',strtotime($value['mconmanagedate'])));
				$left_tks_max_min[]=$value['price'];
				$tks_arr[date('Y-m-d',strtotime($value['mconmanagedate']))]=$value['price'];
			}else if($value["mastertopid"]=="T263102"){
				$ljm_data2[]=array('ydata'=>$value['price'],'xdata'=>date('Y-m-d',strtotime($value['mconmanagedate'])));
				$left_ljm_max_min[]=$value['price'];
				$ljm_arr[date('Y-m-d',strtotime($value['mconmanagedate']))]=$value['price'];
			}else if($value["mastertopid"]=="4187102"){
				$gt_data2[]=array('ydata'=>$value['price'],'xdata'=>date('Y-m-d',strtotime($value['mconmanagedate'])));
				$left_gt_max_min[]=$value['price'];
				$gt_arr[date('Y-m-d',strtotime($value['mconmanagedate']))]=$value['price'];
			}else if($value["mastertopid"]=="5489101"){
				$gm_data2[]=array('ydata'=>$value['price'],'xdata'=>date('Y-m-d',strtotime($value['mconmanagedate'])));
				$left_gm_max_min[]=$value['price'];
				$gm_arr[date('Y-m-d',strtotime($value['mconmanagedate']))]=$value['price'];
			}	
		}

		/*
		螺纹钢主力合约收盘价      SHQHDAY_4
		热卷主力合约收盘价        SHQHDAY_99
		焦炭主力合约收盘价		  SHQHDAY_9
		铁矿石主力合约收盘价      SHQHDAY_20
		焦煤主力合约收盘价	      SHQHDAY_19
		硅铁主力合约收盘价	      SHQHDAY_22
		硅锰主力合约收盘价		  SHQHDAY_23
		*/
		$dta_types=array('SHQHDAY_4','SHQHDAY_99','SHQHDAY_9','SHQHDAY_20','SHQHDAY_19','SHQHDAY_22','SHQHDAY_23');
		$sql2="select dta_6,dta_ym,dta_type from data_table  WHERE dta_type  in  ('".implode("','",$dta_types)."')  AND dta_maxValStatus=1  and dta_ym>='".$sdate."' and dta_ym<='".$edate."'   order by dta_ym asc ";
		$data_table=$this->drcdao->query($sql2);
	
		foreach($data_table as $key=>$value){
			if($value["dta_type"]=="SHQHDAY_4"){
				$lwg_data2[]=array('ydata'=>$value['dta_6'],'xdata'=>$value['dta_ym']);
				if(isset($lwg_arr[$value['dta_ym']])){
					$data=round($lwg_arr[$value['dta_ym']]/0.965-$value['dta_6']);
					$lwg_data3[]=array('ydata'=>$data,'xdata'=>$value['dta_ym']);  
					$right_lwg_max_min[]=$data;
				}
				$left_lwg_max_min[]=$value['dta_6'];
			}else if($value["dta_type"]=="SHQHDAY_99"){
				$rj_data1[]=array('ydata'=>$value['dta_6'],'xdata'=>$value['dta_ym']);
				$left_rj_max_min[]=$value['dta_6'];
				if(isset($rj_arr[$value['dta_ym']])){
					$data=round($rj_arr[$value['dta_ym']]-$value['dta_6']);
					$rj_data3[]=array('ydata'=>$data,'xdata'=>$value['dta_ym']);  
					$right_rj_max_min[]=$data;
				}
			}else if($value["dta_type"]=="SHQHDAY_9"){
				$jt_data1[]=array('ydata'=>$value['dta_6'],'xdata'=>$value['dta_ym']);
				$left_jt_max_min[]=$value['dta_6'];
				if(isset($jt_arr[$value['dta_ym']])){
					$data=round($jt_arr[$value['dta_ym']]/0.92-$value['dta_6']);
					$jt_data3[]=array('ydata'=>$data,'xdata'=>$value['dta_ym']);  
					$right_jt_max_min[]=$data;
				}
			}else if($value["dta_type"]=="SHQHDAY_20"){
				$tks_data1[]=array('ydata'=>$value['dta_6'],'xdata'=>$value['dta_ym']);
				$left_tks_max_min[]=$value['dta_6'];
				if(isset($tks_arr[$value['dta_ym']])){
					$data=round($tks_arr[$value['dta_ym']]/0.92-$value['dta_6']);
					$tks_data3[]=array('ydata'=>$data,'xdata'=>$value['dta_ym']);  
					$right_tks_max_min[]=$data;
				}
			}else if($value["dta_type"]=="SHQHDAY_19"){
				$ljm_data1[]=array('ydata'=>$value['dta_6'],'xdata'=>$value['dta_ym']);
				$left_ljm_max_min[]=$value['dta_6'];
				if(isset($ljm_arr[$value['dta_ym']])){
					$data=round($ljm_arr[$value['dta_ym']]-$value['dta_6']);
					$ljm_data3[]=array('ydata'=>$data,'xdata'=>$value['dta_ym']);  
					$right_ljm_max_min[]=$data;
				}
			}else if($value["dta_type"]=="SHQHDAY_22"){
				$gt_data1[]=array('ydata'=>$value['dta_6'],'xdata'=>$value['dta_ym']);
				$left_gt_max_min[]=$value['dta_6'];
				if(isset($gt_arr[$value['dta_ym']])){
					$data=round($gt_arr[$value['dta_ym']]-200+250-$value['dta_6']);
					$gt_data3[]=array('ydata'=>$data,'xdata'=>$value['dta_ym']);  
					$right_gt_max_min[]=$data;
				}
			}else if($value["dta_type"]=="SHQHDAY_23"){
				$gm_data1[]=array('ydata'=>$value['dta_6'],'xdata'=>$value['dta_ym']);
				$left_gm_max_min[]=$value['dta_6'];
				if(isset($gm_arr[$value['dta_ym']])){
					$data=round($gm_arr[$value['dta_ym']]-200+250-$value['dta_6']);
					$gm_data3[]=array('ydata'=>$data,'xdata'=>$value['dta_ym']);  
					$right_gm_max_min[]=$data;
				}
			}	
		}


		//螺纹钢
		if($left_lwg_max_min){
			$min=min($left_lwg_max_min);
			$max=max($left_lwg_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$left_lwg_infomax=$getmax_min_jg['max'];
			$left_lwg_infomin=$getmax_min_jg['min'];
			$left_lwg_infointerval=$getmax_min_jg['interval'];
		}else{
			$left_lwg_infomax='';
			$left_lwg_infomin='';
			$left_lwg_infointerval='';
		}

		if($right_lwg_max_min){
			$min=min($right_lwg_max_min);
			$max=max($right_lwg_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$right_lwg_infomax=$getmax_min_jg['max'];
			$right_lwg_infomin=$getmax_min_jg['min'];
			$right_lwg_infointerval=$getmax_min_jg['interval'];
		}else{
			$right_lwg_infomax='';
			$right_lwg_infomin='';
			$right_lwg_infointerval='';
		}

		$info['dataName']='上海螺纹钢 HRB400 Φ20-22mm';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$lwg_data1;
		$response['data']['Data1']['data'][] =$info;

		$info['dataName']='螺纹钢主力合约收盘价';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$lwg_data2;
		$response['data']['Data1']['data'][] =$info;


		$info['dataName']='上海螺纹钢期现基差';
		$info['dataType']='bar';
		$info['isLeft']=0;
		$info['dataValue']=$lwg_data3;
		$response['data']['Data1']['data'][] =$info;

		$response['data']['Data1']['data']=$this->upData2($response['data']['Data1']['data']);
		$response['data']['Data1']['leftmax']=$left_lwg_infomax;
		$response['data']['Data1']['leftmin']=$left_lwg_infomin;
		$response['data']['Data1']['leftinterval']=$left_lwg_infointerval;
		$response['data']['Data1']['rightmax']=$right_lwg_infomax;
		$response['data']['Data1']['rightmin']=$right_lwg_infomin;
		$response['data']['Data1']['rightinterval']=$right_lwg_infointerval;



		$sql3="select dt.dta_6,db.date from DataTableBaseInfo as db left join data_table as dt on db.id=dt.baseid  WHERE db.DataType='Java-SHQHBXG' AND dt.dta_maxValStatus=1 and db.date>='".$sdate."' and db.date<='".$edate."'   order by db.date asc ";
		$data_table3=$this->drcdao->query($sql3);

		foreach($data_table3 as $key=>$value){
			$bxg_data1[]=array('ydata'=>$value['dta_6'],'xdata'=>$value['date']);
			$left_bxg_max_min[]=$value['dta_6'];

			if(isset($bxg_arr[$value['date']])){
				$data=round($bxg_arr[$value['date']]+150-$value['dta_6']);
				$bxg_data3[]=array('ydata'=>$data,'xdata'=>$value['date']);  
				$right_bxg_max_min[]=$data;
			}
		}
		//不锈钢
		if($left_bxg_max_min){
			$min=min($left_bxg_max_min);
			$max=max($left_bxg_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$left_bxg_infomax=$getmax_min_jg['max'];
			$left_bxg_infomin=$getmax_min_jg['min'];
			$left_bxg_infointerval=$getmax_min_jg['interval'];
		}else{
			$left_bxg_infomax='';
			$left_bxg_infomin='';
			$left_bxg_infointerval='';
		}

		if($right_bxg_max_min){
			$min=min($right_bxg_max_min);
			$max=max($right_bxg_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$right_bxg_infomax=$getmax_min_jg['max'];
			$right_bxg_infomin=$getmax_min_jg['min'];
			$right_bxg_infointerval=$getmax_min_jg['interval'];
		}else{
			$right_bxg_infomax='';
			$right_bxg_infomin='';
			$right_bxg_infointerval='';
		}


		$info['dataName']='不锈钢主力合约收盘价';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$bxg_data1;
		$response['data']['Data2']['data'][] =$info;

		$info['dataName']='佛山 福建甬金304/2B 2.0*1219mm';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$bxg_data2;
		$response['data']['Data2']['data'][] =$info;


		$info['dataName']='佛山甬金不锈钢基差';
		$info['dataType']='bar';
		$info['isLeft']=0;
		$info['dataValue']=$bxg_data3;
		$response['data']['Data2']['data'][] =$info;

		$response['data']['Data2']['data']=$this->upData2($response['data']['Data2']['data']);
		$response['data']['Data2']['leftmax']=$left_bxg_infomax;
		$response['data']['Data2']['leftmin']=$left_bxg_infomin;
		$response['data']['Data2']['leftinterval']=$left_bxg_infointerval;
		$response['data']['Data2']['rightmax']=$right_bxg_infomax;
		$response['data']['Data2']['rightmin']=$right_bxg_infomin;
		$response['data']['Data2']['rightinterval']=$right_bxg_infointerval;


		//热卷
		if($left_rj_max_min){
			$min=min($left_rj_max_min);
			$max=max($left_rj_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$left_rj_infomax=$getmax_min_jg['max'];
			$left_rj_infomin=$getmax_min_jg['min'];
			$left_rj_infointerval=$getmax_min_jg['interval'];
		}else{
			$left_rj_infomax='';
			$left_rj_infomin='';
			$left_rj_infointerval='';
		}

		if($right_rj_max_min){
			$min=min($right_rj_max_min);
			$max=max($right_rj_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$right_rj_infomax=$getmax_min_jg['max'];
			$right_rj_infomin=$getmax_min_jg['min'];
			$right_rj_infointerval=$getmax_min_jg['interval'];
		}else{
			$right_rj_infomax='';
			$right_rj_infomin='';
			$right_rj_infointerval='';
		}

		$info['dataName']='热卷主力合约收盘价';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$rj_data1;
		$response['data']['Data3']['data'][] =$info;

		$info['dataName']='乐从热卷 Q235B 5.75mm*1500*C';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$rj_data2;
		$response['data']['Data3']['data'][] =$info;


		$info['dataName']='乐从热卷基差';
		$info['dataType']='bar';
		$info['isLeft']=0;
		$info['dataValue']=$rj_data3;
		$response['data']['Data3']['data'][] =$info;

		$response['data']['Data3']['data']=$this->upData2($response['data']['Data3']['data']);
		$response['data']['Data3']['leftmax']=$left_rj_infomax;
		$response['data']['Data3']['leftmin']=$left_rj_infomin;
		$response['data']['Data3']['leftinterval']=$left_rj_infointerval;
		$response['data']['Data3']['rightmax']=$right_rj_infomax;
		$response['data']['Data3']['rightmin']=$right_rj_infomin;
		$response['data']['Data3']['rightinterval']=$right_rj_infointerval;


		//铁矿石
		if($left_tks_max_min){
			$min=min($left_tks_max_min);
			$max=max($left_tks_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$left_tks_infomax=$getmax_min_jg['max'];
			$left_tks_infomin=$getmax_min_jg['min'];
			$left_tks_infointerval=$getmax_min_jg['interval'];
		}else{
			$left_tks_infomax='';
			$left_tks_infomin='';
			$left_tks_infointerval='';
		}

		if($right_tks_max_min){
			$min=min($right_tks_max_min);
			$max=max($right_tks_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$right_tks_infomax=$getmax_min_jg['max'];
			$right_tks_infomin=$getmax_min_jg['min'];
			$right_tks_infointerval=$getmax_min_jg['interval'];
		}else{
			$right_tks_infomax='';
			$right_tks_infomin='';
			$right_tks_infointerval='';
		}

		$info['dataName']='铁矿石主力合约收盘价';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$tks_data1;
		$response['data']['Data4']['data'][] =$info;

		$info['dataName']='青岛港PB粉矿Fe:61.5%';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$tks_data2;
		$response['data']['Data4']['data'][] =$info;


		$info['dataName']='青岛港PB粉基差';
		$info['dataType']='bar';
		$info['isLeft']=0;
		$info['dataValue']=$tks_data3;
		$response['data']['Data4']['data'][] =$info;

		$response['data']['Data4']['data']=$this->upData2($response['data']['Data4']['data']);
		$response['data']['Data4']['leftmax']=$left_tks_infomax;
		$response['data']['Data4']['leftmin']=$left_tks_infomin;
		$response['data']['Data4']['leftinterval']=$left_tks_infointerval;
		$response['data']['Data4']['rightmax']=$right_tks_infomax;
		$response['data']['Data4']['rightmin']=$right_tks_infomin;
		$response['data']['Data4']['rightinterval']=$right_tks_infointerval;


		//焦炭
		if($left_jt_max_min){
			$min=min($left_jt_max_min);
			$max=max($left_jt_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$left_jt_infomax=$getmax_min_jg['max'];
			$left_jt_infomin=$getmax_min_jg['min'];
			$left_jt_infointerval=$getmax_min_jg['interval'];
		}else{
			$left_jt_infomax='';
			$left_jt_infomin='';
			$left_jt_infointerval='';
		}

		if($right_jt_max_min){
			$min=min($right_jt_max_min);
			$max=max($right_jt_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$right_jt_infomax=$getmax_min_jg['max'];
			$right_jt_infomin=$getmax_min_jg['min'];
			$right_jt_infointerval=$getmax_min_jg['interval'];
		}else{
			$right_jt_infomax='';
			$right_jt_infomin='';
			$right_jt_infointerval='';
		}

		$info['dataName']='焦炭主力合约收盘价';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$jt_data1;
		$response['data']['Data5']['data'][] =$info;

		$info['dataName']='吕梁准一级焦';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$jt_data2;
		$response['data']['Data5']['data'][] =$info;


		$info['dataName']='吕梁准一级焦基差';
		$info['dataType']='bar';
		$info['isLeft']=0;
		$info['dataValue']=$jt_data3;
		$response['data']['Data5']['data'][] =$info;

		$response['data']['Data5']['data']=$this->upData2($response['data']['Data5']['data']);
		$response['data']['Data5']['leftmax']=$left_jt_infomax;
		$response['data']['Data5']['leftmin']=$left_jt_infomin;
		$response['data']['Data5']['leftinterval']=$left_jt_infointerval;
		$response['data']['Data5']['rightmax']=$right_jt_infomax;
		$response['data']['Data5']['rightmin']=$right_jt_infomin;
		$response['data']['Data5']['rightinterval']=$right_jt_infointerval;




		//炼焦煤
		if($left_ljm_max_min){
			$min=min($left_ljm_max_min);
			$max=max($left_ljm_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$left_ljm_infomax=$getmax_min_jg['max'];
			$left_ljm_infomin=$getmax_min_jg['min'];
			$left_ljm_infointerval=$getmax_min_jg['interval'];
		}else{
			$left_ljm_infomax='';
			$left_ljm_infomin='';
			$left_ljm_infointerval='';
		}

		if($right_ljm_max_min){
			$min=min($right_ljm_max_min);
			$max=max($right_ljm_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$right_ljm_infomax=$getmax_min_jg['max'];
			$right_ljm_infomin=$getmax_min_jg['min'];
			$right_ljm_infointerval=$getmax_min_jg['interval'];
		}else{
			$right_ljm_infomax='';
			$right_ljm_infomin='';
			$right_ljm_infointerval='';
		}

		$info['dataName']='焦煤主力合约收盘价';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$ljm_data1;
		$response['data']['Data6']['data'][] =$info;

		$info['dataName']='吕梁焦煤';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$ljm_data2;
		$response['data']['Data6']['data'][] =$info;


		$info['dataName']='吕梁焦煤基差';
		$info['dataType']='bar';
		$info['isLeft']=0;
		$info['dataValue']=$ljm_data3;
		$response['data']['Data6']['data'][] =$info;

		$response['data']['Data6']['data']=$this->upData2($response['data']['Data6']['data']);
		$response['data']['Data6']['leftmax']=$left_ljm_infomax;
		$response['data']['Data6']['leftmin']=$left_ljm_infomin;
		$response['data']['Data6']['leftinterval']=$left_ljm_infointerval;
		$response['data']['Data6']['rightmax']=$right_ljm_infomax;
		$response['data']['Data6']['rightmin']=$right_ljm_infomin;
		$response['data']['Data6']['rightinterval']=$right_ljm_infointerval;



		//硅铁
		if($left_gt_max_min){
			$min=min($left_gt_max_min);
			$max=max($left_gt_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$left_gt_infomax=$getmax_min_jg['max'];
			$left_gt_infomin=$getmax_min_jg['min'];
			$left_gt_infointerval=$getmax_min_jg['interval'];
		}else{
			$left_gt_infomax='';
			$left_gt_infomin='';
			$left_gt_infointerval='';
		}

		if($right_gt_max_min){
			$min=min($right_gt_max_min);
			$max=max($right_gt_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$right_gt_infomax=$getmax_min_jg['max'];
			$right_gt_infomin=$getmax_min_jg['min'];
			$right_gt_infointerval=$getmax_min_jg['interval'];
		}else{
			$right_gt_infomax='';
			$right_gt_infomin='';
			$right_gt_infointerval='';
		}

		$info['dataName']='硅铁主力合约收盘价';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$gt_data1;
		$response['data']['Data7']['data'][] =$info;

		$info['dataName']='内蒙包头FeSi75-B加工块';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$gt_data2;
		$response['data']['Data7']['data'][] =$info;


		$info['dataName']='内蒙包头硅铁基差';
		$info['dataType']='bar';
		$info['isLeft']=0;
		$info['dataValue']=$gt_data3;
		$response['data']['Data7']['data'][] =$info;

		$response['data']['Data7']['data']=$this->upData2($response['data']['Data7']['data']);
		$response['data']['Data7']['leftmax']=$left_gt_infomax;
		$response['data']['Data7']['leftmin']=$left_gt_infomin;
		$response['data']['Data7']['leftinterval']=$left_gt_infointerval;
		$response['data']['Data7']['rightmax']=$right_gt_infomax;
		$response['data']['Data7']['rightmin']=$right_gt_infomin;
		$response['data']['Data7']['rightinterval']=$right_gt_infointerval;


		//硅锰
		if($left_gm_max_min){
			$min=min($left_gm_max_min);
			$max=max($left_gm_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$left_gm_infomax=$getmax_min_jg['max'];
			$left_gm_infomin=$getmax_min_jg['min'];
			$left_gm_infointerval=$getmax_min_jg['interval'];
		}else{
			$left_gm_infomax='';
			$left_gm_infomin='';
			$left_gm_infointerval='';
		}

		if($right_gm_max_min){
			$min=min($right_gm_max_min);
			$max=max($right_gm_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$right_gm_infomax=$getmax_min_jg['max'];
			$right_gm_infomin=$getmax_min_jg['min'];
			$right_gm_infointerval=$getmax_min_jg['interval'];
		}else{
			$right_gm_infomax='';
			$right_gm_infomin='';
			$right_gm_infointerval='';
		}

		$info['dataName']='硅锰主力合约收盘价';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$gm_data1;
		$response['data']['Data8']['data'][] =$info;

		$info['dataName']='贵州硅锰';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$gm_data2;
		$response['data']['Data8']['data'][] =$info;


		$info['dataName']='贵州硅锰基差';
		$info['dataType']='bar';
		$info['isLeft']=0;
		$info['dataValue']=$gm_data3;
		$response['data']['Data8']['data'][] =$info;

		$response['data']['Data8']['data']=$this->upData2($response['data']['Data8']['data']);
		$response['data']['Data8']['leftmax']=$left_gm_infomax;
		$response['data']['Data8']['leftmin']=$left_gm_infomin;
		$response['data']['Data8']['leftinterval']=$left_gm_infointerval;
		$response['data']['Data8']['rightmax']=$right_gm_infomax;
		$response['data']['Data8']['rightmin']=$right_gm_infomin;
		$response['data']['Data8']['rightinterval']=$right_gm_infointerval;
		//echo '<pre>';
		//print_r($bxg_data2);
		//print_r($bxg_data3);

		$response['Success'] = 1;
		$response['Message']='获取成功';
		$response['timestamp'] =time();
		$response['data']['date'] =$edate;
		$response['data']['datemonth'] =date('n',strtotime($edate));
		$response['data']['dateday'] =date('j',strtotime($edate));

		ob_clean();
		echo $this->pri_JSON($response);
	}



	public function getScreenData6($params){
		$edate = $params['date'];
		if(empty($edate))
		{
			$edate=date("Y-m-d",strtotime("-1 day"));
		}

		if (_isholiday($edate)) {				
			$coil = 1;
			while (true) {			
				$day_temp = date("Y-m-d", time() -  $coil * 24 * 3600);
				//echo "<br>".$day_temp;
				if (!_isholiday($day_temp)) {
					break;
				}
				$coil++;					
				//echo "<br>coil=".$coil;
			}
			$edate = date("Y-m-d",strtotime($edate) - $coil * 24 * 3600); 
		
		}

        $sdate=date('Y-m-d',strtotime("-1 year",strtotime($edate)));



		//1、河北唐山方坯成本毛利走势图    唐山普碳坯成本     宏兴方坯Q235价格    唐山方坯毛利
		$sql1="select chenben_tax, ndate,type from sg_HangYeChengBenIndex where  type in (10,5,44,14,45,163) and mc_type=0 and ndate>='".$sdate."' and ndate<='".$edate."'   order by ndate asc ";
		$sg_HangYeChengBenIndex=$this->drcdao->query($sql1);
		// echo $sql1;
		// echo '<pre>';
		// print_r($sg_HangYeChengBenIndex);

		foreach($sg_HangYeChengBenIndex as $key=>$value){
			if($value["type"]==10){
				$fp_data1[]=array('ydata'=>$value['chenben_tax'],'xdata'=>$value['ndate']);
				$fp_arr[$value['ndate']]=$value['chenben_tax'];
				$left_fp_max_min[]=$value['chenben_tax'];
			}else if($value["type"]==5){
				$ts_data1[]=array('ydata'=>$value['chenben_tax'],'xdata'=>$value['ndate']);
				$ts_max_min[]=$value['chenben_tax'];
			}else if($value["type"]==45){
				$gl_data2[]=array('ydata'=>$value['chenben_tax'],'xdata'=>$value['ndate']);
				$gl_arr[$value['ndate']]=$value['chenben_tax'];
				$left_gl_max_min[]=$value['chenben_tax'];
			}else if($value["type"]==163){
				$dl_data2[]=array('ydata'=>$value['chenben_tax'],'xdata'=>$value['ndate']);
				$dl_arr[$value['ndate']]=$value['chenben_tax'];
				$left_dl_max_min[]=$value['chenben_tax'];
			}else if($value["type"]==44){
				$rj_data2[]=array('ydata'=>$value['chenben_tax'],'xdata'=>$value['ndate']);
				$rj_arr[$value['ndate']]=$value['chenben_tax'];
				$left_rj_max_min[]=$value['chenben_tax'];
			}else if($value["type"]==14){
				$dg_data1[]=array('ydata'=>$value['chenben_tax'],'xdata'=>$value['ndate']);
				$dg_max_min[]=$value['chenben_tax'];
			}
		}




		$sql2="select price, mconmanagedate,topicture from marketconditions  where topicture in('072023','073112')  and mconmanagedate>= '".$sdate." 00:00:00' and mconmanagedate<= '".$edate." 23:59:59' order by mconmanagedate asc";
		$marketconditions=$this->_dao->query($sql2);

		foreach($marketconditions as $key=>$value){
			$ddate=date('Y-m-d',strtotime($value['mconmanagedate']));
			if($value["topicture"]=="072023"){
				$gl_data1[]=array('ydata'=>$value['price'],'xdata'=>$ddate);
				$dl_data1[]=array('ydata'=>$value['price'],'xdata'=>$ddate);

				if(isset($gl_arr[$ddate])){
					$data=round($value['price']-$gl_arr[$ddate]);
					$gl_data3[]=array('ydata'=>$data,'xdata'=>$ddate);  
					$right_gl_max_min[]=$data;
				}
				if(isset($dl_arr[$ddate])){
					$data=round($value['price']-$dl_arr[$ddate]);
					$dl_data3[]=array('ydata'=>$data,'xdata'=>$ddate);  
					$right_dl_max_min[]=$data;
				}
				$left_gl_max_min[]=$value['price'];
				$left_dl_max_min[]=$value['price'];
			}else if($value["topicture"]=="073112"){
				$rj_data1[]=array('ydata'=>$value['price'],'xdata'=>$ddate);
				if(isset($rj_arr[$ddate])){
					$data=round($value['price']-$rj_arr[$ddate]);
					$rj_data3[]=array('ydata'=>$data,'xdata'=>$ddate);  
					$right_rj_max_min[]=$data;
				}
				$left_rj_max_min[]=$value['price'];
			}
		}


		$sql3="select price, mconmanagedate from marketconditions  where mastertopid='6784118' and mconmanagedate>= '".$sdate." 00:00:00' and mconmanagedate<= '".$edate." 23:59:59' order by mconmanagedate asc";
		$marketconditions3=$this->_dao->query($sql3);

		foreach($marketconditions3 as $key=>$value){
			$ddate=date('Y-m-d',strtotime($value['mconmanagedate']));
			$fp_data2[]=array('ydata'=>$value['price'],'xdata'=>$ddate);
			if(isset($fp_arr[$ddate])){
				$data=round($value['price']-$fp_arr[$ddate]);
				$fp_data3[]=array('ydata'=>$data,'xdata'=>$ddate);  
				$right_fp_max_min[]=$data;
			}
			$left_fp_max_min[]=$value['price'];
		}


		/*$sql4="select aveprice, dateday from shpi_pzggp where svm_vid=6011 and dateday>= '".$sdate."' and dateday<= '".$edate."' order by dateday asc";
		$shpi_pzggp=$this->_dao->query($sql4);

		foreach($shpi_pzggp as $key=>$value){
			$dg_data2[]=array('ydata'=>$value['aveprice'],'xdata'=>$value['dateday']);
			if(isset($dg_arr[$value['dateday']])){
				$data=round($value['aveprice']-$dg_arr[$value['dateday']]);
				$dg_data3[]=array('ydata'=>$data,'xdata'=>$value['dateday']);  
				$right_dg_max_min[]=$data;
			}
			$left_dg_max_min[]=$value['aveprice'];
		}*/




		$sdate2=date('Y-m-d',strtotime("-2 year",strtotime($edate)));
		$sql5="select DATE,D1,D2 from L2Data where L2id=200 and DATE>= '".$sdate2."' and DATE<= '".$edate."' order by DATE asc";
		$L2Data=$this->_dao->query($sql5);
		foreach($L2Data as $key=>$value){
			$gq_data1[]=array('ydata'=>$value['D1'],'xdata'=>$value['DATE']);
			$gq_data2[]=array('ydata'=>$value['D2'],'xdata'=>$value['DATE']);
			$left_gq_max_min[]=$value['D1'];
			$right_gq_max_min[]=$value['D2'];
		}




		$sql6="select  avg,date from (SELECT CAST( AVG( China ) AS DECIMAL( 10, 2 ) ) AS avg,date FROM  `JiaoQiLiLunDunLiRun`  GROUP BY date ) AS tab where  date>= '".$sdate."' and date<= '".$edate."' order by date asc";
		$JiaoQiLiLunDunLiRun=$this->drcdao->query($sql6);
		foreach($JiaoQiLiLunDunLiRun as $key=>$value){
			$jq_data1[]=array('ydata'=>$value['avg'],'xdata'=>$value['date']);
			$jq_max_min[]=$value['avg'];
		}


		
		$sql7="select  avg,date from (SELECT cast(avg(China) as  decimal(10,2)) as avg,date FROM `JiaoQiLiLunDunLiRun` group by date_format(date,'%Y%u')) as tab where  date>= '".$sdate."' and date<= '".$edate."' order by date asc";
		$JiaoQiLiLunDunLiRun2=$this->drcdao->query($sql7);
		foreach($JiaoQiLiLunDunLiRun2 as $key=>$value){
			$jq_data2[]=array('ydata'=>$value['avg'],'xdata'=>$value['date']);
			$jq_max_min[]=$value['avg'];
		}
		
		$sql8="select  avg,date from (SELECT CAST( AVG( China ) AS DECIMAL( 10, 2 ) ) AS avg, DATE_FORMAT( DATE,  '%Y-%m' ) AS date FROM  `JiaoQiLiLunDunLiRun`  GROUP BY date ) AS tab where  date>= '".$sdate."' and date<= '".$edate."' order by date asc";
		$JiaoQiLiLunDunLiRun3=$this->drcdao->query($sql8);
		foreach($JiaoQiLiLunDunLiRun3 as $key=>$value){
			$jq_data3[]=array('ydata'=>$value['avg'],'xdata'=>$value['date']."-01");
			$jq_max_min[]=$value['avg'];
		}


		
		//高炉
		if($left_gl_max_min){
			$min=min($left_gl_max_min);
			$max=max($left_gl_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$left_gl_infomax=$getmax_min_jg['max'];
			$left_gl_infomin=$getmax_min_jg['min'];
			$left_gl_infointerval=$getmax_min_jg['interval'];
		}else{
			$left_gl_infomax='';
			$left_gl_infomin='';
			$left_gl_infointerval='';
		}

		if($right_gl_max_min){
			$min=min($right_gl_max_min);
			$max=max($right_gl_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$right_gl_infomax=$getmax_min_jg['max'];
			$right_gl_infomin=$getmax_min_jg['min'];
			$right_gl_infointerval=$getmax_min_jg['interval'];
		}else{
			$right_gl_infomax='';
			$right_gl_infomin='';
			$right_gl_infointerval='';
		}

		$info['dataName']='上海螺纹钢 HRB400 Φ20-22mm';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$gl_data1;
		$response['data']['Data1']['data'][] =$info;

		$info['dataName']='江苏沿江螺纹钢成本';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$gl_data2;
		$response['data']['Data1']['data'][] =$info;


		$info['dataName']='江苏沿江长流程钢厂螺纹钢毛利';
		$info['dataType']='bar';
		$info['isLeft']=0;
		$info['dataValue']=$gl_data3;
		$response['data']['Data1']['data'][] =$info;

		//$response['data']['Data3']['data']=$this->upData2($response['data']['Data3']['data']);
		$response['data']['Data1']['leftmax']=$left_gl_infomax;
		$response['data']['Data1']['leftmin']=$left_gl_infomin;
		$response['data']['Data1']['leftinterval']=$left_gl_infointerval;
		$response['data']['Data1']['rightmax']=$right_gl_infomax;
		$response['data']['Data1']['rightmin']=$right_gl_infomin;
		$response['data']['Data1']['rightinterval']=$right_gl_infointerval;
		


		//带钢
		if($dg_max_min){
			$min=min($dg_max_min);
			$max=max($dg_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$dg_infomax=$getmax_min_jg['max'];
			$dg_infomin=$getmax_min_jg['min'];
			$dg_infointerval=$getmax_min_jg['interval'];
		}else{
			$dg_infomax='';
			$dg_infomin='';
			$dg_infointerval='';
		}

		$info['dataName']='唐山带钢成本';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$dg_data1;
		$response['data']['Data2']['data'][] =$info;

		//$response['data']['Data2']['data']=$this->upData2($response['data']['Data2']['data']);
		$response['data']['Data2']['max']=$dg_infomax;
		$response['data']['Data2']['min']=$dg_infomin;
		$response['data']['Data2']['interval']=$dg_infointerval;
		

		/*if($left_dg_max_min){
			$min=min($left_dg_max_min);
			$max=max($left_dg_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$left_dg_infomax=$getmax_min_jg['max'];
			$left_dg_infomin=$getmax_min_jg['min'];
			$left_dg_infointerval=$getmax_min_jg['interval'];
		}else{
			$left_dg_infomax='';
			$left_dg_infomin='';
			$left_dg_infointerval='';
		}

		if($right_dg_max_min){
			$min=min($right_dg_max_min);
			$max=max($right_dg_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$right_dg_infomax=$getmax_min_jg['max'];
			$right_dg_infomin=$getmax_min_jg['min'];
			$right_dg_infointerval=$getmax_min_jg['interval'];
		}else{
			$right_dg_infomax='';
			$right_dg_infomin='';
			$right_dg_infointerval='';
		}

		$info['dataName']='唐山带钢成本';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$dg_data1;
		$response['data']['Data2']['data'][] =$info;

		$info['dataName']='带钢2.75*235mm价格';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$dg_data2;
		$response['data']['Data2']['data'][] =$info;


		$info['dataName']='带钢毛利';
		$info['dataType']='bar';
		$info['isLeft']=0;
		$info['dataValue']=$dg_data3;
		$response['data']['Data2']['data'][] =$info;

		//$response['data']['Data2']['data']=$this->upData2($response['data']['Data2']['data']);
		$response['data']['Data2']['leftmax']=$left_dg_infomax;
		$response['data']['Data2']['leftmin']=$left_dg_infomin;
		$response['data']['Data2']['leftinterval']=$left_dg_infointerval;
		$response['data']['Data2']['rightmax']=$right_dg_infomax;
		$response['data']['Data2']['rightmin']=$right_dg_infomin;
		$response['data']['Data2']['rightinterval']=$right_dg_infointerval;*/





		//电炉
		if($left_dl_max_min){
			$min=min($left_dl_max_min);
			$max=max($left_dl_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$left_dl_infomax=$getmax_min_jg['max'];
			$left_dl_infomin=$getmax_min_jg['min'];
			$left_dl_infointerval=$getmax_min_jg['interval'];
		}else{
			$left_dl_infomax='';
			$left_dl_infomin='';
			$left_dl_infointerval='';
		}

		if($right_dl_max_min){
			$min=min($right_dl_max_min);
			$max=max($right_dl_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$right_dl_infomax=$getmax_min_jg['max'];
			$right_dl_infomin=$getmax_min_jg['min'];
			$right_dl_infointerval=$getmax_min_jg['interval'];
		}else{
			$right_dl_infomax='';
			$right_dl_infomin='';
			$right_dl_infointerval='';
		}

		$info['dataName']='上海螺纹钢 HRB400 Φ20-22mm';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$dl_data1;
		$response['data']['Data3']['data'][] =$info;

		$info['dataName']='江苏电炉螺纹钢成本';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$dl_data2;
		$response['data']['Data3']['data'][] =$info;


		$info['dataName']='江苏沿江电炉螺纹钢毛利';
		$info['dataType']='bar';
		$info['isLeft']=0;
		$info['dataValue']=$dl_data3;
		$response['data']['Data3']['data'][] =$info;

		//$response['data']['Data4']['data']=$this->upData2($response['data']['Data4']['data']);
		$response['data']['Data3']['leftmax']=$left_dl_infomax;
		$response['data']['Data3']['leftmin']=$left_dl_infomin;
		$response['data']['Data3']['leftinterval']=$left_dl_infointerval;
		$response['data']['Data3']['rightmax']=$right_dl_infomax;
		$response['data']['Data3']['rightmin']=$right_dl_infomin;
		$response['data']['Data3']['rightinterval']=$right_dl_infointerval;


		//铁水
		if($ts_max_min){
			$min=min($ts_max_min);
			$max=max($ts_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$ts_infomax=$getmax_min_jg['max'];
			$ts_infomin=$getmax_min_jg['min'];
			$ts_infointerval=$getmax_min_jg['interval'];
		}else{
			$ts_infomax='';
			$ts_infomin='';
			$ts_infointerval='';
		}


		$info['dataName']='唐山铁水成本';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$ts_data1;
		$response['data']['Data4']['data'][] =$info;
		$response['data']['Data4']['max']=$ts_infomax;
		$response['data']['Data4']['min']=$ts_infomin;
		$response['data']['Data4']['interval']=$ts_infointerval;




		//热卷
		if($left_rj_max_min){
			$min=min($left_rj_max_min);
			$max=max($left_rj_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$left_rj_infomax=$getmax_min_jg['max'];
			$left_rj_infomin=$getmax_min_jg['min'];
			$left_rj_infointerval=$getmax_min_jg['interval'];
		}else{
			$left_rj_infomax='';
			$left_rj_infomin='';
			$left_rj_infointerval='';
		}

		if($right_rj_max_min){
			$min=min($right_rj_max_min);
			$max=max($right_rj_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$right_rj_infomax=$getmax_min_jg['max'];
			$right_rj_infomin=$getmax_min_jg['min'];
			$right_rj_infointerval=$getmax_min_jg['interval'];
		}else{
			$right_rj_infomax='';
			$right_rj_infomin='';
			$right_rj_infointerval='';
		}

		$info['dataName']='上海热卷 Q235B 5.75mm*1500*C';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$rj_data1;
		$response['data']['Data5']['data'][] =$info;

		$info['dataName']='江苏沿江钢厂热卷成本';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$rj_data2;
		$response['data']['Data5']['data'][] =$info;


		$info['dataName']='江苏沿江钢厂热卷毛利';
		$info['dataType']='bar';
		$info['isLeft']=0;
		$info['dataValue']=$rj_data3;
		$response['data']['Data5']['data'][] =$info;

		$response['data']['Data5']['data']=$this->upData2($response['data']['Data5']['data']);
		$response['data']['Data5']['leftmax']=$left_rj_infomax;
		$response['data']['Data5']['leftmin']=$left_rj_infomin;
		$response['data']['Data5']['leftinterval']=$left_rj_infointerval;
		$response['data']['Data5']['rightmax']=$right_rj_infomax;
		$response['data']['Data5']['rightmin']=$right_rj_infomin;
		$response['data']['Data5']['rightinterval']=$right_rj_infointerval;


		//方坯
		if($left_fp_max_min){
			$min=min($left_fp_max_min);
			$max=max($left_fp_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$left_fp_infomax=$getmax_min_jg['max'];
			$left_fp_infomin=$getmax_min_jg['min'];
			$left_fp_infointerval=$getmax_min_jg['interval'];
		}else{
			$left_fp_infomax='';
			$left_fp_infomin='';
			$left_fp_infointerval='';
		}

		if($right_fp_max_min){
			$min=min($right_fp_max_min);
			$max=max($right_fp_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$right_fp_infomax=$getmax_min_jg['max'];
			$right_fp_infomin=$getmax_min_jg['min'];
			$right_fp_infointerval=$getmax_min_jg['interval'];
		}else{
			$right_fp_infomax='';
			$right_fp_infomin='';
			$right_fp_infointerval='';
		}

		$info['dataName']='唐山普碳坯成本';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$fp_data1;
		$response['data']['Data6']['data'][] =$info;

		$info['dataName']='宏兴方坯Q235价格';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$fp_data2;
		$response['data']['Data6']['data'][] =$info;


		$info['dataName']='唐山方坯毛利';
		$info['dataType']='bar';
		$info['isLeft']=0;
		$info['dataValue']=$fp_data3;
		$response['data']['Data6']['data'][] =$info;

		$response['data']['Data6']['data']=$this->upData2($response['data']['Data6']['data']);
		$response['data']['Data6']['leftmax']=$left_fp_infomax;
		$response['data']['Data6']['leftmin']=$left_fp_infomin;
		$response['data']['Data6']['leftinterval']=$left_fp_infointerval;
		$response['data']['Data6']['rightmax']=$right_fp_infomax;
		$response['data']['Data6']['rightmin']=$right_fp_infomin;
		$response['data']['Data6']['rightinterval']=$right_fp_infointerval;


		

		//重点钢铁企业
		if($left_gq_max_min){
			$min=min($left_gq_max_min);
			$max=max($left_gq_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$left_gq_infomax=$getmax_min_jg['max'];
			$left_gq_infomin=$getmax_min_jg['min'];
			$left_gq_infointerval=$getmax_min_jg['interval'];
		}else{
			$left_gq_infomax='';
			$left_gq_infomin='';
			$left_gq_infointerval='';
		}

		if($right_gq_max_min){
			$min=min($right_gq_max_min);
			$max=max($right_gq_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$right_gq_infomax=$getmax_min_jg['max'];
			$right_gq_infomin=$getmax_min_jg['min'];
			$right_gq_infointerval=$getmax_min_jg['interval'];
		}else{
			$right_gq_infomax='';
			$right_gq_infomin='';
			$right_gq_infointerval='';
		}

		$info['dataName']='利润总额';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$gq_data1;
		$response['data']['Data7']['data'][] =$info;

		$info['dataName']='销售利润率';
		$info['dataType']='bar';
		$info['isLeft']=0;
		$info['dataValue']=$gq_data2;
		$response['data']['Data7']['data'][] =$info;

		$response['data']['Data7']['leftmax']=$left_gq_infomax;
		$response['data']['Data7']['leftmin']=$left_gq_infomin;
		$response['data']['Data7']['leftinterval']=$left_gq_infointerval;
		$response['data']['Data7']['rightmax']=$right_gq_infomax;
		$response['data']['Data7']['rightmin']=$right_gq_infomin;
		$response['data']['Data7']['rightinterval']=$right_gq_infointerval;
		


		//重点焦企
		if($jq_max_min){
			$min=min($jq_max_min);
			$max=max($jq_max_min);
			$getmax_min_jg=$this->getmax_min_jg($min,$max);
			$jq_infomax=$getmax_min_jg['max'];
			$jq_infomin=$getmax_min_jg['min'];
			$jq_infointerval=$getmax_min_jg['interval'];
		}else{
			$jq_infomax='';
			$jq_infomin='';
			$jq_infointerval='';
		}

		$info['dataName']='全国焦企吨焦利润 （日均值）';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$jq_data1;
		$response['data']['Data8']['data'][] =$info;

		$info['dataName']='全国焦企吨焦利润 （周均值）';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$jq_data2;
		$response['data']['Data8']['data'][] =$info;


		$info['dataName']='全国焦企吨焦利润 （月均值）';
		$info['dataType']='line';
		$info['isLeft']=1;
		$info['dataValue']=$jq_data3;
		$response['data']['Data8']['data'][] =$info;

	

		$response['data']['Data8']['data']=$this->upData2($response['data']['Data8']['data']);
		$response['data']['Data8']['max']=$jq_infomax;
		$response['data']['Data8']['min']=$jq_infomin;
		$response['data']['Data8']['interval']=$jq_infointerval;


		$response['Success'] = 1;
		$response['Message']='获取成功';
		$response['timestamp'] =time();
		$response['data']['date'] =$edate;
		$response['data']['datemonth'] =date('n',strtotime($edate));
		$response['data']['dateday'] =date('j',strtotime($edate));
		//echo '<pre>';
		//print_r($marketconditions);

		ob_clean();
		echo $this->pri_JSON($response);



	}





	
	public function arraySort($arr, $keys, $type = 'asc')
	{
		$keysvalue = $new_array = array();
		foreach ($arr as $k => $v) {
			$keysvalue[$k] = $v[$keys];
		}
		
		
		if ($type == 'asc') {
			natsort($keysvalue);
		}
		if ($type == 'desc') {
			asort($keysvalue);
			$keysvalue = array_reverse($keysvalue, TRUE); // 将原数组中的元素顺序翻转,如果第二个参数指定为 true，则元素的键名保持不变
		}
		
		$i=1;

		foreach ($keysvalue as $k => $v) {

			//$new_array[$k] = $arr[$k];

			$new_array1[$k] = $i;
			$i++;
		}
		return $new_array1;
	}

	public function getmax_min_jg($mina,$maxa)
	{
		       $min=$mina;
				//$jg=$this->GetkdbyMaxandMin($maxa,$mina);
				
				$PRECION = 0.000001;
				$X = ($maxa-$mina)/8;	
				if($X < $PRECION)
				{
					$absMax = abs($maxa);
					if($absMax > $PRECION)
					{
						$n = log($absMax, 10);
						$n = floor($n);
						$p = pow(10, $n - 1);
						$maxa = ceil(($maxa + 1)/ $p) * $p;
						$mina = floor(($mina - 1) / $p) * $p;
					}
					else
					{
						$maxa = 4;
						$mina = -4;
					}
					$X = ($maxa - $mina) / 8;
				}
				
				
				
				
				if($X <=1)
				{
					$n = log($X, 10);
					$n = -floor($n);
					$p = pow(10, $n + 1);   //2个零时，乘以1000，保证XX.X样式。
					$delta = ceil($X * $p) / $p;
				}
				else
				{
					$n = log($X, 10);
					$n = floor($n);
					$p = pow(10, $n - 1);   //4位整数时，除以100，保证XX.X样式。
					$delta = ceil($X / $p ) *$p;
				}
				$jg=$delta;
				$mina=$this->GetMinSZ($jg,$mina);
				

			 
				if($mina<0&&$min>=0&&isset($min))
				{
					$mina=0;
				}
			$maxa=$mina+$jg*10;
			if(is_nan($jg)&&$min==0){ $maxa='null';$mina='null';}

            $num=10;
			$xsd1=explode('.',$jg);
			if(count($xsd1)<=1)
			{
				$xs1=0;
			}
			else
			{
				//$xs=abs($xsd);
				$xs1=strlen($xsd1[1]);
			}
			//echo "min ".$maxa;
			
			$ykd=6;
			$ymin=$mina==="null"?"":$mina;
			$ymax=$maxa==="null"?"":$maxa;
		    $yinterval=round(($ymax-$mina)/$ykd,$xs1);
			 
			 
			 
			$max1= $mina+$yinterval*$ykd;
			
			if($maxa!=$max1)
			{
				$maxa=round($max1,$xs1);
			}
			
			//xiangbin add 20180705 end 
			$minmax['min']=$mina;
			$minmax['max']=$maxa;
			$minmax['interval']=$yinterval;
			
			
			
			return $minmax;
	  }
	  
	  public function GetMinSZ($delta,$Min)
	{
		 $n = log($delta, 10);
		$n = floor($n);

		if($n <= 0)   //delta <= 1
		{
			$jn = -$n + 1;    // delta有2个0时，乘以1000
			$p = pow(10, $jn);
			$newMin = floor($Min * $p)/$p;
		}
		else      // delta > 1
		{
			$jn = $n-1;  // delta为3位整数时，除以10
			$p = pow(10, $jn);
			$newMin = floor($Min / $p) * $p;
		}

		//if($newMin >= $Min)
		//{
			$newMin -= $delta;
		//}
		return  $newMin; 
	}

	function request_post($url = '', $param = '') {
        if (empty($url) || empty($param)) {
            return false;
        }
        
        $postUrl = $url;
        $curlPost = $param;
        $ch = curl_init();//初始化curl
        curl_setopt($ch, CURLOPT_URL,$postUrl);//抓取指定网页
        curl_setopt($ch, CURLOPT_HEADER, 0);//设置header
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);//要求结果为字符串且输出到屏幕上
        curl_setopt($ch, CURLOPT_POST, 1);//post提交方式
        curl_setopt($ch, CURLOPT_POSTFIELDS, $curlPost);
        $data = curl_exec($ch);//运行curl
        curl_close($ch);
        
        return $data;
    }
    function getip(){//获取单个ip
	//获取真实ip 开始
	$ip=false;
	if(!empty($_SERVER["HTTP_CLIENT_IP"])){
		$ip = $_SERVER["HTTP_CLIENT_IP"];
	}
	if (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
		$ips = explode (", ", $_SERVER['HTTP_X_FORWARDED_FOR']);
		if ($ip) { array_unshift($ips, $ip); $ip = FALSE; }
		for ($i = 0; $i < count($ips); $i++) {
			if (!preg_match("/^(10│172.16│192.168)./i", $ips[$i])) {
				$ip = $ips[$i];
				break;
			}
		}
	}
	$ipaddress= $ip ? $ip : $_SERVER['REMOTE_ADDR'];
	//获取真实ip 结束
	return $ipaddress;
}
/**************************************************************
     *
     *  使用特定function对数组中所有元素做处理
     *  @param  string  &$array     要处理的字符串
     *  @param  string  $function   要执行的函数
     *  @return boolean $apply_to_keys_also     是否也应用到key上
     *  @access private
     *
     *************************************************************/
    private function pri_arrayRecursive(&$array, $function, $apply_to_keys_also = false)
    {
        static $recursive_counter = 0;
        if (++$recursive_counter > 1000) {
            die('possible deep recursion attack');
        }
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $this->pri_arrayRecursive($array[$key], $function, $apply_to_keys_also);
            } else {
                $array[$key] = $function($value);
                //$array[$key] = $function(addslashes($value));
            }
            if ($apply_to_keys_also && is_string($key)) {
                $new_key = $function($key);
                if ($new_key != $key) {
                    $array[$new_key] = $array[$key];
                    unset($array[$key]);
                }
            }
        }
        $recursive_counter--;
    }
    
    //****************************************
    //
    //            JSON打包 start 将数组从gbk转为utf
    //
    //****************************************
    public function pri_JSON($array) {
        $this->pri_arrayRecursive($array, 'gbk2utf8', true);
        return json_encode($array);
    }
	
	public function getmap($params)
	{
		//echo APP_DIR."/steelhomexxh/project.conf.php";
		
		//echo "111";
	    //print_r($GLOBALS['LWG']);
        $pz=$params['type']?$params['type']:'2023';
		
		//$GLOBALS['GC']
		
		
		$date=$params['date']?$params['date']:date('Y-m-d');
		//$date='2022-01-10';
		$allcity=$GLOBALS['LWG'];
		foreach($allcity as $key=>$tmp){
			$cityid = substr($tmp['cityId'],-2);
			$priceidstr.=",".$cityid.$pz;
		}
		$priceidstr = ltrim( $priceidstr,',');
		$PriceList=$this->getpricebypricestr($priceidstr,$date);
		 
		if(!empty($PriceList))
		{
			$data=array();
			foreach($allcity as $key=>$tmp){
				$cityid = substr($tmp['cityId'],-2);
				$priceids=$cityid.$pz;
				$priceinfo=$PriceList[$priceids];
				
				
				
				if($priceinfo)
				{
					$data1=$tmp;
					$data1['factory']=$priceinfo['factoryarea'];
					$data1['factory']='';
					$data1['value']=$priceinfo['price'];
					$data1['raise']=$priceinfo['raise'];
					$data1['mcode']=$priceinfo['priceids'];
					
					$data[]=$data1;
				}
				
			}
			
			
		  $info['data']=$data;
		  $info['datetime']=$date;
		  $info['option']='螺纹钢';
		}
		else
		{
		  $params['date']=date('Y-m-d',strtotime("-1 days",strtotime($date)));
		  $this->getmap($params);exit;	
		  $info['data']=array();
		  $info['datetime']=$date;
		  $info['option']='螺纹钢';
		}
		ob_clean();
		
		if($params['callback'])
		{
			echo $params['callback'].'('.$this->pri_JSON($info).')';
		}
		else
		{
			echo $this->pri_JSON($info);
		}
		

	}
	function getpricebypricestr($pricestr,$date)
	{
		//echo $time;
		    $idnumber=explode(',',$pricestr);
			//echo count($idnumber);
			$six=$seven=array();
			foreach($idnumber  as $id ){
				if (strlen ( $id ) == 6){//判断id 的字符长度 
					if(!in_array($id ,$six)){
						$six[]= $id ; 			
					} 							
				} 
				if (strlen ($id ) == 7) {
					if(!in_array($id,$seven)){
						$seven[]= $id;
					} 
				}
			}
		  $sixid_str=implode("','",$six);
 		  $sevenid_str=implode("','",$seven);
		$mconmanagedate.="(mconmanagedate>='".$date." 00:00:00' and mconmanagedate<='".$date." 23:59:59')";
		 if($sixid_str!=''){
 		  	$PriceListSQL=" select  factoryarea,price,oldprice,topicture, marketconditions.mconmanagedate from marketconditions
 		  	where    $mconmanagedate and  marketconditions.topicture in ('$sixid_str')";
 		  }
 		  if($sevenid_str!=''){
 		  	$PriceListSQL=" select factoryarea,price,oldprice,marketconditions.mastertopid as topicture, marketconditions.mconmanagedate from marketconditions
 		  	where $mconmanagedate and  marketconditions.mastertopid in ('$sevenid_str')";
 		  }
 		  if($sixid_str!=''&&$sevenid_str!=''){
	 		  $PriceListSQL=" select  factoryarea,price,oldprice,marketconditions.topicture, marketconditions.mconmanagedate from marketconditions  
	 		  where    $mconmanagedate and  marketconditions.topicture in ('$sixid_str') 
	 		  UNION( select factoryarea,price,oldprice,marketconditions.mastertopid as topicture,marketconditions.mconmanagedate from marketconditions 
	 		  where $mconmanagedate  and marketconditions.mastertopid in('$sevenid_str'))";
 		  }
		  //echo $PriceListSQL;exit;
		   $PriceList= $this->_dao->query( $PriceListSQL ); 
		   
		    //print_r($PriceList);
		    
		   $dataarr =array();//数据数组
		   foreach($PriceList  as $v )
		   {
				 if(strstr($v['price'],"-")){
					$avgprice = explode("-",$v['price']);
					$v['price'] = round(($avgprice['0']+$avgprice['1'])/2,2);
				}
				if(strstr($v['oldprice'],"-")){
					$avgprice = explode("-",$v['oldprice']);
					$v['oldprice'] = round(($avgprice['0']+$avgprice['1'])/2,2);
				}
				$v['raise']=$v['price']-$v['oldprice'];
				
			    $dataarr[$v['topicture']]=  $v;
		   }

		   //$data['date']=$datearr;
		   //$data['data']=$dataarr;
	
		  return $dataarr;
	}
	function getchartjs($params)
	{

		$fptypearr=array(
			'4'=>array(
				'1'=>'1',
				'2'=>'3',
				'3'=>'2',
				'4'=>'4',
			),
			'5'=>array(
				'1'=>'5',
				'2'=>'8',
				'3'=>'6',
				'4'=>'9',
				'5'=>'7',
			),
			'6'=>array(
				'1'=>'10',
				'2'=>'12',
				'3'=>'14',
				'4'=>'11',
				'5'=>'13',
				'6'=>'15',
			),
			'8'=>array(
				'1'=>'16',
				'2'=>'18',
				'3'=>'22',
				'4'=>'20',
				'5'=>'17',
				'6'=>'19',
				'7'=>'23',
				'8'=>'21',
			),
		);
		$id=$params['id'];
		$theme=$params['theme'];
		$GUID = $params['GUID'];
        $SignCS = "63cf7d3ea46b8d3ade7d6ceece903dd4";
		$sql="select * from DataScreenTemplate where id='".$id."' and isdel=0 ";
		//echo $sql;
		$DataScreenTemplate=$this->t1dao->getRow($sql);
		//print_r($DataScreenTemplate);
		$jscontent='';
		if($DataScreenTemplate['type']>1)
		{
			for($i=1;$i<9;$i++)
			{
				if($DataScreenTemplate['D'.$i])
				{
					//echo "1111";
					$manmonth=$DataScreenTemplate['labeltime'.$i]?$DataScreenTemplate['labeltime'.$i]:12;
					$DateStart=date('Y-m-d',strtotime('-'.$manmonth.' month'));
					//$DataScreenTemplate[$key]['D'.$i]=DC_URL."/v1.5/" .base64_decode($value['D'.$i])."&GUID=".$GUID."&SignCS=".$SignCS."&DateStart=".$DateStart."&DateEnd=".$DateEnd."&theme=".$theme;
					$pic_data = file_get_contents(DC_URL."/v1.5/" .base64_decode($DataScreenTemplate['D'.$i])."&GUID=".$GUID."&SignCS=".$SignCS."&DateStart=".$DateStart."&DateEnd=&theme=".$theme);
					$pic_data=str_replace('chartsmap','fun'.$DataScreenTemplate['id']."_".$i,$pic_data);
					$pic_data=str_replace('"charts"','"chart'.$fptypearr[$DataScreenTemplate['type']][$i].'"',$pic_data);
					$pic_data=str_replace('<script>','',$pic_data);
					$pic_data=str_replace('</script>','',$pic_data);
					
					//echo DC_URL."/v1.5/" .base64_decode($DataScreenTemplate['D'.$i])."&GUID=".$GUID."&SignCS=".$SignCS."&DateStart=".$DateStart."&DateEnd=".$DateEnd."&theme=".$theme;
					//echo $pic_data;
					//ob_clean();

					$jscontent.=$pic_data;
					// echo $pic_data;exit;
				}
				else
				{
					$jscontent.=" function fun".$DataScreenTemplate['id']."_".$i."(){}";
				}

			}

		}
		//$jscontent=iconv('utf-8','gbk',$jscontent);
		ob_clean();
		echo $jscontent;
		
	}
	
	


}
function gbk2utf8($str) {
        //return iconv("gbk","utf-8",$str);
		return $str;
        //return iconv("GB18030","utf-8",$str);
    }
?>
