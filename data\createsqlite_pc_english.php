<?php
set_time_limit(0);
include_once( "sys.conf.php" );
// sqlite分页类
class SqliteDB{
  public function __construct($mc_type){
    // 初始化数据库，并且连接数据库 数据库配置
	if($mc_type!=1)
	{
		$this->db = new PDO('sqlite:'.dirname(__FILE__).'/yun_english.dat');
	}
	else
	{
        $this->db = new PDO('sqlite:'.dirname(__FILE__).'/yun_english_ng.dat');
	}
    
    $this->table_name=$tab;
    $this->tab_init();
  }
  public function tab_init()
  {
    # 表初始化,创建表
    $tab1="CREATE TABLE [dc_code_class] (
        [ID] INT, 
        [sname] VARCHAR(50), 
        [scode] VARCHAR(50), 
        [snameshort] VARCHAR(20), 
        [hasnode] VARCHAR(10), 
        [ParentID] INT,
        [hasleaves] VARCHAR(10), 
        [sortod] INT);";
    $tab1_index = "CREATE INDEX id_class ON dc_code_class(ID);";
    $tab1_index2 = "CREATE INDEX parentid_class ON dc_code_class(ParentID);";
    $tab1_index3 = @"CREATE INDEX parentidname_class ON dc_code_class(ParentID,sname);";

    $tab2 = "CREATE TABLE [dc_code_datatype] (
        [ID] INT, 
        [dtname] VARCHAR(20), 
        [dtymd] TINYINT(1), 
        [scode1] VARCHAR(100), 
        [scode2] VARCHAR(100), 
        [scode3] VARCHAR(100), 
        [scode4] VARCHAR(100), 
        [scode5] VARCHAR(100), 
        [ParentID] INT, 
        [isSubDatas] TINYINT(1), 
        [subDatasTitle] VARCHAR(100), 
        [subAtt1Db] VARCHAR(100), 
        [subAtt2Db] VARCHAR(100), 
        [subAtt3Db] VARCHAR(100), 
        [Data1Type] TINYINT(1), 
        [Data2Type] TINYINT(1), 
        [Data3Type] TINYINT(1), 
        [Data4Type] TINYINT(1), 
        [Data5Type] TINYINT(1),
        [Data6Type] TINYINT(1),
        [Data7Type] TINYINT(1),
        [Data8Type] TINYINT(1),
        [Data9Type] TINYINT(1),
        [Data10Type] TINYINT(1),
        [Data11Type] TINYINT(1),
        [Data12Type] TINYINT(1),
        [Data13Type] TINYINT(1),
        [Data14Type] TINYINT(1),
        [Data15Type] TINYINT(1),
        [Data16Type] TINYINT(1),
        [Data17Type] TINYINT(1),
        [Data18Type] TINYINT(1),
        [Data19Type] TINYINT(1),
        [Data20Type] TINYINT(1),
        [ndate] VARCHAR(100), 
        [ndata1] VARCHAR(100), 
        [ndata2] VARCHAR(100), 
        [ndata3] VARCHAR(100), 
        [ndata4] VARCHAR(100), 
        [ndata5] VARCHAR(100), 
        [ndata6] VARCHAR(100), 
        [ndata7] VARCHAR(100), 
        [ndata8] VARCHAR(100), 
        [ndata9] VARCHAR(100), 
        [ndata10] VARCHAR(100), 
        [ndata11] VARCHAR(100), 
        [ndata12] VARCHAR(100), 
        [ndata13] VARCHAR(100), 
        [ndata14] VARCHAR(100), 
        [ndata15] VARCHAR(100),
        [ndata16] VARCHAR(100),
        [ndata17] VARCHAR(100),
        [ndata18] VARCHAR(100),
        [ndata19] VARCHAR(100),
        [ndata20] VARCHAR(100),
        [npredata] VARCHAR(100), 
        [naddsubdata] VARCHAR(100), 
        [naddsubhundcore] VARCHAR(100), 
        [targetflagvar] TINYINT(1),
        [isbuy] VARCHAR(10),
        [SYear] VARCHAR(10),
        [EYear] VARCHAR(10),
        [pinzhong] INT,
		[data_source] VARCHAR(50),
		[unitstrings] VARCHAR(200),
		[unitconvers] VARCHAR(200),
		[data_releasetime] VARCHAR(200), 
        [sortod] INT);";
    $tab2_index = "CREATE INDEX id_type ON dc_code_datatype(ID);";
    $tab2_index2 = "CREATE INDEX parentid_type ON dc_code_datatype(ParentID);";
    $tab3 = "CREATE TABLE [dc_code_datatype_subs] (
        [ID] INT, 
        [DID] INT, 
        [sname] VARCHAR(100), 
        [satt1] VARCHAR(100), 
        [satt2] VARCHAR(100), 
        [satt3] VARCHAR(100),
        [scode] VARCHAR(100), 
        [ParentID] INT);";
    $tab3_index = "CREATE INDEX id_subs ON dc_code_datatype_subs(ID);";
    $tab3_index2 = "CREATE INDEX parentid_subs ON dc_code_datatype_subs(ParentID);";
    $tab4 = "CREATE TABLE [dc_search_custom] (
  [ID] INT, 
  [dtname] VARCHAR(100), 
  [dtnameshort] VARCHAR(50), 
  [ImageType] TINYINT(1), 
  [DTID1] INT, 
  [DTID1Sub] INT,
  [Data1Type] TINYINT(1),
  [Data1Type1] TINYINT(1), 
  [Data1Image] TINYINT(1), 
  [Data1Pre] TINYINT(1), 
  [Data1AddSub] TINYINT(1), 
  [Data1AddSubHundCore] TINYINT(1), 
  [Data1Type2] TINYINT(1), 
  [Data1Type3] TINYINT(1), 
  [Data1Type4] TINYINT(1), 
  [Data1Type5] TINYINT(1), 
  [DTID2] INT, 
  [DTID2Sub] INT, 
  [Data2Type] TINYINT(2), 
  [Data2Image] TINYINT(1), 
  [DTID3] INT, 
  [DTID3Sub] INT, 
  [Data3Type] TINYINT(2), 
  [Data3Image] TINYINT(1), 
  [DTID4] INT, 
  [DTID4Sub] INT, 
  [Data4Type] TINYINT(2), 
  [Data4Image] TINYINT(1));";

            $tab5 = "CREATE TABLE [dc_search_system] (
  [ID] INT, 
  [dtname] VARCHAR(100), 
  [dtnameshort] VARCHAR(50), 
  [ImageType] TINYINT(1), 
  [DTID1] INT, 
  [DTID1Sub] INT, 
  [Data1Type] TINYINT(1),
  [Data1Type1] TINYINT(1), 
  [Data1Image] TINYINT(1), 
  [Data1Pre] TINYINT(1), 
  [Data1AddSub] TINYINT(1), 
  [Data1AddSubHundCore] TINYINT(1), 
  [Data1Type2] TINYINT(1), 
  [Data1Type3] TINYINT(1), 
  [Data1Type4] TINYINT(1), 
  [Data1Type5] TINYINT(1), 
  [DTID2] INT, 
  [DTID2Sub] INT, 
  [Data2Type] TINYINT(2), 
  [Data2Image] TINYINT(1), 
  [DTID3] INT, 
  [DTID3Sub] INT, 
  [Data3Type] TINYINT(2), 
  [Data3Image] TINYINT(1), 
  [DTID4] INT, 
  [DTID4Sub] INT, 
  [Data4Type] TINYINT(2), 
  [Data4Image] TINYINT(1), 
  [DTIDJson] TEXT,
  [ParentID] INT);";

            $tab5_index = "CREATE INDEX id_system ON dc_search_system(ID);";
            $tab5_index2 = "CREATE INDEX parentid_system ON dc_search_system(ParentID);";
$tab6 = "CREATE TABLE [dc_search_custom_dfconf] (
  [ID] INT, 
  [dfkey] VARCHAR(20), 
  [dfvalue] VARCHAR(20), 
  [remark] VARCHAR(20),
  [dfod] INT
  );";
$tab6_index = "CREATE INDEX id_custom ON dc_search_custom_dfconf(ID);";
			$tabs = "";
            if (!$this->ExistsTab( "dc_code_class"))
            {
                $tabs .= $tab1;
                $tabs .= $tab1_index;
                $tabs .= $tab1_index2;
                //tabs += tab1_index3;
            }
			else
	        {
              $tabs .= " delete from dc_code_class; ";
			}
            if (!$this->ExistsTab("dc_code_datatype"))
            {
                $tabs .= $tab2;
                $tabs .= $tab2_index;
                $tabs .= $tab2_index2;
            }
			else
	        {
              $tabs .= " delete from dc_code_datatype; ";
			}
            if (!$this->ExistsTab( "dc_code_datatype_subs"))
            {
                $tabs .= $tab3;
                $tabs .= $tab3_index;
                $tabs .= $tab3_index2;
            }
			else
	        {
              $tabs .= " delete from dc_code_datatype_subs; ";
			}
            if (!$this->ExistsTab("dc_search_custom"))
            {
                $tabs .= $tab4;
            }
			else
	        {
              $tabs .= " delete from dc_search_custom; ";
			}
            if (!$this->ExistsTab( "dc_search_system"))
            {
                $tabs .= $tab5;
                $tabs .= $tab5_index;
            }
			else
	        {
              $tabs .= " delete from dc_search_system; ";
			}
			
			if (!$this->ExistsTab( "dc_search_custom_dfconf"))
            {
                $tabs .= $tab6;
                $tabs .= $tab6_index;
            }
			else
	        {
              $tabs .= " delete from dc_search_custom_dfconf; ";
			}
			
			if($tabs!='')
			{
				 $this->db->exec($tabs);//创建表并加索引
			}
  
    // $this->db->exec("CREATE TABLE log(
      // id integer PRIMARY KEY autoincrement,
      // urls varchar(200),
      // ip varchar(200),
      // datetimes datetime default (datetime('now', 'localtime'))
      // )");
  }
  
  public function ExistsTab($tab_name){
  
	    $cmd= "SELECT COUNT(*) c FROM sqlite_master where type='table' and name='". $tab_name ."';";
		$sth = $this->db->prepare($cmd);
		$sth->execute();
		$result = $sth->fetchAll();
		print_r($result);
		$total= $result[0]['c'];   
    
		   if (0 == $total)
            {  
                return false;
            }
            else
            {
                return true;
            }  
  }
  
  public function insert($tab_name,$key_list,$value_list)
  {
    $str="INSERT INTO ".$tab_name." (".$key_list.") values(".$value_list.");";
    //$result=$this->db->exec("INSERT INTO ".$tab_name." (".$key_list.") values(".$value_list.")");
    if (!$result) {
      return false;
    }
	return $str;
    // echo "{{{INSERT INTO ".$tab_name." (".$key_list.") values(".$value_list.")}}}}";
    //$res=$this->db->beginTransaction();//事务回gun
  }
   public function insertMore($tab_name)
  {
    //开启事物,此时会关闭自动提交 
    $this->db->beginTransaction();
    $sql = "INSERT INTO ".$tab_name." (id,urls,ip) values (?,?,?)";
    $stmt = $this->db->prepare($sql);
    //传入参数
	while($s <= 99999){
    $stmt->execute(array(null,"test".$s,"w"));
	$s++;
    $stmt->execute(array(null,"test5","w"));
    $stmt->execute(array(null,"test3","w"));
	}
    //提交事物，并且 数据库连接返回到自动提交模式
    $this->db->commit();
  }
  
  public function insert_dc_search_system($result,$insert_dc_search_system)
  {
	  try{
    //开启事物,此时会关闭自动提交 
    $this->db->beginTransaction();
    $sql = "insert into dc_search_system (ID,dtname,dtnameshort,ImageType,DTID1,DTID1Sub,Data1Type1,Data1Image,Data1Pre,Data1AddSub,Data1AddSubHundCore,Data1Type2,Data1Type3,Data1Type4,Data1Type5,DTID2,DTID2Sub,Data2Type,Data2Image,DTID3,DTID3Sub,Data3Type,Data3Image, DTID4,DTID4Sub,Data4Type,Data4Image,DTIDJson,ParentID) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?);";
    $stmt = $this->db->prepare($sql);
    //传入参数
	foreach($result as $item){
		$stmt->execute(array($item["ID"],$item["dtname"],$item["dtnameshort"],$item["ImageType"],$item["DTID1"],$item["DTID1Sub"],$item["Data1Type1"],$item["Data1Image"],$item["Data1Pre"],$item["Data1AddSub"],$item["Data1AddSubHundCore"], $item["Data1Type2"],  $item["Data1Type3"], $item["Data1Type4"], $item["Data1Type5"],$item["DTID2"],$item["DTID2Sub"],$item["Data2Type"],$item["Data2Image"],$item["DTID3"],$item["DTID3Sub"],$item["Data3Type"],$item["Data3Image"],$item["DTID4"],$item["DTID4Sub"],$item["Data4Type"],$item["Data4Image"],$item["DTIDJson"],$insert_dc_search_system[$item["scode1"]]));
	}
    //提交事物，并且 数据库连接返回到自动提交模式
    $this->db->commit();
	}catch(PDOException $e){
		echo '执行失败'.$e->getMessage();
	}
  }
  
  
  public function insert_dc_code_class($result,$newresultscode,$newresultmcode,$newdc_code_datatype)
  {

    /* foreach($result as $item)
	 {
		 $newresultscode[$item["scode"]]=$item['ID'];
		 $newresultmcode[$item["mcode"]]=$item['ID'];
	 }*/
   //print_r($newresultscode);
	  try{
    //开启事物,此时会关闭自动提交 
    $this->db->beginTransaction();
    $sql = "insert into dc_code_class (ID,sname,scode,snameshort,hasnode,ParentID,hasleaves,sortod) values(?,?,?,?,?,?,?,?)";
    $stmt = $this->db->prepare($sql);
    //传入参数
	foreach($result as $item){
        $ParentID=empty($newresultscode[$item["mcode"]])?0:$newresultscode[$item["mcode"]];
        
        $charactors = array('A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z');
        
       if($ParentID==0)
       {
          if(!in_array($item["scode"],$charactors))
          {
            $ParentID=-1;
          }
       }

        $hasnode=empty($newresultmcode[$item["scode"]])?0:1;
		$hasleaves=empty($newdc_code_datatype[$item["scode"]])?0:1;
		//$hasleaves=$hasnode==0?"0":$hasleaves;
		$item["sname"]=$ParentID==0?$item["snameshort"]:$item["sname"];
		$stmt->execute(array($item["ID"],$item["sname"],$item["scode"],$item["snameshort"],$hasnode,$ParentID,$hasleaves,$item["sod"]));
	}
    //提交事物，并且 数据库连接返回到自动提交模式
    $this->db->commit();
	}catch(PDOException $e){
		echo '执行失败'.$e->getMessage();
	}
  }
  
  
  public function insert_dc_code_datatype($result,$newresultscode,$resultDIDarr)
  {
	try{
    //开启事物,此时会关闭自动提交 
    $this->db->beginTransaction();
    $sql = "insert into dc_code_datatype (ID,dtname,dtymd,scode1,scode2,scode3,scode4,scode5,ParentID,isSubDatas,subDatasTitle,subAtt1Db,subAtt2Db,subAtt3Db,Data1Type,Data2Type,Data3Type,Data4Type,Data5Type,Data6Type,Data7Type,Data8Type,Data9Type,Data10Type,Data11Type,Data12Type,Data13Type,Data14Type,Data15Type,Data16Type,Data17Type,Data18Type,Data19Type,Data20Type,ndate,ndata1,ndata2,ndata3,ndata4,ndata5,ndata6,ndata7,ndata8,ndata9,ndata10,ndata11,ndata12,ndata13,ndata14,ndata15,ndata16,ndata17,ndata18,ndata19,ndata20,npredata,naddsubdata,naddsubhundcore,targetflagvar,isbuy,SYear,EYear,pinzhong,data_source,unitstrings,unitconvers,data_releasetime,sortod)values(?,?,?,?,? ,?,?,?,?,?,?,?,?,?,?,?,?,? ,?,?,?,?,?,?,?,?,?,?,? ,? ,? ,? ,?,? ,? ,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
    $stmt = $this->db->prepare($sql);
	//print_r($result);
    //传入参数
	foreach($result as $item){

             if(!empty($item['scode5']))
		   {
              $pid=$newresultscode[$item['scode5']];
			 
		   }
            else if(!empty($item['scode4']))
		   {
              $pid=$newresultscode[$item['scode4']];
			 
		   }
		   else if(!empty($item['scode3']))
		   {
              $pid=$newresultscode[$item['scode3']];
			   
		   }
		   else if(!empty($item['scode2']))
		   {
              $pid=$newresultscode[$item['scode2']];
		   }
		   else if(!empty($item['scode1']))
		   {
              $pid=$newresultscode[$item['scode1']];

		   }
           $item["isSubDatas"]=0;
           if(is_array($resultDIDarr))
                $item["isSubDatas"]=in_array($item["ID"],$resultDIDarr)?1:0;
			
		   $unitstrings = str_replace(",", "", $item["UnitTypeName_en"]).','.str_replace(",", "", $item["npredata_UnitTypeName"]).','.str_replace(",", "", $item["naddsubdata_UnitTypeName"]).','.str_replace(",", "", $item["daddsubhundcore_UnitTypeName"]).','.str_replace(",", "", $item["UnitTypeName2_en"]).','.str_replace(",", "", $item["UnitTypeName3_en"]).','.str_replace(",", "", $item["UnitTypeName4_en"]).','.str_replace(",", "", $item["UnitTypeName5_en"]).','.str_replace(",", "", $item["UnitTypeName6_en"]).','.str_replace(",", "", $item["UnitTypeName7_en"]).','.str_replace(",", "", $item["UnitTypeName8_en"]).','.str_replace(",", "", $item["UnitTypeName9_en"]).','.str_replace(",", "", $item["UnitTypeName10_en"]).','.str_replace(",", "", $item["UnitTypeName11_en"]).','.str_replace(",", "", $item["UnitTypeName12_en"]).','.str_replace(",", "", $item["UnitTypeName13_en"]).','.str_replace(",", "", $item["UnitTypeName14_en"]).','.str_replace(",", "", $item["UnitTypeName15_en"]).','.str_replace(",", "", $item["UnitTypeName16_en"]).','.str_replace(",", "", $item["UnitTypeName17_en"]).','.str_replace(",", "", $item["UnitTypeName18_en"]).','.str_replace(",", "", $item["UnitTypeName19_en"]).','.str_replace(",", "", $item["UnitTypeName20_en"]);

			$unitconvers = $item["Data1UnitConv"].','.$item["npredata_UnitConv"].','.$item["naddsubdata_UnitConv"].','.$item["daddsubhundcore_UnitConv"].','.$item["Data2UnitConv"].','.$item["Data3UnitConv"].','.$item["Data4UnitConv"].','.$item["Data5UnitConv"].','.$item["Data6UnitConv"].','.$item["Data7UnitConv"].','.$item["Data8UnitConv"].','.$item["Data9UnitConv"].','.$item["Data10UnitConv"].','.$item["Data11UnitConv"].','.$item["Data12UnitConv"].','.$item["Data13UnitConv"].','.$item["Data14UnitConv"].','.$item["Data15UnitConv"].','.$item["Data16UnitConv"].','.$item["Data17UnitConv"].','.$item["Data18UnitConv"].','.$item["Data19UnitConv"].','.$item["Data20UnitConv"];

				$stmt->execute(array($item["ID"],$item["dtname"],$item["dtymd"],$item["scode1"],$item["scode2"],$item["scode3"],$item["scode4"],$item["scode5"],$pid,$item["isSubDatas"],$item["subDatasTitle"],$item["subAtt1Db"],$item["subAtt2Db"],$item["subAtt3Db"],$item["Data1Type"],$item["Data2Type"],$item["Data3Type"],$item["Data4Type"],$item["Data5Type"],$item["Data6Type"],$item["Data7Type"],$item["Data8Type"],$item["Data9Type"],$item["Data10Type"],$item["Data11Type"],$item["Data12Type"],$item["Data13Type"],$item["Data14Type"],$item["Data15Type"],$item["Data16Type"],$item["Data17Type"],$item["Data18Type"],$item["Data19Type"],$item["Data20Type"],$item["ndate"],$item["ndata1"],$item["ndata2"],$item["ndata3"],$item["ndata4"],$item["ndata5"],$item["ndata6"],$item["ndata7"],$item["ndata8"],$item["ndata9"],$item["ndata10"],$item["ndata11"],$item["ndata12"],$item["ndata13"],$item["ndata14"],$item["ndata15"],$item["ndata16"],$item["ndata17"],$item["ndata18"],$item["ndata19"],$item["ndata20"],$item["npredata"],$item["naddsubdata"],$item["naddsubhundcore"],$item["TargetFlagVar"],0,2010,2020,$item["pinzhong"],$item["data_source"],$unitstrings,$unitconvers,$item["data_releasetime"],$item["dtod"]));
	}
    //提交事物，并且 数据库连接返回到自动提交模式
    $this->db->commit();
	}catch(PDOException $e){
		echo '执行失败'.$e->getMessage();
	}
  }
  
  
  
   public function insert_dc_code_datatype_subs($result)
  {
	try{
    //开启事物,此时会关闭自动提交 
    $this->db->beginTransaction();
    $sql = "insert into dc_code_datatype_subs (ID,DID,sname,satt1,satt2,satt3,scode,ParentID)values(?,?,?,?,?,?,?,?)";
    $stmt = $this->db->prepare($sql);
	//print_r($result);
    //传入参数
	foreach($result as $item){
		$stmt->execute(array($item["ID"],$item["DID"],$item["sname"],$item["satt1"],$item["satt2"],$item["satt3"],$item["scode"],$item['ParentID']));
	}
    //提交事物，并且 数据库连接返回到自动提交模式
    $this->db->commit();
	}catch(PDOException $e){
		echo '执行失败'.$e->getMessage();
	}
  }
  
  public function insert_dc_search_custom_dfconf($result)
  {
	try{
    //开启事物,此时会关闭自动提交 
    $this->db->beginTransaction();
    $sql = "insert into dc_search_custom_dfconf (ID,dfkey,dfvalue,remark,dfod )values(?,?,?,?,?)";
    $stmt = $this->db->prepare($sql);
	//print_r($result);
    //传入参数
	foreach($result as $item){
		$stmt->execute(array($item["id"],$item["dfkey"],replacestr($item["dfvalue"]),replacestr($item['remark']),$item["dfod"]));
	}
    //提交事物，并且 数据库连接返回到自动提交模式
    $this->db->commit();
	}catch(PDOException $e){
		echo '执行失败'.$e->getMessage();
	}
  }
  
  public function total($tab_name,$tj='')//求总记录数目
  {
    $sth = $this->db->prepare('SELECT count(id) as c FROM '.$tab_name.' '.$tj);
    $sth->execute();
    $result = $sth->fetchAll();
    return $result[0]['c'];
  }
  
  public function totalxx($sql)//求总记录数目
  {
    $sth = $this->db->prepare($sql);
    $sth->execute();
    $result = $sth->fetchAll();
    return $result[0]['c'];
  }
  public function update()
  {
    # 修改
  }
  function delete($value='')
  {
    # 删除
  }
  public function query($tab_name,$tj='')//表名称和条件
  {
    $sth = $this->db->prepare('SELECT * FROM '.$tab_name.' '.$tj);
    // echo 'SELECT * FROM '.$tab_name.' '.$tj;
    $sth->execute();
    $result = $sth->fetchAll();
    return $result;
  }
}

 function replacestr($str)
{
    $arr=array("&lt;","&gt;","&amp;","&quot;","&times");
    $arr1=array("<",">","&","\"","×");
   $str=str_replace( $arr,$arr1,$str);
   return $str;
}

function array_iconv($str, $in_charset="gbk", $out_charset="utf-8")
{
    return $str;
 if(is_array($str))
 {
 foreach($str as $k => $v)
 {
  $str[$k] = array_iconv($v);
 }
 return $str;
 }
 else
 {
 if(is_string($str))
 {
  // return iconv('UTF-8', 'GBK//IGNORE', $str);
  return mb_convert_encoding($str, $out_charset, $in_charset);
 }
 else
 {
  return $str;
 }
 }
}
if($argv[1])
{
 $_REQUEST['mc_type']=$argv[1];
}
$mc_type=0;
if($_REQUEST['mc_type'])
{
   $mc_type=$_REQUEST['mc_type'];
}

if(!in_array($mc_type,array(0,1)))
{
   echo "错误的mc_type，请检查后再试。<br/>";
   exit;
}


$db=new SqliteDB($mc_type);


///$dsn = 'mysql:dbname=steelhome_t1;host=***********;port=4306';
///$user = 'root2';
///$password = '123456';

///$dsn = 'mysql:dbname=steelhome_t1;host=*************;port=3307';
//$user = 'dbread';
//$password = 'sth@50581010';

//采用预处理+事务处理执行SQL操作
//1.连接数据库
try {
    $pdo = new PDO($dsn, $user, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE,PDO::ERRMODE_EXCEPTION);
	echo "ok ";
} catch (PDOException $e) {
    die("数据库连接失败".$e->getMessage());
}
 



//2.执行数据操作
try{
	
	 $sql="SELECT  `scode1` ,  `scode2` ,  `scode3` ,  `scode4` ,  `scode5` FROM  `dc_code_datatype` WHERE  `mc_type` ='".$mc_type."' AND Status =1";
     $sth = $pdo->prepare($sql);
     $sth->execute();
     $result = $sth->fetchAll();
    foreach($result as $item)
	 {
		 if(!empty($item['scode5']))
		   {
              $pid=$item['scode5'];
			 
		   }
		   else if(!empty($item['scode4']))
		   {
              $pid=$item['scode4'];
			 
		   }
		   else if(!empty($item['scode3']))
		   {
              $pid=$item['scode3'];
			   
		   }
		   else if(!empty($item['scode2']))
		   {
              $pid=$item['scode2'];
		   }
		   else if(!empty($item['scode1']))
		   {
              $pid=$item['scode1'];

		   }

		 $newdc_code_datatype[$pid]=1;
	 }
	 
	 
	 
	 $count_sql = "select ID,mcode,sname_en as sname,snameshort_en as snameshort,scode,sod,sdesc,0 as hasnode from dc_code_class where   mc_type='".$mc_type."' and Status =1 order by sod";
	 $sth = $pdo->prepare($count_sql);
     $sth->execute();
     $result = $sth->fetchAll();

     $result=array_iconv($result);
      foreach($result as $item)
	 {
		 $newresultscode[$item["scode"]]=$item['ID'];
		 $newresultmcode[$item["mcode"]]=$item['ID'];
	 }
      
     
	 $db->insert_dc_code_class($result, $newresultscode, $newresultmcode,$newdc_code_datatype);
     echo "插入dc_code_class操作完成<br/>";
    
	
     $count_sql = "select ID,scode1,dtname_en as dtname,dtnameshort_en as dtnameshort,ImageType,DTID1,DTID1Sub, Data1Type, Data1Type1, Data1Image, Data1Pre, Data1AddSub, Data1AddSubHundCore, Data1Type2, Data1Type3, Data1Type4, Data1Type5, DTID2 , DTID2Sub, Data2Type ,Data2Image, DTID3 , DTID3Sub , Data3Type , Data3Image, DTID4 , DTID4Sub , Data4Type , Data4Image,DTIDJson, zhou1,zhou2,zhou3,zhou4, isjz,isbg,isfg,color,aod from dc_search_system where IsUse='1' and mc_type='".$mc_type."' order by aod";
	 $sth = $pdo->prepare($count_sql);
    
     $sth->execute();
     $result = $sth->fetchAll();
    
	  $db->insert_dc_search_system(array_iconv($result),$newresultscode);
	  echo "插入dc_search_system操作完成<br/>";
	 

$count_sql = "select distinct DID from dc_code_datatype_subs,dc_code_datatype where dc_code_datatype_subs.did= dc_code_datatype.id and dc_code_datatype_subs.Status =1 and dc_code_datatype.Status =1  and dc_code_datatype_subs.mc_type='".$mc_type."' and dc_code_datatype.mc_type='".$mc_type."'"; 
$sth = $pdo->prepare($count_sql);
$sth->execute();
$result = $sth->fetchAll();
foreach($result as $item)
 {
	 $resultDIDarr[]=$item['DID'];
 }

	$count_sql = "select count(*) as c from dc_code_datatype  where mc_type='".$mc_type."' and Status =1";
	$sth = $pdo->prepare($count_sql);
    $sth->execute();
    $result = $sth->fetchAll();
    $totle= $result[0]['c'];
	$pagesize=1000;
    $pagecount=ceil($totle/$pagesize);
    for($i = 0;$i<=$pagecount;$i++){
        // $start = $i;
        // $end = $i + 10000;
		$dataunitconv = "Data1UnitConv,UnitTypeName_en,Data2UnitConv,UnitTypeName2_en,Data3UnitConv,UnitTypeName3_en,Data4UnitConv,UnitTypeName4_en,Data5UnitConv,UnitTypeName5_en,Data6UnitConv,UnitTypeName6_en,Data7UnitConv,UnitTypeName7_en,Data8UnitConv,UnitTypeName8_en,Data9UnitConv,UnitTypeName9_en,Data10UnitConv,UnitTypeName10_en,Data11UnitConv,UnitTypeName11_en,Data12UnitConv,UnitTypeName12_en,Data13UnitConv,UnitTypeName13_en,Data14UnitConv,UnitTypeName14_en,Data15UnitConv,UnitTypeName15_en,Data16UnitConv,UnitTypeName16_en,Data17UnitConv,UnitTypeName17_en,Data18UnitConv,UnitTypeName18_en,Data19UnitConv,UnitTypeName19_en,Data20UnitConv,UnitTypeName20_en,npredata_UnitConv,npredataDW_en as npredata_UnitTypeName,naddsubdata_UnitConv,naddsubdataDW_en as naddsubdata_UnitTypeName,daddsubhundcore_UnitConv,naddsubhundcoreDW_en as daddsubhundcore_UnitTypeName";

        $sql = "select ID,scode1,scode2,scode3,scode4,scode5,dtname_en as dtname,dtymd,dtcomparetype,dbaseType,isSubDatas,subDatasTitle_en as subDatasTitle,subDatasDb,subDatasDb2,subAtt1Db_en as subAtt1Db,subAtt2Db_en as subAtt2Db,subAtt3Db_en as subAtt3Db,Data1Type,Data2Type,Data3Type,Data4Type,Data5Type,Data6Type,Data7Type,Data8Type,Data9Type,Data10Type,Data11Type,Data12Type,Data13Type,Data14Type,Data15Type,Data16Type,Data17Type,Data18Type,Data19Type,Data20Type,startdate,enddata,dtod,ndate_en as ndate,ndata1_en as ndata1,ndata2_en as ndata2,ndata3_en as ndata3,ndata4_en as ndata4,ndata5_en as ndata5,ndata6_en as ndata6,ndata7_en as ndata7,ndata8_en as ndata8,ndata9_en as ndata9,ndata10_en as ndata10,ndata11_en as ndata11,ndata12_en as ndata12,ndata13_en as ndata13,ndata14_en as ndata14,ndata15_en as ndata15,ndata16_en as ndata16,ndata17_en as ndata17,ndata18_en as ndata18,ndata19_en as ndata19,ndata20_en as ndata20,npredata_en as npredata,naddsubdata_en as naddsubdata,naddsubhundcore_en as naddsubhundcore,TargetFlagVar,pinzhong,data_source,".$dataunitconv.",data_releasetime from dc_code_datatype,dc_code_datatype_db where dc_code_datatype_db.DID = dc_code_datatype.ID and dc_code_datatype.mc_type='".$mc_type."' and dc_code_datatype_db.mc_type='".$mc_type."' and Status =1 order by dtod limit  ".($i*$pagesize).",$pagesize";
		 //$sql = "select ID,scode1,scode2,scode3,scode4,dtname as dtname,dtymd,dtcomparetype,dbaseType,isSubDatas,subDatasTitle as subDatasTitle,subDatasDb,subDatasDb2,subAtt1Db as subAtt1Db,subAtt2Db as subAtt2Db,subAtt3Db as subAtt3Db,Data1Type,Data2Type,Data3Type,Data4Type,Data5Type,Data6Type,Data7Type,Data8Type,Data9Type,Data10Type,Data11Type,Data12Type,Data13Type,Data14Type,Data15Type,Data16Type,Data17Type,Data18Type,Data19Type,Data20Type,startdate,enddata,dtod,ndate as ndate,ndata1 as ndata1,ndata2 as ndata2,ndata3 as ndata3,ndata4 as ndata4,ndata5 as ndata5,ndata6 as ndata6,ndata7 as ndata7,ndata8 as ndata8,ndata9 as ndata9,ndata10 as ndata10,ndata11 as ndata11,ndata12 as ndata12,ndata13 as ndata13,ndata14 as ndata14,ndata15 as ndata15,ndata16 as ndata16,ndata17 as ndata17,ndata18 as ndata18,ndata19 as ndata19,ndata20 as ndata20,npredata as npredata,naddsubdata as naddsubdata,naddsubhundcore as naddsubhundcore,TargetFlagVar from dc_code_datatype,dc_code_datatype_db where dc_code_datatype_db.DID = dc_code_datatype.ID and dc_code_datatype.mc_type='0' and dc_code_datatype_db.mc_type='0' and Status =1 order by dtod limit  ".($i*$pagesize).",$pagesize";
		//echo $sql;
        $sth = $pdo->prepare($sql);
		$sth->execute();
		$result = $sth->fetchAll();
        //if(is_array($resultDIDarr))
		    $db->insert_dc_code_datatype(array_iconv($result),$newresultscode,$resultDIDarr);
    }
	 echo "插入dc_code_datatype操作完成<br/>";
	
	 
		$count_sql = "select count(*) as c from dc_code_datatype_subs ,dc_code_datatype where dc_code_datatype_subs.did= dc_code_datatype.id and dc_code_datatype_subs.status =1 and dc_code_datatype.Status =1 and dc_code_datatype_subs.mc_type='".$mc_type."' and dc_code_datatype.isSubDatas=1";
	$sth = $pdo->prepare($count_sql);
    $sth->execute();
    $result = $sth->fetchAll();
    $totle= $result[0]['c'];
	echo $totle;
	$pagesize=1000;
    $pagecount=ceil($totle/$pagesize);
    for($i = 0;$i<=$pagecount;$i++){
        // $start = $i;
        // $end = $i + 10000;
        $sql = "select dc_code_datatype_subs.ID,DID,sname_en as sname,scode_en as scode,sdbtype,satt1_en as satt1,satt2_en as satt2,satt3_en as satt3,aod,dc_code_datatype.id as ParentID from dc_code_datatype_subs,dc_code_datatype where dc_code_datatype_subs.did= dc_code_datatype.id and dc_code_datatype_subs.status =1 and dc_code_datatype.Status =1 and dc_code_datatype_subs.mc_type='".$mc_type."' and dc_code_datatype.isSubDatas=1 order by aod
 limit ".($i*$pagesize).",$pagesize";
	//echo $sql;
         $sth = $pdo->prepare($sql);
		 $sth->execute();
		 $result = $sth->fetchAll();
		$db->insert_dc_code_datatype_subs(array_iconv($result));
    }
    echo "插入dc_code_datatype_subs操作完成<br/>";
	
	$count_sql = "select id,dfkey,dfvalue,remark,dfod from dc_search_custom_dfconf where mc_type='".$mc_type."'  "; 
	$sth = $pdo->prepare($count_sql);
	$sth->execute();
	$result = $sth->fetchAll();
	$db->insert_dc_search_custom_dfconf(array_iconv($result));
	echo "dc_search_custom_dfconf<br/>";
	
}catch(PDOException $e){
    echo '执行失败'.$e->getMessage();
}
 
 echo "操作完成<br/>";
 
//这里需要注意该目录是否存在，并且有创建的权限
$zipname = dirname(__FILE__).'/yun_english.zip' ;
 //这是要打包的文件地址数组
$files = array(dirname(__FILE__)."/yun_english.dat");
if($mc_type!=0)
{
 $zipname = dirname(__FILE__).'/yun_english_ng.zip' ;
 $files = array(dirname(__FILE__)."/yun_english_ng.dat");
} 

 
$zip = new ZipArchive();
$res = $zip->open($zipname, ZipArchive::CREATE);
if ($res === TRUE) {
    foreach ($files as $file) {
 //这里直接用原文件的名字进行打包，也可以直接命名，需要注意如果文件名字一样会导致后面文件覆盖前面的文件，所以建议重新命名
     $new_filename = substr($file, strrpos($file, '/') + 1);
      //$new_filename= $file;

     $zip->addFile($file, $new_filename);
}
}
$zip->close();

echo "操作压缩完成<br/>";

$md5file = md5_file($zipname);

echo  "文件MD5码：".$md5file;



if($md5file)
{

	//$wdsn = 'mysql:dbname=steelhome_t1;host=***********;port=4306';//可写库
	//$wuser = 'root2';
	//$wpassword = '123456';
	//采用预处理+事务处理执行SQL操作
	//1.连接数据库
	try {
		$wpdo = new PDO($wdsn, $wuser, $wpassword);
		$wpdo->setAttribute(PDO::ATTR_ERRMODE,PDO::ERRMODE_EXCEPTION);
		echo "ok ";
	} catch (PDOException $e) {
		die("数据库连接失败".$e->getMessage());
	}
	$sql="update app_version set EnVersionUrlMd5='".$md5file."' where  VersionType=2 and  mc_type='".$mc_type."' and Status=1 limit 1";
	$sth1 = $wpdo->prepare($sql);
	$sth1->execute();
}

?>