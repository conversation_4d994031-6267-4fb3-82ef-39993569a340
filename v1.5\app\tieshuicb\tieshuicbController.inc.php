<?php
/*
 * @Author: x<PERSON><PERSON><PERSON>
 * @Date: 2022-07-16 14:42:07
 * @LastEditTime: 2022-12-30 09:24:12
 * @FilePath: \www.steelhome.cnk:\www\dc.steelhome.cn\v1.5\app\tieshuicb\tieshuicbController.inc.php
 * @Description: 
 * @Copyright: © 2021, SteelHome. All rights reserved.
 */
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );


class tieshuicbController extends AbstractController{
  
  public function __construct(){
    parent::__construct();
	
	$this->_action->setDao( new tieshuicbDao( 'DRCW',"DRC" ) );
	//$this->_action->ngdao = new DcWebViewDGDao('DRCW') ;
	$this->_action->t1dao = new tieshuicbDao('MAIN') ;
	$this->_action->maindao = new tieshuicbDao('91W','91R');
	//$this->_action->maindao_test = new tieshuicbDao('91W','91R');
	$this->_action->gcdao=new tieshuicbDao('GC');
  }
  
  public function _dopre(){
      //$this->_action->checkSession();
  }

  public function v_index() {
	$this->_action->index($this->_request);
  }
  public function v_ajaxgetindexinfo() {
      //header( "Content-Type: text/html; charset=gbk" );
	$this->_action->ajaxgetindexinfo($this->_request);
  }
  public function do_save() {
      //header( "Content-Type: text/html; charset=gbk" );
	$this->_action->save($this->_request);
  }
  public function do_update() {
	$this->_action->update($this->_request);
  }
  public function do_del() {
	$this->_action->del($this->_request);
  }

  public function do_get_tieshui_cb() {
	$this->_action->get_tieshui_cb($this->_request);
  }

  public function v_test1() {
	$this->_action->test1($this->_request);
  }
  public function v_test2() {
	$this->_action->test2($this->_request);
  }
  public function do_dscx() 
  {
	  $this->_action->dscx($this->_request);
  }
  
}