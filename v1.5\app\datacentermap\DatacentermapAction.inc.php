<?php
// error_reporting(E_ALL); ini_set('display_errors', '1');
$GLOBALS['mc_type_list']=array(
	"0"=>"钢之家",
	"1"=>"南钢",
	"2"=>"陕钢",
	"3"=>"新钢",
	"4"=>"新钢价格预测",
);
class DatacentermapAction extends AbstractAction{

	public $stedao;
	public $drc;
	public $sms;

	public function __construct(){
	parent::__construct();
	}

	public function test()
	{
		include_once (APP_DIR."/datacentermap/d3city.php");
		$filecontent=file_get_contents("https://api.mysteel.com/dbus/new/pricemap.html?type=0&datetime=2024-05-14&option=kzlwg3&defaultSign=0&_=1715675021063");
		//echo $filecontent;
		$arr=json_decode($filecontent,true);
		//print_r($arr);
		foreach ($arr['data'] as $key => $value) {
			// if(empty($d3city[$value['city']]))
			// {
			// 	echo $value['city']."<br/>";
			// }
			// if($d3city[$value['city']][0]!=$value['address'][0])
			// {
			// 	echo $value['city'].$value['address'][0].",".$value['address'][1]."<br/>";
			// }
			if($value['fillMapColor'])
			{
				echo $value['city']."<br/>";
			}
		}
		echo "11";
	}
	//检查钢之家后台Session
	public function checkSession(){
	//   if( !$_SESSION['dviewpower'] ){
	// 	  alert("无权限查看");
	// 	  goBack();
	//       //goURL( "/admincp/login.php" );
	//   }
	}
   
	public function index1($params)
	{
		$params['view']='index';
		$params['DTIDJson']=html_entity_decode(Urldecode($params["DTIDJson"]));
        $url=DC_URL.'/v1.5/datacentermap.php?'.http_build_query($params);
		$this->assign("url", $url);
		$this->assign("params", $params);
	}
	public function index($params) {
		// echo "<pre>";print_r($this->_dao);

		// https://dc.steelhome.com/v1.5/dcsearch.php?action=CommonSearch&SignCS=6dbe1ca751d67c009e056e2f0d57781e&GUID=b39d29aace0711ee89b5ec2a72108bae&DateStart=2023-02&DateEnd=2024-02&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=1&ImageTitle=&isbg=0&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=1&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"8547","DTIDSub":"","DataImage":"1","zhou":"0","DataType":"4","GongshiID":"0","LineTitle":"utOxsSDNrLHI1PazpA==","unitconver":"0","unitstring":"","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle=

		// https://dc.steelhome.com/v1.5/dcsearch.php?action=CommonSearch&SignCS=6dbe1ca751d67c009e056e2f0d57781e&GUID=b39d29aace0711ee89b5ec2a72108bae&DateStart=2023-02&DateEnd=2024-02&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=1&ImageTitle=&isbg=0&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=1&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"8547","DTIDSub":"","DataImage":"1","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"utOxsSCxvtTCsvrBvw==","unitconver":"5","unitstring":"zfK21g==","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle=

		// https://localdc.steelhome.com/v1.5/datacentermap.php?view=index&SignCS=&GUID=&Date=&ImageTitle=&mc_type=0&mode=1&DTIDJson=[{"DTID":"92474","DataType":"1"}]
		$params['theme']=$params['theme']?$params['theme']:0;
		$data = $this->getmapdatanew($params);
		$this->assign("ImageTitle", $data['ImageTitle']);
		$this->assign("data", $data['data']);
		$this->assign("province", $data['province']);
		$this->assign("params", $params);
	}

	public function getmapdata($params) {
		$linecolor = array('#FD9A9A','#FFDC63','#54E6AB','#489EFF');
		$params['DateEnd'] = empty($params['DateEnd'])?date("Y-m-d"):$params['DateEnd'];
		$mc_type = empty($params['mc_type'])?0:$params['mc_type'];
		// $isEnglish = empty($params['isEnglish'])?0:$params['isEnglish'];
		$DTIDJson=html_entity_decode(Urldecode($params["DTIDJson"]));
		$Datas=json_decode($DTIDJson,true);
		foreach($Datas as $d_k=>$d_v){
			if(($d_v["DTID"] == "" || $d_v["DTID"] == 0)){
				unset($Datas[$d_k]);
			} else {
				$Datas[$d_k]['LineTitle'] = $this->gb2312Toutf8($this->b64decode($d_v["LineTitle"]));
				$Datas[$d_k]['unitstring'] = $this->gb2312Toutf8($this->b64decode($d_v["unitstring"]));
			}
		}
		$params["ImageTitle"]=html_entity_decode(Urldecode($params["ImageTitle"]));
		$ImageTitle = $this->gb2312Toutf8($this->b64decode($params['ImageTitle']));

		$dataarr = array();
		foreach($Datas as $key=>$val){
			$sql_base = "select * from dc_code_datatype,dc_code_datatype_db  where dc_code_datatype.ID='" . $val['DTID'] . "' and dc_code_datatype_db.DID=dc_code_datatype.ID and dc_code_datatype.mc_type='$mc_type' and dc_code_datatype_db.mc_type='$mc_type'";
			$mes = $this->_dao->getrow($sql_base);
			$city = $mes['city'];
			$province = $mes['province'];
			// $city = $testarray[$key][1];
			// $province = $testarray[$key][0];
			if(empty($province))
			{
				continue;
			}
			$mes['techsqlmain']=$mes['techsqlmain'].( $this->compare(strtolower($mes['techtablename']),'marketconditions')?" and isview=0 ":"");
			$main_sql = "select *** from " . $mes['techtablename'] . " " . str_replace("&lt;", "<", str_replace("&gt;", ">", $mes['techsqlmain']));
			foreach ($Datas as $k3 => $v3) {
				$params2['Data' . ($k3 + 1) . 'Type'] = $v3['DataType'];
			}
			$stringdate = 1;
			foreach ($GLOBALS['datefields'] as $datefield) {
				if ($mes['techtablename'] == $datefield) {
					$stringdate = 0;
				}
			}
			if (strstr($mes['techtablename'], 'DataTableBaseInfo')) {
				$stringdate = 0;
			}
			if ($stringdate == 1) {
				if ($mes['dtymd'] == 1 || $mes['dtymd'] == 2) {
					$timesql = " and str_to_date($mes[ddate],'%Y')>=str_to_date('" . $params['DateStart'] . "','%Y') and str_to_date($mes[ddate],'%Y')<=str_to_date('" . $params['DateEnd'] . "','%Y')";
				} else if ($mes['dtymd'] == 3) {
					$timesql = " and str_to_date($mes[ddate],'%Y-%m')>=str_to_date('" . $params['DateStart'] . "','%Y-%m') and str_to_date($mes[ddate],'%Y-%m-%d')<=str_to_date('" . $params['DateEnd'] . "','%Y-%m')";
				} else {
					$timesql = " and str_to_date($mes[ddate],'%Y-%m-%d')>=str_to_date('" . $params['DateStart'] . "','%Y-%m-%d') and str_to_date($mes[ddate],'%Y-%m-%d')<=str_to_date('" . $params['DateEnd'] . "','%Y-%m-%d')";
				}
			} else {
				$timesql = " and $mes[ddate] >= '" . $params['DateStart'] . "' and $mes[ddate] <='" . $params['DateEnd'] . " 23:59:59'";
			}

			// $where = " and ".$mes['ddate']."<='".$date."' and ".$mes['ddate'].">='".$date."' order by ".$mes['ddate']." desc limit 1";
			// $sql_pic = $main_sql . $where;

			$sql_pic = $main_sql . $timesql." order by ".$mes['ddate']." desc limit 1";

			$zd_pic = $this->get_zdpic($mes, $params2, $key + 1);
			if ($zd_pic) {
				$zd_pic_name = implode(",", array_values($zd_pic));
				//print_R($zd_pic);exit;
				if (!in_array($mes['ddate'], $zd_pic)) {
					if ($zd_pic_name == '') {
						$zd_pic_name .= $mes['ddate'];
					} else {
						$zd_pic_name .= "," . $mes['ddate'];
					}
				}
				if (substr($zd_pic_name, -1) == ',') {
					$zd_pic_name = rtrim($zd_pic_name, ',');
				}
				$sql_pic = str_replace("***", $zd_pic_name, $sql_pic);
			}
			$stedao = $this->getotherdao($mes['techdbname']);
			//echo $sql_pic."<br/>";
			$pic = $stedao->execute($sql_pic);
			// $testarray=array(
			// 	array('江苏','南京'),
			// 	array('江苏','镇江'),
			// 	array('江苏','无锡')
			// );
			// echo '<pre>';print_R($pic);

			//$city = $this->get_city($mes['dtname']);
			//$province = $this->get_province($city);
			
			while (!$pic->EOF) {
				foreach ($zd_pic as $k => $v) {
					if ($mes['dtymd'] == 6 && $mes['ddate'] == $v) {
					} else {
						$picc = str_replace(',', '', $pic->fields($v));//数据不规范，去掉数据中的小数点
						if (strstr($picc, '-') && $mes['ddate'] != $v) {//区间价取中间价
							$jiage = explode("-", $picc);
							if ($jiage[0] != "") {
								$picc = round(($jiage[0] + $jiage[1]) / 2, 1);
							}
						}
					}
				}
				$pic->MoveNext();
			}
			// print_R($province);echo '<br>';
			
			// for
			$dataarr[$province][$city]['city'] = $city;
			$dataarr[$province][$city]['dtname'] =$city?$city:($val['LineTitle']?$val['LineTitle']:$mes['dtname']);
			$dataarr[$province][$city]['value'] = $picc;
			$num=count($dataarr[$province]);
			$dataarr[$province][$city]['color'] =$linecolor[($num%count($linecolor)!=0)?$num%count($linecolor)-1:count($linecolor)-1];
			//echo ($num%count($linecolor)-1)."<br/>";
			
		}
		//print_r($dataarr);
		$data = array();
		$dataarr1 = reset($dataarr);
		$data['ImageTitle'] = $ImageTitle;
		$data['data'] = $dataarr1;
		$data['province'] = array_key_first($dataarr);
		

		// echo '<pre>';print_R($data);
		return $data;

	}
	public function getmapdatanew($params) {
		include_once (APP_DIR."/datacentermap/d3city.php");
		$theme = $params['theme']?$params['theme']:0;
		$linecolor = array('#FD9A9A','#FFDC63','#54E6AB','#489EFF');
		$themecolorarr=array(
			'0'=>array('#f4f3f1','#999','#ffb0b0','#ffe0e0','#caf2ab','#aada85'),
			'1'=>array('#012269','#40464b','#AD3250','#DE5B79','#14a880','#14824b'),
			'2'=>array('#f4f3f1','#999','#ffb0b0','#ffe0e0','#caf2ab','#aada85'),
		);
		$this->assign("areaColor", $themecolorarr[$theme][0]);
		$this->assign("borderColor", $themecolorarr[$theme][1]);
		
		$params['DateEnd'] = empty($params['DateEnd'])?date("Y-m-d"):$params['DateEnd'];
		$mc_type = empty($params['mc_type'])?0:$params['mc_type'];
		// $isEnglish = empty($params['isEnglish'])?0:$params['isEnglish'];
		$DTIDJson=html_entity_decode(Urldecode($params["DTIDJson"]));
		$Datas=json_decode($DTIDJson,true);
		foreach($Datas as $d_k=>$d_v){
			if(($d_v["DTID"] == "" || $d_v["DTID"] == 0)){
				unset($Datas[$d_k]);
			} else {
				$Datas[$d_k]['LineTitle'] = $this->gb2312Toutf8($this->b64decode($d_v["LineTitle"]));
				$Datas[$d_k]['unitstring'] = $this->gb2312Toutf8($this->b64decode($d_v["unitstring"]));
			}
		}
		$params["ImageTitle"]=html_entity_decode(Urldecode($params["ImageTitle"]));
		$ImageTitle = $this->gb2312Toutf8($this->b64decode($params['ImageTitle']));

		$dataarr = array();
		$datearr=array();
		$citylist=array();
		$havecity=array();
		foreach($Datas as $key=>$val){

			/*客户数据省份城市
			$mes = [];
			$mysql = "select cataloguename,D1dtymd from dc_customer_data_catalogue where  id='" . $val['DTID'] . "' limit 1";
			$this->P("mysql:" . $mysql . "<br>");

			$dc_customer_data_catalogue = $this->_dao->getRow($mysql);
			$stedao = $this->getotherdao(6);
			$where = " where parentid='" . $val['DTID'] . "' and isdel=0 ";
			$mes['techtablename'] = 'dc_customer_data';
			$mes['ddate'] = 'D1date';
			$mes['dtymd'] = $dc_customer_data_catalogue['D1dtymd'];
			$mes['isSubDatas'] = 0;
            */
			$sql_base = "select * from dc_code_datatype,dc_code_datatype_db  where dc_code_datatype.ID='" . $val['DTID'] . "' and dc_code_datatype_db.DID=dc_code_datatype.ID and dc_code_datatype.mc_type='$mc_type' and dc_code_datatype_db.mc_type='$mc_type'";
			$mes = $this->_dao->getrow($sql_base);
			$city = $mes['city'];
			$province = $mes['province'];
			if(empty($province))
			{
				continue;
			}
			if(in_array($city,$havecity))
			{
				continue;
			}
			else
			{
				$havecity[]=$city;
			}
			$mes['techsqlmain']=$mes['techsqlmain'].( $this->compare(strtolower($mes['techtablename']),'marketconditions')?" and isview=0 ":"");
			$main_sql = "select *** from " . $mes['techtablename'] . " " . str_replace("&lt;", "<", str_replace("&gt;", ">", $mes['techsqlmain']));
			foreach ($Datas as $k3 => $v3) {
				$params2['Data' . ($k3 + 1) . 'Type'] = $v3['DataType'];
			}
			$stringdate = 1;
			foreach ($GLOBALS['datefields'] as $datefield) {
				if ($mes['techtablename'] == $datefield) {
					$stringdate = 0;
				}
			}
			if (strstr($mes['techtablename'], 'DataTableBaseInfo')) {
				$stringdate = 0;
			}
			if ($stringdate == 1) {
				if ($mes['dtymd'] == 1 || $mes['dtymd'] == 2) {
					$timesql = " and str_to_date($mes[ddate],'%Y')>=str_to_date('" . $params['DateStart'] . "','%Y') and str_to_date($mes[ddate],'%Y')<=str_to_date('" . $params['DateEnd'] . "','%Y')";
				} else if ($mes['dtymd'] == 3) {
					$timesql = " and str_to_date($mes[ddate],'%Y-%m')>=str_to_date('" . $params['DateStart'] . "','%Y-%m') and str_to_date($mes[ddate],'%Y-%m-%d')<=str_to_date('" . $params['DateEnd'] . "','%Y-%m')";
				} else {
					$timesql = " and str_to_date($mes[ddate],'%Y-%m-%d')>=str_to_date('" . $params['DateStart'] . "','%Y-%m-%d') and str_to_date($mes[ddate],'%Y-%m-%d')<=str_to_date('" . $params['DateEnd'] . "','%Y-%m-%d')";
				}
			} else {
				$timesql = " and $mes[ddate] >= '" . $params['DateStart'] . "' and $mes[ddate] <='" . $params['DateEnd'] . " 23:59:59'";
			}

			// $where = " and ".$mes['ddate']."<='".$date."' and ".$mes['ddate'].">='".$date."' order by ".$mes['ddate']." desc limit 1";
			// $sql_pic = $main_sql . $where;

			$sql_pic = $main_sql . $timesql." order by ".$mes['ddate']." desc limit 2";

			$zd_pic = $this->get_zdpic($mes, $params2, $key + 1);
			if ($zd_pic) {
				$zd_pic_name = implode(",", array_values($zd_pic));
				//print_R($zd_pic);exit;
				if (!in_array($mes['ddate'], $zd_pic)) {
					if ($zd_pic_name == '') {
						$zd_pic_name .= $mes['ddate'];
					} else {
						$zd_pic_name .= "," . $mes['ddate'];
					}
				}
				if (substr($zd_pic_name, -1) == ',') {
					$zd_pic_name = rtrim($zd_pic_name, ',');
				}
				$sql_pic = str_replace("***", $zd_pic_name, $sql_pic);
			}
			$stedao = $this->getotherdao($mes['techdbname']);
			//echo $sql_pic."<br/>";
			$pic = $stedao->query($sql_pic);
			foreach ($zd_pic as $k => $v) {
				$dateinfo = $this->chetimenew($pic[0][$mes['ddate']]);
				if(!in_array($dateinfo,$datearr))
				{
					$datearr[]=$dateinfo;
				}
				//echo $dateinfo."<br/>";
				if ($mes['ddate'] == $v) {
				} else {
					$picc = str_replace(',', '', $pic[0][$v]);//数据不规范，去掉数据中的小数点
					if (strstr($picc, '-') && $mes['ddate'] != $v) {//区间价取中间价
						$jiage = explode("-", $picc);
						if ($jiage[0] != "") {
							$picc = round(($jiage[0] + $jiage[1]) / 2, 1);
						}
					}
					$picc1 = str_replace(',', '', $pic[1][$v]);//数据不规范，去掉数据中的小数点
					if (strstr($picc1, '-') && $mes['ddate'] != $v) {//区间价取中间价
						$jiage = explode("-", $picc1);
						if ($jiage[0] != "") {
							$picc1 = round(($jiage[0] + $jiage[1]) / 2, 1);
						}
					}
					$citylist[$province][$city]['value']=(float)$picc;
					$citylist[$province][$city]['raise']=(float)$picc-(float)$picc1;
					$citylist[$province][$city]['times']=$dateinfo;
					$citylist[$province][$city]['city']=$city;
					$citylist[$province][$city]['factory']="";
					$citylist[$province][$city]['province']=$province;
					$citylist[$province][$city]['color']=$this->raisecolor($themecolorarr[$theme],(float)$picc-(float)$picc1);
				}
			}
		}

		$data = array();
		$currentdate=date('Y-m-d');
		if(!empty($datearr))
		{
			
			//print_r($datearr);
			rsort($datearr);
			$currentdate=$datearr[0];
			$province=array_key_first($citylist);
			if(count($citylist)>1)
			{
				$citylistnew=array();
				foreach ($citylist as $k => $v) {
				 foreach ($v as $key => $value) {
					if($value['times']!=$currentdate)
					{
						$value['value']='-';
						$value['raise']='-';
						$value['color']=$this->raisecolor($themecolorarr[$theme],0);
					}
					if(isset($d3city[$value['city']]))
					{
						$value['fillMapColor']=$d3city[$value['city']][2];
					}
					else
					{
						$value['fillMapColor']=0;
					}
					$citylistnew[]=$value;
				}
			  }
			  $citylist=array();
			  $citylist['中国']=$citylistnew;
			  $province='中国';
			}
			else{
				foreach ($citylist[$province] as $key => &$value) {
					if($value['times']!=$currentdate)
					{
						$value['value']='-';
						$value['raise']='-';
						$value['color']=$this->raisecolor($themecolorarr[$theme],0);
					}
				}
			}

			
			
			$data = array();
			$data['data'] = reset($citylist);
			$data['province'] = $province;
			
		}
		else
		{
			$data['data']=array();
			$data['province']="";
		}
		$data['ImageTitle'] = !empty($ImageTitle)?date('j日',strtotime($currentdate)).$ImageTitle:"";
		//print_r($data);exit;
		return $data;

	}
	public function raisecolor($colorarr,$raise)
	{
		if($raise>50)
		{
			return $colorarr[2];
		}
		elseif($raise>0&&$raise<=50)
		{
			return $colorarr[3];
		}
		elseif($raise<0&&$raise>=-50)
		{
			return $colorarr[4];
		}
		elseif($raise<-50)
		{
			return $colorarr[5];
		}
		else
		{
			return $colorarr[0];
		}
	}
	public function map($params)
	{

		$classinfo = $params['theme']=='1'?'theme-blue':'';
		$data = $this->getmapdata2($params);
		$this->assign("datajson",json_encode($data));
		$this->assign("classinfo",$classinfo);
	}
	public function getmapdata2($params) {
		include_once (APP_DIR."/datacentermap/d3city.php");
		//$linecolor = array('#FD9A9A','#FFDC63','#54E6AB','#489EFF');
		$params['DateEnd'] = empty($params['DateEnd'])?date("Y-m-d"):$params['DateEnd'];
		$mc_type = empty($params['mc_type'])?0:$params['mc_type'];
		// $isEnglish = empty($params['isEnglish'])?0:$params['isEnglish'];
		$DTIDJson=html_entity_decode(Urldecode($params["DTIDJson"]));
		$Datas=json_decode($DTIDJson,true);
		foreach($Datas as $d_k=>$d_v){
			if(($d_v["DTID"] == "" || $d_v["DTID"] == 0)){
				unset($Datas[$d_k]);
			} else {
				$Datas[$d_k]['LineTitle'] = $this->gb2312Toutf8($this->b64decode($d_v["LineTitle"]));
				$Datas[$d_k]['unitstring'] = $this->gb2312Toutf8($this->b64decode($d_v["unitstring"]));
			}
		}
		//$params["ImageTitle"]=html_entity_decode(Urldecode($params["ImageTitle"]));
		//$ImageTitle = $this->gb2312Toutf8($this->b64decode($params['ImageTitle']));

		$dataarr = array();
		$datearr=array();
		$citylist=array();
		$havecity=array();
		foreach($Datas as $key=>$val){
			$sql_base = "select * from dc_code_datatype,dc_code_datatype_db  where dc_code_datatype.ID='" . $val['DTID'] . "' and dc_code_datatype_db.DID=dc_code_datatype.ID and dc_code_datatype.mc_type='$mc_type' and dc_code_datatype_db.mc_type='$mc_type'";
			$mes = $this->_dao->getrow($sql_base);
			$city = $mes['city'];
			$province = $mes['province'];
			if(empty($province))
			{
				continue;
			}
			if(in_array($city,$havecity))
			{
				continue;
			}
			else
			{
				$havecity[]=$city;
			}
			$mes['techsqlmain']=$mes['techsqlmain'].( $this->compare(strtolower($mes['techtablename']),'marketconditions')?" and isview=0 ":"");
			$main_sql = "select *** from " . $mes['techtablename'] . " " . str_replace("&lt;", "<", str_replace("&gt;", ">", $mes['techsqlmain']));
			foreach ($Datas as $k3 => $v3) {
				$params2['Data' . ($k3 + 1) . 'Type'] = $v3['DataType'];
			}
			$stringdate = 1;
			foreach ($GLOBALS['datefields'] as $datefield) {
				if ($mes['techtablename'] == $datefield) {
					$stringdate = 0;
				}
			}
			if (strstr($mes['techtablename'], 'DataTableBaseInfo')) {
				$stringdate = 0;
			}
			if ($stringdate == 1) {
				if ($mes['dtymd'] == 1 || $mes['dtymd'] == 2) {
					$timesql = " and str_to_date($mes[ddate],'%Y')>=str_to_date('" . $params['DateStart'] . "','%Y') and str_to_date($mes[ddate],'%Y')<=str_to_date('" . $params['DateEnd'] . "','%Y')";
				} else if ($mes['dtymd'] == 3) {
					$timesql = " and str_to_date($mes[ddate],'%Y-%m')>=str_to_date('" . $params['DateStart'] . "','%Y-%m') and str_to_date($mes[ddate],'%Y-%m-%d')<=str_to_date('" . $params['DateEnd'] . "','%Y-%m')";
				} else {
					$timesql = " and str_to_date($mes[ddate],'%Y-%m-%d')>=str_to_date('" . $params['DateStart'] . "','%Y-%m-%d') and str_to_date($mes[ddate],'%Y-%m-%d')<=str_to_date('" . $params['DateEnd'] . "','%Y-%m-%d')";
				}
			} else {
				$timesql = " and $mes[ddate] >= '" . $params['DateStart'] . "' and $mes[ddate] <='" . $params['DateEnd'] . " 23:59:59'";
			}

			// $where = " and ".$mes['ddate']."<='".$date."' and ".$mes['ddate'].">='".$date."' order by ".$mes['ddate']." desc limit 1";
			// $sql_pic = $main_sql . $where;

			$sql_pic = $main_sql . $timesql." order by ".$mes['ddate']." desc limit 2";

			$zd_pic = $this->get_zdpic($mes, $params2, $key + 1);
			if ($zd_pic) {
				$zd_pic_name = implode(",", array_values($zd_pic));
				//print_R($zd_pic);exit;
				if (!in_array($mes['ddate'], $zd_pic)) {
					if ($zd_pic_name == '') {
						$zd_pic_name .= $mes['ddate'];
					} else {
						$zd_pic_name .= "," . $mes['ddate'];
					}
				}
				if (substr($zd_pic_name, -1) == ',') {
					$zd_pic_name = rtrim($zd_pic_name, ',');
				}
				$sql_pic = str_replace("***", $zd_pic_name, $sql_pic);
			}
			$stedao = $this->getotherdao($mes['techdbname']);
			//echo $sql_pic."<br/>";
			$pic = $stedao->query($sql_pic);

			// $testarray=array(
			// 	array('江苏','南京'),
			// 	array('江苏','镇江'),
			// 	array('江苏','无锡')
			// );
			// echo '<pre>';print_R($pic);

			//$city = $this->get_city($mes['dtname']);
			//$province = $this->get_province($city);
			

            foreach ($zd_pic as $k => $v) {
				$dateinfo = $this->chetimenew($pic[0][$mes['ddate']]);
				if ($mes['ddate'] == $v) {
					//$dateinfo = $this->chetimenew($pic[0][$v]);
					if(!in_array($dateinfo,$datearr))
					{
						$datearr[]=$dateinfo;
					}
				} else {
					$picc = str_replace(',', '', $pic[0][$v]);//数据不规范，去掉数据中的小数点
					if (strstr($picc, '-') && $mes['ddate'] != $v) {//区间价取中间价
						$jiage = explode("-", $picc);
						if ($jiage[0] != "") {
							$picc = round(($jiage[0] + $jiage[1]) / 2, 1);
						}
					}
					$picc1 = str_replace(',', '', $pic[1][$v]);//数据不规范，去掉数据中的小数点
					if (strstr($picc1, '-') && $mes['ddate'] != $v) {//区间价取中间价
						$jiage = explode("-", $picc1);
						if ($jiage[0] != "") {
							$picc1 = round(($jiage[0] + $jiage[1]) / 2, 1);
						}
					}
					$citylist[$dateinfo][$city]['value']=(float)$picc;
					$citylist[$dateinfo][$city]['raise']=(float)$picc-(float)$picc1;
					$citylist[$dateinfo][$city]['times']=$dateinfo;
					$citylist[$dateinfo][$city]['address']=array($d3city[$city][0],$d3city[$city][1]);
					$citylist[$dateinfo][$city]['city']=$city;
					$citylist[$dateinfo][$city]['factory']="";
					$citylist[$dateinfo][$city]['fillMapColor']=$d3city[$city][2]==1?true:false;
					$citylist[$dateinfo][$city]['province']=$province;
					
				}
			}
			
		}
		

		if(!empty($datearr))
		{
			//print_r($citylist);
			rsort($datearr);
			$currentdate=$datearr[0];
			$data = array();
			foreach($citylist[$currentdate] as $k=>$v)
			{
				$data['data'][]= $v;
			}
		}
		else
		{
			$data['data']=array();
		}
		
		return $data;

	}
	function chetimenew($vy)
    {
        $format_rq = explode("-", $vy);
        $num_rq = count($format_rq);
        if ($num_rq == 1) {
            $new_rq = date("Y", strtotime($format_rq[0] . "-01-01"));
        } elseif ($num_rq == 2) {
            $new_rq = date("Y-m", strtotime($format_rq[0] . "-" . $format_rq[1] . "-01"));
        } else {
            $new_rq = date("Y-m-d", strtotime($format_rq[0] . "-" . $format_rq[1] . "-" . $format_rq[2]));
        }
        return $new_rq;
    }
	//获取生成表格图片数据库链接
    public function getotherdao($techdbname)
    {
        switch ($techdbname) {
            case '2':
                include_once(APP_DIR . "/dcsearch/DrcDao.inc.php");
                $stedao = new DrcDao('DRC');
                break;
            case '3':
                include_once(APP_DIR . "/dcsearch/GcDao.inc.php");
                $stedao = new GcDao('GC');
                break;
            case '4':
                include_once(APP_DIR . "/dcsearch/HgDao.inc.php");
                $stedao = new HgDao('91R');
                break;
            case '5':
                include_once(APP_DIR . "/dcsearch/SthDao.inc.php");
                $stedao = new SthDao('91R');
                break;
            //add by zfy started 2016/9/12
            case '6':
                include_once(APP_DIR . "/dcsearch/DrcDao.inc.php");
                $stedao = new DrcDao('MAIN');
                break;
            //add by zfy ended 2016/9/12
            case '8':
                include_once(APP_DIR . "/dcsearch/XgDao.inc.php");
                $stedao = new XgDao('91R');
                break;
            case '9':
                include_once(APP_DIR . "/dcsearch/SgDao.inc.php");
                $stedao = new SgDao('91R');
                break;
            default:
                include_once(APP_DIR . "/dcsearch/DrcDao.inc.php");
                $stedao = new DrcDao('MAIN');
        }
        return $stedao;
    }

	public function get_city($citystr)
	{
		$arr = explode(" ",$citystr);
		return $arr[0];
	}

	// 采用城市名找到省份名称的方法
	public function get_province($area)
	{

		$provinces = array("北京", "天津", "上海", "重庆", "新疆", "西藏", "宁夏", "内蒙古",
             "广西", "黑龙江", "吉林", "辽宁", "河北", "山东", "江苏", "安徽",
             "浙江", "福建", "广东", "海南", "云南", "贵州", "四川", "湖南",
             "湖北", "河南", "山西", "陕西", "甘肃", "青海", "江西", "台湾", "香港", "澳门");
		if (in_array($area, $provinces)) {
			$currpro = $area;
		} else {
			$allcitys = array(
	
				array("北京"),
				
				array("上海"),
				
				array("天津"),
				
				array("重庆"),
				
				array("哈尔滨", "齐齐哈尔", "牡丹江", "大庆", "伊春", "双鸭山", "鹤岗", "鸡西", "佳木斯", "七台河", "黑河", "绥化", "大兴安岭"),
				
				array("长春", "延边", "吉林", "白山", "白城", "四平", "松原", "辽源", "大安", "通化"),
				
				array("沈阳", "大连", "葫芦岛", "旅顺", "本溪", "抚顺", "铁岭", "辽阳", "营口", "阜新", "朝阳", "锦州", "丹东", "鞍山"),
				
				array("呼和浩特", "呼伦贝尔", "锡林浩特", "包头", "赤峰", "海拉尔", "乌海", "鄂尔多斯", "通辽"),
				
				array("石家庄", "唐山", "张家口", "廊坊", "邢台", "邯郸", "沧州", "衡水", "承德", "保定", "秦皇岛"),
				
				array("郑州", "开封", "洛阳", "平顶山", "焦作", "鹤壁", "新乡", "安阳", "濮阳", "许昌", "漯河", "三门峡", "南阳", "商丘", "信阳", "周口", "驻马店"),
				
				array("济南", "青岛", "淄博", "威海", "曲阜", "临沂", "烟台", "枣庄", "聊城", "济宁", "菏泽", "泰安", "日照", "东营", "德州", "滨州", "莱芜", "潍坊"),
				
				array("太原", "阳泉", "晋城", "晋中", "临汾", "运城", "长治", "朔州", "忻州", "大同", "吕梁"),
				
				array("南京", "苏州", "昆山", "南通", "太仓", "吴县", "徐州", "宜兴", "镇江", "淮安", "常熟", "盐城", "泰州", "无锡", "连云港", "扬州", "常州", "宿迁"),
				
				array("合肥", "巢湖", "蚌埠", "安庆", "六安", "滁州", "马鞍山", "阜阳", "宣城", "铜陵", "淮北", "芜湖", "毫州", "宿州", "淮南", "池州"),
				
				array("西安", "韩城", "安康", "汉中", "宝鸡", "咸阳", "榆林", "渭南", "商洛", "铜川", "延安"),
				
				array("银川", "固原", "中卫", "石嘴山", "吴忠"),
				
				array("兰州", "白银", "庆阳", "酒泉", "天水", "武威", "张掖", "甘南", "临夏", "平凉", "定西", "金昌"),
				
				array("西宁", "海北", "海西", "黄南", "果洛", "玉树", "海东", "海南"),
				
				array("武汉", "宜昌", "黄冈", "恩施", "荆州", "神农架", "十堰", "咸宁", "襄樊", "孝感", "随州", "黄石", "荆门", "鄂州"),
				
				array("长沙", "邵阳", "常德", "郴州", "吉首", "株洲", "娄底", "湘潭", "益阳", "永州", "岳阳", "衡阳", "怀化", "韶山", "张家界"),
				
				array("杭州", "湖州", "金华", "宁波", "丽水", "绍兴", "雁荡山", "衢州", "嘉兴", "台州", "舟山", "温州"),
				
				array("南昌", "萍乡", "九江", "上饶", "抚州", "吉安", "鹰潭", "宜春", "新余", "景德镇", "赣州"),
				
				array("福州", "厦门", "龙岩", "南平", "宁德", "莆田", "泉州", "三明", "漳州"),
				
				array("贵阳", "安顺", "赤水", "遵义", "铜仁", "六盘水", "毕节", "凯里", "都匀"),
				
				array("成都", "泸州", "内江", "凉山", "阿坝", "巴中", "广元", "乐山", "绵阳", "德阳", "攀枝花", "雅安", "宜宾", "自贡", "甘孜州", "达州", "资阳", "广安", "遂宁", "眉山", "南充"),
				
				array("广州", "深圳", "潮州", "韶关", "湛江", "惠州", "清远", "东莞", "江门", "茂名", "肇庆", "汕尾", "河源", "揭阳", "梅州", "中山", "德庆", "阳江", "云浮", "珠海", "汕头", "佛山"),
				
				array("南宁", "桂林", "阳朔", "柳州", "梧州", "玉林", "桂平", "贺州", "钦州", "贵港", "防城港", "百色", "北海", "河池", "来宾", "崇左"),
				
				array("昆明", "保山", "楚雄", "德宏", "红河", "临沧", "怒江", "曲靖", "思茅", "文山", "玉溪", "昭通", "丽江", "大理"),
				
				array("海口", "三亚", "儋州", "琼山", "通什", "文昌"),
				
				array("乌鲁木齐", "阿勒泰", "阿克苏", "昌吉", "哈密", "和田", "喀什", "克拉玛依", "石河子", "塔城", "库尔勒", "吐鲁番", "伊宁"),
				
				array("拉萨","昌都地区","山南地区","阿里地区","那曲地区","林芝地区","日喀则地区"),
				
				array("香港"),
				
				array("澳门"),
				
				array("台湾"),
				
			);
			
			$pro[0]="北京";
			
			$pro[1]="上海";
			
			$pro[2]="天津";
			
			$pro[3]="重庆";
			
			$pro[4]="黑龙江";
			
			$pro[5]="吉林";
			
			$pro[6]="辽宁";
			
			$pro[7]="内蒙古";
			
			$pro[8]="河北";
			
			$pro[9]="河南";
			
			$pro[10]="山东";
			
			$pro[11]="山西";
			
			$pro[12]="江苏";
			
			$pro[13]="安徽";
			
			$pro[14]="陕西";
			
			$pro[15]="宁夏";
			
			$pro[16]="甘肃";
			
			$pro[17]="青海";
			
			$pro[18]="湖北";
			
			$pro[19]="湖南";
			
			$pro[20]="浙江";
			
			$pro[21]="江西";
			
			$pro[22]="福建";
			
			$pro[23]="贵州";
			
			$pro[24]="四川";
			
			$pro[25]="广东";
			
			$pro[26]="广西";
			
			$pro[27]="云南";
			
			$pro[28]="海南";
			
			$pro[29]="新疆";
			
			$pro[30]="西藏";
			
			$pro[31]="香港";
			
			$pro[32]="澳门";
			
			$pro[33]="台湾";
			
			for ($i = 0; $i < count($allcitys); $i++)
			{
			
				for ($j = 0;$j < count($allcitys[$i]); $j++)
				{
					if ($allcitys[$i][$j] == $area)
					{
						$currpro = $pro[$i];
					}
					
				}
			}
		}


		return $currpro;
	}

	 //获取图片所需线
	 public function get_zdpic($mes, $params, $type)
	 {
		 if ($type == 1) {
			 $zd = array($mes['ndate'] => $mes['ddate']);
			 //$zd[$mes['ndata1']] = $mes['Ddata1'];
			 if ($params['Data' . $type . 'Type1'] == 1) {
				 if ($mes['Ddata1'] != "") {
					 $zd[$mes['ndata1']] = $mes['Ddata1'];
				 }
			 }
 
			 if ($params['Data' . $type . 'Type2'] == 1) {
				 if ($mes['Ddata2'] != "") {
					 $zd[$mes['ndata2']] = $mes['Ddata2'];
				 }
			 }
			 if ($params['Data' . $type . 'Type3'] == 1) {
				 if ($mes['Ddata3'] != "") {
					 $zd[$mes['ndata3']] = $mes['Ddata3'];
				 }
			 }
			 if ($params['Data' . $type . 'Type4'] == 1) {
				 if ($mes['Ddata4'] != "") {
					 $zd[$mes['ndata4']] = $mes['Ddata4'];
				 }
			 }
			 if ($params['Data' . $type . 'Type5'] == 1) {
				 if ($mes['Ddata5'] != "") {
					 $zd[$mes['ndata5']] = $mes['Ddata5'];
				 }
			 }
 
			 if ($params['Data' . $type . 'Pre'] == 1) {
				 if ($mes['dpredata'] != "") {
					 $zd[$mes['npredata']] = $mes['dpredata'];
				 }
			 }
			 if ($params['Data' . $type . 'AddSub'] == 1) {
				 if ($mes['daddsubdata'] != "") {
					 $zd[$mes['naddsubdata']] = $mes['daddsubdata'];
				 }
			 }
			 if ($params['Data' . $type . 'AddSubHundCore'] == 1) {
				 if ($mes['daddsubhundcore'] != "") {
					 $zd[$mes['naddsubhundcore']] = $mes['daddsubhundcore'];
				 }
			 }
			 //add
			 if ($params['Data1Type'] == 1) {
				 $zd[$mes['ndata1']] = $mes['Ddata1'];
			 }
			 if ($params['Data1Type'] == 2) {
				 $zd[$mes['npredata']] = $mes['dpredata'];
			 }
			 if ($params['Data1Type'] == 3) {
				 $zd[$mes['naddsubdata']] = $mes['daddsubdata'];
			 }
			 if ($params['Data1Type'] == 4) {
				 $zd[$mes['naddsubhundcore']] = $mes['daddsubhundcore'];
			 }
			 if ($params['Data1Type'] == 12) {
				 $zd[$mes['ndata2']] = $mes['Ddata2'];
			 }
			 if ($params['Data1Type'] == 13) {
				 $zd[$mes['ndata3']] = $mes['Ddata3'];
			 }
			 if ($params['Data1Type'] == 14) {
				 $zd[$mes['ndata4']] = $mes['Ddata4'];
			 }
			 if ($params['Data1Type'] == 15) {
				 $zd[$mes['ndata5']] = $mes['Ddata5'];
			 }
			 //end
			 $nn = (int)$params['Data1Type'];
			 if ($nn > 15) {
				 $zd[$mes['ndata' . ($nn - 10)]] = $mes['Ddata' . ($nn - 10)];
			 }
		 } else {
			 if ($params['Data' . $type . 'Type'] == 1) {
				 $zd[$mes['ndata1']] = $mes['Ddata1'];
			 }
			 if ($params['Data' . $type . 'Type'] == 12) {
				 $zd[$mes['ndata2']] = $mes['Ddata2'];
			 }
			 if ($params['Data' . $type . 'Type'] == 13) {
				 $zd[$mes['ndata3']] = $mes['Ddata3'];
			 }
			 if ($params['Data' . $type . 'Type'] == 14) {
				 $zd[$mes['ndata4']] = $mes['Ddata4'];
			 }
			 if ($params['Data' . $type . 'Type'] == 15) {
				 $zd[$mes['ndata5']] = $mes['Ddata5'];
			 }
			 if ($params['Data' . $type . 'Type'] == 16) {
				 $zd[$mes['ndata6']] = $mes['Ddata6'];
			 }
			 if ($params['Data' . $type . 'Type'] == 17) {
				 $zd[$mes['ndata7']] = $mes['Ddata7'];
			 }
			 if ($params['Data' . $type . 'Type'] == 18) {
				 $zd[$mes['ndata8']] = $mes['Ddata8'];
			 }
			 if ($params['Data' . $type . 'Type'] == 19) {
				 $zd[$mes['ndata9']] = $mes['Ddata9'];
			 }
			 if ($params['Data' . $type . 'Type'] == 20) {
				 $zd[$mes['ndata10']] = $mes['Ddata10'];
			 }
			 if ($params['Data' . $type . 'Type'] == 21) {
				 $zd[$mes['ndata11']] = $mes['Ddata11'];
			 }
			 if ($params['Data' . $type . 'Type'] == 22) {
				 $zd[$mes['ndata12']] = $mes['Ddata12'];
			 }
			 if ($params['Data' . $type . 'Type'] == 23) {
				 $zd[$mes['ndata13']] = $mes['Ddata13'];
			 }
			 if ($params['Data' . $type . 'Type'] == 24) {
				 $zd[$mes['ndata14']] = $mes['Ddata14'];
			 }
			 if ($params['Data' . $type . 'Type'] == 25) {
				 $zd[$mes['ndata15']] = $mes['Ddata15'];
			 }
			 if ($params['Data' . $type . 'Type'] == 26) {
				 $zd[$mes['ndata16']] = $mes['Ddata16'];
			 }
			 if ($params['Data' . $type . 'Type'] == 27) {
				 $zd[$mes['ndata17']] = $mes['Ddata17'];
			 }
			 if ($params['Data' . $type . 'Type'] == 28) {
				 $zd[$mes['ndata18']] = $mes['Ddata18'];
			 }
			 if ($params['Data' . $type . 'Type'] == 29) {
				 $zd[$mes['ndata19']] = $mes['Ddata19'];
			 }
			 if ($params['Data' . $type . 'Type'] == 30) {
				 $zd[$mes['ndata20']] = $mes['Ddata20'];
			 }
			 if ($params['Data' . $type . 'Type'] == 2) {
				 $zd[$mes['npredata']] = $mes['dpredata'];
			 }
			 if ($params['Data' . $type . 'Type'] == 3) {
				 $zd[$mes['naddsubdata']] = $mes['daddsubdata'];
			 }
			 if ($params['Data' . $type . 'Type'] == 4) {
				 $zd[$mes['naddsubhundcore']] = $mes['daddsubhundcore'];
			 }
		 }
 
		 //modified by zhangcun start 2018/8/1
		 //print_r($zd);
		 foreach ($zd as $k => $v) {
			 $vv1 = $k;
			 $vv2 = $v;
		 }
		 if ($type == 1) $zd = array($mes['ndate'] => $mes['ddate']);
		 else $zd = array();
		 $zd[$vv1] = $vv2;
		 //print_r($zd);echo"<br>";//exit;
		 //modified by zhangcun end 2018/8/1
 
		 if ($params['AvgLineType']) {
			 $tablenames = array("shpi_coal", "shpi_material", "shpi_material_pzp", "shpi_mj_pzp", "shpi_pi", "shpi_pp", "shpi_pzggp", "shpi_pzp");
			 $columnnames = array("weiprice", "weiindex", "price", "mindex", "weipriceusb", "aveprice", "apindex", "wpindex");
			 if ($params['isEnglish']) $name = "-day average line";
			 else $name = "日均线";
			 $types = explode(",", $params['AvgLineType']);
			 sort($types);
			 foreach ($zd as $z) {
				 if ($mes['techtablename'] == "marketconditions" && ($z == "price" || $z == "pricemk")) {
					 //echo 'in';
					 foreach ($types as $type) {
						 switch ($type) {
							 case 5:
								 $zd["5" . $name] = "average_price_5";
								 break;
							 case 10:
								 $zd["10" . $name] = "average_price_10";
								 break;
							 case 20:
								 $zd["20" . $name] = "average_price_20";
								 break;
							 case 40:
								 $zd["40" . $name] = "average_price_40";
								 break;
							 case 60:
								 $zd["60" . $name] = "average_price_60";
								 break;
							 case 200:
								 $zd["200" . $name] = "average_price_200";
								 break;
						 }
					 }
					 continue;
				 }
				 if (($mes['techdbname'] != 5 && $mes['techdbname']) || !in_array($mes['techtablename'], $tablenames) || !in_array($z, $columnnames)) continue;
				 foreach ($types as $type) {
					 switch ($type) {
						 case 5:
							 $zd["5" . $name] = $z . "_avg_5";
							 break;
						 case 10:
							 $zd["10" . $name] = $z . "_avg_10";
							 break;
						 case 20:
							 $zd["20" . $name] = $z . "_avg_20";
							 break;
						 case 40:
							 $zd["40" . $name] = $z . "_avg_40";
							 break;
						 case 60:
							 $zd["60" . $name] = $z . "_avg_60";
							 break;
						 case 200:
							 $zd["200" . $name] = $z . "_avg_200";
							 break;
					 }
				 }
			 }
		 }
		 return $zd;
	 }

	public function compare($a, $b)
    {
        if (strpos($a, $b) !== false) {
            return true;
        } else {
            return false;
        }
    }

	function b64decode($name)
    {
       if (strstr($name, " ")) {
           $name = str_replace(" ", "+", $name);
       }
        // $name = urldecode($name);
        return base64_decode($name);
    }

	public function gb2312Toutf8($str)
    {
        $str = trim( $str );
        $encode = mb_detect_encoding($str, array('UTF-8','CP936', "ASCII", "GB2312", "GBK",  'BIG5'));
        if ($encode == 'UTF-8') {
            return $str;
        } elseif ($encode == 'CP936') {
            return iconv('CP936', 'UTF-8//IGNORE', $str);
        } else {
            return mb_convert_encoding($str, 'UTF-8', "GBK");
        }
    }

	

		
		

}
?>
