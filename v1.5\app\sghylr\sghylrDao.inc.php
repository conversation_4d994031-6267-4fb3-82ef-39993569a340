<?php
class sghylr<PERSON>ao extends Dao{
  public function __construct( $writer ){
    parent::__construct( $writer );
  }

  //获取品种的一段时间的和
  public function get_HangYeChengBenIndex($typearr, $mc_type, $date){		
    $types = implode("','", $typearr);
    $sql = "select * from sg_HangYeChengBenIndex where type in ('".$types."') and mc_type='".$mc_type."' and ndate = '".$date."'";	  
    // echo $sql."<br>";
    $chengbeninfo = $this->query($sql);
    $chengbenarr = array();
    foreach ($chengbeninfo as $v) {
      $chengbenarr[$v['type']] = $v['chenben_tax'];
    }
    return $chengbenarr;
  }

}
?>