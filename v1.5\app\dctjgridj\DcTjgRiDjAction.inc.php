<?php 

$GLOBALS['type_date']=date("Y-m-d");
$GLOBALS['type_Y']=date("Y");
$GLOBALS['type_Y_m']=date("Y-m");
//$GLOBALS['type_date_h']=date("Y-m-d H:m:s");
// require_once('../../../../steelconf_v3/debug.php');
class DcTjgRiDjAction extends AbstractAction
{
    public $stedao;

    public function __construct()
    {
        parent::__construct();
    } 
    public function index($params)
    {
        //echo "hello world";
		//print_r($GLOBALS['date']);
		//print_r($params);
		if(isset($params['curdate'])){
			//$GLOBALS['date']=$params['curdate'];
			$GLOBALS['type_date']=$params['curdate'];
			$GLOBALS['type_Y']=date("Y",strtotime($GLOBALS['type_date']));
			$GLOBALS['type_Y_m']=date("Y-m",strtotime($GLOBALS['type_date']));
			//$GLOBALS['type_date_h']=date("Y-m-d H:m:s",strtotime($GLOBALS['type_date']));
			//print_r($GLOBALS['type_date_h']);
		}
		$r=IsNgWorkDay($this->maindao,$GLOBALS['type_date']);
		//print_r($r);exit;
		if($r==0){
			echo "今天为休息日,不更新数据！";
			exit;
		}else{
	//	$d=date("Y-m");
		//$now=date("Y-m-d");
		$d=$GLOBALS['type_Y_m'];
		$now=$GLOBALS['type_date'];



		$date_arr=$this->workday($now);//exit;南钢的前一个工作日
		$row_ng_data=$this->ngxs($date_arr);//南钢碳结钢销售情况
		$this->miansc_jgbianh($now);//主要市场价格变化
		$new_ctarray=$this->ctjiag($now);//厂提竞争对手价格
		$RB_zd=$this->qhsp_ypbh($now);//煤焦期货表
		$this->dzprice_t($now);//大宗商品价格
		$this->gzjyuc();//钢之家预测
		//print_r($RB_zd);exit;
		$nj_arr=$this->djjy($row_ng_data,$RB_zd);//南京市场模型定价
		$wx_ct=$new_ctarray['tjgwx']['the_price_tax'];//无锡厂提价
		$hz_ct=$new_ctarray['tjghz']['the_price_tax'];
		$sql_ng="select DataMark,Value from ng_data_table where DataMark in ('Ngyg0020','Ngyg0021')
       		  and dta_ym ='".$GLOBALS['type_Y_m']."' order by dta_ym desc ;";
		$res=$this->ngdao->aquery($sql_ng);
		$wxscang_jhlyue=$res['Ngyg0020'];
		$hzscang_jhlyue=$res['Ngyg0021'];
		//本月南钢所有的工作日
			//$wx_arr=$this->wx_djjy($row_ng_data,$wx_ct,$wxscang_jhlyue);//无锡市场模型定价
			//$hz_arr=$this->hz_djjy($row_ng_data,$hz_ct,$hzscang_jhlyue);//杭州市场模型定价
		//print_r($wxscang_jhlyue);exit;
		/*$sql_c="select modelcontent from ng_price_model where id=973";
		$
		$html=html_entity_decode();*/
		if($_GET['issave']==1){
		//保存数据
			
		$sql="insert into `ng_price_model`(date,modeltype,createtime) values('".$GLOBALS['type_date']."','1',now())";
		// print_r($sql);//exit;
		$this->ngdao->execute($sql);
		$modelid=$this->ngdao->insert_id();
		
		
		$ins_njrdj="insert into ng_price_model_detail(modelid,modelprice,modelprice_updown,modeloldprice,modelprice_type,	modelpirce_name,modelpirce_name_sort,Mid,uid,GUID,Ismakepricy_men,date,createtime) value('".$modelid."','".$nj_arr['nj_moxdj']."','".$nj_arr['nj_zd']."','".$nj_arr['oldprice']."','1','45#Ф50mm碳素结构钢日定价','1','-1','-1','','0','".$GLOBALS['type_date']."',now())";
		$this->ngdao->execute($ins_njrdj);
		
	
		//print_r($wx_arr);exit;
		/*$ins_wxrdj="insert into ng_price_model_detail(modelid,modelprice,modelprice_updown,modeloldprice,modelprice_type,	modelpirce_name,modelpirce_name_sort,Mid,uid,GUID,Ismakepricy_men,date,createtime) value('".$modelid."','".$wx_arr['wx_moxdj']."','".$wx_arr['wx_zd']."','".$wx_arr['oldprice']."','1','45#Ф50mm碳素结构钢无锡市场日定价','2','-1','-1','','0','".$GLOBALS['type_date']."',now())";
		//print_r($ins_wxrdj);
		//$this->ngdao->execute($ins_wxrdj);
		
		
		$ins_hzrdj="insert into ng_price_model_detail(modelid,modelprice,modelprice_updown,modeloldprice,modelprice_type,	modelpirce_name,modelpirce_name_sort,Mid,uid,GUID,Ismakepricy_men,date,createtime) value('".$modelid."','".$hz_arr['hz_moxdj']."','".$hz_arr['hz_zd']."','".$hz_arr['oldprice']."','1','45#Ф50mm碳素结构钢杭州市场日定价','3','-1','-1','','0','".$GLOBALS['type_date']."',now())";
		//$this->ngdao->execute($ins_hzrdj);*/
		
		}
		//print_r($date_arr);
		$title_date=date("Y年n月j日",strtotime($date_arr[1]));
		$this->assign("title_date",$title_date);
		$this->assign("last_daye",$date_arr[1]);
		$this->assign("date_arr",$date_arr);
		$this->assign("d",$d);
		$title_now=date("Y年n月j日",strtotime($now));
		$this->assign("now",$title_now);
		$this->assign("content",$content);
		}
      
       } 
	//南钢南钢碳结钢销售情况
	public function ngxs($date_arr)
	{
		
		$sql="select Value,DataMark,dta_ym  from ng_data_table where DataMark in ('Ngyg0023','Ngyg0021','Ngyg0022','Ngyg0008',
              'Ngyg0011','Ngyg0024','Ngyg0027','Ngyg0025','Ngyg0015','Ngyg0026','Ngyg0028','Ngyg0031','Ngyg0029','Ngyg0030','Ngyg0032','Ngyg0034','Ngyg0036','Ngyg0037','Ngyg0039','Ngyg0038','Ngyg0040','Ngyg0001','Ngyg0002','Ngyg0003','Ngyg0001-1','Ngyg0002-1','Ngyg0003-1')
       		  and   dta_ym <'".$GLOBALS['type_date']."' and   dta_ym >='".date("Y-m-d",strtotime("-15 day",strtotime($date_arr[1])))."' order by dta_ym desc ;";
		
       $row_ng=$this->ngdao->query($sql);
	   
	  // $data_array
	  //$res_tjghz=$this->maindao->query($tjg_hzsql);
		//print_r($sql);exit;
		$d = array();//获取工作日日期
		$row_ng_data = array();
		foreach($row_ng as $tmp){
			$day =$tmp['dta_ym'];
			if(!in_array($day,$d))
			{
				$d[]=$day;
			}
			if(strpos($tmp['DataMark'],'-') !== false){
				$datemark=str_replace('-','_',$tmp['DataMark']);
			}else{
				$datemark=$tmp['DataMark'];
			}
			if($tmp['Value']==""){
				$tmp['Value']=="-";
			}
			$row_ng_data[$day][$datemark]= round($tmp['Value']);
			//print_r($day);
		}
		$d=array_slice($d,0,7);
		$this->assign("day",$d);
		$ngday=$d[0];
		if(empty($row_ng)){
			$ngday=$this->workday_gzj($GLOBALS['type_date']);
			//print_r($ngday);
		}
		//print_r($ngday);
		$ngday=date("Y年n月j日",strtotime($ngday));
		$this->assign("ngday",$ngday);
		//$this->djjy($row_ng_data);
		$this->assign("row_ng_data",$row_ng_data);
		//print_r($row_ng_data);
		return $row_ng_data;
		//print_r($row_ng_data);echo "<pre>";
		
	}
	public function miansc_jgbianh($params){
		//print_r(1111);exit;
		$last_day=$this->workday_gzj($params);
		$last_day_s=$last_day." "."00:00:00";
		$last_day_e=$last_day." "."23:59:59";
		$mastertopid="('1122446','1322434','1222445','0822443','8722441','1122442','1322441','1222441','0822442','8722442','0822441','8722447','1122444','1322436','1222439','0822444','8722454','0822447','1122543','1322545','1222543','0822548','8722531','1122544','1322541','1222542','0822542','8722541','1322533','0822541','122243a')";
		$sql="select articlename,material,factoryarea,oldpricemk as oldprice,pricemk as price,mastertopid from marketconditions where mastertopid in ".$mastertopid."and mconmanagedate > '".$last_day_s."' and mconmanagedate < '".$last_day_e."'";//7位价格id
		$all_data=$this->maindao->query($sql);
		$new_array=array();
		
		//国内主要市场螺纹钢价格变化
		$topicture="('112023','122023','072023','082023','132023','232023','222023','362023')";
		$t_sql="select articlename,material,factoryarea,oldpricemk as oldprice,pricemk as price,topicture from marketconditions where topicture in ".$topicture."and mconmanagedate > '".$last_day_s."' and mconmanagedate < '".$last_day_e."'";//六位价格id
		$all_t_data=$this->maindao->query($t_sql);
		$new_array=array();
		$new_tarray=array();
		//print_r($all_t_data);exit;
		
		//print_r($all_data);exit;七位价格id对应数据
		foreach($all_data as $val){
			
			$val['zd'] = $val['price'] - $val['oldprice'];
			$val['zd']=$this->zd_color($val['zd']);
			//print_r($val['zd']);exit;
			if($val['zd']=="" && $val['zd']!="0"){
				$val['zd']="-";
			}
			if($val['price']==""){
				$val['price']="-";
			}
			/*if($val['oldprice']==""){
				$val['oldprice']="-";
			}*/
			$new_array[$val['mastertopid']]=$val;
		}
		//六位价格id对应数据
		foreach($all_t_data as $val){
			$val['zd'] = $val['price'] - $val['oldprice'];
			$val['zdfu']=round($val['zd']/$val['oldprice']*100,2);
			$val['zdfu']=$this->zd_color($val['zdfu']."%");
			$sum_zd+=$val['zd'];
			$val['zd']=$this->zd_color($val['zd']);
			if($val['zd']=="" && $val['zd']!="0"){
				$val['zd']="-";
			}
			if($val['price']==""){
				$val['price']="-";
			}
			
			$new_tarray[$val['topicture']]=$val;
		}
		
		$avg=array();
		foreach($new_tarray as $val){
			$sum_price+=$val['price'];
		}
		$avg['price']=round($sum_price/8);
		$avg['zd']=$this->zd_color(round($sum_zd/8));
		$avg['zdfu']=$this->zd_color(round(($sum_zd/8)/($sum_price/8)*100,2)."%");
		$this->assign("avg",$avg);
		$mainday=date("Y年n月j日",strtotime($last_day));
		$this->assign("mainday",$mainday);
		$this->assign("new_array",$new_array);
		$this->assign("new_tarray",$new_tarray);
		//echo "<pre>";
		//print_r($new_array);
		
		//print_r($new_array);exit;
		
	}
	//期货夜盘收盘价格与涨跌
	public function qhsp_ypbh($params){
		/*$last_day=$this->workday_gzj($params);
		$n=date("N",strtotime($last_day));
		if($n==6){
			$last_day=$this->workday_gzj($last_day);
		}else if($n==7){
			$last_day = date("Y-m-d",strtotime("$last_day - 1 day"));
			$last_day=$this->workday_gzj($last_day);
		}*/
		$code="('RB','HC','I','JM','J')";
		$sql="select price,zhangdie,day,code,id from steelhome.kmjg_qhprice where code in ".$code."  and time<'17:00:00' and day<='".$GLOBALS['type_date']."' order by day desc limit 5";
		//print_r($sql);exit;
		$res=$this->maindao->query($sql);
		$temp_id=array();
		foreach($res as $key=>$val){
			$temp_id[]=$val['id'];
		}
		sort($temp_id);
		//print_r($temp_id);exit;
		//print_r($res);exit;
		//$qihday=date("Y年n月j日",strtotime($last_day));
		//$this->assign("qihday",$qihday);
		//$last_day=$this->workday_gzj($last_day);
		//$code="('RB','HC','I','JM','J')";
		$sql_t="select price,zhangdie,day,code from steelhome.kmjg_qhprice where code in ".$code." and id<'".$temp_id[0]."'  order by id desc limit 5";
		$res_t=$this->maindao->query($sql_t);
		$price_arr=array();
		
		//print_r($res_t);exit;
		foreach ($res as $price){
			if($price['price']==""){
				$price['price']="-";
			}
			foreach($res_t as $key=>$val){
				if($val['code']==$price['code']){
					if($val["code"]=="I"||$val["code"]=="JM"||$val["code"]=="J") {
						$price['zd']=sprintf("%.1f",$price['price']-$val['price']);
						$price['zdfu']=round($price['zd']/$val['price']*100,2);
					}else{
						$price['zd']=$price['price']-$val['price'];
						$price['zdfu']=round($price['zd']/$val['price']*100,2);
					}
				}
			}
			if($price["code"]=="I"||$price["code"]=="JM"||$price["code"]=="J"){
				$price['price']=sprintf("%.1f",$price["price"]);
			}
			$price_arr[$price['code']]=$price;
			$price_arr[$price['code']]['zhangdie']=$this->zd_color($price_arr[$price['code']]['zd']);
			$price_arr[$price['code']]['zhangdie1']=$price_arr[$price['code']]['zd'];
			$price_arr[$price['code']]['zhangdiefu']=$this->zd_color($price_arr[$price['code']]['zdfu']."%");
		}
		
		$this->assign("price_arr",$price_arr);
		//$this->assign("zd_arr",$zd_arr);
		$this->assign("date",$date);
		//print_r($price_arr[RB]['zhangdie']);
		return $price_arr[RB]['zhangdie1'];
		//$this->assign("date",date());
		//print_r($date);exit;
	}
	//大宗商品价格表
	public function dzprice_t($now){
		$last_day=$this->workday_gzj($now);
		$sql="select * from  steelhome.dollar_dzprice where  type in ('1','2','3','4','5','6','9') and date<='".$GLOBALS['type_date']."' order by id desc limit 7";
		//$res=$this->stedao->query($sql);测试
		$res=$this->maindao->query($sql);//正式
		//print_r($res);
		$dz_res=array();
		foreach($res as $val){
			
				
			
				//echo "<pre>";
				//print_r($val['zdfu']);
			$time = strtotime($val['date']) - 3600*24;
			$forworddate=date("Y-m-d",$time);
			$dsql="select value  from  steelhome.dollar_dzprice where  type='".$val['type']."' and date<='".$forworddate."' order by id desc limit 1";
			$dres=$this->maindao->getOne($dsql);//正式
			$down=$val['value']-$dres;
// 			echo $dsql."<br/>";
// 			echo $val['value']."<br/>";
// 			echo $dres."<br/>";
			
// 			echo $down;
// wufan xiugai 
			
// 			echo $down;
				if($down<0.00 && $down > 0){
					$dz_res['zd'.$val['type']]=number_format($down,4,'.','');
					//$dz_res['zdfu'.$val['type']]=number_format($val['zdfu'],4,'.','');
				}else if($down<-0.00 && $down > -0.01){
					$dz_res['zd'.$val['type']]=number_format($down,4,'.','');
					//$dz_res['zdfu'.$val['type']]=number_format($val['zdfu'],4,'.','');
				}else{
					$dz_res['zd'.$val['type']]=number_format($down,2,'.','');
					
				}
				$val['zdfu']=$dz_res['zd'.$val['type']]/($val['value']-$dz_res['zd'.$val['type']])*100;
				//print_r($val['type']);
				//echo "<pre>";
				//print_r($val['zdfu']);
				//echo "<pre>";
				$dz_res['zdfu'.$val['type']]=round($val['zdfu'],2);
				$dz_res[$val['type']]=number_format($val['value'],2,'.','');
				$dz_res['zdfu'.$val['type']]=$this->zd_color($dz_res['zdfu'.$val['type']]."%");
				$dz_res['zd'.$val['type']]=$this->zd_color($dz_res['zd'.$val['type']]);
			
		}
		$meiyday=$res[0]['date'];
		//print_r($res);
		if(empty($res)){
			$meiyday=$this->workday_gzj($GLOBALS['type_date']);
		}
		$meiyday=date("Y年n月j日",strtotime($meiyday));
		//$meiyday=date("Y年n月j日",strtotime($res[0]['date']));
		$this->assign("meiyday",$meiyday);
		$this->assign("last_day",$last_day);
		$this->assign("dz_res",$dz_res);
	}
	public function ctjiag($params){
		$last_day=$this->workday_gzj($params);
		//$this->workday_gzj("2017-10-07");
		$date=$last_day." "."00:00:00";
		$ct_sql="select id from steelprice_base2  where steel_id='1883' and post_date<='".$date."' and is_show='2' order by post_date desc limit 1";//取最近一个工作日厂提价
		$tj_sql1="select id from steelprice_base  where post_date<='".$date."' and steel_id ='701' and is_show ='2'  order by post_date desc limit 1";
		$tj_sql2="select id from steelprice_base  where post_date<='".$date."' and steel_id ='1844' and is_show ='2'  order by post_date desc limit 1";
		

		$ct_res_id=$this->gcdao->query($ct_sql);
		//print_r($ct_sql);
		//echo "<pre>";
		//print_r($ct_res_id);
		//print_r($tj_sql);
		//echo "<pre>";
		$tj_res_id_tg=$this->gcdao->query($tj_sql1);
		$tj_res_id_sh=$this->gcdao->query($tj_sql2);
		$sb_id_arr=$tj_res_id_tg[0]['id'].",".$tj_res_id_sh[0]['id'];
		//$sb_id_arr.=$tj_res_id_sh[0]['id'];
		//echo "<pre>";
		$sb_id=$ct_res_id[0]['id'];
		//$tj_temp_a
		$sb_id_arr;
		
		//print_r($sb_id_arr);exit;
		//$sb_id_arr="('".$tj_res_id[0]['id']."','".$tj_res_id[1]['id']."')";
		//print_r($sb_id_arr);exit;
		$sb_id_arr="(".$sb_id_arr.")";
		$ctp_sql="select changerate_tax,the_price_tax,quyu,variety from  ct_price_info  where sb_id='".$sb_id."' and specification='Φ29-85' and is_show='2'";
		//print_r($ctp_sql);
		$ct_res=$this->gcdao->query($ctp_sql);
		//print_r($ct_res);exit;
		$tj_sql="select sb_id,gcid,changerate_tax,the_price_tax,quyu,variety from  steelprice_info  where sb_id in ".$sb_id_arr." and variety in('碳结钢','合结钢') and specification in ('Φ30-85mm','Φ30-160mm','Φ29-85mm') and is_show='2'";
		//print_r($tj_sql);
		//echo "<pre>";
		$tj_res=$this->gcdao->query($tj_sql);
		//print_r($tj_res);
		//echo "<pre>";
		$new_ctarray=array();
		$arr_variety=array(
					"碳结钢"=>"tjg",
					"合结钢"=>"hjg"
				);
		
		$arr_city=array(
					
					"南京"=>"nj",
					"无锡"=>"wx",
					"杭州"=>"hz",
					"全国"=>"qg"
				);
		
				
		foreach($ct_res as $val){
			$data_key = $arr_variety[$val['variety']].$arr_city[$val['quyu']];
			if($val['changerate_tax']=="" && $val['changerate_tax']!="0"){
				$val['changerate_tax']="-";
			}if($val['the_price_tax']==""){
				$val['the_price_tax']="-";
			}
			$new_ctarray[$data_key]=$val;
			$new_ctarray[$data_key]['changerate_tax']=$this->zd_color($new_ctarray[$data_key]['changerate_tax']);
		}
		$new_tjarray=array();
		foreach($tj_res as $val){
			if($val['changerate_tax']=="" && $val['changerate_tax']!="0"){
				$val['changerate_tax']="-";
			}if($val['the_price_tax']==""){
				$val['the_price_tax']="-";
			}
			$data_key = $arr_variety[$val['variety']].$arr_city[$val['quyu']].$val['gcid'];
			$new_tjarray[$data_key]=$val;
			$new_tjarray[$data_key]['changerate_tax']=$this->zd_color($new_tjarray[$data_key]['changerate_tax']);
		}
		//
		
		$this->assign("new_ctarray",$new_ctarray);
		$this->assign("new_tjarray",$new_tjarray);
		//print_r($new_tjarray);exit;
		//print_r($new_ctarray[tjgwx]['the_price_tax']);
		return $new_ctarray;
		
	}
	/*hlf strat 2018/5/28*/
	public function gzjyuc($day){
		$last_day=date("Y-m-d",strtotime($GLOBALS['type_date']) - 3600*24);
		$sql="select * from NgGZJForcast where Type='3' and CDate <'".$last_day."' order by CDate desc limit 1";
		$info=$this->maindao->getRow($sql);
		//print_r($info);
		if($info){
			if(abs($info['ZhangDie'])<DELTA){
			$info['ZhangDie']="持平";
			}else if($info['ZhangDie']>DELTA){
				$info['ZhangDie']='上涨'.$info['ZhangDie'].'元/吨';
			}else{
				$info['ZhangDie']="下跌".abs($info['ZhangDie']).'元/吨';
			}
		}else{
			$info['ZhangDie']='无';
		}
		//print_r($info);
		$this->assign("info",$info);
	}
	/*hlf end*/
	//定价建议-----南京
	public function djjy($row_ng_data,$RB_zd){
		
		//$now=date("Y-m-d");
		$now=$GLOBALS['type_date'];
		$date_arr=$this->workday($now);
		//print_r($row_ng_data);
		//echo "<pre>";
		$last_day=$date_arr[1];
		//print_r($last_day);exit;
		//南京
		$sum=0;
		$next_five=array();
		$kuc=$row_ng_data[$last_day]['Ngyg0024'];//库存
		for($i=1;$i<=5;$i++){
			$ng_date=$date_arr[$i];
			$sum=$sum+$row_ng_data[$ng_date]['Ngyg0024'];
			$next_five[$i]=$row_ng_data[$ng_date]['Ngyg0024'];
			//echo "<pre>";
			//print_r($sum);
					
		}
		//exit;
		//print_r($date_arr);
		//echo "<pre>";
		$oldprice=$row_ng_data[$date_arr[1]]['Ngyg0001'];
		//print_r($oldprice);
		//echo "<pre>";
		$max=array_search(max($next_five),$next_five);
		$min=array_search(min($next_five),$next_five);
		$kuc_max=$next_five[$max];
		$kuc_min=$next_five[$min];
		//echo $next_five[$kuc_max];
		
		$avg_kuc=$sum/5;
		
		$nj_zd;
		/*print_r($kuc);
		echo "<pre>";                                                                                                                                                                                                                                                                  
		print_r($kuc_max);
		echo "<pre>";
		print_r($kuc_min);
		echo "<pre>";
		print_r($avg_kuc);
		echo "<pre>";
		print_r($RB_zd);
		echo "<pre>";*/
		//$nj_moxdj;
		if($kuc<=$avg_kuc  ){
			if($kuc!=$kuc_min){
				if($RB_zd>=10){
					//print_r(2222);
					$ng_zd=(int)($RB_zd/10+0.5)*10;
				}
				if($RB_zd<=10 && $RB_zd>0){
					$ng_zd=10;
				}
				if($RB_zd<0 && $RB_zd>-10){
					$ng_zd=0;
				}
				if($RB_zd<=-10 && $RB_zd>-50){
					$ng_zd=(int)($RB_zd/10+0.5)*5;
				}
				if($RB_zd<=-50 && $RB_zd>-100){
					$ng_zd=$RB_zd*0.8;
				}
				if($RB_zd<=-100){
					$ng_zd=$RB_zd*1;
				}
				
			}else{
				
				if($RB_zd>=10){
					$ng_zd=(int)($RB_zd/10+0.5)*20;
				}
				if($RB_zd<10 && $RB_zd>0){
					$ng_zd=10*2;
				}
				if($RB_zd<=0 && $RB_zd>-10){
					$ng_zd=10;
				}
				if($RB_zd<=-10 && $RB_zd>-50){
					$ng_zd=0;
				}
				if($RB_zd<=-50 && $RB_zd>-100){
					$ng_zd=(int)($RB_zd/10+0.5)*5;
				}
				if($RB_zd<=-100){
					$ng_zd=(int)($RB_zd/10+0.5)*8;
				}
				
			}
			
		}else{
			
			if($kuc!=$kuc_max){
				
				if($RB_zd>=100){
					$ng_zd=(int)($RB_zd/10+0.5)*10;
				}
				if($RB_zd<100 && $RB_zd>50){
					$ng_zd=(int)($RB_zd/10+0.5)*8;
				}
				if($RB_zd<=50 && $RB_zd>10){
					$ng_zd=(int)($RB_zd/10+0.5)*5;
				}
				if($RB_zd<=10 && $RB_zd>0){
					$ng_zd=0;
				}
				if($RB_zd<=0 && $RB_zd>-10){
					$ng_zd=-10;
				}
				if($RB_zd<=-10){
					/*print_r($RB);
					echo "<pre>";*/
					$ng_zd=(int)($RB_zd/10+0.5)*10;
					/*print_r($ng_zd);
					echo "<pre>";exit;*/
				}
			}else{
				
				if($RB_zd>=100){
					$ng_zd=(int)($RB_zd/10+0.5)*9;
				}
				if($RB_zd<100 && $RB_zd>50){
					$ng_zd=(int)($RB_zd/10+0.5)*7;
				}
				if($RB_zd<=50 && $RB_zd>20){
					//print_r(2222);
					$ng_zd=(int)($RB_zd/10+0.5)*4;
				}
				if($RB_zd<=20 && $RB_zd>0){
					$ng_zd=0;
				}
				if($RB_zd<=0 && $RB_zd>-10){
					$ng_zd=-20;
				}
				if($RB_zd<=-10){
					//print_r(1111);
					$ng_zd=(int)($RB_zd/10+0.5)*15;
				}
			}
		}
		$ng_zd=	(int)($ng_zd/10)*10;
		//
		$artificial_last_day=date("Y-m-d",strtotime($GLOBALS['type_date']) - 3600*24);
		$artificial_zd=$this->maindao->getRow("select ZhangDie from NgGZJForcast where Type=3 and CDate='".$artificial_last_day."'");
		if($artificial_zd){
			$artificial_zd=$artificial_zd['ZhangDie'];
			$zd_cz=abs($ng_zd-$artificial_zd);
			//print_r($ng_zd);
			//print_r($artificial_zd);
		//	print_r($zd_cz);
			if($ng_zd>0 && $artificial_zd < 0){
				$ng_zd=$ng_zd+$artificial_zd;
			}else if($ng_zd < 0 && $artificial_zd > 0){
				$ng_zd=$ng_zd+$artificial_zd;
			}else if($ng_zd == 0 && $artificial_zd == 0){
				$ng_zd=0;
			}else{
				if($zd_cz>30){
					$ng_zd=$artificial_zd;
				}
			}
		}
		//print_r($artificial_zd);
		//print_r($ng_zd);
		/*print_r($RB_zd);
		echo "<pre>";
		print_r($avg_kuc);
		echo "<pre>";
		print_r($kuc);
		echo "<pre>";
		print_r($kuc_max);
		echo "<pre>";
	print_r($ng_zd);
	echo "<pre>";
	print_r($nj_moxdj);exit;
	//print_r($oldprice);exit;
	//print_r($ng_zd);exit;*/
		$nj_moxdj=$row_ng_data[$date_arr[1]]['Ngyg0001']+$ng_zd;
		$this->assign("nj_moxdj",$nj_moxdj);
		
		$this->assign("ng_zd",$ng_zd);
		$nj_arr=array('nj_moxdj'=>$nj_moxdj,'nj_zd'=>$ng_zd,'oldprice'=>$oldprice);
		return $nj_arr;
		//exit;
		
	}
	//无锡定价模型数据处理
	/*public function wx_djjy($row_ng_data,$wx_ct,$wxscang_jhlyue){
		//$now=date("Y-m-d");
		$now=$GLOBALS['type_date'];
		$date_arr=$this->workday($now);
		//print_r($row_ng_data);
		$last_day=$date_arr[1];
		//print_r($last_day);exit;
		//南京
		$kuc=$row_ng_data[$last_day]['Ngyg0002'];//库存
		//南钢月生产计划量
		$count_hday=$this->workdaysum($now);
			//print_r($count_hday);
		$kdl=$row_ng_data[$last_day]['Ngyg0011']/($wxscang_jhlyue/$count_hday);
		//print_r($row_ng_data[$last_day]['Ngyg0011']);
		//echo "<pre>";
		//print_r($wxscang_jhlyue);
		//echo "<pre>";
		$cz=$kuc-$wx_ct;
		$wx_zd=0;
		//print_r($kuc);
		//echo "<pre>";
		//print_r($cz);
		//echo "<pre>";
	//	print_r($wx_ct);
		//echo "<pre>";
		//print_r($kdl);

		if($cz>50){
			//print_r(5555);
			if($kdl >= 1.1){
				//print_r(5555);
				$wx_zd=50*1.5;
			}else if(1<=$kdl && $kdl<1.1){
				$wx_zd=30*1.5;
			}else if(0.9<=$kdl && $kdl<1){
				$wx_zd=0*1.5;
			}else if(0.7<=$kdl && $kdl<0.9){
				$wx_zd=-30*1.5;
			}else if( $kdl<0.7){
				$wx_zd=-50*1.5;
			}
			
		}
		else if(20<$cz && $cz<=50){
			//print_r(444444);
			if($kdl>=1.1){
				$wx_zd=50*1.3;
			}else if(1<=$kdl && $kdl<1.1){
				$wx_zd=30*1.3;
			}else if(0.9<=$kdl && $kdl<1){
				$wx_zd=0*1.3;
			}else if(0.7<=$kdl && $kdl<0.9){
				$wx_zd=-30*1.3;
			}else if( $kdl<0.7){
				$wx_zd=-50*1.3;
			}
			
		}else if(0<$cz && $cz<=20){
			//print_r(33333);
			if($kdl>=1.1){
				$wx_zd=50*1.2;
			}else if(1<=$kdl && $kdl<1.1){
				$wx_zd=30*1.2;
			}else if(0.9<=$kdl && $kdl<1){
				$wx_zd=0*1.2;
			}else if(0.7<=$kdl && $kdl<0.9){
				$wx_zd=-30*1.2;
			}else if( $kdl<0.7){
				$wx_zd=-50*1.2;
			}
			
		}else if(-20<$cz && $cz<=0){
			//print_r(22222);
			if($kdl>=1.1){
				$wx_zd=50*1;
			}else if(1<=$kdl && $kdl<1.1){
				$wx_zd=30*1;
			}else if(0.9<=$kdl && $kdl<1){
				$wx_zd=0*1;
			}else if(0.7<=$kdl && $kdl<0.9){
				$wx_zd=-30*1;
			}else if( $kdl<0.7){
				$wx_zd=-50*1;
			}
			
		}else if(-50<$cz && $cz<=-20){
			//print_r(11111);
			if($kdl>=1.1){
				$wx_zd=50*1.3;
			}else if(1<=$kdl && $kdl<1.1){
				$wx_zd=30*1.3;
			}else if(0.9<=$kdl && $kdl<1){
				$wx_zd=0*1.3;
			}else if(0.7<=$kdl && $kdl<0.9){
				$wx_zd=-30*1.3;
			}else if( $kdl<0.7){
				$wx_zd=-50*1.3;
			}
			
		}else{
			//print_r(666666);
			if($kdl>=1.1){
				$wx_zd=50*1.5;
			}else if(1<=$kdl && $kdl<1.1){
				$wx_zd=30*1.5;
			}else if(0.9<=$kdl && $kdl<1){
				$wx_zd=0*1.5;
			}else if(0.7<=$kdl && $kdl<0.9){
				$wx_zd=-30*1.5;
			}else if( $kdl<0.7){
				$wx_zd=-50*1.5;
			}
			
		}
		
		$wx_zd=	(int)($wx_zd/10)*10;
		//print_r($wx_zd);
		$mxing_djjy=$kuc+$wx_zd;
		//echo "<pre>";
		//print_r($kdl);
		//echo "<pre>";
		
				//exit;
		$this->assign("mxing_djjy",$mxing_djjy);
		//print_r($mxing_djjy);exit;
		$this->assign("wx_zd",$wx_zd);
		$wx_arr=array('wx_moxdj'=>$mxing_djjy,'wx_zd'=>$wx_zd,'oldprice'=>$kuc);
		return $wx_arr;
		//print_r($wx_zd);
		//exit;
	}
	//杭州模型定价
	public function hz_djjy($row_ng_data,$wx_ct,$wxscang_jhlyue){
		//$now=date("Y-m-d");
		$now=$GLOBALS['type_date'];
		$date_arr=$this->workday($now);
		//print_r($row_ng_data);
		$last_day=$date_arr[1];
		//print_r($last_day);exit;
		//南京
		$kuc=$row_ng_data[$last_day]['Ngyg0003'];//库存
		//南钢月生产计划量
		$count_hday=$this->workdaysum($now);
		$kdl=$row_ng_data[$last_day]['Ngyg0015']/($wxscang_jhlyue/$count_hday);
		$cz=$kuc-$wx_ct;
		$hz_zd;
		if($cz>50){
			//print_r(5555);
			if($kdl >= 1.1){
				$hz_zd=50*1.5;
			}else if(1<=$kdl && $kdl<1.1){
				$hz_zd=30*1.5;
			}else if(0.9<=$kdl && $kdl<1){
				$hz_zd=0*1.5;
			}else if(0.7<=$kdl && $kdl<0.9){
				$hz_zd=-30*1.5;
			}else if( $kdl<0.7){
				$hz_zd=-50*1.5;
			}
			
		}
		else if(20<$cz && $cz<=50){
			//print_r(444444);
			if($kdl>=1.1){
				$hz_zd=50*1.3;
			}else if(1<=$kdl && $kdl<1.1){
				$hz_zd=30*1.3;
			}else if(0.9<=$kdl && $kdl<1){
				$hz_zd=0*1.3;
			}else if(0.7<=$kdl && $kdl<0.9){
				$hz_zd=-30*1.3;
			}else if( $kdl<0.7){
				$hz_zd=-50*1.3;
			}
			
		}else if(0<$cz && $cz<=20){
			//print_r(33333);
			if($kdl>=1.1){
				$hz_zd=50*1.2;
			}else if(1<=$kdl && $kdl<1.1){
				$hz_zd=30*1.2;
			}else if(0.9<=$kdl && $kdl<1){
				$hz_zd=0*1.2;
			}else if(0.7<=$kdl && $kdl<0.9){
				$hz_zd=-30*1.2;
			}else if( $kdl<0.7){
				$hz_zd=-50*1.2;
			}
			
		}else if(-20<$cz && $cz<=0){
			//print_r(22222);
			if($kdl>=1.1){
				$hz_zd=50*1;
			}else if(1<=$kdl && $kdl<1.1){
				$hz_zd=30*1;
			}else if(0.9<=$kdl && $kdl<1){
				$hz_zd=0*1;
			}else if(0.7<=$kdl && $kdl<0.9){
				$hz_zd=-30*1;
			}else if( $kdl<0.7){
				$hz_zd=-50*1;
			}
			
		}else if(-50<$cz && $cz<=-20){
			//print_r(11111);
			if($kdl>=1.1){
				$hz_zd=50*1.3;
			}else if(1<=$kdl && $kdl<1.1){
				$hz_zd=30*1.3;
			}else if(0.9<=$kdl && $kdl<1){
				$hz_zd=0*1.3;
			}else if(0.7<=$kdl && $kdl<0.9){
				$hz_zd=-30*1.3;
			}else if( $kdl<0.7){
				$hz_zd=-50*1.3;
			}
			
		}else{
			//print_r(666666);
			if($kdl>=1.1){
				$hz_zd=50*1.5;
			}else if(1<=$kdl && $kdl<1.1){
				$hz_zd=30*1.5;
			}else if(0.9<=$kdl && $kdl<1){
				$hz_zd=0*1.5;
			}else if(0.7<=$kdl && $kdl<0.9){
				$hz_zd=-30*1.5;
			}else if( $kdl<0.7){
				$hz_zd=-50*1.5;
			}
			
		}
		
		//echo "<pre>";
		//print_r($kdl);
		//echo "<pre>";
		$hz_zd=	(int)($hz_zd/10)*10;
		$hzmxing_djjy=$kuc+$hz_zd;
		
		
		$this->assign("hzmxing_djjy",$hzmxing_djjy);
		//print_r($hzmxing_djjy);exit;
		$this->assign("hz_zd",$hz_zd);
		$hz_arr=array('hz_moxdj'=>$hzmxing_djjy,'hz_zd'=>$hz_zd,'oldprice'=>$kuc);
		return $hz_arr;
		//print_r($wx_zd);
		//exit;
		
	}*/
	//保存数据
	public function savedate($params){
		//$date=date("Y-m-d");
		$title="碳结钢日定价";
		if(isset($params['curdate'])){
			$GLOBALS['type_date']=$params['curdate'];
			$GLOBALS['type_Y']=date("Y",strtotime($GLOBALS['type_date']));
			$GLOBALS['type_Y_m']=date("Y-m",strtotime($GLOBALS['type_date']));
			$url = DC_URL.DCURL."/dctjgridj.php?view=index&issave=1&curdate=".$params['curdate'];
			//$GLOBALS['type_date_h']=date("Y-m-d H:m:s",strtotime($GLOBALS['type_date']));
		}else{
			$url = DC_URL.DCURL."/dctjgridj.php?view=index&issave=1";
		}
		$r=IsNgWorkDay($this->maindao,$GLOBALS['type_date']);
		if($r==0){
			echo "今天为休息日,不更新数据！";
			exit;
		}else{
		$model=$GLOBALS['type_date'];
		
		$model=date("Y年n月j日",strtotime($model));
		$modeltitle=$model.$title;
		//$url = $_SERVER['SERVER_NAME'].$_SERVER['REQUEST_URI'];
		//$url = DC_URL.DCURL."/dctjgridj.php?view=index&issave=1";
		//print_r($_SERVER);
		//echo "111";
		include '/usr/local/www/libs/phpQuery/phpQuery.php';
		//echo "222";
		phpQuery::newDocumentFile($url);
		//echo "333";
		$html=pq("body")->html();
		$html_huz.=pq(".huiz")->html();
		$html_huz=str_replace("五、钢之家判断","1、钢之家判断",$html_huz);
		$html_huz=str_replace("六、今日价格调整建议","2、价格模型建议",$html_huz);
		/*$html_huz="<h3 class='h3'>1、钢之家判断</h3>";
		$html_huz.=pq(".huiz h3:eq(0)")->html();
		$html_huz.="<h3 class='h3'>2、价格模型建议</h3>";
		$html_huz.=pq(".huiz h4:eq(1)")->html();*/
		$huiz_content=htmlentities($html_huz, ENT_QUOTES,"UTF-8");
		print_r($html_huz);
		//echo "444";
		
		$modelcontent=htmlentities($html, ENT_QUOTES,"UTF-8");
		//echo "555";
		$sql="update `ng_price_model` set modeltitle='".$modeltitle."',modelcontent='".$modelcontent."',HuiZong1='".$huiz_content."'  where modeltype='1' order by id desc limit 1";
		//echo "666";
		$this->ngdao->execute($sql);
		//echo "777";
		//echo $modelcontent;
		//print_r($html);
		}
		
	}
	//获取钢之家的前一个工作日
	public function workday_gzj($params){
		$date;
		include_once("/etc/steelconf/config/isholiday.php");

		for($i=0;$i<=20;$i++){
			$last_day=date("Y-m-d",(strtotime($params) - 3600*24*($i+1)));
			//$isexist = file_get_contents("https://holiday.steelhome.com/isholiday.php?date=".$last_day);
			$isexist=_isholiday($last_day);
			//echo "123";
			if($isexist=='1'){
				//print_r($last_day);
				continue;
			}else{
				$date=$last_day;
				break;
			}
		}
		
		return $date;
	}
	   //获取南钢工作日的函数
	public function workday($date){
		//echo $date="2017-10-07";
		 $sql="select date,isholiday from steelhome.holiday  where date <='".$date."' order by date desc limit 10"; 
		//$sql="select * from holiday  where date <='2017-10-7' order by date desc limit 20"; 
		 $res=$this->maindao->aquery($sql);
		 $i = 1;
		 $last_date = $date;
		 $date=array();
		 
		 while( $i < 8 ){
			$last_date=date("Y-m-d",strtotime("-1 day",strtotime($last_date))); 
			if( isset($res[$last_date]) ){
			
				if($res[$last_date]=="0"){
					
					$date[$i]=$last_date;
					$i++;
					//break;
				}else{
					//continue;
				}
			}else{
				
				$n=date("N",strtotime($last_date));
				if($n==7){
					//continue;
				}else{
					
					$date[$i]=$last_date;
					$i++;
					//break;
				}
				
			}
		 }
		 //	print_r($date);
		 return $date;
		
	   }
	   public function workdaysum($date)
	   {
		   $firstday = date('Y-m-01', strtotime($GLOBALS['type_Y_m']));
			$enddate = date('Y-m-d', strtotime("$firstday +1 month -1 day"));
		 // $date=date("Y-m-t");
		  $sql="select date,isholiday from steelhome.holiday  where date <='".$enddate."' and date >='".$firstday."'"; 
		//  print_r($sql);
		//$sql="select * from holiday  where date <='2017-10-7' order by date desc limit 20"; 
		 $res=$this->maindao->aquery($sql);
		 $i = 1;
		 $count;
		 $date=array();
		 //$j=0;
		//print_r($enddate);
		 $birth=explode('-',$enddate);
		 $num=$birth[2];
		// print_r($count);
		 while( $i <= $num ){
			//$last_date=date("Y-m-d",strtotime("-1 day",strtotime($last_date))); 
			$last_date;
			
			if($i<10){
				//$last_date=date("Y-m-0".$i);
				$d='-0'.$i;
				$last_date=$GLOBALS['type_Y_m'].$d;
			}else{
				//$last_date=date("Y-m"."-".$i);
				$last_date=$GLOBALS['type_Y_m'].'-'.$i;
			}
		if(isset($res[$last_date])){
			
			if($res[$last_date]=="0"){

			}else{
				$count++;
					//continue;
			}
		}else{
			$n=date("N",strtotime($last_date));
			if($n==7){
				$count++;
			}
		}
			$i++;
			
		}
		// $sum=date("t")-$count;
		$sum=$num-$count;
		// print_r($sum);
		 return $sum;
	   }
	 public function zd_color($zd){
		if($zd==0){
				 $zd = "<font color=black>".$zd."</font>";
			}else if($zd<0){
				$zd = "<font color=green>".$zd."</font>";
			}else{
				
				$zd = "<font color=red>+".$zd."</font>";
			}
			 return $zd;
	   }
} 
?>