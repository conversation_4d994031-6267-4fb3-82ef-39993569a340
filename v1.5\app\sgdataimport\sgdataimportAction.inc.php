<?php
class sgdataimportAction extends AbstractAction
{
    public function __construct()
    {
        parent::__construct();
    }

    public function checkSession()
    {
    }

    public function index($params)
    {
        $uptype = $params["uptype"];
        $titles = array(
            "3"=>"汉钢成本日报录入",
            "4"=>"龙钢成本日报录入",
            "5"=>"汉钢成本周报录入",
            "6"=>"龙钢成本周报录入",
        );
        $acts = array(
            "3"=>"drcbrbhg",
            "4"=>"drcbrblg",
            "5"=>"drcbzbhg",
            "6"=>"drcbzblg",
        );
        $GUID = $params['GUID'];
        $mode = $params['mode'];
        $this->assign('mode', $mode);
        if($GUID==""){
            alert('GUID不能为空');
            exit;
        }
        $user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
        if(empty($user_infos)){
            alert('用户不存在');
            exit;
        }
        $this->assign('guid', $GUID);
        if($uptype==3||$uptype==4)
        {
            $this->assign('date', date("Y-m-d"));
        }
        elseif($uptype==5||$uptype==6)
        {
            $this->assign('date', date('Y-m-d',strtotime('last friday')));
            $this->assign('enddate', date('Y-m-d',strtotime('Thursday')));
        }
        $total = $this->_dao->getOne( "SELECT COUNT(id) as c FROM sg_data_table_log where uptype = '$uptype' and isdel='0'");
        $page = $params['page'] == '' ? 1 : $params['page'];
        $url = "sgdataimport.php";
        $per = 25;
        $start = ( $page - 1 ) * $per;
        unset( $params['page'] );
        $pagebar = pagebar( $url, $params, $per, $page, $total );
        $this->assign( "pagebar", $pagebar );
        $sql = "select * from sg_data_table_log where uptype = '$uptype' and isdel='0' order by createtime desc limit $start, $per";
        $log_info = $this->_dao->query($sql);
        $createuser = array();
        foreach( $log_info as $v ){
            if(!in_array($v['createuser'],$createuser)){
                $createuser[] = $v['createuser'];
            }
        }
        $createuser_str =  implode("','",$createuser);
        $sql = "select id,truename from adminuser where id in ('$createuser_str')";
        $adminuser_info = $this->homeDao->query($sql);
        $admin_name = array();
        foreach( $adminuser_info as $v ){
            $admin_name[$v['id']] = $v['truename'];
        }
        $this->assign('log_info', $log_info);
        $this->assign('uptype', $uptype);
        $this->assign('title', $titles[$uptype]);
        $this->assign('admin_name', $admin_name);
        $this->assign('act', $acts[$uptype]);
    }

    //4导入龙钢成本日报
    function drcbrblg($params)
    {
        $file = SGUPLOADFILE."/".$params["tmp"];
        $filename = mb_convert_encoding($params["filename"],"GB2312","UTF-8");
        $date = $params["date"];
        $GUID = $params['GUID'];
        $uptype = $params['uptype'];
        $mode = $params['mode'];//1=客户端，2=安卓，3=iOS
        $response = array(
            'Success'  => 1,
            'Message'  => '',
        );
        if($GUID==""){
            $response["Success"] = 0;
            $response['Message'] = 'GUID不能为空';
        }
        if($response["Success"])
        {
            $user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
            if(empty($user_infos)){
                $response["Success"] = 0;
                $response['Message'] = '用户不存在';
            }
            $date = mysql_real_escape_string($params["date"]);
            // if(empty($date))
            // {
            //     $response["Success"] = 0;
            //     $response['Message'] = '年份出错';
            // }
            if($response["Success"])
            {
                require_once "../PHPExcel/PHPExcel.php";
                $type = pathinfo($file); 
                $type = strtolower($type["extension"]);
                if ($type=='xlsx') { 
                    $type='Excel2007'; 
                }elseif($type=='xls') { 
                    $type = 'Excel5';
                }
                $sheetList = explode(',', $params["sheet"]);
                if(trim($sheetList[0])=="")
                {
                    $response["Success"] = 0;
                    $response['Message'] = '未选择工作表';
                }
                if($response["Success"])
                {
                    $objReader = PHPExcel_IOFactory::createReader($type);//判断使用哪种格式
                    $objReader ->setReadDataOnly(true); //只读取数据,会智能忽略所有空白行,这点很重要！！！
                    $objPHPExcel = $objReader->load($file); //加载Excel文件
                    $sheetNames = $objPHPExcel->getSheetNames();//所有的工作表名
                    $sheetCount = $objPHPExcel->getSheetCount();//获取sheet工作表总个数
                    // print_r($sheetNames);
                    // echo $sheetCount;
                    $flag = true;
                    // print_r($sheetList);
                    $tmpsheetName = array();
                    foreach($sheetList as $sheet_index)
                    {
                        $sheet = $objPHPExcel->getSheet($sheet_index);
                        // $sheet_array = $objPHPExcel->getSheet($sheet_index)->toArray();
                        //获取当前工作表最大行数
                        $rows = $sheet->getHighestRow();
                        //获取当前工作表最大列数,返回的是最大的列名，如：B 
                        $cols = $sheet->getHighestColumn();
                        $basesql = "insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,dta_3,dta_4,dta_5,dta_6,dta_7,dta_8,createtime,createuser) values";
                        switch($sheet_index)
                        {
                            case 0:  //炼钢
                                $lgbycl = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("J3")->getFormattedValue()));
                                $lgljcl = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("K3")->getFormattedValue()));
                                $tmpsheetName[] = mb_convert_encoding($sheetNames[$sheet_index],"GB2312","UTF-8");
                            break;
                            case 1:  //轧钢
                                $zgbycl = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("J3")->getFormattedValue()));
                                $zgljcl = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("K3")->getFormattedValue()));
                                // print_r($cols);
                                $tmplist = array();
                                for($i=$rows;$i>=0;$i--)
                                {
                                    for($j="A";$j<=$cols;$j++)
                                    {
                                        $position = $j.$i;
                                        $position_val = iconv("utf-8","gb2312//IGNORE", $sheet->getCell($position)->getValue());
                                        if((strpos($this->trimAll($position_val), '钢材汇总成本')!==false)) //包含
                                        {
                                            /*$j++;
                                            for($k=$j;$k<=$cols;$k++)
                                            {
                                                $val = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell($k.$i)->getValue()));
                                                if(!empty($val))
                                                {
                                                    $tmplist[] = $val;
                                                }
                                            }*/

                                            $dwcb1 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell('D'.$i)->getFormattedValue()));
                                            $zcb1 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell('F'.$i)->getFormattedValue()));
                                            $dwcb2 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell('I'.$i)->getFormattedValue()));
                                            $zcb2 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell('K'.$i)->getFormattedValue()));
                                            break 2;
                                        }
                                    }
                                }
                                $tmpsheetName[] = mb_convert_encoding($sheetNames[$sheet_index],"GB2312","UTF-8");
                            break;
                            default:
                            break;
                        }
                        $valsql = "('SGcbrblg','龙钢成本日报','$date','$lgbycl','$lgljcl','$zgbycl','$zgljcl','$dwcb1','$zcb1','$dwcb2','$zcb2',NOW(),'".$user_infos['Uid']."')";
                        $this->_dao->execute("delete from sg_data_table where dta_ym='$date' and dta_type='SGcbrblg'");
                        $this->_dao->execute($basesql.$valsql);
                    }
                    $sheetName = implode(',',$tmpsheetName);
                    $this->_dao->execute("insert into sg_data_table_log(uptype,filepath,filename,sheetname,createtime,createuser,uptypedate) value('$uptype','$file','$filename','$sheetName',NOW(),'".$user_infos['Uid']."','$date')");
                    $response['Success'] = "1";
                    $response['Message'] = "导入成功";
                }
            }
        }
        echo $this->pri_JSON($response);
        exit;
    }

    //3导入汉钢成本日报
    function drcbrbhg($params)
    {
        ini_set('memory_limit', '512M');
        $file = SGUPLOADFILE."/".$params["tmp"];
        $filename = mb_convert_encoding($params["filename"],"GB2312","UTF-8");
        $date = $params["date"];
        $GUID = $params['GUID'];
        $uptype = $params['uptype'];
        $mode = $params['mode'];//1=客户端，2=安卓，3=iOS
        $response = array(
            'Success'  => 1,
            'Message'  => '',
        );
        if($GUID==""){
            $response["Success"] = 0;
            $response['Message'] = 'GUID不能为空';
        }
        if($response["Success"])
        {
            $user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
            if(empty($user_infos)){
                $response["Success"] = 0;
                $response['Message'] = '用户不存在';
            }
            $date = mysql_real_escape_string($params["date"]);
            // if(empty($date))
            // {
            //     $response["Success"] = 0;
            //     $response['Message'] = '年份出错';
            // }
            if($response["Success"])
            {
                require_once "../PHPExcel/PHPExcel.php";
                $type = pathinfo($file); 
                $type = strtolower($type["extension"]);
                if ($type=='xlsx') { 
                    $type='Excel2007'; 
                }elseif($type=='xls') { 
                    $type = 'Excel5';
                }
                $sheetList = explode(',', $params["sheet"]);
                if(trim($sheetList[0])=="")
                {
                    $response["Success"] = 0;
                    $response['Message'] = '未选择工作表';
                }
                if($response["Success"])
                {
                    $objReader = PHPExcel_IOFactory::createReader($type);//判断使用哪种格式
                    $objReader ->setReadDataOnly(true); //只读取数据,会智能忽略所有空白行,这点很重要！！！
                    $objPHPExcel = $objReader->load($file); //加载Excel文件
                    $sheetNames = $objPHPExcel->getSheetNames();//所有的工作表名
                    $sheetCount = $objPHPExcel->getSheetCount();//获取sheet工作表总个数
                    // print_r($sheetNames);
                    // echo $sheetCount;
                    $flag = true;
                    // print_r($sheetList);
                    $tmpsheetName = array();
                    foreach($sheetList as $sheet_index)
                    {
                        $sheet = $objPHPExcel->getSheet($sheet_index);
                        // $sheet_array = $objPHPExcel->getSheet($sheet_index)->toArray();
                        //获取当前工作表最大行数
                        $rows = $sheet->getHighestRow();
                        //获取当前工作表最大列数,返回的是最大的列名，如：B 
                        $cols = $sheet->getHighestColumn();
                        $basesql = "insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,dta_3,dta_4,dta_5,dta_6,createtime,createuser) values";
                        switch($sheet_index)
                        {
                            case 20:  //炼钢
                                for($i=1;$i<=$rows;$i++)
                                {

                                    if((strpos($this->trimAll(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("A".$i)->getValue())), '钢坯综合成本')!==false))
                                    {
                                        for($c="B";$c<="Z";$c++)
                                        {
                                            $key = $c.$i;
                                            $cell = $this->trimAll(iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getValue()));
                                            if(strpos($cell, "产量")!==false)
                                            {
                                                ++$c;
                                                ++$c;
                                                $lgdrcl = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell($c.$i)->getFormattedValue()));
                                                $lgyljcl = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell($c.($i+1))->getFormattedValue()));
                                                break 2;
                                            }
                                        }
                                    }
                                }
                                $tmpsheetName[] = mb_convert_encoding($sheetNames[$sheet_index],"GB2312","UTF-8");
                            break;
                            case 21:  //轧钢
                                $zgdrcl = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("L3")->getFormattedValue()));
                                $zgyljcl = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("L4")->getFormattedValue()));
                                for($i=1;$i<=$rows;$i++)
                                {
                                    if((strpos($this->trimAll(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("A".$i)->getValue())), '成本费用')!==false))
                                    {
                                        $dwdrcl = str_replace("#DIV/0!","",trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("E".$i)->getFormattedValue())));
                                        $dwyljcl = str_replace("#DIV/0!","",trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("J".$i)->getFormattedValue())));
                                        break;
                                    }
                                }
                                $tmpsheetName[] = mb_convert_encoding($sheetNames[$sheet_index],"GB2312","UTF-8");
                            break;
                            default:
                            break;
                        }
                    }
                    $valsql = "('SGcbrbhg','汉钢成本日报','$date','$lgdrcl','$lgyljcl','$zgdrcl','$zgyljcl','$dwdrcl','$dwyljcl',NOW(),'".$user_infos['Uid']."')";
                    $this->_dao->execute("delete from sg_data_table where dta_ym='$date' and dta_type='SGcbrbhg'");
                    $this->_dao->execute($basesql.$valsql);
                    $sheetName = implode(',',$tmpsheetName);
                    $this->_dao->execute("insert into sg_data_table_log(uptype,filepath,filename,sheetname,createtime,createuser,uptypedate) value('$uptype','$file','$filename','$sheetName',NOW(),'".$user_infos['Uid']."','$date')");
                    $response['Success'] = "1";
                    $response['Message'] = "导入成功";
                }
            }
        }
        echo $this->pri_JSON($response);
        exit;
    }

    //6导入龙钢成本周报
    function drcbzblg($params)
    {
        $file = SGUPLOADFILE."/".$params["tmp"];
        $filename = mb_convert_encoding($params["filename"],"GB2312","UTF-8");
        $date = $params["date"];
        $GUID = $params['GUID'];
        $uptype = $params['uptype'];
        $enddate = $params['enddate'];
        $mode = $params['mode'];//1=客户端，2=安卓，3=iOS
        $response = array(
            'Success'  => 1,
            'Message'  => '',
        );
        if($GUID==""){
            $response["Success"] = 0;
            // $response['Message'] = 'GUID不能为空';
        }
        if($response["Success"])
        {
            $user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
            if(empty($user_infos)){
                $response["Success"] = 0;
                // $response['Message'] = '用户不存在';
            }
            $date = mysql_real_escape_string($params["date"]);
            if($response["Success"])
            {
                require_once "../PHPExcel/PHPExcel.php";
                $type = pathinfo($file); 
                $type = strtolower($type["extension"]);
                if ($type=='xlsx') { 
                    $type='Excel2007'; 
                }elseif($type=='xls') { 
                    $type = 'Excel5';
                }
                $sheetList = explode(',', $params["sheet"]);
                if(trim($sheetList[0])=="")
                {
                    $response["Success"] = 0;
                    $response['Message'] = '未选择工作表';
                }
                if($response["Success"])
                {
                    $objReader = PHPExcel_IOFactory::createReader($type);//判断使用哪种格式
                    $objReader ->setReadDataOnly(true); //只读取数据,会智能忽略所有空白行,这点很重要！！！
                    $objPHPExcel = $objReader->load($file); //加载Excel文件
                    $sheetNames = $objPHPExcel->getSheetNames();//所有的工作表名
                    $sheetCount = $objPHPExcel->getSheetCount();//获取sheet工作表总个数
                    // print_r($sheetNames);
                    // echo $sheetCount;
                    $flag = true;
                    $tmpsheetName = array();
                    foreach($sheetList as $sheet_index)
                    {
                        $sheet = $objPHPExcel->getSheet($sheet_index);
                        // $sheet_array = $objPHPExcel->getSheet($sheet_index)->toArray();
                        //获取当前工作表最大行数
                        $rows = $sheet->getHighestRow();
                        //获取当前工作表最大列数,返回的是最大的列名，如：B 
                        $cols = $sheet->getHighestColumn();
                        switch($sheet_index)
                        {
                            case 0:  //烧结
                                $basesql0 = "insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,dta_3,dta_4,dta_5,dta_6,dta_7,createtime,createuser) values";
                                for($i=1;$i<=$rows;$i++)
                                {
                                    for($j="A";$j<="F";$j++)
                                    {
                                        $key = $j.$i;
                                        $data0[$i][$j] = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getFormattedValue()));
                                    }
                                }
                                foreach($data0 as $rownum0 => $rows_arr0)
                                {
                                    if($data0[$rownum0]["A"]!=""&&$rownum0!=1)
                                    {
                                        $dta0_1 = trim($data0[$rownum0]["A"]);
                                        $dta0_2 = trim($data0[$rownum0]["B"]);
                                        $dta0_3 = trim($data0[$rownum0]["C"]);
                                        $dta0_4 = trim($data0[$rownum0]["D"]);
                                        $dta0_5 = trim($data0[$rownum0]["E"]);
                                        $dta0_6 = trim($data0[$rownum0]["F"]);
                                        $valsql0 .= "('SGcbzblg','龙钢成本周报-烧结','$date','$dta0_1','$dta0_2','$dta0_3','$dta0_4','$dta0_5','$dta0_6','$enddate',NOW(),'".$user_infos['Uid']."'),";
                                    }
                                }
                                if($valsql0)
                                {
                                    $this->_dao->execute("delete from sg_data_table where dta_ym='$date' and dta_7='$enddate' and dta_type='SGcbzblg' and dta_vartype='龙钢成本周报-烧结'");
                                    $cell1 = $this->trimAll(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("F3")->getValue()));
                                    $this->_dao->execute("insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,dta_7,createtime,createuser) values('SGcbzblg','龙钢成本周报-烧结','$date','本周产量','$cell1','$enddate',NOW(),'".$user_infos['Uid']."')");
                                    $tsql = substr($valsql0, 0, -1);
                                    $this->_dao->execute($basesql0.$tsql);
                                    $tmpsheetName[] = mb_convert_encoding($sheetNames[$sheet_index],"GB2312","UTF-8");
                                }
                            break;
                            case 1:  //炼铁
                                $basesql1 = "insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,dta_3,dta_4,dta_5,dta_6,dta_7,createtime,createuser) values";
                                for($i=1;$i<=$rows;$i++)
                                {
                                    for($j="A";$j<="F";$j++)
                                    {
                                        $key = $j.$i;
                                        $data1[$i][$j] = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getFormattedValue()));
                                        
                                    }
                                }
                                foreach($data1 as $rownum1 => $rows_arr1)
                                {
                                    if($data1[$rownum1]["A"]!=""&&$rownum1!=1)
                                    {
                                        $dta1_1 = $data1[$rownum1]["A"];
                                        $dta1_2 = $data1[$rownum1]["B"];
                                        $dta1_3 = $data1[$rownum1]["C"];
                                        $dta1_4 = $data1[$rownum1]["D"];
                                        $dta1_5 = $data1[$rownum1]["E"];
                                        $dta1_6 = $data1[$rownum1]["F"];
                                        $valsql1 .= "('SGcbzblg','龙钢成本周报-炼铁','$date','$dta1_1','$dta1_2','$dta1_3','$dta1_4','$dta1_5','$dta1_6','$enddate',NOW(),'".$user_infos['Uid']."'),";
                                    }
                                }
                                if($valsql1)
                                {
                                    $this->_dao->execute("delete from sg_data_table where dta_ym='$date' and dta_type='SGcbzblg' and dta_7='$enddate' and dta_vartype='龙钢成本周报-炼铁'");
                                    $cell1 = $this->trimAll(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("F3")->getValue()));
                                    $this->_dao->execute("insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,dta_7,createtime,createuser) values('SGcbzblg','龙钢成本周报-炼铁','$date','本周产量','$cell1','$enddate',NOW(),'".$user_infos['Uid']."')");
                                    $tsql = substr($valsql1, 0, -1);
                                    $this->_dao->execute($basesql1.$tsql);
                                    $tmpsheetName[] = mb_convert_encoding($sheetNames[$sheet_index],"GB2312","UTF-8");
                                }
                            break;
                            case 2:  //炼钢
                                $basesql2 = "insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,dta_3,dta_4,dta_5,dta_6,dta_7,createtime,createuser) values";
                                for($i=1;$i<=$rows;$i++)
                                {
                                    for($j="A";$j<="F";$j++)
                                    {
                                        $key = $j.$i;
                                        $data2[$i][$j] = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getFormattedValue()));
                                        
                                    }
                                }
                                foreach($data2 as $rownum2 => $rows_arr2)
                                {
                                    if($data2[$rownum2]["A"]!=""&&$rownum2!=1)
                                    {
                                        $dta2_1 = $data2[$rownum2]["A"];
                                        $dta2_2 = $data2[$rownum2]["B"];
                                        $dta2_3 = $data2[$rownum2]["C"];
                                        $dta2_4 = $data2[$rownum2]["D"];
                                        $dta2_5 = $data2[$rownum2]["E"];
                                        $dta2_6 = $data2[$rownum2]["F"];
                                        $valsql2 .= "('SGcbzblg','龙钢成本周报-炼钢','$date','$dta2_1','$dta2_2','$dta2_3','$dta2_4','$dta2_5','$dta2_6','$enddate',NOW(),'".$user_infos['Uid']."'),";
                                    }
                                }
                                if($valsql2)
                                {
                                    $this->_dao->execute("delete from sg_data_table where dta_ym='$date' and dta_type='SGcbzblg' and dta_7='$enddate' and dta_vartype='龙钢成本周报-炼钢'");
                                    $cell1 = $this->trimAll(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("F3")->getValue()));
                                    $this->_dao->execute("insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,dta_7,createtime,createuser) values('SGcbzblg','龙钢成本周报-炼钢','$date','本周产量','$cell1','$enddate',NOW(),'".$user_infos['Uid']."')");
                                    $tsql = substr($valsql2, 0, -1);
                                    $this->_dao->execute($basesql2.$tsql);
                                    $tmpsheetName[] = mb_convert_encoding($sheetNames[$sheet_index],"GB2312","UTF-8");
                                }
                            break;
                            case 3:  //轧钢
                                $basesql3 = "insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,dta_3,dta_4,dta_5,dta_6,dta_7,createtime,createuser) values";
                                for($i=1;$i<=$rows;$i++)
                                {
                                    for($j="A";$j<="F";$j++)
                                    {
                                        $key = $j.$i;
                                        $data3[$i][$j] = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getFormattedValue()));
                                        
                                    }
                                }
                                foreach($data3 as $rownum3 => $rows_arr3)
                                {
                                    if($data3[$rownum3]["A"]!=""&&$rownum3!=1)
                                    {
                                        $dta3_1 = $data3[$rownum3]["A"];
                                        $dta3_2 = $data3[$rownum3]["B"];
                                        $dta3_3 = $data3[$rownum3]["C"];
                                        $dta3_4 = $data3[$rownum3]["D"];
                                        $dta3_5 = $data3[$rownum3]["E"];
                                        $dta3_6 = $data3[$rownum3]["F"];
                                        $valsql3 .= "('SGcbzblg','龙钢成本周报-轧钢','$date','$dta3_1','$dta3_2','$dta3_3','$dta3_4','$dta3_5','$dta3_6','$enddate',NOW(),'".$user_infos['Uid']."'),";
                                    }
                                }
                                if($valsql3)
                                {
                                    $this->_dao->execute("delete from sg_data_table where dta_ym='$date' and dta_7='$enddate' and dta_type='SGcbzblg' and dta_vartype='龙钢成本周报-轧钢'");
                                    $cell1 = $this->trimAll(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("F3")->getValue()));
                                    $this->_dao->execute("insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,dta_7,createtime,createuser) values('SGcbzblg','龙钢成本周报-轧钢','$date','本周产量','$cell1','$enddate',NOW(),'".$user_infos['Uid']."')");
                                    $tsql = substr($valsql3, 0, -1);
                                    $this->_dao->execute($basesql3.$tsql);
                                    $tmpsheetName[] = mb_convert_encoding($sheetNames[$sheet_index],"GB2312","UTF-8");
                                }
                            break;
                            default:
                            break;
                            
                        }
                    }
                    $sheetName = implode(',',$tmpsheetName);
                    $this->_dao->execute("insert into sg_data_table_log(uptype,filepath,filename,sheetname,createtime,createuser,uptypedate) value('$uptype','$file','$filename','$sheetName',NOW(),'".$user_infos['Uid']."','$date')");
                    $response['Success'] = "1";
                    $response['Message'] = "导入成功";
                }
            }
        }
        echo $this->pri_JSON($response);
        exit;
    }

    //5导入汉钢成本周报
    function drcbzbhg($params)
    {
        $file = SGUPLOADFILE."/".$params["tmp"];
        $filename = mb_convert_encoding($params["filename"],"GB2312","UTF-8");
        $date = $params["date"];
        $enddate = $params["enddate"];
        $uptype = $params["uptype"];
        $GUID = $params['GUID'];
        $mode = $params['mode'];//1=客户端，2=安卓，3=iOS
        $response = array(
            'Success'  => 1,
            'Message'  => '',
        );
        if($GUID==""){
            $response["Success"] = 0;
            // $response['Message'] = 'GUID不能为空';
        }
        if($response["Success"])
        {
            $user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
            if(empty($user_infos)){
                $response["Success"] = 0;
                // $response['Message'] = '用户不存在';
            }
            $date = mysql_real_escape_string($params["date"]);
            if($response["Success"])
            {
                require_once "../PHPExcel/PHPExcel.php";
                $type = pathinfo($file); 
                $type = strtolower($type["extension"]);
                if ($type=='xlsx') { 
                    $type='Excel2007'; 
                }elseif($type=='xls') { 
                    $type = 'Excel5';
                }
                $sheetList = explode(',', $params["sheet"]);
                if(trim($sheetList[0])=="")
                {
                    $response["Success"] = 0;
                    $response['Message'] = '未选择工作表';
                }
                if($response["Success"])
                {
                    $objReader = PHPExcel_IOFactory::createReader($type);//判断使用哪种格式
                    $objReader ->setReadDataOnly(true); //只读取数据,会智能忽略所有空白行,这点很重要！！！
                    $objPHPExcel = $objReader->load($file); //加载Excel文件
                    $sheetNames = $objPHPExcel->getSheetNames();//所有的工作表名
                    $sheetCount = $objPHPExcel->getSheetCount();//获取sheet工作表总个数
                    // print_r($sheetNames);
                    // echo $sheetCount;
                    $flag = true;
                    $tmpsheetName = array();
                    foreach($sheetList as $sheet_index)
                    {
                        $sheet = $objPHPExcel->getSheet($sheet_index);
                        // $sheet_array = $objPHPExcel->getSheet($sheet_index)->toArray();
                        //获取当前工作表最大行数
                        $rows = $sheet->getHighestRow();
                        //获取当前工作表最大列数,返回的是最大的列名，如：B 
                        $cols = $sheet->getHighestColumn();
                        switch($sheet_index)
                        {
                            case 0:  //烧结
                                $basesql0 = "insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,dta_3,dta_4,dta_5,dta_6,dta_7,dta_8,dta_9,dta_10,dta_11,dta_12,dta_13,createtime,createuser) values";
                                for($i=2;$i<=$rows;$i++)
                                {
                                    $C = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("C".$i)->getValue()));
                                    $D = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("D".$i)->getValue()));
                                    $E = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("E".$i)->getValue()));
                                    if($this->isHan($C)&&$this->isHan($D)&&$this->isHan($E))
                                    {
                                        continue;
                                    }
                                    if((strpos($this->trimAll(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("A".$i)->getValue())), '备注')!==false))
                                    {
                                        continue;
                                    }
                                    if(trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("A".$i)->getValue()))!="")
                                    {
                                        $dta_1 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("A".$i)->getFormattedValue()));
                                        $dta_2 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("B".$i)->getFormattedValue()));
                                        $dta_3 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("C".$i)->getFormattedValue()));
                                        $dta_4 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("D".$i)->getFormattedValue()));
                                        $dta_5 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("E".$i)->getFormattedValue()));
                                        $dta_6 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("F".$i)->getFormattedValue()));
                                        $dta_7 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("G".$i)->getFormattedValue()));
                                        $dta_8 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("H".$i)->getFormattedValue()));
                                        $dta_9 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("I".$i)->getFormattedValue()));
                                        $dta_10 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("J".$i)->getFormattedValue()));
                                        $dta_11 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("K".$i)->getFormattedValue()));
                                        $dta_12 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("L".$i)->getFormattedValue()));
                                        $valsql0 .= "('SGcbzbhg','汉钢成本周报-烧结','$date','$dta_1','$dta_2','$dta_3','$dta_4','$dta_5','$dta_6','$dta_7','$dta_8','$dta_9','$dta_10','$dta_11','$dta_12','$enddate',NOW(),'".$user_infos['Uid']."'),";
                                    }
                                }
                                if($valsql0)
                                {
                                    $this->_dao->execute("delete from sg_data_table where dta_ym='$date' and dta_13='$enddate' and dta_type='SGcbzbhg' and dta_vartype='汉钢成本周报-烧结'");
                                    $cell1 = $this->trimAll(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("K3")->getValue()));
                                    $cell2 = $this->trimAll(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("K4")->getValue()));
                                    $this->_dao->execute("insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,dta_13,createtime,createuser) values('SGcbzblg','龙钢成本周报-烧结','$date','本周产量','$cell1','$enddate',NOW(),'".$user_infos['Uid']."')");
                                    $this->_dao->execute("insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,dta_13,createtime,createuser) values('SGcbzblg','龙钢成本周报-烧结','$date','本月累计产量','$cell2','$enddate',NOW(),'".$user_infos['Uid']."')");
                                    $tsql = substr($valsql0, 0, -1);
                                    $this->_dao->execute($basesql0.$tsql);
                                    $tmpsheetName[] = mb_convert_encoding($sheetNames[$sheet_index],"GB2312","UTF-8");
                                }
                            break;
                            case 1:  //炼铁
                                $basesql1_path1 = "insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,dta_3,dta_4,dta_5,dta_6,dta_7,dta_8,dta_9,dta_10,dta_11,dta_12,dta_13,dta_14,createtime,createuser) values";  //dta_13也作为单位列
                                $basesql1_path2 = "insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,dta_3,dta_4,dta_5,dta_6,dta_7,dta_8,dta_9,dta_10,dta_11,dta_12,dta_13,createtime,createuser) values";
                                for($t=2;$t<=$rows;$t++)
                                {
                                    $C_list[] = $this->trimAll(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("C".$t)->getValue()));
                                }
                                if(in_array('单位', $C_list))
                                {
                                    $path = 1;
                                }
                                else
                                {
                                    $path = 2;
                                }

                                for($i=2;$i<=$rows;$i++)
                                {
                                    $C = $this->trimAll(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("C".$i)->getValue()));
                                    $D = $this->trimAll(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("D".$i)->getValue()));
                                    $E = $this->trimAll(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("E".$i)->getValue()));

                                    if($this->isHan($C)&&$this->isHan($D)&&$this->isHan($E))
                                    {
                                        continue;
                                    }

                                    if((strpos($this->trimAll(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("A".$i)->getValue())), '备注')!==false))
                                    {
                                        continue;
                                    }

                                    if(trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("A".$i)->getValue()))!=""&&$path==1)
                                    {
                                        $dta_1 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("A".$i)->getFormattedValue()));
                                        $dta_2 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("B".$i)->getFormattedValue()));
                                        $dta_3 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("C".$i)->getFormattedValue()));
                                        $dta_4 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("D".$i)->getFormattedValue()));
                                        $dta_5 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("E".$i)->getFormattedValue()));
                                        $dta_6 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("F".$i)->getFormattedValue()));
                                        $dta_7 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("G".$i)->getFormattedValue()));
                                        $dta_8 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("H".$i)->getFormattedValue()));
                                        $dta_9 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("I".$i)->getFormattedValue()));
                                        $dta_10 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("J".$i)->getFormattedValue()));
                                        $dta_11 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("K".$i)->getFormattedValue()));
                                        $dta_12 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("L".$i)->getFormattedValue()));
                                        $dta_13 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("M".$i)->getFormattedValue()));
                                        $valsql1_path1 .= "('SGcbzbhg','汉钢成本周报-炼铁','$date','$dta_1','$dta_2','$dta_4','$dta_5','$dta_6','$dta_7','$dta_8','$dta_9','$dta_10','$dta_11','$dta_12','$dta_13','$dta_3','$enddate',NOW(),'".$user_infos['Uid']."'),";
                                    }

                                    if(trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("A".$i)->getValue()))!=""&&$path==2)
                                    {
                                        $dta_1 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("A".$i)->getVagetFormattedValuelue()));
                                        $dta_2 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("B".$i)->getVagetFormattedValuelue()));
                                        $dta_3 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("C".$i)->getVagetFormattedValuelue()));
                                        $dta_4 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("D".$i)->getVagetFormattedValuelue()));
                                        $dta_5 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("E".$i)->getVagetFormattedValuelue()));
                                        $dta_6 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("F".$i)->getVagetFormattedValuelue()));
                                        $dta_7 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("G".$i)->getVagetFormattedValuelue()));
                                        $dta_8 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("H".$i)->getVagetFormattedValuelue()));
                                        $dta_9 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("I".$i)->getVagetFormattedValuelue()));
                                        $dta_10 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("J".$i)->getVgetFormattedValuealue()));
                                        $dta_11 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("K".$i)->getVgetFormattedValuealue()));
                                        $dta_12 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("L".$i)->getVgetFormattedValuealue()));
                                        $valsql1_path2 .= "('SGcbzbhg','汉钢成本周报-炼铁','$date','$dta_1','$dta_2','$dta_3','$dta_4','$dta_5','$dta_6','$dta_7','$dta_8','$dta_9','$dta_10','$dta_11','$dta_12','$enddate',NOW(),'".$user_infos['Uid']."'),";
                                    }
                                }
                                if($valsql1_path2)
                                {
                                    $this->_dao->execute("delete from sg_data_table where dta_ym='$date' and dta_13='$enddate' and dta_type='SGcbzbhg' and dta_vartype='汉钢成本周报-炼铁'");
                                    $tsql = substr($valsql1_path2, 0, -1);
                                    $this->_dao->execute($basesql1_path2.$tsql);
                                    $tmpsheetName[] = mb_convert_encoding($sheetNames[$sheet_index],"GB2312","UTF-8");
                                }
                                if($valsql1_path1)
                                {
                                    $this->_dao->execute("delete from sg_data_table where dta_ym='$date' and dta_14='$enddate' and dta_type='SGcbzbhg' and dta_vartype='汉钢成本周报-炼铁'");
                                    $tsql = substr($valsql1_path1, 0, -1);
                                    $this->_dao->execute($basesql1_path1.$tsql);
                                    $tmpsheetName[] = mb_convert_encoding($sheetNames[$sheet_index],"GB2312","UTF-8");
                                }
                                $cell1 = $this->trimAll(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("M3")->getValue()));
                                $cell2 = $this->trimAll(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("M4")->getValue()));
                                $this->_dao->execute("insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,dta_14,createtime,createuser) values('SGcbzbhg','汉钢成本周报-炼铁','$date','本周产量','$cell1','$enddate',NOW(),'".$user_infos['Uid']."')");
                                $this->_dao->execute("insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,dta_14,createtime,createuser) values('SGcbzbhg','汉钢成本周报-炼铁','$date','本月累计产量','$cell2','$enddate',NOW(),'".$user_infos['Uid']."')");
                            break;
                            case 2:  //炼钢
                                $basesql2 = "insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,dta_3,dta_4,dta_5,dta_6,dta_7,dta_8,dta_9,dta_10,dta_11,dta_12,dta_13,createtime,createuser) values";
                                for($i=2;$i<=$rows;$i++)
                                {
                                    $C = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("C".$i)->getValue()));
                                    $D = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("D".$i)->getValue()));
                                    $E = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("E".$i)->getValue()));
                                    if($this->isHan($C)&&$this->isHan($D)&&$this->isHan($E))
                                    {
                                        continue;
                                    }
                                    if((strpos($this->trimAll(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("A".$i)->getValue())), '备注')!==false))
                                    {
                                        continue;
                                    }
                                    if(trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("A".$i)->getValue()))!="")
                                    {
                                        $dta_1 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("A".$i)->getFormattedValue()));
                                        $dta_2 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("B".$i)->getFormattedValue()));
                                        $dta_3 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("C".$i)->getFormattedValue()));
                                        $dta_4 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("D".$i)->getFormattedValue()));
                                        $dta_5 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("E".$i)->getFormattedValue()));
                                        $dta_6 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("F".$i)->getFormattedValue()));
                                        $dta_7 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("G".$i)->getFormattedValue()));
                                        $dta_8 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("H".$i)->getFormattedValue()));
                                        $dta_9 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("I".$i)->getFormattedValue()));
                                        $dta_10 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("J".$i)->getFormattedValue()));
                                        $dta_11 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("K".$i)->getFormattedValue()));
                                        $dta_12 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("L".$i)->getFormattedValue()));
                                        $valsql2 .= "('SGcbzbhg','汉钢成本周报-炼钢','$date','$dta_1','$dta_2','$dta_3','$dta_4','$dta_5','$dta_6','$dta_7','$dta_8','$dta_9','$dta_10','$dta_11','$dta_12','$enddate',NOW(),'".$user_infos['Uid']."'),";
                                    }
                                }
                                if($valsql2)
                                {
                                    $this->_dao->execute("delete from sg_data_table where dta_ym='$date' and dta_13='$enddate' and dta_type='SGcbzbhg' and dta_vartype='汉钢成本周报-炼钢'");
                                    $cell1 = $this->trimAll(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("L2")->getValue()));
                                    $cell2 = $this->trimAll(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("L3")->getValue()));
                                    $this->_dao->execute("insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,dta_13,createtime,createuser) values('SGcbzbhg','汉钢成本周报-炼钢','$date','本周产量','$cell1','$enddate',NOW(),'".$user_infos['Uid']."')");
                                    $this->_dao->execute("insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,dta_13,createtime,createuser) values('SGcbzbhg','汉钢成本周报-炼钢','$date','本月累计产量','$cell2','$enddate',NOW(),'".$user_infos['Uid']."')");
                                    $tsql = substr($valsql2, 0, -1);
                                    $this->_dao->execute($basesql2.$tsql);
                                    $tmpsheetName[] = mb_convert_encoding($sheetNames[$sheet_index],"GB2312","UTF-8");
                                }
                            break;
                            case 3:  //轧钢
                                $basesql3 = "insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,dta_3,dta_4,dta_5,dta_6,dta_7,dta_8,dta_9,dta_10,dta_11,dta_12,dta_13,createtime,createuser) values";
                                for($i=2;$i<=$rows;$i++)
                                {
                                    $C = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("C".$i)->getValue()));
                                    $D = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("D".$i)->getValue()));
                                    $E = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("E".$i)->getValue()));
                                    if($this->isHan($C)&&$this->isHan($D)&&$this->isHan($E))
                                    {
                                        continue;
                                    }
                                    if((strpos($this->trimAll(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("A".$i)->getValue())), '备注')!==false))
                                    {
                                        continue;
                                    }
                                    if(trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("A".$i)->getValue()))!="")
                                    {
                                        $dta_1 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("A".$i)->getFormattedValue()));
                                        $dta_2 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("B".$i)->getFormattedValue()));
                                        $dta_3 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("C".$i)->getFormattedValue()));
                                        $dta_4 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("D".$i)->getFormattedValue()));
                                        $dta_5 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("E".$i)->getFormattedValue()));
                                        $dta_6 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("F".$i)->getFormattedValue()));
                                        $dta_7 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("G".$i)->getFormattedValue()));
                                        $dta_8 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("H".$i)->getFormattedValue()));
                                        $dta_9 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("I".$i)->getFormattedValue()));
                                        $dta_10 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("J".$i)->getFormattedValue()));
                                        $dta_11 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("K".$i)->getFormattedValue()));
                                        $dta_12 = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("L".$i)->getFormattedValue()));
                                        $valsql3 .= "('SGcbzbhg','汉钢成本周报-轧钢','$date','$dta_1','$dta_2','$dta_3','$dta_4','$dta_5','$dta_6','$dta_7','$dta_8','$dta_9','$dta_10','$dta_11','$dta_12','$enddate',NOW(),'".$user_infos['Uid']."'),";
                                    }
                                }
                                if($valsql3)
                                {
                                    $this->_dao->execute("delete from sg_data_table where dta_ym='$date' and dta_13='$enddate' and dta_type='SGcbzbhg' and dta_vartype='汉钢成本周报-轧钢'");
                                    $cell1 = $this->trimAll(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("K3")->getValue()));
                                    $cell2 = $this->trimAll(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("K4")->getValue()));
                                    $this->_dao->execute("insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,dta_13,createtime,createuser) values('SGcbzbhg','汉钢成本周报-轧钢','$date','本周产量','$cell1','$enddate',NOW(),'".$user_infos['Uid']."')");
                                    $this->_dao->execute("insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,dta_13,createtime,createuser) values('SGcbzbhg','汉钢成本周报-轧钢','$date','本月累计产量','$cell2','$enddate',NOW(),'".$user_infos['Uid']."')");
                                    $tsql = substr($valsql3, 0, -1);
                                    $this->_dao->execute($basesql3.$tsql);
                                    $tmpsheetName[] = mb_convert_encoding($sheetNames[$sheet_index],"GB2312","UTF-8");
                                }
                            break;
                            default:
                            break;
                            
                        }
                    }
                    $sheetName = implode(',',$tmpsheetName);
                    $this->_dao->execute("insert into sg_data_table_log(uptype,filepath,filename,sheetname,createtime,createuser,uptypedate) value('$uptype','$file','$filename','$sheetName',NOW(),'".$user_infos['Uid']."','$date')");
                    $response['Success'] = "1";
                    $response['Message'] = "导入成功";
                }
            }
        }
        echo $this->pri_JSON($response);
        exit;
    }

    //大文件上传
    public function bigFileUpload($params)
    {
        $upfile = $_FILES['file'];
        $uptype = $params['uptype'];
        $GUID = $params['GUID'];
        $mode = $params['mode'];
        $response = array(
            'Success'  => 1,
            'Message'  => '',
            'GUID'     => $GUID,
            'mode'     => $mode,
        );
        if($GUID==""){
            $response['Success'] = 0;
            $response['Message'] = 'GUID不能为空';
        }
        if($response['Success']){
            $user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
            if(empty($user_infos)){
                $response['Success'] = 0;
                $response['Message'] = '用户不存在';
            }
            if($response['Success']){
                $path = SGUPLOADFILE;
                $memcache= new Memcache();
                $memcache->connect(MEMCACHE_SERVER, MEMCACHE_PORT);
                $expire = 1200;
                for ($i=0; $i<4; $i++) {
                    $randnum .= mt_rand(0, 9);
                }
                $params['name'] = iconv("utf-8", "GBK", $params['name']);
                $type = strtolower(substr(strrchr($params['name'], '.'), 1)); //获取文件类型
                $destication_frag_path = $path."/uploads_tmp";  //分片上传的临时文件夹
                if (!file_exists($destication_frag_path)) {
                    mkdir($destication_frag_path, 0777);
                }
                $destication_frag_path =$destication_frag_path."/";
                $redis_key = $params['name'];
                $file_name = explode('.', $params['name']);
                $save_tmp_name = $destication_frag_path.$file_name[0]."_".$params['chunk']."-".$randnum;
                $tmp_file_path = $upfile['tmp_name']; //上传的临时文件
                move_uploaded_file($tmp_file_path, $save_tmp_name);
                if ($params['chunk']==0) {
                    $memcache->set($redis_key, array(), MEMCACHE_COMPRESSED, $expire);
                }
                $all_files_fen_pian = $memcache->get($redis_key);
                $all_files_fen_pian[$params['chunk']]=$save_tmp_name;
                $var = $memcache->set($redis_key, $all_files_fen_pian, MEMCACHE_COMPRESSED, $expire);
                $all_files_fen_pian = $memcache->get($redis_key); //获取分片资源
                $uploaded_count = count($all_files_fen_pian);
                //分片资源上传完毕后，开始分片合并工作
                if ($uploaded_count == $params['chunks']) {
                    if ($all_files_fen_pian && is_array($all_files_fen_pian)) {
                        //创建要合并的最终文件资源
                        $filename = "SG".$uptype."-".$this->random(5);
                        $name1 = date('YmdHis').".".$type;
                        $final_file = $path."/".$filename.$name1;
                        if (file_exists($final_file)) {
                            //开始合并文件分片
                            foreach ($all_files_fen_pian as $fragmentation_file) {
                                $frag_file_handler  = fopen($fragmentation_file, 'rb');
                                fclose($frag_file_handler);      //销毁分片文件资源
                                unlink($fragmentation_file);     //删除已经合并的分片文件
                                usleep(10000);
                            }
                            $response["Success"] = "0";
                            $response["Message"] = "该文件已存在";
                        } else {
                            $final_file_handler = fopen($final_file, 'wb');
                            //开始合并文件分片
                            foreach ($all_files_fen_pian as $fragmentation_file) {
                                $frag_file_handler  = fopen($fragmentation_file, 'rb');
                                $frag_file_content = fread($frag_file_handler, filesize($fragmentation_file));
                                fwrite($final_file_handler, $frag_file_content);
                                unset($frag_file_content);
                                fclose($frag_file_handler);      //销毁分片文件资源
                                unlink($fragmentation_file);     //删除已经合并的分片文件
                                usleep(10000);
                            }

                            if ($params['chunk']==($params['chunks']-1)) { //临时文件转移到目标文件夹

                                if($uptype==3||$uptype==4)
                                {
                                    $par = array(
                                        "tmp" => $filename.$name1,
                                        "filename" => mb_convert_encoding($params["name"],"UTF-8","GB2312"),
                                        "date" => $params['date'],
                                        "GUID" => $GUID,
                                        "uptype" => $uptype,
                                        "mode" => $mode,
                                        "sheet" => "20,21",
                                    );
                                    $this->drcbrbhg($par);
                                }
                                else
                                {
                                    $response['Success'] = 1;
                                    $response['Message'] = '上传成功';
                                    $sheets = $this->readSheet($final_file); //获取工作表列表
                                    $response['Result'] = $this->array_iconv($sheets);
                                    $response['File'] = $filename.$name1;
                                    $response['Date'] = $params['date'];
                                    $response["FileName"] = $params['name'];
                                }

                            } else {
                                $response["Success"] = "0";
                                $response["Message"] = "上传有误，检查服务器配置";
                            }
                        }
                    }else{
                        // $response["Success"] = "0";
                        // $response["Message"] = "分片文件出错";
                    }
                }else{
                    // $response["Success"] = "0";
                    // $response["Message"] = "分片数量出错";
                }
            }
        }
        echo $this->pri_JSON($response);
        // exit;
    }

    //普通文件上传
    function uploadFile($params){
        $upfile = $_FILES['file'];
        $GUID = $params['GUID'];
        $mode = $params['mode'];
        $uptype = $params['uptype'];
        $response = array(
            'Success'  => 1,
            'Message'  => '',
            'GUID'     => $GUID,
            'mode'     => $mode,
        );
        // 不同页面上传类型控制
        switch($params['filetype'])
        {
            case 'excel':
                $uptypes=array(
                    'application/vnd.ms-excel',
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                );
                $filetype = "Excel文件";
            break;
            case 'word':
                $uptypes=array(
                    'application/msword',
                    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                );
                $filetype = "Word文件";
            break;
            default:
                $uptypes=array(
                    'application/vnd.ms-excel',
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                );
                $filetype = "Excel文件";
            break;
        }

        if($GUID==""){
            $response['Success'] = 0;
            $response['Message'] = 'GUID不能为空';
        }
        if($response['Success']){
            $user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
            if(empty($user_infos)){
                $response['Success'] = 0;
                $response['Message'] = '用户不存在';
            }
            if($response['Success']){
                if ($upfile['error']==0)
                {
                    if (is_uploaded_file($upfile['tmp_name'])) {
                        if (in_array($upfile['type'], $uptypes)) {
                            $extName = strtolower(end(explode('.', $upfile ['name'])));
                            $filename = "SG".$uptype."-".$this->random(5); // 设置随机数长度
                            $extName=strtolower(end(explode('.', $upfile['name']))); // 取得扩展名
                            $name1=date('YmdHis').".".$extName;
                            $dir = SGUPLOADFILE;
                            if (!file_exists($dir)) {
                                if (mkdir($dir, 0777)) {
                                    $dest =$dir."/".$filename.$name1;
                                    if (move_uploaded_file($upfile ['tmp_name'], $dest)) {
                                        if($uptype==4)
                                        {
                                            $par = array(
                                                "tmp" => $filename.$name1,
                                                "filename" => $upfile["name"],
                                                "date" => $params['date'],
                                                "GUID" => $GUID,
                                                "uptype" => $uptype,
                                                "mode" => $mode,
                                                "sheet" => "0,1",
                                            );
                                            $this->drcbrblg($par);
                                        }
                                        else
                                        {
                                            $response['Success'] = 1;
                                            $response['Message'] = '上传成功';
                                            $sheets = $this->readSheet($dest); //获取工作表列表
                                            $response['Result'] = $this->array_iconv($sheets);
                                            $response['File'] = $filename.$name1;
                                            $response['FileName'] = mb_convert_encoding($upfile['name'],'gb2312','utf-8');
                                            $response['Date'] = $params['date'];
                                            $response['EndDate2'] = $params['enddate'];
                                        }
                                    } else {
                                        $response['Success'] = 0;
                                        $response['Message'] = '上传失败，目录权限不足';
                                    }
                                } else {
                                    $response['Success'] = 0;
                                    $response['Message'] = '目录不存在，上传失败';
                                }
                            } else {
                                $dest = $dir."/".$filename.$name1;
                                if (move_uploaded_file($upfile ['tmp_name'], $dest)) {
                                    if($uptype==4)
                                    {
                                        $par = array(
                                            "tmp" => $filename.$name1,
                                            "filename" => $upfile["name"],
                                            "date" => $params['date'],
                                            "GUID" => $GUID,
                                            "uptype" => $uptype,
                                            "mode" => $mode,
                                            "sheet" => "0,1",
                                        );
                                        $this->drcbrblg($par);
                                    }
                                    else
                                    {
                                        $response['Success'] = 1;
                                        $response['Message'] = '上传成功';
                                        $sheets = $this->readSheet($dest); //获取工作表列表
                                        $response['Result'] = $this->array_iconv($sheets);
                                        $response['File'] = $filename.$name1;
                                        $response['FileName'] = mb_convert_encoding($upfile['name'],'gb2312','utf-8');
                                        $response['Date'] = $params['date'];
                                        $response['EndDate2'] = $params['enddate'];
                                    }
                                } else {
                                    $response['Success'] = 0;
                                    $response['Message'] = '上传失败，目录权限不足';
                                }
                            }
                        } else {
                            $response['Success'] = 0;
                            $response['Message'] = '上传失败，检查文件是否是'.$filetype;
                        }
                    } else {
                        $response['Success'] = 0;
                        $response['Message'] = '上传失败';
                        clearstatcache(); //清除文件缓存信息
                    }
                }
                else
                {
                    $response['Success'] = 0;
                    $response['Message'] = '上传失败';
                }
            }
        }
        echo $this->pri_JSON($response);
        exit;
    }

    //读取工作表并返回
    function readSheet($file, $year)
    {
        ini_set('memory_limit', '512M');
        require_once "../PHPExcel/PHPExcel.php";
        $type = pathinfo($file); 
        $type = strtolower($type["extension"]);
        if ($type=='xlsx') { 
            $type='Excel2007'; 
        }elseif($type=='xls') { 
            $type = 'Excel5';
        }
        
        $objReader = PHPExcel_IOFactory::createReader($type);//判断使用哪种格式
        $objReader ->setReadDataOnly(true); //只读取数据,会智能忽略所有空白行,这点很重要！！！
        $objPHPExcel = $objReader->load($file); //加载Excel文件
        // $sheets = $objPHPExcel->getSheetNames();
        $cnt = 0;
        foreach ($objPHPExcel->getWorksheetIterator() as $sheet) {
            if ($sheet->getSheetState() === 'hidden') {
                $hiddenSheet[$cnt] = $sheet->getTitle();
            }else{
                $sheetName[$cnt] = $sheet->getTitle();
            }
            $cnt++;
        }
        
        // $sheetCount = $objPHPExcel->getSheetCount();//获取sheet工作表总个数
        
        // foreach($sheets as $k=>$v)
        // {
        //     if(in_array($v, $hiddenSheet))
        //     {
        //         unset($sheets[$k]);
        //     }
        // }
        // return $sheets;
        return $sheetName;
    }

    //文件名随机字符串
    public function random($length)
    {
        $hash = '';
        $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789abcdefghijklmnopqrstuvwxyz';
        $max = strlen($chars) - 1;
        mt_srand(( double ) microtime() * 1000000);
        for ($i = 0; $i < $length; $i ++) {
            $hash .= $chars [mt_rand(0, $max)];
        }
        return $hash;
    }

    private function pri_JSON($array)
    {
        $this->pri_arrayRecursive($array, 'urlencode', true);
        $json = json_encode($array);
        return iconv("GB2312", "UTF-8", urldecode($json));
    }

    private function pri_arrayRecursive(&$array, $function, $apply_to_keys_also = false)
    {
        static $recursive_counter = 0;
        if (++$recursive_counter > 1000) {
            die('possible deep recursion attack');
        }
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $this->pri_arrayRecursive($array[$key], $function, $apply_to_keys_also);
            } else {
                $array[$key] = $function($value);
            }
            if ($apply_to_keys_also && is_string($key)) {
                $new_key = $function($key);
                if ($new_key != $key) {
                    $array[$new_key] = $array[$key];
                    unset($array[$key]);
                }
            }
        }
        $recursive_counter--;
    }
    
    //数组转码
    public function array_iconv($str, $in_charset="utf-8", $out_charset="gb2312")
    {
        if (is_array($str)) {
            foreach ($str as $k => $v) {
                $str[$k] = $this->array_iconv($v);
            }
            return $str;
        } else {
            if (is_string($str)) {
                return mb_convert_encoding($str, $out_charset, $in_charset);
            } else {
                return $str;
            }
        }
    }
    
    public function isHan($str)
    {
        if($str=="")
        {
            return false;
        }
        else
        {
            if (preg_match_all("/^([\x81-\xfe][\x40-\xfe])+$/", $str, $match))
            {
                //全是中文
                return true;
            }
            else
            {
                //不全是中文
                return false;
            }
        }            
    }

    //去除所有空格、换行等
    public function trimAll($str)
    {
        $oldchar=array(" ","　","\t","\n","\r");
        $newchar=array("","","","","");
        return str_replace($oldchar,$newchar,"$str");
    }
}

?>