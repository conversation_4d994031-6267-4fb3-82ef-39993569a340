<?php
class ChartDirectorAction extends AbstractAction
{

    public $oadao;

    public function __construct()
    {
        parent::__construct();
    }


    public function linechart($params, $mes="")
    {
        $isEnglish = $params["isEnglish"];
        if ($isEnglish) {
            $mes = EN_DATASOURCEMES;
            $junzhi = "AVG";
        } else {
            $mes = CN_DATASOURCEMES;
            $junzhi = "均值";
        }
        //echo BASE_DIR;
        require_once(CHARTDIRECTOR_PATH);
        if (isset($_GET['debug'] ) && _GET['debug'] == 2) {
            print"<pre>";
            print_r($params);
            exit;
        }
        //echo 'dddd';
        # The data for the line chart
        $n = 0;
        if ( isset( $params['data'] ) && $params['data'] ) {
            $n = count($params['data']);
        }

        # The labels for the line chart
        //$labels=$params['xlabel'];
        $ytwoflag = isset($params['twoy']) ? $params['twoy']:"";
        $lendtitle = isset($params['lendtitle']) ? $params['lendtitle']:array();
//        print"<pre>"; print_r($lendtitle);
        if ( $lendtitle ) $lendtitle = $this->gb2312ToUtf8Array($lendtitle);
//        print"<pre>"; print_r($lendtitle);
        $yaxistitle = isset($params['yaxistitle']) ? $params['yaxistitle']:"";
        $labels = isset($params['xlabel']) ? $params['xlabel']:"";
        $avg_print = array();
        $avg = array();
        if ($params['isjz'] == "1")  //判断均值是否显示 add by zhangcun 2016/8/11
        {
            for ($i = 0; $i < $n; $i++) {
                $avg[$i] = round(array_sum($params['data'][$i]) / count($params['data'][$i]), 2);
                $avg_print[$i] = "  " . $junzhi . ":" . $avg[$i];
            }
        }

        # Create a XYChart object of size 540 x 375 pixels
        //$linecolor=array(0xfb0303,0xa260e6,0x575ae1,0x66dede,0x8B008B);
        //$linecolor=array(0xff0000,0x1007bd,0xffc000,0xa004c1,0x10c358);
        $linecolor = array();
        if ($params['color'] == "彩色" || $params['color'] == "0") {
            $linecolor = array(0x4F81BD, 0xC0504D, 0x9BBB59, 0x8166A2);
        } elseif ($params['color'] == "蓝色" || $params['color'] == "1") {
            $linecolor = array(0x3C6494, 0x4978B1, 0x7E9BC8, 0xB6C3DC);
        } else {
            $linecolor = array(0x4F81BD, 0xC0504D, 0x9BBB59, 0x8166A2);
        }
        $c = new XYChart(600, 350);//369*198
        $c->setDefaultFonts(CREATE_IMG_TTF_PATH);
        # Add a title to the chart using 18 pts Times Bold Italic font
        $c->addTitle( $params['charttitle'], CREATE_IMG_TTF_PATH, 15);
        if ( !empty($lendtitle) && (count($lendtitle) == 3 || count($lendtitle) == 4 )) {
            $plotarea = $c->setPlotArea(55, 105, 480, 200, 0xffffff, 0xffffff, 0xe9e9e9, 0xc0c0c0);
        } elseif ( !empty($lendtitle) && count($lendtitle) == 5) {
            $plotarea = $c->setPlotArea(55, 115, 480, 200, 0xffffff, 0xffffff, 0xe9e9e9, 0xc0c0c0);
        } else {
            $plotarea = $c->setPlotArea(55, 90, 480, 200, 0xffffff, 0xffffff, 0xe9e9e9, 0xc0c0c0);
        }
        //$plotarea = $c->setPlotArea(50, 90, 500, 200, 0xeeeeee, 0xffffff, 0xe9e9e9, 0xc0c0c0);
        if (count($lendtitle) == 3) {
            $jl = 325;
            $pta = 105;
        } elseif (count($lendtitle) == 4) {
            $jl = 325;
            $pta = 115;
        } else {
            $jl = 310;
            $pta = 90;
        }

        if ($params['isbg'] == "1") {
        } else {
            $plotarea->setBackground2(dirname(__FILE__) . "/../../images/" . BG_IMG);
        }
        $textBoxObj = $c->addText(535, $jl, $mes, CREATE_IMG_TTF_PATH, 10, 0x000000);
        $textBoxObj->setAlignment(TopRight);


        $legendObj = $c->addLegend(60, 28, false, CREATE_IMG_TTF_PATH, 10);
        $legendObj->setBackground(Transparent);
        # Set the x axis labels
        //$c->xAxis->setLabels($labels);
        //$c->xAxis->setLabelStep(2);
        # Set axis label style to 8pts Arial Bold
        $c->xAxis->setLabelStyle(CREATE_IMG_TTF_PATH, 8, TextColor);
        $c->yAxis->setLabelStyle(CREATE_IMG_TTF_PATH, 8);
        # Set axis line width to 2 pixels
        $c->xAxis->setWidth(1);
        $c->yAxis->setWidth(1);
        // $c->setUseYAxis2->setWidth(2);
        # Add a line layer to the chart
        $i = 0;
        //exit;
        $dmin = array();
        $dmax = array();
        $datat = array();
        $minnum = array();
        $maxnum = array();

        for ($i = 0; $i < $n; $i++) {
            $layer = "layer" . $i;
            $$layer = $c->addLineLayer2();
            $$layer->setLineWidth(2);
            $datat = $params['data'][$i];
            $$layer->addDataSet($datat, $linecolor[$i], $this->getmytitle((isset( $lendtitle[$i] )? $lendtitle[$i]:"" ). $avg_print[$i], $i) );
        }

        foreach ($params['data'] as $i => $v) {
            $dmin[$i] = $params['data'][$i][0];
            $dmax[$i] = $params['data'][$i][0];
            $minnum[$i] = 0;
            $maxnum[$i] = 0;
            foreach ($v as $key => $val) {
                if ($val < $dmin[$i]) {
                    $dmin[$i] = $val;
                    $minnum[$i] = $key;
                }
                if ($val > $dmax[$i]) {
                    $dmax[$i] = $val;
                    $maxnum[$i] = $key;
                }
            }
        }
        //print"<pre>"; print_R($labels);
        //print"<pre>"; print_R($dmax);
        //$countlabel=count($params['xlabel'])-1;
        //$stepcount=round($countlabel/6,0);
        //$stepcount=$countlabel/7;
        //$c->xAxis->setLabelStep($stepcount);
        //$c->xAxis->addMark($countlabel,0xffffff,$params['xlabel'][$countlabel]);
        if (count($labels) >= 50) {
            $ii = count($labels) / 7;
            $c->xAxis->addLabel(0, $labels[0]);
            for ($i = 0; $i < 7; $i++) {
                $c->xAxis->addLabel(($i * $ii), $labels[($i * $ii)]);
            }

            $c->xAxis->addLabel(count($labels) - 1, $labels[count($labels) - 1]);
        } elseif (count($labels) <= 12 && $params['bar_datetype']['data'] <= 3 && $params['issametype'] == 1) {
            foreach ($labels as $id => $val) {
                $c->xAxis->addLabel($id, $val);
            }
        } else {
            $c->xAxis->setLabels($labels);
            $countlabel = count($labels);
            $stepcount = round($countlabel / 6, 0);
            $c->xAxis->setLabelStep($stepcount);
            /*$c->xAxis->setLabels($labels);
              $countlabel=count($labels);
			  //$stepcount=round($countlabel/6,0);
			  $stepcount=$countlabel/7;
			  $c->xAxis->setLabelStep($stepcount);
			  */
        }
        // 设置Y轴最大最小值
// 			$min = ceil((min($dmin)-0.10*min($dmin))/10)*10;
// 			$max = ceil((max($dmax)+0.10*max($dmax))/10)*10;
// 			$c->yAxis->setLinearScale($min,$max);
        /*正负数y轴上下限处理*/

        if (10 * ($this->getmax($dmax) - $this->getmin($dmin)) > $this->getmax($dmax)) {
            if ($this->getmin($dmin) > 0) {
                $mina = 0.95 * $this->getmin($dmin);
            } else {
                $mina = 1.05 * $this->getmin($dmin);
            }
            if ($this->getmax($dmax) > 0) {
                $maxa = 1.05 * $this->getmax($dmax);
            } else {
                $maxa = 0.95 * $this->getmax($dmax);
            }
        } else {
            $i = (float)$this->getmax($dmax);       //add by zhangcun  for 线图上下限问题 2016/9/19
            $j = (float)$this->getmin($dmin);
            $k = $i - $j;
            if ($i - 1 > 0 && $j - 1 > 0) {
                $min = (int)$j - (int)($k / 5.0);
                $max = (int)$i + (int)($k / 5.0);
                $mina = $min - 1.0;
                $maxa = $max + 1.0;
            } else {                                //add by zhangcun 2017/7/19 注：对小于一的浮点数据重新处理，上下浮动五分之一，然后保留几位有效小数
                $min = (float)$j - (float)($k / 5.0);
                $max = (float)$i + (float)($k / 5.0);
                $mina = round($min / 1.0, $this->getwei($min));
                $maxa = round($max / 1.0, $this->getwei($max));
            }

            if ($j >= 0 && $mina < 0) $mina = 0;
            if ($i <= 0 && $maxa > 0) $maxa = 0;
        }
        /*正负数y轴上下限处理*/
        $c->yAxis->setLinearScale($mina, $maxa);
        //add by zhangcun for 峰谷值 started  2016/11/11
        if ($params['isfg'] == 1) {
            $xstart = 45; //横轴起始点
            $ystart = 280; //纵轴起始点
            $xlen = 480; //X轴长度
            $ylen = 200; //Y轴长度
            $jingdu = 2;  //最小值最大值保留小数位

            //一条线标记为黑色，多条线为了区分改为与线相同的颜色
            //为方便调整，峰值和谷值略有不同，可以加数字进行微调
            if ($n == 1) {
                $c->addText(round($xstart + $xlen * $minnum[0] / count($params['xlabel']), 0), round($ystart - ($ylen * ($dmin[0] - $mina) / ($maxa - $mina)), 0), round($dmin[0], $jingdu), CREATE_IMG_TTF_PATH, 9, 0x000000);
                $c->addText(round($xstart + $xlen * $maxnum[0] / count($params['xlabel']), 0), round($ystart - ($ylen * ($dmax[0] - $mina) / ($maxa - $mina)), 0), round($dmax[0], $jingdu), CREATE_IMG_TTF_PATH, 9, 0x000000);

            } else {
                for ($i = 0; $i < $n; $i++) {
                    $c->addText(round($xstart + $xlen * $minnum[$i] / count($params['xlabel']), 0), round($ystart - ($ylen * ($dmin[$i] - $mina) / ($maxa - $mina)), 0), round($dmin[$i], $jingdu), CREATE_IMG_TTF_PATH, 9, $linecolor[$i]);
                    $c->addText(round($xstart + $xlen * $maxnum[$i] / count($params['xlabel']), 0), round($ystart - ($ylen * ($dmax[$i] - $mina) / ($maxa - $mina)), 0), round($dmax[$i], $jingdu), CREATE_IMG_TTF_PATH, 9, $linecolor[$i]);
                }
            }
        }
        //add by zhangcun for 峰谷值 ended  2016/11/11
        # Configure the bars within a group to touch each others (no gap)
        // $layer1->setBarGap(0.2, TouchBar);
        //print_r($yaxistitle);
        //$textBoxObj = $c->yAxis->setTitle(iconv("GB2312", "UTF-8",$yaxistitle[0]),CREATE_IMG_TTF_PATH);
        $textBoxObj = $c->yAxis->setTitle($params['dw'][0], CREATE_IMG_TTF_PATH);
        $textBoxObj->setAlignment(TopLeft2);

        # Output the chart
        file_put_contents($params['url'], $c->makeChart2(PNG));
        // header("Content-type: image/png");
        // echo $c->makeChart2(PNG);
        // ob_clean();
        // print($c->makeChart2(PNG));
    }

    public function doublelinechart($params, $mes="")
    {

        $isEnglish = $params["isEnglish"];
        if ($isEnglish) {
            $mes = EN_DATASOURCEMES;
            $junzhi = "AVG";
        } else {
            $mes = CN_DATASOURCEMES;
            $junzhi = "均值";
        }
        //echo BASE_DIR;
        $newdata = array();
        require_once(CHARTDIRECTOR_PATH);
        //print"<pre>";print_r($params);
        foreach ($params['data'] as $k => $v) {
            //if($k<=1)
            {
                $newdata[] = $v;
                $newtitle[] = $params['lendtitle'][$k];
            }
        }

        $n = count($newdata);
        if ($n == 1) {
            $this->linechart($params);
        } else {
            if ($n == 2 && $params['zhou1'] == 0 && $params['zhou2'] == 0) $params['zhou2'] = 1;
            $ytwoflag = $params['twoy'];
            $lendtitle = $params['lendtitle'];
            if ( $lendtitle ) $lendtitle = $this->gb2312ToUtf8Array($lendtitle);
            $yaxistitle = $params['yaxistitle'];
            $labels = $params['xlabel'];
            $num = count($params['data']);
            # Create a XYChart object of size 540 x 375 pixels
            //$linecolor=array(0xfb0303,0xa260e6,0x575ae1,0x66dede,0x8B008B);
            //$linecolor=array(0xff0000,0x1007bd,0xffc000,0xa004c1,0x10c358);
            if ($params['color'] == "彩色" || $params['color'] == "0") {
                $linecolor = array(0x4F81BD, 0xC0504D, 0x9BBB59, 0x8166A2);
            } elseif ($params['color'] == "蓝色" || $params['color'] == "1") {
                $linecolor = array(0x3C6494, 0x4978B1, 0x7E9BC8, 0xB6C3DC);
            } else {
                $linecolor = array(0x4F81BD, 0xC0504D, 0x9BBB59, 0x8166A2);
            }
            if ($num == 4) {
                $jl = 322;
                $pta = 105;
            } elseif ($num == 5) {
                $jl = 325;
                $pta = 115;
            } else {
                $jl = 310;
                $pta = 90;
            }
            $c = new XYChart(600, 350);
            $c->setDefaultFonts(CREATE_IMG_TTF_PATH);
            # Add a title to the chart using 18 pts Times Bold Italic font
            $c->addTitle( $params['charttitle'], CREATE_IMG_TTF_PATH, 15);
            $plotarea = $c->setPlotArea(55, $pta, 460, 200, 0xffffff, 0xffffff, 0xe9e9e9, 0xc0c0c0);
            if ($params['isbg'] == "1") {
            } else {
                $plotarea->setBackground2(dirname(__FILE__) . "/../../images/" . BG_IMG);
            }
            //$plotarea->setBackground2(dirname(__FILE__)."/../../images/".BG_IMG);
            $textBoxObj = $c->addText(515, $jl,  $mes, CREATE_IMG_TTF_PATH, 10, 0x000000);
            $textBoxObj->setAlignment(TopRight);
            $legendObj = $c->addLegend(60, 28, false, CREATE_IMG_TTF_PATH, 10);
            $legendObj->setBackground(Transparent);
            # Set the x axis labels
            //$c->xAxis->setLabels($labels);
            //$c->xAxis->setLabelStep(2);
            # Set axis label style to 8pts Arial Bold
            $c->xAxis->setLabelStyle(CREATE_IMG_TTF_PATH, 8, TextColor);
            $c->yAxis->setLabelStyle(CREATE_IMG_TTF_PATH, 8);
            # Set axis line width to 2 pixels
            $c->xAxis->setWidth(1);
            $c->yAxis->setWidth(1);
            // $c->setUseYAxis2->setWidth(2);
            # Add a line layer to the chart

            if ($params['isjz'] == "1") {
                for ($i = 0; $i < $num; $i++) {
                    $avg[$i] = round(array_sum($params['data'][$i]) / count($params['data'][$i]), 2);
                    $avg_print[$i] = "  " . $junzhi . ":" . $avg[$i];
                }
            }

            $dmin = array();
            $dmax = array();
            $dminline = array();
            $dmaxline = array();
            $zhou = array();
            $ma = 0;
            $ldw = $rdw = "";
            foreach ($params as $id => $da) {
                if (strstr($id, "zhou") && strlen($id) == 5) {
                    //echo $id."=>".$da."<br>";
                    $index = str_replace("zhou", "", $id);
                    $zhou[$index] = empty($da) ? "0" : $da;
                    if ($da == 1 && $index - $num <= 0) $ma++;
                }
            }
            if ($ma > 0 && $ma < $num) $ma = 1;
            else if ($ma == $num) $ma = -1;
            else $ma = 0;
            //print"<pre>";print_r($zhou);//exit;
            //echo $num."<br>";echo $ma."<br>";exit;
            //print_r($params['dw']);
            foreach ($zhou as $id => $val) {
                $layer = "layer" . $id;
                if ($id > $num) {/*echo 3;*/
                    continue;
                }
                $i = 0;
                if ($val == 0 || $ma == -1) { //echo " 0 ";
                    $$layer = $c->addLineLayer2();
                    $$layer->setLineWidth(2);
                    //$datat=$datatemp[0];
                    $datat = $newdata[$id - 1];
                    $dmin[$id] = $this->getmin($newdata[$id - 1]);
                    $dmax[$id] = $this->getmax($newdata[$id - 1]);

                    if ($params["isEnglish"] == 1) {
                        $left = "left";
                        $right = "right";
                    } else {
                        $left = "左";
                        $right = "右";
                    }
                    $$layer->addDataSet($datat, $linecolor[$id - 1],  $this->getmytitle($left . "-" . $lendtitle[$id - 1] . $avg_print[$id - 1], $id - 1));


                    # Configure the bars within a group to touch each others (no gap)
                    // $layer1->setBarGap(0.2, TouchBar);
                    //print_r($yaxistitle);
                    //echo "<br>".($id-1)." 666<br>";
                    $ldw = $ldw == "" ? $params['dw'][$id - 1] : $ldw;
                } elseif ($val == 1) {//echo " 1 ";
                    //echo $layer."<br>";
                    $$layer = $c->addLineLayer2();
                    $$layer->setLineWidth(2);
                    //$datat=$datatemp[1];
                    $datat = $newdata[$id - 1];
                    $$layer->addDataSet($datat, $linecolor[$id - 1], $this->getmytitle($right . "-" . $lendtitle[$id - 1] . $avg_print[$id - 1], $id - 1));
                    //$dminline[$i]=min($datatemp[1]);
                    //$dmaxline[$i]=max($datatemp[1]);
                    $dminline[$id] = $this->getmin($newdata[$id - 1]);
                    $dmaxline[$id] = $this->getmax($newdata[$id - 1]);

                    $$layer->setUseYAxis2();

                    $rdw = $rdw == "" ? $params['dw'][$id - 1] : $rdw;
                } else {
                    //echo $val."<br>";
                }
            }
            if (count($labels) >= 50) {
                $ii = count($labels) / 7;
                $c->xAxis->addLabel(0, $labels[0][0]);
                for ($i = 0; $i < 7; $i++) {
                    $c->xAxis->addLabel((int)($i * $ii), $labels[(int)($i * $ii)]);
                }
                //print"<pre>";print_r($labels);
                $c->xAxis->addLabel(count($labels) - 1, $labels[count($labels) - 1]);
                // $c->xAxis->setLabels($labels);
                // $countlabel = count($labels);
                // $stepcount = round($countlabel / 6, 0);
                // $c->xAxis->setLabelStep($stepcount);
            } elseif (count($labels) <= 12 && $params['bar_datetype']['data'] <= 3 && $params['issametype'] == 1) {
                foreach ($labels as $id => $val) {
                    $c->xAxis->addLabel($id, $val);
                }
            } else {
                $c->xAxis->setLabels($labels);
                $countlabel = count($labels);
                $stepcount = round($countlabel / 6, 0);
                $c->xAxis->setLabelStep($stepcount);
            }
            if ($this->getmin($dmin) > 0) {
                $mina = 0.95 * $this->getmin($dmin);
            } else {
                $mina = 1.05 * $this->getmin($dmin);
            }
            if ($this->getmax($dmax) > 0) {
                $maxa = 1.05 * $this->getmax($dmax);
            } else {
                $maxa = 0.95 * $this->getmax($dmax);
            }

            $c->yAxis->setLinearScale($mina, $maxa);

            //$textBoxObj = $c->yAxis->setTitle(iconv("GB2312", "UTF-8", "左(" . $ldw . ")"), CREATE_IMG_TTF_PATH);
            $textBoxObj = $c->yAxis->setTitle( "左(" . $ldw . ")", CREATE_IMG_TTF_PATH);
            $textBoxObj->setAlignment(TopLeft2);
            if ($ma == 1) {
                if ($this->getmin($dminline) > 0) {
                    $minb = 0.95 * $this->getmin($dminline);
                } else {
                    $minb = 1.05 * $this->getmin($dminline);
                }
                if ($this->getmax($dmaxline) > 0) {
                    $maxb = 1.05 * $this->getmax($dmaxline);
                } else {
                    $maxb = 0.95 * $this->getmax($dmaxline);
                }

                $c->yAxis2->setLinearScale($minb, $maxb);
                //$textBoxObj2 = $c->yAxis2->setTitle(iconv("GB2312", "UTF-8", "右(" . $rdw . ")"), CREATE_IMG_TTF_PATH);
                $textBoxObj2 = $c->yAxis2->setTitle("右(" . $rdw . ")", CREATE_IMG_TTF_PATH);
                $textBoxObj2->setAlignment(TopRight2);
            }
            # Output the chart
            file_put_contents($params['url'], $c->makeChart2(PNG));
            //header("Content-type: image/png");
            //echo $c->makeChart2(PNG);

        }
    }

    public function barchart($params, $mes='')
    {

        $isEnglish = $params["isEnglish"];
        if ($isEnglish) {
            $mes = EN_DATASOURCEMES;
            $junzhi = "AVG";
        } else {
            $mes = CN_DATASOURCEMES;
            $junzhi = "均值";
        }
        require_once(CHARTDIRECTOR_PATH);
        # The data for the bar chart
        //==2012-10-22
        $n = count($params['data']);
        # The labels for the line chart
        //$labels=$params['xlabel'];
        $ytwoflag = $params['twoy'];
        $lendtitle = $params['lendtitle'];
        if ( $lendtitle ) $lendtitle = $this->gb2312ToUtf8Array($lendtitle);
        $yaxistitle = $params['yaxistitle'];
        //$labels=$params['xlabel'];
        $nnum = count($params['data'], 1) - $n;   //统计柱的总数，方便分类进行处理
        // Added for 柱状图 by zhangcun started  2016/08/26
        /*
		 if($nnum>=110)                          //对柱形图进行处理，如果数据超过50条图形会黑掉
		{
            $dataw=array();
			$datew=array();

			  if($nnum<400)  $type=20;                      //对柱形图进行处理，根据数据量来决定取的类型
			  elseif($nnum<3000) $type=65;
			  else $type=261;

			  for($k=0;$k<$n;$k++)             //k代表有几条柱形图的线
			{
			   $num=count($params['data'][$k]);    //num表示第k条线的元素个数
			   for($i=0;$i<$num;$i+=$type)
			   {
				  $mark=(int)($i/$type);
                  $dataw[$k][$mark]=0;
				  for($j=0;$j<$type;$j++)
				  {
					 if($i+$j>=$num) break;
					 $dataw[$k][$mark]+=$params['data'][$k][$i+$j];
				  }
				  $dataw[$k][$mark]=(int)($dataw[$k][$mark]/$j);
				  $datew[$mark]=$params['xlabel'][$i];
			   }
			}

		}
		else {
			$dataw=$params['data'];
			$datew=$params['xlabel'];
		}
*/
        $dataw = $params['data'];
        $datew = $params['xlabel'];                                                        // Added for 柱状图 by zhangcun ended  2016/08/26
        $labels = $datew;

        //$linecolor=array(0xfb0303,0xa260e6,0x575ae1,0x66dede,0x8B008B);
        //$linecolor=array(0x1007bd,0x00d5f5,0xffc000,0xa004c1,0x10c358);
        //$linecolor=array(0xff0000,0x1007bd,0xffc000,0xa004c1,0x10c358);
        if ($params['color'] == "彩色" || $params['color'] == "0") {
            $linecolor = array(0x4F81BD, 0xC0504D, 0x9BBB59, 0x8166A2);
        } elseif ($params['color'] == "蓝色" || $params['color'] == "1") {
            $linecolor = array(0x3C6494, 0x4978B1, 0x7E9BC8, 0xB6C3DC);
        } else {
            $linecolor = array(0x4F81BD, 0xC0504D, 0x9BBB59, 0x8166A2);
        }
        // 2012-10-22
        # Create a XYChart object of size 540 x 375 pixels
        $c = new XYChart(600, 350);
        $c->setDefaultFonts(CREATE_IMG_TTF_PATH);
        # Add a title to the chart using 18 pts Times Bold Italic font
        $c->addTitle( $params['charttitle'], CREATE_IMG_TTF_PATH, 15);

        if ($params['isjz'] == "1") {
            for ($i = 0; $i < count($params['data']); $i++) {
                $avg[$i] = round(array_sum($params['data'][$i]) / count($params['data'][$i]), 2);
                $avg_print[$i] = "  " . $junzhi . ":" . $avg[$i];
            }
        }
        //print"<pre>";print_r($params);
        //print"<pre>";print_r($dataw);
        //print"<pre>";print_r($datew);

        # Set the plotarea at (50, 55) and of 440 x 280 pixels in size. Use a vertical
        # gradient color from light blue (f9f9ff) to blue (6666ff) as background. Set border
        # and grid lines to white (ffffff).
// 			$c->setPlotArea(50, 80, 460, 205, $c->linearGradientColor(0, 55, 0, 335, 0xffffff,
// 				0xe9e9e9), -1, 0xe9e9e9, 0xffffff);
        if (count($lendtitle) == 4) {
            $plotarea = $c->setPlotArea(65, 105, 480, 200, 0xffffff, 0xffffff, 0xe9e9e9, 0xc0c0c0);
        } elseif (count($lendtitle) == 5) {
            $plotarea = $c->setPlotArea(65, 115, 480, 200, 0xffffff, 0xffffff, 0xe9e9e9, 0xc0c0c0);
        } else {
            $plotarea = $c->setPlotArea(65, 90, 480, 200, 0xffffff, 0xffffff, 0xe9e9e9, 0xc0c0c0);
        }

        if ($params['isbg'] == "1") {
        } else {
            $plotarea->setBackground2(dirname(__FILE__) . "/../../images/" . BG_IMG);
        }
        //$plotarea->setBackground2(dirname(__FILE__)."/../../images/".BG_IMG);


        //$legendObj = $c->addLegend(60, 20, false, CREATE_IMG_TTF_PATH, 10);
        # Add a legend box at (50, 28) using horizontal layout. Use 10pts Arial Bold as font,
        # with transparent background.
        if (count($lendtitle) == 4) {
            $jl = 318;
        } elseif (count($lendtitle) == 5) {
            $jl = 325;
        } else {
            $jl = 310;
        }
        $textBoxObj = $c->addText(535, $jl,  $mes, CREATE_IMG_TTF_PATH, 10, 0x000000);
        $textBoxObj->setAlignment(TopRight);
        $legendObj = $c->addLegend(60, 28, false, CREATE_IMG_TTF_PATH, 10);
        $legendObj->setBackground(Transparent);

        # Set the x axis labels
        //$c->xAxis->setLabels($labels);

        # Draw the ticks between label positions (instead of at label positions)
        $c->xAxis->setTickOffset(0.05);

        # Set axis label style to 8pts Arial Bold
        $c->xAxis->setLabelStyle(CREATE_IMG_TTF_PATH, 8, TextColor);
        $c->yAxis->setLabelStyle(CREATE_IMG_TTF_PATH, 8);
        //$c->yAxis2->setLabelStyle(CREATE_IMG_TTF_PATH, 2);
        # Set axis line width to 2 pixels
        $c->xAxis->setWidth(2);
        $c->yAxis->setWidth(2);


        # Add axis title
        // $c->yAxis->setTitle(iconv("GB2312", "UTF-8","产量"),CREATE_IMG_TTF_PATH,15);

        # Add a multi-bar layer with 3 data sets
        $layer = $c->addBarLayer2(Side);
        $layer->setBorderColor(Transparent);
        //print"<pre>";print_r($params);
        for ($i = 0; $i < $n; $i++) {
            $dmin[$i] = min($dataw[$i]);
            $dmax[$i] = max($dataw[$i]);
            $titleStr =  $this->getmytitle($lendtitle[$i] . $avg_print[$i], $i);
            $layer->addDataSet($dataw[$i], $linecolor[$i], $titleStr, CREATE_IMG_TTF_PATH);
        }

        $i = $this->getmin($dmin);
        $j = $this->getmax($dmax);
        if ($i < 0 || $i - round(($j - $i) / 4, 1) >= 0) {
            $ii = $i - round(($j - $i) / 4, 1);
        } else {
            $ii = 0;
        }

        if ($j >= 0 || $j + round(($i - $j) / 4, 1) <= 0) {
            $jj = $j + round(($j - $i) / 4, 1);
        } else {
            $jj = 0;
        }

        $c->yAxis->setLinearScale($ii, $jj);
        $textBoxObj = $c->yAxis->setTitle($params['dw'][0], CREATE_IMG_TTF_PATH);
        $textBoxObj->setAlignment(TopLeft2);

// 			$min = ceil((min($dmin)-0.10*min($dmin))/10)*10;
// 			$max = ceil((max($dmax)+0.10*max($dmax))/10)*10;
// 			//echo $min."------".$max;
// 			$c->yAxis->setLinearScale($min,$max);
        // $layer->addDataSet($data2, 0xff8800, "Server #3");
        if (count($labels) <= 12) {
            if ($params['bar_datetype']['data'] <= 3 && $params['issametype'] == 1) {
                foreach ($labels as $id => $val) {
                    $c->xAxis->addLabel($id, $val);
                }
            } elseif (count($labels) <= 8) {
                foreach ($labels as $id => $val) {
                    $c->xAxis->addLabel($id, $val);
                }
            } else {
                $c->xAxis->setLabels($labels);
                $countlabel = count($datew);
                $stepcount = (int)($countlabel / 6);
                $c->xAxis->setLabelStep($stepcount);
            }
        } else {
            $c->xAxis->setLabels($labels);
            $countlabel = count($datew);
            $stepcount = (int)($countlabel / 6);
            $c->xAxis->setLabelStep($stepcount);
        }
        # Set bar border to transparent. Use glass lighting effect with light direction from
        # left.
        //$layer->setBarShape(CircleShape);
        # Configure the bars within a group to touch each others (no gap)
        //$layer->setBarGap(0.2, TouchBar);
        file_put_contents($params['url'], $c->makeChart2(PNG));
        //header("Content-type: image/png");
        //print($c->makeChart2(PNG));
    }

    public function linebarchart($params, $mes="")
    {
        $isEnglish = $params["isEnglish"];
        if ($isEnglish) {
            $mes = EN_DATASOURCEMES;
            $junzhi = "AVG";
        } else {
            $mes = CN_DATASOURCEMES;
            $junzhi = "均值";
        }
        require_once(CHARTDIRECTOR_PATH);
        # The data for the bar chart
        //==2012-10-22

        $nline = count($params['data'][1]); //线
        $nbar = count($params['data'][0]);  //柱
        $datatemp[0] = $params['data'][1];  //线
        $datatemp[1] = $params['data'][0];  //柱
        $nnum = count($params['data'][0], 1) - $nbar; //柱的总数量

        //print"<pre>";print_r($params);exit;
        //add by zhangcun 2017/7/31 for 右轴选择 功能：柱图能够选择左右轴，$pos代表柱图的坐标轴选择
        $amout = $nline + $nbar;
        $pos = "left";
        $k = 0;
        for ($i = 0; $i < $amout; $i++) {
            $z = "zhou" . ($i + 1);
            if ($params['isbar'][$i] == 1) $k++;
            if ($params['isbar'][$i] == 1 && $params[$z] == 1) {
                $pos = "right";
                continue;
            }
        }
        if ($k == 0) $pos = "right";                //
        if ($nline == 0) $pos = "left";
        //统计柱的总数，方便分类进行处理     Added for 柱线图 by zhangcun started  2016/08/23

        /*		    if($nnum>=110)                          //对柱形图进行处理，如果数据超过50条图形会黑掉
		   {
            $dataw=array();
			$dataw2=array();
			$datew=array();

			  if($nnum<400)  $type=20;                      //对柱形图进行处理，根据数据量来决定取的类型
			  elseif($nnum<3000) $type=65;
			  else $type=261;

			  for($k=0;$k<$nbar;$k++)             //k代表有几条柱形图的线
			 {
			   $num=count($params['data'][0][$k]);    //num表示第k类柱的元素个数
			   for($i=0;$i<$num;$i+=$type)
			   {
				  $mark=(int)($i/$type);
                  $dataw[$k][$mark]=0;
				  $dataw2[$k][$mark]=0;
				  for($j=0;$j<$type;$j++)
				  {
					 if($i+$j>=$num) break;
					 $dataw[$k][$mark]+=$datatemp[1][$k][$i+$j];
				  }
				  $dataw[$k][$mark]=(int)($dataw[$k][$mark]/$j);
				  $datew[$mark]=$params['xlabel'][$i];
			   }
			 }

			for($k=0;$k<$nline;$k++)
			{
			   $num=count($params['data'][1][$k]);    //num表示第k条线的元素个数
			   for($i=0;$i<$num;$i+=$type)
			   {
				  $mark=(int)($i/$type);
				  $dataw2[$k][$mark]=0;
				  for($j=0;$j<$type;$j++)
				  {
					 if($i+$j>=$num) break;                        //当数据取完，就退出循环
					 $dataw2[$k][$mark]+=$datatemp[0][$k][$i+$j];
				  }
				  $dataw2[$k][$mark]=(int)($dataw2[$k][$mark]/$j);   //有可能中途结束，故用j表示实际数目
			   }
			}
			  $datatemp[1]=$dataw;
			  $datatemp[0]=$dataw2;
		}else{
			$datew=$params['xlabel'];
		}															// Added for 柱线图 by zhangcun ended  2016/08/23
*/
        $datew = $params['xlabel'];

        //print"<pre>";print_r($dataw);
        //print"<pre>";print_r($params);
        //print"<pre>";print_r($datew);

        if ($params['isjz'] == "1") {                                             //判断均值是否显示 add by zhangcun 2016/08
            $avg = "";
            $avg_print = array();
            foreach ($datatemp as $kk => $vv)
                foreach ($vv as $ii => $arr) {
                    $avg[$kk] = round(array_sum($datatemp[$kk][$ii]) / count($datatemp[$kk][$ii]), 2);
                    $avg_print[$kk][$ii] = "  " . $junzhi . ":" . $avg[$kk];
                }
        }
        //print"<pre>";print_r($params);print_r($avg);
        // print_r($params);
        # The labels for the line chart
        $ytwoflag = $params['twoy'];
        $lendtitle = $params['lendtitle'];
        if ( $lendtitle ) $lendtitle = $this->gb2312ToUtf8Array($lendtitle);
        $labels = $datew;

        //$linecolor=array(0xfb0303,0xa260e6,0x575ae1,0x66dede,0x8B008B);
        //$linecolor1=array(0x1007bd,0x00d5f5,0xffc000);
        /*	if($nbar==0){
				$linecolor1 = array(0xff0000,0x1007bd,0xffc000,0xa004c1,0x10c358);
			}else{
				$linecolor1=array(0x1007bd,0xffc000,0x10c358,0x66dede,0x10c358);
			}
		*/
        if ($params['color'] == "彩色" || $params['color'] == "0") {
            $linecolor1 = array(0xC0504D, 0x8166A2, 0x9BBB59, 0x4F81BD);
        } elseif ($params['color'] == "蓝色" || $params['color'] == "1") {
            $linecolor1 = array(0x3C6494, 0x4978B1, 0x7E9BC8, 0xB6C3DC);
        } else {
            $linecolor1 = array(0xC0504D, 0x8166A2, 0x9BBB59, 0x4F81BD);
        }
        //$linecolor1=array(0x66dede,0x8B008B,0x5aeb8e);
        // 2012-10-22
        # Create a XYChart object of size 540 x 375 pixels
        $c = new XYChart(600, 350);
        $c->setDefaultFonts(CREATE_IMG_TTF_PATH);
        # Add a title to the chart using 18 pts Times Bold Italic font
        $c->addTitle($params['charttitle'], CREATE_IMG_TTF_PATH, 15);
        // $liukai;
        # Set the plotarea at (50, 55) and of 440 x 280 pixels in size. Use a vertical
        # gradient color from light blue (f9f9ff) to blue (6666ff) as background. Set border
        # and grid lines to white (ffffff).

        if ($nline + $nbar == 4) {
            $plotarea = $c->setPlotArea(55, 105, 460, 200, 0xffffff, 0xffffff, 0xe9e9e9, 0xc0c0c0);
        } elseif ($nline + $nbar == 5) {
            $plotarea = $c->setPlotArea(55, 112, 460, 200, 0xffffff, 0xffffff, 0xe9e9e9, 0xc0c0c0);
        } else {
            $plotarea = $c->setPlotArea(55, 90, 460, 200, 0xffffff, 0xffffff, 0xe9e9e9, 0xc0c0c0);
        }
        //$plotarea = $c->setPlotArea(50, 90, 500, 200, 0xeeeeee, 0xffffff, 0xe9e9e9, 0xc0c0c0);
        if ($nline + $nbar == 4) {
            $jl = 318;
        } elseif ($nline + $nbar == 5) {
            $jl = 325;
        } else {
            $jl = 310;
        }
        if ($params['isbg'] == "1") {
        } else {
            $plotarea->setBackground2(dirname(__FILE__) . "/../../images/" . BG_IMG);
        }
        //$plotarea->setBackground2(dirname(__FILE__)."/../../images/".BG_IMG);
        $textBoxObj = $c->addText(515, $jl, $mes, CREATE_IMG_TTF_PATH, 10, 0x000000);
        $textBoxObj->setAlignment(TopRight);

        $legendObj = $c->addLegend(60, 28, false, CREATE_IMG_TTF_PATH, 10);
        # Add a legend box at (50, 28) using horizontal layout. Use 10pts Arial Bold as font,
        # with transparent background.
        $legendObj->setBackground(Transparent);
        # Set the x axis labels
        //$c->xAxis->setLabels($labels);
        # Draw the ticks between label positions (instead of at label positions)

        $c->xAxis->setTickOffset(0.05);
        //$c->yAxis->setTitle("blue");
        //$c->yAxis2->setTitle("red");

        # Set axis label style to 8pts Arial Bold
        $c->xAxis->setLabelStyle(CREATE_IMG_TTF_PATH, 8, TextColor);
        $c->yAxis->setLabelStyle(CREATE_IMG_TTF_PATH, 8);
        $c->yAxis2->setLabelStyle(CREATE_IMG_TTF_PATH, 8);
        $dminline = array();
        $dmaxline = array();
        for ($i = 0; $i < $nline; $i++) {
            $layer = "layer" . $i;
            $$layer = $c->addLineLayer2();
            $$layer->setLineWidth(2);
            $datat = $datatemp[0][$i];
            $$layer->addDataSet($datat, $linecolor1[$i], $this->getmytitle($lendtitle[1][$i] . $avg_print[0][$i], $i));
            $dminline[$i] = $this->getmin($datatemp[0][$i]);
            $dmaxline[$i] = $this->getmax($datatemp[0][$i]);

            if ($nbar == !0) {
                if ($this->getmin($dminline) > 0) {
                    $minb = 0.95 * $this->getmin($dminline);
                } else {
                    $minb = 1.05 * $this->getmin($dminline);
                }
                if ($this->getmax($dmaxline) > 0) {
                    $maxb = 1.05 * $this->getmax($dmaxline);
                } else {
                    $maxb = 0.95 * $this->getmax($dmaxline);
                }
                if ($pos == "right") {
                    $c->yAxis->setLinearScale($minb, $maxb);
                } elseif ($pos == "left") {
                    $$layer->setUseYAxis2();
                    $c->yAxis2->setLinearScale($minb, $maxb);
                }
            }
        }
        //$m=$i;
        //$m++;

//$textBoxObj = $c->yAxis2->setTitle("ceshi",CREATE_IMG_TTF_PATH);
//$textBoxObj->setAlignment(TopRight2);
        for ($i = 0, $j = 0, $unit = ""; $i < 4; $i++)                                                         //add for 柱线图 by zhangcun 2016/8/29
        {
            if ($params['isbar'][$i] == 1) {
                switch ($j) {
                    case 0:
                        if ($pos == "right") {
                            $textBoxObj = $c->yAxis2->setTitle("柱(" . $params['dw'][$i] . ")", CREATE_IMG_TTF_PATH);
                            $textBoxObj->setAlignment(TopRight2);
                        } elseif ($pos == "left") {
                            $textBoxObj = $c->yAxis->setTitle("柱(" . $params['dw'][$i] . ")", CREATE_IMG_TTF_PATH);
                            $textBoxObj->setAlignment(TopLeft2);
                        }
                        $j = 1;
                        break;
                    default :
                        break;
                }
            } else if ($params['isbar'][$i] == 0) {
                switch ($j) {
                    case 1:
                        if ($pos == "right") {
                            $textBoxObj = $c->yAxis->setTitle( "折(" . $params['dw'][$i] . ")", CREATE_IMG_TTF_PATH);
                            $textBoxObj->setAlignment(TopLeft2);
                        } elseif ($pos == "left") {
                            $textBoxObj = $c->yAxis2->setTitle("折(" . $params['dw'][$i] . ")", CREATE_IMG_TTF_PATH);
                            $textBoxObj->setAlignment(TopRight2);
                        }
                        $i = 10;
                        break;
                    default :
                        $unit = $params['dw'][$i];
                        break;
                }
            }

            if ($i == ($nline - 1) && $j == 0 && $nbar > 0) {
                if ($pos == "right") {
                    $textBoxObj = $c->yAxis->setTitle( "折(" . $unit . ")", CREATE_IMG_TTF_PATH);
                    $textBoxObj->setAlignment(TopLeft2);
                } elseif ($pos == "left") {
                    $textBoxObj = $c->yAxis2->setTitle( "折(" . $unit . ")", CREATE_IMG_TTF_PATH);
                    $textBoxObj->setAlignment(TopRight2);
                }
            } elseif ($i == ($nline - 1) && $j == 0 && $nbar == 0) {
                if ($pos == "right") {
                    $textBoxObj = $c->yAxis->setTitle( "折(" . $unit . ")", CREATE_IMG_TTF_PATH);
                    $textBoxObj->setAlignment(TopLeft2);
                } elseif ($pos == "left") {
                    $textBoxObj = $c->yAxis2->setTitle("折(" . $unit . ")", CREATE_IMG_TTF_PATH);
                    $textBoxObj->setAlignment(TopRight2);
                }
            }
        }
        //print"<pre>";print_r($params);
        //柱状图
        $layerbar = $c->addBarLayer2(Side);
        $layerbar->setBorderColor(Transparent);

        /*if($nline==0){
				$linecolor2 = array(0xff0000,0x1007bd,0xffc000,0xa004c1,0x10c358);
			}else{
				$linecolor2=array(0xff0000,0xa004c1,0x10c358,0xffc000,0x1007bd);
			}*/
        if ($params['color'] == "彩色" || $params['color'] == "0") {
            $linecolor2 = array(0x4F81BD, 0x9BBB59, 0x8166A2, 0xC0504D);
        } elseif ($params['color'] == "蓝色" || $params['color'] == "1") {
            $linecolor2 = array(0xB6C3DC, 0x7E9BC8, 0x4978B1, 0x3C6494);
        } else {
            $linecolor2 = array(0x4F81BD, 0x9BBB59, 0x8166A2, 0xC0504D);
        }
        //$linecolor2=array(0xfb0303,0xa260e6,0x575ae1);
        $dminbar = array();
        $dmaxbar = array();
        for ($i = 0; $i < $nbar; $i++) {

            $dminbar[$i] = $this->getmin($datatemp[1][$i]);
            $dmaxbar[$i] = $this->getmax($datatemp[1][$i]);
            //print_r($dminbar);
            //print_r($dmaxbar);
            $layerbar->addDataSet($datatemp[1][$i], $linecolor2[$i], $lendtitle[0][$i] . $avg_print[1][$i], CREATE_IMG_TTF_PATH);
            $layerbar->setBarGap(0.2, TouchBar);
            //$min = ceil((min($dminbar)-0.10*min($dminbar))/10)*10;
            //$max = ceil((max($dmaxbar)+0.10*max($dmaxbar))/10)*10;
            //$c->yAxis->setLinearScale($min,$max);
            if ($this->getmin($dminbar) > 0) {
                $mina = 0.95 * $this->getmin($dminbar);
            } else {
                $mina = 1.05 * $this->getmin($dminbar);
            }
            if ($this->getmax($dmaxbar) > 0) {
                $maxa = 1.05 * $this->getmax($dmaxbar);
            } else {
                $maxa = 0.95 * $this->getmax($dmaxbar);
            }
            if ($pos == "right") {
                $layerbar->setUseYAxis2();
                $c->yAxis2->setLinearScale($mina, $maxa);
            } elseif ($pos == "left") {
                $c->yAxis->setLinearScale($mina, $maxa);
            }
            if ($nline != 0) {
            }
            $m++;
        }
        //echo "位置[柱]：".$pos."<br>";
        //echo "线: ".$minb."-".$maxb."<br>";
        //echo "柱: ".$mina."-".$maxa."<br>";

        //$textBoxObj->setAlignment(TopLeft2);
        //print_r($datatemp[0][0]);
        //if($nline==0){
        //	$countlabel=count($datatemp[1][0]);
        //}else{
        //	$countlabel=count($datatemp[0][0]);
        //}

        //echo $countlabel;exit;
        //$stepcount=round($countlabel/6,0);
        //$c->xAxis->setLabelStep($stepcount);
        if (count($labels) <= 12) {
            if ($params['bar_datetype']['data'] <= 3 && $params['issametype'] == 1) {
                foreach ($labels as $id => $val) {
                    $c->xAxis->addLabel($id, $val);
                }
            } elseif (count($labels) <= 8) {
                foreach ($labels as $id => $val) {
                    $c->xAxis->addLabel($id, $val);
                }
            } else {
                $c->xAxis->setLabels($labels);
                $countlabel = count($dataw[0]);
                $stepcount = round($countlabel / 6, 0);
                $c->xAxis->setLabelStep($stepcount);
            }
        } else {
            $c->xAxis->setLabels($labels);
            if ($nline == 0) {
                $countlabel = count($datatemp[1][0]);
            } else {
                $countlabel = count($datatemp[0][0]);
            }
            $stepcount = round($countlabel / 6, 0);
            $c->xAxis->setLabelStep($stepcount);
        }
        file_put_contents($params['url'], $c->makeChart2(PNG));

    }

    public function piechart($params, $mes)
    {
        $isEnglish = $params["isEnglish"];
        if ($isEnglish) {
            $mes = EN_DATASOURCEMES;
            $junzhi = "AVG";
        } else {
            $mes = CN_DATASOURCEMES;
            $junzhi = "均值";
        }
        //print"<pre>";print_r($params);
        require_once(CHARTDIRECTOR_PATH);
        $lendtitle = $params['lendtitle'];
        if ( $lendtitle ) $lendtitle = $this->gb2312ToUtf8Array($lendtitle);
        //$params['xlabel']['10']='16/09';
        //$colors=array(0x3370CC,0xE66B1A,0x44BB5C,0x8DEEEE);
        $colors = array(0x3C57C4, 0xE6421A, 0x70CC33, 0x764DB3);
        //$colors=array(0xff332c ,0x1992fd ,0x2cc417,0xFEC34D,0xff9c01  );
        foreach ($lendtitle as $id => $t) {
            $ii = explode("-", $t);
            $mark[] = count($ii) - 1;
            $title[] = $ii[count($ii) - 1];
        }
        //数据转码处理，把gbk转换为utf-8格式，否则显示的是乱码（只限于每个扇区显示）
        foreach ($title as $id => $arr) {
            // $lendtitle[$id] = iconv("GB2312", "UTF-8", $arr);
            $lendtitle[$id] = $arr;
        }

        foreach ($params['data'] as $id => $arr)   //取前一个月的数据，如果没数据就显示无
        {
            $val[] = $arr[count($arr) - 1];
        }
        $val[0] = $val[0] - $val[1] - $val[2] - $val[3]; //第一个表示其他数据
        foreach ($lendtitle as $id => $tt) {
            if ($id != 0) {
                $labels[] = $tt . " " . $val[$id];
                $data[] = $val[$id];
            }
        }
        $newtitle = explode( "（", $lendtitle[0]);
        //print"<pre>";print_r($newtitle);
        $lendtitle = explode("(", $newtitle[0]);
        //print"<pre>";print_r($lendtitle);
        //$lendtitle="";
        //$lendtitle=$newtitle[0];
        $qita = "其他";
        if ($isEnglish) $qita = "Others";
        $labels[] =  $qita . " " . $val[0];
        $data[] = $val[0];
        //print"<pre>";print_r($labels);
        //   if($params['isbg']==1) {
        // $c->setBackground($c->linearGradientColor(0, 0, 0, 100, 0x99ccff, 0xffffff), 0x888888);
        //  }else{
        //   $c->setBackground($c->patternColor(dirname(__FILE__)."/../../images/".BG_IMG), 0x000000, 1);
        //   }
        // 单位和时间添加

        $unit = iconv("GB2312", "UTF-8", "(" . $params['dw'][0] . ")");
        $date = $params['xlabel'][count($params['xlabel']) - 1];
        $t = $params['date'];

        if ($this->compareAB($date, $params['xlabel'][count($params['xlabel']) - 2], $t) == false) {
            $data = "";
            $labels = "";
            $unit = "";
            $c = new PieChart(600, 350);
            $textBoxObj = $c->addTitle( "该日期无数据", CREATE_IMG_TTF_PATH, 15);
        } else {
            $date = explode("/", $date);
            foreach ($date as $i => $v) {
                if ($i == 0) $date[$i] += 2000;
                else $date[$i] += 0;
            }
            switch (count($date)) {
                case 1:
                    $riqi = $date[0] . "年";
                    break;
                case 2:
                    $riqi = $date[0] . "年" . $date[1] . "月";
                    break;
                case 3:
                    $riqi = $date[0] . "年" . $date[1] . "月" . $date[2] . "日";
                    break;
            }

            $c = new PieChart(600, 350);
            $riqi =  $riqi;
            $textBoxObj = $c->addTitle($riqi . $lendtitle[0] . $unit, CREATE_IMG_TTF_PATH, 15);
        }
        $c->setLabelStyle(CREATE_IMG_TTF_PATH, 8, 0x000000);
        $c->setRoundedFrame();
        //$c->setDropShadow();
        # Set the center of the pie at (150, 150) and the radius to 80 pixels
        $c->setPieSize(250, 170, 100);
        # Set the sector label position to be 20 pixels from the pie. Use a join line to
        # connect the labels to the sectors.
        $c->setLabelPos(20, LineColor);

        //$c->set3D(25);
        # Set the pie data and the pie labels
        //if(count($params['data'])==1) $labels=iconv("GB2312","UTF-8", "总计 ".array_sum($val));
        //print"<pre>";print_r($params);print_r($labels);
        $c->setData($data, $labels);
        # Set the sector colors
        $c->setColors(0xffffff);
        $c->setColors2(DataColor, $colors);
        # Use local gradient shading, with a 1 pixel semi-transparent black (bb000000) border
        $c->setSectorStyle(LocalGradientShading, 0xbb000000, 1);
        $textBoxObj = $c->addText(250, 325,  $mes, CREATE_IMG_TTF_PATH, 10, 0x000000);
        # Output the chart
        file_put_contents($params['url'], $c->makeChart2(PNG));
        //print"<pre>";print_r($lendtitle);
    }

    public function pie2chart($params, $mes)
    {
        $isEnglish = $params["isEnglish"];
        if ($isEnglish) {
            $mes = EN_DATASOURCEMES;
            $junzhi = "AVG";
        } else {
            $mes = CN_DATASOURCEMES;
            $junzhi = "均值";
        }
        require_once(CHARTDIRECTOR_PATH);
        $lendtitle = $params['lendtitle'];
        if ( $lendtitle ) $lendtitle=$params['lendtitle'] = $this->gb2312ToUtf8Array($lendtitle);
        $data = array(2, 15, 63, 20);
        //$data = array(21, 18, 15, 12, 8, 24);
        //$labels = array("Labor", "Licenses", "Taxes", "Legal", "Facilities", "Production");
        foreach ($params['lendtitle'] as $id => $title) {
            $labels[] =  $title;
        }
        //print"<pre>";print_r($labels);
        $colors = array(0xB8860B, 0x76EE00, 0x836FFF, 0x54FF9F, 0xDAA520, 0x009900);
        $c = new PieChart(550, 320);

        if ($params['isbg'] == 0) {
            $c->setBackground($c->linearGradientColor(0, 0, 0, 100, 0x99ccff, 0xffffff), 0x888888);
        } else {
            $c->setBackground($c->patternColor(dirname(__FILE__) . "/../../images/" . BG_IMG), 0x000000, 1);
        }
        $c->setRoundedFrame();
        $c->setDropShadow();
        //$c->setLabelStyle(CREATE_IMG_TTF_PATH,7,0x000000);
        //$c->setLabelStyle("宋体",9,0x20FF0000);
        $textBoxObj = $c->addTitle( $params['charttitle'], CREATE_IMG_TTF_PATH, 15);
        $textBoxObj->setMargin2(0, 0, 16, 0);

        $c->setPieSize(150, 150, 100);
        $c->set3D(25);
        $c->setData($data, $labels);
        $c->setColors2(DataColor, $colors);
        $c->setSectorStyle(LocalGradientShading, 0xbb000000, 1);
        //$c->setSectorStyle(LocalGradientShading);
        //$c->setExplode(2, 40);

        $c->setLabelFormat("{={sector}+1}");
        $textBoxObj = $c->setLabelStyle(CREATE_IMG_TTF_PATH, 10);
        $textBoxObj->setBackground(Transparent, 0x444444);

        $b = $c->addLegend(330, 175, true, CREATE_IMG_TTF_PATH, 10);
        $b->setAlignment(Left);

        $b->setBackground(Transparent, 0x444444);
        $b->setRoundedCorners();

        $b->setMargin(16);
        $b->setKeySpacing(0, 5);

        $b->setKeyBorder(SameAsMainColor);

        $b->setText(
            "<*block,valign=top*>{={sector}+1}.<*advanceTo=22*><*block,width=120*>{label}<*/*>" .
            "<*block,width=40,halign=right*>{percent}<*/*>%");


        //$textBoxObj = $c->addText(515, $jl, iconv("GB2312", "UTF-8",$mes),        CREATE_IMG_TTF_PATH, 10, 0x000000);
        //$textBoxObj->setAlignment(TopRight);

        file_put_contents($params['url'], $c->makeChart2(PNG));
    }

    public function stackedbarchart($params, $mes="") //Created by zhangcun for 堆积柱图  2016/09/07
    {
        $isEnglish = $params["isEnglish"];
        if ($isEnglish) {
            $mes = EN_DATASOURCEMES;
            $junzhi = "AVG";
        } else {
            $mes = CN_DATASOURCEMES;
            $junzhi = "均值";
        }
        require_once(CHARTDIRECTOR_PATH);
        # The data for the bar chart
        $n = count($params['data']);
        # The labels for the line chart
        $ytwoflag = $params['twoy'];
        $lendtitle = $params['lendtitle'];
        if ( $lendtitle ) $lendtitle = $this->gb2312ToUtf8Array($lendtitle);
        $yaxistitle = $params['yaxistitle'];
        $nnum = count($params['data'], 1) - $n;      //nnum表示柱的总数

        /*	 if($nnum>=110*$n)                          //对柱形图进行处理，如果柱的个数超过110条图形会黑掉
		{
            $dataw=array();
			$datew=array();

			  if($nnum<400*$n)  $type=20;                      //对柱形图进行处理，根据数据量来决定取的类型
			  elseif($nnum<3000*$n) $type=53;
			  else $type=261;

			  for($k=0;$k<$n;$k++)             //k代表有几条柱形图的线
			{
			   $num=count($params['data'][$k]);    //num表示第k条线的元素个数
			   for($i=0;$i<$num;$i+=$type)
			   {
				  $mark=(int)($i/$type);
                  $dataw[$k][$mark]=0;
				  for($j=0;$j<$type;$j++)
				  {
					 if($i+$j>=$num) break;
					 $dataw[$k][$mark]+=$params['data'][$k][$i+$j];
				  }
				  $dataw[$k][$mark]=(int)($dataw[$k][$mark]/$j);
				  $datew[$mark]=$params['xlabel'][$i];
			   }
			}

		}
		else {
			$dataw=$params['data'];
			$datew=$params['xlabel'];
		}
       */
        $dataw = $params['data'];
        $datew = $params['xlabel'];
        $labels = $datew;


        if ($params['color'] == "彩色" || $params['color'] == "0") {
            $linecolor = array(0x4F81BD, 0xC0504D, 0x9BBB59, 0x8166A2);
        } elseif ($params['color'] == "蓝色" || $params['color'] == "1") {
            $linecolor = array(0x3C6494, 0x4978B1, 0x7E9BC8, 0xB6C3DC);
        } else {
            $linecolor = array(0x4F81BD, 0xC0504D, 0x9BBB59, 0x8166A2);
        }
        # Create a XYChart object of size 540 x 375 pixels
        $c = new XYChart(600, 350);
        $c->setDefaultFonts(CREATE_IMG_TTF_PATH);
        # Add a title to the chart using 18 pts Times Bold Italic font
        $c->addTitle( $params['charttitle'], CREATE_IMG_TTF_PATH, 15);

        if ($params['isjz'] == "1") {
            for ($i = 0; $i < count($params['data']); $i++) {
                $avg[$i] = round(array_sum($params['data'][$i]) / count($params['data'][$i]), 2);
                $avg_print[$i] = "  " . $junzhi . ":" . $avg[$i];
            }
        }
        //print"<pre>";print_r($params);
        //print"<pre>";print_r($dataw);
        //print"<pre>";print_r($datew);

        # Set the plotarea at (50, 55) and of 440 x 280 pixels in size. Use a vertical
        # gradient color from light blue (f9f9ff) to blue (6666ff) as background. Set border
        # and grid lines to white (ffffff).
        if (count($lendtitle) == 4) {
            $plotarea = $c->setPlotArea(55, 105, 480, 200, 0xffffff, 0xffffff, 0xe9e9e9, 0xc0c0c0);
        } elseif (count($lendtitle) == 5) {
            $plotarea = $c->setPlotArea(55, 115, 480, 200, 0xffffff, 0xffffff, 0xe9e9e9, 0xc0c0c0);
        } else {
            $plotarea = $c->setPlotArea(55, 90, 480, 200, 0xffffff, 0xffffff, 0xe9e9e9, 0xc0c0c0);
        }

        if ($params['isbg'] == "1") {
        } else {
            $plotarea->setBackground2(dirname(__FILE__) . "/../../images/" . BG_IMG);
        }

        # Add a legend box at (50, 28) using horizontal layout. Use 10pts Arial Bold as font,
        # with transparent background.
        if (count($lendtitle) == 4) {
            $jl = 318;
        } elseif (count($lendtitle) == 5) {
            $jl = 325;
        } else {
            $jl = 310;
        }
        $textBoxObj = $c->addText(535, $jl,  $mes, CREATE_IMG_TTF_PATH, 10, 0x000000);
        $textBoxObj->setAlignment(TopRight);
        $legendObj = $c->addLegend(60, 28, false, CREATE_IMG_TTF_PATH, 10);
        $legendObj->setBackground(Transparent);

        # Set the x axis labels
        //$c->xAxis->setLabels($labels);

        # Draw the ticks between label positions (instead of at label positions)
        $c->xAxis->setTickOffset(0.05);

        # Set axis label style to 8pts Arial Bold
        $c->xAxis->setLabelStyle(CREATE_IMG_TTF_PATH, 8, TextColor);
        $c->yAxis->setLabelStyle(CREATE_IMG_TTF_PATH, 8);
        # Set axis line width to 2 pixels
        $c->xAxis->setWidth(2);
        $c->yAxis->setWidth(2);

        # Add a multi-bar layer with data sets
        $layer = $c->addBarLayer2(Stack);
        //print_r($params);
        for ($i = 0; $i < $n; $i++) {
            //$dmin[$i]=min($dataw[$i]);
            //$dmax[$i]=max($dataw[$i]);

            $layer->addDataSet($dataw[$i], $linecolor[$i],  $this->getmytitle($lendtitle[$i] . $avg_print[$i], $i), CREATE_IMG_TTF_PATH);
        }
        $layer->setBorderColor(Transparent);
        $textBoxObj = $c->yAxis->setTitle( $params['dw'][0], CREATE_IMG_TTF_PATH);
        $textBoxObj->setAlignment(TopLeft2);

        //$countlabel=count($dataw[0]);
        //$stepcount=round($countlabel/6,0);
        //$c->xAxis->setLabelStep($stepcount);
        if (count($labels) <= 12) {
            if ($params['bar_datetype']['data'] <= 3 && $params['issametype'] == 1) {
                foreach ($labels as $id => $val) {
                    $c->xAxis->addLabel($id, $val);
                }
            } elseif (count($labels) <= 8) {
                foreach ($labels as $id => $val) {
                    $c->xAxis->addLabel($id, $val);
                }
            } else {
                $c->xAxis->setLabels($labels);
                $countlabel = count($dataw[0]);
                $stepcount = round($countlabel / 6, 0);
                $c->xAxis->setLabelStep($stepcount);
            }
        } else {
            $c->xAxis->setLabels($labels);
            $countlabel = count($dataw[0]);
            $stepcount = round($countlabel / 6, 0);
            $c->xAxis->setLabelStep($stepcount);
        }

        # Set bar border to transparent. Use glass lighting effect with light direction from
        # left.
        //$layer->setBarShape(CircleShape);
        # Configure the bars within a group to touch each others (no gap)
        //$layer->setBarGap(0.2, TouchBar);
        file_put_contents($params['url'], $c->makeChart2(PNG));
    }

    //Created by zhangcun for 堆积面积图  2016/09/07
    public function stackedareachart($params, $mes)
    {
        $isEnglish = $params["isEnglish"];
        if ($isEnglish) {
            $mes = EN_DATASOURCEMES;
            $junzhi = "AVG";
        } else {
            $mes = CN_DATASOURCEMES;
            $junzhi = "均值";
        }
        require_once(CHARTDIRECTOR_PATH);
        //print"<pre>"; print_r($params);exit;
        //echo 'dddd';
        # The data for the line chart
        $n = count($params['data']);
        $lendtitle = $params['lendtitle'];
        if ( $lendtitle ) $lendtitle = $this->gb2312ToUtf8Array($lendtitle);
        $labels = $params['xlabel'];

        if ($params['isjz'] == "1")  //判断均值是否显示 add by zhangcun 2016/8/11
        {
            $avg = "";
            $avg_print = array();
            for ($i = 0; $i < $n; $i++) {
                $avg[$i] = round(array_sum($params['data'][$i]) / count($params['data'][$i]), 2);
                $avg_print[$i] = "  " . $junzhi . ":" . $avg[$i];
            }
        }
        //print"<pre>"; print_r($avg_print);
        //$linecolor=array(0xff0000,0x1007bd,0xffc000,0xa004c1,0x10c358);
        if ($params['color'] == "彩色" || $params['color'] == "0") {
            $linecolor = array(0x4F81BD, 0xC0504D, 0x9BBB59, 0x8166A2);
        } elseif ($params['color'] == "蓝色" || $params['color'] == "1") {
            $linecolor = array(0x3C6494, 0x4978B1, 0x7E9BC8, 0xB6C3DC);
        } else {
            $linecolor = array(0x4F81BD, 0xC0504D, 0x9BBB59, 0x8166A2);
        }
        $c = new XYChart(600, 350);
        //$c->setDefaultFonts(CREATE_IMG_TTF_PATH);
        # Add a title to the chart using 15 pts Times Bold Italic font
        $c->addTitle($params['charttitle'], CREATE_IMG_TTF_PATH, 15);
        if (count($lendtitle) == 4) {
            $plotarea = $c->setPlotArea(55, 105, 480, 200, 0xffffff, 0xffffff, 0xe9e9e9, 0xc0c0c0);
        } elseif (count($lendtitle) == 5) {
            $plotarea = $c->setPlotArea(55, 115, 480, 200, 0xffffff, 0xffffff, 0xe9e9e9, 0xc0c0c0);
        } else {
            $plotarea = $c->setPlotArea(55, 90, 480, 200, 0xffffff, 0xffffff, 0xe9e9e9, 0xc0c0c0);
        }
        //$plotarea = $c->setPlotArea(50, 90, 500, 200, 0xeeeeee, 0xffffff, 0xe9e9e9, 0xc0c0c0);
        if (count($lendtitle) == 4) {
            $jl = 322;
        } else {
            $jl = 310;
        }

        if ($params['isbg'] == "1") {
        } else {
            $plotarea->setBackground2(dirname(__FILE__) . "/../../images/" . BG_IMG);
        }
        $textBoxObj = $c->addText(535, $jl,  $mes, CREATE_IMG_TTF_PATH, 10, 0x000000);
        $textBoxObj->setAlignment(TopRight);


        $legendObj = $c->addLegend(60, 28, false, CREATE_IMG_TTF_PATH, 10);
        $legendObj->setBackground(Transparent);
        # Set the x axis labels
        //$c->xAxis->setLabels($labels);
        # Add a Area layer to the chart
        $i = 0;
        //exit;
        //$dmin=array();
        //$dmax=array();
        $datat = array();
        $layer = $c->addAreaLayer2(Stack);
        for ($i = 0; $i < $n; $i++) {
            $datat[$i] = $params['data'][$i];
            $layer->addDataSet($datat[$i], $linecolor[$i],  $this->getmytitle($lendtitle[$i] . $avg_print[$i], $i));
        }

        //$countlabel=count($datat[0]);
        //$stepcount=round($countlabel/7,0);
        //$c->xAxis->setLabelStep($stepcount);
        if (count($labels) >= 50) {
            $ii = count($labels) / 7;
            $c->xAxis->addLabel(0, $labels[0]);
            for ($i = 0; $i < 7; $i++) {
                $c->xAxis->addLabel(($i * $ii), $labels[($i * $ii)]);
            }

            $c->xAxis->addLabel(count($labels) - 1, $labels[count($labels) - 1]);
        } elseif (count($labels) <= 12 && $params['bar_datetype']['data'] <= 3 && $params['issametype'] == 1) {
            foreach ($labels as $id => $val) {
                $c->xAxis->addLabel($id, $val);
            }
        } else {
            $c->xAxis->setLabels($labels);
            $countlabel = count($labels);
            $stepcount = round($countlabel / 6, 0);
            $c->xAxis->setLabelStep($stepcount);
        }

        $textBoxObj = $c->yAxis->setTitle($params['dw'][0], CREATE_IMG_TTF_PATH);
        $textBoxObj->setAlignment(TopLeft2);

        # Output the chart
        file_put_contents($params['url'], $c->makeChart2(PNG));
    }

    public function createchart($params, $type = "line")
    {
        $mc_type = $params["mc_type"];

        if ($mc_type == 1) {
            define("BG_IMG", "150.jpg");
            define("RES_NAME", "南钢产销大数据平台");
        } else {
            define("BG_IMG", "000.gif");
            define("RES_NAME", "钢之家数据中心 www.steelhome.cn/data" );
        }

        define("CN_DATASOURCEMES", "数据来源：" . RES_NAME);
        define("EN_DATASOURCEMES", "Source: SteelHome Database ");
        switch ($type) {
            case 'line':
                $this->linechart($params);
                break;
            case 'bar':
                $this->barchart($params);
                break;
            case 'doubleline':
                $this->doublelinechart($params);
                break;
            case 'linebar':
                $data = $this->filterlinebardata($params['data'], $params['ftp']);
                $datatitle = $this->filterlinebarlendtitle($params['lendtitle'], $params['ftp']);
                $params['data'] = $data;
                $params['lendtitle'] = $datatitle;
                //print_r( $params['lendtitle'])."aaaaaaaa";
                $this->linebarchart($params);
                break;
            case 'stackedbar':
                $this->stackedbarchart($params);
                break;
            case 'stackedarea':
                $this->stackedareachart($params);
                break;
            case 'pie':
                $this->piechart($params);
                break;
            default:
                $this->linechart($params);
                break;
        }
    }

    public function getwei($str) //add by zhangcun 2017/7/19 for 获取小数有效位数
    {
        $wei = 0;
        if ($str - 1.0 > 0) return $wei;
        for ($i = 0; $i < 10; $i++) {
            $str *= 10.0;
            $wei++;
            if ($str - 1.0 > 0) return $wei;
        }
    }

    public function filterlinebardata($params, $ftp)
    {
        //print_r($ftp);
        //print_r($params);
        foreach ($params as $k => $v) {
            if ($ftp[$k] == 1) {
                $data[0][] = $v;//柱状图
            } else {
                $data[1][] = $v;//折线图
            }
        }
        //$data[0]=array($params[0]);
        //$data[1]=array($params[1]);
        return $data;
    }

    public function filterlinebarlendtitle($params, $ftp)
    {
        foreach ($params as $k => $v) {
            if ($ftp[$k] == 1) {
                $data[0][] = $v;//柱状图
            } else {
                $data[1][] = $v;//折线图
            }
        }
        //$data[0]=array($params[0]);
        //$data[1]=array($params[1]);
        return $data;
    }

    public function compareAB($a, $b, $t) //判断时间a,b是否有一个是t的前一年、前一月或者前一天
    {
        //$a="16/01";
        //$b='16/02';
        //$t='16/03';

        $i = explode("/", $a);
        $j = explode("/", $b);
        $k = explode("-", $t);
        if ($i[0] < 100) $i[0] += 2000;
        if ($j[0] < 100) $j[0] += 2000;
        if ($k[0] < 100) $k[0] += 2000;
        $a = implode("-", $i);
        $b = implode("-", $j);
        //$t=implode("-",$k);

        //print"<pre>";print_r($a);echo "<br>";print_r($b);echo "<br>";print_r($t);echo "<br>";
        switch (count($i)) {
            case "1":
                //$t=date("Y",strtotime($a)+3600*24*365);
                //$b=date("Y",strtotime($b)+3600*24*365);
                $t = date("Y", strtotime($k - 1));
                break;
            case "2":
                //$a=date("Y-m",strtotime($a));
                //$b=date("Y-m",strtotime($b));
                if ($k[1] != 1) {
                    $t = date("Y-m", strtotime($k[0] . "-" . ($k[1] - 1)));
                } else {
                    $t = date("Y-m", strtotime(($k[0] - 1) . "-12"));
                }
                break;
            case "3":
                //$a=date("Y-m-d",strtotime($a)+3600*24);
                //$b=date("Y-m-d",strtotime($b)+3600*24);
                $t = date("Y-m-d", strtotime($t) - 3600 * 24);
                break;
        }

        //print"<pre>";print_r($a);echo "<br>";print_r($b);echo "<br>";print_r($t);echo "<br>";
        if ($a == $t || $b == $t) return true;
        //else echo "Fail!";
        return false;
    }

    private function getmin($arr)
    {
        $returnInt = 0;
        if (!is_array($arr[0])) {
            $min = "aaa";
            foreach ($arr as $id => $val) {
                if ($min == "aaa") $min = $val;
                elseif ($min - $val > 0) $min = $val;
            }
            if ($min != "aaa") {
                $returnInt = $min;
            }
        }
        return  $returnInt;
    }

    private function getmax($arr)
    {
        $returnInt = 0;
        if (!is_array($arr[0])) {
            $max = "bbb";
            foreach ($arr as $id => $val) {
                if ($max == "bbb") $max = $val;
                elseif ($max - $val < 0) $max = $val;
            }
            if ($max != "bbb") {
                $returnInt = $max;
            }
        }
        return  $returnInt;
    }

    private function getmytitle($str, $ii, $len = 70)
    {
        $tmpstr = "";
        $mark = 0;
        for ($i = 0; $i < strlen($str); $i++) {
            if (ord(substr($str, $i, 1)) > 0xa0) {
                if ($ii == 0 && $i > $len && $mark == 0) {
                    $tmpstr .= "\n";
                    $mark = 1;
                }
                $tmpstr .= substr($str, $i, 2);
                $i++;
            } else
                $tmpstr .= substr($str, $i, 1);
        }
        return $tmpstr;
    }

    public function gb2312ToUtf8Array( &$changeArray ){
        foreach ( $changeArray as $key => &$value ) {
            if(is_array($value))
            {
                $this->gb2312ToUtf8Array($value);
            }
            else
            {
                $value = $this->utf8ToGb2312($value);
            }
            
        }
        return $changeArray;
    }

    public function utf8ToGb2312($str)
    {
//        if( mb_detect_encoding($str,"UTF-8, ISO-8859-1, GBK")=="UTF-8" ) {//判断是否不是UTF-8编码，如果不是UTF-8编码，则转换为UTF-8编码
//            return iconv("utf-8", "gb2312", $str);
//        } else {
//            return $str;
//        }
        // if( mb_detect_encoding($str,"UTF-8, ISO-8859-1, GBK")!="UTF-8" ) {//判断是否不是UTF-8编码，如果不是UTF-8编码，则转换为UTF-8编码
        //     return iconv("gb2312", "utf-8", $str);
        // } else {
            return $str;
        // }
    }


}