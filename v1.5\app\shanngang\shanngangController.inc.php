<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );

class shanngangController extends AbstractController{
  
    public function __construct(){
        parent::__construct();
        $this->_action->setDao( new shanngangDao("91R") );  
    }

    public function _dopre()
    {
        $this->_action->checkSession();
    }

    public function v_yulong_price_diff(){
    	if(isset($this->_request['submit']) && $this->_request['submit']=="µ¼³ö"){
    		$this->_request['isexport'] = 1;
    		$this->setTemplateName("yulong_price_export.html");
    	}
        $this->_action->yulong_price_diff( $this->_request );
    }
}
?>