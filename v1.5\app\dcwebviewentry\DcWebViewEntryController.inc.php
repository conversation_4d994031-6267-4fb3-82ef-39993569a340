<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );
header('Content-Type:text/html;charset=utf8');
class DcWebViewEntryController extends AbstractController{
  
	public function __construct(){
		parent::__construct();
		$this->_action->setdao(new DcWebViewEntryDao("MAIN"));
	}

	public function _dopre(){
		//echo "<pre/>";print_r($_SESSION);exit;
		//$this->_action->checkSession();
	}

	public function v_index() {
		$this->_action->index($this->_request);
	}
	
	public function do_ThisDateDb() {
		$this->_action->DoThisDateDb($this->_request);
	}

	public function do_ThisDateDbList() {
		$this->_action->DoThisDateDbList($this->_request);
	}

	public function do_Forecast_current() {
		$this->_action->doForecast_current($this->_request);
	}

	public function do_Forecast_previous() {
		$this->_action->doForecast_previous($this->_request);
	}

	public function do_Qhtb_calculate() {
		$this->_action->doQhtb_calculate($this->_request);
	}

	public function do_Qhtb_previous() {
		$this->_action->doQhtb_previous($this->_request);
	}

	public function do_RdjHuiZong() {
		$this->_action->RdjHuiZong($this->_request);
	}

	public function do_DayReport() {
		$this->_action->DayReport($this->_request);
	}

	public function do_settscbindex() {
		$this->_action->settscbindex($this->_request);
	}

	public function do_setcbindex() {
		$this->_action->setcbindex($this->_request);
	}

	public function do_channenghz() {
		$this->_action->channenghz($this->_request);
	}

	public function do_zhbb() {
		$this->_action->dcwebviewzhbb($this->_request);
	}

	public function do_zhbblist() {
		$this->_action->zhbblist($this->_request);
	}
	
	public function do_dbmarketprice() {
		$this->_action->dbmarketprice($this->_request);
	}
	
	public function do_sggcprice() {
		$this->_action->sggcprice($this->_request);
    }
	
	public function do_sgxpcdjkb() {
		$this->_action->sgxpcdjkb($this->_request);
    }
	
	public function do_sgylprice() {
		$this->_action->sgylprice($this->_request);
	}
	
	public function do_sgpqtb() {
		$this->_action->sgpqtb($this->_request);
	}
	
	public function do_sgdataimport() {
		$this->_action->sgdataimport($this->_request);
	}
	
	public function do_sgcwrb() {
		$this->_action->sgcwrb($this->_request);
    }
	
	public function do_sgpqtb_zdj() {
		$this->_action->sgpqtb_zdj($this->_request);
    }

	public function do_sgtscbset() {
		$this->_action->sgtscbset($this->_request);
    }
	
	public function do_sggpcbset() {
		$this->_action->sggpcbset($this->_request);
    }
	
	public function do_sggccbset() {
		$this->_action->sggccbset($this->_request);
	}

	public function do_sglhgcbset() {
		$this->_action->sglhgcbset($this->_request);
	}
	
	public function do_sgqhtb() {
		$this->_action->sgqhtb($this->_request);
	}
	public function do_cgzxrb() {//中信特钢日报
		$this->_action->cgzxrb($this->_request);
	}
	public function do_liuzhousteeldata() {//柳钢市场价格信息收集
		$this->_action->liuzhousteeldata($this->_request);
	}

	public function do_sgbbdr() {
		$this->_action->sgbbdr($this->_request);
	}

	public function do_sgqyjwsp() {
		$this->_action->sgqyjwsp($this->_request);
	}
	public function do_sgscjcprice() {
		$this->_action->sgscjcprice($this->_request);
	}
	public function do_sglrtb() {
		$this->_action->sglrtb($this->_request);
	}
	public function do_sglrjs() {
		$this->_action->sglrjs($this->_request);
	}
	public function do_sgjckp() {
		$this->_action->sgjckp($this->_request);
	}

	public function do_sgxymx() {
		$this->_action->sgxymx($this->_request);
	}

	public function do_sgwlpj() {
		$this->_action->sgwlpj($this->_request);
	}

	public function do_sgweeklyreport() {
		$this->_action->sgweeklyreport($this->_request);
	}

	public function do_articles() {
		$this->_action->articles($this->_request);
	}
	public function do_sgredirect() {
        $this->_action->sgredirect($this->_request);
    }
	  // 新钢所有网页链接由此进入
    public function do_SteelRollingProcess() {
        $this->_action->SteelRollingProcess($this->_request);
    }

    // 新钢所有网页链接由此进入
    public function do_xgredirect() {
        $this->_action->xgredirect($this->_request);
    }

    // 新钢shizg
    public function do_xgredirect_dj() {
        $this->_action->xgredirect_dj($this->_request);
	}
	
	//新钢权限
	public function do_xgquanx() {
        $this->_action->xgquanx($this->_request);
    }

    //新钢研究报告资讯
	public function do_xgyjbg() {
        $this->_action->xgyjbg($this->_request);
    }
    public function do_xgyjbg_show() {
        $this->_action->xgyjbg_show($this->_request);
    }
    public function do_yulong_price_diff() {
        $this->_action->sgscjcprice($this->_request);
    }
	//钢之家信息看板
	public function do_steelhome_xxkb() {
        $this->_action->steelhome_xxkb($this->_request);
    }
	//陕钢研究报告
	public function do_sgyjbg() {
		$this->_action->sgyjbg($this->_request);
	}
	public function do_sgyjbg_show() {
		$this->_action->sgyjbg_show($this->_request);
	}
	//钢之家研究报告
	public function do_styjbg() {
		$this->_action->styjbg($this->_request);
	}
	public function do_styjbg_show() {
		$this->_action->styjbg_show($this->_request);
	}


	public function do_sgcssz() {
        $this->_action->sgcssz($this->_request);
    }

	public function do_logshow() {
        $this->_action->logshow($this->_request);
    }

	public function do_steelhome_khgl() {
        $this->_action->steelhome_khgl($this->_request);
    }

	public function do_dcxxh() {
        $this->_action->dcxxh($this->_request);
    }
	
	public function do_yc_jump() {
        $this->_action->yc_jump($this->_request);
    }

	public function do_jgyc_yj() {
        $this->_action->jgyc_yj($this->_request);
    }

	public function do_jgyc_jc() {
        $this->_action->jgyc_jc($this->_request);
    }
	public function do_sd_steel_yc_jump() {
        $this->_action->sd_steel_yc_jump($this->_request);
    }
	public function do_sd_steel_dingjia_jump() {
        $this->_action->sd_steel_dingjia_jump($this->_request);
    }
	public function do_sd_steel_curr_yc() {
        $this->_action->sd_steel_curr_yc($this->_request);
    }
    public function do_sd_steel_datacenter() {
        $this->_action->sd_steel_datacenter($this->_request);
    }
	public function do_sd_steel_hgfx() {
        $this->_action->sd_steel_hgfx($this->_request);
    }
	public function do_sd_steel_zhinengbaobiao_jump() {
        $this->_action->sd_steel_zhinengbaobiao_jump($this->_request);
    }
}


?>