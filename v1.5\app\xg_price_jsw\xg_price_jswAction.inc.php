<?php 
include_once( FRAME_LIB_DIR ."/action/AbstractAction.inc.php" );

$GLOBALS["upload_title"] = array(
    "1"=>"中厚板普锰板包到结算价格表",
    "2"=>"高线、盘螺、螺纹钢包到结算价格表",
    "3"=>"中厚板销售价格表",
    "4"=>"中厚板普锰板销售包到价格表",
    "5"=>"高线、拉丝线、盘螺、螺纹钢包到价格表",
    "6"=>"品种线材接单执行价格表",
    "7"=>"合金钢销售价格表",
    "8"=>"中厚板销售价格",
    "9"=>"钢坯产品价格表",
    "10"=>"线棒材价格表",
);

$GLOBALS["dta_type_arr"]=array(
    "1"=>"XG-ZHBPMBBDJSJGB",
    "2"=>"XG_GXPLLWGBDJSJGB",
    "3"=>"XG_ZHBXSJGB",
    "4"=>"XG_ZHBPMBXSBDJGB",
    "5"=>"XG_GXLSXPLLWGBDJGB",
    "6"=>"XG_PZXCJDZXJGB",
    "7"=>"XG_HJGXSJGB",
    "8"=>"XG_ZHBXSJGB",
    "9"=>"XG_GPCPJGB",
    "10"=>"XG_XBCJGB",
);



$GLOBALS["sg_steel_upload_title"] = array(
    "1"=>"螺纹钢",
    "2"=>"H型钢",
    "3"=>"特钢",
    "4"=>"热轧卷",
    "5"=>"冷轧卷",
);


$GLOBALS["sg_steel_bigtype"] = array(
    "1"=>"我的钢铁数据",
    "2"=>"市场成交价",
    "3"=>"手持订单进度",
);

class  xg_price_jswAction extends AbstractAction
{
    public function __construct()
    {
        parent::__construct();
    } 
    
    
 	public function base_data_manage($params)
    {
        global $checkUser;
        $type = $params["type"]!=""?$params["type"]:1;  
        $uptype = $params["uptype"]!=""?$params["uptype"]:1; 
		$GUID = $params['GUID'];
        $user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
        if(empty($user_infos)){
            alert($this->utfToGBK('用户不存在'));
            exit;
        }
        if(!in_array($user_infos['Uid'],$checkUser) && $user_infos['Mid']!=1){
            goURL("xg_price_jsw.php?view=nopower");
            exit();
        }
        $where="";
        if($params['id']!=""){
            $where.=" and id='".$params['id']."' ";
        }else if($type==3 && $params['sdate']!="" && $params['edate']!=""){
            $where.=" and sdate='".$params['sdate']."' and edate='".$params['edate']."' ";
        }
        if($type!=7){
            $where.=" and uptype='$uptype' ";
        }
        $base_data=$this->_dao->getRow("select id,sdate,edate from data_table_base  where type='$type'and isdel=0 $where order by id desc limit 1");
        $baseid=$base_data['id'];
        $data=$this->_dao->query("select * from data_table where baseid='$baseid' order by id asc ");
        $newdata=array();
        //$data=$this->array_utfToGBK($data);
        if($type==7){
            foreach($data as $key=>$val){
                if($key==0){
                    $newheader=$val;
                }else{
                    $newdata[$val['dta1']][]=$val;
                }
            }
            foreach($newdata as $key=>$val){
                $newdata[$key][0]['row']=count($val);
            }
        }else{
            if($uptype==1 || $uptype==4){
                $newdata[0]["区域"]="区域";
                foreach($data as $key=>$val){
                    $newdata[0][$val['dta1']]=$val['dta1'];
                    $newdata[$val['dta2']][0]=$val['dta2'];
                    $arr['dta6']=$val['dta6'];
                    $arr['id']=$val['id'];
                    $newdata[$val['dta2']][]=$arr;
                    
                }
            }else if($uptype==3 || $uptype==8 || $uptype==7 || $uptype==10){
                foreach($data as $key=>$val){
                    $header[$val['dta1']][$val['dta2']]['id']=$val['id'];
                    $header[$val['dta1']][$val['dta2']]['data']=$val['dta2'];
                    $header[$val['dta1']][$val['dta2']]['price']=$val['dta6'];
                    $header[$val['dta1']][$val['dta2']]['remark']=$val['dta3'];
                    $header[$val['dta1']][$val['dta2']]['remark2']=$val['dta4'];
                }
                foreach($header as $key=>$val){
                    foreach($val as $key2=>$val2){
                        $newdata[$key][]=$val2;
                    }
                    $newdata[$key][0]['row']=count($val);
                }
            }else if($uptype==9){
                foreach($data as $key=>$val){
                    $arr['id']=$val['id'];
                    $arr['data']=$val['dta2'];
                    $arr['price']=$val['dta6'];
                    $arr['remark']=$val['dta3'];
                    $arr['remark2']=$val['dta4'];
                    $header[$val['dta1']][$val['dta2']][]=$arr;
                }
                foreach($header as $key=>$val){
                    $c=0;
                    foreach($val as $key2=>$val2){
                        $c+=count($val2);
                        $newdata[$key][$key2]=$val2;
                        $newdata[$key][$key2][0]['row2']=count($val2);
                    }
                    $newdata[$key][$key2][0]['row']=$c;
                }
            }else if($uptype==2 || $uptype==5 || $uptype==6){
                foreach($data as $key=>$val){
                    $header[$val['dta1']][$val['dta2']][$val['dta3']]=$val['dta3'];
                    $newdata[$val['dta4']][0]=$val['dta4'];
                    $arr['dta6']=$val['dta6'];
                    $arr['id']=$val['id'];
                    $newdata[$val['dta4']][]=$arr;
                }
                $newheader[0][0]=array("data"=>"区域","row"=>3);
                foreach($header as $key=>$val){
                    $c=0;
                    $arr['data']=$key;
                    foreach($val as $key2=>$val2){
                        $c+=count($val2);
                        $brr['data']=$key2;
                        $brr['col']=count($val2);
                        $newheader[1][]=$brr;
                        foreach($val2 as $key3=>$val3){
                            $crr['data']=$val3;
                            $newheader[2][]=$crr;
                        }
                    }
                    $arr['col']=$c;
                    $newheader[0][]=$arr;
                }
            }
        }
        
        // echo '<pre>';
        // print_r($newdata);exit;
      
        if($params['function']=='export'){

            $objPHPExcel = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
			$objWriter = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($objPHPExcel, 'Xls');
			$sheet = $objPHPExcel->getActiveSheet();
			$sheet->getDefaultColumnDimension()->setWidth(15);//所有单元格（列）默认宽度
            
            $objPHPExcel->getDefaultStyle()->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
			$objPHPExcel->getDefaultStyle()->getAlignment()->setVertical(\PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER);
            $styleThinBlackBorderOutline = array(
				'borders' => array(
					'allborders' => array( //设置全部边框
						'style' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN //粗的是thick
					),
				),
			);

            $sheet->getStyle('A1:P19')->getAlignment()->setVertical(\PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER);    // A1到E3单元格垂直居中
            $sheet->getStyle('A1:P19')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER); // A1单元格水平居中

            $styleArray1 = array(
				'alignment' => array(
				'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
				'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,
				),
				'borders' => array (
					'allBorders' => array (
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,  //设置border样式
                        //'style' => PHPExcel_Style_Border::BORDER_THICK, 另一种样式
                        'color' => array ('argb' => 'FF000000'),     //设置border颜色
                    )
                )
            );
			

            if($params['uptype']==1 || $params['uptype']==4){
                $i=1;
                $j=2;
                foreach($newdata as $k=>$v){
                    if($k==0){
                        foreach($v as $kk=>$vv){
                            $sheet->setCellValueByColumnAndRow($i,1,$vv);
                            $i++;
                        }
                    }else{
                        $ii=1;
                        foreach($v as $kk=>$vv){
                            if($kk==0){
                                $sheet->setCellValueByColumnAndRow($ii,$j,$vv);
                            }else{
                                $sheet->setCellValueByColumnAndRow($ii,$j,$vv['dta6']);
                            }
                            $ii++;
                        }
                        $j++;
                    }
                }
                $sheet->getStyle( 'A1:D6')->applyFromArray($styleArray1);
            }else if($params['uptype']==2||$params['uptype']==5){
                $sheet->setCellValueByColumnAndRow(1,1,"区域");
                $sheet->setCellValueByColumnAndRow(2,1,"高线");
                $sheet->setCellValueByColumnAndRow(2,2,"HPB300");
                $sheet->setCellValueByColumnAndRow(4,1,"盘螺");
                $sheet->setCellValueByColumnAndRow(4,2,"HRB400E");
                $sheet->setCellValueByColumnAndRow(7,1,"螺纹钢");
                $sheet->setCellValueByColumnAndRow(7,2,"HRB400E");
                $sheet->setCellValueByColumnAndRow(2,3,"6-6.5");
                $sheet->setCellValueByColumnAndRow(3,3,"8-10");
                $sheet->setCellValueByColumnAndRow(4,3,"10");
                $sheet->setCellValueByColumnAndRow(5,3,"8");
                $sheet->setCellValueByColumnAndRow(6,3,"6");
                $sheet->setCellValueByColumnAndRow(7,3,"12");
                $sheet->setCellValueByColumnAndRow(8,3,"14");
                $sheet->setCellValueByColumnAndRow(9,3,"16");
                $sheet->setCellValueByColumnAndRow(10,3,"18");
                $sheet->setCellValueByColumnAndRow(11,3,"20");
                $sheet->setCellValueByColumnAndRow(12,3,"22");
                $sheet->setCellValueByColumnAndRow(13,3,"≥25");
                $sheet->setCellValueByColumnAndRow(14,3,"≥28");
                $sheet->setCellValueByColumnAndRow(15,3,"≥32");
                //if($params['uptype']==2){
                    $sheet->setCellValueByColumnAndRow(16,3,"≥36");
                    $sheet->mergeCells('G1:P1');
                    $sheet->mergeCells('G2:P2');
                    $sheet->getStyle( 'A1:P19')->applyFromArray($styleArray1);
                   
                // }else{
                //     $sheet->getStyle( 'A1:O19')->applyFromArray($styleArray1);
                //     $sheet->mergeCells('G1:O1');
                //     $sheet->mergeCells('G2:O2');
                // }
                $sheet->mergeCells('A1:A3');
                $sheet->mergeCells('B1:C1');
                $sheet->mergeCells('B2:C2');
                $sheet->mergeCells('D1:F1');
                $sheet->mergeCells('D2:F2');
                $j=4;
                foreach($newdata as $k=>$v){
                    $i=1;
                    foreach($v as $kk=>$vv){
                        if( $kk==0){
                            $sheet->setCellValueByColumnAndRow($i,$j,$vv);
                        }else{
                            $sheet->setCellValueByColumnAndRow($i,$j,$vv['dta6']); 
                        }
                        $i++;
                    }
                    $j++;
                }
            }else if($params['uptype']==3 || $params['uptype']==8 || $params['uptype']==10 ){
                
                $sheet->setCellValueByColumnAndRow(1,1,"品种");
                $sheet->setCellValueByColumnAndRow(2,1,"牌号");
                $sheet->setCellValueByColumnAndRow(3,1,"基价");
                $sheet->setCellValueByColumnAndRow(4,1,"说明");
                if($params['uptype']==3){
                    $sheet->setCellValueByColumnAndRow(5,1,"区域加价");
                    $sheet->getColumnDimension('E')->setWidth(50); 
                    $sheet->getStyle( 'A1:E39')->applyFromArray($styleArray1);
                }else if($params['uptype']==8){
                    $sheet->getColumnDimension('D')->setWidth(160); 
                    $sheet->getStyle( 'A1:D26')->applyFromArray($styleArray1);
                }else if($params['uptype']==10){
                    $sheet->getColumnDimension('A')->setWidth(30); 
                    $sheet->getColumnDimension('D')->setWidth(50); 
                    $sheet->getStyle( 'A1:D2')->applyFromArray($styleArray1);
                }
                
                $sheet->getColumnDimension('B')->setWidth(25);  
			   
			    
                $j=2;
                $row=2;
                $row2=2;
               
                foreach($newdata as $k=>$v){
                    foreach($v as $kk=>$vv){
                        $sheet->setCellValueByColumnAndRow(1,$j,$k); 
                        $sheet->setCellValueByColumnAndRow(2,$j,$vv['data']); 
                        $sheet->setCellValueByColumnAndRow(3,$j,$vv['price']); 
                        $sheet->setCellValueByColumnAndRow(4,$j,$vv['remark']); 
                        if($params['uptype']==3){
                            $sheet->setCellValueByColumnAndRow(5,$j,$vv['remark2']); 
                        }
                        $j++;
                        $row2++;
                    }
                    
                    $sheet->mergeCells('A'.$row.':A'.$row2-1);
                    $row=$row2;
                }
                
            }else if($params['uptype']==6){
                $sheet->setCellValueByColumnAndRow(1,1,"区域");
                $sheet->setCellValueByColumnAndRow(2,1,"φ6.5—φ12.5");
                $sheet->setCellValueByColumnAndRow(2,2,"拉丝");
                $sheet->setCellValueByColumnAndRow(3,2,"硬线");
                $sheet->setCellValueByColumnAndRow(7,2,"PC线");
                $sheet->setCellValueByColumnAndRow(8,2,"冷镦钢");
                $sheet->setCellValueByColumnAndRow(12,2,"焊线");
                $sheet->setCellValueByColumnAndRow(13,2,"XGM2");
                $sheet->setCellValueByColumnAndRow(2,3,"Q195");
                $sheet->setCellValueByColumnAndRow(3,3,"10～45#");
                $sheet->setCellValueByColumnAndRow(4,3,"50～70#");
                $sheet->setCellValueByColumnAndRow(5,3,"60～70Mn");
                $sheet->setCellValueByColumnAndRow(6,3,"72B");
                $sheet->setCellValueByColumnAndRow(7,3,"82B");
                $sheet->setCellValueByColumnAndRow(8,3,"30MnSi");
                $sheet->setCellValueByColumnAndRow(9,3,"ML08Al");
                $sheet->setCellValueByColumnAndRow(10,3,"ML40Cr");
                $sheet->setCellValueByColumnAndRow(11,3,"22A");
                $sheet->setCellValueByColumnAndRow(12,3,"H08A");
                $sheet->mergeCells('A1:A3');
                $sheet->mergeCells('B1:M1');
                $sheet->mergeCells('C2:G2');
                $sheet->mergeCells('I2:K2');
                $j=4;
                foreach($newdata as $k=>$v){
                    $i=1;
                    foreach($v as $kk=>$vv){
                        if( $kk==0){
                            $sheet->setCellValueByColumnAndRow($i,$j,$vv);
                        }else{
                            $sheet->setCellValueByColumnAndRow($i,$j,$vv['dta6']); 
                        }
                        $i++;
                    }
                    $j++;
                }
                $sheet->getStyle( 'A1:M8')->applyFromArray($styleArray1);
            }else if($params['uptype']==7){
                $sheet->setCellValueByColumnAndRow(1,1,"品种");
                $sheet->setCellValueByColumnAndRow(2,1,"牌号");
                $sheet->setCellValueByColumnAndRow(3,1,"指导价");
                $sheet->getColumnDimension('A')->setWidth(40);  
                $j=2;
                foreach($newdata as $k=>$v){
                    foreach($v as $kk=>$vv){
                        $sheet->setCellValueByColumnAndRow(1,$j,$k); 
                        $sheet->setCellValueByColumnAndRow(2,$j,$vv['data']); 
                        $sheet->setCellValueByColumnAndRow(3,$j,$vv['price']); 
                        $j++;
                    }
                }
                $sheet->getStyle( 'A1:C61')->applyFromArray($styleArray1);
            }else if($params['uptype']==9 ){
                
                $sheet->setCellValueByColumnAndRow(1,1,"品种");
                $sheet->setCellValueByColumnAndRow(2,1,"规格（mm）");
                $sheet->setCellValueByColumnAndRow(3,1,"材质");
                $sheet->setCellValueByColumnAndRow(4,1,"价格（元/吨）");
                $sheet->setCellValueByColumnAndRow(5,1,"备注");
                $sheet->getStyle( 'A1:E3')->applyFromArray($styleArray1);
                $sheet->getColumnDimension('C')->setWidth(30);  
                $sheet->getColumnDimension('E')->setWidth(40);  
                $j=2;
                $row=2;
                $row2=2;
                $row3=2;
                $row4=2;
                foreach($newdata as $k=>$v){
                    foreach($v as $kk=>$vv){
                        foreach($vv as $kkk=>$vvv){
                            $sheet->setCellValueByColumnAndRow(1,$j,$k); 
                            $sheet->setCellValueByColumnAndRow(2,$j,$kk); 
                            $sheet->setCellValueByColumnAndRow(3,$j,$vvv['remark']); 
                            $sheet->setCellValueByColumnAndRow(4,$j,$vvv['price']); 
                            $sheet->setCellValueByColumnAndRow(5,$j,$vvv['remark2']); 
                            $j++;
                            $row2++;
                            $row3++;
                        }
                        $sheet->mergeCells('B'.$row.':B'.$row2-1);
                        $row=$row2;
                        
                    }
                    $sheet->mergeCells('A'.$row4.':A'.$row3-1);
                    $row4=$row3;
                }
                
            }


            header("Pragma: public");
			header("Expires: 0");
			header("Cache-Control:must-revalidate, post-check=0, pre-check=0");
			header("Content-Type:application/force-download");
			header("Content-Type:application/vnd.ms-execl");
			header("Content-Type:application/octet-stream");
			header("Content-Type:application/download");
			header("Content-Disposition:attachment;filename=".$GLOBALS["upload_title"][$uptype]."（".date("Y.m.d",strtotime($params['sdate']))."-".date("Y.m.d",strtotime($params['edate']))."）.xls");
			header("Content-Transfer-Encoding:binary");
			$objWriter->save('php://output');
			exit;


        }


        // echo '<pre>';
        // print_r($newdata);

        $this->assign('newdata',$newdata);
        $this->assign('newheader',$newheader);
        $this->assign('GUID',$params["GUID"]);
        $this->assign('uptype',$uptype);
        $this->assign('type',$type);
        $this->assign('baseid',$baseid);
        $this->assign('params',$params);
        $this->assign('sdate',$base_data['sdate']);
        $this->assign('edate',$base_data['edate']);
        $this->assign('uptype_title',$GLOBALS["upload_title"][$uptype]);
    }

    public function update_base_data($params){
        $type=$params['type'];
        $uptype=$params['uptype'];
        $baseid=$params['baseid'];
        $isadd=$params['isadd'];
        $sdate=$params['sdate'];
        $edate=$params['edate'];
        $GUID = $params['GUID'];
        $user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
        $response["Success"] = 1;
		$response['Message'] =$this->gbkToUTF8('数据保存成功');
        if(empty($user_infos)){
            $response["Success"] = 0;
            $response['Message'] =$this->gbkToUTF8('用户不存在');
            echo $this->pri_JSON($response);
            exit;
        }
        $dta_type=$GLOBALS["dta_type_arr"][$uptype];
        $dta_1_Arr=isset($params["dta_1"])?$params["dta_1"]:array();
        $dta_2_Arr=isset($params["dta_2"])?$params["dta_2"]:array();
        $dta_3_Arr=isset($params["dta_3"])?$params["dta_3"]:array();
        $dta_4_Arr=isset($params["dta_4"])?$params["dta_4"]:array();
        $dta_5_Arr=isset($params["dta_6"])?$params["dta_5"]:array();
        $dta_6_Arr=isset($params["dta_6"])?$params["dta_6"]:array();
        $dta_7_Arr=isset($params["dta_7"])?$params["dta_7"]:array();
        $dta_8_Arr=isset($params["dta_8"])?$params["dta_8"]:array();
        $new_data_3=isset($params["new_dta_3"])?$params["new_dta_3"]:array();
        $new_data_6=isset($params["new_dta_6"])?$params["new_dta_6"]:array();
        $new_dta_info=isset($params["new_dta_info"])?$params["new_dta_info"]:array();
        
        if($type==1 || $type==3 || ($type==2 && $baseid!="") ){
            foreach($dta_6_Arr as $id=>$dta_6){
                $dta_1=isset($dta_1_Arr[$id])?$dta_1_Arr[$id]:"";
                $dta_2=isset($dta_2_Arr[$id])?$dta_2_Arr[$id]:"";
                $dta_3=isset($dta_3_Arr[$id])?$dta_3_Arr[$id]:"";
                $dta_4=isset($dta_4_Arr[$id])?$dta_4_Arr[$id]:"";
                if($uptype==1 || $uptype==2 || $uptype==4 || $uptype==5 || $uptype==6){
                    $this->_dao->execute("UPDATE data_table SET dta6='$dta_6' WHERE id='$id'");
                }else if($uptype==3){
                    $this->_dao->execute("UPDATE data_table SET dta2='$dta_2',dta3='$dta_3',dta4='$dta_4',dta6='$dta_6' WHERE id='$id'");
                }else if($uptype==7 && $type==2){
                    $this->_dao->execute("UPDATE data_table SET dta1='$dta_1',dta2='$dta_2',dta6='$dta_6' WHERE id='$id'");
                }else if($uptype==7 ){
                    $this->_dao->execute("UPDATE data_table SET dta2='$dta_2',dta6='$dta_6' WHERE id='$id'");
                }else if($uptype==8 || $uptype==10){
                    $this->_dao->execute("UPDATE data_table SET dta2='$dta_2',dta3='$dta_3',dta6='$dta_6' WHERE id='$id'");
                }else if($uptype==9){
                    $this->_dao->execute("UPDATE data_table SET dta3='$dta_3',dta6='$dta_6' WHERE id='$id'");
                }
            }
        }else if($type==2 && $baseid==""){
            $valueSql_1="";
            $this->_dao->del_data_table_base(2,$uptype,$sdate,$edate,$user_infos);
            $baseid=$this->_dao->insert_data_table_base(2,$uptype,$sdate,$edate,$user_infos,$dta_type);
            
            foreach($dta_6_Arr as $id=>$dta_6){
                $dta_1=isset($dta_1_Arr[$id])?$dta_1_Arr[$id]:"";
                $dta_2=isset($dta_2_Arr[$id])?$dta_2_Arr[$id]:"";
                $dta_3=isset($dta_3_Arr[$id])?$dta_3_Arr[$id]:"";
                $dta_4=isset($dta_4_Arr[$id])?$dta_4_Arr[$id]:"";
                $valueSql_1.="($baseid,'$dta_1','$dta_2','$dta_3','$dta_4','$dta_6','$dta_type',NOW(),'".$user_infos['Uid']."'),";
            }
            if($valueSql_1!="")
            {
                $basesql = "INSERT INTO data_table(baseid,dta1,dta2,dta3,dta4,dta6,dta_type,createtime,createuser) VALUES";
                $tsql = substr($valueSql_1, 0, -1);
                $this->_dao->execute($basesql.$tsql);
            }
        }else if($type==7 && $baseid!=""){
            foreach($dta_2_Arr as $id=>$dta_2){
                $dta_1=isset($dta_1_Arr[$id])?$dta_1_Arr[$id]:"";
                $dta_5=isset($dta_5_Arr[$id])?$dta_5_Arr[$id]:"";
                $dta_3=isset($dta_3_Arr[$id])?mysql_real_escape_string($dta_3_Arr[$id]):"";
                $dta_4=isset($dta_4_Arr[$id])?mysql_real_escape_string($dta_4_Arr[$id]):"";
                $dta_6=isset($dta_6_Arr[$id])?$dta_6_Arr[$id]:"";
                $dta_7=isset($dta_7_Arr[$id])?mysql_real_escape_string($dta_7_Arr[$id]):"";
                $dta_8=isset($dta_8_Arr[$id])?mysql_real_escape_string($dta_8_Arr[$id]):"";
                if($dta_3!="" || $dta_4!="" || $dta_5!="" || $dta_6!="" || $dta_7!="" || $dta_8!=""){
                    $this->_dao->execute("UPDATE data_table SET dta2='$dta_2',dta3='$dta_3',dta4='$dta_4',dta5='$dta_5',dta6='$dta_6',dta7='$dta_7',dta8='$dta_8' WHERE id='$id'");
                }else{
                    $this->_dao->execute("DELETE FROM data_table WHERE id='$id'");
                }
            }
            $ids=isset($params["ids"])?$params["ids"]:array();
            $data_ids=$this->_dao->getDataByBaseId($baseid);
            foreach($data_ids as $key=>$val){
                if($key!=0 && !in_array($val['id'],$ids)){
                    $this->_dao->execute("DELETE FROM data_table WHERE id='".$val['id']."'");
                }
            }
            if(!empty($new_data_3)){
                $valueSql="";
                foreach($new_data_3 as $key=>$val){
                    $dta_1=isset($params["new_dta_info"][$key])?$params["new_dta_info"][$key]:"";
                    $dta_2_Arr=isset($params["new_dta_2"][$key])?$params["new_dta_2"][$key]:array();
                    $dta_4_Arr=isset($params["new_dta_4"][$key])?$params["new_dta_4"][$key]:array();
                    $dta_5_Arr=isset($params["new_dta_5"][$key])?$params["new_dta_5"][$key]:array();
                    $dta_6_Arr=isset($params["new_dta_6"][$key])?$params["new_dta_6"][$key]:array();
                    $dta_7_Arr=isset($params["new_dta_7"][$key])?$params["new_dta_7"][$key]:array();
                    $dta_8_Arr=isset($params["new_dta_8"][$key])?$params["new_dta_8"][$key]:array();
                    foreach($val as $key2=>$dta_3){
                        $dta_2=$dta_2_Arr[$key2];
                        $dta_3=mysql_real_escape_string($dta_3);
                        $dta_4=mysql_real_escape_string($dta_4_Arr[$key2]);
                        $dta_5=$dta_5_Arr[$key2];
                        $dta_6=$dta_6_Arr[$key2];
                        $dta_7=mysql_real_escape_string($dta_7_Arr[$key2]);
                        $dta_8=mysql_real_escape_string($dta_8_Arr[$key2]);
                        if($dta_3!="" || $dta_4!="" || $dta_5!="" || $dta_6!="" || $dta_7!="" || $dta_8!=""){
                            $valueSql.="($baseid,'$dta_1','$dta_2','$dta_3','$dta_4','$dta_5','$dta_6','$dta_7','$dta_8','YRLZBHZB',NOW(),'".$user_infos['Uid']."'),";
                        }
                    }
                }
                if($valueSql!="")
                {
                    $basesql = "INSERT INTO data_table(baseid,dta1,dta2,dta3,dta4,dta5,dta6,dta7,dta8,dta_type,createtime,createuser) VALUES";
                    $tsql = substr($valueSql, 0, -1);
                    $this->_dao->execute($basesql.$tsql);
                }
            }
        }
        if($type==2){
            if( !empty($new_data_6)){
                $valueSql="";
                foreach($new_data_6 as $key=>$val){
                    if($new_dta_info[$key]!=""){
                        $dta_1_Arr=isset($params["new_dta_1"][$key])?$params["new_dta_1"][$key]:array();
                        $dta_2_Arr=isset($params["new_dta_2"][$key])?$params["new_dta_2"][$key]:array();
                        $dta_3_Arr=isset($params["new_dta_3"][$key])?$params["new_dta_3"][$key]:array();
                        $dta_4_Arr=isset($params["new_dta_4"][$key])?$params["new_dta_4"][$key]:array();
                        foreach($val as $key2=>$dta_6){
                            $dta_1=$dta_1_Arr[$key2];
                            $dta_2=$dta_2_Arr[$key2];
                            $dta_3=$dta_3_Arr[$key2];
                            $dta_4=$dta_4_Arr[$key2];
                            if($uptype==1 || $uptype==4){
                                $dta_2=$new_dta_info[$key];
                            }else if($uptype==2 || $uptype==5 || $uptype==6){
                                $dta_4=$new_dta_info[$key];
                            }else if($uptype==3 || $uptype==7 || $uptype==8){
                                $dta_1=$new_dta_info[$key];
                                $dta_3="";
                                $dta_4="";
                            }else if($uptype==9 || $uptype==10){
                                $dta_1=$new_dta_info[$key];
                            }
                            $valueSql.="($baseid,'$dta_1','$dta_2','$dta_3','$dta_4','$dta_6','$dta_type',NOW(),'".$user_infos['Uid']."'),";
                        }
                    }
                }
                if($valueSql!="")
                {
                    $basesql = "INSERT INTO data_table(baseid,dta1,dta2,dta3,dta4,dta6,dta_type,createtime,createuser) VALUES";
                    $tsql = substr($valueSql, 0, -1);
                    $this->_dao->execute($basesql.$tsql);
                }
            }
            
        }
        if($type==2 || $type==3){
            $this->set_table_data_price($type,$uptype,$sdate,$edate,$user_infos);
        }
        echo $this->pri_JSON($response);
        exit;
    }



    public function data_zd_manage($params)
    {
        global $checkUser;
        $GUID = $params['GUID'];
        $uptype = $params["uptype"]!=""?$params["uptype"]:1; 
        $type = $params["type"]!=""?$params["type"]:1;    
        $user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
        if(empty($user_infos)){
            alert($this->utfToGBK('用户不存在'));
            exit;
        }
        if(!in_array($user_infos['Uid'],$checkUser) && $user_infos['Mid']!=1){
            goURL("xg_price_jsw.php?view=nopower");
            exit();
        }
        $s = "";
        // 结算文的日期默认是上一期的日期
        if($uptype == 1 || $uptype == 2) {
            $s = date('Y-m-d',strtotime("-10 day"));

        }
        $dayRange = $this->getDayRange($s);
        $sdate = $params['sdate'] ? $params['sdate']: $dayRange[0];
        $edate = $params['edate'] ? $params['edate']: $dayRange[1];
        
        $this->assign('sdate',$sdate);
        $this->assign('edate',$edate);
        $this->assign('type',$type);
        $this->assign('GUID',$GUID);
        $this->assign('uptype',$uptype);
        $this->assign('uptype_title',$GLOBALS["upload_title"][$uptype]);
    }

    public function getdatalist($params)
    {
        $uptype = $params["uptype"]!=""?$params["uptype"]:1; 
        $type = $params["type"]!=""?$params["type"]:1;    
		$GUID = $params['GUID'];
        $user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
        
        $per = $params['limit']=='' ? 20 : $params['limit'];
		$page = $params['page'] == '' ? 1 : $params['page'];
		$start = ( $page - 1 ) * $per;
        $where="";
        if($params["sdate"]!=""){
            $where.=" and sdate like '".$params["sdate"]."%'";
        }
        if($params["edate"]!=""){
            $where.=" and edate like '".$params["edate"]."%'";
        }
        if($type!=7){
            $where.=" and uptype='$uptype' ";
        }
        $data=$this->_dao->query("select * from data_table_base  where type='$type' and isdel=0  $where order by id desc limit $start,$per");
        $total=$this->_dao->getOne("select count(*) from data_table_base  where type='$type' and isdel=0   $where ");
        $return_array = array(
            "code"=> 0,
            "data"=> $data,
            "count"=>$total
        );
        echo json_encode($return_array);
		exit;

    }
    public function del_base_data($params)
    {
        $baseid = $params['baseid'];
        $response = array(
			'Success'  => 1,
			'Message'  => $this->gbkToUTF8('删除成功'),
		);
        if($baseid!=""){
            // 删除type=3的数据
            $sql = "select * from data_table_base where id='$baseid' ";
            $baseinfo = $this->_dao->getRow($sql);
            if(!empty($baseinfo) && $baseinfo['type']!=7 && $baseinfo['type']!=9){
                $sql="UPDATE data_table_base SET isdel=1 WHERE uptype='{$baseinfo['uptype']}' and sdate='{$baseinfo['sdate']}'  and edate='{$baseinfo['edate']}' and type=3 and isdel=0 limit 1";
                $this->_dao->execute($sql);
            }
            $sql="UPDATE data_table_base SET isdel=1 WHERE id='$baseid' ";
            $this->_dao->execute($sql);

        }
        echo $this->pri_JSON($response);
		exit;
    }

    

    public function data_zd_input($params){
        global $checkUser;
        $uptype = $params["uptype"]!=""?$params["uptype"]:1; 
        $type = $params["type"]!=""?$params["type"]:1;    
        $isadd = $params["id"]!=""? 0:1;    
		$GUID = $params['GUID'];
        $user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
        if(empty($user_infos)){
            alert($this->utfToGBK('用户不存在'));
            exit;
        }
        if(!in_array($user_infos['Uid'],$checkUser) && $user_infos['Mid']!=1){
            goURL("xg_price_jsw.php?view=nopower");
            exit();
        }
        $where="";
        if($type!=7){
            $where.=" and uptype='$uptype'";
        }
        
        if($params['id']){
            //修改价差
            $baseArr=$this->_dao->getRow("select * from data_table_base  where type='$type' and isdel=0 and id='".$params['id']."' $where order by id desc limit 1");
            $baseid=$baseArr['id'];
            $baseid_1=$baseid;
            $this->assign('baseid',$baseid);
            $sdate = $baseArr['sdate'];
            $edate = $baseArr['edate'];
        }else if($type!=7){
            //录入新价差
            //获取上期价格
            $baseArr=$this->_dao->getRow("select * from data_table_base  where type=3 and isdel=0 and uptype='$uptype' order by edate desc limit 1");
            $baseid=$baseArr['id'];
            if($baseid==""){
                //获取基础数据
                $baseid=$this->_dao->getOne("select id from data_table_base  where type=1 and isdel=0 and uptype='$uptype' order by id desc limit 1");
            }
            $s = "";
            if($uptype == 1 || $uptype == 2) {
                $s = date('Y-m-d',strtotime('-10 day'));
            }
            $dayRange = $this->getDayRange($s);
            $sdate = $params['sdate'] ? $params['sdate']: $dayRange[0];
            $edate = $params['edate'] ? $params['edate']: $dayRange[1];
        }
        
        $data=$this->_dao->query("select * from data_table where baseid='$baseid' order by id asc ");
        $newdata=array();
        //$data=$this->array_utfToGBK($data);
        $pzname_arr=array();
        $city_arr=array();
        if($type==7){
            foreach($data as $key=>$val){
                if($key==0){
                    $newheader=$val;
                }else{
                    $newdata[$val['dta1']][]=$val;
                }
            }
            foreach($newdata as $key=>$val){
                $newdata[$key][0]['row']=count($val);
            }
        }else{
            if($uptype==1 || $uptype==4){
                $newdata[0]["区域"]="区域";
                foreach($data as $key=>$val){
                    $newdata[0][$val['dta1']]=$val['dta1'];
                    $newdata[$val['dta2']][0]=$val['dta2'];
                    $arr['dta6']=$isadd==0?$val['dta6']:"";
                    $arr['id']=$val['id'];
                    $arr['dta1']=$val['dta1'];
                    $arr['dta2']=$val['dta2'];
                    $newdata[$val['dta2']][]=$arr;
                    if(!isset($pzname_arr[$val['dta1']])){
                        $pzname_arr[$val['dta1']]=$val['dta1'];
                    }
                    if(!isset($city_arr[$val['dta2']])){
                        $city_arr[$val['dta2']]=$val['dta2'];
                    }
                }
                $n=0;
                foreach($pzname_arr as $key=>$val){
                    $pzname_arr[$key]=$n;
                    $n++;
                }
                $m=0;
                foreach($city_arr as $key=>$val){
                    $city_arr[$key]=$m;
                    $m++;
                }

                
            }else if($uptype==2 || $uptype==5 || $uptype==6){
                foreach($data as $key=>$val){
                    $header[$val['dta1']][$val['dta2']][$val['dta3']]=$val['dta3'];
                    $newdata[$val['dta4']][0]=$val['dta4'];
                    $arr['dta1']=$val['dta1'];
                    $arr['dta2']=$val['dta2'];
                    $arr['dta3']=$val['dta3'];
                    $arr['dta4']=$val['dta4'];
                    $arr['dta6']=$isadd==0?$val['dta6']:"";
                    $arr['id']=$val['id'];
                    $newdata[$val['dta4']][]=$arr;
                    if(!isset($pzname_arr[$val['dta3']])){
                        $pzname_arr[$val['dta3']]=$val['dta3'];
                    }
                    if(!isset($city_arr[$val['dta4']])){
                        $city_arr[$val['dta4']]=$val['dta4'];
                    }
                }

                $n=0;
                foreach($pzname_arr as $key=>$val){
                    $pzname_arr[$key]=$n;
                    $n++;
                }
                $m=0;
                foreach($city_arr as $key=>$val){
                    $city_arr[$key]=$m;
                    $m++;
                }
                
                $newheader[0][0]=array("data"=>"区域","row"=>3);
                foreach($header as $key=>$val){
                    $c=0;
                    $arr['data']=$key;
                    foreach($val as $key2=>$val2){
                        $c+=count($val2);
                        $brr['data']=$key2;
                        $brr['col']=count($val2);
                        if($key2=="XGM2"){
                            $brr['row']=2;
                        }
                        $newheader[1][]=$brr;
                        foreach($val2 as $key3=>$val3){
                            if($val3==""){
                                continue;
                            }
                            $crr['data']=$val3;
                            $newheader[2][]=$crr;
                        }
                    }
                    $arr['col']=$c;
                    $newheader[0][]=$arr;
                }

                
            }else if($uptype==3 || $uptype==8 || $uptype==7 || $uptype==10){
                foreach($data as $key=>$val){
                    $header[$val['dta1']][$val['dta2']]['id']=$val['id'];
                    $header[$val['dta1']][$val['dta2']]['dta1']=$val['dta1'];
                    $header[$val['dta1']][$val['dta2']]['dta2']=$val['dta2'];
                    $header[$val['dta1']][$val['dta2']]['dta6']=$isadd!=1?$val['dta6']:"";
                    $header[$val['dta1']][$val['dta2']]['dta3']=$val['dta3'];
                    $header[$val['dta1']][$val['dta2']]['dta4']=$val['dta4'];
                }
                $n=0;
                foreach($header as $key=>$val){
                    foreach($val as $key2=>$val2){
                        $val2['jckey']=$n;
                        $newdata[$key][]=$val2;
                    }
                    $newdata[$key][0]['row']=count($val);
                    $pzname_arr[$key]=$n;
                    $n++;
                }
                
            }else if($uptype==9){
                foreach($data as $key=>$val){
                    $arr['id']=$val['id'];
                    $arr['dta2']=$val['dta2'];
                    $arr['dta6']=$isadd!=1?$val['dta6']:"";
                    $arr['dta3']=$val['dta3'];
                    $arr['dta4']=$val['dta4'];
                    $header[$val['dta1']][$val['dta2']][]=$arr;
                }
                $n=0;
                foreach($header as $key=>$val){
                    $c=0;
                    foreach($val as $key2=>$val2){
                        $c+=count($val2);
                        $newdata[$key][$key2]=$val2;
                        $newdata[$key][$key2][0]['row2']=count($val2);
                    }
                    $newdata[$key][$key2][0]['row']=$c;
                    $pzname_arr[$key]=$n;
                    $n++;
                }
            }
        }

        // echo '<pre>';
        // print_r($pzname_arr);

        $this->assign('sdate',$sdate);
        $this->assign('edate',$edate);
        $this->assign('newdata',$newdata);
        $this->assign('newheader',$newheader);
        $this->assign('GUID',$params["GUID"]);
        $this->assign('uptype',$uptype);
        $this->assign('type',$type);
        $this->assign('isadd',$isadd);
        $this->assign('pzname_arr',$pzname_arr);
        $this->assign('city_arr',$city_arr);
        $this->assign('params',$params);
        $this->assign('uptype_title',$GLOBALS["upload_title"][$uptype]);
    }

    public function set_table_data_price($type,$uptype,$sdate,$edate,$user_infos){
        $valueSql="";
        $price_data=array();
        $dta_type=$GLOBALS["dta_type_arr"][$uptype];
        
        $zd_data_baseid=$this->_dao->getBaseIdByTypeAndDate(2,$uptype,$sdate,$edate);//价差
        $zd_data=$this->_dao->getDataByBaseId($zd_data_baseid);
        $price_baseid=$this->_dao->getBaseIdByTypeAndDate(3,$uptype,$sdate,$edate);//本期价格
        $price_baseid_1=$price_baseid;
        $last_price_baseid=$this->_dao->getBaseIdByTypeAndEDate(3,$uptype,$sdate,$edate);//上期价格
        if($last_price_baseid==""){
            $last_price_baseid=$this->_dao->getBaseIdByType(1,$uptype);//基础数据
        }
        $last_price_data=$this->_dao->getDataByBaseId($last_price_baseid);
        $last_price_data_arr=$this->getDataArray($uptype,$last_price_data);
        $last_price_data=$last_price_data_arr['newdata'];
       
        if($price_baseid==""){
            $price_baseid=$this->_dao->insert_data_table_base(3,$uptype,$sdate,$edate,$user_infos,$dta_type);
        }else{
            $price_data=$this->_dao->getDataByBaseId($price_baseid);
        }
        //$zd_data=$this->array_utfToGBK($zd_data);
        $price_data_arr=$this->getDataArray($uptype,$price_data);
        $price_data=$price_data_arr['newdata'];
        $price_ids=$price_data_arr['ids'];
        $newdata=array();
        foreach($zd_data as $key=>$val){
            if($uptype==2 || $uptype==5 || $uptype==6){
                $last_price=!empty($last_price_data)?$last_price_data[$val['dta1']][$val['dta2']][$val['dta3']][$val['dta4']]['dta6']:0;
                $id=!empty($price_data)?$price_data[$val['dta1']][$val['dta2']][$val['dta3']][$val['dta4']]['id']:"";
                $price=!empty($price_data)?$price_data[$val['dta1']][$val['dta2']][$val['dta3']][$val['dta4']]['dta6']:0;
            }else if($uptype==9){
                $last_price=!empty($last_price_data)?$last_price_data[$val['dta1']][$val['dta2']][$val['dta3']]['dta6']:0;
                $price=!empty($price_data)?$price_data[$val['dta1']][$val['dta2']][$val['dta3']]['dta6']:0;
                $id=!empty($price_data)?$price_data[$val['dta1']][$val['dta2']][$val['dta3']]['id']:"";
            }else{
                $last_price=!empty($last_price_data)?$last_price_data[$val['dta1']][$val['dta2']]['dta6']:0;
                $price=!empty($price_data)?$price_data[$val['dta1']][$val['dta2']]['dta6']:0;
                $id=!empty($price_data)?$price_data[$val['dta1']][$val['dta2']]['id']:"";
            }
            if($type==3){
                $price= (float)$price-(float)$last_price;
                $id=$val['id'];
            }else{
                $price=(float)$last_price+(float)$val['dta6'];
            }
            if($id!=""){
                $this->_dao->execute("UPDATE data_table SET dta6='$price' WHERE id='$id'");
                unset($price_ids[$id]);
            }else{

                $valueSql.="($price_baseid,'".$this->gbkToUTF8($val['dta1'])."','".$this->gbkToUTF8($val['dta2'])."','".$this->gbkToUTF8($val['dta3'])."','".$this->gbkToUTF8($val['dta4'])."','$price','$dta_type',NOW(),'".$user_infos['Uid']."'),";
            }
        }
        if($price_ids && $type==2){
            $this->_dao->execute("delete from data_table where baseid='$price_baseid' and id in ('".implode("','",$price_ids)."')");
        }
        if($valueSql!="")
        {
            $basesql = "INSERT INTO data_table(baseid,dta1,dta2,dta3,dta4,dta6,dta_type,createtime,createuser) VALUES";
            $tsql = substr($valueSql, 0, -1);
            $this->_dao->execute($basesql.$tsql);
        }
    }

    function array_utfToGBK($data){
        foreach($data as $key=>$val){
            $data[$key]['dta1']=$this->utfToGBK($val['dta1']);
            $data[$key]['dta2']=$this->utfToGBK($val['dta2']);
            $data[$key]['dta3']=$this->utfToGBK($val['dta3']);
            $data[$key]['dta4']=$this->utfToGBK($val['dta4']);
        }
        return $data;
    }

    function getDataArray($uptype,$data){
        $newdata=array();
        $ids=array();
        if(!empty($data)){
            foreach($data as $key=>$val){
                $val['dta1']=$this->utfToGBK($val['dta1']);
                $val['dta2']=$this->utfToGBK($val['dta2']);
                $val['dta3']=$this->utfToGBK($val['dta3']);
                $val['dta4']=$this->utfToGBK($val['dta4']);
                if($uptype==2 || $uptype==5 || $uptype==6){
                    $newdata[$val['dta1']][$val['dta2']][$val['dta3']][$val['dta4']]=$val;
                }else if($uptype==9){
                    $newdata[$val['dta1']][$val['dta2']][$val['dta3']]=$val;
                }else{
                    $newdata[$val['dta1']][$val['dta2']]=$val;
                }
                $ids[$val['id']]=$val['id'];
            }
        }
        return array("newdata"=>$newdata,"ids"=>$ids);
    }
    function getDayRange($date='') {
        if(empty($date)) $date = date('Y-m-d');
        $inputDate = strtotime($date);
        $day = intval(date('j', $inputDate));

        $x = 3;
        if ($day >= 6 && $day <= 15) {
            $startDay = date('Y-m-06', $inputDate);
            $endDay = date('Y-m-15', $inputDate);
            $x = 1;
        } elseif ($day >= 16 && $day <= 25) {
            $startDay = date('Y-m-16', $inputDate);
            $endDay = date('Y-m-25', $inputDate);
            $x = 2;
        } else if($day <= 5){
            $nextMonth = date('m', strtotime('+1 month', $inputDate));
            $nextYear = date('Y', strtotime('+1 month', $inputDate));
    
            $startDay = date('Y-m-26', strtotime('-1 month', $inputDate));
            $endDay = date('Y-m-05', strtotime('-1month', strtotime("$nextYear-$nextMonth-05")));
        } else if($day >= 26){
            $nextMonth = date('m', strtotime('+1 month', $inputDate));
            $nextYear = date('Y', strtotime('+1 month', $inputDate));
    
            $startDay = date('Y-m-26', strtotime('-0 month', $inputDate));
            $endDay = date('Y-m-05', strtotime('-0 month', strtotime("$nextYear-$nextMonth-05")));
        }
        return array($startDay, $endDay, $x);
    }

    function getDayRangeNow($date='') {
        if(empty($date)) $date = date('Y-m-d');
        $inputDate = strtotime($date);
        $day = intval(date('j', $inputDate));
        $x = 3;
        if ($day >= 6 && $day <= 15) {
            $startDay = date('Y-m-06', $inputDate);
            $endDay = date('Y-m-15', $inputDate);
            $x = 1;
        } elseif ($day >= 16 && $day <= 25) {
            $startDay = date('Y-m-16', $inputDate);
            $endDay = date('Y-m-25', $inputDate);
            $x = 2;
        } else {
            $nextMonth = date('m', strtotime('+1 month', $inputDate));
            $nextYear = date('Y', strtotime('+1 month', $inputDate));
    
            $startDay = date('Y-m-26', strtotime('-0 month', $inputDate));
            $endDay = date('Y-m-05', strtotime('-0month', strtotime("$nextYear-$nextMonth-05")));
        }
        return array($startDay, $endDay, $x);
    }

    //普通文件上传
    function uploadFile($params){
        $upfile = $_FILES['file'];
        $GUID = $params['GUID'];
        $mode = $params['mode'];
        $uptype = $params['uptype'];
        $response = array(
            'Success'  => 1,
            'Message'  => '',
            'GUID'     => $GUID,
            'mode'     => $mode,
        );
        // 不同页面上传类型控制
        switch($params['filetype'])
        {
            case 'excel':
                $uptypes=array(
                    'application/vnd.ms-excel',
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                );
                $filetype = "Excel文件";
            break;
            case 'word':
                $uptypes=array(
                    'application/msword',
                    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                );
                $filetype = "Word文件";
            break;
            default:
                $uptypes=array(
                    'application/vnd.ms-excel',
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                );
                $filetype = "Excel文件";
            break;
        }

        
        if($GUID==""){
            $response['Success'] = 0;
            $response['Message'] = $this->gbkToUTF8('GUID不能为空');
        }
        if($response['Success']){
            $user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
            if(empty($user_infos)){
                $response['Success'] = 0;
                $response['Message'] = $this->gbkToUTF8('用户不存在');
            }
            if($response['Success']){
                if ($upfile['error']==0)
                {
                    
                    if (is_uploaded_file($upfile['tmp_name'])) {
                        
                        if (in_array($upfile['type'], $uptypes)) {
                            
                            $extName = strtolower(end(explode('.', $upfile ['name'])));
                            $filename = "XG".$uptype."-".$this->random(5); // 设置随机数长度
                            $extName=strtolower(end(explode('.', $upfile['name']))); // 取得扩展名
                            $name1=date('YmdHis').".".$extName;
                            $dir =BASE_DIR.XGUPLOADFILE ;
                            if (!file_exists($dir)) {
                                if (mkdir($dir, 0777)) {
                                    $dest =$dir."/".$filename.$name1;
                                    if (move_uploaded_file($upfile ['tmp_name'], $dest)) {
                                        $response['Success'] = 1;
                                        $response['Message'] = $this->gbkToUTF8('上传成功');
                                        $sheets = $this->readSheet($dest); //获取工作表列表
                                        $response['Result'] = $sheets;
                                        $response['File'] = $filename.$name1;
                                        $response['FileName'] = $upfile['name'];
                                        $response['sdate'] = $params['sdate'];
                                        $response['edate'] = $params['edate'];
                                    } else {
                                        $response['Success'] = 0;
                                        $response['Message'] = $this->gbkToUTF8('上传失败，目录权限不足');
                                    }
                                } else {
                                    $response['Success'] = 0;
                                    $response['Message'] = $this->gbkToUTF8('目录不存在，上传失败');
                                }
                            } else {
                                
                                $dest = $dir."/".$filename.$name1;
                               
                                if (move_uploaded_file($upfile ['tmp_name'], $dest)) {
                                    
                                    $response['Success'] = 1;
                                    $response['Message'] = '上传成功';
                                    //echo 2222;exit;
                                    $sheets = $this->readSheet($dest); //获取工作表列表
                                    $response['Result'] = $sheets;
                                    $response['File'] = $filename.$name1;
                                    $response['FileName'] = $upfile['name'];
                                    $response['sdate'] = $params['sdate'];
                                    $response['edate'] = $params['edate'];
                                } else {
                                    $response['Success'] = 0;
                                    $response['Message'] = $this->gbkToUTF8('上传失败，目录权限不足');
                                }
                            }
                        } else {
                            $response['Success'] = 0;
                            $response['Message'] = $this->gbkToUTF8('上传失败，检查文件是否是'.$filetype);
                        }
                    } else {
                        $response['Success'] = 0;
                        $response['Message'] = $this->gbkToUTF8('上传失败');
                        clearstatcache(); //清除文件缓存信息
                    }
                }
                else
                {
                    $response['Success'] = 0;
                    $response['Message'] = $this->gbkToUTF8('上传失败');
                }
            }
        }
        echo $this->pri_JSON($response);
        exit;
    }

    function sheetfile($params)
	{   
		$GUID = $params['GUID'];
		$mode = $params['mode'];//1=客户端，2=安卓，3=iOS
		$filename =$params['oldtmp'];
        $filefilename =$params['filename'];
        

		$response = array(
			'Success'  => 1,
			'Message'  => '',
		);
		if($GUID==""){
			$response["Success"] = 0;
			$response['Message'] =  $this->gbkToUTF8('GUID不能为空');
		}

		if($response["Success"])
		{   
			$user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
			if(empty($user_infos)){
				$response["Success"] = 0;
				$response['Message'] =  $this->gbkToUTF8('用户不存在');
			}
			$sheetList = explode(',', $params["sheet"]);
			$file = BASE_DIR.XGUPLOADFILE."/".$params["tmp"];
			if((empty($sheetList[0])&&$sheetList[0]!=0)||$params["sheet"]=='')
			{ 

				$response["Success"] = 0;
				$response['Message'] =  $this->gbkToUTF8('未选择工作表');
			}

			if($response["Success"])
			{
				//include '/usr/local/www/libs/PHPExcel/PHPExcel.php';
				$type = pathinfo($file); 
				$type = strtolower($type["extension"]);
				if ($type=='xlsx') { 
					$type='Excel2007'; 
				}elseif($type=='xls') { 
					$type = 'Excel5';
				}

              
                $fileType=\PhpOffice\PhpSpreadsheet\IOFactory::identify($file);//自动获取文件的类型提供给phpexcel用
                $objReader=\PhpOffice\PhpSpreadsheet\IOFactory::createReader($fileType);//获取文件读取操作对象
                $objPHPExcel=$objReader->load($file);//加载文件
                $objWorksheet = $objPHPExcel->getActiveSheet();
                $sheetCount = $objPHPExcel->getSheetCount();
                $sheets = $objPHPExcel->getSheetNames();
				$flag = true;
                
				foreach($sheetList as $sheet_index1)
				{
					$sheet_index = $sheetCount-1-$sheet_index1;
					$sheet = $objPHPExcel->getSheet($sheet_index);
					$rows = $sheet->getHighestRow();
					//获取当前工作表最大列数,返回的是最大的列名，如：B 
					$cols = $sheet->getHighestColumn();
					$sheet_v = $sheets[$sheet_index];
					$sheetname .= $sheet_v.",";
                    $listR=array();
                    $listC=array();
                    $data=array();
					$values="";
                    if($params['type']==1){
                    if($params['uptype']==1 || $params['uptype']==4){
                        for($l ='A'; $l <= $cols; $l++){
                            for($k = 1; $k <= $rows; $k++){
                                $key=$l.$k;
                                if($l =='A' && $k!=1){
                                    $listR[$k]=$sheet->getCell($key)->getValue();
                                }else if($k==1 && $l!='A'){
                                    $listC[$l]=$sheet->getCell($key)->getValue();
                                }else if($k!=1 && $l!='A'){
                                    $data[$l][$k]=$sheet->getCell($key)->getValue();
                                }
                            }
                        }
                        if(!empty($data)){
                            $this->_dao->execute("update data_table_base set isdel=1 where type=1 and isdel=0 and uptype='".$params['uptype']."' ");
                            $sql ="insert into data_table_base(type,uptype,dta_type,createtime,createuser,createusername) values(1,'".$params['uptype']."','".$GLOBALS["dta_type_arr"][$params['uptype']]."',now(),'".$user_infos['Uid']."','".$user_infos['TrueName']."')";
                            $this->_dao->execute($sql);
                            $baseid=$this->_dao->insert_id();
                            $basesql = "insert into data_table(baseid,dta1,dta2,dta6,dta_type,createtime,createuser) values";
                            foreach($data as $k=>$v){
                                foreach($v as $k2=>$v2){
                                    $dta_2= $listR[$k2];
                                    $dta_1= $listC[$k];
                                    $values.="('$baseid','$dta_1','$dta_2','$v2','".$GLOBALS["dta_type_arr"][$params['uptype']]."',NOW(),'".$user_infos['Uid']."'),";
                                }
                            }
                        }
                    }else if($params['uptype']==2 || $params['uptype']==5 || $params['uptype']==6){
                        for($l ='A'; $l <= $cols; $l++){
                            for($k = 1; $k <= $rows; $k++){
                                $key=$l.$k;
                                if($l =='A' && $k>3){
                                    $listR[$k]=$sheet->getCell($key)->getValue();
                                }else if($k<=3 && $l!='A'){
                                    $value=$sheet->getCell($key)->getValue();
                                    if($value=="" && $k<=2){
                                        $vv = ord($l)-1;
                                        $vv=chr($vv);
                                        $value=$listC[$k][$vv];
                                    }
                                    $listC[$k][$l]=$value;
                                }else if($k>3 && $l!='A'){
                                    $data[$l][$k]=$sheet->getCell($key)->getValue();
                                }
                            }
                        }
                        if(!empty($data)){
                            $this->_dao->execute("update data_table_base set isdel=1 where type=1 and isdel=0 and uptype='".$params['uptype']."' ");
                            $sql ="insert into data_table_base(type,uptype,dta_type,createtime,createuser,createusername) values(1,'".$params['uptype']."','".$GLOBALS["dta_type_arr"][$params['uptype']]."',now(),'".$user_infos['Uid']."','".$user_infos['TrueName']."')";
                            $this->_dao->execute($sql);
                            $baseid=$this->_dao->insert_id();
                            $basesql = "insert into data_table(baseid,dta1,dta2,dta3,dta4,dta6,dta_type,createtime,createuser) values";
                            foreach($data as $k=>$v){
                                foreach($v as $k2=>$v2){
                                    $dta_4= $listR[$k2];
                                    $dta_1= $listC[1][$k];
                                    $dta_2= $listC[2][$k];
                                    $dta_3= $listC[3][$k];
                                    $values.="('$baseid','$dta_1','$dta_2','$dta_3','$dta_4','$v2','".$GLOBALS["dta_type_arr"][$params['uptype']]."',NOW(),'".$user_infos['Uid']."'),";
                                }
                            }
                        }
                    }else if($params['uptype']==3 || $params['uptype']==8 || $params['uptype']==10){
                        $this->_dao->execute("update data_table_base set isdel=1 where type=1 and isdel=0 and uptype='".$params['uptype']."' ");
                        $sql ="insert into data_table_base(type,uptype,dta_type,createtime,createuser,createusername) values(1,'".$params['uptype']."','".$GLOBALS["dta_type_arr"][$params['uptype']]."',now(),'".$user_infos['Uid']."','".$user_infos['TrueName']."')";
                        $this->_dao->execute($sql);
                        $baseid=$this->_dao->insert_id();
                        $basesql = "insert into data_table(baseid,dta1,dta2,dta3,dta4,dta6,dta_type,createtime,createuser) values";
                        for($k = 2; $k <= $rows; $k++){
                            $dta_1=$sheet->getCell("A".$k)->getValue()!=""?$sheet->getCell("A".$k)->getValue():$listA[$k-1];
                            $dta_2=$sheet->getCell("B".$k)->getValue()!=""?$sheet->getCell("B".$k)->getValue():$listB[$k-1];
                            $dta_3=$sheet->getCell("D".$k)->getValue()!=""?$sheet->getCell("D".$k)->getValue():$listD[$k-1];
                            $dta_4=$sheet->getCell("E".$k)->getValue()!=""?$sheet->getCell("E".$k)->getValue():$listE[$k-1];
                            $dta_6=$sheet->getCell("C".$k)->getValue();
                            $values.="('$baseid','$dta_1','$dta_2','$dta_3','$dta_4','$dta_6','".$GLOBALS["dta_type_arr"][$params['uptype']]."',NOW(),'".$user_infos['Uid']."'),";
                            $listA[$k]=$dta_1;
                            $listB[$k]=$dta_2;
                            $listD[$k]=$dta_3;
                            $listE[$k]=$dta_4;
                        }
                    }else if($params['uptype']==7 && $params['type']==1){
                        $this->_dao->execute("update data_table_base set isdel=1 where type=1 and isdel=0 and uptype='".$params['uptype']."' ");
                        $sql ="insert into data_table_base(type,uptype,dta_type,createtime,createuser,createusername) values(1,'".$params['uptype']."','".$GLOBALS["dta_type_arr"][$params['uptype']]."',now(),'".$user_infos['Uid']."','".$user_infos['TrueName']."')";
                        $this->_dao->execute($sql);
                        $baseid=$this->_dao->insert_id();
                        $basesql = "insert into data_table(baseid,dta1,dta2,dta6,dta_type,createtime,createuser) values";
                        for($k = 2; $k <= $rows; $k++){
                            $dta_1=$sheet->getCell("A".$k)->getValue()!=""?$sheet->getCell("A".$k)->getValue():$listA[$k-1];
                            $dta_2=$sheet->getCell("B".$k)->getValue()!=""?$sheet->getCell("B".$k)->getValue():$listB[$k-1];
                            $dta_6=$sheet->getCell("C".$k)->getValue();
                            $values.="('$baseid','$dta_1','$dta_2','$dta_6','".$GLOBALS["dta_type_arr"][$params['uptype']]."',NOW(),'".$user_infos['Uid']."'),";
                            $listA[$k]=$dta_1;
                            $listB[$k]=$dta_2;
                        }
                    }else if($params['uptype']==7 && $params['type']==2){
                        $this->_dao->execute("update data_table_base set isdel=1 where type=2 and isdel=0 and uptype='".$params['uptype']."' and sdate='".$params['sdate']."' and edate='".$params['edate']."' ");
                        $sql ="insert into data_table_base(type,uptype,dta_type,sdate,edate,createtime,createuser,createusername) values(2,'".$params['uptype']."','".$GLOBALS["dta_type_arr"][$params['uptype']]."','".$params['sdate']."','".$params['edate']."',now(),'".$user_infos['Uid']."','".$user_infos['TrueName']."')";
                        $this->_dao->execute($sql);
                        $baseid=$this->_dao->insert_id();
                        $basesql = "insert into data_table(baseid,dta1,dta2,dta6,dta_type,createtime,createuser) values";
                        for($k = 2; $k <= $rows; $k++){
                            $dta_1=$sheet->getCell("A".$k)->getValue()!=""?$sheet->getCell("A".$k)->getValue():$listA[$k-1];
                            $dta_2=$sheet->getCell("B".$k)->getValue()!=""?$sheet->getCell("B".$k)->getValue():$listB[$k-1];
                            $dta_6=$sheet->getCell("C".$k)->getValue();
                            $values.="('$baseid','$dta_1','$dta_2','$dta_6','".$GLOBALS["dta_type_arr"][$params['uptype']]."',NOW(),'".$user_infos['Uid']."'),";
                            $listA[$k]=$dta_1;
                            $listB[$k]=$dta_2;
                        }
                    }else if($params['uptype']==9){
                        $this->_dao->execute("update data_table_base set isdel=1 where type=1 and isdel=0 and uptype='".$params['uptype']."' ");
                        $sql ="insert into data_table_base(type,uptype,dta_type,createtime,createuser,createusername) values(1,'".$params['uptype']."','".$GLOBALS["dta_type_arr"][$params['uptype']]."',now(),'".$user_infos['Uid']."','".$user_infos['TrueName']."')";
                        $this->_dao->execute($sql);
                        $baseid=$this->_dao->insert_id();
                        $basesql = "insert into data_table(baseid,dta1,dta2,dta3,dta4,dta6,dta_type,createtime,createuser) values";
                        for($k = 2; $k <= $rows; $k++){
                            $dta_1=$sheet->getCell("A".$k)->getValue()!=""?$sheet->getCell("A".$k)->getValue():$listA[$k-1];
                            $dta_2=$sheet->getCell("B".$k)->getValue()!=""?$sheet->getCell("B".$k)->getValue():$listB[$k-1];
                            $dta_3=$sheet->getCell("C".$k)->getValue()!=""?$sheet->getCell("C".$k)->getValue():$listC[$k-1];
                            $dta_4=$sheet->getCell("E".$k)->getValue()!=""?$sheet->getCell("E".$k)->getValue():$listE[$k-1];
                            $dta_6=$sheet->getCell("D".$k)->getValue();
                            $values.="('$baseid','$dta_1','$dta_2','$dta_3','$dta_4','$dta_6','".$GLOBALS["dta_type_arr"][$params['uptype']]."',NOW(),'".$user_infos['Uid']."'),";
                            $listA[$k]=$dta_1;
                            $listB[$k]=$dta_2;
                            $listC[$k]=$dta_3;
                            $listE[$k]=$dta_4;
                        }
                    }
                    }else if($params['uptype']==7 && $params['type']==2){
                        $this->_dao->execute("update data_table_base set isdel=1 where type=2 and isdel=0 and uptype='".$params['uptype']."' and sdate='".$params['sdate']."' and edate='".$params['edate']."' ");
                        $sql ="insert into data_table_base(type,uptype,dta_type,sdate,edate,createtime,createuser,createusername) values(2,'".$params['uptype']."','".$GLOBALS["dta_type_arr"][$params['uptype']]."','".$params['sdate']."','".$params['edate']."',now(),'".$user_infos['Uid']."','".$user_infos['TrueName']."')";
                        $this->_dao->execute($sql);
                        $baseid=$this->_dao->insert_id();
                        $basesql = "insert into data_table(baseid,dta1,dta2,dta6,dta_type,createtime,createuser) values";
                        for($k = 2; $k <= $rows; $k++){
                            $dta_1=$sheet->getCell("A".$k)->getValue()!=""?$sheet->getCell("A".$k)->getValue():$listA[$k-1];
                            $dta_2=$sheet->getCell("B".$k)->getValue()!=""?$sheet->getCell("B".$k)->getValue():$listB[$k-1];
                            $dta_6=$sheet->getCell("C".$k)->getValue();
                            $values.="('$baseid','$dta_1','$dta_2','$dta_6','".$GLOBALS["dta_type_arr"][$params['uptype']]."',NOW(),'".$user_infos['Uid']."'),";
                            $listA[$k]=$dta_1;
                            $listB[$k]=$dta_2;
                        }
                    }else if($params['type']==7){
                        $date=date("Y-m-01",strtotime($this->excelTime($sheet->getCell("B2")->getValue())));
                        $this->_dao->execute("update data_table_base set isdel=1 where type=7 and isdel=0 and sdate='".$date."' ");
                        $sql ="insert into data_table_base(type,dta_type,sdate,createtime,createuser,createusername) values(7,'YRLZBHZB','".$date."',now(),'".$user_infos['Uid']."','".$user_infos['TrueName']."')";
                        $this->_dao->execute($sql);
                        $baseid=$this->_dao->insert_id();
                        $basesql = "insert into data_table(baseid,dta1,dta2,dta3,dta4,dta5,dta6,dta7,dta8,dta_type,createtime,createuser) values";
                        $data=array();
                        for($k = 1; $k <= $rows; $k++){
                            $dta_1=$sheet->getCell("A".$k)->getValue()!=""?$sheet->getCell("A".$k)->getValue():$listA[$k-1];
                            $dta_2=$sheet->getCell("B".$k)->getValue()!=""? $this->excelTime($sheet->getCell("B".$k)->getValue()):$listB[$k-1];
                            $dta_3=$sheet->getCell("C".$k)->getValue()!=""? $this->excelTime($sheet->getCell("C".$k)->getValue()):$listC[$k-1];
                            $dta_4=$sheet->getCell("D".$k)->getValue();
                            $dta_5=$sheet->getCell("E".$k)->getValue();
                            $dta_6=$sheet->getCell("F".$k)->getValue();
                            $dta_7=$sheet->getCell("G".$k)->getValue();
                            $dta_8=$sheet->getCell("H".$k)->getValue();
                            if($dta_4!=""){
                                $listA[$k]=$dta_1;
                                $listB[$k]=$dta_2;
                                $listC[$k]=$dta_3;
                                $values.="('$baseid','$dta_1','$dta_2','$dta_3','$dta_4','$dta_5','$dta_6','$dta_7','$dta_8','YRLZBHZB',NOW(),'".$user_infos['Uid']."'),";
                            }
                        }
                    }else if($params['type']==9){
                        if (strpos($sheet_v, "年") !== false || strpos($sheet_v, "月") !== false || strpos($sheet_v, ".") !== false) {
                            $date_yn=str_replace(".","年",$sheet_v);
                            $date_yn=str_replace("月","",$date_yn);
                            $date_yn=str_replace("年","-",$date_yn);
                        }else{
                            if (strpos($filefilename, "年") !== false || strpos($filefilename, "月") !== false) {
                                // 找到目标字符的位置
                               
                                $month=0;
                                $year=date("Y");
                                $positiony = strpos($filefilename, "年");
                                if ($positiony !== false) { // 确保目标字符存在于字符串中
                                    // 从字符串开始到目标字符前面的部分
                                    $beforeCharPart = substr($filefilename, 0, $positiony);
                                    // 使用正则表达式提取数字部分（假设数字只出现在目标字符前面）
                                    if (preg_match('/(\d+)/', $beforeCharPart, $matches)) {
                                        $year = $matches[1]; // 获取匹配的数字
                                        $filefilename=str_replace($year,"",$filefilename);
                                    } 
                                }
                                $position = strpos($filefilename, "月");
                                if ($position !== false) { // 确保目标字符存在于字符串中
                                    // 从字符串开始到目标字符前面的部分
                                    $beforeCharPart = substr($filefilename, 0, $position);
                                    // 使用正则表达式提取数字部分（假设数字只出现在目标字符前面）
                                    if (preg_match('/(\d+)/', $beforeCharPart, $matches)) {
                                        $month = $matches[1]; // 获取匹配的数字
                                    } 
                                }
                                $date_yn=$year."-".$month;
                            }
                        }
                        
                        //echo $date_yn;
                        $date_yn=date("Y-m-01",strtotime($date_yn));
                        //echo $date_yn;
                        for($k = 1; $k <= $rows; $k++){
                            $arr['A'] = $sheet->getCell("A" . $k)->getValue();
                            $arr['B'] = $sheet->getCell("B" . $k)->getValue();
                            $arr['C'] = $sheet->getCell("C" . $k)->getValue();
                            $arr['D'] = $sheet->getCell("D" . $k)->getValue();
                            $arr['E'] = $sheet->getCell("E" . $k)->getValue();
                            $arr['F'] = $sheet->getCell("F" . $k)->getCalculatedValue();
                            $arr['G'] = $sheet->getCell("G" . $k)->getValue();
                            $arr['H'] = $sheet->getCell("H" . $k)->getValue();
                            $arr['I'] = $sheet->getCell("I" . $k)->getFormattedValue();
                            $arr['J'] = $sheet->getCell("J" . $k)->getFormattedValue();
                            $arr['K'] = $sheet->getCell("K" . $k)->getFormattedValue();
                            $arr['L'] = $sheet->getCell("L" . $k)->getFormattedValue();
                            $arr['M'] = $sheet->getCell("M" . $k)->getFormattedValue();
                            $arr['N'] = $sheet->getCell("N" . $k)->getFormattedValue();
                            $arr['O'] = $sheet->getCell("O" . $k)->getFormattedValue();
                            $arr['P'] = $sheet->getCell("P" . $k)->getFormattedValue();
                            $arr['Q'] = $sheet->getCell("Q" . $k)->getFormattedValue();
                            $arr['R'] = $sheet->getCell("R" . $k)->getFormattedValue();
                            $arr['S']="";
                            if($k==1){
                                $arr['S']="完成档次";
                            }
                            if($k>2){
                                $data_end=empty($data) ? $arr : end($data);
                                if($arr['A']==""){
                                    $arr['A']=$data_end['A'];
                                }
                                if($arr['B']==""){
                                    $arr['B']=$data_end['B'];
                                }

                                if($arr['C']=="" && $arr['B']!="小计"){
                                    $arr['C']=$data_end['C'];
                                }

                                // if($arr['D']=="" && $arr['B']!="小计"){
                                //     $arr['D']=$data_end['C'];
                                // }

                                if($arr['F']=="" && $arr['B']!="小计"){
                                    $arr['F']=$data_end['F'];
                                }

                                if($arr['H']=="" && $arr['E']!=""){
                                    $arr['H']=$data_end['H'];
                                }

                                if($arr['I']!='' && (is_float($arr['I']) || is_numeric($arr['I']))){
                                    $arr['I']=round($arr['I'],2);
                                }
                                if($arr['J']!='' && (is_float($arr['J']) || is_numeric($arr['J']))){
                                    $arr['J']=round($arr['J'],2);
                                }
                                if($arr['K']!='' && (is_float($arr['K']) || is_numeric($arr['K']))){
                                    $arr['K']=round($arr['K'],2);
                                }
                                if($arr['L']!='' && (is_float($arr['L']) || is_numeric($arr['L']))){
                                    $arr['L']=round($arr['L'],2);
                                }

                                if($arr['M']!='' && (is_float($arr['M']) || is_numeric($arr['M']))){
                                    $arr['M']=round($arr['M'],2);
                                }
                                if($arr['N']!='' && (is_float($arr['N']) || is_numeric($arr['N']))){
                                    $arr['N']=round($arr['N'],2);
                                }
                                if($arr['O']!='' && (is_float($arr['O']) || is_numeric($arr['O']))){
                                    $arr['O']=round($arr['O'],2);
                                }
                                if($arr['P']!='' && (is_float($arr['P']) || is_numeric($arr['P']))){
                                    $arr['P']=round($arr['P'],2);
                                }
                                if($arr['Q']!='' && (is_float($arr['Q']) || is_numeric($arr['Q']))){
                                    $arr['Q']=round($arr['Q'],2);
                                }
                                if($arr['R']!='' && (is_float($arr['R']) || is_numeric($arr['R']))){
                                    $arr['R']=round($arr['R'],2);
                                }


                                $arr['E'] = str_replace("\\","\\\\",$arr['E']);

                                if($arr['N']!=''&&  trim($arr['N'])!='-' && $arr['N']<0){
                                    $arr['S']="未完成基准";
                                }else if($arr['N']!='' && $arr['O']!='' && ($arr['N']>=0 || trim($arr['N'])=='-') && $arr['O']<0){
                                    $arr['S']="完成基准";
                                }else if($arr['N']!='' && $arr['O']!='' && $arr['P']!='' && ($arr['N']>=0 || trim($arr['N'])=='-') && ($arr['O']>=0 || trim($arr['O'])=='-') && $arr['P']<0){
                                    $arr['S']="完成目标";
                                }else if($arr['N']!='' && $arr['O']!='' && $arr['P']!='' && ($arr['N']>=0 || trim($arr['N'])=='-') && ($arr['O']>=0 || trim($arr['O'])=='-') && ($arr['P']>=0 || trim($arr['P'])=='-')){
                                    $arr['S']="完成挑战";
                                }else if($arr['N']=='' && ($arr['O']>=0 || trim($arr['O'])=='-') && $arr['P']==''){
                                    $arr['S']="完成目标";
                                }


                            }

                            $data[] = $arr;

                            
                        }
                        
                        if(!empty($data) && $date_yn!=""){
                            $dta_type = "XG_EJDWJXPJJG";
                            $this->_dao->execute("update data_table_base set isdel=1 where type=9 and isdel=0 and uptype='".$params['uptype']."' and sdate='".$date_yn."' ");
                            $sql ="insert into data_table_base(sdate,type,uptype,dta_type,createtime,createuser,createusername) values('".$date_yn."',9,'".$params['uptype']."','".$dta_type."',now(),'".$user_infos['Uid']."','".$user_infos['TrueName']."')";
                            $this->_dao->execute($sql);
                            $baseid=$this->_dao->insert_id();
                            $basesql = "insert into data_table(baseid,dta1,dta2,dta3,dta4,dta5,dta6,dta7,dta8,dta9,dta10,dta11,dta12,dta13,dta14,dta15,dta16,dta17,dta18,dta19,dta_type,createtime,createuser) values";
                            foreach($data as $k=>$v){
                                $values.="('$baseid','".$v['A']."','".$v['B']."','".$v['C']."','".$v['D']."','".$v['E']."','".$v['F']."','".$v['G']."','".$v['H']."','".$v['I']."','".$v['J']."','".$v['K']."','".$v['L']."','".$v['M']."','".$v['N']."','".$v['O']."','".$v['P']."','".$v['Q']."','".$v['R']."','".$v['S']."','".$dta_type."',NOW(),'".$user_infos['Uid']."'),";
                            }
                        }
                    }else if($params['type']==8){
                        $dateList=array();
                        $list1=array();
                        $list2=array();
                        for($l ='A'; $l <= $cols; $l++){
                            for($k = 1; $k <= $rows; $k++){
                                $key=$l.$k;
                                if($k==1 && $l!='A'){
                                    if($sheet->getCell($key)->getValue()!=""){
                                        $list1[]=$sheet->getCell($key)->getValue();
                                    }else{
                                        $list1[]=$list1[count($list1)-1];
                                    }
                                    
                                }
                                else if($k==2 && $l!='A'){
                                    if($sheet->getCell($key)->getValue()!=""){
                                        $list2[]=$sheet->getCell($key)->getValue();
                                    }else{
                                        $list2[]=$list2[count($list2)-1];
                                    }
                                }else if($k>=3 && $l=='A'){
                                    $dateindex = $sheet->getCell($key)->getValue();
                                    $dateindex=str_replace("/","-",$dateindex);
                                    $dateindex=str_replace("年","-",$dateindex);
                                    $dateindex=str_replace("月","-",$dateindex);
                                    if ($dateindex!="" && strpos($dateindex, "-") === false) {
                                        $toTimestamp = \PhpOffice\PhpSpreadsheet\Shared\Date::excelToTimestamp($dateindex);
                                        $dateindex = date("Y-m-d", $toTimestamp );
                                    }
                                    $dateList[$k]=$dateindex;
                                }else if($k>=3 && $l!='A'){
                                    $data[$k][]=$sheet->getCell($key)->getValue();
                                }
                                
                            }
                        }
                        
                        $basesql = "insert into sd_steel_data_table(baseid,dta1,dta2,dta3,dta4,createtime,createuser) values";
                        foreach($data as $k=>$v){
                            $dta1=$GLOBALS["sg_steel_upload_title"][$params['uptype']];
                            $date=$dateList[$k];
                            if($date!=""){
                                $this->drcwdao->execute("update sd_steel_data_table_base set isdel=1 where isdel=0 and type='".$params['uptype']."'  and bigtype='".$params['bigtype']."' and date='".$date."' ");
                                $sql ="insert into sd_steel_data_table_base(bigtype,type,date,createtime,createuser,createusername) values('".$params['bigtype']."','".$params['uptype']."','".$date."',now(),'".$user_infos['Uid']."','".$user_infos['TrueName']."')";
                                $this->drcwdao->execute($sql);
                                $baseid=$this->drcwdao->insert_id();
                                foreach($v as $kk=>$dta4){
                                    $dta2=$list1[$kk];
                                    $dta3=$list2[$kk];
                                    $values.="('$baseid','".$dta1."','".$dta2."','".$dta3."','".$dta4."',NOW(),'".$user_infos['Uid']."'),";
                                }
                            }
                            
                            
                        }   
                    }else if($params['type']==10){
                        $title=$sheet->getCell("B2")->getFormattedValue();
                        if (strpos($title, "日") !== false || strpos($title, "月") !== false) {
                            // 找到目标字符的位置
                           
                            $month=date("m");
                            $day=date("d");
                            
                            $position = strpos($title, "月");
                            if ($position !== false) { // 确保目标字符存在于字符串中
                                // 从字符串开始到目标字符前面的部分
                                $beforeCharPart = substr($title, 0, $position);
                                // 使用正则表达式提取数字部分（假设数字只出现在目标字符前面）
                                if (preg_match('/(\d+)/', $beforeCharPart, $matches)) {
                                    $month = $matches[1]; // 获取匹配的数字
                                    if($month<10){
                                        $month="0".$month;
                                    }
                                } 
                            }
                            $positiony = strpos($title, "日");
                            if ($positiony !== false) { // 确保目标字符存在于字符串中
                                // 从字符串开始到目标字符前面的部分
                                $beforeCharPart = substr($title, $position, $positiony);
                                // 使用正则表达式提取数字部分（假设数字只出现在目标字符前面）
                                if (preg_match('/(\d+)/', $beforeCharPart, $matches)) {
                                    $day = $matches[1]; // 获取匹配的数字
                                    if($day<10){
                                        $day="0".$day;
                                    }
                                    $title=str_replace($day,"",$title);
                                } 
                            }
                            
                            $year=date("Y");
                            $nowmonth=date("m");
                            if($nowmonth=="01" && $month=="12"){
                                $year=date("Y")-1;
                            }
                            $date_yn= $year."-".$month."-".$day;
                        }
                        $date=date("Y-m-d",strtotime($params['date']));
                        if($params['date']!=$date_yn){
                            $date=date("Y-m-d",strtotime($date_yn));
                        }

                        $this->drcwdao->execute("update sd_steel_data_table_base set isdel=1 where isdel=0 and type='".$params['uptype']."'  and bigtype='".$params['bigtype']."' and date='".$date."' ");
                        $sql ="insert into sd_steel_data_table_base(bigtype,type,date,filepath,filename,sheet,createtime,createuser,createusername) values('".$params['bigtype']."','".$params['uptype']."','".$date."','".$file."','".$filefilename."','".$sheet_v."',now(),'".$user_infos['Uid']."','".$user_infos['TrueName']."')";
                        $this->drcwdao->execute($sql);
                        $baseid=$this->drcwdao->insert_id();

                        $basesql = "insert into sd_steel_data_table(baseid,dta1,dta2,dta3,dta4,dta5,dta6,dta7,createtime,createuser) values";

                        for($k = 6; $k <= $rows; $k++){
                            $dta1=$sheet->getCell("A".$k)->getValue();
                            $dta2=$sheet->getCell("B".$k)->getFormattedValue();
                            $dta3=$sheet->getCell("C".$k)->getFormattedValue();
                            $dta4=$sheet->getCell("D".$k)->getFormattedValue();
                            $dta5=$sheet->getCell("E".$k)->getFormattedValue();
                            $dta6=$sheet->getCell("F".$k)->getFormattedValue();
                            $dta7=$sheet->getCell("G".$k)->getFormattedValue();
                            $values.="('$baseid','".trim($dta1)."','".trim($dta2)."','".trim($dta3)."','".trim($dta4)."','".trim($dta5)."','".trim($dta6)."','".trim($dta7)."',NOW(),'".$user_infos['Uid']."'),";
                        }

                    }


                    if(!empty($values))
                    {
                        if($params['type']==8 || $params['type']==10){
                            $tsql = substr($values, 0, -1);
                            $this->drcwdao->execute($basesql.$tsql);
                        }else{
                            $tsql = substr($values, 0, -1);
                            $this->_dao->execute($basesql.$tsql);
    
                            if($params['uptype']==7 && $params['type']==2){
                                $this->set_table_data_price($params['type'],$params['uptype'],$params['sdate'],$params['edate'],$user_infos);
                            }
                        }
                        
                    }else{
                        $error_sheet2[] = $sheet_v;
					    $flag = false;
                    }
				}
				if(!$flag)
				{
					$error2 = implode(',', $error_sheet2);
					if(!empty($error2))
					{
						$msg2 = "工作表：".$error2."出现未知错误，导入失败";
					}
					$response["Success"] = 0;
					$response['Message'] =  $this->gbkToUTF8($msg1."\\n".$msg2);
				}else{
					$response["Success"] = 1;
					$response['Message'] = $this->gbkToUTF8('导入成功');
				}
			}
		}
		echo $this->pri_JSON($response);
		exit;
	}


    public function excelTime($date, $time = false) {
        if(is_numeric($date)){
            if(function_exists('GregorianToJD')){
                if (is_numeric( $date )) {
                    $jd = GregorianToJD( 1, 1, 1970 );
                    $gregorian = JDToGregorian( $jd + intval ( $date ) - 25569 );
                    $date = explode( '/', $gregorian );
                    $date_str = str_pad( $date [2], 4, '0', STR_PAD_LEFT )
    
                        ."-". str_pad( $date [0], 2, '0', STR_PAD_LEFT )
    
                        ."-". str_pad( $date [1], 2, '0', STR_PAD_LEFT )
    
                        . ($time ? " 00:00:00" : '');
    
                    return $date_str;
                }
            }else{
                $date=date('Y-m-d', ($date- 25569) * 24 * 3600);
            }
        }
        return $date;
    }


    public function price_table($params){
        $GUID = $params['GUID'];
        $user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
        if(empty($user_infos)){
            alert($this->utfToGBK('用户不存在'));
            exit;
        }
        $date=$params['date']!=""?$params['date']:date("Y-m-d");
        $type=$params['type']!=""?$params['type']:1;
        $last_date=$this->get_xhdate($date);
        $priceid6[1]=array("253012","253022");
        $priceid6[2]=array();
        $priceid7[1]=array("0730721","363012a","3630323","3630322","2830322","2830321","3630521","3630524","5330522","5330524","2830522","2830521","9430121","9430123","2530123","353012h","3530121","3630128","533012p","2830122","2830123","9430222","2530221","3530221","3530222","5330224","3530721","3630722","5330821","2830721","0730321","0730521","0730525");
        $priceid7[2]=array("9413121","9413122","2511031","3513136","3513131","2813125","9411033","9411031","3511062","2811033","9420231","9420232","252043f","2520433","3520435","352023j","2820231","9420251","9420252","2520452","3520455","3520454","2820242","9420221","9420222","2520422","3520425","352042m","2820226","9420261","9420262","2520463","2520462","3520465","3520461","2820462","9420231","9420232","252043f","2520433","3520435","352023j","2820231");
        $priceids6=implode("','",$priceid6[$type]); 
        $priceids7=implode("','",$priceid7[$type]); 
        $allprice=$this->maindao->get_marketconditions_price($priceids6,$priceids7,$date);
        //echo '<pre>';
        //print_r($allprice);

        $thisTable= array(
            "1"=>array(
                "船板"=>array(
                    "一般（CSSB14-20mm）"=>array("","","","","","","3630323","3630322","0730321","","","","","","2830322","2830321"),
                    "高强(同上）"=>array("","","","","","","","","","","","","","","",""),
                    "小计"=>array("","","","","","","","","","","","","","","",""),
                ),
                "锅炉容器板"=>array(
                    "一般（Q235R-20mm）"=>array("","","","","","","","","","","","","","","",""),
                    "高等级"=>array("","","","","","","3630521","3630524","0730521","0730525","","","5330522","5330524","2830522","2830521"),
                    "小计"=>array("","","","","","","","","","","","","","","",""),
                ),
                "普碳板和低合金结构板"=>array(
                    "Q235系列（含国外牌号）"=>array("9430121","9430123","2530123","253012","353012h","3530121","3630128","363012a","","","","","533012p","","2830122","2830123"),
                    "Q345系列普锰板（16-25MM）"=>array("9430222","","2530221","253022","3530221","3530222","","","","","","","5330224","","",""),
                    "低合金结构钢（含Q345CDE和高强钢钢）"=>array("","","","","","","","","","","","","","","",""),
                    "小计"=>array("","","","","","","","","","","","","","","",""),
                ),
                "工程和机械用钢"=>array(
                    "桥梁钢/高建钢(Q345QD 20mm)"=>array("","","","","","3530721","","3630722","","0730721","","","","5330821","","2830721"),
                    "耐磨钢"=>array("","","","","","","","","","","","","","","",""),
                    "模具钢"=>array("","","","","","","","","","","","","","","",""),
                    "优碳钢（含易切削钢）"=>array("","","","","","","","","","","","","","","",""),
                    "其它(耐候、管线、车轮、合金结构钢等)"=>array("","","","","","","","","","","","","","","",""),
                    "小计"=>array("","","","","","","","","","","","","","","",""),
                ),
                "中厚板合计"=>array(
                    "总计"=>array("","","","","","","","","","","","","","","","")
                ),
            ),
            "2"=>array(
                "0"=>array("高线","高线","盘螺HRB400 8-10","9413121","9413122","","2511031","3513136","3513131","","","","2813125"),
                "1"=>array("高线","高线","普线HPB300 8-10","9411033","9411031","","","","3511062","","","","2811033"),
                "2"=>array("高线","硬线","号钢(含65MN）","","","","","","","","","",""),
                "3"=>array("高线","硬线","号钢(含65MN）","","","","","","","","","",""),
                "4"=>array("高棒","螺纹","HRB400：18-25mm","9420231","9420232","252043f","2520433","3520435","352023j","","","","2820231"),
                "5"=>array("高棒","螺纹","HRB400  32mm","9420251","9420252","","2520452","3520455","3520454","","","","2820242"),
                "6"=>array("棒二","螺纹","HRB400：12","9420221","9420222","","2520422","3520425","352042m","","","","2820226"),
                "7"=>array("棒二","螺纹","HRB400：16mm","9420261","9420262","2520463","2520462","3520465","3520461","","","","2820462"),
                "8"=>array("棒二","螺纹","HRB400：18-25mm","9420231","9420232","252043f","2520433","3520435","352023j","","","","2820231")
            )
        );
        $table="";
        $xiaoj=array();
        $heji=array();
        if($type==1){
            foreach($thisTable[1] as $key=>$val){
                $table.="<tr><td rowspan='".count($val)."'>".$key."</td>";
                foreach($val as $key2=>$val2){
                    $count[$key2]['count']=0;
                    $table.="<td>".$key2."</td>";
                    foreach($val2 as $key3=>$val3){
                        if($allprice[$val3]!=""){
                            $xiaoj[$key][$key3][]=$allprice[$val3];
                            $heji[$key3][]=$allprice[$val3];
                        }
                        if($key2=="小计"){
                            $xj=isset($xiaoj[$key][$key3])?round(array_sum($xiaoj[$key][$key3])/count($xiaoj[$key][$key3]),2):'';
                            $table.="<td>".$xj."</td>";
                        }else if($key2=='总计'){
                            $zj=isset($heji[$key3])?round(array_sum($heji[$key3])/count($heji[$key3]),0):'';
                            $table.="<td>".$zj."</td>";
                        }else{
                            $table.="<td>".$allprice[$val3]."</td>";
                        }
                    }
                    $table.="</tr>";
                }
            }

        }else{
            foreach($thisTable[2] as $key=>$val){
                $table.="<tr>";
                foreach($val as $key2=>$val2){
                    $td=$key2>2?$allprice[$val2]:$val2;
                    $table.="<td>".$td."</td>";
                }
                $table.="</tr>";
            }
        }

        $this->assign('GUID',$GUID);
        $this->assign('type',$type);
        $this->assign('date',$date);
        $this->assign('table',$table);
        $this->assign('price6',$price6);
        $this->assign('price7',$price7);
    }


    public function price_echart($params){
        $type=$params['type']!=""?$params['type']:1;
        $this->assign('type',$type);
        $this->assign('date',$params['date']!=""?$params['date']:date("Y-m-d"));
    }


    public function getEchartData($params)//根据当前日获取上个钢之家工作日
    {
        $date=$params['date']!=""?$params['date']:date("Y-m-d");
        $type=$params['type']!=""?$params['type']:1;
        $last_date=$this->get_xhdate($date);
       
        $ThisChart=array(
            "1"=>array(
                "0"=>array(
                    "priceid"=>array("9430121","2530123","353012h","3630128","533012p","2830122"),
                    "xdata"=>array("新余","南昌","长沙","武汉","成都","广州"),
                    "title"=>array("中厚板价格变动")
                )
            ),
            "2"=>array(
                "0"=>array(
                    "priceid"=>array("9413121","3513136"),
                    "xdata"=>array("新余","长沙"),
                    "title"=>array("高线价格变动")
                ),
                "1"=>array(
                    "priceid"=>array("9420231","252043f","3520435"),
                    "xdata"=>array("新余","南昌","长沙"),
                    "title"=>array("高棒价格变动")
                ),
            )
        );

        $newdata_chart=array();
        $newdata_xdata=array();
        $newdata_chart_all=array();
        foreach($ThisChart[$type] as $key=>$val){
            $priceids7_chart=implode("','",$val['priceid']);
            $all_price=$this->maindao->get_marketconditions_price("",$priceids7_chart,$date); 
            $all_price_last=$this->maindao->get_marketconditions_price("",$priceids7_chart,$last_date);
            foreach($val['priceid'] as $k=>$v)
            {
                $newdata_chart[$key][1][]=$all_price[$v]-$all_price_last[$v];
                $newdata_chart[$key][2][]=round(($all_price[$v]-$all_price_last[$v])/$all_price_last[$v]*100,2);
            }
        }
        foreach($newdata_chart as $key => $val){
            foreach($val as $k => $v){
                $newdata_chart_all[$key][$k]=$v;
            }
            $xdata[$key]=$ThisChart[$type][$key]['xdata'];
            $title[$key]=$ThisChart[$type][$key]['title'];
        }

        $retdata = array("legend" => $xdata, "title" => $title,  "min" => "", "max" => "");
        $retdata['data'] = $newdata_chart_all;
        //$jsondata = $this->convert_to_utf8($retdata);

        echo json_encode($retdata);
        
    }


    public function downExcel($params){
        $GUID=$params['GUID'];
        $uptype=$params['uptype'];
        
        $base_data=$this->_dao->getRow("select id,sdate,edate from data_table_base  where (type='2' or type='1') and uptype='$uptype' and isdel=0 order by id desc limit 1");
        $baseid=$base_data['id'];
        $data=$this->_dao->query("select * from data_table where baseid='$baseid' order by id asc ");
        $newdata=array();
        if($uptype==7){
            foreach($data as $key=>$val){
                $header[$val['dta1']][$val['dta2']]['id']=$val['id'];
                $header[$val['dta1']][$val['dta2']]['data']=$val['dta2'];
                $header[$val['dta1']][$val['dta2']]['price']=$val['dta6'];
                $header[$val['dta1']][$val['dta2']]['remark']=$val['dta3'];
                $header[$val['dta1']][$val['dta2']]['remark2']=$val['dta4'];
            }
            foreach($header as $key=>$val){
                foreach($val as $key2=>$val2){
                    $newdata[$key][]=$val2;
                }
                $newdata[$key][0]['row']=count($val);
            }
    
        }
        $objPHPExcel = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $objWriter = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($objPHPExcel, 'Xlsx');

        //include '/usr/local/www/libs/PHPExcel/PHPExcel.php';
        //$objPHPExcel = new PHPExcel();
        $objActSheet_SY= $objPHPExcel->getActiveSheet();
        $objActSheet_SY -> setCellValue('A1','品种');
        $objActSheet_SY -> setCellValue('B1','牌号');
        $objActSheet_SY -> setCellValue('C1','价差');
        $objActSheet_SY ->getColumnDimension('A')->setWidth(60);
	    $objActSheet_SY ->getColumnDimension('B')->setWidth(20);
	    $objActSheet_SY ->getColumnDimension('C')->setWidth(20);


        $styleArray = array(  
		    'borders' => array(  
				'allborders' => array(  
				'style' =>\PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,//细边框   
				),  
			),  
            'font' => array('size'=>13),
            'alignment' => array('horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER),
        ); 
        $n=2;
        foreach($newdata as $key=>$val){
            foreach($val as $key2=>$val2){
                if($key2==0){
                    $objActSheet_SY-> setCellValue('A'.$n,$key);
                    $objActSheet_SY->mergeCells('A'.$n.':A'.($n+$val2['row']-1));
                }
                $objActSheet_SY -> setCellValue('B'.$n,$val2['data']);
			    $objActSheet_SY -> setCellValue('C'.$n,'');
                $n++;
            }
        }
        $objActSheet_SY->getStyle("A1:C".($n-1))->applyFromArray($styleArray);

        $name = "合金钢销售价差表";
		$filename = $name.'.xls';
		header("Pragma: public");
		header("Expires: 0");
		header("Cache-Control:must-revalidate, post-check=0, pre-check=0");
		header("Content-Type:application/force-download");
		header("Content-Type:application/vnd.ms-execl");
		header("Content-Type:application/octet-stream");
		header("Content-Type:application/download");
		header('Content-Disposition:attachment;filename='.$filename);
		header("Content-Transfer-Encoding:binary");
		//$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
        $objWriter = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($objPHPExcel, 'Xls');
		$objWriter->save('php://output');
    }

    public function nopower($params){

    }



    public function get_xhdate($today)//根据当前日获取上个钢之家工作日
    {
	   $flag=1;
		$lastday=$today;
		while(true)
		{
			$lastday=date('Y-m-d',strtotime('-'.$flag.' day',strtotime($lastday)));
			if(file_get_contents('https://holiday.steelhome.com/isholiday.php?date='.$lastday)!="1")
			{ 
			  break;
			} 
		}
		$today_s=$today;
		$lastday_s=$lastday;
        return  $lastday_s;
    }


    public function jxpj($params){ 
        global $checkUser;
        $GUID = $params['GUID'];
        $uptype = $params["uptype"]!=""?$params["uptype"]:1; 
        $type = $params["type"]!=""?$params["type"]:9;    
        $user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
        if(empty($user_infos)){
            alert($this->utfToGBK('用户不存在'));
            exit;
        }
        $sdate = $params['sdate'] ? $params['sdate']: "Y-m";
        $this->assign('date',$date);
        $this->assign('type',$type);
        $this->assign('GUID',$GUID);
        $this->assign('uptype',$uptype);
    }

    public function getjxpjlist($params){ 
        $uptype = $params["uptype"]!=""?$params["uptype"]:1; 
		$GUID = $params['GUID'];
        $user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
        
        $per = $params['limit']=='' ? 20 : $params['limit'];
		$page = $params['page'] == '' ? 1 : $params['page'];
		$start = ( $page - 1 ) * $per;
        $where="";
        if($params["sdate"]!=""){
            $where.=" and sdate ='".$params["sdate"]."-01'";
        }

        $data=$this->_dao->query("select * from data_table_base  where type='9' and isdel=0  $where order by id desc limit $start,$per");
        $total=$this->_dao->getOne("select count(*) from data_table_base  where type='9' and isdel=0   $where ");

        foreach($data as &$v){
            $v['sdate']=date('Y-m',strtotime($v['sdate']));
        }
        $return_array = array(
            "code"=> 0,
            "data"=> $data,
            "count"=>$total
        );
        echo json_encode($return_array);
		exit;

    }

    public function jxpj_data_manage($params){ 
        $type = $params["type"]!=""?$params["type"]:1;  
        $uptype = $params["uptype"]!=""?$params["uptype"]:1; 
		$GUID = $params['GUID'];
        $user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
        if(empty($user_infos)){
            alert($this->utfToGBK('用户不存在'));
            exit;
        }
        
        $where="";
        if($params['id']==""){
            alert($this->utfToGBK('参数有误！'));
            exit;
        }else{
            $where.=" and id=" . $params['id'] ;
        }
        $base_data=$this->_dao->getRow("select id,sdate,edate from data_table_base  where type='$type'and isdel=0 $where order by id desc limit 1");
        $baseid=$base_data['id'];
        $data=$this->_dao->query("select * from data_table where baseid='$baseid' order by id asc ");
        $newhead=array();
        $newdata=array();
        foreach($data as $k=>$v){
            if($k>1){
                $newdata[$v['dta1']][]=$v;
            }else{
                $newhead[]=$v;
            }
        }

        if(count($newhead)==2 && $newhead[1]['dta10']==""){
            unset($newhead[1]);
        }

        foreach($newdata as $kk=>$vv){
            $newdata[$kk][0]['row']=count($vv);
        }

        $this->assign("newdata",$newdata);
        $this->assign("newhead",$newhead);

      
        if($params['function']=='export'){

            $objPHPExcel = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
			$objWriter = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($objPHPExcel, 'Xls');
			$sheet = $objPHPExcel->getActiveSheet();
			$sheet->getDefaultColumnDimension()->setWidth(15);//所有单元格（列）默认宽度
            $sheet->getDefaultRowDimension()->setRowHeight(-1);
            $objPHPExcel->getDefaultStyle()->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
			$objPHPExcel->getDefaultStyle()->getAlignment()->setVertical(\PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER);
            $styleThinBlackBorderOutline = array(
				'borders' => array(
					'allborders' => array( //设置全部边框
						'style' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN, //粗的是thick
                        'color' => array ('argb' => 'FF000000'),
					),
				),
			);

            


            $sheet->getStyle('A1:S19')->getAlignment()->setVertical(\PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER);    // A1到E3单元格垂直居中
            $sheet->getStyle('A1:S19')->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER); // A1单元格水平居中

            $styleArray1 = array(
				'alignment' => array(
				'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
				'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,
				),
				'borders' => array (
					'allBorders' => array (
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,  //设置border样式
                        //'style' => PHPExcel_Style_Border::BORDER_THICK, 另一种样式
                        'color' => array ('argb' => 'FF000000'),     //设置border颜色
                    )
                )
            );

            $sheet ->getColumnDimension('C')->setWidth(30);
            $sheet ->getColumnDimension('G')->setWidth(40);
            $sheet->getStyle('G')->getAlignment()->setWrapText(TRUE);
            $sheet->getStyle('C')->getAlignment()->setWrapText(TRUE);
			
            $i=1;
            $j=1;
            foreach($newhead as $k=>$v){
                $sheet->setCellValueByColumnAndRow(1,$j,$v['dta1']);
                $sheet->setCellValueByColumnAndRow(2,$j,$v['dta2']);
                $sheet->setCellValueByColumnAndRow(3,$j,$v['dta3']);
                $sheet->setCellValueByColumnAndRow(4,$j,$v['dta4']);
                $sheet->setCellValueByColumnAndRow(5,$j,$v['dta5']);
                $sheet->setCellValueByColumnAndRow(6,$j,$v['dta6']);
                $sheet->setCellValueByColumnAndRow(7,$j,$v['dta7']);
                $sheet->setCellValueByColumnAndRow(8,$j,$v['dta8']);
                $sheet->setCellValueByColumnAndRow(9,$j,$v['dta9']);
                $sheet->setCellValueByColumnAndRow(10,$j,$v['dta10']);
                $sheet->setCellValueByColumnAndRow(11,$j,$v['dta11']);
                $sheet->setCellValueByColumnAndRow(12,$j,$v['dta12']);
                $sheet->setCellValueByColumnAndRow(13,$j,$v['dta13']);
                $sheet->setCellValueByColumnAndRow(14,$j,$v['dta14']);
                $sheet->setCellValueByColumnAndRow(15,$j,$v['dta15']);
                $sheet->setCellValueByColumnAndRow(16,$j,$v['dta16']);
                $sheet->setCellValueByColumnAndRow(17,$j,$v['dta17']);
                $sheet->setCellValueByColumnAndRow(18,$j,$v['dta18']);
                $sheet->setCellValueByColumnAndRow(19,$j,$v['dta19']);
                
                $j++;
            }

                $sheet->mergeCells('A1:A2');
                $sheet->mergeCells('B1:B2');
                $sheet->mergeCells('C1:D2');
                $sheet->mergeCells('E1:E2');
                $sheet->mergeCells('F1:F2');
                $sheet->mergeCells('G1:G2');
                $sheet->mergeCells('H1:H2');
                $sheet->mergeCells('I1:I2');
                $sheet->mergeCells('K1:K2');
                $sheet->mergeCells('L1:L2');
                $sheet->mergeCells('M1:M2');
                $sheet->mergeCells('N1:N2');
                $sheet->mergeCells('O1:O2');
                $sheet->mergeCells('P1:P2');
                $sheet->mergeCells('Q1:Q2');
                $sheet->mergeCells('R1:R2');
                $sheet->mergeCells('S1:S2');
                if($newhead[1]['dta10']==""){
                    $sheet->mergeCells('J1:J2');
                }
               

            
            $j=3;
            $row=3;
            $row2=3;
            foreach($newdata as $k=>$v){
                foreach($v as $kk=>$vv){
                    $sheet->setCellValueByColumnAndRow(1,$j,$vv['dta1']);
                    $sheet->setCellValueByColumnAndRow(2,$j,$vv['dta2']);
                    $sheet->setCellValueByColumnAndRow(3,$j,$vv['dta3']);
                    $sheet->setCellValueByColumnAndRow(4,$j,$vv['dta4']);
                    $sheet->setCellValueByColumnAndRow(5,$j,$vv['dta5']);
                    $sheet->setCellValueByColumnAndRow(6,$j,$vv['dta6']);
                    $sheet->setCellValueByColumnAndRow(7,$j,$vv['dta7']);
                    $sheet->setCellValueByColumnAndRow(8,$j,$vv['dta8']);
                    $sheet->setCellValueByColumnAndRow(9,$j,$vv['dta9']);
                    $sheet->setCellValueByColumnAndRow(10,$j,$vv['dta10']);
                    $sheet->setCellValueByColumnAndRow(11,$j,$vv['dta11']);
                    $sheet->setCellValueByColumnAndRow(12,$j,$vv['dta12']);
                    $sheet->setCellValueByColumnAndRow(13,$j,$vv['dta13']);
                    $sheet->setCellValueByColumnAndRow(14,$j,$vv['dta14']);
                    $sheet->setCellValueByColumnAndRow(15,$j,$vv['dta15']);
                    $sheet->setCellValueByColumnAndRow(16,$j,$vv['dta16']);
                    $sheet->setCellValueByColumnAndRow(17,$j,$vv['dta17']);
                    $sheet->setCellValueByColumnAndRow(18,$j,$vv['dta18']);
                    $sheet->setCellValueByColumnAndRow(19,$j,$vv['dta19']);
                    $j++;
                    $row2++;
                }
                //  echo $row.'##'.$row2;
                //  echo '<br>';
                $sheet->mergeCells('A'.$row.':A'.$row2-1);
                $row=$row2;
            }

            //exit;
            $sheet->getStyle('A1:S'.($j-1))->applyFromArray($styleArray1);
            

            header("Pragma: public");
			header("Expires: 0");
			header("Cache-Control:must-revalidate, post-check=0, pre-check=0");
			header("Content-Type:application/force-download");
			header("Content-Type:application/vnd.ms-execl");
			header("Content-Type:application/octet-stream");
			header("Content-Type:application/download");
			header("Content-Disposition:attachment;filename=绩效评价（".date("Y.m",strtotime($params['sdate']))."）.xls");
			header("Content-Transfer-Encoding:binary");
			$objWriter->save('php://output');
			exit;
        }
    }

   

    public function uploadhuizong($params){ 
		$GUID = $params['GUID'];
        $user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
        if(empty($user_infos)){
            alert($this->utfToGBK('用户不存在'));
            exit;
        }
        
        $where="";
        if($params['id']==""){
            alert($this->utfToGBK('参数有误！'));
            exit;
        }else{
            $where.=" and id=" . $params['id'] ;
        }
        $base_data=$this->_dao->getRow("select id,sdate,edate from data_table_base  where type='9'and isdel=0 $where order by id desc limit 1");
        $baseid=$base_data['id'];
        $data=$this->_dao->query("select * from data_table where baseid='$baseid' order by id asc ");

        $this->assign("dataDate",date("Y-m",strtotime($base_data['sdate'])));
        
        $alltype=array("未完成基准"=>0,"完成基准"=>1,"完成目标"=>2,"完成挑战"=>3);

        $KPIData=array();
        $newdata=array();

        

        if($base_data['sdate']>="2024-04-01"){
            $dept=array(
                "主体单位"=>array("炼铁事业部","厚板事业部","特钢事业部","硅钢薄板事业部"),
                "经营单位"=>array("采购中心","营销中心","金属制品事业部"),
                "辅助单位"=>array("后勤文旅事业部","后勤文旅事业部（中心医院）","维检中心","工程公司","新钢环科","良山事业部","实业公司","乌石山矿"),
                "业务部门"=>array("技术中心","物流中心","财务共享服务中心","能源环保部","设备管理部","数智化部")
            );
    
        }else{
            $dept=array(
                "主体单位"=>array("炼铁事业部","厚板特钢事业部","硅钢薄板事业部"),
                "经营单位"=>array("采购中心","营销中心","金属制品事业部"),
                "辅助单位"=>array("后勤文旅事业部","后勤文旅事业部（中心医院）","维检中心","工程公司","新钢环科","良山事业部","实业公司","乌石山矿"),
                "业务部门"=>array("技术中心","物流中心","财务共享服务中心","能源环保部","设备管理部","数智化部")
            );
    
        }

        

        
        foreach($data as $k=>$v){
            if((trim($v['dta2'])=="降本增效" || trim($v['dta2'])=="重点专项指标") && $v['dta1']!="" ){
                $zb=isset($alltype[$v['dta19']]) ? $alltype[$v['dta19']] : "";
                
                $v['dta1']=trim($v['dta1']);
                if(trim($v['dta1']) == "厚板事业部" || trim($v['dta1']) == "特钢事业部"  || trim($v['dta1']) == "厚板特钢事业部" || trim($v['dta1']) == "营销中心" || trim($v['dta1']) == "硅钢薄板事业部" ){
                    if(trim($v['dta2'])=="降本增效"){
                        $newdata[0][$v['dta1']]['kpi'][$v['dta3']]=$v['dta3'];
                    }
                    
                    $v['dta4']= str_replace(" ", "", trim($v['dta4']));
                    if((trim($v['dta2'])=="降本增效" && trim($v['dta4'])=="小计") || (trim($v['dta2'])=="降本增效" && trim($v['dta4'])=="" && trim($data[$k+1]['dta2'])!="降本增效")){
                        $newdata[0][$v['dta1']]['zb'][$zb][]=$v;
                      
                    }else if(trim($v['dta2'])=="重点专项指标"){
                        $newdata[1][$v['dta1']]['zb'][$zb][]=$v;
                        $newdata[1][$v['dta1']]['kpi'][$v['dta3']]=$v['dta3'];
            
                    }
                }else{
                    if(trim($v['dta2'])=="降本增效"){
                        $newdata[0][$v['dta1']]['zb'][$zb][]=$v;
                        if(trim($v['dta1']) == "财务共享服务中心" ){
                            $newdata[0][$v['dta1']]['kpi'][$v['dta4']]=$v['dta4'];
                        }else{
                            $newdata[0][$v['dta1']]['kpi'][$v['dta3']]=$v['dta3'];
                        }
                    }else if(trim($v['dta2'])=="重点专项指标"){
                        $newdata[1][$v['dta1']]['zb'][$zb][]=$v;
                        $newdata[1][$v['dta1']]['kpi'][$v['dta3']]=$v['dta3'];
                    }
                }
            }
        }

        

        $alldata=array();
        foreach($newdata as $key=>$val){
            foreach($val as $kk=>$vv){
                foreach($vv['zb'] as $kkk=>$vvv){
                    $alldata[$key][$kk]['zb'][$kkk]=count($vvv);
                }
                $alldata[$key][$kk]['kpi']=count($vv['kpi']);
            }
        }

        

        $dataList=array();
        foreach($dept as $dk=>$dv){
            $kpi_num=0;
            $njz_num=0;
            $jz_num=0;
            $mb_num=0;
            $tz_num=0;
            $kpi_num2=0;
            $njz_num2=0;
            $jz_num2=0;
            $mb_num2=0;
            $tz_num2=0;

            foreach($dv as $dkk=>$dvv){
                $arr=array();
                $brr=array();
                $arr['dept']=$dvv;
                $arr['kpi']=isset($alldata[0][$dvv]['kpi'])?$alldata[0][$dvv]['kpi']:"0";
                $arr['njz']=isset($alldata[0][$dvv]['zb'][0])?$alldata[0][$dvv]['zb'][0]:"";
                $arr['jz']=isset($alldata[0][$dvv]['zb'][1])?$alldata[0][$dvv]['zb'][1]:"";
                $arr['mb']=isset($alldata[0][$dvv]['zb'][2])?$alldata[0][$dvv]['zb'][2]:"";
                $arr['tz']=isset($alldata[0][$dvv]['zb'][3])?$alldata[0][$dvv]['zb'][3]:"";

                $dataList[0][]=$arr;
                $kpi_num+=intval($arr['kpi']);
                $njz_num+=intval($arr['njz']);
                $jz_num+=intval($arr['jz']);
                $mb_num+=intval($arr['mb']);
                $tz_num+=intval($arr['tz']);


                $brr['dept']=$dvv;
                $brr['kpi']=isset($alldata[1][$dvv]['kpi'])?$alldata[1][$dvv]['kpi']:"";
                $brr['njz']=isset($alldata[1][$dvv]['zb'][0])?$alldata[1][$dvv]['zb'][0]:"";
                $brr['jz']=isset($alldata[1][$dvv]['zb'][1])?$alldata[1][$dvv]['zb'][1]:"";
                $brr['mb']=isset($alldata[1][$dvv]['zb'][2])?$alldata[1][$dvv]['zb'][2]:"";
                $brr['tz']=isset($alldata[1][$dvv]['zb'][3])?$alldata[1][$dvv]['zb'][3]:"";
                $dataList[1][]=$brr;
                $kpi_num2+=intval($brr['kpi']);
                $njz_num2+=intval($brr['njz']);
                $jz_num2+=intval($brr['jz']);
                $mb_num2+=intval($brr['mb']);
                $tz_num2+=intval($brr['tz']);


            }

            $arr=array();
            $brr=array();
            $arr['dept']=$dk;
            $arr['kpi']=$kpi_num;
            $arr['njz']=$njz_num=="0" ? "" : $njz_num;
            $arr['jz']=$jz_num=="0" ? "" :$jz_num;
            $arr['mb']=$mb_num=="0" ? "" :$mb_num;
            $arr['tz']=$tz_num=="0" ? "" :$tz_num;
            $arr['color']=1;
            $dataList[0][]=$arr;
            $brr['dept']=$dk;
            $brr['kpi']=$kpi_num2;
            $brr['njz']=$njz_num2=="0" ? "" : $njz_num2;
            $brr['jz']=$jz_num2=="0" ? "" :$jz_num2;
            $brr['mb']=$mb_num2=="0" ? "" :$mb_num2;
            $brr['tz']=$tz_num2=="0" ? "" :$tz_num2;
            $brr['color']=1;
            $dataList[1][]=$brr;
        }

        
       

        $this->assign("dataList",$dataList);

      
        if($params['function']=='export'){

            $objPHPExcel = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
			$objWriter = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($objPHPExcel, 'Xls');
			//$sheet = $objPHPExcel->getActiveSheet();
			
            $objPHPExcel->getDefaultStyle()->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
			$objPHPExcel->getDefaultStyle()->getAlignment()->setVertical(\PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER);
            $styleThinBlackBorderOutline = array(
				'borders' => array(
					'allborders' => array( //设置全部边框
						'style' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN, //粗的是thick
                        'color' => array ('argb' => 'FF000000'),
					),
				),
			);

            


           

            $styleArray1 = array(
				'alignment' => array(
				'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
				'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,
				),
				'borders' => array (
					'allBorders' => array (
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,  //设置border样式
                        //'style' => PHPExcel_Style_Border::BORDER_THICK, 另一种样式
                        'color' => array ('argb' => 'FF000000'),     //设置border颜色
                    )
                )
            );

            if(!empty($dataList[0])){
                $objPHPExcel -> setActiveSheetIndex(0);
			    $sheet = $objPHPExcel->getActiveSheet();
                $sheet->getDefaultColumnDimension()->setWidth(15);//所有单元格（列）默认宽度
                $sheet->getDefaultRowDimension()->setRowHeight(20);
                $sheet->setTitle ( '降本增效' ) ;
                
                $sheet ->getColumnDimension('A')->setWidth(30);
                $sheet->setCellValueByColumnAndRow(1,1,"部门");
                $sheet->setCellValueByColumnAndRow(2,1,"KPI数");
                $sheet->setCellValueByColumnAndRow(3,1,"未完成基准");
                $sheet->setCellValueByColumnAndRow(4,1,"完成基准");
                $sheet->setCellValueByColumnAndRow(5,1,"完成目标");
                $sheet->setCellValueByColumnAndRow(6,1,"完成挑战");
                
                $j=2;
                foreach($dataList[0] as $k=>$v){
                    $sheet->setCellValueByColumnAndRow(1,$j,$v['dept']);
                    $sheet->setCellValueByColumnAndRow(2,$j,$v['kpi']);
                    $sheet->setCellValueByColumnAndRow(3,$j,$v['njz']);
                    $sheet->setCellValueByColumnAndRow(4,$j,$v['jz']);
                    $sheet->setCellValueByColumnAndRow(5,$j,$v['mb']);
                    $sheet->setCellValueByColumnAndRow(6,$j,$v['tz']);

                    if($v['color']){
                        $sheet->getStyle('A'.$j.':F'.$j)->applyFromArray(
                            [
                                'fill' => [
                                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                                    'color' => ['rgb' => "FFFF00"],
                                ],
                            ]
                        ); 
                    }
                    
                    $j++;
                }
               

                $sheet->getStyle('A1:F'.($j-1))->applyFromArray($styleArray1);
                $sheet->getStyle('A1:F'.($j-1))->getAlignment()->setVertical(\PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER);    // A1到E3单元格垂直居中
                $sheet->getStyle('A1:F'.($j-1))->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER); // A1单元格水平居中
            }

            if(!empty($dataList[1])){
                $objPHPExcel->createSheet();
				$objPHPExcel -> setActiveSheetIndex(1);
                $sheet2 = $objPHPExcel->getActiveSheet();
                $sheet2->setTitle ( '重点专项指标' ) ;
                

                $sheet2->getDefaultColumnDimension()->setWidth(15);//所有单元格（列）默认宽度
                $sheet2->getDefaultRowDimension()->setRowHeight(20);

                $sheet2 ->getColumnDimension('A')->setWidth(30);
                $sheet2->setCellValueByColumnAndRow(1,1,"部门");
                $sheet2->setCellValueByColumnAndRow(2,1,"KPI数");
                $sheet2->setCellValueByColumnAndRow(3,1,"未完成基准");
                $sheet2->setCellValueByColumnAndRow(4,1,"完成基准");
                $sheet2->setCellValueByColumnAndRow(5,1,"完成目标");
                $sheet2->setCellValueByColumnAndRow(6,1,"完成挑战");
                
                $j=2;
                foreach($dataList[1] as $k=>$v){
                    $sheet2->setCellValueByColumnAndRow(1,$j,$v['dept']);
                    $sheet2->setCellValueByColumnAndRow(2,$j,$v['kpi']);
                    $sheet2->setCellValueByColumnAndRow(3,$j,$v['njz']);
                    $sheet2->setCellValueByColumnAndRow(4,$j,$v['jz']);
                    $sheet2->setCellValueByColumnAndRow(5,$j,$v['mb']);
                    $sheet2->setCellValueByColumnAndRow(6,$j,$v['tz']);
                    if($v['color']){
                        $sheet2->getStyle('A'.$j.':F'.$j)->applyFromArray(
                            [
                                'fill' => [
                                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                                    'color' => ['rgb' => "FFFF00"],
                                ],
                            ]
                        ); 
                    }
                    $j++;
                }

                
                $sheet2->getStyle('A1:F'.($j-1))->applyFromArray($styleArray1);
                $sheet2->getStyle('A1:F'.($j-1))->getAlignment()->setVertical(\PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER);    // A1到E3单元格垂直居中
                $sheet2->getStyle('A1:F'.($j-1))->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER); // A1单元格水平居中
            }

            //exit;
            
            

            header("Pragma: public");
			header("Expires: 0");
			header("Cache-Control:must-revalidate, post-check=0, pre-check=0");
			header("Content-Type:application/force-download");
			header("Content-Type:application/vnd.ms-execl");
			header("Content-Type:application/octet-stream");
			header("Content-Type:application/download");
			header("Content-Disposition:attachment;filename=指标统计（".date("Y.m",strtotime($base_data['sdate']))."）.xls");
			header("Content-Transfer-Encoding:binary");
			$objWriter->save('php://output');
			exit;
        }
    }



    //山钢 我的钢铁数据管理 放在定价模型-月定价下方
    public function sd_steel_data_manage($params)
    {
        $GUID = $params['GUID'];
        $uptype = $params["uptype"]!=""?$params["uptype"]:1; 
        $type = $params["type"]!=""?$params["type"]:8;    
        $user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
        if(empty($user_infos)){
            alert($this->utfToGBK('用户不存在'));
            exit;
        }
        $bigtype = $params["bigtype"]!=""?$params["bigtype"]:($type==9?4:1);
        $date = $params['date'] ? $params['date']:($bigtype==4?date('Y-m'):date('Y-m-d'));
        $this->assign('date',$date);
        $this->assign('type',$type);
        $this->assign('GUID',$GUID);
        $this->assign('uptype',$uptype);
        $this->assign('bigtype',$bigtype);
        $this->assign('params',$params);
        $this->assign('uptype_title',$GLOBALS["sg_steel_upload_title"][$uptype]);
    }

    

    public function get_sg_steel_datalist($params)
    {
        $uptype = $params["uptype"]!=""?$params["uptype"]:1; 
        $bigtype =$params["bigtype"]; 
        $type = $params["type"]!=""?$params["type"]:8;    
		$GUID = $params['GUID'];
        $user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
        
        
        $per = $params['limit']=='' ? 20 : $params['limit'];
		$page = $params['page'] == '' ? 1 : $params['page'];
		$start = ( $page - 1 ) * $per;
        $where=" and type='$uptype' ";
        if($params["date"]!="" && $params["bigtype"]==4){
            $where.=" and date = '".$params["date"]."-01'";
        }else if($params["date"]!=""){
            $where.=" and date = '".$params["date"]."'";
        }
        if( $params["bigtype"]!=""&&$params["bigtype"]!="0"){
            $where.=" and bigtype = '".$bigtype."'";
        }else{
            $where.=" and bigtype in(1,2)";
        }
        $data=$this->drcwdao->query("select * from sd_steel_data_table_base  where  isdel=0  $where order by date desc limit $start,$per");
        foreach($data as &$value){
            $value['typetitle']=$GLOBALS["sg_steel_bigtype"][$value['bigtype']];
            if($params["bigtype"]==4){
                $value['date']=date("Y-m",strtotime($value['date']));
            }
        }
        $total=$this->drcwdao->getOne("select count(*) from sd_steel_data_table_base  where  isdel=0   $where ");
        $return_array = array(
            "code"=> 0,
            "data"=> $data,
            "count"=>$total
        );
        echo json_encode($return_array);
		exit;

    }

    public function catdata($params)
    {
        $GUID = $params['GUID'];
        $uptype = $params["uptype"]!=""?$params["uptype"]:1; 
        $type = $params["type"]!=""?$params["type"]:10;    
        $user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
        if(empty($user_infos)){
            alert($this->utfToGBK('用户不存在'));
            exit;
        }
        $id = $params['id'];
        $http_type = '//'.$_SERVER['HTTP_HOST'];
        $sql = "select filepath,sheet from sd_steel_data_table_base where id = '".$id."'";
        $file = $this->drcwdao->getRow($sql);
        $filepath = explode('/dc.steelhome.cn',$file['filepath']);
        $file_url = $http_type.$filepath[1];
        $sheet=$file['sheet'];
        $this->assign('file_url', $file_url);
        $this->assign('sheet', $sheet);
        $this->assign('GUID', $GUID);
        $this->assign('date', date("Y-m-d"));

    }

    //手持订单量进度
    public function sd_steel_order_manage($params)
    {
        $GUID = $params['GUID'];
        $uptype = $params["uptype"]!=""?$params["uptype"]:1; 
        $type = $params["type"]!=""?$params["type"]:10;    
        $user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
        if(empty($user_infos)){
            alert($this->utfToGBK('用户不存在'));
            exit;
        }
        $date = $params['date'] ? $params['date']:date('Y-m-d');
        $bigtype = $params["bigtype"]!=""?$params["bigtype"]:3;
        $this->assign('date',$date);
        $this->assign('type',$type);
        $this->assign('GUID',$GUID);
        $this->assign('uptype',$uptype);
        $this->assign('params',$params);
        $this->assign('bigtype',$bigtype);
    }

    public function sd_steel_data_input($params){
        $uptype = $params["uptype"]!=""?$params["uptype"]:1; 
        $type = $params["type"]!=""?$params["type"]:8; 
        $bigtype = $params["bigtype"]!=""?$params["bigtype"]:1;    
        $isadd = $params["id"]!=""? 0:1;    
		$GUID = $params['GUID'];
        $user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
        if(empty($user_infos)){
            alert($this->utfToGBK('用户不存在'));
            exit;
        }
        if($params['id']){
            //修改价差and type='$uptype' and bigtype='$bigtype' 
            $baseArr=$this->drcwdao->getRow("select * from sd_steel_data_table_base  where isdel=0 and id='".$params['id']."' order by id desc limit 1");
            $baseid=$baseArr['id'];
            $baseid_1=$baseid;
            $this->assign('baseid',$baseid);
            $date = $baseArr['date'];
            $bigtype = $baseArr["bigtype"];   
        }else{
            //录入新价差
            //获取上期价格
           $baseArr=$this->drcwdao->getRow("select * from sd_steel_data_table_base  where isdel=0 and type='$uptype' and bigtype='$bigtype' order by date desc limit 1");
           $baseid=$baseArr['id'];
            if($baseid==""){
                //获取基础数据
                $baseid=$this->drcwdao->getOne("select id from sd_steel_data_table_base  where isdel=0 and type='$uptype' and bigtype='$bigtype' order by id desc limit 1");
            }
            $date = $params['date'] ? $params['date']: date('Y-m-d');
        }
        $data=$this->drcwdao->query("select * from sd_steel_data_table where baseid='$baseid' order by id asc ");
        $newdata=array();
        //$data=$this->array_utfToGBK($data);
        $pzname_arr=array();
        $city_arr=array();
        
        $header=array();
        $header[0][0][0]="品种";
        $header[1][0]['data']="市场";

        foreach($data as $key=>$val){
            if($val['dta2']!=""){
                $header[0][$val['dta2']][]=$val['dta2'];
            }else if($val['dta1']!=""){
                $header[0][$val['dta1']][]=$val['dta1'];
            }
            if($val['dta3']!=""){
                $header[1][]=array('data'=>$val['dta3']);
            }
            $arr=array(
                'data'=>$isadd==0?$val['dta4']:"",
                'id'=>$isadd==0?$val['id']:"",
                'data2'=>$val['dta2']!=""?$val['dta2']:$val['dta1'],
                'data3'=>$val['dta3'],
            );
            $newdata[0][]= $arr;
        }
        
        $j=0;
        foreach($header[0] as $k=>$v){
            if(count($v)==1){
                $newheader[$j]['data']=$v[0];
            }else{
                $newheader[$j]['data']=$v[0];
                $newheader[$j]['col']=count($v);
            }
            $j++;
        }
        $header[0]=$newheader;
        $this->assign('date',$bigtype==4?date("Y-m",strtotime($date)):$date);
        $this->assign('newdata',$newdata);
        $this->assign('header',$header);
        $this->assign('GUID',$params["GUID"]);
        $this->assign('uptype',$uptype);
        $this->assign('bigtype',$bigtype);
        $this->assign('type',$type);
        $this->assign('isadd',$isadd);
        $this->assign('params',$params);
        $this->assign('uptype_title',$GLOBALS["sg_steel_upload_title"][$uptype]);
    }


    public function update_sd_steel_base_data($params){
        $type=$params['type'];
        $uptype=$params['uptype'];
        $bigtype=$params['bigtype'];
        $baseid=$params['baseid'];
        $date=$bigtype==4?$params['date'].'-01':$params['date'];
        $GUID = $params['GUID'];
        $user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
        $response["Success"] = 1;
		$response['Message'] =$this->gbkToUTF8('数据保存成功');
        if(empty($user_infos)){
            $response["Success"] = 0;
            $response['Message'] =$this->gbkToUTF8('用户不存在');
            echo $this->pri_JSON($response);
            exit;
        }
        $dta_1=$GLOBALS["sg_steel_upload_title"][$uptype];
        $dta_2_Arr=isset($params["dta_2"])?$params["dta_2"]:array();
        $dta_3_Arr=isset($params["dta_3"])?$params["dta_3"]:array();
        $dta_4_Arr=isset($params["dta_4"])?$params["dta_4"]:array();
        
        if($baseid!=""){
            foreach($dta_4_Arr as $id=>$dta_4){
                $dta_2=isset($dta_2_Arr[$id])?$dta_2_Arr[$id]:"";
                $dta_3=isset($dta_3_Arr[$id])?$dta_3_Arr[$id]:"";
                $dta_4=isset($dta_4_Arr[$id])?$dta_4_Arr[$id]:"";
                $this->drcwdao->execute("UPDATE sd_steel_data_table SET dta4='$dta_4' WHERE id='$id'");
            }
        }else if($baseid==""){
            $valueSql_1="";
            $this->drcwdao->del_sd_steel_data_table_base($bigtype,$uptype,$date,$user_infos);
            $baseid=$this->drcwdao->insert_sd_steel_data_table_base($bigtype,$uptype,$date,$user_infos);
            foreach($dta_4_Arr as $id=>$dta_4){
                $dta_2=isset($dta_2_Arr[$id])?$dta_2_Arr[$id]:"";
                $dta_3=isset($dta_3_Arr[$id])?$dta_3_Arr[$id]:"";
                $dta_4=isset($dta_4_Arr[$id])?$dta_4_Arr[$id]:"";
                $valueSql_1.="($baseid,'$dta_1','$dta_2','$dta_3','$dta_4',NOW(),'".$user_infos['Uid']."'),";
            }
            if($valueSql_1!="")
            {
                $basesql = "INSERT INTO sd_steel_data_table(baseid,dta1,dta2,dta3,dta4,createtime,createuser) VALUES";
                $tsql = substr($valueSql_1, 0, -1);
                $this->drcwdao->execute($basesql.$tsql);
            }
        }
        
        echo $this->pri_JSON($response);
        exit;
    }


    public function sd_steel_downExcel($params){
        $GUID=$params['GUID'];
        $uptype=$params['uptype'];
        $bigtype=$params['bigtype']!=''?$params['bigtype']:1;
        $objPHPExcel = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $objWriter = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($objPHPExcel, 'Xlsx');

        $styleArray = array(
            'alignment' => array(
            'horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER,
            'vertical' => \PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER,
            ),
            'borders' => array (
                'allBorders' => array (
                    'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN,  //设置border样式
                    'color' => array ('argb' => 'FF000000'),     //设置border颜色
                )
            )
        );

        $objActSheet_SY= $objPHPExcel->getActiveSheet();
        // $objActSheet_SY ->getColumnDimension('A')->setWidth(15);
	    // $objActSheet_SY ->getColumnDimension('B')->setWidth(15);
	    // $objActSheet_SY ->getColumnDimension('C')->setWidth(15);
        $objActSheet_SY->getDefaultColumnDimension()->setWidth(15);//所有单元格（列）默认宽度

        if($uptype==1){
            $objActSheet_SY -> setCellValue('A1','品种');
            $objActSheet_SY -> setCellValue('B1','螺纹钢');
            $objActSheet_SY->mergeCells('B1:D1');
            $objActSheet_SY -> setCellValue('A2','市场');
            $objActSheet_SY -> setCellValue('B2','莱芜');
            $objActSheet_SY -> setCellValue('C2','临沂');
            $objActSheet_SY -> setCellValue('D2','青岛');
            $objActSheet_SY -> setCellValue('A3',date('Y-m-d'));
            $objActSheet_SY->getStyle("A1:D3")->applyFromArray($styleArray);
        }else if($uptype==2){
            $objActSheet_SY -> setCellValue('A1','品种');
            $objActSheet_SY -> setCellValue('B1','Q235B 200*200mm');
            $objActSheet_SY->mergeCells('B1:D1');
            $objActSheet_SY -> setCellValue('E1','Q235B 600*200mm');
            $objActSheet_SY->mergeCells('E1:G1');
            $objActSheet_SY -> setCellValue('A2','市场');
            $objActSheet_SY -> setCellValue('B2','莱芜');
            $objActSheet_SY -> setCellValue('C2','泰安');
            $objActSheet_SY -> setCellValue('D2','唐山');
            $objActSheet_SY -> setCellValue('E2','莱芜');
            $objActSheet_SY -> setCellValue('F2','泰安');
            $objActSheet_SY -> setCellValue('G2','唐山');
            $objActSheet_SY -> setCellValue('A3',date('Y-m-d'));
            $objActSheet_SY->getStyle("A1:G3")->applyFromArray($styleArray);
        }else if($uptype==3){
            $objActSheet_SY -> setCellValue('A1','品种');
            $objActSheet_SY -> setCellValue('B1','45#');
            $objActSheet_SY->mergeCells('B1:C1');
            $objActSheet_SY -> setCellValue('D1','20CrMnTi');
            $objActSheet_SY->mergeCells('D1:F1');
            $objActSheet_SY -> setCellValue('A2','市场');
            $objActSheet_SY -> setCellValue('B2','莱芜');
            $objActSheet_SY -> setCellValue('C2','杭州（南钢）');
            $objActSheet_SY -> setCellValue('D2','莱芜');
            $objActSheet_SY -> setCellValue('E2','无锡');
            $objActSheet_SY -> setCellValue('F2','杭州');
            $objActSheet_SY -> setCellValue('A3',date('Y-m-d'));
            $objActSheet_SY->getStyle("A1:F3")->applyFromArray($styleArray);
        }else if($uptype==4){
            $objActSheet_SY -> setCellValue('A1','品种');
            $objActSheet_SY -> setCellValue('B1','Q235B 4.75mm');
            $objActSheet_SY->mergeCells('B1:I1');
            $objActSheet_SY -> setCellValue('J1','Q355B 4.75mm');
            $objActSheet_SY->mergeCells('J1:N1');
            $objActSheet_SY -> setCellValue('A2','市场');
            $objActSheet_SY -> setCellValue('B2','莱芜');
            $objActSheet_SY -> setCellValue('C2','日照');
            $objActSheet_SY -> setCellValue('D2','泰安');
            $objActSheet_SY -> setCellValue('E2','临沂');
            $objActSheet_SY -> setCellValue('F2','上海');
            $objActSheet_SY -> setCellValue('G2','合肥');
            $objActSheet_SY -> setCellValue('H2','天津');
            $objActSheet_SY -> setCellValue('I2','鞍山');
            $objActSheet_SY -> setCellValue('J2','莱芜');
            $objActSheet_SY -> setCellValue('K2','日照');
            $objActSheet_SY -> setCellValue('L2','泰安');
            $objActSheet_SY -> setCellValue('M2','临沂');
            $objActSheet_SY -> setCellValue('N2','上海');
            $objActSheet_SY -> setCellValue('A3',date('Y-m-d'));
            $objActSheet_SY->getStyle("A1:N3")->applyFromArray($styleArray);
        }else if($uptype==5){
            $objActSheet_SY -> setCellValue('A1','品种');
            $objActSheet_SY -> setCellValue('B1','冷轧1500');
            $objActSheet_SY->mergeCells('B1:D1');
            $objActSheet_SY -> setCellValue('E1','冷轧2030');
            $objActSheet_SY->mergeCells('E1:J1');
            $objActSheet_SY -> setCellValue('A2','市场');
            $objActSheet_SY -> setCellValue('B2','莱芜');
            $objActSheet_SY -> setCellValue('C2','日照');
            $objActSheet_SY -> setCellValue('D2','临沂');

            $objActSheet_SY -> setCellValue('E2','日照');
            $objActSheet_SY -> setCellValue('F2','临沂');
            $objActSheet_SY -> setCellValue('G2','上海');
            $objActSheet_SY -> setCellValue('H2','天津');
            $objActSheet_SY -> setCellValue('I2','广州');
            $objActSheet_SY -> setCellValue('J2','鞍山');
            $objActSheet_SY -> setCellValue('A3',date('Y-m-d'));
            $objActSheet_SY->getStyle("A1:J3")->applyFromArray($styleArray);
        }
        if($bigtype==1){
            $name = $GLOBALS["sg_steel_upload_title"][$uptype]."模板（我的钢铁数据）";
        }else{
            $name = $GLOBALS["sg_steel_upload_title"][$uptype]."模板（市场成交价）";
        }
		$filename = $name.'.xls';
		header("Pragma: public");
		header("Expires: 0");
		header("Cache-Control:must-revalidate, post-check=0, pre-check=0");
		header("Content-Type:application/force-download");
		header("Content-Type:application/vnd.ms-execl");
		header("Content-Type:application/octet-stream");
		header("Content-Type:application/download");
		header('Content-Disposition:attachment;filename='.$filename);
		header("Content-Transfer-Encoding:binary");
		//$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
        $objWriter = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($objPHPExcel, 'Xls');
		$objWriter->save('php://output');
    }


    public function del_sd_steel_base_data($params)
    {
        $baseid = $params['baseid'];
        $response = array(
			'Success'  => 1,
			'Message'  => $this->gbkToUTF8('删除成功'),
		);
        if($baseid!=""){
            $sql="UPDATE sd_steel_data_table_base SET isdel=1 WHERE id='$baseid' ";
            $this->drcwdao->execute($sql);
        }
        echo $this->pri_JSON($response);
		exit;
    }


    public function gbkToUTF8( $str )
    {
        //return iconv("GBK","utf-8//IGNORE",$str);
        return $str;
    }
    function convert_to_utf8($params, $array = null)
    {
        if (count($array) == 0) {
            if (is_array($params)) {
                foreach ($params as $k => $v) {
                    unset($params[$k]);
                    $k = iconv("GBK", "UTF-8", $k);
                    $params[$k] = $this->convert_to_utf8($v);
                }
                return $params;
            } else {
                return iconv("GBK", "UTF-8", $params);
            }
        } else {
            foreach ($params as $key => $value) {
                if (array_key_exists($array, $key)) $params[$key] = $this->convert_to_utf8($value);
            }
        }
    }

    //读取工作表并返回
    public function readSheet($file)
    {
        //include '/usr/local/www/libs/PHPExcel/PHPExcel.php';
		$type = pathinfo($file); 
       
		$type = strtolower($type["extension"]);
       
		if ($type=='xlsx') { 
			$type='Excel2007'; 
		}elseif($type=='xls') { 
			$type = 'Excel5';
		}

        $fileType= \PhpOffice\PhpSpreadsheet\IOFactory::identify($file);
       
		$objReader=\PhpOffice\PhpSpreadsheet\IOFactory::createReader($fileType);
        
        
		$objPHPExcel = $objReader->load($file); //加载Excel文件
		$sheets = $objPHPExcel->getSheetNames();
		return $sheets;
    }
    public function utfToGBK( $str )
    {
        //return iconv("utf-8//IGNORE","GBK",$str);
        return $str;
    }


    
    //文件名随机字符串
    public function random($length)
    {
        $hash = '';
        $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789abcdefghijklmnopqrstuvwxyz';
        $max = strlen($chars) - 1;
        mt_srand(( double ) microtime() * 1000000);
        for ($i = 0; $i < $length; $i ++) {
            $hash .= $chars [mt_rand(0, $max)];
        }
        return $hash;
    }

    private function pri_JSON($array)
    {
        $this->pri_arrayRecursive($array, 'urlencode', true);
        $json = json_encode($array);
        return urldecode($json);
    }

    private function pri_arrayRecursive(&$array, $function, $apply_to_keys_also = false)
    {
        static $recursive_counter = 0;
        if (++$recursive_counter > 1000) {
            die('possible deep recursion attack');
        }
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $this->pri_arrayRecursive($array[$key], $function, $apply_to_keys_also);
            } else {
                $array[$key] = $function($value);
            }
            if ($apply_to_keys_also && is_string($key)) {
                $new_key = $function($key);
                if ($new_key != $key) {
                    $array[$new_key] = $array[$key];
                    unset($array[$key]);
                }
            }
        }
        $recursive_counter--;
    }

} 
?>