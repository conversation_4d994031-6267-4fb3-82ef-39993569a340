<?php

class sgscjcpriceAction extends AbstractAction
{
    public function __construct()
    {
        parent::__construct();
    }

    function zhangdie($data, $type = '0', $dc = '0')
    {
        if ($dc == 1) {
            if ($data == 0) {
                if ($type == '-') {
                    $data = '—';
                } else {
                    $data = $data;
                }
            }
        } else {
            if ($data > 0) {
                $data = '<font color=red>↑' . abs($data) . '</font>';
            } elseif ($data < 0) {
                $data = '<font color=green>↓' . abs($data) . '</font>';
            } elseif ($data == 0) {
                if ($type == '-') {
                    $data = '<font color=black>—</font>';
                } else {
                    $data = '<font color=black>' . $data . '</font>';
                }
            }
        }

        return $data;
    }

    public function index($params)
    {
        if ($params['date'] == "") {
            $flag = 1;
            $lastday = date("Y-m-d");
            while (true) {
                if (file_get_contents(APP_URL_WWW . '/isholiday.php?date=' . $lastday) != "1") {
                    break;
                }
                $lastday = date('Y-m-d', strtotime('-' . $flag . ' day', strtotime($lastday)));
            }
            $params['date'] = $lastday;
        }
        $date = $params['date'];


        $price_arr = array(
            '西安市场' => array("龙钢" => '5620231', "酒钢" => "5620234", "晋钢" => "5620237", "建邦" => "5620432", "建龙" => "562023d"),
            '兰州市场' => array("龙钢" => '5720234', "酒钢" => "5720431", "八钢" => "5720232", "宁夏钢铁" => "5720436", "兰鑫钢铁" => "5720432", "申银特钢" => "572043b"),
            '成都市场' => array("龙钢" => '5320235', "威钢" => "532023q", "达钢" => "532023a", "酒钢" => "532043y", "成实" => "5320234", "重庆永航" => "532023o"),
            '重庆市场' => array("龙钢" => '522043a', "达钢" => "5220233", "重钢" => "522023o", "德胜" => "5220231", "重庆永航" => "522023h", "成实" => "522023n"),
            '郑州市场' => array("龙钢" => '372043m', "安钢" => "3720431", "晋钢" => "3720436", "敬业" => "372043r", "沙钢永兴" => "3720438"),
        );


        // $price_id1 = array("5620231","5620234","5620237","5620432","562023d");//西安市场
        // $price_id2 = array("5720234","5720431","5720232","5720436","5720432","572043b");
        // $price_id3 = array("5320235","532023q","532023a","532043y","5320234","532023o");
        // $price_id4 = array("522043a","5220233","522023o","5220231","522023h");
        // $price_id = array_merge($price_id1, $price_id2, $price_id3, $price_id4);
        // $price_id = array("W287102","569422");
        // $price_id='';
        // foreach ($price_arr as $key => $value) {
        // 	$price_id = array();
        // 	$topicture_arr = array();
        // 	$mastertopid_arr = array();
        //   foreach ($value as $key2 => $value2) {
        // 	$price_id[]=$value2;
        //   }

        // foreach($price_id as $id_tmp1) strlen($id_tmp1)==6 ? $topicture_arr[] = $id_tmp1 : $mastertopid_arr[] = $id_tmp1;
        // // echo "<pre>";
        // $mastertopids[$key] = "'".implode("','", $mastertopid_arr)."'";
        // $topictures[$key] = "'".implode("','", $topicture_arr)."'";


        // }


        //$first =1 表示每周星期一为开始日期 0表示每周日为开始日期
        $first = 1;
        //获取当前周的第几天 周日是 0 周一到周六是 1 - 6
        $w = date('w', strtotime($date));
        //获取本周开始日期，如果$w是0，则表示周日，减去 6 天
        $week_start = date('Y-m-d', strtotime("$date -" . ($w ? $w - $first : 6) . ' days'));
        //本周结束日期
        $week_end = date('Y-m-d', strtotime("$week_start +6 days"));


        $enddate = $date . " 23:59:59";//本月本周本年的结束时间

        $this_week_start = $week_start . " 00:00:00"; //本周开始时间
        $this_week_num = ceil(abs(strtotime($this_week_start) - strtotime($enddate)) / (60 * 60 * 24)); //本周天数


        $last_week_start = date('Y-m-d', strtotime("$week_start -7 days")) . " 00:00:00"; //上周开始时间
        $last_week_end = date('Y-m-d', strtotime("$week_end -7 days")) . " 23:59:59"; //上周结束时间
        $last_week_num = '7';//上周天数


        $this_start_date = date('Y-m', strtotime($date)) . "-01 00:00:00";//本月开始
        $this_month_num = ceil(abs(strtotime($this_start_date) - strtotime($enddate)) / (60 * 60 * 24)); //本月天数

        $last_start_date = date('Y-m', strtotime('-1 month', strtotime($date))) . "-01 00:00:00";//上月开始
        $last_end_date = date('Y-m-d', strtotime('-1 day', strtotime('+1 month', strtotime($last_start_date)))) . " 23:59:59";//上月结束
        $last_month_num = ceil(abs(strtotime($last_start_date) - strtotime($last_end_date)) / (60 * 60 * 24)); //上月天数


        $last_yearmonth_start = date('Y-m', strtotime('-1 year', strtotime($date))) . "-01 00:00:00";//去年同月开始
        $last_yearmonth_end = date('Y-m-d', strtotime('-1 day', strtotime('+1 month', strtotime($last_yearmonth_start)))) . " 23:59:59";//去年同月结束
        $last_yearmonth_num = ceil(abs(strtotime($last_yearmonth_start) - strtotime($last_yearmonth_end)) / (60 * 60 * 24)); //去年同月天数

        $this_year_start = date('Y', strtotime($date)) . "-01-01 00:00:00";//本年开始，本年结束$enddate
        $this_year_num = ceil(abs(strtotime($this_year_start) - strtotime($enddate)) / (60 * 60 * 24)); //今年天数

        $last_year_start = date('Y', strtotime('-1 year', strtotime($date))) . "-01-01 00:00:00";//去年开始
        $last_year_end = date('Y', strtotime('-1 year', strtotime($date))) . "-12-31 23:59:59";//去年开始
        $last_year_num = ceil(abs(strtotime($last_year_start) - strtotime($last_year_end)) / (60 * 60 * 24)); //去年天数

        $list = array();

        foreach ($price_arr as $sck => $scv) {

            foreach ($scv as $k => $v) {
                $strlen = strlen($v);
                if ($strlen == 6) {
                    $idarr_6[] = $v;
                } elseif ($strlen == 7) {
                    $idarr_7[] = $v;
                }
            }
        }
        //目前都是七位，
        $idarr_7str = implode("','", $idarr_7);

        // $list[$sck][$k]=$v;
        $sql_br = "SELECT mastertopid,price FROM marketconditions WHERE  mastertopid in ('" . $idarr_7str . "') AND mconmanagedate >= '" . $date . " 00:00:00" . "' AND mconmanagedate <= '" . $enddate . "' group by mastertopid,mconmanagedate";
        $brprice_arr = $this->homeDao->query($sql_br);//本日
        foreach ($brprice_arr as $key => $value) {
            $brprice[$value['mastertopid']] = $value['price'];
        }


        $sql_bz = "SELECT mastertopid,price FROM marketconditions WHERE  mastertopid in ('" . $idarr_7str . "')  AND mconmanagedate >= '" . $this_week_start . "' AND mconmanagedate <= '" . $enddate . "' group by mastertopid,mconmanagedate";
        $bzprice = $this->homeDao->query($sql_bz);
        foreach ($bzprice as $key => $value) {
            $bzprice_arr[$value['mastertopid']][] = $value['price'];
        }
        foreach ($bzprice_arr as $key => $value) {
            $bzavg[$key] = round(array_sum($value) / count($value));//本周均价
        }
        //$bzavg =round($bzprice/$this_week_num);//本周均价
        // echo '<pre>';print_R($bzavg);exit;

        $sql_sz = "SELECT mastertopid,price FROM marketconditions WHERE  mastertopid in ('" . $idarr_7str . "')  AND mconmanagedate >= '" . $last_week_start . "' AND mconmanagedate <= '" . $last_week_end . "' group by mastertopid,mconmanagedate";
        $szprice = $this->homeDao->query($sql_sz);
        foreach ($szprice as $key => $value) {
            $szprice_arr[$value['mastertopid']][] = $value['price'];
        }
        foreach ($szprice_arr as $key => $value) {
            $szavg[$key] = round(array_sum($value) / count($value));//上周均价
        }


        $sql_by = "SELECT mastertopid,price FROM marketconditions WHERE  mastertopid in ('" . $idarr_7str . "')  AND mconmanagedate >= '" . $this_start_date . "' AND mconmanagedate <= '" . $enddate . "' group by mastertopid,mconmanagedate";
        $byprice = $this->homeDao->query($sql_by);

        foreach ($byprice as $key => $value) {
            $byprice_arr[$value['mastertopid']][] = $value['price'];
        }
        foreach ($byprice_arr as $key => $value) {
            $byavg[$key] = round(array_sum($value) / count($value));//本月均价
        }


        $sql_sy = "SELECT mastertopid,price FROM marketconditions WHERE  mastertopid in ('" . $idarr_7str . "')  AND mconmanagedate >= '" . $last_start_date . "' AND mconmanagedate <= '" . $last_end_date . "' group by mastertopid,mconmanagedate";
        $syprice = $this->homeDao->query($sql_sy);
        // $syavg =round($syprice/$last_month_num);//上月均价
        foreach ($syprice as $key => $value) {
            $syprice_arr[$value['mastertopid']][] = $value['price'];
        }
        foreach ($syprice_arr as $key => $value) {
            $syavg[$key] = round(array_sum($value) / count($value));//本月均价
        }
        // echo  '<pre>';print_R($syavg);exit;
        $sql_sny = "SELECT mastertopid,price FROM marketconditions WHERE  mastertopid in ('" . $idarr_7str . "')  AND mconmanagedate >= '" . $last_yearmonth_start . "' AND mconmanagedate <= '" . $last_yearmonth_end . "' group by mastertopid,mconmanagedate";
        $snyprice = $this->homeDao->query($sql_sny);
        // $snyavg =round($snyprice/$last_yearmonth_num);//去年同月均价

        foreach ($snyprice as $key => $value) {
            $snyprice_arr[$value['mastertopid']][] = $value['price'];
        }
        foreach ($snyprice_arr as $key => $value) {
            $snyavg[$key] = round(array_sum($value) / count($value));//本月均价
        }

        $sql_bn = "SELECT mastertopid,price FROM marketconditions WHERE   mastertopid in ('" . $idarr_7str . "')  AND mconmanagedate >= '" . $this_year_start . "' AND mconmanagedate <= '" . $enddate . "' group by mastertopid,mconmanagedate";
        $bnprice = $this->homeDao->query($sql_bn);
        // $bnavg =round($bnprice/$this_year_num);//本年均价

        foreach ($bnprice as $key => $value) {
            $bnprice_arr[$value['mastertopid']][] = $value['price'];
        }
        foreach ($bnprice_arr as $key => $value) {
            $bnavg[$key] = round(array_sum($value) / count($value));//本月均价
        }
// echo  '<pre>';print_R($syavg);exit;

        $sql_sn = "SELECT mastertopid,price FROM marketconditions WHERE  mastertopid in ('" . $idarr_7str . "')  AND mconmanagedate >= '" . $last_year_start . "' AND mconmanagedate <= '" . $last_year_end . "' group by mastertopid,mconmanagedate";
        $snprice = $this->homeDao->query($sql_sn);
        // $snavg =round($snprice/$last_year_num);
        foreach ($snprice as $key => $value) {
            $snprice_arr[$value['mastertopid']][] = $value['price'];
        }
        foreach ($snprice_arr as $key => $value) {
            $snavg[$key] = round(array_sum($value) / count($value));//上年均价
        }
        foreach ($price_arr as $sck => $scv) {

            foreach ($scv as $k => $v) {
                if ($k == '龙钢') {//龙钢求均价
                    $list[$sck][$k]['br'] = $brprice[$v];
                    $list[$sck][$k]['bz'] = $bzavg[$v];
                    $list[$sck][$k]['sz'] = $szavg[$v];
                    $list[$sck][$k]['by'] = $byavg[$v];
                    $list[$sck][$k]['sy'] = $syavg[$v];
                    $list[$sck][$k]['sny'] = $snyavg[$v];
                    $list[$sck][$k]['bn'] = $bnavg[$v];
                    $list[$sck][$k]['sn'] = $snavg[$v];

                } else {
                    if ($params['dc'] == 1) {
                        //其他钢厂为龙钢均价-钢厂均价的差价
                        $list[$sck][$k]['br'] = $this->zhangdie($list[$sck]['龙钢']['br'] - $brprice[$v], '-', 1);
                        $list[$sck][$k]['bz'] = $this->zhangdie($list[$sck]['龙钢']['bz'] - $bzavg[$v], '-', 1);
                        $list[$sck][$k]['sz'] = $this->zhangdie($list[$sck]['龙钢']['sz'] - $szavg[$v], '-', 1);
                        $list[$sck][$k]['by'] = $this->zhangdie($list[$sck]['龙钢']['by'] - $byavg[$v], '-', 1);
                        $list[$sck][$k]['sy'] = $this->zhangdie($list[$sck]['龙钢']['sy'] - $syavg[$v], '-', 1);
                        $list[$sck][$k]['sny'] = $this->zhangdie($list[$sck]['龙钢']['sny'] - $snyavg[$v], '-', 1);
                        $list[$sck][$k]['bn'] = $this->zhangdie($list[$sck]['龙钢']['bn'] - $bnavg[$v], '-', 1);
                        $list[$sck][$k]['sn'] = $this->zhangdie($list[$sck]['龙钢']['sn'] - $snavg[$v], '-', 1);
                    } else {
                        //其他钢厂为龙钢均价-钢厂均价的差价
                        $list[$sck][$k]['br'] = $this->zhangdie($list[$sck]['龙钢']['br'] - $brprice[$v], '-');
                        $list[$sck][$k]['bz'] = $this->zhangdie($list[$sck]['龙钢']['bz'] - $bzavg[$v], '-');
                        $list[$sck][$k]['sz'] = $this->zhangdie($list[$sck]['龙钢']['sz'] - $szavg[$v], '-');
                        $list[$sck][$k]['by'] = $this->zhangdie($list[$sck]['龙钢']['by'] - $byavg[$v], '-');
                        $list[$sck][$k]['sy'] = $this->zhangdie($list[$sck]['龙钢']['sy'] - $syavg[$v], '-');
                        $list[$sck][$k]['sny'] = $this->zhangdie($list[$sck]['龙钢']['sny'] - $snyavg[$v], '-');
                        $list[$sck][$k]['bn'] = $this->zhangdie($list[$sck]['龙钢']['bn'] - $bnavg[$v], '-');
                        $list[$sck][$k]['sn'] = $this->zhangdie($list[$sck]['龙钢']['sn'] - $snavg[$v], '-');
                    }

                }
            }
        }


        if ($params['dc'] == 1) {
            $this->scjc_export($date, $list);
            exit;
        }


        $this->assign('date', date('n月j日', strtotime($date)));
        $this->assign('date2', $date);
        $this->assign('list', $list);

        $this->assign('mode', $params['mode'] ? $params['mode'] : 1);
        $style_url = dirname(DC_URL . $_SERVER['PHP_SELF']);
        $this->assign("style_url", $style_url);
        $this->assign('GUID', $params['GUID']);
    }


    public function lwgprice($params)
    {
        $price_arr = array("全国" => '', "北京" => '3920231', "上海" => "0720431", "西安" => "5620231", "成都" => "532023q", "兰州" => "5720431", "郑州" => "3720436");
        $beizhu = array("全国" => '', "北京" => '河钢承钢', "上海" => "中天", "西安" => "陕钢", "成都" => "威钢", "兰州" => "酒钢", "郑州" => "晋钢");
        foreach ($price_arr as $k => $v) {

            $strlen = strlen($v);
            if ($strlen == 6) {
                $idarr_6[] = $v;
            } elseif ($strlen == 7) {
                $idarr_7[] = $v;
            }

        }


        if ($params['date'] == "") $date = date("Y-m-d");
        else    $date = $params['date'];


        //目前都是七位，
        $idarr_7str = implode("','", $idarr_7);

        // $list[$sck][$k]=$v;
        $sql_br = "SELECT mastertopid,price,pricemk,mconmanagedate FROM marketconditions WHERE  mastertopid in ('" . $idarr_7str . "') AND mconmanagedate >= '" . $date . " 00:00:00'  AND mconmanagedate <= '" . $date . " 23:59:59' group by mastertopid order by mconmanagedate desc";

        $brprice = $this->homeDao->query($sql_br);//本日

        foreach ($brprice as $key => $value) {
            $brprice_arr[$value['mastertopid']]['sc'] = $value['pricemk'];//市场价
            $brprice_arr[$value['mastertopid']]['wj'] = $value['price'];//网价
        }
        //上周四 当前价格-上周四价格 周涨跌
        //$first =1 表示每周星期一为开始日期 0表示每周日为开始日期
        $first = 1;
        //获取当前周的第几天 周日是 0 周一到周六是 1 - 6
        $w = date('w', strtotime($date));
        //获取本周开始日期，如果$w是0，则表示周日，减去 6 天
        $week_start = date('Y-m-d', strtotime("$date -" . ($w ? $w - $first : 6) . ' days'));
        //上周四日期
        $week_four = date('Y-m-d', strtotime("$week_start -4 days"));
        //上周一
        $week_one = date('Y-m-d', strtotime("$week_start -7 days"));
        if (file_get_contents(APP_URL_WWW . '/isholiday.php?date=' . $week_four) == "1" && file_get_contents(APP_URL_WWW . '/isholiday.php?date=' . $week_one) == "1") {
            $week_four = date('Y-m-d', strtotime("$week_four -7 days"));
            $week_one = date('Y-m-d', strtotime("$week_one -7 days"));
        }
        $sql_sz = "select * from( SELECT mastertopid,price,pricemk,mconmanagedate FROM marketconditions WHERE  mastertopid in ('" . $idarr_7str . "') AND mconmanagedate >= '" . $week_one . " 00:00:00'  AND mconmanagedate <= '" . $week_four . " 23:59:59' order by mconmanagedate desc) as m  group by m.mastertopid ";

        $szprice = $this->homeDao->query($sql_sz);

        foreach ($szprice as $key => $value) {
            $szprice_arr[$value['mastertopid']]['sc'] = $value['pricemk'];//市场价
            $szprice_arr[$value['mastertopid']]['wj'] = $value['price'];//网价
        }
        //上月末 当前价格-上月末价格 月涨跌
        $sql_sy = "select * from(SELECT mastertopid,price,pricemk,mconmanagedate FROM marketconditions WHERE  mastertopid in ('" . $idarr_7str . "') AND   mconmanagedate>'" . date('Y-m', strtotime('-1 month', strtotime($date))) . "-20 00:00:00" . "' AND mconmanagedate <'" . date('Y-m', strtotime($date)) . "-01 ' order by mconmanagedate desc) as m  group by m.mastertopid";

        $syprice = $this->homeDao->query($sql_sy);

        foreach ($syprice as $key => $value) {
            $syprice_arr[$value['mastertopid']]['sc'] = $value['pricemk'];//市场价
            $syprice_arr[$value['mastertopid']]['wj'] = $value['price'];//网价
        }


        //全国 28个城市均价
        $today_qg = $this->get_price_bytopid("072023,082023,112023,122023,172023,222023,242023,252023,282023,332023,352023,362023,372023,392023,402023,422023,432023,412023,452023,502023,512023,522023,532023,552023,542023,562023,572023,602023", $date, 1);
        $sz_qg = $this->get_price_bytopid("072023,082023,112023,122023,172023,222023,242023,252023,282023,332023,352023,362023,372023,392023,402023,422023,432023,412023,452023,502023,512023,522023,532023,552023,542023,562023,572023,602023", $date, 2);
        $sy_qg = $this->get_price_bytopid("072023,082023,112023,122023,172023,222023,242023,252023,282023,332023,352023,362023,372023,392023,402023,422023,432023,412023,452023,502023,512023,522023,532023,552023,542023,562023,572023,602023", $date, 3);
        //今天的均价
        $today_price_sum_sc = 0;
        foreach ($today_qg['sc'] as $key => $value) {
            $today_price_sum_sc = $today_price_sum_sc + $value;

        }
        $today_price_sum_wj = 0;
        foreach ($today_qg['wj'] as $key => $value) {
            $today_price_sum_wj = $today_price_sum_wj + $value;

        }
        $qg_today_avg_sc = round($today_price_sum_sc / count($today_qg['sc']));
        $qg_today_avg_wj = round($today_price_sum_wj / count($today_qg['wj']));

        //上周的均价
        $sz_price_sum_sc = 0;
        foreach ($sz_qg['sc'] as $key => $value) {
            $sz_price_sum_sc = $sz_price_sum_sc + $value;
        }
        $sz_price_sum_wj = 0;
        foreach ($sz_qg['wj'] as $key => $value) {
            $sz_price_sum_wj = $sz_price_sum_wj + $value;
        }

        $qg_sz_avg_sc = round($sz_price_sum_sc / count($sz_qg['sc']));
        $qg_sz_avg_wj = round($sz_price_sum_wj / count($sz_qg['wj']));
        //  echo $qg_sz_avg_wj;exit;
        //上月的均价
        $sy_price_sum_wj = 0;
        foreach ($sy_qg['wj'] as $key => $value) {
            $sy_price_sum_wj = $sy_price_sum_wj + $value;
        }
        $sy_price_sum_sc = 0;
        foreach ($sy_qg['sc'] as $key => $value) {
            $sy_price_sum_sc = $sy_price_sum_sc + $value;
        }
        $qg_sy_avg_sc = round($sy_price_sum_sc / count($sy_qg['sc']));
        $qg_sy_avg_wj = round($sy_price_sum_wj / count($sy_qg['wj']));


        // echo '<pre>';print_R($brprice_arr);print_R($szprice_arr);print_R($syprice_arr);exit;
        $data = array();
        foreach ($price_arr as $k => $v) {
            if ($k == '全国') {
                $data[base64_encode($k)]['sc'] = $qg_today_avg_sc;
                if (empty($qg_today_avg_sc)) {
                    $data[base64_encode($k)]['sc_zzd'] = '';
                    $data[base64_encode($k)]['sc_yzd'] = '';
                } else {
                    $data[base64_encode($k)]['sc_zzd'] = $qg_today_avg_sc - $qg_sz_avg_sc;
                    $data[base64_encode($k)]['sc_yzd'] = $qg_today_avg_sc - $qg_sy_avg_sc;
                }

                $data[base64_encode($k)]['wj'] = $qg_today_avg_wj;
                if (empty($qg_today_avg_wj)) {
                    $data[base64_encode($k)]['wj_zzd'] = '';
                    $data[base64_encode($k)]['wj_yzd'] = '';
                } else {
                    $data[base64_encode($k)]['wj_zzd'] = $qg_today_avg_wj - $qg_sz_avg_wj;
                    $data[base64_encode($k)]['wj_yzd'] = $qg_today_avg_wj - $qg_sy_avg_wj;
                }
                $data[base64_encode($k)]['bz'] = base64_encode($beizhu[$k]);
            } else {
                $data[base64_encode($k)]['sc'] = $brprice_arr[$v]['sc'];
                if (empty($brprice_arr[$v]['sc'])) {
                    $data[base64_encode($k)]['sc_zzd'] = '';
                    $data[base64_encode($k)]['sc_yzd'] = '';
                } else {
                    $data[base64_encode($k)]['sc_zzd'] = $brprice_arr[$v]['sc'] - $szprice_arr[$v]['sc'];
                    $data[base64_encode($k)]['sc_yzd'] = $brprice_arr[$v]['sc'] - $syprice_arr[$v]['sc'];
                }

                $data[base64_encode($k)]['wj'] = $brprice_arr[$v]['wj'];
                if (empty($brprice_arr[$v]['wj'])) {
                    $data[base64_encode($k)]['wj_zzd'] = '';
                    $data[base64_encode($k)]['wj_yzd'] = '';
                } else {
                    $data[base64_encode($k)]['wj_zzd'] = $brprice_arr[$v]['wj'] - $szprice_arr[$v]['wj'];
                    $data[base64_encode($k)]['wj_yzd'] = $brprice_arr[$v]['wj'] - $syprice_arr[$v]['wj'];
                }

                $data[base64_encode($k)]['bz'] = base64_encode($beizhu[$k]);
            }

        }


        $json_arr['data'] = $data;

        echo json_encode($json_arr);

        exit;

    }


    public function cityandwj_avg($params)
    {


        //对应id都是7位的
        $idarr_7 = array("5620231", "5613121", "5611032", "532023q", "5313121", "5311034", "5720431", "5713122", "5711031");
        // $beizhu=array("北京"=>'河钢承钢',"上海"=>"中天","西安"=>"陕钢","成都"=>"威钢","兰州"=>"酒钢","郑州"=>"晋钢");
        if ($params['date'] == "") $date = date("Y-m-d");
        else    $date = $params['date'];


        //目前都是七位，
        $idarr_7str = implode("','", $idarr_7);

        // $list[$sck][$k]=$v;
        $sql_br = "SELECT mastertopid,price,pricemk,mconmanagedate FROM marketconditions WHERE  mastertopid in ('" . $idarr_7str . "') AND mconmanagedate >= '" . $date . " 00:00:00'  AND mconmanagedate <= '" . $date . " 23:59:59' group by mastertopid order by mconmanagedate desc";

        $brprice = $this->homeDao->query($sql_br);//本日

        foreach ($brprice as $key => $value) {
            // $brprice_arr[$value['mastertopid']]['sc']=$value['pricemk'];//市场价
            // $brprice_arr[$value['mastertopid']]['wj']=$value['price'];//网价
            $brprice_arr[$value['mastertopid']] = $value['price'];//网价
        }
        //上周四 当前价格-上周四价格 周涨跌
        //$first =1 表示每周星期一为开始日期 0表示每周日为开始日期
        $first = 1;
        //获取当前周的第几天 周日是 0 周一到周六是 1 - 6
        $w = date('w', strtotime($date));
        //获取本周开始日期，如果$w是0，则表示周日，减去 6 天
        $week_start = date('Y-m-d', strtotime("$date -" . ($w ? $w - $first : 6) . ' days'));
        //上周四日期
        $week_four = date('Y-m-d', strtotime("$week_start -4 days"));
        //上周一
        $week_one = date('Y-m-d', strtotime("$week_start -7 days"));
        if (file_get_contents(APP_URL_WWW . '/isholiday.php?date=' . $week_four) == "1" && file_get_contents(APP_URL_WWW . '/isholiday.php?date=' . $week_one) == "1") {
            $week_four = date('Y-m-d', strtotime("$week_four -7 days"));
            $week_one = date('Y-m-d', strtotime("$week_one -7 days"));
        }
        $sql_sz = "select * from(SELECT mastertopid,price,pricemk,mconmanagedate FROM marketconditions WHERE  mastertopid in ('" . $idarr_7str . "') AND mconmanagedate >= '" . $week_one . " 00:00:00'  AND mconmanagedate <= '" . $week_four . " 23:59:59' order by mconmanagedate desc) as m group by m.mastertopid ";

        $szprice = $this->homeDao->query($sql_sz);

        foreach ($szprice as $key => $value) {
            // $szprice_arr[$value['mastertopid']]['sc']=$value['pricemk'];//市场价
            // $szprice_arr[$value['mastertopid']]['wj']=$value['price'];//网价
            $szprice_arr[$value['mastertopid']] = $value['price'];//网价
        }

        //全国 28个城市均价
        $lwg_arr = array('072023', '082023', '112023', '122023', '172023', '222023', '242023', '252023', '282023', '332023', '352023', '362023', '372023', '392023', '402023', '422023', '432023', '412023', '452023', '502023', '512023', '522023', '532023', '552023', '542023', '562023', '572023', '602023');
        $pl_arr = array('071312', '081312', '111312', '121312', '171312', '221312', '241312', '251312', '281312', '331312', '351312', '361312', '371312', '391312', '401312', '421312', '431312', '411312', '451312', '501312', '511312', '521312', '531312', '551312', '541312', '561312', '571312', '601312');
        $xc_arr = array('071103', '081103', '111103', '121103', '171103', '221103', '241103', '251103', '281103', '331103', '351103', '361103', '371103', '391103', '401103', '421103', '431103', '411103', '451103', '501103', '511103', '521103', '531103', '551103', '541103', '561103', '571103', '601103');

        //本日
        $today_qg = $this->get_price_bytopid("072023,082023,112023,122023,172023,222023,242023,252023,282023,332023,352023,362023,372023,392023,402023,422023,432023,412023,452023,502023,512023,522023,532023,552023,542023,562023,572023,602023,071312,081312,111312,121312,171312,221312,241312,251312,281312,331312,351312,361312,371312,391312,401312,421312,431312,411312,451312,501312,511312,521312,531312,551312,541312,561312,571312,601312,071103,081103,111103,121103,171103,221103,241103,251103,281103,331103,351103,361103,371103,391103,401103,421103,431103,411103,451103,501103,511103,521103,531103,551103,541103,561103,571103,601103", $date, 1);


        //上周
        $sz_qg = $this->get_price_bytopid("072023,082023,112023,122023,172023,222023,242023,252023,282023,332023,352023,362023,372023,392023,402023,422023,432023,412023,452023,502023,512023,522023,532023,552023,542023,562023,572023,602023,071312,081312,111312,121312,171312,221312,241312,251312,281312,331312,351312,361312,371312,391312,401312,421312,431312,411312,451312,501312,511312,521312,531312,551312,541312,561312,571312,601312,071103,081103,111103,121103,171103,221103,241103,251103,281103,331103,351103,361103,371103,391103,401103,421103,431103,411103,451103,501103,511103,521103,531103,551103,541103,561103,571103,601103", $date, 2);

        $lw_qg_avg_br = 0;
        $pl_qg_avg_br = 0;
        $gx_qg_avg_br = 0;
        $lw_qg_avg_sz = 0;
        $pl_qg_avg_sz = 0;
        $gx_qg_avg_sz = 0;

        $br_lwg_count = 0;
        $br_pl_count = 0;
        $br_xc_count = 0;
        $sz_lwg_count = 0;
        $sz_pl_count = 0;
        $sz_xc_count = 0;
        $today_price_sum_qglwgwj = 0;
        $sz_price_sum_qglwgwj = 0;
        foreach ($lwg_arr as $key => $value) {
            if (isset($today_qg['wj'][$value]) && $today_qg['wj'][$value] != '') {
                $today_price_sum_qglwgwj = $today_price_sum_qglwgwj + $today_qg['wj'][$value];
                $br_lwg_count = $br_lwg_count + 1;
            }
            if (isset($sz_qg['wj'][$value]) && $sz_qg['wj'][$value] != '') {
                $sz_price_sum_qglwgwj = $sz_price_sum_qglwgwj + $sz_qg['wj'][$value];
                $sz_lwg_count = $sz_lwg_count + 1;
            }
        }
        $today_price_sum_qgplwj = 0;
        $sz_price_sum_qgplwj = 0;
        foreach ($pl_arr as $key => $value) {
            if (isset($today_qg['wj'][$value]) && $today_qg['wj'][$value] != '') {
                $today_price_sum_qgplwj = $today_price_sum_qgplwj + $today_qg['wj'][$value];
                $br_pl_count = $br_pl_count + 1;
            }
            if (isset($sz_qg['wj'][$value]) && $sz_qg['wj'][$value] != '') {
                $sz_price_sum_qgplwj = $sz_price_sum_qgplwj + $sz_qg['wj'][$value];
                $sz_pl_count = $sz_pl_count + 1;
            }
        }
        $today_price_sum_qgxcwj = 0;
        $sz_price_sum_qgxcwj = 0;
        foreach ($xc_arr as $key => $value) {
            if (isset($today_qg['wj'][$value]) && $today_qg['wj'][$value] != '') {
                $today_price_sum_qgxcwj = $today_price_sum_qgxcwj + $today_qg['wj'][$value];
                $br_xc_count = $br_xc_count + 1;
            }
            if (isset($sz_qg['wj'][$value]) && $sz_qg['wj'][$value] != '') {
                $sz_price_sum_qgxcwj = $sz_price_sum_qgxcwj + $sz_qg['wj'][$value];
                $sz_xc_count = $sz_xc_count + 1;
            }
        }

        $lw_qg_avg_br = round($today_price_sum_qglwgwj / $br_lwg_count);
        $pl_qg_avg_br = round($today_price_sum_qgplwj / $br_pl_count);
        $gx_qg_avg_br = round($today_price_sum_qgxcwj / $br_xc_count);

        $lw_qg_avg_sz = round($sz_price_sum_qglwgwj / $sz_lwg_count);
        $pl_qg_avg_sz = round($sz_price_sum_qgplwj / $sz_pl_count);
        $gx_qg_avg_sz = round($sz_price_sum_qgxcwj / $sz_xc_count);

        $LW_QG = $lw_qg_avg_br - $lw_qg_avg_sz;
        $PL_QG = $pl_qg_avg_br - $pl_qg_avg_sz;
        $XC_QG = $gx_qg_avg_br - $gx_qg_avg_sz;


        // $target = $this->_dao->query("SELECT * FROM target_average_price_comparison WHERE year='".date('Y',strtotime($date))."'");
        // foreach ($target as $key => $value) {
        // 	$target_arr[$value['area']][$value['pz']]= $value['target'];
        // }

        $data = array();

        $data = array(
            '0' => array(//西安(陕钢)
                base64_encode('螺纹钢') => array("brqy" => $brprice_arr['5620231'], "brqg" => $lw_qg_avg_br, "area" => $brprice_arr['5620231'] - $szprice_arr['5620231'], "QG" => $LW_QG, "cyz" => $brprice_arr['5620231'] - $lw_qg_avg_br, "mbz" => $target_arr['西安(陕钢)']['螺纹钢']),
                base64_encode('盘螺') => array("brqy" => $brprice_arr['5613121'], "brqg" => $pl_qg_avg_br, "area" => $brprice_arr['5613121'] - $szprice_arr['5613121'], "QG" => $PL_QG, "cyz" => $brprice_arr['5613121'] - $pl_qg_avg_br, "mbz" => $target_arr['西安(陕钢)']['盘螺']),
                base64_encode('线材') => array("brqy" => $brprice_arr['5611032'], "brqg" => $gx_qg_avg_br, "area" => $brprice_arr['5611032'] - $szprice_arr['5611032'], "QG" => $XC_QG, "cyz" => $brprice_arr['5611032'] - $gx_qg_avg_br, "mbz" => '')
            ),
            '1' => array(//成都(威钢)
                base64_encode('螺纹钢') => array("brqy" => $brprice_arr['532023q'], "brqg" => $lw_qg_avg_br, "area" => $brprice_arr['532023q'] - $szprice_arr['532023q'], "QG" => $LW_QG, "cyz" => $brprice_arr['532023q'] - $lw_qg_avg_br, "mbz" => $target_arr['成都(威钢)']['螺纹钢']),
                base64_encode('盘螺') => array("brqy" => $brprice_arr['5313121'], "brqg" => $pl_qg_avg_br, "area" => $brprice_arr['5313121'] - $szprice_arr['5313121'], "QG" => $PL_QG, "cyz" => $brprice_arr['5313121'] - $pl_qg_avg_br, "mbz" => $target_arr['成都(威钢)']['盘螺']),
                base64_encode('线材') => array("brqy" => $brprice_arr['5311034'], "brqg" => $gx_qg_avg_br, "area" => $brprice_arr['5311034'] - $szprice_arr['5311034'], "QG" => $XC_QG, "cyz" => $brprice_arr['5311034'] - $gx_qg_avg_br, "mbz" => '')
            ),
            '2' => array(//兰州(酒钢)
                base64_encode('螺纹钢') => array("brqy" => $brprice_arr['5720431'], "brqg" => $lw_qg_avg_br, "area" => $brprice_arr['5720431'] - $szprice_arr['5720431'], "QG" => $LW_QG, "cyz" => $brprice_arr['5720431'] - $lw_qg_avg_br, "mbz" => $target_arr['兰州(酒钢)']['螺纹钢']),
                base64_encode('盘螺') => array("brqy" => $brprice_arr['5713122'], "brqg" => $pl_qg_avg_br, "area" => $brprice_arr['5713122'] - $szprice_arr['5713122'], "QG" => $PL_QG, "cyz" => $brprice_arr['5713122'] - $pl_qg_avg_br, "mbz" => $target_arr['兰州(酒钢)']['盘螺']),
                base64_encode('线材') => array("brqy" => $brprice_arr['5711031'], "brqg" => $gx_qg_avg_br, "area" => $brprice_arr['5711031'] - $szprice_arr['5711031'], "QG" => $XC_QG, "cyz" => $brprice_arr['5711031'] - $gx_qg_avg_br, "mbz" => '')
            ),
        );

        //第二张列表

        $all_price_id = array(
            'LW-XA' => '5620231',
            'LW-CD' => '532023q',
            'LW-LZ' => '5720431',
            // 'LW-QG'=>'072023,082023,112023,122023,172023,222023,242023,252023,282023,332023,352023,362023,372023,392023,402023,422023,432023,412023,452023,502023,512023,522023,532023,552023,542023,562023,572023,602023,071312,081312,111312,121312,171312,221312,241312,251312,281312,331312,351312,361312,371312,391312,401312,421312,431312,411312,451312,501312,511312,521312,531312,551312,541312,561312,571312,601312,071103,081103,111103,121103,171103,221103,241103,251103,281103,331103,351103,361103,371103,391103,401103,421103,431103,411103,451103,501103,511103,521103,531103,551103,541103,561103,571103,601103',
            // 'LW-QG'=>'072023,082023,112023,122023,172023,222023,242023,252023,282023,332023,352023,362023,372023,392023,402023,422023,432023,412023,452023,502023,512023,522023,532023,552023,542023,562023,572023,602023',
        );
        $arr_price = $this->get_all_price($all_price_id, $date);
        $year_five = date('Y', strtotime($date));
        $year_four = $year_five - 1;
        $year_three = $year_five - 2;
        $year_two = $year_five - 3;
        $year_one = $year_five - 4;

        $data2 = array($year_one => '', $year_two => '', $year_three => '', $year_four => '', $year_five => '',);

        foreach ($data2 as $key => $value) {
            //平均值
            if (!empty($arr_price['LW-QG'][$key])) {
                $xa_avg[$key] = round(array_sum($arr_price['LW-XA'][$key]) / count($arr_price['LW-XA'][$key]) - array_sum($arr_price['LW-QG'][$key]) / count($arr_price['LW-QG'][$key]));
                $cd_avg[$key] = round(array_sum($arr_price['LW-CD'][$key]) / count($arr_price['LW-CD'][$key]) - array_sum($arr_price['LW-QG'][$key]) / count($arr_price['LW-QG'][$key]));
                $lz_avg[$key] = round(array_sum($arr_price['LW-LZ'][$key]) / count($arr_price['LW-LZ'][$key]) - array_sum($arr_price['LW-QG'][$key]) / count($arr_price['LW-QG'][$key]));
            }

            //同日期差的最大最小值，以全国日期为标准来进行计算
            $xa_max_price = '';
            $cd_max_price = '';
            $lz_max_price = '';
            $xa_min_price = '';
            $cd_min_price = '';
            $lz_min_price = '';
            foreach ($arr_price['LW-QG'][$key] as $k => $v) {
                if (isset($arr_price['LW-XA'][$key][$k])) {
                    $xajc_price = $arr_price['LW-XA'][$key][$k] - $v;
                    if ($xa_max_price == '') $xa_max_price = $xajc_price;
                    else if ($xajc_price >= $xa_max_price) $xa_max_price = $xajc_price;

                    if ($xa_min_price == '') $xa_min_price = $xajc_price;
                    else if ($xajc_price <= $xa_min_price) $xa_min_price = $xajc_price;
                }
                if (isset($arr_price['LW-CD'][$key][$k])) {
                    $cdjc_price = $arr_price['LW-CD'][$key][$k] - $v;
                    if ($cd_max_price == '') $cd_max_price = $cdjc_price;
                    else if ($cdjc_price >= $cd_max_price) $cd_max_price = $cdjc_price;

                    if ($cd_min_price == '') $cd_min_price = $cdjc_price;
                    else if ($cdjc_price <= $cd_min_price) $cd_min_price = $cdjc_price;
                }
                if (isset($arr_price['LW-LZ'][$key][$k])) {
                    $lzjc_price = $arr_price['LW-LZ'][$key][$k] - $v;
                    if ($lz_max_price == '') $lz_max_price = $lzjc_price;
                    else if ($lzjc_price >= $lz_max_price) $lz_max_price = $lzjc_price;

                    if ($lz_min_price == '') $lz_min_price = $lzjc_price;
                    else if ($lzjc_price <= $lz_min_price) $lz_min_price = $lzjc_price;
                }
            }
            $xa_max[$key] = $xa_max_price;
            $cd_max[$key] = $cd_max_price;
            $lz_max[$key] = $lz_max_price;
            $xa_min[$key] = $xa_min_price;
            $cd_min[$key] = $cd_min_price;
            $lz_min[$key] = $lz_min_price;

        }
        $data_jc = array();
        $num = 0;
        foreach ($data2 as $key => $value) {
            if ($key == date('Y', strtotime($date))) {
                if (date('m', strtotime($date)) == '01') {
                    $dyear = $key . '年1月';
                } else {
                    $dyear = $key . '年1月-' . date('n', strtotime($date)) . '月';
                }
            } else {
                $dyear = $key . '年';
            }
            $data_jc[$num][base64_encode('西安')]['avg'] = $xa_avg[$key];
            $data_jc[$num][base64_encode('成都')]['avg'] = $cd_avg[$key];
            $data_jc[$num][base64_encode('兰州')]['avg'] = $lz_avg[$key];

            $data_jc[$num][base64_encode('西安')]['max'] = $xa_max[$key];
            $data_jc[$num][base64_encode('成都')]['max'] = $cd_max[$key];
            $data_jc[$num][base64_encode('兰州')]['max'] = $lz_max[$key];

            $data_jc[$num][base64_encode('西安')]['min'] = $xa_min[$key];
            $data_jc[$num][base64_encode('成都')]['min'] = $cd_min[$key];
            $data_jc[$num][base64_encode('兰州')]['min'] = $lz_min[$key];
            $year_arr[$num] = base64_encode($dyear);

            $num++;
        }


        $json_arr['data'] = $data;
        $json_arr['data_jc'] = $data_jc;
        $json_arr['year_arr'] = $year_arr;
        echo json_encode($json_arr);

        exit;


        $this->assign('save_hl', $params['save_hl']);
        $this->assign('date', date('n月j日', strtotime($date)));

        $this->assign('date2', $date);
        $this->assign('data', $data);
        $this->assign('data_jc', $data_jc);
        $this->assign('mode', $params['mode'] ? $params['mode'] : 1);
        $style_url = dirname(DC_URL . $_SERVER['PHP_SELF']);
        $this->assign("style_url", $style_url);
        $this->assign('GUID', $params['GUID']);
    }


    public function xacdandqg($params)
    {

        //对应id都是7位的
        $idarr_7 = array("5620231", "5613121", "5611032", "3920231", "3913321", "3911031", "0720431", "0713124", "0711039", "532023q", "5313121", "5311034");
        $all_price_id = array(
            'XA' => '5620231,5613121,5611032',
            'BJ' => '3920231,3913321,3911031',
            'SH' => '0720431,0713124,0711039',
            'CD' => '532023q,5313121,5311034',
        );
        if ($params['date'] == "") $date = date("Y-m-d");
        else    $date = $params['date'];

        //目前都是七位，
        $idarr_7str = implode("','", $idarr_7);

        $arr_price = $this->get_all_price_xacdandqg($all_price_id, $date);
        $year_five = date('Y', strtotime($date));
        $year_four = $year_five - 1;
        $year_three = $year_five - 2;
        $year_two = $year_five - 3;
        $year_one = $year_five - 4;

        //第一张表
        $data = array();
        if (empty($arr_price['5620231'][$year_five][$date]) || empty($arr_price['3920231'][$year_five][$date])) {
            $dif_lw_xb = '';
        } else {
            $dif_lw_xb = $arr_price['5620231'][$year_five][$date] - $arr_price['3920231'][$year_five][$date];
        }
        if (empty($arr_price['5620231'][$year_five][$date]) || empty($arr_price['0720431'][$year_five][$date])) {
            $dif_lw_xs = '';
        } else {
            $dif_lw_xs = $arr_price['5620231'][$year_five][$date] - $arr_price['0720431'][$year_five][$date];
        }

        if (empty($arr_price['5613121'][$year_five][$date]) || empty($arr_price['3913321'][$year_five][$date])) {
            $dif_pl_xb = '';
        } else {
            $dif_pl_xb = $arr_price['5613121'][$year_five][$date] - $arr_price['3913321'][$year_five][$date];
        }
        if (empty($arr_price['5613121'][$year_five][$date]) || empty($arr_price['0713124'][$year_five][$date])) {
            $dif_pl_xs = '';
        } else {
            $dif_pl_xs = $arr_price['5613121'][$year_five][$date] - $arr_price['0713124'][$year_five][$date];
        }

        if (empty($arr_price['5611032'][$year_five][$date]) || empty($arr_price['3911031'][$year_five][$date])) {
            $dif_xc_xb = '';
        } else {
            $dif_xc_xb = $arr_price['5611032'][$year_five][$date] - $arr_price['3911031'][$year_five][$date];
        }
        if (empty($arr_price['5611032'][$year_five][$date]) || empty($arr_price['0711039'][$year_five][$date])) {
            $dif_xc_xs = '';
        } else {
            $dif_xc_xs = $arr_price['5611032'][$year_five][$date] - $arr_price['0711039'][$year_five][$date];
        }


        if (empty($arr_price['532023q'][$year_five][$date]) || empty($arr_price['3920231'][$year_five][$date])) {
            $dif_lw_cb = '';
        } else {
            $dif_lw_cb = $arr_price['532023q'][$year_five][$date] - $arr_price['3920231'][$year_five][$date];
        }
        if (empty($arr_price['532023q'][$year_five][$date]) || empty($arr_price['0720431'][$year_five][$date])) {
            $dif_lw_cs = '';
        } else {
            $dif_lw_cs = $arr_price['532023q'][$year_five][$date] - $arr_price['0720431'][$year_five][$date];
        }

        if (empty($arr_price['5313121'][$year_five][$date]) || empty($arr_price['3913321'][$year_five][$date])) {
            $dif_pl_cb = '';
        } else {
            $dif_pl_cb = $arr_price['5313121'][$year_five][$date] - $arr_price['3913321'][$year_five][$date];
        }
        if (empty($arr_price['5313121'][$year_five][$date]) || empty($arr_price['0713124'][$year_five][$date])) {
            $dif_pl_cs = '';
        } else {
            $dif_pl_cs = $arr_price['5313121'][$year_five][$date] - $arr_price['0713124'][$year_five][$date];
        }

        if (empty($arr_price['5311034'][$year_five][$date]) || empty($arr_price['3911031'][$year_five][$date])) {
            $dif_xc_cb = '';
        } else {
            $dif_xc_cb = $arr_price['5311034'][$year_five][$date] - $arr_price['3911031'][$year_five][$date];
        }
        if (empty($arr_price['5311034'][$year_five][$date]) || empty($arr_price['0711039'][$year_five][$date])) {
            $dif_xc_cs = '';
        } else {
            $dif_xc_cs = $arr_price['5311034'][$year_five][$date] - $arr_price['0711039'][$year_five][$date];
        }
        $data = array(
            '0' => array(
                base64_encode('品种') => array(//标题栏
                    base64_encode('西安') => base64_encode('西安'),
                    base64_encode('北京') => base64_encode('北京'),
                    base64_encode('上海') => base64_encode('上海'),
                    base64_encode('西安-北京') => base64_encode('西安-北京'),
                    base64_encode('西安-上海') => base64_encode('西安-上海'),
                ),
                base64_encode('螺纹钢') => array(
                    base64_encode('西安') => $arr_price['5620231'][$year_five][$date],
                    base64_encode('北京') => $arr_price['3920231'][$year_five][$date],
                    base64_encode('上海') => $arr_price['0720431'][$year_five][$date],
                    base64_encode('西安-北京') => $dif_lw_xb,
                    base64_encode('西安-上海') => $dif_lw_xs,
                ),
                base64_encode('盘螺') => array(
                    base64_encode('西安') => $arr_price['5613121'][$year_five][$date],
                    base64_encode('北京') => $arr_price['3913321'][$year_five][$date],
                    base64_encode('上海') => $arr_price['0713124'][$year_five][$date],
                    base64_encode('西安-北京') => $dif_pl_xb,
                    base64_encode('西安-上海') => $dif_pl_xs,
                ),
                base64_encode('线材') => array(
                    base64_encode('西安') => $arr_price['5611032'][$year_five][$date],
                    base64_encode('北京') => $arr_price['3911031'][$year_five][$date],
                    base64_encode('上海') => $arr_price['0711039'][$year_five][$date],
                    base64_encode('西安-北京') => $dif_xc_xb,
                    base64_encode('西安-上海') => $dif_xc_xs,
                ),
            ),
            '1' => array(

                base64_encode('品种') => array(//标题栏
                    base64_encode('成都') => base64_encode('成都'),
                    base64_encode('北京') => base64_encode('北京'),
                    base64_encode('上海') => base64_encode('上海'),
                    base64_encode('成都-北京') => base64_encode('成都-北京'),
                    base64_encode('成都-上海') => base64_encode('成都-上海'),
                ),
                base64_encode('螺纹钢') => array(
                    base64_encode('成都') => $arr_price['532023q'][$year_five][$date],
                    base64_encode('北京') => $arr_price['3920231'][$year_five][$date],
                    base64_encode('上海') => $arr_price['0720431'][$year_five][$date],
                    base64_encode('成都-北京') => $dif_lw_cb,
                    base64_encode('成都-上海') => $dif_lw_cs,
                ),
                base64_encode('盘螺') => array(
                    base64_encode('成都') => $arr_price['5313121'][$year_five][$date],
                    base64_encode('北京') => $arr_price['3913321'][$year_five][$date],
                    base64_encode('上海') => $arr_price['0713124'][$year_five][$date],
                    base64_encode('成都-北京') => $dif_pl_cb,
                    base64_encode('成都-上海') => $dif_pl_cs,
                ),
                base64_encode('线材') => array(
                    base64_encode('成都') => $arr_price['5311034'][$year_five][$date],
                    base64_encode('北京') => $arr_price['3911031'][$year_five][$date],
                    base64_encode('上海') => $arr_price['0711039'][$year_five][$date],
                    base64_encode('成都-北京') => $dif_xc_cb,
                    base64_encode('成都-上海') => $dif_xc_cs,
                ),
            ),
        );


        $data2 = array($year_one => '', $year_two => '', $year_three => '', $year_four => '', $year_five => '',);


        foreach ($data2 as $key => $value) {
            //平均值
            $xa_bj_avg[$key] = round(array_sum($arr_price['5620231'][$key]) / count($arr_price['5620231'][$key]) - array_sum($arr_price['3920231'][$key]) / count($arr_price['3920231'][$key]));
            $cd_bj_avg[$key] = round(array_sum($arr_price['532023q'][$key]) / count($arr_price['532023q'][$key]) - array_sum($arr_price['3920231'][$key]) / count($arr_price['3920231'][$key]));
            $xa_sh_avg[$key] = round(array_sum($arr_price['5620231'][$key]) / count($arr_price['5620231'][$key]) - array_sum($arr_price['0720431'][$key]) / count($arr_price['0720431'][$key]));
            $cd_sh_avg[$key] = round(array_sum($arr_price['532023q'][$key]) / count($arr_price['532023q'][$key]) - array_sum($arr_price['0720431'][$key]) / count($arr_price['0720431'][$key]));
            //同日期差的最大最小值，以北京或者上海日期为标准来进行计算
            $xa_bj_max_price = '';
            $cd_sh_max_price = '';

            $xa_bj_min_price = '';
            $cd_sh_min_price = '';
            //与北京对比
            foreach ($arr_price['3920231'][$key] as $k => $v) {
                if (isset($arr_price['5620231'][$key][$k])) {
                    $xajc_price = $arr_price['5620231'][$key][$k] - $v;
                    if ($xa_bj_max_price == '') $xa_bj_max_price = $xajc_price;
                    else if ($xajc_price >= $xa_bj_max_price) $xa_bj_max_price = $xajc_price;

                    if ($xa_bj_min_price == '') $xa_bj_min_price = $xajc_price;
                    else if ($xajc_price <= $xa_bj_min_price) $xa_bj_min_price = $xajc_price;
                }
                if (isset($arr_price['532023q'][$key][$k])) {
                    $cdjc_price = $arr_price['532023q'][$key][$k] - $v;
                    if ($cd_bj_max_price == '') $cd_bj_max_price = $cdjc_price;
                    else if ($cdjc_price >= $cd_bj_max_price) $cd_bj_max_price = $cdjc_price;

                    if ($cd_bj_min_price == '') $cd_bj_min_price = $cdjc_price;
                    else if ($cdjc_price <= $cd_bj_min_price) $cd_bj_min_price = $cdjc_price;
                }
            }
            //与上海对比
            foreach ($arr_price['0720431'][$key] as $k => $v) {
                if (isset($arr_price['5620231'][$key][$k])) {
                    $xajc_price = $arr_price['5620231'][$key][$k] - $v;
                    if ($xa_sh_max_price == '') $xa_sh_max_price = $xajc_price;
                    else if ($xajc_price >= $xa_sh_max_price) $xa_sh_max_price = $xajc_price;

                    if ($xa_sh_min_price == '') $xa_sh_min_price = $xajc_price;
                    else if ($xajc_price <= $xa_sh_min_price) $xa_sh_min_price = $xajc_price;
                }
                if (isset($arr_price['532023q'][$key][$k])) {
                    $cdjc_price = $arr_price['532023q'][$key][$k] - $v;
                    if ($cd_sh_max_price == '') $cd_sh_max_price = $cdjc_price;
                    else if ($cdjc_price >= $cd_sh_max_price) $cd_sh_max_price = $cdjc_price;

                    if ($cd_sh_min_price == '') $cd_sh_min_price = $cdjc_price;
                    else if ($cdjc_price <= $cd_sh_min_price) $cd_sh_min_price = $cdjc_price;
                }
            }

            $xa_bj_max[$key] = $xa_bj_max_price;
            $xa_sh_max[$key] = $xa_sh_max_price;
            $cd_bj_max[$key] = $cd_bj_max_price;
            $cd_sh_max[$key] = $cd_sh_max_price;

            $xa_bj_min[$key] = $xa_bj_min_price;
            $cd_bj_min[$key] = $cd_bj_min_price;
            $xa_sh_min[$key] = $xa_sh_min_price;
            $cd_sh_min[$key] = $cd_sh_min_price;

        }

        $data_jc = array();
        foreach ($data2 as $key => $value) {
            if ($key == date('Y', strtotime($date))) {
                if (date('m', strtotime($date)) == '01') {
                    $dyear = $key . '年1月';
                } else {
                    $dyear = $key . '年1月-' . date('n', strtotime($date)) . '月';
                }
            } else {
                $dyear = $key . '年';
            }
            // $data_jc['bj'][$dyear]['西安']['avg']=$xa_bj_avg[$key];
            // $data_jc['bj'][$dyear]['西安']['max']=$xa_bj_max[$key];
            // $data_jc['bj'][$dyear]['西安']['min']=$xa_bj_min[$key];
            // $data_jc['bj'][$dyear]['成都']['avg']=$cd_bj_avg[$key];
            // $data_jc['bj'][$dyear]['成都']['max']=$cd_bj_max[$key];
            // $data_jc['bj'][$dyear]['成都']['min']=$cd_bj_min[$key];

            // $data_jc['sh'][$dyear]['西安']['avg']=$xa_sh_avg[$key];
            // $data_jc['sh'][$dyear]['西安']['max']=$xa_sh_max[$key];
            // $data_jc['sh'][$dyear]['西安']['min']=$xa_sh_min[$key];
            // $data_jc['sh'][$dyear]['成都']['avg']=$cd_sh_avg[$key];
            // $data_jc['sh'][$dyear]['成都']['max']=$cd_sh_max[$key];
            // $data_jc['sh'][$dyear]['成都']['min']=$cd_sh_min[$key];

            $data_jc[base64_encode($dyear)] = array(
                base64_encode('西安') => array(
                    'bj_avg' => $xa_bj_avg[$key],
                    'bj_max' => $xa_bj_max[$key],
                    "bj_min" => $xa_bj_min[$key],
                    'sh_avg' => $cd_bj_avg[$key],
                    'sh_max' => $cd_bj_max[$key],
                    "sh_min" => $cd_bj_min[$key],
                ),
                base64_encode('成都') => array(
                    'bj_avg' => $cd_bj_avg[$key],
                    'bj_max' => $cd_bj_max[$key],
                    "bj_min" => $cd_bj_min[$key],
                    'sh_avg' => $cd_sh_avg[$key],
                    'sh_max' => $cd_sh_max[$key],
                    "sh_min" => $cd_sh_min[$key],
                ),

            );
        }


        $json_arr['data'] = $data;
        $json_arr['data_jc'] = $data_jc;

        echo json_encode($json_arr);
        exit;

    }

    public function fourcity($params)
    {

        // $year=date('Y',strtotime($params['date']));
        $managedate_sql = "   and managedate>='" . ($year - 4) . "-01-01 00:00' and managedate<='" . $params['date'] . " 23:59' ";
        // 京沪宁泉均价=Avg（3920231，0720431，1120431永钢，1720242永锋）
        // 西安网价均价id=5620231
        // 成都网价均价id=532023q
        $fouravg = array();
        $xaavg = array();
        $cdavg = array();

        $year_five = date('Y', strtotime($params['date']));
        $year_four = $year_five - 1;
        $year_three = $year_five - 2;
        $year_two = $year_five - 3;
        $year_one = $year_five - 4;

        $timedate = array($year_one => '', $year_two => '', $year_three => '', $year_four => '', $year_five => '',);

        foreach ($timedate as $key => $value) {
            $sql = "select avg(marketconditions.price) as avg from marketconditions where mastertopid in ('3920231','0720431','1120431','1720242')    and mconmanagedate>='" . $key . "-01-01 00:00' and mconmanagedate<'" . ($key + 1) . "-01-01 00:00' ";
            $row_price = $this->homeDao->getone($sql);
            $fouravg[$key] = round($row_price);

            $sql2 = "select avg(marketconditions.price) as avg from marketconditions where mastertopid in ('5620231')    and mconmanagedate>='" . $key . "-01-01 00:00' and mconmanagedate<'" . ($key + 1) . "-01-01 00:00' ";
            $row_price2 = $this->homeDao->getone($sql2);
            $xaavg[$key] = round($row_price2);

            $sql3 = "select avg(marketconditions.price) as avg from marketconditions where mastertopid in ('5620231')    and mconmanagedate>='" . $key . "-01-01 00:00' and mconmanagedate<'" . ($key + 1) . "-01-01 00:00' ";
            $row_price3 = $this->homeDao->getone($sql3);
            $cdavg[$key] = round($row_price3);
        }


        /*
				$sql="select marketconditions.price,DATE_FORMAT(managedate,'%Y-%m-%d') as managedate,marketconditions.mastertopid as arrid,year(managedate) as year from marketconditions,marketrecord where mastertopid in ('3920231','0720431','1120431','1720242','5620231','532023q') $managedate_sql and marketconditions.marketrecordid=marketrecord.id ";
				$row_price = $this->homeDao->query($sql);
				// echo"<pre>";print_r($table_arr);
				//数据分组，按区域，年划分
				$fourcity_arr=array();
				$four_arr=array();
				$cd_arr=array();
				$xa_arr=array();
				foreach ($row_price as $key => $value) {
					$price = $value['price'];
					if (str_replace("-", "xx", $price) == $price)
					{
						$price = $price;
					}
					else
					{
						$a = $price;
						$price = ceil((substr($a, 0, strpos($a, "-")) + substr($a, strpos($a, "-") + 1)) / 20) * 10;
					}

					 if($value['arrid']=='3920231'||$value['arrid']=='0720431'||$value['arrid']=='1120431'||$value['arrid']=='1720242'){
						$fourcity_arr[$value['year']][$value['managedate']][$value['arrid']]=$price;
					 }else if($value['arrid']=='5620231'){
						$xa_arr[$value['year']][$value['managedate']]=$price;
					 }else if($value['arrid']=='532023q'){
						$cd_arr[$value['year']][$value['managedate']]=$price;
					 }
				}
				//四个城市进行处理，求每日均价

				foreach ($fourcity_arr as $yk => $yv) {
					foreach ($yv as $rk => $rv) {
							$all_price='';
							$all_count='';
								foreach ($rv as $k => $v) {
									if(!empty($v)){
										$all_price=$all_price+$v;
										$all_count=$all_count+1;
									}
								}
							$four_arr[$yk][$rk]=round($all_price/$all_count);
						}
					}
				//根据每日价格算每年平均价
				foreach ($four_arr as $yk => $yv) {
					$all_price='';
					$all_count='';
					foreach ($yv as $rk => $rv) {
							if(!empty($rv)){
								$all_price=$all_price+$rv;
								$all_count=$all_count+1;
							}
						}
					$fouravg[$yk]=round($all_price/$all_count);
				}
				foreach ($xa_arr as $yk => $yv) {
					$all_price='';
					$all_count='';
					foreach ($yv as $rk => $rv) {
							if(!empty($rv)){
								$all_price=$all_price+$rv;
								$all_count=$all_count+1;
							}
						}
					$xaavg[$yk]=round($all_price/$all_count);
				}
				foreach ($cd_arr as $yk => $yv) {
					$all_price='';
					$all_count='';
					foreach ($yv as $rk => $rv) {
							if(!empty($rv)){
								$all_price=$all_price+$rv;
								$all_count=$all_count+1;
							}
						}
					$cdavg[$yk]=round($all_price/$all_count);
				}*/
        $arr_price['four'] = $fouravg;
        $arr_price['cd'] = $cdavg;
        $arr_price['xa'] = $xaavg;

        echo json_encode($arr_price);

    }

    public function target($params)
    {

        if ($params['year'] == "") {
            $year = date("Y");
        } else {
            $year = $params['year'];
            $where = " where year='" . $year . "' ";
        }
        $total = $this->_dao->getOne("SELECT COUNT(id) as c FROM target_average_price_comparison $where");
        $page = $params['page'] == '' ? 1 : $params['page'];
        $url = "sgscjcprice.php";
        $per = 25;
        $start = ($page - 1) * $per;
        unset($params['page']);
        $pagebar = pagebar($url, $params, $per, $page, $total);
        $this->assign("pagebar", $pagebar);

        $sql = "SELECT * FROM target_average_price_comparison $where order by area limit $start, $per";

        $data = $this->_dao->query($sql);

        $this->assign('data', $data);
        $this->assign('year', $year);
        $this->assign('mode', $params['mode'] ? $params['mode'] : 1);
        $style_url = dirname(DC_URL . $_SERVER['PHP_SELF']);
        $this->assign("style_url", $style_url);
        $this->assign('GUID', $params['GUID']);
    }

    public function addtarget($params)
    {

        if (isset($params['id']) && !empty($params['id'])) {

            $sql = "SELECT * FROM target_average_price_comparison where id ='" . $params['id'] . "'";

            $data = $this->_dao->getrow($sql);

            $this->assign('data', $data);
        }


        $this->assign('mode', $params['mode'] ? $params['mode'] : 1);
        $style_url = dirname(DC_URL . $_SERVER['PHP_SELF']);
        $this->assign("style_url", $style_url);
        $this->assign('GUID', $params['GUID']);
    }

    public function inserttarget($params)
    {

        // $area=iconv("UTF-8","GB2312",$params['area']);
        // $pz=iconv("UTF-8","GB2312",$params['pz']);

        $area = $params['area'];
        $pz = $params['pz'];

        $year = $this->_dao->getone("select year from target_average_price_comparison where id!='" . $params['id'] . "' and year='" . $params['year'] . "' and area='" . $area . "' and pz='" . $pz . "' ");

        if ($year) {
            alert('所选年份的区域品种的目标值已经设定，请重新设置');
            goback();
            exit;
        }


        if (isset($params['id']) && !empty($params['id'])) {
            $this->_dao->execute("update target_average_price_comparison set area='" . $area . "',pz='" . $pz . "',year='" . $params['year'] . "',target='" . $params['target'] . "' ");

        } else {
            $this->_dao->execute("insert into target_average_price_comparison (area,year,pz,target)values('" . $area . "','" . $params['year'] . "','" . $pz . "','" . $params['target'] . "')");
        }
        alert('设置成功');
        gourl("sgscjcprice.php?view=target");
    }

    public function deltarget($params)
    {
        $this->_dao->execute("delete from target_average_price_comparison where id='" . $params['id'] . "' ");

        echo 1;
    }

    function getjsonarr($data)
    {
        $data = html_entity_decode($data);
        $data = str_replace(" ", "+", $data);
        $data_arr = json_decode($data, true);
        return $data_arr;
    }


    // 根据日期和ID取价格
    function get_price_bytopid($citystrxh, $tdateday, $type)
    {


        if ($type == 1) {
            $managedate_sql = "   and managedate>'" . $tdateday . " 00:00' and managedate<'" . $tdateday . " 23:59' ";
        }
        if ($type == 2) {
            $first = 1;
            //获取当前周的第几天 周日是 0 周一到周六是 1 - 6
            $w = date('w', strtotime($tdateday));
            //获取本周开始日期，如果$w是0，则表示周日，减去 6 天
            $week_start = date('Y-m-d', strtotime("$tdateday -" . ($w ? $w - $first : 6) . ' days'));
            //上周四日期
            $week_four = date('Y-m-d', strtotime("$week_start -4 days"));
            //上周一
            $week_one = date('Y-m-d', strtotime("$week_start -7 days"));

            if (file_get_contents(APP_URL_WWW . '/isholiday.php?date=' . $week_four) == "1" && file_get_contents(APP_URL_WWW . '/isholiday.php?date=' . $week_one) == "1") {
                $week_four = date('Y-m-d', strtotime("$week_four -7 days"));
                $week_one = date('Y-m-d', strtotime("$week_one -7 days"));
            }

            $managedate_sql = "   and managedate>'" . $week_one . " 00:00' and managedate<'" . $week_four . " 23:59' ";
        }
        if ($type == 3) {

            $managedate_sql = "   and mconmanagedate>'" . date('Y-m', strtotime('-1 month', strtotime($tdateday))) . "-20 00:00:00" . "' AND mconmanagedate <'" . date('Y-m', strtotime($tdateday)) . "-01 ' ";
        }
        $array = explode(',', $citystrxh);

        foreach ($array as $k => $v) {
            if (strlen($v) == 6) {
                $top_6[] = "'" . $v . "'";
            } else {
                $top_7[] = "'" . $v . "'";
            }
        }
        $xhxxx_6 = "(" . implode(',', $top_6) . ")";
        if (is_array($top_7) && !empty($top_7)) {
            $xhxxx_7 = "(" . implode(',', $top_7) . ")";
            // 当天价格
            $sql = "(select * from (select marketconditions.price,marketconditions.oldprice,marketconditions.pricemk,managedate,marketconditions.topicture as arrid from marketconditions,marketrecord where topicture in " . $xhxxx_6 . " $managedate_sql and marketconditions.marketrecordid=marketrecord.id ordey by managedate desc) as m group by m.arrid) union (select * from (select marketconditions.price,marketconditions.oldprice,max(managedate),marketconditions.mastertopid as arrid from marketconditions,marketrecord where mastertopid in " . $xhxxx_7 . " $managedate_sql' and marketconditions.marketrecordid=marketrecord.id   ordey by managedate desc) as m group by m.arrid) ";
        } else {
            $sql = "select * from (select marketconditions.price,marketconditions.marketrecordid,marketconditions.pricemk,managedate,marketconditions.topicture as arrid from marketconditions,marketrecord where topicture in " . $xhxxx_6 . " $managedate_sql and marketconditions.marketrecordid=marketrecord.id order by managedate desc) as m group by m.arrid ";

        }
        //   if($type==1){echo $sql;exit;}
        //  if($type==2){echo $sql;exit;}
        //    echo $sql;echo '<br>';
        $row_price = $this->homeDao->query($sql);

        $i = 0;
        foreach ($row_price as $key => $value) {
            $price = $value['price'];
            if (str_replace("-", "xx", $price) == $price) {
                $price = $price;
            } else {
                $a = $price;
                $price = ceil((substr($a, 0, strpos($a, "-")) + substr($a, strpos($a, "-") + 1)) / 20) * 10;
            }
            $arr_price['wj'][$value['arrid']] = $price;

            $price_mk = $value['pricemk'];
            if (str_replace("-", "xx", $price_mk) == $price_mk) {
                $price_mk = $price_mk;
            } else {
                $b = $price_mk;
                $price = ceil((substr($b, 0, strpos($a, "-")) + substr($b, strpos($b, "-") + 1)) / 20) * 10;
            }
            $arr_price['sc'][$value['arrid']] = $price_mk;
            $i++;
        }

        return $arr_price;
    }


    function get_all_price($all_price_id, $tdateday)
    {
        // $all_price_id=array(
        // 	'LW-XA'=>'5620231',
        // 	'LW-CD'=>'532023q',
        // 	'LW-LZ'=>'5720431',
        // 	'LW-QG'=>'072023,082023,112023,122023,172023,222023,242023,252023,282023,332023,352023,362023,372023,392023,402023,422023,432023,412023,452023,502023,512023,522023,532023,552023,542023,562023,572023,602023,071312,081312,111312,121312,171312,221312,241312,251312,281312,331312,351312,361312,371312,391312,401312,421312,431312,411312,451312,501312,511312,521312,531312,551312,541312,561312,571312,601312,071103,081103,111103,121103,171103,221103,241103,251103,281103,331103,351103,361103,371103,391103,401103,421103,431103,411103,451103,501103,511103,521103,531103,551103,541103,561103,571103,601103',
        // );
        $year = date('Y', strtotime($tdateday));
        $managedate_sql = "   and managedate>='" . ($year - 4) . "-01-01 00:00' and managedate<='" . $tdateday . " 23:59' ";

        // $qg_array=explode(',',$all_price_id['LW-QG']);
        $top_7[] = "'" . $all_price_id['LW-XA'] . "'";
        $top_7[] = "'" . $all_price_id['LW-CD'] . "'";
        $top_7[] = "'" . $all_price_id['LW-LZ'] . "'";
        // foreach($qg_array as $k=>$v)
        // {
        // 	if(strlen($v)==6)
        // 	{
        // 		$top_6[]="'".$v."'";
        // 	}
        // 	else
        // 	{
        // 		$top_7[]="'".$v."'";
        // 	}
        // }
        //全国全是6位
        // $xhxxx_6 = "(" . implode(',',$top_6) . ")";

        $xhxxx_7 = "(" . implode(',', $top_7) . ")";

        // $sql="(select marketconditions.price,DATE_FORMAT(managedate,'%Y-%m-%d') as managedate,marketconditions.topicture as arrid,year(managedate) as year from marketconditions,marketrecord where topicture in " .$xhxxx_6. " $managedate_sql and marketconditions.marketrecordid=marketrecord.id ) union (select marketconditions.price,DATE_FORMAT(managedate,'%Y-%m-%d') as managedate,marketconditions.mastertopid as arrid,year(managedate) as year from marketconditions,marketrecord where mastertopid in " .$xhxxx_7. " $managedate_sql and marketconditions.marketrecordid=marketrecord.id )";
        $sql = "(select marketconditions.price,DATE_FORMAT(managedate,'%Y-%m-%d') as managedate,marketconditions.mastertopid as arrid,year(managedate) as year from marketconditions,marketrecord where mastertopid in " . $xhxxx_7 . " $managedate_sql and marketconditions.marketrecordid=marketrecord.id )";
        $row_price = $this->homeDao->query($sql);


        //数据分组，按区域，年划分
        $xa_arr = array();
        $cd_arr = array();
        $lz_arr = array();
        // $qg_allarr=array();
        // $qg_arr=array();
        foreach ($row_price as $key => $value) {
            $price = $value['price'];
            if (str_replace("-", "xx", $price) == $price) {
                $price = $price;
            } else {
                $a = $price;
                $price = ceil((substr($a, 0, strpos($a, "-")) + substr($a, strpos($a, "-") + 1)) / 20) * 10;
            }

            if ($value['arrid'] == $all_price_id['LW-XA']) {
                $xa_arr[$value['year']][$value['managedate']] = $price;
            } else if ($value['arrid'] == $all_price_id['LW-CD']) {
                $cd_arr[$value['year']][$value['managedate']] = $price;
            } else if ($value['arrid'] == $all_price_id['LW-LZ']) {
                $lz_arr[$value['year']][$value['managedate']] = $price;
            } else {
                // $qg_allarr[$value['year']][$value['managedate']][$value['arrid']]=$price;
            }
        }

        //全国28个城市进行处理，求每日均价

        // foreach ($qg_allarr as $yk => $yv) {
        // 	foreach ($yv as $rk => $rv) {
        // 			$all_price='';
        // 			$all_count='';
        // 				foreach ($rv as $k => $v) {
        // 					if(!empty($v)){
        // 						$all_price=$all_price+$v;
        // 						$all_count=$all_count+1;
        // 					}
        // 				}
        // 			$qg_arr[$yk][$rk]=round($all_price/$all_count);
        // 		}
        // 	}
        //取全国每日均价
        $sql = "select price,pricemk,DATE_FORMAT(managedate,'%Y-%m-%d') as managedate,type,year(managedate) as year from marketconditions_qgprice    where  type=1 and managedate>='" . ($year - 4) . "-01-01' and managedate<='" . $tdateday . "' ";
        $qg_avg_arr = $this->homeDao->query($sql);
        foreach ($qg_avg_arr as $key => $value) {
            $qg_avg[$value['year']][$value['managedate']] = $value['price'];
        }

        $arr_price['LW-XA'] = $xa_arr;
        $arr_price['LW-CD'] = $cd_arr;
        $arr_price['LW-LZ'] = $lz_arr;
        // $arr_price['LW-QG']=$qg_arr;
        $arr_price['LW-QG'] = $qg_avg;

        return $arr_price;
    }

    function get_all_price_xacdandqg($all_price_id, $tdateday)
    {

        $year = date('Y', strtotime($tdateday));
        $managedate_sql = "   and managedate>='" . ($year - 4) . "-01-01 00:00' and managedate<='" . $tdateday . " 23:59' ";


        foreach ($all_price_id as $k => $v) {
            $array = explode(',', $v);
            foreach ($array as $k2 => $v2) {
                if (strlen($v2) == 6) {
                    $top_6[] = "'" . $v2 . "'";
                } else {
                    $top_7[] = "'" . $v2 . "'";
                }
            }
        }
        //全国全是6位
        // $xhxxx_6 = "(" . implode(',',$top_6) . ")";

        $xhxxx_7 = "(" . implode(',', $top_7) . ")";

        //$sql="(select marketconditions.price,DATE_FORMAT(managedate,'%Y-%m-%d') as managedate,marketconditions.topicture as arrid,year(managedate) as year from marketconditions,marketrecord where topicture in " .$xhxxx_6. " $managedate_sql and marketconditions.marketrecordid=marketrecord.id ) union (select marketconditions.price,DATE_FORMAT(managedate,'%Y-%m-%d') as managedate,marketconditions.mastertopid as arrid,year(managedate) as year from marketconditions,marketrecord where mastertopid in " .$xhxxx_7. " $managedate_sql and marketconditions.marketrecordid=marketrecord.id )";
        $sql = "(select marketconditions.price,DATE_FORMAT(managedate,'%Y-%m-%d') as managedate,marketconditions.mastertopid as arrid,year(managedate) as year from marketconditions,marketrecord where mastertopid in " . $xhxxx_7 . " $managedate_sql and marketconditions.marketrecordid=marketrecord.id )";

        $row_price = $this->homeDao->query($sql);


        //数据分组，按区域，年划分
        $xa_arr = array();
        $cd_arr = array();
        $bj_arr = array();
        $sh_arr = array();
        foreach ($row_price as $key => $value) {
            $price = $value['price'];
            if (str_replace("-", "xx", $price) == $price) {
                $price = $price;
            } else {
                $a = $price;
                $price = ceil((substr($a, 0, strpos($a, "-")) + substr($a, strpos($a, "-") + 1)) / 20) * 10;
            }

            //  if(strstr($all_price_id['XA'],$value['arrid'])){
            // 	$xa_arr[$value['year']][$value['managedate']][$value['arrid']]=$price;
            //  }
            //  if(strstr($all_price_id['CD'],$value['arrid'])){
            // 	$cd_arr[$value['year']][$value['managedate']][$value['arrid']]=$price;
            //  }
            //  if(strstr($all_price_id['BJ'],$value['arrid'])){
            // 	$bj_arr[$value['year']][$value['managedate']][$value['arrid']]=$price;
            //  }
            //  if(strstr($all_price_id['SH'],$value['arrid'])){
            // 	$sh_arr[$value['year']][$value['managedate']][$value['arrid']]=$price;
            //  }
            $allarr[$value['arrid']][$value['year']][$value['managedate']] = $price;
        }


        return $allarr;
    }

    function get_all_price_ppjc($all_price_id, $startdate, $enddate)
    {


        $managedate_sql = "   and managedate>='" . $startdate . " 00:00' and managedate<='" . $enddate . " 23:59' ";


        foreach ($all_price_id as $k => $v) {
            $array = explode(',', $v);
            foreach ($array as $k2 => $v2) {
                if (strlen($v2) == 6) {
                    $top_6[] = "'" . $v2 . "'";
                } else {
                    $top_7[] = "'" . $v2 . "'";
                }
            }
        }
        //全国全是6位
        // $xhxxx_6 = "(" . implode(',',$top_6) . ")";

        $xhxxx_7 = "(" . implode(',', $top_7) . ")";

        //$sql="(select marketconditions.price,DATE_FORMAT(managedate,'%Y-%m-%d') as managedate,marketconditions.topicture as arrid,year(managedate) as year from marketconditions,marketrecord where topicture in " .$xhxxx_6. " $managedate_sql and marketconditions.marketrecordid=marketrecord.id ) union (select marketconditions.price,DATE_FORMAT(managedate,'%Y-%m-%d') as managedate,marketconditions.mastertopid as arrid,year(managedate) as year from marketconditions,marketrecord where mastertopid in " .$xhxxx_7. " $managedate_sql and marketconditions.marketrecordid=marketrecord.id )";
        $sql = "(select marketconditions.price,marketconditions.pricemk,DATE_FORMAT(managedate,'%Y-%m-%d') as managedate,marketconditions.mastertopid as arrid,year(managedate) as year from marketconditions,marketrecord where mastertopid in " . $xhxxx_7 . " $managedate_sql and marketconditions.marketrecordid=marketrecord.id )";

        $row_price = $this->homeDao->query($sql);


        //数据分组，按区域，年划分
        $xa_arr = array();
        $cd_arr = array();
        $lz_arr = array();
        $zz_arr = array();

        foreach ($row_price as $key => $value) {
            $price = $value['price'];
            if (str_replace("-", "xx", $price) == $price) {
                $price = $price;
            } else {
                $a = $price;
                $price = ceil((substr($a, 0, strpos($a, "-")) + substr($a, strpos($a, "-") + 1)) / 20) * 10;
            }
            $arr_price['wj'][$value['arrid']][$value['managedate']] = $price;

            $price_mk = $value['pricemk'];
            if (str_replace("-", "xx", $price_mk) == $price_mk) {
                $price_mk = $price_mk;
            } else {
                $b = $price_mk;
                $price = ceil((substr($b, 0, strpos($a, "-")) + substr($b, strpos($b, "-") + 1)) / 20) * 10;
            }
            $arr_price['sc'][$value['arrid']][$value['managedate']] = $price_mk;
            $i++;
        }

        $allarr = array();
        foreach ($arr_price as $yk => $yv) {
            foreach ($yv as $rk => $rv) {
                $all_price = '';
                $all_count = '';
                foreach ($rv as $k => $v) {
                    if (!empty($v)) {
                        $all_price = $all_price + $v;
                        $all_count = $all_count + 1;
                    }
                }
                $allarr[$yk][$rk] = round($all_price / $all_count);
            }
        }


        return $allarr;
    }

    public function ppjc($params)
    {

        //对应id都是7位的
        $idarr_7 = array("5620231", "5613121", "5611032", "5620237", "5613123", "5611033", "5320235", "531312a", "5311037", "532023q", "5313121", "5311034", "5720234", "5713121", "5711037", "5720431", "5713122", "5711031", "372043m", "371312c", "371103e", "3720436", "3713124", "3711033");
        $all_price_id = array(
            'XA' => '5620231,5613121,5611032,5620237,5613123,5611033',
            'CD' => '5320235,531312a,5311037,532023q,5313121,5311034',
            'LZ' => '5720234,5713121,5711037,5720431,5713122,5711031',
            'ZZ' => '372043m,371312c,371103e,3720436,3713124,3711033',
        );
        if ($params['startdate'] == "") $startdate = date("Y-m-d");
        else    $startdate = $params['startdate'];

        if ($params['enddate'] == "") $enddate = date("Y-m-d");
        else    $enddate = $params['enddate'];
        //目前都是七位，
        $idarr_7str = implode("','", $idarr_7);
        // $startdate='20200-12-12';
        $arr_price = $this->get_all_price_ppjc($all_price_id, $startdate, $enddate);

        //第一张表
        $data = array();


        $data = array(
            '西安' => array(
                '陕钢' => array(
                    'wj_lwg' => $arr_price['wj']['5620231'],
                    'wj_pl' => $arr_price['wj']['5613121'],
                    'wj_xc' => $arr_price['wj']['5611032'],
                    'sc_lwg' => $arr_price['sc']['5620231'],
                    'sc_pl' => $arr_price['sc']['5613121'],
                    'sc_xc' => $arr_price['sc']['5611032'],
                ),
                '晋钢/建邦/立恒' => array(
                    'wj_lwg' => $arr_price['wj']['5620237'],
                    'wj_pl' => $arr_price['wj']['5613123'],
                    'wj_xc' => $arr_price['wj']['5611033'],
                    'sc_lwg' => $arr_price['sc']['5620237'],
                    'sc_pl' => $arr_price['sc']['5613123'],
                    'sc_xc' => $arr_price['sc']['5611033'],
                ),
                '差异' => array(
                    'wj_lwg' => $this->zhangdie($arr_price['wj']['5620231'] - $arr_price['wj']['5620237'], '-', $params['dc']),
                    'wj_pl' => $this->zhangdie($arr_price['wj']['5613121'] - $arr_price['wj']['5613123'], '-', $params['dc']),
                    'wj_xc' => $this->zhangdie($arr_price['wj']['5611032'] - $arr_price['wj']['5611033'], '-', $params['dc']),
                    'sc_lwg' => $this->zhangdie($arr_price['sc']['5620231'] - $arr_price['sc']['5620237'], '-', $params['dc']),
                    'sc_pl' => $this->zhangdie($arr_price['sc']['5613121'] - $arr_price['sc']['5613123'], '-', $params['dc']),
                    'sc_xc' => $this->zhangdie($arr_price['sc']['5611032'] - $arr_price['sc']['5611033'], '-', $params['dc']),
                ),
            ),
            '成都' => array(

                '陕钢' => array(
                    'wj_lwg' => $arr_price['wj']['5320235'],
                    'wj_pl' => $arr_price['wj']['531312a'],
                    'wj_xc' => $arr_price['wj']['5311037'],
                    'sc_lwg' => $arr_price['sc']['5320235'],
                    'sc_pl' => $arr_price['sc']['531312a'],
                    'sc_xc' => $arr_price['sc']['5311037'],
                ),
                '威刚' => array(
                    'wj_lwg' => $arr_price['wj']['532023q'],
                    'wj_pl' => $arr_price['wj']['5313121'],
                    'wj_xc' => $arr_price['wj']['5311034'],
                    'sc_lwg' => $arr_price['sc']['532023q'],
                    'sc_pl' => $arr_price['sc']['5313121'],
                    'sc_xc' => $arr_price['sc']['5311034'],
                ),
                '差异' => array(
                    'wj_lwg' => $this->zhangdie($arr_price['wj']['5320235'] - $arr_price['wj']['532023q'], '-', $params['dc']),
                    'wj_pl' => $this->zhangdie($arr_price['wj']['531312a'] - $arr_price['wj']['5313121'], '-', $params['dc']),
                    'wj_xc' => $this->zhangdie($arr_price['wj']['5311037'] - $arr_price['wj']['5311034'], '-', $params['dc']),
                    'sc_lwg' => $this->zhangdie($arr_price['sc']['5320235'] - $arr_price['sc']['532023q'], '-', $params['dc']),
                    'sc_pl' => $this->zhangdie($arr_price['sc']['531312a'] - $arr_price['sc']['5313121'], '-', $params['dc']),
                    'sc_xc' => $this->zhangdie($arr_price['sc']['5311037'] - $arr_price['sc']['5311034'], '-', $params['dc']),
                ),
            ),
            '兰州' => array(
                '陕钢' => array(
                    'wj_lwg' => $arr_price['wj']['5720234'],
                    'wj_pl' => $arr_price['wj']['5713121'],
                    'wj_xc' => $arr_price['wj']['5711037'],
                    'sc_lwg' => $arr_price['sc']['5720234'],
                    'sc_pl' => $arr_price['sc']['5713121'],
                    'sc_xc' => $arr_price['sc']['5711037'],
                ),
                '酒钢' => array(
                    'wj_lwg' => $arr_price['wj']['5720431'],
                    'wj_pl' => $arr_price['wj']['5713122'],
                    'wj_xc' => $arr_price['wj']['5711031'],
                    'sc_lwg' => $arr_price['sc']['5720431'],
                    'sc_pl' => $arr_price['sc']['5713122'],
                    'sc_xc' => $arr_price['sc']['5711031'],
                ),
                '差异' => array(
                    'wj_lwg' => $this->zhangdie($arr_price['wj']['5720234'] - $arr_price['wj']['5720431'], '-', $params['dc']),
                    'wj_pl' => $this->zhangdie($arr_price['wj']['5713121'] - $arr_price['wj']['5713122'], '-', $params['dc']),
                    'wj_xc' => $this->zhangdie($arr_price['wj']['5711037'] - $arr_price['wj']['5711031'], '-', $params['dc']),
                    'sc_lwg' => $this->zhangdie($arr_price['sc']['5720234'] - $arr_price['sc']['5720431'], '-', $params['dc']),
                    'sc_pl' => $this->zhangdie($arr_price['sc']['5713121'] - $arr_price['sc']['5713122'], '-', $params['dc']),
                    'sc_xc' => $this->zhangdie($arr_price['sc']['5711037'] - $arr_price['sc']['5711031'], '-', $params['dc']),
                ),
            ),
            '郑州' => array(
                '陕钢' => array(
                    'wj_lwg' => $arr_price['wj']['372043m'],
                    'wj_pl' => $arr_price['wj']['371312c'],
                    'wj_xc' => $arr_price['wj']['371103e'],
                    'sc_lwg' => $arr_price['sc']['372043m'],
                    'sc_pl' => $arr_price['sc']['371312c'],
                    'sc_xc' => $arr_price['sc']['371103e'],
                ),
                '晋钢' => array(
                    'wj_lwg' => $arr_price['wj']['3720436'],
                    'wj_pl' => $arr_price['wj']['3713124'],
                    'wj_xc' => $arr_price['wj']['3711033'],
                    'sc_lwg' => $arr_price['sc']['3720436'],
                    'sc_pl' => $arr_price['sc']['3713124'],
                    'sc_xc' => $arr_price['sc']['3711033'],
                ),
                '差异' => array(
                    'wj_lwg' => $this->zhangdie($arr_price['wj']['372043m'] - $arr_price['wj']['3720436'], '-', $params['dc']),
                    'wj_pl' => $this->zhangdie($arr_price['wj']['371312c'] - $arr_price['wj']['3713124'], '-', $params['dc']),
                    'wj_xc' => $this->zhangdie($arr_price['wj']['371103e'] - $arr_price['wj']['3711033'], '-', $params['dc']),
                    'sc_lwg' => $this->zhangdie($arr_price['sc']['372043m'] - $arr_price['sc']['3720436'], '-', $params['dc']),
                    'sc_pl' => $this->zhangdie($arr_price['sc']['371312c'] - $arr_price['sc']['3713124'], '-', $params['dc']),
                    'sc_xc' => $this->zhangdie($arr_price['sc']['371103e'] - $arr_price['sc']['3711033'], '-', $params['dc']),
                ),
            ),
        );

        if ($params['dc'] == 1) {
            $this->ppjc_export($startdate, $enddate, $data);
            exit;
        }


        $this->assign('startdate', $startdate);
        $this->assign('enddate', $enddate);
        $this->assign('date2', $date);
        $this->assign('data', $data);

        $this->assign('mode', $params['mode'] ? $params['mode'] : 1);
        $style_url = dirname(DC_URL . $_SERVER['PHP_SELF']);
        $this->assign("style_url", $style_url);
        $this->assign('GUID', $params['GUID']);


        // $json_arr['data']=$data;

        // echo json_encode($json_arr);


    }

    public function qgavg($params)
    {
        // echo '<pre>';print_R($this->homeDao);

        // $lwg_arr=array(072023,082023,112023,122023,172023,222023,242023,252023,282023,332023,352023,362023,372023,392023,402023,422023,432023,412023,452023,502023,512023,522023,532023,552023,542023,562023,572023,602023);
        // $pl_arr=array(071312,081312,111312,121312,171312,221312,241312,251312,281312,331312,351312,361312,371312,391312,401312,421312,431312,411312,451312,501312,511312,521312,531312,551312,541312,561312,571312,601312);
        // $xc_arr=array(071103,081103,111103,121103,171103,221103,241103,251103,281103,331103,351103,361103,371103,391103,401103,421103,431103,411103,451103,501103,511103,521103,531103,551103,541103,561103,571103,601103);

        $all_price_id = array(
            //螺纹钢
            '1' => '072023,082023,112023,122023,172023,222023,242023,252023,282023,332023,352023,362023,372023,392023,402023,422023,432023,412023,452023,502023,512023,522023,532023,552023,542023,562023,572023,602023',
            //盘螺
            '2' => '071312,081312,111312,121312,171312,221312,241312,251312,281312,331312,351312,361312,371312,391312,401312,421312,431312,411312,451312,501312,511312,521312,531312,551312,541312,561312,571312,601312',
            //线材
            '3' => '071103,081103,111103,121103,171103,221103,241103,251103,281103,331103,351103,361103,371103,391103,401103,421103,431103,411103,451103,501103,511103,521103,531103,551103,541103,561103,571103,601103',
        );
        $type = $params['type'];
        $qg_wj_arr = array();//网价
        $qg_sc_arr = array();//市场价

        //取起始日期
        $lasttime = $this->homeDao->getOne("select max(managedate) from marketconditions_qgprice where  type='" . $type . "'");
        if (empty($lasttime)) {
            // $year=date('Y');
            // $lasttime=(date('Y')-4).'-01-01';
            $lasttime = '2004-01-01';
        }

        //结束日期
        $enddate = date("Y-m-d");


        $managedate_sql = "   and managedate>='" . $lasttime . " 00:00' and managedate<='" . $enddate . " 23:59' ";


        $qg_array = explode(',', $all_price_id[$type]);
        foreach ($qg_array as $k1 => $v1) {
            if (strlen($v1) == 6) {
                $top_6[] = "'" . $v1 . "'";
            } else {
                $top_7[] = "'" . $v1 . "'";
            }
        }
        //全国全是6位
        // $xhxxx_6 = "(" . implode(',',$top_6) . ")";
        // $xhxxx_7 = "(" . implode(',',$top_7) . ")";
        // $sql="(select marketconditions.price,DATE_FORMAT(managedate,'%Y-%m-%d') as managedate,marketconditions.topicture as arrid,year(managedate) as year from marketconditions,marketrecord where topicture in " .$xhxxx_6. " $managedate_sql and marketconditions.marketrecordid=marketrecord.id ) union (select marketconditions.price,DATE_FORMAT(managedate,'%Y-%m-%d') as managedate,marketconditions.mastertopid as arrid,year(managedate) as year from marketconditions,marketrecord where mastertopid in " .$xhxxx_7. " $managedate_sql and marketconditions.marketrecordid=marketrecord.id )";

        //全国全是6位
        $xhxxx_6 = "(" . implode(',', $top_6) . ")";
        $sql = "(select marketconditions.price,marketconditions.pricemk,DATE_FORMAT(managedate,'%Y-%m-%d') as managedate,marketconditions.topicture as arrid,year(managedate) as year from marketconditions,marketrecord where topicture in " . $xhxxx_6 . " $managedate_sql and marketconditions.marketrecordid=marketrecord.id )";
        $row_price = $this->homeDao->query($sql);

        //网价

        foreach ($row_price as $key => $value) {
            $price = $value['price'];
            if (str_replace("-", "xx", $price) == $price) {
                $price = $price;
            } else {
                $a = $price;
                $price = ceil((substr($a, 0, strpos($a, "-")) + substr($a, strpos($a, "-") + 1)) / 20) * 10;
            }


            $qg_allarr[$value['managedate']][$value['arrid']]['wj'] = $price;

            $pricemk = $value['pricemk'];
            if (str_replace("-", "xx", $pricemk) == $pricemk) {
                $pricemk = $pricemk;
            } else {
                $a = $pricemk;
                $pricemk = ceil((substr($a, 0, strpos($a, "-")) + substr($a, strpos($a, "-") + 1)) / 20) * 10;
            }


            $qg_allarr[$value['managedate']][$value['arrid']]['sc'] = $pricemk;

        }
        // echo '<pre>';print_R($qg_wj_allarr);
        foreach ($qg_allarr as $yk => $yv) {
            $wj_all_price = '';
            $wj_all_count = '';
            $sc_all_price = '';
            $sc_all_count = '';
            foreach ($yv as $rk => $rv) {

                if (!empty($rv['wj'])) {
                    $wj_all_price = $wj_all_price + $rv['wj'];
                    $wj_all_count = $wj_all_count + 1;
                }

                if (!empty($rv['sc'])) {
                    $sc_all_price = $sc_all_price + $rv['sc'];
                    $sc_all_count = $sc_all_count + 1;
                }

            }
            // echo  $wj_all_count;echo '<br>';
            // echo  $sc_all_count;echo '<br>';
            // echo  $wj_all_price;echo '<br>';
            // echo  $sc_all_price;echo '<br>';exit;
            $qg_arr[$yk]['wj'] = round($wj_all_price / $wj_all_count);//取当日数据的平均值，作为均价
            $qg_arr[$yk]['sc'] = round($sc_all_price / $sc_all_count);//
        }


        $basesql1 = "insert into marketconditions_qgprice(managedate,pricemk,price,type) values";
        $i = 1;
        foreach ($qg_arr as $key => $value) {
            $valsql1 .= "('" . $key . "','" . $value['sc'] . "','" . $value['wj'] . "','" . $type . "'),";
            $i++;
        }

        if ($valsql1) {
            $tsql = substr($valsql1, 0, -1);
            $this->homeDao->execute($basesql1 . $tsql);
        }

    }


    private function scjc_export($date, $list)
    {
        include '/usr/local/www/libs/PHPExcel/PHPExcel.php';
        $objPHPExcel = new PHPExcel();
        $objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
        $sheet = $objPHPExcel->getActiveSheet();

        $sheet->getDefaultStyle()->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $sheet->getDefaultStyle()->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_CENTER);


        // 标题行
        $sheet->getColumnDimensionByColumn(0)->setWidth(22);
        $sheet->getColumnDimensionByColumn(1)->setWidth(30);
        $sheet->getColumnDimensionByColumn(2)->setWidth(22);
        $sheet->getColumnDimensionByColumn(3)->setWidth(22);
        $sheet->getColumnDimensionByColumn(4)->setWidth(22);
        $sheet->getColumnDimensionByColumn(5)->setWidth(22);
        $sheet->getColumnDimensionByColumn(6)->setWidth(22);
        $sheet->getColumnDimensionByColumn(7)->setWidth(22);
        $sheet->getColumnDimensionByColumn(8)->setWidth(22);

        $sheet->setCellValueByColumnAndRow(0, 1, date('Y年m月d日', strtotime($date)) . '竞争钢厂市场价差评价（φ18-22螺纹钢）');
        $sheet->mergeCellsByColumnAndRow(0, 1, 8, 1);
        // $phpExcel->getActiveSheet()->getStyle('A1')->setFont( $phpFont );
        // $phpExcel->getActiveSheet()->getStyle('A1')->getFont()->setColor( $phpColor );
        // echo '<pre>';print_R($list);exit;
        $i = 0;//列
        $r = 2;//行

        foreach ($list as $k1 => $v1) {

            $sheet->setCellValueByColumnAndRow($i, $r, $k1);
            $sheet->setCellValueByColumnAndRow($i + 1, $r, date('m月d日', strtotime($date)));
            $sheet->setCellValueByColumnAndRow($i + 2, $r, '本周');
            $sheet->setCellValueByColumnAndRow($i + 3, $r, '上周');
            $sheet->setCellValueByColumnAndRow($i + 4, $r, '本月');
            $sheet->setCellValueByColumnAndRow($i + 5, $r, '上月');
            $sheet->setCellValueByColumnAndRow($i + 6, $r, '去年同月');
            $sheet->setCellValueByColumnAndRow($i + 7, $r, '本年');
            $sheet->setCellValueByColumnAndRow($i + 8, $r, '去年全年');
            $sheet->getStyle(chr($i + 66) . $r)->getFont()->getColor()->setRGB('000000');
            $sheet->getStyle(chr($i + 67) . $r)->getFont()->getColor()->setRGB('000000');
            $sheet->getStyle(chr($i + 68) . $r)->getFont()->getColor()->setRGB('000000');
            $sheet->getStyle(chr($i + 69) . $r)->getFont()->getColor()->setRGB('000000');
            $sheet->getStyle(chr($i + 70) . $r)->getFont()->getColor()->setRGB('000000');
            $sheet->getStyle(chr($i + 71) . $r)->getFont()->getColor()->setRGB('000000');
            $sheet->getStyle(chr($i + 72) . $r)->getFont()->getColor()->setRGB('000000');
            $sheet->getStyle(chr($i + 73) . $r)->getFont()->getColor()->setRGB('000000');


            foreach ($v1 as $k2 => $v2) {
                $sheet->setCellValueByColumnAndRow($i, $r + 1, $k2);

                $sheet->setCellValueByColumnAndRow($i + 1, $r + 1, $v2['br']);
                if ($v2['br'] > 0 && $k2 != '龙钢') {
                    $sheet->setCellValueByColumnAndRow($i + 1, $r + 1, '↑' . abs($v2['br']));
                    $sheet->getStyle(chr($i + 66) . ($r + 1))->getFont()->getColor()->setRGB('FF0000');    //设置字体颜色
                }
                if ($v2['br'] < 0 && $k2 != '龙钢') {
                    $sheet->setCellValueByColumnAndRow($i + 1, $r + 1, '↓' . abs($v2['br']));
                    $sheet->getStyle(chr($i + 66) . ($r + 1))->getFont()->getColor()->setRGB('32CD32');    //设置字体颜色
                }

                $sheet->setCellValueByColumnAndRow($i + 2, $r + 1, $v2['bz']);
                if ($v2['bz'] > 0 && $k2 != '龙钢') {
                    $sheet->setCellValueByColumnAndRow($i + 2, $r + 1, '↑' . abs($v2['bz']));
                    $sheet->getStyle(chr($i + 67) . ($r + 1))->getFont()->getColor()->setRGB('FF0000');    //设置字体颜色
                }
                if ($v2['bz'] < 0 && $k2 != '龙钢') {
                    $sheet->setCellValueByColumnAndRow($i + 2, $r + 1, '↓' . abs($v2['bz']));
                    $sheet->getStyle(chr($i + 67) . ($r + 1))->getFont()->getColor()->setRGB('32CD32');    //设置字体颜色
                }
                $sheet->setCellValueByColumnAndRow($i + 3, $r + 1, $v2['sz']);
                if ($v2['sz'] > 0 && $k2 != '龙钢') {
                    $sheet->setCellValueByColumnAndRow($i + 3, $r + 1, '↑' . abs($v2['sz']));
                    $sheet->getStyle(chr($i + 68) . ($r + 1))->getFont()->getColor()->setRGB('FF0000');    //设置字体颜色
                }
                if ($v2['sz'] < 0 && $k2 != '龙钢') {
                    $sheet->setCellValueByColumnAndRow($i + 3, $r + 1, '↓' . abs($v2['sz']));
                    $sheet->getStyle(chr($i + 68) . ($r + 1))->getFont()->getColor()->setRGB('32CD32');    //设置字体颜色
                }
                $sheet->setCellValueByColumnAndRow($i + 4, $r + 1, $v2['by']);
                if ($v2['by'] > 0 && $k2 != '龙钢') {
                    $sheet->setCellValueByColumnAndRow($i + 4, $r + 1, '↑' . abs($v2['by']));
                    $sheet->getStyle(chr($i + 69) . ($r + 1))->getFont()->getColor()->setRGB('FF0000');    //设置字体颜色
                }
                if ($v2['by'] < 0 && $k2 != '龙钢') {
                    $sheet->setCellValueByColumnAndRow($i + 4, $r + 1, '↓' . abs($v2['by']));
                    $sheet->getStyle(chr($i + 69) . ($r + 1))->getFont()->getColor()->setRGB('32CD32');    //设置字体颜色
                }
                $sheet->setCellValueByColumnAndRow($i + 5, $r + 1, $v2['sy']);
                if ($v2['sy'] > 0 && $k2 != '龙钢') {
                    $sheet->setCellValueByColumnAndRow($i + 5, $r + 1, '↑' . abs($v2['sy']));
                    $sheet->getStyle(chr($i + 70) . ($r + 1))->getFont()->getColor()->setRGB('FF0000');    //设置字体颜色
                }
                if ($v2['sy'] < 0 && $k2 != '龙钢') {
                    $sheet->setCellValueByColumnAndRow($i + 5, $r + 1, '↓' . abs($v2['sy']));
                    $sheet->getStyle(chr($i + 70) . ($r + 1))->getFont()->getColor()->setRGB('32CD32');    //设置字体颜色
                }
                $sheet->setCellValueByColumnAndRow($i + 6, $r + 1, $v2['sny']);
                if ($v2['sny'] > 0 && $k2 != '龙钢') {
                    $sheet->setCellValueByColumnAndRow($i + 6, $r + 1, '↑' . abs($v2['sny']));
                    $sheet->getStyle(chr($i + 71) . ($r + 1))->getFont()->getColor()->setRGB('FF0000');    //设置字体颜色
                }
                if ($v2['sny'] < 0 && $k2 != '龙钢') {
                    $sheet->setCellValueByColumnAndRow($i + 6, $r + 1, '↓' . abs($v2['sny']));
                    $sheet->getStyle(chr($i + 71) . ($r + 1))->getFont()->getColor()->setRGB('32CD32');    //设置字体颜色
                }
                $sheet->setCellValueByColumnAndRow($i + 7, $r + 1, $v2['bn']);
                if ($v2['bn'] > 0 && $k2 != '龙钢') {
                    $sheet->setCellValueByColumnAndRow($i + 7, $r + 1, '↑' . abs($v2['bn']));
                    $sheet->getStyle(chr($i + 72) . ($r + 1))->getFont()->getColor()->setRGB('FF0000');    //设置字体颜色
                }
                if ($v2['bn'] < 0 && $k2 != '龙钢') {
                    $sheet->setCellValueByColumnAndRow($i + 7, $r + 1, '↓' . abs($v2['bn']));
                    $sheet->getStyle(chr($i + 72) . ($r + 1))->getFont()->getColor()->setRGB('32CD32');    //设置字体颜色
                }
                $sheet->setCellValueByColumnAndRow($i + 8, $r + 1, $v2['sn']);
                if ($v2['sn'] > 0 && $k2 != '龙钢') {
                    $sheet->setCellValueByColumnAndRow($i + 8, $r + 1, '↑' . abs($v2['sn']));
                    $sheet->getStyle(chr($i + 73) . ($r + 1))->getFont()->getColor()->setRGB('FF0000');    //设置字体颜色
                }
                if ($v2['sn'] < 0 && $k2 != '龙钢') {
                    $sheet->setCellValueByColumnAndRow($i + 8, $r + 1, '↓' . abs($v2['sn']));
                    $sheet->getStyle(chr($i + 73) . ($r + 1))->getFont()->getColor()->setRGB('32CD32');    //设置字体颜色
                }
                $r++;
            }

        }

        $file_name = date('Y年m月d日', strtotime($date)) . '竞争钢厂市场价差评价（φ18-22螺纹钢）';
        header("Pragma: public");
        header("Expires: 0");
        header("Cache-Control:must-revalidate, post-check=0, pre-check=0");
        header("Content-Type:application/force-download");
        header("Content-Type:application/vnd.ms-execl");
        header("Content-Type:application/octet-stream");
        header("Content-Type:application/download");;
        header('Content-Disposition:attachment;filename="' . $file_name . '.xls"');
        header("Content-Transfer-Encoding:binary");

        $objWriter->save('php://output');
    }

    private function ppjc_export($startdate, $enddate, $list)
    {
        include '/usr/local/www/libs/PHPExcel/PHPExcel.php';
        $objPHPExcel = new PHPExcel();
        $objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
        $sheet = $objPHPExcel->getActiveSheet();

        $sheet->getDefaultStyle()->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
        $sheet->getDefaultStyle()->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_CENTER);


        // 标题行
        $sheet->getColumnDimensionByColumn(0)->setWidth(22);
        $sheet->getColumnDimensionByColumn(1)->setWidth(30);
        $sheet->getColumnDimensionByColumn(2)->setWidth(22);
        $sheet->getColumnDimensionByColumn(3)->setWidth(22);
        $sheet->getColumnDimensionByColumn(4)->setWidth(22);
        $sheet->getColumnDimensionByColumn(5)->setWidth(22);
        $sheet->getColumnDimensionByColumn(6)->setWidth(22);
        $sheet->getColumnDimensionByColumn(7)->setWidth(22);


        $sheet->mergeCells("A1:H1");
        $sheet->setCellValue("A1", date('Y年m月d日', strtotime($startdate)) . '-' . date('Y年m月d日', strtotime($enddate)) . '品牌价差');
        $sheet->mergeCells("A2:A3");
        $sheet->setCellValue("A2", '区域');
        $sheet->mergeCells("B2:B3");
        $sheet->setCellValue("B2", '钢厂');
        $sheet->mergeCells("C2:E2");
        $sheet->setCellValue("C2", '网价');
        $sheet->mergeCells("F2:H2");
        $sheet->setCellValue("F2", '市场价');

        $sheet->setCellValue("C3", '螺纹钢');
        $sheet->setCellValue("D3", '盘螺');
        $sheet->setCellValue("E3", '线材');
        $sheet->setCellValue("F3", '螺纹钢');
        $sheet->setCellValue("G3", '盘螺');
        $sheet->setCellValue("H3", '线材');


        $sheet->mergeCells("A4:A6");
        $sheet->mergeCells("A7:A9");
        $sheet->mergeCells("A10:A12");
        $sheet->mergeCells("A13:A15");
        $i = 0;//列
        $r = 4;//行

        foreach ($list as $k1 => $v1) {

            $sheet->setCellValueByColumnAndRow($i, $r, $k1);


            foreach ($v1 as $k2 => $v2) {
                $sheet->setCellValueByColumnAndRow($i + 1, $r, $k2);
                $sheet->setCellValueByColumnAndRow($i + 2, $r, $v2['wj_lwg']);
                $sheet->setCellValueByColumnAndRow($i + 3, $r, $v2['wj_pl']);
                $sheet->setCellValueByColumnAndRow($i + 4, $r, $v2['wj_xc']);
                $sheet->setCellValueByColumnAndRow($i + 5, $r, $v2['sc_lwg']);
                $sheet->setCellValueByColumnAndRow($i + 6, $r, $v2['sc_pl']);
                $sheet->setCellValueByColumnAndRow($i + 7, $r, $v2['sc_xc']);

                if ($v2['wj_lwg'] > 0 && $k2 == '差异') {
                    $sheet->setCellValueByColumnAndRow($i + 2, $r, '↑' . abs($v2['wj_lwg']));
                    $sheet->getStyle(chr($i + 67) . $r)->getFont()->getColor()->setRGB('FF0000');    //设置字体颜色
                }
                if ($v2['wj_lwg'] < 0 && $k2 == '差异') {
                    $sheet->setCellValueByColumnAndRow($i + 2, $r, '↓' . abs($v2['wj_lwg']));
                    $sheet->getStyle(chr($i + 67) . $r)->getFont()->getColor()->setRGB('32CD32');    //设置字体颜色
                }

                if ($v2['wj_pl'] > 0 && $k2 == '差异') {
                    $sheet->setCellValueByColumnAndRow($i + 3, $r, '↑' . abs($v2['wj_pl']));
                    $sheet->getStyle(chr($i + 68) . $r)->getFont()->getColor()->setRGB('FF0000');    //设置字体颜色
                }
                if ($v2['wj_pl'] < 0 && $k2 == '差异') {
                    $sheet->setCellValueByColumnAndRow($i + 3, $r, '↓' . abs($v2['wj_pl']));
                    $sheet->getStyle(chr($i + 68) . $r)->getFont()->getColor()->setRGB('32CD32');    //设置字体颜色
                }
                if ($v2['wj_xc'] > 0 && $k2 == '差异') {
                    $sheet->setCellValueByColumnAndRow($i + 4, $r, '↑' . abs($v2['wj_xc']));
                    $sheet->getStyle(chr($i + 69) . $r)->getFont()->getColor()->setRGB('FF0000');    //设置字体颜色
                }
                if ($v2['wj_xc'] < 0 && $k2 == '差异') {
                    $sheet->setCellValueByColumnAndRow($i + 4, $r, '↓' . abs($v2['wj_xc']));
                    $sheet->getStyle(chr($i + 69) . $r)->getFont()->getColor()->setRGB('32CD32');    //设置字体颜色
                }
                if ($v2['sc_lwg'] > 0 && $k2 == '差异') {
                    $sheet->setCellValueByColumnAndRow($i + 5, $r, '↑' . abs($v2['sc_lwg']));
                    $sheet->getStyle(chr($i + 70) . $r)->getFont()->getColor()->setRGB('FF0000');    //设置字体颜色
                }
                if ($v2['sc_lwg'] < 0 && $k2 == '差异') {
                    $sheet->setCellValueByColumnAndRow($i + 5, $r, '↓' . abs($v2['sc_lwg']));
                    $sheet->getStyle(chr($i + 70) . $r)->getFont()->getColor()->setRGB('32CD32');    //设置字体颜色
                }
                if ($v2['sc_pl'] > 0 && $k2 == '差异') {
                    $sheet->setCellValueByColumnAndRow($i + 6, $r, '↑' . abs($v2['sc_pl']));
                    $sheet->getStyle(chr($i + 71) . $r)->getFont()->getColor()->setRGB('FF0000');    //设置字体颜色
                }
                if ($v2['sc_pl'] < 0 && $k2 == '差异') {
                    $sheet->setCellValueByColumnAndRow($i + 6, $r, '↓' . abs($v2['sc_pl']));
                    $sheet->getStyle(chr($i + 71) . $r)->getFont()->getColor()->setRGB('32CD32');    //设置字体颜色
                }
                if ($v2['sc_xc'] > 0 && $k2 == '差异') {
                    $sheet->setCellValueByColumnAndRow($i + 7, $r, '↑' . abs($v2['sc_xc']));
                    $sheet->getStyle(chr($i + 72) . $r)->getFont()->getColor()->setRGB('FF0000');    //设置字体颜色
                }
                if ($v2['sc_xc'] < 0 && $k2 == '差异') {
                    $sheet->setCellValueByColumnAndRow($i + 7, $r, '↓' . abs($v2['sc_xc']));
                    $sheet->getStyle(chr($i + 72) . $r)->getFont()->getColor()->setRGB('32CD32');    //设置字体颜色
                }
                $r++;
            }

        }

        $file_name = date('Y年m月d日', strtotime($startdate)) . '-' . date('Y年m月d日', strtotime($enddate)) . '品牌价差';
        header("Pragma: public");
        header("Expires: 0");
        header("Cache-Control:must-revalidate, post-check=0, pre-check=0");
        header("Content-Type:application/force-download");
        header("Content-Type:application/vnd.ms-execl");
        header("Content-Type:application/octet-stream");
        header("Content-Type:application/download");;
        header('Content-Disposition:attachment;filename="' . $file_name . '.xls"');
        header("Content-Transfer-Encoding:binary");

        $objWriter->save('php://output');
    }
}


?>