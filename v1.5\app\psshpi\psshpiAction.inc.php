<?php 
//require_once ("/etc/steelconf/config/isholiday.php");
include_once( APP_DIR."/psshpi/psshpiBaseAction.inc.php" );
define('PS_NORIGHT',"抱歉，您没有权限查看该数据。\r\n如需咨询订阅，请联系钢之家客服，联系电话：15800777957/021-50585733。");
class  psshpiAction extends psshpiBaseAction
{
    public function __construct()
    {
        parent::__construct();
    } 
	/*101*/
	public function GetPsRealtimeNewsList($params){
		
		$GUID = $params['GUID'];
		$debug = $params['debug'];
		$mode = $params['mode'];//1=客户端，2=安卓，3=iOS

        if($GUID==""){
            $arr['Success']='0';
            $arr['Message']= base64_encode($this->utf8ToGb2312('非法登录,GUID为空！'));
        }else{

            //if($Status=="1")
			{
                if($params['StartCon']!=""){
                    $date = $params['StartCon'];
                    $where .=" and Date>='".$date." 00:00:00' ";
                }
                if($params['EndCon']!=""){
                    $date = $params['EndCon'];
                    $where .=" and Date<='".$date." 23:59:59' ";
                }
                if($params['FromCon']!=""){
                    $FromCon = $params['FromCon'];
                    $cSource = substr($FromCon, 0, 3); 
                    $pageNumber = substr($FromCon, 3); 

                    $where .=" and cSource='".$cSource."' ";
                    if($pageNumber){
                        //$pageNumber = sprintf("%04d",$pageNumber);
                        $where .=" and pageNumber like '%".$pageNumber."%' ";
                    }
                }
                if($params['Condition']!=""){
                    $Condition = $params['Condition'];
                    $where .=" and headLine  like '%".$Condition."%' ";
                }
                if($params['AppMode']!=""){
                    
                }

                $per = 20;
                if($params['Records']!=""){
                    $per = $params['Records'];
                }
                $total = $this->maindao->getPs_realnews_total($where,$debug);

                $page = $params['Page'] == '' ? 1 : $params['Page'];
                //$per = 20;
                $start = ( $page - 1 ) * $per;

                $list = $this->maindao->getPs_realnews($where,$start,$per,$debug);	
                $data = array();
                foreach($list as $key=>$tmp){
                    $data[$key]['id'] = $tmp['sid'];
                    $data[$key]['date'] = date("Y-m-d H:i:s",strtotime($tmp['date']));

                    //Flash加前缀
                    if($tmp['type']=="2"){
                        $tmp['headLine'] = "NEWSFLASH：".$tmp['headLine'];
                    }
                    $data[$key]['headLine'] = base64_encode( $this->utf8ToGb2312( $tmp['headLine'] ));
                    $data[$key]['cSource'] = $tmp['cSource'];
                    $data[$key]['pageNumber'] = $tmp['pageNumber'];
                    $data[$key]['type'] = $tmp['type'];

                    $hasDetail = "0";//0: 没有详细 1：有详细
                    if($tmp['fileName']!=""){
                        $hasDetail = "1";
                    }
                    $data[$key]['hasDetail'] = $hasDetail;

                    $urlLink = trim($tmp['urlLink']);
                    if(strpos($urlLink,'http://')===false && strpos($urlLink,'https://')===false){
                        $urlLink = "";
                    }
                    $data[$key]['urlLink'] = $urlLink;
                }


                $arr['Success']='1';
                $arr['Message']= base64_encode($this->utf8ToGb2312( '查询成功' ));
                $arr['TotalPage']= ceil($total/$per);
                $arr['News']= $data;
            }
			/*else{
                $arr['Success']='0';
                $arr['Message']= base64_encode('您不能查看，请联系管理员开通权限！');
            }*/
            

        }


        $json_string = $this->pri_JSON($arr); 
		echo $json_string;
        exit;

	}
    /*102*/
    public function newsdetail($params){
        //print_r($params);
        $GUID = $params['GUID'];
        $newsid = $params['newsid'];
        $pageNumber = $params['pageNumber'];
        $cSource = $params['cSource'];
        $cSource2 = $params['cSource2'];
        //$newsid = "16504";
		$mode = $params['mode'];//1=客户端，2=安卓，3=iOS
        
        if($GUID=="" || ( $newsid=="" && $pageNumber=="")){
            echo "非法登录,GUID为空！";
        }else{
            
            $Status = $this->HasRight($GUID);

			if($Status=='1'){
            if($newsid && $pageNumber==""){
                $where = " and sid='".$newsid."' ";
            }else if($pageNumber!=""){
                $pageNumber = sprintf ( "%04d",$pageNumber);
                $where = " and pageNumber='".$pageNumber."' and cSource='".$cSource."' ";
                if($cSource2){
                    $where .=" and cSource2='".$cSource2."' ";
                }
            }

            $info = $this->maindao->getPs_realnews_detail($where);	
            if($info['take']!="")
            {
                $info['take2'] = explode(" ",$info['take']);
                $info['take2'] = $info['take2'][1];
            }
            $info['cont2'] = sprintf ( "%04d",$info['cont']);

            //如果有cont，查看是否有cont页的内容
            if($info['cont']!=""){
                $pageNumber = sprintf ( "%04d",$info['cont']);
                $where = " and pageNumber='".$pageNumber."' and cSource='".$info['cSource']."' ";
                if($info['cSource2']){
                    $where .=" and cSource2='".$info['cSource2']."' ";
                }
                $info_pre = $this->maindao->getPs_realnews_detail($where);	
                $is_prepage = "0";
                if($info_pre['id']){
                    $is_prepage = "1";
                }
                $this->assign( "is_prepage", $is_prepage );
            }

            //读取内容
            $newpath =  '/usr/local/www/'.$info['fileName'];
            $content = '';
            
            if(file_exists($newpath)!=false){
                $zp = gzopen($newpath, "r");
                while($tempc = gzread($zp, 10000))
                {
                    $content .= $tempc;
                }
                gzclose($zp);
            }
            //echo "<pre>";
            //echo $content;
            //$content = nl2br($content);
            
            //$info['textBody'] = $content;
            $info['textBody'] = html_entity_decode($content);
            //$info['textBody'] = htmlspecialchars_decode($content);
            $this->assign( "info", $info );

            $imgurl = "http://".STEELHOME_SITE_NOHTTP."/_v2image/showgraph_platts/platts_zst_".date("Y").".png";
            //背景图
		    $bg_img="background:url(".$imgurl.") no-repeat top center;";
            $this->assign( "bg_img", $bg_img );
            
            if($mode==1){
                $head_url = DC_URL."/v1.5/css/ps_small.css";
            }else if($mode==3||$mode==5){
                $head_url = DC_URL."/v1.5/css/ps_middle.css";
            }else if($mode==2||$mode==4){
                $head_url = DC_URL."/v1.5/css/ps_large.css";
            }else{
                $head_url = DC_URL."/v1.5/css/ps_middle.css";
            }
            $style_url= dirname(DC_URL.$_SERVER['PHP_SELF']);
            $this->assign( "style_url", $style_url );
            $this->assign( "head_url", $head_url );
            $this->assign( "params", $params );
			}else{
				echo $this->NoRightHtml();
				//echo str_replace("\r\n","<br>","<h2>".PS_NORIGHT."</h2>");
				exit;
			}
        }
    }
    /*103*/
    public function GetPsRealtimeNumsList($params){
        $GUID = $params['GUID'];
        $MDC = $params['MDC'];
        $SymbolCon = $params['SymbolCon'];
        $SymbolCon = trim($SymbolCon);
//        $SymbolCon =iconv("UTF-8","GB2312",  $SymbolCon);
       
        /*1:日数据
        2:周数据
        3：月数据
        4：季度数据
        */
        $Type = $params['Type'];

        if($GUID==""){
            $arr['Success']='0';
            $arr['Message']= base64_encode($this->utf8ToGb2312( '非法登录,GUID为空！' ));
        }else{

            $Status = $this->HasRight($GUID);
            //$Status = "1";
            //if($Status=="1")
			{

                //普氏基础数据表
                $ps_basedata = $this->gcdao->get_PSshpiBaseInfos($MDC);	

                $symbol_DW = "'1'";
                $symbol_WA = "'1'";
                $symbol_MA = "'1'";
                $symbol_QR = "'1'";
                $symbols = array();
                foreach($ps_basedata as $tmp){
                    /*Freq  DW: 每日更新 WA:每周更新 MA：每月更新 FN：有限的，偶尔出现*/              
                    switch ($tmp['Freq'])
                    {
                        case "DW":
                            $symbol_DW .=",'".$tmp['Symbol']."'";
                            break;
                        case "WA":
                            $symbol_WA .=",'".$tmp['Symbol']."'";
                            break;
                        case "MA":
                            $symbol_MA .=",'".$tmp['Symbol']."'";
                            break;
                        case "QR":
                            $symbol_QR .=",'".$tmp['Symbol']."'";
                            break;
                        
                    }
                    $symbols[$tmp['Symbol']] = $tmp;

                }
                $date_DW = date("Y-m-d",strtotime("-10 day",strtotime("2018-04-05")));
                $date_WA = date("Y-m-d",strtotime("-30 day",strtotime("2018-04-05")));
                $date_MA = date("Y-m-d",strtotime("-90 day",strtotime("2018-04-05")));
                $date_QR = date("Y-m-d",strtotime("-180 day",strtotime("2018-04-05")));

                $where_DW = " and symbol in ($symbol_DW) and permcode='".$MDC."' and dateTime>='".$date_DW."' ";
                $where_WA = " and symbol in ($symbol_WA) and permcode='".$MDC."' and dateTime>='".$date_WA."' ";
                $where_MA = " and symbol in ($symbol_MA) and permcode='".$MDC."' and dateTime>='".$date_MA."' ";
                $where_QR = " and symbol in ($symbol_QR) and permcode='".$MDC."' and dateTime>='".$date_QR."' ";

                if($SymbolCon!="") {
                    $Type="";
                    $SymbolCon2 = $this->gcdao->get_PSshpiBaseInfo_Symbol($SymbolCon);
                }

                $where="";
                if(!empty($SymbolCon2)) {
                    $SymbolCon2 = implode("','",$SymbolCon2);
                    $where = " and symbol in ( '".$SymbolCon2."' ) and permcode='".$MDC."' and dateTime>='".$date_MA."' ";
                }
                
                
                /*日数据，周数据，月数据，季度数据:DailyNums,WeeklyNums,MonthlyNums, QRNums*/
                if($Type=="1"){
                    $Data = $this->maindao->getPs_realnum($where_DW,$params['debug']);
                }else if($Type=="2"){
                    //周数据
                    $Data = $this->maindao->getPs_realnum($where_WA,$params['debug']);
                }else if($Type=="3"){
                    //月数据
                    $Data = $this->maindao->getPs_realnum($where_MA,$params['debug']);
                }else if($Type=="4"){
                    //季数据
                    $Data = $this->maindao->getPs_realnum($where_QR,$params['debug']);
                }else{
                    //搜索
                    if($where!=""){
                        $Data = $this->maindao->getPs_realnum($where,$params['debug']);
                    }
                }
                //print_r($Data);

                $arr_day = array();
                $batu = array();
                foreach($Data as $tmp){
                    $batu[$tmp['batu']] = $tmp['batu'];
                    $arr_day[$tmp['symbol']][$tmp['batu']] = $tmp;
                }
                
                $DailyNums = array();
                foreach($arr_day as $key=>$tmp){
                    $temp = array();                
                    $temp['symbol'] = $key;
                    foreach($tmp as $tmp2){
                        $temp['dateTime'] = $tmp2['dateTime'];
						if($Status=="1")$temp[$tmp2['batu']] = $tmp2['value'];
						else $temp[$tmp2['batu']] = '';
                    }
                    $temp['chnDesc'] = base64_encode( $this->utf8ToGb2312( $symbols[$key]['cDescription'] ));
					//xiangbin add 20181203
					$temp['CurrCN'] = base64_encode( $this->utf8ToGb2312( $symbols[$key]['CurrCN'] ) );
					$temp['UOMCN'] = base64_encode( $this->utf8ToGb2312( $symbols[$key]['UOMCN'] ));
					//xiangbin add 20181203
                    $temp['endDesc'] = $symbols[$key]['eDescription'];
                    $temp['Freq'] = $symbols[$key]['Freq'];

                    $DailyNums[] = $temp;
                }

                $page = $params['Page'] == '' ? 1 : $params['Page'];
                $per = $params['Records'] == '' ? 20 : $params['Records'];
                $start = ( $page - 1 ) * $per;
                $total = count($DailyNums);

                $Data = array_slice($DailyNums ,$start ,$start+$per);
                
                $tip = "查询成功！";
                if(empty($DailyNums)){
                    $tip = "没有数据！";
                }
                


                $arr['Success']='1';
                $arr['Message']= base64_encode($this->utf8ToGb2312($tip));
                $arr['TotalPage']= ceil($total/$per);
                $arr['includeFields']= implode(",",$batu);
                if($Type=="1"){
                    $arr['DailyNums']= $Data;
                }else if($Type=="2"){
                    $arr['WeeklyNums']= $Data;
                }else if($Type=="3"){
                    $arr['MonthlyNums']= $Data;
                }else if($Type=="4"){
                    $arr['QRNums']= $Data;
                }else{
                    $arr['DailyNums']= $Data;
                }
            }
			/*else{
                $arr['Success']='0';
                $arr['Message']= base64_encode("您不能查看，请联系管理员开通权限！");
            }*/

        }


        $json_string = $this->pri_JSON($arr); 
		echo $json_string;
        exit;
    }
    function get_batu($batu,$type){

        $data = array(
            "a"=>array("chn"=>"询问","end"=>"Ask"),
            "b"=>array("chn"=>"投标","end"=>"Bid"),
            "c"=>array("chn"=>"结算价","end"=>"Close"),
            "e"=>array("chn"=>"持仓量","end"=>"Open Interest"),
            "h"=>array("chn"=>"高位","end"=>"High"),
            "l"=>array("chn"=>"低位","end"=>"Low"),
            "o"=>array("chn"=>"未平仓","end"=>"Open"),
            "t"=>array("chn"=>"交易序号","end"=>"Number of transactions or trades"),
            "u"=>array("chn"=>"未说明","end"=>"Unspecified"),
            "w"=>array("chn"=>"成交量","end"=>"Traded Volume")
            );

        return $data[$batu][$type];

    }
    /*104*/
    public function CanUsePs($params){

        $GUID = $params['GUID'];
		if($GUID==""){
            $arr['Success']='0';
            $arr['Message']= base64_encode($this->utf8ToGb2312('非法登录,GUID为空！'));
        }else{
            $Status = $this->HasRight($GUID);
			if($Status == '1'){
				$arr['Success']='1';
				$arr['Message']= base64_encode($this->utf8ToGb2312('查询成功'));
			}else{
				$arr['Success']='1';
				$arr['Message']= base64_encode($this->utf8ToGb2312( PS_NORIGHT ));
			}
            $arr['Status']=$Status;
        }


        $json_string = $this->pri_JSON($arr); 
		echo $json_string;
        exit;
    }
	/*105*/    
    public function GetPsNumType($params){
        
       
            $data = array(
            "0"=>array("Name"=>"SI-Metals: Iron Ore 铁矿石价格","MDC"=>"SI"),
            "1"=>array("Name"=>"MC-Coal: Metallurgical Coal 冶金煤与冶金焦价格","MDC"=>"MC"),
            "2"=>array("Name"=>"SS-Metals: Steel Americas 美洲钢材价格","MDC"=>"SS"),
            "3"=>array("Name"=>"SR-Metals: Steel EMEA 欧洲中东非洲钢材价格","MDC"=>"SR"),
            "4"=>array("Name"=>"SQ-Metals: Steel Asia 亚洲钢材价格","MDC"=>"SQ"),
            "5"=>array("Name"=>"ST-Metals: Ferrous Scrap 废钢和生铁价格","MDC"=>"ST"),
            "6"=>array("Name"=>"MF-Metals: Steel Mill Economics/Metals Spreads 全球钢铁价差 ","MDC"=>"MF"),
            "7"=>array("Name"=>"FA-Metals: Ferroalloys 铁合金价格","MDC"=>"FA"),
            "8"=>array("Name"=>"LT-Metals: Light 轻金属价格","MDC"=>"LT"),
            "9"=>array("Name"=>"PM-Metals: Precious 贵金属价格","MDC"=>"PM"),
            "10"=>array("Name"=>"MM-Metals: Major 主要有色金属价格","MDC"=>"MM"),
            "11"=>array("Name"=>"MI-Metals: Minor 稀有金属价格","MDC"=>"MI"),
            "12"=>array("Name"=>"MRY-Metals: Dry Freight 干散货运费","MDC"=>"MRY"),
            "13"=>array("Name"=>"FX-Foreign Exchange: 3rd Party Rates & Platts MOC Assessments 外汇汇率&普氏MOC","MDC"=>"FX"),
            "14"=>array("Name"=>"IF-Futures/Nearbys: NYMEX Metals NYMEX金属期货合约 ","MDC"=>"IF"),
            "15"=>array("Name"=>"LM-Futures: LME Ferrous LME有色金属","MDC"=>"LM"),
            "16"=>array("Name"=>"BAT-Metals: Battery Materials 电池材料","MDC"=>"BAT")
            ); 
  

        foreach($data as &$tmp){
            $tmp['Name'] = base64_encode( $this->utf8ToGb2312($tmp['Name']) );
        }


        $arr['Success']='1';
		$arr['Message']= base64_encode( $this->utf8ToGb2312('查询成功'));
        $arr['lists']=$data;


        $json_string = $this->pri_JSON($arr); 
		echo $json_string;
        exit;
    }
	/*106*/    
    public function echarts($params){
        $GUID = $params['GUID'];
        $Symbol = $params['Symbol'];
        //$Symbol = "XNHRU18";

		if($GUID==""){
            $arr['Success']='0';
            $arr['Message']= base64_encode( $this->utf8ToGb2312( '非法登录,GUID为空！' ));
        }else{

            $Status = $this->HasRight($GUID);
			if($Status=='1'){

				$batu = $params['batu'];
				//$batu = array("l","o","h","c");
				//echo "<pre>";

        //普氏基础数据表
        $ps_basedata = $this->gcdao->get_PSshpiBaseInfo($Symbol);	
        //echo "no picture";
        //print_r( $ps_basedata );
        if($ps_basedata['Freq']=="QR"){
            $date = date("Y-m-d",strtotime("-5 year"));
        }else{
            $date = date("Y-m-d",strtotime("-1 year"));
        }
        $where = " and symbol='{$Symbol}' and dateTime>='".$date."' ";
        $Data = $this->maindao->getPs_realnum($where);
        //print_r($Data);
        
        $batu2 = array();
        $arr_data = array();
        $datetime = array();    
        //echo "<pre>";
        foreach($Data as $tmp){
            $batu2[$tmp['batu']] = $tmp['batu'];

            $arr_data[$tmp['batu']][$tmp['dateTime']] = $tmp['value'];
            $datetime[] = $tmp['dateTime'];
            
        }
        //print_r($arr_data);
        //print_r($datetime);
        
        /*if(empty($batu)){
            $batu = array_slice($batu2 ,0 ,4);
        }
        //print_r($batu);
        //print_r($batu2);
        foreach($batu2 as $tmp){
            if(!in_array($tmp,$batu)){
                //echo $tmp."#";
                unset($arr_data[$tmp]);
            }
        }*/
        $arr_aquire = array_keys($arr_data);

        $datax = array_unique($datetime);
		$count = count($datax);

        $xLables = array_values($datax);
        
        //$datasource="数据来源：钢之家数据中心 www.steelhome.cn/data";
        file_get_contents("http://".STEELHOME_SITE_NOHTTP."/_v2image/showgraph_platts/psImageCreate.php");
				$imgurl = "http://".STEELHOME_SITE_NOHTTP."/_v2image/showgraph_platts/platts_zst_".date('Y').".png";

        //背景图
		$bg_img="background:url(".$imgurl.") no-repeat center center;";

        $title = $Symbol."走势图（".$date."至".date("Y-m-d")."）";


        $params['ps_basedata'] = $ps_basedata;
		$params['data'] = $arr_data;
		$params['datasource'] = $datasource;
		$params['xlable'] = $xLables;
		$params['title'] = $title;
		$params['count'] = $count;
		$params['batu2'] = $batu2;
		$params['bg_img'] = $bg_img;
		$params['arr_aquire'] = $arr_aquire;
		//print_r($params);

				$this->create_echarts($params);
				exit;
			}else{
				//echo str_replace("\r\n","<br>","<h2>".PS_NORIGHT."</h2>");
				echo $this->NoRightHtml();
				exit;
			}
			
		}
    }
	   
	function HasRight($GUID){
		//获取用户信息
		$user_infos = $this->t1dao->get_userinfo_byguid($GUID);
		$Mid=$user_infos['Mid'];//会员id
		$userid=$user_infos['Uid'];//用户id
		//$TrueName=$user_infos['TrueName'];//用户名
		$PsPrivilage = $this->_dao->getRow("select * from PsPrivilage where uid ='".$userid."' and mc_type='0' and UserType=1 limit 1");
		$Status = "0";
		if($PsPrivilage['Start'] <= date("Y-m-d") && $PsPrivilage['End'] >= date("Y-m-d")){
			$Status = "1";
		}
		/*1号钢之家有权限*/
		if($Mid=="1"){
			$Status = "1";
		}
		//$Status = "0";
		return $Status;
	}

	function NoRightHtml(){
		file_get_contents("http://".STEELHOME_SITE_NOHTTP."/_v2image/showgraph_platts/psImageCreate.php");
		$html =
		'<html lang="en">
		 <head>
			<meta charset="utf8">
			<meta name="Generator" content="EditPlus?">
			<meta name="Author" content="">
			<meta name="Keywords" content="">
			<meta name="Description" content="">
			<meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=10,minimum-scale=0.1,user-scalable=yes">
			<meta http-equiv="X-UA-Compatible" content="IE=Edge">
			<title>普氏</title>
			<link href="css/psbase.css?20190221" rel="stylesheet" type="text/css">
		</head>
		<body>
		<div class="platts">
			<div class="tips pc">抱歉，您没有权限查看该数据。如需咨询订阅，请联系钢之家客服，联系电话：</div>
			<!--
			<div class="tips moblie">抱歉，您没有权限查看该数据。<br>如需咨询订阅，请联系钢之家客服，联系电话：</div>
			-->
			<div class="phone"><span>15800777957/021-50585733</span></div>
			<div class="logo"><img src="http://'.STEELHOME_SITE_NOHTTP.'/_v2image/showgraph_platts/platts_zst_'.date('Y').'.png"></div>
		</div>
		</body>
		</html>';
		return $html;
	}


    public function utf8ToGb2312($str)
    {
        if( mb_detect_encoding($str,"UTF-8, ISO-8859-1, GBK")=="UTF-8" ) {//判断是否不是UTF-8编码，如果不是UTF-8编码，则转换为UTF-8编码
            return iconv("utf-8", "gb2312", $str);
        } else {
            return $str;
        }
    }


} 
?>