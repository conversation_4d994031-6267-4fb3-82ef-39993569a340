﻿<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">


<title>钢之家会议数据大屏</title>
<link rel="stylesheet" href="css/meeting/index.css">
<link rel="stylesheet" href="css/meeting/public.css">
<script src="js/meeting/layui/layui.all.js"></script>
<link rel="stylesheet" href="js/meeting/layui/css/layui.css">

<style>
.header h1 {
   
     width:100%;
     max-width: 1380px;
     background-repeat: no-repeat;
background-position: center;
background-size: cover;
height: 88px;
justify-content: center;
align-items: center;

}
.header h2 {
   width:100%;
   max-width: 1380px;
   background-repeat: no-repeat;
background-position: center;
background-size: cover;
height: 88px;
justify-content: center;
align-items: center;
margin: 0px auto;
}
.nav ul{
    display: flex;
  justify-content: center;
  list-style-type: none; /* 移除列表的标记，如果需要的话 */
  padding: 0; 
}
.nav li.active{height: 40px;line-height: 32px;}
.jq22-content span{color: #00FFFF;}
.jq22-content{width: 96%;margin: 0px auto;margin-top: 6px;display: flex;}
.jq33-content span{color: #00FFFF;}
.jq33-content{width: 96%;margin: 0px auto;margin-top: 10px;}
.progressbar{margin-top: 1px;width: 80%;float: left;}
.jq22-content .left{width: 600px;float: left;line-height: 22px;}
.jq22-content .right{float: right;height: 22px;width: calc(100% - 600px);}
.span{display: block;margin-top: 3px;}
.right2{float: right;height: 40px;width: calc(100% - 240px);}
.header:before{
        background: none;
    }
    .header:after{
        background: none;
    }
    .progressbar{width: 60%;}
    /* .left{width: 500px;float: left;height: 40px;}
    .right{float: right;height: 40px;width: calc(100% - 500px);} */
    .header h1{background: none;}

    .center .center-left .left-top{height: 45%;}
    .center .center-left .left-cen{height:54%;margin-top: 10px;}
    .center .center-right{width: 32%;}
    .center .center-left{width: 32.5%;}

    .center .center-right .right-top{height: 45%;}
    .center .center-right .right-cen{height:54%;margin-top: 10px;}
    .title{font-size: 15px;text-align: left;width: 96%;margin: 0px auto;padding-top: 5px;}
    #chart6title{margin-bottom: 10px;}
    .center{top:55px}
    .header h1 span.font1{padding:  5px 0px 0px;}
    .header h1{height: 45px;}
    .header{height: 0px;}
    .header h1 span.font1{font-size: 25px;}
    .nav{height:40px ;}
    .nav li{width: 33%;height: 40px; line-height: 40px;}


    .leftk{width: 100%;float: left;height: 30px;}
    .rightk{height: 30px;width:100%;margin-bottom: 10px;float: left;}

    .jq33-content .left{width: 100%;float: left;height: 20px;}
    .header h2 span{padding:  10px 0px 0px;font-size: 26px;font-weight: bold;}
    .header h2{height: 36px;color: #01C4F7;text-align: center;}
    .datetitle{font-size: 18px;text-align: center;padding-top: 0px;}
</style>
</head>
<body>

<!-- <div class="zuo" id="zuodian"></div>
<div class="you" id="youdian"></div> -->

<header>
<div class="header">
	<h1><span class="font1" id="maintitle"><img src="images/gzj_logo.png"><font id="surveytitle"><{$meetname.MeetingName}></font></span></h1>
    <{$title2}>
    <{if $params.id==54 || $params.id==55 }> 
        <div style="height: 40px;" ><div class="datetitle title " >2024年11月1日　南京</div></div>
    <{elseif $params.id==94 || $params.id==95 }> 
        <div style="height: 40px;" ><div class="datetitle title " >2025年7月17日</div></div>
    <{else}>
    <{$datetitle}>
    <{/if}>
    
</div>
</header>

<section>

       
    <div class="center" id="mokuai2" style="display:none;<{$top}>">
		
        <div class="center-left fl" style="width: 30%;" >
            <!-- <div class="no">
                <div class="no-bd">
                  <ul>
                    <li>答题人数：<span  id="li4" style="color: #ffeb7b;"></span></li>
                  </ul>
                </div>
              </div> -->

            <div class="left-top rightTop" style="height: 55%;" id="div8">
                <div class="title" id="chart8title"></div>
                 <div class="bottom-b">
                    <div  id="chart8" class="allnav"></div>
                    <div  id="chart8Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                    <div class="panel-footer"></div>
                </div>                    
            </div>
            

        </div>


        <div class="center-left fl" style="width: 68%;" id="fl2">

            <div class="left-top rightTop" id="div9" style="height: 98%;padding-top: 10px;">
                <div class="title" id="chart9title"></div>
                 <div class="bottom-b" id="chart9">
                </div>                    
            </div>
            
            <div class="left-top rightTop" id="div10" style="height: 50%;padding-top: 10px;margin-top: 10px;">
                <div class="title" id="chart10title"></div>
                 <div class="bottom-b" id="chart10">
                </div>                    
            </div>

            <div class="left-top rightTop" id="div100" style="height: 32%;padding-top: 10px;margin-top: 10px;">
                <div class="title" id="chart100title"></div>
                 <div class="bottom-b" id="chart100">
                </div>                    
            </div>
        </div>
         
       
        
    </div>
    <div class="center" id="mokuai1" style="<{$top}>">
		
            <div class="center-left fl">

                <div class="left-top rightTop">
                    <div class="title" id="chart1title"></div>
					 <div class="bottom-b">
                        <div  id="chart1" class="allnav"></div>
						<div  id="chart1Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                        <div class="panel-footer"></div>
                    </div>                    
                </div>
                <div class="left-cen rightTop" id="div2">
                    <div class="title" id="chart2title"></div>
                     <div class="bottom-b">
                        <div  id="chart2" class="allnav"></div>
						<div  id="chart2Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                        <div class="panel-footer"></div>
                    </div>
                </div>

            </div>


            <div class="center-left fl" id="bigDiv1"  <{if $params.mtid==441}>  style="display: none;"<{/if}>>
                  <!-- <div style="height: 40px;" >
                        <div class="title" style="padding-top: 0px;text-align: center;font-size: 18px;"><{$meetingdate}></div>    
                </div> -->
                <div class="left-top rightTop"  id="div6" style="height: 98%;padding-top: 10px;">
                    <div class="title" id="chart6title"></div>
					 <div class="bottom-b" id="chart6">
                        
						<div  id="chart6Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                        <div class="panel-footer"></div>
                    </div>                    
                </div>
            </div>

            <div class="center-left fl" id="bigDiv2" <{if $params.mtid!=441}> style="display: none;"<{/if}>>
                <div class="left-top rightTop" id="div28">
                    <div class="title" id="chart28title"></div>
					 <div class="bottom-b">
                        <div  id="chart28" class="allnav"></div>
						<div  id="chart28Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                        <div class="panel-footer"></div>
                    </div>                    
                </div>
                <div class="left-cen rightTop" id="div29">
                    <div class="title" id="chart29title"></div>
                     <div class="bottom-b">
                        <div  id="chart29" class="allnav"></div>
						<div  id="chart29Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                        <div class="panel-footer"></div>
                    </div>
                </div>

            </div>
			 
			<div class="center-right fr">

					<div class="right-top rightTop" id="div3" <{if $params.mtid==399}> style="height: 100%;"<{/if}> <{if $params.mtid==401}> style="height: 60%;"<{/if}>>
						<div class="title" id="chart3title"></div>
						 <div class="right-top-top">
                            <div id="chart3" class="allnav"></div>
							<div  id="chart3Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                            <div class="panel-footer"></div>
                        </div>
					</div>
					<div class="right-cen"  id="div4"  >
						<div class="title" id="chart4title"></div>
						<div class="right-cen-cent">
							<div id="chart4" class="allnav"></div>
							<div  id="chart4Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                            <div class="panel-footer"></div>
                        </div>
					</div>
			</div>
    </div>


    <div class="center" id="mokuai3" style="display: none; <{$top}>">
		
        <div class="center-left fl" style="width: 24%;">

            <div class="left-top rightTop div11">
                <div class="title" id="chart11title"></div>
                 <div class="bottom-b">
                    <div  id="chart11" class="allnav"></div>
                    <div  id="chart11Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                    <div class="panel-footer"></div>
                </div>                    
            </div>
            <div class="left-cen rightTop div15">
                <div class="title" id="chart15title"></div>
                 <div class="bottom-b">
                    <div  id="chart15" class="allnav"></div>
                    <div  id="chart15Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                    <div class="panel-footer"></div>
                </div>
            </div>

        </div>

        <div class="center-left fl" style="width: 24%;">

            <div class="left-top rightTop div12">
                <div class="title" id="chart12title"></div>
                 <div class="bottom-b">
                    <div  id="chart12" class="allnav"></div>
                    <div  id="chart12Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                    <div class="panel-footer"></div>
                </div>                    
            </div>
            <div class="left-cen rightTop div16">
                <div class="title" id="chart16title"></div>
                 <div class="bottom-b">
                    <div  id="chart16" class="allnav"></div>
                    <div  id="chart16Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                    <div class="panel-footer"></div>
                </div>
            </div>

        </div>

        
        <div class="center-left fl" style="width: 24%;">

            <div class="left-top rightTop div13">
                <div class="title" id="chart13title"></div>
                 <div class="bottom-b">
                    <div  id="chart13" class="allnav"></div>
                    <div  id="chart13Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                    <div class="panel-footer"></div>
                </div>                    
            </div>
            <div class="left-cen rightTop div17">
                <div class="title" id="chart17title"></div>
                 <div class="bottom-b">
                    <div  id="chart17" class="allnav"></div>
                    <div  id="chart17Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                    <div class="panel-footer"></div>
                </div>
            </div>

        </div>

        <div class="center-left fl" style="width: 24%;">

            <div class="left-top rightTop div14">
                <div class="title" id="chart14title"></div>
                 <div class="bottom-b">
                    <div  id="chart14" class="allnav"></div>
                    <div  id="chart14Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                    <div class="panel-footer"></div>
                </div>                    
            </div>
            <div class="left-cen rightTop div18">
                <div class="title" id="chart18title"></div>
                 <div class="bottom-b">
                    <div  id="chart18" class="allnav"></div>
                    <div  id="chart18Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                    <div class="panel-footer"></div>
                </div>
            </div>

        </div>
    </div>

    <div class="center" id="mokuai4" style="display: none;<{$top}>">
        <div class="center-left fl" style="width: 48%;" id="before_1">
            <div class="left-top rightTop div21">
                <div class="title" id="chart21title"></div>
                 <div class="bottom-b">
                    <div  id="chart21" class="allnav"></div>
                    <div  id="chart21Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                    <div class="panel-footer"></div>
                </div>                    
            </div>
            <div class="left-cen rightTop div22">
                <div class="title" id="chart22title"></div>
                 <div class="bottom-b">
                    <div  id="chart22" class="allnav"></div>
                    <div  id="chart22Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                    <div class="panel-footer"></div>
                </div>
            </div>
        </div>

        <div class="center-left fl" style="width: 48%;" id="before_2">
            <div class="left-top rightTop div23">
                <div class="title" id="chart23title"></div>
                 <div class="bottom-b">
                    <div  id="chart23" class="allnav"></div>
                    <div  id="chart23Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                    <div class="panel-footer"></div>
                </div>                    
            </div>
            <div class="left-cen rightTop div24">
                <div class="title" id="chart24title"></div>
                 <div class="bottom-b">
                    <div  id="chart24" class="allnav"></div>
                    <div  id="chart24Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                    <div class="panel-footer"></div>
                </div>
            </div>
        </div>

        <div class="center-left fl" style="width: 32%;display: none;" id="before_3">
            <div class="left-top rightTop div25">
                <div class="title" id="chart25title"></div>
                 <div class="bottom-b">
                    <div  id="chart25" class="allnav"></div>
                    <div  id="chart25Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                    <div class="panel-footer"></div>
                </div>                    
            </div>
            <div class="left-cen rightTop div26">
                <div class="title" id="chart26title"></div>
                 <div class="bottom-b">
                    <div  id="chart26" class="allnav"></div>
                    <div  id="chart26Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                    <div class="panel-footer"></div>
                </div>
            </div>
            <div class="left-cen rightTop div27" style="display: none;">
                <div class="title" id="chart27title"></div>
                 <div class="bottom-b">
                    <div  id="chart27" class="allnav"></div>
                    <div  id="chart27Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                    <div class="panel-footer"></div>
                </div>
            </div>
        </div>
    </div>
</section>

<nav>

<div class="nav"> 
	<ul style="width: 70%;margin: 0px auto;"> 
        <{foreach from=$surveylist key=key item=v}>
        <li id="<{$key}>"><{if $key==1}>会前调查<{elseif $key==2}>会中调查<{elseif $key==3}>会后调查<{/if}></li>
        
        
        <{if $key==1 && $params.mtid!=441 && $questionnum>5}> 
        <li id="next<{$key}>">会前调查2</li>
        <{elseif $key==1 && $params.mtid==441 && $questionnum>6}> 
        <li id="next<{$key}>">会前调查2</li>
        <{/if}>
        <{/foreach}>
	</ul> 
</div>

</nav>
<script>
    var istry='<{$istry}>';
    var mtid='<{$params.mtid}>';
    var isphone=0;
    var id='<{$params.id}>';
</script>
<script src="js/meeting/jquery.min.js"></script>
<script src="js/meeting/axios.min.js"></script>
<script src="js/meeting/echarts.min.js"></script>
<script src="js/meeting/surveyecharts.js?v=20250716"></script>
<script src="js/meeting/fontscroll.js"></script>
<script src="js/meeting/util.js"></script>

<script src="js/meeting/jquery.lineProgressbar.js"></script> 

    <script>
      
	$(function(){  
		
});

        //顶部时间
        function getTime() {
            var myDate = new Date();
            var myYear = myDate.getFullYear(); //获取完整的年份(4位,1970-????)
            var myMonth = myDate.getMonth() + 1; //获取当前月份(0-11,0代表1月)
            var myToday = myDate.getDate(); //获取当前日(1-31)
            var myDay = myDate.getDay(); //获取当前星期X(0-6,0代表星期天)
            var myHour = myDate.getHours(); //获取当前小时数(0-23)
            var myMinute = myDate.getMinutes(); //获取当前分钟数(0-59)
            var mySecond = myDate.getSeconds(); //获取当前秒数(0-59)
            var week = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
            var nowTime;

            nowTime = myYear + '-' + fillZero(myMonth) + '-' + fillZero(myToday) + '&nbsp;&nbsp;' + fillZero(myHour) + ':' + fillZero(myMinute) + ':' + fillZero(mySecond) + '&nbsp;&nbsp;' + week[myDay] + '&nbsp;&nbsp;';
            //console.log(nowTime);
            $('#time').html(nowTime+'企业管理处');
			//$('#lrcsdate').html(myYear+"年"+myMonth+"月"+myToday+"日");
        };

        function fillZero(str) {
            var realNum;
            if (str < 10) {
                realNum = '0' + str;
            } else {
                realNum = str;
            }
            return realNum;
        }
        setInterval(getTime, 1000);
    </script>
</body>
</html>