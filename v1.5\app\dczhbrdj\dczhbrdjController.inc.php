<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );

class dczhbrdjController extends AbstractController{
  
  public function __construct(){
    parent::__construct();
	$this->_action->setDao( new dczhbrdjDao("DRCW") );
	$this->_action->t1Dao=new dczhbrdjDao("MAIN");
	$this->_action->homeDao=new dczhbrdjDao("91R");
  }

	public function v_index(){
		$this->_action->index( $this->_request );
	}
	public function do_savedata(){
		$this->_action->savedata( $this->_request ); //保存文本
	}
}
?>