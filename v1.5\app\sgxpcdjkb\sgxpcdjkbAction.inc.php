<?php
class sgxpcdjkbAction extends AbstractAction{
   public function __construct(){
    parent::__construct();    
  }
  
	public function index($params)
	{
		if($params['datadate']==""){
            
            $flag = 1;
            $lastday = date("Y-m-d");
            while(true)
            {
                if(file_get_contents(APP_URL_WWW.'/isholiday.php?date='.$lastday)!="1")
                { 
                  break;
                } 
                $lastday=date('Y-m-d',strtotime('-'.$flag.' day',strtotime($lastday)));
            }
            $params['datadate'] = $lastday;
        }

		//带钢 山西建龙
		$sxjldg=$this->gcdao->get_CcPriceData("1163","86b22df91a747dbdf21bee29c6e8d992",$params['datadate']);
		$this->assign("sxjldg",$sxjldg);
		//带钢 唐山瑞丰
		$tsrfdg=$this->gcdao->get_CcPriceData("350","45560b4cc25af41e76ddb4a96480ab6a",$params['datadate']);
		$this->assign("tsrfdg",$tsrfdg);
		//北方带钢会议
		$bfhydg=$this->gcdao->get_CcPriceData("1193","5c9fbf5b0c3f0e6fc7a4bb3513b41558",$params['datadate']);
		$this->assign("bfhydg",$bfhydg);

		//山西立恒 高线8-10mmQ235
		$sxlhgx=$this->gcdao->get_CcPriceData("1540","3196205bbfb6d4e79f13d401f353d633",$params['datadate']);
		$this->assign("sxlhgx",$sxlhgx);
		//山西高义 高线8-10mmQ235
		$sxgygx=$this->gcdao->get_CcPriceData("1683","1ba73396be042ffa49eb8eb3806ee09c",$params['datadate']);
		$this->assign("sxgygx",$sxgygx);
		//山西宏达 高线8-10mmQ195
		$sxhdgx195=$this->gcdao->get_CcPriceData("1542","1a54e5e637f85c95e49580b870fef4ac",$params['datadate']);
		$this->assign("sxhdgx195",$sxhdgx195);
		//山西宏达 高线8-10mmQ235
		$sxhdgx=$this->gcdao->get_CcPriceData("1542","5fe5db193cf118865cf3051aad19cdbe",$params['datadate']);
		$this->assign("sxhdgx",$sxhdgx);
		//山西华鑫源 高线8-10mmQ235
		$sxhxygx=$this->gcdao->get_CcPriceData("1539","3196205bbfb6d4e79f13d401f353d633",$params['datadate']);
		$this->assign("sxhxygx",$sxhxygx);

		$priceid = "5660112,5360113,6760151,564112,5341126,6741121,5680264,5380261,6780261,5312342,5712371,5912321,5311522,5322331";

		$data = $this->getpricebypricestr($priceid,$params["datadate"],$params["datadate"],0);
		
		$nowmonth = date('m',strtotime($params["datadate"]));
		$nowyear = date('Y',strtotime($params["datadate"]));
		if ($nowmonth == 1) {
			$lastmonth = 12;
			$lastyear = $nowyear - 1;
		} 
		else 
		{
			$lastmonth = $nowmonth - 1;
			$lastyear = $nowyear;
		}
		$lastEndDay = $lastyear . '-' . $lastmonth . '-' . date('t', mktime(0,0,0,$nowmonth-1,1,$nowyear));
		$e_time = date('Y-m-d',strtotime($lastEndDay));//上个月的月末时间
		$beginLastmonth = date('Y-m-d',mktime(0,0,0,$nowmonth-1,1,$nowyear)); //上月初时间
		$lsmonth = $this->_dao->get_lastmonthday($beginLastmonth,$e_time,5660112);//上月数据时间
		
		$lastdata = $this->getpricebypricestr($priceid,$lsmonth['lasda'],$lsmonth['lasda'],2);//上月末数据

		$beginThismonth = date('Y-m-d',mktime(0,0,0,$nowmonth,1,$nowyear)); //本月初时间
		$thmonth = $this->_dao->get_lastmonthday($beginThismonth,$params["datadate"],5660112);//本月数据时间
		$thismonthdata = $this->getpricebypricestr($priceid,$thmonth['firda'],$params["datadate"],1);//本月数据
		
		$lastmonthdata = $this->getpricebypricestr($priceid,$lsmonth['firda'],$lsmonth['lasda'],1);//上月数据
		
		//汉中钢铁2.75mm*295*C
		$xadg['price'] = $data['price']['5660112'];
		$xadg['zhangdie'] = $this->zhangdie($data['price']['5660112'], $data['oldprice']['5660112']);
		$xadg['syzhangdie'] = $this->zhangdie($data['price']['5660112'], $lastdata['price']['5660112']);
		$xadg['thisavg'] = $thmonth['dacount'] > 0 ? round($thismonthdata['5660112'] / $thmonth['dacount']) : "-";
		$xadg['lastzhangdie'] = $lsmonth['dacount'] > 0 ? $this->zhangdie($xadg['thisavg'], round($lastmonthdata['5660112'] / $lsmonth['dacount'])) :"-";
		$this->assign("xadg",$xadg);

		//汉中钢铁2.5-3.5mm*235*C
		$cddg['price'] = $data['price']['5360113'];
		$cddg['zhangdie'] = $this->zhangdie($data['price']['5360113'], $data['oldprice']['5360113']);
		$cddg['syzhangdie'] = $this->zhangdie($data['price']['5360113'], $lastdata['price']['5360113']);
		$cddg['thisavg'] = $thmonth['dacount'] > 0 ? round($thismonthdata['5360113'] / $thmonth['dacount']) : "-";
		$cddg['lastzhangdie'] = $lsmonth['dacount'] > 0 ? $this->zhangdie($cddg['thisavg'], round($lastmonthdata['5360113'] / $lsmonth['dacount'])) : "-";
		$this->assign("cddg",$cddg);

		//瑞丰2.75*360-455mm*C
		$tsdg['price'] = $data['price']['6760151'];
		$tsdg['zhangdie'] = $this->zhangdie($data['price']['6760151'], $data['oldprice']['6760151']);
		$tsdg['syzhangdie'] = $this->zhangdie($data['price']['6760151'], $lastdata['price']['6760151']);
		$tsdg['thisavg'] = $thmonth['dacount'] >  0 ? round($thismonthdata['6760151'] / $thmonth['dacount']) : "-";
		$tsdg['lastzhangdie'] = $lsmonth['dacount'] > 0 ? $this->zhangdie($tsdg['thisavg'], round($lastmonthdata['6760151'] / $lsmonth['dacount'])) : "-";
		$this->assign("tsdg",$tsdg);

		//太钢2.75mm*1250*C
		$xarj['price'] = $data['price']['564112'];
		$xarj['zhangdie'] = $this->zhangdie($data['price']['564112'], $data['oldprice']['564112']);
		$xarj['syzhangdie'] = $this->zhangdie($data['price']['564112'], $lastdata['price']['564112']);
		$xarj['thisavg'] = $thmonth['dacount'] > 0 ? round($thismonthdata['564112'] / $thmonth['dacount']) : "-";
		$xarj['lastzhangdie'] = $lsmonth['dacount'] > 0 ? $this->zhangdie($xarj['thisavg'], round($lastmonthdata['564112'] / $lsmonth['dacount'])) : "-";
		$this->assign("xarj",$xarj);

		//攀钢2.75mm*1500*C
		$cdrj['price'] = $data['price']['5341126'];
		$cdrj['zhangdie'] = $this->zhangdie($data['price']['5341126'], $data['oldprice']['5341126']);
		$cdrj['syzhangdie'] = $this->zhangdie($data['price']['5341126'], $lastdata['price']['5341126']);
		$cdrj['thisavg'] = $thmonth['dacount'] > 0 ? round($thismonthdata['5341126'] / $thmonth['dacount']) :"-";
		$cdrj['lastzhangdie'] = $lsmonth['dacount'] > 0 ? $this->zhangdie($cdrj['thisavg'], round($lastmonthdata['5341126'] / $lsmonth['dacount'])) :"-";
		$this->assign("cdrj",$cdrj);

		//唐钢2.75mm*1500*6000(卷价-20)
		$tsrj['price'] = $data['price']['6741121'];
		$tsrj['zhangdie'] = $this->zhangdie($data['price']['6741121'], $data['oldprice']['6741121']);
		$tsrj['syzhangdie'] = $this->zhangdie($data['price']['6741121'], $lastdata['price']['6741121']);
		$tsrj['thisavg'] = $thmonth['dacount'] > 0 ? round($thismonthdata['6741121'] / $thmonth['dacount']) : "-";
		$tsrj['lastzhangdie'] =  $lsmonth['dacount'] > 0 ? $this->zhangdie($tsrj['thisavg'], round($lastmonthdata['6741121'] / $lsmonth['dacount'])) : "-";
		$this->assign("tsrj",$tsrj);

		//陕西通达4″*(3.5-4.0)mm
		$xahg['price'] = $data['price']['5680264'];
		$xahg['zhangdie'] = $this->zhangdie($data['price']['5680264'], $data['oldprice']['5680264']);
		$xahg['syzhangdie'] = $this->zhangdie($data['price']['5680264'], $lastdata['price']['5680264']);
		$xahg['thisavg'] = $thmonth['dacount'] > 0 ? round($thismonthdata['5680264'] / $thmonth['dacount']) : "-";
		$xahg['lastzhangdie'] = $lsmonth['dacount'] > 0 ? $this->zhangdie($xahg['thisavg'], round($lastmonthdata['5680264'] / $lsmonth['dacount'])) : "-";
		$this->assign("xahg",$xahg);

		//成都京华（华岐）4″*3.75mm
		$cdhg['price'] = $data['price']['5380261'];
		$cdhg['zhangdie'] = $this->zhangdie($data['price']['5380261'], $data['oldprice']['5380261']);
		$cdhg['syzhangdie'] = $this->zhangdie($data['price']['5380261'], $lastdata['price']['5380261']);
		$cdhg['thisavg'] = $thmonth['dacount'] > 0 ? round($thismonthdata['5380261'] / $thmonth['dacount']) : "-";
		$cdhg['lastzhangdie'] = $lsmonth['dacount'] > 0 ? $this->zhangdie($cdhg['thisavg'], round($lastmonthdata['5380261'] / $lsmonth['dacount'])) : "-";
		$this->assign("cdhg",$cdhg);

		//友发4″*3.75mm
		$tshg['price'] = $data['price']['6780261'];
		$tshg['zhangdie'] = $this->zhangdie($data['price']['6780261'], $data['oldprice']['6780261']);
		$tshg['syzhangdie'] = $this->zhangdie($data['price']['6780261'], $lastdata['price']['6780261']);
		$tshg['thisavg'] = $thmonth['dacount'] > 0 ? round($thismonthdata['6780261'] / $thmonth['dacount']) : "-";
		$tshg['lastzhangdie'] = $lsmonth['dacount'] > 0  ? $this->zhangdie($tshg['thisavg'], round($lastmonthdata['6780261'] / $lsmonth['dacount'])) : "-";
		$this->assign("tshg",$tshg);

		//西安 带钢-热卷
		$xadgjrj['price'] = $data['price']['5660112'] - $data['price']['564112'];
		$xadgjrj['zhangdie'] = $this->zhangdie($data['price']['5660112'] - $data['price']['564112'], $data['oldprice']['5660112'] - $data['oldprice']['564112']);
		$xadgjrj['syzhangdie'] = $this->zhangdie($data['price']['5660112'] - $data['price']['564112'], $lastdata['price']['5660112'] - $lastdata['price']['564112']);
		$xadgjrj['thisavg'] =  $thmonth['dacount'] > 0 ? round(($thismonthdata['5660112'] - $thismonthdata['564112']) / $thmonth['dacount']) : 0;
		$xadgjrj['lastzhangdie'] = $lsmonth['dacount'] >0 ? $this->zhangdie($xadgjrj['thisavg'], round(($lastmonthdata['5660112'] - $lastmonthdata['564112']) / $lsmonth['dacount'])) : "-";
		$this->assign("xadgjrj",$xadgjrj);
		
		//成都 带钢-热卷
		$cddgjrj['price'] = $data['price']['5360113'] - $data['price']['5341126'];
		$cddgjrj['zhangdie'] = $this->zhangdie($data['price']['5360113'] - $data['price']['5341126'], $data['oldprice']['5360113'] - $data['oldprice']['5341126']);
		$cddgjrj['syzhangdie'] = $this->zhangdie($data['price']['5360113'] - $data['price']['5341126'], $lastdata['price']['5360113'] - $lastdata['price']['5341126']);
		$cddgjrj['thisavg'] = $thmonth['dacount'] > 0 ? round(($thismonthdata['5360113'] - $thismonthdata['5341126']) / $thmonth['dacount']) : "-";
		$cddgjrj['lastzhangdie'] = $lsmonth['dacount'] > 0 ? $this->zhangdie($cddgjrj['thisavg'], round(($lastmonthdata['5360113'] - $lastmonthdata['5341126']) / $lsmonth['dacount'])) : "-";
		$this->assign("cddgjrj",$cddgjrj);

		//唐山 带钢-热卷
		$tsdgjrj['price'] = $data['price']['6760151'] - $data['price']['6741121'];
		$tsdgjrj['zhangdie'] = $this->zhangdie($data['price']['6760151'] - $data['price']['6741121'], $data['oldprice']['6760151'] - $data['oldprice']['6741121']);
		$tsdgjrj['syzhangdie'] = $this->zhangdie($data['price']['6760151'] - $data['price']['6741121'], $lastdata['price']['6760151'] - $lastdata['price']['6741121']);
		$tsdgjrj['thisavg'] = $thmonth['dacount'] > 0 ? round(($thismonthdata['6760151'] - $thismonthdata['6741121']) / $thmonth['dacount']) : "-";
		$tsdgjrj['lastzhangdie'] = $lsmonth['dacount'] > 0 ? $this->zhangdie($tsdgjrj['thisavg'], round(($lastmonthdata['6760151'] - $lastmonthdata['6741121']) / $lsmonth['dacount'])) : 0 ;
		$this->assign("tsdgjrj",$tsdgjrj);

		//西安 焊管-带钢
		$xahgjdg['price'] = $data['price']['5680264'] - $data['price']['5660112'];
		$xahgjdg['zhangdie'] = $this->zhangdie($data['price']['5680264'] - $data['price']['5660112'], $data['oldprice']['5680264'] - $data['oldprice']['5660112']);
		$xahgjdg['syzhangdie'] = $this->zhangdie($data['price']['5680264'] - $data['price']['5660112'], $lastdata['price']['5680264'] - $lastdata['price']['5660112']);
		$xahgjdg['thisavg'] = $thmonth['dacount'] > 0 ? round(($thismonthdata['5680264'] - $thismonthdata['5660112']) / $thmonth['dacount']) : "-";
		$xahgjdg['lastzhangdie'] = $lsmonth['dacount'] > 0 ? $this->zhangdie($xahgjdg['thisavg'], round(($lastmonthdata['5680264'] - $lastmonthdata['5660112']) / $lsmonth['dacount'])) : "-";
		$this->assign("xahgjdg",$xahgjdg);

		//成都 焊管-带钢
		$cdhgjdg['price'] = $data['price']['5380261'] - $data['price']['5360113'];
		$cdhgjdg['zhangdie'] = $this->zhangdie($data['price']['5380261'] - $data['price']['5360113'], $data['oldprice']['5380261'] - $data['oldprice']['5360113']);
		$cdhgjdg['syzhangdie'] = $this->zhangdie($data['price']['5380261'] - $data['price']['5360113'], $lastdata['price']['5380261'] - $lastdata['price']['5360113']);
		$cdhgjdg['thisavg'] =  $thmonth['dacount'] >  0 ? round(($thismonthdata['5380261'] - $thismonthdata['5360113']) / $thmonth['dacount']) : "-";
		$cdhgjdg['lastzhangdie'] = $lsmonth['dacount'] > 0 ? $this->zhangdie($cdhgjdg['thisavg'], round(($lastmonthdata['5380261'] - $lastmonthdata['5360113']) / $lsmonth['dacount'])) : "-";
		$this->assign("cdhgjdg",$cdhgjdg);

		//唐山 焊管-带钢
		$tshgjdg['price'] = $data['price']['6780261'] - $data['price']['6760151'];
		$tshgjdg['zhangdie'] = $this->zhangdie($data['price']['6780261'] - $data['price']['6760151'], $data['oldprice']['6780261'] - $data['oldprice']['6760151']);
		$tshgjdg['syzhangdie'] = $this->zhangdie($data['price']['6780261'] - $data['price']['6760151'], $lastdata['price']['6780261'] - $lastdata['price']['6760151']);
		$tshgjdg['thisavg'] = $thmonth['dacount']> 0  ? round(($thismonthdata['6780261'] - $thismonthdata['6760151']) / $thmonth['dacount']) : "-";
		$tshgjdg['lastzhangdie'] =  $lsmonth['dacount'] > 0 ? $this->zhangdie($tshgjdg['thisavg'], round(($lastmonthdata['6780261'] - $lastmonthdata['6760151']) / $lsmonth['dacount'])) : "-";
		$this->assign("tshgjdg",$tshgjdg);

		//成都82B高线
		$cdgx['price'] = $data['price']['5312342'];
		$cdgx['zhangdie'] = $this->zhangdie($data['price']['5312342'], $data['oldprice']['5312342']);
		$cdgx['syzhangdie'] = $this->zhangdie($data['price']['5312342'], $lastdata['price']['5312342']);
		$cdgx['thisavg'] =  $thmonth['dacount'] > 0 ? round($thismonthdata['5312342'] / $thmonth['dacount']) : "-";
		$cdgx['lastzhangdie'] =  $lsmonth['dacount'] > 0 ? $this->zhangdie($cdgx['thisavg'], round($lastmonthdata['5312342'] / $lsmonth['dacount'])) : "-";
		$this->assign("cdgx",$cdgx);

		//兰州82B高线
		$lzgx['price'] = $data['price']['5712371'];
		$lzgx['zhangdie'] = $this->zhangdie($data['price']['5712371'], $data['oldprice']['5712371']);
		$lzgx['syzhangdie'] = $this->zhangdie($data['price']['5712371'], $lastdata['price']['5712371']);
		$lzgx['thisavg'] =  $thmonth['dacount'] > 0 ? round($thismonthdata['5712371'] / $thmonth['dacount']) : "-";
		$lzgx['lastzhangdie'] =  $lsmonth['dacount'] > 0 ? $this->zhangdie($lzgx['thisavg'], round($lastmonthdata['5712371'] / $lsmonth['dacount'])) : "-";
		$this->assign("lzgx",$lzgx);

		//银川82B高线
		$ycgx['price'] = $data['price']['5912321'];
		$ycgx['zhangdie'] = $this->zhangdie($data['price']['5912321'], $data['oldprice']['5912321']);
		$ycgx['syzhangdie'] = $this->zhangdie($data['price']['5912321'], $lastdata['price']['5912321']);
		$ycgx['thisavg'] = $thmonth['dacount'] > 0 ? round($thismonthdata['5912321'] / $thmonth['dacount']) :"-";
		$ycgx['lastzhangdie'] = $lsmonth['dacount'] > 0 ? $this->zhangdie($ycgx['thisavg'], round($lastmonthdata['5912321'] / $lsmonth['dacount'])) : "-";
		$this->assign("ycgx",$ycgx);

		//成都65#高线
		$cdgx65['price'] = $data['price']['5311522'];
		$cdgx65['zhangdie'] = $this->zhangdie($data['price']['5311522'], $data['oldprice']['5311522']);
		$cdgx65['syzhangdie'] = $this->zhangdie($data['price']['5311522'], $lastdata['price']['5311522']);
		$cdgx65['thisavg'] = $thmonth['dacount'] > 0  ? round($thismonthdata['5311522'] / $thmonth['dacount']) : "-";
		$cdgx65['lastzhangdie'] = $lsmonth['dacount'] > 0 ? $this->zhangdie($cdgx65['thisavg'], round($lastmonthdata['5311522'] / $lsmonth['dacount'])) : "-";
		$this->assign("cdgx65",$cdgx65);

		//成都Q235圆钢16-25mm
		$cdyg['price'] = $data['price']['5322331'];
		$cdyg['zhangdie'] = $this->zhangdie($data['price']['5322331'], $data['oldprice']['5322331']);
		$cdyg['syzhangdie'] = $this->zhangdie($data['price']['5322331'], $lastdata['price']['5322331']);
		$cdyg['thisavg'] = $thmonth['dacount'] > 0 ?  round($thismonthdata['5322331'] / $thmonth['dacount']) : "-";
		$cdyg['lastzhangdie'] =  $lsmonth['dacount'] > 0 ? $this->zhangdie($cdyg['thisavg'], round($lastmonthdata['5322331'] / $lsmonth['dacount'])) : "-";
		$this->assign("cdyg",$cdyg);

		$this->assign("datadate",$params["datadate"]);
		$this->assign("mode",$params["mode"]);
		//$this->assign("GUID",$GUID);
	}

	function getpricebypricestr($pricestr,$stime,$etime,$issum)
	{
		$idnumber=explode(',',$pricestr);
		$six=$seven=array();
		foreach($idnumber  as $id ){
			if (strlen ( $id ) == 6){//判断id 的字符长度 
				if(!in_array($id ,$six)){
					$six[]= $id ; 			
				} 							
			} 
			if (strlen ($id ) == 7) {
				if(!in_array($id,$seven)){
					$seven[]= $id;
				} 
			}
		}
		$sixid_str=implode("','",$six);
 		$sevenid_str=implode("','",$seven);
		$mconmanagedate.="(mconmanagedate>='".$stime." 00:00:00' and mconmanagedate<='".$etime." 23:59:59')";

		if($issum == "0")
		{
			$sixconditions = "marketconditions.pricemk price,marketconditions.topicture, marketconditions.oldpricemk oldprice,marketconditions.mconmanagedate";
			$sevenconditions = "marketconditions.price,marketconditions.mastertopid as topicture, marketconditions.oldprice,marketconditions.mconmanagedate";
		}
		else
		{
			$sixconditions = "marketconditions.pricemk price,marketconditions.topicture,marketconditions.mconmanagedate";
			$sevenconditions = "marketconditions.price,marketconditions.mastertopid as topicture,marketconditions.mconmanagedate";
		}

		if($sixid_str!=''){
 			$PriceListSQL=" select ".$sixconditions." from marketconditions where $mconmanagedate and marketconditions.topicture in ('$sixid_str')";
 		}
 		if($sevenid_str!=''){
 			$PriceListSQL=" select ".$sevenconditions." from marketconditions where $mconmanagedate and marketconditions.mastertopid in ('$sevenid_str')";
 		}
 		if($sixid_str!=''&&$sevenid_str!=''){
	 		$PriceListSQL=" select ".$sixconditions." from marketconditions where $mconmanagedate and marketconditions.topicture in ('$sixid_str') UNION( select ".$sevenconditions." from marketconditions where $mconmanagedate  and marketconditions.mastertopid in('$sevenid_str'))";
 		}
		//echo $PriceListSQL;
		$PriceList= $this->_dao->query($PriceListSQL);
		
		if($issum == "1")
		{
			foreach($PriceList as $v )
			{
				if(strstr($v['price'],"-"))
				{
					$avgprice = explode("-",$v['price']);
					$v['price'] = round(($avgprice['0']+$avgprice['1'])/2,2);
				}
				
				if(!isset($data[$v['topicture']]))
					$data[$v['topicture']] = $v['price'];
				else
					$data[$v['topicture']] += $v['price'];
			}
		}
		else
		{
			foreach($PriceList  as $v )
			{
				if(strstr($v['price'],"-")){
					$avgprice = explode("-",$v['price']);
					$v['price'] = round(($avgprice['0']+$avgprice['1'])/2,2);
				}
				if($issum == "0")
				{
					if(strstr($v['oldprice'],"-")){
						$avgoldprice = explode("-",$v['oldprice']);
						$v['oldprice'] = round(($avgoldprice['0']+$avgoldprice['1'])/2,2);
					}
					$data["oldprice"][$v['topicture']]=  $v['oldprice'];
				}
				
				$data["price"][$v['topicture']]=  $v['price'];
			}
		}
		return $data;
	}

	function zhangdie($nowprice, $lastprice)
	{
		$zhangdie = $nowprice - $lastprice;
		if($zhangdie == "0")
			$zhangdie = "-";
		else if($zhangdie < "0")
			$zhangdie = "<font color='green'>↓".($lastprice - $nowprice)."</font>";
		else
			$zhangdie = "<font color='red'>↑".$zhangdie."</font>";

		return $zhangdie;
	}
}
?>