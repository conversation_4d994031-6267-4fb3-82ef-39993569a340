<?php
header("Content-type: text/html; charset=utf-8");
class dayreportAction extends AbstractAction{
   public function __construct(){
    parent::__construct();    
  }
	public function gc($params)
	{
		//echo microtime()."</br>";
		$curdate=empty($params['curdate'])?date("Y-m-d"):$params['curdate'];
		$mode=$params['mode'];
		$flat="--";
        $mrgc=array();
        $mrgc2=array();
		$idarr_6=array();
		$idarr_7=array("1311423","1211122","F111123","0822439","0822433","0822434","1260151","073012c","0730421");
		$ng_idarr=array('Nglw0001','Nglw0004','Nglw0005','Ngdg0002','Ngdg0009','Ngdg0010','Ngzb0014','Ngzb0016');
		$drc_arr=array('25b67ebabb304fdb9298942ad2528388');
		$all_idarr=array_merge($idarr_6,$idarr_7,$ng_idarr,$drc_arr);

		$days=array();
		$yearfirstday=$this->getlastworkday(date("Y-1-1",strtotime($curdate)-24*3600),"-1");
		$currentfirstday=date('Y-m-d', strtotime("last sunday +1 day",strtotime($curdate)));
		$lastworkday=$this->getlastworkday($curdate);
		$lastweekday=$this->getlastworkday($currentfirstday);
		//echo "lastworkday:".$lastworkday."<br> yearfirstday:".$yearfirstday."<br>";exit;
		
		$dataarr_sql="select * from marketconditions where ((mconmanagedate>'$lastweekday' and mconmanagedate<'".date("Y-m-d",strtotime($curdate)+24*3600)."') or (mconmanagedate>'$yearfirstday' and mconmanagedate<'".date("Y-m-d",strtotime($yearfirstday)+24*3600)."')) and  mastertopid in ('".implode("','",$idarr_7)."')";//echo $dataarr_sql."</br>";
		$dataarr=$this->homeDao->query($dataarr_sql);

		$ngdata_sql="select * from ng_data_table where (dta_ym='$yearfirstday' or (dta_ym>='$lastweekday' and dta_ym<='$curdate')) and datamark in ('".implode("','",$ng_idarr)."')"; //echo $ngdata_sql."</br>";
		$ngdata=$this->_dao->query($ngdata_sql);

		$drcdata_sql="SELECT * FROM  `ct_price_info` WHERE (ftime='$yearfirstday' or (ftime>='$lastweekday' and ftime<='$curdate')) and `onlyid` =  '25b67ebabb304fdb9298942ad2528388' AND gcid =121 AND is_show =2 AND  `the_price_tax` !=  ''";//echo $drcdata_sql."</br>";

		$drcdata=$this->gcDao->query($drcdata_sql);
		
		foreach($dataarr as $arr){
			$date=date("Y-m-d",strtotime($arr["mconmanagedate"]));
			$price=explode("-",$arr['price']);
			$price=round(array_sum($price)/count($price),0);
			if(in_array($arr["mastertopid"],$idarr_7)){
				$id=$arr["mastertopid"];
			}else{
				$id=$arr["topicture"];
			}
			
			$mrgc[$date][$id]=$price;
		}
		if(!array_key_exists($curdate,$mrgc)) $mrgc[$curdate]=array();

		foreach($drcdata as $arr){
			$date=date("Y-m-d",strtotime($arr["ftime"]));
			$id=$arr["onlyid"];
			$price=$arr["the_price_tax"];
			$mrgc[$date][$id]=$price;
		}
		//print"<pre>";print_r($mrgc);print"</pre>";

		foreach($ngdata as $kk=>$vv){
			if(array_key_exists($vv['dta_ym'],$mrgc)&&$vv['dta_ym']!=$currentfirstday)
				$mrgc[$vv["dta_ym"]][$vv['DataMark']]=$vv['Value'];
		}

		foreach($mrgc as $date=>$arr){
			$days[]=$date;
			foreach($all_idarr as $id){
				if($arr[$id]=="") $mrgc[$date][$id]=$flat;
			}
		}

		$ncdata=$mrgc[$yearfirstday];


		//foreach($mrgc as $kdate=>$arr){
		//	$mrgc[$kdate]['SHQHDAY_4']=$qihuo["SHQHDAY_4"][$kdate]==""?$flat:$qihuo["SHQHDAY_4"][$kdate];
		//}

		$todaydata=$mrgc[$curdate];
		//unset($mrgc[$curdate]);

		foreach($all_idarr as $id){
			if($todaydata[$id]!=$flat){
				$lastworkday_zd[$id]=$this->getzd($todaydata[$id]-$mrgc[$lastworkday][$id]);
				$lastweekday_zd[$id]=$this->getzd($todaydata[$id]-$mrgc[$lastweekday][$id]);
				$yearfirstday_zd[$id]=$this->getzd($todaydata[$id]-$mrgc[$yearfirstday][$id]);
				if($todaydata[$id]===""){
					$todaydata[$id]=$flat;
					$lastworkday_zd[$id]=$flat;
					$lastweekday_zd[$id]=$flat;
					$yearfirstday_zd[$id]=$flat;
				}else{
					if($lastworkday_zd[$id]==="") $lastworkday_zd[$id]=$flat;
					if($lastweekday_zd[$id]==="") $lastweekday_zd[$id]=$flat;
					if($yearfirstday_zd[$id]==="") $yearfirstday_zd[$id]=$flat;
				}
			}else{
				$lastworkday_zd[$id]=$flat;
				$lastweekday_zd[$id]=$flat;
				$yearfirstday_zd[$id]=$flat;
			}
		}
		//unset($mrgc[$yearfirstday]);

		//print"<pre>";print_r($mrgc);print"</pre>";
		foreach($mrgc as $kdate=>$arr){
			if($kdate!=$curdate&&$kdate!=$yearfirstday){
				$kdate=str_replace("-","/",$kdate);
				$mrgc2[$kdate]=$arr;
			}
		}
		ksort($mrgc2);
		//echo microtime()."</br>";
		$this->assign("yearfirstday",date("Y/m/d",strtotime($yearfirstday)));
		$this->assign("selectdate",$curdate);
		$this->assign("curdate",date("Y/m/d",strtotime($curdate)));
		$this->assign("ncdata",$ncdata);
		$this->assign("mrgc",$mrgc2);
		$this->assign("todaydata",$todaydata);
		$this->assign("lastworkday_zd",$lastworkday_zd);
		$this->assign("lastweekday_zd",$lastweekday_zd);
		$this->assign("yearfirstday_zd",$yearfirstday_zd);
		$this->assign("everydaydata",$mrgc[$lastworkday]);
		$this->assign("nowday",date("Y年n月j日",strtotime($curdate)));
		$this->assign("mode",$mode);
	}

	public function yl($params)
	{
		//echo microtime()."</br>";
		$curdate=empty($params['curdate'])?date("Y-m-d"):$params['curdate'];
		$flat="--";
		$idarr_6=array("448610","B98650","678310","010203");
		$idarr_7=array("D286105","D286103","D286104","6784111","1182141","0762601","H986401");
		$qharr=array("SHQHDAY_4","SHQHDAY_20");//SHQHDAY_4:螺纹钢，SHQHDAY_20:铁矿石
		$dollar_arr=array('2','3');//2:美黄金,3:美原油
		$blockid_arr=array('hf_GC','hf_CL');//hf_GC:黄金,hf_CL:原油
		$idarr=array_merge($idarr_6,$idarr_7);

		$allpz=array_merge($qharr,$idarr,$dollar_arr);
		//echo "<pre>";print_r($idarr);exit;
        $mryl = array();
		$days=array();
		$currentfirstday=date('Y-m-d', strtotime("last sunday +1 day",strtotime($curdate)));
		$lastworkday=$this->getlastworkday($curdate);
		$lastweekday=$this->getlastworkday($currentfirstday);
		//echo "lastworkday:".$lastworkday."<br> lastweekday:".$lastweekday."<br>";exit;
		
		$datasql="select * from marketconditions where (mconmanagedate>'$lastweekday' and mconmanagedate<'".date("Y-m-d",strtotime($curdate)+24*3600)."') and  (topicture in ('".implode("','",$idarr_6)."') or mastertopid in ('".implode("','",$idarr_7)."'))";
		//echo $datasql."<br>";
		$dataarr=$this->homeDao->query($datasql);
		
		foreach($dataarr as $arr){
			$price=explode("-",$arr['price']);
			$price=$price=round(array_sum($price)/count($price),0);;
			$arr["mconmanagedate"]=date("Y-m-d",strtotime($arr["mconmanagedate"]));
			if(in_array($arr["mastertopid"],$idarr_7)){
				$id=$arr["mastertopid"];
			}else{
				$id=$arr["topicture"];
			}
			
			$mryl[$arr["mconmanagedate"]][$id]=$price;
		}
		if(!array_key_exists($curdate,$mryl)) $mryl[$curdate]=array();
		foreach($mryl as $k=>$v){
			$days[]=$k;
			//echo $k."<br>";
			foreach($idarr as $id){
				//echo $v[$id]."</br>";
				if($v[$id]=="") $mryl[$k][$id]=$flat;
			}
		}
		
		ksort($days);
		$qihuoarr=$this->_dao->query("select * from data_table where dta_ym>='".$days[0]."' and dta_ym<='".$curdate."' and dta_maxValStatus=1 and dta_type in ('".implode("','",$qharr)."') ");

		foreach($qihuoarr as $k=>$v){
			$qihuo[$v["dta_type"]][$v["dta_ym"]]=$v["dta_7"];
		}
		$midsql="select max(id) from dollar_dzprice where type in (".implode(",",$dollar_arr).") and date>='".$days[0]."' and date<='".$curdate."' group by type,date";
		$mids=$this->homeDao->getOnes($midsql);

		if($mids){
			$meisql="select date,type,value,updown from dollar_dzprice where id in (".implode(",",$mids).")";
		}else{
			$meisql="select date,type,value,updown from dollar_dzprice where id =''";
		}
		//echo $meisql."<br>";
		$meiarr=$this->homeDao->query($meisql);

		foreach($meiarr as $k=>$v){
			$mei[$v["type"]][$v["date"]]=$v["value"];
		}

		foreach($mryl as $kdate=>$arr){
			$mryl[$kdate]['SHQHDAY_4']=$qihuo["SHQHDAY_4"][$kdate]==""?$flat:$qihuo["SHQHDAY_4"][$kdate];
			$mryl[$kdate]['SHQHDAY_20']=$qihuo["SHQHDAY_20"][$kdate]==""?$flat:$qihuo["SHQHDAY_20"][$kdate];
			$mryl[$kdate]['2']=$mei["2"][$kdate]==""?$flat:round($mei["2"][$kdate],2);
			$mryl[$kdate]['3']=$mei["3"][$kdate]==""?$flat:round($mei["3"][$kdate],2);
		}

		$mryl[$curdate]['2']=$this->getvalbySinaAPI('hf_GC');
		$mryl[$curdate]['3']=$this->getvalbySinaAPI('hf_CL');


		$todaydata=$mryl[$curdate];
		$todaydata['2']=$mryl[$curdate]['2'][0][1];
		$todaydata['3']=$mryl[$curdate]['3'][0][1];
		unset($mryl[$curdate]);
		//print"<pre>";print_r($todaydata);exit;
		foreach($allpz as $id){
			if($todaydata[$id]!=$flat){
				$lastworkday_zd[$id]=$this->getzd(floatval( $todaydata[$id] )- floatval( $mryl[$lastworkday][$id] ) );
				$lastweekday_zd[$id]=$this->getzd(floatval( $todaydata[$id] )- floatval( $mryl[$lastweekday][$id] ) );
				if($todaydata[$id]===""){
					$todaydata[$id]=$flat;
					$lastworkday_zd[$id]=$flat;
					$lastweekday_zd[$id]=$flat;
				}else{
					if($lastworkday_zd[$id]==="") $lastworkday_zd[$id]=$flat;
					if($lastweekday_zd[$id]==="") $lastweekday_zd[$id]=$flat;
				}
			}else{
				$lastworkday_zd[$id]=$flat;
				$lastweekday_zd[$id]=$flat;
			}
		}
		
		$mryl2 = array();
		foreach($mryl as $kdate=>$arr){
			$kdate=str_replace("-","/",$kdate);
			$mryl2[$kdate]=$arr;
		}

		ksort($mryl2);
		//echo microtime()."</br>";

		//print"<pre>";print_r($todaydata);print"</pre>";//print_r($mryl);
		$this->assign("selectdate",$curdate);
		$this->assign("curdate",date("Y/m/d",strtotime($curdate)));
		$this->assign("mryl",$mryl2);
		$this->assign("todaydata",$todaydata);
		$this->assign("lastworkday_zd",$lastworkday_zd);
		$this->assign("lastweekday_zd",$lastweekday_zd);
		$this->assign("nowday",date("Y年n月j日",strtotime($curdate)));
		$this->assign("mode",$params['mode']);
	}

	function getlastworkday($date="",$pretype=1){
		if($pretype=="-1"){
			$fuhao=">";
			$jian="-1";
			$order = "";
		}else{
			$fuhao="<";
			$jian="1";
			$order = "desc";
		}
		if($date=="") $date=date("Y-m-d");
		$sql="select date,isholiday from steelhome.holiday where date".$fuhao."'$date' order by date $order limit 7"; 
		//echo $sql."<br>";//exit;
		$workday="";
		$holiday=$this->homeDao->aquery($sql);
		//echo "<pre>";print_r($holiday);
		$i=300;
		while(empty($workday)&&$i--){
			$date=date("Y-m-d",strtotime($date)-($jian)*24*3600);
			if($holiday[$date]==="0"){
				$workday=$date;
			}elseif($holiday[$date]=="1"){
				continue;
			}else{
				$weekday=date("N",strtotime($date));
				if($weekday=="6"||$weekday=="7"){
					continue;
				}else{
					$workday=$date;
				}
			}
		}
		//print"<pre>";print_r($workday);
		return $workday;
	}

	function getzd($zd,$format="+",$jingdu="-1"){
		if($jingdu>=0){
			$zd=round($zd,$jingdu);
		}
		if(abs($zd)>0.01){
			if($zd>0){
				if($format=="+") $zd="<font color='red'>+".$zd."</font>";
				else $zd="<font color='red'>".$zd."</font>";
			}else{
				$zd="<font color='green'>".$zd."</font>";
			}
		}else{
			$zd=0;
		}
		return $zd;
	}

	function getvalbySinaAPI($type,$date=""){
		$url ="http://hq.sinajs.cn/list=$type".$date;
		$tmp = file_get_contents( $url );
		preg_match_all("|\"(.*)\"|U", $tmp, $matchs);
		$data1 = $matchs[1];
		$data = array();

		foreach($data1 as &$item){
			if($item == "")continue;
			$data[] = explode(",",$item);
		}

		if( !$data ) return false;
		$tmpData = array();

		foreach( $data as $key => &$v ){
			$temp_item = array();
			if($type!="DINIW")
			{
				$temp_item[1] = $v[0];//最新价
				$temp_item[2] = $v[13];//名称
				$temp_item[3] = $v[12];//时间
				$temp_item[4] = $v[0]-$v[7];//涨跌
			}
			else
			{
				$temp_item[1] = $v[1];//最新价
				$temp_item[2] = $v[9];//名称
				$temp_item[3] = $v[10];//时间
				$temp_item[4] = $v[1]-$v[3];//涨跌
			}
			$tmpData[]=$temp_item;
		}
		return $tmpData;
	}
}
?>