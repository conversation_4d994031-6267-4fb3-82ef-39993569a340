<?php
class dcpredictionAction extends AbstractAction{
   public function __construct(){
    parent::__construct();    
  }
	public function index($params)
	{
	}

	public function currentview($params){
		$type=$params['Type'];
		$mode=$params["mode"];
		$GUID=$params["GUID"];
		$this->fg_type($type,$vtype,$ptype);
		$sql="select id,Title,Detail from NanGangForcast where Pinzhong='$vtype' and Type='$ptype' order by CreateTime desc,UpdateTime desc limit 1";
		$mes=$this->_dao->getRow($sql);
		if($mes['Detail']!=""){
			$mes['Title']=html_entity_decode($mes['Title'], ENT_QUOTES,"UTF-8");
			$mes['Title']=html_entity_decode($mes['Title'], ENT_QUOTES,"UTF-8");

			$mes['Detail']=html_entity_decode($mes['Detail'], ENT_QUOTES,"UTF-8");
			$mes['Detail']=html_entity_decode($mes['Detail'], ENT_QUOTES,"UTF-8");
			$mes['Detail']=str_replace("&nbsp;","&emsp;",$mes['Detail']);
			$this->assign("arr_show",1);

			$conarr=explode("/",$mes['Detail']);
			foreach($conarr as $i=>$d){
				if(strstr($d,".css")){

					if($mode=="1"||$mode==""){								//pc端
						$d=str_replace("ng_forecast_large.css","ng_forecast_small.css",$d);
						$d=str_replace("ng_forecast_middle.css","ng_forecast_small.css",$d);
					}elseif($mode=="3"||$mode=="5"){						//平板
						$d=str_replace("ng_forecast_small.css","ng_forecast_middle.css",$d);
						$d=str_replace("ng_forecast_large.css","ng_forecast_middle.css",$d);
					}elseif($mode=="2"||$mode=="4"){						//手机
						$d=str_replace("ng_forecast_middle.css","ng_forecast_large.css",$d);
						$d=str_replace("ng_forecast_small.css","ng_forecast_large.css",$d);
					}
					$conarr[$i]=$d;
					//echo $conarr[$i];
				}
			}
			$mes['Detail']=implode("/",$conarr);
		}
		//added by shizg started 2020/9/22
		$mes['Detail'] = str_replace( "http:", "", $mes['Detail'] );
		//added by shizg ended 2020/9/22
		//print"</pre>";print_r($mes);
		$this->assign("title1",$GLOBALS['prediction_model'][$vtype]);
		$this->assign("title2",$GLOBALS['predictionname'][$ptype]);
		$this->assign("mode",$mode);
		$this->assign("arr",$mes);
		$this->assign("type",$type);
		$this->assign("GUID",$GUID);
	}

	public function previousview($params){
		$type=$params['Type'];
		$mode=$params["mode"];
		$GUID=$params["GUID"];
		$page=$this->formatpage($params["page"]);
		$pagenum="10";//一页显示的条数
		$search="";
		$startdate=$params["startdate"];
		$enddate=$params["enddate"];
		$this->fg_type($type,$vtype,$ptype);
		{
			$select=" select count(1) from NanGangForcast ";
			if($vtype){
				$where=" where Pinzhong='$vtype' and Type='$ptype' ";
			}else{
				$where=" where Type='$ptype' ";
			}
			$search.=$params["title"]==""?" ":" and Title like binary '%$params[title]%' ";
			$search.=$startdate==""?" ":" and str_to_date(CreateTime,'%Y-%m-%d')>='$startdate' ";
			$search.=$enddate==""?" ":" and str_to_date(CreateTime,'%Y-%m-%d')<='$enddate' ";
			$orderby=" order by CreateTime desc,UpdateTime desc ";
			$limit=" limit ".(($page-1)*$pagenum).",".$pagenum;
		}
		$amount=$this->_dao->getone($select.$where.$search);
		$select=" select * from NanGangForcast ";
		$sql=$select.$where.$search.$orderby.$limit;//echo $sql;
		$mes2=$this->_dao->query($sql);
		foreach($mes2 as $k=>$v){
			$v['Title']=html_entity_decode($v['Title'], ENT_QUOTES,"UTF-8");
			$v['Title']=html_entity_decode($v['Title'], ENT_QUOTES,"UTF-8");
			$v['Title'] = str_replace("<h2 class='titlestyle' >", "",$v['Title']);
			$v['Title'] = str_replace("</h2>", "",$v['Title']);

			$v['Title'] = str_replace("<h3>", "",$v['Title']);
			$v['Title'] = str_replace("<h3 >", "",$v['Title']);
			$v['Title'] = str_replace("</h3>", "",$v['Title']);
			$mes[$k+($page-1)*$pagenum]=$v;
		}
		//print"<pre>";print_r($mes);print"</pre>";exit;
		$pagelabel=$this->getpagelabel($amount,$pagenum,$page,"dcprediction.php?view=previous&Type=".$type."&GUID=".$GUID."&mode=".$mode);
		$this->assign("title1",$GLOBALS['prediction_model'][$vtype]);
		$this->assign("title2",$GLOBALS['predictionname'][$ptype]);
		$this->assign("mode",$mode);
		$this->assign("arr",$mes);
		$this->assign("type",$type);
		$this->assign("GUID",$GUID);
		$this->assign("pagelabel",$pagelabel);
		//$page = $params['page'] == '' ? 1 : $params['page'];
		$url = "dcprediction.php";
		unset( $params['page'] );
		$pagebar = pagebar( $url, $params, $pagenum, $page, $amount );
		$this->assign( "pagebar", $pagebar );
	}

	public function dataview($params){
		$type=$params['Type'];
		$mode=$params["mode"];
		$GUID=$params["GUID"];
		$id=$params["id"];
		$this->fg_type($type,$vtype,$ptype);
		$sql="select id,Title,Detail from NanGangForcast where Pinzhong='$vtype' and Type='$ptype' and id='$id' limit 1";//echo $sql;
		$mes=$this->_dao->getRow($sql);
		
		$mes['Title']=html_entity_decode($mes['Title'], ENT_QUOTES,"UTF-8");
		$mes['Title']=html_entity_decode($mes['Title'], ENT_QUOTES,"UTF-8");
		if(!strstr($mes['Title'],"</h2>")) $mes['Title']="<h2 class='titlestyle' >".$mes['Title']."</h2>";

		$mes['Detail']=html_entity_decode($mes['Detail'], ENT_QUOTES,"UTF-8");
		$mes['Detail']=html_entity_decode($mes['Detail'], ENT_QUOTES,"UTF-8");
		$mes['Detail']=str_replace("&nbsp;","&emsp;",$mes['Detail']);

		$conarr=explode("/",$mes['Detail']);
		foreach($conarr as $i=>$d){
			if(strstr($d,".css")){
				if($mode=="1"||$mode==""){								//pc端
					$d=str_replace("ng_forecast_large.css","ng_forecast_small.css",$d);
					$d=str_replace("ng_forecast_middle.css","ng_forecast_small.css",$d);
				}elseif($mode=="3"||$mode=="5"){						//平板
					$d=str_replace("ng_forecast_small.css","ng_forecast_middle.css",$d);
					$d=str_replace("ng_forecast_large.css","ng_forecast_middle.css",$d);
				}elseif($mode=="2"||$mode=="4"){						//手机
					$d=str_replace("ng_forecast_middle.css","ng_forecast_large.css",$d);
					$d=str_replace("ng_forecast_small.css","ng_forecast_large.css",$d);
				}
				$conarr[$i]=$d;
				//echo $conarr[$i];
			}
		}
		$mes['Detail']=implode("/",$conarr);
		//added by shizg started 2020/9/22
		$mes['Detail'] = str_replace( "http:", "", $mes['Detail'] );
		//added by shizg ended 2020/9/22

		//print"<pre>";print_r($mes);print"</pre>";
		$this->assign("title1",$GLOBALS['prediction_model'][$vtype]);
		$this->assign("title2",$GLOBALS['predictionname'][$ptype]);
		$this->assign("mode",$mode);
		$this->assign("arr",$mes);
		$this->assign("type",$type);
		$this->assign("GUID",$GUID);
	}

	public function exportdata($params){
		$type=$params['Type'];
		$mode=$params["mode"];
		$GUID=$params["GUID"];
		$id=$params["id"];
		$this->fg_type($type,$vtype,$ptype);
		$sql="select id,Title,Detail from NanGangForcast where Pinzhong='$vtype' and Type='$ptype' and id='$id' limit 1";//echo $sql;
		$mes=$this->_dao->getRow($sql);
		$mes['Detail']=html_entity_decode($mes['Detail'], ENT_QUOTES,"UTF-8");
		$mes['Detail']=html_entity_decode($mes['Detail'], ENT_QUOTES,"UTF-8");
		$mes['Detail']=str_replace("&nbsp;","&emsp;",$mes['Detail']);

		$mes['Title']=html_entity_decode($mes['Title'], ENT_QUOTES,"UTF-8");
		$mes['Title']=html_entity_decode($mes['Title'], ENT_QUOTES,"UTF-8");
		$mes['Title'] = str_replace("<h2 class='titlestyle' >", "",$mes['Title']);
		$mes['Title'] = str_replace("</h2>", "",$mes['Title']);


		$mes['Title'] = str_replace("<div style='text-align:center;'>", "",$mes['Title']);
		$mes['Title'] = str_replace("</div>", "",$mes['Title']);

			
		$conarr=explode("/",$mes['Detail']);
		foreach($conarr as $i=>$d){
			if(strlen($d)<100&&strstr($d,".css")){
				$d=str_replace("_large.css","_small.css",$d);
				$d=str_replace("_middle.css","_small.css",$d);
				$conarr[$i]=$d;
				//echo $conarr[$i];
			}
		}
		$con=implode("/",$conarr);

		$name=$mes["Title"];
		ob_start(); //打开缓冲区 
		header("Cache-Control: public");
		Header("Content-type: application/octet-stream"); 
		Header("Accept-Ranges: bytes"); 

		if (strpos($_SERVER["HTTP_USER_AGENT"],'MSIE')) {
			header('Content-Disposition: attachment; filename='.$name.'.doc');
		}else if (strpos($_SERVER["HTTP_USER_AGENT"],'Firefox')) {
			header('Content-Disposition: attachment; filename='.$name.'.doc'); 
		} else {
			header('Content-Disposition: attachment; filename='.$name.'.doc');
		}
		header("Pragma:no-cache");
		header("Expires:0");
		echo $con;
		ob_end_flush();
		//$_SESSION["gogo"]="download";
		exit;
	}
	
	private function fg_type($type,&$variety_type,&$prediction_type){//分割:品种类型，预测类型
		$typename=$GLOBALS['predictions'][$type];
		if($typename=="日预测"){
			$variety_type="0";
			foreach($GLOBALS['predictionname'] as $k=>$v){
				if($v==$typename) $prediction_type=$k;
			}
			return;
		}
		$len=strlen($typename);
		$variety_type=substr($typename,0,$len-6);
		$prediction_type=substr($typename,$len-6,6);
		//echo $len." ".$variety_type." ".$prediction_type;

		foreach($GLOBALS['prediction_model'] as $k=>$v){
			if($v==$variety_type) $variety_type=$k;
		}
		foreach($GLOBALS['predictionname'] as $k=>$v){
			if($v==$prediction_type) $prediction_type=$k;
		}
	}

	private function getpagelabel($amount,$pagenum,$page,$url){
		$pagemax=($amount%$pagenum==0)?round($amount/$pagenum,0):(floor($amount/$pagenum)+1);
		//echo $amount." ".$pagenum." ".$page." ".$pagemax." ".$url;
		$label="";
		$ye=3;		//最多隔页显示的可点击页数
		if($pagemax==0) return "<div class='page tc oh mt20'><span class='disabled'>上一页</span> <span class='me'> 1 </span> <span class='disabled'>下一页</span></div>";			//如果没有数据，则直接返空
		if($page==1) $label="<span class='disabled'>上一页</span><span class='me'>1</span>"; //第一页
		else $label="<a href='$url&page=".($page-1)."' class='sxy'>上一页</a><a href='$url&page=1'>1</a>";
		for($i=2;$i<$pagemax;$i++){
			if($i-$page>$ye||$page-$i>$ye) continue;
			elseif($i-$page==$ye||$page-$i==$ye){
				$label.="...";
			}elseif($i==$page){
				$label.="<span class='me'>$i</span>";
			}else{
				$label.="<a href='$url&page=$i'>$i</a>";
			}
		}
		if($pagemax>1) {
			if($pagemax!=$page)
				$label.="<a href='$url&page=$pagemax'>$pagemax</a>"; //最后一页
			else
				$label.="<span class='me'>$page</span>";
		}
		if($page==$pagemax) $label.="<span class='disabled'>下一页</span>";
		else $label.="<a href='".$url."&page=".($page+1)."' class='sxy'>下一页</a>";
		return "<div class='page tc oh mt20'>".$label."</div>";
	}

	private function formatpage($page){
		$page=(int)$page;
		if($page-1<0) $page=1;
		return $page;
	}
}
?>