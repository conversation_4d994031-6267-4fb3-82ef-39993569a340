<?php


class sglr<PERSON>ao extends Dao
{
    public function __construct($writer)
    {
        parent::__construct($writer);
    }

    public function getlrinfo($type,$sdate,$edate){
       $sql="select * from sg_DongTaiChengBenIndex where type in($type) and ndate>='".$sdate."' and ndate<='".$edate."' order by ndate desc ";
       return $this->query($sql);
    }

    public function getallinfo($type,$sdate,$edate){
        $sql="select * from sg_DongTaiChengBenIndex where type in($type) and ndate>='".$sdate."' and ndate<='".$edate."' order by ndate desc ";
        echo $sql;
        return $this->query($sql);
    }
}
?>