<?php
/*
 * @Author: x<PERSON><PERSON><PERSON>
 * @Date: 2022-07-16 14:42:07
 * @LastEditTime: 2025-05-09 17:35:06
 * @FilePath: \oa.steelhome.cnk:\www\dc.steelhome.cn\v1.5\app\sghylr\sghylrAction.inc.php
 * @Description: 
 * @Copyright: © 2021, SteelHome. All rights reserved.
 */
// $GLOBALS ['chengbenarr'] = array('11','12','42','160','72','102','132','190','13','43','161','73','103','133','191','14','44','162','74','104','134','192','15','45','163','75','105','135','193');
//15：402023=》A1302030062M91041800、135：532023=》A5101030062M91612000、193：532023=》A5101030062M91612000
//18 唐山H型钢利润
$GLOBALS ['cbjgarr'] = array('10'=>'6784117',
    '12'=>'401103','42'=>'071103','160'=>'071103','72'=>'431103','102'=>'171103','132'=>'531103','190'=>'531103',
    '13'=>'401312','43'=>'071312','161'=>'071312','73'=>'431312','103'=>'171312','133'=>'531312','191'=>'531312',
    '14'=>'403112','44'=>'073112','162'=>'073112','74'=>'433112','104'=>'173112','134'=>'533112','192'=>'533112',
    '15'=>'A1302030062M91041800','45'=>'072023','163'=>'072023','75'=>'432023','105'=>'172023','135'=>'A5101030062M91612000','193'=>'A5101030062M91612000',
    '226'=>'4531125','256'=>'6731126','286'=>'6731126','316'=>'713112','346'=>'Q13112','376'=>'223112','406'=>'2431124','436'=>'253112','466'=>'363112','496'=>'353112','526'=>'283112','556'=>'533112',
    '18'=>'677311','318'=>'D27311'
);
/*$GLOBALS ['cbjgarr'] = array('318'=>'D27311');*/

class sghylrAction extends AbstractAction{

	public function __construct(){
		parent::__construct();    
	}

	public function savehylr($params)
	{
		$mc_type = $params['mc_type'];
		$sdate = $params['sdate'];
		$edate = $params['edate'];
		$cbjgarr = $GLOBALS ['cbjgarr']; 
		$typearr = array();
		$jiageidarr = array();
		foreach ($cbjgarr as $type => $jiageid) {
			$typearr[] = $type;
			$jiageidarr[] = $jiageid;
		}
		$valSql = "";
		while (strtotime($sdate) <= strtotime($edate)) {
			// echo $sdate."<br>";
			$chengbenarr = $this->drcdao->get_HangYeChengBenIndex($typearr, $mc_type, $sdate);
			$pricearr = $this->get_marketprice($jiageidarr, $sdate);
			// echo "<pre>";print_r($pricearr);eixt;
			foreach ($cbjgarr as $type => $jiageid) {
				if (!empty($chengbenarr[$type]) && !empty($pricearr[$jiageid])) {
					$lirun = $pricearr[$jiageid] - $chengbenarr[$type];
					$valSql.="('".$type."','".$lirun."','".$sdate."','".$mc_type."'),";
				}

                if (!empty($chengbenarr[$type]) && !empty($pricearr[$jiageid])) {
                    // echo "update sg_HangYeLiRunIndex set del = '1' where ndate = '".$sdate."'"."<br>";
                    $this->drcdao->execute("update sg_HangYeLiRunIndex set del = '1' where mc_type='".$mc_type."' and type='".$type."' and ndate = '".$sdate."'");
                }
			}
			if (!empty($cbjgarr) && !empty($pricearr)) {
				// echo "update sg_HangYeLiRunIndex set del = '1' where ndate = '".$sdate."'"."<br>";
				//$this->drcdao->execute("update sg_HangYeLiRunIndex set del = '1' where mc_type='".$mc_type."' and type='".$type."' and ndate = '".$sdate."'");
			}
			$sdate = date("Y-m-d",strtotime("+1 day",strtotime($sdate)));
		}
		$basesql = "insert into sg_HangYeLiRunIndex (type,lirun,ndate,mc_type) values";
		$insertsql = substr($basesql.$valSql, 0, -1);
		if(!empty($valSql))
		{
			$this->drcdao->execute($insertsql);
			// echo $insertsql."<br><br>";
		}
		echo 1;
	}

	function get_marketprice($jiageidarr,$date){
		$six=$seven=$newid=array();
		foreach($jiageidarr  as $id ){
			if (strlen ( $id ) == 6){//ÅÐ¶Ïid µÄ×Ö·û³¤¶È 
				if(!in_array($id ,$six)){
					$six[]= $id ; 			
				} 							
			} 
			if (strlen ($id ) == 7) {
				if(!in_array($id,$seven)){
					$seven[]= $id;
				} 
			}
            if (strlen ($id ) > 7) {
                if(!in_array($id,$newid)){
                    $newid[]= $id;
                }
            }
		}
		$sixid_str=implode("','",$six);
		$sevenid_str=implode("','",$seven);
        $newid_str=implode("','",$newid);
		$mconmanagedate="(mconmanagedate>='".$date." 00:00:00' and mconmanagedate<='".$date." 23:59:59')";
		$PriceList1 = array();
		$PriceList2 = array();
		$PriceList_new = array();
		if($sixid_str!=''){
			$PriceListSQL=" select marketconditions.price,marketconditions.oldprice,marketconditions.topicture, marketconditions.mconmanagedate from marketconditions
			where $mconmanagedate and  marketconditions.topicture in ('$sixid_str') order by  marketconditions.mconmanagedate desc limit ".(count($six));
			$PriceList1 = $this->_dao->query( $PriceListSQL );
		 //    echo $PriceListSQL;
		}
		if($sevenid_str!=''){
			$PriceListSQL=" select marketconditions.price,marketconditions.oldprice,marketconditions.mastertopid as topicture, marketconditions.mconmanagedate from marketconditions
			where $mconmanagedate and  marketconditions.mastertopid in ('$sevenid_str') order by  marketconditions.mconmanagedate desc limit ".(count($seven));
			$PriceList2 = $this->_dao->query( $PriceListSQL );
		}
		$PriceList = array_merge($PriceList1,$PriceList2);
		// echo"<pre>";print_r($PriceList);
		$Price1 = array();
		foreach($PriceList  as $v )
		{
			$Price1[$v['topicture']] = $v['price'];
		}
        if($newid_str!=''){
            $PriceListSQL=" select `price_code`,price from `marketconditions_view`
			where $mconmanagedate and price_code in ('$newid_str')  ";
            $PriceList_new = $this->_dao->query( $PriceListSQL );
            foreach($PriceList_new  as $v )
            {
                $Price1[$v['price_code']] = $v['price'];
            }
        }
		return $Price1;
	}

	public function test($params){
	    exit;
        $sql = "select type,ndate,id from sg_HangYeLiRunIndex where ndate>='2014-08-01' and ndate<='2025-07-11' and mc_type=0 order by id ASC";
        $list = $this->drcdao->query($sql);
        $data = array();
        foreach ($list as $tmp){
            $data[$tmp['type']][$tmp['ndate']] = $tmp['id'];
        }

        foreach ($data as $item){
            foreach ($item as $tmp){
                $sql = "update sg_HangYeLiRunIndex set del='0' where id='".$tmp."' and del='1' ";
                //echo $sql."<br>";
                $this->drcdao->execute($sql);
            }
        }
    }

}
?>