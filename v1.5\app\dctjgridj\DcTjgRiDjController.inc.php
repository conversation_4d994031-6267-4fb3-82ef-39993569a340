<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );

class DcTjgRiDjController extends AbstractController{
  
  public function __construct(){
    parent::__construct();
	
	//$this->_action->setDao( new DcTjgRiDjDao( "MAIN" ) );
	//$this->_action->bizdao = new SystemDao('BIZ') ;
	$this->_action->stedao = new DcTjgRiDjDao('MAIN') ;
	$this->_action->ngdao = new DcTjgRiDjDao('DRCW') ;
	//echo "<pre>";print_r($this->_action->ngdao);exit;
	$this->_action->maindao = new DcTjgRiDjDao('91R') ;
	$this->_action->gcdao=new DcTjgRiDjDao('GC');//hlf/钢厂调价的本期价格及涨跌幅,正式库
	//$this->_action->qhdao=new DcTjgRiDjDao('HQT');//	煤焦期货表			
	
  }
  
  public function _dopre(){
      //$this->_action->checkSession();
  }

  public function v_index() {
  	
	$this->_action->index($this->_request);
  }
   public function do_ngxs() {
  	
	$this->_action->ngxs($this->_request);
  }
   public function do_savedate() {
  	
	$this->_action->savedate($this->_request);
  }
}