<?php
class sgqhtb<PERSON><PERSON> extends Dao{
  public function __construct( $writer ){
    parent::__construct( $writer );
  }

function get_style($value){
  if($value == 0){
    $value = '--';
  }elseif( $value > 0 ){
    $value = '<font color="red">↑'.$value.'</font>';
  }elseif( $value < 0 ){
    $value = '<font color="green">↓'.abs($value).'</font>';
  }
  return $value;
}

//获取品种的一段时间的和
public function get_sc_KaiGongLvHuiZong($time1,$time2){		

  $sql = "select Date,KaiGonglv2,num2 from sc_KaiGongLvHuiZong  where `AreaCode`=0  and Date>='$time1' and Date<='$time2'   order by Date desc limit 2";	  

  $sc_KaiGongLvHuiZong=$this->query($sql);

  $shuju['Date']=$sc_KaiGongLvHuiZong[0]['Date'];
  $shuju['KaiGonglv']=$sc_KaiGongLvHuiZong[0]['KaiGonglv2'];
  $shuju['KaiGonglvzd1']=$sc_KaiGongLvHuiZong[0]['KaiGonglv2']-$sc_KaiGongLvHuiZong[1]['KaiGonglv2'];
  $shuju['KaiGonglvzd']=$this->get_style($sc_KaiGongLvHuiZong[0]['KaiGonglv2']-$sc_KaiGongLvHuiZong[1]['KaiGonglv2']);
  $value = $sc_KaiGongLvHuiZong[1]['KaiGonglv2'] != 0 ? Round($shuju['KaiGonglvzd1']/abs($sc_KaiGongLvHuiZong[1]['KaiGonglv2'])*100, 2) : "0";
  $shuju['KaiGonglvzdf']=$this->get_style($value);
  return $shuju;
}
public function get_data_table($time1,$time2,$where=" 1 "){		

  $sql = "select dta_2,dta_ym from steelhome_drc.data_table  WHERE  $where and dta_ym>='$time1' and dta_ym<='$time2'  order by dta_ym desc limit 2";	  

  $data_table=$this->query($sql);

  $shuju['Date']=$data_table[0]['dta_ym'];
  $shuju['KaiGonglv']=$data_table[0]['dta_2'];
  $shuju['KaiGonglvzd1']=$data_table[0]['dta_2']-$data_table[1]['dta_2'];
  $shuju['KaiGonglvzd']=$this->get_style($data_table[0]['dta_2']-$data_table[1]['dta_2']);
  $value = $data_table[1]['dta_2'] != 0 ? Round($shuju['KaiGonglvzd1']/abs($data_table[1]['dta_2'])*100,2) :"0";
  $shuju['KaiGonglvzdf']=$this->get_style( $value );
  return $shuju;
}

public function get_kucun_hz($time1,$time2){		

  $sql = "select value,date from steelhome_gc.gcsckucun_hz WHERE type = '5' and date>='$time1' and date<='$time2'  order by date desc limit 2";	  

  $data_table=$this->query($sql);
  // print_r($data_table);
  $shuju['Date']=$data_table[0]['date'];
  $shuju['value']=$data_table[0]['value'];
  $shuju['valuezd1']=$data_table[0]['value']-$data_table[1]['value'];
  $shuju['valuezd']=$this->get_style($data_table[0]['value']-$data_table[1]['value']);
  $value = $data_table[1]['value']!= 0 ? Round($shuju['valuezd1']/abs($data_table[1]['value'])*100,2):0;
  $shuju['valuezdf']=$this->get_style( $value );
  return $shuju;
}

public function get_JiChaInfo($time1,$time2,$where){		

  $sql = "select value,Date from JiChaInfo WHERE $where and Date>='$time1' and Date<='$time2' order by Date desc limit 2";	  
  $data_table=$this->query($sql);
  $shuju['Date']=$data_table[0]['Date'];
  $shuju['value']=$data_table[0]['value'];
  $shuju['valuezd1']=$data_table[0]['value']-$data_table[1]['value'];
  $shuju['valuezd']=$this->get_style($data_table[0]['value']-$data_table[1]['value']);
  $value = $data_table[1]['value']!= 0 ? Round($shuju['valuezd1']/abs($data_table[1]['value'])*100,2):0;
  $shuju['valuezdf']=$this->get_style( $value );
  return $shuju;
}

public function get_marketconditions($time1,$time2,$where){		
  $sql = "select price,mconmanagedate from marketconditions WHERE $where and mconmanagedate>='$time1 00:00:00' and mconmanagedate<='$time2 23:59:59' order by mconmanagedate desc limit 2";	  
  $data_table=$this->query($sql);
  $shuju['Date']=date("Y-m-d",strtotime($data_table[0]['mconmanagedate']));
  $shuju['value']=$data_table[0]['price'];
  $shuju['valuezd1']=$data_table[0]['price']-$data_table[1]['price'];
  $shuju['valuezd']=$this->get_style($data_table[0]['price']-$data_table[1]['price']);
  $value = $data_table[1]['price']!=0 ? Round($shuju['valuezd1']/abs($data_table[1]['price'])*100,2):0;
  $shuju['valuezdf']=$this->get_style( $value );
  return $shuju;
}

public function get_data_table2($time1,$time2,$table,$date,$dta,$where){		
  $sql = "select ".$dta.",".$date." from ".$table."  WHERE  $where and ".$date.">='$time1' and ".$date."<='$time2'   order by ".$date." desc limit 2";	  
  // echo $sql;
  $data_table=$this->query($sql);
  $shuju['Date']=$data_table[0][$date];
  $shuju['value']=round(floatval( $data_table[0][$dta] ),2);
  $shuju['sq_value']=round( floatval( $data_table[1][$dta] ),2);
  $shuju['valuezd1']=round( floatval( $data_table[0][$dta] ),2)-round( floatval( $data_table[1][$dta] ),2);
  $shuju['valuezd']=$this->get_style(round( floatval( $data_table[0][$dta] ),2)-round( floatval( $data_table[1][$dta] ),2));
  $value = abs(round( floatval( $data_table[1][$dta] ),2))!=0 ? Round(floatval( $shuju['valuezd1'] )/abs(round( floatval( $data_table[1][$dta] ),2))*100,2):0;
  $shuju['valuezdf']=$this->get_style( $value );
  return $shuju;
}

public function get_data_table3($time1,$time2){		
  $sql = "SELECT dbinfo.date AS DATE,dt.* FROM steelhome_drc.data_table as dt left join steelhome_drc.DataTableBaseInfo as dbinfo on dt.baseid=dbinfo.id where dbinfo.`DataType`='Java-GMCL' and dbinfo.date>='$time1' and dbinfo.date<='$time2'   order by dbinfo.date desc limit 2";	  
  // echo $sql;
  $data_table=$this->query($sql);
  $shuju['Date']=$data_table[0]['DATE'];
  $shuju['value']=$data_table[0]['dta_1'];
  $shuju['sq_value']=$data_table[1]['dta_1'];
  $shuju['valuezd1']=$data_table[0]['dta_1']-$data_table[1]['dta_1'];
  $shuju['valuezd']=$this->get_style($data_table[0]['dta_1']-$data_table[1]['dta_1']);
  $value = $data_table[1]['dta_1']!= 0 ?  Round($shuju['valuezd1']/abs($data_table[1]['dta_1'])*100,2):0;
  $shuju['valuezdf']=$this->get_style( $value );
  return $shuju;
}

public function get_canNengLYL($stime, $etime, $dta_vartype)
{

  $baseidSql = "select ID from sc_operating_base WHERE curStartDate='$stime' and curEndDate='$etime' and dta_vartype='$dta_vartype' and isComplete=1";
  $baseid = $this->getOne($baseidSql);
  if ($baseid) {
      $sql = "SELECT sc_steelcom.name,sc_operating.dta_steelid,sc_operating.dta_vartype, sc_operating.dta_1,sc_operating.dta_2,sc_operating.dta_3,sc_operating.dta_4,sc_operating.dta_5,sc_operating.dta_6,sc_operating.dta_7,sc_operating.dta_8, sc_steelcom.area,sc_steelcom.operating,sc_steelcom.province FROM sc_operating, sc_steelcom where sc_operating.dta_steelid=sc_steelcom.id and sc_operating.baseid='$baseid' and sc_operating.dta_vartype = '$dta_vartype' order by sc_operating.dta_steelid, sc_operating.acttime desc";
//           echo $sql."</br></br></br>";
      return $this->query($sql);
  }
}

public function get_operating($ndate,$where=""){
		
  $sql="select 
     sc_other_price.coke_stock,sc_other_price.coking_coal_stock,sc_other_price.coking_coal_stock_weight,sc_other_price.kaigonglv,sc_other_price.channeng,sc_othercom.name,sc_othercom.area,sc_othercom.city,sc_othercom.province,sc_othercom.st_type,sc_othercom.jtkc_sampling,sc_othercom.ljmkc_sampling from sc_other_price left join sc_othercom on sc_othercom.id=sc_other_price.st_id and sc_othercom.comType = '1' where type='3'  and ndate='".$ndate."' and sc_other_price.channeng!='' $where ";
     // echo $sql."<br />";
 
     return $this->query( $sql );
 }

public function get_area_hz($dta_vartype,$datadate){
  $base_info = $this->query("select ID,curStartDate,curEndDate,prevStartDate,prevEndDate from sc_operating_base where dta_vartype='".$dta_vartype."' and isComplete=1 and curEndDate <= '$datadate' order by curEndDate desc limit 2");
  $luowen_info = $this->query("select CityId,dta_8,baseid from sc_operating_area_hz where CityId in('-1','中南地区','西北地区','西南地区') and baseid in ('".$base_info[0]['ID']."','".$base_info[1]['ID']."')" );
  $luowen = array();
  $area =array();
  foreach($luowen_info as $k=>$v){
    $luowen[$v['baseid']][$v['CityId']] = $v;
  }
  $area['bq']['date'] = $base_info[0]['curEndDate'];
  $area['bq']['shuju'] = $luowen[$base_info[0]['ID']];
  $area['sq']['shuju'] = $luowen[$base_info[1]['ID']];
  return $area;
}

public function get_L2Data($time1,$time2,$where=" 1 "){		

  $sql = "select D1,DATE from L2Data  WHERE  $where and DATE>='$time1' and DATE<='$time2'   order by DATE desc limit 2";	  

  $L2Data=$this->query($sql);

  $shuju['Date']=$L2Data[0]['DATE'];
  $shuju['cl']=$L2Data[0]['D1'];
  $shuju['clzd1']=$L2Data[0]['D1']-$L2Data[1]['D1'];
  $shuju['clzd']=$this->get_style($L2Data[0]['D1']-$L2Data[1]['D1']);
  $value = $L2Data[1]['D1'] != 0 ? Round($shuju['clzd1']/abs($L2Data[1]['D1'])*100,2) :"0";
  $shuju['clzdf']=$this->get_style( $value );
  return $shuju;
}
public function get_sc_othercom_stock_hz($time1,$time2,$where=" 1 "){		

  $sql = "select jt_kc,Date from sc_othercom_stock_hz  WHERE  $where and Date>='$time1' and Date<='$time2'   order by Date desc limit 2";	  

  $sc_othercom_stock_hz=$this->query($sql);

  $shuju['Date']=$sc_othercom_stock_hz[0]['Date'];
  $shuju['kc']=$sc_othercom_stock_hz[0]['jt_kc'];
  $shuju['sq_kc']=$sc_othercom_stock_hz[1]['jt_kc'];
  $shuju['kczd1']=$sc_othercom_stock_hz[0]['jt_kc']-$sc_othercom_stock_hz[1]['jt_kc'];
  $shuju['kczd']=$this->get_style($sc_othercom_stock_hz[0]['jt_kc']-$sc_othercom_stock_hz[1]['jt_kc']);
  $value = $sc_othercom_stock_hz[1]['jt_kc'] != 0 ? Round($shuju['kczd1']/abs($sc_othercom_stock_hz[1]['jt_kc'])*100,2) : 0 ;
  $shuju['kczdf']=$this->get_style( $value );
  return $shuju;
}

public function get_sc_kc($citynames,$varietynames,$sdate,$edate){
  $sql = "SELECT si.company_name,sb.cityname,si.varietyname , si.the_stock,sb.post_date FROM `stockmarket_base` sb , `stockmarket_info` si  WHERE sb.id=si.sb_id and sb.post_date >= '$sdate 00:00:00' and sb.post_date <= '$edate 23:59:59' and  sb.cityname in ($citynames) and si.varietyname in ($varietynames) and si.varietyname!='小计' order by sb.post_date desc";
  // echo $sql."<br/>";
  return $this->query($sql);
}

public function get_MeiJiaoNeedAndStockHuiZong($time1,$time2,$where=" 1 "){		

  $sql = "select CurrStock,HuiZongDate from MeiJiaoNeedAndStockHuiZong  WHERE  $where and HuiZongDate>='$time1' and HuiZongDate<='$time2'   order by HuiZongDate desc limit 2";	  

  $MeiJiaoNeedAndStockHuiZong=$this->query($sql);

  $shuju['Date']=$MeiJiaoNeedAndStockHuiZong[0]['HuiZongDate'];
  $shuju['kc']=$MeiJiaoNeedAndStockHuiZong[0]['CurrStock'];
  $shuju['sq_kc']=$MeiJiaoNeedAndStockHuiZong[1]['CurrStock'];
  $shuju['kczd1']=$MeiJiaoNeedAndStockHuiZong[0]['CurrStock']-$MeiJiaoNeedAndStockHuiZong[1]['CurrStock'];
  $shuju['kczd']=$this->get_style($MeiJiaoNeedAndStockHuiZong[0]['CurrStock']-$MeiJiaoNeedAndStockHuiZong[1]['CurrStock']);
  $value = $MeiJiaoNeedAndStockHuiZong[1]['CurrStock'] !=0 ? Round($shuju['kczd1']/abs($MeiJiaoNeedAndStockHuiZong[1]['CurrStock'])*100,2):0;
  $shuju['kczdf']=$this->get_style($value);
  return $shuju;
}
public function get_gkkc($time1,$time2,$where=" 1 "){		

  $sql = "select dta_2,date from steelhome_drc.DataTableBaseInfo as db left join steelhome_drc.data_table as dt on db.id=dt.baseid  WHERE  $where and date>='$time1' and date<='$time2'   order by date desc limit 2";	  

  $MeiJiaoNeedAndStockHuiZong=$this->query($sql);

  $shuju['Date']=$MeiJiaoNeedAndStockHuiZong[0]['date'];
  $shuju['kc']=$MeiJiaoNeedAndStockHuiZong[0]['dta_2'];
  $shuju['sq_kc']=$MeiJiaoNeedAndStockHuiZong[1]['dta_2'];
  $shuju['kczd1']=$MeiJiaoNeedAndStockHuiZong[0]['dta_2']-$MeiJiaoNeedAndStockHuiZong[1]['dta_2'];
  $shuju['kczd']=$this->get_style($MeiJiaoNeedAndStockHuiZong[0]['dta_2']-$MeiJiaoNeedAndStockHuiZong[1]['dta_2']);
  $value = $MeiJiaoNeedAndStockHuiZong[1]['dta_2'] != 0 ? Round($shuju['kczd1']/abs($MeiJiaoNeedAndStockHuiZong[1]['dta_2'])*100,2) : 0;
  $shuju['kczdf']=$this->get_style( $value );
  return $shuju;
}

public function  get_JiaoQiLiLunDunLiRun($time1,$time2,$where=" 1 "){		

  $sql = "SELECT cast(avg(China) as  decimal(10,2)) as weekavg,date FROM  steelhome_drc.`JiaoQiLiLunDunLiRun` WHERE  $where and date>='$time1' and date<='$time2'   group by date_format(date,'%Y%u')   order by date desc limit 2";	  

  $JiaoQiLiLunDunLiRun=$this->query($sql);

  $shuju['Date']=$JiaoQiLiLunDunLiRun[0]['date'];
  $shuju['lr']=$JiaoQiLiLunDunLiRun[0]['weekavg'];
  $shuju['lrzd1']=$JiaoQiLiLunDunLiRun[0]['weekavg']-$JiaoQiLiLunDunLiRun[1]['weekavg'];
  $shuju['lrzd']=$this->get_style($JiaoQiLiLunDunLiRun[0]['weekavg']-$JiaoQiLiLunDunLiRun[1]['weekavg']);
  $value = $JiaoQiLiLunDunLiRun[1]['weekavg'] != 0 ? Round($shuju['lrzd1']/abs($JiaoQiLiLunDunLiRun[1]['weekavg'])*100,2) : 0;
  $shuju['lrzdf']=$this->get_style( $value );
  return $shuju;
}

public function  get_sc_operating_area_hz($time1,$time2,$where=" 1 "){		

  $sql = "SELECT cast((dta_7/dta_6*100) as  decimal(10,2)) as weekavg,acttime FROM  `sc_operating_area_hz` WHERE  $where and acttime>='$time1' and acttime<='$time2'   order by acttime desc limit 2";	  

  $JiaoQiLiLunDunLiRun=$this->query($sql);

  $shuju['Date']=date('Y-m-d',strtotime($JiaoQiLiLunDunLiRun[0]['acttime']));
  $shuju['cnlyl']=$JiaoQiLiLunDunLiRun[0]['weekavg'];
  $shuju['cnlylzd1']=$JiaoQiLiLunDunLiRun[0]['weekavg']-$JiaoQiLiLunDunLiRun[1]['weekavg'];
  $shuju['cnlylzd']=$this->get_style($JiaoQiLiLunDunLiRun[0]['weekavg']-$JiaoQiLiLunDunLiRun[1]['weekavg']);
  $value = $JiaoQiLiLunDunLiRun[1]['weekavg'] != 0 ? Round($shuju['cnlylzd1']/abs($JiaoQiLiLunDunLiRun[1]['weekavg'])*100,2):0;
  $shuju['cnlylzdf']=$this->get_style( $value );
  return $shuju;
}

public function  get_sckccity($time1,$time2,$where=" 1 "){		

  $sql = "select the_stock,last_stock,changevalue,post_date from stockmarket_base as sb,stockmarket_info as si  WHERE   $where and  sb.id=si.sb_id and post_date>='$time1' and post_date<='$time2'   order by post_date desc limit 2";	  

  $stockmarket_base=$this->query($sql);

  $shuju['Date']=date('Y-m-d',strtotime($stockmarket_base[0]['post_date']));
  $shuju['kc']=$stockmarket_base[0]['the_stock'];
  $shuju['kczd1']=$stockmarket_base[0]['changevalue'];
  $shuju['kczd']=$this->get_style($stockmarket_base[0]['changevalue']);
  $value = $stockmarket_base[1]['last_stock'] != 0 ? Round($shuju['kczd1']/abs($stockmarket_base[1]['last_stock'])*100,2) : 0;
  $shuju['kczdf']=$this->get_style( $value );
  return $shuju;
}

public function  get_sckcarea($time1,$time2,$where=" 1 "){		

  $sql = "select value,time from steelhome_drc.kucun_hz  WHERE   $where and time>='$time1' and time<='$time2'   order by time desc limit 2";	  

  $kucun_hz=$this->query($sql);

  $shuju['Date']=date('Y-m-d',strtotime($kucun_hz[0]['time']));
  $shuju['kc']=$kucun_hz[0]['value'];
  $shuju['sq_kc']=$kucun_hz[1]['value'];
  $shuju['kczd1']=$kucun_hz[0]['value']-$kucun_hz[1]['value'];
  $shuju['kczd']=$this->get_style($kucun_hz[0]['value']-$kucun_hz[1]['value']);
  $value = $kucun_hz[1]['value'] != 0 ? Round($shuju['kczd1']/abs($kucun_hz[1]['value'])*100,2): 0;
  $shuju['kczdf']=$this->get_style( $value );
  return $shuju;
}

public function  get_gckcarea($time1,$time2,$where=" 1 "){		

  $sql = "select Value,ZhangDie, Date from KuCunHZ  WHERE   $where and Date>='$time1' and Date<='$time2'   order by Date desc limit 2";	  

  $KuCunHZ=$this->query($sql);

  $shuju['Date']=date('Y-m-d',strtotime($KuCunHZ[0]['Date']));
  $shuju['kc']=$KuCunHZ[0]['Value'];
  $shuju['kczd1']=$KuCunHZ[0]['Value']-$KuCunHZ[1]['Value'];
  $shuju['kczd']=$this->get_style($KuCunHZ[0]['Value']-$KuCunHZ[1]['Value']);
  $value = $KuCunHZ[1]['Value']!=0 ? Round($shuju['kczd1']/abs($KuCunHZ[1]['Value'])*100,2): 0 ;
  $shuju['kczdf']=$this->get_style( $value );
  return $shuju;
}

public function get_steelbyarea2($area,$kc_type){
  $sql = "SELECT a.id as  steelid , a.name  as steelname,a.kc_type,a.province FROM   `sc_steelcom` as a,sc_steel_admin as b  where a.id=b.st_id ".$sql_where." and a.area='$area' and (a.kc_type like '$kc_type,%' or a.kc_type like '%,$kc_type,%' or a.kc_type like '%,$kc_type' or a.kc_type='$kc_type' ) group by binary steelname ORDER BY a.province asc, a.st_type ASC, a.id asc ";
  // echo $sql ;exit;
  return $this->query($sql);
  
}

public function get_steelids($kc_type){
  $sql = "SELECT `id` as  steelid  FROM   `sc_steelcom`  where (kc_type like '$kc_type,%' or kc_type like '%,$kc_type,%' or kc_type like '%,$kc_type' or kc_type='$kc_type' )";
  
  $id1s = $this->query($sql);
  
  $ids = array();
  foreach($id1s as $id1)
  {
    $ids[] = $id1['steelid'];
  }
  
  return $ids;
}

public function get_prevStocks($ids, $sdate,$edate, $bigType)
{
  $nCount = count($ids);
  $arrs = array();
  
  if($nCount > 0)
  {
    if($sdate!="" && $edate!=""){
      $sdate = $sdate;
      $edate = $edate;
    }else{
      $time = strtotime($sdate);
      $time2 = strtotime($sdate) - 3600*24*15;
      $sdate = date("Y-m-d",$time2);
      $edate = date("Y-m-d",$time);
    }
    $sdate=$sdate."00:00:00";
    $edate=$edate."23:59:59";
    $idsStr = implode(",", $ids);
    $sql = "select * from sc_steelstock2 where BigType = $bigType and 
    sc_id in($idsStr)
    and actime < '$edate' and actime >= '$sdate'
    order by actime desc";
    //echo "sql = $sql<br/>";exit;
    $arrs = $this->query($sql);
    
  }
  return $arrs;
}

public function get_currentInfos($ids, $sdate, $edate, $smallType, $adminid=null)
{
  $nCount = count($ids);
  $arrs = array();
  
  if($nCount > 0)
  {
    $idsStr = implode(",", $ids);
    $sql = "select sc_id,  BigType, SmallType, TotalLine, UsingLine,Stock,actime from sc_steelstock2 where SmallType = $smallType and sc_id in($idsStr)
    and actime <= '$edate 23:59:59' and actime >= '$sdate 00:00:00'";
  
    if($adminid != null)
    {
      $sql .= " and adminid=$adminid";
    }
    $sql .= " order by actime desc";
      //echo $sql;exit;
    $arrs = $this->query($sql);
    //echo "smallType sql = $sql<br/>";
  }

  return $arrs;
}


}
?>