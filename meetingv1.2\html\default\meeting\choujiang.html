<!DOCTYPE html>
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta charset="utf-8">
<title><{$jxtitle}></title>
<link rel="stylesheet" href="css/choujiang/main.css?t=0913"/>
<link rel="stylesheet" href="css/choujiang/animate.css?t=0913"/>
<link rel="stylesheet" href="css/choujiang/index2.css?t=202221122"/>
<link rel="stylesheet" href="js/choujiang/index.css" media="screen" type="text/css">
<script type="text/javascript" src="js/choujiang/base64.js"></script>
<style>
	/* #awardbg img {
		animation-name: zoomIn;
		animation-duration: 2s;
		animation-iteration-count: 1;
		animation-fill-mode: forwards;
	} */
	#jxname{    letter-spacing: 0px;}
</style>
</head>
<body>
	<{if $mtid==429 || $mtid==434}>
	<style>
		body {
			background-image:url(images/choujiang/<{$mtid}>/bodybg.jpg);
		}
	</style>
	<{/if}>

	<div class="container none"></div>
	
	<div class="pc-box" style="display: block;">
		<!--开始按钮-->
		<div class="start-btn"></div>  
		<!--停止按钮-->
		<div class="lottery-stop-btn" style="display: none;"></div>
		<!--开始前页面-->
		<div id="main">
			<div id="result" style=" margin-top: 0px;">
				
				<!-- <h2 class="top_title" id="top_title_xyj"><{$jxtitle}></h2> -->
				<!-- <img src="images/choujiang/name.png" style="width: 400px;"> -->
				<div class="huojiang" id="md">
					<!-- <div id="zjnum1"  class="md">奖品数量：<{$cjnum}></div> -->
					<div id="awardbg">
						<img src="images/choujiang/434/jp<{$jx}>.png" id="jxbgimage"  class="jxbgimage">
						<div class="jpxx" id="jpxx">
							<span class="jpname" id="jpname"><{$jxtitle}>&nbsp;<{$jxnames}></span>
						</div>
					</div>			
				</div>
			</div>
		</div>

		<!--滚动区域-->
		<div class="signthreed-wall-block" >
			<div class="Sparkling">
				<img src="images/choujiang/434/bodybg3.jpg">
			</div>
			<div class="wall3d hidden">
				<ul></ul>
			</div>
			<div class="lottery-userBox zam-app-flex-ajCenter2 hidden">
				<div class="lottery-userBox-wrapper zam-app-flex-alignCenter zam-app-justifyspacearound zam-app-flex-wrap">
					<div class="box_angle_top box_angle"></div>
					<div class="box_angle_bottom box_angle"></div>
					
				</div>
			</div>
		</div>
		<div class="lottery-luckers zam-app-flex-ajCenter hidden">
			<div class="kong"></div>
			<{if $mtid==410}>
			<h2 class="top_title_meeting2">2024年广西·第十五届钢铁产业链发展形势会议</h2>
			<h2 class="top_title_meeting2">本次礼品由广西池城贸易有限公司赞助</h2>
			<{/if}>
			<a onclick="showAll()"><div class="lottery-poptitle">获奖名单</div></a>
			<div class="lottery-popBox-prize"><span><{$jxtitle}></span><span id="jxname">-<{$jxnames}></span></div>
			<div class="lottery-luckers-wrapper zam-app-flex-ajCenter2"></div>
		</div>
		
		<script type="text/html" id="outlucker-tpl">
			<div class="lottery-luckers-item0 zam-app-flex-ajCenter">
				<div class="lottery-luckers-img0 frotate" onclick="qxzj(this,'{{# uid }}','{{# TrueName }}')" >
					<img src="{{ headimgurl }}">
				</div>
				<div class="lottery-luckers-name0 zam-app-flex-ajCenter">
					{{# comname }}
					{{# post }}
					{{# nickname }}
					
				</div>
			</div>
		</script>

		<script type="text/html" id="outlucker-tpl1">
			<div class="lottery-luckers-item1 zam-app-flex-ajCenter">
				<div class="lottery-luckers-img1 frotate" onclick="qxzj(this,'{{# uid }}','{{# TrueName }}')" >
					<img src="{{ headimgurl }}">
				</div>
				<div class="lottery-luckers-name1 zam-app-flex-ajCenter">
					{{# comname }}
					{{# post }}
					{{# nickname }}
					
				</div>
			</div>
		</script>

		<script type="text/html" id="outlucker-tpl2">
			<div class="lottery-luckers-item2 zam-app-flex-ajCenter">
				<div class="lottery-luckers-img2 frotate" onclick="qxzj(this,'{{# uid }}','{{# TrueName }}')">
					<img src="{{ headimgurl }}">
				</div>
				<div class="lottery-luckers-name2 zam-app-flex-ajCenter">
					{{# comname }}
					{{# post }}
					{{# nickname }}
					
				</div>
		
			</div>
		</script>
		<script type="text/html" id="outlucker-tpl3">
			<div class="lottery-luckers-item3 zam-app-flex-ajCenter">
				<div class="lottery-luckers-img3 frotate" onclick="qxzj(this,'{{# uid }}','{{# TrueName }}')">
					<img src="{{ headimgurl }}">
				</div>
				<div class="lottery-luckers-name3 zam-app-flex-ajCenter">
					{{# comname }}
					{{# post }}
					{{# nickname }}
					
				</div>
		
			</div>
		</script>
		

		<script type="text/html" id="outlucker-tpl4">
			<div class="lottery-luckers-item4 zam-app-flex-ajCenter">
				<div class="lottery-luckers-img4 frotate" onclick="qxzj(this,'{{# uid }}','{{# TrueName }}')">
					<img src="{{ headimgurl }}">
				</div>
				<div class="lottery-luckers-name4 zam-app-flex-ajCenter">
					{{# comname }}
					{{# post }}
					{{# nickname }}
					
				</div>
		
			</div>
		</script>


		<script type="text/html" id="outlucker-tpl5">
			<div class="lottery-luckers-item5 zam-app-flex-ajCenter">
				<div class="lottery-luckers-img5 frotate" onclick="qxzj(this,'{{# uid }}','{{# TrueName }}')">
					<img src="{{ headimgurl }}">
				</div>
				<div class="lottery-luckers-name5 zam-app-flex-ajCenter">
					{{# comname }}
					{{# post }}
					{{# nickname }}
					
				</div>
		
			</div>
		</script>

		<script type="text/html" id="outlucker-tpl6">
			<div class="lottery-luckers-item6 zam-app-flex-ajCenter">
				<div class="lottery-luckers-img6 frotate" onclick="qxzj(this,'{{# uid }}','{{# TrueName }}')">
					<img src="{{ headimgurl }}">
				</div>
				<div class="lottery-luckers-name6 zam-app-flex-ajCenter">
					{{# comname }}
					{{# post }}
					{{# nickname }}
					
				</div>
		
			</div>
		</script>


		<script type="text/html" id="outlucker-tpl7">
			<div class="lottery-luckers-item7 zam-app-flex-ajCenter">
				<div class="lottery-luckers-img7 frotate" onclick="qxzj(this,'{{# uid }}','{{# TrueName }}')">
					<img src="{{ headimgurl }}">
				</div>
				<div class="lottery-luckers-name7 zam-app-flex-ajCenter">
					{{# comname }}
					{{# post }}
					{{# nickname }}
					
				</div>
		
			</div>
		</script>

		<script type="text/html" id="outlucker-tpl9">
			<div class="lottery-luckers-item9 zam-app-flex-ajCenter">
				<div class="lottery-luckers-img9 frotate" onclick="qxzj(this,'{{# uid }}','{{# TrueName }}')">
					<img src="{{ headimgurl }}">
				</div>
				<div class="lottery-luckers-name9 zam-app-flex-ajCenter">
					{{# comname }}
					{{# post }}
					{{# nickname }}
					
				</div>
		
			</div>
		</script>
		<script type="text/html" id="outlucker-tpl100">
			<div class="lottery-luckers-item100 zam-app-flex-ajCenter">
				<div class="lottery-luckers-img100 frotate" onclick="qxzj(this,'{{# uid }}','{{# TrueName }}')">
					<img src="{{ headimgurl }}">
				</div>
				<div class="lottery-luckers-name100 zam-app-flex-ajCenter">
					{{# nickname }}
				</div>
		
			</div>
		</script>
	</div>



<script type="text/javascript" src="js/choujiang/jquery.2.1.4.js"></script>
<script type="text/javascript" src="js/choujiang/keyboard.min.js?v=20210608"></script>
<script type="text/javascript" src="js/choujiang/template.js?v=20210608"></script>
<script type="text/javascript" src="js/choujiang/snabbt.js?v=20210608"></script>
<script type="text/javascript" src="js/choujiang/lotteryani.js?v=20210608"></script>
<script>
	var personArr=[];
	let jsonString='<{$data}>';
	let jsonObj = JSON.parse(jsonString);
	for(var i=0;i<jsonObj.length;i++){
		if(jsonObj[i].headimgurl=="" ){
			jsonObj[i].headimgurl="default_head.png";
		}else{
			personArr.push(jsonObj[i]);
		}
	}
    var datajson1=jsonObj;
	var mtid="<{$mtid}>";
	var jx="<{$jx}>";
	var turnumber="<{$turnumber}>";
	var cjnum="<{$cjnum}>";
	// var jxnames=`<{$jxnames}>`;
	// var jxnames=JSON.parse(jsonStr);
	// console.log(jxnames);
	console.log(datajson1);
	
</script>


<script type="text/javascript" src='js/choujiang/transform.js'></script>
<script type="text/javascript" src='js/choujiang/tick.js'></script>
<script type="text/javascript" src='js/choujiang/3d.js'></script>
<script type="text/javascript" src='js/choujiang/lucky.js'></script>

<script type="text/javascript">
	(function($) {
		$.fn.extend({
			animateControl: function (anicss,callback) {
				var self = this;
				self.addClass('animated ' + anicss).one('animationend webkitAnimationEnd', function() {
					self.removeClass('animated ' + anicss);
					if (typeof callback === 'function') {
						callback($(self));
					}
				});
				//return self;
			}
		});
	})(jQuery);

	

	window.onload=function(){
		var num=0;
		var num1=0;
		var list = [];
		var outluckerRender = template('outlucker-tpl');
		var html = '';
		if(localStorage[jx+'temp'+mtid]!=null&&localStorage[jx+'temp'+mtid]!='[]'){
			$("#result").hide();
			var storedNames=JSON.parse(localStorage[jx+'temp'+mtid]);
			var djc=localStorage[jx+'djc'+mtid];
			var prize=turnumber;
			var m = prize.split(",");
			var sum=0;
			var allsum=0;
			for(var j=0;j<m.length;j++){
				if(j<djc){
					sum+=Number(m[j]);
				}
				allsum+=Number(m[j]);
			}
			if(storedNames.length>sum){
				djc=Number(djc)+1;
			}
			var cjgs=m[djc-1];
			var pagenum=storedNames.length-sum;
			if(pagenum==0){
				pagenum=cjgs;
			}else if(pagenum<0){
				djc=Number(djc)-1;
				var sum=0;
				for(var j=0;j<m.length;j++){
					if(j<djc){
						sum+=Number(m[j]);
					}
				}
				pagenum=storedNames.length-sum;
			}
			num=storedNames.length;
			for(var i = 0; i < storedNames.length; i++){
				if(storedNames[i]!=""&&storedNames[i]!=null){
					num1++;
					if(i-pagenum<0){
						list.push(storedNames[i]);
					}	
				}
			}

			if(num1>=cjnum && num1>=allsum){
				list=storedNames;
				$(".lottery-poptitle").html("全部获奖名单");
				$("#jxname").css("display","none");
				$(".Sparkling").css('display', 'none');
			}
			var arr=new Array();
			for(var i = 0; i < list.length; i++){
				arr[list.length-i-1]=list[i];
			}
			if(arr.length==1){
				var outluckerRender = template('outlucker-tpl1');
			}else if(arr.length==2){
				var outluckerRender = template('outlucker-tpl1');
			}else if(arr.length==3){
				var outluckerRender = template('outlucker-tpl3');
			}else if(arr.length==4){
				var outluckerRender = template('outlucker-tpl4');
			}else if(arr.length==5){
				var outluckerRender = template('outlucker-tpl5');
			}else if(arr.length==6){
				var outluckerRender = template('outlucker-tpl6');
			}else if(arr.length==7 || arr.length==8){
				var outluckerRender = template('outlucker-tpl7');
			}else if(arr.length==9 || arr.length==10){
				var outluckerRender = template('outlucker-tpl9');
			}else if(arr.length==11 || arr.length==12){
				var outluckerRender = template('outlucker-tpl6');
			}else if(arr.length>=40){
				var outluckerRender = template('outlucker-tpl100');
			}else{
				var outluckerRender = template('outlucker-tpl');
			}
			$.each(arr,function(i,v){
				var nickname = '';
				nickname += '<div>'+v.TrueName+'</div>';
				v.nickname = nickname;
				var post = '';
				post += '<div>'+v.Post+'</div>';
				v.post = post;
				var comname = '';
				comname += '<div>'+v.ComName+'</div>';
				v.comname = comname;
				html += outluckerRender(v);
				
			});
			$(".lottery-luckers-wrapper").html(html);
			$(".lottery-stop-btn").hide();
			$(".start-btn").show();
			$(".lottery-luckers").removeClass('hidden').addClass('lottery-popBox-ani');
			$(".Sparkling").css('display', 'block');
		}else{
			localStorage[jx+'djc'+mtid]='0';
		}

		// if(localStorage[jx+'djc'+mtid]==0){
		// 	var refreshInterval = setTimeout(function() {
		// 		if(localStorage[jx+'djc'+mtid]==0){
		// 			location.reload();
		// 		}else{
		// 			clearTimeout(refreshInterval);
		// 		}
		// 	}, 3 * 60 * 1000);
		// }
		 
		//setjxname();
	}


	// function setjxname(){
	//     var djc=localStorage[jx+'djc'+mtid];
	// 	if(djc>0){
	// 		if(jxnames.length==1){
	// 			$("#jxname").html("-"+jxnames[0]);
	// 		}else{
	// 			$("#jxname").html("-"+jxnames[djc-1]);
	// 		}
	// 	}else{
	// 		if(jxnames.length==1){
	// 			$("#jxname").html("-"+jxnames[0]);
	// 		}else{
	// 			$("#jxname").html('');
	// 		}
	// 	}
	// }


	//开始抽奖
	var isClick = true;
	$(".start-btn").on('click',function(){
		//间隔一秒可点击开始
		if(isClick) {
            isClick = false;
			//定时器
			setTimeout(function() {
                isClick = true;
            }, 1000);//一秒内不能重复点击

			//轮次设置
			var prize=turnumber;
			var datajson=getdata();
			var djc=localStorage[jx+'djc'+mtid];
			var m = prize.split(",");
			var sum=0;
			for(var j=0;j<m.length;j++){
				if(j<=djc){
					sum+=Number(m[j]);
				}
			}

			if(localStorage[jx+'temp'+mtid]!=null){
				var storedNames=JSON.parse(localStorage[jx+'temp'+mtid]);
				var cjgs=m[djc];
				var cjgs1=m[djc];
				if(storedNames.length<sum){
					cjgs=sum-storedNames.length;
				}
			}else{
				var cjgs=m[djc];
				var cjgs1=m[djc];
			}

			var selnum = cjgs;//当前轮次抽奖人数
			if(datajson.length<selnum){
				return alert('扫码入场人数过少，无法进行抽奖');
			}


			if(!selnum||selnum==''){
				//奖项已抽完 显示所有中奖人
				if(m.length>0){
					showAll();
					return false;
				}else{
					return alert('请设置抽奖人数');
				}
			}

			var flag3=true;
			if(selnum<m[djc]){
				flag3=false;
			}
			var flag2=true;
			var html = '';
			var list=[];
			var countnum=countProperties(datajson);		
		
			//奖品图片背景
			$("#result").hide();
			$(".container").show();
			$(".signthreed-wall-block").animateControl('zoomIn');
			$(".lottery-wrapper").addClass('hidden');
			$(".lottery-luckers").removeClass('lottery-popBox-ani').addClass('hidden');		
			$(".start-btn").hide();
			$(".lottery-stop-btn").show();

			
			var outluckerRender = template('outlucker-tpl');
			for(var j =0;j<selnum;j++){
				if(countnum>0){
					var id=parseInt(Math.floor(Math.random()*countnum));
					if(id-countnum+1>=0){
						id=parseInt(Math.floor(Math.random()*countnum));
					} 
					if(datajson[id]!=null){
						var temp=[];
						var length=0;
						var flag=false;
						//datajson[id]['awardname']=jxnames[Number(djc)];
						if(localStorage[jx+'temp'+mtid]!=null){
							var storedNames=JSON.parse(localStorage[jx+'temp'+mtid]);
							for(var i = 0; i < storedNames.length; i++){
								if(storedNames[i]==""){
									flag=true;
									var flag1=1;
								}	
							}
							if(flag==false){
								for(var t = 0; t < storedNames.length; t++){
									temp[t+1] =storedNames[t];
									flag==false;
								}
							}
							if(flag==true){
								for(var i = 0; i < storedNames.length; i++){
									temp[i] =storedNames[i];	
										if(flag1=='1'){
											if(storedNames[i]==""){
												temp[i]=datajson[id];
												flag1='2';
											}
										}
									}
							}
						}
						if(flag==false){
							temp[0]=datajson[id];
						}
						localStorage[jx+'temp'+mtid]=JSON.stringify(temp);
						var datajson=getdata();
						var countnum=countProperties(datajson);
					}
				}else{
					flag2=false;
					break;	
				}
			}

			if(flag2){
				localStorage[jx+'djc'+mtid]=Number(djc)+1;
				var pagenum=m[localStorage[jx+'djc'+mtid]-1];
			}else{
				var pagenum=m[localStorage[jx+'djc'+mtid]];
			}
		
			var storedNames3=JSON.parse(localStorage[jx+'temp'+mtid]);
			var djc=localStorage[jx+'djc'+mtid];
			var prize=turnumber;
			var m = prize.split(",");
			var sum=0;
			for(var j=0;j<m.length;j++){
				if(j<djc){
					sum+=Number(m[j]);
				}
			}
			if(storedNames3.length>sum){
				djc=Number(djc)+1;
			}
			var cjgs=m[djc-1];
			var pagenum=storedNames3.length-sum;
			if(pagenum==0){
				pagenum=cjgs;
			}else if(pagenum<0){
				djc=Number(djc)-1;
				var sum=0;
				for(var j=0;j<m.length;j++){
					if(j<djc){
						sum+=Number(m[j]);
					}
				}
				pagenum=storedNames3.length-sum;
			}

			var num=0;
			var num1=0;
			if(localStorage[jx+'temp'+mtid]!=null){
				var storedNames=JSON.parse(localStorage[jx+'temp'+mtid]);
				num=storedNames.length;
				for(var i = 0; i < storedNames.length; i++){
					if(storedNames[i]!="" && storedNames[i]!=null){
						num1++;
						var reg=new RegExp(" ","g");
						if( i-pagenum<0){
							list.push(storedNames[i]);
						}
					}
				}
			}
		
			var arr=new Array();
			for(var i = 0; i < list.length; i++){
				arr[list.length-i-1]=list[i];
			}

			if(arr.length==1||arr.length==2){
				outluckerRender=template('outlucker-tpl1');
			}else if(arr.length==3){
				outluckerRender=template('outlucker-tpl3');
			}else if(arr.length==4){
				outluckerRender=template('outlucker-tpl4');
			}else if(arr.length==5){
				outluckerRender=template('outlucker-tpl5');
			}else if(arr.length==6){
				outluckerRender=template('outlucker-tpl6');
			}else if(arr.length==7 || arr.length==8){
				outluckerRender = template('outlucker-tpl7');
			}else if(arr.length==9 || arr.length==10){
				outluckerRender = template('outlucker-tpl9');
			}else if(arr.length==11 || arr.length==12){
				var outluckerRender = template('outlucker-tpl6');
			}else if(arr.length>=40){
				var outluckerRender = template('outlucker-tpl100');
			}else{
				outluckerRender=template('outlucker-tpl');
			}

			$.each(arr,function(i,v){
				var nickname = '';
				nickname += '<div>'+v.TrueName+'</div>';
				v.nickname = nickname;
				var post = '';
					post += '<div>'+v.Post+'</div>';
					v.post = post;
				var comname = '';
				comname += '<div>'+v.ComName+'</div>';
				v.comname = comname;
				html += outluckerRender(v);
					
			});
			$(".lottery-luckers-wrapper").html(html);	
			$(".Sparkling").css('display', 'none');
			$(".lottery-poptitle").html("获奖名单");
		}else{
				//alert('请勿过快点击');
		}
	});



	$(".lottery-stop-btn").on('click',function(){
		if(isClick) {
			$(".container").hide();
			$(".lottery-stop-btn").hide();
			$(".start-btn").show();
			$(".lottery-luckers").removeClass('hidden').addClass('lottery-popBox-ani');
			$(".lottery-luckers-wrapper").animateControl('bounceInDown');
			$(".Sparkling").css('display', 'block');
			var storedNames=JSON.parse(localStorage[jx+'temp'+mtid]);
			//setjxname();
			isClick=false;
			setTimeout(function() {
				isClick=true;
            }, 2000);//2秒内不能重复点击
		}else{
			//alert("点击过快");
		}
	});


	keyboardJS.bind('enter', function(e) {
		if($(".lottery-stop-btn").is(":hidden")) {
			$(".start-btn").trigger('click');
		}else{
			$(".lottery-stop-btn").trigger('click');
		}
	});


	//去掉取消和已中奖
	function getdata(){
		var datajson=[];
		var array=[];
		for(var i=0;i<4;i++){
			if(localStorage[i+'temp'+mtid]!=null){
				var jxarray=JSON.parse(localStorage[i+'temp'+mtid]);
				array = array.concat(jxarray);
			}
			if(localStorage[i+'qxcj'+mtid]!=null){
				var qxarray=JSON.parse(localStorage[i+'qxcj'+mtid]);
				array = array.concat(qxarray);
			}
		}
		
		if(array.length>0){
			for (var i =0; i< datajson1.length; i++) {
				a = datajson1[i].uid;
				var flg = false;
				for (var j =0; j< array.length; j++) {
					if(datajson1[i]==""){
						flg = true;
					}
					if(datajson1[j]==""){
						flg = true;
					}
					if(array[j]!=null){
						b = array[j].uid;
					}else{
						b=0;
					}
					if (a==b ) {
						flg = true;
					}
				}
				if(!flg){
					datajson.push(datajson1[i]);
				}
			}
		}else{
			datajson= datajson1;
	 	}
		return datajson;
	}


	//取消中奖
	function qxzj(obj,uid,name){
		if($(".lottery-poptitle").html().indexOf("全部") != -1){
			return false;
		}
		if(window.confirm("确定"+name+"取消中奖吗?")){
			obj.parentNode.remove();
			var storedNames=JSON.parse(localStorage[jx+'temp'+mtid]);
			var qxcj=[];
			var length=0;
			if(localStorage[jx+'qxcj'+mtid]!=null){
				var qxcjs=JSON.parse(localStorage[jx+'qxcj'+mtid]);
				length=qxcjs.length;
				for(var i = 0; i <qxcjs.length; i++){
					qxcj[i] =qxcjs[i];
				}
			}
			for(var i = 0; i < storedNames.length; i++){
				var reg=new RegExp(" ","g");
				if(storedNames[i]!=null&&uid==storedNames[i].uid){
					qxcj[length] =storedNames[i];
					storedNames.splice(i, 1);	
				}
				localStorage[jx+'temp'+mtid]=JSON.stringify(storedNames);
			}
			localStorage[jx+'qxcj'+mtid]=JSON.stringify(qxcj);
			var storedNames=JSON.parse(localStorage[jx+'temp'+mtid]);

			var djc=localStorage[jx+'djc'+mtid];
			var prize=turnumber;
			var m = prize.split(",");
			var sum=0;
			for(var j=0;j<m.length;j++){
				if(j<djc){
					sum+=Number(m[j]);
				}
			}
			if(storedNames.length<sum){
				localStorage[jx+'djc'+mtid]=Number(djc)-1;
			}
			var num1=0;
			var num=0;
			if(localStorage[jx+'temp'+mtid]!=null){
				var storedNames=JSON.parse(localStorage[jx+'temp'+mtid]);
				num=storedNames.length;
				for(var i = 0; i < storedNames.length; i++){
					if(storedNames[i]!=""&& storedNames[i]!=null){
						num1++;
					}
					if(storedNames[i]!=null){
						num++;
					}
				}
			}
		}
	}

	function countProperties (obj) {
		var count = 0;
		for (var property in obj) {
			if (Object.prototype.hasOwnProperty.call(obj, property)) {
				count++;
			}
		}
		return count;
	}

	function showAll(){
		var num=0;
		var num1=0;
		var list = [];
		var outluckerRender = template('outlucker-tpl3');
		var html = '';
		if(localStorage[jx+'temp'+mtid]!=null){
			$("#result").hide();
			var storedNames=JSON.parse(localStorage[jx+'temp'+mtid]);
			var djc=localStorage[jx+'djc'+mtid];
			var prize=turnumber;
			var m = prize.split(",");
			var sum=0;
			var allsum=0;
			for(var j=0;j<m.length;j++){
				if(j<djc){
					sum+=Number(m[j]);
				}
				allsum+=Number(m[j]);
			}
			if(storedNames.length>sum){
				djc=Number(djc)+1;
			}
			var cjgs=m[djc-1];
			var pagenum=storedNames.length-sum;
			if(pagenum==0){
				pagenum=cjgs;
			}else if(pagenum<0){
				var djc=Number(localStorage[jx+'djc'+mtid])-1;
				var sum=0;
				for(var j=0;j<m.length;j++){
					if(j<djc){
						sum+=Number(m[j]);
					}
				}
				pagenum=storedNames.length-sum;
			}
			num=storedNames.length;
			for(var i = 0; i < storedNames.length; i++){
				if(storedNames[i]!=""){
					num1++;
					if(i-pagenum<0){
						list.push(storedNames[i]);
					}	
				}
			}
			
			bodybg1="url(images/choujiang/429/bodybg.jpg)";
			bodybg2="url(images/choujiang/429/bodybg2.jpg)";
			bodybg3="url(images/choujiang/429/bodybg3.jpg)";

			if(mtid==434){
				bodybg1="url(images/choujiang/434/bodybg.jpg)";
				bodybg2="url(images/choujiang/434/bodybg2.jpg)";
				bodybg3="url(images/choujiang/434/bodybg3.jpg)";
			}
			
			if($(".lottery-poptitle").html().indexOf("全部") != -1){
				$(".lottery-poptitle").html("获奖名单");
				$("#jxname").css("display","");
				if(list.length==1){
					var outluckerRender = template('outlucker-tpl1');
				}else if(list.length==2){
					var outluckerRender = template('outlucker-tpl1');
				}else if(list.length==3){
					var outluckerRender = template('outlucker-tpl3');
				}else if(list.length==4){
					var outluckerRender = template('outlucker-tpl4');
				}else if(list.length==5){
					var outluckerRender = template('outlucker-tpl5');
				}else if(list.length==6){
					var outluckerRender = template('outlucker-tpl6');
				}else if(list.length==7 || list.length==8){
					var outluckerRender = template('outlucker-tpl7');
				}else if(list.length==9 || list.length==10){
					var outluckerRender = template('outlucker-tpl9');
				}else if(list.length==11 || list.length==12){
					var outluckerRender = template('outlucker-tpl6');
				}else if(list.length>=40){
					var outluckerRender = template('outlucker-tpl100');
				}else{
					var outluckerRender = template('outlucker-tpl');
				}
				$(".Sparkling").css('display', 'block');
				//document.body.style.backgroundImage = "url(images/choujiang/bodybg.jpg)";
				document.body.style.backgroundImage = bodybg1;
			}else{
				$(".lottery-poptitle").html("全部获奖名单");
				$("#jxname").css("display","none");
				list=storedNames;
				if(list.length==1){
					var outluckerRender = template('outlucker-tpl1');
					$(".Sparkling").css('display', 'none');
					//document.body.style.backgroundImage = "url(images/choujiang/bodybg2.jpg)";
					document.body.style.backgroundImage = bodybg2;
				}else if(list.length==2){
					var outluckerRender = template('outlucker-tpl1');
				}else if(list.length==3){
					var outluckerRender = template('outlucker-tpl3');
				}else if(list.length==4){
					var outluckerRender = template('outlucker-tpl4');
				}else if(list.length==5){
					var outluckerRender = template('outlucker-tpl5');
				}else if(list.length==6){
					var outluckerRender = template('outlucker-tpl6');
				}else if(list.length==7 || list.length==8){
					var outluckerRender = template('outlucker-tpl7');
				}else if(list.length==9 || list.length==10){
					var outluckerRender = template('outlucker-tpl9');
				}else if(list.length==11 || list.length==12){
					var outluckerRender = template('outlucker-tpl6');
				}else if(list.length>=40){
					var outluckerRender = template('outlucker-tpl100');
				}else{
					var outluckerRender = template('outlucker-tpl');
				}
				// if(list.length>16){
				// 	$(".lottery-luckers").height('800px');
				// }
				$(".Sparkling").css('display', 'none');
				//document.body.style.backgroundImage = "url(images/choujiang/bodybg3.jpg)";
				document.body.style.backgroundImage = bodybg3;
			}
			var arr=new Array();
			for(var i = 0; i < list.length; i++){
				arr[list.length-i-1]=list[i];
			}
			$.each(arr,function(i,v){
				var nickname = '';
				nickname += '<div>'+v.TrueName+'</div>';
				v.nickname = nickname;
				var post = '';
				post += '<div>'+v.Post+'</div>';
				v.post = post;
				var comname = '';
				comname += '<div>'+v.ComName+'</div>';
				v.comname = comname;
				html += outluckerRender(v);
				
			});
			$(".lottery-luckers-wrapper").html(html);
			$(".wall3d").addClass('hidden');
			$(".lottery-userBox").addClass('hidden');
			$(".lottery-stop-btn").hide();
			$(".start-btn").show();
			$(".lottery-luckers").removeClass('hidden').addClass('lottery-popBox-ani');
		}

	}

	$(".lottery-popBox-prize").on('click',function(){
		window.open('meeting.php?view=cj_set&jx='+jx,"_blank");
	});
	$("#top_title_xyj").on('click',function(){
		window.open('meeting.php?view=cj_set&jx='+jx,"_blank");
	});

</script>

</body>
</html>