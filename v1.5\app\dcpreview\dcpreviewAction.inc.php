<?php
class dcpreviewAction extends AbstractAction{
   public function __construct(){
    parent::__construct();    
  }
	public function index($params)
	{
		//echo $_SESSION["gogo"]."<br>";$_SESSION["gogo"]="";
		//print_r($params);
		//hlf start 2020/7/23
		if($params['mc_type']=='2'){
			$mc_type=2;
			$db="gc_price_model";
		}else{
			$mc_type=1;
			$db="ng_price_model";
		}
		//print_r($db);
		//hlf end
        $type=$params["Type"];
		$mode=$params["mode"];//null、1:PC端 2:Android 手机 3:Android 平板 4:iphone 5:ipad
		$page=$this->formatpage($params["page"]);
		$pagenum="10";//一页显示的条数
		$search="";
		$startdate=$params["startdate"];
		$enddate=$params["enddate"];		
		$GUID=$params["GUID"];
		$userid=$this->getuidbyGUID($GUID);
		{
			$select=" select count(1) from steelhome_drc.".$db." ";
			$where=" where modeltype='$type' and is_wangqi='1' "; //此处ismakepricy应为1,0只是为了测试用
			//and id in (select distinct modelid from steelhome_drc.ng_price_model_detail where uid='$userid')
			//$where=" where modeltype='2' and ismakepricy='0' and id in (select distinct modelid from steelhome_drc.ng_price_model_detail where uid='-1')"; //测试用
			$search.=$params["title"]==""?" ":" and modeltitle like '%$params[title]%' ";
			$search.=$startdate==""?" ":" and date>='$startdate' ";
			$search.=$enddate==""?" ":" and date<='$enddate' ";
			$orderby=" order by date desc ";
			$limit=" limit ".(($page-1)*$pagenum).",".$pagenum;
		}
		$amount=$this->_dao->getone($select.$where.$search); //echo $amount;
		
		$select=" select * from steelhome_drc.".$db." force index(date)";
		$sql=$select.$where.$search.$orderby.$limit;
		//echo $sql."<br>";
		$arr2=$this->_dao->query($sql);

		/*foreach($arr as &$ar){
			foreach($ar as &$a){
				$a=substr(htmlspecialchars($a),0,30);
			}
		}*/
		foreach($arr2 as $k=>$v){
			$arr[$k+($page-1)*$pagenum+1]=$v;
		}
		//hlf strat 2020/7/20
		if($mc_type==2){
			$privilege_arr=$GLOBALS['privilege'][2];
		}else{
			$privilege_arr=$GLOBALS['privilege'][1];
		}
		foreach($privilege_arr as $id=>$title){
			//echo $id."<br>";
			if($type==$id){
				$title1=substr(trim($title),0,strlen($title)-6);				//取品种名称，即从第一个字符到倒数第七个字符
				$title2=substr(trim($title),strlen($title)-6,strlen($title)-1);	//取定价类型，即最后六个字符
			}
		}
		//hlf end
		$pagelabel=$this->getpagelabel($amount,$pagenum,$page,"dcpreview.php?view=index&Type=".$type."&GUID=".$GUID."&mode=".$mode."&startdate=".$startdate."&enddate=".$enddate."&title=".$params['title']."&mc_type=".$mc_type);
		if($params['mc_type']=='2'){
			$pagelabel=$this->getpagelabel($amount,$pagenum,$page,"dcpreview.php?view=sg_index&Type=".$type."&GUID=".$GUID."&mode=".$mode."&startdate=".$startdate."&enddate=".$enddate."&title=".$params['title']."&mc_type=".$mc_type);
		}
		
		
		$this->assign("type",$type);
		$this->assign("title1",$title1);
		$this->assign("title2",$title2);
		$this->assign("arr",$arr);
		$this->assign("page",$page);
		$this->assign("mode",$mode);
		$this->assign("mc_type",$mc_type);
		$this->assign("GUID",$GUID);
		$this->assign("pagenum",$pagenum);
		$this->assign("pagelabel",$pagelabel);
		$url = "dcpreview.php";
		unset( $params['page'] );
		$pagebar = pagebar( $url, $params, $pagenum, $page, $amount );
		$this->assign( "pagebar", $pagebar );
	}

	public function lymanage($params){
		$modelid=$params['modelid'];
		$SignCS=$params['SignCS'];
		$GUID=$params['GUID'];
		$Type=$_GET["Type"];
		$mode=$_GET["mode"];

		$userid=$this->getuidbyGUID($GUID);

		$lyarr=$this->_dao->query("select * from Ng_RecordManage where modelid='$modelid' order by id");
		foreach($lyarr as &$ar){
			//$ar["URL"]="//iwww.steelhome.cn/data/uploadfile/recorder/".$ar["URL"];//测试
			$ar["URL"]="//dc.steelhome.cn/uploadfile/recorder/".$ar["URL"];//正式
		}
		switch($mode){
			case 1:$fontsize=20;break;
			case 2:
			case 4:$fontsize=40;break;
			case 3:
			case 5:$fontsize=30;break;
			default:$fontsize=20;break;
		}
		$this->assign("fontsize",$fontsize."px");
		$this->assign("lyarr",$lyarr);
		$this->assign("SignCS",$SignCS);
		$this->assign("GUID",$GUID);
		$this->assign("Type",$Type);
		$this->assign("mode",$mode);
	}

	public function doexportdata($params){
		//hlf start 2020/7/23
		if($params['mc_type']==2){
			$mc_type=2;
			$db="gc_price_model";
		}else{
			$mc_type=1;
			$db="ng_price_model";
		}
		//print_r($db);exit;
		//hlf end
		//$_SESSION["gogo"]="download fail";
		$id=$_GET["id"];
		$type=$_GET["Type"];
		$SignCS=$params['SignCS'];
		$GUID=$_GET["GUID"];
		//$id=845;
		$sql="select modeltitle,pricycontent from steelhome_drc.".$db." where id='$id'";
		$data=$this->_dao->getRow($sql);
		$con=$data["pricycontent"];

		
		$rr=strpos($con,'http://dc.steelhome.cn'); 
		if($rr){
			$con=str_replace("http:","https:",$con);
		}else{
			$tt=strpos($con,'//dc.steelhome.cn'); 
			if($tt){
				$con=str_replace("//dc.steelhome.cn","https://dc.steelhome.cn",$con);
			}
		}


		
		
		if($con=="") {echo"<script>window.location='dcpreview.php?view=index&Type=$type&GUID=$GUID&mc_type=$mc_type'</script>";exit;}
		
		$temp2=$con;
		
		$con=iconv('gbk','utf-8',html_entity_decode($con, ENT_QUOTES,"ISO-8859-1")); 
		

		if($con==''){
			$con=html_entity_decode($temp2, ENT_QUOTES,"UTF-8");
		}
		//echo $con;exit;

		$conarr=explode("/",$con);
		foreach($conarr as $i=>$d){
			if(strlen($d)<100&&strstr($d,".css")){
				$d=str_replace("_large.css","_small.css",$d);
				$d=str_replace("_middle.css","_small.css",$d);
				$conarr[$i]=$d;
				//echo $conarr[$i];
			}
		}
		$con=implode("/",$conarr);

		$name=$data["modeltitle"];
		ob_start(); //打开缓冲区 
		header("Cache-Control: public");
		Header("Content-type: application/octet-stream"); 
		Header("Accept-Ranges: bytes"); 

		if (strpos($_SERVER["HTTP_USER_AGENT"],'MSIE')) {
			header('Content-Disposition: attachment; filename='.$name.'.doc');
		}else if (strpos($_SERVER["HTTP_USER_AGENT"],'Firefox')) {
			header('Content-Disposition: attachment; filename='.$name.'.doc'); 
		} else {
			header('Content-Disposition: attachment; filename='.$name.'.doc');
		}
		header("Pragma:no-cache");
		header("Expires:0");
		$con=iconv('utf-8','gbk',$con);
		echo $con;
		ob_end_flush();
		//$_SESSION["gogo"]="download";
		exit;
	}

	public function webview(){
		header('Content-Type:text/html;charset=utf8');
		$id=$_GET["id"];
		$type=$_GET["Type"];
		$GUID=$_GET["GUID"];
		//hlf start 2020/7/23
		if($_GET['mc_type']==2){
			$db="gc_price_model";
		}else{
			$db="ng_price_model";
		}
		//hlf end
		$mode=$_GET["mode"];//echo $mode;//null、1:PC端 2:Android 手机 3:Android 平板 4:iphone 5:ipad
		$sql="select id,modeltitle,pricycontent,modelcontent from steelhome_drc.".$db." where id='$id'";
		//print_r($sql);
		$data=$this->_dao->getRow($sql);
		// print_r($data);
		 
		//$data['modeltitle'] =iconv('gbk','utf-8',html_entity_decode($data['modeltitle'], ENT_QUOTES,"ISO-8859-1"));
		// $temp1=$data['pricycontent'];
		// $data['pricycontent'] =iconv('gbk','utf-8',html_entity_decode($data['pricycontent'], ENT_QUOTES,"ISO-8859-1"));
		 
		// if($data['pricycontent']==''){
		// 	$data['pricycontent'] =html_entity_decode($temp1, ENT_QUOTES,"UTF-8");
		// }
		$con=$data["pricycontent"]!=''?$data["pricycontent"]:$data["modelcontent"];
		$con=str_replace("http:","",$con);
		//if($con=="") {echo"<script>window.location='dcpreview.php?view=index&Type=$type&GUID=$GUID'</script>";exit;}
	
		//$con=html_entity_decode($con, ENT_QUOTES,"UTF-8");
		$temp2=$con;
		$con=iconv('gbk','utf-8',html_entity_decode($con, ENT_QUOTES,"ISO-8859-1")); 
		

		if($con==''){
			$con =html_entity_decode($temp2, ENT_QUOTES,"UTF-8");
		}

		$conarr=explode("/",$con);
		foreach($conarr as $i=>$d){
			if(strlen($d)<100&&strstr($d,".css")){

				if($mode=="1"||$mode==""){								//pc端
					$d=str_replace("_large.css","_small.css",$d);
					$d=str_replace("_middle.css","_small.css",$d);
				}elseif($mode=="3"||$mode=="5"){						//
					$d=str_replace("_small.css","_middle.css",$d);
					$d=str_replace("_large.css","_middle.css",$d);
				}elseif($mode=="2"||$mode=="4"){
					$d=str_replace("_middle.css","_large.css",$d);
					$d=str_replace("_small.css","_large.css",$d);
				}
				$conarr[$i]=$d;
				//echo $conarr[$i];
			}
		}
		switch($mode){
			case 1:$fontsize=20;break;
			case 2:
			case 4:$fontsize=34;break;
			case 3:
			case 5:$fontsize=26;break;
			default:$fontsize=20;break;
		}
		$this->assign("fontsize",$fontsize."px");
		$this->assign("content",implode("/",$conarr));
	}

	public function del($params){
		/*$id=$params["delid"];
		
		$SignCS=$params['SignCS'];
		$GUID=$params['GUID'];
		$Type=$params['Type'];

		$userid=$this->getuidbyGUID($GUID);
		$haspower=$this->t1Dao->getone("select orderno from app_license_privilege where uid='$userid' and mc_type='1' limit 1");
		$haspower=explode(",",$haspower);
		$haspower=$haspower[$Type-1];
		if($haspower=="") {echo "0";exit;}
		$orderno=$this->t1Dao->query("select orderno from app_license_privilege where mc_type='1'");
		$hasnopower=0;
		foreach($orderno as &$no){
			$no=explode(",",$no);
			$no=$no[$Type-1];
			if($no-$haspower>=0) {$hasnopower=1;break;} //如果不是最后一个分管领导，还是无权限
		}
		if($hasnopower){echo "0";exit;}

		$sql="delete from Ng_RecordManage where id='$id'";
		//echo $sql;2018/1/25
		$this->_dao->execute($sql);
		echo 1;
		*/
	}

	public function tblist($params)
	{
		//header('Content-Type:text/html;charset=UTF-8');
        $type=$params["Type"];
		$mode=$params["mode"];//null、1:PC端 2:Android 手机 3:Android 平板 4:iphone 5:ipad
		$mc_type=$params['mc_type'];
		$page=$this->formatpage($params["page"]);
		$pagenum="8";//一页显示的条数
		$search="";
		$startdate=$params["startdate"];
		$enddate=$params["enddate"];
		$GUID=$params["GUID"];
		$userid=$this->getuidbyGUID($GUID);
		{
			$select=" select count(1) from steelhome_drc.TaoliModel ";
			$where=" where Type='$type' and mc_type='$mc_type'"; 
			$search.=$startdate==""?" ":" and date>='$startdate' ";
			$search.=$enddate==""?" ":" and date<='$enddate' ";
			$orderby=" order by date desc ,createtime desc";
			$limit=" limit ".(($page-1)*$pagenum).",".$pagenum;
		}
		$amount=$this->_dao->getone($select.$where.$search); //echo $amount;
		
		$select=" select id,createtime,date from steelhome_drc.TaoliModel force index(date)";
		$sql=$select.$where.$search.$orderby.$limit;
		//echo $sql."<br>";
		$arr2=$this->_dao->query($sql);

		foreach($arr2 as $k=>$v){
			$arr[$k+($page-1)*$pagenum+1]=$v;
		}

		$title=$GLOBALS["TB_TYPE"][$type];
		foreach($arr as $k=> $v){
			$arr[$k]["title"]=$title.date("Y年n月j日H时i分s秒",strtotime($v['createtime']));
		}
		//print"<pre>";print_r($arr);exit;
		if($mc_type==2 or $mc_type==3){
			$pagelabel=$this->getpagelabelnew($amount,$pagenum,$page,"dcpreview.php?view=".$params['view']."&Type=".$type."&GUID=".$GUID."&mc_type=".$mc_type."&mode=".$mode."&startdate=".$startdate."&enddate=".$enddate);
		}else{
			$pagelabel=$this->getpagelabel($amount,$pagenum,$page,"dcpreview.php?view=index&Type=".$type."&GUID=".$GUID."&mode=".$mode."&startdate=".$startdate."&enddate=".$enddate."&title=".$params['title']."&mc_type=".$mc_type);
		}
	
		$this->assign("type",$type);
		$this->assign("arr",$arr);
		$this->assign("GUID",$GUID);
		$this->assign("mc_type",$mc_type);
		$this->assign("mode",$mode);
		$this->assign("page",$page);
		$this->assign("title",$title);
		$this->assign("view",$params['view']);
		$this->assign("pagenum",$pagenum);
		$this->assign("pagelabel",$pagelabel);
	}

	public function tbdata(){
		header('Content-Type:text/html;charset=utf8');
		$id=$_GET["id"];
		$type=$_GET["Type"];
		$GUID=$_GET["GUID"];
		$mode=$_GET["mode"];//echo $mode;//null、1:PC端 2:Android 手机 3:Android 平板 4:iphone 5:ipad
		$sql="select modelcontent from steelhome_drc.TaoliModel where id='$id'";
		$data=$this->_dao->getRow($sql);
		$con=$data["modelcontent"];
		$con=html_entity_decode($con, ENT_QUOTES,"UTF-8");
		$con=str_replace("http:","",$con);
		$conarr=explode("/",$con);
		foreach($conarr as $i=>$d){
			if(strlen($d)<100&&strstr($d,".css")){

				if($mode=="1"||$mode==""){								//pc端
					$d=str_replace("_large.css","_small.css",$d);
					$d=str_replace("_middle.css","_small.css",$d);
				}elseif($mode=="3"||$mode=="5"){						//
					$d=str_replace("_small.css","_middle.css",$d);
					$d=str_replace("_large.css","_middle.css",$d);
				}elseif($mode=="2"||$mode=="4"){
					$d=str_replace("_middle.css","_large.css",$d);
					$d=str_replace("_small.css","_large.css",$d);
				}
				$conarr[$i]=$d;
				//echo $conarr[$i];
			}
		}
		$title=$GLOBALS["TB_TYPE"][$type];
		//file_put_contents("/tmp/a27.txt",print_r(implode("/",$conarr),true),FILE_APPEND);
		$style_url= dirname("//".$_SERVER['SERVER_NAME'].$_SERVER['PHP_SELF']);
		$this->assign( "style_url", $style_url );
		$this->assign("content",implode("/",$conarr));
		$this->assign("mode",$mode);
		$this->assign("title",$title);
	}
	
	private function formatpage($page){
		$page=(int)$page;
		if($page-1<0) $page=1;
		return $page;
	}

	private function getpagelabel($amount,$pagenum,$page,$url){
		$pagemax=($amount%$pagenum==0)?round($amount/$pagenum,0):(floor($amount/$pagenum)+1);
		//echo $amount." ".$pagenum." ".$page." ".$pagemax." ".$url;
		$label="";
		$ye=3;		//最多隔页显示的可点击页数
		if($pagemax==0) return "<div class='page tc oh mt20'><span class='disabled'>上一页</span> <span class='me'> 1 </span> <span class='disabled'>下一页</span></div>";			//如果没有数据，则直接返空
		if($page==1) $label="<span class='disabled'>上一页</span><span class='me'>1</span>"; //第一页
		else $label="<a href='$url&page=".($page-1)."' class='sxy'>上一页</a><a href='$url&page=1'>1</a>";
		for($i=2;$i<$pagemax;$i++){
			if($i-$page>$ye||$page-$i>$ye) continue;
			elseif($i-$page==$ye||$page-$i==$ye){
				$label.="...";
			}elseif($i==$page){
				$label.="<span class='me'>$i</span>";
			}else{
				$label.="<a href='$url&page=$i'>$i</a>";
			}
		}
		if($pagemax>1) {
			if($pagemax!=$page)
				$label.="<a href='$url&page=$pagemax'>$pagemax</a>"; //最后一页
			else
				$label.="<span class='me'>$page</span>";
		}
		if($page==$pagemax) $label.="<span class='disabled'>下一页</span>";
		else $label.="<a href='".$url."&page=".($page+1)."' class='sxy'>下一页</a>";
		return "<div class='page tc oh mt20'>".$label."</div>";
	}
	function getuidbyGUID($GUID){
		$uid="";
		$sql="select Uid from steelhome_t1.app_session_temp where GUID='$GUID'";
		$uid=$this->t1Dao->getone($sql);
		return $uid;
	}
	
	private function getpagelabelnew($amount,$pagenum,$page,$url){
		$pagemax=($amount%$pagenum==0)?round($amount/$pagenum,0):(floor($amount/$pagenum)+1);
		
		//echo $amount." ".$pagenum." ".$page." ".$pagemax." ".$url;
		$label="<div class='flex justify-between flex-1 sm:hidden'>";
		if($page==1 || $pagemax==0){
			$label.="<span class='relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default leading-5 rounded-md'>
                    
			上一页
		</span>";
		}else{
			$label.="<a href='$url&page=".($page-1)."' class='relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 leading-5 rounded-md hover:text-gray-500 focus:outline-none focus:shadow-outline-blue focus:border-blue-300 active:bg-gray-100 active:text-gray-700 transition ease-in-out duration-150'>

			上一页
			</a>";
		}

		if($page==$pagemax || $pagemax==0){
			$label.="<span class='relative inline-flex items-center px-4 py-2 ml-3 text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default leading-5 rounded-md'>下一页</span>";
		}else{
			$label.="<a  href='$url&page=".($page+1)."'class='relative inline-flex items-center px-4 py-2 ml-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 leading-5 rounded-md hover:text-gray-500 focus:outline-none focus:shadow-outline-blue focus:border-blue-300 active:bg-gray-100 active:text-gray-700 transition ease-in-out duration-150'>

			下一页
			</a>";
		}
		$label.="</div>";
		$ye=10;
		$label.="<div class='hidden sm:flex-1 sm:flex sm:items-center sm:justify-between'>
		<div>
		<p class='text-sm text-gray-700 leading-5'>
		Showing
		<span class='font-medium'>16</span>
		to
		<span class='font-medium'>30</span>
		of
		<span class='font-medium'>41</span>
		results
		</p>
		</div>";	

		if($page==1 || $pagemax==0) {//第一页
			$label.="<div><span class='relative z-0 inline-flex shadow-sm rounded-md'><span aria-disabled='true' aria-label='&amp;laquo; Previous'>
				<span class='relative inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default rounded-l-md leading-5' aria-hidden='true'>
					<svg class='w-5 h-5' fill='currentColor' viewBox='0 0 20 20'>
						<path fill-rule='evenodd' d='M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z' clip-rule='evenodd'></path>
					</svg>
				</span>
			</span>";
 		}else {
			$label.="<div><span class='relative z-0 inline-flex shadow-sm rounded-md'><a href='$url&page=".($page-1)."' rel='prev' class='relative inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-l-md leading-5 hover:text-gray-400 focus:z-10 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue active:bg-gray-100 active:text-gray-500 transition ease-in-out duration-150' aria-label='&amp;laquo; Previous' ><svg class='w-5 h-5' fill='currentColor' viewBox='0 0 20 20'>
			<path fill-rule='evenodd' d='M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z' clip-rule='evenodd' />
		</svg></a>";
		}
		if($pagemax==0){
			$label.="<span aria-current='page'><span class='relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default leading-5'>1</span></span>";
		}else{
			if($page==1){
				$label.="<span aria-current='page'><span class='relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default leading-5'>1</span></span>";
			}else{
				$label.="<a href='$url&page=1' class='relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-gray-700 bg-white border border-gray-300 leading-5 hover:text-gray-500 focus:z-10 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue active:bg-gray-100 active:text-gray-700 transition ease-in-out duration-150' aria-label='Go to page 1'>1</a>";
			}	
			$label1="<span class='relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-gray-700 bg-white border border-gray-300 leading-5 hover:text-gray-500 focus:z-10 focus:outline-none focus:border-blue-300  transition ease-in-out duration-150'>...</span>";
			
			for($i=2;$i<$pagemax;$i++){
				$label2="<span aria-current='page'><span class='relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default leading-5'>$i</span></span>";
				$label3="<a href='$url&page=$i' class='relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-gray-700 bg-white border border-gray-300 leading-5 hover:text-gray-500 focus:z-10 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue active:bg-gray-100 active:text-gray-700 transition ease-in-out duration-150' aria-label='Go to page $i'>$i</a>";
				
				if($page<$ye-1){//前10，
					if( $i>$ye ) continue;
					if($i==$ye){
						$label.=$label1;
					}elseif($i==$page){
						$label.=$label2;
					}else{
						$label.=$label3;
					}
				}else if($pagemax-$page<$ye-1){//后10
					if( $i<$pagemax-$ye) continue;
					if($i==$pagemax-$ye){
						$label.=$label1;
					}elseif($i==$page){
						$label.=$label2;
					}else{
						$label.=$label3;
					}
				}else{//中间数
					if( $i<$page-2 || $i>$page+$ye-2) continue;
					if($i==$page-2 || $i==$page+$ye-2){
						$label.=$label1;
					}elseif($i==$page){
						$label.=$label2;
					}else{
						$label.=$label3;
					}
				}
			
			}
		}
		
		if($pagemax>1) {
			if($pagemax!=$page)
				$label.="<a href='$url&page=$pagemax'  class='relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-gray-700 bg-white border border-gray-300 leading-5 hover:text-gray-500 focus:z-10 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue active:bg-gray-100 active:text-gray-700 transition ease-in-out duration-150' aria-label='Go to page $pagemax'>$pagemax</a>"; //最后一页
			else
				$label.="<span aria-current='page'><span class='relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default leading-5'>$page</span></span>";
		}
		if($page==$pagemax || $pagemax==0){
			$label.="<span aria-disabled='true' aria-label='Next &amp;raquo;'>
				<span class='relative inline-flex items-center px-2 py-2 -ml-px text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default rounded-r-md leading-5' aria-hidden='true'>
					<svg class='w-5 h-5' fill='currentColor' viewBox='0 0 20 20'>
						<path fill-rule='evenodd' d='M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z' clip-rule='evenodd' />
					</svg>
				</span>
			</span>";
		} else {$label.="<a href='".$url."&page=".($page+1)."' rel='next' class='relative inline-flex items-center px-2 py-2 -ml-px text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-r-md leading-5 hover:text-gray-400 focus:z-10 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue active:bg-gray-100 active:text-gray-500 transition ease-in-out duration-150' aria-label='Next &amp;raquo;'>
				<svg class='w-5 h-5' fill='currentColor' viewBox='0 0 20 20'>
					<path fill-rule='evenodd' d='M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z' clip-rule='evenodd'></path>
				</svg></a>";
		}

		$label.="</span></div>";
	
		return "<div align='right' style='margin-top:5px; font-size:12px'> <nav role='navigation' aria-label='Pagination Navigation' class='flex items-center justify-between'>".$label."</nav></div>";
	}

	
}
?>