<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );

class sgfinancialweeklyreportController extends AbstractController{
  
  public function __construct(){
    parent::__construct();
	$this->_action->setDao( new sgfinancialweeklyreportDao("DRCW") );
	$this->_action->t1Dao=new sgfinancialweeklyreportDao("MAIN");
	$this->_action->homeDao=new sgfinancialweeklyreportDao("91R");
	$this->_action->gcDao=new sgfinancialweeklyreportDao("GC");
  }

	public function v_index(){
		$this->_action->index( $this->_request );
	}
	public function do_savedata(){
		$this->_action->savedata( $this->_request );
	}
}
?>