var maxwidthscen=1920;
var nowdate='2021-06-02';

nowdate='';
var articleid=0;
var GUID='';
$(function() {
    // $('#chart1Loading').show();
    // $('#chart2Loading').show();
    // $('#chart3Loading').show();
    // $('#chart4Loading').show();
    var str=getQueryString('date');
    if(str!=null)
    {
        nowdate=str;
    }
    GUID=getQueryString('GUID');
    if(GUID==null)
    {
        GUID='';
    }

    var activeIndex = -1;
    var liDoms = $(".nav ul li");
    var iframeDoms = [];
    var pepoactive=false;
    var xunhuannum=0;
    var fontSize=13;
    if(isphone==1){
        fontSize=28;
    }

    var activeId=1;
    liDoms.bind("click", function(e){
        var idx = Array.prototype.indexOf.call(liDoms, this);
        activeId = e.target.id;
        
        pepoactive=true;
        xunhuannum=0;
        clickLiByIndex(idx);
    })

    /*点击某一个*/
    function clickLiByIndex(idx){

        if(idx === activeIndex) return;
        var predom = liDoms[activeIndex];
        //取消之前的高亮样式
        if(predom){
            $(predom).removeClass("active");
        }
        //显示当前高亮样式
        var target = liDoms[idx];
        if(target){
            //EUI.addClassName(target, "active");
            $(target).addClass("active");

        }
        nowdate='';
        
        activeIndex = idx;
        activeId=$(".nav ul li").eq(idx).attr('id');
        clickIndex(activeId);
    }
    var active=getQueryString('activeIndex');
    if(active==null)
    {
        active=0;
    }
    

    clickLiByIndex(active);
    
    //clickIndex(1);
    function clickIndex(idx)
    {
       
            
            if(idx==1){
                document.getElementById("chart1").removeAttribute("_echarts_instance_");
                document.getElementById("chart2").removeAttribute("_echarts_instance_");
                document.getElementById("chart3").removeAttribute("_echarts_instance_");
                document.getElementById("chart4").removeAttribute("_echarts_instance_");
                document.getElementById("chart6").removeAttribute("_echarts_instance_");
                $('#mokuai2').hide();
                $('#mokuai3').hide();
                $('#mokuai1').show();
                $('#mokuai4').hide();
                window.scrollTo(0, 0);
                //$('#mapFrame').attr('src','meeting.php?view=map&skin=blue&ProductType=2&istry='+istry);
                initChart1();
            }else if(idx==3){
                document.getElementById("chart8").removeAttribute("_echarts_instance_");
                document.getElementById("chart9").removeAttribute("_echarts_instance_");
                //document.getElementById("chart10").removeAttribute("_echarts_instance_");
                $('#mokuai1').hide();
                $('#mokuai2').show();
                $('#mokuai3').hide();
                $('#mokuai4').hide();
                window.scrollTo(0, 0);
                initChart2();
                
            }else if(idx==2){
                document.getElementById("chart11").removeAttribute("_echarts_instance_");
                document.getElementById("chart12").removeAttribute("_echarts_instance_");
                document.getElementById("chart13").removeAttribute("_echarts_instance_");
                document.getElementById("chart14").removeAttribute("_echarts_instance_");
                document.getElementById("chart15").removeAttribute("_echarts_instance_");
                document.getElementById("chart16").removeAttribute("_echarts_instance_");
                document.getElementById("chart17").removeAttribute("_echarts_instance_");
                document.getElementById("chart18").removeAttribute("_echarts_instance_");
                $('#mokuai1').hide();
                $('#mokuai2').hide();
                $('#mokuai3').show();
                $('#mokuai4').hide();
                window.scrollTo(0, 0);
                initChart3();
            
            }else if(idx=="next1"){
                document.getElementById("chart21").removeAttribute("_echarts_instance_");
                document.getElementById("chart22").removeAttribute("_echarts_instance_");
                document.getElementById("chart23").removeAttribute("_echarts_instance_");
                document.getElementById("chart24").removeAttribute("_echarts_instance_");
                //document.getElementById("chart25").removeAttribute("_echarts_instance_");
                $('#mokuai1').hide();
                $('#mokuai2').hide();
                $('#mokuai3').hide();
                //$('#mokuai4').show();
                window.scrollTo(0, 0);
                initChart4();
            
            }
    }


    function initChart3(){
        $('#chart11Loading').show();
        $('#chart12Loading').show();
        $('#chart13Loading').show();
        $('#chart14Loading').show();
        $('#chart15Loading').show();
        $('#chart16Loading').show();
        $('#chart17Loading').show();
        $('#chart18Loading').show();

        axios.post("meeting.php?action=getMeetingSurveyScreenData&type=2&mtid="+mtid+"&id="+id+"&isphone="+isphone)
        .then((res) => 
        {
            $('#surveytitle').html(res.title);
            $('#chart11title').html(res.screentitle1);
            $('#chart12title').html(res.screentitle2);
            $('#chart13title').html(res.screentitle3);
            $('#chart14title').html(res.screentitle4);
            $('#chart15title').html(res.screentitle5);
            $('#chart16title').html(res.screentitle6);
            $('#chart17title').html(res.screentitle7);
            $('#chart18title').html(res.screentitle8);
            $('#surveytitle').html(res.title);
            $('#chart11Loading').hide();
            $('#chart12Loading').hide();
            $('#chart13Loading').hide();
            $('#chart14Loading').hide();
            $('#chart15Loading').hide();
            $('#chart16Loading').hide();
            $('#chart17Loading').hide();
            $('#chart18Loading').hide();


            if (res.screendata1) {
                let myCharts = echarts.init(document.getElementById('chart11'))
                if(res.screendata1.length>0 && res.screendata1[0].content!="" && res.screendata1[0].content!=undefined){
                
                    if (res.screendata1) {
                        var str="";
                        for(var i=0;i<res.screendata1.length;i++){
                            str+='<div class="jq33-content"> <div class="left"><span>'+(i+1)+"、"+res.screendata1[i].content+'</span></div></div>';
                           
                        }
                        document.getElementById('chart11').innerHTML=str;
                    }
                }else{
                var optiont=res.screendata1[0].name;
                let option=[];
                if(optiont.length<10){
                    //3.配置
                    option = {
                        grid: {
                            show: true,                                 //是否显示图表背景网格    
                            left: 5,                                    //图表距离容器左侧多少距离
                            right: 5,                                //图表距离容器右侧侧多少距离
                            bottom: isphone==1 ? 50 : 5,                              //图表距离容器上面多少距离
                            top: isphone==1 ? 80 : 40,                                         //图表距离容器下面多少距离
                            containLabel: true,                     //防止标签溢出  
                            
                        },
                        xAxis: {
                            type: 'category',
                            data:res.screendata1.map(item => item.name),
                            axisLabel: {
                                textStyle: {
                                    color: '#01C4F7', //坐标值得具体的颜色                   
                                },
                                fontSize:fontSize,
                                formatter:function(params){
                                    var newParamsName = "";// 最终拼接成的字符串
                                        var paramsNameNumber = params.length;// 实际标签的个数
                                        var provideNumber = id==23?4:4;// 每行能显示的字的个数
                                        var rowNumber = Math.ceil(paramsNameNumber / provideNumber);// 换行的话，需要显示几行，向上取整
                                        // 条件等同于rowNumber>1
                                        if (paramsNameNumber > provideNumber) {
                                            
                                            for (var p = 0; p < rowNumber; p++) {
                                                var tempStr = "";// 表示每一次截取的字符串
                                                var start = p * provideNumber;// 开始截取的位置
                                                var end = start + provideNumber;// 结束截取的位置
                                                // 此处特殊处理最后一行的索引值
                                                if (p == rowNumber - 1) {
                                                    // 最后一次不换行
                                                    tempStr = params.substring(start, paramsNameNumber);
                                                } else {
                                                    // 每一次拼接字符串并换行
                                                    tempStr = params.substring(start, end) + "\n";
                                                }
                                                newParamsName += tempStr;// 最终拼成的字符串
                                            }
                            
                                        } else {
                                            // 将旧标签的值赋给新标签
                                            newParamsName = params;
                                        }
                                        //将最终的字符串返回
                                        return newParamsName
                                }
                            }
                        },
                        yAxis: {
                            type: 'value',
                            axisLabel: {
                                textStyle: {
                                    color: '#01C4F7', //坐标值得具体的颜色                   
                                },
                                fontSize:fontSize
                            }
                        },
                        series: [{
                            data: res.screendata1,
                            type: 'bar',
                            barMaxWidth:40,
                            label: {
                                show: true,
                                position: 'top',
                                formatter:function(params){
                                    return params.data.result+"%\n"+params.data.value+"人";
                                    },
                                    fontSize:fontSize
                            },
                    
                            itemStyle: {
                                opacity: 0,
                                normal:{
                                    color:function(params){
                                        var colorList = [
                                            '#E87C25','#01C4F7','#FCCE10',"#FA640D",'#CB98E0','#863BF2','#087998','#1F52FA'
                                        ];
                                        return colorList[params.dataIndex];
                                    }
                                }
                            },
                        }] 
                    }
                }else{
                    option = {
                        tooltip: {
                                trigger: 'item',
                                formatter: function (params) {
                                    return '人数：'+params.data.value+"人（"+params.data.result+"%）"
                                }
                            },
                       
                        legend: {
                            y:'top',
                                  orient: 'vertical',
                                  left: 'left',
                                  bottom:30,
                                  textStyle:{
                                    color: '#00FFFF',
                                    
                                    fontSize: fontSize,
                                  }
                                },
                        label: {
                            show: true, 
                            position:'inside',
                            formatter: function (arg) {
                                let text =arg.data.name;
                                let value_format = arg.value+'人  ';
                                let percent_format = arg.data.result + '%';
                                // if (text.length >12 && isphone==1) {
                                //     return text = `${text.slice(0, 6)}\n${text.slice(6, text.length)}\n(${value_format}${percent_format})`
                                // } else{
                                    return '\n  （'+ arg.data.value + '人  '+arg.data.result+'%）' 
                                //}
                                
                            },
                            textStyle:{
                                fontSize: fontSize,
                             }
                        },
                        series: [
                            {
                                type: 'pie',
                                data: res.screendata1,
                                center: ['50%', '60%'], //饼图位置
                                itemStyle:{
                                    labelLine: {
                                        show: false,
                                        
                                    },
                                    
                                    normal: {
                                        color: function (colors) {
                                        var colorList = [
                                                    '#fc8251',
                                                    '#5470c6',
                                                    '#9A60B4',
                                                    '#f9c956',
                                                    '#3BA272',
                                                    '#F21755',
                                                    '#03BCFA',
                                                    '#FA4603',
                                                    '#08FA03'
                                                  ];
                                            return colorList[colors.dataIndex];
                                        }
                                    },
                                },
                                label: {
                                   fontSize:fontSize
                                },
                                radius: '40%',
                            },
                            
                        ]
                    }
                }
                
                
                //4.渲染图表
                myCharts.setOption(option);
                window.addEventListener("resize", function() {
                    myCharts.resize();
                });
            }
            }else{
                $(".div11").css("display","none");
            }


            if (res.screendata3) {
                let myCharts = echarts.init(document.getElementById('chart13'))
                if(res.screendata3.length>0 && res.screendata3[0].content!="" && res.screendata3[0].content!=undefined){
                
                    if (res.screendata3) {
                        var str="";
                        for(var i=0;i<res.screendata3.length;i++){
                            str+='<div class="jq33-content"> <div class="left"><span>'+(i+1)+"、"+res.screendata3[i].content+'</span></div></div>';
                           
                        }
                        document.getElementById('chart13').innerHTML=str;
                    }
                }else{
                var optiont=res.screendata3[0].name;
                let option=[];
                if(optiont.length<10){
                    option = {
                        grid: {
                            show: true,                                 //是否显示图表背景网格    
                            left: 5,                                    //图表距离容器左侧多少距离
                            right: 5,                                //图表距离容器右侧侧多少距离
                            bottom: isphone==1 ? 50 : 5,                              //图表距离容器上面多少距离
                            top: isphone==1 ? 80 : 40,                                         //图表距离容器下面多少距离
                            containLabel: true,                     //防止标签溢出  
                            
                        },
                        xAxis: {
                            type: 'category',
                            data:res.screendata3.map(item => item.name),
                            axisLabel: {
                                textStyle: {
                                    color: '#01C4F7', //坐标值得具体的颜色                   
                                },
                                fontSize:fontSize,
                                formatter:function(params){
                                    var newParamsName = "";// 最终拼接成的字符串
                                        var paramsNameNumber = params.length;// 实际标签的个数
                                        var provideNumber = 6;// 每行能显示的字的个数
                                        var rowNumber = Math.ceil(paramsNameNumber / provideNumber);// 换行的话，需要显示几行，向上取整
                                        // 条件等同于rowNumber>1
                                        if (paramsNameNumber > provideNumber) {
                                            
                                            for (var p = 0; p < rowNumber; p++) {
                                                var tempStr = "";// 表示每一次截取的字符串
                                                var start = p * provideNumber;// 开始截取的位置
                                                var end = start + provideNumber;// 结束截取的位置
                                                // 此处特殊处理最后一行的索引值
                                                if (p == rowNumber - 1) {
                                                    // 最后一次不换行
                                                    tempStr = params.substring(start, paramsNameNumber);
                                                } else {
                                                    // 每一次拼接字符串并换行
                                                    tempStr = params.substring(start, end) + "\n";
                                                }
                                                newParamsName += tempStr;// 最终拼成的字符串
                                            }
                            
                                        } else {
                                            // 将旧标签的值赋给新标签
                                            newParamsName = params;
                                        }
                                        //将最终的字符串返回
                                        return newParamsName
                                }
                            }
                        },
                        yAxis: {
                            type: 'value',
                            axisLabel: {
                                textStyle: {
                                    color: '#01C4F7', //坐标值得具体的颜色                   
                                },
                                fontSize:fontSize
                            }
                        },
                        series: [{
                            data: res.screendata3,
                            type: 'bar',
                            barMaxWidth:40,
                            label: {
                                show: true,
                                position: 'top',
                                formatter:function(params){
                                    return params.data.result+"%\n"+params.data.value+"人";
                                    },
                                    fontSize:fontSize
                            },
                    
                            itemStyle: {
                                opacity: 0,
                                normal:{
                                    color:function(params){
                                        var colorList = [
                                            '#E87C25','#01C4F7','#FCCE10',"#FA640D",'#CB98E0','#863BF2','#087998','#1F52FA'
                                        ];
                                        return colorList[params.dataIndex];
                                    }
                                }
                            },
                        }] 
                    }
                }else{
                    option = {
                        tooltip: {
                                trigger: 'item',
                                formatter: function (params) {
                                    return '人数：'+params.data.value+"人（"+params.data.result+"%）"
                                }
                            },
                       
                        legend: {
                            y:'top',
                                  orient: 'vertical',
                                  left: 'left',
                                  bottom:30,
                                  textStyle:{
                                    color: '#00FFFF',
                                    
                                    fontSize: fontSize,
                                  }
                                },
                        label: {
                            show: true, 
                            position:'inside',
                            formatter: function (arg) {
                                let text =arg.data.name;
                                let value_format = arg.value+'人  ';
                                let percent_format = arg.data.result + '%';
                                // if (text.length >12 && isphone==1) {
                                //     return text = `${text.slice(0, 6)}\n${text.slice(6, text.length)}\n(${value_format}${percent_format})`
                                // } else{
                                    return '\n  （'+ arg.data.value + '人  '+arg.data.result+'%）' 
                                //}
                                
                            },
                            textStyle:{
                                fontSize: fontSize,
                             }
                        },
                        series: [
                            {
                                type: 'pie',
                                data: res.screendata3,
                                center: ['50%', '60%'], //饼图位置
                                itemStyle:{
                                    labelLine: {
                                        show: false,
                                        
                                    },
                                    
                                    normal: {
                                        color: function (colors) {
                                        var colorList = [
                                                    '#fc8251',
                                                    '#5470c6',
                                                    '#9A60B4',
                                                    '#f9c956',
                                                    '#3BA272',
                                                    '#F21755',
                                                    '#03BCFA',
                                                    '#FA4603',
                                                    '#08FA03'
                                                  ];
                                            return colorList[colors.dataIndex];
                                        }
                                    },
                                },
                                label: {
                                   fontSize:fontSize
                                },
                                radius: '40%',
                            },
                            
                        ]
                    }
                }
                //4.渲染图表
                myCharts.setOption(option);
                window.addEventListener("resize", function() {
                    myCharts.resize();
                });
            }
            }else{
                $(".div13").css("display","none");
            }


            if (res.screendata2) {
                let myCharts = echarts.init(document.getElementById('chart12'))
                if(res.screendata2.length>0 && res.screendata2[0].content!="" && res.screendata2[0].content!=undefined){
                
                    if (res.screendata2) {
                        var str="";
                        for(var i=0;i<res.screendata2.length;i++){
                            str+='<div class="jq33-content"> <div class="left"><span>'+(i+1)+"、"+res.screendata2[i].content+'</span></div></div>';
                           
                        }
                        document.getElementById('chart12').innerHTML=str;
                    }
                }else{
               // 3.配置
                let option = {
                    grid: {
                        show: true,                                 //是否显示图表背景网格    
                        left: 5,                                    //图表距离容器左侧多少距离
                        right: 5,                                //图表距离容器右侧侧多少距离
                        bottom: isphone==1 ? 50 : 5,                              //图表距离容器上面多少距离
                        top: isphone==1 ? 80 : 40,                                         //图表距离容器下面多少距离
                        containLabel: true,                     //防止标签溢出  
                        
                    },
                    xAxis: {
                        type: 'category',
                        data:res.screendata2.map(item => item.name),
                        axisLabel: {
                            textStyle: {
                                color: '#01C4F7', //坐标值得具体的颜色                   
                            },
                            fontSize:fontSize,
                            formatter:function(params){
                                var newParamsName = "";// 最终拼接成的字符串
                                    var paramsNameNumber = params.length;// 实际标签的个数
                                    var provideNumber = id==24?9:7;// 每行能显示的字的个数
                                    var rowNumber = Math.ceil(paramsNameNumber / provideNumber);// 换行的话，需要显示几行，向上取整
                                    // 条件等同于rowNumber>1
                                    if (paramsNameNumber > provideNumber) {
                                        
                                        for (var p = 0; p < rowNumber; p++) {
                                            var tempStr = "";// 表示每一次截取的字符串
                                            var start = p * provideNumber;// 开始截取的位置
                                            var end = start + provideNumber;// 结束截取的位置
                                            // 此处特殊处理最后一行的索引值
                                            if (p == rowNumber - 1) {
                                                // 最后一次不换行
                                                tempStr = params.substring(start, paramsNameNumber);
                                            } else {
                                                // 每一次拼接字符串并换行
                                                tempStr = params.substring(start, end) + "\n";
                                            }
                                            newParamsName += tempStr;// 最终拼成的字符串
                                        }
                        
                                    } else {
                                        // 将旧标签的值赋给新标签
                                        newParamsName = params;
                                    }
                                    //将最终的字符串返回
                                    return newParamsName
                            }
                        }
                    },
                    yAxis: {
                        type: 'value',
                        axisLabel: {
                            textStyle: {
                                color: '#01C4F7', //坐标值得具体的颜色                   
                            },
                            fontSize:fontSize
                        }
                    },
                    series: [{
                        data: res.screendata2,
                        type: 'bar',
                        barMaxWidth:40,
                        label: {
                            show: true,
                            position: 'top',
                            formatter:function(params){
                                return params.data.result+"%\n"+params.data.value+"人";
                                },
                                fontSize:fontSize
                        },
                   
                        itemStyle: {
                            opacity: 0,
                            normal:{
                                color:function(params){
                                    var colorList = [
                                        '#E87C25','#01C4F7','#FCCE10',"#FA640D",'#CB98E0','#863BF2','#087998','#1F52FA'
                                    ];
                                    return colorList[params.dataIndex];
                                }
                            }
                        },
                    }] 
                }
                //4.渲染图表
                myCharts.setOption(option);
                window.addEventListener("resize", function() {
                    myCharts.resize();
                });
            }
            }else{
                $(".div12").css("display","none");
            }



            if (res.screendata4) {
                let myCharts = echarts.init(document.getElementById('chart14'))
                if(res.screendata4.length>0 && res.screendata4[0].content!="" && res.screendata4[0].content!=undefined){
                
                    if (res.screendata4) {
                        var str="";
                        for(var i=0;i<res.screendata4.length;i++){
                            str+='<div class="jq33-content"> <div class="left"><span>'+(i+1)+"、"+res.screendata4[i].content+'</span></div></div>';
                           
                        }
                        document.getElementById('chart14').innerHTML=str;
                    }
                }else{
               // 3.配置
                let option = {
                    grid: {
                        show: true,                                 //是否显示图表背景网格    
                        left: 5,                                    //图表距离容器左侧多少距离
                        right: 5,                                //图表距离容器右侧侧多少距离
                        bottom: isphone==1 ? 120 : 25,                              //图表距离容器上面多少距离
                        top: isphone==1 ? 80 : 40,                                         //图表距离容器下面多少距离
                        containLabel: true,                     //防止标签溢出  
                        
                    },
                    xAxis: {
                        type: 'category',
                        data:res.screendata4.map(item => item.name),
                        axisLabel: {
                            textStyle: {
                                color: '#01C4F7', //坐标值得具体的颜色                   
                            },
                            fontSize:fontSize,
                            formatter:function(params){
                                var newParamsName = "";// 最终拼接成的字符串
                                    var paramsNameNumber = params.length;// 实际标签的个数
                                    var provideNumber = 4;// 每行能显示的字的个数
                                    var rowNumber = Math.ceil(paramsNameNumber / provideNumber);// 换行的话，需要显示几行，向上取整
                                    // 条件等同于rowNumber>1
                                    if (paramsNameNumber > provideNumber) {
                                        
                                        for (var p = 0; p < rowNumber; p++) {
                                            var tempStr = "";// 表示每一次截取的字符串
                                            var start = p * provideNumber;// 开始截取的位置
                                            var end = start + provideNumber;// 结束截取的位置
                                            // 此处特殊处理最后一行的索引值
                                            if (p == rowNumber - 1) {
                                                // 最后一次不换行
                                                tempStr = params.substring(start, paramsNameNumber);
                                            } else {
                                                // 每一次拼接字符串并换行
                                                tempStr = params.substring(start, end) + "\n";
                                            }
                                            newParamsName += tempStr;// 最终拼成的字符串
                                        }
                        
                                    } else {
                                        // 将旧标签的值赋给新标签
                                        newParamsName = params;
                                    }
                                    //将最终的字符串返回
                                    return newParamsName
                            }
                        }
                    },
                    yAxis: {
                        type: 'value',
                        axisLabel: {
                            textStyle: {
                                color: '#01C4F7', //坐标值得具体的颜色                   
                            },
                            fontSize:fontSize
                        }
                    },
                    series: [{
                        data: res.screendata4,
                        type: 'bar',
                        barMaxWidth:40,
                        label: {
                            show: true,
                            position: 'top',
                            formatter:function(params){
                                return params.data.result+"%\n"+params.data.value+"人";
                                },
                                fontSize:fontSize
                        },
                   
                        itemStyle: {
                            opacity: 0,
                            normal:{
                                color:function(params){
                                    var colorList = [
                                        '#E87C25','#01C4F7','#FCCE10',"#FA640D",'#CB98E0','#863BF2','#087998','#1F52FA'
                                    ];
                                    return colorList[params.dataIndex];
                                }
                            }
                        },
                    }] 
                }
                //4.渲染图表
                myCharts.setOption(option);
                window.addEventListener("resize", function() {
                    myCharts.resize();
                });
            }
            }else{
                $(".div14").css("display","none");
            }


            if (res.screendata5) {
                let myCharts = echarts.init(document.getElementById('chart15'))
                if(res.screendata5.length>0 && res.screendata5[0].content!="" && res.screendata5[0].content!=undefined){
                
                    if (res.screendata5) {
                        var str="";
                        for(var i=0;i<res.screendata5.length;i++){
                            str+='<div class="jq33-content"> <div class="left"><span>'+(i+1)+"、"+res.screendata5[i].content+'</span></div></div>';
                           
                        }
                        document.getElementById('chart15').innerHTML=str;
                    }
                }else{
               // 3.配置
                let option = {
                    grid: {
                        show: true,                                 //是否显示图表背景网格    
                        left: 5,                                    //图表距离容器左侧多少距离
                        right: 5,                                //图表距离容器右侧侧多少距离
                        bottom: isphone==1 ? 50 : 5,                              //图表距离容器上面多少距离
                        top: isphone==1 ? 80 : 40,                                         //图表距离容器下面多少距离
                        containLabel: true,                     //防止标签溢出  
                        
                    },
                    xAxis: {
                        type: 'category',
                        data:res.screendata5.map(item => item.name),
                        axisLabel: {
                            textStyle: {
                                color: '#01C4F7', //坐标值得具体的颜色                   
                            },
                            fontSize:fontSize,
                            formatter:function(params){
                                var newParamsName = "";// 最终拼接成的字符串
                                    var paramsNameNumber = params.length;// 实际标签的个数
                                    var provideNumber = 4;// 每行能显示的字的个数
                                    var rowNumber = Math.ceil(paramsNameNumber / provideNumber);// 换行的话，需要显示几行，向上取整
                                    // 条件等同于rowNumber>1
                                    if (paramsNameNumber > provideNumber) {
                                        
                                        for (var p = 0; p < rowNumber; p++) {
                                            var tempStr = "";// 表示每一次截取的字符串
                                            var start = p * provideNumber;// 开始截取的位置
                                            var end = start + provideNumber;// 结束截取的位置
                                            // 此处特殊处理最后一行的索引值
                                            if (p == rowNumber - 1) {
                                                // 最后一次不换行
                                                tempStr = params.substring(start, paramsNameNumber);
                                            } else {
                                                // 每一次拼接字符串并换行
                                                tempStr = params.substring(start, end) + "\n";
                                            }
                                            newParamsName += tempStr;// 最终拼成的字符串
                                        }
                        
                                    } else {
                                        // 将旧标签的值赋给新标签
                                        newParamsName = params;
                                    }
                                    //将最终的字符串返回
                                    return newParamsName
                            }
                        }
                    },
                    yAxis: {
                        type: 'value',
                        axisLabel: {
                            textStyle: {
                                color: '#01C4F7', //坐标值得具体的颜色                   
                            },
                            fontSize:fontSize
                        }
                    },
                    series: [{
                        data: res.screendata5,
                        type: 'bar',
                        barMaxWidth:40,
                        label: {
                            show: true,
                            position: 'top',
                            formatter:function(params){
                                return params.data.result+"%\n"+params.data.value+"人";
                                },
                                fontSize:fontSize
                        },
                   
                        itemStyle: {
                            opacity: 0,
                            normal:{
                                color:function(params){
                                    var colorList = [
                                        '#E87C25','#01C4F7','#FCCE10',"#FA640D",'#CB98E0','#863BF2','#087998','#1F52FA'
                                    ];
                                    return colorList[params.dataIndex];
                                }
                            }
                        },
                    }] 
                }
                //4.渲染图表
                myCharts.setOption(option);
                window.addEventListener("resize", function() {
                    myCharts.resize();
                });
            }
            }else{
                $(".div15").css("display","none");
            }


            if (res.screendata6) {
                let myCharts = echarts.init(document.getElementById('chart16'))
                if(res.screendata6.length>0 && res.screendata6[0].content!="" && res.screendata6[0].content!=undefined){
                    if (res.screendata6) {
                        var str="";
                        for(var i=0;i<res.screendata6.length;i++){
                            str+='<div class="jq33-content"> <div class="left"><span>'+(i+1)+"、"+res.screendata6[i].content+'</span></div></div>';
                           
                        }
                        document.getElementById('chart16').innerHTML=str;
                    }
                }else{
                var optiont=res.screendata6[0].name;
                let option=[];
                if(optiont.length<10){
                // 3.配置
                    option = {
                        grid: {
                            show: true,                                 //是否显示图表背景网格    
                            left: 5,                                    //图表距离容器左侧多少距离
                            right: 5,                                //图表距离容器右侧侧多少距离
                            bottom: isphone==1 ? 50 : 5,                              //图表距离容器上面多少距离
                            top: isphone==1 ? 80 : 40,                                         //图表距离容器下面多少距离
                            containLabel: true,                     //防止标签溢出  
                            
                        },
                        xAxis: {
                            type: 'category',
                            data:res.screendata6.map(item => item.name),
                            axisLabel: {
                                textStyle: {
                                    color: '#01C4F7', //坐标值得具体的颜色                   
                                },
                                fontSize:fontSize,
                                formatter:function(params){
                                    var newParamsName = "";// 最终拼接成的字符串
                                        var paramsNameNumber = params.length;// 实际标签的个数
                                        var provideNumber = 4;// 每行能显示的字的个数
                                        var rowNumber = Math.ceil(paramsNameNumber / provideNumber);// 换行的话，需要显示几行，向上取整
                                        // 条件等同于rowNumber>1
                                        if (paramsNameNumber > provideNumber) {
                                            
                                            for (var p = 0; p < rowNumber; p++) {
                                                var tempStr = "";// 表示每一次截取的字符串
                                                var start = p * provideNumber;// 开始截取的位置
                                                var end = start + provideNumber;// 结束截取的位置
                                                // 此处特殊处理最后一行的索引值
                                                if (p == rowNumber - 1) {
                                                    // 最后一次不换行
                                                    tempStr = params.substring(start, paramsNameNumber);
                                                } else {
                                                    // 每一次拼接字符串并换行
                                                    tempStr = params.substring(start, end) + "\n";
                                                }
                                                newParamsName += tempStr;// 最终拼成的字符串
                                            }
                            
                                        } else {
                                            // 将旧标签的值赋给新标签
                                            newParamsName = params;
                                        }
                                        //将最终的字符串返回
                                        return newParamsName
                                }
                            }
                        },
                        yAxis: {
                            type: 'value',
                            axisLabel: {
                                textStyle: {
                                    color: '#01C4F7', //坐标值得具体的颜色                   
                                },
                                fontSize:fontSize
                            }
                        },
                        series: [{
                            data: res.screendata6,
                            type: 'bar',
                            barMaxWidth:40,
                            label: {
                                show: true,
                                position: 'top',
                                formatter:function(params){
                                    return params.data.result+"%\n"+params.data.value+"人";
                                    },
                                    fontSize:fontSize
                            },
                    
                            itemStyle: {
                                opacity: 0,
                                normal:{
                                    color:function(params){
                                        var colorList = [
                                            '#E87C25','#01C4F7','#FCCE10',"#FA640D",'#CB98E0','#863BF2','#087998','#1F52FA'
                                        ];
                                        return colorList[params.dataIndex];
                                    }
                                }
                            },
                        }] 
                    }
                }else{
                    option = {
                        tooltip: {
                                trigger: 'item',
                                formatter: function (params) {
                                    return '人数：'+params.data.value+"人（"+params.data.result+"%）"
                                }
                            },
                       
                        legend: {
                            y:'top',
                                  orient: 'vertical',
                                  left: 'left',
                                  bottom:30,
                                  textStyle:{
                                    color: '#00FFFF',
                                    
                                    fontSize: fontSize,
                                  }
                                },
                        label: {
                            show: true, 
                            position:'inside',
                            formatter: function (arg) {
                                let text =arg.data.name;
                                let value_format = arg.value+'人  ';
                                let percent_format = arg.data.result + '%';
                                // if (text.length >12 && isphone==1) {
                                //     return text = `${text.slice(0, 6)}\n${text.slice(6, text.length)}\n(${value_format}${percent_format})`
                                // } else{
                                    return '\n  （'+ arg.data.value + '人  '+arg.data.result+'%）' 
                                //}
                                
                            },
                            textStyle:{
                                fontSize: fontSize,
                             }
                        },
                        series: [
                            {
                                type: 'pie',
                                data: res.screendata6,
                                center: ['50%', '60%'], //饼图位置
                                itemStyle:{
                                    labelLine: {
                                        show: false,
                                        
                                    },
                                    
                                    normal: {
                                        color: function (colors) {
                                        var colorList = [
                                                    '#fc8251',
                                                    '#5470c6',
                                                    '#9A60B4',
                                                    '#f9c956',
                                                    '#3BA272',
                                                    '#F21755',
                                                    '#03BCFA',
                                                    '#FA4603',
                                                    '#08FA03'
                                                  ];
                                            return colorList[colors.dataIndex];
                                        }
                                    },
                                },
                                label: {
                                   fontSize:fontSize
                                },
                                radius: '40%',
                            },
                            
                        ]
                    }
                }
                //4.渲染图表
                myCharts.setOption(option);
                window.addEventListener("resize", function() {
                    myCharts.resize();
                });
            }
            }else{
                $(".div16").css("display","none");
            }


            if (res.screendata7) {
                let myCharts = echarts.init(document.getElementById('chart17'))
                if(res.screendata7.length>0 && res.screendata7[0].content!="" && res.screendata7[0].content!=undefined){
                    if (res.screendata7) {
                        var str="";
                        for(var i=0;i<res.screendata7.length;i++){
                            str+='<div class="jq33-content"> <div class="left"><span>'+(i+1)+"、"+res.screendata7[i].content+'</span></div></div>';
                           
                        }
                        document.getElementById('chart17').innerHTML=str;
                    }
                }else{
               // 3.配置
                let option = {
                    grid: {
                        show: true,                                 //是否显示图表背景网格    
                        left: 5,                                    //图表距离容器左侧多少距离
                        right: 5,                                //图表距离容器右侧侧多少距离
                        bottom: isphone==1 ? 150 : 5,                              //图表距离容器上面多少距离
                        top: isphone==1 ? 80 : 40,                                         //图表距离容器下面多少距离
                        containLabel: true,                     //防止标签溢出  
                        
                    },
                    xAxis: {
                        type: 'category',
                        data:res.screendata7.map(item => item.name),
                        axisLabel: {
                            textStyle: {
                                color: '#01C4F7', //坐标值得具体的颜色                   
                            },
                            fontSize:fontSize,
                            formatter:function(params){
                                var newParamsName = "";// 最终拼接成的字符串
                                    var paramsNameNumber = params.length;// 实际标签的个数
                                    var provideNumber = id==23?9:3;// 每行能显示的字的个数
                                    var rowNumber = Math.ceil(paramsNameNumber / provideNumber);// 换行的话，需要显示几行，向上取整
                                    // 条件等同于rowNumber>1
                                    if (paramsNameNumber > provideNumber) {
                                        
                                        for (var p = 0; p < rowNumber; p++) {
                                            var tempStr = "";// 表示每一次截取的字符串
                                            var start = p * provideNumber;// 开始截取的位置
                                            var end = start + provideNumber;// 结束截取的位置
                                            // 此处特殊处理最后一行的索引值
                                            if (p == rowNumber - 1) {
                                                // 最后一次不换行
                                                tempStr = params.substring(start, paramsNameNumber);
                                            } else {
                                                // 每一次拼接字符串并换行
                                                tempStr = params.substring(start, end) + "\n";
                                            }
                                            newParamsName += tempStr;// 最终拼成的字符串
                                        }
                        
                                    } else {
                                        // 将旧标签的值赋给新标签
                                        newParamsName = params;
                                    }
                                    //将最终的字符串返回
                                    return newParamsName
                            }
                        }
                    },
                    yAxis: {
                        type: 'value',
                        axisLabel: {
                            textStyle: {
                                color: '#01C4F7', //坐标值得具体的颜色                   
                            },
                            fontSize:fontSize
                        }
                    },
                    series: [{
                        data: res.screendata7,
                        type: 'bar',
                        barMaxWidth:40,
                        label: {
                            show: true,
                            position: 'top',
                            formatter:function(params){
                                return params.data.result+"%\n"+params.data.value+"人";
                                },
                                fontSize:fontSize
                        },
                   
                        itemStyle: {
                            opacity: 0,
                            normal:{
                                color:function(params){
                                    var colorList = [
                                        '#E87C25','#01C4F7','#FCCE10',"#FA640D",'#CB98E0','#863BF2','#087998','#1F52FA'
                                    ];
                                    return colorList[params.dataIndex];
                                }
                            }
                        },
                    }] 
                }
                //4.渲染图表
                myCharts.setOption(option);
                window.addEventListener("resize", function() {
                    myCharts.resize();
                });
            }
            }else{
                $(".div17").css("display","none");
            }


            if (res.screendata8) {
                let myCharts = echarts.init(document.getElementById('chart18'))
                if(res.screendata8.length>0 && res.screendata8[0].content!="" && res.screendata8[0].content!=undefined){
                    if (res.screendata8) {
                        var str="";
                        for(var i=0;i<res.screendata8.length;i++){
                            str+='<div class="jq33-content"> <div class="left"><span>'+(i+1)+"、"+res.screendata8[i].content+'</span></div></div>';
                           
                        }
                        document.getElementById('chart18').innerHTML=str;
                    }
                }else{
               // 3.配置
                let option = {
                    grid: {
                        show: true,                                 //是否显示图表背景网格    
                        left: 5,                                    //图表距离容器左侧多少距离
                        right: 5,                                //图表距离容器右侧侧多少距离
                        bottom: isphone==1 ? 50 : 5,                              //图表距离容器上面多少距离
                        top: isphone==1 ? 80 : 40,                                         //图表距离容器下面多少距离
                        containLabel: true,                     //防止标签溢出  
                        
                    },
                    xAxis: {
                        type: 'category',
                        data:res.screendata8.map(item => item.name),
                        axisLabel: {
                            textStyle: {
                                color: '#01C4F7', //坐标值得具体的颜色                   
                            },
                            fontSize:fontSize,
                            formatter:function(params){
                                var newParamsName = "";// 最终拼接成的字符串
                                    var paramsNameNumber = params.length;// 实际标签的个数
                                    var provideNumber = 6;// 每行能显示的字的个数
                                    var rowNumber = Math.ceil(paramsNameNumber / provideNumber);// 换行的话，需要显示几行，向上取整
                                    // 条件等同于rowNumber>1
                                    if (paramsNameNumber > provideNumber) {
                                        
                                        for (var p = 0; p < rowNumber; p++) {
                                            var tempStr = "";// 表示每一次截取的字符串
                                            var start = p * provideNumber;// 开始截取的位置
                                            var end = start + provideNumber;// 结束截取的位置
                                            // 此处特殊处理最后一行的索引值
                                            if (p == rowNumber - 1) {
                                                // 最后一次不换行
                                                tempStr = params.substring(start, paramsNameNumber);
                                            } else {
                                                // 每一次拼接字符串并换行
                                                tempStr = params.substring(start, end) + "\n";
                                            }
                                            newParamsName += tempStr;// 最终拼成的字符串
                                        }
                        
                                    } else {
                                        // 将旧标签的值赋给新标签
                                        newParamsName = params;
                                    }
                                    //将最终的字符串返回
                                    return newParamsName
                            }
                        }
                    },
                    yAxis: {
                        type: 'value',
                        axisLabel: {
                            textStyle: {
                                color: '#01C4F7', //坐标值得具体的颜色                   
                            },
                            fontSize:fontSize
                        }
                    },
                    series: [{
                        data: res.screendata8,
                        type: 'bar',
                        barMaxWidth:40,
                        label: {
                            show: true,
                            position: 'top',
                            formatter:function(params){
                                return params.data.result+"%\n"+params.data.value+"人";
                                },
                                fontSize:fontSize
                        },
                   
                        itemStyle: {
                            opacity: 0,
                            normal:{
                                color:function(params){
                                    var colorList = [
                                        '#E87C25','#01C4F7','#FCCE10',"#FA640D",'#CB98E0','#863BF2','#087998','#1F52FA'
                                    ];
                                    return colorList[params.dataIndex];
                                }
                            }
                        },
                    }] 
                }
                //4.渲染图表
                myCharts.setOption(option);
                window.addEventListener("resize", function() {
                    myCharts.resize();
                });
            }
            }else{
                $(".div18").css("display","none");
            }

        });





    }



function initChart2(){
    $('#chart1Loading').hide();
    $('#chart2Loading').hide();
    $('#chart3Loading').hide();
    $('#chart4Loading').hide();
    $('#chart28Loading').hide();
    $('#chart29Loading').hide();
    $('#chart6Loading').hide();
    $('#chart11Loading').hide();
    $('#chart12Loading').hide();
    $('#chart13Loading').hide();
    $('#chart14Loading').hide();
    $('#chart15Loading').hide();
    $('#chart16Loading').hide();
    $('#chart17Loading').hide();
    $('#chart18Loading').hide();
    $('#chart19Loading').hide();
    $('#chart8Loading').show();
    $('#chart9Loading').show();
   // $('#chart108Loading').show();
    axios.post("meeting.php?action=getMeetingSurveyScreenData&type=3&mtid="+mtid+"&id="+id+"&isphone="+isphone)
    .then((res) => 
    {
        $('#surveytitle').html(res.title);
        //document.getElementById('li3').innerHTML = res.num1+"人";
        //document.getElementById('li4').innerHTML = res.num2+"人";
        $('#chart8title').html(res.screentitle1);
        $('#chart9title').html(res.screentitle2);
        //$('#chart10title').html(res.screentitle3);
        $('#chart8Loading').hide();
        //$('#chart10Loading').hide();
        $('#chart9Loading').hide();

        if (res.screendata1) {

            if (id==78) {
                $('#chart9title').html(res.screentitle1);
                $('#div8').css("display","none");
                $('#div9').css("display","block");
                $('#fl2').css("width","98%");

                if(isphone==1){
                    $('#div9').css("height","120rem");
                }
                
                var str="";
                for(var i=0;i<res.screendata1.length;i++){
                    str+='<div class="jq22-content"> <div class="left"><span>'+res.screendata1[i].name+'</span>&nbsp;&nbsp;&nbsp; </div><div class="right"><div id="progressbarsee'+i+'" class="progressbar"></div>  <span class="span">&nbsp;'+res.screendata1[i].result+'%，'+res.screendata1[i].value+'人</span></div></div>';
                }
                document.getElementById('chart9').innerHTML=str;
                for(var i=0;i<res.screendata1.length;i++){
                    $('#progressbarsee'+i).LineProgressbar({
                        percentage: res.screendata1[i].result,height: '3px',radius: '3px'
                    });
                }  
            }else{
             let myCharts = echarts.init(document.getElementById('chart8'))
             //3.配置
             let option = {
                grid: {
                    show: true,                                 //是否显示图表背景网格    
                    left: 5,                                    //图表距离容器左侧多少距离
                    right: 5,                                //图表距离容器右侧侧多少距离
                    bottom: isphone==1 ? 50 : 5,                              //图表距离容器上面多少距离
                    top: isphone==1 ? 80 : 40,                                         //图表距离容器下面多少距离
                    containLabel: true,                     //防止标签溢出  
                   
                },
                xAxis: {
                    type: 'category',
                    data:res.screendata1.map(item => item.name),
                    axisLabel: {
                        textStyle: {
                            color: '#01C4F7', //坐标值得具体的颜色                   
                        },
                        fontSize:fontSize,
                        formatter:function(params){
                            var newParamsName = "";// 最终拼接成的字符串
                               var paramsNameNumber = params.length;// 实际标签的个数
                               var provideNumber = 4;// 每行能显示的字的个数
                               var rowNumber = Math.ceil(paramsNameNumber / provideNumber);// 换行的话，需要显示几行，向上取整
                               var screenWidth = document.documentElement.clientWidth;
                                if(screenWidth<1260){
                                    provideNumber=4
                                }else{
                                    provideNumber=6
                                }
                           
                               // 条件等同于rowNumber>1
                               if (paramsNameNumber > provideNumber) {
                                 
                                  for (var p = 0; p < rowNumber; p++) {
                                      var tempStr = "";// 表示每一次截取的字符串
                                      var start = p * provideNumber;// 开始截取的位置
                                      var end = start + provideNumber;// 结束截取的位置
                                      // 此处特殊处理最后一行的索引值
                                      if (p == rowNumber - 1) {
                                         // 最后一次不换行
                                         tempStr = params.substring(start, paramsNameNumber);
                                      } else {
                                         // 每一次拼接字符串并换行
                                         tempStr = params.substring(start, end) + "\n";
                                      }
                                     newParamsName += tempStr;// 最终拼成的字符串
                                  }
                 
                               } else {
                                  // 将旧标签的值赋给新标签
                                  newParamsName = params;
                               }
                               //将最终的字符串返回
                               return newParamsName
                        }
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLabel: {
                        textStyle: {
                            color: '#01C4F7', //坐标值得具体的颜色                   
                        },
                        fontSize:fontSize
                    }
                },
                series: [{
                    data: res.screendata1,
                    type: 'bar',
                    barMaxWidth:40,
                    label: {
                        show: true,
                        position: 'top',
                        formatter:function(params){
                            return params.data.result+"%\n"+params.data.value+"人";
                         },
                         fontSize:fontSize
                    },
                
                    itemStyle: {
                        opacity: 0,
                        normal:{
                            color:function(params){
                                var colorList = [
                                    '#E87C25','#01C4F7','#FCCE10',"#FA640D",'#CB98E0','#863BF2','#087998','#1F52FA'
                                ];
                                return colorList[params.dataIndex];
                            }
                        }
                    },
                }] 
            }
                //4.渲染图表
                myCharts.setOption(option);
                window.addEventListener("resize", function() {
                    myCharts.resize();
                });
            }
        } else{
            $("#div8").css("display","none");
        }
        
        if (res.screendata2) {
            var str="";
            for(var i=0;i<res.screendata2.length;i++){
                str+='<div class="jq22-content"> <div class="left"><span>'+res.screendata2[i].name+'</span>&nbsp;&nbsp;&nbsp; </div><div class="right"><div id="progressbars'+i+'" class="progressbar"></div>  <span class="span">&nbsp;'+res.screendata2[i].result+'%，'+res.screendata2[i].value+'人</span></div></div>';
               
            }
            document.getElementById('chart9').innerHTML=str;
            for(var i=0;i<res.screendata2.length;i++){
                $('#progressbars'+i).LineProgressbar({
                    percentage: res.screendata2[i].result,height: '3px',radius: '3px'
                });
            }
           
        }else{
            if (id!=78) {
                $("#div9").css("display","none");
            }
           
        }

        if (res.screendata3 && ((mtid==407 && id==56) || mtid==427 || mtid==441) ) {
            $('#chart10title').html(res.screentitle3);
            $('#chart10Loading').hide();

            if(isphone!=1){
                if(mtid==427 || mtid==441){
                    $("#div9").css("height","50%");
                    $("#div10").css("height","50%");
                }else{
                    $("#div9").css("height","36%");
                    $("#div10").css("height","63%");
                }
            }
            
            if(id==79 || id==82 || id==83){
                let myCharts = echarts.init(document.getElementById('chart10'))
                //3.配置
                let option = {
                   grid: {
                       show: true,                                 //是否显示图表背景网格    
                       left: 5,                                    //图表距离容器左侧多少距离
                       right: 5,                                //图表距离容器右侧侧多少距离
                       bottom: isphone==1 ? 50 : 5,                              //图表距离容器上面多少距离
                       top: isphone==1 ? 80 : 40,                                         //图表距离容器下面多少距离
                       containLabel: true,                     //防止标签溢出  

                   },
                   xAxis: {
                       type: 'category',
                       data:res.screendata3.map(item => item.name),
                       axisLabel: {
                           textStyle: {
                               color: '#01C4F7', //坐标值得具体的颜色                   
                           },
                           fontSize:fontSize,
                           formatter:function(params){
                               var newParamsName = "";// 最终拼接成的字符串
                                  var paramsNameNumber = params.length;// 实际标签的个数
                                  var provideNumber = 4;// 每行能显示的字的个数
                                  var rowNumber = Math.ceil(paramsNameNumber / provideNumber);// 换行的话，需要显示几行，向上取整
                                  var screenWidth = document.documentElement.clientWidth;
                                   if(screenWidth<1260){
                                       provideNumber=4
                                   }else{
                                       provideNumber=6
                                   }
                              
                                  // 条件等同于rowNumber>1
                                  if (paramsNameNumber > provideNumber) {
                                    
                                     for (var p = 0; p < rowNumber; p++) {
                                         var tempStr = "";// 表示每一次截取的字符串
                                         var start = p * provideNumber;// 开始截取的位置
                                         var end = start + provideNumber;// 结束截取的位置
                                         // 此处特殊处理最后一行的索引值
                                         if (p == rowNumber - 1) {
                                            // 最后一次不换行
                                            tempStr = params.substring(start, paramsNameNumber);
                                         } else {
                                            // 每一次拼接字符串并换行
                                            tempStr = params.substring(start, end) + "\n";
                                         }
                                        newParamsName += tempStr;// 最终拼成的字符串
                                     }
                    
                                  } else {
                                     // 将旧标签的值赋给新标签
                                     newParamsName = params;
                                  }
                                  //将最终的字符串返回
                                  return newParamsName
                           }
                       }
                   },
                   yAxis: {
                       type: 'value',
                       axisLabel: {
                           textStyle: {
                               color: '#01C4F7', //坐标值得具体的颜色                   
                           },
                           fontSize:fontSize
                       }
                   },
                   series: [{
                       data: res.screendata3,
                       type: 'bar',
                       barMaxWidth:40,
                       label: {
                           show: true,
                           position: 'top',
                           formatter:function(params){
                               return params.data.result+"%\n"+params.data.value+"人";
                            },
                            fontSize:fontSize
                       },
                   
                       itemStyle: {
                           opacity: 0,
                           normal:{
                               color:function(params){
                                   var colorList = [
                                       '#E87C25','#01C4F7','#FCCE10',"#FA640D",'#CB98E0','#863BF2','#087998','#1F52FA'
                                   ];
                                   return colorList[params.dataIndex];
                               }
                           }
                       },
                   }] 
               }
                   //4.渲染图表
                   myCharts.setOption(option);
                   window.addEventListener("resize", function() {
                       myCharts.resize();
                   });
            }else{
                var str="";
                for(var i=0;i<res.screendata3.length;i++){
                    str+='<div class="jq22-content"> <div class="left"><span>'+res.screendata3[i].name+'</span>&nbsp;&nbsp;&nbsp; </div><div class="right"><div id="progressbarse'+i+'" class="progressbar"></div>  <span class="span">&nbsp;'+res.screendata3[i].result+'%，'+res.screendata3[i].value+'人</span></div></div>';
                }
                document.getElementById('chart10').innerHTML=str;
                for(var i=0;i<res.screendata3.length;i++){
                    $('#progressbarse'+i).LineProgressbar({
                        percentage: res.screendata3[i].result,height: '3px',radius: '3px'
                    });
                }
            }
        }else{
            $("#div10").css("display","none");
        }

        if (res.screendata4 && id==81) {
            $('#chart100title').html(res.screentitle4);
            $('#chart100Loading').hide();

            if(isphone!=1){
                $("#div9").css("height","32%");
                $("#div10").css("height","32%");
                $("#div100").css("height","32%");
            }
            
            
    
            var str="";
            for(var i=0;i<res.screendata4.length;i++){
                str+='<div class="jq22-content"> <div class="left"><span>'+res.screendata4[i].name+'</span>&nbsp;&nbsp;&nbsp; </div><div class="right"><div id="progressbarsee'+i+'" class="progressbar"></div>  <span class="span">&nbsp;'+res.screendata4[i].result+'%，'+res.screendata4[i].value+'人</span></div></div>';
            }
            document.getElementById('chart100').innerHTML=str;
            for(var i=0;i<res.screendata4.length;i++){
                $('#progressbarsee'+i).LineProgressbar({
                    percentage: res.screendata4[i].result,height: '3px',radius: '3px'
                });
            }
            
        }else{
            $("#div100").css("display","none");
        }
    });

    
}
 
function initChart1()
{
    $('#chart1Loading').show();
    $('#chart2Loading').show();
    $('#chart3Loading').show();
    $('#chart4Loading').show();
    $('#chart28Loading').show();
    $('#chart29Loading').show();
    $('#chart6Loading').show();
    axios.post("meeting.php?action=getMeetingSurveyScreenData&type=1&mtid="+mtid+"&id="+id+"&isphone="+isphone )
    .then((res) => 
    {
        console.log(res);
       $('#surveytitle').html(res.title);
        //document.getElementById('li1').innerHTML = res.num1+"人";
        //document.getElementById('li2').innerHTML = res.num2+"人";
        $('#chart1title').html(res.screentitle1);
        $('#chart2title').html(res.screentitle4);
        $('#chart3title').html(res.screentitle3);
        $('#chart4title').html(res.screentitle5);
        $('#chart6title').html(res.screentitle2);
        $('#chart1Loading').hide();
        $('#chart2Loading').hide();
        $('#chart3Loading').hide();
        $('#chart4Loading').hide();
        $('#chart6Loading').hide();
        $('#chart28Loading').hide();
        $('#chart29Loading').hide();
        var myChart = echarts.init(document.getElementById('chart1'));
        var myChart2 = echarts.init(document.getElementById('chart2'));
        var myChart3 = echarts.init(document.getElementById('chart3'));
        var myChart4 = echarts.init(document.getElementById('chart4'));
        var legend = [];
        var series = [];
        if (res.screendata1) {
            let myCharts = echarts.init(document.getElementById('chart1'))
            //3.配置
            let option = {
               grid: {
                   show: true,                                 //是否显示图表背景网格    
                   left: 5,                                    //图表距离容器左侧多少距离
                   right: 5,                                //图表距离容器右侧侧多少距离
                   bottom: isphone==1 ? 70 : (mtid==413 ? 10:20),                              //图表距离容器上面多少距离
                   top: isphone==1 ? 40 : (mtid==413 ? 35:35),                                         //图表距离容器下面多少距离
                   containLabel: true,                     //防止标签溢出  
                  
               },
               xAxis: {
                   type: 'category',
                   
                   data:res.screendata1.map(item => item.name),
                   axisLabel: {
                       textStyle: {
                           color: '#01C4F7', //坐标值得具体的颜色                   
                       },
                       fontSize: fontSize, //设置坐标轴文本标签的字体大小
                       formatter:function(params){
                           var newParamsName = "";// 最终拼接成的字符串
                              var paramsNameNumber = params.length;// 实际标签的个数
                              var provideNumber = ((isphone==1&&res.screendata1.length>4) || res.screendata1.length>5 )&& mtid!=397&& mtid!=396 && mtid!=400 && mtid!=401 && mtid!=409 ? 2 : 6;// 每行能显示的字的个数
                                if(mtid==399){
                                    provideNumber=7;
                                }
                                if(id==54 || id==55){
                                    provideNumber=6;
                                }
                                
                                var screenWidth = document.documentElement.clientWidth;
                                if(mtid==427){
                                    if(screenWidth>1236){
                                        provideNumber=9;
                                     }else{
                                        provideNumber=6;
                                     }
                                }
                                if(mtid==441){
                                    if(screenWidth>1669){
                                        provideNumber=9;
                                     }else{
                                        provideNumber=5;
                                     }
                                }
                                if(mtid==435 || id==82 || id==83){
                                    if(screenWidth>1576){
                                        provideNumber=3;
                                     }else{
                                        provideNumber=2;
                                     }
                                }
                                if(id==84){
                                    provideNumber = getChartNum(res.screendata1.length);
                                }
                                var rowNumber = Math.ceil(paramsNameNumber / provideNumber);// 换行的话，需要显示几行，向上取整
                              
                                if(mtid==435 || mtid==440){
                                    // 条件等同于rowNumber>1
                                    if (paramsNameNumber > provideNumber) {
                                                                            
                                        for (var p = 0; p < rowNumber; p++) {
                                            var tempStr = "";// 表示每一次截取的字符串
                                            var start = p * provideNumber;// 开始截取的位置
                                            var end = start + provideNumber;// 结束截取的位置
                                            // 此处特殊处理最后一行的索引值
                                            if (p == rowNumber - 1) {
                                                // 最后一次不换行
                                                tempStr = params.substring(start, paramsNameNumber);
                                            } else {
                                                // 每一次拼接字符串并换行
                                                tempStr = params.substring(start, end) + "\n";
                                            }
                                            
                                            newParamsName += tempStr;// 最终拼成的字符串
                                        }

                                    } else {
                                        // 将旧标签的值赋给新标签
                                        newParamsName = params;
                                    }
                                }else{
                                    // 条件等同于rowNumber>1
                                    if (paramsNameNumber > provideNumber) {
                                                                    
                                        for (var p = 0; p < rowNumber; p++) {
                                            var tempStr = "";// 表示每一次截取的字符串
                                            var start = p * provideNumber;// 开始截取的位置
                                            if(screenWidth<1800){
                                                if(p==2 && (id==54 || id==55) ){
                                                    start=start-1;
                                                }
                                                if(paramsNameNumber<=11  && (id==54 || id==55) && p==2){
                                                    start=start-2;
                                                }
                                            }
                                            
                                            var end = start + provideNumber;// 结束截取的位置
                                            if(screenWidth<1800){
                                                if(p==1 && (id==54 || id==55)){
                                                    end=end-1;
                                                }
                                                if(paramsNameNumber<=11  &&  (id==54 || id==55)&& p==1){
                                                    end=end-2;
                                                }
                                            }
                                            // 此处特殊处理最后一行的索引值
                                            if (p == rowNumber - 1 ) {
                                                // 最后一次不换行
                                                tempStr = params.substring(start, paramsNameNumber);
                                            } else {
                                                // 每一次拼接字符串并换行
                                                if(rowNumber==3 && p==1 && screenWidth>1222 && mtid!=413&& mtid!=407&& mtid!=427){
                                                    tempStr = params.substring(start, end);
                                                }else if(rowNumber==3 && p==1 && screenWidth>=1800 &&  (id==54 || id==55)){
                                                    tempStr = params.substring(start, end);
                                                }else{
                                                    tempStr = params.substring(start, end) + "\n";
                                                }
                                            }
                                            newParamsName += tempStr;// 最终拼成的字符串
                                        }

                                    } else {
                                    // 将旧标签的值赋给新标签
                                    newParamsName = params;
                                    }
                                }
                               
                              //将最终的字符串返回
                              return newParamsName
                       }
                   }
               },
               yAxis: {
                   type: 'value',
                   axisLabel: {
                       textStyle: {
                           color: '#01C4F7', //坐标值得具体的颜色                   
                       },
                       fontSize: fontSize,
                   }
               },
               series: [{
                   data: res.screendata1,
                   type: 'bar',
                   barMaxWidth:40,
                   label: {
                       show: true,
                       position: 'top',
                       formatter:function(params){
                           if(res.screendata1.length>5){
                               return params.data.result+"%\n"+params.data.value+"人";
                           }else{
                               return params.data.result+"%，"+params.data.value+"人";
                           }
                        },
                        fontSize:fontSize
                   },
               
                   itemStyle: {
                       opacity: 0,
                       normal:{
                           color:function(params){
                               var colorList = [
                                   '#E87C25','#01C4F7','#FCCE10',"#FA640D",'#CB98E0','#863BF2','#087998','#1F52FA'
                               ];
                               return colorList[params.dataIndex];
                           }
                       }
                   },
               }] 
           }
            //4.渲染图表
            myCharts.setOption(option);
            window.addEventListener("resize", function() {
                myCharts.resize();
            });
        }
       //   else {
       //      var option1 = getOptionDay_xbnew([], [], [], "bug", myChart,istry);
       //      myCharts.setOption(option1, true);
       //  }


        if (res.screendata3) {
            let myCharts = echarts.init(document.getElementById('chart3'))
            let option = {
                grid: {
                    show: true,                                 //是否显示图表背景网格    
                    left: 5,                                    //图表距离容器左侧多少距离
                    right: 5,                                //图表距离容器右侧侧多少距离
                    bottom: isphone==1 ? 50 : ( (id==54 || id==55 || mtid==434)?25:5),                              //图表距离容器上面多少距离
                    top: isphone==1||mtid==399||mtid==415 ? 40 :30,                         //图表距离容器下面多少距离
                    containLabel: true,                     //防止标签溢出  
                   
                },
                xAxis: {
                    type: 'category',
                    data:res.screendata3.map(item => item.name),
                    axisLabel: {
                        textStyle: {
                            color: '#01C4F7', //坐标值得具体的颜色                   
                        },
                        fontSize: fontSize,
                        formatter:function(params){
                            var newParamsName = "";// 最终拼接成的字符串
                            var paramsNameNumber = params.length;// 实际标签的个数
                            var provideNumber = mtid!=397&&mtid!=409&&res.screendata3.length>5?2:5;// 每行能显示的字的个数
                            // console.log(params);
                            provideNumber=getChartNum(res.screendata3.length);
                            
                            var screenWidth = document.documentElement.clientWidth;
                            if(mtid==407 && id!=56){
                                if(screenWidth>1810){
                                    provideNumber=9;
                                 }else{
                                    provideNumber=5;
                                 }
                            }
                            if(mtid==434){
                                provideNumber=6;
                            }
                            if(mtid==427 && id!=84 ){
                                if(screenWidth>1236){
                                    provideNumber=8;
                                 }else{
                                    provideNumber=6;
                                 }
                            }
                            if(id==82 || id==83 ){
                                if(screenWidth>1449){
                                    provideNumber=4;
                                }else if(screenWidth>1405){
                                    provideNumber=3;
                                }else{
                                    provideNumber=2;
                                 }
                            }
                            if(mtid==441){
                                if(screenWidth>1640){
                                    provideNumber=9;
                                }else if(screenWidth>1225){
                                    provideNumber=7;
                                }else{
                                    provideNumber=5;
                                }
                            }

                            // 获取当前标签的索引
                            var index = res.screendata3.map(item => item.name).indexOf(params);
                            var rowNumber = Math.ceil(paramsNameNumber / provideNumber);// 换行的话，需要显示几行，向上取整
                            // 条件等同于rowNumber>1
                            if (paramsNameNumber > provideNumber) {
                              
                                for (var p = 0; p < rowNumber; p++) {
                                    var tempStr = "";// 表示每一次截取的字符串
                                    var start = p * provideNumber;// 开始截取的位置
                                    var end = start + provideNumber;// 结束截取的位置
                                    // 此处特殊处理最后一行的索引值
                                    //  if (p == rowNumber - 1) {
                                    //     // 最后一次不换行
                                    //     tempStr = params.substring(start, paramsNameNumber);
                                    //  } else {
                                    //     // 每一次拼接字符串并换行
                                    //     tempStr = params.substring(start, end) + "\n";
                                    //  }

                                    if(index+1==res.screendata3.length && mtid==407 && p==1){
                                        end = start + 4;// 结束截取的位置
                                    }
                                    if(index+1==res.screendata3.length && mtid==407 && p==2){
                                        start = start -1;// 结束截取的位置
                                    }

                                    if (p == rowNumber - 1 ) {
                                        // 最后一次不换行
                                        tempStr = params.substring(start, paramsNameNumber);
                                    } else {
                                        // 每一次拼接字符串并换行
                                        if(rowNumber==3 && p==1 && screenWidth>1630 && mtid==407){
                                            tempStr = params.substring(start, end);
                                        }else{
                                            tempStr = params.substring(start, end) + "\n";
                                        }
                                    }
                                    newParamsName += tempStr;// 最终拼成的字符串
                                }
                            } else {
                                // 将旧标签的值赋给新标签
                                newParamsName = params;
                            }
                            //将最终的字符串返回
                            return newParamsName
                        }
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLabel: {
                        textStyle: {
                            color: '#01C4F7', //坐标值得具体的颜色                   
                        },
                        fontSize: fontSize
                    }
                },
                series: [{
                    data: res.screendata3,
                    type: 'bar',
                    barMaxWidth:40,
                    label: {
                        show: true,
                        position: 'top',
                        formatter:function(params){
                           if(res.screendata3.length>5){
                               return params.data.result+"%\n"+params.data.value+"人";
                           }else{
                               return params.data.result+"%，"+params.data.value+"人";
                           }
                           
                        },
                        fontSize:fontSize
                    },
                    itemStyle: {
                        opacity: 0,
                        normal:{
                            color:function(params){
                                var colorList = [
                                   '#E87C25','#01C4F7','#FCCE10',"#FA640D",'#CB98E0','#863BF2','#087998','#1F52FA'
                                ];
                                return colorList[params.dataIndex];
                            }
                        }
                    },
                }] 
            }
            myCharts.setOption(option);
            window.addEventListener("resize", function() {
                myCharts.resize();
            });
        }else{
           $("#div3").css("display","none");
       }

        if (res.screendata4) {
            let myCharts = echarts.init(document.getElementById('chart2'));
            
            let option = {
                grid: {
                    show: true,                                 //是否显示图表背景网格    
                    left: 5,                                    //图表距离容器左侧多少距离
                    right: 5,                                //图表距离容器右侧侧多少距离
                    bottom: isphone==1 ? 60 :  (mtid==413 ? 0:10),                              //图表距离容器上面多少距离
                    top: isphone==1 ? 80 :  (mtid==413 || mtid==441 ? 35:30),                                        //图表距离容器下面多少距离
                    containLabel: true,                     //防止标签溢出  
                   
                },
                color: [
                    '#60C0DD','#D7504B','#C6E579','#F4E001','#26C0C0','#FCCE10',"#FA640D",'#0DD6F5','#863BF2'
                ],
                xAxis: {
                    type: 'category',
                    data:res.screendata4.map(item => item.name),
                    axisLabel: {
                        textStyle: {
                            color: '#01C4F7', //坐标值得具体的颜色                   
                        },
                        fontSize: fontSize,
                        formatter:function(params){
                                var newParamsName = "";// 最终拼接成的字符串
                                var paramsNameNumber = params.length;// 实际标签的个数
                                var screenWidth = document.documentElement.clientWidth;
                                var provideNumber = mtid!=397&&mtid!=399&&mtid!=401&&mtid!=409&&res.screendata4.length>4?2:4;// 每行能显示的字的个数
                                provideNumber = getChartNum(res.screendata4.length);
                                if(mtid==407){
                                    provideNumber=6;
                                }
                                if(mtid==441){
                                    if(screenWidth>1438){
                                        provideNumber=6;
                                    }else{
                                        provideNumber=4;
                                    }
                                    
                                }
                                var rowNumber = Math.ceil(paramsNameNumber / provideNumber);// 换行的话，需要显示几行，向上取整
                                // 条件等同于rowNumber>1
                               
                                if(mtid==407&&id!=56){
                                    rowNumber=3;
                                }
                                // 获取当前标签的索引
                                var index = res.screendata4.map(item => item.name).indexOf(params);
                                if (paramsNameNumber > provideNumber) {
                                    
                                    for (var p = 0; p < rowNumber; p++) {
                                        var tempStr = "";// 表示每一次截取的字符串
                                        var start = p * provideNumber;// 开始截取的位置
                                        var end = start + provideNumber;// 结束截取的位置
                                        
                                        if(index+1==res.screendata4.length && mtid==407 && p==2){
                                            start = start -3;// 结束截取的位置
                                        }
                                        if(index+1==res.screendata4.length && mtid==407 && p==1){
                                            end = start + 3;// 结束截取的位置
                                        }

                                        // if(mtid==407 && id!=56 && p==2 && index+1!=res.screendata4.length){
                                        //     start = start -1;// 结束截取的位置
                                        // }
                                        // if(mtid==407 && id!=56  && p==1 && index+1!=res.screendata4.length){
                                        //     end = start + 1;// 结束截取的位置
                                        // }

                                        // 此处特殊处理最后一行的索引值
                                        if (p == rowNumber - 1 ) {
                                            // 最后一次不换行
                                            tempStr = params.substring(start, paramsNameNumber);
                                        } else {
                                            
                                            // 每一次拼接字符串并换行
                                            if(rowNumber==3 && p==1 && screenWidth>1496 && mtid==407){
                                                tempStr = params.substring(start, end);
                                            }else{
                                                tempStr = params.substring(start, end) + "\n";
                                            }
                                        }
                                        newParamsName += tempStr;// 最终拼成的字符串
                                    }
                                    
                                } else {
                                    // 将旧标签的值赋给新标签
                                    newParamsName = params;
                                }
                                //将最终的字符串返回
                                return newParamsName
                        }
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLabel: {
                       textStyle: {
                           color: '#01C4F7', //坐标值得具体的颜色                   
                       },
                       fontSize: fontSize,
                   }
                },
                series: [{
                    data: res.screendata4,
                    type: 'bar',
                    barMaxWidth:40,
                    label: {
                            show: true,
                            position: 'top',
                            formatter:function(params){
                               if(res.screendata4.length>5){
                                   return params.data.result+"%\n"+params.data.value+"人";
                               }else{
                                   return params.data.result+"%，"+params.data.value+"人";
                               }
                               
                            },
                            fontSize:fontSize
                    },
                    itemStyle: {
                        opacity: 0,
                        normal:{
                            color:function(params){
                                var colorList = [
                                   '#E87C25','#01C4F7','#FCCE10',"#FA640D",'#CB98E0','#863BF2','#087998','#1F52FA'
                                ];
                                return colorList[params.dataIndex];
                            }
                        }
                    },
                }] 
            }
            myCharts.setOption(option);
            window.addEventListener("resize", function() {
                myCharts.resize();
            }); 
        }else{
           $("#div2").css("display","none");
        }

        //唐山会议 一屏显示6个问题
        if(mtid==441 && (res.screentitle7==undefined || res.screentitle7=="")){
            $("#bigDiv1").css("display","none");
            $("#bigDiv2").css("display","");

            if (res.screendata2) {
                    $('#chart28title').html(res.screentitle2);
                    let myCharts = echarts.init(document.getElementById('chart28'))
                    //3.配置
                    let option = {
                        grid: {
                            show: true,                                 //是否显示图表背景网格    
                            left: 5,                                    //图表距离容器左侧多少距离
                            right: 5,                                //图表距离容器右侧侧多少距离
                            bottom: isphone==1 ? 80 : 15,                              //图表距离容器上面多少距离
                            top: 30,                                         //图表距离容器下面多少距离
                            containLabel: true,                     //防止标签溢出  
                           
                        },
                        xAxis: {
                            type: 'category',
                            data:res.screendata2.map(item => item.name),
                            axisLabel: {
                                textStyle: {
                                    color: '#01C4F7', //坐标值得具体的颜色                   
                                },
                                fontSize: fontSize,
                                formatter:function(params){
                                   var newParamsName = "";// 最终拼接成的字符串
                                       var paramsNameNumber = params.length;// 实际标签的个数
                                       var provideNumber =4;// 每行能显示的字的个数
                                       var screenWidth = document.documentElement.clientWidth;
                                       if(res.screendata2.length>4){
                                               provideNumber=2;
                                       }
                                       
                                        if(mtid==441){
                                            if(screenWidth>1242){
                                                provideNumber=6;
                                            }else{
                                                provideNumber=3;
                                            }
                                        }
                                        var rowNumber = Math.ceil(paramsNameNumber / provideNumber);// 换行的话，需要显示几行，向上取整
                                        // 条件等同于rowNumber>1
                                        if (paramsNameNumber > provideNumber) {
                                            for (var p = 0; p < rowNumber; p++) {
                                                var tempStr = "";// 表示每一次截取的字符串
                                                var start = p * provideNumber;// 开始截取的位置
                                                var end = start + provideNumber;// 结束截取的位置
                                                // 此处特殊处理最后一行的索引值
                                                if (p == rowNumber - 1) {
                                                    // 最后一次不换行
                                                    tempStr = params.substring(start, paramsNameNumber);
                                                } else {
                                                    // 每一次拼接字符串并换行
                                                    tempStr = params.substring(start, end) + "\n";
                                                }
                                                newParamsName += tempStr;// 最终拼成的字符串
                                            }
                                        } else {
                                            // 将旧标签的值赋给新标签
                                            newParamsName = params;
                                        }
                                        //将最终的字符串返回
                                        return newParamsName
                                }
                            }
                        },
                        yAxis: {
                            type: 'value',
                            axisLabel: {
                                textStyle: {
                                    color: '#01C4F7', //坐标值得具体的颜色                   
                                },
                                fontSize: fontSize,
                            }
                        },
                        series: [{
                            data: res.screendata2,
                            type: 'bar',
                            barMaxWidth:40,
                            label: {
                                show: true,
                                position: 'top',
                                formatter:function(params){
                                   if(res.screendata2.length>5){
                                       return params.data.result+"%\n"+params.data.value+"人";
                                   }else{
                                       return params.data.result+"%，"+params.data.value+"人";
                                   }
                                },
                                fontSize:fontSize
                            },
                            itemStyle: {
                                opacity: 0,
                                normal:{
                                    color:function(params){
                                        var colorList = [
                                            '#E87C25','#01C4F7','#FCCE10',"#FA640D",'#CB98E0','#863BF2','#087998','#1F52FA'
                                        ];
                                        return colorList[params.dataIndex];
                                    }
                                }
                            },
                        }] 
                    }
                    //4.渲染图表
                    myCharts.setOption(option);
                    window.addEventListener("resize", function() {
                        myCharts.resize();
                    });
            }

            if (res.screendata5) {
                    $('#chart29title').html(res.screentitle5);
                    let myCharts = echarts.init(document.getElementById('chart29'))
                    //3.配置
                    let option = {
                        grid: {
                            show: true,                                 //是否显示图表背景网格    
                            left: 5,                                    //图表距离容器左侧多少距离
                            right: 5,                                //图表距离容器右侧侧多少距离
                            bottom: isphone==1 ? 80 : 5,                              //图表距离容器上面多少距离
                            top: 40,                                         //图表距离容器下面多少距离
                            containLabel: true,                     //防止标签溢出  
                           
                        },
                        xAxis: {
                            type: 'category',
                            data:res.screendata5.map(item => item.name),
                            axisLabel: {
                                textStyle: {
                                    color: '#01C4F7', //坐标值得具体的颜色                   
                                },
                                fontSize: fontSize,
                                formatter:function(params){
                                   var newParamsName = "";// 最终拼接成的字符串
                                       var paramsNameNumber = params.length;// 实际标签的个数
                                       var provideNumber =4;// 每行能显示的字的个数
                                       var screenWidth = document.documentElement.clientWidth;
                                       
                                        if(mtid==441){
                                            if(screenWidth>1680){
                                                provideNumber=5;
                                            }else if(screenWidth>1406){
                                                provideNumber=4;
                                            }else{
                                                provideNumber=3;
                                            }
                                        }
                                        var rowNumber = Math.ceil(paramsNameNumber / provideNumber);// 换行的话，需要显示几行，向上取整
                                        // 条件等同于rowNumber>1
                                        if (paramsNameNumber > provideNumber) {
                                            for (var p = 0; p < rowNumber; p++) {
                                                var tempStr = "";// 表示每一次截取的字符串
                                                var start = p * provideNumber;// 开始截取的位置
                                                var end = start + provideNumber;// 结束截取的位置
                                                // 此处特殊处理最后一行的索引值
                                                if (p == rowNumber - 1) {
                                                    // 最后一次不换行
                                                    tempStr = params.substring(start, paramsNameNumber);
                                                } else {
                                                    // 每一次拼接字符串并换行
                                                    tempStr = params.substring(start, end) + "\n";
                                                }
                                                newParamsName += tempStr;// 最终拼成的字符串
                                            }
                                        } else {
                                            // 将旧标签的值赋给新标签
                                            newParamsName = params;
                                        }
                                        //将最终的字符串返回
                                        return newParamsName
                               }
                            }
                        },
                        yAxis: {
                            type: 'value',
                            axisLabel: {
                                textStyle: {
                                    color: '#01C4F7', //坐标值得具体的颜色                   
                                },
                                fontSize: fontSize,
                            }
                        },
                        series: [{
                            data: res.screendata5,
                            type: 'bar',
                            barMaxWidth:40,
                            label: {
                                show: true,
                                position: 'top',
                                formatter:function(params){
                                   if(res.screendata5.length>5){
                                       return params.data.result+"%\n"+params.data.value+"人";
                                   }else{
                                       return params.data.result+"%，"+params.data.value+"人";
                                   }
                                },
                                fontSize:fontSize
                            },
                            itemStyle: {
                                opacity: 0,
                                normal:{
                                    color:function(params){
                                        var colorList = [
                                            '#E87C25','#01C4F7','#FCCE10',"#FA640D",'#CB98E0','#863BF2','#087998','#1F52FA'
                                        ];
                                        return colorList[params.dataIndex];
                                    }
                                }
                            },
                        }] 
                    }
                    //4.渲染图表
                    myCharts.setOption(option);
                    window.addEventListener("resize", function() {
                        myCharts.resize();
                    });
            }

            if (res.screendata6) {
                $('#chart4title').html(res.screentitle6);
                let myCharts = echarts.init(document.getElementById('chart4'));
                if((res.screendata6.length>0 && res.screendata6[0].content!="" && res.screendata6[0].content!=undefined)||res.screendata6.length==0){
                    if (res.screendata6) {
                        var str="";
                        for(var i=0;i<res.screendata6.length;i++){
                            str+='<div class="jq33-content"> <div class="left"><span>'+(i+1)+"、"+res.screendata6[i].content+'</span></div></div>';
                            
                        }
                        document.getElementById('chart4').innerHTML=str;
                    }
                }else{
                    let myCharts = echarts.init(document.getElementById('chart4'))
                    //3.配置
                    let option = {
                        grid: {
                            show: true,                                 //是否显示图表背景网格    
                            left: 5,                                    //图表距离容器左侧多少距离
                            right: 5,                                //图表距离容器右侧侧多少距离
                            bottom: isphone==1 ? 80 : 5,                              //图表距离容器上面多少距离
                            top: 30,                                         //图表距离容器下面多少距离
                            containLabel: true,                     //防止标签溢出  
                           
                        },
                        xAxis: {
                            type: 'category',
                            data:res.screendata6.map(item => item.name),
                            axisLabel: {
                                    textStyle: {
                                        color: '#01C4F7', //坐标值得具体的颜色                   
                                    },
                                    fontSize: fontSize,
                                    formatter:function(params){
                                    var newParamsName = "";// 最终拼接成的字符串
                                       var paramsNameNumber = params.length;// 实际标签的个数
                                       var provideNumber =4;// 每行能显示的字的个数
                                       var screenWidth = document.documentElement.clientWidth;
                                       
                                        if(mtid==441){
                                            if(screenWidth>1640){
                                                provideNumber=7;
                                            }else if(screenWidth>1414){
                                                provideNumber=6;
                                            }else if(screenWidth>1197){
                                                provideNumber=5;
                                            }else{
                                                provideNumber=4;
                                            }
                                        }
                                        var rowNumber = Math.ceil(paramsNameNumber / provideNumber);// 换行的话，需要显示几行，向上取整
        
                                        
                                        // 条件等同于rowNumber>1
                                        if (paramsNameNumber > provideNumber) {
                                            
                                            for (var p = 0; p < rowNumber; p++) {
                                                var tempStr = "";// 表示每一次截取的字符串
                                                var start = p * provideNumber;// 开始截取的位置
                                                var end = start + provideNumber;// 结束截取的位置
                                                // 此处特殊处理最后一行的索引值
                                                if (p == rowNumber - 1) {
                                                    // 最后一次不换行
                                                    tempStr = params.substring(start, paramsNameNumber);
                                                } else {
                                                    // 每一次拼接字符串并换行
                                                    tempStr = params.substring(start, end) + "\n";
                                                }
                                                
                                                newParamsName += tempStr;// 最终拼成的字符串
                                            }
                            
                                        } else {
                                            // 将旧标签的值赋给新标签
                                            newParamsName = params;
                                        }
                                        //将最终的字符串返回
                                        return newParamsName
                                }
                            }
                        },
                        yAxis: {
                            type: 'value',
                            axisLabel: {
                                textStyle: {
                                    color: '#01C4F7', //坐标值得具体的颜色                   
                                },
                                fontSize: fontSize,
                            }
                        },
                        series: [{
                            data: res.screendata6,
                            type: 'bar',
                            barMaxWidth:40,
                            label: {
                                show: true,
                                position: 'top',
                                formatter:function(params){
                                   if(res.screendata6.length>5){
                                       return params.data.result+"%\n"+params.data.value+"人";
                                   }else{
                                       return params.data.result+"%，"+params.data.value+"人";
                                   }
                                },
                                fontSize:fontSize
                            },
                            itemStyle: {
                                opacity: 0,
                                normal:{
                                    color:function(params){
                                        var colorList = [
                                            '#E87C25','#01C4F7','#FCCE10',"#FA640D",'#CB98E0','#863BF2','#087998','#1F52FA'
                                        ];
                                        return colorList[params.dataIndex];
                                    }
                                }
                            },
                        }] 
                    }
                    //4.渲染图表
                    myCharts.setOption(option);
                    window.addEventListener("resize", function() {
                        myCharts.resize();
                    });
                }
            }else{
                $(".div4").css("display","none");
            }
        }else{
            if (res.screendata5) {
                let myCharts = echarts.init(document.getElementById('chart4'));
                if((res.screendata5.length>0 && res.screendata5[0].content!="" && res.screendata5[0].content!=undefined)||res.screendata5.length==0){
                    if (res.screendata5) {
                        var str="";
                        for(var i=0;i<res.screendata5.length;i++){
                            str+='<div class="jq33-content"> <div class="left"><span>'+(i+1)+"、"+res.screendata5[i].content+'</span></div></div>';
                            
                        }
                        document.getElementById('chart4').innerHTML=str;
                    }
                }else{
                    let myCharts = echarts.init(document.getElementById('chart4'))
                    //3.配置
                    let option = {
                        grid: {
                            show: true,                                 //是否显示图表背景网格    
                            left: 5,                                    //图表距离容器左侧多少距离
                            right: 5,                                //图表距离容器右侧侧多少距离
                            bottom: isphone==1 ? 80 : (mtid==439 ? 35:15),                              //图表距离容器上面多少距离
                            top: 30,                                         //图表距离容器下面多少距离
                            containLabel: true,                     //防止标签溢出  
                           
                        },
                        xAxis: {
                            type: 'category',
                            data:res.screendata5.map(item => item.name),
                            axisLabel: {
                                textStyle: {
                                    color: '#01C4F7', //坐标值得具体的颜色                   
                                },
                                fontSize: fontSize,
                                formatter:function(params){
                                   var newParamsName = "";// 最终拼接成的字符串
                                       var paramsNameNumber = params.length;// 实际标签的个数
                                       var provideNumber =4;// 每行能显示的字的个数
                                       var screenWidth = document.documentElement.clientWidth;
                                       if(res.screendata5.length>4){
                                               provideNumber=2;
                                       }
                                       if(res.screendata5.length<4){
                                           provideNumber=6;
                                       }
                                      
                                        
                                        if(mtid==397 || mtid==409){
                                        provideNumber=5;
                                        }
                                        if(mtid==408 ||mtid==403  ){
                                        provideNumber=4;
                                        }
                                        
                                        if(res.screendata5.length==2){
                                            provideNumber=7;
                                        }
                                        
                                        if(mtid==427 && id!=84){
                                            provideNumber=getChartStyle();
                                        }else{
                                            provideNumber=getChartNum(res.screendata5.length);
                                           
                                        }
                                        if(mtid==441){
                                            if(screenWidth>1406){
                                                provideNumber=4;
                                            }else{
                                                provideNumber=3;
                                            }
                                        }
                                        var rowNumber = Math.ceil(paramsNameNumber / provideNumber);// 换行的话，需要显示几行，向上取整
        
                                        
                                        // 条件等同于rowNumber>1
                                        if (paramsNameNumber > provideNumber) {
                                            
                                            for (var p = 0; p < rowNumber; p++) {
                                                var tempStr = "";// 表示每一次截取的字符串
                                                var start = p * provideNumber;// 开始截取的位置
                                                var end = start + provideNumber;// 结束截取的位置
                                                // 此处特殊处理最后一行的索引值
                                                if (p == rowNumber - 1) {
                                                    // 最后一次不换行
                                                    tempStr = params.substring(start, paramsNameNumber);
                                                } else {
                                                    // 每一次拼接字符串并换行
                                                    tempStr = params.substring(start, end) + "\n";
                                                }
                                                
                                                newParamsName += tempStr;// 最终拼成的字符串
                                            }
                            
                                        } else {
                                            // 将旧标签的值赋给新标签
                                            newParamsName = params;
                                        }
                                        //将最终的字符串返回
                                        return newParamsName
                               }
                            }
                        },
                        yAxis: {
                            type: 'value',
                            axisLabel: {
                                textStyle: {
                                    color: '#01C4F7', //坐标值得具体的颜色                   
                                },
                                fontSize: fontSize,
                            }
                        },
                        series: [{
                            data: res.screendata5,
                            type: 'bar',
                            barMaxWidth:40,
                            label: {
                                show: true,
                                position: 'top',
                                formatter:function(params){
                                   if(res.screendata5.length>5){
                                       return params.data.result+"%\n"+params.data.value+"人";
                                   }else{
                                       return params.data.result+"%，"+params.data.value+"人";
                                   }
                                },
                                fontSize:fontSize
                            },
                            itemStyle: {
                                opacity: 0,
                                normal:{
                                    color:function(params){
                                        var colorList = [
                                            '#E87C25','#01C4F7','#FCCE10',"#FA640D",'#CB98E0','#863BF2','#087998','#1F52FA'
                                        ];
                                        return colorList[params.dataIndex];
                                    }
                                }
                            },
                        }] 
                    }
                    //4.渲染图表
                    myCharts.setOption(option);
                    window.addEventListener("resize", function() {
                        myCharts.resize();
                    });
                }
            }else{
                $(".div4").css("display","none");
            }
            if (res.screendata2) {
                let myCharts = echarts.init(document.getElementById('chart6'));

                let option = {
                    tooltip: {
                        trigger: 'item',
                        formatter: function (params) {
                            return "选项："+params.data.name+'<br/>人数：'+params.data.value+"人（"+params.data.result+"%）"
                        }
                    },
                
                    legend: {
                            y:'top',
                            orient: 'vertical',
                            left: 'left',
                            bottom:30,
                            textStyle:{
                            color: '#00FFFF',
                            fontSize: fontSize,
                            }
                    },
                    label: {
                        show: true, 
                        position:'inside',
                        formatter: function (arg) {
                            let text =arg.data.name;
                            let value_format = arg.value+'人  ';
                            let percent_format = arg.data.result + '%';
                            if (text.length >12 && isphone==1) {
                                return text = `${text.slice(0, 6)}\n${text.slice(6, text.length)}\n(${value_format}${percent_format})`
                            } else{
                                if(hasComma(text) &&( mtid==399 ||mtid==401)){
                                    text=text.replace(",", "，"); 
                                    let str=text.split("，");
                                    return str[0] +'\n'+str[1] +'\n  （'+ arg.data.value + '人  '+arg.data.result+'%）' 
                                }else if(hasComma2(text) &&mtid==4213){
                                    text=text.replace("(", "（"); 
                                    let str=text.split("（");
                                        return str[0] +'\n （'+str[1] +'\n  （'+ arg.data.value + '人  '+arg.data.result+'%）' 
                                    }else{
                                        return arg.data.name +'\n  （'+ arg.data.value + '人  '+arg.data.result+'%）' 
                                    }
                                }
                            },
                        textStyle:{
                            fontSize: fontSize,
                        }
                    },
                    series: [
                        {
                            type: 'pie',
                            data: res.screendata2,
                            center: ['50%', '60%'], //饼图位置
                            itemStyle:{
                                labelLine: {
                                    show: false,
                                    
                                },
                                
                                normal: {
                                    color: function (colors) {
                                    var colorList = [
                                                '#fc8251',
                                                '#5470c6',
                                                '#9A60B4',
                                                '#f9c956',
                                                '#3BA272',
                                                '#F21755',
                                                '#03BCFA',
                                                '#FA4603',
                                                '#08FA03'
                                            ];
                                        return colorList[colors.dataIndex];
                                    }
                                },
                            },
                            label: {
                            fontSize:fontSize
                            },
                            radius: '40%',
                        },
                        
                    ]
                }
                myCharts.setOption(option);
                window.addEventListener("resize", function() {
                    myCharts.resize();
                }); 
            }else{
                $("#div6").css("display","none");
            }
        }
    })
    if(isphone==1){
    initChart4();
    }
}
 

function getChartNum(len){
    var provideNumber=4;
    var screenWidth = document.documentElement.clientWidth;
    if(len>6){
        if(screenWidth<1400){
            provideNumber=2
        }else{
            provideNumber=3
        }
    }else if(len==6){
        if(screenWidth<1165){
            provideNumber=2
        }else if(screenWidth<1393){
            provideNumber=3
        }else{
            provideNumber=4
        }
    }else if(len==5){
        if(screenWidth<1165){
            provideNumber=3
        }else if(screenWidth<1429){
            provideNumber=4
        }else if(screenWidth<1689){
            provideNumber=5
        }else{
            provideNumber=6
        }
    }else if(len==4){
        if(screenWidth<1186){
            provideNumber=4
        }else if(screenWidth<1406){
            provideNumber=5
        }else if(screenWidth<1620){
            provideNumber=6
        }else{
            provideNumber=7
        }
    }else if(len==2){
        provideNumber=7
    }else{
        provideNumber=8
        if(screenWidth<1165){
            provideNumber=6
        }else if(screenWidth<1429){
            provideNumber=8
        }
    }
    return provideNumber;
}
function getChart2Num(len){
   var provideNumber=4;
   var screenWidth = document.documentElement.clientWidth;
    if(len>6){
        if(screenWidth<1080){
            provideNumber=2
        }else if(screenWidth<1290){
            provideNumber=3
        }else if(screenWidth<1487){
            provideNumber=4
        }else if(screenWidth<1640){
            provideNumber=5
        }else{
            provideNumber=6
        }
    }else if(len==6){
        if(screenWidth<1165){
            provideNumber=3
        }else if(screenWidth<1229){
            provideNumber=4
        }else if(screenWidth<1410){
            provideNumber=5
        }else if(screenWidth<1560){
            provideNumber=6
        }else{
            provideNumber=7
        }
    }else if(len==5){
        if(screenWidth<1000){
            provideNumber=4
        }else if(screenWidth<1130){
            provideNumber=5
        }else if(screenWidth<1306){
            provideNumber=6
        }else if(screenWidth<1640){
            provideNumber=7
        }else{
            provideNumber=8
        }
    }else if(len==4){
        if(screenWidth<1069){
            provideNumber=6
        }else if(screenWidth<1165){
            provideNumber=8
        }else if(screenWidth<1401){
            provideNumber=9
        }else if(screenWidth<1529){
            provideNumber=10
        }else{
            provideNumber=11
        }
    }else{
        if(screenWidth<1165){
            provideNumber=8
        }else if(screenWidth<1429){
            provideNumber=10
        }else{
            provideNumber=12
        }
    }
    return provideNumber;
}

function getChartStyle(){
    var screenWidth = document.documentElement.clientWidth;
    if(screenWidth<1430){
        provideNumber=6
    }else if(screenWidth<1553){
        provideNumber=8
    }else if(screenWidth<1590){
        provideNumber=10
    }else{
        provideNumber=11
    }
    return provideNumber;
}

function getChart3Num(len){
    var provideNumber=4;
    var screenWidth = document.documentElement.clientWidth;
    if(len>6){
        if(screenWidth<1490){
            provideNumber=2
        }else if(screenWidth<1640){
            provideNumber=3
        }else{
            provideNumber=4
        }
    }else if(len==6){
        if(screenWidth<1165){
            provideNumber=3
        }else if(screenWidth<1229){
            provideNumber=4
        }else if(screenWidth<1410){
            provideNumber=5
        }else if(screenWidth<1560){
            provideNumber=6
        }else{
            provideNumber=7
        }
    }else if(len==5){
        if(screenWidth<1430){
            provideNumber=4
        }else if(screenWidth<1679){
            provideNumber=5
        }else{
            provideNumber=6
        }
    }else if(len==4){
        if(screenWidth<1069){
            provideNumber=6
        }else if(screenWidth<1165){
            provideNumber=8
        }else if(screenWidth<1401){
            provideNumber=9
        }else if(screenWidth<1529){
            provideNumber=10
        }else{
            provideNumber=11
        }
    }else{
        if(screenWidth<1165){
            provideNumber=8
        }else if(screenWidth<1429){
            provideNumber=10
        }else{
            provideNumber=12
        }
    }
    return provideNumber;
}


function initChart4()
{
    $('#chart21Loading').show();
    $('#chart22Loading').show();
    $('#chart23Loading').show();
    $('#chart24Loading').show();
    //$('#chart25Loading').show();
    axios.post("meeting.php?action=getMeetingSurveyScreenData&type=1&mtid="+mtid+"&id="+id+"&isphone="+isphone )
    .then((res) => 
    {
        var more=false;
       
        
        if(isphone!=1 && res.screentitle10!=undefined && res.screendata10!=undefined && res.screentitle10!=""){
            $('#before_1').css("width","32%");
            $('#before_2').css("width","32%");
            $('#before_3').css("display","block");
            $('#mokuai4').show();
            var title6=res.screentitle6;
            var title7=res.screentitle7;
            var title8=res.screentitle8;
            var title9=res.screentitle9;
            var title10=res.screentitle10;
            var title11=res.screentitle11;
            var data6=res.screendata6;
            var data7=res.screendata7;
            var data8=res.screendata8;
            var data9=res.screendata9;
            var data10=res.screendata10;
            var data11=res.screendata11;
            res.screentitle8=title9;
            res.screentitle9=title10;
            res.screentitle10=title8;
            res.screendata8=data9;
            res.screendata9=data10;
            res.screendata10=data8;
            more=true;

            $('#chart25Loading').hide();
            $('#chart26Loading').hide();
            $('#chart27Loading').hide();
        }else{
            $('#mokuai4').show();
        }



        $('#surveytitle').html(res.title);
        $('#chart21Loading').hide();
        $('#chart22Loading').hide();
        $('#chart23Loading').hide();
        $('#chart24Loading').hide();
       
        var legend = [];
        var series = [];
        if (res.screendata6) {
           $('#chart21title').html(res.screentitle6);
            let myCharts = echarts.init(document.getElementById('chart21'));
            if((res.screendata6.length>0 && res.screendata6[0].content!="" && res.screendata6[0].content!=undefined)||res.screendata6.length==0){
                if (res.screendata6) {
                    var str="";
                    for(var i=0;i<res.screendata6.length;i++){
                        str+='<div class="jq33-content"> <div class="left"><span>'+(i+1)+"、"+res.screendata6[i].content+'</span></div></div>';
                        
                    }
                    document.getElementById('chart21').innerHTML=str;
                }
            }else{
                let myCharts = echarts.init(document.getElementById('chart21'))
                //3.配置
                let option = {
                grid: {
                    show: true,                                 //是否显示图表背景网格    
                    left: 5,                                    //图表距离容器左侧多少距离
                    right: 5,                                //图表距离容器右侧侧多少距离
                    bottom: isphone==1 ? 80 : 10,                              //图表距离容器上面多少距离
                        top: isphone==1? 40 :40,  
                    containLabel: true,                     //防止标签溢出  
                    
                },
                xAxis: {
                    type: 'category',
                    
                    data:res.screendata6.map(item => item.name),
                    axisLabel: {
                        textStyle: {
                            color: '#01C4F7', //坐标值得具体的颜色                   
                        },
                        fontSize: fontSize, //设置坐标轴文本标签的字体大小
                        formatter:function(params){
                            var newParamsName = "";// 最终拼接成的字符串
                                var paramsNameNumber = params.length;// 实际标签的个数
                                var provideNumber =isphone==1 ? 4: 8;// 每行能显示的字的个数
                               
                                
                                if(mtid==427 || mtid==441){
                                    var screenWidth = document.documentElement.clientWidth;
                                    if(screenWidth>=1609){
                                        provideNumber =7;
                                    }else if(screenWidth>=1409){
                                        provideNumber =6;
                                    }else if(screenWidth>=1145){
                                        provideNumber =5;
                                    }else{
                                        provideNumber =4;
                                    }
                                }
                                if(id==82 || id==83 || id==84){
                                    provideNumber=getChartNum(res.screendata6.length);
                                }
                                if(id==85){
                                    var screenWidth = document.documentElement.clientWidth;
                                    if(screenWidth<1189){
                                        provideNumber=6;
                                    }else{
                                        provideNumber=7;
                                    }
                                }
                               
                                var rowNumber = Math.ceil(paramsNameNumber / provideNumber);// 换行的话，需要显示几行，向上取整
                                // 条件等同于rowNumber>1
                                if (paramsNameNumber > provideNumber) {
                                    
                                    for (var p = 0; p < rowNumber; p++) {
                                        var tempStr = "";// 表示每一次截取的字符串
                                        var start = p * provideNumber;// 开始截取的位置
                                        var end = start + provideNumber;// 结束截取的位置
                                        // 此处特殊处理最后一行的索引值
                                        if (p == rowNumber - 1) {
                                            // 最后一次不换行
                                            tempStr = params.substring(start, paramsNameNumber);
                                        } else {
                                            // 每一次拼接字符串并换行
                                            tempStr = params.substring(start, end) + "\n";
                                        }
                                        newParamsName += tempStr;// 最终拼成的字符串
                                    }
                    
                                } else {
                                    // 将旧标签的值赋给新标签
                                    newParamsName = params;
                                }
                                //将最终的字符串返回
                                return newParamsName
                        }
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLabel: {
                        textStyle: {
                            color: '#01C4F7', //坐标值得具体的颜色                   
                        },
                        fontSize: fontSize,
                    }
                },
                series: [{
                    data: res.screendata6,
                    type: 'bar',
                    barMaxWidth:40,
                    label: {
                        show: true,
                        position: 'top',
                        formatter:function(params){
                            return params.data.result+"\n"+params.data.value+"人";
                            },
                            fontSize:fontSize
                    },
                
                    itemStyle: {
                        opacity: 0,
                        normal:{
                            color:function(params){
                                var colorList = [
                                    '#E87C25','#01C4F7','#FCCE10',"#FA640D",'#CB98E0','#863BF2','#087998','#1F52FA'
                                ];
                                return colorList[params.dataIndex];
                            }
                        }
                    },
                }] 
            }
                //4.渲染图表
                myCharts.setOption(option);
                window.addEventListener("resize", function() {
                    myCharts.resize();
                });
            }
        }else{
            $(".div21").css("display","none");
        }
        if (res.screendata7) {
            $('#chart23title').html(res.screentitle7);
            let myCharts = echarts.init(document.getElementById('chart23'));
            if((res.screendata7.length>0 && res.screendata7[0].content!="" && res.screendata7[0].content!=undefined) || res.screendata7.length==0){
                if (res.screendata7) {
                    var str="";
                    for(var i=0;i<res.screendata7.length;i++){
                        str+='<div class="jq33-content"> <div class="left"><span>'+(i+1)+"、"+res.screendata7[i].content+'</span></div></div>';
                        
                    }
                    document.getElementById('chart23').innerHTML=str;
                }
            }else{
                let myCharts = echarts.init(document.getElementById('chart23'))
                let option = {
                    grid: {
                        show: true,                                 //是否显示图表背景网格    
                        left: 5,                                    //图表距离容器左侧多少距离
                        right: 5,                                //图表距离容器右侧侧多少距离
                        bottom: isphone==1 ? 80 : 10,                              //图表距离容器上面多少距离
                        top: isphone==1? 40 :40,  
                        containLabel: true,                     //防止标签溢出  
                    
                    },
                    xAxis: {
                        type: 'category',
                        data:res.screendata7.map(item => item.name),
                        axisLabel: {
                            textStyle: {
                                color: '#01C4F7', //坐标值得具体的颜色                   
                            },
                            fontSize: fontSize,
                            formatter:function(params){
                            var newParamsName = "";// 最终拼接成的字符串
                                var paramsNameNumber = params.length;// 实际标签的个数
                                var provideNumber =4;// 每行能显示的字的个数

                                if(id==82 || id==83){
                                    var screenWidth = document.documentElement.clientWidth;
                                    if(screenWidth>1494){
                                        provideNumber=17;
                                    }else if(screenWidth>1186){
                                        provideNumber=12;
                                    }else if(screenWidth>1000){
                                        provideNumber=10;
                                    }else{
                                        provideNumber=6;
                                    }
                                    
                                }else if(id==84){
                                    provideNumber=getChartNum(res.screendata7.length);
                                }else{
                                    provideNumber=getChartNum(res.screendata7.length);
                                }

                                if(id==85){
                                    var screenWidth = document.documentElement.clientWidth;
                                    if(screenWidth<1645){
                                        provideNumber=6;
                                    }else{
                                        provideNumber=10;
                                    }
                                }
                                
                                
                                
                                var rowNumber = Math.ceil(paramsNameNumber / provideNumber);// 换行的话，需要显示几行，向上取整
                                
                                // 条件等同于rowNumber>1
                                if (paramsNameNumber > provideNumber) {
                                    
                                    for (var p = 0; p < rowNumber; p++) {
                                        var tempStr = "";// 表示每一次截取的字符串
                                        var start = p * provideNumber;// 开始截取的位置
                                        var end = start + provideNumber;// 结束截取的位置
                                        // 此处特殊处理最后一行的索引值
                                        if (p == rowNumber - 1) {
                                            // 最后一次不换行
                                            tempStr = params.substring(start, paramsNameNumber);
                                        } else {
                                            // 每一次拼接字符串并换行
                                            tempStr = params.substring(start, end) + "\n";
                                        }
                                        newParamsName += tempStr;// 最终拼成的字符串
                                    }
                    
                                } else {
                                    // 将旧标签的值赋给新标签
                                    newParamsName = params;
                                }
                                //将最终的字符串返回
                                return newParamsName
                        }
                        }
                    },
                    yAxis: {
                        type: 'value',
                        axisLabel: {
                            textStyle: {
                                color: '#01C4F7', //坐标值得具体的颜色                   
                            },
                            fontSize: fontSize,
                        }
                    },
                    series: [{
                        data: res.screendata7,
                        type: 'bar',
                        barMaxWidth:40,
                        label: {
                            show: true,
                            position: 'top',
                            formatter:function(params){
                                if(id==84){
                                    return params.data.result+"\n"+params.data.value+"人";
                                }else{
                                    return params.data.result+"%，"+params.data.value+"人";
                                }
                            
                            },
                            fontSize:fontSize
                        },
                        itemStyle: {
                            opacity: 0,
                            normal:{
                                color:function(params){
                                    var colorList = [
                                        '#E87C25','#01C4F7','#FCCE10',"#FA640D",'#CB98E0','#863BF2','#087998','#1F52FA'
                                    ];
                                    return colorList[params.dataIndex];
                                }
                            }
                        },
                    }] 
                }
                myCharts.setOption(option);
                window.addEventListener("resize", function() {
                    myCharts.resize();
                });
            }
        }else{
            $(".div23").css("display","none");
        }
      
        if (res.screendata8) {
            $('#chart22title').html(res.screentitle8);
                let myCharts = echarts.init(document.getElementById('chart22'));
                if((res.screendata8.length>0 && res.screendata8[0].content!="" && res.screendata8[0].content!=undefined) || res.screendata8.length==0){
                    if (res.screendata8) {
                        var str="";
                        for(var i=0;i<res.screendata8.length;i++){
                            str+='<div class="jq33-content"> <div class="left"><span>'+(i+1)+"、"+res.screendata8[i].content+'</span></div></div>';
                            
                        }
                        document.getElementById('chart22').innerHTML=str;
                    }
                }else if(res.screendata8.length>0 ){
                let myCharts = echarts.init(document.getElementById('chart22'))
                let option = {
                    grid: {
                        show: true,                                 //是否显示图表背景网格    
                        left: 5,                                    //图表距离容器左侧多少距离
                        right: 5,                                //图表距离容器右侧侧多少距离
                        bottom: isphone==1 ? 80 : 10,                              //图表距离容器上面多少距离
                        top: isphone==1? 40 :30,                         //图表距离容器下面多少距离
                        containLabel: true,                     //防止标签溢出  
                    
                    },
                    xAxis: {
                        type: 'category',
                        data:res.screendata8.map(item => item.name),
                        axisLabel: {
                            textStyle: {
                                color: '#01C4F7', //坐标值得具体的颜色                   
                            },
                            fontSize: fontSize,
                            formatter:function(params){
                            var newParamsName = "";// 最终拼接成的字符串
                                var paramsNameNumber = params.length;// 实际标签的个数
                                var provideNumber = 8;// 每行能显示的字的个数
                                
                                provideNumber=getChartNum(res.screendata8.length);

                                var rowNumber = Math.ceil(paramsNameNumber / provideNumber);// 换行的话，需要显示几行，向上取整
                                // 条件等同于rowNumber>1
                                if (paramsNameNumber > provideNumber) {
                                    
                                    for (var p = 0; p < rowNumber; p++) {
                                        var tempStr = "";// 表示每一次截取的字符串
                                        var start = p * provideNumber;// 开始截取的位置
                                        var end = start + provideNumber;// 结束截取的位置
                                        // 此处特殊处理最后一行的索引值
                                        if (p == rowNumber - 1) {
                                            // 最后一次不换行
                                            tempStr = params.substring(start, paramsNameNumber);
                                        } else {
                                            // 每一次拼接字符串并换行
                                            tempStr = params.substring(start, end) + "\n";
                                        }
                                        newParamsName += tempStr;// 最终拼成的字符串
                                    }
                    
                                } else {
                                    // 将旧标签的值赋给新标签
                                    newParamsName = params;
                                }
                                //将最终的字符串返回
                                return newParamsName
                        }
                        }
                    },
                    yAxis: {
                        type: 'value',
                        axisLabel: {
                            textStyle: {
                                color: '#01C4F7', //坐标值得具体的颜色                   
                            },
                            fontSize: fontSize
                        }
                    },
                    series: [{
                        data: res.screendata8,
                        type: 'bar',
                        barMaxWidth:40,
                        label: {
                            show: true,
                            position: 'top',
                            formatter:function(params){
                            return params.data.result+"%，"+params.data.value+"人";
                            },
                            fontSize:fontSize
                        },
                        itemStyle: {
                            opacity: 0,
                            normal:{
                                color:function(params){
                                    var colorList = [
                                    '#E87C25','#01C4F7','#FCCE10',"#FA640D",'#CB98E0','#863BF2','#087998','#1F52FA'
                                    ];
                                    return colorList[params.dataIndex];
                                }
                            }
                        },
                    }] 
                }
                myCharts.setOption(option);
                window.addEventListener("resize", function() {
                    myCharts.resize();
                });
            }
        }else{
            $(".div22").css("display","none");
        }
        if (res.screendata9) {
            $('#chart24title').html(res.screentitle9);
                let myCharts = echarts.init(document.getElementById('chart24'));
                if((res.screendata9.length>0 && res.screendata9[0].content!="" && res.screendata9[0].content!=undefined) || res.screendata9.length==0){
                    if (res.screendata9) {
                        var str="";
                        for(var i=0;i<res.screendata9.length;i++){
                            str+='<div class="jq33-content"> <div class="left"><span>'+(i+1)+"、"+res.screendata9[i].content+'</span></div></div>';
                        
                        }
                        document.getElementById('chart24').innerHTML=str;
                    }
                }else{
                
                    let option = {
                        grid: {
                            show: true,                                 //是否显示图表背景网格    
                            left: 5,                                    //图表距离容器左侧多少距离
                            right: 5,                                //图表距离容器右侧侧多少距离
                            bottom: isphone==1 ? 100 : 8,                              //图表距离容器上面多少距离
                            top: isphone==1 ? 80 : 40,                                        //图表距离容器下面多少距离
                            containLabel: true,                     //防止标签溢出  
                            
                        },
                        color: [
                            '#60C0DD','#D7504B','#C6E579','#F4E001','#26C0C0','#FCCE10',"#FA640D",'#0DD6F5','#863BF2'
                        ],
                        xAxis: {
                            type: 'category',
                            data:res.screendata9.map(item => item.name),
                            axisLabel: {
                                textStyle: {
                                    color: '#01C4F7', //坐标值得具体的颜色                   
                                },
                                fontSize: fontSize,
                                formatter:function(params){
                                    var newParamsName = "";// 最终拼接成的字符串
                                    var paramsNameNumber = params.length;// 实际标签的个数
                                    var provideNumber = 8;// 每行能显示的字的个数
                                    if(more){
                                        provideNumber=getChartNum(res.screendata9.length);
                                    }else{
                                        provideNumber=getChart2Num(res.screendata9.length);
                                    }
                                    
                                    var rowNumber = Math.ceil(paramsNameNumber / provideNumber);// 换行的话，需要显示几行，向上取整
                                    // 条件等同于rowNumber>1
                                    if (paramsNameNumber > provideNumber) {
                                        
                                        for (var p = 0; p < rowNumber; p++) {
                                            var tempStr = "";// 表示每一次截取的字符串
                                            var start = p * provideNumber;// 开始截取的位置
                                            var end = start + provideNumber;// 结束截取的位置
                                            // 此处特殊处理最后一行的索引值
                                            if (p == rowNumber - 1) {
                                                // 最后一次不换行
                                                tempStr = params.substring(start, paramsNameNumber);
                                            } else {
                                                // 每一次拼接字符串并换行
                                                tempStr = params.substring(start, end) + "\n";
                                            }
                                            newParamsName += tempStr;// 最终拼成的字符串
                                        }
                        
                                    } else {
                                        // 将旧标签的值赋给新标签
                                        newParamsName = params;
                                    }
                                    //将最终的字符串返回
                                    return newParamsName
                                }
                            }
                        },
                        yAxis: {
                            type: 'value',
                            axisLabel: {
                                textStyle: {
                                    color: '#01C4F7', //坐标值得具体的颜色                   
                                },
                                fontSize: fontSize,
                            }
                        },
                        series: [{
                            data: res.screendata9,
                            type: 'bar',
                            barMaxWidth:40,
                            label: {
                                    show: true,
                                    position: 'top',
                                    formatter:function(params){
                                        return params.data.result+"%\n"+params.data.value+"人";
                                    },
                                    fontSize:fontSize
                            },
                            itemStyle: {
                                opacity: 0,
                                normal:{
                                    color:function(params){
                                        var colorList = [
                                            '#E87C25','#01C4F7','#FCCE10',"#FA640D",'#CB98E0','#863BF2','#087998','#1F52FA'
                                        ];
                                        return colorList[params.dataIndex];
                                    }
                                }
                            },
                        }] 
                    }
                    myCharts.setOption(option);
                    window.addEventListener("resize", function() {
                        myCharts.resize();
                    }); 
                }
            }else{
                $(".div24").css("display","none");
            }


        if (res.screendata10) {
            $('#chart25title').html(res.screentitle10);
                let myCharts = echarts.init(document.getElementById('chart25'));
                if((res.screendata10.length>0 && res.screendata10[0].content!="" && res.screendata10[0].content!=undefined) || res.screendata10.length==0){
                    if (res.screendata10) {
                        var str="";
                        for(var i=0;i<res.screendata10.length;i++){
                            str+='<div class="jq33-content"> <div class="left"><span>'+(i+1)+"、"+res.screendata10[i].content+'</span></div></div>';
                        
                        }
                        document.getElementById('chart25').innerHTML=str;
                    }
                }else{
                
                let option = {
                    grid: {
                        show: true,                                 //是否显示图表背景网格    
                        left: 5,                                    //图表距离容器左侧多少距离
                        right: 5,                                //图表距离容器右侧侧多少距离
                        bottom: isphone==1 ? 100 : 12,                              //图表距离容器上面多少距离
                        top: isphone==1 ? 80 : 40,                                        //图表距离容器下面多少距离
                        containLabel: true,                     //防止标签溢出  
                        
                    },
                    color: [
                        '#60C0DD','#D7504B','#C6E579','#F4E001','#26C0C0','#FCCE10',"#FA640D",'#0DD6F5','#863BF2'
                    ],
                    xAxis: {
                        type: 'category',
                        data:res.screendata10.map(item => item.name),
                        axisLabel: {
                            textStyle: {
                                color: '#01C4F7', //坐标值得具体的颜色                   
                            },
                            fontSize: fontSize,
                            formatter:function(params){
                                var newParamsName = "";// 最终拼接成的字符串
                                var paramsNameNumber = params.length;// 实际标签的个数
                                var provideNumber = 8;// 每行能显示的字的个数
                                provideNumber=getChartNum(res.screendata10.length);
                               
                                if(id==77){
                                    var screenWidth = document.documentElement.clientWidth;
                                    if(screenWidth>=1275){
                                        provideNumber=9;
                                    }else{
                                        provideNumber=5;
                                    }
                                }
                                if(id==84){
                                    provideNumber=getChartNum(res.screendata10.length);
                                }
                                var rowNumber = Math.ceil(paramsNameNumber / provideNumber);// 换行的话，需要显示几行，向上取整
                                // 条件等同于rowNumber>1
                                if (paramsNameNumber > provideNumber) {
                                    
                                    for (var p = 0; p < rowNumber; p++) {
                                        var tempStr = "";// 表示每一次截取的字符串
                                        var start = p * provideNumber;// 开始截取的位置
                                        var end = start + provideNumber;// 结束截取的位置
                                        // 此处特殊处理最后一行的索引值
                                        if (p == rowNumber - 1) {
                                            // 最后一次不换行
                                            tempStr = params.substring(start, paramsNameNumber);
                                        } else {
                                            // 每一次拼接字符串并换行
                                            tempStr = params.substring(start, end) + "\n";
                                        }
                                        newParamsName += tempStr;// 最终拼成的字符串
                                    }
                    
                                } else {
                                    // 将旧标签的值赋给新标签
                                    newParamsName = params;
                                }
                                //将最终的字符串返回
                                return newParamsName

                            }
                        }
                    },
                    yAxis: {
                        type: 'value',
                        axisLabel: {
                            textStyle: {
                                color: '#01C4F7', //坐标值得具体的颜色                   
                            },
                            fontSize: fontSize,
                        }
                    },
                    series: [{
                        data: res.screendata10,
                        type: 'bar',
                        barMaxWidth:40,
                        label: {
                                show: true,
                                position: 'top',
                                formatter:function(params){
                                    return params.data.result+"%\n"+params.data.value+"人";
                                },
                                fontSize:fontSize
                        },
                        itemStyle: {
                            opacity: 0,
                            normal:{
                                color:function(params){
                                    var colorList = [
                                        '#E87C25','#01C4F7','#FCCE10',"#FA640D",'#CB98E0','#863BF2','#087998','#1F52FA'
                                    ];
                                    return colorList[params.dataIndex];
                                }
                            }
                        },
                    }] 
                }
                myCharts.setOption(option);
                window.addEventListener("resize", function() {
                    myCharts.resize();
                }); 
            }
        }else{
            $(".div25").css("display","none");
        }
       

        if (res.screendata11) {
            $('#chart26title').html(res.screentitle11);
                let myCharts = echarts.init(document.getElementById('chart26'));
                if((res.screendata11.length>0 && res.screendata11[0].content!="" && res.screendata11[0].content!=undefined) || res.screendata11.length==0){
                    if (res.screendata11) {
                        var str="";
                        for(var i=0;i<res.screendata11.length;i++){
                            str+='<div class="jq33-content"> <div class="left"><span>'+(i+1)+"、"+res.screendata11[i].content+'</span></div></div>';
                        
                        }
                        document.getElementById('chart26').innerHTML=str;
                    }
                }else{
                
                let option = {
                    grid: {
                        show: true,                                 //是否显示图表背景网格    
                        left: 5,                                    //图表距离容器左侧多少距离
                        right: 5,                                //图表距离容器右侧侧多少距离
                        bottom: isphone==1 ? 100 : 8,                              //图表距离容器上面多少距离
                        top: isphone==1 ? 80 : 40,                                        //图表距离容器下面多少距离
                        containLabel: true,                     //防止标签溢出  
                        
                    },
                    color: [
                        '#60C0DD','#D7504B','#C6E579','#F4E001','#26C0C0','#FCCE10',"#FA640D",'#0DD6F5','#863BF2'
                    ],
                    xAxis: {
                        type: 'category',
                        data:res.screendata11.map(item => item.name),
                        axisLabel: {
                            textStyle: {
                                color: '#01C4F7', //坐标值得具体的颜色                   
                            },
                            fontSize: fontSize,
                            formatter:function(params){
                                var newParamsName = "";// 最终拼接成的字符串
                                var paramsNameNumber = params.length;// 实际标签的个数
                                var provideNumber = 8;// 每行能显示的字的个数
                                
                                if(more){
                                    provideNumber=getChart3Num(res.screendata11.length);
                                }else{
                                    provideNumber=getChart2Num(res.screendata11.length);
                                }


                                var rowNumber = Math.ceil(paramsNameNumber / provideNumber);// 换行的话，需要显示几行，向上取整
                                // 条件等同于rowNumber>1
                                if (paramsNameNumber > provideNumber) {
                                    
                                    for (var p = 0; p < rowNumber; p++) {
                                        var tempStr = "";// 表示每一次截取的字符串
                                        var start = p * provideNumber;// 开始截取的位置
                                        var end = start + provideNumber;// 结束截取的位置
                                        // 此处特殊处理最后一行的索引值
                                        if (p == rowNumber - 1) {
                                            // 最后一次不换行
                                            tempStr = params.substring(start, paramsNameNumber);
                                        } else {
                                            // 每一次拼接字符串并换行
                                            tempStr = params.substring(start, end) + "\n";
                                        }
                                        newParamsName += tempStr;// 最终拼成的字符串
                                    }
                    
                                } else {
                                    // 将旧标签的值赋给新标签
                                    newParamsName = params;
                                }
                                //将最终的字符串返回
                                return newParamsName
                            }
                        }
                    },
                    yAxis: {
                        type: 'value',
                        axisLabel: {
                            textStyle: {
                                color: '#01C4F7', //坐标值得具体的颜色                   
                            },
                            fontSize: fontSize,
                        }
                    },
                    series: [{
                        data: res.screendata11,
                        type: 'bar',
                        barMaxWidth:40,
                        label: {
                                show: true,
                                position: 'top',
                                formatter:function(params){
                                    return params.data.result+"%\n"+params.data.value+"人";
                                },
                                fontSize:fontSize
                        },
                        itemStyle: {
                            opacity: 0,
                            normal:{
                                color:function(params){
                                    var colorList = [
                                        '#E87C25','#01C4F7','#FCCE10',"#FA640D",'#CB98E0','#863BF2','#087998','#1F52FA'
                                    ];
                                    return colorList[params.dataIndex];
                                }
                            }
                        },
                    }] 
                }
                myCharts.setOption(option);
                window.addEventListener("resize", function() {
                    myCharts.resize();
                }); 
            }
        }else{
            $(".div26").css("display","none");
        }


        if (res.screendata12) {
            $('#chart27title').html(res.screentitle12);
                let myCharts = echarts.init(document.getElementById('chart27'));
                if((res.screendata12.length>0 && res.screendata12[0].content!="" && res.screendata12[0].content!=undefined) || res.screendata12.length==0){
                    if (res.screendata12) {
                        var str="";
                        for(var i=0;i<res.screendata12.length;i++){
                            str+='<div class="jq33-content"> <div class="left"><span>'+(i+1)+"、"+res.screendata12[i].content+'</span></div></div>';
                        
                        }
                        document.getElementById('chart27').innerHTML=str;
                    }
                }else{
                
                let option = {
                    grid: {
                        show: true,                                 //是否显示图表背景网格    
                        left: 5,                                    //图表距离容器左侧多少距离
                        right: 5,                                //图表距离容器右侧侧多少距离
                        bottom: isphone==1 ? 100 : 8,                              //图表距离容器上面多少距离
                        top: isphone==1 ? 80 : 40,                                        //图表距离容器下面多少距离
                        containLabel: true,                     //防止标签溢出  
                        
                    },
                    color: [
                        '#60C0DD','#D7504B','#C6E579','#F4E001','#26C0C0','#FCCE10',"#FA640D",'#0DD6F5','#863BF2'
                    ],
                    xAxis: {
                        type: 'category',
                        data:res.screendata12.map(item => item.name),
                        axisLabel: {
                            textStyle: {
                                color: '#01C4F7', //坐标值得具体的颜色                   
                            },
                            fontSize: fontSize,
                            formatter:function(params){
                                var newParamsName = "";// 最终拼接成的字符串
                                var paramsNameNumber = params.length;// 实际标签的个数
                                var provideNumber = 8;// 每行能显示的字的个数
                                
                                if(more){
                                    provideNumber=getChart3Num(res.screendata12.length);
                                }else{
                                    provideNumber=getChart2Num(res.screendata12.length);
                                }


                                var rowNumber = Math.ceil(paramsNameNumber / provideNumber);// 换行的话，需要显示几行，向上取整
                                // 条件等同于rowNumber>1
                                if (paramsNameNumber > provideNumber) {
                                    
                                    for (var p = 0; p < rowNumber; p++) {
                                        var tempStr = "";// 表示每一次截取的字符串
                                        var start = p * provideNumber;// 开始截取的位置
                                        var end = start + provideNumber;// 结束截取的位置
                                        // 此处特殊处理最后一行的索引值
                                        if (p == rowNumber - 1) {
                                            // 最后一次不换行
                                            tempStr = params.substring(start, paramsNameNumber);
                                        } else {
                                            // 每一次拼接字符串并换行
                                            tempStr = params.substring(start, end) + "\n";
                                        }
                                        newParamsName += tempStr;// 最终拼成的字符串
                                    }
                    
                                } else {
                                    // 将旧标签的值赋给新标签
                                    newParamsName = params;
                                }
                                //将最终的字符串返回
                                return newParamsName
                            }
                        }
                    },
                    yAxis: {
                        type: 'value',
                        axisLabel: {
                            textStyle: {
                                color: '#01C4F7', //坐标值得具体的颜色                   
                            },
                            fontSize: fontSize,
                        }
                    },
                    series: [{
                        data: res.screendata12,
                        type: 'bar',
                        barMaxWidth:40,
                        label: {
                                show: true,
                                position: 'top',
                                formatter:function(params){
                                    return params.data.result+"%\n"+params.data.value+"人";
                                },
                                fontSize:fontSize
                        },
                        itemStyle: {
                            opacity: 0,
                            normal:{
                                color:function(params){
                                    var colorList = [
                                        '#E87C25','#01C4F7','#FCCE10',"#FA640D",'#CB98E0','#863BF2','#087998','#1F52FA'
                                    ];
                                    return colorList[params.dataIndex];
                                }
                            }
                        },
                    }] 
                }
                myCharts.setOption(option);
                window.addEventListener("resize", function() {
                    myCharts.resize();
                }); 
            }
        }else{
            $(".div27").css("display","none");
        }
    })
}

    function hasComma(str) {
        return str.includes(",") || str.includes("，");
    }

    function hasComma2(str) {
        return str.includes("(") || str.includes("（");
    }

    Date.prototype.Format = function (fmt) { // author: meizz
    var o = {
    "M+": this.getMonth() + 1, // 月份
    "d+": this.getDate(), // 日
    "h+": this.getHours(), // 小时
    "m+": this.getMinutes(), // 分
    "s+": this.getSeconds(), // 秒
    "q+": Math.floor((this.getMonth() + 3) / 3), // 季度
    "S": this.getMilliseconds() // 毫秒
    };
    if (/(y+)/.test(fmt))
    fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
    for (var k in o)
    if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
    return fmt;
    }


    function randomNum(minNum, maxNum) {
        switch (arguments.length) {
            case 1:
                return parseInt(Math.random() * minNum + 1, 10);
                break;
            case 2:
                return parseInt(Math.random() * (maxNum - minNum + 1) + minNum, 10);
                break;
            default:
                return 0;
                break;
        }
    }
    $(document).keypress(function (event) {
        var code = (event.keyCode ? event.keyCode : event.which); 
    
        if ((code == 27)||(code == 13)){
            jsobj.closeDialogWindow(articleid);
        }
    });
});