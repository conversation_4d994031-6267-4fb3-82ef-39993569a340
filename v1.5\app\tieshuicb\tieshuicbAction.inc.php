<?php 
//require_once ("/etc/steelconf/config/isholiday.php");

include_once( APP_DIR."/tieshuicb/tieshuicbBaseAction.inc.php" );
class  tieshuicbAction extends tieshuicbBaseAction
{
    public function __construct()
    {
        parent::__construct();
    } 
	
	public function test1($params){
		
		echo "test1";
	}
	public function dscx($params)
	{
		if(date("H:i:s")<="01:50:00" && date("H:i:s")>"02:10:00")
		{
			$sql ="update ng_ModifyChengBenParameterTask set status=0 ,	cleardate='".date("Y-m-d")."' where status='1' and type='1' and ( cleardate is null or cleardate !='".date("Y-m-d")."')"; 
			$this->maindao->execute($sql);
		}
		
		
		$sql="select cbp.Date,cpt.id,cbp.type  from ng_ChengBenParameter2 cbp,  ng_ModifyChengBenParameterTask cpt where cbp.id=cpt.pid and  cpt.type='1' and cpt.status='0'";
		$lis=$this->maindao->query($sql);
		foreach($lis as $tmp)
		{	
           // $sql="select min(Date) as Date from ng_ChengBenParameter2  where Date>'".$tmp['Date']."' ";
			$sql="select min(Date) as Date from ng_ChengBenParameter2  where Date>'".$tmp['Date']."' and isDelete='0'  and type='".$tmp['type']."' order by id desc limit 1";
			$minDate=$this->maindao->getOne($sql);
			if(!$minDate)
			{
				$minDate=date("Y-m-d");
			}
			
			$type=$tmp['type']==1?101:102;
			
			// $sql="select  Date,id ,type from ng_TieHeJinChengBenIndex  where Date>='".$tmp['Date']."' and Date<'".$minDate."' and type= ".$type;
			
			// $list=$this->maindao->query($sql);
			$sql="delete from ng_TieHeJinChengBenIndex  where Date>='".$tmp['Date']."' and Date<'".$minDate."' and type= '".$type."'";
			$this->maindao->execute($sql);
			
			
			$sql ="update ng_ModifyChengBenParameterTask set status='1' where id='".$tmp['id']."'"; 
			$this->maindao->execute($sql);
			
			
			
			$holidaylist =$this->maindao->query("select date,isholiday from holiday where date>='".$tmp['Date']."' and Date<'".$minDate."'  ");
			foreach($holidaylist as $holiday )
			{
				if($holiday['isholiday']==1)
				{
					$holidaylists[]=$holiday['date'];
				}
				else
				{
					$noholidaylists[]=$holiday['date'];
				}
				
			}
			$startdate=$tmp['Date'];
			$enddate=$minDate;		
			while(strtotime($startdate)<strtotime($minDate))
			{
				
				
				if(!(in_array($startdate,$holidaylists) or date('D', strtotime($startdate)) == "Sat" or date('D', strtotime($startdate)) == 'Sun')||in_array($startdate,$noholidaylists))
				{
					$data['date']=$startdate;
					  if($tmp['type']==1)
						{
							
							$this->jstscb($data);
						}
						else if($tmp['type']==2)
						{
							$this->tstscb($data);
						}
				}
				
				$startdate=date('Y-m-d',strtotime('+1 day',strtotime($startdate)));
				
			}	
			
				// foreach($list as $tmp1)
			   // {
				   // $data['date']=$tmp1['Date'];
				    // if($tmp['type']==1)
					// {
						
						// $this->jstscb($data);
					// }
					// else if($tmp['type']==2)
					// {
						// $this->tstscb($data);
					// }
			   // }
				$sql ="update ng_ModifyChengBenParameterTask set status='2' where id='".$tmp['id']."'"; 
				$this->maindao->execute($sql);
				 echo "更新数据".$tmp['Date']."-".$minDate."数据成功";
			 
		}
		echo "执行完成";
	}
 	
	public function test2($params){
		
		
		
		$today=date("Y-m-d");
		if($params['curdate'])
		{
			if(checkdate(date("m",strtotime($params['curdate'])),date("d",strtotime($params['curdate'])),date("Y",strtotime($params['curdate']))))
			{
				$today=$params['curdate'];
			}

		}
		$date=$this->get_xhdate($today);
		//echo $date;
		$type=$params['type'];
		$data['date']=$date;
		if(empty($type))
		{
			$this->jstscb($data);
			$this->tstscb($data);
		}
		else if($type==1)
		{
			
			$this->jstscb($data);
		}
		else if($type==2)
		{
			$this->tstscb($data);
		}
		
	
		
		
		echo "执行成功";
		
		
	}
	
 	public function jstscb($params)
	{
		$date=$params['date'];
		//echo $date;
		$sql = "select * from ng_ChengBenParameter2 where Date<='".$date."' and type='1' and isDelete='0' order by Date desc ,id desc limit 1 " ;
		 $ng_ChengBenParameter2 = $this->maindao->getRow($sql);
		
		 $sql = "SELECT rd1   FROM busbankrate WHERE   `rdate` <='$date'  order by rdate desc  limit 1";
		 //echo  $sql ;
		 $busbankrate = $this->_dao->getRow($sql);
		// print_r($this->_dao);
		 //print_r($busbankrate);
		$sql ="select rd1 from rmbrate where `rdate` <='$date' order by  rdate desc limit 1";
		 $rmbraterd1 = $this->_dao->getOne($sql);//人民币汇率中间价
		 
		 $zzsl=(strtotime('2018-05-01')<=strtotime($date))?'0.16':'0.17';
         $zzsl=$ng_ChengBenParameter2['ZenZhiSuiLv'];
			// $dat=date('Y-m-01', strtotime($date));
			// $shijian=date('Y-m-d', strtotime("$dat +1 month -1 day"));
			
			//$sql = "select * from steelhome_gc.SteelCaiGou_base join steelhome_gc.SteelCaiGou_Info where SteelCaiGou_base.id = SteelCaiGou_Info.sb_id and run_date<='".$shijian."' and SteelCaiGou_base.is_show = 2 and SteelCaiGou_Info.is_show = 2 and SteelCaiGou_Info.onlyid ='977ab5ccf37dcb9dc6747aacb166cbbf' and steel_id =107 and changerate_tax!='' and the_price_tax!='' order by run_date desc limit 1 ";
			$sql = "select * from steelhome_gc.SteelCaiGou_base , steelhome_gc.SteelCaiGou_Info where SteelCaiGou_base.id = SteelCaiGou_Info.sb_id and run_date<='".$date."' and SteelCaiGou_base.is_show = 2 and SteelCaiGou_Info.is_show = 2 and SteelCaiGou_Info.onlyid ='977ab5ccf37dcb9dc6747aacb166cbbf' and steel_id ='107'  order by run_date desc limit 1 ";
			$result = $this->gcdao->getRow($sql);
			$price6786102=$result['the_price_tax'];
			//echo $price6786102;  
			//print_r($ng_ChengBenParameter2);
			//echo $busbankrate[rd1].'</br>';
			$A=$this->get_A($price6786102,$ng_ChengBenParameter2,$busbankrate['rd1']);
			
			$priceH98640=$this->selectSCPrice('H98640',$date);
			//echo $priceH98640.'<br/>'; 
			$B=$this->get_B($priceH98640,$ng_ChengBenParameter2,round($rmbraterd1/100,4),$zzsl);
			$priceV38310=$this->selectSCPrice('V38310',$date);
			$C=$this->get_C($priceV38310,$ng_ChengBenParameter2,$busbankrate['rd1'],$zzsl);
			
			$priceU36330=$this->selectSCPrice('U36330',$date);
			$D=$this->get_D($priceU36330,$ng_ChengBenParameter2,$busbankrate['rd1']);
			echo "江苏铁水";			
			echo "价格A：".$A.'</br>';
			echo "价格B：".$B.'</br>';
			echo "价格C：".$C.'</br>';
			echo "价格D：".$D.'</br>';
			$X=$this->get_X($A,$B,$C,$D,$ng_ChengBenParameter2,$zzsl);
			 echo "价格X：".$X.'</br>';
			 //indexValue Date type
			
			$sql = "select id from ng_TieHeJinChengBenIndex where type='101' and Date='".$date."' order by id desc" ;
	        $query = $this->maindao->query($sql);
			if($query!=null)
			{
			   $sql="update ng_TieHeJinChengBenIndex set indexValue='".round($X)."' where Date='".$date."' and type=101"; 
			   $this->maindao->execute($sql);
			}
			else
			{
				$sql="insert into `ng_TieHeJinChengBenIndex`(type,indexValue,Date) values(101,'".round($X)."','".$date."')";
				
				$this->maindao->execute($sql);
			}
	}
	
	
	public function tstscb($params)
	{
		$date=$params['date'];
		$sql = "select * from ng_ChengBenParameter2 where Date<='".$date."' and type='2' and isDelete='0' order by Date desc,id desc  limit 1 " ;
		 $ng_ChengBenParameter2 = $this->maindao->getRow($sql);
		
		 $sql = "SELECT rd3  FROM busbankrate WHERE   `rdate` <='$date' order by rdate desc limit 1  ";
		 $busbankrate = $this->_dao->getRow($sql);
		$sql ="select rd1 from rmbrate where `rdate` <='$date' order by rdate desc limit 1";
		 $rmbraterd1 = $this->_dao->getOne($sql);//人民币汇率中间价
		 
		 $zzsl=(strtotime('2018-05-01')<=strtotime($date))?'0.16':'0.17';
		 $zzsl=$ng_ChengBenParameter2['ZenZhiSuiLv'];
		 $price6786102=$this->selectSCPrice('6786102',$date);
			 $A=$this->get_A2($price6786102,$ng_ChengBenParameter2,$busbankrate['rd3']);
			 $priceH98640=$this->selectSCPrice('H98640',$date);
			 $B=$this->get_B($priceH98640,$ng_ChengBenParameter2,round($rmbraterd1/100,4),$zzsl);
			 $price678311=$this->selectSCPrice('678311',$date);
			 //echo $price678311;
			 $C=$this->get_C2($price678311,$ng_ChengBenParameter2,$busbankrate['rd3']);
			
			 $priceV263303=$this->selectSCPrice('V263303',$date);
			 $D=$this->get_D($priceV263303,$ng_ChengBenParameter2,$busbankrate['rd3']);
			 $X=$this->get_X($A,$B,$C,$D,$ng_ChengBenParameter2,$zzsl);
			
			echo "唐山铁水";			
			echo "价格A：".$A.'</br>';
			echo "价格B：".$B.'</br>';
			echo "价格C：".$C.'</br>';
			echo "价格D：".$D.'</br>';
			 echo "价格X：".$X.'</br>';
			$sql = "select id from ng_TieHeJinChengBenIndex where type='102' and Date='".$date."' order by id desc" ;
	        $query = $this->maindao->query($sql);
			if($query!=null)
			{
				$sql="update ng_TieHeJinChengBenIndex set indexValue='".round($X)."' where Date='".$date."' and type=102"; 
			   $this->maindao->execute($sql);
			}
			else
			{
				$sql="insert into `ng_TieHeJinChengBenIndex`(type,indexValue,Date) values(102,'".round($X)."','".$date."')";
				
				$this->maindao->execute($sql);
			}
		 
		 
		 
	} 
	
	
   // 1查询 市场价格
    public function selectSCPrice($topicture,$mconmanagedate) {//根据id，指定日期获取价格
		
		if(strlen($topicture)=='6'){
			$where = " and topicture = '$topicture' ";
		}else if (strlen($topicture)=='7'){
			$where = " and mastertopid = '$topicture' ";
		}
		
    	$sql = "select price,pricemk from marketconditions where mconmanagedate<='$mconmanagedate 23:59:59' $where order by mconmanagedate desc limit 1";
        if($topicture=="V383102"){
            //echo $sql;
        }
    	$price1 = $this->maindao->getRow($sql);
		if($topicture!='H98640')
		{
			$price=$price1['price'];
		}
		else
		{
			$price=$price1['pricemk'];
		}
    	return $price;
    }

		function get_xhdate($today)//根据当前日获取上个钢之家工作日
		{
			    $flag=1;
				$lastday=$today;
					while(true)
					{
						$lastday=date('Y-m-d',strtotime('-'.$flag.' day',strtotime($lastday)));
						//echo $lastday;
						if(file_get_contents(APP_URL_WWW.'/isholiday.php?date='.$lastday)!="1")
						{ 
						  break;
						} 
					}
				$today_s=$today;
				$lastday_s=$lastday;
			

			return  $lastday_s;
		}
		
 		 function get_A($price,$ChengBenParameter2)
		{
			//echo $price;
			//echo  "a=($price+$ChengBenParameter2[DaoChangYunFeiA])*(1+$ChengBenParameter2[TuSunA])*$ChengBenParameter2[BiLiA]|";
			return ($price+$ChengBenParameter2['DaoChangYunFeiA'])*(1+$ChengBenParameter2['TuSunA'])*$ChengBenParameter2['BiLiA'];
			
			 //A=（【价格】+【到厂运费】）*（1+【途损】）*【比例】
		}
		function get_B($price,$ChengBenParameter2,$zjj,$zzsl)
		{
			//echo "B价格计算：($price*$zjj*(1+$zzsl)+$ChengBenParameter2[GangZaiFeiB]+$ChengBenParameter2[DaoChangYunFeiB])*(1+$ChengBenParameter2[TuSunB])*$ChengBenParameter2[BiLiB] ".'<br/>';

			return ((float)$price*(float)$zjj*(1+(float)$zzsl)+(float)$ChengBenParameter2['GangZaiFeiB']+(float)$ChengBenParameter2['DaoChangYunFeiB'])*(1+(float)$ChengBenParameter2['TuSunB'])*(float)$ChengBenParameter2['BiLiB'];
			
			 //B=（【普氏指数】*【人民币汇率中间价】/100*(1+【增值税率】)+【港杂】+【到厂运费】）*（1+途损）*【比例】
		}
		function get_C($price,$ChengBenParameter2,$txl,$zzsl)
		{
         // echo  $price; echo "$txl";
		  //echo "C价格： (($price*(1-$txl/1000*3)+$ChengBenParameter2[DaoChangYunFeiC])/(1-$ChengBenParameter2[HanShuiLvC])*$ChengBenParameter2[WaiGouBiLiC]+(($price*(1-$txl/1000*3)+$ChengBenParameter2[DaoChangYunFeiC])/(1-$ChengBenParameter2[HanShuiLvC])-$ChengBenParameter2[ZiChanWaiGouJiaChaC]*(1+$zzsl))*(1-$ChengBenParameter2[WaiGouBiLiC]))*$ChengBenParameter2[JiaoBiC]".'<br/>';
			return (((float)$price*(1-(float)$txl/1000*3)+(float)$ChengBenParameter2['DaoChangYunFeiC'])/(1-(float)$ChengBenParameter2['HanShuiLvC'])*(float)$ChengBenParameter2['WaiGouBiLiC']+(((float)$price*(1-(float)$txl/1000*3)+(float)$ChengBenParameter2['DaoChangYunFeiC'])/(1-(float)$ChengBenParameter2['HanShuiLvC'])-(float)$ChengBenParameter2['ZiChanWaiGouJiaChaC']*(1+(float)$zzsl))*(1-(float)$ChengBenParameter2['WaiGouBiLiC']))*(float)$ChengBenParameter2['JiaoBiC']; 
			//C=（（【采购价格】*（1-贴现率/1000*3）+【到厂运费】）/（1-【含水率】）*【外购比例】+（（【采购价格】*（1-贴现率/1000*3）+【到厂运费】）/（1-【含水率】）-【自产外购价差】*（1+【增值税率】）*（1-外购比例）））*【焦比】
		}
		function get_D($price,$ChengBenParameter2,$txl)
		{
			
			//echo "D价格：($price*(1-$txl/1000*3)+$ChengBenParameter2[DaoChangYunFeiD])/(1-$ChengBenParameter2[HanShuiLvD])*$ChengBenParameter2[PenMeiLiangD]"."<br/>";
            return ((float)$price*(1-(float)$txl/1000*3)+(float)$ChengBenParameter2['DaoChangYunFeiD'])/(1-(float)$ChengBenParameter2['HanShuiLvD'])*(float)$ChengBenParameter2['PenMeiLiangD'];
			//D=（【采购价格】*（1-贴现率/1000*3）+【到厂运费】）/（1-【含水率】）*【喷煤量】
		} 
		function get_X($A,$B,$C,$D,$ChengBenParameter2,$zzsl)
		{
			return  ($A+$B)*$ChengBenParameter2['DunTieKuangHao']+$C+$D+($ChengBenParameter2['LianTieFuLiao']+$ChengBenParameter2['ShaoJieFuLiaoRanLiao']+$ChengBenParameter2['ZhiZhaoFeiYong']+$ChengBenParameter2['LianTieNengYuanDongli']+$ChengBenParameter2['ShaoJieNengYuanDongLi']+$ChengBenParameter2['QiuTuanNengYuanDongLi'])*(1+$zzsl);
		   //铁水成本X=（A+B）*【吨铁矿耗】+C+D+（【炼铁辅料】+【烧结辅料燃料】+【制造费用】+【炼铁能源动力】+【烧结能源动力】+【球团能源动力】）*（1+【增值税率】）
		             //X=（A+B）*【吨铁矿耗】+C+D+（【炼铁辅料】+【烧结辅料燃料】+【制造费用】+【炼铁能源动力】+【烧结能源动力】+【球团能源动力】）*（1+【增值税率】）
		} 
       function get_A2($price,$ChengBenParameter2,$txl)
	   {
		  return  ((float)$price*(1-(float)$txl*3/1000)+(float)$ChengBenParameter2['DaoChangYunFeiA'])*(1+(float)$ChengBenParameter2['TuSunA'])*(float)$ChengBenParameter2['BiLiA'];
	      //A=（【价格】*（1-【承兑汇票贴现率】*3/1000）+【到厂运费】）*（1+【途损】）*【比例】
	   }
	
		function get_C2($price,$ChengBenParameter2,$txl)
	   {
		   //echo $price;
		   //echo  "(($price*(1-$txl/1000*3)+$ChengBenParameter2[DaoChangYunFeiC])/(1-$ChengBenParameter2[HanShuiLvC])*$ChengBenParameter2[WaiGouBiLiC])*$ChengBenParameter2[JiaoBiC]";
		   return (((float)$price*(1-(float)$txl/1000*3)+(float)$ChengBenParameter2['DaoChangYunFeiC'])/(1-(float)$ChengBenParameter2['HanShuiLvC'])*(float)$ChengBenParameter2['WaiGouBiLiC'])*(float)$ChengBenParameter2['JiaoBiC'];
		   //C=（（【采购价格】*（1-贴现率/1000*3）+【到厂运费】）/（1-【含水率】）*【外购比例】）*【焦比】
  
	   }
	   
		
} 
?>
