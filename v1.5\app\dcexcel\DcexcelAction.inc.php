<?php
ini_set("memory_limit", "256M");
ob_start('ob_gzhandler');

class DcexcelAction extends AbstractAction{

	public function __construct(){
	parent::__construct();
	}

	/**
	* 函数名称: getIP
	* 函数功能: 取得手机IP
	* 输入参数: none
	* 函数返回值: 成功返回string
	* 其它说明: 说明
	*/
	private function getIP() {
		$ip=getenv('REMOTE_ADDR');
		$ip_ = getenv('HTTP_X_FORWARDED_FOR');
		if (($ip_ != "") && ($ip_ != "unknown")) {
			$ip=$ip_;
		}
		$ip=explode(",",$ip);
		return $ip[0];
	}

	private function pri_JSON($array) {   
		$this->pri_arrayRecursive($array, 'urlencode', true);      
		$json = json_encode($array);  
		return urldecode($json);
	}   

	private function pri_arrayRecursive(&$array, $function, $apply_to_keys_also = false)
	{
		static $recursive_counter = 0;
		if (++$recursive_counter > 1000) {
			die('possible deep recursion attack');
		}
		foreach ($array as $key => $value) {
			if (is_array($value)) {
				$this->pri_arrayRecursive($array[$key], $function, $apply_to_keys_also);
			} else {
				$array[$key] = $function($value);
				//$array[$key] = $function(addslashes($value));
			}
			if ($apply_to_keys_also && is_string($key)) {
				$new_key = $function($key);
				if ($new_key != $key) {
					$array[$new_key] = $array[$key];
					unset($array[$key]);
				}
			}
		}
		$recursive_counter--;
	}

	public function excellogin($params)
	{
		$username = trim( $params['username'] );
		$password = md5( $params['password'] );
		
		$user = $this->stedao->checkuser( $username, $password );

		if(!empty($user)){
			$this->InsertLog($user['mid'],$user['id'],$user['truename'],"excel插件抓取",$user['truename']."excel插件登录 ".date('Y-m-d H:i:s',time()));
			$arr['Success'] = "1";
			$arr['Result'] =  $user;
		}else{
			$arr['Success'] = "0";
			$arr['Message'] =  '用户名密码不匹配！';
		}
		
		$json_string = $this->pri_JSON($arr);
		echo $json_string;
	}

	function getpricebypricestr($pricestr,$stime,$etime,$desc)
	{
		$shipiflag=0;
		$idnumber=explode(',',$pricestr);
		$six=$seven=array();
		foreach($idnumber  as $id ){
			if(strpos($id,'+') !== false || strpos($id,' ') !== false || strpos($id,'-') !== false)
			{
				if(strpos($id,'+') !== false)
					$IDcal = explode('+',$id);
				if(strpos($id,' ') !== false)
					$IDcal = explode(' ',$id);
				if(strpos($id,'-') !== false)
					$IDcal = explode('-',$id);
				$id = trim($IDcal[0]);
			}

			if(strlen ( $id ) == 5)
				$id = "0".$id;

			if (strlen ( $id ) == 6){//判断id 的字符长度 
				if(!in_array($id ,$six)){
					$six[]= $id ; 			
				} 							
			} 
			if (strlen ($id ) == 7) {
				if(!in_array($id,$seven)){
					$seven[]= $id;
				} 
			}

			if(strlen ($id ) == 24) //钢材价格指数点位
			{
				$shipiflag=1;
			}
		}
		//echo '<pre/>';print_r($seven);exit;
		if($desc == 1)
			$order=" order by mconmanagedate desc ";
		else
			$order=" order by mconmanagedate asc ";
     
		$sixid_str=implode("','",$six);
 		$sevenid_str=implode("','",$seven);
		$mconmanagedate.="(mconmanagedate>='".$stime." 00:00:00' and mconmanagedate<='".$etime." 23:59:59')";
		if($sixid_str!=''){
			$PriceListSQL=" select price,marketconditions.topicture, marketconditions.mconmanagedate from marketconditions where $mconmanagedate and  marketconditions.topicture in ('$sixid_str') $order ";
		}
		if($sevenid_str!=''){
			$PriceListSQL=" select price,marketconditions.mastertopid as topicture, marketconditions.mconmanagedate from marketconditions where $mconmanagedate and  marketconditions.mastertopid in ('$sevenid_str') $order";
		}
		if($sixid_str!=''&&$sevenid_str!=''){
			$PriceListSQL="select * from ( select price,topicture, marketconditions.mconmanagedate from marketconditions where $mconmanagedate and topicture in ('$sixid_str') UNION( select price,mastertopid as topicture,mconmanagedate from marketconditions where $mconmanagedate and mastertopid in('$sevenid_str'))) as tab  $order ";
		}
		//echo $PriceListSQL;exit;
		$PriceList= $this->stedao->query( $PriceListSQL ); 

		if($shipiflag == 1)
		{
			$shipisql="select dateday,wpindex from shpi_pp where bc_id=4 and dateday>='".$stime." 00:00:00' and dateday<='".$etime." 23:59:59'";
			$shipiList= $this->stedao->query( $shipisql ); 
			
			foreach($shipiList  as $shipi )
			{
				$spdata[$shipi['dateday']] = round($shipi['wpindex'],2);
			}
		}
		$data=array();//日期数组
		$date=array();
		
		foreach($PriceList  as $v )
        {
			$date11=date('Y-m-d',strtotime($v['mconmanagedate']));
			if(!in_array( $date11,$date))
			{
				$date[]= $date11;
				$dataarr[$date11][$v['topicture']]=$v['price'];
			}
			$dataarr[$date11][$v['topicture']]=$v['price'];
        }

		foreach($date as $dt )
        {
			$dataarr[$dt]['steelshpi']=$spdata[$dt];
        }
		//echo '<pre/>';print_r($dataarr);exit;
		$data1['price']=$dataarr;
		$data1['date']=$date;
		return $data1;
          
	}

	public function updatepricedata( $params ){
		$pricestr = $params['pricestr'];
		$stime = $params['stime'];
		$desc = $params['desc'];
		$etime = date('Y-m-d',time());

		if(strtotime($stime)>strtotime($etime))
		{
			$arr['Success'] = "0";
			$arr['Message'] =  '当前已为最新数据！';
		}
		$pricearr = explode (",", $pricestr);
		$datalist=$this->getpricebypricestr($pricestr,$stime,$etime,$desc);
		
		foreach($datalist['date'] as $val) 
		{
			$pricedt = "";
			foreach($pricearr as $val1) 
			{
				if(strlen ( $val1 ) == 5)
					$val1 = "0".$val1;
				if(strlen ( $val1 ) == 24)//钢材价格指数点位
					$val1 = "steelshpi";
				
				if(strpos($val1,'-') !== false){ 
					$IDcal = explode('-',$val1);
					$price = $datalist['price'][$val][$IDcal[0]] ? ($datalist['price'][$val][$IDcal[0]]-$IDcal[1]) : "";
				}
				elseif(strpos($val1,'+') !== false || strpos($val1,' ') !== false){ 
					$IDcal = explode('+',$val1);
					$IDcal = explode(' ',$val1);
					$price = $datalist['price'][$val][$IDcal[0]] ? ($datalist['price'][$val][$IDcal[0]]+$IDcal[1]) : "";
				}
				else
					$price = $datalist['price'][$val][$val1] ? $datalist['price'][$val][$val1] : "";

				$pricedt .= $price.',';
			}
			$pricedata[] = str_replace("-","/",$val).','.substr($pricedt,0,strlen($pricedt)-1);
		}
		//echo '<pre/>';print_r($pricedata);exit;
		if(!empty($pricedata))
		{
			$this->InsertLog($params['mid'],$params['uid'],iconv("utf-8","gb2312",$params['username']),"excel插件抓取",$stime."~".$etime."价格ID抓取",iconv("utf-8","gb2312",$pricestr));
			$arr['Success'] = "1";
			$arr['Result'] =  $pricedata;
		}
		else
		{
			$arr['Success'] = "0";
			$arr['Message'] =  '无数据更新！';
		}

		$json_string = $this->pri_JSON($arr);
		echo $json_string;
	}

	public function updateshipidata( $params ){
		$stime = $params['stime'];
		$etime = date('Y-m-d',time());
		if(strtotime($stime)>=strtotime($etime))
		{
			$arr['Success'] = "0";
			$arr['Message'] =  '当前已为最新数据！';
		}
		
		$shipisql = "select dateday,wpindex,weiprice from shpi_pi where dateday>'$stime' and dateday<='$etime'";
		$shipiList= $this->stedao->query( $shipisql );

		foreach($shipiList as $val) 
		{
			$val['dateday'] = str_replace("-","/",$val['dateday']);
			$val['weiprice'] = round($val['weiprice'],0);
			
			$shipidata[] = implode(",",$val);
		}
		
		if(!empty($shipidata))
		{
			$this->InsertLog($params['mid'],$params['uid'],iconv("utf-8","gb2312",$params['username']),"excel插件抓取",$stime."~".$etime."钢材指数抓取");
			$arr['Success'] = "1";
			$arr['Result'] =  $shipidata;
		}
		else
		{
			$arr['Success'] = "0";
			$arr['Message'] =  '无数据更新！';
		}

		$json_string = $this->pri_JSON($arr);
		echo $json_string;
	}

	public function updatekucundata( $params ){
		$stime = $params['stime'];
		$entime = explode('-',$stime);
		if(!empty($entime))
		{
			if($entime[1] == "Jan")
				$stime = "20".$entime[2]."-01-".(strlen($entime[0]) == 1 ? "0".$entime[0] : $entime[0]);
			elseif($entime[1] == "Feb")
				$stime = "20".$entime[2]."-02-".(strlen($entime[0]) == 1 ? "0".$entime[0] : $entime[0]);
			elseif($entime[1] == "Mar")
				$stime = "20".$entime[2]."-03-".(strlen($entime[0]) == 1 ? "0".$entime[0] : $entime[0]);
			elseif($entime[1] == "Apr")
				$stime = "20".$entime[2]."-04-".(strlen($entime[0]) == 1 ? "0".$entime[0] : $entime[0]);
			elseif($entime[1] == "May")
				$stime = "20".$entime[2]."-05-".(strlen($entime[0]) == 1 ? "0".$entime[0] : $entime[0]);
			elseif($entime[1] == "Jun")
				$stime = "20".$entime[2]."-06-".(strlen($entime[0]) == 1 ? "0".$entime[0] : $entime[0]);
			elseif($entime[1] == "Jul")
				$stime = "20".$entime[2]."-07-".(strlen($entime[0]) == 1 ? "0".$entime[0] : $entime[0]);
			elseif($entime[1] == "Aug")
				$stime = "20".$entime[2]."-08-".(strlen($entime[0]) == 1 ? "0".$entime[0] : $entime[0]);
			elseif($entime[1] == "Sep")
				$stime = "20".$entime[2]."-09-".(strlen($entime[0]) == 1 ? "0".$entime[0] : $entime[0]);
			elseif($entime[1] == "Oct")
				$stime = "20".$entime[2]."-10-".(strlen($entime[0]) == 1 ? "0".$entime[0] : $entime[0]);
			elseif($entime[1] == "Nov")
				$stime = "20".$entime[2]."-11-".(strlen($entime[0]) == 1 ? "0".$entime[0] : $entime[0]);
			elseif($entime[1] == "Dec")
				$stime = "20".$entime[2]."-12-".(strlen($entime[0]) == 1 ? "0".$entime[0] : $entime[0]);
		}
		$etime = date('Y-m-d',time());

		$kucunsql = "select date,dta_4,dta_2 from DataTableBaseInfo as db left join data_table as dt on db.id=dt.baseid where db.DataType='Java-zygkmtkctj' and date>'$stime' and date<='$etime'";
		$kucunList= $this->ngdrc->query( $kucunsql );

		foreach($kucunList as $val) 
		{
			$ennewtime = gmstrftime("%d-%b",strtotime($val['date']));
			$kucundata[$val['date']][$val['dta_2']] = $val['dta_4'] * 10;
		}

		foreach($kucundata as $key=>$data) 
		{
			$kcdata[] = gmstrftime("%d-%b",strtotime($key)).'-'.$entime[2].','.implode(",",$data);
		}
		
		if(!empty($kcdata))
		{
			$this->InsertLog($params['mid'],$params['uid'],iconv("utf-8","gb2312",$params['username']),"excel插件抓取",$stime."~".$etime."煤焦库存统计抓取");
			$arr['Success'] = "1";
			$arr['Result'] =  $kcdata;
		}
		else
		{
			$arr['Success'] = "0";
			$arr['Message'] =  '无数据更新！';
		}

		$json_string = $this->pri_JSON($arr);
		echo $json_string;
	}

	function InsertLog( $mid,$uid,$username,$ActionName,$MessageTitle,$MessageDesc="" ){
		//echo '<pre/>';print_r($this->_dao);exit;
		$this->_dao->WriteLog($mid, $uid, $username, $ActionName, $this->getIP(), $MessageTitle, $MessageDesc);
	}

	public function dolog( $params ){
		
		$this->setDao->WriteLog($params['mid'], $params['uid'], iconv('utf-8', 'gb2312', $params['ActionName']), $this->getIP(), iconv('utf-8', 'gb2312', $params['MessageTitle']), $params['MessageDesc']);
		 
		$arr['Success'] = "1";
		$arr['Message'] =  '日志记录！';
		$json_string = $this->pri_JSON($arr);
		echo $json_string;
	}
}
?>
