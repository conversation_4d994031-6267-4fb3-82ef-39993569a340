.header {  width: 100%;   height: 1.4rem; position:relative; overflow:hidden;}
.header:before{ content:''; display:block; position:absolute; top:1px; left:0; right:0; width:100%; height:1px; background:#126ABA;}

.header .header-left { width: 30%; height: 1.05rem; text-indent:25px; line-height: 1.05rem}

.header .header-center { width: 40%; position: relative;}

.header .header-center .header-title { text-align: center; color: #92C5EF; font-size: .5rem; font-weight:bold; /*letter-spacing:5px;*/ padding:0.3rem 0; font-family:"SimHei";}
.date{ text-align:center; font-size:16px; font-weight:bold;}

.header .header-img {
    background: url(../../images/title.png) no-repeat center center;
    background-size: 100%;
    height: 100%;
    width: 100%;
    position: absolute;
    top: .2rem;
}

.header .header-right {
    width: 30%;
    height: 1.05rem;
}

.header .header-bottom {
    width: calc(100% - .4rem);
    height: .16rem;
    background: url(../../images/header.png) no-repeat;
    background-size: calc(100% - .2rem) 100%;
    padding: 0 .2rem;
    margin-left: .3rem;
}

.center { padding: 0 .8rem; overflow:hidden;}

.zuo{ position:fixed; left:0; width:0.4rem; height:1rem; background:rgba(0,0,0,0.6); border:1px solid #004F9D; border-left:0; border-radius:0 5px 5px 0; top:50%; margin-top:-.5rem;}
.zuo:hover{ opacity:0.8;}
.zuo img{ margin:0.35rem 0.13rem 0; width:0.14rem;}

.you{ position:fixed; right:0; width:0.4rem; height:1rem; background:rgba(0,0,0,0.6); border:1px solid #004F9D; border-right:0; border-radius:5px 0  0 5px; top:50%; margin-top:-.5rem;}
.you:hover{ opacity:0.8;}
.you img{ margin:0.35rem 0.13rem 0; width:0.14rem;}

.center .center-left {    width: 28%;}

.center .center-left .left-top {
    width: 100%;
    height: 4rem;
    margin-bottom: .4rem
}

.center .center-left .left-top h1 {
    font-size: .4rem;
    transform: rotateX(0deg);
    align-items: center;
    margin-bottom: 8px;
    position: relative;
    -webkit-transform-style: preserve-3d;
    -moz-transform-style: preserve-3d;
    -ms-transform-style: preserve-3d;
    transform-style: preserve-3d;
    transform-origin: 50% 50%;
    transition: transform 500ms cubic-bezier(.15, .52, .5, .9) 0s;
    transition: all 1s;
}

.title {
    width: 100%;
    height: 0.5rem;
	font-size:0.2rem;
	font-weight:bold;
	background:url(../../images/navbar.png)no-repeat 0 0; background-size:100%;
	text-align:center;
    line-height: 0.44rem;
}

.navbar{font-size:16px;	font-weight:bold;text-align:center;}

.top-list p {
    font-size: .18rem;
    color: #029698;
    padding: .3rem
}

.top-list li {
    float: left;
    font-size: .2rem;
    width: 50%;
    height: .4rem;
    line-height: .4rem;
    padding: .2rem
}

.center .center-left .left-cen {
    width: 100%;
    height: 4rem;
    margin-bottom: .4rem
}

.center .center-left .left-cen .company {
    width: calc(100% - .2rem);
    height: calc(100% - .6rem);
    margin-left: .1rem;
    margin-top: .1rem;
    padding: .2rem;
    box-sizing: border-box
}

.center .center-left .left-cen .company li {
    height: .3rem;
    line-height: .3rem
}

.center .center-left .left-bottom {
    width: 100%;
    height: 3.2rem
}

.center .center-left .bottom-b {
    width: calc(100% - 0.3rem);
    height: 3.2rem;
    margin-left: .3rem;
}

.center .center-cen {
    width: 40%;
	margin-top:0.3rem;
	margin-left:2%;
}

.center .center-cen .cen-top {
    width: 100%;
    height: 6.8rem;
    margin-bottom: .4rem;
    position: relative;
	background:none;
	border:0;
}

.center-cen .border:after{ border-left:0; border-right:0;}
.center-cen .border:before{ border-top:0; border-bottom:0;}

.center .center-cen .cen-top .top-title {
    position: absolute;
    width: 35%;
    height: 1.5rem;
    left: .5rem;
    top: .5rem
}

.center .center-cen .cen-top .top-title li {
    float: left;
    width: 50%;
    height: 1rem
}

.center .center-cen .cen-top .top-title li p,
.center .center-cen .cen-top .top-title li span,
.center .center-cen .cen-top .top-title li b {

}

.center .center-cen .cen-top .top-title li p {
    padding: .1rem
}

.center .center-cen .cen-top .top-title li span {
    text-align: center;
    display: inline-block;
    width: .35rem;
    height: .45rem;
    background: #37a9ea;
    margin-left: .1rem;
    line-height: .4rem;
    font-size: .3rem;
    margin-top: .1rem
}

.center .center-cen .cen-top .top-bottom {
    width: 96%;
	margin:0 auto;
    height: 4.3rem;
}

.eye{ display:table; margin:0.3rem auto 0;}

.top-bottom{}
.top-bottom1 span{font-size:0.8rem; font-family:"SimSun"; color:#ffffff; font-weight:normal; margin:0 0.15rem; padding:0.15rem 0.08rem; background:#134094; border:1px solid #2270E7;}

.top-bottom1{ display:table; margin:0.1rem auto; font-size:14px; font-weight:bold; overflow:hidden;}
.top-bottom1 div,.top-bottom1 p{ float:left;}
.top-bottom1 p{ position:relative; top:1rem;}
.center .center-cen .cen-top .top-bottom1 div {
    font-size: .8rem;
    text-align: center;
    display:inline-block;
    margin: 0 auto;
    line-height: 1.6rem
}

.production_line{ width: 100%; overflow:hidden; font-size:0.35rem;font-weight:bold; text-align:center;font-family:SimHei}

.center .center-cen .cen-bottom {
    width: 100%;
    height: 3.2rem
}

.center .center-cen .bottom-b {
    width: calc(100% - 0.2rem);
    height: 2.6rem;
    margin-left: .1rem;
    margin-top: .1rem
}

.center .center-right { width: 28%;}

.center .center-right .right-top {
    width: 100%;
    height: 4rem;
    margin-bottom: .4rem
}

.center .center-right .right-top .right-top-top {
    width: calc(100% - 0.3rem);
    height: 3.2rem;
    margin-left: .3rem;
}


.center .center-right .right-cen {
    width: 100%;
    height: 4rem;
    border: 1px solid #1A76C1;
   background:#021245; 
    transition: all 1s;
    cursor: pointer;
    position: relative
}

.center .center-right .right-foot{ text-indent:0.2rem; font-weight:bold;}

 .center .center-right .right-cen .right-cen-cent {
    width: calc(100% - 0.3rem);
    height: 3.2rem;
    margin-left: .3rem;
}

.center .center-right .right-bottom {
    width: 100%;
    height: 3.2rem
}

.center .center-right .right-bottom .chat { width: calc(100% - 0.2rem); height: 2.6rem; margin-left: .1rem; margin-top: .1rem;}

.gun { margin-top: .2rem}

.gun span {
    display: block;
    float: left;
    height: .2rem;
    width: 33%;
    text-align: center;
    font-size: .14rem;
    font-weight: 600;
    color: #61d2f7;
    text-align: center;
    margin-bottom: .1rem
}

#FontScroll {
    width: 100%;
    height: 2.3rem;
    overflow: hidden;
    margin-top: .1rem
}

#FontScroll ul li {
    height: .32rem;
    width: 100%;
    text-align: center;
    line-height: .32rem;
    overflow: hidden;
    font-size: .14rem
}

#FontScroll ul li:nth-child(1) {
    box-shadow: -10px 0 15px #034c6a inset, 10px 0 15px #034c6a inset
}

#FontScroll ul li:hover {
    box-shadow: -10px 0 15px #034c6a inset, 10px 0 15px #034c6a inset;
    cursor: pointer
}

.fontInner span {
    display: inline-block;
    width: 31%;
}

.fontInner span b {
    display: inline-block;
    width: .2rem;
    height: .2rem;
    border-radius: .03rem;
    line-height: .2rem
}

#FontScroll ul li:nth-child(8n-4) .fontInner span b {
    background: #20a8fe
}

#FontScroll ul li:nth-child(8n-3) .fontInner span b {
    background: #eb6841
}

#FontScroll ul li:nth-child(8n-2) .fontInner span b {
    background: #3fb8af
}

#FontScroll ul li:nth-child(8n-1) .fontInner span b {
    background: #fe4365
}

#FontScroll ul li:nth-child(8n-5) .fontInner span b {
    background: #fc9d9a
}

#FontScroll ul li:nth-child(8n-6) .fontInner span b {
    background: #edc951
}

#FontScroll ul li:nth-child(8n-7) .fontInner span b {
    background: #c8c8a9
}

#FontScroll ul li:nth-child(8n) .fontInner span b {
    background: #83af9b
}

#FontScroll ul li:first-child .fontInner span b {
    background: #036564
}

#FontScroll ul li:last-child .fontInner span b {
    background: #3299bb
}


.el-loading-mask {
    position: absolute;
    z-index: 2000;
    margin: 0;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    transition: opacity .3s
}


.el-loading-parent--relative {
    position: relative!important
}

.el-loading-parent--hidden {
    overflow: hidden!important
}

.el-loading-mask {
    position: absolute;
    z-index: 2000;
    margin: 0;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    transition: opacity .3s
}

.el-loading-mask.is-fullscreen {
    position: fixed
}

.el-loading-mask.is-fullscreen .el-loading-spinner {
    margin-top: -25px
}

.el-loading-mask.is-fullscreen .el-loading-spinner .circular {
    height: 50px;
    width: 50px
}

.el-loading-spinner {
    top: 50%;
    margin-top: -21px;
    width: 100%;
    text-align: center;
    position: absolute
}

.el-loading-spinner .el-loading-text {
    color: #0c69b3;
    margin: 3px 0;
    font-size: 14px
}

.el-loading-spinner .circular {
    height: 42px;
    width: 42px;
    animation: k 2s linear infinite
}

.el-loading-spinner .path {
    animation: j 1.5s ease-in-out infinite;
    stroke-dasharray: 90,150;
    stroke-dashoffset: 0;
    stroke-width: 2;
    stroke: #0c69b3;
    stroke-linecap: round
}

.el-loading-spinner i {
    color: #0c69b3
}

.el-loading-fade-enter,.el-loading-fade-leave-active {
    opacity: 0
}

@keyframes j {
    0% {
        stroke-dasharray: 1,200;
        stroke-dashoffset: 0
    }

    50% {
        stroke-dasharray: 90,150;
        stroke-dashoffset: -40px
    }

    to {
        stroke-dasharray: 90,150;
        stroke-dashoffset: -120px
    }
}

.layui-layer-content{color:#000}