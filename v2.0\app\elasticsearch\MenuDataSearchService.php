<?php
require_once('ElasticsearchService.php');
class MenuDataSearchService
{
    private $es;
    private $current_mc_type;

    public function __construct()
    {
        $this->es = new ElasticsearchService();
    }

    public function search($keyword,$mc_type)
    {
        // 设置当前的mc_type
        $this->current_mc_type = $mc_type;

        // 执行组合查询
        $searchResults = $this->combinedSearch($keyword,$mc_type);
        // 处理结果
        $result = $this->processSearchResults($searchResults);

        return $result;
    }

    private function combinedSearch($keyword,$mc_type)
    {
        $keyword = htmlspecialchars_decode($keyword);
        // 修改查询结构，使用更宽松的匹配规则
        $params = [
            'index' => ['code_class_index', 'code_datatype_index'],
            'body' => [
                'query' => [
                    'bool' => [
                        'must' => [ // 添加 mc_type 的精确匹配条件
                            [
                                'term' => [
                                    'mc_type' => $mc_type // 替换为需要匹配的值
                                ]
                            ]
                        ],
                        'should' => [
                            // 使用wildcard进行模糊匹配
                            [
                                'wildcard' => [
                                    'sname' => "*${keyword}*"
                                ]
                            ],
                            [
                                'wildcard' => [
                                    'dtname' => "*${keyword}*"
                                ]
                            ]
                        ],
                        'minimum_should_match' => 1 // 至少匹配一个 should 条件
                    ]
                ],
                '_source' => [
                    'includes' => [
                        'id', 'scode*', 'sname', 'dtname', 'sod', 'level','parent_scode', 'full_path','DataImage','unit','TargetFlagVar','subAtt1Db','subAtt1Db_en','subDatasTitle','subDatasTitle_en','level','dtymd','data_releasetime','pinzhong','province','city'
                    ]
                ],
                'size' => 1000,
                'sort' => [
                    '_score' => 'desc',
                    'sod' => 'asc'
                ]
            ]
        ];

        // 添加调试信息
        try {
            $result = $this->es->search($params);
            if (empty($result['hits']['hits'])) {
                // 记录搜索参数和结果
                error_log("Search params: " . json_encode($params));
                error_log("Search result: " . json_encode($result));
            }
            return $result;
        } catch (\Exception $e) {
            error_log("ES search error: " . $e->getMessage());
            throw $e;
        }
    }

    private function processSearchResults($searchResults)
    {
        $menuMatches = [];
        $dataMatches = [];
        $allScodes = [];
        // 一次性收集所有需要的scode
        foreach ($searchResults['hits']['hits'] as $hit) {
            $source = $hit['_source'];
            $source['isSearch'] = 1;
            if ($hit['_index'] === 'code_class_index') {
                $menuMatches[] = $source;
                $this->collectScodes($source['full_path'], $allScodes);
            } else {
                $dataMatches[] = $source;
                // 收集数据关联的所有菜单码
                for ($i = 1; $i <= 5; $i++) {
                    if (!empty($source['scode' . $i])) {
                        $this->collectDataScodes($source['scode' . $i], $allScodes);
                    }
                }
            }
        }
        $allScodes = array_unique($allScodes);

        // 一次性批量查询所有需要的菜单
        $allMenus = $this->batchGetMenus($allScodes);
        // 构建结果
        return $this->buildResultTrees($menuMatches, $dataMatches, $allMenus);
    }

    private function buildResultTrees($menuMatches, $dataMatches, $allMenus)
    {
        $treeMap = [];

//        echo "<pre>menuMatches=";print_r($menuMatches);
//        echo "<pre>dataMatches=";print_r($dataMatches);
//        echo "<pre>allMenus=";print_r($allMenus);
        // 处理匹配到的数据
        if (!empty($dataMatches)) {
            $dataTreeMap = [];
            foreach ($dataMatches as $data) {
                $parentCode = $this->getLastDataSCode($data);
                if (!isset($dataTreeMap[$parentCode])) {
                    $path = [];
                    $currentCode = $parentCode;

                    // 构建从根节点到数据父菜单的完整路径对象
                    while ($currentCode) {
                        if (isset($allMenus[$currentCode])) {
                            // 检查当前菜单是否在匹配列表中
                            $isMatched = false;
                            if (!empty($menuMatches)) {
                                foreach ($menuMatches as $menu) {
                                    if ($menu['scode'] === $currentCode) {
                                        $isMatched = true;
                                        break;
                                    }
                                }
                            }
                            array_unshift($path, [
                                'isParent' => '1',
                                'ID' => $allMenus[$currentCode]['id'],
                                'scode' => $currentCode,
                                'label' => html_entity_decode($allMenus[$currentCode]['sname']),
                                'sod' => $allMenus[$currentCode]['sod'],
                                'level' => $allMenus[$currentCode]['level'],
                                'matched' => $isMatched
                            ]);
                            $currentCode = $allMenus[$currentCode]['parent_scode'];
                        } else {
                            break;
                        }
                    }
                    $dataTreeMap[$parentCode] = [
                        'path' => $path,
                        'data' => []
                    ];
                }

                // 添加数据到对应父菜单的数据列表中
                $dataTreeMap[$parentCode]['data'][] = [
                    'isParent' => '0',
                    'ID' => $data['id'],
                    'label' => html_entity_decode($data['dtname']),
                    'sod' => $data['sod'],
                    'DataImage' => $data['DataImage'],
                    'unit' => $data['unit'],
                    'TargetFlagVar' => $data['TargetFlagVar'],
                    'subAtt1Db' => $data['subAtt1Db'],
                    'subAtt1Db_en' => $data['subAtt1Db_en'],
                    'subDatasTitle' => $data['subDatasTitle'],
                    'subDatasTitle_en' => $data['subDatasTitle_en'],
                    'dtymd' => $data['dtymd'],
                    'data_releasetime' => $data['data_releasetime'],
                    'pinzhong' => $data['pinzhong'],
                    'province' => $data['province'],
                    'city' => $data['city'],
                    'level' => $data['level'],
                    'parent_scode' => $data['parent_scode'],
                    'matched' => true
                ];
            }
            foreach ($dataTreeMap as $item) {
                $path = $item['path'];
                $path[] = $item['data'];
                $tree = $this->buildDataTreeFromPath($path);
                if ($tree) {
                    $rootCode = $path[0]['scode'];
                    if (!isset($treeMap[$rootCode])) {
                        $treeMap[$rootCode] = $tree;
                    } else {
                        // 递归合并树结构
                        $this->mergeTreeNodes($treeMap[$rootCode], $tree);
                    }
                }
            }
        }
//        echo "<pre>treeMap111=";print_r($treeMap);
        // 处理匹配到的菜单
        if (!empty($menuMatches)) {
            foreach ($menuMatches as $menu) {
                $path = [];
                $currentCode = $menu['scode'];

                // 构建从根节点到菜单的完整路径对象
                while ($currentCode) {
                    if (isset($allMenus[$currentCode])) {
                        $isMatched = $currentCode === $menu['scode'];
                        array_unshift($path, [
                            'isParent' => '1',
                            'ID' => $allMenus[$currentCode]['id'],
                            'scode' => $currentCode,
                            'label' => html_entity_decode($allMenus[$currentCode]['sname']),
                            'sod' => $allMenus[$currentCode]['sod'],
                            'level' => $allMenus[$currentCode]['level'],
                            'matched' => $isMatched
                        ]);
                        $currentCode = $allMenus[$currentCode]['parent_scode'];
                    } else {
                        break;
                    }
                }
                // 构建树形结构
//                echo "<pre>path=";print_r($path);
                $tree = $this->buildTreeFromPath($path);
//                echo "<pre>tree=";print_r($tree);
                if ($tree) {
                    // 找到匹配的菜单节点
                    $matchedNode = &$this->findNodeByCode($tree, $menu['scode']);
                    if ($matchedNode) {
                        // 获取匹配菜单下的子菜单和数据
                        $this->appendMenuChildren($matchedNode);
                    }
                    $rootCode = $path[0]['scode'];
                    if (!isset($treeMap[$rootCode])) {
                        $treeMap[$rootCode] = $tree;
                    } else {
                        // 递归合并树结构
                        $this->mergeTreeNodes($treeMap[$rootCode], $tree);
                    }
                }
            }
        }
//        echo "<pre>treeMap111=";print_r($treeMap);
        // 最后一次遍历所有节点，确保子菜单和数据都被正确添加
        foreach ($treeMap as &$root) {
            $this->ensureChildrenComplete($root);
        }

        return array_values($treeMap);
    }

    private function &findNodeByCode(&$node, $scode) {
        if ($node['scode'] === $scode) {
            return $node;
        }

        if (isset($node['children'])) {
            foreach ($node['children'] as &$child) {
                if (isset($child['isParent']) && $child['isParent'] === '1') {
                    $result = &$this->findNodeByCode($child, $scode);
                    if ($result !== null) {
                        return $result;
                    }
                }
            }
        }

        $null = null;
        return $null;
    }

    private function appendMenuChildren(&$menuNode) {
        if (!isset($menuNode['children'])) {
            $menuNode['children'] = [];
        }

        // 获取直接子菜单
        $childMenus = $this->getDirectChildMenusFromES($menuNode['scode'], $this->current_mc_type);

        // 如果有子菜单，递归处理子菜单
        if (!empty($childMenus)) {
            foreach ($childMenus as $childMenu) {
                $childNode = [
                    'isParent' => '1',
                    'ID' => $childMenu['id'],
                    'scode' => $childMenu['scode'],
                    'label' => html_entity_decode($childMenu['sname']),
                    'sod' => $childMenu['sod'],
                    'level' => $childMenu['level'],
                    'children' => []
                ];
                // 递归处理子菜单
                $this->appendMenuChildren($childNode);
                $menuNode['children'][] = $childNode;
            }
        }
        // 如果没有子菜单（叶子节点），才获取数据
        else {
            // 获取当前菜单下的数据
            $data = $this->getDataFromES($menuNode['scode'], $menuNode['level']);
            if (!empty($data)) {
                foreach ($data as $item) {
                    $menuNode['children'][] = [
                        'isParent' => '0',
                        'ID' => $item['id'],
                        'label' => html_entity_decode($item['dtname']),
                        'sod' => $item['sod'],
                        'DataImage' => $item['DataImage'],
                        'unit' => $item['unit'],
                        'TargetFlagVar' => $item['TargetFlagVar'],
                        'subAtt1Db' => $item['subAtt1Db'],
                        'subAtt1Db_en' => $item['subAtt1Db_en'],
                        'subDatasTitle' => $item['subDatasTitle'],
                        'subDatasTitle_en' => $item['subDatasTitle_en'],
                        'dtymd' => $item['dtymd'],
                        'data_releasetime' => $item['data_releasetime'],
                        'pinzhong' => $item['pinzhong'],
                        'province' => $item['province'],
                        'city' => $item['city']
                    ];
                }
            }
        }
    }

    private function ensureChildrenComplete(&$node) {
        if (!isset($node['children']) || !is_array($node['children'])) {
            return;
        }

        // 按照 sod 排序子节点
        usort($node['children'], function($a, $b) {
            return ($a['sod'] ?? 0) - ($b['sod'] ?? 0);
        });

        // 递归处理每个子节点
        foreach ($node['children'] as &$child) {
            if (isset($child['isParent']) && $child['isParent'] === '1') {
                $this->ensureChildrenComplete($child);
            }
        }
    }

    /**
     * 从ES获取直接子菜单
     */
    private function getDirectChildMenusFromES($parentCode, $mc_type = null)
    {
        $mustConditions = [
            [
                'term' => [
                    'parent_scode' => $parentCode
                ]
            ]
        ];

        // 添加mc_type过滤条件
        if ($mc_type !== null) {
            $mustConditions[] = [
                'term' => [
                    'mc_type' => $mc_type
                ]
            ];
        }

        $params = [
            'index' => 'code_class_index',
            'body' => [
                'query' => [
                    'bool' => [
                        'must' => $mustConditions
                    ]
                ],
                'size' => 1000,
                'sort' => [
                    'sod' => 'asc'
                ]
            ]
        ];

        $response = $this->es->search($params);
        $menus = [];

        if (isset($response['hits']['hits'])) {
            foreach ($response['hits']['hits'] as $hit) {
                $menus[] = $hit['_source'];
            }
        }
        return $menus;
    }

    /**
     * 从ES获取指定菜单下的数据
     */
    private function getDataFromES($menuCode, $level)
    {
        $scodeField = 'scode' . $level;
        $mustConditions = [
            ['term' => [$scodeField => $menuCode]]
        ];

        // 添加mc_type过滤条件
        if ($this->current_mc_type !== null) {
            $mustConditions[] = [
                'term' => [
                    'mc_type' => $this->current_mc_type
                ]
            ];
        }

        $params = [
            'index' => 'code_datatype_index',
            'body' => [
                'query' => [
                    'bool' => [
                        'must' => $mustConditions
                    ]
                ],
                'size' => 1000,
                'sort' => [
                    'sod' => 'asc'
                ]
            ]
        ];
        $response = $this->es->search($params);
        $data = [];

        if (isset($response['hits']['hits'])) {
            foreach ($response['hits']['hits'] as $hit) {
                $data[] = $hit['_source'];
            }
        }
        return $data;
    }

    private function collectScodes($scode, &$allScodes)
    {
        if (!empty($scode)) {
            foreach (explode('/', $scode) as $item) {
                $allScodes[] = $item;
            }
        }
    }

    private function collectDataScodes($scode, &$allScodes)
    {
        $allScodes[] = $scode;
    }

    private function batchGetMenus($scodes)
    {
        $queryConditions = [
            'terms' => [
                'scode' => array_values((array)$scodes)
            ]
        ];

        // 如果设置了mc_type，添加过滤条件
        if ($this->current_mc_type !== null) {
            $params = [
                'index' => 'code_class_index',
                'body' => [
                    'query' => [
                        'bool' => [
                            'must' => [
                                $queryConditions,
                                [
                                    'term' => [
                                        'mc_type' => $this->current_mc_type
                                    ]
                                ]
                            ]
                        ]
                    ],
                    'size' => 1000,
                    'sort' => [
                        'sod' => 'asc'
                    ]
                ]
            ];
        } else {
            $params = [
                'index' => 'code_class_index',
                'body' => [
                    'query' => $queryConditions,
                    'size' => 1000,
                    'sort' => [
                        'sod' => 'asc'
                    ]
                ]
            ];
        }

        $result = $this->es->search($params);

        // 转换为便于查找的格式
        $menuMap = [];
        foreach ($result['hits']['hits'] as $hit) {
            $menuMap[$hit['_source']['scode']] = $hit['_source'];
        }

        return $menuMap;
    }

    private function getLastDataSCode($data)
    {
        //$data 有5个字段，取scode1到scode5，从后往前queue，直到有值，返回scode
        for ($i = 5; $i >= 1; $i--) {
            if (!empty($data['scode' . $i])) {
                return $data['scode' . $i];
            }
        }
    }

    /**
     * 将路径数组转换为嵌套的树形结构
     */
    private function buildTreeFromPath($path)
    {
        if (empty($path)) {
            return null;
        }

        $root = $path[0];
        if (!isset($root['children'])) {
            $root['children'] = [];
        }

        $current = &$root;
        for ($i = 1; $i < count($path); $i++) {
            if (is_array($path[$i])) {
                // If it's a data array (contains multiple data nodes)
                if (isset($path[$i][0]) && is_array($path[$i][0])) {
                    foreach ($path[$i] as $dataNode) {
                        $current['children'][] = $dataNode;
                    }
                } else {
                    // 如果是个菜单节点
                    if (isset($path[$i]['isParent']) && $path[$i]['isParent'] === '1') {
                        $current['children'][] = $path[$i];
                        $current = &$current['children'][count($current['children']) - 1];
                        if (!isset($current['children'])) {
                            $current['children'] = [];
                        }
                    } else {
                        // Single data node
                        $current['children'][] = $path[$i];
                    }
                }
            }
        }

        return $root;
    }

    private function buildDataTreeFromPath($path)
    {
        if (empty($path)) {
            return null;
        }

        // 获取根节点
        $root = array_shift($path);
        if (!isset($root['children'])) {
            $root['children'] = [];
        }
        $current = &$root;

        // 处理路径中的每个节点
        while (!empty($path)) {
            $node = array_shift($path);

            // 如果是数据数组（最后一个元素）
            if (is_array($node) && isset($node[0])) {
                foreach ($node as $dataNode) {
                    $current['children'][] = $dataNode;
                }
                break;
            }

            // 如果是菜单节点
            if (isset($node['isParent']) && $node['isParent'] === '1') {
                if (!isset($current['children'])) {
                    $current['children'] = [];
                }
                $current['children'][] = $node;
                $current = &$current['children'][count($current['children']) - 1];
                if (!isset($current['children'])) {
                    $current['children'] = [];
                }
            } else {
                // 单个数据节点
                if (!isset($current['children'])) {
                    $current['children'] = [];
                }
                $current['children'][] = $node;
            }
        }

        return $root;
    }

    // 添加辅助方法来合并树节点
    private function mergeTreeNodes(&$target, $source)
    {
        if (!is_array($target) || !is_array($source) ||
            !isset($target['scode']) || !isset($source['scode']) ||
            $target['scode'] !== $source['scode']) {
            return;
        }

        if (!isset($target['children'])) {
            $target['children'] = [];
        }

        if (!empty($source['children'])) {
            foreach ($source['children'] as $sourceChild) {
                if (!is_array($sourceChild)) {
                    continue;
                }

                // 菜单节点
                if (isset($sourceChild['isParent']) && $sourceChild['isParent'] === '1') {
                    $found = false;
                    foreach ($target['children'] as &$targetChild) {
                        if (!is_array($targetChild)) {
                            continue;
                        }
                        if (isset($targetChild['isParent']) &&
                            isset($targetChild['scode']) &&
                            isset($sourceChild['scode']) &&
                            $targetChild['scode'] === $sourceChild['scode']) {
                            $this->mergeTreeNodes($targetChild, $sourceChild);
                            $found = true;
                            break;
                        }
                    }
                    if (!$found) {
                        $target['children'][] = $sourceChild;
                    }
                } else {
                    // 数据节点
                    $found = false;
                    foreach ($target['children'] as $targetChild) {
                        if (!is_array($targetChild)) {
                            continue;
                        }
                        if (isset($targetChild['ID']) &&
                            isset($sourceChild['ID']) &&
                            $targetChild['ID'] === $sourceChild['ID']) {
                            $found = true;
                            break;
                        }
                    }
                    if (!$found) {
                        $target['children'][] = $sourceChild;
                    }
                }
            }
        }

        if (!empty($target['children'])) {
            $target['children'] = array_values(array_filter($target['children'], 'is_array'));
        }
    }
}