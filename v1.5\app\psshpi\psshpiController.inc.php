<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );


class psshpiController extends AbstractController{
  
  public function __construct(){
    parent::__construct();
	
	$this->_action->setDao( new psshpiDao( 'DRCW',"DRC" ) );
	//$this->_action->ngdao = new psshpiDao('DRCW') ;
	$this->_action->t1dao = new psshpiDao('MAIN') ;
	$this->_action->maindao = new psshpiDao('91R');
	
	$this->_action->gcdao=new psshpiDao('GC');
  }
  
  public function _dopre(){
      //$this->_action->checkSession();
  }

  public function v_GetPsRealtimeNewsList() {
	$this->_action->GetPsRealtimeNewsList($this->_request);
  }
  /*GetPsRealtimeNewsDetail*/
  public function v_newsdetail() {
	$this->_action->newsdetail($this->_request);
  }
  public function v_GetPsRealtimeNumsList() {
	$this->_action->GetPsRealtimeNumsList($this->_request);
  }
  public function v_CanUsePs() {
	$this->_action->CanUsePs($this->_request);
  }
  public function v_GetPsNumType() {
	$this->_action->GetPsNumType($this->_request);
  }
  public function v_echarts() {
	$this->_action->echarts($this->_request);
  }
  
  
}