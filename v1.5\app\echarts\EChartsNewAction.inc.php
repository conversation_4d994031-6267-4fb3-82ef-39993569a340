<?php
define("MaxType",1);
class EChartsNewAction extends AbstractAction{

  public $oadao;

  public function __construct(){
    parent::__construct();    
  }

  private function getIP() {
    $ip=getenv('REMOTE_ADDR');
    $ip_ = getenv('HTTP_X_FORWARDED_FOR');
    if (($ip_ != "") && ($ip_ != "unknown")) {
		$ip=$ip_;
    }
	$ip=explode(",",$ip);
    return $ip[0];
  }
  
	public function getwei($str) //add by zhangcun 2017/7/19 for 获取小数有效位数
	{
		$wei=0;
		if($str-1.0>0) return $wei;
		for($i=0;$i<10;$i++){
			$str*=10.0;
			$wei++;
			if($str-1.0>0) return $wei;
		}
	}
    public function getdatebydate($date1, $date2, $type = 5) //5周6日
    {
        include_once(APP_DIR . "/dcsearch/SthDao.inc.php");
        $stedao = new SthDao('91R');
        $datenum = 0;
        $date1 = str_replace('/', '-', $date1);
        if (strtotime($date1) <= strtotime($date2)) {
            if ($type == 6) {
                $date1 = date("Y-m-d", strtotime($date1 . " +1 day"));
                $date2 = date("Y-m-d", strtotime($date2 . " -2 day"));
                $holiday = $stedao->query("select date,isholiday from holiday where date>='$date1' and date<='$date2'");
                $holidayarr = array();
                foreach ($holiday as $k => $v) {
                    $holidayarr[$v['date']] = $v['isholiday'];
                }
                while (strtotime($date1) < strtotime($date2)) {

                    $isholiday = 0;
                    if (isset($holidayarr[$date1])) {
                        $isholiday = $holidayarr[$date1];
                    } else {
                        if (date('D', strtotime($date1)) == "Sat" or date('D', strtotime($date1)) == 'Sun') {
                            $isholiday = 1;
                        }
                    }
                    if ($isholiday != 1) {
                        $datenum++;
                    }

                    $date1 = date("Y-m-d", strtotime($date1 . " +1 day"));
                }
            } else {

                //$date2=date("Y-m-d",strtotime($date2." -2 day"));
                $sweek = date("W", strtotime($date1));

                $eweek = date("W", strtotime($date2));

                if ($sweek <= $eweek) {

                    $datenum = $eweek - $sweek;
                } else {
                    $sweek1 = date("Y-12-31", strtotime($date1));

                    $sweekday = date("w", strtotime($sweek1));

                    $sweek2 = date("W", strtotime($sweek1));

                    $datenum = $sweek2 - $sweek + $eweek;
                    // if($sweekday!=0)
                    // {
                    // $datenum--;
                    // }

                }

            }


        }

        return $datenum;
    }


    public function getwei2($str) //add by zhangcun 2018/6/27 for 获取小数有效位数
    {
        $wei = explode(".", $str);
        return strlen($wei[1]);
    }

    public function filterlinebardata($params, $ftp)
    {
        //print_r($ftp);
        //print_r($params);
        foreach ($params as $k => $v) {
            if ($ftp[$k] == 1) {
                $data[0][] = $v;//柱状图
            } else {
                $data[1][] = $v;//折线图
            }
        }
        //$data[0]=array($params[0]);
        //$data[1]=array($params[1]);
        return $data;
    }

    public function filterlinebarlendtitle($params, $ftp)
    {
        foreach ($params as $k => $v) {
            if ($ftp[$k] == 1) {
                $data[0][] = $v;//柱状图
            } else {
                $data[1][] = $v;//折线图
            }
        }
        //$data[0]=array($params[0]);
        //$data[1]=array($params[1]);
        return $data;
    }

    public function compareAB($a, $b, $t) //判断时间a,b是否有一个是t的前一年、前一月或者前一天
    {
        //$a="16/01";
        //$b='16/02';
        //$t='16/03';

        $i = explode("/", $a);
        $j = explode("/", $b);
        $k = explode("-", $t);
        if ($i[0] < 100) $i[0] += 2000;
        if ($j[0] < 100) $j[0] += 2000;
        if ($k[0] < 100) $k[0] += 2000;
        $a = implode("-", $i);
        $b = implode("-", $j);
        //$t=implode("-",$k);

        //print"<pre>";print_r($a);echo "<br>";print_r($b);echo "<br>";print_r($t);echo "<br>";
        switch(count($i)){
         case "1":   
			       //$t=date("Y",strtotime($a)+3600*24*365);
		           //$b=date("Y",strtotime($b)+3600*24*365);
		           $t=date("Y",strtotime($k-1));
			       break;
		 case "2": 
			       //$a=date("Y-m",strtotime($a));
		           //$b=date("Y-m",strtotime($b));
				   if($k[1]!=1) {
					   $t=date("Y-m",strtotime($k[0]."-".($k[1]-1)));
				   }
				   else{
					   $t=date("Y-m",strtotime(($k[0]-1)."-12"));
				   }
				   break;
		 case "3":
			       //$a=date("Y-m-d",strtotime($a)+3600*24);
		           //$b=date("Y-m-d",strtotime($b)+3600*24);
		           $t=date("Y-m-d",strtotime($t)-3600*24);
				   break;
		}
        
	    //print"<pre>";print_r($a);echo "<br>";print_r($b);echo "<br>";print_r($t);echo "<br>";
		if($a==$t||$b==$t) return true;
        //else echo "Fail!";
		return false;
	}
    public function finddate($array, $date)
    {

        //$count = 0;
        foreach ($array as $day) {
            //echo $day."<br/>";
            //$interval[$count] = abs(strtotime($date) - strtotime($day));
            $interval[] = abs(strtotime($date) - strtotime($day));
            //$count++;
        }

        asort($interval);
        $closest = key($interval);

        //echo $array[$closest];

        $data['date'] = $array[$closest];
        $data['key'] = $closest;
        return $data;
    }
	private function getmin($arr){
		if (is_array($arr[0])) return "";
        $min = "aaa";
        foreach ($arr as $id => $val) {
            $val = $val=="'-'" ?0:$val;
            if ($min == "aaa") $min = $val;
            elseif (floatval( $min ) - $val > 0) $min = $val;
        }
        if ($min == "aaa") {
            return "";
        } else {
            return $min;
        }
	}
	private function getmax($arr){
		if(is_array($arr[0])) return "";
		$max="bbb";
		foreach($arr as $id=>$val){
            $val = $val=="'-'" ?0:$val;
			if($max=="bbb") $max=$val;
			 elseif (floatval( $max ) - $val < 0) $max = $val;
		}
		if($max=="bbb"){
			return "";
		}else{
			return $max;
		}
	}

		
	private function getmytitle($str,$ii,$len=70){
		$tmpstr = "";
		$mark=0;
		for($i = 0; $i < strlen($str); $i++) {
			if(ord(substr($str, $i, 1)) > 0xa0) {
				if($ii==0&&$i>$len&&$mark==0){
					$tmpstr .="\n";
					$mark=1;
				}
				$tmpstr .= substr($str, $i, 2);
				$i++;
			} else
				$tmpstr .= substr($str, $i, 1);
		}
		return $tmpstr;
	}
	private function isrn($arr,$isjia1=0)  //是否存在瑞年
	{
       $cunzai=2;
		foreach($arr as $year)
		{
			if($isjia1==1)
			{
				$year++;
			}
			if(($year%4==0&&$year%100!=0)||($year%400==0))
			{
				$cunzai=1;
			   break;
			}
		}
       return  $cunzai;
	}
	public function dataview($params,$dao){
		
		//echo '<pre>';	print_R($this->convert_to_utf8($params['lendtitle']));exit;
		//print_r($params['xlabel']);
		//echo $params['zhou1']." ".$params['zhou2'];exit;
		//echo microtime()."<br>";
		//$array=array('xlabel','lendtitle','charttitle','dw','index');
		/* echo '<pre>';
		print_r($params); */
		//echo '<pre>';print_R($params);exit;
        $width=$params["width"]?$params['width']:600;
        $height=$params["height"]?$params['height']:336;
		$mc_type=$params["mc_type"];
		$istry=$params["istry"];
		$theme=$params['theme']?$params['theme']:0;
		$isEnglish=$_REQUEST["isEnglish"];
		$datasourcetype=$_REQUEST["datasourcetype"];
		$align=$params['align']?$params['align']:'center';
		$isstaticimg=$params['isstaticimg']?$params['isstaticimg']:'0';
        $smalltitle=$params['smalltitle']?$params['smalltitle']:'0';
		$url=$params['url'];
		$mode=$_GET['mode']?$_GET['mode']:1;
        $params['lendtitle'] = $params['lendtitle']==NULL?[]:$params['lendtitle'];
		 // 获取去掉重复数据的数组   
       $unique_arr = array_unique ( $params['lendtitle'] );   
       // 获取重复数据的数组   
       $repeat_arr = array_diff_assoc ( $params['lendtitle'], $unique_arr );
       
		//foreach($params['lendtitle'] as $k=>$title){
	  
       foreach($repeat_arr as $k=>$title){	
			$params['lendtitle'][$k]=trim($params['lendtitle'][$k]).str_repeat("#",$k);
			//for($i=$k+1;$i<4;$i++){
			//	if($title==$params['lendtitle'][$i])$params['lendtitle'][$k]=" ".$params['lendtitle'][$k];
			//}
			
		}
        $params['datainit']=$params['data'];

		if($mode==3||$mode==5){
			$pic_hz="_small";
		}else{
			$pic_hz="";
		}
		
		if($mc_type==2){
			define("BG_IMG","200".$pic_hz.".gif");
			define("RES_NAME","陕钢集团云数据经营评价系统");
		 }else if($mc_type==1){
		   define("BG_IMG","150".$pic_hz.".jpg");
		   define("RES_NAME","南钢产销大数据平台");
	    }else if($mc_type==3){
			define("BG_IMG","300".$pic_hz.".gif");
			define("RES_NAME","新钢产线效益预测系统");
		 }else if($mc_type==4){
			define("BG_IMG","300".$pic_hz.".gif");
			define("RES_NAME","新钢价格预测系统");
		 }else{
		   define("BG_IMG","000".$pic_hz.".gif");
		   define("RES_NAME","钢之家数据中心 www.steelhome.com/data");
	    }

        if ($isEnglish) {
            $datasource = "Source: SteelHome Database ";
            $junzhi = "AVG";
        } elseif ($datasourcetype == "ahsteel") {
            $datasource = " ";
            $junzhi = "均值";
        } else {
            $datasource = "数据来源：" . RES_NAME;
            $junzhi = "均值";
        }
        // $params = $this->convert_to_utf8($params);
        
        if ($params['color'] == "0") {//彩色

            //$linecolor=array('#4F81BD','#C0504D','#9BBB59','#8166A2');
            //$linecolor=array('#416FA6','#A8423F','#3D96AE','#DA8137');//hide by zhangcun 2018/8/20
            $linecolor = array('#3C6494', '#A8423F', '#228B22', '#FFCC00', '#D87093', '#778899', '#00FFFF', '#FF4500', '#C0504D', '#9BBB59', '#8166A2');
        } elseif ($params['color'] == "1")//蓝色
        {
            $linecolor = array('#096594', '#3C6494', '#4978B1', '#708090', '#7E9BC8', '#9AC0CD', '#B6C3DC', '#F0FFFF', '#C0504D', '#9BBB59', '#8166A2');
        } 
        elseif ($params['color'] == "2")//生成修改
        {
            $linecolor = array('#4F81BD', '#C0504D', '#9BBB59', '#8064A2', '#4BACC6', '#F79646', '#B6C3DC', '#F0FFFF', '#C0504D', '#9BBB59', '#8166A2');
        }
        elseif ($params['color'] == "3")//生成修改
        {
            $linecolor = array('#ff0000', '#0000ff', '#00ff00', '#708090', '#7E9BC8', '#9AC0CD', '#B6C3DC', '#F0FFFF', '#C0504D', '#9BBB59', '#8166A2');
        } 
        else {

            //$linecolor=array('#4F81BD','#C0504D','#9BBB59','#8166A2');
            //$linecolor=array('#416FA6','#A8423F','#3D96AE','#DA8137');//hide by zhangcun 2018/8/20
            $linecolor = array('#3C6494', '#A8423F', '#228B22', '#FFCC00', '#D87093', '#778899', '#00FFFF', '#FF4500', '#C0504D', '#9BBB59', '#8166A2');
        }
        $ip = $this->getIP();
        if (array_key_exists($_REQUEST['GUID'], $GLOBALS["testaccountarr"]) && $_REQUEST['SignCS'] != '' && (in_array($ip, $GLOBALS["testip"]) || $GLOBALS["nowtest"] == 1)) {
            $user = $GLOBALS["testaccountuser"];
        } else {
            $user = $dao->getRow("SELECT * FROM app_session_temp WHERE GUID = '" . $_REQUEST['GUID'] . "' AND mc_type='$mc_type'");
        }
        $zdycolors = $dao->getRow("select CustomColor1,CustomColor2,CustomColor3,CustomColor4,CustomColor from dc_custom_color where mid='$user[Mid]' and uid='$user[Uid]' and mc_type='$mc_type' limit 1");
        if (empty($zdycolors["CustomColor"])) {
            $zdycolor = array($zdycolors["CustomColor1"], $zdycolors["CustomColor2"], $zdycolors["CustomColor3"], $zdycolors["CustomColor4"]);
        } else {
            $zdycolor = explode(",", $zdycolors["CustomColor"]);

        }


        $data = array();
        $nzd = 0;
        $nzdtype = false;
        $nzdtypenew = false;
        $yearorminthdata = false;
		if($_GET['ImageType']=="8"){
			//echo "5555";
			$ChartExtdecode=$params['ChartExt']?$params['ChartExt']:array();
            $DateArr_ImageType8=$ChartExtdecode['DateStr'];
			$date_ImageType8=explode(',',$DateArr_ImageType8);
			$date_ImageType8=array_unique($date_ImageType8);//去除重复年
            $setdatanum = explode(',', $ChartExtdecode['setdatanum']);
            if (empty($ChartExtdecode['setdatanum'])) {
                $setdatanum = array(6, 6);
            }
            $setdatanum[0] = $setdatanum[0] > 50 ? 50 : $setdatanum[0];
            $setdatanum[1] = $setdatanum[1] > 50 ? 50 : $setdatanum[1];

            $SetDatearr = explode(',', $ChartExtdecode['SetDate']);
            if (empty($ChartExtdecode['SetDate'])) {
                $SetDatearr = array('01-01', '06-30');
                //$SetDatearr=array('10-01','01-30');
            }
            if (strlen($SetDatearr[0]) < 3) {
                // $ytday=array('1'=>'31','2'=>'29','3'=>'31','4'=>'30','5'=>'31','6'=>'30','7'=>'31','8'=>'31','9'=>'30','10'=>'31','11'=>'30','12'=>'31');
                // $num1 = (int) $SetDatearr[1];
                // $SetDatearr=array($SetDatearr[0].'-01',$SetDatearr[1].'-'.$ytday[$num1]);
                $SetDatearr[0] = $SetDatearr[0] . '-01';
                //print_r($SetDatearr);
            }
            if (strlen($SetDatearr[1]) < 3) {
                $ytday = array('1' => '31', '2' => '29', '3' => '31', '4' => '30', '5' => '31', '6' => '30', '7' => '31', '8' => '31', '9' => '30', '10' => '31', '11' => '30', '12' => '31');
                $num1 = (int)$SetDatearr[1];
                $SetDatearr[1] = $SetDatearr[1] . '-' . $ytday[$num1];
                //print_r($SetDatearr);
            }


            $SetDatearr[0] = date("m-d", strtotime('2020-' . $SetDatearr[0]));
            $SetDatearr[1] = date("m-d", strtotime('2020-' . $SetDatearr[1]));

            if ($ChartExtdecode['Type'] == 3 && $ChartExtdecode['isfestival'] != 3) {
				$nzdtype=true;

			   $leftdate=$this->isrn($date_ImageType8);
			   $rightdate=$this->isrn($date_ImageType8,1);
			   sort($date_ImageType8);
				// print_r($date_ImageType8);
				//$rndatearr=array("01-01","01-02","01-03","01-04","01-05","01-06","01-07","01-08","01-09","01-10","01-11","01-12","01-13","01-14","01-15","01-16","01-17","01-18","01-19","01-20","01-21","01-22","01-23","01-24","01-25","01-26","01-27","01-28","01-29","01-30","01-31","02-01","02-02","02-03","02-04","02-05","02-06","02-07","02-08","02-09","02-10","02-11","02-12","02-13","02-14","02-15","02-16","02-17","02-18","02-19","02-20","02-21","02-22","02-23","02-24","02-25","02-26","02-27","02-28","02-29","03-01","03-02","03-03","03-04","03-05","03-06","03-07","03-08","03-09","03-10","03-11","03-12","03-13","03-14","03-15","03-16","03-17","03-18","03-19","03-20","03-21","03-22","03-23","03-24","03-25","03-26","03-27","03-28","03-29","03-30","03-31","04-01","04-02","04-03","04-04","04-05","04-06","04-07","04-08","04-09","04-10","04-11","04-12","04-13","04-14","04-15","04-16","04-17","04-18","04-19","04-20","04-21","04-22","04-23","04-24","04-25","04-26","04-27","04-28","04-29","04-30","05-01","05-02","05-03","05-04","05-05","05-06","05-07","05-08","05-09","05-10","05-11","05-12","05-13","05-14","05-15","05-16","05-17","05-18","05-19","05-20","05-21","05-22","05-23","05-24","05-25","05-26","05-27","05-28","05-29","05-30","05-31","06-01","06-02","06-03","06-04","06-05","06-06","06-07","06-08","06-09","06-10","06-11","06-12","06-13","06-14","06-15","06-16","06-17","06-18","06-19","06-20","06-21","06-22","06-23","06-24","06-25","06-26","06-27","06-28","06-29","06-30","07-01","07-02","07-03","07-04","07-05","07-06","07-07","07-08","07-09","07-10","07-11","07-12","07-13","07-14","07-15","07-16","07-17","07-18","07-19","07-20","07-21","07-22","07-23","07-24","07-25","07-26","07-27","07-28","07-29","07-30","07-31","08-01","08-02","08-03","08-04","08-05","08-06","08-07","08-08","08-09","08-10","08-11","08-12","08-13","08-14","08-15","08-16","08-17","08-18","08-19","08-20","08-21","08-22","08-23","08-24","08-25","08-26","08-27","08-28","08-29","08-30","08-31","09-01","09-02","09-03","09-04","09-05","09-06","09-07","09-08","09-09","09-10","09-11","09-12","09-13","09-14","09-15","09-16","09-17","09-18","09-19","09-20","09-21","09-22","09-23","09-24","09-25","09-26","09-27","09-28","09-29","09-30","10-01","10-02","10-03","10-04","10-05","10-06","10-07","10-08","10-09","10-10","10-11","10-12","10-13","10-14","10-15","10-16","10-17","10-18","10-19","10-20","10-21","10-22","10-23","10-24","10-25","10-26","10-27","10-28","10-29","10-30","10-31","11-01","11-02","11-03","11-04","11-05","11-06","11-07","11-08","11-09","11-10","11-11","11-12","11-13","11-14","11-15","11-16","11-17","11-18","11-19","11-20","11-21","11-22","11-23","11-24","11-25","11-26","11-27","11-28","11-29","11-30","12-01","12-02","12-03","12-04","12-05","12-06","12-07","12-08","12-09","12-10","12-11","12-12","12-13","12-14","12-15","12-16","12-17","12-18","12-19","12-20","12-21","12-22","12-23","12-24","12-25","12-26","12-27","12-28","12-29","12-30","12-31");
				//$pndatearr=array("01-01","01-02","01-03","01-04","01-05","01-06","01-07","01-08","01-09","01-10","01-11","01-12","01-13","01-14","01-15","01-16","01-17","01-18","01-19","01-20","01-21","01-22","01-23","01-24","01-25","01-26","01-27","01-28","01-29","01-30","01-31","02-01","02-02","02-03","02-04","02-05","02-06","02-07","02-08","02-09","02-10","02-11","02-12","02-13","02-14","02-15","02-16","02-17","02-18","02-19","02-20","02-21","02-22","02-23","02-24","02-25","02-26","02-27","02-28","03-01","03-02","03-03","03-04","03-05","03-06","03-07","03-08","03-09","03-10","03-11","03-12","03-13","03-14","03-15","03-16","03-17","03-18","03-19","03-20","03-21","03-22","03-23","03-24","03-25","03-26","03-27","03-28","03-29","03-30","03-31","04-01","04-02","04-03","04-04","04-05","04-06","04-07","04-08","04-09","04-10","04-11","04-12","04-13","04-14","04-15","04-16","04-17","04-18","04-19","04-20","04-21","04-22","04-23","04-24","04-25","04-26","04-27","04-28","04-29","04-30","05-01","05-02","05-03","05-04","05-05","05-06","05-07","05-08","05-09","05-10","05-11","05-12","05-13","05-14","05-15","05-16","05-17","05-18","05-19","05-20","05-21","05-22","05-23","05-24","05-25","05-26","05-27","05-28","05-29","05-30","05-31","06-01","06-02","06-03","06-04","06-05","06-06","06-07","06-08","06-09","06-10","06-11","06-12","06-13","06-14","06-15","06-16","06-17","06-18","06-19","06-20","06-21","06-22","06-23","06-24","06-25","06-26","06-27","06-28","06-29","06-30","07-01","07-02","07-03","07-04","07-05","07-06","07-07","07-08","07-09","07-10","07-11","07-12","07-13","07-14","07-15","07-16","07-17","07-18","07-19","07-20","07-21","07-22","07-23","07-24","07-25","07-26","07-27","07-28","07-29","07-30","07-31","08-01","08-02","08-03","08-04","08-05","08-06","08-07","08-08","08-09","08-10","08-11","08-12","08-13","08-14","08-15","08-16","08-17","08-18","08-19","08-20","08-21","08-22","08-23","08-24","08-25","08-26","08-27","08-28","08-29","08-30","08-31","09-01","09-02","09-03","09-04","09-05","09-06","09-07","09-08","09-09","09-10","09-11","09-12","09-13","09-14","09-15","09-16","09-17","09-18","09-19","09-20","09-21","09-22","09-23","09-24","09-25","09-26","09-27","09-28","09-29","09-30","10-01","10-02","10-03","10-04","10-05","10-06","10-07","10-08","10-09","10-10","10-11","10-12","10-13","10-14","10-15","10-16","10-17","10-18","10-19","10-20","10-21","10-22","10-23","10-24","10-25","10-26","10-27","10-28","10-29","10-30","10-31","11-01","11-02","11-03","11-04","11-05","11-06","11-07","11-08","11-09","11-10","11-11","11-12","11-13","11-14","11-15","11-16","11-17","11-18","11-19","11-20","11-21","11-22","11-23","11-24","11-25","11-26","11-27","11-28","11-29","11-30","12-01","12-02","12-03","12-04","12-05","12-06","12-07","12-08","12-09","12-10","12-11","12-12","12-13","12-14","12-15","12-16","12-17","12-18","12-19","12-20","12-21","12-22","12-23","12-24","12-25","12-26","12-27","12-28","12-29","12-30","12-31");
				
				$rndatearr=array("01/01","01/02","01/03","01/04","01/05","01/06","01/07","01/08","01/09","01/10","01/11","01/12","01/13","01/14","01/15","01/16","01/17","01/18","01/19","01/20","01/21","01/22","01/23","01/24","01/25","01/26","01/27","01/28","01/29","01/30","01/31","02/01","02/02","02/03","02/04","02/05","02/06","02/07","02/08","02/09","02/10","02/11","02/12","02/13","02/14","02/15","02/16","02/17","02/18","02/19","02/20","02/21","02/22","02/23","02/24","02/25","02/26","02/27","02/28","02/29","03/01","03/02","03/03","03/04","03/05","03/06","03/07","03/08","03/09","03/10","03/11","03/12","03/13","03/14","03/15","03/16","03/17","03/18","03/19","03/20","03/21","03/22","03/23","03/24","03/25","03/26","03/27","03/28","03/29","03/30","03/31","04/01","04/02","04/03","04/04","04/05","04/06","04/07","04/08","04/09","04/10","04/11","04/12","04/13","04/14","04/15","04/16","04/17","04/18","04/19","04/20","04/21","04/22","04/23","04/24","04/25","04/26","04/27","04/28","04/29","04/30","05/01","05/02","05/03","05/04","05/05","05/06","05/07","05/08","05/09","05/10","05/11","05/12","05/13","05/14","05/15","05/16","05/17","05/18","05/19","05/20","05/21","05/22","05/23","05/24","05/25","05/26","05/27","05/28","05/29","05/30","05/31","06/01","06/02","06/03","06/04","06/05","06/06","06/07","06/08","06/09","06/10","06/11","06/12","06/13","06/14","06/15","06/16","06/17","06/18","06/19","06/20","06/21","06/22","06/23","06/24","06/25","06/26","06/27","06/28","06/29","06/30","07/01","07/02","07/03","07/04","07/05","07/06","07/07","07/08","07/09","07/10","07/11","07/12","07/13","07/14","07/15","07/16","07/17","07/18","07/19","07/20","07/21","07/22","07/23","07/24","07/25","07/26","07/27","07/28","07/29","07/30","07/31","08/01","08/02","08/03","08/04","08/05","08/06","08/07","08/08","08/09","08/10","08/11","08/12","08/13","08/14","08/15","08/16","08/17","08/18","08/19","08/20","08/21","08/22","08/23","08/24","08/25","08/26","08/27","08/28","08/29","08/30","08/31","09/01","09/02","09/03","09/04","09/05","09/06","09/07","09/08","09/09","09/10","09/11","09/12","09/13","09/14","09/15","09/16","09/17","09/18","09/19","09/20","09/21","09/22","09/23","09/24","09/25","09/26","09/27","09/28","09/29","09/30","10/01","10/02","10/03","10/04","10/05","10/06","10/07","10/08","10/09","10/10","10/11","10/12","10/13","10/14","10/15","10/16","10/17","10/18","10/19","10/20","10/21","10/22","10/23","10/24","10/25","10/26","10/27","10/28","10/29","10/30","10/31","11/01","11/02","11/03","11/04","11/05","11/06","11/07","11/08","11/09","11/10","11/11","11/12","11/13","11/14","11/15","11/16","11/17","11/18","11/19","11/20","11/21","11/22","11/23","11/24","11/25","11/26","11/27","11/28","11/29","11/30","12/01","12/02","12/03","12/04","12/05","12/06","12/07","12/08","12/09","12/10","12/11","12/12","12/13","12/14","12/15","12/16","12/17","12/18","12/19","12/20","12/21","12/22","12/23","12/24","12/25","12/26","12/27","12/28","12/29","12/30","12/31"); 
				$pndatearr=array("01/01","01/02","01/03","01/04","01/05","01/06","01/07","01/08","01/09","01/10","01/11","01/12","01/13","01/14","01/15","01/16","01/17","01/18","01/19","01/20","01/21","01/22","01/23","01/24","01/25","01/26","01/27","01/28","01/29","01/30","01/31","02/01","02/02","02/03","02/04","02/05","02/06","02/07","02/08","02/09","02/10","02/11","02/12","02/13","02/14","02/15","02/16","02/17","02/18","02/19","02/20","02/21","02/22","02/23","02/24","02/25","02/26","02/27","02/28","03/01","03/02","03/03","03/04","03/05","03/06","03/07","03/08","03/09","03/10","03/11","03/12","03/13","03/14","03/15","03/16","03/17","03/18","03/19","03/20","03/21","03/22","03/23","03/24","03/25","03/26","03/27","03/28","03/29","03/30","03/31","04/01","04/02","04/03","04/04","04/05","04/06","04/07","04/08","04/09","04/10","04/11","04/12","04/13","04/14","04/15","04/16","04/17","04/18","04/19","04/20","04/21","04/22","04/23","04/24","04/25","04/26","04/27","04/28","04/29","04/30","05/01","05/02","05/03","05/04","05/05","05/06","05/07","05/08","05/09","05/10","05/11","05/12","05/13","05/14","05/15","05/16","05/17","05/18","05/19","05/20","05/21","05/22","05/23","05/24","05/25","05/26","05/27","05/28","05/29","05/30","05/31","06/01","06/02","06/03","06/04","06/05","06/06","06/07","06/08","06/09","06/10","06/11","06/12","06/13","06/14","06/15","06/16","06/17","06/18","06/19","06/20","06/21","06/22","06/23","06/24","06/25","06/26","06/27","06/28","06/29","06/30","07/01","07/02","07/03","07/04","07/05","07/06","07/07","07/08","07/09","07/10","07/11","07/12","07/13","07/14","07/15","07/16","07/17","07/18","07/19","07/20","07/21","07/22","07/23","07/24","07/25","07/26","07/27","07/28","07/29","07/30","07/31","08/01","08/02","08/03","08/04","08/05","08/06","08/07","08/08","08/09","08/10","08/11","08/12","08/13","08/14","08/15","08/16","08/17","08/18","08/19","08/20","08/21","08/22","08/23","08/24","08/25","08/26","08/27","08/28","08/29","08/30","08/31","09/01","09/02","09/03","09/04","09/05","09/06","09/07","09/08","09/09","09/10","09/11","09/12","09/13","09/14","09/15","09/16","09/17","09/18","09/19","09/20","09/21","09/22","09/23","09/24","09/25","09/26","09/27","09/28","09/29","09/30","10/01","10/02","10/03","10/04","10/05","10/06","10/07","10/08","10/09","10/10","10/11","10/12","10/13","10/14","10/15","10/16","10/17","10/18","10/19","10/20","10/21","10/22","10/23","10/24","10/25","10/26","10/27","10/28","10/29","10/30","10/31","11/01","11/02","11/03","11/04","11/05","11/06","11/07","11/08","11/09","11/10","11/11","11/12","11/13","11/14","11/15","11/16","11/17","11/18","11/19","11/20","11/21","11/22","11/23","11/24","11/25","11/26","11/27","11/28","11/29","11/30","12/01","12/02","12/03","12/04","12/05","12/06","12/07","12/08","12/09","12/10","12/11","12/12","12/13","12/14","12/15","12/16","12/17","12/18","12/19","12/20","12/21","12/22","12/23","12/24","12/25","12/26","12/27","12/28","12/29","12/30","12/31");
				
                $leftdate=$leftdate==1?$rndatearr:$pndatearr;
                $rightdate=$rightdate==1?$rndatearr:$pndatearr;

				foreach($params['xlabel'] as $i=>$v){
					$v=str_replace('-','/',$v);
					//echo $v."<br>";
					$year=explode('/',$v);
				//	if(count($year)==1) {$nzd=1;break;}
					$date=substr($v,strlen($year[0])+1);
					if(!strstr($date,'/'))//为的是数据存为年月格式
					{
						$date=$date."/01";
						$yearorminthdata=true;
					}
					$datearr[$date]=1;
					$data[$year[0]][$date]=$params['data'][0][$i];
				}
				if($yearorminthdata)
				{
					$leftdate=array('01/01','02/01','03/01','04/01','05/01','06/01','07/01','08/01','09/01','10/01','11/01','12/01');
					$rightdate=$leftdate;
				}
				//$date=array_keys($datearr);
				//sort($date);
				$params['xlabel']=array_merge($leftdate,$rightdate);
				$params['data']=array();
                                $params['datainit'] = array();
				$params['lendtitle']=array();
				$i=0;
				foreach($date_ImageType8 as $i=>$v)
				{
					//$datanew=$data[$v]?$data[$v]:$data[($v-2000)];
					$datanew=$data[$v]?$data[$v]:$data[(($v-2000)>=10?($v-2000):"0".($v-2000))];


                    if ( false) {
                        // print_r($params['datanew1'][$datanew]);
                        // echo $datanew;
                        // $params['data'][$i]=$params['datanew1'][$datanew];
                    } else {
                        foreach ($leftdate as $o => $d)//年折叠图左侧日期
                        {
                            if ($datanew[$d] == "") {
                                //$year = $v < 2000 ? $v + 2000 : $v;
                                $year =$v;
                                if (strstr($leftdate[$o], '/')) {
                                    $mdate = $year . "/" . $leftdate[$o];
                                } else {
                                    $mdate = $year . "/" . $leftdate[$o] . "/01";
                                }
                                if (strtotime($mdate) > time()) {
                                    $params['data'][$i][$d] = "'-'";
                                    $params['datainit'][$i][$d] = "'-'";
                                    continue;
                                }
				// $params['data'][$i][$d]=$this->getbu($datanew,$leftdate,$o-1,$mc_type);
				// if($params['data'][$i][$d]== "") {
				// 	$params['data'][$i][$d]=$this->getbu2($datanew,$leftdate,$o+1,$mc_type);
				// }
                                if($params['dtymd']==5)//周数据补
                                    {
                                        $params['data'][$i][$d]=$this->getbu2($datanew,$leftdate,$o+1,$mc_type);
                                    }
                                    else
                                    {
                                        $params['data'][$i][$d]="'-'";
                                    }
                                    $params['datainit'][$i][$d] = "'-'";
                                        if (empty($params['data'][$i][$d])) {
                                            $params['data'][$i][$d] = "'-'";
                                        }

                            } else {

                                $params['data'][$i][$d] = $datanew[$d];
                                $params['datainit'][$i][$d] = $datanew[$d];

                            }

                        }
                    }

                    //$datanew1=$data[($v+1)]?$data[($v+1)]:$data[(($v+1)-2000)];
                    $datanew1 = $data[($v + 1)] ? $data[($v + 1)] : $data[(($v + 1 - 2000) >= 10 ? ($v + 1 - 2000) : "0" . ($v + 1 - 2000))];
                    foreach ($leftdate as $o => $d)//年折叠图右侧日期
                    {
                        if ($datanew1[$d] == "") {
                            //$year = ($v + 1) < 2000 ? ($v + 1) + 2000 : ($v + 1);
                            $year = ($v + 1);
                            if (strstr($leftdate[$o], '/')) {
                                $mdate = $year . "/" . $leftdate[$o];
                            } else {
                                $mdate = $year . "/" . $leftdate[$o] . "/01";
                            }
                            //echo $mdate."<br/>";
                            if (strtotime($mdate) > time()) {
                                $params['datanew'][$i][$d] = "'-'";
                                $params['datainitnew'][$i][$d] = "'-'";
                                continue;
                            }
				// $params['datanew'][$i][$d]=$this->getbu($datanew1,$leftdate,$o-1,$mc_type);
				// if($params['datanew'][$i][$d]== "") {
				// 	$params['datanew'][$i][$d]=$this->getbu2($datanew1,$leftdate,$o+1,$mc_type);
				// } 
				        //    $params['datanew'][$i][$d]="'-'";
                        //     if (empty($params['datanew'][$i][$d])) {
                        //         $params['datanew'][$i][$d] = "'-'";
                        //     }

                            if($params['dtymd']==5)//周数据补
                            {
                                $params['datanew'][$i][$d]=$this->getbu2($datanew1,$leftdate,$o+1,$mc_type);
                            }
                            else
                            {
                                $params['datanew'][$i][$d]="'-'";
                            }
                            //$params['datainit'][$i][$d] = "'-'";
                            $params['datainitnew'][$i][$d] = "'-'";
                                if (empty($params['datanew'][$i][$d])) {
                                    $params['datanew'][$i][$d]="'-'";
                                }

                        } else {

                            $params['datanew'][$i][$d] = $datanew1[$d];
                            $params['datainitnew'][$i][$d] = $datanew1[$d];

                        }
                        $params['data'][$i][] = $params['datanew'][$i][$d];
                        $params['datainit'][$i][] = $params['datainitnew'][$i][$d];
                    }
                    // print_r($params['data'][$i]);
                    // print_r($params['datanew'][$i]);


                    // $params['data'][$i]=$params['data'][$i]+$params['datanew'][$i];

                    // print_r($params['data'][$i]);


                    $params['datanew1'][($v + 1)] = $params['datanew'][$i];
                    //print_r($params['datanew1']);

                    //$params['lendtitle'][$i] = $v < 2000 ? ($v + 2000) . "-" . ($v + 1 + 2000) : $v . "-" . ($v + 1);
                    $params['lendtitle'][$i] = $v . "-" . ($v + 1);
                    $i++;
                }
                //print_r($params['data']);
                //exit;

            } else if ($ChartExtdecode['isfestival']) {

                $nzdtypenew = true;
                sort($date_ImageType8);
                if ($ChartExtdecode['Type'] == 1) {
                    $date_ImageType8 = array_unique($date_ImageType8);//去除重复年

                    if (count($date_ImageType8) != 1) {
                        $date_ImageType8_new = array();
                        for ($i1 = $date_ImageType8[0]; $i1 <= $date_ImageType8[1]; $i1++) {
                            $date_ImageType8_new[] = $i1;
                        }

                        $date_ImageType8 = $date_ImageType8_new;

                    }

                }
                //print_r($date_ImageType8);
                //print_r($params['xlabel']);
                $datearrlist = array();
                $data = array();
                if ($ChartExtdecode['isfestival'] != 3) {
                    foreach ($params['xlabel'] as $i => $v) {
                        $v = str_replace('-', '/', $v);
                        //echo $v."<br>";
                        $year = explode('/', $v);
                        //	if(count($year)==1) {$nzd=1;break;}
                        $date = substr($v, strlen($year[0]) + 1);
                        if (!strstr($date, '/'))//为的是数据存为年月格式
                        {
                            $date = $date . "/01";
                            $yearorminthdata = true;
                        }
                        $datearr[$date] = 1;
                        //$year = $year[0] < 2000 ? $year[0] + 2000 : $year[0];
                        $year = $year[0] ;
                        $data[$i] = $params['data'][0][$i];
                        $datearrlist[] = $year . "/" . $date;
                    }

                    $startleft = ($setdatanum[0] * -1);
                    $startright = $setdatanum[1];
                    //echo  $startleft."|||".$startright;
                    for ($i = $startleft; $i <= $startright; $i++) {

                        $num[] = $i;
                    }
                    //print_r($num);
                    $params['xlabel'] = $num;
                    $params['data'] = array();
                    $params['datainit'] = array();
                    $params['lendtitle'] = array();
                    $i1 = 0;
                    $lnchunkjiedatelist = array(
                        '2001' => '2001-01-24',
                        '2002' => '2002-02-12',
                        '2003' => '2003-02-01',
                        '2004' => '2004-01-22',
                        '2005' => '2005-02-09',
                        '2006' => '2006-01-29',
                        '2007' => '2007-02-18',
                        '2008' => '2008-02-07',
                        '2009' => '2009-01-26',
                        '2010' => '2010-02-14',
                        '2011' => '2011-02-03',
                        '2012' => '2012-01-23',
                        '2013' => '2013-02-01',
                        '2014' => '2014-01-31',
                        '2015' => '2015-02-18',
                        '2016' => '2016-02-08',
                        '2017' => '2017-01-28',
                        '2018' => '2018-02-16',
                        '2019' => '2019-02-05',
                        '2020' => '2020-01-25',
                        '2021' => '2021-02-12',
                        '2022' => '2022-02-01',
                        '2023' => '2023-01-22',
                        '2024' => '2024-02-10',
                        '2025' => '2025-01-29',
                        '2026' => '2026-02-17',
                        '2027' => '2027-02-06',
                        '2028' => '2028-01-26',
                        '2029' => '2029-02-13',
                        '2030' => '2030-02-03',
                        '2031' => '2031-01-23',
                        '2032' => '2032-02-11',
                        '2033' => '2033-01-31',
                        '2034' => '2034-02-19',
                        '2035' => '2035-02-08',
                        '2036' => '2036-01-28',
                        '2037' => '2037-02-15',
                        '2038' => '2038-02-04',
                        '2039' => '2039-01-24',
                        '2040' => '2040-02-12',
                        '2041' => '2041-02-01',
                        '2042' => '2042-01-22',
                        '2043' => '2043-02-01',
                        '2044' => '2044-01-03',
                        '2045' => '2045-02-17',
                        '2046' => '2046-02-06',
                        '2047' => '2047-01-26',
                        '2048' => '2048-02-14',
                        '2049' => '2049-02-02',
                        '2050' => '2050-01-23');


                    foreach ($date_ImageType8 as $i => $v) {
                        if ($ChartExtdecode['isfestival'] == 1) {
                            $finddate = $lnchunkjiedatelist[$v];
                        } else {
                            $finddate = $v . "/10/01";
                        }
                        //echo $finddate;
                        $findatedata = $this->finddate($datearrlist, $finddate);
                        //print_r($findatedata);
                        if (empty($findatedata)) {
                            continue;
                        }
                        //echo $finddate."|||||";
                        if ($ChartExtdecode['isfestival'] == 1) {
                            if (time() > (strtotime($finddate) - 86400 * 60) && time() <= strtotime($finddate) - 86400 * 3 && in_array($params["dtymd"], array(5, 6))) //超过三天的春节
                            {
                                $findatekey = $findatedata['key'];
                                //echo $findatedata['date']."|||".$finddate;
                                $datenum = $this->getdatebydate($findatedata['date'], $finddate, $params["dtymd"]);
                                if (abs($num[0]) < $datenum) {
                                    continue;
                                }
                                foreach ($num as $o => $d)//
                                {
                                    //echo $d-$datenum;
                                    if ($d + $datenum <= 0) {
                                        $findatekeynew = $findatekey + $d + $datenum;
                                        $shuzhi = $data[$findatekeynew];
                                    } else {
                                        $shuzhi = '';
                                    }

                                    $shuzhi = (empty($shuzhi) ? "'-'" : $shuzhi);
                                    $params['data'][$i1][] = $shuzhi;
                                    $params['datainit'][$i1][] = $shuzhi;
                                }

                                //$params['lendtitle'][$i1] = $v < 2000 ? ($v + 2000) : $v;
                                $params['lendtitle'][$i1] = $v;
                                $i1++;
                                continue;
                            } else if (time() < strtotime($finddate) - 86400 * 3) {
                                continue;
                            }
                        } else {
                            if (time() < strtotime($finddate)) {
                                continue;
                            }
                        }


                        if (strtotime($findatedata['date']) > strtotime($finddate) && $findatedata['key'] == 0) {

                            continue;
                        }


                        //print_r($findatedata);
                        if (strtotime($findatedata['date']) > strtotime($finddate)) {
                            $py = 1;
                        } else if (strtotime($findatedata['date']) < strtotime($finddate)) {
                            $py = -1;
                        } else {
                            $py = 0;
                        }
                        $findatekey = $findatedata['key'];
                        foreach ($num as $o => $d)//
                        {
                            if ($yearorminthdata) {
                                $findatekeynew = $findatekey + $d;
                                $shuzhi = $data[$findatekeynew];
                            } else {
                                if ($py > 0) {
                                    //echo $findatekey."||||".$d."<br/>";
                                    $findatekeynew = $findatekey + $d;
                                    if ($d > 0) {
                                        $findatekeynew = $findatekey + $d - 1;

                                        $shuzhi = $data[$findatekeynew];
                                    } else if ($d < 0) {
                                        $findatekeynew = $findatekey + $d;
                                        $shuzhi = $data[$findatekeynew];
                                    } else {
                                        $findatekeynew = $findatekey + $d;

                                        $shuzhi = ($data[$findatekeynew] + $data[$findatekeynew - 1]) / 2;
                                        if (empty($data[$findatekeynew]) || empty($data[$findatekeynew - 1])) {
                                            $shuzhi = $data[$findatekeynew] + $data[$findatekeynew - 1];
                                        }
                                    }
                                } else if ($py < 0) {
                                    if ($d > 0) {
                                        $findatekeynew = $findatekey + $d;
                                        $shuzhi = $data[$findatekeynew];
                                    } else if ($d < 0) {
                                        $findatekeynew = $findatekey + $d + 1;
                                        $shuzhi = $data[$findatekeynew];
                                    } else {
                                        $findatekeynew = $findatekey + $d;
                                        $shuzhi = ($data[$findatekeynew] + $data[$findatekeynew + 1]) / 2;
                                        if (empty($data[$findatekeynew]) || empty($data[$findatekeynew + 1])) {
                                            $shuzhi = $data[$findatekeynew] + $data[$findatekeynew + 1];
                                        }
                                    }
                                } else {
                                    $findatekeynew = $findatekey + $d;
                                    $shuzhi = $data[$findatekeynew];
                                }
                            }
                            //echo "shenem<br/>";
                            //echo $shuzhi."<br/>";
                            $shuzhi = (empty($shuzhi) ? "'-'" : $shuzhi);
                            $params['data'][$i1][] = $shuzhi;
                            $params['datainit'][$i1][] = $shuzhi;
                        }
                        //$params['lendtitle'][$i1] = $v < 2000 ? ($v + 2000) : $v;
                        $params['lendtitle'][$i1] = $v;
                        $i1++;
                        //exit;
                    }

                    //print_r($params['data']);
                } else//指定日期年折叠图
                {
                    $startleft = 0;
                    if (strtotime('2020-' . $SetDatearr[0]) > strtotime('2020-' . $SetDatearr[1])) {
                        $startleft = 1;
                    }
                    //print_r($params['xlabel']);
                    foreach ($params['xlabel'] as $i => $v) {
                        $v = str_replace('-', '/', $v);
                        //echo $v."<br>";
                        $year = explode('/', $v);
                        //	if(count($year)==1) {$nzd=1;break;}
                        $date = substr($v, strlen($year[0]) + 1);
                        if (!strstr($date, '/'))//为的是数据存为年月格式
                        {
                            $date = $date . "/01";
                            $yearorminthdata = true;
                        }
                        $datearr[$date] = 1;
                        // if($startleft>0)
                        // {
                        // 	//echo $year[0].'/'.$date.'||||||'.$SetDatearr[0]."<br/>";
                        // 	 if(strtotime('2020/'.$date)>=strtotime('2020-'.$SetDatearr[0]))
                        // 	 {
                        // 		// echo $date."鬼啊<br/>";
                        // 		$data[($year[0]+1)][$date]=$params['data'][0][$i];
                        // 	 }
                        // 	 else
                        // 	 {
                        // 		$data[$year[0]][$date]=$params['data'][0][$i];
                        // 	 }
                        // }
                        // else
                        // {
                        // 	$data[$year[0]][$date]=$params['data'][0][$i];
                        // }
                        $data[$year[0]][$date] = $params['data'][0][$i];
                    }
                    //print_r($data);
                    $rndatearr = array("01/01", "01/02", "01/03", "01/04", "01/05", "01/06", "01/07", "01/08", "01/09", "01/10", "01/11", "01/12", "01/13", "01/14", "01/15", "01/16", "01/17", "01/18", "01/19", "01/20", "01/21", "01/22", "01/23", "01/24", "01/25", "01/26", "01/27", "01/28", "01/29", "01/30", "01/31", "02/01", "02/02", "02/03", "02/04", "02/05", "02/06", "02/07", "02/08", "02/09", "02/10", "02/11", "02/12", "02/13", "02/14", "02/15", "02/16", "02/17", "02/18", "02/19", "02/20", "02/21", "02/22", "02/23", "02/24", "02/25", "02/26", "02/27", "02/28", "02/29", "03/01", "03/02", "03/03", "03/04", "03/05", "03/06", "03/07", "03/08", "03/09", "03/10", "03/11", "03/12", "03/13", "03/14", "03/15", "03/16", "03/17", "03/18", "03/19", "03/20", "03/21", "03/22", "03/23", "03/24", "03/25", "03/26", "03/27", "03/28", "03/29", "03/30", "03/31", "04/01", "04/02", "04/03", "04/04", "04/05", "04/06", "04/07", "04/08", "04/09", "04/10", "04/11", "04/12", "04/13", "04/14", "04/15", "04/16", "04/17", "04/18", "04/19", "04/20", "04/21", "04/22", "04/23", "04/24", "04/25", "04/26", "04/27", "04/28", "04/29", "04/30", "05/01", "05/02", "05/03", "05/04", "05/05", "05/06", "05/07", "05/08", "05/09", "05/10", "05/11", "05/12", "05/13", "05/14", "05/15", "05/16", "05/17", "05/18", "05/19", "05/20", "05/21", "05/22", "05/23", "05/24", "05/25", "05/26", "05/27", "05/28", "05/29", "05/30", "05/31", "06/01", "06/02", "06/03", "06/04", "06/05", "06/06", "06/07", "06/08", "06/09", "06/10", "06/11", "06/12", "06/13", "06/14", "06/15", "06/16", "06/17", "06/18", "06/19", "06/20", "06/21", "06/22", "06/23", "06/24", "06/25", "06/26", "06/27", "06/28", "06/29", "06/30", "07/01", "07/02", "07/03", "07/04", "07/05", "07/06", "07/07", "07/08", "07/09", "07/10", "07/11", "07/12", "07/13", "07/14", "07/15", "07/16", "07/17", "07/18", "07/19", "07/20", "07/21", "07/22", "07/23", "07/24", "07/25", "07/26", "07/27", "07/28", "07/29", "07/30", "07/31", "08/01", "08/02", "08/03", "08/04", "08/05", "08/06", "08/07", "08/08", "08/09", "08/10", "08/11", "08/12", "08/13", "08/14", "08/15", "08/16", "08/17", "08/18", "08/19", "08/20", "08/21", "08/22", "08/23", "08/24", "08/25", "08/26", "08/27", "08/28", "08/29", "08/30", "08/31", "09/01", "09/02", "09/03", "09/04", "09/05", "09/06", "09/07", "09/08", "09/09", "09/10", "09/11", "09/12", "09/13", "09/14", "09/15", "09/16", "09/17", "09/18", "09/19", "09/20", "09/21", "09/22", "09/23", "09/24", "09/25", "09/26", "09/27", "09/28", "09/29", "09/30", "10/01", "10/02", "10/03", "10/04", "10/05", "10/06", "10/07", "10/08", "10/09", "10/10", "10/11", "10/12", "10/13", "10/14", "10/15", "10/16", "10/17", "10/18", "10/19", "10/20", "10/21", "10/22", "10/23", "10/24", "10/25", "10/26", "10/27", "10/28", "10/29", "10/30", "10/31", "11/01", "11/02", "11/03", "11/04", "11/05", "11/06", "11/07", "11/08", "11/09", "11/10", "11/11", "11/12", "11/13", "11/14", "11/15", "11/16", "11/17", "11/18", "11/19", "11/20", "11/21", "11/22", "11/23", "11/24", "11/25", "11/26", "11/27", "11/28", "11/29", "11/30", "12/01", "12/02", "12/03", "12/04", "12/05", "12/06", "12/07", "12/08", "12/09", "12/10", "12/11", "12/12", "12/13", "12/14", "12/15", "12/16", "12/17", "12/18", "12/19", "12/20", "12/21", "12/22", "12/23", "12/24", "12/25", "12/26", "12/27", "12/28", "12/29", "12/30", "12/31");

                    if ($yearorminthdata) {
                        $day = date("j", strtotime('2020-' . $SetDatearr[0]));
                        if ($day > 1) {
                            $day = date("n", strtotime('2020-' . $SetDatearr[0]));
                            $day++;
                            if ($day < 10) {
                                $SetDatearr[0] = "0" . $day . "-01";
                            } else {
                                $SetDatearr[0] = $day . "-01";
                            }

                        } else {
                            $SetDatearr[0] = date("m-01", strtotime('2020-' . $SetDatearr[0]));
                        }


                        $SetDatearr[1] = date("m-01", strtotime('2020-' . $SetDatearr[1]));


                        $rndatearr = array('01/01', '02/01', '03/01', '04/01', '05/01', '06/01', '07/01', '08/01', '09/01', '10/01', '11/01', '12/01');
                    }
                    //$closest = key($interval);
                    $key1 = array_search(str_replace('-', '/', $SetDatearr[0]), $rndatearr);
                    $key2 = array_search(str_replace('-', '/', $SetDatearr[1]), $rndatearr);
                    //echo $key1."||||".$key2."<br/>";
                    if ($ChartExtdecode['Type'] != 3) {
                        if ($key1 <= $key2) {
                            $leftdate = array_slice($rndatearr, $key1, ($key2 - $key1 + 1));
                            //print_r($leftdate);
                        } else {
                            $leftdate1 = array_slice($rndatearr, $key1);
                            //print_r($leftdate1);
                            $leftdate2 = array_slice($rndatearr, 0, ($key2 + 1));
                            $leftdate = array_merge($leftdate1, $leftdate2);
                        }
                    } else {
                        $leftdate = array_slice($rndatearr, $key1);
                        //print_r($leftdate);
                        $leftdate_2 = array_slice($rndatearr, 0, $key2 + 1);
                        //print_r($leftdate_2);

                    }


                    //print_r($leftdate);
                    $params['xlabel'] = array();
                    $params['xlabel'] = $leftdate;
                    $params['data'] = array();
                    $params['datainit'] = array();
                    $params['lendtitle'] = array();
                    $i1 = 0;
                    foreach ($date_ImageType8 as $i => $v) {
                        //$datanew=$data[$v]?$data[$v]:$data[($v-2000)];


                        if ($ChartExtdecode['Type'] != 3) {
                            $datanew = $data[$v] ? $data[$v] : $data[(($v - 2000) >= 10 ? ($v - 2000) : "0" . ($v - 2000))];

                            foreach ($leftdate as $o => $d)//年折叠图右侧日期
                            {
                                if ($datanew[$d] == "") {

                                    //$year = $v < 2000 ? $v + 2000 : $v;;
                                    $year = $v;
                                    if (strstr($leftdate[$o], '/')) {
                                        $mdate = $year . "/" . $leftdate[$o];
                                    } else {
                                        $mdate = $year . "/" . $leftdate[$o] . "/01";
                                    }
                                    if (strtotime($mdate) > time()) {
                                        $params['datanew'][$i][$d] = "'-'";
                                        $params['datainitnew'][$i][$d] = "'-'";
                                        continue;
                                    }

					// $params['datanew'][$i][$d]=$this->getbu($datanew,$leftdate,$o-1,$mc_type);
					// if($params['datanew'][$i][$d]== "") {
					// 	$params['datanew'][$i][$d]=$this->getbu2($datanew,$leftdate,$o+1,$mc_type);
					// } 
					                //  $params['datanew'][$i][$d]="'-'";
                                    // if (empty($params['datanew'][$i][$d])) {
                                    //     $params['datanew'][$i][$d] = "'-'";
                                    //     //$params['datanew'][$i][$d]="'null'";
                                    // }

                                    if($params['dtymd']==5)//周数据补
                                    {
                                        $params['datanew'][$i][$d]=$this->getbu2($datanew,$leftdate,$o+1,$mc_type);
                                    }
                                    else
                                    {
                                        $params['datanew'][$i][$d]="'-'";
                                    }
                                    //$params['datainit'][$i][$d] = "'-'";
                                    $params['datainitnew'][$i][$d] = "'-'";
                                        if (empty($params['datanew'][$i][$d])) {
                                            $params['datanew'][$i][$d]="'-'";
                                        }

                                } else {

                                    $params['datanew'][$i][$d] = $datanew[$d];
                                    $params['datainitnew'][$i][$d] = $datanew[$d];

                                }
                                $params['data'][$i][] = $params['datanew'][$i][$d];
                                $params['datainit'][$i][] = $params['datainitnew'][$i][$d];
                            }
                            //$params['lendtitle'][$i] = $v < 2000 ? ($v + 2000) : $v;
                            $params['lendtitle'][$i] = $v ;
                        } else {
                            $datanew = $data[$v] ? $data[$v] : $data[(($v - 2000) >= 10 ? ($v - 2000) : "0" . ($v - 2000))];
                            if ( false) {
                                // print_r($params['datanew1'][$datanew]);
                                // echo $datanew;
                                // $params['data'][$i]=$params['datanew1'][$datanew];
                            } else {
                                foreach ($leftdate as $o => $d)//年折叠图左侧日期
                                {
                                    if ($datanew[$d] == "") {
                                        //$year = $v < 2000 ? $v + 2000 : $v;
                                        $year = $v ;
                                        if (strstr($leftdate[$o], '/')) {
                                            $mdate = $year . "/" . $leftdate[$o];
                                        } else {
                                            $mdate = $year . "/" . $leftdate[$o] . "/01";
                                        }
                                        if (strtotime($mdate) > time()) {
                                            $params['data'][$i][$d] = "'-'";
                                            $params['datainit'][$i][$d] = "'-'";
                                            continue;
                                        }
					// $params['data'][$i][$d]=$this->getbu($datanew,$leftdate,$o-1,$mc_type);
					// if($params['data'][$i][$d]== "") {
					// 	$params['data'][$i][$d]=$this->getbu2($datanew,$leftdate,$o+1,$mc_type);
					// }
					// $params['data'][$i][$d]="'-'";
                    //                     if (empty($params['data'][$i][$d])) {
                    //                         $params['data'][$i][$d] = "'-'";
                    //                     }

                                        if($params['dtymd']==5)//周数据补
                                        {
                                            $params['data'][$i][$d]=$this->getbu2($datanew,$leftdate,$o+1,$mc_type);
                                        }
                                        else
                                        {
                                            $params['data'][$i][$d]="'-'";
                                        }
                                        //$params['datainit'][$i][$d] = "'-'";
                                        $params['datainit'][$i][$d] = "'-'";
                                            if (empty($params['data'][$i][$d])) {
                                                $params['data'][$i][$d]="'-'";
                                            }
    


                                    } else {

                                        $params['data'][$i][$d] = $datanew[$d];
                                        $params['datainit'][$i][$d] = $datanew[$d];

                                    }

                                }
                            }

                            //$datanew1=$data[($v+1)]?$data[($v+1)]:$data[(($v+1)-2000)];
                            $datanew1 = $data[($v + 1)] ? $data[($v + 1)] : $data[(($v + 1 - 2000) >= 10 ? ($v + 1 - 2000) : "0" . ($v + 1 - 2000))];
                            //print_r($datanew1);
                            foreach ($leftdate_2 as $o => $d)//年折叠图右侧日期
                            {
                                //echo $d."<br/>";
                                if ($datanew1[$d] == "") {
                                    //$year = ($v + 1) < 2000 ? ($v + 1) + 2000 : ($v + 1);
                                    $year = ($v + 1);
                                    if (strstr($leftdate_2[$o], '/')) {
                                        $mdate = $year . "/" . $leftdate_2[$o];
                                    } else {
                                        $mdate = $year . "/" . $leftdate_2[$o] . "/01";
                                    }
                                    //echo $mdate."<br/>";
                                    if (strtotime($mdate) > time()) {
                                        //echo "---<br/>";
                                        $params['datanew'][$i][$d] = "'-'";
                                        $params['datainitnew'][$i][$d] = "'-'";
                                        continue;
                                    }
					// $params['datanew'][$i][$d]=$this->getbu($datanew1,$leftdate_2,$o-1,$mc_type);
					// if($params['datanew'][$i][$d]== "") {
					// 	$params['datanew'][$i][$d]=$this->getbu2($datanew1,$leftdate_2,$o+1,$mc_type);
					// } 

					// $params['datanew'][$i][$d]="'-'";
                    //                 if (empty($params['datanew'][$i][$d])) {
                    //                     $params['datanew'][$i][$d] = "'-'";
                    //                 }

                                    if($params['dtymd']==5)//周数据补
                                        {
                                            $params['datanew'][$i][$d]=$this->getbu2($datanew1,$leftdate,$o+1,$mc_type);
                                        }
                                        else
                                        {
                                            $params['datanew'][$i][$d]="'-'";
                                        }
                                        $params['datainitnew'][$i][$d] = "'-'";
                                            if (empty($params['data'][$i][$d])) {
                                                $params['datanew'][$i][$d]="'-'";
                                            }
    

                                } else {

                                    $params['datanew'][$i][$d] = $datanew1[$d];
                                    $params['datainitnew'][$i][$d] = $datanew1[$d];

                                }
                                $params['data'][$i][] = $params['datanew'][$i][$d];
                                $params['datainit'][$i][] = $params['datainitnew'][$i][$d];
                            }
                            //echo $i."<br/>";
                            //print_r($params['data'][$i]);
                            // print_r($params['datanew'][$i]);


                            // $params['data'][$i]=$params['data'][$i]+$params['datanew'][$i];

                            // print_r($params['data'][$i]);


                            $params['datanew1'][($v + 1)] = $params['datanew'][$i];
                            //print_r($params['datanew1']);

                            //$params['lendtitle'][$i] = $v < 2000 ? ($v + 2000) . "-" . ($v + 1 + 2000) : $v . "-" . ($v + 1);
                            $params['lendtitle'][$i] =$v . "-" . ($v + 1);
                        }
                        $i1++;

                    }

                    if ($ChartExtdecode['Type'] == 3) {
                        if ($yearorminthdata) {
                            $nzdtype = true;
                        }

                        $params['xlabel'] = array_merge($leftdate, $leftdate_2);
                    }

                    // 	$i++;
                    // }


                }
            } else {
                foreach ($params['xlabel'] as $i => $v) {
                    $v = str_replace('-', '/', $v);
                    //echo $v."<br>";
                    $year = explode('/', $v);
                    if (count($year) == 1) {
                        $nzd = 1;
                        break;
                    }
                    $date = substr($v, strlen($year[0]) + 1);
                    $datearr[$date] = 1;
                    $data[$year[0]][$date] = $params['data'][0][$i];
                }
                $datearr = is_array($datearr)?$datearr:array();
                $date = array_keys($datearr);
                sort($date);

                $params['xlabel'] = $date;
                $params['data'] = array();
                $params['datainit'] = array();
                $params['lendtitle'] = array();
                $i1 = 0;
                //print"<pre>";
                foreach ($data as $y => $v) {
                    //echo $y."\t";
                    //print_r($v);//exit;
                    foreach ($date as $o => $d) {
                        if ($v[$d] == "") {
                            //$year = $y < 2000 ? $y + 2000 : $y;
                            $year = $y;
                            if (strstr($date[$o], '/')) {
                                $mdate = $year . "/" . $date[$o];
                            } else {
                                $mdate = $year . "/" . $date[$o] . "/01";
                            }
                            if (strtotime($mdate) > time()) {
                                $params['data'][$i1][$d] = "'-'";
                                $params['datainit'][$i1][$d] = "'-'";
                                continue;
                            }

                            if($params['dtymd']==5)//周数据补
                            {
                                $params['data'][$i1][$d]=$this->getbu2($v,$date,$o+1,$mc_type);
                            }
                            else
                            {
                                $params['data'][$i1][$d]="'-'";
                            }
                            $params['datainit'][$i1][$d] = "'-'";
                            //20210301 addbyxr 本期为空不补数据,上期为空的补数据
				//xiangbin add 20230801 start 年折叠不补数据
				//$params['data'][$i1][$d]=$this->getbu2($v,$date,$o+1,$mc_type);
				
				  //xiangbin add 20230801 end 
                            if ($params['data'][$i1][$d] == "") {

                                $params['data'][$i1][$d] = "'-'";

                            }
                            /* $params['data'][$i1][$d]=$this->getbu($v,$date,$o-1);

						if($params['data'][$i1][$d]== "") {
							$params['data'][$i1][$d]=$this->getbu2($v,$date,$o+1);
						}
						*/
                        } else {
                            $params['data'][$i1][$d] = $v[$d];
                            $params['datainit'][$i1][$d] = $v[$d];
                        }
                    }
                    //$params['lendtitle'][$i1] = $y < 2000 ? $y + 2000 : $y;
                    $params['lendtitle'][$i1] = $y;
                    $i1++;
                }
            }
            //年折叠图强制配色位置
            // $zdycolor=array('#5B9BD5','#ED7D31','#A5A5A5','#FFC000','#4472C4','#70AD47','#326797','#9E480E','#636363','#997300','#264478','#43682B','#7CAFDD','#F1975A','#B7B7B7','#FFCD33','#698ED0','#8CC168','#327DC2','#D26012');


            //print"<pre>";print_r($params);exit;//print_r($date);print_r($data);
        }
        //print"<pre>";print_r($data);
        //上面的$params['data'][$i][$d]，最后一年的最后几天会串到之前的天数，所以清一下
        $enddata = '';
        $enddata2 = '';
        foreach ($data as $a => $b) {
            $enddata = $b;

        }

        foreach ($enddata as $a => $b) {
            $enddata2 = $a;//最后有数据的一天

        }


        if ($_GET['debug'] == "2") {

            echo '-----------------------------------------------------';
            print"<pre>";
            print_r($params['data']);
        }

        //echo $enddata2;exit;
        //print"<pre>";$params['data'];


        if (!$nzdtype && !$nzdtypenew) {
            foreach ($params['data'] as $a => $b) {
                foreach ($b as $a1 => $b1) {
                    if (strstr($a1, '/')) {//精确到日期的处理
                        if (strtotime($a1) > strtotime($enddata2) && $a == ($i - 1)) {
                            // echo $a.'-----'.$a1.'<br>';
                            $params['data'][$a][$a1] = "'-'";//最后有数据的一天后都为空
                        }
                    } else {//精确到月份的处理
                        if ($a1 > $enddata2 && $a == ($i - 1)) {
                            $params['data'][$a][$a1] = "'-'";//最后有数据的一天后都为空
                        }
                    }
                }
            }
        }

        if ($_GET['debug'] == "2") {
            print"<pre>";
            print_r($params['data']);
        }


        //url中datajson有CustomColor，就全用CustomColor的
        $CustomColorArray = empty($params['CustomColor']) ? array() : $params['CustomColor'];
        $CustomColor_str = implode('', $CustomColorArray);


        //end ------------------
		$lendtitlenum='';
		if(isset($params['ChartExt']['Type']) && $params['ChartExt']['Type']==2){
			$DateStr=explode(',',$params['ChartExt']['DateStr']);
			// sort($DateStr);
		   if(count($params['lendtitle'])<count($DateStr)){

			//add $DateStr链接里面大于当前年份的在算一个计数
				$tk_num=0;
				
				foreach ($DateStr as $tky => $tkv) { 
					if(date('Y')<$tkv){
						$tk_num=$tk_num+1;
					}
				}
            //end
			$lendtitlenum=count($DateStr)-count($params['lendtitle'])-$tk_num;
		   }
		}

        $cnum = count($zdycolor) - count($params['lendtitle']);//lendtitle个数代表数据个数
        if (empty($zdycolors["CustomColor"]) && empty($zdycolors["CustomColor1"]) && empty($zdycolors["CustomColor2"]) && empty($zdycolors["CustomColor3"]) && empty($zdycolors["CustomColor4"])) {

        } else {
            foreach ($zdycolor as $i => $color) {
                // if($i==(count($params['data'])-1)&&$_GET['ImageType']=="8"){//强制最新的哪条线红色
                // 	$color="#EA0000";
                // }
                if (!empty($CustomColor_str)) {//CustomColor有的话所有线都有，没有都没有。有就不走配色方案，强行用链接的
                    $linecolor[$i] = "#" . $params['CustomColor'][$i];
                } else {
                    if (strstr($color, "#") && strlen($color) == 7 && $color != "#FFFFFF" && $_GET['ImageType'] != "8") {

                        $linecolor[$i] = $color;

                    }

                    if ($_GET['ImageType'] == "8" && empty($params['ChartExt']) && $color != "#FFFFFF") {
                        // $linecolor[$i-$cnum]=$zdycolor[$i];
                        if (date('Y', strtotime($_GET['DateEnd'])) - date('Y', strtotime($_GET['DateStart'])) == (count($params['lendtitle']) - 1)) {
                            $linecolor[$i] = $color;

                        } else if (($i - $cnum) >= 0 && $cnum >= 0) {
                            $linecolor[$i - $cnum] = $zdycolor[$i];
                        }


                    } else if ($_GET['ImageType'] == "8" && !empty($params['ChartExt']) && $color != "#FFFFFF") {
						if($params['ChartExt']['Type']==2){
							if(empty($zdycolor[$i+(int)$lendtitlenum])){
								$linecolor[$i]=$linecolor[$i+(int)$lendtitlenum];
							}else{
								$linecolor[$i]=$zdycolor[$i+(int)$lendtitlenum];
							}
							
						}else{
							$linecolor[$i]=$color;
						} 
						
					}
			  }
				
			
			}
		}

		//xiangbin add 20220805 start
		$themelist=array('#000','#3FDDFF','#000');
		$themelist1=array('#000','#202F55','#000');
		$zcolor=$themelist[$theme];
		$zlinecolor=$themelist1[$theme];
		if($theme>0)
		{
			$linecolor=array('#00F5FF', '#F9AF1A', '#FB4D4B','#E87C25','#27727B','#FE8463','#9BCA63','#FAD860','#F3A43B','#60C0DD');
			if($theme>1)
			{
				$linecolor=array('#1754A4', '#E41643', '#00CCFF', '#F9AF1A', '#FB4D4B','#E87C25','#27727B','#FE8463','#9BCA63','#FAD860','#F3A43B','#60C0DD');
			}
			
		}
         //xiangbin add 20220805 end

        $zdycolor = 'color:["' . implode('","', $linecolor) . '"],';
        $mycolor = 'color:["' . implode('","', $linecolor) . '"],';

        //print"<pre>";print_r($params);exit;
        if ($params['isbg'] != '1') {
            //$bg_img="background:url(http://".$_SERVER['SERVER_NAME'].DCURL."/../images/".BG_IMG.") no-repeat 0px ".(4*count($params['lendtitle'])+10)."px ;";
            if ($mode == 1) {
                $bg_img = "background:url(//" . $_SERVER['SERVER_NAME'] . DCURL . "/../images/" . BG_IMG . ")";
                //if($mc_type==1)
                //$bg_img.="no-repeat 10px ".($mc_type==1?"4":"-4")."px;";
                $bg_img .= "center 50%  no-repeat ";
                //else $bg_img.="no-repeat 15px 60px;";
            } else {
                $bg_img = "background:url(//" . $_SERVER['SERVER_NAME'] . DCURL . "/../images/" . BG_IMG . ") no-repeat center center;";
            }
        } else {
            $bg_img = "";
            //xiangbin add 20201116 start 无背景时数据来源也不显示
            $datasource = "";
            //xiangbin add 20201116 end
		}
        if($params['isbg'] == '3')
        {
            $bg_img = "";
        }
		if($theme>0)
		{
			$datasource="数据来源：钢之家数据中心";
		}
        switch ($params['ImageType']) {
            case '1':
                $ImageType = "'echarts/chart/bar'";
                break;
            case '2':
                $ImageType = "'echarts/chart/line'";
                break;
            case '3':
                $ImageType = "'echarts/chart/bar','echarts/chart/line'";
                break;
            case '4':
                $ImageType = "'echarts/chart/line'";
                break;
            case '5':
                $ImageType = "'echarts/chart/bar'";
                break;
            case '6':
                $ImageType = "'echarts/chart/line'";
                break;
            case '7':
                $ImageType = "'echarts/chart/pie'";
                break;
            case '8':
                $ImageType = "'echarts/chart/bar','echarts/chart/line'";
                break;
            default:
                $params['ImageType'] = "2";
                $ImageType = "'echarts/chart/line'";
                break;
        }

        if ($params['isjz'] == "1" || $params["ImageType"] == "7") {//
            for ($i = 0; $i < count($params['lendtitle']); $i++) {
                $sum = 0;
                $count = 0;
                foreach ($params['datainit'][$i] as $k => $v) {
                    if ($v != '-' && $v != "'-'") {
                        $sum += $v;
                        $count++;
                    }
                }

                $avg[$i] = $count > 0 ? round($sum / $count, 2) : 0;
                //$avg_print[$i]=" ".$junzhi.":".((int)$avg[$i]);
                $avg_print[$i] = " " . $junzhi . ":" . ($avg[$i]);
            }
        }

        if ($isEnglish) {
            $zhe = "Line";
            $zhu = "Bar";
            $zuo = "Left";
            $you = "Right";
        } else {
            $zhe = "线";
            $zhu = "柱";
            $zuo = "左";
            $you = "右";
        }

        if ($params["ImageTitle"]) {
            $bigtitle = $params['ImageTitle'];
        } else {
            $bigtitle = $params['charttitle'];
        }
        // $bigtitle = $this->convert_to_utf8($bigtitle);


        $titlechange = 0;
        $titlestyle = "";
        if ($mode != "1") {
            $rowmaxlen = 28;
            $bigtitle = $this->StrSplit2Row($bigtitle, $rowmaxlen);
            $titlestyle = "textStyle:{color:'black',fontWeight:'normal',fontSize:tipfontsize+2,fontFamily:'SimHei'},";
            if (strlen($bigtitle) > $rowmaxlen) $titlechange = 1;
        } else {
            
            if($smalltitle!=1)
            {
                $titlestyle = "textStyle:{color:'black',fontWeight:'normal',fontFamily:'SimHei'},";
            }
            else
            {
                $titlestyle = "textStyle:{color:'black',fontWeight:'normal',fontSize:14,fontFamily:'SimHei'},";
            }
            
        }
        if ($mode == "1") $titlechange += 1;

        for ($i = 0; $i < count($params['lendtitle']); $i++) {
            if ($params['ImageType'] == 8) $tname[$i] = $_GET['Data1Image'] == 1 ? 'bar' : 'line';
            else $tname[$i] = $this->imagetype($params['ImageType'], $params['dataimagetype'][$i]);
        }

        if ($params['ImageType'] == "3") {
            $yztype = "";
            $bardw = "";
            $linedw = "";
            $mark = 1;
            $sameimage = -1;
            //$i 轴的数量，注意下修改
            for ($i = 0; $i < count($params['lendtitle']); $i++) {
                if ($yztype == "" && $params['zhou' . ($i + 1)] == "1") {
                    if ($params['dataimagetype'][$i] == "1") {
                        $yztype = "bar";
                    } else {
                        $yztype = "line";
                    }
                }
                if ($params['dataimagetype'][$i] == "1") {
                    if ($bardw == "") $bardw = $params['dw'][$i];
                } else {
                    if ($linedw == "") $linedw = $params['dw'][$i];
                }
                $myimagetype = $params['dataimagetype'][$i] == "1" ? 1 : 0;
                if ($sameimage == -1) {
                    $sameimage = $myimagetype;
                } else if ($sameimage != $myimagetype) {
                    $mark = 0;
                }
            }
            if ($mark == 1 || count($params['lendtitle']) < 2) {
                for ($i = 0; $i < count($params['lendtitle']); $i++) {
                    $youzhou[$i] = 0;
                }
                $ldw = $bardw == "" ? $zhe . "(" . $linedw . ")" : $zhu . "(" . $bardw . ")";
                $isyz = 0;
            } else {
                if ($yztype == "") $yztype = "bar";//如果柱线都有，都没有选择右轴，则默认柱为右轴
                for ($i = 0; $i < count($params['lendtitle']); $i++) {
                    if ($params['dataimagetype'][$i] == "1") {
                        if ($yztype == "bar") $youzhou[$i] = 1;
                        else $youzhou[$i] = 0;
                    } else {
                        if ($yztype == "bar") $youzhou[$i] = 0;
                        else $youzhou[$i] = 1;
                    }
                }
                if ($yztype == "bar") {
                    $ldw = $zhe . "(" . $linedw . ")";
                    $rdw = $zhu . "(" . $bardw . ")";
                } else {
                    $ldw = $zhu . "(" . $bardw . ")";
                    $rdw = $zhe . "(" . $linedw . ")";
                }
                $isyz = 1;
            }
        }
        //print_r($params);exit;print_r($youzhou);print_r($isyz);
        if ($params['ImageType'] == "4") {
            $leftdw = "";
            $rightdw = "";
            if (count($params['lendtitle']) == 2 && $params['zhou1'] == 0 && $params['zhou2'] == 0) {
                $params['zhou2'] = 1;
            }
            $mark = 1;
            $allsame = "-1";
            for ($i = 0; $i < count($params['lendtitle']); $i++) {
                if ($allsame == "-1") $allsame = $params['zhou' . ($i + 1)];
                else if ($allsame != $params['zhou' . ($i + 1)]) {
                    $mark = 0;
                }
            }
            if ($mark) {
                for ($i = 0; $i < count($params['lendtitle']); $i++) {
                    $youzhou[$i] = 0;
                }
                $ldw = $zuo . "(" . $params['dw'][0] . ")";
                $isyz = 0;
            } else {
                for ($i = 0; $i < count($params['lendtitle']); $i++) {
                    if ($params['zhou' . ($i + 1)] == "1") {
                        $youzhou[$i] = 1;
                        if ($rightdw == "") $rightdw = $params['dw'][$i];
                    } else {
                        $youzhou[$i] = 0;
                        if ($leftdw == "") $leftdw = $params['dw'][$i];
                    }
                }
                $ldw = $zuo . "(" . $leftdw . ")";
                $rdw = $you . "(" . $rightdw . ")";
                $isyz = 1;
            }

        }

		/*if(count($params['lendtitle'])==2&&$params['zhou1']==0&&$params['zhou2']==0){
			$params['zhou2']=1;
		}

		if($params['ImageType']=="3"||$params['ImageType']=="4"){//只有这两种图才有右轴选择功能：柱线图、两轴线图
			$isyz=0;
			$youzhou="";
			$yzname="";
			$chartnum=4;
			$ldw="";
			$rdw="";
			$rall=1;
			for($i=1;$i<=count($params['lendtitle']);$i++){
				if($params['zhou'.$i]=="1"||$yzname==$tname[$i-1]) {
					if(empty($yzname)){
						$yzname=$tname[$i-1];
						$rdw=$params['dw'][$i-1];
					}
					if($params['zhou'.$i]=="1"){
						$youzhou[$i-1]=1;
					}else{
						$ldw=$params['dw'][$i-1];
					}
					$isyz++;
				}else{
					$ldw=$params['dw'][$i-1];
				}
				if($params['zhou'.$i]!="1") $rall=0;
			}
			if($rall==1) {$ldw="";$isyz=0;$rdw="";}
			if($isyz==$chartnum||$isyz==0) {
				$isyz=0;
				$youzhou=array();
				$yzname="";
			}else{
				$isyz=1;
				if(empty($isEnglish)){
					if($yzname=="bar"){
						$rdw="柱(".$rdw.")";
						$ldw="折(".$ldw.")";
					}else{
						if($params['ImageType']=="3"){
							$rdw="折(".$rdw.")";
							$ldw="柱(".$ldw.")";
						}
						if($params['ImageType']=="4"){
							$rdw="右(".$rdw.")";
							$ldw="左(".$ldw.")";
						}
					}
				}
			}
		}
		*/
        //add by zhangcun for 横轴坐标最右点显示 2018/9/18
        $dataamount = is_array($params['xlabel']) ? count($params['xlabel']) : 0;//数据总数
        if ($params['lineNum'] == 4) {
            $datenum = 4;//显示日期的个数
        } else {
            $datenum = 6;//显示日期的个数
        }

        $jiange = 0;//相邻日期间隔
        $leftjg = 0;//左间隔

        if ($dataamount - 37 < 0) {
            $jiange = round(($dataamount - 1) / 6, 0) + 1;
            $datenum = (int)($dataamount / $jiange);
            $leftjg = ($dataamount - $jiange * $datenum) % $jiange;
        } else {
            $jiange = (int)(($dataamount - 1) / $datenum);
            $leftjg = (int)($dataamount - $jiange * $datenum);
            if ($leftjg * 2 - $datenum > 0) {
                $datenum++;
            }
            $leftjg = ($dataamount - $jiange * $datenum) % $jiange;
        }
        //end
        $DateFormat = $params['DateFormat'];
        if (empty($DateFormat)) $DateFormat = 0;


        $biaozhun = '';
        $biaozhun = str_replace("/", "-", $params['xlabel'][0]);
        $biaozhun = explode("-", $biaozhun);
        if ($DateFormat == 1) {//月数据，显示12个及以下，数据格式对应的不是年月格式的DateFormat跳转0
            if (count($biaozhun) != 2) {//不是月数据,走通用接口
                $DateFormat = 0;
            } else {
                $datenum = 12;

                if ($dataamount > 12) {
                    $jiange = (int)(($dataamount - 1) / $datenum) + 1;
                    $leftjg = (int)($dataamount - $jiange * $datenum);
                    if ($leftjg * 2 - $datenum > 0) {
                        $datenum++;
                    }
                    $leftjg = ($dataamount - $jiange * $datenum) % $jiange;
                }

            }
        }
        // echo $jiange.'<br>';echo $leftjg.'<br>';echo $datenum.'<br>';
        if ($DateFormat == 2) {

            if (count($biaozhun) != 1) {//不是月数据,走通用接口
                $DateFormat = 0;
            } else {
                $datenum = 10;

                if ($dataamount > 2) {
                    $jiange = (int)(($dataamount - 1) / $datenum) + 1;
                    $leftjg = (int)($dataamount - $jiange * $datenum);
                    if ($leftjg * 2 - $datenum > 0) {
                        $datenum++;
                    }
                    $leftjg = ($dataamount - $jiange * $datenum) % $jiange;
                }

            }
        }

        if ($DateFormat == 3) {
            if (count($biaozhun) != 2) {
                $DateFormat = 0;
            } else {
                $datenum = 8;

                if ($dataamount > 8) {
                    $jiange = (int)(($dataamount - 1) / $datenum) + 1;
                    $leftjg = (int)($dataamount - $jiange * $datenum);
                    if ($leftjg * 2 - $datenum > 0) {
                        $datenum++;
                    }
                    $leftjg = ($dataamount - $jiange * $datenum) % $jiange;
                }

            }
        }

        $jsarr = " var colorlist=['" . implode("','", $linecolor) . "']; 
		var dataamount=$dataamount;var datenum=$datenum;var jg=$jiange;var leftjg=$leftjg; var DateFormat=$DateFormat;
		document.getElementById('datasource').style.display='none';var mode=" . $mode . ";var mc_type=" . $mc_type . ";var isyouzhou=" . (empty($rdw) ? 0 : 1) . ";\n var youzhou=\"" . $youzhou[0] . "," . $youzhou[1] . "," . $youzhou[2] . "," . $youzhou[3] . "\".split(',');
		";
        if ($mc_type == "1") {
            $jsarr .= "\n var lengend_y='340';";
            $subtitle = $datasource;
        } else {
            $jsarr .= "var lengend_y='30';";
            $subtitle = $datasource;
        }
        if (empty($ldw)) $ldw = $params['dw'][0];

        $leftmin = array();
        $leftmax = array();
        $rightmin = array();
        $rightmax = array();
        $lminnum = array();
        $lmaxnum = array();
        $rminnum = array();
        $rmaxnum = array();
		$usejd=0;

		if($params['ImageType']=="5"){//堆积柱图
			//count($params['xlabel'])
			for($i=0;$i<count($params['xlabel']);$i++){
				$av[$i]=0;
				for($j=0;$j<count($params['data']);$j++){
					$av[$i]+=floatval($params['data'][$j][$i]);
				}
			}
			$leftmin[]=min($av);
            $leftmax[]=max($av);
			$rightmin[]=min($av);
			$rightmax[]=max($av);
		}else{
            foreach ($params['data'] as $i => $v) {
                if ($youzhou[$i] == "1") {
                    $rightmin[$i] = $params['data'][$i][0];
                    $rightmax[$i] = $params['data'][$i][0];
                    $rminnum[$i] = 0;
                    $rmaxnum[$i] = 0;
                    foreach ($v as $key => $val) {
                        if ($rightmin[$i] == "'-'") {
                            $rightmin[$i] = $val;
                        }
                        if ($val != "'-'") {
                            if ($val < $rightmin[$i]) {
                                $rightmin[$i] = $val;
                                $rminnum[$i] = $key;
                            }
                            if ($val > $rightmax[$i]) {
                                $rightmax[$i] = $val;
                                $rmaxnum[$i] = $key;
                            }
                        }

                    }
                    //$rightmin[]= min($v);
                    //$rightmax[]= max($v);


                } else {
                    // $leftmin[$i]=$params['data'][$i][0];
                    // $leftmax[$i]=$params['data'][$i][0];
                    $params['data'][$i] = $params['data'][$i] ? $params['data'][$i] : [];
                    $leftmin[$i] = reset($params['data'][$i]);
                    $leftmax[$i] = reset($params['data'][$i]);
                    $lminnum[$i] = 0;
                    $lmaxnum[$i] = 0;
                    foreach ($v as $key => $val) {
                        if ($leftmin[$i] == "'-'") {
                            $leftmin[$i] = $val;
                        }
                        if ($val != "'-'") {
                            if ($val < $leftmin[$i]) {
                                $leftmin[$i] = $val;
                                $lminnum[$i] = $key;
                            }
                            if ($val > $leftmax[$i]) {
                                $leftmax[$i] = $val;
                                $lmaxnum[$i] = $key;
                            }
                        }
                    }
                }
            }
        }
	   //print_r($leftmin);print_r($leftmax);
		//print_r($params['data']);
		
		for($ii=0;$ii<2;$ii++){
		   if($ii>0&&$isyz==0) break;
		   if($ii==0){
			   $dmin=$leftmin;
			   $dmax=$leftmax;
		   }else{
			   $dmin=$rightmin;
			   $dmax=$rightmax;
		   }
            $mina = $this->getmin($dmin);
            $maxa = $this->getmax($dmax);


            //add by xurui20190724
            if ($ii == 0) {
                if ($params['leftmin'] != '') {
                    $mina = $params['leftmin'];
                }

                if ($params['leftmax'] != '') {
                    $maxa = $params['leftmax'];
                }
            } else {
                if ($params['rightmin'] != '') {
                    $mina = $params['rightmin'];
                }
                if ($params['rightmax'] != '') {
                    $maxa = $params['rightmax'];
                }
            }

            //end


            $min = $mina;
            //$jg=$this->GetkdbyMaxandMin($maxa,$mina);

            $PRECION = 0.000001;
            $X = ((float)$maxa - (float)$mina) / 8;
            if ($X < $PRECION) {
                $absMax = abs((float)$maxa);
                if ($absMax > $PRECION) {
                    $n = log($absMax, 10);
                    $n = floor($n);
                    $p = pow(10, $n - 1);
                    $maxa = ceil(($maxa + 1) / $p) * $p;
                    $mina = floor(($mina - 1) / $p) * $p;
                } else {
                    $maxa = 4;
                    $mina = -4;
                }
                $X = ($maxa - $mina) / 8;
            }


            if ($X <= 1) {
                $n = log($X, 10);
                $n = -floor($n);
                $p = pow(10, $n + 1);   //2个零时，乘以1000，保证XX.X样式。
                $delta = ceil($X * $p) / $p;
            } else {
                $n = log($X, 10);
                $n = floor($n);
                $p = pow(10, $n - 1);   //4位整数时，除以100，保证XX.X样式。
                $delta = ceil($X / $p) * $p;
            }
            $jg = $delta;
            $mina = $this->GetMinSZ($jg, $mina);
	//xiangbin 20230530 强制零刻度 strat 
	if($mina<0&&($mina+$jg*10)>0)
	{
		$zcnum=floor(-($mina)/$jg);
		if(abs($mina+$jg*$zcnum)>=abs($mina+$jg*($zcnum+1)))
		{
			$mina=($mina-($mina+$jg*($zcnum+1)));
		}
		else
		{
			$mina=($mina-($mina+$jg*($zcnum)))-$jg;
		}
	}
	//xiangbin 20230530 强制零刻度 end 
				
            //add by xr
            //根据左右轴限定最小值
            if ($ii == 0) {
                if ($params['leftmin'] != '') {


                    $mina = $params['leftmin'];
                }

            } else {

                if ($params['rightmin'] != '') {

                    $mina = $params['rightmin'];
                }
            }
            //end

            if ($ii == 0) {
                $xsd = explode('.', $jg);
                if (count($xsd) <= 1) {
                    $xs = 0;
                } else {
                    //$xs=abs($xsd);
                    $xs = strlen($xsd[1]);
                }
            } else {
                $xsd1 = explode('.', $jg);
                if (count($xsd1) <= 1) {
                    $xs1 = 0;
                } else {
                    //$xs=abs($xsd);
                    $xs1 = strlen($xsd1[1]);
                }
            }

            if ($mina < 0 && $min >= 0 && isset($min)) {
                $mina = 0;
            }
            $maxa = $mina + $jg * 10;
            //echo "最小值：".$mina."<br/>";
            //echo "最大值：".$maxa."<br/>";
            //add by xr
            //根据左右轴限定最大值
            if ($ii == 0) {
                if ($params['leftmax'] != '') {


                    $maxa = $params['leftmax'];
                }

            } else {
                if ($params['rightmax'] != '') {


                    $maxa = $params['rightmax'];
                }
            }

            //end
            if (is_nan($jg) && $min == 0) {
                $maxa = 'null';
                $mina = 'null';
            }


            $num = 10;
            //xiangbin add 20180705 end
            $minmax['min'][$ii] = $mina;
            $minmax['max'][$ii] = $maxa;


            if ($ii == 0) {
                $lmin = $mina;
                $lmax = $maxa;
            } else {
                $rmin = $mina;
                $rmax = $maxa;
            }
        }
        $lcha = $minmax['max'][0] - $minmax['min'][0];
        $rcha = $minmax['max'][1] - $minmax['min'][1];

        if ($params['ImageType'] == "5") {
            $lmin = '0';
        }

        if ($_GET['debug'] == 1) {
            echo "lcha:" . $lcha . "</br>";
            echo "rcha:" . $rcha . "</br>";
            echo "lmin:" . $lmin . "</br>";
            echo "lmax:" . $lmax . "</br>";
            echo "rmin:" . $rmin . "</br>";
            echo "rmax:" . $rmax . "</br>";
        }
        if ($isyz == "1") {


            $ykd = $mode == 1 ? $num > 10 ? 10 : $num : 5;
            $ymin = $rmin === "null" ? "" : $rmin;
            $ymax = $rmax === "null" ? "" : $rmax;
            $yinterval = round(($rmax - $rmin) / $ykd, $xs1);

            $yaxis = ",{name:'" . $rdw . "',nameTextStyle:{fontSize:tipfontsize},type : 'value',splitNumber: " . $ykd . ",	axisTick:{show: false},
						axisLabel: {   
					         	textStyle:{
								fontSize:tipfontsize,
								color:'".$zcolor."',
							},
                
							formatter: function (value, index) {
								//console.log(value);
								var val=value*1.0;
								 val=val.toFixed(".$xs1.");
								return val;
							}
							            
						},
						nameTextStyle: {
							fontSize:tipfontsize,
							color: '".$zcolor."',
						},
						".($theme>0?
						"splitLine: {
							show: true,
							lineStyle:{
							color: ['".$zlinecolor."']
						  }
						},":"")."
						//minInterval: 6,
						interval:$yinterval,//每次增加几个  
						axisLine: {lineStyle: {color:'".$zlinecolor."',width:1}}".($rmin==="null"?"":",min:".$rmin).($rmax==="null"?"":",max:".$rmax)."}";
		// $yaxis=",{name:'".$rdw."',nameTextStyle:{fontSize:tipfontsize},type : 'value',splitNumber: ".($mode==1?$num>10?10:$num:5).",
		// axisLabel: {   
		//      	textStyle:{
		// 		fontSize:tipfontsize
		// 	},

		// 	formatter: function (value, index) {
		// 		//console.log(value);
		// 		var val=value*1.0;
		// 		 val=val.toFixed(".$xs1.");
		// 		return val;
		// 	}    
						
		// },axisLine: {lineStyle: {color:'#000',width:1}}".($rmin==="null"?"":",min:".$rmin).($rmax==="null"?"":",max:".$rmax)."}";
			
		}
		
		if(($params['ImageType']=="1"||$params['ImageType']=="5"||$params['ImageType']=="2"||$params['ImageType']=="3"||$params['ImageType']=="4"||$params['ImageType']=="6")){
			if($DateFormat==1){ 
				foreach ($params['xlabel'] as $key => $value) {
					$value=str_replace("/","-",$value);
					$value= explode("-",$value);
					$params['xlabel'][$key]=abs($value[1]).'月';
				}
			}
			if($DateFormat==2){
				foreach ($params['xlabel'] as $key => $value) {
					$value=str_replace("/","-",$value);
					$value= explode("-",$value);
				    $num_year=0;
				    if(strlen($value[0])==2){
						if($value[0]>50){
							$num_year='19'.$value[0];
						}else{
							$num_year='20'.$value[0];
						}
					}else{
						$num_year=$value[0];
					}

                    $params['xlabel'][$key] = $num_year;
                }
            }
            if ($DateFormat == 3) {
                foreach ($params['xlabel'] as $key => $value) {
                    $value = str_replace("/", "-", $value);
                    $value = explode("-", $value);
                    $num_year = 0;
                    if (strlen($value[0]) == 2) {
                        if ($value[0] > 50) {
                            $num_year = '19' . $value[0];
                        } else {
                            $num_year = '20' . $value[0];
                        }
                    } else {
                        $num_year = $value[0];
                    }

                    $params['xlabel'][$key] = $num_year . '-' . $value[1];
                }
            }
        }
		$xlabelStr = is_array($params['xlabel'])?implode('","', $params['xlabel']):"";
        $xlabel = 'data:["' . $xlabelStr . '"]';

        $series = "";
        $strcolor = '';
        foreach ($linecolor as $lck => $lcv) {
            if ($strcolor == '') {
                $strcolor = $lcv;
            } else {
                $strcolor = $strcolor . ',' . $lcv;
            }
        }

        $formatter = ',formatter: function(params){
						//console.log(params);
						var len=params.length;
						var showstr=params[0].name+"<br>";
						var linecolor_arr="'.$strcolor.'";
					
						var lc_arr=  linecolor_arr.split(",");
				
						for(var i=0;i<len;i++){
							
							var dotHtml = "<span style=display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:"+params[i].color+"></span>";
							showstr=showstr+dotHtml;
							//sname=jstitle[i].split("'.$junzhi.'");
							sname=params[i].seriesName.split("'.$junzhi.'");
							sname=sname[0].trim();
							//sname=sname.replace("中国","");
							//sname=sname.replace("价格指数","");
							//var ss=sname;
							//sname="";
							//var lth=30;
							//while(ss.length>lth){
							//	sname+=ss.slice(0,lth)+"<br>";
							//	ss=ss.substring(lth);
							//}
							//sname+=ss;
							if(isyouzhou==1){
									if("'.$isEnglish.'"==1){
											sname=youzhou[i]==1?"right-"+sname:"left-"+sname;
								
	                               }else{
											sname=youzhou[i]==1?"右-"+sname:"左-"+sname;
	                                }
							}
							var mystr=sname+" : "+'.($istry?'"***"':'params[i].value').';
							var mt=parseInt(mystr.length/hhlen);
							if(mystr.length-mt*hhlen>0) mt++;
							for(var t=0;t<mt;t++){
								showstr+=mystr.slice(hhlen*t,hhlen*t+hhlen)+"<br>";
							}
						}
						return showstr
					}';

		$topstyle='';
         if($params['lineNum']!='4'){
			// if(($mc_type==2&&$mode==1)){
			// // if(($mc_type==2&&$mode==1)||($mc_type==3&&$mode==1)){
		
			// 	$topstyle='top: "61px",left:"90px",right:"90px",';
			// }else{
		
		        if(strlen($lmax)>='8'){//根据左轴数字大小调整宽度
					$topstyle='left:"70px",right:"70px",';	
				}
				if(strlen($lmax)>='10'){
					$topstyle='left:"90px",right:"90px",';	
				}
			// }
		 }else{
			{$topstyle='top: "61px",left:"50px",right:"50px",';}
		 } 
	
				
		$grid=',grid:{
					//width:mywidth,
					//height:myChart.height,
					borderWidth:0.4,
					borderColor:"black",
					shadowOffsetY:0,
					shadowOffsetX:0,
					show:true,
					x:xwidth,
				
					'.$topstyle.'
					x2:'.($isyz=="1"?"xwidth":"x2width").',
					y:'.($mode==3||$mode==5?35:50).',
					y2:'.($align=='center'?(50+($mode==3||$mode==5?-10:0)):(30+count($params['data'])*20)).'
				}';
		$xAxis=",xAxis : [
					{
						type : 'category',
					".($theme<1?"
						//splitNumber: 8,
						//splitLine:false,
						boundaryGap: true,
					    
						axisLabel: {
                                    showMaxLabel: true,
									textStyle:{
										fontSize:tipfontsize,
										color:'".$zcolor."',
									}
									".(($params['ImageType']=="1"||$params['ImageType']=="5"||$params['ImageType']=="2"||$params['ImageType']=="3"||$params['ImageType']=="4"||$params['ImageType']=="6")?",interval:function(index,data){
										//console.log('index:'+index);
										//console.log('data:'+data);
										// return true;
										if(DateFormat==1&&(dataamount<=12||index==dataamount-1||(index-leftjg+1>=0&&(index-leftjg+1)%jg==0))){
											 return true;
										}
										if(DateFormat==3&&(dataamount<=8||index==dataamount-1||(index-leftjg+1>=0&&(index-leftjg+1)%jg==0))){
											return true;
									   }
										if(DateFormat==2&&(dataamount<=10||index==dataamount-1||(index-leftjg+1>=0&&(index-leftjg+1)%jg==0))){
											return true;
									    }
										if(dataamount<=7||index==dataamount-1||(index-leftjg+1>=0&&(index-leftjg+1)%jg==0)){ 
											//console.log('index:'+index);
											//if(index/jg>=6)return false;
											return true;
										}
										
										//else console.log(index+':'+data);
									}":	($nzdtype?" ,interval:function(index,data){
										if(data.indexOf('/01')>-1)
										{
						
											return true;
										}
									
									} ,formatter: function (name) {
										var name1=name.split('/');

										if(name1[0].charAt(0) == '0')
										name1 = name1[0].substring(1, name1[0].length);
										if('".$isEnglish."'==1)
										{
											var monthsarr=['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
											return monthsarr[name1[0]-1];
										}
										else
										{
										 return name1[0]+'月';
										}
									}":""))."
									
								},
								axisTick: {  
									alignWithLabel: true,
									textStyle:{
										fontSize:tipfontsize
									},
									// 坐标轴小标记
									show: true       // 属性show控制显示与否，默认不显示
									".(($params['ImageType']=="1"||$params['ImageType']=="5"||$params['ImageType']=="2"||$params['ImageType']=="3"||$params['ImageType']=="4"||$params['ImageType']=="6")?",interval:function(index,data){
										//console.log('index:'+index);
										//console.log('data:'+data);
										
										if(DateFormat==1&&(dataamount<=12||index==dataamount-1||(index-leftjg+1>=0&&(index-leftjg+1)%jg==0))){
											return true;
										}
										if(DateFormat==3&&(dataamount<=8||index==dataamount-1||(index-leftjg+1>=0&&(index-leftjg+1)%jg==0))){
											return true;
									   }
										if(DateFormat==2&&(dataamount<=10||index==dataamount-1||(index-leftjg+1>=0&&(index-leftjg+1)%jg==0))){
											return true;
									    }
										if(dataamount<=7||index==dataamount-1||(index-leftjg+1>=0&&(index-leftjg+1)%jg==0)){ 
										 //console.log('index111:'+index);
										// console.log('dataamount:'+index);
										// console.log('jg:'+jg);
										// console.log('index/jg:'+index/jg);
										// console.log('dataamount/jg:'+dataamount/jg);
										//if(index/jg>=parseInt(dataamount-1/jg))return false;
											return true;
										}
										
										//else console.log(index+':'+data);
									}":"")."
								},":"axisLabel: {
                                    //showMaxLabel: true,
									textStyle:{
										fontSize:tipfontsize,
										color:'".$zcolor."',
									}
								 },
								 axisTick: {  
									alignWithLabel: true,
								 },")."
						axisLine: {lineStyle: {color:'".$zlinecolor."',width:1},onZero:false},
						<{xdata}>
					}
				]";
			
				 $zkd=$mode==1?$num>10?10:$num:5;
				 $zmin=$lmin==="null"?"":$lmin;
				 $zmax=$lmax==="null"?"":$lmax;
				 $zinterval=round(($lmax-$lmin)/$zkd,$xs);
				 $yAxis=",yAxis : [
					{
						name:'<{ldw}>',
						nameTextStyle:{fontSize:tipfontsize},
						type : 'value',
						axisTick:{show: false},
						splitNumber: ".$zkd.",
						axisLabel: {
							textStyle:{
								fontSize:tipfontsize,
								color:'".$zcolor."',
							},
							formatter: function (value, index) {
								//console.log(value);
								var val=value*1.0;
								 val=val.toFixed(".$xs.");
								return val;
							}                
						},
						nameTextStyle: {
							fontSize:tipfontsize,
							color: '".$zcolor."',
						},".($theme>0?
						"splitLine: {
							show: true,
							lineStyle:{
							color: ['".$zlinecolor."']
						  }
						},":"")."
				         //minInterval: 6,
				          interval:$zinterval,//每次增加几个
						axisLine: {lineStyle: {color:'".$zlinecolor."',width:1}}".
						($lmin==="null"?"":",min:<{min_l}>").($lmax==="null"?"":",max:<{max_l}>")
					."}<{other_yaxis}>
				]";
		   /*	
		$yAxis=",yAxis : [
					{
						name:'<{ldw}>',
						nameTextStyle:{fontSize:tipfontsize},
						type : 'value',
						splitNumber: ".($mode==1?$num>10?10:$num:5).",
						axisLabel: {
							textStyle:{
								fontSize:tipfontsize
							},
							formatter: function (value, index) {
								//console.log(value);
								var val=value*1.0;
								 val=val.toFixed(".$xs.");
								return val;
							}                
						},
						axisLine: {lineStyle: {color:'#000',width:1}}".
						($lmin==="null"?"":",min:<{min_l}>").($lmax==="null"?"":",max:<{max_l}>")
					."}<{other_yaxis}>
				]";
			*/
		$jstitle="";
		
		$lendtitle=$params['lendtitle'];
		//echo $mode;exit;
		/*if($mode==1){//pc
			$jq_len=array("1"=>"1000","2"=>"26","3"=>"14","4"=>"10");
		}elseif($mode==2){//android pad
			$jq_len=array("1"=>"1000","2"=>"26","3"=>"6","4"=>"3");
		}elseif($mode==3){//android phone
			$jq_len=array("1"=>"1000","2"=>"26","3"=>"6","4"=>"3");
		}elseif($mode==4){//ipad
			$jq_len=array("1"=>"1000","2"=>"26","3"=>"10","4"=>"8");
		}elseif($mode==5){//iphone
			$jq_len=array("1"=>"1000","2"=>"26","3"=>"6","4"=>"4");
		}else{
			$jq_len=array("1"=>"1000","2"=>"26","3"=>"14","4"=>"10");
		}*/
		$qian=array("　","\t","\n","\r");
		$hou=array("","","","","");
        // $lendtitle = $this->convert_to_utf8($lendtitle);
		foreach($lendtitle as $k=>$title){
			$title=str_replace($qian,$hou,$title);
		    if($params['ImageType']!="7")$title.=$avg_print[$k];
			if($jstitle=="")$jstitle.=$title;
			else $jstitle.="@@".$title;
			//$tle=mb_substr($lendtitle[$k], 0, $jq_len[count($params['lendtitle'])],"utf8");
			//if($tle!=$lendtitle[$k]) $lendtitle[$k]=$tle."...";
		}
		
		$jsarr.="var jstitle='$jstitle'.split('@@');";
		$legendtitle=implode("','",$lendtitle);
		$rand=rand(0,1000);
		$modeltext=file_get_contents(DC_URL.DCURL."/../echarts-2.2.7/model20200413.html?v=".$rand);
		 
		
		if($params['ImageType']=="5"){
			$modeltext=str_replace(',min:<{min_l}>,max:<{max_l}>',"",$modeltext);//堆积柱图可以把最小最大值去掉	
			$stack=",stack: '总量'";
		}
		if($params['ImageType']=="6"){
			$itemStyle=",itemStyle: {normal: {areaStyle: {type: 'default', opacity:0.4}}	}";
		}
		if($params['ImageType']=="7"){
			$formatter=',formatter: "{a} <br/>{b} : {c} ({d}%)"';
			$grid="";
			$xAxis="";
			$yAxis="";

			//$icon="	icon: 'roundRect',";

			$legend_x="x:'left',";
			$formatter=',formatter: function(params){
						//console.log(params);return;
						var sname=params[1].trim();
						//sname=sname.replace("中国","");
						//sname=sname.replace("价格指数","");
						var showstr=sname+" : "+params[2]+"("+params[3]+"%)";
						return showstr
					}';
		}


        $ChartExtLineStyle = array();
        foreach ($params['ChartExtLineStyle'] as $sk => $sv) {
            if ($ChartExtdecode['Type'] != 3) {
                $ChartExtLineStyle[$sv['DateStr']] = $sv;
            } else if ($ChartExtdecode['Type'] == 3) {
                $ChartExtLineStyle[$sv['DateStr'] . '-' . ($sv['DateStr'] + 1)] = $sv;
            }
        }
        if(empty($params['ChartExtLineStyle']))
        {
            $nowyear=date("y");
            $nowyeardata=array();
            if(is_array($params['data']))
            {
                foreach($params['data'][count($params['data'])-1] as $v)
                {
                    $nowyeardata[]=$v;
                }
            }
            
            if($params['ImageType']==8&&isset($data[$nowyear])&&$ChartExtdecode['isfestival'] == 0&&count($data[$nowyear])==1&&$nowyeardata[1]=="'-'")
            {
                $ChartExtLineStyle[$nowyear+2000] = array('DateStr'=>($nowyear+2000),'linestylelight' => 1, 'linestylewidth' =>'', 'linestyletype' => 'solid');
            }
        }

        $series = array();
        $change_icon = "";
        for ($i = 0; $i < count($lendtitle); $i++) {
            if ($params['ImageType'] == "7") {
                //$series[]="{value:".$avg[$i].",name:'".$lendtitle[$i]."'}";
                $series[] = "{value:" . $avg[$i] . ",name:jstitle2['" . $i . "']}";

                //饼图每个都是bar
                $data_icon[$i] = "bar";
            } else {
                //$params['data'][$i][$minnum[$i]]="{value:".$params['data'][$i][$minnum[$i]].",symbol: 'star6',symbolSize : 20,symbolRotate : 10,itemStyle: {normal: { color: 'yellowgreen' },emphasis: {color: 'orange',label : {show: true,position: 'inside',textStyle : {fontSize : '20'}}}}}";
                //$params['data'][$i][$maxnum[$i]]="{value:".$params['data'][$i][$maxnum[$i]].",symbol: 'star6',symbolSize : 20,symbolRotate : 10,itemStyle: {normal: { color: 'yellowgreen' },emphasis: {color: 'orange',label : {show: true,position: 'inside',textStyle : {fontSize : '20'}}}}}";

                //$axisTick="axisTick:{splitNumber: 6},";
                $splitNumber = "splitNumber:8,";
                $chartname = '"name":jstitle2[' . $i . ']';
                $charttype = '"type":"' . $tname[$i] . '"';

                //图例图表表现形式更改，根据之前取得$tname[$i]，bar line，来写
                //add

                if ($tname[$i] == "bar") {

                    $data_icon[$i] = "bar";

                } else if ($tname[$i] == "line") {

                    $data_icon[$i] = "line";

                } else {//暂时其他的就空的，用默认的
                    $data_icon[$i] = "";

                }

                //end

                $yAxisIndex = ",yAxisIndex: " . (empty($youzhou[$i]) ? 0 : 1);
                $chartdataStr = is_array( $params['data'][$i] ) ? implode(',', $params['data'][$i]) : "";
                $chartdata = '"data":[' . $chartdataStr . ']';
                if ($params['isfg'] == 1) {
                    $chart_fg = ",markPoint:{ show:true,data:[{type : 'max', name: '最大值',symbol:'circle',symbolSize:10,symbolOffset: [0, -8],label:{color:'red'},itemStyle:{normal:{color:'rgba(0,0,0,0)'}}},{type : 'min', name: '最小值',symbol:'circle',symbolSize:10, symbolOffset: [0, 8],label:{color:'green'},itemStyle:{normal:{color:'rgba(0,0,0,0)'}}}]}";
                }
                //$showSymbol='symbol:"emptyCircle",showSymbol: false,';
                // $showSymbol='symbol:"none",showSymbol: false,';
                //$showSymbol='showSymbol: true,';//showSymbol：ture 线上原点


                $linestylewidth = '';
                $linestyletype = "";

                if ($mc_type == 3) {
                    $showSymbol = 'symbol:"none",showSymbol: false,smooth: true,connectNulls:true,  ';//平滑度//空线连接
                } else {
                    $showSymbol = 'symbol:"none",showSymbol: false,';//平滑度//空线连接
                }


                if ($params['ImageType'] == "8") {

                    if (isset($ChartExtLineStyle[$lendtitle[$i]]) && !empty($ChartExtLineStyle[$lendtitle[$i]])) {
                        if (!empty($ChartExtLineStyle[$lendtitle[$i]]['linestylewidth'])) {
                            $linestylewidth = 'width:' . $ChartExtLineStyle[$lendtitle[$i]]['linestylewidth'] . ',';
                        }
                        if (!empty($ChartExtLineStyle[$lendtitle[$i]]['linestyletype'])) {
                            $linestyletype = "type:'" . $ChartExtLineStyle[$lendtitle[$i]]['linestyletype'] . "',";
                        }
                        if (empty($ChartExtLineStyle[$lendtitle[$i]]['linestylelight'])) {
                            if ($mc_type == 3) {
                                $showSymbol = 'symbol:"none",showSymbol: false,smooth: true,connectNulls:true,';
                            } else {
                                $showSymbol = 'symbol:"none",showSymbol: false,';
                            }

                            if ($change_icon == '') {//设置高亮，高亮1，不高亮的-1，拼接在一起
                                $change_icon = '-1';
                            } else {
                                $change_icon = $change_icon . '$' . '-1';
                            }
                        } else {
                            if ($mc_type == 3) {
                                $showSymbol = 'showSymbol: true,symbolSize: 8,smooth: true,connectNulls:true,';
                            } else {
                                $showSymbol = 'showSymbol: true,symbolSize: 8,';
                            }

                            if ($change_icon == '') {
                                $change_icon = '1';
                            } else {
                                $change_icon = $change_icon . '$' . '1';
                            }
                        }

                    } else {
                        if ($change_icon == '') {
                            $change_icon = '-1';
                        } else {
                            $change_icon = $change_icon . '$' . '-1';
                        }
                    }

                } else {
                    //其他线图
                    if (isset($params['linestylewidth'][$i])) $linestylewidth = 'width:' . $params['linestylewidth'][$i] . ',';
                    if (isset($params['linestyletype'][$i])) $linestyletype = "type:'" . $params['linestyletype'][$i] . "',";
                    if (!isset($params['linestyletype'][$i]) || $params['linestylelight'][$i] == 0) {
                        if ($mc_type == 3) {
                            $showSymbol = 'symbol:"none",showSymbol: false,smooth: true,connectNulls:true,';
                        } else {
                            $showSymbol = 'symbol:"none",showSymbol: false,';
                        }
                        if ($change_icon == '') {
                            $change_icon = '-1';
                        } else {
                            $change_icon = $change_icon . '$' . '-1';
                        }
                    } else {
                        if ($mc_type == 3) {
                            $showSymbol = 'showSymbol: true,symbolSize: 8,smooth: true,connectNulls:true,';
                        } else {
                            $showSymbol = 'showSymbol: true,symbolSize: 8,';
                        }
                        if ($change_icon == '') {
                            $change_icon = '1';
                        } else {
                            $change_icon = $change_icon . '$' . '1';
                        }
                    }
                }

                if ($tname[$i] == "line") $zlevel = ",zlevel:1";
                else $zlevel = ",zlevel:0";
                if ($params['ImageType'] != "6") { //$linecolor[$i];

                    $itemStyle = ",itemStyle:{
						                  normal:{color:function(params){
												return (colorlist[" . $i . "%colorlist.length])
																  },
												lineStyle:{ 
													    //  width:2,//根据参数加粗 
														   " . $linestylewidth . "
														   // type: 'dashed',//虚线dashed  点dotted 默认线
														   " . $linestyletype . "
															color:'$linecolor[$i]' 
														
														   }
													  
																						
																}
													//高亮			
													//,emphasis:{color:'red'}			
															
											}";
				}
			
				$series[]="{".$axisTick.$showSymbol. $lineStyle.$splitNumber.$chartname.",".$charttype.$yAxisIndex.$stack.$radius.",".$chartdata.$chart_fg.$zlevel.$itemStyle.",connectNulls: true}";
			}
		}

		if($params['ImageType']=="7"){
			$myseries="{ type:'pie',   radius : '50%', center: ['50%', '60%'], itemStyle : dataStyle,label:{normal:{show: true,position: 'inside',formatter: '{d}%'}},data:[".implode(",",$series)."]}";
			$myseries="{ type:'pie',   radius : '66%', center: ['50%', '54%'],label:{normal:{show: true,position: 'outside',formatter: '{d}%'}},data:[".implode(",",$series)."],emphasis:{itemStyle: {shadowBlur: 10,shadowOffsetX: 0,shadowColor: 'rgba(0, 0, 0, 0.5)'}}}";
            $tip_trigger="item";
			$bg_img="";
		}else{
			$myseries=implode(",",$series);
			$tip_trigger="axis";
		}
		
		
		$chart_y=40+16*count($lendtitle)+(count($lendtitle)==4?0:15);
		
		//echo "rand:".$rand."</br>";exit;
	

		$savejs=" ondblclick=\"showsavebtn()\" ";
		if($_GET['mode']=="1"){

			if($params['lineNum']!=4){

				
					if($mc_type==2||$mc_type==3){
					   $fuzhi='<input type="image" src="./images/sg_copy.jpg" tittle="复制" value="复制" onclick="copyimg();" style="float:left;background:#E6E8EB;border:1px solid #D7D9DB;">';
	   
					}elseif($mc_type==0){
						$fuzhi='<input type="image" src="./images/sg_copy.jpg" tittle="复制" value="复制" onclick="copyimg();" style="float:right;background:#E6E8EB;border:1px solid #D7D9DB;">';
		
					}else{
					   $fuzhi='<input type="image" src="./images/zy_copy.jpg" tittle="复制" value="复制" onclick="copyimg();" style="float:left;margin-right:7px;">';
	                }
				   
				   $savejs="onmouseover=\"document.getElementById('save').style.display='block';\" onmouseout=\"document.getElementById('save').style.display='none';\"";
	   
			}
		}
		if($params['lineNum']!=4){
			if($_GET['isbig']!="1"){

				if($mc_type==2||$mc_type==3){//陕钢的换一套
					if($_GET['mode']=="1"){
						$isbig='<input type="image" src="./images/sg_big.jpg" tittle="放大" value="放大" onclick="bigimg();" style="float:left;background:#E6E8EB;border:1px solid #D7D9DB;">';
						}
						$issave='<input type="image" src="./images/sg_save.jpg"  tittle="保存"  value="保存" onclick="saveimg();" style="float:left;background:#E6E8EB;border:1px solid #D7D9DB;">';
					

				}elseif($mc_type==0){
					if($_GET['mode']=="1"){
						$isbig='<input type="image" src="./images/sg_big.jpg" tittle="放大" value="放大" onclick="bigimg();" style="float:right;background:#E6E8EB;border:1px solid #D7D9DB;">';
						}
						$issave='<input type="image" src="./images/sg_save.jpg"  tittle="保存"  value="保存" onclick="saveimg();" style="float:right;background:#E6E8EB;border:1px solid #D7D9DB;">';
					

				}else{
					if($_GET['mode']=="1"){
						$isbig='<input type="image" src="./images/zy_big.png" tittle="放大" value="放大" onclick="bigimg();" style="float:left;margin-right:7px;">';
						}
						$issave='<input type="image" src="./images/zy_save.png"  tittle="保存"  value="保存" onclick="saveimg();" style="float:left;margin-right:7px;">';
					
				}
				
			}else{
				if($mc_type==2||$mc_type==3){//陕钢的换一套
					  $issave='<input type="image" src="./images/sg_save.jpg"  tittle="保存"  value="保存" onclick="saveimg();" style="float:left;background:#E6E8EB;border:1px solid #D7D9DB;">';

				}elseif($mc_type==0){
					$issave='<input type="image" src="./images/sg_save.jpg"  tittle="保存"  value="保存" onclick="saveimg();" style="float:right;background:#E6E8EB;border:1px solid #D7D9DB;">';

			    }else{
					  $issave='<input type="image" src="./images/zy_save.png"  tittle="保存"  value="保存" onclick="saveimg();" style="float:left;margin-right:7px;">';
					
				}

			}
			
		}

		if($mc_type==2||$mc_type==3){
			$containerstyle="height:30px;width:300px; position:fixed;z-index:9999;";

			$select_three=$fuzhi.$issave.$isbig;
		}elseif($mc_type==0){
			$containerstyle="float:right;height:30px;width:100%; position:fixed;z-index:9999;";
			$select_three=$isbig.$issave.$fuzhi;
		}
        //	$copypic=$isEnglish?"copy_en.png":"copy.png";
        //	$savepic=$isEnglish?"save_en.png":"save.png";
       if($mc_type==2||$mc_type==3||$mc_type==0){
		$copypic="sg_copy.jpg";
		$savepic="sg_save.jpg";
		$bigpic="sg_big.jpg";

	   }else{
		$copypic="zy_copy.png";
		$savepic="zy_save.png";
		$bigpic="zy_big.png";
	   }
	  

		if($isEnglish==1){
			$sj='No Data';
		}else{
			$sj='暂无数据';
		}
        //add 单个线图例样式根据数组顺序传过去
		$data_icon_str='';
		foreach ($data_icon as $ic_k => $ic_v) {
			if ($data_icon_str=='') {
				$data_icon_str=$ic_v;
			}else{
			$data_icon_str=$data_icon_str.'$'.$ic_v;
			}
		}
		
		$dataicon_jsarr="var data_icon='$data_icon_str'.split('$');";
       //end
		$change_iconarr="var change_icon='$change_icon'.split('$');";
		$modeltext=str_replace('<{change_icon}>',$change_iconarr,$modeltext);	//年折叠图指定修改图列

		$modeltext=str_replace('<{sj}>',$sj,$modeltext);
		$modeltext=str_replace('<{savejs}>',$savejs,$modeltext);	//电脑版mouseover显示 手机版双击显示 zk  
		$modeltext=str_replace('<{fuzhi}>',$fuzhi,$modeltext);	//背景图
		$modeltext=str_replace('<{copypic}>',$copypic,$modeltext);
		$modeltext=str_replace('<{savepic}>',$savepic,$modeltext);
		$modeltext=str_replace('<{bigpic}>',$bigpic,$modeltext);
		$modeltext=str_replace('<{isbig}>',$isbig,$modeltext);	
		$modeltext=str_replace('<{issave}>',$issave,$modeltext);	

		$modeltext=str_replace('<{background_img}>',$bg_img,$modeltext);	//背景图
		$modeltext=str_replace('<{jsarr}>',$jsarr,$modeltext);	//js数组，用于左右轴的
		$modeltext=str_replace('<{title}>',$bigtitle,$modeltext);//主标题
		$modeltext=str_replace('<{titlestyle}>',$titlestyle,$modeltext);//标题样式
		$modeltext=str_replace('<{subtitle}>',$subtitle,$modeltext);				//子标题
		$modeltext=str_replace('<{tip_trigger}>',$tip_trigger,$modeltext);
		$modeltext=str_replace('<{formatter}>',$formatter,$modeltext);		//标签样式，自定义
		$modeltext=str_replace('<{legend_data}>',$legendtitle,$modeltext);	//四个标题
		$modeltext=str_replace('<{legend_x}>',$legend_x,$modeltext);		//标题x轴位置

		//$modeltext=str_replace('<{icon}>',$icon,$modeltext);		//图标设置
		$modeltext=str_replace('<{data_icon}>',$dataicon_jsarr,$modeltext);		//单个图标设置

		$modeltext=str_replace('<{grid}>',$grid,$modeltext);				//坐标系位置
		$modeltext=str_replace('<{chart_y}>',$chart_y,$modeltext);			//用来控制坐标轴位置
		$modeltext=str_replace('<{xAxis}>',$xAxis,$modeltext);				//X轴设定
		$modeltext=str_replace('<{yAxis}>',$yAxis,$modeltext);				//Y轴设定
		$modeltext=str_replace('<{mycolor}>',$mycolor,$modeltext);			//自定义颜色
		$modeltext=str_replace('<{echarts_types}>',$ImageType,$modeltext);	//图形类型设定
		$modeltext=str_replace('<{xdata}>',$xlabel,$modeltext);				//日期
		$modeltext=str_replace('<{ldw}>',$ldw,$modeltext);					//左轴单位
		$modeltext=str_replace('<{min_l}>',$lmin,$modeltext);				//y轴(左)最小值
		$modeltext=str_replace('<{max_l}>',$lmax,$modeltext);				//y轴(左)最大值
		$modeltext=str_replace('<{other_yaxis}>',$yaxis,$modeltext);		//双轴的右轴
		$modeltext=str_replace('<{series}>',$myseries,$modeltext);			//数值
		$modeltext=str_replace('<{data_source}>',$datasource,$modeltext);	//数据来源

        $modeltext=str_replace('<{imgname}>',$params['imgname'],$modeltext);	//图片名
		
		$modeltext=str_replace('<{callbackname}>',$params['callbackname'],$modeltext);	//图片名
		
		$modeltext=str_replace('<{align}>',$align,$modeltext);	//图例对齐lefe center
		$modeltext=str_replace('<{isstaticimg}>',$isstaticimg,$modeltext);
		$modeltext=str_replace('<{url}>',$url,$modeltext);
		//echo $params['align'];
		$modeltext=str_replace('<{orient}>',$align=='center'?'horizontal':'vertical',$modeltext);
        $modeltext=str_replace('<{width}>',$width,$modeltext);
        $modeltext=str_replace('<{height}>', $height,$modeltext);
		//echo "shenejewqjeio";
		echo $modeltext;exit;
	}

    private function imagetype($imagetype, $dataimagetype)
    {
        //echo $imagetype." ".$dataimagetype."<br>";
        $eimagetype = "";
        switch ($dataimagetype) {
            case "1":
                $eimagetype = "bar";
                break;
            case "2":
                $eimagetype = "line";
                break;
            default:
                $eimagetype = "line";
                break;
        }
        switch ($imagetype) {
            case "1":
            case "5":
                $eimagetype = "bar";
                break;
            case "2":
            case "4":
            case "6":
                $eimagetype = "line";
                break;
            case "7":
                $eimagetype = "pie";
                break;
        }
        //echo $eimagetype."<br>";
        return $eimagetype;
    }

    function convert_to_utf8($params, $array = array())
    {
        // 改版后该方法转换有点问题，也不需要转换utf-8了，所以直接将原数组返回 2023-04-28
        return $params; 
        if (count($array) == 0) {
            if (is_array($params) && count($params) > 0) {
                foreach ($params as $k => $v) {
                    $params[$k] = $this->convert_to_utf8($v);
                }
                return $params;
            } elseif ( is_array($params) && empty($params)) {
            } else {
				if(is_string($params))
				{
					$params = str_replace("\n", "", $params);
					if( mb_detect_encoding($params,"UTF-8, ISO-8859-1, GBK")!="UTF-8" ) {//判断是否不是UTF-8编码，如果不是UTF-8编码，则转换为UTF-8编码
						return  iconv("gbk","utf-8",$params);
					} else {
						return $params;
					}
				}
				else
				{
					return $params;
				}
               
            }
        } else {
            foreach ($params as $key => $value) {
                if (array_key_exists($array, $key)) $params[$key] = $this->convert_to_utf8($value);
            }
        }
    }

	function strconvert($data,$code='UTF-8'){
		if( !empty($data) ){
			$fileType = mb_detect_encoding($data , array('UTF-8','GBK','LATIN1','BIG5')) ;
			if( $fileType != $code){
				$data = mb_convert_encoding($data ,$code , $fileType);
			}
		}
		return $data;
	}
	
	//分割字符串成数组 zk 
	function mbStrSplit ($string, $len=1) {
		$start = 0;
		$strlen = mb_strlen($string);
		while ($strlen) {
			$array[] = mb_substr($string,$start,$len,"utf8");
			$string = mb_substr($string, $len, $strlen,"utf8");
			$strlen = mb_strlen($string);
		}
		return $array;
	}
	//将标题字符串分割成2行的字符串 zk 
	function StrSplit2Row($str,$len){
		$r = $this->mbStrSplit($str, $len);
		$str2row="";
		$rlen=count($r);
		for($i=0;$i<$rlen;$i++){
			if($i==0)
			{
				if($rlen>1)
					$str2row.= $r[$i]."\\n";
				else
					$str2row.= $r[$i];	
			}
			else  
				$str2row.= $r[$i];	
				 
		}
		return $str2row;
	}
	
/* 	function GetmaxorMin($m,$type)
	{
		$delta=0.00001;
		$fun=null;
		if($type==MaxType)
		{
			$fun=ceil;
		}
		else
		{
			$fun=floor;
		}
		$mm=0;
		if(abs($m)>1&&abs($m)<=1000)
		{
			//$m10=$m/10;
			//$mm=$fun($m10)*10;
			//echo "初始值：".$m;
			$mm=$fun($m);
			//echo "结束值：".$mm;
			//echo  $m."xiangbin".$mm."<br/>";
			
		}
		else if($m>1000||$m<-1000)
		{
			$m10=$m/10;
			$mm=$fun($m10)*10;
		}
		else
		{
			$sbm=abs($m);
			if($sbm<$delta)
			{
				$mm=0;
			}
			else
			{
				//echo "<br/>".log10($sbm)."<br/>";
				$n=ceil(log10($sbm))-1;
				//echo $sbm."xiangbin".$n;echo "<br/>";
				$p=pow(10,-$n);
				//echo $p;echo "<br/>";
				$mm=$fun($m*$p)/$p;
			}
		}
		return $mm;
	}
	function GetkdbyMaxandMin($max,$min)
	{
		$cha=($max-$min)/8;
		echo "平均刻度".$cha."<br/>";
		$mm=0;
		if($cha>=100)
		{
			//$mm=ceil($cha);
			$mm=ceil($cha/10)*10;
			echo $cha;
		}
		 else
		{
			
			if($cha>=1)
			{
				$mm=ceil($cha*10)/10;
			}
			else
			{
				$n=floor(log10($cha));
				//echo "<br/>".$n;
				if($n!=0) $n=$n-1;
				$p=pow(10,-$n);
				$mm=ceil($cha*$p)/$p;
			}
			//echo $cha."<br/>";
			
	  }
		return $mm;
		
	} */
    function GetkdbyMaxandMin($Max,$Min)
	{
		$PRECION = 0.000001;
		$X = ($Max-$Min)/8;	
		if($X < $PRECION)
		{
			$absMax = abs($Max);
			if($absMax > $PRECION)
			{
				$n = log($absMax, 10);
				$n = floor($n);
				$p = pow(10, $n - 1);
				$Max = ceil(($Max + 1)/ $p) * $p;
				$Min = floor(($Min - 1) / $p) * $p;
			}
			else
			{
				$Max = 4;
				$Min = -4;
			}
			$X = ($Max - $Min) / 8;
		}
		
		
		
		
		if($X <=1)
		{
			$n = log($X, 10);
			$n = -floor($n);
			$p = pow(10, $n + 1);   //2个零时，乘以1000，保证XX.X样式。
			$delta = ceil($X * $p) / $p;
		}
		else
		{
			$n = log($X, 10);
			$n = floor($n);
			$p = pow(10, $n - 1);   //4位整数时，除以100，保证XX.X样式。
			$delta = ceil($X / $p ) *$p;
		}
		
		return $delta;
	}
	function GetMinSZ($delta,$Min)
	{
		 $n = log($delta, 10);
		$n = floor($n);

        if ($n <= 0)   //delta <= 1
        {
            $jn = -$n + 1;    // delta有2个0时，乘以1000
            $p = pow(10, $jn);
            $newMin = floor($Min * $p) / $p;
        } else      // delta > 1
        {
            $jn = $n - 1;  // delta为3位整数时，除以10
            $p = pow(10, $jn);
            $newMin = floor(floatval( $Min ) / $p) * $p;
        }

        //if($newMin >= $Min)
        //{
        $newMin -= $delta;
        //}
        return $newMin;
    }

    function getbu($array, $date, $k, $mc_type)
    {
        for ($i = $k; $i >= 0; $i--) {
            if ($array[$date[$i]] != "") {
		if($mc_type==3 || $mc_type==4|| $mc_type==5){
			return	"'-'";
                } else {
                    return $array[$date[$i]];
                }
            }
        }
        return "";
    }

    function getbu2($array, $date, $k, $mc_type)
    {
        for ($i = $k; $i <= count($date); $i++) {
            if ($array[$date[$i]] != "") {
                // return $array[$date[$i]];

		if($mc_type==3 || $mc_type==4|| $mc_type==5){
			return	"'-'";
                } else {
                    return $array[$date[$i]];
                }
            }
        }
        return "";
    }
}
?>