<?php

class sg_steelhome_dataAction extends AbstractAction{
	public function __construct(){
		parent::__construct();    
	}

	public function get_city_price($params){
		$curdate=$params['curdate'];
		$nowday=$params['nowday'];
		$lastdate=$params['lastdate'];
		
		$gcprice[0]=array("5620237","7620232","5720431","A120231","X920232","7520431","5320231","5220233","3720436");
		$gcprice[1]=array("5611038","7611035","5711031","A111031","X911031","7511037","5311034","5211031","3713321");
		$gcprice[2]=array("561312h","7613125","5713122","A113121","X913121","7513323","5313121","5213123","3713321");
		$mastertopidlist=array_merge($gcprice[0],$gcprice[1],$gcprice[2]);

		$valarr=$this->get_gzjprice($mastertopidlist,$nowday);
		$valarr2=$this->get_gzjprice($mastertopidlist,$lastdate);

		//主要竞争钢厂调价情况  
		//晋钢
		$gcarr['jg']['1217']=array("63b7901a4130cf5e58a1f765481d0847","ee4a2d46e77fb0ca6fae1c13f7faf743","1d74e8158b6348beaf3cbbebfd5efb2a");
		//山西建龙
		$gcarr['sxjl']['1163']=array("1606c697ce47db674bf1a52a6c593fe8","17ea100ae2a164f1d26456c03a9a83ee","76f2005711466186ea9d611f8f96cb93");
		//建邦
		$gcarr['jb']['1528']=array("5a4d9192679e2db5f5565bdc89f360e8","98f0c9fd0f0e24026a67f2f219df9517","768860d11382f0ae648fe0785cd9fddd");
		//高义
		$gcarr['gy']['1683']=array("62524b79eb46ad080353cf9cac61eed6","80603751bc8d9f7910370f426d1976eb");
		//立恒
		$gcarr['lh']['1540']=array("222053f85f7600b91abd7441d17f81d0","330f89a586cc09ffea4d9b62c27f1d48","51e5aa0e1a37167508051235518edc2d");
		//威钢成都
		$gcarr['wgcd']['505']=array("052de248d01c60406dec17d3d7ccd234","c7b23c56990f422c8503e5c4db4d4d49","ed7a47decadd30444e1f7e5890fb401a");
		//达钢成都
		$gcarr['dgcd']['1466']=array("f76a6fb9ab2de19052d7673c145b14c7","3196205bbfb6d4e79f13d401f353d633","9a38c0caa819980df292ec8e19e1e3c0");
		//威钢重庆
		$gcarr['wgcq']['505']=array("aadfbde92cf37cd1fdcd147d548c61f7","18d148ea1427a348e2b194139086c50f","887824d2d964c20aa83b013cb6825899");
		//德胜成都
		$gcarr['dscd']['1880']=array("75d3f14e431a1162f2a0ab10168e673b");
		//达钢重庆
		$gcarr['dgcq']['1465']=array("222053f85f7600b91abd7441d17f81d0","4f7622648ae22b46259685af8dce476d","7158305bd160c6e4b59e7628c13d1aaf");
		//酒钢兰州
		$gcarr['jglz']['1471']=array("eea4802d73864cf30ba51b46ea980b2c","1a5ab77bcb1d06efb21b8a553cb14101","222f77b1f844e3bdf53f09af6cd57433");
		
		foreach($gcarr as $key=>$arr){
			foreach($arr as $key1=>$value){
				foreach($value as $key2=>$val){
					$ctarr2=$this->get_gcprice($val,$key1,$nowday);
					$ctarr2['variety']=iconv('gb2312','utf-8',$ctarr2['variety']);
					$gctj[$key][]=$ctarr2;
				}
			}
		}

		$NewArray=array(
			"valarr"=>$valarr,
			"valarr2"=>$valarr2,
			"gctj"=>$gctj
		);
		echo json_encode($NewArray);
	}

	//价格id
	public function get_gcprice($onlyid,$gcid,$ftime){
		$list=implode(",",$mastertopidlist);
		$sql="select variety,the_price_tax as price,last_price_tax as oldprice,(the_price_tax-last_price_tax) as zd,gcid,ftime from steelhome_gc.steelprice_info where onlyid='$onlyid' and gcid = '$gcid' and is_show=2 and ftime<='$ftime' order by ftime desc limit 1";
		$ctarr2=$this->gcDao->getRow($sql);
		return $ctarr2;
	}

	//钢厂价格
	public function get_gzjprice($mastertopidlist,$date){
		$list=implode(",",$mastertopidlist);
		$sql="select mastertopid, `price` from steelhome.marketconditions, steelhome.marketrecord where marketconditions.`marketrecordid` = marketrecord.id and mastertopid in ('".implode("','",$mastertopidlist)."') and  managedate>= '".$date." 00:00:00' and managedate<= '".$date." 23:59:59' order by FIND_IN_SET(mastertopid, '$list');";
		$valarr=$this->homeDao->Aquery($sql);
		return $valarr;
	}

	//钢厂采购
	public function get_gccgprice($params){

		$onlyid=$params['onlyid'];
		$gcid=$params['gcid'];
		$date=$params['date'];


		

		$sql = "select the_price_tax from steelhome_gc.SteelCaiGou_base , steelhome_gc.SteelCaiGou_Info where SteelCaiGou_base.id = SteelCaiGou_Info.sb_id and  run_date>='".$date."-01 00:00:00' and run_date<='".$date."-31  23:59:59' and SteelCaiGou_base.is_show = 2 and SteelCaiGou_Info.is_show = 2 and SteelCaiGou_Info.onlyid ='$onlyid' and steel_id ='$gcid'  order by run_date desc limit 1 ";
		$ctarr2=$this->gcDao->getRow($sql);
		echo json_encode($ctarr2);
	}


	public function get_peices7List($params){
		$curdate=$params['curdate'];
		$sdate=$params['sdate'];
		
		$vid7=array('5611035','A111031','X911031','571103d','3711036','531103n','5211035','561312f','A113121','X913121','571312a','3713324','5313124','521312h','562023d','A120231','X920231','5720432','3720439','5320234','522023h','5611032','5611033','5711037','5711031','371103e','3711031','5311037','5311034','521103y','5211031','5613121','561312n','5713121','5713122','371312c','3713321','531312a','5313121','5213128','5213123','5620231','5620432','5720234','5720431','372043m','3720432','5320235','5320231','522043a','5220433','X920231','X920232');
		
		$peices7=$this->get_gzjprice($vid7,$curdate);
		$lase_prices7=$this->get_gzjprice($vid7,$sdate);
		
		$tmp_qh_lwg=$this->maindrc->Aquery("SELECT dta_ym,dta_6 FROM `data_table` WHERE str_to_date(dta_ym, '%Y-%m-%d %H:%i:%s') in(str_to_date('".$sdate."', '%Y-%m-%d %H:%i:%s'),str_to_date('".$curdate."', '%Y-%m-%d %H:%i:%s'))  AND dta_type in('SHQHDAY_4') and dta_1='2010'   order by id ASC") ;
		
		$NewArray=array(
			"peices7"=>$peices7,
			"lase_prices7"=>$lase_prices7,
			"tmp_qh_lwg"=>$tmp_qh_lwg
		);
		echo json_encode($NewArray);
	}

	//增值税率
	public function get_zzs($params){
		$sql="select rdate,rvalue from cntaxrate where isdel=0 and status=1 and rtype=1 order by rdate desc limit 1";
		$valarr=$this->homeDao->getRow($sql);
		echo json_encode($valarr);
	}

	//本月人民币汇率平均
	public function get_rmbhl($params){
		$sdate=$params['sdate'];
		$edate=$params['edate'];

		$sql = "select avg(rd1)as rmbhl from rmbrate where rdate>='".$sdate." 00:00:00' and rdate<='".$edate." 23:59:59'";
		$valarr = $this->maindrc->getRow($sql);

		echo json_encode($valarr);
	}


	//本月人民币汇率平均
	public function get_rmbhl2($params){
		$edate=$params['edate'];
		$sql = "select rd1 as rmbhl from rmbrate where rdate<='".$edate." 23:59:59' order by rdate desc limit 1";
		$valarr = $this->maindrc->getRow($sql);
		echo json_encode($valarr);
	}

	//本月普氏指数平均
	public function get_pszs($params){
		$sdate=$params['sdate'];
		$edate=$params['edate'];
		//$sql="select avg(`price`) as pszs from steelhome.marketconditions, steelhome.marketrecord where marketconditions.`marketrecordid` = marketrecord.id and topicture='H98640' and  managedate>= '".$date."-01 00:00:00' and managedate<= '".$date."-31 23:59:59'";
		
		$sql="select avg(`price`) as pszs from steelhome.marketconditions where  topicture='H98640' and  mconmanagedate>= '".$sdate." 00:00:00' and mconmanagedate<= '".$edate." 23:59:59'";
		
		$valarr = $this->homeDao->getRow($sql);
		
		echo json_encode($valarr);
	}


	//终止日普氏指数
	public function get_daypszs($params){
		$edate=$params['edate'];
		//$sql="select avg(`price`) as pszs from steelhome.marketconditions, steelhome.marketrecord where marketconditions.`marketrecordid` = marketrecord.id and topicture='H98640' and  managedate>= '".$date."-01 00:00:00' and managedate<= '".$date."-31 23:59:59'";
		
		$sql="select `price` as pszs,mconmanagedate from steelhome.marketconditions where  topicture='H98640'  and mconmanagedate<= '".$edate." 23:59:59' order by mconmanagedate desc limit 1";
		
		$valarr = $this->homeDao->getRow($sql);
		echo json_encode($valarr);
	}


	//进口矿，煤矿价格
	public function get_JKKMTPrice($params){
		$priceid=$params["priceid"];
		$date=$params["date"];
		if($priceid!=""){
			$where="";
			if(strlen($priceid)==6){
				$where.=" and topicture='$priceid'";
			}else if(strlen($priceid)==7){
				$where.=" and mastertopid='$priceid'";
			}
			$sql="select articlename,material,factoryarea,marketremarks,`price`  from steelhome.marketconditions where  mconmanagedate<= '".$date." 23:59:59' $where order by mconmanagedate asc limit 1";			
			$arr = $this->homeDao->getRow($sql);
			$content="";
			if($arr["articlename"]!=""){
				$content.= iconv('gb2312','utf-8',$arr["articlename"])."|";
			}
			if($arr["material"]!=""){
				$content.=iconv('gb2312','utf-8',$arr["material"])."|";
			}
			if($arr["factoryarea"]!=""){
				$content.=iconv('gb2312','utf-8',$arr["factoryarea"])."|";
			}
			if($arr["marketremarks"]!=""){
				$content.=iconv('gb2312','utf-8',$arr["marketremarks"])."|";
			}
			$content=substr($content, 0, -1);
			$arr["originname"]=$content;
			echo json_encode($arr);
		}
	}

	//大屏市场价格
	public function get_ScreenPrice($params){
		$topicture=$params["topicture"];
		$edate=$params["edate"];
		$sdate=$params["sdate"];
		if($topicture!=""){
			$sql="select `price` as ydata,DATE_FORMAT(mconmanagedate,'%Y-%m-%d') xdata from steelhome.marketconditions where  topicture='$topicture' and mconmanagedate>= '".$sdate." 00:00:00' and mconmanagedate<= '".$edate." 23:59:59' order by mconmanagedate asc limit 15";
			$arr = $this->homeDao->query($sql);
			echo json_encode($arr);
		}
	}

	//大屏陕晋川甘指数
	public function get_SJCGIndex($params){
		$Type=$params["Type"];
		$edate=$params["edate"];
		$sdate=$params["sdate"];
		if($edate!=""){
			$sql="select `index` as ydata,Date as xdata  from steelhome.SJCGIndex where Type='$Type' and CityName ='西安' and DType=0 and Status =1 and Date>='$sdate' and Date<='$edate' order by Date asc limit 15";
			$arr = $this->homeDao->query($sql);
			echo json_encode($arr);
		}
	}
	

	//大屏市场价格
	public function get_ScreenPriceTime($params){
		$date=$params["date"];
		if($date!=""){
			$sql="(select `price` as ydata,DATE_FORMAT(mconmanagedate,'%Y-%m-%d') xdata,topicture from steelhome.marketconditions where  topicture='562043'  and mconmanagedate<= '".$date." 23:59:59' order by mconmanagedate desc limit 1) UNION ALL (select `price` as ydata,DATE_FORMAT(mconmanagedate,'%Y-%m-%d') xdata,topicture from steelhome.marketconditions where  topicture='561312'  and mconmanagedate<= '".$date." 23:59:59' order by mconmanagedate desc limit 1) UNION ALL (select `price` as ydata,DATE_FORMAT(mconmanagedate,'%Y-%m-%d') xdata,topicture from steelhome.marketconditions where  topicture='561103'  and mconmanagedate<= '".$date." 23:59:59' order by mconmanagedate desc limit 1) UNION ALL (select `price` as ydata,DATE_FORMAT(mconmanagedate,'%Y-%m-%d') xdata,topicture from steelhome.marketconditions where  topicture='566011'  and mconmanagedate<= '".$date." 23:59:59' order by mconmanagedate desc limit 1) UNION ALL (select `price` as ydata,DATE_FORMAT(mconmanagedate,'%Y-%m-%d') xdata,topicture from steelhome.marketconditions where  topicture='676014'  and mconmanagedate<= '".$date." 23:59:59' order by mconmanagedate desc limit 1) ";
			$arr = $this->homeDao->query($sql);
			echo json_encode($arr);
		}
	}


	//大屏陕晋川甘指数
	public function get_ScreenIndexTime($params){
		$date=$params["date"];
		if($date!=""){
			$sql="(select `index` as ydata,Date as xdata,Type  from steelhome.SJCGIndex where Type='1' and CityName ='西安' and DType=0 and Status =1 and Date<='$date' order by Date desc limit 1)  UNION ALL (select `index` as ydata,Date as xdata,Type  from steelhome.SJCGIndex where Type='2' and CityName ='西安' and DType=0 and Status =1 and Date<='$date' order by Date desc limit 1) UNION ALL (select `index` as ydata,Date as xdata,Type  from steelhome.SJCGIndex where Type='3' and CityName ='西安' and DType=0 and Status =1 and Date<='$date' order by Date desc limit 1)";
			$arr = $this->homeDao->query($sql);
			echo json_encode($arr);
		}
	}
	


	//新钢主焦煤指数
	public function get_zjmIndex($params){
		$date=$params["date"];
		if($date!=""){
			$year=date("Y",strtotime($date));
			$month=date("m",strtotime($date));
			$sql="select `monthp`  from steelhome.shpi_month where bigtype='mj' and  type='6'  and smalltype='4' and calyear='$year' and calmonth='$month'  order by id desc limit 1  ";
			$arr = $this->homeDao->getRow($sql);
			echo json_encode($arr);
		}
	}

	public function get_GcPriceData($params){	
		$gcid = $params['gcid'];
		$onlyid = $params['onlyid'];
		$nowdate = $params['edate'];

		$sql="select the_price_tax,ftime from steelhome_gc.steelprice_info where onlyid='$onlyid' and gcid = '$gcid' and is_show=2 and ftime<='$nowdate' order by ftime desc limit 1";
		$DdData=$this->gcDao->getRow($sql);
		echo json_encode($DdData);
	}
	//???????????м??id??
	public function get_rzg_tksPrice($params){	
		$date = $params['date'];
		$sql="select id from marketrecord where managedate>='$date 00:00:00' and managedate<='$date 23:59:59' and title like '%日照港(进口)铁矿石价格行情%' order by managedate desc limit 1  ";
		$DdData=$this->homeDao->getRow($sql);
		$newData=array();
		if($DdData){
			$sql="select articlename,material,factoryarea,marketremarks,price,topicture,mastertopid from marketconditions where marketrecordid=$DdData[id] and isview=0 and (topicture!='' or mastertopid!='') order by Serialnumb asc";
			$RzgData=$this->homeDao->query($sql);
			foreach($RzgData as $key=>$val){
				$content="";
				if($val["articlename"]!=""){
					$content.= iconv('gb2312','utf-8',$val["articlename"])."|";
				}
				if($val["material"]!=""){
					$content.=iconv('gb2312','utf-8',$val["material"])."|";
				}
				if($val["factoryarea"]!=""){
					$content.=iconv('gb2312','utf-8',$val["factoryarea"])."|";
				}
				if($val["marketremarks"]!=""){
					$content.=iconv('gb2312','utf-8',$val["marketremarks"])."|";
				}
				$content=substr($content, 0, -1);//????
				$newData[$key]["originname"]=$content;
				if($val["mastertopid"]!=""){
					$newData[$key]["createtype"]=$val["mastertopid"];
				}else{
					$newData[$key]["createtype"]=$val["topicture"];
				}
				$newData[$key]["price"]=$val["price"];
			}
		}
		echo json_encode($newData);
	}


	public function get_gzjPriceTkspider($params){
		$newData=array();
		if($params['list']!=""){
			if(!is_array($params['list']) ){
				$params['list']=explode(",",$params['list']);
			}
			$date=$params['date']==""?date("Y-m-d"):$params['date'];
			$sql="select articlename,material,factoryarea,marketremarks,mastertopid,topicture, `price` from steelhome.marketconditions, steelhome.marketrecord where marketconditions.`marketrecordid` = marketrecord.id  and  managedate>= '".$date." 00:00:00' and managedate<= '".$date." 23:59:59' and (mastertopid in ('".implode("','",$params['list'])."') or topicture in ('".implode("','",$params['list'])."')) order by Serialnumb asc";
			$valarr=$this->homeDao->query($sql);
			
			foreach($valarr as $key=>$val){
				$content="";
				if($val["articlename"]!=""){
					$content.= iconv('gb2312','utf-8',$val["articlename"])."|";
				}
				if($val["material"]!=""){
					$content.=iconv('gb2312','utf-8',$val["material"])."|";
				}
				if($val["factoryarea"]!=""){
					$content.=iconv('gb2312','utf-8',$val["factoryarea"])."|";
				}
				if($val["marketremarks"]!=""){
					$content.=iconv('gb2312','utf-8',$val["marketremarks"])."|";
				}
				$content=substr($content, 0, -1);
				$newData[$key]["originname"]=$content;
				if(in_array($val["mastertopid"],$params['list'])){
					$newData[$key]["createtype"]=$val["mastertopid"];
				}else{
					$newData[$key]["createtype"]=$val["topicture"];
				}
				$newData[$key]["price"]=$val["price"];
			}
		}

		echo json_encode($newData);
	}


	public function getData_gnk($params){
		$vid=$params["vid"];
		$date=$params['date']==""?date("Y-m-d"):$params['date'];
		$sql="select `weiprice` as price from steelhome.shpi_material_pzp where  vid=$vid and dateday<='$date' order by id desc limit 1";
		$valarr=$this->homeDao->query($sql);
		if(!empty($valarr)){
			$valarr[0]["originname"]=iconv('gb2312','utf-8',"国内矿");
			$valarr[0]["createtype"]=iconv('gb2312','utf-8',"国内矿");
		}
		echo json_encode($valarr);
	}
	
}
?>