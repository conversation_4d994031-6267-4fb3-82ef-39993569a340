@charset "utf-8";
.zam-app-flex{/**flex??**/
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
}
.zam-app-alignCenter{/**flex?? ????**/
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
}
.zam-app-justifyCenter{/**flex?? ????**/
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
}
.zam-app-justifyspacearound{
    justify-content: space-around;
    -webkit-justify-content: space-around;
    -ms-flex-pack: space-around;
}
.zam-app-justifyspacebetween{
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;
}
.zam-app-directionColumn{
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}
.zam-app-flex-alignCenter{
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
}
.zam-app-flex-alignflexend{
    -webkit-box-align: end;
    -webkit-align-items: flex-end;
    -ms-flex-align: end;
    align-items: flex-end;
}
.zam-app-flex-justifyCenter{
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

.zam-app-flex-ajCenter{
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
}
.zam-app-flex-ajCenter2{
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
}
.zam-app-flex-wrap {
    flex-wrap:wrap;
    -webkit-flex-wrap:wrap;
    -ms-flex-wrap: wrap;
}
li{list-style:none}
.pc-box{
    width: 100%;
    height: 100%;
    overflow:hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
.signthreed-wall-block{
    width: 100%;
    height: 100%;
    position: fixed;
    overflow: hidden;
    -webkit-user-drag: none;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    -webkit-perspective: 1200px;
    left:0;
    top:0;
    pointer-events: none;
}
.signthreed-wall-block .wall3d{
    width: 100%;
    height: 100%;
    transform-style: preserve-3d;
    -webkit-transform-style: preserve-3d;
}
.signthreed-wall-block .wall3d.hidden{
    display:none;
}
.wall3d ul {
    position: absolute;
    width:100%;
    height: 100%;
    transform-style: preserve-3d;
    -webkit-transform-style: preserve-3d;
}
.wall3d .card {
    position: absolute;
    width: 50px;
    height: 50px;
    margin: -25px 0 0 -25px;
    top: 50%;
    left: 50%;
    display: none;	   border-radius:50%;	   box-shadow: 0 0 20px #ff0000;
}
  
.wall3d .activate {
    display:block;
}
.wall3d ul li{
    background-color: rgba(255,0,0,1);
    border-radius:5px;
}
.wall3d .casttc li{
    border-radius: 50%;
    overflow: hidden;
}
.wall3d ul li img {
    width: 100%;
    height:100%;
    border-radius:50%;
    -webkit-filter:brightness(1.1) opacity(.7);
}
.lottery-userBox{position:absolute;top:0;left:0;width:100%;height:100%;}
.lottery-userBox.hidden{display:none}
.lottery-userBox-wrapper{
    padding:10px 20px;
    max-width:1200px;
    max-height:560px;
    min-height:240px;
    color:#fff;
    position: relative
}
.lottery-userBox-item{margin:15px 0px}
.lottery-userBox-item .lottery-userBox-avatar {
    width: 140px;
    height: 140px;
    padding: 10px;
}
.lottery-userBox-avatar.frotate {
    position: relative;
    width:150px;
    height:150px;
    padding:8px;
}
.lottery-userBox-avatar.frotate:after {
    content: "";
    width: 100%;
    height: 100%;
    display: inline-block;
    position: absolute;
    top: 0;
    left: 0;
    animation: foreverrotate linear infinite;
    animation-duration: 2s;
    background-image: url("../../images/choujiang/usercircle.png");
    background-size: 100% 100%;
}
@keyframes foreverrotate {
    0% {
        transform: rotate(0)
    }

    to {
        transform: rotate(-1turn)
    }
}
.lottery-userBox-avatar img{width:100%;height:100%;border-radius:50%;}

.lottery-userBox-item p{width:200px;font-size:24px;text-align:center;text-overflow: ellipsis;white-space: nowrap;padding-top: 20px;}
.lottery-userBox-wrapper .box_angle{
    height: 160px;
    position: absolute;
    width: 100%;
    left: 0;
}
.lottery-userBox-wrapper .box_angle:after,.lottery-userBox-wrapper .box_angle:before{
    content:"";
    display: inline-block;
    position: absolute;
    width: 181px;
    height: 160px;
    /* background: url("images/userangle.png"); */
    background-size: 100% 100%;
}
.box_angle_top{top:0;}
.box_angle_top:before {
    top: 0;
    left: -17px;
    transform: rotate(270deg)
}
.box_angle_top:after {
    top: -9px;
    right: -8px;
    transform: rotate(0deg)
}
.box_angle_bottom {
    bottom:0px
}
.box_angle_bottom:before {
    bottom: -10px;
    left: -7px;
    transform: rotate(180deg)
}
.box_angle_bottom:after {
    bottom: -3px;
    right: -18px;
    transform: rotate(90deg)
}

.lottery-luckers{
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0px;
    /* left: calc(50% - 600px); */
    /* background: rgba(0,0,0,.2); */
    border-radius: 10px;
    /* box-shadow: 0 0 2px #c4a071; */
    transform: rotateX(180deg);
}
.lottery-popBox-ani {
    display: block;
    animation: 1s linear 0s 1 normal forwards running lotterypopAni;
}
@keyframes lotterypopAni{from{opacity:0;transform:rotateX(180deg)}to{opacity:1;transform:rotateX(360deg)}}
.lottery-luckers.hidden{display:none}
.lottery-poptitle {
    height: 60px;
    line-height: 60px;
    font-size: 44px;
    color: #ffff00;
    font-weight: 700;
    text-align: center;
     background-image:-webkit-linear-gradient(bottom,#FFC95F,#ffd800); 
  -webkit-background-clip:text; 
  -webkit-text-fill-color:transparent;
  letter-spacing: 8px;
}
.kong{width: 100%;height: 30px;}
.lottery-close-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 112px;
    height:46px;
    cursor: pointer;
    margin:0 auto;
    margin-top: 10px;
    background-image: url("../../images/choujiang/close.png");
    background-repeat: no-repeat;
    background-size: cover;
    display: none;
}

.pcbgBox {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    z-index: -1;
    pointer-events: none;
    background: #000;
    overflow:hidden;
}
.main-bg{
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    z-index:-1;
    background-size:cover;
    background-repeat:no-repeat
}

body {
    height: 100%;
    width: 100%;
    overflow: hidden;
    background-image: url(../../images/choujiang/bodybg.jpg);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    font: 12px/18px "Microsoft Yahei", Tahoma, Helvetica, Arial, Verdana, "\5b8b\4f53", sans-serif;
    color: #51555C;
}
  
h2.top_title{
    height:80px;
    line-height: 80px;
    font-size: 54px;
    color: #f3b747;
    text-align: center;
    letter-spacing:12px;
}
.top_title{
    cursor: pointer;	  background-image:-webkit-linear-gradient(bottom,#f3b747,#ffd800); 
  -webkit-background-clip:text; 
  -webkit-text-fill-color:transparent;
    text-shadow: 3px 3px 3px rgba(255,183,184,0.2);
}
h2.top_meeting{
    height: 80px;
    line-height: 80px;
    font-size: 52px;
    color: #f3b747;
    text-align: center;
    letter-spacing:8px;
}
.top_meeting{
    cursor: pointer;	  background-image:-webkit-linear-gradient(bottom,#f3b747,#ffd800); 
  -webkit-background-clip:text; 
  -webkit-text-fill-color:transparent;
    text-shadow: 3px 3px 3px rgba(255,183,184,0.2);
}
.md {
    color: #ffff00;
    font-weight: bold;
    font-size: 24px;
    text-align: center;
    position: relative;
    height: 40px;
    line-height: 40px;
    cursor: pointer;
    padding: 0 24px;	  background-image:-webkit-linear-gradient(bottom,#f3b747,#ffd800); 
  -webkit-background-clip:text; 
  -webkit-text-fill-color:transparent;
}
.huojiang{
    margin-top: 10px;
}

.start-btn {
    width: 200px;
    height: 52px;
    cursor: pointer;
    margin: 0 auto;
    margin-top: 5px;
    margin-right: -10px;
    background-image: url(../../images/choujiang/start.png);
    background-repeat: no-repeat;
    background-size: cover;
    position: relative;
    z-index: 9999;
}
.lottery-stop-btn {
    right: 20px;
    width: 200px;
    height: 52px;
    cursor: pointer;
    margin: 0 auto;
    margin-top: 5px;
    margin-right: -10px;
    background-image: url(../../images/choujiang/stop.png);
    background-repeat: no-repeat;
    background-size: cover;
    display: none;
}
.lottery-stop-btn-xyj {
  right: 20px;
  width: 200px;
  height: 52px;
  cursor: pointer;
  margin: 0 auto;
  margin-top: 5px;
  margin-right: -10px;
  background-image: url(../../images/choujiang/stop.png);
  background-repeat: no-repeat;
  background-size: cover;
  display: none;
}

.lottery-md-btn {
  right: 20px;
  width: 200px;
  height: 52px;
  cursor: pointer;
  margin: 0 auto;
  margin-top: 5px;
  margin-right: -10px;
  background-image: url(../../images/choujiang/md.png);
  background-repeat: no-repeat;
  background-size: cover;
  display: none;
}
#result {
    width: 88%;
    line-height: 24px;
    font-size: 24px;
    text-align: center;
    color: white;
    border-radius: 8px;
    margin: 0px auto;
}

input.cancel {
    color: #ff0000;
    text-shadow: #ff0 1px 0 0, #ff0 0 1px 0, #ff0 -1px 0 0, #ff0 0 -1px 0;
}
.cancel input {
    border: none;
    outline: none;
    background: none;
    font-size: 30px;
}
.lottery-mainbox {
    top: 90px;
    bottom: 40px;
}
.normal-box {
    width: 100%;
    height: 620px;
    padding: 20px 0 30px;
    position: relative;
}
.lottery-wrapper {
    width: 100%;
    height: 100%;
}
.zam-app-flex-ajCenter2 {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
}
.right-reward-box {
    width: 98%;
    height: 620px;
    margin: 0 18px;
    overflow-y: auto;
    overflow-x: hidden;
    position: relative;
}
.right-lucker {
    width: 162px;
    height: 160px;
    overflow: hidden;
    margin-top: 10px;
    font-size: 14px;
    color: #fff;
    position: relative;
    transition: all .35s;
    -webkit-transition: all .35s;
    -moz-transition: all .35s;
    -ms-transition: all .35s;
    -o-transition: all .35s;
    text-align: center;
    float: left;
}
.right-luckeravatar {
    position: relative;
    width: 100px;
    height: 100px;
    border: 2px solid #fff;
    margin: 0 auto;
    border-radius: 50%;
}
.right-luckeravatar img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
}
.right-luckeravatar .right-luckerdel {
    border-radius: 50%;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    margin: auto;
    line-height: 100px;
    background-color: rgba(0,0,0,.5);
    opacity: 0;
    -webkit-transition: opacity .3s;
    transition: opacity .3s;
    color: #fff;
    cursor: pointer;
}
[class^="zam-icon-"], [class*=" zam-icon-"] {
    font-family: 'zam-icon' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
.title-left {
    width: 200px;
    height: 80px;
    line-height: 80px;
    float: left;
}
.title-right {
    width: 200px;
    height: 80px;
    line-height: 80px;
    float: right;
}
#zjnum2{
    height: auto !important;
    margin: 0px auto;
    width: 160px;
}
.jpxx{
    font-size: 42px;
}
#awardbg {
    margin-top: 2%;
}
.Sparkling{ width:100%; height:100%;display: none;}
.Sparkling img{ width:100%; height:100%;}
@keyframes animate{
		0%{
			filter:opacity(0%)
		}
		25%{
			filter:opacity(50%)
		}
		50%{
			filter:opacity(100%)
		}
		75%{
			filter:opacity(50%)
		}
		100%{
			filter:opacity(0%)
		}
}



.lottery-popBox-prize{
    font-size: 26px;
    color: #ffea86;
    font-weight: 700;
    height: 46px;
    line-height: 46px;
    text-align: center;
    overflow: hidden;
    letter-spacing: 6px;
}
.lottery-luckers-wrapper{
    width:96%;
    height:520px;
    overflow-y:auto;
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    margin: 0px auto;
    /* display: flex;
    justify-content: center;
    align-items: flex-start;
    padding-top: 10px; */
}
.lottery-luckers-item{
    width: 500px;
    margin:10px 30px;
}
.lottery-luckers-img.frotate {
    position: relative;
    width:220px;
    height:220px;
    padding:8px;
}
.lottery-luckers-img.frotate:after {
    content: "";
    width: 100%;
    height: 100%;
    display: inline-block;
    position: absolute;
    top: 0;
    left: 0;
    animation-duration: 2s;
    background-size: 100% 100%
}
.lottery-luckers-img img{
    width: 100%;
    height: 100%;
    border-radius: 50%;
}
.lottery-luckers-name{
    width: 500px;
    overflow: hidden;
    background-size: 100% 100%;
    height:100px;
}
.lottery-luckers-name div{
    font-size: 22px;
    color: yellow;
    text-align: center;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-top: 12px;
}



.lottery-luckers-item1{width: 460px;margin-top: -10vh;}
.lottery-luckers-img1 img {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	border: 2px solid #FFFBEA;
	border: none;
}
.lottery-luckers-img1.frotate {
	position: relative;
	width:190px;
	height: 190px;
	padding: 8px;
}
.lottery-luckers-name1 {
	width: 460px;
	overflow: hidden;
	height: 100px;
}
.lottery-luckers-name1 div {
	font-size: 34px;
	color: #ffff00;
	text-align: center;
	text-overflow: ellipsis;
}
.lottery-luckers-item3 {
    width: 24%;
    margin-top: -4vh;
}
.lottery-luckers-img3 img {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	border: 2px solid #FFFBEA;
	border: none;
}
.lottery-luckers-img3.frotate {
	position: relative;
	width: 130px;
	height: 130px;
	padding: 8px;
}
.lottery-luckers-name3{
	width: 100%;
	overflow: hidden;
	height: 80px;
}
.lottery-luckers-name3 div {
	font-size: 24px;
	color: #ffff00;
	text-align: center;
	text-overflow: ellipsis;
}


.lottery-luckers-item4{width: 20%;margin-top: -10vh;}
.lottery-luckers-img4 img {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	border: 2px solid #FFFBEA;
	border: none;
}
.lottery-luckers-img4.frotate {
	position: relative;
	width: 120px;
	height: 120px;
	padding: 8px;
}
.lottery-luckers-name4{
	width: 100%;
	overflow: hidden;
	height: 100px;
}
.lottery-luckers-name4 div {
	font-size:24px;
	color: #ffff00;
	text-align: center;
	text-overflow: ellipsis;
}

.lottery-luckers-item5{width: 16%;margin-top: -5vh;}
.lottery-luckers-img5 img {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	border: 2px solid #FFFBEA;
	border: none;
}
.lottery-luckers-img5.frotate {
	position: relative;
	width: 112px;
	height: 112px;
	padding: 6px;
}
.lottery-luckers-name5{
	width: 100%;
	overflow: hidden;
	height: 70px;
}
.lottery-luckers-name5 div {
	font-size:26px;
	color: #ffff00;
	text-align: center;
	text-overflow: ellipsis;
}

.lottery-luckers-item6{width: 15%;margin-top: 0vh;}
.lottery-luckers-img6 img {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	border: 2px solid #FFFBEA;
	border: none;
}
.lottery-luckers-img6.frotate {
	position: relative;
	width: 110px;
	height: 110px;
	padding: 6px;
}
.lottery-luckers-name6{
	width: 100%;
	overflow: hidden;
	height: 80px;
}
.lottery-luckers-name6 div {
	font-size:26px;
	color: #ffff00;
	text-align: center;
	text-overflow: ellipsis;
}


.lottery-luckers-item7{width: 21%;}
.lottery-luckers-img7 img {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	border: 2px solid #FFFBEA;
	border: none;
}
.lottery-luckers-img7.frotate {
	position: relative;
	width: 110px;
	height: 110px;
	padding: 8px;
}
.lottery-luckers-name7{
	width: 100%;
	overflow: hidden;
	height: 70px;
}
.lottery-luckers-name7 div {
	font-size:24px;
	color: #ffff00;
	text-align: center;
	text-overflow: ellipsis;
}


.lottery-luckers-item9{width: 17%;margin-top: 2vh;}
.lottery-luckers-img9 img {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	border: 2px solid #FFFBEA;
	border: none;
}
.lottery-luckers-img9.frotate {
	position: relative;
	width: 110px;
	height: 110px;
	padding: 8px;
}
.lottery-luckers-name9{
	width: 100%;
	overflow: hidden;
	height: 70px;
}
.lottery-luckers-name9 div {
	font-size:26px;
	color: #ffff00;
	text-align: center;
	text-overflow: ellipsis;
}


.lottery-luckers-item0{width: 152px;}
.lottery-luckers-img0 img {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	border: 2px solid #FFFBEA;
	border: none;
}
.lottery-luckers-img0.frotate {
	position: relative;
	width:84px;
	height: 84px;
	padding: 3px;
}
.lottery-luckers-name0{
	width: 100%;
	overflow: hidden;
	height: 50px;
}
.lottery-luckers-name0 div {
	font-size:20px;
	color: #ffff00;
	text-align: center;
	text-overflow: ellipsis;
}


.lottery-luckers-item100{width: 84px;}
.lottery-luckers-img100 img {
	width: 100%;
	height: 100%;
	border-radius: 50%;
	border: 2px solid #FFFBEA;
	border: none;
}
.lottery-luckers-img100.frotate {
	position: relative;
	width: 56px;
	height: 56px;
	padding: 3px;
}
.lottery-luckers-name100{
	width: 100%;
	overflow: hidden;
	height: 45px;
}
.lottery-luckers-name100 div {
	font-size:18px;
	color: #ffff00;
	text-align: center;
	text-overflow: ellipsis;
}

.jxbgimage {
    height: 350px;
    border-radius: 20px;
    border: 30px solid rgba(255, 255, 0, 0.15);
}
.jpxx span.jpname {
    font-family: "Arial";
    font-weight: bold;
    color: #ffff00;
    text-shadow: 5px 5px 5px rgba(97, 10, 11, 0.5);
    letter-spacing: 0px;
}

.logo {
    width: 148px;
    position: absolute;
    left: 2%;
    top: 2%;
    z-index: 999;
}
.logo img {
    width: 100%;
}

@media (min-width : 1441px)  and (max-width:1600px){
    
    .lottery-luckers-item3 {
        width: 24%;
    }
    .lottery-luckers-name3 {
        height: 80px;
    }
    .lottery-luckers-name3 div {
        font-size: 22px;
        margin-top: 8px;
    }
    .lottery-luckers-img3.frotate {
        width: 120px;
        height: 120px;
    }


    .lottery-luckers-item4{width: 20%;margin-top: -10vh;}
    .lottery-luckers-name4 {
        height: 70px;
    }
    .lottery-luckers-name4 div {
        font-size: 24px;
    }
    .lottery-luckers-img4.frotate {
        width: 110px;
        height: 110px;
    }

    .lottery-luckers-item5{width: 16%;margin-top: -5vh;}
    .lottery-luckers-img5.frotate {
        position: relative;
        width: 108px;
        height: 108px;
        padding: 6px;
    }
    .lottery-luckers-name5{
        width: 100%;
        overflow: hidden;
        height: 70px;
    }
    .lottery-luckers-name5 div {
        font-size:26px;
    }

   
    .lottery-luckers-item6 {
        width: 15%;
    }
    .lottery-luckers-img6.frotate {
        width: 100px;
        height: 100px;
    }
    .lottery-luckers-name6 {
        width: 100%;
        height: 70px;
    }
    .lottery-luckers-name6 div {
        font-size: 26px;
    }

    .lottery-luckers-item9 {
        width: 17%;
        margin-top: 1vh;
    }
    .lottery-luckers-img9.frotate {
        width: 104px;
        height: 104px;
    }
    .lottery-luckers-name9 {
        width: 100%;
        height: 70px;
    }
    .lottery-luckers-name9 div {
        font-size: 26px;
    }

    /* .lottery-luckers-item100{width: 100px;}
    .lottery-luckers-img100 img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        border: 2px solid #FFFBEA;
        border: none;
    }
    .lottery-luckers-img100.frotate {
        position: relative;
        width: 60px;
        height: 60px;
        padding: 3px;
    }
    .lottery-luckers-name100{
        width: 100%;
        overflow: hidden;
        height: 40px;
    }
    .lottery-luckers-name100 div {
        font-size:16px;
        color: #ffff00;
        text-align: center;
        text-overflow: ellipsis;
        margin-top: 0px;
    } */

    .lottery-luckers-item0{width: 136px;}
    .lottery-luckers-img0.frotate {
        width:76px;
        height: 76px;
        padding: 3px;
    }
    .lottery-luckers-name0{
        width: 100%;
        height: 50px;
    }
    .lottery-luckers-name0 div {
        font-size:20px;
    }
    .lottery-luckers-wrapper{
        width:96%;
        height: 500px;
    }
}

@media (min-width : 1280px)  and (max-width:1440px){
    .lottery-luckers-wrapper{
            width:96%;
            height: 480px;
    }
    .lottery-luckers-item9 {
        width: 17%;
        margin-top: 1vh;
    }
    .lottery-luckers-img9.frotate {
        width: 96px;
        height: 96px;
    }
    .lottery-luckers-name9 {
        width: 100%;
        height: 65px;
    }
    .lottery-luckers-name9 div {
        font-size: 22px;
    }
    
    .lottery-luckers-item5 {
        width: 16%;
        margin-top: -5vh;
    }
    .lottery-luckers-img5.frotate {
        width:106px;
        height: 106px;
    }
    .lottery-luckers-name5 {
        width: 100%;
        height: 70px;
    }
    .lottery-luckers-name5 div {
        font-size: 23px;
    }
    .lottery-luckers-item1{width: 420px;}
    .lottery-luckers-img1.frotate {
        width: 170px;
        height: 170px;
    }
    .lottery-luckers-name1 div {
        font-size: 28px;
    }
    .lottery-luckers-name1 {
        height: 88px;
    }

    .lottery-luckers-item3 {
        width: 24%;
    }
    .lottery-luckers-name3 {
        height: 70px;
    }
    .lottery-luckers-name3 div {
        font-size: 23px;
    }
    .lottery-luckers-img3.frotate {
        width: 110px;
        height: 110px;
    }


    .lottery-luckers-item4 {
        width: 20%;
        margin-top: -10vh;
    }
    .lottery-luckers-name4 {
        height: 70px;
    }
    .lottery-luckers-name4 div {
        font-size: 22px;
        margin-top: 10px;
    }
    .lottery-luckers-img4.frotate {
        width: 106px;
        height: 106px;
    }

    .lottery-luckers-item6 {
        width: 15%;
        margin-top: 0vh;
    }
    .lottery-luckers-img6.frotate {
        width: 92px;
        height: 92px;
        padding: 4px;
    }
    .lottery-luckers-name6 {
        width: 100%;
        height: 70px;
    }
    .lottery-luckers-name6 div {
        font-size: 20px;
    }

    .lottery-luckers-item7 {
        width: 300px;
    }
    .lottery-luckers-name7 {
        height: 70px;
    }
    .lottery-luckers-name7 div {
        font-size: 23px;
    }
    .lottery-luckers-img7.frotate {
        width: 106px;
        height: 106px;
        padding: 4px;
    }

    .lottery-luckers-name0 {
        height: 50px;
        width: 100%;
    }
    .lottery-luckers-item0 {
        width: 120px;
        margin-top: 0vh;
    }
    .lottery-luckers-name0 div {
        font-size: 18px;
    }
    .lottery-luckers-img0.frotate {
        width: 68px;
        height: 68px;
        padding: 3px;
    }

    /* .lottery-luckers-item100{width: 96px;}
    .lottery-luckers-img100 img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
        border: 2px solid #FFFBEA;
        border: none;
    }
    .lottery-luckers-img100.frotate {
        position: relative;
        width: 56px;
        height: 56px;
        padding: 3px;
    }
    .lottery-luckers-name100{
        width: 100%;
        overflow: hidden;
        height:40px;
    }
    .lottery-luckers-name100 div {
        font-size:16px;
        color: #ffff00;
        text-align: center;
        text-overflow: ellipsis;
        margin-top: 0px;
    } */
}

@media (min-width : 769px)  and (max-width:1280px){
    .lottery-luckers-item1{width: 340px;}
    .lottery-luckers-item5 {
        width: 16%;
        margin-top: -5vh;
    }
    .lottery-luckers-img5.frotate {
        width:90px;
        height: 90px;
    }
    .lottery-luckers-name5 {
        width: 100%;
        height: 60px;
    }
    .lottery-luckers-name5 div {
        font-size: 20px;
    }

    .lottery-luckers-item7 {
        width: 250px;
    }
    .lottery-luckers-name7 {
        height: 60px;
    }
    .lottery-luckers-name7 div {
        font-size: 20px;
    }
    .lottery-luckers-img7.frotate {
        width: 98px;
        height: 98px;
        padding: 4px;
    }


    .lottery-luckers-item6 {
        width: 15%;
        margin-top: 0vh;
    }
    .lottery-luckers-img6.frotate {
        width: 88px;
        height: 88px;
        padding: 4px;
    }
    .lottery-luckers-name6 {
        width: 100%;
        height: 70px;
    }
    .lottery-luckers-name6 div {
        font-size: 20px;
    }


    .lottery-luckers-item9 {
        width: 17%;
    }
    .lottery-luckers-img9.frotate {
        width: 90px;
        height: 90px;
    }
    .lottery-luckers-name9 {
        width: 100%;
        height: 60px;
    }
    .lottery-luckers-name9 div {
        font-size:20px;
    }
    .lottery-luckers-item4 {
        width: 20%;
    }
    .lottery-luckers-name4 {
        height: 70px;
    }
    .lottery-luckers-name4 div {
        font-size: 21px;
    }
    .lottery-luckers-img4.frotate {
        width: 96px;
        height: 96px;
    }

    .lottery-luckers-item3 {
        width: 24%;
    }
    .lottery-luckers-name3 {
        height: 70px;
    }
    .lottery-luckers-name3 div {
        font-size: 21px;
    }
    .lottery-luckers-img3.frotate {
        width: 100px;
        height: 100px;
    }


    /* .lottery-luckers-item100{width: 90px;}
    .lottery-luckers-img100.frotate {
        position: relative;
        width: 55px;
        height: 55px;
        padding: 3px;
    }
    .lottery-luckers-name100{
        width: 100%;
        overflow: hidden;
        height: 38px;
    }
    .lottery-luckers-name100 div {
        font-size:15px;
        color: #ffff00;
        text-align: center;
        text-overflow: ellipsis;
        margin-top: 0px;
    } */


    .lottery-luckers-name0 {
        height: 40px;
        width: 100%;
    }
    .lottery-luckers-item0 {
        width: 116px;
        margin-top: 0vh;
    }
    .lottery-luckers-name0 div {
        font-size: 17px;
    }
    .lottery-luckers-img0.frotate {
        width: 64px;
        height: 64px;
        padding: 3px;
    }
    h2.top_title {
        height: 60px;
        line-height: 60px;
        font-size: 46px;
    }
    .lottery-luckers-wrapper{
        width:96%;
        height: 460px;
    }
    .logo {
        width: 128px;
    }
}