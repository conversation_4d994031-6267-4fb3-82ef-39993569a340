
@font-face {
  font-family: 'eletree_icon';
  src:  url('../fonts/eletree_icon.eot?uccppv');
  src:  url('../fonts/eletree_icon.eot?uccppv#iefix') format('embedded-opentype'),
    url('../fonts/eletree_icon.ttf?uccppv') format('truetype'),
    url('../fonts/eletree_icon.woff?uccppv') format('woff'),
    url('../fonts/eletree_icon.svg?uccppv#eletree_icon') format('svg');
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

[class^="eletree_icon-"], [class*=" eletree_icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'eletree_icon' !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.eletree_icon-check_half:before {
  content: "\e904";
}
.eletree_icon-check_none:before {
  content: "\e905";
}
.eletree_icon-check_full:before {
  content: "\e906";
}
.eletree_icon-search:before {
  content: "\e907";
}
.eletree_icon-dropdown_bottom:before {
  content: "\e903";
}
.eletree_icon-dropdown_right:before {
  content: "\e902";
}
.eletree_icon-tree_fold:before {
  content: "\e900";
}
.eletree_icon-tree_leaf:before {
  content: "\e901";
}
.eletree_icon-edit:before {
  content: "\e908";
}
.eletree_icon-file_leaf:before {
  content: "\e924";
}
.eletree_icon-file_fold:before {
  content: "\e92f";
}
.eletree_icon-loading1:before {
  content: "\e97c";
}
.eletree_icon-loading2:before {
  content: "\e982";
}
.eletree_icon-loading3:before {
  content: "\e983";
}
.eletree_icon-delete:before {
  content: "\e9ac";
}
.eletree_icon-add:before {
  content: "\ea0a";
}
.eletree_icon-radio_checked:before {
  content: "\ea54";
}
.eletree_icon-radio_checked_none:before {
  content: "\ea56";
}
