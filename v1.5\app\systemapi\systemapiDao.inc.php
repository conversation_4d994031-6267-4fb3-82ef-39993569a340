<?php
class systemapiDao extends Dao{
  public function __construct( $writer ){
    parent::__construct( $writer );
  }
  //通过guid取得用户信息
	public function get_userinfo_byguid($GUID){
		$sql = "select * from sg_app_session_temp where  GUID ='$GUID'  order by  LoginDate DESC  limit 1";
		return $this->getRow($sql);
  }
  
  //获取用户权限和用户名
	public function get_license_privilege($Uid){
		$sql = "select * from sg_app_license,sg_app_license_privilege where sg_app_license.id=sg_app_license_privilege.lid and sg_app_license_privilege.Uid='$Uid' and sg_app_license_privilege.mc_type=2 limit 1";
		return $this->getRow($sql);
	}
}
?>