<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );

class sgdataimportController extends AbstractController{
  
	public function __construct(){
		parent::__construct();
		$this->_action->setDao( new sgdataimportDao("DRCW") );  //drc
		$this->_action->t1Dao=new sgdataimportDao("MAIN");
		$this->_action->homeDao=new sgdataimportDao("91R");
		// $this->_action->gcDao=new sgdataimportDao("GC");
	}

	public function _dopre()
	{
		$this->_action->checkSession();
	}
	
	function do_uploadFile()
	{
		$this->_action->uploadFile( $this->_request );
	}

	function do_bigFileUpload()
	{
		$this->_action->bigFileUpload( $this->_request );
	}

	public function v_index()
	{
		$this->_action->index( $this->_request );
	}

	public function v_cbrblg()
	{
		$this->_action->cbrblg( $this->_request );
	}

	public function v_cbrbhg()
	{
		$this->_action->cbrbhg( $this->_request );
	}

	public function v_cbzblg()
	{
		$this->_action->cbzblg( $this->_request );
	}

	public function v_cbzbhg()
	{
		$this->_action->cbzbhg( $this->_request );
	}

	function do_drcbrbhg()
	{
		$this->_action->drcbrbhg( $this->_request );
	}

	function do_drcbrblg()
	{
		$this->_action->drcbrblg( $this->_request );
	}

	function do_drcbzblg()
	{
		$this->_action->drcbzblg( $this->_request );
	}

	function do_drcbzbhg()
	{
		$this->_action->drcbzbhg( $this->_request );
	}

}
?>