<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );

class dcpreviewController extends AbstractController{
  
  public function __construct(){
    parent::__construct();
	$this->_action->setDao( new dcpreviewDao("DRCW") );
	$this->_action->t1Dao=new dcpreviewDao("MAIN");
  }

	public function v_index(){
		$this->_action->index( $this->_request );
	}
	public function v_indexnew(){//数字化转型临时用
		$this->_action->index( $this->_request );
	}
	public function v_sg_index(){
		$this->_action->index( $this->_request );
	}

	public function v_dataview(){
		$this->_action->webview( $this->_request );
	}

	public function do_export(){
		$this->_action->doexportdata( $this->_request );
	}

	public function do_del(){
		$this->_action->del( $this->_request );
	}

	public function v_lymanage(){
		$this->_action->lymanage( $this->_request );
	}

	public function v_tblist(){
		$this->_action->tblist( $this->_request );
	}
	public function v_tblist_xg_sg(){
		$this->_action->tblist( $this->_request );
	}

	public function v_tbdata(){
		$this->_action->tbdata( $this->_request );
	}
}
?>