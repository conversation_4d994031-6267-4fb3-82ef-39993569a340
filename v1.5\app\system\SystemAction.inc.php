<?php

//require_once('../../../../steelconf_v3/debug.php');
class SystemAction extends AbstractAction
{

    public $stedao;


    public function __construct()
    {
        parent::__construct();
    }


    // public function checkSession(){
    //     if( $_SESSION['dusername'] == '' || $_SESSION['duserid'] == '' ){
    //       echo "<script>window.top.location.href='index.php?view=login'</script>";
    //     exit;
    //  }
    // }

    //取得设备类型
    private function getSystemType()
    {
        return " ";
    }

    //取得系统版本
    private function getSystemVersion()
    {
        return " ";
    }
    

    //hlf/add-----系统版本检查
    public function CheckLastVersion($params)
    {
        $SignCS = $params['SignCS'];
        $mc_type = $params['mc_type'];
        $isEnglish = $params['isEnglish'];
        $SystemType = $params['SystemType'];
        // if(!empty($params['mc_type']))$mc_type=$params['mc_type'];
        //else $mc_type=0;
        if ($SignCS == '') {
            $arr['Success'] = '0';
            $arr['Message'] = base64_encode('设备号错误');
            if ($isEnglish) $arr['Message'] = base64_encode('Wrong Device Number');
        } else if ($SystemType == '') {
            $arr['Success'] = '0';
            $arr['Message'] = base64_encode('系统类型错误');
            if ($isEnglish) $arr['Message'] = base64_encode('Wrong System Type');
        } else {
            $arr['Success'] = '1';
            $arr['Message'] = base64_encode('查询成功');
            if ($isEnglish) $arr['Message'] = base64_encode('Success');
            if (!empty($params['mc_type']) && $params['mc_type'] == '1') {
                switch ($SystemType) {
                    case 1 :
                        $arr['VersionLast'] = $GLOBALS['RELEASE']['Release_Android_NG'];
                        $arr['VersionDate'] = $GLOBALS['RELEASE']['Release_Android_Date_NG'];
                        $arr['VersionUrl'] = $GLOBALS['RELEASE']['Release_Android_url_NG'];
                        break;
                    case 2 :
                        $arr['VersionLast'] = $GLOBALS['RELEASE']['Release_AndroidPad_NG'];
                        $arr['VersionDate'] = $GLOBALS['RELEASE']['Release_AndroidPad_Date_NG'];
                        $arr['VersionUrl'] = $GLOBALS['RELEASE']['Release_AndroidPad_url_NG'];
                        break;
                    case 3 :
                        $arr['VersionLast'] = $GLOBALS['RELEASE']['Release_PC_NG'];
                        $arr['VersionDate'] = $GLOBALS['RELEASE']['Release_PC_Date_NG'];
                        $arr['VersionUrl'] = $GLOBALS['RELEASE']['Release_PC_url_NG'];
                        $arr['VersionMD5'] = $GLOBALS['RELEASE']['Release_PC_MD5_NG'];
                        break;
                }
            } else if (!empty($params['mc_type']) && $params['mc_type'] == '2') {
                switch ($SystemType) {
                    case 1 :
                        $arr['VersionLast'] = $GLOBALS['RELEASE']['Release_Android_SG'];
                        $arr['VersionDate'] = $GLOBALS['RELEASE']['Release_Android_Date_SG'];
                        $arr['VersionUrl'] = $GLOBALS['RELEASE']['Release_Android_url_SG'];
                        break;
                    case 2 :
                        $arr['VersionLast'] = $GLOBALS['RELEASE']['Release_AndroidPad_SG'];
                        $arr['VersionDate'] = $GLOBALS['RELEASE']['Release_AndroidPad_Date_SG'];
                        $arr['VersionUrl'] = $GLOBALS['RELEASE']['Release_AndroidPad_url_SG'];
                        break;
                    case 3 :
                        $arr['VersionLast'] = $GLOBALS['RELEASE']['Release_PC_SG'];
                        $arr['VersionDate'] = $GLOBALS['RELEASE']['Release_PC_Date_SG'];
                        $arr['VersionUrl'] = $GLOBALS['RELEASE']['Release_PC_url_SG'];
                        $arr['VersionMD5'] = $GLOBALS['RELEASE']['Release_PC_MD5_SG'];
                        break;
                }
            } else if (!empty($params['mc_type']) && $params['mc_type'] == '3') {
                switch ($SystemType) {
                    case 1 :
                        $arr['VersionLast'] = $GLOBALS['RELEASE']['Release_Android_XG'];
                        $arr['VersionDate'] = $GLOBALS['RELEASE']['Release_Android_Date_XG'];
                        $arr['VersionUrl'] = $GLOBALS['RELEASE']['Release_Android_url_XG'];
                        break;
                    case 2 :
                        $arr['VersionLast'] = $GLOBALS['RELEASE']['Release_AndroidPad_XG'];
                        $arr['VersionDate'] = $GLOBALS['RELEASE']['Release_AndroidPad_Date_XG'];
                        $arr['VersionUrl'] = $GLOBALS['RELEASE']['Release_AndroidPad_url_XG'];
                        break;
                    case 3 :
                        $arr['VersionLast'] = $GLOBALS['RELEASE']['Release_PC_XG'];
                        $arr['VersionDate'] = $GLOBALS['RELEASE']['Release_PC_Date_XG'];
                        $arr['VersionUrl'] = $GLOBALS['RELEASE']['Release_PC_url_XG'];
                        $arr['VersionMD5'] = $GLOBALS['RELEASE']['Release_PC_MD5_XG'];
                        break;
                }
			   }else if(!empty($params['mc_type']) && $params['mc_type']=='4' ){
                    switch($SystemType){
                    case 1 :
                        $arr['VersionLast'] = $GLOBALS['RELEASE']['Release_Android_XGJGYC'];
                        $arr['VersionDate'] = $GLOBALS['RELEASE']['Release_Android_Date_XGJGYC'];
                        $arr['VersionUrl'] = $GLOBALS['RELEASE']['Release_Android_url_XGJGYC'];
                        break;
                    case 2 :
                        $arr['VersionLast'] = $GLOBALS['RELEASE']['Release_AndroidPad_XGJGYC'];
                        $arr['VersionDate'] = $GLOBALS['RELEASE']['Release_AndroidPad_Date_XGJGYC'];
                        $arr['VersionUrl'] = $GLOBALS['RELEASE']['Release_AndroidPad_url_XGJGYC'];
                        break;
                    case 3 :
                        $arr['VersionLast'] = $GLOBALS['RELEASE']['Release_PC_XGJGYC'];
                        $arr['VersionDate'] = $GLOBALS['RELEASE']['Release_PC_Date_XGJGYC'];
                        $arr['VersionUrl'] = $GLOBALS['RELEASE']['Release_PC_url_XGJGYC'];
                        $arr['VersionMD5'] = $GLOBALS['RELEASE']['Release_PC_MD5_XGJGYC'];
                        break;
                    }
                }else if(!empty($params['mc_type']) && $params['mc_type']=='5' ){
				switch($SystemType){
				 case 1 :
					 $arr['VersionLast'] = $GLOBALS['RELEASE']['Release_Android_SHANSTEEL'];
					 $arr['VersionDate'] = $GLOBALS['RELEASE']['Release_Android_Date_SHANSTEEL'];
					 $arr['VersionUrl'] = $GLOBALS['RELEASE']['Release_Android_url_SHANSTEEL'];
					 break;
				 case 2 :
					 $arr['VersionLast'] = $GLOBALS['RELEASE']['Release_AndroidPad_SHANSTEEL'];
					 $arr['VersionDate'] = $GLOBALS['RELEASE']['Release_AndroidPad_Date_SHANSTEEL'];
					 $arr['VersionUrl'] = $GLOBALS['RELEASE']['Release_AndroidPad_url_SHANSTEEL'];
					 break;
				 case 3 :
					 $arr['VersionLast'] = $GLOBALS['RELEASE']['Release_PC_SHANSTEEL'];
					 $arr['VersionDate'] = $GLOBALS['RELEASE']['Release_PC_Date_SHANSTEEL'];
					 $arr['VersionUrl'] = $GLOBALS['RELEASE']['Release_PC_url_SHANSTEEL'];
					 $arr['VersionMD5'] = $GLOBALS['RELEASE']['Release_PC_MD5_SHANSTEEL'];
					 break;
				}
				
            } else {
                $mc_type = 0;
                switch ($SystemType) {
                    case 1 :
                        $arr['VersionLast'] = $GLOBALS['RELEASE']['Release_Android_GZJ'];
                        $arr['VersionDate'] = $GLOBALS['RELEASE']['Release_Android_Date_GZJ'];
                        $arr['VersionUrl'] = $GLOBALS['RELEASE']['Release_Android_url_GZJ'];
                        break;
                    case 2 :
                        $arr['VersionLast'] = $GLOBALS['RELEASE']['Release_AndroidPad_GZJ'];
                        $arr['VersionDate'] = $GLOBALS['RELEASE']['Release_AndroidPad_Date_GZJ'];
                        $arr['VersionUrl'] = $GLOBALS['RELEASE']['Release_AndroidPad_url_GZJ'];
                        break;
                    case 3 :
                        if ($isEnglish) {
                            $arr['VersionLast'] = $GLOBALS['RELEASE']['Release_PC_GZJEN'];
                            $arr['VersionDate'] = $GLOBALS['RELEASE']['Release_PC_Date_GZJEN'];
                            $arr['VersionUrl'] = $GLOBALS['RELEASE']['Release_PC_url_GZJEN'];
                            $arr['VersionMD5'] = $GLOBALS['RELEASE']['Release_PC_MD5_GZJEN'];
                        } else {
                            $arr['VersionLast'] = $GLOBALS['RELEASE']['Release_PC_GZJ'];
                            $arr['VersionDate'] = $GLOBALS['RELEASE']['Release_PC_Date_GZJ'];
                            $arr['VersionUrl'] = $GLOBALS['RELEASE']['Release_PC_url_GZJ'];
                            $arr['VersionMD5'] = $GLOBALS['RELEASE']['Release_PC_MD5_GZJ'];
                        }
                        break;

                }

            }

        }

        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

//add by zhangcun for repairtool
    public function RepairToolUpdate($params)
    {
        $SignCS = $params['SignCS'];
        $DataBaseVersion = $params['Version'];
        $isEnglish = $params['isEnglish'];

        $retarr = array('Success' => '0', 'Message' => '', 'URL' => '', 'MD5' => '', 'IsCompress' => '0', 'Status' => '1', 'Arguments' => 'delDbFile');
        if (empty($SignCS) || empty($DataBaseVersion)) {
            $retarr['Message'] = "参数错误";
            if ($isEnglish) $retarr['Message'] = "Wrong Parameter";
        } else {
            $retarr['Success'] = "1";
            $retarr['Message'] = "获取成功";
            if ($isEnglish) $retarr['Message'] = "Success";
            //$retarr['Version'] = $GLOBALS['RELEASE']['Release_RepairTool_Version'];
            //$retarr['VersionDate'] = $GLOBALS['RELEASE']['Release_RepairTool_VersionDate'];
            $retarr['URL'] = $GLOBALS['RELEASE']['Release_RepairTool_URL'];
            $retarr['MD5'] = $GLOBALS['RELEASE']['Release_RepairTool_MD5'];
        }
        $json_string = $this->pri_JSON($retarr);
        echo $json_string;
    }

//add by zhangcun for UpdateLog
    public function GetUpdateLog($params)
    {
        //echo '{"Success":"1","Message":"获取成功","LOG":"MS4g0N64tMj0uMlidWc7DQoyLiDQwtT2wcvSu7j2uabE3DsNCg==","Date":"","NewVersion":""}';exit;
        $SignCS = $params['SignCS'];
        $SoftVersion = $params['SoftVersion'];
        $SystemType = $params['SystemType'];
        $mc_type = $params['mc_type'] ? $params['mc_type'] : 0;
        $isEnglish = $params['isEnglish'];

        $retarr = array('Success' => '0', 'Message' => '', 'LOG' => '', 'Date' => '', 'NewVersion' => '');

        $retarr['NewVersion'] = $GLOBALS['RELEASE']['Release_Android_NG'];
        $retarr['NewVersion'] = $GLOBALS['RELEASE']['Release_AndroidPad_NG'];
        $retarr['NewVersion'] = $GLOBALS['RELEASE']['Release_PC_NG'];

        $retarr['NewVersion'] = $GLOBALS['RELEASE']['Release_Android_GZJ'];
        $retarr['NewVersion'] = $GLOBALS['RELEASE']['Release_AndroidPad_GZJ'];
        $retarr['NewVersion'] = $GLOBALS['RELEASE']['Release_PC_GZJEN'];
        $retarr['NewVersion'] = $GLOBALS['RELEASE']['Release_PC_GZJ'];

        if ($mc_type) {
            switch ($SystemType) {
                case 'NewPC':
                    $retarr['NewVersion'] = $GLOBALS['RELEASE']['Release_PC_NG'];
                    break;
                case 'NewAndroidPad':
                    $retarr['NewVersion'] = $GLOBALS['RELEASE']['Release_AndroidPad_NG'];
                    break;
                case 'NewAndroidPhone':
                    $retarr['NewVersion'] = $GLOBALS['RELEASE']['Release_Android_NG'];
                    break;
                //case 'NewiPad': $retarr['NewVersion'] = $GLOBALS['RELEASE']['Release_Android_NG']; break;
                //case 'NewiPhone': $retarr['NewVersion'] = $GLOBALS['RELEASE']['Release_Android_NG']; break;
            }
        } else {
            if ($isEnglish) $retarr['NewVersion'] = $GLOBALS['RELEASE']['Release_PC_GZJEN'];
            else {
                switch ($SystemType) {
                    case 'NewPC':
                        $arr['VersionLast'] = $GLOBALS['RELEASE']['Release_PC_GZJ'];
                        break;
                    case 'NewAndroidPad':
                        $arr['VersionLast'] = $GLOBALS['RELEASE']['Release_AndroidPad_GZJ'];
                        break;
                    case 'NewAndroidPhone':
                        $arr['VersionLast'] = $GLOBALS['RELEASE']['Release_Android_GZJ'];
                        break;
                    //case 'NewiPad': $arr['VersionLast'] = $GLOBALS['RELEASE']['Release_Android_NG']; break;
                    //case 'NewiPhone': $arr['VersionLast'] = $GLOBALS['RELEASE']['Release_Android_NG']; break;
                }
            }
        }

        if (empty($SignCS) || empty($SoftVersion)) {
            $retarr['Message'] = "参数错误";
            if ($isEnglish) $retarr['Message'] = "Wrong Parameter";
        } else {
            $sql = "select Version,Detail from dc_update_log where SystemType='$SystemType' and mc_type='$mc_type' and isEnglish='$isEnglish' order by id";
            $list = $this->_dao->execute($sql);

            $logcontent = array();
            //$msoftarr=explode('.',$SoftVersion);
            foreach ($list as $k => $v) {
                //echo $v['Version']."=>".$v['Detail']."<br>";
                if ($this->CompareVersion($SoftVersion, $v['Version']) == -1 && $this->CompareVersion($retarr['NewVersion'], $v['Version']) >= 0) {
                    $v['Detail'] = trim($v['Detail']);
                    $logcontent[] = $v['Detail'];
                }
            }
            $i = 1;
            foreach ($logcontent as $k => $v) {
                $v = str_replace("\r\n", ";", $v);
                $vv = explode(';', $v);
                foreach ($vv as $mlog) {
                    $mlog = trim($mlog);
                    if ($mlog && strstr($retarr['LOG'], $mlog) == false) $retarr['LOG'] .= ($i++) . ". " . $mlog . ";\r\n";
                }
            }
            if ($retarr['LOG']) {
                $retarr['LOG'] = base64_encode( $this->utf8ToGb2312( $retarr['LOG'] ) );
                $retarr['Success'] = "1";
                $retarr['Message'] = "获取成功";
                if ($isEnglish) $retarr['Message'] = "Success";
            } else {
                $retarr['Message'] = "获取失败";
                if ($isEnglish) $retarr['Message'] = "Failed";
            }
        }
        $retarr['Message'] = base64_encode( $this->utf8ToGb2312( $retarr['Message'] ) );
        $json_string = $this->pri_JSON($retarr);
        echo $json_string;
    }

    // VersionOne > VersionTwo: 1;
    // VersionOne = VersionTwo: 0;
    // VersionOne < VersionTwo: -1;
    private function CompareVersion($VersionOne, $VersionTwo)
    {
        $ret = 0;

        $onearr = explode('.', $VersionOne);
        $twoarr = explode('.', $VersionTwo);

        $len = count($onearr) > count($twoarr) ? count($onearr) : count($twoarr);

        for ($i = 0; $i < $len; $i++) {
            if ($twoarr[$i] - $onearr[$i] > 0) {
                $ret = -1;
                break;
            } else if ($twoarr[$i] - $onearr[$i] < 0) {
                $ret = 1;
                break;
            }
        }
        if ($_REQUEST['debug']) echo $VersionOne . ":" . $VersionTwo . "=" . $ret . "<br>";
        return $ret;
    }

    /**
     * 函数名称: getIP
     * 函数功能: 取得手机IP
     * 输入参数: none
     * 函数返回值: 成功返回string
     * 其它说明: 说明
     */
    private function getIP()
    {
        $ip = getenv('REMOTE_ADDR');
        $ip_ = getenv('HTTP_X_FORWARDED_FOR');
        if (($ip_ != "") && ($ip_ != "unknown")) {
            $ip = $ip_;
        }
        $ip = explode(",", $ip);
        return $ip[0];
    }

    public function getdata($array, $params)
    {
        $data = array();
        foreach ($array as $a) {
            $data[] = $a . "='" . $params[$a] . "'";
        }
        $data = implode(",", $data);
        return $data;
    }

    private function pri_arrayRecursive(&$array, $function, $apply_to_keys_also = false)
    {
        static $recursive_counter = 0;
        if (++$recursive_counter > 1000) {
            die('possible deep recursion attack');
        }
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $this->pri_arrayRecursive($array[$key], $function, $apply_to_keys_also);
            } else {
                $array[$key] = $function($value);
                //$array[$key] = $function(addslashes($value));
            }
            if ($apply_to_keys_also && is_string($key)) {
                $new_key = $function($key);
                if ($new_key != $key) {
                    $array[$new_key] = $array[$key];
                    unset($array[$key]);
                }
            }
        }
        $recursive_counter--;
    }


    private function pri_JSON($array)
    {
        //处理空数组
//        $array = emptyArrayToNullStr($array);
        $this->pri_arrayRecursive($array, 'urlencode', true);
        $json = json_encode($array);
        //return iconv("GB2312","UTF-8",urldecode($json));
        return urldecode($json);
    }

    public function SoftInstall($params)
    {
//print"<pre>";print_r($this->stedao);exit;
        $UserName = $params['UserName'];
        $PassWord = $params['PassWord'];
        $LoginDate = $params['LoginDate'];
        $SignCS = $params['SignCS'];
        $LicenseKey = $params['LicenseKey'];
        $LicenseKeyD = $params['LicenseKeyD'];
        $SystemType = $params['SystemType'];
        $SystemTypeString = $params['SystemTypeString'];
        if (!empty($params['mc_type'])) $mc_type = $params['mc_type'];
        else $mc_type = 0;
        if (!empty($params['isEnglish'])) $isEnglish = $params['isEnglish'];
        else $isEnglish = 0;
        // $isEnglish = $this->_dao->getOne("SELECT isEnglish FROM app_license WHERE LicenseKeyMain='".$LicenseKey."' and Status=1 and mc_type='".$mc_type."'");


        $LoginIp = $this->getIP();
        $SystemVersion = '';

        $arr = array(
            'Success' => '0',
            'Message' => base64_encode('初始化失败'),
        );
        if ($isEnglish) $arr['Message'] = base64_encode('Initialization Failed!');

        $arrtem = $params;
        //array_pop($arrtem);
        foreach ($arrtem as $k => $v) {
            $actionstr .= "&" . $k . "=" . $v;
        }

        foreach ($GLOBALS["testaccountarr"] as $na => $pa) {
            if ($UserName == $na && $PassWord == $pa) {
                $arr['Success'] = '1';
                $arr['Message'] = base64_encode('初始化成功');
                if ($isEnglish) $arr['Message'] = base64_encode('Success');
                $json_string = $this->pri_JSON($arr);
                echo $json_string;
                exit;
            }
        }
        $user = $this->stedao->getRow("select * from adminuser where username='" . $UserName . "' and passwordmd5='" . md5($PassWord) . "' ");

        if (empty($user)) {
            $arr['Success'] = '0';
            $arr['Message'] = base64_encode($this->utf8ToGb2312('用户名或密码错误[1001]'));
            if ($isEnglish) $arr['Message'] = base64_encode('Wrong Login ID or Password[1001]');
        }

        if (!empty($user) && !empty($LicenseKey)) {

            $sign = $this->_dao->getRow("SELECT * FROM app_license WHERE LicenseKeyMain='" . $LicenseKey . "' and Status=1 and mc_type='" . $mc_type . "'");

            if (!empty($sign) && $sign['MID'] == $user['mid']) {
                if (!empty($LicenseKeyD)) {
                    //echo "SELECT SignCS FROM app_license_detail WHERE LID='".$sign['ID']."' and LicenseKeyD='".$LicenseKeyD."' and mc_type='".$mc_type."' ";
                    //$sign2 = $this->_dao->getRow("SELECT SignCS FROM app_license_detail WHERE LID='".$sign['ID']."' and LicenseKeyD='".$LicenseKeyD."' and mc_type='".$mc_type."' ");
                    $sign2 = $this->_dao->getRow("SELECT SignCS,UseUser,ForceInit,ForceInitQuota,ForceInitQuotaUsed FROM app_license_detail WHERE LID='" . $sign['ID'] . "' and LicenseKeyD='" . $LicenseKeyD . "' and mc_type='" . $mc_type . "' ");
                    $ForceInit = $sign2['ForceInit'];
                    $ForceInitQuota = $sign2['ForceInitQuota'];
                    $ForceInitQuotaUsed = $sign2['ForceInitQuotaUsed'];
                    if ($sign2['UseUser'] != "") {
                        $sign2 = $this->_dao->getRow("SELECT SignCS FROM app_license_detail WHERE LID='" . $sign['ID'] . "' and UseUser='" . $UserName . "' and LicenseKeyD='" . $LicenseKeyD . "' and mc_type='" . $mc_type . "' ");
                    }


                    //print_r($sign2);echo "<br>1 ".($SignCS)." 1<br>";exit;

                    $initsigncs = $this->_dao->getone("select SignCS from app_license_detail where Status=1 and SignCS='$SignCS' and mc_type='" . $mc_type . "'");
                    //echo  $initsigncs;
                    if ($initsigncs && $initsigncs != $SignCS && $ForceInit != 1) {
                        $arr['Success'] = '0';
                        $arr['Message'] = base64_encode('设备已被注册使用，无法重复初始化[1004]');
                        if ($isEnglish) $arr['Message'] = base64_encode('The device has been registered and cannot be repeated initialization[1004]');
                    } elseif (($sign2 != "" && (empty($sign2['SignCS']))) || $sign2['SignCS'] == $SignCS || ($ForceInit == 1 && $ForceInitQuotaUsed < $ForceInitQuota)) {
                        $sysstr = iconv('GB2312', 'UTF-8', $SystemTypeString);
                        if ($ForceInit == 1 && !empty($sign2['SignCS']) && $sign2['SignCS'] != $SignCS) {
                            $Used = $ForceInitQuotaUsed + 1;
                            $this->_dao->execute("UPDATE app_license_detail SET ForceInitQuotaUsed='" . $Used . "',SignCS='" . $SignCS . "',SystemTypeString='" . $sysstr . "',UserDate=NOW(),UseUser = '" . $UserName . "' WHERE LID='" . $sign['ID'] . "' and LicenseKeyD='" . $LicenseKeyD . "' and mc_type='" . $mc_type . "' ");

                        } else {

                            $this->_dao->execute("UPDATE app_license_detail SET SignCS='" . $SignCS . "',SystemTypeString='" . $sysstr . "',UserDate=NOW(),UseUser = '" . $UserName . "' WHERE LID='" . $sign['ID'] . "' and LicenseKeyD='" . $LicenseKeyD . "' and mc_type='" . $mc_type . "' ");

                        }

                        $arr['Success'] = '1';
                        $arr['Message'] = base64_encode('初始化成功');
                        if ($isEnglish) $arr['Message'] = base64_encode('Success');
                        //日志处理
                        $Uid = $user['Uid'];
                        $Mid = $user['Mid'];
                        $this->_dao->WriteLog($Mid, $Uid, $SignCS, "SoftInstall", $actionstr, $LoginIp, $SystemType, $SystemVersion, "初始化", '', '', $mc_type);

                        if ($mc_type == 2) {
                            $SGLoginURL = SGDCURL . '/web/systemapi.php?action=gzjSysLogin';
                            $logininfo['username'] = 'testuser';
                            $logininfo['pwd'] = '123456';
                            $logininfo['mc_type'] = '2';
                            //print_r($logininfo);
                            $mess = $this->http_post($SGLoginURL, http_build_query($logininfo));
                            //print_r($mess);
                            $content = $mess['content'];
                            //print_r($content);
                            $token = json_decode($this->clearBom($content), true);

                            //print_r($token);
                            $token = $token['Token'];
                            //echo $token;
                            $app_license_detail_id = $this->_dao->getone("select ID from app_license_detail where LID='" . $sign['ID'] . "' and LicenseKeyD='" . $LicenseKeyD . "' and mc_type='" . $mc_type . "'");
                            $SGURL = SGDCURL . '/web/systemapi.php?action=syncdb&mc_type=2';

                            $tbinfo['tablename'] = 'app_license_detail';
                            $tbinfo['type'] = 'update';
                            $tbinfo['pk'] = 'ID';
                            $tbinfo['data'][] = array(
                                'ID' => $app_license_detail_id,
                                'SignCS' => $SignCS,
                                'SystemTypeString' => $SystemTypeString,
                                'UserDate' => date("Y-m-d H:i:s"),
                                'UseUser' => $this->strconvert($UserName)
                            );
                            $tbinfo1[] = $tbinfo;
                            $tjinfo['dtjson'] = json_encode($tbinfo1);
                            $tjinfo['token'] = $token;
                            $mess = $this->http_post($SGURL, urldecode(http_build_query($tjinfo)));

						}
						else if($mc_type==3 || $mc_type==4){//同步至新钢服务器调用47号接口


                            $XGLoginURL = XGDCURL . '/api/gzjSysLogin';
                            $logininfo['username'] = 'testuser';
                            $logininfo['pwd'] = '123456';
							$logininfo['mc_type']=$mc_type;
                            //print_r($logininfo);
                            $mess = $this->http_post($XGLoginURL, http_build_query($logininfo));
                            //print_r($mess);
                            $content = $mess['content'];
                            //print_r($content);
                            $token = json_decode($this->clearBom($content), true);

                            //print_r($token);
                            $token = $token['Token'];
                            //echo $token;
                            $app_license_detail_id = $this->_dao->getone("select ID from app_license_detail where LID='" . $sign['ID'] . "' and LicenseKeyD='" . $LicenseKeyD . "' and mc_type='" . $mc_type . "'");
                            $XGURL = XGDCURL . '/api/syncdb';

                            $tbinfo['tablename'] = 'app_license_detail';
                            $tbinfo['type'] = 'update';
                            $tbinfo['pk'] = 'ID';
                            $tbinfo['data'][] = array(
                                'ID' => $app_license_detail_id,
                                'SignCS' => $SignCS,
                                'SystemTypeString' => $SystemTypeString,
                                'UserDate' => date("Y-m-d H:i:s"),
                                'UseUser' => $this->strconvert($UserName)
                            );
                            //print_r(json_encode($tbinfo));
                            $tbinfo1[] = $tbinfo;
                            $tjinfo['dtjson'] = json_encode($tbinfo1);
                            $tjinfo['token'] = $token;
                            //echo urldecode(http_build_query($tjinfo));
                            //echo $XGURL;
                            $mess = $this->http_post($XGURL, urldecode(http_build_query($tjinfo)));
                            //print_r($mess);
                        }

                    } else if ($ForceInit == 1 && $ForceInitQuotaUsed >= $ForceInitQuota) {
                        $arr['Success'] = '0';
                        $arr['Message'] = base64_encode('强制登录额度不足[1005]');
                        if ($isEnglish) $arr['Message'] = base64_encode('Not Enough Forced Sign-ins[1005]');
                    } else {
                        $arr['Success'] = '0';
                        $arr['Message'] = base64_encode('子序列号出错[1003]');
                        if ($isEnglish) $arr['Message'] = base64_encode('Wrong Sub-Serial Number[1003]');
                    }
                }
            } else {
                if (empty($sign)) $mes = '主序列号出错[1002]';
                else $mes = '主序列号与初始化账号不匹配[1002]';
                $arr['Success'] = '0';
                $arr['Message'] = base64_encode($mes);
                if ($isEnglish) $arr['Message'] = base64_encode('Initialization Failed[1002]');
            }
        }

        $json_string = $this->pri_JSON($arr);
        echo $json_string;

    }

//xiangbin add 20201204 start

    public function clearBom($str)
    {
        $bom = chr(239) . chr(187) . chr(191);
        return str_replace($bom, '', $str);
    }

    /**
     * POST 请求
     * @param string $url
     * @param array $param
     * @param boolean $post_file 是否文件上传
     * @return string content
     */
    function http_post($url, $param, $post_file = false)
    {
        $oCurl = curl_init();

        if (stripos($url, "https://") !== FALSE) {
            curl_setopt($oCurl, CURLOPT_SSL_VERIFYPEER, FALSE);
            curl_setopt($oCurl, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($oCurl, CURLOPT_SSLVERSION, 1); //CURL_SSLVERSION_TLSv1
        }
        if (PHP_VERSION_ID >= 50500 && class_exists('\CURLFile')) {
            $is_curlFile = true;
        } else {
            $is_curlFile = false;
            if (defined('CURLOPT_SAFE_UPLOAD')) {
                curl_setopt($oCurl, CURLOPT_SAFE_UPLOAD, false);
            }
        }

        if ($post_file) {
            if ($is_curlFile) {
                foreach ($param as $key => $val) {
                    if (isset($val["tmp_name"])) {
                        $param[$key] = new \CURLFile(realpath($val["tmp_name"]), $val["type"], $val["name"]);
                    } else if (substr($val, 0, 1) == '@') {
                        $param[$key] = new \CURLFile(realpath(substr($val, 1)));
                    }
                }
            }
            $strPOST = $param;
        } else {
            //$strPOST = json_encode($param);
            $strPOST = $param;
        }

        curl_setopt($oCurl, CURLOPT_URL, $url);
        curl_setopt($oCurl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($oCurl, CURLOPT_POST, true);
        curl_setopt($oCurl, CURLOPT_POSTFIELDS, $strPOST);
        curl_setopt($oCurl, CURLOPT_VERBOSE, 1);
        curl_setopt($oCurl, CURLOPT_HEADER, 1);

        // $sContent = curl_exec($oCurl);
        // $aStatus  = curl_getinfo($oCurl);

        $sContent = $this->execCURL($oCurl);

        curl_close($oCurl);

        return $sContent;
    }

    /**
     * 执行CURL请求，并封装返回对象
     */
    function execCURL($ch)
    {
        $response = curl_exec($ch);
        $error = curl_error($ch);
        $result = array('header' => '',
            'content' => '',
            'curl_error' => '',
            'http_code' => '',
            'last_url' => '');

        if ($error != "") {
            $result['curl_error'] = $error;
            return $result;
        }

        $header_size = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
        $result['header'] = str_replace(array("\r\n", "\r", "\n"), "<br/>", substr($response, 0, $header_size));
        $result['content'] = substr($response, $header_size);
        $result['http_code'] = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $result['last_url'] = curl_getinfo($ch, CURLINFO_EFFECTIVE_URL);
        $result["base_resp"] = array();
        $result["base_resp"]["ret"] = $result['http_code'] == 200 ? 0 : $result['http_code'];
        $result["base_resp"]["err_msg"] = $result['http_code'] == 200 ? "ok" : $result["curl_error"];

        return $result;
    }


//xiangbin add 20201204 end


    public function Login($params)
    {
        if ($params['debug'] == 1) {
            print"<pre>";
            print_r($params);
        }
        //exit;
        $LoginType = $params['LoginType'];
        $UserName = $params['UserName'];
        $PassWord = str_replace(" ","+",$params['PassWord']);
        $LoginDate = $params['LoginDate'];
        $SignCS = $params['SignCS'];
        $LicenseSign = $params['LicenseSign'];
        $SystemType = $params['SystemType'];
        $SystemTypeString = $params['SystemTypeString'];
        $SystemVersion = $params['SystemVersion'];
        $quanxian = $params['quanxian'];
        $IsEncrypt = $params['IsEncrypt'];

        $LoginIp = $this->getIP();
        $ip = $LoginIp;
        if (empty($params['mc_type'])) $mc_type = 0;
        else $mc_type = $params['mc_type'];

        if (empty($params['isEnglish'])) $isEnglish = 0;
        else $isEnglish = $params['isEnglish'];
        //add by xr 是否英文根据条件查询，不用参数进行定义了
        // $isEnglish = $this->_dao->getOne("SELECT IsEnglish FROM app_license_detail WHERE SignCS ='".$SignCS."' and Status=1 and (UseUser = '".$UserName."' or UseUser = '') and mc_type='$mc_type'");
        // echo "SELECT isEnglish FROM app_license_detail WHERE SignCS ='".$SignCS."' and Status=1 and (UseUser = '".$UserName."' or UseUser = '') and mc_type='$mc_type'";
        //end
        if($mc_type==3){
            if($UserName=='xgjzc263'){
                $arr['Success'] = '0';
                $arr['ErrorType'] = '1001';
                $arr['Message'] = base64_encode($this->utf8ToGb2312('用户名或密码错误[1001]'));
                if ($isEnglish) $arr['Message'] = base64_encode('Wrong Login ID or Password[1001]');
                $json_string = $this->pri_JSON($arr);
                echo $json_string;
                exit;
            }
        }
        $arrtem = $params;
        //array_pop($arrtem);

        $actionstr = "";
        foreach ($arrtem as $k => $v) {
            $actionstr .= "&" . $k . "=" . $v;
        }

        //add by xr 新增初始化合并
        if ($IsEncrypt == 1) {
            $memcache = new Memcache;
            $memcache->connect(MEMCACHE_SERVER, MEMCACHE_PORT);
            $AesKey = $memcache->get($SignCS . $UserName);
            $memcache->close();
            // $PassWord
//  echo $SignCS.$UserName;exit;
            /**
             *  加密
             */
            //    $AesKey='55BPehZTgcqJlKHy';
            // define('SECRETKEY', $AesKey);
            //  echo $AesKey;exit;
            // echo 'AesKey：'.$AesKey.'<br>';
            // $string =$this->encrypt('cxy2019');
            // print_r($string);
            // echo '<hr>';
            /**
             *  解密
             */
//			 echo 'AesKey：'.$AesKey.'<br>';
//			 echo $SignCS.$UserName.'----'.$AesKey;
            $PassWord = $this->decrypt($PassWord, $AesKey);
            if (empty($PassWord)) {
                $arr['Success'] = '0';
                $arr['ErrorType'] = '1001';
                $arr['Message'] = base64_encode($this->utf8ToGb2312('用户名或密码错误[1001]'));
                if ($isEnglish) $arr['Message'] = base64_encode('Wrong Login ID or Password[1001]');
                $json_string = $this->pri_JSON($arr);
                echo $json_string;
                exit;
            }

        }


        //end


        //$this->_dao->WriteLog($Mid, $Uid, $SignCS, "Login",$actionstr, $LoginIp, $SystemType, $SystemVersion,"登录",'','',$mc_type);

        //$LoginIp=explode(",",$LoginIp);
        //$LoginIp=trim($LoginIp[0]);

        //add by zhangcun 2017/12/15 for 测试账号 无条件登录
        //print"<pre>";print_r($GLOBALS["testaccountarr"]);exit;
        function split2($n)
        {
            $n |= 0;
            //echo $n>>1;
            $pad = 0;
            $arr = array();
            while ($n) {
                if ($n & 1) array_push($arr, 1 << $pad);
                //echo $n;
                $pad++;
                $n >>= 1;
            }
            return $arr;
        }


        foreach ($GLOBALS["testaccountarr"] as $na => $pa) {
            if ($UserName == $na && $PassWord == $pa) {
                if (!(in_array($ip, $GLOBALS["testip"]) || $GLOBALS["nowtest"] == 1)) {
                    $arr = array(
                        'Success' => '0',
                        'ErrorType' => '-1',
                        'Message' => base64_encode('非法登录'),
                    );
                    $json_string = $this->pri_JSON($arr);
                    echo $json_string;
                    exit;
                }
                //测试账号记录
                $this->_dao->WriteLog('1', '1', $SignCS, "Login", $actionstr, $LoginIp, $SystemType, $SystemVersion, "登录", '', '', $mc_type);

                // SELECT * FROM `app_license_popedom_item` WHERE `typeid` =1 AND itemname IN ('铁前成本', '钢轧成本', '利润汇总', '销售定价', '绩效效益预测', '研究报告', '原料性价比', '期货套保') ORDER BY FIELD( itemname, '铁前成本', '钢轧成本', '利润汇总', '销售定价', '绩效效益预测', '研究报告', '原料性价比', '期货套保' )

                // $res=$this->_dao->query("SELECT popid  FROM `app_license_popedom` WHERE `uid` =452459 AND popid IN ( 35, 40, 41,2, 8, 9, 7, 45 ) GROUP BY popid ORDER BY FIELD( popid,  35, 40, 41,2, 8, 9, 7, 45)");
                if ($mc_type == 3 && $SystemType != 'NewPC') {
                    // 铁前成本 C 钢轧成本 F 利润汇总 G 销售定价 N  管理看板 K  数据录入Q    绩效效益预测  J 研究报告 L 原料性价比 K 期货套保 O  接单效益 P 经营分析  R 管理看板查看 S
                    $xgquanxian = "C|F|G|N|J|O|P|K|Q|R|S";
                } elseif ($mc_type == 3 && $SystemType == 'NewPC') {
                    $xgquanxian = "C|F|G|J|N|P|K|Q|O|R|S";
                }else if($mc_type==4&&$SystemType=='NewPC'){
                    $xgquanxian="L|C|F|G|K|J|O|M|P|R|S|T|N|U";
                }else{
                    $xgquanxian = "";
                }

                if ($mc_type == 2)
                    $dpshow = 1;
                else
                    $dpshow = "";


                if ($quanxian == 1) {
                    //获取品种

                    //取定制id，testaccount账号特殊处理，不是testaccount的账号采用下面注释的方法
                    // $SignCS_arr = $this->_dao->getRow("select * from app_license_detail where SignCS ='".$SignCS."' and LicenseKeyD = '".$LicenseSign."' and mc_type='".$mc_type."' and Status = 1 ");

                    // $lid_arr=$this->_dao->query("select dc_code_datatypeid from app_license_dzprivilege where lid ='".$SignCS_arr['LID']."' and mc_type='".$mc_type."'");
                    // $dzqxstr='';
                    // foreach ($lid_arr as $k_lid => $k_v) {
                    //     if( $dzqxstr==''){
                    // 		$dzqxstr=$k_v['dc_code_datatypeid'];

                    // 	}else{
                    // 		$dzqxstr=$dzqxstr.','.$k_v['dc_code_datatypeid'];
                    // 	}
                    // }


                    if ($params['dzquanxian'] == 1) {
                        $alldz = $this->_dao->query("select ID,pinzhong  from dc_code_datatype where scode2='B25' and mc_type='$mc_type'");

                        $dzqxstr = '';

                        foreach ($alldz as $k_lid => $k_v) {
                            $dzpz = '';
                            $dzpz = split2($k_v['pinzhong']);

                            foreach ($dzpz as $dzk => $dzv) {
                                if ($dzv == '8388608') {//匹配定制权限，匹配到的拼接dc_code_datatype 的id

                                    if ($dzqxstr == '') {
                                        $dzqxstr = $k_v['ID'];

                                    } else {
                                        $dzqxstr = $dzqxstr . ',' . $k_v['ID'];
                                    }
                                }
                            }
                        }
                    }

                    $pz = $this->_dao->getOne("select pinzhong from app_license where MID = '1' and Status=1 and mc_type='" . $mc_type . "' limit 1 ", 0);
                    //echo $GUID;echo '<br>';
                    //print_R($GLOBALS["testaccountarr"]);

                    if ($pz == '8192' || (array_key_exists($params['UserName'], $GLOBALS["testaccountarr"]) && $SignCS != '')) {//echo 1111;
                        $pz = 'allpz';
                    } else {
                        foreach (split2($pz) as $k => $v) {
                            if ($k == 0) {
                                $pz = $v;
                            } else {
                                $pz = $pz . '|' . $v;
                            }
                        }

                    }
                    //echo $pz;
                    //orderlist：读取dc_custom_order表 根据Mid和mc_type查询
                    $orderlist = $this->_dao->query("select app_license.ID,app_license.MID,DataType,BuyType,Syear,Eyear from dc_custom_order inner join app_license on dc_custom_order.Mid = app_license.Mid and app_license.mc_type='$mc_type' and dc_custom_order.mc_type='$mc_type' where dc_custom_order.MID='1' AND app_license.StartDate<NOW() AND app_license.EndDate>now()");

                    $isyx = $this->_dao->getRow("select * from app_license where MID = '1' AND app_license.StartDate<NOW() AND app_license.EndDate>now() ");

                    if ($isyx) {

                        $ids = $this->_dao->getOnes("select ID from dc_code_class where mcode='' and mc_type='$mc_type'");
                        foreach ($ids as $key => $val) {
                            $one = array();
                            $one['ID'] = 0;
                            $one['MID'] = 1;
                            $one['DataType'] = $val;
                            $one['BuyType'] = 2;
                            $one['Syear'] = '2014';
                            $one['Eyear'] = '2019';
                            $orderlist[] = $one;
                        }
                    }

                    $idstr = "";
                    foreach ($orderlist as $key => $val) {
                        $idstr .= $val['DataType'] . ",";
                    }
                    $idstr = rtrim($idstr, ",");
                    //echo '<pre>';print_r($orderlist);
                    if ($idstr)
                        $scodelist = $this->_dao->query("select id,scode from dc_code_class where ID in(" . $idstr . ") and mc_type='$mc_type';");
                    //echo '<pre>';print_r($scodelist);

                    foreach ($scodelist as $key => $val) {
                        $scodelist2[$val['id']] = $val['scode'];
                    }

                    foreach ($orderlist as $key => $val) {
                        $scode = $scodelist2[$val['DataType']];
                        $orderlist[$key]['scode'] = $scode;
                        $orderlist[$key]['level'] = $this->getcurrentCengshu2($scode);
                    }


                    //去除重复数据，id=0的去除
                    foreach ($orderlist as $key1 => $val1) {
                        foreach ($orderlist as $key => $val) {
                            if ($val['DataType'] == $val1['DataType'] && $val['ID'] != $val1['ID']) {
                                if ($val['ID'] == '0') {
                                    unset($orderlist[$key]);
                                }
                                if ($val1['ID'] == '0') {
                                    unset($orderlist[$key1]);
                                }
                            }

                        }
                    }
                    if ($params['dzquanxian'] == 1) {//定制权限字段存在返回，不存在不返回
                        $arr = array(
                            'Success' => '1',
                            'GUID' => $UserName,
                            'Message' => base64_encode($this->utf8ToGb2312( '登录成功' ) ),
                            'InterFaceUrl' => '',
                            'Result' => array(
                                'Uid' => $GLOBALS["testaccountuser"]["Uid"],
                                'UserName' => $UserName,
                                'TrueName' => base64_encode($this->utf8ToGb2312( "测试账号_" . $UserName ) ),
                                'MobileNumber' => "",
                                'Mid' => $GLOBALS["testaccountuser"]["Mid"],
                                'ComName' => '',
                                'UserLevel' => '',
                                'ExpiredDate' => '2040-01-01',
                                'SmsExpiredDate' => '2040-01-01',
                                'AdminName' => '',
                                'AdminPhone' => '',
                                'AdminMobile' => '',
                                'IsUpdate' => 1,
                                'SQLitePass' => $this->_dao->getone("select SQLitePass from app_license where mc_type='$mc_type' and mid=1 limit 1"),
                                'dzquanxian' => $dzqxstr,
                                'pinzhong' => $pz,
                                'orderlist' => array_values($orderlist),
                                'xgquanxian' => $xgquanxian,
                                'dpshow' => $dpshow,
                                'cronconfig' => '',
                            )
                        );
                    } else {
                        $arr = array(
                            'Success' => '1',
                            'GUID' => $UserName,
                            'Message' => base64_encode($this->utf8ToGb2312( '登录成功' ) ),
                            'InterFaceUrl' => '',
                            'Result' => array(
                                'Uid' => $GLOBALS["testaccountuser"]["Uid"],
                                'UserName' => $UserName,
                                'TrueName' => base64_encode($this->utf8ToGb2312( "测试账号_" . $UserName ) ),
                                'MobileNumber' => "",
                                'Mid' => $GLOBALS["testaccountuser"]["Mid"],
                                'ComName' => '',
                                'UserLevel' => '',
                                'ExpiredDate' => '2040-01-01',
                                'SmsExpiredDate' => '2040-01-01',
                                'AdminName' => '',
                                'AdminPhone' => '',
                                'AdminMobile' => '',
                                'IsUpdate' => 1,
                                'SQLitePass' => $this->_dao->getone("select SQLitePass from app_license where mc_type='$mc_type' and mid=1 limit 1"),

                                'pinzhong' => $pz,
                                'orderlist' => array_values($orderlist),
                                'xgquanxian' => $xgquanxian,
                                'dpshow' => $dpshow,
                                'cronconfig' => '',
                            )
                        );
                    }


                } else {
                    $arr = array(
                        'Success' => '1',
                        'GUID' => $UserName,
                        'Message' => base64_encode($this->utf8ToGb2312( '登录成功' ) ),
                        'InterFaceUrl' => '',
                        'Result' => array(
                            'Uid' => $GLOBALS["testaccountuser"]["Uid"],
                            'UserName' => $UserName,
                            'TrueName' => base64_encode( $this->utf8ToGb2312( "测试账号_" . $UserName ) ),
                            'MobileNumber' => "",
                            'Mid' => $GLOBALS["testaccountuser"]["Mid"],
                            'ComName' => '',
                            'UserLevel' => '',
                            'ExpiredDate' => '2040-01-01',
                            'SmsExpiredDate' => '2040-01-01',
                            'AdminName' => '',
                            'AdminPhone' => '',
                            'AdminMobile' => '',
                            'IsUpdate' => 1,
                            'SQLitePass' => $this->_dao->getone("select SQLitePass from app_license where mc_type='$mc_type' and mid=1 limit 1"),

                        )
                    );
                }


                //登录成功,session更新
                $this->_dao->execute("REPLACE INTO app_session_temp SET GUID='$UserName', SignCS='$SignCS', Mid='" . $GLOBALS["testaccountuser"]["Mid"] . "', Uid='" . $GLOBALS["testaccountuser"]["Uid"] . "', ComName='', UserName='$UserName',TrueName='" . "测试账号_" . $UserName . "', MobileNumber='', ExpiredDate='2040-01-01', LoginDate=NOW(), LoginIp='$LoginIp', SystemType='$SystemType',SystemVersion='$date', LastDate=NOW(), LastAction='Login',mc_type='$mc_type'");
                //登录成功推送
                if ($mc_type == 3) {//新钢推送

                    $deviceType = '';
                    if ($SystemType == 'NewAndroidPhone') {
                        $deviceType = '17';
                    }
                    if ($SystemType == 'NewAndroidPad') {
                        $deviceType = '18';
                    }
                    if ($SystemType == 'NewiPad') {
                        $deviceType = '20';
                    }
                    if ($SystemType == 'NewiPhone') {
                        $deviceType = '19';
                    }

                    // $deviceType=17;
                    // $params['token']='0c9f065906347fde11d511468fa85849393c';
                    // echo $GLOBALS["testaccountuser"]["Uid"];
                    $ret = file_get_contents(STEELHOME_URL . "cron_97_system/xingePush/xingePush.php?deviceType=" . $deviceType . "&pushType=bind&tags=" . $GLOBALS["testaccountuser"]["Uid"] . "&token=" . $params['Token']);
                    $ret = json_decode($ret, true);

                }


                if ($mc_type == 2)//陕钢
                {

                    $SGLoginURL = SGDCURL . '/web/systemapi.php?action=gzjSysLogin';
                   
                    $logininfo['username'] = 'testuser';
                    $logininfo['pwd'] = '123456';
                    $logininfo['mc_type'] = '2';
                    //print_r($logininfo);
                    $mess = $this->http_post($SGLoginURL, http_build_query($logininfo));
                    //print_r($mess);
                    $content = $mess['content'];
                    //print_r($content);
                    $token = json_decode($this->clearBom($content), true);

                    //print_r($token);
                    $token = $token['Token'];
                    //echo $token;
                    //$app_license_detail_id=$this->_dao->getone("select ID from app_license_detail where LID='".$sign['ID']."' and LicenseKeyD='".$LicenseKeyD."' and mc_type='".$mc_type."'");
                    $SGURL = SGDCURL . '/web/systemapi.php?action=syncdb&mc_type=2';

                    $tbinfo['tablename'] = 'app_session_temp';
                    $tbinfo['type'] = 'replace';
                    $tbinfo['pk'] = 'GUID';
                    $tbinfo['data'][] = array(
                        'GUID' => $UserName,
                        'SignCS' => $SignCS,
                        'Mid' => $GLOBALS["testaccountuser"]["Mid"],
                        'Uid' => $GLOBALS["testaccountuser"]["Uid"],
                        'ComName' => '',
                        'UserName' => $this->strconvert($UserName),
                        'TrueName' => $this->strconvert("测试账号_" . $UserName),
                        'MobileNumber' => '',
                        'ExpiredDate' => '2040-01-01',
                        'LoginDate' => date("Y-m-d H:i:s"),
                        'LoginIp' => $LoginIp,
                        'SystemType' => $SystemType,
                        'SystemVersion' => $date,
                        'LastDate' => date("Y-m-d H:i:s"),
                        'LastAction' => 'Login',
                        'mc_type' => $mc_type,
                        'isEnglish' => $isEnglish
                    );
                    $tbinfo1[] = $tbinfo;
                    $tjinfo['dtjson'] = json_encode($tbinfo1);
                    $tjinfo['token'] = $token;

                    $mess = $this->http_post($SGURL, urldecode(http_build_query($tjinfo)));

                } //xiangbin add 20201207 strat
					else if($mc_type==3 || $mc_type==4)//新钢
                {

                    $XGLoginURL = XGDCURL . '/api/gzjSysLogin';
                    $logininfo['username'] = 'testuser';
                    $logininfo['pwd'] = '123456';
						$logininfo['mc_type']=$mc_type;
                    //print_r($logininfo);
                    $mess = $this->http_post($XGLoginURL, http_build_query($logininfo));
                    //print_r($mess);
                    $content = $mess['content'];
                    //print_r($content);
                    $token = json_decode($this->clearBom($content), true);

                    //print_r($token);
                    $token = $token['Token'];
                    //echo $token;
                    //$app_license_detail_id=$this->_dao->getone("select ID from app_license_detail where LID='".$sign['ID']."' and LicenseKeyD='".$LicenseKeyD."' and mc_type='".$mc_type."'");
                    $XGURL = XGDCURL . '/api/syncdb';

                    $tbinfo['tablename'] = 'app_session_temp';
                    $tbinfo['type'] = 'replace';
                    $tbinfo['pk'] = 'GUID';
                    $tbinfo['data'][] = array(
                        'GUID' => $UserName,
                        'SignCS' => $SignCS,
                        'Mid' => $GLOBALS["testaccountuser"]["Mid"],
                        'Uid' => $GLOBALS["testaccountuser"]["Uid"],
                        'ComName' => '',
                        'UserName' => $this->strconvert($UserName),
                        'TrueName' => $this->strconvert("测试账号_" . $UserName),
                        'MobileNumber' => '',
                        'ExpiredDate' => '2040-01-01',
                        'LoginDate' => date("Y-m-d H:i:s"),
                        'LoginIp' => $LoginIp,
                        'SystemType' => $SystemType,
                        'SystemVersion' => $date,
                        'LastDate' => date("Y-m-d H:i:s"),
                        'LastAction' => 'Login',
                        'mc_type' => $mc_type,
                        'isEnglish' => $isEnglish
                    );
                    //print_r(json_encode($tbinfo));
                    $tbinfo1[] = $tbinfo;
                    $tjinfo['dtjson'] = json_encode($tbinfo1);
                    $tjinfo['token'] = $token;
                    //echo urldecode(http_build_query($tjinfo));
                    //echo $XGURL;
                    $mess = $this->http_post($XGURL, urldecode(http_build_query($tjinfo)));

                }
                //xiangbin add 20201207 end

                if ($isEnglish) $arr['Message'] = base64_encode('Success');
                $json_string = $this->pri_JSON($arr);
                echo $json_string;
                exit;
            }
        }
        //add by zhangcun 2017/12/15 for 测试账号 无条件登录
        $arr = array(
            'Success' => '0',
            'ErrorType' => '0',
            'Message' => base64_encode($this->utf8ToGb2312( '登录失败' ) ),
        );
        if ($isEnglish) $arr['Message'] = base64_encode('Login Failed!');

        $password_md5 = md5($PassWord);
        if ($params['debug'] == 1 && $params['passwordmd5'] != '') $password_md5 = $params['passwordmd5'];

		if($mc_type == 5){
			$logonflag = " IsClose = 0 ";
		}else if($mc_type == 4){
			$logonflag = " xglogonflag = 1";
		}else{
			$logonflag = " logonflag = 1";
		}
		$user = $this->stedao->getRow("select * from adminuser where username='".$UserName."' and passwordmd5='".$password_md5."' and state=1 and $logonflag");
        //print_r("select * from adminuser where username='".$UserName."' and passwordmd5='".md5($PassWord)."' ");
        //print_r($user);
        if (empty($user)) {
            $arr['Success'] = '0';
            $arr['ErrorType'] = '1001';
            $arr['Message'] = base64_encode($this->utf8ToGb2312('用户名或密码错误[1001]'));
            if ($isEnglish) $arr['Message'] = base64_encode('Wrong Login ID or Password[1001]');
        }

        //add by zfy started 2016/9/14
        if (!empty($user)) {
            $mid = $user['mid'];
            $endDaterow = $this->_dao->getRow("select EndDate,istry from app_license where MID='" . $mid . "' and mc_type='" . $mc_type . "' and status=1");
            if ($mc_type != 0 || $isEnglish != 0) {
                $endDate = $endDaterow['EndDate'];
                $endDate = strtotime($endDate." 23:59:59");
                $nowDate = time();
                if ($endDate < $nowDate) {
                    $arr2['Success'] = '0';
                    $arr2['Message'] = base64_encode($this->utf8ToGb2312( '账号使用时间到期[1008]' ) );
                    if ($isEnglish) $arr2['Message'] = base64_encode('Due Account[1008]');
                    $arr2['ErrorType'] = '1008';
                    $json_string = $this->pri_JSON($arr2);
                    echo $json_string;
                    exit;
                }
            } else//钢之家中文会员
            {
                if (empty($endDaterow) && $IsEncrypt == 1)//系统未查到试用数据中记录，创建
                {
                    $authnum = '';
                    $ychar = "0,1,2,3,4,5,6,7,8,9,a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z";
                    $list = explode(",", $ychar);
                    for ($i = 0; $i < 6; $i++) {
                        $randnum = rand(0, 61); // 10+26+26;
                        $authnum .= $list[$randnum];
                    }
                    $istry = 1;
                    $CompanyName = $this->stedao->getOne("select compfname from member where mbid = '" . $mid . "'  limit 1");
                    $LicenseKeyMain = $this->makelicense();
                    $LicenseNums = 1;
                    $StartDate = date('Y-m-d');
                    //$EndDate = '2040-01-01';
                    $EndDate = date("Y-m-d",strtotime("+1 month",strtotime($StartDate)));
                    $Remarks = '系统自动创建试用';
                    //$mc_type=0;
                    $ip = $this->getip();
                    $this->_dao->execute("insert into app_license set Mid='" . $mid . "',CompanyName='" . $CompanyName . "',LicenseKeyMain='" . $LicenseKeyMain . "',LicenseNums='" . $LicenseNums . "',StartDate='" . $StartDate . "',EndDate='" . $EndDate . "',Remarks='" . $Remarks . "',mc_type=0,pinzhong='4186111',IsEnglish='0',istry=1, CreateDate=NOW(),CreateUser='系统创建',CreateIp='" . $ip . "',Status =1,SQLitePass='" . $authnum . "'");
                    $lid = $this->_dao->getOne("SELECT LAST_INSERT_ID();", 0);
                    $license = $this->makelicense();
                    $this->_dao->execute("insert into app_license_detail set LID='" . $lid . "' ,IsEnglish='0',LicenseKeyD='" . $license . "', CreateDate=NOW(),CreateUser='系统创建',Status ='1' ,istry=1, mc_type='" . $mc_type . "'");

                    $Syear = date('Y');
                    $Eyear = $Syear + 1;
                    //目录权限

                    //正式
                    $sql = "INSERT INTO `dc_custom_order` (`MID`, `CompanyName`, `DataType`, `BuyType`, `Syear`, `Eyear`, `StartDate`, `EndDate`, `CreateUser`, `CreateDate`, `UpdateUser`, `UpdateDate`, `mc_type`) VALUES
					( $mid, '', 46, 2, $Syear, $Eyear, '0000-00-00', '0000-00-00', '系统创建', NOW(), '系统创建', '0000-00-00 00:00:00', 0),
					( $mid, '', 49, 2, $Syear, $Eyear, '0000-00-00', '0000-00-00', '系统创建', NOW(), '系统创建', '0000-00-00 00:00:00', 0),
					( $mid, '', 37, 2, $Syear, $Eyear, '0000-00-00', '0000-00-00', '系统创建', NOW(), '系统创建', '0000-00-00 00:00:00', 0),
					( $mid, '', 47, 2, $Syear, $Eyear, '0000-00-00', '0000-00-00', '系统创建', NOW(), '系统创建', '0000-00-00 00:00:00', 0),
					( $mid, '', 23, 2, $Syear, $Eyear, '0000-00-00', '0000-00-00', '系统创建', NOW(), '系统创建', '0000-00-00 00:00:00', 0),
					( $mid, '', 22, 2, $Syear, $Eyear, '0000-00-00', '0000-00-00', '系统创建', NOW(), '系统创建', '0000-00-00 00:00:00', 0),
					( $mid, '', 48, 2, $Syear, $Eyear, '0000-00-00', '0000-00-00', '系统创建', NOW(), '系统创建', '0000-00-00 00:00:00', 0),
					( $mid, '', 54, 2, $Syear, $Eyear, '0000-00-00', '0000-00-00', '系统创建', NOW(), '系统创建', '0000-00-00 00:00:00', 0),
					( $mid, '', 31, 2, $Syear, $Eyear, '0000-00-00', '0000-00-00', '系统创建', NOW(), '系统创建', '0000-00-00 00:00:00', 0),
					( $mid, '', 4979, 2, $Syear, $Eyear, '0000-00-00', '0000-00-00', '系统创建', NOW(), '系统创建', '0000-00-00 00:00:00', 0),
					( $mid, '', 4997, 2, $Syear, $Eyear, '0000-00-00', '0000-00-00', '系统创建', NOW(), '系统创建', '0000-00-00 00:00:00', 0),
					( $mid, '', 5427, 2, $Syear, $Eyear, '0000-00-00', '0000-00-00', '系统创建', NOW(), '系统创建', '0000-00-00 00:00:00', 0),
					( $mid, '', 5428, 2, $Syear, $Eyear, '0000-00-00', '0000-00-00', '系统创建', NOW(), '系统创建', '0000-00-00 00:00:00', 0),
					( $mid, '', 5429, 2, $Syear, $Eyear, '0000-00-00', '0000-00-00', '系统创建', NOW(), '系统创建', '0000-00-00 00:00:00', 0);";

                    /*
					//c测试
					$sql="INSERT INTO `dc_custom_order` (`MID`, `CompanyName`, `DataType`, `BuyType`, `Syear`, `Eyear`, `StartDate`, `EndDate`, `CreateUser`, `CreateDate`, `UpdateUser`, `UpdateDate`, `mc_type`) VALUES
					( $mid, '', 46, 2, $Syear, $Eyear, '0000-00-00', '0000-00-00', '系统创建', NOW(), '系统创建', '0000-00-00 00:00:00', 0),
					( $mid, '', 49, 2, $Syear, $Eyear, '0000-00-00', '0000-00-00', '系统创建', NOW(), '系统创建', '0000-00-00 00:00:00', 0),
					( $mid, '', 37, 2, $Syear, $Eyear, '0000-00-00', '0000-00-00', '系统创建', NOW(), '系统创建', '0000-00-00 00:00:00', 0),
					( $mid, '', 47, 2, $Syear, $Eyear, '0000-00-00', '0000-00-00', '系统创建', NOW(), '系统创建', '0000-00-00 00:00:00', 0),
					( $mid, '', 23, 2, $Syear, $Eyear, '0000-00-00', '0000-00-00', '系统创建', NOW(), '系统创建', '0000-00-00 00:00:00', 0),
					( $mid, '', 22, 2, $Syear, $Eyear, '0000-00-00', '0000-00-00', '系统创建', NOW(), '系统创建', '0000-00-00 00:00:00', 0),
					( $mid, '', 48, 2, $Syear, $Eyear, '0000-00-00', '0000-00-00', '系统创建', NOW(), '系统创建', '0000-00-00 00:00:00', 0),
					( $mid, '', 54, 2, $Syear, $Eyear, '0000-00-00', '0000-00-00', '系统创建', NOW(), '系统创建', '0000-00-00 00:00:00', 0),
					( $mid, '', 31, 2, $Syear, $Eyear, '0000-00-00', '0000-00-00', '系统创建', NOW(), '系统创建', '0000-00-00 00:00:00', 0),
					( $mid, '', 7752, 2, $Syear, $Eyear, '0000-00-00', '0000-00-00', '系统创建', NOW(), '系统创建', '0000-00-00 00:00:00', 0),
					( $mid, '', 7760, 2, $Syear, $Eyear, '0000-00-00', '0000-00-00', '系统创建', NOW(), '系统创建', '0000-00-00 00:00:00', 0),
					( $mid, '', 8246, 2, $Syear, $Eyear, '0000-00-00', '0000-00-00', '系统创建', NOW(), '系统创建', '0000-00-00 00:00:00', 0),
					( $mid, '', 8247, 2, $Syear, $Eyear, '0000-00-00', '0000-00-00', '系统创建', NOW(), '系统创建', '0000-00-00 00:00:00', 0),
					( $mid, '', 8248, 2, $Syear, $Eyear, '0000-00-00', '0000-00-00', '系统创建', NOW(), '系统创建', '0000-00-00 00:00:00', 0);";
                                  	*/
                    $insert = $this->_dao->execute($sql);

                    $data1 = '添加了全品种';
                    $sql = "insert into app_buy_logs SET MID='" . $mid . "',OpName='购买品种',OpDesc='" . $data1 . "',OpDate=now(),OpIp='" . $ip . "',OpUser='系统创建',mc_type='" . $mc_type . "'";
                    $insert = $this->_dao->execute($sql);

                } else {
                    $istry = $endDaterow['istry'];
                    if ($istry == 0) {
                        $sql = "select istry from app_license_detail where mc_type='" . $mc_type . "' and UseUser='" . $UserName . "' and IsEnglish=0 and Status ='1'  order by UserDate desc ";

                        $app_license_detail = $this->_dao->getrow($sql);
                        if (!empty($app_license_detail)) {
                            if ($app_license_detail['istry'] != 1) {
                                $endDate = $endDaterow['EndDate'];
                                //$endDate = strtotime($endDate);
                                $endDate = strtotime($endDate." 23:59:59");
                                $nowDate = time();
                                if ($endDate < $nowDate) {
                                    $arr2['Success'] = '0';
                                    $arr2['Message'] = base64_encode($this->utf8ToGb2312('账号使用时间到期[1008]'));
                                    if ($isEnglish) $arr2['Message'] = base64_encode('Due Account[1008]');
                                    $arr2['ErrorType'] = '1008';
                                    $json_string = $this->pri_JSON($arr2);
                                    echo $json_string;
                                    exit;
                                }
                            } else {
                                $istry = 1;
                            }
                        } else//账号不存在
                        {
                            $istry = 1;
                        }


                    }
                }
            }

        }

        //add by zfy ended 2016/9/14
        if (!empty($user)) {
            //add by xr   初始化合并

            //if ($isEnglish == 0 && $IsEncrypt == 1) 
            if ( $IsEncrypt == 1){//中文初始化
                $Soft_sign = $this->_dao->getRow("SELECT * FROM app_license WHERE MID='" . $mid . "' and Status=1 and mc_type='" . $mc_type . "'");

                $Soft_sign2 = $this->_dao->getRow("SELECT ID,SignCS,UseUser,ForceInit,ForceInitQuota,ForceInitQuotaUsed FROM app_license_detail WHERE LID='" . $Soft_sign['ID'] . "' and UseUser='" . $UserName . "' and SignCS='" . $SignCS . "' and Status=1 and mc_type='" . $mc_type . "' ");
                $sysstr = iconv('GB2312', 'UTF-8', $SystemTypeString);
                $sysstr = $SystemTypeString;
                $is_tp = 0;

                if (empty($Soft_sign2)) {//没有绑定的子序列号，初始化绑定

                    //优先取一个已经分配设备绑定
                    $empty_id = $this->_dao->getone("SELECT ID FROM app_license_detail  WHERE UseUser='" . $UserName . "' and Status=1 and mc_type='" . $mc_type . "' and LID='" . $Soft_sign['ID'] . "' and SignCS=''  order by  id desc limit 1");

                    //有username的没有空设备，优先没有username的绑定
                    if (empty($empty_id)) {

                        $empty_id = $this->_dao->getone("SELECT ID FROM app_license_detail  WHERE UseUser='' and Status=1 and mc_type='" . $mc_type . "' and LID='" . $Soft_sign['ID'] . "' and SignCS='' order by  id desc limit 1 ");

                        if (empty($empty_id)) {
                            //没有空设备，修改一个有设备号的绑定
                            $empty_id = $this->_dao->getone("SELECT ID FROM app_license_detail  WHERE UseUser='" . $UserName . "' and Status=1 and LID='" . $Soft_sign['ID'] . "' and mc_type='" . $mc_type . "' and SignCS!='' order by  id desc limit 1 ");

                            if (!empty($empty_id)) {
                                //绑定成功
                                $is_tp = 1;
                                $this->_dao->execute("UPDATE app_license_detail set IsAutoBind=0 , SignCS='" . $SignCS . "',SystemTypeString='" . $sysstr . "',UserDate=NOW()  WHERE ID='" . $empty_id . "'");

                            } else {

                                if ($istry && $mc_type == "0") {
                                    //试用账号创建子序列号
                                    $license = $this->makelicense();
                                    $this->_dao->execute("insert into app_license_detail set LID='" . $Soft_sign['ID'] . "' ,IsEnglish='0',LicenseKeyD='" . $license . "',IsAutoBind=1,SignCS='" . $SignCS . "', UseUser='" . $UserName . "',SystemTypeString='" . $sysstr . "', CreateDate=NOW(),CreateUser='系统创建',UserDate=NOW(),Status ='1' ,istry=1, mc_type='" . $mc_type . "'");
                                    //更新子序列号数量
                                    $this->_dao->execute("update app_license set LicenseNums=LicenseNums+1 where ID='" . $Soft_sign['ID'] . "'");

                                } else {
                                    $arr2['Success'] = '0';
                                    $arr2['Message'] = base64_encode($this->utf8ToGb2312( '初始化设备数量配额不足，初始化失败[1012]' ));
                                    if ($isEnglish) $arr2['Message'] = base64_encode('Due Account[1012]');
                                    $arr2['ErrorType'] = '1012';
                                    $json_string = $this->pri_JSON($arr2);
                                    echo $json_string;
                                    exit;
                                }


                            }

                        } else {
                            $is_tp = 1;
                            //绑定成功
                            $this->_dao->execute("UPDATE app_license_detail set IsAutoBind=1,SignCS='" . $SignCS . "', UseUser='" . $UserName . "',SystemTypeString='" . $sysstr . "',UserDate=NOW()  WHERE ID='" . $empty_id . "'");
                        }
                    } else {
                        $is_tp = 1;
                        //绑定成功
                        $this->_dao->execute("UPDATE app_license_detail set IsAutoBind=1,SignCS='" . $SignCS . "',SystemTypeString='" . $sysstr . "',UserDate=NOW()  WHERE ID='" . $empty_id . "'");

                    }

                }

            }

            if ($mc_type == 2 && $is_tp == 1) {
                $SGLoginURL = SGDCURL . '/web/systemapi.php?action=gzjSysLogin';
                $logininfo['username'] = 'testuser';
                $logininfo['pwd'] = '123456';
                $logininfo['mc_type'] = '2';
                //print_r($logininfo);
                $mess = $this->http_post($SGLoginURL, http_build_query($logininfo));
                //print_r($mess);
                $content = $mess['content'];
                //print_r($content);
                $token = json_decode($this->clearBom($content), true);

                //print_r($token);
                $token = $token['Token'];
                //echo $token;
                $app_license_detail_id = $this->_dao->getone("select ID from app_license_detail where LID='" . $Soft_sign['ID'] . "' and UseUser='" . $UserName . "' and SignCS='" . $SignCS . "' and mc_type='" . $mc_type . "'");
                $SGURL = SGDCURL . '/web/systemapi.php?action=syncdb&mc_type=2';


                $tbinfo['tablename'] = 'app_license_detail';
                $tbinfo['type'] = 'update';
                $tbinfo['pk'] = 'ID';
                $tbinfo['data'][] = array(
                    'ID' => $app_license_detail_id,
                    'SignCS' => $SignCS,
                    'SystemTypeString' => $this->strconvert($SystemTypeString),
                    'UserDate' => date("Y-m-d H:i:s"),
                    'UseUser' => $this->strconvert($UserName)
                );
                $tbinfo1[] = $tbinfo;
                $tjinfo['dtjson'] = json_encode($tbinfo1);
                $tjinfo['token'] = $token;
                $mess = $this->http_post($SGURL, urldecode(http_build_query($tjinfo)));
                $tbinfo = array();
                $tbinfo1 = array();

                $tbinfo['tablename'] = 'adminuser';
                $tbinfo['type'] = 'replace';
                $tbinfo['pk'] = 'id';
                $tbinfo['data'][] = array(
                    'id' => $user['id'],
                    'truename' => $this->strconvert($user['truename']),
                    'passwordmd5' => $user['passwordmd5'],
                    'state' => $user['state'],
                    'mobil' => $user['mobil'],
                    'mid' => $user['mid'],
                    'username' => $this->strconvert($UserName)
                );
                $tbinfo1[] = $tbinfo;
                $tjinfo['dtjson'] = json_encode($tbinfo1);
                $tjinfo['token'] = $token;
                $mess = $this->http_post($SGURL, urldecode(http_build_query($tjinfo)));
                $tbinfo = array();
                $tbinfo1 = array();


			}
			else if(($mc_type==3 || $mc_type==4)&&$is_tp==1){//同步至新钢服务器调用47号接口


                $XGLoginURL = XGDCURL . '/api/gzjSysLogin';
                $logininfo['username'] = 'testuser';
                $logininfo['pwd'] = '123456';
				$logininfo['mc_type']=$mc_type;
                //print_r($logininfo);
                $mess = $this->http_post($XGLoginURL, http_build_query($logininfo));
                //print_r($mess);
                $content = $mess['content'];
                //print_r($content);
                $token = json_decode($this->clearBom($content), true);

                //print_r($token);
                $token = $token['Token'];
                //echo $token;
                $app_license_detail_id = $this->_dao->getone("select ID from app_license_detail where LID='" . $Soft_sign['ID'] . "' and UseUser='" . $UserName . "' and SignCS='" . $SignCS . "' and mc_type='" . $mc_type . "'");
                $XGURL = XGDCURL . '/api/syncdb';

                $tbinfo['tablename'] = 'app_license_detail';
                $tbinfo['type'] = 'update';
                $tbinfo['pk'] = 'ID';
                $tbinfo['data'][] = array(
                    'ID' => $app_license_detail_id,
                    'SignCS' => $SignCS,
                    'SystemTypeString' => $this->strconvert($SystemTypeString),
                    'UserDate' => date("Y-m-d H:i:s"),
                    'UseUser' => $this->strconvert($UserName)
                );
                //print_r(json_encode($tbinfo));
                $tbinfo1[] = $tbinfo;
                $tjinfo['dtjson'] = json_encode($tbinfo1);
                $tjinfo['token'] = $token;
                //echo urldecode(http_build_query($tjinfo));
                //echo $XGURL;
                $mess = $this->http_post($XGURL, urldecode(http_build_query($tjinfo)));
                //print_r($mess);
                $tbinfo = array();
                $tbinfo1 = array();
                //file_put_contents("/tmp/abc.txt",print_r($mess,true)."\n",FILE_APPEND);


                $tbinfo['tablename'] = 'adminuser';
                $tbinfo['type'] = 'replace';
                $tbinfo['pk'] = 'id';
                $tbinfo['data'][] = array(
                    'id' => $user['id'],
                    'truename' => $this->strconvert($user['truename']),
                    'passwordmd5' => $user['passwordmd5'],
                    'state' => $user['state'],
                    'mobil' => $user['mobil'],
                    'mid' => $user['mid'],
                    'username' => $this->strconvert($UserName)
                );
                $tbinfo1[] = $tbinfo;
                $tjinfo['dtjson'] = json_encode($tbinfo1);
                $tjinfo['token'] = $token;
                $mess = $this->http_post($XGURL, urldecode(http_build_query($tjinfo)));
                $tbinfo = array();
                $tbinfo1 = array();
            }
            //end


            //获取	当前设备号
            $license = $this->_dao->getOnes("SELECT LicenseKeyD FROM app_license_detail WHERE SignCS ='" . $SignCS . "' and Status=1 and (UseUser = '" . $UserName . "' or UseUser = '') and mc_type='$mc_type'");
            if ($params['debug'] == 1) {
                echo "</br>license:";
                print_r($license);
                echo "</br>";
            }

            $getsigncs = $this->_dao->getOne("SELECT 1 FROM app_license_detail WHERE SignCS !='$SignCS' and Status=1 and UseUser = '" . $UserName . "' and mc_type='" . $mc_type . "' ");

            if (empty($license)) {
                if ($getsigncs) {
                    $errormes = '当前设备未绑定或绑定错误，请联系管理员[1009]';
                    if ($isEnglish) $errormes = 'Device Number Error，Please Contact Your Manager[1009]';
                    $errtype = 1009;
                } else {
                    $errormes = '登录账号与初始化账号不符[1010]';
                    if ($isEnglish) $errormes = 'Login ID Is Not The Same With Initialized ID[1010]';
                    $errtype = 1010;
                }
                $arr2['Success'] = '0';
                $arr2['ErrorType'] = $errtype;
                $arr2['Message'] = base64_encode($this->utf8ToGb2312( $errormes ));
                $json_string = $this->pri_JSON($arr2);
                echo $json_string;
                exit;
            }

            $date = date('Ymd');//echo $date." ".$LoginDate;

            foreach ($license as $value) {
                $sign = md5($value . $SignCS . $date);
                if ($params['debug'] == 1) {
                    print_r($value . $SignCS . $date);
                    echo "</br>";
                }
                if ($IsEncrypt == 1 || $LicenseSign == $sign || date("Ymd", strtotime($LoginDate)) != $date) {
                    $GUID = $this->_dao->getGUID();
                    $Uid = $user['id'];
                    $TrueName = $user['truename'];
                    $Mid = $user['mid'];
                    $ComName = $this->stedao->getCompnameBymid($user['mid']);
                    $UserLevel = $user['power'];
                    $MobileNumber = $user['mobil'];
                    //$ExpiredDate = '';
                    $InterFaceUrl = '';
                    //据用户名与设备标识，获取app_session
                    $appSession = $this->_dao->getAppSession($UserName, $SignCS, $mc_type);

                    //根据设备标识 与 license 获取 客户端是否需要更新数据
                    $isupdate = $this->_dao->getOne("select IsUpdate from app_license_detail where SignCS ='" . $SignCS . "' and LicenseKeyD = '" . $value . "' and Status = 1 and mc_type='" . $mc_type . "' limit 1", 0);
                    $ExpiredDate = $this->_dao->getOne("select EndDate from app_license where Mid ='" . $Mid . "' and Status = 1 and mc_type='" . $mc_type . "' limit 1", 0);

                    //获取 sqlite 密码
                    $SQLitePass = $this->_dao->getOne("select SQLitePass from app_license where MID = '" . $Mid . "' and mc_type='" . $mc_type . "' limit 1 ", 0);
                    if (!empty($appSession['GUID'])) {
                        $GUID = $appSession['GUID'];
                    }

                    $accessId = $params['Accessid'];
                    $token = $params['Token'];
                    $secretKey = $GLOBALS['XG_KEY'][$accessId];

                    /*if($accessId!=''&&$token!=''){
						require_once("/usr/local/www/libs/xgpush/src/XgTools.php");
						$tag = array("SEND_HOT_NEWS");
						try {
							$ret = setTags($accessId,$secretKey,$token,$tag);
							if($ret['ret_code']!='0'){
								$status = '0';
								$message = $ret['err_msg'];
							}
						}catch (Exception $e) {
								$status = '0';
								$message = $e->getMessage();
						}
					}*/

                    //登录成功,session更新
                    $this->_dao->execute("REPLACE INTO app_session_temp SET GUID='$GUID', SignCS='$SignCS', Mid='$Mid', Uid='$Uid', ComName='$ComName', UserName='$UserName',TrueName='$TrueName', MobileNumber='$MobileNumber', ExpiredDate='$ExpiredDate', LoginDate=NOW(), LoginIp='$LoginIp', SystemType='$SystemType',SystemVersion='$date', LastDate=NOW(), LastAction='Login',mc_type='$mc_type'");
                    //登录成功推送
                    if ($mc_type == 3) {//新钢推送

                        $deviceType = '';
                        if ($SystemType == 'NewAndroidPhone') {
                            $deviceType = '17';
                        }
                        if ($SystemType == 'NewAndroidPad') {
                            $deviceType = '18';
                        }
                        if ($SystemType == 'NewiPad') {
                            $deviceType = '20';
                        }
                        if ($SystemType == 'NewiPhone') {
                            $deviceType = '19';
                        }

                        // $deviceType=17;
                        // $params['token']='0c9f065906347fde11d511468fa85849393c';
                        // echo $GLOBALS["testaccountuser"]["Uid"];
                        $ret = file_get_contents(STEELHOME_URL . "cron_97_system/xingePush/xingePush.php?deviceType=" . $deviceType . "&pushType=bind&tags=" . $Uid . "&token=" . $params['Token']);
                        $ret = json_decode($ret, true);

                    }

                    //日志处理
                    if ($mc_type == 2)//陕钢
                    {

                        $SGLoginURL = SGDCURL . '/web/systemapi.php?action=gzjSysLogin';
                        $logininfo['username'] = 'testuser';
                        $logininfo['pwd'] = '123456';
                        $logininfo['mc_type'] = '2';
                        //print_r($logininfo);
                        $mess = $this->http_post($SGLoginURL, http_build_query($logininfo));
                        //print_r($mess);
                        $content = $mess['content'];
                        //print_r($content);
                        $token = json_decode($this->clearBom($content), true);

                        //print_r($token);
                        $token = $token['Token'];
                        $SGURL = SGDCURL . '/web/systemapi.php?action=syncdb&mc_type=2';

                        $tbinfo['tablename'] = 'app_session_temp';
                        $tbinfo['type'] = 'replace';
                        $tbinfo['pk'] = 'GUID';
                        $tbinfo['data'][] = array(
                            'GUID' => $GUID,
                            'SignCS' => $SignCS,
                            'Mid' => $Mid,
                            'Uid' => $Uid,
                            'ComName' => $this->strconvert($ComName),
                            'UserName' => $this->strconvert($UserName),
                            'TrueName' => $this->strconvert($TrueName),
                            'MobileNumber' => $MobileNumber,
                            'ExpiredDate' => $ExpiredDate,
                            'LoginDate' => date("Y-m-d H:i:s"),
                            'LoginIp' => $LoginIp,
                            'SystemType' => $SystemType,
                            'SystemVersion' => $date,
                            'LastDate' => date("Y-m-d H:i:s"),
                            'LastAction' => 'Login',
                            'mc_type' => $mc_type,
                            'isEnglish' => $isEnglish
                        );

                        $tbinfo1[] = $tbinfo;
                        $tjinfo['dtjson'] = json_encode($tbinfo1);
                        $tjinfo['token'] = $token;
						$mess=$this->http_post( $SGURL,  urldecode(http_build_query($tjinfo)));
					}
					//xiangbin add 20201207 strat 					
					else if($mc_type==3 || $mc_type==4)//新钢
					{
					  
						$XGLoginURL=XGDCURL.'/api/gzjSysLogin';
						$logininfo['username']='testuser';
						$logininfo['pwd']='123456';
						$logininfo['mc_type']=$mc_type;
                        //print_r($logininfo);


                        $mess = $this->http_post($XGLoginURL, http_build_query($logininfo));
                        //print_r($mess);
                        $content = $mess['content'];
                        //print_r($content);
                        $token = json_decode($this->clearBom($content), true);

                        //print_r($token);
                        $token = $token['Token'];
                        //echo $token;
                        //$app_license_detail_id=$this->_dao->getone("select ID from app_license_detail where LID='".$sign['ID']."' and LicenseKeyD='".$LicenseKeyD."' and mc_type='".$mc_type."'");
                        $XGURL = XGDCURL . '/api/syncdb';

                        $tbinfo['tablename'] = 'app_session_temp';
                        $tbinfo['type'] = 'replace';
                        $tbinfo['pk'] = 'GUID';
                        $tbinfo['data'][] = array(
                            'GUID' => $GUID,
                            'SignCS' => $SignCS,
                            'Mid' => $Mid,
                            'Uid' => $Uid,
                            'ComName' => $this->strconvert($ComName),
                            'UserName' => $this->strconvert($UserName),
                            'TrueName' => $this->strconvert($TrueName),
                            'MobileNumber' => $MobileNumber,
                            'ExpiredDate' => $ExpiredDate,
                            'LoginDate' => date("Y-m-d H:i:s"),
                            'LoginIp' => $LoginIp,
                            'SystemType' => $SystemType,
                            'SystemVersion' => $date,
                            'LastDate' => date("Y-m-d H:i:s"),
                            'LastAction' => 'Login',
                            'mc_type' => $mc_type,
                            'isEnglish' => $isEnglish
                        );
                        //print_r(json_encode($tbinfo));
                        $tbinfo1[] = $tbinfo;
                        $tjinfo['dtjson'] = json_encode($tbinfo1);
                        $tjinfo['token'] = $token;
                        //echo urldecode(http_build_query($tjinfo));
                        //echo $XGURL;
                        $mess = $this->http_post($XGURL, urldecode(http_build_query($tjinfo)));

                    }
                    //xiangbin add 20201207 end

                    $this->_dao->WriteLog($Mid, $Uid, $SignCS, "Login", $actionstr, $LoginIp, $SystemType, $SystemVersion, "登录", '', '', $mc_type);

                    $adminuser = $this->stedao->getAdminUser($Mid);
                    //add by xr

                    $xgquanxian = "";//$xgquanxian="C|F|G|N|J|L|K|O";
                    if ($mc_type == 3 && $SystemType != 'NewPC') {
                        //$pop_idarr = $this->_dao->query("SELECT id,itemname FROM `app_license_popedom_item` WHERE `typeid` =1 and mc_type=3 AND itemname IN ('铁前成本', '钢轧成本', '利润汇总', '销售定价', '绩效效益预测', '研究报告',  '期货套保', '接单效益',  '管理看板', '数据录入', '经营分析', '管理看板查看') ORDER BY FIELD( itemname, '铁前成本', '钢轧成本', '利润汇总', '销售定价', '绩效效益预测', '研究报告', '期货套保', '接单效益',  '管理看板', '数据录入', '经营分析', '管理看板查看')");
                        $pop_idarr = $this->_dao->query("SELECT id,itemname FROM `app_license_popedom_item` WHERE `typeid` =1 and mc_type=3 AND itemname IN ('铁前成本', '钢轧成本', '利润汇总', '销售定价', '绩效效益预测',  '期货套保', '接单效益',  '管理看板', '数据录入', '经营分析', '管理看板查看') ORDER BY FIELD( itemname, '铁前成本', '钢轧成本', '利润汇总', '销售定价', '绩效效益预测', '期货套保', '接单效益',  '管理看板', '数据录入', '经营分析', '管理看板查看')");
                        $pop_idstr = '';
                        foreach ($pop_idarr as $popkey => $popvalue) {
                            if ($pop_idstr == "") {
                                $pop_idstr = "'" . $popvalue['id'] . "'";
                            } else {
                                $pop_idstr .= ",'" . $popvalue['id'] . "'";
                            }

                            if ($popvalue['itemname'] == '铁前成本') {
                                $xg_popid[$popvalue['id']] = 'C';
                            }
                            if ($popvalue['itemname'] == '钢轧成本') {
                                $xg_popid[$popvalue['id']] = 'F';
                            }
                            if ($popvalue['itemname'] == '利润汇总') {
                                $xg_popid[$popvalue['id']] = 'G';
                            }
                            if ($popvalue['itemname'] == '绩效效益预测') {
                                $xg_popid[$popvalue['id']] = 'J';
                            }
                            if ($popvalue['itemname'] == '销售定价') {
                                $xg_popid[$popvalue['id']] = 'N';
                            }
                            if ($popvalue['itemname'] == '期货套保') {
                                $xg_popid[$popvalue['id']] = 'O';
                            }
//
//                            if ($popvalue['itemname'] == '研究报告') {
//                                $xg_popid[$popvalue['id']] = 'L';
//                            }
                            if ($popvalue['itemname'] == '接单效益') {
                                $xg_popid[$popvalue['id']] = 'P';
                            }
                            if ($popvalue['itemname'] == '管理看板') {
                                $xg_popid[$popvalue['id']] = 'K';
                            }
                            if ($popvalue['itemname'] == '数据录入') {
                                $xg_popid[$popvalue['id']] = 'Q';
                            }
                            if ($popvalue['itemname'] == '经营分析') {
                                $xg_popid[$popvalue['id']] = 'R';
                            }
                            if ($popvalue['itemname'] == '管理看板查看') {
                                $xg_popid[$popvalue['id']] = 'S';
                            }
                        }
                        $popid_arr = $this->_dao->query("SELECT *  FROM `app_license_popedom` WHERE `uid` ='" . $Uid . "' and mc_type=3 AND isdel=0 AND popid IN (" . $pop_idstr . " ) GROUP BY popid ORDER BY FIELD( popid, " . $pop_idstr . ")");

                    } elseif ($mc_type == 3 && $SystemType == 'NewPC') {
                        //$pop_idarr = $this->_dao->query("SELECT id,itemname FROM `app_license_popedom_item` WHERE `typeid` =1 and mc_type=3 AND itemname IN ('铁前成本', '钢轧成本', '利润汇总','绩效效益预测','销售定价', '接单效益','管理看板','数据录入', '期货套保','研究报告','经营分析', '管理看板查看') ORDER BY FIELD( itemname, '铁前成本', '钢轧成本', '利润汇总','绩效效益预测','销售定价', '接单效益','管理看板','数据录入', '期货套保','研究报告','经营分析', '管理看板查看' )");
                        $pop_idarr = $this->_dao->query("SELECT id,itemname FROM `app_license_popedom_item` WHERE `typeid` =1 and mc_type=3 AND itemname IN ('铁前成本', '钢轧成本', '利润汇总','绩效效益预测','销售定价', '接单效益','管理看板','数据录入', '期货套保','经营分析', '管理看板查看') ORDER BY FIELD( itemname, '铁前成本', '钢轧成本', '利润汇总','绩效效益预测','销售定价', '接单效益','管理看板','数据录入', '期货套保','经营分析', '管理看板查看' )");
                        // 铁前成本 C 钢轧成本 F 利润汇总 G 销售定价 N  管理看板 K  数据录入Q    绩效效益预测  J 研究报告 L 原料性价比 K 期货套保 O  接单效益 P
                        // "C|F|G|J|N|P|K|Q|O|L";
                        $pop_idstr = '';
                        foreach ($pop_idarr as $popkey => $popvalue) {
                            if ($pop_idstr == "") {
                                $pop_idstr = "'" . $popvalue['id'] . "'";
                            } else {
                                $pop_idstr .= ",'" . $popvalue['id'] . "'";
                            }

                            if ($popvalue['itemname'] == '铁前成本') {
                                $xg_popid[$popvalue['id']] = 'C';
                            }
                            if ($popvalue['itemname'] == '钢轧成本') {
                                $xg_popid[$popvalue['id']] = 'F';
                            }
                            if ($popvalue['itemname'] == '利润汇总') {
                                $xg_popid[$popvalue['id']] = 'G';
                            }
                            if ($popvalue['itemname'] == '绩效效益预测') {
                                $xg_popid[$popvalue['id']] = 'J';
                            }
                            if ($popvalue['itemname'] == '销售定价') {
                                $xg_popid[$popvalue['id']] = 'N';
                            }
                            if ($popvalue['itemname'] == '期货套保') {
                                $xg_popid[$popvalue['id']] = 'O';
                            }
//
//                            if ($popvalue['itemname'] == '研究报告') {
//                                $xg_popid[$popvalue['id']] = 'L';
//                            }
                            if ($popvalue['itemname'] == '接单效益') {
                                $xg_popid[$popvalue['id']] = 'P';
                            }
                            if ($popvalue['itemname'] == '管理看板') {
                                $xg_popid[$popvalue['id']] = 'K';
                            }
                            if ($popvalue['itemname'] == '数据录入') {
                                $xg_popid[$popvalue['id']] = 'Q';
                            }
                            if ($popvalue['itemname'] == '经营分析') {
                                $xg_popid[$popvalue['id']] = 'R';
                            }
                            if ($popvalue['itemname'] == '管理看板查看') {
                                $xg_popid[$popvalue['id']] = 'S';
                            }
                        }
                        $popid_arr = $this->_dao->query("SELECT *  FROM `app_license_popedom` WHERE `uid` ='" . $Uid . "' and mc_type=3 AND isdel=0 AND popid IN (" . $pop_idstr . " ) GROUP BY popid ORDER BY FIELD( popid, " . $pop_idstr . ")");

					
					}elseif($mc_type==4){
						// 价格预测 C 行业成本 F 市场对标 J 智慧大屏 K 指标预警 L 期货套保 O 行业对标 G 系统管理 M mc_type=4
						$pop_idarr=$this->_dao->query("SELECT id,itemname FROM `app_license_popedom_item` WHERE `typeid` =17 and mc_type=4 AND itemname IN ('价格预测', '行业对标', '市场对标','智慧大屏','销售定价', '指标预警','期货套保','行业成本', '系统管理','我的数据','我的模板','智慧报表','销售定价','原燃料报表','研究报告') ORDER BY FIELD( itemname, '价格预测', '行业对标', '市场对标','智慧大屏','销售定价', '指标预警','期货套保','行业成本', '系统管理' ,'我的数据','我的模板','智慧报表','销售定价','原燃料报表','研究报告')");
                        $pop_idstr=''; 
						foreach ($pop_idarr as $popkey => $popvalue) {
							if($pop_idstr==""){
								$pop_idstr="'".$popvalue['id']."'";
							}else{
								$pop_idstr.=",'".$popvalue['id']."'";
							}

							if($popvalue['itemname']=='价格预测'){
								$xg_popid[$popvalue['id']]='C';
							}
							if($popvalue['itemname']=='行业成本'){
								$xg_popid[$popvalue['id']]='F';
							}
							if($popvalue['itemname']=='市场对标'){
								$xg_popid[$popvalue['id']]='J';
							}
							if($popvalue['itemname']=='智慧大屏'){
								$xg_popid[$popvalue['id']]='K';
							}
							if($popvalue['itemname']=='指标预警'){
								$xg_popid[$popvalue['id']]='L';
							}
							if($popvalue['itemname']=='期货套保'){
								$xg_popid[$popvalue['id']]='O';
							}
							
							if($popvalue['itemname']=='行业对标'){
								$xg_popid[$popvalue['id']]='G';
							}
							if($popvalue['itemname']=='系统管理'){
								$xg_popid[$popvalue['id']]='M';
							}
							if($popvalue['itemname']=='我的数据'){
								$xg_popid[$popvalue['id']]='P';
							}
							if($popvalue['itemname']=='我的模板'){
								$xg_popid[$popvalue['id']]='R';
							}
							if($popvalue['itemname']=='智慧报表'){
								$xg_popid[$popvalue['id']]='S';
							}
							if($popvalue['itemname']=='原燃料报表'){
								$xg_popid[$popvalue['id']]='T';
							}
							if($popvalue['itemname']=='销售定价'){
								$xg_popid[$popvalue['id']]='N';
							}
							if($popvalue['itemname']=='研究报告'){
								$xg_popid[$popvalue['id']]='U';
							}
						}
						$popid_arr=$this->_dao->query("SELECT *  FROM `app_license_popedom` WHERE `uid` ='".$Uid."' and mc_type=4 AND isdel=0 AND popid IN (".$pop_idstr." ) GROUP BY popid ORDER BY FIELD( popid, ".$pop_idstr.")");

					
					}elseif($mc_type==5){
						// 价格预测 C 行业成本 F 市场对标 J 智慧大屏 K 指标预警 L 期货套保 O 行业对标 G 系统管理 M mc_type=5
						$pop_idarr=$this->_dao->query("SELECT id,itemname FROM `app_license_popedom_item` WHERE `typeid` =20 and mc_type=5 AND itemname IN ('价格预测', '行业对标', '市场对标','智慧大屏','销售定价', '指标预警','期货套保','行业成本', '系统管理','我的数据','我的模板','智能报表','定价模型','原燃料报表','研究报告') ORDER BY FIELD( itemname, '价格预测', '行业对标', '市场对标','智慧大屏','销售定价', '指标预警','期货套保','行业成本', '系统管理' ,'我的数据','我的模板','智能报表','定价模型','原燃料报表','研究报告')");
                        $pop_idstr=''; 
						foreach ($pop_idarr as $popkey => $popvalue) {
							if($pop_idstr==""){
								$pop_idstr="'".$popvalue['id']."'";
							}else{
								$pop_idstr.=",'".$popvalue['id']."'";
							}

							if($popvalue['itemname']=='价格预测'){
								$xg_popid[$popvalue['id']]='C';
							}
							if($popvalue['itemname']=='行业成本'){
								$xg_popid[$popvalue['id']]='F';
							}
							if($popvalue['itemname']=='市场对标'){
								$xg_popid[$popvalue['id']]='J';
							}
							if($popvalue['itemname']=='智慧大屏'){
								$xg_popid[$popvalue['id']]='K';
							}
							if($popvalue['itemname']=='指标预警'){
								$xg_popid[$popvalue['id']]='L';
							}
							if($popvalue['itemname']=='期货套保'){
								$xg_popid[$popvalue['id']]='O';
							}
							
							if($popvalue['itemname']=='行业对标'){
								$xg_popid[$popvalue['id']]='G';
							}
							if($popvalue['itemname']=='系统管理'){
								$xg_popid[$popvalue['id']]='M';
							}
							if($popvalue['itemname']=='我的数据'){
								$xg_popid[$popvalue['id']]='P';
							}
							if($popvalue['itemname']=='我的模板'){
								$xg_popid[$popvalue['id']]='R';
							}
							if($popvalue['itemname']=='智能报表'){
								$xg_popid[$popvalue['id']]='S';
							}
							if($popvalue['itemname']=='原燃料报表'){
								$xg_popid[$popvalue['id']]='T';
							}
							if($popvalue['itemname']=='定价模型'){
								$xg_popid[$popvalue['id']]='N';
							}
							if($popvalue['itemname']=='研究报告'){
								$xg_popid[$popvalue['id']]='U';
							}
						}
						$popid_arr=$this->_dao->query("SELECT *  FROM `app_license_popedom` WHERE `uid` ='".$Uid."' and mc_type=5 AND isdel=0 AND popid IN (".$pop_idstr." ) GROUP BY popid ORDER BY FIELD( popid, ".$pop_idstr.")");

					
					}
					if(($mc_type==3 || $mc_type==4|| $mc_type==5) &&!empty($popid_arr)){ 
						// 铁前成本 C 钢轧成本 F 利润汇总 G 销售定价 N 绩效效益预测 J 研究报告 L 原料性价比 K 期货套保 O mc_type=3
						// 价格预测 C 行业对标 F 市场对标 G 智慧大屏 K 指标预警 L 期货套保 O 行业成本 F 系统管理 M mc_type=4
						foreach ($popid_arr as $popid_k => $popid_v) { 
							if($xgquanxian==""){
								$xgquanxian=$xg_popid[$popid_v['popid']];
							}else{
								$xgquanxian.='|'.$xg_popid[$popid_v['popid']];
							}
						}
					}

                    if ($mc_type == 2) {
                        $popid_arr = $this->_dao->getOne("SELECT ID  FROM `app_license_popedom` WHERE `uid` ='" . $Uid . "' and mc_type=2 AND isdel=0 AND popid=54");

                        if (!empty($popid_arr))
                            $dpshow = 1;
                        else
                            $dpshow = "";


                    }

                    if ($quanxian == 1) {

                        if ($params['dzquanxian'] == 1) {
                            $SignCS_arr = $this->_dao->getRow("select * from app_license_detail where SignCS ='" . $SignCS . "' and UseUser  = '" . $UserName . "' and mc_type='" . $mc_type . "' and Status = 1 ");

                            $lid_arr = $this->_dao->query("select dc_code_datatypeid from app_license_dzprivilege where lid ='" . $SignCS_arr['LID'] . "' and mc_type='" . $mc_type . "'");

                            $dzqxstr = '';
                            foreach ($lid_arr as $k_lid => $k_v) {
                                if ($dzqxstr == '') {
                                    $dzqxstr = $k_v['dc_code_datatypeid'];
                                } else {
                                    $dzqxstr = $dzqxstr . ',' . $k_v['dc_code_datatypeid'];
                                }
                            }
                        }

                        //获取品种
                        $pz = $this->_dao->getOne("select pinzhong from app_license where MID = '$mid' and Status=1 and mc_type='" . $mc_type . "' limit 1 ", 0);
                        if ($pz == '8192') {
                            $pz = 'allpz';
                        } else {
                            foreach (split2($pz) as $k => $v) {
                                if ($k == 0) {
                                    $pz = $v;
                                } else {
                                    $pz = $pz . '|' . $v;
                                }
                            }

                        }

                        //orderlist：读取dc_custom_order表 根据Mid和mc_type查询
                        $orderlist = $this->_dao->query("select app_license.ID,app_license.MID,DataType,BuyType,Syear,Eyear from dc_custom_order inner join app_license on dc_custom_order.Mid = app_license.Mid and app_license.mc_type='$mc_type' and dc_custom_order.mc_type='$mc_type' where dc_custom_order.MID='$mid' AND app_license.StartDate<NOW() AND app_license.EndDate>now()");


                        if ($mc_type == 0) {
                            $ids = $this->_dao->getOnes("select ID from dc_code_class where scode in ('A','B','N') and mc_type='$mc_type'");
                        } else {
                            $ids = $this->_dao->getOnes("select ID from dc_code_class where scode in ('A','B') and mc_type='$mc_type'");
                        }

                        $isyx = $this->_dao->getRow("select * from app_license where MID = '$mid' AND app_license.StartDate<NOW() AND app_license.EndDate>now() ");

                        if ($isyx) {
                            if ($mc_type == 0) {
                                $ids = $this->_dao->getOnes("select ID from dc_code_class where scode in ('A','B','N') and mc_type='$mc_type'");
                            } else {
                                $ids = $this->_dao->getOnes("select ID from dc_code_class where scode in ('A','B') and mc_type='$mc_type'");
                            }

                            foreach ($ids as $key => $val) {
                                $one = array();
                                $one['ID'] = 0;
                                $one['MID'] = 1;
                                $one['DataType'] = $val;
                                $one['BuyType'] = 2;
                                $one['Syear'] = '2014';
                                $one['Eyear'] = '2019';
                                $orderlist[] = $one;
                            }
                        }


                        /*foreach($orderlist as $key=>$val) {
							$scode=$this->_dao->getOne("select scode from dc_code_class where ID='".$val['DataType']."'");
							$orderlist[$key]['scode']=$scode;
							$orderlist[$key]['level']=$this->getcurrentCengshu( $scode,$num=1,$mc_type);


						}*/
                        $idstr = "";
                        foreach ($orderlist as $key => $val) {
                            $idstr .= $val['DataType'] . ",";
                        }
                        $idstr = rtrim($idstr, ",");
                        //echo '<pre>';print_r($orderlist);
                        if ($idstr)
                            $scodelist = $this->_dao->query("select id,scode from dc_code_class where ID in(" . $idstr . ") and mc_type='$mc_type';");
                        //echo '<pre>';print_r($scodelist);

                        foreach ($scodelist as $key => $val) {
                            $scodelist2[$val['id']] = $val['scode'];
                        }
                        //echo '<pre>';print_r($scodelist2);
                        foreach ($orderlist as $key => $val) {
                            $scode = $scodelist2[$val['DataType']];
                            $orderlist[$key]['scode'] = $scode;
                            $orderlist[$key]['level'] = $this->getcurrentCengshu2($scode);
                            if ($scode == '') {
                                unset($orderlist[$key]);
                            }
                            if ($istry == 1 && $mc_type == 0 && in_array($scode, array('K', 'O')))//试用去除KO
                            {
                                unset($orderlist[$key]);
                            }
                        }

                        //去除重复数据，id=0的去除
                        foreach ($orderlist as $key1 => $val1) {
                            foreach ($orderlist as $key => $val) {
                                if ($val['DataType'] == $val1['DataType'] && $val['ID'] != $val1['ID']) {
                                    if ($val['ID'] == '0') {
                                        unset($orderlist[$key]);
                                    }
                                    if ($val1['ID'] == '0') {
                                        unset($orderlist[$key1]);
                                    }
                                }

                            }
                        }
                        $cronconfig = array();
                        if ($mc_type == 3) {
                            $cronconfig_arr = $this->_dao->query("select * from xinsteel_cronconfig where userid='" . $Uid . "'");
                            $n = 0;
                            foreach ($cronconfig_arr as $key => $value) {
                                $cronconfig[$n]['url'] = $value['url'];
                                $cronconfig[$n]['frequency'] = $value['frequency'];
                                $cronconfig[$n]['dayweeks'] = $value['dayweeks'];
                                $cronconfig[$n]['crontime'] = $value['crontime'];
                                $cronconfig[$n]['title'] = $value['title'];
                                $cronconfig[$n]['desc'] = $value['desc'];
                                $n++;
                            }
                        }
                        if ($params['dzquanxian'] == 1) {
                            $arr = array(
                                'Success' => '1',
                                'GUID' => $GUID,
                                'Message' => base64_encode($this->utf8ToGb2312( '登录成功' )),
                                'InterFaceUrl' => $InterFaceUrl,
                                'Result' => array(
                                    'Uid' => $Uid,
                                    'UserName' => $UserName,
                                    'TrueName' => base64_encode( $this->utf8ToGb2312( $TrueName )),
                                    'MobileNumber' => $MobileNumber,
                                    'Mid' => $Mid,
                                    'ComName' => base64_encode( $this->utf8ToGb2312( $ComName ) ),
                                    'UserLevel' => $UserLevel,
                                    'ExpiredDate' => $ExpiredDate,
                                    'SmsExpiredDate' => $ExpiredDate,
                                    'AdminName' => base64_encode( $this->utf8ToGb2312( $adminuser['truename'] ) ),
                                    'AdminPhone' => $adminuser['tel'],
                                    'AdminMobile' => $adminuser['mobil'],
                                    'IsUpdate' => $isupdate,
                                    'SQLitePass' => $SQLitePass,
                                    'dzquanxian' => $istry ? "" : $dzqxstr,
                                    'pinzhong' => $pz,
                                    'orderlist' => array_values($orderlist),
                                    'xgquanxian' => $xgquanxian,
                                    'dpshow' => $dpshow,
                                    'cronconfig' => $cronconfig,
                                    'istry' => $istry
                                )
                            );
                        } else {
                            $arr = array(
                                'Success' => '1',
                                'GUID' => $GUID,
                                'Message' => base64_encode($this->utf8ToGb2312( '登录成功' )),
                                'InterFaceUrl' => $InterFaceUrl,
                                'Result' => array(
                                    'Uid' => $Uid,
                                    'UserName' => $UserName,
                                    'TrueName' => base64_encode( $this->utf8ToGb2312( $TrueName )),
                                    'MobileNumber' => $MobileNumber,
                                    'Mid' => $Mid,
                                    'ComName' => base64_encode($this->utf8ToGb2312( $ComName )),
                                    'UserLevel' => $UserLevel,
                                    'ExpiredDate' => $ExpiredDate,
                                    'SmsExpiredDate' => $ExpiredDate,
                                    'AdminName' => base64_encode( $this->utf8ToGb2312( $adminuser['truename'] )),
                                    'AdminPhone' => $adminuser['tel'],
                                    'AdminMobile' => $adminuser['mobil'],
                                    'IsUpdate' => $isupdate,
                                    'SQLitePass' => $SQLitePass,

                                    'pinzhong' => $pz,
                                    'orderlist' => array_values($orderlist),
                                    'xgquanxian' => $xgquanxian,
                                    'dpshow' => $dpshow,
                                    'cronconfig' => $cronconfig,
                                    'istry' => $istry
                                )
                            );

                        }

                    } else {
                        $arr = array(
                            'Success' => '1',
                            'GUID' => $GUID,
                            'Message' => base64_encode($this->utf8ToGb2312( '登录成功' )),
                            'InterFaceUrl' => $InterFaceUrl,
                            'Result' => array(
                                'Uid' => $Uid,
                                'UserName' => $UserName,
                                'TrueName' => base64_encode($this->utf8ToGb2312( $TrueName )),
                                'MobileNumber' => $MobileNumber,
                                'Mid' => $Mid,
                                'ComName' => base64_encode( $this->utf8ToGb2312( $ComName )),
                                'UserLevel' => $UserLevel,
                                'ExpiredDate' => $ExpiredDate,
                                'SmsExpiredDate' => $ExpiredDate,
                                'AdminName' => base64_encode( $this->utf8ToGb2312( $adminuser['truename'] )),
                                'AdminPhone' => $adminuser['tel'],
                                'AdminMobile' => $adminuser['mobil'],
                                'IsUpdate' => $isupdate,
                                'SQLitePass' => $SQLitePass,
                            )
                        );
                    }
                    if ($isEnglish) $arr['Message'] = 'Success';
                    break;
                }
                /*else
				{
					$arr = array(
						'Success'=>'0',
						'ErrorType'=>'1000',
						'ErrorPhoneNumber'=>'50581010',
						'Message'=>base64_encode('登录出错'),
					);
				}
				unset($sign);
				*/
            }
        }
        if ($arr['Success'] == '0' && $arr['ErrorType'] == "0") {
            $exist = $this->_dao->getOne("select 1 from app_license_detail where status=1 and mc_type='" . $mc_type . "' and SignCS ='$SignCS' ");
            if ($exist) {
                $arr['Message'] = base64_encode($this->utf8ToGb2312( "该设备号已被绑定，登录失败[1011]" ));
                if ($isEnglish) $arr['Message'] = base64_encode("Login Failed For Device Number has already been bound[1011]");
                $arr['ErrorType'] = 1011;
            }
        }

        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    public function LoginOut($params)
    {

        $arrtem = $params;
        //array_pop($arrtem);
        foreach ($arrtem as $k => $v) {
            $actionstr .= "&" . $k . "=" . $v;
        }

        $SignCS = $params['SignCS'];
        $SystemType = $params['SystemType'];
        $GUID = $params['GUID'];
        $SystemVersion = $params['SystemVersion'];
        if (!empty($params['mc_type'])) $mc_type = $params['mc_type'];
        else $mc_type = 0;
        $isEnglish = $params['isEnglish'];
        $LoginIp = $this->getIP();

        if (array_key_exists($GUID, $GLOBALS["testaccountarr"]) && $SignCS != '') {
            $user = $GLOBALS["testaccountuser"];
        } else {
            $user = $this->_dao->getUser($GUID, $SignCS, $mc_type);
        }
        $Uid = $user['Uid'];
        $Mid = $user['Mid'];

        $accessId = $params['Accessid'];
        $token = $params['Token'];
        $secretKey = $GLOBALS['XG_KEY'][$accessId];
        /*if($accessId!=''&&$token!=''){
			require_once("/usr/local/www/libs/xgpush/src/XgTools.php");
			try {
				$ret = delAllTags($accessId,$secretKey,$token);
				if($ret['ret_code']!='0'){
					$status = '0';
					$message = $ret['err_msg'];
				}
			}catch (Exception $e){
					$status = '0';
					$message = $e->getMessage();
			}
		}*/
        //删除临时表记录
        // $this->_dao->execute( "DELETE FROM app_session_temp WHERE GUID='$GUID' limit 1" );

	if($mc_type==2)//陕钢注销
        {

            $SGLoginURL = SGDCURL . '/web/systemapi.php?action=gzjSysLogin';
            $logininfo['username'] = 'testuser';
            $logininfo['pwd'] = '123456';
            $logininfo['mc_type'] = '2';
            $mess = $this->http_post($SGLoginURL, http_build_query($logininfo));
            $content = $mess['content'];
            $token = json_decode($this->clearBom($content), true);
            $token = $token['Token'];
            $SGURL = SGDCURL . '/web/systemapi.php?action=syncdb&mc_type=2';

            $tbinfo['tablename'] = 'app_session_temp';
            $tbinfo['type'] = 'delete';
            $tbinfo['pk'] = 'GUID';
            $tbinfo['data'][] = array(
                'GUID' => $GUID
            );
            //print_r(json_encode($tbinfo));
            $tbinfo1[] = $tbinfo;
            $tjinfo['dtjson'] = json_encode($tbinfo1);
            $tjinfo['token'] = $token;
            //echo urldecode(http_build_query($tjinfo));
            //echo $XGURL;
	    if($params['UserName']!='zhangkang'&&$params['GUID']!='bac0eeba92af11ec91f3001d09719b40')//提供给陕钢的guid 不清理
            	$mess = $this->http_post($SGURL, urldecode(http_build_query($tjinfo)));

        } //xiangbin add 20201207 strat
		else if($mc_type==3 || $mc_type==4)//新钢注销
        {

            $XGLoginURL = XGDCURL . '/api/gzjSysLogin';
            $logininfo['username'] = 'testuser';
            $logininfo['pwd'] = '123456';
			$logininfo['mc_type']=$mc_type;
            //print_r($logininfo);
            $mess = $this->http_post($XGLoginURL, http_build_query($logininfo));
            //print_r($mess);
            $content = $mess['content'];
            //print_r($content);
            $token = json_decode($this->clearBom($content), true);

            //print_r($token);
            $token = $token['Token'];
            //echo $token;
            //$app_license_detail_id=$this->_dao->getone("select ID from app_license_detail where LID='".$sign['ID']."' and LicenseKeyD='".$LicenseKeyD."' and mc_type='".$mc_type."'");
            $XGURL = XGDCURL . '/api/syncdb';

            $tbinfo['tablename'] = 'app_session_temp';
            $tbinfo['type'] = 'delete';
            $tbinfo['pk'] = 'GUID';
            $tbinfo['data'][] = array(
                'GUID' => $GUID
            );
            //print_r(json_encode($tbinfo));
            $tbinfo1[] = $tbinfo;
            $tjinfo['dtjson'] = json_encode($tbinfo1);
            $tjinfo['token'] = $token;
            //echo urldecode(http_build_query($tjinfo));
            //echo $XGURL;
	    if($params['UserName']!='xiscorobot'&&$params['GUID']!='65237e8c9c2411ebbebc001d09719b40')//不清理robot账号
            	$mess = $this->http_post($XGURL, urldecode(http_build_query($tjinfo)));

        }
        //xiangbin add 20201207 end

        //登录成功推送
        if ($mc_type == 3) {//新钢推送

            $deviceType = '';
            if ($SystemType == 'NewAndroidPhone') {
                $deviceType = '17';
            }
            if ($SystemType == 'NewAndroidPad') {
                $deviceType = '18';
            }
            if ($SystemType == 'NewiPad') {
                $deviceType = '20';
            }
            if ($SystemType == 'NewiPhone') {
                $deviceType = '19';
            }

            // $deviceType=17;
            // $params['token']='0c9f065906347fde11d511468fa85849393c';
            // echo $GLOBALS["testaccountuser"]["Uid"];
            $ret = file_get_contents(STEELHOME_URL . "cron_97_system/xingePush/xingePush.php?deviceType=" . $deviceType . "&pushType=unbind&tags=" . $GLOBALS["testaccountuser"]["Uid"] . "&token=" . $params['Token']);
            $ret = json_decode($ret, true);

        }

        //日志处理
        $this->_dao->WriteLog($Mid, $Uid, $SignCS, "LoginOut", $actionstr, $LoginIp, $SystemType, $SystemVersion, "注销登录", '', '', $mc_type);
        $arr = array(
            'Success' => '1',
            'Message' => base64_encode('注销成功'),
        );
        if ($isEnglish) $arr['Message'] = base64_encode('Logout Succeeded!');

        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }


    // 重置客户是否更新字段 2013/10/30

    public function SetUpdate($params)
    {


        $SignCS = $params['SignCS'];
        $LicenseSign = $params['LicenseSign']; // 子license
        $LoginIp = $this->getIP();

        $GUID = $params['GUID'];
        $SignCS = $params['SignCS'];
        $action = $params['action'];        //接口名称
        if (!empty($params['mc_type'])) $mc_type = $params['mc_type'];
        else $mc_type = 0;
        $isEnglish = $params['isEnglish'];
        $user = $this->_dao->getUser($GUID, $SignCS, $mc_type);

        $arrtem = $params;
        //array_pop($arrtem);
        foreach ($arrtem as $k => $v) {
            $actionstr .= "&" . $k . "=" . $v;
        }

        $arr = array(
            'Success' => '0',
            'Message' => base64_encode('更新失败'),
        );
        if ($isEnglish) $arr['Message'] = "Update Failed!";

        $user = $this->_dao->getUser($GUID, $SignCS, $mc_type);


        //根据设备标识 与 license 获取 客户端是否需要更新数据
        $isupdate = $this->_dao->getRow("select * from app_license_detail where SignCS ='" . $SignCS . "' and LicenseKeyD = '" . $LicenseSign . "' and mc_type='" . $mc_type . "' and Status = 1  limit 1", 0);

        if ($isupdate) {

            $this->_dao->execute("update app_license_detail set IsUpdate = 0 where ID = '" . $isupdate['ID'] . "' and mc_type='" . $mc_type . "'");

            $arr = array(
                'Success' => '1',
                'Message' => base64_encode('更新成功'),
            );
            if ($isEnglish) $arr['Message'] = base64_encode('Success');

            $this->_dao->WriteLog($user['Mid'], $user['Uid'], $SignCS, $action, $actionstr, $this->getIP(), $this->getSystemType(), $this->getSystemVersion(), "更新数据", '', '', $mc_type);

        }

        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }


    public function SystemVersionUpdate($params)
    {

        if (!empty($params['mc_type'])) $mc_type = $params['mc_type'];
        else $mc_type = 0;
        $isEnglish = $params['isEnglish'];
        //系统版本
        $xtbb = $this->_dao->getRow("select * from app_version where VersionType=1 and Status=1 and mc_type='$mc_type'");

        //数据库版本
        $sjbb = $this->_dao->getRow("select * from app_version where VersionType=2 and Status=1 and mc_type='$mc_type'");


        $DB_VERSION_LAST = $sjbb['VersionNo'];
        $DB_VERSION_LAST_DATE = $sjbb['ReleaseDate'];

        $SYS_VERSION_LAST = $xtbb['VersionNo'];
        $SYS_VERSION_LAST_DATE = $xtbb['ReleaseDate'];
        $SYS_VERSION_LAST_URL = $sjbb['DownloadUrl'];


        $AppVersionCurrent = $params['AppVersionCurrent'];
        $DbVersionCurrent = $params['DbVersionCurrent'];
        $DbVersionDateLast = $params['DbVersionDateLast'];
        $LicenseSign = $params['LicenseSign'];
        $SystemType = $params['SystemType'];
        $SystemTypeString = $params['SystemTypeString'];
        $SignCS = $params['SignCS'];
        $GUID = $params['GUID'];
        $IsInstall = $params['IsInstall'];

        $arr = array(
            'Success' => '0',
            'Message' => '失败',
        );
        if ($isEnglish) $arr['Message'] = 'Failed!';

//		if($IsInstall=="1" && $DbVersionCurrent=="")
//		{
//			//第一次安装
//			$MobileNumber="";
//			$InstallDate = date( "Y-m-d H:i:s" );
//			$CreateDate = date( "Y-m-d H:i:s" );
//
//		}else{
//
//		}

        if ($DB_VERSION_LAST != $DbVersionCurrent) {
            //频道
            $pd = $this->_dao->query("SELECT ID,sname,scode,sod FROM app_code_channel WHERE 1 AND Status=1 ORDER BY sod Asc");
            //区域
            $qy = $this->_dao->query("SELECT ID,sname,scode,sod FROM app_code_area WHERE 1 AND Status=1 ORDER BY sod Asc");
            //城市
            $cs = $this->_dao->query("SELECT ID,sname,scode,chcode,areacode,sod FROM app_code_city WHERE 1 AND Status=1 ORDER BY chcode,areacode,sod Asc");
            //品种
            $pz = $this->_dao->query("SELECT ID,sname,scode,chcode,sod FROM app_code_variety WHERE 1 AND Status=1 ORDER BY chcode,sod Asc");
            //栏目
            $lm = $this->_dao->query("SELECT ID,sname,scode,chcode,sod FROM app_code_column WHERE 1 AND Status=1 ORDER BY sod Asc");
            //钢厂
            $gc = $this->_dao->query("SELECT ID,sname,scode,areacode,sod FROM app_code_gc WHERE 1 AND Status=1 ORDER BY areacode,sod Asc");
            //地图分类
            $mapc1 = $this->_dao->query("SELECT ID,sname,scode,sod FROM app_code_mapc1 WHERE 1 AND Status=1 ORDER BY sod Asc");
            //地图品种
            $mapc2 = $this->_dao->query("SELECT ID,sname,scode,mcode,sod FROM app_code_mapc2 WHERE 1 AND Status=1 ORDER BY mcode,sod Asc");
            $arr['Success'] = '1';
            $arr['Message'] = base64_encode('成功');
            if ($isEnglish) $arr['Message'] = base64_encode('Success');
            $arr['VersionUrl'] = '';
            $arr['VersionLast'] = $DB_VERSION_LAST;
            $arr['VersionDate'] = $DB_VERSION_LAST_DATE;
            $arr['SystemVersionLast'] = $SYS_VERSION_LAST;
            $arr['SystemVersionUrl'] = $SYS_VERSION_LAST_URL;
            $arr['SystemVersionDate'] = $SYS_VERSION_LAST_DATE;

            $arr['Results1'] = $pd;
            $arr['Results2'] = $qy;
            $arr['Results3'] = $cs;
            $arr['Results4'] = $pz;
            $arr['Results5'] = $lm;
            $arr['Results6'] = $gc;
            $arr['Results7'] = $mapc1;
            $arr['Results8'] = $mapc2;
        }

        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    public function SystemVersionGet($params)
    {
        if (!empty($params['mc_type'])) $mc_type = $params['mc_type'];
        else $mc_type = 0;
        $isEnglish = $params['isEnglish'];

        //系统版本
        $xtbb = $this->_dao->getRow("select * from app_version where VersionType=1 and Status=1 and mc_type='$mc_type'");

        //数据库版本
        $sjbb = $this->_dao->getRow("select * from app_version where VersionType=2 and Status=1 and mc_type='$mc_type'");


        $DB_VERSION_LAST = $sjbb['VersionNo'];
        $DB_VERSION_LAST_DATE = $sjbb['ReleaseDate'];

        $SYS_VERSION_LAST = $xtbb['VersionNo'];
        $SYS_VERSION_LAST_DATE = $xtbb['ReleaseDate'];
        $SYS_VERSION_LAST_URL = $sjbb['DownloadUrl'];

        $AndroidSystemVersionUrl = $sjbb['AndroidSystemVersionUrl'];
        $AndroidSystemVersionUrlMd5 = $sjbb['AndroidSystemVersionUrlMd5'];
        $IOSSystemVersionUrl = $sjbb['IOSSystemVersionUrl'];
        $IOSSystemVersionUrlMd5 = $sjbb['IOSSystemVersionUrlMd5'];

        $EnSystemVersionUrl = $sjbb['EnSystemVersionUrl'];
        $EnVersionUrlMd5 = $sjbb['EnVersionUrlMd5'];


        $AppVersionCurrent = $params['AppVersionCurrent'];
        $DbVersionCurrent = $params['DbVersionCurrent'];
        $DbVersionDateLast = $params['DbVersionDateLast'];
        $LicenseSign = $params['LicenseSign'];
        $SystemType = $params['SystemType'];
        $SystemTypeString = $params['SystemTypeString'];
        $SignCS = $params['SignCS'];
        $GUID = $params['GUID'];
        $SystemVersion = $params['SystemVersion'];

        $arr = array(
            'Success' => '0',
            'Message' => $this->utf8ToGb2312('失败'),
        );
        if ($isEnglish) $arr['Message'] = 'Failed!';

        $arr['Success'] = '1';
        $arr['Message'] = base64_encode($this->utf8ToGb2312('成功'));
        if ($isEnglish) $arr['Message'] = 'Success';
        $arr['VersionUrl'] = '';

        $arr['VersionLast'] = $DB_VERSION_LAST;
        $arr['VersionDate'] = $DB_VERSION_LAST_DATE;
        $arr['SystemVersionLast'] = $SYS_VERSION_LAST;
        $arr['SystemVersionUrl'] = $SYS_VERSION_LAST_URL;
        $arr['SystemVersionDate'] = $SYS_VERSION_LAST_DATE;


        $arr['EnSystemVersionUrl'] = $sjbb['EnSystemVersionUrl'];
        $arr['EnVersionUrlMd5'] = $sjbb['EnVersionUrlMd5'];

        if ($params['MaxLineNum'] == 1) {

            if (!empty($sjbb['MaxLineNum'])) {
                $arr['MaxLineNum'] = $sjbb['MaxLineNum'];//数据库版本的MaxLineNum

            } else {
                $arr['MaxLineNum'] = 5;
            }


        }


        if ($SYS_VERSION_LAST > $AppVersionCurrent) {
            $arr['IsNewSystemVersion'] = "1";

        } else {
            $arr['IsNewSystemVersion'] = "0";
        }

        if ($DB_VERSION_LAST > $DbVersionCurrent) {
            $arr['IsNewDbVersion'] = "1";
        } else {
            $arr['IsNewDbVersion'] = "0";
        }

        //add by xr

        if (array_key_exists($GUID, $GLOBALS["testaccountarr"]) && $SignCS != '') {
            $user = $GLOBALS["testaccountuser"];
        } else {
            $user = $this->_dao->getUser($GUID, $SignCS, $mc_type);
        }
        if ($params['IsNeedUpdateDB'] == "1") {
            $systypesql = "and  SystemType='" . $params['SystemType'] . "'";
        }

        $ActionDate = $this->_dao->query("select ActionDate from app_logs  where MID='" . $user['Mid'] . "'and SignCS ='" . $SignCS . "' " . $systypesql . "   and  mc_type='$mc_type' order by ActionDate desc");
        if ($mc_type != 3) {
            $OpDate = $this->_dao->getone("select OpDate from app_buy_logs   where MID='" . $user['Mid'] . "'  and   mc_type='$mc_type' order by OpDate desc");
        } else {//新钢记录时间
            $OpDate = $this->_dao->getone("select create_time from dc_oplog   where userid='" . $user['Uid'] . "'  and optype=9 and  mc_type='$mc_type' order by 	create_time desc");

        }

        //登录记录的倒数第二条的时间对比
        if (isset($params['IsNeedUpdateDB'])) {
            if ($ActionDate[1]['ActionDate'] < $OpDate) {
                $arr['IsNeedUpdateDB'] = "1";
            } else {
                $arr['IsNeedUpdateDB'] = "0";
            }
        }

        if ($mc_type == 3 && array_key_exists($GUID, $GLOBALS["testaccountarr"]) && $SignCS != '') {//新钢测试账号强制更新
            $arr['IsNeedUpdateDB'] = "1";
        }

        if ($params['debug'] == 2) {
            echo $arr['IsNeedUpdateDB'];
            echo '<br>';
            echo "select OpDate from app_buy_logs   where MID='" . $user['Mid'] . "'  and   mc_type='$mc_type' order by OpDate desc";
            echo '<br>';
            echo "select ActionDate from app_logs  where MID='" . $user['Mid'] . "'and SignCS ='" . $SignCS . "' " . $systypesql . "   and  mc_type='$mc_type' order by ActionDate desc";
            echo '<br>';
            echo "SELECT * FROM app_session_temp WHERE GUID = '$GUID' AND mc_type='$mc_type'";
            echo '<br>';
            print_r($user);
            echo '<br>';
            echo 'app_logs:' . $ActionDate[1]['ActionDate'];
            echo '<br>';
            echo 'app_buy_logs:' . $OpDate;
            echo '<br>';

        }
        if ($params['IsNeedUpdateDB'] == 1) {
            $arr['AndroidSystemVersionUrl'] = $sjbb['AndroidSystemVersionUrl'];
            $arr['AndroidSystemVersionUrlMd5'] = $sjbb['AndroidSystemVersionUrlMd5'];
            $arr['IOSSystemVersionUrl'] = $sjbb['IOSSystemVersionUrl'];
            $arr['IOSSystemVersionUrlMd5'] = $sjbb['IOSSystemVersionUrlMd5'];
            $arr['SystemVersionUrlMd5'] = $sjbb['DownloadUrlMd5'];


            /*

			function split2($n) {
				$n |= 0;
				//echo $n>>1;
				$pad = 0;
				$arr = array();
				while ($n) {
					if ($n & 1) array_push($arr, 1 << $pad);
					//echo $n;
					$pad++;
					$n >>= 1;
				}
				return $arr;
			}

			//获取品种
			$pz = $this->_dao->getOne("select pinzhong from app_license where MID = '".$user['Mid']."' and Status=1 and mc_type='".$mc_type."' limit 1 ",0);
			if($pz=='8192'){
				$pz='allpz';
			}else{
				foreach( split2($pz) as $k=>$v){
					if($k==0){
						$pz=$v;
					}else{
						$pz=$pz.'|'.$v;
					}
				}

			}

			//orderlist：读取dc_custom_order表 根据Mid和mc_type查询
			$orderlist =$this->_dao->query("select app_license.ID,app_license.MID,DataType,BuyType,Syear,Eyear from dc_custom_order inner join app_license on dc_custom_order.Mid = app_license.Mid and app_license.mc_type='$mc_type' and dc_custom_order.mc_type='$mc_type' where dc_custom_order.MID='".$user['Mid']."' AND app_license.StartDate<NOW() AND app_license.EndDate>now()");
			$ids=$this->_dao->getOnes("select ID from dc_code_class where scode in ('A','B') and mc_type='$mc_type'");
			$isyx = $this->_dao->getRow("select * from app_license where MID = '".$user['Mid']."' AND app_license.StartDate<NOW() AND app_license.EndDate>now() ");
			if($isyx){
				$ids=$this->_dao->getOnes("select ID from dc_code_class where scode in ('A','B') and mc_type='$mc_type'");
				foreach($ids as $key=>$val) {
					$one='';
					$one['ID']=0;
					$one['MID']=1;
					$one['DataType']=$val;
					$one['BuyType']=2;
					$one['Syear']='2014';
					$one['Eyear']='2019';
					$orderlist[]=$one;
				}
			}


			foreach($orderlist as $key=>$val) {
				$scode=$this->_dao->getOne("select scode from dc_code_class where ID='".$val['DataType']."'");
				$orderlist[$key]['scode']=$scode;
				$orderlist[$key]['level']=$this->getcurrentCengshu( $val['scode'],$num=1);


			}

			//去除重复数据，id=0的去除
			foreach($orderlist as $key1=>$val1){
				foreach($orderlist as $key=>$val){
					if($val['DataType']==$val1['DataType']&&$val['ID']!=$val1['ID']){
						if($val['ID']=='0'){
							unset($orderlist[$key]);
						}
						if($val1['ID']=='0'){
							unset($orderlist[$key1]);
						}
					}

				}
			}

			$arr['pinzhong']=$pz;
			$arr['orderlist']=array_values($orderlist);*/
        }


        //end

        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    public function BizPublish($params)
    {

        $SignCS = $params['SignCS'];
        $GUID = $params['GUID'];
        $ChCode = $params['ChCode'];
        $MID = $params['MID'];
        $IsZb = $params['IsZb'];
        $ZbDesc = $params['ZbDesc'];
        $ZbCitys = $params['ZbCitys'];
        $Resources = $params['Resources'];

        if (!$this->checkGUID($params)) {
            return;
        }
        $arr = array(
            'Success' => '0',
            'Message' => base64_encode('发布失败'),
        );

        //供求专版
        $IsZb = $params["IsZb"];
        $ZbCitys = $params["ZbCitys"];
        $ZbDesc = $params["ZbDesc"];

        $zbtitle = $params["zbtitle"];
        $user = $this->_dao->getUser($GUID, $SignCS, $mc_type);
        $uid = $user["Uid"];
        $citystr = implode(",", $ZbCitys);

        $this->stedao->create_reszbtemp($MID);

        foreach ($params['Resources'] as $array_temp) {
            $this->stedao->insert_reszbtemp($MID, $array_temp);
        }
        //操作日志
        $bgycg = $params["bgycg"]; //资源类型,1供应,2采购
        $remoteaddr = $params["remoteaddr"];

        $divall = $params["divall"];
        $dataids = $params["dataids"];

        $title = $params["title"];
        $deleall = $params["deleall"];
        $mright = $params["mright"];
        $total = count($params['Resources']); //资源信息数量

        $array_temp_zb['Title'] = $title;
        $array_temp_zb['Messagenumb'] = $total;
        $array_temp_zb['Settop'] = 0;
        $array_temp_zb['Varietyid'] = "";
        $array_temp_zb['Cityid'] = "";
        $array_temp_zb['Jj'] = "";
        $array_temp_zb['Isbxg'] = "";
        $array_temp_zb['Messagezbid'] = 0;
        $array_temp_zb['Bwherefrom'] = 0;
        $array_temp_zb['dataids'] = 0;
        $array_temp_zb['bonline'] = $bonline;
        $array_temp_zb['citystr'] = $citystr;
        $array_temp_zb['info'] = $info;
        $array_temp_zb['zbtitle'] = $zbtitle;
        $array_temp_zb['uid'] = $uid;
        $array_temp_zb['remoteaddr'] = $remoteaddr;
        $array_temp_zb['userid'] = $uid;

        $this->stedao->create_memberzbtemp($MID);
        $this->stedao->insert_memberzbtemp($MID, $array_temp_zb);
        $arr = array(
            'Success' => '1',
            'Message' => base64_encode('发布成功'),
            'memberzbid' => 0,
        );
        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    public function BizGet($params)
    {

        $SignCS = $params['SignCS'];
        $GUID = $params['GUID'];
        $ChCode = $params['ChCode'];
        $MID = $params['MID'];
        $ZbDesc = $params['ZbDesc'];
        $ZbCitys = $params['ZbCitys'];

        if (!$this->checkGUID($params)) {
            return;
        }
        $arr = array(
            'Success' => '0',
            'Message' => base64_encode('失败'),
        );
        $data = $this->stedao->query("SELECT id as resid,lb,pz,pm,cz,gg,jg,sl,cd,jhdd,jhck,effect_date,res_type,res_ch,istjzy,isxh,bz,jz,bmzk,rcl,remark,memberzbid FROM reszb WHERE memberid ='$MID'");
        foreach ($data as &$tmp) {
            $tmp['pm'] = base64_encode($tmp['pm']);
            $tmp['cz'] = base64_encode($tmp['cz']);
            $tmp['gg'] = base64_encode($tmp['gg']);
            $tmp['jg'] = base64_encode($tmp['jg']);
            $tmp['cd'] = base64_encode($tmp['cd']);
            $tmp['jhdd'] = base64_encode($tmp['jhdd']);
            $tmp['jhck'] = base64_encode($tmp['jhck']);
            $tmp['bz'] = base64_encode($tmp['bz']);
            $tmp['bmzk'] = base64_encode($tmp['bmzk']);
            $tmp['rcl'] = base64_encode($tmp['rcl']);
            $tmp['remark'] = base64_encode($tmp['remark']);
        }
        $arr = array(
            'Success' => '1',
            'Message' => base64_encode('成功'),
            'memberzbid' => 0,
            'Results' => $data
        );

        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    public function ShowAbout($params)
    {

        $SignCS = $params['SignCS'];
        $GUID = $params['GUID'];

        if (!$this->checkGUID($params)) {
            return;
        }
        $user = $this->_dao->getUser($GUID, $SignCS, $mc_type);
        $Mid = $user['Mid'];
        $contenthtml = file_get_contents("about.html");
        $contenthtml = base64_encode($contenthtml);

        $contenthtmlfee = file_get_contents("fee.html");
        $contenthtmlfee = base64_encode($contenthtmlfee);

        $arr = array(
            'Success' => '1',
            'Message' => $contenthtml,
            'MessageFee' => $contenthtmlfee,
            'USID' => $user['SignCS'],
            'MobileNumber' => $user['MobileNumber'],
            'ExpiredDate' => date("Y-m-d", strtotime($user['ExpiredDate'])),
        );
        if ($Mid > 1) {
            $adminuser = $this->stedao->getAdminUser($Mid);
            if ($adminuser) {
                $arr['AdminName'] = base64_encode($adminuser['truename']);
                $arr['AdminPhone'] = $adminuser['tel'];
                $arr['AdminMobile'] = $adminuser['mobil'];
            } else {
                $arr['AdminName'] = "";
                $arr['AdminPhone'] = "";
                $arr['AdminMobile'] = "";
            }
        } else {
            $arr['AdminName'] = "";
            $arr['AdminPhone'] = "";
            $arr['AdminMobile'] = "";
        }
        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    public function GetMobileCheckedCode($params)
    {

        $SystemType = $params['SystemType'];
        $MobileNumber = $params['MobileNumber'];
        $SignCS = $params['SignCS'];
        $CheckedCode = rand(100000, 999999);
        $Cudate = date("Y-m-d H:i:s");//当前日期
        $ExpiredDate = date("Y-m-d H:i:s", strtotime("+ 10 min")); //10分钟
        $ExpiredDateDay1 = date("Y-m-d H:i:s", strtotime("- 1 day"));
        $ExpiredDateDay2 = date("Y-m-d H:i:s", strtotime("- 10 day"));

        if ($MobileNumber == "" || preg_match("/^[0-9]{11}$/", $MobileNumber) == 0) {
            $arr = array(
                'Success' => '0',
                'Message' => base64_encode("你的手机号不符合规则。"),
                'ErrorPhoneNumber' => '***********',
                'Results' => ''
            );
        }
        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    public function checkGUID($params)
    {
        $GUID = $params['GUID'];
        $SignCS = $params['SignCS'];
        $action = $params['action'];        //接口名称
        if (!empty($params['mc_type'])) $mc_type = $params['mc_type'];
        else $mc_type = 0;

        if (array_key_exists($GUID, $GLOBALS["testaccountarr"]) && $SignCS != '') {
            $user = $GLOBALS["testaccountuser"];
        } else {
            $user = $this->_dao->getUser($GUID, $SignCS, $mc_type);
        }

        $arrtem = $params;
        //array_pop($arrtem);
        foreach ($arrtem as $k => $v) {
            $actionstr .= "&" . $k . "=" . $v;
        }

        if (!empty($user)) {
            //最后活动
            $this->_dao->execute("UPDATE app_session_temp SET LastDate=NOW(), LastAction='$action' WHERE GUID='$GUID' and mc_type='$mc_type' limit 1");
            //日志处理
            $this->_dao->WriteLog($user['Mid'], $user['Uid'], $SignCS, $action, $actionstr, $this->getIP(), $this->getSystemType(), $this->getSystemVersion(), "checkGUID", '', '', $mc_type);
            return true;
        } else {
            $arr = array(
                'Success' => '0',
                'Message' => base64_encode('非法操作'),
            );
            if ($isEnglish) $arr['Message'] = base64_encode('Illegal Operation');
            $json_string = $this->pri_JSON($arr);
            echo $json_string;
            return false;
        }
    }


     /**
     * 注销账号接口
     * Created by std.
     * Date:2025/2/20 14:06
     */
    public function loginOff($params){
        $GUID = $params["GUID"];
        $SignCS = $params['SignCS'];
        $mc_type =  $params['mc_type'];
        $SystemType =  $params['SystemType'];
        $SystemVersion =  $params['SystemVersion'];
        $MobileNumber = $params['MobileNumber'];
        $CheckedCode = $params['CheckedCode'];
        //获取用户信息
        $user = $this->_dao->getUser($GUID,$SignCS,$mc_type);
        $Uid = $user['Uid'];
        $Mid = $user['Mid'];
        //获取手机信息
        $LoginIp = $this->getIP();
		if($user){
            $sysdao=new SystemDao('91W','91R') ;
			$sysdao->logout_adminuser($Uid);

            //删除临时表记录
            $this->_dao->execute( "DELETE FROM app_session_temp WHERE GUID='$GUID' and mc_type='$mc_type' " );
            //日志处理
            $this->_dao->WriteLog($Mid, $Uid, $SignCS, "LoginOutUser",'账号注销', $LoginIp, $SystemType, $SystemVersion,'','','',$mc_type);
		}
        $arr = array(
            'Success' => '1',
            'Message' => base64_encode('注销成功')
        );
        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }


    public function checkgx($params)
    {//检查更新，目前仅限钢之家数据，以后可在基础上修改
        //1.找到循环目录->循环所有的数据类型->找到对应的子数据类型
        //2.根据子数据类型中的id号（注意6位、7位id对应的字段不同），找到最新的一条信息，取 品名+产地+材质+备注信息
        //3.判断当前信息（t1数据库）是否与查询到的信息相同，不同则更新，设标志为1
        //4.根据标志判断是否需要修改版本号，更新到数据库

        $mcodes = array("B09", "B13");//目录代码
        $num = array('2', '2');        //目录层级
        $mc_type = 0;

        $where = "( ";
        foreach ($mcodes as $k => $mcode) {
            if (strlen($where) < 10) $where .= "scode" . $num[$k] . "='$mcode'";
            else $where .= " or scode" . $num[$k] . "='$mcode'";
        }
        $where .= " )";

        $disids = array("R26350", "T36319", "826315");

        $mark = 0;//设定初始标识，用于验证后面是否需要修改版本号
        if ($params['debug']) define("ISTEST", $params['debug']);
        else define("ISTEST", 0);
        $topictureids = array();
        $dids = $this->_dao->getOnes("select id from dc_code_datatype where $where and mc_type='$mc_type'");

        foreach ($dids as $did) {
            $ddata = $this->_dao->query("select id,sname,scode,satt1,satt2,satt3 from dc_code_datatype_subs where did='$did' and status=1 and mc_type='$mc_type'");
            foreach ($ddata as $data) {
                $cxid = $data['scode'];
                $cxname = "";
                if (strlen($cxid) == 6) $cxname = "topicture";
                if (strlen($cxid) == 7) $cxname = "mastertopid";
                if (empty($cxname) || in_array($cxid, $disids)) continue;
                $topictureids[$cxid] = 1;

                $cxsql = "select articlename,factoryarea,material,marketremarks from marketconditions where $cxname='$cxid' order by id desc limit 1";
                //echo $cxsql."<br>";
                $updata = $this->stedao->getrow($cxsql);// and mconmanagedate>'".date("Y-m-d")."'

                $nname = $updata["articlename"] . "-" . $updata["factoryarea"] . (empty($updata["marketremarks"]) ? "" : "(" . $updata["marketremarks"] . ")");
                $pinzhong = $updata["material"];

                if (!empty($updata['material']) && ($data['satt3'] != "" || $data['satt2'] != "" || $data['sname'] != $nname || $data['satt1'] != $pinzhong)) {
                    $updatesql = "update dc_code_datatype_subs set sname='$nname',satt1='$pinzhong',satt2='',satt3='' where id='".$data['id']."' and mc_type='$mc_type' limit 1";
                    if (ISTEST == 0) {
                        $this->_dao->execute($updatesql);
                        echo $updatesql."<br>";
                    }else {
                        echo $updatesql . "<br>";
                    }
                    $mark = 1;
                } else {

                }
            }
        }
        if ($mark == 1) {
            $version = $this->_dao->getone("select VersionNo from app_version where VersionType=2 and Status=1 and mc_type='$mc_type' limit 1");
            $newversion = $version + 0.01;
            $updateversionsql = "update app_version set VersionNo=$newversion where VersionType=2 and Status=1 and mc_type='$mc_type' limit 1";
            //if(ISTEST==0) $this->_dao->execute($updateversionsql);
            //else echo "\n update version sql:".$updateversionsql."<br>";
        }
        echo "ok<br>";
        if ($params['show'] == 1) {
            echo "<pre>";
            echo count($topictureids);
            print_r($topictureids);
            echo "</pre>";
        }
    }

    public function ErrorSubmit($params)
    {
        $GUID = $params["GUID"];
        $SignCS = $params["SignCS"];
        $mc_type = $params["mc_type"] ? $params["mc_type"] : "0";
        $user = $this->_dao->getUser($GUID, $SignCS, $mc_type);

        $notallowed = array("view", "GUID", "SignCS", "mc_type");
        $myparam = "";
        foreach ($params as $k => $v) {
            if (in_array($k, $notallowed) == false) {
                $k = $this->strconvert($k, "GBK");
                $v = $this->strconvert($v, "GBK");
                $myparam .= "&$k=$v";
            }
        }
        $baseurl = APP_URL_WWW;
        // if (strstr($_SERVER["SERVER_NAME"], "iwww")) $baseurl = "iwww2.steelhome.cn";
        // else $baseurl = "www.steelhome.cn";
        $url = $baseurl . "/jiucuo_dc.php?userid=$user[Uid]" . $myparam;
        //echo "<pre>";print_r($user);print_r($params);print_r($url);echo "</pre>";exit;
        header("Location:" . $url);
    }

    function strconvert($data, $code = 'UTF-8')
    {
        if (!empty($data)) {
            $fileType = mb_detect_encoding($data, array('UTF-8', 'GBK', 'LATIN1', 'BIG5'));
            if ($fileType != $code) {
                $data = mb_convert_encoding($data, $code, $fileType);
            }
        }
        return $data;
    }

    public function getcurrentCengshu($scode, $num, $mc_type)
    {//当前层数

        $scode1 = $this->_dao->getone("select mcode  from dc_code_class where scode='" . $scode . "' and mc_type='" . $mc_type . "'");

        if (!empty($scode1)) {
            $num = $num + 1;
            $num = $this->getcurrentCengshu($scode1, $num, $mc_type);
        }

        return $num;

    }


    public function getcurrentCengshu2($scode)
    {//当前层数

        $slen = strlen($scode);
        if ($slen == 1 || $slen == 2) $num = 1;
        else $num = 2;


        return $num;

    }


    public function GetLoginAesKey($params)
    {
        $retarr['Success'] = "0";
        $retarr['Message'] = base64_encode("失败");
        $SignCS = $params['SignCS'];
        $UserName = $params['UserName'];
        $AesKey = $this->random(16);

        $memcache = new Memcache;
        $memcache->connect(MEMCACHE_SERVER, MEMCACHE_PORT);
        $expire = 120;
        // echo $SignCS.$UserName.'-----'.$AesKey;
        $memcache->set($SignCS . $UserName, $AesKey, MEMCACHE_COMPRESSED, $expire);
        $memcache->close();
        // file_put_contents("/tmp/abc.txt",$SignCS.$UserName.$AesKey.print_r($params,true)."\n",FILE_APPEND);

        $retarr['Success'] = "1";
        $retarr['Message'] = base64_encode("成功");
        $retarr['AesKey'] = $AesKey;
        $json_string = $this->pri_JSON($retarr);
        echo $json_string;


    }

    function random($length)
    {
        srand(date("s"));
        $possible_charactors = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
        $string = "";
        while (strlen($string) < $length) {
            $string .= substr($possible_charactors, (rand() % (strlen($possible_charactors))), 1);
        }
        return ($string);
    }


    /**
     * 加密方法
     * @param string $str
     * @return string
     */
    function encrypt($str)
    {
        //AES, 128 ECB模式加密数据
        $str = $this->addPKCS7Padding($str);
        // $iv = mcrypt_create_iv(mcrypt_get_iv_size(MCRYPT_RIJNDAEL_128, MCRYPT_MODE_CBC), MCRYPT_RAND);
        // define('A', $iv);

        // $encrypt_str = mcrypt_encrypt(MCRYPT_RIJNDAEL_128, SECRETKEY, $str, MCRYPT_MODE_CBC, $iv);
        //$encrypt_str = mcrypt_encrypt(MCRYPT_RIJNDAEL_128, SECRETKEY, $str, MCRYPT_MODE_ECB);
        $tag = null;
        $encrypt_str = openssl_encrypt($str, 'aes-128-ecb', SECRETKEY, $options = 2, $iv = "", $tag, $aad = "", $tag_length = 16);
        return base64_encode($encrypt_str);
    }

    /**
     * 解密方法
     * @param string $str
     * @return string
     */
    function decrypt($str, $AesKey)
    {
        //AES, 128 ECB模式加密数据

        //AES, 128 ECB模式加密数据
        // $encrypt_str = mcrypt_decrypt(MCRYPT_RIJNDAEL_128, SECRETKEY, $str, MCRYPT_MODE_CBC,A);
        $encrypt_str = openssl_decrypt($str, 'aes-128-ecb', $AesKey, $options = 2, $iv = "", $tag = "", $aad = "");

        //$encrypt_str = mcrypt_decrypt(MCRYPT_RIJNDAEL_128,  $AesKey, $str, MCRYPT_MODE_ECB);
        $encrypt_str = $this->stripPKSC7Padding($encrypt_str);

        //print_r( $enctypt_str);
        return $encrypt_str;


        return $encrypt_str;
    }

    /**
     * 填充算法
     * @param string $source
     * @return string
     */
    function addPKCS7Padding($source)
    {
        $source = trim($source);
        $block = mcrypt_get_block_size('rijndael-128', 'ecb');
        $pad = $block - (strlen($source) % $block);
        if ($pad <= $block) {
            $char = chr($pad);
            $source .= str_repeat($char, $pad);
        }
        return $source;
    }

    /**
     * 移去填充算法
     * @param string $source
     * @return string
     */
    function stripPKSC7Padding($source)
    {
        $char = substr($source, -1);
        $num = ord($char);
        $source = substr($source, 0, -$num);
        return $source;
    }

    /**
     * 获取signcs
     * @param string $source
     * @return string
     */
    function GetSignCS($params)
    {
        $username = $params["UserName"];
        if ($username != '') {
            $sql = "SELECT SignCS	FROM `app_license_detail` WHERE `UseUser` = '" . $username . "' AND `mc_type` =2  and SignCS!='' ORDER BY `app_license_detail`.`UserDate` DESC LIMIT 1";
            $SignCS = $this->_dao->getone($sql);
            if ($SignCS == '') {
                $SignCS = 'tmpsigncs_' . $username;
            }
        }
        echo $SignCS;
    }

    function encrypt2($str, $SECRETKEY)
    {
        //AES, 128 ECB模式加密数据
        $str = $this->addPKCS7Padding($str);
        $encrypt_str = mcrypt_encrypt(MCRYPT_RIJNDAEL_128, $SECRETKEY, $str, MCRYPT_MODE_ECB);
        return base64_encode($encrypt_str);
    }

    function GetSignCSencrypt($params)
    {
        $AesKey = $params["key"];
        $password = $params["PassWord"];
        $string = $this->encrypt2($password, $AesKey);
        echo $string;
    }

    public function makelicense()
    {
        // 前5位
        $firse = rand(10000, 99999);
        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $second = "";
        for ($i = 0; $i < 3; $i++) {
            $second .= $chars[mt_rand(0, strlen($chars) - 1)];
        }
        $three = rand(1000000, 9999999);
        $foue = rand(10000, 99999);
        $li = $firse . "-" . $second . "-" . $three . "-" . $foue;
        $t1 = $this->_dao->getRow("select * from app_license where LicenseKeyMain = '$li' ");
        $t2 = $this->_dao->getRow("select * from app_license_detail where LicenseKeyD = '$li' ");
        if ($t1 || $t2) {
            $this->makelicense();
        } else {
            return $li;
        }
    }

    public function utf8ToGb2312($str)
    {
        if (mb_detect_encoding($str, "UTF-8, ISO-8859-1, GBK") == "UTF-8") {//判断是否不是UTF-8编码，如果不是UTF-8编码，则转换为UTF-8编码
            return iconv("utf-8", "gb2312", $str);
        } else {
            return $str;
        }
    }
	
	public function GUIDLogin($params){
		
		$LoginType=$params['LoginType'];
		$GUID=$params['GUID'];
		$UserName=$params['UserName'];
		$LoginDate=$params['LoginDate'];
		$SignCS=$params['SignCS'];
		$LicenseSign=$params['LicenseSign'];
		$SystemType=$params['SystemType'];
		$SystemTypeString=$params['SystemTypeString'];
		$SystemVersion=$params['SystemVersion'];
		$quanxian=$params['quanxian'];
		$IsEncrypt=$params['IsEncrypt'];
		
		$LoginIp = $this->getIP();		
		if(empty($params['mc_type'])) $mc_type=0;
		else $mc_type=$params['mc_type'];
		
		if(empty($params['isEnglish'])) $isEnglish=0;
		else $isEnglish=$params['isEnglish'];
		
		$actionstr = "";
		foreach($params as $k=>$v){
			$actionstr.="&".$k."=".$v;
		}

		function split2($n) {
			$n |= 0;
			$pad = 0;
			$arr = array();
			while ($n) {
				if ($n & 1) array_push($arr, 1 << $pad);
				$pad++;
				$n >>= 1;
			}
			return $arr;
		}
		
		foreach($GLOBALS["testaccountarr"] as $na=>$pa){
			if($GUID==$na){
				if(!(in_array($LoginIp,$GLOBALS["testip"])||$GLOBALS["nowtest"]==1)){
					$arr = array(
						'Success'=>'0',
						'ErrorType'=>'-1',
						'Message'=>base64_encode($this->utf8ToGb2312('非法登录')),
					);
					$json_string = $this->pri_JSON($arr);
					echo $json_string;
					exit;
				}
				//测试账号记录
				$this->_dao->WriteLog('1','1', $SignCS, "Login",$actionstr, $LoginIp, $SystemType, $SystemVersion,"网页跳转客户端登录",'','',$mc_type);
			
				if($mc_type==3&&$SystemType!='NewPC'){
					// 铁前成本 C 钢轧成本 F 利润汇总 G 销售定价 N  管理看板 K  数据录入Q    绩效效益预测  J 研究报告 L 原料性价比 K 期货套保 O  接单效益 P 经营分析  R 管理看板查看 S
					$xgquanxian="C|F|G|N|J|L|O|P|K|Q|R|S";
				}elseif($mc_type==3&&$SystemType=='NewPC'){
					$xgquanxian="C|F|G|J|N|P|K|Q|O|L|R|S";
				}else{
					$xgquanxian="";
				}
			
				if($mc_type==2)
					$dpshow =1;
				else
					$dpshow="";

				if($quanxian==1){				
					if($params['dzquanxian']==1){ 
						$alldz=$this->_dao->query("select ID,pinzhong from dc_code_datatype where scode2='B25' and mc_type='$mc_type'");						 
						$dzqxstr='';
					   
						foreach ($alldz as $k_lid => $k_v) {
							$dzpz='';
							$dzpz=split2($k_v['pinzhong']);							
							foreach ($dzpz as $dzk => $dzv) 
							{
								if($dzv=='8388608')
								{//匹配定制权限，匹配到的拼接dc_code_datatype 的id								   
									if( $dzqxstr=='')
										$dzqxstr=$k_v['ID'];
									else
									   $dzqxstr=$dzqxstr.','.$k_v['ID'];
								}
							}
						}
					}
			 
					$pz = $this->_dao->getOne("select pinzhong from app_license where MID = '1' and Status=1 and mc_type='".$mc_type."' limit 1 ",0);
				
					if($pz=='8192'||(array_key_exists($params['UserName'],$GLOBALS["testaccountarr"])&&$SignCS!=''))
						$pz='allpz';
					else
					{
						foreach( split2($pz) as $k=>$v){
							if($k==0)
								$pz=$v;
							else
								$pz=$pz.'|'.$v;
						}						
					}
					$orderlist =$this->_dao->query("select app_license.ID,app_license.MID,DataType,BuyType,Syear,Eyear from dc_custom_order inner join app_license on dc_custom_order.Mid = app_license.Mid and app_license.mc_type='$mc_type' and dc_custom_order.mc_type='$mc_type' where dc_custom_order.MID='1' AND app_license.StartDate<NOW() AND app_license.EndDate>now()");				
					$isyx = $this->_dao->getRow("select * from app_license where MID = '1' AND app_license.StartDate<NOW() AND app_license.EndDate>now() ");
				
					if($isyx){					
						$ids=$this->_dao->getOnes("select ID from dc_code_class where mcode='' and mc_type='$mc_type'");
						foreach($ids as $key=>$val) {
							$one=array();
							$one['ID']=0;
							$one['MID']=1;
							$one['DataType']=$val;
							$one['BuyType']=2;
							$one['Syear']='2014';
							$one['Eyear']='2019';
							$orderlist[]=$one;
						}
					}
					
					foreach($orderlist as $key=>$val) {
						$idstr.=$val['DataType'].",";
					}
					$idstr=rtrim($idstr, ",");
					if($idstr)
						$scodelist=$this->_dao->query("select id,scode from dc_code_class where ID in(".$idstr.") and mc_type='$mc_type';");
					
					foreach($scodelist as $key=>$val) {
						$scodelist2[$val['id']]=$val['scode'];
					}
					
					foreach($orderlist as $key=>$val) {
						$scode=$scodelist2[$val['DataType']];
						$orderlist[$key]['scode']=$scode;
						$orderlist[$key]['level']=$this->getcurrentCengshu2( $scode);
					}				
					
					//去除重复数据，id=0的去除
					foreach($orderlist as $key1=>$val1){
						foreach($orderlist as $key=>$val){
							if($val['DataType']==$val1['DataType']&&$val['ID']!=$val1['ID']){	
								if($val['ID']=='0'){
									unset($orderlist[$key]);
								}
								if($val1['ID']=='0'){
									unset($orderlist[$key1]);
								}
							}								
						}
					}

					if($params['dzquanxian']==1){//定制权限字段存在返回，不存在不返回
						$arr = array(
							'Success'=>'1',
							'GUID' => $GUID,
							'Message'=>base64_encode($this->utf8ToGb2312('登录成功')),
							'InterFaceUrl'=>'',
							'Result'=>array(
								'Uid'=>$GLOBALS["testaccountuser"]["Uid"],
								'UserName'=>$na,
								'TrueName'=>base64_encode($this->utf8ToGb2312("测试账号_".$na)),
								'MobileNumber'=>"",
								'Mid'=>$GLOBALS["testaccountuser"]["Mid"],
								'ComName'=>'',
								'UserLevel'=>'',
								'ExpiredDate'=>'2040-01-01',
								'SmsExpiredDate'=>'2040-01-01',
								'AdminName'=>'',
								'AdminPhone'=>'',
								'AdminMobile'=>'',
								'IsUpdate'=>1,
								'SQLitePass'=>$this->_dao->getone("select SQLitePass from app_license where mc_type='$mc_type' and mid=1 limit 1"),
								'dzquanxian'=>$dzqxstr,
								'pinzhong'=>$pz,
								'orderlist'=> array_values($orderlist),
								'xgquanxian'=>$xgquanxian,
								'dpshow'=>$dpshow,
								'cronconfig'=>'',
							)
						);
					}else{
						$arr = array(
							'Success'=>'1',
							'GUID' => $GUID,
							'Message'=>base64_encode($this->utf8ToGb2312('登录成功')),
							'InterFaceUrl'=>'',
							'Result'=>array(
								'Uid'=>$GLOBALS["testaccountuser"]["Uid"],
								'UserName'=>$na,
								'TrueName'=>base64_encode($this->utf8ToGb2312("测试账号_".$na)),
								'MobileNumber'=>"",
								'Mid'=>$GLOBALS["testaccountuser"]["Mid"],
								'ComName'=>'',
								'UserLevel'=>'',
								'ExpiredDate'=>'2040-01-01',
								'SmsExpiredDate'=>'2040-01-01',
								'AdminName'=>'',
								'AdminPhone'=>'',
								'AdminMobile'=>'',
								'IsUpdate'=>1,
								'SQLitePass'=>$this->_dao->getone("select SQLitePass from app_license where mc_type='$mc_type' and mid=1 limit 1"),								
								'pinzhong'=>$pz,
								'orderlist'=> array_values($orderlist),
								'xgquanxian'=>$xgquanxian,
								'dpshow'=>$dpshow,
								'cronconfig'=>'',
							)
						);
					}
				}
				else
				{
					$arr = array(
						'Success'=>'1',
						'GUID' => $GUID,
						'Message'=>base64_encode($this->utf8ToGb2312('登录成功')),
						'InterFaceUrl'=>'',
						'Result'=>array(
							'Uid'=>$GLOBALS["testaccountuser"]["Uid"],
							'UserName'=>$na,
							'TrueName'=>base64_encode($this->utf8ToGb2312("测试账号_".$na)),
							'MobileNumber'=>"",
							'Mid'=>$GLOBALS["testaccountuser"]["Mid"],
							'ComName'=>'',
							'UserLevel'=>'',
							'ExpiredDate'=>'2040-01-01',
							'SmsExpiredDate'=>'2040-01-01',
							'AdminName'=>'',
							'AdminPhone'=>'',
							'AdminMobile'=>'',
							'IsUpdate'=>1,
							'SQLitePass'=>$this->_dao->getone("select SQLitePass from app_license where mc_type='$mc_type' and mid=1 limit 1")							
						)
					);
				}
				
				//登录成功,session更新
				$this->_dao->execute( "REPLACE INTO app_session_temp SET GUID='$GUID', SignCS='$SignCS', Mid='".$GLOBALS["testaccountuser"]["Mid"]."', Uid='".$GLOBALS["testaccountuser"]["Uid"]."', ComName='', UserName='$na',TrueName='"."测试账号_".$na."', MobileNumber='', ExpiredDate='2040-01-01', LoginDate=NOW(), LoginIp='$LoginIp', SystemType='$SystemType',SystemVersion='$date', LastDate=NOW(), LastAction='Login',mc_type='$mc_type'" );
				//登录成功推送
				if($mc_type==3){//新钢推送
					$deviceType='';
					if($SystemType=='NewAndroidPhone'){
						$deviceType='17';
					}
					if($SystemType=='NewAndroidPad'){
						$deviceType='18';
					}
					if($SystemType=='NewiPad'){
						$deviceType='20';
					}
					if($SystemType=='NewiPhone'){
						$deviceType='19';
					}
					
					$ret = file_get_contents(STEELHOME_URL."cron_97_system/xingePush/xingePush.php?deviceType=".$deviceType."&pushType=bind&tags=".$GLOBALS["testaccountuser"]["Uid"]."&token=".$params['Token']);
					$ret = json_decode($ret,true);
				}

				if($mc_type==2)//陕钢
				{
					$SGLoginURL=SGDCURL.'/web/systemapi.php?action=gzjSysLogin';
					$logininfo['username']='testuser';
					$logininfo['pwd']='123456';
					$logininfo['mc_type']='2';
					$mess=$this->http_post( $SGLoginURL,  http_build_query($logininfo));
					$content=$mess['content'];
					$token=json_decode($this->clearBom($content),true);
					
					$token=$token['Token'];
					$SGURL=SGDCURL.'/web/systemapi.php?action=syncdb&mc_type=2';

					$tbinfo['tablename']='app_session_temp';
					$tbinfo['type']='replace';
					$tbinfo['pk']='GUID';
					$tbinfo['data'][]=array(
						'GUID'=>$GUID,
						'SignCS'=>$SignCS,
						'Mid'=>$GLOBALS["testaccountuser"]["Mid"],
						'Uid'=>$GLOBALS["testaccountuser"]["Uid"],
						'ComName'=>'',
						'UserName'=>$this->strconvert($na),
						'TrueName'=>$this->strconvert("测试账号_".$na),
						'MobileNumber'=>'',
						'ExpiredDate'=>'2040-01-01',
						'LoginDate'=>date( "Y-m-d H:i:s" ),					 
						'LoginIp'=>$LoginIp,
						'SystemType'=>$SystemType,
						'SystemVersion'=>$date,
						'LastDate'=>date( "Y-m-d H:i:s" ),				 
						'LastAction'=>'Login',
						'mc_type'=>$mc_type,
						'isEnglish'=>$isEnglish
					);
					$tbinfo1[]=$tbinfo;
					$tjinfo['dtjson']=json_encode($tbinfo1);
					$tjinfo['token']=$token;

					$mess=$this->http_post( $SGURL,  urldecode(http_build_query($tjinfo)));					
				}									
				else if($mc_type==3 || $mc_type==4)//新钢
				{
					$XGLoginURL=XGDCURL.'/api/gzjSysLogin';
					$logininfo['username']='testuser';
					$logininfo['pwd']='123456';
					$logininfo['mc_type']=$mc_type;
					$mess=$this->http_post( $XGLoginURL,  http_build_query($logininfo));
					$content=$mess['content'];
					$token=json_decode($this->clearBom($content),true);
					
					$token=$token['Token'];
					$XGURL=XGDCURL.'/api/syncdb';

					$tbinfo['tablename']='app_session_temp';
					$tbinfo['type']='replace';
					$tbinfo['pk']='GUID';
					$tbinfo['data'][]=array(
						'GUID'=>$GUID,
						'SignCS'=>$SignCS,
						'Mid'=>$GLOBALS["testaccountuser"]["Mid"],
						'Uid'=>$GLOBALS["testaccountuser"]["Uid"],
						'ComName'=>'',
						'UserName'=>$this->strconvert($na),
						'TrueName'=>$this->strconvert("测试账号_".$na),
						'MobileNumber'=>'',
						'ExpiredDate'=>'2040-01-01',
						'LoginDate'=>date( "Y-m-d H:i:s" ),					 
						'LoginIp'=>$LoginIp,
						'SystemType'=>$SystemType,
						'SystemVersion'=>$date,
						'LastDate'=>date( "Y-m-d H:i:s" ),				 
						'LastAction'=>'Login',
						'mc_type'=>$mc_type,
						'isEnglish'=>$isEnglish
					);
					$tbinfo1[]=$tbinfo;
					$tjinfo['dtjson']=json_encode($tbinfo1);
					$tjinfo['token']=$token;
					$mess=$this->http_post( $XGURL,  urldecode(http_build_query($tjinfo)));
					
				}

				if($isEnglish) $arr['Message']=base64_encode('Success');
				$json_string = $this->pri_JSON($arr);
				echo $json_string;
				exit;
			}
		}
		$arr = array(
			'Success'=>'0',
			'ErrorType'=>'0',
			'Message'=>base64_encode($this->utf8ToGb2312('登录失败')),
		);
		if($isEnglish) $arr['Message']=base64_encode('Login Failed!');
			
		$user = $this->_dao->getRow("select * from app_session_temp where GUID='".$GUID."' and SignCS='".$SignCS."' and mc_type='".$mc_type."' and isEnglish='".$isEnglish."'");
		if(empty($user)){
			$arr['Success']='0';
			$arr['ErrorType']='1001';
			$arr['Message']= base64_encode($this->utf8ToGb2312('用户名或密码错误[1001]'));
			if($isEnglish)	$arr['Message']= base64_encode('Wrong Login ID or Password[1001]');
		}
		
		$istry=0;//是否试用账号
		if(!empty($user)){
			$mid=$user['Mid'];
			
			//钢之家数据中心试用权限
			$endDaterow=$this->_dao->getRow("select EndDate,istry from app_license where MID='".$mid."'  and mc_type='".$mc_type."' and status=1");
			if($mc_type!=0||$isEnglish!=0)
			{
				$endDate=$endDaterow['EndDate'];
				//$endDate=strtotime($endDate);
                $endDate = strtotime($endDate." 23:59:59");
				$nowDate=time();
				if($endDate<$nowDate){
					$arr2['Success']='0';
					$arr2['Message']= base64_encode($this->utf8ToGb2312('账号使用时间到期[1008]'));
					if($isEnglish) $arr2['Message']= base64_encode('Due Account[1008]');
					$arr2['ErrorType']='1008';
					$json_string = $this->pri_JSON($arr2);
					echo $json_string;
					exit;
				}
			}
			else//钢之家中文会员
			{
				$istry=$endDaterow['istry'];
				if($istry==0)
				{
					$sql="select istry from app_license_detail where mc_type='".$mc_type."' and UseUser='".$user['UserName']."' and IsEnglish=0 and Status ='1' order by UserDate desc ";
					$app_license_detail=$this->_dao->getrow($sql);
					if(!empty($app_license_detail))
					{
						if($app_license_detail['istry']!=1)
						{
							$endDate=$endDaterow['EndDate'];
							//$endDate=strtotime($endDate);
                            $endDate = strtotime($endDate." 23:59:59");
							$nowDate=time();
							if($endDate<$nowDate){
								$arr2['Success']='0';
								$arr2['Message']= base64_encode($this->utf8ToGb2312('账号使用时间到期[1008]'));
								if($isEnglish) $arr2['Message']= base64_encode('Due Account[1008]');
								$arr2['ErrorType']='1008';
								$json_string = $this->pri_JSON($arr2);
								echo $json_string;
								exit;
							}
						}
						else
						{
							$istry=1;
						}
					}
					else//账号不存在
					{
						$istry=1;
					}
				}
			}			
		}
		
		if(!empty($user)){
			if($isEnglish==0&&$IsEncrypt==1){//中文初始化
				$Soft_sign = $this->_dao->getRow("SELECT * FROM app_license WHERE MID='".$mid."' and Status=1 and mc_type='".$mc_type."'");
				$Soft_sign2 = $this->_dao->getRow("SELECT ID,SignCS,UseUser,ForceInit,ForceInitQuota,ForceInitQuotaUsed FROM app_license_detail WHERE LID='".$Soft_sign['ID']."' and UseUser='".$user['UserName']."' and SignCS='".$user['SignCS']."' and Status=1 and mc_type='".$mc_type."' ");
				$sysstr=iconv('GB2312','UTF-8',$SystemTypeString);
				$sysstr=$SystemTypeString;
				$is_tp=0;
				
				if(empty($Soft_sign2)){//没有绑定的子序列号，初始化绑定					
					 //优先取一个已经分配设备绑定
					 $empty_id = $this->_dao->getone("SELECT ID FROM app_license_detail  WHERE UseUser='".$user['UserName']."' and Status=1 and mc_type='".$mc_type."' and LID='".$Soft_sign['ID']."' and SignCS=''  order by  id desc limit 1");
					
					//有username的没有空设备，优先没有username的绑定
					if(empty($empty_id)){ 

						$empty_id = $this->_dao->getone("SELECT ID FROM app_license_detail  WHERE UseUser='' and Status=1 and mc_type='".$mc_type."' and LID='".$Soft_sign['ID']."' and SignCS='' order by  id desc limit 1 ");					
						if(empty($empty_id)){
							//没有空设备，修改一个有设备号的绑定
							$empty_id = $this->_dao->getone("SELECT ID FROM app_license_detail  WHERE UseUser='".$user['UserName']."' and Status=1 and LID='".$Soft_sign['ID']."' and mc_type='".$mc_type."' and SignCS!='' order by  id desc limit 1 ");
						
							if(!empty($empty_id)){
								//绑定成功
								$is_tp=1;
								$this->_dao->execute( "UPDATE app_license_detail set IsAutoBind=0 , SignCS='".$user['SignCS']."',SystemTypeString='".$sysstr."',UserDate=NOW()  WHERE ID='".$empty_id."'" );							   
							}
							else{
								$arr2['Success']='0';
								$arr2['Message']= base64_encode($this->utf8ToGb2312($this->utf8ToGb2312('初始化设备数量配额不足，初始化失败[1012]')));
								if($isEnglish) $arr2['Message']= base64_encode('Due Account[1012]');
								$arr2['ErrorType']='1012';
								$json_string = $this->pri_JSON($arr2);
								echo $json_string;
								exit;								
							}
						 }
						 else{ 
							$is_tp=1;
							//绑定成功
							$this->_dao->execute( "UPDATE app_license_detail set IsAutoBind=1,SignCS='".$user['SignCS']."',UseUser='".$user['UserName']."',SystemTypeString='".$sysstr."',UserDate=NOW()  WHERE ID='".$empty_id."'" );
						 }					
					}
					else{ 
						$is_tp=1;
						//绑定成功
						$this->_dao->execute( "UPDATE app_license_detail set IsAutoBind=1,SignCS='".$user['SignCS']."',SystemTypeString='".$sysstr."',UserDate=NOW()  WHERE ID='".$empty_id."'" );						
					}	
				}					
			}
			
			//根据GUID获取用户信息
			if($isEnglish==0)
				$userinfo= $this->stedao->getRow("select * from adminuser where id='".$user['Uid']."'");
			else
				$userinfo = $this->stedao->getRow("select * from steelhomeen.member where mbid='".$user['Uid']."'");

			if($mc_type==2&&$is_tp==1)
			{
				$SGLoginURL=SGDCURL.'/web/systemapi.php?action=gzjSysLogin';
				$logininfo['username']='testuser';
				$logininfo['pwd']='123456';
				$logininfo['mc_type']='2';
				$mess=$this->http_post( $SGLoginURL,  http_build_query($logininfo));
				$content=$mess['content'];
				$token=json_decode($this->clearBom($content),true);
				
				$token=$token['Token'];
				$app_license_detail_id=$this->_dao->getone("select ID from app_license_detail where LID='".$Soft_sign['ID']."' and UseUser='".$user['UserName']."' and SignCS='".$user['SignCS']."' and mc_type='".$mc_type."'");
				$SGURL=SGDCURL.'/web/systemapi.php?action=syncdb&mc_type=2';

				$tbinfo['tablename']='app_license_detail';
				$tbinfo['type']='update';
				$tbinfo['pk']='ID';
				$tbinfo['data'][]=array(
					'ID'=>$app_license_detail_id,
					'SignCS'=>$user['SignCS'],
					'SystemTypeString'=>$this->strconvert($SystemTypeString),
					'UserDate'=>date( "Y-m-d H:i:s" ),
					'UseUser'=>$this->strconvert($user['UserName'])
				);
				$tbinfo1[]=$tbinfo;
				$tjinfo['dtjson']=json_encode($tbinfo1);
				$tjinfo['token']=$token;
				$mess=$this->http_post( $SGURL,  urldecode(http_build_query($tjinfo)));
				$tbinfo=array();
				$tbinfo1=array();

				$tbinfo['tablename']='adminuser';
				$tbinfo['type']='replace';
				$tbinfo['pk']='id';
				$tbinfo['data'][]=array(
					'id'=>$userinfo['id'],
					'truename'=>$this->strconvert($userinfo['truename']),
					'passwordmd5'=>$userinfo['passwordmd5'],
					'state'=>$userinfo['state'],
					'mobil'=>$userinfo['mobil'],
					'mid'=>$userinfo['mid'],
					'username'=>$this->strconvert($user['UserName'])
				);
				$tbinfo1[]=$tbinfo;
				$tjinfo['dtjson']=json_encode($tbinfo1);
				$tjinfo['token']=$token;
				$mess=$this->http_post( $SGURL,  urldecode(http_build_query($tjinfo1)));
				$tbinfo=array();
				$tbinfo1=array();
			}
			else if(($mc_type==3 || $mc_type==4)&&$is_tp==1){//同步至新钢服务器调用47号接口
				$XGLoginURL=XGDCURL.'/api/gzjSysLogin';
				$logininfo['username']='testuser';
				$logininfo['pwd']='123456';
				$logininfo['mc_type']=$mc_type;
				$mess=$this->http_post( $XGLoginURL,  http_build_query($logininfo));
				$content=$mess['content'];
				$token=json_decode($this->clearBom($content),true);
				
				$token=$token['Token'];
				$app_license_detail_id=$this->_dao->getone("select ID from app_license_detail where LID='".$Soft_sign['ID']."' and UseUser='".$user['UserName']."' and SignCS='".$user['SignCS']."' and mc_type='".$mc_type."'");
				$XGURL=XGDCURL.'/api/syncdb';

				$tbinfo['tablename']='app_license_detail';
				$tbinfo['type']='update';
				$tbinfo['pk']='ID';
				$tbinfo['data'][]=array(
					'ID'=>$app_license_detail_id,
					'SignCS'=>$user['SignCS'],
					'SystemTypeString'=>$this->strconvert($SystemTypeString),
					'UserDate'=>date( "Y-m-d H:i:s" ),
					'UseUser'=>$this->strconvert($user['UserName'])
				);
				$tbinfo1[]=$tbinfo;
				$tjinfo['dtjson']=json_encode($tbinfo1);
				$tjinfo['token']=$token;
				$mess=$this->http_post( $XGURL,  urldecode(http_build_query($tjinfo)));
				$tbinfo=array();
				$tbinfo1=array();
				//file_put_contents("/tmp/abc.txt",print_r($mess,true)."\n",FILE_APPEND);

				$tbinfo['tablename']='adminuser';
				$tbinfo['type']='replace';
				$tbinfo['pk']='id';
				$tbinfo['data'][]=array(
					'id'=>$userinfo['id'],
					'truename'=>$this->strconvert($userinfo['truename']),
					'passwordmd5'=>$userinfo['passwordmd5'],
					'state'=>$userinfo['state'],
					'mobil'=>$userinfo['mobil'],
					'mid'=>$userinfo['mid'],
					'username'=>$this->strconvert($user['UserName'])
				);
				$tbinfo1[]=$tbinfo;
				$tjinfo['dtjson']=json_encode($tbinfo1);
				$tjinfo['token']=$token;
				$mess=$this->http_post( $XGURL,  urldecode(http_build_query($tjinfo)));
				$tbinfo=array();
				$tbinfo1=array();
			}
			
			//获取	当前设备号
			$license = $this->_dao->getOnes("SELECT LicenseKeyD FROM app_license_detail WHERE SignCS ='".$user['SignCS']."' and Status=1 and (UseUser = '".$user['UserName']."' or UseUser = '') and mc_type='$mc_type'");
			if($params['debug']==1) {echo "</br>license:";print_r($license);echo "</br>";}
			
			$getsigncs = $this->_dao->getOne("SELECT ID FROM app_license_detail WHERE SignCS !='".$user['SignCS']."' and Status=1 and UseUser = '".$user['UserName']."' and mc_type='".$mc_type."' ");
		
			if(empty($license)){ 
				if($getsigncs) {
					$errormes='当前设备未绑定或绑定错误，请联系管理员[1009]';
					if($isEnglish) $errormes='Device Number Error，Please Contact Your Manager[1009]';
					$errtype=1009;
				}else{
					$errormes='登录账号与初始化账号不符[1010]';
					if($isEnglish) $errormes='Login ID Is Not The Same With Initialized ID[1010]';
					$errtype=1010;
				}
				$arr2['Success']='0';
				$arr2['ErrorType']=$errtype;
				$arr2['Message']= base64_encode($this->utf8ToGb2312($errormes));
				$json_string = $this->pri_JSON($arr2);
				echo $json_string;
				exit;
			}
			
			$date = date('Ymd');			
			foreach($license as $value)
			{   
				$sign = md5($value.$user['SignCS'].$date);
				if($params['debug']==1) {print_r($value.$user['SignCS'].$date);echo "</br>";}
				
				if($IsEncrypt==1||$LicenseSign==$sign||date("Ymd",strtotime($LoginDate))!=$date){
					
					if($isEnglish==0||$userinfo['mid']==1){
						$Uid = $userinfo['id'];
						$TrueName = $userinfo['truename'];
						$Mid = $userinfo['mid'];
						$ComName = $this->stedao->getCompnameBymid( $user['Mid'] );
						$UserLevel = $userinfo['power'];
						$MobileNumber = $userinfo['mobil'];
					}elseif($isEnglish==1&&$userinfo['mid']!=1){
						$Uid = $userinfo['mbid'];
						$TrueName = $userinfo['truename'];
						$adminTruename = $userinfo['admintruename'];
						$Mid = $userinfo['comid'];
						$ComName = $userinfo['compabb'];
						$UserLevel = $userinfo['power'];
						$MobileNumber = $userinfo['mmobil'];
						$tel = $userinfo['mtel'];
					}
					$InterFaceUrl='';
					
					//根据设备标识 与 license 获取 客户端是否需要更新数据
					$isupdate = $this->_dao->getOne("select IsUpdate from app_license_detail where SignCS ='".$user['SignCS']."' and LicenseKeyD = '".$value."' and Status = 1 and mc_type='".$mc_type."' limit 1",0);
					$ExpiredDate = $this->_dao->getOne("select EndDate from app_license where Mid ='".$Mid."' and Status = 1 and mc_type='".$mc_type."' limit 1",0);

					//获取 sqlite 密码
					$SQLitePass = $this->_dao->getOne("select SQLitePass from app_license where MID = '".$Mid."' and mc_type='".$mc_type."' limit 1 ",0);
					
					$accessId = $params['Accessid'];
					$token = $params['Token'];
					$secretKey = $GLOBALS['XG_KEY'][$accessId];
					
					//登录成功,session更新
					$this->_dao->execute( "REPLACE INTO app_session_temp SET GUID='$GUID', SignCS='".$user['SignCS']."', Mid='$Mid', Uid='$Uid', ComName='$ComName', UserName='".$user['UserName']."',TrueName='$TrueName', MobileNumber='$MobileNumber', ExpiredDate='$ExpiredDate', LoginDate=NOW(), LoginIp='$LoginIp', SystemType='$SystemType',SystemVersion='$date', LastDate=NOW(), LastAction='Login',mc_type='$mc_type',isEnglish='$isEnglish'" );
					//登录成功推送
					if($mc_type==3){//新钢推送
				
						$deviceType='';
						if($SystemType=='NewAndroidPhone'){
							$deviceType='17';
						}
						if($SystemType=='NewAndroidPad'){
							$deviceType='18';
						}
						if($SystemType=='NewiPad'){
							$deviceType='20';
						}
						if($SystemType=='NewiPhone'){
							$deviceType='19';
						}
						
						$ret = file_get_contents(STEELHOME_URL."cron_97_system/xingePush/xingePush.php?deviceType=".$deviceType."&pushType=bind&tags=".$Uid."&token=".$params['Token']);
						$ret = json_decode($ret,true);
					  
					}
				
					//日志处理
					if($mc_type==2)//陕钢
					{
						$SGLoginURL=SGDCURL.'/web/systemapi.php?action=gzjSysLogin';
						$logininfo['username']='testuser';
						$logininfo['pwd']='123456';
						$logininfo['mc_type']='2';
						
						$mess=$this->http_post( $SGLoginURL,  http_build_query($logininfo));
						$content=$mess['content'];
						$token=json_decode($this->clearBom($content),true);
						
						$token=$token['Token'];
						$SGURL=SGDCURL.'/web/systemapi.php?action=syncdb&mc_type=2';

						$tbinfo['tablename']='app_session_temp';
						$tbinfo['type']='replace';
						$tbinfo['pk']='GUID';
						$tbinfo['data'][]=array(
							'GUID'=>$GUID,
							'SignCS'=>$user['SignCS'],
							'Mid'=>$Mid,
							'Uid'=>$Uid,
							'ComName'=>$this->strconvert($ComName),
							'UserName'=>$this->strconvert($user['UserName']),
							'TrueName'=>$this->strconvert($TrueName),
							'MobileNumber'=>$MobileNumber,
							'ExpiredDate'=>$ExpiredDate,
							'LoginDate'=>date( "Y-m-d H:i:s" ),					 
							'LoginIp'=>$LoginIp,
							'SystemType'=>$SystemType,
							'SystemVersion'=>$date,
							'LastDate'=>date( "Y-m-d H:i:s" ),				 
							'LastAction'=>'Login',
							'mc_type'=>$mc_type,
							'isEnglish'=>$isEnglish
						);

						$tbinfo1[]=$tbinfo;
						$tjinfo['dtjson']=json_encode($tbinfo1);
						$tjinfo['token']=$token;

						$mess=$this->http_post( $SGURL,  urldecode(http_build_query($tjinfo)));
					}					
					else if($mc_type==3 || $mc_type==4)//新钢
					{
					  
						$XGLoginURL=XGDCURL.'/api/gzjSysLogin';
						$logininfo['username']='testuser';
						$logininfo['pwd']='123456';
						$logininfo['mc_type']=$mc_type;
						$mess=$this->http_post( $XGLoginURL,  http_build_query($logininfo));
						$content=$mess['content'];
						$token=json_decode($this->clearBom($content),true);
						
						$token=$token['Token'];
						//$app_license_detail_id=$this->_dao->getone("select ID from app_license_detail where LID='".$sign['ID']."' and LicenseKeyD='".$LicenseKeyD."' and mc_type='".$mc_type."'");
						$XGURL=XGDCURL.'/api/syncdb';

						$tbinfo['tablename']='app_session_temp';
						$tbinfo['type']='replace';
						$tbinfo['pk']='GUID';
						$tbinfo['data'][]=array(
							'GUID'=>$GUID,
							'SignCS'=>$user['SignCS'],
							'Mid'=>$Mid,
							'Uid'=>$Uid,
							'ComName'=>$this->strconvert($ComName),
							'UserName'=>$this->strconvert($user['UserName']),
							'TrueName'=>$this->strconvert($TrueName),
							'MobileNumber'=>$MobileNumber,
							'ExpiredDate'=>$ExpiredDate,
							'LoginDate'=>date( "Y-m-d H:i:s" ),					 
							'LoginIp'=>$LoginIp,
							'SystemType'=>$SystemType,
							'SystemVersion'=>$date,
							'LastDate'=>date( "Y-m-d H:i:s" ),				 
							'LastAction'=>'Login',
							'mc_type'=>$mc_type,
							'isEnglish'=>$isEnglish
						);
						$tbinfo1[]=$tbinfo;
						$tjinfo['dtjson']=json_encode($tbinfo1);
						$tjinfo['token']=$token;
						
						$mess=$this->http_post( $XGURL,  urldecode(http_build_query($tjinfo)));
					}
					$this->_dao->WriteLog($Mid, $Uid, $user['SignCS'], "Login",$actionstr, $LoginIp, $SystemType, $SystemVersion,"网页跳转客户端登录",'','',$mc_type);
					if($isEnglish==0)$adminuser = $this->stedao->getAdminUser($Mid);
					elseif($isEnglish==1)$adminuser = array("truename"=>$adminTruename);
					
					$xgquanxian="";//$xgquanxian="C|F|G|N|J|L|K|O";
					if($mc_type==3&&$SystemType!='NewPC'){
						$pop_idarr=$this->_dao->query("SELECT id,itemname FROM `app_license_popedom_item` WHERE `typeid` =1 and mc_type=3 AND itemname IN ('铁前成本', '钢轧成本', '利润汇总', '销售定价', '绩效效益预测', '研究报告',  '期货套保', '接单效益',  '管理看板', '数据录入', '经营分析', '管理看板查看') ORDER BY FIELD( itemname, '铁前成本', '钢轧成本', '利润汇总', '销售定价', '绩效效益预测', '研究报告', '期货套保', '接单效益',  '管理看板', '数据录入', '经营分析', '管理看板查看')");
						$pop_idstr='';
						foreach ($pop_idarr as $popkey => $popvalue) {
							if($pop_idstr==""){
								$pop_idstr="'".$popvalue['id']."'";
							}else{
								$pop_idstr.=",'".$popvalue['id']."'";
							}

							if($popvalue['itemname']=='铁前成本'){
								$xg_popid[$popvalue['id']]='C';
							}
							if($popvalue['itemname']=='钢轧成本'){
								$xg_popid[$popvalue['id']]='F';
							}
							if($popvalue['itemname']=='利润汇总'){
								$xg_popid[$popvalue['id']]='G';
							}
							if($popvalue['itemname']=='绩效效益预测'){
								$xg_popid[$popvalue['id']]='J';
							}
							if($popvalue['itemname']=='销售定价'){
								$xg_popid[$popvalue['id']]='N';
							}
							if($popvalue['itemname']=='期货套保'){
								$xg_popid[$popvalue['id']]='O';
							}
							
							if($popvalue['itemname']=='研究报告'){
								$xg_popid[$popvalue['id']]='L';
							}
							if($popvalue['itemname']=='接单效益'){
								$xg_popid[$popvalue['id']]='P';
							}
							if($popvalue['itemname']=='管理看板'){
								$xg_popid[$popvalue['id']]='K';
							}
							if($popvalue['itemname']=='数据录入'){
								$xg_popid[$popvalue['id']]='Q';
							}
							if($popvalue['itemname']=='经营分析'){
								$xg_popid[$popvalue['id']]='R';
							}
							if($popvalue['itemname']=='管理看板查看'){
								$xg_popid[$popvalue['id']]='S';
							}
						}
						$popid_arr=$this->_dao->query("SELECT *  FROM `app_license_popedom` WHERE `uid` ='".$Uid."' and mc_type=3 AND isdel=0 AND popid IN (".$pop_idstr." ) GROUP BY popid ORDER BY FIELD( popid, ".$pop_idstr.")");
					
					}elseif($mc_type==3&&$SystemType=='NewPC'){
						$pop_idarr=$this->_dao->query("SELECT id,itemname FROM `app_license_popedom_item` WHERE `typeid` =1 and mc_type=3 AND itemname IN ('铁前成本', '钢轧成本', '利润汇总','绩效效益预测','销售定价', '接单效益','管理看板','数据录入', '期货套保','研究报告','经营分析', '管理看板查看') ORDER BY FIELD( itemname, '铁前成本', '钢轧成本', '利润汇总','绩效效益预测','销售定价', '接单效益','管理看板','数据录入', '期货套保','研究报告','经营分析', '管理看板查看' )");
						// 铁前成本 C 钢轧成本 F 利润汇总 G 销售定价 N  管理看板 K  数据录入Q    绩效效益预测  J 研究报告 L 原料性价比 K 期货套保 O  接单效益 P
						// "C|F|G|J|N|P|K|Q|O|L";
						$pop_idstr=''; 
						foreach ($pop_idarr as $popkey => $popvalue) {
							if($pop_idstr==""){
								$pop_idstr="'".$popvalue['id']."'";
							}else{
								$pop_idstr.=",'".$popvalue['id']."'";
							}

							if($popvalue['itemname']=='铁前成本'){
								$xg_popid[$popvalue['id']]='C';
							}
							if($popvalue['itemname']=='钢轧成本'){
								$xg_popid[$popvalue['id']]='F';
							}
							if($popvalue['itemname']=='利润汇总'){
								$xg_popid[$popvalue['id']]='G';
							}
							if($popvalue['itemname']=='绩效效益预测'){
								$xg_popid[$popvalue['id']]='J';
							}
							if($popvalue['itemname']=='销售定价'){
								$xg_popid[$popvalue['id']]='N';
							}
							if($popvalue['itemname']=='期货套保'){
								$xg_popid[$popvalue['id']]='O';
							}
							
							if($popvalue['itemname']=='研究报告'){
								$xg_popid[$popvalue['id']]='L';
							}
							if($popvalue['itemname']=='接单效益'){
								$xg_popid[$popvalue['id']]='P';
							}
							if($popvalue['itemname']=='管理看板'){
								$xg_popid[$popvalue['id']]='K';
							}
							if($popvalue['itemname']=='数据录入'){
								$xg_popid[$popvalue['id']]='Q';
							}
							if($popvalue['itemname']=='经营分析'){
								$xg_popid[$popvalue['id']]='R';
							}
							if($popvalue['itemname']=='管理看板查看'){
								$xg_popid[$popvalue['id']]='S';
							}
						}
						$popid_arr=$this->_dao->query("SELECT *  FROM `app_license_popedom` WHERE `uid` ='".$Uid."' and mc_type=3 AND isdel=0 AND popid IN (".$pop_idstr." ) GROUP BY popid ORDER BY FIELD( popid, ".$pop_idstr.")");
					}
					if($mc_type==3&&!empty($popid_arr)){ 
						// 铁前成本 C 钢轧成本 F 利润汇总 G 销售定价 N 绩效效益预测 J 研究报告 L 原料性价比 K 期货套保 O
						foreach ($popid_arr as $popid_k => $popid_v) { 
							if($xgquanxian==""){
								$xgquanxian=$xg_popid[$popid_v['popid']];
							}else{
								$xgquanxian.='|'.$xg_popid[$popid_v['popid']];
							}
						}
					}

					if($mc_type==2)
					{
						$popid_arr=$this->_dao->getOne("SELECT ID  FROM `app_license_popedom` WHERE `uid` ='".$Uid."' and mc_type=2 AND isdel=0 AND popid=54");
					
						if(!empty($popid_arr))
							$dpshow=1;
						else
							$dpshow="";
					}

					if($quanxian==1){
						
						if($params['dzquanxian']==1){
							$SignCS_arr = $this->_dao->getRow("select * from app_license_detail where SignCS ='".$user['SignCS']."' and UseUser  = '".$user['UserName']."' and mc_type='".$mc_type."' and Status = 1 ");
					
							$lid_arr=$this->_dao->query("select dc_code_datatypeid from app_license_dzprivilege where lid ='".$SignCS_arr['LID']."' and mc_type='".$mc_type."'");
						
							$dzqxstr='';
							foreach ($lid_arr as $k_lid => $k_v) {
								if( $dzqxstr==''){
									$dzqxstr=$k_v['dc_code_datatypeid'];
								}else{
									$dzqxstr=$dzqxstr.','.$k_v['dc_code_datatypeid'];
								}
							}
						}
					
						//获取品种
						$pz = $this->_dao->getOne("select pinzhong from app_license where MID = '$mid' and Status=1 and mc_type='".$mc_type."' limit 1 ",0);
						if($pz=='8192'){
							$pz='allpz';
						}else{
							foreach( split2($pz) as $k=>$v){
								if($k==0){
									$pz=$v;
								}else{
									$pz=$pz.'|'.$v;
								}
							}
								
						}
						
						//orderlist：读取dc_custom_order表 根据Mid和mc_type查询
						$orderlist =$this->_dao->query("select app_license.ID,app_license.MID,DataType,BuyType,Syear,Eyear from dc_custom_order inner join app_license on dc_custom_order.Mid = app_license.Mid and app_license.mc_type='$mc_type' and dc_custom_order.mc_type='$mc_type' where dc_custom_order.MID='$mid' AND app_license.StartDate<NOW() AND app_license.EndDate>now()");

						if($mc_type==0){
							$ids=$this->_dao->getOnes("select ID from dc_code_class where scode in ('A','B','N') and mc_type='$mc_type'");
						}else{
							$ids=$this->_dao->getOnes("select ID from dc_code_class where scode in ('A','B') and mc_type='$mc_type'");
						}
					
						$isyx = $this->_dao->getRow("select * from app_license where MID = '$mid' AND app_license.StartDate<NOW() AND app_license.EndDate>now() ");
						
						if($isyx){
							if($mc_type==0){
								$ids=$this->_dao->getOnes("select ID from dc_code_class where scode in ('A','B','N') and mc_type='$mc_type'");
							}else{
								$ids=$this->_dao->getOnes("select ID from dc_code_class where scode in ('A','B') and mc_type='$mc_type'");
							}
							
							foreach($ids as $key=>$val) {
								$one=array();
								$one['ID']=0;
								$one['MID']=1;
								$one['DataType']=$val;
								$one['BuyType']=2;
								$one['Syear']='2014';
								$one['Eyear']='2019';
								$orderlist[]=$one;
							}
						}
						
						foreach($orderlist as $key=>$val) {
							$idstr.=$val['DataType'].",";
						}
						$idstr=rtrim($idstr, ",");
						if($idstr)
							$scodelist=$this->_dao->query("select id,scode from dc_code_class where ID in(".$idstr.") and mc_type='$mc_type';");
						
						foreach($scodelist as $key=>$val) {
							$scodelist2[$val['id']]=$val['scode'];
						}
						foreach($orderlist as $key=>$val){
							$scode=$scodelist2[$val['DataType']];
							$orderlist[$key]['scode']=$scode;
							$orderlist[$key]['level']=$this->getcurrentCengshu2( $scode);
							if($scode==''){
								 unset($orderlist[$key]);
							}
							if($istry==1&&$mc_type==0&&in_array($scode,array('K','O')))//试用去除KO
							{
								unset($orderlist[$key]);
							}
						}
					 
						//去除重复数据，id=0的去除
						foreach($orderlist as $key1=>$val1){
							foreach($orderlist as $key=>$val){
								if($val['DataType']==$val1['DataType']&&$val['ID']!=$val1['ID']){	
									if($val['ID']=='0'){
										unset($orderlist[$key]);
									}
									if($val1['ID']=='0'){
										unset($orderlist[$key1]);
									}
								}
									
							}
						}
						if($mc_type==3){
							$cronconfig_arr =$this->_dao->query("select * from xinsteel_cronconfig where userid='".$Uid."'");
							$cronconfig='';
							$n=0;
							foreach ($cronconfig_arr as $key => $value) {
								$cronconfig[$n]['url']=$value['url'];
								$cronconfig[$n]['frequency']=$value['frequency'];
								$cronconfig[$n]['dayweeks']=$value['dayweeks'];
								$cronconfig[$n]['crontime']=$value['crontime'];
								$cronconfig[$n]['title']=$value['title'];
								$cronconfig[$n]['desc']=$value['desc'];
								$n++;
							}
						}else{
							$cronconfig='';
						}
					
						if($params['dzquanxian']==1){
							$arr = array(
								'Success'=>'1',
								'GUID' => $GUID,
								'Message'=>base64_encode($this->utf8ToGb2312('登录成功')),
								'InterFaceUrl'=>$InterFaceUrl,
								'Result'=>array(
									'Uid'=>$Uid,
									'UserName'=>$user['UserName'],
									'TrueName'=>base64_encode($this->utf8ToGb2312($TrueName)),
									'MobileNumber'=>$MobileNumber,
									'Mid'=>$Mid,
									'ComName'=>base64_encode($this->utf8ToGb2312($ComName)),
									'UserLevel'=>$UserLevel,
									'ExpiredDate'=>$ExpiredDate,
									'SmsExpiredDate'=>$ExpiredDate,
									'AdminName'=>base64_encode($this->utf8ToGb2312($adminuser['truename'])),
									'AdminPhone'=>$adminuser['tel'],
									'AdminMobile'=>$adminuser['mobil'],
									'IsUpdate'=>$isupdate,
									'SQLitePass'=>$SQLitePass,
									'dzquanxian'=>$istry?"":$dzqxstr,
									'pinzhong'=>$pz,
									'orderlist'=>array_values($orderlist),
									'xgquanxian'=>$xgquanxian,
									'dpshow'=>$dpshow,
									'cronconfig'=>$cronconfig,
									'istry'=>$istry
								)
							);
						}else{
							$arr = array(
								'Success'=>'1',
								'GUID' => $GUID,
								'Message'=>base64_encode($this->utf8ToGb2312('登录成功')),
								'InterFaceUrl'=>$InterFaceUrl,
								'Result'=>array(
									'Uid'=>$Uid,
									'UserName'=>$user['UserName'],
									'TrueName'=>base64_encode($this->utf8ToGb2312($TrueName)),
									'MobileNumber'=>$MobileNumber,
									'Mid'=>$Mid,
									'ComName'=>base64_encode($this->utf8ToGb2312($ComName)),
									'UserLevel'=>$UserLevel,
									'ExpiredDate'=>$ExpiredDate,
									'SmsExpiredDate'=>$ExpiredDate,
									'AdminName'=>base64_encode($this->utf8ToGb2312($adminuser['truename'])),
									'AdminPhone'=>$adminuser['tel'],
									'AdminMobile'=>$adminuser['mobil'],
									'IsUpdate'=>$isupdate,
									'SQLitePass'=>$SQLitePass,									
									'pinzhong'=>$pz,
									'orderlist'=>array_values($orderlist),
									'xgquanxian'=>$xgquanxian,
									'dpshow'=>$dpshow,
									'cronconfig'=>$cronconfig,
									'istry'=>$istry
								)
							);
						}

					}else{
						$arr = array(
							'Success'=>'1',
							'GUID' => $GUID,
							'Message'=>base64_encode($this->utf8ToGb2312('登录成功')),
							'InterFaceUrl'=>$InterFaceUrl,
							'Result'=>array(
								'Uid'=>$Uid,
								'UserName'=>$user['UserName'],
								'TrueName'=>base64_encode($this->utf8ToGb2312($TrueName)),
								'MobileNumber'=>$MobileNumber,
								'Mid'=>$Mid,
								'ComName'=>base64_encode($this->utf8ToGb2312($ComName)),
								'UserLevel'=>$UserLevel,
								'ExpiredDate'=>$ExpiredDate,
								'SmsExpiredDate'=>$ExpiredDate,
								'AdminName'=>base64_encode($this->utf8ToGb2312($adminuser['truename'])),
								'AdminPhone'=>$adminuser['tel'],
								'AdminMobile'=>$adminuser['mobil'],
								'IsUpdate'=>$isupdate,
								'SQLitePass'=>$SQLitePass,
							)
						);
					}
					if($isEnglish) $arr['Message']='Success';
					break;
				}
			}
		}
		
		if($arr['Success']=='0'&&$arr['ErrorType']=="0"){
			$exist=$this->_dao->getOne("select ID from app_license_detail where status=1 and mc_type='".$mc_type."' and SignCS ='$SignCS' ");
			if($exist){
				$arr['Message']=base64_encode($this->utf8ToGb2312("该设备号已被绑定，登录失败[1011]"));
				if($isEnglish) $arr['Message']=base64_encode("Login Failed For Device Number has already been bound[1011]");
				$arr['ErrorType']=1011;
			}
		}
		$json_string = $this->pri_JSON($arr); 
		echo $json_string;
	}


    
}

?>