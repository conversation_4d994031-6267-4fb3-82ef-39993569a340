<?php
//header('Content-Type:text/html;charset=gb18030');
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );

class sgqhtbController extends AbstractController{
  
	public function __construct(){
		parent::__construct();
		$this->_action->setDao( new sgqhtbDao("91R") );
		$this->_action->gcdao = new sgqhtbDao('GC') ;
		//$this->_action->t1Dao=new dbmarketpriceDao("MAIN");
	}

	public function v_jiaotan(){
		$this->_action->jiaotan( $this->_request );
	}

	public function v_luowen(){
		$this->_action->luowen( $this->_request );
	}

	public function v_tiekuang(){
		$this->_action->tiekuang( $this->_request );
	}

	public function v_feigang(){
		$this->_action->feigang( $this->_request );
	}

	public function v_tiehejin(){
		$this->_action->tiehejin( $this->_request );
	}

	public function v_index(){
		$this->_action->index( $this->_request );
	}

}
?>