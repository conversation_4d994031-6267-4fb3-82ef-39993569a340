<?php 
class  lwgdaypriceAction extends AbstractAction
{
    public function __construct()
    {
        parent::__construct();
    } 
 	public function index($params)
    {
		$modeltype = $params['Type'];
		$GUID = $params['GUID'];
		$mode = $params['mode'];//1=客户端，2=安卓，3=iOS
		$date = $params['curdate']==""?date("Y-m-d"):$params['curdate'];
		$isHuiZong = $params['HuiZong'];

		//获取用户信息
		$user_infos = $this->t1dao->get_userinfo_byguid($GUID);
		$Mid=$user_infos['Mid'];//会员id
		$userid=$user_infos['Uid'];//用户id
		$SignCS =$user_infos['SignCS'];//设备标识
		$truename =$user_infos['TrueName'];//真实姓名
		//获取用户权限和用户名app_license_privilege
		$license_detail_info = $this->t1dao->get_license_privilege($Mid);
		//echo "license_detail_info=<pre>";print_R($license_detail_info);
		$privilege_info = array();
		$orderno_info = array();
		$username_arr = array();
		$user_info = array();
		foreach ($license_detail_info as $license_key => $license_value){
			$privilege_arr = explode(',',$license_value['privilege']);
			$orderno_arr = explode(',',$license_value['orderno']);
			if($privilege_arr[$modeltype-1]){
				$privilege_info[$license_value['username']]=$privilege_arr;
				$orderno_info[$license_value['username']]=$orderno_arr;
				$username_arr[]=$license_value['username'];
				$user_info['truename'][$license_value['username']] = $license_value['truename'];
				$user_info['userid'][$license_value['username']] = $license_value['uid'];
			}
		}
		//日时间
		//$date = date("Y-m-d");
		//$date = "2017-12-23 15:00:00";
		$this_day_start = $date;
		$this_day_end = date("Y-m-d",strtotime("+1 day",strtotime($date)));
		//旬时间
		$xunday1 = '';
		$nodatatitle = '暂无';
		if(date("d",strtotime($date))<11){
			$xunday1 = '01';
			$nodatatitle .= date("Y年n月",strtotime($date))."上旬中厚板旬定价";
			$nodatatitlet .= date("Y年n月",strtotime($date))."上旬碳结钢旬定价";
		}elseif(date("d",strtotime($date))<21){
			$xunday1 = '11';
			$nodatatitle .= date("Y年n月",strtotime($date))."中旬中厚板旬定价";
			$nodatatitlet .= date("Y年n月",strtotime($date))."中旬碳结钢旬定价";
		}else{
			$xunday1 = '21';
			$nodatatitle .= date("Y年n月",strtotime($date))."下旬中厚板旬定价";
			$nodatatitlet .= date("Y年n月",strtotime($date))."下旬碳结钢旬定价";
		}
		$this_xun_start = date("Y-m-$xunday1",strtotime($date));
		$this_xun_end = date("Y-m-d",strtotime($date));
		if($modeltype == '3'){
			$thisdate_start = $this_day_start;
			$thisdate_end = $this_day_end;
			$this->assign("nodatatitle","暂无".date("Y年n月j日",strtotime($date))."螺纹钢日定价");
		}else if ($modeltype == '7'){
			$this->assign("nodatatitle",$nodatatitle);
			$thisdate_start = $this_xun_start;
			$thisdate_end = $this_xun_end;
		}
		
		//获取ng_price_model表信息
		$model_info = $this->_dao->get_ng_price_model_info($modeltype,$thisdate_start,$thisdate_end);
		
		$models=$model_info;
// 		add by wufan 
		$modelid = $model_info['id'];
		if($modelid!=''){
		//价格决策人
		$price_admin = array();
		//分管领导
		$admin = array();
		//系统数据
		$xt_detail_info = $this->_dao->get_ng_price_model_detail_info($modelid,'-1',$modeltype);
		$modeloldprice = $xt_detail_info[0]['modeloldprice'];
		$this_user_privilege = "";
		$price_admin_all_commit = 1;
		foreach($privilege_info as $username => $privilege_arr){
			$privilege = $privilege_arr[$modeltype-1];
			$puserid = $user_info['userid'][$username];
			$orderno = $orderno_info[$username][$modeltype-1];
			if($userid==$puserid)$this_user_privilege = $privilege;
			$ptruename = $user_info['truename'][$username];
			$model_detail_info = $this->_dao->get_ng_price_model_detail_info($modelid,$puserid,$modeltype);	
			if(!empty($model_detail_info)){
				//价格决策人
				if($privilege==2){
					foreach($model_detail_info as $mkey => $mvalue){
						$price_admin[$puserid]['truename'] = $ptruename;
						$price_admin[$puserid]['modelprice'] = $mvalue['modelprice'];
						$price_admin[$puserid]['modeloldprice'] = $mvalue['modeloldprice'];
						$price_admin[$puserid]['abs_modelprice_updown'] = $mvalue['abs_modelprice_updown'];
						$price_admin[$puserid]['modelprice_updown'] = $mvalue['modelprice_updown'];
						$price_admin[$puserid]['modelpirce_name'] = $mvalue['modelpirce_name'];
						$price_admin[$puserid]['uid'] = $mvalue['uid'];
						$price_admin[$puserid]['orderno'] = $orderno;
					}

				//分管领导 
				}elseif($privilege==1){
						$admin[$puserid]['truename'] = $ptruename;
						$admin[$puserid]['modelprice'] = $model_detail_info[0]['modelprice'];
						$admin[$puserid]['modeloldprice'] = $model_detail_info[0]['modeloldprice'];
						$admin[$puserid]['abs_modelprice_updown'] = $model_detail_info[0]['abs_modelprice_updown'];
						$admin[$puserid]['modelprice_updown'] = $model_detail_info[0]['modelprice_updown'];
						$admin[$puserid]['modelpirce_name'] = $model_detail_info[0]['modelpirce_name'];
						$admin[$puserid]['uid'] = $model_detail_info[0]['uid'];
						$admin[$puserid]['orderno'] = $orderno;
				}
			}else{
				//价格决策人
				if($privilege==2){
					$price_admin[$puserid]['truename'] = $ptruename;
					$price_admin[$puserid]['orderno'] = $orderno;
					$price_admin_all_commit = 0;

				//分管领导
				}elseif($privilege==1){
					$admin[$puserid]['truename'] = $ptruename;
					$admin[$puserid]['orderno'] = $orderno;
				}
			}
		}		
		$model_info['modelcontent'] = html_entity_decode($model_info['modelcontent']);
		$ismakepricyByadmin = $this->_dao->getismakepricyByadmin($modelid);
		$ismakepricy = $this->_dao->getismakepricy($modelid);
		$this_issubmit = $this->_dao->getThisUserIsMakepricy($modelid,$userid);
		$this_issubmit >0 ?1:0;
		if(($ismakepricy!=1&&($this_user_privilege==1||$this_user_privilege==3))){
			$admin_sort = array();
			$admin_new = array();
			foreach ($admin as $akey => $avalue){
				$admin_sort[$avalue['orderno']][$akey] = $avalue;
			}
			ksort($admin_sort);
			$all_admin_num = count($admin_sort);
			$admin_commit = 1;
			$i = 0;
			$admin_sort = array_values($admin_sort);
			foreach ($admin_sort as $skey => $svalue){
				foreach($svalue as $ssk =>$ssv){
					//如果当前分管领导已经定价，并且下一个分管领导也定价了，则显示下一个分管领导的定价
					if($this_admin_issubmit==1&&$ssv['modelprice']!=''){
						$admin_new[$ssk] = $ssv;
					}
					//如果当前分管领导后面的分管领导定价了，则当前分管领导不能再次定价了
					if($stop&&$ssv['modelprice']!=''&&$ssv['truename'] != $truename){
						$admin_not_edit = 1;
						break;
					}
					if($ssv['truename'] == $truename){
						//判断当前分管领导前面一个分管领导是否定价
						if($skey!=0){
						foreach($admin_sort[$skey-1] as $lllk =>$lllv){
							if($lllv['modelprice']==''){
								$admin_commit = 0;
							}
						}
						}elseif($price_admin_all_commit!=1){
							$admin_commit = 0;
						}
						//判断当前分管领导是否定价
						if($ssv['modelprice']!='')$this_admin_issubmit = 1;
						$stop = 1;
						//如果当前分管领导是排序第一或者前面领导已经定完价，进来时可以定价
						if(($i == 0&&$price_admin_all_commit==1)||$admin_commit==1){
							$admin_new[$ssk] = $ssv;
							}
					}
					if(!$stop){
						$admin_new[$ssk] = $ssv;
					}
				}
				$i++;
			}
		}else{//决策之后
			$admin_sort = array();
			$admin_new = array();
			foreach ($admin as $akey => $avalue){
				$admin_sort[$avalue['orderno']][$akey] = $avalue;
			}
			ksort($admin_sort);
			$admin_sort = array_values($admin_sort);
			//echo "<pre>admin_sort=";print_R($admin_sort);
			$count = 0;
			foreach ($admin_sort as $skey => $svalue){
				foreach($svalue as $ssk =>$ssv){
					if($ismakepricy==1){
						$admin_new[$ssk] = $ssv;
					}else{
						if($ssv['modelprice']!=''){
							$admin_new[$ssk] = $ssv;
							$count++;
						}
					}
					
				}
			}
		}
		$isJc = $count>0?1:0;
		if(($admin_not_edit!=1&&$admin_commit==1&&$price_admin_all_commit==1)||($this_admin_issubmit==1&&$admin_not_edit!=1)){
			$admin_can_commit = 1;
		}
		if($ismakepricyByadmin==0){
			$price_admin_can_commit = 1;
		}
		//判断是否是市场部人员
		if($this_user_privilege==3&&$ismakepricy!=1&&$price_admin_all_commit==1){
			$i=0;
			$admin_can_commit = 1;
			foreach($admin_new as $adminkey=>$value){
				$userid = $adminkey;
				if($value['modelprice']=='')$i++;
			}
			if($i>1){
				$admin_can_commit = 0;
			}
			$isLastAdmin = 1;
		}
		$now_admin_num = count($admin_new);
		//最后一个分管领导（非市场部）
		if(($now_admin_num!=''&&$all_admin_num!=''&&$all_admin_num == $now_admin_num)&&$this_user_privilege!=3){
			$isLastAdmin = 1;
		}
		if($modeltype==3){
			$STR = "较昨日";
			if($mode==1){
				$head_url = "//".$_SERVER['SERVER_NAME']."/v1.5/css/ridj_small.css";
			}else if($mode==3||$mode==5){
				$head_url = "//".$_SERVER['SERVER_NAME']."/v1.5/css/ridj_middle.css";
			}else if($mode==2||$mode==4){
				$head_url = "//".$_SERVER['SERVER_NAME']."/v1.5/css/ridj_large.css";
			}
		}elseif($modeltype==7||$modeltype==2){
			$STR = "较前旬";
			if($mode==1){
				$head_url = "//".$_SERVER['SERVER_NAME']."/v1.5/css/xundj_small.css";
			}else if($mode==3||$mode==5){
				$head_url = "//".$_SERVER['SERVER_NAME']."/v1.5/css/xundj_middle.css";
			}else if($mode==2||$mode==4){
				$head_url = "//".$_SERVER['SERVER_NAME']."/v1.5/css/xundj_large.css";
			}
		}
		if($isHuiZong=='1'&&($this_user_privilege==''||$this_user_privilege=='0')){
			echo "-1";exit;
		}
		
	
		if($models['is_wangqi']!='1') {
			 
			 
			$up_wq='update ng_price_model set  is_wangqi=1 where id="'.$models['id'].'" and modeltype="'.$modeltype.'"   ';
			
			$this->_dao->execute($up_wq);
			
		}
		$this->assign( "islwg",0);
			if($models['pricycontent']==''&&$_GET['save_lwghl']!=1){
			 	$url = "http://".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']."&save_lwghl=1";
				// $url = "http://iwww.steelhome.cn/data/v1.5/web/dcwebview3.php?index&Type=2&GUID=ced31fecdf1511e7b891001aa00de1ab&mode=1&save_tjghl=1";		
				include '/usr/local/www/libs/phpQuery/phpQuery.php';
				phpQuery::newDocumentFile($url);
				$content=pq("html")->html();
				$up_wq='update ng_price_model set pricycontent ="'.htmlentities($content,ENT_QUOTES,"UTF-8").'" where id="'.$models['id'].'" and modeltype="'.$modeltype.'" and is_wangqi=1  ';
				
				$this->_dao->execute($up_wq);
				$this->assign( "islwg",1);
			}
		
		$this->assign("model_info",$model_info);
		$this->assign("xt_detail_info",$xt_detail_info);
		$this->assign("price_admin",$price_admin);
		$this->assign("admin",$admin_new);
		$this->assign("userid",$userid);
		$this->assign("mid",$Mid);
		$this->assign("modeltype",$modeltype);
		$this->assign("modelid",$modelid);
		$this->assign("STR",$STR);
		$this->assign("head_url",$head_url);
		$this->assign("ismakepricy",$ismakepricy);//是否最终决策
		$this->assign("isLastAdmin",$isLastAdmin);//是否是最后一个分管领导决策
		$this->assign("this_user_privilege",$this_user_privilege);//当前用户权限
		$this->assign("modeloldprice",$modeloldprice);//上期价格
		$this->assign("price_admin_can_commit",$price_admin_can_commit);//价格决策人是否可以提交
		$this->assign("admin_can_commit",$admin_can_commit);//分管领导人是否可以提交
		$this->assign("this_issubmit",$this_issubmit);//当前用户是否决策
		$this->assign("thisdate_start",$thisdate_start);//当前用户是否决策
		$this->assign("isJc",$isJc);//分管领导决策过后就显示
		$this->assign("isHuiZong",$isHuiZong);//是否是汇总使用  1=汇总
		$this->assign( "params", $params);
		}else{
			$this->assign("nodata","1");
			
			
		}
    }

	
	public function save_price($params){

		$thisdate_start=$params['thisdate_start'];
		$uid=$params['uid'];
		$mid=$params['Mid'];
		$params['isLastAdmin']==1 ?  $isLastAdmin=1:$isLastAdmin=0;
		$modeltype=$params['modeltype'];
		$modelid=$params['modelid'];
		$price=$params['tprice'];
		$oldprice=$params['modeloldprice'];
		$updown=$params['tupdown'];
		$updownprice=$params['tupdownprice'];
		$updownprice = $updownprice*$updown;
		$modelpirce_name = $GLOBALS ['MODEL_TYPE'][$modeltype];
		if($params['privilege']==3){
			$privilege = 3;
			$UserType = $privilege;
		}else{
			$params['privilege']==1 ?  $privilege=1:$privilege=0;
			$UserType = $privilege+1;
		}

		
		
		$res = $this->_dao->get_ng_price_model_detail_info($modelid,$uid,$modeltype);
		
		if(empty($res)){
			$this->_dao->insert_ng_price_model_detail_info($modelid,$uid,$mid,$modeltype,$isLastAdmin,$price,$oldprice,$updownprice,$modelpirce_name,$UserType,$thisdate_start);
		}else{
			$this->_dao->update_ng_price_model_detail_info($modelid,$uid,$modeltype,$price,$updownprice,$thisdate_start);
		}
		
	
		//$url = DC_URL."/data/v1.5/web/lwgdayprice.php?view=index";
		$url = "http://".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']."&save_lwghl=1";
		include '/usr/local/www/libs/phpQuery/phpQuery.php';
		phpQuery::newDocumentFile($url);
		$html=pq("head")->html();
		$html.=pq("body")->html();
		$modelcontent=htmlentities($html, ENT_QUOTES,"UTF-8");
		$this->_dao->update_ng_price_model2($modelid,$modelcontent);
		
		
		//保存最终决策内容
		if($isLastAdmin==1){ 
			$this->_dao->update_ng_price_model($modelid,'');

			$modelcontent=htmlentities($html, ENT_QUOTES,"UTF-8");
			$this->_dao->update_ng_price_model($modelid,$modelcontent);
		}
		gourl($_SERVER['HTTP_REFERER']);
		
	}
} 
?>