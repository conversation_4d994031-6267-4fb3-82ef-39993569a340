<?php
class sgylpriceAction extends AbstractAction{
   public function __construct(){
    parent::__construct();    
	}

	function zhangdie($data, $type='0')
	{
			if ($data>0) {
					$data = '<font color=red>↑'.abs($data).'</font>';
			} elseif ($data<0) {
					$data = '<font color=green>↓'.abs($data).'</font>';
			} elseif ($data==0) {
					if ($type=='-') {
							$data = '<font color=black>―</font>';
					} else {
							$data = '<font color=black>'.$data.'</font>';
					}
			}
			return $data;
	}

	public function index($params)
	{
		if($params['date']==""){
            $flag = 1;
            $lastday = date("Y-m-d");
            while(true)
            {
                if(file_get_contents(APP_URL_WWW.'/isholiday.php?date='.$lastday)!="1")
                { 
                  break;
                } 
                $lastday=date('Y-m-d',strtotime('-'.$flag.' day',strtotime($lastday)));
            }
            $params['date'] = $lastday;
		}
		$date = $params['date'];
		$price_id1 = array(
			"D28611","D28630","D286121","D286302","D28660","D286602","D286a0","D286142","D28619","D286106",
			"118611","118630","1186121","1186302","118660","1186602","1186a0","1186142","1186191","118619",
			"888611","888630","8886121","8886602","888619","888615",
			"158650","158640","1586402","1586302","158660","1586602","1586a0","158670","158619","1586403");
		$price_id2 = array("5989101","418910","5389101","5589101","6089101","W287102","569422","539422","689422");
		$price_id3 = array("S283111","T283111","K183111","6683121","U283111","U283102","A183111");
		$price_id4 = array("A16620","V26330","K16330","M16330");
		$price_id = array_merge($price_id1, $price_id2, $price_id3, $price_id4);
		// $price_id = array("W287102","569422");
		$topicture_arr = array();
		$mastertopid_arr = array();
		foreach($price_id as $id_tmp1) strlen($id_tmp1)==6 ? $topicture_arr[] = $id_tmp1 : $mastertopid_arr[] = $id_tmp1;
		// echo "<pre>";
		$mastertopids = "'".implode("','", $mastertopid_arr)."'";
		$topictures = "'".implode("','", $topicture_arr)."'";
		$enddate = $date." 23:59:59";
		$this_start_date  = date('Y-m', strtotime($date))."-01 00:00:00";
		$last_start_date = date('Y-m',strtotime('-1 month',strtotime($date)))."-01 00:00:00";
		$last_end_date = date('Y-m-d',strtotime('-1 day', strtotime('+1 month',strtotime($last_start_date))))." 23:59:59";
		$last_day_num = ceil(abs(strtotime($last_start_date) - strtotime($last_end_date))/(60*60*24));  // 上月天数
		$this_day_num = ceil(abs(strtotime($date." 23:59:59") - strtotime($this_start_date))/(60*60*24));  // 本月到选取时间的天数
		$sql = "(SELECT price, oldprice,  mconmanagedate, topicture, material FROM marketconditions WHERE topicture IN (".$topictures.") AND mconmanagedate >= '".$last_start_date."' AND mconmanagedate <= '".$enddate."') UNION (SELECT price, oldprice, mconmanagedate, mastertopid as topicture, material FROM marketconditions WHERE mastertopid IN (".$mastertopids.") AND mconmanagedate >= '".$last_start_date."' AND mconmanagedate <= '".$enddate."')";
		$rs = $this->homeDao->query($sql);
		$ok_list = array();
		foreach($rs as $datas){
			$new_data[$datas['topicture']][date('Y-m-d',strtotime($datas['mconmanagedate']))]['price'] = $datas['price'];
			$new_data[$datas['topicture']][date('Y-m-d',strtotime($datas['mconmanagedate']))]['oldprice'] = $datas['oldprice'];
			// $new_data[$datas['topicture']][date('Y-m-d',strtotime($datas['mconmanagedate']))]['articlename'] = $datas['articlename'];

			if($datas['mconmanagedate']>=($date." 00:00:00")&&$datas['mconmanagedate']<=($date." 23:59:59")){
				$ok_list[$datas['topicture']]['zb'] = $datas['material'];
				$ok_list[$datas['topicture']]['price'] = $datas['price'];
				$ok_list[$datas['topicture']]['rzd'] = $this->zhangdie($datas['price']-$datas['oldprice'], '-');
				$ok_list[$datas['topicture']]['mconmanagedate'] = $datas['mconmanagedate'];
			}
		}
		foreach($new_data as $topicture=>$dates){
			$i = 0;
			$j = 0;
			foreach($dates as $date_k=>$data){
				if($date_k>=date('Y-m-d', strtotime($last_start_date))&&$date_k<=$last_end_date&&$data['price']!='-'){
					$i++;
					$totle_price[$topicture]['last'] += $data['price'];
				}
				if($date_k>=date("Y-m-d", strtotime($this_start_date))&&$date_k<=($date." 23:59:59")&&$data['price']!='-'){
					$j++;
					$totle_price[$topicture]['this'] += $data['price'];
				}
			}
			$ok_list[$topicture]['jprice'] = round($totle_price[$topicture]['this']/$j, 0);
			$ok_list[$topicture]['yzd'] = $this->zhangdie(round($ok_list[$topicture]['jprice'] - ($totle_price[$topicture]['last']/$i), 0), '-');
		}
		// 废钢采购价格 -- 山西建龙（1163）和长峰钢铁（1565）

		$varietyid['fg'] = "ll,054,c15";
		$steel_id_cf = "1565";
		$steel_id_sx = "1163";
		$sql = "select sb.steel_id,sb.post_date,sb.run_date,si.specification,last_price,the_price,ftime,variety from SteelCaiGou_base sb,SteelCaiGou_Info si where sb.id=si.sb_id and steel_id='$steel_id_cf' and sb.VarietyId in ('".$varietyid['fg']."') and sb.is_show=2 and sb.ComType=1 and sb.post_date like (select post_date from SteelCaiGou_base where steel_id='$steel_id_cf' and VarietyId = 'll,054,c15' and is_show =2 and ComType =1 and post_date<='$date 23:59:59' order by `post_date` desc limit 1) order by orderby_id";
		$steel_data_cf = $this->gcDao->query($sql);
		$sql = "select sb.steel_id,sb.post_date,sb.run_date,si.specification,last_price,the_price,ftime,variety from SteelCaiGou_base sb,SteelCaiGou_Info si where sb.id=si.sb_id and steel_id='$steel_id_sx' and sb.VarietyId in ('".$varietyid['fg']."') and sb.is_show=2 and sb.ComType=1 and sb.post_date like (select post_date from SteelCaiGou_base where steel_id='$steel_id_sx' and VarietyId = 'll,054,c15' and is_show =2 and ComType =1 and post_date<='$date 23:59:59' order by `post_date` desc limit 1) order by orderby_id";
		$steel_data_sx = $this->gcDao->query($sql);
		$sxjl_data = array();
		$cfgt_data = array();
		foreach($steel_data_cf as &$v_v){
			$the_price = explode('-', $v_v['the_price']);
			$v_v['the_price'] = round(($the_price[0]+$the_price[1])/count($the_price), 0);
			$last_price = explode('-', $v_v['last_price']);
			$v_v['last_price'] = round(($last_price[0]+$last_price[1])/count($last_price), 0);
			$v_v['tf'] = $this->zhangdie($v_v['the_price']-$v_v['last_price'], "-");
			$cfgt_data[] = $v_v;
		}
		foreach($steel_data_sx as $v_v){
			$v_v['tf'] = $this->zhangdie($v_v['the_price']-$v_v['last_price'], "-");
			$sxjl_data[] = $v_v;
		}
		$this->assign('date', date('n月j日', strtotime($date)));
		$this->assign('date2', $date);
		$this->assign('ok_list', $ok_list);
		$this->assign('sxjl', $sxjl_data);
		$this->assign('cfgt', $cfgt_data);
		$this->assign('mode', $params['mode']?$params['mode']:1);
		$style_url= dirname(DC_URL.$_SERVER['PHP_SELF']);
		$this->assign( "style_url", $style_url );
		$this->assign('GUID', $params['GUID']);
	}
}


?>