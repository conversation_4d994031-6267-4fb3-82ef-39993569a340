* {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
	 cursor: url(../../images/jiantou.png) 8 3, auto !important;
}

html,body { width: 100%; height: 100%; font-family: myFirstFont !important;}
html{ background:unset}
body{ background-image: unset; background-size:100% auto; background-repeat:no-repeat; background-position: 0 bottom;}

.fl { float: left}
.fr { float: right}

ul,ol,li{ list-style: none}
@font-face { font-family: myFirstFont; src: url('DISPLAY FREE TFB.ttf');}
::-webkit-scrollbar { width: 5px; height: 5px; position: absolute}
::-webkit-scrollbar-thumb { background-color: #5bc0de;}
::-webkit-scrollbar-track {   background-color: #ddd;}
.allnav { height: 100%;}
.rightTop { position: relative; transition: all 1s; }
/* .header{background: url(../../images/title.jpg) no-repeat center 40px;} */
html{ background:#ffffff url(../../images/htmlbg.png) repeat-x 0 0; height:100%;}


 .header:before{background:none;} 
 .header:after{background:none} 
 .header h1{background:none;width: 658px;;} 
 .header h1 *{ color:#fff;}
 .header{width: 100%;
    height: 120px;
    position: relative;
    overflow: hidden;
    background: url(../../images/title.jpg) no-repeat center 40px;} 
 .header .header-left {  height: 45px; text-indent:25px; line-height: 45px; color:#ffffff;}
 
.nav{ background:#1855A5; height:46px; line-height:42px;}
.nav li{ color:#ADCCF3; height:46px; font:normal 16px/46px "Microsoft YaHei";}
.nav li.active{ background:#E41646; border:2px solid #EB88A4; color:#ffffff; font:bold 16px/42px "Microsoft YaHei"; height:46px;}

/* html,body{ background:none;} */
.welcome{ background:url(images/welcomebg2.jpg) no-repeat 0 0; background-size:100% 100%; height:100%; position:relative;}
.welcome_logo{ font:bolder 18px/30px "Microsoft Yahei"; color:#5ccdff; margin:10px 0 0 20px;}
.welcome h1{ font:bold 80px/100px "Microsoft Yahei"; text-align:center; letter-spacing:10px; margin-top:14%;}
.welcome h2{ font:normal 40px/100px "Microsoft Yahei"; color:#3786e8; letter-spacing:5px; text-align:center;}

.pageleft{ position:absolute; bottom:20px; left:20px; width:50px; height:50px; border-radius:50%; background:rgba(255,255,255,0.1);}
.pageleft:hover{ background:rgba(255,255,255,0.15);}
.pageleft div{ width:20px; height:20px; border-top:3px solid #ffffff; border-left:3px solid #ffffff; transform:rotate(-45deg); margin:15px 0 0 20px;}
.pageleft:active div{ width:18px; height:18px;  border-top:3px solid #78F5FB; border-left:3px solid #78F5FB; margin:16px 0 0 21px;}
.pageleft:active{ box-shadow:0 0 10px rgba(0,0,0,0.3) inset; background:rgba(0,0,0,0.05);}
.pageright{ position:absolute; bottom:20px; right:20px; width:50px; height:50px; border-radius:50%; background:rgba(255,255,255,0.1);}
.pageright:hover{ background:rgba(255,255,255,0.15);}
.pageright div{ width:20px; height:20px; border-top:3px solid #ffffff; border-right:3px solid #ffffff; transform:rotate(45deg); margin:15px 0 0 10px;}
.pageright:active div{ width:18px; height:18px;  border-top:3px solid #78F5FB; border-right:3px solid #78F5FB; margin:16px 0 0 11px;}
.pageright:active{ box-shadow:0 0 10px rgba(0,0,0,0.3) inset; background:rgba(0,0,0,0.05);}