* {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box
}

*{ padding: 0; margin: 0; color: #B3D7F4; font-family:"SimSun";}

html,body { font-size: .16rem; position: relative; width: 100%; height: 100%; font-family: myFirstFont !important;}

html{background: #000C3D url(../../images/bg.png) no-repeat 0 top; background-size: 100%; }
body{background: url(../../images/footbg.png) no-repeat 0 bottom; background-size: 100%; }

.fl {
    float: left
}

.fr {
    float: right
}

ul,
ol {
    list-style: none
}

.allnav {
    height: 100%
}

@font-face {
    font-family: myFirstFont;
    src: url('DISPLAY FREE TFB.ttf')
}

::-webkit-scrollbar {
    width: 5px;
    height: 5px;
    position: absolute
}

::-webkit-scrollbar-thumb {
    background-color: #5bc0de
}

::-webkit-scrollbar-track {
    background-color: #ddd
}

.allnav {
    height: 100%
}

.rightTop {    width: 100%;    height: 450px;    position: relative;    border: 1px solid #1A76C1;    background: #021245;    transition: all 1s;    cursor: pointer}

.border:before {
    content: '';
    position: absolute;
    width: 90%;
    height: 100%;
    bottom: -1px;
    top: -1px;
    left: 5%;
    border-bottom: 1px solid #1D3267;
    border-top: 1px solid #1D3267;
    transition: all .5s
}

.border:after {
    content: '';
    position: absolute;
    width: 100%;
    height: 86%;
    left: -1px;
    right: -1px;
    top: 7%;
    border-left: 1px solid #1D3267;
    border-right: 1px solid #1D3267;
    transition: all .5s
}

.border:hover::before {   width: 0%}

.border:hover::after {  height: 0%}

.border h4 {
    margin: 15px 0 5px 20px;
    color: #8adeff;
    line-height: 35px;
    font-size: 18px;
    font-weight: 500
}

* {
    cursor: url(../../images/pointer.png) 8 3, auto !important
}