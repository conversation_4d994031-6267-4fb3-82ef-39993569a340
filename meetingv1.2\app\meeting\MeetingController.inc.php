<?php
include_once(FRAME_LIB_DIR . "/controller/AbstractController.inc.php");

class MeetingController extends AbstractController
{
    public function __construct()
    {
        parent::__construct();
        $this->_action->setDao(new MeetingDao('91W'));
        $this->_action->smsDao = new MeetingDao('98SMW');
        $this->_action->oaDao = new MeetingDao('OA');
    }
    // 后台admincp用户SESSION检测
    // public function _dopre(){
    // $this->_action->checkSession();
    // }
    public function v_index()
    {
        $this->_action->index($this->_request);
    }

    public function do_Login()
    {
        //header("Content-Type: text/html; charset=gbk");
        $this->_action->meet_Login($this->_request);
    }

    public function do_GetUserInfoByCid()
    {
        //header("Content-Type: text/html; charset=gbk");
        $this->_action->meet_GetUserInfoByCid($this->_request);
    }

    // 二维码扫描查询
    public function do_QRSearch()
    {
        //header("Content-Type: text/html; charset=gbk");
        $this->_action->meet_QRSearch2($this->_request);
    }
    // 绑定微信
    public function do_bind_weixin()
    {
        //header("Content-Type: text/html; charset=gbk");
        $this->_action->bind_weixin($this->_request);
    }
    // 签到
    public function do_getbind_weixin()
    {
        //header("Content-Type: text/html; charset=gbk");
        $this->_action->getbind_weixin($this->_request);
    }
    // 解绑账号(微信号与代表证绑定)
    public function do_unbind_weixin()
    {
        //header("Content-Type: text/html; charset=gbk");
        $this->_action->unbind_weixin($this->_request);
    }
    //根据电话号码查信息
    public function do_Getmesmob()
    {
        //header("Content-Type: text/html; charset=gbk");
        $this->_action->Getmes_mobile($this->_request);
    }
    // 根据类型获取会议相关信息
    public function do_Getmestype()
    {
        //header("Content-Type: text/html; charset=gbk");
        $this->_action->Getmes_type($this->_request);
    }
    // 更新备注信息
    public function do_Updateremark()
    {
        //header("Content-Type: text/html; charset=gbk");
        $this->_action->updateremark($this->_request);
    }
    // 姓名信息查询
    public function do_Searchname()
    {
        //header("Content-Type: text/html; charset=gbk");
        $this->_action->meet_Searchname($this->_request);
    }
    // 编号查询
    public function do_BHSearch()
    {
        //header("Content-Type: text/html; charset=gbk");
        $this->_action->meet_BHSearch($this->_request);
    }

    // 序号查询
    public function do_XHSearch()
    {
        //header("Content-Type: text/html; charset=gbk");
        $this->_action->meet_XHSearch($this->_request);
    }

    // 现场报到
    public function do_QRBaoDao()
    {
        //header("Content-Type: text/html; charset=gbk");
        $this->_action->meet_QRBaoDao2($this->_request);
    }
    // 资料领取接口
    public function do_QRZiliaoLq()
    {
        //header("Content-Type: text/html; charset=gbk");
        $this->_action->meet_QRZiliaoLq2($this->_request);
    }
    // 纪念品领取接口
    public function do_QRjnpLq()
    {
        //header("Content-Type: text/html; charset=gbk");
        $this->_action->meet_QRjnpLq($this->_request);
    }
    // 地图领取接口
    public function do_QRmapLq()
    {
        $this->_action->meet_QRMapLq($this->_request);
    }
    
    // 旅游登记领取接口
    public function do_QRLvYouDj()
    {
        //header("Content-Type: text/html; charset=gbk");
        $this->_action->meet_QRLvYouDj($this->_request);
    }
    // 报道历史记录查询
    public function do_history_signin()
    {
        //header("Content-Type: text/html; charset=gbk");
        $this->_action->history_signin($this->_request);
    }
    // 正在进行中会议接口
    public function do_QRmeetingsets()
    {
        //header("Content-Type: text/html; charset=gbk");
        $this->_action->meet_QRmeetingsets($this->_request);
    }
    // 入场接口
    public function do_RuChang()
    {
        //header("Content-Type: text/html; charset=gbk");
        $this->_action->meet_RuChang2($this->_request);
    }
    // 出场接口
    public function do_ChuChang()
    {
        //header("Content-Type: text/html; charset=gbk");
        $this->_action->meet_ChuChang2($this->_request);
    }
    // 最新报到代表接口
    public function do_ZXBaoDao()
    {
        //header("Content-Type: text/html; charset=gbk");
        $this->_action->meet_ZXBaoDao($this->_request);
    }
    // 最新报到列表接口
    public function do_ZXLieBiao()
    {
        //header("Content-Type: text/html; charset=gbk");
        $this->_action->meet_ZXLieBiao($this->_request);
    }
    // 资料下载接口
    public function do_ZLXiaZai()
    {
        //header("Content-Type: text/html; charset=gbk");
        $this->_action->meet_ZLXiaZai($this->_request);
    }
    // 获取参会代表信息接口
    public function do_GetMemberInfo()
    {
        //header("Content-Type: text/html; charset=gbk");
        $this->_action->meet_GetMemberInfo($this->_request);
    }


    //add by nicc 

    //修改交费状态字段

    public function do_SetFinStatus()
    {
        //header("Content-Type: text/html; charset=gbk");
        $this->_action->SetFinStatus($this->_request);
    }


    //酒店入住接口
    public function do_CheckinHotel()
    {
        $this->_action->CheckinHotel($this->_request);
    }

    //抽奖登记接口
    public function do_SetAward()
    {
        $this->_action->SetAward($this->_request);
    }

    //领奖接口
    public function do_GetAward()
    {
        $this->_action->GetAward($this->_request);
    }

    //领奖接口(全体奖)
    public function do_GetAwardAll()
    {
        $this->_action->GetAwardAll($this->_request);
    }

    //取消领奖接口
    public function do_CancerAward()
    {
        $this->_action->CancerAward($this->_request);
    }

    //初始化 获奖接口
    public function do_GetAwardList()
    {
        $this->_action->GetAwardList($this->_request);
    }


    //更新接口
    public function do_SetRun()
    {
        $this->_action->SetRun($this->_request);
    }

    //获取状态接口

    public function do_GetStatus()
    {
        $this->_action->GetStatus($this->_request);
    }


    //获取入住入场 接口
    public function do_GetRcType()
    {
        //header("Content-Type: text/html; charset=gbk");
        $this->_action->GetRcType($this->_request);
    }

    //获取酒店类型 接口
    public function do_GetHotelType()
    {
        $this->_action->GetHotelType($this->_request);
    }


    //同步入场信息
    public function do_SetRcDate()
    {
        $this->_action->doSetRcDate($this->_request);
    }

    //同步入场信息
    public function do_checkbyopenid()
    {
        //header("Content-Type: text/html; charset=gbk");
        $this->_action->checkbyopenid($this->_request);
    }
    public function do_bindifsuccess()
    {
        //header("Content-Type: text/html; charset=gbk");
        $this->_action->bind_ifsuccess($this->_request);
    }
    public function do_unbindifsuccess()
    {
        //header("Content-Type: text/html; charset=gbk");
        $this->_action->unbind_ifsuccess($this->_request);
    }
    public function do_bindstate()
    {
        //header("Content-Type: text/html; charset=gbk");
        $this->_action->bindstate($this->_request);
    }
    public function do_updatewxmes()
    {
        //header("Content-Type: text/html; charset=gbk");
        $this->_action->updatewxmes($this->_request);
    }
    // 微信选座获取信息
    public function do_getuserinfo_weixin()
    {
        //header("Content-Type: text/html; charset=gbk");
        $this->_action->getuserinfo_weixin($this->_request);
    }
    public function do_getuserinfolist_weixin_mid_mtid()//获取会议同一会员参会人员
    {
        //header("Content-Type: text/html; charset=gbk");
        $this->_action->getuserinfolist_weixin_mid_mtid($this->_request);
    }
    public function do_sug()//获取会议同一会员参会人员
    {
        //header("Content-Type: text/html; charset=gbk");
        $this->_action->sug($this->_request);
    }

    public function do_GetPerson()//获取会议同一会员参会人员
    {
        //header("Content-Type: text/html; charset=utf-8");
        $this->_action->GetPerson($this->_request);
    }
    public function do_meet_GetMemberInfo_managetruename()//获取会议同一会员参会人员
    {
        //header("Content-Type: text/html; charset=gbk");
        $this->_action->meet_GetMemberInfo_managetruename($this->_request);
    }
    //推送暂时注释
    public function do_pushaward()
    {
        //header("Content-Type: text/html; charset=gbk");
        $this->_action->pushaward($this->_request);
    }
    /*
    public function do_ouyeelpush()
    {
         //header("Content-Type: text/html; charset=gbk");
        $this->_action->ouyeelpush($this->_request);						
    }
    */

    /**
     * @param 
     * user:shizg
     * time:2022/11/4 10:07
     * TODO 更新欧冶中奖人员奖项
     */
    //欧冶抽奖推送接口关闭
    /*
    public function do_ouyeelpushanyone()
    {
        $this->_action->ouyeelpushanyone($this->_request);
    }
    public function do_ouyeelpushanyonenew()
    {
        $this->_action->ouyeelpushanyonenew($this->_request);
    }
    */
    /*public function do_ouyeelpushanyone1109()
    {
        $this->_action->ouyeelpushanyone1109($this->_request);
    }
    */
    public function do_getxyjperson()
    {
        $this->_action->getxyjperson($this->_request);
    }
    public function do_RuChangNew()
    {
        $this->_action->doRuChangNew($this->_request);
    }
    public function do_CheckLastVersion()
    {
        $this->_action->CheckLastVersion($this->_request);
    }
    public function do_RuChangTongji()
    {
        $this->_action->doRuChangTongji($this->_request);
    }

    public function do_RuchangLogin()
    {
        //header("Content-Type: text/html; charset=gbk");
        $this->_action->doRuchangLogin($this->_request);
    }
    public function do_addCancerAward()
    {
        $this->_action->addCancerAward($this->_request);
    }
    //奖品清空
    public function do_clearCancerAward()
    {

        $this->_action->clearCancerAward($this->_request);
    }

    public function do_getQiandaoList()
    {
        $this->_action->getQiandaoList($this->_request);
    }
    public function do_setQiandaoType()
    {
        $this->_action->setQiandaoType($this->_request);
    }
    public function v_map()
    {
        $this->_action->map($this->_request);
    }
    public function do_getMeetingScreenData()
    {
        $this->_action->getMeetingScreenData($this->_request);
    }
    public function do_getmap()
    {
        $this->_action->getmap($this->_request);
    }
    public function do_getMeetingScreenData2()
    {
        $this->_action->getMeetingScreenData2($this->_request);
    }
    public function v_survey()
    {
        $this->_action->survey($this->_request);
    }
    public function do_getMeetingSurveyScreenData()
    {
        $this->_action->getMeetingSurveyScreenData($this->_request);
    }

    public function v_work()
    {
        $this->_action->work($this->_request);
    }

    public function v_survey_phone()
    {
        $this->_action->survey_phone($this->_request);
    }

    public function v_meeting_choujiang()
    {
        $this->_action->meeting_choujiang($this->_request);
    }
    public function v_choujiang_set()
    {
        $this->_action->choujiang_set($this->_request);
    }
    public function v_choujiang()
    {
        $this->_action->choujiang($this->_request);
    }
    public function v_cj_set()
    {
        $this->_action->cj_set($this->_request);
    }

    public function v_survey_meeting()
    {
        $this->_action->survey_meeting($this->_request);
    }

    public function do_getMeetingSurveyScreenData_2()
    {
        $this->_action->getMeetingSurveyScreenData_2($this->_request);
    }

    //推送幸运奖  晚宴
    public function do_addCancerAwardNew()
    {
        $this->_action->addCancerAwardNew($this->_request);
    }

    //推送中奖人信息，区分领奖状态
    public function do_pushawardNew()
    {
        $this->_action->pushawardNew($this->_request);
    }


    //二、三等奖抽完一轮自动推送中奖人信息，前面推送的不覆盖
    public function do_pushawardAuto()
    {
        $this->_action->pushawardAuto($this->_request);
    }
    //领奖扫码的
    public function do_getLingJiangRuchangList()
    {
        $this->_action->getLingJiangRuchangList($this->_request);
    }
    public function do_GetYdzwrl()
    {
        $this->_action->GetYdzwrl($this->_request);
    }
    // 获取全部奖品信息
    public function do_getAllPrizes()
    {
        $this->_action->getAllPrizes($this->_request);
    }

    //新春联谊会抽奖
    public function v_newyear_choujiang()
    {
        $this->_action->newyear_choujiang($this->_request);
    }

    //新春联谊会抽奖
    public function v_newyear_cjset()
    {
        $this->_action->newyear_cjset($this->_request);
    }

    //新春联谊会抽奖 全体同步为阳光普照奖
    public function do_push_ygpz()
    {
        $this->_action->push_ygpz($this->_request);
    }
    public function do_bdlogs()
    {
        $this->_action->bdlogs($this->_request);
    }
    public function do_GetBDChangeMessageList()
    {
        $this->_action->GetBDChangeMessageList($this->_request);
    }
    public function do_Setlogstatus()
    {
        $this->_action->Setlogstatus($this->_request);
    }
    public function do_GetMapTypeList()
    {
        $this->_action->GetMapTypeList($this->_request);
    }


}

?>