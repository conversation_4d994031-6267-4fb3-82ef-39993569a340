(function ($) {
    $.fn.lucky = function (opt) {
        var opts = {
            row: 3, //每排显示个数  必须为奇数
            col: 3, //每列显示个数  必须为奇数
            depth: 2, //纵深度
            iconW: 50, //图片的宽
            iconH: 50, //图片的高
            iconRadius: 40, //图片的圆角
            data: personArr, //图片的地址数据
        }
        var _self = $(this);
        var settings = $.extend({}, opts, opt);
        var M = {
            WinHeight: $(this).height(),
            winWidth: $(this).width(),
            isThisTag: false,
            centerX: Math.ceil(settings.row / 2) - 1,
            centerY: Math.ceil(settings.col / 2) - 1,
            timer: null,
            isStop: false,
        };

        // 初始化应用
        var initFun = function () {
            initEleFun();
            stepFun();
        };
        var initEleFun = function () {
            for (var i = 0; i < settings.depth; i++) {
                createEleFun(i);
            }
        }

        var createEleFun = function (n) {
           
            // 创建所有的dom元素
            var eleStr = '';
            var ii=0;
            for (var i = 0; i < settings.row; i++) {
                for (var r = 0; r < settings.col; r++) {
                    // if (i == M.centerX && r == M.centerY) {
                    //     if (!M.isThisTag) {

                    //         eleStr += '<div style="' + styleFun(i, r) + '" class="js_current_dom"></div>';
                    //         M.isThisTag = true;

                    //     }

                    // } else {
                        eleStr += '<div data-depth="' + n + '" style=" background-color:rgba(0,0,0,0);' + styleFun(i, r) + '" class="element"></div>';
                    //}
                   
                }
            }
            _self.append(eleStr);
        }

        //设置每个头像的位置与样式
        var styleFun = function (i, r) {
                onlyWidth = M.winWidth / settings.row,
                onlyHeight = M.WinHeight / settings.col,
                onlyCenterW = (M.winWidth / settings.row - settings.iconW) / 2,
                onlyCenterH = (M.WinHeight / settings.col - settings.iconH) / 2;


                var left=(i * onlyWidth + onlyCenterW)+(Math.floor(Math.random() * (30 - (-60) + 1)) - 60);
                var top=(r * onlyHeight + onlyCenterH)+(Math.floor(Math.random() * (30 - (-50) + 1)) - 50);
            var style = 'position:absolute;width:' + settings.iconW + 'px;height:' + settings.iconH + 'px;border-radius:' + settings.iconRadius + 'px;left:' + left + 'px;top :' + top + 'px;';
            return style;
        }

        var stepFun2 = function () {
            var elements = $('.element').length;
            for (var i = 0; i < elements; i++) {
                var element = $('.element')[i];
                var num=Math.round(Math.random() *  (settings.data.length-1));
                $(element).css('background-image', 'url(' + settings.data[num].headimgurl + ')');
            }
        }

        //让每个头像运动
        var stepFun = function () {

            var index = 0, elements = $('.element').length;
           
            for (var i = 0; i < elements; i++) {
                var element = $('.element')[i];
                var num=Math.round(Math.random() *  (settings.data.length-1));
                $(element).css('background-image', 'url(' + settings.data[num].headimgurl + ')');
             

                var depth = $(element).attr('data-depth');
                Transform(element);
                element.translateZ = -depth * 600;

                setTimeout(function (element) {
                    var random = Math.floor(Math.random() * 3) + 15;
                        
                        tick(function () {
                            if (!M.isStop) {
                                $(element).css('opacity', 1);
                               // var num=Math.ceil(Math.random() *  (settings.data.length-1));
                               // $(element).css('background-image', 'url(' + settings.data[num].headimgurl + ')');
                                element.translateZ >= 600 ? element.translateZ = -depth * 600 : element.translateZ += random;
                                if(element.translateZ>500){
                                 var num=Math.round(Math.random() *  (settings.data.length-1));
                                $(element).css('background-image', 'url(' + settings.data[num].headimgurl + ')');
                                }
                              // console.log(settings.data[num].headimgurl);
                              
                              if(element.translateZ<-200){
                                    // 添加透明度动画
                                var maxOpacity = 0.6; // 最大不透明度
                                var minOpacity = 1; // 最小透明度
                               var opacityRange = maxOpacity - minOpacity;
                                var currentOpacity = maxOpacity - (element.translateZ / 600) * opacityRange; // 根据深度设置透明度
                                currentOpacity = Math.min(maxOpacity, Math.max(minOpacity, currentOpacity)); // 限制在合理范围内
                                $(element).css('opacity', currentOpacity);
                              }

                              
                             if(element.translateZ<-200){
                                    // 添加透明度动画
                                var maxOpacity = 0.6; // 最大不透明度
                                var minOpacity = 1; // 最小透明度
                               var opacityRange = maxOpacity - minOpacity;
                                var currentOpacity = maxOpacity - (element.translateZ / 600) * opacityRange; // 根据深度设置透明度
                                currentOpacity = Math.min(maxOpacity, Math.max(minOpacity, currentOpacity)); // 限制在合理范围内
                                $(element).css('opacity', currentOpacity);
                              }

                            


                            }
                        })
                }(element),400)
            }
           
           
        }

        var preloadImg = function(arr){

        }

        //让中间的头像随机切换背景图
        var randomFun = function () {

            M.timer = setInterval(function () {
                //如果内定号码不存在，则为随机号码
                var randomNum = Math.floor(Math.random() * settings.data.length); //（随机数）

                $('.js_current_dom').css({
                   'background-image': 'url(' + settings.data[randomNum].headimgurl + ')'
                })
            }, 100000)
        }
        //停止运动
        M.stop = function () {
            clearInterval(M.timer);
            M.isStop = true;
        }
        //开始运动
        M.open = function () {
            randomFun();
            M.isStop = false;
        }
        initFun();
        return M;
    }

})(jQuery);