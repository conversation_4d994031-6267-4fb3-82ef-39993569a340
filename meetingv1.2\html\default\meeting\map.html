﻿

<!DOCTYPE html>
<html lang="zh-CN">

<head>
	<meta charset="utf-8">
	<title>钢之家行情在线</title>
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
	<meta name="x5-orientation" content="portrait">
	<meta name="mobile-web-app-capable" content="yes">
	<meta name="apple-mobile-web-app-capable" content="yes">
	<meta name="apple-mobile-web-app-status-bar-style" content="black">
	<meta name="robots" content="nofollow">
	<meta name="format-detection" content="telephone=no" />
	<!-- <script src="//a.mysteelcdn.com/libs/js/vconsole.min.js"></script> -->
	<!-- <link href="css/index.css?v=20190521" rel="stylesheet" type="text/css" /> -->
	<link href="css/meeting/map/index.css?v=20211214" rel="stylesheet" type="text/css" />
	<link href="css/meeting/map/mobiscroll.core.css" rel="stylesheet" type="text/css" />
	
</head>
<body class="<{$classinfo}>">
	<div class="cont clearfix container">
		<div id="swipTab" class="swiper-container">
			<div class="swiper-wrapper">
				<div data-hash="hqmap" class="pannel swiper-slide hqmap">
					<div class="loading" id="loading"></div>
					<div class="nodata" style="position:absolute;top:5rem;text-align:center;width: 100%;z-index:2;display:none">暂无数据</div>
					<div class="clearfix topBtn" style="display:none">
						<div id="selectOpt" class="dateSelect borNone" >
							<div class="selectInput">
								<span class="optName"></span>
								<div class="spec"></div>
								<span class="upicon"></span>
							</div>
							<div id="test_select" class="demos opt_select" data-role="none" style="display: none;">
								
							</div>
						</div> 
						<div class="dateSelect">

							<!--<input value="" name="appDate" id="appDate" onfocus="return false;" type="text" class="selectInput brl" aria-label="Text input with dropdown button"/>-->
							<input value="" name="appDate" id="appDate" onfocus="this.blur()" type="text"
								class="selectInput brl" aria-label="Text input with dropdown button" />
							<span class="upicon dateSelectUpicon"></span>
						</div>
					</div>
					<div class="dwo mengceng" style="display: none;"></div>
					<div class="mappal clearfix china-map">
						<div id='myStateButton' class="btn-group pull-right" data-toggle="buttons" style="display:none">
							<label class="btn btn-info active">
								<input type="radio" name="options" data-val="0" autocomplete="off" checked=""> 日</label>
							<label class="btn btn-info">
								<input type="radio" name="options" data-val="1" autocomplete="off"> 周</label>
							<label class="btn btn-info">
								<input type="radio" name="options" data-val="2" autocomplete="off"> 月</label>
						</div>
						<div id="myStateButtonnew" class="btn-group pull-right" data-toggle="buttons" >
						  <ul>
						  
						   <{foreach from=$pzarr key=key item=v}>
						   <li class='btn btn-info<{if $key=="2023"}> active <{/if}> ' style="display:block" data-val="<{$key}>" ><{$v}></li>
						   <{/foreach}>
							
						  </ul>
						</div>
						<div id="main" style="min-width: 100%; overflow: hidden;"></div>
						
						<div id="zoomBtn">
							<span class="refresh"></span>
							<span class="sbtn jia" data-zoom="+1">+</span>
							<span class="sbtn jian" data-zoom="-1">-</span>
						</div>
						
					</div>
					<div class="globalmap" style="position:relative; min-width: 100%; height:780px;">
						<!-- 为ECharts准备一个具备大小（宽高）的Dom -->
						<div id="ImportedMapCharts" style="min-width: 100%; overflow: hidden;"></div>
						<div class="up-scale">+</div>
						<div class="down-scale">-</div>
					</div>
					<div id="cityPrice"></div>
				</div>
				
			</div>
		</div>
		
	</div>
	<div class="downloadDialog close_hook">
		<div class="download_mark"></div>
		<div class="download_cont">
			<div class="title">提示</div>
			<!-- <p class="note">打开我的钢铁手机版<br>查看更多品种信息</p> -->
			<p class="note"><br>登录查看详情</p>
			<div class="download_btn">
				<button class="download_cancle">取消</button>
				<button class="download_sure">去登录</button>
			</div>
		</div>
	</div>
	
	<script src="js/meeting/map/libs/require.min.js" data-main="js/meeting/map/index.min.js?v=20231031"></script>
</body>

</html>