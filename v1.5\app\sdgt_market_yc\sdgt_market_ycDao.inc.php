<?php

class sdgt_market_ycD<PERSON> extends Dao
{
    public function __construct($writer)
    {
        parent::__construct($writer);
    }
    //获取预测数据
    function get_yc_data($yc_type,$channel,$sdete,$edete){
        $sql = "select * from sd_steel_day_weeks_month_predict where yc_type = '".$yc_type."' and channel = '".$channel."' and yc_sdate = '".$sdete."' and yc_edate = '".$edete."'";
        //echo $sql;
        $data = $this->query($sql);
        $ret_data = array();
        foreach ( $data as $pkey => $pvalue ){
            if($pvalue["yc_direction"] == "1"){
                $ret_data[$pvalue["yc_varietyname"]]["ycfx"] = "上涨";
            }else if($pvalue["yc_direction"] == "2"){
                $ret_data[$pvalue["yc_varietyname"]]["ycfx"] = "持平";
            }else if($pvalue["yc_direction"] == "3"){
                $ret_data[$pvalue["yc_varietyname"]]["ycfx"] = "下跌";
            }
            if($pvalue["yc_fudu1"] == "0" && $pvalue["yc_fudu2"] == "0"){
                $ret_data[$pvalue["yc_varietyname"]]["ycfd"] = "-";
            }else{
                $ret_data[$pvalue["yc_varietyname"]]["ycfd"] = $pvalue["yc_fudu1"] ."-". $pvalue["yc_fudu2"];
            }
            if($pvalue["yc_direction"] == "1"){
                $ret_data[$pvalue["yc_varietyname"]]["ycfxfd"] = "+".$pvalue["yc_fudu1"] ."-". $pvalue["yc_fudu2"];
                $ret_data[$pvalue["yc_varietyname"]]["yc_zd"] = round(($pvalue["yc_fudu1"] + $pvalue["yc_fudu2"])/2);
            }else if($pvalue["yc_direction"] == "2"){
                $ret_data[$pvalue["yc_varietyname"]]["ycfxfd"] = "--";
                $ret_data[$pvalue["yc_varietyname"]]["yc_zd"] = 0;
            }else if($pvalue["yc_direction"] == "3"){
                $ret_data[$pvalue["yc_varietyname"]]["ycfxfd"] = "-".$pvalue["yc_fudu2"] ."-". $pvalue["yc_fudu1"];
                $ret_data[$pvalue["yc_varietyname"]]["yc_zd"] = 0 - round(($pvalue["yc_fudu1"] + $pvalue["yc_fudu2"])/2);
            }
        }
        return $ret_data;
    }

    public function get_yc_avg_price($sdate,$edate){
        $sql = "SELECT yc_varietyname,ROUND(AVG(yc_price)) as avg_price FROM sd_steel_day_weeks_month_predict WHERE `yc_edate` <= '".$edate."' and `yc_edate` >= '".$sdate."' and yc_type = 1 GROUP BY yc_varietyname";
        $data = $this->query($sql);
        $ret_data = array();
        foreach ( $data as $pkey => $pvalue ){
            $ret_data[$pvalue["yc_varietyname"]] = $pvalue['avg_price'];
        }
        return $ret_data;
    }

    //获取市场要闻
    function get_sdgt_marketNews($type,$sdate,$edate,$news_type){
        $sql = "select content from sd_steel_marketNews where type = '".$type."' and CDate >= '".$sdate."' and CDate <= '".$edate."' and news_type = '".$news_type."'";
        // echo $sql;
        return $this->getOne($sql);
    }
    /**
     * 获取期货数据
     * Created by zfy.
     * Date:2023/1/9 16:11
     * @param $workDate
     * @param $typeStr
     * @return array
     */
    public function getFuturesByTypes($workDate,$typeStr){
        $startInfo = $this->Aquery("SELECT a.dta_type,a.dta_6 FROM data_table a, (SELECT dta_type, MAX( dta_ym) dta_ym FROM  data_table  WHERE dta_ym< '".$workDate['this_start_date']."' AND dta_type IN ($typeStr) and dta_maxValStatus='1'  GROUP BY dta_type)b WHERE a.dta_type= b.dta_type AND a.dta_ym= b.dta_ym and dta_maxValStatus='1'");
        $endInfo = $this->Aquery("SELECT a.dta_type,a.dta_6 FROM data_table a, (SELECT dta_type, MAX( dta_ym) dta_ym FROM  data_table  WHERE dta_ym<= '".$workDate['this_end_date']."' AND dta_type IN ($typeStr) and dta_maxValStatus='1' GROUP BY dta_type)b WHERE a.dta_type= b.dta_type AND a.dta_ym= b.dta_ym and dta_maxValStatus='1'");
        return array("startPrice"=>$startInfo,"endPrice"=>$endInfo);
    }

    /**
     * 获取纽原油数据
     * Created by zfy.
     * Date:2023/1/9 16:21
     * @param $workDate
     * @param $typeStr
     * @return array
     */
    public function getNYCrudeOil($workDate,$typeStr){
        $startInfo = $this->getOne("select mvalue from gfutures_shpi where bianma = '$typeStr' and datetime <= '".$workDate['last_end_date']."' order by datetime desc limit 1");
        $endInfo = $this->getOne("select mvalue from gfutures_shpi where bianma = '$typeStr' and datetime <= '".$workDate['this_end_date']."' order by datetime desc limit 1");
        return array("startPrice"=>$startInfo,"endPrice"=>$endInfo);
    }

    /**
     * 获取美元指数
     * Created by zfy.
     * Date:2023/1/9 16:22
     * @param $workDate
     * @return array
     */
    public function getUSDIndex($workDate){
        $startInfo = $this->getOne("select rd2 from dolrate where rdate <= '".$workDate['last_end_date']."' order by rdate desc limit 1");
        $endInfo = $this->getOne("select rd2 from dolrate where rdate <= '".$workDate['this_end_date']."' order by rdate desc limit 1");
        return array("startPrice"=>$startInfo,"endPrice"=>$endInfo);
    }
    /**
     * 人民币汇率
     * Created by zfy.
     * Date:2023/1/9 16:24
     * @param $workDate
     * @return array
     */
    public function getRMBRate($workDate){
        $startInfo = $this->getOne("select rd1 from rmbrate where rdate <= '".$workDate['last_end_date']."' order by rdate desc limit 1");
        $endInfo = $this->getOne("select rd1 from rmbrate where rdate <= '".$workDate['this_end_date']."' order by rdate desc limit 1");
        return array("startPrice"=>$startInfo,"endPrice"=>$endInfo);
    }
    /**
     * 获取钢厂调价
     * Created by zfy.
     * Date:2023/1/10 9:01
     * @param $xunDateList
     * @param $onlyIdList
     * @return array
     */
    public function getSteelPrice($xunDateList,$onlyIdList){
        $where = "";
        foreach ($onlyIdList as $index => $item) {
            if ($index != 0) $or = "or";
            $where .= " $or (onlyid='" . $item['onlyid'] . "' and gcid='" . $item['gcid'] . "')";
        }
        // $priceInfo = $this->query("SELECT * FROM (SELECT id,the_price_tax,last_price_tax,the_price,last_price,onlyid, ftime, gcid,variety,material,specification,tax_type,CONCAT(onlyid,'-',gcid) as concat_field FROM  `steelprice_info` WHERE ($where) AND the_price_tax != '' AND the_price_tax != '0' AND ftime <= '".$xunDateList['this_end_date']."' GROUP BY ftime, concat_field ORDER BY id DESC)  AS a GROUP BY concat_field");
        // $changeRate_tax = $this->Aquery("SELECT CONCAT(onlyid,'-',gcid) as concat_field,sum(changerate_tax) FROM `steelprice_info` WHERE ($where) AND ftime >= '".$xunDateList['this_start_date']."' AND ftime <= '".$xunDateList['this_end_date']."' group by concat_field");
        // $changeRate_no_tax = $this->Aquery("SELECT CONCAT(onlyid,'-',gcid) as concat_field,sum(changerate) FROM `steelprice_info` WHERE ($where) AND ftime >= '".$xunDateList['this_start_date']."' AND ftime <= '".$xunDateList['this_end_date']."' group by concat_field");
        $priceInfo = $this->query("SELECT * FROM (SELECT id,the_price_tax,last_price_tax,the_price,last_price,onlyid, ftime, gcid,variety,material,specification,tax_type,CONCAT(onlyid,'-',gcid) as concat_field FROM  `steelprice_info` WHERE ($where) AND the_price_tax != '' AND the_price_tax != '0' AND ftime <= '".$xunDateList['this_end_date']."' AND ftime >= '".$xunDateList['this_start_date']."' GROUP BY ftime, concat_field ORDER BY id DESC)  AS a GROUP BY concat_field");
        $changeRate_tax = $this->Aquery("SELECT CONCAT(onlyid,'-',gcid) as concat_field,sum(changerate_tax) FROM `steelprice_info` WHERE ($where) AND ftime >= '".$xunDateList['this_start_date']."' AND ftime <= '".$xunDateList['this_end_date']."' group by concat_field");
        $changeRate_no_tax = $this->Aquery("SELECT CONCAT(onlyid,'-',gcid) as concat_field,sum(changerate) FROM `steelprice_info` WHERE ($where) AND ftime >= '".$xunDateList['this_start_date']."' AND ftime <= '".$xunDateList['this_end_date']."' group by concat_field");
        $gcIdName = $this->Aquery("select st_code,name from sc_steelcom");
        $retList = array();
        foreach ($onlyIdList as $index => $item) {
            foreach ($priceInfo as $i => $priceItem) {
                if ($priceItem['onlyid'] == $item['onlyid'] && $priceItem['gcid'] == $item['gcid']){
                    if ($priceItem['tax_type'] == '1'){
                        $priceItem['tax'] = "含税";
                        $priceItem['price'] = $priceItem['the_price_tax'];
                        $changeRate = $changeRate_tax;
                    }else{
                        $priceItem['tax'] = "不含税";
                        $priceItem['price'] = $priceItem['the_price'];
                        $changeRate = $changeRate_no_tax;
                    }
                    $retList[$item['onlyid']."-".$item['gcid']] = $priceItem;
                    $retList[$item['onlyid']."-".$item['gcid']]['gc_name'] = $gcIdName[$priceItem['gcid']];
//                    $retList[$item['onlyid']]['date'] = date("m-d",strtotime($priceItem['ftime']));
                    $retList[$item['onlyid']."-".$item['gcid']]['date'] = date("Y-m-d",strtotime($priceItem['ftime']));
                    if ($changeRate[$item['onlyid']."-".$item['gcid']]){
                        $retList[$item['onlyid']."-".$item['gcid']]['change_rate'] = $this->zhangdie($changeRate[$item['onlyid']."-".$item['gcid']]);
                    }else{
                        $retList[$item['onlyid']."-".$item['gcid']]['change_rate'] = 0;
                    }
                }
            }
        }
        return $retList;
    }

    public function getYRLSteelPrice($xunDateList,$onlyIdList){
        $where = "";
        foreach ($onlyIdList as $index => $item) {
            if ($index != 0) $or = "or";
            $where .= " $or (onlyid='" . $item['onlyid'] . "' and gcid='" . $item['gcid'] . "')";
        }
        $priceInfo = $this->query("SELECT * FROM (SELECT id,the_price_tax,last_price_tax,the_price,last_price,onlyid, ftime, gcid,variety,specification FROM  `SteelCaiGou_Info` WHERE ($where) AND the_price_tax != '' AND ftime >= '".$xunDateList['this_start_date']."' AND ftime <= '".$xunDateList['this_end_date']."' GROUP BY ftime, onlyid, gcid ORDER BY id DESC)  AS a GROUP BY onlyid, gcid");
        
        $changeRate = $this->Aquery("SELECT onlyid,sum(changerate_tax) FROM `SteelCaiGou_Info` WHERE ($where) AND ftime >= '".$xunDateList['this_start_date']."' AND ftime <= '".$xunDateList['this_end_date']."' group by onlyid");
        $gcIdName = $this->Aquery("select st_code,name from sc_steelcom");
        $retList = array();
        foreach ($onlyIdList as $index => $item) {
            foreach ($priceInfo as $i => $priceItem) {
                if ($priceItem['onlyid'] == $item['onlyid']){
                    $retList[$item['onlyid']] = $priceItem;
                    $retList[$item['onlyid']]['gc_name'] = $gcIdName[$priceItem['gcid']];
//                    $retList[$item['onlyid']]['date'] = date("m-d",strtotime($priceItem['ftime']));
                    $retList[$item['onlyid']]['date'] = date("Y-m-d",strtotime($priceItem['ftime']));
                    if ($changeRate[$item['onlyid']]){
                        $retList[$item['onlyid']]['change_rate'] = $this->zhangdie($changeRate[$item['onlyid']]);
                    }else{
                        $retList[$item['onlyid']]['change_rate'] = 0;
                    }
                }
            }
        }
        return $retList;
    }

    /**
     * Created by zfy.
     * Date:2024/6/4 16:48
     * @param $dateList
     * @return array
     */
    public function getNgSteelPriceInfo($dateList){
        $list = $this->getRow("select Value,dta_ym from ng_data_table where DataMark='Ngzb0001' and dta_ym<='" . $dateList['this_end_date'] . "' order by dta_ym desc limit 1");

        //上期数据 且价格不跟本期一样
        $lastValueList = $this->getRow("select dta_ym,Value from ng_data_table where DataMark='Ngzb0001' and dta_ym<='" . $dateList['last_end_date'] . "' and Value!='".$list['Value']."' order by dta_ym desc limit 1");

        //当前价格最早的执行日期
        $runDate = $this->getOne("select dta_ym from ng_data_table where DataMark='Ngzb0001' and dta_ym>'" . $lastValueList['dta_ym'] . "' and Value='".$list['Value']."' order by dta_ym asc limit 1");
        $retInfo = [];
        $retInfo['date'] = $runDate;
        $retInfo['price'] = $list['Value'];
        $retInfo['updown'] = $this->zhangdie($list['Value'] - $lastValueList['Value']);
        $is_show = 0;
        if($runDate <= $dateList['this_end_date'] && $runDate >= $dateList['this_start_date']){
            $is_show = 1;
        }
        $retInfo['is_show'] = $is_show;
        return $retInfo;
    }

    /**
     * 获取行情信息 普氏用
     * Created by zfy.
     * Date:2024/6/4 10:04
     * @param $dateList
     * @param $idStr
     * @return mixed
     */
    public function getHangQingInfo($dateList,$idStr)
    {
        $list = $this->query("select a.topicture as priceid,a.price,a.oldprice,a.mconmanagedate from marketconditions a,(select topicture,max(mconmanagedate) mconmanagedate from marketconditions where mconmanagedate<='" . $dateList['this_end_date'] . " 23:59:59' AND topicture IN ($idStr) group by topicture) b where a.topicture= b.topicture and a.mconmanagedate= b.mconmanagedate");
        $lastList = $this->query("select a.topicture as priceid,a.price,a.oldprice,a.mconmanagedate from marketconditions a,(select topicture,max(mconmanagedate) mconmanagedate from marketconditions where mconmanagedate<='" . $dateList['last_end_date'] . " 23:59:59' AND topicture IN ($idStr) group by topicture) b where a.topicture= b.topicture and a.mconmanagedate= b.mconmanagedate");

        $lastInfo = array();
        foreach ($lastList as $index => $item) {
            $lastInfo[$item['priceid']] = $item;
        }
        foreach ($list as $index => &$item) {
            $list[$index]['updown'] = $this->zhangdie($item['price'] - $lastInfo[$item['priceid']]['price']);
            $list[$index]['date'] = date("Y-m-d",strtotime($item['mconmanagedate']));;
        }
        $is_show = 0;
        if($runDate <= $dateList['this_end_date'] && $list[0]['date'] >= $dateList['this_start_date']){
            $is_show = 1;
        }
        $list[0]['is_show'] = $is_show;
        return $list;
    }

    /**
     * 获取市场库存的本旬 上旬 上月 上年数据
     * Created by zfy.
     * Date:2023/1/10 11:15
     * @param $dateList
     * @param $typeStr
     * @return array
     */
    public function getMarketStock($dateList,$typeStr){
        $thisDate = $this->Aquery("select a.type,a.value from kucun_hz a,(select type,max(time) time from kucun_hz where time<='".$dateList['thisDate']."' AND type IN ( $typeStr ) group by type) b where a.type= b.type and a.time= b.time ");
        $lastDate = $this->Aquery("select a.type,a.value from kucun_hz a,(select type,max(time) time from kucun_hz where time<='".$dateList['lastDate']."' AND type IN ( $typeStr ) group by type) b where a.type= b.type and a.time= b.time ");
        $lastMonth = $this->Aquery("select a.type,a.value from kucun_hz a,(select type,max(time) time from kucun_hz where time<='".$dateList['lastMonth']."' AND type IN ( $typeStr ) group by type) b where a.type= b.type and a.time= b.time ");
        $lastYear = $this->Aquery("select a.type,a.value from kucun_hz a,(select type,max(time) time from kucun_hz where time<='".$dateList['lastYear']."' AND type IN ( $typeStr ) group by type) b where a.type= b.type and a.time= b.time ");
        if ($typeStr != 6)$this->sumStock($thisDate,$lastDate,$lastMonth,$lastYear);
        return array("thisDate"=>$thisDate,"lastDate"=>$lastDate,"lastMonth"=>$lastMonth,"lastYear"=>$lastYear);
    }
    /**
     * 获取钢厂库存的本旬 上旬 上月 上年数据
     * Created by zfy.
     * Date:2023/1/10 11:23
     * @param $dateList
     * @param $TypeStr
     * @return array
     */
    public function getSteelStock($dateList,$TypeStr){

        $thisDate = $this->Aquery("select a.Type,a.Value from KuCunHZ a,(select Type,max(Date) Date from KuCunHZ where Date<='".$dateList['thisDate']."' AND Type IN ( $TypeStr ) group by Type) b where a.Type= b.Type and a.Date= b.Date and Area=0");
        $lastDate = $this->Aquery("select a.Type,a.Value from KuCunHZ a,(select Type,max(Date) Date from KuCunHZ where Date<='".$dateList['lastDate']."' AND Type IN ( $TypeStr ) group by Type) b where a.Type= b.Type and a.Date= b.Date and Area=0");
        $lastMonth = $this->Aquery("select a.Type,a.Value from KuCunHZ a,(select Type,max(Date) Date from KuCunHZ where Date<='".$dateList['lastMonth']."' AND Type IN ( $TypeStr ) group by Type) b where a.Type= b.Type and a.Date= b.Date and Area=0");
        $lastYear = $this->Aquery("select a.Type,a.Value from KuCunHZ a,(select Type,max(Date) Date from KuCunHZ where Date<='".$dateList['lastYear']."' AND Type IN ( $TypeStr ) group by Type) b where a.Type= b.Type and a.Date= b.Date and Area=0");
        if ($TypeStr != 6)$this->sumStock($thisDate,$lastDate,$lastMonth,$lastYear);
        return array("thisDate"=>$thisDate,"lastDate"=>$lastDate,"lastMonth"=>$lastMonth,"lastYear"=>$lastYear);
    }

    /**
     * 获取数据表 本旬 上旬 上月 上年数据 单个字段
     * Created by zfy.
     * Date:2024/5/21 16:03
     * @param $dateList
     * @param $fields
     * @param $tableName
     * @param $where
     * @return array
     */
    public function getDataTableInfo($dateList,$fields,$tableName,$where){
        $thisDate = $this->getOne("select $fields from $tableName where $where and dta_ym <= '".$dateList['thisDate']."' order by dta_ym desc limit 1");
        $lastDate = $this->getOne("select $fields from $tableName where $where and dta_ym <= '".$dateList['lastDate']."' order by dta_ym desc limit 1");
        $lastMonth = $this->getOne("select $fields from $tableName where $where and dta_ym <= '".$dateList['lastMonth']."' order by dta_ym desc limit 1");
        $lastYear = $this->getOne("select $fields from $tableName where $where and dta_ym <= '".$dateList['lastYear']."' order by dta_ym desc limit 1");
        return array("thisDate"=>$thisDate,"lastDate"=>$lastDate,"lastMonth"=>$lastMonth,"lastYear"=>$lastYear);
    }

    /**
     * 获取数据表 本旬 上旬 上月 上年数据 多个字段
     * Created by zfy.
     * Date:2024/5/21 16:03
     * @param $dateList
     * @param $fields
     * @param $tableName
     * @param $where
     * @return array
     */
    public function getDataTableInfoRow($dateList,$fields,$tableName,$where){
        $thisDate = $this->getRow("select $fields from $tableName where $where and dta_ym <= '".$dateList['thisDate']."' order by dta_ym desc limit 1");
        $lastDate = $this->getRow("select $fields from $tableName where $where and dta_ym <= '".$dateList['lastDate']."' order by dta_ym desc limit 1");
        $lastMonth = $this->getRow("select $fields from $tableName where $where and dta_ym <= '".$dateList['lastMonth']."' order by dta_ym desc limit 1");
        $lastYear = $this->getRow("select $fields from $tableName where $where and dta_ym <= '".$dateList['lastYear']."' order by dta_ym desc limit 1");
        return array("thisDate"=>$thisDate,"lastDate"=>$lastDate,"lastMonth"=>$lastMonth,"lastYear"=>$lastYear);
    }

    /**
     * 获取钢厂库存 可用天数 本旬 上旬 上月 上年数据
     * Created by zfy.
     * Date:2024/5/21 17:18
     * @param $dateList
     * @return array
     */
    public function getSteelStockAndDay($dateList)
    {
        $thisDate = $this->getRow("select ImportKuCunNum,ImportKuCunDays from TieKuangShiKuCun where sc_id=-1 and managerid=-1 and Date <= '".$dateList['thisDate']."' order by Date desc limit 1");
        $lastDate = $this->getRow("select ImportKuCunNum,ImportKuCunDays from TieKuangShiKuCun where sc_id=-1 and managerid=-1 and Date <= '".$dateList['lastDate']."' order by Date desc limit 1");
        $lastMonth = $this->getRow("select ImportKuCunNum,ImportKuCunDays from TieKuangShiKuCun where sc_id=-1 and managerid=-1 and Date <= '".$dateList['lastMonth']."' order by Date desc limit 1");
        $lastYear = $this->getRow("select ImportKuCunNum,ImportKuCunDays from TieKuangShiKuCun where sc_id=-1 and managerid=-1 and Date <= '".$dateList['lastYear']."' order by Date desc limit 1");
        return array("thisDate"=>$thisDate,"lastDate"=>$lastDate,"lastMonth"=>$lastMonth,"lastYear"=>$lastYear);
    }

    /**
     * 获取市场+钢厂库存的本旬 上旬 上月 上年数据
     * Created by zfy.
     * Date:2023/1/10 11:23
     * @param $dateList
     * @param $typeStr
     * @return array
     */
    public function getSteelMarketStock($dateList,$typeStr){
        $thisDate = $this->Aquery("select a.type,a.value from gcsckucun_hz a,(select type,max(date) date from gcsckucun_hz where date<='".$dateList['thisDate']."' AND type IN ( $typeStr ) group by type) b where a.type= b.type and a.date= b.date ");
        $lastDate = $this->Aquery("select a.type,a.value from gcsckucun_hz a,(select type,max(date) date from gcsckucun_hz where date<='".$dateList['lastDate']."' AND type IN ( $typeStr ) group by type) b where a.type= b.type and a.date= b.date ");
        $lastMonth = $this->Aquery("select a.type,a.value from gcsckucun_hz a,(select type,max(date) date from gcsckucun_hz where date<='".$dateList['lastMonth']."' AND type IN ( $typeStr ) group by type) b where a.type= b.type and a.date= b.date ");
        $lastYear = $this->Aquery("select a.type,a.value from gcsckucun_hz a,(select type,max(date) date from gcsckucun_hz where date<='".$dateList['lastYear']."' AND type IN ( $typeStr ) group by type) b where a.type= b.type and a.date= b.date ");
        if ($typeStr != 6)$this->sumStock($thisDate,$lastDate,$lastMonth,$lastYear);
        return array("thisDate"=>$thisDate,"lastDate"=>$lastDate,"lastMonth"=>$lastMonth,"lastYear"=>$lastYear);
    }

    /**
     * 焦炭库存钢厂库存量
     * Created by zfy.
     * Date:2024/5/27 14:19
     * @param $dateList
     * @return array
     */
    public function getCokeStock($dateList)
    {
        $thisDate = $this->getOne("select CurrStock from MeiJiaoNeedAndStockHuiZong WHERE  `Area` =7 AND  `PinZhong` =1 and HuiZongDate <= '".$dateList['thisDate']." 23:59:59' order by HuiZongDate desc limit 1");
        $lastDate = $this->getOne("select CurrStock from MeiJiaoNeedAndStockHuiZong WHERE  `Area` =7 AND  `PinZhong` =1 and HuiZongDate <= '".$dateList['lastDate']." 23:59:59' order by HuiZongDate desc limit 1");
        $lastMonth = $this->getOne("select CurrStock from MeiJiaoNeedAndStockHuiZong WHERE  `Area` =7 AND  `PinZhong` =1 and HuiZongDate <= '".$dateList['lastMonth']." 23:59:59' order by HuiZongDate desc limit 1");
        $lastYear = $this->getOne("select CurrStock from MeiJiaoNeedAndStockHuiZong WHERE  `Area` =7 AND  `PinZhong` =1 and HuiZongDate <= '".$dateList['lastYear']." 23:59:59' order by HuiZongDate desc limit 1");
        return array("thisDate"=>$thisDate,"lastDate"=>$lastDate,"lastMonth"=>$lastMonth,"lastYear"=>$lastYear);
    }

    /**
     * 100家独立焦企库存量
     * Created by zfy.
     * Date:2024/5/27 14:29
     * @param $dateList
     * @return array
     */
    public function getCokeStock100($dateList)
    {
        $thisDate = $this->getrow("select jt_kc,ljm_kcw,ljm_kc from sc_othercom_stock_hz WHERE  AreaCode=0 and Date <= '".$dateList['thisDate']."' order by Date desc limit 1");
        $lastDate = $this->getrow("select jt_kc,ljm_kcw,ljm_kc from sc_othercom_stock_hz WHERE  AreaCode=0 and Date <= '".$dateList['lastDate']."' order by Date desc limit 1");
        $lastMonth = $this->getrow("select jt_kc,ljm_kcw,ljm_kc from sc_othercom_stock_hz WHERE  AreaCode=0 and Date <= '".$dateList['lastMonth']."' order by Date desc limit 1");
        $lastYear = $this->getrow("select jt_kc,ljm_kcw,ljm_kc from sc_othercom_stock_hz WHERE  AreaCode=0 and Date <= '".$dateList['lastYear']."' order by Date desc limit 1");
        return array("thisDate"=>$thisDate,"lastDate"=>$lastDate,"lastMonth"=>$lastMonth,"lastYear"=>$lastYear);
    }

    /**
     * 获取datatable base表联查数据
     * Created by zfy.
     * Date:2024/5/27 14:36
     * @param $dateList
     * @param $field
     * @param $table
     * @param $where
     * @return array
     */
    public function getDataTableBaseInfo($dateList,$field,$table,$where)
    {
        $thisDate = $this->getOne("select $field from $table where $where and date <= '".$dateList['thisDate']."' order by date desc limit 1");
        $lastDate = $this->getOne("select $field from $table where $where and date <= '".$dateList['lastDate']."' order by date desc limit 1");
        $lastMonth = $this->getOne("select $field from $table where $where and date <= '".$dateList['lastMonth']."' order by date desc limit 1");
        $lastYear = $this->getOne("select $field from $table where $where and date <= '".$dateList['lastYear']."' order by date desc limit 1");
        return array("thisDate"=>$thisDate,"lastDate"=>$lastDate,"lastMonth"=>$lastMonth,"lastYear"=>$lastYear);
    }

    /**
     * 获取最新库存的本旬日期
     * Created by zfy.
     * Date:2023/1/10 14:31
     * @return mixed
     */
    public function getNewStockDate($dateList){
        return $this->getOne("select date from gcsckucun_hz where date<='".$dateList['thisDate']."' order by id desc limit 1");
    }

    /**
     * 获取高炉、电炉、焦化开工率产能利用率
     * Created by zfy.
     * Date:2024/5/28 10:19
     * @param $dateList
     * @param $operatingSort
     * @return array
     */
    public function getOperatingInfo($dateList,$operatingSort){
        $typeStr = implode("','",$operatingSort);
        $thisDate = $this->query("SELECT a.dta_type,a.dta_2,a.dta_3 FROM data_table a, (SELECT dta_type, MAX( dta_ym) dta_ym FROM  data_table  WHERE dta_ym<= '".$dateList['thisDate']."' AND dta_type IN ('$typeStr') and dta_1='全国' GROUP BY dta_type)b WHERE a.dta_type= b.dta_type AND a.dta_ym= b.dta_ym and dta_1='全国'");
        $thisDate = $this->AqueryMoreFields($thisDate,"dta_type");
        $lastDate = $this->query("SELECT a.dta_type,a.dta_2,a.dta_3 FROM data_table a, (SELECT dta_type, MAX( dta_ym) dta_ym FROM  data_table  WHERE dta_ym<= '".$dateList['lastDate']."' AND dta_type IN ('$typeStr') and dta_1='全国' GROUP BY dta_type)b WHERE a.dta_type= b.dta_type AND a.dta_ym= b.dta_ym and dta_1='全国'");
        $lastDate = $this->AqueryMoreFields($lastDate,"dta_type");
        $lastMonth = $this->query("SELECT a.dta_type,a.dta_2,a.dta_3 FROM data_table a, (SELECT dta_type, MAX( dta_ym) dta_ym FROM  data_table  WHERE dta_ym<= '".$dateList['lastMonth']."' AND dta_type IN ('$typeStr') and dta_1='全国' GROUP BY dta_type)b WHERE a.dta_type= b.dta_type AND a.dta_ym= b.dta_ym and dta_1='全国'");
        $lastMonth = $this->AqueryMoreFields($lastMonth,"dta_type");
        $lastYear = $this->query("SELECT a.dta_type,a.dta_2,a.dta_3 FROM data_table a, (SELECT dta_type, MAX( dta_ym) dta_ym FROM  data_table  WHERE dta_ym<= '".$dateList['lastYear']."' AND dta_type IN ('$typeStr') and dta_1='全国' GROUP BY dta_type)b WHERE a.dta_type= b.dta_type AND a.dta_ym= b.dta_ym and dta_1='全国'");
        $lastYear = $this->AqueryMoreFields($lastYear,"dta_type");
        return array("thisDate"=>$thisDate,"lastDate"=>$lastDate,"lastMonth"=>$lastMonth,"lastYear"=>$lastYear);
    }

    public function getOperating_area_hz($dateList){
        $thisDate = $this->query("SELECT dta_vartype,dta_2,dta_3,dta_4,dta_5,dta_6,dta_7,dta_8 FROM `sc_operating_area_hz` WHERE `dta_vartype` IN ('3','5') AND `CityId` = '-1' and acttime<='".$dateList['thisDate']."' ORDER BY `id` DESC limit 2");
        $thisDate = $this->AqueryMoreFields($thisDate,"dta_vartype");

        $lastDate = $this->query("SELECT dta_vartype,dta_2,dta_3,dta_4,dta_5,dta_6,dta_7,dta_8 FROM `sc_operating_area_hz` WHERE `dta_vartype` IN ('3','5') AND `CityId` = '-1' and acttime<='".$dateList['lastDate']."' ORDER BY `id` DESC limit 2");
        $lastDate = $this->AqueryMoreFields($lastDate,"dta_vartype");

        return array("thisDate"=>$thisDate,"lastDate"=>$lastDate);
    }

    public function getOperating_stcom_count($dateList){
        $sql = "SELECT curEndDate FROM `sc_operating_base`  WHERE dta_vartype = '3' and `curEndDate`<='".$dateList['thisDate']."' order by id DESC limit 1 ";
        $curEndDate = $this->getOne($sql);

        $sql = "SELECT count(*) as num FROM `sc_operating` WHERE `baseid` in (SELECT id FROM `sc_operating_base`  WHERE dta_vartype = '3' and `curEndDate`='".$curEndDate."' )  ";
        $dta_vartype3 = $this->getOne($sql);

        $sql = "SELECT curEndDate FROM `sc_operating_base`  WHERE dta_vartype = '5' and `curEndDate`<='".$dateList['thisDate']."' order by id DESC limit 1 ";
        $curEndDate = $this->getOne($sql);

        $sql = "SELECT count(*) as num FROM `sc_operating` WHERE `baseid` in (SELECT id FROM `sc_operating_base`  WHERE dta_vartype = '5' and `curEndDate`='".$curEndDate."' )  ";
        $dta_vartype5 = $this->getOne($sql);

        $arr = array();
        $arr['3'] = $dta_vartype3;
        $arr['5'] = $dta_vartype5;
        return $arr;
    }

    /**
     * 处理数组 将数组中的值作为键
     * Created by zfy.
     * Date:2024/8/23 9:44
     * @param $list
     * @param $keyFields
     * @return array
     */
    protected function AqueryMoreFields($list,$keyFields)
    {
        $ret = [];
        foreach ($list as $index => $item) {
            $key = $item[$keyFields];
            unset($item[$keyFields]);
            $ret[$key] = $item;
        }
        return $ret;
    }

    /**
     * 获取焦化开工率产能利用率
     * Created by zfy.
     * Date:2024/5/28 10:23
     * @param $dateList
     * @return array
     */
    public function getCokeOperatingInfo($dateList)
    {
        $thisDate = $this->getOne("select KaiGonglv2 from sc_KaiGongLvHuiZong where AreaCode=0 and Date <= '".$dateList['thisDate']."' order by Date desc limit 1");
        $lastDate = $this->getOne("select KaiGonglv2 from sc_KaiGongLvHuiZong where AreaCode=0 and Date <= '".$dateList['lastDate']."' order by Date desc limit 1");
        $lastMonth = $this->getOne("select KaiGonglv2 from sc_KaiGongLvHuiZong where AreaCode=0 and Date <= '".$dateList['lastMonth']."' order by Date desc limit 1");
        $lastYear = $this->getOne("select KaiGonglv2 from sc_KaiGongLvHuiZong where AreaCode=0 and Date <= '".$dateList['lastYear']."' order by Date desc limit 1");
        return array("thisDate"=>$thisDate,"lastDate"=>$lastDate,"lastMonth"=>$lastMonth,"lastYear"=>$lastYear);
    }

    /**
     * 获取日均成交量数据
     * Created by zfy.
     * Date:2023/1/10 17:05
     * @param $dateList
     * @param $turnoverSort
     * @return array
     */
    public function getTurnoverInfo($dateList,$turnoverSort){
        $typeStr = implode("','",$turnoverSort);
        $thisDate = $this->Aquery("select a.PinZhong,a.value from ChengJiaoLiangDiaoChaTotalInfo a,(select PinZhong,max(EndDate) EndDate from ChengJiaoLiangDiaoChaTotalInfo where EndDate <='".$dateList['thisDate']."' AND PinZhong IN ('$typeStr') AND TYPE =2
AND `cityid` = '-1' group by PinZhong) b where a.PinZhong= b.PinZhong and a.EndDate= b.EndDate AND TYPE =2 AND `cityid` = '-1'");
        $lastDate = $this->Aquery("select a.PinZhong,a.value from ChengJiaoLiangDiaoChaTotalInfo a,(select PinZhong,max(EndDate) EndDate from ChengJiaoLiangDiaoChaTotalInfo where EndDate <='".$dateList['lastDate']."' AND PinZhong IN ('$typeStr') AND TYPE =2
AND `cityid` = '-1' group by PinZhong) b where a.PinZhong= b.PinZhong and a.EndDate= b.EndDate AND TYPE =2 AND `cityid` = '-1'");
        $lastMonth = $this->Aquery("select a.PinZhong,a.value from ChengJiaoLiangDiaoChaTotalInfo a,(select PinZhong,max(EndDate) EndDate from ChengJiaoLiangDiaoChaTotalInfo where EndDate <='".$dateList['lastMonth']."' AND PinZhong IN ('$typeStr') AND TYPE =2
AND `cityid` = '-1' group by PinZhong) b where a.PinZhong= b.PinZhong and a.EndDate= b.EndDate AND TYPE =2 AND `cityid` = '-1'");
        $lastYear = $this->Aquery("select a.PinZhong,a.value from ChengJiaoLiangDiaoChaTotalInfo a,(select PinZhong,max(EndDate) EndDate from ChengJiaoLiangDiaoChaTotalInfo where EndDate <='".$dateList['lastYear']."' AND PinZhong IN ('$typeStr') AND TYPE =2
AND `cityid` = '-1' group by PinZhong) b where a.PinZhong= b.PinZhong and a.EndDate= b.EndDate AND TYPE =2 AND `cityid` = '-1'");
        return array("thisDate"=>$thisDate,"lastDate"=>$lastDate,"lastMonth"=>$lastMonth,"lastYear"=>$lastYear);
    }

    public function getNewOperatingDate($dateList){
        return $this->getOne("select dta_ym from data_table where dta_type='GC_KGL' and dta_ym<='".$dateList['thisDate']."' order by id desc limit 1");
    }

    /**
     * 获取钢材成本
     * Created by zfy.
     * Date:2024/5/28 10:43
     * @param $dateList
     * @param $sort
     * @return array
     */
    public function getCostAndProfitInfo($dateList,$sort){
        $typeStr = implode(",",$sort);
        $thisDate = $this->Aquery("select a.type,a.chenben_tax from sg_HangYeChengBenIndex a,(select type,max(ndate) ndate from sg_HangYeChengBenIndex where ndate<='".$dateList['thisDate']."' AND type IN ($typeStr) AND mc_type=0 group by type) b where a.type= b.type and a.ndate= b.ndate and  mc_type=0");
        $lastDate = $this->Aquery("select a.type,a.chenben_tax from sg_HangYeChengBenIndex a,(select type,max(ndate) ndate from sg_HangYeChengBenIndex where ndate<='".$dateList['lastDate']."' AND type IN ($typeStr) AND mc_type=0 group by type) b where a.type= b.type and a.ndate= b.ndate and  mc_type=0");
        $lastMonth = $this->Aquery("select a.type,a.chenben_tax from sg_HangYeChengBenIndex a,(select type,max(ndate) ndate from sg_HangYeChengBenIndex where ndate<='".$dateList['lastMonth']."' AND type IN ($typeStr) AND mc_type=0 group by type) b where a.type= b.type and a.ndate= b.ndate and  mc_type=0");
        $lastYear = $this->Aquery("select a.type,a.chenben_tax from sg_HangYeChengBenIndex a,(select type,max(ndate) ndate from sg_HangYeChengBenIndex where ndate<='".$dateList['lastYear']."' AND type IN ($typeStr) AND mc_type=0 group by type) b where a.type= b.type and a.ndate= b.ndate and  mc_type=0");
        return array("thisDate"=>$thisDate,"lastDate"=>$lastDate,"lastMonth"=>$lastMonth,"lastYear"=>$lastYear);
    }

    /**
     * 获取钢材成本毛利计算所需的市场价格信息
     * Created by zfy.
     * Date:2023/1/11 16:19
     * @param $dateList
     * @param $sort
     * @return array
     */
    public function getMarketPriceInfo($dateList,$sort){
        $idList = $this->getPriceIdStr($sort);
        $id6 = $idList['id6'];
        $id7 = $idList['id7'];
        $thisDate6 = [];
        $lastDate6 = [];
        $lastMonth6 = [];
        $lastYear6 = [];
        $thisDate7 = [];
        $lastDate7 = [];
        $lastMonth7 = [];
        $lastYear7 = [];


        if ($id6) {
            $thisDate6 = $this->Aquery("select a.topicture as priceid,a.price from marketconditions a,(select topicture,max(mconmanagedate) mconmanagedate from marketconditions where mconmanagedate<='" . $dateList['thisDate'] . " 23:59:59' AND topicture IN ('$id6') group by topicture) b where a.topicture= b.topicture and a.mconmanagedate= b.mconmanagedate");
            $lastDate6 = $this->Aquery("select a.topicture as priceid,a.price from marketconditions a,(select topicture,max(mconmanagedate) mconmanagedate from marketconditions where mconmanagedate<='" . $dateList['lastDate'] . " 23:59:59' AND topicture IN ('$id6') group by topicture) b where a.topicture= b.topicture and a.mconmanagedate= b.mconmanagedate");
            $lastMonth6 = $this->Aquery("select a.topicture as priceid,a.price from marketconditions a,(select topicture,max(mconmanagedate) mconmanagedate from marketconditions where mconmanagedate<='" . $dateList['lastMonth'] . " 23:59:59' AND topicture IN ('$id6') group by topicture) b where a.topicture= b.topicture and a.mconmanagedate= b.mconmanagedate");
            $lastYear6 = $this->Aquery("select a.topicture as priceid,a.price from marketconditions a,(select topicture,max(mconmanagedate) mconmanagedate from marketconditions where mconmanagedate<='" . $dateList['lastYear'] . " 23:59:59' AND topicture IN ('$id6') group by topicture) b where a.topicture= b.topicture and a.mconmanagedate= b.mconmanagedate");
        }

        if ($id7) {
            $thisDate7 = $this->Aquery("select a.mastertopid as priceid,a.price from marketconditions a,(select mastertopid,max(mconmanagedate) mconmanagedate from marketconditions where mconmanagedate<='" . $dateList['thisDate'] . " 23:59:59' AND mastertopid IN ('$id7') group by mastertopid) b where a.mastertopid= b.mastertopid and a.mconmanagedate= b.mconmanagedate");
            $lastDate7 = $this->Aquery("select a.mastertopid as priceid,a.price from marketconditions a,(select mastertopid,max(mconmanagedate) mconmanagedate from marketconditions where mconmanagedate<='" . $dateList['lastDate'] . " 23:59:59' AND mastertopid IN ('$id7') group by mastertopid) b where a.mastertopid= b.mastertopid and a.mconmanagedate= b.mconmanagedate");
            $lastMonth7 = $this->Aquery("select a.mastertopid as priceid,a.price from marketconditions a,(select mastertopid,max(mconmanagedate) mconmanagedate from marketconditions where mconmanagedate<='" . $dateList['lastMonth'] . " 23:59:59' AND mastertopid IN ('$id7') group by mastertopid) b where a.mastertopid= b.mastertopid and a.mconmanagedate= b.mconmanagedate");
            $lastYear7 = $this->Aquery("select a.mastertopid as priceid,a.price from marketconditions a,(select mastertopid,max(mconmanagedate) mconmanagedate from marketconditions where mconmanagedate<='" . $dateList['lastYear'] . " 23:59:59' AND mastertopid IN ('$id7') group by mastertopid) b where a.mastertopid= b.mastertopid and a.mconmanagedate= b.mconmanagedate");
        }
        return array("thisDate"=>($thisDate6+$thisDate7),"lastDate"=>($lastDate6+$lastDate7),"lastMonth"=>($lastMonth6+$lastMonth7),"lastYear"=>($lastYear6+$lastYear7));
    }

    /**
     * 获取焦企利润
     * Created by zfy.
     * Date:2024/5/28 16:23
     * @param $dateList
     * @return array
     */
    public function getJqLiRun($dateList)
    {
        $thisDate = $this->getrow("select ROUND(China) as China,ROUND(ShanXi) as ShanXi,ROUND(HeBei) as HeBei,ROUND(HeNan) as HeNan,ROUND(ShanDong) as ShanDong from JiaoQiLiLunDunLiRun WHERE  date <= '".$dateList['thisDate']."' order by date desc limit 1");
        $lastDate = $this->getrow("select ROUND(China) as China,ROUND(ShanXi) as ShanXi,ROUND(HeBei) as HeBei,ROUND(HeNan) as HeNan,ROUND(ShanDong) as ShanDong from JiaoQiLiLunDunLiRun WHERE  date <= '".$dateList['lastDate']."' order by date desc limit 1");
        $lastMonth = $this->getrow("select ROUND(China) as China,ROUND(ShanXi) as ShanXi,ROUND(HeBei) as HeBei,ROUND(HeNan) as HeNan,ROUND(ShanDong) as ShanDong from JiaoQiLiLunDunLiRun WHERE  date <= '".$dateList['lastMonth']."' order by date desc limit 1");
        $lastYear = $this->getrow("select ROUND(China) as China,ROUND(ShanXi) as ShanXi,ROUND(HeBei) as HeBei,ROUND(HeNan) as HeNan,ROUND(ShanDong) as ShanDong from JiaoQiLiLunDunLiRun WHERE  date <= '".$dateList['lastYear']."' order by date desc limit 1");
        return array("thisDate"=>$thisDate,"lastDate"=>$lastDate,"lastMonth"=>$lastMonth,"lastYear"=>$lastYear);
    }

    //获取焦企利润--干熄焦
    public function getJqLiRun2($dateList)
    {
        $area_arr = array("China","山西","山东","河北","河南");
        $ret_arr = array("thisDate"=>array(),"lastDate"=>array(),"lastMonth"=>array(),"lastYear"=>array());
        foreach($area_arr as $area){
            $thisDate = $this->getrow("select * from jiaoqi_theory_profit_dry WHERE area = '".$area."' and date <= '".$dateList['thisDate']."' order by date desc limit 1");
            $lastDate = $this->getrow("select * from jiaoqi_theory_profit_dry WHERE area = '".$area."' and date <= '".$dateList['lastDate']."' order by date desc limit 1");
            $lastMonth = $this->getrow("select * from jiaoqi_theory_profit_dry WHERE area = '".$area."' and date <= '".$dateList['lastMonth']."' order by date desc limit 1");
            $lastYear = $this->getrow("select * from jiaoqi_theory_profit_dry WHERE area = '".$area."' and date <= '".$dateList['lastYear']."' order by date desc limit 1");
            $ret_arr["thisDate"][$area] = round($thisDate["value"]);
            $ret_arr["lastDate"][$area] = round($lastDate["value"]);
            $ret_arr["lastMonth"][$area] = round($lastMonth["value"]);
            $ret_arr["lastYear"][$area] = round($lastYear["value"]);
        }
        
        return $ret_arr;
    }

    /**
     * 硅铁、硅锰成本
     * Created by zfy.
     * Date:2024/5/29 17:09
     * @param $dateList
     * @param $type
     * @return array
     */
    public function getSiliconTinInfo($dateList,$type){
        $thisDate = $this->Aquery("select type,chenben_tax from HJHangYeChengBenIndex,HJHangYeChengBenIndex_post where HJHangYeChengBenIndex.id=HJHangYeChengBenIndex_post.pid and type='$type' and ndate <= '".$dateList['thisDate']."' order by ndate desc limit 1");
        $lastDate = $this->Aquery("select type,chenben_tax from HJHangYeChengBenIndex,HJHangYeChengBenIndex_post where HJHangYeChengBenIndex.id=HJHangYeChengBenIndex_post.pid and type='$type' and ndate <= '".$dateList['lastDate']."' order by ndate desc limit 1");
        $lastMonth = $this->Aquery("select type,chenben_tax from HJHangYeChengBenIndex,HJHangYeChengBenIndex_post where HJHangYeChengBenIndex.id=HJHangYeChengBenIndex_post.pid and type='$type' and ndate <= '".$dateList['lastMonth']."' order by ndate desc limit 1");
        $lastYear = $this->Aquery("select type,chenben_tax from HJHangYeChengBenIndex,HJHangYeChengBenIndex_post where HJHangYeChengBenIndex.id=HJHangYeChengBenIndex_post.pid and type='$type' and ndate <= '".$dateList['lastYear']."' order by ndate desc limit 1");
        return array("thisDate"=>$thisDate,"lastDate"=>$lastDate,"lastMonth"=>$lastMonth,"lastYear"=>$lastYear);
    }

    /**
     * 市场信息内容获取
     * Created by zfy.
     * Date:2024/6/3 17:47
     * @param $dateList
     * @param $type
     * @param $news_type
     * @return mixed
     */
    public function getMarketNews($dateList,$type,$news_type){
        return $this->getOne("select content from sd_steel_marketNews where CDate >= '".$dateList['this_start_date']."' and CDate <= '".$dateList['this_end_date']."' and type=$type and news_type=$news_type order by CDate desc limit 1");
    }

    /**
     * 获取下周预测数据
     * Created by zfy.
     * Date:2024/6/4 14:35
     * @param $start_date
     * @param $end_date
     * @param int $yc_type
     * @return array
     */
    public function getLastWeekForecast($start_date, $end_date, $yc_type = 2,$isChinese = 0){
        $LastWeekForecast = $this->query("select yc_varietyname,yc_direction,yc_fudu1,yc_fudu2,yc_sdate,yc_edate from sd_steel_day_weeks_month_predict where yc_sdate>='".$start_date."' and yc_sdate<='".$end_date."' and yc_type='$yc_type'");
        $retList = array();
        if ($isChinese) {
            $zdList = array(1 => "上涨<br/>", 2 => "持平", 3 => "下跌<br/>");
        }else{
            $zdList = array(1 => "+", 2 => "--", 3 => "-");
        }
        if ($LastWeekForecast) {
            foreach ($LastWeekForecast as $item) {
                $retList[$item['yc_varietyname']]['yc_direction'] = $item['yc_direction'];
                $retList[$item['yc_varietyname']]['yc_fudu1'] = $item['yc_fudu1'];
                $retList[$item['yc_varietyname']]['yc_fudu2'] = $item['yc_fudu2'];
                $retList[$item['yc_varietyname']]['yc_direction_name'] = $zdList[$item['yc_direction']];
                $retList[$item['yc_varietyname']]['yc_fudu'] = ($item['yc_fudu1'] == 0 && $item['yc_fudu2'] == 0) ? "—" : $item['yc_fudu1'] . "-" . $item['yc_fudu2'];
                $retList['dateStr'] = date("n月j日",strtotime($item['yc_sdate']))."至".date("n月j日",strtotime($item['yc_edate']));
            }
        }
        return $retList;
    }

    public function getSteelRepairInfo($start_date, $end_date,$varietyid){
        $varietyStr = implode("','",$varietyid);
        $sql = "select device.id,device.t1_field0,device.t1_field1,device.t1_field2,device.t1_field3,device.t1_field4,device.t1_field6,device.t1_field16,device.t1_field11,device.t1_field8,device.t1_field12,device.ResumptionPending,EliminationDate,ishuizong,stcom.name,stcom.area from sc_device device,sc_steelcom stcom where device.sc_id=stcom.id and ( ((t1_field12>='$start_date 00:00:00' and t1_field12<='$end_date 23:59:59') or (t1_field10>='$start_date 00:00:00' and t1_field10<='$end_date 23:59:59') or (t1_field11 < '$start_date 00:00:00' and t1_field12 > '$end_date 23:59:59') or (t1_field11>='$start_date 00:00:00') or ResumptionPending=1 ) and (t1_field6!=3  or (t1_field6=3 and EliminationDate>='$start_date' and EliminationDate<='$end_date' and EliminationDate>t1_field11)) ) and (t1_field11 != '0000-00-00 00:00:00' or t1_field12 != '0000-00-00 00:00:00')  and t1_field1 in ('$varietyStr')";
        $devices = $this->query($sql);
        return $devices;
    }

    public function get_fc_huizong_devices($start_date,$end_date,$varietyid){
        $varietyStr = implode("','",$varietyid);
        // $sql = "select device.id,device.t1_field0,device.t1_field1,device.t1_field2,device.t1_field3,device.t1_field4,device.t1_field6,device.t1_field16,device.t1_field11,device.t1_field8,device.t1_field12,device.ResumptionPending,EliminationDate,ishuizong,stcom.name,stcom.area from sc_device device,sc_steelcom stcom where device.sc_id=stcom.id and ( ((t1_field12>='$start_date 00:00:00' and t1_field12<='$end_date 23:59:59') or (t1_field10>='$start_date 00:00:00' and t1_field10<='$end_date 23:59:59') or (t1_field11 < '$start_date 00:00:00' and t1_field12 > '$end_date 23:59:59') or (t1_field11>='$start_date 00:00:00') or ResumptionPending=1 ) and (t1_field6!=3  or (t1_field6='1' or t1_field6='2' and EliminationDate>='$start_date' and EliminationDate<='$end_date' and EliminationDate>t1_field11)) ) and (t1_field11 != '0000-00-00 00:00:00' or t1_field12 != '0000-00-00 00:00:00')  and t1_field1 in ('$varietyStr')";
        $sql = "select device.id,device.t1_field0,device.t1_field1,device.t1_field2,device.t1_field3,device.t1_field4,device.t1_field6,device.t1_field16,device.t1_field11,device.t1_field8,device.t1_field12,device.ResumptionPending,EliminationDate,ishuizong,stcom.name,stcom.area from sc_device device,sc_steelcom stcom where device.sc_id=stcom.id and (t1_field12>='$start_date 00:00:00' and t1_field12<='$end_date 23:59:59') and (t1_field11 != '0000-00-00 00:00:00' or t1_field12 != '0000-00-00 00:00:00') and t1_field1 in ('$varietyStr') and (t1_field6='1' or t1_field6='2') and ResumptionPending!=1";
        $devices = $this->query($sql);
        return $devices;
    }

    /**
     * 将价格id数组分开，分为6 和 7位价格id字符串
     * Created by zfy.
     * Date:2023/1/9 14:44
     * @param $allPriceIdList
     * @return array
     */
    protected function getPriceIdStr($allPriceIdList){
        $id6 = array();
        $id7 = array();
        foreach ($allPriceIdList as $idItem) {
            if (strlen($idItem) == 6) {
                $id6[] = $idItem;
            } elseif (strlen($idItem) == 7) {
                $id7[] = $idItem;
            }
        }
        $id6Str = implode("','",$id6);
        $id7Str = implode("','",$id7);
        return array('id6'=>$id6Str,'id7'=>$id7Str);
    }

    /**
     * 计算五大品种库存
     * Created by zfy.
     * Date:2023/1/10 11:46
     * @param $thisDate
     * @param $lastDate
     * @param $lastMonth
     * @param $lastYear
     */
    protected function sumStock(&$thisDate,&$lastDate,&$lastMonth,&$lastYear){
        $thisDate = $this->sumForeach($thisDate);
        $lastDate = $this->sumForeach($lastDate);
        $lastMonth = $this->sumForeach($lastMonth);
        if ($lastYear)$lastYear = $this->sumForeach($lastYear);
    }
    protected function sumForeach($data){
        $data[6] = 0;
        foreach ($data as $datum) {
            $data[6] +=$datum;
        }
        return $data;
    }


    function zhangdie($int,$symbolType = 0){
        if ($symbolType == 0){
            $up = "+";
            $down = "-";
        }else{
            $up = "↑";
            $down = "↓";
        }
        if($int<0){
            $intstr = "<font color=green><strong>".$down.abs($int)."</strong></font>";
        }elseif($int>0){
            $intstr = "<font color=red><strong>".$up.abs($int)."</strong></font>";
        }elseif($int==""){
            $intstr = "<strong>—</strong>";
        }else{
            $intstr = "<font ><strong>".$int."</strong></font>";
        }
        return $intstr;
    }
}

?>