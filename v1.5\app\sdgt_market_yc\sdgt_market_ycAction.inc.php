<?php
putenv('PUPPETEER_CACHE_DIR=/var/www/.cache/puppeteer');  // 指定一个 www 用户可访问的路径

require_once("/etc/steelconf/config/isholiday.php");
$GLOBALS['QH_TYPES'] = "'SHQHDAY_4','SHQHDAY_99','SHQHDAY_20','SHQHDAY_9','SHQHDAY_19','SHQHDAY_22','SHQHDAY_23'";
$GLOBALS['YUAN_YOU_BM'] = "10010140";
$GLOBALS['QH_TYPES_LIST'] = array('SHQHDAY_4','SHQHDAY_99','SHQHDAY_20','SHQHDAY_9','SHQHDAY_19','SHQHDAY_22','SHQHDAY_23');
//研究中心山钢预测录入也有一份，修改请同步修改；
$GL<PERSON><PERSON><PERSON>['priceid6'] = "'173012','183012','073012','403012','363012','694312','874312','D24312','074312','D24412','184412','074412','692043','172023','872023','182023','082023','697311','827311','077311','677311','287311','692243','702243','872243','122243','082243','188611','D28611','178210','678210','118210','238210','678411'";
$GLOBALS['priceid7'] = "'6931122','D231121','8231125','8731121','0731123','D252151','0752155','4189101','5989101','5789101','4389101','4188201','4388201','2063131','F363122','T263108','S263103','6663101','4363104','K163163','K163161','S263141','U263141','D283114','T283111','4383111','S283111','6683112','4187102','5987107','5887104','4487102','1843122','H986401','0762601'";
//调价
$GLOBALS['WEEK_ONLY_ID_LIST'] = array(
//    array("onlyid"=>'724ed9d55364232fd872dfb721e4671f',"gcid"=>"107"),//南钢 厚板
    array("onlyid"=>'e8aa1d1ca0b7f3e06f10b66b348777cf',"gcid"=>"301"),//首钢
    array("onlyid"=>'f83e2b2ed746e608f3b17015856bcd6e',"gcid"=>"205"),//湘钢
    array("onlyid"=>'8cda77d365b5bb47229fae484f50462c',"gcid"=>"202"),//安钢
    array("onlyid"=>'e23b2b44270a05ef68441f91ae55ce39',"gcid"=>"113"),//马钢 型钢
    array("onlyid"=>'53ce1f87ebeb11009fa030aef01486f2',"gcid"=>"346"),//津西
    array("onlyid"=>'1b5ccd23025fcb00a615886e5c97a07b',"gcid"=>"130"),//日钢
    array("onlyid"=>'cb106e74274d68b178be1193838d7e88',"gcid"=>"122"),//淮钢 特钢
    array("onlyid"=>'c31d0a6b954d86306ce07803509590b9',"gcid"=>"107"),//南钢
    array("onlyid"=>'ba75ede110dda0b6126af9a097fc4c98',"gcid"=>"1198"),//建龙
    array("onlyid"=>'bfbd857401c3572a27e56ff657307156',"gcid"=>"301"),//首钢 热轧
    array("onlyid"=>'084417e381720e39199d70e6d96bca73',"gcid"=>"364"),//唐钢
    array("onlyid"=>'f05f8808ef1d1b3c121ab2b3bfc45c21',"gcid"=>"130"),//日钢
    // array("onlyid"=>'f398cc7bca28d820200ec2a043f32163',"gcid"=>"1178"),//泰钢
    array("onlyid"=>'cf9172173a635aa3f87f5440fd9ca0b7',"gcid"=>"135"),//石横
    array("onlyid"=>'e9d127732432b67101504fc307a0418c',"gcid"=>"101"),//宝钢 冷轧
    array("onlyid"=>'e9d127732432b67101504fc307a0418c',"gcid"=>"301"),//首钢
    array("onlyid"=>'e9d127732432b67101504fc307a0418c',"gcid"=>"364"),//邯钢
    array("onlyid"=>'e9d127732432b67101504fc307a0418c',"gcid"=>"113"),//马钢
    array("onlyid"=>'e9d127732432b67101504fc307a0418c',"gcid"=>"204"),//涟钢
    array("onlyid"=>'056cb5417915d2b93385182334e45280',"gcid"=>"130"),//日钢
    array("onlyid"=>'3fbc5816cf358e260229a2d10ba94af9',"gcid"=>"1182"),//永锋 钢筋
    array("onlyid"=>'28b9cdc8e71c2dfbb94a4b049930f88e',"gcid"=>"135"),//石横
    array("onlyid"=>'eea4802d73864cf30ba51b46ea980b2c',"gcid"=>"120"),//沙钢
    array("onlyid"=>'ac0b6a6957145a33557a9927ab6129af',"gcid"=>"1596"),//镔鑫
    array("onlyid"=>'222053f85f7600b91abd7441d17f81d0',"gcid"=>"1540"),//晋南
);
//原燃料调价
$GLOBALS['WEEK_YRL_ONLY_ID_LIST'] = array(
    array("onlyid"=>'00ec5156766c9ae3b6ecd6e7384cafac',"gcid"=>"120"),//沙钢普碳废钢
    array("onlyid"=>'923c6ed2ffddb35c274140fb86df8c54',"gcid"=>"364"),//河钢集团硅铁
    array("onlyid"=>'85dee82d6323af2a2ed74c3b5a1819ed',"gcid"=>"364"),//河钢集团硅锰
    array("onlyid"=>'6512bd43d9caa6e02c990b0a82652dca',"gcid"=>"130"),//日照钢铁湿熄焦 
    array("onlyid"=>'5b5ff3806bb9961dc9e603eb4852aa8e',"gcid"=>"130"),//日照钢铁干熄焦
    array("onlyid"=>'14f501702ec44df127de2fbb701a2d21',"gcid"=>"364"),//河钢集团湿熄焦
);
//库存
$GLOBALS['STOCK_TYPE_STR'] = "'1', '2', '3', '4', '5'";

//预测品种name
$GLOBALS['YC_GC_NAME'] = array("厚板","热卷","冷卷","酸洗","镀锌","螺纹钢","H型钢","碳结钢","方坯");
$GLOBALS['YC_YL_NAME1'] = array("铁矿石","进口矿指数","废钢","硅锰","硅铁","高锰","镍板","气煤","1/3焦煤","主焦煤","瘦煤","冶金焦");
$GLOBALS['YC_YL_NAME2'] = array("铁矿石","废钢","硅锰","硅铁","高锰","镍板","气煤","1/3焦煤","主焦煤","瘦煤","冶金焦");
define( "DC_DOMAIN_URL",APP_URL_DC);
define( "DOMAIN_URL",DC_DOMAIN_URL."/v1.5");
define( "SG_DATACENTER_GUID","8184e36f606711ef89b5ec2a72108bae");
define( "DATACENTER_GUID","1f853150891e11eabebc001d09719b40");
define( "DATACENTER_SIGNCS","8b46e267268907263bbec91ec65915f4");
class sdgt_market_ycAction extends AbstractAction
{
	public function __construct()
	{
    	parent::__construct();
	}

	public function day_market_yc($params){
	    if($params['saving']=="2"){
            $params['saving']="1";
        }
		$day = $params["sdate"];
        if($day == ""){
            $day = $this->drcwdao->getOne("select yc_edate from sd_steel_day_weeks_month_predict where yc_type = 1 and status = 1 GROUP BY yc_type,yc_edate order by yc_edate desc limit 1");
            
        }
        $params["titleName"] = date("Y年n月j日",strtotime($this->get_xhdate2($day)))."分析预测";
		// $day = "2023-04-28";
		$lastday = $this->get_xhdate($day);
		
		//1、钢材、原燃料市场变化
		$ret_arr = $this->get_gc_yrl_data($day,$day,$lastday,$lastday);
        foreach($ret_arr as &$ret_v){
            $day_arr = explode("-",$ret_v["tprice"]["date"]);
            $day_arr2 = explode("-",$ret_v["lprice"]["date"]);
			$ret_v["tprice"]["date"] = $day_arr[0];
			$ret_v["lprice"]["date"] = $day_arr2[0];
		}

        if($params["save_price"] == 1){
            $this->save_price($ret_arr,1,$day);
        }

        //与上月末 上月均价 年内高点比较
        $this->get_yc_data_compare($ret_arr,$day);

        //钢厂检修
        $this->handleSteelRepair(['this_start_date'=>$day,"this_end_date"=>$day]);
		//取钢材昨预测数据
        $gc_day_yc_data = $this->_dao->get_yc_data(1,1,$day,$day);
		$gc_day_yc_str = $this->get_yc_table_tr($gc_day_yc_data,1,1);
        $gc_yc_price_str = "<tr>
                            <td>预测价格</td>
                            <td>".($ret_arr[0]['lprice']['houban_price'] + ($gc_day_yc_data['厚板']['yc_zd']))."</td>
                            <td>".($ret_arr[0]['lprice']['rzbj_price'] + ($gc_day_yc_data['热卷']['yc_zd']))."</td>
                            <td>".($ret_arr[0]['lprice']['lzbj_price'] + ($gc_day_yc_data['冷卷']['yc_zd']))."</td>
                            <td>".($ret_arr[0]['lprice']['suanxi_price'] + ($gc_day_yc_data['酸洗']['yc_zd']))."</td>
                            <td>".($ret_arr[0]['lprice']['duxin_price'] + ($gc_day_yc_data['镀锌']['yc_zd']))."</td>
                            <td>".($ret_arr[0]['lprice']['lw_price'] + ($gc_day_yc_data['螺纹钢']['yc_zd']))."</td>
                            <td>".($ret_arr[0]['lprice']['h_xg_price'] + ($gc_day_yc_data['H型钢']['yc_zd']))."</td>
                            <td>".($ret_arr[0]['lprice']['tjg_price'] + ($gc_day_yc_data['碳结钢']['yc_zd']))."</td>
                            <td>".($ret_arr[0]['lprice']['fp_price'] + ($gc_day_yc_data['方坯']['yc_zd']))."</td>
                        </tr>";
        $gc_day_yc_str .= $gc_yc_price_str;

		//取原料昨预测数据
        $ll_day_yc_data = $this->_dao->get_yc_data(1,2,$day,$day);
		$ll_day_yc_str = $this->get_yc_table_tr($ll_day_yc_data,2,1);
        $ll_yc_price_str = "<tr>
                            <td>预测价格</td>
                            <td>".($ret_arr[1]['lprice']['tks_price'] + ($ll_day_yc_data['铁矿石']['yc_zd']))."</td>
                            <td>--</td>
                            <td>".($ret_arr[1]['lprice']['fg_price'] + ($ll_day_yc_data['废钢']['yc_zd']))."</td>
                            <td>".($ret_arr[1]['lprice']['gm_price'] + ($ll_day_yc_data['硅锰']['yc_zd']))."</td>
                            <td>".($ret_arr[1]['lprice']['gt_price'] + ($ll_day_yc_data['硅铁']['yc_zd']))."</td>
                            <td>".($ret_arr[1]['lprice']['gtmt_price'] + ($ll_day_yc_data['高锰']['yc_zd']))."</td>
                            <td>".($ret_arr[1]['lprice']['nieban_price'] + ($ll_day_yc_data['镍板']['yc_zd']))."</td>
                            <td>".($ret_arr[1]['lprice']['qimei_price'] + ($ll_day_yc_data['气煤']['yc_zd']))."</td>
                            <td>".($ret_arr[1]['lprice']['jiaomei_price'] + ($ll_day_yc_data['1/3焦煤']['yc_zd']))."</td>
                            <td>".($ret_arr[1]['lprice']['dljm_price'] + ($ll_day_yc_data['主焦煤']['yc_zd']))."</td>
                            <td>".($ret_arr[1]['lprice']['shoumei_price'] + ($ll_day_yc_data['瘦煤']['yc_zd']))."</td>
                            <td>".($ret_arr[1]['lprice']['zyjj_price'] + ($ll_day_yc_data['冶金焦']['yc_zd']))."</td>
                        </tr>";
        $ll_day_yc_str .= $ll_yc_price_str;


		//3、矿煤焦钢期货主力合约及国际大宗商品、外汇市场收盘价 
		//data_table表：SHQHDAY_4、SHQHDAY_99、SHQHDAY_20、SHQHDAY_9、SHQHDAY_19、SHQHDAY_22、SHQHDAY_23、gfutures_shpi表取bianma=10010140、dolrate表：取rd2、Rmbrate表取rd1
		$qh_yy_shpi_type = array('SHQHDAY_4','SHQHDAY_99','SHQHDAY_20','SHQHDAY_9','SHQHDAY_19','SHQHDAY_22','SHQHDAY_23');
		$qh_yy_shpi_data = array();
		
		$qh_data = $this->get_qh_data($lastday,$day,$GLOBALS['QH_TYPES']);
		$yy_data = $this->getNiuYuanyou($lastday,$day,$GLOBALS['YUAN_YOU_BM']);
		$my_data = $this->getMeiYuanShpi($lastday,$day);
		$rmb_data = $this->getRMBShpi($lastday,$day);

		foreach($qh_data as $qh_key => $qh_item){
			if($qh_key == "tprice")
				$qh_yy_shpi_data[$qh_key]["date"] = $day;
			else if($qh_key == "lprice")
				$qh_yy_shpi_data[$qh_key]["date"] = $lastday;
			foreach($qh_yy_shpi_type as $type){
				if($qh_key == "tprice" || $qh_key == "lprice"){
					$qh_yy_shpi_data[$qh_key][$type] = $qh_item[$type];
				}
			}
		}

		foreach($yy_data as $yy_key => $yy_item){
			if($yy_key == "tprice" || $yy_key == "lprice"){
				$qh_yy_shpi_data[$yy_key]["10010140"] = $yy_item;
			}
		}

		foreach($my_data as $my_key => $my_item){
			if($my_key == "tprice" || $my_key == "lprice"){
				$qh_yy_shpi_data[$my_key]["meiyuan"] = $my_item;
			}
		}

		foreach($rmb_data as $rmb_key => $rmb_item){
			if($rmb_key == "tprice" || $rmb_key == "lprice"){
				$qh_yy_shpi_data[$rmb_key]["rmb"] = $rmb_item;
			}
		}
		
		$qh_yy_shpi_data["zd"]["zhangdie"] = "涨跌";
		foreach($qh_yy_shpi_data["tprice"] as $zd_key3 => $zd_v3){
			if($zd_key3 != "date"){
                if($qh_yy_shpi_data["tprice"][$zd_key3] != 0 && $qh_yy_shpi_data["lprice"][$zd_key3] != 0){
                    $qh_yy_shpi_data["zd"][$zd_key3] = $this->zhangdie ( $qh_yy_shpi_data["tprice"][$zd_key3] - $qh_yy_shpi_data["lprice"][$zd_key3] );
                }else{
                    $qh_yy_shpi_data["zd"][$zd_key3] = $this->zhangdie (0);
                }
            }
		}
		// echo "<pre/>";print_r($qh_data);print_r($yy_data);print_r($my_data);print_r($rmb_data);print_r($qh_yy_shpi_data);

		//二、竞争钢厂调价情况  暂无
        $dateList = array("this_start_date"=>$day,
            "this_end_date"=>$day,
            "last_start_date"=>$lastday,
            "last_end_date"=>$lastday);
        $this->handleSteelPrice($dateList);

		//三、市场热点事件及点评
		$sdgt_scyp = html_entity_decode($this->_dao->get_sdgt_marketNews(1,$day,$day,101));
        $this->assign( "sdgt_scyp", $sdgt_scyp);
        $this->assign( "type", "1");
        $this->assign( "adminid", $params['adminid']);
        $this->assign( "sdate", $day);
        $this->assign( "edate", $day);
		//市场热点及点评
        $sdgt_marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(1,$day,$day,0));
        $this->assign( "sdgt_marketNews", $sdgt_marketNews);
        //钢材市场价格变化情况
        $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(1,$day,$day,102));
        $this->assign( "marketNews102", $marketNews);
        //原燃料市场价格变化
        $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(1,$day,$day,103));
        $this->assign( "marketNews103", $marketNews);
        //期货分析录入
        $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(1,$day,$day,104));
        $this->assign( "marketNews104", $marketNews);
        //竞争钢厂调价情况
        $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(1,$day,$day,105));
        $this->assign( "marketNews105", $marketNews);
        //竞争钢厂设备检修与复产情况
        $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(1,$day,$day,106));
        $this->assign( "marketNews106", $marketNews);

		//四、XX月YY+1日市场价格预测
		//取钢材明天预测数据
		$tomorrow = $this->get_xhdate2($day);
        $gc_day_yc_data2 = $this->_dao->get_yc_data(1,1,$tomorrow,$tomorrow);
		$gc_day_yc_str2 = $this->get_yc_table_tr($gc_day_yc_data2,1,2);
        $gc_yc_price_str2 = "<tr>
                            <td>预测价格</td>
                            <td>".($ret_arr[0]['tprice']['houban_price'] + ($gc_day_yc_data2['厚板']['yc_zd']))."</td>
                            <td>".($ret_arr[0]['tprice']['rzbj_price'] + ($gc_day_yc_data2['热卷']['yc_zd']))."</td>
                            <td>".($ret_arr[0]['tprice']['lzbj_price'] + ($gc_day_yc_data2['冷卷']['yc_zd']))."</td>
                            <td>".($ret_arr[0]['tprice']['suanxi_price'] + ($gc_day_yc_data2['酸洗']['yc_zd']))."</td>
                            <td>".($ret_arr[0]['tprice']['duxin_price'] + ($gc_day_yc_data2['镀锌']['yc_zd']))."</td>
                            <td>".($ret_arr[0]['tprice']['lw_price'] + ($gc_day_yc_data2['螺纹钢']['yc_zd']))."</td>
                            <td>".($ret_arr[0]['tprice']['h_xg_price'] + ($gc_day_yc_data2['H型钢']['yc_zd']))."</td>
                            <td>".($ret_arr[0]['tprice']['tjg_price'] + ($gc_day_yc_data2['碳结钢']['yc_zd']))."</td>
                            <td>".($ret_arr[0]['tprice']['fp_price'] + ($gc_day_yc_data2['方坯']['yc_zd']))."</td>
                        </tr>";
        $gc_day_yc_str2 .= $gc_yc_price_str2;

		//取原料明天预测数据
        $ll_day_yc_data2 = $this->_dao->get_yc_data(1,2,$tomorrow,$tomorrow);
		$ll_day_yc_str2 = $this->get_yc_table_tr($ll_day_yc_data2,2,2);
        $ll_yc_price_str2 = "<tr>
                            <td>预测价格</td>
                            <td>".($ret_arr[1]['tprice']['tks_price'] + ($ll_day_yc_data2['铁矿石']['yc_zd']))."</td>
                            <td>".($ret_arr[1]['tprice']['fg_price'] + ($ll_day_yc_data2['废钢']['yc_zd']))."</td>
                            <td>".($ret_arr[1]['tprice']['gm_price'] + ($ll_day_yc_data2['硅锰']['yc_zd']))."</td>
                            <td>".($ret_arr[1]['tprice']['gt_price'] + ($ll_day_yc_data2['硅铁']['yc_zd']))."</td>
                            <td>".($ret_arr[1]['tprice']['gtmt_price'] + ($ll_day_yc_data2['高锰']['yc_zd']))."</td>
                            <td>".($ret_arr[1]['tprice']['nieban_price'] + ($ll_day_yc_data2['镍板']['yc_zd']))."</td>
                            <td>".($ret_arr[1]['tprice']['qimei_price'] + ($ll_day_yc_data2['气煤']['yc_zd']))."</td>
                            <td>".($ret_arr[1]['tprice']['jiaomei_price'] + ($ll_day_yc_data2['1/3焦煤']['yc_zd']))."</td>
                            <td>".($ret_arr[1]['tprice']['dljm_price'] + ($ll_day_yc_data2['主焦煤']['yc_zd']))."</td>
                            <td>".($ret_arr[1]['tprice']['shoumei_price'] + ($ll_day_yc_data2['瘦煤']['yc_zd']))."</td>
                            <td>".($ret_arr[1]['tprice']['zyjj_price'] + ($ll_day_yc_data2['冶金焦']['yc_zd']))."</td>
                        </tr>";
        $ll_day_yc_str2 .= $ll_yc_price_str2;

		$this->assign("gc_day_yc_str2",$gc_day_yc_str2);
		$this->assign("ll_day_yc_str2",$ll_day_yc_str2);

		$this->assign("gc_data",$ret_arr[0]);
		$this->assign("yrl_data",$ret_arr[1]);
		$this->assign("gc_day_yc_str",$gc_day_yc_str);
		$this->assign("ll_day_yc_str",$ll_day_yc_str);
		$this->assign("day",date('n月j日',strtotime($day)));
		// $this->assign("yrl_data",$ret_arr[1]);
		$this->assign("qh_yy_shpi_data",$qh_yy_shpi_data);
        $this->assign("mode",$params['mode']);
        $this->assign("params",$params);

        //保存明日预测价格
        if($params["save_price"] == 1){
            $ret_arr[0]['tprice']['houban_price'] = $ret_arr[0]['tprice']['houban_price'] + ($gc_day_yc_data2['厚板']['yc_zd']);
            $ret_arr[0]['tprice']['rzbj_price'] = $ret_arr[0]['tprice']['rzbj_price'] + ($gc_day_yc_data2['热卷']['yc_zd']);
            $ret_arr[0]['tprice']['lzbj_price'] = $ret_arr[0]['tprice']['lzbj_price'] + ($gc_day_yc_data2['冷卷']['yc_zd']);
            $ret_arr[0]['tprice']['suanxi_price'] = $ret_arr[0]['tprice']['suanxi_price'] + ($gc_day_yc_data2['酸洗']['yc_zd']);
            $ret_arr[0]['tprice']['duxin_price']  = $ret_arr[0]['tprice']['duxin_price'] + ($gc_day_yc_data2['镀锌']['yc_zd']);
            $ret_arr[0]['tprice']['lw_price'] = $ret_arr[0]['tprice']['lw_price'] + ($gc_day_yc_data2['螺纹钢']['yc_zd']);
            $ret_arr[0]['tprice']['h_xg_price'] = $ret_arr[0]['tprice']['h_xg_price'] + ($gc_day_yc_data2['H型钢']['yc_zd']);
            $ret_arr[0]['tprice']['tjg_price'] = $ret_arr[0]['tprice']['tjg_price'] + ($gc_day_yc_data2['碳结钢']['yc_zd']);
            $ret_arr[0]['tprice']['fp_price'] = $ret_arr[0]['tprice']['fp_price'] + ($gc_day_yc_data2['方坯']['yc_zd']);

            $ret_arr[1]['tprice']['tks_price'] = $ret_arr[1]['tprice']['tks_price'] + ($ll_day_yc_data2['铁矿石']['yc_zd']);
            $ret_arr[1]['tprice']['fg_price'] = $ret_arr[1]['tprice']['fg_price'] + ($ll_day_yc_data2['废钢']['yc_zd']);
            $ret_arr[1]['tprice']['gm_price'] = $ret_arr[1]['tprice']['gm_price'] + ($ll_day_yc_data2['硅锰']['yc_zd']);
            $ret_arr[1]['tprice']['gt_price'] = $ret_arr[1]['tprice']['gt_price'] + ($ll_day_yc_data2['硅铁']['yc_zd']);
            $ret_arr[1]['tprice']['gtmt_price'] = $ret_arr[1]['tprice']['gtmt_price'] + ($ll_day_yc_data2['高锰']['yc_zd']);
            $ret_arr[1]['tprice']['nieban_price'] = $ret_arr[1]['tprice']['nieban_price'] + ($ll_day_yc_data2['镍板']['yc_zd']);
            $ret_arr[1]['tprice']['qimei_price'] = $ret_arr[1]['tprice']['qimei_price'] + ($ll_day_yc_data2['气煤']['yc_zd']);
            $ret_arr[1]['tprice']['jiaomei_price'] = $ret_arr[1]['tprice']['jiaomei_price'] + ($ll_day_yc_data2['1/3焦煤']['yc_zd']);
            $ret_arr[1]['tprice']['dljm_price'] = $ret_arr[1]['tprice']['dljm_price'] + ($ll_day_yc_data2['主焦煤']['yc_zd']);
            $ret_arr[1]['tprice']['shoumei_price'] = $ret_arr[1]['tprice']['shoumei_price'] + ($ll_day_yc_data2['瘦煤']['yc_zd']);
            $ret_arr[1]['tprice']['zyjj_price'] = $ret_arr[1]['tprice']['zyjj_price'] + ($ll_day_yc_data2['冶金焦']['yc_zd']);
            $this->save_price($ret_arr,1,$tomorrow);
        }
    }

    public function save_price($data_arr,$type,$edate){
        $gc_arr = $data_arr[0];
        $ll_arr = $data_arr[1];
        $day_arr = explode("-",$gc_arr["tprice"]["date"]);
       
        // echo "<pre/>";print_r($day_arr);print_r($gc_arr);print_r($ll_arr);echo $edate;echo "</pre>";
        // $GLOBALS['YC_GC_NAME'] = array("厚板","热卷","冷卷","酸洗","镀锌","螺纹钢","H型钢","碳结钢","方坯");
        // $GLOBALS['YC_YL_NAME2'] = array("铁矿石","废钢","硅锰","硅铁","高锰","镍板","气煤","1/3焦煤","主焦煤","瘦煤","冶金焦");
        foreach($GLOBALS['YC_GC_NAME'] as $gc_key){
            $yc_price = 0;
            if($gc_key == "厚板"){
                $yc_price = $gc_arr["tprice"]["houban_price"];
            }else  if($gc_key == "热卷"){
                $yc_price = $gc_arr["tprice"]["rzbj_price"];
            }else  if($gc_key == "冷卷"){
                $yc_price = $gc_arr["tprice"]["lzbj_price"];
            }else  if($gc_key == "酸洗"){
                $yc_price = $gc_arr["tprice"]["suanxi_price"];
            }else  if($gc_key == "镀锌"){
                $yc_price = $gc_arr["tprice"]["duxin_price"];
            }else  if($gc_key == "螺纹钢"){
                $yc_price = $gc_arr["tprice"]["lw_price"];
            }else  if($gc_key == "H型钢"){
                $yc_price = $gc_arr["tprice"]["h_xg_price"];
            }else  if($gc_key == "碳结钢"){
                $yc_price = $gc_arr["tprice"]["tjg_price"];
            }else  if($gc_key == "方坯"){
                $yc_price = $gc_arr["tprice"]["fp_price"];
            }
            $this->_dao->execute("update sd_steel_day_weeks_month_predict set yc_price = '".$yc_price."' where yc_edate = '".$edate."' and yc_type = '".$type."' and yc_varietyname = '".$gc_key."' and channel = 1");
        }
        foreach($GLOBALS['YC_YL_NAME2'] as $ll_key){
            $yc_price = 0;
            if($ll_key == "铁矿石"){
                $yc_price = $ll_arr["tprice"]["tks_price"];
            }else if($ll_key == "废钢"){
                $yc_price = $ll_arr["tprice"]["fg_price"];
            }else if($ll_key == "硅锰"){
                $yc_price = $ll_arr["tprice"]["gm_price"];
            }else if($ll_key == "硅铁"){
                $yc_price = $ll_arr["tprice"]["gt_price"];
            }else if($ll_key == "高锰"){
                $yc_price = $ll_arr["tprice"]["gtmt_price"];
            }else if($ll_key == "镍板"){
                $yc_price = $ll_arr["tprice"]["nieban_price"];
            }else if($ll_key == "气煤"){
                $yc_price = $ll_arr["tprice"]["qimei_price"];
            }else if($ll_key == "1/3焦煤"){
                $yc_price = $ll_arr["tprice"]["jiaomei_price"];
            }else if($ll_key == "主焦煤"){
                $yc_price = $ll_arr["tprice"]["dljm_price"];
            }else if($ll_key == "瘦煤"){
                $yc_price = $ll_arr["tprice"]["shoumei_price"];
            }else if($ll_key == "冶金焦"){
                $yc_price = $ll_arr["tprice"]["zyjj_price"];
            }
            $this->_dao->execute("update sd_steel_day_weeks_month_predict set yc_price = '".$yc_price."' where yc_edate = '".$edate."' and yc_type = '".$type."' and yc_varietyname = '".$ll_key."' and channel = 2");
        }
        echo "均价保存成功！";
        if($type != 1){
            exit;
        }
    }

    public function get_yc_data_compare($data_arr,$day){
        $gc_arr = $data_arr[0];
        $ll_arr = $data_arr[1];

        $gc_arr2 = array(
            "厚板"=>$gc_arr["tprice"]["houban_price"],
            "热卷"=>$gc_arr["tprice"]["rzbj_price"],
            "冷卷"=>$gc_arr["tprice"]["lzbj_price"],
            "酸洗"=>$gc_arr["tprice"]["suanxi_price"],
            "镀锌"=>$gc_arr["tprice"]["duxin_price"],
            "螺纹钢"=>$gc_arr["tprice"]["lw_price"],
            "H型钢"=>$gc_arr["tprice"]["h_xg_price"],
            "碳结钢"=>$gc_arr["tprice"]["tjg_price"],
            "方坯"=>$gc_arr["tprice"]["fp_price"]
        );
        $ll_arr2 = array(
            "铁矿石"=>$ll_arr["tprice"]["tks_price"],
            "进口矿指数"=>$ll_arr["tprice"]["tks_shpi_price"],
            "废钢"=>$ll_arr["tprice"]["fg_price"],
            "硅锰"=>$ll_arr["tprice"]["gm_price"],
            "硅铁"=>$ll_arr["tprice"]["gt_price"],
            "高锰"=>$ll_arr["tprice"]["gtmt_price"],
            "镍板"=>$ll_arr["tprice"]["nieban_price"],
            "气煤"=>$ll_arr["tprice"]["qimei_price"],
            "1/3焦煤"=>$ll_arr["tprice"]["jiaomei_price"],
            "主焦煤"=>$ll_arr["tprice"]["dljm_price"],
            "瘦煤"=>$ll_arr["tprice"]["shoumei_price"],
            "冶金焦"=>$ll_arr["tprice"]["zyjj_price"]
        );

        $benyue_sdate = date("Y-m-01", strtotime($day));
        $benyue_edate = $day;
        $shangyue_mo_date = $this->get_xhdate($benyue_sdate);
        $shangyue_chu_date = date("Y-m-01",strtotime($benyue_sdate . "-1 day"));
        $jinnian_sdate = date("Y", strtotime($day)) . "-01-01";
        $jinnian_edate = date("Y", strtotime($day)) . "-12-31";
        // $GLOBALS['YC_GC_NAME'] = array("厚板","热卷","冷卷","酸洗","镀锌","螺纹钢","H型钢","碳结钢","方坯");
        // $GLOBALS['YC_YL_NAME1'] = array("铁矿石","进口矿指数","废钢","硅锰","硅铁","高锰","镍板","气煤","1/3焦煤","主焦煤","瘦煤","冶金焦");
        // $tks_shpi_sym_shpi = $this->maindao->getOne("select price from marketconditions where mastertopid = 'H986401' AND mconmanagedate >='" . $shangyue_mo_date . " 00:00:00' AND mconmanagedate <='" . $shangyue_mo_date . "  23:59:59'");
        $tks_shpi_sym_shpi = $this->maindao->getOne("select price from marketconditions where mastertopid = 'H986401' AND mconmanagedate <='" . $shangyue_mo_date . "  23:59:59' order by mconmanagedate desc limit 1");
        $tks_shpi_sy_avg = $this->maindao->getOne("select price from marketconditions where mastertopid = 'H986401' AND mconmanagedate >='" . $shangyue_chu_date . " 00:00:00' AND mconmanagedate <='" . $shangyue_mo_date . "  23:59:59'");
        $tks_shpi_by_avg = $this->maindao->getOne("select price from marketconditions where mastertopid = 'H986401' AND mconmanagedate >='" . $benyue_sdate . " 00:00:00' AND mconmanagedate <='" . $benyue_edate . "  23:59:59'");
        $tks_shpi_jn_max = $this->maindao->getOne("SELECT  MAX(CAST(price AS double)) as max_shpi  from marketconditions where mastertopid = 'H986401' AND mconmanagedate >='" . $jinnian_sdate . " 00:00:00' AND mconmanagedate <='" . $jinnian_edate . " 23:59:59' ");

        $benyue_avg_price_arr = $this->_dao->get_yc_avg_price($benyue_sdate,$benyue_edate);

        $shangyue_avg_price_arr = $this->_dao->get_yc_avg_price($shangyue_chu_date,$shangyue_mo_date);

        $shangyue_mo_data_arr = $this->_dao->get_yc_avg_price($shangyue_mo_date,$shangyue_mo_date);

        $jinnian_max_data_arr = $this->_dao->query("SELECT yc_varietyname,MAX(yc_price) as max_price FROM sd_steel_day_weeks_month_predict WHERE `yc_edate` <= '".$jinnian_edate."' and `yc_edate` >= '".$jinnian_sdate."'  and yc_type = 1 GROUP BY yc_varietyname");
        $max_data_arr = array();
        foreach ( $jinnian_max_data_arr as $key => $v ){
            $max_data_arr[$v["yc_varietyname"]] = $v['max_price'];
        }

        $gc_shangyuemo_tr_str = "<tr><td>较上月末</td>";
        $gc_benyue_avg_tr_str = "<tr><td>本月均价较上月均价</td>";
        $gc_jinnian_max_tr_str = "<tr><td>较年内高点</td>";
        foreach($GLOBALS['YC_GC_NAME'] as $gc_key){
            $gc_shangyuemo_tr_str .= "<td>".$this->zhangdie($gc_arr2[$gc_key] - $shangyue_mo_data_arr[$gc_key])."</td>";
            $gc_benyue_avg_tr_str .= "<td>".$this->zhangdie($benyue_avg_price_arr[$gc_key] - $shangyue_avg_price_arr[$gc_key])."</td>";
            $gc_jinnian_max_tr_str .= "<td>".$this->zhangdie($gc_arr2[$gc_key] - $max_data_arr[$gc_key])."</td>";
        }
        $gc_shangyuemo_tr_str .= "</tr>";
        $gc_benyue_avg_tr_str .= "</tr>";
        $gc_jinnian_max_tr_str .= "</tr>";

        $ll_shangyuemo_tr_str = "<tr><td>较上月末</td>";
        $ll_benyue_avg_tr_str = "<tr><td>本月均价较上月均价</td>";
        $ll_jinnian_max_tr_str = "<tr><td>较年内高点</td>";
        foreach($GLOBALS['YC_YL_NAME1'] as $ll_key){
            if($ll_key == "进口矿指数"){
                if ($ll_arr2[$ll_key] == "--"){
                    $ll_shangyuemo_tr_str .= "<td>--</td>";
                    $ll_jinnian_max_tr_str .= "<td>--</td>";
                    $ll_arr2[$ll_key] = 0;
                }else {
                    $ll_shangyuemo_tr_str .= "<td>" . $this->zhangdie($ll_arr2[$ll_key] - $tks_shpi_sym_shpi) . "</td>";
                    $ll_jinnian_max_tr_str .= "<td>" . $this->zhangdie($ll_arr2[$ll_key] - $tks_shpi_jn_max) . "</td>";
                }
                $ll_benyue_avg_tr_str .= "<td>" . $this->zhangdie($tks_shpi_by_avg - $tks_shpi_sy_avg) . "</td>";

            }else{
                $ll_shangyuemo_tr_str .= "<td>".$this->zhangdie($ll_arr2[$ll_key] - $shangyue_mo_data_arr[$ll_key])."</td>";
                $ll_benyue_avg_tr_str .= "<td>".$this->zhangdie($benyue_avg_price_arr[$ll_key] - $shangyue_avg_price_arr[$ll_key])."</td>";
                $ll_jinnian_max_tr_str .= "<td>".$this->zhangdie($ll_arr2[$ll_key] - $max_data_arr[$ll_key])."</td>";
            }
        }
        $ll_shangyuemo_tr_str .= "</tr>";
        $ll_benyue_avg_tr_str .= "</tr>";
        $ll_jinnian_max_tr_str .= "</tr>";

        $this->assign("gc_shangyuemo_tr_str",$gc_shangyuemo_tr_str);
        $this->assign("gc_benyue_avg_tr_str",$gc_benyue_avg_tr_str);
        $this->assign("gc_jinnian_max_tr_str",$gc_jinnian_max_tr_str);

        $this->assign("ll_shangyuemo_tr_str",$ll_shangyuemo_tr_str);
        $this->assign("ll_benyue_avg_tr_str",$ll_benyue_avg_tr_str);
        $this->assign("ll_jinnian_max_tr_str",$ll_jinnian_max_tr_str);
    }

	public function forecast_list($params){
        $mode = $params['mode'] == "" ? 1 : $params['mode'];
        $saving = $params['saving'];
        $adminid = $params['adminid'];
        $_SESSION['adminid'] = $adminid;
        if($saving=="1" && $adminid==""){
            echo "未登录";
            exit;
        }
	    $viewList = array("1"=>"day_market_yc","2"=>"week_market_yc","3"=>"yue_market_yc");
        $this->assign("type",$params['type']);
        $this->assign("mode",$mode);
        $this->assign("saving",$saving);
        $this->assign("viewName",$viewList[$params['type']]);
    }

	public function ajax_forecast_list($params){
        $page = $params['page'];
        $saving = $params['saving'];
        $limit = $params['limit'] == "" ? 10 : $params['limit'];
        if ($page < 1) {
            $page = 1;
        }
        $start = ($page - 1) * $limit;


        if($saving=="1"){
            $yc_sdate = date("Y-m-d",strtotime("-3 month"));
            $dataInfo = $this->drcwdao->query("select yc_edate,yc_type,yc_sdate from sd_steel_day_weeks_month_predict where yc_type = '".$params['type']."' and yc_sdate>='".$yc_sdate."' GROUP BY yc_type,yc_edate order by yc_edate desc ");


        }else{
            $total = count($this->drcwdao->query("select * from sd_steel_day_weeks_month_predict where yc_type = '".$params['type']."' and status = 1 GROUP BY yc_type,yc_edate"));
            $dataInfo = $this->drcwdao->query("select * from sd_steel_day_weeks_month_predict where yc_type = '".$params['type']."' and status = 1 GROUP BY yc_type,yc_edate order by yc_edate desc limit  $start,$limit");
        }



        $typeList = array('1'=>"分析预测",'2'=>"周分析预测",'3'=>"分析预测");
        $data = array();
        foreach ($dataInfo as $tmp){
            // $dateList = $this->getXunCalcDateArray($tmp['yc_edate']);
            if($params['type'] == "3"){
                $tmp['yc_varietyname'] = date("Y年n月",strtotime($tmp['yc_edate']." +1 day")).$typeList[$params['type']];
            }elseif ($params['type'] == "2"){
                $startDate = strtotime("next monday", strtotime($tmp['yc_edate']));
                $endDate = $this->get_xhdate(date("Y-m-d",strtotime("next sunday", $startDate)));
                $tmp['yc_varietyname'] = date("Y年n月j日",strtotime($endDate)).$typeList[$params['type']];
            }else{
                $tmp['yc_varietyname'] = date("Y年n月j日",strtotime($this->get_xhdate2($tmp['yc_edate']))).$typeList[$params['type']];
            }
            if($saving=="1") {
                $tmp['adminid'] = $_SESSION['adminid'];
                $arr = array();
                $arr['type'] = $params['type'];
                $arr['sdate'] = $tmp['yc_sdate'];
                $arr['edate'] = $tmp['yc_edate'];
                $state = $this->getMarketNews($arr);
                //当月25日生成下月预测，下月10日回顾及修正
                $curr_update_end_date = date("Y-m-10",strtotime($tmp['yc_sdate']." +1 month"));
                $curr_update_start_date = date("Y-m-25",strtotime($tmp['yc_sdate']));
                $curr_date = date("Y-m-d");
                if($params['type'] == "3" && ($curr_update_start_date <= $curr_date && $curr_date <= $curr_update_end_date)){
                    // echo $tmp['yc_varietyname']."--".$tmp['yc_edate']."--";echo $curr_update_start_date."--".$curr_update_end_date."<br/>";
                }else if ($state == 1) {
                    continue;
                }
            }
            $data[] = $tmp;
        }
        $code = 0;
        if ($data) {
            $code = 0;
        }
        if($saving=="1") {
            $total = count($data);
        }
        $return_array = array(
            "code" => $code,
            "data" => $data,
            "count" => $total
        );
        echo json_encode($return_array);
    }

    function getMarketNews($params){
        $type = $params['type'];
        $sdate = $params['sdate'];
        $edate = $params['edate'];

        $type_sum_arr = array(
            "1"=>7,
            "2"=>20,
            "3"=>16,
            "4"=>4,
            "5"=>16,
            "6"=>5
        );

        $sql = "select * from sd_steel_marketNews where type = '".$type."' and CDate >= '".$sdate."' and CDate <= '".$edate."' group by news_type";
        $count = $this->_dao->query($sql);
        $count = count($count);

        $state = 1;
        if($count<$type_sum_arr[$type] && strtotime($edate)>=strtotime("2024-12-24")){
            $state = 2;
        }
        return $state;
    }

	//根据当前日获取上个钢之家工作日
    function get_xhdate($today){
        $flag=1;
        $lastday=$today;
        while(true){
            $lastday=date('Y-m-d',strtotime('-'.$flag.' day',strtotime($lastday)));
            //echo $lastday;
            if(file_get_contents('https://holiday.steelhome.com/isholiday.php?date='.$lastday)!="1")
            { 
                break;
            } 
        }
        $today_s=$today;
        $lastday_s=$lastday;
        return  $lastday_s;
	}

	//根据当前日获取下个钢之家工作日
    function get_xhdate2($today){
        $flag=1;
        $lastday=$today;
        while(true){
            $lastday=date('Y-m-d',strtotime('+'.$flag.' day',strtotime($lastday)));
            //echo $lastday;
            if(file_get_contents('https://holiday.steelhome.com/isholiday.php?date='.$lastday)!="1")
            { 
                break;
            } 
        }
        $today_s=$today;
        $lastday_s=$lastday;
        return  $lastday_s;
	}
    //根据当前日获取本月钢之家工作日个数
    function get_xhdate3($today){

        $sdate = date("Y-m-01",strtotime($today));
        $edate = date("Y-m-t",strtotime($today));
        $before = 0;
        $after = 0;
        while(strtotime($sdate)<=strtotime($edate)){

            if(file_get_contents('https://holiday.steelhome.com/isholiday.php?date='.$sdate)!="1")
            {
                if(strtotime($sdate)<=strtotime($today)){
                    $before++;
                }else{
                    $after++;
                }
            }
            $sdate=date('Y-m-d',strtotime('+1 day',strtotime($sdate)));
        }
        $arr = array();
        $arr['before'] = $before;
        $arr['after'] = $after;
        return  $arr;
    }
    //根据当前日获取最近一个上个钢之家工作日
    function get_xhdate4($today){
        $flag=1;
        $lastday=$today;
        while(true){

            //echo $lastday;
            if(file_get_contents('https://holiday.steelhome.com/isholiday.php?date='.$lastday)!="1")
            {
                break;
            }
            $lastday=date('Y-m-d',strtotime('-'.$flag.' day',strtotime($lastday)));
        }
        $today_s=$today;
        $lastday_s=$lastday;
        return  $lastday_s;
    }

    function get_work_date_next($today){
        $flag=1;
        $lastday=$today;
        if (file_get_contents('https://holiday.steelhome.com/isholiday.php?date='.$lastday)=="1") {
            while (true) {
                $lastday = date('Y-m-d', strtotime('+' . $flag . ' day', strtotime($lastday)));
                //echo $lastday;
                if (file_get_contents('https://holiday.steelhome.com/isholiday.php?date=' . $lastday) != "1") {
                    break;
                }
            }
        }
        $lastday_s=$lastday;
        return  $lastday_s;
    }

	function get_yc_table_tr($data_arr,$type,$type2){
		// $gc_arr = array("厚板","热卷","冷卷","酸洗","镀锌","螺纹钢","H型钢","碳结钢","方坯");
        $gc_arr = $GLOBALS['YC_GC_NAME'];
		$ll_arr = array();
		if($type2 == 1 || $type2 == 3){
			// $ll_arr = array("铁矿石","进口矿指数","废钢","硅锰","硅铁","高锰","镍板","气煤","1/3焦煤","主焦煤","瘦煤","冶金焦");
            $ll_arr = $GLOBALS['YC_YL_NAME1'];
		}else{
			// $ll_arr = array("铁矿石","废钢","硅锰","硅铁","高锰","镍板","气煤","1/3焦煤","主焦煤","瘦煤","冶金焦");
            $ll_arr = $GLOBALS['YC_YL_NAME2'];
		}
		$td_str = "";
		$td_str2 = "";
        $td_str3 = "";

		if($type == 1){
			//钢材
			foreach($gc_arr as $gcname){
				if($type2 == 1 || $type2 == 3){
					$td_str .= "<td>".$data_arr[$gcname]["ycfxfd"]."</td>";
				}else if($type2 == 2){
					$td_str .= "<td>".$data_arr[$gcname]["ycfx"]."</td>";
					$td_str2 .= "<td>".$data_arr[$gcname]["ycfd"]."</td>";
				}
                // $td_str3 .= "<td>".$data_arr[$gcname]["yc_price"]."</td>";
			}
		}else{
			//炉料
			foreach($ll_arr as $llname){
				if($type2 == 1 || $type2 == 3){
					$td_str .= "<td>".$data_arr[$llname]["ycfxfd"]."</td>";
				}else if($type2 == 2){
					$td_str .= "<td>".$data_arr[$llname]["ycfx"]."</td>";
					$td_str2 .= "<td>".$data_arr[$llname]["ycfd"]."</td>";
				}
                // $td_str3 .= "<td>".$data_arr[$gcname]["yc_price"]."</td>";
			}
		}
		$ret_str = "";
		if($type2 == 1){
			$ret_str = "<tr><td>昨预测</td>".$td_str."</tr>";
		}else if($type2 == 2){
			$ret_str = "<tr><td>预测方向</td>".$td_str."</tr>"."<tr><td>预测幅度</td>".$td_str2."</tr>";
		}else if($type2 == 3){
			$ret_str = "<tr><td>上月预测</td>".$td_str."</tr>";
		}
        // $ret_str .= "<tr><td>预测价格</td>".$td_str3."</tr>";
		return $ret_str;
	}

    //月预测走势图
    function get_yue_yc_charts($edate){
         //取一年内数据
         $sdate = date("Y-m-01",strtotime($edate."-1 year"));
        $imagesUrls = array(
            "hg_imgs"=>array(
                "0"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart='.$sdate.'&DateEnd='.$edate.'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=1&ImageTitle=ucy2qNfKsvrNttfK&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"56610","DTIDSub":"","DataImage":"1","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"tbHUwta1","unitconver":"0","unitstring":"0trUqg==","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"56999","DTIDSub":"","DataImage":"1","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"wNu8xta1","unitconver":"0","unitstring":"0trUqg==","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle=',
                "1"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart='.$sdate.'&DateEnd='.$edate.'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=1&ImageTitle=t7%2b12LL6v6q3os2218o%3d&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"56611","DTIDSub":"","DataImage":"1","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"tbHUwta1","unitconver":"0","unitstring":"JQ==","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"62011","DTIDSub":"","DataImage":"1","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"wNu8xta1","unitconver":"0","unitstring":"JQ==","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle=',
                "2"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart='.$sdate.'&DateEnd='.$edate.'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=1&ImageTitle=u%2fm0ocnoyqm9qMnozbbXyg%3d%3d&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"56612","DTIDSub":"","DataImage":"1","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"tbHUwta1","unitconver":"0","unitstring":"JQ==","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"62014","DTIDSub":"","DataImage":"1","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"wNu8xta1","unitconver":"0","unitstring":"JQ==","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle=',
                "3"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart='.$sdate.'&DateEnd='.$edate.'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=1&ImageTitle=1sbU7NK1ucy2qNfKsvrNttfK&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"56613","DTIDSub":"","DataImage":"1","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"tbHUwta1","unitconver":"0","unitstring":"JQ==","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"62017","DTIDSub":"","DataImage":"1","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"wNu8xta1","unitconver":"0","unitstring":"JQ==","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle=',
                "4"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart='.$sdate.'&DateEnd='.$edate.'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=1&ImageTitle=yee74c%2f7t9HGt8HjytvX3Lbu&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"56615","DTIDSub":"","DataImage":"1","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"tbHUwta1","unitconver":"0","unitstring":"JQ==","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"62022","DTIDSub":"","DataImage":"1","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"wNu8xta1","unitconver":"0","unitstring":"JQ==","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle=',
                "5"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart='.$sdate.'&DateEnd='.$edate.'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=1&ImageTitle=zeLDs7P2v9qjqMjLw%2fGx0ta1o6k%3d&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"56619","DTIDSub":"","DataImage":"1","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"tbHUwta1","unitconver":"0","unitstring":"JQ==","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"62031","DTIDSub":"","DataImage":"1","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"wNu8xta1","unitconver":"0","unitstring":"JQ==","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle=',
               "6"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart='.$sdate.'&DateEnd='.$edate.'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=1&ImageTitle=uaTStdT2vNPWtQ%3d%3d&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"56614","DTIDSub":"","DataImage":"1","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"tbHUwta1","unitconver":"0","unitstring":"JQ==","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"62019","DTIDSub":"","DataImage":"1","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"wNu8xta1","unitconver":"0","unitstring":"JQ==","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle=',
                "7"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart='.$sdate.'&DateEnd='.$edate.'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=1&ImageTitle=1sbU7NK1UE1J1rjK%2fQ%3d%3d&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"8526","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"UE1JILWx1MI=","unitconver":"0","unitstring":"JQ==","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle='
            ),
            "xy_imgs"=>array(
                "8"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart='.$sdate.'&DateEnd='.$edate.'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=1&ImageTitle=&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"55525","DTIDSub":"","DataImage":"1","zhou":"0","DataType":"4","GongshiID":"0","LineTitle":"xvuztbL6wb/NrLHI1PazpA==","unitconver":"0","unitstring":"","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"55525","DTIDSub":"","DataImage":"1","zhou":"0","DataType":"13","GongshiID":"0","LineTitle":"xvuztbL6wb/A27zGzayxyNT2s6Q=","unitconver":"0","unitstring":"JQ==","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle=',
                "9"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart='.$sdate.'&DateEnd='.$edate.'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=1&ImageTitle=sfnP5KGiz7TSwrv6oaK%2f1bX3tbHUws2sscg%3d&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"597","DTIDSub":"","DataImage":"1","zhou":"0","DataType":"13","GongshiID":"0","LineTitle":"sfnP5A==","unitconver":"0","unitstring":"JQ==","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"596","DTIDSub":"","DataImage":"1","zhou":"0","DataType":"13","GongshiID":"0","LineTitle":"z7TSwrv6","unitconver":"0","unitstring":"JQ==","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"652","DTIDSub":"","DataImage":"1","zhou":"0","DataType":"13","GongshiID":"0","LineTitle":"v9W19w==","unitconver":"0","unitstring":"JQ==","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle=',
                "10"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart='.$sdate.'&DateEnd='.$edate.'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=1&ImageTitle=1Oy0rMj9tPPWuLHq&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"179","DTIDSub":"","DataImage":"1","zhou":"0","DataType":"4","GongshiID":"0","LineTitle":"zeq5pMG/","unitconver":"0","unitstring":"","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"180","DTIDSub":"","DataImage":"1","zhou":"0","DataType":"4","GongshiID":"0","LineTitle":"0MK907aptaXBvw==","unitconver":"0","unitstring":"","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"181","DTIDSub":"","DataImage":"1","zhou":"0","DataType":"4","GongshiID":"0","LineTitle":"ytaz1raptaXBvw==","unitconver":"0","unitstring":"","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle=',
                "11"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart='.$sdate.'&DateEnd='.$edate.'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=1&ImageTitle=&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"665","DTIDSub":"","DataImage":"1","zhou":"0","DataType":"13","GongshiID":"0","LineTitle":"sb7Uws2sscg=","unitconver":"0","unitstring":"JQ==","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle=',
                "12"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart='.$sdate.'&DateEnd='.$edate.'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=1&ImageTitle=&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"175","DTIDSub":"","DataImage":"1","zhou":"0","DataType":"4","GongshiID":"0","LineTitle":"wNu8xs2sscjU9rf5","unitconver":"0","unitstring":"","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle=',
                "13"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart='.$sdate.'&DateEnd='.$edate.'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=1&ImageTitle=&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"56612","DTIDSub":"","DataImage":"1","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"u/m0ocnoyqm9qMnozbbXyrWx1MLNrLHI1Pa3+SjUwim1sdTCzayxyNT2t/k=","unitconver":"0","unitstring":"JQ==","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle='
            )
            );

        foreach ($imagesUrls as $index => $images) {
            foreach ($images as $id => $imagesUrl) {
                $imageid = "tu" . $id . "_" . $id;
                $imagesUrlList[$index][] =  '<div align="center" class="" id="' . $imageid . '"><iframe name="iframe" style="margin:0; padding:0; width:500px; height:279px; background-color:#FFF; visibility:inherit;" src=\'' . $imagesUrl . '&callbackname=' . $imageid . '\' frameborder="0" scrolling="no"></iframe></div>';
            }
        }
        $this->assign("imagesUrlLists",$imagesUrlList);
        // echo "<pre/>";print_r($imagesUrlList);
    }

	function yue_market_yc($params){
        if($params['saving']=="2"){
            $params['saving']="1";
        }
		// $sdate = isset($params["sdate"]) == true?$params["sdate"]:date("Y-m-01");
        // $edate = isset($params["edate"]) == true?$params["edate"]:date("Y-m-d");
        $sdate = "";
        $edate = "";
        if($params["sdate"] == "" || $params["edate"] == ""){
            $date_arr = $this->drcwdao->getRow("select * from sd_steel_day_weeks_month_predict where yc_type = 3 and status = 1 GROUP BY yc_type,yc_edate order by yc_edate desc limit 1");
            $sdate = $date_arr["yc_sdate"];
            $edate = $date_arr["yc_edate"];
        }else{
            $sdate = $params["sdate"];
            $edate = $params["edate"];
        }
        $params["titleName"] = date("Y年n月",strtotime($edate." +1 day"))."分析预测（".date("n月j日",strtotime($sdate))."-".date("n月j日",strtotime($edate))."）";

		$sdate2 = date("Y-m-d",strtotime($sdate."-1 month"));
		$edate2 = date("Y-m-d",strtotime($sdate."-1 day"));

        //钢厂检修
        $this->handleSteelRepair(['this_start_date'=>$sdate,"this_end_date"=>$edate]);

        $this->assign( "adminid", $params['adminid']);
        $this->assign( "type", "3");
        $this->assign( "sdate", $sdate);
        $this->assign( "edate", $edate);
        
        //我国主要财政金融数据 文字描述
        $sdgt_scyp = html_entity_decode($this->_dao->get_sdgt_marketNews(3,$sdate,$edate,303));
        $this->assign( "marketNews303", $sdgt_scyp);
        
        //我国钢材坯进出口 文字描述
        $sdgt_scyp = html_entity_decode($this->_dao->get_sdgt_marketNews(3,$sdate,$edate,305));
        $this->assign( "marketNews305", $sdgt_scyp);
        
        //下游行业需求 文字描述
        $sdgt_scyp = html_entity_decode($this->_dao->get_sdgt_marketNews(3,$sdate,$edate,307));
        $this->assign( "marketNews307", $sdgt_scyp);
        //国内铁矿石供需 文字描述
        $sdgt_scyp = html_entity_decode($this->_dao->get_sdgt_marketNews(3,$sdate,$edate,308));
        $this->assign( "marketNews308", $sdgt_scyp);
        //国内焦煤供需 文字描述
        $sdgt_scyp = html_entity_decode($this->_dao->get_sdgt_marketNews(3,$sdate,$edate,309));
        $this->assign( "marketNews309", $sdgt_scyp);
        //国内焦炭供需 文字描述
        $sdgt_scyp = html_entity_decode($this->_dao->get_sdgt_marketNews(3,$sdate,$edate,310));
        $this->assign( "marketNews310", $sdgt_scyp);
        //竞争钢厂设备检修情况
        $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(3,$sdate,$edate,311));
        $this->assign( "marketNews311", $marketNews);
        //摘要
        $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(3,$sdate,$edate,312));
        $this->assign( "marketNews312", $marketNews);
        //钢材市场价格变化情况
        $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(3,$sdate,$edate,313));
        $this->assign( "marketNews313", $marketNews);
        //原燃料市场价格变化
        $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(3,$sdate,$edate,314));
        $this->assign( "marketNews314", $marketNews);

        //原燃料市场价格变化
        $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(3,$sdate,$edate,315));
        $this->assign( "marketNews315", $marketNews);

		//1、钢材、原燃料市场变化
		$ret_arr = $this->get_gc_yrl_data($edate,$sdate,$edate2,$sdate2);
		// echo "<pre/>";print_r($ret_arr);

        //市场研判 == 本月市场回顾
        $sdgt_scyp = html_entity_decode($this->_dao->get_sdgt_marketNews(3,$sdate,$edate,301));
        if($params["saving"] == 1 && $sdgt_scyp == ""){
            $sdgt_scyp = '<p>主要钢材品种'.date("n",strtotime($sdate)).'月均价较'.date("n",strtotime($sdate2)).'月均价变化幅度：唐山方坯'.$this->yue_yc_zhangdie4($ret_arr[0]['zd']['fp_price']).'元/吨，螺纹钢'.$this->yue_yc_zhangdie4($ret_arr[0]['zd']['lw_price']).'元/吨，45#碳结钢'.$this->yue_yc_zhangdie4($ret_arr[0]['zd']['tjg_price']).'元/吨，H型钢'.$this->yue_yc_zhangdie4($ret_arr[0]['zd']['h_xg_price']).'元/吨，厚板'.$this->yue_yc_zhangdie4($ret_arr[0]['zd']['houban_price']).'元/吨，热卷'.$this->yue_yc_zhangdie4($ret_arr[0]['zd']['rzbj_price']).'元/吨，冷卷'.$this->yue_yc_zhangdie4($ret_arr[0]['zd']['lzbj_price']).'元/吨，酸洗'.$this->yue_yc_zhangdie4($ret_arr[0]['zd']['suanxi_price']).'元/吨，镀锌'.$this->yue_yc_zhangdie4($ret_arr[0]['zd']['duxin_price']).'元/吨。</p>';

            $sdgt_scyp .= '<p>主要原燃料'.date("n",strtotime($sdate)).'月均价较'.date("n",strtotime($sdate2)).'月均价变化幅度：铁矿石'.$this->yue_yc_zhangdie4($ret_arr[1]['zd']['tks_price']).'元/吨，废钢'.$this->yue_yc_zhangdie4($ret_arr[1]['zd']['fg_price']).'元/吨，焦炭'.$this->yue_yc_zhangdie4($ret_arr[1]['zd']['zyjj_price']).'元/吨，气煤'.$this->yue_yc_zhangdie4($ret_arr[1]['zd']['qimei_price']).'元/吨，1/3焦煤'.$this->yue_yc_zhangdie4($ret_arr[1]['zd']['jiaomei_price']).'元/吨，主焦煤'.$this->yue_yc_zhangdie4($ret_arr[1]['zd']['dljm_price']).'元/吨，瘦煤'.$this->yue_yc_zhangdie4($ret_arr[1]['zd']['shoumei_price']).'元/吨，硅锰'.$this->yue_yc_zhangdie4($ret_arr[1]['zd']['gm_price']).'元/吨，硅铁'.$this->yue_yc_zhangdie4($ret_arr[1]['zd']['gt_price']).'元/吨。</p>';
        }
        $this->assign( "sdgt_scyp", $sdgt_scyp);
        
		// foreach($ret_arr as &$ret_v){
		// 	$ret_v["tprice"]["date"] = date('Y年n月',strtotime($ret_v["tprice"]["date2"]));
		// 	$ret_v["lprice"]["date"] = date('Y年n月',strtotime($ret_v["lprice"]["date2"]));
		// }
        if($params["save_price"] == 1){
            $this->save_price($ret_arr,3,$edate);
        }
        //与上月末 上月均价 年内高点比较
        $this->get_yc_data_compare($ret_arr,$edate);
		//取钢材预测数据
        $gc_yue_yc_data = $this->_dao->get_yc_data(3,1,$sdate,$edate);
		$gc_yue_yc_str = $this->get_yc_table_tr($gc_yue_yc_data,1,3);
        // $gc_yc_price_str = "<tr>
        //                     <td>预测价格</td>
        //                     <td>".($ret_arr[0]['lprice']['houban_price'] + ($gc_yue_yc_data['厚板']['yc_zd']))."</td>
        //                     <td>".($ret_arr[0]['lprice']['rzbj_price'] + ($gc_yue_yc_data['热卷']['yc_zd']))."</td>
        //                     <td>".($ret_arr[0]['lprice']['lzbj_price'] + ($gc_yue_yc_data['冷卷']['yc_zd']))."</td>
        //                     <td>".($ret_arr[0]['lprice']['suanxi_price'] + ($gc_yue_yc_data['酸洗']['yc_zd']))."</td>
        //                     <td>".($ret_arr[0]['lprice']['duxin_price'] + ($gc_yue_yc_data['镀锌']['yc_zd']))."</td>
        //                     <td>".($ret_arr[0]['lprice']['lw_price'] + ($gc_yue_yc_data['螺纹钢']['yc_zd']))."</td>
        //                     <td>".($ret_arr[0]['lprice']['h_xg_price'] + ($gc_yue_yc_data['H型钢']['yc_zd']))."</td>
        //                     <td>".($ret_arr[0]['lprice']['tjg_price'] + ($gc_yue_yc_data['碳结钢']['yc_zd']))."</td>
        //                     <td>".($ret_arr[0]['lprice']['fp_price'] + ($gc_yue_yc_data['方坯']['yc_zd']))."</td>
        //                 </tr>";
        $gc_yc_price_str = $this->get_yc_gc_table_tr($ret_arr[0],$gc_yue_yc_data);
        $gc_yue_yc_str .= $gc_yc_price_str;

        //修正预测 钢材
        $gc_yue_yc_data_update = $this->_dao->get_yc_data(301,1,$sdate,$edate);
        $update_gc_tr = $this->get_yc_gc_table_tr($ret_arr[0],$gc_yue_yc_data_update,1);
        $gc_yue_yc_str .= $update_gc_tr;
		
		//取原料预测数据
        $ll_yue_yc_data = $this->_dao->get_yc_data(3,2,$sdate,$edate);
		$ll_yue_yc_str = $this->get_yc_table_tr($ll_yue_yc_data,2,3);
        // $ll_yc_price_str = "<tr>
        //                     <td>预测价格</td>
        //                     <td>".($ret_arr[1]['lprice']['tks_price'] + ($ll_yue_yc_data['铁矿石']['yc_zd']))."</td>
        //                     <td>--</td>
        //                     <td>".($ret_arr[1]['lprice']['fg_price'] + ($ll_yue_yc_data['废钢']['yc_zd']))."</td>
        //                     <td>".($ret_arr[1]['lprice']['gm_price'] + ($ll_yue_yc_data['硅锰']['yc_zd']))."</td>
        //                     <td>".($ret_arr[1]['lprice']['gt_price'] + ($ll_yue_yc_data['硅铁']['yc_zd']))."</td>
        //                     <td>".($ret_arr[1]['lprice']['gtmt_price'] + ($ll_yue_yc_data['高锰']['yc_zd']))."</td>
        //                     <td>".($ret_arr[1]['lprice']['nieban_price'] + ($ll_yue_yc_data['镍板']['yc_zd']))."</td>
        //                     <td>".($ret_arr[1]['lprice']['qimei_price'] + ($ll_yue_yc_data['气煤']['yc_zd']))."</td>
        //                     <td>".($ret_arr[1]['lprice']['jiaomei_price'] + ($ll_yue_yc_data['1/3焦煤']['yc_zd']))."</td>
        //                     <td>".($ret_arr[1]['lprice']['dljm_price'] + ($ll_yue_yc_data['主焦煤']['yc_zd']))."</td>
        //                     <td>".($ret_arr[1]['lprice']['shoumei_price'] + ($ll_yue_yc_data['瘦煤']['yc_zd']))."</td>
        //                     <td>".($ret_arr[1]['lprice']['zyjj_price'] + ($ll_yue_yc_data['冶金焦']['yc_zd']))."</td>
        //                 </tr>";
        $ll_yc_price_str = $this->get_yc_ll_table_tr($ret_arr[1],$ll_yue_yc_data,0,1);
        $ll_yue_yc_str .= $ll_yc_price_str;

        //修正预测 原料
        $ll_yue_yc_data_update = $this->_dao->get_yc_data(301,2,$sdate,$edate);
        $update_ll_tr = $this->get_yc_ll_table_tr($ret_arr[1],$ll_yue_yc_data_update,1,1);
        $ll_yue_yc_str .= $update_ll_tr;

		$this->assign("gc_yue_yc_str",$gc_yue_yc_str);
		$this->assign("ll_yue_yc_str",$ll_yue_yc_str);

		//钢价变动原因
		$gcbd_marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(3,$sdate,$edate,3));
        $this->assign( "gcbd_marketNews", $gcbd_marketNews);
		//原燃料变动原因
		$yrl_marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(3,$sdate,$edate,4));
        $this->assign( "yrl_marketNews", $yrl_marketNews);

		//1、我国主要宏观经济数据预测
		//固定资产投资取L2Data表：L2id=140 and L2OrderNo=40
		$gdzc_data = $this->get_hgData(140,40,"",$edate);
        $this->get_yue_yc_charts($edate);

		//房地产开发投资: L2id=141 and L2OrderNo=43
		$fdc_data = $this->get_hgData(141,43,"",$edate);

		//基础设施建设投资: L2id=203 and L2OrderNo=42
		$jcjs_data = $this->get_hgData(203,42,"",$edate);

		//制造业固定资产投资: L2id=204 and L2OrderNo=41
		$zzy_data = $this->get_hgData(204,41,"",$edate);

        $jjsj_str =  "";
        $i = 0;
        $news_key1 = "";
        $news_key2 = "";
        foreach($gdzc_data as $key => $item){
            if($i == 0){
                $news_key1 = $key;
            }
            if($i == 1){
                $news_key2 = $key;
            }
            $jjsj_str .= "<tr>
                              <td>".$gdzc_data[$key]["DATE2"]."</td>
                              <td>".$gdzc_data[$key]["D3"]."</td>
                              <td>".$gdzc_data[$key]["D4"]."</td>
                              <td>".$fdc_data[$key]["D3"]."</td>
                              <td>".$fdc_data[$key]["D4"]."</td>
                              <td>".$jcjs_data[$key]["D3"]."</td>
                              <td>".$jcjs_data[$key]["D4"]."</td>
                              <td>".$zzy_data[$key]["D3"]."</td>
                              <td>".$zzy_data[$key]["D4"]."</td>
                          </tr>";
            
            $i++;
            if($i == 6){
                break;
            }
        }
        
		// 社会消费品零售总额取L2Data表：L2id=144 and L2OrderNo=45
		$shxfp_data = $this->get_hgData(144,45,"",$edate);
		// 外贸出口（人民币值） ：L2id=547 and L2OrderNo=13
		$wmck_rmb_data = $this->get_hgData(547,13,"",$edate);
		// 工业增加值 ：L2id=142 and L2OrderNo=44取D1和D2
		$gyzjz_data = $this->get_hgData(142,44,"",$edate);
		// 制造业PMI指数 ：L2id=152 and L2OrderNo=1 取D1
		$zzy_pmi_data = $this->get_hgData(152,1,"",$edate);

        //我国主要宏观经济数据 文字描述
        $sdgt_scyp = html_entity_decode($this->_dao->get_sdgt_marketNews(3,$sdate,$edate,302));
        if($params["saving"] == 1 && $sdgt_scyp == ""){
            //供应端，工业增加值增长6%；而需求端的固定资产投资增长只有3.9%、社零增长只有3.7%、外贸出口增长6.9%。有效需求不足，特别是内需疲弱是我国经济运行的重要问题。从7月份PMI数据来看，同样存在生产大于需求的情况。7月份我国制造业PMI指数49.4%，较上月略降0.1个百分点。其中，生产指数50.1%，较上月下降0.6个百分点，继续处于扩张区间；新订单指数49.3%，下降0.2个百分点；新出口订单指数48.5%，上升0.2个百分点，都处在收缩区间。这势必导致企业产品出厂价格下跌，利润恶化。
            if($gyzjz_data[$news_key1]["D2"] > 0){
                $sdgt_scyp .= "供应端，工业增加值增长".$gyzjz_data[$news_key1]["D2"]."%；";
            }else if($gyzjz_data[$news_key1]["D2"] < 0){
                $sdgt_scyp .= "供应端，工业增加值下降".abs($gyzjz_data[$news_key1]["D2"])."%；";
            }

            if($gdzc_data[$news_key1]["D4"] > 0){
                $sdgt_scyp .= "需求端的固定资产投资增长".$gdzc_data[$news_key1]["D4"]."%、";
            }else if($shxfp_data[$news_key1]["D4"] < 0){
                $sdgt_scyp .= "需求端的固定资产投资下降".abs($gdzc_data[$news_key1]["D4"])."%、";
            }

            if($shxfp_data[$news_key1]["D4"] > 0){
                $sdgt_scyp .= "社零增长".$shxfp_data[$news_key1]["D4"]."%、";
            }else if($shxfp_data[$news_key1]["D4"] < 0){
                $sdgt_scyp .= "社零下降".abs($shxfp_data[$news_key1]["D4"])."%、";
            }

            if($wmck_rmb_data[$news_key1]["D4"] > 0){
                $sdgt_scyp .= "外贸出口增长".$wmck_rmb_data[$news_key1]["D4"]."%。";
            }else if($wmck_rmb_data[$news_key1]["D4"] < 0){
                $sdgt_scyp .= "外贸出口下降".abs($wmck_rmb_data[$news_key1]["D4"])."%。";
            }

            $sdgt_scyp .= date("n",strtotime($zzy_pmi_data[$news_key1]["DATE"]))."月份我国制造业PMI指数".$zzy_pmi_data[$news_key1]["D1"]."%，";
            $zzy_pmi_jc = $zzy_pmi_data[$news_key1]["D1"] - $zzy_pmi_data[$news_key2]["D1"];
            if($zzy_pmi_jc > 0){
                $sdgt_scyp .= "较上月增长".$zzy_pmi_jc."个百分点。";
            }else if($zzy_pmi_jc < 0){
                $sdgt_scyp .= "较上月下降".abs($zzy_pmi_jc)."个百分点。";
            }
        }
        $this->assign( "marketNews302", $sdgt_scyp);

        $jjsj_str_2 =  "";
        $i = 0;
        foreach($shxfp_data as $key => $item){
            $jjsj_str_2 .= "<tr>
                              <td>".$shxfp_data[$key]["DATE2"]."</td>
                              <td>".$shxfp_data[$key]["D3"]."</td>
                              <td>".$shxfp_data[$key]["D4"]."</td>
                              <td>".$wmck_rmb_data[$key]["D3"]."</td>
                              <td>".$wmck_rmb_data[$key]["D4"]."</td>
                              <td>".$gyzjz_data[$key]["D1"]."</td>
                              <td>".$gyzjz_data[$key]["D2"]."</td>
                              <td>".$shxfp_data[$key]["DATE2"]."</td>
                              <td>".$zzy_pmi_data[$key]["D1"]."</td>
                          </tr>";
                    
            $i++;
            if($i == 6){
                break;
            }
        }
        $this->assign( "jjsj_str_2", $jjsj_str_2 );

		//2、我国主要财政金融数据预测
		// M2取L2Data表：L2id=216 and L2OrderNo=2 取D1 M1:取上述条件查出来的D2字段
		$m1_m2_data = $this->get_hgData(216,2,"",$edate);
		// 新增社融：L2id=235 and L2OrderNo=4的D1 新增贷款取D2
		$xzsr_data = $this->get_hgData(235,4,"",$edate);
		// 财政收支同比增幅收入：L2id=233 and L2OrderNo=11取D3 
		$czsz_sr_data = $this->get_hgData(233,11,"",$edate);
		// 财政收支同比增幅支出：L2id=234 and L2OrderNo=12取D3
		$czsz_zc_data = $this->get_hgData(234,12,"",$edate);
		// 地方债：L2id=677 and L2OrderNo=15地方债取D1 专项债取：D2
		$dfz_data = $this->get_hgData(677,15,"",$edate);

        $czjrsj_str = "";
        $i = 0;
        foreach($m1_m2_data as $key => $item){
            $czjrsj_str .= "<tr>
                              <td>".$m1_m2_data[$key]["DATE2"]."</td>
                              <td>".$m1_m2_data[$key]["D1"]."</td>
                              <td>".$m1_m2_data[$key]["D2"]."</td>
                              <td>".$xzsr_data[$key]["D1"]."</td>
                              <td>".$xzsr_data[$key]["D2"]."</td>
                              <td>".$czsz_sr_data[$key]["D3"]."</td>
                              <td>".$czsz_zc_data[$key]["D3"]."</td>
                              <td>".$dfz_data[$key]["D1"]."</td>
                              <td>".$dfz_data[$key]["D2"]."</td>
                          </tr>";
            $i++;
            if($i == 6){
                break;
            }
        }

		// 三、国内钢材市场供求形势预测
		// 粗钢：L2id=109 and L2OrderNo=11当月产量：D1同比增幅：D3
		$gq_cg_data =  $this->get_hgData(109,11,"",$edate);
		// 钢材：L2id=189 and L2OrderNo=13当月产量：D1同比增幅：D3
		$gq_gc_data =  $this->get_hgData(189,13,"",$edate);
		// L2id=190 and L2OrderNo=17  粗钢日均产量取D1  钢材日均产量D3
		$rjcl_gc_cg_data = $this->get_hgData(190,17,"",$edate);

        $gqxs_str = "";
        $i = 0;
        foreach($gq_cg_data as $key => $item){
            $gqxs_str .= "<tr>
                              <td>".$gq_cg_data[$key]["DATE2"]."</td>
                              <td>".$gq_cg_data[$key]["D1"]."</td>
                              <td>".$gq_cg_data[$key]["D3"]."</td>
                              <td>".$rjcl_gc_cg_data[$key]["D1"]."</td>
                              <td>".$gq_gc_data[$key]["D1"]."</td>
                              <td>".$gq_gc_data[$key]["D3"]."</td>
                              <td>".$rjcl_gc_cg_data[$key]["D3"]."</td>
                          </tr>";
            $i++;
            if($i == 6){
                break;
            } 
        }

        //我国钢铁产量 文字描述
        $sdgt_scyp = html_entity_decode($this->_dao->get_sdgt_marketNews(3,$sdate,$edate,304));
        if($params["saving"] == 1 && $sdgt_scyp == ""){
            $sdgt_scyp = "根据国家统计局数据计算，".date("n",strtotime($gq_cg_data[$news_key1]["DATE"]))."月份我国粗钢和钢材产量分别为".$gq_cg_data[$news_key1]["D1"]."万吨和".$gq_gc_data[$news_key1]["D1"]."万吨，";
            if($gq_cg_data[$news_key1]["D3"] < 0){
                $sdgt_scyp .= "同比分别下降".abs($gq_cg_data[$news_key1]["D3"])."%";
            }else if($gq_cg_data[$news_key1]["D3"] > 0){
                $sdgt_scyp .= "同比分别增长".abs($gq_cg_data[$news_key1]["D3"])."%";
            }
            if($gq_gc_data[$news_key1]["D3"] < 0){
                $sdgt_scyp .= "和下降".abs($gq_gc_data[$news_key1]["D3"])."%";
            }else if($gq_gc_data[$news_key1]["D3"] > 0){
                $sdgt_scyp .= "和增长".abs($gq_gc_data[$news_key1]["D3"])."%";
            }
            $sdgt_scyp .= "；粗钢和钢材日均产量分别".$rjcl_gc_cg_data[$news_key1]["D1"]."万吨和".$rjcl_gc_cg_data[$news_key1]["D3"]."万吨，";
            if($rjcl_gc_cg_data[$news_key2]["D1"] != "" && $rjcl_gc_cg_data[$news_key2]["D1"] != 0){
                $rjcl_cg_hb = round(($rjcl_gc_cg_data[$news_key1]["D1"] - $rjcl_gc_cg_data[$news_key2]["D1"]) / $rjcl_gc_cg_data[$news_key2]["D1"] * 100,1);
                if($rjcl_cg_hb < 0){
                    $sdgt_scyp .= "环比分别下降".abs($rjcl_cg_hb)."%";
                }else if($rjcl_cg_hb > 0){
                    $sdgt_scyp .= "环比分别增长".abs($rjcl_cg_hb)."%";
                }
            }

            if($rjcl_gc_cg_data[$news_key2]["D3"] != "" && $rjcl_gc_cg_data[$news_key2]["D3"] != 0){
                $rjcl_gc_hb = round(($rjcl_gc_cg_data[$news_key1]["D3"] - $rjcl_gc_cg_data[$news_key2]["D3"]) / $rjcl_gc_cg_data[$news_key2]["D3"] * 100,1);
                if($rjcl_gc_hb < 0){
                    $sdgt_scyp .= "和下降".abs($rjcl_gc_hb)."%。";
                }else if($rjcl_gc_hb > 0){
                    $sdgt_scyp .= "和下降".abs($rjcl_gc_hb)."%。";
                }
            }
        }
        $this->assign( "marketNews304", $sdgt_scyp);
        
		// 钢材：L2id=192 and L2OrderNo=1 进口量：D1出口量：D2
		$ckyc_gc_data = $this->get_hgData(192,1,"",$edate);
		// 钢坯：L2id=193 and L2OrderNo=2 进口量：D1出口量：D2
		$ckyc_gp_data = $this->get_hgData(193,2,"",$edate);
        $gp_jck_str = "";
        $i = 0;
        foreach($ckyc_gc_data as $key => $item){
            $gp_jck_str .= "<tr>
                              <td>".$ckyc_gc_data[$key]["DATE2"]."</td>
                              <td>".$ckyc_gc_data[$key]["D1"]."</td>
                              <td>".$ckyc_gc_data[$key]["D2"]."</td>
                              <td>".round($ckyc_gp_data[$key]["D1"],1)."</td>
                              <td>".round($ckyc_gp_data[$key]["D2"],1)."</td>
                          </tr>";
            $i++;
            if($i == 6){
                break;
            }
        }

		// 3、我国粗钢和钢材资源供应量预测
		// 粗钢资源供应量：L2id=196 and L2OrderNo=40 当月：D1同比增幅：D3
		$cgzy_data = $this->get_hgData(196,40,"",$edate);
		// 钢材资源供应量：L2id=197 and L2OrderNo=41 当月：D1同比增幅：D3
		$gczy_gp_data = $this->get_hgData(197,41,"",$edate);
        
        //6月份我国粗钢和钢材资源供应量分别为8272万吨和11738万吨，日均环比分别增长2.9%和增长6.7%，同比分别下降1.5%和增长2.3%。
        //我国粗钢和钢材资源供应量 文字描述
        $sdgt_scyp = html_entity_decode($this->_dao->get_sdgt_marketNews(3,$sdate,$edate,306));
        if($params["saving"] == 1 && $sdgt_scyp == ""){
            $sdgt_scyp = date("n",strtotime($cgzy_data[$news_key1]["DATE"]))."月份我国粗钢和钢材资源供应量分别为".$cgzy_data[$news_key1]["D1"]."万吨和".$gczy_gp_data[$news_key1]["D1"]."万吨，";
            if($cgzy_data[$news_key1]["D3"] < 0){
                $sdgt_scyp .= "同比分别下降".abs($cgzy_data[$news_key1]["D3"])."%";
            }else if($cgzy_data[$news_key1]["D3"] > 0){
                $sdgt_scyp .= "同比分别增长".abs($cgzy_data[$news_key1]["D3"])."%";
            }
            if($gczy_gp_data[$news_key1]["D3"] < 0){
                $sdgt_scyp .= "和下降".abs($gczy_gp_data[$news_key1]["D3"])."%。";
            }else if($gczy_gp_data[$news_key1]["D3"] > 0){
                $sdgt_scyp .= "和增长".abs($gczy_gp_data[$news_key1]["D3"])."%。";
            }
            $sdgt_scyp .="预计XXXX月份我国粗钢和钢材资源供应量分别为XXXX万吨和XXXX万吨，同比分别下降XXXX%和增长XXXX%；XXXX月份我国粗钢和钢材资源供应量分别为XXXX万吨和XXXX万吨，同比分别增长XXXX%和增长XXXX%。";
        }
        $this->assign( "marketNews306", $sdgt_scyp);

        $zygyl_str = "";
        $i = 0;
        foreach($cgzy_data as $key => $item){
            $zygyl_str .= "<tr>
                              <td>".$cgzy_data[$key]["DATE2"]."</td>
                              <td>".$cgzy_data[$key]["D1"]."</td>
                              <td>".$cgzy_data[$key]["D3"]."</td>
                              <td>".$gczy_gp_data[$key]["D1"]."</td>
                              <td>".$gczy_gp_data[$key]["D3"]."</td>
                          </tr>";
            $i++;
            if($i == 6){
                break;
            }
        }

		 //5、下游行业需求预测
		// 房屋新开工面积：L2id=154 and L2OrderNo=2 取D1
		$fwkg_data = $this->get_hgData(154,2,"",$edate);

        //房屋施工面积：L2id=155 and L2OrderNo=3 取D2
        $fwsgmj_data = $this->get_hgData(155,3,"",$edate);

        //房屋销售面积：L2id=153 and L2OrderNo=1 取D3为当月 D4为累计
        $fwsxmj_data = $this->get_hgData(153,1,"",$edate);

		// 挖掘机：L2id=187 and L2OrderNo=3 取D1
		$wjj_data = $this->get_hgData(187,3,"",$edate);

        //水泥产量：L2id=161 and L2OrderNo=16
        $shuini_data = $this->get_hgData(161,16,"",$edate);

		// 汽车：L2id=172 and L2OrderNo=1 取D1
		$qc_data = $this->get_hgData(172,1,"",$edate);

		// 集装箱：L2id=185 and L2OrderNo=2 取D1
		$jzx_data = $this->get_hgData(185,2,"",$edate);

        // 造船完工量：L2id=468 and L2OrderNo=1 取D1
		$zcwgl_data = $this->get_hgData(468,1,"",$edate);

		// 造船新接订单：L2id=469 and L2OrderNo=2 取D1
		$zcxjdd_data = $this->get_hgData(469,2,"",$edate);

        // 造船手持订单量：L2id=470 and L2OrderNo=3 取D1
		$zcscddl_data = $this->get_hgData(470,3,"",$edate);

		// 空调：L2id=182 and L2OrderNo=8 取D1
		$kt_data = $this->get_hgData(182,8,"",$edate);

		// 冰箱：L2id=180 and L2OrderNo=6 取D1
		$bx_data = $this->get_hgData(180,6,"",$edate);

		// 洗衣机：L2id=178 and L2OrderNo=5 取D1
		$xyj_data = $this->get_hgData(178,5,"",$edate);

        $xyhy_str = "";
        $xyhy_table_str = '<table border="0" cellspacing="0" cellpadding="0">';
        $last_year = date("Y",strtotime($edate . "-1 year"));
        $curr_year = date("Y",strtotime($edate));
        $curr_month = date("n",strtotime($edate));
        if($curr_month == 1){
            $last_year = $last_year - 1;
            $curr_year = $curr_year - 1;
            $curr_month = 12;
            $last_month = 11;
        }else{
            // $curr_month = $curr_month-1;
            $last_month = $curr_month-1;
        }
        // $curr_month = $curr_month-1;

        $curr_key1 = $curr_year."年".$curr_month."月";
        $curr_key2 = $curr_year."年".$last_month."月";

        $last_key1 = $last_year."年".$curr_month."月";
        $last_key2 = $last_year."年".$last_month."月";
        $xyhy_table_str .= '<thead>
                                <tr>
                                    <td colspan="9">'.$last_year.'-'.$curr_year.'年主要用钢行业运行情况        同比：%</td>
                                </tr>
                            </thead>';
        $xyhy_table_str .=  '<tr style="background: #E6E8EB;">
                                <td colspan="2" rowspan="2">下游行业</td>
                                <td rowspan="2">指标</td>
                                <td colspan="3">累计同比%</td>
                                <td colspan="2">当月同比%</td>
                                <td rowspan="2">变化趋势</td>
                            </tr>';
        $xyhy_table_str .=  '<tr style="background: #E6E8EB;">
                                <td>'.$last_year.'年1-12月</td>
                                <td>'.$curr_year.'年1-'.$last_month.'月</td>
                                <td>'.$curr_year.'年1-'.$curr_month.'月</td>
                                <td>'.$last_month.'月</td>
                                <td>'.$curr_month.'月</td>
                            </tr>';
        $xyhy_table_str .=  '<tr>
                                <td rowspan="5">建筑业</td>
                                <td rowspan="4">房地产</td>
                                <td>投资额</td>
                                <td>'.$this->hg_data_zhangdie($fdc_data[$last_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($fdc_data[$curr_key2]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($fdc_data[$curr_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($fdc_data[$curr_key2]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie($fdc_data[$curr_key1]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie2($fdc_data[$curr_key1]["D3"] - $fdc_data[$curr_key2]["D3"]).'</td>
                            </tr>';
        $xyhy_table_str .=  '<tr>
                                <td>新开工面积</td>
                                <td>'.$this->hg_data_zhangdie($fwkg_data[$last_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($fwkg_data[$curr_key2]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($fwkg_data[$curr_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($fwkg_data[$curr_key2]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie($fwkg_data[$curr_key1]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie2($fwkg_data[$curr_key1]["D3"] - $fwkg_data[$curr_key2]["D3"]).'</td>
                            </tr>';
        $xyhy_table_str .=  '<tr>
                                <td>房屋施工面积</td>
                                <td>'.$this->hg_data_zhangdie($fwsgmj_data[$last_key1]["D2"]).'</td>
                                <td>'.$this->hg_data_zhangdie($fwsgmj_data[$curr_key2]["D2"]).'</td>
                                <td>'.$this->hg_data_zhangdie($fwsgmj_data[$curr_key1]["D2"]).'</td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>';
        $xyhy_table_str .=  '<tr>
                                <td>房屋销售面积</td>
                                <td>'.$this->hg_data_zhangdie($fwsxmj_data[$last_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($fwsxmj_data[$curr_key2]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($fwsxmj_data[$curr_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($fwsxmj_data[$curr_key2]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie($fwsxmj_data[$curr_key1]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie2($fwsxmj_data[$curr_key1]["D3"] - $fwsxmj_data[$curr_key2]["D3"]).'</td>
                            </tr>';
        $xyhy_table_str .=  '<tr>
                                <td>基建</td>
                                <td>投资额</td>
                                <td>'.$this->hg_data_zhangdie($jcjs_data[$last_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($jcjs_data[$curr_key2]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($jcjs_data[$curr_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($jcjs_data[$curr_key2]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie($jcjs_data[$curr_key1]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie2($jcjs_data[$curr_key1]["D3"] - $jcjs_data[$curr_key2]["D3"]).'</td>
                            </tr>';
        $xyhy_table_str .=  '<tr>
                                <td rowspan="10">制造业</td>
                                <td rowspan="2">机械</td>
                                <td>挖掘机产量</td>
                                <td>'.$this->hg_data_zhangdie($wjj_data[$last_key1]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie($wjj_data[$curr_key2]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie($wjj_data[$curr_key1]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie($wjj_data[$curr_key2]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie($wjj_data[$curr_key1]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie2($wjj_data[$curr_key1]["D3"] - $wjj_data[$curr_key2]["D3"]).'</td>
                            </tr>';
        $xyhy_table_str .=  '<tr>
                                <td>水泥专用设备产量</td>
                                <td>'.$this->hg_data_zhangdie($shuini_data[$last_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($shuini_data[$curr_key2]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($shuini_data[$curr_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($shuini_data[$curr_key2]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie($shuini_data[$curr_key1]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie2($shuini_data[$curr_key1]["D3"] - $shuini_data[$curr_key2]["D3"]).'</td>
                            </tr>';
        $xyhy_table_str .=  '<tr>
                                <td>汽车</td>
                                <td>产量</td>
                                <td>'.$this->hg_data_zhangdie($qc_data[$last_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($qc_data[$curr_key2]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($qc_data[$curr_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($qc_data[$curr_key2]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie($qc_data[$curr_key1]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie2($qc_data[$curr_key1]["D3"] - $qc_data[$curr_key2]["D3"]).'</td>
                            </tr>';
        $xyhy_table_str .=  '<tr>
                                <td rowspan="3">造船</td>
                                <td>造船完工量</td>
                                <td>'.$this->hg_data_zhangdie($zcwgl_data[$last_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($zcwgl_data[$curr_key2]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($zcwgl_data[$curr_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($zcwgl_data[$curr_key2]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($zcwgl_data[$curr_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie2($zcwgl_data[$curr_key1]["D4"] - $zcwgl_data[$curr_key2]["D4"]).'</td>
                            </tr>';
        $xyhy_table_str .=  '<tr>
                                <td>新接订单量</td>
                                <td>'.$this->hg_data_zhangdie($zcxjdd_data[$last_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($zcxjdd_data[$curr_key2]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($zcxjdd_data[$curr_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($zcxjdd_data[$curr_key2]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($zcxjdd_data[$curr_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie2($zcxjdd_data[$curr_key1]["D4"] - $zcxjdd_data[$curr_key2]["D4"]).'</td>
                            </tr>';
        $xyhy_table_str .=  '<tr>
                                <td>手持订单量</td>
                                <td>'.$this->hg_data_zhangdie($zcscddl_data[$last_key1]["D2"]).'</td>
                                <td>'.$this->hg_data_zhangdie($zcscddl_data[$curr_key2]["D2"]).'</td>
                                <td>'.$this->hg_data_zhangdie($zcscddl_data[$curr_key1]["D2"]).'</td>
                                <td>'.$this->hg_data_zhangdie($zcscddl_data[$curr_key2]["D2"]).'</td>
                                <td>'.$this->hg_data_zhangdie($zcscddl_data[$curr_key1]["D2"]).'</td>
                                <td>'.$this->hg_data_zhangdie2($zcscddl_data[$curr_key1]["D2"] - $zcscddl_data[$curr_key2]["D2"]).'</td>
                            </tr>';
        $xyhy_table_str .=  '<tr>
                                <td rowspan="3">家电</td>
                                <td>冰箱产量</td>
                                <td>'.$this->hg_data_zhangdie($bx_data[$last_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($bx_data[$curr_key2]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($bx_data[$curr_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($bx_data[$curr_key2]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie($bx_data[$curr_key1]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie2($bx_data[$curr_key1]["D3"] - $bx_data[$curr_key2]["D3"]).'</td>
                            </tr>';
        $xyhy_table_str .=  '<tr>
                                <td>洗衣机产量</td>
                                <td>'.$this->hg_data_zhangdie($xyj_data[$last_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($xyj_data[$curr_key2]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($xyj_data[$curr_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($xyj_data[$curr_key2]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie($xyj_data[$curr_key1]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie2($xyj_data[$curr_key1]["D3"] - $xyj_data[$curr_key2]["D3"]).'</td>
                            </tr>';
        $xyhy_table_str .=  '<tr>
                                <td>空调产量</td>
                                <td>'.$this->hg_data_zhangdie($kt_data[$last_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($kt_data[$curr_key2]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($kt_data[$curr_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($kt_data[$curr_key2]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie($kt_data[$curr_key1]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie2($kt_data[$curr_key1]["D3"] - $kt_data[$curr_key2]["D3"]).'</td>
                            </tr>';
        $xyhy_table_str .=  '<tr>
                                <td>集装箱</td>
                                <td>产量</td>
                                <td>'.$this->hg_data_zhangdie($jzx_data[$last_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($jzx_data[$curr_key2]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($jzx_data[$curr_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($jzx_data[$curr_key2]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie($jzx_data[$curr_key1]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie2($jzx_data[$curr_key1]["D3"] - $jzx_data[$curr_key2]["D3"]).'</td>
                            </tr>';
        // $i = 0;
        // foreach($fwkg_data as $key => $item){
        //     $xyhy_str .= "<tr>
        //                       <td>".$fwkg_data[$key]["DATE2"]."</td>
        //                       <td>".$fwkg_data[$key]["D3"]."</td>
        //                       <td>".$wjj_data[$key]["D3"]."</td>
        //                       <td>".$qc_data[$key]["D3"]."</td>
        //                       <td>".$jzx_data[$key]["D3"]."</td>
        //                       <td>".$zcxjdd_data[$key]["D3"]."</td>
        //                       <td>".$kt_data[$key]["D3"]."</td>
        //                       <td>".$bx_data[$key]["D3"]."</td>
        //                       <td>".$xyj_data[$key]["D3"]."</td>
        //                   </tr>";
        //     $i++;
        //     if($i == 6){
        //         break;
        //     }
        // }
        $this->assign("xyhy_table_str",$xyhy_table_str."</table>");
		//5、国内铁矿石供需预测
		//国产原矿产量 L2id=328 and L2OrderNo=5 取D1
		$wgyk_cl = $this->get_hgData(328,5,"",$edate);
		//铁矿石进口量 L2id=328 and L2OrderNo=5 取D1
		$tks_jkl = $this->get_hgData(579,5,"",$edate);
		//我国生铁产量L2id=188 and L2OrderNo=2 取D1
		$wgst_cl = $this->get_hgData(188,2,"",$edate);
		//铁矿石供需平衡
		$tks_gx_data = $this->get_tks_gx_pingheng($edate);
        //铁矿石出口量
        $tks_ck_data = $this->drcdao->query("select * FROM data_table WHERE dta_type = 'E' and dta_1='五、铁矿' ORDER BY dta_ym DESC LIMIT 15");
        $tks_ck_arr = array();
        foreach($tks_ck_data as $tksck_v){
            $riqi = date('Y年n月',strtotime($tksck_v["dta_ym"]));
            $tks_ck_arr[$riqi] = round(($tksck_v['dta_2'] / 10000),1);
        }

		$tksgx_str = "";
        $i = 0;
        foreach($wgyk_cl as $key => $item){
            $tksgx_str .= "<tr>
                              <td>".$wgyk_cl[$key]["DATE2"]."</td>
                              <td>".$wgyk_cl[$key]["D1"]."</td>
                              <td>".$tks_jkl[$key]["D1"]."</td>
                              <td>".$tks_ck_arr[$key]."</td>
                              <td>".$tks_gx_data[$key]["tiegongying"]."</td>
                              <td>".$wgst_cl[$key]["D1"]."</td>
                              <td>".$tks_gx_data[$key]["pinghengbiao"]."</td>
                          </tr>";
            $i++;
            if($i == 6){
                break;
            }
        }

		//焦煤供需平衡表
		$jm_gx_data = $this->get_jmmjt_gx_pingheng(2,$edate);
		//我国原煤产量及同比增幅（月）  L2id=333 and L2OrderNo=7 取D1
		$ym_cl = $this->get_hgData(333,7,"",$edate);
		//我国炼焦精煤产量（月）L2id=727 and L2OrderNo=34 取D1
		$ljjm_cl = $this->get_hgData(727,34,"",$edate);
		//我国焦炭产量 L2id=327 and L2OrderNo=14 取D1
		$jt_cl = $this->get_hgData(327,14,"",$edate);
		//炼焦煤进出口 	L2id=498 and L2OrderNo=9 D1为出口量 D2为进口量
		$ljm_jck = $this->get_hgData(498,9,"",$edate);

		$jmgx_str = "";
        $i = 0;
        foreach($ym_cl as $key => $item){
            $jmgx_str .= "<tr>
                              <td>".$ym_cl[$key]["DATE2"]."</td>
                              <td>".$ym_cl[$key]["D1"]."</td>
							  <td>".$ljjm_cl[$key]["D1"]."</td>
                              <td>".$ljm_jck[$key]["D2"]."</td>
                              <td>".$ljm_jck[$key]["D1"]."</td>
							  <td>".$jt_cl[$key]["D1"]."</td>
                              <td>".$jm_gx_data[$key]["data_gy"]."</td>
                              <td>".$jm_gx_data[$key]["data_xq"]."</td>
                              <td>".$jm_gx_data[$key]["data_phb"]."</td>
                          </tr>";
            $i++;
            if($i == 6){
                break;
            }
        }
		//生铁产量
		//焦炭产量
		//焦炭进出口量L2id=331 and L2OrderNo=6 进口取D1 出口取D2
		$jt_jck = $this->get_hgData(327,4,"",$edate);
		//焦炭供需平衡
		$jt_gx_data = $this->get_jmmjt_gx_pingheng(1,$edate);

		$jtgx_str = "";
        $i = 0;
		foreach($wgst_cl as $key => $item){
            $jtgx_str .= "<tr>
                              <td>".$wgst_cl[$key]["DATE2"]."</td>
                              <td>".$wgst_cl[$key]["D1"]."</td>
							  <td>".$jt_cl[$key]["D1"]."</td>
                              <td>".$jt_jck[$key]["D1"]."</td>
                              <td>".$jt_jck[$key]["D2"]."</td>
                              <td>".$jt_gx_data[$key]["data_gy"]."</td>
                              <td>".$jt_gx_data[$key]["data_xq"]."</td>
                              <td>".$jt_gx_data[$key]["data_phb"]."</td>
                          </tr>";
            $i++;
            if($i == 6){
                break;
            }
        }

		//本月重大事件及市场影响分析
		$byzdsx_marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(3,$sdate,$edate,5));
        $this->assign( "byzdsx_marketNews", $byzdsx_marketNews);
		//后市判断
		$hspd_marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(3,$sdate,$edate,6));
        $this->assign( "hspd_marketNews", $hspd_marketNews);

		$next_month_first_date = date('Y-m-01',strtotime("next month",strtotime($sdate)));
        $next_month_last_date = date('Y-m-d', strtotime("$next_month_first_date +1 month -1 day"));
		//取钢材预测数据
        $gc_yue_yc_data2 = $this->_dao->get_yc_data(3,1,$next_month_first_date,$next_month_last_date);
		$gc_yue_yc_str2 = $this->get_yc_table_tr($gc_yue_yc_data2,1,2);
        // $gc_yc_price_str2 = "<tr>
        //                     <td>预测价格</td>
        //                     <td>".($ret_arr[0]['tprice']['houban_price'] + ($gc_yue_yc_data2['厚板']['yc_zd']))."</td>
        //                     <td>".($ret_arr[0]['tprice']['rzbj_price'] + ($gc_yue_yc_data2['热卷']['yc_zd']))."</td>
        //                     <td>".($ret_arr[0]['tprice']['lzbj_price'] + ($gc_yue_yc_data2['冷卷']['yc_zd']))."</td>
        //                     <td>".($ret_arr[0]['tprice']['suanxi_price'] + ($gc_yue_yc_data2['酸洗']['yc_zd']))."</td>
        //                     <td>".($ret_arr[0]['tprice']['duxin_price'] + ($gc_yue_yc_data2['镀锌']['yc_zd']))."</td>
        //                     <td>".($ret_arr[0]['tprice']['lw_price'] + ($gc_yue_yc_data2['螺纹钢']['yc_zd']))."</td>
        //                     <td>".($ret_arr[0]['tprice']['h_xg_price'] + ($gc_yue_yc_data2['H型钢']['yc_zd']))."</td>
        //                     <td>".($ret_arr[0]['tprice']['tjg_price'] + ($gc_yue_yc_data2['碳结钢']['yc_zd']))."</td>
        //                     <td>".($ret_arr[0]['tprice']['fp_price'] + ($gc_yue_yc_data2['方坯']['yc_zd']))."</td>
        //                 </tr>";
        $gc_yc_price_str2 = $this->get_yc_gc_table_tr($ret_arr[0],$gc_yue_yc_data2);
        $gc_yue_yc_str2 .= $gc_yc_price_str2;

        //修正预测 钢材
        $gc_yue_yc_data_update2 = $this->_dao->get_yc_data(301,1,$next_month_first_date,$next_month_last_date);
        $update_gc_tr2 = $this->get_yc_gc_table_tr($ret_arr[0],$gc_yue_yc_data_update2,1);
        $gc_yue_yc_str2 .= $update_gc_tr2;

		//取原料预测数据
        $ll_yue_yc_data2 = $this->_dao->get_yc_data(3,2,$next_month_first_date,$next_month_last_date);
		$ll_yue_yc_str2 = $this->get_yc_table_tr($ll_yue_yc_data2,2,2);
        // $ll_yc_price_str2 = "<tr>
        //                     <td>预测价格</td>
        //                     <td>".($ret_arr[1]['tprice']['tks_price'] + ($ll_yue_yc_data2['铁矿石']['yc_zd']))."</td>
        //                     <td>".($ret_arr[1]['tprice']['fg_price'] + ($ll_yue_yc_data2['废钢']['yc_zd']))."</td>
        //                     <td>".($ret_arr[1]['tprice']['gm_price'] + ($ll_yue_yc_data2['硅锰']['yc_zd']))."</td>
        //                     <td>".($ret_arr[1]['tprice']['gt_price'] + ($ll_yue_yc_data2['硅铁']['yc_zd']))."</td>
        //                     <td>".($ret_arr[1]['tprice']['gtmt_price'] + ($ll_yue_yc_data2['高锰']['yc_zd']))."</td>
        //                     <td>".($ret_arr[1]['tprice']['nieban_price'] + ($ll_yue_yc_data2['镍板']['yc_zd']))."</td>
        //                     <td>".($ret_arr[1]['tprice']['qimei_price'] + ($ll_yue_yc_data2['气煤']['yc_zd']))."</td>
        //                     <td>".($ret_arr[1]['tprice']['jiaomei_price'] + ($ll_yue_yc_data2['1/3焦煤']['yc_zd']))."</td>
        //                     <td>".($ret_arr[1]['tprice']['dljm_price'] + ($ll_yue_yc_data2['主焦煤']['yc_zd']))."</td>
        //                     <td>".($ret_arr[1]['tprice']['shoumei_price'] + ($ll_yue_yc_data2['瘦煤']['yc_zd']))."</td>
        //                     <td>".($ret_arr[1]['tprice']['zyjj_price'] + ($ll_yue_yc_data2['冶金焦']['yc_zd']))."</td>
        //                 </tr>";
        $ll_yc_price_str2 = $this->get_yc_ll_table_tr($ret_arr[1],$ll_yue_yc_data2,0,0);
        $ll_yue_yc_str2 .= $ll_yc_price_str2;


        //修正预测 原料
        $ll_yue_yc_data_update2 = $this->_dao->get_yc_data(301,2,$next_month_first_date,$next_month_last_date);
        $update_ll_tr2 = $this->get_yc_ll_table_tr($ret_arr[1],$ll_yue_yc_data_update2,1,0);
        $ll_yue_yc_str2 .= $update_ll_tr2;

		$this->assign("gc_yue_yc_str2",$gc_yue_yc_str2);
		$this->assign("ll_yue_yc_str2",$ll_yue_yc_str2);

		$this->assign( "jtgx_str", $jtgx_str );
		$this->assign( "jmgx_str", $jmgx_str );
		$this->assign( "tksgx_str", $tksgx_str );
        $this->assign( "xyhy_str", $xyhy_str );
        $this->assign( "zygyl_str", $zygyl_str );
        $this->assign( "gp_jck_str", $gp_jck_str );
        $this->assign( "gqxs_str", $gqxs_str );
        $this->assign( "czjrsj_str", $czjrsj_str );
        $this->assign( "jjsj_str", $jjsj_str );
		$this->assign("gc_data",$ret_arr[0]);
		$this->assign("yrl_data",$ret_arr[1]);
		$this->assign("day",date('n月j日',strtotime($day)));
        $this->assign("mode",$params['mode']);
        $this->assign("params",$params);
	}

    function hg_jj_fenxi($params){
        $edate = date("Y-m-d",strtotime(date("Y-m-d") . " -2 month"));
        if($params["edate"] != ""){
            $edate = $params["edate"];
        }
        $sdate = date("Y-m-d");
        if($params["sdate"] != ""){
            $sdate = $params["sdate"];
        }

        //下游行业需求 文字描述
        $sdgt_scyp = html_entity_decode($this->_dao->get_sdgt_marketNews(7,$sdate,$edate,701));
        $this->assign( "marketNews701", $sdgt_scyp);
        $this->assign( "type", "7");

        //房地产开发投资: L2id=141 and L2OrderNo=43
		$fdc_data = $this->get_hgData(141,43,"",$edate);

        //基础设施建设投资: L2id=203 and L2OrderNo=42
		$jcjs_data = $this->get_hgData(203,42,"",$edate);

        //5、下游行业需求预测
		// 房屋新开工面积：L2id=154 and L2OrderNo=2 取D1
		$fwkg_data = $this->get_hgData(154,2,"",$edate);

        //房屋施工面积：L2id=155 and L2OrderNo=3 取D2
        $fwsgmj_data = $this->get_hgData(155,3,"",$edate);

        //房屋销售面积：L2id=153 and L2OrderNo=1 取D3为当月 D4为累计
        $fwsxmj_data = $this->get_hgData(153,1,"",$edate);

		// 挖掘机：L2id=187 and L2OrderNo=3 取D1
		$wjj_data = $this->get_hgData(187,3,"",$edate);

        //水泥产量：L2id=161 and L2OrderNo=16
        $shuini_data = $this->get_hgData(161,16,"",$edate);

		// 汽车：L2id=172 and L2OrderNo=1 取D1
		$qc_data = $this->get_hgData(172,1,"",$edate);

		// 集装箱：L2id=185 and L2OrderNo=2 取D1
		$jzx_data = $this->get_hgData(185,2,"",$edate);

        // 造船完工量：L2id=468 and L2OrderNo=1 取D1
		$zcwgl_data = $this->get_hgData(468,1,"",$edate);

		// 造船新接订单：L2id=469 and L2OrderNo=2 取D1
		$zcxjdd_data = $this->get_hgData(469,2,"",$edate);

        // 造船手持订单量：L2id=470 and L2OrderNo=3 取D1
		$zcscddl_data = $this->get_hgData(470,3,"",$edate);

		// 空调：L2id=182 and L2OrderNo=8 取D1
		$kt_data = $this->get_hgData(182,8,"",$edate);

		// 冰箱：L2id=180 and L2OrderNo=6 取D1
		$bx_data = $this->get_hgData(180,6,"",$edate);

		// 洗衣机：L2id=178 and L2OrderNo=5 取D1
		$xyj_data = $this->get_hgData(178,5,"",$edate);

        $xyhy_str = "";
        $xyhy_table_str = '<table border="0" cellspacing="0" cellpadding="0">';
        $last_year = date("Y",strtotime($edate . "-1 year"));
        $curr_year = date("Y",strtotime($edate));
        $curr_month = date("n",strtotime($edate));
        $curr_month = $curr_month-1;
        $last_month = $curr_month-1;

        $curr_key1 = $curr_year."年".$curr_month."月";
        $curr_key2 = $curr_year."年".$last_month."月";

        $last_key1 = $last_year."年".$curr_month."月";
        $last_key2 = $last_year."年".$last_month."月";
        $xyhy_table_str .= '<thead>
                                <tr>
                                    <td colspan="9">'.$last_year.'-'.$curr_year.'年主要用钢行业运行情况        同比：%</td>
                                </tr>
                            </thead>';
        $xyhy_table_str .=  '<tr style="background: #E6E8EB;">
                                <td colspan="2" rowspan="2">下游行业</td>
                                <td rowspan="2">指标</td>
                                <td colspan="3">累计同比%</td>
                                <td colspan="2">当月同比%</td>
                                <td rowspan="2">变化趋势</td>
                            </tr>';
        $xyhy_table_str .=  '<tr style="background: #E6E8EB;">
                                <td>'.$last_year.'年1-12月</td>
                                <td>'.$curr_year.'年1-'.$last_month.'月</td>
                                <td>'.$curr_year.'年1-'.$curr_month.'月</td>
                                <td>'.$last_month.'月</td>
                                <td>'.$curr_month.'月</td>
                            </tr>';
        $xyhy_table_str .=  '<tr>
                                <td rowspan="5">建筑业</td>
                                <td rowspan="4">房地产</td>
                                <td>投资额</td>
                                <td>'.$this->hg_data_zhangdie($fdc_data[$last_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($fdc_data[$curr_key2]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($fdc_data[$curr_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($fdc_data[$curr_key2]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie($fdc_data[$curr_key1]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie2($fdc_data[$curr_key1]["D3"] - $fdc_data[$curr_key2]["D3"]).'</td>
                            </tr>';
        $xyhy_table_str .=  '<tr>
                                <td>新开工面积</td>
                                <td>'.$this->hg_data_zhangdie($fwkg_data[$last_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($fwkg_data[$curr_key2]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($fwkg_data[$curr_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($fwkg_data[$curr_key2]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie($fwkg_data[$curr_key1]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie2($fwkg_data[$curr_key1]["D3"] - $fwkg_data[$curr_key2]["D3"]).'</td>
                            </tr>';
        $xyhy_table_str .=  '<tr>
                                <td>房屋施工面积</td>
                                <td>'.$this->hg_data_zhangdie($fwsgmj_data[$last_key1]["D2"]).'</td>
                                <td>'.$this->hg_data_zhangdie($fwsgmj_data[$curr_key2]["D2"]).'</td>
                                <td>'.$this->hg_data_zhangdie($fwsgmj_data[$curr_key1]["D2"]).'</td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>';
        $xyhy_table_str .=  '<tr>
                                <td>房屋销售面积</td>
                                <td>'.$this->hg_data_zhangdie($fwsxmj_data[$last_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($fwsxmj_data[$curr_key2]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($fwsxmj_data[$curr_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($fwsxmj_data[$curr_key2]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie($fwsxmj_data[$curr_key1]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie2($fwsxmj_data[$curr_key1]["D3"] - $fwsxmj_data[$curr_key2]["D3"]).'</td>
                            </tr>';
        $xyhy_table_str .=  '<tr>
                                <td>基建</td>
                                <td>投资额</td>
                                <td>'.$this->hg_data_zhangdie($jcjs_data[$last_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($jcjs_data[$curr_key2]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($jcjs_data[$curr_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($jcjs_data[$curr_key2]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie($jcjs_data[$curr_key1]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie2($jcjs_data[$curr_key1]["D3"] - $jcjs_data[$curr_key2]["D3"]).'</td>
                            </tr>';
        $xyhy_table_str .=  '<tr>
                                <td rowspan="10">制造业</td>
                                <td rowspan="2">机械</td>
                                <td>挖掘机产量</td>
                                <td>'.$this->hg_data_zhangdie($wjj_data[$last_key1]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie($wjj_data[$curr_key2]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie($wjj_data[$curr_key1]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie($wjj_data[$curr_key2]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie($wjj_data[$curr_key1]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie2($wjj_data[$curr_key1]["D3"] - $wjj_data[$curr_key2]["D3"]).'</td>
                            </tr>';
        $xyhy_table_str .=  '<tr>
                                <td>水泥专用设备产量</td>
                                <td>'.$this->hg_data_zhangdie($shuini_data[$last_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($shuini_data[$curr_key2]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($shuini_data[$curr_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($shuini_data[$curr_key2]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie($shuini_data[$curr_key1]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie2($shuini_data[$curr_key1]["D3"] - $shuini_data[$curr_key2]["D3"]).'</td>
                            </tr>';
        $xyhy_table_str .=  '<tr>
                                <td>汽车</td>
                                <td>产量</td>
                                <td>'.$this->hg_data_zhangdie($qc_data[$last_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($qc_data[$curr_key2]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($qc_data[$curr_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($qc_data[$curr_key2]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie($qc_data[$curr_key1]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie2($qc_data[$curr_key1]["D3"] - $qc_data[$curr_key2]["D3"]).'</td>
                            </tr>';
        $xyhy_table_str .=  '<tr>
                                <td rowspan="3">造船</td>
                                <td>造船完工量</td>
                                <td>'.$this->hg_data_zhangdie($zcwgl_data[$last_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($zcwgl_data[$curr_key2]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($zcwgl_data[$curr_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($zcwgl_data[$curr_key2]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($zcwgl_data[$curr_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie2($zcwgl_data[$curr_key1]["D4"] - $zcwgl_data[$curr_key2]["D4"]).'</td>
                            </tr>';
        $xyhy_table_str .=  '<tr>
                                <td>新接订单量</td>
                                <td>'.$this->hg_data_zhangdie($zcxjdd_data[$last_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($zcxjdd_data[$curr_key2]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($zcxjdd_data[$curr_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($zcxjdd_data[$curr_key2]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($zcxjdd_data[$curr_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie2($zcxjdd_data[$curr_key1]["D4"] - $zcxjdd_data[$curr_key2]["D4"]).'</td>
                            </tr>';
        $xyhy_table_str .=  '<tr>
                                <td>手持订单量</td>
                                <td>'.$this->hg_data_zhangdie($zcscddl_data[$last_key1]["D2"]).'</td>
                                <td>'.$this->hg_data_zhangdie($zcscddl_data[$curr_key2]["D2"]).'</td>
                                <td>'.$this->hg_data_zhangdie($zcscddl_data[$curr_key1]["D2"]).'</td>
                                <td>'.$this->hg_data_zhangdie($zcscddl_data[$curr_key2]["D2"]).'</td>
                                <td>'.$this->hg_data_zhangdie($zcscddl_data[$curr_key1]["D2"]).'</td>
                                <td>'.$this->hg_data_zhangdie2($zcscddl_data[$curr_key1]["D2"] - $zcscddl_data[$curr_key2]["D2"]).'</td>
                            </tr>';
        $xyhy_table_str .=  '<tr>
                                <td rowspan="3">家电</td>
                                <td>冰箱产量</td>
                                <td>'.$this->hg_data_zhangdie($bx_data[$last_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($bx_data[$curr_key2]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($bx_data[$curr_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($bx_data[$curr_key2]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie($bx_data[$curr_key1]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie2($bx_data[$curr_key1]["D3"] - $bx_data[$curr_key2]["D3"]).'</td>
                            </tr>';
        $xyhy_table_str .=  '<tr>
                                <td>洗衣机产量</td>
                                <td>'.$this->hg_data_zhangdie($xyj_data[$last_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($xyj_data[$curr_key2]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($xyj_data[$curr_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($xyj_data[$curr_key2]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie($xyj_data[$curr_key1]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie2($xyj_data[$curr_key1]["D3"] - $xyj_data[$curr_key2]["D3"]).'</td>
                            </tr>';
        $xyhy_table_str .=  '<tr>
                                <td>空调产量</td>
                                <td>'.$this->hg_data_zhangdie($kt_data[$last_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($kt_data[$curr_key2]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($kt_data[$curr_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($kt_data[$curr_key2]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie($kt_data[$curr_key1]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie2($kt_data[$curr_key1]["D3"] - $kt_data[$curr_key2]["D3"]).'</td>
                            </tr>';
        $xyhy_table_str .=  '<tr>
                                <td>集装箱</td>
                                <td>产量</td>
                                <td>'.$this->hg_data_zhangdie($jzx_data[$last_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($jzx_data[$curr_key2]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($jzx_data[$curr_key1]["D4"]).'</td>
                                <td>'.$this->hg_data_zhangdie($jzx_data[$curr_key2]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie($jzx_data[$curr_key1]["D3"]).'</td>
                                <td>'.$this->hg_data_zhangdie2($jzx_data[$curr_key1]["D3"] - $jzx_data[$curr_key2]["D3"]).'</td>
                            </tr>';
        
        $this->assign("xyhy_table_str",$xyhy_table_str."</table>");
        $params["sdate"] = $sdate;
        $params["edate"] = $edate;
        $this->assign("mode",$params['mode']);
        $this->get_yue_yc_charts($edate);
        $this->assign("params",$params);
    }

    function hg_data_zhangdie($int,$decimal=1){
        //返回涨跌变化
		$intstr = "";
		if($int<0){
			$intstr = "<font color=green><strong>-".round(abs($int),$decimal)."</strong></font>";
		}elseif($int>0){
			$intstr = "<font color=red><strong>".round(abs($int),$decimal)."</strong></font>";
		}elseif($int=="" || $int==0){
			$intstr = "<strong>--</strong>";
		}else{
			$intstr = "<font ><strong>".$int."</strong></font>";
		}
		return $intstr;
    }
    function hg_data_zhangdie2($int){
        //返回涨跌变化
		$intstr = "";
		if($int<0){
			$intstr = "<font color=green><strong>↓</strong></font>";
		}elseif($int>0){
			$intstr = "<font color=red><strong>↑</strong></font>";
		}elseif($int=="" || $int==0){
			$intstr = "<strong>--</strong>";
		}
		return $intstr;
    }

	function xun_dingjia($params){
        if($params['saving']=="2"){
            $params['saving']="1";
        }
		$sdate = $params["sdate"];
        $edate = $params["edate"];
		if($sdate == "" || $edate == ""){
			echo "查询失败，请重试！";exit;
		}
		if($params["titleName"] == ""){
            $params["titleName"] = date("Y年n月j日",strtotime($edate))."旬定价";
        }
		$bx_sday = date("j",strtotime($sdate));
		if($bx_sday == 1){
			$edate2 = date("Y-m-d",strtotime($sdate."-1 day"));
			$sdate2 = date("Y-m-21",strtotime($edate2));
		}else if($bx_sday == 11){
			$edate2 = date("Y-m-d",strtotime($sdate."-1 day"));
			$sdate2 = date("Y-m-01",strtotime($edate2));
		}else if($bx_sday == 21){
			$edate2 = date("Y-m-d",strtotime($sdate."-1 day"));
			$sdate2 = date("Y-m-11",strtotime($edate2));
		}
		$yue_avg_sdate = date("Y-m-01",strtotime($edate));
		// echo $sdate2."--".$edate2;

        $my_steel_data_arr = array(
            "莱芜","临沂","青岛"
        );
        $my_steel_by_avg_arr = array();
        //我的钢铁数据 本旬均价
        $my_steel_laiwu_bx_avg = $this->drcdao->getOne("select avg(b.dta4) from sd_steel_data_table_base as a,sd_steel_data_table as b where a.id = b.baseid and a.type = 1 and bigtype = 1 and a.isdel = 0 and a.date >= '".$sdate."' and a.date <= '".$edate."' and dta1= '螺纹钢' and dta3 = '莱芜'");
        //我的钢铁数据 上旬均价
        $my_steel_laiwu_sx_avg = $this->drcdao->getOne("select avg(b.dta4) from sd_steel_data_table_base as a,sd_steel_data_table as b where a.id = b.baseid and a.type = 1 and bigtype = 1 and a.isdel = 0 and a.date >= '".$sdate2."' and a.date <= '".$edate2."' and dta1= '螺纹钢' and dta3 = '莱芜'");
        //本月均价
        foreach($my_steel_data_arr as $city){
            $my_steel_by_avg_arr[] = $this->drcdao->getOne("select avg(b.dta4) from sd_steel_data_table_base as a,sd_steel_data_table as b where a.id = b.baseid and a.type = 1 and bigtype = 1 and a.isdel = 0 and a.date >= '".$yue_avg_sdate."' and a.date <= '".$edate."' and dta1= '螺纹钢' and dta3 = '".$city."'");
        }
        $table_tr_str = "<tr><td>我的钢铁</td>
                        <td>".round($my_steel_laiwu_sx_avg)."</td>
                        <td>".round($my_steel_laiwu_bx_avg)."</td>
                        <td>".round($my_steel_by_avg_arr[0])."</td>
                        <td>".round($my_steel_by_avg_arr[1])."</td>
                        <td>".round(($my_steel_by_avg_arr[1]-$my_steel_by_avg_arr[0]))."</td>
                        <td>".round($my_steel_by_avg_arr[2])."</td>
                        <td>".round(($my_steel_by_avg_arr[2]-$my_steel_by_avg_arr[0]))."</td></tr>"; 

		//一、φ22mmHRB400E螺纹钢目标市场本旬均价变化情况 莱芜、临沂、青岛、 692043、872023、182044 新：6920231、8720232、1820233
		$priceids = "6920231";
		//本旬均价
		$lwg_data = $this->hq_price_7_week($edate,$sdate,$priceids,$edate2,$sdate2);
		//莱芜 临沂 青岛 本月均价 
        $avg_price_arr = $this->maindao->query("select avg(price) as avg_price,mastertopid from marketconditions where mastertopid in ('6920231','8720232','1820233') AND mconmanagedate >'" . $yue_avg_sdate . " 00:00:00' AND mconmanagedate <'" . $edate . "  23:59:59' group by mastertopid");
        $laiwu_avg = 0;
        $linyi_avg = 0;
        $qingdao_avg = 0;
        foreach($avg_price_arr as $avg_item){
            if($avg_item['mastertopid'] == '6920231'){
                $laiwu_avg = $avg_item['avg_price'];
            }else if($avg_item['mastertopid'] == '8720232'){
                $linyi_avg = $avg_item['avg_price'];
            }else if($avg_item['mastertopid'] == '1820233'){
                $qingdao_avg = $avg_item['avg_price'];
            }
        }
		// $laiwu_avg = $this->maindao->getOne("select avg(price) from marketconditions where topicture = '692043' AND mconmanagedate >'" . $yue_avg_sdate . " 00:00:00' AND mconmanagedate <'" . $edate . "  23:59:59'");
		// $linyi_avg = $this->maindao->getOne("select avg(price) from marketconditions where topicture = '872023' AND mconmanagedate >'" . $yue_avg_sdate . " 00:00:00' AND mconmanagedate <'" . $edate . "  23:59:59'");
		// $qingdao_avg = $this->maindao->getOne("select avg(price) from marketconditions where topicture = '182044' AND mconmanagedate >'" . $yue_avg_sdate . " 00:00:00' AND mconmanagedate <'" . $edate . "  23:59:59'");
		
		$table_tr_str .= "<tr><td>钢之家</td>
						 <td>".$lwg_data["lprice"]["6920231"]."</td>
						 <td>".$lwg_data["tprice"]["6920231"]."</td>
						 <td>".round($laiwu_avg)."</td>
						 <td>".round($linyi_avg)."</td>
						 <td>".round(($linyi_avg-$laiwu_avg))."</td>
						 <td>".round($qingdao_avg)."</td>
						 <td>".round(($qingdao_avg-$laiwu_avg))."</td></tr>"; 

        $wz_avg1 = round(($lwg_data["lprice"]["6920231"] + $my_steel_laiwu_sx_avg) / 2);
        $wz_avg2 = round(($lwg_data["tprice"]["6920231"] + $my_steel_laiwu_bx_avg) / 2);
        $wz_avg3 = round(($laiwu_avg + $my_steel_by_avg_arr[0]) / 2);
        $wz_avg4 = round(($linyi_avg + $my_steel_by_avg_arr[1]) / 2);
        $wz_avg5 = $wz_avg4 - $wz_avg3;
        $wz_avg6 = round(($qingdao_avg + $my_steel_by_avg_arr[2]) / 2);
        $wz_avg7 = $wz_avg6 - $wz_avg3;
        $table_tr_str .= "<tr><td>网站均价</td>
						 <td>".$wz_avg1."</td>
						 <td>".$wz_avg2."</td>
						 <td>".$wz_avg3."</td>
						 <td>".$wz_avg4."</td>
						 <td>".$wz_avg5."</td>
						 <td>".$wz_avg6."</td>
						 <td>".$wz_avg7."</td></tr>"; 

        //市场成交价 本旬均价
        $my_steel_laiwu_bx_city_price = $this->drcdao->getOne("select avg(b.dta4) from sd_steel_data_table_base as a,sd_steel_data_table as b where a.id = b.baseid and a.type = 1 and bigtype = 2 and a.isdel = 0 and a.date >= '".$sdate."' and a.date <= '".$edate."' and dta1= '螺纹钢' and dta3 = '莱芜'");
        
        //市场成交价 上旬均价
        $my_steel_laiwu_sx_city_price = $this->drcdao->getOne("select avg(b.dta4) from sd_steel_data_table_base as a,sd_steel_data_table as b where a.id = b.baseid and a.type = 1 and bigtype = 2 and a.isdel = 0 and a.date >= '".$sdate2."' and a.date <= '".$edate2."' and dta1= '螺纹钢' and dta3 = '莱芜'");
       
        //本月均价
        $my_steel_by_city_price_avg_arr = array();
        foreach($my_steel_data_arr as $city2){
            $my_steel_by_city_price_avg_arr[] = $this->drcdao->getOne("select avg(b.dta4) from sd_steel_data_table_base as a,sd_steel_data_table as b where a.id = b.baseid and a.type = 1 and bigtype = 2 and a.isdel = 0 and a.date >= '".$yue_avg_sdate."' and a.date <= '".$edate."' and dta1= '螺纹钢' and dta3 = '".$city2."'");
        }
        $table_tr_str .= "<tr><td>市场成交价</td>
                        <td>".round($my_steel_laiwu_sx_city_price)."</td>
                        <td>".round($my_steel_laiwu_bx_city_price)."</td>
                        <td>".round($my_steel_by_city_price_avg_arr[0])."</td>
                        <td>".round($my_steel_by_city_price_avg_arr[1])."</td>
                        <td>".round(($my_steel_by_city_price_avg_arr[1]-$my_steel_by_city_price_avg_arr[0]))."</td>
                        <td>".round($my_steel_by_city_price_avg_arr[2])."</td>
                        <td>".round(($my_steel_by_city_price_avg_arr[2]-$my_steel_by_city_price_avg_arr[0]))."</td></tr>"; 
		
		//二、XX月YY日国内相关市场φ22mmHRB400E螺纹钢价格情况 南京、杭州、上海、广州、合肥、北京、天津 112023、082023、072023、282023、222023、392023、402023
		$priceids2 = "'112023','082023','072023','282023','222023','392023','402023'";
		//本旬与上旬价格
		$gnxgsc_data = $this->hq_price_6($edate,$sdate,$priceids2,$edate2,$sdate2);
		//本旬结束日期价格及上一个工作日价格
        $sx_edate = date("Y-m-d",strtotime($edate." +1 day"));
		$date3 =  $this->get_xhdate($sx_edate);
		$date4 =  $this->get_xhdate($sdate);
		$bx_day_price_arr = $this->hq_price_6($date3,$date3,$priceids2,$date4,$date4);
		$table_tr_str2 = "<tr><td>".date("n月j日",strtotime($date3))."</td>
						 <td>".$bx_day_price_arr["tprice"]["112023"]."</td>
						 <td>".$bx_day_price_arr["tprice"]["082023"]."</td>
						 <td>".$bx_day_price_arr["tprice"]["072023"]."</td>
						 <td>".$bx_day_price_arr["tprice"]["282023"]."</td>
						 <td>".$bx_day_price_arr["tprice"]["222023"]."</td>
						 <td>".$bx_day_price_arr["tprice"]["392023"]."</td>
						 <td>".$bx_day_price_arr["tprice"]["402023"]."</td></tr>";

		$table_tr_str2 .= "<tr><td>较前旬末</td>
						 <td>".$bx_day_price_arr["zd"]["112023"]."</td>
						 <td>".$bx_day_price_arr["zd"]["082023"]."</td>
						 <td>".$bx_day_price_arr["zd"]["072023"]."</td>
						 <td>".$bx_day_price_arr["zd"]["282023"]."</td>
						 <td>".$bx_day_price_arr["zd"]["222023"]."</td>
						 <td>".$bx_day_price_arr["zd"]["392023"]."</td>
						 <td>".$bx_day_price_arr["zd"]["402023"]."</td></tr>"; 
		
		 $table_tr_str2 .= "<tr><td>本旬均价</td>
						 <td>".$gnxgsc_data["tprice"]["112023"]."</td>
						 <td>".$gnxgsc_data["tprice"]["082023"]."</td>
						 <td>".$gnxgsc_data["tprice"]["072023"]."</td>
						 <td>".$gnxgsc_data["tprice"]["282023"]."</td>
						 <td>".$gnxgsc_data["tprice"]["222023"]."</td>
						 <td>".$gnxgsc_data["tprice"]["392023"]."</td>
						 <td>".$gnxgsc_data["tprice"]["402023"]."</td></tr>"; 

		$table_tr_str2 .= "<tr><td>较前旬涨跌</td>
						 <td>".$gnxgsc_data["zd"]["112023"]."</td>
						 <td>".$gnxgsc_data["zd"]["082023"]."</td>
						 <td>".$gnxgsc_data["zd"]["072023"]."</td>
						 <td>".$gnxgsc_data["zd"]["282023"]."</td>
						 <td>".$gnxgsc_data["zd"]["222023"]."</td>
						 <td>".$gnxgsc_data["zd"]["392023"]."</td>
						 <td>".$gnxgsc_data["zd"]["402023"]."</td></tr>";
		//三、沙钢螺纹钢价格调整情况
		$sglw_marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(4,$sdate,$edate,7));
        if($sglw_marketNews=="" && $params["saving"] == 1){
            // $sglw_marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(4,$sdate2,$edate2,7));
            //　　1月11日至1月20日（本旬第一个工作日-本旬最后一个工作日），山东及周边地区钢厂螺纹钢累计价格调幅：1、石横特钢累计下调10元，莱钢永锋下调10元，22mmHRB400E执行价均为3280元/吨；2、敬业下调10元，18mmHRB400E执行价为3270元/吨；3、山西建龙上调110元，20mmHRB400E执行价均为3360元/吨；山西建邦下调10元，18mmHRB400E执行价3380元/吨。
            $dateList = array("this_start_date"=>$sdate,
            "this_end_date"=>$edate,
            "last_start_date"=>$sdate2,
            "last_end_date"=>$edate2);
            
            $sglw_marketNews = date("n月j日",strtotime($sdate))."至".date("n月j日",strtotime($edate))."，山东及周边地区钢厂螺纹钢累计价格调幅：";
            $priceInfo = $this->gcdao->getSteelPrice($dateList,$GLOBALS['WEEK_ONLY_ID_LIST']);
            foreach($priceInfo as $price_v){
                if($price_v["variety"] == "螺纹钢"){
                    $sglw_marketNews .= $price_v["gc_name"].$this->tiaojia_zhangdie($price_v["change_rate"]).",".$price_v["specification"]."执行价格为".$price_v["price"]."元/吨；";
                }
            }
        }
		//四、N旬螺纹钢定价建议
		$lwdj_marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(4,$sdate,$edate,8));
        if($lwdj_marketNews=="" && $params["saving"] == 1){
            $lwdj_marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(4,$sdate2,$edate2,8));
        }

         //螺纹钢成交量走势图
         $end_year = date("Y",strtotime($edate));
         $start_year = $end_year - 2;
         $yearStr = $start_year.",".$end_year;
         $img_url = DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart=&DateEnd=&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=8&ImageTitle=ufrE2rK%2ft9a%2brc%2f6yczC3c7GuNbI1b75s8m9u8G%2f&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"6977","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"wt3OxrjWyNW++bPJvbvBvw==","unitconver":"1","unitstring":"ttY=","iscustomerdb":"0"}]&needCompress=0&ChartExt={"isfestival": "0","Type":"1","DateStr":"'.$yearStr.'"}&ChartExtLineStyle=';
         $imageid = "tu1_1";
         $images_str = '<div class="" id="' . $imageid . '" style="text-align: center;"><iframe name="iframe" style="margin:0; padding:0; width:500px; height:279px; background-color:#FFF; visibility:inherit;" src=\'' . $img_url . '&callbackname=' . $imageid . '\' frameborder="0" scrolling="no"></iframe></div>';
 
         $this->assign( "images_str", $images_str);

        $this->assign( "adminid", $params['adminid']);
        $this->assign( "type", "4");
        $this->assign( "sdate", $sdate);
        $this->assign( "edate", $edate);
        $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(4,$sdate,$edate,401));
        if($marketNews=="" && $params["saving"] == 1){
            $priceids_401 = "'692023','872023','182023'";
		    $priceids_data401 = $this->hq_price_6($date3,$date3,$priceids_401,$date4,$date4);
            $bx_sx_avg_data = $this->hq_price_6($edate,$sdate,$priceids_401,$edate2,$sdate2);

            $marketNews = "截至".date("n月j日",strtotime($edate))."，莱芜、临沂和青岛市场22mmHRB400E螺纹钢价格分别为".$priceids_data401["tprice"]["692023"]."、".$priceids_data401["tprice"]["872023"]."和".$priceids_data401["tprice"]["182023"]."元/吨，分别较".date("n月j日",strtotime($date4)).$this->zhangdie7($priceids_data401["tprice"]["692023"] - $priceids_data401["lprice"]["692023"])."元/吨、".$this->zhangdie7($priceids_data401["tprice"]["872023"] - $priceids_data401["lprice"]["872023"])."元/吨和".$this->zhangdie7($priceids_data401["tprice"]["182023"] - $priceids_data401["lprice"]["182023"])."元/吨。本旬莱芜、临沂和青岛市场均价分别为".$bx_sx_avg_data["tprice"]["692023"]."元/吨、".$bx_sx_avg_data["tprice"]["872023"]."元/吨和".$bx_sx_avg_data["tprice"]["182023"]."元/吨，分别较上旬均价".$this->zhangdie7($bx_sx_avg_data["tprice"]["692023"] - $bx_sx_avg_data["lprice"]["692023"])."/吨、".$this->zhangdie7($bx_sx_avg_data["tprice"]["872023"] - $bx_sx_avg_data["lprice"]["872023"])."元/吨和".$this->zhangdie7($bx_sx_avg_data["tprice"]["182023"] - $bx_sx_avg_data["lprice"]["182023"])."元/吨。";
        }
        $this->assign( "marketNews401", $marketNews);
        $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(4,$sdate,$edate,402));
        if($marketNews=="" && $params["saving"] == 1){
            // $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(4,$sdate2,$edate2,402));
            //截至1月20日（本旬最后一个工作日，即当日），上海、杭州、常州、南京、合肥、广州、北京和天津市场螺纹钢价格分别为3370（ID：692023）元/吨、3440（ID：692023）元/吨、3370（ID：692023）元/吨、3450（ID：692023）元/吨、3480（ID：692023）元/吨、3520（ID：692023）元/吨、3300（ID：692023）元/吨和3290（ID：692023）元/吨，分别较1月10日（上一旬最后一个工作日）上涨70（当日价格-上旬末价格）元/吨、上涨130（当日价格-上旬末价格）元/吨、上涨80（当日价格-上旬末价格）元/吨、80（当日价格-上旬末价格）元/吨、上涨90（当日价格-上旬末价格）元/吨、上涨50（当日价格-上旬末价格）元/吨、上涨100（当日价格-上旬末价格）元/吨和（当日价格-上旬末价格）70元/吨。*月*旬（1月13日-1月20日）（本旬第一个工作日-本旬最后一个工作日）均价分别为3336（ID：692023）元/吨、3373（ID：692023）元/吨、3321（ID：692023）元/吨、3400（ID：692023）元/吨、3437（ID：692023）元/吨、3499（ID：692023）元/吨、3234（ID：692023）元/吨和3250（ID：692023）元/吨，分别较1月上旬（1月2日-1月10日）（上一旬第一个工作日-上一旬最后一个工作日）下跌13（本旬均价-上旬均价）元/吨、上涨4（本旬均价-上旬均价）元/吨、下跌11（本旬均价-上旬均价）元/吨、下跌16（本旬均价-上旬均价）元/吨、上涨10（本旬均价-上旬均价）元/吨、下跌11（本旬均价-上旬均价）元/吨、下跌（本旬均价-上旬均价）10元/吨和下跌20（本旬均价-上旬均价）元/吨。

            $priceids_402 = "'072023','082023','132023','112023','222023','282023','392023','402023'";
		    $priceids_data402 = $this->hq_price_6($date3,$date3,$priceids_402,$date4,$date4);
            $bx_sx_avg_data_402 = $this->hq_price_6($edate,$sdate,$priceids_402,$edate2,$sdate2);

            $marketNews = "截至".date("n月j日",strtotime($edate))."，上海、杭州、常州、南京、合肥、广州、北京和天津市场螺纹钢价格分别为".$priceids_data402["tprice"]["072023"]."元/吨、".$priceids_data402["tprice"]["082023"]."元/吨、".$priceids_data402["tprice"]["132023"]."元/吨、".$priceids_data402["tprice"]["112023"]."元/吨、".$priceids_data402["tprice"]["222023"]."元/吨、".$priceids_data402["tprice"]["282023"]."元/吨、".$priceids_data402["tprice"]["392023"]."元/吨和".$priceids_data402["tprice"]["402023"]."元/吨，分别较".date("n月j日",strtotime($date4)).$this->zhangdie7($priceids_data402["tprice"]["072023"] - $priceids_data402["lprice"]["072023"])."元/吨、".$this->zhangdie7($priceids_data402["tprice"]["082023"] - $priceids_data402["lprice"]["082023"])."元/吨、".$this->zhangdie7($priceids_data402["tprice"]["132023"] - $priceids_data402["lprice"]["132023"])."元/吨、".$this->zhangdie7($priceids_data402["tprice"]["112023"] - $priceids_data402["lprice"]["112023"])."元/吨、".$this->zhangdie7($priceids_data402["tprice"]["222023"] - $priceids_data402["lprice"]["222023"])."元/吨、".$this->zhangdie7($priceids_data402["tprice"]["282023"] - $priceids_data402["lprice"]["282023"])."元/吨、".$this->zhangdie7($priceids_data402["tprice"]["392023"] - $priceids_data402["lprice"]["392023"])."元/吨和".$this->zhangdie7($priceids_data402["tprice"]["402023"] - $priceids_data402["lprice"]["402023"])."元/吨。本旬均价分别为".$bx_sx_avg_data_402["tprice"]["072023"]."元/吨、".$bx_sx_avg_data_402["tprice"]["082023"]."元/吨、".$bx_sx_avg_data_402["tprice"]["132023"]."元/吨、".$bx_sx_avg_data_402["tprice"]["112023"]."元/吨、".$bx_sx_avg_data_402["tprice"]["222023"]."元/吨、".$bx_sx_avg_data_402["tprice"]["282023"]."元/吨、".$bx_sx_avg_data_402["tprice"]["392023"]."元/吨和".$bx_sx_avg_data_402["tprice"]["402023"]."元/吨，分别较上旬 ".$this->zhangdie7($bx_sx_avg_data_402["tprice"]["072023"] - $bx_sx_avg_data_402["lprice"]["072023"])."元/吨、".$this->zhangdie7($bx_sx_avg_data_402["tprice"]["082023"] - $bx_sx_avg_data_402["lprice"]["082023"])."元/吨、".$this->zhangdie7($bx_sx_avg_data_402["tprice"]["132023"] - $bx_sx_avg_data_402["lprice"]["132023"])."元/吨、".$this->zhangdie7($bx_sx_avg_data_402["tprice"]["112023"] - $bx_sx_avg_data_402["lprice"]["112023"])."元/吨、".$this->zhangdie7($bx_sx_avg_data_402["tprice"]["222023"] - $bx_sx_avg_data_402["lprice"]["222023"])."元/吨、".$this->zhangdie7($bx_sx_avg_data_402["tprice"]["282023"] - $bx_sx_avg_data_402["lprice"]["282023"])."元/吨、".$this->zhangdie7($bx_sx_avg_data_402["tprice"]["392023"] - $bx_sx_avg_data_402["lprice"]["392023"])."和".$this->zhangdie7($bx_sx_avg_data_402["tprice"]["402023"] - $bx_sx_avg_data_402["lprice"]["402023"])."元/吨。";
        }
        $this->assign( "marketNews402", $marketNews);

		$this->assign( "table_tr_str", $table_tr_str );
		$this->assign( "table_tr_str2", $table_tr_str2 );
		$this->assign( "sglw_marketNews", $sglw_marketNews );
		$this->assign( "lwdj_marketNews", $lwdj_marketNews );
        $this->assign( "params", $params);
        $this->assign("mode",$params['mode']);
	}

    public function tiaojia_zhangdie($int){
        if($int>0){
            return "上调".abs($data)."元/吨";
        }elseif($int<0){
            return "下调".abs($data)."元/吨";
        }else{
            return "保持平稳";
        }
    }

	public function zhou_dingjia($params){
        if($params['saving']=="2"){
            $params['saving']="1";
        }
        $sdate = $params["sdate"];
        $edate = $params["edate"];
		if($sdate == "" || $edate == ""){
			echo "查询失败，请重试！";exit;
		}
        if($params["titleName"] == ""){
            $params["titleName"] = date("Y年n月j日",strtotime($edate))."周定价";
        }
        $xz_stime = strtotime("next monday", strtotime($edate));
        $xz_edate = $this->get_xhdate(date("Y-m-d",strtotime("next sunday", $xz_stime)));
        $xz_sdate = date("Y-m-d",$xz_stime);
        // echo $xz_sdate . "--" .$xz_edate;exit;

        $sz_stime = strtotime("last monday", strtotime($edate));
        $sz_edate = $this->get_xhdate(date("Y-m-d",strtotime("last sunday", $sz_stime)));
        if($sz_edate == "2025-01-27"){
            $sz_sdate = "2025-01-20";
        }else{
            $sz_sdate = date("Y-m-d",strtotime("last monday", $sz_stime));
        }
        
        // echo $sz_sdate . "--" .$sz_edate;exit;

        //下周钢材市场预测
        // $gc_day_yc_data = $this->_dao->get_yc_data(2,1,$xz_sdate,$xz_edate);
        // $this->assign("gc_day_yc_data",$gc_day_yc_data);
		// 一、厚板周定价
        // 1、下周（XX月YY日至YY+7日）厚板价格预测
        // $hb_yc_str = "预计下周厚板价格".$gc_day_yc_data["厚板"]["ycfxfd"];

        $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(5,$sdate,$edate,501));
        $this->assign( "type", "5");
        $this->assign( "adminid", $params['adminid']);
        $this->assign( "sdate", $sdate);
        $this->assign( "edate", $edate);

        $dateList["thisDate"] = $edate;
        $dateList["lastDate"] = $sz_edate;
        $dateList["lastMonth"] = date("Y-m-d",strtotime($edate . "-1 month"));
        $dateList["lastYear"] = date("Y-m-d",strtotime($edate . "-1 year"));
        
        //库存汇总
        $steelStock = $this->gcdao->query("select * from gcsckucun_hz where date <= '".$edate."' and type=3 order by date desc limit 2");

        if($marketNews=="" && $params["saving"] == 1){
            $price_ids = "173012,873012,183012";
            $zhb_price_arr = $this->hq_price_6($edate,$edate,$price_ids,$sz_edate,$sz_edate);

            $zhb_avg_price_arr = $this->hq_price_6($edate,$sdate,$price_ids,$sz_edate,$sz_sdate);

            $marketNews .= "　　".date("n月j日",strtotime($edate))."，济南、临沂和青岛20mmQ235中厚板价格分别为".$zhb_price_arr["tprice"]["173012"]."元/吨、".$zhb_price_arr["tprice"]["873012"]."元/吨和".$zhb_price_arr["tprice"]["183012"]."元/吨，较".date("n月j日",strtotime($sz_edate))."分别".$this->zhangdie6($zhb_price_arr["zd2"]["173012"])."、".$this->zhangdie6($zhb_price_arr["zd2"]["873012"])."和".$this->zhangdie6($zhb_price_arr["zd2"]["183012"])."。<br/>";

           $marketNews .= "　　".date("n月j日",strtotime($sdate))."-".date("n月j日",strtotime($edate))."，济南、临沂和青岛20mmQ235中板周均价分别为".$zhb_avg_price_arr["tprice"]["173012"]."元/吨、".$zhb_avg_price_arr["tprice"]["873012"]."元/吨和".$zhb_avg_price_arr["tprice"]["183012"]."元/吨，较".date("n月j日",strtotime($sz_sdate))."-".date("n月j日",strtotime($sz_edate))."分别".$this->zhangdie6($zhb_avg_price_arr["zd2"]["173012"])."、".$this->zhangdie6($zhb_avg_price_arr["zd2"]["873012"])."和".$this->zhangdie6($zhb_avg_price_arr["zd2"]["183012"])."。<br/>";

           
           $marketNews .= "　　"."本周中厚板市场和钢厂总库存".($steelStock[0]["value"])."万吨，较上周".$this->zhangdie3($steelStock[0]["value"] - $steelStock[1]["value"])."；";

           //开工率
           $bz_zhb_kgl_arr = $this->gcdao->query("SELECT dta_vartype,dta_2,dta_3,dta_4,dta_5,dta_6,dta_7,dta_8 FROM `sc_operating_area_hz` WHERE `dta_vartype` = '10' AND `CityId` = '-1' and acttime<='".$edate."' ORDER BY `id` DESC limit 2");
           $bz_zhb_kgl = round($bz_zhb_kgl_arr[0]["dta_3"] / $bz_zhb_kgl_arr[0]["dta_2"] * 100,2);
           $sz_zhb_kgl = round($bz_zhb_kgl_arr[1]["dta_3"] / $bz_zhb_kgl_arr[1]["dta_2"] * 100,2);
           $marketNews .= "产线开工率".$bz_zhb_kgl."%，较上周".$this->zhangdie2($bz_zhb_kgl - $sz_zhb_kgl)."。";

           //产量
           $zhb_zcl_data = $this->gcdao->query("SELECT * FROM `market_consumption` WHERE Type=3 and Area=0 and Date <='".$edate."' ORDER BY `Date` DESC limit 2");
           $marketNews .= "本周国内主要钢厂中厚板周产量为".$zhb_zcl_data[0]["weekly_output"]."万吨，较上周".$this->zhangdie3($zhb_zcl_data[0]["weekly_output"] - $zhb_zcl_data[1]["weekly_output"])."。";
        }
        $this->assign( "marketNews501", $marketNews);

        $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(5,$sdate,$edate,502));
        if($marketNews=="" && $params["saving"] == 1){
            // $marketNews = $gc_cb_str;
            // $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(5,$sz_sdate,$sz_edate,502));
            //港口矿（青岛港PB粉）760（ID：188611）元/吨，下跌5（本周末-上周末）元/吨
            $gkk_price_ids = "188611";
            $gkk_price_arr = $this->hq_price_6($edate,$edate,$gkk_price_ids,$sz_edate,$sz_edate);
            $marketNews = "港口矿（青岛港PB粉）".$gkk_price_arr["tprice"]["188611"]."元/吨，".$this->zhangdie6($gkk_price_arr["tprice"]["188611"] - $gkk_price_arr["lprice"]["188611"])."。";
        }
        $this->assign( "marketNews502", $marketNews);
        // 3、主要竞争对手接单价格
        $dateList = array("this_start_date"=>$sdate,
            "this_end_date"=>$edate,
            "last_start_date"=>$sz_sdate,
            "last_end_date"=>$sz_edate);
        // $ngPriceInfo = $this->_dao->getNgSteelPriceInfo($dateList);
        // $ngPriceInfo_str = "目前南钢接单基价为".$ngPriceInfo["price"]."元/吨。";

        //调价
        $dateList = array("this_start_date"=>$sdate,
        "this_end_date"=>$edate,
        "last_start_date"=>$sz_sdate,
        "last_end_date"=>$sz_edate);
        $priceInfo = $this->gcdao->getSteelPrice($dateList,$GLOBALS['WEEK_ONLY_ID_LIST']);

        $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(5,$sdate,$edate,503));
        if($marketNews=="" && $params["saving"] == 1){
            // $marketNews = $ngPriceInfo_str;
            // $marketNews .= html_entity_decode($this->_dao->get_sdgt_marketNews(5,$sz_sdate,$sz_edate,503));
            $marketNews = date("n月j日",strtotime($sdate))."至".date("n月j日",strtotime($edate))."，国内主要钢厂中厚板调价情况如下：";
            foreach($priceInfo as $price_v){
                if($price_v["variety"] == "中厚板"){
                    $marketNews .= $price_v["gc_name"].$this->tiaojia_zhangdie($price_v["change_rate"]).",".$price_v["specification"]."执行价格为".$price_v["price"]."元/吨；";
                }
            }
        }
        $this->assign( "marketNews503", $marketNews);
        // 4、上期接单情况及目前手持订单情况
        $sc_ddl_info = $this->drcdao->query("select b.* from sd_steel_data_table_base as a,sd_steel_data_table as b where a.id = b.baseid and bigtype = 3 and a.isdel = 0 and a.date >= '".$sdate." 00:00:00' and a.date <= '".$edate." 23:59:59'");
        $hb_scddl_arr = array();
        $hb_hj_scddl_arr = array();
        $lrb_scddl_arr = array();
        $tg_scddl_arr = array();
        $hxg_scddl_arr = array();
        foreach($sc_ddl_info as $dd_info){
            $dd_info["scdd_date"] = date("Y-m-d",strtotime($dd_info["createtime"]));
            if($dd_info["dta1"] == "1.厚板"){
                // $this->assign("hb_date",$dd_info["scdd_date"]);
                // $this->assign("hb_scdd_arr",$dd_info);
            }else if($dd_info["dta1"] == "L4300"){
                $hb_scddl_arr[$dd_info["scdd_date"]]["lw_dta2"] = $dd_info["dta2"];
                $hb_scddl_arr[$dd_info["scdd_date"]]["lw_dta3"] = $dd_info["dta3"];
                $hb_scddl_arr[$dd_info["scdd_date"]]["hb_date"] = $dd_info["scdd_date"];
                $hb_hj_scddl_arr["lw_hbhj_dta2"] += $dd_info["dta2"];
                $hb_hj_scddl_arr["lw_hbhj_dta3"] += $dd_info["dta3"];
                // $this->assign("hb_laiwu_scdd_arr",$dd_info);
            }else if($dd_info["dta1"] == "R3500/R4300"){
                $hb_scddl_arr[$dd_info["scdd_date"]]["rz_dta2"] = $dd_info["dta2"];
                $hb_scddl_arr[$dd_info["scdd_date"]]["rz_dta3"] = $dd_info["dta3"];
                $hb_scddl_arr[$dd_info["scdd_date"]]["hb_date"] = $dd_info["scdd_date"];
                $hb_hj_scddl_arr["rz_hbhj_dta2"] += $dd_info["dta2"];
                $hb_hj_scddl_arr["rz_hbhj_dta3"] += $dd_info["dta3"];
                // $this->assign("hb_rizhao_scdd_arr",$dd_info);
            }else if($dd_info["dta1"] == "热轧2050" || $dd_info["dta1"] == "热轧1500" || $dd_info["dta1"] == "冷轧2030" || $dd_info["dta1"] == "冷轧1500"){
                $lrb_scddl_arr[] = $dd_info;
            }else if($dd_info["dta1"] == "3.特钢"){
                $tg_scddl_arr[] = $dd_info;
            }else if($dd_info["dta1"] == "2.型钢"){
                $hxg_scddl_arr[] = $dd_info;
            }
        }
        // echo "<pre/>";print_r($hb_scddl_arr);
        // 5、建议下周接单价格
        $hb_xzdjjj = html_entity_decode($this->_dao->get_sdgt_marketNews(5,$sdate,$edate,9));
        if($hb_xzdjjj=="" && $params["saving"] == 1){
            // $marketNews = $lrbj_zdj_str;
            $hb_xzdjjj = html_entity_decode($this->_dao->get_sdgt_marketNews(5,$sz_sdate,$sz_edate,9));
        }

        $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(5,$sdate,$edate,504));
        if($marketNews=="" && $params["saving"] == 1){
            $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(5,$sz_sdate,$sz_edate,504));
        }
        $this->assign( "marketNews504", $marketNews);

        // 二、冷热轧周定价</h2>
        // 1、下周（XX月YY日至YY+7日）冷热轧价格预测
        $lrz_yc_str = "预计下周冷轧价格".$gc_day_yc_data["冷卷"]["ycfxfd"]."，热轧价格".$gc_day_yc_data["热卷"]["ycfxfd"];
        $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(5,$sdate,$edate,505));
        if($marketNews=="" && $params["saving"] == 1){
            // $marketNews = $lrz_yc_str;
            // $marketNews .= html_entity_decode($this->_dao->get_sdgt_marketNews(5,$sz_sdate,$sz_edate,505));
            $lzbj_price_ids = "'693112','D23112','823112','873112','694312','D24312','874312'";
            $lzbj_price_arr = $this->hq_price_6($edate,$edate,$lzbj_price_ids,$sz_edate,$sz_edate);
            $lzbj_avg_price_arr = $this->hq_price_6($edate,$sdate,$lzbj_price_ids,$sz_edate,$sz_sdate);

            $marketNews .= "　　".date("n月j日",strtotime($edate)) . "，莱芜、日照、泰安和临沂市场热卷价格分别为".$lzbj_price_arr["tprice"]["693112"]."元/吨、".$lzbj_price_arr["tprice"]["D23112"]."元/吨、".$lzbj_price_arr["tprice"]["823112"]."元/吨和".$lzbj_price_arr["tprice"]["873112"]."元/吨，分别较".date("n月j日",strtotime($sz_edate)).$this->zhangdie6($lzbj_price_arr["zd2"]["693112"])."、".$this->zhangdie6($lzbj_price_arr["zd2"]["D23112"])."、".$this->zhangdie6($lzbj_price_arr["zd2"]["823112"])."和".$this->zhangdie6($lzbj_price_arr["zd2"]["873112"])."。<br/>";

            $marketNews .=  "　　".date("n月j日",strtotime($sdate)) . "-" . date("n月j日",strtotime($edate))."，莱芜、日照、泰安和临沂市场热卷均价分别为".$lzbj_avg_price_arr["tprice"]["693112"]."元/吨、".$lzbj_avg_price_arr["tprice"]["D23112"]."元/吨、".$lzbj_avg_price_arr["tprice"]["823112"]."元/吨和".$lzbj_avg_price_arr["tprice"]["873112"]."元/吨，分别较".date("n月j日",strtotime($sz_sdate)) . "-" . date("n月j日",strtotime($sz_edate))."均价".$this->zhangdie6($lzbj_avg_price_arr["zd2"]["693112"])."、".$this->zhangdie6($lzbj_avg_price_arr["zd2"]["D23112"])."、".$this->zhangdie6($lzbj_avg_price_arr["zd2"]["823112"])."和".$this->zhangdie6($lzbj_avg_price_arr["zd2"]["873112"])."。<br/>";

            $marketNews .=  "　　".date("n月j日",strtotime($edate)) . "，莱芜、日照、临沂市场冷卷价格分别为".$lzbj_price_arr["tprice"]["694312"]."元/吨、".$lzbj_price_arr["tprice"]["D24312"]."元/吨、".$lzbj_price_arr["tprice"]["874312"]."元/吨，分别较".date("n月j日",strtotime($sz_edate)).$this->zhangdie6($lzbj_price_arr["zd2"]["694312"])."、".$this->zhangdie6($lzbj_price_arr["zd2"]["D24312"])."和".$this->zhangdie6($lzbj_price_arr["zd2"]["874312"])."。<br/>";

            $marketNews .=  "　　".date("n月j日",strtotime($sdate)) . "-"  . date("n月j日",strtotime($edate))."，莱芜、日照、临沂市场冷卷均价分别为".$lzbj_avg_price_arr["tprice"]["694312"]."元/吨、".$lzbj_avg_price_arr["tprice"]["D24312"]."元/吨、".$lzbj_avg_price_arr["tprice"]["874312"]."元/吨，分别较".date("n月j日",strtotime($sz_sdate)) . "-" . date("n月j日",strtotime($sz_edate))."均价".$this->zhangdie7($lzbj_avg_price_arr["zd2"]["694312"])."、".$this->zhangdie6($lzbj_avg_price_arr["zd2"]["D24312"])."和".$this->zhangdie6($lzbj_avg_price_arr["zd2"]["874312"])."。<br/>";

            //库存
            $rz_steelStock = $this->gcdao->query("select * from gcsckucun_hz where date <= '".$edate."' and type=1 order by date desc limit 2");
            $lz_steelStock = $this->gcdao->query("select * from gcsckucun_hz where date <= '".$edate."' and type=2 order by date desc limit 2");
            $marketNews .= "　　本周国内热轧板卷市场和钢厂库存为".$rz_steelStock[0]["value"]."万吨，冷轧板卷市场和钢厂库存为".$lz_steelStock[0]["value"]."万吨，分别较上周".$this->zhangdie3($rz_steelStock[0]["value"] - $rz_steelStock[1]["value"])."和".$this->zhangdie3($lz_steelStock[0]["value"] - $lz_steelStock[1]["value"])."。<br/>";
            //产量
           $lzbj_zcl_data = $this->gcdao->query("SELECT * FROM `market_consumption` WHERE Type=5 and Area=0 and Date <='".$edate."' ORDER BY `Date` DESC limit 2");
           $rzbj_zcl_data = $this->gcdao->query("SELECT * FROM `market_consumption` WHERE Type=4 and Area=0 and Date <='".$edate."' ORDER BY `Date` DESC limit 2");
           $marketNews .= "　　"."本周国内主要钢厂热卷和冷轧板卷周产量分别为".$rzbj_zcl_data[0]["weekly_output"]."万吨和".$lzbj_zcl_data[0]["weekly_output"]."万吨，分别较上周".$this->zhangdie3($rzbj_zcl_data[0]["weekly_output"] - $rzbj_zcl_data[1]["weekly_output"])."和".$this->zhangdie3($lzbj_zcl_data[0]["weekly_output"] - $lzbj_zcl_data[1]["weekly_output"])."。";
        }
        $this->assign( "marketNews505", $marketNews);

		// 2、下周钢材成本变动预测
        $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(5,$sdate,$edate,506));
        if($marketNews=="" && $params["saving"] == 1){
            // $marketNews = $gc_cb_str;
            // $marketNews .= html_entity_decode($this->_dao->get_sdgt_marketNews(5,$sz_sdate,$sz_edate,506));
            //1月10日，莱芜市场冷热板卷价差为650（ID：694312-ID：693112）元吨，较1月3日收窄10（本期-上期，缩小为收窄、增加为扩大，持平）元/吨。
            $lzbj_rzbj_jc = $this->hq_price_6($edate,$edate,"'693112','694312'",$sz_edate,$sz_edate);
            $bz_lrjc = $lzbj_rzbj_jc["tprice"]["694312"] - $lzbj_rzbj_jc["tprice"]["693112"];
            $sz_lrjc = $lzbj_rzbj_jc["lprice"]["694312"] - $lzbj_rzbj_jc["lprice"]["693112"];
            $marketNews = "　　".date("n月j日",strtotime($edate))."，莱芜市场冷热板卷价差为".abs($bz_lrjc)."元吨，较".date("n月j日",strtotime($sz_edate))."".$this->dingjia_jiacha($bz_lrjc - $sz_lrjc)."。";
        }
        $this->assign( "marketNews506", $marketNews);
        // 3、主要竞争对手接单价格
        $lrbj_arr = array(
                        array("onlyid"=>'f05f8808ef1d1b3c121ab2b3bfc45c21',"gcid"=>"130"),//临沂
                        array("onlyid"=>'a47a7f0ccdfdf323024075589bc53e11',"gcid"=>"130"),//日照
                        array("onlyid"=>'8b75fd5f0f27106198c3103120236fa8',"gcid"=>"130"),//潍坊
                        array("onlyid"=>'366d98c42c7ceb73af4e19adaf96f341',"gcid"=>"130"),//青岛 到此为冷热轧的
                        array("onlyid"=>'b62a0b01113b55de136f40351d052996',"gcid"=>"2137")//杭州 特钢
                    );
        $priceInfo = $this->gcdao->getSteelPrice($dateList,$lrbj_arr);
        $lrbj_zdj_str = "目前日钢热轧出厂基价临沂".$priceInfo["f05f8808ef1d1b3c121ab2b3bfc45c21-130"]['price']."元/吨、日照".$priceInfo["a47a7f0ccdfdf323024075589bc53e11-130"]['price']."元/吨、潍坊".$priceInfo["8b75fd5f0f27106198c3103120236fa8-130"]['price']."元/吨、青岛".$priceInfo["366d98c42c7ceb73af4e19adaf96f341-130"]['price']."元/吨。";
        $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(5,$sdate,$edate,507));
        if($marketNews=="" && $params["saving"] == 1){
            // $marketNews = $lrbj_zdj_str;
            // $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(5,$sz_sdate,$sz_edate,507));
            $marketNews = date("n月j日",strtotime($sdate))."至".date("n月j日",strtotime($edate))."，国内主要钢厂冷轧板卷、热轧板卷调价情况如下：";
            foreach($priceInfo as $price_v){
                if($price_v["variety"] == "冷轧板卷" || $price_v["variety"] == "热轧板卷"){
                    $marketNews .= $price_v["gc_name"].$this->tiaojia_zhangdie($price_v["change_rate"]).",".$price_v["specification"]."执行价格为".$price_v["price"]."元/吨；";
                }
            }
        }
        $this->assign( "marketNews507", $marketNews);

        // 4、上期接单情况及目前手持订单情况
        $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(5,$sdate,$edate,508));
        $this->assign( "marketNews508", $marketNews);
        // 5、建议下周接单价格
        $lrz_xzdjjj = html_entity_decode($this->_dao->get_sdgt_marketNews(5,$sdate,$edate,10));
        if($lrz_xzdjjj=="" && $params["saving"] == 1){
            // $marketNews = $lrbj_zdj_str;
            $lrz_xzdjjj = html_entity_decode($this->_dao->get_sdgt_marketNews(5,$sz_sdate,$sz_edate,10));
        }

        // 二、特钢周定价</h2>
        // 1、下周（XX月YY日至YY+7日）优特钢价格预测
        // $tg_yc_str = "预计下周特钢价格".$gc_day_yc_data["碳结钢"]["ycfxfd"];
        $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(5,$sdate,$edate,509));
        if($marketNews=="" && $params["saving"] == 1){
            // $marketNews = $tg_yc_str;
            // $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(5,$sz_sdate,$sz_edate,509));
            $tg_price_ids = "'692243','692254','692154','692135'";
            $tg_price_arr = $this->hq_price_6($edate,$edate,$tg_price_ids,$sz_edate,$sz_edate);
            $tg_avg_price_arr = $this->hq_price_6($edate,$sdate,$tg_price_ids,$sz_edate,$sz_sdate);
            $marketNews .= "　　".date("n月j日",strtotime($edate)) . "，莱芜市场碳结钢、合结钢、齿轮钢和铬钼钢均价分别为".$tg_price_arr["tprice"]["692243"]."元/吨、".$tg_price_arr["tprice"]["692254"]."元/吨、".$tg_price_arr["tprice"]["692154"]."元/吨和".$tg_price_arr["tprice"]["692135"]."元/吨，分别较".date("n月j日",strtotime($sz_edate)).$this->zhangdie6($tg_price_arr["zd2"]["692243"])."、".$this->zhangdie6($tg_price_arr["zd2"]["692254"])."、".$this->zhangdie6($tg_price_arr["zd2"]["692154"])."和".$this->zhangdie6($tg_price_arr["zd2"]["692135"])."。<br/>";

            $marketNews .= "　　".date("n月j日",strtotime($sdate)) ."-".date("n月j日",strtotime($edate)) . "，莱芜市场碳结钢、合结钢、齿轮钢和铬钼钢价格分别为".$tg_avg_price_arr["tprice"]["692243"]."元/吨、".$tg_avg_price_arr["tprice"]["692254"]."元/吨、".$tg_avg_price_arr["tprice"]["692154"]."元/吨和".$tg_avg_price_arr["tprice"]["692135"]."元/吨，分别较".date("n月j日",strtotime($sz_sdate))."-".date("n月j日",strtotime($sz_edate)).$this->zhangdie6($tg_avg_price_arr["zd2"]["692243"])."、".$this->zhangdie6($tg_avg_price_arr["zd2"]["692254"])."、".$this->zhangdie6($tg_avg_price_arr["zd2"]["692154"])."和".$this->zhangdie6($tg_avg_price_arr["zd2"]["692135"])."。<br/>";

            //本周国内优特钢棒材市场和钢厂总库存量为184.3万吨，较上周增加4.5万吨；本周优特钢棒材产线开工率86.42%，较上周提高2.47个百分点。
            $ytg_gc_kc_hz_arr = $this->drcdao->query("select Round(value/10000,2) as value from kucun_hz where type = '6' ORDER BY time DESC limit 2");
            $ytg_sc_kc_hz_arr = $this->gcdao->query("select value from KuCunHZ where type=6 and area=0 ORDER BY Date DESC limit 2");
            $bz_ytg_kc_sum = $ytg_gc_kc_hz_arr[0]["value"] + $ytg_sc_kc_hz_arr[0]["value"];
            $sz_ytg_kc_sum = $ytg_gc_kc_hz_arr[1]["value"] + $ytg_sc_kc_hz_arr[1]["value"];
            $ytg_kgl_arr = $this->gcdao->query("select Round(dta_3/dta_2*100,2) as kgl,acttime from sc_operating_area_hz where dta_vartype=35 and CityId='-1' ORDER BY `sc_operating_area_hz`.`acttime` DESC limit 2");
            $marketNews .= "　　"."本周国内优特钢棒材市场和钢厂总库存量为".$bz_ytg_kc_sum."万吨，较上周".$this->zhangdie3($bz_ytg_kc_sum - $sz_ytg_kc_sum)."；";
            $marketNews .= ""."本周优特钢棒材产线开工率".$ytg_kgl_arr[0]["kgl"]."%，较上周".$this->zhangdie2($ytg_kgl_arr[0]["kgl"] - $ytg_kgl_arr[1]["kgl"])."。<br/>";

            //1月10日济南市场碳结钢与螺纹钢价差为510（ID:172243-ID:172023)元/吨，较上周扩大30(本周价差-上周价差，下同)元/吨；莱芜与杭州市场碳结钢价差为120（ID:692243-ID:082243)元/吨，较上周扩大40元/吨；莱芜与杭州市场齿轮钢价差为60（ID:692154-ID:082154)元/吨，较上周扩大70元/吨。

            $ytg_priceid = "'172243','172023','692243','082243','082244','692154','082154'";
            
            $ytg_data_arr = $this->hq_price_6($edate,$edate,$ytg_priceid,$sz_edate,$sz_edate);
            $jn_lw_tjg = $ytg_data_arr["tprice"]["172243"] - $ytg_data_arr["tprice"]["172023"];
            $sz_jn_lw_tjg = $ytg_data_arr["lprice"]["172243"] - $ytg_data_arr["lprice"]["172023"];
            $lylw_hztjg = $ytg_data_arr["tprice"]["692243"] - $ytg_data_arr["tprice"]["082243"];
            $sz_lylw_hztjg = $ytg_data_arr["lprice"]["692243"] - $ytg_data_arr["lprice"]["082243"];
            $lyclg_hzclg = $ytg_data_arr["tprice"]["692154"] - $ytg_data_arr["tprice"]["082154"];
            $sz_lyclg_hzclg = $ytg_data_arr["lprice"]["692154"] - $ytg_data_arr["lprice"]["082154"];


            $marketNews .= "　　".date("n月j日",strtotime($edate))."济南市场碳结钢与螺纹钢价差为".abs($jn_lw_tjg)."元/吨，较上周".$this->dingjia_jiacha($jn_lw_tjg - $sz_jn_lw_tjg)."；";
            $marketNews .= "莱芜与杭州市场碳结钢价差为".abs($lylw_hztjg)."元/吨，较上周".$this->dingjia_jiacha($lylw_hztjg - $sz_lylw_hztjg)."；";
            $marketNews .= "临沂与杭州市场齿轮钢价差为".abs($lyclg_hzclg)."元/吨，较上周".$this->dingjia_jiacha($lyclg_hzclg - $sz_lyclg_hzclg)."；";
        }
        $this->assign( "marketNews509", $marketNews);
		// 2、下周钢材成本变动预测
        $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(5,$sdate,$edate,510));
        if($marketNews=="" && $params["saving"] == 1){
            // $marketNews = $gc_cb_str;
            // $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(5,$sz_sdate,$sz_edate,510));
            //1月10日，莱芜市场45#碳结钢比螺纹钢价格高510（ID:692243-ID:692023)元/吨，较1月3日价差扩大30元/吨。
            $ytg_priceid_tjg_lwg = "'692243','692023'";
            $ytg_priceid_tjg_lwg_arr = $this->hq_price_6($edate,$edate,$ytg_priceid_tjg_lwg,$sz_edate,$sz_edate);

            $tjg_lwg_jc1 = $ytg_priceid_tjg_lwg_arr["tprice"]["692243"] - $ytg_priceid_tjg_lwg_arr["tprice"]["692023"];
            $tjg_lwg_jc2 = $ytg_priceid_tjg_lwg_arr["lprice"]["692243"] - $ytg_priceid_tjg_lwg_arr["lprice"]["692023"];
            $marketNews .= date("n月j日",strtotime($edate))."，莱芜市场45#碳结钢比螺纹钢价格高".abs($tjg_lwg_jc1)."元/吨，较".date("n月j日",strtotime($sz_edate))."价差".$this->dingjia_jiacha($tjg_lwg_jc1 - $tjg_lwg_jc2)."。";
        }
        $this->assign( "marketNews510", $marketNews);
        // 3、主要竞争对手接单价格
        // $tg_zdj_str = "目前淮钢45#钢挂牌价".$priceInfo["f05f8808ef1d1b3c121ab2b3bfc45c21-2137"]['price']."元吨，较上期".$priceInfo["f05f8808ef1d1b3c121ab2b3bfc45c21-2137"]['price']."元吨。";
        $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(5,$sdate,$edate,511));
        if($marketNews=="" && $params["saving"] == 1){
            // $marketNews = $tg_zdj_str;
            // $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(5,$sz_sdate,$sz_edate,511));
            $marketNews = date("n月j日",strtotime($sdate))."至".date("n月j日",strtotime($edate))."，国内主要钢厂特钢调价情况如下：";
            foreach($priceInfo as $price_v){
                if($price_v["variety"] == "碳结钢" || $price_v["variety"] == "合结钢" || $price_v["variety"] == "齿轮钢" || $price_v["variety"] == "铬钼钢"){
                    $marketNews .= $price_v["gc_name"].$this->tiaojia_zhangdie($price_v["change_rate"]).",".$price_v["specification"]."执行价格为".$price_v["price"]."元/吨；";
                }
            }
        }
        $this->assign( "marketNews511", $marketNews);
        // 4、上期接单情况及目前手持订单情况
        $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(5,$sdate,$edate,512));
        $this->assign( "marketNews512", $marketNews);
        // 5、建议下周接单价格
        $tg_xzdjjj = html_entity_decode($this->_dao->get_sdgt_marketNews(5,$sdate,$edate,11));
        if($tg_xzdjjj=="" && $params["saving"] == 1){
            $tg_xzdjjj = html_entity_decode($this->_dao->get_sdgt_marketNews(5,$sz_sdate,$sz_edate,11));
        }

        // 二、H型钢周定价</h2>
        // 1、下周（XX月YY日至YY+7日）型钢价格预测
        // $hxg_yc_str = "预计下周H型钢价格".$gc_day_yc_data["H型钢"]["ycfxfd"];
        $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(5,$sdate,$edate,513));
        if($marketNews==""  && $params["saving"] == 1){
            // $marketNews = $hxg_yc_str;
            // $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(5,$sz_sdate,$sz_edate,513));
            //1月10日，莱芜和泰安市场H型钢（200*200）价格分别为3310（ID:697311）和3520（ID:827311）元/吨，分别较1月3日下跌30元/吨和40元/吨。1月6-1月10日，莱芜和泰安市场H型钢（200*200）均价分别为3324（ID:697311本周内的均值）和3540（ID:827311本周内的均值）元/吨，较12月30-1月3日均价下跌16和下跌20元/吨。本周H型钢产线开工率69.44%，较上周回落5.56个百分点
            $h_xg_price_str = "'697311','827311'";
            $hxg_price_arr = $this->hq_price_6($edate,$edate,$h_xg_price_str,$sz_edate,$sz_edate);
            $hxg_avg_price_arr = $this->hq_price_6($edate,$sdate,$h_xg_price_str,$sz_edate,$sz_sdate);
            $marketNews .= "　　".date("n月j日",strtotime($edate))."，莱芜和泰安市场H型钢（200*200）价格分别为".$hxg_price_arr["tprice"]["697311"]."和".$hxg_price_arr["tprice"]["827311"]."元/吨，分别较".date("n月j日",strtotime($sz_edate)).$this->zhangdie6($hxg_price_arr["tprice"]["697311"] - $hxg_price_arr["lprice"]["697311"])."和".$this->zhangdie6($hxg_price_arr["tprice"]["827311"] - $hxg_price_arr["lprice"]["827311"])."。".date("n月j日",strtotime($sdate))."-".date("n月j日",strtotime($edate))."，莱芜和泰安市场H型钢（200*200）均价分别为".$hxg_avg_price_arr["tprice"]["697311"]."和".$hxg_avg_price_arr["tprice"]["827311"]."元/吨，较".date("n月j日",strtotime($sz_sdate))."-".date("n月j日",strtotime($sz_edate))."均价".$this->zhangdie6($hxg_avg_price_arr["tprice"]["697311"] - $hxg_avg_price_arr["lprice"]["697311"])."和".$this->zhangdie6($hxg_avg_price_arr["tprice"]["827311"] - $hxg_avg_price_arr["lprice"]["827311"])."。<br/>";

            $hxg_kgl_arr = $this->gcdao->query("select Round(dta_3/dta_2*100,2) as kgl,acttime from sc_operating_area_hz where dta_vartype=9 and CityId='-1' ORDER BY `sc_operating_area_hz`.`acttime` DESC limit 2");
            $marketNews .= "　　本周H型钢产线开工率".$hxg_kgl_arr[0]["kgl"]."%，较上周".$this->zhangdie2($hxg_kgl_arr[0]["kgl"] - $hxg_kgl_arr[1]["kgl"])."。";

        }
        $this->assign( "marketNews513", $marketNews);
		// 2、下周钢材成本变动预测
        $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(5,$sdate,$edate,514));
        if($marketNews=="" && $params["saving"] == 1){
            // $marketNews = $gc_cb_str;
            // $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(5,$sz_sdate,$sz_edate,514));
            //H型钢200*200 泰安与马鞍山市场价差=827311-237311 泰安与唐山市场价差=827311-677311 泰安与常州市场=827311-137311
            //1月10日，唐山方坯价格2930（ID:678411）元/吨，较上周末下跌80元/吨，与当地市场200H型钢价格3100（ID:677311）元/吨的价差为170（H型钢价格-方坯价格）元/吨，较1月3日价差收窄50元/吨。

            $hxg_priceid = "'827311','237311','677311','137311','678411'";
            $hxg_data_arr = $this->hq_price_6($edate,$edate,$hxg_priceid,$sz_edate,$sz_edate);
            $fp_hxg_jc1 = $hxg_data_arr["tprice"]["677311"] - $hxg_data_arr["tprice"]["678411"];
            $fp_hxg_jc2 = $hxg_data_arr["lprice"]["677311"] - $hxg_data_arr["lprice"]["678411"];
            $marketNews .= "　　".date("n月j日",strtotime($edate))."，唐山方坯价格".$hxg_data_arr["tprice"]["678411"]."元/吨，较上周末".$this->zhangdie7($hxg_data_arr["tprice"]["678411"] - $hxg_data_arr["lprice"]["678411"])."元/吨，与当地市场200H型钢价格".$hxg_data_arr["tprice"]["677311"]."元/吨的价差为".abs($fp_hxg_jc1)."元/吨，较".date("n月j日",strtotime($sz_edate))."价差".$this->dingjia_jiacha($fp_hxg_jc1 - $fp_hxg_jc2)."。<br/>";
            $marketNews .= date("n月j日",strtotime($edate))."泰安与马鞍山市场价差为".abs($hxg_data_arr["tprice"]["827311"] - $hxg_data_arr["tprice"]["237311"])."元/吨";
            $marketNews .= "，泰安与唐山市场价差为".abs($hxg_data_arr["tprice"]["827311"] - $hxg_data_arr["tprice"]["677311"])."元/吨";
            $marketNews .= "，泰安与常州市场价差为".abs($hxg_data_arr["tprice"]["827311"] - $hxg_data_arr["tprice"]["137311"]) ."元/吨";
        }
        $this->assign( "marketNews514", $marketNews);
        // 3、主要竞争对手接单价格
        $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(5,$sdate,$edate,515));
        if($marketNews=="" && $params["saving"] == 1){
            // $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(5,$sz_sdate,$sz_edate,515));
            $marketNews = date("n月j日",strtotime($sdate))."至".date("n月j日",strtotime($edate))."，国内主要钢厂H型钢调价情况如下：";
            foreach($priceInfo as $price_v){
                if($price_v["variety"] == "H型钢" ){
                    $marketNews .= $price_v["gc_name"].$this->tiaojia_zhangdie($price_v["change_rate"]).",".$price_v["specification"]."执行价格为".$price_v["price"]."元/吨；";
                }
            }
        }
        $this->assign( "marketNews515", $marketNews);
        // 4、上期接单情况及目前手持订单情况
        $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(5,$sdate,$edate,516));
        if($marketNews=="" && $params["saving"] == 1){
            $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(5,$sz_sdate,$sz_edate,516));
        }
        $this->assign( "marketNews516", $marketNews);
        // 5、建议下周接单价格
        $hxg_xzdjjj = html_entity_decode($this->_dao->get_sdgt_marketNews(5,$sdate,$edate,12));
        if($hxg_xzdjjj=="" && $params["saving"] == 1){
            $hxg_xzdjjj = html_entity_decode($this->_dao->get_sdgt_marketNews(5,$sz_sdate,$sz_edate,12));
        }

        $this->assign("hb_scddl_arr",$hb_scddl_arr);
        $this->assign("hb_hj_scddl_arr",$hb_hj_scddl_arr);
        $this->assign("lrb_scddl_arr",$lrb_scddl_arr);
        $this->assign("tg_scddl_arr",$tg_scddl_arr);
        $this->assign("hxg_scddl_arr",$hxg_scddl_arr);

        $this->assign("gc_cb_str",$gc_cb_str);
        $this->assign("hb_xzdjjj",$hb_xzdjjj);
        $this->assign("lrz_xzdjjj",$lrz_xzdjjj);
        $this->assign("tg_xzdjjj",$tg_xzdjjj);
        $this->assign("hxg_xzdjjj",$hxg_xzdjjj);
        $this->assign("xz_sdate",date("n月j日",strtotime($xz_sdate)));
        $this->assign("xz_edate",date("n月j日",strtotime($xz_edate)));

        $this->assign("hb_yc_str",$hb_yc_str);
        $this->assign("lrz_xzdjjj",$lrz_xzdjjj);
        $this->assign("tg_yc_str",$tg_yc_str);
        $this->assign("hxg_yc_str",$hxg_yc_str);
        $this->assign("params",$params);
        $this->assign("mode",$params['mode']);
	}

    function dingjia_jiacha($int){
        $intstr = "";
		if($int<0){
			$intstr = "收窄".abs($int)."元/吨";
		}elseif($int>0){
			$intstr = "扩大".abs($int)."元/吨";
		}elseif($int=="" || $int==0){
			$intstr = "持平";
		}
		return $intstr;
    }

	function get_tks_gx_pingheng($edate){
		$sql = 'SELECT * FROM `tks_gx_pingheng` WHERE `type` = "1" and dat_ym <= "'.$edate.'" ORDER BY dat_ym desc limit 15';
		// echo $sql;
		$dataArray = $this->drcdao->query( $sql );
		$ret_data = array();
		foreach($dataArray as &$v){
            $riqi = date('Y年n月',strtotime($v["dat_ym"]));
            $v["DATE2"] = $riqi;
            // if($v["is_yuce"] == 1){
            //     $v["DATE2"] = "<font color='blue'>". $riqi."</font>";
            //     $v["D1"] = "<font color='blue'>". $v["D1"]."</font>";
            //     $v["D2"] = "<font color='blue'>". $v["D2"]."</font>";
            //     $v["D3"] = "<font color='blue'>". $v["D3"]."</font>";
            //     $v["D4"] = "<font color='blue'>". $v["D4"]."</font>";
            // }
            $ret_data[$riqi] = $v;
        }
		// echo "<pre/>";print_r($dataArray);
		return $ret_data;
	}

    /**
     * 山钢周预测
     * Created by zfy.
     * Date:2024/5/20 11:16
     * @param $params
     * @throws Exception
     */
    public function week_market_yc($params)
    {
        if($params['saving']=="2"){
            $params['saving']="1";
        }
        $date = $params["edate"];
        if($date == ""){
            $date = $this->drcwdao->getOne("select yc_edate from sd_steel_day_weeks_month_predict where yc_type = 2 and status = 1 GROUP BY yc_type,yc_edate order by yc_edate desc limit 1");
            $startDate = strtotime("next monday", strtotime($date));
            $endDate = $this->get_xhdate(date("Y-m-d",strtotime("next sunday", $startDate)));
            // $params["titleName"] = date("Y年n月j日",strtotime($endDate))."周预测";
        }
        // $params["titleName"] = "周分析预测（".date("n月j日",strtotime($date))."-".date("n月j日",strtotime($endDate))."）";
        //周日期
        $weekDateList = $this->getWeekDate($date);
        //工作日
        $workDate = $this->getWorkDate($weekDateList);
        //旬 月同期 年同比日期
        $dateList = $this->getWeekMonthYearDate($workDate);
        //旬 月末 年同比日期
        $dateEndMonthList = $this->getWeekEndMonthYearDate($workDate);
        //一、市场研判
        $this->assign("marketResearch",html_entity_decode($this->_dao->getMarketNews($workDate,2,201)));
        $this->assign( "type", "2");
        $this->assign( "adminid", $params['adminid']);
        $this->assign( "sdate", $workDate['this_start_date']);
        $this->assign( "edate", $workDate['this_end_date']);
        $params["titleName"] = date("Y年",strtotime($workDate['this_end_date']))."周分析预测（".date("n月j日",strtotime($workDate['this_start_date']))."-".date("n月j日",strtotime($workDate['this_end_date']))."）";
        //二.1.2、钢材、原燃料市场变化
        $ret_arr = $this->get_gc_yrl_data($workDate['this_end_date'],$workDate['this_start_date'],$workDate['last_end_date'],$workDate['last_start_date']);
        if($params["save_price"] == 1){
            $this->save_price($ret_arr,2,$workDate['this_end_date']);
        }
        //与上月末 上月均价 年内高点比较
        $this->get_yc_data_compare($ret_arr,$workDate['this_end_date']);

        //卷螺差、冷热差、板卷差、南北价差表格 及四张走势图，杭州对济南市场价差（螺纹）
        $this->getPzPrice_duibiao($workDate);

        $marketNews = html_entity_decode($this->_dao->getMarketNews($workDate,2,202));
        if($workDate['this_end_date'] > "2025-04-07"){
            $marketNews202_new = "主要品种，中厚板周均价".$ret_arr[0]["tprice"]["houban_price"]."元/吨，较上周均价".$this->zhangdie6($ret_arr[0]["tprice"]["houban_price"] - $ret_arr[0]["lprice"]["houban_price"])."；热轧板卷均价".$ret_arr[0]["tprice"]["rzbj_price"]."元/吨，较上周均价".$this->zhangdie6($ret_arr[0]["tprice"]["rzbj_price"] - $ret_arr[0]["lprice"]["rzbj_price"])."；冷轧板卷均价".$ret_arr[0]["tprice"]["lzbj_price"]."元/吨，较上周均价".$this->zhangdie6($ret_arr[0]["tprice"]["lzbj_price"] - $ret_arr[0]["lprice"]["lzbj_price"])."；螺纹钢均价".$ret_arr[0]["tprice"]["lw_price"]."元/吨，较上周均价".$this->zhangdie6($ret_arr[0]["tprice"]["lw_price"] - $ret_arr[0]["lprice"]["lw_price"])."；H型钢均价".$ret_arr[0]["tprice"]["h_xg_price"]."元/吨，较上周均价".$this->zhangdie6($ret_arr[0]["tprice"]["h_xg_price"] - $ret_arr[0]["lprice"]["h_xg_price"])."。";
            $this->assign( "marketNews202_new", $marketNews202_new);
        }else{

        }
        $this->assign( "marketNews202", $marketNews);
        
        $marketNews = html_entity_decode($this->_dao->getMarketNews($workDate,2,203));
        if($workDate['this_end_date'] > "2025-04-07"){
            $marketNews203_new = "其中，青岛港进口矿周均价".$ret_arr[1]["tprice"]["tks_price"]."元/吨，较上周均价".$this->zhangdie6($ret_arr[1]["tprice"]["tks_price"] - $ret_arr[1]["lprice"]["tks_price"])."；废钢均价".$ret_arr[1]["tprice"]["fg_price"]."元/吨，较上周均价".$this->zhangdie6($ret_arr[1]["tprice"]["fg_price"] - $ret_arr[1]["lprice"]["fg_price"])."；焦煤均价".$ret_arr[1]["tprice"]["dljm_price"]."元/吨，较上周均价".$this->zhangdie6($ret_arr[1]["tprice"]["dljm_price"] - $ret_arr[1]["lprice"]["dljm_price"])."；冶金焦均价".$ret_arr[1]["tprice"]["zyjj_price"]."元/吨，较上周均价".$this->zhangdie6($ret_arr[1]["tprice"]["zyjj_price"] - $ret_arr[1]["lprice"]["zyjj_price"])."。";
            $this->assign( "marketNews203_new", $marketNews203_new);
        }
        $this->assign( "marketNews203", $marketNews);
        //二.3、矿煤焦钢期货主力合约及国际大宗商品、外汇市场
        $this->handleFutures($workDate);
        
        //三.1、竞争钢厂调价情况
        $marketNews = html_entity_decode($this->_dao->getMarketNews($workDate,2,205));
        $this->assign( "marketNews205", $marketNews);
        $this->handleSteelPrice($weekDateList);
        //钢厂检修信息
        $this->handleSteelRepair($weekDateList);
        $marketNews = html_entity_decode($this->_dao->getMarketNews($workDate,2,216));
        $this->assign( "marketNews216", $marketNews);
        //螺纹钢、中厚板、热轧板卷日均成交量变化情况
        $marketNews = html_entity_decode($this->_dao->getMarketNews($workDate,2,217));
        $this->assign( "marketNews217", $marketNews);

        //三.2、关注钢厂原燃料采购价格变化情况
        $marketNews = html_entity_decode($this->_dao->getMarketNews($workDate,2,206));
        $this->assign( "marketNews206", $marketNews);
        $this->handleYRLSteelPrice($weekDateList);
        //四.1、市场 钢厂 五大库存
        $marketNews = html_entity_decode($this->_dao->getMarketNews($workDate,2,207));
        $this->assign( "marketNews207", $marketNews);
        $this->handleStockInfo($dateEndMonthList);
        //四.2、优特钢市场和钢厂库存变化
        $marketNews = html_entity_decode($this->_dao->getMarketNews($workDate,2,208));
        $this->assign( "marketNews208", $marketNews);
        $this->handleYTGStockInfo($dateEndMonthList);
        //四.3、进口铁矿石港口库存、到港量、疏港量及钢厂库存量
        $marketNews = html_entity_decode($this->_dao->getMarketNews($workDate,2,209));
        $this->assign( "marketNews209", $marketNews);
        $this->handleImportIronInfo($dateEndMonthList);
        //四.4、钢厂废钢库存变化情况
        $marketNews = html_entity_decode($this->_dao->getMarketNews($workDate,2,210));
        $this->assign( "marketNews210", $marketNews);
        $this->handleScrapSteelInfo($dateEndMonthList);
        //四.5、焦煤焦炭库存变化
        $marketNews = html_entity_decode($this->_dao->getMarketNews($workDate,2,211));
        $this->assign( "marketNews211", $marketNews);
        $this->handleCokeStockInfo($dateEndMonthList);
        //五.本周高炉、电炉、焦炉开工率和主要钢材品种周日均成交量
        $marketNews = html_entity_decode($this->_dao->getMarketNews($workDate,2,212));
        $this->assign( "marketNews212", $marketNews);
        $this->handleOperatingData($dateList);
        //六.1、钢材成本与毛利变化
        $this->handleCostAndProfit($dateList,$workDate);
        //六.2、焦化企业吨焦毛利变化
        $marketNews = html_entity_decode($this->_dao->getMarketNews($workDate,2,214));
        $this->assign( "marketNews214", $marketNews);
        $this->handleCokeProfit($dateEndMonthList);
        //六.3、硅铁、硅锰成本毛利变化
        $marketNews = html_entity_decode($this->_dao->getMarketNews($workDate,2,215));
        $this->assign( "marketNews215", $marketNews);
        $this->handleSiliconTin($dateEndMonthList);
        //七、本周重大事件及影响点评
        $this->assign("importNewsAndComment",html_entity_decode($this->_dao->getMarketNews($workDate,2,1)));

        //八.1、市场预测 2、风险提示
        $this->assign("marketForecastAndRiskWarning",html_entity_decode($this->_dao->getMarketNews($workDate,2,2)));
        //八.3 XX月YY日至XX月YY+7日价格预测
        $this->handleLastWeekForecast($weekDateList);
        //图片处理
        $this->handleTrendChart($weekDateList);

        $this->assign("mode",$params['mode']);
        $this->assign("endDateStr",date("n月j日",strtotime($workDate['this_end_date'])));
        $this->assign("startDateStr",date("n月j日",strtotime($workDate['this_start_date'])));
        $this->assign("lastEndDateStr",date("n月j日",strtotime($workDate['last_end_date'])));
        $this->assign("gc_data",$ret_arr[0]);
        $this->assign("yrl_data",$ret_arr[1]);
        $this->assign("params",$params);
    }

    /**
     * 钢厂检修信息
     * Created by zfy.
     * Date:2024/7/24 9:08
     * @param $weekDateList
     */
    protected function handleSteelRepair($weekDateList){
        $start_date = $weekDateList['this_start_date'];
        $end_date = $weekDateList['this_end_date'];
        $jx_devices_info = $this->get_jxhz_device($start_date,$end_date,1);
        if(!empty($jx_devices_info["devices"]))
            $jx_tableinfo = $this->get_jxhz_table($start_date,$end_date, $jx_devices_info,1);

        if($end_date > "2025-04-07" && $start_date!=$end_date){
            $jx_tableinfo["contents"] = "";
            $this->assign("jx_tableinfo_contents",$jx_tableinfo["contents"]);
        }
        $this->assign("jx_tableinfo",$jx_tableinfo);

        //复产
        // $fc_sdate  = date("Y-m-d", strtotime('last saturday', strtotime($start_date)));
        // $fc_edate = date("Y-m-d", strtotime('friday this week', strtotime($start_date)));
        $fc_sdate = $weekDateList['this_start_date'];
        $fc_edate = $weekDateList['this_end_date'];
        $fc_devices_info = $this->get_jxhz_device($fc_sdate,$fc_edate,2);
        if(!empty($fc_devices_info["devices"])){
            $fc_tableinfo = $this->get_jxhz_table($fc_sdate,$fc_edate, $fc_devices_info,2);
            // if(!empty($jx_devices_info["devices"])){
            //     $jx_tableinfo["s_table"] = str_replace("</table>",$fc_tableinfo["s_table"],$jx_tableinfo["s_table"]);
            // }else{
            //     $jx_tableinfo["s_table"] = '<table border="1" cellpadding="0" cellspacing="0" style="text-align: center;" align="center">' . $fc_tableinfo["s_table"];
            // }
        }

        if($end_date > "2025-04-07" && $start_date!=$end_date){
            $this->assign("fc_tableinfo_contents",$fc_tableinfo["contents"]);
            $fc_tableinfo["contents"] = "";
        }
        $this->assign("fc_tableinfo",$fc_tableinfo);
    }

    /**
     * 钢厂检修信息表格信息
     * Created by zfy.
     * Date:2024/7/24 9:08
     * @param $start_date
     * @param $end_date
     * @param $devices_info
     * @return array
     */
    protected function get_jxhz_table($start_date, $end_date, $devices_info,$jx_fc_type)
    {
        $zxarearows = $devices_info['zxarearows'];
        $devices = $devices_info['devices'];
        //影响品种合计
        $varietySum = $devices_info['varietySum'];
        //影响日均产量合计
        $productSum = $devices_info['productSum'];
        $needShowVariety = array("高炉", "转炉", "电炉");

        //设备名称数组
        $DeviceNameInfo = $this->gcdao->Aquery("SELECT id,d_name FROM sc_device_code ");

        $s_table = "";
        $newgc = array();
        $s_table .= '<table border="1" cellpadding="0" cellspacing="0" style="text-align: center;" align="center">';
            // $s_table .= '<table border="0" cellspacing="0" cellpadding="0">';
        $s_table .= "<thead><tr>
                <td>区域</td>
                <td style='width: 10%;'>钢厂</td>
                <td>检修开始时间</td>
                <td>复产时间</td>
                <td>检修天数</td>
                <td style='width: 15%;'>检修设备</td>
                <td>影响品种</td>
                <td>日均影响<br>产量(万吨)</td></tr></thead>";

        $newyxpz = array();
        foreach ($devices as $area => $item) {
            $j = 0;
            $s_td_0 = "<td rowspan=" . $zxarearows[$area] . ">" . $area . " </td>";
            foreach ($item as $itemk => $itemv) {
                foreach ($itemv as $itemvv) {
                    //显示的单位
                    $unit = $GLOBALS['DEVICE_UNIT_MAP'][$DeviceNameInfo[$itemvv['t1_field1']]];
                    //显示的品种
                    $varietyName = in_array($DeviceNameInfo[$itemvv['t1_field1']], $needShowVariety) ? $DeviceNameInfo[$itemvv['t1_field1']] : "";
                    if ($j == 0) {
                        $s_table = $s_table . "<tr>" . $s_td_0;
                    } else {
                        $s_table = $s_table . "<tr>";
                    }
                    // 检修年份小于当前年的，要显示年月日
                    $stime = $this->getdev_stime($itemvv);
                    $itemvv_name = $itemvv['name'];
                    // 发布时 停产时间 在本周 标红
                    if (strtotime($itemvv['t1_field11']) >= strtotime($start_date . " 00:00:00") && strtotime($itemvv['t1_field11']) <= strtotime($end_date . " 23:59:59")) {
//                        $itemvv_name = "<span style='color:red'>" . $itemvv['name'] . " </span>";
                        $newgc[] = $itemvv['name'];

                        $vitname = $GLOBALS['DEVICE_VARIETY'][$DeviceNameInfo[$itemvv['t1_field1']]];
                        if ($vitname == "螺纹钢") {
                            $vitname = "棒材";
                        }
                        if ($vitname == "粗钢") {
                            if ($itemvv['t1_field1'] == "4") {
                                $vitname = "转炉";
                            } elseif ($itemvv['t1_field1'] == "5") {
                                $vitname = "电炉";
                            }
                        }
                        $newyxpz[$vitname] += 1;
                    }

                    $s_table = $s_table . "<td>" . $itemvv_name . " </td>";
                    $s_table = $s_table . "<td>" . $stime . " </td>";
                    $s_table = $s_table . "<td>" . date("Y年n月j日",strtotime($itemvv['t1_field12'])) . " </td>";
                    $s_table = $s_table . "<td>" . $itemvv['date'] . " </td>";
                    $s_table = $s_table . "<td>" . $itemvv['t1_field2'] . ' ' . $itemvv['t1_field3'] . $unit . $varietyName . " </td>";
                    $s_table = $s_table . "<td>" . $GLOBALS['DEVICE_VARIETY'][$DeviceNameInfo[$itemvv['t1_field1']]] . " </td>";
                    $s_table = $s_table . "<td>" . $itemvv['cl'] . " </td>";
                    // $s_table = $s_table . "<td>" . $itemvv['zcl'] . " </td>";
                    // $s_table = $s_table . "<td>" . $itemvv['qjzcl'] . " </td>";
                    $s_table = $s_table . "</tr>";
                    $j++;
                }
            }
        }
        $colspanNum = count($varietySum);
        $unitDeviceArr = [3,4,5];
        $jx_fc_name = "";
        $jxname = "";
        if($jx_fc_type == 1){
            $jx_fc_name = "分类检修汇总";
            $jxname = "检修";
        }else if($jx_fc_type == 2){
            $jx_fc_name = "分类复产汇总";
            $jxname = "复产";
        }
        if(!empty($varietySum)){
            $summary_table = "<tr><td rowspan='$colspanNum'>".$jx_fc_name."</td>";
            foreach ($varietySum as $t1_field1num => $varietyNum) {
                $unitName = in_array($t1_field1num, $unitDeviceArr) ? "座" : "条";
                $summary_table .= "<td>" . $DeviceNameInfo[$t1_field1num] . "</td>";
                // $summary_table .= "<td></td>";
                // $summary_table .= "<td></td>";
                // $summary_table .= "<td></td>";
                $summary_table .= "<td>".$varietyNum.$unitName."</td>";
                $summary_table .= "<td>" . $GLOBALS['DEVICE_VARIETY'][$DeviceNameInfo[$t1_field1num]] . "</td>";
                $summary_table .= "<td>".$productSum[$t1_field1num]."</td></tr>";
            }
        }
        
        $s_table = $s_table . "</table>";
        $s_table_hz = '<table border="1" cellpadding="0" cellspacing="0" style="text-align: center;" align="center"><thead><tr>
                <td>区域</td>
                <td style="width: 10%;">类型</td>
                <td style="width: 15%;">检修设备</td>
                <td>影响品种</td>
                <td>日均影响<br>产量(万吨)</td></tr></thead>'.$summary_table. "</table>";
//        $s_table .= '<p style="text-indent:21.0pt"><span style="font-size:10.5pt;font-family:楷体">备注：表内标红色钢厂为本期新增检修钢厂。</p>';

        $contents = "本周国内主要钢厂";
        foreach ($varietySum as $t1_field1num => $varietyNum) {
            $unitName = in_array($t1_field1num, $unitDeviceArr) ? "座" : "条";
            $contents .= "新增".$varietyNum.$unitName. $DeviceNameInfo[$t1_field1num] .$jxname."，影响";
            $contents .= $GLOBALS['DEVICE_VARIETY'][$DeviceNameInfo[$t1_field1num]] . "产量".$productSum[$t1_field1num]."万吨/天；";
        }

        

        $jianshu = "";

        if (!empty($newgc)) {
            $jianshu = "新增";
            $jianshu .= count(array_unique($newgc), 0) . "家钢厂";
            $newyxpz2 = array();
            foreach ($newyxpz as $k => $v) {
                if ($k == "铁水") {
                    $pname = "座高炉";
                } elseif ($k == "粗钢") {
                    $pname = "座电炉";
                } elseif ($k == "电炉") {
                    $pname = "座电炉";
                } elseif ($k == "转炉") {
                    $pname = "座转炉";
                } elseif ($k == "螺纹钢") {
                    $pname = "条棒材";
                } else {
                    $pname = "条" . $k;
                }
                $newyxpz2[] = $v . $pname;
            }
            $jianshu .= implode("、", $newyxpz2);
            $jianshu .= $jxname . "。";
        }
        $tableinfo = array();
        $tableinfo['jianshu'] = $jianshu;
        $tableinfo['s_table'] = $s_table;
        $tableinfo['contents'] = $contents;
        $tableinfo['s_table_hz'] = $s_table_hz;
        return $tableinfo;
    }

    /**
     * 钢厂检修信息
     * Created by zfy.
     * Date:2024/7/23 14:24
     * @param $start_date
     * @param $end_date
     * @return array
     */
    protected function get_jxhz_device($start_date,$end_date,$jx_fc_type){
        $varietyid = array('3','5','10','11','14','7','9');
        $viarr = array('3' => '高炉', '4' => '转炉', '5' => '电炉');

        if($jx_fc_type == 1){
            $devices_info = $this->gcdao->getSteelRepairInfo($start_date,$end_date,$varietyid);
        }else{
            $devices_info = $this->gcdao->get_fc_huizong_devices($start_date,$end_date,$varietyid);
        }
//        echo "<pre>";print_r($devices_info);exit();
        $devices_vi = array();
        $devices_vi2 = array(); //不汇总数据
        $zcdevices_vi = array(); //方便计算轧材各品种小计
        $zxdevices_vi = array(); //不分品种（中西部）
        $zxarearows = array(); //不分品种（中西部） 区域小计
        foreach ($varietyid as $k_type => $type) {  //按照品种排序
            foreach ($devices_info as $v) {
                if (in_array($v['t1_field1'], $GLOBALS['OPERATING_DEVICE'][$type]) && $v['t1_field1'] != "24") { //去除冷轧不锈钢
                    if ($jx_fc_type == 2 || (strtotime($v['t1_field11']) >= strtotime($start_date . " 00:00:00") && strtotime($v['t1_field11']) <= strtotime($end_date . " 23:59:59"))) {
                        //检修天数
                        $v['date'] = $this->getdev_dates($v);
                        $cn = $this->getcn($v);
                        // echo $cn."<br>";
                        //日均影响产量
                        $v['cl'] = round(($cn / 365), 2);
                        //影响总产量
                        $v['zcl'] = $v['cl'] * $v['date'];
                        //汇总期内影响产量
                        $v['qjzcl'] = $v['cl'] * $this->get_hzcl($v['t1_field11'], $v['t1_field12'], $v['ResumptionPending'], $start_date, $end_date, $v['t1_field6'], $v['EliminationDate']);

                        $mon_ago6 = date("Y-m-d 00:00:00", strtotime("- 6 month"));
                        if ($jx_fc_type == 1 && ($v['ishuizong'] == "0" || strtotime($v['t1_field11']) < strtotime($mon_ago6)) && $v['ishuizong'] != "2") { //将检修不汇总数据提出来 ishuizong 是否检修汇总：1：汇总 0：取消汇总 2：加入汇总
                            if ($type == "3" || $type == "4" || $type == "5") {
                                $devices_vi2[$viarr[$v['t1_field1']]][$v['area']][$v['name']][] = $v;
                            } else {
                                $devices_vi2['轧材'][$v['area']][$v['name']][] = $v;
                            }
                        } else {
                            $zxdevices_vi[$v['area']][$v['name']][] = $v;
                            $zxarearows[$v['area']] += 1;
                            if ($type == "3" || $type == "4" || $type == "5") {
                                $devices_vi[$viarr[$v['t1_field1']]][$v['area']][$v['name']][] = $v;
                            } else {
                                $devices_vi['轧材'][$v['area']][$v['name']][] = $v;
                                $zcdevices_vi[$type][] = $v;
                            }
                        }
                    }
                }
            }
        }
        $devices3 = array(); //不分品种（中西部）
        foreach ($zxdevices_vi as $area => $areav) {
            foreach ($GLOBALS['OPERATING_DEVICE_AREA'] as $area_order) { // 区域排序
                if ($area_order == $area) {
                    $devices3[$area] = $areav;
                }
            }
        }
        //获取区域小计
        $arearows = array();
        $gcsum = array();  //钢厂合计数组
        $sbsum = array();  //检修设备合计数组
        $clsum = array();  //日均影响产量合计数组
        $zclsum = array();  //影响总产量合计数组
        $qjzclsum = array();  //汇总期内影响产量合计数组
        $varietySum = [];//影响品种合计数组
        $productSum = [];//影响日均产量合计数组
        $devices = array();
        foreach ($devices_vi as $vi => $vi_v) {
            foreach ($GLOBALS['OPERATING_DEVICE_AREA'] as $area_order) { // 区域排序
                foreach ($vi_v as $area => $areav) {
                    if ($area_order == $area) {
                        $devices[$vi][$area] = $areav;
                        foreach ($areav as $name => $namev) {
                            $gcsum[$vi][$area] += 1;
                            foreach ($namev as $v) {
                                $varietySum[$v['t1_field1']] += 1;
                                $productSum[$v['t1_field1']] += $v['cl'];
                                $arearows[$vi][$area] += 1;
                                $clsum[$vi][$area] += $v['cl'];
                                $zclsum[$vi][$area] += $v['zcl'];
                                $qjzclsum[$vi][$area] += $v['qjzcl'];
                            }
                        }
                    }
                }
            }
        }
        $gcsum = $this->get_areasum($gcsum);
        $arearows = $this->get_areasum($arearows);
        $sbsum = $arearows;
        $clsum = $this->get_areasum($clsum);
        $zclsum = $this->get_areasum($zclsum);
        $qjzclsum = $this->get_areasum($qjzclsum);

        //计算轧材各个类别小计
        $zc_gcnamearr = array();
        $zc_gcsum = array();  //钢厂合计数组
        $zc_sbsum = array();  //检修设备合计数组
        $zc_clsum = array();  //日均影响产量合计数组
        $zc_zclsum = array();  //影响总产量合计数组
        $zc_qjzclsum = array();  //汇总期内影响产量合计数组
        foreach ($zcdevices_vi as $k_type => $v_type) {
            foreach ($v_type as $v) {
                $zc_gcnamearr[$k_type][] = $v['name'];
                $zc_sbsum[$k_type] += 1;
                $zc_clsum[$k_type] += $v['cl'];
                $zc_zclsum[$k_type] += $v['zcl'];
                $zc_qjzclsum[$k_type] += $v['qjzcl'];
            }
        }
        foreach ($zc_gcnamearr as $k_type => $v) {
            $zc_gcsum[$k_type] = count(array_unique($v));
        }

        // echo "<pre>";print_r($this->_dao);
        $devices_info = array();
        $devices_info['arearows'] = $arearows;
        $devices_info['gcsum'] = $gcsum;
        $devices_info['sbsum'] = $sbsum;
        $devices_info['clsum'] = $clsum;
        $devices_info['zclsum'] = $zclsum;
        $devices_info['qjzclsum'] = $qjzclsum;
        $devices_info['zc_gcsum'] = $zc_gcsum;
        $devices_info['zc_sbsum'] = $zc_sbsum;
        $devices_info['zc_clsum'] = $zc_clsum;
        $devices_info['zc_zclsum'] = $zc_zclsum;
        $devices_info['zc_qjzclsum'] = $zc_qjzclsum;
        $devices_info['varietySum'] = $varietySum;
        $devices_info['productSum'] = $productSum;
        // $devices_info['devices'] = $devices;
        // $devices_info['devices2'] = $devices2;
        $devices_info['devices'] = $devices3;
        $devices_info['zxarearows'] = $zxarearows;
//        $devices_info['arearows2'] = $arearows2;
//        echo "<pre>";print_r($devices_info);exit();
        return $devices_info;
    }



    /**
     * 处理矿煤焦钢期货主力合约及国际大宗商品、外汇市场
     * Created by zfy.
     * Date:2023/1/9 15:33
     * @param $workDate
     */
    protected function handleFutures($workDate){
        //期货数据
        $futuresInfo = $this->_dao->getFuturesByTypes($workDate,$GLOBALS['QH_TYPES']);
        $thisFuturesInfo = $futuresInfo['endPrice'];
        $lastFuturesInfo = $futuresInfo['startPrice'];
        $list = array();
        foreach ($GLOBALS['QH_TYPES_LIST'] as $type) {
            foreach ($thisFuturesInfo as $key => $thisItem) {
                if ($type == $key){
                    $list[$type]['thisPrice'] = $thisItem;
                }
            }
            foreach ($lastFuturesInfo as $lKey => $lastItem) {
                if ($type == $lKey){
                    $list[$type]['lastPrice'] = $lastItem;
                }
            }
        }
        foreach ($list as $index => &$item) {
            $item['priceZd'] = $this->zhangdie($item['thisPrice']-$item['lastPrice']);
        }

        //纽原油数据
        $NYCrudeOilInfo = $this->_dao->getNYCrudeOil($workDate,$GLOBALS['YUAN_YOU_BM']);
        $NYCrudeOilInfo['priceZd'] = $this->zhangdie($NYCrudeOilInfo['endPrice']-$NYCrudeOilInfo['startPrice']);

        //美元指数
        $USDIndexInfo = $this->_dao->getUSDIndex($workDate);
        $USDIndexInfo['priceZd'] = $this->zhangdie($USDIndexInfo['endPrice']-$USDIndexInfo['startPrice']);

        //人民币汇率
        $RMBRateInfo = $this->_dao->getRMBRate($workDate);
        $RMBRateInfo['priceZd'] = $this->zhangdie($RMBRateInfo['endPrice']-$RMBRateInfo['startPrice']);

        $marketNews = html_entity_decode($this->_dao->getMarketNews($workDate,2,204));
        if($workDate["this_end_date"] > "2025-04-07"){
            $marketNews204_new = "以收盘价计，".date("n月j日",strtotime($workDate["this_end_date"]))."螺纹钢主力合约收盘价为".$list["SHQHDAY_4"]["thisPrice"]."元，较上周五".$this->zhangdie7($list["SHQHDAY_4"]["thisPrice"] - $list["SHQHDAY_4"]["lastPrice"])."元；热轧板卷".$list["SHQHDAY_99"]["thisPrice"]."元，".$this->zhangdie7($list["SHQHDAY_99"]["thisPrice"] - $list["SHQHDAY_99"]["lastPrice"])."元；铁矿".$list["SHQHDAY_20"]["thisPrice"]."元，".$this->zhangdie7($list["SHQHDAY_20"]["thisPrice"] - $list["SHQHDAY_20"]["lastPrice"])."元；焦炭".$list["SHQHDAY_9"]["thisPrice"]."元，".$this->zhangdie7($list["SHQHDAY_9"]["thisPrice"] - $list["SHQHDAY_9"]["lastPrice"])."元；焦煤".$list["SHQHDAY_19"]["thisPrice"]."元，".$this->zhangdie7($list["SHQHDAY_19"]["thisPrice"] - $list["SHQHDAY_19"]["lastPrice"])."元。美元指数为".$USDIndexInfo["endPrice"]."点，较上周五".$this->zhangdie7($USDIndexInfo["endPrice"] - $USDIndexInfo["startPrice"])."点。";
            $this->assign( "marketNews204_new", $marketNews204_new);
        }
        $this->assign( "marketNews204", $marketNews);
        $this->assign("QH_TYPES_LIST",$GLOBALS['QH_TYPES_LIST']);
        $this->assign("list",$list);
        $this->assign("NYCrudeOilInfo",$NYCrudeOilInfo);
        $this->assign("USDIndexInfo",$USDIndexInfo);
        $this->assign("RMBRateInfo",$RMBRateInfo);
    }
    /**
     * 处理钢厂调价
     * Created by zfy.
     * Date:2023/1/10 9:24
     * @param $dateList
     */
    protected function handleSteelPrice($dateList){
        $priceInfo = $this->gcdao->getSteelPrice($dateList,$GLOBALS['WEEK_ONLY_ID_LIST']);
        $ngPriceInfo = $this->_dao->getNgSteelPriceInfo($dateList);
        $this->assign("priceInfo",$priceInfo);
        $this->assign("ngPriceInfo",$ngPriceInfo);
    }

    /**
     * 原燃料市场变化
     * Created by zfy.
     * Date:2024/6/4 9:52
     * @param $dateList
     */
    protected function handleYRLSteelPrice($dateList){
        $psPriceInfo = $this->maindao->getHangQingInfo($dateList,"'H98640'");
        $priceInfo = $this->gcdao->getYRLSteelPrice($dateList,$GLOBALS['WEEK_YRL_ONLY_ID_LIST']);
        $this->assign("yrlPriceInfo",$priceInfo);
        $this->assign("psPriceInfo",$psPriceInfo);
    }
    /**
     * 处理 市场 钢厂 五大库存
     * Created by zfy.
     * Date:2023/1/10 11:38
     * @param $dateList
     */
    protected function handleStockInfo($dateList){
        $marketStockSort = array(5,4,3,1,2,6);
        $steelStockSort = array(1,2,3,4,5,6);
        $steelMarketStockSort = array(5,4,3,1,2,6);
        //市场库存
        $marketStock = $this->_dao->getMarketStock($dateList,$GLOBALS['STOCK_TYPE_STR']);
        $marketStockList = $this->calcStockData($marketStock);
        //钢厂库存
        $steelStock = $this->gcdao->getSteelStock($dateList,$GLOBALS['STOCK_TYPE_STR']);
        $steelStockList = $this->calcStockData($steelStock);
        //市场钢厂合计库存
        $steelMarketStock = $this->gcdao->getSteelMarketStock($dateList,$GLOBALS['STOCK_TYPE_STR']);
        $steelMarketStockList = $this->calcStockData($steelMarketStock);

        $NewStockDate = $this->gcdao->getNewStockDate($dateList);
        $NewStockDate = date("n月j日",strtotime($NewStockDate));

        $jc_thisDate = bcadd($steelMarketStock['thisDate']['5'],$steelMarketStock['thisDate']['4'],2);
        $jc_lastDate = bcadd($steelMarketStock['lastDate']['5'],$steelMarketStock['lastDate']['4'],2);
        $bc_thisDate = bcadd($steelMarketStock['thisDate']['3'],$steelMarketStock['thisDate']['1'],2);
        $bc_thisDate = bcadd($bc_thisDate,$steelMarketStock['thisDate']['2'],2);
        $bc_lastDate = bcadd($steelMarketStock['lastDate']['3'],$steelMarketStock['lastDate']['1'],2);
        $bc_lastDate = bcadd($bc_lastDate,$steelMarketStock['lastDate']['2'],2);

        $contents = "截至".$NewStockDate."，国内市场五大钢材品种市场库存".$marketStock['thisDate']['6']."万吨，";
        $contents .= "较上周末".$this->zhangdie3(bcsub($marketStock['thisDate']['6'],$marketStock['lastDate']['6'],2))."，";
        $contents .= "较去年同期".$this->zhangdie3(bcsub($marketStock['thisDate']['6'],$marketStock['lastYear']['6'],2))."。";
        $contents .= "其中，螺纹钢库存量".$marketStock['thisDate']['5']."万吨，";
        $contents .= "较上周末".$this->zhangdie3(bcsub($marketStock['thisDate']['5'],$marketStock['lastDate']['5'],2))."；";
        $contents .= "中厚板库存".$marketStock['thisDate']['3']."万吨，";
        $contents .= "较上周末".$this->zhangdie3(bcsub($marketStock['thisDate']['3'],$marketStock['lastDate']['3'],2))."；";
        $contents .= "热轧板卷库存".$marketStock['thisDate']['1']."万吨，";
        $contents .= "较上周末".$this->zhangdie3(bcsub($marketStock['thisDate']['1'],$marketStock['lastDate']['1'],2))."。<br>";
        $contents .= "　　截至".$NewStockDate."，钢之家网站重点统计钢铁企业钢材库存量为".$steelStock['thisDate']['6']."万吨，";
        $contents .= "较上周末".$this->zhangdie3(bcsub($steelStock['thisDate']['6'],$steelStock['lastDate']['6'],2))."，";
        $contents .= "较去年同期".$this->zhangdie3(bcsub($steelStock['thisDate']['6'],$steelStock['lastYear']['6'],2))."。";
        $contents .= "其中，螺纹钢库存量".$steelStock['thisDate']['1']."万吨，";
        $contents .= "较上周末".$this->zhangdie3(bcsub($steelStock['thisDate']['1'],$steelStock['lastDate']['1'],2))."；";
        $contents .= "中厚板库存".$steelStock['thisDate']['3']."万吨，";
        $contents .= "较上周末".$this->zhangdie3(bcsub($steelStock['thisDate']['3'],$steelStock['lastDate']['3'],2))."；";
        $contents .= "热轧板卷库存".$steelStock['thisDate']['4']."万吨，";
        $contents .= "较上周末".$this->zhangdie3(bcsub($steelStock['thisDate']['4'],$steelStock['lastDate']['4'],2))."。<br>";
        $contents .= "　　分品种来看，本周建筑钢材（螺纹钢+线盘）库存总计较上周";
        $contents .= $this->zhangdie3(bcsub($jc_thisDate,$jc_lastDate,2))."，";
        $contents .= "板材（中厚板+热轧板卷+冷轧板卷）库存较上周";
        $contents .= $this->zhangdie3(bcsub($bc_thisDate,$bc_lastDate,2))."。";

        $this->assign("date",$NewStockDate);
        if($dateList['thisDate'] > "2025-04-07"){
            $this->assign("stockContents_new",$contents);
        }else{
            $this->assign("stockContents",$contents);
        }
        $this->assign("marketStockSort",$marketStockSort);
        $this->assign("steelStockSort",$steelStockSort);
        $this->assign("steelMarketStockSort",$steelMarketStockSort);
        $this->assign("marketStockList",$marketStockList);
        $this->assign("steelStockList",$steelStockList);
        $this->assign("steelMarketStockList",$steelMarketStockList);
    }

    /**
     * 优特钢库存
     * Created by zfy.
     * Date:2024/5/21 9:47
     * @param $dateList
     */
    protected function handleYTGStockInfo($dateList){
        //市场库存
        $marketStock = $this->_dao->getMarketStock($dateList,6);
        $marketStockList = $this->calcYTGStockData($marketStock);
        //钢厂库存
        $steelStock = $this->gcdao->getSteelStock($dateList,6);
        $steelStockList = $this->calcStockData($steelStock);

        //生成文字内容
        $marketStock['thisDate'][6] = round($marketStock['thisDate'][6] / 10000,2);
        $marketStock['lastDate'][6] = round($marketStock['lastDate'][6] / 10000,2);
        $marketStock['lastMonth'][6] = round($marketStock['lastMonth'][6] / 10000,2);
        $marketStock['lastYear'][6] = round($marketStock['lastYear'][6] / 10000,2);

        $stock_sum_thisDate = bcadd($marketStock['thisDate']['6'],$steelStock['thisDate']['6'],2);
        $stock_sum_lastDate = bcadd($marketStock['lastDate']['6'],$steelStock['lastDate']['6'],2);
        $stock_sum_lastMonth = bcadd($marketStock['lastMonth']['6'],$steelStock['lastMonth']['6'],2);
        $stock_sum_lastYear = bcadd($marketStock['lastYear']['6'],$steelStock['lastYear']['6'],2);
        $contents = "本周国内优特钢棒材总库存".$stock_sum_thisDate."万吨，";
        $contents .= "较上周末".$this->zhangdie3(bcsub($stock_sum_thisDate,$stock_sum_lastDate,2))."，";
        $contents .= "较上月末".$this->zhangdie3(bcsub($stock_sum_thisDate,$stock_sum_lastMonth,2))."，";
        $contents .= "较去年同期".$this->zhangdie3(bcsub($stock_sum_thisDate,$stock_sum_lastYear,2))."。";
        $contents .= "其中，市场库存".$marketStock['thisDate']['6']."万吨，较上周末".$this->zhangdie3($marketStock['thisDate']['6'] - $marketStock['lastDate']['6'])."，钢厂库存".$steelStock['thisDate']['6']."万吨，较上周末".$this->zhangdie3($steelStock['thisDate']['6'] - $steelStock['lastDate']['6'])."。";

        $ytg_hj_tr = "<tr><td>库存合计</td><td>".$stock_sum_thisDate."</td><td>".$this->zhangdie(bcsub($stock_sum_thisDate,$stock_sum_lastDate,2))."</td><td>".$this->zhangdie(bcsub($stock_sum_thisDate,$stock_sum_lastMonth,2))."</td><td>".$this->zhangdie(bcsub($stock_sum_thisDate,$stock_sum_lastYear,2))."</td></tr>";
        $this->assign("ytg_hj_tr",$ytg_hj_tr);

        if($dateList['thisDate'] > "2025-04-07"){
            $this->assign("ytgStockContents_new",$contents);
        }else{
            $this->assign("ytgStockContents",$contents);
        }
        $this->assign("ytgMarketStockList",$marketStockList);
        $this->assign("ytgSteelStockList",$steelStockList);
    }

    /**
     * 进口铁矿石港口库存、到港量、疏港量及钢厂库存量
     * Created by zfy.
     * Date:2024/5/21 11:24
     * @param $dateList
     */
    protected function handleImportIronInfo($dateList){
        //港口库存
        $portStock = $this->_dao->getDataTableInfo($dateList,"dta_3","data_table"," dta_type = 'JKKCTJ_1' and dta_1='合计' ");
        $portStockInfo = $this->calcArrayData($portStock);
        //到港量
        $Arrival = $this->_dao->getDataTableInfo($dateList,"dta_7","data_table"," dta_type = 'TKS_FH_DG_SG' ");
        $ArrivalInfo = $this->calcArrayData($Arrival);
        //日均疏港量
        $DailyArrival = $this->_dao->getDataTableInfo($dateList,"dta_3","data_table"," dta_type= 'JKKCTJ_1' and `dta_1`='全国港口日均疏港量' ");
        $DailyArrivalInfo = $this->calcArrayData($DailyArrival);
        //钢厂库存 可用天数
        $steelStock = $this->gcdao->getSteelStockAndDay($dateList);
        $steelStockDaysInfo = $this->calcStockData($steelStock);

        //文字内容
        $contents = "据钢之家网站统计，国内主要港口铁矿石库存量".$portStock['thisDate']."万吨，";
        $contents .= "较上周".$this->zhangdie3(bcsub($portStock['thisDate'],$portStock['lastDate']))."，";
        $contents .= "较上月末".$this->zhangdie3(bcsub($portStock['thisDate'],$portStock['lastMonth']))."，";
        $contents .= "较去年同期".$this->zhangdie3(bcsub($portStock['thisDate'],$portStock['lastYear']))."。";
        $contents .= "本周钢厂进口矿库存".$steelStock['thisDate']['ImportKuCunNum']."万吨，";
        $contents .= "较上周末".$this->zhangdie3($steelStock['thisDate']['ImportKuCunNum'] - $steelStock['lastDate']['ImportKuCunNum'])."；到港总量".$Arrival['thisDate']."万吨，较上周".$this->zhangdie3($Arrival['thisDate'] - $Arrival['lastDate'])."，日均疏港量".$DailyArrival['thisDate']."万吨，较上周".$this->zhangdie3($DailyArrival['thisDate'] - $DailyArrival['lastDate'])."。";

        if($dateList["thisDate"] > "2025-04-07"){
            $this->assign("portStockContents_new",$contents);
        }else{
            $this->assign("portStockContents",$contents);
        }
        $this->assign("portStockInfo",$portStockInfo);
        $this->assign("ArrivalInfo",$ArrivalInfo);
        $this->assign("DailyArrivalInfo",$DailyArrivalInfo);
        $this->assign("steelStockDaysInfo",$steelStockDaysInfo);
    }

    /**
     * 78家钢企废钢库存变化情况
     * Created by zfy.
     * Date:2024/5/22 8:37
     * @param $dateList
     */
    protected function handleScrapSteelInfo($dateList)
    {
        //钢企废钢库存总量 可用天数
        $scrapSteelStock = $this->_dao->getDataTableInfoRow($dateList,"dta_4,dta_5","data_table"," dta_type='fg_kchz' and dta_2='合计' ");
        $scrapSteelStockInfo = $this->calcStockData($scrapSteelStock);

        //钢企废钢到港量
        $scrapSteelArrivalQuantity = $this->_dao->getDataTableInfo($dateList,"dta_4","data_table"," dta_type='fg_rjdhlhz' ");
        $scrapSteelArrivalQuantityInfo = $this->calcArrayData($scrapSteelArrivalQuantity,3);

        //钢企废钢消耗量
        $scrapSteelConsumption = $this->_dao->getDataTableInfo($dateList,"dta_4","data_table"," dta_type='fg_rjxhlhz' ");
        $scrapSteelConsumptionInfo = $this->calcArrayData($scrapSteelConsumption,3);

        //文字内容
        $contents = "本周国内78家钢厂废钢库存量".$scrapSteelStock['thisDate']['dta_4']."万吨，";
        $contents .= "较上周".$this->zhangdie3(bcsub($scrapSteelStock['thisDate']['dta_4'],$scrapSteelStock['lastDate']['dta_4'],2))."，";
        $contents .= "较上月末".$this->zhangdie3(bcsub($scrapSteelStock['thisDate']['dta_4'],$scrapSteelStock['lastMonth']['dta_4'],2))."，";
        $contents .= "较去年同期".$this->zhangdie3(bcsub($scrapSteelStock['thisDate']['dta_4'],$scrapSteelStock['lastYear']['dta_4'],2))."。";
        $contents .= "可用天数".$scrapSteelStock["thisDate"]["dta_5"]."天，较上周".$this->zhangdie9($scrapSteelStock["thisDate"]["dta_5"] - $scrapSteelStock["lastDate"]["dta_5"])."天。";

        if($dateList["thisDate"] > "2025-04-07"){
            $this->assign("scrapSteelStockContents_new",$contents);
        }else{
            $this->assign("scrapSteelStockContents",$contents);
        }
        $this->assign("scrapSteelStockInfo",$scrapSteelStockInfo);
        $this->assign("scrapSteelArrivalQuantityInfo",$scrapSteelArrivalQuantityInfo);
        $this->assign("scrapSteelConsumptionInfo",$scrapSteelConsumptionInfo);
    }

    protected function handleCokeStockInfo($dateList){
        //焦炭库存钢厂库存量
        $cokeStock = $this->gcdao->getCokeStock($dateList);
        $cokeStockInfo = $this->calcArrayData($cokeStock);
        //焦炭、焦煤 100家独立焦企库存量 库存可用天数
        $cokeStock100 = $this->gcdao->getCokeStock100($dateList);
        $cokeStock100Info = $this->calcStockData($cokeStock100);
        //四大港口
        $fourPortStock = $this->_dao->getDataTableBaseInfo($dateList,'dta_2','DataTableBaseInfo as db left join data_table as dt on db.id=dt.baseid'," db.DataType='Java-SGGKJTKC' and  dta_1='合计' ");
        $fourPortStockInfo = $this->calcArrayData($fourPortStock);
        //五大港口进口炼焦煤
        $fivePortCokeCoal = $this->_dao->getDataTableBaseInfo($dateList,'dta_3','DataTableBaseInfo as db left join data_table as dt on db.id=dt.baseid'," db.DataType='Java-zyyhgkjkljmkc' and dta_2='总库存' ");
        $fivePortCokeCoalInfo = $this->calcArrayData($fivePortCokeCoal);
        //六大港口煤炭库存
        $sixPortCoalStock = $this->_dao->getDataTableBaseInfo($dateList,'dta_4','DataTableBaseInfo as db left join data_table as dt on db.id=dt.baseid'," db.DataType='Java-zygkmtkctj' and dta_2='合计' ");
        $sixPortCoalStockInfo = $this->calcArrayData($sixPortCoalStock);

        //文字内容
        $contents = "本周钢厂焦炭库存".$cokeStock['thisDate']."万吨，";
        $contents .= "较上周".$this->zhangdie3(bcsub($cokeStock['thisDate'],$cokeStock['lastDate'],2))."；";
        $contents .= "独立焦企焦炭库存".$cokeStock100['thisDate']['jt_kc']."万吨，";
        $contents .= "较上周".$this->zhangdie3(bcsub($cokeStock100['thisDate']['jt_kc'],$cokeStock100['lastDate']['jt_kc'],2))."；";
        $contents .= "四大港口焦炭库存".$fourPortStock['thisDate']."万吨，";
        $contents .= "较上周".$this->zhangdie3(bcsub($fourPortStock['thisDate'],$fourPortStock['lastDate'],2))."；";
        $contents .= "五大港口进口炼焦煤库存".$fivePortCokeCoal['thisDate']."万吨，";
        $contents .= "较上周".$this->zhangdie3(bcsub($fivePortCokeCoal['thisDate'],$fivePortCokeCoal['lastDate'],2))."；";
        // $contents .= "焦化厂因为亏损，降低焦煤库存，";
        $contents .= "本周100家独立焦企炼焦煤库存".$cokeStock100['thisDate']['ljm_kcw']."万吨，";
        $contents .= "较上周".$this->zhangdie3(bcsub($cokeStock100['thisDate']['ljm_kcw'],$cokeStock100['lastDate']['ljm_kcw'],2))."。";

        if($dateList["thisDate"] < "2025-04-07"){
            $this->assign("cokeStockContents",$contents);
        }else{
            $this->assign("cokeStockContents_new",$contents);
        }
        $this->assign("cokeStockInfo",$cokeStockInfo);
        $this->assign("cokeStock100Info",$cokeStock100Info);
        $this->assign("fourPortStockInfo",$fourPortStockInfo);
        $this->assign("fivePortCokeCoalInfo",$fivePortCokeCoalInfo);
        $this->assign("sixPortCoalStockInfo",$sixPortCoalStockInfo);
    }

    /**
     * 处理高炉、电炉、焦炉开工率和主要钢材品种周日均成交量
     * Created by zfy.
     * Date:2024/5/28 10:19
     * @param $dateList
     */
    protected function handleOperatingData($dateList){
        //开工率
        $operatingSort = array("GC_KGL","DL_KGL");
        $operatingInfo = $this->_dao->getOperatingInfo($dateList,$operatingSort);
        $operatingList = $this->calcStockMoreData($operatingInfo);
        $cokeOperating = $this->gcdao->getCokeOperatingInfo($dateList);
        $cokeOperatingList = $this->calcArrayData($cokeOperating);
        //成交量
        $turnoverSort = array("1","4","2");
        $turnoverInfo = $this->gcdao->getTurnoverInfo($dateList,$turnoverSort);
        $turnoverList = $this->calcStockData($turnoverInfo);
        //环比
        $lwg_hb = round(($turnoverInfo["thisDate"][1] - $turnoverInfo["lastDate"][1]) / $turnoverInfo["lastDate"][1] * 100,1);
        $zhb_hb = round(($turnoverInfo["thisDate"][4] - $turnoverInfo["lastDate"][4]) / $turnoverInfo["lastDate"][4] * 100,1);
        $rzbj_hb = round(($turnoverInfo["thisDate"][2] - $turnoverInfo["lastDate"][2]) / $turnoverInfo["lastDate"][2] * 100,1);
        $hb_tr_str = "<tr><td>增减幅%</td><td>".$this->zhangdie($lwg_hb)."</td><td>".$this->zhangdie($zhb_hb)."</td><td>".$this->zhangdie($rzbj_hb)."</td></tr>";

        $lwg_sy_zjf = $this->zhangdie(round(($turnoverInfo["thisDate"][1] - $turnoverInfo["lastMonth"][1]) / $turnoverInfo["lastMonth"][1] * 100,1));
        $zhb_sy_zjf = $this->zhangdie(round(($turnoverInfo["thisDate"][4] - $turnoverInfo["lastMonth"][4]) / $turnoverInfo["lastMonth"][4] * 100,1));
        $rzbj_sy_zjf = $this->zhangdie(round(($turnoverInfo["thisDate"][2] - $turnoverInfo["lastMonth"][2]) / $turnoverInfo["lastMonth"][2] * 100,1));
        $sy_zjf_tr_str = "<tr><td>增减幅%</td><td>".$lwg_sy_zjf."</td><td>".$zhb_sy_zjf."</td><td>".$rzbj_sy_zjf."</td></tr>";

        $lwg_qntq_zjf = round(($turnoverInfo["thisDate"][1] - $turnoverInfo["lastYear"][1]) / $turnoverInfo["lastYear"][1] * 100,1);
        $zhb_qntq_zjf = round(($turnoverInfo["thisDate"][4] - $turnoverInfo["lastYear"][4]) / $turnoverInfo["lastYear"][4] * 100,1);
        $rzbj_qntq_zjf = round(($turnoverInfo["thisDate"][2] - $turnoverInfo["lastYear"][2]) / $turnoverInfo["lastYear"][2] * 100,1);
        $qntq_zjf_tr_str = "<tr><td>增减幅%</td><td>".$this->zhangdie($lwg_qntq_zjf)."</td><td>".$this->zhangdie($zhb_qntq_zjf)."</td><td>".$this->zhangdie($rzbj_qntq_zjf)."</td></tr>";

        //截至到2月27日，螺纹钢周日均成交量4.75万吨，环比增长11.3%，同比增长419.4%；中厚板周日均成交量1.97万吨，环比增长6.4%，同比增长82%；热轧板卷周日均成交量1.48万吨，环比增长3.3%，同比增长66.8%。
        $cjl_contents = "截至到".date("n月j日",strtotime($dateList['thisDate']))."，螺纹钢周日均成交量".$turnoverInfo["thisDate"][1]."万吨，环比".$this->cjl_zhangdie($lwg_hb)."，同比".$this->cjl_zhangdie($lwg_qntq_zjf)."；中厚板周日均成交量".$turnoverInfo["thisDate"][4]."万吨，环比".$this->cjl_zhangdie($zhb_hb)."，同比".$this->cjl_zhangdie($zhb_qntq_zjf)."；热轧板卷周日均成交量".$turnoverInfo["thisDate"][2]."万吨，环比".$this->cjl_zhangdie($rzbj_hb)."，同比".$this->cjl_zhangdie($rzbj_qntq_zjf)."。";

        $operating_stcom_count = $this->gcdao->getOperating_stcom_count($dateList);
        $operating_area_hz = $this->gcdao->getOperating_area_hz($dateList);
        //print_r($operating_area_hz);

        //生成文字marketNews212
        $contents = "据钢之家网站对国内".$operating_stcom_count['3']."家钢厂调查显示，";
        $contents .= "本周调查高炉总产能".$operating_area_hz['thisDate']['3']['dta_6']."万吨，开工产能".$operating_area_hz['thisDate']['3']['dta_7']."万吨，";
        $contents .= "按产能计开工率为".$operatingInfo['thisDate']['GC_KGL']['dta_2']."%，";
        $contents .= "较上期调查".$this->zhangdie2(bcsub($operatingInfo['thisDate']['GC_KGL']['dta_2'],$operatingInfo['lastDate']['GC_KGL']['dta_2'],2))."；";
        $contents .= "调查高炉总容积".$operating_area_hz['thisDate']['3']['dta_4']."立方米，其中开工容积".$operating_area_hz['thisDate']['3']['dta_5']."立方米，";
        $contents .= "按容积计算开工率为".$operatingInfo['thisDate']['GC_KGL']['dta_3']."%，";
        $contents .= "较上期调查".$this->zhangdie2(bcsub($operatingInfo['thisDate']['GC_KGL']['dta_3'],$operatingInfo['lastDate']['GC_KGL']['dta_3'],2))."。<br></p><p>";

        $contents .= "　　据钢之家网站对国内".$operating_stcom_count['5']."家独立电炉企业调查显示，";
        $contents .= "本周调查电炉数量为".$operating_area_hz['thisDate']['5']['dta_2']."台，其中开工电炉".$operating_area_hz['thisDate']['5']['dta_3']."台，";
        $contents .= "按数量计开工率为".$operatingInfo['thisDate']['DL_KGL']['dta_3']."%，";
        $contents .= "较上期调查".$this->zhangdie2(bcsub($operatingInfo['thisDate']['DL_KGL']['dta_3'],$operatingInfo['lastDate']['DL_KGL']['dta_3'],2))."；";
        $contents .= "调查独立电炉企业总产能为".$operating_area_hz['thisDate']['5']['dta_6']."万吨，开工产能为".$operating_area_hz['thisDate']['5']['dta_7']."万吨，";
        $contents .= "产能利用率为".$operatingInfo['thisDate']['DL_KGL']['dta_2']."%，";
        $contents .= "较上期调查".$this->zhangdie2(bcsub($operatingInfo['thisDate']['DL_KGL']['dta_2'],$operatingInfo['lastDate']['DL_KGL']['dta_2'],2))."。";

        //marketNews211
        $contents2 = "本周焦炭产能利用率".$cokeOperating['thisDate']."%，";
        $contents2 .= "较上周".$this->zhangdie2(bcsub($cokeOperating['thisDate'],$cokeOperating['lastDate'],2))."，";
        $contents2 .= "较上月末".$this->zhangdie2(bcsub($cokeOperating['thisDate'],$cokeOperating['lastMonth'],2))."，";
        $contents2 .= "较去年同期".$this->zhangdie2(bcsub($cokeOperating['thisDate'],$cokeOperating['lastYear'],2))."。";

        if($dateList["thisDate"] < "2025-04-07"){
            $this->assign("operatingContents",$contents);
            $this->assign("operatingContents2",$contents2);
            $this->assign("cjl_contents",$cjl_contents);
        }else{
            $this->assign("operatingContents_new",$contents);
            $this->assign("operatingContents2_new",$contents2);
            $this->assign("cjl_contents_new",$cjl_contents);
        }
        
        $this->assign("operatingDate",date("n月j日",strtotime($this->_dao->getNewOperatingDate($dateList))));
        $this->assign("operatingSort",$operatingSort);
        $this->assign("operatingList",$operatingList);
        $this->assign("operatingFieldSort",["dta_2","dta_3"]);
        $this->assign("cokeOperatingList",$cokeOperatingList);
        $this->assign("turnoverSort",$turnoverSort);
        $this->assign("turnoverList",$turnoverList);
        $this->assign("hb_tr_str",$hb_tr_str);
        $this->assign("sy_zjf_tr_str",$sy_zjf_tr_str);
        $this->assign("qntq_zjf_tr_str",$qntq_zjf_tr_str);
    }

    public function cjl_zhangdie($int){
        if($int>0){
            return "增长".abs($int)."%";
        }elseif($int<0){
            return "减少".abs($int)."%";
        }else{
            return "持平";
        }
    }

    /**
     * 焦化企业吨焦毛利变化
     * Created by zfy.
     * Date:2024/5/28 16:24
     * @param $dateList
     */
    protected function handleCokeProfit($dateList){
        //湿熄焦
        $cokeProfit = $this->_dao->getJqLiRun($dateList);
        $cokeProfitInfo = $this->calcStockData($cokeProfit);

        //干熄焦
        $cokeProfit2 = $this->_dao->getJqLiRun2($dateList);
        $cokeProfitInfo2 = $this->calcStockData($cokeProfit2);

        //文字内容
        $contents = "本周全国焦企平均吨焦（湿熄）".$this->zhangdie4(round($cokeProfit['thisDate']['China']))."，".$this->dingjia_jiacha(round($cokeProfit['thisDate']['China'] - $cokeProfit['lastDate']['China']))."，";
        $contents .= "其中：山西焦企".$this->zhangdie4(round($cokeProfit['thisDate']['ShanXi']))."、";
        $contents .= "山东".$this->zhangdie4(round($cokeProfit['thisDate']['ShanDong']))."、";
        $contents .= "河北".$this->zhangdie4(round($cokeProfit['thisDate']['HeBei']))."。";

        //文字内容
        $contents .= "全国焦企平均吨焦（干熄）".$this->zhangdie4(round($cokeProfit2['thisDate']['China']))."，";
        $contents .= "其中：山西焦企".$this->zhangdie4(round($cokeProfit2['thisDate']['山西']))."、";
        $contents .= "山东".$this->zhangdie4(round($cokeProfit2['thisDate']['山东']))."、";
        $contents .= "河北".$this->zhangdie4(round($cokeProfit2['thisDate']['河北']))."。";

        if($dateList["thisDate"] > "2025-04-07"){
            $this->assign("cokeProfitContents_new",$contents);
        }else{
            $this->assign("cokeProfitContents",$contents);
        }
        
        $this->assign("cokeProfitInfo",$cokeProfitInfo);
        $this->assign("cokeProfitInfo2",$cokeProfitInfo2);
    }

    /**
     * 钢材成本与毛利变化
     * Created by zfy.
     * Date:2024/5/28 10:43
     * @param $dateList
     */
    protected function handleCostAndProfit($dateList,$workDate){
        //成本
        $sort = array("45","15","105","163","44","14","104");
        $costAndProfitInfo = $this->_dao->getCostAndProfitInfo($dateList,$sort);
        $costAndProfitList = $this->calcStockData($costAndProfitInfo);

        //市场价格
        $priceSort = array("072023","672023","D220431","072023c","123112","6731126", "D23112");
        $marketPriceInfo = $this->maindao->getMarketPriceInfo($dateList,$priceSort);
        $marketPriceList = $this->calcStockData($marketPriceInfo);

        //毛利计算
        $grossProfitInfo = $this->calcGrossProfit($costAndProfitInfo,$marketPriceInfo);
        $grossProfitList = $this->calcStockData($grossProfitInfo);

        //其中河北唐山螺纹钢和热卷成本分别为A元/吨和B元/吨，分别较上周提高A1元/吨和B1元/吨，山东日照螺纹钢和热卷成本分别为C元/吨和D元/吨，分别较上周提高C1元/吨和D1元/吨。
        $marketNews = html_entity_decode($this->_dao->getMarketNews($workDate,2,213));
        if($dateList["thisDate"] > "2025-04-07"){
            $marketNews213_new = "其中河北唐山螺纹钢和热卷成本分别为".$costAndProfitInfo["thisDate"]["15"]."元/吨和".$costAndProfitInfo["thisDate"]["14"]."元/吨，分别较上周".$this->zhangdie8($costAndProfitInfo["thisDate"]["15"] - $costAndProfitInfo["lastDate"]["15"])."元/吨和".$this->zhangdie8($costAndProfitInfo["thisDate"]["14"] - $costAndProfitInfo["lastDate"]["14"])."元/吨，山东日照螺纹钢和热卷成本分别为".$costAndProfitInfo["thisDate"]["105"]."元/吨和".$costAndProfitInfo["thisDate"]["104"]."元/吨，分别较上周".$this->zhangdie8($costAndProfitInfo["thisDate"]["105"] - $costAndProfitInfo["lastDate"]["105"])."元/吨和".$this->zhangdie8($costAndProfitInfo["thisDate"]["104"] - $costAndProfitInfo["lastDate"]["104"])."元/吨。";
            $this->assign( "marketNews213_new", $marketNews213_new);
        }
        
        $this->assign( "marketNews213", $marketNews);
        $this->assign("sort",$sort);
        $this->assign("costAndProfitList",$costAndProfitList);
        $this->assign("priceSort",$priceSort);
        $this->assign("marketPriceList",$marketPriceList);
        $this->assign("grossProfitList",$grossProfitList);
    }

    /**
     * 硅铁、硅锰成本毛利变化
     * Created by zfy.
     * Date:2024/5/29 15:42
     * @param $dateList
     */
    protected function handleSiliconTin($dateList){
        //硅铁成本
        $guiTie = $this->_dao->getSiliconTinInfo($dateList,5);
        $guiTieList = $this->calcStockData($guiTie);
        //硅锰成本
        $guiMeng = $this->_dao->getSiliconTinInfo($dateList,4);
        $guiMengList = $this->calcStockData($guiMeng);

        //硅铁、硅锰市场价格
        $priceSort = array("L187102","4389101");
        $marketPriceInfo = $this->maindao->getMarketPriceInfo($dateList,$priceSort);
        $marketPriceList = $this->calcStockData($marketPriceInfo);

        //硅铁、硅锰毛利
        $thjData = $this->mergeArray($guiTie,$guiMeng);
        $grossProfitInfo = $this->calcGrossProfit($thjData,$marketPriceInfo);
        $grossProfitList = $this->calcStockData($grossProfitInfo);

        //文字内容
        $contents = "本周，硅铁和硅锰成本分别为".$guiTie['thisDate']['5']."元/吨和".$guiMeng['thisDate']['4']."元/吨，";
        $contents .= "较上周分别".$this->zhangdie5(bcsub($guiTie['thisDate']['5'],$guiTie['lastDate']['5']));
        $contents .= "和".$this->zhangdie5(bcsub($guiMeng['thisDate']['4'],$guiMeng['lastDate']['4']))."；";
        $contents .= "毛利分别为".$grossProfitInfo['thisDate']['L187102']."元/吨和".$grossProfitInfo['thisDate']['4389101']."元/吨，";
        $contents .= "分别较上周".$this->zhangdie5(bcsub($grossProfitInfo['thisDate']['L187102'],$grossProfitInfo['lastDate']['L187102']));
        $contents .= "和".$this->zhangdie5(bcsub($grossProfitInfo['thisDate']['4389101'],$grossProfitInfo['lastDate']['4389101']))."。";

        if($dateList["thisDate"] > "2025-04-07"){
            $this->assign("grossProfitContents_new",$contents);
        }else{
            $this->assign("grossProfitContents",$contents);
        }
        $this->assign("thjPriceSort",$priceSort);
        $this->assign("guiTieList",$guiTieList);
        $this->assign("guiMengList",$guiMengList);
        $this->assign("thjMarketPriceList",$marketPriceList);
        $this->assign("thjgrossProfitList",$grossProfitList);
    }

    /**
     * 处理价格预测
     * Created by zfy.
     * Date:2024/6/4 15:05
     * @param $dateList
     */
    protected function handleLastWeekForecast($dateList){
        $lastWeekForecastInfo = $this->_dao->getLastWeekForecast($dateList['next_start_date'],$dateList['next_end_date'], 2 , 1);
        $thisWeekForecastInfo = $this->_dao->getLastWeekForecast($dateList['this_start_date'],$dateList['this_end_date'], 2 , 0);
        $this->assign("lastWeekForecastInfo",$lastWeekForecastInfo);
        $this->assign("thisWeekForecastInfo",$thisWeekForecastInfo);
        $this->assign("steel_variety_list",array("houban_price"=>"厚板","rzbj_price"=>"热卷","lzbj_price"=>"冷卷","suanxi_price"=>"酸洗","duxin_price"=>"镀锌","lw_price"=>"螺纹钢","h_xg_price"=>"H型钢","tjg_price"=>"碳结钢","fp_price"=>"方坯"));
        $this->assign("thj_variety_list",array("tks_price"=>"铁矿石","fg_price"=>"废钢","gm_price"=>"硅锰","gt_price"=>"硅铁","gtmt_price"=>"高锰","nieban_price"=>"镍板","qimei_price"=>"气煤","jiaomei_price"=>"1/3焦煤","dljm_price"=>"主焦煤","shoumei_price"=>"瘦煤","zyjj_price"=>"冶金焦"));
    }


    /**
     * 处理周预测的所有图表
     * Created by zfy.
     * Date:2024/5/20 15:47
     * @param $xunDateList
     */
    protected function handleTrendChart($xunDateList){

        //年折叠图取近五年数据
        $end_year = date("Y",strtotime($xunDateList['this_end_date']));
        $start_year = $end_year - 2;
        $yearStr = $start_year.",".$end_year;
        $oneYearAgo = date("Y-m-d",strtotime($xunDateList['this_end_date']."-1 year"));
        $imagesUrls = array(
            "stock"=>array(
                "1"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart=&DateEnd=&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=8&ImageTitle=uNayxM7ltPPGt9bWytCzob%2fitOY%3d&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"635","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"uNayxM7ltPPGt9bWv+K05ijW3Cmxvtbcv+K05g==","unitconver":"5","unitstring":"zfK21g==","iscustomerdb":"0"}]&needCompress=0&ChartExt={"isfestival": "0","Type":"1","DateStr":"'.$yearStr.'"}&ChartExtLineStyle=',
                "2"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart=&DateEnd=&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=8&ImageTitle=uNayxM7ltPPGt9bWuNazp7%2fitOY%3d&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"62018","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"uNazp87ltPPGt9bWv+K05ijW3Cm/4rTm","unitconver":"5","unitstring":"zfK21g==","iscustomerdb":"0"}]&needCompress=0&ChartExt={"isfestival": "0","Type":"1","DateStr":"'.$yearStr.'"}&ChartExtLineStyle='
            ),
            "operating"=>array(
                "3"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart=&DateEnd=&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=8&ImageTitle=ufrE2tb30qq41rOnuN%2fCr7C0svrE3L%2bquaTCyg%3d%3d&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"646","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"yKu5+rjfwq8tsLSy+sTcv6q5pMLK","unitconver":"0","unitstring":"JQ==","iscustomerdb":"0"}]&needCompress=0&ChartExt={"isfestival": "0","Type":"1","DateStr":"'.$yearStr.'"}&ChartExtLineStyle=',
                "4"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart=&DateEnd=&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=8&ImageTitle=ufrE2rbAwaK158KvxvPStbC0svrE3L%2bquaTCyg%3d%3d&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"56990","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"yKu5+rXnwq8tsLSy+sTcv6q5pMLK","unitconver":"0","unitstring":"JQ==",
                "iscustomerdb":"0"}]&needCompress=0&ChartExt={"isfestival": "0","Type":"1","DateStr":"'.$yearStr.'"}&ChartExtLineStyle='
            ),
            "costProfit"=>array(
                "9"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart='.$oneYearAgo.'&DateEnd='.$xunDateList['this_end_date'].'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=2&ImageTitle=va3L1dHYva2438KvL7Xnwq%2fC3c7GuNazybG%2b0%2bvJz7qjwt3OxrjWvNu48Q%3d%3d&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"91078","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"uN/Cr8Ldzsa41rPJsb4=","unitconver":"1","unitstring":"1Ko=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"91082","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"tefCr8Ldzsa41rPJsb4=","unitconver":"1","unitstring":"1Ko=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"18941","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"yc+6o0hSQjQwMKa1MjAtMjJtbQ==","unitconver":"1","unitstring":"1Ko=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle=',
                "10"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart='.$oneYearAgo.'&DateEnd='.$xunDateList['this_end_date'].'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=2&ImageTitle=va3L1dHYva2438KvyMi+7bPJsb7T68nPuqPIyL7tvNu48Q==&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"91073","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"uN/Cr8jIvu2zybG+","unitconver":"1","unitstring":"1Ko=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"33252","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"yc+6o1EyMzVCIDUuNzVtbSoxNTAwKkM=","unitconver":"1","unitstring":"1Ko=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle='
            ),
            "ytgStock"=>array(
                "11"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart='.$oneYearAgo.'&DateEnd='.$xunDateList['this_end_date'].'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=2&ImageTitle=08XM2LjWsPSyxMrQs6G/4rTm&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"688","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"08XM2LjWsPSyxMrQs6G/4rTm","unitconver":"1","unitstring":"ttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle=',
                "12"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart='.$oneYearAgo.'&DateEnd='.$xunDateList['this_end_date'].'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=2&ImageTitle=08XM2LjWxvPStb/itOajqNbco6k=&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"91754","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"08XM2LjWxvPStb/itOYo1twp19y/4rTm","unitconver":"0","unitstring":"zfK21g==","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle='
            ),
            "portStock"=>array(
                "13"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart=&DateEnd=&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=8&ImageTitle=&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"11","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"1tC5+tb30qq427/azPq/88qvv+K05ijW3Cm/4rTm","unitconver":"5","unitstring":"zfK21g==","iscustomerdb":"0"}]&needCompress=0&ChartExt={"isfestival": "0","Type":"1","DateStr":"'.$yearStr.'"}&ChartExtLineStyle=',
                "14"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart='.$oneYearAgo.'&DateEnd='.$xunDateList['this_end_date'].'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=2&ImageTitle=&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"62460","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"yKu5+jQ2uNvM+r/zyq+1vbjb19zBvw==","unitconver":"5","unitstring":"zfK21g==","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle=',
                "15"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart='.$oneYearAgo.'&DateEnd='.$xunDateList['this_end_date'].'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=2&ImageTitle=&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"679","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"ufrE2rjbv9rK6Ljbwb/Iq7n6uNu/2sjVvvnK6Ljbwb8=","unitconver":"5","unitstring":"zfK21g==","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle=',
                "16"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart='.$oneYearAgo.'&DateEnd='.$xunDateList['this_end_date'].'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=2&ImageTitle=&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"110745","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"yKu5+rjWs6fM+r/zyq+9+L/av+K05sG/","unitconver":"5","unitstring":"zfK21g==","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle='
            ),
            "scrapStock"=>array(
                "17"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart='.$oneYearAgo.'&DateEnd='.$xunDateList['this_end_date'].'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=3&ImageTitle=&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"62449","DTIDSub":"","DataImage":"1","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"t8+41r/itObX3MG/","unitconver":"0","unitstring":"zfK21g==","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"62450","DTIDSub":"","DataImage":"2","zhou":"1","DataType":"1","GongshiID":"0","LineTitle":"t8+41r/itOa/ydPDzOzK/Q==","unitconver":"0","unitstring":"zOw=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle=',
                "18"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart='.$oneYearAgo.'&DateEnd='.$xunDateList['this_end_date'].'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=2&ImageTitle=ufrE2jc4vNK41sbzt8%2b41sjVvvm1vbv10%2bvI1b75z%2fu6xMG%2f&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"62451","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"yNW++bW9u/XBvw==","unitconver":"0","unitstring":"zfK21g==","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"62452","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"yNW++c/7usTBvw==","unitconver":"0","unitstring":"zfK21g==","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle='
            ),
            "cokeStock"=>array(
                "19"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart=&DateEnd=&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=8&ImageTitle=&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"645","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"tsDBor25xvOy+sTcwPvTw8LKLcirufoyMDC80g==","unitconver":"0","unitstring":"JQ==","iscustomerdb":"0"}]&needCompress=0&ChartExt={"isfestival": "0","Type":"1","DateStr":"'.$yearStr.'"}&ChartExtLineStyle=',
                "20"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart='.$oneYearAgo.'&DateEnd='.$xunDateList['this_end_date'].'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=5&ImageTitle=uNazp6GivbnG87rNuNu%2f2r25zL%2b%2f4rTmwb8%3d&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"61943","DTIDSub":"","DataImage":"5","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"ODC80rjWs6c=","unitconver":"0","unitstring":"zfK21g==","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"3722","DTIDSub":"","DataImage":"5","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"MTAwvNK2wMGivbnG8w==","unitconver":"0","unitstring":"zfK21g==","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"54279","DTIDSub":"","DataImage":"5","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"y8S087jbv9o=","unitconver":"1","unitstring":"zfK21g==","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle=',
                "21"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart='.$oneYearAgo.'&DateEnd='.$xunDateList['this_end_date'].'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=3&ImageTitle=MTAwvNK2wMGivbnG88G2vbnDur%2fitObT67%2fitOa%2fydPDzOzK%2fQ%3d%3d&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"56914","DTIDSub":"","DataImage":"1","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"vbnDur/itOY=","unitconver":"0","unitstring":"zfK21g==","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"3723","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"wba9ucO6v+K05r/J08PM7Mr9","unitconver":"0","unitstring":"zOw=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle=',
                "22"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart='.$oneYearAgo.'&DateEnd='.$xunDateList['this_end_date'].'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=2&ImageTitle=zuW087jbv9q9%2bL%2fawba9ucO6v%2bK05sG%2f&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"62435","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"19y/4rTm","unitconver":"0","unitstring":"zfK21g==","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle=',
//                "22"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart='.$oneYearAgo.'&DateEnd='.$xunDateList['this_end_date'].'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=2&ImageTitle=wfm087jbv9rDusy%2fv%2bK05sG%2f&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"62442","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"us+8xs2zvMY=","unitconver":"0","unitstring":"zfK21g==","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle='
            ),
            "cokeProfit"=>array(
                "23"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart='.$oneYearAgo.'&DateEnd='.$xunDateList['this_end_date'].'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=2&ImageTitle=&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"3718","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"yKu5+r25xvO21r25wPvI8yAoyNW++da1KbbWwPvI8w==","unitconver":"1","unitstring":"1Ko=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle=',
                "24"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart='.$oneYearAgo.'&DateEnd='.$xunDateList['this_end_date'].'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=2&ImageTitle=1vfSqrL6x%2fi9ucbztta9ucD7yPOjqMjVvvnWtaOp&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"91172","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"yb3O9w==","unitconver":"1","unitstring":"1Ko=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"91178","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"yb22qw==","unitconver":"1","unitstring":"1Ko=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"91175","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"utOxsQ==","unitconver":"1","unitstring":"1Ko=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"91193","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"utPEzw==","unitconver":"1","unitstring":"1Ko=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle=',
                "25"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart='.$oneYearAgo.'&DateEnd='.$xunDateList['this_end_date'].'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=2&ImageTitle=&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"147944","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"yKu5+r25xvO21r25wPvI8y24yc+oKMjVvvnWtSm21sD7yPM=","unitconver":"1","unitstring":"1Ko=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle=',
                "26"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart='.$oneYearAgo.'&DateEnd='.$xunDateList['this_end_date'].'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=2&ImageTitle=1vfSqrL6x%2fi4yc%2bovbm21r25wPvI86OoyNW%2b%2bda1o6k%3d&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"148161","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"yb3O9w==","unitconver":"1","unitstring":"1Ko=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"148167","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"yb22qw==","unitconver":"1","unitstring":"1Ko=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"148164","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"utOxsQ==","unitconver":"1","unitstring":"1Ko=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"148176","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"utPEzw==","unitconver":"1","unitstring":"1Ko=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle='
            ),
            "thjProfit"=>array(
                "27"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart='.$oneYearAgo.'&DateEnd='.$xunDateList['this_end_date'].'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=2&ImageTitle=uejM%2brPJsb7T68rQs6G827jx&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"92833","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"uejM+rrPvfCzybG+","unitconver":"1","unitstring":"1Ko=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"6686","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"RmVTaTc1LUK807mkv+m827jx","unitconver":"1","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle=',
                "28"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart='.$oneYearAgo.'&DateEnd='.$xunDateList['this_end_date'].'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=2&ImageTitle=uejDzLPJsb7T68rQs6G827jx&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"92832","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"uejDzLrPvfCzybG+","unitconver":"1","unitstring":"1Ko=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"6697","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"yb3O90ZlTW42OFNpbDgoUKHcMC4yNSUpvNu48Q==","unitconver":"1","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle='
            ),
            "avgPrice"=>array(
                "29"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.SG_DATACENTER_GUID.'&DateStart='.$oneYearAgo.'&DateEnd='.$xunDateList['this_end_date'].'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=2&ImageTitle=vfzSu8TquvGw5aGiyMi%2b7cjVvvm829ffysbNvA%3d%3d&isbg=3&isjz=0&isfg=0&color=0&mc_type=5&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"148123","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"uvGw5Q==","unitconver":"0","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"148125","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"yMi+7Q==","unitconver":"0","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle=',
                "30"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.SG_DATACENTER_GUID.'&DateStart='.$oneYearAgo.'&DateEnd='.$xunDateList['this_end_date'].'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=2&ImageTitle=vfzSu8Tqwt3OxrjWoaJI0M241qGit73F98jVvvm829ffysbNvA%3d%3d&isbg=3&isjz=0&isfg=0&color=0&mc_type=5&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"148129","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"wt3OxrjW","unitconver":"0","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"148130","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"SNDNuNY=","unitconver":"0","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"148132","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"t73F9w==","unitconver":"0","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle=',
                "31"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.SG_DATACENTER_GUID.'&DateStart='.$oneYearAgo.'&DateEnd='.$xunDateList['this_end_date'].'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=2&ImageTitle=vfzSu8TqwOS%2b7aGiy%2bHPtKGitsbQv8jVvvm829ffysbNvA%3d%3d&isbg=3&isjz=0&isfg=0&color=0&mc_type=5&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"148126","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"wOS+7Q==","unitconver":"0","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"148127","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"y+HPtA==","unitconver":"0","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"148128","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"tsbQvw==","unitconver":"0","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle=',
                "32"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.SG_DATACENTER_GUID.'&DateStart='.$oneYearAgo.'&DateEnd='.$xunDateList['this_end_date'].'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=2&ImageTitle=vfzSu8TqzLy94bjWyNW%2b%2bbzb19%2fKxs28&isbg=3&isjz=0&isfg=0&color=0&mc_type=5&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"148131","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"zLy94bjWvNu48Q==","unitconver":"0","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle='
            ),
            "yrlPrice"=>array(
                "33"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.SG_DATACENTER_GUID.'&DateStart='.$oneYearAgo.'&DateEnd='.$xunDateList['this_end_date'].'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=2&ImageTitle=vfzSu8Tq0rG98L25oaLW9725w7rI1b75vNvX38rGzbw%3d&isbg=3&isjz=0&isfg=0&color=0&mc_type=5&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"148143","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"0rG98L25","unitconver":"0","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"148141","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"1ve9ucO6","unitconver":"0","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle=',
                "34"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.SG_DATACENTER_GUID.'&DateStart='.$oneYearAgo.'&DateEnd='.$xunDateList['this_end_date'].'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=4&ImageTitle=vfzSu8TqzPq%2f88qvoaK9%2bL%2fav%2fPWuMr9yNW%2b%2bbzb19%2fKxs28&isbg=3&isjz=0&isfg=0&color=0&mc_type=5&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"148133","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"zPq/88qv","unitconver":"0","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"142043","DTIDSub":"","DataImage":"2","zhou":"1","DataType":"1","GongshiID":"0","LineTitle":"vfi/2r/z1rjK/Q==","unitconver":"0","unitstring":"w8DUqi+21g==","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle='
            ),
            "cjl"=>array(
                "35"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart=&DateEnd=&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=8&ImageTitle=ufrE2rK%2ft9a%2brc%2f6yczC3c7GuNbI1b75s8m9u8G%2f&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"6977","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"wt3OxrjWyNW++bPJvbvBvw==","unitconver":"1","unitstring":"ttY=","iscustomerdb":"0"}]&needCompress=0&ChartExt={"isfestival": "0","Type":"1","DateStr":"'.$yearStr.'"}&ChartExtLineStyle=',
                "36"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart=&DateEnd=&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=8&ImageTitle=ufrE2rK%2ft9a%2brc%2f6yczIyL7tyNW%2b%2bbPJvbvBvw%3d%3d&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"6978","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"yMjU/rDlvu3I1b75s8m9u8G/","unitconver":"0","unitstring":"ttY=","iscustomerdb":"0"}]&needCompress=0&ChartExt={"isfestival": "0","Type":"1","DateStr":"'.$yearStr.'"}&ChartExtLineStyle=',
                "37"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart=&DateEnd=&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=8&ImageTitle=ufrE2rK%2ft9a%2brc%2f6yczW0LrxsOXI1b75s8m9u8G%2f&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"6979","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"1tC68bDlyNW++bPJvbvBvw==","unitconver":"0","unitstring":"ttY=","iscustomerdb":"0"}]&needCompress=0&ChartExt={"isfestival": "0","Type":"1","DateStr":"'.$yearStr.'"}&ChartExtLineStyle='
            ),
            "gc_jc_img"=>array(
                "38"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart='.$oneYearAgo.'&DateEnd='.$xunDateList['this_end_date'].'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=3&ImageTitle=vu3C3bLu&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"19826","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"wt3OxrjW","unitconver":"1","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"33854","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"yMjU/rDlvu0=","unitconver":"1","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"","DTIDSub":"","DataImage":"1","zhou":"0","DataType":"1","GongshiID":"12747","LineTitle":"vu3C3bLu","unitconver":"0","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle=',
                "39"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart='.$oneYearAgo.'&DateEnd='.$xunDateList['this_end_date'].'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=3&ImageTitle=wOTIyLLu&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"33855","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"yMjU/rDlvu0=","unitconver":"1","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"36932","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"wOTU/rDlvu0=","unitconver":"1","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"","DTIDSub":"","DataImage":"1","zhou":"0","DataType":"1","GongshiID":"12748","LineTitle":"wOTIyLLu","unitconver":"0","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle=',
                '40'=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart='.$oneYearAgo.'&DateEnd='.$xunDateList['this_end_date'].'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=3&ImageTitle=sOW%2b7bLu&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"33855","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"yMjU/rDlvu0=","unitconver":"1","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"29869","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"1tC68bDl","unitconver":"1","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"","DTIDSub":"","DataImage":"1","zhou":"1","DataType":"1","GongshiID":"12749","LineTitle":"sOW+7bLu","unitconver":"0","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle=',
                '41'=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart='.$oneYearAgo.'&DateEnd='.$xunDateList['this_end_date'].'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=3&ImageTitle=xM%2bxsbzbsu4%3d&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"19826","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"vMPEz8Ldzsa41g==","unitconver":"1","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"19061","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"urzW3cLdzsa41g==","unitconver":"1","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"","DTIDSub":"","DataImage":"1","zhou":"0","DataType":"1","GongshiID":"12788","LineTitle":"xM+xsbzbsu4=","unitconver":"0","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle='
            )
        );
        // $i = 1;
        foreach ($imagesUrls as $index => $images) {
            foreach ($images as $id => $imagesUrl) {
                $imageid = "tu" . $id . "_" . $id;
                //width:500px; height:279px;
                //if($i == 1){
                    // if($id == 35){
                    //     $imagesUrlList[$index][] = "<tr><td>" . '<div align="center" class="" id="' . $imageid . '"><iframe name="iframe" style="margin:0; padding:0; width:500px; height:279px; background-color:#FFF; visibility:inherit;" src=\'' . $imagesUrl . '&callbackname=' . $imageid . '\' frameborder="0" scrolling="no"></iframe></div></td><td></td></tr>';
                    // }else{
                        $imagesUrlList[$index][] =  '<div align="center" class="" id="' . $imageid . '"><iframe name="iframe" style="margin:0; padding:0; width:500px; height:279px; background-color:#FFF; visibility:inherit;" src=\'' . $imagesUrl . '&callbackname=' . $imageid . '\' frameborder="0" scrolling="no"></iframe></div>';
                    // }
                    // $i++;
                // }else if($i == 2){
                //     $imagesUrlList[$index][] = "<td>" . '<div align="center" class="" id="' . $imageid . '"><iframe name="iframe" style="margin:0; padding:0; width:500px; height:279px; background-color:#FFF; visibility:inherit;" src=\'' . $imagesUrl . '&callbackname=' . $imageid . '\' frameborder="0" scrolling="no"></iframe></div></td></tr>';
                //     $i = 1;
                // }
            }
        }
        $this->assign("imagesUrlLists",$imagesUrlList);
    }

    /**
     * 通过市场价格 和 成本 计算毛利
     * Created by zfy.
     * Date:2024/5/28 10:44
     * @param $costAndProfitInfo
     * @param $marketPriceInfo
     * @return array
     */
    protected function calcGrossProfit($costAndProfitInfo,$marketPriceInfo){
        $typeMatch = array("072023"=>"45","672023"=>"15","D220431"=>"105","072023c"=>"163","5320234"=>"193","123112"=>"44","6731126"=>"14","D23112"=>"104","L187102"=>"5","4389101"=>"4");
        $grossProfitInfo = array();
        foreach ($marketPriceInfo as $index => $item) {
            foreach ($item as $priceId => $price) {
                $grossProfitInfo[$index][$priceId] = $price - $costAndProfitInfo[$index][$typeMatch[$priceId]];
            }
        }
        return $grossProfitInfo;
    }

    /**
     * 组合两个数组
     * Created by zfy.
     * Date:2024/5/29 17:43
     * @param $arr1
     * @param $arr2
     * @return array
     */
    protected function mergeArray($arr1,$arr2)
    {
        $ret_data = array();
        foreach ($arr1 as $key => $value){
            $ret_data[$key] = $value + $arr2[$key];
        }
        return $ret_data;
    }
    // 获取检检修天数
    protected function getdev_dates($itemvv)
    {
        if ($itemvv['t1_field6'] == "3" && (strtotime($itemvv['EliminationDate']) < strtotime($itemvv['t1_field12']))) {
            if (strtotime($itemvv['EliminationDate']) > strtotime($itemvv['t1_field11'])) {
                $date = floor((strtotime($itemvv['EliminationDate']) - strtotime($itemvv['t1_field11'])) / 86400);
            } elseif (strtotime($itemvv['EliminationDate']) <= strtotime($itemvv['t1_field11'])) {
                $date = 0;
            }
        } else {
            //检修天数 如果复产日期是待定（ResumptionPending=1）的话，检修天数为30
            //输入了复产日期，又勾了待定的，不要按照默认30天计算检修天数,按复产日期-停产日期计算天数
            $date = ($itemvv['ResumptionPending'] == '1' && $itemvv['t1_field12'] == "0000-00-00 00:00:00") ? '30' : floor((strtotime($itemvv['t1_field12']) - strtotime($itemvv['t1_field11'])) / 86400);
        }
        return $date;
    }

    protected function getcn($itemvv)
    {
        if ($itemvv['t1_field1'] == '9' && $itemvv['t1_field16'] > 0) {
            $cn = $itemvv['t1_field16'];
        } else {
            $cn = $itemvv['t1_field4'];
        }
        return (float)$cn;
    }

    protected function get_hzcl($t1_field11, $t1_field12, $ResumptionPending, $start_date, $end_date, $t1_field6, $EliminationDate)
    {
        if ($t1_field6 == "3") {
            if ($t1_field12 == "0000-00-00 00:00:00") {
                if (strtotime($t1_field11) < strtotime($EliminationDate)) {
                    $t1_field12 = $EliminationDate;
                    $mark = 1;
                } elseif (strtotime($t1_field11) >= strtotime($EliminationDate)) {
                    $times = 0;
                }
            } elseif (strtotime($t1_field12) < strtotime($EliminationDate)) {
                $mark = 1;
            } elseif (strtotime($t1_field12) >= strtotime($EliminationDate) && strtotime($t1_field11) < strtotime($EliminationDate)) {
                $t1_field12 = $EliminationDate;
                $mark = 1;
            } elseif (strtotime($t1_field11) >= strtotime($EliminationDate)) {
                $times = 0;
            }
        } else {
            $mark = 1;
        }

        if ($mark == "1") {
            if ($ResumptionPending == 1 && $t1_field12 == "0000-00-00 00:00:00") {
                if (strtotime($t1_field11) <= strtotime($start_date)) {
                    $times = floor((strtotime($end_date) - strtotime($start_date)) / 86400);
                    if ($times > 30) {
                        $times = 30;
                    }
                } elseif (strtotime($t1_field11) > strtotime($start_date) && strtotime($t1_field11) < strtotime($end_date)) {
                    $times = floor((strtotime($end_date) - strtotime($t1_field11)) / 86400);
                    if ($times > 30) {
                        $times = 30;
                    }
                } elseif (strtotime($t1_field11) == strtotime($end_date)) {
                    $times = 1;
                } elseif (strtotime($t1_field11) > strtotime($end_date)) {
                    $times = 0;
                }
            } elseif (strtotime($start_date) <= strtotime($t1_field11) && strtotime($t1_field12) <= strtotime($end_date)) //X<=A<B<=Z
            {
                $times = floor((strtotime($t1_field12) - strtotime($t1_field11)) / 86400);
            } elseif (strtotime($start_date) <= strtotime($t1_field11) && strtotime($t1_field11) < strtotime($end_date) && strtotime($t1_field12) >= strtotime($end_date)) // X<=A<Z<=B
            {
                $times = floor((strtotime($end_date) - strtotime($t1_field11)) / 86400);
            } elseif (strtotime($t1_field11) == strtotime($end_date) && strtotime($t1_field12) > strtotime($end_date)) // A=Z<B
            {
                $times = 1;
            } elseif (strtotime($t1_field11) <= strtotime($start_date) && strtotime($t1_field12) <= strtotime($end_date) && strtotime($t1_field12) >= strtotime($start_date)) //A<=X<B=<Z
            {
                $times = floor((strtotime($t1_field12) - strtotime($start_date)) / 86400);
            }
            // elseif(strtotime($t1_field11)<strtotime($start_date) && strtotime($t1_field12) == strtotime($start_date)) //A<X=B
            // {
            //     $times = 1;
            // }
            elseif (strtotime($t1_field11) <= strtotime($start_date) && strtotime($end_date) <= strtotime($t1_field12)) //A<=X<Z<=B
            {
                $times = floor((strtotime($end_date) - strtotime($start_date)) / 86400);
            } elseif (strtotime($t1_field11) <= strtotime($start_date) && strtotime($end_date) <= strtotime($t1_field12) && strtotime($start_date) == strtotime($end_date)) //A<=X=Z<=B
            {
                $times = 1;
            } elseif (strtotime($t1_field11) > strtotime($end_date) || strtotime($t1_field12) < strtotime($start_date)) //Z<A or B<X
            {
                $times = 0;
            }
        }
        return $times;
    }

    protected function get_areasum($arr)
    {
        foreach ($arr as $vi => $areaarr) {
            foreach ($areaarr as $v) {
                $arr[$vi]['合计'] += $v;
            }
        }
        return $arr;
    }

    protected function getdev_stime($devices)
    {
        // 检修年份小于当前年的，要显示年月日
        $year = date("Y", strtotime($devices['t1_field11']));
        $this_year = date("Y");
//        if ($year < $this_year) {
//            $stime = $this->getdateno0($devices['t1_field11'], "年月日");
//        } else {
//            $stime = $this->getdateno0($devices['t1_field11'], "月日");
//        }
        //全部显示年月日
        $stime = $this->getdateno0($devices['t1_field11'], "年月日");
        return $stime;
    }
    protected function getdateno0($date, $type)
    {
        $year = date("Y", strtotime($date));
        $month = date("m", strtotime($date));
        $day = date("d", strtotime($date));

        if ($month < 10) {
            $month = str_replace("0", "", $month);
        }
        if ($day < 10) {
            $day = str_replace("0", "", $day);
        }

        if ("年月日" == $type) {
            return $year . "年" . $month . "月" . $day . "日";
        }
        if ("月日" == $type) {
            return $month . "月" . $day . "日";
        }
    }

    function get_jmmjt_gx_pingheng($type,$edate){
        $sql = 'SELECT * FROM `jmjt_gx_pingheng` WHERE `datatype` = "'.$type.'" and dat_ym <= "'.$edate.'" ORDER BY dat_ym desc limit 15';
        // echo $sql;
        $dataArray = $this->drcdao->query( $sql );
        $ret_data = array();
        foreach($dataArray as &$v){
            $riqi = date('Y年n月',strtotime($v["dat_ym"]));
            $v["DATE2"] = $riqi;
            // if($v["is_yuce"] == 1){
            //     $v["DATE2"] = "<font color='blue'>". $riqi."</font>";
            //     $v["D1"] = "<font color='blue'>". $v["D1"]."</font>";
            //     $v["D2"] = "<font color='blue'>". $v["D2"]."</font>";
            //     $v["D3"] = "<font color='blue'>". $v["D3"]."</font>";
            //     $v["D4"] = "<font color='blue'>". $v["D4"]."</font>";
            // }
            $ret_data[$riqi] = $v;
        }
        return $ret_data;
    }

	function get_hgData($L2id,$L2OrderNo,$tscl="",$edate){
        //update 2023-05-23 L2OrderNo为排序序号，可变，可不传
        // echo "<pre/>";print_r($this->maindao);
        if($tscl == 1){
            $sql = 'SELECT a . * FROM (SELECT * FROM `L2Data` WHERE `L2Id` = "'.$L2id.'" and DATE <= "'.$edate.'" ORDER BY `L2Data`.`DATE` DESC) AS a GROUP BY DATE_FORMAT( a.DATE, "%Y-%m" ) ORDER BY a.DATE desc limit 36';
        }else{
            // $sql = "select * from L2Data where L2id= '".$L2id."' and L2OrderNo= '".$L2OrderNo."' ".$where." order by DATE asc limit 15";
            $sql = 'SELECT a . * FROM (SELECT * FROM `L2Data` WHERE `L2Id` = "'.$L2id.'" and DATE <= "'.$edate.'" ORDER BY `L2Data`.`DATE` DESC) AS a ORDER BY a.DATE desc limit 36';
        }
		$query_data = $this->maindao->query( $sql );
        $ret_data = array();
        foreach($query_data as &$v){
            $riqi = date('Y年n月',strtotime($v["DATE"]));
            $v["DATE2"] = $riqi;
            if($v["is_yuce"] == 1){
                $v["DATE2"] = "<font color='blue'>". $riqi."</font>";
                $v["D1"] = "<font color='blue'>". $v["D1"]."</font>";
                $v["D2"] = "<font color='blue'>". $v["D2"]."</font>";
                $v["D3"] = "<font color='blue'>". $v["D3"]."</font>";
                $v["D4"] = "<font color='blue'>". $v["D4"]."</font>";
            }
            $ret_data[$riqi] = $v;
        }
		return $ret_data;
	}


	function get_gc_yrl_data($lastday,$day,$lastday2,$day2){
        //研究中心山钢预测录入也有一份，修改请同步修改；
		//1、钢材市场价格变化情况
        $houban = array('173012','183012','073012','403012','363012');
		$rzbj = array('6931122','D231121','8231125','8731121','0731123');
		$lzbj = array('694312','874312','D24312','074312');
        $lzbj2 = "1843122";
		$suanxi = array('D252151','0752155');
		$duxin = array('D24412','184412','074412');
		$lw = array('692043','172023','872023','182023','082023');
		$h_xg = array('697311','827311','077311','677311','287311');
		$tjg = array('692243','702243','872243','122243','082243');
		// $fp = '6784115';
        $fp = '678411';

		//2、原燃料市场价格变化
		$tks = array('188611','D28611');
		$tks_shpi = "H986401";
		$fg = array('178210','678210','118210','238210');
		$gm = array('4189101','5989101','5789101','4389101');
		$gt = array('4187102','5987107','5887104','4487102');
		$gtmt = array('4188201','4388201');
        $nieban = '0762601';
		$qimei = '2063131';
		$jiaomei = 'F363122';
		$dljm = array('T263108','S263103','6663101','4363104');
		$shoumei = array('K163163','K163161','S263141','U263141');
		$zyjj = array('D283114','T283111','4383111','S283111','6683112');

		//钢材市场变化情况
		$price6_arr = $this->hq_price_6($lastday,$day,$GLOBALS['priceid6'],$lastday2,$day2);
        $price7_arr = $this->hq_price_7_week($lastday,$day,$GLOBALS['priceid7'],$lastday2,$day2);
		// echo "<pre/>";print_r($price6_arr);print_r($price7_arr);
		
		$gc_market_data = array(
			"tprice" => array("houban"=>0,
							"houban_num"=>0,
							"rzbj"=>0,
							"rzbj_num"=>0,
							"lzbj"=>0,
							"lzbj_num"=>0,
							"suanxi"=>0,
							"suanxi_num"=>0,
							"duxin"=>0,
							"duxin_num"=>0,
							"lw"=>0,
							"lw_num"=>0,
							"h_xg"=>0,
							"h_xg_num"=>0,
							"tjg"=>0,
							"tjg_num"=>0,
							"fp"=>0,
							"fp_num"=>0),
			"lprice" => array("houban"=>0,
							"houban_num"=>0,
							"rzbj"=>0,
							"rzbj_num"=>0,
							"lzbj"=>0,
							"lzbj_num"=>0,
							"suanxi"=>0,
							"suanxi_num"=>0,
							"duxin"=>0,
							"duxin_num"=>0,
							"lw"=>0,
							"lw_num"=>0,
							"h_xg"=>0,
							"h_xg_num"=>0,
							"tjg"=>0,
							"tjg_num"=>0,
							"fp"=>0,
							"fp_num"=>0),
			"zd" => array("houban"=>0,
							"houban_num"=>0,
							"rzbj"=>0,
							"rzbj_num"=>0,
							"lzbj"=>0,
							"lzbj_num"=>0,
							"suanxi"=>0,
							"suanxi_num"=>0,
							"duxin"=>0,
							"duxin_num"=>0,
							"lw"=>0,
							"lw_num"=>0,
							"h_xg"=>0,
							"h_xg_num"=>0,
							"tjg"=>0,
							"tjg_num"=>0,
							"fp"=>0,
							"fp_num"=>0)
		);
		$yrl_market_data = array(
			"tprice" => array("tks"=>0,
							"tks_num"=>0,
							"tks_shpi"=>0,
							"tks_shpi_num"=>0,
							"fg"=>0,
							"fg_num"=>0,
							"gm"=>0,
							"gm_num"=>0,
							"gt"=>0,
							"gt_num"=>0,
							"gtmt"=>0,
							"gtmt_num"=>0,
                            "nieban"=>0,
							"nieban_num"=>0,
							"qimei"=>0,
							"qimei_num"=>0,
							"jiaomei"=>0,
							"jiaomei_num"=>0,
							"dljm"=>0,
							"dljm_num"=>0,
							"shoumei"=>0,
							"shoumei_num"=>0,
							"zyjj"=>0,
							"zyjj_num"=>0),
			"lprice" => array("tks"=>0,
							"tks_num"=>0,
							"tks_shpi"=>0,
							"tks_shpi_num"=>0,
							"fg"=>0,
							"fg_num"=>0,
							"gm"=>0,
							"gm_num"=>0,
							"gt"=>0,
							"gt_num"=>0,
							"gtmt"=>0,
							"gtmt_num"=>0,
                            "nieban"=>0,
							"nieban_num"=>0,
							"qimei"=>0,
							"qimei_num"=>0,
							"jiaomei"=>0,
							"jiaomei_num"=>0,
							"dljm"=>0,
							"dljm_num"=>0,
							"shoumei"=>0,
							"shoumei_num"=>0,
							"zyjj"=>0,
							"zyjj_num"=>0),
			"zd" => array("tks"=>0,
							"tks_num"=>0,
							"tks_shpi"=>0,
							"tks_shpi_num"=>0,
							"fg"=>0,
							"fg_num"=>0,
							"gm"=>0,
							"gm_num"=>0,
							"gt"=>0,
							"gt_num"=>0,
							"gtmt"=>0,
							"gtmt_num"=>0,
                            "nieban"=>0,
							"nieban_num"=>0,
							"qimei"=>0,
							"qimei_num"=>0,
							"jiaomei"=>0,
							"jiaomei_num"=>0,
							"dljm"=>0,
							"dljm_num"=>0,
							"shoumei"=>0,
							"shoumei_num"=>0,
							"zyjj"=>0,
							"zyjj_num"=>0)
		);

		// $tks_shpi_arr = $this->getTksShpi($lastday,$day,$lastday2,$day2);
		// $yrl_market_data["tprice"]["tks_shpi"] = $tks_shpi_arr["tprice"];
		// $yrl_market_data["lprice"]["tks_shpi"] = $tks_shpi_arr["lprice"];
		// $yrl_market_data["zd"]["tks_shpi"] = $tks_shpi_arr["zd"];

		foreach($price6_arr as $key6 => $item6){
			if($key6 == "tprice" || $key6 == "lprice"){

                $gc_market_data[$key6]["fp"] += $item6[$fp];
				$gc_market_data[$key6]["fp_num"] ++;

				foreach($houban as $houban_id){
					$gc_market_data[$key6]["houban"] += $item6[$houban_id];
					$gc_market_data[$key6]["houban_num"] ++;
				}
				foreach($lzbj as $lzbj_id){
					$gc_market_data[$key6]["lzbj"] += $item6[$lzbj_id];
					$gc_market_data[$key6]["lzbj_num"] ++;
				}
				foreach($duxin as $duxin_id){
					$gc_market_data[$key6]["duxin"] += $item6[$duxin_id];
					$gc_market_data[$key6]["duxin_num"] ++;
				}
				foreach($lw as $lw_id){
					$gc_market_data[$key6]["lw"] += $item6[$lw_id];
					$gc_market_data[$key6]["lw_num"] ++;
				}
				foreach($h_xg as $h_xg_id){
					$gc_market_data[$key6]["h_xg"] += $item6[$h_xg_id];
					$gc_market_data[$key6]["h_xg_num"] ++;
				}
				foreach($tjg as $tjg_id){
					$gc_market_data[$key6]["tjg"] += $item6[$tjg_id];
					$gc_market_data[$key6]["tjg_num"] ++;
				}
				foreach($tks as $tks_id){
					$yrl_market_data[$key6]["tks"] += $item6[$tks_id];
					$yrl_market_data[$key6]["tks_num"] ++;
				}
				foreach($fg as $fg_id){
					$yrl_market_data[$key6]["fg"] += $item6[$fg_id];
					$yrl_market_data[$key6]["fg_num"] ++;
				}
			}
		}
		foreach($price7_arr as $key7 => $item7){
			if($key7 == "tprice" || $key7 == "lprice"){
                $gc_market_data[$key7]["lzbj"] += $item7[$lzbj2];
				$gc_market_data[$key7]["lzbj_num"] ++;

                $yrl_market_data[$key7]["tks_shpi"] += $item7[$tks_shpi];
				$yrl_market_data[$key7]["tks_shpi_num"] ++;

				foreach($rzbj as $rzbj_id){
					$gc_market_data[$key7]["rzbj"] += $item7[$rzbj_id];
					$gc_market_data[$key7]["rzbj_num"] ++;
				}
				foreach($suanxi as $suanxi_id){
					$gc_market_data[$key7]["suanxi"] += $item7[$suanxi_id];
					$gc_market_data[$key7]["suanxi_num"] ++;
				}

				foreach($gm as $gm_id){
					$yrl_market_data[$key7]["gm"] += $item7[$gm_id];
					$yrl_market_data[$key7]["gm_num"] ++;
				}
				foreach($gt as $gt_id){
					$yrl_market_data[$key7]["gt"] += $item7[$gt_id];
					$yrl_market_data[$key7]["gt_num"] ++;
				}
				foreach($gtmt as $gtmt_id){
					$yrl_market_data[$key7]["gtmt"] += $item7[$gtmt_id];
					$yrl_market_data[$key7]["gtmt_num"] ++;
				}

                $yrl_market_data[$key7]["nieban"] += $item7[$nieban];
				$yrl_market_data[$key7]["nieban_num"] ++;

				$yrl_market_data[$key7]["qimei"] += $item7[$qimei];
				$yrl_market_data[$key7]["qimei_num"] ++;

				$yrl_market_data[$key7]["jiaomei"] += $item7[$jiaomei];
				$yrl_market_data[$key7]["jiaomei_num"] ++;

				foreach($dljm as $dljm_id){
					$yrl_market_data[$key7]["dljm"] += $item7[$dljm_id];
					$yrl_market_data[$key7]["dljm_num"] ++;
				}

				foreach($shoumei as $shoumei_id){
					$yrl_market_data[$key7]["shoumei"] += $item7[$shoumei_id];
					$yrl_market_data[$key7]["shoumei_num"] ++;
				}
				foreach($zyjj as $zyjj_id){
					$yrl_market_data[$key7]["zyjj"] += $item7[$zyjj_id];
					$yrl_market_data[$key7]["zyjj_num"] ++;
				}
			}
		}
		// echo "<pre/>";print_r($yrl_market_data);

		$gc_data = array();
		foreach($gc_market_data as $gc_key => $gc_item){
			$i = 0;
			if($gc_key == "tprice")
				$gc_data[$gc_key]["date"] = $day;
			else if($gc_key == "lprice")
				$gc_data[$gc_key]["date"] = $day2;
			foreach($gc_item as $key1 => $v1){
				if($i % 2 == 0 && $gc_item[$key1."_num"] != 0){
					$gc_data[$gc_key][$key1."_price"] = round($gc_item[$key1] / $gc_item[$key1."_num"]);
				}
				$i++;
			}
		}

		$gc_data["zd"]["zhangdie"] = "涨跌";
		foreach($gc_data["tprice"] as $zd_key => $zd_v){
			if($zd_key != "date"){
                if($gc_data["tprice"][$zd_key] !=0 && $gc_data["lprice"][$zd_key] != 0){
                    $gc_data["zd"][$zd_key] = $this->zhangdie ( $gc_data["tprice"][$zd_key] - $gc_data["lprice"][$zd_key] );
                }else{
                    $gc_data["zd"][$zd_key] = $this->zhangdie (0);
                }
            }
		}
		$yrl_data = array();
		foreach($yrl_market_data as $yrl_key => $yrl_item){
			$j = 0;
			if($yrl_key == "tprice")
				$yrl_data[$yrl_key]["date"] = $day;
			else if($yrl_key == "lprice")
				$yrl_data[$yrl_key]["date"] = $day2;
			foreach($yrl_item as $key2 => $v2){
				if($j % 2 == 0 && $yrl_item[$key2."_num"] != 0){
                    if($key2 == "tks_shpi"){
                        $yrl_data[$yrl_key][$key2."_price"] = round(($yrl_item[$key2] / $yrl_item[$key2."_num"]),2);
                    }else{
                        $yrl_data[$yrl_key][$key2."_price"] = round($yrl_item[$key2] / $yrl_item[$key2."_num"]);
                    }
				}
				$j++;
			}
		}
		$yrl_data["zd"]["zhangdie"] = "涨跌";
		foreach($yrl_data["tprice"] as $zd_key2 => $zd_v2){
			if($zd_key2 != "date"){
                if($yrl_data["tprice"][$zd_key2] !=0 && $yrl_data["lprice"][$zd_key2] != 0){
                    $yrl_data["zd"][$zd_key2] = $this->zhangdie ( $yrl_data["tprice"][$zd_key2] - $yrl_data["lprice"][$zd_key2] );
                }else{
                    $yrl_data["zd"][$zd_key2] = $this->zhangdie (0);
                }
                //单独处理进口矿指数
                if ($zd_key2 == 'tks_shpi_price' && $yrl_data["tprice"][$zd_key2] == 0){
                    $yrl_data["tprice"][$zd_key2] = "--";
                    $yrl_data["zd"][$zd_key2] = "--";
                }
            }
				
		}
		// echo "<pre/>";print_r($gc_data);print_r($yrl_data);
		$ret_arr = array(0=>$gc_data,1=>$yrl_data);
        foreach($ret_arr as &$ret_v){
            $ret_v["tprice"]["date"] = date('n月j日',strtotime($day))."-".date('n月j日',strtotime($lastday));
            $ret_v["lprice"]["date"] = date('n月j日',strtotime($day2))."-".date('n月j日',strtotime($lastday2));
        }
		return $ret_arr;
	}

	//获取当前工作日矿煤焦钢期货主力合约及大宗商品外汇市场收盘价
	function get_qh_data($sdate,$edate,$types){
		$sql = "select dta_type,dta_6,dta_ym from data_table where dta_maxValStatus=1 and dta_type in (".$types.") and dta_ym = '".$edate."'";
		// echo $sql;
		$dataArray = $this->drcdao->query( $sql );
		$type_dataArray = array();
		foreach ( $dataArray as $key => $value ){
			$type_dataArray [$value ['dta_type']] = $value ['dta_6'];
		}

		$sql2 = "select dta_type,dta_6,dta_ym from data_table where dta_maxValStatus=1 and dta_type in (".$types.") and dta_ym ='".$sdate."'";
		// echo $sql2;
		$dataArray2 = $this->drcdao->query( $sql2 );

		$type_dataArray2 = array();
		foreach ( $dataArray2 as $key => $value ){
			$type_dataArray2 [$value ['dta_type']] = $value ['dta_6'];
		}
		$ret_data = array();
		foreach ( $type_dataArray2 as $pkey => $pvalue ){
			$ret_data ['tprice'] [$pkey] = $type_dataArray [$pkey];
			$ret_data ['lprice'] [$pkey] = $pvalue;
			if($type_dataArray [$pkey] == "" || $pvalue == ""){
				$ret_data ['zd'] [$pkey] = "0";
			}else{
				$ret_data ['zd'] [$pkey] = $this->zhangdie ( $type_dataArray [$pkey] - $pvalue );
			}
			// $ret_data ['zd2'] [$pkey] = $pvalue - $type_dataArray2 [$pkey];
			// $ret_data ['zd3'] [$pkey] = $this->zhangdie3 ( $pvalue - $type_dataArray2 [$pkey] );
			// $ret_data ['zd4'] [$pkey] = $this->zhangdie4 ( $pvalue - $type_dataArray2 [$pkey] );
		}
		if(empty($ret_data)){
			foreach ( $type_dataArray as $pkey => $pvalue ){
				$ret_data ['zd'] [$pkey] = "0";
				$ret_data ['tprice'] [$pkey] = $pvalue;
				$ret_data ['lprice'] [$pkey] = $type_dataArray2 [$pkey];
			}
		}
		return $ret_data;
	}

	function getTksShpi($sdate,$edate,$sdate2,$edate2){
		$yy_sql = "select FORMAT(avg(weipriceusb),2) as weipriceusb from shpi_material_pzp WHERE vid = 3 and dateday >= '".$edate."' and dateday <= '".$sdate."'";
		$dataArray = $this->maindao->getRow( $yy_sql );
		
		$yy_sql2 = "select FORMAT(avg(weipriceusb),2) as weipriceusb from shpi_material_pzp where vid = 3 and dateday >= '".$edate2."' and dateday <= '".$sdate2."'";
		$dataArray2 = $this->maindao->getRow( $yy_sql2 );
		
		$ret_dataArray = array();
        if( $dataArray2 ["weipriceusb"] == "" || $dataArray["weipriceusb"] == ""){
            $ret_dataArray ['zd'] = "0";
        }else{
            $ret_dataArray ['zd'] = $this->zhangdie ( $dataArray["weipriceusb"] - $dataArray2 ["weipriceusb"] );
        }
		$ret_dataArray ['tprice'] = $dataArray["weipriceusb"];
		$ret_dataArray ['lprice'] = $dataArray2 ["weipriceusb"];
		//print_r($dataArray2);
		return $ret_dataArray;
	}

	function getNiuYuanyou($sdate,$edate,$bianma){
		$yy_sql = "select * from gfutures_shpi where bianma= '".$bianma."' and datetime = '".$edate."'";
		$dataArray = $this->drcdao->getRow( $yy_sql );
		
		$yy_sql2 = "select * from gfutures_shpi where bianma= '".$bianma."' and datetime = '".$sdate."'";
		$dataArray2 = $this->drcdao->getRow( $yy_sql2 );
		
		$ret_dataArray = array();
        if( $dataArray2 ["mvalue"] == "" || $dataArray["mvalue"] == ""){
            $ret_dataArray ['zd'] = "0";
        }else{
            $ret_dataArray ['zd'] = $this->zhangdie ( $dataArray["mvalue"] - $dataArray2 ["mvalue"] );
        }
		$ret_dataArray ['tprice'] = $dataArray["mvalue"];
		$ret_dataArray ['lprice'] = $dataArray2 ["mvalue"];
		//print_r($dataArray2);
		return $ret_dataArray;
	}

	function getMeiYuanShpi($sdate,$edate){
		$sql = "select * from dolrate where rdate = '".$edate." 00:00:00'";
		$dataArray = $this->drcdao->getRow( $sql );

		$sql2 = "select * from dolrate where rdate = '".$sdate." 00:00:00'";
		$dataArray2 = $this->drcdao->getRow( $sql2 );
		
		$ret_dataArray = array();
		$zhangdie = round($dataArray["rd2"] - $dataArray2 ["rd2"],4);
		$intstr = "";
		if($zhangdie<0){
			$intstr = "<font color=green><strong>↓".$zhangdie."</strong></font>";
		}elseif($zhangdie>0){
			$intstr = "<font color=red><strong>↑".$zhangdie."</strong></font>";
		}elseif($zhangdie=="" || $zhangdie==0){
			$intstr = "<strong>--</strong>";
		}else{
			$intstr = "<font ><strong>".$zhangdie."</strong></font>";
		}
		$ret_data ['zd'] = $intstr;
		$ret_data ['tprice'] = $dataArray["rd2"];
		$ret_data ['lprice'] = $dataArray2["rd2"];
		//print_r($ret_data);
		return $ret_data;
	}

	function getRMBShpi($sdate,$edate){
		$sql = "select * from rmbrate where rdate = '".$edate." 00:00:00'";
		$dataArray = $this->drcdao->getRow( $sql );

		$sql2 = "select * from rmbrate where rdate = '".$sdate." 00:00:00'";
		$dataArray2 = $this->drcdao->getRow( $sql2 );
		
		$ret_dataArray = array();
		$ret_data ['zd'] = $this->zhangdie ( $dataArray["rd1"] - $dataArray2 ["rd1"] );
		$ret_data ['tprice'] = $dataArray["rd1"];
		$ret_data ['lprice'] = $dataArray2["rd1"];
		return $ret_data;
	}

	 //获取六位价格id价格
	 function hq_price_6($stime, $etime, $topicture,$stime2, $etime2){
         $tprice = [];
         $lprice = [];
		$sql = "select price,topicture,mconmanagedate from marketconditions where topicture in (" . $topicture . ") AND mconmanagedate >='" . $etime . " 00:00:00' AND mconmanagedate <='" . $stime . "  23:59:59' and isview=0 order by topicture asc ";
//         echo $sql;
		$query = $this->maindao->query( $sql );
		// echo "<pre/>";print_r($query);
		foreach ( $query as $key => $value ){
			if (strstr ( $value ['price'], "-" )){
				$avgprice = explode ( "-", $value ['price'] );
				$value ['price'] = round ( ($avgprice ['0'] + $avgprice ['1']) / 2, 2 );
			}

			$tprice [$value ['topicture']][0] += $value ['price'];
			$tprice [$value ['topicture']][1] ++;
		}
		$sql2 = "select price,topicture,mconmanagedate from marketconditions where topicture in (" . $topicture . ") AND mconmanagedate >='" . $etime2 . " 00:00:00' AND mconmanagedate <='" . $stime2 . "  23:59:59' and isview=0 order by topicture asc ";
		// echo $sql2;
		$query2 = $this->maindao->execute( $sql2 );

		foreach ( $query2 as $key2 => $value2 ){
			if (strstr ( $value2 ['price'], "-" )){
				$avgprice = explode ( "-", $value2 ['price'] );
				$value2 ['price'] = round ( ($avgprice ['0'] + $avgprice ['1']) / 2, 2 );
			}

			// $lprice [$value2 ['topicture']] = $value2 ['price'];
			$lprice [$value2 ['topicture']][0] += $value2 ['price'];
			$lprice [$value2 ['topicture']][1] ++;
		}
		// echo "<pre/>";print_r($lprice);
		foreach ( $tprice as $pkey => $pvalue ){
			// $price_arr ['zd'] [$pkey] = $this->zhangdie ( $pvalue - $lprice [$pkey] );
			// $price_arr ['zd2'] [$pkey] = $pvalue - $lprice [$pkey];
            // $price_arr ['zd3'] [$pkey] = $this->zhangdie3 ( $pvalue - $lprice [$pkey] );
            // $price_arr ['zd4'] [$pkey] = $this->zhangdie4 ( $pvalue - $lprice [$pkey] );
			if($pvalue[1] != "" && $pvalue[1] != 0){
				$price_arr ['tprice'] [$pkey] = round($pvalue[0] / $pvalue[1]);
			}
			if($lprice [$pkey][1] != "" && $lprice [$pkey][1] != 0){
				$price_arr ['lprice'] [$pkey] = round($lprice [$pkey][0] / $lprice [$pkey][1]);
			}
            $price_arr ['zd'] [$pkey] = $this->zhangdie ( $price_arr ['tprice'] [$pkey] - $price_arr ['lprice'] [$pkey] );
            $price_arr ['zd2'] [$pkey] =  $price_arr ['tprice'] [$pkey] - $price_arr ['lprice'] [$pkey] ;
		}
		return $price_arr;
	}

    //获取六位价格id价格
    function get_price($stime, $etime, $topicture,$stime2="", $etime2=""){
        $tPrice = [];
        $lPrice = [];

        $topicture6 = array();
        $mastertopid7 = array();
        foreach ($topicture as $item){
            if(strlen($item)==6){
                $topicture6[] = $item;
            }else{
                $mastertopid7[] = $item;
            }
        }
        $topicture = implode("','",$topicture6);
        $mastertopid = implode("','",$mastertopid7);
        if(empty($mastertopid7)){
            $sql = " select price,topicture,mconmanagedate from marketconditions where topicture in ('" . $topicture . "') AND mconmanagedate >='" . $stime . " 00:00:00' AND mconmanagedate <='" . $etime . "  23:59:59' and isview=0";
        }else if(empty($topicture6)){
            $sql = " select price,mastertopid as topicture,mconmanagedate from marketconditions where mastertopid in ('" . $mastertopid . "')  AND mconmanagedate >='" . $stime . " 00:00:00' AND mconmanagedate <='" . $etime . "  23:59:59' and isview=0";
        }else{
            $sql = " (select price,topicture,mconmanagedate from marketconditions where topicture in ('" . $topicture . "') AND mconmanagedate >='" . $stime . " 00:00:00' AND mconmanagedate <='" . $etime . "  23:59:59' and isview=0) UNION (select price,mastertopid as topicture,mconmanagedate from marketconditions where mastertopid in ('" . $mastertopid . "') AND mconmanagedate >='" . $stime . " 00:00:00' AND mconmanagedate <='" . $etime . "  23:59:59' and isview=0)  ";
        }
        //echo $sql."<br>";
        $query = $this->maindao->query( $sql );
        foreach ( $query as $key => $value ){
            if (strstr ( $value['price'], "-" )){
                $avgprice = explode ( "-", $value['price'] );
                $value['price'] = round ( ($avgprice['0'] + $avgprice['1']) / 2, 2 );
            }
            if (strstr ( $value['oldprice'], "-" )){
                $avgprice = explode ( "-", $value['oldprice'] );
                $value['oldprice'] = round ( ($avgprice ['0'] + $avgprice['1']) / 2, 2 );
            }

            $tPrice[$value['topicture']][0] += $value['price'];
            $tPrice[$value['topicture']][1]++;
        }

        if($stime2!="" && $etime2!="") {
            if(empty($mastertopid7)) {
                $sql2 = " select price,topicture,mconmanagedate from marketconditions where topicture in ('" . $topicture . "') AND mconmanagedate >='" . $stime2 . " 00:00:00' AND mconmanagedate <='" . $etime2 . "  23:59:59' and isview=0 ";
            }else if(empty($topicture6)){
                $sql2 = " select price,mastertopid as topicture,mconmanagedate from marketconditions where mastertopid in ('" . $mastertopid . "')  AND mconmanagedate >='" . $stime2 . " 00:00:00' AND mconmanagedate <='" . $etime2 . "  23:59:59' and isview=0";
            }else {
                $sql2 = " (select price,topicture,mconmanagedate from marketconditions where topicture in ('" . $topicture . "') AND mconmanagedate >='" . $stime2 . " 00:00:00' AND mconmanagedate <='" . $etime2 . "  23:59:59' and isview=0) UNION (select price,mastertopid as topicture,mconmanagedate from marketconditions where mastertopid in ('" . $mastertopid . "') AND mconmanagedate >='" . $stime2 . " 00:00:00' AND mconmanagedate <='" . $etime2 . "  23:59:59' and isview=0)";
            }
            $query2 = $this->maindao->execute($sql2);
            foreach ($query2 as $key2 => $value2) {
                if (strstr($value2 ['price'], "-")) {
                    $avgprice = explode("-", $value2['price']);
                    $value2['price'] = round(($avgprice['0'] + $avgprice['1']) / 2, 2);
                }
                $lPrice[$value2['topicture']][0] += $value2['price'];
                $lPrice[$value2['topicture']][1]++;
            }
        }

        $price_arr = array();
        foreach ( $tPrice as $pkey => $pvalue ){
            if($pvalue[1] != "" && $pvalue[1] != 0){
                $price_arr[$pkey]['tprice'] = round($pvalue[0] / $pvalue[1]);
            }
            if(isset($lPrice[$pkey])) {
                if ($lPrice[$pkey][1] != "" && $lPrice[$pkey][1] != 0) {
                    $price_arr[$pkey]['lprice'] = round($lPrice[$pkey][0] / $lPrice[$pkey][1]);
                    $price_arr[$pkey]['zd'] = bcsub($price_arr[$pkey]['tprice'],$price_arr[$pkey]['lprice']);
                }
            }
        }
        return $price_arr;
    }
    //获取六位价格id价格均价
    function get_avgprice($stime, $etime, $topicture,$stime2="", $etime2=""){
        $tPrice = [];
        $lPrice = [];

        $topicture6 = array();
        $mastertopid7 = array();
        foreach ($topicture as $item){
            if(strlen($item)==6){
                $topicture6[] = $item;
            }else{
                $mastertopid7[] = $item;
            }
        }
        $topicture = implode("','",$topicture6);
        $mastertopid = implode("','",$mastertopid7);
        if(empty($mastertopid7)){
            $sql = " select price,topicture,mconmanagedate from marketconditions where topicture in ('" . $topicture . "') AND mconmanagedate >='" . $stime . " 00:00:00' AND mconmanagedate <='" . $etime . "  23:59:59' and isview=0";
        }else if(empty($topicture6)){
            $sql = " select price,mastertopid as topicture,mconmanagedate from marketconditions where mastertopid in ('" . $mastertopid . "')  AND mconmanagedate >='" . $stime . " 00:00:00' AND mconmanagedate <='" . $etime . "  23:59:59' and isview=0";
        }else{
            $sql = " (select price,topicture,mconmanagedate from marketconditions where topicture in ('" . $topicture . "') AND mconmanagedate >='" . $stime . " 00:00:00' AND mconmanagedate <='" . $etime . "  23:59:59' and isview=0) UNION (select price,mastertopid as topicture,mconmanagedate from marketconditions where mastertopid in ('" . $mastertopid . "') AND mconmanagedate >='" . $stime . " 00:00:00' AND mconmanagedate <='" . $etime . "  23:59:59' and isview=0)  ";
        }
        //echo $sql."<br>";
        $query = $this->maindao->query( $sql );
        foreach ( $query as $key => $value ){
            if (strstr ( $value['price'], "-" )){
                $avgprice = explode ( "-", $value['price'] );
                $value['price'] = round ( bcadd((float)$avgprice['0'],(float)$avgprice['1']) / 2, 2 );
            }

            $tPrice[0] += (float)$value['price'];
            $tPrice[1]++;
        }

        if($stime2!="" && $etime2!="") {
            if(empty($mastertopid7)) {
                $sql2 = " select price,topicture,mconmanagedate from marketconditions where topicture in ('" . $topicture . "') AND mconmanagedate >='" . $stime2 . " 00:00:00' AND mconmanagedate <='" . $etime2 . "  23:59:59'  and isview=0";
            }else if(empty($topicture6)){
                $sql2 = " select price,mastertopid as topicture,mconmanagedate from marketconditions where mastertopid in ('" . $mastertopid . "')  AND mconmanagedate >='" . $stime2 . " 00:00:00' AND mconmanagedate <='" . $etime2 . "  23:59:59' and isview=0";
            }else {
                $sql2 = " (select price,topicture,mconmanagedate from marketconditions where topicture in ('" . $topicture . "') AND mconmanagedate >='" . $stime2 . " 00:00:00' AND mconmanagedate <='" . $etime2 . "  23:59:59' and isview=0) UNION (select price,mastertopid as topicture,mconmanagedate from marketconditions where mastertopid in ('" . $mastertopid . "') AND mconmanagedate >='" . $stime2 . " 00:00:00' AND mconmanagedate <='" . $etime2 . "  23:59:59' and isview=0)";
            }
            $query2 = $this->maindao->execute($sql2);
            foreach ($query2 as $key2 => $value2) {
                if (strstr($value2 ['price'], "-")) {
                    $avgprice = explode("-", $value2['price']);
                    $value2['price'] = round(bcadd((float)$avgprice['0'],(float)$avgprice['1']) / 2, 2);
                }
                $lPrice[0] += (float)$value2['price'];
                $lPrice[1]++;
            }
        }

        $price_arr = array();
        if($tPrice[1] != "" && $tPrice[1] != 0){
            $price_arr['tprice'] = round($tPrice[0] / $tPrice[1]);
        }
        if(isset($lPrice)) {
            if ($lPrice[1] != "" && $lPrice[1] != 0) {
                $price_arr['lprice'] = round($lPrice[0] / $lPrice[1]);
                $price_arr['zd'] = bcsub($price_arr['tprice'],$price_arr['lprice']);
            }
        }
        return $price_arr;
    }
    //获取六位价格id一段时间的所有价格
    function getPrice_AllDate($stime, $etime, $topicture){
        $tPrice = [];

        $topicture = implode("','",$topicture);
        $sql = "select price,oldprice,topicture,mconmanagedate from marketconditions where topicture in ('" . $topicture . "') AND mconmanagedate >='" . $stime . " 00:00:00' AND mconmanagedate <='" . $etime . "  23:59:59' and isview=0 order by mconmanagedate ASC ";
        $query = $this->maindao->query( $sql );

        foreach ( $query as $key => $value ){
            $mconmanagedate = date("j",strtotime($value['mconmanagedate']));
            if (strstr ( $value ['price'], "-" )){
                $avgprice = explode ( "-", $value ['price'] );
                $value ['price'] = round ( ($avgprice ['0'] + $avgprice ['1']) / 2, 2 );
            }

            $tPrice [$value ['topicture']][$mconmanagedate] = $value ['price'];

        }

        return $tPrice;
    }
    function get_mysteel_AllDate($bigtype,$stime, $etime ){
        $tPrice = [];

        $sql = " select dta1,dta2,dta3,dta4,`date` from sd_steel_data_table_base base,sd_steel_data_table detail where base.id=detail.baseid and bigtype='".$bigtype."' and isdel=0 and date >='" . $stime . " 00:00:00' AND date <='" . $etime . "  23:59:59' ";
        $query = $this->drcdao->query( $sql );
        foreach ( $query as $key => $value ){
            $date = date("j",strtotime($value['date']));
            $toPicture = $value['dta2'].$value['dta3'];
            $tPrice[$toPicture][$date] = $value['dta4'];
        }

        return $tPrice;
    }
    //获取我的钢铁价格
    function get_mysteel_avgprice($bigtype,$stime, $etime ,$stime2="", $etime2=""){
        $tPrice = [];
        $lPrice = [];

        $sql = " select dta1,dta2,dta3,avg(dta4) as dta4 from sd_steel_data_table_base base,sd_steel_data_table detail where base.id=detail.baseid and bigtype='".$bigtype."' and isdel=0 and dta4!='' and date >='" . $stime . " 00:00:00' AND date <='" . $etime . "  23:59:59' group by dta2,dta3 ";
        $query = $this->drcdao->query( $sql );
        foreach ( $query as $key => $value ){
            $toPicture = $value['dta2'].$value['dta3'];
            $tPrice[$toPicture] = round($value['dta4']);
        }

        if($stime2!="" && $etime2!="") {
            $sql2 = " select dta1,dta2,dta3,avg(dta4) as dta4 from sd_steel_data_table_base base,sd_steel_data_table detail where base.id=detail.baseid and bigtype='".$bigtype."' and isdel=0 and dta4!='' and date >='" . $stime2 . " 00:00:00' AND date <='" . $etime2 . "  23:59:59' group by dta2,dta3 ";
            $query2 = $this->drcdao->execute($sql2);
            foreach ($query2 as $key2 => $value2) {
                $toPicture = $value2['dta2'].$value2['dta3'];
                $lPrice[$toPicture] = round($value2['dta4']);
            }
        }

        $price_arr = array();
        foreach ( $tPrice as $pkey => $pvalue ){
            $price_arr[$pkey]['tprice'] = $pvalue;
            if(isset($lPrice[$pkey])) {
                $price_arr[$pkey]['lprice'] = $lPrice[$pkey];
                $price_arr[$pkey]['zd'] = bcsub($price_arr[$pkey]['tprice'],$price_arr[$pkey]['lprice']);
            }
        }
        return $price_arr;
    }
	//获取七位价格id价格
	function hq_price_7_week($stime, $etime, $mastertopid,$stime2, $etime2){
		$sql = "select price,mastertopid,mconmanagedate from marketconditions where mastertopid in (" . $mastertopid . ") AND mconmanagedate >'" . $etime . " 00:00:00' AND mconmanagedate <'" . $stime . "  23:59:59' order by mastertopid asc ";

		$query = $this->maindao->query( $sql );
		foreach ( $query as $key => $value ){
			if (strstr ( $value ['price'], "-" )){
				$avgprice = explode ( "-", $value ['price'] );
				$value ['price'] = round ( ($avgprice ['0'] + $avgprice ['1']) / 2, 2 );
			}

			// $tprice [$value ['mastertopid']] = $value ['price'];
			$tprice [$value ['mastertopid']][0] += $value ['price'];
			$tprice [$value ['mastertopid']][1] ++;
		}
		// echo "<pre>";print_r($tprice);
		$sql2 = "select price,mastertopid,mconmanagedate from marketconditions where mastertopid in (" . $mastertopid . ") AND mconmanagedate >'" . $etime2 . " 00:00:00' AND mconmanagedate <'" . $stime2 . "  23:59:59' order by mastertopid asc ";
		$query2 = $this->maindao->execute ( $sql2 );
		foreach ( $query2 as $key2 => $value2 ){
			if (strstr ( $value2 ['price'], "-" )){
				$avgprice = explode ( "-", $value2 ['price'] );
				$value2 ['price'] = round ( ($avgprice ['0'] + $avgprice ['1']) / 2, 2 );
			}

			// $lprice [$value2 ['mastertopid']] = $value2 ['price'];
			$lprice [$value2 ['mastertopid']][0] += $value2 ['price'];
			$lprice [$value2 ['mastertopid']][1] ++;
		}
		
        foreach ( $lprice as $pkey => $pvalue ){
			if($tprice [$pkey][1] != "" && $tprice [$pkey][1] != 0){
                if($pkey == "H986401"){
                    $price_arr ['tprice'] [$pkey] = round(($tprice [$pkey][0] / $tprice [$pkey][1]),2);
                }else{
                    $price_arr ['tprice'] [$pkey] = round($tprice [$pkey][0] / $tprice [$pkey][1]);
                }
				
			}
			if($pvalue[1] != "" && $pvalue[1] != 0){
                if($pkey == "H986401"){
                    $price_arr ['lprice'] [$pkey] = round(($pvalue[0] / $pvalue[1]),2);
                }else{
                    $price_arr ['lprice'] [$pkey] = round($pvalue[0] / $pvalue[1]);
                }
			}
		}

		// foreach ( $tprice as $pkey => $pvalue ){
			// $price_arr ['zd'] [$pkey] = $this->zhangdie ( $pvalue - $lprice [$pkey] );
			// $price_arr ['zd2'] [$pkey] = $pvalue - $lprice [$pkey];
            // $price_arr ['zd3'] [$pkey] = $this->zhangdie3 ( $pvalue - $lprice [$pkey] );
            // $price_arr ['zd4'] [$pkey] = $this->zhangdie4 ( $pvalue - $lprice [$pkey] );
			// $price_arr ['tprice'] [$pkey] = $pvalue;
			// $price_arr ['lprice'] [$pkey] = $lprice [$pkey];
			// if($pvalue[1] != "" && $pvalue[1] != 0){
            //     if($pkey == "H986401"){
            //         $price_arr ['tprice'] [$pkey] = round(($pvalue[0] / $pvalue[1]),2);
            //     }else{
            //         $price_arr ['tprice'] [$pkey] = round($pvalue[0] / $pvalue[1]);
            //     }
				
			// }
			// if($lprice [$pkey][1] != "" && $lprice [$pkey][1] != 0){
            //     if($pkey == "H986401"){
            //         $price_arr ['lprice'] [$pkey] = round(($lprice [$pkey][0] / $lprice [$pkey][1]),2);
            //     }else{
            //         $price_arr ['lprice'] [$pkey] = round($lprice [$pkey][0] / $lprice [$pkey][1]);
            //     }
			// }
		// }
		return $price_arr;
	}

	public function saving_marketNews($params){
        $type = $params['type'];
        $sdate = $params['sdate'];
        $edate = $params['edate'];
        $adminid = $params['adminid'];
        $news_type = $params['news_type'];
        $content = $params['content'];

        if(!isset($_SESSION['adminid'])){
            $_SESSION['adminid'] = $adminid;
        }
        $content = str_replace('EN-US','',$content);
        $content = str_replace("br","/p><p",$content);
        $content = str_replace('MsoNormal','',$content);


        $sql = "select * from sd_steel_marketNews where type = '".$type."' and CDate >= '".$sdate."' and CDate <= '".$edate."' and news_type = '".$news_type."'";
        $marketNews = $this->_dao->getRow($sql);
        if(empty($marketNews)){
            $content = "<p>".$content."</p>";
            $insert_sql = "insert into sd_steel_marketNews set type = '".$type."' , CDate = '".$edate."', news_type = '".$news_type."',content = '".$content."',create_time=now(),update_time=now(),adminid='".$_SESSION['adminid']."' ";
            $this->_dao->execute($insert_sql);
        }else{
            $update_sql = "update sd_steel_marketNews set content = '".$content."',update_time=now(),updateAdminid='".$_SESSION['adminid']."' where id='".$marketNews['id']."' ";
            $this->_dao->execute($update_sql);
        }
        $info = array("code"=>"1");
        echo json_encode($info);
        exit;
    }

    public function saving_marketNews2($params){
        //echo "<pre>";
        //print_r($params);
        //exit;
        $type = $params['type'];
        $sdate = $params['sdate'];
        $edate = $params['edate'];
        $adminid = $params['adminid'];
        $news_type = $params['news_type'];
        if(!isset($_SESSION['adminid'])){
            $_SESSION['adminid'] = $adminid;
        }

        foreach ($news_type as $key=>$tmp){
            $tmp = str_replace("EN-US","",$tmp);
            $tmp = str_replace("br","/p><p",$tmp);
            $tmp = str_replace("MsoNormal","",$tmp);

            $sql = "select * from sd_steel_marketNews where type = '".$type."' and CDate >= '".$sdate."' and CDate <= '".$edate."' and news_type = '".$key."'";
            $marketNews = $this->_dao->getRow($sql);
            if(empty($marketNews)){
                $tmp = "<p>".$tmp."</p>";
                $insert_sql = "insert into sd_steel_marketNews set type = '".$type."' , CDate = '".$edate."', news_type = '".$key."',content = '".$tmp."',create_time=now(),update_time=now(),adminid='".$_SESSION['adminid']."' ";
                $this->_dao->execute($insert_sql);
            }else{
                $update_sql = "update sd_steel_marketNews set content = '".$tmp."',update_time=now(),updateAdminid='".$_SESSION['adminid']."' where id='".$marketNews['id']."' ";
                $this->_dao->execute($update_sql);
            }
        }

        alert("保存成功！");
        goback();
        exit;
    }

    //获取截止当前日期工作日数量
    public function get_gzr_num($edate){
        $gzr_num = 0;
        while(true){
            if(file_get_contents('https://holiday.steelhome.com/isholiday.php?date='.$edate)=="0")
            { 
                $gzr_num++;
            }
            if(date('j',strtotime($edate)) == "1"){
                break;
            }else{
                $edate=date('Y-m-d',strtotime($edate . '-1 day'));
            }
        }
        return $gzr_num;
    }

    public function yue_market_price($params){
        //print_r($this->maindao);
        //$params["sdate"] = "2024-05-01";
        //$params["edate"] = "2024-05-31";
        if($params['saving']=="2"){
            $params['saving']="1";
        }
        $sdate = isset($params["sdate"]) == true?$params["sdate"]:date("Y-m-01");
        $edate = isset($params["edate"]) == true?$params["edate"]:date("Y-m-d");

        $edate_t = date("Y-m-t",strtotime($edate));

        if($params["titleName"] == ""){
            $params["titleName"] = date("Y年n月",strtotime($edate))."月定价";
        }
        //上个月
        $edate2 = date("Y-m-d",strtotime($sdate."-1 day"));
        $sdate2 = date("Y-m-01",strtotime($edate2));

        //根据当前日获取上个钢之家工作日
        $trade_day = $this->get_xhdate4($edate);
        $trade_day2 = $this->get_xhdate4($edate2);


        $this->assign("date_mdd",date("n月j-",strtotime($sdate)).date("j日",strtotime($edate)));
        $this->assign("date_m",date("n月",strtotime($edate)));
        $this->assign("date_m2",date("n月",strtotime($edate2)));

        //我的钢铁价格
        $MS_Price = $this->get_mysteel_avgprice("1",$sdate,$edate,$sdate2,$edate2);
        $MS_Price_Day = $this->get_mysteel_avgprice("1",$trade_day,$trade_day,$trade_day2,$trade_day2);
        //$MS_Price_all = $this->get_mysteel_AllDate("1",$sdate,$edate);

        //市场成交价
        $Market_Price = $this->get_mysteel_avgprice("2",$sdate,$edate,$sdate2,$edate2);
        $Market_Price_Day = $this->get_mysteel_avgprice("2",$trade_day,$trade_day,$trade_day2,$trade_day2);
        //$Market_Price_all = $this->get_mysteel_AllDate("2",$sdate,$edate);

        //结算价
        $Settlement_Price = $this->get_mysteel_avgprice("4",$sdate,$edate,$sdate2,$edate2);
        $Settlement_Price_Day = $this->get_mysteel_avgprice("4",$trade_day,$trade_day,$trade_day2,$trade_day2);

        //主要城市H型钢价格======started
        $topicture_H = array(
            "697311","827311","677311",
            "69731V","82731V","67731V"
        );
        $topicture_H2 = array(
            "Q235B 200*200mm莱芜","Q235B 200*200mm泰安","Q235B 200*200mm唐山",
            "Q235B 600*200mm莱芜","Q235B 600*200mm泰安","Q235B 600*200mm唐山"
        );

        $price_H = $this->get_price($sdate,$edate,$topicture_H,$sdate2,$edate2);

        //网站平均
        $avgPrice_H = array();
        foreach ($topicture_H as $key=>$item){
            $key2 = $topicture_H2[$key];
            $avgPrice_H[] = round(bcadd($price_H[$item]['tprice'],$MS_Price[$key2]['tprice'])/2);
            $avgPrice_H[] = round(bcadd($price_H[$item]['lprice'],$MS_Price[$key2]['lprice'])/2);
            $avgPrice_H[] = $this->zhangdie(round(bcadd($price_H[$item]['zd'],$MS_Price[$key2]['zd'])/2));
        }
        //主要城市H型钢价格======ended

        //主要城市特钢价格======started
        $topicture_TG = array("692243","082244","692154","122154","082154");
        $topicture_TG2 = array(
            "45#莱芜","45#杭州（南钢）",
            "20CrMnTi莱芜","20CrMnTi无锡","20CrMnTi杭州"
        );
        $price_TG = $this->get_price($sdate,$edate,$topicture_TG,$sdate2,$edate2);

        //网站平均
        $avgPrice_TG = array();
        foreach ($topicture_TG as $key=>$item){
            $key2 = $topicture_TG2[$key];
            $avgPrice_TG[] = round(bcadd($price_TG[$item]['tprice'],$MS_Price[$key2]['tprice'])/2);
            $avgPrice_TG[] = round(bcadd($price_TG[$item]['lprice'],$MS_Price[$key2]['lprice'])/2);
            $avgPrice_TG[] = $this->zhangdie(round(bcadd($price_TG[$item]['zd'],$MS_Price[$key2]['zd'])/2));
        }
        //主要城市特钢价格======ended

        //主要城市热轧价格======started
        $topicture_RZ = array(
            "693117","D23112","823112","873117","073112",
            "693122","D23122","823122","873122","073122","223117","403112","463112");
        $price_RZ = $this->get_price($sdate,$edate,$topicture_RZ,$sdate2,$edate2);

        $topicture_RZ1 = array(
            "693117","D23112","823112","873117","073112",
            "693122","D23122","823122","873122","073122"
        );
        $topicture_RZ2 = array(
            "Q235B 4.75mm莱芜","Q235B 4.75mm日照","Q235B 4.75mm泰安","Q235B 4.75mm临沂","Q235B 4.75mm上海",
            "Q355B 4.75mm莱芜","Q355B 4.75mm日照","Q355B 4.75mm泰安","Q355B 4.75mm临沂","Q355B 4.75mm上海"
        );

        $topicture_RZ1_1 = array(
            "693117","D23112","823112","873117"
        );
        $topicture_RZ2_1 = array(
            "Q235B 4.75mm莱芜","Q235B 4.75mm日照","Q235B 4.75mm泰安","Q235B 4.75mm临沂"
        );

        $topicture_RZ1_2 = array(
            "D23122","073122","223117","403112","463112"
        );
        $topicture_RZ2_2 = array(
            "Q235B 4.75mm日照","Q235B 4.75mm上海","Q235B 4.75mm合肥","Q235B 4.75mm天津","Q235B 4.75mm鞍山"
        );

        //网站平均
        $avgPrice_RZ = array();
        foreach ($topicture_RZ1 as $key=>$item){
            $key2 = $topicture_RZ2[$key];
            $avgPrice_RZ[] = round(bcadd($price_RZ[$item]['tprice'],$MS_Price[$key2]['tprice'])/2);
            $avgPrice_RZ[] = round(bcadd($price_RZ[$item]['lprice'],$MS_Price[$key2]['lprice'])/2);
            $avgPrice_RZ[] = $this->zhangdie(round(bcadd($price_RZ[$item]['zd'],$MS_Price[$key2]['zd'])/2));
        }
        $avgPrice_RZ_1 = array();
        foreach ($topicture_RZ1_1 as $key=>$item){
            $key2 = $topicture_RZ2_1[$key];
            $avgPrice_RZ_1[] = round(bcadd($price_RZ[$item]['tprice'],$MS_Price[$key2]['tprice'])/2);
            $avgPrice_RZ_1[] = round(bcadd($price_RZ[$item]['lprice'],$MS_Price[$key2]['lprice'])/2);
            $avgPrice_RZ_1[] = $this->zhangdie(round(bcadd($price_RZ[$item]['zd'],$MS_Price[$key2]['zd'])/2));
        }
        $avgPrice_RZ_2 = array();
        foreach ($topicture_RZ1_2 as $key=>$item){
            $key2 = $topicture_RZ2_2[$key];
            $avgPrice_RZ_2[] = round(bcadd($price_RZ[$item]['tprice'],$MS_Price[$key2]['tprice'])/2);
            $avgPrice_RZ_2[] = round(bcadd($price_RZ[$item]['lprice'],$MS_Price[$key2]['lprice'])/2);
            $avgPrice_RZ_2[] = $this->zhangdie(round(bcadd($price_RZ[$item]['zd'],$MS_Price[$key2]['zd'])/2));
        }
        //主要城市热轧价格======ended

        //主要城市冷轧价格======started
        $topicture_LZ = array(
            "694312","D24312","8743123",
            "D24312","8743123","0743121","4043123","2842121","464212"
        );
        $price_LZ = $this->get_price($sdate,$edate,$topicture_LZ,$sdate2,$edate2);

        $topicture_LZ1 = array(
            "694312","D24312","8743123"
        );
        $topicture_LZ2 = array(
            "冷轧1500莱芜","冷轧1500日照","冷轧1500临沂"
        );

        $topicture_LZ1_1 = array(
            "D24312","8743123","0743121","4043123","2842121","464212"
        );
        $topicture_LZ2_1 = array(
            "冷轧2030日照","冷轧2030临沂","冷轧2030上海",
            "冷轧2030天津","冷轧2030广州","冷轧2030鞍山"
        );

        //网站平均
        $avgPrice_LZ = array();
        foreach ($topicture_LZ1 as $key=>$item){
            $key2 = $topicture_LZ2[$key];
            $avgPrice_LZ[] = round(bcadd($price_LZ[$item]['tprice'],$MS_Price[$key2]['tprice'])/2);
            $avgPrice_LZ[] = round(bcadd($price_LZ[$item]['lprice'],$MS_Price[$key2]['lprice'])/2);
            $avgPrice_LZ[] = $this->zhangdie(round(bcadd($price_LZ[$item]['zd'],$MS_Price[$key2]['zd'])/2));
        }
        $avgPrice_LZ_1 = array();
        foreach ($topicture_LZ1_1 as $key=>$item){
            $key2 = $topicture_LZ2_1[$key];
            $avgPrice_LZ_1[] = round(bcadd($price_LZ[$item]['tprice'],$MS_Price[$key2]['tprice'])/2);
            $avgPrice_LZ_1[] = round(bcadd($price_LZ[$item]['lprice'],$MS_Price[$key2]['lprice'])/2);
            $avgPrice_LZ_1[] = $this->zhangdie(round(bcadd($price_LZ[$item]['zd'],$MS_Price[$key2]['zd'])/2));
        }
        //主要城市冷轧价格======ended

        //每日价格变化表日期
        $num_1 = array("1","2","3","4","5","6","7","8","9","10","11","12","13","14");//日价格变化表第一行日期
        $num_2 = array("15","16","17","18","19","20","21","22","23","24","25","26");//日价格变化表第二行日期

        //文字内容
        //$sdgt_scyp = html_entity_decode($this->_dao->get_sdgt_marketNews(3,$sdate,$edate_t,301));//月预测市场回顾

        $schg = html_entity_decode($this->_dao->get_sdgt_marketNews(6,$sdate,$edate_t,13));
        //if($schg==""){
            //8.月定价第一部分本月市场回顾，使用月预测的文字描述部分=本月市场回顾
            //$schg = $sdgt_scyp;
        //}
        $jgtzjy_Hxg = html_entity_decode($this->_dao->get_sdgt_marketNews(6,$sdate,$edate_t,14));
        $jgtzjy_tg = html_entity_decode($this->_dao->get_sdgt_marketNews(6,$sdate,$edate_t,15));
        $jgtzjy_rz = html_entity_decode($this->_dao->get_sdgt_marketNews(6,$sdate,$edate_t,16));
        $jgtzjy_lz = html_entity_decode($this->_dao->get_sdgt_marketNews(6,$sdate,$edate_t,17));

        $curr_yue_gzr = $this->get_gzr_num($edate);
        $curr_yue_mo_date = date("Y-m-d",strtotime($sdate . "+1 month -1 day"));
        $curr_yue_mo_gzr = $this->get_gzr_num($curr_yue_mo_date);
        if($params['saving']=="1"){
            $hxg_ids = "'697311','69731D','697316','692243','692154','827311','697317','827317','827316','693112','693122','692154','694312'";
            $hxg_avg_price_arr = $this->hq_price_6($edate,$sdate,$hxg_ids,$edate2,$sdate2);
            $hxg_price_arr = $this->hq_price_6($trade_day,$trade_day,$hxg_ids,$trade_day,$trade_day);
            //月定价的文字部分 取上一期的,本期的把上期的带过来，然后在上期的基础上修改
            if($schg==""){
                $schg = html_entity_decode($this->_dao->get_sdgt_marketNews(6,$sdate2,$edate2,13));
            }
            if($jgtzjy_Hxg==""){
                // $jgtzjy_Hxg = html_entity_decode($this->_dao->get_sdgt_marketNews(6,$sdate2,$edate2,14));
                //①2月1-24日（月初至24日），莱芜市场200H（ID：697311）、250H（ID：69731D）和350H（ID：697316）均价分别为3356（1-24日均价）元/吨、3396（1-24日均价）元/吨和3426（1-24日均价）元/吨，较1月（上月）均价上涨22元（1-当日均价-上月均价）/吨、上涨22元（1-当日均价-上月均价）/吨和上涨22元（1-当日均价-上月均价）/吨；预计2月均价分别为3359（计算公式：（（1-当日均价）*实际工作日+当日价格*本月剩余工作日）/当月工作日；下同）元/吨、3399（同上）元/吨和3429（同上）元/吨，均较1月（上月）均价上涨24（1-当日均价-上月均价）元/吨、上涨24（1-当日均价-上月均价）元/吨和上涨24（1-当日均价-上月均价）元/吨。②2月1-24日，泰安市场200H（ID：827311）、250H（ID：697317）和350H（ID：697316）均价分别为3567、3447和3447元/吨，分别较1月均价上涨16、18和18元/吨，预计2月均价分别为3568、3448和3448元/吨，分别较1月均价上涨17、19和19元/吨。

                $jgtzjy_Hxg = "①".date("n",strtotime($edate))."月1-24日，莱芜市场200H、250H和350H均价分别为".$hxg_avg_price_arr["tprice"]["697311"]."元/吨、".$hxg_avg_price_arr["tprice"]["69731D"]."元/吨和".$hxg_avg_price_arr["tprice"]["697316"]."元/吨，较".date("n",strtotime($edate2))."月均价".$this->zhangdie6($hxg_avg_price_arr["zd2"]["697311"])."、".$this->zhangdie6($hxg_avg_price_arr["zd2"]["69731D"])."和".$this->zhangdie6($hxg_avg_price_arr["zd2"]["697316"])."；";

                $yc_price1 = round(($hxg_avg_price_arr["tprice"]["697311"] * $curr_yue_gzr + $hxg_price_arr["tprice"]["697311"] * ($curr_yue_mo_gzr - $curr_yue_gzr)) / $curr_yue_mo_gzr);
                $yc_price2 = round(($hxg_avg_price_arr["tprice"]["69731D"] * $curr_yue_gzr + $hxg_price_arr["tprice"]["69731D"] * ($curr_yue_mo_gzr - $curr_yue_gzr)) / $curr_yue_mo_gzr);
                $yc_price3 = round(($hxg_avg_price_arr["tprice"]["697316"] * $curr_yue_gzr + $hxg_price_arr["tprice"]["697316"] * ($curr_yue_mo_gzr - $curr_yue_gzr)) / $curr_yue_mo_gzr);

                $jgtzjy_Hxg .= "预计".date("n",strtotime($edate))."均价分别为".$yc_price1."元/吨、".$yc_price2."元/吨和".$yc_price3."元/吨，均较".date("n",strtotime($edate2))."月均价".$this->zhangdie6($yc_price1 - $hxg_avg_price_arr["lprice"]["697311"])."、".$this->zhangdie6($yc_price2 - $hxg_avg_price_arr["lprice"]["69731D"])."和".$this->zhangdie6($yc_price3 - $hxg_avg_price_arr["lprice"]["697316"])."。②".date("n",strtotime($edate))."月1-24日，泰安市场200H、250H和350H均价分别为".$hxg_avg_price_arr["tprice"]["827311"]."、".$hxg_avg_price_arr["tprice"]["827317"]."和".$hxg_avg_price_arr["tprice"]["827316"]."元/吨，分别较".date("n",strtotime($edate2))."月均价".$this->zhangdie6($hxg_avg_price_arr["zd2"]["827311"])."、".$this->zhangdie6($hxg_avg_price_arr["zd2"]["827317"])."和".$this->zhangdie6($hxg_avg_price_arr["zd2"]["827316"])."，";

                $yc_price4 = round(($hxg_avg_price_arr["tprice"]["827311"] * $curr_yue_gzr + $hxg_price_arr["tprice"]["827311"] * ($curr_yue_mo_gzr - $curr_yue_gzr)) / $curr_yue_mo_gzr);
                $yc_price5 = round(($hxg_avg_price_arr["tprice"]["827317"] * $curr_yue_gzr + $hxg_price_arr["tprice"]["827317"] * ($curr_yue_mo_gzr - $curr_yue_gzr)) / $curr_yue_mo_gzr);
                $yc_price6 = round(($hxg_avg_price_arr["tprice"]["827316"] * $curr_yue_gzr + $hxg_price_arr["tprice"]["827316"] * ($curr_yue_mo_gzr - $curr_yue_gzr)) / $curr_yue_mo_gzr);
                $jgtzjy_Hxg .= "预计".date("n",strtotime($edate))."月均价分别为".$yc_price4."、".$yc_price5."和".$yc_price6."元/吨，分别较".date("n",strtotime($edate2))."月均价".$this->zhangdie6($yc_price4 - $hxg_avg_price_arr["lprice"]["827311"])."、".$this->zhangdie6($yc_price5 - $hxg_avg_price_arr["lprice"]["827317"])."和".$this->zhangdie6($yc_price6 - $hxg_avg_price_arr["lprice"]["827316"])."。";
                
            }
            if($jgtzjy_tg==""){
                // $jgtzjy_tg = html_entity_decode($this->_dao->get_sdgt_marketNews(6,$sdate2,$edate2,15));
                //①2月1-24日，莱芜市场45#碳结钢均价为3791（ID：692243）元/吨，较1月均价下跌13元/吨；预计2月均价为3789元/吨，较1月均价下跌15元/吨。②2月1-24日，莱芜市场20CrMnTi均价为4091（ID：692154）元/吨，较1月均价下跌13元/吨，预计2月均价为4089元/吨，较1月均价下跌15元/吨。
                $yc_price7 = ($hxg_avg_price_arr["tprice"]["692243"] * $curr_yue_gzr + $hxg_price_arr["tprice"]["692243"] * ($curr_yue_mo_gzr - $curr_yue_gzr)) / $curr_yue_mo_gzr;
                $yc_price8 = ($hxg_avg_price_arr["tprice"]["692154"] * $curr_yue_gzr + $hxg_price_arr["tprice"]["692154"] * ($curr_yue_mo_gzr - $curr_yue_gzr)) / $curr_yue_mo_gzr;
                $jgtzjy_tg = "①".date("n",strtotime($edate))."月1-24日，莱芜市场45#碳结钢均价为".$hxg_avg_price_arr["tprice"]["692243"]."元/吨，较".date("n",strtotime($edate2))."月均价".$this->zhangdie6($hxg_avg_price_arr["zd2"]["692243"])."；预计".date("n",strtotime($edate))."月均价为".$yc_price7."元/吨，较".date("n",strtotime($edate2))."月均价".$this->zhangdie6($yc_price7 - $hxg_avg_price_arr["lprice"]["692243"])."。②".date("n",strtotime($edate))."月1-24日，莱芜市场20CrMnTi均价为".$hxg_avg_price_arr["tprice"]["692154"]."元/吨，较".date("n",strtotime($edate2))."月均价".$this->zhangdie6($hxg_avg_price_arr["zd2"]["692154"])."，预计".date("n",strtotime($edate))."月均价为".$yc_price8."元/吨，较".date("n",strtotime($edate2))."月均价".$this->zhangdie6($yc_price8 - $hxg_avg_price_arr["lprice"]["692154"])."。";
            }
            if($jgtzjy_rz==""){
                // $jgtzjy_rz = html_entity_decode($this->_dao->get_sdgt_marketNews(6,$sdate2,$edate2,16));
                //①2月1-24日，莱芜市场热卷Q235均价3407（ID：693112）元/吨，较1月均价下跌5元/吨；预计2月均价3403元/吨，较1月均价下跌8元/吨。②2月1-24日，莱芜市场热卷Q355均价3497（ID：693122）元/吨，较1月均价下跌10元/吨；预计2月均价3497元/吨，较1月均价下跌15元/吨。
                $yc_price9 = round(($hxg_avg_price_arr["tprice"]["693112"] * $curr_yue_gzr + $hxg_price_arr["tprice"]["693112"] * ($curr_yue_mo_gzr - $curr_yue_gzr)) / $curr_yue_mo_gzr);
                $yc_price10 = round(($hxg_avg_price_arr["tprice"]["693122"] * $curr_yue_gzr + $hxg_price_arr["tprice"]["693122"] * ($curr_yue_mo_gzr - $curr_yue_gzr)) / $curr_yue_mo_gzr);
                $jgtzjy_rz = "①".date("n",strtotime($edate))."月1-24日，莱芜市场热卷Q235均价".$hxg_avg_price_arr["tprice"]["693112"]."元/吨，较".date("n",strtotime($edate2))."月均价".$this->zhangdie6($hxg_avg_price_arr["zd2"]["693112"])."；预计".date("n",strtotime($edate))."月均价为".$yc_price9."元/吨，较".date("n",strtotime($edate2))."月均价".$this->zhangdie6($yc_price9 - $hxg_avg_price_arr["lprice"]["693112"])."。②".date("n",strtotime($edate))."月1-24日，莱芜市场热卷Q355均价".$hxg_avg_price_arr["tprice"]["693122"]."元/吨，较".date("n",strtotime($edate2))."月均价".$this->zhangdie6($hxg_avg_price_arr["zd2"]["693122"])."，预计".date("n",strtotime($edate))."月均价为".$yc_price10."元/吨，较".date("n",strtotime($edate2))."月均价".$this->zhangdie6($yc_price10 - $hxg_avg_price_arr["lprice"]["693122"])."。";
            }
            if($jgtzjy_lz==""){
                // $jgtzjy_lz = html_entity_decode($this->_dao->get_sdgt_marketNews(6,$sdate2,$edate2,17));
                //①2月1-24日，莱芜市场普冷均价3958（ID：694312）元/吨，较1月均价下跌76元/吨；预计2月均价3948元/吨，较1月均价下跌86元/吨。
                $yc_price11 = round(($hxg_avg_price_arr["tprice"]["694312"] * $curr_yue_gzr + $hxg_price_arr["tprice"]["694312"] * ($curr_yue_mo_gzr - $curr_yue_gzr)) / $curr_yue_mo_gzr);
                $jgtzjy_lz = "①".date("n",strtotime($edate))."月1-24日，莱芜市场普冷均价".$hxg_avg_price_arr["tprice"]["694312"]."元/吨，较".date("n",strtotime($edate2))."月均价".$this->zhangdie6($hxg_avg_price_arr["zd2"]["694312"])."；预计".date("n",strtotime($edate))."月均价为".$yc_price11."元/吨，较".date("n",strtotime($edate2))."月均价".$this->zhangdie6($yc_price11 - $hxg_avg_price_arr["lprice"]["694312"])."。";
            }
        }
        $this->assign("sdate",$sdate);
        $this->assign("edate",$edate_t);
        $this->assign("type","6");
        $this->assign( "adminid", $params['adminid']);

        /*$marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(6,$sdate,$edate,601));
        $this->assign( "marketNews601", $marketNews);
        $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(6,$sdate,$edate,602));
        $this->assign( "marketNews602", $marketNews);
        $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(6,$sdate,$edate,603));
        $this->assign( "marketNews603", $marketNews);
        $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(6,$sdate,$edate,604));
        $this->assign( "marketNews604", $marketNews);
        $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(6,$sdate,$edate,605));
        $this->assign( "marketNews605", $marketNews);
        $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(6,$sdate,$edate,606));
        $this->assign( "marketNews606", $marketNews);
        $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(6,$sdate,$edate,607));
        $this->assign( "marketNews607", $marketNews);
        $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(6,$sdate,$edate,608));
        $this->assign( "marketNews608", $marketNews);
        $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(6,$sdate,$edate,609));
        $this->assign( "marketNews609", $marketNews);
        $marketNews = html_entity_decode($this->_dao->get_sdgt_marketNews(6,$sdate,$edate,610));
        $this->assign( "marketNews610", $marketNews);*/

        $schg = str_replace("font-size:","",$schg);
        $schg = str_replace("font-family:","",$schg);
        $jgtzjy_Hxg = str_replace("font-size:","",$jgtzjy_Hxg);
        $jgtzjy_Hxg = str_replace("font-family:","",$jgtzjy_Hxg);
        $jgtzjy_tg = str_replace("font-size:","",$jgtzjy_tg);
        $jgtzjy_tg = str_replace("font-family:","",$jgtzjy_tg);
        $jgtzjy_rz = str_replace("font-size:","",$jgtzjy_rz);
        $jgtzjy_rz = str_replace("font-family:","",$jgtzjy_rz);
        $jgtzjy_lz = str_replace("font-size:","",$jgtzjy_lz);
        $jgtzjy_lz = str_replace("font-family:","",$jgtzjy_lz);

        //省内市场主要钢厂品种价格变化情况一览表
        $topicture_pz = array(
            "厚板Q235A20mm"=>array("173012","183012","073012","403012","363012"),
            "型钢H200*200"=>array("697311","827311","077311","677311","287311"),
            "45#16-50mm"=>array("692243","702243","872243","122243","082243"),
            "热轧4.75mm"=>array("6931122","D231121","8231125","8731121","073112"),
            "冷轧1.0mm"=>array("694312","874312","1843122","D24312","074312"),
            "酸洗SPHC.0mm"=>array("D252151","0752155"),
            "镀锌1.0mm"=>array("D24412","184412","0744123"),
            "螺纹HRB400E22mm"=>array("692043","172023","872023","182044","082023"),
        );


        //省内市场主要钢厂品种价格变化情况一览表
        $gcpz_price = array();
        $gcpz_price[] = array("品种",
            date("n月j日",strtotime($trade_day2)),
            date("n月j日",strtotime($trade_day)),"涨跌",
            date("n月均价",strtotime($edate2)),
            date("n月j-",strtotime($sdate)).date("j日均价",strtotime($edate)),"涨跌",
            date("预计n月均价",strtotime($edate)),"涨跌");

        //（（1-25日所有工作日的实际价格）+（25日价格乘以25日之后的工作日））÷ 实际工作日 = 预计本月均价
        //本月交易日天数
        $tradeDays = $this->get_xhdate3($edate);
        $days = bcadd($tradeDays['before'],$tradeDays['after']);
        //print_r($tradeDays);

        foreach ($topicture_pz as $key=>$item){
            $price_pz = $this->get_avgprice($trade_day,$trade_day,$item,$trade_day2,$trade_day2);
            $price_pz2 = $this->get_avgprice($sdate,$edate,$item,$sdate2,$edate2);

            //预计本月均价
            $expect_price = ($price_pz2['tprice']*$tradeDays['before']);
            $expect_price += ($price_pz['tprice']*$tradeDays['after']);
            $expect_price = round($expect_price/$days);

            $arr = array();
            $arr[] = $key;
            $arr[] = $price_pz['lprice'];
            $arr[] = $price_pz['tprice'];
            $arr[] = $this->zhangdie($price_pz['zd']);
            $arr[] = $price_pz2['lprice'];
            $arr[] = $price_pz2['tprice'];
            $arr[] = $this->zhangdie($price_pz2['zd']);
            $arr[] = $expect_price;
            $arr[] = $this->zhangdie(bcsub($expect_price,$price_pz2['lprice']));

            $gcpz_price[] = $arr;
        }

        //主要城市H型钢价格对比表====start
        $Hxg_price = array();

        $arr = array("我的钢铁");
        foreach ($topicture_H2 as $item){
            $arr[] = $MS_Price[$item]['tprice'];
            $arr[] = $MS_Price[$item]['lprice'];
            $arr[] = $this->zhangdie($MS_Price[$item]['zd']);
        }
        $Hxg_price[] = $arr;

        $arr = array("钢之家");
        foreach ($topicture_H as $item){
            $arr[] = $price_H[$item]['tprice'];
            $arr[] = $price_H[$item]['lprice'];
            $arr[] = $this->zhangdie($price_H[$item]['zd']);
        }
        $Hxg_price[] = $arr;

        $arr = array("网站平均");
        foreach ($avgPrice_H as $item){
            $arr[] = $item;
        }
        $Hxg_price[] = $arr;

        $arr = array("市场成交价");
        foreach ($topicture_H2 as $item){
            $arr[] = $Market_Price[$item]['tprice'];
            $arr[] = $Market_Price[$item]['lprice'];
            $arr[] = $this->zhangdie($Market_Price[$item]['zd']);
        }
        $Hxg_price[] = $arr;

        $Hxg_price[] = array("莱芜-网站平均",
            "--","--","--",
            $this->zhangdie(bcsub($avgPrice_H[0],$avgPrice_H[3])),$this->zhangdie(bcsub($avgPrice_H[1],$avgPrice_H[4])),"--",
            $this->zhangdie(bcsub($avgPrice_H[0],$avgPrice_H[6])),$this->zhangdie(bcsub($avgPrice_H[1],$avgPrice_H[7])),"--",
            "--","--","--",
            $this->zhangdie(bcsub($avgPrice_H[9],$avgPrice_H[12])),$this->zhangdie(bcsub($avgPrice_H[10],$avgPrice_H[13])),"--",
            $this->zhangdie(bcsub($avgPrice_H[9],$avgPrice_H[15])),$this->zhangdie(bcsub($avgPrice_H[10],$avgPrice_H[16])),"--"
        );

        $arr = array("莱芜-市场成交价");
        foreach ($topicture_H2 as $item){
            if(strpos($item,'莱芜') !== false) {
                $arr[] = "--";
                $arr[] = "--";
                $arr[] = "--";
            } else {
                if(strpos($item,'200*200') !== false){
                    $key = "Q235B 200*200mm莱芜";
                }else{
                    $key = "Q235B 600*200mm莱芜";
                }
                $arr[] = $this->zhangdie(bcsub($Market_Price[$key]['tprice'],$Market_Price[$item]['tprice']));
                $arr[] = $this->zhangdie(bcsub($Market_Price[$key]['lprice'],$Market_Price[$item]['lprice']));
                $arr[] = "--";
            }
        }
        $Hxg_price[] = $arr;

        //普碳结算均价与市场均价涨跌幅度对比
        $H_arr = array(
            "H200*200"=>array("MS"=>"Q235B 200*200mm莱芜","SH"=>"697311"),
            "H600*200"=>array("MS"=>"Q235B 600*200mm莱芜","SH"=>"69731V")
        );
        $H200_price = array();
        foreach ($H_arr as $key=>$tmp){

            $price_pz = $this->get_avgprice($trade_day,$trade_day,array($tmp["SH"]));
            $tprice_after = round(bcadd($price_pz['tprice'],$MS_Price_Day[$tmp["MS"]]['tprice'])/2);

            //echo $tmp["MS"]."##".$price_pz['tprice']."##".$MS_Price_Day[$tmp["MS"]]['tprice']."<br>";
            //print_r($tradeDays);

            //本月交易日实际网站均价
            $tprice_before = round(bcadd($price_H[$tmp["SH"]]['tprice'],$MS_Price[$tmp["MS"]]['tprice'])/2);
            $lprice_before = round(bcadd($price_H[$tmp["SH"]]['lprice'],$MS_Price[$tmp["MS"]]['lprice'])/2);
            $zd_before = round(bcadd($price_H[$tmp["SH"]]['zd'],$MS_Price[$tmp["MS"]]['zd'])/2);

            //预计本月网站均价
            $expect_price = ($tprice_before*$tradeDays['before']);
            $expect_price += ($tprice_after*$tradeDays['after']);
            $expect_price = round($expect_price/$days);

            $H200_price[$key][0] = array("网站均价",
                $lprice_before,
                $tprice_before,
                $this->zhangdie($zd_before),
                $expect_price,
                $this->zhangdie(bcsub($expect_price,$lprice_before))
            );

            //预计本月市场成交价均价
            $expect_price = ($Market_Price[$tmp["MS"]]['tprice']*$tradeDays['before']);
            $expect_price += ($Market_Price_Day[$tmp["MS"]]['tprice']*$tradeDays['after']);
            $expect_price = round($expect_price/$days);

            $H200_price[$key][1] = array("市场成交价",
                $Market_Price[$tmp["MS"]]['lprice'],
                $Market_Price[$tmp["MS"]]['tprice'],
                $this->zhangdie($Market_Price[$tmp["MS"]]['zd']),
                $expect_price,
                $this->zhangdie(bcsub($expect_price,$Market_Price[$tmp["MS"]]['lprice']))
            );

            //预计本月结算价均价
            $H200_price[$key][2] = array("结算价",
                $Settlement_Price[$tmp["MS"]]['lprice'],
                $Settlement_Price[$tmp["MS"]]['tprice'],
                $this->zhangdie($Settlement_Price[$tmp["MS"]]['zd']),
                "--",
                "--"
            );
        }
        //主要城市H型钢价格对比表====end

        //主要城市特钢价格对比表====start
        $Tg_price = array();

        $arr = array("我的钢铁");
        foreach ($topicture_TG2 as $item){
            $arr[] = $MS_Price[$item]['tprice'];
            $arr[] = $MS_Price[$item]['lprice'];
            $arr[] = $this->zhangdie($MS_Price[$item]['zd']);
        }
        $Tg_price[] = $arr;

        $arr = array("钢之家");
        foreach ($topicture_TG as $item){
            $arr[] = $price_TG[$item]['tprice'];
            $arr[] = $price_TG[$item]['lprice'];
            $arr[] = $this->zhangdie($price_TG[$item]['zd']);
        }
        $Tg_price[] = $arr;

        $arr = array("网站平均");
        foreach ($avgPrice_TG as $item){
            $arr[] = $item;
        }
        $Tg_price[] = $arr;

        $arr = array("市场成交价");
        foreach ($topicture_TG2 as $item){
            $arr[] = $Market_Price[$item]['tprice'];
            $arr[] = $Market_Price[$item]['lprice'];
            $arr[] = $this->zhangdie($Market_Price[$item]['zd']);
        }
        $Tg_price[] = $arr;

        $Tg_price[] = array("莱芜-网站平均",
            "--","--","--",
            $this->zhangdie(bcsub($avgPrice_TG[0],$avgPrice_TG[3])),$this->zhangdie(bcsub($avgPrice_TG[1],$avgPrice_TG[4])),"--",
            "--","--","--",
            $this->zhangdie(bcsub($avgPrice_TG[6],$avgPrice_TG[9])),$this->zhangdie(bcsub($avgPrice_TG[7],$avgPrice_TG[10])),"--",
            $this->zhangdie(bcsub($avgPrice_TG[6],$avgPrice_TG[12])),$this->zhangdie(bcsub($avgPrice_TG[7],$avgPrice_TG[13])),"--"
        );

        $arr = array("莱芜-市场成交价");
        foreach ($topicture_TG2 as $item){
            if(strpos($item,'莱芜') !== false) {
                $arr[] = "--";
                $arr[] = "--";
                $arr[] = "--";
            } else {
                if(strpos($item,'45') !== false){
                    $key = "45#莱芜";
                }else{
                    $key = "20CrMnTi莱芜";
                }
                $arr[] = $this->zhangdie(bcsub($Market_Price[$key]['tprice'],$Market_Price[$item]['tprice']));
                $arr[] = $this->zhangdie(bcsub($Market_Price[$key]['lprice'],$Market_Price[$item]['lprice']));
                $arr[] = "--";
            }
        }
        $Tg_price[] = $arr;


        $T_arr = array(
            "45#"=>array("MS"=>"45#莱芜","SH"=>"692243"),
            "20CrMnTi"=>array("MS"=>"20CrMnTi莱芜","SH"=>"692154")
        );
        $Tg_price2 = array();
        foreach ($T_arr as $key=>$tmp){

            $price_pz = $this->get_avgprice($trade_day,$trade_day,array($tmp["SH"]));
            $tprice_after = round(bcadd($price_pz['tprice'],$MS_Price_Day[$tmp["MS"]]['tprice'])/2);

            //本月交易日实际网站均价
            $tprice_before = round(bcadd($price_TG[$tmp["SH"]]['tprice'],$MS_Price[$tmp["MS"]]['tprice'])/2);
            $lprice_before = round(bcadd($price_TG[$tmp["SH"]]['lprice'],$MS_Price[$tmp["MS"]]['lprice'])/2);
            $zd_before = round(bcadd($price_TG[$tmp["SH"]]['zd'],$MS_Price[$tmp["MS"]]['zd'])/2);

            //预计本月网站均价
            $expect_price = ($tprice_before*$tradeDays['before']);
            $expect_price += ($tprice_after*$tradeDays['after']);
            $expect_price = round($expect_price/$days);

            $Tg_price2[$key][0] = array("网站均价",
                $lprice_before,
                $tprice_before,
                $this->zhangdie($zd_before),
                $expect_price,
                $this->zhangdie(bcsub($expect_price,$lprice_before))
            );

            //预计本月市场成交价均价
            $expect_price = ($Market_Price[$tmp["MS"]]['tprice']*$tradeDays['before']);
            $expect_price += ($Market_Price_Day[$tmp["MS"]]['tprice']*$tradeDays['after']);
            $expect_price = round($expect_price/$days);

            $Tg_price2[$key][1] = array("市场成交价",
                $Market_Price[$tmp["MS"]]['lprice'],
                $Market_Price[$tmp["MS"]]['tprice'],
                $this->zhangdie($Market_Price[$tmp["MS"]]['zd']),
                $expect_price,
                $this->zhangdie(bcsub($expect_price,$Market_Price[$tmp["MS"]]['lprice']))
            );

            //预计本月结算价均价

            $Tg_price2[$key][2] = array("结算价",
                $Settlement_Price[$tmp["MS"]]['lprice'],
                $Settlement_Price[$tmp["MS"]]['tprice'],
                $this->zhangdie($Settlement_Price[$tmp["MS"]]['zd']),
                "--",
                "--"
            );
        }
        //主要城市特钢价格对比表====end

        //主要城市热轧价格对比表====start
        $rz_price = array();
        $arr = array("我的钢铁");
        foreach ($topicture_RZ2 as $item){
            $arr[] = $MS_Price[$item]['tprice'];
        }
        $rz_price[] = $arr;
        $arr = array("钢之家");
        foreach ($topicture_RZ1 as $item){
            $arr[] = $price_RZ[$item]['tprice'];
        }
        $rz_price[] = $arr;

        $arr = array("网站平均");
        foreach ($topicture_RZ1 as $key=>$item){
            $key2 = $topicture_RZ2[$key];
            $arr[] = round(bcadd($price_RZ[$item]['tprice'],$MS_Price[$key2]['tprice'])/2);
        }
        $rz_price[] = $arr;

        $arr = array("市场成交价");
        foreach ($topicture_RZ2 as $item){
            $arr[] = $Market_Price[$item]['tprice'];
        }
        $rz_price[] = $arr;

        $rz_price1 = array();
        $arr = array("我的钢铁");
        foreach ($topicture_RZ2_1 as $item){
            $arr[] = $MS_Price[$item]['tprice'];
            $arr[] = $MS_Price[$item]['lprice'];
            $arr[] = $this->zhangdie($MS_Price[$item]['zd']);
        }
        $rz_price1[] = $arr;
        $arr = array("钢之家");
        foreach ($topicture_RZ1_1 as $item){
            $arr[] = $price_RZ[$item]['tprice'];
            $arr[] = $price_RZ[$item]['lprice'];
            $arr[] = $this->zhangdie($price_RZ[$item]['zd']);
        }
        $rz_price1[] = $arr;

        $arr = array("网站平均");
        foreach ($avgPrice_RZ_1 as $item){
            $arr[] = $item;
        }
        $rz_price1[] = $arr;

        $arr = array("市场成交价");
        foreach ($topicture_RZ2_1 as $item){
            $arr[] = $Market_Price[$item]['tprice'];
            $arr[] = $Market_Price[$item]['lprice'];
            $arr[] = $this->zhangdie($Market_Price[$item]['zd']);
        }
        $rz_price1[] = $arr;

        $rz_price1[] = array("莱芜-网站平均",
            "--","--","--",
            $this->zhangdie(bcsub($avgPrice_RZ_1[0],$avgPrice_RZ_1[3])),$this->zhangdie(bcsub($avgPrice_RZ_1[1],$avgPrice_RZ_1[4])),"--",
            $this->zhangdie(bcsub($avgPrice_RZ_1[0],$avgPrice_RZ_1[6])),$this->zhangdie(bcsub($avgPrice_RZ_1[1],$avgPrice_RZ_1[7])),"--",
            $this->zhangdie(bcsub($avgPrice_RZ_1[0],$avgPrice_RZ_1[9])),$this->zhangdie(bcsub($avgPrice_RZ_1[1],$avgPrice_RZ_1[10])),"--"
        );

        $arr = array("莱芜-市场成交价");
        foreach ($topicture_RZ2_1 as $item){
            if(strpos($item,'莱芜') !== false) {
                $arr[] = "--";
                $arr[] = "--";
                $arr[] = "--";
            } else {
                $key = "Q235B 4.75mm莱芜";
                $arr[] = $this->zhangdie(bcsub($Market_Price[$key]['tprice'],$Market_Price[$item]['tprice']));
                $arr[] = $this->zhangdie(bcsub($Market_Price[$key]['lprice'],$Market_Price[$item]['lprice']));
                $arr[] = "--";
            }
        }
        $rz_price1[] = $arr;

        $rz_price2 = array();
        $arr = array("我的钢铁");
        foreach ($topicture_RZ2_2 as $item){
            $arr[] = $MS_Price[$item]['tprice'];
            $arr[] = $MS_Price[$item]['lprice'];
            $arr[] = $this->zhangdie($MS_Price[$item]['zd']);
        }
        $rz_price2[] = $arr;
        $arr = array("钢之家");
        foreach ($topicture_RZ1_2 as $item){
            $arr[] = $price_RZ[$item]['tprice'];
            $arr[] = $price_RZ[$item]['lprice'];
            $arr[] = $this->zhangdie($price_RZ[$item]['zd']);
        }
        $rz_price2[] = $arr;

        $arr = array("网站平均");
        foreach ($avgPrice_RZ_2 as $item){
            $arr[] = $item;
        }
        $rz_price2[] = $arr;

        $arr = array("市场成交价");
        foreach ($topicture_RZ2_2 as $item){
            $arr[] = $Market_Price[$item]['tprice'];
            $arr[] = $Market_Price[$item]['lprice'];
            $arr[] = $this->zhangdie($Market_Price[$item]['zd']);
        }
        $rz_price2[] = $arr;

        $rz_price2[] = array("日照-网站平均",
            "--","--","--",
            $this->zhangdie(bcsub($avgPrice_RZ_2[0],$avgPrice_RZ_2[3])),$this->zhangdie(bcsub($avgPrice_RZ_2[1],$avgPrice_RZ_2[4])),"--",
            $this->zhangdie(bcsub($avgPrice_RZ_2[0],$avgPrice_RZ_2[6])),$this->zhangdie(bcsub($avgPrice_RZ_2[1],$avgPrice_RZ_2[7])),"--",
            $this->zhangdie(bcsub($avgPrice_RZ_2[0],$avgPrice_RZ_2[9])),$this->zhangdie(bcsub($avgPrice_RZ_2[1],$avgPrice_RZ_2[10])),"--",
            $this->zhangdie(bcsub($avgPrice_RZ_2[0],$avgPrice_RZ_2[12])),$this->zhangdie(bcsub($avgPrice_RZ_2[1],$avgPrice_RZ_2[13])),"--"
        );

        $arr = array("日照-市场成交价");
        foreach ($topicture_RZ2_2 as $item){
            if(strpos($item,'莱芜') !== false) {
                $arr[] = "--";
                $arr[] = "--";
                $arr[] = "--";
            } else {
                $key = "Q355B 4.75mm日照";
                $arr[] = $this->zhangdie(bcsub($Market_Price[$key]['tprice'],$Market_Price[$item]['tprice']));
                $arr[] = $this->zhangdie(bcsub($Market_Price[$key]['lprice'],$Market_Price[$item]['lprice']));
                $arr[] = "--";
            }
        }
        $rz_price2[] = $arr;

        //结算均价与市场均价涨跌幅度对比
        $rz_arr = array(
            "Q235B"=>array("MS"=>"Q235B 4.75mm莱芜","SH"=>"693117"),
            "Q355B"=>array("MS"=>"Q355B 4.75mm莱芜","SH"=>"D23122")
        );
        $rz_price3 = array();
        foreach ($rz_arr as $key=>$tmp){

            $price_pz = $this->get_avgprice($trade_day,$trade_day,array($tmp["SH"]));
            $tprice_after = round(bcadd($price_pz['tprice'],$MS_Price_Day[$tmp["MS"]]['tprice'])/2);

            //本月交易日实际网站均价
            $tprice_before = round(bcadd($price_RZ[$tmp["SH"]]['tprice'],$MS_Price[$tmp["MS"]]['tprice'])/2);
            $lprice_before = round(bcadd($price_RZ[$tmp["SH"]]['lprice'],$MS_Price[$tmp["MS"]]['lprice'])/2);
            $zd_before = round(bcadd($price_RZ[$tmp["SH"]]['zd'],$MS_Price[$tmp["MS"]]['zd'])/2);

            //预计本月网站均价
            $expect_price = ($tprice_before*$tradeDays['before']);
            $expect_price += ($tprice_after*$tradeDays['after']);
            $expect_price = round($expect_price/$days);

            $rz_price3[$key][0] = array("网站均价",
                $lprice_before,
                $tprice_before,
                $this->zhangdie($zd_before),
                $expect_price,
                $this->zhangdie(bcsub($expect_price,$lprice_before))
            );

            //预计本月市场成交价均价
            $expect_price = ($Market_Price[$tmp["MS"]]['tprice']*$tradeDays['before']);
            $expect_price += ($Market_Price_Day[$tmp["MS"]]['tprice']*$tradeDays['after']);
            $expect_price = round($expect_price/$days);

            $rz_price3[$key][1] = array("市场成交价",
                $Market_Price[$tmp["MS"]]['lprice'],
                $Market_Price[$tmp["MS"]]['tprice'],
                $this->zhangdie($Market_Price[$tmp["MS"]]['zd']),
                $expect_price,
                $this->zhangdie(bcsub($expect_price,$Market_Price[$tmp["MS"]]['lprice']))
            );

            //预计本月结算价均价

            $rz_price3[$key][2] = array("结算价",
                $Settlement_Price[$tmp["MS"]]['lprice'],
                $Settlement_Price[$tmp["MS"]]['tprice'],
                $this->zhangdie($Settlement_Price[$tmp["MS"]]['zd']),
                "--",
                "--"
            );
        }
        //主要城市热轧价格对比表====ended


        //主要城市冷轧价格对比表====start
        //stcode 694312	D24312	8743123	0743121	4043123	2842121
        $lz_price = array();
        $arr = array("我的钢铁");
        foreach ($topicture_LZ2 as $item){
            $arr[] = $MS_Price[$item]['tprice'];
            $arr[] = $MS_Price[$item]['lprice'];
            $arr[] = $this->zhangdie($MS_Price[$item]['zd']);
        }
        $lz_price[] = $arr;
        $arr = array("钢之家");
        foreach ($topicture_LZ1 as $item){
            $arr[] = $price_LZ[$item]['tprice'];
            $arr[] = $price_LZ[$item]['lprice'];
            $arr[] = $this->zhangdie($price_LZ[$item]['zd']);
        }
        $lz_price[] = $arr;
        $arr = array("网站平均");
        foreach ($avgPrice_LZ as $item){
            $arr[] = $item;
        }
        $lz_price[] = $arr;
        $arr = array("市场成交价");
        foreach ($topicture_LZ2 as $item){
            $arr[] = $Market_Price[$item]['tprice'];
            $arr[] = $Market_Price[$item]['lprice'];
            $arr[] = $this->zhangdie($Market_Price[$item]['zd']);
        }
        $lz_price[] = $arr;

        $lz_price[] = array("莱芜-网站平均",
            "--","--","--",
            $this->zhangdie(bcsub($avgPrice_LZ[0],$avgPrice_LZ[3])),$this->zhangdie(bcsub($avgPrice_LZ[1],$avgPrice_LZ[4])),"--",
            $this->zhangdie(bcsub($avgPrice_LZ[0],$avgPrice_LZ[6])),$this->zhangdie(bcsub($avgPrice_LZ[1],$avgPrice_LZ[7])),"--"
        );

        $arr = array("莱芜-市场成交价");
        foreach ($topicture_LZ2 as $item){
            if(strpos($item,'莱芜') !== false) {
                $arr[] = "--";
                $arr[] = "--";
                $arr[] = "--";
            } else {
                $key = "冷轧1500莱芜";
                $arr[] = $this->zhangdie(bcsub($Market_Price[$key]['tprice'],$Market_Price[$item]['tprice']));
                $arr[] = $this->zhangdie(bcsub($Market_Price[$key]['lprice'],$Market_Price[$item]['lprice']));
                $arr[] = "--";
            }
        }
        $lz_price[] = $arr;

        $lz_price1 = array();
        $arr = array("我的钢铁");
        foreach ($topicture_LZ2_1 as $item){
            $arr[] = $MS_Price[$item]['tprice'];
            $arr[] = $MS_Price[$item]['lprice'];
            $arr[] = $this->zhangdie($MS_Price[$item]['zd']);
        }
        $lz_price1[] = $arr;
        $arr = array("钢之家");
        foreach ($topicture_LZ1_1 as $item){
            $arr[] = $price_LZ[$item]['tprice'];
            $arr[] = $price_LZ[$item]['lprice'];
            $arr[] = $this->zhangdie($price_LZ[$item]['zd']);
        }
        $lz_price1[] = $arr;

        $arr = array("网站平均");
        foreach ($avgPrice_LZ_1 as $item){
            $arr[] = $item;
        }
        $lz_price1[] = $arr;

        $arr = array("市场成交价");
        foreach ($topicture_LZ2_1 as $item){
            $arr[] = $Market_Price[$item]['tprice'];
            $arr[] = $Market_Price[$item]['lprice'];
            $arr[] = $this->zhangdie($Market_Price[$item]['zd']);
        }
        $lz_price1[] = $arr;

        $lz_price1[] = array("日照-网站平均",
            "--","--","--",
            $this->zhangdie(bcsub($avgPrice_LZ_1[0],$avgPrice_LZ_1[3])),$this->zhangdie(bcsub($avgPrice_LZ_1[1],$avgPrice_LZ_1[4])),"--",
            $this->zhangdie(bcsub($avgPrice_LZ_1[0],$avgPrice_LZ_1[6])),$this->zhangdie(bcsub($avgPrice_LZ_1[1],$avgPrice_LZ_1[7])),"--",
            $this->zhangdie(bcsub($avgPrice_LZ_1[0],$avgPrice_LZ_1[9])),$this->zhangdie(bcsub($avgPrice_LZ_1[1],$avgPrice_LZ_1[10])),"--",
            $this->zhangdie(bcsub($avgPrice_LZ_1[0],$avgPrice_LZ_1[12])),$this->zhangdie(bcsub($avgPrice_LZ_1[1],$avgPrice_LZ_1[13])),"--",
            $this->zhangdie(bcsub($avgPrice_LZ_1[0],$avgPrice_LZ_1[15])),$this->zhangdie(bcsub($avgPrice_LZ_1[1],$avgPrice_LZ_1[16])),"--"
        );

        $arr = array("日照-市场成交价");
        foreach ($topicture_LZ2_1 as $item){
            if(strpos($item,'日照') !== false) {
                $arr[] = "--";
                $arr[] = "--";
                $arr[] = "--";
            } else {
                $key = "冷轧2030日照";
                $arr[] = $this->zhangdie(bcsub($Market_Price[$key]['tprice'],$Market_Price[$item]['tprice']));
                $arr[] = $this->zhangdie(bcsub($Market_Price[$key]['lprice'],$Market_Price[$item]['lprice']));
                $arr[] = "--";
            }
        }
        $lz_price1[] = $arr;

        //结算均价与市场均价涨跌幅度对比
        $lz_arr = array(
            "SPCC-SD"=>array("MS"=>"冷轧1500莱芜","SH"=>"694312")
        );
        $lz_price2 = array();
        foreach ($lz_arr as $key=>$tmp){

            $price_pz = $this->get_avgprice($trade_day,$trade_day,array($tmp["SH"]));
            $tprice_after = round(bcadd($price_pz['tprice'],$MS_Price_Day[$tmp["MS"]]['tprice'])/2);

            //本月交易日实际网站均价
            $tprice_before = round(bcadd($price_LZ[$tmp["SH"]]['tprice'],$MS_Price[$tmp["MS"]]['tprice'])/2);
            $lprice_before = round(bcadd($price_LZ[$tmp["SH"]]['lprice'],$MS_Price[$tmp["MS"]]['lprice'])/2);
            $zd_before = round(bcadd($price_LZ[$tmp["SH"]]['zd'],$MS_Price[$tmp["MS"]]['zd'])/2);

            //预计本月网站均价
            $expect_price = ($tprice_before*$tradeDays['before']);
            $expect_price += ($tprice_after*$tradeDays['after']);
            $expect_price = round($expect_price/$days);

            $lz_price2[$key][0] = array("网站均价",
                $lprice_before,
                $tprice_before,
                $this->zhangdie($zd_before),
                $expect_price,
                $this->zhangdie(bcsub($expect_price,$lprice_before))
            );

            //预计本月市场成交价均价
            $expect_price = ($Market_Price[$tmp["MS"]]['tprice']*$tradeDays['before']);
            $expect_price += ($Market_Price_Day[$tmp["MS"]]['tprice']*$tradeDays['after']);
            $expect_price = round($expect_price/$days);

            $lz_price2[$key][1] = array("市场成交价",
                $Market_Price[$tmp["MS"]]['lprice'],
                $Market_Price[$tmp["MS"]]['tprice'],
                $this->zhangdie($Market_Price[$tmp["MS"]]['zd']),
                $expect_price,
                $this->zhangdie(bcsub($expect_price,$Market_Price[$tmp["MS"]]['lprice']))
            );

            //预计本月结算价均价

            $lz_price2[$key][2] = array("结算价",
                $Settlement_Price[$tmp["MS"]]['lprice'],
                $Settlement_Price[$tmp["MS"]]['tprice'],
                $this->zhangdie($Settlement_Price[$tmp["MS"]]['zd']),
                "--",
                "--"
            );
        }

        //主要城市冷轧价格对比表====end

        $this->assign("schg",$schg);
        $this->assign("jgtzjy_Hxg",$jgtzjy_Hxg);
        $this->assign("jgtzjy_tg",$jgtzjy_tg);
        $this->assign("jgtzjy_rz",$jgtzjy_rz);
        $this->assign("jgtzjy_lz",$jgtzjy_lz);

        $this->assign("gcpz_price",$gcpz_price);

        $this->assign("Hxg_price",$Hxg_price);
        $this->assign("H200_price",$H200_price);

        $this->assign("Tg_price",$Tg_price);
        $this->assign("Tg_price2",$Tg_price2);

        $this->assign("rz_price",$rz_price);
        $this->assign("rz_price1",$rz_price1);
        $this->assign("rz_price2",$rz_price2);
        $this->assign("rz_price3",$rz_price3);

        $this->assign("lz_price",$lz_price);
        $this->assign("lz_price1",$lz_price1);
        $this->assign("lz_price2",$lz_price2);
        $this->assign("mode",$params['mode']);
        $this->assign("params",$params);

        $this->monthMarketPriceChart($edate);

    }

    /**
     * 处理月定价的所有图表
     * Created by hezpeng.
     * Date:2024/6/18 15:47
     * @param
     */
    protected function monthMarketPriceChart($edate){

        $oneYearAgo = date("Y-m-d",strtotime($edate."-1 year"));
        $URL = DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&smalltitle=1&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart='.$oneYearAgo.'&DateEnd='.$edate;
        $URL .= "&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=2&needCompress=0&ChartExt=&ChartExtLineStyle=";
        $URL .= "&isbg=3&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=";
        $imagesUrls = array(
            "0"=>$URL.'&ImageTitle=uvGw5VEyMzVBMjBtbcqhxNrK0LOhvNu48dffysbNvA%3d%3d&DTIDJson=[{"DTID":"29869","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"vMPEzw==","unitconver":"1","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"29993","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"x+C1ug==","unitconver":"1","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"29226","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"yc+6ow==","unitconver":"1","unitstring":"1Ko=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"30990","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"zOy98g==","unitconver":"1","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"30635","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"zuS6ug==","unitconver":"1","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]',
            "1"=>$URL.'&ImageTitle=0M241kgyMDAqMjAwyqHE2srQs6G827jx19%2fKxs28&DTIDJson=[{"DTID":"43351","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"wLPO3w==","unitconver":"1","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"124032","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"zKmwsg==","unitconver":"1","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"42296","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"yc+6ow==","unitconver":"1","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"42933","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"zMbJvQ==","unitconver":"1","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"42663","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"uePW3Q==","unitconver":"1","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]',
            "2"=>$URL.'&ImageTitle=NDUjMTYtNTBtbcqhxNrK0LOhvNu48dffysbNvA%3d%3d&DTIDJson=[{"DTID":"50787","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"wLPO3w==","unitconver":"1","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"51008","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"zqu3uw==","unitconver":"1","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"50833","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"wdnSyg==","unitconver":"1","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"50355","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"zt7O/Q==","unitconver":"1","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"50273","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"urzW3Q==","unitconver":"1","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]',
            "3"=>$URL.'&ImageTitle=wOTU%2fjEuMG1tyqHE2srQs6G827jx19%2fKxs28&DTIDJson=[{"DTID":"38067","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"wLPO3w==","unitconver":"1","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"38201","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"wdnSyg==","unitconver":"1","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"122402","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"x+C1ug==","unitconver":"1","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"93007","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"yNXV1Q==","unitconver":"0","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"36594","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"yc+6ow==","unitconver":"1","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]',
            "4"=>$URL.'&ImageTitle=yMjU%2fjQuNzVtbcqhxNrK0LOhvNu48dffysbNvA%3d%3d&DTIDJson=[{"DTID":"36023","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"wLPO3w==","unitconver":"1","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"54827","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"yNXV1Q==","unitconver":"1","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"36254","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"zKmwsg==","unitconver":"1","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"36375","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"wdnSyg==","unitconver":"1","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"33296","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"yc+6ow==","unitconver":"1","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]',
            "5"=>$URL.'&ImageTitle=y%2bHPtFNQSEPKocTaytCzobzbuPHX38rGzbw%3d&DTIDJson=[{"DTID":"121973","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"yNXV1Q==","unitconver":"1","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"33344","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"yc+6ow==","unitconver":"1","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]',
            "6"=>$URL.'&ImageTitle=tsbQvzEuMG1tyqHE2srQs6G827jx19%2fKxs28&DTIDJson=[{"DTID":"93011","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"yNXV1Q==","unitconver":"0","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"38440","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"x+C1ug==","unitconver":"1","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"38315","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"yc+6ow==","unitconver":"1","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]',
            "7"=>$URL.'&ImageTitle=wt3OxkhSQjQwMEUyMm1tytCzobzbuPHX38rGzbw%3d&DTIDJson=[{"DTID":"22739","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"wLPO3w==","unitconver":"1","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"91197","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"vMPEzw==","unitconver":"1","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"115593","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"wdnSyg==","unitconver":"1","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"19875","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"x+C1ug==","unitconver":"1","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"19061","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"urzW3Q==","unitconver":"1","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]',
        );
        foreach ($imagesUrls as $index => $imagesUrl) {
            $imageid = "tu" . $index;

            $imagesUrlList[] = '<div class="" id="' . $imageid . '"><iframe name="iframe" style="margin:0; padding:0; width:500px; height:279px; background-color:#FFF; visibility:inherit;" src=\'' . $imagesUrl . '&callbackname=' . $imageid . '\' frameborder="0" scrolling="no"></iframe></div>';

        }
        $this->assign("imagesUrlLists",$imagesUrlList);
    }

    function get_month_All_list1($num,$MS_Price_all,$Market_Price_all,$SH_Price_all){

        $List = array();
        $arr = array("样本");
        foreach ($num as $tmp){
            $arr[] = $tmp."日";
        }
        $List[] = $arr;//第一行

        $arr = array("MS");
        foreach ($num as $i){
            $arr[] = isset($MS_Price_all[$i]) ? $MS_Price_all[$i] : "";
        }
        $List[] = $arr;//第二行

        $arr = array("SH");
        foreach ($num as $i){
            $arr[] = isset($SH_Price_all[$i]) ? $SH_Price_all[$i] : "";
        }
        $List[] = $arr;//第三行

        $arr = array("均价");
        foreach ($num as $i){
            if(isset($MS_Price_all[$i]) && isset($SH_Price_all[$i])){
                $arr[] = round(bcadd($MS_Price_all[$i],$SH_Price_all[$i])/2);
            }else{
                $arr[] = "--";
            }
        }
        $List[] = $arr;//第四行
        $arr = array("成交价");
        foreach ($num as $i){
            $arr[] = isset($Market_Price_all[$i]) ? $Market_Price_all[$i] : "";
        }
        $List[] = $arr;//第五行

        return $List;
    }

    function get_month_All_list2($num,$MS_Price_all,$Market_Price_all,$SH_Price_all,$MS_Price,$Market_Price,$SH_Price){

        $List = array();
        $arr = array("样本");
        foreach ($num as $tmp){
            $arr[] = $tmp."日";
        }
        $arr[] = "";
        $arr[] = "均价";
        $List[] = $arr;//第一行

        $arr = array("MS");
        foreach ($num as $i){
            $arr[] = isset($MS_Price_all[$i]) ? $MS_Price_all[$i] : "";
        }
        $arr[] = "";
        $arr[] = $MS_Price['tprice'];
        $List[] = $arr;//第二行

        $arr = array("SH");
        foreach ($num as $i){
            $arr[] = isset($SH_Price_all[$i]) ? $SH_Price_all[$i] : "";
        }
        $arr[] = "";
        $arr[] = $SH_Price['tprice'];
        $List[] = $arr;//第三行

        $arr = array("均价");
        foreach ($num as $i){
            if(isset($MS_Price_all[$i]) && isset($SH_Price_all[$i])){
                $arr[] = round(bcadd($MS_Price_all[$i],$SH_Price_all[$i])/2);
            }else{
                $arr[] = "--";
            }
        }
        $arr[] = "";
        $arr[] = round(bcadd($SH_Price['tprice'],$MS_Price['tprice'])/2);
        $List[] = $arr;//第四行

        $arr = array("成交价");
        foreach ($num as $i){
            $arr[] = isset($Market_Price_all[$i]) ? $Market_Price_all[$i] : "";
        }
        $arr[] = "";
        $arr[] = $Market_Price['tprice'];
        $List[] = $arr;//第五行

        return $List;
    }

	//返回涨跌变化
	function zhangdie($int,$decimal=2){
		$intstr = "";
		if($int<0){
			$intstr = "<font color=green><strong>-".round(abs($int),$decimal)."</strong></font>";
		}elseif($int>0){
			$intstr = "<font color=red><strong>+".round(abs($int),$decimal)."</strong></font>";
		}elseif($int=="" || $int==0){
			$intstr = "<strong>0</strong>";
		}else{
			$intstr = "<font ><strong>".$int."</strong></font>";
		}
		return $intstr;
	}

    //返回涨跌变化
	function yue_yc_zhangdie4($int){
        $int = str_replace("<strong>--</strong>","持平",$int);
        $int = str_replace("<font ><strong>","",$int);
        $int = str_replace("</strong></font>","",$int);
        $int = str_replace("<font color=red><strong>+","上涨",$int);
        $int = str_replace("<font color=green><strong>-","下跌",$int);
		// if($int<0){
		// 	$intstr = "下跌".abs($int);
		// }elseif($int>0){
		// 	$intstr = "上涨".abs($int);
		// }elseif($int=="" || $int==0){
		// 	$intstr = "持平";
		// }
		return $int;
	}
    function zhangdie2($data){
        if($data>0){
            return "提高".abs($data)."个百分点";
        }elseif($data<0){
            return "回落".abs($data)."个百分点";
        }else{
            return "保持平稳";
        }
    }

    function zhangdie3($data){
        $data = (float)$data;
        $data2 = "";
        if($data > 0){
            $data2 = '增加'.$data.'万吨';
        }
        if($data < 0){
            $data2 = '减少'.abs($data).'万吨';
        }
        if($data == 0){
            $data2 = '持平';
        }
        return $data2;
    }

    function zhangdie4($data){
        if($data>0){
            return "盈余".abs($data)."元/吨";
        }elseif($data<0){
            return "亏损".abs($data)."元/吨";
        }else{
            return "保持平稳";
        }
    }

    function zhangdie5($data){
        if($data>0){
            return "上升".abs($data)."元/吨";
        }elseif($data<0){
            return "下降".abs($data)."元/吨";
        }else{
            return "持平";
        }
    }
    function zhangdie6($data){
        if($data>0){
            return "上涨".abs($data)."元/吨";
        }elseif($data<0){
            return "下跌".abs($data)."元/吨";
        }else{
            return "持平";
        }
    }
    function zhangdie7($data){
        if($data>0){
            return "上涨".abs($data);
        }elseif($data<0){
            return "下跌".abs($data);
        }else{
            return "持平";
        }
    }
    function zhangdie8($data){
        if($data>0){
            return "提高".abs($data);
        }elseif($data<0){
            return "降低".abs($data);
        }else{
            return "持平";
        }
    }

    function zhangdie9($data){
        $data = (float)$data;
        $data2 = "";
        if($data > 0){
            $data2 = '增加'.$data;
        }
        if($data < 0){
            $data2 = '减少'.abs($data);
        }
        if($data == 0){
            $data2 = '持平';
        }
        return $data2;
    }

    /**
     * 获取本周一和本周五的工作日日期
     * Created by zfy.
     * Date:2024/5/20 11:37
     * @param DateTime $date
     * @return DateTime[]
     */
    function getWeekBoundaries(DateTime $date)
    {
        // 克隆日期对象以免修改原对象
        $monday = clone $date;
        $friday = clone $date;
        // 获取本周一的日期
        $monday->modify('this week Monday');
        // 获取本周五的日期
        $friday->modify('this week Sunday');
        //返回工作日
        return [$monday, $friday];
    }

    /**
     * 获取上周 本周的周预测时间
     * Created by zfy.
     * Date:2024/5/20 11:47
     * @throws Exception
     */
    function getWeekDate($date)
    {
        $today = new DateTime($date);
        // 获取本周一和本周日的日期
        list($thisWeekMonday, $thisWeekSunday) = $this->getWeekBoundaries($today);
        // 获取上周一和上周日的日期
        $lastWeekMonday = clone $thisWeekMonday;
        $lastWeekMonday->modify('-1 week');
        $lastWeekSunday = clone $thisWeekSunday;
        $lastWeekSunday->modify('-1 week');
        // 获取下周一和下周日的日期
        $nextWeekMonday = clone $thisWeekMonday;
        $nextWeekMonday->modify('+1 week');
        $nextWeekSunday = clone $thisWeekSunday;
        $nextWeekSunday->modify('+1 week');

        $thisWeekMondayStr = $thisWeekMonday->format('Y-m-d');
        $thisWeekFridayStr = $thisWeekSunday->format('Y-m-d');
        $lastWeekMondayStr = $lastWeekMonday->format('Y-m-d');
        $lastWeekFridayStr = $lastWeekSunday->format('Y-m-d');
        $nextWeekMondayStr = $nextWeekMonday->format('Y-m-d');
        $nextWeekFridayStr = $nextWeekSunday->format('Y-m-d');
        return array(
            "this_start_date"=>$thisWeekMondayStr,
            "this_end_date"=>$thisWeekFridayStr,
            "last_start_date"=>$lastWeekMondayStr,
            "last_end_date"=>$lastWeekFridayStr,
            "next_start_date"=>$nextWeekMondayStr,
            "next_end_date"=>$nextWeekFridayStr,
        );
    }
    protected function getWorkDate($dateList)
    {
        $retList = array();
        $retList['this_start_date'] = $this->get_work_date_next($dateList['this_start_date']);
        $retList['this_end_date'] = get_work_day($dateList['this_end_date']);
        $retList['last_start_date'] = $this->get_work_date_next($dateList['last_start_date']);
        $retList['last_end_date'] = get_work_day($dateList['last_end_date']);
        return $retList;
    }
    /**
     * 获取 旬 月 年同比日期
     * Created by zfy.
     * Date:2023/1/10 11:02
     * @param $dateList
     * @return array
     */
    protected function getWeekMonthYearDate($dateList){
        $retList = array();
        $retList['thisDate'] = $dateList['this_end_date'];
        $retList['lastDate'] = $dateList['last_end_date'];
        $retList['lastMonth'] = date("Y-m-d",strtotime($dateList['this_end_date']."-1 month"));
        $retList['lastYear'] = date("Y-m-d",strtotime($dateList['this_end_date']."-1 year"));
        return $retList;
    }

    /**
     * 获取 旬 月末 年同比日期
     * Created by zfy.
     * Date:2023/1/10 11:02
     * @param $dateList
     * @return array
     * @throws Exception
     */
    protected function getWeekEndMonthYearDate($dateList){
        $date = new DateTime($dateList['this_end_date']);
        $retList = array();
        $retList['thisDate'] = $dateList['this_end_date'];
        $retList['lastDate'] = $dateList['last_end_date'];
        //上月末
        $retList['lastMonth'] = $date->modify('last day of last month')->format('Y-m-d');
        $retList['lastYear'] = date("Y-m-d",strtotime($dateList['this_end_date']."-1 year"));
        return $retList;
    }
    /**
     * 计算库存的 旬 月 年同比数据
     * Created by zfy.
     * Date:2023/1/10 11:59
     * @param $stockList
     * @return array
     */
    protected function calcStockData($stockList)
    {
        $retList = array();
        $retList['thisDate'] = $stockList['thisDate'];
        foreach ($stockList as $index => $item) {
            if ($index != 'thisDate') {
                foreach ($item as $type => $stock) {
                    $retList[$index][$type] = $this->zhangdie($stockList['thisDate'][$type] - $stock);
                }
            }
        }
        return $retList;
    }

    /**
     * 计算数据的 旬 月 年同比数据 多个数据
     * Created by zfy.
     * Date:2023/1/10 11:59
     * @param $stockList
     * @return array
     */
    protected function calcStockMoreData($stockList)
    {
        $retList = array();
        $retList['thisDate'] = $stockList['thisDate'];
        foreach ($stockList as $index => $item) {
            if ($index != 'thisDate') {
                foreach ($item as $type => $stock) {
                    foreach ($stock as $field => $v) {
                        $retList[$index][$type][$field] = $this->zhangdie($stockList['thisDate'][$type][$field] - $v);
                    }
                }
            }
        }
        return $retList;
    }
    /**
     * 计算优特钢库存的 旬 月 年同比数据 优特钢库存单位为万
     * Created by zfy.
     * Date:2023/1/10 11:59
     * @param $stockList
     * @return array
     */
    protected function calcYTGStockData($stockList)
    {
        $retList = array();
        $retList['thisDate'][6] = round($stockList['thisDate'][6] / 10000,2);
        foreach ($stockList as $index => $item) {
            if ($index != 'thisDate') {
                foreach ($item as $type => $stock) {
                    $retList[$index][$type] = $this->zhangdie(($stockList['thisDate'][$type] - $stock) / 10000);
                }
            }
        }
        return $retList;
    }

    /**
     * 单层数组计算 旬 月 年同比数据
     * Created by zfy.
     * Date:2024/5/21 17:15
     * @param $stockList
     * @param int $decimal 小数精度
     * @return array
     */
    protected function calcArrayData($stockList,$decimal = 2)
    {
        $retList = array();
        $retList['thisDate'] = $stockList['thisDate'];
        foreach ($stockList as $index => $item) {
            if ($index != 'thisDate') {
                $retList[$index] = $this->zhangdie($stockList['thisDate'] - $item , $decimal);
            }
        }
        return $retList;
    }

    public function run_create_dingjia_list($params){
        $type = $params["type"] == ""?1:$params["type"];
        $curr_date = $params["date"] == ""?date("Y-m-d"):$params["date"];
        $insert_sdate = "";
        $insert_edate = "";
        if($type == 1){
            //周定价
            $curr_day = strtotime($curr_date);
            $next_monday = date("Y-m-d",strtotime("next monday", $curr_day));
            $bz_eday = $this->get_xhdate($next_monday);
    
            $bz_sday = date('Y-m-d', strtotime('monday this week',$curr_day));
            // echo $bz_sday."--".$bz_eday;exit;
            if($bz_eday == $curr_date){
                $insert_sdate = $bz_sday;
                $insert_edate = $bz_eday;
            }
            
        }else if($type == 2){
            //旬定价
            $xun_day1 = date("Y-m-11",strtotime($curr_date));
            $shangxun_edate = $this->get_xhdate($xun_day1);
            if($shangxun_edate == $curr_date){
                $insert_sdate = date("Y-m-01",strtotime($curr_date));
                $insert_edate = date("Y-m-10",strtotime($curr_date));
            }
            $xun_day2 = date("Y-m-21",strtotime($curr_date));
            $zhongxun_edate = $this->get_xhdate($xun_day2);
            if($zhongxun_edate == $curr_date){
                $insert_sdate = date("Y-m-11",strtotime($curr_date));
                $insert_edate = date("Y-m-20",strtotime($curr_date));
            }
            $xun_eday3 = date("Y-m-01",strtotime($curr_date));
            $xun_day3 = date("Y-m-d",strtotime($xun_eday3."+1 month"));
            $xiaxun_edate = $this->get_xhdate($xun_day3);
            // echo $curr_date."--".$xiaxun_edate."--".$xun_day3;
            if($xiaxun_edate == $curr_date){
                $insert_sdate = date("Y-m-21",strtotime($curr_date));
                $xy_sdate = date("Y-m-d",strtotime(date("Y-m-01",strtotime($curr_date))."+1 month"));
                $insert_edate = date("Y-m-d",strtotime($xy_sdate."-1 day"));
            }
        }else if($type == 3){
            //月定价
            // $by_edate = date("Y-m-d",strtotime(date("Y-m-01",strtotime($curr_date))."+1 month"));
            $by_edate = date("Y-m-24",strtotime($curr_date));
            if(file_get_contents('https://holiday.steelhome.com/isholiday.php?date='.$by_edate)=="1"){
                $edate = $this->get_xhdate($by_edate);
            }else{
                $edate = $by_edate;
            }
            if($edate == $curr_date){
                $insert_sdate = date("Y-m-01",strtotime($curr_date));
                $insert_edate = date("Y-m-24",strtotime($by_edate."-1 day"));
            }
        }
        if($insert_sdate != "" && $insert_edate != ""){
            $ids = $this->drcdao->getOne("select id from sd_steel_dingjia_list where sdate = '".$insert_sdate."' and edate = '".$insert_edate."' and type = '".$type."'");
            if($ids == ""){
                $insert_sql = "insert into sd_steel_dingjia_list set sdate = '".$insert_sdate."' , edate = '".$insert_edate."', type = '".$type."',create_time = '".date("Y-m-d H:i:s")."'";
                // echo $insert_sql;
                $this->drcwdao->execute($insert_sql);
                echo "生成成功!";
            }else{
                echo "生成失败！";
            }
        }else{
            echo "生成失败！";
        }
    }

    public function dingjia_list($params){
        $mode = $params['mode'] == "" ? 1 : $params['mode'];
        $saving = $params['saving'];
        $adminid = $params['adminid'];
        $_SESSION['adminid'] = $adminid;
        if($saving=="1" && $adminid==""){
            echo "未登录";
            exit;
        }
	    $viewList = array("1"=>"zhou_dingjia","2"=>"xun_dingjia","3"=>"yue_market_price");
        $this->assign("type",$params['type']);
        $this->assign("mode",$mode);
        $this->assign("saving",$saving);
        $this->assign("viewName",$viewList[$params['type']]);
    }
    public function ajax_dingjia_list($params){
        $page = $params['page'];
        $saving = $params['saving'];
        $limit = $params['limit'] == "" ? 10 : $params['limit'];
        if ($page < 1) {
            $page = 1;
        }
        $start = ($page - 1) * $limit;
        if($saving=="1"){
            $sdate = date("Y-m-d",strtotime("-3 month"));
            $dataInfo = $this->drcwdao->query("select * from sd_steel_dingjia_list where type = '".$params['type']."' and sdate>='".$sdate."' order by edate desc ");
        }else{
            $total = count($this->drcwdao->query("select * from sd_steel_dingjia_list where type = '".$params['type']."' order by edate"));
            $dataInfo = $this->drcwdao->query("select * from sd_steel_dingjia_list where type = '".$params['type']."' order by edate desc limit  $start,$limit");
        }

        $typeList = array('1'=>"周定价",'2'=>"旬定价",'3'=>"月定价");
        $typeList_marketNews = array('1'=>"5",'2'=>"4",'3'=>"6");
        $data = array();
        foreach ($dataInfo as $tmp){
            $edate = $tmp['edate'];
            if($params['type'] == "3"){
                $tmp['yc_varietyname'] = date("Y年n月",strtotime($tmp['edate']." +1 day")).$typeList[$params['type']];
                $edate = date("Y-m-t",strtotime($edate));
            }elseif ($params['type'] == "2"){
                $tmp['yc_varietyname'] = date("Y年n月j日",strtotime($tmp['edate'])).$typeList[$params['type']];
            }else{
                $tmp['yc_varietyname'] = date("Y年n月j日",strtotime($tmp['edate'])).$typeList[$params['type']];
            }
            if($saving=="1") {
                $tmp['adminid'] = $_SESSION['adminid'];
                $arr = array();
                $arr['type'] = $typeList_marketNews[$params['type']];
                $arr['sdate'] = $tmp['sdate'];
                $arr['edate'] = $edate;
                $state = $this->getMarketNews($arr);
                if ($state == 1) {
                    continue;
                }
            }
            $data[] = $tmp;
        }
        $code = 0;
        if ($data) {
            $code = 0;
        }
        if($saving=="1") {
            $total = count($data);
        }
        $return_array = array(
            "code" => $code,
            "data" => $data,
            "count" => $total
        );
        echo json_encode($return_array);
    }

    public function get_yc_gc_table_tr($ret_arr,$gc_yue_yc_data,$is_xz=0){
        $gc_yc_price_str = "";
        $td_str = "预测价格";
        if($is_xz == 1)
             $td_str = "预测价格（修正）";
        if(!empty($ret_arr) && !empty($gc_yue_yc_data)){
            $gc_yc_price_str = "<tr>
                                    <td>".$td_str."</td>
                                    <td>".($ret_arr['lprice']['houban_price'] + ($gc_yue_yc_data['厚板']['yc_zd']))."</td>
                                    <td>".($ret_arr['lprice']['rzbj_price'] + ($gc_yue_yc_data['热卷']['yc_zd']))."</td>
                                    <td>".($ret_arr['lprice']['lzbj_price'] + ($gc_yue_yc_data['冷卷']['yc_zd']))."</td>
                                    <td>".($ret_arr['lprice']['suanxi_price'] + ($gc_yue_yc_data['酸洗']['yc_zd']))."</td>
                                    <td>".($ret_arr['lprice']['duxin_price'] + ($gc_yue_yc_data['镀锌']['yc_zd']))."</td>
                                    <td>".($ret_arr['lprice']['lw_price'] + ($gc_yue_yc_data['螺纹钢']['yc_zd']))."</td>
                                    <td>".($ret_arr['lprice']['h_xg_price'] + ($gc_yue_yc_data['H型钢']['yc_zd']))."</td>
                                    <td>".($ret_arr['lprice']['tjg_price'] + ($gc_yue_yc_data['碳结钢']['yc_zd']))."</td>
                                    <td>".($ret_arr['lprice']['fp_price'] + ($gc_yue_yc_data['方坯']['yc_zd']))."</td>
                                </tr>";
        }
        
        return $gc_yc_price_str;
    }

    public function get_yc_ll_table_tr($ret_arr,$ll_yue_yc_data,$is_xz=0,$is_zyc=0){
        $ll_yc_price_str = "";
        $td_str = "预测价格";
        $td_str2 = "";
        if($is_xz == 1)
             $td_str = "预测价格（修正）";
        if($is_zyc == 1)
             $td_str2 = "<td>--</td>";
        if(!empty($ret_arr) && !empty($ll_yue_yc_data)){
            $ll_yc_price_str = "<tr>
                                    <td>".$td_str."</td>
                                    <td>".($ret_arr['lprice']['tks_price'] + ($ll_yue_yc_data['铁矿石']['yc_zd']))."</td>
                                    ".$td_str2."
                                    <td>".($ret_arr['lprice']['fg_price'] + ($ll_yue_yc_data['废钢']['yc_zd']))."</td>
                                    <td>".($ret_arr['lprice']['gm_price'] + ($ll_yue_yc_data['硅锰']['yc_zd']))."</td>
                                    <td>".($ret_arr['lprice']['gt_price'] + ($ll_yue_yc_data['硅铁']['yc_zd']))."</td>
                                    <td>".($ret_arr['lprice']['gtmt_price'] + ($ll_yue_yc_data['高锰']['yc_zd']))."</td>
                                    <td>".($ret_arr['lprice']['nieban_price'] + ($ll_yue_yc_data['镍板']['yc_zd']))."</td>
                                    <td>".($ret_arr['lprice']['qimei_price'] + ($ll_yue_yc_data['气煤']['yc_zd']))."</td>
                                    <td>".($ret_arr['lprice']['jiaomei_price'] + ($ll_yue_yc_data['1/3焦煤']['yc_zd']))."</td>
                                    <td>".($ret_arr['lprice']['dljm_price'] + ($ll_yue_yc_data['主焦煤']['yc_zd']))."</td>
                                    <td>".($ret_arr['lprice']['shoumei_price'] + ($ll_yue_yc_data['瘦煤']['yc_zd']))."</td>
                                    <td>".($ret_arr['lprice']['zyjj_price'] + ($ll_yue_yc_data['冶金焦']['yc_zd']))."</td>
                                </tr>";
        }
        
        return $ll_yc_price_str;
    }
    public function exportPdf($params)
    {
//        exec('node /usr/local/www/dc.steelhome.cn/v1.5/pdf-export/js/pdf_export.js "https://dc.steelhome.com/v1.5/web/sdgt_market_yc.php?view=week_market_yc&edate=2024-10-07&mode=2" "/tmp/sgpdf/output_6757dd5240bd3.pdf"', $output, $return_code);
//        echo "<pre>output=";print_r($output);
//        echo "<pre>return_code=";print_r($return_code);
//        exit();
        try {
            // 设置必要的环境变量和路径
            $nodeScript = '/usr/local/www/dc.steelhome.cn/v1.5/pdf-export/js/pdf_export.js';
            $outputDir = '/tmp/sgpdf';

            // 确保输出目录存在
            if (!is_dir($outputDir)) {
                error_log("Creating output directory: " . $outputDir);
                if (!mkdir($outputDir, 0755, true)) {
                    error_log("Failed to create output directory");
                    throw new Exception("Failed to create output directory");
                }
            }

            // 生成唯一的输出文件名
            $outputFileName = 'output_' . date('Ymd_His') . '_' . uniqid() . '.pdf';
            $outputPath = $outputDir . '/' . $outputFileName;

            // 构建URL（从参数中获取日期）
            $sdate = $params['sdate'] ?? date('Y-m-d');
            $edate = $params['edate'] ?? date('Y-m-d');
            $mode = $params['mode'] ?? '102';//不能用2，2表示安卓手机版
            $view = $params['view_type'] ?? 'day_market_yc';
            $url = DC_DOMAIN_URL."/v1.5/web/sdgt_market_yc.php?view={$view}&edate={$edate}&sdate={$sdate}&mode={$mode}";
//            $url = "https://dc.steelhome.com/v1.5/web/sdgt_market_yc.php?view=day_market_yc&sdate={$edate}&mode={$mode}";
//            $url = $params['url'];
            error_log("Generated URL: " . $url);
            error_log("Output path: " . $outputPath);

            // 检查 Node.js 模块是否安装
            $nodeModulesPath = dirname($nodeScript) . '/node_modules';
            if (!is_dir($nodeModulesPath)) {
                error_log("Node modules directory not found at: " . $nodeModulesPath);
                throw new Exception("Required Node.js modules not installed");
            }

            // 设置环境变量
            $env = [
                'PATH' => '/usr/local/bin:/usr/bin:/bin',
                'NODE_PATH' => $nodeModulesPath,
                'PUPPETEER_SKIP_CHROMIUM_DOWNLOAD' => 'true',  // 使用系统安装的 Chrome
                'NODE_ENV' => 'production'
            ];

            //处理页眉标题
            switch ($view){
                case "day_market_yc":
                    $headerTitle = "<b>".date("Y年n月j日",strtotime($this->get_xhdate2($sdate)))."分析预测</b>";
                    break;
                case "week_market_yc":
                    //周日期
                    $weekDateList = $this->getWeekDate($edate);
                    //工作日
                    $workDate = $this->getWorkDate($weekDateList);
                    $headerTitle = "<div><b>".date("Y年",strtotime($workDate['this_end_date']))."周分析预测</b></div><div style='font-size: 12px'>(".date("n月j日",strtotime($workDate['this_start_date']))." - ".date("n月j日",strtotime($workDate['this_end_date'])).")</div>";
                    break;
                case "yue_market_yc":
                    $headerTitle = "<div><b>".date("Y年n月",strtotime($edate." +1 day"))."分析预测</b></div><div style='font-size: 12px'>(".date("n月j日",strtotime($sdate))." - ".date("n月j日",strtotime($edate)).")</div>";
                    break; 
                case "zhou_dingjia":
                    $headerTitle = "<b>".date("Y年n月j日",strtotime($this->get_xhdate2($edate)))."周定价</b>";
                    break; 
                case "xun_dingjia":
                    $headerTitle = "<b>".date("Y年n月j日",strtotime($this->get_xhdate2($edate)))."旬定价</b>";
                    break;
                case "yue_market_price":
                    $headerTitle = "<b>".date("Y年n月",strtotime($this->get_xhdate2($edate)))."月定价</b>";
                    break;
            }

            $imgtype = $params['imgType'] ?? "1";
            
            // 构建命令
            $command = sprintf(
                '/usr/bin/node %s %s %s %s %s 2>&1',
                escapeshellarg($nodeScript),
                escapeshellarg($url),
                escapeshellarg($outputPath),
                escapeshellarg($headerTitle),
                escapeshellarg($imgtype)
            );

            error_log("Executing command with environment:");
            error_log("Environment: " . print_r($env, true));
            error_log("Command: " . $command);

            // 使用 proc_open 执行命令以获取更详细的输出
            $descriptorspec = [
                0 => ["pipe", "r"],  // stdin
                1 => ["pipe", "w"],  // stdout
                2 => ["pipe", "w"]   // stderr
            ];

            $cwd = dirname($nodeScript);  // 设置工作目录
            error_log("Working directory: " . $cwd);

            $process = proc_open($command, $descriptorspec, $pipes, $cwd, $env);

            if (is_resource($process)) {
                // 读取输出
                $stdout = stream_get_contents($pipes[1]);
                $stderr = stream_get_contents($pipes[2]);

                // 关闭管道
                fclose($pipes[0]);
                fclose($pipes[1]);
                fclose($pipes[2]);

                // 获取进程退出码
                $returnCode = proc_close($process);

                // 记录执行结果
                error_log("PDF Generation Command output:");
                error_log("Return Code: " . $returnCode);
                error_log("STDOUT: " . $stdout);
                error_log("STDERR: " . $stderr);

                if ($returnCode === 0 && file_exists($outputPath)) {
//                    error_log("PDF generated successfully at: " . $outputPath);
                    // PDF生成成功，设置响应头并输出文件
                    header('Content-Type: application/pdf');
                    header('Content-Disposition: attachment; filename="' . $outputFileName . '"');
                    header('Content-Length: ' . filesize($outputPath));

                    readfile($outputPath);
                    unlink($outputPath); // 清理临时文件
                    exit;
                    // 读取PDF文件内容
//                    $pdfContent = file_get_contents($outputPath);

                    // 清理临时PDF文件
//                    unlink($outputPath);
//
//                    // 设置响应头
//                    header('Content-Type: application/pdf');
//                    header('Content-Disposition: attachment; filename="市场研究_' . date('Y-m-d') . '.pdf"');
//                    header('Cache-Control: no-cache, must-revalidate');
//                    header('Pragma: no-cache');
//                    header('Content-Length: ' . strlen($pdfContent));
//
//                    // 输出PDF内容
//                    echo $pdfContent;
//                    exit;
                } else {
                    error_log("PDF generation failed. File exists check: " . (file_exists($outputPath) ? 'true' : 'false'));
                    throw new Exception("PDF generation failed with code $returnCode\nSTDOUT: $stdout\nSTDERR: $stderr");
                }
            } else {
                throw new Exception("Failed to start PDF generation process");
            }
        } catch (Exception $e) {
            error_log("PDF Generation Error: " . $e->getMessage());
            error_log("Error trace: " . $e->getTraceAsString());
            header('Content-Type: application/json');
            echo json_encode([
                'success' => false,
                'message' => 'PDF generation failed: ' . $e->getMessage()
            ]);
        }
    }

    public function datacenter_manage($params){
        $type = $params['type'];
        $mode = $params['mode'];
        $sdate = $params['sdate']=="" ? date("Y-m-01",strtotime("-16 month")) : $params['sdate'];
        $edate = $params['edate']=="" ? date("Y-m-d") : $params['edate'];

        $sdate2 = date("Y-01-01",strtotime($sdate));
        $sdate2 = date("Y-01-01",strtotime("-2 day".$sdate2));
        $edate2 = date("Y-m-t",strtotime("-2 day".$sdate2));

        //固定资产投资取L2Data表L2id
        $L2Data_arr = array(
            "1"=>array("title"=>"GDP","id"=>"625"),
            "2"=>array("title"=>"固定资产投资","id"=>"140"),
            "3"=>array("title"=>"工业增加值","id"=>"142"),
            "4"=>array("title"=>"社会消费品零售总额","id"=>"144"),
            "5"=>array("title"=>"进出口总额","id"=>"547,548"),
            "6"=>array("title"=>"CPI与PPI","id"=>"117,145"),
            "7"=>array("title"=>"社融增量","id"=>"235"),
            "8"=>array("title"=>"人民币贷款","id"=>"235"),
            "9"=>array("title"=>"制造业PMI","id"=>"152,634"),
            "10"=>array("title"=>"建筑业PMI","id"=>"602"),
            "11"=>array("title"=>"美元指数","id"=>"237"),
            "12"=>array("title"=>"人民币兑美元汇率（中间价）","id"=>"218"),
            "13"=>array("title"=>"美国联邦基金利率","id"=>"474"),
            "14"=>array("title"=>"美国CPI走势","id"=>"657"),
            "15"=>array("title"=>"全球制造业PMI","id"=>""),
            "16"=>array("title"=>"主要发达国家GDP","id"=>"344,345"),
            "17"=>array("title"=>"钢材价格日报表","id"=>"")
        );

        $L2id = $L2Data_arr[$type]['id'];
        $title = $L2Data_arr[$type]['title'];
        $arr = array();
        $arr['id'] = $L2id;
        $arr['title'] = $title;
        $arr['sdate'] = $sdate;
        $arr['edate'] = $edate;
        $arr['sdate2'] = $sdate2;
        $arr['edate2'] = $edate2;
        if( $type == "1" ) {
            $sdate = $params['sdate']=="" ? date("Y-m-d",strtotime("-2 year")) : $params['sdate'];
            $arr['sdate'] = $sdate;
            $table_tr = $this->get_l2data1($arr);
        }else if( $type == "2" ) {
            $table_tr = $this->get_l2data2($arr);
        }else if( $type == "3" ) {
            $table_tr = $this->get_l2data3($arr);
        }else if( $type == "4" ) {
            $table_tr = $this->get_l2data4($arr);
        }else if( $type == "5" ) {
            $table_tr = $this->get_l2data5($arr);
        }else if( $type == "6" ) {
            $table_tr = $this->get_l2data6($arr);
        }else if( $type == "7" ) {
            $table_tr = $this->get_l2data7($arr);
        }else if( $type == "8" ) {
            $table_tr = $this->get_l2data8($arr);
        }else if( $type == "9" ) {
            $table_tr = $this->get_l2data9($arr);
        }else if( $type == "10" ) {
            $table_tr = $this->get_l2data10($arr);
        }else if( $type == "11" ) {
            $sdate = $params['sdate']=="" ? date("Y-m-d",strtotime("-1 month")) : $params['sdate'];
            $arr['sdate'] = $sdate;
            $table_tr = $this->get_l2data11($arr);
        }else if( $type == "12" ) {
            $sdate = $params['sdate']=="" ? date("Y-m-d",strtotime("-1 month")) : $params['sdate'];
            $arr['sdate'] = $sdate;
            $table_tr = $this->get_l2data12($arr);
        }else if( $type == "13" ) {
            //$sdate = $params['sdate']=="" ? date("Y-m-d",strtotime("-1 month")) : $params['sdate'];
            $arr['sdate'] = $sdate;
            $table_tr = $this->get_l2data13($arr);
        }else if( $type == "14" ) {
            $table_tr = $this->get_l2data14($arr);
        }else if( $type == "15" ) {
            $table_tr = $this->get_l2data15($arr);
        }else if( $type == "16" ) {
            $sdate = $params['sdate']=="" ? date("Y-m-d",strtotime("-5 year")) : $params['sdate'];
            $arr['sdate'] = $sdate;
            $table_tr = $this->get_l2data16($arr);
        }else if( $type == "17" ) {
            $sdate = $params['sdate']=="" ? date("Y-m-d",strtotime("-1 month")) : $params['sdate'];
            $arr['sdate'] = $sdate;
            $table_tr = $this->get_l2data17($arr);
        }

        $this->assign("title",$title);
        $this->assign("table_tr",$table_tr);
        $this->assign("sdate",$sdate);
        $this->assign("edate",$edate);
        $this->assign("mode",$mode);
        $this->assign("params",$params);
    }

    public function get_l2data17($params){

        $L2id = $params['id'];
        $sdate = $params['sdate'];
        //$sdate = date("Y-m-d",strtotime("-1 month".$params['edate']));
        $edate = $params['edate'];

        $sql = "SELECT dateday,mindex,price FROM `shpi_material` WHERE topicture='50' and dateday >= '" . $sdate . "' and dateday <= '" . $edate . "' ORDER BY dateday DESC";
        $list = $this->maindao->query($sql);

        $table_tr = "<thead><tr id='tr_title' style='display: none;'><td colspan='3'>".$params['title']."</td></tr><tr><td>日期</td><td>综合铁水成本指数（点）</td><td>综合铁水成本价格（元/吨）</td></tr></thead>";
        foreach ($list as $key=>$item) {

            $table_tr .= "<tr><td>" . $item['dateday'] . "</td><td>" . $item['mindex'] . "</td><td>" . $item['price'] . "</td></tr>";
        }

        return $table_tr;
    }

    public function get_l2data16($params){

        $L2id = $params['id'];
        //$sdate = date("Y-m-d",strtotime("-3 year".$params['sdate']));
        $sdate = $params['sdate'];
        $edate = $params['edate'];

        $sql = "SELECT * FROM `L2Data` WHERE `L2Id` in (" . $L2id . ") and DATE >= '" . $sdate . "' and DATE <= '" . $edate . "' ORDER BY `L2Data`.`DATE` DESC";
        $list = $this->maindao->query($sql);
        $list_1 = array();
        foreach ($list as $item) {
            $list_1[$item['L2Id']][] = $item;
        }

        $data = array();
        foreach ($list_1 as $key1 => $value) {
            foreach ($value as $key => $item) {

                $arr = array();
                $arr['DATE'] = $item['DATE'];
                $arr['D1'] = $item['D1'];
                $arr['D2'] = $item['D2'];
                $arr['D3'] = $item['D3'];
                $arr['D4'] = $item['D4'];

                $data[$arr['DATE']][$item['L2Id']] = $arr;
            }
        }

        $table_tr = "<thead><tr id='tr_title' style='display: none;'><td colspan='6'>".$params['title']."</td></tr><tr><td>日期</td><td>世界</td><td>中国</td><td>美国</td><td>日本</td><td>欧元区</td></tr></thead>";
        foreach ($data as $key=>$item) {
            $table_tr .= "<tr><td>" . $key . "</td><td>" . $item['345']['D1'] . "</td><td>" . $item['344']['D2'] . "</td><td>" . $item['344']['D1'] . "</td><td>" . $item['344']['D3'] . "</td><td>" . $item['344']['D4'] . "</td></tr>";
        }

        return $table_tr;
    }

    public function get_l2data15($params){

        $L2id = $params['id'];
        $sdate = $params['sdate'];
        $edate = $params['edate'];

        $sql = "SELECT dta_ym,dta_1,dta_2 FROM `data_table` WHERE dta_type='PMCW' and dta_ym >= '" . $sdate . "' and dta_ym <= '" . $edate . "' ORDER BY `dta_ym` DESC";
        $list = $this->drcdao->query($sql);
        $data = array();
        foreach ($list as $item) {
            $data[$item['dta_ym']][$item['dta_1']] = $item['dta_2'];
        }

        $table_tr = "<thead><tr id='tr_title' style='display: none;'><td colspan='7'>".$params['title']."</td></tr><tr><td>日期</td><td>全球制造业</td><td>中国</td><td>美国</td><td>欧元区</td><td>英国</td><td>日本</td></tr></thead>";
        foreach ($data as $key=>$item) {
            $table_tr .= "<tr><td>" . $key . "</td><td>" . $item['世界'] . "</td><td>" . $item['中国（CFLP）'] . "</td>";
            $table_tr .= "<td>" . $item['美国ISM'] . "</td><td>" . $item['欧元区'] . "</td><td>" . $item['英国'] . "</td><td>" . $item['日本'] . "</td></tr>";
        }

        return $table_tr;
    }

    public function get_l2data14($params){

        $L2id = $params['id'];
        $sdate = $params['sdate'];
        $edate = $params['edate'];

        $sql = "SELECT * FROM `L2Data` WHERE `L2Id` = '" . $L2id . "' and DATE >= '" . $sdate . "' and DATE <= '" . $edate . "' ORDER BY `L2Data`.`DATE` DESC";
        $list = $this->maindao->query($sql);

        $table_tr = "<thead><tr id='tr_title' style='display: none;'><td colspan='3'>".$params['title']."</td></tr><tr><td>日期</td><td>CPI环比（%）</td><td>CPI同比（%）</td></tr></thead>";
        foreach ($list as $key=>$item) {
            $item['DATE'] = date("Y年n月",strtotime($item['DATE']));
            $table_tr .= "<tr><td>" . $item['DATE'] . "</td><td>" . $item['D1'] . "</td><td>" . $item['D2'] . "</td></tr>";
        }

        return $table_tr;
    }

    public function get_l2data13($params){

        $L2id = $params['id'];
        $sdate3 = date("Y-m-d",strtotime("-3 year".$params['sdate']));
        $sdate1 = date("Y-m-d",strtotime("-1 year".$params['sdate']));
        $edate2 = $params['edate'];
        $sdate2 = $params['sdate'];


        $sql = "SELECT DATE,D1 FROM `L2Data` WHERE `L2Id` = '" . $L2id . "' and DATE >= '" . $sdate3 . "' and DATE <= '" . $edate2 . "' ORDER BY `L2Data`.`DATE` ASC";
        $list = $this->maindao->query($sql);
        $data_pre = array();
        foreach ($list as $item) {
            $data_pre[$item['DATE']] = $item;
        }
        $mlc = array();
        while (strtotime($sdate3)<=strtotime($edate2)){
            if(isset($data_pre[$sdate3])){
                $mlc = $data_pre[$sdate3];
            }else{
                $data_pre[$sdate3] = $mlc;
            }
            $sdate3 = date('Y-m-d', strtotime('+1 day', strtotime($sdate3)));
            //break;
        }
        $data = array();
        foreach ($list as $key=>$item){

            if(strtotime($item['DATE'])<strtotime($params['sdate'])){
                continue;
            }

            $key_tong = date("Y-", strtotime("-1 year" . $item['DATE'])) . date("m-d", strtotime($item['DATE']));
            $key_huan = date("Y-m-d", strtotime("-1 day" . $item['DATE']));

            $arr = array();
            $arr['DATE'] = $item['DATE'];
            $arr['value'] = $item['D1'];

            $arr['huan'] = $data_pre[$key_huan]['D1'] ?? 0;
            $arr['huan'] = $this->calculate_num($arr['value'], $arr['huan']);

            $arr['tong'] = $data_pre[$key_tong]['D1'] ?? 0;
            $arr['tong'] = $this->calculate_num($arr['value'], $arr['tong']);

            $data[] = $arr;
        }
        $data = array_reverse($data);
        $table_tr = "<thead><tr id='tr_title' style='display: none;'><td colspan='4'>".$params['title']."</td></tr><tr><td>日期</td><td>美国联邦基金利率（%）</td><td>环比（%）</td><td>同比（%）</td></tr></thead>";
        foreach ($data as $key=>$item) {
            $table_tr .= "<tr><td>" . $item['DATE'] . "</td><td>" . $item['value'] . "</td><td>" . $item['huan'] . "</td><td>" . $item['tong'] . "</td></tr>";
        }

        return $table_tr;
    }

    public function get_l2data12($params){

        $L2id = $params['id'];
        $sdate = $params['sdate'];
        $edate = $params['edate'];
        $sdate2 = date("Y-m-d",strtotime("-1 month".$params['sdate2']));
        $edate2 = $params['edate'];

        $sql = "SELECT * FROM `L2Data` WHERE `L2Id` = '" . $L2id . "' and DATE >= '" . $sdate . "' and DATE <= '" . $edate . "' ORDER BY `L2Data`.`DATE` ASC";
        $list = $this->maindao->query($sql);

        //上一年
        $sql = "SELECT * FROM `L2Data` WHERE `L2Id` = '" . $L2id . "' and DATE >= '" . $sdate2 . "' and DATE <= '" . $edate2 . "' ORDER BY `L2Data`.`DATE` ASC";
        $list2 = $this->maindao->query($sql);
        $data_pre = array();
        foreach ($list2 as $item) {
            $data_pre[$item['DATE']] = $item;
        }
        $sdate3 = $params['sdate2'];
        $item = array();
        while (strtotime($sdate3)<=strtotime($edate2)){
            if(isset($data_pre[$sdate3])){
                $item = $data_pre[$sdate3];
            }else{
                $data_pre[$sdate3] = $item;
            }
            $sdate3 = date('Y-m-d', strtotime('+1 day', strtotime($sdate3)));
            //break;
        }

        //echo "<pre>";
        //print_r($data_pre);

        $data = array();
        foreach ($list as $key => $item) {
            if($item['DATE']<$params['sdate']){
                continue;
            }
            $key_tong = date("Y-", strtotime("-1 year" . $item['DATE'])) . date("m-d", strtotime($item['DATE']));
            $key_huan = date("Y-m-d", strtotime("-1 day" . $item['DATE']));

            $arr = array();
            $arr['DATE'] = $item['DATE'];
            $arr['value'] = $item['D1'];

            //$huan = "##".$arr['huan'];
            $arr['huan'] = $data_pre[$key_huan]['D1'] ?? 0;
            $arr['huan'] = $this->calculate_num($arr['value'], $arr['huan'],"3");

            $arr['tong'] = $data_pre[$key_tong]['D1'] ?? 0;

            //$tong = "##".$arr['tong'];
            $arr['tong'] = $this->calculate_num($arr['value'], $arr['tong'],"3");
            //$arr['value'] .= $tong;

            $data[] = $arr;

        }
        $data = array_reverse($data);
        $table_tr = "<thead><tr id='tr_title' style='display: none;'><td colspan='4'>".$params['title']."</td></tr><tr><td>日期</td><td>人民币兑美元</td><td>环比（%）</td><td>同比（%）</td></tr></thead>";
        foreach ($data as $item) {
            $table_tr .= "<tr>";
            $table_tr .= "<td>" . $item['DATE'] . "</td>";
            $table_tr .= "<td>" . $item['value'] . "</td>";
            $table_tr .= "<td>" . $item['huan'] . "</td>";
            $table_tr .= "<td>" . $item['tong'] . "</td>";
            $table_tr .= "</tr>";
        }

        return $table_tr;
    }

    public function get_l2data11($params){

        $L2id = $params['id'];
        $sdate = date("Y-m-d",strtotime("-1 month".$params['sdate']));
        $edate = $params['edate'];
        //$sdate2 = $params['sdate2'];
        //$edate2 = $params['edate2'];
        $sdate2 = date("Y-m-d",strtotime("-1 month".$params['sdate2']));
        $edate2 = $params['edate'];

        //$edate3 = date("Y-m-d",strtotime("-1 month".$params['edate']));

        $sql = "SELECT * FROM `L2Data` WHERE `L2Id` = '" . $L2id . "' and DATE >= '" . $sdate . "' and DATE <= '" . $edate . "' ORDER BY `L2Data`.`DATE` ASC";
        $list = $this->maindao->query($sql);
        $list_1 = array();
        $list_2 = array();
        $list_3 = array();
        $list_4 = array();
        foreach ($list as $item) {
            $weeks = date('YW',strtotime($item['DATE']));
            //$weeks = (float)$weeks;
            $months = date('Ym',strtotime($item['DATE']));
            $list_1[$weeks][] = round($item['D1'],2);
            $list_2[$item['DATE']] = $list_1[$weeks];

            $list_3[$months][] = round($item['D1'],2);
            $list_4[$item['DATE']] = $list_3[$months];
        }
        $week_avg = array();
        foreach ($list_2 as $key=>$tmp) {
            $week_avg[] = round(array_sum($tmp) / count($tmp), 2);
        }
        $months_avg = array();
        foreach ($list_4 as $key=>$tmp) {
            $months_avg[] = round(array_sum($tmp)/count($tmp),2);
        }
        //echo "<pre>";
        //print_r($list_1);
        //print_r($list_2);

        //上一年
        $sql = "SELECT * FROM `L2Data` WHERE `L2Id` = '" . $L2id . "' and DATE >= '" . $sdate2 . "' and DATE <= '" . $edate2 . "' ORDER BY `L2Data`.`DATE` ASC";
        $list2 = $this->maindao->query($sql);
        $data_pre = array();
        $list2_1 = array();
        $list2_3 = array();
        foreach ($list2 as $item) {
            $weeks = date('YW',strtotime($item['DATE']));
            $months = date('Ym',strtotime($item['DATE']));

            $list2_1[$weeks][] = round($item['D1'],2);
            $list2_3[$months][] = round($item['D1'],2);

            $data_pre[$item['DATE']] = $item;
        }
        $week_avg2 = array();
        foreach ($list2_1 as $key=>$tmp) {
            $week_avg2[$key] = round(array_sum($tmp) / count($tmp), 2);
        }
        $months_avg2 = array();
        foreach ($list2_3 as $key=>$tmp) {
            $months_avg2[$key] = round(array_sum($tmp)/count($tmp),2);
        }

        $sdate3 = $params['sdate2'];
        $item = array();
        while (strtotime($sdate3)<=strtotime($edate2)){
            if(isset($data_pre[$sdate3])){
                $item = $data_pre[$sdate3];
            }else{
                $data_pre[$sdate3] = $item;
            }
            $sdate3 = date('Y-m-d', strtotime('+1 day', strtotime($sdate3)));
            //break;
        }

        $data = array();
        foreach ($list as $key => $item) {
            if($item['DATE']<$params['sdate']){
                continue;
            }
            $key_tong = date("Y-", strtotime("-1 year" . $item['DATE'])) . date("m-d", strtotime($item['DATE']));
            $key_huan = date("Y-m-d", strtotime("-1 day" . $item['DATE']));

            $weeks = date('W',strtotime($item['DATE']));

            $arr = array();
            $arr['DATE'] = $item['DATE'];
            $arr['value'] = round($item['D1'],2);

            $arr['huan'] = $data_pre[$key_huan]['D1'] ?? 0;
            $arr['huan'] = $this->calculate_num($arr['value'], $arr['huan'],"3");

            $arr['tong'] = $data_pre[$key_tong]['D1'] ?? 0;
            $arr['tong'] = $this->calculate_num($arr['value'], $arr['tong'],"3");

            if( $weeks=="01" ){
                $weeks = date('YW', strtotime("-7 day".$item['DATE']));
            }else{
                $W = date('W', strtotime($item['DATE']));
                $W -= 1;
                if ($W<10){
                    $W = "0".$W;
                }
                $weeks = date('Y', strtotime($item['DATE'])).$W;
            }
            $arr['week_huan'] = $week_avg2[$weeks];
            //$week_huan = "##".$arr['week_huan']."##".$weeks;
            $arr['week_avg'] = $week_avg[$key];//周均值
            $arr['week_huan'] = $this->calculate_num($arr['week_avg'], $arr['week_huan'],"3");
            //$arr['week_avg'] .= $week_huan;

            $months = date('Ym',strtotime("-1 month".$item['DATE']));
            $arr['month_huan'] = $months_avg2[$months];

            $arr['month_avg'] = $months_avg[$key];//月均值
            $arr['month_huan'] = $this->calculate_num($arr['month_avg'], $arr['month_huan'],"3");

            //if($arr['DATE']>=$edate3)
            $data[] = $arr;
        }
        $data = array_reverse($data);
        $table_tr = "<thead><tr id='tr_title' style='display: none;'><td colspan='8'>".$params['title']."</td></tr><tr><td>日期</td><td>美元指数</td><td>环比（%）</td><td>同比（%）</td><td>周均值</td><td>环比（%）</td><td>月均值</td><td>环比（%）</td></tr></thead>";
        foreach ($data as $item) {
            $table_tr .= "<tr>";
            $table_tr .= "<td>" . $item['DATE'] . "</td>";
            $table_tr .= "<td>" . $item['value'] . "</td>";
            $table_tr .= "<td>" . $item['huan'] . "</td>";
            $table_tr .= "<td>" . $item['tong'] . "</td>";
            $table_tr .= "<td>" . $item['week_avg'] . "</td>";
            $table_tr .= "<td>" . $item['week_huan'] . "</td>";
            $table_tr .= "<td>" . $item['month_avg'] . "</td>";
            $table_tr .= "<td>" . $item['month_huan'] . "</td>";
            $table_tr .= "</tr>";
        }

        return $table_tr;
    }
    public function get_l2data10($params){

        $L2id = $params['id'];
        $sdate = $params['sdate'];
        $edate = $params['edate'];

        $sql = "SELECT * FROM `L2Data` WHERE `L2Id` = '" . $L2id . "' and DATE >= '" . $sdate . "' and DATE <= '" . $edate . "' ORDER BY `L2Data`.`DATE` DESC";
        $list = $this->maindao->query($sql);

        $table_tr = "<thead><tr id='tr_title' style='display: none;'><td colspan='7'>".$params['title']."</td></tr><tr><td>日期</td><td>建筑业PMI</td><td>新订单指数</td><td>投入品价格</td><td>销售价格</td><td>从业人员</td><td>业务活动预期</td></tr></thead>";
        foreach ($list as $key=>$item) {
            $table_tr .= "<tr><td>" . $item['DATE'] . "</td><td>" . $item['D1'] . "</td><td>" . $item['D2'] . "</td><td></td><td></td><td>" . $item['D3'] . "</td><td>" . $item['D4'] . "</td></tr>";
        }

        return $table_tr;
    }
    public function get_l2data9($params){

        $L2id = $params['id'];
        $sdate = $params['sdate'];
        $edate = $params['edate'];
        $sdate2 = $params['sdate2'];
        $edate2 = $params['edate2'];

        $sql = "SELECT * FROM `L2Data` WHERE `L2Id` in (" . $L2id . ") and DATE >= '" . $sdate . "' and DATE <= '" . $edate . "' ORDER BY `L2Data`.`DATE` DESC";
        $list = $this->maindao->query($sql);
        $list_1 = array();
        foreach ($list as $item) {
            $list_1[$item['L2Id']][] = $item;
        }

        $data = array();
        foreach ($list_1 as $key1 => $value) {
            foreach ($value as $key => $item) {

                $arr = array();
                $arr['DATE'] = $item['DATE'];
                $arr['D1'] = $item['D1'];
                $arr['D2'] = $item['D2'];
                $arr['D3'] = $item['D3'];

                $data[$arr['DATE']][$item['L2Id']] = $arr;
            }
        }

        $table_tr = "<thead><tr id='tr_title' style='display: none;'><td colspan='7'>".$params['title']."</td></tr><tr><td>日期</td><td>PMI</td><td>新订单</td><td>新出口订单</td><td>生产</td><td>产成品库存</td><td>原材料库存</td></tr></thead>";
        foreach ($data as $key=>$item) {
            $table_tr .= "<tr><td>" . $key . "</td><td>" . $item['152']['D1'] . "</td><td>" . $item['634']['D2'] . "</td><td>" . $item['634']['D3'] . "</td><td>" . $item['634']['D1'] . "</td><td></td><td></td></tr>";
        }

        return $table_tr;
    }
    public function get_l2data8($params){

        $L2id = $params['id'];
        $sdate = $params['sdate'];
        $edate = $params['edate'];
        $sdate2 = $params['sdate2'];
        $edate2 = $params['edate2'];

        $sql = "SELECT * FROM `L2Data` WHERE `L2Id` in (" . $L2id . ") and DATE >= '" . $sdate . "' and DATE <= '" . $edate . "' ORDER BY `L2Data`.`DATE` ASC";
        $list = $this->maindao->query($sql);
        //上一年
        $sql = "SELECT * FROM `L2Data` WHERE `L2Id` in (" . $L2id . ") and DATE >= '" . $sdate2 . "' and DATE <= '" . $edate . "' ORDER BY `L2Data`.`DATE` ASC";
        $list2 = $this->maindao->query($sql);
        $data_pre = array();
        foreach ($list2 as $item) {
            $data_pre[$item['DATE']] = $item;
        }

        $data = array();
        foreach ($list as $key => $item) {
            $key_tong = date("Y-m", strtotime("-1 year" . $item['DATE']));
            $key_huan = date("Y-m", strtotime("-1 month" . $item['DATE']));

            $arr = array();
            $arr['DATE'] = $item['DATE'];
            $arr['value'] = $item['D2'];

            $arr['huan'] = $data_pre[$key_huan]['D2'] ?? 0;
            $arr['huan'] = $this->calculate_num($arr['value'], $arr['huan']);

            $arr['tong'] = $data_pre[$key_tong]['D2'] ?? 0;
            $arr['tong'] = $this->calculate_num($arr['value'], $arr['tong']);

            $data[] = $arr;
        }
        $data = array_reverse($data);
        $table_tr = "<thead><tr id='tr_title' style='display: none;'><td colspan='4'>".$params['title']."</td></tr><tr><td>日期</td><td>人民币贷款</td><td>环比（%）</td><td>同比（%）</td></tr></thead>";
        foreach ($data as $item) {
            $table_tr .= "<tr><td>" . $item['DATE'] . "</td><td>" . $item['value'] . "</td><td>" . $item['huan'] . "</td><td>" . $item['tong'] . "</td></tr>";
        }

        return $table_tr;
    }
    public function get_l2data7($params){

        $L2id = $params['id'];
        $sdate = $params['sdate'];
        $edate = $params['edate'];
        $sdate2 = $params['sdate2'];
        $edate2 = $params['edate2'];

        $sql = "SELECT * FROM `L2Data` WHERE `L2Id` in (" . $L2id . ") and DATE >= '" . $sdate . "' and DATE <= '" . $edate . "' ORDER BY `L2Data`.`DATE` ASC";
        $list = $this->maindao->query($sql);
        //上一年
        $sql = "SELECT * FROM `L2Data` WHERE `L2Id` in (" . $L2id . ") and DATE >= '" . $sdate2 . "' and DATE <= '" . $edate . "' ORDER BY `L2Data`.`DATE` ASC";
        $list2 = $this->maindao->query($sql);
        $data_pre = array();
        foreach ($list2 as $item) {
            $data_pre[$item['DATE']] = $item;
        }

        $data = array();
        foreach ($list as $key => $item) {
            $key_tong = date("Y-m", strtotime("-1 year" . $item['DATE']));
            $key_huan = date("Y-m", strtotime("-1 month" . $item['DATE']));

            $arr = array();
            $arr['DATE'] = date("Y年n月",strtotime($item['DATE']));
            $arr['value'] = $item['D1'];


            $arr['huan'] = $data_pre[$key_huan]['D1'] ?? 0;
            $arr['huan'] = $this->calculate_num($arr['value'], $arr['huan']);

            $arr['tong'] = $data_pre[$key_tong]['D1'] ?? 0;
            $arr['tong'] = $this->calculate_num($arr['value'], $arr['tong']);

            $data[] = $arr;
        }
        $data = array_reverse($data);
        $table_tr = "<thead><tr id='tr_title' style='display: none;'><td colspan='4'>".$params['title']."</td></tr><tr><td>日期</td><td>社融增量金额（亿元）</td><td>环比（%）</td><td>同比（%）</td></tr></thead>";
        foreach ($data as $item) {
            $table_tr .= "<tr><td>" . $item['DATE'] . "</td><td>" . $item['value'] . "</td><td>" . $item['huan'] . "</td><td>" . $item['tong'] . "</td></tr>";
        }

        return $table_tr;
    }
    public function get_l2data6($params){

        $L2id = $params['id'];
        $sdate = $params['sdate'];
        $edate = $params['edate'];
        $sdate2 = $params['sdate2'];
        $edate2 = $params['edate2'];

        $sql = "SELECT * FROM `L2Data` WHERE `L2Id` in (" . $L2id . ") and DATE >= '" . $sdate . "' and DATE <= '" . $edate . "' ORDER BY `L2Data`.`DATE` DESC";
        $list = $this->maindao->query($sql);
        $list_1 = array();
        foreach ($list as $item) {
            $list_1[$item['L2Id']][] = $item;
        }

        $data = array();
        foreach ($list_1 as $key1 => $value) {
            foreach ($value as $key => $item) {

                $arr = array();
                $arr['DATE'] = date("Y年n月",strtotime($item['DATE']));
                $arr['D1'] = $item['D1'];
                $arr['D2'] = $item['D2'];

                $data[$arr['DATE']][$item['L2Id']] = $arr;
            }
        }

        $table_tr = "<thead><tr id='tr_title' style='display: none;'><td colspan='5'>".$params['title']."</td></tr><tr><td>日期</td><td>CPI同比（%）</td><td>环比（%）</td><td>PPI同比（%）</td><td>环比（%）</td></tr></thead>";
        foreach ($data as $key=>$item) {
            $table_tr .= "<tr><td>" . $key . "</td><td>" . $item['117']['D1'] . "</td><td>" . $item['145']['D1'] . "</td><td>" . $item['117']['D2'] . "</td><td>" . $item['145']['D2'] . "</td></tr>";
        }

        return $table_tr;
    }
    public function get_l2data5($params){

        $L2id = $params['id'];
        $sdate = $params['sdate'];
        $edate = $params['edate'];
        $sdate2 = $params['sdate2'];
        $edate2 = $params['edate2'];

        $sql = "SELECT * FROM `L2Data` WHERE `L2Id` in (" . $L2id . ") and DATE >= '" . $sdate . "' and DATE <= '" . $edate . "' ORDER BY `L2Data`.`DATE` ASC";
        $list = $this->maindao->query($sql);
        $list_1 = array();
        foreach ($list as $item) {
            $list_1[$item['L2Id']][] = $item;
        }
        //上一年
        $sql = "SELECT * FROM `L2Data` WHERE `L2Id` in (" . $L2id . ") and DATE >= '" . $sdate2 . "' and DATE <= '" . $edate . "' ORDER BY `L2Data`.`DATE` ASC";
        $list2 = $this->maindao->query($sql);
        $data_pre = array();
        //$end_arr = array();
        foreach ($list2 as $item) {
            $data_pre[$item['L2Id']][$item['DATE']] = $item;
            //$end_arr[$item['L2Id']] = $item;
        }

        $data = array();
        foreach ($list_1 as $key1 => $value) {
            foreach ($value as $key => $item) {
                $key_huan = date("Y-m", strtotime("-1 month" . $item['DATE']));
                $arr = array();
                $arr['DATE'] = date("Y年n月",strtotime($item['DATE']));
                $arr['value'] = $item['D1'];

                /*if ($key == "0") {
                    $arr['huan'] = $end_arr[$item['L2Id']]['D1'];
                } else {
                    $arr['huan'] = $list_1[$item['L2Id']][($key - 1)]['D1'];
                }*/
                $arr['huan'] = $data_pre[$item['L2Id']][$key_huan]['D1'] ?? 0;
                $arr['huan'] = $this->calculate_num($arr['value'], $arr['huan']);

                $arr['tong'] = $item['D3'];

                $data[$arr['DATE']][$item['L2Id']] = $arr;
            }
        }
        $data = array_reverse($data);
        $table_tr = "<thead><tr id='tr_title' style='display: none;'><td colspan='7'>".$params['title']."</td></tr><tr><td>日期</td><td>进口（亿元）</td><td>环比（%）</td><td>同比（%）</td><td>出口（亿元）</td><td>环比（%）</td><td>同比（%）</td></tr></thead>";
        foreach ($data as $key=>$item) {
            $table_tr .= "<tr><td>" . $key . "</td><td>" . $item['548']['value'] . "</td><td>" . $item['548']['huan'] . "</td><td>" . $item['548']['tong'] . "</td><td>" . $item['547']['value'] . "</td><td>" . $item['547']['huan'] . "</td><td>" . $item['547']['tong'] . "</td></tr>";
        }

        return $table_tr;
    }
    public function get_l2data4($params){

        $L2id = $params['id'];
        $sdate = $params['sdate'];
        $edate = $params['edate'];
        $sdate2 = $params['sdate2'];
        $edate2 = $params['edate2'];

        $sql = "SELECT * FROM `L2Data` WHERE `L2Id` in (" . $L2id . ") and DATE >= '" . $sdate . "' and DATE <= '" . $edate . "' ORDER BY `L2Data`.`DATE` ASC";
        $list = $this->maindao->query($sql);
        //上一年
        $sql = "SELECT * FROM `L2Data` WHERE `L2Id` in (" . $L2id . ") and DATE >= '" . $sdate2 . "' and DATE <= '" . $edate . "' ORDER BY `L2Data`.`DATE` ASC";
        $list2 = $this->maindao->query($sql);
        $data_pre = array();
        //$end_arr = array();
        foreach ($list2 as $item) {
            $data_pre[$item['DATE']] = $item;
            //$end_arr = $item;
        }

        $data = array();
        foreach ($list as $key => $item) {
            $key_huan = date("Y-m", strtotime("-1 month" . $item['DATE']));

            $arr = array();
            $arr['DATE'] = date("Y年n月",strtotime($item['DATE']));
            $arr['value'] = $item['D1'];

            /*if ($key == "0") {
                $arr['huan'] = $end_arr['D1'];
            } else {
                $arr['huan'] = $list[($key - 1)]['D1'];
            }*/
            $arr['huan'] = $data_pre[$key_huan]['D1'] ?? 0;
            $arr['huan'] = $this->calculate_num($arr['value'], $arr['huan']);

            $arr['tong'] = $item['D3'];

            $data[] = $arr;
        }
        $data = array_reverse($data);
        $table_tr = "<thead><tr id='tr_title' style='display: none;'><td colspan='4'>".$params['title']."</td></tr><tr><td>日期</td><td>社会消费品零售总额（亿元）</td><td>环比（%）</td><td>同比（%）</td></tr></thead>";
        foreach ($data as $item) {
            $table_tr .= "<tr><td>" . $item['DATE'] . "</td><td>" . $item['value'] . "</td><td>" . $item['huan'] . "</td><td>" . $item['tong'] . "</td></tr>";
        }

        return $table_tr;
    }
    public function get_l2data3($params){

        $L2id = $params['id'];
        $sdate = $params['sdate'];
        $edate = $params['edate'];
        $sdate2 = $params['sdate2'];
        $edate2 = $params['edate2'];

        $sql = "SELECT * FROM `L2Data` WHERE `L2Id` in (" . $L2id . ") and DATE >= '" . $sdate . "' and DATE <= '" . $edate . "' ORDER BY `L2Data`.`DATE` ASC";
        $list = $this->maindao->query($sql);
        //上一年
        $sql = "SELECT * FROM `L2Data` WHERE `L2Id` in (" . $L2id . ") and DATE >= '" . $sdate2 . "' and DATE <= '" . $edate . "' ORDER BY `L2Data`.`DATE` ASC";
        $list2 = $this->maindao->query($sql);
        $data_pre = array();
        //$end_arr = array();
        foreach ($list2 as $item) {
            $data_pre[$item['DATE']] = $item;
            //$end_arr = $item;
        }

        $data = array();
        foreach ($list as $key => $item) {
            //$key_tong = date("Y-", strtotime("-1 year" . $item['DATE'])) . date("m", strtotime($item['DATE']));
            $key_huan = date("Y-m", strtotime("-1 month" . $item['DATE']));

            $arr = array();
            $arr['DATE'] = date("Y年1-n月",strtotime($item['DATE']));
            $arr['value'] = $item['D2'];

            /*if ($key == "0") {
                $arr['huan'] = $end_arr['D2'];
            } else {
                $arr['huan'] = $list[($key - 1)]['D2'];
            }*/
            $arr['huan'] = $data_pre[$key_huan]['D2'] ?? 0;
            $arr['huan'] = $this->calculate_num($arr['value'], $arr['huan']);

            //$arr['tong'] = $data_pre[$key_tong]['D2'] ?? 0;
            //$arr['tong'] = $this->calculate_num($arr['value'], $arr['tong']);
            $arr['tong'] = $item['D1'];

            $data[] = $arr;
        }
        $data = array_reverse($data);
        $table_tr = "<thead><tr id='tr_title' style='display: none;'><td colspan='4'>".$params['title']."</td></tr><tr><td>日期</td><td>工业增加值（%）</td><td>环比（%）</td><td>同比（%）</td></tr></thead>";
        foreach ($data as $item) {
            $table_tr .= "<tr><td>" . $item['DATE'] . "</td><td>" . $item['value'] . "</td><td>" . $item['huan'] . "</td><td>" . $item['tong'] . "</td></tr>";
        }

        return $table_tr;
    }

    public function get_l2data2($params){

        $L2id = $params['id'];
        $sdate = $params['sdate'];
        $edate = $params['edate'];
        $sdate2 = $params['sdate2'];
        $edate2 = $params['edate2'];

        $sql = "SELECT * FROM `L2Data` WHERE `L2Id` in (" . $L2id . ") and DATE >= '" . $sdate . "' and DATE <= '" . $edate . "' ORDER BY `L2Data`.`DATE` DESC";
        $list = $this->maindao->query($sql);
        //上一年
        /*$sql = "SELECT * FROM `L2Data` WHERE `L2Id` in (" . $L2id . ") and DATE >= '" . $sdate2 . "' and DATE <= '" . $edate2 . "' ORDER BY `L2Data`.`DATE` ASC";
        $list2 = $this->maindao->query($sql);
        $data_pre = array();
        $end_arr = array();
        foreach ($list2 as $item) {
            $data_pre[$item['DATE']] = $item;
            $end_arr = $item;
        }*/

        $data = array();
        foreach ($list as $key => $item) {

            $arr = array();
            $arr['DATE'] = date("Y年1-n月",strtotime($item['DATE']));
            $arr['value'] = $item['D2'];

            /*if ($key == "0") {
                $arr['huan'] = $end_arr['D2'];
            } else {
                $arr['huan'] = $list[($key - 1)]['D2'];
            }
            $arr['huan'] = $this->calculate_num($arr['value'], $arr['huan']);*/

            $arr['huan'] = $item['D3'];
            $arr['tong'] = $item['D4'];

            $data[] = $arr;
        }

        $table_tr = "<thead><tr id='tr_title' style='display: none;'><td colspan='4'>".$params['title']."</td></tr><tr><td>日期</td><td>固投（亿元）</td><td>环比（%）</td><td>同比（%）</td></tr></thead>";
        foreach ($data as $item) {
            $table_tr .= "<tr><td>" . $item['DATE'] . "</td><td>" . $item['value'] . "</td><td>" . $item['huan'] . "</td><td>" . $item['tong'] . "</td></tr>";
        }

        return $table_tr;
    }

    public function get_l2data1($params){

        $L2id = $params['id'];
        $sdate = $params['sdate'];
        $edate = $params['edate'];
        $sdate2 = $params['sdate2'];
        $edate2 = $params['edate2'];

        /*$sql = "SELECT * FROM `L2Data` WHERE `L2Id` in (" . $L2id . ") and DATE >= '" . $sdate . "' and DATE <= '" . $edate . "' ORDER BY `L2Data`.`DATE` ASC";
        $list = $this->maindao->query($sql);
        //上一年
        $sql = "SELECT * FROM `L2Data` WHERE `L2Id` in (" . $L2id . ") and DATE >= '" . $sdate2 . "' and DATE <= '" . $edate2 . "' ORDER BY `L2Data`.`DATE` ASC";
        $list2 = $this->maindao->query($sql);
        $data_pre = array();
        $end_arr = array();
        foreach ($list2 as $item) {
            $data_pre[$item['DATE']] = $item;
            $end_arr = $item;
        }*/
        $sql = "SELECT `date`,dta_2,dta_4,dta_6 FROM DataTableBaseInfo as db left join data_table as dt on db.id=dt.baseid WHERE db.DataType='Java-Guoneishengchansummew' and  dta_1='GDP' and `date` >= '" . $sdate . "' and `date` <= '" . $edate . "' ORDER BY `date` DESC ";
        $list = $this->drcdao->query($sql);

        $quarter = array(
            "01-01"=>"1季度",
            "04-01"=>"2季度",
            "07-01"=>"3季度",
            "10-01"=>"4季度"
        );

        $data = array();
        foreach ($list as $key => $item) {

            $md = date("m-d", strtotime($item['date']));
            $arr = array();
            $arr['DATE'] = date("Y年",strtotime($item['date'])).$quarter[$md];
            $arr['value'] = $item['dta_2'];
            $arr['huan'] = $item['dta_6'];
            $arr['tong'] = $item['dta_4'];

            $data[] = $arr;
        }

        $table_tr = "<thead><tr id='tr_title' style='display: none;'><td colspan='4'>".$params['title']."</td></tr><tr><td>日期</td><td>GDP（亿元）</td><td>环比（%）</td><td>同比（%）</td></tr></thead>";
        foreach ($data as $item) {
            $table_tr .= "<tr><td>" . $item['DATE'] . "</td><td>" . $item['value'] . "</td><td>" . $item['huan'] . "</td><td>" . $item['tong'] . "</td></tr>";
        }

        return $table_tr;
    }

    public function calculate_num($b_data,$s_data,$Decimal="2"){
        $calculate = 0;
        if($b_data!=$s_data && $s_data!="" && $s_data!=0){
            $calculate = bcsub($b_data,$s_data,$Decimal+2);
            $calculate = round($calculate/$s_data*100,$Decimal);
        }
        return $calculate;
    }

    public function getPzPrice_duibiao($workDate){
        // 卷螺差=济南市场5.75mm*1500热轧板卷主流价格 173112 - 20mmHRB400主流价格价差 172023
        // 冷热差=济南市场1.0mm冷轧板卷主流价格 174212- 济南市场5.75mm*1500热轧板卷主流价格 173112
        // 板卷差=济南市场20mm中厚板主流价格 173012 - 济南市场5.75mm*1500热轧板卷主流价格 173112
        // 南北价差=济南市场20mm螺纹钢价格 172023 - 杭州市场20mm螺纹钢价格 082023
        if($workDate["this_end_date"] == "2025-01-26"){
            $workDate["this_end_date"] = "2025-01-24";
        }
        $priceid_str = "'173112','172023','174212','173012','082023'";
        $price_arr = $this->hq_price_6($workDate["this_start_date"],$workDate["this_start_date"],$priceid_str,$workDate["this_end_date"],$workDate["this_end_date"]);
        // 去年同期
        $qntq_start_date = $this->get_xhdate(date("Y-m-d",strtotime($workDate["this_start_date"] . " -1 year + 1 day")));
        $qntq_end_date = $this->get_xhdate(date("Y-m-d",strtotime($workDate["this_end_date"] . " -1 year + 1 day")));
        $qntq_price_arr = $this->hq_price_6($qntq_start_date,$qntq_start_date,$priceid_str,$qntq_end_date,$qntq_end_date);

        //去年最大值与最小值
        $qn_sdate = date("Y-01-01",strtotime($qntq_start_date));
        $qn_end_date = date("Y-12-31",strtotime($qntq_start_date));
        $qn_price_arr = $this->maindao->query("select price,mconmanagedate,topicture from marketconditions where topicture in (".$priceid_str.") AND mconmanagedate >='".$qn_sdate." 00:00:00' AND mconmanagedate <='".$qn_end_date." 23:59:59' and isview=0 order by topicture asc");

        $jn_rzbj_data = array();
        $jn_lwg_data = array();
        $jn_lzbj_data = array();
        $jn_zhb_data = array();
        $hz_lwg_data = array();

        foreach($qn_price_arr as $item){
            $riqi = date("Y-m-d",strtotime($item["mconmanagedate"]));
            if($item["topicture"] == "173112"){
                $jn_rzbj_data[$riqi] = $item["price"];
            }else if($item["topicture"] == "172023"){
                $jn_lwg_data[$riqi] = $item["price"];
            }else if($item["topicture"] == "174212"){
                $jn_lzbj_data[$riqi] = $item["price"];
            }else if($item["topicture"] == "173012"){
                $jn_zhb_data[$riqi] = $item["price"];
            }else if($item["topicture"] == "082023"){
                $hz_lwg_data[$riqi] = $item["price"];
            }
        }

        //计算差值最大及最小值
        $jlc_max = "";
        $jlc_min = "";

        $lrc_max = "";
        $lrc_min = "";

        $bjc_max = "";
        $bjc_min = "";

        $nbc_max = "";
        $nbc_min = "";
        foreach($jn_rzbj_data as $key=>$v){
            //卷螺差
            $jlc_new = $jn_rzbj_data[$key] - $jn_lwg_data[$key];
            if($jlc_max == "" || $jlc_min == ""){
                $jlc_max = $jlc_new;
                $jlc_min = $jlc_new;
            }
            if($jlc_new > $jlc_max){
                $jlc_max = $jlc_new;
            }else if($jlc_new < $jlc_min){
                $jlc_min = $jlc_new;
            }

            //冷热差
            $lrc_new = $jn_lzbj_data[$key] - $jn_rzbj_data[$key];
            if($lrc_max == "" || $lrc_min == ""){
                $lrc_max = $lrc_new;
                $lrc_min = $lrc_new;
            }
            if($lrc_new > $lrc_max){
                $lrc_max = $lrc_new;
            }else if($lrc_new < $lrc_min){
                $lrc_min = $lrc_new;
            }

            //板卷差
            $bjc_new = $jn_zhb_data[$key] - $jn_rzbj_data[$key];
            if($bjc_max == "" || $bjc_min == ""){
                $bjc_max = $bjc_new;
                $bjc_min = $bjc_new;
            }
            if($bjc_new > $bjc_max){
                $bjc_max = $bjc_new;
            }else if($bjc_new < $bjc_min){
                $bjc_min = $bjc_new;
            }

            //南北差
            $nbc_new = $jn_lwg_data[$key] - $hz_lwg_data[$key];
            if($nbc_max == "" || $nbc_min == ""){
                $nbc_max = $nbc_new;
                $nbc_min = $nbc_new;
            }
            if($nbc_new > $nbc_max){
                $nbc_max = $nbc_new;
            }else if($nbc_new < $nbc_min){
                $nbc_min = $nbc_new;
            }
        }

        $td1 = date("n.j",strtotime($workDate["this_end_date"]));
        $td2 = date("n.j",strtotime($workDate["this_start_date"]));
        $td3 = date("y年",strtotime($qntq_start_date))."同期";
        $td4 = date("y年",strtotime($qn_sdate))."最大值";
        $td5 = date("y年",strtotime($qn_sdate))."最小值";
        $td6 = $td1."-".$td2;
        $td7 = $td1."-".$td3;
        $td8 = $td1."-".$td4;
        $td9 = $td1."-".$td5;

        $thead_str = '<thead>
                        <tr>
                            <td>(元/吨)</td>
                            <td>'.$td1.'</td>
                            <td>'.$td2.'</td>
                            <td>'.$td3.'</td>
                            <td>'.$td4.'</td>
                            <td>'.$td5.'</td>
                            <td>'.$td6.'</td>
                            <td>'.$td7.'</td>
                            <td>'.$td8.'</td>
                            <td>'.$td9.'</td>
                        </tr>
                    </thead>';


        $jlc_td1 = $price_arr['lprice']['173112'] - $price_arr['lprice']['172023'];
        $jlc_td2 = $price_arr['tprice']['173112'] - $price_arr['tprice']['172023'];
        $jlc_td3 = $qntq_price_arr['lprice']['173112'] - $qntq_price_arr['lprice']['172023'];
        $jlc_td4 = $jlc_td1 - $jlc_td2;
        $jlc_td5 = $jlc_td1 - $jlc_td3;
        $jlc_td6 = $jlc_td1 - $jlc_max;
        $jlc_td7 = $jlc_td1 - $jlc_min;

        $ret_arr_data[0][] = "卷螺差";
        $ret_arr_data[0][] = $jlc_td1;
        $ret_arr_data[0][] = $jlc_td2;
        $ret_arr_data[0][] = $jlc_td3;
        $ret_arr_data[0][] = $jlc_max;
        $ret_arr_data[0][] = $jlc_min;
        $ret_arr_data[0][] = $this->zhangdie($jlc_td4);
        $ret_arr_data[0][] = $this->zhangdie($jlc_td5);
        $ret_arr_data[0][] = $this->zhangdie($jlc_td6);
        $ret_arr_data[0][] = $this->zhangdie($jlc_td7);



        $lrc_td1 = $price_arr['lprice']['174212'] - $price_arr['lprice']['173112'];
        $lrc_td2 = $price_arr['tprice']['174212'] - $price_arr['tprice']['173112'];
        $lrc_td3 = $qntq_price_arr['lprice']['174212'] - $qntq_price_arr['lprice']['173112'];
        $lrc_td4 = $lrc_td1 - $lrc_td2;
        $lrc_td5 = $lrc_td1 - $lrc_td3;
        $lrc_td6 = $lrc_td1 - $lrc_max;
        $lrc_td7 = $lrc_td1 - $lrc_min;

        $ret_arr_data[1][] = "冷热差";
        $ret_arr_data[1][] = $lrc_td1;
        $ret_arr_data[1][] = $lrc_td2;
        $ret_arr_data[1][] = $lrc_td3;
        $ret_arr_data[1][] = $lrc_max;
        $ret_arr_data[1][] = $lrc_min;
        $ret_arr_data[1][] = $this->zhangdie($lrc_td4);
        $ret_arr_data[1][] = $this->zhangdie($lrc_td5);
        $ret_arr_data[1][] = $this->zhangdie($lrc_td6);
        $ret_arr_data[1][] = $this->zhangdie($lrc_td7);


        $bjc_td1 = $price_arr['lprice']['173012'] - $price_arr['lprice']['173112'];
        $bjc_td2 = $price_arr['tprice']['173012'] - $price_arr['tprice']['173112'];
        $bjc_td3 = $qntq_price_arr['lprice']['173012'] - $qntq_price_arr['lprice']['173112'];
        $bjc_td4 = $bjc_td1 - $bjc_td2;
        $bjc_td5 = $bjc_td1 - $bjc_td3;
        $bjc_td6 = $bjc_td1 - $bjc_max;
        $bjc_td7 = $bjc_td1 - $bjc_min;

        $ret_arr_data[2][] = "板卷差";
        $ret_arr_data[2][] = $bjc_td1;
        $ret_arr_data[2][] = $bjc_td2;
        $ret_arr_data[2][] = $bjc_td3;
        $ret_arr_data[2][] = $bjc_max;
        $ret_arr_data[2][] = $bjc_min;
        $ret_arr_data[2][] = $this->zhangdie($bjc_td4);
        $ret_arr_data[2][] = $this->zhangdie($bjc_td5);
        $ret_arr_data[2][] = $this->zhangdie($bjc_td6);
        $ret_arr_data[2][] = $this->zhangdie($bjc_td7);


        $nbc_td1 = $price_arr['lprice']['172023'] - $price_arr['lprice']['082023'];
        $nbc_td2 = $price_arr['tprice']['172023'] - $price_arr['tprice']['082023'];
        $nbc_td3 = $qntq_price_arr['lprice']['172023'] - $qntq_price_arr['lprice']['082023'];
        $nbc_td4 = $nbc_td1 - $nbc_td2;
        $nbc_td5 = $nbc_td1 - $nbc_td3;
        $nbc_td6 = $nbc_td1 - $nbc_max;
        $nbc_td7 = $nbc_td1 - $nbc_min;

        $ret_arr_data[3][] = "南北差";
        $ret_arr_data[3][] = $nbc_td1;
        $ret_arr_data[3][] = $nbc_td2;
        $ret_arr_data[3][] = $nbc_td3;
        $ret_arr_data[3][] = $nbc_max;
        $ret_arr_data[3][] = $nbc_min;
        $ret_arr_data[3][] = $this->zhangdie($nbc_td4);
        $ret_arr_data[3][] = $this->zhangdie($nbc_td5);
        $ret_arr_data[3][] = $this->zhangdie($nbc_td6);
        $ret_arr_data[3][] = $this->zhangdie($nbc_td7);

        // echo $qntq_start_date;echo $qntq_end_date;
        // echo "<pre>";print_r($workDate);print_r($ret_arr_data);;echo "</pre>";exit;

        //钢材价差情况
        $marketNews = html_entity_decode($this->_dao->getMarketNews($workDate,2,218));
        if($workDate["this_end_date"] > "2025-04-07"){
            $marketNews218_new = "其中，济南市场卷螺差为".$ret_arr_data[0][1]."元/吨，较上周末".$this->dingjia_jiacha($jlc_td4)."；冷热差为".$ret_arr_data[1][1]."元/吨，较上周末".$this->dingjia_jiacha($lrc_td4)."；板卷差为".$ret_arr_data[2][1]."元/吨，较上周末".$this->dingjia_jiacha($bjc_td4)."；济南与杭州市场螺纹钢的南北价差为".$ret_arr_data[3][1]."元/吨，较上周末".$this->dingjia_jiacha($nbc_td4)."。";
            $this->assign( "marketNews218_new", $marketNews218_new);
        }
        $this->assign( "marketNews218", $marketNews);
        $this->assign("thead_str",$thead_str);
        $this->assign("ret_arr_data",$ret_arr_data);
    }

    //粗钢供需平衡表
    function cg_gxph($params){

        $edate = date("Y-m-d",strtotime(date("Y-m-d") . " -2 month"));
        if($params["edate"] != ""){
            $edate = $params["edate"];
        }

        $year_1 = date("Y",strtotime($edate));
        $month_1 = date("n",strtotime($edate));
        $year_2 = $year_1 - 1;
        $year_3 = $year_2 - 1;
        // $sdate = date("Y-m-d");
        // if($params["sdate"] != ""){
        //     $sdate = $params["sdate"];
        // }

        //粗钢产量：L2id=111 and L2OrderNo=43
        $cg_cl_arr = array();
        $cg_cl_data = $this->maindao->query("SELECT * FROM `L2Data` WHERE `L2Id` = '111' and L2OrderNo = '43' and DATE <= '".$year_1."' ORDER BY DATE DESC limit 5 ");
        foreach($cg_cl_data as $cg_item){
            $cg_cl_arr[$cg_item["DATE"]][] = round($cg_item["D1"],2);
            $cg_cl_arr[$cg_item["DATE"]][] = round($cg_item["D2"],2);
        }

        //粗钢进出口量：L2id=194 and L2OrderNo=3 今年
        $cg_jk_ck = $this->maindao->getRow("SELECT sum(D2) as ckl,sum(D1) as jkl FROM `L2Data` WHERE `L2Id` = '194' and L2OrderNo = '3' and DATE >= '".$year_1."-01' and DATE <= '".$year_1.$month_1."'");

        //粗钢进出口量：L2id=194 and L2OrderNo=3 去年
        $cg_jk_ck2 = $this->maindao->getRow("SELECT sum(D2) as ckl,sum(D1) as jkl FROM `L2Data` WHERE `L2Id` = '194' and L2OrderNo = '3' and DATE >= '".$year_2."-01' and DATE <= '".$year_2."-12'");

        //粗钢进出口量：L2id=194 and L2OrderNo=3 前年
        $cg_jk_ck3 = $this->maindao->getRow("SELECT sum(D2) as ckl,sum(D1) as jkl FROM `L2Data` WHERE `L2Id` = '194' and L2OrderNo = '3' and DATE >= '".$year_3."-01' and DATE <= '".$year_3."-12'");

        

        $table_str = '<thead>';

        if($month_1 == 1){
            $td_str = $year_1."年".$month_1."月";
        }else{
            $td_str = $year_1."年1-".$month_1."月";
        }
        $table_str .= "<tr>
                        <td>单位：万吨</td>
                        <td>".$year_3."</td>
                        <td>".$year_2."</td>
                        <td>".$year_2."同比（%）</td>
                        <td>".$td_str."</td>
                        <td>".$year_1."年同比（%）</td>
                       </tr></thead>";

        $ret_arr["cgcl"][] = "粗钢产量";
        $ret_arr["cgcl"][] = $cg_cl_arr[$year_3][0];
        $ret_arr["cgcl"][] = $cg_cl_arr[$year_2][0];
        $cg_tb_zf = $this->zhangdie(round(($cg_cl_arr[$year_2][0] - $cg_cl_arr[$year_3][0])/$cg_cl_arr[$year_3][0] * 100,2));
        $ret_arr["cgcl"][] = $cg_tb_zf;
        $ret_arr["cgcl"][] = $cg_cl_arr[$year_1][0];
        $ret_arr["cgcl"][] = $this->zhangdie(round(($cg_cl_arr[$year_1][0] - $cg_cl_arr[$year_2][0])/$cg_cl_arr[$year_2][0] * 100,2));

        $ret_arr["cgck"][] = "粗钢出口";
        $ret_arr["cgck"][] = round($cg_jk_ck3["ckl"],2);
        $ret_arr["cgck"][] = round($cg_jk_ck2["ckl"],2);
        $cg_ck_tb_zf = $this->zhangdie(round(($cg_jk_ck2["ckl"] - $cg_jk_ck3["ckl"])/$cg_jk_ck3["ckl"] * 100,2));
        $ret_arr["cgck"][] = $cg_ck_tb_zf;
        $ret_arr["cgck"][] = round($cg_jk_ck["ckl"],2);
        $ret_arr["cgck"][] = $this->zhangdie(round(($cg_jk_ck["ckl"] - $cg_jk_ck2["ckl"])/$cg_jk_ck2["ckl"] * 100,2));

        $ret_arr["cgjk"][] = "粗钢进口";
        $ret_arr["cgjk"][] = round($cg_jk_ck3["jkl"],2);
        $ret_arr["cgjk"][] = round($cg_jk_ck2["jkl"],2);
        $cg_jk_tb_zf = $this->zhangdie(round(($cg_jk_ck2["jkl"] - $cg_jk_ck3["jkl"])/$cg_jk_ck3["jkl"] * 100,2));
        $ret_arr["cgjk"][] = $cg_jk_tb_zf;
        $ret_arr["cgjk"][] = round($cg_jk_ck["jkl"],2);
        $ret_arr["cgjk"][] = $this->zhangdie(round(($cg_jk_ck["jkl"] - $cg_jk_ck2["jkl"])/$cg_jk_ck2["jkl"] * 100,2));

        $ret_arr["cgjjk"][] = "粗钢净出口";
        $ret_arr["cgjjk"][] = $ret_arr["cgck"][1] - $ret_arr["cgjk"][1];
        $ret_arr["cgjjk"][] = $ret_arr["cgck"][2] - $ret_arr["cgjk"][2];
        $cg_jjk_tb_zf = $this->zhangdie(round(($ret_arr["cgjjk"][2] - $ret_arr["cgjjk"][1])/$ret_arr["cgjjk"][1] * 100,2));
        $ret_arr["cgjjk"][] = $cg_jjk_tb_zf;
        $ret_arr["cgjjk"][] = $ret_arr["cgck"][4] - $ret_arr["cgjk"][4];
        $ret_arr["cgjjk"][] = $this->zhangdie(round(($ret_arr["cgjjk"][4] - $ret_arr["cgjjk"][2])/$ret_arr["cgjjk"][2] * 100,2));

        $ret_arr["cggyl"][] = "粗钢供应量";
        $ret_arr["cggyl"][] = $ret_arr["cgcl"][1] - $ret_arr["cgjjk"][1];
        $ret_arr["cggyl"][] = $ret_arr["cgcl"][2] - $ret_arr["cgjjk"][2];
        $cg_gyl_tb_zf = $this->zhangdie(round(($ret_arr["cggyl"][2] - $ret_arr["cggyl"][1])/$ret_arr["cggyl"][1] * 100,2));
        $ret_arr["cggyl"][] = $cg_gyl_tb_zf;
        $ret_arr["cggyl"][] = $ret_arr["cgcl"][4] - $ret_arr["cgjjk"][4];
        $ret_arr["cggyl"][] = $this->zhangdie(round(($ret_arr["cggyl"][4] - $ret_arr["cggyl"][2])/$ret_arr["cggyl"][2] * 100,2));

        $ret_arr["cgxql"][] = "粗钢需求量";
        $ret_arr["cgxql"][] = $cg_cl_arr[$year_3][1];
        $ret_arr["cgxql"][] = $cg_cl_arr[$year_2][1];
        $cg_xql_tb_zf = $this->zhangdie(round(($cg_cl_arr[$year_2][1] - $cg_cl_arr[$year_3][1])/$cg_cl_arr[$year_3][1] * 100,2));
        $ret_arr["cgxql"][] = $cg_xql_tb_zf;
        $ret_arr["cgxql"][] = $cg_cl_arr[$year_1][1];
        $ret_arr["cgxql"][] = $this->zhangdie(round(($ret_arr["cgxql"][4] - $ret_arr["cgxql"][2])/$ret_arr["cgxql"][2] * 100,2));
        
        $ret_arr["gy_xq"][] = "供-需";
        $ret_arr["gy_xq"][] = $this->zhangdie(round($ret_arr["cggyl"][1] - $ret_arr["cgxql"][1],2));
        $ret_arr["gy_xq"][] = $this->zhangdie(round($ret_arr["cggyl"][2] - $ret_arr["cgxql"][2],2));
        $ret_arr["gy_xq"][] = "";
        $ret_arr["gy_xq"][] = round($ret_arr["cggyl"][4] - $ret_arr["cgxql"][4],2);
        $ret_arr["gy_xq"][] = "";

        $this->assign("table_str",$table_str);
        $this->assign("ret_arr",$ret_arr);
        $params["edate"] = $edate;
        $this->assign("params",$params);
        $this->assign("mode",$params['mode']);
        
    }

    function tjj_cg_cl($params){
        $sdate = date("Y-m-01",strtotime(date("Y-m-01") . " -1 year"));
        // $sdate = "2024-01-01";
        if($params["sdate"] != ""){
            $sdate = $params["sdate"];
        }

        $edate = date("Y-m-d");
        if($params["edate"] != ""){
            $edate = $params["edate"];
        }

        $ret_arr = array();
        //粗钢产量：L2id=109 and L2OrderNo=11
        $cg_cl_data = $this->maindao->query("SELECT * FROM `L2Data` WHERE `L2Id` = '109' and L2OrderNo = '11' and DATE >= '".$sdate."' and DATE <= '".$edate."' ORDER BY DATE DESC");
        //粗钢产日均量：L2id=245 and L2OrderNo=1
        $qn_sdate = date("Y-m-d",strtotime($sdate . " -1 year"));
        $cg_cl_rj_data = $this->maindao->query("SELECT * FROM `L2Data` WHERE `L2Id` = '245' and L2OrderNo = '1' and DATE >= '".$qn_sdate."' and DATE <= '".$edate."' ORDER BY DATE DESC");

        // //同比、环比 粗钢产日均量：L2id=245 and L2OrderNo=1
        // $qn_sdate = date("Y-m-d",strtotime($sdate . " -1 year"));
        // $qn_edate = date("Y-m-d",strtotime($edate . " -1 year"));
        // $qn_cg_cl_rj_data = $this->maindao->query("SELECT * FROM `L2Data` WHERE `L2Id` = '245' and L2OrderNo = '1' and DATE >= '".$qn_sdate."' and DATE <= '".$qn_edate."' ORDER BY DATE DESC");

        
        foreach($cg_cl_data as $key=>$item){
            // $ret_arr[$key] = $item;
            $riqi = date("Y年n月",strtotime($item["DATE"]));
            $item_arr = array(
                "cgcl1"=>$item["D1"],
                // "hb1"=>round(($item["D1"] - $item["D1"]) / $item["D1"] * 100,2),
                "tb1"=>$item["D3"]
            );

            $hb_riqi = date("Y-m",strtotime($item["DATE"] . " -1 month"));
            $tb_riqi = date("Y-m",strtotime($item["DATE"] . " -1 year"));

            if($cg_cl_data[$key+1]["D1"] != "" && $cg_cl_data[$key+1]["D1"] != 0){
                $item_arr["hb1"] = round(($item["D1"] - $cg_cl_data[$key+1]["D1"]) / $cg_cl_data[$key+1]["D1"] * 100,2);
            }else{
                $hb_cgcl_data = $this->maindao->getRow("SELECT * FROM `L2Data` WHERE `L2Id` = '109' and L2OrderNo = '11' and DATE < '".$hb_riqi."' ORDER BY DATE DESC limit 1");
                $item_arr["hb1"] = round(($item["D1"] - $hb_cgcl_data["D1"]) / $hb_cgcl_data["D1"] * 100,2);
            }
            
            foreach($cg_cl_rj_data as $rj_v){
                if($rj_v["DATE"] == $item["DATE"]){
                    $item_arr["cgcl2"] = $rj_v["D1"];
                    foreach($cg_cl_rj_data as $rj_v2){
                        if($rj_v2["DATE"] == $hb_riqi){
                            if($rj_v2["D1"] != "" && $rj_v2["D1"] != 0){
                                $item_arr["hb2"] = round(($rj_v["D1"] - $rj_v2["D1"]) / $rj_v2["D1"] * 100,2);
                            }else{
                                $hb_cg_rjcl = $this->maindao->getRow("SELECT * FROM `L2Data` WHERE `L2Id` = '245' and L2OrderNo = '1' and DATE < '".$hb_riqi."' ORDER BY DATE DESC limit 1");
                                $item_arr["hb2"] = round(($rj_v["D1"] - $hb_cg_rjcl["D1"]) / $hb_cg_rjcl["D1"] * 100,2);
                            }
                        }
                        if($rj_v2["DATE"] == $tb_riqi){
                            $item_arr["tb2"] = round(($rj_v["D1"] - $rj_v2["D1"]) / $rj_v2["D1"] * 100,2);
                        }
                    }
                }
            }

            $ret_arr[$riqi] = $item_arr;
        }

        // foreach($cg_cl_rj_data as $key=>$item){
        //     $riqi = date("Y年n月",strtotime($item["DATE"]));
        //     $ret_arr[$riqi]["cgcl2"] = $item["D1"];

            
        // }

        // echo "<pre/>";print_r($cg_cl_rj_data);
        $params["edate"] = $edate;
        $params["sdate"] = $sdate;
        $this->assign("params",$params);
        $this->assign("ret_arr",$ret_arr);
        $this->assign("mode",$params['mode']);
    }

    function gx_xun_cg_gc_cl($params){
        $sdate = date("Y-m-01",strtotime(date("Y-m-01") . " -1 year"));
        // $sdate = "2023-01-01";
        if($params["sdate"] != ""){
            $sdate = $params["sdate"];
        }

        $edate = date("Y-m-d");
        if($params["edate"] != ""){
            $edate = $params["edate"];
        }

        $ret_arr = array();
        //钢协粗钢产量：L2id=230 and L2OrderNo=2 粗钢D1,钢材D3
        $gx_cg_gc_cl = $this->maindao->query("SELECT * FROM `L2Data` WHERE `L2Id` = '230' and L2OrderNo = '2' and DATE >= '".$sdate."' and DATE <= '".$edate."' ORDER BY DATE ASC");

        //钢协粗钢产量：L2id=230 and L2OrderNo=2 粗钢D1,钢材D3
        $hb_tb_date = date("Y-m-d",strtotime($sdate . " -1 year"));
        $hb_tb_gx_cg_gc_cl = $this->maindao->query("SELECT * FROM `L2Data` WHERE `L2Id` = '230' and L2OrderNo = '2' and DATE >= '".$hb_tb_date."' and DATE <= '".$edate."' ORDER BY DATE ASC");

        foreach($gx_cg_gc_cl as $item){
            $riqi = date("Y年n月",strtotime($item["DATE"]));
            $date_j = date("j",strtotime($item["DATE"]));

            $cg_hb = 0;
            $cg_tb = 0;
            $gc_hb = 0;
            $gc_tb = 0;
            $hb_date = date("Y-m-d",strtotime($item["DATE"] . " -10 day"));
            $hb_j = date("j",strtotime($hb_date));
            if($hb_j == "2" || $hb_j == "12" || $hb_j == "22"){
                $hb_date = date("Y-m-d",strtotime($hb_date . " -1 day"));
            }
            $tb_date = date("Y-m-d",strtotime($item["DATE"] . " -1 month"));
            $ret_arr[$riqi]["上旬"]["riqi"] = $riqi;
            if($date_j == 1){
                //上旬
                $ret_arr[$riqi]["上旬"]["cgcl"] = $item["D1"];
                $ret_arr[$riqi]["上旬"]["gccl"] = $item["D3"];
                foreach($hb_tb_gx_cg_gc_cl as $hb_tb){
                    if($hb_tb["DATE"] == $hb_date){
                        $cg_hb = round(($item["D1"] - $hb_tb["D1"])/$hb_tb["D1"] * 100 ,2);
                        $gc_hb = round(($item["D3"] - $hb_tb["D3"])/$hb_tb["D3"] * 100 ,2);
                    }else if($hb_tb["DATE"] == $tb_date){
                        // echo "同比：".$hb_tb["DATE"]."--".$tb_date."--".$item["D1"]."--".$hb_tb["D1"]."<br/>";
                        $cg_tb = round(($item["D1"] - $hb_tb["D1"])/$hb_tb["D1"] * 100 ,2);
                        $gc_tb = round(($item["D3"] - $hb_tb["D3"])/$hb_tb["D3"] * 100 ,2);
                    }
                }
                $ret_arr[$riqi]["上旬"]["cghb"] = $cg_hb;
                $ret_arr[$riqi]["上旬"]["cgtb"] = $cg_tb;
                $ret_arr[$riqi]["上旬"]["gchb"] = $gc_hb;
                $ret_arr[$riqi]["上旬"]["gctb"] = $gc_tb;
            }else if($date_j == 11){
                $ret_arr[$riqi]["中旬"]["cgcl"] = $item["D1"];
                $ret_arr[$riqi]["中旬"]["gccl"] = $item["D3"];
                foreach($hb_tb_gx_cg_gc_cl as $hb_tb){
                    if($hb_tb["DATE"] == $hb_date){
                        $cg_hb = round(($item["D1"] - $hb_tb["D1"])/$hb_tb["D1"] * 100 ,2);
                        $gc_hb = round(($item["D3"] - $hb_tb["D3"])/$hb_tb["D3"] * 100 ,2);
                    }else if($hb_tb["DATE"] == $tb_date){
                        $cg_tb = round(($item["D1"] - $hb_tb["D1"])/$hb_tb["D1"] * 100 ,2);
                        $gc_tb = round(($item["D3"] - $hb_tb["D3"])/$hb_tb["D3"] * 100 ,2);
                    }
                }
                $ret_arr[$riqi]["中旬"]["cghb"] = $cg_hb;
                $ret_arr[$riqi]["中旬"]["cgtb"] = $cg_tb;
                $ret_arr[$riqi]["中旬"]["gchb"] = $gc_hb;
                $ret_arr[$riqi]["中旬"]["gctb"] = $gc_tb;
            }else if($date_j == 21){
                $ret_arr[$riqi]["下旬"]["cgcl"] = $item["D1"];
                $ret_arr[$riqi]["下旬"]["gccl"] = $item["D3"];
                foreach($hb_tb_gx_cg_gc_cl as $hb_tb){
                    if($hb_tb["DATE"] == $hb_date){
                        $cg_hb = round(($item["D1"] - $hb_tb["D1"])/$hb_tb["D1"] * 100 ,2);
                        $gc_hb = round(($item["D3"] - $hb_tb["D3"])/$hb_tb["D3"] * 100 ,2);
                    }else if($hb_tb["DATE"] == $tb_date){
                        $cg_tb = round(($item["D1"] - $hb_tb["D1"])/$hb_tb["D1"] * 100 ,2);
                        $gc_tb = round(($item["D3"] - $hb_tb["D3"])/$hb_tb["D3"] * 100 ,2);
                    }
                }
                $ret_arr[$riqi]["下旬"]["cghb"] = $cg_hb;
                $ret_arr[$riqi]["下旬"]["cgtb"] = $cg_tb;
                $ret_arr[$riqi]["下旬"]["gchb"] = $gc_hb;
                $ret_arr[$riqi]["下旬"]["gctb"] = $gc_tb;
            }
        }

        $row_span_arr = array();
        foreach($ret_arr as $key=>$ret_item){
            $row_span_arr[$key] = count($ret_item);

        }
        // echo "<pre/>";print_r($ret_arr);print_r($row_span_arr);
        $params["edate"] = $edate;
        $params["sdate"] = $sdate;
        $params["type"] = $type;
        $this->assign("params",$params);
        $this->assign("ret_arr",$ret_arr);
        $this->assign("row_span_arr",$row_span_arr);
        $this->assign("mode",$params['mode']);
    }

    function gp_gc_jck($params){
        $sdate = date("Y-m-01",strtotime(date("Y-m-01") . " -1 year"));
        // $sdate = "2023-01-01";
        if($params["sdate"] != ""){
            $sdate = $params["sdate"];
        }

        $edate = date("Y-m-d");
        if($params["edate"] != ""){
            $edate = $params["edate"];
        }

        $type = 1;
        if($params["type"] != ""){
            $type = $params["type"];
        }

        $zd_1 = "D1";
        if($type == 1){
            $zd_1 = "D1";
            $params["title"] = "钢材、钢坯进口量";
        }else if($type == 2){
            $zd_1 = "D2";
            $params["title"] = "钢材、钢坯出口量";
        }

        $ret_arr = array();
        //钢坯进出口：L2id=193 and L2OrderNo=2 粗钢D1,钢材D3
        // $gp_jck = $this->maindao->query("SELECT * FROM `L2Data` WHERE `L2Id` = '193' and L2OrderNo = '2' and DATE >= '".$sdate."' and DATE <= '".$edate."' ORDER BY DATE ASC");

        //钢材进出口：L2id=193 and L2OrderNo=2 粗钢D1,钢材D3
        $gc_jck = $this->maindao->query("SELECT * FROM `L2Data` WHERE `L2Id` = '192' and L2OrderNo = '1' and DATE >= '".$sdate."' and DATE <= '".$edate."' ORDER BY DATE desc");

        //钢坯进出口：L2id=192 and L2OrderNo=1 粗钢D1,钢材D3
        $hb_tb_date = date("Y-m-d",strtotime($sdate . " -1 year"));
        $hb_tb_gp_jck = $this->maindao->query("SELECT * FROM `L2Data` WHERE `L2Id` = '193' and L2OrderNo = '2' and DATE >= '".$hb_tb_date."' and DATE <= '".$edate."' ORDER BY DATE desc");

        $hb_tb_gx_gc_jck = $this->maindao->query("SELECT * FROM `L2Data` WHERE `L2Id` = '192' and L2OrderNo = '1' and DATE >= '".$hb_tb_date."' and DATE <= '".$edate."' ORDER BY DATE desc");

        foreach($gc_jck as $gc_item){
            $riqi = date("Y年n月",strtotime($gc_item["DATE"]));
            $ret_arr[$riqi]["gc_data"] = round($gc_item[$zd_1],2);

            $hb_riqi = date("Y-m",strtotime($gc_item["DATE"] . " -1 month"));
            $tb_riqi = date("Y-m",strtotime($gc_item["DATE"] . " -1 year"));

            $gc_hb = 0;
            $gp_hb = 0;
            $gc_tb = 0;
            $gp_tb = 0;
            foreach($hb_tb_gx_gc_jck as $gc_v){
                if($hb_riqi == $gc_v["DATE"]){
                    $ret_arr[$riqi]["gc_hb"] = round(($gc_item[$zd_1] - $gc_v[$zd_1]) / $gc_v[$zd_1] * 100,2);
                }
                if($tb_riqi == $gc_v["DATE"]){
                    $ret_arr[$riqi]["gc_tb"] = round(($gc_item[$zd_1] - $gc_v[$zd_1]) / $gc_v[$zd_1] * 100,2);
                }
            }

            foreach($hb_tb_gp_jck as $gp_v){
                if($gp_v["DATE"] == $gc_item["DATE"]){
                    $ret_arr[$riqi]["gp_data"] = round($gp_v[$zd_1],2);
                }

                if($hb_riqi == $gp_v["DATE"]){
                    if($gp_v[$zd_1] == 0 || $gp_v[$zd_1] == ""){
                        $ret_arr[$riqi]["gp_hb"] = "--";
                    }else{
                        $ret_arr[$riqi]["gp_hb"] = round(($ret_arr[$riqi]["gp_data"] - $gp_v[$zd_1]) / $gp_v[$zd_1] * 100,2);
                    }
                    
                }
                if($tb_riqi == $gp_v["DATE"]){
                    if($gp_v[$zd_1] == 0 || $gp_v[$zd_1] == ""){
                        $ret_arr[$riqi]["gp_tb"] = "--";
                    }else{
                        $ret_arr[$riqi]["gp_tb"] = round(($ret_arr[$riqi]["gp_data"] - $gp_v[$zd_1]) / $gp_v[$zd_1] * 100,2);
                    }
                }
            }
        }
        // echo "<pre/>";print_r($ret_arr);
        $params["edate"] = $edate;
        $params["sdate"] = $sdate;
        $this->assign("params",$params);
        $this->assign("ret_arr",$ret_arr);
        $this->assign("mode",$params['mode']);
    }

    function quanqiu_cgcl($params){
        $sdate = date("Y-m-01",strtotime(date("Y-m-d") . " -1 year"));
        // $sdate = "2023-01";
        if($params["sdate"] != ""){
            $sdate = date("Y-m",strtotime($params["sdate"]));
        }

        $edate = date("Y-m-d");
        if($params["edate"] != ""){
            $edate = date("Y-m",strtotime($params["edate"]));
        }

        //全球粗钢产量
        $qq_cgcl = $this->drcwdao->query("SELECT * FROM `data_table` WHERE dta_type = 'CG' and  dta_1='合计' and dta_ym >= '".$sdate."' and dta_ym <= '".$edate."' order by dta_ym desc");

        //中国
        $zg_cgcl = $this->drcwdao->query("SELECT * FROM `data_table` WHERE dta_type = 'CG' and  dta_1='中国大陆' and dta_ym >= '".$sdate."' and dta_ym <= '".$edate." order by dta_ym desc'");

        //美国
        $mg_cgcl = $this->drcwdao->query("SELECT * FROM `data_table` WHERE dta_type = 'CG' and  dta_1='美国' and dta_ym >= '".$sdate."' and dta_ym <= '".$edate."' order by dta_ym desc");

        //欧盟
        $om_cgcl = $this->drcwdao->query("SELECT * FROM `data_table` WHERE dta_type = 'CG' and  dta_1='欧盟(27国)' and dta_ym >= '".$sdate."' and dta_ym <= '".$edate."' order by dta_ym desc");

        //英国
        $yg_cgcl = $this->drcwdao->query("SELECT * FROM `data_table` WHERE dta_type = 'CG' and  dta_1='英国' and dta_ym >= '".$sdate."' and dta_ym <= '".$edate."' order by dta_ym desc");

        //日本
        $rb_cgcl = $this->drcwdao->query("SELECT * FROM `data_table` WHERE dta_type = 'CG' and  dta_1='日本' and dta_ym >= '".$sdate."' and dta_ym <= '".$edate."' order by dta_ym desc");

        $ret_arr = array();

        foreach($qq_cgcl as $qq_v){
            $riqi = date("Y年n月",strtotime($qq_v["dta_ym"]));
            $ret_arr[$riqi]["qqcgcl"] = $qq_v["dta_2"];
            foreach($zg_cgcl as $zg_v){
                if($qq_v["dta_ym"] == $zg_v["dta_ym"]){
                    $ret_arr[$riqi]["zgcgcl"] = $zg_v["dta_2"];
                }
            }

            foreach($mg_cgcl as $mg_v){
                if($qq_v["dta_ym"] == $mg_v["dta_ym"]){
                    $ret_arr[$riqi]["mgcgcl"] = $mg_v["dta_2"];
                }
            }

            foreach($om_cgcl as $om_v){
                if($qq_v["dta_ym"] == $om_v["dta_ym"]){
                    $ret_arr[$riqi]["omcgcl"] = $om_v["dta_2"];
                }
            }

            foreach($yg_cgcl as $yg_v){
                if($qq_v["dta_ym"] == $yg_v["dta_ym"]){
                    $ret_arr[$riqi]["ygcgcl"] = $yg_v["dta_2"];
                }
            }

            foreach($rb_cgcl as $rb_v){
                if($qq_v["dta_ym"] == $rb_v["dta_ym"]){
                    $ret_arr[$riqi]["rbcgcl"] = $rb_v["dta_2"];
                }
            }
        }
        // echo "<pre/>";print_r($params);echo "</pre>";
        $params["edate"] = $edate;
        $params["sdate"] = $sdate;
        $this->assign("params",$params);
        $this->assign("ret_arr",$ret_arr);
        $this->assign("mode",$params['mode']);
    }

    function hyxq_hz($params){
        $params["edate"] = $edate;
        $params["sdate"] = $sdate;
        $this->assign("params",$params);
        $this->assign("ret_arr",$ret_arr);
        $this->assign("mode",$params['mode']);
    }

    //钢材供应
    function gc_gy_tables($params){
        
        $sdate = date("Y-m-01",strtotime(date("Y-m-d") . " -1 year"));
        // $sdate = "2023-01";
        if($params["sdate"] != ""){
            $sdate = date("Y-m",strtotime($params["sdate"]));
        }

        $edate = date("Y-m-d");
        if($params["edate"] != ""){
            $edate = date("Y-m",strtotime($params["edate"]));
        }

        $type = $params["type"] == ""?1:$params["type"];
        $getData = array();
        
        $td1_str = "";
        if($type == 1){
            //基础设施建设投资: L2id=203 and L2OrderNo=42
            $getData = $this->get_hgData(203,42,"",$edate);
            $td1_str = "基建投资";
        }else if($type == 2){
            //房地产开发投资: L2id=141 and L2OrderNo=43
		    $getData = $this->get_hgData(141,43,"",$edate);
            $td1_str = "房地产投资";
        }else if($type == 3){
            //房屋销售面积：L2id=153 and L2OrderNo=1 取D3为当月 D4为累计
            $getData = $this->get_hgData(153,1,"",$edate);
            $td1_str = "房地产成交";
        }else if($type == 4){
            // 房屋新开工面积：L2id=154 and L2OrderNo=2 取D1
		    $getData = $this->get_hgData(154,2,"",$edate);
            $td1_str = "房屋新开工面积";
        }else if($type == 5){
            //房屋施工面积：L2id=155 and L2OrderNo=3 取D2
            $getData = $this->get_hgData(155,3,"",$edate);
            $td1_str = "房屋施工面积";
        }else if($type == 6){
            // 汽车：L2id=172 and L2OrderNo=1 取D1
		    $getData = $this->get_hgData(172,1,"",$edate);
            $td1_str = "汽车";
        }else if($type == 7){
            // 挖掘机：L2id=187 and L2OrderNo=3 取D1
		    $getData = $this->get_hgData(187,3,"",$edate);
            $td1_str = "挖掘机产量";
        }else if($type == 8){
            // 工业增加值 ：L2id=142 and L2OrderNo=44取D1和D2
		    $getData = $this->get_hgData(142,44,"",$edate);
            $td1_str = "工业增加值";
        }else if($type == 9){
            // 集装箱：L2id=185 and L2OrderNo=2 取D1
		    $getData = $this->get_hgData(185,2,"",$edate);
            $td1_str = "集装箱";
        }else if($type == 10){
            // 空调：L2id=182 and L2OrderNo=8 取D1
            $getData = $this->get_hgData(182,8,"",$edate);
            $td1_str = "空调产量";
        }else if($type == 11){
            // 冰箱：L2id=180 and L2OrderNo=6 取D1
            $getData = $this->get_hgData(180,6,"",$edate);
            $td1_str = "冰箱产量";
        }else if($type == 12){
            // 洗衣机：L2id=178 and L2OrderNo=5 取D1
            $getData = $this->get_hgData(178,5,"",$edate);
            $td1_str = "洗衣机产量";
        }
		

        
        $td2_str = "";
        $td3_str = "";
        $ret_arr= array();
        foreach($getData as $key=>&$v){
            if($td2_str == ""){
                $td2_str = date("Y",strtotime($v["DATE"]));
                $td3_str = ($td2_str - 1) . "年";
                $td2_str = $td2_str . "年";
            }

            $qn_date_key = date("Y年n月",strtotime($v["DATE"] . " -1 year"));
            
            $v["qntq"] = $getData[$qn_date_key]["D1"];
            $day3 = date("n月",strtotime($v["DATE"]));
            $v["day3"] = $day3;

            //环比date
            if($day3 == "2月"){
                $hb_date = date("Y年n月",strtotime($v["DATE"] . " -2 month"));
            }else{
                $hb_date = date("Y年n月",strtotime($v["DATE"] . " -1 month"));
            }
            
            // echo $hb_date."<br/>";

            if($getData[$hb_date]["D1"] != "" && $getData[$hb_date]["D1"] != 0){
                $v["hb"] = round(($v["D1"] - $getData[$hb_date]["D1"]) / $getData[$hb_date]["D1"] * 100,2);
            }
            
            // echo $sdate."==".date("Y-n",strtotime($v["DATE"]))."<br/>";
            if($sdate > date("Y-n",strtotime($v["DATE"]))){
                break;
            }else{
                $ret_arr[] = $v;
            }
                
        }
        // echo "<pre/>"; print_r($ret_arr);echo "</pre>";
        $params["type"] = $type;
        $this->assign("params",$params);
        $this->assign("ret_arr",$ret_arr);
        $this->assign("mode",$params['mode']);
        $this->assign("td1_str",$td1_str);
        $this->assign("td2_str",$td2_str);
        $this->assign("td3_str",$td3_str);
    }

    function zc_hz_teble($params){
        $sdate = date("Y-m-01",strtotime(date("Y-m-d") . " -1 year"));
        // $sdate = "2023-01";
        if($params["sdate"] != ""){
            $sdate = date("Y-m",strtotime($params["sdate"]));
        }

        $edate = date("Y-m-d");
        if($params["edate"] != ""){
            $edate = date("Y-m",strtotime($params["edate"]));
        }

         // 造船完工量：L2id=468 and L2OrderNo=1 取D1
		$zcwgl_data = $this->get_hgData(468,1,"",$edate);

		// 造船新接订单：L2id=469 and L2OrderNo=2 取D1
		$zcxjdd_data = $this->get_hgData(469,2,"",$edate);

        // 造船手持订单量：L2id=470 and L2OrderNo=3 取D1
		$zcscddl_data = $this->get_hgData(470,3,"",$edate);


        $td2_str = "";
        $td3_str = "";
        $ret_arr= array();
        foreach($zcwgl_data as $zc_key=>&$zc_v){
            if($td2_str == ""){
                $td2_str = date("Y",strtotime($zc_v["DATE"]));
                $td3_str = ($td2_str - 1) . "年";
                $td2_str = $td2_str . "年";
            }

            $day3 = date("Y年n月",strtotime($zc_v["DATE"]));

            if($sdate > date("Y-n",strtotime($zc_v["DATE"]))){
                break;
            }else{
                //完工量
                $qn_riqi = date("Y年n月",strtotime($zc_v["DATE"] . " -1 year"));

                $ret_arr[$day3]["zcwgl"]["riqi"] = date("n月",strtotime($zc_v["DATE"]));

                $ret_arr[$day3]["zcwgl"]["qnwgl"] = $zcwgl_data[$qn_riqi]["D1"];

                $ret_arr[$day3]["zcwgl"]["jnwgl"] = $zc_v["D1"];

                $sy_riqi = date("Y-m-01",strtotime($zc_v["DATE"] . " -1 month"));
                $i = 0;
                while ($i <= 12) {
                    if(!empty($zcwgl_data[date("Y年n月",strtotime($sy_riqi))])){
                        $i = 13;break;
                    }else{
                        $sy_riqi = date("Y-m-01",strtotime($sy_riqi . " -1 month"));
                    }
                    $i++;
                }
                $sy_riqi = date("Y年n月",strtotime($sy_riqi));

                if($zcwgl_data[$sy_riqi]["D1"] != "" && $zcwgl_data[$sy_riqi]["D1"] != 0)
                    $ret_arr[$day3]["zcwgl"]["jnhb"] = round(($zc_v["D1"] - $zcwgl_data[$sy_riqi]["D1"]) / $zcwgl_data[$sy_riqi]["D1"] * 100,2);
                $ret_arr[$day3]["zcwgl"]["jntb"] = $zc_v["D3"];

                $ret_arr[$day3]["zcwgl"]["jnlj"] = $zc_v["D2"];

                $ret_arr[$day3]["zcwgl"]["jnljtb"] = $zc_v["D4"];

                //新接订单量
                $ret_arr[$day3]["zcxjddl"]["riqi"] = date("n月",strtotime($zc_v["DATE"]));

                $ret_arr[$day3]["zcxjddl"]["qnxjddl"] = $zcxjdd_data[$qn_riqi]["D1"];

                $ret_arr[$day3]["zcxjddl"]["jnxjddl"] = $zcxjdd_data[$day3]["D1"];

                if($zcxjdd_data[$sy_riqi]["D1"] != "" && $zcxjdd_data[$sy_riqi]["D1"] != 0)
                    $ret_arr[$day3]["zcxjddl"]["jnhb"] = round(($zcxjdd_data[$day3]["D1"] - $zcxjdd_data[$sy_riqi]["D1"]) / $zcxjdd_data[$sy_riqi]["D1"] * 100,2);
                $ret_arr[$day3]["zcxjddl"]["jntb"] = $zcxjdd_data[$day3]["D3"];

                $ret_arr[$day3]["zcxjddl"]["jnlj"] = $zcxjdd_data[$day3]["D2"];

                $ret_arr[$day3]["zcxjddl"]["jnljtb"] = $zcxjdd_data[$day3]["D4"];

                //手持订单量
                $ret_arr[$day3]["scddl"]["riqi"] = date("n月",strtotime($zc_v["DATE"]));

                $ret_arr[$day3]["scddl"]["qnscddl"] = $zcscddl_data[$qn_riqi]["D1"];

                $ret_arr[$day3]["scddl"]["jnscddl"] = $zcscddl_data[$day3]["D1"];

                if($zcscddl_data[$sy_riqi]["D1"] != "" && $zcscddl_data[$sy_riqi]["D1"] != 0){
                    $ret_arr[$day3]["scddl"]["jnhb"] = round(($zcscddl_data[$day3]["D1"] - $zcscddl_data[$sy_riqi]["D1"]) / $zcscddl_data[$sy_riqi]["D1"] * 100,2);
                }
                    
                $ret_arr[$day3]["scddl"]["jntb"] = $zcscddl_data[$day3]["D2"];

                // $ret_arr[$day3]["scddl"]["jnlj"] = $zcscddl_data[$day3]["D2"];

                // $ret_arr[$day3]["scddl"]["jnljtb"] = $zcscddl_data[$day3]["D4"];

            }
        }
        // echo "<pre/>"; print_r($ret_arr);echo "</pre>";
        $this->assign("params",$params);
        $this->assign("ret_arr",$ret_arr);
        $this->assign("mode",$params['mode']);
        $this->assign("td2_str",$td2_str);
        $this->assign("td3_str",$td3_str);
    }
    
    function jdhy_hz_teble($params){
        if(date("n") < 3){
            $sdate = date("Y-01-01",strtotime(date("Y-m-d") . " -2 year"));
            $edate = date("Y-12-31",strtotime(date("Y-m-d") . " -1 year"));
        }else{
            $sdate = date("Y-m-01",strtotime(date("Y-m-d") . " -1 year"));
            $edate = date("Y-m-d");
        }
        
        // $sdate = "2023-01";
        if($params["sdate"] != ""){
            $sdate = date("Y-m",strtotime($params["sdate"]));
        }

        
        if($params["edate"] != ""){
            $edate = date("Y-m",strtotime($params["edate"]));
        }

        // 空调：L2id=182 and L2OrderNo=8 取D1
		$kt_data = $this->get_hgData(182,8,"",$edate);

		// 冰箱：L2id=180 and L2OrderNo=6 取D1
		$bx_data = $this->get_hgData(180,6,"",$edate);

		// 洗衣机：L2id=178 and L2OrderNo=5 取D1
		$xyj_data = $this->get_hgData(178,5,"",$edate);

        $ret_arr = array();
        
        $jn_date_y = date("Y",strtotime($edate));
        $jn_date_m = date("n",strtotime($edate));

        $i = 0;
        while($i <= 12){
            if(!empty($kt_data[$jn_date_y."年".$jn_date_m."月"])){
                $i = 13;break;
            }else{
                $jn_date_m--;
            }
            $i++;
        }
        
        $qn_day = $jn_date_y - 1;
        $ret_arr[0] = array(
            "name"=>"空调",
            "qn_cl"=>$kt_data[$qn_day."年12月"]["D2"],
            "qn_tb"=>$kt_data[$qn_day."年12月"]["D4"],
            "jn_cl"=>$kt_data[$jn_date_y."年".$jn_date_m."月"]["D2"],
            "jn_tb"=>$kt_data[$jn_date_y."年".$jn_date_m."月"]["D4"]
        );

        $ret_arr[1] = array(
            "name"=>"冰箱",
            "qn_cl"=>$bx_data[$qn_day."年12月"]["D2"],
            "qn_tb"=>$bx_data[$qn_day."年12月"]["D4"],
            "jn_cl"=>$bx_data[$jn_date_y."年".$jn_date_m."月"]["D2"],
            "jn_tb"=>$bx_data[$jn_date_y."年".$jn_date_m."月"]["D4"]
        );

        $ret_arr[2] = array(
            "name"=>"洗衣机",
            "qn_cl"=>$xyj_data[$qn_day."年12月"]["D2"],
            "qn_tb"=>$xyj_data[$qn_day."年12月"]["D4"],
            "jn_cl"=>$xyj_data[$jn_date_y."年".$jn_date_m."月"]["D2"],
            "jn_tb"=>$xyj_data[$jn_date_y."年".$jn_date_m."月"]["D4"]
        );
        // echo $qn_day."年12月--";echo $jn_date_y."年".$jn_date_m."月";
        // echo "<pre/>"; print_r($ret_arr);echo "</pre>";
        $this->assign("params",$params);
        $this->assign("ret_arr",$ret_arr);
        $this->assign("mode",$params['mode']);
        $this->assign("jn_date_y",$jn_date_y);
        $this->assign("jn_date_m",$jn_date_m);
        $this->assign("qn_day",$qn_day);
    }

    function qc_hy_hz($params){
        if(date("n") < 3){
            $sdate = date("Y-01-01",strtotime(date("Y-m-d") . " -2 year"));
            $edate = date("Y-12-31",strtotime(date("Y-m-d") . " -1 year"));
        }else{
            $sdate = date("Y-m-01",strtotime(date("Y-m-d") . " -1 year"));
            $edate = date("Y-m-d");
        }
        
        // $sdate = "2023-01";
        if($params["sdate"] != ""){
            $sdate = date("Y-m",strtotime($params["sdate"]));
        }

        
        if($params["edate"] != ""){
            $edate = date("Y-m",strtotime($params["edate"]));
        }

        //乘用车 L2Id = 647 ,L2OrderOn = 22
        $cyc_data = $this->get_hgData(647,22,"",$edate);

        //商用车 L2Id = 646 ,L2OrderOn = 21
        $syc_data = $this->get_hgData(646,21,"",$edate);

        //新能源车 L2Id = 603 ,L2OrderOn = 12
        $xnyc_data = $this->get_hgData(603,12,"",$edate);

        $jn_date_y = date("Y",strtotime($edate));
        $jn_date_m = date("n",strtotime($edate));

        $i = 0;
        while($i <= 12){
            if(!empty($cyc_data[$jn_date_y."年".$jn_date_m."月"])){
                $i = 13;break;
            }else{
                $jn_date_m--;
            }
            $i++;
        }
        
        $qn_day = $jn_date_y - 1;

        if($cyc_data[$jn_date_y."年".($jn_date_m-1)."月"]["D1"] != "" && $cyc_data[$jn_date_y."年".($jn_date_m-1)."月"]["D1"] !=0)
            $cyc_jn_dyhb = round(($cyc_data[$jn_date_y."年".$jn_date_m."月"]["D1"] - $cyc_data[$jn_date_y."年".($jn_date_m-1)."月"]["D1"]) / $cyc_data[$jn_date_y."年".($jn_date_m-1)."月"]["D1"] * 100,2);
        $ret_arr[0] = array(
            "name"=>"乘用车",
            "qn_cl"=>$cyc_data[$qn_day."年12月"]["D2"],
            "jn_dycl"=>$cyc_data[$jn_date_y."年".$jn_date_m."月"]["D1"],
            "jn_dyhb"=>$cyc_jn_dyhb,
            "jn_dytb"=>$cyc_data[$jn_date_y."年".$jn_date_m."月"]["D3"],
            "jn_lj"=>$cyc_data[$jn_date_y."年".$jn_date_m."月"]["D2"],
            "jn_ljtb"=>$cyc_data[$jn_date_y."年".$jn_date_m."月"]["D4"]
        );

        if($syc_data[$jn_date_y."年".($jn_date_m-1)."月"]["D1"] != "" && $syc_data[$jn_date_y."年".($jn_date_m-1)."月"]["D1"] !=0)
            $syc_jn_dyhb = round(($syc_data[$jn_date_y."年".$jn_date_m."月"]["D1"] - $syc_data[$jn_date_y."年".($jn_date_m-1)."月"]["D1"]) / $syc_data[$jn_date_y."年".($jn_date_m-1)."月"]["D1"] * 100,2);
        $ret_arr[1] = array(
            "name"=>"商用车",
            "qn_cl"=>$syc_data[$qn_day."年12月"]["D2"],
            "jn_dycl"=>$syc_data[$jn_date_y."年".$jn_date_m."月"]["D1"],
            "jn_dyhb"=>$syc_jn_dyhb,
            "jn_dytb"=>$syc_data[$jn_date_y."年".$jn_date_m."月"]["D3"],
            "jn_lj"=>$syc_data[$jn_date_y."年".$jn_date_m."月"]["D2"],
            "jn_ljtb"=>$syc_data[$jn_date_y."年".$jn_date_m."月"]["D4"]
        );

        if($xnyc_data[$jn_date_y."年".($jn_date_m-1)."月"]["D1"] != "" && $xnyc_data[$jn_date_y."年".($jn_date_m-1)."月"]["D1"] !=0)
            $xnyc_jn_dyhb = round(($xnyc_data[$jn_date_y."年".$jn_date_m."月"]["D1"] - $xnyc_data[$jn_date_y."年".($jn_date_m-1)."月"]["D1"]) / $xnyc_data[$jn_date_y."年".($jn_date_m-1)."月"]["D1"] * 100,2);
        $ret_arr[2] = array(
            "name"=>"新能源车",
            "qn_cl"=>$xnyc_data[$qn_day."年12月"]["D2"],
            "jn_dycl"=>$xnyc_data[$jn_date_y."年".$jn_date_m."月"]["D1"],
            "jn_dyhb"=>$xnyc_jn_dyhb,
            "jn_dytb"=>$xnyc_data[$jn_date_y."年".$jn_date_m."月"]["D3"],
            "jn_lj"=>$xnyc_data[$jn_date_y."年".$jn_date_m."月"]["D2"],
            "jn_ljtb"=>$xnyc_data[$jn_date_y."年".$jn_date_m."月"]["D4"]
        );

        $this->assign("params",$params);
        $this->assign("ret_arr",$ret_arr);
        $this->assign("mode",$params['mode']);
        $this->assign("jn_date_y",$jn_date_y);
        $this->assign("jn_date_m",$jn_date_m);
        $this->assign("qn_day",$qn_day);
    }

    function jxzz_hy_hz($params){
        if(date("n") < 3){
            $sdate = date("Y-01-01",strtotime(date("Y-m-d") . " -2 year"));
            $edate = date("Y-12-31",strtotime(date("Y-m-d") . " -1 year"));
        }else{
            $sdate = date("Y-m-01",strtotime(date("Y-m-d") . " -1 year"));
            $edate = date("Y-m-d");
        }
        
        // $sdate = "2023-01";
        if($params["sdate"] != ""){
            $sdate = date("Y-m",strtotime($params["sdate"]));
        }

        
        if($params["edate"] != ""){
            $edate = date("Y-m",strtotime($params["edate"]));
        }

        //挖掘机产量 L2id=187 and L2OrderNo=3 取D1
        $wjj_cl = $this->get_hgData(187,3,"",$edate);

        //水泥专用设备产量L2id=161 and L2OrderNo=16
        $shuini_cl = $this->get_hgData(161,16,"",$edate);

        //金属冶炼设备产量 L2id=169 14
        $jsylsb_cl = $this->get_hgData(169,14,"",$edate);

        //大型拖拉机产量  L2id=170 15
        $dxtlj_cl = $this->get_hgData(170,15,"",$edate);

        $jn_date_y = date("Y",strtotime($edate));
        $jn_date_m = date("n",strtotime($edate));
        
        $i = 0;
        while($i <= 12){
            if(!empty($wjj_cl[$jn_date_y."年".$jn_date_m."月"])){
                $i = 13;break;
            }else{
                $jn_date_m--;
            }
            $i++;
        }

        $qn_day = $jn_date_y - 1;

        $ret_arr[0] = array(
            "name"=>"机械制造",
            "zhibiao"=>"挖掘机产量",
            "qn_ljtb"=>$wjj_cl[$qn_day."年12月"]["D4"],
            "jn_dytb"=>$wjj_cl[$jn_date_y."年".$jn_date_m."月"]["D3"],
            "jn_ljtb"=>$wjj_cl[$jn_date_y."年".$jn_date_m."月"]["D4"],
            "jiao_qntb"=>($wjj_cl[$jn_date_y."年".$jn_date_m."月"]["D4"] - ($wjj_cl[$qn_day."年12月"]["D4"])),
            "hb"=>($wjj_cl[$jn_date_y."年".$jn_date_m."月"]["D4"] - ($wjj_cl[$jn_date_y."年".$jn_date_m."月"]["D3"]))
        );

        $ret_arr[1] = array(
            "zhibiao"=>"水泥专用设备产量",
            "qn_ljtb"=>$shuini_cl[$qn_day."年12月"]["D4"],
            "jn_dytb"=>$shuini_cl[$jn_date_y."年".$jn_date_m."月"]["D3"],
            "jn_ljtb"=>$shuini_cl[$jn_date_y."年".$jn_date_m."月"]["D4"],
            "jiao_qntb"=>($shuini_cl[$jn_date_y."年".$jn_date_m."月"]["D4"] - ($shuini_cl[$qn_day."年12月"]["D4"])),
            "hb"=>($shuini_cl[$jn_date_y."年".$jn_date_m."月"]["D4"] - ($shuini_cl[$jn_date_y."年".$jn_date_m."月"]["D3"]))
        );

        $ret_arr[2] = array(
            "zhibiao"=>"金属冶炼设备产量",
            "qn_ljtb"=>$jsylsb_cl[$qn_day."年12月"]["D4"],
            "jn_dytb"=>$jsylsb_cl[$jn_date_y."年".$jn_date_m."月"]["D3"],
            "jn_ljtb"=>$jsylsb_cl[$jn_date_y."年".$jn_date_m."月"]["D4"],
            "jiao_qntb"=>($jsylsb_cl[$jn_date_y."年".$jn_date_m."月"]["D4"] - ($jsylsb_cl[$qn_day."年12月"]["D4"])),
            "hb"=>($jsylsb_cl[$jn_date_y."年".$jn_date_m."月"]["D4"] - ($jsylsb_cl[$jn_date_y."年".$jn_date_m."月"]["D3"]))
        );

        $ret_arr[3] = array(
            "zhibiao"=>"大型拖拉机产量",
            "qn_ljtb"=>$dxtlj_cl[$qn_day."年12月"]["D4"],
            "jn_dytb"=>$dxtlj_cl[$jn_date_y."年".$jn_date_m."月"]["D3"],
            "jn_ljtb"=>$dxtlj_cl[$jn_date_y."年".$jn_date_m."月"]["D4"],
            "jiao_qntb"=>($dxtlj_cl[$jn_date_y."年".$jn_date_m."月"]["D4"] - ($dxtlj_cl[$qn_day."年12月"]["D4"])),
            "hb"=>($dxtlj_cl[$jn_date_y."年".$jn_date_m."月"]["D4"] - ($dxtlj_cl[$jn_date_y."年".$jn_date_m."月"]["D3"]))
        );

        $this->assign("params",$params);
        $this->assign("ret_arr",$ret_arr);
        $this->assign("mode",$params['mode']);
        $this->assign("jn_date_y",$jn_date_y);
        $this->assign("jn_date_m",$jn_date_m);
        $this->assign("qn_day",$qn_day);
    }
}