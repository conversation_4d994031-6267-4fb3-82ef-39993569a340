<?php
include_once(FRAME_LIB_DIR . "/controller/AbstractController.inc.php");

class sdgt_market_ycController extends AbstractController
{

    public function __construct()
    {
        parent::__construct();
        $this->_action->setDao(new sdgt_market_ycDao('DRCW'));
        $this->_action->maindao = new sdgt_market_ycDao('91R');
        $this->_action->gcdao = new sdgt_market_ycDao('GC');
        $this->_action->drcwdao = new sdgt_market_ycDao('DRCW');
        $this->_action->drcdao = new sdgt_market_ycDao('DRC');
        $this->_action->sms = new sdgt_market_ycDao("98SMR");
        $this->_action->t1dao = new sdgt_market_ycDao("MAIN");
    }

    public function v_day_market_yc()
    {
        $this->_action->day_market_yc($this->_request);
    }

    public function v_yue_market_yc()
    {
        $this->_action->yue_market_yc($this->_request);
    }

    public function v_forecast_list()
    {
        $this->_action->forecast_list($this->_request);
    }

    public function do_ajax_forecast_list()
    {
        $this->_action->ajax_forecast_list($this->_request);
    }

    public function v_xun_dingjia()
    {
        $this->_action->xun_dingjia($this->_request);
    }

    public function v_zhou_dingjia()
    {
        $this->_action->zhou_dingjia($this->_request);
    }

    public function v_week_market_yc()
    {
        $this->_action->week_market_yc($this->_request);
    }


    public function v_yue_market_price()
    {
        $this->_action->yue_market_price($this->_request);
    }
    public function do_run_create_dingjia_list(){
        $this->_action->run_create_dingjia_list($this->_request);
    }
    public function v_dingjia_list(){
        $this->_action->dingjia_list($this->_request);
    }
    public function do_ajax_dingjia_list()
    {
        $this->_action->ajax_dingjia_list($this->_request);
    }

    public function do_saving_marketNews()
    {
        $this->_action->saving_marketNews($this->_request);
    }
    public function do_saving_marketNews2()
    {
        $this->_action->saving_marketNews2($this->_request);
    }

    public function do_exportPdf()
    {
        $this->_action->exportPdf($this->_request);
    }
    public function v_datacenter_manage(){
        $this->_action->datacenter_manage($this->_request);
    }
    public function v_hg_jj_fenxi(){
        $this->_action->hg_jj_fenxi($this->_request);
    }
    public function v_cg_gxph(){
        $this->_action->cg_gxph($this->_request);
    }
    public function v_tjj_cg_cl(){
        $this->_action->tjj_cg_cl($this->_request);
    }
    public function v_gx_xun_cg_gc_cl(){
        $this->_action->gx_xun_cg_gc_cl($this->_request);
    }
    public function v_gp_gc_jck(){
        $this->_action->gp_gc_jck($this->_request);
    }

    public function v_quanqiu_cgcl(){
        $this->_action->quanqiu_cgcl($this->_request);
    }

    public function v_hyxq_hz(){
        $this->_action->hyxq_hz($this->_request);
    }

    public function v_gc_gy_tables(){
        $this->_action->gc_gy_tables($this->_request);
    }
    public function v_zc_hz_teble(){
        $this->_action->zc_hz_teble($this->_request);
    }
    public function v_jdhy_hz_teble(){
        $this->_action->jdhy_hz_teble($this->_request);
    }
    public function v_qc_hy_hz(){
        $this->_action->qc_hy_hz($this->_request);
    }
    public function v_jxzz_hy_hz(){
        $this->_action->jxzz_hy_hz($this->_request);
    }
}