<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );

class sgscjcpriceController extends AbstractController{
  
  public function __construct(){
    parent::__construct();
	$this->_action->setDao( new sgscjcpriceDao("DRCW") );
	$this->_action->t1Dao=new sgscjcpriceDao("MAIN");
	$this->_action->homeDao=new sgscjcpriceDao("91R");
	$this->_action->gcDao=new sgscjcpriceDao("GC");
  }

	public function v_index(){
		$this->_action->index( $this->_request );
	}
	

	public function do_lwgprice(){
		$this->_action->lwgprice( $this->_request );
	}

	public function do_cityandwj_avg(){
		$this->_action->cityandwj_avg( $this->_request );
	}
	public function do_target(){
		$this->_action->target( $this->_request );
	}
	public function do_addtarget(){
		$this->_action->addtarget( $this->_request );
	}
	public function do_inserttarget(){
		$this->_action->inserttarget( $this->_request );
	}
	public function do_deltarget(){
		$this->_action->deltarget( $this->_request );
	}
	public function do_xacdandqg(){
		$this->_action->xacdandqg( $this->_request );
	}
	public function do_fourcity(){
		$this->_action->fourcity( $this->_request );
	}
    //品牌价差（新增需求）
	public function v_ppjc(){
		$this->_action->ppjc( $this->_request );
	}
	//全国均价
	public function do_qgavg(){
		$this->_action->qgavg( $this->_request );
	}
}
?>