<?php 
$GLOBALS['type_date']=date("Y-m-d");
$GLOBALS['type_Y']=date("Y");
$GLOBALS['type_Y_m']=date("Y-m");
class DcZhbXunDjAction extends AbstractAction
{
    public $stedao;

    public function __construct()
    {
        parent::__construct();
    }  
	


    public function index($params)
    {	
		if(isset($params['curdate'])){
			//$GLOBALS['date']=$params['curdate'];
			$GLOBALS['type_date']=$params['curdate'];
			$GLOBALS['type_Y']=date("Y",strtotime($GLOBALS['type_date']));
			$GLOBALS['type_Y_m']=date("Y-m",strtotime($GLOBALS['type_date']));
			//$GLOBALS['type_date_h']=date("Y-m-d H:m:s",strtotime($GLOBALS['type_date']));
			//print_r($GLOBALS['type_date_h']);
		}
		//include_once("/etc/steelconf/config/isholiday.php");
		//$firstday = date('Y-m-01');
		$firstday = $GLOBALS['type_Y_m'].'-01';
		$lastday = date('d',strtotime("$firstday +1 month -1 day"));
		//print_r($lastday);exit;
		//$showtime=date("Y-m");
		//$showxun=date("d");
		$showtime=$GLOBALS['type_Y_m'];
		//print_r($showtime);
		$showxun=date("d",strtotime($GLOBALS['type_date']));
		//print_r($showxun);exit;
		$lastxunsd="20";//前一旬的开始日
		$lastxuned=$lastday;//前一旬的结束日
		$title_lastxunsd;
		//$showxun='10';//test
		//$showtime=date("2017-10");//test
		if($showxun > "10" && $showxun < "21"){
			$showxun="中";
			$lastxuned="10";
			$lastM=$showtime;
			$lastMDs=workday_gzj(date("Y-m-01",strtotime($GLOBALS['type_Y_m'])));
			$lastxunsd=date("j",strtotime($lastMDs));
			$lastMDe=$showtime."-".$lastxuned;
			$title_lastxunsd=date("Y-m-01",strtotime($GLOBALS['type_Y_m']));
		
		}else if($showxun <="31" && $showxun>"20"){
			$showxun="下";
			//$lastxunsd="11";
			$lastxuned="20";
			$lastM=$showtime;
			//$lastMDs=$lastM."-".$lastxunsd;
			$lastMDs=date("Y-m-11",strtotime($GLOBALS['type_Y_m']));
			$lastMDs=workday_gzj($lastMDs);
			$lastxunsd=date("j",strtotime($lastMDs));
			$lastMDe=$lastM."-".$lastxuned;
			$title_lastxunsd=date("Y-m-11",strtotime($GLOBALS['type_Y_m']));
		}else{
			$lastM=date('Y-m', strtotime("m -1 ",strtotime($GLOBALS['type_Y_m'])));
			$showxun="上";
			$lastxuned=date('t',strtotime('m -1 ',strtotime($GLOBALS['type_Y_m'])));
			//$lastxunsd="20";
			$lastMDs=$lastM."-21";
			$lastMDs=workday_gzj($lastMDs);
			$lastxunsd=date("j",strtotime($lastMDs));
			$lastMDe=$lastM."-".$lastxuned;
			//print_r($GLOBALS['type_Y_m']);
			$title_lastxunsd=date("Y-m-21",strtotime("m -1 ",strtotime($GLOBALS['type_Y_m'])));
			//print_r($title_lastxunsd);
		}
		$i=$lastxunsd;
		$j=0;
		//print_r($lastxuned);exit;
		//print_r($i);
		$da_arr=array();
		if($lastxunsd>21){
			
			$n=date("N",strtotime($lastMDs));
			//include_once("/etc/steelconf/config/isholiday.php");
			//$isexist = file_get_contents("https://holiday.steelhome.com/isholiday.php?date=".$lastMDs);
			$isexist=_isholiday($lastMDs);
			if($isexist != "1"){
				$da_arr[0]=$lastxunsd;
			}else if($n!="6" && $n!="7"){
				$da_arr[0]=$lastxunsd;
			}
			$i=1;
			$j=1;
		}
		//$d[0]=$lastxunsd;
		for($i;$i<=$lastxuned;$i++){
			$d[$j]=$i;
			$j++;
		}
		//print_r($d);
		$sql="select date,isholiday from steelhome.holiday  where date >='".$lastMDs."' and date <='".$lastMDe."'  order by date desc "; 
		$res=$this->maindao->Aquery($sql);
		foreach($d as $key=>$day){
			$date=$lastM."-".$day;

			$date = date("Y-m-d",strtotime($date));
			
			$n=date("N",strtotime($date));
			if($res[$date]!=''){
				
				if($res[$date]=='0'){
					$da_arr[]=$day;
				}
			}else if($n!="6" && $n!="7" ){
				$da_arr[]=$day;
			}
			
		}
		
		
		//print_r($da_arr);exit;
		$s="23:59:59";
		$e="23:59:59";
		$date1=$lastMDs." ".$s;
		$date2=$lastMDe." ".$e;
		//print_r($date1);
		//print_r($date2);
		
		$ng_res=$this->zhbsc_xs($showtime,$lastM."-".$lastxuned,$showxun);//exit;南钢棒材生产与销售情况
	
		$res_date=$this->bxunshijg($date1,$date2,$da_arr,$lastM);//本旬市场价格
		$this->gctj_hz($title_lastxunsd,$lastMDe);//钢厂调价汇总
		$this->gzjyuc($lastMDe,$lastMDs);//钢之家预测
		$showtime_last=date("Y-m",strtotime("m -1 ",strtotime($GLOBALS['type_date'])));
		$this->assign("showtime",date("Y年n月",strtotime($showtime)));
		$this->assign("showtime_last",date("Y年n月",strtotime($showtime_last)));
		$this->assign("showxun",$showxun);
		$arr=$this->djjy($title_lastxunsd,$date2,$ng_res,$res_date);//定价建议
		$title_lastxunsd=date("Y年n月j日",strtotime($title_lastxunsd));
		$this->assign("title_lastxunsd",$title_lastxunsd);
		$this->assign("lastxunsd",$lastxunsd);
		$this->assign("lastxuned",$lastxuned);
		
		
		$this->assign("lastM",date("Y年n月",strtotime($lastM)));
		$this->assign("d",$da_arr);
		//print_r($title_lastxunsd);exit;
		
		//print_r($res_date);exit;
		//echo 1111;
		if($_GET['issave']==1){
		   // 保存数据
		   if($showxun=='上'){
				$date=date("Y-m-01",strtotime($GLOBALS['type_Y_m']));
		   }else if($showxun=='中'){
				$date=date("Y-m-11",strtotime($GLOBALS['type_Y_m']));
		   }else{
			   $date=date("Y-m-21",strtotime($GLOBALS['type_Y_m']));
		   }
		   $sql="insert into `ng_price_model`(date,modeltype,createtime) values('".$date."','7',now())";
			// print_r($sql);//exit;
			$this->ngdao->execute($sql);
			$modelid=$this->ngdao->insert_id();
			//$modelid='';
			$moxdj=$arr['moxdj'];
			//print_r($moxdj);exit;
			$zd=$arr['zd'];
			$ngyg=$moxdj-$zd;
			//print_r($ngyg);
			
			$ins_xundj="insert into ng_price_model_detail(modelid,modelprice,modelprice_updown,modeloldprice,modelprice_type,	modelpirce_name,modelpirce_name_sort,Mid,uid,GUID,Ismakepricy_men,date,createtime) value('".$modelid."','".$moxdj."','".$zd."','".$ngyg."','7','20mmQ235B中厚板基价','1','-1','-1','','0','".$date."',now())";
			$this->ngdao->execute($ins_xundj);
		}
		//echo 2222;
	  $this->assign("params",$params);
	  
    } 

	//保存数据
	public function savexundj($params)
	{
		//print_r($params);exit;
		if(isset($params['curdate'])){
			$GLOBALS['type_date']=$params['curdate'];
			$GLOBALS['type_Y']=date("Y",strtotime($GLOBALS['type_date']));
			$GLOBALS['type_Y_m']=date("Y-m",strtotime($GLOBALS['type_date']));
			//$GLOBALS['type_date_h']=date("Y-m-d H:m:s",strtotime($GLOBALS['type_date']));
			$url = DC_URL.DCURL."/dczhbxundj.php?view=index&issave=1&curdate=".$params['curdate'];
		}else{
			$url = DC_URL.DCURL."/dczhbxundj.php?view=index&issave=1";
		}
		$showxun=date("d",strtotime($GLOBALS['type_date']));
		//$showxun=date("d");
		//$showxun='10';
		if($showxun > "10" && $showxun < "21"){
			$showxun="中";	
		}else if($showxun <="31" && $showxun>"20"){
			$showxun="下";		
		}else{
			$showxun="上";
		}
		$title="旬中厚板旬定价";
		$model=date("Y年n月",strtotime($GLOBALS['type_Y_m'])).$showxun;
		//print_r($model);
		$modeltitle=$model.$title;
		//$url = DC_URL.DCURL."/dczhbxundj.php?view=index&issave=1";
		include '/usr/local/www/libs/phpQuery/phpQuery.php';
		phpQuery::newDocumentFile($url);
		$html=pq("body")->html();
		print_r($url);
		$modelcontent=htmlentities($html, ENT_QUOTES,"UTF-8");
		
		$sql="update `ng_price_model` set modeltitle='".$modeltitle."',modelcontent='".$modelcontent."'  where modeltype='7' order by id desc limit 1";
		$this->ngdao->execute($sql);
		//echo 111;
		print_r($html);
		//print_r( $html);

	}
	public function zhbsc_xs($showtime,$end,$showxun)
	{
		
	$d=date("d",strtotime($GLOBALS['type_date']));	
	//$d=1;
	if($d >"10"){
		
		$month=$showtime;	
		$startdate=$showtime.'-'."01";
		
		$last_showtime=date("Y-m",strtotime("m -1 ",strtotime($GLOBALS['type_Y_m'])));
		$lastdate_s=date("Y-m-01",strtotime("m -1 ",strtotime($GLOBALS['type_Y_m'])));
		if($showxun=="中"){
			$date_arr=$this->workday(date("Y-m-11",strtotime($GLOBALS['type_Y_m'])));
			$enddate=$date_arr[1];
			$date_last=$this->workday(date("Y-m-11",strtotime("m -1 ",strtotime($GLOBALS['type_Y_m']))));
			$lastdate_e=$date_last[1];
		}else if ($showxun=="下"){
			$date_arr=$this->workday(date("Y-m-21",strtotime($GLOBALS['type_Y_m'])));
			$enddate=$date_arr[1];
			$date_last=$this->workday(date("Y-m-21",strtotime("m -1 ",strtotime($GLOBALS['type_Y_m']))));
			$lastdate_e=$date_last[1];
		}
		$this->assign("last_date",date("n月",strtotime($month)));
		$last_tdate=date("Y-m",strtotime("-1 month ",strtotime($GLOBALS['type_Y_m'])));
		//print_r($last_tdate);
		$this->assign("last_tdate",date("n月",strtotime($last_tdate)));
		
		/*print_r($enddate);
		print_r($lastdate_e);*/
	}else{
		
		$month=date("Y-m",strtotime("m -1 ",strtotime($GLOBALS['type_Y_m'])));
		//print_r($month);exit;
		$startdate=date("Y-m-01",strtotime("m -1 ",strtotime($GLOBALS['type_Y_m'])));
		$enddate=date("Y-m-t",strtotime("m -1 ",strtotime($GLOBALS['type_Y_m'])));
		$last_showtime=date("Y-m",strtotime("-2 month",strtotime($GLOBALS['type_Y_m'])));
		$lastdate_s=date("Y-m-01",strtotime("-2 month ",strtotime($GLOBALS['type_Y_m'])));
		$lastdate_e=date("Y-m-d",strtotime($month) -3600*24);
		$this->assign("last_date",date("n月",strtotime($month)));
		$last_tdate=date("Y-m",strtotime("-2 month ",strtotime($GLOBALS['type_Y_m'])));
		//print_r($last_tdate);
		$this->assign("last_tdate",date("n月",strtotime($last_tdate)));
		
	}
	$last_2date=date("Y-m",strtotime("+2 month ",strtotime($last_tdate)));
	$last_3date=date("Y-m",strtotime("+3 month ",strtotime($last_tdate)));
	$lastdate_2e=date("Y-m-t",strtotime($month));
	$lastdate_2s=date("Y-m-01",strtotime($month));
	//$lastdate_3e=date("Y-m-t",strtotime($lastdate_e));
	//$lastdate_3s=date("Y-m-01",strtotime($lastdate_s));
	//print_r($lastdate_3e);
	$sql_2="SELECT DataMark,dta_ym,Value,dta_vartype FROM `ng_data_table` WHERE 1 and ( (`DataMark` in ('Ngzb0021','Ngzb0022','Ngzb0018','Ngzb0019') and dta_vartype =1 and (`dta_ym`<='".$lastdate_2e."' and `dta_ym`>='".$lastdate_2s."')) or ( `DataMark` in ('Ngzb0013','Ngzb0015') and dta_vartype =2 and `dta_ym`='".$month."' )) ";
	$res_2arr=$this->ngdao->query($sql_2);
		//echo "<pre>";
		//print_r($sql_2);
		$temp_jhl=array();
		$temp_xsl=array();
		foreach ($res_2arr as $val){
			
			if($val['dta_vartype']=='1'){
				$temp_xsl[$val['DataMark']][$val['dta_ym']]=$val['Value'];
			}else if($val['dta_vartype']=='2'){
				$temp_jhl[$val['DataMark']]=$val['Value'];
			}
			
		}
		//print_r($temp_xsl);
		foreach($temp_xsl as $k=>$v){
			ksort($v);
			$temp_xsl[$k]=$v;
			$temp_xsl[$k]['end']=end($v);
			//print_r(end($v));exit;
			if($k=='Ngzb0021'){
				$temp_xsl[$k]['ddl']=$temp_jhl['Ngzb0013']==0?0:round(end($v)/$temp_jhl['Ngzb0013']*100,2);
			}
			if($k=='Ngzb0022'){
				$temp_xsl[$k]['ddl']=$temp_jhl['Ngzb0015']==0?0:round(end($v)/$temp_jhl['Ngzb0015']*100,2);
			}
			if($temp_xsl[$k]['ddl']==0){
				$temp_xsl[$k]['ddl']='';
			}else{
				$temp_xsl[$k]['ddl']=$temp_xsl[$k]['ddl']."%";
			}
			
		}
	
		$this->assign("temp_xsl",$temp_xsl);
		$this->assign("temp_jhl",$temp_jhl);


		/*$temp_2arr=array();
		$temp_xsl=array();
		foreach ($res_2arr as $val){
			$temp_2arr[$val['dta_ym'].$val['DataMark']]=$val['Value'];
			if($val['DataMark']=='Ngzb0016'){
				$temp_xsl[$val['DataMark']][$val['dta_ym']]=$val['Value'];
			}
			if($val['DataMark']=='Ngzb0014'){
				$temp_xsl[$val['DataMark']][$val['dta_ym']]=$val['Value'];
			}
		}
		//echo "<pre>";
		//print_r($temp_xsl);
		//print_r($temp_xsl['Ngzb0014']);
		//$temp_xsl['Ngzb0014']=arsort($temp_xsl['Ngzb0014']);
		//$temp_xsl['Ngzb0016']=arsort($temp_xsl['Ngzb0016']);
		ksort($temp_xsl['Ngzb0016']);
		ksort($temp_xsl['Ngzb0014']);
		//print_r($temp_xsl['Ngzb0014']);
		//累计值
		//print_r($temp_2arr);
			$key2=$lastdate_2e."Ngzb0014";
			//print_r($key);
			//$xsl2=$temp_2arr[$key2];	//本月
			$xsl2=end($temp_xsl['Ngzb0014']);	
		
			$key_2last=$lastdate_3e."Ngzb0016";//上月同期
			//$xsl_2last=$temp_2arr[$key_2last];
			$xsl_2last=end($temp_xsl['Ngzb0016']);
		//exit;
		//print_r($xsl_2last);
	
		$jhl2=$temp_2arr[$month.Ngzb0013];
		$jhl_2last=$temp_2arr[$month.Ngzb0015];
		
		$this->assign("temp_2arr",$temp_2arr);
		$ddl2=round($xsl2/$jhl2*100,2);
		//print_r($ddl);
		$last_2ddl=round($xsl_2last/$jhl_2last*100,2);
		
		
		$this->assign("ddl2",$ddl2);
		$this->assign("last_2ddl",$last_2ddl);
		//print_r($d);
		
		
		$this->assign("res_2jhl",$jhl2);
		$this->assign("res_2xsl",$xsl2);
		$this->assign("last_2jhl",$jhl_2last);
		$this->assign("last_2xslr",$xsl_2last);*/





	//print_r($sql_2);
	//hlf start 2018/6/14*/
	$this->assign("last_2date",date("n月",strtotime($last_2date)));
	$this->assign("last_3date",date("n月",strtotime($last_3date)));

	//hlf end 2018/6/14
		$sql="SELECT DataMark,dta_ym,Value,dta_vartype FROM `ng_data_table` WHERE 1 and ( (`DataMark` in ('Ngzb0020','Ngzb0003','Ngzb0017') and dta_vartype =1 and ((`dta_ym`<='".$lastdate_e."' and `dta_ym`>='".$lastdate_s."') or(`dta_ym`<='".$enddate."' and `dta_ym`>='".$startdate."') )) or ( `DataMark` in ('Ngzb0020','Ngzb0003') and dta_vartype =2 and (`dta_ym`='".$last_showtime."' or`dta_ym`='".$month."') )) ";
		//print_r($sql);
		$res_arr=$this->ngdao->query($sql);
		$t_arr=array();
		$temp_arr=array();
		foreach ($res_arr as $val){
			$t_arr[$val['dta_ym'].$val['DataMark']]=$val['Value'];
			if($val['dta_ym']==$month || $val['dta_ym']>=$startdate){
				$temp_arr[1][]=$val;//本月
			}else if($val['dta_ym']==$last_showtime || $val['dta_ym']>=$lastdate_s && $val['dta_ym']<=$lastdate_e){
				$temp_arr[0][]=$val;//上月
			}

		}
		$temp_1jhl=array();//本月与上月计划量
		$temp_1xsl=array();//本月与上月销售量
		foreach ($temp_arr as $key=>$val){
			foreach ($val as $k=>$v){
				if($v['dta_vartype']=='1'){
					$temp_1xsl[$key][$v['DataMark']][$v['dta_ym']]=$v['Value'];
				}else if($v['dta_vartype']=='2'){
					$temp_1jhl[$key][$v['DataMark']]=$v['Value'];
				}
			}
			
		}
		
		foreach($temp_1xsl as $k=>$v){
			foreach($v as $key=>$val){
				ksort($val);
				$temp_1xsl[$k][$key]=$val;
				$temp_1xsl[$k][$key]['end']=end($val);
				if($key=='Ngzb0020'){
					$temp_1xsl[$k][$key]['ddl']=round(end($val)/$temp_1jhl[$k]['Ngzb0003']*100,2);
				}
				if($temp_1xsl[$k][$key]['ddl']==0){
					$temp_1xsl[$k][$key]['ddl']='';
				}else{
					$temp_1xsl[$k][$key]['ddl']=$temp_1xsl[$k][$key]['ddl']."%";
				}
			}
			
		}
		//echo "<pre>";
		//print_r($temp_1xsl);
		$this->assign("temp_1xsl",$temp_1xsl);
		$this->assign("temp_1jhl",$temp_1jhl);
		//累计值
		
		/*	$key=$enddate."Ngzb0004";
			//print_r($key);
			$xsl=$temp_arr[$key];	//本月
		
			$key_last=$lastdate_e."Ngzb0004";//上月同期
			$xsl_last=$temp_arr[$key_last];
		//exit;
		//print_r($last_day);exit;
		
		$jhl=$temp_arr[$month.Ngzb0003];
		$jhl_last=$temp_arr[$last_showtime.Ngzb0003];
		
		$this->assign("temp_arr",$temp_arr);
		
		$ddl=round($xsl/$jhl*100,2);
		//print_r($ddl);
		$last_ddl=round($xsl_last/$jhl_last*100,2);
		$zf_jhl=round($jhl/$jhl_last*100-100,2);
		$zf_xsl=round($xsl/$xsl_last*100-100,2);
		
		$this->assign("ddl",$ddl);
		$this->assign("last_ddl",$last_ddl);
		$this->assign("zf_jhl",$zf_jhl);
		$this->assign("zf_xsl",$zf_xsl);
		//print_r($d);
		
		
		
		$this->assign("res_jhl",$jhl);
		$this->assign("res_xsl",$xsl);
		$this->assign("last_jhl",$jhl_last);
		$this->assign("last_xslr",$xsl_last);*/
		//print_r( $temp_arr);
		$this->assign("date",$d);
		return $t_arr;
	}
	public function bxunshijg($date1,$date2,$da_arr,$lastM){
		//print_r($date2);
		//$date1='2018-04-20';
		//$date2='2018-04-30';
		$new_day=date("Y-m-d",strtotime("-1 month ",strtotime($date2)));
		//print_r($new_day);
		$top_arr=array('073012','083012','093012','883012','123012','133012','653012','113012');
		$topicturestr="('".implode("','",$top_arr)."')";
		$sql="select * from marketconditions where topicture in ".$topicturestr."and mconmanagedate > '".$new_day."' and mconmanagedate < '".$date2."'";
		//print_r($sql);
		$mstopid=array('1230120');
		$mstopid_str="('".implode("','",$mstopid)."')";
		$sql2="select * from marketconditions where mastertopid in ".$mstopid_str." and mconmanagedate > '".$new_day."' and mconmanagedate < '".$date2."'";
		$all_zhb=$this->maindao->query($sql);
		$all_zhb_mid=$this->maindao->query($sql2);
		//print_r($sql2);
		$res_zhb=array();
		$res_zhb_one=array();
		$res_zhb_two=array();
		$res_zhb_tre=array();
		$tmp_d1=date("j",strtotime($date1));//前前旬时间
		//print_r($tmp_d1);
		if($tmp_d1<='10'){
			$date_1=date("Y-m-01 00:00:00",strtotime($date1));
			$date_2=date("Y-m-21 00:00:00",strtotime("-1 month ",strtotime($date_1)));
			$title_time_1=date("n",strtotime($date1))."月中旬";
			$title_time_2=date("n",strtotime($date_1))."月上旬";
			$title_time_3=date("n",strtotime($date_2))."月下旬";
			$s_Y=date("Y年",strtotime($date_2));
			$e_Y=date("Y年",strtotime($date2));
		}else if($tmp_d1>'20'){
			$date_1=date("Y-m-21 00:00:00",strtotime($date1));
			$date_2=date("Y-m-11 00:00:00",strtotime($date_1));
			$title_time_1=date("n",strtotime($date2))."月上旬";
			$title_time_2=date("n",strtotime($date_1))."月下旬";
			$title_time_3=date("n",strtotime($date_2))."月中旬";
			$s_Y=date("Y年",strtotime($date_2));
			$e_Y=date("Y年",strtotime($date2));
		}else if($tmp_d1<='20' && $tmp_d1>='11'){
			$date_1=date("Y-m-11 00:00:00",strtotime($date1));
			$date_2=date("Y-m-01 00:00:00",strtotime($date_1));
			$title_time_1=date("n",strtotime($date1))."月下旬";
			$title_time_2=date("n",strtotime($date_1))."月中旬";
			$title_time_3=date("n",strtotime($date_2))."月上旬";
			$s_Y=date("Y年",strtotime($date_2));
			$e_Y=date("Y年",strtotime($date2));
		}
		$res_zhb_gc=array();
		foreach($all_zhb as $temp){
			
				$res_zhb_gc[$temp['topicture']]=$temp['factoryarea'];
				if($temp['mconmanagedate']>$date1){
					$res_zhb[]=$temp;
					$res_zhb_one[$temp['topicture']][]=$temp['pricemk'];
				}
				if($temp['mconmanagedate']>$date_1 && $temp['mconmanagedate'] <= $date1){
					$res_zhb_two[$temp['topicture']][]=$temp['pricemk'];
				}
				if($temp['mconmanagedate']>$date_2 && $temp['mconmanagedate'] < $date_1){
					$res_zhb_tre[$temp['topicture']][]=$temp['pricemk'];
				}
			
		}
		foreach($all_zhb_mid as $temp){
			
				$res_zhb_gc[$temp['mastertopid']]=$temp['factoryarea'];
				if($temp['mconmanagedate']>$date1){
					$res_zhb[]=$temp;
					$res_zhb_one[$temp['mastertopid']][]=$temp['pricemk'];
				}
				if($temp['mconmanagedate']>$date_1 && $temp['mconmanagedate'] <= $date1){
					$res_zhb_two[$temp['mastertopid']][]=$temp['pricemk'];
				}
				if($temp['mconmanagedate']>$date_2 && $temp['mconmanagedate'] < $date_1){
					$res_zhb_tre[$temp['mastertopid']][]=$temp['pricemk'];
				}
			
		
		}
		$this->assign("res_zhb_gc",$res_zhb_gc);
		//print_r($res_zhb_gc);
		$all_avg=array();
		foreach($res_zhb_one as $key=>$val){
			
			if(in_array($key,$top_arr)){
				$avg_one[$key]=round(array_sum($val)/count($val));
			}
			if(in_array($key,$mstopid)){
				$avg_one[$key]=round(array_sum($val)/count($val));
			}
			
		}
		foreach($res_zhb_two as $key=>$val){
			
			if(in_array($key,$top_arr)){
				$avg_two[$key]=round(array_sum($val)/count($val));
			}
			if(in_array($key,$mstopid)){
				$avg_two[$key]=round(array_sum($val)/count($val));
			}
		}
		foreach($res_zhb_tre as $key=>$val){
			
			if(in_array($key,$top_arr)){
				$avg_tre[$key]=round(array_sum($val)/count($val));
			}
			if(in_array($key,$mstopid)){
				$avg_tre[$key]=round(array_sum($val)/count($val));
			}
			
		}
		$sum1=0;
		$sum2=0;
		$sum3=0;
		$avg_id=array('073012','083012','093012','883012','133012','113012','1230120');
		foreach($avg_one as $key=>$v){
			if(in_array($key,$avg_id)){
				$sum1+=$v;
			}
		}
		foreach($avg_two as $key=>$v){
			if(in_array($key,$avg_id)){
				$sum2+=$v;
			}
		}
		foreach($avg_tre as $key=>$v){
			if(in_array($key,$avg_id)){
				$sum3+=$v;
			}
		}
		//print_r($sum2);
		//print_r($sum1);
		$all_avg[0]=round($sum3/7);
		$all_avg[1]=round($sum2/7);
		
		$all_avg[2]=round($sum1/7);
		//print_r($all_avg);
		$this->assign("all_avg",$all_avg);
		$this->assign("avg_one",$avg_one);
		$this->assign("avg_two",$avg_two);
		$this->assign("avg_tre",$avg_tre);
		$this->assign("title_time_1",$title_time_1);
		$this->assign("title_time_2",$title_time_2);
		$this->assign("title_time_3",$title_time_3);
		$this->assign("s_Y",$s_Y);
		$this->assign("e_Y",$e_Y);
		//$avg_one=array_sum($res_zhb_one)/count($res_zhb_one);
		//print_r($avg_one);
		$res_data = array();
		
		foreach($res_zhb as $tmp){
			
			$day = date("j",strtotime($tmp['mconmanagedate']));
			//print_r($day);
			foreach($da_arr as $date){
				$d=$lastM."-".$date;
				//$date = date("j",strtotime($d));
			//	print_r($day);
				if($date==$day){
					$res_data[$tmp['topicture']][$day]=  $tmp['pricemk'];
					//print_r($res_data[$tmp['topicture']][$day]);
					//echo "<pre>";
				}
				
			}
			
			
		}
		foreach($top_arr as $topicture){
			foreach($da_arr as $date){
				if($res_data[$topicture][$date]=="") {
					$res_data[$topicture][$date]="-";
				}
			}
		}
		foreach($res_data as $key=>&$tmp){
			//print_r($tmp);
			$tmp['zd'] = (int)reset($tmp) - (int)end($tmp);
			if($tmp['zd']!=0){
				$tmp['zd']=-$tmp['zd'] ;
				$tmp['zd']=$this->zd_color1($tmp['zd']);
			}
		}
		
		
		$this->assign("res_data",$res_data);
		return $res_data;
	}
	function zhangdie($int){
	  //update by shizg started 2018/05/15
		$intstr = "";
		if($int<0){
			$intstr = "<font style='color:green;'>↓".abs($int)."</font>";
		}elseif($int>0){
			$intstr = "<font style='color:red;'>↑".abs($int)."</font>";
		}elseif($int==0){
			$intstr = "<font >".$int."</font>";
		}elseif($int==""){
			$intstr = "--";
		}
		return $intstr;
		
  }
	//钢厂调价汇总
	public function gctj_hz($start_date,$end_date){

		$steel_id = "('1335','205','120','301','133','134','361','348','101','401')";
      $steel_id_arr = array('1335','205','120','301','133','134','361','348','101','401');
      $onlyid = "('9f49810bacda42ae80e79d9a76f35545','d47f8d59a54c53ecdfff5fc4cff48317','f85aa06ced69478aceec31111afaf986','e8aa1d1ca0b7f3e06f10b66b348777cf','0b37f732caa6698f383c3dc7d8507326','4fdf367092744647047f4e831919005f','e67527a524cd38e69fec59e74c269e88','f85aa06ced69478aceec31111afaf986')";
		 $sql = "select steel_id, steel_name, variety, specification, material, changerate_tax, the_price_tax, run_date, onlyid from steelhome_gc.steelprice_base join steelhome_gc.steelprice_info where steelprice_base.id = steelprice_info.sb_id and run_date >= '".$start_date."' and run_date <= '".$end_date."' and steelprice_base.is_show = 2 and steelprice_info.is_show = 2 and changerate_tax!='' and the_price_tax!='' and  steelprice_info.onlyid in $onlyid and steel_id in $steel_id order by steel_id";
    $result = $this->gcdao->query($sql);
	//echo "<pre>";
	
    foreach ($result as $key => $value) {
			$arr[$value['steel_id']][$value['run_date']] = $value;
			$arr[$value['steel_id']][$value['run_date']]['run_date'] = date("Y-m-d",strtotime($value['run_date']));
			//$arr[$value['steel_id']][$value['run_date']]['changerate_tax'] = $this->zhangdie($value['changerate_tax']);
    }
	$changerate_tax=array();
	$zhbtj=array();
	for($i=0;$i<count($steel_id_arr);$i++){
		$tmp_rundate=$start_date;
		foreach($arr[$steel_id_arr[$i]] as $key=>$value){
			$changerate_tax[$steel_id_arr[$i]][]=$value['changerate_tax'];
			if($tmp_rundate<=$value['run_date']){
				$tmp_rundate=$value['run_date'];
				$zhbtj[$steel_id_arr[$i]] = $value;
				$zhbtj[$steel_id_arr[$i]]['run_date'] = date("Y-m-d",strtotime($value['run_date']));
				//$zhbtj[$steel_id_arr[$i]]['changerate_tax'] = $this->zhangdie($value['changerate_tax']);
			}
		}
	}
	//echo "<pre>";
	//print_r( $changerate_tax);
	for($i=0;$i<count($steel_id_arr);$i++){
		if($zhbtj[$steel_id_arr[$i]] != ""){
			$zhbtj[$steel_id_arr[$i]]['changerate_tax'] = $this->zhangdie(array_sum($changerate_tax[$steel_id_arr[$i]]));
		}
	 }
	// echo "<pre>";
	//print_r($zhbtj);
    for($i=0;$i<count($steel_id_arr);$i++){
      if($zhbtj[$steel_id_arr[$i]] == ""){
        $sql = "select steel_id, steel_name, variety, specification, material, the_price_tax, run_date, onlyid from steelhome_gc.steelprice_base join steelhome_gc.steelprice_info where steelprice_base.id = steelprice_info.sb_id  and steelprice_base.is_show = 2 and steelprice_info.is_show = 2 and steelprice_info.onlyid in $onlyid and steel_id='".$steel_id_arr[$i]."' and run_date< '".$start_date."' and changerate_tax!='' and the_price_tax!='' order by steelprice_info.id desc limit 1";
		//print_r($sql);
        $result =$this->gcdao->query($sql);
        $zhbtj[$steel_id_arr[$i]] = $result[0];
        $zhbtj[$steel_id_arr[$i]]['run_date'] = date("Y-m-d",strtotime($result[0]['run_date']));
        $zhbtj[$steel_id_arr[$i]]['changerate_tax'] = "--";
             // 
      }
    }
	//echo "<pre>";
	//print_r( $zhbtj);	//$zhbtj=$arr;
		$zhbtj_table = "<table border=1 cellspacing=0 cellpadding=0 style='text-align:center'>
                        <tr>
                          <td>钢厂</td>
                          <td>品种</td>
                          <td>规格</td>
                          <td>材质</td>
                          <td>调幅<br/>（含税）</td>
                          <td>执行价格<br/>（含税）</td>
                          <td>执行日期</td>
                        </tr>
						<tr>
                          <td>宝钢</td>
                          <td>".$zhbtj['101']['variety']."</td>
                          <td>16-50mm*3000</td>
                          <td>Q235</td>
                          <td>".$zhbtj['101']['changerate_tax']."</td>
                          <td>".$zhbtj['101']['the_price_tax']."</td>
                          <td>".$zhbtj['101']['run_date']."</td>
                        </tr>
                        <tr>
                          <td>鞍钢集团</td>
                          <td>".$zhbtj['401']['variety']."</td>
                          <td>20mm*2000</td>
                          <td>Q235B</td>
                          <td>".$zhbtj['401']['changerate_tax']."</td>
                          <td>".$zhbtj['401']['the_price_tax']."</td>
                          <td>".$zhbtj['401']['run_date']."</td>
                        </tr>
                        <tr>
                          <td>首秦</td>
                          <td>".$zhbtj['301']['variety']."</td>
                          <td>20mm*2500</td>
                          <td>Q235B</td>
                          <td>".$zhbtj['301']['changerate_tax']."</td>
                          <td>".$zhbtj['301']['the_price_tax']."</td>
                          <td>".$zhbtj['301']['run_date']."</td>
                        </tr>
                        <tr>
                          <td>山钢集团</td>
                          <td>".$zhbtj['1335']['variety']."</td>
                          <td>16-40mm</td>
                          <td>Q235B</td>
                          <td>".$zhbtj['1335']['changerate_tax']."</td>
                          <td>".$zhbtj['1335']['the_price_tax']."</td>
                          <td>".$zhbtj['1335']['run_date']."</td>
                        </tr>
                        <tr>
                          <td>湘钢</td>
                          <td>".$zhbtj['205']['variety']."</td>
                          <td>14-28mm</td>
                          <td>Q235B</td>
                          <td>".$zhbtj['205']['changerate_tax']."</td>
                          <td>".$zhbtj['205']['the_price_tax']."</td>
                          <td>".$zhbtj['205']['run_date']."</td>
                        </tr>
                       
                        <tr>
                          <td>沙钢</td>
                          <td>".$zhbtj['120']['variety']."</td>
                          <td>20mm*2000</td>
                          <td>Q235</td>
                          <td>".$zhbtj['120']['changerate_tax']."</td>
                          <td>".$zhbtj['120']['the_price_tax']."</td>
                          <td>".$zhbtj['120']['run_date']."</td>
                        </tr>
						
						 <tr>
                          <td>新钢</td>
                          <td>".$zhbtj['133']['variety']."</td>
                          <td>20mm*2000</td>
                          <td>Q235B</td>
                          <td>".$zhbtj['133']['changerate_tax']."</td>
                          <td>".$zhbtj['133']['the_price_tax']."</td>
                          <td>".$zhbtj['133']['run_date']."</td>
                        </tr>
						 
						 <tr>
                          <td>河北文丰</td>
                          <td>".$zhbtj['361']['variety']."</td>
                          <td>20mm*2000</td>
                          <td>Q235B</td>
                          <td>".$zhbtj['361']['changerate_tax']."</td>
                          <td>".$zhbtj['361']['the_price_tax']."</td>
                          <td>".$zhbtj['361']['run_date']."</td>
                        </tr>
						 <tr>
                          <td>普阳钢铁</td>
                          <td>".$zhbtj['348']['variety']."</td>
                          <td>20mm*2200</td>
                          <td>Q235B</td>
                          <td>".$zhbtj['348']['changerate_tax']."</td>
                          <td>".$zhbtj['348']['the_price_tax']."</td>
                          <td>".$zhbtj['348']['run_date']."</td>
                        </tr>
                      </table>";





		/*$s_table='<table>';  
        $s_th="<tr>
        <td>&nbsp;</td>
		<td align='center'>钢厂</td>
		<td align='center'>品种</td>
		<td align='center'>规格</td>
		<td align='center'>材质</td>
		<td align='center'>调幅(含税)</td>
		<td align='center'>执行价格(含税)</td>
		<td align='center'>执行日期</td>
		<td align='center'>备注</td>
        </tr>";
	//	print_r($start_date);
		//print_r($end_date);*/

		//$arr_area=array("华东地区","华北地区","东北地区","中南地区","西北地区","西南地区");
		// foreach($arr_area as $k=>$v){ 
			// $sql="SELECT `st_code` FROM  `sc_steelcom`  where area='$v'  and st_code!='' order by a_od "; 
			// $arr=$this->gcdao->getones($sql);
			// $str_steelid = implode(",", $arr);
			 /*发布日期-执行日期>=5不汇总*/
           //   $where_date = " and timestampdiff(DAY,`run_date`,`post_date`)<5 ";
			//  $sql = "SELECT graph_show,variety ,sum(changerate_tax) as changerate_tax,the_price_tax,specification,material,message ,steel_id,steel_name,max(run_date) as run_date FROM (SELECT si.* ,steel_name,steel_id,run_date FROM steelprice_base as sb , steelprice_info as si  WHERE sb.id=si.sb_id and  si.is_show=2 and `post_date` >='".$start_date."' and `post_date` <='".$end_date."'  and  steel_id in ($str_steelid) and si.variety='".中厚板."' ".$where_date." and changerate_tax!='' and changerate!=''  order by run_date desc ) as A group by steel_name,variety,specification,material,quyu,message  order by steel_name asc,variety asc ,run_date asc ,material asc ,specification asc ";
			 //echo "<br>".$sql;
			//$array=$this->gcdao->query($sql);
			//echo "<pre>";  print_r($array); echo "</pre>";
			//$s_tr.=$this->get_gtr($array,$v);

		// }
		//$s_table=$s_table.$s_th.$s_tr."</table>";
		$this->assign("s_table", $zhbtj_table);

	}
	public function get_gtr($array,$area){
		 $s_tr="";
		 $j = 0;
		 $rowspan = 0;
		for($k=0; $k<count($array);$k++){
    		if($array[$k]['graph_show']=="2"){
    			$rowspan++;
    		}
		}
		$s_td_0="<td rowspan=".$rowspan." align='left' >".$area." </td>";
		for($i=0; $i<count($array);$i++){
    	if($array[$i]['graph_show']=="2"){
    		$run_date=date("Y-m-d",strtotime($array[$i]['run_date']));
	      $changerate_tax=$array[$i]['changerate_tax'];
	      $the_price_tax=$array[$i]['the_price_tax'];
	      $run_price_show=$array[$i]['run_price_show'];
	      $change_show=$array[$i]['change_show'];
	      $message=$array[$i]['message'];

	      if($changerate_tax>0 && substr_count($changerate_tax, "+") == 0){
	        $changerate_tax="+".$changerate_tax;
	      }

	      if($run_price_show==2){
	        $the_price_tax="";
	      }
	      if($change_show==2){
	        $changerate_tax="";
	      }
			 $changerate_tax=$this->zd_color($changerate_tax);
	      if($j!=0){
	      	$s_td_0="";
	      }
	      $steel_id=$array[$i]['steel_id'];
	      $steel_name=$array[$i]['steel_name'];

				if(substr_count($steel_name, "攀钢") == 1){
					$steel_id="501";
				}
				if(substr_count($steel_name, "达钢") == 1){
					$steel_id="506";
				}
				if(substr_count($steel_name, "水钢") == 1){
					$steel_id="507";
				}
				if(substr_count($steel_name, "德胜") == 1){
					$steel_id="508";
				}
				if(substr_count($steel_name, "酒钢") == 1){
	        $steel_id="602";
	      }
	      $s_tr.="<tr>".$s_td_0."
	      <td >".$steel_name."</td>
	      <td>".$array[$i]['variety']."</td>
	      <td>".$array[$i]['specification']."</td>
	      <td>".$array[$i]['material']."</td>
	      <td>".$changerate_tax."</td>
	      <td>".$the_price_tax."</td>
	      <td>".$run_date."</td>
	      <td>".$message."</td>
	      </tr>";
	      $j++;
    	}
    }

	return $s_tr;


	}
	public function gzjyuc($e_day,$s_day){
		$sql="SELECT * FROM  `NgGZJForcast` WHERE  `Type` =8 and `CDate` <='".$e_day."' AND  `CDate` >'".$s_day."' order by CDate desc limit 1";
		$info=$this->maindao->getRow($sql);
		//print_r($sql);
		if($info){
			if(abs($info['ZhangDie'])<DELTA){
			$info['ZhangDie']="持平";
			}else if($info['ZhangDie']>DELTA){
				$info['ZhangDie']='上涨'.$info['ZhangDie'].'元/吨';
			}else{
				$info['ZhangDie']="下跌".abs($info['ZhangDie']).'元/吨';
			}
		}else{
			$info['ZhangDie']='无';
		}
		//print_r($info);
		$this->assign("info",$info);
	}
	public function djjy($start,$end,$ng_res,$res_date){
		$ng_sql="select dta_ym,Value from ng_data_table where DataMark='Ngzb0001' and dta_ym<='".$end."' and dta_ym>='$start' order by dta_ym desc limit 1";
		//print_r($ng_sql);
		$res_arr=$this->ngdao->query($ng_sql);
		$ng_price=$res_arr[0]['Value'];
		//print_r($res_date);
		$new_arr=$res_date[123012];
		$jqtmp=array_slice($new_arr,-2,1);
		
		if($jqtmp[0]=="-"){
			$g_price=array_sum($new_arr)/(count($new_arr)-2);
		}else{
			$g_price=(array_sum($new_arr)-$jqtmp[0])/(count($new_arr)-2);
		}
		
		//print_r($g_price);
		$d=date("d",strtotime($start));
		//print_r($d);
		$last_eday;
		$last_sday;
		if($d>20){
			$last_eday=date("Y-m-21",strtotime("m -1 ",strtotime($GLOBALS['type_Y_m'])));
			$last_sday=date("Y-m-11",strtotime("m -1 ",strtotime($GLOBALS['type_Y_m'])));
		}else if($d<=20 && $d>10){
			$last_sday=date("Y-m-01",strtotime($GLOBALS['type_Y_m']));
			$last_eday=date("Y-m-11",strtotime($GLOBALS['type_Y_m']));
		}else if($d<=10){
			$last_sday=date("Y-m-21",strtotime("m -1 ",strtotime($GLOBALS['type_Y_m'])));
			$last_eday=date("Y-m-01",strtotime($GLOBALS['type_Y_m']));
		}
		$sql="select * from marketconditions where topicture='123012' and mconmanagedate >= '".$last_sday."' and mconmanagedate <'".$last_eday."'";
		//print_r($sql);
		$res_zhb=$this->maindao->query($sql);
		$res_data=array();
		foreach($res_zhb as $tmp){
			$day = date("d",strtotime($tmp['mconmanagedate']));
			$res_data[$tmp['topicture']][$day] =  $tmp['pricemk'];
		}
		//print_r($res_data);
		$new_last_p=$res_data[123012];
		$last_g_prcie=array_sum($new_last_p)/count($new_last_p);//没问题
		//print_r($end);
		$sql="select pricemk as price,mconmanagedate from marketconditions where topicture='123012' and mconmanagedate <='".$end."' order by mconmanagedate desc limit 10";
		//print_r($sql);
		$next_tenprice=$this->maindao->query($sql);
		//print_r($next_tenprice);
		$temp_price=reset($next_tenprice);
		$end_price=$temp_price['price'];
		$temp_next=array();
		foreach($next_tenprice as $val){
			$temp_next[]=$val['price'];
			
		}
		$avg_ten=array_sum($temp_next)/count($temp_next);
		$sum_five=0;
		$temp=array();
		for($i=0;$i<5;$i++){
			//print_r($next_tenprice[$i]);
			$sum_five=$sum_five+$next_tenprice[$i]['price'];
		}
		$avg_five=$sum_five/5;
		$zd;
		$BB=$g_price-$last_g_prcie;
		
		//echo "<pre>";
		
		
		//print_r($workday);
		$ng_04price=end($ng_res);
		if(count($ng_res)==1){
			$ng_04price=0;
		}
		
		
		$ng_03price=$ng_res[$GLOBALS['type_Y_m'].'Ngzb0003'];
		//$ng_03price=$ng_res[date("Y-11").Ngzb0003];
		$cz=$ng_04price/$ng_03price*100;
		//print_r($cz);
		$d=date("j",strtotime($GLOBALS['type_date']));
		//print_r($d);
		/*print_r($ng_04price);
		echo "<pre>";
		print_r($cz);
		echo "<pre>";
		print_r($ng_03price);
		echo "<pre>";
		print_r($g_price);
		echo "<pre>";
		print_r($last_g_prcie);
		echo "<pre>";
		print_r($avg_five);
		echo "<pre>";
		print_r($avg_ten);
		echo "<pre>";*/
		//print_r($BB);
		//$d=1;
		if($d<11){
			if($end_price>$avg_five && $end_price>$avg_ten){
				
				$zd=$BB+20;
				
			}
			if($end_price<=$avg_five && $end_price>$avg_ten){
		
				$zd=$BB;
			}
			if($end_price<=$avg_ten && $end_price>$avg_five){
				//print_r(3333);
				
				$zd=$BB;
			}
			if($end_price<=$avg_five && $end_price<=$avg_ten){
				//print_r(4444);
				$zd=$BB-20;
			}
		}
		if($d<21 && $d>10){
			
			if($cz<30){
				$zd=$BB-20;
			}else if($cz<50 && $cz>=30){
				$zd=$BB;
			}else{
				
				$zd=$BB+20;
				//print_r($zd);
				
			}
			
		}
		if($d<=31 && $d>20){
			if($cz<60){
				$zd=$BB-50;
			}else if($cz<70 && $cz>=60){
				$zd=$BB-20;
			}else if($cz<80 && $cz>=70){
				$zd=$BB;
			}else{
				$zd=$BB+20;
			}
			
		}
		$zd=(int)($zd/10)*10;
		//print_r($zd);
		$moxdj=$ng_price+$zd;
		$this->assign("moxdj",$moxdj);
		$this->assign("zd",$zd);
		$dj_arr=array('moxdj'=>$moxdj,'zd'=>$zd);
		
		return $dj_arr;
	}
	public function workday($date){
		//echo $date="2017-10-07";
		 $sql="select date,isholiday from steelhome.holiday  where date <='".$date."' order by date desc limit 10"; 
		//$sql="select * from holiday  where date <='2017-10-7' order by date desc limit 20"; 
		 $res=$this->maindao->aquery($sql);
		 $i = 1;
		 $last_date = $date;
		 $date=array();
		 
		 while( $i < 8 ){
			$last_date=date("Y-m-d",strtotime("-1 day",strtotime($last_date))); 
			if( isset($res[$last_date]) ){
			
				if($res[$last_date]=="0"){
					
					$date[$i]=$last_date;
					$i++;
					//break;
				}else{
					//continue;
				}
			}else{
				
				$n=date("N",strtotime($last_date));
				if($n==7){
					//continue;
				}else{
					
					$date[$i]=$last_date;
					$i++;
					//break;
				}
				
			}
		 }
		 //	print_r($date);
		 return $date;
		
	   }
	   public function zd_color($zd){
		if($zd==0){
				 $zd = "<font color=black>".$zd."</font>";
			}else if($zd<0){
				$zd = "<font color=green>".$zd."</font>";
			}else{
				$zd = "<font color=red>".$zd."</font>";
			}
			 return $zd;
	   }
	    public function zd_color1($zd){
		if($zd==0){
				 $zd = "<font color=black>".$zd."</font>";
			}else if($zd<0){
				$zd = "<font color=green>".$zd."</font>";
			}else{
				$zd = "<font color=red>+".$zd."</font>";
			}
			 return $zd;
	   }
} 
?>