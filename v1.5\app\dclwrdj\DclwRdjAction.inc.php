<?php
class DclwRdjAction extends AbstractAction{
   public function __construct(){
    parent::__construct();    
  }
	public function index($params)
	{
		$flat="--";
		$curdate=$params['curdate'];
		if(empty($curdate)) $curdate=date("Y-m-d");
        //$type=$params["Type"];

		$markarr=array('Nglw0001','Nglw0002','Nglw0003','Nglw0004','Nglw0005','Nglw0006','Nglw0007'); //螺纹钢销售情况的数据标志
		$markstr=implode("','",$markarr);

		$workday=$this->getworkdays(date("Y-m-d",strtotime($curdate)));
		sort($workday);
		$lastworkday=$workday[count($workday)-1];
		//print"<pre>";print_r($workday);

		$sql="select * from steelhome_drc.ng_data_table where datamark in ('$markstr') and dta_ym>='$workday[0]' and dta_ym<='".$lastworkday."' order by dta_ym desc";

		//echo $sql."<br>";
		$arr=$this->_dao->query($sql);
		//print"<pre>";print_r($arr);//exit;
		$date1="";
		$date2="";//date1 表示最近一天，date2表示date1之前有数据的日期，用于后面计算涨跌
		foreach($arr as $k=>$v){
			if($date1=="") $date1=$v["dta_ym"];
			elseif($date2==""||$date2==$date1) $date2=$v["dta_ym"];
			if($v["DataMark"]=="Nglw0007"){
				$sales[$v["dta_ym"]][$v["DataMark"]]=$v["Value"];
			}else{
				if(count($sales)<=7) $sales[$v["dta_ym"]][$v["DataMark"]]=round($v["Value"],0);
			}
			if(count($sales)>=8) unset($sales[$v["dta_ym"]]); //当超过七个，要去掉
		}
		if(empty($arr)){$date1=date("Y-m-d",strtotime($curdate));$date2=date("Y-m-d",strtotime($curdate)-24*3600);}
		//print"<pre>";print_r($sales);
		
		$lwctjg=$sales[$date1]['Nglw0001'];
		$txl=$sales[$date1]['Nglw0007'];
		if($sales[$date1]['Nglw0007']){
			$txl=$txl>0.1?($txl/1000.0):$txl;
			$lwctjg=(int)($lwctjg*(1-$txl*3));
		}
		$lwctjg2=$sales[$date2]['Nglw0001'];
		$txl2=$sales[$date2]['Nglw0007'];
		if($sales[$date2]['Nglw0007']){
			$txl2=$txl2>0.1?($txl2/1000.0):$txl2;
			$lwctjg2=(int)($lwctjg2*(1-$txl2*3));
		}
		//echo $date2." ".$sales[$date2]['Nglw0001'];
		$lwctzd=$lwctjg-$lwctjg2;
		$plctjg=$sales[$date1]['Nglw0006'];
		$plctzd=$plctjg-$sales[$date2]['Nglw0006'];
		foreach($sales as $date=>$array){
			foreach($markarr as $mark){
				if($sales[$date][$mark]==="") $sales[$date][$mark]=$flat;
			}
		}
		//print"<pre>";print_r($sales);//exit;
		if(is_array($sales))
			krsort($sales);//必须写在这个赋值之后
		
		$lwarr=array('112023','122023','072023','0820235','232023','222023','362023');
		$plarr=array('111312','121312','071312','081312','231312','221312','361312');
		$idarr=array_merge($lwarr,$plarr);
		//print_r($idarr);exit;
		
		$nowday=date("Y-m-d",strtotime($curdate));
		$dataday=$this->homeDao->getone("select mconmanagedate from steelhome.marketconditions where (topicture in ('".implode("','",$idarr)."') or mastertopid='0820235') and mconmanagedate<'$nowday' order by mconmanagedate desc limit 1");
		$dataday=date("Y-m-d",strtotime($dataday));//echo $dataday;

		$sql="select topicture,mastertopid,pricemk as price,(pricemk-oldpricemk) as zd,factoryarea from steelhome.marketconditions where (topicture in ('".implode("','",$idarr)."') or mastertopid='0820235') and mconmanagedate>'".$dataday."' and mconmanagedate<'".date("Y-m-d",strtotime($dataday)+24*3600)."'";
		//echo $sql."<br>";
		$valarr=$this->homeDao->query($sql);
		
		$lwpj['price']=0;
		$lwpj['zd']=0;
		$plpj['price']=0;
		$plpj['zd']=0;
		foreach($valarr as $arr){
			if($arr["mastertopid"]=="0820235") $arr["topicture"]=$arr["mastertopid"];
			$idval[$arr["topicture"]]=$arr["price"];
			$zdval[$arr["topicture"]]=$arr["zd"];
			if(in_array($arr["topicture"],$lwarr)){
				$lwpj['price']+=$arr["price"];
				$lwpj['zd']+=$arr["zd"];
			}elseif(in_array($arr["topicture"],$plarr)){
				$plpj['price']+=$arr["price"];
				$plpj['zd']+=$arr["zd"];
			}
			$gcname[$arr["topicture"]]=$arr["factoryarea"];
		}

		$lwpj['price']=round($lwpj['price']*1.0/count($lwarr),0);
		$lwpj['zd']=round($lwpj['zd']*1.0/count($lwarr),0);
		$plpj['price']=round($plpj['price']*1.0/count($plarr),0);
		$plpj['zd']=round($plpj['zd']*1.0/count($plarr),0);

		if($lwpj['zd']>0) $lwpj['zd']="<font color='red'>+".$lwpj['zd']."</font>";
		elseif($lwpj['zd']<0) $lwpj['zd']="<font color='green'>".$lwpj['zd']."</font>";
		else $lwpj['zd']=$flat;

		if($plpj['zd']>0) $plpj['zd']="<font color='red'>+".$plpj['zd']."</font>";
		elseif($plpj['zd']<0) $plpj['zd']="<font color='green'>".$plpj['zd']."</font>";
		else $plpj['zd']=$flat;

		foreach($idarr as $id){
			if($zdval[$id]>0) $zdval[$id]="<font color='red'>+".$zdval[$id]."</font>";
			elseif($zdval[$id]<0) $zdval[$id]="<font color='green'>".$zdval[$id]."</font>";
			if($zdval[$id]=="") $zdval[$id]=0;
			if($idval[$id]==="") {
				$idval[$id]=$flat;
				$zdval[$id]=$flat;
			}
		}
		//print"<pre>";print_r($idval);print_r($zdval);//exit;

		$sql="(select variety,the_price_tax as price,(the_price_tax-last_price_tax) as zd,gcid,ftime from steelhome_gc.ct_price_info where onlyid ='25b67ebabb304fdb9298942ad2528388' and gcid = '120' and is_show=2 and ftime<'$curdate' order by ftime desc limit 1 ) union (select variety,the_price_tax as price,(the_price_tax-last_price_tax) as zd,gcid,ftime from steelhome_gc.ct_price_info where onlyid = '25b67ebabb304fdb9298942ad2528388' and gcid = '121'  and is_show=2 and ftime<'$curdate' order by ftime desc limit 1) union (select variety,the_price_tax as price,(the_price_tax-last_price_tax) as zd,gcid,ftime from steelhome_gc.ct_price_info where onlyid = 'ed7a47decadd30444e1f7e5890fb401a' and gcid = '120' and is_show=2 and ftime<'$curdate' order by ftime desc limit 1) union (select variety,the_price_tax as price,(the_price_tax-last_price_tax) as zd,gcid,ftime from steelhome_gc.ct_price_info where onlyid = 'ed7a47decadd30444e1f7e5890fb401a' and gcid = '121' and is_show=2 and ftime<'$curdate' order by ftime desc limit 1) union (select variety,the_price_tax as price,(the_price_tax-last_price_tax) as zd,gcid,ftime from steelhome_gc.ct_price_info where onlyid ='f2df3d644afaf14f630c33c5d5e408d7' and gcid = '113' and is_show=2 and ftime<'$curdate' order by ftime desc limit 1) union (select variety,the_price_tax as price,(the_price_tax-last_price_tax) as zd,gcid,ftime from steelhome_gc.ct_price_info where onlyid ='c2ec61f202b7fadff6789e7c4e65be3a' and gcid = '113' and is_show=2 and ftime<'$curdate' order by ftime desc limit 1) union (select variety,the_price_tax as price,(the_price_tax-last_price_tax) as zd,gcid,ftime from steelhome_gc.ct_price_info where onlyid ='25b67ebabb304fdb9298942ad2528388' and gcid = '1186' and is_show=2 and ftime<'$curdate' order by ftime desc limit 1) union (select variety,the_price_tax as price,(the_price_tax-last_price_tax) as zd,gcid,ftime from steelhome_gc.ct_price_info where onlyid ='ed7a47decadd30444e1f7e5890fb401a' and gcid = '1186' and is_show=2 and ftime<'$curdate' order by ftime desc limit 1)";
		$ctarr=$this->gcDao->query($sql);
		//print"<pre>";print_r($ctarr);//exit;

		$sql="(select variety,the_price_tax as price,(the_price_tax-last_price_tax) as zd,gcid,ftime from steelhome_gc.steelprice_info where onlyid = '0cebcfa531241c65ba87a4006bbf39da' and gcid = '1457' and is_show=2 and ftime<'$curdate' order by ftime desc limit 1) union (select variety,the_price_tax as price,(the_price_tax-last_price_tax) as zd,gcid,ftime from steelhome_gc.steelprice_info where onlyid = '222f77b1f844e3bdf53f09af6cd57433' and gcid = '1457' and is_show=2 and ftime<'$curdate' order by ftime desc limit 1)";
		$ctarr2=$this->gcDao->query($sql);

		$convert=array("螺纹钢"=>'lw',"盘螺"=>'pl');
		$gcidarr=array(120,121,113,1186,1457);
		foreach($ctarr as $arr){
			$arr["price"]=(int)$arr["price"];//这里如果不需要，可以注释掉
			$arr["zd"]=(int)$arr["zd"];
			$ctval[$arr["gcid"]][$convert[$arr["variety"]]]=$arr["price"];
			$ctzd[$arr["gcid"]][$convert[$arr["variety"]]]=$arr["zd"];
		}
		foreach($ctarr2 as $arr){
			$arr["price"]=(int)$arr["price"];//这里如果不需要，可以注释掉
			$arr["zd"]=(int)$arr["zd"];
			$ctval[$arr["gcid"]][$convert[$arr["variety"]]]=$arr["price"];
			$ctzd[$arr["gcid"]][$convert[$arr["variety"]]]=$arr["zd"];
		}

		foreach($gcidarr as $gcid){
			foreach($convert as $kk=>$vv){
				if($ctzd[$gcid][$vv]>0) $ctzd[$gcid][$vv]="<font color='red'>+".$ctzd[$gcid][$vv]."</font>";
				elseif($ctzd[$gcid][$vv]<0) $ctzd[$gcid][$vv]="<font color='green'>".$ctzd[$gcid][$vv]."</font>";
				if($ctzd[$gcid][$vv]=="") $ctzd[$gcid][$vv]=0;
				if($ctval[$gcid][$vv]==="") {
					$ctval[$gcid][$vv]=$flat;
					$ctzd[$gcid][$vv]=$flat;
				}
			}
		}
		//print"<pre>";print_r($ctval);print_r($ctzd);
		
		$qhcodearr=array('RB','HC','I','JM','J');
		$sql="select day,code,price from steelhome.kmjg_qhprice where code in ('".implode("','",$qhcodearr)."') and day<='".date("Y-m-d",strtotime($curdate))."' and time<'17:00:00' order by id desc limit 5";
		$qharr=$this->homeDao->query($sql);
		$qhdate=$qharr[0]["day"];
		$sql2=str_replace(date("Y-m-d",strtotime($curdate)),$qhdate,$sql);
		$sql2=str_replace("day<=","day<",$sql2);
		$sql2=str_replace("time<'17:00:00'","time>='17:00:00'",$sql2);//echo $sql."<br>".$sql2;
		$qharr2=$this->homeDao->query($sql2);
		$qhdate2=$qharr2[0]["day"];

		//print"<pre>";print_r($qharr);print_r($qharr2);
		foreach($qharr2 as $id=>$arr){
			$qharr2[$arr["code"]]=$arr["price"];
			unset($qharr2[$id]);
		}
		$qhzd=array();
		$qhzdf=array();
		foreach($qharr as $arr){
			if($arr["code"]=="I"||$arr["code"]=="JM"||$arr["code"]=="J") {
				$qhval[$arr["code"]]=sprintf("%.1f",$arr["price"]);
				$qhzd[$arr["code"]]=sprintf("%.1f",$qhval[$arr["code"]]-$qharr2[$arr["code"]]);
			}else{
				$qhval[$arr["code"]]=$arr["price"];
				$qhzd[$arr["code"]]=$qhval[$arr["code"]]-$qharr2[$arr["code"]];
			}
			$qhzdf[$arr["code"]]=round($qhzd[$arr["code"]]*100.0/$qharr2[$arr["code"]],2)."%";
		}

		$qhzdRB=$qhzd["RB"];
		foreach($qhcodearr as $code){
			if($qhzd[$code]>0){
				$qhzd[$code]="<font color='red'>+".$qhzd[$code]."</font>";
				$qhzdf[$code]="<font color='red'>+".$qhzdf[$code]."</font>";
			}elseif($qhzd[$code]<0){
				$qhzd[$code]="<font color='green'>".$qhzd[$code]."</font>";
				$qhzdf[$code]="<font color='green'>".$qhzdf[$code]."</font>";
			}
			if($qhzd[$code]==""){
				$qhzd[$code]=0;
				$qhzdf[$code]="0%";
			}
			if($qhval[$code]===""){
				$qhval[$code]=$flat;
				$qhzd[$code]=$flat;
			}
		}

		$d_dzarr=array(1,2,3,4,5,6,9);
		$sql="select date,type,value,updown from steelhome.dollar_dzprice where type in ('".implode("','",$d_dzarr)."') and date<'".date("Y-m-d",strtotime($curdate))."' order by id desc limit 7";
		$dzarr=$this->homeDao->query($sql);

		$dzzd=array();
		$dzzdf=array();
		foreach($dzarr as $arr){
			
			$time = strtotime($arr['date']) - 3600*24;
			$forworddate=date("Y-m-d",$time);
			$dsql="select value  from  steelhome.dollar_dzprice where  type='".$arr['type']."' and date<='".$forworddate."' order by id desc limit 1";
			$dres=$this->homeDao->getOne($dsql);//正式
			$down=$arr['value']-$dres;//this by wufan

		
			
			$dzval[$arr["type"]]=sprintf("%.2f",$arr["value"]);
			$dzzd[$arr["type"]]=sprintf("%.2f",$down);
			if(abs($dzzd[$arr["type"]])-0.01<0) {
				$dzzd[$arr["type"]]=0.00;
			}
			$zdf=$dzzd[$arr["type"]]*100.0/($dzval[$arr["type"]]-$dzzd[$arr["type"]]);
			if(abs($zdf)<0.01){
				$dzzdf[$arr["type"]]="0%";
			}else{
				$dzzdf[$arr["type"]]=round($zdf,2)."%";
			}
		}
		$dzdate=$dzarr[0]["date"];
		foreach($d_dzarr as $code){
			if($dzzd[$code]>0){
				$dzzd[$code]="<font color='red'>+".$dzzd[$code]."</font>";
				$dzzdf[$code]="<font color='red'>+".$dzzdf[$code]."</font>";
			}elseif($dzzd[$code]<0){
				$dzzd[$code]="<font color='green'>".$dzzd[$code]."</font>";
				$dzzdf[$code]="<font color='green'>".$dzzdf[$code]."</font>";
			}
			if($dzzd[$code]==""){
				$dzzd[$code]=0;
				$dzzdf[$code]="0%";
			}
			if($dzval[$code]==="") {
				$dzval[$code]=$flat;
				$dzzd[$code]=$flat;
			}
		}
		//print"<pre>";print_r($dzval);print_r($dzzd);

		$gzj_lwg_prediction=$this->homeDao->getRow("select Detail,ZhangDie from NgGZJForcast where Type=1 and CDate<'$curdate' order by id desc limit 1");

		$zhangdie =$gzj_lwg_prediction['ZhangDie'];

		if($gzj_lwg_prediction)
		{
			if(abs($zhangdie)<=DELTA) $zhangdie="持平";//浮点数为0
			elseif($zhangdie-DELTA>0) $zhangdie="上涨".$zhangdie."元/吨";
			else $zhangdie="下跌".abs($zhangdie)."元/吨";
		}else{
			$zhangdie="暂无";
		}
		
		$gzj_lwg_prediction['ZhangDie']=$zhangdie;


		//AAAA=Nglw0001+BB;BB计算方法如下
		$A=$lwctjg;
		$B=$ctval[120]["lw"];
		$RB=$qhzdRB;
		if($workday[count($workday)-1]!=date("Y-m-d",strtotime($curdate)-24*3600)){//如果是周日或者国庆长假这种没有螺纹钢期货涨跌的日期
			$rbsql="select pricemk from steelhome.marketconditions where topicture='678411' and pricemk!='' order by mconmanagedate desc limit 2";
			$rb=$this->homeDao->getRow($rbsql);
			$RB=$rb[0]['pricemk']-$rb[1]['pricemk'];
		}
		//echo "A=".$A.";B=".$B.";RB=".$RB."<br>Nglw0005:".$sales[$date1]["Nglw0005"]."<br>";
		if($sales[$date1]["Nglw0005"]>24500&&$sales[$date1]["Nglw0005"]<25500){//24500<Value（1, Nglw0005）<25500
			if($A-$B-50>0){
				$BB=(int)(($RB-($A-$B-50))/10+0.5)*10;
			}elseif($A-$B-30<0){
				$BB=(int)(($RB-($A-$B-30))/10+0.5)*10;
			}else{//30=<(A-B)=<50
				$BB=(int)($RB/10+0.5)*10;
			}
		}elseif($sales[$date1]["Nglw0005"]>=25500){//Value（1, Nglw0005）>=25500
			if($A-$B-50>0){
				if($RB-10>=0){
					$BB=(int)(($RB-($A-$B-50))*0.8/10)*10;
				}elseif($RB>0&&$RB<10){
					$BB=0-($A-$B-50);
				}elseif($RB<=0&&$RB+10>0){
					$BB=-10-($A-$B-50);
				}else{
					$BB=(int)(($RB-($A-$B-50))*1.2/10+0.5)*10;
				}
			}elseif($A-$B-30<0){
				if($RB-10>=0){
					$BB=(int)(($RB-($A-$B-30))*0.8/10)*10;
				}elseif($RB>0&&$RB<10){
					$BB=0-($A-$B-30);
				}elseif($RB<=0&&$RB+10>0){
					$BB=-10-($A-$B-30);
				}else{
					$BB=(int)(($RB-($A-$B-30))*1.2/10+0.5)*10;
				}
			}else{//30=<(A-B)=<50
				if($RB-10>=0){
					$BB=(int)($RB*0.8/10)*10;
				}elseif($RB>0&&$RB<10){
					$BB=0;
				}elseif($RB<=0&&$RB+10>0){
					$BB=-10;
				}else{
					$BB=(int)($RB*1.2/10+0.5)*10;
				}
			}
		}else{//Value（1, Nglw0005）=<24500
			
			if($A-$B-50>0){
				if($RB-10>=0){
					$BB=(int)(($RB-($A-$B-50))*1.2/10+0.5)*10;
				}elseif($RB>0&&$RB<10){
					$BB=10-($A-$B-50);
				}elseif($RB<=0&&$RB+10>0){
					$BB=0-($A-$B-50);
				}else{
					$BB=(int)(($RB-($A-$B-50))*0.8/10)*10;
				}
			}elseif($A-$B-30<0){
				if($RB-10>=0){
					$BB=(int)(($RB-($A-$B-30))*1.2/10+0.5)*10;
				}elseif($RB>0&&$RB<10){
					$BB=10-($A-$B-30);
				}elseif($RB<=0&&$RB+10>0){
					$BB=0-($A-$B-30);
				}else{
					$BB=(int)(($RB-($A-$B-30))*0.8/10)*10;
				}
			}else{//30=<(A-B)=<50
				if($RB-10>=0){
					$BB=(int)($RB*1.2/10+0.5)*10;
				}elseif($RB>0&&$RB<10){
					$BB=10;
				}elseif($RB<=0&&$RB+10>0){
					$BB=0;
				}else{
					$BB=(int)($RB*0.8/10)*10;
				}
			}
		}
	if($_GET["debug"]){echo "curdate=".$curdate."\t A=".$A."\t B=".$B."\t RB=".$RB."\t Nglw0005=".$sales[$date1]["Nglw0005"]."\t";}
		$BB=(int)($BB/10)*10;
		$AAAA=$sales[$date1]['Nglw0001']+$BB;

		//echo $BB."\t".$AAAA."\r\n";exit;
		//AAAA=Nglw0001+BB;
		if($_GET["debug"]){echo "AAAA=".$AAAA.";BB=".$BB."<br>";}


		$this->assign("type",$type);
		$this->assign("lastworkday",date("Y年n月j日",strtotime($date1)));
		$this->assign("nowday",date("Y年n月j日",strtotime($curdate)));
		$this->assign("dataday",date("Y年n月j日",strtotime($dataday)));
		$this->assign("zt",date("Y年n月j日",strtotime($dataday)));
		$this->assign("sales",$sales);
		$this->assign("idval",$idval);
		$this->assign("zdval",$zdval);
		$this->assign("gcname",$gcname);
		$this->assign("lwctjg",$lwctjg);
		$this->assign("lwctzd",$lwctzd<0?"<font color='green'>".$lwctzd."</font>":($lwctzd>0?"<font color='red'>+".$lwctzd."</font>":$lwctzd));
		$this->assign("plctjg",$plctjg);
		$this->assign("plctzd",$plctzd<0?"<font color='green'>".$plctzd."</font>":($plctzd>0?"<font color='red'>+".$plctzd."</font>":$plctzd));
		$this->assign("ctval",$ctval);
		$this->assign("ctzd",$ctzd);
		$this->assign("qhval",$qhval);
		$this->assign("qhzd",$qhzd);
		$this->assign("qhzdf",$qhzdf);
		$this->assign("dzval",$dzval);
		$this->assign("dzzd",$dzzd);
		$this->assign("dzzdf",$dzzdf);
		$this->assign("qhdate",date("Y年n月j日",strtotime($qhdate)));
		$this->assign("dzdate",date("Y年n月j日",strtotime($dzdate)));
		$this->assign("AAAA",$AAAA);
		$this->assign("BB",$BB);
		$this->assign("flat",$flat);
		$this->assign("gzj_prediction",$gzj_lwg_prediction);
		$this->assign("lwpj",$lwpj);
		$this->assign("plpj",$plpj);

		
		//页面打开直接处理，定时运行程序用window.onload无法触发事件
		$modelpirce_name="20mmHRB400螺纹钢价格";
		$type=3;
		$title=date("Y年n月j日",strtotime($curdate))."螺纹钢日定价";
		$modelprice=$AAAA;
		$modeloldprice=$sales[$date1]['Nglw0001'];
		$modelprice_updown=$BB;

		if($_GET["issave"]==1){
			$sql="insert into steelhome_drc.ng_price_model (createtime,date,modeltitle,modeltype,ismakepricy) values (NOW(),'".date("Y-m-d",strtotime($curdate))."','$title','$type','0')";
			$this->_dao->execute($sql);
			$rid=$this->_dao->insert_id();
			$sql="insert into steelhome_drc.ng_price_model_detail (modelid,modelprice,modelprice_updown,modeloldprice,modelprice_type,modelpirce_name,modelpirce_name_sort,Mid,uid,Ismakepricy_men,createtime,date) values ('$rid','$modelprice','$modelprice_updown','$modeloldprice','$type','$modelpirce_name','1','-1','-1','0',NOW(),'".date("Y-m-d",strtotime($curdate))."')";
			$this->_dao->execute($sql);
		}
		//end by zhangcun for 螺纹钢日定价 2017/12/6
	}

	public function savedata($params){
		$curdate=$params['curdate'];
		if(empty($curdate)) $curdate=date("Y-m-d");
		if(IsNgWorkDay($this->homeDao,date("Y-m-d",strtotime($curdate)))!="1") {echo "今天是休息日，不生成数据";exit;}
		$type=3;
		$url = DC_URL.DCURL."/dclwrdj.php?view=index&issave=1&curdate=".$curdate;
		//echo $url;exit;
		include '/usr/local/www/libs/phpQuery/phpQuery.php';
		phpQuery::newDocumentFile($url);
		$html=pq("body")->html();
		
		$s_huizong="<!--huizong start-->";
		$e_huizong="<!--huizong end-->";
		$spos=strpos($html,$s_huizong);
		$epos=strpos($html,$e_huizong);
		$huizong=substr($html,$spos+strlen($s_huizong),$epos-$spos);
		$huizong=str_replace("六、钢之家判断","1、钢之家判断",$huizong);
		$huizong=str_replace("七、今日价格调整建议","2、价格模型建议",$huizong);
		$huizong=htmlentities($huizong, ENT_QUOTES,"UTF-8");
		

		$html=str_replace($s_huizong,"",$html);
		$html=str_replace($e_huizong,"",$html);
		$modelcontent=htmlentities($html, ENT_QUOTES,"UTF-8");
		$sql="update `ng_price_model` set modelcontent='".$modelcontent."',HuiZong1='$huizong'  where modeltype='$type' and date>='".date("Y-m-d",strtotime($curdate))."' and date<'".date("Y-m-d",time()+3600*24)."' order by id desc limit 1";
		$this->_dao->execute($sql);
		echo "ok";exit;
	}

	function getworkdays($date,$day=7){
		$workday=array();
		$sql="select date,isholiday from steelhome.holiday where date<'$date' order by date desc limit ".((int)($day/7)*2+8); //echo $sql."<br>";//exit;
		$ho=$this->homeDao->aquery($sql);
		//echo "<pre>";print_r($ho);
		for($i=0;count($workday)<$day;$i++){
			$d=date("Y-m-d",strtotime($date)-($i+1)*3600*24);
			if($ho[$d]=="1"){
				continue;
			}
			if($ho[$d]==="0"){
				$workday[]=$d;
			}else{
				//echo "<br>".$d." ".$ho[$d]."<br>";
				if(date("N",strtotime($d))=="7"){
					continue;
				}else{
					$workday[]=$d;
				}
			}
		}
		//print"<pre>";print_r($workday);
		return $workday;
	}
}
?>