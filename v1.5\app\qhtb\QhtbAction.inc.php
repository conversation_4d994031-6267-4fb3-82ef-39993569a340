<?php 

$GLOBALS['type_date']=date("Y-m-d");
$GLOBALS['type_Y']=date("Y");
$GLOBALS['type_Y_m']=date("Y-m");
class QhtbAction extends AbstractAction
{
    public $stedao;

    public function __construct()
    {
        parent::__construct();
    } 
    public function xunigc($params)
    {
		
		$mc_type=$params['mc_type'];
		if(isset($params['curdate'])){
			$GLOBALS['type_date']=$params['curdate'];
			$GLOBALS['type_Y']=date("Y",strtotime($GLOBALS['type_date']));
			$GLOBALS['type_Y_m']=date("Y-m",strtotime($GLOBALS['type_date']));
		}
		//if($parsms['issave']!=1){
			$sql="select * from TaoliParameter where type='1' and mc_type='".$mc_type."' order by id desc limit 1";
			$abc_list=$this->drc->getRow($sql);
			//print_r($abc_list);

			if(!empty($abc_list)){
				$params['a']=$abc_list['v1'];
				$params['b']=$abc_list['v2'];
				$params['c']=$abc_list['v3'];
				$params['d']=$abc_list['v4'];
				$params['e']=$abc_list['v5'];

			}
			$blockid =array(
			1 => 'RB', //'439',  //螺纹钢
			2 => 'I', //'473',  // 铁矿石
			3 => 'JM', //'453',  // 煤焦
			4 => 'J', //'452',  // 焦碳
			5 => 'HC',
			);
			$block_array=array();
			$date=$this->workday_gzj($GLOBALS['type_date']);
			if($date=='1'){
				
				for($i=0;$i<10;$i++){
					$tmp_date=$this->workday_gzj($date);
					if($tmp_date=='1'){
						$date=date("Y-m-d",strtotime("-1 day",strtotime($date)));
					}else{
						break;
					}
					
				}
					
				//	echo "今天休息！";exit;
				
			}
			//else{
		// 		foreach( $blockid as $key => $bid ){
		// 			$tmp = $this->getqhstrbyurl_sina( $bid );
		// 			//print_r($tmp);
		// 			//echo "<pre>";
		// 			if($key=='1'){
		// 				$RB=$tmp[2];
		// 				$block_array['RB']=$tmp[2];
		// 			}
		// 			if($key=='2'){
		// 				$IR=$tmp[2];
		// 				$block_array['IR']=$tmp[2];
		// 			}
		// 			if($key=='3'){
		// 				$JM=$tmp[2];
		// 				$block_array['JM']=$tmp[2];
		// 			}
		// 			if($key=='4'){
		// 				$JT=$tmp[2];
		// 				$block_array['JT']=$tmp[2];
		// 			}
		// 			if($key=='5'){
		// 				$HC=$tmp[2];
		// 				$block_array['HC']=$tmp[2];
		// 			}
					
		// 	//	}
		// }
		$memcache = new Memcache; 
		$memcache->connect(MEMCACHE_SERVER, MEMCACHE_PORT); 
		$block_array=$memcache->get("timing_gctb_qh");
		$memcache->close();
		$style_url= dirname("//".$_SERVER['SERVER_NAME'].$_SERVER['PHP_SELF']);
		$this->assign( "style_url", $style_url );
		$this->assign("params",$params);
		$this->assign("abc_list",$abc_list);
		$this->assign("block_array",$block_array);
		
       } 
	   public function save_xunigc($params){
		 //  exit;
		  header('Content-Type:text/html;charset=utf8');
		  $this->drc->execute("INSERT INTO  `steelhome_drc`.`TaoliParameter` (`type` ,`v1` ,`v2` ,`v3` ,`v4` ,`v5` ,`v6`  ,`createtime`,`mc_type`)VALUES ('1',  '".$params['TKS']."',  '".$params['JiaoT']."',  '".$params['ST']."',  '".$params['GP']."',  '".$params['GZF']."',  '',   now(),'".$params['mc_type']."');");
			//print_r($params);
			$sql="select * from app_session_temp where  GUID ='".$params['GUID']."'  order by  LoginDate DESC  limit 1";
			$res=$this->_dao->getRow($sql);
			$Mid=$res['Mid'];
			$uid=$res['Uid'];
			 $str="&RB=".$params['RB']."&JM=".$params['JM']."&HC=".$params['HC']."&JT=".$params['JT']."&IR=".$params['IR']."&a=".$params['TKS']."&b=".$params['JiaoT']."&c=".$params['ST']."&d=".$params['GP']."&e=".$params['GZF']."&AAA=".$params['RBCBJT']."&XXX=".$params['PMLRJT']."&BBB=".$params['RBCBJM']."&YYY=".$params['PMLRJTJM']."&CCC=".$params['HCCBJT']."&ZZZ=".$params['HCPMLRJT']."&DDD=".$params['HCCBJM']."&SSS=".$params['HCPMLRJTJM']."";
			//print_r($sql);exit;
			$url = DC_URL.DCURL."/qihtb.php?view=xunigc&mode=".$params['mode']."&GUID=".$params['GUID']."&mc_type=".$params['mc_type']."&issave=1&curdate=".$GLOBALS['type_date']."".$str;
			//print_r($url);
			include '/usr/local/www/libs/phpQuery/phpQuery.php';
			phpQuery::newDocumentFile($url);
			$html=pq("html")->html();
			//print_r($url);exit;
			$modelcontent=htmlentities($html, ENT_QUOTES,"UTF-8");
			$insql="INSERT INTO  `steelhome_drc`.`TaoliModel` (`type` ,`modelcontent` ,`mid` ,`uid` ,`createtime` ,`date`,`mc_type`)VALUES ( '1',  '$modelcontent',  '$Mid',  '$uid',  now(),  '".$GLOBALS['type_date']."','".$params['mc_type']."');";
			$this->drc->execute($insql);
			
			//print_r(DC_URL.DCURL."/qihtb.php?view=xunigc&GUID='".$params['GUID']."'".$str);
			//exit;
			//$str=iconv('UTF-8', 'GB2312','保存成功');
			//print_r($url);
			//print_r($html);exit;
			echo "<script>alert('保存成功')</script>";
			$go_url="qihtb.php?view=xunigc&mode=".$params['mode']."&GUID=".$params['GUID']."&mc_type=".$params['mc_type'].$str;
			//echo $go_url;
			goURL($go_url);
	   }


		public function xunigcnew($params)
		{
			//print_r($params);
			$mc_type=$params['mc_type'];
			if(isset($params['curdate'])){
				$GLOBALS['type_date']=$params['curdate'];
				$GLOBALS['type_Y']=date("Y",strtotime($GLOBALS['type_date']));
				$GLOBALS['type_Y_m']=date("Y-m",strtotime($GLOBALS['type_date']));
			}
            $sql="select * from TaoliParameter where type='1' and mc_type='".$mc_type."' order by id desc limit 1";
            $abc_list=$this->drc->getRow($sql);
            // echo "<pre>";
            //   file_put_contents("/tmp/xunigcnew.log", print_r("EEE===".date("Y-m-d H:m:s"),true).PHP_EOL, FILE_APPEND);
            //echo  "22201".date("Y-m-d H:m:s");
            if(!empty($abc_list)){
                $params['a']=$abc_list['v1'];
                $params['b']=$abc_list['v2'];
                $params['c']=$abc_list['v3'];
                $params['d']=$abc_list['v4'];
                $params['e']=$abc_list['v5'];
                $params['f']=$abc_list['v6'];
                $params['g']=$abc_list['v7'];
                $params['h']=$abc_list['v8'];
                $params['i']=$abc_list['v9'];
                $params['j']=$abc_list['v10'];
                $params['k']=$abc_list['v11'];

            }
            $blockid =array(
            1 => 'RB', //'439',  //螺纹钢
            2 => 'I', //'473',  // 铁矿石
            3 => 'JM', //'453',  // 煤焦
            4 => 'J', //'452',  // 焦碳
            5 => 'HC',
            );
            $block_array=array();
            $date=$this->workday_gzj($GLOBALS['type_date']);
            if($date=='1'){
                for($i=0;$i<10;$i++){
                    $tmp_date=$this->workday_gzj($date);
                    if($tmp_date=='1'){
                        $date=date("Y-m-d",strtotime("-1 day",strtotime($date)));
                    }else{
                        break;
                    }
                    
                }
                //	echo "今天休息！";exit;
            }
			$memcache = new Memcache;
			$memcache->connect(MEMCACHE_SERVER, MEMCACHE_PORT); 
			$block_array=$memcache->get("timing_gctb_qh");
			
            if($block_array==false){
                foreach( $blockid as $key => $bid ){
                    $tmp = $this->getqhstrbyurl_sina( $bid );
                    if($key=='1'){
                        $RB=$tmp[2];
                        $block_array['RB']=round($tmp[2], 2);
                    }
                    if($key=='2'){
                        $IR=$tmp[2];
                        $block_array['IR']=round($tmp[2], 2);
                    }
                    if($key=='3'){
                        $JM=$tmp[2];
                        $block_array['JM']=round($tmp[2], 2);
                    }
                    if($key=='4'){
                        $JT=$tmp[2];
                        $block_array['JT']=round($tmp[2], 2);
                    }
                    if($key=='5'){
                        $HC=$tmp[2];
                        $block_array['HC']=round($tmp[2], 2);
                    }
                }
                if(!empty($block_array)){
                    $memcache->set("timing_gctb_qh",$block_array,MEMCACHE_COMPRESSED, time()+(7*86400));
                }
            }
            $memcache->close();
			// file_put_contents("/tmp/xunigcnew.log", print_r("CCCC===".date("Y-m-d H:m:s"),true).PHP_EOL, FILE_APPEND);
		    $FG=$this->selectSCPrice('118210', $GLOBALS['type_date']);
            //    file_put_contents("/tmp/xunigcnew.log", print_r("BBB===".date("Y-m-d H:m:s"),true).PHP_EOL, FILE_APPEND);
            if(strstr($FG,"-")){
                $avgprice = explode("-",$FG);
                $FG = round(($avgprice['0']+$avgprice['1'])/2,2);
            }
            $block_array['FG']=$FG;

            $style_url= dirname("//".$_SERVER['SERVER_NAME'].$_SERVER['PHP_SELF']);
            //    file_put_contents("/tmp/xunigcnew.log", print_r("AAAA===".date("Y-m-d H:m:s"),true).PHP_EOL, FILE_APPEND);
            $this->assign( "style_url", $style_url );
            $this->assign("params",$params);
            $this->assign("abc_list",$abc_list);
            $this->assign("block_array",$block_array);
		}

		  public function save_xunigcnew($params){
			//  exit;
			 header('Content-Type:text/html;charset=utf8');
	
			 $this->drc->execute("INSERT INTO  `steelhome_drc`.`TaoliParameter` (`type` ,`v1` ,`v2` ,`v3` ,`v4` ,`v5` ,`v6`,`v7` ,`v8` ,`v9` ,`v10` ,`v11`   ,`createtime`,`mc_type`)VALUES ('1',  '".$params['a']."',  '".$params['b']."',  '".$params['c']."',  '".$params['d']."',  '".$params['e']."','".$params['f']."',  '".$params['g']."',  '".$params['h']."',  '".$params['i']."',  '".$params['j']."', '".$params['k']."',     now(),'".$params['mc_type']."');");
			
			 //print_r($params);
			   $sql="select * from app_session_temp where  GUID ='".$params['GUID']."'  order by  LoginDate DESC  limit 1";
			   $res=$this->_dao->getRow($sql);
			   $Mid=$res['Mid'];
			   $uid=$res['Uid'];
				$str="&RB=".$params['RB']."&JM=".$params['JM']."&HC=".$params['HC']."&JT=".$params['JT']."&IR=".$params['IR']."&FG=".$params['FG']."&a=".$params['a']."&b=".$params['b']."&c=".$params['c']."&d=".$params['d']."&e=".$params['e']."&f=".$params['f']."&g=".$params['g']."&h=".$params['h']."&i=".$params['i']."&j=".$params['j']."&k=".$params['k']."&AAA=".$params['RBCBJT']."&XXX=".$params['PMLRJT']."&BBB=".$params['RBCBJM']."&YYY=".$params['PMLRJTJM']."&CCC=".$params['HCCBJT']."&ZZZ=".$params['HCPMLRJT']."&DDD=".$params['HCCBJM']."&SSS=".$params['HCPMLRJTJM']."";
			   //print_r($sql);exit;
			   $url = DC_URL.DCURL."/qihtb.php?view=xunigcnew&mode=".$params['mode']."&GUID=".$params['GUID']."&mc_type=".$params['mc_type']."&issave=1&curdate=".$GLOBALS['type_date']."".$str;
			   //print_r($url);
			   include '/usr/local/www/libs/phpQuery/phpQuery.php';
			   phpQuery::newDocumentFile($url);
			   $html=pq("html")->html();
			   //print_r($url);exit;
			   $modelcontent=htmlentities($html, ENT_QUOTES,"UTF-8");
			   $insql="INSERT INTO  `steelhome_drc`.`TaoliModel` (`type` ,`modelcontent` ,`mid` ,`uid` ,`createtime` ,`date`,`mc_type`)VALUES ( '1',  '$modelcontent',  '$Mid',  '$uid',  now(),  '".$GLOBALS['type_date']."','".$params['mc_type']."');";
			   $this->drc->execute($insql);
			   //print_r($url);exit;
			   //print_r(DC_URL.DCURL."/qihtb.php?view=xunigc&GUID='".$params['GUID']."'".$str);
			   //exit;
			   //$str=iconv('UTF-8', 'GB2312','保存成功');
			   //print_r($url);
			   //print_r($html);exit;
			   echo "<script>alert('保存成功')</script>";
			   $go_url="qihtb.php?view=xunigcnew&mode=".$params['mode']."&GUID=".$params['GUID']."&mc_type=".$params['mc_type'].$str;
			   //echo $go_url;
			   goURL($go_url);
		  }
		  
		  public function selectSCPrice($topicture,$mconmanagedate) {
			//add by zfy started 2018/5/30
			if(strlen($topicture)=='6'){
				$where = " and topicture = '$topicture' ";
			}else if (strlen($topicture)=='7'){
				$where = " and mastertopid = '$topicture' ";
			}
			//add by zfy started 2018/5/30
			$sql = "select price from marketconditions where mconmanagedate<='$mconmanagedate 23:59:59' $where order by mconmanagedate desc limit 1";
			$price = $this->steelhome->getOne($sql);
			//echo $sql.":price=".$price."</br>";
			return $price;
		}

		public function timing_save($params){
			//print_r("111");
			if(isset($params['curdate']) && $params['curdate']!=''){
				$GLOBALS['type_date']=$params['curdate'];
			}
			$a='1.6';
			$b='0.5';
			$c='550';
			$d='250';
			$e='30';
			//$code_str="('RB','JM','J','I')";
			$blockid =array(
			1 => 'RB', //'439',  //螺纹钢
			2 => 'I', //'473',  // 铁矿石
			3 => 'JM', //'453',  // 煤焦
			4 => 'J', //'452',  // 焦碳
			5 => 'HC',
			);
			$date=$this->workday_gzj($GLOBALS['type_date']);
			if($date=='1'){
				for($i=0;$i<10;$i++){
					$tmp_date=$this->workday_gzj($date);
					if($tmp_date=='1'){
						$date=date("Y-m-d",strtotime("-1 day",strtotime($date)));
					}else{
						break;
					}
					
				}
				
			//	echo "今天休息！";exit;
			}
			// else{
				// foreach( $blockid as $key => $bid ){
				// 	$tmp = $this->getqhstrbyurl_sina( $bid );
				// 	//print_r($tmp);
				// 	if($key=='1'){
				// 		$RB=$tmp[2];
				// 	}
				// 	if($key=='2'){
				// 		$IR=$tmp[2];
				// 	}
				// 	if($key=='3'){
				// 		$JM=$tmp[2];
				// 	}
				// 	if($key=='4'){
				// 		$JT=$tmp[2];
				// 	}
				// 	if($key=='5'){
				// 		$HC=$tmp[2];
				// 	}
					
				// }
				$memcache = new Memcache; 
				$memcache->connect(MEMCACHE_SERVER, MEMCACHE_PORT); 
				$block_array=$memcache->get("timing_gctb_qh");
				$memcache->close();
				$RB=$block_array["RB"];
				$IR=$block_array["IR"];
				$JM=$block_array["JM"];
				$JT=$block_array["JT"];
				$HC=$block_array["HC"];
				$AAA=($a*($IR+$e)+$b*$JT+$c+$d)*1.16;
				$XXX=$RB-$AAA;
				$BBB=($a*($IR+$e)+$b*($JM*1.4)+$c+$d)*1.16;
				$YYY=$RB-$BBB;
				//$AAA=round($AAA);
				$XXX=round($XXX,2);
				//$BBB=round($BBB);
				$YYY=round($YYY,2);
			//	print_r($RB);
				$CCC=$AAA+200*1.16;
				$DDD=$BBB+200*1.16;
				$ZZZ=$HC-$CCC;
				$SSS=$HC-$DDD;
				$ZZZ=round($ZZZ,2);
				//$BBB=round($BBB);
				$SSS=round($SSS,2);
				$isset_sql="select * from `TaoliModelTable` where type=1 and date='".$GLOBALS['type_date']."' and mc_type=1 ";
				$temp=$this->drc->query($isset_sql);
				if(!empty($temp)){
					$update_sql="update `TaoliModelTable` set `value1`='$XXX' , `value2`='$YYY',`value3`='$ZZZ' , `value4`='$SSS',`createtime`=now() where type=1 and date='".$GLOBALS['type_date']."' and mc_type=1";
					$this->drc->execute($update_sql);
				}else{
					$insql="INSERT INTO  `TaoliModelTable` (`type` ,`value1` ,`value2` ,`value3` ,`value4` ,`createtime` ,`date`,mc_type)VALUES ('1',  '$XXX',  '$YYY',  '$ZZZ',  '$SSS',  now(),  '".$GLOBALS['type_date']."','1');";
					$this->drc->execute($insql);
				}

			//}
			

		}
		function curl_file_get_contents($durl){
			$ch = curl_init();
			curl_setopt($ch, CURLOPT_URL, $durl);
			curl_setopt($ch, CURLOPT_HEADER, 0);
			curl_setopt($ch, CURLOPT_HTTPHEADER,
				array("Referer: https://finance.sina.com.cn/futuremarket/",
					"User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/97.0.4692.99 Safari/537.36",
					"sec-ch-ua: \" Not;A Brand\";v=\"99\", \"Google Chrome\";v=\"97\", \"Chromium\";v=\"97\"",
					"sec-ch-ua-mobile: ?0",
					"sec-ch-ua-platform: \"Windows\"",
					"Sec-Fetch-Dest: script",
					"Sec-Fetch-Mode: no-cors",
					"Sec-Fetch-Site: cross-site"));
			ob_start();
			$data = curl_exec($ch);
			$qhdata = ob_get_contents();
			ob_end_clean();
			if (!curl_errno($ch)) {
				$info = curl_getinfo($ch);
			} else {
				echo 'Curl error: ' . curl_error($ch);
				curl_close($ch);
				exit;
			}
			curl_close($ch);
			$qhdata = iconv("gb2312","UTF-8//IGNORE",$qhdata);
			return $qhdata;
		}
		public function getqhstrbyurl_sina( $type, $space = false ){
			//$url = "http://nufm.dfcfw.com/EM_Finance2014NumericApplication/JS.aspx?type=CT&cmd=C.${type}&sty=FCFL4O&sortType=C&sortRule=-1&js=[(x)]&token=7bc05d0d4c3c22ef9fca8c2a912d779c";
			$code= $type.date("ym");
			$code_end = date('ym',strtotime("+1 year"));
			for($i=1;$i<=12;$i++){
				$code .= ",${type}".date('ym',strtotime("+$i month"));
			}
			
			$url ="http://hq.sinajs.cn/list=${code}&rn=".time();

		  $tmp = $this->curl_file_get_contents( $url );
		  //echo $tmp; 
		  preg_match_all("|\"(.*)\"|U", $tmp, $matchs);
		  //preg_match_all("|hq_str_nf_(.*)=\"|U", $html, $code_matchs);
		 // $tmp_codes = $code_matchs[1];
		  $data1 = $matchs[1];
		  $data = array();
		  foreach($data1 as &$item){
		  	if($item == "")continue;
		  	$data[] = explode(",",$item);
		  }
		  if( !$data ) return false;
		  $tmpData = array();
		  $maxVolume = 0;
		  $maxIndex = -1;
		  foreach( $data as $key => &$v ){
		  	$tmpVolume = $v[13];
		  	if($tmpVolume > $maxVolume) {
		  		$maxVolume = $tmpVolume;
		  		$maxIndex = $key;
		  	}
		  }
		  
		  if(isset($data[$maxIndex])){
			$maxData = $data[$maxIndex];
			  
		  	$tmpData[1] = str_replace("热轧卷板","热卷",$maxData[0]);
		  	$tmpData[2] = $maxData[8];
			$tmpData[3] = $maxData[8] - $maxData[10];
			$tmpData[4] = $maxData[6];
			$tmpData[5] = $maxData[11];
			$tmpData[6] = $maxData[7];
			$tmpData[7] = $maxData[12];
			$tmpData[8] = $maxData[14];
			$tmpData[9] = $maxData[2];
			$tmpData[10] = $maxData[10];
			$tmpData[11] = $maxData[3];
			$tmpData[12] = $maxData[4];
			$tmpData[13] = $maxData[13];
			$tmpData[14] = $maxData[18];
		  }
		  
		  if(empty($tmpData)) return false;
		  return $tmpData;
	}
	public function gctbyl($params){
		$mc_type=$params['mc_type'];
		if(isset($params['curdate'])){
			$GLOBALS['type_date']=$params['curdate'];
			$GLOBALS['type_Y']=date("Y",strtotime($GLOBALS['type_date']));
			$GLOBALS['type_Y_m']=date("Y-m",strtotime($GLOBALS['type_date']));
		}
		$sql="select * from TaoliParameter where type='2' and mc_type='".$mc_type."' order by id desc limit 1";
		$abc_list=$this->drc->getRow($sql);
		//print_r($sql);
		if(!empty($abc_list)){
			$params['E']=$abc_list['v1'];
			$params['F']=$abc_list['v2'];
			$params['G']=$abc_list['v3'];
			$params['H']=$abc_list['v4'];
			

		}
		// $blockid =array(
		// 	1 => 'RB', //'439',  //螺纹钢
		// 	2 => 'I', //'473',  // 铁矿石
		// 	3 => 'JM', //'453',  // 煤焦
		// 	4 => 'J', //'452',  // 焦碳
		// 	5 => 'HC',
		// 	);
			$block_array=array();
			$date=$this->workday_gzj($GLOBALS['type_date']);
			if($date=='1'){
				for($i=0;$i<10;$i++){
					$tmp_date=$this->workday_gzj($date);
					if($tmp_date=='1'){
						$date=date("Y-m-d",strtotime("-1 day",strtotime($date)));
					}else{
						break;
					}
					
				}
				
			//	echo "今天休息！";exit;
			}
			// if($date=='1'){
			// 	echo "今天休息！";exit;
			// }else{
				// foreach( $blockid as $key => $bid ){
				// 	$tmp = $this->getqhstrbyurl_sina( $bid );

					
				// 	if($key=='2'){
				// 		$IR=$tmp[2];
				// 		$block_array['IR']=$tmp[2];
				// 	}
				// 	if($key=='3'){
				// 		$JM=$tmp[2];
				// 		$block_array['JM']=$tmp[2];
				// 	}
				// 	if($key=='4'){
				// 		$JT=$tmp[2];
				// 		$block_array['JT']=$tmp[2];
				// 	}
					
				// }
		//}
		$memcache = new Memcache; 
	 
		$memcache->connect(MEMCACHE_SERVER, MEMCACHE_PORT); 
		//print_r($memcache);
		$block_array=$memcache->get("timing_gctb_qh");
		$memcache->close();
		$style_url= dirname("//".$_SERVER['SERVER_NAME'].$_SERVER['PHP_SELF']);
		$this->assign( "style_url", $style_url );
		$this->assign("params",$params);
		$this->assign("abc_list",$abc_list);
		$this->assign("block_array",$block_array);
	}
		 public function save_gctbyl($params){
		   header('Content-Type:text/html;charset=utf8');
			//print_r($params);exit;
			$this->drc->execute("INSERT INTO  `steelhome_drc`.`TaoliParameter` (`type` ,`v1` ,`v2` ,`v3` ,`v4` ,`v5` ,`v6`  ,`createtime`,`mc_type`)VALUES ('2','".$params['E']."',  '".$params['F']."',  '".$params['G']."',  '".$params['H']."','',  '',  now(),'".$params['mc_type']."');");
			$sql="select * from app_session_temp where  GUID ='".$params['GUID']."'  order by  LoginDate DESC  limit 1";
			$res=$this->_dao->getRow($sql);
			$Mid=$res['Mid'];
			$uid=$res['Uid'];
			 $str="&A=".$params['A']."&JM=".$params['JM']."&JT=".$params['JT']."&IR=".$params['IR']."&E=".$params['E']."&F=".$params['F']."&G=".$params['G']."&H=".$params['H']."&gcd=".$params['gcd']."&tksd=".$params['tksd']."&tkss=".$params['tkss']."&tksnjc=".$params['tksnjc']."&tkszj=".$params['tkszj']."&jtd=".$params['jtd']."&jts=".$params['jts']."&jtnjc=".$params['jtnjc']."&jtzj=".$params['jtzj']."&jmd=".$params['jmd']."&jms=".$params['jms']."&jmnjc=".$params['jmnjc']."&jmzj=".$params['jmzj']."&sumzj=".$params['sumzj']."";
			//print_r($sql);exit;
			$url = DC_URL.DCURL."/qihtb.php?view=gctbyl&mode=".$params['mode']."&GUID=".$params['GUID']."&mc_type=".$params['mc_type']."&issave=1&curdate=".$GLOBALS['type_date']."".$str;
			include '/usr/local/www/libs/phpQuery/phpQuery.php';
			phpQuery::newDocumentFile($url);
			$html=pq("html")->html();
			//print_r($url);
			$modelcontent=htmlentities($html, ENT_QUOTES,"UTF-8");
			$insql="INSERT INTO  `steelhome_drc`.`TaoliModel` (`type` ,`modelcontent` ,`mid` ,`uid` ,`createtime` ,`date`,`mc_type`)VALUES ( '2',  '$modelcontent',  '$Mid',  '$uid',  now(),  '".$GLOBALS['type_date']."','".$params['mc_type']."');";
			$this->drc->execute($insql);
			
			//print_r(DC_URL.DCURL."/qihtb.php?view=xunigc&GUID='".$params['GUID']."'".$str);
			//exit;
			echo "<script> window.alert = function(name){
					
					var iframe = document.createElement('IFRAME');
					iframe.style.display='none';
					iframe.setAttribute('src', 'data:text/plain,');
					document.documentElement.appendChild(iframe);
					window.frames[0].window.alert(name);
					iframe.parentNode.removeChild(iframe);
		  		}
		  		alert('保存成功')</script>";
			$go_url="qihtb.php?view=gctbyl&mode=".$params['mode']."&GUID=".$params['GUID']."&mc_type=".$params['mc_type']."".$str;
			
			goURL($go_url);
	   }
	   public function tksft($params){
		  // print_r($this->drc);
		  $mc_type=$params['mc_type'];
		if(isset($params['curdate'])){
			$GLOBALS['type_date']=$params['curdate'];
			$GLOBALS['type_Y']=date("Y",strtotime($GLOBALS['type_date']));
			$GLOBALS['type_Y_m']=date("Y-m",strtotime($GLOBALS['type_date']));
		}
		 $sql="select * from TaoliParameter where type='3' and mc_type='".$mc_type."'  order by id desc limit 1";
		$abc_list=$this->drc->getRow($sql);
			//print_r($abc_list);
		//print_r($sql);
		if(!empty($abc_list)){
			$params['B']=$abc_list['v1'];
			$params['C']=$abc_list['v2'];
			$params['D']=$abc_list['v3'];
			$params['E']=$abc_list['v4'];
			$params['F']=$abc_list['v5'];
			$params['H']=$abc_list['v6'];
			

		}
		$blockid =array(
			1 => 'RB', //'439',  //螺纹钢
			2 => 'I', //'473',  // 铁矿石
			3 => 'JM', //'453',  // 煤焦
			4 => 'J', //'452',  // 焦碳
			5 => 'HC',
			);
			$block_array=array();
			$date=$this->workday_gzj($GLOBALS['type_date']);
			if($date=='1'){
				for($i=0;$i<10;$i++){
					$tmp_date=$this->workday_gzj($date);
					if($tmp_date=='1'){
						$date=date("Y-m-d",strtotime("-1 day",strtotime($date)));
					}else{
						break;
					}
					
				}
				
			//	echo "今天休息！";exit;
			}
			// if($date=='1'){
			// 	echo "今天休息！";exit;
			// }else{
				// foreach( $blockid as $key => $bid ){
				// 	$tmp = $this->getqhstrbyurl_sina( $bid );

					
				// 	if($key=='2'){
				// 		$IR=$tmp[2];
				// 		$block_array['IR']=$tmp[2];
				// 	}
					
				// }
		//}
		$memcache = new Memcache; 
		$memcache->connect(MEMCACHE_SERVER, MEMCACHE_PORT); 
		$block_array=$memcache->get("timing_gctb_qh");
		$memcache->close();
		$style_url= dirname("//".$_SERVER['SERVER_NAME'].$_SERVER['PHP_SELF']);
		$sql="select * from rmbrate where rdate='".$GLOBALS['type_date']."'";
		$meiy_list=$this->drc->getRow($sql);
		//print_r($meiy_list);
		$this->assign("MYRB",$meiy_list['rd1']/100);
		$this->assign( "style_url", $style_url );
		$this->assign("params",$params);
		$this->assign("abc_list",$abc_list);
		$this->assign("block_array",$block_array);
	   }
	   public function save_tksft($params){
		   
		    header('Content-Type:text/html;charset=utf8');
		 //  print_r($params);exit;
		 $this->drc->execute("INSERT INTO  `steelhome_drc`.`TaoliParameter` (`type` ,`v1` ,`v2` ,`v3` ,`v4` ,`v5` ,`v6` ,`createtime`,`mc_type`)VALUES ('3','".$params['B']."',  '".$params['C']."', '".$params['D']."',  '".$params['E']."',  '".$params['F']."',  '".$params['H']."',  now(),'".$params['mc_type']."');");
			$sql="select * from app_session_temp where  GUID ='".$params['GUID']."'  order by  LoginDate DESC  limit 1";
			$res=$this->_dao->getRow($sql);
			$Mid=$res['Mid'];
			$uid=$res['Uid'];
			 $str="&A=".$params['A']."&B=".$params['B']."&C=".$params['C']."&D=".$params['D']."&E=".$params['E']."&F=".$params['F']."&G=".$params['G']."&H=".$params['H']."&tkqhjg=".$params['tkqhjg']."&qdgzf=".$params['qdgzf']."&zjcb=".$params['zjcb']."&jycb=".$params['jycb']."&yfc=".$params['yfc']."&hlzjj=".$params['hlzjj']."&zzsl=".$params['zzsl']."&qqjg=".$params['qqjg']."";
			
			$url = DC_URL.DCURL."/qihtb.php?view=tksft&mode=".$params['mode']."&GUID=".$params['GUID']."&mc_type=".$params['mc_type']."&issave=1&curdate=".$GLOBALS['type_date']."".$str;
			include '/usr/local/www/libs/phpQuery/phpQuery.php';
			//print_r($url);
			phpQuery::newDocumentFile($url);
			$html=pq("html")->html();
			
			//print_r($url);
			$modelcontent=htmlentities($html, ENT_QUOTES,"UTF-8");
			$insql="INSERT INTO  `steelhome_drc`.`TaoliModel` (`type` ,`modelcontent` ,`mid` ,`uid` ,`createtime` ,`date`,`mc_type`)VALUES ( '3',  '$modelcontent',  '$Mid',  '$uid',  now(),  '".$GLOBALS['type_date']."','".$params['mc_type']."');";
			$this->drc->execute($insql);
			
			echo "<script>alert('保存成功')</script>";
			$go_url="qihtb.php?view=tksft&mode=".$params['mode']."&GUID=".$params['GUID']."&mc_type=".$params['mc_type']."".$str;
			//echo $go_url;
			gourl($go_url);
	   }
	   public function timing_tksft($params){
		   //print_r("111");
			if(isset($params['curdate']) && $params['curdate']!=''){
				$GLOBALS['type_date']=$params['curdate'];
			}
			$B=21.5;
			$C=8;
			$D=10;
			$E=0.02;
			$F=6;
			$H=16;
			$sql="select * from rmbrate where rdate='".$GLOBALS['type_date']."'";
			$meiy_list=$this->drc->getRow($sql);
			if (!$meiy_list){
			    echo "当前日期无汇率数据";exit();
            }
			//print_r($meiy_list);
			$G=$meiy_list['rd1']/100;
			//$code_str="('RB','JM','J','I')";
			$blockid =array(
			1 => 'nf_I', //'473',  // 铁矿石
			);
			$date=$this->workday_gzj($GLOBALS['type_date']);
			if($date=='1'){
				for($i=0;$i<10;$i++){
					$tmp_date=$this->workday_gzj($date);
					if($tmp_date=='1'){
						$date=date("Y-m-d",strtotime("-1 day",strtotime($date)));
					}else{
						break;
					}
					
				}
				
			//	echo "今天休息！";exit;
			}
			// if($date=='1'){
			// 	echo "今天休息！";exit;
			// }else{
				foreach( $blockid as $key => $bid ){
					$tmp = $this->getqhstrbyurl_sina( $bid );
					if($key=='1'){
						$A=$tmp[2];
					}
					
					
				}
               $X=($A-$B+ $A*$D/100*$F/100*180/360+$A*$E/100*2-$C)/$G/(1+$H/100);

				//print_r($A);
				$X=round($X,2);
				$isset_sql="select * from `TaoliModelTable` where type=3 and date='".$GLOBALS['type_date']."' and mc_type=1 ";
				$temp=$this->drc->query($isset_sql);
				if(!empty($temp)){
					$update_sql="update `TaoliModelTable` set `value1`='$X' ,`createtime`=now() where type=3 and date='".$GLOBALS['type_date']."' and mc_type=1";
					$this->drc->execute($update_sql);
				}else{
					$insql="INSERT INTO  `TaoliModelTable` (`type` ,`value1` ,`value2` ,`value3` ,`value4` ,`createtime` ,`date`,mc_type)VALUES ('3',  '$X',  '',  '',  '',  now(),  '".$GLOBALS['type_date']."','1');";
					$this->drc->execute($insql);
				}

			//}
			
	   }
		//钢之家工作日
	   public function workday_gzj($day){
		//print_r($day);
		$date = "";
		include_once("/etc/steelconf/config/isholiday.php");
		$isexist=_isholiday($day);
		if($isexist=='1'){
			$date='1';
		}else{
			$date='0';
		}		
		return $date;
	}
	public function timing_gctb_qh(){
		$blockid =array(
			1 => 'nf_RB', //'439',  //螺纹钢
			2 => 'nf_I', //'473',  // 铁矿石
			3 => 'nf_JM', //'453',  // 煤焦
			4 => 'nf_J', //'452',  // 焦碳
			5 => 'nf_HC',
		);
		 $memcache = new Memcache; 
		 $memcache->connect(MEMCACHE_SERVER, MEMCACHE_PORT); 
		 $block_array=$memcache->get("timing_gctb_qh");
         if($block_array==false)
         {
            $block_array=array();
         }
		foreach( $blockid as $key => $bid ){
			$tmp = $this->getqhstrbyurl_sina( $bid );
			if($tmp[2]!=""){
				if($key=='1' ){
					$RB=$tmp[2];
					$block_array['RB']=$tmp[2];
				}
				if($key=='2'){
					$IR=$tmp[2];
					$block_array['IR']=$tmp[2];
				}
				if($key=='3'){
					$JM=$tmp[2];
					$block_array['JM']=$tmp[2];
				}
				if($key=='4'){
					$JT=$tmp[2];
					$block_array['JT']=$tmp[2];
				}
				if($key=='5'){
					$block_array['HC']=$tmp[2];
				}
			}
			
			
		}
		// print_r($block_array);
		if(!empty($block_array)){
			//$memcache = new Memcache; 
	 
			//$memcache->connect(MEMCACHE_SERVER, MEMCACHE_PORT); 
			//print_r($memcache);
			$expire=time()+(7*86400);
			
			$memcache->set("timing_gctb_qh",$block_array,MEMCACHE_COMPRESSED,$expire);
			
			echo "ok";
		}else{
			echo "null";
		}
		$memcache->close();
	}
	//补数据
	public function adddate($params){
		//螺纹钢--SHQHDAY_4
		//焦煤--SHQHDAY_19
		//焦炭--SHQHDAY_9
		//铁矿石--SHQHDAY_20
		$start_date=$params['start_date'];
		$end_date=$params['end_date'];
		//$data_item=$params['data_item'];
		$data_item="('SHQHDAY_4','SHQHDAY_19','SHQHDAY_9','SHQHDAY_20','SHQHDAY_99')";
		$sql = "SELECT *   FROM `data_table` WHERE `dta_ym` >='$start_date' and `dta_ym` <='$end_date' and `dta_type` in $data_item AND dta_1 not like '%小计%' AND dta_1 not like '%总计%' AND dta_1 not like '%合计%' AND dta_1 not like '%合计%' and dta_maxValStatus = 1  order by TO_DAYS(`dta_ym`) asc ";
	//	echo $sql;
		$array = $this->drc->query ( $sql );
		$sql="select * from rmbrate where rdate>='$dta_ym' and rdate<='$end_date'";
		$meiy_list=$this->drc->query($sql);
		// echo "<pre>"; print_R($array); echo "</pre>";
		
		$meiyuan=array();
		foreach($meiy_list as $k=>$val){
			$date= date('Y-m-d',strtotime($val['rdate']));
			$meiyuan[$date]=$val['rd1']/100;
		}
		
		$arr_temp = array ();
		foreach($array as $k=>$val){
			$dta_ym=$val['dta_ym'];
			$arr_temp[$val['dta_ym']][$val['dta_type']]['dta_7']=$val['dta_7'];
			$arr_temp[$val['dta_ym']][$val['dta_type']]['dta_ym']=$val['dta_ym'];		
		}
	
		$temp=array();
		foreach($arr_temp as $k_type=>$val_type){
			if(!empty($val_type['SHQHDAY_4']) && !empty($val_type['SHQHDAY_9']) && !empty($val_type['SHQHDAY_19']) && !empty($val_type['SHQHDAY_20']) && !empty($val_type['SHQHDAY_99'])){
				$temp[$k_type]=$val_type;
			}

		}
		$a='1.6';
		$b='0.5';
		$c='550';
		$d='250';
		$e='30';
		foreach($temp as $k_type=>$val_type){
			$dta_ym=$k_type;
			foreach($val_type as $k=>$val){
				
				//print_r($k);
				//echo "111";exit;
				if($k=="SHQHDAY_4"){
					$RB=$val['dta_7'];	
					
				}
				if($k=="SHQHDAY_9"){
					$JT=$val['dta_7'];
				}
				if($k=="SHQHDAY_19"){
					$JM=$val['dta_7'];	
				}
				if($k=="SHQHDAY_20"){
					$IR=$val['dta_7'];
					$A=$val['dta_7'];
				}
				if($k=="SHQHDAY_99"){
					$HC=$val['dta_7'];
				}

			}
			if($params['type']=='1'){
				/*echo "<pre>";
				print_r($IR);
				echo "<pre>";
				print_r($RB);
				echo "<pre>";
				print_r($JT);
				echo "<pre>";
				print_r($JM);*/
				$AAA=($a*($IR+$e)+$b*$JT+$c+$d)*1.16;
				$XXX=$RB-$AAA;
				$XXX=round($XXX,2);
				$BBB=($a*($IR+$e)+$b*($JM*1.4)+$c+$d)*1.16;
				$YYY=$RB-$BBB;
				$YYY=round($YYY,2);
				$CCC=$AAA+200*1.16;
				$DDD=$BBB+200*1.16;
				$ZZZ=$HC-$CCC;
				$SSS=$HC-$DDD;
				$ZZZ=round($ZZZ,2);
				$SSS=round($SSS,2);
				$isset_sql="select * from `TaoliModelTable` where type=1 and date='".$dta_ym."' and mc_type=$mc_type";
				$temp=$this->drc->query($isset_sql);
				if(!empty($temp)){
					$update_sql="update `TaoliModelTable` set `value1`='$XXX' , `value2`='$YYY',`value3`='$ZZZ' , `value4`='$SSS',`createtime`=now() where type=1 and date='".$dta_ym."' and mc_type=$mc_type";
					$this->drc->execute($update_sql);
					//echo "<pre>";
					//echo $update_sql;
				}else{
					$insql="INSERT INTO  `TaoliModelTable` (`type` ,`value1` ,`value2` ,`value3` ,`value4` ,`createtime` ,`date`,mc_type)VALUES ('1',  '$XXX',  '$YYY',  '$ZZZ',  '$SSS',  now(),  '".$dta_ym."','$mc_type');";
					$this->drc->execute($insql);
					//echo "<pre>";
					//echo $insql;
				}
				
			}
			if($params['type']=='3'){
				//echo "111";
				$B=21.5;
				$C=8;
				$D=10;
				$E=0.02;
				$F=6;
				$H=16;
				if($meiyuan[$dta_ym]!=''){
					$G=$meiyuan[$dta_ym];
					$X=($A-$B+ $A*$D/100*$F/100*180/360+$A*$E/100*2-$C)/$G/(1+$H/100);
					$X=round($X,2);
					$isset_sql="select * from `TaoliModelTable` where type=3 and date='".$dta_ym."' and mc_type=$mc_type";
					$temp=$this->drc->query($isset_sql);
					if(!empty($temp)){
						$update_sql="update `TaoliModelTable` set `value1`='$X' ,`createtime`=now() where type=3 and date='".$dta_ym."' and mc_type=$mc_type ";
						$this->drc->execute($update_sql);
					}else{
						$insql="INSERT INTO  `TaoliModelTable` (`type` ,`value1` ,`value2` ,`value3` ,`value4` ,`createtime` ,`date`,mc_type)VALUES ('3',  '$X',  '',  '',  '',  now(),  '".$dta_ym."','$mc_type');";
						$this->drc->execute($insql);
					}
				}
				
			}
			

		}
	
		echo "OK";exit;	

	}


	
	public function timing_save_sg_xg($params){
		//print_r("111");
		if(isset($params['curdate']) && $params['curdate']!=''){
			$GLOBALS['type_date']=$params['curdate'];
		}
		$mc_type=$params['mc_type']?$params['mc_type']:3;
		$a='1.6';
		$b='0.5';
		$c='200';
		$d='1.6';
		$e='0.7';
		$f='200';
		$g='0.96';
		$h='0.15';
		$i='400';
		$j='250';
		$k='220';
		//$code_str="('RB','JM','J','I')";
		$blockid =array(
		1 => 'RB', //'439',  //螺纹钢
		2 => 'I', //'473',  // 铁矿石
		3 => 'JM', //'453',  // 煤焦
		4 => 'J', //'452',  // 焦碳
		5 => 'HC',
		);
		$date=$this->workday_gzj($GLOBALS['type_date']);
		if($date=='1'){
			for($i=0;$i<10;$i++){
				$tmp_date=$this->workday_gzj($date);
				if($tmp_date=='1'){
					$date=date("Y-m-d",strtotime("-1 day",strtotime($date)));
				}else{
					break;
				}
				
			}
			
		//	echo "今天休息！";exit;
		}
		// else{
			// foreach( $blockid as $key => $bid ){
			// 	$tmp = $this->getqhstrbyurl_sina( $bid );
			// 	//print_r($tmp);
			// 	if($key=='1'){
			// 		$RB=$tmp[2];
			// 	}
			// 	if($key=='2'){
			// 		$IR=$tmp[2];
			// 	}
			// 	if($key=='3'){
			// 		$JM=$tmp[2];
			// 	}
			// 	if($key=='4'){
			// 		$JT=$tmp[2];
			// 	}
			// 	if($key=='5'){
			// 		$HC=$tmp[2];
			// 	}
				
			// }
			$memcache = new Memcache; 
	 
			$memcache->connect(MEMCACHE_SERVER, MEMCACHE_PORT); 
		
			$block_array=$memcache->get("timing_gctb_qh");
			
			$memcache->close();
			$RB=$block_array['RB'];
			$IR=$block_array['IR'];
			$JM=$block_array['JM'];
			$JT=$block_array['JT'];
			$HC=$block_array['HC'];

			$FG=$this->selectSCPrice('118210', $GLOBALS['type_date']);

		   if(strstr($FG,"-")){
			$avgprice = explode("-",$FG);
			$FG = round(($avgprice['0']+$avgprice['1'])/2,2);
		   }
		  

		  $AAA=round(($a*$IR+$b*$JT+$c)+$h*$FG+$i+$j);
		  $BBB=round(($d*$IR+$e*$JM+$f)+$h*$FG+$i+$j);

		  $CCC=round(($a*$IR+$b*$JT+$c)+$h*$FG+$i+$k);

		  $DDD=round(($d*$IR+$e*$JM+$f)+$h*$FG+$i+$k);

		  $XXX=round($RB-$AAA);
		  $YYY=round($RB-$BBB);
		  $ZZZ=round($HC-$CCC);
		  $SSS=round($HC-$DDD);
		  
			$isset_sql="select * from `TaoliModelTable` where type=1 and date='".$GLOBALS['type_date']."' and mc_type='".$mc_type."' ";
			$temp=$this->drc->query($isset_sql);
			if(!empty($temp)){
				$update_sql="update `TaoliModelTable` set `value1`='$XXX' , `value2`='$YYY',`value3`='$ZZZ' , `value4`='$SSS',`createtime`=now() where type=1 and date='".$GLOBALS['type_date']."' and mc_type='2' ";
				$this->drc->execute($update_sql);
				$update_sql="update `TaoliModelTable` set `value1`='$XXX' , `value2`='$YYY',`value3`='$ZZZ' , `value4`='$SSS',`createtime`=now() where type=1 and date='".$GLOBALS['type_date']."' and mc_type='".$mc_type."' ";
				$this->drc->execute($update_sql);
			}else{
				$insql="INSERT INTO  `TaoliModelTable` (`type` ,`value1` ,`value2` ,`value3` ,`value4` ,`createtime` ,`date`,mc_type)VALUES ('1',  '$XXX',  '$YYY',  '$ZZZ',  '$SSS',  now(),  '".$GLOBALS['type_date']."','2');";
				$this->drc->execute($insql);
				$insql="INSERT INTO  `TaoliModelTable` (`type` ,`value1` ,`value2` ,`value3` ,`value4` ,`createtime` ,`date`,mc_type)VALUES ('1',  '$XXX',  '$YYY',  '$ZZZ',  '$SSS',  now(),  '".$GLOBALS['type_date']."','".$mc_type."');";
				$this->drc->execute($insql);
			
			}

		//}
		

	}



	public function adddate_sg_xg($params){
		//螺纹钢--SHQHDAY_4
		//焦煤--SHQHDAY_19
		//焦炭--SHQHDAY_9
		//铁矿石--SHQHDAY_20
		//热卷--
		$mc_type=$params['mc_type']?$params['mc_type']:1;
		$start_date=$params['start_date']?$params['start_date']:'2014-01-01';
		$end_date=$params['end_date']?$params['end_date']:date('Y-m-d');
		//$data_item=$params['data_item'];
		$data_item="('SHQHDAY_4','SHQHDAY_19','SHQHDAY_9','SHQHDAY_20','SHQHDAY_99')";
		$sql = "SELECT *   FROM `data_table` WHERE `dta_ym` >='$start_date' and `dta_ym` <='$end_date' and `dta_type` in $data_item AND dta_1 not like '%小计%' AND dta_1 not like '%总计%' AND dta_1 not like '%合计%' AND dta_1 not like '%合计%' and dta_maxValStatus = 1  order by TO_DAYS(`dta_ym`) asc ";
	//	echo $sql;
		$array = $this->drc->query ( $sql );
		$sql="select * from rmbrate where rdate>='$dta_ym' and rdate<='$end_date'";
		$meiy_list=$this->drc->query($sql);
		// echo "<pre>"; print_R($array); echo "</pre>";
		
		$meiyuan=array();
		foreach($meiy_list as $k=>$val){
			$date= date('Y-m-d',strtotime($val['rdate']));
			$meiyuan[$date]=$val['rd1']/100;
		}
		
		$arr_temp = array ();
		foreach($array as $k=>$val){
			$dta_ym=$val['dta_ym'];
			$arr_temp[$val['dta_ym']][$val['dta_type']]['dta_7']=$val['dta_7'];
			$arr_temp[$val['dta_ym']][$val['dta_type']]['dta_ym']=$val['dta_ym'];		
		}
	
		$temp=array();
		foreach($arr_temp as $k_type=>$val_type){
			if(!empty($val_type['SHQHDAY_4']) && !empty($val_type['SHQHDAY_9']) && !empty($val_type['SHQHDAY_19']) && !empty($val_type['SHQHDAY_20']) && !empty($val_type['SHQHDAY_99'])){
				$temp[$k_type]=$val_type;
			}

		}
		$a='1.6';
		$b='0.5';
		$c='200';
		$d='1.6';
		$e='0.7';
		$f='200';
		$g='0.96';
		$h='0.15';
		$i='400';
		$j='250';
		$k='220';
		//print_r($temp);
		foreach($temp as $k_type=>$val_type){
			$dta_ym=$k_type;
			foreach($val_type as $k=>$val){
				
				//print_r($k);
				//echo "111";exit;
				if($k=="SHQHDAY_4"){
					$RB=$val['dta_7'];	
					
				}
				if($k=="SHQHDAY_9"){
					$JT=$val['dta_7'];
				}
				if($k=="SHQHDAY_19"){
					$JM=$val['dta_7'];	
				}
				if($k=="SHQHDAY_20"){
					$IR=$val['dta_7'];
					$A=$val['dta_7'];
				}
				if($k=="SHQHDAY_99"){
					$HC=$val['dta_7'];
				}

			}
		
			
				$FG=$this->selectSCPrice('118210', $dta_ym);

				if(strstr($FG,"-")){
					$avgprice = explode("-",$FG);
					$FG = round(($avgprice['0']+$avgprice['1'])/2,2);
				}
				$AAA=round(($a*$IR+$b*$JT+$c)+$h*$FG+$i+$j);
				$BBB=round(($d*$IR+$e*$JM+$f)+$h*$FG+$i+$j);

				$CCC=round(($a*$IR+$b*$JT+$c)+$h*$FG+$i+$k);

				$DDD=round(($d*$IR+$e*$JM+$f)+$h*$FG+$i+$k);

				$XXX=round($RB-$AAA);
				$YYY=round($RB-$BBB);
				$ZZZ=round($HC-$CCC);
				$SSS=round($HC-$DDD);
				$isset_sql="select * from `TaoliModelTable` where type=1 and date='".$dta_ym."' and mc_type=$mc_type";
				$temp=$this->drc->query($isset_sql);
				
				if(!empty($temp)){
					$update_sql="update `TaoliModelTable` set `value1`='$XXX' , `value2`='$YYY',`value3`='$ZZZ' , `value4`='$SSS',`createtime`=now() where type=1 and date='".$dta_ym."' and mc_type=2";
					$this->drc->execute($update_sql);
					$update_sql="update `TaoliModelTable` set `value1`='$XXX' , `value2`='$YYY',`value3`='$ZZZ' , `value4`='$SSS',`createtime`=now() where type=1 and date='".$dta_ym."' and mc_type=$mc_type";
					$this->drc->execute($update_sql);
					//echo "<pre>";
					echo $update_sql;
				}else{
					$insql="INSERT INTO  `TaoliModelTable` (`type` ,`value1` ,`value2` ,`value3` ,`value4` ,`createtime` ,`date`,mc_type)VALUES ('1',  '$XXX',  '$YYY',  '$ZZZ',  '$SSS',  now(),  '".$dta_ym."','2');";
					$this->drc->execute($insql);
					$insql="INSERT INTO  `TaoliModelTable` (`type` ,`value1` ,`value2` ,`value3` ,`value4` ,`createtime` ,`date`,mc_type)VALUES ('1',  '$XXX',  '$YYY',  '$ZZZ',  '$SSS',  now(),  '".$dta_ym."','$mc_type');";
					$this->drc->execute($insql);
					//echo "<pre>";
					echo $insql;
				}
				
			
			

		}
	
		echo "OK";exit;	

	}

} 
?>