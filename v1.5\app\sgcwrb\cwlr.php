<?php

class sgcwlrAction extends AbstractAction
{
	public function __construct()
	{
    	parent::__construct();
	}

	public function checkSession()
	{
    }
    
    public function lr($params) {
        // $curdate = date('Y-m');
        // $predate = date('Y-m', strtotime('-15 month', $curdate));
        $GUID = trim($params['GUID']);
        $mode = $params['mode'];
        if($GUID==""){
            alert('GUID不能为空');
            exit;
        }
        $user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
        if(empty($user_infos)){
            alert('用户不存在');
            exit;
        }
        // print_r($user_infos);
        $this->assign('GUID', $GUID);
        $this->assign('mode', $mode);
        $total = $this->_dao->getOne( "select count(id) from sg_data_table where dta_type='SG_SXFYLR'");
        $page = $params['page'] == '' ? 1 : $params['page'];
        $url = "sgcwrb.php";
        $per = 20;
        $start = ( $page - 1 ) * $per;
        unset( $params['page'] );
        $pagebar = pagebar( $url, $params, $per, $page, $total );
        $this->assign( "pagebar", $pagebar );
        $sql = "select id,dta_ym,dta_1,dta_2,dta_3,dta_4,dta_5,dta_6 from sg_data_table where dta_type='SG_SXFYLR' order by dta_ym desc limit $start,$per";
        $data = $this->_dao->query($sql);
        $this->assign('data', $data);
    }

    public function doadd($params){
        $GUID = trim($params['GUID']);
        $mode = $params['mode'];
        if($GUID==""){
            alert('GUID不能为空');
            exit;
        }
        $user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
        if(empty($user_infos)){
            alert('用户不存在');
            exit;
        }
        $sdate = $params['sdate'];
        $dta1 = trim($params["lg_gl"]);
        $dta2 = trim($params["lg_cw"]);
        $dta3 = trim($params["lg_xs"]);
        $dta4 = trim($params["hg_gl"]);
        $dta5 = trim($params["hg_cw"]);
        $dta6 = trim($params["hg_xs"]);
        $data = array(
            "龙钢管理费用"=>$dta1,
            "龙钢财务费用"=>$dta2,
            "龙钢销售费用"=>$dta3,
            "汉钢管理费用"=>$dta4,
            "汉钢财务费用"=>$dta5,
            "汉钢销售费用"=>$dta6,
        );
        foreach ($data as $k=>$v) {
            if(!is_numeric(trim($v)) && trim($v) != ""){
                alert($k.' 数据有误！请检查后重试');
                goURL("sgcwrb.php?view=lr&GUID=".$GUID."&mode=".$mode);
                exit;
            }
        }

        if (trim($params['sid']) != "" && (is_numeric(trim($params['sid'])))) {
            $id = trim($params['sid']);
            $sql = "update sg_data_table set dta_1='$dta1',dta_2='$dta2',dta_3='$dta3',dta_4='$dta4',dta_5='$dta5',dta_6='$dta6',dta_7='".$user_infos['Uid']."',dta_8=NOW() where id='$id'";
            $this->_dao->execute($sql);
            alert('修改成功！');
            goURL("sgcwrb.php?view=lr&GUID=".$GUID."&mode=".$mode);
        } elseif (trim($params['sid']) == ""){
            $co = $this->_dao->getone("select count(id) from sg_data_table where  dta_type='SG_SXFYLR' and dta_ym='$sdate'");
            if ($co > 0) {
                alert('该年月已有数据！');
                goURL("sgcwrb.php?view=lr&GUID=".$GUID."&mode=".$mode);
                exit;
            }
    
            $inssql = "insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,dta_3,dta_4,dta_5,dta_6,createuser,createtime) values('SG_SXFYLR','三项费用录入','$sdate','$dta1','$dta2','$dta3','$dta4','$dta5','$dta6','".$user_infos['Uid']."',NOW())";
            $this->_dao->execute($inssql);
            alert('新增成功！');
            goURL("sgcwrb.php?view=lr&GUID=".$GUID."&mode=".$mode);
        }
    }
    
    public function dodel($params)
    {
        $GUID = trim($params['GUID']);
        $mode = $params['mode'];
        if($GUID==""){
            alert('GUID不能为空');
            exit;
        }
        $user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
        if(empty($user_infos)){
            alert('用户不存在');
            exit;
        }
        $sql = "delete from sg_data_table where id='".$params['id']."'";
        $this->_dao->execute($sql);
        alert('删除成功！');
        goURL("sgcwrb.php?view=lr&GUID=".$GUID."&mode=".$mode);
    }
}

?>