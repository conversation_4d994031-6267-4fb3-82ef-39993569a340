
<!DOCTYPE html>
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta charset="utf-8">
	<title>会议中奖数据—<{$meetingName}></title>
	<link rel="stylesheet" href="css/choujiang/main.css?t=0913"/>
	<script type="text/javascript" src="js/choujiang/jquery.2.1.4.js"></script>
	<script type="text/javascript" src="js/choujiang/ajax.js"></script>
	<script type="text/javascript" src="js/choujiang/base64.js"></script>
	<link rel="stylesheet" href="css/choujiang/index.css?t=0913"/>
	<style type="text/css">
	body{background-image: url(images/choujiang/429/bodybg3.jpg);}
	.demo{ display: flex;justify-content: flex-start;width:1126px; margin: 30px auto; text-align:left;height: 126px; padding:10px 10px 0 10px;}
	.btn{ background:none; cursor: pointer; color:#cc0000; text-shadow:0 1px 0 rgba(255,255,255,0.6); font:bold 20px/50px "Microsoft Yahei"; border:none transparent; outline:none;}
	.bt{ text-align:center;background:url("images/choujiang/btbj.png") no-repeat; width:154px; height:60px; margin:0 auto;}
	table td{font-size: 18px;color: #ffffff;}
	table td input{font-size: 18px;color: #333333;width: 300px;height: 25px;line-height: 25px;border: 1px solid #51555C ;border-radius: 10px; outline:none;}
	table td button{background: #ffff00;width: 120px;height: 30px;line-height: 30px;border-radius: 10px;outline: 0;border: 1px solid #ffff00;color: #FF1A08;font-weight: bold;font-size: 18px;margin-left: 35%;margin-top: 20px;}
	table td button:active{background: #FFB900;}
	.wrapper{margin-top: 150px;}
	h2.top_title_meeting2{
		width: 70%;
		word-wrap: break-word;
		margin: 0px auto;
		height: auto;
		font-size: 38px;
	}
	</style>
<script type="text/javascript">
	var mtid="<{$mtid}>";
	
	function qkong(){
		if(window.confirm("确定清空中奖数据吗?")){
			console.log(localStorage['temp'+mtid]);
			if(localStorage['temp'+mtid]!=null){
				localStorage.removeItem("temp"+mtid);
			}
			if(localStorage['qxcj'+mtid]!=null){
				localStorage.removeItem("qxcj"+mtid);
			}
			console.log(localStorage['djc'+mtid]);
			if(localStorage['djc'+mtid]!=null){
				localStorage.removeItem("djc"+mtid);
			}
			qksql();
		}
	}

	function qksql(){
		$.ajax({
                type: "post",
                url: "meeting.php?action=clearCancerAward",
                data: {mtid:mtid,awardtype:5},
                dataType: "jsonp",
                success: function (response) {
                    if (response.Success == 1) {
                        alert("已清空");
                    } else {
						if(response.Message=='no')
                        	alert('会议已结束');
                    }
                },
                error: function () {
                   alert("失败");
                }
            });
			
	}





	function tuis(){
		if(localStorage['temp'+mtid]!=null && localStorage['temp'+mtid]!="[]"){
			var storedNames=localStorage['temp'+mtid];
			storedNames=JSON.parse(storedNames);
			var arr = new Array( );
			for(var i=0;i<storedNames.length;i++ ){
				var writer={"uid":storedNames[i]["uid"],"PersonSign":storedNames[i]['PersonSign'],"Mid":storedNames[i]['Mid']}; 
				arr.push(writer);
			}
			arr=JSON.stringify(arr);
			$.ajax({
                type: "post",
                url: "meeting.php?action=pushaward",
                data: {mtid:mtid,awardtype:5,Status:0, awarddata:Base64.encode(arr)},
                dataType: "jsonp",
                success: function (response) {
                    if (response.Success == 1) {
                        alert(response.Message);
                    } else {
                        alert(response.Msg);
                    }
                },
                error: function () {
                   alert("失败");
                }
            });
				
		}else{
			alert("无数据");
		}
	}




	function ObjStory(uid,PersonSign) //声明对象
	{
		this.uid = uid;
		this.PersonSign= PersonSign;
	}

	
</script>


</head>
<body>
	<div class="kong"></div>
	<{if $mtid==410}>
	<h2 class="top_title_meeting2">2024年广西·第十五届钢铁产业链发展形势会议</h2>
	<h2 class="top_title_meeting2">本次礼品由广西池城贸易有限公司赞助</h2>
	<{else}>
	<h2 class="top_title_meeting2"><{$meetingName}></h2>
	<{/if}>

	
	<div class="wrapper">
		<div id="main">
			<div class="demo" >
				<div class="bt"><input onclick="tuis();" type="button" class="btn" id="start" value="推送中奖数据"></div>
				<div class="bt"><input onclick="qkong();" type="button" class="btn" id="start" value="清空中奖数据"></div>
			</div>
		</div>
	</div>
</body>
</html>