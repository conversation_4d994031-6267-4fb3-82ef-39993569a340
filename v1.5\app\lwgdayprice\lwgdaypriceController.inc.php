<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );

class lwgdaypriceController extends AbstractController{
  
  public function __construct(){
    parent::__construct();
	
	$this->_action->setDao( new lwgdaypriceDao( "DRCW","DRC" ) );
	$this->_action->t1dao = new lwgdaypriceDao('MAIN') ;
	//$this->_action->stdao = new lwgdaypriceDao('91R') ;
	
  }
  
  public function _dopre(){
      //$this->_action->checkSession();
  }

  public function v_index() {
	$this->_action->index($this->_request);
  }

  public function do_save_price() {
	$this->_action->save_price($this->_request);
  }
}