<?php
class  xg<PERSON>nx<PERSON><PERSON> extends Dao{
	public function __construct($writer){
		 parent::__construct( $writer );
	}

	//通过mid取得公司信息
	public function get_userinfo_bymid($MID,$mc_type){
		$sql = "select * from app_license where  MID ='$MID' and mc_type='$mc_type' order by  CreateDate DESC  limit 1";
		return $this->getRow($sql);
	}


	//通过guid取得用户信息
	public function get_userinfo_byguid($GUID){
		$sql = "select * from app_session_temp where  GUID ='$GUID' order by  LoginDate DESC  limit 1";
		return $this->getRow($sql);
	}


	//通过guid取得用户信息
	public function get_detail_byguid($ID){
		$sql = "select * from app_license_detail where  ID ='$ID'  order by  CreateDate DESC  limit 1";
		return $this->getRow($sql);
	}

	//通过SignCS取得设备用户信息
	public function get_userinfo_bySignCS($SignCS,$mc_type,$MID){
		$sql = "select * from app_session_temp where  SignCS ='$SignCS' and mc_type='$mc_type' AND Mid='$MID' order by  LoginDate DESC  limit 1";
		return $this->getRow($sql);
	}

	//获取拥有的权限
	public function get_popedom_byid($LID,$uid,$mc_type){
		$sql = "select id,popid from app_license_popedom where uid='$uid' and mc_type='$mc_type' and isdel=0 ";
		//echo $sql;
		return $this->AQUERY($sql);
	}


	//获取拥有的权限
	public function get_self_popedom($uid,$mc_type){
		$quanxsql="SELECT app_license_popedom_item.id,app_license_popedom_item.itemname FROM app_license_popedom LEFT JOIN app_license_popedom_item ON app_license_popedom.popid=app_license_popedom_item.id LEFT JOIN app_license_popedom_type ON  app_license_popedom_type.id=app_license_popedom_item.typeid WHERE  app_license_popedom.uid='$uid'  and app_license_popedom.isdel='0' and app_license_popedom_type.status=1 AND app_license_popedom_type.mc_type='$mc_type' AND app_license_popedom_type.isdel=0 AND app_license_popedom_item.status=1 AND app_license_popedom_item.mc_type='$mc_type' AND app_license_popedom_item.isdel=0 order by app_license_popedom_item.id asc";
        
		return $this->AQUERY($quanxsql);
	}


	public function get_createpopedom_byid($LID,$uid,$mc_type,$userid){
		$sql = "select popid,creatuser from app_license_popedom where uid='$uid' and mc_type='$mc_type' and isdel=0 and creatuser!='$userid' ";
		//echo $sql;
		return $this->AQUERY($sql);
	}


	public function get_admin_byguid($userid){
		$sql = "select * from adminuser where  id ='$userid' ";
		return $this->getRow($sql);
	}

	
	//获取用户权限和用户名
	public function get_license_privilege($Uid){
		$sql = "select * from app_license,app_license_privilege where  app_license.id=app_license_privilege.lid and app_license_privilege.Uid='$Uid' and app_license_privilege.mc_type=1 limit 1";
		return $this->getRow($sql);
	}

	 //获取
	public function get_ChengBenParameter($where=""){
		$sql = "select * from sg_ChengBenParameter2 where 1 {$where} order by ndate DESC,id DESC limit 1";
        //echo $sql;
		return $this->getRow($sql);
	}
	
	public function getAll($type,$start,$per){
		$sql = "select * from sg_ChengBenParameter2 where Type='".$type."' and isdel=0 order by ndate DESC,id DESC limit $start,$per";
        //echo $sql;
		return $this->query($sql);
	}
	//获取ng_ChengBenParameter表信息
	public function get_sg_ChengBenParameter($Date,$Type){
		$sql = "select * from sg_ChengBenParameter where  ndate <='$Date' and  Type ='$Type' and isdel ='0' order by ndate desc,id desc  limit 1";
		//echo $sql;
		return $this->getRow($sql);
	}
	//获取
	public function get_sg_ChengBenParameter2($Date,$Type){
		$sql = "select * from sg_ChengBenParameter2 where  ndate <='$Date' and  Type ='$Type' and isdel ='0' order by ndate desc,id desc  limit 1";
		//echo $sql;
		return $this->getRow($sql);
	}
	//获取ng_ChengBenParameter3表信息
	public function get_sg_ChengBenParameter3($Date,$Type){
		$sql = "select * from sg_ChengBenParameter3 where  ndate <='$Date' and  Type ='$Type' and isdel ='0' order by ndate desc,id desc  limit 1";
		//echo $sql;
		return $this->getRow($sql);
	}
	//获取今日市场价格
	public function get_marketconditions_price_6($topictures,$mconmanagedate){
		$sql = "select price,topicture,mconmanagedate from marketconditions where topicture in (" . $topictures . ")  AND mconmanagedate >'" . $mconmanagedate . " 00:00:00' AND mconmanagedate <'" . $mconmanagedate . "  23:59:59'  order by topicture asc";
		$result = $this->query( $sql );
		
		foreach ( $result as $key => $value ){
			if (strstr ( $value ['price'], "-" )){
				$avgprice = explode ( "-", $value ['price'] );
				$value ['price'] = round ( ($avgprice ['0'] + $avgprice ['1']) / 2, 2 );
			}
			$tprice [$value['topicture']] = $value ['price'];
		}
		return $tprice;
	}
	
	//获取今日市场价格
	public function get_marketconditions_price_7($topictures,$mconmanagedate){
		$sql = "select price,mastertopid,mconmanagedate from marketconditions where mastertopid in (" . $topictures . ")  AND mconmanagedate >'" . $mconmanagedate . " 00:00:00' AND mconmanagedate <'" . $mconmanagedate . "  23:59:59'  order by mastertopid asc ";
		$result = $this->query( $sql );
		foreach ( $result as $key => $value ){
			if (strstr ( $value ['price'], "-" )){
				$avgprice = explode ( "-", $value ['price'] );
				$value ['price'] = round ( ($avgprice ['0'] + $avgprice ['1']) / 2, 2 );
			}
			$tprice [$value['mastertopid']] = $value ['price'];
		}
		return $tprice;
	}

	public function getpricebypricestr($pricestr,$date)
	{
		//echo $time;
		$idnumber=explode(',',$pricestr);
		//echo count($idnumber);
		$six=$seven=array();
		foreach($idnumber  as $id ){
			if (strlen ( $id ) == 6){//判断id 的字符长度 
				if(!in_array($id ,$six)){
					$six[]= $id ; 			
				} 							
			} 
			if (strlen ($id ) == 7) {
				if(!in_array($id,$seven)){
					$seven[]= $id;
				} 
			}
		}
		$sixid_str=implode("','",$six);
 		$sevenid_str=implode("','",$seven);
		$mconmanagedate.="(mconmanagedate>='".$date." 00:00:00' and mconmanagedate<='".$date." 23:59:59')";
		if($sixid_str!=''){
			$PriceListSQL=" select marketconditions.pricemk price,marketconditions.topicture, marketconditions.mconmanagedate from marketconditions
 		  	where $mconmanagedate and  marketconditions.topicture in ('$sixid_str')";
 		}
 		if($sevenid_str!=''){
 		  	$PriceListSQL=" select marketconditions.price,marketconditions.mastertopid as topicture, marketconditions.mconmanagedate from marketconditions
 		  	where $mconmanagedate and  marketconditions.mastertopid in ('$sevenid_str')";
 		}
 		if($sixid_str!=''&&$sevenid_str!=''){
	 		$PriceListSQL=" select marketconditions.pricemk price,marketconditions.topicture, marketconditions.mconmanagedate from marketconditions where $mconmanagedate and  marketconditions.topicture in ('$sixid_str') UNION( select marketconditions.price,marketconditions.mastertopid as topicture,marketconditions.mconmanagedate from marketconditions where $mconmanagedate  and marketconditions.mastertopid in('$sevenid_str'))";
 		}
		//echo $PriceListSQL;
		$PriceList= $this->query( $PriceListSQL ); 
		$dataarr =array();//数据数组
		$datearr=array();//日期数组
		foreach($PriceList  as $v )
		{
			if(strstr($v['price'],"-")){
				$avgprice = explode("-",$v['price']);
				$v['price'] = round(($avgprice['0']+$avgprice['1'])/2,2);
			}
			$dataarr[$v['topicture']]=  $v['price'];

		}
		return $dataarr;
	}

	//获取研究中心贴现率
	public function get_drc_hbh_txl($Date){
		$sql = "SELECT * FROM busbankrate WHERE   `rdate` <='$Date' order by rdate desc  limit 1";
		return $this->getRow($sql);
	}
	//获取研究中心汇率
	public function get_drc_rmb_hl($Date){
		$sql = "SELECT * FROM rmbrate WHERE   `rdate` <='$Date' order by rdate desc  limit 1";
		return $this->getRow($sql);
	}
	//获取钢之家税率
	public function get_master_cntaxrate($Date){
		$sql = "SELECT * FROM cntaxrate WHERE   `rdate` <='$Date' and status=1 and 	rtype=1 and isdel=0 order by rdate desc  limit 1";
		return $this->getRow($sql);
	}

}