<?php

$GLOBALS['pinzhong'] = array(
    "1"=>"lw",
    "2"=>"gx",
    "3"=>"pl"
);
$GLOBALS['pinzhong2'] = array(
    "1"=>"螺纹",
    "2"=>"高线",
    "3"=>"盘螺"
);
$GLOBALS['zdjsc'] = array(
    '西安',
    '成都',
    '重庆',
    '郑州',
    '兰州',
    '韩城',
    '汉中'
);
class sgpqtbAction extends AbstractAction
{
    public function __construct()
    {
        parent::__construct();
    }

    public function checkSession()
    {
    }

    public function index($params)
    {
        $GUID = $params['GUID'];
        // $GUID = "6615bc4ac53d11e7b891001aa00de1ab";
        $mode = $params['mode'];//1=客户端，2=安卓，3=iOS
        if($GUID==""){
            $params['tip'] = "GUID不能为空！";
            exit;
        }
        //获取用户信息
        $user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
        $Mid=$user_infos['Mid'];//会员id
        $userid=$user_infos['Uid'];//用户id
        $TrueName=$user_infos['TrueName'];//用户id
        $pqinfo = $this->t1Dao->getRow("select pqid from app_license_privilege where uid='".$userid."' and mc_type=2");

        global $pinzhong,$zdjsc;
        $date = $params['date']?$params['date']:date('Y-m-d');
        $pianqu = $this->_dao->aquery("select pqid,pianqu from sg_PianQu");
        if($pqinfo['pqid']=='0'||$pqinfo['pqid']=="")
        {
            alert("请先设置自己所属的片区！");
            exit;
        }
        $pqid = $pqinfo['pqid'];  //片区id
        $pq = $pianqu[$pqid];  //片区
        $lastdate = date('Y-m-d', strtotime('-14 day', strtotime($date)));
        $sql = "select * from sg_PqxxTiBao where pqid='".$pqid."' and createtime>='$date 00:00:00' and createtime<='$date 23:59:59' and adminid='$userid'";
        $tbs = $this->_dao->query($sql);
        $sql = "select dta_ym,dta_1,dta_2,dta_3,dta_4,round(AVG(dta_6)) dta_6 from sg_data_table where dta_type='SGtjtz' and dta_4 like '$pq%' and dta_ym>='$lastdate' and dta_ym<'$date' group by dta_1,dta_ym";
        $zrprice_arr = $this->_dao->query($sql);
        $sql = "select dta_ym,dta_1,dta_2,dta_3,dta_4,dta_5 from sg_data_table where dta_type='TEST_ERP_XSSJ' and dta_4 like '$pqid' and dta_ym>='$lastdate' and dta_ym<='$date' and dta_1 in ('螺纹钢','高线','盘螺') group by dta_1,dta_ym";
        $zrkdl_arr = $this->_dao->query($sql);
        foreach ($zrprice_arr as $infoprice) {
            $zrprice[$infoprice['dta_ym']][] = $infoprice;
        }
        $yesterday = date('Y-m-d', strtotime('-1 day', strtotime($date)));
        while ($lastdate<$yesterday) {
            foreach ($zrprice as $date_k=>$price) {
                if ($yesterday == $date_k) {
                    foreach ($price as $info) {
                        if ($info['dta_1']=='螺纹钢') {
                            $this->assign('lwzrprice', $info['dta_6']);
                        } elseif ($info['dta_1']=='线材') {
                            $this->assign('gxzrprice', $info['dta_6']);
                        } elseif ($info['dta_1']=='盘螺') {
                            $this->assign('plzrprice', $info['dta_6']);
                        }
                    }
                    break 2;
                }
            }
            $yesterday = date('Y-m-d', strtotime('-1 day', strtotime($yesterday)));
        }

        foreach ($zrkdl_arr as $infokdl) {
            $zrkdl[$infokdl['dta_ym']][] = $infokdl;
        }
        // print_r($zrkdl);
        $yesterday = date('Y-m-d', strtotime('-1 day', strtotime($date)));
        while ($lastdate<$yesterday) {
            foreach ($zrkdl as $date_k=>$kdl) {
                if ($yesterday == $date_k) {
                    foreach ($kdl as $info) {
                        if ($info['dta_1']=='螺纹钢') {
                            $this->assign('lwzrkdl', $info['dta_5']);
                        } elseif ($info['dta_1']=='高线') {
                            $this->assign('gxzrkdl', $info['dta_5']);
                        } elseif ($info['dta_1']=='盘螺') {
                            $this->assign('plzrkdl', $info['dta_5']);
                        }
                    }
                    break 2;
                }
            }
            $yesterday = date('Y-m-d', strtotime('-1 day', strtotime($yesterday)));
        }
        
        $pqid = "select id,jzgc from sg_pqtojzgc where pqid='$pqid'";
        $jzgcs = $this->_dao->aquery($pqid);
        $this->assign('jzgcs', $jzgcs);

        foreach ($tbs as $tb) {
            if ($tb['pinzhong'] == 1) {
                $this->assign('lwid', $tb['id']);
                $this->assign('lwprice', $tb['price']);
                $this->assign('lwjzgc', $tb['jzgc']);
                $this->assign('lwkdl', $tb['kdl']);
                $this->assign('lwzhangdieprice', $tb['tjjy']);
                $this->assign('lwzhangdie', $tb['tjjytype']);
            } elseif ($tb['pinzhong'] == 2) {
                $this->assign('gxid', $tb['id']);
                $this->assign('gxprice', $tb['price']);
                $this->assign('gxjzgc', $tb['jzgc']);
                $this->assign('gxkdl', $tb['kdl']);
                $this->assign('gxzhangdieprice', $tb['tjjy']);
                $this->assign('gxzhangdie', $tb['tjjytype']);
            } elseif ($tb['pinzhong'] == 3) {
                $this->assign('plid', $tb['id']);
                $this->assign('plprice', $tb['price']);
                $this->assign('pljzgc', $tb['jzgc']);
                $this->assign('plkdl', $tb['kdl']);
                $this->assign('plzhangdieprice', $tb['tjjy']);
                $this->assign('plzhangdie', $tb['tjjytype']);
            }
        }

        $this->assign('date', $date);
        $this->assign('pianqu', $pianqu);
        $this->assign('pq', $pq);
        $this->assign('GUID', $GUID);
        $this->assign('mode', $mode);
        $this->assign('truename', $TrueName);

        //指导价列表
        // $sql = "select * from sg_zhidaojia where ndate='$date'";
        // $datArr = $this->_dao->query($sql);
        // $this->assign('datArr', $datArr);

        // $sql = "select * from sg_zhidaojia where pqid='".$pqid."' and ndate='$date'";
        // $datArr2 = $this->_dao->query($sql);
        // $this->assign('datArr2', $datArr2);

        /*$a = array('01'=>'西咸片区','02'=>$pianqu['02'],'03'=>$pianqu['03'],'04'=>$pianqu['04'],'05'=>$pianqu['05'],'06'=>$pianqu['06'],'07'=>$pianqu['07'],'08'=>$pianqu['08'],'09'=>$pianqu['09'],'10'=>$pianqu['10'],'19'=>$pianqu['19'],'20'=>$pianqu['20'],'21'=>$pianqu['21']);
        $aa = array('晋钢','建龙','建邦','立恒','宏达','包钢万腾','酒钢');
        $b = array('13'=>$pianqu['13'],'14'=>$pianqu['14'],'15'=>$pianqu['15'],'16'=>$pianqu['16'],'17'=>$pianqu['17'],'18'=>$pianqu['18']);
        $bb = array('略刚','建龙','立恒','高义','达钢','德胜','威刚');
        $c = array('11'=>$pianqu['11']);
        $cc = array('晋钢','济源','亚新');
        $ins = "insert into sg_pqtojzgc (pqid,pq,jzgc) values";
        $sql = "";
        foreach($c as $kc=>$vc){
            foreach($cc as $vcc){
                $sql.="('$kc','$vc','$vcc'),";
            }
        }
        $insql2 = substr($sql, 0, -1);
        $this->_dao->execute($ins.$insql2);*/
    }

    public function history($params)
    {
        $GUID = $params['GUID'];
        // $GUID = "6615bc4ac53d11e7b891001aa00de1ab";
        $mode = $params['mode'];//1=客户端，2=安卓，3=iOS
        if($GUID==""){
            $params['tip'] = "GUID不能为空！";
            exit;
        }
        //获取用户信息
        $user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
        $Mid=$user_infos['Mid'];//会员id
        $userid=$user_infos['Uid'];//用户id
        $TrueName=$user_infos['TrueName'];//用户id
        $pqinfo = $this->t1Dao->getRow("select pqid from app_license_privilege where uid='".$userid."' and mc_type=2");

        global $pinzhong,$zdjsc;
        $date = $params['date']?$params['date']:date('Y-m-d');
        $pianqu = $this->_dao->aquery("select pqid,pianqu from sg_PianQu");
        // if($pqinfo['pqid']=='0'||$pqinfo['pqid']=="")
        // {
        //     alert("请先设置自己所属的片区！");
        //     exit;
        // }
        $adminid = $params['adminid'];
        $pqid = $pqinfo['pqid'];  //片区id
        $pq = $pianqu[$pqid];  //片区
        // $lastdate = date('Y-m-d', strtotime('-14 day', strtotime($date)));
        $sql = "select * from sg_PqxxTiBao where pqid='".$pqid."' and createtime>='$date 00:00:00' and createtime<='$date 23:59:59' and adminid='$adminid'";
        // $sql = "select * from sg_PqxxTiBao where pqid='".$pqid."' and createtime>='$date 00:00:00' and createtime<='$date 23:59:59' and adminid='$adminid'";
        $tbs = $this->_dao->query($sql);
        // $sql = "select dta_ym,dta_1,dta_2,dta_3,dta_4,round(AVG(dta_6)) dta_6 from sg_data_table where dta_type='SGtjtz' and dta_4 like '$pq%' and dta_ym>='$lastdate' and dta_ym<'$date' group by dta_1,dta_ym";
        // $zrprice_arr = $this->_dao->query($sql);
        // $sql = "select dta_ym,dta_1,dta_2,dta_3,dta_4,dta_5 from sg_data_table where dta_type='TEST_ERP_XSSJ' and dta_4 like '$pqid' and dta_ym>='$lastdate' and dta_ym<='$date' and dta_1 in ('螺纹钢','高线','盘螺') group by dta_1,dta_ym";
        // $zrkdl_arr = $this->_dao->query($sql);
        // foreach ($zrprice_arr as $infoprice) {
        //     $zrprice[$infoprice['dta_ym']][] = $infoprice;
        // }
        // $yesterday = date('Y-m-d', strtotime('-1 day', strtotime($date)));
        // while ($lastdate<$yesterday) {
        //     foreach ($zrprice as $date_k=>$price) {
        //         if ($yesterday == $date_k) {
        //             foreach ($price as $info) {
        //                 if ($info['dta_1']=='螺纹钢') {
        //                     $this->assign('lwzrprice', $info['dta_6']);
        //                 } elseif ($info['dta_1']=='线材') {
        //                     $this->assign('gxzrprice', $info['dta_6']);
        //                 } elseif ($info['dta_1']=='盘螺') {
        //                     $this->assign('plzrprice', $info['dta_6']);
        //                 }
        //             }
        //             break 2;
        //         }
        //     }
        //     $yesterday = date('Y-m-d', strtotime('-1 day', strtotime($yesterday)));
        // }

        // foreach ($zrkdl_arr as $infokdl) {
        //     $zrkdl[$infokdl['dta_ym']][] = $infokdl;
        // }
        // print_r($zrkdl);
        // $yesterday = date('Y-m-d', strtotime('-1 day', strtotime($date)));
        // while ($lastdate<$yesterday) {
        //     foreach ($zrkdl as $date_k=>$kdl) {
        //         if ($yesterday == $date_k) {
        //             foreach ($kdl as $info) {
        //                 if ($info['dta_1']=='螺纹钢') {
        //                     $this->assign('lwzrkdl', $info['dta_5']);
        //                 } elseif ($info['dta_1']=='高线') {
        //                     $this->assign('gxzrkdl', $info['dta_5']);
        //                 } elseif ($info['dta_1']=='盘螺') {
        //                     $this->assign('plzrkdl', $info['dta_5']);
        //                 }
        //             }
        //             break 2;
        //         }
        //     }
        //     $yesterday = date('Y-m-d', strtotime('-1 day', strtotime($yesterday)));
        // }
        
        // $pqid = "select id,jzgc from sg_pqtojzgc where pqid='$pqid'";
        // $jzgcs = $this->_dao->aquery($pqid);
        // $this->assign('jzgcs', $jzgcs);

        foreach ($tbs as $tb) {
            if ($tb['pinzhong'] == 1) {
                $this->assign('lwid', $tb['id']);
                $this->assign('lwprice', $tb['price']);
                $this->assign('lwjzgc', $tb['jzgc']);
                $this->assign('lwkdl', $tb['kdl']);
                $this->assign('lwzhangdieprice', $tb['tjjy']);
                $this->assign('lwzhangdie', $tb['tjjytype']);
                $this->assign('lwzrprice', $tb['zrprice']);
                $this->assign('lwzrkdl', $tb['zrkdl']);
                $this->assign('lwzjkdl', $tb['kdlzj']);
                $this->assign('lwzdprice', $tb['pricezd']);
            } elseif ($tb['pinzhong'] == 2) {
                $this->assign('gxid', $tb['id']);
                $this->assign('gxprice', $tb['price']);
                $this->assign('gxjzgc', $tb['jzgc']);
                $this->assign('gxkdl', $tb['kdl']);
                $this->assign('gxzhangdieprice', $tb['tjjy']);
                $this->assign('gxzhangdie', $tb['tjjytype']);
                $this->assign('gxzrprice', $tb['zrprice']);
                $this->assign('gxzrkdl', $tb['zrkdl']);
                $this->assign('gxzjkdl', $tb['kdlzj']);
                $this->assign('gxzdprice', $tb['pricezd']);
            } elseif ($tb['pinzhong'] == 3) {
                $this->assign('plid', $tb['id']);
                $this->assign('plprice', $tb['price']);
                $this->assign('pljzgc', $tb['jzgc']);
                $this->assign('plkdl', $tb['kdl']);
                $this->assign('plzhangdieprice', $tb['tjjy']);
                $this->assign('plzhangdie', $tb['tjjytype']);
                $this->assign('plzrprice', $tb['zrprice']);
                $this->assign('plzrkdl', $tb['zrkdl']);
                $this->assign('plzjkdl', $tb['kdlzj']);
                $this->assign('plzdprice', $tb['pricezd']);
            }
        }

        $this->assign('date', $date);
        $this->assign('pianqu', $pianqu);
        $this->assign('pq', $pq);
        $this->assign('GUID', $GUID);
        $this->assign('mode', $mode);
        $this->assign('truename', $TrueName);
    }

    public function history1($params)
    {
        $GUID = $params['GUID'];
        $mode = $params['mode'];//1=客户端，2=安卓，3=iOS
        if($GUID==""){
            $params['tip'] = "GUID不能为空！";
            exit;
        }
        $user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
        $Mid=$user_infos['Mid'];//会员id
        $userid=$user_infos['Uid'];//用户id
        $TrueName=$user_infos['TrueName'];//用户id
        // $qxinfo = $this->t1Dao->get_license_privilege($userid);  //获取权限信息
        $pqinfo = $this->t1Dao->getRow("select pqid from app_license_privilege where uid='".$userid."' and mc_type=2");
        $date = $params['date']?$params['date']:date('Y-m-d');
        $pianqu = $this->_dao->aquery("select pqid,pianqu from sg_PianQu");
        if($pqinfo['pqid']=='0'||$pqinfo['pqid']=="")
        {
            alert("请先设置自己所属的片区！");
            exit;
        }
        $pqid = $pqinfo['pqid'];  //片区id
        $pq = $pianqu[$pqid];  //片区
        $power = 0;
        if ($power) {  // 超管
            $where = "";
        } else {
            $where = " and pqid='".$pqid."'";
        }
        $total = $this->_dao->getOne( "select count(*) from (select * from (select DATE_FORMAT(createTime,'%Y-%m-%d') createdate,pqid,adminId from sg_PqxxTiBao where 1 $where) t group by createdate,pqid,adminId) t2");
        $page = $params['page'] == '' ? 1 : $params['page'];
        $url = "sgpqtb.php";
        $per = 20;
        $start = ( $page - 1 ) * $per;
        unset( $params['page'] );
        $pagebar = pagebar( $url, $params, $per, $page, $total );
        $this->assign( "pagebar", $pagebar );
        $sql = "select * from (select DATE_FORMAT(createTime,'%Y-%m-%d') createdate,pqid,adminId from sg_PqxxTiBao where 1 $where) t group by createdate,pqid,adminId order by createdate desc limit $start,$per";
        $tbdata = $this->_dao->query($sql);
        $admin = array();
        foreach ($tbdata as $va) {
            if (!in_array($va[adminId], $admin)) {
                $admin[] = $va[adminId];
            }
        }
        $ids = implode("','", $admin);
        $sql = "select id,trueName from adminuser where id in ('$ids')";
        $users = $this->homeDao->aquery($sql);
        $this->assign('tbdata', $tbdata);
        $this->assign('pianqu', $pianqu);
        $this->assign('users', $users);
        $this->assign('GUID', $GUID);
        $this->assign('mode', $mode);
    }

    public function save($params)
    {
        global $pinzhong;
        // print_r($params);
        // exit;
        $GUID = $params['GUID'];
        $mode = $params['mode'];//1=客户端，2=安卓，3=iOS
        if($GUID==""){
            $params['tip'] = "GUID不能为空！";exit;
        }
        if(date('H:i:s') > "17:30:00") {
            alert("当日17:30之后无法修改！");
            goURL("sgpqtb.php?view=index&GUID=".$GUID."&mode=".$mode);
            exit;
        }
        //获取用户信息
        $user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
        $Mid=$user_infos['Mid'];//会员id
        $userid=$user_infos['Uid'];//用户id
        $TrueName=$user_infos['TrueName'];//用户id
        $pqinfo = $this->t1Dao->getRow("select pqid from app_license_privilege where uid='".$userid."' and mc_type=2");
        $pianqu = $this->_dao->aquery("select pqid,pianqu from sg_PianQu");
        $pqid = $pqinfo['pqid'];  //片区id
        $pq = $pianqu[$pqid];  //片区
        $TrueName=$user_infos['TrueName'];//用户id
        // print_r($params);
        // exit;
        $insql = "insert into sg_PqxxTiBao(pqid,pinzhong,kdl,zrkdl,kdlzj,jzgc,price,zrprice,pricezd,tjjytype,tjjy,adminId,createtime) values";
        $gc = array();
        foreach($pinzhong as $k1=>$v1) {
            if($params[$v1.'jzgc']==""||$params[$v1.'jzgc']=="0") {
                $jzgc = "";
            } elseif ($params[$v1.'jzgc']=="1") {  //新增钢厂
                $jzgc = mysql_real_escape_string($params[$v1.'jzgc_xz']);
                if (!in_array($jzgc, $gc)) {
                    $gc[] = $jzgc;
                }
            } else {
                $jzgc = mysql_real_escape_string($params[$v1.'jzgc']);
            }
            // echo $jzgc;
            if(empty($params[$v1.'id'])) {
                 //没有数据的时候插入数据 
                // if((!empty($params['kdl'.$k1]))||(!empty($params['jzgccjjg'.$k1]))||(!empty($params['scqk'.$k1]))||(!empty($params['tjjy'.$k1])))
                // {
                $insql2 .= "('".$pqid."','$k1','".$params[$v1.'kdl']."','".$params[$v1.'zrkdl']."','".$params[$v1.'zjkdl']."','$jzgc','".$params[$v1.'price']."','".$params[$v1.'zrprice']."','".$params[$v1.'zdprice']."','".$params[$v1.'zhangdie']."','".$params[$v1.'zhangdieprice']."','".$userid."',NOW()),";
                // }
            } else {
                //有数据的时候更新数据
                $upsql = "update sg_PqxxTiBao set kdl='".$params[$v1.'kdl']."',kdlzj='".$params[$v1.'zjkdl']."',jzgc='$jzgc',price='".$params[$v1.'price']."',pricezd='".$params[$v1.'zdprice']."',tjjytype='".$params[$v1.'zhangdie']."',tjjy='".$params[$v1.'zhangdieprice']."',updateTime=NOW(),adminId='".$userid."' where id='".$params[$v1.'id']."'";
                // echo $upsql;exit;
                $this->_dao->execute($upsql);
            }
        }
        if($insql2)
        {
            $insql2 = substr($insql2, 0, -1);
            $this->_dao->execute($insql.$insql2);
            $gcbase = "insert into sg_pqtojzgc(pqid,pq,jzgc,createuserid,createtime) ";
            $gcsql = "";
            foreach ($gc as $jzgc2) {
                $gcsql .= "values('$pqid', '$pq', '$jzgc2','$userid',NOW()),";
            }
            if($gcsql!="") {
                $this->_dao->execute($gcbase.substr($gcsql, 0, -1));
            }
        }
        alert("保存成功");
        goURL("sgpqtb.php?view=index&GUID=".$GUID."&mode=".$mode);
    }

    //指导价录入保存
    public function save2($params)
    {
        global $pinzhong,$zdjsc;
        $GUID = $params['GUID'];
        $mode = $params['mode'];//1=客户端，2=安卓，3=iOS
        if($GUID==""){
            $params['tip'] = "GUID不能为空！";exit;
        }
        //获取用户信息
        $user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
        $Mid=$user_infos['Mid'];//会员id
        $userid=$user_infos['Uid'];//用户id

        $insql = "insert into sg_zhidaojia_dr(pinzhong,cityname,price,ndate,adminId,createtime) values";
        for($i=0;$i<count($params['id']);$i++)
        {
            if(empty($params['id'][$i])&&(!empty($params['price'][$i])))
            {
                $insql2 .= "('".mysql_real_escape_string($params['pinzhong'][$i])."','".mysql_real_escape_string($params['cityname'][$i])."','".mysql_real_escape_string($params['price'][$i])."','".mysql_real_escape_string($params['ndate'][$i])."','".$userid."',NOW()),";
            }
            else if($params['id'][$i])
            {
                $upsql = "update sg_zhidaojia_dr set price='".mysql_real_escape_string($params['price'][$i])."',ndate='".mysql_real_escape_string($params['ndate'][$i])."',updateTime=NOW() where id='".mysql_real_escape_string($params['id'][$i])."'";
                $this->_dao->execute($upsql);
            }
        }

        if($insql2)
        {
            $insql = $insql.substr($insql2, 0, -1);
            $this->_dao->execute($insql);
        }
        alert("保存成功");
        goURL("sgpqtb.php?view=zdj_lr&GUID=".$GUID."&mode=".$mode);
    }

    function saveOne($params)
    {
        $GUID = $params['GUID'];
        $mode = $params['mode'];//1=客户端，2=安卓，3=iOS
        if($GUID==""){
            $params['tip'] = "GUID不能为空！";exit;
        }
        //获取用户信息
        $user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
        $Mid=$user_infos['Mid'];//会员id
        $userid=$user_infos['Uid'];//用户id
        $TrueName=$user_infos['TrueName'];//用户id
        if($params['id']&&$params['id']!='0')
        {
            $upsql = "update sg_zhidaojia_dr set price='".mysql_real_escape_string($params['price'])."',ndate='".mysql_real_escape_string($params['ndate'])."',updateTime=NOW() where id='".mysql_real_escape_string($params['id'])."'";
            $this->_dao->execute($upsql);
            echo 1;
        }
        else
        {
            $upsql = "insert into sg_zhidaojia_dr set pinzhong='".mysql_real_escape_string($params['pz'])."', price='".mysql_real_escape_string($params['price'])."',ndate='".mysql_real_escape_string($params['ndate'])."', cityname='".mysql_real_escape_string(mb_convert_encoding($params['cityname'], 'gb2312', 'utf-8'))."', createTime=NOW(), adminId='".$userid."'";
            $this->_dao->execute($upsql);
            echo 2;
        }
        exit;
    }

    function zdj_lr($params)
    {
        global $pinzhong2,$pinzhong,$zdjsc;
        $GUID = $params['GUID'];
        $mode = $params['mode'];//1=客户端，2=安卓，3=iOS
        if($GUID==""){
            $params['tip'] = "GUID不能为空！";exit;
        }
        //获取用户信息
        $user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
        $Mid=$user_infos['Mid'];//会员id
        $userid=$user_infos['Uid'];//用户id
        $TrueName=$user_infos['TrueName'];//用户id
        $pqinfo = $this->t1Dao->getRow("select pqid from app_license_privilege where uid='".$userid."' and mc_type=2");

        $date = $params['date']?$params['date']:date('Y-m-d');
        $pianqu = $this->_dao->aquery("select pqid,pianqu from sg_PianQu");
        $pqid = $pqinfo['pqid'];  //片区id
        $pq = $pianqu[$pqinfo['pqid']];  //片区
        if($date!=date('Y-m-d')){
            $sta = 0;
        }else{
            $sta = 1;
        }
        $this->assign('date', $date);
        $this->assign('pianqu', $pianqu);
        $this->assign('pinzhong', $pinzhong2);
        $this->assign('pq', $pq);
        $this->assign('sta', $sta);
        $this->assign('GUID', $GUID);
        $this->assign('today', date('Y-m-d'));
        $sql = "select * from sg_zhidaojia_dr where ndate like '%$date%'";
        $zdj = $this->_dao->query($sql);
        
        foreach($pinzhong as $pz_k=>$p_zv)
        {
            foreach($zdjsc as $zdjsc_k => $zdjsc_v)
            {
                $new_zdj[$pz_k][$zdjsc_v] = array();
            }
            foreach($zdj as $zdj_v)
            {
                if($pz_k==$zdj_v['pinzhong'])
                {
                    $new_zdj[$pz_k][$zdj_v['cityname']]['price'] = $zdj_v['price'];
                    $new_zdj[$pz_k][$zdj_v['cityname']]['ndate'] = $zdj_v['ndate'];
                    $new_zdj[$pz_k][$zdj_v['cityname']]['id'] = $zdj_v['id'];
                }
            }
        }

        $this->assign('new_zdj', $new_zdj);
        $this->assign('GUID', $GUID);
        $this->assign('mode', $mode);
    }

    function zdjdr($params)
    {
        global $pinzhong;
        $date = $params['date'];
        if(empty($date))
        {
            exit;
        }
        $pianqu = $this->_dao->aquery("select pqid,pianqu from sg_PianQu");
        $sql = "select * from gc_price_model_detail where modelpirce_name like '西安%' and ndate='$date' and mc_type=2 and Ismakepricy_men=1";
        $model_arr = $this->_dao->query($sql);
        // print_r($model_arr);
        
        foreach($model_arr as $v_l)
        {
            $modelprice_arr[$v_l['pingzhong']]['modelprice'] = $v_l['modelprice'];
            $modelprice_arr[$v_l['pingzhong']]['modelprice_name'] = $v_l['modelpirce_name'];
            $modelprice_arr[$v_l['pingzhong']]['uid'] = $v_l['uid'];
            $modelprice_arr[$v_l['pingzhong']]['ndate'] = $v_l['ndate'];
        }
        // print_r($modelprice_arr);
        $insql = "insert into sg_zhidaojia(pinzhong,modelprice,modelprice_name,jiacha,price,pqid,adminId,ndate,createTime) values ";
        foreach($pianqu as $pqid=>$pianqu)
        {
            foreach($pinzhong as $pzid=>$pz)
            {
                $inser2 .= "('$pzid','".$modelprice_arr[$pzid]['modelprice']."','".$modelprice_arr[$pzid]['modelprice_name']."','10','".($modelprice_arr[$pzid]['modelprice']+10)."','$pqid','".$modelprice_arr[$pzid]['uid']."','".$modelprice_arr[$pzid]['ndate']."',NOW()),";
            }
        }

        $insert = $insql.substr($inser2,0,-1);
        $this->_dao->execute($insert);
        // echo $insert;
    }

    function drPqxxTiBao($params){
        $pianqu = $this->_dao->aquery("select pqid,pianqu from sg_PianQu");
        foreach($pianqu as $pqid=>$pq)
        {
            $num1 = substr(mt_rand(3000, 5200),0,3)*10;
            $num2 = substr(mt_rand(3000, 5200),0,3)*10;
            $num3 = substr(mt_rand(3000, 5200),0,3)*10;

            $num1_1 = substr(mt_rand(7000, 10000),0,3)*10;
            $num2_1 = substr(mt_rand(7000, 10000),0,3)*10;
            $num3_1 = substr(mt_rand(7000, 10000),0,3)*10;
            $sql = "insert into sg_PqxxTiBao(pqid,pinzhong,kdl,jzgccjjg,scqk,tjjy,adminId,createtime) values('$pqid','1','$num1_1','$num1','','','391935',NOW()),('$pqid','2','$num2_1','$num2','','','391935',NOW()),('$pqid','3','$num3_1','$num3','','','391935',NOW())";
            $this->_dao->execute($sql);
        }
    }

    function drZhidaojia_dr($params){

        $sql = "select * from sg_zhidaojia_dr where ndate like '%$date%'";
        $zdj = $this->_dao->query($sql);
        if(!empty($zdj))
        {
            exit;
        }

        $sql = "insert into sg_zhidaojia_dr(pinzhong,cityname,price,ndate,adminId,createtime) values('1','西安','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('1','成都','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('1','重庆','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('1','郑州','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('1','兰州','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('2','西安','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('2','成都','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('2','重庆','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('2','郑州','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('2','兰州','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('3','西安','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('3','成都','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('3','重庆','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('3','郑州','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('3','兰州','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW())";
        // echo $sql."<br><br>";
        $this->_dao->execute($sql);
    }

    public function random($length)
    {
        $hash = '';
        $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789abcdefghijklmnopqrstuvwxyz';
        $max = strlen($chars) - 1;
        mt_srand(( double ) microtime() * 1000000);
        for ($i = 0; $i < $length; $i ++) {
            $hash .= $chars [mt_rand(0, $max)];
        }
        return $hash;
    }
    
    function vmrkcUpload($params)
    {
        $year = date('Y');
        $years = array();
        for($i=0;$i<=30;$i++)
        {
            $years[] = $year-$i;
        }
        $this->assign('years', $years);
        $GUID = $params['GUID'];
        $mode = $params['mode'];//1=客户端，2=安卓，3=iOS
        if($GUID==""){
            alert('GUID不能为空');
            exit;
        }
        $user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
        if(empty($user_infos)){
            alert('用户不存在');
            exit;
        }
        $this->assign('guid', $GUID);
        $this->assign('mode', $mode);

        $uptype = 1;
        $total = $this->_dao->getOne( "SELECT COUNT(id) as c FROM sg_data_table_log where uptype = '$uptype' and isdel='0'");
        $page = $params['page'] == '' ? 1 : $params['page'];
        $url = "sgpqtb.php";
        $per = 25;
        $start = ( $page - 1 ) * $per;
        unset( $params['page'] );
        $pagebar = pagebar( $url, $params, $per, $page, $total );
        $this->assign( "pagebar", $pagebar );
        $sql = "select * from sg_data_table_log where uptype = '$uptype' and isdel='0' order by createtime desc limit $start, $per";
        $log_info = $this->_dao->query($sql);
        $createuser = array();
        foreach( $log_info as $v ){
            if(!in_array($v['createuser'],$createuser)){
                $createuser[] = $v['createuser'];
            }
        }
        $createuser_str =  implode("','",$createuser);
        $sql = "select id,truename from adminuser where id in ('$createuser_str')";
        $adminuser_info = $this->homeDao->query($sql);
        $admin_name = array();
        foreach( $adminuser_info as $v ){
            $admin_name[$v['id']] = $v['truename'];
        }
        $this->assign('log_info', $log_info);
        $this->assign('admin_name', $admin_name);
    }

    function readSheet($file, $year)
    {
        require_once "../PHPExcel/PHPExcel.php";
        $type = pathinfo($file); 
        $type = strtolower($type["extension"]);
        if ($type=='xlsx') { 
            $type='Excel2007'; 
        }elseif($type=='xls') { 
            $type = 'Excel5';
        }
        
        $objReader = PHPExcel_IOFactory::createReader($type);//判断使用哪种格式
        $objReader ->setReadDataOnly(true); //只读取数据,会智能忽略所有空白行,这点很重要！！！
        $objPHPExcel = $objReader->load($file); //加载Excel文件
        $cnt = 0;
        foreach ($objPHPExcel->getWorksheetIterator() as $sheet) {
            if ($sheet->getSheetState() === 'hidden') {
                $hiddenSheet[$cnt] = $sheet->getTitle();
            }else{
                $sheetName[$cnt] = $sheet->getTitle();
            }
            $cnt++;
        }
        return $sheetName;
    }

    function sheet($params)
    {
        $GUID = $params['GUID'];
        $mode = $params['mode'];//1=客户端，2=安卓，3=iOS
        $this->assign('mode', $mode);
        $response = array(
            'Success'  => 1,
            'Message'  => '',
            'GUID'     => $GUID,
            'mode'     => $mode,
        );
        if($GUID==""){
            $response['Success'] = 0;
            $response['Message'] = 'GUID不能为空';
        }
        if($response['Success']){
            $year = $params['year'];
            $upfile = $_FILES['file'];
            if(strpos($upfile["name"], $year)===false)
            {
                // $response['Success'] = 0;
                $response['Message'] = '选取年份与文件数据年份不一致！';
            }
            $user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
            if(empty($user_infos)){
                $response['Success'] = 0;
                $response['Message'] = '用户不存在';
            }
            // $sheet = $this->readSheet("/usr/local/www/www.steelhome.cn/data/v1.5/uploadfile/mrkc/SG1-uQuMx20200904112834.xls");
            // $response['Result'] = $this->array_iconv($sheets11);
            // print_r($sheet);
            // $response['Result'] = $sheets;
            // $response['Result222'] = array(1,2);
            if($response['Success']){
                if ($upfile['error']==0)
                {
                    if (is_uploaded_file($upfile['tmp_name'])) {
                        $uptypes=array(
                            'application/vnd.ms-excel',
                            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                        );
                        if (in_array($upfile['type'], $uptypes)) {
                            $extName = strtolower(end(explode('.', $upfile ['name'])));
                            $filename = "SG1-".$this->random(5); // 设置随机数长度
                            $extName=strtolower(end(explode('.', $upfile['name']))); // 取得扩展名
                            $name1=date('YmdHis').".".$extName;
                            $dir = UPLOADFILE;
                            if (!file_exists($dir)) {
                                if (mkdir($dir, 0777)) {
                                    $dest =$dir."/".$filename.$name1;
                                    if (move_uploaded_file($upfile ['tmp_name'], $dest)) {
                                        $response['Success'] = 1;
                                        $response['Message'] = '上传成功';
                                        $sheet = $this->readSheet($dest);
                                        $response['Result'] = $this->array_iconv($sheet);
                                        $response['File'] = $filename.$name1;
                                        $response['FileName'] = mb_convert_encoding($upfile['name'],"GB2312","UTF-8");
                                        $response['Year'] = $year;
                                    } else {
                                        $response['Success'] = 0;
                                        $response['Message'] = '上传失败！目录权限不足';
                                    }
                                } else {
                                    $response['Success'] = 0;
                                    $response['Message'] = '目录不存在，上传失败！';
                                }
                            } else {
                                $dest = $dir."/".$filename.$name1;
                                if (move_uploaded_file($upfile ['tmp_name'], $dest)) {
                                    $response['Success'] = 1;
                                    $response['Message'] = '上传成功';
                                    $sheet = $this->readSheet($dest);
                                    $response['Result'] = $this->array_iconv($sheet);
                                    $response['File'] = $filename.$name1;
                                    $response['FileName'] = mb_convert_encoding($upfile['name'],"GB2312","UTF-8");
                                    $response['Year'] = $year;
                                } else {
                                    $response['Success'] = 0;
                                    $response['Message'] = '上传失败！目录权限不足';
                                }
                            }
                        } else {
                            $response['Success'] = 0;
                            $response['Message'] = '上传失败！检查文件是否是Excel文件';
                        }
                    } else {
                        clearstatcache(); //清除文件缓存信息
                        $response['Success'] = 0;
                        $response['Message'] = '上传失败！';
                    }
                }
                else
                {
                    alert('上传失败！');
                    goURL("sgpqtb.php?view=mrkcupload&GUID=".$GUID."&mode=".$mode);
                    exit;
                }
            }
        }
        $json_string = $this->pri_JSON($response);
        echo $json_string;
        exit;
    }

    function dosheet($params)
    {
        $GUID = $params['GUID'];
        $filename = mb_convert_encoding($params["filename"],"GB2312","UTF-8");
        $mode = $params['mode'];//1=客户端，2=安卓，3=iOS
        $response = array(
            'Success'  => 1,
            'Message'  => '',
            // 'GUID'     => $GUID,
            // 'mode'     => $mode,
        );
        if($GUID==""){
            $response["Success"] = 0;
            $response['Message'] = 'GUID不能为空';
        }
        if($response["Success"])
        {
            $user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
            if(empty($user_infos)){
                $response["Success"] = 0;
                $response['Message'] = '用户不存在';
            }

            $sheetList = explode(',', $params["sheet"]);
            $file = UPLOADFILE."/".$params["tmp"];
            if(empty($sheetList[0])&&$sheetList[0]!=0)
            {
                $response["Success"] = 0;
                $response['Message'] = '未选择工作表';
            }
            $year = mysql_real_escape_string($params["year"]);
            if(empty($year))
            {
                $response["Success"] = 0;
                $response['Message'] = '年份出错';
            }
            if($response["Success"])
            {
                require_once "../PHPExcel/PHPExcel.php";
                $type = pathinfo($file); 
                $type = strtolower($type["extension"]);
                if ($type=='xlsx') { 
                    $type='Excel2007'; 
                }elseif($type=='xls') { 
                    $type = 'Excel5';
                }
                $objReader = PHPExcel_IOFactory::createReader($type);//判断使用哪种格式
                $objReader ->setReadDataOnly(true); //只读取数据,会智能忽略所有空白行,这点很重要！！！
                $objPHPExcel = $objReader->load($file); //加载Excel文件
                $sheets = $objPHPExcel->getSheetNames();
                $sheetCount = $objPHPExcel->getSheetCount();//获取sheet工作表总个数
        
                $flag = true;
                $tmpsheetName = array();
                foreach($sheetList as $sheet_index1)
                {
                    
                    $sheet_index = $sheetCount-1-$sheet_index1;
                    // $sheet = $objPHPExcel->setActiveSheetIndexByName($sheet_v);
                    $sheet = $objPHPExcel->getSheet($sheet_index);
                    // $sheet_array = $objPHPExcel->getSheet($sheet_index)->toArray();
                    //得到当前工作表对象
                    // $curSheet = $objPHPExcel->getActiveSheet();
                    //获取当前工作表最大行数
                    $rows = $sheet->getHighestRow();
                    //获取当前工作表最大列数,返回的是最大的列名，如：B 
                    // $cols = $sheet->getHighestColumn();
                    // print_r($cols);
                    //将当前工作表名当键，内容为值存入数组
                    $sheet_v = $sheets[$sheet_index];
                    $tmpsheetName[] = mb_convert_encoding($sheet_v,"GB2312","UTF-8");
                    $data = array();
                    $listA = array();
                    for($k = 3; $k <= $rows; $k++){
                        $key = "A".$k;
                        $listA[$k] = iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getValue());
                        if(empty($listA[$k]))
                        {
                            unset($listA[$k]);
                        }
            
                        if(mb_strlen($listA[$k])>10)
                        {
                            unset($listA[$k]);
                        }
                    }
                    // print_r($listA);
                    $t = array_keys($listA);
                    //大写字母A的ASCII值是65 A-Z对应65-90
                    for($j = 'B'; $j <= 'J'; $j++ ){
                        for($k = 3; $k <= $rows; $k++){
                            $key = $j.$k;
                            // $data[$sheet_v][$j.$k] = iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue());
                            // $data[$sheet_v][$key] = iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getValue());
                            $info = iconv("utf-8","gb2312//IGNORE", $sheet->getCell("B".$k)->getValue());
                            if(!empty($info))
                            {
                                $data[$k][$j] = iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue());
                                for($i=0;$i<(count($t)-1);$i++)
                                {
                                    if(!empty($t[$i+1]))
                                    {
                                        if($k>=$t[$i] && $k<$t[$i+1])
                                        {
                                            // $this->getFirstCharter("");
                                            $data[$k]['Z'] = "SGLG";
                                        }
                                        if($k>=$t[$i+1])
                                        {
                                            $data[$k]['Z'] = "SGHG";
                                        }
                                    }
                                }
                            }
                                        
                        }
                    }
                    $regex="'\d{1,2}月\d{1,2}日'is";
                    $A1 = iconv("utf-8","gb2312//IGNORE", $sheet->getCell('A1')->getValue());
                    preg_match($regex,$A1,$matches);
                    if(empty($matches[0]))
                    {
                        $matches = array();
                        $regex="'元月\d{1,2}日'is";
                        preg_match($regex,$A1,$matches);
                    }
            
                    if(!empty($matches[0]))
                    {
                        $str = str_replace('元', '1', $matches[0]);
                        $ml = explode('月', $str);
                        // $m = $ml[0]<10 ? "0".$ml[0] : $ml[0];
                        $m = $ml[0];
                        if(!empty($ml[1]))
                        {
                            $dl = explode('日',$ml[1]);
                            // $d = $dl[0]<10 ? "0".$dl[0] : $dl[0];
                            $d = $dl[0];
                        }
                        else
                        {
                            // alert("工作表：".$sheet_v." A1单元格日期格式有误，请检查！");
                            // exit;
                            $error_sheet1[] = $sheet_v;
                            $flag = false;
                            continue;
                        }
                    }
                    else
                    {
                        $error_sheet1[] = $sheet_v;
                        $flag = false;
                        continue;
                    }
            
                    $type = array(
                        "进口矿"=>"jkk",
                        "国内矿"=>"gnk",
                        "焦炭"=>"jt",
                        "焦沫"=>"jm",
                        "喷煤"=>"pm",
                        "废钢"=>"fg",
                        "生铁"=>"st",
                        "硅锰"=>"gm",
                        "硅铁"=>"gt",
                        "钒氮"=>"fd",
                        "铌铁"=>"nt",
                    );
                    
                    if($sheet_index < 10 && $m == 12) //前几个sheet可能存在上年的数据
                    {
                        $tmpyear = $year - 1;
                    }
                    else
                    {
                        $tmpyear = $year;
                    }
                    $date = date("Y-m-d", strtotime($tmpyear."-".$m."-".$d));
                    // $c = $this->_dao->getone("select count(*) c from sg_data_table where dta_ym='$date' and dta_9 in ('SGHG','SGLG')");
                    // if($c>0)
                    // {
                    // 	// alert("已有数据");
                    // }

                    $this->_dao->execute("delete from sg_data_table where dta_ym='$date' and dta_9 in ('SGHG','SGLG')");
                    $basesql = "insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,dta_3,dta_4,dta_5,dta_6,dta_7,dta_8,dta_9,createtime,createuser) values";
                    $valSql = "";
                    foreach($data as $rows=>$columns)
                    {
                        $dta_type = $columns['Z'].$type[$columns['B']];
                        $dta_vartype = $columns['B'];
                        $dta_1 = $columns['C'];
                        $dta_2 = $columns['D'];
                        $dta_3 = $columns['E'];
                        if($type[$columns['B']]=='jkk')
                        {
                            // $dta_4 = sprintf("%.1f", ($columns['C']+$columns['D']+$columns['E']));
                            $dta_4 = sprintf("%.1f", $columns['F']);
                            $d4_arr = explode('.', $dta_4);
                            if($d4_arr[1]=='0')
                            {
                                $dta_4 = $d4_arr[0];
                            }
                        }
                        else
                        {
                            $dta_4 = $columns['F'];
                        }
                        // $dta_4 = round($columns['F'], 1);
                        $dta_5 = $columns['G'];
                        // $dta_6 = round($dta_4/$dta_5);
                        $dta_6 = round($columns['H']);
                        $dta_7 = $columns['I'];
                        $dta_8 = mysql_real_escape_string($columns['J']); 
                        $dta_9 = $columns['Z'];
                        if($dta_type!="SGHG"&&$dta_type!="SGLG")
                        {
                            $valSql.="('$dta_type','$dta_vartype','$date','$dta_1','$dta_2','$dta_3','$dta_4','$dta_5','$dta_6','$dta_7','$dta_8','$dta_9',NOW(),'".$user_infos['Uid']."'),";
                        }
                        else
                        {
                            if(!empty($columns['B'])&&!in_array($columns['B'], $wlrList))
                            {
                                $wlrList[] = $columns['B'];  //表格未录入数据
                            }
                        }
                    }
                    
                    $insertsql = substr($basesql.$valSql, 0, -1);
            
                    if(!empty($valSql))
                    {
                        $this->_dao->execute($insertsql);
                        // echo $insertsql."<br><br>";
                    }else{
                        // 失败
                        $error_sheet2[] = $sheet_v;
                        $flag = false;
                    }
                }
                if(!$flag)
                {
                    $error1 = implode(',', $error_sheet1);
                    $error2 = implode(',', $error_sheet2);
                    if(!empty($error1))
                    {
                        $msg1 = "工作表：".$error1."的A1单元格日期格式有误，导入失败";
                    }
                    if(!empty($error2))
                    {
                        $msg2 = "工作表：".$error2."出现未知错误，导入失败";
                    }
                    $response["Success"] = 0;
                    $response['Message'] = $msg1."\\n".$msg2;
                }else{
                    $sheetName = implode(',',$tmpsheetName);
                    $this->_dao->execute("insert into sg_data_table_log(uptype,filepath,filename,sheetname,createtime,createuser,uptypedate) value('1','$file','$filename','$sheetName',NOW(),'".$user_infos['Uid']."','$date')");
                    $response["Success"] = 1;
                    $response['Message'] = '导入成功';
                }
            }
        }
        echo $this->pri_JSON($response);
        exit;
    }

    //php获取中文字符拼音首字母
    function getFirstCharter($str){
        if(empty($str)){
                return '';
        }
        $fchar = ord($str[0]);
        if($fchar >= ord('A') && $fchar <= ord('z')){
                return strtoupper($str[0]);
        }
        $s1=iconv('UTF-8','gb2312',$str);
        $s2=iconv('gb2312','UTF-8',$s1);
        $s=$s2==$str?$s1:$str;
        $asc=ord($s[0])*256+ord($s[1])-65536;
        
        if($asc>=-20319&&$asc<=-20284) return 'A';
        if($asc>=-20283&&$asc<=-19776) return 'B';
        if($asc>=-19775&&$asc<=-19219) return 'C';
        if($asc>=-19218&&$asc<=-18711) return 'D';
        if($asc>=-18710&&$asc<=-18527) return 'E';
        if($asc>=-18526&&$asc<=-18240) return 'F';
        if($asc>=-18239&&$asc<=-17923) return 'G';
        if($asc>=-17922&&$asc<=-17418) return 'H';
        if($asc>=-17417&&$asc<=-16475) return 'J';
        if($asc>=-16474&&$asc<=-16213) return 'K';
        if($asc>=-16212&&$asc<=-15641) return 'L';
        if($asc>=-15640&&$asc<=-15166) return 'M';
        if($asc>=-15165&&$asc<=-14923) return 'N';
        if($asc>=-14922&&$asc<=-14915) return 'O';
        if($asc>=-14914&&$asc<=-14631) return 'P';
        if($asc>=-14630&&$asc<=-14150) return 'Q';
        if($asc>=-14149&&$asc<=-14091) return 'R';
        if($asc>=-14090&&$asc<=-13319) return 'S';
        if($asc>=-13318&&$asc<=-12839) return 'T';
        if($asc>=-12838&&$asc<=-12557) return 'W';
        if($asc>=-12556&&$asc<=-11848) return 'X';
        if($asc>=-11847&&$asc<=-11056) return 'Y';
        if($asc>=-11055&&$asc<=-10247) return 'Z';
        return '其他';
    }

    function vkckb($params)
    {
        $GUID = $params['GUID'];
        $mode = $params['mode'];
        $this->assign('mode', $mode);
        if($GUID==""){
            alert('GUID不能为空');
            exit;
        }
        $user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
        if(empty($user_infos)){
            alert('用户不存在');
            exit;
        }
        $this->assign('guid', $GUID);
        $this->assign('date', date("Y-m-d"));

        $uptype = 2;
        $total = $this->_dao->getOne( "SELECT COUNT(id) as c FROM sg_data_table_log where uptype = '$uptype' and isdel='0'");
        $page = $params['page'] == '' ? 1 : $params['page'];
        $url = "sgpqtb.php";
        $per = 25;
        $start = ( $page - 1 ) * $per;
        unset( $params['page'] );
        $pagebar = pagebar( $url, $params, $per, $page, $total );
        $this->assign( "pagebar", $pagebar );
        $sql = "select * from sg_data_table_log where uptype = '$uptype' and isdel='0' order by createtime desc limit $start, $per";
        $log_info = $this->_dao->query($sql);
        $createuser = array();
        foreach( $log_info as $v ){
            if(!in_array($v['createuser'],$createuser)){
                $createuser[] = $v['createuser'];
            }
        }
        $createuser_str =  implode("','",$createuser);
        $sql = "select id,truename from adminuser where id in ('$createuser_str')";
        $adminuser_info = $this->homeDao->query($sql);
        $admin_name = array();
        foreach( $adminuser_info as $v ){
            $admin_name[$v['id']] = $v['truename'];
        }
        $this->assign('log_info', $log_info);
        $this->assign('admin_name', $admin_name);
    }

    function uploadFileAll($params){
        $upfile = $_FILES['file'];
        $GUID = $params['GUID'];
        $mode = $params['mode'];
        $response = array(
            'Success'  => 1,
            'Message'  => '',
            'GUID'     => $GUID,
            'mode'     => $mode,
        );
        // 不同页面上传类型控制
        switch($params['filetype'])
        {
            case 'excel':
                $uptypes=array(
                    'application/vnd.ms-excel',
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                );
                $filetype = "Excel文件";
            break;
            case 'word':
                $uptypes=array(
                    'application/msword',
                    'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                );
                $filetype = "Word文件";
            break;
            default:
                $uptypes=array(
                    'application/vnd.ms-excel',
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                );
                $filetype = "Excel文件";
            break;
        }

		if($GUID==""){
			$response['Success'] = 0;
			$response['Message'] = 'GUID不能为空';
		}
		if($response['Success']){
			$user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
			if(empty($user_infos)){
				$response['Success'] = 0;
				$response['Message'] = '用户不存在';
			}
			if($response['Success']){
				if ($upfile['error']==0)
				{
					if (is_uploaded_file($upfile['tmp_name'])) {
						if (in_array($upfile['type'], $uptypes)) {
							$extName = strtolower(end(explode('.', $upfile ['name'])));
							$filename = "SG2-".$this->random(5); // 设置随机数长度
							$extName=strtolower(end(explode('.', $upfile['name']))); // 取得扩展名
							$name1=date('YmdHis').".".$extName;
							$dir = UPLOADFILE_KCKB;
							if (!file_exists($dir)) {
								if (mkdir($dir, 0777)) {
									$dest =$dir."/".$filename.$name1;
									if (move_uploaded_file($upfile ['tmp_name'], $dest)) {
										$response['Success'] = 1;
										$response['Message'] = '上传成功';
										$sheets = $this->readSheet($dest); //获取工作表列表
										$response['Result'] = $this->array_iconv($sheets);
										$response['File'] = $filename.$name1;
										$response['Date'] = $params['date'];
										$response['FileName'] = mb_convert_encoding($upfile ['name'],"GB2312","UTF-8");
										// $this->drkckb($dest, $params['date']);
									} else {
										$response['Success'] = 0;
										$response['Message'] = '上传失败，目录权限不足';
									}
								} else {
									$response['Success'] = 0;
									$response['Message'] = '目录不存在，上传失败';
								}
							} else {
								$dest = $dir."/".$filename.$name1;
								if (move_uploaded_file($upfile ['tmp_name'], $dest)) {
									$response['Success'] = 1;
									$response['Message'] = '上传成功';
									$sheets = $this->readSheet($dest); //获取工作表列表
									$response['Result'] = $this->array_iconv($sheets);
									$response['File'] = $filename.$name1;
									$response['Date'] = $params['date'];
									$response['FileName'] = mb_convert_encoding($upfile ['name'],"GB2312","UTF-8");
									// $this->drkckb($dest, $params['date']);
								} else {
									$response['Success'] = 0;
									$response['Message'] = '上传失败，目录权限不足';
								}
							}
						} else {
							$response['Success'] = 0;
							$response['Message'] = '上传失败，检查文件是否是'.$filetype;
						}
					} else {
						$response['Success'] = 0;
						$response['Message'] = '上传失败';
						clearstatcache(); //清除文件缓存信息
					}
				}
				else
				{
					$response['Success'] = 0;
					$response['Message'] = '上传失败';
				}
			}
		}
		echo $this->pri_JSON($response);
		exit;
	}

	function drkckb($params)
	{
		$file = UPLOADFILE_KCKB."/".$params["tmp"];
		$filename = mb_convert_encoding($params["filename"],"GB2312","UTF-8");
		$date = $params["date"];
		$GUID = $params['GUID'];
		$mode = $params['mode'];//1=客户端，2=安卓，3=iOS
		$response = array(
			'Success'  => 1,
			'Message'  => '',
			// 'GUID'     => $GUID,
			// 'mode'     => $mode,
		);
		if($GUID==""){
			$response["Success"] = 0;
			$response['Message'] = 'GUID不能为空';
		}
		if($response["Success"])
		{
			$user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
			if(empty($user_infos)){
				$response["Success"] = 0;
				$response['Message'] = '用户不存在';
			}

			if(empty($sheetList[0])&&$sheetList[0]!=0)
			{
				$response["Success"] = 0;
				$response['Message'] = '未选择工作表';
			}
			$date = mysql_real_escape_string($params["date"]);
			if(empty($date))
			{
				$response["Success"] = 0;
				$response['Message'] = '年份出错';
			}
			if($response["Success"])
			{
				require_once "../PHPExcel/PHPExcel.php";
				$type = pathinfo($file); 
				$type = strtolower($type["extension"]);
				if ($type=='xlsx') { 
					$type='Excel2007'; 
				}elseif($type=='xls') { 
					$type = 'Excel5';
				}
				$sheetList = explode(',', $params["sheet"]);
				
				$objReader = PHPExcel_IOFactory::createReader($type);//判断使用哪种格式
				$objReader ->setReadDataOnly(true); //只读取数据,会智能忽略所有空白行,这点很重要！！！
				$objPHPExcel = $objReader->load($file); //加载Excel文件
				$sheetNames = $objPHPExcel->getSheetNames();//所有的工作表名
				// $sheetCount = $objPHPExcel->getSheetCount();//获取sheet工作表总个数
				$flag = true;
				$tmpsheetName = array();
				foreach($sheetList as $sheetIndex)
				{
					// $sheet_name = iconv("utf-8","gb2312//IGNORE", $sheetNames[$sheetIndex]);
					$sheet = $objPHPExcel->getSheet($sheetIndex);
					// switch($sheet_name)
					switch($sheetIndex)
					{
						case 0: //每日生产信息
							$rows = $sheet->getHighestRow();  //行数
							// $cell->isMergeRangeValueCell()
							// $cell->getMergeRange()
							// $cellIterator = $row->getCellIterator();
							// exit;
							for($j = 'A'; $j <= 'C'; $j++ ){
								for($k = 2; $k <= 19; $k++){
									$key = $j.$k;
									if($key=="A3") //此单元格可能为日期
									{
										if($sheet->getCell($key)->getValue()=='=TODAY()')  //确认该单元格是否为日期
										{
											$data0_sc[$k][$j] = date('Y-m-d',PHPExcel_Shared_Date::ExcelToPHP(iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue())));
										}
										else
										{
											$data0_sc[$k][$j] = iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue());
										}
									}
									else
									{
										$data0_sc[$k][$j] = iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue());
									}
								}
							}
							for($j = 'E'; $j <= 'K'; $j++ ){
								for($k = 1; $k <= $rows; $k++){
									$key = $j.$k;
									if($key=="F1") //此单元格可能为日期
									{
										if($sheet->getCell($key)->getValue()=='=TODAY()')  //确认该单元格是否为日期
										{
											$data0[$k][$j] = date('Y-m-d',PHPExcel_Shared_Date::ExcelToPHP(iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue())));
										}
										else
										{
											$data0[$k][$j] = iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue());
										}
									}
									else{
										$data0[$k][$j] = iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue());
									}
								}
							}
							//每日生产信息表导入 1 start  2020-08-07
							//说明：dta_1：主业公司	dta_2：轧线名称	dta_3：轧制规格 dta_4：A2单元格（生产信息）
							$basesql0_sc = "insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,dta_3,dta_4,createtime,createuser) values";
							foreach($data0_sc as $row0=>$columns0) //1,2,3...
							{
								foreach($columns0 as $col0=>$datainfo0) //A,B,C...
								{
									if($row0>=5&&$col0=='A')
									{
										if($datainfo0)
										{
											$tmp0_sc = $datainfo0;
										}
										$dta_1_0 = $tmp0_sc;
									}
									if($row0>=5&&$col0=='B')
									{
										$dta_2_0 = $datainfo0;
									}
									if($row0>=5&&$col0=='C')
									{
										$dta_3_0 = $datainfo0;
									}
								}
								if($row0>=5&&(!empty($data0_sc[$row0]['B'])))
								{
									$values0_sc .= "('SG_mrscxx','每日生产信息','$date','$dta_1_0','$dta_2_0','$dta_3_0','".$data0_sc[2]['A']."',NOW(),'".$user_infos['Uid']."'),";
								}
							}
							if($values0_sc)
							{
								$this->_dao->execute("delete from sg_data_table where dta_ym='$date' and dta_type='SG_mrscxx' and dta_vartype='每日生产信息' and dta_4='".$data0_sc[2]['A']."'");
								$tsql = substr($values0_sc, 0, -1);
								$this->_dao->execute($basesql0_sc.$tsql);
							}
							//每日生产信息表导入 1 end

							//每日生产信息表导入 2 start    2020-08-07  J列合并单元格读取有误，会漏读取
							//说明：dta_1：区域	dta_2：库房名称 dta_3：库存	dta_4：在途	dta_5：合计（吨）dta_6：合理库存(万吨) dta_7：短缺规格 dta_8：G1单元格（陕钢各区域库存）
							$basesql0 = "insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,dta_3,dta_4,dta_5,dta_6,dta_7,dta_8,createtime,createuser) values";
							foreach($data0 as $row0=>$columns0) //1,2,3...
							{
								if($row0>=3)
								{
									foreach($columns0 as $col0=>$datainfo0) //A,B,C...
									{
										if($data0[$row0]['E'])
										{
											$tmp0 = $data0[$row0]['E'];
										}
										$dta_1_0 = $tmp0;
										$dta_2_0 = $data0[$row0]['F'];
										$dta_3_0 = round($data0[$row0]['G']);
										$dta_4_0 = round($data0[$row0]['H']);
										$dta_5_0 = round($data0[$row0]['I']);
										$dta_6_0 = $data0[$row0]['J'];
										$dta_7_0 = $data0[$row0]['K'];
									}
									if($data0[$row0]['E']=='钢材总合计'||(strpos($data0[$row0]['E'], '总合计')!==false))
									{
										// $values0 .= "('SG_mrscxx','每日生产信息','$date','$dta_1_0','$dta_2_0','$dta_3_0','$dta_4_0','$dta_5_0','$dta_6_0','$dta_7_0','".$data0[1]['G']."'),";
										// 以下行数均属于合计列，暂不存
										break;
									}
									else
									{
										if(!empty($data0[$row0]['F']))
										{
											$values0 .= "('SG_mrscxx','每日生产信息','$date','$dta_1_0','$dta_2_0','$dta_3_0','$dta_4_0','$dta_5_0','$dta_6_0','$dta_7_0','".$data0[1]['G']."',NOW(),'".$user_infos['Uid']."'),";
										}
									}

								}
							}
							if($values0)
							{
								$this->_dao->execute("delete from sg_data_table where dta_ym='$date' and dta_type='SG_mrscxx' and dta_8='".$data0[1]['G']."' and dta_vartype='每日生产信息'");
								$tsql = substr($values0, 0, -1);
								$this->_dao->execute($basesql0.$tsql);
								$tmpsheetName[] = mb_convert_encoding($sheetNames[$sheetIndex],"GB2312","UTF-8");
							}
							//每日生产信息表导入 2 end
						break;
						case 1: //龙钢库存
							$rows = $sheet->getHighestRow();  //行数
							$cols = $sheet->getHighestColumn(); //列数
							++$cols;
							for($j = 'A'; $j != $cols; $j++ ){
								for($k = 1; $k <= $rows; $k++){
									$key = $j.$k;
									if($key=="A2")  //此单元格可能为日期
									{
										if($sheet->getCell($key)->getValue()=='=TODAY()')  //确认该单元格是否为日期
										{
											$data1[$k][$j] = date('Y-m-d',PHPExcel_Shared_Date::ExcelToPHP(iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue())));
										}
										else
										{
											$data1[$k][$j] = iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue());
										}
									}
									else{
										$data1[$k][$j] = iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue());
									}
								}
							}
							//龙钢库存表导入 start   2020-08-07
							//说明：dta_1：定尺	dta_2：规格 dta_3：西轧总库存	dta_4：西轧可发库存 dta_5：新站台总库存	dta_6：新站台可发库存 dta_7：柞水同兴总 dta_8：柞水同兴可发 dta_9：同兴总 dta_10：同兴可发 dta_11：棒材一线总 dta_12：棒材一线可发 dta_13：棒材二线总 dta_14：棒材二线可发 dta_15：棒材三线总 dta_16：棒材三线可发 dta_17：高线总 dta_18：高线可发 dta_19:合计总 dta_20：合计可发 dta_21：在途
							// print_r($data1);
							$basesql1 = "insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,dta_3,dta_4,dta_5,dta_6,dta_7,dta_8,dta_9,dta_10,dta_11,dta_12,dta_13,dta_14,dta_15,dta_16,dta_17,dta_18,dta_19,dta_20,dta_21,createtime,createuser) values";
							foreach($data1 as $row1=>$columns1) //1,2,3...
							{
								if($row1>=6)
								{
									foreach($columns1 as $col1=>$datainfo1) //A,B,C...
									{
										if($data1[$row1]['A'])
										{
											$tmp1 = $data1[$row1]['A'];
										}
										$dta_1_1 = $tmp1;
										$dta_2_1 = $data1[$row1]['B'];
										$dta_3_1 = $data1[$row1]['C'];
										$dta_4_1 = $data1[$row1]['D'];
										$dta_5_1 = $data1[$row1]['E'];
										$dta_6_1 = $data1[$row1]['F'];
										$dta_7_1 = $data1[$row1]['G'];
										$dta_8_1 = $data1[$row1]['H'];
										$dta_9_1 = $data1[$row1]['I'];
										$dta_10_1 = $data1[$row1]['J'];
										$dta_11_1 = $data1[$row1]['K'];
										$dta_12_1 = $data1[$row1]['L'];
										$dta_13_1 = $data1[$row1]['M'];
										$dta_14_1 = $data1[$row1]['N'];
										$dta_15_1 = $data1[$row1]['O'];
										$dta_16_1 = $data1[$row1]['P'];
										$dta_17_1 = $data1[$row1]['Q'];
										$dta_18_1 = $data1[$row1]['R'];
										$dta_19_1 = $data1[$row1]['S'];
										$dta_20_1 = $data1[$row1]['T'];
										$dta_21_1 = $data1[$row1]['U'];
									}
									if($data1[$row1]['A']=='总合计'||(strpos($data1[$row1]['A'], '总合计')!==false))
									{
										$values1 .= "('SG_lgkc','龙钢库存','$date','总合计','总合计','$dta_3_1','$dta_4_1','$dta_5_1','$dta_6_1','$dta_7_1','$dta_8_1','$dta_9_1','$dta_10_1','$dta_11_1','$dta_12_1','$dta_13_1','$dta_14_1','$dta_15_1','$dta_16_1','$dta_17_1','$dta_18_1','$dta_19_1','$dta_20_1','$dta_21_1',NOW(),'".$user_infos['Uid']."'),";
									}
									else if($data1[$row1]['A']=='备注'||(strpos($data1[$row1]['A'], '备注')!==false))
									{
										$values1 .= "('SG_lgkc','龙钢库存','$date','备注','','".$data1[$row1]['C']."','','".$data1[$row1]['E']."','','".$data1[$row1]['G']."','','".$data1[$row1]['I']."','','".$data1[$row1]['K']."','','".$data1[$row1]['M']."','','".$data1[$row1]['O']."','','".$data1[$row1]['Q']."','','".$data1[$row1]['S']."','','".$data1[$row1]['U']."',NOW(),'".$user_infos['Uid']."'),";
									}
									else
									{
										if(!empty($data1[$row1]['B']))
										{
											$values1 .= "('SG_lgkc','龙钢库存','$date','$dta_1_1','$dta_2_1','$dta_3_1','$dta_4_1','$dta_5_1','$dta_6_1','$dta_7_1','$dta_8_1','$dta_9_1','$dta_10_1','$dta_11_1','$dta_12_1','$dta_13_1','$dta_14_1','$dta_15_1','$dta_16_1','$dta_17_1','$dta_18_1','$dta_19_1','$dta_20_1','$dta_21_1',NOW(),'".$user_infos['Uid']."'),";
										}
									}

                                }
                            }
                            if($values1)
                            {
                                $this->_dao->execute("delete from sg_data_table where dta_ym='$date' and dta_type='SG_lgkc' and dta_vartype='龙钢库存'");
                                $tsql = substr($values1, 0, -1);
                                $this->_dao->execute($basesql1.$tsql);
                                $lw_sum1 = $data1[31]['S']+$data1[55]['S']+$data1[79]['S'];
                                for($di=85;$di<=106;$di++){
                                    $lw_sum1 = $lw_sum1 + $data1[$di]['S'];
                                }
                                $pl_sum1 = $data1[160]['S'];
                                for($di=80;$di<=84;$di++){
                                    $pl_sum1 = $pl_sum1 + $data1[$di]['S'];
                                }
                                $gx_sum1 = $data1[137]['S'];
                                $this->_dao->execute("insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,dta_19,createtime,createuser) values('SG_lgkc','龙钢库存','$date','螺纹','合计','$lw_sum1',NOW(),".$user_infos['Uid']."),('SG_lgkc','龙钢库存','$date','盘螺','合计','$pl_sum1',NOW(),".$user_infos['Uid']."),('SG_lgkc','龙钢库存','$date','高线','合计','$gx_sum1',NOW(),".$user_infos['Uid'].")");
                                $tmpsheetName[] = mb_convert_encoding($sheetNames[$sheetIndex],"GB2312","UTF-8");
                            }
                            //龙钢库存表导入 end
                        break;
                        case 2: //板带
                            $rows = $sheet->getHighestRow();  //行数
                            $cols = $sheet->getHighestColumn(); //列数
                            ++$cols;
                            for($j = 'A'; $j != $cols; $j++ ){
                                for($k = 1; $k <= $rows; $k++){
                                    $key = $j.$k;
                                    $data2[$k][$j] = iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue());
                                }
                            }
                            //板带表导入 start   2020-08-07
                            //说明：dta_1：规格（该列表格内为空的）	dta_2：规格 dta_3：总库存件数 dta_4：总库存吨位 dta_5：可发库存件数	dta_6：可发库存吨位 dta_7：备注
                            $basesql2 = "insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,dta_3,dta_4,dta_5,dta_6,dta_7,createtime,createuser) values";
                            foreach($data2 as $row2=>$columns2) //1,2,3...
                            {
                                if($row2>=5)
                                {
                                    foreach($columns2 as $col2=>$datainfo2) //A,B,C...
                                    {
                                        if($data1[$row1]['A'])
                                        {
                                            $tmp2 = $data1[$row1]['A'];
                                        }
                                        $dta_1_2 = $tmp2;
                                        $dta_2_2 = $data2[$row2]['B'];
                                        $dta_3_2 = $data2[$row2]['C'];
                                        $dta_4_2 = $data2[$row2]['D'];
                                        $dta_5_2 = $data2[$row2]['E'];
                                        $dta_6_2 = $data2[$row2]['F'];
                                        $dta_7_2 = $data2[$row2]['G'];
                                    }
                                    if($data2[$row2]['A']=='合计'||(strpos($data2[$row2]['A'], '合计')!==false))
                                    {
                                        $values2 .= "('SG_bd','板带','$date','合计','合计','$dta_3_2','$dta_4_2','$dta_5_2','$dta_6_2','$dta_7_2',NOW(),'".$user_infos['Uid']."'),";
                                    }
                                    else
                                    {
                                        if(!empty($data2[$row2]['B']))
                                        {
                                            $values2 .= "('SG_bd','板带','$date','$dta_1_2','$dta_2_2','$dta_3_2','$dta_4_2','$dta_5_2','$dta_6_2','$dta_7_2',NOW(),'".$user_infos['Uid']."'),";
                                        }
                                    }

                                }
                            }
                            if($values2)
                            {
                                $this->_dao->execute("delete from sg_data_table where dta_ym='$date' and dta_type='SG_bd' and dta_vartype='板带'");
                                $tsql = substr($values2, 0, -1);
                                $this->_dao->execute($basesql2.$tsql);
                                // $bd_sum2 = $data2[373]['D'];
                                // $this->_dao->execute("insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,dta_19,createtime,createuser) values('SG_bd','板带','$date','板带','合计','$bd_sum2',NOW(),".$user_infos['Uid'].")");
                                $tmpsheetName[] = mb_convert_encoding($sheetNames[$sheetIndex],"GB2312","UTF-8");
                            }
                            //板带表导入 end
                        break;
                        case 3: //汉钢库存
                            $rows = $sheet->getHighestRow();  //行数
                            $cols = $sheet->getHighestColumn(); //列数
                            ++$cols;
                            for($j = 'A'; $j != $cols; $j++ ){
                                for($k = 1; $k <= $rows; $k++){
                                    $key = $j.$k;
                                    if($key=="A2")  //此单元格可能为日期
                                    {
                                        if($sheet->getCell($key)->getValue()=='=TODAY()')  //确认该单元格是否为日期
                                        {
                                            $data3[$k][$j] = date('Y-m-d',PHPExcel_Shared_Date::ExcelToPHP(iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue())));
                                        }
                                        else
                                        {
                                            $data3[$k][$j] = iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue());
                                        }
                                    }
                                    else
                                    {
                                        $data3[$k][$j] = iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue());
                                    }
                                }
                            }
                            //汉钢库存表导入 start   2020-08-10
                            //说明：dta_1：定尺	dta_2：规格 dta_3：棒材一线总	dta_4：棒材一线可发 dta_5：棒材二线总库存	dta_6：棒材二线可发库存 dta_7：双高线总 dta_8：双高线可发 dta_9：单高线总 dta_10：单高线可发 dta_11：成品库总 dta_12：成品库可发 dta_13：料场总 dta_14：料场可发 dta_15：中转库总 dta_16：中转库可发 dta_17：直配库总 dta_18：直配库可发 dta_19:合计总 dta_20：合计可发 dta_21：在途
                            $basesql3 = "insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,dta_3,dta_4,dta_5,dta_6,dta_7,dta_8,dta_9,dta_10,dta_11,dta_12,dta_13,dta_14,dta_15,dta_16,dta_17,dta_18,dta_19,dta_20,dta_21,createtime,createuser) values";
                            foreach($data3 as $row3=>$columns3) //1,2,3...
                            {
                                if($row3>=5)
                                {
                                    foreach($columns3 as $col3=>$datainfo3) //A,B,C...
                                    {
                                        if($data3[$row3]['A'])
                                        {
                                            $tmp3 = $data3[$row3]['A'];
                                        }
                                        $dta_1_3 = $tmp3;
                                        $dta_2_3 = $data3[$row3]['B'];
                                        $dta_3_3 = $data3[$row3]['C'];
                                        $dta_4_3 = $data3[$row3]['D'];
                                        $dta_5_3 = $data3[$row3]['E'];
                                        $dta_6_3 = $data3[$row3]['F'];
                                        $dta_7_3 = $data3[$row3]['G'];
                                        $dta_8_3 = $data3[$row3]['H'];
                                        $dta_9_3 = $data3[$row3]['I'];
                                        $dta_10_3 = $data3[$row3]['J'];
                                        $dta_11_3 = $data3[$row3]['K'];
                                        $dta_12_3 = $data3[$row3]['L'];
                                        $dta_13_3 = $data3[$row3]['M'];
                                        $dta_14_3 = $data3[$row3]['N'];
                                        $dta_15_3 = $data3[$row3]['O'];
                                        $dta_16_3 = $data3[$row3]['P'];
                                        $dta_17_3 = $data3[$row3]['Q'];
                                        $dta_18_3 = $data3[$row3]['R'];
                                        $dta_19_3 = $data3[$row3]['S'];
                                        $dta_20_3 = $data3[$row3]['T'];
                                        $dta_21_3 = $data3[$row3]['U'];
                                    }
                                    if($data3[$row3]['A']=='总合计'||(strpos($data3[$row3]['A'], '总合计')!==false))
                                    {
                                        $values3 .= "('SG_hgkc','汉钢库存','$date','总合计','总合计','$dta_3_3','$dta_4_3','$dta_5_3','$dta_6_3','$dta_7_3','$dta_8_3','$dta_9_3','$dta_10_3','$dta_11_3','$dta_12_3','$dta_13_3','$dta_14_3','$dta_15_3','$dta_16_3','$dta_17_3','$dta_18_3','$dta_19_3','$dta_20_3','$dta_21_3',NOW(),'".$user_infos['Uid']."'),";
                                    }
                                    else if($data3[$row3]['A']=='备注'||(strpos($data3[$row3]['A'], '备注')!==false))
                                    {
                                        $values3 .= "('SG_hgkc','汉钢库存','$date','备注','".$data3[$row3]['C']."','','','','','','','','','','','','','','','','','','','',NOW(),'".$user_infos['Uid']."'),";
                                    }
                                    else
                                    {
                                        if(!empty($data3[$row3]['B']))
                                        {
                                            $values3 .= "('SG_hgkc','汉钢库存','$date','$dta_1_3','$dta_2_3','$dta_3_3','$dta_4_3','$dta_5_3','$dta_6_3','$dta_7_3','$dta_8_3','$dta_9_3','$dta_10_3','$dta_11_3','$dta_12_3','$dta_13_3','$dta_14_3','$dta_15_3','$dta_16_3','$dta_17_3','$dta_18_3','$dta_19_3','$dta_20_3','$dta_21_3',NOW(),'".$user_infos['Uid']."'),";
                                        }
                                    }
                                    

                                }
                            }
                            if($values3)
                            {
                                $this->_dao->execute("delete from sg_data_table where dta_ym='$date' and dta_type='SG_hgkc' and dta_vartype='汉钢库存'");
                                $tsql = substr($values3, 0, -1);
                                $this->_dao->execute($basesql3.$tsql);
                                $lw_sum = $data1[31]['S']+$data3[55]['S']; //螺纹
                                for($di=56;$di<=66;$di++){
                                    $lw_sum = $lw_sum + $data3[$di]['S'];
                                }
                                for($di=85;$di<=106;$di++){
                                    $lw_sum = $lw_sum + $data3[$di]['S'];
                                }
                                $pl_sum = 0;  //盘螺
                                for($di=80;$di<=84;$di++){
                                    $pl_sum = $pl_sum + $data3[$di]['S'];
                                }
                                for($di=138;$di<=141;$di++){
                                    $pl_sum = $pl_sum + $data3[$di]['S'];
                                }
                                $mgg_sum = 0;  //锚杆钢
                                for($di=67;$di<=78;$di++){
                                    $mgg_sum = $mgg_sum + $data3[$di]['S'];
                                }
                                $gx_sum = $data3[126]['S'];  //高线
                                for($di=120;$di<=124;$di++){
                                    $gx_sum = $gx_sum + $data3[$di]['S'];
                                }
                                $yg_sum = 0;  //圆钢
                                for($di=127;$di<=134;$di++){
                                    $yg_sum = $yg_sum + $data3[$di]['S'];
                                }
                                $yx_sum = 0;  //硬线
                                $Bg_sum = 0;  //B钢
                                $hs_sum = 0;  //焊丝
                                $ld_sum = 0;  //冷镦
                                $gqlw_sum = 0;//高强螺纹
                                $PCgb_sum = 0;//PC钢棒
                                for($di=5;$di<=count($data3);$di++){
                                    if(strpos($data3[$di]['B'], '#')!==false){
                                        $yx_sum = $yx_sum + $data3[$di]['S'];
                                    }elseif(strpos($data3[$di]['B'], 'B/')!==false){
                                        $Bg_sum = $Bg_sum + $data3[$di]['S'];
                                    }elseif(strpos($data3[$di]['B'], 'ER70S-6/5.5')!==false){
                                        $hs_sum = $hs_sum + $data3[$di]['S'];
                                    }elseif(strpos($data3[$di]['B'], 'ML45')!==false){
                                        $ld_sum = $ld_sum + $data3[$di]['S'];
                                    }elseif(strpos($data3[$di]['B'], 'T')!==false){
                                        $gqlw_sum = $gqlw_sum + $data3[$di]['S'];
                                    }elseif(strpos($data3[$di]['B'], '30MnSi')!==false){
                                        $PCgb_sum = $PCgb_sum + $data3[$di]['S'];
                                    }
                                }
                                $this->_dao->execute("insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,dta_19,createtime,createuser) values
                                ('SG_hgkc','汉钢库存','$date','螺纹','合计','$lw_sum',NOW(),".$user_infos['Uid']."),
                                ('SG_hgkc','汉钢库存','$date','盘螺','合计','$pl_sum',NOW(),".$user_infos['Uid']."),
                                ('SG_hgkc','汉钢库存','$date','高线','合计','$gx_sum',NOW(),".$user_infos['Uid']."),
                                ('SG_hgkc','汉钢库存','$date','锚杆钢','合计','$mgg_sum',NOW(),".$user_infos['Uid']."),
                                ('SG_hgkc','汉钢库存','$date','圆钢','合计','$yg_sum',NOW(),".$user_infos['Uid']."),
                                ('SG_hgkc','汉钢库存','$date','硬线','合计','$yx_sum',NOW(),".$user_infos['Uid']."),
                                ('SG_hgkc','汉钢库存','$date','B钢','合计','$Bg_sum',NOW(),".$user_infos['Uid']."),
                                ('SG_hgkc','汉钢库存','$date','焊丝','合计','$hs_sum',NOW(),".$user_infos['Uid']."),
                                ('SG_hgkc','汉钢库存','$date','冷镦','合计','$ld_sum',NOW(),".$user_infos['Uid']."),
                                ('SG_hgkc','汉钢库存','$date','高强螺纹','合计','$gqlw_sum',NOW(),".$user_infos['Uid']."),
                                ('SG_hgkc','汉钢库存','$date','PC钢棒','合计','$PCgb_sum',NOW(),".$user_infos['Uid'].")");
                                $tmpsheetName[] = mb_convert_encoding($sheetNames[$sheetIndex],"GB2312","UTF-8");
                            }
                            //汉钢库存表导入  end
                        break;
                        case 4: //富扎（隐藏）
                            $rows = $sheet->getHighestRow();  //行数
                            $cols = $sheet->getHighestColumn(); //列数
                            ++$cols;
                            for($j = 'A'; $j != $cols; $j++ ){
                                for($k = 1; $k <= $rows; $k++){
                                    $key = $j.$k;
                                    if($key=="A2")  //此单元格可能为日期
                                    {
                                        if($sheet->getCell($key)->getValue()=='=TODAY()')  //确认该单元格是否为日期
                                        {
                                            $data4[$k][$j] = date('Y-m-d',PHPExcel_Shared_Date::ExcelToPHP(iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue())));
                                        }
                                        else
                                        {
                                            $data4[$k][$j] = iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue());
                                        }
                                    }
                                    else
                                    {
                                        $data4[$k][$j] = iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue());
                                    }
                                }
                            }
                            //富扎表导入 （隐藏，暂无数据） start  2020-08-10
                            //说明：dta_1：定尺	dta_2：规格 dta_3：实际库存	dta_4：可发库存 dta_5：合计
                            $basesql4 = "insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,dta_3,dta_4,dta_5,createtime,createuser) values";
                            foreach($data4 as $row4=>$columns4) //1,2,3...
                            {
                                if($row4>=6)
                                {
                                    foreach($columns4 as $col4=>$datainfo4) //A,B,C...
                                    {
                                        if($data4[$row4]['A'])
                                        {
                                            $tmp4 = $data4[$row4]['A'];
                                        }
                                        $dta_1_4 = $tmp4;
                                        $dta_2_4 = $data4[$row4]['B'];
                                        $dta_3_4 = $data4[$row4]['C'];
                                        $dta_4_4 = $data4[$row4]['D'];
                                        $dta_5_4 = $data4[$row4]['E'];
                                    }
                                    if($data4[$row4]['A']=='合计'||(strpos($data4[$row4]['A'], '合计')!==false))
                                    {
                                        $values4 .= "('SG_fz','富扎','$date','合计','合计','$dta_3_4','$dta_4_4','$dta_5_4',NOW(),'".$user_infos['Uid']."'),";
                                    }
                                    else
                                    {
                                        if(!empty($data4[$row4]['B']))
                                        {
                                            $values4 .= "('SG_fz','富扎','$date','$dta_1_4','$dta_2_4','$dta_3_4','$dta_4_4','$dta_5_4',NOW(),'".$user_infos['Uid']."'),";
                                        }
                                    }

                                }
                            }
                            if($values4)
                            {
                                $this->_dao->execute("delete from sg_data_table where dta_ym='$date' and dta_type='SG_fz' and dta_vartype='富扎'");
                                $tsql = substr($values4, 0, -1);
                                $this->_dao->execute($basesql4.$tsql);
                                $tmpsheetName[] = mb_convert_encoding($sheetNames[$sheetIndex],"GB2312","UTF-8");
                            }
                            //富扎表导入 end
                        break;
                        case 5: //丰义
                            $rows = $sheet->getHighestRow();  //行数
                            $cols = $sheet->getHighestColumn(); //列数
                            ++$cols;
                            for($j = 'A'; $j != $cols; $j++ ){
                                for($k = 1; $k <= $rows; $k++){
                                    $key = $j.$k;
                                    if($key=="A2")  //此单元格可能为日期
                                    {
                                        if($sheet->getCell($key)->getValue()=='=TODAY()')  //确认该单元格是否为日期
                                        {
                                            $data5[$k][$j] = date('Y-m-d',PHPExcel_Shared_Date::ExcelToPHP(iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue())));
                                        }
                                        else
                                        {
                                            $data5[$k][$j] = iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue());
                                        }
                                    }
                                    else
                                    {
                                        $data5[$k][$j] = iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue());
                                    }
                                }
                            }
                            //丰义表导入 start   2020-08-10
                            //说明：dta_1：定尺	dta_2：规格 dta_3：总库存	dta_4：可发库存 dta_5：合计总库存 dta_6：合计可发库存
                            $basesql5 = "insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,dta_3,dta_4,dta_5,dta_6,createtime,createuser) values";
                            foreach($data5 as $row5=>$columns5) //1,2,3...
                            {
                                if($row5>=6)
                                {
                                    foreach($columns5 as $col5=>$datainfo5) //A,B,C...
                                    {
                                        if($data5[$row5]['A'])
                                        {
                                            $tmp5 = $data5[$row5]['A'];
                                        }
                                        $dta_1_5 = $tmp5;
                                        $dta_2_5 = $data5[$row5]['B'];
                                        $dta_3_5 = $data5[$row5]['C'];
                                        $dta_4_5 = $data5[$row5]['D'];
                                        $dta_5_5 = $data5[$row5]['E'];
                                        $dta_6_5 = $data5[$row5]['F'];
                                    }
                                    if($data5[$row5]['A']=='总合计'||(strpos($data5[$row5]['A'], '总合计')!==false))
                                    {
                                        $values5 .= "('SG_lf','丰义','$date','总合计','总合计','$dta_3_5','$dta_4_5','$dta_5_5','$dta_6_5',NOW(),'".$user_infos['Uid']."'),";
                                    }
                                    else if($data5[$row5]['A']=='备注'||(strpos($data5[$row5]['A'], '备注')!==false))
                                    {
                                        $values5 .= "('SG_lf','丰义','$date','备注','".$data5[$row5]['B']."','".$data5[$row5]['C']."','','','',NOW(),'".$user_infos['Uid']."'),";
                                    }
                                    else
                                    {
                                        if(!empty($data5[$row5]['B']))
                                        {
                                            $values5 .= "('SG_lf','丰义','$date','$dta_1_5','$dta_2_5','$dta_3_5','$dta_4_5','$dta_5_5','$dta_6_5',NOW(),'".$user_infos['Uid']."'),";
                                        }
                                    }
                                }
                            }
                            if($values5)
                            {
                                $this->_dao->execute("delete from sg_data_table where dta_ym='$date' and dta_type='SG_lf' and dta_vartype='丰义'");
                                $tsql = substr($values5, 0, -1);
                                $this->_dao->execute($basesql5.$tsql);
                                $tmpsheetName[] = mb_convert_encoding($sheetNames[$sheetIndex],"GB2312","UTF-8");
                            }
                            //丰义表导入 end
                        break;
                        case 6: //委外加工柞水同兴（隐藏）
                            $rows = $sheet->getHighestRow();  //行数
                            $cols = $sheet->getHighestColumn(); //列数
                            ++$cols;
                            for($j = 'A'; $j != $cols; $j++ ){
                                for($k = 1; $k <= $rows; $k++){
                                    $key = $j.$k;
                                    if($key=="A2")  //此单元格可能为日期
                                    {
                                        if($sheet->getCell($key)->getValue()=='=TODAY()')  //确认该单元格是否为日期
                                        {
                                            $data6[$k][$j] = date('Y-m-d',PHPExcel_Shared_Date::ExcelToPHP(iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue())));
                                        }
                                        else
                                        {
                                            $data6[$k][$j] = iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue());
                                        }
                                    }
                                    else
                                    {
                                        $data6[$k][$j] = iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue());
                                    }
                                }
                            }
                            //委外加工柞水同兴导入 （隐藏，暂无数据） start   2020-08-10
                            //说明：dta_1：定尺	dta_2：规格 dta_3：总库存	dta_4：可发库存 dta_5：合计总库存 dta_6：合计可发库存
                            $basesql6 = "insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,dta_3,dta_4,dta_5,dta_6,createtime,createuser) values";
                            foreach($data6 as $row6=>$columns6) //1,2,3...
                            {
                                if($row6>=6)
                                {
                                    foreach($columns6 as $col6=>$datainfo6) //A,B,C...
                                    {
                                        if($data6[$row6]['A'])
                                        {
                                            $tmp6 = $data6[$row6]['A'];
                                        }
                                        $dta_1_6 = $tmp6;
                                        $dta_2_6 = $data6[$row6]['B'];
                                        $dta_3_6 = $data6[$row6]['C'];
                                        $dta_4_6 = $data6[$row6]['D'];
                                        $dta_5_6 = $data6[$row6]['E'];
                                        $dta_6_6 = $data6[$row6]['F'];
                                    }
                                    if($data6[$row6]['A']=='总合计'||(strpos($data6[$row6]['A'], '总合计')!==false))
                                    {
                                        $values6 .= "('SG_zstx','委外加工柞水同兴','$date','总合计','总合计','$dta_3_6','$dta_4_6','$dta_5_6','$dta_6_6',NOW(),'".$user_infos['Uid']."'),";
                                    }
                                    else if($data6[$row6]['A']=='备注'||(strpos($data6[$row6]['A'], '备注')!==false))
                                    {
                                        $values6 .= "('SG_zstx','委外加工柞水同兴','$date','备注','".$data6[$row6]['B']."','','','','',NOW(),'".$user_infos['Uid']."'),";
                                    }
                                    else
                                    {
                                        if(!empty($data6[$row6]['B']))
                                        {
                                            $values6 .= "('SG_zstx','委外加工柞水同兴','$date','$dta_1_6','$dta_2_6','$dta_3_6','$dta_4_6','$dta_5_6','$dta_6_6',NOW(),'".$user_infos['Uid']."'),";
                                        }
                                    }
                                }
                            }
                            if($values6)
                            {
                                $this->_dao->execute("delete from sg_data_table where dta_ym='$date' and dta_type='SG_zstx' and dta_vartype='委外加工柞水同兴'");
                                $tsql = substr($values6, 0, -1);
                                $this->_dao->execute($basesql6.$tsql);
                                $tmpsheetName[] = mb_convert_encoding($sheetNames[$sheetIndex],"GB2312","UTF-8");
                            }
                            //委外加工柞水同兴表导入 end 
                        break;
                        case 7: //区域中心库库存快报
                            $rows = $sheet->getHighestRow();  //行数
                            $cols = $sheet->getHighestColumn(); //列数
                            $qy_cils = $cols;
                            ++$cols;
                            for($j = 'A'; $j != $cols; $j++ ){
                                for($k = 1; $k <= $rows; $k++){
                                    $key = $j.$k;
                                    if($key=="A2")  //此单元格可能为日期
                                    {
                                        if($sheet->getCell($key)->getValue()=='=TODAY()')  //确认该单元格是否为日期
                                        {
                                            $data7[$k][$j] = date('Y-m-d',PHPExcel_Shared_Date::ExcelToPHP(iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue())));
                                        }
                                        else
                                        {
                                            $data7[$k][$j] = iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue());
                                        }
                                    }
                                    else
                                    {
                                        $data7[$k][$j] = iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue());
                                    }
                                }
                            }
                            //区域中心库库存快报表导入 start   2020-08-10
                            //说明：dta_1：定尺	dta_2：规格 dta_3：现货	dta_4：在途 dta_5：小计 dta_6：现货 dta_7：在途 dta_8：小计 dta_9：合计 dta_10：西安分公司韩城钢材库，dta_1、dta_2固定为定尺和规格，dta_10为各区域名，此表分区域存储
                            $basesql7 = "insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,dta_3,dta_4,dta_5,dta_6,dta_7,dta_8,dta_9,dta_10,createtime,createuser) values";
                            $columnCount = PHPExcel_Cell::columnIndexFromString($qy_cils);
                            foreach($data7 as $row7=>$columns7) //1,2,3...
                            {
                                $x = 'B';
                                if($row7>=6)
                                {
                                    if($columns7['A'])
                                    {
                                        $tmp7 = $columns7['A'];
                                    }
                                    
                                    $dta_1_7 = $tmp7;
                                    $dta_2_7 = $columns7[$x++];
                                    
                                    for($i=1;$i<=(($columnCount-3)/7);$i++)
                                    {
                                        $dta_10_7 = trim($data7['3'][$x]);
                                        $dta_3_7 = str_replace("　","",$columns7[$x++]);
                                        $dta_4_7 = str_replace("　","",$columns7[$x++]);
                                        $dta_5_7 = str_replace("　","",$columns7[$x++]);
                                        $dta_6_7 = str_replace("　","",$columns7[$x++]);
                                        $dta_7_7 = str_replace("　","",$columns7[$x++]);
                                        $dta_8_7 = str_replace("　","",$columns7[$x++]);
                                        $dta_9_7 = str_replace("　","",$columns7[$x++]);

                                        $dta_3_7 = strlen($dta_3_7)>12?sprintf("%.3f", $dta_3_7):$dta_3_7;
                                        $dta_4_7 = strlen($dta_4_7)>12?sprintf("%.3f", $dta_4_7):$dta_4_7;
                                        $dta_5_7 = strlen($dta_5_7)>12?sprintf("%.3f", $dta_5_7):$dta_5_7;
                                        $dta_6_7 = strlen($dta_6_7)>12?sprintf("%.3f", $dta_6_7):$dta_6_7;
                                        $dta_7_7 = strlen($dta_7_7)>12?sprintf("%.3f", $dta_7_7):$dta_7_7;
                                        $dta_8_7 = strlen($dta_8_7)>12?sprintf("%.3f", $dta_8_7):$dta_8_7;
                                        $dta_9_7 = strlen($dta_9_7)>12?sprintf("%.3f", $dta_9_7):$dta_9_7;

                                        if($columns7['A']=='总合计'||(strpos($columns7['A'], '总合计')!==false))
                                        {
                                            $values7 .= "('SG_qyzxkkckb','区域中心库库存快报','$date','总合计','总合计','$dta_3_7','$dta_4_7','$dta_5_7','$dta_6_7','$dta_7_7','$dta_8_7','$dta_9_7','$dta_10_7',NOW(),'".$user_infos['Uid']."'),";
                                        }
                                        else
                                        {
                                            if(!empty($columns7['B']))
                                            {
                                                $values7 .= "('SG_qyzxkkckb','区域中心库库存快报','$date','$dta_1_7','$dta_2_7','$dta_3_7','$dta_4_7','$dta_5_7','$dta_6_7','$dta_7_7','$dta_8_7','$dta_9_7','$dta_10_7',NOW(),'".$user_infos['Uid']."'),";
                                            }
                                        }
                                    }
                                }
                            }
                            if($values7)
                            {
                                $this->_dao->execute("delete from sg_data_table where dta_ym='$date' and dta_type='SG_qyzxkkckb' and dta_vartype='区域中心库库存快报'");
                                $tsql = substr($values7, 0, -1);
                                $this->_dao->execute($basesql7.$tsql);
                                $tmpsheetName[] = mb_convert_encoding($sheetNames[$sheetIndex],"GB2312","UTF-8");
                            }
                            //区域中心库库存快报表导入 end
                        break;
                        case 8: //库存结构
                            $rows = $sheet->getHighestRow();  //行数
                            $cols = $sheet->getHighestColumn(); //列数
                            $kc_cils = $cols;
                            ++$cols;
                            for($j = 'A'; $j != $cols; $j++ ){
                                for($k = 1; $k <= $rows; $k++){
                                    $key = $j.$k;
                                    $data8[$k][$j] = str_replace("　", "", iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue()));
                                }
                            }
                            //库存结构表导入 start   2020-08-10
                            //说明：dta_1：定尺	dta_2：规格 dta_3：现货	dta_4：在途 dta_5：小计 dta_6：现货 dta_7：在途 dta_8：小计 dta_9：合计 dta_10：西安分公司韩城钢材库，dta_1、dta_2固定为定尺和规格，dta_10为各区域名，此表分区域存储
                            $basesql8 = "insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,dta_3,dta_4,dta_5,dta_6,dta_7,createtime,createuser) values";
                            foreach($data8 as $row8=>$columns8) //1,2,3...
                            {
                                if($row8>=4)
                                {
                                    if($columns8['A'])
                                    {
                                        $tmp8 = $columns8['A'];
                                    }
                                    
                                    $dta_1_8 = $tmp8;
                                    $dta_2_8 = $columns8['B'];  //规格
                                    //西安库
                                    $dta_3_8 = str_replace("#DIV/0!","",$columns8['C']);
                                    $dta_4_8 = str_replace("#DIV/0!","",$columns8['D']);
                                    $dta_5_8 = str_replace("#DIV/0!","",$columns8['E']);
                                    $dta_6_8 = str_replace("#DIV/0!","",$columns8['F']);
                                    $dta_7_8 = $data8[2]['C'];
                                    // $dta_3_8 = str_replace("#DIV/0!", "", $dta_3_8);
                                    // $dta_4_8 = str_replace("#DIV/0!", "", $dta_4_8);
                                    // $dta_5_8 = str_replace("#DIV/0!", "", $dta_5_8);
                                    // $dta_6_8 = str_replace("#DIV/0!", "", $dta_6_8);
                                    $dta_3_8 = strlen($dta_3_8)>12?sprintf("%.3f", $dta_3_8):$dta_3_8;
                                    $dta_4_8 = strlen($dta_4_8)>12?sprintf("%.3f", $dta_4_8):$dta_4_8;
                                    $dta_5_8 = strlen($dta_5_8)>12?sprintf("%.3f", $dta_5_8):$dta_5_8;
                                    $dta_6_8 = strlen($dta_6_8)>12?sprintf("%.3f", $dta_6_8):$dta_6_8;
                                    if($columns8['A']=='总合计'||(strpos($columns8['A'], '合计')!==false)&&(!(strpos($columns8['A'], '备注')!==false)))
                                    {
                                        $values8 .= "('SG_kcjg','库存结构','$date','".$columns8['A']."','".$columns8['A']."','$dta_3_8','$dta_4_8','$dta_5_8','$dta_6_8','$dta_7_8',NOW(),'".$user_infos['Uid']."'),";
                                    }
                                    else
                                    {
                                        if(!empty($columns8['B']))
                                        {
                                            $values8 .= "('SG_kcjg','库存结构','$date','$dta_1_8','$dta_2_8','$dta_3_8','$dta_4_8','$dta_5_8','$dta_6_8','$dta_7_8',NOW(),'".$user_infos['Uid']."'),";
                                        }
                                    }
                                    //西安533钢材库
                                    $dta_3_8 = str_replace("#DIV/0!","",$columns8['G']);
                                    $dta_4_8 = str_replace("#DIV/0!","",$columns8['H']);
                                    $dta_5_8 = str_replace("#DIV/0!","",$columns8['I']);
                                    $dta_6_8 = str_replace("#DIV/0!","",$columns8['J']);
                                    $dta_7_8 = $data8[2]['G'];
                                    // $dta_3_8 = str_replace("#DIV/0!", "", $dta_3_8);
                                    // $dta_4_8 = str_replace("#DIV/0!", "", $dta_4_8);
                                    // $dta_5_8 = str_replace("#DIV/0!", "", $dta_5_8);
                                    // $dta_6_8 = str_replace("#DIV/0!", "", $dta_6_8);
                                    $dta_3_8 = strlen($dta_3_8)>12?sprintf("%.3f", $dta_3_8):$dta_3_8;
                                    $dta_4_8 = strlen($dta_4_8)>12?sprintf("%.3f", $dta_4_8):$dta_4_8;
                                    $dta_5_8 = strlen($dta_5_8)>12?sprintf("%.3f", $dta_5_8):$dta_5_8;
                                    $dta_6_8 = strlen($dta_6_8)>12?sprintf("%.3f", $dta_6_8):$dta_6_8;
                                    if($columns8['A']=='总合计'||(strpos($columns8['A'], '合计')!==false)&&(!(strpos($columns8['A'], '备注')!==false)))
                                    {
                                        $values8 .= "('SG_kcjg','库存结构','$date','".$columns8['A']."','".$columns8['A']."','$dta_3_8','$dta_4_8','$dta_5_8','$dta_6_8','$dta_7_8',NOW(),'".$user_infos['Uid']."'),";
                                    }
                                    else
                                    {
                                        if(!empty($columns8['B']))
                                        {
                                            $values8 .= "('SG_kcjg','库存结构','$date','$dta_1_8','$dta_2_8','$dta_3_8','$dta_4_8','$dta_5_8','$dta_6_8','$dta_7_8',NOW(),'".$user_infos['Uid']."'),";
                                        }
                                    }

                                    //重复
                                    $dta_3_8 = str_replace("#DIV/0!","",$columns8['K']);
                                    $dta_4_8 = str_replace("#DIV/0!","",$columns8['L']);
                                    $dta_5_8 = str_replace("#DIV/0!","",$columns8['M']);
                                    $dta_6_8 = str_replace("#DIV/0!","",$columns8['N']);
                                    $dta_7_8 = $data8[2]['K'];
                                    // $dta_3_8 = str_replace("#DIV/0!", "", $dta_3_8);
                                    // $dta_4_8 = str_replace("#DIV/0!", "", $dta_4_8);
                                    // $dta_5_8 = str_replace("#DIV/0!", "", $dta_5_8);
                                    // $dta_6_8 = str_replace("#DIV/0!", "", $dta_6_8);
                                    $dta_3_8 = strlen($dta_3_8)>12?sprintf("%.3f", $dta_3_8):$dta_3_8;
                                    $dta_4_8 = strlen($dta_4_8)>12?sprintf("%.3f", $dta_4_8):$dta_4_8;
                                    $dta_5_8 = strlen($dta_5_8)>12?sprintf("%.3f", $dta_5_8):$dta_5_8;
                                    $dta_6_8 = strlen($dta_6_8)>12?sprintf("%.3f", $dta_6_8):$dta_6_8;
                                    if($columns8['A']=='总合计'||(strpos($columns8['A'], '合计')!==false)&&(!(strpos($columns8['A'], '备注')!==false)))
                                    {
                                        $values8 .= "('SG_kcjg','库存结构','$date','".$columns8['A']."','".$columns8['A']."','$dta_3_8','$dta_4_8','$dta_5_8','$dta_6_8','$dta_7_8',NOW(),'".$user_infos['Uid']."'),";
                                    }
                                    else
                                    {
                                        if(!empty($columns8['B']))
                                        {
                                            $values8 .= "('SG_kcjg','库存结构','$date','$dta_1_8','$dta_2_8','$dta_3_8','$dta_4_8','$dta_5_8','$dta_6_8','$dta_7_8',NOW(),'".$user_infos['Uid']."'),";
                                        }
                                    }

                                    //宝鸡库
                                    $dta_3_8 = str_replace("#DIV/0!","",$columns8['O']);
                                    $dta_4_8 = str_replace("#DIV/0!","",$columns8['P']);
                                    $dta_5_8 = str_replace("#DIV/0!","",$columns8['Q']);
                                    // $dta_6_8 = str_replace("#DIV/0!","",$columns8['J']);
                                    $dta_7_8 = $data8[2]['O'];
                                    // $dta_3_8 = str_replace("#DIV/0!", "", $dta_3_8);
                                    // $dta_4_8 = str_replace("#DIV/0!", "", $dta_4_8);
                                    // $dta_5_8 = str_replace("#DIV/0!", "", $dta_5_8);
                                    // $dta_6_8 = str_replace("#DIV/0!", "", $dta_6_8);
                                    $dta_3_8 = strlen($dta_3_8)>12?sprintf("%.3f", $dta_3_8):$dta_3_8;
                                    $dta_4_8 = strlen($dta_4_8)>12?sprintf("%.3f", $dta_4_8):$dta_4_8;
                                    $dta_5_8 = strlen($dta_5_8)>12?sprintf("%.3f", $dta_5_8):$dta_5_8;
                                    // $dta_6_8 = strlen($dta_6_8)>12?sprintf("%.3f", $dta_6_8):$dta_6_8;
                                    if($columns8['A']=='总合计'||(strpos($columns8['A'], '合计')!==false)&&(!(strpos($columns8['A'], '备注')!==false)))
                                    {
                                        $values8 .= "('SG_kcjg','库存结构','$date','".$columns8['A']."','".$columns8['A']."','$dta_3_8','$dta_4_8','','$dta_5_8','$dta_7_8',NOW(),'".$user_infos['Uid']."'),";
                                    }
                                    else
                                    {
                                        if(!empty($columns8['B']))
                                        {
                                            $values8 .= "('SG_kcjg','库存结构','$date','$dta_1_8','$dta_2_8','$dta_3_8','$dta_4_8','','$dta_5_8','$dta_7_8',NOW(),'".$user_infos['Uid']."'),";
                                        }
                                    }
                                    //安口窑库
                                    $dta_3_8 = str_replace("#DIV/0!","",$columns8['R']);
                                    $dta_4_8 = str_replace("#DIV/0!","",$columns8['S']);
                                    $dta_5_8 = str_replace("#DIV/0!","",$columns8['T']);
                                    $dta_6_8 = str_replace("#DIV/0!","",$columns8['U']);
                                    $dta_7_8 = $data8[2]['R'];
                                    // $dta_3_8 = str_replace("#DIV/0!", "", $dta_3_8);
                                    // $dta_4_8 = str_replace("#DIV/0!", "", $dta_4_8);
                                    // $dta_5_8 = str_replace("#DIV/0!", "", $dta_5_8);
                                    // $dta_6_8 = str_replace("#DIV/0!", "", $dta_6_8);
                                    $dta_3_8 = strlen($dta_3_8)>12?sprintf("%.3f", $dta_3_8):$dta_3_8;
                                    $dta_4_8 = strlen($dta_4_8)>12?sprintf("%.3f", $dta_4_8):$dta_4_8;
                                    $dta_5_8 = strlen($dta_5_8)>12?sprintf("%.3f", $dta_5_8):$dta_5_8;
                                    $dta_6_8 = strlen($dta_6_8)>12?sprintf("%.3f", $dta_6_8):$dta_6_8;
                                    if($columns8['A']=='总合计'||(strpos($columns8['A'], '合计')!==false)&&(!(strpos($columns8['A'], '备注')!==false)))
                                    {
                                        $values8 .= "('SG_kcjg','库存结构','$date','".$columns8['A']."','".$columns8['A']."','$dta_3_8','$dta_4_8','$dta_5_8','$dta_6_8','$dta_7_8',NOW(),'".$user_infos['Uid']."'),";
                                    }
                                    else
                                    {
                                        if(!empty($columns8['B']))
                                        {
                                            $values8 .= "('SG_kcjg','库存结构','$date','$dta_1_8','$dta_2_8','$dta_3_8','$dta_4_8','$dta_5_8','$dta_6_8','$dta_7_8',NOW(),'".$user_infos['Uid']."'),";
                                        }
                                    }
                                    //安康库
                                    $dta_3_8 = str_replace("#DIV/0!","",$columns8['V']);
                                    $dta_4_8 = str_replace("#DIV/0!","",$columns8['W']);
                                    $dta_5_8 = str_replace("#DIV/0!","",$columns8['X']);
                                    // $dta_6_8 = str_replace("#DIV/0!","",$columns8['J']);
                                    $dta_7_8 = $data8[2]['V'];
                                    $dta_3_8 = strlen($dta_3_8)>12?sprintf("%.3f", $dta_3_8):$dta_3_8;
                                    $dta_4_8 = strlen($dta_4_8)>12?sprintf("%.3f", $dta_4_8):$dta_4_8;
                                    $dta_5_8 = strlen($dta_5_8)>12?sprintf("%.3f", $dta_5_8):$dta_5_8;
                                    // $dta_6_8 = strlen($dta_6_8)>12?sprintf("%.3f", $dta_6_8):$dta_6_8;
                                    if($columns8['A']=='总合计'||(strpos($columns8['A'], '合计')!==false)&&(!(strpos($columns8['A'], '备注')!==false)))
                                    {
                                        $values8 .= "('SG_kcjg','库存结构','$date','".$columns8['A']."','".$columns8['A']."','$dta_3_8','$dta_4_8','','$dta_5_8','$dta_7_8',NOW(),'".$user_infos['Uid']."'),";
                                    }
                                    else
                                    {
                                        if(!empty($columns8['B']))
                                        {
                                            $values8 .= "('SG_kcjg','库存结构','$date','$dta_1_8','$dta_2_8','$dta_3_8','$dta_4_8','','$dta_5_8','$dta_7_8',NOW(),'".$user_infos['Uid']."'),";
                                        }
                                    }
                                    //天水库
                                    $dta_3_8 = str_replace("#DIV/0!","",$columns8['Y']);
                                    $dta_4_8 = str_replace("#DIV/0!","",$columns8['Z']);
                                    $dta_5_8 = str_replace("#DIV/0!","",$columns8['AA']);
                                    // $dta_6_8 = str_replace("#DIV/0!","",$columns8['J']);
                                    $dta_7_8 = $data8[2]['Y'];
                                    $dta_3_8 = strlen($dta_3_8)>12?sprintf("%.3f", $dta_3_8):$dta_3_8;
                                    $dta_4_8 = strlen($dta_4_8)>12?sprintf("%.3f", $dta_4_8):$dta_4_8;
                                    $dta_5_8 = strlen($dta_5_8)>12?sprintf("%.3f", $dta_5_8):$dta_5_8;
                                    // $dta_6_8 = strlen($dta_6_8)>12?sprintf("%.3f", $dta_6_8):$dta_6_8;
                                    if($columns8['A']=='总合计'||(strpos($columns8['A'], '合计')!==false)&&(!(strpos($columns8['A'], '备注')!==false)))
                                    {
                                        $values8 .= "('SG_kcjg','库存结构','$date','".$columns8['A']."','".$columns8['A']."','$dta_3_8','$dta_4_8','','$dta_5_8','$dta_7_8',NOW(),'".$user_infos['Uid']."'),";
                                    }
                                    else
                                    {
                                        if(!empty($columns8['B']))
                                        {
                                            $values8 .= "('SG_kcjg','库存结构','$date','$dta_1_8','$dta_2_8','$dta_3_8','$dta_4_8','','$dta_5_8','$dta_7_8',NOW(),'".$user_infos['Uid']."'),";
                                        }
                                    }
                                    //天水(社棠）
                                    $dta_3_8 = str_replace("#DIV/0!","",$columns8['AB']);
                                    $dta_4_8 = str_replace("#DIV/0!","",$columns8['AC']);
                                    $dta_5_8 = str_replace("#DIV/0!","",$columns8['AD']);
                                    // $dta_6_8 = str_replace("#DIV/0!","",$columns8['J']);
                                    $dta_7_8 = $data8[2]['AB'];
                                    $dta_3_8 = strlen($dta_3_8)>12?sprintf("%.3f", $dta_3_8):$dta_3_8;
                                    $dta_4_8 = strlen($dta_4_8)>12?sprintf("%.3f", $dta_4_8):$dta_4_8;
                                    $dta_5_8 = strlen($dta_5_8)>12?sprintf("%.3f", $dta_5_8):$dta_5_8;
                                    // $dta_6_8 = strlen($dta_6_8)>12?sprintf("%.3f", $dta_6_8):$dta_6_8;
                                    if($columns8['A']=='总合计'||(strpos($columns8['A'], '合计')!==false)&&(!(strpos($columns8['A'], '备注')!==false)))
                                    {
                                        $values8 .= "('SG_kcjg','库存结构','$date','".$columns8['A']."','".$columns8['A']."','$dta_3_8','$dta_4_8','','$dta_5_8','$dta_7_8',NOW(),'".$user_infos['Uid']."'),";
                                    }
                                    else
                                    {
                                        if(!empty($columns8['B']))
                                        {
                                            $values8 .= "('SG_kcjg','库存结构','$date','$dta_1_8','$dta_2_8','$dta_3_8','$dta_4_8','','$dta_5_8','$dta_7_8',NOW(),'".$user_infos['Uid']."'),";
                                        }
                                    }
                                    //兰州库
                                    $dta_3_8 = str_replace("#DIV/0!","",$columns8['AE']);
                                    $dta_4_8 = str_replace("#DIV/0!","",$columns8['AF']);
                                    $dta_5_8 = str_replace("#DIV/0!","",$columns8['AG']);
                                    $dta_6_8 = str_replace("#DIV/0!","",$columns8['AH']);
                                    $dta_7_8 = $data8[2]['AE'];
                                    $dta_3_8 = strlen($dta_3_8)>12?sprintf("%.3f", $dta_3_8):$dta_3_8;
                                    $dta_4_8 = strlen($dta_4_8)>12?sprintf("%.3f", $dta_4_8):$dta_4_8;
                                    $dta_5_8 = strlen($dta_5_8)>12?sprintf("%.3f", $dta_5_8):$dta_5_8;
                                    $dta_6_8 = strlen($dta_6_8)>12?sprintf("%.3f", $dta_6_8):$dta_6_8;
                                    if($columns8['A']=='总合计'||(strpos($columns8['A'], '合计')!==false)&&(!(strpos($columns8['A'], '备注')!==false)))
                                    {
                                        $values8 .= "('SG_kcjg','库存结构','$date','".$columns8['A']."','".$columns8['A']."','$dta_3_8','$dta_4_8','$dta_5_8','$dta_6_8','$dta_7_8',NOW(),'".$user_infos['Uid']."'),";
                                    }
                                    else
                                    {
                                        if(!empty($columns8['B']))
                                        {
                                            $values8 .= "('SG_kcjg','库存结构','$date','$dta_1_8','$dta_2_8','$dta_3_8','$dta_4_8','$dta_5_8','$dta_6_8','$dta_7_8',NOW(),'".$user_infos['Uid']."'),";
                                        }
                                    }
                                    //南充达海库
                                    $dta_3_8 = str_replace("#DIV/0!","",$columns8['AI']);
                                    $dta_4_8 = str_replace("#DIV/0!","",$columns8['AJ']);
                                    $dta_5_8 = str_replace("#DIV/0!","",$columns8['AK']);
                                    // $dta_6_8 = str_replace("#DIV/0!","",$columns8['J']);
                                    $dta_7_8 = $data8[2]['AI'];
                                    $dta_3_8 = strlen($dta_3_8)>12?sprintf("%.3f", $dta_3_8):$dta_3_8;
                                    $dta_4_8 = strlen($dta_4_8)>12?sprintf("%.3f", $dta_4_8):$dta_4_8;
                                    $dta_5_8 = strlen($dta_5_8)>12?sprintf("%.3f", $dta_5_8):$dta_5_8;
                                    // $dta_6_8 = strlen($dta_6_8)>12?sprintf("%.3f", $dta_6_8):$dta_6_8;
                                    if($columns8['A']=='总合计'||(strpos($columns8['A'], '合计')!==false)&&(!(strpos($columns8['A'], '备注')!==false)))
                                    {
                                        $values8 .= "('SG_kcjg','库存结构','$date','".$columns8['A']."','".$columns8['A']."','$dta_3_8','$dta_4_8','','$dta_5_8','$dta_7_8',NOW(),'".$user_infos['Uid']."'),";
                                    }
                                    else
                                    {
                                        if(!empty($columns8['B']))
                                        {
                                            $values8 .= "('SG_kcjg','库存结构','$date','$dta_1_8','$dta_2_8','$dta_3_8','$dta_4_8','','$dta_5_8','$dta_7_8',NOW(),'".$user_infos['Uid']."'),";
                                        }
                                    }
                                    //重庆钢材库
                                    $dta_3_8 = str_replace("#DIV/0!","",$columns8['AL']);
                                    $dta_4_8 = str_replace("#DIV/0!","",$columns8['AM']);
                                    $dta_5_8 = str_replace("#DIV/0!","",$columns8['AN']);
                                    // $dta_6_8 = str_replace("#DIV/0!","",$columns8['J']);
                                    $dta_7_8 = $data8[2]['AL'];
                                    $dta_3_8 = strlen($dta_3_8)>12?sprintf("%.3f", $dta_3_8):$dta_3_8;
                                    $dta_4_8 = strlen($dta_4_8)>12?sprintf("%.3f", $dta_4_8):$dta_4_8;
                                    $dta_5_8 = strlen($dta_5_8)>12?sprintf("%.3f", $dta_5_8):$dta_5_8;
                                    // $dta_6_8 = strlen($dta_6_8)>12?sprintf("%.3f", $dta_6_8):$dta_6_8;
                                    if($columns8['A']=='总合计'||(strpos($columns8['A'], '合计')!==false)&&(!(strpos($columns8['A'], '备注')!==false)))
                                    {
                                        $values8 .= "('SG_kcjg','库存结构','$date','".$columns8['A']."','".$columns8['A']."','$dta_3_8','$dta_4_8','','$dta_5_8','$dta_7_8',NOW(),'".$user_infos['Uid']."'),";
                                    }
                                    else
                                    {
                                        if(!empty($columns8['B']))
                                        {
                                            $values8 .= "('SG_kcjg','库存结构','$date','$dta_1_8','$dta_2_8','$dta_3_8','$dta_4_8','','$dta_5_8','$dta_7_8',NOW(),'".$user_infos['Uid']."'),";
                                        }
                                    }
                                    //成都达海库
                                    $dta_3_8 = str_replace("#DIV/0!","",$columns8['AO']);
                                    $dta_4_8 = str_replace("#DIV/0!","",$columns8['AP']);
                                    $dta_5_8 = str_replace("#DIV/0!","",$columns8['AQ']);
                                    // $dta_6_8 = str_replace("#DIV/0!","",$columns8['J']);
                                    $dta_7_8 = $data8[2]['AO'];
                                    $dta_3_8 = strlen($dta_3_8)>12?sprintf("%.3f", $dta_3_8):$dta_3_8;
                                    $dta_4_8 = strlen($dta_4_8)>12?sprintf("%.3f", $dta_4_8):$dta_4_8;
                                    $dta_5_8 = strlen($dta_5_8)>12?sprintf("%.3f", $dta_5_8):$dta_5_8;
                                    // $dta_6_8 = strlen($dta_6_8)>12?sprintf("%.3f", $dta_6_8):$dta_6_8;
                                    if($columns8['A']=='总合计'||(strpos($columns8['A'], '合计')!==false)&&(!(strpos($columns8['A'], '备注')!==false)))
                                    {
                                        $values8 .= "('SG_kcjg','库存结构','$date','".$columns8['A']."','".$columns8['A']."','$dta_3_8','$dta_4_8','','$dta_5_8','$dta_7_8',NOW(),'".$user_infos['Uid']."'),";
                                    }
                                    else
                                    {
                                        if(!empty($columns8['B']))
                                        {
                                            $values8 .= "('SG_kcjg','库存结构','$date','$dta_1_8','$dta_2_8','$dta_3_8','$dta_4_8','','$dta_5_8','$dta_7_8',NOW(),'".$user_infos['Uid']."'),";
                                        }
                                    }
                                    //成都锦鑫库
                                    $dta_3_8 = str_replace("#DIV/0!","",$columns8['AR']);
                                    $dta_4_8 = str_replace("#DIV/0!","",$columns8['AS']);
                                    $dta_5_8 = str_replace("#DIV/0!","",$columns8['AT']);
                                    // $dta_6_8 = str_replace("#DIV/0!","",$columns8['J']);
                                    $dta_7_8 = $data8[2]['AR'];
                                    $dta_3_8 = strlen($dta_3_8)>12?sprintf("%.3f", $dta_3_8):$dta_3_8;
                                    $dta_4_8 = strlen($dta_4_8)>12?sprintf("%.3f", $dta_4_8):$dta_4_8;
                                    $dta_5_8 = strlen($dta_5_8)>12?sprintf("%.3f", $dta_5_8):$dta_5_8;
                                    // $dta_6_8 = strlen($dta_6_8)>12?sprintf("%.3f", $dta_6_8):$dta_6_8;
                                    if($columns8['A']=='总合计'||(strpos($columns8['A'], '合计')!==false)&&(!(strpos($columns8['A'], '备注')!==false)))
                                    {
                                        $values8 .= "('SG_kcjg','库存结构','$date','".$columns8['A']."','".$columns8['A']."','$dta_3_8','$dta_4_8','','$dta_5_8','$dta_7_8',NOW(),'".$user_infos['Uid']."'),";
                                    }
                                    else
                                    {
                                        if(!empty($columns8['B']))
                                        {
                                            $values8 .= "('SG_kcjg','库存结构','$date','$dta_1_8','$dta_2_8','$dta_3_8','$dta_4_8','','$dta_5_8','$dta_7_8',NOW(),'".$user_infos['Uid']."'),";
                                        }
                                    }
                                    //成都自储库存
                                    $dta_3_8 = str_replace("#DIV/0!","",$columns8['AU']);
                                    $dta_4_8 = str_replace("#DIV/0!","",$columns8['AV']);
                                    $dta_5_8 = str_replace("#DIV/0!","",$columns8['AW']);
                                    // $dta_6_8 = str_replace("#DIV/0!","",$columns8['J']);
                                    $dta_7_8 = $data8[2]['AU'];
                                    $dta_3_8 = strlen($dta_3_8)>12?sprintf("%.3f", $dta_3_8):$dta_3_8;
                                    $dta_4_8 = strlen($dta_4_8)>12?sprintf("%.3f", $dta_4_8):$dta_4_8;
                                    $dta_5_8 = strlen($dta_5_8)>12?sprintf("%.3f", $dta_5_8):$dta_5_8;
                                    // $dta_6_8 = strlen($dta_6_8)>12?sprintf("%.3f", $dta_6_8):$dta_6_8;
                                    if($columns8['A']=='总合计'||(strpos($columns8['A'], '合计')!==false)&&(!(strpos($columns8['A'], '备注')!==false)))
                                    {
                                        $values8 .= "('SG_kcjg','库存结构','$date','".$columns8['A']."','".$columns8['A']."','$dta_3_8','$dta_4_8','','$dta_5_8','$dta_7_8',NOW(),'".$user_infos['Uid']."'),";
                                    }
                                    else
                                    {
                                        if(!empty($columns8['B']))
                                        {
                                            $values8 .= "('SG_kcjg','库存结构','$date','$dta_1_8','$dta_2_8','$dta_3_8','$dta_4_8','','$dta_5_8','$dta_7_8',NOW(),'".$user_infos['Uid']."'),";
                                        }
                                    }
                                    //绵阳库
                                    $dta_3_8 = str_replace("#DIV/0!","",$columns8['AX']);
                                    $dta_4_8 = str_replace("#DIV/0!","",$columns8['AY']);
                                    $dta_5_8 = str_replace("#DIV/0!","",$columns8['AZ']);
                                    // $dta_6_8 = str_replace("#DIV/0!","",$columns8['J']);
                                    $dta_7_8 = $data8[2]['AX'];
                                    $dta_3_8 = strlen($dta_3_8)>12?sprintf("%.3f", $dta_3_8):$dta_3_8;
                                    $dta_4_8 = strlen($dta_4_8)>12?sprintf("%.3f", $dta_4_8):$dta_4_8;
                                    $dta_5_8 = strlen($dta_5_8)>12?sprintf("%.3f", $dta_5_8):$dta_5_8;
                                    // $dta_6_8 = strlen($dta_6_8)>12?sprintf("%.3f", $dta_6_8):$dta_6_8;
                                    if($columns8['A']=='总合计'||(strpos($columns8['A'], '合计')!==false)&&(!(strpos($columns8['A'], '备注')!==false)))
                                    {
                                        $values8 .= "('SG_kcjg','库存结构','$date','".$columns8['A']."','".$columns8['A']."','$dta_3_8','$dta_4_8','','$dta_5_8','$dta_7_8',NOW(),'".$user_infos['Uid']."'),";
                                    }
                                    else
                                    {
                                        if(!empty($columns8['B']))
                                        {
                                            $values8 .= "('SG_kcjg','库存结构','$date','$dta_1_8','$dta_2_8','$dta_3_8','$dta_4_8','','$dta_5_8','$dta_7_8',NOW(),'".$user_infos['Uid']."'),";
                                        }
                                    }
                                    //张吉怀
                                    $dta_3_8 = str_replace("#DIV/0!","",$columns8['BA']);
                                    $dta_4_8 = str_replace("#DIV/0!","",$columns8['BB']);
                                    $dta_5_8 = str_replace("#DIV/0!","",$columns8['BC']);
                                    // $dta_6_8 = str_replace("#DIV/0!","",$columns8['J']);
                                    $dta_7_8 = $data8[2]['BA'];
                                    $dta_3_8 = strlen($dta_3_8)>12?sprintf("%.3f", $dta_3_8):$dta_3_8;
                                    $dta_4_8 = strlen($dta_4_8)>12?sprintf("%.3f", $dta_4_8):$dta_4_8;
                                    $dta_5_8 = strlen($dta_5_8)>12?sprintf("%.3f", $dta_5_8):$dta_5_8;
                                    // $dta_6_8 = strlen($dta_6_8)>12?sprintf("%.3f", $dta_6_8):$dta_6_8;
                                    if($columns8['A']=='总合计'||(strpos($columns8['A'], '合计')!==false)&&(!(strpos($columns8['A'], '备注')!==false)))
                                    {
                                        $values8 .= "('SG_kcjg','库存结构','$date','".$columns8['A']."','".$columns8['A']."','$dta_3_8','$dta_4_8','','$dta_5_8','$dta_7_8',NOW(),'".$user_infos['Uid']."'),";
                                    }
                                    else
                                    {
                                        if(!empty($columns8['B']))
                                        {
                                            $values8 .= "('SG_kcjg','库存结构','$date','$dta_1_8','$dta_2_8','$dta_3_8','$dta_4_8','','$dta_5_8','$dta_7_8',NOW(),'".$user_infos['Uid']."'),";
                                        }
                                    }
                                    //富轧                       
                                    $dta_3_8 = str_replace("#DIV/0!","",$columns8['BD']);
                                    $dta_4_8 = str_replace("#DIV/0!","",$columns8['BE']);
                                    $dta_5_8 = str_replace("#DIV/0!","",$columns8['BF']);
                                    // $dta_6_8 = str_replace("#DIV/0!","",$columns8['J']);
                                    $dta_7_8 = $data8[2]['BD'];
                                    $dta_3_8 = strlen($dta_3_8)>12?sprintf("%.3f", $dta_3_8):$dta_3_8;
                                    $dta_4_8 = strlen($dta_4_8)>12?sprintf("%.3f", $dta_4_8):$dta_4_8;
                                    $dta_5_8 = strlen($dta_5_8)>12?sprintf("%.3f", $dta_5_8):$dta_5_8;
                                    // $dta_6_8 = strlen($dta_6_8)>12?sprintf("%.3f", $dta_6_8):$dta_6_8;
                                    if($columns8['A']=='总合计'||(strpos($columns8['A'], '合计')!==false)&&(!(strpos($columns8['A'], '备注')!==false)))
                                    {
                                        $values8 .= "('SG_kcjg','库存结构','$date','".$columns8['A']."','".$columns8['A']."','$dta_3_8','$dta_4_8','','$dta_5_8','$dta_7_8',NOW(),'".$user_infos['Uid']."'),";
                                    }
                                    else
                                    {
                                        if(!empty($columns8['B']))
                                        {
                                            $values8 .= "('SG_kcjg','库存结构','$date','$dta_1_8','$dta_2_8','$dta_3_8','$dta_4_8','','$dta_5_8','$dta_7_8',NOW(),'".$user_infos['Uid']."'),";
                                        }
                                    }
                                    //龙丰                      
                                    $dta_3_8 = str_replace("#DIV/0!","",$columns8['BG']);
                                    $dta_4_8 = str_replace("#DIV/0!","",$columns8['BH']);
                                    $dta_5_8 = str_replace("#DIV/0!","",$columns8['BI']);
                                    // $dta_6_8 = str_replace("#DIV/0!","",$columns8['J']);
                                    $dta_7_8 = $data8[2]['BG'];
                                    $dta_3_8 = strlen($dta_3_8)>12?sprintf("%.3f", $dta_3_8):$dta_3_8;
                                    $dta_4_8 = strlen($dta_4_8)>12?sprintf("%.3f", $dta_4_8):$dta_4_8;
                                    $dta_5_8 = strlen($dta_5_8)>12?sprintf("%.3f", $dta_5_8):$dta_5_8;
                                    // $dta_6_8 = strlen($dta_6_8)>12?sprintf("%.3f", $dta_6_8):$dta_6_8;
                                    if($columns8['A']=='总合计'||(strpos($columns8['A'], '合计')!==false)&&(!(strpos($columns8['A'], '备注')!==false)))
                                    {
                                        $values8 .= "('SG_kcjg','库存结构','$date','".$columns8['A']."','".$columns8['A']."','$dta_3_8','$dta_4_8','','$dta_5_8','$dta_7_8',NOW(),'".$user_infos['Uid']."'),";
                                    }
                                    else
                                    {
                                        if(!empty($columns8['B']))
                                        {
                                            $values8 .= "('SG_kcjg','库存结构','$date','$dta_1_8','$dta_2_8','$dta_3_8','$dta_4_8','','$dta_5_8','$dta_7_8',NOW(),'".$user_infos['Uid']."'),";
                                        }
                                    }
                                }
                            }
                            if($values8)
                            {
                                $this->_dao->execute("delete from sg_data_table where dta_ym='$date' and dta_type='SG_kcjg' and dta_vartype='库存结构'");
                                $tsql = substr($values8, 0, -1);
                                $this->_dao->execute($basesql8.$tsql);
                            }

                            //BJ列之后的内容
                            $basesql9 = "insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,dta_3,dta_4,dta_5,dta_6,dta_7,dta_8,createtime,createuser) values";
                            foreach($data8 as $row9=>$columns9) //1,2,3...
                            {
                                if($row9>=4)
                                {
                                    if($columns9['A'])
                                    {
                                        $tmp9 = $columns9['A'];
                                    }
                                    
                                    $dta_1_9 = $tmp9;
                                    $dta_2_9 = $columns9['B'];  //规格
                                    //柞水
                                    $dta_3_9 = str_replace("#DIV/0!","",$columns9['BJ']);
                                    $dta_4_9 = str_replace("#DIV/0!","",$columns9['BK']);
                                    // $dta_5_9 = str_replace("#DIV/0!","",$columns9['E']);
                                    // $dta_6_9 = str_replace("#DIV/0!","",$columns9['F']);
                                    $dta_8_9 = $data8[2]['BJ'];
                                    $dta_3_9 = strlen($dta_3_9)>12?sprintf("%.3f", $dta_3_9):$dta_3_9;
                                    $dta_4_9 = strlen($dta_4_9)>12?sprintf("%.3f", $dta_4_9):$dta_4_9;
                                    // $dta_5_9 = strlen($dta_5_9)>12?sprintf("%.3f", $dta_5_9):$dta_5_9;
                                    // $dta_6_9 = strlen($dta_6_9)>12?sprintf("%.3f", $dta_6_9):$dta_6_9;
                                    if($columns9['A']=='总合计'||(strpos($columns9['A'], '合计')!==false)&&(!(strpos($columns9['A'], '备注')!==false)))
                                    {
                                        $values9 .= "('SG_kcjg','库存结构','$date','".$columns9['A']."','".$columns9['A']."','$dta_3_9','$dta_4_9','','','','$dta_8_9',NOW(),'".$user_infos['Uid']."'),";
                                    }
                                    else
                                    {
                                        if(!empty($columns9['B']))
                                        {
                                            $values9 .= "('SG_kcjg','库存结构','$date','$dta_1_9','$dta_2_9','$dta_3_9','$dta_4_9','','','','$dta_8_9',NOW(),'".$user_infos['Uid']."'),";
                                        }
                                    }
                                    //丰义
                                    $dta_3_9 = str_replace("#DIV/0!","",$columns9['BL']);
                                    $dta_4_9 = str_replace("#DIV/0!","",$columns9['BM']);
                                    // $dta_5_9 = str_replace("#DIV/0!","",$columns9['E']);
                                    // $dta_6_9 = str_replace("#DIV/0!","",$columns9['F']);
                                    $dta_8_9 = $data8[2]['BL'];
                                    $dta_3_9 = strlen($dta_3_9)>12?sprintf("%.3f", $dta_3_9):$dta_3_9;
                                    $dta_4_9 = strlen($dta_4_9)>12?sprintf("%.3f", $dta_4_9):$dta_4_9;
                                    // $dta_5_9 = strlen($dta_5_9)>12?sprintf("%.3f", $dta_5_9):$dta_5_9;
                                    // $dta_6_9 = strlen($dta_6_9)>12?sprintf("%.3f", $dta_6_9):$dta_6_9;
                                    if($columns9['A']=='总合计'||(strpos($columns9['A'], '合计')!==false)&&(!(strpos($columns9['A'], '备注')!==false)))
                                    {
                                        $values9 .= "('SG_kcjg','库存结构','$date','".$columns9['A']."','".$columns9['A']."','$dta_3_9','$dta_4_9','','','','$dta_8_9',NOW(),'".$user_infos['Uid']."'),";
                                    }
                                    else
                                    {
                                        if(!empty($columns9['B']))
                                        {
                                            $values9 .= "('SG_kcjg','库存结构','$date','$dta_1_9','$dta_2_9','$dta_3_9','$dta_4_9','','','','$dta_8_9',NOW(),'".$user_infos['Uid']."'),";
                                        }
                                    }

                                    //韩城库自储库存
                                    $dta_3_9 = str_replace("#DIV/0!","",$columns9['BN']);
                                    // $dta_4_9 = str_replace("#DIV/0!","",$columns9['BM']);
                                    // $dta_5_9 = str_replace("#DIV/0!","",$columns9['E']);
                                    // $dta_6_9 = str_replace("#DIV/0!","",$columns9['F']);
                                    $dta_8_9 = $data8[2]['BN'];
                                    $dta_3_9 = strlen($dta_3_9)>12?sprintf("%.3f", $dta_3_9):$dta_3_9;
                                    // $dta_4_9 = strlen($dta_4_9)>12?sprintf("%.3f", $dta_4_9):$dta_4_9;
                                    // $dta_5_9 = strlen($dta_5_9)>12?sprintf("%.3f", $dta_5_9):$dta_5_9;
                                    // $dta_6_9 = strlen($dta_6_9)>12?sprintf("%.3f", $dta_6_9):$dta_6_9;
                                    if($columns9['A']=='总合计'||(strpos($columns9['A'], '合计')!==false)&&(!(strpos($columns9['A'], '备注')!==false)))
                                    {
                                        $values9 .= "('SG_kcjg','库存结构','$date','".$columns9['A']."','".$columns9['A']."','$dta_3_9','','','','','$dta_8_9',NOW(),'".$user_infos['Uid']."'),";
                                    }
                                    else
                                    {
                                        if(!empty($columns9['B']))
                                        {
                                            $values9 .= "('SG_kcjg','库存结构','$date','$dta_1_9','$dta_2_9','$dta_3_9','','','','','$dta_8_9',NOW(),'".$user_infos['Uid']."'),";
                                        }
                                    }

                                    //韩城
                                    $dta_3_9 = str_replace("#DIV/0!","",$columns9['BO']);
                                    $dta_4_9 = str_replace("#DIV/0!","",$columns9['BP']);
                                    $dta_5_9 = str_replace("#DIV/0!","",$columns9['BQ']);
                                    $dta_6_9 = str_replace("#DIV/0!","",$columns9['BR']);
                                    $dta_7_9 = str_replace("#DIV/0!","",$columns9['BS']);
                                    $dta_8_9 = $data8[2]['BO'];
                                    $dta_3_9 = strlen($dta_3_9)>12?sprintf("%.3f", $dta_3_9):$dta_3_9;
                                    $dta_4_9 = strlen($dta_4_9)>12?sprintf("%.3f", $dta_4_9):$dta_4_9;
                                    $dta_5_9 = strlen($dta_5_9)>12?sprintf("%.3f", $dta_5_9):$dta_5_9;
                                    $dta_6_9 = strlen($dta_6_9)>12?sprintf("%.3f", $dta_6_9):$dta_6_9;
                                    $dta_7_9 = strlen($dta_7_9)>12?sprintf("%.3f", $dta_7_9):$dta_7_9;
                                    if($columns9['A']=='总合计'||(strpos($columns9['A'], '合计')!==false)&&(!(strpos($columns9['A'], '备注')!==false)))
                                    {
                                        $values9 .= "('SG_kcjg','库存结构','$date','".$columns9['A']."','".$columns9['A']."','$dta_3_9','$dta_4_9','$dta_5_9','$dta_6_9','$dta_7_9','$dta_8_9',NOW(),'".$user_infos['Uid']."'),";
                                    }
                                    else
                                    {
                                        if(!empty($columns9['B']))
                                        {
                                            $values9 .= "('SG_kcjg','库存结构','$date','$dta_1_9','$dta_2_9','$dta_3_9','$dta_4_9','$dta_5_9','$dta_6_9','$dta_7_9','$dta_8_9',NOW(),'".$user_infos['Uid']."'),";
                                        }
                                    }

                                    //汉钢
                                    $dta_3_9 = str_replace("#DIV/0!","",$columns9['BT']);
                                    $dta_4_9 = str_replace("#DIV/0!","",$columns9['BU']);
                                    $dta_5_9 = str_replace("#DIV/0!","",$columns9['BV']);
                                    $dta_6_9 = str_replace("#DIV/0!","",$columns9['BW']);
                                    $dta_7_9 = str_replace("#DIV/0!","",$columns9['BX']);
                                    $dta_8_9 = $data8[2]['BT'];
                                    $dta_3_9 = strlen($dta_3_9)>12?sprintf("%.3f", $dta_3_9):$dta_3_9;
                                    $dta_4_9 = strlen($dta_4_9)>12?sprintf("%.3f", $dta_4_9):$dta_4_9;
                                    $dta_5_9 = strlen($dta_5_9)>12?sprintf("%.3f", $dta_5_9):$dta_5_9;
                                    $dta_6_9 = strlen($dta_6_9)>12?sprintf("%.3f", $dta_6_9):$dta_6_9;
                                    $dta_7_9 = strlen($dta_7_9)>12?sprintf("%.3f", $dta_7_9):$dta_7_9;
                                    if($columns9['A']=='总合计'||(strpos($columns9['A'], '合计')!==false)&&(!(strpos($columns9['A'], '备注')!==false)))
                                    {
                                        $values9 .= "('SG_kcjg','库存结构','$date','".$columns9['A']."','".$columns9['A']."','$dta_3_9','$dta_4_9','$dta_5_9','$dta_6_9','$dta_7_9','$dta_8_9',NOW(),'".$user_infos['Uid']."'),";
                                    }
                                    else
                                    {
                                        if(!empty($columns9['B']))
                                        {
                                            $values9 .= "('SG_kcjg','库存结构','$date','$dta_1_9','$dta_2_9','$dta_3_9','$dta_4_9','$dta_5_9','$dta_6_9','$dta_7_9','$dta_8_9',NOW(),'".$user_infos['Uid']."'),";
                                        }
                                    }

                                    //合计
                                    $dta_3_9 = str_replace("#DIV/0!","",$columns9['BY']);
                                    $dta_4_9 = str_replace("#DIV/0!","",$columns9['BZ']);
                                    $dta_5_9 = str_replace("#DIV/0!","",$columns9['CA']);
                                    $dta_6_9 = str_replace("#DIV/0!","",$columns9['CB']);
                                    $dta_7_9 = str_replace("#DIV/0!","",$columns9['CD']);
                                    $dta_8_9 = $data8[2]['BY'];
                                    $dta_3_9 = strlen($dta_3_9)>12?sprintf("%.3f", $dta_3_9):$dta_3_9;
                                    $dta_4_9 = strlen($dta_4_9)>12?sprintf("%.3f", $dta_4_9):$dta_4_9;
                                    $dta_5_9 = strlen($dta_5_9)>12?sprintf("%.3f", $dta_5_9):$dta_5_9;
                                    $dta_6_9 = strlen($dta_6_9)>12?sprintf("%.3f", $dta_6_9):$dta_6_9;
                                    $dta_7_9 = strlen($dta_7_9)>12?sprintf("%.3f", $dta_7_9):$dta_7_9;
                                    if($columns9['A']=='总合计'||(strpos($columns9['A'], '合计')!==false)&&(!(strpos($columns9['A'], '备注')!==false)))
                                    {
                                        $values9 .= "('SG_kcjg','库存结构','$date','".$columns9['A']."','".$columns9['A']."','$dta_3_9','$dta_4_9','$dta_5_9','$dta_6_9','$dta_7_9','$dta_8_9',NOW(),'".$user_infos['Uid']."'),";
                                    }
                                    else
                                    {
                                        if(!empty($columns9['B']))
                                        {
                                            $values9 .= "('SG_kcjg','库存结构','$date','$dta_1_9','$dta_2_9','$dta_3_9','$dta_4_9','$dta_5_9','$dta_6_9','$dta_7_9','$dta_8_9',NOW(),'".$user_infos['Uid']."'),";
                                        }
                                    }
                                }
                            }
                            if($values9)
                            {
                                $tsql = substr($values9, 0, -1);
                                $this->_dao->execute($basesql9.$tsql);
                                $tmpsheetName[] = mb_convert_encoding($sheetNames[$sheetIndex],"GB2312","UTF-8");
                            }
                            //库存结构表导入 end
                        break;
                        default:
                        break;
                    }
                }
                if($flag)
                {
                    $sheetName = implode(',',$tmpsheetName);
                    $this->_dao->execute("insert into sg_data_table_log(uptype,filepath,filename,sheetname,createtime,createuser,uptypedate) value('2','$file','$filename','$sheetName',NOW(),'".$user_infos['Uid']."','$date')");
                    $response["Success"] = 1;
                    $response['Message'] = "导入成功";
                }

            }
        }
        echo $this->pri_JSON($response);
        exit;
        // $year_arr = explode('年', $filename);
        // $month_arr = explode('月', $year_arr[1]);
        // $day_arr = explode('日', $month_arr[1]);
        // $date = $year_arr[0].'/'.$month_arr[0].'/'.$day_arr[0];
        // $year_arr = explode('年', $filename);
        // $month_arr = explode('月', $year_arr[1]);
        // $day_arr = explode('日', $month_arr[1]);
        // $date = date('Y-m-d',strtotime($year_arr[0].'/'.$month_arr[0].'/'.$day_arr[0]));

    }

    private function pri_JSON($array)
    {
        $this->pri_arrayRecursive($array, 'urlencode', true);
        $json = json_encode($array);
        return iconv("GB2312", "UTF-8", urldecode($json));
    }

    private function pri_arrayRecursive(&$array, $function, $apply_to_keys_also = false)
    {
        static $recursive_counter = 0;
        if (++$recursive_counter > 1000) {
            die('possible deep recursion attack');
        }
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $this->pri_arrayRecursive($array[$key], $function, $apply_to_keys_also);
            } else {
                $array[$key] = $function($value);
            }
            if ($apply_to_keys_also && is_string($key)) {
                $new_key = $function($key);
                if ($new_key != $key) {
                    $array[$new_key] = $array[$key];
                    unset($array[$key]);
                }
            }
        }
        $recursive_counter--;
	}
	
	//数组转码
    public function array_iconv($str, $in_charset="utf-8", $out_charset="gb2312")
    {
        if (is_array($str)) {
            foreach ($str as $k => $v) {
                $str[$k] = $this->array_iconv($v);
            }
            return $str;
        } else {
            if (is_string($str)) {
                return mb_convert_encoding($str, $out_charset, $in_charset);
            } else {
                return $str;
            }
        }
    }
}

?>