*{ margin:0; padding:0; font-family:"Microsoft YaHei";}
.nav{ position:fixed;bottom:0; left:0; right:0; height:52px; background:url(../../images/navbg.jpg) repeat-x 0 0; width:100%;cursor:pointer;}
.nav li{ float:left; width:16.666%; text-align:center; line-height:52px; font:normal 18px/48px "Microsoft YaHei"; cursor:pointer; color:#1367BB;}
.nav li.active{  background:url(../../images/navactive.jpg) repeat-x 0 0; background-size:100%; border:2px solid #68D8FE; height:52px; line-height:48px; font-weight:bold; color:#00FFFF;}
.nav li:hover{ color:#00FFFF;}

.header h1{ background:url(../../images/headercenterbg.png) no-repeat 0 0; height:88px; width:880px; margin:0 auto; text-align:center;}
.header h1 *{ color:#01C4F7; font-family:"Microsoft YaHei";}
.header h1 span.font1{ font-size:32px; letter-spacing:5px; font-weight:bold;  padding:10px 0 8px; display:block;}
.header h1 span.font2{ font-size:16px;}

.header{ position:relative;background:url(../../images/headerbg.jpg) repeat-x 0 0; width:100%; height:89px;}
.header:after{ background:url(../../images/headerbian.jpg) no-repeat 0 0; width:122px; height:89px; content:""; position:absolute; display:block; top:0; left:0;}
.header:before{ background:url(../../images/headerbian.jpg) no-repeat 0 0; width:122px; height:89px; content:""; position:absolute; display:block; top:0; right:0; transform:rotateY(180deg);}

.center { /*padding: 0 .8rem;*/ position:absolute; top:90px; bottom:70px; left:0; right:0;}

.zuo{ position:fixed; z-index:9999; left:10px; width:40px; height:40px; background:rgba(32,47,85,0.5); border-radius:50%; top:50%; margin-top:-20px;}
.zuo:after{ content:""; position:absolute; display:block; left:15px; top:12px; width:12px; height:12px; transform:rotate(-45deg); border-top:2px solid #3FDDFF; border-left:2px solid #3FDDFF;}
.zuo:hover{ opacity:0.8;}

.you{ position:fixed; z-index:9999; right:10px; width:40px; height:40px;background:rgba(32,47,85,0.5); border-radius:50%; top:50%; margin-top:-20px;}
.you:after{ content:""; position:absolute; display:block; right:15px; top:12px; width:12px; height:12px; transform:rotate(45deg); border-top:2px solid #3FDDFF; border-right:2px solid #3FDDFF;}
.you:hover{ opacity:0.8;}

.center .center-left { width: 24%; margin-left:0.5%;margin-right:0.5%}
.center .center-left .left-top { width: 100%; height: 50%; }
.center .center-left .left-top h1 { font-size: .4rem; transform: rotateX(0deg); align-items: center; margin-bottom: 8px; position: relative; -webkit-transform-style: preserve-3d; -moz-transform-style: preserve-3d; -ms-transform-style: preserve-3d; transform-style: preserve-3d; transform-origin: 50% 50%; transition: transform 500ms cubic-bezier(.15, .52, .5, .9) 0s; transition: all 1s;}
.title { width: 100%; /*font-size:0.26rem;*/ font-weight:bold; text-align:center; font-family:"Microsoft YaHei"; color:#01C4F7;}
.navbar{font-size:16px;	font-weight:bold;text-align:center;}

.center .center-left .left-cen { width: 100%; height: 50%;margin-top:20px; }
.center .center-left .left-cen .company { width: calc(100% - .2rem); height: calc(100% - .6rem); margin-left: .1rem; margin-top: .1rem; padding: .2rem; box-sizing: border-box;}
.center .center-left .left-cen .company li { height: .3rem; line-height: .3rem}
.center .center-left .left-bottom { width: 100%; height: 3.2rem}
.center .center-left .bottom-b { /*width: calc(100% - 0.3rem);*/ width:100%;height: 90%; /*margin-left: .3rem;*/}
.center .center-cen { width: 49%; /*margin-top:0.3rem;*/ margin-left:0.5%;margin-right:0.5%}
.center .center-cen .cen-top { width: 100%; height: 50%;  position: relative; background:none;	border:0;}
.center-cen .border:after{ border-left:0; border-right:0;}
.center-cen .border:before{ border-top:0; border-bottom:0;}

.center .center-cen .bottom-b { /* width: calc(100% - 0.2rem); */ width:100%; height: 90%;margin-left: .3rem;}
.center .center-right { width: 24%; margin-left:0.5%;margin-right:0.5% }
.center .center-right .right-top { width: 100%; height: 50%;}
.center .center-right .right-top .right-top-top { width: 100%; height: 90%;}
.center .center-right .right-cen { width: 100%; height: 50%;margin-top:20px; transition: all 1s; cursor: pointer; position: relative}
 .center .center-right .right-cen .right-cen-cent { width: 100%; height: 90%;}

.el-loading-mask { position: absolute; z-index: 2000; margin: 0; top: 0; right: 0; bottom: 0; left: 0;  transition: opacity .3s;}
.el-loading-parent--relative { position: relative!important;}
.el-loading-parent--hidden { overflow: hidden!important;}
.el-loading-mask { position: absolute; z-index: 2000; margin: 0; top: 0; right: 0; bottom: 0; left: 0; transition: opacity .3s;}
.el-loading-mask.is-fullscreen { position: fixed;}
.el-loading-mask.is-fullscreen .el-loading-spinner { margin-top: -25px;}
.el-loading-mask.is-fullscreen .el-loading-spinner .circular { height: 50px;  width: 50px;}
.el-loading-spinner { top: 50%; margin-top: -21px; width: 100%; text-align: center; position: absolute;}
.el-loading-spinner .el-loading-text { color: #0c69b3; margin: 3px 0; font-size: 14px;}
.el-loading-spinner .circular { height: 42px; width: 42px; animation: k 2s linear infinite;}
.el-loading-spinner .path { animation: j 1.5s ease-in-out infinite; stroke-dasharray: 90,150; stroke-dashoffset: 0; stroke-width: 2; stroke: #0c69b3; stroke-linecap: round;}
.el-loading-spinner i { color: #0c69b3;}
.el-loading-fade-enter,.el-loading-fade-leave-active { opacity: 0;}

@keyframes j {
    0% {
        stroke-dasharray: 1,200;
        stroke-dashoffset: 0
    }

    50% {
        stroke-dasharray: 90,150;
        stroke-dashoffset: -40px
    }

    to {
        stroke-dasharray: 90,150;
        stroke-dashoffset: -120px
    }
}

.layui-layer-content{color:#000;}
.center .chajia{ float:left; width:6.6rem; height:4.5rem; margin-top:8%;}
.center .chajia:first-child{  margin-left:0.5rem;}
.chajia .right-top-top,.chajia .bottom-b{ position:relative;}