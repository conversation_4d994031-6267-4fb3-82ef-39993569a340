<?php
class  cbmodel<PERSON><PERSON> extends Dao{
	public function __construct($writer){
		 parent::__construct( $writer );
	}
	//通过guid取得用户信息
	public function get_userinfo_byguid($GUID){
		$sql = "select * from app_session_temp where  GUID ='$GUID'  order by  LoginDate DESC  limit 1";
		return $this->getRow($sql);
	}
	
	//获取用户权限和用户名
	public function get_license_privilege($Uid){
		$sql = "select * from app_license,app_license_privilege where  app_license.id=app_license_privilege.lid and app_license_privilege.Uid='$Uid' and app_license_privilege.mc_type=1 limit 1";
		return $this->getRow($sql);
	}
	
	//获取研究中心环渤海贴现率
	public function get_drc_hbh_txl($Date){
		$sql = "SELECT * FROM busbankrate WHERE   `rdate` <='$Date' order by rdate desc  limit 1";
		return $this->getRow($sql);
	}
	//获取ng_ChengBenParameter3表信息
	public function get_ng_ChengBenParameter3($Date,$Type){
		$sql = "select * from ng_ChengBenParameter3 where  Date <='$Date' and  Type ='$Type' and isDelete ='0' order by Date desc,id desc  limit 1";
		//echo $sql;
		return $this->getRow($sql);
	}
	//获取ng_ChengBenParameter3表信息
	public function get_ng_ChengBenParameter3_byid($id){
		$sql = "select * from ng_ChengBenParameter3 where  id='$id'";
		return $this->getRow($sql);
	}
	//获取ng_ChengBenParameter3表信息
	public function get_ng_ChengBenParameter3_nextday($Date,$Type){
		$sql = "select Date from ng_ChengBenParameter3 where   Date >'$Date' and  Type ='$Type' and isDelete =0 order by Date asc,id asc  limit 1";
		return $this->getOne($sql);
	}
	//获取get_ng_TieHeJinChengBenIndex表信息
	public function get_ng_TieHeJinChengBenIndex($Date,$type){
		$sql = "select * from ng_TieHeJinChengBenIndex where  Date ='$Date' and  type ='$type' order by id desc  limit 1";
		return $this->getRow($sql);
	}
	
	//根据ng_ModifyChengBenParameterTask表信息看成本指数是否可以计算
	public function get_can_calc(){
		$sql = "select * from ng_ModifyChengBenParameterTask where  status !=2 and  type =1 order by pid desc ";
		return $this->getRow($sql);
	}
	
	//取出需要计算的ng_ModifyChengBenParameterTask表信息
	public function get_ng_ModifyChengBenParameterTask($type){
		$sql = "select * from ng_ModifyChengBenParameterTask where  status =0 and  type ='$type' order by pid desc ";
		echo $sql;//exit;
		return $this->query($sql);
	}
	
	//更新ng_ModifyChengBenParameterTask表信息
	public function update_ng_ModifyChengBenParameterTask($today_now){
		$sql = "update ng_ModifyChengBenParameterTask set status = 0,cleardate='$today_now' where status = 1 and  type = 2 and cleardate!= '$today_now' ";
		return $this->execute($sql);
	}
	//更新update_ng_ModifyChengBenParameterTask_ing表信息
	public function update_ng_ModifyChengBenParameterTask_ing($pid,$status,$today_now){
		$sql = "update ng_ModifyChengBenParameterTask set status = '$status' where status = 0 and  type = 2 and pid = '$pid' ";
		return $this->execute($sql);
	}
	//更新update_ng_ModifyChengBenParameterTask_ing表信息
	public function update_ng_ModifyChengBenParameterTask10_ing($id,$status){
		$sql = "update ng_ModifyChengBenParameterTask set status = '$status' where status = 0  and id = '$id' ";
		return $this->execute($sql);
	}
	//更新update_ng_ModifyChengBenParameterTask_ing表信息
	public function update_ng_ModifyChengBenParameterTask_ed($pid,$status,$today_now){
		$sql = "update ng_ModifyChengBenParameterTask set status = '$status' where status = 1 and  type = 2 and pid = '$pid' ";
		return $this->execute($sql);
	}
	//更新update_ng_ModifyChengBenParameterTask_ing表信息
	public function update_ng_ModifyChengBenParameterTask10_ed($id,$status){
		$sql = "update ng_ModifyChengBenParameterTask set status = '$status' where status = 1 and id = '$id' ";
		return $this->execute($sql);
	}
	//更新update_ng_TieHeJinChengBenIndex表信息
	public function update_ng_TieHeJinChengBenIndex($Date,$type,$indexValue){
		$sql = "update ng_TieHeJinChengBenIndex set indexValue = '$indexValue' where Date ='$Date' and  type ='$type' ";
		return $this->execute($sql);
	}
	
	//插入insert_ng_TieHeJinChengBenIndex表信息
	public function insert_ng_TieHeJinChengBenIndex($Date,$type,$indexValue){
		$sql = "insert into ng_TieHeJinChengBenIndex set indexValue = '$indexValue' , Date ='$Date' ,  type ='$type' ";
		return $this->execute($sql);
	}

	//插入insert_ng_TieHeJinChengBenIndex表信息
	public function insert_ng_TieHeJinChengBenIndex_value($value){
		$sql = "insert into ng_TieHeJinChengBenIndex  (`Date`,`type`,`indexValue`) VALUES $value";
		return $this->execute($sql);
	}
	
	//删除delete_ng_TieHeJinChengBenIndex表信息
	public function delete_ng_TieHeJinChengBenIndex($sdate,$type,$edate){
		$sql = "delete from ng_TieHeJinChengBenIndex where Date >='$sdate' and Date <'$edate' and type in ($type) ";
		return $this->execute($sql);
	}
	
	//获取今日市场价格
	public function get_marketconditions_price_6($topictures,$mconmanagedate){
		$sql = "select price,topicture,mconmanagedate from marketconditions where topicture in (" . $topictures . ")  AND mconmanagedate >'" . $mconmanagedate . " 00:00:00' AND mconmanagedate <'" . $mconmanagedate . "  23:59:59'  order by topicture asc";
		$result = $this->query( $sql );
		
		foreach ( $result as $key => $value ){
			if (strstr ( $value ['price'], "-" )){
				$avgprice = explode ( "-", $value ['price'] );
				$value ['price'] = round ( ($avgprice ['0'] + $avgprice ['1']) / 2, 2 );
			}
			$tprice [$value['topicture']] = $value ['price'];
		}
		return $tprice;
	}
	
	//获取今日市场价格
	public function get_marketconditions_price_7($topictures,$mconmanagedate){
		$sql = "select price,mastertopid,mconmanagedate from marketconditions where mastertopid in (" . $topictures . ")  AND mconmanagedate >'" . $mconmanagedate . " 00:00:00' AND mconmanagedate <'" . $mconmanagedate . "  23:59:59'  order by mastertopid asc ";
		$result = $this->query( $sql );
		foreach ( $result as $key => $value ){
			if (strstr ( $value ['price'], "-" )){
				$avgprice = explode ( "-", $value ['price'] );
				$value ['price'] = round ( ($avgprice ['0'] + $avgprice ['1']) / 2, 2 );
			}
			$tprice [$value['mastertopid']] = $value ['price'];
		}
		return $tprice;
	}
	//获取ng_ChengBenParameter3列表
	public function getAll($type,$start,$per){
		$sql = "select * from ng_ChengBenParameter3 where Type='".$type."' and isDelete='0' order by Date DESC,id DESC limit $start,$per";
        //echo $sql;
		return $this->query($sql);
	}
	//插入ng_ChengBenParameter3表信息
	public function insert_ng_ChengBenParameter3($value){
		$sql = "insert into ng_TieHeJinChengBenIndex  (`Date`,`type`,`indexValue`) VALUES $value";
		return $this->execute($sql);
	}
		//获取ng_ChengBenParameter3表信息
	public function get_ng_ChengBenParameter3ById($id){
		$sql = "select * from ng_ChengBenParameter3 where  id=$id";
		return $this->getRow($sql);
	}
	//获取get_ng_TieHeJinChengBenIndex表信息
	public function get_ng_TieHeJinChengBenIndex2($Date,$type){
		$sql = "select * from ng_TieHeJinChengBenIndex where  Date <='$Date' and  type ='$type' order by Date desc,id desc  limit 1";
		//echo $sql;
		return $this->getRow($sql);
	}

	//获取isholiday表信息
	public function get_isholiday_arr_by_date($sdate,$edate){
		$sql = "select * from holiday where date >= '$sdate' and date <= '$edate' ";
		return $this->query($sql);
	}

	public function selectSCPrice($topicture,$mconmanagedate) {//根据id，指定日期获取价格
		
		if(strlen($topicture)=='6'){
			$where = " and topicture = '$topicture' ";
		}else if (strlen($topicture)=='7'){
			$where = " and mastertopid = '$topicture' ";
		}
		
    	$sql = "select price,pricemk from marketconditions where mconmanagedate<='$mconmanagedate 23:59:59' $where order by mconmanagedate desc limit 1";
		//echo "<br>$sql";
    	$price1 = $this->getRow($sql);
    	return $price1['price'];
    }

	
}