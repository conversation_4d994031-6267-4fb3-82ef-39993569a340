<?php
class dczhbrdjAction extends AbstractAction{
   public function __construct(){
    parent::__construct();    
  }
	public function index($params)
	{
		$flat="--";
        $type=$params["Type"];
		$curdate=$params['curdate'];
		if(empty($curdate)) $curdate=date("Y-m-d");
		$nowday=date("Y-m-d",strtotime($curdate));
		$jsday=date("Y-m-d",strtotime($curdate)-15*24*3600);
		$topicturearr=array('073012','153012','123012','883012','133012','653012','113012','083012','093012');
		$sql="select topicture,pricemk as price from marketconditions where topicture in ('".implode("','",$topicturearr)."') and mconmanagedate>='$jsday' and mconmanagedate<'$nowday'";
		//echo $sql."<br>";
		$jsarr=$this->homeDao->query($sql);
		//print"<pre>";print_r($jsarr);

		foreach($jsarr as $arr){
			$jsprice[$arr["topicture"]]+=$arr["price"];
			$c[$arr["topicture"]]++;
		}
		foreach($jsprice as $i=>$v){
			$jsprice[$i]/=$c[$i];
		}
		//print"<pre>";print_r($jsprice);
		foreach($topicturearr as $topicture){
			if($jsprice[$topicture]==="") $jsprice[$topicture]=$flat;
			else $jsprice[$topicture]=(int)$jsprice[$topicture];
		}

		$workday=$this->getworkdays(date("Y-m-d",strtotime($curdate)));
		//print"<pre>";print_r($workday);exit;
		sort($workday);
		$lastworkday=$workday[count($workday)-1];
		$markarr=array('Ngzb0011','Ngzb0006','Ngzb0007','Ngzb0009'); //中厚板销售情况的数据标志 'Ngzb0002',

		$sql="select * from steelhome_drc.ng_data_table where datamark in ('".implode("','",$markarr)."') and dta_ym>='$workday[0]' and dta_ym<='".$lastworkday."' order by dta_ym desc";
		$arr=$this->_dao->query($sql);

		$lastprice=$this->_dao->getone("select Value from steelhome_drc.ng_data_table where datamark ='Ngzb0002' and dta_ym>='$workday[0]' and dta_ym<'$curdate' order by dta_ym desc limit 1");

		$date1="";
		$date2="";//date1 表示最近一天，date2表示date1之前有数据的日期，用于后面计算涨跌
		$sales = array();
		foreach($arr as $k=>$v){
			if($date1=="") $date1=$v["dta_ym"];
			elseif($date2==""||$date2==$date1) $date2=$v["dta_ym"];
			if(count($sales)<=7) $sales[$v["dta_ym"]][$v["DataMark"]]=round($v["Value"],0);
			if(count($sales)>=8) unset($sales[$v["dta_ym"]]); //当超过七个，要去掉
		}
		if(empty($arr)){$date1=date("Y-m-d",strtotime($curdate));$date2=date("Y-m-d",strtotime($curdate)-24*3600);}
		foreach($sales as $date=>$array){
			foreach($markarr as $mark){
				if($sales[$date][$mark]==="") $sales[$date][$mark]=$flat;
			}
		}

		krsort($sales);

		$isminval=1;
		$i=0;
		$lastkc=0;
		foreach($sales as $d=>$v){
			if($lastkc==0) $lastkc=$v["Ngzb0007"];
			if($i++>=5) break;
			$fifavg+=$v["Ngzb0007"];
			if($v["Ngzb0007"]-$sales[$date1]["Ngzb0007"]<0) {
				$isminval=0;
				//echo "<pre>".$d."=>";
				//print_r($v);
				//echo "</pre>";
			}
		}
		$fifavg/=5;
		
		$zb_pt['price']=0;
		$zb_pt['zd']=0;
		$zb_dhj['price']=0;
		$zb_dhj['zd']=0;
		$rj_pt['price']=0;
		$rj_pt['zd']=0;
		$rj_dhj['price']=0;
		$rj_dhj['zd']=0;
		
		$zb_pt_arr=array('113012','1230120','883012','073012','083012','093012','223012');
		$zb_dhj_arr=array('113022','883022','073022','083022','093022');			//,'123022','223022'
		$rj_pt_arr=array('113112','123112','883112','073112','083112','093112','223112');
		$rj_dhj_arr=array('113122','123122','883122','073122','083122','0941221','223122');
		$idarr=array_merge($zb_pt_arr,$zb_dhj_arr,$rj_pt_arr,$rj_dhj_arr);
		$masteridarr=array('0941221','1230120','1230224','2230225');
		
		$dataday=$this->homeDao->getone("select mconmanagedate from steelhome.marketconditions where topicture in ('".implode("','",$idarr)."') and mconmanagedate<'$nowday' order by mconmanagedate desc limit 1");
		$dataday=date("Y-m-d",strtotime($dataday));

		$sql="select topicture,mastertopid,pricemk as price,(pricemk-oldpricemk) as zd,factoryarea from steelhome.marketconditions where (topicture in ('".implode("','",$idarr)."') or mastertopid in('".implode("','",$masteridarr)."')) and mconmanagedate>='".$dataday."' and mconmanagedate<'".date("Y-m-d",strtotime($dataday)+24*3600)."'";
		//echo $sql;
		$valarr=$this->homeDao->query($sql);
		
		$scpj['price']=array();
		$scpj['zd']=array();
		foreach($valarr as $arr){
			if(in_array($arr["mastertopid"],$masteridarr)) {$pid=$arr["mastertopid"];}
			else $pid=$arr["topicture"];
			$idval[$pid]=$arr["price"];
			$zdval[$pid]=$arr["zd"];
			$gcname[$pid]=$arr["factoryarea"];

			//print_r($pid);print_r($rj_pt_arr);
			if(in_array($pid,$zb_pt_arr)){
				$zb_pt['price']+=$arr["price"];
				$zb_pt['zd']+=$arr["zd"];
			}elseif(in_array($pid,$zb_dhj_arr)){
				$zb_dhj['price']+=$arr["price"];
				$zb_dhj['zd']+=$arr["zd"];
			}elseif(in_array($pid,$rj_pt_arr)){
				$rj_pt['price']+=$arr["price"];
				$rj_pt['zd']+=$arr["zd"];
			}elseif(in_array($pid,$rj_dhj_arr)){
				$rj_dhj['price']+=$arr["price"];
				$rj_dhj['zd']+=$arr["zd"];
			}
		}

		$zb_pt['price']=round($zb_pt['price']*1.0/count($zb_pt_arr),0);
		$zb_pt['zd']=round($zb_pt['zd']*1.0/count($zb_pt_arr),0);
		$zb_dhj['price']=round($zb_dhj['price']*1.0/count($zb_dhj_arr),0);
		$zb_dhj['zd']=round($zb_dhj['zd']*1.0/count($zb_dhj_arr),0);

		$rj_pt['price']=round($rj_pt['price']*1.0/count($rj_pt_arr),0);
		$rj_pt['zd']=round($rj_pt['zd']*1.0/count($rj_pt_arr),0);
		$rj_dhj['price']=round($rj_dhj['price']*1.0/count($rj_dhj_arr),0);
		$rj_dhj['zd']=round($rj_dhj['zd']*1.0/count($rj_dhj_arr),0);

		if($zb_pt['zd']>0) $zb_pt['zd']="<font color='red'>+".$zb_pt['zd']."</font>";
		elseif($zb_pt['zd']<0) $zb_pt['zd']="<font color='green'>".$zb_pt['zd']."</font>";
		else $zb_pt['zd']=0;

		if($zb_dhj['zd']>0) $zb_dhj['zd']="<font color='red'>+".$zb_dhj['zd']."</font>";
		elseif($zb_dhj['zd']<0) $zb_dhj['zd']="<font color='green'>".$zb_dhj['zd']."</font>";
		else $zb_dhj['zd']=0;

		if($rj_pt['zd']>0) $rj_pt['zd']="<font color='red'>+".$rj_pt['zd']."</font>";
		elseif($rj_pt['zd']<0) $rj_pt['zd']="<font color='green'>".$rj_pt['zd']."</font>";
		else $rj_pt['zd']=0;

		if($rj_dhj['zd']>0) $rj_dhj['zd']="<font color='red'>+".$rj_dhj['zd']."</font>";
		elseif($rj_dhj['zd']<0) $rj_dhj['zd']="<font color='green'>".$rj_dhj['zd']."</font>";
		else $rj_dhj['zd']=0;

		foreach($idarr as $id){
			if($zdval[$id]>0) $zdval[$id]="<font color='red'>+".$zdval[$id]."</font>";
			elseif($zdval[$id]<0) $zdval[$id]="<font color='green'>".$zdval[$id]."</font>";
			if($zdval[$id]=="") $zdval[$id]=0;
			if($idval[$id]===""){
				$idval[$id]=$flat;
				$zdval[$id]=$flat;
			}
		}

		$qhcodearr=array('RB','HC','I','JM','J');
		$sql="select day,code,price from steelhome.kmjg_qhprice where code in ('".implode("','",$qhcodearr)."') and day<='".date("Y-m-d",strtotime($curdate))."' and time<'17:00:00' order by id desc limit 5";
		$qharr=$this->homeDao->query($sql);
		$qhdate=$qharr[0]["day"];

		$sql2=str_replace(date("Y-m-d",strtotime($curdate)),$qhdate,$sql);
		$sql2=str_replace("day<=","day<",$sql2);
		$sql2=str_replace("time<'17:00:00'","time>='17:00:00'",$sql2);//echo $sql."<br>".$sql2;
		$qharr2=$this->homeDao->query($sql2);
		$qhdate2=$qharr2[0]["day"];
		
		$qhzd=array();
		$qhzdf=array();
		foreach($qharr2 as $id=>$arr){
			$qharr2[$arr["code"]]=$arr["price"];
			unset($qharr2[$id]);
		}
		foreach($qharr as $arr){
			if($arr["code"]=="I"||$arr["code"]=="JM"||$arr["code"]=="J") {
				$qhval[$arr["code"]]=sprintf("%.1f",$arr["price"]);
				$qhzd[$arr["code"]]=sprintf("%.1f",$qhval[$arr["code"]]-$qharr2[$arr["code"]]);
			}else{
				$qhval[$arr["code"]]=$arr["price"];
				$qhzd[$arr["code"]]=$qhval[$arr["code"]]-$qharr2[$arr["code"]];
			}
			$qhzdf[$arr["code"]]=round($qhzd[$arr["code"]]*100.0/$qharr2[$arr["code"]],2)."%";
		}
		foreach($qhcodearr as $code){
			if($qhzd[$code]>0){
				$qhzd[$code]="<font color='red'>+".$qhzd[$code]."</font>";
				$qhzdf[$code]="<font color='red'>+".$qhzdf[$code]."</font>";
			}elseif($qhzd[$code]<0){
				$qhzd[$code]="<font color='green'>".$qhzd[$code]."</font>";
				$qhzdf[$code]="<font color='green'>".$qhzdf[$code]."</font>";
			}
			if($qhzd[$code]==""){
				$qhzd[$code]=0;
				$qhzdf[$code]="0%";
			}
			if($qhval[$code]===""){
				$qhval[$code]=$flat;
				$qhzd[$code]=$flat;
			}
		}

		$d_dzarr=array(1,2,3,4,5,6,9);
		$sql="select date,type,value,updown from steelhome.dollar_dzprice where type in ('".implode("','",$d_dzarr)."') and date<'".date("Y-m-d",strtotime($curdate))."' order by id desc limit 7";
		$dzarr=$this->homeDao->query($sql);
		
		$dzzd=array();
		$dzzdf=array();
		foreach($dzarr as $arr){
			$time = strtotime($arr['date']) - 3600*24;
			$forworddate=date("Y-m-d",$time);
			$dsql="select value  from  steelhome.dollar_dzprice where  type='".$arr['type']."' and date<='".$forworddate."' order by id desc limit 1";
			$dres=$this->homeDao->getOne($dsql);//正式
			$down=$arr['value']-$dres;//this by wufan
			
			$dzval[$arr["type"]]=sprintf("%.2f",$arr["value"]);
			$dzzd[$arr["type"]]=sprintf("%.2f",$down);
			if(abs($dzzd[$arr["type"]])-0.01<0) $dzzd[$arr["type"]]=$down;

			$zdf=$dzzd[$arr["type"]]*100.0/($dzval[$arr["type"]]-$dzzd[$arr["type"]]);
			if(abs($zdf)<0.01){
				$dzzdf[$arr["type"]]="0%";
			}else{
				$dzzdf[$arr["type"]]=round($zdf,2)."%";
			}
		}
		$dzdate=$dzarr[0]["date"];
		foreach($d_dzarr as $code){
			if($dzzd[$code]>0){
				$dzzd[$code]="<font color='red'>+".$dzzd[$code]."</font>";
				$dzzdf[$code]="<font color='red'>+".$dzzdf[$code]."</font>";
			}elseif($dzzd[$code]<0){
				$dzzd[$code]="<font color='green'>".$dzzd[$code]."</font>";
				$dzzdf[$code]="<font color='green'>".$dzzdf[$code]."</font>";
			}
			if($dzzd[$code]==""){
				$dzzd[$code]=0;
				$dzzdf[$code]="0%";
			}
			if($dzval[$code]==="") {
				$dzval[$code]=$flat;
				$dzzd[$code]=$flat;
			}
		}

		$gzj_zhb_prediction=$this->homeDao->getRow("select Detail,ZhangDie from NgGZJForcast where Type=2 and CDate='".date("Y-m-d",strtotime($curdate)-24*3600)."' order by id desc limit 1");
		$zhangdie =$gzj_zhb_prediction['ZhangDie'];
		$gzj_zhbyczhangdie=$zhangdie;

		if($gzj_zhb_prediction)
		{
			if(abs($zhangdie)<=DELTA) $zhangdie="持平";//浮点数为0
			elseif($zhangdie-DELTA>0) $zhangdie="上涨".$zhangdie."元/吨";
			else $zhangdie="下跌".abs($zhangdie)."元/吨";
		}else{
			$zhangdie="无";
		}
		
		$gzj_zhb_prediction['ZhangDie']=$zhangdie;

		//公式：AAAA=Ngzb0002+BB1;BB1计算方法如下
		$HC=(int)$qhzd["HC"];
		//echo "lastkc=".$lastkc.";fifavg=".$fifavg.";isminval=".$isminval.";HC=".$HC."<br>";
		if($lastkc-$fifavg<=0){
			if($isminval==0){
				if($HC-10>=0){
					$BB1=(int)($HC/10)*10;
				}elseif($HC>0&&$HC-10<0){
					$BB1=10;
				}elseif($HC+10>0&&$HC<=0){
					$BB1=0;
				}elseif($HC+50>0&&$HC+10<=0){
					$BB1=(int)($HC*0.5/10)*10;
				}elseif($HC+100>0&&$HC+50<=0){
					$BB1=(int)($HC*0.8/10)*10;
				}else{
					$BB1=(int)($HC*0.5/10)*10;
				}
			}else{
				if($HC-10>=0){
					$BB1=(int)($HC*2/10)*10;
				}elseif($HC>0&&$HC-10<0){
					$BB1=10*2;
				}elseif($HC+10>0&&$HC<=0){
					$BB1=10;
				}elseif($HC+50>0&&$HC+10<=0){
					$BB1=0;
				}elseif($HC+100>0&&$HC+50<=0){
					$BB1=(int)($HC*0.5/10)*10;
				}else{
					$BB1=(int)($HC*0.8/10)*10;
				}
			}
		}else{
			if($isminval==0){
				if($HC-100>=0){
					$BB1=(int)($HC/10)*10;
				}elseif($HC-50>0&&$HC-100<0){
					$BB1=(int)($HC*0.8/10)*10;
				}elseif($HC-10>0&&$HC-50<=0){
					$BB1=(int)($HC*0.5/10)*10;
				}elseif($HC>0&&$HC-10<=0){
					$BB1=0;
				}elseif($HC+10>0&&$HC<=0){
					$BB1=-10;
				}else{
					$BB1=(int)($HC/10)*10;
				}
			}else{
				if($HC-100>=0){
					$BB1=(int)($HC*0.9/10)*10;
				}elseif($HC-50>0&&$HC-100<0){
					$BB1=(int)($HC*0.7/10)*10;
				}elseif($HC-10>0&&$HC-50<=0){
					$BB1=(int)($HC*0.4/10)*10;
				}elseif($HC>0&&$HC-10<=0){
					$BB1=0;
				}elseif($HC+10>0&&$HC<=0){
					$BB1=-20;
				}else{
					$BB1=(int)($HC*1.5/10)*10;
				}
			}
		}
		if($zhangdie!="无"){
			if($params['debug'])echo "<br>before:预测涨跌(".$gzj_zhbyczhangdie.")"."<=>计算涨跌(".$BB1.")<br>";
			if(($gzj_zhbyczhangdie>0&&$BB1<0)||($gzj_zhbyczhangdie<0&&$BB1>0)){
				$BB1=$gzj_zhbyczhangdie+$BB1;
			}elseif(abs($gzj_zhbyczhangdie-$BB1)>30){
				$BB1=$gzj_zhbyczhangdie;
			}
		}
		if($params['debug'])echo "after:".$BB1."<br>";
		$AAAA=$lastprice+$BB1;
		//公式：AAAA=Ngzb0002+BB1;
		//echo "AAAA=".$AAAA.";BB1=".$BB1."<br>";

		$this->assign("nowday",date("Y年n月j日",strtotime($nowday)));
		$this->assign("jsday",date("Y年n月j日",strtotime($jsday))."-".date("n月j日",strtotime($nowday)-24*3600));
		$this->assign("jsprice",$jsprice);
		$this->assign("lastworkday",date("Y年n月j日",strtotime($date1)));
		$this->assign("dataday",date("Y年n月j日",strtotime($dataday)));
		$this->assign("sales",$sales);
		$this->assign("idval",$idval);
		$this->assign("zdval",$zdval);
		$this->assign("zb_pt",$zb_pt);
		$this->assign("zb_dhj",$zb_dhj);
		$this->assign("rj_pt",$rj_pt);
		$this->assign("rj_dhj",$rj_dhj);
		$this->assign("gcname",$gcname);
		$this->assign("qhval",$qhval);
		$this->assign("qhzd",$qhzd);
		$this->assign("qhzdf",$qhzdf);
		$this->assign("qhdate",date("Y年n月j日",strtotime($qhdate)));
		$this->assign("dzval",$dzval);
		$this->assign("dzzd",$dzzd);
		$this->assign("dzzdf",$dzzdf);
		$this->assign("dzdate",date("Y年n月j日",strtotime($dzdate)));
		$this->assign("gzj_prediction",$gzj_zhb_prediction);

		//页面打开直接处理，定时运行程序用window.onload无法触发事件
		$modelpirce_name="20mmQ235B中厚板价格";
		$type=6;
		$title=date("Y年n月j日",strtotime($curdate))."中厚板日定价";
		$modelprice=$AAAA;
		$modeloldprice=$lastprice;
		$modelprice_updown=$BB1;

		if($_GET["issave"]==1){
			$sql="insert into steelhome_drc.ng_price_model (createtime,date,modeltitle,modeltype,ismakepricy) values (NOW(),'".date("Y-m-d",strtotime($curdate))."','$title','$type','0')";
			$this->_dao->execute($sql);
			$rid=$this->_dao->insert_id();
			$sql="insert into steelhome_drc.ng_price_model_detail (modelid,modelprice,modelprice_updown,modeloldprice,modelprice_type,modelpirce_name,modelpirce_name_sort,Mid,uid,Ismakepricy_men,createtime,date) values ('$rid','$modelprice','$modelprice_updown','$modeloldprice','$type','$modelpirce_name','1','-1','-1','0',NOW(),'".date("Y-m-d",strtotime($curdate))."')";
			$this->_dao->execute($sql);
		}
		//end by zhangcun for 螺纹钢日定价 2017/12/7
	}

	public function savedata($params){
		$curdate=$params['curdate'];
		if(empty($curdate)) $curdate=date("Y-m-d");
		if(IsNgWorkDay($this->homeDao,date("Y-m-d",strtotime($curdate)))!="1") {echo "今天是休息日，不生成数据";exit;}
		$type=6;
		$url = DC_URL.DCURL."/dczhbrdj.php?view=index&issave=1&curdate=".$curdate;
		include '/usr/local/www/libs/phpQuery/phpQuery.php';
		phpQuery::newDocumentFile($url);
		$html=pq("body")->html();

		$s_huizong="<!--huizong start-->";
		$e_huizong="<!--huizong end-->";
		$spos=strpos($html,$s_huizong);
		$epos=strpos($html,$e_huizong);
		$huizong=substr($html,$spos+strlen($s_huizong),$epos-$spos);
		$huizong=str_replace("七、钢之家判断","1、钢之家判断",$huizong);
		$huizong=str_replace("八、今日价格调整建议","2、价格模型建议",$huizong);
		$huizong=htmlentities($huizong, ENT_QUOTES,"UTF-8");

		$html=str_replace($s_huizong,"",$html);
		$html=str_replace($e_huizong,"",$html);

		$modelcontent=htmlentities($html, ENT_QUOTES,"UTF-8");
		$sql="update steelhome_drc.ng_price_model set modelcontent='".$modelcontent."',HuiZong1='$huizong' where modeltype='$type' and date>='".date("Y-m-d",strtotime($curdate))."' and date<'".date("Y-m-d",strtotime($curdate)+3600*24)."' order by id desc limit 1";
		$this->_dao->execute($sql);
		echo "ok";exit;
	}

	function getworkdays($date,$day=7){
		$workday=array();
		$sql="select date,isholiday from steelhome.holiday where date<'$date' order by date desc limit ".((int)($day/7)*2+8); //echo $sql."<br>";//exit;
		$ho=$this->homeDao->aquery($sql);
		//echo "<pre>";print_r($ho);
		for($i=0;count($workday)<$day;$i++){
			$d=date("Y-m-d",strtotime($date)-($i+1)*3600*24);
			if($ho[$d]=="1"){
				continue;
			}
			if($ho[$d]==="0"){
				$workday[]=$d;
			}else{
				//echo "<br>".$d." ".$ho[$d]."<br>";
				if(date("N",strtotime($d))=="7"){
					continue;
				}else{
					$workday[]=$d;
				}
			}
		}
		//print"<pre>";print_r($workday);
		return $workday;
	}
}
?>