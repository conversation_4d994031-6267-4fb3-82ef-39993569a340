﻿<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<title>技术部日常工作反馈记录</title>
<link rel="stylesheet" href="css/meeting/index.css">
<link rel="stylesheet" href="css/meeting/public.css">
<script src="js/meeting/layui/layui.all.js"></script>
<link rel="stylesheet" href="js/meeting/layui/css/layui.css">
<style>
/* 
.allnav div{
    width: 100%;
    height: 500px;
} */
.nav li{width: 50%;}
</style>
</head>
<body>

<!-- <div class="zuo" id="zuodian"></div>
<div class="you" id="youdian"></div> -->

<header>
<div class="header">
	<h1><span class="font1" id="maintitle"><img src="images/gzj_logo.png">技术部日常工作反馈记录</span><span class="font2" id="sjdate">2023.10.23-2024.01.10</span></h1>
</div>
</header>

<section>

       
    <div class="center" id="mokuai1" >
		
        <div class="center-left " style="width: 96%;height: 95%; margin: 0px auto;margin-top: 3%;">

            <div class="left-top rightTop" style="height: 100%;">
                <div class="title" id="chart5title" style="font-size: 18px;margin-bottom: 20px;"></div>
                 <div class="bottom-b">
                    <div  id="chart5" class="allnav"></div>
                    <div  id="chart5Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                    <div class="panel-footer"></div>
                </div>                    
            </div>
           

        </div>
        
    </div>

    <div class="center" id="mokuai2"  style="display: none;">
		
        <div class="center-left " style="width: 96%;height: 95%; margin: 0px auto;margin-top: 3%;">

            <div class="left-top rightTop" style="height: 100%;">
                <div class="title" id="chart1title" style="font-size: 18px;margin-bottom: 20px;"></div>
                 <div class="bottom-b">
                    <div  id="chart1" class="allnav"></div>
                    <div  id="chart1Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                    <div class="panel-footer"></div>
                </div>                    
            </div>
           

        </div>
        
    </div>
   
</section>
<nav>

    <div class="nav"> 
        <ul style="width: 50%;margin: 0px auto;"> 
            
            <li>每周数据统计</li> 
            <li>每日数据统计</li> 
        </ul> 
    </div>
    
    </nav>
<script>
    var istry='<{$istry}>';
</script>
<script src="js/meeting/jquery.min.js"></script>
<script src="js/meeting/axios.min.js"></script>
<script src="js/meeting/echarts.min.js"></script>



<script>
    var maxwidthscen=1920;
var nowdate='2021-06-02';
nowdate='';
var articleid=0;
$(function() {

 var activeIndex = -1;
 var liDoms = $(".nav ul li");
 var iframeDoms = [];
 var pepoactive=false;
 var xunhuannum=0;
 liDoms.bind("click", function(){
     var idx = Array.prototype.indexOf.call(liDoms, this);
     pepoactive=true;
     xunhuannum=0;
     clickLiByIndex(idx);
 })

 /*点击某一个*/
 function clickLiByIndex(idx){

     if(idx === activeIndex) return;
     var predom = liDoms[activeIndex];
     //取消之前的高亮样式
     if(predom){
         $(predom).removeClass("active");
     }
     //显示当前高亮样式
     var target = liDoms[idx];
     if(target){
         //EUI.addClassName(target, "active");
         $(target).addClass("active");

     }
     nowdate='';
     clickIndex(idx);
     activeIndex = idx;
 }
 var active=getQueryString('activeIndex');
 if(active==null)
 {
     active=0;
 }
 clickLiByIndex(active);
 
 //clickIndex(1);
 function clickIndex(idx)
 {
     
         if(idx==1){
            document.getElementById("chart5").removeAttribute("_echarts_instance_");
             $('#mokuai1').show();
             $('#mokuai2').hide();
             initChart2();
         }else if(idx==0){
             
             document.getElementById("chart1").removeAttribute("_echarts_instance_");
             $('#mokuai1').hide();
             $('#mokuai2').show();
             initChart1();
         }
 }


 function initChart1(){
    $('#chart1Loading').hide();
    $('#chart5Loading').hide();
    let myCharts = echarts.init(document.getElementById('chart1'))
     var datalist=["17","62","28","24","12","23","21","33","37","28","51","19"];
    var series = [
  {
    data: [13,43, 11, 14, 3, '-', '-',1,1, '-', '-', '-'],
    type: 'bar',
    stack: 'a',
    name: '产品缺陷',
    barMaxWidth:40,
    label: {
      show: true,
      color:'#000',
      formatter: (params) => Math.round((params.value / datalist[params.dataIndex]) *100) + '%'
    },
    
  },
  {
    data: [4, 6, 9,7,5,16,13,18,23,16,23,4],
    type: 'bar',
    stack: 'a',
    name: '系统缺陷',
    barMaxWidth:40,
    label: {
      show: true,
      color:'#000',
      formatter: (params) => Math.round((params.value / datalist[params.dataIndex]) *100) + '%'
    },
  },
  {
    data: [ '-',10,  1,3,'-',3,2,10,4,2,3,'-'],
    type: 'bar',
    stack: 'a',
    name: '操作问题',
    barMaxWidth:40,
    label: {
      show: true,
      color:'#000',
      formatter: (params) => Math.round((params.value / datalist[params.dataIndex]) *100) + '%'
    },
  },
  {
    data: ['-',4, 4,'-',2,'-',3,2,3,7,21,7],
    type: 'bar',
    stack: 'a',
    name: '需求问题',
    barMaxWidth:40,
    label: {
      show: true,
      color:'#000',
      formatter: (params) => Math.round((params.value / datalist[params.dataIndex]) *100) + '%'
    },
  },
  {
    data: ['-','-','-','-','-','-','-','-',4,'-','-','-'],
    type: 'bar',
    stack: 'a',
    name: '兼容问题',
    barMaxWidth:40,
    label: {
      show: true,
      color:'#000',
      formatter: (params) => Math.round((params.value / datalist[params.dataIndex]) *100) + '%'
    },
  },
  {
    data: ['-','-',2,'-',2,1,2,2,2,'-',3,5],
    type: 'bar',
    stack: 'a',
    name: '网络问题',
    barMaxWidth:40,
    label: {
      show: true,
      color:'#000',
      formatter: (params) => Math.round((params.value / datalist[params.dataIndex]) *100) + '%'
    },
  },
  {
      name: '综合',
      type: 'bar',
      stack: 'a',
      label: {
        show: true,
        position: 'top',
        formatter: function (p) { //这里处理展示的数据和
          let sum = datalist[p.dataIndex] ;
          return sum+"条";
        },
        textStyle:{
                            color: '#00FFFF',
                            
                            fontSize: 15,
                          }
      },
      emphasis: {
        focus: 'series'
      },
      data: [0, 0, 0, 0, 0,0, 0, 0, 0, 0,0,0]//为了隐藏总计bar
    }
];
const totalLabelStyle = {
  color: '#000',
  fontSize: 14,
  fontWeight: 'bold',
  textAlign: 'center',
  textBaseline: 'middle',
  position: 'top'
};
option = {
    color:['#5470C6','#91CC75',
                                     '#FCD25C','#EE6666',
                                     '#7DD2F3','#F0805A','#F26C05','#26C0C0'],
    grid: {
        left: 55,                                    //图表距离容器左侧多少距离
        right: 35,                                //图表距离容器右侧侧多少距离
        bottom: 35,                              //图表距离容器上面多少距离
        top: 55,  
    },
    legend: {
    data: ['产品缺陷', '系统缺陷', '操作问题', '需求问题', '兼容问题','网络问题'],
    textStyle:{
                            color: '#00FFFF',
                            
                            fontSize: 13,
                          }
  },
  xAxis: {
    type: 'category',
    data:['<{$names2}>'],
    axisLabel: {
                            textStyle: {
                                color: '#00FFFF', //坐标值得具体的颜色   
                                fontSize: 14               
                            }
                        }
  },
  yAxis: {
    type: 'value',
    name:'条',
    axisLabel: {
                            textStyle: {
                                color: '#00FFFF', //坐标值得具体的颜色   
                                fontSize: 14               
                            }
                        }
  },
  series: series
};

             myCharts.setOption(option);

             window.addEventListener("resize", function() {
                 myCharts.resize();
             });

 }


    function initChart2(){
        $('#chart1Loading').hide();
        $('#chart5Loading').hide();
            var series = [];
                let myCharts = echarts.init(document.getElementById('chart5'))
                option = {
                    color:['#FCCE10','#0AF017',"#FA640D",'#0DD6F5','#863BF2'],
                    tooltip: {
                        trigger: 'axis',
                        axisPointer: {
                            type: 'cross',
                        },
                        formatter: function (params) {
                            return params[0].name+'：'+params[0].value+" 条"
                        }
                    },
                    grid: {
                        
                        left: 55,                                    //图表距离容器左侧多少距离
                        right: 35,                                //图表距离容器右侧侧多少距离
                        bottom: 35,                              //图表距离容器上面多少距离
                        top: 55,  
                        
                    },
                    toolbox: {
                        padding:20,
                        show: true,
                    },
                    xAxis: {
                        type: 'category',
                        boundaryGap: false,
                        data:['<{$names}>'],
                        axisLabel: {
                            textStyle: {
                                color: '#00FFFF', //坐标值得具体的颜色   
                                fontSize: 14               
                            }
                        }
                    },
                    yAxis: {
                        type: 'value',
                        name:'条',
                        minInterval : 1,
                        axisLabel: {
                            formatter: '{value}条'
                        },
                        axisPointer: {
                            snap: true
                        },
                        axisLabel: {
                            textStyle: {
                                color: '#00FFFF', //坐标值得具体的颜色  
                                fontSize: 14                
                            }
                        },
                        nameTextStyle: {
                            color: "#00FFFF",
                        },
                    },
                    
                    series: [
                        {
                            name:'27日报到人数',
                            type: 'line',
                            symbolSize: 7,
                            smooth: true,
                            data:[<{$values}>],
                            
                        },
                    ]
                };
                myCharts.setOption(option);
                window.addEventListener("resize", function() {
                    myCharts.resize();
                });

            
            
        

 }
});
</script>
    
<script src="js/meeting/fontscroll.js"></script>
<script src="js/meeting/util.js"></script>
</body>
</html>