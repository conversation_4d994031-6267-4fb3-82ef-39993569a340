<?php
// ini_set('display_errors',1);            //错误信息  
// ini_set('display_startup_errors',1);    //php启动错误信息  
// error_reporting(-1);                    //打印出所有的 错误信息  1
set_time_limit(0);
//var_dump($argv);
include_once( "sys.conf.php" );
//echo $dsn;
try {
    $pdo = new PDO($dsn, $user, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE,PDO::ERRMODE_EXCEPTION);
	//echo "ok ";
} catch (PDOException $e) {
    die("数据库连接失败".$e->getMessage());
}

if(isset($argv[1]))
{
 $_REQUEST['mc_type']=$argv[1];
}
$mc_type=0;
if(isset($_REQUEST['mc_type']))
{
   $mc_type=$_REQUEST['mc_type'];
}
if(!in_array($mc_type,array(0,1,2,3,4,5)))
{
   echo "错误的mc_type，请检查后再试。<br/>";
   exit;
}
$count_sql = "select * from app_version where mc_type='".$mc_type."' order by ID desc limit 1";
$sth = $pdo->prepare($count_sql);
$sth->execute();
$result = $sth->fetchAll();
foreach($result as $item)
{
    $TaskRun=$item['TaskRun'];
    print_r($TaskRun);
}
 
