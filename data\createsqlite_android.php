<?php
set_time_limit(0);
include_once( "sys.conf.php" );

$pcsqllitename=array(
'0'=>array('menu_db.db','yun_android.zip'),
'1'=>array('menu_db_ng.db','yun_android_ng.zip'),
'2'=>array('menu_db_sg.db','yun_android_sg.zip'),
'3'=>array('menu_db_xg.db','yun_android_xg.zip'),
'4'=>array('menu_db_xg_jgyc.db','yun_android_xg_jgyc.zip'),
'5'=>array('menu_db_shansteel.db','yun_android_shansteel.zip')
);

// sqlite分页类
class SqliteDB{
  public function __construct($mc_type){
    // 初始化数据库，并且连接数据库 数据库配置
	// if($mc_type!=1)
	// {
	// 	$this->db = new PDO('sqlite:'.dirname(__FILE__).'/yun.dat');
	// }
	// else
	// {
    //     $this->db = new PDO('sqlite:'.dirname(__FILE__).'/yun_ng.dat');
    // }
   global $pcsqllitename;
    //print_r($pcsqllitename);
   // echo $pcsqllitename[$mc_type][0];exit;
    $this->db = new PDO('sqlite:'.dirname(__FILE__).'/'.($pcsqllitename[$mc_type][0]));
    
    $this->table_name=$tab;
    $this->tab_init();
  }
  public function tab_init()
  {

	  # 表初始化,创建表
	  $tab1="CREATE TABLE TREE_FIRST_BEAN (ID varchar(10) PRIMARY KEY NOT NULL ,MCODE varchar(20),SNAME varchar(50),SCODE varchar(20),SOD int(10),SDESC varchar(20),HASLEAVES vrachar(10),CD vrachar(10),FLAG int(10));";
	  $tab2=" CREATE TABLE TREE_SECOND_BEAN (ID varchar(10),MCODE varchar(20),SNAME varchar(20),SNAMESHORT varchar(20),SCODE varchar(20),SOD int(10),HASNODE varchar(20),SDESC varchar(20),ParentID varchar(10),TREE_DEPTH varchar(20),HASLEAVES varchar(20),FLAG int(10));";
    

$tab3 = "CREATE TABLE TREE_FIFTH_BEAN (ID varchar(10) PRIMARY KEY NOT NULL ,SCODE1 varchar(20),SCODE2 varchar(20),SCODE3 varchar(20),SCODE4 varchar(20),SCODE5 varchar(20),DTNAME varchar(20),DTYMD varchar(20),DTCOMPARETYPE varchar(20),DBASE_TYPE varchar(20),IS_SUB_DATAS varchar(20),SUB_DATAS_TITLE varchar(20),SUB_DATAS_DB varchar(20),SUB_DATAS_DB2 varchar(20),SUB_ATT1_DB varchar(20),SUB_ATT2_DB varchar(20),SUB_ATT3_DB varchar(20),DATA1_TYPE varchar(20),DATA1_UNIT_TYPE varchar(20),DATA1_UNIT_CONV varchar(20),IS_DATA2 varchar(20),DATA2_TYPE varchar(20),DATA2_UNIT_TYPE varchar(20),DATA2_UNIT_CONV varchar(20),IS_DATA3 varchar(20),DATA3_TYPE varchar(20),DATA3_UNIT_TYPE varchar(20),DATA3_UNIT_CONV varchar(20),IS_DATA4 varchar(20),DATA4_TYPE varchar(20),DATA4_UNIT_TYPE varchar(20),DATA4_UNIT_CONV varchar(20),IS_DATA5 varchar(20),DATA5_TYPE varchar(20),DATA5_UNIT_TYPE varchar(20),DATA5_UNIT_CONV varchar(20),IS_PRE_DATA varchar(20),IS_ADD_SUB_DATA varchar(20),IS_ADD_SUB_HUND_CORE varchar(20),STARTDATE varchar(20),ENDDATAIS_CURRENT varchar(20),ENDDATA varchar(20),DTOD varchar(20),DTDESC varchar(20),TECHDBNAME varchar(20),TECHTABLENAME varchar(20),TECHSQLMAIN varchar(50),NDATE varchar(20),NDATA1 varchar(20),NDATA2 varchar(20),NDATA3 varchar(20),NDATA4 varchar(20),NDATA5 varchar(20),NPREDATA varchar(20),NADDSUBDATA varchar(20),NADDSUBHUNDCORE varchar(20),DDATE varchar(20),DDATA1 varchar(20),DDATA2 varchar(20),DDATA3 varchar(20),DDATA4 varchar(20),DDATA5 varchar(20),DPREDATA varchar(20),DADDSUBDATA varchar(20),DADDSUBHUNDCORE varchar(20),ISBUY varchar(20),TARGET_FLAG_VAR varchar(20),DATA6_TYPE varchar(20),DATA7_TYPE varchar(20),DATA8_TYPE varchar(20),DATA9_TYPE varchar(20),DATA10_TYPE varchar(20),DATA11_TYPE varchar(20),DATA12_TYPE varchar(20),DATA13_TYPE varchar(20),DATA14_TYPE varchar(20),DATA15_TYPE varchar(20),DATA16_TYPE varchar(20),DATA17_TYPE varchar(20),DATA18_TYPE varchar(20),DATA19_TYPE varchar(20),DATA20_TYPE varchar(20),NDATA6 varchar(20),NDATA7 varchar(20),NDATA8 varchar(20),NDATA9 varchar(20),NDATA10 varchar(20),NDATA11 varchar(20),NDATA12 varchar(20),NDATA13 varchar(20),NDATA14 varchar(20),NDATA15 varchar(20),NDATA16 varchar(20),NDATA17 varchar(20),NDATA18 varchar(20),NDATA19 varchar(20),NDATA20 varchar(20),PINZHONG varchar(20),DATA_SOURCE varchar(50),UNITSTRINGS VARCHAR(200),UNITCONVERS VARCHAR(200));";

$tab3_index ="CREATE INDEX IDX_TREE_FIFTH_BEAN_SCODE1 ON TREE_FIFTH_BEAN (SCODE1 ASC);";

$tab3_index2 ="CREATE INDEX IDX_TREE_FIFTH_BEAN_SCODE2 ON TREE_FIFTH_BEAN (SCODE2 ASC);";

$tab3_index3 ="CREATE INDEX IDX_TREE_FIFTH_BEAN_SCODE3 ON TREE_FIFTH_BEAN (SCODE3 ASC);";

$tab3_index4 ="CREATE INDEX IDX_TREE_FIFTH_BEAN_SCODE4 ON TREE_FIFTH_BEAN (SCODE4 ASC);";

$tab3_index5 ="CREATE INDEX IDX_TREE_FIFTH_BEAN_SCODE5 ON TREE_FIFTH_BEAN (SCODE5 ASC);";

$tab3_index6 ="CREATE INDEX IDX_TREE_FIFTH_BEAN_ISBUY ON TREE_FIFTH_BEAN (ISBUY ASC);";

$tab3_index7 ="CREATE INDEX IDX_TREE_FIFTH_BEAN_ISBUY2 ON TREE_FIFTH_BEAN (ISBUY,SCODE1,PINZHONG);";

$tab3_index8 ="CREATE INDEX IDX_TREE_FIFTH_BEAN_ISBUY3 ON TREE_FIFTH_BEAN (ISBUY,SCODE2,PINZHONG);";


$tab4 = "CREATE TABLE TREE_SUB_DATA_BEAN (ID varchar(20) PRIMARY KEY NOT NULL ,SNAME varchar(20),SCODE varchar(20),SDBTYPE varchar(20),SATT1 varchar(20),SATT2 varchar(20),SATT3 varchar(20),AOD varchar(20),MSG varchar(20),FIFTH_ID varchar(20),POSITION varchar(20));";

$tab4_index = "CREATE INDEX IDX_TREE_SUB_DATA_BEAN_FIFTH_ID ON TREE_SUB_DATA_BEAN (FIFTH_ID ASC);";
$tab4_index2 = "CREATE INDEX IDX_TREE_SUB_DATA_BEAN_MSG ON TREE_SUB_DATA_BEAN (MSG ASC);";

            
	$tab5 = "CREATE TABLE TREE_COMMON_DATA_BEAN (ID varchar(20) PRIMARY KEY NOT NULL ,DTNAME varchar(20),DTNAMESHORT varchar(20),IMAGE_TYPE varchar(20),DTID1 varchar(20),DTID1_SUB varchar(20),DATA1_TYPE varchar(20),DATA1_TYPE1 varchar(20),DATA1_IMAGE varchar(20),DATA1_PRE varchar(20),DATA1_ADD_SUB varchar(20),DATA1_ADD_SUB_HUND_CORE varchar(20),DATA1_TYPE2 varchar(20),DATA1_TYPE3 varchar(20),DATA1_TYPE4 varchar(20),DATA1_TYPE5 varchar(20),DTID2 varchar(20),DTID2_SUB varchar(20),DATA2_TYPE varchar(20),DATA2_IMAGE varchar(20),DTID3 varchar(20),DTID3_SUB varchar(20),DATA3_TYPE varchar(20),DATA3_IMAGE varchar(20),DTID4 varchar(20),DTID4_SUB varchar(20),DATA4_TYPE varchar(20),DATA4_IMAGE varchar(20),AOD varchar(20),FIRST_ID varchar(20),DTIDJson TEXT);";

    $tab6 = "CREATE TABLE CUSTOM_DFCONF (ID varchar(20) PRIMARY KEY NOT NULL ,DFKEY varchar(20),DFVALUE varchar(20),REMARK varchar(20),DFOD int(10));";
 
			$tabs = "";
            if (!$this->ExistsTab( "android_metadata"))
            {
              $tabs = " CREATE TABLE android_metadata (locale varchar(20)); insert into android_metadata(locale)values('zh_CN');  ";
			}
			
            if (!$this->ExistsTab( "TREE_FIRST_BEAN"))
            {
                $tabs .= $tab1;
             
            }
			else
	        {
              $tabs .= " delete from TREE_FIRST_BEAN; ";
			}
			if (!$this->ExistsTab( "TREE_SECOND_BEAN"))
            {
                $tabs .= $tab2;
            }
			else
	        {
              $tabs .= " delete from TREE_SECOND_BEAN; ";
			}
            if (!$this->ExistsTab("TREE_FIFTH_BEAN"))
            {
                $tabs .= $tab3;
                $tabs .= $tab3_index;
                $tabs .= $tab3_index2;
				$tabs .= $tab3_index3;
				$tabs .= $tab3_index4;
				$tabs .= $tab3_index5;
				$tabs .= $tab3_index6;
				$tabs .= $tab3_index7;
				$tabs .= $tab3_index8;
            }
			else
	        {
              $tabs .= " delete from TREE_FIFTH_BEAN; ";
			}
            if (!$this->ExistsTab( "TREE_SUB_DATA_BEAN"))
            {
                $tabs .= $tab4;
                $tabs .= $tab4_index;
				$tabs .= $tab4_index2;
               
            }
			else
	        {
              $tabs .= " delete from TREE_SUB_DATA_BEAN; ";
			}
            if (!$this->ExistsTab("TREE_COMMON_DATA_BEAN"))
            {
                $tabs .= $tab5;
            }
			else
	        {
              $tabs .= " delete from TREE_COMMON_DATA_BEAN; ";
			}
			
			if (!$this->ExistsTab("CUSTOM_DFCONF"))
            {
                $tabs .= $tab6;
            }
			else
	        {
              $tabs .= " delete from CUSTOM_DFCONF; ";
			}
			
            print_r($tabs);
			if($tabs!='')
			{
				 $this->db->exec($tabs);//创建表并加索引
			}
  
  }
  
  public function ExistsTab($tab_name){
  
	    $cmd= "SELECT COUNT(*) c FROM sqlite_master where type='table' and name='". $tab_name ."';";
		$sth = $this->db->prepare($cmd);
		$sth->execute();
		$result = $sth->fetchAll();
		print_r($result);
		$total= $result[0]['c'];   
    
		   if (0 == $total)
            {  
                return false;
            }
            else
            {
                return true;
            }  
  }
  
  public function insert($tab_name,$key_list,$value_list)
  {
    $str="INSERT INTO ".$tab_name." (".$key_list.") values(".$value_list.");";
    //$result=$this->db->exec("INSERT INTO ".$tab_name." (".$key_list.") values(".$value_list.")");
    if (!$result) {
      return false;
    }
	return $str;
    // echo "{{{INSERT INTO ".$tab_name." (".$key_list.") values(".$value_list.")}}}}";
    //$res=$this->db->beginTransaction();//事务回gun
  }
   public function insertMore($tab_name)
  {
    //开启事物,此时会关闭自动提交 
    $this->db->beginTransaction();
    $sql = "INSERT INTO ".$tab_name." (id,urls,ip) values (?,?,?)";
    $stmt = $this->db->prepare($sql);
    //传入参数
	while($s <= 99999){
    $stmt->execute(array(null,"test".$s,"w"));
	$s++;
    $stmt->execute(array(null,"test5","w"));
    $stmt->execute(array(null,"test3","w"));
	}
    //提交事物，并且 数据库连接返回到自动提交模式
    $this->db->commit();
  }
  
  public function insert_dc_search_system($result,$insert_dc_search_system)
  {
	  try{
    //开启事物,此时会关闭自动提交 
    $this->db->beginTransaction();
    $sql = "insert into TREE_COMMON_DATA_BEAN (ID,DTNAME,DTNAMESHORT,IMAGE_TYPE,DTID1,DTID1_SUB,DATA1_TYPE,DATA1_TYPE1,DATA1_IMAGE,DATA1_PRE,DATA1_ADD_SUB,DATA1_ADD_SUB_HUND_CORE,DATA1_TYPE2,DATA1_TYPE3,DATA1_TYPE4,DATA1_TYPE5,DTID2,DTID2_SUB,DATA2_TYPE,DATA2_IMAGE,DTID3,DTID3_SUB,DATA3_TYPE,DATA3_IMAGE,DTID4,DTID4_SUB,DATA4_TYPE,DATA4_IMAGE,AOD,FIRST_ID,DTIDJson) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?);";
    $stmt = $this->db->prepare($sql);
    //传入参数
	foreach($result as $item){

        $stmt->execute(array($item["ID"],replacestr($item["dtname"]),replacestr($item["dtnameshort"]),$item["ImageType"],$item["DTID1"],$item["DTID1Sub"],$item["Data1Type"],$item["Data1Type1"],$item["Data1Image"],$item["Data1Pre"],$item["Data1AddSub"],$item["Data1AddSubHundCore"], $item["Data1Type2"],  $item["Data1Type3"], $item["Data1Type4"], $item["Data1Type5"],$item["DTID2"],$item["DTID2Sub"],$item["Data2Type"],$item["Data2Image"],$item["DTID3"],$item["DTID3Sub"],$item["Data3Type"],$item["Data3Image"],$item["DTID4"],$item["DTID4Sub"],$item["Data4Type"],$item["Data4Image"],$item["aod"],$insert_dc_search_system[$item["scode1"]],$item["DTIDJson"]));

		
	}
    //提交事物，并且 数据库连接返回到自动提交模式
    $this->db->commit();
	}catch(PDOException $e){
		echo '执行失败'.$e->getMessage();
	}
  }
  
  
  public function insert_dc_code_class($result,$newresultscode,$newresultmcode,$newdc_code_datatype,$VersionNo)
  {

    /* foreach($result as $item)
	 {
		 $newresultscode[$item["scode"]]=$item['ID'];
		 $newresultmcode[$item["mcode"]]=$item['ID'];
	 }*/
   //print_r($newresultscode);
	  try{
    //开启事物,此时会关闭自动提交 
    $this->db->beginTransaction();
    

    $sql = "insert into TREE_SECOND_BEAN (ID,MCODE,SNAME,SNAMESHORT,SCODE,SOD,HASNODE,SDESC,ParentID,TREE_DEPTH,HASLEAVES,FLAG)values(?,?,?,?,?,?,?,?,?,'',?,?)";
    $stmt = $this->db->prepare($sql);
    $VersionNo=number_format($VersionNo+0.01, 2);
	$sql1 = "insert into TREE_FIRST_BEAN (ID,MCODE,SNAME,SCODE,SOD,SDESC,HASLEAVES,CD,FLAG) values(?,?,?,?,?,?,?,'".$VersionNo."',?)";
    $stmt1 = $this->db->prepare($sql1);

    //传入参数
	foreach($result as $item){
		$ParentID=empty($newresultscode[$item["mcode"]])?0:$newresultscode[$item["mcode"]];
        $hasnode=empty($newresultmcode[$item["scode"]])?0:1;
		 $hasleaves=empty($newdc_code_datatype[$item["scode"]])?0:1;
		 //$hasleaves=$hasnode==0?"0":$hasleaves;
		if($ParentID!=0)
	  {
        $stmt->execute(array($item["ID"],$item["mcode"],replacestr($item["sname"]),replacestr($item["snameshort"]),$item["scode"],$item["sod"],$hasnode,$item["sdesc"],$ParentID, $hasleaves,$item["flag"]));
	  }
	  else if($item["mcode"]=='')
	  {
		  $stmt1->execute(array($item["ID"],$item["mcode"],replacestr($item["sname"]),$item["scode"],$item["sod"],$item["sdesc"], $hasleaves,$item["flag"]));
	  } 
	}
    //提交事物，并且 数据库连接返回到自动提交模式
    $this->db->commit();
	}catch(PDOException $e){
		echo '执行失败'.$e->getMessage();
	}
  }
  
  
  public function insert_dc_code_datatype($result,$newresultscode,$resultDIDarr)
  {
	  
	try{
    //开启事物,此时会关闭自动提交 
    $this->db->beginTransaction();
    //$sql = "insert into dc_code_datatype (ID,dtname,dtymd,scode1,scode2,scode3,scode4,scode5,ParentID,isSubDatas,subDatasTitle,subAtt1Db,subAtt2Db,subAtt3Db,Data1Type,Data2Type,Data3Type,Data4Type,Data5Type,Data6Type,Data7Type,Data8Type,Data9Type,Data10Type,Data11Type,Data12Type,Data13Type,Data14Type,Data15Type,Data16Type,Data17Type,Data18Type,Data19Type,Data20Type,ndate,ndata1,ndata2,ndata3,ndata4,ndata5,ndata6,ndata7,ndata8,ndata9,ndata10,ndata11,ndata12,ndata13,ndata14,ndata15,ndata16,ndata17,ndata18,ndata19,ndata20,npredata,naddsubdata,naddsubhundcore,targetflagvar,isbuy,SYear,EYear,pinzhong)values(?,?,?,?,? ,?,?,?,?,?,?,?,?,?,?,?,?,? ,?,?,?,?,?,?,?,?,?,?,? ,? ,? ,? ,?,? ,? ,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,? ,?,?,?,?,?,?)";


  $sql="insert into  TREE_FIFTH_BEAN (ID,SCODE1 ,SCODE2 ,SCODE3 ,SCODE4 ,SCODE5 ,DTNAME ,DTYMD ,DTCOMPARETYPE ,DBASE_TYPE ,IS_SUB_DATAS ,SUB_DATAS_TITLE ,SUB_DATAS_DB ,SUB_DATAS_DB2 ,SUB_ATT1_DB ,SUB_ATT2_DB ,SUB_ATT3_DB ,DATA1_TYPE ,DATA1_UNIT_TYPE ,DATA1_UNIT_CONV ,IS_DATA2 ,DATA2_TYPE ,DATA2_UNIT_TYPE ,DATA2_UNIT_CONV ,IS_DATA3 ,DATA3_TYPE ,DATA3_UNIT_TYPE ,DATA3_UNIT_CONV ,IS_DATA4 ,DATA4_TYPE ,DATA4_UNIT_TYPE ,DATA4_UNIT_CONV ,IS_DATA5 ,DATA5_TYPE ,DATA5_UNIT_TYPE ,DATA5_UNIT_CONV ,IS_PRE_DATA ,IS_ADD_SUB_DATA ,IS_ADD_SUB_HUND_CORE ,STARTDATE ,ENDDATAIS_CURRENT ,ENDDATA ,DTOD ,DTDESC ,TECHDBNAME ,TECHTABLENAME ,TECHSQLMAIN ,NDATE ,NDATA1 ,NDATA2 ,NDATA3 ,NDATA4 ,NDATA5 ,NPREDATA ,NADDSUBDATA ,NADDSUBHUNDCORE ,DDATE ,DDATA1 ,DDATA2 ,DDATA3 ,DDATA4 ,DDATA5 ,DPREDATA ,DADDSUBDATA ,DADDSUBHUNDCORE ,ISBUY ,TARGET_FLAG_VAR ,DATA6_TYPE ,DATA7_TYPE ,DATA8_TYPE ,DATA9_TYPE ,DATA10_TYPE ,DATA11_TYPE ,DATA12_TYPE ,DATA13_TYPE ,DATA14_TYPE ,DATA15_TYPE ,DATA16_TYPE ,DATA17_TYPE ,DATA18_TYPE ,DATA19_TYPE ,DATA20_TYPE ,NDATA6 ,NDATA7 ,NDATA8 ,NDATA9 ,NDATA10 ,NDATA11 ,NDATA12 ,NDATA13 ,NDATA14 ,NDATA15 ,NDATA16 ,NDATA17 ,NDATA18 ,NDATA19 ,NDATA20,PINZHONG,DATA_SOURCE,UNITSTRINGS,UNITCONVERS )values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";



    $stmt = $this->db->prepare($sql);
	//print_r($result);
    //传入参数
	foreach($result as $item){
               if(!empty($item['scode5']))
		   {
              $pid=$newresultscode[$item['scode5']];
			  $cengji=5;
		   }
            else if(!empty($item['scode4']))
		   {
              $pid=$newresultscode[$item['scode4']];
			  $cengji=4;
		   }
		   else if(!empty($item['scode3']))
		   {
              $pid=$newresultscode[$item['scode3']];
			   $cengji=3;
		   }
		   else if(!empty($item['scode2']))
		   {
              $pid=$newresultscode[$item['scode2']];
			  $cengji=2;
		   }
		   else if(!empty($item['scode1']))
		   {
              $pid=$newresultscode[$item['scode1']];
              $cengji=1;
		   }
		   
		   $isinsert=1;
		   $errorcode='';
		   for($i=1;$i<=$cengji;$i++)
		   {
			//    if($i==1)
			//    {
			// 	   if(empty($newresultmcode[$item['scode1']]))
			// 	   {
			// 		   $isinsert=0;
			// 		   $errorcode=$item['scode1'];
			// 		   break;
			// 	   }
				   
			//    }
			//    else
			//    {
				   if(empty($newresultscode[$item['scode'.$i]]))
				   {
					   $isinsert=0;
					   $errorcode=$item['scode'.$i];
					   echo 'scode'.$i."||";
					   break;
				   }
			   //}
		   }
		   $item["isSubDatas"]=0;
           if(is_array($resultDIDarr))
                $item["isSubDatas"]=in_array($item["ID"],$resultDIDarr)?1:0;
		   // $item["isSubDatas"]=in_array($item["ID"],$resultDIDarr)?1:0;
             /* $sql="insert into  TREE_FIFTH_BEAN (ID,SCODE1 ,SCODE2 ,SCODE3 ,SCODE4 ,SCODE5 ,DTNAME ,DTYMD ,DTCOMPARETYPE ,DBASE_TYPE ,IS_SUB_DATAS ,SUB_DATAS_TITLE ,SUB_DATAS_DB ,SUB_DATAS_DB2 ,SUB_ATT1_DB ,SUB_ATT2_DB ,SUB_ATT3_DB ,DATA1_TYPE ,DATA1_UNIT_TYPE ,DATA1_UNIT_CONV ,IS_DATA2 ,DATA2_TYPE ,DATA2_UNIT_TYPE ,DATA2_UNIT_CONV ,IS_DATA3 ,DATA3_TYPE ,DATA3_UNIT_TYPE ,DATA3_UNIT_CONV ,IS_DATA4 ,DATA4_TYPE ,DATA4_UNIT_TYPE ,DATA4_UNIT_CONV ,IS_DATA5 ,DATA5_TYPE ,DATA5_UNIT_TYPE ,DATA5_UNIT_CONV ,IS_PRE_DATA ,IS_ADD_SUB_DATA ,IS_ADD_SUB_HUND_CORE ,STARTDATE ,ENDDATAIS_CURRENT ,ENDDATA ,DTOD ,DTDESC ,TECHDBNAME ,TECHTABLENAME ,TECHSQLMAIN ,NDATE ,NDATA1 ,NDATA2 ,NDATA3 ,NDATA4 ,NDATA5 ,NPREDATA ,NADDSUBDATA ,NADDSUBHUNDCORE ,DDATE ,DDATA1 ,DDATA2 ,DDATA3 ,DDATA4 ,DDATA5 ,DPREDATA ,DADDSUBDATA ,DADDSUBHUNDCORE ,ISBUY ,TARGET_FLAG_VAR ,DATA6_TYPE ,DATA7_TYPE ,DATA8_TYPE ,DATA9_TYPE ,DATA10_TYPE ,DATA11_TYPE ,DATA12_TYPE ,DATA13_TYPE ,DATA14_TYPE ,DATA15_TYPE ,DATA16_TYPE ,DATA17_TYPE ,DATA18_TYPE ,DATA19_TYPE ,DATA20_TYPE ,NDATA6 ,NDATA7 ,NDATA8 ,NDATA9 ,NDATA10 ,NDATA11 ,NDATA12 ,NDATA13 ,NDATA14 ,NDATA15 ,NDATA16 ,NDATA17 ,NDATA18 ,NDATA19 ,NDATA20,PINZHONG )
*/
  //$sql="ID,scode1 ,scode2 ,scode3 ,scode4 ,scode5 ,dtname ,dtymd ,dtcomparetype ,dbaseType ,isSubDatas ,subDatasTitle ,subDatasDb ,subDatasDb2 ,subAtt1Db ,subAtt2Db ,subAtt3Db ,Data1Type ,Data1UnitType ,Data1UnitConv ,isData2 ,Data2Type ,Data2UnitType ,Data2UnitConv ,isData3 ,Data3Type ,Data3UnitType ,Data3UnitConv ,isData4 ,Data4Type ,Data4UnitType ,Data4UnitConv ,isData5 ,Data5Type ,Data5UnitType ,Data5UnitConv ,isPreData ,isAddSubData ,isAddSubHundCore ,startdate ,enddataisCurrent ,enddata ,dtod ,dtdesc ,techdbname ,techtablename ,techsqlmain ,ndate ,ndata1 ,ndata2 ,ndata3 ,ndata4 ,ndata5 ,npredata ,naddsubdata ,naddsubhundcore ,ddate ,Ddata1 ,Ddata2 ,Ddata3 ,Ddata4 ,Ddata5 ,dpredata ,daddsubdata ,daddsubhundcore ,ISBUY ,TargetFlagVar ,Data6Type ,Data7Type ,Data8Type ,Data9Type ,Data10Type ,Data11Type ,Data12Type ,Data13Type ,Data14Type ,Data15Type ,Data16Type ,Data17Type ,Data18Type ,Data19Type ,Data20Type ,ndata6 ,ndata7 ,ndata8 ,ndata9 ,ndata10 ,ndata11 ,ndata12 ,ndata13 ,ndata14 ,ndata15 ,ndata16 ,ndata17 ,ndata18 ,ndata19 ,ndata20 ,pinzhong )";
  
		   $unitstrings = $item["UnitTypeName"].','.$item["npredata_UnitTypeName"].','.$item["naddsubdata_UnitTypeName"].','.$item["daddsubhundcore_UnitTypeName"].','.$item["UnitTypeName2"].','.$item["UnitTypeName3"].','.$item["UnitTypeName4"].','.$item["UnitTypeName5"].','.$item["UnitTypeName6"].','.$item["UnitTypeName7"].','.$item["UnitTypeName8"].','.$item["UnitTypeName9"].','.$item["UnitTypeName10"].','.$item["UnitTypeName11"].','.$item["UnitTypeName12"].','.$item["UnitTypeName13"].','.$item["UnitTypeName14"].','.$item["UnitTypeName15"].','.$item["UnitTypeName16"].','.$item["UnitTypeName17"].','.$item["UnitTypeName18"].','.$item["UnitTypeName19"].','.$item["UnitTypeName20"];

		   $unitconvers = $item["Data1UnitConv"].','.$item["npredata_UnitConv"].','.$item["naddsubdata_UnitConv"].','.$item["daddsubhundcore_UnitConv"].','.$item["Data2UnitConv"].','.$item["Data3UnitConv"].','.$item["Data4UnitConv"].','.$item["Data5UnitConv"].','.$item["Data6UnitConv"].','.$item["Data7UnitConv"].','.$item["Data8UnitConv"].','.$item["Data9UnitConv"].','.$item["Data10UnitConv"].','.$item["Data11UnitConv"].','.$item["Data12UnitConv"].','.$item["Data13UnitConv"].','.$item["Data14UnitConv"].','.$item["Data15UnitConv"].','.$item["Data16UnitConv"].','.$item["Data17UnitConv"].','.$item["Data18UnitConv"].','.$item["Data19UnitConv"].','.$item["Data20UnitConv"];
		 
		 if($isinsert)
		 {
			 $stmt->execute(array($item["ID"],$item["scode1"],$item["scode2"],$item["scode3"],$item["scode4"],$item["scode5"],replacestr($item["dtname"]),$item["dtymd"],$item["dtcomparetype"],$item["dbaseType"],$item["isSubDatas"],$item["subDatasTitle"],$item["subDatasDb"],$item["subDatasDb2"],$item["subAtt1Db"],$item["subAtt2Db"],$item["subAtt3Db"],$item["Data1Type"],$item["Data1UnitType"],$item["Data1UnitConv"],$item["isData2"],$item["Data2Type"],$item["Data2UnitType"],$item["Data2UnitConv"],$item["isData3"],$item["Data3Type"],$item["Data3UnitType"],$item["Data3UnitConv"],$item["isData4"],$item["Data4Type"],$item["Data4UnitType"],$item["Data4UnitConv"],$item["isData5"],$item["Data5Type"],$item["Data5UnitType"],$item["Data5UnitConv"],$item["isPreData"],$item["isAddSubData"],$item["isAddSubHundCore"],$item["startdate"],$item["enddataisCurrent"],$item["enddata"],$item["dtod"],$item["dtdesc"],$item["techdbname"],$item["techtablename"],$item["techsqlmain"],$item["ndate"],$item["ndata1"],$item["ndata2"],$item["ndata3"],$item["ndata4"],$item["ndata5"],$item["npredata"],$item["naddsubdata"],$item["naddsubhundcore"],$item["ddate"],$item["Ddata1"],$item["Ddata2"],$item["Ddata3"],$item["Ddata4"],$item["Ddata5"],$item["dpredata"],$item["daddsubdata"],$item["daddsubhundcore"],'0',$item["TargetFlagVar"],$item["Data6Type"],$item["Data7Type"],$item["Data8Type"],$item["Data9Type"],$item["Data10Type"],$item["Data11Type"],$item["Data12Type"],$item["Data13Type"],$item["Data14Type"],$item["Data15Type"],$item["Data16Type"],$item["Data17Type"],$item["Data18Type"],$item["Data19Type"],$item["Data20Type"],$item["ndata6"],$item["ndata7"],$item["ndata8"],$item["ndata9"],$item["ndata10"],$item["ndata11"],$item["ndata12"],$item["ndata13"],$item["ndata14"],$item["ndata15"],$item["ndata16"],$item["ndata17"],$item["ndata18"],$item["ndata19"],$item["ndata20"],$item["pinzhong"],$item["data_source"],$unitstrings,$unitconvers));
		 }
		 else
		 {
			 echo '--errorcode:'.$errorcode.'--\r\n';
		 }
			 
        
	}
    //提交事物，并且 数据库连接返回到自动提交模式
    $this->db->commit();
	}catch(PDOException $e){
		echo '执行失败'.$e->getMessage();
	}
  }
  
  
  
   public function insert_dc_code_datatype_subs($result)
  {
	try{
    //开启事物,此时会关闭自动提交 
    $this->db->beginTransaction();
    //$sql = "insert into dc_code_datatype_subs (ID,DID,sname,satt1,satt2,satt3,scode,ParentID)values(?,?,?,?,?,?,?,?)";
	$sql="insert into TREE_SUB_DATA_BEAN (ID,SNAME,SCODE,SDBTYPE,SATT1,SATT2,SATT3,AOD,MSG,FIFTH_ID,POSITION)values(?,?,?,?,?,?,?,?,?,?,?)";
    $stmt = $this->db->prepare($sql);
	//print_r($result);
    //传入参数
	foreach($result as $item){

        
                 $name='';
                if(!empty($item['satt3']))
				{
					 $name=$item['satt3']." ";
				}
				if(!empty($item['sname']))
				{
					 $name.=$item['sname']." ";
				}
				if(!empty($item['satt1']))
				{
					 $name.=$item['satt1']." ";
				}
				if(!empty($item['satt2']))
				{
					 $name.=$item['satt2'];
				}
				//$name=str_replace("'","''",$name);
				$Id=$item["ID"].",".$item["DID"];

		$stmt->execute(array($Id,replacestr($item["sname"]),$item["scode"],$item['sdbtype'],$item["satt1"],$item["satt2"],$item["satt3"],$item["aod"],$name,$item["DID"],($item["aod"]-1)));
	}
    //提交事物，并且 数据库连接返回到自动提交模式
    $this->db->commit();
	}catch(PDOException $e){
		echo '执行失败'.$e->getMessage();
	}
  }
  
  public function insert_dc_search_custom_dfconf($result)
  {
	try{
    //开启事物,此时会关闭自动提交 
    $this->db->beginTransaction();
    $sql = "insert into CUSTOM_DFCONF (ID,DFKEY,DFVALUE,REMARK,DFOD)values(?,?,?,?,?)";
    $stmt = $this->db->prepare($sql);
	//print_r($result);
    //传入参数
	foreach($result as $item){
		$stmt->execute(array($item["id"],$item["dfkey"],replacestr($item["dfvalue"]),replacestr($item['remark']),$item["dfod"]));
	}
    //提交事物，并且 数据库连接返回到自动提交模式
    $this->db->commit();
	}catch(PDOException $e){
		echo '执行失败'.$e->getMessage();
	}
  }
  
  public function total($tab_name,$tj='')//求总记录数目
  {
    $sth = $this->db->prepare('SELECT count(id) as c FROM '.$tab_name.' '.$tj);
    $sth->execute();
    $result = $sth->fetchAll();
    return $result[0]['c'];
  }
  
  public function totalxx($sql)//求总记录数目
  {
    $sth = $this->db->prepare($sql);
    $sth->execute();
    $result = $sth->fetchAll();
    return $result[0]['c'];
  }
  public function update()
  {
    # 修改
  }
  function delete($value='')
  {
    # 删除
  }
  public function query($tab_name,$tj='')//表名称和条件
  {
    $sth = $this->db->prepare('SELECT * FROM '.$tab_name.' '.$tj);
    // echo 'SELECT * FROM '.$tab_name.' '.$tj;
    $sth->execute();
    $result = $sth->fetchAll();
    return $result;
  }
}

 function replacestr($str)
{
    $arr=array("&lt;","&gt;","&amp;","&quot;","&times");
    $arr1=array("<",">","&","\"","×");
   $str=str_replace( $arr,$arr1,$str);
   return $str;
}

function array_iconv($str, $in_charset="gbk", $out_charset="utf-8")
{
	return $str;
 if(is_array($str))
 {
 foreach($str as $k => $v)
 {
  $str[$k] = array_iconv($v);
 }
 return $str;
 }
 else
 {
 if(is_string($str))
 {
  // return iconv('UTF-8', 'GBK//IGNORE', $str);
  return mb_convert_encoding($str, $out_charset, $in_charset);
 }
 else
 {
  return $str;
 }
 }
}
if($argv[1])
{
 $_REQUEST['mc_type']=$argv[1];
}
$mc_type=0;
if($_REQUEST['mc_type'])
{
   $mc_type=$_REQUEST['mc_type'];
}

if(!in_array($mc_type,array(0,1,2,3,4,5)))
{
   echo "错误的mc_type，请检查后再试。<br/>";
   exit;
}


$db=new SqliteDB($mc_type);


//$dsn = 'mysql:dbname=steelhome_t1;host=***********;port=4306';
//$user = 'root2';
//$password = '123456';

///$dsn = 'mysql:dbname=steelhome_t1;host=*************;port=3307';
//$user = 'dbread';
//$password = 'sth@50581010';

//采用预处理+事务处理执行SQL操作
//1.连接数据库
try {
    $pdo = new PDO($dsn, $user, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE,PDO::ERRMODE_EXCEPTION);
	echo "ok ";
} catch (PDOException $e) {
    die("数据库连接失败".$e->getMessage());
}
 
//2.执行数据操作
try{
	

	 $count_sql = "select VersionNo  from app_version where  VersionType=2 and  mc_type='".$mc_type."' and Status=1 limit 1";
	 $sth = $pdo->prepare($count_sql);
     $sth->execute();
     $resultVersionNo = $sth->fetchAll();
	
	  $sql="SELECT  `scode1` ,  `scode2` ,  `scode3` ,  `scode4` ,  `scode5` FROM  `dc_code_datatype` WHERE  `mc_type` ='".$mc_type."' AND STATUS =1";
     $sth = $pdo->prepare($sql);
     $sth->execute();
     $result = $sth->fetchAll();
    foreach($result as $item)
	 {
		 if(!empty($item['scode5']))
		   {
              $pid=$item['scode5'];
			 
		   }
		   else if(!empty($item['scode4']))
		   {
              $pid=$item['scode4'];
			 
		   }
		   else if(!empty($item['scode3']))
		   {
              $pid=$item['scode3'];
			   
		   }
		   else if(!empty($item['scode2']))
		   {
              $pid=$item['scode2'];
		   }
		   else if(!empty($item['scode1']))
		   {
              $pid=$item['scode1'];

		   }

		 $newdc_code_datatype[$pid]=1;
	 }

	 if( $mc_type==0)
	 {
		$count_sql = "select ID,mcode,sname as sname,snameshort,scode,sod,sdesc,0 as hasnode,flag from dc_code_class where   mc_type='".$mc_type."' and scode not in ('K','L','O','P','Q','R') and Status =1 order by sod";
	 }
	 else if( $mc_type!=2)
	 {
		$count_sql = "select ID,mcode,sname as sname,snameshort,scode,sod,sdesc,0 as hasnode,flag from dc_code_class where   mc_type='".$mc_type."' and Status =1 order by sod";
	 }
	 else
	 {
		$count_sql = "select ID,mcode,sname as sname,snameshort,scode,sod,sdesc,0 as hasnode,flag from dc_code_class where   mc_type='".$mc_type."' and scode!='AE' and Status =1 order by sod";
	 }
	 
	 $sth = $pdo->prepare($count_sql);
     $sth->execute();
     $result = $sth->fetchAll();

     $result=array_iconv($result);
      foreach($result as $item)
	 {
		 $newresultscode[$item["scode"]]=$item['ID'];
		 $newresultmcode[$item["mcode"]]=$item['ID'];
	 }
      

	 $db->insert_dc_code_class($result, $newresultscode, $newresultmcode,$newdc_code_datatype,$resultVersionNo[0]['VersionNo']);
	 echo "插入dc_code_class操作完成<br/>";
	
     $count_sql = "select ID,scode1,dtname as dtname,dtnameshort as dtnameshort,ImageType,DTID1,DTID1Sub, Data1Type, Data1Type1, Data1Image, Data1Pre, Data1AddSub, Data1AddSubHundCore, Data1Type2, Data1Type3, Data1Type4, Data1Type5, DTID2 , DTID2Sub, Data2Type ,Data2Image, DTID3 , DTID3Sub , Data3Type , Data3Image, DTID4 , DTID4Sub , Data4Type , Data4Image,DTIDJson, zhou1,zhou2,zhou3,zhou4, isjz,isbg,isfg,color,aod from dc_search_system where IsUse='0' and mc_type='".$mc_type."' order by aod";
	 $sth = $pdo->prepare($count_sql);
    
     $sth->execute();
     $result = $sth->fetchAll();
    
	  $db->insert_dc_search_system(array_iconv($result),$newresultscode);
	  echo "插入dc_search_system操作完成<br/>";
	 

$count_sql = "select distinct DID from dc_code_datatype_subs,dc_code_datatype where dc_code_datatype_subs.did= dc_code_datatype.id and dc_code_datatype_subs.Status =1 and dc_code_datatype.Status =1  and dc_code_datatype_subs.mc_type='".$mc_type."' and dc_code_datatype.mc_type='".$mc_type."'"; 
$sth = $pdo->prepare($count_sql);
$sth->execute();
$result = $sth->fetchAll();
foreach($result as $item)
 {
	 $resultDIDarr[]=$item['DID'];
 }

	$count_sql = "select count(*) as c from dc_code_datatype  where mc_type='".$mc_type."' and Status =1 and ID not in(".$NotDIDStr.")";
	$sth = $pdo->prepare($count_sql);
    $sth->execute();
    $result = $sth->fetchAll();
    $totle= $result[0]['c'];
	$pagesize=1000;
    $pagecount=ceil($totle/$pagesize);
    for($i = 0;$i<=$pagecount;$i++){
        // $start = $i;
        // $end = $i + 10000;
		$dataunitconv = "Data1UnitConv,UnitTypeName,Data2UnitConv,UnitTypeName2,Data3UnitConv,UnitTypeName3,Data4UnitConv,UnitTypeName4,Data5UnitConv,UnitTypeName5,Data6UnitConv,UnitTypeName6,Data7UnitConv,UnitTypeName7,Data8UnitConv,UnitTypeName8,Data9UnitConv,UnitTypeName9,Data10UnitConv,UnitTypeName10,Data11UnitConv,UnitTypeName11,Data12UnitConv,UnitTypeName12,Data13UnitConv,UnitTypeName13,Data14UnitConv,UnitTypeName14,Data15UnitConv,UnitTypeName15,Data16UnitConv,UnitTypeName16,Data17UnitConv,UnitTypeName17,Data18UnitConv,UnitTypeName18,Data19UnitConv,UnitTypeName19,Data20UnitConv,UnitTypeName20,npredata_UnitConv,dc_code_datatype_db.npredataDW as npredata_UnitTypeName,naddsubdata_UnitConv,dc_code_datatype_db.naddsubdataDW as naddsubdata_UnitTypeName,daddsubhundcore_UnitConv,dc_code_datatype_db.naddsubhundcoreDW as daddsubhundcore_UnitTypeName";

        $sql = "select ID,scode1 ,scode2 ,scode3 ,scode4 ,scode5 ,dtname ,dtymd ,dtcomparetype ,dbaseType ,isSubDatas ,subDatasTitle ,subDatasDb ,subDatasDb2 ,subAtt1Db ,subAtt2Db ,subAtt3Db ,Data1Type ,Data1UnitType ,Data1UnitConv ,isData2 ,Data2Type ,Data2UnitType ,Data2UnitConv ,isData3 ,Data3Type ,Data3UnitType ,Data3UnitConv ,isData4 ,Data4Type ,Data4UnitType ,Data4UnitConv ,isData5 ,Data5Type ,Data5UnitType ,Data5UnitConv ,isPreData ,isAddSubData,isAddSubHundCore  ,startdate ,enddataisCurrent ,enddata ,dtod ,dtdesc ,techdbname ,techtablename ,techsqlmain ,ndate ,ndata1 ,ndata2 ,ndata3 ,ndata4 ,ndata5 ,npredata ,naddsubdata ,naddsubhundcore ,ddate ,Ddata1 ,Ddata2 ,Ddata3 ,Ddata4 ,Ddata5 ,dpredata ,daddsubdata ,daddsubhundcore ,TargetFlagVar ,Data6Type ,Data7Type ,Data8Type ,Data9Type ,Data10Type ,Data11Type ,Data12Type ,Data13Type ,Data14Type ,Data15Type ,Data16Type ,Data17Type ,Data18Type ,Data19Type ,Data20Type ,ndata6 ,ndata7 ,ndata8 ,ndata9 ,ndata10 ,ndata11 ,ndata12 ,ndata13 ,ndata14 ,ndata15 ,ndata16 ,ndata17 ,ndata18 ,ndata19 ,ndata20 ,pinzhong,data_source,".$dataunitconv." from dc_code_datatype,dc_code_datatype_db where dc_code_datatype_db.DID = dc_code_datatype.ID and dc_code_datatype.mc_type='".$mc_type."' and dc_code_datatype_db.mc_type='".$mc_type."' and Status =1 and ID not in(".$NotDIDStr.") order by dtod limit  ".($i*$pagesize).",$pagesize";
		 //$sql = "select ID,scode1,scode2,scode3,scode4,dtname as dtname,dtymd,dtcomparetype,dbaseType,isSubDatas,subDatasTitle as subDatasTitle,subDatasDb,subDatasDb2,subAtt1Db as subAtt1Db,subAtt2Db as subAtt2Db,subAtt3Db as subAtt3Db,Data1Type,Data2Type,Data3Type,Data4Type,Data5Type,Data6Type,Data7Type,Data8Type,Data9Type,Data10Type,Data11Type,Data12Type,Data13Type,Data14Type,Data15Type,Data16Type,Data17Type,Data18Type,Data19Type,Data20Type,startdate,enddata,dtod,ndate as ndate,ndata1 as ndata1,ndata2 as ndata2,ndata3 as ndata3,ndata4 as ndata4,ndata5 as ndata5,ndata6 as ndata6,ndata7 as ndata7,ndata8 as ndata8,ndata9 as ndata9,ndata10 as ndata10,ndata11 as ndata11,ndata12 as ndata12,ndata13 as ndata13,ndata14 as ndata14,ndata15 as ndata15,ndata16 as ndata16,ndata17 as ndata17,ndata18 as ndata18,ndata19 as ndata19,ndata20 as ndata20,npredata as npredata,naddsubdata as naddsubdata,naddsubhundcore as naddsubhundcore,TargetFlagVar from dc_code_datatype,dc_code_datatype_db where dc_code_datatype_db.DID = dc_code_datatype.ID and dc_code_datatype.mc_type='0' and dc_code_datatype_db.mc_type='0' and Status =1 order by dtod limit  ".($i*$pagesize).",$pagesize";
		//echo $sql;
        $sth = $pdo->prepare($sql);
		$sth->execute();
		$result = $sth->fetchAll();
		//if(is_array($resultDIDarr))
			$db->insert_dc_code_datatype(array_iconv($result),$newresultscode,$resultDIDarr);
    }
	 echo "插入dc_code_datatype操作完成<br/>";
	
	 
		$count_sql = "select count(*) as c from dc_code_datatype_subs ,dc_code_datatype where dc_code_datatype_subs.did= dc_code_datatype.id and dc_code_datatype_subs.Status =1 and dc_code_datatype.Status =1  and dc_code_datatype_subs.mc_type='".$mc_type."' and dc_code_datatype.isSubDatas=1";
	$sth = $pdo->prepare($count_sql);
    $sth->execute();
    $result = $sth->fetchAll();
    $totle= $result[0]['c'];
	echo $totle;
	$pagesize=1000;
    $pagecount=ceil($totle/$pagesize);
    for($i = 0;$i<=$pagecount;$i++){
        // $start = $i;
        // $end = $i + 10000;
        $sql = "select dc_code_datatype_subs.ID,DID,sname as sname,scode as scode,sdbtype,satt1 as satt1,satt2 as satt2,satt3 as satt3,aod,dc_code_datatype.id as ParentID from dc_code_datatype_subs,dc_code_datatype where dc_code_datatype_subs.did= dc_code_datatype.id and dc_code_datatype_subs.Status =1  and dc_code_datatype.Status =1  and dc_code_datatype_subs.mc_type='".$mc_type."'  and dc_code_datatype.isSubDatas=1 order by aod
 limit ".($i*$pagesize).",$pagesize";
	//echo $sql;
         $sth = $pdo->prepare($sql);
		 $sth->execute();
		 $result = $sth->fetchAll();
		$db->insert_dc_code_datatype_subs(array_iconv($result));
    }
    echo "插入dc_code_datatype_subs操作完成<br/>";
	
	$count_sql = "select id,dfkey,dfvalue,remark,dfod from dc_search_custom_dfconf where mc_type='".$mc_type."'  "; 
	$sth = $pdo->prepare($count_sql);
	$sth->execute();
	$result = $sth->fetchAll();
	$db->insert_dc_search_custom_dfconf(array_iconv($result));
	echo "dc_search_custom_dfconf<br/>";
	
	
}catch(PDOException $e){
    echo '执行失败'.$e->getMessage();
}
 
 echo "操作完成<br/>";
 

//这里需要注意该目录是否存在，并且有创建的权限
// $zipname = dirname(__FILE__).'/yun.zip' ;
//  //这是要打包的文件地址数组
// $files = array(dirname(__FILE__)."/yun.dat");


$zipname = dirname(__FILE__).'/'.$pcsqllitename[$mc_type][1] ;
$files = array(dirname(__FILE__)."/".$pcsqllitename[$mc_type][0]);


// if($mc_type!=0)
// {
//  $zipname = dirname(__FILE__).'/yun_ng.zip' ;
//  $files = array(dirname(__FILE__)."/yun_ng.dat");
// } 

$db=null;
$pdo=null;
$zip = new ZipArchive();
$res = $zip->open($zipname, ZipArchive::CREATE);
if ($res === TRUE) {
    foreach ($files as $file) {
 //这里直接用原文件的名字进行打包，也可以直接命名，需要注意如果文件名字一样会导致后面文件覆盖前面的文件，所以建议重新命名
     $new_filename = substr($file, strrpos($file, '/') + 1);
     $zip->addFile($file, $new_filename);
}
}
$zip->close();

echo "操作压缩完成<br/>";


$md5file = md5_file($zipname);

echo  "文件MD5码：".$md5file;
if($md5file)
{

	//$wdsn = 'mysql:dbname=steelhome_t1;host=***********;port=4306';//可写库
	//$wuser = 'root2';
	//$wpassword = '123456';
	//采用预处理+事务处理执行SQL操作
	//1.连接数据库
	try {
		$wpdo = new PDO($wdsn, $wuser, $wpassword);
		$wpdo->setAttribute(PDO::ATTR_ERRMODE,PDO::ERRMODE_EXCEPTION);
		echo "ok ";
	} catch (PDOException $e) {
		die("数据库连接失败".$e->getMessage());
	}
	$sql="update app_version set AndroidSystemVersionUrlMd5='".$md5file."' where  VersionType=2 and  mc_type='".$mc_type."' and Status=1 limit 1";
	$sth1 = $wpdo->prepare($sql);
	$sth1->execute();
}

/*$wdsn = 'mysql:dbname=steelhome_t1;host=***********;port=4306';//可写库
$wuser = 'root2';
$wpassword = '123456';
//采用预处理+事务处理执行SQL操作
//1.连接数据库
try {
    $wpdo = new PDO($wdsn, $wuser, $wpassword);
    $wpdo->setAttribute(PDO::ATTR_ERRMODE,PDO::ERRMODE_EXCEPTION);
	echo "ok ";
} catch (PDOException $e) {
    die("数据库连接失败".$e->getMessage());
}
$sql="update app_version set DownloadUrlMd5='".$md5file."' where  VersionType=2 and  mc_type=0 and Status=1 limit 1";
$sth1 = $wpdo->prepare($sql);
$sth1->execute();
*/

?>