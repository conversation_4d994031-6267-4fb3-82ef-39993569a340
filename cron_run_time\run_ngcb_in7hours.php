<?php
require_once ("/etc/steelconf/config/isholiday.php");
/*每天七点生成前一天的南钢成本数据*/

if($argc > 1){
	$date = $argv[1];
	$Type = $argv[2];
	$date = date("Y-m-d",strtotime($date."-1 day"));
	while( _isholiday ( $date )){
		$date = date("Y-m-d",strtotime($date."-1 day"));
	}
}else{
}
//chdir("/usr/local/www/www.steelhome.cn/data/v1.5/web");
chdir("/usr/local/www/dc.steelhome.cn/v1.5/web");
$_REQUEST["action"]=$_GET["action"]="calc_cbmx";
$_REQUEST["Date"]=$_GET["Date"]=$date;
$_REQUEST["Type"]=$_GET["Type"]=$Type;
include_once ("cbmodel.php");

echo "shi_end";


?>