var maxwidthscen=1920;
var nowdate='2021-06-02';

nowdate='';
var articleid=0;
var GUID='';
$(function() {
    // $('#chart1Loading').show();
    // $('#chart2Loading').show();
    // $('#chart3Loading').show();
    // $('#chart4Loading').show();
    var str=getQueryString('date');
    if(str!=null)
    {
        nowdate=str;
    }
    GUID=getQueryString('GUID');
    if(GUID==null)
    {
        GUID='';
    }

    var activeIndex = -1;
    var liDoms = $(".nav ul li");
    var iframeDoms = [];
    var pepoactive=false;
    var xunhuannum=0;
    var fontSize=13;
    if(isphone==1){
        fontSize=28;
    }

    var activeId=1;
    liDoms.bind("click", function(e){
        var idx = Array.prototype.indexOf.call(liDoms, this);
        activeId = e.target.id;
        
        pepoactive=true;
        xunhuannum=0;
        clickLiByIndex(idx);
    })

    /*点击某一个*/
    function clickLiByIndex(idx){

        if(idx === activeIndex) return;
        var predom = liDoms[activeIndex];
        //取消之前的高亮样式
        if(predom){
            $(predom).removeClass("active");
        }
        //显示当前高亮样式
        var target = liDoms[idx];
        if(target){
            //EUI.addClassName(target, "active");
            $(target).addClass("active");

        }
        nowdate='';
        
        activeIndex = idx;
        activeId=$(".nav ul li").eq(idx).attr('id');
        clickIndex(activeId);
    }
    var active=getQueryString('activeIndex');
    if(active==null)
    {
        active=0;
    }
    

    //clickLiByIndex(active);
    
    clickIndex(1);
    function clickIndex(idx)
    {
       
                document.getElementById("chart21").removeAttribute("_echarts_instance_");
                document.getElementById("chart22").removeAttribute("_echarts_instance_");
                document.getElementById("chart23").removeAttribute("_echarts_instance_");
                document.getElementById("chart24").removeAttribute("_echarts_instance_");
                $('#mokuai4').show();
                window.scrollTo(0, 0);
                initChart4();
            
            
    }



 function getChartNum(len){
    var provideNumber=4;
    var screenWidth = document.documentElement.clientWidth;
    if(len>6){
        if(screenWidth<1400){
            provideNumber=2
        }else{
            provideNumber=3
        }
    }else if(len==6){
        if(screenWidth<1165){
            provideNumber=3
        }else if(screenWidth<1429){
            provideNumber=4
        }else{
            provideNumber=5
        }
    }else if(len==5){
        if(screenWidth<1165){
            provideNumber=3
        }else if(screenWidth<1429){
            provideNumber=4
        }else if(screenWidth<1689){
            provideNumber=5
        }else{
            provideNumber=6
        }
    }else if(len==4){
        if(screenWidth<1165){
            provideNumber=4
        }else if(screenWidth<1406){
            provideNumber=5
        }else if(screenWidth<1620){
            provideNumber=6
        }else{
            provideNumber=7
        }
    }else{
        provideNumber=8
        if(screenWidth<1165){
            provideNumber=6
        }else if(screenWidth<1429){
            provideNumber=8
        }
    }
    return provideNumber;
 }

 function getChart2Num(len){
    var provideNumber=4;
    var screenWidth = document.documentElement.clientWidth;
    if(len>6){
        if(screenWidth<1400){
            provideNumber=2
        }else{
            provideNumber=3
        }
    }else if(len==6){
        if(screenWidth<1165){
            provideNumber=3
        }else if(screenWidth<1229){
            provideNumber=4
        }else if(screenWidth<1410){
            provideNumber=5
        }else if(screenWidth<1560){
            provideNumber=6
        }else{
            provideNumber=7
        }
    }else if(len==5){
        if(screenWidth<1165){
            provideNumber=3
        }else if(screenWidth<1429){
            provideNumber=4
        }else{
            provideNumber=6
        }
    }else if(len==4){
        if(screenWidth<1069){
            provideNumber=6
        }else if(screenWidth<1165){
            provideNumber=8
        }else if(screenWidth<1401){
            provideNumber=9
        }else if(screenWidth<1529){
            provideNumber=10
        }else{
            provideNumber=11
        }
    }else{
        if(screenWidth<1165){
            provideNumber=8
        }else if(screenWidth<1429){
            provideNumber=10
        }else{
            provideNumber=12
        }
    }
    return provideNumber;
 }
 
 function initChart4()
 {
     $('#chart21Loading').show();
     $('#chart22Loading').show();
     $('#chart23Loading').show();
     $('#chart24Loading').show();
     //$('#chart25Loading').show();
     axios.post("meeting.php?action=getMeetingSurveyScreenData_2&type=1&mtid="+mtid+"&id="+id+"&isphone="+isphone )
     .then((res) => 
     {
        $('#surveytitle').html(res.title);
         $('#chart21Loading').hide();
         $('#chart22Loading').hide();
         $('#chart23Loading').hide();
         $('#chart24Loading').hide();
        // $('#chart25Loading').hide();
         var myChart = echarts.init(document.getElementById('chart21'));
         var myChart2 = echarts.init(document.getElementById('chart22'));
         var myChart3 = echarts.init(document.getElementById('chart23'));
         var myChart4 = echarts.init(document.getElementById('chart24'));
         var legend = [];
         var series = [];
         if (res.screendata1) {
            $('#chart21title').html(res.screentitle1);
             let myCharts = echarts.init(document.getElementById('chart21'));
             if(res.screendata1[0].content!="" && res.screendata1[0].content!=undefined){
                
                if (res.screendata1) {
                    var str="";
                    for(var i=0;i<res.screendata1.length;i++){
                        str+='<div class="jq33-content"> <div class="left"><span>'+(i+1)+"、"+res.screendata1[i].content+'</span></div></div>';
                       
                    }
                    document.getElementById('chart21').innerHTML=str;
                }
             }else{
             let myCharts = echarts.init(document.getElementById('chart21'))
             //3.配置
             let option = {
                grid: {
                    show: true,                                 //是否显示图表背景网格    
                    left: 5,                                    //图表距离容器左侧多少距离
                    right: 5,                                //图表距离容器右侧侧多少距离
                    bottom: isphone==1 ? 80 : 20,                              //图表距离容器上面多少距离
                     top: isphone==1? 40 :30,  
                    containLabel: true,                     //防止标签溢出  
                   
                },
                xAxis: {
                    type: 'category',
                    
                    data:res.screendata1.map(item => item.name),
                    axisLabel: {
                        textStyle: {
                            color: '#01C4F7', //坐标值得具体的颜色                   
                        },
                        fontSize: fontSize, //设置坐标轴文本标签的字体大小
                        formatter:function(params){
                            var newParamsName = "";// 最终拼接成的字符串
                               var paramsNameNumber = params.length;// 实际标签的个数
                               var provideNumber =isphone==1 ? 4: 8;// 每行能显示的字的个数

                                
                               var rowNumber = Math.ceil(paramsNameNumber / provideNumber);// 换行的话，需要显示几行，向上取整
                               // 条件等同于rowNumber>1
                               if (paramsNameNumber > provideNumber) {
                                 
                                  for (var p = 0; p < rowNumber; p++) {
                                      var tempStr = "";// 表示每一次截取的字符串
                                      var start = p * provideNumber;// 开始截取的位置
                                      var end = start + provideNumber;// 结束截取的位置
                                      // 此处特殊处理最后一行的索引值
                                      if (p == rowNumber - 1) {
                                         // 最后一次不换行
                                         tempStr = params.substring(start, paramsNameNumber);
                                      } else {
                                         // 每一次拼接字符串并换行
                                         tempStr = params.substring(start, end) + "\n";
                                      }
                                     newParamsName += tempStr;// 最终拼成的字符串
                                  }
                 
                               } else {
                                  // 将旧标签的值赋给新标签
                                  newParamsName = params;
                               }
                               //将最终的字符串返回
                               return newParamsName
                        }
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLabel: {
                        textStyle: {
                            color: '#01C4F7', //坐标值得具体的颜色                   
                        },
                        fontSize: fontSize,
                    }
                },
                series: [{
                    data: res.screendata1,
                    type: 'bar',
                    barMaxWidth:40,
                    label: {
                        show: true,
                        position: 'top',
                        formatter:function(params){
                            return params.data.result+"%，"+params.data.value+"人";
                         },
                         fontSize:fontSize
                    },
                
                    itemStyle: {
                        opacity: 0,
                        normal:{
                            color:function(params){
                                var colorList = [
                                    '#E87C25','#01C4F7','#FCCE10',"#FA640D",'#CB98E0','#863BF2','#087998','#1F52FA'
                                ];
                                return colorList[params.dataIndex];
                            }
                        }
                    },
                }] 
            }
             //4.渲染图表
             myCharts.setOption(option);
             window.addEventListener("resize", function() {
                 myCharts.resize();
             });
            }
         }else{
            $(".div21").css("display","none");
         }
        //   else {
        //      var option1 = getOptionDay_xbnew([], [], [], "bug", myChart,istry);
        //      myCharts.setOption(option1, true);
        //  }
         
         if (res.screendata2) {
            $('#chart23title').html(res.screentitle2);
             let myCharts = echarts.init(document.getElementById('chart23'));
             if(res.screendata2[0].content!="" && res.screendata2[0].content!=undefined){
                
                if (res.screendata2) {
                    var str="";
                    for(var i=0;i<res.screendata2.length;i++){
                        str+='<div class="jq33-content"> <div class="left"><span>'+(i+1)+"、"+res.screendata2[i].content+'</span></div></div>';
                       
                    }
                    document.getElementById('chart23').innerHTML=str;
                }
             }else{
             let myCharts = echarts.init(document.getElementById('chart23'))
             let option = {
                 grid: {
                     show: true,                                 //是否显示图表背景网格    
                     left: 5,                                    //图表距离容器左侧多少距离
                     right: 5,                                //图表距离容器右侧侧多少距离
                     bottom: isphone==1 ? 80 : 20,                              //图表距离容器上面多少距离
                     top: isphone==1? 40 :30,  
                     containLabel: true,                     //防止标签溢出  
                    
                 },
                 xAxis: {
                     type: 'category',
                     data:res.screendata2.map(item => item.name),
                     axisLabel: {
                         textStyle: {
                             color: '#01C4F7', //坐标值得具体的颜色                   
                         },
                         fontSize: fontSize,
                         formatter:function(params){
                            var newParamsName = "";// 最终拼接成的字符串
                               var paramsNameNumber = params.length;// 实际标签的个数
                               var provideNumber =4;// 每行能显示的字的个数
                                
                               provideNumber=getChart2Num(res.screendata2.length);
                               console.log(provideNumber);
                               
                               var rowNumber = Math.ceil(paramsNameNumber / provideNumber);// 换行的话，需要显示几行，向上取整

                               
                               // 条件等同于rowNumber>1
                               if (paramsNameNumber > provideNumber) {
                                 
                                  for (var p = 0; p < rowNumber; p++) {
                                      var tempStr = "";// 表示每一次截取的字符串
                                      var start = p * provideNumber;// 开始截取的位置
                                      var end = start + provideNumber;// 结束截取的位置
                                      // 此处特殊处理最后一行的索引值
                                      if (p == rowNumber - 1) {
                                         // 最后一次不换行
                                         tempStr = params.substring(start, paramsNameNumber);
                                      } else {
                                         // 每一次拼接字符串并换行
                                         tempStr = params.substring(start, end) + "\n";
                                      }
                                     newParamsName += tempStr;// 最终拼成的字符串
                                  }
                 
                               } else {
                                  // 将旧标签的值赋给新标签
                                  newParamsName = params;
                               }
                               //将最终的字符串返回
                               return newParamsName
                        }
                     }
                 },
                 yAxis: {
                     type: 'value',
                     axisLabel: {
                         textStyle: {
                             color: '#01C4F7', //坐标值得具体的颜色                   
                         },
                         fontSize: fontSize,
                     }
                 },
                 series: [{
                     data: res.screendata2,
                     type: 'bar',
                     barMaxWidth:40,
                     label: {
                         show: true,
                         position: 'top',
                         formatter:function(params){
                            return params.data.result+"%，"+params.data.value+"人";
                         },
                         fontSize:fontSize
                     },
                     itemStyle: {
                         opacity: 0,
                         normal:{
                             color:function(params){
                                 var colorList = [
                                     '#E87C25','#01C4F7','#FCCE10',"#FA640D",'#CB98E0','#863BF2','#087998','#1F52FA'
                                 ];
                                 return colorList[params.dataIndex];
                             }
                         }
                     },
                 }] 
             }
             myCharts.setOption(option);

             window.addEventListener("resize", function() {
                 myCharts.resize();
             });
            }
         }else{
            $(".div23").css("display","none");
         }
       


         if (res.screendata3) {
            $('#chart22title').html(res.screentitle3);
             let myCharts = echarts.init(document.getElementById('chart22'));
             if(res.screendata3[0].content!="" && res.screendata3[0].content!=undefined){
                
                if (res.screendata3) {
                    var str="";
                    for(var i=0;i<res.screendata3.length;i++){
                        str+='<div class="jq33-content"> <div class="left"><span>'+(i+1)+"、"+res.screendata3[i].content+'</span></div></div>';
                       
                    }
                    document.getElementById('chart22').innerHTML=str;
                }
             }else{
             let myCharts = echarts.init(document.getElementById('chart22'))
             let option = {
                 grid: {
                     show: true,                                 //是否显示图表背景网格    
                     left: 5,                                    //图表距离容器左侧多少距离
                     right: 5,                                //图表距离容器右侧侧多少距离
                     bottom: isphone==1 ? 80 : 20,                              //图表距离容器上面多少距离
                     top: isphone==1? 40 :30,                         //图表距离容器下面多少距离
                     containLabel: true,                     //防止标签溢出  
                    
                 },
                 xAxis: {
                     type: 'category',
                     data:res.screendata3.map(item => item.name),
                     axisLabel: {
                         textStyle: {
                             color: '#01C4F7', //坐标值得具体的颜色                   
                         },
                         fontSize: fontSize,
                         formatter:function(params){
                            var newParamsName = "";// 最终拼接成的字符串
                               var paramsNameNumber = params.length;// 实际标签的个数
                               var provideNumber = 8;// 每行能显示的字的个数
                               
                               provideNumber=getChart2Num(res.screendata3.length);
                               
                               var rowNumber = Math.ceil(paramsNameNumber / provideNumber);// 换行的话，需要显示几行，向上取整
                               // 条件等同于rowNumber>1
                               if (paramsNameNumber > provideNumber) {
                                 
                                  for (var p = 0; p < rowNumber; p++) {
                                      var tempStr = "";// 表示每一次截取的字符串
                                      var start = p * provideNumber;// 开始截取的位置
                                      var end = start + provideNumber;// 结束截取的位置
                                      // 此处特殊处理最后一行的索引值
                                      if (p == rowNumber - 1) {
                                         // 最后一次不换行
                                         tempStr = params.substring(start, paramsNameNumber);
                                      } else {
                                         // 每一次拼接字符串并换行
                                         tempStr = params.substring(start, end) + "\n";
                                      }
                                     newParamsName += tempStr;// 最终拼成的字符串
                                  }
                 
                               } else {
                                  // 将旧标签的值赋给新标签
                                  newParamsName = params;
                               }
                               //将最终的字符串返回
                               return newParamsName
                        }
                     }
                 },
                 yAxis: {
                     type: 'value',
                     axisLabel: {
                         textStyle: {
                             color: '#01C4F7', //坐标值得具体的颜色                   
                         },
                         fontSize: fontSize
                     }
                 },
                 series: [{
                     data: res.screendata3,
                     type: 'bar',
                     barMaxWidth:40,
                     label: {
                         show: true,
                         position: 'top',
                         formatter:function(params){
                            return params.data.result+"%，"+params.data.value+"人";
                         },
                         fontSize:fontSize
                     },
                     itemStyle: {
                         opacity: 0,
                         normal:{
                             color:function(params){
                                 var colorList = [
                                    '#E87C25','#01C4F7','#FCCE10',"#FA640D",'#CB98E0','#863BF2','#087998','#1F52FA'
                                 ];
                                 return colorList[params.dataIndex];
                             }
                         }
                     },
                 }] 
             }
             myCharts.setOption(option);
             window.addEventListener("resize", function() {
                 myCharts.resize();
             });
            }
         }else{
            $(".div22").css("display","none");
         }

         if (res.screendata4) {
            $('#chart24title').html(res.screentitle4);
             let myCharts = echarts.init(document.getElementById('chart24'));
             if(res.screendata4[0].content!=""){
                
                if (res.screendata4) {
                    var str="";
                    for(var i=0;i<res.screendata4.length;i++){
                        str+='<div class="jq33-content"> <div class="left"><span>'+(i+1)+"、"+res.screendata4[i].content+'</span></div></div>';
                       
                    }
                    document.getElementById('chart24').innerHTML=str;
                }
             }else{

             
             let option = {
                 grid: {
                     show: true,                                 //是否显示图表背景网格    
                     left: 5,                                    //图表距离容器左侧多少距离
                     right: 5,                                //图表距离容器右侧侧多少距离
                     bottom: isphone==1 ? 100 : 8,                              //图表距离容器上面多少距离
                     top: isphone==1 ? 80 : 40,                                        //图表距离容器下面多少距离
                     containLabel: true,                     //防止标签溢出  
                    
                 },
                 color: [
                     '#60C0DD','#D7504B','#C6E579','#F4E001','#26C0C0','#FCCE10',"#FA640D",'#0DD6F5','#863BF2'
                 ],
                 xAxis: {
                     type: 'category',
                     data:res.screendata4.map(item => item.name),
                     axisLabel: {
                        textStyle: {
                            color: '#01C4F7', //坐标值得具体的颜色                   
                        },
                        fontSize: fontSize,
                        formatter:function(params){
                            var newParamsName = "";// 最终拼接成的字符串
                               var paramsNameNumber = params.length;// 实际标签的个数
                               var provideNumber = 8;// 每行能显示的字的个数
                               
                               var rowNumber = Math.ceil(paramsNameNumber / provideNumber);// 换行的话，需要显示几行，向上取整
                               // 条件等同于rowNumber>1
                               if (paramsNameNumber > provideNumber) {
                                 
                                  for (var p = 0; p < rowNumber; p++) {
                                      var tempStr = "";// 表示每一次截取的字符串
                                      var start = p * provideNumber;// 开始截取的位置
                                      var end = start + provideNumber;// 结束截取的位置
                                      // 此处特殊处理最后一行的索引值
                                      if (p == rowNumber - 1) {
                                         // 最后一次不换行
                                         tempStr = params.substring(start, paramsNameNumber);
                                      } else {
                                         // 每一次拼接字符串并换行
                                         tempStr = params.substring(start, end) + "\n";
                                      }
                                     newParamsName += tempStr;// 最终拼成的字符串
                                  }
                 
                               } else {
                                  // 将旧标签的值赋给新标签
                                  newParamsName = params;
                               }
                               //将最终的字符串返回
                               return newParamsName
                        }
                    }
                 },
                 yAxis: {
                     type: 'value',
                     axisLabel: {
                        textStyle: {
                            color: '#01C4F7', //坐标值得具体的颜色                   
                        },
                        fontSize: fontSize,
                    }
                 },
                 series: [{
                     data: res.screendata4,
                     type: 'bar',
                     barMaxWidth:40,
                     label: {
                             show: true,
                             position: 'top',
                             formatter:function(params){
                                return params.data.result+"%\n"+params.data.value+"人";
                             },
                             fontSize:fontSize
                     },
                     itemStyle: {
                         opacity: 0,
                         normal:{
                             color:function(params){
                                 var colorList = [
                                    '#E87C25','#01C4F7','#FCCE10',"#FA640D",'#CB98E0','#863BF2','#087998','#1F52FA'
                                 ];
                                 return colorList[params.dataIndex];
                             }
                         }
                     },
                 }] 
             }
             myCharts.setOption(option);
             window.addEventListener("resize", function() {
                 myCharts.resize();
             }); 
            }
         }else{
            $(".div24").css("display","none");
         }
        



     })
 }

 function hasComma(str) {
    return str.includes(",") || str.includes("，");
  }

Date.prototype.Format = function (fmt) { // author: meizz
var o = {
 "M+": this.getMonth() + 1, // 月份
 "d+": this.getDate(), // 日
 "h+": this.getHours(), // 小时
 "m+": this.getMinutes(), // 分
 "s+": this.getSeconds(), // 秒
 "q+": Math.floor((this.getMonth() + 3) / 3), // 季度
 "S": this.getMilliseconds() // 毫秒
};
if (/(y+)/.test(fmt))
 fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
for (var k in o)
 if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
   return fmt;
}
/*setInterval(function(){
                 // initChart1();
                 // initChart2();
                 // initChart3();
                 resetchart();
                 var time1=new Date().Format("yyyy-MM-dd hh:mm:ss");
                console.log("定时更新"+time1 );
             },1000*60*5)//五分钟定时执行一次
*/

// var liDoms = $(".nav ul li");


 // setInterval(function(){
 //  //console.log(liDoms.length);
 //      var activeIndex1=activeIndex;
 //      activeIndex1++;
 //      if(activeIndex1>=liDoms.length)
 //      {
 //         activeIndex1=0
 //      }
 //      if(!pepoactive)
 //      {
 //         clickLiByIndex(activeIndex1);
 //      }
 //      else
 //      {
 //         if(xunhuannum>2)
 //         {
 //             xunhuannum=0;
 //             pepoactive=false;
 //             clickLiByIndex(activeIndex1);
 //         }
 //         else
 //         {
 //             xunhuannum++; 
 //         }  
 //      }
 //      //var time1=new Date().Format("yyyy-MM-dd hh:mm:ss");
 //      //console.log("定时更新"+time1+"当前"+activeIndex1 );
 //      //console.log(activeIndex);
      
     
 // },1000*30)//两分钟定时执行一次

     function randomNum(minNum, maxNum) {
         switch (arguments.length) {
             case 1:
                 return parseInt(Math.random() * minNum + 1, 10);
                 break;
             case 2:
                 return parseInt(Math.random() * (maxNum - minNum + 1) + minNum, 10);
                 break;
             default:
                 return 0;
                 break;
         }
     }
 

 
 
 $(document).keypress(function (event) {
     var code = (event.keyCode ? event.keyCode : event.which); 
   
     if ((code == 27)||(code == 13)){
         jsobj.closeDialogWindow(articleid);
     }
 });

 
 
 
 
});