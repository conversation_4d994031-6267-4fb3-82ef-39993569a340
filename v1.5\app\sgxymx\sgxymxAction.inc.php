<?php
class sgxymxAction extends AbstractAction
{
	public function __construct()
	{
    	parent::__construct();
	}

	public function checkSession()
	{
	}


	public function random($length)
    {
        $hash = 'CR-';
        $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789abcdefghijklmnopqrstuvwxyz';
        $max = strlen($chars) - 1;
        mt_srand(( double ) microtime() * 1000000);
        for ($i = 0; $i < $length; $i ++) {
            $hash .= $chars [mt_rand(0, $max)];
        }
        return $hash;
	}
	
	function index($params)
	{
		$mode=$_GET['mode'];
		$datadate=$params["datadate"];
		$dataym=date("Y-m",strtotime($datadate));
		if($datadate==''){
			$datadate = date('Y-m-d',time());
			$dataym = date('Y-m',time());  
		}
		$jiliangtype=array("1"=>"检尺","2"=>"检斤");
		$yunshutype=array("1"=>"汽运","2"=>"火车");
		$type=$params["type"];
		if($type==''){
			$type="螺纹钢";
		}
		

		$longgang=array(
			'西安'=>'西咸片区',
			'韩城'=>'环韩城片区',
			'延安'=>'延安片区',
			'榆林'=>'榆林片区',
			'安康'=>'安康片区',
			'宝鸡'=>'宝平片区',
			'铜川'=>'铜川片区',
			'商洛'=>'商洛片区',
			'兰州'=>'甘青藏片区',
			'庆阳'=>'庆阳片区',
			'天水'=>'天水片区',
			'银川'=>'榆林片区',
			'运城'=>'环韩城片区',
			'广元'=>'广元片区',
			'汉中'=>'环汉中片区',
			'重庆'=>'重庆片区',
			'成都'=>'成都片区',
			'绵阳'=>'绵阳片区',
			'南充'=>'南充片区',
			'郑州'=>'郑州片区',
			'贵阳'=>'云贵片区',
			'昆明'=>'云贵片区',
			'武汉'=>'武汉片区'
		);
		

		$lgcity=array();
		foreach($longgang as $key=>$value){
			$lgcity[]=$key;
		}

		$lgsql1="select dta_5,dta_6 from  sg_data_table where dta_type ='SGtjtz'  and dta_1='".$type."' and  dta_ym='".$datadate."'  and dta_5 in  ('".implode("','",$lgcity)."')  and dta_4 in  ('".implode("','",$longgang)."')  group by dta_5  ";
		
		$lgXS=$this->_dao->AQUERY($lgsql1);
		 
		//$lgsql2="select dta_2 from  sg_data_table where dta_type ='TEST_ERP_ZHYF'  and dta_1='龙钢' and  dta_ym='".$dataym."'  and dta_2 in  ('".implode("','",$lgcity)."')    group by dta_2  ";
		
		$lgsql2="select dta_2 from  sg_data_table where dta_type ='TEST_ERP_ZHYF'  and dta_1='龙钢' and  dta_ym='".$datadate."'  ";
		$lgYF=$this->_dao->getOne($lgsql2);

		$pq_list=$this->_dao->query("select sg_PianQuCity.*,sg_PianQu.pianqu from sg_PianQuCity,sg_PianQu where  sg_PianQuCity.pqid=sg_PianQu.pqid");
		
		$lgType=array();
		$hgType=array();
		foreach($pq_list as $k=>$val){
			if($val['type']=='1'){
				$lgType[$val['cityname']]['jiliangtype']=$val['jiliangtype'];
				$lgType[$val['cityname']]['yunshutype']=$val['yunshutype'];
				$lgType[$val['cityname']]['jlfs']=$jiliangtype[$val['jiliangtype']];
				$lgType[$val['cityname']]['ysfs']=$yunshutype[$val['yunshutype']];
			}else if($val['type']=='2'){
				$hgType[$val['cityname']]['jiliangtype']=$val['jiliangtype'];
				$hgType[$val['cityname']]['yunshutype']=$val['yunshutype'];
				$hgType[$val['cityname']]['jlfs']=$jiliangtype[$val['jiliangtype']];
				$hgType[$val['cityname']]['ysfs']=$yunshutype[$val['yunshutype']];
			}
		}
		
		$lgArr=array();
		foreach($longgang as $lk=>$lv){
			$lgArr[$lk]['A']=$lgXS[$lk];
			$lgArr[$lk]['ysfs']=$lgType[$lk]['ysfs'];
			$lgArr[$lk]['jlfs']=$lgType[$lk]['jlfs'];
			$lgArr[$lk]['B']=$lgYF;
			
			if($lgType[$lk]['jiliangtype']=='2'){
				$lgArr[$lk]['C']=$lgArr[$lk]['A']-$lgArr[$lk]['A']*(1-3.5/100);
			}else if($lgType[$lk]['jiliangtype']=='1'){
				$lgArr[$lk]['C']=0;
			}
			//if($lgArr[$lk]['A']!=''){
				$lgArr[$lk]['ccjg']=$lgArr[$lk]['A']-$lgArr[$lk]['B']-$lgArr[$lk]['C'];
			//}
			
		}
		
		$lgarray=$this->arraySort($lgArr,'ccjg','desc');

		

		foreach($lgArr as $k=>$v){
			$lgArr[$k]['sort']=$lgarray[$k];
		}

		
		
		$hangang=array(
			'西安'=>'西咸片区',
			'宝鸡'=>'宝平片区',
			'安康'=>'安康片区',
			'榆林'=>'榆林片区',
			'延安'=>'延安片区',
			'铜川'=>'铜川片区',
			'商洛'=>'商洛片区',
			'兰州'=>'甘青藏片区',
			'庆阳'=>'庆阳片区',
			'天水'=>'天水片区',
			'汉中'=>'环汉中片区',
			'广元'=>'广元片区',
			'重庆'=>'重庆片区',
			'成都'=>'成都片区',
			'绵阳'=>'绵阳片区',
			'南充'=>'南充片区',
			'郑州'=>'郑州片区',
			'贵阳'=>'云贵片区',
			'昆明'=>'云贵片区',
			'武汉'=>'武汉片区'
		);

		$hgcity=array();
		foreach($hangang as $key=>$value){
			$hgcity[]=$key;
		}

		$hgsql1="select dta_5,dta_6 from  sg_data_table where dta_type ='SGtjtz'  and dta_1='".$type."' and  dta_ym='".$datadate."'  and dta_5 in  ('".implode("','",$hgcity)."')  and dta_4 in  ('".implode("','",$hangang)."')  group by dta_5  ";
		$hgXS=$this->_dao->AQUERY($hgsql1);
		 
		//$hgsql2="select dta_2,dta_3 from  sg_data_table where dta_type ='TEST_ERP_ZHYF'  and dta_1='汉钢' and  dta_ym='".$dataym."'  and dta_2 in  ('".implode("','",$hgcity)."')    group by dta_2  ";
		$hgsql2="select dta_2 from  sg_data_table where dta_type ='TEST_ERP_ZHYF'  and dta_1='汉钢' and  dta_ym='".$datadate."'  ";
		$hgYF=$this->_dao->getOne($hgsql2);
		
		
		$hgArr=array();
		foreach($hangang as $lk=>$lv){
			$hgArr[$lk]['A']=$hgXS[$lk];
			$hgArr[$lk]['ysfs']=$hgType[$lk]['ysfs'];
			$hgArr[$lk]['jlfs']=$hgType[$lk]['jlfs'];
			$hgArr[$lk]['B']=$hgYF;
			
			if($hgType[$lk]['jiliangtype']=='2'){
				$hgArr[$lk]['C']=$hgArr[$lk]['A']-$hgArr[$lk]['A']*(1-3.5/100);
			}else if($hgType[$lk]['jiliangtype']=='1'){
				$hgArr[$lk]['C']=0;
			}
			
				$hgArr[$lk]['ccjg']=$hgArr[$lk]['A']-$hgArr[$lk]['B']-$hgArr[$lk]['C'];
			
		}
		$hgarray=$this->arraySort($hgArr,'ccjg','desc');
		
		foreach($hgArr as $k=>$v){
			$hgArr[$k]['sort']=$hgarray[$k];
		}
		
	
		$this->assign("type",$type);
		$this->assign("lgArr",$lgArr);
		$this->assign("hgArr",$hgArr);
		$this->assign("mode",$mode);
		$this->assign("datadate",$datadate);
	}
	function gdbj($params)
	{
		
		$mode=$_GET['mode'];
		$searchType=$params["searchType"];
		if($searchType==''){
			$searchType="日查询";
		}

		//$sdate='2020-08-01';
		//$edate='2020-10-28';
		$sdate=$params["sdate"];
		$edate=$params["edate"];
		if($sdate==''){
			$sdate = date('Y-m-01',time());  
			$edate = date('Y-m-d',time());  
		}

		
		$nym = date('Y-m',time()); 
		$sym=date("Y-m",strtotime($sdate));
		$eym=date("Y-m",strtotime($edate));

		$sym1=date("Y-n",strtotime($sdate));
		$eym1=date("Y-n",strtotime($edate));


		$eym2 = date("Y-m",strtotime($edate."-1 month"));
		
		$longgang=array(
			"1"=>array("带钢","带钢","螺纹钢φ18-22（西安区域）","西安"),
			"2"=>array("带钢","带钢","螺纹钢φ18-22（成都区域）","成都")
		);
		

		$hangang=array(
			"1"=>array("硬线","硬线","硬线"),
			"2"=>array("B钢","盘卷","B钢"),
			"3"=>array("焊丝","焊丝","焊丝"),
			"4"=>array("PC钢棒","PC钢棒","PC钢棒"),
			"5"=>array("拉丝材","拉丝材","拉丝材"),
			"6"=>array("普碳圆钢","圆钢","普碳圆钢"),
			"7"=>array("锚杆","锚杆钢","锚杆钢"),
			"8"=>array("高强度螺纹钢","螺纹钢","高强度螺纹钢")
		);

		$where="and dta_1='螺纹钢'  and dta_7='龙钢' ";//01西安 11成都
		//龙钢 螺纹钢φ18-22（西安区域）
		$lgxa=$this->_dao->get_sg_data_table('TEST_ERP_XSSJ',$sdate,$edate,$where." and dta_4='01'" );
		$A1=round($lgxa['zje']/$lgxa['kdl'],2);
		$A[1]=$A1;

		
		//龙钢 螺纹钢φ18-22（成都区域）
		$lgcd=$this->_dao->get_sg_data_table('TEST_ERP_XSSJ',$sdate,$edate,$where." and dta_4='17'" );
		$A2=round($lgcd['zje']/$lgcd['kdl'],2);
		$A[2]=$A2;
		

		//汉钢 螺纹钢φ18-22（成都区域）
		$hgcd=$this->_dao->get_sg_data_table('TEST_ERP_XSSJ',$sdate,$edate," and dta_1='螺纹钢'  and dta_7='汉钢' and dta_4='17'" );
		$A3=round($hgcd['zje']/$hgcd['kdl'],2);
		$A[3]=$A3;

	
        //龙钢西安运费A1
		$lgyfxa=$this->_dao->get_sg_data_table_yf('TEST_ERP_YF',$sym,$eym," and dta_1='龙钢'  and dta_2='01' " );
		$C1=round($lgyfxa['yf']);
		$C[1]=$C1;

		//龙钢成都运费A2
		$lgyfcd=$this->_dao->get_sg_data_table_yf('TEST_ERP_YF',$sym,$eym," and dta_1='龙钢'  and dta_2='17' " );
		$C2=round($lgyfcd['yf']);
		$C[2]=$C2;
		

		//汉钢成都运费A3
		$hgyfcd=$this->_dao->get_sg_data_table_yf('TEST_ERP_YF',$sym,$eym," and dta_1='汉钢'  and dta_2='17' " );
		$C3=round($hgyfcd['yf']);
		$C[3]=$C3;

		
		//龙钢带钢（除高线、盘螺和螺纹钢之外的钢材）综合运费 D1
		$lgyf=$this->_dao->get_sg_data_table_zhyf('TEST_ERP_YF2',$sdate,$edate," and dta_1='龙钢' and dta_3!='螺纹钢' and dta_3!='盘螺' and dta_3!='高线' ");
		$D1=round($lgyf['yf']);
		

		//汉钢除高线、盘螺和螺纹钢之外的综合运费 D2
		$hgyf=$this->_dao->get_sg_data_table_zhyf('TEST_ERP_YF2',$sdate,$edate," and dta_1='汉钢' and dta_3!='螺纹钢' and dta_3!='盘螺' and dta_3!='高线' ");
		$D2=round($hgyf['yf']);
		
		//B
		$lg_b=array();
		$hg_b=array();

		if($searchType=="日查询"){
			foreach($longgang as $lk=>$lv){
				
				$lgb=$this->_dao->getR("SGHCGCRXS",$sdate,$edate," and dta_2='".$lv[1]."'  and dta_3='龙钢'  ");
				$lg_b[$lk]['B']=round($lgb,2);
			}

			foreach($hangang as $hk=>$hv){
				$hgb=$this->_dao->getR("SGHCGCRXS",$sdate,$edate," and dta_2='".$hv[2]."'  and dta_3='汉钢'  ");
				$hg_b[$hk]['B']=round($hgb,2);
			}

		}else if($searchType=="月查询"){
			
			if($sym==$eym && $sym==$nym){

				$lgarr=$this->_dao->AQUERY("select dta_2,dta_9 from sg_data_table where dta_type='SGHCGCRXS'  and dta_ym='".$edate."' and dta_2!='' and dta_3='龙钢' order by id desc ");
				foreach($longgang as $lk=>$lv){
					$lg_b[$lk]['B']=$lgarr[$lv[1]];
				}
				$hgarr=$this->_dao->AQUERY("select dta_2,dta_9 from sg_data_table where dta_type='SGHCGCRXS'  and dta_ym='".$edate."' and dta_2!='' and dta_3='汉钢' order by id desc ");
				foreach($hangang as $hk=>$hv){
					$hg_b[$hk]['B']=$hgarr[$hv[2]];
				}

			}else if($sym==$eym && $sym!=$nym){
				foreach($longgang as $lk=>$lv){
					$lg_b[$lk]['B']=$this->_dao->getB("SGYJYJSYB","SGLG",$eym," and dta_1='".$lv[1]."'  ");
				}
				foreach($hangang as $hk=>$hv){
					$hg_b[$hk]['B']=$this->_dao->getB("SGYJYJSYB","SGHG",$eym," and dta_1='".$hv[1]." 小计'  ");
				}

				
			}else if($sym!=$eym && $eym!=$nym){
				foreach($longgang as $lk=>$lv){
					$lgsum=$this->_dao->getA("SGYJYJSYB","SGLG",$sym,$eym," and dta_1='".$lv[1]."'  ");
					$lg_b[$lk]['B']=round($lgsum['je']/$lgsum['sl'],2);
				}
				foreach($hangang as $hk=>$hv){
					$hgsum=$this->_dao->getA("SGYJYJSYB","SGHG",$sym,$eym," and dta_1='".$hv[1]." 小计'  ");
					$hg_b[$hk]['B']=round($hgsum['je']/$hgsum['sl'],2);
				}
			}else if($sym!=$eym && $eym==$nym){
			
				foreach($longgang as $lk=>$lv){
					$lgy=$this->_dao->getA("SGYJYJSYB","SGLG",$sym,$eym2," and dta_1='".$lv[1]."'  ");
					$je=$lgy['je'];
					$sl=$lgy['sl'];
					$lgr=$this->_dao->getC("SGHCGCRXS",$edate," and dta_2='".$lv[1]."' and dta_3='龙钢'  ");
					$I=$lgr['xl'];
					$J=$lgr['jg'];
					$lg_b[$lk]['B']=round(($je+$I*$J)/($sl+$I),2);
				}
				foreach($hangang as $hk=>$hv){
					$hgy=$this->_dao->getA("SGYJYJSYB","SGHG",$sym,$eym2," and dta_1='".$hv[1]." 小计'  ");
					$je=$hgy['je'];
					$sl=$hgy['sl'];
					$hgr=$this->_dao->getC("SGHCGCRXS",$edate," and dta_2='".$hv[2]."' and dta_3='汉钢'  ");
					$I=$hgr['xl'];
					$J=$hgr['jg'];
					$hg_b[$hk]['B']=round(($je+$I*$J)/($sl+$I),2);
				}
			}
		}
		//echo '<pre>';
		//print_r($lg_b);
		$lgArr=array();
		$hgArr=array();
		foreach($longgang as $lgk=>$lgv){
			$lgArr[$lgk]['qy']=$lgv[2];
			$lgArr[$lgk]['A']=$A[$lgk];
			$lgArr[$lgk]['cp']=$lgv[0];
			$lgArr[$lgk]['B']=$lg_b[$lgk]['B'];
			$lgArr[$lgk]['C']=$C[$lgk];
			$lgArr[$lgk]['D']=$D1;
			$jlfs=$this->_dao->getOne("select jiliangtype from sg_PianQuCity where type=1 and cityname='$lgv[3]'");
			if($jlfs==1){
				$X=round($lgArr[$lgk]['A']/(1-3.5/100)-$lgArr[$lgk]['A'],2);
				$lgArr[$lgk]['Y']=$lgArr[$lgk]['A']-$lgArr[$lgk]['C']+$X;
			}else if($jlfs==2){
				$lgArr[$lgk]['Y']=$lgArr[$lgk]['A']-$lgArr[$lgk]['C'];
			}
			$lgArr[$lgk]['Z']=$lgArr[$lgk]['B']-$lgArr[$lgk]['D'];
			$lgArr[$lgk]['cy']=$lgArr[$lgk]['Y']-$lgArr[$lgk]['Z'];
		}

		foreach($hangang as $hgk=>$hgv){
			$hgArr[$hgk]['A']=$A3;
			$hgArr[$hgk]['cp']=$hgv[0];
			$hgArr[$hgk]['B']=$hg_b[$hgk]['B'];
			
			$hgArr[$hgk]['C']=$C3;
			$hgArr[$hgk]['D']=$D2;
			$jlfs=$this->_dao->getOne("select jiliangtype from sg_PianQuCity where type=2 and cityname='成都'");
			$X=round($A3/(1-3.5/100)-$A3,2);
			
			if($jlfs==1){
				$hgArr[$hgk]['Y']=$A3-$C3+$X;
			}else if($jlfs==2){
				$hgArr[$hgk]['Y']=$A3-$C3;
			}
			$hgArr[$hgk]['Z']=$hgArr[$hgk]['B']-$D2;
			$hgArr[$hgk]['cy']=$hgArr[$hgk]['Y']-$hgArr[$hgk]['Z'];
		}
	
		$this->assign("lgArr",$lgArr);
		$this->assign("hgArr",$hgArr);
		$this->assign("sdate",$sdate);
		$this->assign("edate",$edate);
		$this->assign("params",$params);
	}


	function xspq($params)
	{
		
		$mode=$_GET['mode'];
		$sdate=$params["sdate"];
		$edate=$params["edate"];
         
		if($sdate=='')
		{
			$sdate = date('Y-m-01',time());  
			$edate = date('Y-m-d',time());  
			
		}
		$type=$params["type"];
		if($type=='')
		{
			$type = '螺纹钢';  
			
		}
		//$sdate='2020-10-14';
		//$edate='2020-10-14';

		$lg_pq_array=array(
			'08'=>array('安康片区','1'),
			'21'=>array('天水片区','1'),
			'01'=>array('西咸片区','1'),
			'04'=>array('工程事业部','1'),
			'22'=>array('宝鸡片区','1'),
			'06'=>array('延安片区','1'),
			'20'=>array('甘青藏片区','1'),
			'03'=>array('铜川片区','1'),
			'09'=>array('庆阳片区','1'),
			'02'=>array('商洛片区','1'),
			'07'=>array('环韩城片区','1'),
			'11'=>array('郑州片区','1'),
			'05'=>array('榆林片区','1'),
			'13'=>array('环汉中片区','2'),
			'15'=>array('绵阳片区','2'),
			'17'=>array('成都片区','2'),
			'18'=>array('重庆片区','2'),
			'12'=>array('武汉片区','1'),
			'23'=>array('云贵片区','2')
		); 
		 foreach($lg_pq_array as $key=>$value)
		 {
			$lgpqarr[]=$key;
		 }
		 $lgpqstr=implode("','",$lgpqarr);
		$sql=" select dta_4,sum(dta_5) xlsj,sum(dta_6) xsje from  sg_data_table where dta_type =  'TEST_ERP_XSSJ' and  dta_7='龙钢' and dta_1='".$type."' and  dta_ym>='".$sdate."' and dta_ym<='".$edate."' and dta_4 in ('".$lgpqstr."') group by dta_4  ";
		$lgpqxsl=$this->_dao->query($sql);
		$lg_zxl=0;
		  foreach($lgpqxsl as $key=>$value)
		  {
			$lg_zxl+=$value['xlsj'];
			$lg_pq_array[$value['dta_4']]['xlsj']=$value;
			$lg_pq_array[$value['dta_4']]['jsjg']=Round($value['xsje']/$value['xlsj']);
		  }
		  foreach($lg_pq_array as $key=>$value)
		  {
			$lg_pq_array[$key]['xszb']=Round(($value['xlsj']['xlsj']/$lg_zxl*100),2);//销量占比
		  }
		  $new_array=$this->arraySort($lg_pq_array,'xszb','desc');
		 

		$sql=" select dta_2,avg(dta_3) pjyf from  sg_data_table where dta_type =  'TEST_ERP_YF' and  dta_1='龙钢'  and dta_ym>='".date('Y-m',strtotime($sdate))."' and dta_ym<='".date('Y-m',strtotime($edate))."' and dta_2 in ('".$lgpqstr."') group by dta_2  ";
		$lgpqyf=$this->_dao->query($sql);
		foreach($lgpqyf as $key=>$value)
		{
			$lg_pq_array[$value['dta_2']]['pjyf']=$value['pjyf'];
		}
		foreach($lg_pq_array as $key=>$value)
		{
		 $lg_pq_array[$key]['xszbpx']=$new_array[$key];
		 $lg_pq_array[$key]['dtccj']= $lg_pq_array[$key]['jsjg']- $lg_pq_array[$key]['pjyf'];
		 $lg_pq_array[$key]['dtjcccj']= $lg_pq_array[$key]['1']==1?$lg_pq_array[$key]['dtccj']:Round($lg_pq_array[$key]['dtccj']*(1-3.5/100));
		}
         
		  $this->assign("lg_pq_array",$lg_pq_array);

		 //print_r($lg_pq_array);

		 $hg_pq_array=array(
			'04'=>array('工程事业部','1'),
			'22'=>array('宝鸡片区','1'),
			'19'=>array('陇南片区','1'),
			'01'=>array('西咸片区','1'),
			'08'=>array('安康片区','1'),
			'20'=>array('甘青藏片区','1'),
			'14'=>array('广元片区','2'),
			'23'=>array('云贵片区','2'),
			'13'=>array('环汉中片区','2'),
			'17'=>array('成都片区','2'),
			'15'=>array('绵阳片区','2'),
			'16'=>array('南充片区','2'),
			'12'=>array('武汉片区','1'),
			'18'=>array('重庆片区','2')
			
		); 
       // echo $sql;
	   foreach($hg_pq_array as $key=>$value)
	   {
		  $hgpqarr[]=$key;
	   }
	   $hgpqstr=implode("','",$hgpqarr);
	  $sql=" select dta_4,sum(dta_5) xlsj,sum(dta_6) xsje from  sg_data_table where dta_type =  'TEST_ERP_XSSJ' and  dta_7='龙钢' and dta_1='".$type."' and  dta_ym>='".$sdate."' and dta_ym<='".$edate."' and dta_4 in ('".$hgpqstr."') group by dta_4  ";
	  $hgpqxsl=$this->_dao->query($sql);
	  $hg_zxl=0;
	  foreach($hgpqxsl as $key=>$value)
	  {
		$hg_zxl+=$value['xlsj'];
		$hg_pq_array[$value['dta_4']]['xlsj']=$value;
		$hg_pq_array[$value['dta_4']]['jsjg']=Round($value['xsje']/$value['xlsj']);
	  }
	  foreach($hg_pq_array as $key=>$value)
		{
		$hg_pq_array[$key]['xszb']=Round(($value['xlsj']['xlsj']/$hg_zxl*100),2);//销量占比
		}
//echo "<br/>";
	   $new_array=$this->arraySort($hg_pq_array,'xszb','desc');
	   

	   $sql=" select dta_2,avg(dta_3) pjyf from  sg_data_table where dta_type =  'TEST_ERP_YF' and  dta_1='汉钢'  and  dta_ym>='".date('Y-m',strtotime($sdate))."' and dta_ym<='".date('Y-m',strtotime($edate))."' and dta_2 in ('".$hgpqstr."') group by dta_2  ";
		$hgpqyf=$this->_dao->query($sql);
		foreach($hgpqyf as $key=>$value)
		{
			$hg_pq_array[$value['dta_2']]['pjyf']=$value['pjyf'];
		}

		foreach($hg_pq_array as $key=>$value)
	   {
		  $hg_pq_array[$key]['xszbpx']=$new_array[$key];
		  $hg_pq_array[$key]['dtccj']= $hg_pq_array[$key]['jsjg']- $hg_pq_array[$key]['pjyf'];
		  $hg_pq_array[$key]['dtjcccj']= $hg_pq_array[$key]['1']==1?$hg_pq_array[$key]['dtccj']:Round($hg_pq_array[$key]['dtccj']*(1-3.5/100));
	   }

	   

	  $this->assign("hg_pq_array",$hg_pq_array);
	  $this->assign("type",$type);
	  $this->assign("sdate",$sdate);
	  $this->assign("edate",$edate);
	  
	
	}


	public function arraySort($arr, $keys, $type = 'asc')
	{
		$keysvalue = $new_array = array();
		foreach ($arr as $k => $v) {
			$keysvalue[$k] = $v[$keys];
		}
		
		
		if ($type == 'asc') {
			natsort($keysvalue);
		}
		if ($type == 'desc') {
			asort($keysvalue);
			$keysvalue = array_reverse($keysvalue, TRUE); // 将原数组中的元素顺序翻转,如果第二个参数指定为 true，则元素的键名保持不变
		}
		
		$i=1;

		foreach ($keysvalue as $k => $v) {

			//$new_array[$k] = $arr[$k];

			$new_array1[$k] = $i;
			$i++;
		}
		return $new_array1;
	}
// X=(((A)*O/100*(1+M/100)*(1-A2/100)+A3)/(1+M/100)+A1/(1+N/100)/(1-A2/100)/A4*D3*D2/100+D1
// (((E3)*6.7*1.13*0.92+25)/1.13+167/1.09)/0.92/62*55.6*0.92+160
	
	private function pri_JSON($array)
	{
		$this->pri_arrayRecursive($array, 'urlencode', true);
		$json = json_encode($array);
		return iconv("GB2312", "UTF-8", urldecode($json));
	}

	private function pri_arrayRecursive(&$array, $function, $apply_to_keys_also = false)
	{
		static $recursive_counter = 0;
		if (++$recursive_counter > 1000) {
			die('possible deep recursion attack');
		}
		foreach ($array as $key => $value) {
			if (is_array($value)) {
				$this->pri_arrayRecursive($array[$key], $function, $apply_to_keys_also);
			} else {
				$array[$key] = $function($value);
			}
			if ($apply_to_keys_also && is_string($key)) {
				$new_key = $function($key);
				if ($new_key != $key) {
					$array[$new_key] = $array[$key];
					unset($array[$key]);
				}
			}
		}
		$recursive_counter--;
	}

	//数组转码
	public function array_iconv($str, $in_charset="utf-8", $out_charset="gb2312")
	{
		if (is_array($str)) {
			foreach ($str as $k => $v) {
				$str[$k] = $this->array_iconv($v);
			}
			return $str;
		} else {
			if (is_string($str)) {
				return mb_convert_encoding($str, $out_charset, $in_charset);
			} else {
				return $str;
			}
		}
	}

	public function isHan($str)
	{
		if($str=="")
		{
			return false;
		}
		else
		{
			if (preg_match_all("/^([\x81-\xfe][\x40-\xfe])+$/", $str, $match))
			{
				//全是中文
				return true;
			}
			else
			{
				//不全是中文
				return false;
			}
		}			
	}

	//去除所有空格、换行等
	public function trimAll($str)
	{
		$oldchar=array(" ","　","\t","\n","\r");
		$newchar=array("","","","","");
		return str_replace($oldchar,$newchar,"$str");
	}
}

?>