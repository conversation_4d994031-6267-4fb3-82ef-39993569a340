<?php
class  ridingjiahuizongDao extends Dao{
	public function __construct($writer){
		 parent::__construct( $writer );
	}
	//通过guid取得用户信息
	public function get_userinfo_byguid($GUID){
		$sql = "select * from app_session_temp where  GUID ='$GUID'  order by  LoginDate DESC  limit 1";
		return $this->getRow($sql);
	}
	
	public function get_ng_price_model_info($modeltype,$date){
		
		$where = " date ='$date' ";
		
		$sql = "select HuiZong1 from steelhome_drc.ng_price_model where modeltype='$modeltype' and $where order by id desc limit 1";
		//echo "$sql";
		return $this->getRow($sql);
	}

	
}