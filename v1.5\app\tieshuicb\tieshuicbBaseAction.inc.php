<?php 
include_once( FRAME_LIB_DIR ."/action/AbstractAction.inc.php" );
class  tieshuicbBaseAction extends AbstractAction
{
    public function __construct()
    {
        parent::__construct();
    } 
	
	
 	public function index($params)
    {
		
		$GUID = $params['GUID'];
		//$GUID = "6615bc4ac53d11e7b891001aa00de1ab";
		$mode = $params['mode'];//1=客户端，2=安卓，3=iOS
		//$date = date("Y-m-d");

        if($GUID==""){
            $params['tip'] = "GUID不能为空！";       
        }
		
		//获取用户信息
		$user_infos = $this->t1dao->get_userinfo_byguid($GUID);
		$Mid=$user_infos['Mid'];//会员id
		$userid=$user_infos['Uid'];//用户id
		$TrueName=$user_infos['TrueName'];//用户id
		
		
		//获取用户权限和用户名app_license_privilege
		$license_detail_info = $this->t1dao->get_license_privilege($userid);
        //print_R($license_detail_info);
        $this->assign("cb_privilege",$license_detail_info['cb_privilege']);
        $this->assign("mode",$mode);
        $this->assign("tip",$params['tip']);
        $this->assign("pid",$params['pid']);
        $this->assign("jj",$params['jj']);

        
        $where = " and isDelete=0 ";
        if($params['date']!=""){
            $date = $params['date'];

            $where .=" and Date<='".$date."' ";
        }
        if($params['Type']!=""){
            $params['type'] = $params['Type'];
        }

        if($params['type']!=""){
            $where .=" and Type='".$params['type']."' ";
        }

        if($params['id']!=""){
            $where .=" and id='".$params['id']."' ";
        }
		

        $info = $this->maindao->get_ng_ChengBenParameter2($where);	 
        //echo "<pre>";
        //print_r($info);
        $info['TuSunA'] = $info['TuSunA']*100;
        $info['BiLiA'] = $info['BiLiA']*100;
        $info['TuSunB'] = $info['TuSunB']*100;
        $info['BiLiB'] = $info['BiLiB']*100;
        $info['HanShuiLvC'] = $info['HanShuiLvC']*100;
        $info['WaiGouBiLiC'] = $info['WaiGouBiLiC']*100;
        $info['HanShuiLvD'] = $info['HanShuiLvD']*100;
        $info['ZenZhiSuiLv'] = $info['ZenZhiSuiLv']*100;

        //print_r($info);
        
        $this->assign("info",$info);

        $total = $this->maindao->getOne( "SELECT COUNT(id) as c FROM  ng_ChengBenParameter2 where Type='".$params['type']."' and isDelete=0 " );
        $page = $params['page'] == '' ? 1 : $params['page'];
        $per = 10;
        $start = ( $page - 1 ) * $per;
        $url = "tieshuicb.php";

        $data = $this->maindao->getAll($params['type'],$start,$per);	   
        //print_r($data);
        $this->assign("data",$data);
        $this->assign("page",$page);
        $this->assign("tatalpage",ceil($total/$per));
        
        if($params['date']==""){
            
            $flag=1;
            $lastday=date("Y-m-d");
            while(true)
            {
                $lastday=date('Y-m-d',strtotime('-'.$flag.' day',strtotime($lastday)));
                //echo $lastday;
                if(file_get_contents(APP_URL_WWW.'/isholiday.php?date='.$lastday)!="1")
                { 
                  break;
                } 
            }
            $params['date'] = $lastday;
        }
        $jiage_arr = $this->get_price($params['date'],$params['type']);
        $this->assign("GuoChanKuangA",$jiage_arr['GuoChanKuangA']);
        $this->assign("JinKouKuangB",$jiage_arr['JinKouKuangB']);
        $this->assign("JiaoTanC",$jiage_arr['JiaoTanC']);
        $this->assign("PenChuiMeiD",$jiage_arr['PenChuiMeiD']);
        $this->assign("ChengDuiHuiPiaoTieXianLv",$jiage_arr['ChengDuiHuiPiaoTieXianLv']);
        $this->assign("RenMingBiHuiLv",$jiage_arr['RenMingBiHuiLv']);


        if($mode==1){
            $head_url = "//".$_SERVER['SERVER_NAME']."/v1.5/css/tieshui_small.css";
        }else if($mode==3||$mode==5){
            $head_url = "//".$_SERVER['SERVER_NAME']."/v1.5/css/tieshui_middle.css";
        }else if($mode==2||$mode==4){
            $head_url = "//".$_SERVER['SERVER_NAME']."/v1.5/css/tieshui_large.css";
        }else{
            $head_url = "//".$_SERVER['SERVER_NAME']."/v1.5/css/tieshui_middle.css";
        }
		$style_url= dirname("//".$_SERVER['SERVER_NAME'].$_SERVER['PHP_SELF']);
		$this->assign( "style_url", $style_url );
		$this->assign("head_url",$head_url);

        $this->assign("params",$params);
		
    }

    public function ajaxgetindexinfo($params){
        //echo "111";
        $page = $params['page'] == '' ? 1 : $params['page'];
        $per = 10;
        $start = ( $page - 1 ) * $per;
        $data = $this->maindao->getAll($params['type'],$start,$per);
        $content = "";
        foreach($data as $tmp){
            $content.='<tr>
				<td>'.$tmp['Date'].'</td>
				<td>'.$tmp['CreateDate'].'</td>
				<td>'.$tmp['adminname'].'</td>
				<td><a onclick="update('.$tmp['id'].');">修改</a></td>
			</tr>';
        }
        echo $content;
        exit;
    }

    public function update($params){
        $info = array();
        if($params['id']!=""){
            $where .=" and id='".$params['id']."' ";
            $info = $this->maindao->get_ng_ChengBenParameter2($where);	   
            $info['TuSunA'] = $info['TuSunA']*100;
            $info['BiLiA'] = $info['BiLiA']*100;
            $info['TuSunB'] = $info['TuSunB']*100;
            $info['BiLiB'] = $info['BiLiB']*100;
            $info['HanShuiLvC'] = $info['HanShuiLvC']*100;
            $info['WaiGouBiLiC'] = $info['WaiGouBiLiC']*100;
            $info['HanShuiLvD'] = $info['HanShuiLvD']*100;
            $info['ZenZhiSuiLv'] = $info['ZenZhiSuiLv']*100;

            
            $jiage_arr = $this->get_price($info['Date'],$info['Type']);
            $info['GuoChanKuangA'] = $jiage_arr['GuoChanKuangA'];
            $info['JinKouKuangB'] = $jiage_arr['JinKouKuangB'];
            $info['JiaoTanC'] = $jiage_arr['JiaoTanC'];
            $info['PenChuiMeiD'] = $jiage_arr['PenChuiMeiD'];
            $info['ChengDuiHuiPiaoTieXianLv'] = $jiage_arr['ChengDuiHuiPiaoTieXianLv'];
            $info['RenMingBiHuiLv'] = $jiage_arr['RenMingBiHuiLv'];

        }
        echo json_encode($info);
        exit;
    
    }

    function get_price($date,$type){
        $arr = array("date"=>$date);

        
     
        $sql ="select rd1 from rmbrate where `rdate` <='$date' order by  rdate desc limit 1";
        $rmbraterd1 = $this->_dao->getOne($sql);//人民币汇率中间价
     
        $zzsl=(strtotime('2018-05-01')<=strtotime($date))?'0.16':'0.17';

        if($type=="1"){

            $sql = "SELECT rd1   FROM busbankrate WHERE   `rdate` <='$date'  order by rdate desc  limit 1";
            $busbankrate = $this->_dao->getOne($sql);	

			$dat=date('Y-m-01', strtotime($date));
			$shijian=date('Y-m-d', strtotime("$dat +1 month -1 day"));			
			$sql = "select * from steelhome_gc.SteelCaiGou_base , steelhome_gc.SteelCaiGou_Info where SteelCaiGou_base.id = SteelCaiGou_Info.sb_id and run_date<='".$shijian."' and SteelCaiGou_base.is_show = 2 and SteelCaiGou_Info.is_show = 2 and SteelCaiGou_Info.onlyid ='977ab5ccf37dcb9dc6747aacb166cbbf' and steel_id =107  order by run_date desc limit 1 ";
			$result = $this->gcdao->getRow($sql);

			$GuoChanKuangA=$result['the_price_tax'];
            $JinKouKuangB=$this->selectSCPrice('H98640',$date);
            $JiaoTanC=$this->selectSCPrice('V38310',$date);
            $PenChuiMeiD=$this->selectSCPrice('U36330',$date);
			
        }else{

            $sql = "SELECT rd3   FROM busbankrate WHERE   `rdate` <='$date'  order by rdate desc  limit 1";
            $busbankrate = $this->_dao->getOne($sql);	

            $GuoChanKuangA=$this->selectSCPrice('6786102',$date);
			$JinKouKuangB=$this->selectSCPrice('H98640',$date);
			$JiaoTanC=$this->selectSCPrice('678311',$date);
			$PenChuiMeiD=$this->selectSCPrice('V263303',$date);
        }
        $info = array();
        $info['GuoChanKuangA'] = $GuoChanKuangA;
        $info['JinKouKuangB'] = $JinKouKuangB;
        $info['JiaoTanC'] = $JiaoTanC;
        $info['PenChuiMeiD'] = $PenChuiMeiD;
        $info['ChengDuiHuiPiaoTieXianLv'] = $busbankrate;
        $info['RenMingBiHuiLv'] = $rmbraterd1;
        return $info;
    }

    public function get_tieshui_cb($params){
        file_put_contents("/tmp/get_tieshui_cb",print_r($params,true));
        $date = $params['date'];
        $type = $params['type'];
        $params['TuSunA'] = $params['TuSunA']/100;
        $params['BiLiA'] = $params['BiLiA']/100;
        $params['TuSunB'] = $params['TuSunB']/100;
        $params['BiLiB'] = $params['BiLiB']/100;
        $params['HanShuiLvC'] = $params['HanShuiLvC']/100;
        $params['WaiGouBiLiC'] = $params['WaiGouBiLiC']/100;
        $params['HanShuiLvD'] = $params['HanShuiLvD']/100;
        $params['ZenZhiSuiLv'] = $params['ZenZhiSuiLv']/100;


        
        $rmbraterd1 = $params['RenMingBiHuiLv'];//人民币汇率中间价
        $zzsl=$params['ZenZhiSuiLv'];

        $busbankrate = $params['ChengDuiHuiPiaoTieXianLv'];	

        $GuoChanKuangA=$params['GuoChanKuangA'];
        $JinKouKuangB=$params['JinKouKuangB'];
        $JiaoTanC=$params['JiaoTanC'];
        $PenChuiMeiD=$params['PenChuiMeiD'];

        if($type=="1"){
           			
			$A=$this->get_A($GuoChanKuangA,$params,$busbankrate);
			$B=$this->get_B($JinKouKuangB,$params,round($rmbraterd1/100,4),$zzsl);			
			$C=$this->get_C($JiaoTanC,$params,$busbankrate,$zzsl);
            //echo "111";
            //echo $C;
			$D=$this->get_D($PenChuiMeiD,$params,$busbankrate);
			
        }else if($type=="2"){

            $A=$this->get_A2($GuoChanKuangA,$params,$busbankrate);
            $B=$this->get_B($JinKouKuangB,$params,round($rmbraterd1/100,4),$zzsl);
            $C=$this->get_C2($JiaoTanC,$params,$busbankrate,$zzsl);
			$D=$this->get_D($PenChuiMeiD,$params,$busbankrate);
			
        }
        //file_put_contents("/tmp/get_tieshui_cb","a==".$A."\n",FILE_APPEND);
        //file_put_contents("/tmp/get_tieshui_cb","b==".$B."\n",FILE_APPEND);
        //file_put_contents("/tmp/get_tieshui_cb","c==".$C."\n",FILE_APPEND);
        //file_put_contents("/tmp/get_tieshui_cb","d==".$D."\n",FILE_APPEND);

        $X=$this->get_X($A,$B,$C,$D,$params,$zzsl);
        //file_put_contents("/tmp/get_tieshui_cb","x==".$X."\n",FILE_APPEND);

        //echo "a=".$A."|b=".$B."|c=".$C."|d=".$D."|x=".round($X);
        if($params['isSave']=="1"){
            return round($X);
        }else{
            echo round($X);
            exit;
        }
    }

    public function del($params){
        $status="2";
        if($params['id']!="" && $params['GUID']!=""){
            $this->maindao->execute( "update ng_ChengBenParameter2 set isDelete=1 where id='".$params['id']."'");
            //更新任务表相同日期状态
            $where =" and id='".$params['id']."' ";
            $info = $this->maindao->get_ng_ChengBenParameter2($where);	 

            $sql = "select mTask.id from ng_ModifyChengBenParameterTask mTask,ng_ChengBenParameter2 cb2 where cb2.id=mTask.pid and mTask.status=0 and cb2.Date='".$info['Date']."' and cb2.Type='".$info['Type']."'";
            $task_pid = $this->maindao->query($sql);
            foreach($task_pid as $key=>$tmp){
                $this->maindao->execute("update ng_ModifyChengBenParameterTask set status='2' where id = '".$tmp['id']."'");
            }
            //江苏唐山铁水成本计算影响其他成本计算  ---start--查询开始执行日期，以及结束日期
            if($info['Type']=="1"){
                $other_type = "10";
            }else{
                $other_type = "11";
            }
            $start = $info['Date'];
            $end = date("Y-m-d");
            $end2 = $this->maindao->getOne( "select Date from ng_ChengBenParameter2 where Type='".$info['Type']."' and Date > '".$info['Date']."' and isDelete!='1' order by Date ASC limit 1");
            if($end2) $end = date("Y-m-d",strtotime("-1 day",strtotime($end2))); 
            $this->maindao->execute( "update ng_ModifyChengBenParameterTask SET status=2 where start='".$start."' and end='".$end."' and Type='".$other_type."' ");
            $this->maindao->execute( "INSERT INTO ng_ModifyChengBenParameterTask SET start='".$start."',end='".$end."',Type='".$other_type."',status=0");
            //唐山铁水成本计算影响其他成本计算  ---end--查询开始执行日期，以及结束日期
            $this->maindao->execute( "INSERT INTO ng_ModifyChengBenParameterTask SET pid='".$params['id']."',Type='1',status=0");
            $status="1";
            $tip = "删除成功";
        }
        if($params['GUID']==""){
            $tip = "删除失败，GUID不能为空！";
        }
        //echo $status;
        gourl("./tieshuicb.php?view=index&GUID=".$params['GUID']."&type=".$params['type']."&mode=".$params['mode']."&tip=".$tip);
        exit;
    }

    public function save($params){
        //echo "<pre>";
        //print_r($params);
        $GUID = $params['GUID'];
        if($GUID==""){
            $tip = "保存失败，GUID不能为空！";
        }else{

            
            $type = $params['type'];
            $date = $params['date'];
            if($GUID==""){
                gourl("./tieshuicb.php?view=index&GUID=".$GUID."&type=".$type."&mode=".$params['mode']."&pid=".$pid."&date=".$params['date']."&jj=".$X."&tip=保存成功");
            }
            //获取用户信息
            $user_infos = $this->t1dao->get_userinfo_byguid($GUID);
            $Mid=$user_infos['Mid'];//会员id
            $userid=$user_infos['Uid'];//用户id
            $TrueName=$user_infos['TrueName'];//用户id

            $array  =array("TuSunA","BiLiA","DaoChangYunFeiA","GangZaiFeiB","DaoChangYunFeiB","TuSunB","BiLiB","ZiChanWaiGouJiaChaC","HanShuiLvC","DaoChangYunFeiC","WaiGouBiLiC","JiaoBiC","HanShuiLvD","DaoChangYunFeiD","PenMeiLiangD","DunTieKuangHao","LianTieFuLiao","ShaoJieFuLiaoRanLiao","ZhiZhaoFeiYong","LianTieNengYuanDongli","ShaoJieNengYuanDongLi","QiuTuanNengYuanDongLi","ZenZhiSuiLv");

            $data = array();
            $data_100 = array("TuSunA","BiLiA","TuSunB","BiLiB","HanShuiLvC","WaiGouBiLiC","HanShuiLvD","ZenZhiSuiLv");
            foreach( $array as $a ){
                if(in_array($a,$data_100)){
                    $a2 = ($params[$a])/100;
                    $data[] = $a . "='". $a2 . "'";
                }else{
                    $data[] = $a . "='". $params[$a] . "'";
                }
            }
            $data = implode( ",", $data );
            //echo "INSERT INTO ng_ChengBenParameter2 SET $data ,adminid='".$userid."',Type='".$params['type']."',CreateDate=now(),Date='".$params['date']."'";exit;
            
            
            $this->maindao->execute( "INSERT INTO ng_ChengBenParameter2 SET $data ,adminid='".$userid."',adminname='".$TrueName."',Type='".$params['type']."',CreateDate=now(),Date='".$params['date']."'");

            $pid = $this->maindao->insert_id();
            
            $params['isSave'] = "1";
            $X = $this->get_tieshui_cb($params);
            //节假日不保存计算
            /*if(file_get_contents(APP_URL_WWW.'/isholiday.php?date='.$date)!="1"){  
            if($type=="1"){
                $base_type="101";
                //唐山铁水成本计算影响其他成本计算
                $other_type="10";
            }else if($type=="2"){
                $base_type="102";
                $other_type="11";
            }
            //插入当天数据
            $sql = "select id from ng_TieHeJinChengBenIndex where type='".$base_type."' and Date='".$date."' limit 1" ;
            $query = $this->maindao->getOne($sql);
            if($query!=null)
            {
               $sql="update ng_TieHeJinChengBenIndex set indexValue='".round($X)."' where Date='".$date."' and type='".$base_type."' "; 
               $this->maindao->execute($sql);
            }
            else
            {
                $sql="insert into `ng_TieHeJinChengBenIndex`(type,indexValue,Date) values('".$base_type."','".round($X)."','".$date."')";
                
                $this->maindao->execute($sql);
            }
            //echo $sql;exit;
            }*/

            
            //更新任务表相同日期状态
            $sql = "select mTask.id from ng_ModifyChengBenParameterTask mTask,ng_ChengBenParameter2 cb2 where cb2.id=mTask.pid and mTask.status=0 and cb2.Date='$date' and cb2.Type=".$type;
            $task_pid = $this->maindao->query($sql);
            foreach($task_pid as $key=>$tmp){
                $this->maindao->execute("update ng_ModifyChengBenParameterTask set status='2' where id = '".$tmp['id']."'");
            }
            
            //江苏唐山铁水成本计算影响其他成本计算  ---start--查询开始执行日期，以及结束日期
            $start = $params['date'];
            $end = date("Y-m-d");
            $end2 = $this->maindao->getOne( "select Date from ng_ChengBenParameter2 where Type='".$params['type']."' and Date > '".$params['date']."' and isDelete!='1' order by Date ASC limit 1");
            if($end2) $end = date("Y-m-d",strtotime("-1 day",strtotime($end2)));  
            $this->maindao->execute( "update ng_ModifyChengBenParameterTask SET status=2 where start='".$start."' and end='".$end."' and Type='".$other_type."' ");
            $this->maindao->execute( "INSERT INTO ng_ModifyChengBenParameterTask SET start='".$start."',end='".$end."',Type='".$other_type."',status=0");
            //唐山铁水成本计算影响其他成本计算  ---end--查询开始执行日期，以及结束日期
            //修改历史记录
            $this->maindao->execute( "INSERT INTO ng_ModifyChengBenParameterTask SET pid='".$pid."',Type='1',status=0");
            $tip = "保存成功";
        }
        //exit;
        
        //alert("保存成功");
        gourl("./tieshuicb.php?view=index&GUID=".$GUID."&type=".$type."&mode=".$params['mode']."&pid=".$pid."&date=".$params['date']."&jj=".$X."&tip=".$tip);
       
        //echo '<script type="text/javascript">window.history.go(-1);</script>'; 

        //goback();
    }

	
	
} 
?>