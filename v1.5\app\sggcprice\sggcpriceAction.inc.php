<?php
include_once('/etc/steelconf/config/isholiday.php');
include_once( '/usr/local/www/libs/phpQuery/phpQuery.php');

$GLOBALS['pz']=array("1"=>"螺纹钢","2"=>"高线","3"=>"盘螺");
$GLOBALS['city']=array("xa"=>"西安","hc"=>"韩城","hz"=>"汉中","cd"=>"成都","cq"=>"重庆",'zz'=>"郑州","lz"=>"兰州");
$GLOBALS['city_sort']=array("1"=>"西安",'2'=>"韩城","3"=>"汉中","4"=>"成都","5"=>"重庆",'6'=>"郑州","7"=>"兰州");
class sggcpriceAction extends AbstractAction{
   public function __construct(){
    parent::__construct();    
  }
	public function index($params)
	{
		//print_r($this->_dao);exit;
		$flat="--";
		$curdate=$params['curdate'];
		//$GUID=$params['GUID'];
		$GUID='1';
		if(empty($curdate)){
			$curdate=date("Y-m-d");
		} 

		$lastdate=date("Y-m-d",strtotime("-1 day",strtotime($curdate)));
		for($j=0;$j<10;$j++){
            if(_isholiday($lastdate)){
                $lastdate=date("Y-m-d",strtotime("- 1 day",strtotime($lastdate)));
            }else{
                break;
            }
		}

		//hlf start 2020/07/21
		//今日销售开单量
		//$now_xs=$this->getXs($curdate);

		$sgpq[0]=array(
			"西咸片区"=>"西咸",
			"商洛片区"=>"商洛",
			"铜川片区"=>"铜川",
			"工程事业部西安"=>"工程",
			"榆林片区"=>"榆林",
			"延安片区"=>"延安",
			"环韩城片区"=>"韩城",
			"安康片区"=>"安康",
			"庆阳片区"=>"庆阳",
			"宝平片区"=>"宝平"
		);

		$sgpq[1]=array(
			"郑州片区"=>"郑州",
			"环汉中片区"=>"汉中",
			"广元片区"=>"广元",
			"绵阳片区"=>"绵阳",
			"南充片区"=>"南充",
			"成都片区"=>"成都",
			"重庆片区"=>"重庆",
			"陇南片区"=>"陇南",
			"甘青藏片区"=>"兰州",
			"天水片区"=>"天水"
		);

		$pq_list=$this->_dao->query("select sg_PqxxTiBao.*,sg_PianQu.pianqu,sg_PianQu.pqid as pid from sg_PqxxTiBao,sg_PianQu where sg_PqxxTiBao.createTime<='".$curdate." 23:59:59' and sg_PqxxTiBao.createTime>='".$curdate." 00:00:00' and sg_PqxxTiBao.pinzhong in(1,2,3) and sg_PqxxTiBao.pqid=sg_PianQu.pqid order by updateTime,createTime,pqid asc ");
		//$pq_list2=$this->_dao->query("select sg_PqxxTiBao.*,sg_PianQu.pianqu,sg_PianQu.pqid as pid from sg_PqxxTiBao,sg_PianQu where sg_PqxxTiBao.createTime<='".$lastdate." 23:59:59' and sg_PqxxTiBao.createTime>='".$lastdate." 00:00:00' and sg_PqxxTiBao.pinzhong in(1,2,3) and sg_PqxxTiBao.pqid=sg_PianQu.pqid order by updateTime,createTime,pqid asc ");

		
		$pq_info=array();
		$pq_info2=array();
		
		foreach($pq_list as  $pq_k=>$pq_v){
			$pq_info[$pq_v['pianqu']][$pq_v['pinzhong']]=$pq_v;
		}
		foreach($GLOBALS['pz'] as $pk1=>$pk3){

			foreach($pq_info as  $pq_k2=>&$pq_v2){
				if(!$pq_v2[$pk1]){
					$pq_info[$pq_k2][$pk1]=array();
				}
				ksort($pq_v2);
			}
		}
		
		$pq_data=array();
		$pq_total=array();
		
		for($t=0;$t<2;$t++){
			foreach($sgpq[$t] as  $k=>$v){
				foreach($GLOBALS['pz'] as $pk=>$pv){
					$pq_data[$t][$pk][$pv][$k]=$pq_info[$k][$pk]['kdl'];
					$zj=$pq_info[$k][$pk]['kdl']=='' ? '':$pq_info[$k][$pk]['kdl']-$pq_info[$k][$pk]['zrkdl'];
					
					if($pq_info[$k][$pk]['kdl']!=''){
						$zj=$pq_info[$k][$pk]['kdl']-$pq_info[$k][$pk]['zrkdl'];
					
						if($zj>0){
							$zj="<font color='red'>↑".$zj."</font>";
						}elseif($zj<0){
							$zj="<font color='green'>↓".abs($zj)."</font>";
						}else{
							$zj=$flat;
						} 
					}else{
						$zj='';
					}

					$pq_data[$t][$pk][$pv.'增减'][$k]=$zj;
					$pq_total[$t][$pk][$pv][$k]=$pq_info[$k][$pk]['price'];

					if($pq_info[$k][$pk]['price']!=''){
						$zd=$pq_info[$k][$pk]['price']-$pq_info[$k][$pk]['zrprice'];
					
						if($zd>0){
							$zd="<font color='red'>↑".$zd."</font>";
						}elseif($zd<0){
							$zd="<font color='green'>↓".abs($zd)."</font>";
						}else{
							$zd=$flat;
						} 
					}else{
						$zd='';
					}
					
					$pq_total[$t][$pk][$pv.'涨跌'][$k]=$zd;
					$pq_total[$t][$pk][$pv.'竞争钢厂'][$k]=$pq_info[$k][$pk]['jzgc'];
					
					if($pq_info[$k][$pk]['tjjytype']=='1'){
						$type="涨";
					}else if($pq_info[$k][$pk]['tjjytype']=='2'){
						$type="跌";
					}else if($pq_info[$k][$pk]['tjjytype']=='0'){
						$type= $pq_info[$k][$pk]['tjjy']!=''? "平":'';
					}else{
						$type='';
					}
					$pq_total[$t][$pk][$pv.'调价建议'][$k]=$type.$pq_info[$k][$pk]['tjjy'];
				}
			}
		}

		
		$this->assign("sgpqmc",$sgpq[0]);
		$this->assign("sgpqmc2",$sgpq[1]);
		$this->assign("pq_xs",$pq_data[0]);
		$this->assign("pq_xs2",$pq_data[1]);
		$this->assign("pq_price",$pq_total[0]);
		$this->assign("pq_price2",$pq_total[1]);

		

		//定价建议
		$dj_list=$this->djjy($curdate,$GUID);
		// print_r($_GET);
		// print_r($params);exit;
		if($_GET['issave']==1){
			//保存数据---保存为下一个工作日
			$ndate=date("Y-m-d",strtotime("+ 1 day",strtotime($curdate)));
			for($i=0;$i<10;$i++){
				if(_isholiday($ndate)){
					$ndate=date("Y-m-d",strtotime("+ 1 day",strtotime($ndate)));
				}else{
					break;
				}
			}
			
			//查询当天是否已经存在模型定价
			$isexit=$this->_dao->getRow("select id from gc_price_model where date='".$ndate."' and isdel='0' ");
			if(!empty($isexit)){
				$this->_dao->execute("update gc_price_model_detail set isdel=1 where modelid='".$isexit['id']."' and UserType=0 and isdel=0");
				$modelid=$isexit['id'];
			}else{
				$sql="INSERT INTO  `steelhome_drc`.`gc_price_model` (`createtime` ,`date` ,`modeltype` ,`ismakepricy`   ,`is_wangqi` ,`isdel` ,`mc_type`)VALUES (now() , '".$ndate."'  , '1' , 0  , 0 , 0 , '2')";
				// print_r($sql);//exit;
				$this->_dao->execute($sql);
				$modelid=$this->_dao->insert_id();
			}
			
			
			
			foreach($dj_list as $key=>$value){
				foreach($value as $k1=>$v1){
					$modelpirce_name=$k1."市场";
					$modelpirce_name_sort = array_search($k1, $GLOBALS['city_sort']);
					 $ins_rdj="INSERT INTO  `steelhome_drc`.`gc_price_model_detail` (`modelid` ,`pingzhong` ,`modelprice` ,`modelprice_updown` ,`modeloldprice` ,`modelprice_type` ,`modelpirce_name` ,`modelpirce_name_sort` ,`Mid` ,`uid` ,`UserType`  ,`Ismakepricy_men` ,`Ischecked` ,`createtime` ,`ndate`  ,`mc_type`)VALUES ('".$modelid."',  '".$key."',  '".$v1['price']."',  '".$v1['zf']."',  '".$v1['lprice']."',  '1',  '".$modelpirce_name."',  '".$modelpirce_name_sort."',  '-1',  '-1',  '0', '0','0',  now(),  '".$ndate."',  '2')";
					$this->_dao->execute($ins_rdj);
				}
			}
			
		}
		$this->assign("city",$GLOBALS['city']);
		$this->assign("mx_list",$dj_list);
		//hlf end 
		$this->assign("nowday",date("Y年n月j日",strtotime($curdate)));
		//end by std 2020/7/10
	}

	

	function change_to_quotes($str) {
		return sprintf("'%s'", $str);
	 }

	 public function getXs($curdate){
		$now_week=date('Y-m-d', (time() - ((date('w') == 0 ? 7 : date('w')) - 1) * 24 * 3600));
		$last_sweek=date("Y-m-d",strtotime("-7 day",strtotime($now_week)));
		$last_eweek=date("Y-m-d",strtotime("-1 day",strtotime($now_week)));
		//print_r($last_eweek);
		$last_sweek1=date("Y-m-d",strtotime("-7 day",strtotime($last_sweek)));
		$last_eweek1=date("Y-m-d",strtotime("-1 day",strtotime($last_eweek)));

		$last_sweek2=date("Y-m-d",strtotime("-7 day",strtotime($last_sweek1)));
		$last_eweek2=date("Y-m-d",strtotime("-1 day",strtotime($last_sweek1)));

		$last_sweek3=date("Y-m-d",strtotime("-7 day",strtotime($last_sweek2)));
		$last_eweek3=date("Y-m-d",strtotime("-1 day",strtotime($last_eweek2)));

		$last_sweek4=date("Y-m-d",strtotime("-7 day",strtotime($last_sweek3)));
		$last_eweek4=date("Y-m-d",strtotime("-1 day",strtotime($last_eweek3)));

		$this->assign("now",date("n.d",strtotime($curdate)));
		$this->assign("now1",date("n月d日",strtotime($curdate)));
		$this->assign("now_week",date("n.d",strtotime($now_week)));
		$this->assign("last_sweek",date("n.d",strtotime($last_sweek)));
		$this->assign("last_eweek",date("n.d",strtotime($last_eweek)));

		$this->assign("last_sweek1",date("n.d",strtotime($last_sweek1)));
		$this->assign("last_eweek1",date("n.d",strtotime($last_eweek1)));
		$this->assign("last_sweek2",date("n.d",strtotime($last_sweek2)));
		$this->assign("last_eweek2",date("n.d",strtotime($last_eweek2)));
		$this->assign("last_sweek3",date("n.d",strtotime($last_sweek3)));
		$this->assign("last_eweek3",date("n.d",strtotime($last_eweek3)));
		$this->assign("last_sweek4",date("n.d",strtotime($last_sweek4)));
		$this->assign("last_eweek4",date("n.d",strtotime($last_eweek4)));

		//print_r($now_week);
	 }

	 public function djjy($curdate,$GUID){
		 //模型预测涨跌
		 $sdate=date("Y-m-d",strtotime("-1 day",strtotime($curdate)));
		for($i=0;$i<10;$i++){
            if(_isholiday($sdate)){
                $sdate=date("Y-m-d",strtotime("- 1 day",strtotime($sdate)));
            }else{
                break;
            }
        }
		// $vid6="";
		$vid7="'5611035','A111031','X911031','571103d','3711036','531103n','5211035','561312f','A113121','X913121','571312a','3713324','5313124','521312h','562023d','A120231','X920231','5720432','3720439','5320234','522023h','5611032','5611033','5711037','5711031','371103e','3711031','5311037','5311034','521103y','5211031','5613121','561312n','5713121','5713122','371312c','3713321','531312a','5313121','5213128','5213123','5620231','5620432','5720234','5720431','372043m','3720432','5320235','5320231','522043a','5220433','X920231','X920232'";
		// $peices6=$this->homeDao->query("select * from  marketconditions where topicture in($vid6) and mconmanagedate <='".$curdate." 23:59:59' and mconmanagedate>='".$curdate." 00:00:00'");
		$peices7=$this->homeDao->Aquery("select mastertopid,price from  marketconditions where mastertopid in($vid7) and mconmanagedate <='".$curdate." 23:59:59' and mconmanagedate>='".$curdate." 00:00:00'");
		$lase_prices7=$this->homeDao->Aquery("select mastertopid,price from  marketconditions where mastertopid in($vid7) and mconmanagedate <='".$sdate." 23:59:59' and mconmanagedate>='".$sdate." 00:00:00'");
		$Wa1=$Wb1=$Wc1=1;
		$Wa2=$Wb2=$Wc2=1;
		$W3=0.8;
		//A1,B1,C1
		$A1['xa']=$peices7['5611035']-$lase_prices7['5611035'];
		$A1['hc']=$peices7['A111031']-$lase_prices7['A111031'];
		$A1['hz']=$peices7['X911031']-$lase_prices7['X911031'];
		$A1['cd']=$peices7['531103n']-$lase_prices7['531103n'];
		$A1['cq']=$peices7['5211035']-$lase_prices7['5211035'];
		$A1['zz']=$peices7['3711036']-$lase_prices7['3711036'];
		$A1['lz']=$peices7['571103d']-$lase_prices7['571103d'];

		$B1['xa']=$peices7['561312f']-$lase_prices7['561312f'];
		$B1['hc']=$peices7['A113121']-$lase_prices7['A113121'];
		$B1['hz']=$peices7['X913121']-$lase_prices7['X913121'];
		$B1['cd']=$peices7['5313124']-$lase_prices7['5313124'];
		$B1['cq']=$peices7['521312h']-$lase_prices7['521312h'];
		$B1['zz']=$peices7['3713324']-$lase_prices7['3713324'];
		$B1['lz']=$peices7['571312a']-$lase_prices7['571312a'];

		$C1['xa']=$peices7['562023d']-$lase_prices7['562023d'];
		$C1['hc']=$peices7['A120231']-$lase_prices7['A120231'];
		$C1['hz']=$peices7['X920231']-$lase_prices7['X920231'];
		$C1['cd']=$peices7['5320234']-$lase_prices7['5320234'];
		$C1['cq']=$peices7['522023h']-$lase_prices7['522023h'];
		$C1['zz']=$peices7['3720439']-$lase_prices7['3720439'];
		$C1['lz']=$peices7['5720432']-$lase_prices7['5720432'];


		//A，A2;B，B2;C，C2;
		$A2['xa']=60; $A2['hc']=0; $A2['hz']=0; $A2['cd']=-20; $A2['cq']=-10; $A2['zz']=-70; $A2['lz']=-50; 
		$B2['xa']=60; $B2['hc']=0; $B2['hz']=0; $B2['cd']=-20; $B2['cq']=-10; $B2['zz']=-100; $B2['lz']=-50;
		$C2['xa']=50; $C2['hc']=0; $C2['hz']=50; $C2['cd']=0; $C2['cq']=-10; $C2['zz']=-60; $C2['lz']=-50; 


		$A['xa']=$peices7['5611032']-$peices7['5611033'];
		$A['hc']=0; $A['hz']=0;
		$A['cd']=$peices7['5311037']-$peices7['5311034'];
		$A['cq']=$peices7['521103y']-$peices7['5211031'];
		$A['zz']=$peices7['371103e']-$peices7['3711031'];
		$A['lz']=$peices7['5711037']-$peices7['5711031'];

		$B['xa']=$peices7['5613121']-$peices7['561312n'];
		$B['hc']=0; $B['hz']=0;
		$B['cd']=$peices7['531312a']-$peices7['5313121'];
		$B['cq']=$peices7['5213128']-$peices7['5213123'];
		$B['zz']=$peices7['371312c']-$peices7['3713321'];
		$B['lz']=$peices7['5713121']-$peices7['5713122'];

		$C['xa']=$peices7['5620231']-$peices7['5620432'];
		$C['hc']=0; $C['hz']=0;
		$C['hz']=$peices7['X920231']-$peices7['X920232'];
		$C['cd']=$peices7['5320235']-$peices7['5320231'];
		$C['cq']=$peices7['522043a']-$peices7['5220433'];
		$C['zz']=$peices7['372043m']-$peices7['3720432'];
		$C['lz']=$peices7['5720234']-$peices7['5720431'];

		

		//D
		$tmp_qh_lwg=$this->maindrc->Aquery("SELECT dta_ym,dta_6 FROM `data_table` WHERE str_to_date(dta_ym, '%Y-%m-%d %H:%i:%s') in(str_to_date('".$sdate."', '%Y-%m-%d %H:%i:%s'),str_to_date('".$curdate."', '%Y-%m-%d %H:%i:%s'))  AND dta_type in('SHQHDAY_4') and dta_1='2010'   order by id ASC") ;
		$D=$tmp_qh_lwg[$curdate]-$tmp_qh_lwg[$sdate];
		
		
		//W4
		$tmp_W4=1;
		foreach($A1 as $ak=>$av){
			if($av >0 && $B1[$ak]> 0 && $C1[$ak]> 0 ){
				if($W4>1){
					$W4[$ak]=1.2;
				}else if($W4<1){
					$W4[$ak]=0.8;
				}
				
			}else if($av <0 && $B1[$ak] < 0 && $C1[$ak] < 0 ){
				if($W4>1){
					$W4[$ak]=0.8;
				}else if($W4<1){
					$W4[$ak]=1.2;
				}
			}else if($av ==0 && $B1[$ak] == 0 && $C1[$ak] == 0){
				$W4[$ak]=1;
			}

			if($W4[$ak]==""){
				$W4[$ak]=$tmp_W4;
			}
			
			//高线
			$zdf['2'][$ak]=($A1[$ak]*$Wa1+($A2[$ak]-$A[$ak])*$Wa2+$D*$W3)/($Wa1+$W3)*$W4[$ak];
			$zdf['2'][$ak]=round($zdf['2'][$ak]/10)*10;
			//盘螺
			$zdf['3'][$ak]=($B1[$ak]*$Wb1+($B2[$ak]-$B[$ak])*$Wb2+$D*$W3)/($Wb1+$W3)*$W4[$ak];
			$zdf['3'][$ak]=round($zdf['3'][$ak]/10)*10;
			//print_r($D*$W3);
			//螺纹
			$zdf['1'][$ak]=($C1[$ak]*$Wb1+($C2[$ak]-$C[$ak])*$Wc2+$D*$W3)/($Wc1+$W3)*$W4[$ak];
			$zdf['1'][$ak]=round($zdf['1'][$ak]/10)*10;
		}
		
		
		 
		$prices=$this->_dao->query("select * from sg_zhidaojia_dr where ndate ='".$curdate."'  and pinzhong in(1,2,3) and isdel=0");
		$list=array();
		foreach($prices as $key=>$value){
			$key = array_search($value['cityname'], $GLOBALS['city']);
			$zf=$zdf[$value['pinzhong']][$key];
			$value['zf']=$zf;
			$value['lprice']=$value['price'];
			$value['price']+=$zf;
			$list[$value['pinzhong']][$value['cityname']]=$value;
		}
		//  echo "<pre>";
		//  print_r( $list);exit;
		return $list;
		
	 }
	 //保存数据
	public function savedata($params){

		if(isset($params['curdate'])){
			$curdate=$params['curdate'];
			$url = DC_URL.DCURL."/sggcprice.php?view=index&issave=1&curdate=".$params['curdate'];
		}else{
			$curdate=date("Y-m-d");
			$url = DC_URL.DCURL."/sggcprice.php?view=index&issave=1";
		}
		// $r=IsNgWorkDay($this->maindao,$GLOBALS['type_date']);
		// if($r==0){
		// 	echo "今天为休息日,不更新数据！";
		// 	exit;
		// }else{
		//$model=$GLOBALS['type_date'];
		//$url="http://www.baidu.com";
		//print_r($url);exit;
		$curdate=date("Y-m-d",strtotime("+ 1 day",strtotime($curdate)));
			for($i=0;$i<10;$i++){
				if(_isholiday($curdate)){
					$curdate=date("Y-m-d",strtotime("+ 1 day",strtotime($curdate)));
				}else{
					break;
				}
			}

		$modeltitle=date("Y年n月j日",strtotime($curdate))."建筑钢材日定价模型";
	
		phpQuery::newDocumentFile($url);
		
		$html=pq("body")->html();
		//$html=file_get_contents($url);
		//$html_huz.=pq(".huiz")->html();
		// $html_huz=str_replace("五、钢之家判断","1、钢之家判断",$html_huz);
		// $html_huz=str_replace("六、今日价格调整建议","2、价格模型建议",$html_huz);
	
		//$huiz_content=htmlentities($html_huz, ENT_QUOTES,"GB18030");
		//print_r($html_huz);
		//echo "444";
		
		$modelcontent=htmlentities($html, ENT_QUOTES,"UTF-8");
	//	print_r($html);
		//echo "111";exit;
		//echo "555";isdel
		$sql="update `gc_price_model` set modeltitle='".$modeltitle."',modelcontent='".$modelcontent."' where modeltype='1' and isdel=0 order by id desc limit 1";
		//echo "666";
		$this->_dao->execute($sql);
		//echo "777";
		//echo $modelcontent;
		print_r($html);
		// }
		
	}

	public function webview($params){
		global $city_sort;
		$flat="--";
		$curdate=$params['curdate'];
		
		if(empty($curdate)) $curdate=date("Y-m-d");
		$now_xs=$this->getXs($curdate);
		$nowday=date("Y-m-d",strtotime($curdate));
		for($j=0;$j<10;$j++){
            if(_isholiday($nowday)){
                $nowday=date("Y-m-d",strtotime("- 1 day",strtotime($nowday)));
            }else{
                break;
            }
		}

		$lastdate=date("Y-m-d",strtotime("-1 day",strtotime($nowday)));
		for($j=0;$j<10;$j++){
            if(_isholiday($lastdate)){
                $lastdate=date("Y-m-d",strtotime("- 1 day",strtotime($lastdate)));
            }else{
                break;
            }
		}
		
		
		$area[0]=array("晋钢","晋钢","酒钢","龙钢","略钢","威钢","德胜","达钢","晋钢");
		$area[1]=array("立恒","立恒","酒钢","龙钢","龙钢","威钢","威钢","达钢","济源");
		$area[2]=array("立恒","立恒","酒钢","龙钢","龙钢","威钢","威钢","达钢","济源");

		$gcprice[0]=array("5620237","7620232","5720431","A120231","X920232","7520431","5320231","5220233","3720436");
		$gcprice[1]=array("5611038","7611035","5711031","A111031","X911031","7511037","5311034","5211031","3713321");
		$gcprice[2]=array("561312h","7613125","5713122","A113121","X913121","7513323","5313121","5213123","3713321");

		$mastertopidlist=array_merge($gcprice[0],$gcprice[1],$gcprice[2]);
		$list=implode(",",$mastertopidlist);
		

		$sql="select mastertopid, `price` from steelhome.marketconditions, steelhome.marketrecord where marketconditions.`marketrecordid` = marketrecord.id and mastertopid in ('".implode("','",$mastertopidlist)."') and  managedate> '".$nowday." 00:00' and managedate< '".$nowday." 23:59' order by FIND_IN_SET(mastertopid, '$list');";
		$valarr=$this->homeDao->Aquery($sql);
		
		$sql2="select mastertopid, `price` from steelhome.marketconditions, steelhome.marketrecord where marketconditions.`marketrecordid` = marketrecord.id and mastertopid in ('".implode("','",$mastertopidlist)."') and  managedate> '".$lastdate." 00:00' and managedate< '".$lastdate." 23:59' order by FIND_IN_SET(mastertopid, '$list');";
		$valarr2=$this->homeDao->Aquery($sql2);

		$idval=array();
		foreach($gcprice as $key=>$tmp){
			foreach($tmp as $key2=>$tmp2){
				if($valarr[$tmp2]!=''){
					$idval[$key][$key2]['index']=$valarr[$tmp2];
					$zd=$valarr[$tmp2]-$valarr2[$tmp2];
					if($zd>0){
						$zd="<font color='red'>↑".$zd."</font>";
					}elseif($zd<0){
						$zd="<font color='green'>↓".abs($zd)."</font>";
					}else{
						$zd=$flat;
					} 
					$idval[$key][$key2]['zd']=$zd;
				}
				$idval[$key][$key2]['gc']=$area[$key][$key2];
			}
		}
		$this->assign("idval",$idval);
		
		
		//主要竞争钢厂调价情况  
		//晋钢
		$gcarr['jg']['1217']=array("63b7901a4130cf5e58a1f765481d0847","ee4a2d46e77fb0ca6fae1c13f7faf743","1d74e8158b6348beaf3cbbebfd5efb2a");
		//山西建龙
		$gcarr['sxjl']['1163']=array("1606c697ce47db674bf1a52a6c593fe8","17ea100ae2a164f1d26456c03a9a83ee","76f2005711466186ea9d611f8f96cb93");
		//建邦
		$gcarr['jb']['1528']=array("5a4d9192679e2db5f5565bdc89f360e8","98f0c9fd0f0e24026a67f2f219df9517","768860d11382f0ae648fe0785cd9fddd");
		//高义
		$gcarr['gy']['1683']=array("62524b79eb46ad080353cf9cac61eed6","80603751bc8d9f7910370f426d1976eb");
		//立恒
		$gcarr['lh']['1540']=array("222053f85f7600b91abd7441d17f81d0","330f89a586cc09ffea4d9b62c27f1d48","51e5aa0e1a37167508051235518edc2d");
		//威钢成都
		$gcarr['wgcd']['505']=array("052de248d01c60406dec17d3d7ccd234","c7b23c56990f422c8503e5c4db4d4d49","ed7a47decadd30444e1f7e5890fb401a");
		//达钢成都
		$gcarr['dgcd']['1466']=array("f76a6fb9ab2de19052d7673c145b14c7","3196205bbfb6d4e79f13d401f353d633","9a38c0caa819980df292ec8e19e1e3c0");
		//威钢重庆
		$gcarr['wgcq']['505']=array("aadfbde92cf37cd1fdcd147d548c61f7","18d148ea1427a348e2b194139086c50f","887824d2d964c20aa83b013cb6825899");
		//德胜成都
		$gcarr['dscd']['1880']=array("75d3f14e431a1162f2a0ab10168e673b");
		//达钢重庆
		$gcarr['dgcq']['1465']=array("222053f85f7600b91abd7441d17f81d0","4f7622648ae22b46259685af8dce476d","7158305bd160c6e4b59e7628c13d1aaf");
		//酒钢兰州
		$gcarr['jglz']['1471']=array("eea4802d73864cf30ba51b46ea980b2c","1a5ab77bcb1d06efb21b8a553cb14101","222f77b1f844e3bdf53f09af6cd57433");
		$convert=array("螺纹钢"=>'lw',"高线"=>'gx',"盘螺"=>'pl');

		foreach($gcarr as $key=>$arr){
			foreach($arr as $key1=>$value){
				foreach($value as $key2=>$val){
					$sql="select variety,the_price_tax as price,last_price_tax as oldprice,(the_price_tax-last_price_tax) as zd,gcid,ftime from steelhome_gc.steelprice_info where onlyid='$val' and gcid = '$key1' and is_show=2 and ftime<='$nowday' order by ftime desc limit 1";
					$ctarr2=$this->gcDao->getRow($sql);
					$gctj[$key][]=$ctarr2;
				}
			}
		}

		foreach($gctj as $key=>$value){
			foreach($value as $key1=>$val){
				if($val['zd']>0) $val['zd']="<font color='red'>↑".$val['zd']."</font>";
				elseif($val['zd']<0) $val['zd']="<font color='green'>↓".abs($val['zd'])."</font>";
				else $val['zd']=$flat;
				$gctjs[$key][$convert[$val["variety"]]]=$val;
			}
		}

		$this->assign("gctjs",$gctjs);
		$this->assign("nowday",date("Y年n月j日",strtotime($curdate)));
		$this->assign("titledate",date("Y年n月j日",strtotime($curdate)));
		$this->assign("dataday",date("Y年m月j日",strtotime($dataday)));
		$this->assign("lastworkday",date("Y年n月j日",strtotime($nowday)));
		$this->assign("day1",date("d",strtotime($nowday)));
		$this->assign("day2",date("d",strtotime($dataday)));


	
		$GUID=$params['GUID'];
		//$GUID="feee475ad63411e697d2001d09719b40";//决策
		
		$type=$params['Type'];
		if($params['curdate']!=''){
			$date=$params['curdate'];
		}else{
			//获取最近的模型时间
			$date=$this->_dao->getOne("select date from gc_price_model where modeltype='".$type."'   and mc_type='2' and isdel=0 order by date desc,id desc limit 1");
			//$date = date("Y-m-d");
		}
		$model=$this->_dao->getrow("select id,modelcontent,modeltitle,ismakepricy from gc_price_model where modeltype='".$type."' and date='".$date."' and isdel=0 and mc_type='2' order by id desc limit 1");
		$this->assign("model",$model);
		if($params['show']!=1){	
		$sql="select * from app_session_temp where  GUID ='".$GUID."'  order by  LoginDate DESC  limit 1";
		$res=$this->t1Dao->getRow($sql);
		
		$Mid=$res['Mid'];
		$uid=$res['Uid'];
		$username=$res['UserName'];
		
		$SignCS=$res['SignCS'];

		$sql="select * from app_license where Mid='$Mid'   and  mc_type=2 order by CreateDate desc limit 1";
		$res=$this->t1Dao->getRow($sql);	
		
		$id=$res['ID'];
		$sql="select * from app_license_privilege  where liD ='".$id."'  and privilege!='' and mc_type='2' ";
		$res=$this->t1Dao->query($sql); 
		
		$where="";
		foreach($res as $qxk=>$qxv){
			$r[$qxv['uid']]=explode(',',$qxv['privilege']);
			$d[$qxv['uid']]=explode(',',$qxv['orderno']);
			$user_arr[$qxv['uid']]=$qxv['truename'];
		}
		//print_r($user_arr);
		$qx=$r[$uid][$type-1];
		$Ismakepricy_men=0;
		if($qx==2){//建议人
			$where.=" and ((GUID='".$GUID."' and UserType=1) or  UserType=0) ";
		}else if($qx==4){//普通用户
			$where.="  and UserType=0 and Mid='-1' and uid='-1'";
		}else if($qx==1 || $qx==3){//决策人 序号大的决策人可以看到序号小的决策人的价格;3市场部等同于最后决策人
			$sort=$d[$uid][$type-1];
			$uid_str=$uid.",";
			$isjc=0;
			$max_sort=$sort;
			foreach($d as $dk=>$dv){
				$otner_sort=$dv[$type-1];
				if($r[$dk][$type-1]==1 || $r[$dk][$type-1]==3){
					if($qx==3){
						$uid_str.=$dk.",";
					}else if($qx==1 && $r[$dk][$type-1]==1){
						if($sort>$otner_sort){
							$uid_str.=$dk.",";
						}
						if($max_sort<$otner_sort){
							$max_sort=$otner_sort;
						}
					}
				}
			}
			
			if($qx==3 || $max_sort==$sort){
				$Ismakepricy_men=1;
			}
			$uid_str=trim($uid_str,",");
			$where.=' and (UserType=0 or UserType=1 or(UserType=2 and uid in('.$uid_str.')))';
		}
		//echo $where;
		
		$modeldetail=$this->_dao->query("select * from gc_price_model_detail where modelid='".$model['id']."'  and isdel=0 $where order by UserType asc,pingzhong asc,modelpirce_name_sort asc");
		
		$mx_list=array();
		$jy_list=array();
		$jc_list=array();
		$citys=array();
	//判断是否当前决策人之前的决策人,建议人都已填完价格，如果有决策人（建议人）未填，则当前决策人不可以填写价格
		$all_jc_uid=explode(",",$uid_str);
		$all_jy_uid=array();
		foreach($r as $rk=>$rv){
			if($rv[$type-1]==2){
				$all_jy_uid[]=$rk;
			}
		}

		
		$model_info=array();
		$model_arr=array();


		foreach($modeldetail as $key=>$value){
			$city=str_replace("市场","",$value['modelpirce_name']);
			$value['city']=$city;
			// $arr[$value['pingzhong']][$value['modelpirce_name_sort']]=array();
			// $arr[$value['pingzhong']][$value['modelpirce_name_sort']]=$value;
			if($value['UserType']==0){
				$citys[$value['modelpirce_name_sort']]=$city;
				$mx_list[$value['pingzhong']][$value['modelpirce_name_sort']]=$value;
			}else if($value['UserType']==1){
				if($value['uid']==$uid){//如果是建议，判断当前用户是否已经建议完成
					$isjc=1;
				}
				$jy_list[$value['uid']][$value['pingzhong']][$value['modelpirce_name_sort']]=$value;
			}else{
				//如果是决策人，判断当前用户是否已经决策过
				if($value['uid']==$uid){
					$isjc=1;
				}
				$jc_list[$value['uid']][$value['pingzhong']][$value['modelpirce_name_sort']]=$value;
			}
		}

		
		foreach($city_sort as  $k=>$v){
			foreach($GLOBALS['pz'] as $pk=>$pv){
				$model_arr[$pk][$k]=$mx_list[$pk][$k];
			}
		}
		
		$jy_is_end=1;//建议人是否都填写完成
		$jc_is_end=1;//当前决策人之前的决策人是否都填写完成
		if(count($jy_list)!=count($all_jy_uid)){
			$jy_is_end=0;
		}
		if($isjc==1 && count($jc_list)!=count($all_jc_uid)){
			$jc_is_end=0;
		}else if($isjc==0 && count($jc_list)!=(count($all_jc_uid)-1)){
			$jc_is_end=0;
		}
		//是否是最后一个建议人
		$islast_jy=0;
		if(count($jc_list)==(count($all_jc_uid)-1) && $isjc==0){
			$islast_jy=1;
		}
		if($model['ismakepricy']=='1'){//已决策，则建议人按分数拍序
			$pf_id=implode(",",$all_jy_uid);
			$pf=$this->_dao->Aquery("select uid,score from gc_price_model_score where uid in($pf_id) and Mid='".$Mid."' and mc_type='2' and ndate='".$date."' order by score desc ");
			$tmp_list=array();
			foreach($pf as $pfk=>$pfv){
				if(!empty($jy_list[$pfk])){
					$tmp_list[$pfk]=$jy_list[$pfk];
				}
			}
			
			$jy_list=$tmp_list;
			
			// print_r($pf);
			//print_r("select uid,score from gc_price_model_score where uid in($pf_id) and Mid='".$Mid."' and mc_type='2' and ndate='".$date."' order by score desc ");
		}
		//echo "<pre>";
		//print_r($jy_list);
		//如果是未填写，则灰色字体显示前一天的实际价格
		if($isjc==0){
			$sj_date=date("Y-m-d",strtotime("- 1 day",strtotime($date)));
			for($i=0;$i<10;$i++){
				if(_isholiday($sj_date)){
					$sj_date=date("Y-m-d",strtotime("- 1 day",strtotime($sj_date)));
				}else{
					break;
				}
			}
			$prices=$this->_dao->query("select * from sg_zhidaojia_dr where ndate ='".$sj_date."'  and pinzhong in(1,2,3) and isdel=0");
			$price_arr=array();
			$price_info=array();

			foreach($prices as  $pq_k=>$pq_v){
				$price_info[$pq_v['cityname']][$pq_v['pinzhong']]=$pq_v;
			}
			foreach($GLOBALS['pz'] as $pk1=>$pk3){
				foreach($price_info as  $pq_k2=>&$pq_v2){
					if(!$pq_v2[$pk1]){
						$price_info[$pq_k2][$pk1]=array();
					}
					ksort($pq_v2);
				}
			}
			foreach($city_sort as  $k=>$v){
				foreach($GLOBALS['pz'] as $pk=>$pv){
					$price_arr[$pk][$k]=$price_info[$v][$pk]['price'];
				}
			}
			$this->assign("price_arr",$price_arr);
		}

		$sgsheet=array(
			"0"=>array("西咸片区"=>"西安"),
			"1"=>array("西咸片区"=>"咸阳"),
			"2"=>array("商洛片区"=>"商洛"),
			"3"=>array("铜川片区"=>"铜川"),
			"4"=>array("工程事业部"=>"西安"),
			"5"=>array("榆林片区"=>"榆林"),
			"6"=>array("榆林片区"=>"银川"),
			"7"=>array("延安片区"=>"延安"),
			"8"=>array("环韩城片区"=>"韩城"),
			"9"=>array("环韩城片区"=>"渭北"),
			"10"=>array("环韩城片区"=>"渭南"),
			"11"=>array("环韩城片区"=>"运城"),
			"12"=>array("环韩城片区"=>"临汾"),
			"13"=>array("环韩城片区"=>"太原"),
			"14"=>array("环韩城片区"=>"三门峡"),
			"15"=>array("安康片区"=>"安康"),
			"16"=>array("庆阳片区"=>"庆阳"),
			"17"=>array("宝平片区"=>"宝鸡"),
			"18"=>array("宝平片区"=>"平凉")
		);
		$sgsheet2=array(
			'0'=>array("郑州片区"=>"郑州"),
			'1'=>array("郑州片区"=>"洛阳"),
			'2'=>array("郑州片区"=>"南阳"),
			'3'=>array("环汉中片区"=>"汉中"),
			'4'=>array("环汉中片区"=>"巴中"),
			'5'=>array("广元片区"=>"广元"),
			'6'=>array("绵阳片区"=>"绵阳"),
			'7'=>array("南充片区"=>"南充"),
			'8'=>array("南充片区"=>"达州"),
			'9'=>array("成都片区"=>"成都"),
			'10'=>array("重庆片区"=>"重庆"),
			'11'=>array("重庆片区"=>"万州"),
			'12'=>array("陇南片区"=>"陇南"),
			'13'=>array("甘青藏片区"=>"兰州"),
			'14'=>array("天水片区"=>"天水"),
			'15'=>array("天水片区"=>"陇西")
		);

		$pz=array("1"=>"螺纹钢","2"=>"线材","3"=>"盘螺");



		$tj_list=$this->_dao->query("select * from sg_data_table where dta_type='SGtjhz' and dta_vartype='陕钢调价汇总' and dta_ym='".$date."' order by id desc ");
		$tjhz=0;
		if(!empty($tj_list)){
			$tjhz=1;
			$tj_arr=array();
			$tj_info=array();
			$tj_info2=array();

			foreach($tj_list as $tk=>$tv){
				$tj_arr[$tv['dta_1']][$tv['dta_4']][$tv['dta_5']]=$tv['dta_6'];
			}
			
			foreach($pz as $pk=>$pv){
				foreach($sgsheet as $sk=>$sv){
					foreach($sv as $k=>$v){
						$tj_info[$pk][]=$tj_arr[$pv][$k][$v];
					}
				}
				foreach($sgsheet2 as $sk2=>$sv2){
					foreach($sv2 as $k2=>$v2){
						$tj_info2[$pk][]=$tj_arr[$pv][$k2][$v2];
					}
				}
			}
		}
	}
		
		
		$model['modelcontent']=html_entity_decode($model['modelcontent'],ENT_QUOTES,'GB18038');
		$this->assign("detail_list",$detail_list);
		$this->assign("mx_list",$mx_list);
		$this->assign("jy_list",$jy_list);
		$this->assign("jc_list",$jc_list);
		$this->assign("citys",$citys);
		$this->assign("pz",$GLOBALS['pz']);
		$this->assign("qx",$qx);
		$this->assign("model",$model);
		$this->assign("Mid",$Mid);
		$this->assign("uid",$uid);
		$this->assign("isjc",$isjc);
		$this->assign("type",$type);
		$this->assign("curdate",$date);
		$this->assign("olddate",$curdate);
		$this->assign("GUID",$GUID);
		$this->assign("show",$params['show']);
		$this->assign("Ismakepricy_men",$Ismakepricy_men);
		$this->assign("jy_is_end",$jy_is_end);
		$this->assign("jc_is_end",$jc_is_end);
		$this->assign("user_arr",$user_arr);
		$this->assign("params",$params);
		$this->assign("islast_jy",$islast_jy);

		$this->assign("city_sort",$city_sort);
		$this->assign("model_arr",$model_arr);
		$this->assign("tjhz",$tjhz);
		$this->assign("tj_info",$tj_info);
		$this->assign("tj_info2",$tj_info2);
		
		$style_url= dirname("https://".$_SERVER['SERVER_NAME'].$_SERVER['PHP_SELF']);
		$this->assign( "style_url", $style_url );
	
		
		//  echo "<pre>";
		//  print_r($qx);
		
	}
	public function timing_dx($params){//定时提醒填写建议
		$type=$params['type'];
		if($params['isjc']){//发给决策人
			$sql="select * from app_license_privilege  where 1  and privilege!='' and mc_type='2' ";
			$res=$this->t1Dao->query($sql); 

			$where="";
			foreach($res as $qxk=>$qxv){
				$privilege=explode(',',$qxv['privilege']);
				$qx=$privilege[$type-1];
				if($qx=='1'){
					$r[]=$qxv['uid'];
				}
			}
			$uid_str=implode(",",$r);
			if($uid_str!=""){
				$sql="select * from app_session_temp where Uid in($uid_str)  and mc_type='2' group by Uid  order by  LoginDate DESC  limit 1";
				$allinfo=$this->t1Dao->query($sql);
			}
			
		}else{//发给建议人
			$sql="select * from app_license_privilege  where 1  and privilege!='' and mc_type='2' ";
			$res=$this->t1Dao->query($sql); 

			$where="";
			foreach($res as $qxk=>$qxv){
				$privilege=explode(',',$qxv['privilege']);
				$qx=$privilege[$type-1];
				if($qx=='2'){
					$r[]=$qxv['uid'];
				}
			}
			$uid_str=implode(",",$r);
			//$date=date("Y-m-d");
			$model=$this->_dao->getrow("select id,modelcontent,modeltitle,ismakepricy from gc_price_model where modeltype='".$type."'  and isdel=0 and mc_type='2'  order by date desc limit 1");
			$modeldetail=$this->_dao->Aquery("select id,uid from gc_price_model_detail where modelid='".$model['id']."' and ndate='".$date."'  and isdel=0 and UserType=1 and uid in($uid_str) group by uid order by UserType asc,pingzhong asc,modelpirce_name_sort asc");
			
			if($modeldetail){
				foreach($r as $k=>$v){
					foreach($modeldetail as $k1=>$v1){
						if($v==$v1){
							unset($r[$k]);
						}
					}
				}
			}
			if(count($r)!=0){
				$uid_str1=implode(",",$r);
				$sql="select * from app_session_temp where mc_type=2 and Uid in($uid_str1)  group by Uid order by  LoginDate DESC ";
				$allinfo=$this->t1Dao->query($sql); 
				
			}
		}
		
		
		$bizid="";
		$sessionid="";
		$msgcount=1;
		$msgsign="";
		$srcmid=0;
		$srctermid="";
		$priority=3;
		$requesttime="NOW()";
		$submittime="1970-01-01 00:00:00";
		$seqid="";
		$msgid="";
		$status=0;
		$smgstatus="";
		$smgStatusNotes="";
		$donetime="1970-01-01 00:00:00";
		$reserved1="";
		$reserved2="";
		$reserved3="";
		$reserved4="";
		//print_r(count($allinfo));
		// $allinfo['13085554576']['mid']=1;
		// $allinfo['13085554576']['userid']=1;
		// $allinfo['13085554576']['usermobile']='13085554576';
		//echo "<pre>";
		//print_r($allinfo);
		if($allinfo){
			$sql_sms_send = "INSERT INTO sth_sm_mt_send(bizid,sessionid,msgcontent,msgcount,msgsign,srcmid,srctermid,desttermid,priority,requesttime,submittime,seqid,msgid,status,smgstatus,smgStatusNotes,donetime,reserved1,reserved2,reserved3,reserved4)  VALUES ";
		
			foreach($allinfo as $key=>$value){
				if($params['isjc']){//发给决策人
					$msgcontent="领导，您好，调价建议已给出，请登录陕钢云数据营销评价系统给予决策";
				}else{
					$msgcontent=$value['TrueName']."，您好，请登录陕钢云数据营销评价系统填写建筑钢材调价建议";
				}
				
				// $msgcontent=iconv("gbk","utf-8",$msgcontent);
				$srcmid=$value['Mid'];
				$desttermid=$value['MobileNumber'];
				$sql_sms_send.= "('$bizid','$sessionid','$msgcontent','$msgcount','$msgsign','$srcmid','$srctermid','$desttermid','$priority',$requesttime,'$submittime','$seqid','$msgid','$status','$smgstatus','$smgStatusNotes','$donetime','$reserved1','$reserved2','$reserved3','$reserved4'),";
			}
			//echo $sql_sms_send;
			//exit;
			
			$sql_sms_send = substr($sql_sms_send, 0, -1);
			// $this->sms->execute("set character  set 'utf8';");
			// $this->sms->execute("set names 'utf8';");

		$this->sms->Execute($sql_sms_send);

			// $this->sms->execute("set character  set 'latin1';");
			// $this->sms->execute("set names 'latin1';");
			echo "ok";
		}
		
	}

	public function save_price($params){
		//print_r($params);exit;
		$uid=$params['uid'];
		$Mid=$params['Mid'];
		$type=$params['type'];
		$modelid=$params['model_id'];
		$qx=$params['qx'];
		$Ismakepricy_men=$params['Ismakepricy_men'];
		if($qx==2){
			$UserType=1;
		}else if($qx==1){
			$UserType=2;
		}
		if($params['mx_checked']=="on"){
			
			$mx_sql="update gc_price_model_detail set Ischecked='1' where modelid='".$modelid."' and UserType=0  and isdel=0";
			//echo $mx_sql;
			$this->_dao->execute($mx_sql);
		}
		$ndate=date("Y-m-d",strtotime("- 1 day",strtotime($params['curdate'])));
			for($i=0;$i<10;$i++){
				if(_isholiday($ndate)){
					$ndate=date("Y-m-d",strtotime("- 1 day",strtotime($ndate)));
				}else{
					break;
				}
			}

			
		if(!empty($params['zf'])){//提交建议/决策价
			//指导价为前一个工作日的指导价
			
			$prices=$this->_dao->query("select * from sg_zhidaojia_dr where ndate ='".$ndate."'  and pinzhong in(1,2,3) and isdel=0");
			$price_arr=array();
			foreach($prices as $pk=>$pv){
				$key = array_search($pv['cityname'], $GLOBALS['city_sort']);
				
				$price_arr[$pv['pinzhong']][$key]=$pv;
			}
			//$zf_arr=array();
			// echo "<pre>";
			// print_r($price_arr);
			foreach($params['zf'] as $key=>$value){
				//$pz_key = array_search($key, $GLOBALS['pz']);
				$Ischecked=0;
			
				foreach($value as $k1=>$v1){
					//echo "1111";
					$city_key=$k1+1;
					$lprice=$price_arr[$key][$city_key]['price'];
					$price=$price_arr[$key][$city_key]['price']+$v1;
					$ins_rdj="INSERT INTO  `steelhome_drc`.`gc_price_model_detail` (`modelid` ,`pingzhong` ,`modelprice` ,`modelprice_updown` ,`modeloldprice` ,`modelprice_type` ,`modelpirce_name` ,`modelpirce_name_sort` ,`Mid` ,`uid` ,`UserType` ,`GUID` ,`Ismakepricy_men` ,`Ischecked` ,`createtime` ,`ndate`  ,`mc_type`)VALUES ('".$modelid."',  '".$key."',  '".$price."',  '".$v1."',  '".$lprice."',  '1',  '".$GLOBALS['city_sort'][$city_key]."市场',  '".$city_key."',  '".$Mid."',  '".$uid."',  '".$UserType."','".$params['GUID']."', '".$Ismakepricy_men."','".$Ischecked."',  now(),  '".$params['curdate']."',  '2')";
					//echo "<pre>";
					//echo $ins_rdj;
					$this->_dao->execute($ins_rdj);
					//$zf_arr[$pz_key][$city_key]=$v1;
				}
			}

			// if(!empty($params['jc_check'])){
			// 	//print_r($params['jc_check']);
			// 	foreach($params['jc_check'] as $ck1=>$ckv1){
			// 		$up_sql="update gc_price_model_detail set Ischecked=1 where modelid='".$modelid."' and uid='".$ck1."'";
			// 		//echo "<pre>".$up_sql;
			// 		$this->_dao->execute($up_sql);
			// 	}
			// }
		}
		
		
		//exit;
		
		if($params['is_tjhz']=="on"){
			$sgsheet=array(
				"1"=>array(
					'0'=>array('螺纹钢','HRB400E','φ18-22','西咸片区','西安'),
					'1'=>array('螺纹钢','HRB400E','φ18-22','西咸片区','咸阳'),
					'2'=>array('螺纹钢','HRB400E','φ18-22','商洛片区','商洛'),
					'3'=>array('螺纹钢','HRB400E','φ18-22','铜川片区','铜川'),
					'4'=>array('螺纹钢','HRB400E','φ18-22','工程事业部','西安'),
					'5'=>array('螺纹钢','HRB400E','φ18-22','榆林片区','榆林'),
					'6'=>array('螺纹钢','HRB400E','φ18-22','榆林片区','银川'),
					'7'=>array('螺纹钢','HRB400E','φ18-22','延安片区','延安'),
					'8'=>array('螺纹钢','HRB400E','φ18-22','环韩城片区','韩城'),
					'9'=>array('螺纹钢','HRB400E','φ18-22','环韩城片区','渭北'),
					'10'=>array('螺纹钢','HRB400E','φ18-22','环韩城片区','渭南'),
					'11'=>array('螺纹钢','HRB400E','φ18-22','环韩城片区','运城'),
					'12'=>array('螺纹钢','HRB400E','φ18-22','环韩城片区','临汾'),
					'13'=>array('螺纹钢','HRB400E','φ18-22','环韩城片区','太原'),
					'14'=>array('螺纹钢','HRB400E','φ18-22','环韩城片区','三门峡'),
					'15'=>array('螺纹钢','HRB400E','φ18-22','安康片区','安康'),
					'16'=>array('螺纹钢','HRB400E','φ18-22','庆阳片区','庆阳'),
					'17'=>array('螺纹钢','HRB400E','φ18-22','宝平片区','宝鸡'),
					'18'=>array('螺纹钢','HRB400E','φ18-22','宝平片区','平凉')
				),
				"2"=>array(
					'0'=>array('线材','HPB300','φ8-φ10','西咸片区','西安'),
					'1'=>array('线材','HPB300','φ8-φ10','西咸片区','咸阳'),
					'2'=>array('线材','HPB300','φ8-φ10','商洛片区','商洛'),
					'3'=>array('线材','HPB300','φ8-φ10','铜川片区','铜川'),
					'4'=>array('线材','HPB300','φ8-φ10','工程事业部','西安'),
					'5'=>array('线材','HPB300','φ8-φ10','榆林片区','榆林'),
					'6'=>array('线材','HPB300','φ8-φ10','榆林片区','银川'),
					'7'=>array('线材','HPB300','φ8-φ10','延安片区','延安'),
					'8'=>array('线材','HPB300','φ8-φ10','环韩城片区','韩城'),
					'9'=>array('线材','HPB300','φ8-φ10','环韩城片区','渭北'),
					'10'=>array('线材','HPB300','φ8-φ10','环韩城片区','渭南'),
					'11'=>array('线材','HPB300','φ8-φ10','环韩城片区','运城'),
					'12'=>array('线材','HPB300','φ8-φ10','环韩城片区','临汾'),
					'13'=>array('线材','HPB300','φ8-φ10','环韩城片区','太原'),
					'14'=>array('线材','HPB300','φ8-φ10','环韩城片区','三门峡'),
					'15'=>array('线材','HPB300','φ8-φ10','安康片区','安康'),
					'16'=>array('线材','HPB300','φ8-φ10','庆阳片区','庆阳'),
					'17'=>array('线材','HPB300','φ8-φ10','宝平片区','宝鸡'),
					'18'=>array('线材','HPB300','φ8-φ10','宝平片区','平凉')
				),
				"3"=>array(
					'0'=>array('盘螺','HRB400E','φ8-φ10','西咸片区','西安'),
					'1'=>array('盘螺','HRB400E','φ8-φ10','西咸片区','咸阳'),
					'2'=>array('盘螺','HRB400E','φ8-φ10','商洛片区','商洛'),
					'3'=>array('盘螺','HRB400E','φ8-φ10','铜川片区','铜川'),
					'4'=>array('盘螺','HRB400E','φ8-φ10','工程事业部','西安'),
					'5'=>array('盘螺','HRB400E','φ8-φ10','榆林片区','榆林'),
					'6'=>array('盘螺','HRB400E','φ8-φ10','榆林片区','银川'),
					'7'=>array('盘螺','HRB400E','φ8-φ10','延安片区','延安'),
					'8'=>array('盘螺','HRB400E','φ8-φ10','环韩城片区','韩城'),
					'9'=>array('盘螺','HRB400E','φ8-φ10','环韩城片区','渭北'),
					'10'=>array('盘螺','HRB400E','φ8-φ10','环韩城片区','渭南'),
					'11'=>array('盘螺','HRB400E','φ8-φ10','环韩城片区','运城'),
					'12'=>array('盘螺','HRB400E','φ8-φ10','环韩城片区','临汾'),
					'13'=>array('盘螺','HRB400E','φ8-φ10','环韩城片区','太原'),
					'14'=>array('盘螺','HRB400E','φ8-φ10','环韩城片区','三门峡'),
					'15'=>array('盘螺','HRB400E','φ8-φ10','安康片区','安康'),
					'16'=>array('盘螺','HRB400E','φ8-φ10','庆阳片区','庆阳'),
					'17'=>array('盘螺','HRB400E','φ8-φ10','宝平片区','宝鸡'),
					'18'=>array('盘螺','HRB400E','φ8-φ10','宝平片区','平凉')
				)
			);


			$sgsheet2=array(
				"1"=>array(
					'0'=>array('螺纹钢','HRB400E','φ18-22','郑州片区','郑州'),
					'1'=>array('螺纹钢','HRB400E','φ18-22','郑州片区','洛阳'),
					'2'=>array('螺纹钢','HRB400E','φ18-22','郑州片区','南阳'),
					'3'=>array('螺纹钢','HRB400E','φ18-22','环汉中片区','汉中'),
					'4'=>array('螺纹钢','HRB400E','φ18-22','环汉中片区','巴中'),
					'5'=>array('螺纹钢','HRB400E','φ18-22','广元片区','广元'),
					'6'=>array('螺纹钢','HRB400E','φ18-22','绵阳片区','绵阳'),
					'7'=>array('螺纹钢','HRB400E','φ18-22','南充片区','南充'),
					'8'=>array('螺纹钢','HRB400E','φ18-22','南充片区','达州'),
					'9'=>array('螺纹钢','HRB400E','φ18-22','成都片区','成都'),
					'10'=>array('螺纹钢','HRB400E','φ18-22','重庆片区','重庆'),
					'11'=>array('螺纹钢','HRB400E','φ18-22','重庆片区','万州'),
					'12'=>array('螺纹钢','HRB400E','φ18-22','陇南片区','陇南'),
					'13'=>array('螺纹钢','HRB400E','φ18-22','甘青藏片区','兰州'),
					'14'=>array('螺纹钢','HRB400E','φ18-22','天水片区','天水'),
					'15'=>array('螺纹钢','HRB400E','φ18-22','天水片区','陇西')
				),
				"2"=>array(
					'0'=>array('线材','HPB300','φ8-φ10','郑州片区','郑州'),
					'1'=>array('线材','HPB300','φ8-φ10','郑州片区','洛阳'),
					'2'=>array('线材','HPB300','φ8-φ10','郑州片区','南阳'),
					'3'=>array('线材','HPB300','φ8-φ10','环汉中片区','汉中'),
					'4'=>array('线材','HPB300','φ8-φ10','环汉中片区','巴中'),
					'5'=>array('线材','HPB300','φ8-φ10','广元片区','广元'),
					'6'=>array('线材','HPB300','φ8-φ10','绵阳片区','绵阳'),
					'7'=>array('线材','HPB300','φ8-φ10','南充片区','南充'),
					'8'=>array('线材','HPB300','φ8-φ10','南充片区','达州'),
					'9'=>array('线材','HPB300','φ8-φ10','成都片区','成都'),
					'10'=>array('线材','HPB300','φ8-φ10','重庆片区','重庆'),
					'11'=>array('线材','HPB300','φ8-φ10','重庆片区','万州'),
					'12'=>array('线材','HPB300','φ8-φ10','陇南片区','陇南'),
					'13'=>array('线材','HPB300','φ8-φ10','甘青藏片区','兰州'),
					'14'=>array('线材','HPB300','φ8-φ10','天水片区','天水'),
					'15'=>array('线材','HPB300','φ12-φ10','天水片区','陇西')
				),
				"3"=>array(
					'0'=>array('盘螺','HRB400E','φ12-φ10','郑州片区','郑州'),
					'1'=>array('盘螺','HRB400E','φ12-φ10','郑州片区','洛阳'),
					'2'=>array('盘螺','HRB400E','φ12-φ10','郑州片区','南阳'),
					'3'=>array('盘螺','HRB400E','φ12-φ10','环汉中片区','汉中'),
					'4'=>array('盘螺','HRB400E','φ12-φ10','环汉中片区','巴中'),
					'5'=>array('盘螺','HRB400E','φ12-φ10','广元片区','广元'),
					'6'=>array('盘螺','HRB400E','φ12-φ10','绵阳片区','绵阳'),
					'7'=>array('盘螺','HRB400E','φ12-φ10','南充片区','南充'),
					'8'=>array('盘螺','HRB400E','φ12-φ10','南充片区','达州'),
					'9'=>array('盘螺','HRB400E','φ12-φ10','成都片区','成都'),
					'10'=>array('盘螺','HRB400E','φ12-φ10','重庆片区','重庆'),
					'11'=>array('盘螺','HRB400E','φ12-φ10','重庆片区','万州'),
					'12'=>array('盘螺','HRB400E','φ12-φ10','陇南片区','陇南'),
					'13'=>array('盘螺','HRB400E','φ12-φ10','甘青藏片区','兰州'),
					'14'=>array('盘螺','HRB400E','φ12-φ10','天水片区','天水'),
					'15'=>array('盘螺','HRB400E','φ8-φ10','天水片区','陇西')
				)
			);
			
			$jc_info=$this->_dao->query("select * from gc_price_model_detail where ndate='".$params['curdate']."' and Ismakepricy_men=1 and mc_type=2  order by id desc");
			//echo "select modelpirce_name_sort,modelprice_updown from gc_price_model_detail where ndate='".$params['curdate']."' and Ismakepricy_men=1 and mc_type=2  order by id desc";
			
			
			$price_info=array();
			foreach($jc_info as $jk=>$jv){
				$price_info[$jv['pingzhong']][$jv['modelpirce_name_sort']]=$jv['modelprice_updown'];
			}

			$A1['1']=$price_info['1']['1'];	
			$A1['2']=$price_info['2']['1'];	
			$A1['3']=$price_info['3']['1'];	
			$B1['1']=$price_info['1']['2'];	
			$B1['2']=$price_info['2']['2'];	
			$B1['3']=$price_info['3']['2'];
			$C1['1']=$price_info['1']['3'];	
			$C1['2']=$price_info['2']['3'];	
			$C1['3']=$price_info['3']['3'];
			$D1['1']=$price_info['1']['4'];	
			$D1['2']=$price_info['2']['4'];	
			$D1['3']=$price_info['3']['4'];
			$E1['1']=$price_info['1']['5'];	
			$E1['2']=$price_info['2']['5'];	
			$E1['3']=$price_info['3']['5'];
			$F1['1']=$price_info['1']['6'];	
			$F1['2']=$price_info['2']['6'];	
			$F1['3']=$price_info['3']['6'];
			$G1['1']=$price_info['1']['7'];	
			$G1['2']=$price_info['2']['7'];	
			$G1['3']=$price_info['3']['7'];

			
			$pq_info=array("1"=>"西咸片区","2"=>"环韩城片区","3"=>"环汉中片区","4"=>"成都片区","5"=>"重庆片区","6"=>"郑州片区","7"=>"甘青藏片区");
			$tj_info=$this->_dao->query("select * from sg_data_table where dta_ym='".$ndate."' and dta_type='SGtjtz' and dta_5 in ('".implode("','",$GLOBALS['city_sort'])."')  and dta_4 in  ('".implode("','",$pq_info)."')order by id desc");
			
			$pz=array("1"=>"螺纹钢","2"=>"线材","3"=>"盘螺");
			

			$tjprice_info=array();
			foreach($tj_info as $tk=>$tv){
				$ckey = array_search($tv['dta_5'], $GLOBALS['city_sort']);
				$pkey = array_search($tv['dta_1'], $pz);
				$tjprice_info[$pkey][$ckey]=$tv['dta_6'];
			}

			$A0['1']=$tjprice_info['1']['1'];	
			$A0['2']=$tjprice_info['2']['1'];	
			$A0['3']=$tjprice_info['3']['1'];	

			$B0['1']=$tjprice_info['1']['2'];	
			$B0['2']=$tjprice_info['2']['2'];	
			$B0['3']=$tjprice_info['3']['2'];

			$C0['1']=$tjprice_info['1']['3'];	
			$C0['2']=$tjprice_info['2']['3'];	
			$C0['3']=$tjprice_info['3']['3'];

			$D0['1']=$tjprice_info['1']['4'];	
			$D0['2']=$tjprice_info['2']['4'];	
			$D0['3']=$tjprice_info['3']['4'];

			$E0['1']=$tjprice_info['1']['5'];	
			$E0['2']=$tjprice_info['2']['5'];	
			$E0['3']=$tjprice_info['3']['5'];

			$F0['1']=$tjprice_info['1']['6'];	
			$F0['2']=$tjprice_info['2']['6'];	
			$F0['3']=$tjprice_info['3']['6'];

			$G0['1']=$tjprice_info['1']['7'];	
			$G0['2']=$tjprice_info['2']['7'];	
			$G0['3']=$tjprice_info['3']['7'];

			$A[1]= $A0['1']==''?'':$A0['1']+$A1['1'];
			$A[2]= $A0['2']==''?'':$A0['2']+$A1['2'];
			$A[3]= $A0['3']==''?'':$A0['3']+$A1['3'];

			$B[1]= $B0['1']==''?'':$B0['1']+$B1['1'];
			$B[2]= $B0['2']==''?'':$B0['2']+$B1['2'];
			$B[3]= $B0['3']==''?'':$B0['3']+$B1['3'];

			$C[1]= $C0['1']==''?'':$C0['1']+$C1['1'];
			$C[2]= $C0['2']==''?'':$C0['2']+$C1['2'];
			$C[3]= $C0['3']==''?'':$C0['3']+$C1['3'];

			$D[1]= $D0['1']==''?'':$D0['1']+$D1['1'];
			$D[2]= $D0['2']==''?'':$D0['2']+$D1['2'];
			$D[3]= $D0['3']==''?'':$D0['3']+$D1['3'];

			$E[1]= $E0['1']==''?'':$E0['1']+$E1['1'];
			$E[2]= $E0['2']==''?'':$E0['2']+$E1['2'];
			$E[3]= $E0['3']==''?'':$E0['3']+$E1['3'];

			$F[1]= $F0['1']==''?'':$F0['1']+$F1['1'];
			$F[2]= $F0['2']==''?'':$F0['2']+$F1['2'];
			$F[3]= $F0['3']==''?'':$F0['3']+$F1['3'];

			$G[1]= $G0['1']==''?'':$G0['1']+$G1['1'];
			$G[2]= $G0['2']==''?'':$G0['2']+$G1['2'];
			$G[3]= $G0['3']==''?'':$G0['3']+$G1['3'];


			$tjhz_list=array();
			$tjhz_list2=array();
			for($k=1;$k<=3;$k++){
				if($A[$k]!=''){
					$tjhz_list[$k][0]=$A[$k];
					$tjhz_list[$k][1]=$A[$k];
					$tjhz_list[$k][2]=$A[$k]-20;
					$tjhz_list[$k][3]=$A[$k]-30;
					$tjhz_list[$k][4]=$A[$k];
					$tjhz_list[$k][5]=$A[$k]-60;
					$tjhz_list[$k][6]=$A[$k]-60;
					$tjhz_list[$k][7]=$A[$k]-30;
					$tjhz_list[$k][10]=$A[$k]-40;
					$tjhz_list[$k][15]=$A[$k];
					$tjhz_list[$k][16]=$A[$k]-10;
					$tjhz_list[$k][17]=$A[$k]-20;
					$tjhz_list[$k][18]=$A[$k]-10;
					$tjhz_list2[$k][12]=$A[$k]+40;
					$tjhz_list2[$k][14]=$A[$k]-30;
				}
				if($B[$k]!=''){
					$tjhz_list[$k][8]=$B[$k];
					$tjhz_list[$k][9]=$B[$k]+50;
					$tjhz_list[$k][11]=$B[$k]+20;
					$tjhz_list[$k][12]=$B[$k]+20;
					$tjhz_list[$k][13]=$B[$k]+40;
					$tjhz_list[$k][14]=$B[$k]+40;
				}
				if($F[$k]!=''){
					$tjhz_list2[$k][0]=$F[$k];
					$tjhz_list2[$k][1]=$F[$k]+20;
					$tjhz_list2[$k][2]=$F[$k]+70;
				}
				if($C[$k]!=''){
					$tjhz_list2[$k][3]=$C[$k];
					$tjhz_list2[$k][4]=$C[$k]+20;
					$tjhz_list2[$k][5]=$C[$k]+10;
				}
				if($D[$k]!=''){
					$tjhz_list2[$k][6]=$D[$k];
					$tjhz_list2[$k][7]=$D[$k]-20;
					$tjhz_list2[$k][8]=$D[$k]+30;
					$tjhz_list2[$k][9]=$D[$k];
				}
				if($E[$k]!=''){
					$tjhz_list2[$k][10]=$E[$k];
					$tjhz_list2[$k][11]=$E[$k]+40;
				}
				if($G[$k]!=''){
					$tjhz_list2[$k][13]=$G[$k];
					$tjhz_list2[$k][15]=$G[$k];
				}
			}

			
		
			foreach($tjhz_list as $jk=>$jv){
				foreach($jv as $k=>$v){
					$dta_1=$sgsheet[$jk][$k][0];
					$dta_2=$sgsheet[$jk][$k][1];
					$dta_3=$sgsheet[$jk][$k][2];
					$dta_4=$sgsheet[$jk][$k][3];
					$dta_5=$sgsheet[$jk][$k][4];
					$dta_6=$v;
					$hzsql="insert into sg_data_table set dta_type='SGtjhz',dta_vartype='陕钢调价汇总',dta_ym='".$params['curdate']."',dta_1='".$dta_1."',dta_2='".$dta_2."',dta_3='".$dta_3."',dta_4='".$dta_4."',dta_5='".$dta_5."',dta_6='".$dta_6."',createtime=NOW(),	createuser='$uid'";
					$this->_dao->execute($hzsql);
					
				}
			}


			foreach($tjhz_list2 as $jk2=>$jv2){
				foreach($jv2 as $k2=>$v2){
					$dta_1=$sgsheet2[$jk2][$k2][0];
					$dta_2=$sgsheet2[$jk2][$k2][1];
					$dta_3=$sgsheet2[$jk2][$k2][2];
					$dta_4=$sgsheet2[$jk2][$k2][3];
					$dta_5=$sgsheet2[$jk2][$k2][4];
					$dta_6=$v2;
					$hzsql2="insert into sg_data_table set dta_type='SGtjhz',dta_vartype='陕钢调价汇总',dta_ym='".$params['curdate']."',dta_1='".$dta_1."',dta_2='".$dta_2."',dta_3='".$dta_3."',dta_4='".$dta_4."',dta_5='".$dta_5."',dta_6='".$dta_6."',createtime=NOW(),	createuser='$uid'";
					$this->_dao->execute($hzsql2);
					
				}
			}
		}

		if($Ismakepricy_men==1){
			$url = $_SERVER['HTTP_REFERER']."&show=1";
			phpQuery::newDocumentFile($url);
			$content=pq("html")->html();
			$pricycontent=htmlentities($content,ENT_QUOTES,"UTF-8");
			$up_wq='update gc_price_model set pricycontent ="'.$pricycontent.'",ismakepricy=1,makepricytime=now(),is_wangqi=1 where id="'.$modelid.'" and isdel=0 and modeltype="'.$type.'"';
			//echo "<pre>".$up_wq;
			//print_r($content);
			$this->_dao->execute($up_wq);
			$arr=array("curdate"=>$params['curdate'],"Type"=>$type);
			if(!isset($params['is_tjhz'])){
				$this->countGrade($arr);
			}
			$url1 = DC_URL.DCURL."/sgpqtb.php?action=zdjdr&date=".$params['curdate'];
			file_get_contents($url1);
		}
		if($params['islast_jy']==1){
			$timing_arr['GUID']=$params['GUID'];
			$timing_arr['type']=$params['type'];
			$timing_arr['isjc']='1';
			$this->timing_dx($timing_arr);
		}
		//exit;
		$url = $_SERVER['HTTP_REFERER'];
		goUrl($url);
	}

	public function sc_wq($params){//定时生成往期
		$ndate=$params['ndate'];
		$type=$params['type'];
		$model=$this->_dao->getrow("select id,modelcontent,modeltitle,ismakepricy from gc_price_model where modeltype='".$type."' and date<'".$ndate."' and isdel=0 and mc_type='2' order by id desc limit 1");
		$model['modelcontent']=html_entity_decode($model['modelcontent'],ENT_QUOTES,'UTF-8');
		$this->assign("model",$model);
		$this->assign("params",$params);
		$style_url= dirname("//".$_SERVER['SERVER_NAME'].$_SERVER['PHP_SELF']);
		$this->assign( "style_url", $style_url );
		
		//print_r($_SERVER);
		
	}

	public function do_sc_wq($params){
		$ndate=$params['ndate'];
		$type=$params['type'];
		$mode=$params['mode'];
		$model=$this->_dao->getrow("select id,date,modelcontent,modeltitle,ismakepricy from gc_price_model where modeltype='".$type."' and date<'".$ndate."' and isdel=0 and mc_type='2' and is_wangqi=0  order by id desc limit 1");
		
		$modelid=$model['id'];
		$date=$model['date'];
		//$url = DC_URL.DCURL."/sggcprice.php?view=sc_wq&ndate=".$ndate."&type=".$type."&mode=".$mode;
		
		$url = DC_URL.DCURL."/sggcprice.php?view=webview&show=1&curdate=".$date."&Type=".$type."&mode=".$mode;
		phpQuery::newDocumentFile($url);
		$content=pq("html")->html();
		
		$pricycontent=htmlentities($content,ENT_QUOTES,"UTF-8");
		$up_wq='update gc_price_model set pricycontent ="'.$pricycontent.'",ismakepricy=1,makepricytime=now(),is_wangqi=1 where id="'.$modelid.'" and isdel=0 and modeltype="'.$type.'"';
	//	echo "<pre>".$up_wq;
		//print_r($content);
		$this->_dao->execute($up_wq);
		$arr=array("curdate"=>$ndate,"Type"=>$type);
		$this->countGrade($arr);
		
		
	}
	public function countGrade($params){
		
		$ndate=$params['curdate'];//数据时间
		$type=$params['Type'];//模型类型
		$model=$this->_dao->getrow("select id from gc_price_model where modeltype='".$type."' and date<='".$ndate."' and mc_type='2'  and isdel=0 order by date desc, id desc limit 1");
		
		$modeldetail=$this->_dao->query("select * from gc_price_model_detail where modelid='".$model['id']."' and UserType!='0' and isdel=0 ");
		
		//print_r("select id from gc_price_model where modeltype='".$type."' and date<='".$ndate."' and mc_type='2'  and isdel=0 order by date desc, id desc limit 1");
		$list=array();
		$jc_list=array();
		
		foreach($modeldetail as $key=>$value){
			if($value['UserType']==2){
				
				$jc_list[$value['pingzhong']][$value['modelpirce_name']]['zd']=$value['modelprice_updown'];
				$jc_list[$value['pingzhong']][$value['modelpirce_name']]['price']=$value['modelprice'];
			}else{
				
				//$list[$value['pingzhong']][$value['modelpirce_name']][]=$value;
				$list[$value['uid']][]=$value;
			}
		}
		//echo "1111";
		//print_r($modeldetail);
		foreach($list as $k1=>$v1){
			$grade=10;
			$UserType=1;
			$Mid=0;
			
			foreach($v1 as $k2=>$v2){
				$sj_zd=$jc_list[$v2['pingzhong']][$v2['modelpirce_name']]['zd'];
				//$sj_price=$jc_list[$v2['pingzhong']][$v2['modelpirce_name']['price'];
				if($v2['modelprice_updown']==$sj_zd){
					$grade+=2;
				}else if(($v2['modelprice_updown'] >0 && $sj_zd>0) || ($v2['modelprice_updown'] < 0 && $sj_zd< 0) ){
					$grade+=1;
				}
				$UserType=$v2['UserType'];
				$Mid=$v2['Mid'];
			}
			//echo "1111";
			$sql="INSERT INTO  `steelhome_drc`.`gc_price_model_score` (`modelid`  ,`modelprice_type` ,`Mid` ,`uid` ,`UserType` ,`createtime` ,`ndate` ,`score` ,`mc_type`)VALUES ('".$model['id']."',  '".$type."',  '".$Mid."',  '".$k1."', '".$UserType."',  now(),  '".$ndate."',  '".$grade."',  '2')";
			//echo "<pre>".$sql;
			$this->_dao->execute($sql);
		}
		//exit;
		echo "ok";exit;

	}
}
?>