<?php
class sgcwrbDao extends Dao{
  public function __construct( $writer ){
    parent::__construct( $writer );
  }

  public function getSJCGIndex($ndate,$vtype){
  $sql = "select `CityName`,`index` from SJCGIndex where Date='$ndate' and Type='$vtype' and DType='0'  and Status=1";
  // echo $sql."<br>";
	$SJCGIndex_info = $this->query($sql);
	return $SJCGIndex_info;
  }

  public function get_shpi_material($value_zd, $topicture_arr, $date){
    $topicture_str = implode("','",$topicture_arr);
    $sql = "select topicture,$value_zd from shpi_material where topicture in ('$topicture_str') and dateday = '$date'";  
    // echo $sql."<br>";
    $shpi_material_info = $this->query($sql);
    foreach ($shpi_material_info as $v) {
      $shpi_material[$v['topicture']] = $v['price'];
    }
    return $shpi_material;
  }

  public function get_FG($value_zd, $table, $where, $date_zd, $date){
    $sql = "select $value_zd from $table $where and $date_zd = '$date'";  
    // echo $sql."<br>";
    return $this->getone($sql);
    }

    public function getpricebypricestr($pricestr,$date)
	{
		//echo $time;
		$idnumber=explode(',',$pricestr);
		//echo count($idnumber);
    $six=$seven=array();
		foreach($idnumber  as $id ){
			if (strlen ( $id ) == 6){//判断id 的字符长度 
				if(!in_array($id ,$six)){
					$six[]= $id ; 			
				} 							
			} 
			if (strlen ($id ) == 7) {
				if(!in_array($id,$seven)){
					$seven[]= $id;
				} 
			}
		}
		$sixid_str=implode("','",$six);
 		$sevenid_str=implode("','",$seven);
		$mconmanagedate.="(mconmanagedate>='".$date." 00:00:00' and mconmanagedate<='".$date." 23:59:59')";
		if($sixid_str!=''){
			$PriceListSQL=" select marketconditions.price,marketconditions.topicture, marketconditions.mconmanagedate from marketconditions
 		  	where $mconmanagedate and  marketconditions.topicture in ('$sixid_str')";
 		}
 		if($sevenid_str!=''){
 		  	$PriceListSQL=" select marketconditions.price,marketconditions.mastertopid as topicture, marketconditions.mconmanagedate from marketconditions
 		  	where $mconmanagedate and  marketconditions.mastertopid in ('$sevenid_str')";
 		}
 		if($sixid_str!=''&&$sevenid_str!=''){
	 		$PriceListSQL=" select marketconditions.price,marketconditions.topicture, marketconditions.mconmanagedate from marketconditions where $mconmanagedate and  marketconditions.topicture in ('$sixid_str') UNION( select marketconditions.price,marketconditions.mastertopid as topicture,marketconditions.mconmanagedate from marketconditions where $mconmanagedate  and marketconditions.mastertopid in('$sevenid_str'))";
 		}
		//echo $PriceListSQL;
		$PriceList= $this->query( $PriceListSQL ); 
		$dataarr =array();//数据数组
		$datearr=array();//日期数组
		foreach($PriceList  as $v )
		{
			if(strstr($v['price'],"-")){
				$avgprice = explode("-",$v['price']);
				$v['price'] = round(($avgprice['0']+$avgprice['1'])/2,2);
			}
			$dataarr[$v['topicture']]=  $v['price'];

		}
		return $dataarr;
	}

    /**
     * 普氏价格是否存在
     * Created by zfy.
     * Date:2021/1/26 10:43
     * @param $date
     * @param $priceid
     * @return bool
     */
    public function isPlattsPrice($date, $priceid)
    {
        $sql = "select price from marketconditions where topicture ='$priceid' and mconmanagedate>='$date 00:00:00' and mconmanagedate<='$date 23:59:59'";
        $price = $this->getOne($sql);
        return $price > 0 ? $price : 0;
    }

    /**
     * 获取普氏平均数据
     * Created by zfy.
     * Date:2021/1/27 10:37
     * @param $priceid
     * @param $sdate
     * @param $edate
     * @return array
     */
    public function getAvgPlattsPrice($priceid,$sdate,$edate){
        $lastsdate = date("Y-m-d",strtotime('-7 days',strtotime($sdate)));
        $lastedate = date("Y-m-d",strtotime('-7 days',strtotime($edate)));
        $thisPrice = $this->Aquery("select topicture,avg(`pricemk`) as avgprice from marketconditions where topicture ='$priceid' and mconmanagedate>='$sdate 00:00:00' and mconmanagedate<='$edate 23:59:59'");
        $lastPrice = $this->Aquery("select topicture,avg(`pricemk`) as avgprice from marketconditions where topicture ='$priceid' and mconmanagedate>='$lastsdate 00:00:00' and mconmanagedate<='$lastedate 23:59:59'");
        $priceList = array();
        foreach ($thisPrice as $index => $item) {
            $priceList[$index]['this_data'] = $item;
            $priceList[$index]['last_data'] = $lastPrice[$index];
        }
        return $priceList;
    }

    /**
     * 获取螺纹钢均价
     * Created by zfy.
     * Date:2021/1/29 9:38
     * @param $date
     * @return mixed
     */
    public function getMarketconditionQgprice($date){
        $sql = "select price as avgPrice from marketconditions_qgprice where managedate='$date' and type='1'";
        return $this->getOne($sql);
    }

    public function get_report_list($where, $limit)
    {

        return $this->query("select * from steelhome_gc.sg_report where isdel=0 $where order by date_ym desc $limit");
    }

    public function get_report_lists($where)
    {

        return $this->getOne("select count(*) from steelhome_gc.sg_report where isdel=0 $where   ");
    }

    public function getReport($id)
    {
        return $this->getOne("select filepath from steelhome_gc.sg_report where id ='".$id."' limit 1");
    }

    //取得已登陆用户
    public function getUser( $GUID, $SignCS ,$mc_type ){
        return $this->getRow( "SELECT * FROM app_session_temp WHERE GUID = '$GUID' AND mc_type='$mc_type'" );
    }

    public function WriteLog($Mid, $Uid, $SignCS, $ActionName, $Actionstr, $ActionIp, $SystemType, $SystemVersion, $MessageTitle = '', $MessageId = '', $MessageDesc = '', $mc_type = 0)
    {
        $this->execute("INSERT INTO app_logs SET Mid='$Mid', Uid='$Uid', SignCS='$SignCS', ActionName='$ActionName',Actionstr='$Actionstr', ActionDate=NOW(),ActionIp='$ActionIp', SystemType='$SystemType', SystemVersion='$SystemVersion',MessageId='$MessageId', MessageTitle='$MessageTitle', MessageDesc='$MessageDesc' , mc_type='$mc_type'");
    }

}
?>