<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );

class sg_steelhome_dataController extends AbstractController{
  
	public function __construct(){
		parent::__construct();
		$this->_action->setDao( new sg_steelhome_dataDao("DRCW") );
		$this->_action->homeDao=new sg_steelhome_dataDao("91R");
		$this->_action->gcDao=new sg_steelhome_dataDao("GC");
		$this->_action->maindrc=new sg_steelhome_dataDao("DRC");
	}

	
	public function do_get_city_price(){
		$this->_action->get_city_price( $this->_request ); //市场价格变化情况接口
	}

	public function do_get_peices7List(){
		$this->_action->get_peices7List( $this->_request ); //市场价格变化情况接口
	}

	
	public function do_get_zzs(){
		$this->_action->get_zzs( $this->_request ); //增值税率
	}


	public function do_get_rmbhl(){
		$this->_action->get_rmbhl( $this->_request ); //人民币汇率
	}
	public function do_get_rmbhl2(){
		$this->_action->get_rmbhl2( $this->_request ); //人民币汇率
	}

	public function do_get_pszs(){
		$this->_action->get_pszs( $this->_request ); //人民币汇率
	}

	public function do_get_daypszs(){
		$this->_action->get_daypszs( $this->_request ); //人民币汇率
	}

	public function do_get_gccgprice(){
		$this->_action->get_gccgprice( $this->_request ); //人民币汇率
	}

	public function do_get_ScreenPrice(){
		$this->_action->get_ScreenPrice( $this->_request ); //螺纹钢市场价格
	}

	public function do_get_SJCGIndex(){
		$this->_action->get_SJCGIndex( $this->_request ); 
	}

	//大屏市场价格
	public function do_get_ScreenPriceTime(){
		$this->_action->get_ScreenPriceTime( $this->_request ); 
	}

	public function do_get_ScreenIndexTime(){
		$this->_action->get_ScreenIndexTime( $this->_request ); 
	}

	public function do_get_JKKMTPrice(){
		$this->_action->get_JKKMTPrice( $this->_request ); 
	}

	public function do_get_zjmIndex(){
		$this->_action->get_zjmIndex( $this->_request ); 
	}

	public function do_get_GcPriceData(){
		$this->_action->get_GcPriceData( $this->_request ); 
	}
	
	public function do_get_gzjprice2(){
		$this->_action->get_gzjprice2( $this->_request ); 
	}
	public function do_get_rzg_tksPrice(){
		$this->_action->get_rzg_tksPrice( $this->_request ); 
	}
	public function do_get_gzjPriceTkspider(){
		$this->_action->get_gzjPriceTkspider( $this->_request ); 
	}
	public function do_getData_gnk(){
		$this->_action->getData_gnk( $this->_request ); 
	}
	
}
?>