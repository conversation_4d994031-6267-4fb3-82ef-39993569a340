var maxwidthscen=1920;
var nowdate='2021-06-02';
nowdate='';
var articleid=0;
var GUID='';
$(function() {
 // $('#chart1Loading').show();
 // $('#chart2Loading').show();
 // $('#chart3Loading').show();
 // $('#chart4Loading').show();
 var str=getQueryString('date');
 if(str!=null)
 {
     nowdate=str;
 }
  GUID=getQueryString('GUID');
 if(GUID==null)
 {
     GUID='';
 }

 var activeIndex = -1;
 var liDoms = $(".nav ul li");
 var iframeDoms = [];
 var pepoactive=false;
 var xunhuannum=0;
 liDoms.bind("click", function(){
     var idx = Array.prototype.indexOf.call(liDoms, this);
     pepoactive=true;
     xunhuannum=0;
     clickLiByIndex(idx);
 })

 /*点击某一个*/
 function clickLiByIndex(idx){

     if(idx === activeIndex) return;
     var predom = liDoms[activeIndex];
     //取消之前的高亮样式
     if(predom){
         $(predom).removeClass("active");
     }
     //显示当前高亮样式
     var target = liDoms[idx];
     if(target){
         //EUI.addClassName(target, "active");
         $(target).addClass("active");

     }
     nowdate='';
     clickIndex(idx);
     activeIndex = idx;
 }
 var active=getQueryString('activeIndex');
 if(active==null)
 {
     active=0;
 }
 clickLiByIndex(active);
 
 //clickIndex(1);
 function clickIndex(idx)
 {
     
         if(idx==0){
             document.getElementById("chart1").removeAttribute("_echarts_instance_");
             document.getElementById("chart2").removeAttribute("_echarts_instance_");
             document.getElementById("chart3").removeAttribute("_echarts_instance_");
             document.getElementById("chart4").removeAttribute("_echarts_instance_");
             $('#mokuai1').hide();
             $('#mokuai2').show();
             $('#mapFrame').attr('src','meeting.php?view=map&skin=blue&ProductType=2&istry='+istry);
             initChart1();
         }else if(idx==1){
             document.getElementById("chart5").removeAttribute("_echarts_instance_");
             $('#mokuai1').show();
             $('#mokuai2').hide();
             initChart2();
         }
 }

 function initChart2(){
     $('#chart1Loading').hide();
     $('#chart2Loading').hide();
     $('#chart3Loading').hide();
     $('#chart4Loading').hide();
     $('#chart5Loading').show();
     axios.post("meeting.php?action=getMeetingScreenData2&date=" + nowdate+'&GUID='+GUID+'&mtid='+mtid )
     .then((res) => 
     {
         $('#chart5Loading').hide();
         
         var series = [];
         if (res.screendata1) {
             let myCharts = echarts.init(document.getElementById('chart5'))
         option = {
             color:['#FCCE10','#0AF017',"#FA640D",'#0DD6F5','#863BF2'],
             tooltip: {
                 trigger: 'axis',
                 axisPointer: {
                     type: 'cross',
                 },
                 formatter: function (params) {
                     return params[0].name+'<br/>报到人数：'+params[0].value+"人"
                 }
             },
             grid: {
                   
                 left: 35,                                    //图表距离容器左侧多少距离
                 right: 35,                                //图表距离容器右侧侧多少距离
                 bottom: 35,                              //图表距离容器上面多少距离
                 top: 55,  
                
             },
             toolbox: {
                 padding:20,
                 show: true,
                 // feature: {
                 //     saveAsImage: {
                 //         connectedBackgroundColor: '#000A3B',
                 //         backgroundColor: '#000A3B'
                 //     }
                 // },
                 // iconStyle: {
                 //     borderColor: "#ffffff",  // 图标默认颜色
                 //   },
             },
             xAxis: {
                 type: 'category',
                 boundaryGap: false,
                 data:res.screendata1.map(item => item.name),
                 axisLabel: {
                     textStyle: {
                         color: '#00FFFF', //坐标值得具体的颜色                   
                     }
                 }
             },
             yAxis: {
                 type: 'value',
                 name:'人',
                 minInterval : 1,
                 axisLabel: {
                     formatter: '{value}人'
                 },
                 axisPointer: {
                     snap: true
                 },
                 axisLabel: {
                     textStyle: {
                         color: '#00FFFF', //坐标值得具体的颜色                   
                     }
                 },
                 nameTextStyle: {
                     color: "#00FFFF",
                 },
             },
             legend: [{

                 data: ['26日报到人数：'+res.screendata26.value+"人",'27日报到人数：'+res.screendata27.value+"人",'28日报到人数：'+res.screendata28.value+"人",'30日报到人数：'+res.screendata30.value+"人",'31日报到人数：'+res.screendata31.value+"人"],
                 textStyle: {
                     color: "#ffffff",
                 },
                 icon:'rect'
               },
             ],
             series: [
                 {
                     name:'26日报到人数：'+res.screendata26.value+"人",
                     type: 'line',
                     smooth: true,
                     data:res.screendata1.map(item => item.value),
                     lineStyle: {
                         width: 2,
                         color: new echarts.graphic.LinearGradient(0, 0, 1, 0, res.screendata1.map((value, index) => {
                            
                           return {
                             offset: index / (res.screendata1.length - 1),
                             color:  value.name.indexOf("26日") != -1? '#FCCE10' :  value.name.indexOf("27日") != -1 ? '#0AF017' : value.name.indexOf("28日") != -1 ? "#FA640D" : value.name.indexOf("30日")?"#863BF2":"#0DD6F5"
                           };
                         }))
                       }
                 },
                 {
                     name:'27日报到人数：'+res.screendata27.value+"人",
                     type: 'line',
                     smooth: true,
                     data:[]
                 },
                 {
                     name:'28日报到人数：'+res.screendata28.value+"人",
                     type: 'line',
                     smooth: true,
                     data:[],
                     
                 },
                 {
                     name:'30日报到人数：'+res.screendata30.value+"人",
                     type: 'line',
                     smooth: true,
                     data:[],
                     
                 },
                 {
                     name:'31日报到人数：'+res.screendata31.value+"人",
                     type: 'line',
                     smooth: true,
                     data:[],
                     
                 }
             ]
         };
         myCharts.setOption(option);
         window.addEventListener("resize", function() {
             myCharts.resize();
         });

         
     }
     });

 }
 
 function initChart1()
 {
     $('#chart1Loading').hide();

     $('#chart1Loading').show();
     $('#chart2Loading').show();
     $('#chart3Loading').show();
     $('#chart4Loading').show();
     axios.post("meeting.php?action=getMeetingScreenData&date=" + nowdate+'&GUID='+GUID+'&mtid='+mtid )
     .then((res) => 
     {
         //console.log(res);
         //setdate(res.date);
         $('#chart1title').html('职务分类');
         $('#chart2title').html('性别');
         $('#chart3title').html('企业类型');
         $('#chart4title').html('参会人数统计');
         $('#chart1Loading').hide();
         $('#chart2Loading').hide();
         $('#chart3Loading').hide();
         $('#chart4Loading').hide();
         var myChart = echarts.init(document.getElementById('chart1'));
         var myChart2 = echarts.init(document.getElementById('chart2'));
         var myChart3 = echarts.init(document.getElementById('chart3'));
         var myChart4 = echarts.init(document.getElementById('chart4'));
         var legend = [];
         var series = [];
         if (res.screendata1) {
             let myCharts = echarts.init(document.getElementById('chart1'))
             //3.配置
             let option = {
                 grid: {
                   
                     left: 5,                                    //图表距离容器左侧多少距离
                     right: 5,                                //图表距离容器右侧侧多少距离
                     bottom: 5,                              //图表距离容器上面多少距离
                     top: 20,  
                    
                 },
                 label: {
                     show: true, 
                     formatter: function (arg) {
                         return arg.data.name + arg.data.value + '人' 
                     }
                 },
                 series: [
                     {
                         type: 'pie',
                         data: res.screendata1,
                         itemStyle:{
                             normal: {
                                 color: function (colors) {
                                 var colorList = [
                                             '#fc8251',
                                             '#5470c6',
                                             '#9A60B4',
                                             '#ef6567',
                                             '#f9c956',
                                             '#3BA272',
                                             '#F21755',
                                             '#03BCFA',
                                             '#FA4603',
                                             '#08FA03'
                                           ];
                                     return colorList[colors.dataIndex];
                                 }
                             },
                         },
                         label: {
                             position: 'outside', // 将标签移至饼图扇区外部
                            // fontSize:'12px'
                         },
                         radius: '50%',
                     },
                     
                 ]
             }
             //4.渲染图表
             myCharts.setOption(option);
             window.addEventListener("resize", function() {
                 myCharts.resize();
             });
         } else {
             var option1 = getOptionDay_xbnew([], [], [], "bug", myChart,istry);
             myCharts.setOption(option1, true);
         }
         
         if (res.screendata2) {
             let myCharts = echarts.init(document.getElementById('chart2'))
             let option = {
                 grid: {
                     show: true,                                 //是否显示图表背景网格    
                     left: 5,                                    //图表距离容器左侧多少距离
                     right: 5,                                //图表距离容器右侧侧多少距离
                     bottom: 5,                              //图表距离容器上面多少距离
                     top: 20,                                         //图表距离容器下面多少距离
                     containLabel: true,                     //防止标签溢出  
                    
                 },
                 xAxis: {
                     type: 'category',
                     data:res.screendata2.map(item => item.name),
                     axisLabel: {
                         textStyle: {
                             color: '#01C4F7', //坐标值得具体的颜色                   
                         }
                     }
                 },
                 yAxis: {
                     type: 'value',
                     axisLabel: {
                         textStyle: {
                             color: '#01C4F7', //坐标值得具体的颜色                   
                         },
                        
                     }
                 },
                 series: [{
                     data: res.screendata2.map(item => item.value),
                     type: 'bar',
                     label: {
                         show: true,
                         position: 'top'
                     },
                     itemStyle: {
                         opacity: 0,
                         normal:{
                             color:function(params){
                                 var colorList = [
                                     '#E87C25','#01C4F7',
                                 ];
                                 return colorList[params.dataIndex];
                             }
                         }
                     },
                 }] 
             }
             myCharts.setOption(option);

             window.addEventListener("resize", function() {
                 myCharts.resize();
             });
         } else {
             var option1 = getOptionDay_xbnew([], [], [], "bug", myChart2,istry);
             myCharts.setOption(option1, true);
         }


         if (res.screendata3) {
             let myCharts = echarts.init(document.getElementById('chart3'))
             let option = {
                 grid: {
                     show: true,                                 //是否显示图表背景网格    
                     left: 5,                                    //图表距离容器左侧多少距离
                     right: 5,                                //图表距离容器右侧侧多少距离
                     bottom: 5,                              //图表距离容器上面多少距离
                     top: 20,                                       //图表距离容器下面多少距离
                     containLabel: true,                     //防止标签溢出  
                    
                 },
                 xAxis: {
                     type: 'category',
                     data:res.screendata3.map(item => item.name),
                     axisLabel: {
                         textStyle: {
                             color: '#01C4F7', //坐标值得具体的颜色                   
                         },
                        
                         formatter:function(params){
                             var newParamsName = "";// 最终拼接成的字符串
                                var paramsNameNumber = params.length;// 实际标签的个数
                                var provideNumber = 1;// 每行能显示的字的个数
                                var rowNumber = Math.ceil(paramsNameNumber / provideNumber);// 换行的话，需要显示几行，向上取整
                                // 条件等同于rowNumber>1
                                if (paramsNameNumber > provideNumber) {
                                  
                                   for (var p = 0; p < rowNumber; p++) {
                                       var tempStr = "";// 表示每一次截取的字符串
                                       var start = p * provideNumber;// 开始截取的位置
                                       var end = start + provideNumber;// 结束截取的位置
                                       // 此处特殊处理最后一行的索引值
                                       if (p == rowNumber - 1) {
                                          // 最后一次不换行
                                          tempStr = params.substring(start, paramsNameNumber);
                                       } else {
                                          // 每一次拼接字符串并换行
                                          tempStr = params.substring(start, end) + "\n";
                                       }
                                      newParamsName += tempStr;// 最终拼成的字符串
                                   }
                  
                                } else {
                                   // 将旧标签的值赋给新标签
                                   newParamsName = params;
                                }
                                //将最终的字符串返回
                                return newParamsName
                         }
                     }
                 },
                 yAxis: {
                     type: 'value',
                     axisLabel: {
                         textStyle: {
                             color: '#01C4F7', //坐标值得具体的颜色                   
                         }
                         
                     }
                 },
                 series: [{
                     data: res.screendata3.map(item => item.value),
                     type: 'bar',
                     label: {
                         show: true,
                         position: 'top'
                     },
                     itemStyle: {
                         opacity: 0,
                         normal:{
                             color:function(params){
                                 var colorList = [
                                     '#F26C05','#FCCE10','#E87C25','#27727B',
                                     '#9BCA63','#60C0DD',
                                     '#D7504B','#C6E579','#F4E001','#F0805A','#26C0C0'
                                 ];
                                 return colorList[params.dataIndex];
                             }
                         }
                     },
                 }] 
             }
             myCharts.setOption(option);
             window.addEventListener("resize", function() {
                 myCharts.resize();
             });
         }

         if (res.screendata4) {
             let myCharts = echarts.init(document.getElementById('chart4'));
             
             let option = {
                 grid: {
                     show: true,                                 //是否显示图表背景网格    
                     left: 5,                                    //图表距离容器左侧多少距离
                     right: 5,                                //图表距离容器右侧侧多少距离
                     bottom: 5,                              //图表距离容器上面多少距离
                     top: 20,                                        //图表距离容器下面多少距离
                     containLabel: true,                     //防止标签溢出  
                    
                 },
                 color: [
                     '#60C0DD','#D7504B','#C6E579','#F4E001','#F0805A','#26C0C0'
                 ],
                 xAxis: {
                     type: 'category',
                     data:res.screendata4.map(item => item.name),
                     axisLabel: {
                         textStyle: {
                             color: '#01C4F7', //坐标值得具体的颜色                   
                         }
                     }
                 },
                 yAxis: {
                     type: 'value',
                     axisLabel: {
                         textStyle: {
                             color: '#01C4F7', //坐标值得具体的颜色                   
                         }
                     }
                 },
                 series: [{
                     data: res.screendata4.map(item => item.value),
                     type: 'bar',
                     label: {
                             show: true,
                             position: 'top'
                     },
                     itemStyle: {
                         opacity: 0,
                         normal:{
                             color:function(params){
                                 var colorList = [
                                     '#E87C25','#01C4F7',
                                 ];
                                 return colorList[params.dataIndex];
                             }
                         }
                     },
                 }] 
             }
             myCharts.setOption(option);
             window.addEventListener("resize", function() {
                 myCharts.resize();
             }); 
         }
     })
 }
 
 


Date.prototype.Format = function (fmt) { // author: meizz
var o = {
 "M+": this.getMonth() + 1, // 月份
 "d+": this.getDate(), // 日
 "h+": this.getHours(), // 小时
 "m+": this.getMinutes(), // 分
 "s+": this.getSeconds(), // 秒
 "q+": Math.floor((this.getMonth() + 3) / 3), // 季度
 "S": this.getMilliseconds() // 毫秒
};
if (/(y+)/.test(fmt))
 fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
for (var k in o)
 if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
   return fmt;
}
/*setInterval(function(){
                 // initChart1();
                 // initChart2();
                 // initChart3();
                 resetchart();
                 var time1=new Date().Format("yyyy-MM-dd hh:mm:ss");
                console.log("定时更新"+time1 );
             },1000*60*5)//五分钟定时执行一次
*/

// var liDoms = $(".nav ul li");


 // setInterval(function(){
 //  //console.log(liDoms.length);
 //      var activeIndex1=activeIndex;
 //      activeIndex1++;
 //      if(activeIndex1>=liDoms.length)
 //      {
 //         activeIndex1=0
 //      }
 //      if(!pepoactive)
 //      {
 //         clickLiByIndex(activeIndex1);
 //      }
 //      else
 //      {
 //         if(xunhuannum>2)
 //         {
 //             xunhuannum=0;
 //             pepoactive=false;
 //             clickLiByIndex(activeIndex1);
 //         }
 //         else
 //         {
 //             xunhuannum++; 
 //         }  
 //      }
 //      //var time1=new Date().Format("yyyy-MM-dd hh:mm:ss");
 //      //console.log("定时更新"+time1+"当前"+activeIndex1 );
 //      //console.log(activeIndex);
      
     
 // },1000*30)//两分钟定时执行一次

     function randomNum(minNum, maxNum) {
         switch (arguments.length) {
             case 1:
                 return parseInt(Math.random() * minNum + 1, 10);
                 break;
             case 2:
                 return parseInt(Math.random() * (maxNum - minNum + 1) + minNum, 10);
                 break;
             default:
                 return 0;
                 break;
         }
     }
 

 
 
 $(document).keypress(function (event) {
     var code = (event.keyCode ? event.keyCode : event.which); 
   
     if ((code == 27)||(code == 13)){
         jsobj.closeDialogWindow(articleid);
     }
 });

 
 
 
 
});