<?php
$GLOBALS ['city_arr'] = array("西安"=>"xian","郑州"=>"zhengzhou","成都"=>"chengdu","重庆"=>"chongqing","兰州"=>"lanzhou");
$GLOBALS ['jiageid_arr'] = array("1"=>"A120231","2"=>"A113121","3"=>"A111031");
include_once ("cwlr.php");
class sg_steelhome_priceAction extends AbstractAction
{
	public function __construct()
	{
    	parent::__construct();
	}

	public function checkSession()
	{
	}

	function getzhangdie($val1,$val2){
		if (empty($val1)) {
			$cha = '';
		} else {
			$cha = round($val1 - $val2,2);
		}
		// if ($cha>0) {
		// 	$zhangdie = '<font color="red">↑'.abs($cha).'</font>';
		// } elseif ($cha<0) {
		// 	$zhangdie = '<font color="green">↓'.abs($cha).'</font>';
		// } elseif ($cha==0) {
		// 	$zhangdie = '--';
		// }
		return $cha;
	}

	function pi_bj($arr_date, $vtype){
		$city_arr = $GLOBALS ['city_arr'];
		//本平均价
		$sql1="select CityName,avg(`index`) as bawp from SJCGIndex where Date>='".$arr_date["bs"]."' and Date<='".$arr_date["be"]."' and Type='$vtype' and DType='0' and Status=1 and CityName in ('西安','郑州','成都','重庆','兰州') group by CityName";
		//上平均价
		$sql2="select CityName,avg(`index`) as sawp from SJCGIndex where Date>='".$arr_date["ss"]."' and Date<='".$arr_date["se"]."' and Type='$vtype' and DType='0' and Status=1 and CityName in ('西安','郑州','成都','重庆','兰州') group by CityName";
		// echo $sql1."<br>".$sql2."<br>";
		$binfo=$this->homeDao->query($sql1);
		$sinfo=$this->homeDao->query($sql2);
		$binfo2 = array();
		foreach($binfo as $k => $v){
			$binfo2[$v['CityName']] = $v['bawp'];
		}
		$sinfo2 = array();
		foreach($sinfo as $k => $v){
			$sinfo2[$v['CityName']] = $v['sawp'];
		}
		// echo"<pre>";print_r($binfo2);exit;
		$bi_arr = array();
		foreach($binfo2 as $k => $v){
			$bavpi=$v;
			$savpi=$sinfo2[$k];
			if($savpi != 0)
			$bp=round($bavpi-$savpi);
			else
			$bp=0;
			if($bp == '-0'){
				$bp = 0;
			}
			// if ($bp>0) {
			// 	$zhangdie = '<font color="red">↑'.abs($bp).'</font>';
			// } elseif ($bp<0) {
			// 	$zhangdie = '<font color="green">↓'.abs($bp).'</font>';
			// } elseif ($bp==0) {
			// 	$zhangdie = '--';
			// } else {
			// 	$zhangdie = $bp;
			// }
			$bi_arr[$k] = $bp;
		}
		return $bi_arr;
	}

	function pi_bj2($arr_date, $vtype){
		$jiageid_arr = $GLOBALS ['jiageid_arr'];
		//本平均价
		$strlen = strlen($jiageid_arr[$vtype]);
		if ($strlen == 6) {
			$jiage = "topicture";
		} elseif ($strlen == 7) {
			$jiage = "mastertopid";
		}
		$sql1="select avg(`price`) as bawp from marketconditions where mconmanagedate>='".$arr_date["bs"]." 00:00:00' and mconmanagedate<='".$arr_date["be"]." 23:59:59' and marketconditions.$jiage='$jiageid_arr[$vtype]'";
		//上平均价
		$sql2="select avg(`price`) as sawp from marketconditions where mconmanagedate>='".$arr_date["ss"]." 00:00:00' and mconmanagedate<='".$arr_date["se"]." 23:59:59' and marketconditions.$jiage='$jiageid_arr[$vtype]'";
		// echo $sql1."<br>".$sql2."<br>"; 
		$bavpi=$this->homeDao->getOne($sql1);
		$savpi=$this->homeDao->getOne($sql2);
		
		if($savpi != 0)
        $bp=round(($bavpi-$savpi));
		else
		$bp=0;
		if($bp == '-0'){
			$bp = 0;
		}
		return $bp;
	}

	function pi_bj3($arr_date, $mark_arr, $table){
		$markstr = implode("','",$mark_arr);
		//本平均价
		if ($table == "marketconditions") {
			$sql1="select topicture as mark, avg(`price`) as bawp from marketconditions where mconmanagedate>='".$arr_date["bs"]."' and mconmanagedate<='".$arr_date["be"]."' and topicture in ('$markstr') group by topicture";
			//上平均价
			$sql2="select topicture as mark,avg(`price`) as sawp from marketconditions where mconmanagedate>='".$arr_date["ss"]."' and mconmanagedate<='".$arr_date["se"]."' and topicture in ('$markstr') group by topicture";

			$binfo=$this->homeDao->query($sql1);
			$sinfo=$this->homeDao->query($sql2);
		} elseif ($table == "shpi_material") {
			$sql1="select topicture as mark, avg(`price`) as bawp from shpi_material where dateday>='".$arr_date["bs"]."' and dateday<='".$arr_date["be"]."' and topicture in ('$markstr') group by topicture";
			//上平均价
			$sql2="select topicture as mark,avg(`price`) as sawp from shpi_material where dateday>='".$arr_date["ss"]."' and dateday<='".$arr_date["se"]."' and topicture in ('$markstr') group by topicture";

			$binfo=$this->homeDao->query($sql1);
			$sinfo=$this->homeDao->query($sql2);
		} elseif ($table == "sg_data_table") {
			$sql1="select dta_1 as mark, avg(`dta_2`) as bawp from sg_data_table where `dta_type` = 'TEST_ERP_CG_HC' and dta_ym>='".$arr_date["bs"]."' and dta_ym<='".$arr_date["be"]."' and dta_1 in ('$markstr') group by dta_1";

			//上平均价
			$sql2="select dta_1 as mark, avg(`dta_2`) as sawp from sg_data_table where `dta_type` = 'TEST_ERP_CG_HC' and dta_ym>='".$arr_date["ss"]."' and dta_ym<='".$arr_date["se"]."' and dta_1 in ('$markstr') group by dta_1";

			$binfo=$this->_dao->query($sql1);
			$sinfo=$this->_dao->query($sql2);
		}
		// echo $sql1."<br>".$sql2."<br>";
		
		$binfo2 = array();
		foreach($binfo as $k => $v){
			$binfo2[$v['mark']] = $v['bawp'];
		}
		$sinfo2 = array();
		foreach($sinfo as $k => $v){
			$sinfo2[$v['mark']] = $v['sawp'];
		}
		// echo"<pre>";print_r($binfo2);
		// echo"<pre>";print_r($sinfo2);
		$bi_arr = array();
		foreach($binfo2 as $k => $v){
			$bavpi=$v;
			$savpi=$sinfo2[$k];
			if($savpi != 0)
			$bp=round(($bavpi-$savpi),2);
			else
			$bp="";
			if($bp == '-0'){
				$bp = 0;
			}
			$bi_arr[$k] = $bp;
		}
		return $bi_arr;
	}
	function pi_bj_shuju($arr_date, $value_zd, $table, $where, $date_zd){
		//本平均价
		$sql1="select avg(`$value_zd`) as bawp from $table $where AND $date_zd>='".$arr_date["bs"]."' and $date_zd<='".$arr_date["be"]."'";
		//上平均价
		$sql2="select avg(`$value_zd`) as sawp from $table $where AND $date_zd>='".$arr_date["ss"]."' and $date_zd<='".$arr_date["se"]."'";
		// echo $sql1."<br>".$sql2."<br>"; 
		$bavpi=$this->homeDao->getone($sql1);
		$savpi=$this->homeDao->getone($sql2);
		// echo $sql1."<br>".$sql2."<br>"; 

		if($savpi != 0)
		$bp=round(($bavpi-$savpi),2);
		else
		$bp="";
		if($bp == '-0'){
			$bp = 0;
		}
		return $bp;
	}

	function getjsonarr($data) {
		$data = html_entity_decode($data);
		$data = str_replace(" ","+",$data);
		$data_arr = json_decode($data, true);
		return $data_arr;
	}

	function getdecimal($data, $weishu) {
		if (empty($weishu)) {
			$weishu = 0;
		}
		return round($data,$weishu);
	}

	function getpreday($qdate,$vtype){
		$sqldate2="select Date from SJCGIndex where Date<'$qdate' and Type='$vtype' and DType='0' and Status=1 order by Date desc limit 1";
		// echo $sqldate2;
		$qdate = $this->homeDao->getone($sqldate2);		
		$qdate = date("Y-m-d",strtotime ($qdate));
		return $qdate;
	}
	
	public function get_xsjzpj_data($params){
		$ndate = $params['ndate'];
		// $ldate = $params['ldate'];
		$vtype = $params['vtype'];
		$ndate = $this->homeDao->getone("select Date from SJCGIndex where Date<='$ndate' and Type='$vtype' and DType='0' and Status=1 order by Date desc limit 1");
		$ldate = $this->getpreday($ndate,$vtype);
		$weekd = $this->weekday($ndate); //周环
		$mondayx = $this->monday($ndate);  //月环
		$mondayx_s = $this->monday_same($ndate); //月同
		$seasonx = $this->season($ndate); //季环
		$yeardayx = $this->yearday($ndate); //年环
		$seyd = $this->yearday_same($ndate); //年同
		$markettype_1 = array(
			'1'=>'西安',
			'2'=>'郑州',
			'3'=>'成都',
			'4'=>'重庆',
			'5'=>'兰州'
		);
		$jiageid_arr = $GLOBALS ['jiageid_arr'];

		// 陕晋川甘 价格
		$SJCGIndex_b = $this->homeDao->getSJCGIndex($ndate,$vtype);
		$SJCGIndex_s = $this->homeDao->getSJCGIndex($ldate,$vtype);
		$jiage = array();
		foreach($SJCGIndex_s as $v){
			$SJCGIndex_s2[$v['CityName']] =  $this->getdecimal($v['index']);
		}
		foreach($SJCGIndex_b as $v){
			$jiage[$v['CityName']]["price"] = $this->getdecimal($v['index']);
			$jiage[$v['CityName']]["zhangdie"] = $this->getzhangdie(round($v['index']),$SJCGIndex_s2[$v['CityName']]);
		}
		// echo "<pre>";print_r($jiage);

		$zhouhuan = array();
		$zhouhuan = $this->pi_bj($weekd, $vtype);
		$yuehuan = array();
		$yuehuan = $this->pi_bj($mondayx, $vtype);
		$yuetong = array();
		$yuetong = $this->pi_bj($mondayx_s, $vtype);
		$jihuan = array();
		$jihuan = $this->pi_bj($seasonx, $vtype);
		$nianhuan = array();
		$nianhuan = $this->pi_bj($yeardayx, $vtype);
		$niantong = array();
		$niantong = $this->pi_bj($seyd, $vtype);
		// echo"<pre>";print_r($zhouhuan);

		foreach ($markettype_1 as $k => $v) {
			$table_arr[$k]["price"] = $jiage[$v]["price"];
			$table_arr[$k]["zhangdie"] = $jiage[$v]["zhangdie"];
			$table_arr[$k]["zhouhuan"] = $zhouhuan[$v];
			$table_arr[$k]["yuehuan"] = $yuehuan[$v];
			$table_arr[$k]["yuetong"] = $yuetong[$v];
			$table_arr[$k]["jihuan"] = $jihuan[$v];
			$table_arr[$k]["nianhuan"] = $nianhuan[$v];
			$table_arr[$k]["niantong"] = $niantong[$v];
		}

		// 韩城市场龙钢高线价格
		$sql = "select marketconditions.price,marketconditions.oldprice from marketconditions where mconmanagedate>='$ndate 00:00:00' and mconmanagedate<='$ndate 23:59:59' and (marketconditions.mastertopid='$jiageid_arr[$vtype]' or topicture='$jiageid_arr[$vtype]')";
		// echo $sql;
		$marketconditions_info = $this->homeDao->getrow($sql);
		$hclg['price'] = $marketconditions_info['price'];
		$hclg['zhangdie'] = $this->getzhangdie($marketconditions_info['price'],$marketconditions_info['oldprice']);
		$hclg['zhouhuan'] = $this->pi_bj2($weekd, $vtype);
		$hclg['yuehuan'] = $this->pi_bj2($mondayx, $vtype);
		$hclg['yuetong'] = $this->pi_bj2($mondayx_s, $vtype);
		$hclg['jihuan'] = $this->pi_bj2($seasonx, $vtype);
		$hclg['nianhuan'] = $this->pi_bj2($yeardayx, $vtype);
		$hclg['niantong'] = $this->pi_bj2($seyd, $vtype);

		$table_arr['6']["price"] = $hclg['price'];
		$table_arr['6']["zhangdie"] = $hclg['zhangdie'];
		$table_arr['6']["zhouhuan"] = $hclg['zhouhuan'];
		$table_arr['6']["yuehuan"] = $hclg['yuehuan'];
		$table_arr['6']["yuetong"] = $hclg['yuetong'];
		$table_arr['6']["jihuan"] = $hclg['jihuan'];
		$table_arr['6']["nianhuan"] = $hclg['nianhuan'];
		$table_arr['6']["niantong"] = $hclg['niantong'];
		// echo"<pre>";print_r($table_arr);
		echo json_encode($table_arr);
	}

	public function get_cgjzpj_data($params){
		$ndate = $params['ndate'];
		$vtype = $params['vtype'];
		$jiage_arr = $this->getjsonarr($params['jiage_arr']);
		$topicture_arr = $this->getjsonarr($params['topicture_arr']);
		$weekd = $this->getjsonarr($params['weekd']);
		$mondayx = $this->getjsonarr($params['mondayx']);
		$mondayx_s = $this->getjsonarr($params['mondayx_s']);
		$seasonx = $this->getjsonarr($params['seasonx']);
		$yeardayx = $this->getjsonarr($params['yeardayx']);
		$seyd = $this->getjsonarr($params['seyd']);

		$ldate = $this->homeDao->getone("select dateday from shpi_material WHERE 1 and topicture='11' and dateday<'$ndate' order by dateday desc limit 1");
		
		$jagestr = implode("','",$jiage_arr);
		$sql = "select topicture,price,oldprice from marketconditions where topicture in ('$jagestr') and mconmanagedate>='$ndate 00:00:00' and mconmanagedate<='$ndate 23:59:59'";
		$jg_info = $this->homeDao->query($sql);
		$jgarr = array();
		foreach ($jg_info as $k => $v) {
			$jgarr[$v['topicture']] =  $v;
		}
		$jg_zhouhuan = array();
		$jg_zhouhuan = $this->pi_bj3($weekd, $jiage_arr, "marketconditions");
		$jg_yuehuan = array();
		$jg_yuehuan = $this->pi_bj3($mondayx, $jiage_arr, "marketconditions");
		$jg_yuetong = array();
		$jg_yuetong = $this->pi_bj3($mondayx_s, $jiage_arr, "marketconditions");
		$jg_jihuan = array();
		$jg_jihuan = $this->pi_bj3($seasonx, $jiage_arr, "marketconditions");
		$jg_nianhuan = array();
		$jg_nianhuan = $this->pi_bj3($yeardayx, $jiage_arr, "marketconditions");
		$jg_niantong = array();
		$jg_niantong = $this->pi_bj3($seyd, $jiage_arr, "marketconditions");

		$shpi_material_b = $this->homeDao->get_shpi_material("price",$topicture_arr,$ndate);
		$shpi_material_s = $this->homeDao->get_shpi_material("price",$topicture_arr,$ldate);
		$jg_zhouhuan2 = array();
		$jg_zhouhuan2 = $this->pi_bj3($weekd, $topicture_arr, "shpi_material");
		$jg_yuehuan2 = array();
		$jg_yuehuan2 = $this->pi_bj3($mondayx, $topicture_arr, "shpi_material");
		$jg_yuetong2 = array();
		$jg_yuetong2 = $this->pi_bj3($mondayx_s, $topicture_arr, "shpi_material");
		$jg_jihuan2 = array();
		$jg_jihuan2 = $this->pi_bj3($seasonx, $topicture_arr, "shpi_material");
		$jg_nianhuan2 = array();
		$jg_nianhuan2 = $this->pi_bj3($yeardayx, $topicture_arr, "shpi_material");
		$jg_niantong2 = array();
		$jg_niantong2 = $this->pi_bj3($seyd, $topicture_arr, "shpi_material");

		$pcm_b = $this->homeDao->get_FG("weiprice", "shpi_mj_pzp", " WHERE vid=0 AND type=3", "dateday", $ndate);
		$pcm_s = $this->homeDao->get_FG("weiprice", "shpi_mj_pzp", " WHERE vid=0 AND type=3", "dateday", $ldate);
		$pcm_zhouhuan = $this->pi_bj_shuju($weekd, "weiprice", "shpi_mj_pzp", " WHERE vid=0 AND type=3", "dateday");
		$pcm_yuehuan = $this->pi_bj_shuju($mondayx, "weiprice", "shpi_mj_pzp", " WHERE vid=0 AND type=3", "dateday");
		$pcm_yuetong = $this->pi_bj_shuju($mondayx_s, "weiprice", "shpi_mj_pzp", " WHERE vid=0 AND type=3", "dateday");
		$pcm_jihuan = $this->pi_bj_shuju($seasonx, "weiprice", "shpi_mj_pzp", " WHERE vid=0 AND type=3", "dateday");
		$pcm_nianhuan = $this->pi_bj_shuju($yeardayx, "weiprice", "shpi_mj_pzp", " WHERE vid=0 AND type=3", "dateday");
		$pcm_niantong = $this->pi_bj_shuju($seyd, "weiprice", "shpi_mj_pzp", " WHERE vid=0 AND type=3", "dateday");

		$data = array(
			'jgarr'=>$jgarr,
			'jg_zhouhuan'=>$jg_zhouhuan,
			'jg_yuehuan'=>$jg_yuehuan,
			'jg_yuetong'=>$jg_yuetong,
			'jg_jihuan'=>$jg_jihuan,
			'jg_nianhuan'=>$jg_nianhuan,
			'jg_niantong'=>$jg_niantong,
			'shpi_material_b'=>$shpi_material_b,
			'shpi_material_s'=>$shpi_material_s,
			'jg_zhouhuan2'=>$jg_zhouhuan2,
			'jg_yuehuan2'=>$jg_yuehuan2,
			'jg_yuetong2'=>$jg_yuetong2,
			'jg_jihuan2'=>$jg_jihuan2,
			'jg_nianhuan2'=>$jg_nianhuan2,
			'jg_niantong2'=>$jg_niantong2,
			'pcm_b'=>$pcm_b,
			'pcm_s'=>$pcm_s,
			'pcm_zhouhuan'=>$pcm_zhouhuan,
			'pcm_yuehuan'=>$pcm_yuehuan,
			'pcm_yuetong'=>$pcm_yuetong,
			'pcm_jihuan'=>$pcm_jihuan,
			'pcm_nianhuan'=>$pcm_nianhuan,
			'pcm_niantong'=>$pcm_niantong,
		);
		// echo"<pre>";print_r($pcm_zhouhuan);
		echo json_encode($data);
	}

	// 根据价格id获取钢之家一段时间的 价格
	public function get_market_price($params){
		$sdate = $params['sdate'];
		$edate = $params['edate'];
		$priceid = $params['priceid'];
		if (strlen($priceid) == 6) {
			$jiageid = 'topicture';
		} elseif (strlen($priceid) == 7) {
			$jiageid = 'mastertopid';
		}
		$sql = "select mconmanagedate,price from marketconditions where $jiageid = '$priceid' and mconmanagedate>='$sdate 00:00:00' and mconmanagedate<='$edate 23:59:59'";
		$price_info = $this->homeDao->query($sql);
		// echo "<pre>";print_r($price_info);
		$price_arr = array();
		foreach ($price_info as $v) {
			$price_arr[date("Y-m-d",strtotime($v['mconmanagedate']))] = $v['price'];
		}
		echo json_encode($price_arr);
	}

	// 西安市场价位水平
	public function get_xian_price($params){
		$sdate = $params['sdate'];
		$edate = $params['edate'];
		$smonth = $params['smonth'];
		$emonth = $params['emonth'];
		// echo $smonth.",".$emonth;
		$priceid_arr = array('562023','561312','561103');
		$data['xian_avg1'] = $this->getavgprice($sdate, $edate, $priceid_arr);
		$data['xian_avg2'] = $this->getavgprice($smonth, $emonth, $priceid_arr);
		$data['quanguo_avg1'] = $this->getqgavgprice($sdate, $edate);
		$data['quanguo_avg2'] = $this->getqgavgprice($smonth, $emonth);
		echo json_encode($data);
		// echo "<pre>";print_r($xian_avg1);
	}

	// 28个城市螺纹钢\盘螺\高线平均价 
	function getqgavgprice($sdate, $edate){
		$sql = "select type,avg(pricemk) as avgprice from marketconditions_qgprice where managedate >= '".$sdate."' and managedate <= '".$edate."' and type in ('1','2','3') group by type";
		// echo $sql;
		$price_info = $this->homeDao->query($sql);
		$pricearr2 = array();
		foreach ($price_info as $v) {
			$pricearr2[$v['type']] = round($v['avgprice']);
		}
		return $pricearr2;
	}

	function getOnePrice($params){
		$date = $params['date'];
		$priceid = $params['priceid'];
		$strlen = strlen($priceid);
		if ($strlen == 6) {
			$jagestr = 'topicture';
		} elseif ($strlen == 7) {
			$jagestr = 'mastertopid';
		}
		$sql = "select price from marketconditions where mconmanagedate <= '".$date." 23:59:59' and $jagestr = '$priceid' order by mconmanagedate desc limit 1";
		$price = $this->homeDao->getOne($sql);
		echo $price;
	}

	// 获取钢之家行情价格，返回给定价格id指定时间内对应的均价
	function getavgprice($sdate, $edate, $priceid_arr){
		$idarr_6 = array();
        $idarr_7 = array();
        $pricearr_6 = array();
        $pricearr_7 = array();
        foreach ($priceid_arr as $v) {
            $strlen = strlen($v);
            if ($strlen == 6) {
                $idarr_6[] = $v;
            } elseif ($strlen == 7) {
                $idarr_7[] = $v;
			}
		}
        if (!empty($idarr_6)) {
			$idarrstr_6 = implode("','",$idarr_6);
			$sql = "select topicture,avg(price) as avgprice from marketconditions where mconmanagedate >= '".$sdate." 00:00:00' and mconmanagedate <= '".$edate." 23:59:59' and topicture in ('$idarrstr_6') group by topicture";
			// echo $sql."<br>";
            $conditions_info_6 = $this->homeDao->query($sql);
            foreach ($conditions_info_6 as $v) {
				if($v['topicture']!='H98640')
				{
					$pricearr_6[$v['topicture']] = round($v['avgprice']);
				}
				else
				{
					$pricearr_6[$v['topicture']] = round($v['avgprice'],2);
				}
                
            }
        }
        if (!empty($idarr_7)) {
			$idarrstr_7 = implode("','",$idarr_7);
			$sql = "select mastertopid,avg(price) as avgprice from marketconditions where mconmanagedate >= '".$sdate." 00:00:00' and mconmanagedate <= '".$edate." 23:59:59' and mastertopid in ('$idarrstr_7') group by mastertopid";
            $conditions_info_7 = $this->homeDao->query($sql);
            foreach ($conditions_info_7 as $v) {
                $pricearr_7[$v['mastertopid']] = round($v['avgprice']);
            }
		}
		
		$pricearr = $pricearr_6 + $pricearr_7;
		return $pricearr;
	}

	//added by hezpeng for sgjcxsjg started 2021/01/14
    //建筑钢材销售价格-取钢之家网站28个城市汇总价格
    public function get_sgjcxsjg_data($params){

        //螺纹高线盘螺均价
        $jiage = array(
            "lw" => array("072023","082023","112023","122023","172023","222023","242023","252023","282023","332023","352023",
                            "362023","372023","392023","402023","422023","432023","412023","452023",
                            "502023","512023","522023","532023","552023","542023","562023","572023","602023"),
            "gx" => array("071103","081103","111103","121103","171103","221103","241103","251103","281103","331103","351103",
                            "361103","371103","391103","401103","421103","431103","411103","451103",
                            "501103","511103","521103","531103","551103","541103","561103","571103","601103"),
            "pl" => array("071312","081312","111312","121312","171312","221312","241312","251312","281312","331312","351312",
                            "361312","371312","391312","401312","421312","431312","411312","451312",
                            "501312","511312","521312","531312","551312","541312","561312","571312","601312"),


        );

        $jiage2 = array(
            "2023"=>"lw",
            "1103"=>"gx",
            "1312"=>"pl"
        );

        $date = $params['date'];
        if($date=="")
            $date = date("Y-m-d");

        $price_id = array();
        foreach($jiage as $key=>$jiage_arr) {
            $price_id = array_merge($price_id,$jiage_arr);
        }
        //print_r($price_id);

        $date_arr = $this->get_date($date);
        //print_r($date_arr);

        $weekd = $date_arr['week'];
        $weekd['bs'] = strtotime($weekd['bs']);
        $weekd['be'] = strtotime($weekd['be']);
        $weekd['ss'] = strtotime($weekd['ss']);
        $weekd['se'] = strtotime($weekd['se']);
        $mondayx = $date_arr['month'];
        $mondayx['bs'] = strtotime($mondayx['bs']);
        $mondayx['be'] = strtotime($mondayx['be']);
        $mondayx['ss'] = strtotime($mondayx['ss']);
        $mondayx['se'] = strtotime($mondayx['se']);
        $yeardayx = $date_arr['year'];
        $yeardayx['bs'] = strtotime($yeardayx['bs']);
        $seyd = $date_arr['year2'];
        $seyd['bs'] = strtotime($seyd['bs']);
        $seyd['be'] = strtotime($seyd['be']);
        $seyd['se'] = strtotime($seyd['se']);

        //echo date("i:s")."b<br>";
        $list = $this->getPrice2($date_arr['year'], $price_id);
        //print_r($list);
        //echo date("i:s")."c<br>";
        $data = array();
        foreach($list as $key=>$tmp){

            $mconmanagedate = strtotime($tmp['mconmanagedate']);
            $topicture = substr($tmp['topicture'],-4);
            $topicture = $jiage2[$topicture];
            $price = $tmp['price'];
            if( $mconmanagedate>=$weekd['bs'] && $mconmanagedate<=$weekd['be'] ){
                $data[$topicture]['jg_week']['bawp'][] = $price;
            }
            if( $mconmanagedate>=$weekd['ss'] && $mconmanagedate<=$weekd['se'] ){
                $data[$topicture]['jg_week']['sawp'][] = $price;
            }

            if( $mconmanagedate>=$mondayx['bs'] && $mconmanagedate<=$mondayx['be'] ){
                $data[$topicture]['jg_month']['bawp'][] = $price;
            }
            if( $mconmanagedate>=$mondayx['ss'] && $mconmanagedate<=$mondayx['se'] ){
                $data[$topicture]['jg_month']['sawp'][] = $price;
            }

            if( $mconmanagedate>=$yeardayx['bs'] ) {
                $data[$topicture]['jg_year']['bawp'][] = $price;
            }else{
                $data[$topicture]['jg_year']['sawp'][] = $price;
            }

            if( $mconmanagedate>=$seyd['bs'] && $mconmanagedate<=$seyd['be'] ){
                $data[$topicture]['jg_yeartong']['bawp'][] = $price;
            }
            if( $mconmanagedate<=$seyd['se'] ){
                $data[$topicture]['jg_yeartong']['sawp'][] = $price;
            }

        }
        //echo date("i:s")."d<br>";

        $return = array();
        foreach ($data as $key => $data2) {
            foreach ($data2 as $key2 => $data3) {
                foreach ($data3 as $key3 => $data4) {
                    $avg = array_sum($data4) / count($data4);
                    $return[$key][$key2][$key3] = round($avg);
                }
            }
        }
        //echo date("i:s")."f<br>";
        echo json_encode($return);
        exit;
    }

    public function get_sgjcxsjg_data2($params){

        //螺纹高线盘螺均价
        $jiage = array(
            "lw" => array("072023","082023","112023","122023","172023","222023","242023","252023","282023","332023","352023",
                            "362023","372023","392023","402023","422023","432023","412023","452023",
                            "502023","512023","522023","532023","552023","542023","562023","572023","602023"),
            "gx" => array("071103","081103","111103","121103","171103","221103","241103","251103","281103","331103","351103",
                            "361103","371103","391103","401103","421103","431103","411103","451103",
                            "501103","511103","521103","531103","551103","541103","561103","571103","601103"),
            "pl" => array("071312","081312","111312","121312","171312","221312","241312","251312","281312","331312","351312",
                            "361312","371312","391312","401312","421312","431312","411312","451312",
                            "501312","511312","521312","531312","551312","541312","561312","571312","601312"),


        );

        //print_r($jiage);
        //echo "<pre>";
        //echo DEBUG_TEST_DRC;
        //print_r($this->homeDao);
        //exit;


        $date = $params['date'];
        if($date=="")
        $date = date("Y-m-d");

        $date_arr = $this->get_date($date);
        //print_r($date_arr);

        $weekd = $date_arr['week'];
        $mondayx = $date_arr['month'];
        $yeardayx = $date_arr['year'];
        $seyd = $date_arr['year2'];

        $data = array();
        foreach($jiage as $key=>$jiage_arr) {

            //周
            $jg_zhouhuan = $this->getPrice($weekd, $jiage_arr);
            //月
            $jg_yuehuan = $this->getPrice($mondayx, $jiage_arr);
            //全年
            $jg_nianhuan = $this->getPrice($yeardayx, $jiage_arr);
            //年同
            $jg_niantong = $this->getPrice($seyd, $jiage_arr);


            $arr = array(
                'jg_week' => $jg_zhouhuan,
                'jg_month' => $jg_yuehuan,
                'jg_year' => $jg_nianhuan,
                'jg_yeartong' => $jg_niantong
            );

            $data[$key] = $arr;
            //break;
        }

        echo json_encode($data);
        exit;
    }

    function get_date($date){

        //$first =1 表示每周星期一为开始日期 0表示每周日为开始日期
        $first=1;
        //获取当前周的第几天 周日是 0 周一到周六是 1 - 6
        $w=date('w',strtotime($date));
        //获取本周开始日期，如果$w是0，则表示周日，减去 6 天
        $week_start=date('Y-m-d',strtotime("$date -".($w ? $w - $first : 6).' days'));
        $week_start=date('Y-m-d',strtotime("-3 days",strtotime($week_start)));//上周五

        $this_week_start=$week_start; //本周开始时间
        $this_week_end=date('Y-m-d',strtotime("$week_start +6 days"));//本周结束日期 本周四

        //如果本周四到上周五跨月，则表格中的“本月”为上月一个完整月的数值，本年为本年初到上月末的数值
        $this_week_start2 = date('Y-m-01',strtotime($this_week_start));
        $this_week_end2 = date('Y-m-01',strtotime($this_week_end));
        if( strtotime($this_week_start2) < strtotime($this_week_end2) ){
            $this_month_start  = date('Y-m', strtotime($this_week_start))."-01";//本月开始
            $this_month_end = date('Y-m-d',strtotime('-1 day', strtotime('+1 month',strtotime($this_month_start))));//本月结束

            $this_year_end = $this_month_end;//本年结束

            $last_month_start = date('Y-m',strtotime('-1 month',strtotime($this_month_start)))."-01";//上月开始
            $last_month_end = date('Y-m-d',strtotime('-1 day', strtotime('+1 month',strtotime($last_month_start))));//上月结束

            $last_year_end2=date('Y-m-d', strtotime('-1 year',strtotime($this_month_end)));//去年同期结束
        }else{
            $this_month_start  = date('Y-m', strtotime($date))."-01";//本月开始
            $this_month_end = date('Y-m-d',strtotime('-1 day', strtotime('+1 month',strtotime($this_month_start))));//本月结束

            $this_year_end = $date;//本年结束

            $last_month_start = date('Y-m',strtotime('-1 month',strtotime($date)))."-01";//上月开始
            $last_month_end = date('Y-m-d',strtotime('-1 day', strtotime('+1 month',strtotime($last_month_start))));//上月结束

            $last_year_end2=date('Y-m-d', strtotime('-1 year',strtotime($date)));//去年同期结束
        }



        $this_year_start=date('Y', strtotime($date))."-01-01";//本年开始



        $last_week_start=date('Y-m-d',strtotime("$week_start -7 days")); //上周开始时间
        $last_week_end=date('Y-m-d',strtotime("$this_week_end -7 days")); //上周结束时间



        $last_year_start=date('Y', strtotime('-1 year',strtotime($date)))."-01-01";//去年开始
        $last_year_end=date('Y', strtotime('-1 year',strtotime($date)))."-12-31";//去年结束



        $arr = array(
            "week"=>array(
                "bs"=>$this_week_start,
                "be"=>$this_week_end,
                "ss"=>$last_week_start,
                "se"=>$last_week_end
            ),
            "month"=>array(
                "bs"=>$this_month_start,
                "be"=>$this_month_end,
                "ss"=>$last_month_start,
                "se"=>$last_month_end
            ),
            "year"=>array(
                "bs"=>$this_year_start,
                "be"=>$this_year_end,
                "ss"=>$last_year_start,
                "se"=>$last_year_end
            ),
            "year2"=>array(
                "bs"=>$this_year_start,
                "be"=>$this_year_end,
                "ss"=>$last_year_start,
                "se"=>$last_year_end2
            )

        );

        return $arr;


    }

    function getPrice($arr_date, $mark_arr){

        $markstr = implode("','",$mark_arr);
        //本平均价
        $sql1="select avg(`price`) as bawp from marketconditions where mconmanagedate>='".$arr_date["bs"]." 00:00:00' and mconmanagedate<='".$arr_date["be"]." 23:59:59' and topicture in ('$markstr') ";
        //上平均价
        $sql2="select avg(`price`) as sawp from marketconditions where mconmanagedate>='".$arr_date["ss"]." 00:00:00' and mconmanagedate<='".$arr_date["se"]." 23:59:59' and topicture in ('$markstr') ";

        //echo $sql1."<br>";
        $bawp=$this->homeDao->getOne($sql1);
        $sawp=$this->homeDao->getOne($sql2);

        $bi_arr = array();
        $bi_arr['bawp'] = round($bawp);
        $bi_arr['sawp'] = round($sawp);

        return $bi_arr;
    }

    function getPrice2($arr_date, $mark_arr){
        //echo date("i:s")."a<br>";
        $markstr = implode("','",$mark_arr);
        //本年价格
        $sql1="select price ,mconmanagedate,topicture from marketconditions where mconmanagedate>='".$arr_date["ss"]." 00:00:00' and mconmanagedate<='".$arr_date["be"]." 23:59:59' and topicture in ('$markstr') ";
        //上年价格
        //$sql2="select price ,mconmanagedate,topicture from marketconditions where mconmanagedate>='".$arr_date["ss"]." 00:00:00' and mconmanagedate<='".$arr_date["se"]." 23:59:59' and topicture in ('$markstr') ";

        //echo $sql1."<br>";
        //echo $sql2."<br>";
        $bawp=$this->homeDao->query($sql1);
        //echo date("i:s")."b<br>";
        //$sawp=$this->homeDao->query($sql2);
        //echo date("i:s")."c<br>";

        $bi_arr = array();
        //$bi_arr['bawp'] = $bawp;
        //$bi_arr['sawp'] = $sawp;

        return $bawp;
    }

    //带钢销售价格评价-带钢会议价格
    public function get_dgxsjg_data($params){

	    $sdate = $params['sdate'];
	    $edate = $params['edate'];

	    $sql = "SELECT steelprice_base.id, steelprice_base.run_date, steelprice_info.the_price_tax
            FROM `steelprice_base` , steelprice_info
            WHERE steelprice_base.id = steelprice_info.sb_id
            AND steel_id =1193
            AND steelprice_base.is_show =2
            AND run_date >= '".$sdate."'
            AND run_date <= '".$edate."'
            AND steelprice_info.specification LIKE '2.75mm%' ";
	    $info = $this->gcDao->query($sql);
        $data = array();
        foreach($info as $key=>$tmp){
            $date = date("Y-m",strtotime($tmp['run_date']));
            $data[$date] = $tmp['the_price_tax'];
        }


        echo json_encode($data);
        exit;
    }
	//added by hezpeng for sgjcxsjg ended 2021/01/14
	//xiangbin add 2000115 start 根据指定价格id获取 指定日期数据
	public function get_price_data($params){

	    $sdate = $params['date'];
		$pricestr = $params['pricestr'];
		$this->homeDao->getpricebypricestr($pricestr,$date);
		echo json_encode($data);
        exit;

	}
	//xiangbin add 2000115 end

    /**
     * 自动生成市场综述
     * 每周四晚上20:00开始，每半小时扫描普氏指数（id：H98640），直至有数据时，自动生成文档，并保存。
     * Created by zfy.
     * Date:2021/1/25 15:38
     */
    public function create_content_by_price_id($params)
    {
        $article_type = 21;
        $edate = $params['edate']==''?date("Y-m-d"):$params['edate'];
        $sdate = $params['sdate']==''?date("Y-m-d",strtotime('last Friday',strtotime($edate))):$params['sdate'];
        $PriceIdList = array('392023','072023','562023','532023','572023','372023','676011','678411','678311','666310','438210','758210','118210','678210','548910','418910','6683112','M163302','5989101');
        $plattsId = "H98640";
        $week = array("0"=>"周日","1"=>"周一","2"=>"周二","3"=>"周三","4"=>"周四","5"=>"周五","6"=>"周六");
        //普氏价格是否存在
        $isPlattsPrice = $this->homeDao->isPlattsPrice($edate,$plattsId);

        if ($isPlattsPrice){
            $listData = array();
            //全国三级大螺纹网价均价（钢之家28个城市三级螺纹钢平均价格）
            $lwgThisData = $this->homeDao->getMarketconditionQgprice($edate);
            $lwgLastData = $this->homeDao->getMarketconditionQgprice($sdate);
            $listData['lwg_data']['this_data'] = $lwgThisData;
            $listData['lwg_data']['last_data'] = $lwgLastData;
            $listData['lwg_data']['up_down_1'] = $this->getUpDown($lwgThisData,$lwgLastData,1);
            $listData['lwg_data']['up_down_2'] = $this->getUpDown($lwgThisData,$lwgLastData,2);
            //行情价格
            $thisData = $this->homeDao->getpricebypricestr(implode(',',$PriceIdList),$edate);
            $lastData = $this->homeDao->getpricebypricestr(implode(',',$PriceIdList),$sdate);
            foreach ($thisData as $index => $thisDatum) {
                $listData[$index]['this_data'] = $thisDatum;
                $listData[$index]['last_data'] = $lastData[$index];
                $listData[$index]['up_down_1'] = $this->getUpDown($thisDatum,$lastData[$index],1);
                $listData[$index]['up_down_2'] = $this->getUpDown($thisDatum,$lastData[$index],2);
            }
            //普氏均价
            $plattsData = $this->homeDao->getAvgPlattsPrice($plattsId,$sdate,$edate);
            foreach ($plattsData as $index => &$plattsDatum) {
                $plattsDatum['up_down_3'] = $this->getUpDown($plattsDatum['this_data'],$plattsDatum['last_data'],3);
            }
            $listData = $listData+$plattsData;
            $dateStr = "本".$week[date('w',strtotime($edate))]."(".date('n月j日',strtotime($edate)).")";
            $content = $this->getMarketOverview($listData,$isPlattsPrice,$dateStr);
            $arr['success'] = 1;
            $arr['content'] = base64_encode($content);
            $arr['message'] = base64_encode("生成成功");
//            $this->homeDao->insert_public_articles($content,$edate,$article_type);
//            echo $content;
//            exit();
        }else{
            $arr['success'] = 0;
            $arr['content'] = '';
            $arr['message'] = base64_encode("普氏无数据");
        }
        echo $this->pri_JSON($arr);
    }


    /**
     * 获取市场综述文章
     * Created by zfy.
     * Date:2021/1/27 10:47
     * @param $listData
     * @param $isPlattsPrice
     * @param $dateStr
     * @return string
     */
    protected function getMarketOverview($listData,$isPlattsPrice,$dateStr){
        $content = "";
        //螺纹钢
        $content.= "<p style=\"text-indent: 2em;\">一、螺纹钢</p>";
        $content.= "<p style=\"text-indent: 2em;\">截至".$dateStr."全国三级大螺纹网价均价".$listData['lwg_data']['this_data']."元/吨，周环比".$listData['lwg_data']['up_down_2']."。其中：北京".$listData['392023']['this_data']."元/吨".$listData['392023']['up_down_1']."，上海".$listData['072023']['this_data']."元/吨".$listData['072023']['up_down_1']."，西安".$listData['562023']['this_data']."元/吨".$listData['562023']['up_down_1']."，成都".$listData['532023']['this_data']."元/吨".$listData['532023']['up_down_1']."，兰州".$listData['572023']['this_data']."元/吨".$listData['572023']['up_down_1']."，郑州".$listData['372023']['this_data']."元/吨".$listData['372023']['up_down_1']."。</p>";
        //带钢
        $content.= "<p style=\"text-indent: 2em;\">二、带钢</p>";
        $content.= "<p style=\"text-indent: 2em;\">截至".$dateStr."，唐山瑞丰窄带报价".$listData['676011']['this_data']."元/吨，周环比".$listData['676011']['up_down_2']."；唐山钢坯报价".$listData['678411']['this_data']."元/吨，周环比".$listData['678411']['up_down_2']."。</p>";
        //铁矿石
        $content.= "<p style=\"text-indent: 2em;\">三、铁矿石</p>";
        $content.= "<p style=\"text-indent: 2em;\">截至".$dateStr."，普氏62%铁精粉指数均价".round($listData['H98640']['this_data'],2)."美元，周环比".$listData['H98640']['up_down_3']."，最新普氏62%铁精粉指数为".$isPlattsPrice."美元。</p>";
        //煤焦
        $content.= "<p style=\"text-indent: 2em;\">四、煤焦</p>";
        $content.= "<p style=\"text-indent: 2em;\">截至".$dateStr."，准一级焦：山西临汾".$listData['6683112']['this_data']."元/吨，周环比".$listData['6683112']['up_down_2']."；河北唐山".$listData['678311']['this_data']."元/吨，周环比".$listData['678311']['up_down_2']."。山西安泽炼焦煤".$listData['666310']['this_data']."元/吨, 周环比".$listData['666310']['up_down_2']."。山西晋城喷吹无烟煤".$listData['M163302']['this_data']."元/吨，周环比".$listData['M163302']['up_down_2']."。</p>";
        //废钢
        $content.= "<p style=\"text-indent: 2em;\">五、废钢</p>";
        $content.= "<p style=\"text-indent: 2em;\">截至".$dateStr."，6mm重废：山西吕梁".$listData['438210']['this_data']."元/吨，周环比".$listData['438210']['up_down_2'].";四川成都".$listData['758210']['this_data']."元/吨，周环比".$listData['758210']['up_down_2']."；江苏张家港".$listData['118210']['this_data']."元/吨，周环比".$listData['118210']['up_down_2']."；河北唐山".$listData['678210']['this_data']."元/吨，周环比".$listData['678210']['up_down_2']."。</p>";
        //硅锰合金
        $content.= "<p style=\"text-indent: 2em;\">六、硅锰合金</p>";
        $content.= "<p style=\"text-indent: 2em;\">截至".$dateStr."，硅锰（6818）：宁夏".$listData['5989101']['this_data']."元/吨，周环比".$listData['5989101']['up_down_2']."；贵州".$listData['548910']['this_data']."元/吨，周环比".$listData['548910']['up_down_2']."；内蒙古".$listData['418910']['this_data']."元/吨，周环比".$listData['418910']['up_down_2']."。</p>";

        return $content;
    }

    /**
     * 获取本期与上期的涨跌
     * Created by zfy.
     * Date:2021/1/26 14:43
     * @param $thisNum
     * @param $lastNum
     * @param $type 1:箭头  2：文字
     * @return string
     */
    protected function getUpDown($thisNum, $lastNum, $type)
    {
        $up_down = $thisNum - $lastNum;
        $abs_up_down = abs($up_down);
        $ret = "";
        if ($up_down > 0) {
            switch ($type){
                case '1':
                    $ret = "<span style=\"color: red\">↑$abs_up_down</span>元/吨";
                    break;
                case '2':
                    $ret = "上涨" . $abs_up_down . "元/吨";
                    break;
                case '3':
                    $ret = "上涨" . round($abs_up_down,2) . "美元";
                    break;
            }
        } elseif ($up_down < 0) {
            switch ($type){
                case '1':
                    $ret = "<span style=\"color: green\">↓$abs_up_down</span>元/吨";
                    break;
                case '2':
                    $ret = "下跌" . $abs_up_down . "元/吨";
                    break;
                case '3':
                    $ret = "下跌" . $abs_up_down . "美元";
                    break;
            }
        } else {
            $ret = "持平";
        }
        return $ret;
    }

    /**
     * 中文json打包+转码
     * Created by zfy.
     * Date:2021/1/27 10:49
     * @param $array
     * @return string
     */
    private function pri_JSON($array)
    {
        $this->pri_arrayRecursive($array, 'urlencode', true);
        $json = json_encode($array);
        return urldecode($json);
    }

    private function pri_arrayRecursive(&$array, $function, $apply_to_keys_also = false)
    {
        static $recursive_counter = 0;
        if (++$recursive_counter > 1000) {
            die('possible deep recursion attack');
        }
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $this->pri_arrayRecursive($array[$key], $function, $apply_to_keys_also);
            } else {
                $array[$key] = $function($value);
            }
            if ($apply_to_keys_also && is_string($key)) {
                $new_key = $function($key);
                if ($new_key != $key) {
                    $array[$new_key] = $array[$key];
                    unset($array[$key]);
                }
            }
        }
        $recursive_counter--;
    }

	public  function get_gzj_price_avg($params)
	{
		$sdate=$params['sdate'];
		$edate=$params['edate'];
		$priceidstr=$params['priceidstr'];
		$priceid_arr = explode(",",$priceidstr);
		//print_r($priceid_arr );
		//echo $priceidstr;
		//getavgprice($sdate, $edate, $priceid_arr)
		$data = $this->getavgprice($sdate, $edate, $priceid_arr);
		echo json_encode($data);
	}

	public function get_gccg_price($params)
	{
		$sdate=$params['sdate'];
		$edate=$params['edate'];
		$gcid=$params['gcid'];
		$onlyid=$params['onlyid'];


		if(empty($sdate))
		{
			$sql="select the_price_tax from SteelCaiGou_Info where 1 and is_show=2 and the_price_tax!='' and the_price_tax!='0' AND gcid='".$gcid."' and onlyid='".$onlyid."' and ftime>='".$sdate."' and ftime<='".$edate."' order by  ftime desc  limit 1";
		}
		else
		{
			$sql="select the_price_tax from SteelCaiGou_Info where 1 and is_show=2 and the_price_tax!='' and the_price_tax!='0' AND gcid='".$gcid."' and onlyid='".$onlyid."' and ftime<='".$edate."' order by  ftime desc  limit 1";
		}
		
		//echo $sql;
		$ctarr2=$this->gcDao->getRow($sql);
		echo json_encode($ctarr2);
	}

	/*周环比*/
	function weekday($date){
		$wnum=date("w",strtotime($date));
		$weekd["be"]=$date;
		if ($wnum==0){
		   //$weekd["be"]=$date;
		   $weekd["bs"]=date("Y-m-d",strtotime ("-6 day $date"));
		   $weekd["se"]=date("Y-m-d",strtotime ("-7 day $date"));
		   $weekd["ss"]=date("Y-m-d",strtotime ("-13 day $date"));
		}else{
		   $jwn=7-$wnum;
		   $gwn=$wnum-1;
		   $weekd["be"]=date("Y-m-d",strtotime ("+$jwn day $date"));
		   $weekd["bs"]=date("Y-m-d",strtotime ("-$gwn day $date"));
		   //为了处理国庆节期间无数据，将日期强行推前1个星期
		   //$sgwn=$wnum+13;
		   $sgwn=$wnum+6;
		   $weekd["se"]=date("Y-m-d",strtotime ("-$wnum day $date"));
		   $weekd["ss"]=date("Y-m-d",strtotime ("-$sgwn day $date"));
		}
		//added by shizg for 国庆节数据处理 started 2017/10/10
		// $date2 = $weekd["ss"];
		// $ret = $this->isholiday($date2);
		// $needckecklastweek = 0;
		// while($date2<$weekd["se"]){
		// 	if(!$ret){
		// 		$needckecklastweek = 1;
		// 	}
		//    	$date2 = date("Y-m-d",strtotime($date2."+1 day"));
	   	// }
		// if($needckecklastweek==0){
		//    $weekd["se"]=date("Y-m-d",strtotime ($weekd["se"]."-7 day "));
		//    $weekd["ss"]=date("Y-m-d",strtotime ($weekd["ss"]."-7 day "));
		// }
		return $weekd;
	}
	/*月环比*/
	function monday($date){
		$mnum=date("n",strtotime($date));
		$y=date("Y",strtotime($date));
		if ($mnum==1){		    
		   $weekd["bs"]=$y."-01-01";
		   $weekd["be"]=$y."-01-".date("d",strtotime($date));
		   $weekd["ss"]=($y-1)."-12-01";
		   $weekd["se"]=($y-1)."-12-31";
		}else{
		   $weekd["bs"]=$y."-".date("m",strtotime($date))."-01";
		   $weekd["be"]=$y."-".date("m",strtotime($date))."-".date("d",strtotime($date));
		   $mnum1=$mnum-1;
		   if ($mnum1<10){$mnum1="0".$mnum1;}
		   $weekd["ss"]=$y."-".$mnum1."-01";
		   $weekd["se"]=$y."-".$mnum1."-31";
		}
		return $weekd;
	}
	/*月同比*/
	function monday_same($date)
	{
		if ((date("m",strtotime($date))-1)<0){	$yab=date("Y",strtotime($date))-1;$mab=12;}else{	$yab=date("Y",strtotime($date))-1;$mab=date("m",strtotime($date));}
	  	$semd=array("se"=>$yab.'-'.$mab.'-31',"ss" =>$yab.'-'.$mab.'-01',
	              "be" =>date("Y",strtotime($date)).'-'.date("m",strtotime($date)).'-'.date("d",strtotime($date)),
				  "bs" =>date("Y",strtotime($date)).'-'.date("m",strtotime($date)).'-01');
		return $semd;
	}
	/*季环比*/
	function season($date){
		$mnum=date("n",strtotime($date));
		$y=date("Y",strtotime($date));
		switch ($mnum){
		   case 1:
		   case 2:
		   case 3:
			  $weekd["bs"]=$y."-01-01";$weekd["be"]=$y."-03-".date("d",strtotime($date));$weekd["ss"]=($y-1)."-10-01";$weekd["se"]=($y-1)."-12-31";break;
		   case 4:
		   case 5:
		   case 6:
			 $weekd["bs"]=$y."-04-01";$weekd["be"]=$y."-06-".date("d",strtotime($date));$weekd["ss"]=$y."-01-01";$weekd["se"]=$y."-03-31";break;
		   case 7:
		   case 8:
		   case 9:
			 $weekd["bs"]=$y."-07-01";$weekd["be"]=$y."-09-".date("d",strtotime($date));$weekd["ss"]=$y."-04-01";$weekd["se"]=$y."-06-30";break;
		   case 10:
		   case 11:
		   case 12:
			 $weekd["bs"]=$y."-10-01";$weekd["be"]=$y."-12-".date("d",strtotime($date));$weekd["ss"]=$y."-07-01";$weekd["se"]=$y."-09-30";break; 
		}
		return $weekd;
	}
	/*年环比*/
	function yearday($date){
		$y=date("Y",strtotime($date));
		$weekd["bs"]=$y."-01-01";
		$weekd["be"]=$y."-12-".date("d",strtotime($date));
		$weekd["ss"]=($y-1)."-01-01";
		$weekd["se"]=($y-1)."-12-31";
		return $weekd;
	}
	/*年同比*/
	function yearday_same($date)
	{
		$seyy=(date("Y",strtotime($date))-1)."-".date("m",strtotime($date))."-".date("d",strtotime($date));
		$seyd=array( "se"=>$seyy,"ss" =>(date("Y",strtotime($date))-1).'-01-01',"be" =>date("Y-m-d",strtotime($date)),"bs" =>date("Y",strtotime($date)).'-01-01');
		return $seyd;
	}


    public function sg_report_list($params)
    {
        /*if($params['mode']=="2"){
            gourl("newtab:https://www.steelhome.cn/uploadfile/2021/3/20/jzgc3.19.pdf");exit;
        }*/
        //gourl("https://www.steelhome.cn/uploadfile/2021/3/20/jzgc3.19.pdf");//exit;
        $GUID = $params['GUID'];
        //$GUID = "2c61a12a49c511ebbebc001d09719b40";
        $SignCS = $params['SignCS'];
        //$SignCS = "8b46e267268907263bbec91ec65915f4";
        $mode = $params['mode'];
        $mc_type = $params['mc_type']=="" ? "2" : $params['mc_type'];
        $startdate=$params["startdate"];
        $enddate=$params["enddate"];

        $type = $params['type'];
        $AppMode = $params['AppMode'];

        $action = $params['action'];        //接口名称
        $ip = $this->getIP();
        //判断是不是测试账号
        if (array_key_exists($GUID, $GLOBALS["testaccountarr"]) && $SignCS != '' && (in_array($ip, $GLOBALS["testip"]) || $GLOBALS["nowtest"] == 1)) {
            $user = $GLOBALS["testaccountuser"];
        } else {
            $user = $this->t1Dao->getUser($GUID, $SignCS, $mc_type);
        }
        $mid = $user['Mid'];
        $uid = $user['Uid'];

        if($mc_type=="0"){
            $arr_title = array(
                "1"=>"周报 ",
                "2"=>"月报 ",
                "3"=>"年报 "
            );
        }else{
            $arr_title = array(
                "1"=>"研究报告 > 每周报告 ",
                "2"=>"研究报告 > 每月报告 ",
                "3"=>"研究报告 > 每年报告 "
            );
        }

        $title = $arr_title[$type];

        if($type){
            $where = " and ntype = '".$type."' ";
        }else{
            $where = " and 1!=1 ";
        }

        $where .= " and mc_type = '".$mc_type."' ";

        $where .= $startdate=="" ? "" : " and date_ym>='$startdate' ";
        $where .= $enddate=="" ? "" : " and date_ym<='$enddate' ";


        $url = "&get_pdf=1&mode=".$mode;


        $page=$this->formatpage($params["page"]);
        if($params['show']=="1"){
            $pagenum="1";//一页显示的条数
        }else{
            $pagenum="18";//一页显示的条数
        }

        $limit=" limit ".(($page-1)*$pagenum).",".$pagenum;

        $amount=$this->_dao->get_report_lists($where);

        $data = $this->_dao->get_report_list($where,$limit);
        foreach($data as $key=>&$tmp){

            $tmp['url'] = "sg_steelhome_price.php?action=get_news_pdf&GUID=".$GUID."&mc_type=".$mc_type."&mode=".$mode."&SignCS=".$SignCS."&id=".$tmp['id'];
            if($params['show']=="1"){
                gourl($tmp['url']);
                exit;
            }
        }

        $pagelabel=$this->getpagelabelnew($amount,$pagenum,$page,"sg_steelhome_price.php?view=".$params['view']."&GUID=".$GUID."&mc_type=".$mc_type."&mode=".$mode."&type=".$type );

        $arrtem = $params;
        $actionstr = "";
        foreach ($arrtem as $k => $v) {
            $actionstr .= "&" . $k . "=" . $v;
        }
        $this->t1Dao->WriteLog($mid, $uid, $SignCS, $action, $actionstr, $ip, "", "", "查看周报列表", '', '', $mc_type);

        //print_r($data);
        $this->assign("GUID",$GUID);
        $this->assign("title",$title);
        $this->assign("mode",$mode);
        $this->assign("mc_type",$mc_type);
        $this->assign("view",$params['view']);
        $this->assign("data",$data);
        $this->assign("params",$params);
        $this->assign("pagelabel",$pagelabel);
    }
    /**
     * 函数名称: getIP
     * 函数功能: 取得手机IP
     * 输入参数: none
     * 函数返回值: 成功返回string
     * 其它说明: 说明
     */
    private function getIP()
    {
        $ip = getenv('REMOTE_ADDR');
        $ip_ = getenv('HTTP_X_FORWARDED_FOR');
        if (($ip_ != "") && ($ip_ != "unknown")) {
            $ip = $ip_;
        }
        $ip = explode(",", $ip);
        return $ip[0];
    }

    public function get_news_pdf($params){
        $id = $params['id'];

        $mc_type = $params['mc_type'];
        $mode = $params['mode'];
        $GUID = $params['GUID'];
        $SignCS = $params['SignCS'];
        $action = $params['action'];		//接口名称

        $user = $this->t1Dao->getUser($GUID,$SignCS,$mc_type);
        $mid = $user['Mid'];
        $uid = $user['Uid'];

        $ip = $this->getIP();

        $path = $this->_dao->getReport($id);
        $url2 = "http://".STEELHOME_SITE_NOHTTP."/uploadfile/".$path;
        //$url2 = "http://idrc.in.steelhome.cn/".$path;

        //echo $url2;exit;

        //$title = "陕钢定制报告";
        //$this->t1Dao->WriteMemberLog($GUID, $SignCS,$mid,$user['ComName'],$title,$params["mode"],$mc_type, $action, $this->getIP());

        $arrtem = $params;
        $actionstr = "";
        foreach ($arrtem as $k => $v) {
            $actionstr .= "&" . $k . "=" . $v;
        }
        $this->t1Dao->WriteLog($mid, $uid, $SignCS, $action, $actionstr, $ip, "", "", "查看周报", '', '', $mc_type);

        if($mode=="2" || $mode=="3"){
            $url2 = "newtab:".$url2;
        }

        echo '<script>location.href = "'.$url2.'";</script>';
        exit;

    }

    private function formatpage($page){
        $page=(int)$page;
        if($page-1<0) $page=1;
        return $page;
    }

    private function getpagelabelnew($amount, $pagenum, $page, $url)
    {
        $pagemax = ($amount % $pagenum == 0) ? round($amount / $pagenum, 0) : (floor($amount / $pagenum) + 1);

        //echo $amount." ".$pagenum." ".$page." ".$pagemax." ".$url;
        $label = "<div class='flex justify-between flex-1 sm:hidden'>";
        if ($page == 1 || $pagemax == 0) {
            $label .= "<span class='relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default leading-5 rounded-md'>
                    
			上一页
		</span>";
        } else {
            $label .= "<a href='$url&page=" . ($page - 1) . "' class='relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 leading-5 rounded-md hover:text-gray-500 focus:outline-none focus:shadow-outline-blue focus:border-blue-300 active:bg-gray-100 active:text-gray-700 transition ease-in-out duration-150'>

			上一页
			</a>";
        }

        if ($page == $pagemax || $pagemax == 0) {
            $label .= "<span class='relative inline-flex items-center px-4 py-2 ml-3 text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default leading-5 rounded-md'>下一页</span>";
        } else {
            $label .= "<a  href='$url&page=" . ($page + 1) . "'class='relative inline-flex items-center px-4 py-2 ml-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 leading-5 rounded-md hover:text-gray-500 focus:outline-none focus:shadow-outline-blue focus:border-blue-300 active:bg-gray-100 active:text-gray-700 transition ease-in-out duration-150'>

			下一页
			</a>";
        }
        $label .= "</div>";
        $ye = 10;
        $label .= "<div class='hidden sm:flex-1 sm:flex sm:items-center sm:justify-between'>
		<div>
		<p class='text-sm text-gray-700 leading-5'>
		Showing
		<span class='font-medium'>16</span>
		to
		<span class='font-medium'>30</span>
		of
		<span class='font-medium'>41</span>
		results
		</p>
		</div>";

        if ($page == 1 || $pagemax == 0) {//第一页
            $label .= "<div><span class='relative z-0 inline-flex shadow-sm rounded-md'><span aria-disabled='true' aria-label='&amp;laquo; Previous'>
				<span class='relative inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default rounded-l-md leading-5' aria-hidden='true'>
					<svg class='w-5 h-5' fill='currentColor' viewBox='0 0 20 20'>
						<path fill-rule='evenodd' d='M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z' clip-rule='evenodd'></path>
					</svg>
				</span>
			</span>";
        } else {
            $label .= "<div><span class='relative z-0 inline-flex shadow-sm rounded-md'><a href='$url&page=" . ($page - 1) . "' rel='prev' class='relative inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-l-md leading-5 hover:text-gray-400 focus:z-10 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue active:bg-gray-100 active:text-gray-500 transition ease-in-out duration-150' aria-label='&amp;laquo; Previous' ><svg class='w-5 h-5' fill='currentColor' viewBox='0 0 20 20'>
			<path fill-rule='evenodd' d='M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z' clip-rule='evenodd' />
		</svg></a>";
        }
        if ($pagemax == 0) {
            $label .= "<span aria-current='page'><span class='relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default leading-5'>1</span></span>";
        } else {
            if ($page == 1) {
                $label .= "<span aria-current='page'><span class='relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default leading-5'>1</span></span>";
            } else {
                $label .= "<a href='$url&page=1' class='relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-gray-700 bg-white border border-gray-300 leading-5 hover:text-gray-500 focus:z-10 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue active:bg-gray-100 active:text-gray-700 transition ease-in-out duration-150' aria-label='Go to page 1'>1</a>";
            }
            $label1 = "<span class='relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-gray-700 bg-white border border-gray-300 leading-5 hover:text-gray-500 focus:z-10 focus:outline-none focus:border-blue-300  transition ease-in-out duration-150'>...</span>";

            for ($i = 2; $i < $pagemax; $i++) {
                $label2 = "<span aria-current='page'><span class='relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default leading-5'>$i</span></span>";
                $label3 = "<a href='$url&page=$i' class='relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-gray-700 bg-white border border-gray-300 leading-5 hover:text-gray-500 focus:z-10 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue active:bg-gray-100 active:text-gray-700 transition ease-in-out duration-150' aria-label='Go to page $i'>$i</a>";

                if ($page < $ye - 1) {//前10，
                    if ($i > $ye) continue;
                    if ($i == $ye) {
                        $label .= $label1;
                    } elseif ($i == $page) {
                        $label .= $label2;
                    } else {
                        $label .= $label3;
                    }
                } else if ($pagemax - $page < $ye - 1) {//后10
                    if ($i < $pagemax - $ye) continue;
                    if ($i == $pagemax - $ye) {
                        $label .= $label1;
                    } elseif ($i == $page) {
                        $label .= $label2;
                    } else {
                        $label .= $label3;
                    }
                } else {//中间数
                    if ($i < $page - 2 || $i > $page + $ye - 2) continue;
                    if ($i == $page - 2 || $i == $page + $ye - 2) {
                        $label .= $label1;
                    } elseif ($i == $page) {
                        $label .= $label2;
                    } else {
                        $label .= $label3;
                    }
                }

            }
        }

        if ($pagemax > 1) {
            if ($pagemax != $page)
                $label .= "<a href='$url&page=$pagemax'  class='relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-gray-700 bg-white border border-gray-300 leading-5 hover:text-gray-500 focus:z-10 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue active:bg-gray-100 active:text-gray-700 transition ease-in-out duration-150' aria-label='Go to page $pagemax'>$pagemax</a>"; //最后一页
            else
                $label .= "<span aria-current='page'><span class='relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default leading-5'>$page</span></span>";
        }
        if ($page == $pagemax || $pagemax == 0) {
            $label .= "<span aria-disabled='true' aria-label='Next &amp;raquo;'>
				<span class='relative inline-flex items-center px-2 py-2 -ml-px text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default rounded-r-md leading-5' aria-hidden='true'>
					<svg class='w-5 h-5' fill='currentColor' viewBox='0 0 20 20'>
						<path fill-rule='evenodd' d='M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z' clip-rule='evenodd' />
					</svg>
				</span>
			</span>";
        } else {
            $label .= "<a href='" . $url . "&page=" . ($page + 1) . "' rel='next' class='relative inline-flex items-center px-2 py-2 -ml-px text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-r-md leading-5 hover:text-gray-400 focus:z-10 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue active:bg-gray-100 active:text-gray-500 transition ease-in-out duration-150' aria-label='Next &amp;raquo;'>
				<svg class='w-5 h-5' fill='currentColor' viewBox='0 0 20 20'>
					<path fill-rule='evenodd' d='M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z' clip-rule='evenodd'></path>
				</svg></a>";
        }

        $label .= "</span></div>";

        return "<div align='right' style='margin-top:5px; font-size:12px'> <nav role='navigation' aria-label='Pagination Navigation' class='flex items-center justify-between'>" . $label . "</nav></div>";
    }

}

?>