<?php
include_once("xg_price_ycAction.inc.php");
/**
 * 使用这个类之前先去controller中将view或者action的值添加到$_other_view_action变量中
 */
class otherAction extends xg_price_ycAction
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 获取并保存生产日报数据
     */
    public function getAndSaveScrbData($req) {
        // 获取新钢内网提供的数据（生产情况数据、产成品库存数据、中间品库存数据、原燃料库存数据）
        $types = array("GetScqkData","GetCcpkcData","GetZjpkcData","GetYrlkcData");
        $type = $req["scrbType"];
        $ymd = $req["ymd"];
        if(empty($ymd)) {
            $ymd = date("Y-m-d", strtotime("-1 day"));
        }
        if($type == "all") {
            $this->scrb("GetScqkData", $ymd);
            $this->scrb("GetCcpkcData", $ymd);
            $this->scrb("GetZjpkcData", $ymd);
            $this->scrb("GetYrlkcData", $ymd);
            exit;
        } else if(in_array($type, $types)){
            $this->scrb($type, $ymd);
            exit;
        } else {
            echo "type error";
            exit;
        }
    }

    private function scrb($type, $ymd) {
        $targetUrl = urlencode("http://10.70.0.12:8098/api/GetScrbData/{$type}?pushProdData=".$ymd);
        // 此处只能以新钢正式服务器作为中间代理请求内网地址
        $url = "https://218.87.96.135:14007/api/getJsonByUrl?url={$targetUrl}&method=post";
        $content = file_get_contents($url, false, stream_context_create(array("ssl" => array("verify_peer"=>false,"verify_peer_name"=>false))));
        // print_r(iconv("utf-8","gbk",$content));
        $data = array();
        if(!empty($content)) {
            $content = json_decode($content, true);
            $content = json_decode($content, true);
            $olddata = array();
            if(!empty($content['pushDate']))
            $olddata = $this->array_iconv($content['pushDate']);
            foreach($olddata as $olddata_val) {
                $data[$olddata_val['PM']] = $olddata_val;
            }
        } else {
            echo "no data";
            exit;
        }
        if($type=="GetScqkData") {
            $this->xg->execute("update prodDataForDailyBase set isdel=1 where data_date='{$ymd}' and data_type=1 and isdel=0 limit 1");
            $sql = "insert into prodDataForDailyBase set data_date='{$ymd}',data_type=1,createtime=now(),isdel=0";
            $this->xg->execute($sql);
            $baseid = $this->xg->insert_id();
            $sql = "insert into prodDataForDailyDetail (baseid,pm,yjhcl,rjhcl,rwccl,yljcl,nljcl,yljjhb) values ";
            $valsql = "";
            foreach($data as $val) {
                $valsql .= "('{$baseid}','{$val['PM']}','{$val['YJHCL']}','{$val['RJHCL']}','{$val['RWCCL']}','{$val['YLJCL']}','{$val['NLJCL']}','{$val['YLJJHB']}'),";
            }
            $valsql = rtrim($valsql, ",");
            $sql .= iconv("gbk", "utf-8", $valsql);
            $this->xg->execute("set names utf8");
            $this->xg->execute($sql);
        } else {
            // "GetCcpkcData","GetZjpkcData","GetYrlkcData"
            $data_type = 1;
            if($type=="GetCcpkcData") {
                $data_type = 2;
            } else if($type=="GetZjpkcData") {
                $data_type = 3;
            }
            $this->xg->execute("update inventoryDataBase set isdel=1 where data_date='{$ymd}' and data_type={$data_type} and isdel=0 limit 1");
            $sql = "insert into inventoryDataBase set data_date='{$ymd}',data_type={$data_type},createtime=now(),isdel=0";
            $this->xg->execute($sql);
            $baseid = $this->xg->insert_id();
            if($type=="GetCcpkcData" || $type=="GetZjpkcData") {
                $sql = "insert into inventoryDataDetail (baseid,pm,kcsl,dekc,kcpc) values ";
                $valsql = "";
                foreach($data as $val) {
                    $valsql .= "('{$baseid}','{$val['PM']}','{$val['DRKC']}','{$val['DEKC']}','{$val['PCZ']}'),";
                }
            } else {
                $sql = "insert into inventoryDataDetail (baseid,pm,kcsl,dekc,kcpc,dezzts,sjzzts) values ";
                $valsql = "";
                foreach($data as $val) {
                    $valsql .= "('{$baseid}','{$val['PM']}','{$val['DQKC']}','{$val['DEKC']}','{$val['PCZ']}','{$val['DEZZTS']}','{$val['SJZZTS']}'),";
                }
            }
            $valsql = rtrim($valsql, ",");
            $sql .= iconv("gbk", "utf-8", $valsql);
            $this->xg->execute("set names utf8");
            $this->xg->execute($sql);
        }
        echo $type."：success！";
    }

    public function productInventory($req) {
        $req['edate'] = $req['edate'] ? $req['edate'] : date("Y-m-d", strtotime("-1 day"));
        $req['data_type'] = $req['data_type'] ? (int)$req['data_type'] : 2;
        $ymd = date("Y-m-d", strtotime($req['edate']));
        $type = $req['data_type'] ? (int)$req['data_type'] : 2;
        $thisTable = array();
        $tbody = "<tbody>";
        if($type==2) {
            $thisTable = array(
                "厚板特钢事业部" => array(
                    "中板库" => array(),
                    "厚板库" => array(),
                    "棒一库" => array(),
                    "棒二库" => array(),
                    "高线库" => array(),
                    "板加预处理板库" => array(),
                    "板加非计划板库" => array(),
                    "板加非计划卷库" => array(),
                    "板加仓储板库" => array(),
                    "板加配送棒材库" => array(),
                    "板加配送线材库" => array(),
                    "小计" => array(),
                ),
                "硅钢薄板事业部" => array(
                    "热轧库" => array(),
                    "酸轧中间库" => array(),
                    "连退库" => array(),
                    "优特钢带库" => array(),
                    "新钢新材库" => array(),
                    "小计" => array(),
                ),
                "金属制品事业部" => array(
                    "新华新材料库" => array(),
                    "新钢金属库" => array(),
                    "小计" => array(),
                ),
                "公司外产成品库" => array(
                    "长沙热卷库" => array(),
                    "新余线棒材库" => array(),
                    "长沙线棒材库" => array(),
                    "衡阳线棒材库" => array(),
                    "广州线棒材库" => array(),
                    "贵阳线棒材库" => array(),
                    "小计" => array(),
                ),
                "合计" => array(),
            );
            $kcdata = parent::getScrbData('GetCcpkcData', $ymd);
            foreach($kcdata as $val) {
                $t = array();
                $t = explode("-", $val['PM']);
                if(isset($thisTable[$t[0]][$t[1]]))
                $thisTable[$t[0]][$t[1]] = $val;
            }
            foreach($thisTable as $bigPm => $pzArray) {
                if($bigPm == '合计') continue;
                $thisTable[$bigPm]['小计'] = array('DRKC' => 0, 'DEKC' => 0, 'PCZ' => 0);
                foreach($pzArray as $smallPm => $valArray) {
                    if($smallPm == '小计') continue;
                    $thisTable[$bigPm][$smallPm]['DRKC'] = round((float)$valArray['DRKC'], 0);
                    $thisTable[$bigPm][$smallPm]['DEKC'] = round((float)$valArray['DEKC'], 0);
                    $thisTable[$bigPm][$smallPm]['PCZ'] = round((float)$valArray['PCZ'], 0);
                    $thisTable[$bigPm][$smallPm]['YJZ'] = round((float)$valArray['YJZ'], 0);
                    if($thisTable[$bigPm][$smallPm]['DRKC']<0){
                        $thisTable[$bigPm][$smallPm]['DRKC'] = 0;
                        $thisTable[$bigPm][$smallPm]['PCZ'] = round($thisTable[$bigPm][$smallPm]['DRKC'] - $thisTable[$bigPm][$smallPm]['DEKC'], 0);
                    }
                    $thisTable[$bigPm]['小计']['DRKC'] += $thisTable[$bigPm][$smallPm]['DRKC'];
                    $thisTable[$bigPm]['小计']['DEKC'] += $thisTable[$bigPm][$smallPm]['DEKC'];
                    $thisTable[$bigPm]['小计']['PCZ'] += $thisTable[$bigPm][$smallPm]['PCZ'];
                    $thisTable[$bigPm]['小计']['YJZ'] += $thisTable[$bigPm][$smallPm]['YJZ'];
                    unset($thisTable[$bigPm][$smallPm]['PM']);
                }
                $thisTable["合计"]['DRKC'] += $thisTable[$bigPm]['小计']['DRKC'];
                $thisTable["合计"]['DEKC'] += $thisTable[$bigPm]['小计']['DEKC'];
                $thisTable["合计"]['PCZ'] += $thisTable[$bigPm]['小计']['PCZ'];
                $thisTable["合计"]['YJZ'] += $thisTable[$bigPm]['小计']['YJZ'];
            }
        } else if ($type==3) {
            $thisTable = array(
                "厚板特钢事业部" => array(
                    "钢一区坯料库" => array(),
                    "棒一坯料库" => array(),
                    "棒二坯料库" => array(),
                    "高线坯料库" => array(),
                    "中板坯料库" => array(),
                    "厚板坯料库" => array(),
                    "坯料-小计" => array(),
                    "中板中间品库" => array(),
                    "厚板中间品库" => array(),
                    "板加预处理原料库" => array(),
                    "中间品-小计" => array(),
                    "合计" => array(),
                ),
                "硅钢薄板事业部" => array(
                    "钢二区坯料" => array(),
                    "热轧坯料库" => array(),
                    "坯料-小计" => array(),
                    "酸轧原料卷库" => array(),
                    "连退原料卷库" => array(),
                    "优钢酸洗原料卷库" => array(),
                    "优钢中间品库" => array(),
                    "硅钢连退原料卷库" => array(),
                    "硅钢中间品库" => array(),
                    "中间品-小计" => array(),
                    "合计" => array(),
                ),
                "金属制品事业部" => array(
                    "新华新材料原料库" => array(),
                    "新钢金属原料库" => array(),
                    "小计" => array(),
                ),
                "坯料" => array(),
                "中间品" => array(),
                "总计" => array()
            );
            $kcdata = parent::getScrbData('GetZjpkcData', $ymd);
            foreach($kcdata as $val) {
                $t = array();
                $t = explode("-", $val['PM']);
                if(isset($thisTable[$t[0]][$t[1]]))
                $thisTable[$t[0]][$t[1]] = $val;
            }

            foreach($thisTable as $bigPm => $pzArray) {
                if($bigPm == "金属制品事业部") {
                    $thisTable[$bigPm]['小计'] = array('DRKC' => 0, 'DEKC' => 0, 'PCZ' => 0);
                    foreach($pzArray as $smallPm => $valArray) {
                        if($smallPm == '小计') continue;
                        $thisTable[$bigPm][$smallPm]['DRKC'] = round((float)$valArray['DRKC'], 0);
                        $thisTable[$bigPm][$smallPm]['DEKC'] = round((float)$valArray['DEKC'], 0);
                        $thisTable[$bigPm][$smallPm]['PCZ'] = round((float)$valArray['PCZ'], 0);
                        $thisTable[$bigPm][$smallPm]['YJZ'] = round((float)$valArray['YJZ'], 0);
                        if($thisTable[$bigPm][$smallPm]['DRKC']<0){
                            $thisTable[$bigPm][$smallPm]['DRKC'] = 0;
                            $thisTable[$bigPm][$smallPm]['PCZ'] = round($thisTable[$bigPm][$smallPm]['DRKC'] - $thisTable[$bigPm][$smallPm]['DEKC'], 0);
                        }
                        $thisTable[$bigPm]['小计']['DRKC'] += $thisTable[$bigPm][$smallPm]['DRKC'];
                        $thisTable[$bigPm]['小计']['DEKC'] += $thisTable[$bigPm][$smallPm]['DEKC'];
                        $thisTable[$bigPm]['小计']['PCZ'] += $thisTable[$bigPm][$smallPm]['PCZ'];
                        $thisTable[$bigPm]['小计']['YJZ'] += $thisTable[$bigPm][$smallPm]['YJZ'];
                        unset($thisTable[$bigPm][$smallPm]['PM']);
                    }
                } else if ($bigPm == "厚板特钢事业部" || $bigPm == "硅钢薄板事业部") {
                    $thisTable[$bigPm]['坯料-小计'] = array('DRKC' => 0, 'DEKC' => 0, 'PCZ' => 0);
                    $thisTable[$bigPm]['中间品-小计'] = array('DRKC' => 0, 'DEKC' => 0, 'PCZ' => 0);
                    $thisTable[$bigPm]['合计'] = array('DRKC' => 0, 'DEKC' => 0, 'PCZ' => 0);
                    foreach($pzArray as $smallPm => $valArray) {
                        if($smallPm == '中间品-小计' || $smallPm == '坯料-小计' || $smallPm == '合计') continue;
                        $thisTable[$bigPm][$smallPm]['DRKC'] = round((float)$valArray['DRKC'], 0);
                        $thisTable[$bigPm][$smallPm]['DEKC'] = round((float)$valArray['DEKC'], 0);
                        $thisTable[$bigPm][$smallPm]['PCZ'] = round((float)$valArray['PCZ'], 0);
                        $thisTable[$bigPm][$smallPm]['YJZ'] = round((float)$valArray['YJZ'], 0);
                        if($thisTable[$bigPm][$smallPm]['DRKC']<0){
                            $thisTable[$bigPm][$smallPm]['DRKC'] = 0;
                            $thisTable[$bigPm][$smallPm]['PCZ'] = round($thisTable[$bigPm][$smallPm]['DRKC'] - $thisTable[$bigPm][$smallPm]['DEKC'], 0);
                        }
                        if(stripos($smallPm, "坯料") !== false) {
                            $thisTable[$bigPm]['坯料-小计']['DRKC'] += $thisTable[$bigPm][$smallPm]['DRKC'];
                            $thisTable[$bigPm]['坯料-小计']['DEKC'] += $thisTable[$bigPm][$smallPm]['DEKC'];
                            $thisTable[$bigPm]['坯料-小计']['PCZ'] += $thisTable[$bigPm][$smallPm]['PCZ'];
                            $thisTable[$bigPm]['坯料-小计']['YJZ'] += $thisTable[$bigPm][$smallPm]['YJZ'];
                        } else {
                            $thisTable[$bigPm]['中间品-小计']['DRKC'] += $thisTable[$bigPm][$smallPm]['DRKC'];
                            $thisTable[$bigPm]['中间品-小计']['DEKC'] += $thisTable[$bigPm][$smallPm]['DEKC'];
                            $thisTable[$bigPm]['中间品-小计']['PCZ'] += $thisTable[$bigPm][$smallPm]['PCZ'];
                            $thisTable[$bigPm]['中间品-小计']['YJZ'] += $thisTable[$bigPm][$smallPm]['YJZ'];
                        }
                        $thisTable[$bigPm]['合计']['DRKC'] += $thisTable[$bigPm][$smallPm]['DRKC'];
                        $thisTable[$bigPm]['合计']['DEKC'] += $thisTable[$bigPm][$smallPm]['DEKC'];
                        $thisTable[$bigPm]['合计']['PCZ'] += $thisTable[$bigPm][$smallPm]['PCZ'];
                        $thisTable[$bigPm]['合计']['YJZ'] += $thisTable[$bigPm][$smallPm]['YJZ'];
                        unset($thisTable[$bigPm][$smallPm]['PM']);
                    }
                }
                if(isset($thisTable[$bigPm]['合计']['DRKC'])) {
                    $thisTable["总计"]['DRKC'] += $thisTable[$bigPm]['合计']['DRKC'];
                    $thisTable["总计"]['DEKC'] += $thisTable[$bigPm]['合计']['DEKC'];
                    $thisTable["总计"]['PCZ'] += $thisTable[$bigPm]['合计']['PCZ'];
                    $thisTable["总计"]['YJZ'] += $thisTable[$bigPm]['合计']['YJZ'];
                } else {
                    $thisTable["总计"]['DRKC'] += $thisTable[$bigPm]['小计']['DRKC'];
                    $thisTable["总计"]['DEKC'] += $thisTable[$bigPm]['小计']['DEKC'];
                    $thisTable["总计"]['PCZ'] += $thisTable[$bigPm]['小计']['PCZ'];
                    $thisTable["总计"]['YJZ'] += $thisTable[$bigPm]['小计']['YJZ'];
                }
                $thisTable["坯料"]['DRKC'] += $thisTable[$bigPm]['坯料-小计']['DRKC'];
                $thisTable["坯料"]['DEKC'] += $thisTable[$bigPm]['坯料-小计']['DEKC'];
                $thisTable["坯料"]['PCZ'] += $thisTable[$bigPm]['坯料-小计']['PCZ'];
                $thisTable["坯料"]['YJZ'] += $thisTable[$bigPm]['坯料-小计']['YJZ'];
                if(isset($thisTable[$bigPm]['小计']['DRKC'])) {
                    $thisTable["中间品"]['DRKC'] += $thisTable[$bigPm]['小计']['DRKC'];
                    $thisTable["中间品"]['DEKC'] += $thisTable[$bigPm]['小计']['DEKC'];
                    $thisTable["中间品"]['PCZ'] += $thisTable[$bigPm]['小计']['PCZ'];
                    $thisTable["中间品"]['YJZ'] += $thisTable[$bigPm]['小计']['YJZ'];
                } else {
                    $thisTable["中间品"]['DRKC'] += $thisTable[$bigPm]['中间品-小计']['DRKC'];
                    $thisTable["中间品"]['DEKC'] += $thisTable[$bigPm]['中间品-小计']['DEKC'];
                    $thisTable["中间品"]['PCZ'] += $thisTable[$bigPm]['中间品-小计']['PCZ'];
                    $thisTable["中间品"]['YJZ'] += $thisTable[$bigPm]['中间品-小计']['YJZ'];
                }
            }
        }
        // print_r($thisTable);
        foreach($thisTable as $bigPm => $pzArray) {
            if($bigPm == "总计" || $bigPm == "坯料" || $bigPm == "中间品" || $bigPm == "合计") {
                $tbody .= "<tr><td colspan='2'>$bigPm</td><td>{$pzArray['DRKC']}</td><td>{$pzArray['DEKC']}</td><td>{$pzArray['PCZ']}</td><td>{$pzArray['YJZ']}</td></tr>";
                continue;
            }
            $i = 0;
            foreach($pzArray as $smallPm => $valArray) {
                $color = "#000000";
                if($thisTable[$bigPm][$smallPm]['PCZ'] > 0) $color = "#FF0000";
                if($i == 0) {
                    $tbody .= "<tr><td rowspan='".(count($pzArray))."'>$bigPm</td><td>{$smallPm}</td><td>{$thisTable[$bigPm][$smallPm]['DRKC']}</td><td>{$thisTable[$bigPm][$smallPm]['DEKC']}</td><td style='color:{$color}'>{$thisTable[$bigPm][$smallPm]['PCZ']}</td><td>{$thisTable[$bigPm][$smallPm]['YJZ']}</td></tr>";
                } else {
                    $tbody .= "<tr><td>{$smallPm}</td><td>{$thisTable[$bigPm][$smallPm]['DRKC']}</td><td>{$thisTable[$bigPm][$smallPm]['DEKC']}</td><td style='color:{$color}'>{$thisTable[$bigPm][$smallPm]['PCZ']}</td><td>{$thisTable[$bigPm][$smallPm]['YJZ']}</td></tr>";
                }
                $i++;
            }
        }
        $tbody .= "<tbody>";
        $h1 = "产成品库存";
        if($type == 3) {
            $h1 = "中间品库存";
        }
        $this->assign("tbody", $tbody);
        $this->assign("req", $req);
        $this->assign("h1", $h1);
        $this->assign("datestr", date("n月j日", strtotime($ymd)));
    }

    // 价格对标 
    public function priceBenchmarking($req) {
        $GUID = $req['GUID']? $req['GUID'] : "";
        $req['mc_type'] = $mc_type = $req['mc_type']? $req['mc_type'] : "4";
        if(empty($GUID)){
            alert('GUID不能为空');
            exit;
        }
        $user_infos = $this->t1dao->get_userinfo_byguid($GUID);
        if(empty($user_infos)){
            alert('用户不存在');
            exit;
        }
        $do = $req['do']? $req['do'] : "";
        $pz = array(
            "0"=>array('name'=>'化产售价（硫酸铵）','db_dw'=>"湘钢",'xg_by'=>0,'xg_zd'=>0,'xg_nlj'=>0,'db_by'=>0,'db_nlj'=>0,'xg_db'=>0),
            "1"=>array('name'=>'水渣售价','db_dw'=>"湘钢",'xg_by'=>0,'xg_zd'=>0,'xg_nlj'=>0,'db_by'=>0,'db_nlj'=>0,'xg_db'=>0),
            "2"=>array('name'=>'异议联系响应时间','db_dw'=>"/",'xg_by'=>"0h",'xg_zd'=>"0h",'xg_nlj'=>"0h",'db_by'=>"/",'db_nlj'=>"/",'xg_db'=>"/"),
            "3"=>array('name'=>'中厚板售价','db_dw'=>"湘钢",'xg_by'=>0,'xg_zd'=>0,'xg_nlj'=>0,'db_by'=>0,'db_nlj'=>0,'xg_db'=>0),
            "4"=>array('name'=>'热卷售价','db_dw'=>"涟钢",'xg_by'=>0,'xg_zd'=>0,'xg_nlj'=>0,'db_by'=>0,'db_nlj'=>0,'xg_db'=>0),
            "5"=>array('name'=>'冷卷售价','db_dw'=>"涟钢",'xg_by'=>0,'xg_zd'=>0,'xg_nlj'=>0,'db_by'=>0,'db_nlj'=>0,'xg_db'=>0),
            "6"=>array('name'=>'线材售价','db_dw'=>"方大",'xg_by'=>0,'xg_zd'=>0,'xg_nlj'=>0,'db_by'=>0,'db_nlj'=>0,'xg_db'=>0),
            "7"=>array('name'=>'棒材售价','db_dw'=>"方大",'xg_by'=>0,'xg_zd'=>0,'xg_nlj'=>0,'db_by'=>0,'db_nlj'=>0,'xg_db'=>0),
            "8"=>array('name'=>'普板长沙售价','db_dw'=>"湘钢",'xg_by'=>0,'xg_zd'=>0,'xg_nlj'=>0,'db_by'=>0,'db_nlj'=>0,'xg_db'=>0),
            "9"=>array('name'=>'螺纹南昌售价','db_dw'=>"方大",'xg_by'=>0,'xg_zd'=>0,'xg_nlj'=>0,'db_by'=>0,'db_nlj'=>0,'xg_db'=>0),
            "10"=>array('name'=>'普卷长沙售价','db_dw'=>"涟钢",'xg_by'=>0,'xg_zd'=>0,'xg_nlj'=>0,'db_by'=>0,'db_nlj'=>0,'xg_db'=>0),
            "11"=>array('name'=>'冷卷华南售价','db_dw'=>"湛宝",'xg_by'=>0,'xg_zd'=>0,'xg_nlj'=>0,'db_by'=>0,'db_nlj'=>0,'xg_db'=>0),
            "12"=>array('name'=>'优钢杭州售价','db_dw'=>"衢州元立",'xg_by'=>0,'xg_zd'=>0,'xg_nlj'=>0,'db_by'=>0,'db_nlj'=>0,'xg_db'=>0),
        );
        $req['date'] = $date = $req['date']? $req['date'] : date("Y-m");
        $dateStr = date("Y年n月", strtotime($date));
        switch ($do) {
            case 'save':
                $xg_by = $req['xg_by'];
                $db_by = $req['db_by'];
                $now = date("Y-m-d H:i:s");
                $true = $user_infos['TrueName'];
                $dta_type = "XG-PRICEBENCHMARKING";
                $this->xg->execute("update data_table_base set isdel=1,deladminid='{$user_infos['Uid']}',deltime='$now' where sdate='{$date}-01' and type='6' and dta_type='$dta_type'");
                $sql = "insert into data_table_base set sdate='{$date}-01',createuser='{$user_infos['Uid']}',dta_type='$dta_type',type='6',createusername='{$true}',createtime='$now'";
                $this->xg->execute($sql);
                $baseid = $this->xg->insert_id();
                $sql = "insert into data_table (baseid,dta_type,dta1,dta2,dta3,dta4,createtime,createuser) values ";
                foreach($pz as $key => $val) {
                    $dta2 = $val['name'];
                    $dta3 = preg_replace('/[^0-9.]/', '', $xg_by[$key]);
                    $dta4 = preg_replace('/[^0-9.]/', '', $db_by[$key]);
                    $sql .= "($baseid, '$dta_type', '$key', '{$dta2}', '{$dta3}','{$dta4}', '$now', '{$user_infos['Uid']}'),";
                }
                $sql = rtrim($sql, ",");
                $this->xg->execute($sql);
                Alert("保存成功");
                goUrl("xg_price_yc.php?view={$req['view']}&GUID={$req['GUID']}&mode={$req['mode']}&date=".$date."&do=");
                break;
            default:
                $y = date("Y", strtotime($date));
                $curMonth = $date."-01";
                $lastMonth = date("Y-m-01", strtotime("-1 month", strtotime($date)));
                $sql = "select dtb.date,dt.dta1,dt.dta2,dt.dta3,dt.dta4 from data_table dt inner join (select id,sdate date from data_table_base where isdel=0 and type=6 and sdate<='{$date}-01' and sdate>='{$y}-01-01') dtb on dt.baseid=dtb.id ";
                $resdata = $this->xg->query($sql);
                $xg_one_year = array();
                $db_one_year = array();
                foreach($resdata as $val) {
                    $xg_one_year[$val['dta1']][$val['date']] = $val['dta3'];
                    $db_one_year[$val['dta1']][$val['date']] = $val['dta4'];
                }
                if(empty($xg_one_year)) break;
                $ys = count($xg_one_year[0]);
                if($ys==0) $ys = 1;
                foreach($pz as $dat1 => &$pzval) {
                    $ws = 0;
                    $wz = "";
                    if($dat1==2) {
                        $ws = 2;
                        $wz = "h";
                    }
                    $pzval['xg_by'] = ((float)$xg_one_year[$dat1][$curMonth]).$wz;
                    $pzval['xg_zd'] = ($xg_one_year[$dat1][$curMonth]-$xg_one_year[$dat1][$lastMonth]).$wz;
                    $pzval['xg_nlj'] = (round(array_sum($xg_one_year[$dat1])/$ys, $ws)).$wz;
                    if($pzval['db_dw']!="/"){
                        $pzval['db_by'] = ((float)$db_one_year[$dat1][$curMonth]);
                        $pzval['db_nlj'] = round(array_sum($db_one_year[$dat1])/$ys, $ws);
                        $pzval['xg_db'] = $xg_one_year[$dat1][$curMonth]-$db_one_year[$dat1][$curMonth];
                    }
                }
                unset($pzval);
                break;
        }
        $this->assign("req", $req);
        $this->assign("h1", "售价对标");
        $this->assign("datestr", $dateStr);
        $this->assign("tbody", "");
        $this->assign("bodydata", $pz);
    }

    public function loadWord($req) {
        // 25b782888b0c11ed8b5a001aa00de1ab
        global $checkUser;
        $GUID = $req['GUID']? $req['GUID'] : "";
        if(empty($GUID)){
            alert('GUID不能为空');
            exit;
        }
        $user_infos = $this->t1dao->get_userinfo_byguid($GUID);
        if(empty($user_infos)){
            alert('用户不存在');
            exit;
        }
        if(!in_array($user_infos['Uid'],$checkUser) && $user_infos['Mid']!=1){
            goURL("xg_price_jsw.php?view=nopower");
            exit();
        }
        if(isset($req['export']) && $req['export'] == "1") {
            $this->export_word($req);
            /*中文字号VS英文字号(磅)VS像素值的对应关系：
            八号＝5磅(5pt) ==(5/72)*96=6.67 =6px
            七号＝5.5磅 ==(5.5/72)*96=7.3 =7px
            小六＝6.5磅 ==(6.5/72)*96=8.67 =8px
            六号＝7.5磅 ==(7.5/72)*96=10px
            小五＝9磅 ==(9/72)*96=12px
            五号＝10.5磅 ==(10.5/72)*96=14px
            小四＝12磅 ==(12/72)*96=16px
            四号＝14磅 ==(14/72)*96=18.67 =18px
            小三＝15磅 ==(15/72)*96=20px
            三号＝16磅 ==(16/72)*96=21.3 =21px
            小二＝18磅 ==(18/72)*96=24px
            二号＝22磅 ==(22/72)*96=29.3 =29px
            小一＝24磅 ==(24/72)*96=32px
            一号＝26磅 ==(26/72)*96=34.67 =34px
            小初＝36磅 ==(36/72)*96=48px
            初号＝42磅 ==(42/72)*96=56px*/
        } else {

            $dayRange = $this->getDayRange();
            $sdate = $req['sdate'] ? $req['sdate']: $dayRange[0];
            $edate = $req['edate'] ? $req['edate']: $dayRange[1];

            if($req['muban'] == "1") {

            } else {
                // 结算文的默认日期需要往前推
                if($req['sdate']==""){
                    $dayRange = $this->getDayRange(date("Y-m-d", strtotime($sdate."-10 day")));
                    $sdate = $dayRange[0];
                    $edate = $dayRange[1];
                }
            }
            $req['sdate'] = $sdate;
            $req['edate'] = $edate;
            $dayRange = $this->getDayRange($sdate);
            $SYMD = date("Y年n月j日", strtotime($sdate));
            $SY = date("Y", strtotime($sdate));
            $SM = date("m", strtotime($sdate));
            $EMD = date("n月j日", strtotime($edate));
            $dateStr = date("n月j日", strtotime($sdate))."-".date("n月j日", strtotime($edate));
            
            $req['muban'] = $req['muban'] ? $req['muban'] : "1";
            $this->xg->execute("set names utf8");
            $title = "";
            $content = "";
            if($req['muban'] == "1") {
                $title = "价格表";
                $SM = date("m", strtotime($edate));
                $SY = date("Y", strtotime($edate));
                $one = '<p><span style="font-size: 16pt;font-family: 仿宋;">·内部资料 注意保管·</span></p><p><br></p><p><br></p><p><br></p><p style="text-align: center;font-size: 22pt;font-weight:600;">新余钢铁股份有限公司</p><p style="text-align: center;font-size: 22pt;font-weight:600;">主体产品销售价格表</p><p><br></p><p><br></p><p><br></p><p><br></p><p><br></p>';
                $one .= '<p style="text-align: center;font-size: 16pt;">价格表（'.$SY.'）'.$SM.''.$dayRange[2].'</p><p><br></p>';
                $one .= '<p style="text-align: center;font-size: 16pt;font-family: 仿宋;">（板、线棒）</p><p><br></p><p><br></p><p><br></p><p><br></p><p><br></p>';
                $one .= '<p style="text-align: center;font-size: 16pt;font-family: 仿宋;">经营财务部</p>';
                $one .= '<p style="text-align: center;font-size: 16pt;font-family: 仿宋;">'.$SYMD.'</p>';
                $content .= $one;
                $content .= '<p style="text-align: center;font-size: 14pt;"><br></p>';
                $content .= '<p style="text-align: center;font-size: 22pt;font-weight:300;font-family:方正小标宋简体">内贸产品销售价格</p>';
                // 一、中厚板销售价格
                $content .= '<h2 style="font-size: 16pt;font-weight:300;">一、中厚板销售价格</h2><p style="font-size: 16pt;">（一）中厚板（锁价模式）：</p>';
                $content .= '<p style="text-align: right;font-family: 仿宋;">单位：元/吨（含税）</p>';
                
                $content .= $this->getPriceTable(3, $req['muban'], $sdate, $edate);
                $content .= '<p style="text-align: center;font-size: 14pt;"><br></p>';
                $content .= '<p style="text-align: center;font-size: 14pt;"><br></p>';
                $content .= '<p style="text-indent: 2em;font-size: 14pt;line-height: 21pt;height:21pt;font-family: 仿宋;">说明：上表表列为20*2000*10000（mm）规格价格，技术加减价及其他价格规定按价格文执行。</p>';
                $content .= '<p style="text-align: center;font-size: 16pt;font-family: 仿宋;">'.$SYMD.'～'.$EMD.'接单锁价出厂价格</p>';
                $content .= '<p style="font-size: 16pt;">（二）中厚板普锰板销售包到价格表（后结算模式）：</p>';
                $content .= '<p style="text-align: right;">单位：元/吨（含税）</p>';
                $content .= $this->getPriceTable(4, $req['muban'], $sdate, $edate);
                $content .= '<p style="text-indent: 2em;font-size: 14pt;line-height: 21pt;height:21pt;font-family: 仿宋;">上表表列为20*2000*10000（mm）规格价格，技术加减价及其他价格规定按价格文执行。</p>';
                $content .= '<p style="text-align: center;font-size: 16pt;font-family: 仿宋;">'.$SYMD.'～'.$EMD.'接单指导包到价</p>';
                // 二、钢坯产品
                $content .= '<h2 style="font-size: 16pt;">二、钢坯产品（锁价模式）：</h2>';
                $content .= $this->getPriceTable(9, $req['muban'], $sdate, $edate);
                $content .= '<p style="text-indent: 2em;font-size: 14pt;height: 27pt;line-height: 27pt;font-family: 仿宋;">厚度加价：以厚度200mm～250mm为基价，厚度<200mm加价50元/吨，厚度250.1mm～300mm加50元/吨，厚度>300mm加600元/吨。</p>';
                $content .= '<p style="text-indent: 2em;font-size: 14pt;height: 27pt;line-height: 27pt;font-family: 仿宋;">非计划板坯Q195A/B、Q235A/B、Q345A/B、规格200-250mm的实行统价，价格按上表板坯Q195A/B、Q235A/B价格优惠100元/吨，并实行批量加减价，其批量加减价如下：</p>';
                $content .= '<p style="text-indent: 2em;font-size: 14pt;font-family: 仿宋;">采购量<1000吨的，加价20元/吨；</p>';
                $content .= '<p style="text-indent: 2em;font-size: 14pt;font-family: 仿宋;">采购量≥5000吨的，优惠20元/吨。</p>';
                $content .= '<p style="text-align: center;font-size: 16pt;font-family: 仿宋;">'.$SYMD.'～'.$EMD.'接单锁价出厂价格</p>';
                // 三、线、棒材销售价格
                $content .= '<h2 style="font-size: 16pt;">三、线、棒材销售价格</h2>';
                $content .= '<p style="font-size: 16pt;">(一)高线、拉丝线、盘螺、螺纹钢包到价格（后结算模式）（含税）:</p>';
                $content .= $this->getPriceTable(5, $req['muban'], $sdate, $edate);
                $content .= '<p style="text-align: center;font-size: 16pt;font-family: 仿宋;">'.$SYMD.'～'.$EMD.'接单指导包到价</p>';
                $content .= '<p style="text-indent: 2em;font-size: 14pt;font-family: 仿宋;">说明：</p>';
                $content .= '<p style="text-indent: 2em;font-size: 14pt;font-family: 仿宋;">(1)XQ235和HPB300同规格同价。</p>';
                // $content .= '<p style="text-indent: 2em;font-size: 14pt;font-family: 仿宋;">(2)南昌区域的站到门运输执行N+3定价模式。</p>';
                $content .= '<p style="text-indent: 2em;font-size: 14pt;font-family: 仿宋;">(2)螺纹钢以9m定尺，用户要求12m定尺的长沙区域加价20元/吨，江西区域加价50元/吨，要求其它定尺的加价50元/吨。</p>';
                $content .= '<p style="text-indent: 2em;font-size: 14pt;font-family: 仿宋;">(3)江西区域螺纹钢HRB400结算价与HRB400E同价，盘螺HRB400结算价与HRB400E同价。</p>';
                $content .= '<p style="text-indent: 2em;font-size: 14pt;font-family: 仿宋;">(4)HRB500E在HRB400E基础上加价300元/吨，湖南区域HRB500E在HRB400E基础上加价260元/吨，贵阳区域HRB500E在HRB400E基础上加价220元/吨。</p>';
                $content .= '<p style="text-indent: 2em;font-size: 14pt;font-family: 仿宋;">(5)HRB600E品种加价在HRB400E基础上加价600元/吨。</p>';
                $content .= '<p style="text-indent: 2em;font-size: 14pt;font-family: 仿宋;">(6)省内盘螺Φ12mm在Φ8-10mm基础上加价50元/吨，省外盘螺Φ12mm在Φ8-10mm基础上加价30元/吨。</p>';
                $content .= '<p style="text-indent: 2em;font-size: 14pt;font-family: 仿宋;">(7)长沙区域表列价格为磅计计重价，按磅计重量结算。其它区域为理计计重价,按理计重量结算。</p>';
                $content .= '<p style="font-size: 16pt;">（二）品种线材（锁价模式）：</p>';
                $content .= '<p style="text-align: center;font-size: 14pt;font-family: 仿宋;">'.$SYMD.'～'.$EMD.'接单锁价出厂价格：</p>';
                $content .= '<p style="text-align: right;">单位：元/吨（含税）</p>';
                $content .= $this->getPriceTable(6, $req['muban'], $sdate, $edate);
                $content .= '<p style="text-align: center;font-size: 14pt;"><br></p>';
                $content .= '<p style="text-align: center;font-size: 14pt;"><br></p>';
                $content .= '<p style="text-align: center;font-size: 14pt;"><br></p>';
                // $content .= '<p style="text-align: center;font-size: 14pt;"><br></p>';
                // $content .= '<p style="text-align: center;font-size: 14pt;"><br></p>';
                // 四、合金钢销售价格表
                $content .= '<h2 style="font-size: 16pt;">四、合金钢销售价格表（锁价模式）：</h2>';
                $content .= '<p style="text-indent: 2em;font-family: 仿宋;">合金钢指导价包含热处理交货状态和合金成本，规格为20*2000*10000mm，其它规格和性能要求加减价参照价格文中相关规定执行。</p>';
                $content .= '<p style="text-align: center;font-family: 仿宋;">'.$SYMD.'～'.$EMD.'接单锁价出厂价格：</p>';
                $content .= $this->getPriceTable(7, $req['muban'], $sdate, $edate);
                // 五、出口产品销售价格
                $content .= '<h2 style="font-size: 16pt;clear: left;font-family: 仿宋;">五、出口产品销售价格（锁价模式）：</h2>';
                $content .= '<p style="font-size: 16pt;">（一）中厚板</p>';
                $content .= '<p style="text-align: right;">单位：美元/吨</p>';
                $content .= $this->getPriceTable(8, $req['muban'], $sdate, $edate);
                $content .= '<p style="text-align: center;font-family: 仿宋;font-size: 14pt;">'.$SYMD.'～'.$EMD.'接单锁价出厂价格</p>';             
                $content .= '<p style="font-size: 16pt;">（二）线棒材</p>';
                $content .= $this->getPriceTable(10, $req['muban'], $sdate, $edate);
                $content .= '<p style="text-align: center;font-family: 仿宋;font-size: 14pt;">'.$SYMD.'～'.$EMD.'接单锁价出厂价格</p>';
            } else {
                // $ss = $dayRange[2] + 1;
                $dayRange = $this->getDayRange(date('Y-m-d', strtotime($sdate."+11day")));
                $SM = date("m", strtotime($dayRange[1]));
                $SY = date("Y", strtotime($dayRange[1]));
                $SYMD2 = date("Y年n月j日", strtotime($dayRange[0]));
                $EMD2 = date("n月j日", strtotime($dayRange[1]));
                $title = "结算文";
                $content .= '<p style="text-align: center;font-size: 22pt;font-weight:600;">主体产品销售结算价格及优惠</p>';
                $content .= '<p style="text-align: center;font-size: 10.5pt;font-family: 仿宋;">结算文（'.$SY.'）'.$SM.''.$dayRange[2].'</p>';
                $content .= '<p style="text-align: center;font-size: 10.5pt;font-family: 仿宋;">（内贸板、线棒）</p>';
                $content .= '<p style="text-indent: 2em;font-size: 10.5pt;font-family: 仿宋;">本期主体产品结算价格及优惠如下：</p>';
                $content .= '<h2 style="font-size: 10.5pt;text-indent: 2em;">一、价格表优惠</h2>';
                $content .= '<p style="text-indent: 2em;font-size: 10.5pt;font-family: 仿宋;">以经营财务部价表价格为基准，协议用户、非协议用户在价格表表列价格基础上优惠100元/吨（品种线材、合金钢与出口除外）。</p>';
                $content .= '<h2 style="font-size: 10.5pt;text-indent: 2em;font-size: 10.5pt;">二、后结算模式发货结算价（<span style="font-weight: 300;font-size: 10.5pt;font-family: 仿宋;">（表列价格已减去（一、价格表优惠））</span></h2>';
                $content .= '<p style="text-indent: 2em;font-size: 10.5pt;font-family: 楷体;">(一)中厚板普锰板</p>';
                $content .= '<p style="text-indent: 2em;font-size: 10.5pt;font-family: 仿宋;">'.$SYMD.'～'.$EMD.'期间发货的中厚板普锰板包到结算价：</p>';
                $content .= '<p style="text-align: right;font-size: 9pt;">单位：元/吨（含税）</p>';
                $content .= $this->getPriceTable(1, $req['muban'], $sdate, $edate);
                $content .= '<p style="text-indent: 2em;font-size: 10.5pt;font-family: 仿宋;">采购中心自提的中厚板普锰板按新余包到价减10元/吨结算。上表表列为20*2000*10000（mm）规格价格，技术加减价及其他价格规定按价格文执行。</p>';
                $content .= '<p style="text-indent: 2em;font-size: 10.5pt;font-family: 楷体;">(二)高线、盘螺、螺纹钢</p>';

                $sql = "SELECT sdate,edate FROM data_table_base WHERE uptype = 2 AND `type` = 3 AND isdel = 0 AND sdate >= '{$sdate}' and sdate <= '{$edate}' ORDER BY sdate";
                $dat2 = $this->xg->query($sql);
                foreach($dat2 as $dat2_val) {
                    $jsw_SYMD = date("Y年n月j日", strtotime($dat2_val['sdate']));
                    $jsw_EMD = date("n月j日", strtotime($dat2_val['edate']));
                    $dat2_key = array_search($dat2_val, $dat2);
                    if($dat2_key>0){
                        // $content .= '<p style="line-height:400px;height:400px;"><br></p>';
                        $content .= '<p style="font-size: 14pt;"><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br><br></p>';
                        
                    }
                    $content .= '<p style="text-indent: 2em;font-size: 10.5pt;font-family: 仿宋;">'.$jsw_SYMD.'～'.$jsw_EMD.'期间发货的高线、盘螺、螺纹钢包到结算价：</p>';
                    $content .= '<p style="text-align: right;font-size: 9pt;">单位：元/吨（含税）</p>';
                    $content .= $this->getPriceTable(2, $req['muban'], $dat2_val['sdate'], $dat2_val['edate']);
                }
                $content .= '<p style="text-indent: 2em;font-size: 10.5pt;font-family: 仿宋;">说明：</p>';
                $content .= '<p style="text-indent: 2em;font-size: 10.5pt;font-family: 仿宋;">(1)XQ235和HPB300同规格同价。</p>';
                // $content .= '<p style="text-indent: 2em;font-size: 10.5pt;font-family: 仿宋;">(2)南昌区域的站到门运输执行N+3定价模式。</p>';
                $content .= '<p style="text-indent: 2em;font-size: 10.5pt;font-family: 仿宋;">(2)螺纹钢以9m定尺，用户要求12m定尺的长沙区域加价20元/吨，江西区域加价30元/吨，广州区域不加价，要求其它定尺的加价50元/吨。</p>';
                $content .= '<p style="text-indent: 2em;font-size: 10.5pt;font-family: 仿宋;">(3)江西区域螺纹钢HRB400结算价与HRB400E同价，盘螺HRB400结算价与HRB400E同价。</p>';
                $content .= '<p style="text-indent: 2em;font-size: 10.5pt;font-family: 仿宋;">(4)HRB500E在HRB400E基础上加价300元/吨，湖南区域HRB500E在HRB400E基础上加价260元/吨，贵阳区域HRB500E在HRB400E基础上加价220元/吨。</p>';
                $content .= '<p style="text-indent: 2em;font-size: 10.5pt;font-family: 仿宋;">(5)HRB600E品种加价在HRB400E基础上加价600元/吨。</p>';
                $content .= '<p style="text-indent: 2em;font-size: 10.5pt;font-family: 仿宋;">(6)省内盘螺Φ12mm在Φ8-10mm基础上加价50元/吨，省外盘螺Φ12mm在Φ8-10mm基础上加价30元/吨。</p>';
                $content .= '<p style="text-indent: 2em;font-size: 10.5pt;font-family: 仿宋;">(7)长沙区域表列价格为磅计计重价，按磅计重量结算。其它区域为理计计重价,按理计重量结算。</p>';
                $content .= '<p style="text-indent: 2em;font-size: 10.5pt;font-family: 仿宋;">本通知执行期为'.$SYMD2.'～'.$EMD2.'，有调整时执行新调整的文件（通知）。</p>';
                $content .= '<p style="text-align: right;font-size: 10.5pt;font-family: 仿宋;">经营财务部</p>';
                $content .= '<p style="text-align: right;font-size: 10.5pt;font-family: 仿宋;">'.$SYMD2.'</p>';
            }
            $this->assign("title", $title);
            $this->assign("content", $content);
            $this->assign("dateStr", $dateStr);
            $this->assign("req", $req);
        }
    }

    private function getPriceTable($uptype, $muban, $sdate, $edate) {
        $width = "100%;";
        $table = "";
        if($uptype == 5 || $uptype == 2){
            $width = "100%;mystyle;";
        }
        if($uptype == 8){
            $width .= "font-size: 14px;";
        }
        if($uptype == 7){
            // $table .= "<table style='width: 100%;' border='1' bordercolor='#000000' cellspacing='0' cellpadding='2'><tr><td>";
            // $width = "100%;font-size:20px;";
            $bboo = "border: 0px;font-size: 14px;";
        }
        $table .= '<table border="1" bordercolor="#000000" cellspacing="0" cellpadding="2" style="font-size: 9pt;border-collapse:collapse; font-family: 仿宋; width:'.$width.'text-align: center;'.$bboo.'">';
        $tbody = "";
        // $sql = "SELECT dt.dta1, dt.dta2, dt.dta3, dt.dta4, dt.dta5, dt.dta6 FROM data_table dt
        // INNER JOIN (
        //     SELECT id
        //     FROM data_table_base
        //     WHERE uptype = {$uptype}
        //     AND type = 3
        //     AND isdel = 0
        //     AND sdate = '{$sdate}'
        //     AND edate = '{$edate}'
        //     ORDER BY createtime DESC
        //     LIMIT 1
        // ) db ON dt.baseid = db.id ORDER BY dt.id ASC";
        $sql = "SELECT dt.dta1, dt.dta2, dt.dta3, dt.dta4, dt.dta5, dt.dta6 FROM data_table dt
        INNER JOIN (
            SELECT id
            FROM data_table_base
            WHERE uptype = {$uptype}
            AND type = 3
            AND isdel = 0
            AND sdate <= '{$sdate}'
            ORDER BY sdate desc,createtime DESC
            LIMIT 1
        ) db ON dt.baseid = db.id ORDER BY dt.id ASC";
        // $data = $this->array_iconv($this->xg->query($sql));
        $data = $this->xg->query($sql);
        $tmpdata = array();
        switch($uptype){
            case 3:
                $table .= '<thead><tr><td style="width: 16%;height: 26px;">品种</td><td style="width: 20%">牌号</td><td style="width: 20%">基价</td><td>说明</td><td style="width: 20%">区域加价</td></tr></thead>';
                $tmpdata = array(
                    "普 板" => array(),
                    "锰板" => array(),
                    "高强板" => array(),
                    "建筑用钢板" => array(),
                    "桥梁板" => array(),
                    "船板" => array(),
                    "容器板" => array(),
                    "管线" => array(),
                );
                foreach($data as $key_data => $val_data) {
                    $tmpdata[$val_data['dta1']][] = $val_data;
                }
                $tbody .= '<tbody>';
                foreach ($tmpdata as $pm => $thisdata) {
                    if($pm == "船板"||$pm=="容器板"||$pm=="管线") continue;
                    $kk = 0;
                    foreach($thisdata as $rownum => $row) {
                        $num = count($thisdata);
                        if($rownum == 0) {
                            $tbody .= '<tr><td rowspan='.$num.'>'.$pm.'</td>';
                        } else {
                            $tbody .= '<tr>';
                        }
                        $tbody .= '<td style="height: 28px;">' . $row['dta2'] . '</td>';
                        $tbody .= '<td>' . $row['dta6'] . '</td>';
                        if($pm == "锰板") {
                            if($rownum == 0) 
                            $tbody .= '<td rowspan=3 style="text-align: left;">' . $row['dta3'] . '</td>';
                            else if($rownum == 3) $tbody .= '<td rowspan=2 style="text-align: left;">' . $row['dta3'] . '</td>';
                        } else {
                            if($rownum == 0) {
                                $tbody .= '<td rowspan='.$num.' style="text-align: left;">' . $row['dta3'] . '</td>';
                            }
                        }
                        if(($pm == "普 板" || $pm == "普板") && $rownum == 0) {
                            $tbody .= '<td rowspan=100 style="text-align: left;"><p>区域加价如下：</p><p>江西 +50；</p><p>湖南 +50；</p><p>其它 0</p></td>';
                        }
                        $kk++;
                        if($kk == $num)
                        $tbody .= '</tr>';
                    }
                    unset($tmpdata[$pm]);
                }
                $tbody .= '</tbody></table><p><br></p><p><br></p>';
                // 船板 后的数据放入第二个表格中
                $tbody .= $table;
                $tbody .= '<tbody>';
                foreach ($tmpdata as $pm => $thisdata) {
                    $kk = 0;
                    foreach($thisdata as $rownum => $row) {
                        $num = count($thisdata);
                        if($rownum == 0) {
                            $tbody .= '<tr><td rowspan='.$num.'>'.$pm.'</td>';
                        } else {
                            $tbody .= '<tr>';
                        }
                        $tbody .= '<td style="height: 28px;">' . $row['dta2'] . '</td>';
                        $tbody .= '<td>' . $row['dta6'] . '</td>';

                        if($rownum == 0) {
                            if($pm=="容器板") {
                                $row['dta3'] = "Q245R、Q345R含NB 47013一级探伤加价，16MnDR、370R含正火加价及NB 47013一级探伤加价。要求其他级别探伤的再追加价差。要求不探伤的不予探伤减价。";
                                $tbody .= '<td rowspan=4 style="text-align: left;">' . $row['dta3'] . '</td>';
                            } else if($pm=="管线") {
                                $row['dta3'] = "<p>1、表列价格含冲击加价；</p><p>2、表列价格含GB/T三级探伤加价，要求其他级别探伤的再追加价差，要求不探伤的不予探伤减价。</p>";
                                $tbody .= '<td rowspan='.count($tmpdata[$pm]).' style="text-align: left;">' . $row['dta3'] . '</td>';
                            } else if($pm == "船板") {
                                $tbody .= '<td></td><td rowspan=100 style="text-align: left;"><p>区域加价如下：</p><p>江西 +50；</p><p>湖南 +50；</p><p>其它 0</p><p>船板区域不加价</p></td>';
                            }
                        } else if($rownum == 4) {
                            if($pm=="容器板") {
                                $row['dta3'] = "表列价格为含A578 B探伤价格，要求其他级别探伤的再追加价差，要求不探伤的不予探伤减价。";
                                $tbody .= '<td rowspan=6 style="text-align: left;">' . $row['dta3'] . '</td>';
                            }
                        }
                        $kk++;
                        if($kk == $num)
                        $tbody .= '</tr>';
                    }
                }
                $tbody .= '</tbody>';
                break;
            
            case 1:  // 表格结构和4一样
            case 4:
                $table .= '<thead><tr><td style="height: 1.18cm">区域</td><td>Q235B</td><td>Q345B</td><td>Q355B</td></tr></thead>';
                $tbody .= '<tbody>';
                foreach($data as $key_data => $val_data) {
                    $tmpdata[$val_data['dta2']][$val_data['dta1']] = $val_data['dta6'];
                }
                foreach ($tmpdata as $pm => $thisdata) {
                    $tbody .= '<tr><td style="height: 1.18cm">'.$pm.'</td>';
                    $tbody .= '<td style="height: 1.18cm">' . $thisdata['Q235B'] . '</td>';
                    $tbody .= '<td style="height: 1.18cm">' . $thisdata['Q345B'] . '</td>';
                    $tbody .= '<td style="height: 1.18cm">' . $thisdata['Q355B'] . '</td>';
                    $tbody .= '</tr>';
                }
                $tbody .= '</tbody>';
                break;
            case 2:  // 表格结构和5一样
            case 5:
                $row2 = "<tr><td colspan=2>HPB300</td><td colspan=3>HRB400E</td><td colspan=10>HRB400E</td></tr>";
                $row3 = "<tr><td>6-6.5</td><td>8-10</td><td>10</td><td>8</td><td>6</td><td>12</td><td>14</td><td>16</td><td>18</td><td>20</td><td>22</td><td>≥25</td><td>≥28</td><td>≥32</td><td>≥36</td></tr>";
                $table .= '<thead><tr><td rowspan=3>区域</td><td colspan=2>高线</td><td colspan=3>盘螺</td><td colspan=10>螺纹钢</td></tr>'.$row2.$row3.'</thead>';
                foreach($data as $key_data => $val_data) {
                    $tmpdata[$val_data['dta4']][$val_data['dta3']] = $val_data;
                }
                $tbody .= '<tbody>';
                foreach ($tmpdata as $areak => $thisdata) {
                    $tbody .= '<tr style="width: auto;"><td>'.$areak.'</td>';
                    $tbody .= '<td style="width: 6.7%;">' . $thisdata['6-6.5']['dta6'] . '</td>';
                    $tbody .= '<td style="width: 6.2%;">' . $thisdata['8-10']['dta6'] . '</td>';
                    $tbody .= '<td style="width: 6.2%;">' . $thisdata['10']['dta6'] . '</td>';
                    $tbody .= '<td style="width: 6.2%;">' . $thisdata['8']['dta6'] . '</td>';
                    $tbody .= '<td style="width: 6.2%;">' . $thisdata['6']['dta6'] . '</td>';
                    $tbody .= '<td style="width: 6.2%;">' . $thisdata['12']['dta6'] . '</td>';
                    $tbody .= '<td style="width: 6.2%;">' . $thisdata['14']['dta6'] . '</td>';
                    $tbody .= '<td style="width: 6.2%;">' . $thisdata['16']['dta6'] . '</td>';
                    $tbody .= '<td style="width: 6.2%;">' . $thisdata['18']['dta6'] . '</td>';
                    $tbody .= '<td style="width: 6.2%;">' . $thisdata['20']['dta6'] . '</td>';
                    $tbody .= '<td style="width: 6.2%;">' . $thisdata['22']['dta6'] . '</td>';
                    $tbody .= '<td style="width: 6.2%;">' . $thisdata['≥25']['dta6'] . '</td>';
                    $tbody .= '<td style="width: 6.2%;">' . $thisdata['≥28']['dta6'] . '</td>';
                    $tbody .= '<td style="width: 6.2%;">' . $thisdata['≥32']['dta6'] . '</td>';
                    $tbody .= '<td style="width: 6.2%;">' . $thisdata['≥36']['dta6'] . '</td>';
                    $tbody .= '</tr>';
                }
                $tbody .= '</tbody>';
                break;
            case 6:
                $row2 = "<tr><td style='height: 28px;'>拉丝</td><td colspan=5>硬线</td><td>PC线</td><td colspan=3>冷镦钢</td><td>焊线</td><td rowspan=2>XGM2</td></tr>";
                $row3 = "<tr><td style='height: 28px;'>Q195</td><td>10～45#</td><td>50～70#</td><td>60～70Mn</td><td>72B</td><td>82B</td><td>30MnSi</td><td>ML08Al</td><td>ML40Cr</td><td>22A</td><td>H08A</td></tr>";
                $table .= '<thead><tr><td rowspan=3>区域</td><td colspan=12 style="height: 28px;">φ6.5—φ12.5</td></tr>'.$row2.$row3.'</thead>';
                foreach($data as $key_data => $val_data) {
                    $tmpdata[$val_data['dta4']][$val_data['dta3']."&&&".$val_data['dta2']] = $val_data;
                    // $tmpdata[$val_data['dta4']][$val_data['dta2']] = $val_data;
                }
                // print_r($tmpdata);exit;
                $tbody .= '<tbody>';
                foreach ($tmpdata as $areak => $thisdata) {
                    $tbody .= '<tr><td style="height: 28px;">'.$areak.'</td>';
                    $tbody .= '<td>' . $thisdata['Q195&&&拉丝']['dta6'] . '</td>';
                    $tbody .= '<td>' . $thisdata['10～45#&&&硬线']['dta6'] . '</td>';
                    $tbody .= '<td>' . $thisdata['50～70#&&&硬线']['dta6'] . '</td>';
                    $tbody .= '<td>' . $thisdata['60～70Mn&&&硬线']['dta6'] . '</td>';
                    $tbody .= '<td>' . $thisdata['72B&&&硬线']['dta6'] . '</td>';
                    $tbody .= '<td>' . $thisdata['82B&&&硬线']['dta6'] . '</td>';
                    $tbody .= '<td>' . $thisdata['30MnSi&&&PC线']['dta6'] . '</td>';
                    $tbody .= '<td>' . $thisdata['ML08Al&&&冷镦钢']['dta6'] . '</td>';
                    $tbody .= '<td>' . $thisdata['ML40Cr&&&冷镦钢']['dta6'] . '</td>';
                    $tbody .= '<td>' . $thisdata['22A&&&冷镦钢']['dta6'] . '</td>';
                    $tbody .= '<td>' . $thisdata['H08A&&&焊线']['dta6'] . '</td>';
                    $tbody .= '<td>' . $thisdata['&&&XGM2']['dta6'] . '</td>';
                    // $tbody .= '<td>' . $thisdata['拉丝']['dta6'] . '</td>';
                    // $tbody .= '<td>' . $thisdata['硬线']['dta6'] . '</td>';
                    // $tbody .= '<td>' . $thisdata['硬线']['dta6'] . '</td>';
                    // $tbody .= '<td>' . $thisdata['硬线']['dta6'] . '</td>';
                    // $tbody .= '<td>' . $thisdata['硬线']['dta6'] . '</td>';
                    // $tbody .= '<td>' . $thisdata['硬线']['dta6'] . '</td>';
                    // $tbody .= '<td>' . $thisdata['PC线']['dta6'] . '</td>';
                    // $tbody .= '<td>' . $thisdata['冷镦钢']['dta6'] . '</td>';
                    // $tbody .= '<td>' . $thisdata['冷镦钢']['dta6'] . '</td>';
                    // $tbody .= '<td>' . $thisdata['冷镦钢']['dta6'] . '</td>';
                    // $tbody .= '<td>' . $thisdata['焊线']['dta6'] . '</td>';
                    // $tbody .= '<td>' . $thisdata['XGM2']['dta6'] . '</td>';
                    $tbody .= '</tr>';
                }
                $tbody .= '</tbody>';
                break;
            case 7:
                $table .= '<thead><tr><td style="width: 30%;height: 24px;">中厚板</td><td style="width: 10%;">交货状态</td><td style="width: 9%;">基价</td><td style="border:none;width: 2%;"></td><td style="width: 30%;">中厚板</td><td style="width: 10%;">交货状态</td><td style="width: 9%;">基价</td></tr></thead>';
                foreach($data as $key_data => $val_data) {
                    if($val_data['dta1']=="NM400（≤30mm）") break;
                    $tmpdata1[$val_data['dta1']."^^^".$val_data['dta2']] = $val_data;
                    unset($data[$key_data]);
                }
                foreach($data as $key_data => $val_data) {
                    $tmpdata2[$val_data['dta1']."^^^".$val_data['dta2']] = $val_data;
                }
                $tbody .= '<tbody>';
                $count = count($tmpdata1) > count($tmpdata2) ? count($tmpdata1) : count($tmpdata2);
                for($iii = 0; $iii < $count; $iii++) {
                    $thisdata1 = current($tmpdata1);
                    $thisdata2 = current($tmpdata2);
                    next($tmpdata1);
                    next($tmpdata2);
                    if(!empty($thisdata1)){
                        $tbody .= '<tr><td style="height: 24px;word-wrap: break-word;
                        word-break: break-all;">'. $thisdata1['dta1'] .'</td>';
                        $tbody .= '<td style="">' . $thisdata1['dta2'] . '</td>';
                        $tbody .= '<td style="">' . $thisdata1['dta6'] . '</td>';
                    }
                    if(!empty($thisdata2)){
                        if(empty($thisdata1)) $tbody .= "<tr><td></td><td></td><td></td>";
                        $tbody .= '<td style="border:none;"></td>';
                        $tbody .= '<td style="word-wrap: break-word;
                        word-break: break-all;">' . $thisdata2['dta1'] .'</td>';
                        $tbody .= '<td style="">' . $thisdata2['dta2'] . '</td>';
                        $tbody .= '<td style="">' . $thisdata2['dta6'] . '</td>';
                        $tbody .= '</tr>';
                    }
                }

                // foreach ($tmpdata1 as $thisdata) {
                //     $tbody .= '<tr><td style="height: 28px;">'. $thisdata['dta1'] .'</td>';
                //     $tbody .= '<td>' . $thisdata['dta2'] . '</td>';
                //     $tbody .= '<td>' . $thisdata['dta6'] . '</td>';

                //     $tbody .= '</tr>';
                // }
                // $tbody .= '</tbody></table></td><td><table border="1" bordercolor="#000000" cellspacing="0" cellpadding="2" style="font-size: 9pt;border-collapse:collapse;text-align: center;width:'.$width.'marign-left:2px">';
                // $tbody .= '<thead><tr><td style="width: 60%;height: 28px;">中厚板</td><td style="width: 20%;">交货状态</td><td style="width: 20%;">指导价</td></tr></thead>';
                // $tbody .= '<tbody>';
                // foreach ($tmpdata2 as $thisdata) {
                //     $tbody .= '<tr><td style="height: 28px;">'. $thisdata['dta1'] .'</td>';
                //     $tbody .= '<td>' . $thisdata['dta2'] . '</td>';
                //     $tbody .= '<td>' . $thisdata['dta6'] . '</td>';
                //     $tbody .= '</tr>';
                // }
                $tbody .= '</tbody>';
                break;
            case 8:
                $theight = "22px;";
                $table .= '<thead><tr><td style="height: '.$theight.';width:20%;">品种</td><td width="30%">牌号</td><td width="10%">基价</td><td width="30%">说明</td></tr></thead>';
                foreach($data as $key_data => $val_data) {
                    $tmpdata[$val_data['dta1']][] = $val_data;
                }
                $tbody .= '<tbody>';
                // print_r($tmpdata);exit;
                foreach ($tmpdata as $pm => $thisdata) {
                    $kk = 0;
                    foreach($thisdata as $rownum => $row) {
                        $num = count($thisdata);
                        if($rownum == 0) {
                            $tbody .= '<tr><td rowspan='.$num.'>'.$pm.'</td>';
                        } else {
                            $tbody .= '<tr>';
                        }
                        $tbody .= '<td style="height: '.$theight.'">' . $row['dta2'] . '</td>';
                        $tbody .= '<td>' . $row['dta6'] . '</td>';
                        if($rownum == 0) {
                            if($pm == "普板") {
                                $rowspan = count($tmpdata['普板'])+count($tmpdata['锰板'])+count($tmpdata['高强结构钢']);
                                $row['dta3'] = '含Ti、Cr加价$10/吨；</p>';
                                $tbody .= '<td rowspan='.$rowspan.' style="text-align: left;"><p>'.$row['dta3'].'</p></td>';
                            } else if($pm == "管线钢") {
                                $rowspan = count($tmpdata[$pm]);
                                $row['dta3'] = "1、含A578 B级探伤，要求其他级别探伤的再追加价差，要求不探伤的不予探伤减价。</p><p>2、表列价含-10℃冲击加价；</p><p>3、其他合金加价需评审确定。";
                                $tbody .= '<td rowspan='.$rowspan.' style="text-align: left;"><p>'.$row['dta3'].'</p></td>';
                            } else if($pm == "船板"){
                                $rowspan = count($tmpdata[$pm]);
                                $row['dta3'] = "";
                                $tbody .= '<td rowspan='.$rowspan.' style="text-align: left;"><p>'.$row['dta3'].'</p></td>';
                            } else if($pm == "容器板"){
                                $rowspan = count($tmpdata[$pm]);
                                $row['dta3'] = "1、含A578 B级探伤价格,要求其他级别探伤的再追加价差，要求不探伤的不予探伤减价。";
                                $tbody .= '<td rowspan='.$rowspan.' style="text-align: left;"><p>'.$row['dta3'].'</p></td>';
                            } else if($pm == "耐候板") {
                                $rowspan = count($tmpdata[$pm]);
                                $tbody .= '<td rowspan='.$rowspan.' style="text-align: left;"><p>'.$row['dta3'].'</p></td>';
                            }
                            
                        }
                        $kk++;
                        if($kk == $num)
                        $tbody .= '</tr>';
                    }
                }
                $tbody .= '<tr><td colspan=4>备注：1、表列规格为20*2000*10000mm规格价格，技术加减价及其它价格规定按价格文执行；<br>2、欧盟、美洲、印度、土耳其、中东区域，出口中厚板表列价下调10美元。</td></tr>';
                $tbody .= '</tbody>';
                break;
            case 9:
                $table .= '<thead><tr><td style="height: 28px;">品名</td><td>规格（mm）</td><td>材质</td><td>价格（元/吨）</td><td>备注</td></tr></thead>';
                foreach($data as $key_data => $val_data) {
                    $tmpdata[$val_data['dta1']][] = $val_data;
                }
                $tbody .= '<tbody>';
                foreach ($tmpdata as $pm => $thisdata) {
                    $kk = 0;
                    foreach($thisdata as $rownum => $row) {
                        $num = count($thisdata);
                        if($rownum == 0) {
                            // $tbody .= '<tr><td rowspan='.$num.'>'.$pm.'</td>';
                        } else {
                            $tbody .= '<tr>';
                        }
                        $tbody .= '<td>'.$pm.'</td>';
                        $tbody .= '<td style="height: 28px;">' . $row['dta2'] . '</td>';
                        $tbody .= '<td>' . $row['dta3'] . '</td>';
                        $tbody .= '<td>' . $row['dta6'] . '</td>';
                        $tbody .= '<td>' . $row['dta4'] . '</td>';
                        $kk++;
                        if($kk == $num)
                        $tbody .= '</tr>';
                    }
                }
                break;
            case 10:
                $table .= '<thead><tr><td style="height: 30px;">品种</td><td>牌号</td><td>基价</td><td>说明</td></tr></thead>';
                foreach($data as $key_data => $val_data) {
                    $tmpdata[$val_data['dta1']][] = $val_data;
                }
                $tbody .= '<tbody>';
                foreach ($tmpdata as $pm => $thisdata) {
                    $kk = 0;
                    foreach($thisdata as $rownum => $row) {
                        $num = count($thisdata);
                        if($rownum == 0) {
                            $tbody .= '<tr><td rowspan='.$num.'>'.$pm.'</td>';
                        } else {
                            $tbody .= '<tr>';
                        }
                        $tbody .= '<td style="height: 30px;">' . $row['dta2'] . '</td>';
                        $tbody .= '<td>' . $row['dta6'] . '</td>';
                        if($rownum == 0) {
                            $tbody .= '<td rowspan='.$num.' style="text-align: left;">' . $row['dta3'] . '</td>';
                        }
                        $kk++;
                        if($kk == $num)
                        $tbody .= '</tr>';
                    }
                    unset($tmpdata[$pm]);
                }
                $tbody .= '</tbody>';
                break;
        }
        $table .= $tbody.'</table>';
        return $table;
    }

    function getDayRange($date='') {
        if(empty($date)) $date = date('Y-m-d');
        $inputDate = strtotime($date);
        $day = intval(date('j', $inputDate));

        $x = "";
        if ($day >= 6 && $day <= 15) {
            $startDay = date('Y-m-06', $inputDate);
            $endDay = date('Y-m-15', $inputDate);
            $x = "-1";
        } elseif ($day >= 16 && $day <= 25) {
            $startDay = date('Y-m-16', $inputDate);
            $endDay = date('Y-m-25', $inputDate);
            $x = "-2";
        } else {
            $nextMonth = date('m', strtotime('+1 month', $inputDate));
            $nextYear = date('Y', strtotime('+1 month', $inputDate));
    
            $startDay = date('Y-m-26', strtotime('-0 month', $inputDate));
            $endDay = date('Y-m-05', strtotime('-0month', strtotime("$nextYear-$nextMonth-05")));
        }
        return array($startDay, $endDay, $x);
    }

    public function export_word($params){
        $title=iconv("utf-8","gb2312",base64_decode($params['title']));
        $ym = "";
        $p_css="style='text-indent:2em; margin-top:2px;margin-bottom:2px; font-size:12pt; font-family:楷体;'" ;
        $old_p_css='class="p3"';

        $span_css="<span style='font-size: 10.5pt;font-family:楷体;'>" ;
        $old_span_css='<span>';

        $p_css1="style='text-indent:0em; margin-top:2px;margin-bottom:2px; font-size:12pt; font-family:楷体;font-weight:bold;'" ;
        $old_p_css1='class="p1 p2"';
        $header="";
        // $tmp_content=$header."<center><h2 style='font-size:15pt;font-family:黑体;'>".$title."</h2></center>".mb_convert_encoding (base64_decode($params['html']),"gb2312","utf-8");
        $tmp_content=$header.mb_convert_encoding (base64_decode($params['html']),"gb2312","utf-8");
        
        $content="<div style='background-size: 90%;'>".$tmp_content."</div>";
        $content= str_replace($old_p_css,$p_css,$content);
        $content= str_replace($old_p_css1,$p_css1,$content);
        $content= str_replace($old_span_css,$span_css,$content);
        
        ob_start(); //打开缓冲区 
        header("Cache-Control: public");
        Header("Content-type: application/octet-stream"); 
        Header("Accept-Ranges: bytes"); 
        echo '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:w="urn:schemas-microsoft-com:office:word" xmlns="//www.w3.org/TR/REC-html40">  
            <head> 
            <meta http-equiv="Content-Type" content="text/html; charset=gb2312"/> 
            <link rel="stylesheet" type="text/css" href="https://www.steelhome.cn/_v2app/css/weekbao.css">
            <style>
            #one{padding: 10px 15px;background-size: 98%;background-position: center;opacity: 0.9;}
            </style>
            <style>
            v\:* {behavior:url(#default#VML);}
            o\:* {behavior:url(#default#VML);}
            w\:* {behavior:url(#default#VML);}
            .shape {behavior:url(#default#VML);}
            </style>
            <style>
            @page
            {
                mso-page-orientation:portrait;
                size:21cm 29.7cm ;   
                margin:2.54cm 1cm 2.54cm 1cm;
            }
            @page Section1 {
                mso-header-margin:0.9cm;
                mso-footer-margin:1.75cm;
                mso-header: h1;
                mso-footer: f1;
                }
            div.Section1 { page:Section1; }
            table#hrdftrtbl
            {
                margin:0in 0in 0in 0in;
                width:1px;
                height:1px;
                overflow:hidden;
            }
        
            p.MsoFooter, li.MsoFooter, div.MsoFooter
            {
                margin:0in;
                margin-bottom:.0001pt;
                mso-pagination:widow-orphan;
                tab-stops:center 3.0in right 6.0in;
            }
        
            </style>
            <xml><w:WordDocument><w:View>Print</w:View>
            <w:Zoom>100</w:Zoom>
            <w:DoNotOptimizeForBrowser/></xml>  
            </head>';
      
        echo "<body>
            <div class='Section1' id='one' >
            ".$content."<table id='hrdftrtbl' border='0' cellspacing='0' cellpadding='0' style='font-size: 12px;'>
                <tr><td><div style='mso-element:header' id=h1 >
                    <!-- HEADER-tags -->
                        <p class=MsoHeader style='text-algin:center;'>".$header."</p>
                    <!-- end HEADER-tags -->
                    </div>
                </td>
                <td>
                <div style='mso-element:footer' id=f1><span style='position:relative;z-index:-1'> 
                    <!-- FOOTER-tags -->
                    <span style='mso-no-proof:yes'><!--[if gte vml 1]><v:shapetype
                    id='_x0000_t75' coordsize='21600,21600' o:spt='75' o:preferrelative='t'
                    path='m@4@5l@4@11@9@11@9@5xe' filled='f' stroked='f'>
                    <v:formulas>
                    <v:f eqn='if lineDrawn pixelLineWidth 0'/>
                    <v:f eqn='sum @0 1 0'/>
                    <v:f eqn='sum 0 0 @1'/>
                    <v:f eqn='prod @2 1 2'/>
                    <v:f eqn='prod @3 21600 pixelWidth'/>
                    <v:f eqn='prod @3 21600 pixelHeight'/>
                    <v:f eqn='sum @0 0 1'/>
                    <v:f eqn='prod @6 1 2'/>
                    <v:f eqn='prod @7 21600 pixelWidth'/>
                    <v:f eqn='sum @8 21600 0'/>
                    <v:f eqn='prod @7 21600 pixelHeight'/>
                    <v:f eqn='sum @10 21600 0'/>
                    </v:formulas>
                    <v:path o:extrusionok='f' gradientshapeok='t' o:connecttype='rect'/>
                    <o:lock v:ext='edit' aspectratio='t'/>
                    </v:shapetype><v:shape id='Picture_x0020_1' o:spid='_x0000_s3073' type='#_x0000_t75'
                    alt='VHB' style='position:absolute;
                    margin-right:0pt;margin-top:-400pt;
                    z-index:-1;
                    visibility:visible;mso-wrap-style:square;mso-wrap-distance-left:9pt;
                    mso-wrap-distance-top:0;mso-wrap-distance-right:9pt;
                    mso-wrap-distance-bottom:0;mso-position-horizontal:absolute;
                    mso-position-horizontal-relative:text;mso-position-vertical:absolute;
                    mso-position-vertical-relative:text'>
                    </v:shape><![endif]--></span>
                </div>
                <div style='mso-element:header' id=fh1>
                    <p class=MsoHeader ><span lang=EN-US style='mso-ansi-language:EN-US'>&nbsp;<o:p></o:p></span></p>
                </div> 
                <div style='mso-element:footer' id=ff1>
                    <p class=MsoFooter><span lang=EN-US style='mso-ansi-language:EN-US'>&nbsp;<o:p></o:p></span></p>
                </div>

                </td></tr>
                </table>
            </div>";
        echo "</body>";
        echo "</html>";
        

        if (strpos($_SERVER["HTTP_USER_AGENT"],'MSIE')) {
            header('Content-Disposition: attachment; filename='.$title.'.doc');
        }else if (strpos($_SERVER["HTTP_USER_AGENT"],'Firefox')) {
            header('Content-Disposition: attachment; filename='.$title.'.doc'); 
        } else {
            header('Content-Disposition: attachment; filename='.$title.'.doc');
        }
        header("Pragma:no-cache");
        header("Expires:0");
    }

    // 多维数组编码转换 utf8->gbk
    function array_iconv($data, $input = 'utf-8' ,$output = 'gbk'){
        $result = array();
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $result[$key] = $this->array_iconv($value, $input, $output);
            } else {
                $result[$key] = iconv($input, $output, $value);
            }
        }
        return $result;
    }
}