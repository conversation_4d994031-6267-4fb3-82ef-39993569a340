<?php
class  lwgdaypriceDao extends Dao{
	public function __construct($writer){
		 parent::__construct( $writer );
	}
	//通过guid取得用户信息
	public function get_userinfo_byguid($GUID){
		$sql = "select * from app_session_temp where  GUID ='$GUID'  order by  LoginDate DESC  limit 1";
		return $this->getRow($sql);
	}
	
	//获取用户权限和用户名
	public function get_license_privilege($Mid){
		$sql = "select app_license_privilege.privilege,app_license_privilege.username,app_license_privilege.truename,app_license_privilege.uid,app_license_privilege.orderno from app_license,app_license_privilege where  app_license.id=app_license_privilege.lid and app_license.Mid='$Mid' and app_license_privilege.mc_type=1";
		return $this->query($sql);
	}

	/*public function get_truename_by_username($username){
		$sql = "select * from adminuser where username in($username)";
		return $this->query($sql);
	}*/
	public function get_ng_price_model_info($modeltype,$startdate,$enddate){
		//$sql = "select * from ng_price_model where modeltype=$modeltype and   createtime>='".date('Y-m-d')." 00:00:00' and  createtime<='".date('Y-m-d')." 23:59:59' order by createtime desc limit 1";
		if($modeltype == '3'){
			$where = " date ='$startdate' ";
		}else if($modeltype == '7'){
			$where = " date>='$startdate' and  date<='$enddate' ";
		}
		$sql = "select * from steelhome_drc.ng_price_model where modeltype='$modeltype' and $where order by id desc limit 1";
		//echo "$sql";
		return $this->getRow($sql);
	}

	public function get_ng_price_model_detail_info($modelid,$userid,$modeltype){
		$sql = "select id,modelprice,modeloldprice,modelprice_updown,ABS(modelprice_updown) as abs_modelprice_updown,modelpirce_name,createtime,uid from ng_price_model_detail where modelprice_type='$modeltype' and modelid='$modelid' and uid='$userid' order by modelpirce_name_sort asc";
		//echo $sql."<br>";
		return $this->query($sql);
	}

	public function insert_ng_price_model_detail_info($modelid,$uid,$mid,$modeltype,$Ismakepricy_men,$price,$oldprice,$updownprice,$modelpirce_name,$UserType,$date){
		$sql = "insert into ng_price_model_detail (modelid,uid,Mid,modelprice_type,Ismakepricy_men,modelprice,modeloldprice,modelprice_updown,modelpirce_name,createtime,UserType,date) values ('$modelid','$uid','$mid','$modeltype','$Ismakepricy_men','$price','$oldprice','$updownprice','$modelpirce_name',NOW(),'$UserType','$date')";
		$this->execute($sql);
	}

	public function update_ng_price_model_detail_info($modelid,$uid,$modeltype,$price,$updownprice,$date){
		$sql = "update ng_price_model_detail set modelprice='$price',modelprice_updown='$updownprice',createtime=NOW(),date='$date' where modelid='$modelid' and modelprice_type='$modeltype' and uid='$uid' ";
		$this->execute($sql);
	}

	public function update_ng_price_model($modelid,$content){
		if($content==''){
			$sql = "update ng_price_model set ismakepricy=1,makepricytime=now() where id='$modelid'";
		}else{
			$sql = "update ng_price_model set pricycontent='$content',ismakepricy=1,makepricytime=now() where id='$modelid'";
		}
		$this->execute($sql);
	}
	public function update_ng_price_model2($modelid,$content){
		$sql = "update ng_price_model set is_wangqi=1,pricycontent='$content'  where id='$modelid' ";
		$this->execute($sql);
	}
	public function getismakepricy($modelid){
		$sql = "select ismakepricy from ng_price_model  where id='$modelid'";
		return $this->getone($sql);
	}
	public function getismakepricyByadmin($modelid){
		$sql = "select count(1) from ng_price_model_detail  where modelid='$modelid' and UserType=2";
		return $this->getone($sql);
	}
	public function getThisUserIsMakepricy($modelid,$uid){
		$sql = "select count(1) from ng_price_model_detail  where modelid='$modelid' and uid='$uid'";
		return $this->getone($sql);
	}
}