requirejs.config({
    urlArgs: "v6.5",
    paths: {
        jquery: "/meetingv1.2/js/meeting/map/libs/jquery.min",
        d3: "d3.min",
        topojson: "topojson.v1.min",
        mobiscroll: "mobiscroll2.min",
        optSelect: "optSelect.min",
        //wordecharts: "https://a.mysteelcdn.com/tikuangshi/echarts",
        //map: "https://a.mysteelcdn.com/tikuangshi/map",
        //jweixin: "https://a.mysteelcdn.com/libs/wxshare/jweixin-1.0.0",
        china: "china",
        //discountPup: "https://a.mysteelcdn.com/appwap/share/common/js/discountPup"
    },
    shim: {
        jquery: {
            exports: "$"
        },
        mobiscroll: {
            deps: ["jquery"]
        },
        openBox: {
            deps: ["jquery"]
        }
    },
    map: {
        "*": {
            css: "css"
        }
    },
    waitSeconds: 30
}),
require(["jquery", "d3", "topojson","idangerous.swiper.min", "css!idangerous.swiper.css", "mobiscroll", "china"], function(b, S, k) {
    function e() {
        Date.prototype.dateformat = function(e) {
            var t = {
                "M+": this.getMonth() + 1,
                "d+": this.getDate(),
                "h+": this.getHours(),
                "m+": this.getMinutes(),
                "s+": this.getSeconds(),
                "q+": Math.floor((this.getMonth() + 3) / 3),
                S: this.getMilliseconds()
            };
            for (var a in /(y+)/.test(e) && (e = e.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length))),
            t)
                new RegExp("(" + a + ")").test(e) && (e = e.replace(RegExp.$1, 1 == RegExp.$1.length ? t[a] : ("00" + t[a]).substr(("" + t[a]).length)));
            return e
        }
    }
    e.prototype = {
        baseApi: "localhost" == window.location.hostname || 0 <= window.location.hostname.indexOf("192.168.20") ? "" : "//openapi.mysteel.com/wap",
        openapi: "//openapi.mysteel.com/v4wap",
        center_point: {
            width: -60,
            height: 45,
            botomfix: 41
        },
        timerArr: [],
        start: function() {
            var e = this;
            localStorage.setItem("goback_wxurl", window.location.href),
            e.getQueryString("userId") ? localStorage.setItem("mysteel_userId", e.getQueryString("userId")) : localStorage.removeItem("mysteel_userId"),
            e.getHashString("uniqueMark") ? localStorage.setItem("uniqueMark", e.getHashString("uniqueMark")) : localStorage.removeItem("uniqueMark"),
            //this.getHashString("uniqueMark") && e.subform(e.baseApi + "/v4/app/share/counts.ms?type=1&uniqueMark=" + (this.getHashString("uniqueMark") || ""), ""),
            //this.checkLogin(),
            //this.aapLogin(!1),
            this.getQueryString("gldata") && sessionStorage.removeItem("dayVal"),
			this.getQueryString("ProductType") && sessionStorage.removeItem("dayVal"),
            "blue" == this.getQueryString("skin") && b("body").addClass("theme-blue"),
            //"1" == this.getQueryString("bg") && b("#main").css("background-image", "url(https://a.mysteelcdn.com/appwap/map/dist/images/bg_blue.jpg)").css(" background-repeat", "no-repeat").css("background-size", "100% 100%"),
            //window.Wheight = b(window).innerHeight() - b(".topBtn").height() - b(".pagetab").height() + 10,
			window.Wheight = b(window).innerHeight()+ 10,//处理全屏
			
            //b("#linemain").height(Wheight - b("#lineTop").height() - b(".topVal").height() - b(".timeType").height() - 15);
            b("#myStateButton .active input").attr("data-val"),
            b("#appDate").val(),
            b("#selectOpt .optName").attr("data-val");
            var t = this.getQueryString("mysteeldata")
              , a = this.getQueryString("bigscreen");
            b("#main,#linemain, #ImportedMapCharts").on("touchmove", function(e) {
                return e.preventDefault(),
                !1
            }),
            "object" != typeof window.sessionStorage || this.browser().ie6 || this.browser().ie7 || this.browser().ie8 ? (b("#main,#linemain,#ImportedMapCharts").html("<p style='text-align: center;line-height: 40px;'>您的浏览器不支持html5,请升级浏览器！</p>"),
            b("#loading").hide()) : (this.setStartval(),
            (b("#main").height(Wheight - 10),
            this.setTab()) ,
            //this.showAD(),
            this.bindEvent(),
           /*  "jkk" != b("#selectOpt .optName").attr("data-val") ? (b(".china-map").show(),
            b(".globalmap").hide(),
            this.loadData(1)) : (b(".china-map").hide(),
            b(".globalmap").show(),
            this.getImportedMapPrice(),
            e.sessionVal()) */
			(b(".china-map").show(),
            b(".globalmap").hide(),
            this.loadData(1))
			
			)
        },
        browser: function() {
            var e = window.navigator.userAgent.toLowerCase()
              , t = {
                msie: /msie/.test(e) && !/opera/.test(e),
                opera: /opera/.test(e),
                safari: /webkit/.test(e) && !/chrome/.test(e),
                firefox: /firefox/.test(e),
                chrome: /chrome/.test(e)
            }
              , a = "";
            for (var i in t)
                if (t[i]) {
                    a = "safari" == i ? "version" : i;
                    break
                }
            return t.version = a && RegExp("(?:" + a + ")[\\/: ]([\\d.]+)").test(e) ? RegExp.$1 : "0",
            t.ie = t.msie,
            t.ie6 = t.msie && 6 == parseInt(t.version, 10),
            t.ie7 = t.msie && 7 == parseInt(t.version, 10),
            t.ie8 = t.msie && 8 == parseInt(t.version, 10),
            t.ie9 = t.msie && 9 == parseInt(t.version, 10),
            t
        },
        cookie: function(e, t, a) {
            if (void 0 === t) {
                var i = null;
                if (document.cookie && "" != document.cookie)
                    for (var n = document.cookie.split(";"), s = 0; s < n.length; s++) {
                        var o = n[s].replace(/(^\s*)|(\s*$)/g, "");
                        if (o.substring(0, e.length + 1) == e + "=") {
                            i = decodeURIComponent(o.substring(e.length + 1));
                            break
                        }
                    }
                return i
            }
            a = a || {},
            null === t && (t = "",
            a.expires = -1);
            var r, l = "";
            a.expires && ("number" == typeof a.expires || a.expires.toUTCString) && ("number" == typeof a.expires ? (r = new Date).setTime(r.getTime() + 24 * a.expires * 60 * 60 * 1e3) : r = a.expires,
            l = "; expires=" + r.toUTCString());
            var d = a.path ? "; path=" + a.path : ""
              , p = a.domain ? "; domain=" + a.domain : ""
              , c = a.secure ? "; secure" : "";
            document.cookie = [e, "=", encodeURIComponent(t), l, d, p, c].join("")
        },
        switchOpt: function() {
            var e = this
              , t = e.localData()
              , a = b("#test_select option[value='" + t.option + "']").next();
            0 === a.length && (a = b("#test_select option[value='kzlwg']"));
            var i = a.text().split(",")
              , n = void 0 === a.attr("value") ? "kzlwg" : a.attr("value");
            i[1].match(/^\s*$/) && (i[1] = ""),
            i[2].match(/^\s*$/) && (i[2] = ""),
            b("#selectOpt .optName").attr("data-val", n).html(i[0] || ""),
            b("#selectOpt .spec").html((i[1] || "") + " " + i[2] || "");
            var s = "type=" + t.type + "&datetime=" + t.datetime + "&option=" + n + "&zstimeType=" + t.zstimeType + "&zsoption=" + t.zsoption;
            e.sessionVal("dayVal", s),
            e.loadData()
        },
        loadData: function(e) {
            var n = this
              , t = 1 === e ? 1 : 0
              //, a = "mapDta" + Math.floor(100 * Math.random().toFixed(2))
              , i = n.localData();
            b(".nodata").hide(),
            b("#main, #ImportedMapCharts").html(""),
            document.getElementById("loading").style.display = "block",
            b.ajax({
                //url: "steelhomexxh.php?action=getmap&type=" + i.pz + "&date=" + i.datetime + "&option=" + i.option + "&defaultSign=" + t,
                url: "meeting.php?action=getmap&type=" + i.pz + "&date=&option=" + i.option + "&defaultSign=" + t,
				dataType: "json",
                timeout: "20000",
                async: !1,
                //jsonpCallback: a,
                success: function(e) {
                    if (e.errno)
                        return b("#main, #ImportedMapCharts").html("<p style='text-align: center;line-height: 150px;'>数据加载失败，请稍后重试</p>"),
                        void (document.getElementById("loading").style.display = "none");
                    0 == e.data.length && b(".nodata").show();
                    for (var t = [], a = [], i = 0; i < e.data.length; i++)
                        e.data[i].adFirstShow ? t.push(e.data[i]) : a.push(e.data[i]);
                    e.data = t.concat(a).filter(function(e, t) {
                        return e.address
                    }),
                    window.mapdata = e,
                    n.dataMax(),
                    n.loadMap(),
                    document.getElementById("loading").style.display = "none",
                    b("#zoomBtn .refresh").hasClass("rolling") && b("#zoomBtn .refresh").removeClass("rolling")
                },
                error: function(e) {
                    b("#main, #ImportedMapCharts").html("<p style='text-align: center;line-height: 150px;'>数据加载失败，请稍后重试</p>"),
                    document.getElementById("loading").style.display = "none"
                }
            })
        },
        dataMax: function() {
            Array.prototype.indexOf = function(e) {
                for (var t = 0, a = this.length; t < a; t++)
                    if (this[t] == e)
                        return t;
                return -1
            }
            ,
            Array.prototype.max = function() {
                var e = Math.max.apply({}, this);
                return [e, this.indexOf(e)]
            }
            ,
            Array.prototype.min = function() {
                var e = Math.min.apply({}, this);
                return [e, this.indexOf(e)]
            }
            ,
            Array.prototype.remove = function(e) {
                var t = this.indexOf(e);
                -1 < t && this.splice(t, 1)
            }
            ;
            var i, n, s, o, e, t, a, r, l, d = window.mapdata, p = "";
            0 < d.data.length && (i = [],
            s = n = 0,
            o = [],
            d.data.forEach(function(e, t) {
                "-" != d.data[t].value && o.push(d.data[t])
            }),
            o.forEach(function(e, t) {
                var a = "-" === o[t].value ? 0 : o[t].value;
                0 === a ? o.remove(o[t]) : (i.push(a),
                n += parseInt(o[t].value),
                s += parseInt(o[t].raise))
            }),
            e = o[i.max()[1]],
            t = o[i.min()[1]],
            a = (n / i.length).toFixed(0),
            "NaN" == (l = 0 < (r = (s / i.length).toFixed(0)) ? "+" + r : r) && (l = 0),
            "铁矿石" == t.breed && (t.factory = "",
            e.factory = ""),
            p += '<div class="view view1"><p class="viewTitle">平均价</p><p class="' + this.getColor(r) + '">' + (this.getQueryString("istry")=="1"?'***':a) + " " + (this.getQueryString("istry")=="1"?'***':l) + "</p></div>",
            p += '<div class="view view2"><p class="viewTitle">最高价</p><p>' + e.city + " " + e.factory + '</p><p class="' + this.getColor(e.raise) + '">' + (this.getQueryString("istry")=="1"?'***':e.value) + " " + (this.getQueryString("istry")=="1"?'***':e.raise) + "</p></div>",
            p += '<div class="view view3"><p class="viewTitle">最低价</p><p>' + t.city + " " + t.factory + '</p><p class="' + this.getColor(t.raise) + '">' + (this.getQueryString("istry")=="1"?'***':t.value) + " " + (this.getQueryString("istry")=="1"?'***':t.raise) + "</p></div>"),
            b("#overView").html(p)
        },
        getColor: function(e, t) {
            var a = parseFloat(e);
            if (!t)
                return 0 < a ? "red" : a < 0 ? "green" : ""
        },
        loadMap: function() {
            var r = this;
            b("#main").empty();
            var o = this.localData()
              , l = 850;
            "1" === r.getQueryString("bigscreen") && (l = 1080);
			//l=l<window.innerWidth?l:300;
			//l=l*0.85;
			console.log(l);
            var e = window.innerWidth
              //, t = window.innerHeight - this.center_point.botomfix
			  , t = window.innerHeight
              , a = l
              , i = .5 * a
              , n = 15 * a
              , d = function(e) {
                return e <= 1 ? i : 600 * e
            }
              //, s = S.scale.linear().domain([320, 1600]).range([-105, this.center_point.width])
			  , s = S.scale.linear().domain([320, 1600]).range([-105, this.center_point.width])
              //, p = S.scale.linear().domain([480, 900]).range([this.center_point.height, 55])
			  , p = S.scale.linear().domain([480, 900]).range([this.center_point.height, 55])
              //, c = S.geo.mercator().center([Math.abs(s(e)), p(t)]).translate([e, 2 * t])
			  , c = S.geo.mercator().center([Math.abs(s(e)), p(t+89)]).translate([e, 2 * t])
              , h = S.behavior.zoom().scale(a).scaleExtent([i, n]).center([e / 2, t / 2]).on("zoom", function() {
                var e = h.scale();
				console.log(Math.abs(s(e))+"||||"+p(t));
                b("#zoomBtn span").removeClass("gray"),
                4e3 < e ? b("#zoomBtn .jia").addClass("gray") : e <= 900 && b("#zoomBtn .jian").addClass("gray");
                c.translate(h.translate()).scale(h.scale()),
                S.select("#chinamap").attr("transform", function(e) {
                    return "translate(" + h.translate() + ")scale(" + h.scale() / l + "," + h.scale() / l + ")"
                }),
                w.selectAll("text").attr("transform", function(e) {
                    return h.scale() < d(e.citytype) ? "translate(-999,-999)" : "translate(" + c(e.address)[0] + "," + c(e.address)[1] + ")"
                }),
                w.selectAll("circle").attr("transform", function(e) {
                    return h.scale() < d(e.citytype) ? "translate(-999,-999)" : "translate(" + c(e.address)[0] + "," + c(e.address)[1] + ")"
                }),
                y()
            })
              , m = S.geo.path().projection(c)
              , u = S.select("#main").append("svg").attr("width", e).attr("height", t).append("g").attr("id", "chinamap");
            b(window).resize(function() {
                //b("#linemain").height(Wheight - b("#lineTop").height() - b(".topVal").height() - b(".timeType").height() - 15),
                //S.select("#main svg").attr("width", window.innerWidth).attr("height", window.innerHeight - 51),
				S.select("#main svg").attr("width", window.innerWidth).attr("height", window.innerHeight),
                S.select("#mapTitle").attr("x", window.innerWidth - 100)
            });
            var g = mapdata.datetime;
            "0" == o.type && (g = "",
            b("#appDate").val(mapdata.datetime),
            r.sessionVal()),
            "pbf" == o.option || "ctf" == o.option || "pbk" == o.option || "kljsf" == o.option || "bxhhf" == o.option || "jbbf" == o.option || "bc" == o.option || "hhf" == o.option ? b("#legend").addClass("type2") : "ymdf" == o.option ? (b("#legend").removeClass("type2"),
            b("#legend .legendList").eq(0).html('<li><span class="color1"></span> &gt; 20</li><li><span class="color2"></span> 1 ~ 20</li><li><span class="color3"></span> -1 ~-20</li><li><span class="color4"></span> &lt; -20</li>')) : (b("#legend").removeClass("type2"),
            b("#legend .legendList").eq(0).html('<li><span class="color1"></span> &gt; 50</li><li><span class="color2"></span> 1 ~ 50</li><li><span class="color3"></span> -1 ~-50</li><li><span class="color4"></span> &lt; -50</li>')),
            S.select("#main svg").append("text").attr("id", "mapTitle").attr("textLength", "1" == o.type ? 90 : 80).attr("x", "1" == o.type ? e - 100 : e - 88).attr("y", 62).text(g);
            var w = S.select("#main svg").append("g").attr("id", "maptext");
            S.select("#main svg").call(h).call(h.event);
            function v(e) {
                S.select("#chinamap").selectAll("path").data(e.features).enter().append("path").attr("d", m).attr("fill", function(i) {
                    var n;
                    return n = "blue" == r.getQueryString("skin") ? "#012269" : "#f4f3f1",
                    "blue" === r.getQueryString("skin") ? b.each(mapdata.data, function(e, t) {
                        var a;
                        mapdata.data[e].province && mapdata.data[e].province === i.properties.name && mapdata.data[e].fillMapColor && (a = parseFloat(mapdata.data[e].raise) || 0,
                        "铁矿石" == mapdata.data[e].breed ? 10 < a ? n = "#972440" : a <= 10 && 5 < a ? n = "#AD3250" : a <= 5 && 0 < a ? n = "#de5b79" : 0 === a ? n = "#012269" : a < 0 && -5 <= a ? n = "#14a880" : a < -5 && -10 <= a ? n = "#14824b" : a < -10 && (n = "#0e6b30") : 0 <= ["玉米淀粉"].indexOf(mapdata.data[e].breed) ? 20 < a ? n = "#972440" : a <= 20 && 0 < a ? n = "#de5b79" : 0 === a ? n = "#012269" : -20 <= a && a < 0 ? n = "#14a880" : a < -20 && (n = "#14824b") : 50 < a ? n = "#972440" : a <= 50 && 0 < a ? n = "#de5b79" : 0 === a ? n = "#012269" : -50 <= a && a < 0 ? n = "#14a880" : a < -50 && (n = "#14824b"))
                    }) : b.each(mapdata.data, function(e, t) {
                        var a;
                        mapdata.data[e].province && mapdata.data[e].province === i.properties.name && mapdata.data[e].fillMapColor && (a = parseFloat(mapdata.data[e].raise) || 0,
                        "铁矿石" == mapdata.data[e].breed ? 10 < a ? n = "#fd8282" : a <= 10 && 5 < a ? n = "#ffb0b0" : a <= 5 && 0 < a ? n = "#ffe0e0" : 0 === a ? n = "#f4f3f1" : a < 0 && -5 <= a ? n = "#caf2ab" : a < -5 && -10 <= a ? n = "#aada85" : a < -10 && (n = "#6cbb2e") : 0 <= ["玉米淀粉"].indexOf(mapdata.data[e].breed) ? 20 < a ? n = "#ffb0b0" : a <= 20 && 0 < a ? n = "#ffe0e0" : 0 === a ? n = "#f4f3f1" : -20 <= a && a < 0 ? n = "#caf2ab" : a < -20 && (n = "#aada85") : 50 < a ? n = "#ffb0b0" : a <= 50 && 0 < a ? n = "#ffe0e0" : 0 === a ? n = "#f4f3f1" : -50 <= a && a < 0 ? n = "#caf2ab" : a < -50 && (n = "#aada85"))
                    }),
                    n
                }),
                S.select("#chinamap").append("image").attr("transform", function() {
                    return "translate(" + c([104.93, 40.83])[0] + "," + c([104.93, 40.83])[1] + ")"
                }).attr("width", 850 === l ? 350 : 445).attr("height", 850 === l ? 650 : 830).attr("xlink:href", function() {
                    r.getQueryString("skin"),
                    r.getQueryString("bigscreen")
                }),
                w.selectAll("circle").data(window.mapdata.data).enter().append("circle").attr("transform", function(e) {
                    return l < d(e.citytype) ? "translate(-999,-999)" : "translate(" + c(e.address)[0] + "," + c(e.address)[1] + ")"
                }).attr("r", 3).attr("fill", "blue" == r.getQueryString("skin") ? "#fff" : "#666"),
                w.selectAll("text").data(window.mapdata.data).enter().append("text").attr("x", function(e) {
                    return e.offset ? e.offset[0] : 5
                }).attr("y", function(e) {
                    return e.offset ? -e.offset[1] : 0
                }).attr("class", "MyText").attr("transform", function(e) {
                    return l < d(e.citytype) ? "translate(-999,-999)" : "translate(" + c(e.address)[0] + "," + c(e.address)[1] + ")"
                }).on("touchstart", function(e, t) {
                    var a = event.targetTouches[0];
                    window.startPos = {
                        x: a.pageX,
                        y: a.pageY,
                        time: (new Date).getTime()
                    }
                }).on("click", function(e, t) {
					//xiangbin 点击事件去除
                    // if (r.checkbtn() && !islogin)
                        // return r.isShowDownloadDialog(!1),
                        // !1;
                    // var a = !0;
                    // "1" === r.getQueryString("bigscreen") && (a = !1),
                    // window.parent && window.parent.postMessage({
                        // option: b("#selectOpt .optName").data("val") || "",
                        // time: b("#appDate").val() || "",
                        // type: b("#myStateButton .active input").attr("data-val") || ""
                    // }, "*");
                    // var i = o.option
                      // , n = "//" + location.host + location.pathname.replace("index.html", "") + "pages.html?mcode=" + encodeURI(e.city) + "&optName=" + encodeURI("" + i) + "&datetime=" + b("#appDate").val() + (r.getQueryString("skin") ? "&skin=blue" : "") + (r.getQueryString("bg") ? "&bg=1" : "");
                    // return void 0 !== window.scriptingHelper ? window.scriptingHelper.openWindow(parseInt(8), "http:" + n, e.breed + " " + e.material + " " + e.spec) : a && (location.hash = "#hqmap",
                    // window.location = n),
                    // !1
                }).on("touchend", function(e, t) {
                    if (event.stopPropagation(),
                    event.preventDefault(),
                    r.checkbtn() && !islogin)
                        return r.isShowDownloadDialog(!1),
                        !1;
                    var a = !0;
                    if ("1" === r.getQueryString("bigscreen") && (a = !1),
                    "click" !== event.type) {
                        window.parent && window.parent.postMessage({
                            option: b("#selectOpt .optName").data("val") || "",
                            time: b("#appDate").val() || "",
                            type: b("#myStateButton .active input").attr("data-val") || ""
                        }, "*");
                        var i = (new Date).getTime() - startPos.time
                          , n = o.option
                          , s = "//" + location.host + location.pathname.replace("index.html", "") + "pages.html?mcode=" + encodeURI(e.city) + "&optName=" + encodeURI("" + n) + "&datetime=" + b("#appDate").val() + (r.getQueryString("skin") ? "&skin=blue" : "") + (r.getQueryString("bg") ? "&bg=1" : "");
                        if (i < 200 && a)
                            return void 0 !== window.scriptingHelper ? window.scriptingHelper.openWindow(parseInt(8), "http:" + s, e.breed + " " + e.material + " " + e.spec) : window.location = s,
                            !1;
                        startPos.time = 0
                    }
                }).text(function(e) {
                    var t = b("#selectOpt .optName").text();
                    return "铁矿石" === t ? e.city : "铁合金" === t ? e.city + " " + e.factory + " " + e.spec : e.city + " " + e.factory
                }).attr("city", function(e, t) {
                    b(".MyText[city=" + e.city + "]").length;
                    return r.getQueryString("skin") || "ad" !== e.type || S.select(this).text(e.city + " ").append("tspan").attr("class", "bold red").text(e.factory),
                    e.city
                }).attr("citytype", function(e, t) {
                    return e.citytype
                }).append("tspan").attr("x", function(e) {
                    return this.parentNode.attributes.x.value
                }).attr("dy", "1.2em").attr("class", function(e) {
                    return "ad" === e.type ? "bold " + r.getColor(e.raise, !0) : r.getColor(e.raise, !0)
                }).text(function(e) {
                    return r.getQueryString("istry")==1?'***':e.value
                }).append("tspan").attr("dx", "0.7em").text(function(e) {
                    if(r.getQueryString("istry")==1)
                    {
                        return '';
                    }
                    var t = parseFloat(e.raise) || 0;
                    return 0 < t && (t = "+" + t),
                    t
                }),
                y()
            }
            var f;
            function y() {
                for (var e = 0; e <= r.timerArr.length; e++)
                    clearInterval(r.timerArr[e]);
                r.timerArr = [],
                b.each(window.mapdata.data, function(e, t) {
                    var a, i, n = b(".MyText[city=" + t.city + "]");
                    function s() {
                        num++,
                        num > n.length || (a < n.length ? h.scale() < d(n.eq(a).attr("citytype")) ? (a++,
                        s()) : (n.removeClass("animat").hide(),
                        n.eq(a).addClass("animat").show(),
                        a++) : a >= n.length && (a = 0,
                        h.scale() < d(n.eq(a).attr("citytype")) ? (a++,
                        s()) : (n.removeClass("animat").hide(),
                        n.eq(a).addClass("animat").show(),
                        a++)))
                    }
                    n.length <= 1 || (num = a = 0,
                    s(),
                    i = setInterval(function() {
                        num = 0,
                        s()
                    }, 5e3),
                    r.timerArr.push(i))
                })
            }
            S.select("#zoomBtn").selectAll("span[data-zoom]").on("click", function() {
                if (b(this).hasClass("gray"))
                    return;
                var e = 6;
                "1" === r.getQueryString("bigscreen") && (e = 2);
                u.call(h.event);
                var t = h.center()
                  , a = h.translate()
                  , i = function(e) {
                    var t = h.scale()
                      , a = h.translate();
                    return [(e[0] - a[0]) / t, (e[1] - a[1]) / t]
                }(t)
                  , n = h.scale()
                  , s = h.scale() * Math.pow(e, +this.getAttribute("data-zoom"));
                4800 < s && l < n ? s = 4800 : s <= 1200 && n < l && (s = l);
                h.scale(s);
                var o = function(e) {
                    var t = h.scale()
                      , a = h.translate();
                    return [e[0] * t + a[0], e[1] * t + a[1]]
                }(i);
                h.translate([a[0] + t[0] - o[0], a[1] + t[1] - o[1]]),
                u.transition().duration(750).call(h.event)
            }),
            window.china || (f = k.feature(chinadata, chinadata.objects.china),
            window.china = f),
            v(window.china)
        },
        getHashString: function(e) {
            var t = new RegExp("(^|&|#|/?)" + e + "=([^(&|#|?)]*)(&|$|)","i")
              , a = (window.location.search.substr(1) + window.location.hash).match(t);
            return null != a ? unescape(a[2]) : null
        },
        getQueryString: function(e) {
            var t = new RegExp("(^|&)" + e + "=([^&]*)(&|$)","i")
              , a = window.location.search.substr(1).match(t);
            return null != a ? unescape(a[2]) : null
        },
        parseQueryString: function(e) {
            var t = /([^&=]+)=([\w\W]*?)(&|$|#)/g
              , a = /^[^\?]+\?([\w\W]+)$/.exec(e)
              , i = {};
            if (a && a[1])
                for (var n, s = a[1]; null != (n = t.exec(s)); )
                    i[n[1]] = n[2];
            return i
        },
        localData: function() {
			var lx=this.getQueryString("ProductType");
            var e, t = sessionStorage.getItem("dayVal");
            t && (e = "/?" + t);
            var a = this.parseQueryString(e)
              , i = (new Date).dateformat("yyyy-MM-dd");
            return {
                datetime: a.date || i,
                option: "undefined" != a.option && a.option || "kzlwg",
                type: a.type || "0",
				pz: a.pz || (null!=lx?(lx==2?"8610":"2023"):"2023"),
                zsoption: a.zsoption ||"gczh_abs"
				
            }
        },
        bindEvent: function() {
            var t = this
              , s = t.localData();
            //t.getMapOption(),//临时去除下拉
            b("#test_select .optItem").each(function(e, t) {
                var a, i, n;
                b(t).attr("value") == s.option && (a = b(t).find("strong").text(),
                i = b(t).find(".val").text(),
                (n = b(t).find(".rise").text()) && 0 < i.length && (n = "<br/>" + b(t).find(".rise").text()),
                b("#selectOpt .spec").html(i + n),
                b("#selectOpt .optName").html(a).attr({
                    "data-val": s.option
                }),
                document.title = a + "价格-钢之家行情在线")
            });
            var e = s.datetime
              , a = (new Date).getFullYear()
              , i = {};
            b("#appDate").val(e),
            i.date = {
                preset: "date"
            },
            i.select = {
                preset: "select"
            },
            i.time = {
                preset: "time"
            },
            i.default1 = {
                theme: "android-ics light",
                display: "top",
                mode: "scroller",
                dateFormat: "yyyy-mm-dd",
                lang: "zh",
                showNow: !1,
                height: 30,
                showOnFocus: !0,
                rows: 5,
                headerText: !1,
                startYear: a - 10,
                endYear: a,
                judgeEnvironment: t.checkbtn()
            },
            b("#appDate").scroller("destroy").scroller(b.extend(i.date, i.default1)),
            DateScrollerTool.dateCallback = function() {
                b("#selectOpt .upicon").hasClass("down") && (b("#appDate").parents(".pannel").find(".mengceng").hide(),
                b("#selectOpt .opt_select").animate({
                    height: "0"
                }),
                b("#selectOpt .upicon").removeClass("down"),
                b("#selectOpt .selectInput").removeClass("gray"))
            }
            ,
            b("#appDate").on("change", function(e) {
                return "jkk" != b("#selectOpt .optName").attr("data-val") ? (t.sessionVal(),
                t.loadData(),
                !1) : void t.sessionVal()
            }),
            // null != s.type && (b("#myStateButton .btn").removeClass("active"),
            // b("#myStateButton").find("input").eq(s.type).parent().addClass("active")),
            // b("#myStateButton").on("click", ".btn", function() {
                // if ("none" === b("#loading").css("display"))
                    // return b("#myStateButton .btn").removeClass("active"),
                    // b(this).addClass("active"),
                    // t.sessionVal(),
                    // t.loadData(),
                    // !1
            // }),
			null != s.pz && (b("#myStateButtonnew .btn").removeClass("active"),
            //b("#myStateButtonnew").find("li").eq(0).addClass("active")),
			b("#myStateButtonnew ul li[data-val='"+s.pz+"']").addClass("active")),
            b("#myStateButtonnew").on("click", ".btn", function() {
                if ("none" === b("#loading").css("display"))
                    return b("#myStateButtonnew .btn").removeClass("active"),
                    b(this).addClass("active"),
                    t.sessionVal(),
                    t.loadData(),
                    !1
            }),
			
			
            b("#zoomBtn").on("click", ".refresh", function() {
                return b(this).addClass("rolling"),
                t.loadData(),
                !1
            })
        },
        datedifference: function(e, t) {
            var a;
            return e = Date.parse(e),
            a = (t = Date.parse(t)) - e,
            a = Math.abs(a),
            Math.floor(a / 864e5)
        },
        checkbtn: function() {
            this.getQueryString("fr");
            var e = this.getQueryString("mysteeldata")
              , t = this.getQueryString("bigscreen")
              , a = e || t ? !1 : !0;
            if ((window.mysteeljs || window.webkit) && (window.mysteeljs || void 0 !== window.webkit && void 0 !== window.webkit.messageHandlers.hideShareMenu) && (a = !1),
            a)
                return a
        },
        isShowDownloadDialog: function(e) {
            e ? window.islogin || (window.location.href = "https://m.steelphone.com/share/register/index.html?goback=" + encodeURIComponent(window.location.href)) : (this.getQueryString("fr"),
            this.getQueryString("mysteeldata"),
            this.getQueryString("bigscreen"),
            b(".close_hook").show(),
            $temp = b(".download_btn button").attr("disabled"),
            b(".download_btn button").removeAttr($temp),
            b(".download_cancle").click(function(e) {
                b(".close_hook").hide()
            }),
            b(".download_sure").click(function(e) {
                b(".close_hook").hide(),
                window.location.href = "https://m.steelphone.com/share/register/index.html?goback=" + encodeURIComponent(window.location.href)
            }))
        },
        showAD: function() {
            var e, a = this, t = this.getQueryString("userId") || "", i = this.getQueryString("UAlocal") || "0", n = this.getQueryString("wap") || "0", s = this.getQueryString("fr") || "01022", o = sessionStorage.getItem("showAD") || !0, r = navigator.userAgent.toLowerCase(), l = document.getElementById("app-bar");
            "micromessenger" != r.match(/MicroMessenger/i) && "0" !== i || "0" == o || (l.style.display = "block"),
            ("1" === n || void 0 !== window.mysteeljs || void 0 !== window.webkit && void 0 !== window.webkit.messageHandlers.hideShareMenu) && (l.style.display = "none",
            e = b(window).innerHeight() - b(".topBtn").height() - b(".pagetab").height() + 10,
            b("#linemain").height(e - b("#lineTop").height() - b(".topVal").height() - b(".timeType").height() - 15),
            b("#main").height(e - 10)),
            a.checkLogin().then(function() {
                "" == t || '""' == t ? (document.getElementById("downLink").innerHTML = "打开",
                document.getElementById("downLink").onclick = function() {
                    window._hmt.push(["_trackEvent", "点击事件", "行情地图", "底部下载按钮"]),
                    setTimeout(function() {
                        window.location = "//www.steelphone.com/xz/brige/?origin=hqzx&fr=" + s + "&uniqueMark=" + (a.getHashString("uniqueMark") || "")
                    }, 300)
                }
                ) : (document.getElementById("downLink").innerHTML = "打开",
                b.ajax({
                    dataType: "jsonp",
                    timeout: "20000",
                    async: !1,
                    url: a.baseApi + "/v4/app/share/getUserInviteUrl.ms?userId=" + t,
                    success: function(t) {
                        document.getElementById("downLink").onclick = function() {
                            window._hmt.push(["_trackEvent", "点击事件", "行情地图", "底部邀请按钮"]);
                            var e = t.advUrl + a.getHashString("uniqueMark") || "";
                            setTimeout(function() {
                                window.location = e
                            }, 300)
                        }
                    }
                }))
            })
        },
        showTip: function() {
            var t;
            window.localStorage && null === localStorage.getItem("firstShow") && (b("#cityPrice").show(),
            b("#cityPrice").html('<div class="dwo"></div><div class="helpTip tip1"><p>放大地图可查看更多城市价格<br><span class="btn">下一步</span></p></div>'),
            t = 1,
            b("#cityPrice").click(function(e) {
                t = 1 === t ? (b("#cityPrice").html('<div class="dwo"></div><div class="helpTip tip2"><p>点击城市查看当月价格走势<br><span class="btn">知道了</span></p></div>'),
                2) : (b("#cityPrice").remove(),
                0)
            }),
            localStorage.setItem("firstShow", "2"))
        },
        getMapOption: function() {
            var o = this;
            b.ajax({
                dataType: "json",
                timeout: "20000",
                async: !1,
                url: "//api.mysteel.com/dbus/new/priceMapMenuTree.html",
                success: function(e) {
                    for (var t = e, a = "<div class='scool-tab'><div class='nav-con'>", i = "<div class='scool-opt'><div class='opt-con'>", n = 0; n < t.length; n++) {
                        e = t[n].data;
                        a += '<div class="select-tab-nav" data-id="' + t[n].breedName + '">' + decodeURI(t[n].breedName) + "</div>",
                        i += '<div id="' + t[n].breedName + '" class="opt-item"><div class="select-title">' + decodeURI(t[n].breedName) + "</div>";
                        for (var s = 0; s < e.length; s++)
                            selectedClass = e[s].value == o.localData().option ? "selected" : "",
                            i += '<div class="optItem ' + selectedClass + '" data-val="' + e[s].value + '" value="' + e[s].value + '"><strong><em class="lineNumb' + e[s].type + '"></em>' + e[s].tag.split(",")[0] + '</strong><p class="val">' + e[s].tag.split(",")[1] + '</p><p class="rise">' + e[s].tag.split(",")[2] + "</p></div>";
                        i += "</div>"
                    }
                    a += "</div></div>",
                    i += "</div></div>",
                    b("#selectOpt .opt_select").html(a + i)
                }
            }),
            o.SelectOption("selectOpt")
        },
        getZS: function() {
            var t = this;
            t.localData();
            b.ajax({
                dataType: "jsonp",
                timeout: "20000",
                async: !1,
                jsonpCallback: "allzs",
                url: t.openapi + "/xpic/reportApp.ms",
                success: function(e) {
                    t.setlineopt(e.data)
                }
            }),
            t.getlinedata()
        },
        getlinedata: function() {
            var d = this
              , e = d.localData().zsoption;
            Math.floor(100 * Math.random().toFixed(2));
            b.ajax({
                type: "GET",
                dataType: "jsonp",
                timeout: "20000",
                async: !1,
                jsonpCallback: "zsdetail",
                url: d.openapi + "/xpic/reportApp.ms?tabName=" + e,
                success: function(e) {
                    if (b("#lineTop .linelist").html(""),
                    b("#lineTop .lineVal").html(""),
                    b("#lineTip").html(""),
                    0 == e.data.length)
                        return b("#linemain").html("暂无数据").css({
                            "text-align": "center",
                            "padding-top": "30px"
                        }),
                        void b("#line_loading").hide();
                    b("#linemain").css({
                        "padding-top": "unset"
                    });
                    var t = d.getColor(e.raise)
                      , a = d.getColor(e.lastDayScale)
                      , i = d.getColor(e.lastWeekScale)
                      , n = d.getColor(e.lastMonthScale)
                      , s = d.getColor(e.lastYearScale)
                      , o = '<p class="p1 ' + t + '">' + e.value + '</p><p class="p2 gray">' + e.date.substr(4, 2) + "-" + e.date.substr(6, 2) + " 更新</p>"
                      , r = '<div class="swiper-wrapper"><ul class="swiper-slide"><li>昨日:<span class="' + a + '">' + e.lastDayValue + '</span></li><li>日环比:<span class="' + a + '">' + e.lastDayScale + '</span></li><li>上周:<span class="' + i + '">' + e.lastWeekValue + '</span></li><li>周环比:<span class="' + i + '">' + e.lastWeekScale + "</span></li></ul>";
                    r += '<ul class="swiper-slide"><li>上月:<span class="' + n + '">' + e.lastMonthValue + '</span></li><li>上月环比:<span class="' + n + '">' + e.lastMonthScale + '</span></li><li>去年:<span class="' + s + '">' + e.lastYearValue + '</span></li><li>去年环比:<span class="' + s + '">' + e.lastYearScale + "</span></li></ul></div>",
                    b("#lineTop .linelist").html(r),
                    b("#lineTop .lineVal").html(o);
                    new Swiper("#lineTop .linelist",{
                        loop: !0,
                        autoplay: 3e3,
                        autoplayDisableOnInteraction: !1,
                        mode: "vertical"
                    });
                    var l = [e.value, e.date.substr(0, 4) + "-" + e.date.substr(4, 2) + "-" + e.date.substr(6, 2)];
                    d.setline(e.data, l)
                }
            })
        },
        SelectOption: function(i) {
            var a = this
              , e = b(".opt_select").height();
            b("body").on("click", ".opt-con .optItem", function() {
                var e, t;
                 
				// a.checkbtn() && !islogin ? (a.isShowDownloadDialog(!1),
                // !1) : 
				return (b(this).parents("#" + i).find(".optItem").removeClass("selected"),
                b(this).addClass("selected"),
                b(this).parents("#" + i).find(".upicon").removeClass("down"),
                b(this).parents("#" + i).find(".optName").html(b(this).find("strong").text()).attr({
                    "data-val": b(this).attr("value")
                }),
                b(this).parents(".pannel").find(".mengceng").hide(),
                b(this).parents(".pannel").find("#" + i + " .selectInput").removeClass("gray"),
                b(this).parents(".pannel").find(".opt_select").animate({
                    height: "0"
                }),
                "selectOpt" == b(this).parents("#" + i).attr("id") && (t = 0 < (e = b(this).find(".val").text()).length && 0 < b(this).find(".rise").text().length ? "<br/>" + b(this).find(".rise").text() : b(this).find(".rise").text(),
                b(this).parents("#" + i).find(".spec").html(e + t),
                document.title = b("#selectOpt .optName").html() + "价格-钢之家行情在线",
                // "jkk" == b(this).attr("data-val") ? (b(".china-map").hide(),
                // b(".globalmap").show(),
                // a.getImportedMapPrice(),
                // a.sessionVal(),a.setshare()) : 
				(b(".china-map").show(),
                a.sessionVal(),
                a.loadData()))
                // void ("zhisuOpt" == b(this).parents("#" + i).attr("id") && (b("#line_loading").show(),
                // a.sessionVal(),
                // a.getlinedata(),
                // document.title = b("#zhisuOpt .optName").html() + "价格指标"))
				)
            }),
            b("body").on("click", "#" + i + " .selectInput", function() {
                var a;
                return "selectOpt" == b(this).parents("#" + i).attr("id") && DateScrollerTool.cancel(),
                // "zhisuOpt" == b(this).parents("#" + i).attr("id") && (a = b("#zhisuOpt .selectInput .optName").attr("data-val"),
                // b(this).parents("#" + i).find(".optItem").removeClass("selected"),
                // b(this).parents("#" + i).find(".optItem").each(function(e, t) {
                    // b(t).attr("value") == a && b(t).addClass("selected")
                // })),
                b(this).find(".upicon").hasClass("down") ? (b(this).parents(".pannel").find("#" + i + " .upicon").removeClass("down"),
                b(this).parents(".pannel").find("#" + i + " .selectInput").removeClass("gray"),
                b(this).parents(".pannel").find(".mengceng").hide(),
                void b(this).parents(".pannel").find("#" + i + " .opt_select").animate({
                    height: "0"
                })) : (b(this).parents(".pannel").find(".mengceng").show(),
                b(this).parents(".pannel").find("#" + i + " .selectInput").addClass("gray"),
                b(this).parents(".pannel").find("#" + i + " .upicon").addClass("down"),
                b(this).parents(".pannel").find("#" + i + " .opt_select").height(0).show().animate({
                    height: e + "px"
                }),
                b(this).parents(".pannel").find("#" + i + " .scool-opt").scrollTop(10),
                void setTimeout(function() {
                    var e = b("#" + i + " .opt_select .optItem.selected").position().top - b("#" + i + " .scool-opt").height() / 3;
                    b("#" + i + " .scool-opt").scrollTop(e)
                }, 350))
            }),
            b("body").on("click", ".mengceng", function() {
                b(this).parents(".pannel").find("#" + i + " .upicon").removeClass("down"),
                b(this).parents(".pannel").find(".mengceng").hide(),
                b(this).parents(".pannel").find("#" + i + " .selectInput").removeClass("gray"),
                b(this).parents(".pannel").find("#" + i + " .opt_select").animate({
                    height: "0"
                })
            });
            var n = b("#" + i + " .opt-item");
            b("#" + i + " .scool-opt").scroll(function() {
                var t = b(this).scrollTop()
                  , e = n.length;
                eachend = !0,
                n.each(function(e) {
                    b(this).position().top - .4 * b(".scool-opt").height() < t && eachend && (b("#" + i + " .nav-con .select-tab-nav").removeClass("active"),
                    b("#" + i + " .nav-con .select-tab-nav").eq(e).addClass("active"))
                }),
                Math.abs(t + b("#" + i + " .opt_select").height() - b("#" + i + " .opt-con").outerHeight()) <= 1 && (b("#" + i + " .nav-con .select-tab-nav").removeClass("active"),
                b("#" + i + " .nav-con .select-tab-nav").eq(e - 1).addClass("active"))
            }),
            b("body").on("click", ".nav-con .select-tab-nav", function() {
                var e = this;
                positionTop = b("#" + b(this).data("id")).position().top,
                b(".scool-opt").scrollTop(positionTop),
                setTimeout(function() {
                    b(e).siblings(".select-tab-nav").removeClass("active"),
                    b(e).addClass("active")
                }, 10)
            })
        },
        setlineopt: function(e) {
            for (var r = this, t = "<div class='scool-tab'><div class='nav-con'>", a = "<div class='scool-opt'><div class='opt-con'>", i = "", n = "", s = r.localData().zsoption, o = 1, l = 0; l < e.length; l++) {
                var d = e[l].child;
                t += '<div class="select-tab-nav" data-id="' + e[l].code + '">' + decodeURI(e[l].name) + "</div>",
                a += '<div id="' + e[l].code + '" class="opt-item"><div class="select-title">' + decodeURI(e[l].name) + "</div>";
                for (var p, c = 0; c < d.length; c++) {
                    s === d[c].key && (b("#zhisuOpt .optName").text(decodeURI(d[c].name)),
                    -1 < location.hash.indexOf("#hqzs") && (document.title = decodeURI(d[c].name) + "价格指标")),
                    "gczh_abs" !== d[c].key && "bxgzs_abs" !== d[c].key && "yszh_abs" !== d[c].key || (p = "",
                    r.getQueryString("skin") || (0 < d[c].raise ? p = "red" : d[c].raise < 0 && (p = "green")),
                    i += '<li data-val="' + d[c].key + '"><p>' + decodeURI(d[c].name) + '</p><p class="nmb ' + p + '">' + (d[c].value ? d[c].value : "") + '</p><p class="small ' + p + '">' + (d[c].raise ? d[c].raise : "") + " " + (d[c].percent ? d[c].percent : "") + "</p></li>"),
                    "shuju_jiaotan" !== d[c].key && "shuju_ozfk62" !== d[c].key && "mhg_jc" !== d[c].key || (p = "",
                    r.getQueryString("skin") || (0 < d[c].raise ? p = "red" : d[c].raise < 0 && (p = "green")),
                    n += '<li data-val="' + d[c].key + '"><p>' + decodeURI(d[c].name) + '</p><p class="nmb ' + p + '">' + (d[c].value ? d[c].value : "") + '</p><p class="small ' + p + '">' + (d[c].raise ? d[c].raise : "") + " " + (d[c].percent ? d[c].percent : "") + "</p></li>"),
                    d[c].setLine ? o++ : (selectedClass = d[c].key == b("#zhisuOpt .selectInput .optName").attr("data-val") ? "selected" : "",
                    p = 0 < d[c].raise ? "red" : 0 == d[c].raise ? "" : "green",
                    a += '<div class="optItem ' + selectedClass + '" data-val="' + o + '" value="' + d[c].key + '"><strong><em class="lineNumb1"></em>' + decodeURI(d[c].name) + '</strong><p class="val ' + p + '">' + (d[c].value || "") + '</p><p class="rise ' + p + '">' + (d[c].raise || "") + " " + (d[c].percent || "") + "</p></div>")
                }
                a += "</div>"
            }
            t += "</div></div>",
            a += "</div></div>",
            b("#zhisuOpt .opt_select").html(t + a),
            b(".topVal .up").html(i),
            b(".topVal .down").html(n),
            r.SelectOption("zhisuOpt");
            new Swiper(".topVal",{
                loop: !0,
                autoplay: 5e3,
                autoplayDisableOnInteraction: !1,
                mode: "vertical"
            });
            b(".topVal").on("click", "li", function(e) {
                if (r.checkbtn() && !islogin)
                    return r.isShowDownloadDialog(!1),
                    !1;
                b("#line_loading").show();
                var t, a = b(this).attr("data-val"), i = b(this).find("p:first-child").text(), n = sessionStorage.getItem("dayVal");
                n && (t = "/?" + n);
                var s = r.parseQueryString(t)
                  , o = "type=" + s.type + "&date=" + s.datetime + "&option=" + s.option + "&zstimeType=" + s.zstimeType + "&zsoption=" + a;
                return b("#zhisuOpt .optName").attr({
                    "data-val": a
                }).text(i),
                r.sessionVal("dayVal", o),
                r.getlinedata(),
                b(this).parents(".pannel").find(".upicon").hasClass("down") ? (b(this).parents(".pannel").find("#zhisuOpt .upicon").removeClass("down"),
                b(this).parents(".pannel").find("#zhisuOpt .selectInput").removeClass("gray"),
                b(this).parents(".pannel").find(".mengceng").hide(),
                void b(this).parents(".pannel").find("#zhisuOpt .opt_select").animate({
                    height: "0"
                })) : (document.title = b("#zhisuOpt .optName").html() + "价格指标",
                !1)
            })
        },
        setStartval: function() {
            var e, t = sessionStorage.getItem("dayVal");
            t && (e = "/?" + t);
            var a = this.parseQueryString(e)
              , i = this.parseQueryString(window.location.href)
              , n = (new Date).dateformat("yyyy-MM-dd");
            // b("#zhisuOpt .optName").attr("data-val", a.zsoption || i.zsoption || "gczh_abs"),
            // b(".timeType .cur").attr("data-val"),
            // (a.zstimeType || i.zstimeType) && (b(".timeType li").removeClass("cur"),
            // b(".timeType li[data-val='" + (a.zstimeType || i.zstimeType) + "']").addClass("cur"));
            var s = "type=" + (a.type || i.type || "0") + "&pz=" + (a.pz || i.pz || (null!=i.ProductType?(i.ProductType==2?"8610":"2023"):"2023")) + "&date=" + (a.date || i.date || n) + "&option=" + (a.option || i.option || "kzlwg") + "&zstimeType=" + (a.zstimeType || i.zstimeType || "week") + "&zsoption=" + (a.zsoption || i.zsoption || "gczh_abs");
            console.log(s);
			this.sessionVal("dayVal", s)
        },
        sessionVal: function(e, t) {
            var a, i, n = e || "dayVal", s = (s = b("body").attr("link") || window.location.href + "?Source=wx").split("#");
            t ? sessionStorage.setItem(n, t) : (
			//a = "type=" + b("#myStateButton .active input").attr("data-val") + "&date=" + b("#appDate").val() + "&option=" + b("#selectOpt .optName").attr("data-val") + "&zsoption=" + b("#zhisuOpt .optName").attr("data-val") + "&zstimeType=" + b(".timeType .cur").attr("data-val"),
            a = "type=" + b("#myStateButton .active input").attr("data-val") + "&pz=" + b("#myStateButtonnew li.active").attr("data-val") + "&date=" + b("#appDate").val() + "&option=" + b("#selectOpt .optName").attr("data-val") + "&zsoption=" + b("#zhisuOpt .optName").attr("data-val") + "&zstimeType=" + b(".timeType .cur").attr("data-val"),
			sessionStorage.setItem(n, a),
            '""' == (i = window.login_userId || this.getQueryString("userId") || this.aapLogin(!1) || window.localStorage.getItem("userId") || "") && (i = ""),
            b("body").attr("link", window.location.origin + window.location.pathname + "?" + a + "&userId=" + i + "&#" + (s[1] && 0 <= s[1].indexOf("hqzs") ? "hqzs" : "hqmap"))
            //this.setshare()
			)
        },
        setlineTip: function(e, t, a) {
            var i = ""
              , n = e ? 100 * (t - e) / e : 0;
            this.getQueryString("skin") || (0 < n ? i = "red" : n < 0 && (i = "green"));
            var s = "<li>" + a + '</li><li>价格：<span class="' + i + '">' + t + '</span></li><li>日涨跌幅：<span class="' + i + '">' + n.toFixed(2) + "%</span></li>";
            b("#lineTip").html(s)
        },
        setline: function(m, u) {
            var g = this;
            require(["echarts.min"], function(e) {
                var t = []
                  , a = []
                  , i = u[1]
                  , n = new Date(i.split("-")[0],i.split("-")[1] - 1,i.split("-")[2]).getTime()
                  , s = 864e5;
                switch (b(".timeType .cur").attr("data-val")) {
                case "week":
                    r = new Date(n - 7 * s);
                    break;
                case "month":
                    r = new Date(n - 30 * s);
                    break;
                case "halfyear":
                    r = new Date(n - 180 * s);
                    break;
                default:
                    var o = m[m.length - 1].date
                      , r = new Date(o.substr(0, 4),o.substr(4, 2) - 1,o.substr(6, 2))
                }
                for (var l = 0; l < 365; l++) {
                    var d = new Date(n - l * s)
                      , p = d;
                    if (m[l] && m[l - 1] && (d = new Date(m[l].date.substr(0, 4),m[l].date.substr(4, 2) - 1,m[l].date.substr(6, 2)),
                    p = new Date(m[l - 1].date.substr(0, 4),m[l - 1].date.substr(4, 2) - 1,m[l - 1].date.substr(6, 2))),
                    r.getTime() >= d.getTime()) {
                        r = p.dateformat("yyyy-MM-dd");
                        break
                    }
                }
                "object" == typeof r && (r = r.dateformat("yyyy-MM-dd")),
                g.setlineTip(m[1].value, m[0].value, u[1]);
                for (var c = 0, l = 0; l < m.length; l++)
                    m[l + 1] && (startrasie = m[l].value - m[l + 1].value,
                    startrasie %= m[l + 1].value,
                    c = m[l + 1].value),
                    t.push(m[l].date.substr(0, 4) + "/" + m[l].date.substr(4, 2) + "/" + m[l].date.substr(6, 2)),
                    a.push({
                        value: m[l].value,
                        prevalue: c
                    });
                t = t.reverse(),
                a = a.reverse();
                var h = e.init(document.getElementById("linemain"));
                option = {
                    tooltip: {
                        trigger: "axis",
                        formatter: function(e, t, a) {
                            g.setlineTip(e[0].data.prevalue, e[0].data.value, e[0].name)
                        }
                    },
                    grid: {
                        top: 20,
                        left: 50,
                        bottom: 30,
                        right: 38
                    },
                    xAxis: {
                        type: "category",
                        scale: !0,
                        boundaryGap: !1,
                        axisLine: {
                            onZero: !1
                        },
                        data: t,
                        axisTick: {
                            inside: !0
                        }
                    },
                    yAxis: {
                        type: "value",
                        scale: !0,
                        axisTick: {
                            inside: !0
                        }
                    },
                    dataZoom: {
                        xAxisIndex: 0,
                        type: "inside",
                        show: !1,
                        startValue: i.split("-").join("/"),
                        endValue: r.split("-").join("/")
                    },
                    series: [{
                        name: "价格",
                        type: "line",
                        showSymbol: !1,
                        hoverAnimation: !0,
                        smooth: !0,
                        clipOverflow: !1,
                        sampling: "average",
                        itemStyle: {
                            normal: {
                                color: "#003A90"
                            }
                        },
                        data: a
                    }]
                },
                h.setOption(option, !0),
                b("#line_loading").hide()
            })
        },
        setTab: function() {
            var s = this;
            s.localData();
            //s.getZS(),
            /* b(".timeType").on("click", "li", function(e) {
                return s.checkbtn() && !islogin ? s.isShowDownloadDialog(!1) : (b(this).siblings().removeClass("cur"),
                b(this).addClass("cur"),
                b("#line_loading").show(),
                s.sessionVal(),
                s.getlinedata()),
                !1
            });
            var n = 0;
            -1 < location.hash.indexOf("#hqzs") && (n = 1);
            new Swiper(".swiper-container",{
                hashnav: !0,
                simulateTouch: !1,
                onlyExternal: !0,
                hashnavWatchState: !1,
                speed: 500,
                initialSlide: n,
                pagination: ".pagetab ul",
                paginationElement: "li",
                paginationClickable: !0,
                onSlideChangeEnd: function(e, t) {
                    var a, i, n;
                    b("body").attr("link") && (a = b("body").attr("link").split("#")),
                    1 === e.activeIndex ? (document.title = b("#zhisuOpt .optName").html() + "价格指标",
                    a && 0 < a.length && (a[1] = "hqzs",
                    i = a.join("#")),
                    b("body").attr({
                        logo: "https://a.mysteelcdn.com/appwap/map/dist/images/zsshare.png",
                        link: i || window.location.href + "?Source=wx#hqzs",
                        desc: "钢材、原料、有色金属、建筑建材等价格走势，为您提供价格行情参考！"
                    })) : (document.title = b("#selectOpt .optName").html() + "价格-我的钢铁行情在线",
                    a && 0 < a.length && (a[1] = "hqmap",
                    n = a.join("#")),
                    b("body").attr({
                        logo: "https://a.mysteelcdn.com/appwap/map/dist/images/hqdt_fx.png?v=20210903",
                        link: n || window.location.href + "?Source=wx#hqmap",
                        desc: "钢材、原料、有色金属、建筑建材、农产品等各地行情一图展示，及时更新，今天你看了吗？"
                    })),
                    s.setshare()
                },
                onFirstInit: function(e) {
                    var t, a, i;
                    b("body").attr("link") && (t = b("body").attr("link").split("#")),
                    1 === n ? (document.title = b("#zhisuOpt .optName").html() + "价格指标",
                    t && 0 < t.length && (t[1] = "hqzs",
                    a = t.join("#")),
                    b("body").attr({
                        logo: "https://a.mysteelcdn.com/appwap/map/dist/images/zsshare.png",
                        link: a || window.location.href + "?Source=wx#hqzs",
                        desc: "钢材、原料、有色金属、建筑建材等价格走势，为您提供价格行情参考！"
                    })) : (document.title = b("#selectOpt .optName").html() + "价格-我的钢铁行情在线",
                    t && 0 < t.length && (t[1] = "hqmap",
                    i = t.join("#")),
                    b("body").attr({
                        logo: "https://a.mysteelcdn.com/appwap/map/dist/images/hqdt_fx.png?v=20210903",
                        link: i || window.location.href + "?Source=wx#hqmap",
                        desc: "钢材、原料、有色金属、建筑建材、农产品等各地行情一图展示，及时更新，今天你看了吗？"
                    })),
                    s.setshare()
                }
            });
            b(".pagetab ul>li").eq(0).text("行情在线"),
            b(".pagetab ul>li").eq(1).text("价格指数") */
        },
        checkEmt: function() {
            var e = {};
            return window.mysteeljs || window.webkit ? void 0 !== window.mysteeljs ? (e.emt = "app",
            window.userInfoJson = JSON.parse(window.mysteeljs.getUserInfo("false"))) : void 0 !== window.webkit && void 0 !== window.webkit.messageHandlers.getUserInfo ? e.emt = "app" : navigator.userAgent.toLowerCase().match(/MicroMessenger/i) && (e.emt = "wx") : navigator.userAgent.toLowerCase().match(/MicroMessenger/i) ? e.emt = "wx" : e.emt = "wap",
            e
        },
        getWordJson: function() {
            return {
                Afghanistan: "阿富汗",
                Angola: "安哥拉",
                Albania: "阿尔巴尼亚",
                "United Arab Emirates": "阿联酋",
                Argentina: "阿根廷",
                Armenia: "亚美尼亚",
                "French Southern and Antarctic Lands": "法属南半球和南极领地",
                Australia: "澳大利亚",
                Austria: "奥地利",
                Azerbaijan: "阿塞拜疆",
                Burundi: "布隆迪",
                Belgium: "比利时",
                Benin: "贝宁",
                "Burkina Faso": "布基纳法索",
                Bangladesh: "孟加拉国",
                Bulgaria: "保加利亚",
                "The Bahamas": "巴哈马",
                "Bosnia and Herzegovina": "波斯尼亚和黑塞哥维那",
                Belarus: "白俄罗斯",
                Belize: "伯利兹",
                Bermuda: "百慕大",
                Bolivia: "玻利维亚",
                Brazil: "巴西",
                Brunei: "文莱",
                Bhutan: "不丹",
                Botswana: "博茨瓦纳",
                "Central African Republic": "中非共和国",
                Canada: "加拿大",
                Switzerland: "瑞士",
                Chile: "智利",
                China: "中国",
                "Ivory Coast": "象牙海岸",
                Cameroon: "喀麦隆",
                "Democratic Republic of the Congo": "刚果民主共和国",
                "Republic of the Congo": "刚果共和国",
                Colombia: "哥伦比亚",
                "Costa Rica": "哥斯达黎加",
                Cuba: "古巴",
                "Northern Cyprus": "北塞浦路斯",
                Cyprus: "塞浦路斯",
                "Czech Republic": "捷克共和国",
                Germany: "德国",
                Djibouti: "吉布提",
                Denmark: "丹麦",
                "Dominican Republic": "多明尼加共和国",
                Algeria: "阿尔及利亚",
                Ecuador: "厄瓜多尔",
                Egypt: "埃及",
                Eritrea: "厄立特里亚",
                Spain: "西班牙",
                Estonia: "爱沙尼亚",
                Ethiopia: "埃塞俄比亚",
                Finland: "芬兰",
                Fiji: "斐",
                "Falkland Islands": "福克兰群岛",
                France: "法国",
                Gabon: "加蓬",
                "United Kingdom": "英国",
                Georgia: "格鲁吉亚",
                Ghana: "加纳",
                Guinea: "几内亚",
                Gambia: "冈比亚",
                "Guinea Bissau": "几内亚比绍",
                "Equatorial Guinea": "赤道几内亚",
                Greece: "希腊",
                Greenland: "格陵兰",
                Guatemala: "危地马拉",
                "French Guiana": "法属圭亚那",
                Guyana: "圭亚那",
                Honduras: "洪都拉斯",
                Croatia: "克罗地亚",
                Haiti: "海地",
                Hungary: "匈牙利",
                Indonesia: "印尼",
                India: "印度",
                Ireland: "爱尔兰",
                Iran: "伊朗",
                Iraq: "伊拉克",
                Iceland: "冰岛",
                Israel: "以色列",
                Italy: "意大利",
                Jamaica: "牙买加",
                Jordan: "约旦",
                Japan: "日本",
                Kazakhstan: "哈萨克斯坦",
                Kenya: "肯尼亚",
                Kyrgyzstan: "吉尔吉斯斯坦",
                Cambodia: "柬埔寨",
                "South Korea": "韩国",
                Kosovo: "科索沃",
                Kuwait: "科威特",
                Laos: "老挝",
                Lebanon: "黎巴嫩",
                Liberia: "利比里亚",
                Libya: "利比亚",
                "Sri Lanka": "斯里兰卡",
                Lesotho: "莱索托",
                Lithuania: "立陶宛",
                Luxembourg: "卢森堡",
                Latvia: "拉脱维亚",
                Morocco: "摩洛哥",
                Moldova: "摩尔多瓦",
                Madagascar: "马达加斯加",
                Mexico: "墨西哥",
                Macedonia: "马其顿",
                Mali: "马里",
                Myanmar: "缅甸",
                Montenegro: "黑山",
                Mongolia: "蒙古",
                Mozambique: "莫桑比克",
                Mauritania: "毛里塔尼亚",
                Malawi: "马拉维",
                Malaysia: "马来西亚",
                Namibia: "纳米比亚",
                "New Caledonia": "新喀里多尼亚",
                Niger: "尼日尔",
                Nigeria: "尼日利亚",
                Nicaragua: "尼加拉瓜",
                Netherlands: "荷兰",
                Norway: "挪威",
                Nepal: "尼泊尔",
                "New Zealand": "新西兰",
                Oman: "阿曼",
                Pakistan: "巴基斯坦",
                Panama: "巴拿马",
                Peru: "秘鲁",
                Philippines: "菲律宾",
                "Papua New Guinea": "巴布亚新几内亚",
                Poland: "波兰",
                "Puerto Rico": "波多黎各",
                "North Korea": "北朝鲜",
                Portugal: "葡萄牙",
                Paraguay: "巴拉圭",
                Qatar: "卡塔尔",
                Romania: "罗马尼亚",
                Russia: "俄罗斯",
                Rwanda: "卢旺达",
                "Western Sahara": "西撒哈拉",
                "Saudi Arabia": "沙特阿拉伯",
                Sudan: "苏丹",
                "South Sudan": "南苏丹",
                Senegal: "塞内加尔",
                "Solomon Islands": "所罗门群岛",
                "Sierra Leone": "塞拉利昂",
                "El Salvador": "萨尔瓦多",
                Somaliland: "索马里兰",
                Somalia: "索马里",
                "Republic of Serbia": "塞尔维亚共和国",
                Suriname: "苏里南",
                Slovakia: "斯洛伐克",
                Slovenia: "斯洛文尼亚",
                Sweden: "瑞典",
                Swaziland: "斯威士兰",
                Syria: "叙利亚",
                Chad: "乍得",
                Togo: "多哥",
                Thailand: "泰国",
                Tajikistan: "塔吉克斯坦",
                Turkmenistan: "土库曼斯坦",
                "East Timor": "东帝汶",
                "Trinidad and Tobago": "特里尼达和多巴哥",
                Tunisia: "突尼斯",
                Turkey: "土耳其",
                "United Republic of Tanzania": "坦桑尼亚联合共和国",
                Uganda: "乌干达",
                Ukraine: "乌克兰",
                Uruguay: "乌拉圭",
                "United States of America": "美国",
                Uzbekistan: "乌兹别克斯坦",
                Venezuela: "委内瑞拉",
                Vietnam: "越南",
                Vanuatu: "瓦努阿图",
                "West Bank": "西岸",
                Yemen: "也门",
                "South Africa": "南非",
                Zambia: "赞比亚",
                Zimbabwe: "津巴布韦"
            }
        },
        getUrl: function(e, t) {
            var a = "//" + location.host + location.pathname.replace("index.html", "") + "pages.html?mcode=" + e + "&areaName=" + t + "&datetime=" + b("#appDate").val() + (this.getQueryString("skin") ? "&skin=blue" : "");
            void 0 !== window.scriptingHelper ? window.scriptingHelper.openWindow(parseInt(8), "http:" + a) : window.location.href = a
        },
        getImportedMapPrice: function() {
            var s;
            document.getElementById("loading").style.display = "none";
            var t = b(window).innerHeight() - b(".topBtn").height()
              , i = b(window).innerWidth();
            b(".globalmap").height(t),
            b("#ImportedMapCharts").height(t);
            var o = this;
            require.config({
                paths: {
                    echarts: "assets/global/plugins/echarts/"
                }
            }),
            require(["echarts", "echarts/chart/map"], function(e) {
                echarts = e,
                function() {
                    s = echarts.init(document.getElementById("ImportedMapCharts"));
                    var n = {
                        title: {
                            text: "",
                            x: "center",
                            textStyle: {
                                fontSize: 16,
                                fontWeight: "bolder",
                                color: "#333"
                            }
                        },
                        tooltip: {
                            trigger: "axis",
                            show: !1
                        },
                        calculable: !1,
                        roamController: {
                            zlevel: 20,
                            show: !0,
                            x: i - 100,
                            y: -100,
                            step: 20,
                            width: 75,
                            height: t - 20,
                            padding: [15, 15, 15, 15],
                            borderColor: "#ccc",
                            handleColor: "#ccc",
                            fillerColor: "#000",
                            backgroundColor: "rgba(0,0,0,0)",
                            mapTypeControl: {
                                world: !0
                            }
                        },
                        series: [{
                            name: "价格",
                            type: "map",
                            mapType: "world",
                            mapRoam: !0,
                            roam: !0,
                            hoverable: !1,
                            scaleLimit: {
                                min: .8,
                                max: 4
                            },
                            mapLocation: {
                                x: "center",
                                y: "center",
                                width: 2 * i,
                                height: 2 * t
                            },
                            itemStyle: {
                                normal: {
                                    borderWidth: 1,
                                    borderColor: "white",
                                    label: {
                                        show: !1
                                    },
                                    areaStyle: {
                                        color: "#c1e3fa"
                                    }
                                },
                                emphasis: {
                                    color: "#f8ad5e",
                                    label: {
                                        show: !0
                                    }
                                }
                            },
                            data: [{
                                name: "澳大利亚",
                                value: 22404.488,
                                id: 81667,
                                itemStyle: {
                                    normal: {
                                        color: "#c1e3fa",
                                        label: {
                                            show: !0
                                        }
                                    }
                                }
                            }, {
                                name: "巴西",
                                value: 195210.154,
                                id: 81668,
                                itemStyle: {
                                    normal: {
                                        color: "#c1e3fa",
                                        label: {
                                            show: !0
                                        }
                                    }
                                }
                            }, {
                                name: "智利",
                                value: 17150.76,
                                id: 81632,
                                itemStyle: {
                                    normal: {
                                        color: "#93cdf3",
                                        label: {
                                            show: !0
                                        }
                                    }
                                }
                            }, {
                                name: "印度",
                                value: 1205624.648,
                                id: 81669,
                                itemStyle: {
                                    normal: {
                                        color: "#c1e3fa",
                                        label: {
                                            show: !0
                                        }
                                    }
                                }
                            }, {
                                name: "菲律宾",
                                value: 93444.322,
                                id: 80802,
                                itemStyle: {
                                    normal: {
                                        color: "#93cdf3",
                                        label: {
                                            show: !0
                                        }
                                    }
                                }
                            }, {
                                name: "印尼",
                                value: 240676.485,
                                id: 80803,
                                itemStyle: {
                                    normal: {
                                        color: "#93cdf3",
                                        label: {
                                            show: !0
                                        }
                                    }
                                }
                            }, {
                                name: "俄罗斯",
                                value: 21861.476,
                                id: 80800,
                                itemStyle: {
                                    normal: {
                                        color: "#93cdf3",
                                        label: {
                                            show: !0
                                        }
                                    }
                                }
                            }, {
                                name: "马来西亚",
                                value: 28275.835,
                                id: 80804,
                                itemStyle: {
                                    normal: {
                                        color: "#93cdf3",
                                        label: {
                                            show: !0
                                        }
                                    }
                                }
                            }, {
                                name: "委内瑞拉",
                                value: 236.299,
                                id: 81633,
                                itemStyle: {
                                    normal: {
                                        color: "#93cdf3",
                                        label: {
                                            show: !0
                                        }
                                    }
                                }
                            }, {
                                name: "毛里塔尼亚",
                                value: 3609.42,
                                id: 81631,
                                itemStyle: {
                                    normal: {
                                        color: "#93cdf3",
                                        label: {
                                            show: !0
                                        }
                                    }
                                }
                            }, {
                                name: "伊朗",
                                value: 240676.485,
                                id: 80801,
                                itemStyle: {
                                    normal: {
                                        color: "#93cdf3",
                                        label: {
                                            show: !0
                                        }
                                    }
                                }
                            }, {
                                name: "南非",
                                value: 51452.352,
                                id: 81630,
                                itemStyle: {
                                    normal: {
                                        color: "#93cdf3",
                                        label: {
                                            show: !0
                                        }
                                    }
                                }
                            }, {
                                name: "乌克兰",
                                value: 46050.22,
                                id: 80800,
                                itemStyle: {
                                    normal: {
                                        color: "#93cdf3",
                                        label: {
                                            show: !0
                                        }
                                    }
                                }
                            }],
                            nameMap: {}
                        }]
                    };
                    s.setOption(n),
                    function(e) {
                        e.series[0].nameMap = o.getWordJson(),
                        s.setOption(e, !0)
                    }(n),
                    ecConfig = require("echarts/config"),
                    s.on(ecConfig.EVENT.ROAMCONTROLLER, function(e) {
                        var t = s.chart.map._mapDataMap.world.transform.baseScale
                          , a = n.series[0].scaleLimit;
                        b(".up-scale").css({
                            bottom: 120,
                            left: i - 100
                        }),
                        b(".down-scale").css({
                            bottom: 120,
                            left: i - 57
                        }),
                        t > a.max ? b(".up-scale").show() : t < a.min ? b(".down-scale").show() : (b(".up-scale").hide(),
                        b(".down-scale").hide())
                    }),
                    s.on(ecConfig.EVENT.CLICK, function(e) {
                        var a, i = e, t = n.series[0].data;
                        jQuery.each(t, function(e, t) {
                            (a = t.name) == i.name ? (t.selected = !0,
                            o.getUrl(t.id, a)) : t.selected = !1
                        }),
                        s.setOption(n, !0)
                    })
                }(),
                window.addEventListener("resize6", function() {
                    s.resize()
                })
            })
        },
        subform: function(e, t) {
            b.ajax({
                type: "GET",
                url: e,
                dataType: "jsonp",
                complete: function() {
                    t && t()
                }
            })
        },
        setshare: function() {
            var l = this;
            window.random = (new Date).getTime(),
            require(["jweixin", "jquery"], function(n, e) {
                var t;
                window.jssign = function(e, t, a, i) {
                    n.config({
                        debug: !1,
                        appId: e,
                        timestamp: t,
                        nonceStr: a,
                        signature: i,
                        jsApiList: ["onMenuShareTimeline", "onMenuShareAppMessage", "onMenuShareQQ", "hideOptionMenu"]
                    })
                }
                ,
                window.random = (new Date).getTime() + Math.random(),
                t = "app" == l.checkEmt().emt ? e("body").attr("link") : e("body").attr("link").split("#")[0] + "&uniqueMark=" + window.random + "#" + e("body").attr("link").split("#")[1];
                var a, i, s, o, r = {
                    title: document.title,
                    desc: e("body").attr("desc") || "为您提供全国各地的品种价格展示，一眼看涨跌",
                    link: t || window.location.href + "?Source=wx&uniqueMark=" + window.random,
                    imgUrl: e("body").attr("logo") || "https://a.mysteelcdn.com/appwap/map/dist/images/hqdt_fx.png?v=20210903",
                    success: function(e) {
                        var t = 3
                          , t = "onMenuShareTimeline:ok" == e.errMsg || "shareTimeline:ok" == e.errMsg ? 4 : 3
                          , a = l.baseApi + "/v4/app/share/createWebShare.ms?uniqueMark=" + window.random + "&objectId=&objectName=行情地图价格指数&channelId=&channelName=&type=25&lastShareId=&shareChannel=" + t + "&authToken=" + l.cookie("authToken");
                        l.subform(a, ""),
                        window.random = (new Date).getTime() + Math.random()
                    }
                };
                (window.mysteeljs || window.webkit) && (a = document.title,
                i = r.desc,
                s = r.link,
                o = r.imgUrl,
                window.mysteeljs ? (window.mysteeljs && window.mysteeljs.hideShareMenu(!1),
                window.mysteeljs.share(a, i, s, o, "", "25")) : window.webkit.messageHandlers && window.webkit.messageHandlers.hideShareMenu && (window.webkit.messageHandlers.hideShareMenu.postMessage("false"),
                r = [a, i, s, o, "", "25"],
                window.webkit.messageHandlers.share.postMessage(r))),
                n.ready(function() {
                    n.onMenuShareAppMessage(r),
                    n.onMenuShareTimeline(r),
                    n.onMenuShareQQ(r)
                }),
                e.getScript(l.baseApi + "/v4/app/invite/jssign.ms?functionName=jssign&url=" + encodeURIComponent(location.href.split("#")[0]))
            })
        },
        checkLogin: function() {
            return b.ajax({
                url: this.baseApi + "/v4/webuser/checkCookie.ms",
                type: "POST",
                headers: {
                    authToken: this.cookie("authToken")
                },
                success: function(e) {
                    200 == e.status && e.data ? (window.login_userId = e.data.userId,
                    window.islogin = !0,
                    e.data.bulletFrame && discountPup({
                        leftBtnLink: "https://lkme.cc/78D/OGSv5UC1O"
                    }).popup()) : (window.login_userId = "",
                    window.islogin = !1)
                }
            })
        },
        aapLogin: function(e) {
            if (window.mysteeljs || void 0 !== window.webkit && void 0 !== window.webkit.messageHandlers.getUserInfo)
                if (window.mysteeljs) {
                    var t = window.mysteeljs.getUserInfo("false")
                      , a = JSON.parse(t) || "";
                    if (a && a.userId && a.encCellphone)
                        return a.userId;
                    if (!e)
                        return "";
                    window.mysteeljs.getUserInfo("true")
                } else
                    window.webkit.messageHandlers.getUserInfo.postMessage("false")
        }
    },
    (new e).start()
});
