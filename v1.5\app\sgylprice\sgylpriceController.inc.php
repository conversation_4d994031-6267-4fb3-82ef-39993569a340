<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );

class sgylpriceController extends AbstractController{
  
  public function __construct(){
    parent::__construct();
	$this->_action->setDao( new sgylpriceDao("DRCW") );
	$this->_action->t1Dao=new sgylpriceDao("MAIN");
	$this->_action->homeDao=new sgylpriceDao("91R");
	$this->_action->gcDao=new sgylpriceDao("GC");
  }

	public function v_index(){
		$this->_action->index( $this->_request );
	}
	public function do_savedata(){
		$this->_action->savedata( $this->_request );
	}
}
?>