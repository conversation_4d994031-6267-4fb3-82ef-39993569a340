<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );
class AdminController extends AbstractController{

    public function __construct(){
        parent::__construct();
        $this->_action->setDao( new AdminDao( 'MAIN' ) );
        $this->_action->stedao= new AdminDao('91W','91R');
        $this->_action->drc= new AdminDao( 'DRC' );
        $this->_action->gc= new AdminDao( 'GC' );
        $this->_action->sms= new AdminDao( '98SMR' );
    }

  //后台admincp用户SESSION检测
  public function _dopre(){
      $this->_action->checkSession();
  }

  //订单列表
  public function v_index(){
    $this->_action->index( $this->_request );
  }

 

  public function v_licenselist() {
	$this->_action->licenselist($this->_request);
  }
  public function v_addclass() {
	$this->_action->addclass($this->_request);
  }
  public function do_addclass() {
	$this->_action->addclass($this->_request);
  }
  public function v_editclass() {
	$this->_action->editclass($this->_request);
  }
  public function v_edit_datatype() {
	$this->_action->edit_datatype($this->_request);
  }
   public function do_edit_datatype() {
	$this->_action->edit_datatype($this->_request);
  }
  public function v_classlist() {
  	$this->_action->classlist($this->_request);
  }
  public function v_datatypelist() {
  	$this->_action->datatypelist($this->_request);
  }
  public function v_add_datatype() {
  	$this->_action->add_datatype($this->_request);
  }
  public function v_replacedata() {
  	$this->_action->replacedata($this->_request);
  }
  public function do_delclass() {
  	$this->_action->delclass($this->_request);
  }
  public function do_delvar() {
  	$this->_action->delvar($this->_request);
  }

  //add by ncc
  public function do_addchilddata() {
	$this->_action->addchilddata($this->_request);
  }

  public function do_delchildtype() {
	$this->_action->delchildtype($this->_request);
  }
  public function do_ajaxgetpz(){
       header( "Content-Type: text/html; charset=utf-8" );
	  $this->_action->ajaxgetpz( $this->_request );
  } 
  //hlf/2017/11/25
public function do_ajaxgetscode2(){
       header( "Content-Type: text/html; charset=utf-8" );
	  $this->_action->ajaxgetscode2( $this->_request );
  } 
  public function do_ajaxgetscode3(){
       header( "Content-Type: text/html; charset=utf-8" );
	  $this->_action->ajaxgetscode3( $this->_request );
  } 
//根据一级目录获取 数据类型
  public function do_ajaxgettype(){
       header( "Content-Type: text/html; charset=utf-8" );
	  $this->_action->ajaxgettype( $this->_request );
  } 
  public function do_ajaxgettype1(){
       header( "Content-Type: text/html; charset=utf-8" );
	  $this->_action->ajaxgettype1( $this->_request );
  }
  //根据一级目录获取 数据类型
  public function do_ajaxgettype2(){
       header( "Content-Type: text/html; charset=utf-8" );
	  $this->_action->ajaxgettype2( $this->_request );
  } 
   //根据一级目录获取 数据类型
  public function do_ajaxgettype3(){
       header( "Content-Type: text/html; charset=utf-8" );
	  $this->_action->ajaxgettype3( $this->_request );
  } 
  
   public function do_ajaxgettype4(){
       header( "Content-Type: text/html; charset=utf-8" );
	  $this->_action->ajaxgettype4( $this->_request );
  }
  
   public function do_ajaxgettype5(){
       header( "Content-Type: text/html; charset=utf-8" );
	  $this->_action->ajaxgettype5( $this->_request );
  }

	 public function do_ajaxgettype6(){
       header( "Content-Type: text/html; charset=utf-8" );
	  $this->_action->ajaxgettype6( $this->_request );
  }
  
  public function do_ajaxgettype7(){
       header( "Content-Type: text/html; charset=utf-8" );
	  $this->_action->ajaxgettype7( $this->_request );
  }
  
  public function v_searchlist() {
	$this->_action->searchlist($this->_request);
  }
  
  public function do_searchlist() {
	$this->_action->searchlist($this->_request);
  }

  public function v_addsearch() {
	$this->_action->addsearch($this->_request);
  }

  public function do_addsearch() {
	$this->_action->doaddsearch($this->_request);
  }

  public function v_editsearch() {
	$this->_action->editsearch($this->_request);
  }

  public function do_editsearch() {
	$this->_action->doeditsearch($this->_request);
  }


  public function do_ajaxsetcheck(){
       header( "Content-Type: text/html; charset=utf-8" );
	  $this->_action->ajaxsetcheck( $this->_request );
  } 

  public function v_usersealist() {
	$this->_action->usersealist($this->_request);
  }
  
  public function do_usersealist() {
	$this->_action->usersealist($this->_request);
  }

  public function v_ylsearch() {
	$this->_action->ylsearch($this->_request);
  }

  public function v_userylsearch() {
	$this->_action->userylsearch($this->_request);
  }

  public function v_userdetail() {
	$this->_action->userdetail($this->_request);
  }
  
  public function do_userdetail() {
	$this->_action->userdetail($this->_request);
  }


  public function v_datatypeyl() {
	$this->_action->datatypeyl($this->_request);
  }

 

   public function do_ajaxcheckcode(){
       header( "Content-Type: text/html; charset=utf-8" );
	  $this->_action->ajaxcheckcode( $this->_request );
  } 

  public function v_importhq() {
  	$this->_action->importhq($this->_request);
  }
  public function v_importgc() {
  	$this->_action->importgc($this->_request);
  }
  public function v_loglist() {
	$this->_action->loglist($this->_request);
  }

  public function do_importsub() {
	$this->_action->importsub($this->_request);
  }

  public function do_setpl() {
	$this->_action->setpl($this->_request);
  }

  public function do_delsearch() {
	$this->_action->delsearch($this->_request);
  }

  public function do_sondata() {
	$this->_action->sondata($this->_request);
  }

  public function do_getcodemes() {
	$this->_action->getmcodemes($this->_request);
  }

  public function v_test(){
	  $this->_action->test($this->_request);
  }

  public function do_ajaxgetcode(){
	  $this->_action->ajaxgetcode($this->_request);
  }

  public function do_getnewcode(){
	  $this->_action->getnewcode($this->_request);
  }
  //-----
  public function v_pzsz(){
  	$this->_action->pzsz($this->_request);
  }
  
   public function do_pzszzk(){
  	$this->_action->pzszzk($this->_request);
  }
  
  public function v_selpzsz(){
  	$this->_action->selpzsz($this->_request);
  }
  public function do_updatepzsz(){
  	$this->_action->updatepzsz($this->_request);
  }
  public function v_importprice(){
  	$this->_action->importprice($this->_request);
  }

  public function do_getwool(){
  	$this->_action->getwool($this->_request);
  }
  
  public function do_update_name(){
  	$this->_action->update_name($this->_request);
  }

  // 处理数据  如：将1000 转化为4
  public function do_change_UnitConv(){
  	$this->_action->change_UnitConv($this->_request);
  }

  public function v_setsmsid(){
  	$this->_action->setsmsid($this->_request);
  }

  public function  do_getinfo(){
    $this->_action->getinfo( $this->_request);
  }

  public function  do_delsms(){
      $this->_action->delsms( $this->_request);
  }


  public function v_editsms(){
      $this->_action->veditsms( $this->_request);
  }

  public function do_doeditsms(){
      $this->_action->doeditsms( $this->_request);
  }

  public function v_addsms(){
    $this->_action->addsms( $this->_request);
  }

  public function do_doaddsms(){
      $this->_action->doaddsms( $this->_request);
  }

  public function do_getsms(){
    $this->_action->getsms( $this->_request);
  }

  //add by std 2021/5/25
  public function v_powerlist(){
    $this->_action->powerlist($this->_request);
  }

  public function v_addtype(){
    $this->_action->addtype($this->_request);
  }

  
  public function do_doaddtype(){
    $this->_action->doaddtype($this->_request);
  }

  public function v_additem(){
    $this->_action->additem($this->_request);
  }

  public function do_doadditem(){
    $this->_action->doadditem($this->_request);
  }

  public function do_delpopedom(){
    $this->_action->delpopedom($this->_request);
  }

  public function v_add_datatypenew(){
    $this->_action->add_datatypenew($this->_request);
  }
  public function do_getFirstL(){
    $this->_action->getFirstL($this->_request);
  }

  public function v_datatypechart(){
    $this->_action->datatypechart($this->_request);
  }

  public function do_copyCode(){
    $this->_action->copyCode($this->_request);
  }
  public function do_getCodeData(){
    $this->_action->getCodeData($this->_request);
  }
  public function do_migrateData(){
    $this->_action->migrateData($this->_request);
  }

  public function do_saveDtod(){
    $this->_action->saveDtod($this->_request);
  }
  public function do_saveSod(){
    $this->_action->saveSod($this->_request);
  }
  //std end 

  public function v_tks_gx_table(){
    $this->_action->tks_gx_table($this->_request);
  }
  public function do_delete_jkkcs(){
    $this->_action->delete_jkkcs($this->_request);
  }
  public function v_tks_jkk_table(){
    $this->_action->tks_jkk_table($this->_request);
  }
  public function do_delete_tks_jkk(){
    $this->_action->delete_tks_jkk($this->_request);
  }
  public function v_tks_ckk_table(){
    $this->_action->tks_ckk_table($this->_request);
  }
  public function do_delete_tks_ckk(){
    $this->_action->delete_tks_ckk($this->_request);
  }
  public function v_calc_tks_gx(){
    $this->_action->calc_tks_gx($this->_request);
  }
  public function v_jmjt_gx_table(){
    $this->_action->jmjt_gx_table($this->_request);
  }
  public function do_delete_jmjt_setings(){
    $this->_action->delete_jmjt_setings($this->_request);
  }
  public function v_calc_jmjt_gx(){
    $this->_action->calc_jmjt_gx($this->_request);
  }
}

?>