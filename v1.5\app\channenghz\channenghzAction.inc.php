<?php
class channenghzAction extends AbstractAction{
   public function __construct(){
    parent::__construct();    
  }
	public function index($params)
	{
		//print"<pre>";print_r($params);
		$selectprovince=$params['state'];
		$selectcity=$params['city'];
		$mode=$_GET['mode'];
		//echo $mode;
		$name='';
		$where="";

		//if ($params['city2']=='1'||$params['city2']=='2') {
		//	$name='市';
		//}else{
		//	$name='省区市';	
		//}
		if($params['city2']==""){
			//if($selectprovince==="0"||$selectprovince!=""){
			//	$selectprovince=array($selectprovince=>$GLOBALS['channeng_placesMap'][$selectprovince."0000"]);
			//}
			if($selectcity==""&&$selectprovince!=""){
				//$where=" and sc_steelcom.province = '".$params['state']."'";
				$selectcity=$this->getcitybyprovince($selectprovince);
				$selectprovince=array($selectprovince=>$GLOBALS['channeng_placesMap'][$selectprovince."0000"]);
				//print_r($selectcity);
				$name='市';
				$groupname="city";
			}elseif($selectcity!=""){
				$selectcity=array(0=>$selectcity);
			}else{
				$selectprovince=$GLOBALS['channeng_SHENG_FEN'];
			}
		}elseif($params['city2']==1){
			$selectprovince="";
			$selectcity=$GLOBALS['twojia'];
		}elseif($params['city2']==2){
			$selectprovince="";
			$selectcity=$GLOBALS['fwei'];
		}elseif($params['city2']==3){
			$selectprovince=$GLOBALS['sanjiao'];
			$selectcity="";
		}
	
				foreach($selectcity as $k1=>$v1){	
					if(strstr($v1,'市')){
					$selectcity[]=str_replace("市","",$v1);
					}else{
						$selectcity[]=$v1.'市';	
					}

				}
			//	echo '<pre>';print_r($selectcity);
		if($where==""){
			if($selectcity!=""){
				$where.=" and sc_steelcom.city in ('".implode("','",$selectcity)."')";
				$name='市';
				$groupname="city";
			}
			if($selectprovince!=""){
				if(is_array($selectprovince)) $where.=" and sc_steelcom.province in ('".implode("','",array_keys($selectprovince))."')";
				else  $where.=" and sc_steelcom.province ='$selectprovince'";
				if($selectcity==""){
					$name='省区市';
					$groupname="province";
				}
			}
			if($selectprovince==""&&$selectcity=="")
			{
				$name='省区市';
				$groupname="province";
			}
		}
		$sql="select sc_steelcom.$groupname as city,sum(sc_device.t1_field4) as cn,count(*) as count from sc_device,sc_steelcom where sc_device.sc_id=sc_steelcom.id and sc_device.t1_field1='3' and (sc_device.t1_field6=1 or sc_device.t1_field6=2) $where group by sc_steelcom.$groupname";
		//echo $sql."</br>";
		$this->P($sql);
		$info=$this->_dao->query($sql);
		
		//$this->P($info);
		
		$sumcount=0;
		$sumcn=0;
		$infor=array();
		if($groupname=="province"){
			$this->P($selectprovince);
			foreach($selectprovince as $i=>$v){
				$cityname=$v;
				$cityname=str_replace("省","",$cityname);
				$cityname=str_replace("市","",$cityname);
				if(trim($cityname)!="")$infor[$cityname]=array('city'=>$cityname,'count'=>'','cn'=>'');
			}
			foreach($info as $i=>$v){
				$provinceid = $info[$i]['city'];
				$info[$i]['city']=$GLOBALS['channeng_placesMap'][$provinceid."0000"];
				$info[$i]['city']=str_replace("省","",$info[$i]['city']);
				$info[$i]['city']=str_replace("市","",$info[$i]['city']);

				if(trim($info[$i]['city'])!=""){
				    if ( empty( $infor[$info[$i]['city']]['count'] ) ) {
                        $infor[$info[$i]['city']]['count'] = 0;
                    }
				    if ( empty( $infor[$info[$i]['city']]['cn'] ) ) {
                        $infor[$info[$i]['city']]['cn'] = 0;
                    }

					$infor[$info[$i]['city']]['city']=$info[$i]['city'];
					$infor[$info[$i]['city']]['count']+= (int)$info[$i]['count'];
					$infor[$info[$i]['city']]['cn']+=(int)$info[$i]['cn'];
					$sumcount+=(int)$info[$i]['count'];
					$sumcn+=(int)$info[$i]['cn'];
				}
			}
		}else{
			$this->P($selectcity);
			foreach($selectcity as $i=>$v){
				$cityname=$v;
				$cityname=str_replace("省","",$cityname);
				$cityname=str_replace("市","",$cityname);
				if(trim($cityname)!="")$infor[$cityname]=array('city'=>$cityname,'count'=>'','cn'=>'');
			}
			foreach($info as $i=>$v){
				$info[$i]['city']=str_replace("省","",$info[$i]['city']);
				$info[$i]['city']=str_replace("市","",$info[$i]['city']);
				if(trim($info[$i]['city'])!=""){
                    if ( empty( $infor[$info[$i]['city']]['count'] ) ) {
                        $infor[$info[$i]['city']]['count'] = 0;
                    }
                    if ( empty( $infor[$info[$i]['city']]['cn'] ) ) {
                        $infor[$info[$i]['city']]['cn'] = 0;
                    }

                    $infor[$info[$i]['city']]['city']=$info[$i]['city'];
					$infor[$info[$i]['city']]['count']+= (int)$info[$i]['count'];
					$infor[$info[$i]['city']]['cn']+=(int)$info[$i]['cn'];
					$sumcount+= (int)$info[$i]['count'];
					$sumcn+= (int)$info[$i]['cn'];
				}
			}
		}
		$this->P($infor);

		$this->assign("type",$type);
		$this->assign("sumcount",$sumcount);
		$this->assign("sumcn",$sumcn);
		$this->assign("infor",$infor);
		$this->assign("mode",$mode);
		$this->assign("mode",$mode);
		$this->assign("GUID",$GUID);
		$this->assign("amount",count($infor));
		$this->assign("name",$name);
	}
	
	private function formatpage($page){
		$page=(int)$page;
		if($page-1<0) $page=1;
		return $page;
	}

	private function getcitybyprovince($provinceid){
		$idlen=strlen($provinceid);
		$ret=array();
		foreach ($GLOBALS['channeng_placesMap'] as $key=>$value) {
			if($key!=$provinceid."0000"&&strlen($key)==4+$idlen&&substr($key,0,$idlen)==$provinceid&&$key%100==0) $ret[]=$value;
		}
		return $ret;
	}

	private function getallprovince(){
		$ret=array();
		$province=array();
		foreach ($GLOBALS['channeng_placesMap'] as $key=>$value) {
			if(substr($key,-4,-1)=="0000") {
				$key=str_replace("0000","",$key);
				$value=str_replace("省","",$value);
				$ret[$key]=$value;
			}
		}
		//$this->P($ret);
		return $province;
	}

	function P($arr){
		if($_GET['debug']==1){
		print"<pre>";print_r($arr);print"</pre>";
		}
	}
}
?>