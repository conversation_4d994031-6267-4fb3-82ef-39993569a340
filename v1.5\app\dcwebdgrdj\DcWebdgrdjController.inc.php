<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );

class DcWebdgrdjController extends AbstractController{
  
  public function __construct(){
    parent::__construct();
	
	$this->_action->setDao( new DcWebdgrdjDao( "MAIN" ) );
	//$this->_action->bizdao = new SystemDao('BIZ') ;
	$this->_action->steelhome= new DcWebdgrdjDao('91R'.$houzhui) ;//$houzhui 代表测试链接串 正式不用带
	$this->_action->drc= new DcWebdgrdjDao('DRCW','DRC') ;
  }

  public function _dopre(){
	  //echo "<pre/>";print_r($_SESSION);exit;
      //$this->_action->checkSession();
  }
	

  public function v_index() {
	$this->_action->index($this->_request);
  }
  public function do_save_price() {
	$this->_action->save_price($this->_request);
  }

	
  public function do_ThisDateDb() {
	$this->_action->DoThisDateDb($this->_request);
  }

  public function do_ThisDateDbList() {
	$this->_action->DoThisDateDbList($this->_request);
  }
}


?>