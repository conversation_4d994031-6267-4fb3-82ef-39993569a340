<?php 
$GLOBALS['type_date']=date("Y-m-d");
$GLOBALS['type_Y']=date("Y");
$GLOBALS['type_Y_m']=date("Y-m");
//$GLOBALS['type_date_h']=date("Y-m-d H:m:s");
// require_once('../../../../steelconf_v3/debug.php');
class DcDgRiDjAction extends AbstractAction
{
    public $stedao;
	
    public function __construct()
    {
        parent::__construct();
    } 
    public function index($params)
    {
      if(isset($params['curdate'])){
			//$GLOBALS['date']=$params['curdate'];
			$GLOBALS['type_date']=$params['curdate'];
			$GLOBALS['type_Y']=date("Y",strtotime($GLOBALS['type_date']));
			$GLOBALS['type_Y_m']=date("Y-m",strtotime($GLOBALS['type_date']));
			//$GLOBALS['type_date_h']=date("Y-m-d H:m:s",strtotime($GLOBALS['type_date']));
			//print_r($GLOBALS['type_date_h']);
		}
		$r=IsNgWorkDay($this->maindao,$GLOBALS['type_date']);
		if($r==0){
			echo "今天为休息日,不更新数据！";
			exit;
		}else{
		 
		//print_r($this->stedao);exit;
		
		//	$d=date("Y-m");
		//$now=date("Y-m-d");
		$d=$GLOBALS['type_Y_m'];
		$now=$GLOBALS['type_date'];
		$date_arr=$this->workday($now);//exit;南钢的前一个工作日
		 
		$row_ng_data=$this->ngxs($date_arr);//南钢碳结钢销售情况
		
		$new_array=$this->miansc_jgbianh($now);//主要市场价格变化
		$this->gzjyuc();//钢之家预测
		//echo "hello world";
		$HC_zd=$this->qhsp_ypbh($now);//煤焦期货表
		$this->dzprice_t($now);//大宗商品价格表
		//print_r($RB_zd);exit;
		$wx_ys=$new_array['1260191']['price'];
		$gp_zd=$new_array['6784112']['zd'];
		$dg_arr=$this->djjy($row_ng_data,$HC_zd,$wx_ys,$gp_zd);
		
		//print_r($wxscang_jhlyue);exit;*/
		//print_r($dg_arr);exit;
		if($_GET['issave']==1){
		//保存数据
		$sql="insert into `ng_price_model`(date,modeltype,createtime) values('".$GLOBALS['type_date']."','4',now())";
		// print_r($sql);//exit;
		$this->ngdao->execute($sql);
		$modelid=$this->ngdao->insert_id();
		
		
		$ins_dgrdj="insert into ng_price_model_detail(modelid,modelprice,modelprice_updown,modeloldprice,modelprice_type,	modelpirce_name,modelpirce_name_sort,Mid,uid,GUID,Ismakepricy_men,date,createtime) value('".$modelid."','".$dg_arr['dg_moxdj']."','".$dg_arr['dg_zd']."','".$dg_arr['oldprice']."','4','普碳薄带钢无锡市场日定价','1','-1','-1','','0','".$GLOBALS['type_date']."',now())";
		//print_r($ins_dgrdj);
		$this->ngdao->execute($ins_dgrdj);
		
		
		
		$title_date=$this->workday_gzj($date_arr[1]);
		$title_date=date("Y年n月j日",strtotime($title_date));
		$this->assign("title_date",$title_date);
		$title_now=date("Y年n月j日",strtotime($now));
		$this->assign("now",$title_now);
		$this->assign("last_daye",$date_arr[1]);
		$this->assign("date_arr",$date_arr);
		$this->assign("d",$d);
		}
		//$this->assign("now",$now);
		}
       } 
	//南钢南钢碳结钢销售情况
	public function ngxs($date_arr)
	{
		
		$sql="select Value,DataMark,dta_ym  from ng_data_table where DataMark in ('Ngdg0015','Ngdg0007','Ngdg0008','Ngdg0016','Ngdg0001','Ngdg0017','Ngdg0002')
       		  and   dta_ym <='".$GLOBALS['type_date']."' and   dta_ym >='".date("Y-m-d",strtotime("-15 day",strtotime($date_arr[1])))."' order by dta_ym desc ;";
		
       $row_ng=$this->ngdao->query($sql);
	   $d = array();//获取工作日日期
		$row_ng_data = array();
		foreach($row_ng as $tmp){
			$day =$tmp['dta_ym'];
			if(!in_array($day,$d))
			{
				$d[]=$day;
			}
			if(strpos($tmp['DataMark'],'-') !== false){
				$datemark=str_replace('-','_',$tmp['DataMark']);
			}else{
				$datemark=$tmp['DataMark'];
			}
			$row_ng_data[$day][$datemark]=round( $tmp['Value']);
			//print_r($day);
		}
		$d=array_slice($d,0,7);
		//print_r($d);
		$ngday=$d[0];
		//print_r($ngday);
		
		if(empty($row_ng)){
			$ngday=$this->workday_gzj($GLOBALS['type_date']);
			//print_r($ngday);
		}
		$ngday=date("Y年n月j日",strtotime($ngday));
		$this->assign("ngday",$ngday);
		$this->assign("day",$d);
		$this->assign("row_ng_data",$row_ng_data);
		return $row_ng_data;
		//print_r($row_ng_data);echo "<pre>";
		
	}
	public function miansc_jgbianh($params){
		//print_r(1111);exit;
		$last_day=$this->workday_gzj($params);
		//print_r($last_day);exit;
		$last_day_s=$last_day." "."00:00:00";
		$last_day_e=$last_day." "."23:59:59";
		$mid_arr=array('1260191','6760114','2960117','6784112','1260151','1260198');
		$mastertopid="('".implode("','",$mid_arr)."')";
		//$mastertopid="('1260191','6760147','2960117','6784112')";
		$sql="select articlename,material,factoryarea as gc,oldpricemk as oldprice,pricemk as price,mastertopid from marketconditions where mastertopid in ".$mastertopid."and mconmanagedate > '".$last_day_s."' and mconmanagedate < '".$last_day_e."'";//7位价格id
		//print_r($sql);
		$all_data=$this->maindao->query($sql);
		$new_array=array();
		
		//国内主要市场螺纹钢价格变化
		//$topicture="('074112','124112','084112','888411')";
		$top_arr=array('074112','124112','084112','888411');
		$topicture="('".implode("','",$top_arr)."')";
		$t_sql="select articlename,material,factoryarea as gc,oldpricemk as oldprice,pricemk as price,topicture from marketconditions where topicture in ".$topicture."and mconmanagedate > '".$last_day_s."' and mconmanagedate < '".$last_day_e."'";//六位价格id
		$all_t_data=$this->maindao->query($t_sql);
		//print_r($t_sql);
		$new_array=array();
		$new_tarray=array();
		
		//print_r($all_data);exit;七位价格id对应数据
		foreach($all_data as $val){
			$val['zd'] = $val['price'] - $val['oldprice'];
			$val['zd']=$this->zd_color($val['zd']);
			$new_array[$val['mastertopid']]=$val;
			
		}
		foreach($mid_arr as $mid){
			if($new_array[$mid]==""){
				$new_array[$mid]="-";
			}
		}
		//六位价格id对应数据
		foreach($all_t_data as $val){
			$val['zd'] = $val['price'] - $val['oldprice'];
			$val['zd']=$this->zd_color($val['zd']);
			$new_tarray[$val['topicture']]=$val;
		}
		foreach($top_arr as $tid){
			if($new_tarray[$tid]==""){
				$new_tarray[$tid]="-";
			}
		}
		$mainday=date("Y年n月j日",strtotime($last_day));
		$this->assign("mainday",$mainday);
		$this->assign("new_array",$new_array);
		$this->assign("new_tarray",$new_tarray);
		
		//print_r($new_array);
		return $new_array;
		
	}
	//期货夜盘收盘价格与涨跌
	public function qhsp_ypbh($params){
		
		
		$code="('RB','HC','I','JM','J')";
		$sql="select price,zhangdie,day,code,id from steelhome.kmjg_qhprice where code in ".$code." and time<'17:00:00' and day<='".$GLOBALS['type_date']."'  order by day desc limit 5";
		//print_r($sql);exit;
		$res=$this->maindao->query($sql);
		$temp_id=array();
		foreach($res as $key=>$val){
			$temp_id[]=$val['id'];
		}
		sort($temp_id);
		//print_r($temp_id);exit;
		//print_r($res);exit;
		//$qihday=date("Y年n月j日",strtotime($last_day));
		//$this->assign("qihday",$qihday);
		//$last_day=$this->workday_gzj($last_day);
		//$code="('RB','HC','I','JM','J')";
		$sql_t="select price,zhangdie,day,code from steelhome.kmjg_qhprice where code in ".$code." and id<'".$temp_id[0]."'  order by id desc limit 5";
		$res_t=$this->maindao->query($sql_t);
		$price_arr=array();
		
		//print_r($res_t);exit;
		foreach ($res as $price){
			if($price['price']==""){
				$price['price']="-";
			}
			foreach($res_t as $key=>$val){
				if($val['code']==$price['code']){
					if($val["code"]=="I"||$val["code"]=="JM"||$val["code"]=="J") {
						$price['zd']=sprintf("%.1f",$price['price']-$val['price']);
						$price['zdfu']=round($price['zd']/$val['price']*100,2);
					}else{
						$price['zd']=$price['price']-$val['price'];
						$price['zdfu']=round($price['zd']/$val['price']*100,2);
					}
				}
			}
			if($price["code"]=="I"||$price["code"]=="JM"||$price["code"]=="J"){
				$price['price']=sprintf("%.1f",$price["price"]);
			}
			$price_arr[$price['code']]=$price;
			$price_arr[$price['code']]['zhangdie']=$this->zd_color($price_arr[$price['code']]['zd']);
			$price_arr[$price['code']]['zhangdie1']=$price_arr[$price['code']]['zd'];
			$price_arr[$price['code']]['zhangdiefu']=$this->zd_color($price_arr[$price['code']]['zdfu']."%");
		}
		//print_r($price_arr);
		$this->assign("price_arr",$price_arr);
		$this->assign("date",$date);
		return $price_arr['HC']['zhangdie1'];
	}
	//大宗商品价格表
	public function dzprice_t($now){
		$sql="select * from  steelhome.dollar_dzprice where  type in ('1','2','3','4','5','6','9') and date<='".$GLOBALS['type_date']."' order by id desc limit 7";
		//$res=$this->stedao->query($sql);测试
		$res=$this->maindao->query($sql);//正式
		$dz_res=array();
		foreach($res as $val){
			
			
			$time = strtotime($val['date']) - 3600*24;
			$forworddate=date("Y-m-d",$time);
			$dsql="select value  from  steelhome.dollar_dzprice where  type='".$val['type']."' and date<='".$forworddate."' order by id desc limit 1";
			$dres=$this->maindao->getOne($dsql);//正式
			$down=$val['value']-$dres;//this by wufan
			
		//	$val['zdfu']=$val['updown']/($val['value']-$val['updown'])*100;
			//if($val['type']==1){
				if($down<0.00 && $down > 0){
					$dz_res['zd'.$val['type']]=number_format($down,4,'.','');
					//$dz_res['zdfu'.$val['type']]=number_format($val['zdfu'],4,'.','');
				}else if($down<-0.00 && $down > -0.01){
					$dz_res['zd'.$val['type']]=number_format($down,4,'.','');
					//$dz_res['zdfu'.$val['type']]=number_format($val['zdfu'],4,'.','');
				}else{
					$dz_res['zd'.$val['type']]=number_format($down,2,'.','');
					//$dz_res['zdfu'.$val['type']]=number_format($val['zdfu'],2,'.','');
				}
				$val['zdfu']=$dz_res['zd'.$val['type']]/($val['value']-$dz_res['zd'.$val['type']])*100;
				$dz_res['zdfu'.$val['type']]=round($val['zdfu'],2);
				$dz_res[$val['type']]=number_format($val['value'],2,'.','');
				$dz_res['zdfu'.$val['type']]=$this->zd_color($dz_res['zdfu'.$val['type']]."%");
				$dz_res['zd'.$val['type']]=$this->zd_color($dz_res['zd'.$val['type']]);
				//print_r($dz_res['zd'.$val['type']]);
			if($dz_res[$val['type']]==""){
				$dz_res[$val['type']]='-';
			}
			if($val['updown']="0"){
				$dz_res['zd'.$val['type']]='-';
			}
		}
		$meiyday=$res[0]['date'];
		//print_r($res);
		if(empty($res)){
			$meiyday=$this->workday_gzj($GLOBALS['type_date']);
		}
		$meiyday=date("Y年n月j日",strtotime($meiyday));
		$this->assign("meiyday",$meiyday);
		$this->assign("dz_res",$dz_res);
	}
	public function gzjyuc(){
		$last_day=date("Y-m-d",strtotime($GLOBALS['type_date']) - 3600*24);
		//$date_arr=$this->workday($GLOBALS['type_date']);
		//print_r($row_ng_data);
		//$last_day=;
		$sql="select * from NgGZJForcast where Type='4' and CDate ='".$last_day."' order by CDate desc limit 1";
		//print_r($sql);
		$info=$this->maindao->getRow($sql);
		if($info){
			if(abs($info['ZhangDie'])<DELTA){
			$info['ZhangDie']="持平";
			}else if($info['ZhangDie']>DELTA){
				$info['ZhangDie']='上涨'.$info['ZhangDie'].'元/吨';
			}else{
				$info['ZhangDie']="下跌".abs($info['ZhangDie']).'元/吨';
			}
		}else{
			$info['ZhangDie']='无';
		}
		//print_r($info);
		$this->assign("info",$info);
	}
	//定价建议-----带钢
	public function djjy($row_ng_data,$HC_zd,$wx_ys,$gp_zd){
		
		//$now=date("Y-m-d");
		$now=$GLOBALS['type_date'];
		$date_arr=$this->workday($now);
		//print_r($row_ng_data);
		$last_day=$date_arr[1];
		
		$sum=0;
		$next_five=array();
		//print_r($last_day);
		$kuc=$row_ng_data[$last_day]['Ngdg0016'];//库存
		for($i=1;$i<=5;$i++){
			$ng_date=$date_arr[$i];
			$sum=$sum+$row_ng_data[$ng_date]['Ngdg0016'];
			$next_five[$i]=$row_ng_data[$ng_date]['Ngdg0016'];
		}//最近5天库存的量
		//print_r($row_ng_data);
		$price=$row_ng_data[$last_day]['Ngdg0002'];//带钢最近价格
		//print_r($last_day);
		//print_r($row_ng_data);
		$max=array_search(max($next_five),$next_five);
		$min=array_search(min($next_five),$next_five);
		$kuc_max=$next_five[$max];
		$kuc_min=$next_five[$min];
		//echo $next_five[$kuc_max];
		$AB=$price-$wx_ys;//南钢带-无锡岩松价格
		$avg_kuc=$sum/5;
		$ng_zd;
		
		//print_r($AB);
		$zd=(float)$HC_zd;
		//print_r($last_date);exit;
		//$last_day="2017-12-04";
		//print_r($AB);
		$n=date("N",strtotime($now));
		if($n=="1"){
			$zd=(float)$gp_zd;
		}
		if($kuc <= $avg_kuc){
			if($kuc != $kuc_min){
				if($AB-20 > 0 ){
					$ng_zd=(int)(($zd-($AB-20))/10+0.5)*10;
					
				}else if($AB-10 < 0){
					$ng_zd=(int)(($zd-($AB-10))/10+0.5)*10;
					
				}else if( $AB>=10  && $AB<=20){
					$ng_zd=(int)($zd/10+0.5)*10;
				}
				
			}else{
				if($AB-20 > 0 ){
					$ng_zd=(int)(($zd-($AB-20))*1.2/10+0.5)*10;
					
				}else if($AB-10 < 0){
					$ng_zd=(int)(($zd-($AB-10))*1.2/10+0.5)*10;
					
				}else if( $AB>=10  && $AB  <=20){
					$ng_zd=(int)($zd*1.2/10+0.5)*10;
				}
			}
			
		}else{
			if($kuc != $kuc_max){
				//print_r($AB);
				if($AB-20 > 0 ){
					$ng_zd=(int)(($zd-($AB-20))*0.9/10+0.5)*10;
					
				}else if($AB-10 < 0){
					$ng_zd=(int)(($zd-($AB-10))*0.9/10+0.5)*10;
					
				}else if( $AB>=10  && $AB <=20){
					$ng_zd=(int)($zd*0.9/10+0.5)*10;
				}
				
			}else{
				if($AB-20 > 0 ){
				//	print_r($zd);
					$ng_zd=((int)(($zd-($AB-20))*0.8/10+0.5))*10;
					//print_r($ng_zd);
					
				}else if($AB-10 < 0){
					$ng_zd=(int)(($zd-($AB-10))*0.8/10+0.5)*10;
					
				}else if( $AB>=10 && $AB <=20){
					
					$ng_zd=(int)($zd*0.8/10+0.5)*10;
				}
			}
			
		}
		
		$ng_zd=(int)($ng_zd/10)*10;
		$artificial_last_day=date("Y-m-d",strtotime($GLOBALS['type_date']) - 3600*24);
		$artificial_zd=$this->maindao->getRow("select ZhangDie from NgGZJForcast where Type=3 and CDate='".$artificial_last_day."'");
		if($artificial_zd){
			$artificial_zd=$artificial_zd['ZhangDie'];
			//$artificial_zd=-40;
			$zd_cz=abs($ng_zd-$artificial_zd);
			//print_r($ng_zd);
			//print_r($artificial_zd);
			//print_r($zd_cz);
			if($ng_zd>0 && $artificial_zd < 0){
				$ng_zd=$ng_zd+$artificial_zd;
			}else if($ng_zd < 0 && $artificial_zd > 0){
				$ng_zd=$ng_zd+$artificial_zd;
			}else if($ng_zd == 0 && $artificial_zd == 0){
				$ng_zd=0;
			}else{
				if($zd_cz>30){
					$ng_zd=$artificial_zd;
				}
			}
		}
		//echo "<pre>";
		//print_r($ng_zd);
		$dg_moxdj=$row_ng_data[$date_arr[1]]['Ngdg0002']+$ng_zd;
		$this->assign("dg_moxdj",$dg_moxdj);
	//	print_r($ng_zd);
	//echo "<pre>";
	//print_r($dg_moxdj);
	//echo "<pre>";
		$this->assign("dg_zd",$ng_zd);
		$nj_arr=array('dg_moxdj'=>$dg_moxdj,'dg_zd'=>$ng_zd,'oldprice'=>$price);
		//exit;
		return $nj_arr;
		
		
	}
	
	//保存数据
	public function savedate($params){
		//$date=date("Y-m-d");
		$title="带钢日定价";
		if(isset($params['curdate'])){
			$GLOBALS['type_date']=$params['curdate'];
			$GLOBALS['type_Y']=date("Y",strtotime($GLOBALS['type_date']));
			$GLOBALS['type_Y_m']=date("Y-m",strtotime($GLOBALS['type_date']));
			//$GLOBALS['type_date_h']=date("Y-m-d H:m:s",strtotime($GLOBALS['type_date']));
			$url = DC_URL.DCURL."/dcdgridj.php?view=index&issave=1&curdate=".$params['curdate'];
		}else{
			$url = DC_URL.DCURL."/dcdgridj.php?view=index&issave=1";
		}
		//print_r($GLOBALS['type_date']);
		$r=IsNgWorkDay($this->maindao,$GLOBALS['type_date']);
		if($r==0){
			echo "今天为休息日,不更新数据！";
			exit;
		}else{
		//$model=$GLOBALS['type_date'];
		$model=date("Y年n月j日",strtotime($GLOBALS['type_date']));
		$modeltitle=$model.$title;
		//$url = $_SERVER['SERVER_NAME'].$_SERVER['REQUEST_URI'];
		//print_r($_SERVER);exit;
		
		//print_r($_SERVER);
		//echo "111";
		include '/usr/local/www/libs/phpQuery/phpQuery.php';
		//echo "222";
		phpQuery::newDocumentFile($url);
		//echo "333";
		$html=pq("body")->html();
		$html_huz.=pq(".huiz")->html();
		$html_huz=str_replace("六、钢之家判断","1、钢之家判断",$html_huz);
		$html_huz=str_replace("七、今日价格调整建议","2、价格模型建议",$html_huz);
		/*$html_huz="<h3 class='h3'>1、钢之家判断</h3>";
		$html_huz.=pq(".huiz h4:eq(0)")->html();
		$html_huz.="<h3 class='h3'>2、价格模型建议</h3>";
		$html_huz.=pq(".huiz h4:eq(1)")->html();*/
		$huiz_content=htmlentities($html_huz, ENT_QUOTES,"UTF-8");
		//echo "444";
		
		$modelcontent=htmlentities($html, ENT_QUOTES,"UTF-8");
		//echo "555";
		$sql="update `ng_price_model` set modeltitle='".$modeltitle."',modelcontent='".$modelcontent."',HuiZong1='".$huiz_content."'  where modeltype='4' order by id desc limit 1";
		//echo "666";
		$this->ngdao->execute($sql);
		//echo "777";
		//echo $modelcontent;
		print_r($html_huz);
		}
		
	}
	//获取钢之家的前一个工作日
	
	function workday_gzj($params){
		
		$date;
		include_once("/etc/steelconf/config/isholiday.php");

		for($i=0;$i<=20;$i++){
			
			$last_day=date("Y-m-d",(strtotime($params) - 3600*24*($i+1)));
			
			
			$isexist=_isholiday($last_day);
			if($isexist==1){
				//print_r(1111);
				continue;
			}else{
				//print_r(2222);
				$date=$last_day;
				break;
			}
			
		}
		//print_r(11111);exit;
		//print_r($date);exit;
		return $date;
	}
	   //获取南钢工作日的函数
	public function workday($date){
		//echo $date="2017-10-07";
		 $sql="select date,isholiday from steelhome.holiday  where date <='".$date."' order by date desc limit 10"; 
		//$sql="select * from holiday  where date <='2017-10-7' order by date desc limit 20"; 
		 $res=$this->maindao->aquery($sql);
		 $i = 1;
		 $last_date = $date;
		 $date=array();
		 
		 while( $i < 8 ){
			$last_date=date("Y-m-d",strtotime("-1 day",strtotime($last_date))); 
			if( isset($res[$last_date]) ){
			
				if($res[$last_date]=="0"){
					
					$date[$i]=$last_date;
					$i++;
					//break;
				}else{
					//continue;
				}
			}else{
				
				$n=date("N",strtotime($last_date));
				if($n==7){
					//continue;
				}else{
					
					$date[$i]=$last_date;
					$i++;
					//break;
				}
				
			}
		 }
		 //	print_r($date);
		 return $date;
		
	   }
	   public function zd_color($zd){
		if($zd==0){
				 $zd = "<font color=black>".$zd."</font>";
			}else if($zd<0){
				$zd = "<font color=green>".$zd."</font>";
			}else{
				
				$zd = "<font color=red>+".$zd."</font>";
				
			}
			 return $zd;
	   }
	  
} 
?>