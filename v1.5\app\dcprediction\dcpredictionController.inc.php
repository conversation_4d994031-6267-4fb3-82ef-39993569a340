<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );

class dcpredictionController extends AbstractController{
  
  public function __construct(){
    parent::__construct();
	$this->_action->setDao( new dcpredictionDao("91R") );
	//$this->_action->t1Dao=new dcpredictionDao("MAIN");
  }

	public function v_index(){
		$this->_action->index( $this->_request );
	}

	public function v_current(){
		$this->_action->currentview( $this->_request );
	}

	public function v_previous(){
		$this->_action->previousview( $this->_request );
	}
	public function v_previousnew(){
		$this->_action->previousview( $this->_request );
	}

	public function v_dataview(){
		$this->_action->dataview( $this->_request );
	}

	public function do_export(){
		$this->_action->exportdata( $this->_request );
	}
}
?>