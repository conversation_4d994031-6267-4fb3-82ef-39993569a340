<?php
//header('Content-Type:text/html;charset=gb18030');
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );

class customerdataController extends AbstractController{
  
  public function __construct(){
    parent::__construct();
	$this->_action->setDao( new customerdataDao("91R") );
	$this->_action->gcdao = new customerdataDao('GC') ;
	$this->_action->t1Dao=new customerdataDao("MAIN");
  }

	public function v_index($params){
		$this->_action->index( $this->_request );
	}

	public function do_getcal(){
		$this->_action->getcal( $this->_request );
	}

	public function do_delcal(){
		$this->_action->delcal( $this->_request );
	}

	public function v_detail($params){
		$this->_action->detail( $this->_request );
	}

	public function do_getdata(){
		$this->_action->getdata( $this->_request );
	}

	public function v_catalogue($params){
		$this->_action->catalogue( $this->_request );
	}

	public function do_docatalogue(){
		$this->_action->docatalogue( $this->_request );
	}

	public function v_data($params){
		$this->_action->data( $this->_request );
	}

	public function do_dodata(){
		$this->_action->dodata( $this->_request );
	}

	public function v_datadetail($params){
		$this->_action->datadetail( $this->_request );
	}
	
	public function do_getdatadetail(){
		$this->_action->getdatadetail( $this->_request );
	}

	public function v_adddatadetail($params){
		$this->_action->adddatadetail( $this->_request );
	}
	
	public function do_dodatadetail(){
		$this->_action->dodatadetail( $this->_request );
	}

	public function do_getselectcal(){
		$this->_action->getselectcal( $this->_request );
	}
	
	public function do_importexcel(){
		$this->_action->importexcel( $this->_request );
	}
	
	public function v_datadat($params){
		$this->_action->datadat( $this->_request );
	}
	
	public function do_datadat(){
		$this->_action->dodatadat( $this->_request );
	}

	public function v_importdatadetail($params){
		$this->_action->importdatadetail( $this->_request );
	}
	
	public function do_getimportdatadetail(){
		$this->_action->getimportdatadetail( $this->_request );
	}
}
?>