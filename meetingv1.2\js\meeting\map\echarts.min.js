!function(t,e){"function"==typeof define&&define.amd?define([],e):"object"==typeof module&&module.exports?module.exports=e():t.echarts=e()}(this,function(){var t,e;!function(){function i(t,e){if(!e)return t;if(0===t.indexOf(".")){var i=e.split("/"),n=t.split("/"),r=i.length-1,a=n.length,o=0,s=0;t:for(var l=0;a>l;l++)switch(n[l]){case"..":if(!(r>o))break t;o++,s++;break;case".":s++;break;default:break t}return i.length=r-o,n=n.slice(s),i.concat(n).join("/")}return t}function n(t){function e(e,o){if("string"==typeof e){var s=n[e];return s||(s=a(i(e,t)),n[e]=s),s}e instanceof Array&&(o=o||function(){},o.apply(this,r(e,o,t)))}var n={};return e}function r(e,n,r){for(var s=[],l=o[r],u=0,h=Math.min(e.length,n.length);h>u;u++){var c,d=i(e[u],r);switch(d){case"require":c=l&&l.require||t;break;case"exports":c=l.exports;break;case"module":c=l;break;default:c=a(d)}s.push(c)}return s}function a(t){var e=o[t];if(!e)throw new Error("No "+t);if(!e.defined){var i=e.factory,n=i.apply(this,r(e.deps||[],i,t));"undefined"!=typeof n&&(e.exports=n),e.defined=1}return e.exports}var o={};e=function(t,e,i){o[t]={id:t,deps:e,factory:i,defined:0,exports:{},require:n(t)}},t=n("")}();var i="undefined",n="applyTransform",r="parent",a="retrieve",o="queryComponents",s="ecModel",l="category",u="option",h="getShallow",c="getItemModel",d="ordinal",f="dimensions",p="getLabel",m="getExtent",v="contain",g="isString",y="function",_="isArray",x="replace",b="zlevel",w="traverse",T="splice",M="dispose",A="series",C="extend",S="remove",P="isObject",I="colorStops",z="update",k="create",L="dataIndex",D="getData",O="indexOf",R="length",E="ignore",B="canvasSupported",N="animation",G="string",Z="prototype",F="toLowerCase",V="zrender/core/vector",H="zrender/core/env",q="setStyle",W="position",U="bottom",X="center",j="middle",Y="getHeight",$="getWidth",Q="target",K="height",J="getBoundingRect",te="getFont",ee="textAlign",ie="textStyle",ne="getModel",re="defaults",ae="coordinateSystem",oe="removeAll",se="zrender/core/util",le="require";e("echarts/chart/line",[le,se,"../echarts","./line/LineSeries","./line/LineView","../visual/symbol","../layout/points","../processor/dataSample","../component/grid"],function(t){var e=t(se),i=t("../echarts"),n=i.PRIORITY;t("./line/LineSeries"),t("./line/LineView"),i.registerVisual(e.curry(t("../visual/symbol"),"line","circle","line")),i.registerLayout(e.curry(t("../layout/points"),"line")),i.registerProcessor(n.PROCESSOR.STATISTIC,e.curry(t("../processor/dataSample"),"line")),t("../component/grid")}),e("echarts/component/tooltip",[le,"./tooltip/TooltipModel","./tooltip/TooltipView","../echarts"],function(t){t("./tooltip/TooltipModel"),t("./tooltip/TooltipView"),t("../echarts").registerAction({type:"showTip",event:"showTip",update:"none"},function(){}),t("../echarts").registerAction({type:"hideTip",event:"hideTip",update:"none"},function(){})}),e("echarts/component/dataZoom",[le,"./dataZoom/typeDefaulter","./dataZoom/DataZoomModel","./dataZoom/DataZoomView","./dataZoom/SliderZoomModel","./dataZoom/SliderZoomView","./dataZoom/InsideZoomModel","./dataZoom/InsideZoomView","./dataZoom/dataZoomProcessor","./dataZoom/dataZoomAction"],function(t){t("./dataZoom/typeDefaulter"),t("./dataZoom/DataZoomModel"),t("./dataZoom/DataZoomView"),t("./dataZoom/SliderZoomModel"),t("./dataZoom/SliderZoomView"),t("./dataZoom/InsideZoomModel"),t("./dataZoom/InsideZoomView"),t("./dataZoom/dataZoomProcessor"),t("./dataZoom/dataZoomAction")}),e("echarts/component/grid",[le,"../util/graphic",se,"../echarts","../coord/cartesian/Grid","./axis"],function(t){var e=t("../util/graphic"),i=t(se),n=t("../echarts");t("../coord/cartesian/Grid"),t("./axis"),n.extendComponentView({type:"grid",render:function(t){this.group[oe](),t.get("show")&&this.group.add(new e.Rect({shape:t[ae].getRect(),style:i[re]({fill:t.get("backgroundColor")},t.getItemStyle()),silent:!0,z2:-1}))}}),n.registerPreprocessor(function(t){t.xAxis&&t.yAxis&&!t.grid&&(t.grid={})})}),e("echarts/component/title",[le,"../echarts","../util/graphic","../util/layout"],function(t){var e=t("../echarts"),i=t("../util/graphic"),n=t("../util/layout");e.extendComponentModel({type:"title",layoutMode:{type:"box",ignoreSize:!0},defaultOption:{zlevel:0,z:6,show:!0,text:"",target:"blank",subtext:"",subtarget:"blank",left:0,top:0,backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,padding:5,itemGap:10,textStyle:{fontSize:18,fontWeight:"bolder",color:"#333"},subtextStyle:{color:"#aaa"}}}),e.extendComponentView({type:"title",render:function(t,e,r){if(this.group[oe](),t.get("show")){var a=this.group,o=t[ne](ie),s=t[ne]("subtextStyle"),l=t.get(ee),u=t.get("textBaseline"),h=new i.Text({style:{text:t.get("text"),textFont:o[te](),fill:o.getTextColor()},z2:10}),c=h[J](),d=t.get("subtext"),f=new i.Text({style:{text:d,textFont:s[te](),fill:s.getTextColor(),y:c[K]+t.get("itemGap"),textBaseline:"top"},z2:10}),p=t.get("link"),m=t.get("sublink");h.silent=!p,f.silent=!m,p&&h.on("click",function(){window.open(p,"_"+t.get(Q))}),m&&f.on("click",function(){window.open(m,"_"+t.get("subtarget"))}),a.add(h),d&&a.add(f);var v=a[J](),g=t.getBoxLayoutParams();g.width=v.width,g[K]=v[K];var y=n.getLayoutRect(g,{width:r[$](),height:r[Y]()},t.get("padding"));l||(l=t.get("left")||t.get("right"),l===j&&(l=X),"right"===l?y.x+=y.width:l===X&&(y.x+=y.width/2)),u||(u=t.get("top")||t.get(U),u===X&&(u=j),u===U?y.y+=y[K]:u===j&&(y.y+=y[K]/2),u=u||"top"),a.attr(W,[y.x,y.y]);var _={textAlign:l,textVerticalAlign:u};h[q](_),f[q](_),v=a[J]();var x=y.margin,b=t.getItemStyle(["color","opacity"]);b.fill=t.get("backgroundColor");var w=new i.Rect({shape:{x:v.x-x[3],y:v.y-x[0],width:v.width+x[1]+x[3],height:v[K]+x[0]+x[2]},style:b,silent:!0});i.subPixelOptimizeRect(w),a.add(w)}}})}),e("echarts/echarts",[le,H,"./model/Global","./ExtensionAPI","./CoordinateSystem","./model/OptionManager","./model/Component","./model/Series","./view/Component","./view/Chart","./util/graphic","./util/model","./util/throttle","zrender",se,"zrender/tool/color","zrender/mixin/Eventful","zrender/core/timsort","./visual/seriesColor","./preprocessor/backwardCompat","./loading/default","./data/List","./model/Model","./util/number","./util/format","zrender/core/matrix",V],function(t){function e(t){return function(e,i,n){e=e&&e[F](),ge[Z][t].call(this,e,i,n)}}function i(){ge.call(this)}function n(t,e,n){function r(t,e){return t.prio-e.prio}n=n||{},typeof e===G&&(e=Ne[e]),this.id,this.group,this._dom=t;var a=this._zr=pe.init(t,{renderer:n.renderer||"canvas",devicePixelRatio:n.devicePixelRatio,width:n.width,height:n[K]});this._throttledZrFlush=fe.throttle(me.bind(a.flush,a),17),this._theme=me.clone(e),this._chartsViews=[],this._chartsMap={},this._componentsViews=[],this._componentsMap={},this._api=new te(this),this._coordSysMgr=new ee,ge.call(this),this._messageCenter=new i,this._initEvents(),this.resize=me.bind(this.resize,this),this._pendingActions=[],ye(Be,r),ye(Re,r),a[N].on("frame",this._onframe,this)}function r(t,e,i){var n,r=this._model,a=this._coordSysMgr.getCoordinateSystems();e=de.parseFinder(r,e);for(var o=0;o<a[R];o++){var s=a[o];if(s[t]&&null!=(n=s[t](r,e,i)))return n}}function a(t,e){var i=this._model;i&&i.eachComponent({mainType:"series",query:e},function(n){var r=this._chartsMap[n.__viewId];r&&r.__alive&&r[t](n,i,this._api,e)},this)}function o(t,e){var i=De[t.type],n=i.actionInfo,r=n[z]||z;this[Se]=!0;var a=[t],o=!1;t.batch&&(o=!0,a=me.map(t.batch,function(e){return e=me[re](me[C]({},e),t),e.batch=null,e}));for(var s,l=[],u="highlight"===t.type||"downplay"===t.type,h=0;h<a[R];h++){var c=a[h];s=i.action(c,this._model),s=s||me[C]({},c),s.type=n.event||s.type,l.push(s),u&&ke[r].call(this,c)}"none"===r||u||(this[Ie]?(ke.prepareAndUpdate.call(this,t),this[Ie]=!1):ke[r].call(this,t)),s=o?{type:n.event||t.type,batch:l}:l[0],this[Se]=!1,!e&&this._messageCenter.trigger(s.type,s)}function s(t){for(var e=this._pendingActions;e[R];){var i=e.shift();o.call(this,i,t)}}function l(t,e,i){var n=this._api;_e(this._componentsViews,function(r){var a=r.__model;r[t](a,e,n,i),W(a,r)},this),e.eachSeries(function(r){var a=this._chartsMap[r.__viewId];a[t](r,e,n,i),W(r,a),v(r,a)},this),m(this._zr,e)}function u(t,e){for(var i="component"===t,n=i?this._componentsViews:this._chartsViews,r=i?this._componentsMap:this._chartsMap,a=this._zr,o=0;o<n[R];o++)n[o].__alive=!1;e[i?"eachComponent":"eachSeries"](function(t,o){if(i){if(t===A)return}else o=t;var s=o.id+"_"+o.type,l=r[s];if(!l){var u=oe.parseClassType(o.type),h=i?ue.getClass(u.main,u.sub):he.getClass(u.sub);if(!h)return;l=new h,l.init(e,this._api),r[s]=l,n.push(l),a.add(l.group)}o.__viewId=s,l.__alive=!0,l.__id=s,l.__model=o},this);for(var o=0;o<n[R];){var s=n[o];s.__alive?o++:(a[S](s.group),s[M](e,this._api),n[T](o,1),delete r[s.__id])}}function h(t,e){_e(Re,function(i){i.func(t,e)})}function c(t){var e={};t.eachSeries(function(t){var i=t.get("stack"),n=t[D]();if(i&&"list"===n.type){var r=e[i];r&&(n.stackedOn=r),e[i]=n}})}function d(t,e){var i=this._api;_e(Be,function(n){n.isLayout&&n.func(t,i,e)})}function f(t,e){var i=this._api;t.clearColorPalette(),t.eachSeries(function(t){t.clearColorPalette()}),_e(Be,function(n){n.func(t,i,e)})}function p(t,e){var i=this._api;_e(this._componentsViews,function(n){var r=n.__model;n.render(r,t,i,e),W(r,n)},this),_e(this._chartsViews,function(t){t.__alive=!1},this),t.eachSeries(function(n){var r=this._chartsMap[n.__viewId];r.__alive=!0,r.render(n,t,i,e),r.group.silent=!!n.get("silent"),W(n,r),v(n,r)},this),m(this._zr,t),_e(this._chartsViews,function(e){e.__alive||e[S](t,i)},this)}function m(t,e){var i=t.storage,n=0;i[w](function(t){t.isGroup||n++}),n>e.get("hoverLayerThreshold")&&!j.node&&i[w](function(t){t.isGroup||(t.useHoverLayer=!0)})}function v(t,e){var i=0;e.group[w](function(t){"group"===t.type||t[E]||i++});var n=+t.get("progressive"),r=i>t.get("progressiveThreshold")&&n&&!j.node;r&&e.group[w](function(t){t.isGroup||(t.progressive=r?Math.floor(i++/n):-1,r&&t.stopAnimation(!0))});var a=t.get("blendMode")||null;e.group[w](function(t){t.isGroup||t[q]("blend",a)})}function W(t,e){var i=t.get("z"),n=t.get(b);e.group[w](function(t){"group"!==t.type&&(null!=i&&(t.z=i),null!=n&&(t[b]=n))})}function X(t){function e(t,e){for(var i=0;i<t[R];i++){var n=t[i];n[a]=e}}var i=0,n=1,r=2,a="__connectUpdateStatus";me.each(Oe,function(o,s){t._messageCenter.on(s,function(o){if(Fe[t.group]&&t[a]!==i){var s=t.makeActionFromEvent(o),l=[];me.each(Ze,function(e){e!==t&&e.group===t.group&&l.push(e)}),e(l,i),_e(l,function(t){t[a]!==n&&t.dispatchAction(s)}),e(l,r)}})})}var j=t(H),J=t("./model/Global"),te=t("./ExtensionAPI"),ee=t("./CoordinateSystem"),ie=t("./model/OptionManager"),oe=t("./model/Component"),le=t("./model/Series"),ue=t("./view/Component"),he=t("./view/Chart"),ce=t("./util/graphic"),de=t("./util/model"),fe=t("./util/throttle"),pe=t("zrender"),me=t(se),ve=t("zrender/tool/color"),ge=t("zrender/mixin/Eventful"),ye=t("zrender/core/timsort"),_e=me.each,xe=1e3,be=5e3,we=1e3,Te=2e3,Me=3e3,Ae=4e3,Ce=5e3,Se="__flagInMainProcess",Pe="__hasGradientOrPatternBg",Ie="__optionUpdated";i[Z].on=e("on"),i[Z].off=e("off"),i[Z].one=e("one"),me.mixin(i,ge);var ze=n[Z];ze._onframe=function(){this[Ie]&&(this[Se]=!0,ke.prepareAndUpdate.call(this),this[Se]=!1,this[Ie]=!1)},ze.getDom=function(){return this._dom},ze.getZr=function(){return this._zr},ze.setOption=function(t,e,i){if(this[Se]=!0,!this._model||e){var n=new ie(this._api),r=this._theme,a=this._model=new J(null,null,r,n);a.init(null,null,r,n)}this.__lastOnlyGraphic=!(!t||!t.graphic),me.each(t,function(t,e){"graphic"!==e&&(this.__lastOnlyGraphic=!1)},this),this._model.setOption(t,Ee),i?this[Ie]=!0:(ke.prepareAndUpdate.call(this),this._zr.flush(),this[Ie]=!1),this[Se]=!1,s.call(this,!1)},ze.setTheme=function(){console.log("ECharts#setTheme() is DEPRECATED in ECharts 3.0")},ze[ne]=function(){return this._model},ze.getOption=function(){return this._model&&this._model.getOption()},ze[$]=function(){return this._zr[$]()},ze[Y]=function(){return this._zr[Y]()},ze.getRenderedCanvas=function(t){if(j[B]){t=t||{},t.pixelRatio=t.pixelRatio||1,t.backgroundColor=t.backgroundColor||this._model.get("backgroundColor");var e=this._zr,i=e.storage.getDisplayList();return me.each(i,function(t){t.stopAnimation(!0)}),e.painter.getRenderedCanvas(t)}},ze.getDataURL=function(t){t=t||{};var e=t.excludeComponents,i=this._model,n=[],r=this;_e(e,function(t){i.eachComponent({mainType:t},function(t){var e=r._componentsMap[t.__viewId];e.group[E]||(n.push(e),e.group[E]=!0)})});var a=this.getRenderedCanvas(t).toDataURL("image/"+(t&&t.type||"png"));return _e(n,function(t){t.group[E]=!1}),a},ze.getConnectedDataURL=function(t){if(j[B]){var e=this.group,i=Math.min,n=Math.max,r=1/0;if(Fe[e]){var a=r,o=r,s=-r,l=-r,u=[],h=t&&t.pixelRatio||1;me.each(Ze,function(r){if(r.group===e){var h=r.getRenderedCanvas(me.clone(t)),c=r.getDom().getBoundingClientRect();a=i(c.left,a),o=i(c.top,o),s=n(c.right,s),l=n(c[U],l),u.push({dom:h,left:c.left,top:c.top})}}),a*=h,o*=h,s*=h,l*=h;var c=s-a,d=l-o,f=me.createCanvas();f.width=c,f[K]=d;var p=pe.init(f);return _e(u,function(t){var e=new ce.Image({style:{x:t.left*h-a,y:t.top*h-o,image:t.dom}});p.add(e)}),p.refreshImmediately(),f.toDataURL("image/"+(t&&t.type||"png"))}return this.getDataURL(t)}},ze.convertToPixel=me.curry(r,"convertToPixel"),ze.convertFromPixel=me.curry(r,"convertFromPixel"),ze.containPixel=function(t,e){var i,n=this._model;return t=de.parseFinder(n,t),me.each(t,function(t,n){n[O]("Models")>=0&&me.each(t,function(t){var r=t[ae];if(r&&r.containPoint)i|=!!r.containPoint(e);else if("seriesModels"===n){var a=this._chartsMap[t.__viewId];a&&a.containPoint&&(i|=a.containPoint(e,t))}},this)},this),!!i},ze.getVisual=function(t,e){var i=this._model;t=de.parseFinder(i,t,{defaultMainType:"series"});var n=t.seriesModel,r=n[D](),a=t.hasOwnProperty("dataIndexInside")?t.dataIndexInside:t.hasOwnProperty(L)?r.indexOfRawIndex(t[L]):null;return null!=a?r.getItemVisual(a,e):r.getVisual(e)};var ke={update:function(t){var e=this._model,i=this._api,n=this._coordSysMgr,r=this._zr;if(e){e.restoreData(),n[k](this._model,this._api),h.call(this,e,i),c.call(this,e),n[z](e,i),f.call(this,e,t),p.call(this,e,t);var a=e.get("backgroundColor")||"transparent",o=r.painter;if(o.isSingleCanvas&&o.isSingleCanvas())r.configLayer(0,{clearColor:a});else{if(!j[B]){var s=ve.parse(a);a=ve.stringify(s,"rgb"),0===s[3]&&(a="transparent")}a[I]||a.image?(r.configLayer(0,{clearColor:a}),this[Pe]=!0,this._dom.style.background="transparent"):(this[Pe]&&r.configLayer(0,{clearColor:null}),this[Pe]=!1,this._dom.style.background=a)}}},updateView:function(t){var e=this._model;e&&(e.eachSeries(function(t){t[D]().clearAllVisual()}),f.call(this,e,t),l.call(this,"updateView",e,t))},updateVisual:function(t){var e=this._model;e&&(e.eachSeries(function(t){t[D]().clearAllVisual()}),f.call(this,e,t),l.call(this,"updateVisual",e,t))},updateLayout:function(t){var e=this._model;e&&(d.call(this,e,t),l.call(this,"updateLayout",e,t))},highlight:function(t){a.call(this,"highlight",t)},downplay:function(t){a.call(this,"downplay",t)},prepareAndUpdate:function(t){var e=this._model;u.call(this,"component",e),u.call(this,"chart",e),this.__lastOnlyGraphic?(_e(this._componentsViews,function(i){var n=i.__model;n&&"graphic"===n.mainType&&(i.render(n,e,this._api,t),W(n,i))},this),this.__lastOnlyGraphic=!1):ke[z].call(this,t)}};ze.resize=function(t){this[Se]=!0,this._zr.resize(t);var e=this._model&&this._model.resetOption("media");ke[e?"prepareAndUpdate":z].call(this),this._loadingFX&&this._loadingFX.resize(),this[Se]=!1,s.call(this)},ze.showLoading=function(t,e){if(me[P](t)&&(e=t,t=""),t=t||"default",this.hideLoading(),Ge[t]){var i=Ge[t](this._api,e),n=this._zr;this._loadingFX=i,n.add(i)}},ze.hideLoading=function(){this._loadingFX&&this._zr[S](this._loadingFX),this._loadingFX=null},ze.makeActionFromEvent=function(t){var e=me[C]({},t);return e.type=Oe[t.type],e},ze.dispatchAction=function(t,e){if(me[P](e)||(e={silent:!!e}),De[t.type]){if(this[Se])return void this._pendingActions.push(t);o.call(this,t,e.silent),e.flush?this._zr.flush(!0):e.flush!==!1&&j.browser.weChat&&this._throttledZrFlush(),s.call(this,e.silent)}},ze.on=e("on"),ze.off=e("off"),ze.one=e("one");var Le=["click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","globalout","contextmenu"];ze._initEvents=function(){_e(Le,function(t){this._zr.on(t,function(e){var i,n=this[ne](),r=e[Q];if("globalout"===t)i={};else if(r&&null!=r[L]){var a=r.dataModel||n.getSeriesByIndex(r.seriesIndex);i=a&&a.getDataParams(r[L],r.dataType)||{}}else r&&r.eventData&&(i=me[C]({},r.eventData));i&&(i.event=e,i.type=t,this.trigger(t,i))},this)},this),_e(Oe,function(t,e){this._messageCenter.on(e,function(t){this.trigger(e,t)},this)},this)},ze.isDisposed=function(){return this._disposed},ze.clear=function(){this.setOption({series:[]},!0)},ze[M]=function(){if(!this._disposed){this._disposed=!0;var t=this._api,e=this._model;_e(this._componentsViews,function(i){i[M](e,t)}),_e(this._chartsViews,function(i){i[M](e,t)}),this._zr[M](),delete Ze[this.id]}},me.mixin(n,ge);var De=[],Oe={},Re=[],Ee=[],Be=[],Ne={},Ge={},Ze={},Fe={},Ve=new Date-0,He=new Date-0,qe="_echarts_instance_",We={version:"3.3.2",dependencies:{zrender:"3.2.2"}};We.init=function(t,e,i){var r=new n(t,e,i);return r.id="ec_"+Ve++,Ze[r.id]=r,t.setAttribute&&t.setAttribute(qe,r.id),X(r),r},We.connect=function(t){if(me[_](t)){var e=t;t=null,me.each(e,function(e){null!=e.group&&(t=e.group)}),t=t||"g_"+He++,me.each(e,function(e){e.group=t})}return Fe[t]=!0,t},We.disConnect=function(t){Fe[t]=!1},We[M]=function(t){me.isDom(t)?t=We.getInstanceByDom(t):typeof t===G&&(t=Ze[t]),t instanceof n&&!t.isDisposed()&&t[M]()},We.getInstanceByDom=function(t){var e=t.getAttribute(qe);return Ze[e]},We.getInstanceById=function(t){return Ze[t]},We.registerTheme=function(t,e){Ne[t]=e},We.registerPreprocessor=function(t){Ee.push(t)},We.registerProcessor=function(t,e){typeof t===y&&(e=t,t=xe),Re.push({prio:t,func:e})},We.registerAction=function(t,e,i){typeof e===y&&(i=e,e="");var n=me[P](t)?t.type:[t,t={event:e}][0];t.event=(t.event||n)[F](),e=t.event,De[n]||(De[n]={action:i,actionInfo:t}),Oe[e]=n},We.registerCoordinateSystem=function(t,e){ee.register(t,e)},We.registerLayout=function(t,e){typeof t===y&&(e=t,t=we),Be.push({prio:t,func:e,isLayout:!0})},We.registerVisual=function(t,e){typeof t===y&&(e=t,t=Me),Be.push({prio:t,func:e})},We.registerLoading=function(t,e){Ge[t]=e};var Ue=oe.parseClassType;return We.extendComponentModel=function(t,e){var i=oe;if(e){var n=Ue(e);i=oe.getClass(n.main,n.sub,!0)}return i[C](t)},We.extendComponentView=function(t,e){var i=ue;if(e){var n=Ue(e);i=ue.getClass(n.main,n.sub,!0)}return i[C](t)},We.extendSeriesModel=function(t,e){var i=le;if(e){e="series."+e[x]("series.","");var n=Ue(e);i=oe.getClass(n.main,n.sub,!0)}return i[C](t)},We.extendChartView=function(t,e){var i=he;if(e){e[x]("series.","");var n=Ue(e);i=he.getClass(n.main,!0)}return i[C](t)},We.setCanvasCreator=function(t){me.createCanvas=t},We.registerVisual(Te,t("./visual/seriesColor")),We.registerPreprocessor(t("./preprocessor/backwardCompat")),We.registerLoading("default",t("./loading/default")),We.registerAction({type:"highlight",event:"highlight",update:"highlight"},me.noop),We.registerAction({type:"downplay",event:"downplay",update:"downplay"},me.noop),We.List=t("./data/List"),We.Model=t("./model/Model"),We.graphic=t("./util/graphic"),We.number=t("./util/number"),We.format=t("./util/format"),We.matrix=t("zrender/core/matrix"),We.vector=t(V),We.color=t("zrender/tool/color"),We.util={},_e(["map","each","filter",O,"inherits","reduce","filter","bind","curry",_,g,P,"isFunction",C,re,"clone"],function(t){We.util[t]=me[t]}),We.PRIORITY={PROCESSOR:{FILTER:xe,STATISTIC:be},VISUAL:{LAYOUT:we,GLOBAL:Te,CHART:Me,COMPONENT:Ae,BRUSH:Ce}},We}),e("echarts/scale/Time",[le,se,"../util/number","../util/format","./Interval"],function(t){var e=t(se),i=t("../util/number"),n=t("../util/format"),r=t("./Interval"),a=r[Z],o=Math.ceil,s=Math.floor,l=1e3,u=60*l,h=60*u,c=24*h,d=function(t,e,i,n){for(;n>i;){var r=i+n>>>1;t[r][2]<e?i=r+1:n=r}return i},f=r[C]({type:"time",getLabel:function(t){var e=this._stepLvl,i=new Date(t);return n.formatTime(e[0],i)},niceExtent:function(t,e,n){var r=this._extent;if(r[0]===r[1]&&(r[0]-=c,r[1]+=c),r[1]===-1/0&&1/0===r[0]){var a=new Date;r[1]=new Date(a.getFullYear(),a.getMonth(),a.getDate()),r[0]=r[1]-c}this.niceTicks(t);var l=this._interval;e||(r[0]=i.round(s(r[0]/l)*l)),n||(r[1]=i.round(o(r[1]/l)*l))},niceTicks:function(t){t=t||10;var e=this._extent,n=e[1]-e[0],r=n/t,a=p[R],l=d(p,r,0,a),u=p[Math.min(l,a-1)],h=u[2];if("year"===u[0]){var c=n/h,f=i.nice(c/t,!0);h*=f}var m=[o(e[0]/h)*h,s(e[1]/h)*h];this._stepLvl=u,this._interval=h,this._niceExtent=m},parse:function(t){return+i.parseDate(t)}});e.each([v,"normalize"],function(t){f[Z][t]=function(e){return a[t].call(this,this.parse(e))}});var p=[["hh:mm:ss",1,l],["hh:mm:ss",5,5*l],["hh:mm:ss",10,10*l],["hh:mm:ss",15,15*l],["hh:mm:ss",30,30*l],["hh:mm\nMM-dd",1,u],["hh:mm\nMM-dd",5,5*u],["hh:mm\nMM-dd",10,10*u],["hh:mm\nMM-dd",15,15*u],["hh:mm\nMM-dd",30,30*u],["hh:mm\nMM-dd",1,h],["hh:mm\nMM-dd",2,2*h],["hh:mm\nMM-dd",6,6*h],["hh:mm\nMM-dd",12,12*h],["MM-dd\nyyyy",1,c],["week",7,7*c],["month",1,31*c],["quarter",3,380*c/4],["half-year",6,380*c/2],["year",1,380*c]];return f[k]=function(){return new f},f}),e("echarts/scale/Log",[le,se,"./Scale","../util/number","./Interval"],function(t){function e(t,e){return u(t,l(e))}var i=t(se),n=t("./Scale"),r=t("../util/number"),a=t("./Interval"),o=n[Z],s=a[Z],l=r.getPrecisionSafe,u=r.round,h=Math.floor,c=Math.ceil,d=Math.pow,f=Math.log,g=n[C]({type:"log",base:10,$constructor:function(){n.apply(this,arguments),this._originalScale=new a},getTicks:function(){var t=this._originalScale,n=this._extent,a=t[m]();return i.map(s.getTicks.call(this),function(i){var o=r.round(d(this.base,i));return o=i===n[0]&&t.__fixMin?e(o,a[0]):o,o=i===n[1]&&t.__fixMax?e(o,a[1]):o},this)},getLabel:s[p],scale:function(t){return t=o.scale.call(this,t),d(this.base,t)},setExtent:function(t,e){var i=this.base;t=f(t)/f(i),e=f(e)/f(i),s.setExtent.call(this,t,e)},getExtent:function(){var t=this.base,i=o[m].call(this);i[0]=d(t,i[0]),i[1]=d(t,i[1]);var n=this._originalScale,r=n[m]();return n.__fixMin&&(i[0]=e(i[0],r[0])),n.__fixMax&&(i[1]=e(i[1],r[1])),i},unionExtent:function(t){this._originalScale.unionExtent(t);var e=this.base;t[0]=f(t[0])/f(e),t[1]=f(t[1])/f(e),o.unionExtent.call(this,t)},niceTicks:function(t){t=t||10;var e=this._extent,i=e[1]-e[0];if(!(1/0===i||0>=i)){var n=r.quantity(i),a=t/i*n;for(.5>=a&&(n*=10);!isNaN(n)&&Math.abs(n)<1&&Math.abs(n)>0;)n*=10;var o=[r.round(c(e[0]/n)*n),r.round(h(e[1]/n)*n)];this._interval=n,this._niceExtent=o}},niceExtent:function(t,e,i){s.niceExtent.call(this,t,e,i);var n=this._originalScale;n.__fixMin=e,n.__fixMax=i}});return i.each([v,"normalize"],function(t){g[Z][t]=function(e){return e=f(e)/f(this.base),o[t].call(this,e)}}),g[k]=function(){return new g},g}),e(se,[le],function(){function t(e){if(null==e||"object"!=typeof e)return e;var i=e,n=k.call(e);if("[object Array]"===n){i=[];for(var r=0,a=e[R];a>r;r++)i[r]=t(e[r])}else if(z[n])i=e.constructor.from(e);else if(!I[n]&&!M(e)){i={};for(var o in e)e.hasOwnProperty(o)&&(i[o]=t(e[o]))}return i}function e(i,n,r){if(!w(n)||!w(i))return r?t(n):i;for(var a in n)if(n.hasOwnProperty(a)){var o=i[a],s=n[a];!w(s)||!w(o)||_(s)||_(o)||M(s)||M(o)||T(s)||T(o)?!r&&a in i||(i[a]=t(n[a],!0)):e(o,s,r)}return i}function i(t,i){for(var n=t[0],r=1,a=t[R];a>r;r++)n=e(n,t[r],i);return n}function n(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i]);return t}function r(t,e,i){for(var n in e)e.hasOwnProperty(n)&&(i?null!=e[n]:null==t[n])&&(t[n]=e[n]);return t}function a(){return document.createElement("canvas")}function o(){return P||(P=V.createCanvas().getContext("2d")),P}function s(t,e){if(t){if(t[O])return t[O](e);for(var i=0,n=t[R];n>i;i++)if(t[i]===e)return i}return-1}function l(t,e){function i(){}var n=t[Z];i[Z]=e[Z],t[Z]=new i;for(var r in n)t[Z][r]=n[r];t[Z].constructor=t,t.superClass=e}function u(t,e,i){t=Z in t?t[Z]:t,e=Z in e?e[Z]:e,r(t,e,i)}function h(t){return t?typeof t==G?!1:"number"==typeof t[R]:void 0}function c(t,e,i){if(t&&e)if(t.forEach&&t.forEach===D)t.forEach(e,i);else if(t[R]===+t[R])for(var n=0,r=t[R];r>n;n++)e.call(i,t[n],n,t);else for(var a in t)t.hasOwnProperty(a)&&e.call(i,t[a],a,t)}function d(t,e,i){if(t&&e){if(t.map&&t.map===N)return t.map(e,i);for(var n=[],r=0,a=t[R];a>r;r++)n.push(e.call(i,t[r],r,t));return n}}function f(t,e,i,n){if(t&&e){if(t.reduce&&t.reduce===F)return t.reduce(e,i,n);for(var r=0,a=t[R];a>r;r++)i=e.call(n,i,t[r],r,t);return i}}function p(t,e,i){if(t&&e){if(t.filter&&t.filter===E)return t.filter(e,i);for(var n=[],r=0,a=t[R];a>r;r++)e.call(i,t[r],r,t)&&n.push(t[r]);return n}}function m(t,e,i){if(t&&e)for(var n=0,r=t[R];r>n;n++)if(e.call(i,t[n],n,t))return t[n]}function v(t,e){var i=B.call(arguments,2);return function(){return t.apply(e,i.concat(B.call(arguments)))}}function g(t){var e=B.call(arguments,1);return function(){return t.apply(this,e.concat(B.call(arguments)))}}function _(t){return"[object Array]"===k.call(t)}function x(t){return typeof t===y}function b(t){return"[object String]"===k.call(t)}function w(t){var e=typeof t;return e===y||!!t&&"object"==e}function T(t){return!!I[k.call(t)]}function M(t){return"object"==typeof t&&"number"==typeof t.nodeType&&"object"==typeof t.ownerDocument}function A(){for(var t=0,e=arguments[R];e>t;t++)if(null!=arguments[t])return arguments[t]}function C(){return Function.call.apply(B,arguments)}function S(t,e){if(!t)throw new Error(e)}var P,I={"[object Function]":1,"[object RegExp]":1,"[object Date]":1,"[object Error]":1,"[object CanvasGradient]":1,"[object CanvasPattern]":1,"[object Image]":1,"[object Canvas]":1},z={"[object Int8Array]":1,"[object Uint8Array]":1,"[object Uint8ClampedArray]":1,"[object Int16Array]":1,"[object Uint16Array]":1,"[object Int32Array]":1,"[object Uint32Array]":1,"[object Float32Array]":1,"[object Float64Array]":1},k=Object[Z].toString,L=Array[Z],D=L.forEach,E=L.filter,B=L.slice,N=L.map,F=L.reduce,V={inherits:l,mixin:u,clone:t,merge:e,mergeAll:i,extend:n,defaults:r,getContext:o,createCanvas:a,indexOf:s,slice:C,find:m,isArrayLike:h,each:c,map:d,reduce:f,filter:p,bind:v,curry:g,isArray:_,isString:b,isObject:w,isFunction:x,isBuildInObject:T,isDom:M,retrieve:A,assert:S,noop:function(){}};return V}),e("echarts/chart/line/LineSeries",[le,"../helper/createListFromArray","../../model/Series"],function(t){var e=t("../helper/createListFromArray"),i=t("../../model/Series");return i[C]({type:"series.line",dependencies:["grid","polar"],getInitialData:function(t,i){return e(t.data,this,i)},defaultOption:{zlevel:0,z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,hoverAnimation:!0,clipOverflow:!0,label:{normal:{position:"top"}},lineStyle:{normal:{width:2,type:"solid"}},step:!1,smooth:!1,smoothMonotone:null,symbol:"emptyCircle",symbolSize:4,symbolRotate:null,showSymbol:!0,showAllSymbol:!1,connectNulls:!1,sampling:"none",animationEasing:"linear",progressive:0,hoverLayerThreshold:1/0}})}),e("echarts/chart/line/LineView",[le,se,"../helper/SymbolDraw","../helper/Symbol","./lineAnimationDiff","../../util/graphic","../../util/model","./poly","../../view/Chart"],function(t){function e(t,e){if(t[R]===e[R]){for(var i=0;i<t[R];i++){var n=t[i],r=e[i];if(n[0]!==r[0]||n[1]!==r[1])return}return!0}}function i(t){return"number"==typeof t?t:t?.3:0}function n(t){var e=t.getGlobalExtent();if(t.onBand){var i=t.getBandWidth()/2-1,n=e[1]>e[0]?1:-1;e[0]+=n*i,e[1]-=n*i}return e}function r(t){return t>=0?1:-1}function a(t,e){var i=t.getBaseAxis(),n=t.getOtherAxis(i),a=i.onZero?0:n.scale[m]()[0],o=n.dim,s="x"===o||"radius"===o?1:0;return e.mapArray([o],function(n,l){for(var u,h=e.stackedOn;h&&r(h.get(o,l))===r(n);){u=h;break}var c=[];return c[s]=e.get(i.dim,l),c[1-s]=u?u.get(o,l,!0):a,t.dataToPoint(c)},!0)}function o(t,e,i){var r=n(t.getAxis("x")),a=n(t.getAxis("y")),o=t.getBaseAxis().isHorizontal(),s=Math.min(r[0],r[1]),l=Math.min(a[0],a[1]),u=Math.max(r[0],r[1])-s,h=Math.max(a[0],a[1])-l,c=i.get("lineStyle.normal.width")||2,d=i.get("clipOverflow")?c/2:Math.max(u,h);o?(l-=d,h+=2*d):(s-=d,u+=2*d);var f=new y.Rect({shape:{x:s,y:l,width:u,height:h}});return e&&(f.shape[o?"width":K]=0,y.initProps(f,{shape:{width:u,height:h}},i)),f}function s(t,e,i){var n=t.getAngleAxis(),r=t.getRadiusAxis(),a=r[m](),o=n[m](),s=Math.PI/180,l=new y.Sector({shape:{cx:t.cx,cy:t.cy,r0:a[0],r:a[1],startAngle:-o[0]*s,endAngle:-o[1]*s,clockwise:n.inverse}});return e&&(l.shape.endAngle=-o[0]*s,y.initProps(l,{shape:{endAngle:-o[1]*s}},i)),l}function l(t,e,i){return"polar"===t.type?s(t,e,i):o(t,e,i)}function u(t,e,i){for(var n=e.getBaseAxis(),r="x"===n.dim||"radius"===n.dim?0:1,a=[],o=0;o<t[R]-1;o++){var s=t[o+1],l=t[o];a.push(l);var u=[];switch(i){case"end":u[r]=s[r],u[1-r]=l[1-r],a.push(u);break;case j:var h=(l[r]+s[r])/2,c=[];u[r]=c[r]=h,u[1-r]=l[1-r],c[1-r]=s[1-r],a.push(u),a.push(c);break;default:u[r]=l[r],u[1-r]=s[1-r],a.push(u)}}return t[o]&&a.push(t[o]),a}function h(t,e){var i=t.getVisual("visualMeta");if(i&&i[R]&&t.count()){for(var n,r=i[R]-1;r>=0;r--)if(i[r].dimension<2){n=i[r];break}if(n&&"cartesian2d"===e.type){var a=n.dimension,o=t[f][a],s=e.getAxis(o),l=c.map(n.stops,function(t){return{coord:s.toGlobalCoord(s.dataToCoord(t.value)),color:t.color}}),u=l[R],h=n.outerColors.slice();u&&l[0].coord>l[u-1].coord&&(l.reverse(),h.reverse());var d=10,p=l[0].coord-d,m=l[u-1].coord+d,v=m-p;if(.001>v)return"transparent";c.each(l,function(t){t.offset=(t.coord-p)/v}),l.push({offset:u?l[u-1].offset:.5,color:h[1]||"transparent"}),l.unshift({offset:u?l[0].offset:.5,color:h[0]||"transparent"});var g=new y.LinearGradient(0,0,0,0,l,!0);return g[o]=p,g[o+"2"]=m,g}}}var c=t(se),p=t("../helper/SymbolDraw"),v=t("../helper/Symbol"),g=t("./lineAnimationDiff"),y=t("../../util/graphic"),_=t("../../util/model"),x=t("./poly"),w=t("../../view/Chart");return w[C]({type:"line",init:function(){var t=new y.Group,e=new p;this.group.add(e.group),this._symbolDraw=e,this._lineGroup=t},render:function(t,n,r){var o=t[ae],s=this.group,d=t[D](),f=t[ne]("lineStyle.normal"),p=t[ne]("areaStyle.normal"),m=d.mapArray(d.getItemLayout,!0),v="polar"===o.type,g=this._coordSys,y=this._symbolDraw,_=this._polyline,x=this._polygon,b=this._lineGroup,w=t.get(N),T=!p.isEmpty(),M=a(o,d),A=t.get("showSymbol"),C=A&&!v&&!t.get("showAllSymbol")&&this._getSymbolIgnoreFunc(d,o),P=this._data;P&&P.eachItemGraphicEl(function(t,e){t.__temp&&(s[S](t),P.setItemGraphicEl(e,null))}),A||y[S](),s.add(b);var I=!v&&t.get("step");_&&g.type===o.type&&I===this._step?(T&&!x?x=this._newPolygon(m,M,o,w):x&&!T&&(b[S](x),x=this._polygon=null),b.setClipPath(l(o,!1,t)),A&&y.updateData(d,C),d.eachItemGraphicEl(function(t){t.stopAnimation(!0)}),e(this._stackedOnPoints,M)&&e(this._points,m)||(w?this._updateAnimation(d,M,o,r,I):(I&&(m=u(m,o,I),M=u(M,o,I)),_.setShape({points:m}),x&&x.setShape({points:m,stackedOnPoints:M})))):(A&&y.updateData(d,C),I&&(m=u(m,o,I),M=u(M,o,I)),_=this._newPolyline(m,o,w),T&&(x=this._newPolygon(m,M,o,w)),b.setClipPath(l(o,!0,t)));var z=h(d,o)||d.getVisual("color");_.useStyle(c[re](f.getLineStyle(),{fill:"none",stroke:z,lineJoin:"bevel"}));var k=t.get("smooth");if(k=i(t.get("smooth")),_.setShape({smooth:k,smoothMonotone:t.get("smoothMonotone"),connectNulls:t.get("connectNulls")}),x){var L=d.stackedOn,O=0;if(x.useStyle(c[re](p.getAreaStyle(),{fill:z,opacity:.7,lineJoin:"bevel"})),L){var R=L.hostModel;O=i(R.get("smooth"))}x.setShape({smooth:k,stackedOnSmooth:O,smoothMonotone:t.get("smoothMonotone"),connectNulls:t.get("connectNulls")})}this._data=d,this._coordSys=o,this._stackedOnPoints=M,this._points=m,this._step=I},dispose:function(){},highlight:function(t,e,i,n){var r=t[D](),a=_.queryDataIndex(r,n);if(!(a instanceof Array)&&null!=a&&a>=0){var o=r.getItemGraphicEl(a);if(!o){var s=r.getItemLayout(a);if(!s)return;o=new v(r,a),o[W]=s,o.setZ(t.get(b),t.get("z")),o[E]=isNaN(s[0])||isNaN(s[1]),o.__temp=!0,r.setItemGraphicEl(a,o),o.stopSymbolAnimation(!0),this.group.add(o)
}o.highlight()}else w[Z].highlight.call(this,t,e,i,n)},downplay:function(t,e,i,n){var r=t[D](),a=_.queryDataIndex(r,n);if(null!=a&&a>=0){var o=r.getItemGraphicEl(a);o&&(o.__temp?(r.setItemGraphicEl(a,null),this.group[S](o)):o.downplay())}else w[Z].downplay.call(this,t,e,i,n)},_newPolyline:function(t){var e=this._polyline;return e&&this._lineGroup[S](e),e=new x.Polyline({shape:{points:t},silent:!0,z2:10}),this._lineGroup.add(e),this._polyline=e,e},_newPolygon:function(t,e){var i=this._polygon;return i&&this._lineGroup[S](i),i=new x.Polygon({shape:{points:t,stackedOnPoints:e},silent:!0}),this._lineGroup.add(i),this._polygon=i,i},_getSymbolIgnoreFunc:function(t,e){var i=e.getAxesByScale(d)[0];return i&&i.isLabelIgnored?c.bind(i.isLabelIgnored,i):void 0},_updateAnimation:function(t,e,i,n,r){var a=this._polyline,o=this._polygon,s=t.hostModel,l=g(this._data,t,this._stackedOnPoints,e,this._coordSys,i),h=l.current,c=l.stackedOnCurrent,d=l.next,f=l.stackedOnNext;r&&(h=u(l.current,i,r),c=u(l.stackedOnCurrent,i,r),d=u(l.next,i,r),f=u(l.stackedOnNext,i,r)),a.shape.__points=l.current,a.shape.points=h,y.updateProps(a,{shape:{points:d}},s),o&&(o.setShape({points:h,stackedOnPoints:c}),y.updateProps(o,{shape:{points:d,stackedOnPoints:f}},s));for(var p=[],m=l.status,v=0;v<m[R];v++){var _=m[v].cmd;if("="===_){var x=t.getItemGraphicEl(m[v].idx1);x&&p.push({el:x,ptIdx:v})}}a.animators&&a.animators[R]&&a.animators[0].during(function(){for(var t=0;t<p[R];t++){var e=p[t].el;e.attr(W,a.shape.__points[p[t].ptIdx])}})},remove:function(){var t=this.group,e=this._data;this._lineGroup[oe](),this._symbolDraw[S](!0),e&&e.eachItemGraphicEl(function(i,n){i.__temp&&(t[S](i),e.setItemGraphicEl(n,null))}),this._polyline=this._polygon=this._coordSys=this._points=this._stackedOnPoints=this._data=null}})}),e("echarts/visual/symbol",[le],function(){return function(t,e,i,n){n.eachRawSeriesByType(t,function(t){var r=t[D](),a=t.get("symbol")||e,o=t.get("symbolSize");r.setVisual({legendSymbol:i||a,symbol:a,symbolSize:o}),n.isSeriesFiltered(t)||(typeof o===y&&r.each(function(e){var i=t.getRawValue(e),n=t.getDataParams(e);r.setItemVisual(e,"symbolSize",o(i,n))}),r.each(function(t){var e=r[c](t),i=e[h]("symbol",!0),n=e[h]("symbolSize",!0);null!=i&&r.setItemVisual(t,"symbol",i),null!=n&&r.setItemVisual(t,"symbolSize",n)}))})}}),e("echarts/layout/points",[le],function(){return function(t,e){e.eachSeriesByType(t,function(t){var e=t[D](),i=t[ae];if(i){var n=i[f];"singleAxis"===i.type?e.each(n[0],function(t,n){e.setItemLayout(n,isNaN(t)?[0/0,0/0]:i.dataToPoint(t))}):e.each(n,function(t,n,r){e.setItemLayout(r,isNaN(t)||isNaN(n)?[0/0,0/0]:i.dataToPoint([t,n]))},!0)}})}}),e("echarts/processor/dataSample",[],function(){var t={average:function(t){for(var e=0,i=0,n=0;n<t[R];n++)isNaN(t[n])||(e+=t[n],i++);return 0===i?0/0:e/i},sum:function(t){for(var e=0,i=0;i<t[R];i++)e+=t[i]||0;return e},max:function(t){for(var e=-1/0,i=0;i<t[R];i++)t[i]>e&&(e=t[i]);return e},min:function(t){for(var e=1/0,i=0;i<t[R];i++)t[i]<e&&(e=t[i]);return e},nearest:function(t){return t[0]}},e=function(t){return Math.round(t[R]/2)};return function(i,n){n.eachSeriesByType(i,function(i){var n=i[D](),r=i.get("sampling"),a=i[ae];if("cartesian2d"===a.type&&r){var o=a.getBaseAxis(),s=a.getOtherAxis(o),l=o[m](),u=l[1]-l[0],h=Math.round(n.count()/u);if(h>1){var c;typeof r===G?c=t[r]:typeof r===y&&(c=r),c&&(n=n.downSample(s.dim,1/h,c,e),i.setData(n))}}},this)}}),e("echarts/component/tooltip/TooltipModel",[le,"../../echarts"],function(t){t("../../echarts").extendComponentModel({type:"tooltip",defaultOption:{zlevel:0,z:8,show:!0,showContent:!0,trigger:"item",triggerOn:"mousemove",alwaysShowContent:!1,confine:!1,showDelay:0,hideDelay:100,transitionDuration:.4,enterable:!1,backgroundColor:"rgba(50,50,50,0.7)",borderColor:"#333",borderRadius:4,borderWidth:0,padding:5,extraCssText:"",axisPointer:{type:"line",axis:"auto",animation:!0,animationDurationUpdate:200,animationEasingUpdate:"exponentialOut",lineStyle:{color:"#555",width:1,type:"solid"},crossStyle:{color:"#555",width:1,type:"dashed",textStyle:{}},shadowStyle:{color:"rgba(150,150,150,0.3)"}},textStyle:{color:"#fff",fontSize:14}}})}),e("echarts/component/dataZoom/typeDefaulter",[le,"../../model/Component"],function(t){t("../../model/Component").registerSubTypeDefaulter("dataZoom",function(){return"slider"})}),e("echarts/component/dataZoom/DataZoomModel",[le,se,H,"../../echarts","../../util/model","./helper","./AxisProxy"],function(t){function e(t){var e={};return f(["start","end","startValue","endValue","throttle"],function(i){t.hasOwnProperty(i)&&(e[i]=t[i])}),e}function i(t,e,i,n){null!=i[e]&&null==i[t]&&(n[t]=null)}var n=t(se),r=t(H),a=t("../../echarts"),h=t("../../util/model"),c=t("./helper"),d=t("./AxisProxy"),f=n.each,p=c.eachAxisDim,m=a.extendComponentModel({type:"dataZoom",dependencies:["xAxis","yAxis","zAxis","radiusAxis","angleAxis",A],defaultOption:{zlevel:0,z:4,orient:null,xAxisIndex:null,yAxisIndex:null,filterMode:"filter",throttle:null,start:0,end:100,startValue:null,endValue:null},init:function(t,i,n){this._dataIntervalByAxis={},this._dataInfo={},this._axisProxies={},this.textStyleModel,this._autoThrottle=!0;var r=e(t);this.mergeDefaultAndTheme(t,n),this.doInit(r)},mergeOption:function(t){var i=e(t);n.merge(this[u],t,!0),this.doInit(i)},doInit:function(t){var e=this[u];r[B]||(e.realtime=!1),this._setDefaultThrottle(t),i("start","startValue",t,e),i("end","endValue",t,e),this.textStyleModel=this[ne](ie),this._resetTarget(),this._giveAxisProxies()},_giveAxisProxies:function(){var t=this._axisProxies;this.eachTargetAxis(function(e,i,n,r){var a=this.dependentModels[e.axis][i],o=a.__dzAxisProxy||(a.__dzAxisProxy=new d(e.name,i,this,r));t[e.name+"_"+i]=o},this)},_resetTarget:function(){var t=this[u],e=this._judgeAutoMode();p(function(e){var i=e.axisIndex;t[i]=h.normalizeToArray(t[i])},this),"axisIndex"===e?this._autoSetAxisIndex():"orient"===e&&this._autoSetOrient()},_judgeAutoMode:function(){var t=this[u],e=!1;p(function(i){null!=t[i.axisIndex]&&(e=!0)},this);var i=t.orient;return null==i&&e?"orient":e?void 0:(null==i&&(t.orient="horizontal"),"axisIndex")},_autoSetAxisIndex:function(){var t=!0,e=this.get("orient",!0),i=this[u];if(t){var r="vertical"===e?{dim:"y",axisIndex:"yAxisIndex",axis:"yAxis"}:{dim:"x",axisIndex:"xAxisIndex",axis:"xAxis"};this.dependentModels[r.axis][R]&&(i[r.axisIndex]=[0],t=!1)}t&&p(function(e){if(t){var n=[],r=this.dependentModels[e.axis];if(r[R]&&!n[R])for(var a=0,o=r[R];o>a;a++)r[a].get("type")===l&&n.push(a);i[e.axisIndex]=n,n[R]&&(t=!1)}},this),t&&this[s].eachSeries(function(t){this._isSeriesHasAllAxesTypeOf(t,"value")&&p(function(e){var r=i[e.axisIndex],a=t.get(e.axisIndex),l=t.get(e.axisId),u=t[s][o]({mainType:e.axis,index:a,id:l})[0];a=u.componentIndex,n[O](r,a)<0&&r.push(a)})},this)},_autoSetOrient:function(){var t;this.eachTargetAxis(function(e){!t&&(t=e.name)},this),this[u].orient="y"===t?"vertical":"horizontal"},_isSeriesHasAllAxesTypeOf:function(t,e){var i=!0;return p(function(n){var r=t.get(n.axisIndex),a=this.dependentModels[n.axis][r];a&&a.get("type")===e||(i=!1)},this),i},_setDefaultThrottle:function(t){if(t.hasOwnProperty("throttle")&&(this._autoThrottle=!1),this._autoThrottle){var e=this[s][u];this[u].throttle=e[N]&&e.animationDurationUpdate>0?100:20}},getFirstTargetAxisModel:function(){var t;return p(function(e){if(null==t){var i=this.get(e.axisIndex);i[R]&&(t=this.dependentModels[e.axis][i[0]])}},this),t},eachTargetAxis:function(t,e){var i=this[s];p(function(n){f(this.get(n.axisIndex),function(r){t.call(e,n,r,this,i)},this)},this)},getAxisProxy:function(t,e){return this._axisProxies[t+"_"+e]},setRawRange:function(t){f(["start","end","startValue","endValue"],function(e){this[u][e]=t[e]},this)},getPercentRange:function(){var t=this.findRepresentativeAxisProxy();return t?t.getDataPercentWindow():void 0},getValueRange:function(t,e){if(null!=t||null!=e)return this.getAxisProxy(t,e).getDataValueWindow();var i=this.findRepresentativeAxisProxy();return i?i.getDataValueWindow():void 0},findRepresentativeAxisProxy:function(){var t=this._axisProxies;for(var e in t)if(t.hasOwnProperty(e)&&t[e].hostedBy(this))return t[e];for(var e in t)if(t.hasOwnProperty(e)&&!t[e].hostedBy(this))return t[e]}});return m}),e("echarts/component/dataZoom/DataZoomView",[le,"../../view/Component"],function(t){var e=t("../../view/Component");return e[C]({type:"dataZoom",render:function(t,e,i){this.dataZoomModel=t,this[s]=e,this.api=i},getTargetInfo:function(){function t(t,e,i,n){for(var r,a=0;a<i[R];a++)if(i[a].model===t){r=i[a];break}r||i.push(r={model:t,axisModels:[],coordIndex:n}),r.axisModels.push(e)}var e=this.dataZoomModel,i=this[s],n=[],r=[],a=[];return e.eachTargetAxis(function(e,s){var l=i.getComponent(e.axis,s);if(l){a.push(l);var u,h=e.axis;"xAxis"===h||"yAxis"===h?u="grid":("angleAxis"===h||"radiusAxis"===h)&&(u="polar");var c=u?i[o]({mainType:u,index:l.get(u+"Index"),id:l.get(u+"Id")})[0]:null;null!=c&&t(c,l,"grid"===u?n:r,c.componentIndex)}},this),{cartesians:n,polars:r,axisModels:a}}})}),e("echarts/component/dataZoom/SliderZoomModel",[le,"./DataZoomModel"],function(t){var e=t("./DataZoomModel"),i=e[C]({type:"dataZoom.slider",layoutMode:"box",defaultOption:{show:!0,right:"ph",top:"ph",width:"ph",height:"ph",left:null,bottom:null,backgroundColor:"rgba(47,69,84,0)",dataBackground:{lineStyle:{color:"#2f4554",width:.5,opacity:.3},areaStyle:{color:"rgba(47,69,84,0.3)",opacity:.3}},borderColor:"#ddd",fillerColor:"rgba(167,183,204,0.4)",handleIcon:"M8.2,13.6V3.9H6.3v9.7H3.1v14.9h3.3v9.7h1.8v-9.7h3.3V13.6H8.2z M9.7,24.4H4.8v-1.4h4.9V24.4z M9.7,19.1H4.8v-1.4h4.9V19.1z",handleSize:"100%",handleStyle:{color:"#a7b7cc"},labelPrecision:null,labelFormatter:null,showDetail:!0,showDataShadow:"auto",realtime:!0,zoomLock:!1,textStyle:{color:"#333"}}});return i}),e("echarts/component/dataZoom/SliderZoomView",[le,se,"../../util/graphic","../../util/throttle","./DataZoomView","../../util/number","../../util/layout","../helper/sliderMove"],function(t){function e(t){return"x"===t?"y":"x"}var i=t(se),a=t("../../util/graphic"),o=t("../../util/throttle"),h=t("./DataZoomView"),c=a.Rect,d=t("../../util/number"),f=d.linearMap,m=t("../../util/layout"),v=t("../helper/sliderMove"),y=d.asc,_=i.bind,b=i.each,w=7,T=1,P=30,I="horizontal",z="vertical",k=5,L=["line","bar","candlestick","scatter"],D=h[C]({type:"dataZoom.slider",init:function(t,e){this._displayables={},this._orient,this._range,this._handleEnds,this._size,this._handleWidth,this._handleHeight,this._location,this._dragging,this._dataShadowInfo,this.api=e},render:function(t,e,i,n){return D.superApply(this,"render",arguments),o.createOrUpdate(this,"_dispatchZoomAction",this.dataZoomModel.get("throttle"),"fixRate"),this._orient=t.get("orient"),this.dataZoomModel.get("show")===!1?void this.group[oe]():(n&&"dataZoom"===n.type&&n.from===this.uid||this._buildView(),void this._updateView())},remove:function(){D.superApply(this,S,arguments),o.clear(this,"_dispatchZoomAction")},dispose:function(){D.superApply(this,M,arguments),o.clear(this,"_dispatchZoomAction")},_buildView:function(){var t=this.group;t[oe](),this._resetLocation(),this._resetInterval();var e=this._displayables.barGroup=new a.Group;this._renderBackground(),this._renderHandle(),this._renderDataShadow(),t.add(e),this._positionGroup()},_resetLocation:function(){var t=this.dataZoomModel,e=this.api,n=this._findCoordRect(),r={width:e[$](),height:e[Y]()},a=this._orient===I?{right:r.width-n.x-n.width,top:r[K]-P-w,width:n.width,height:P}:{right:w,top:n.y,width:P,height:n[K]},o=m.getLayoutParams(t[u]);i.each(["right","top","width",K],function(t){"ph"===o[t]&&(o[t]=a[t])});var s=m.getLayoutRect(o,r,t.padding);this._location={x:s.x,y:s.y},this._size=[s.width,s[K]],this._orient===z&&this._size.reverse()},_positionGroup:function(){var t=this.group,e=this._location,i=this._orient,n=this.dataZoomModel.getFirstTargetAxisModel(),r=n&&n.get("inverse"),a=this._displayables.barGroup,o=(this._dataShadowInfo||{}).otherAxisInverse;a.attr(i!==I||r?i===I&&r?{scale:o?[-1,1]:[-1,-1]}:i!==z||r?{scale:o?[-1,-1]:[-1,1],rotation:Math.PI/2}:{scale:o?[1,-1]:[1,1],rotation:Math.PI/2}:{scale:o?[1,1]:[1,-1]});var s=t[J]([a]);t.attr(W,[e.x-s.x,e.y-s.y])},_getViewExtent:function(){return[0,this._size[0]]},_renderBackground:function(){var t=this.dataZoomModel,e=this._size;this._displayables.barGroup.add(new c({silent:!0,shape:{x:0,y:0,width:e[0],height:e[1]},style:{fill:t.get("backgroundColor")},z2:-40}))},_renderDataShadow:function(){var t=this._dataShadowInfo=this._prepareDataShadowInfo();if(t){var e=this._size,n=t[A],r=n.getRawData(),o=n.getShadowDim?n.getShadowDim():t.otherDim,s=r.getDataExtent(o),l=.3*(s[1]-s[0]);s=[s[0]-l,s[1]+l];var u,h=[0,e[1]],c=[0,e[0]],d=[[e[0],0],[0,0]],p=[],m=c[1]/(r.count()-1),v=0,g=Math.round(r.count()/e[0]);r.each([o],function(t,e){if(g>0&&e%g)return void(v+=m);var i=null==t||isNaN(t)||""===t,n=i?0:f(t,s,h,!0);i&&!u&&e?(d.push([d[d[R]-1][0],0]),p.push([p[p[R]-1][0],0])):!i&&u&&(d.push([v,0]),p.push([v,0])),d.push([v,n]),p.push([v,n]),v+=m,u=i});var y=this.dataZoomModel;this._displayables.barGroup.add(new a.Polygon({shape:{points:d},style:i[re]({fill:y.get("dataBackgroundColor")},y[ne]("dataBackground.areaStyle").getAreaStyle()),silent:!0,z2:-20})),this._displayables.barGroup.add(new a.Polyline({shape:{points:p},style:y[ne]("dataBackground.lineStyle").getLineStyle(),silent:!0,z2:-19}))}},_prepareDataShadowInfo:function(){var t=this.dataZoomModel,n=t.get("showDataShadow");if(n!==!1){var r,a=this[s];return t.eachTargetAxis(function(o,s){var l=t.getAxisProxy(o.name,s).getTargetSeriesModels();i.each(l,function(t){if(!(r||n!==!0&&i[O](L,t.get("type"))<0)){var l=e(o.name),u=a.getComponent(o.axis,s).axis;r={thisAxis:u,series:t,thisDim:o.name,otherDim:l,otherAxisInverse:t[ae].getOtherAxis(u).inverse}}},this)},this),r}},_renderHandle:function(){var t=this._displayables,e=t.handles=[],i=t.handleLabels=[],n=this._displayables.barGroup,r=this._size,o=this.dataZoomModel;n.add(t.filler=new c({draggable:!0,cursor:"move",drift:_(this._onDragMove,this,"all"),ondragstart:_(this._showDataInfo,this,!0),ondragend:_(this._onDragEnd,this),onmouseover:_(this._showDataInfo,this,!0),onmouseout:_(this._showDataInfo,this,!1),style:{fill:o.get("fillerColor"),textPosition:"inside"}})),n.add(new c(a.subPixelOptimizeRect({silent:!0,shape:{x:0,y:0,width:r[0],height:r[1]},style:{stroke:o.get("dataBackgroundColor")||o.get("borderColor"),lineWidth:T,fill:"rgba(0,0,0,0)"}})));var s=o.get("handleIcon");b([0,1],function(t){var r=a.makePath(s,{style:{strokeNoScale:!0},rectHover:!0,cursor:"vertical"===this._orient?"ns-resize":"ew-resize",draggable:!0,drift:_(this._onDragMove,this,t),ondragend:_(this._onDragEnd,this),onmouseover:_(this._showDataInfo,this,!0),onmouseout:_(this._showDataInfo,this,!1)},{x:-.5,y:0,width:1,height:1},X),l=r[J]();this._handleHeight=d.parsePercent(o.get("handleSize"),this._size[1]),this._handleWidth=l.width/l[K]*this._handleHeight,r[q](o[ne]("handleStyle").getItemStyle());var u=o.get("handleColor");null!=u&&(r.style.fill=u),n.add(e[t]=r);var h=o.textStyleModel;this.group.add(i[t]=new a.Text({silent:!0,invisible:!0,style:{x:0,y:0,text:"",textVerticalAlign:"middle",textAlign:"center",fill:h.getTextColor(),textFont:h[te]()},z2:10}))},this)},_resetInterval:function(){var t=this._range=this.dataZoomModel.getPercentRange(),e=this._getViewExtent();this._handleEnds=[f(t[0],[0,100],e,!0),f(t[1],[0,100],e,!0)]},_updateInterval:function(t,e){var i=this._handleEnds,n=this._getViewExtent();v(e,i,n,"all"===t||this.dataZoomModel.get("zoomLock")?"rigid":"cross",t),this._range=y([f(i[0],n,[0,100],!0),f(i[1],n,[0,100],!0)])},_updateView:function(t){var e=this._displayables,i=this._handleEnds,n=y(i.slice()),r=this._size;b([0,1],function(t){var n=e.handles[t],a=this._handleHeight;n.attr({scale:[a,a],position:[i[t],r[1]/2-a/2]})},this),e.filler.setShape({x:n[0],y:0,width:n[1]-n[0],height:r[1]}),this._updateDataInfo(t)},_updateDataInfo:function(t){function e(t){var e=a.getTransform(o.handles[t][r],this.group),i=a.transformDirection(0===t?"right":"left",e),h=this._handleWidth/2+k,c=a[n]([p[t]+(0===t?-h:h),this._size[1]/2],e);s[t][q]({x:c[0],y:c[1],textVerticalAlign:l===I?j:i,textAlign:l===I?i:X,text:u[t]})}var i=this.dataZoomModel,o=this._displayables,s=o.handleLabels,l=this._orient,u=["",""];if(i.get("showDetail")){var h=i.findRepresentativeAxisProxy();if(h){var c=h.getAxisModel().axis,d=this._range,f=t?h.calculateDataWindow({start:d[0],end:d[1]},h.getDataExtent()).valueWindow:h.getDataValueWindow();u=[this._formatLabel(f[0],c),this._formatLabel(f[1],c)]}}var p=y(this._handleEnds.slice());e.call(this,0),e.call(this,1)},_formatLabel:function(t,e){var n=this.dataZoomModel,r=n.get("labelFormatter"),a=n.get("labelPrecision");(null==a||"auto"===a)&&(a=e.getPixelPrecision());var o=null==t&&isNaN(t)?"":e.type===l||"time"===e.type?e.scale[p](Math.round(t)):t.toFixed(Math.min(a,20));return i.isFunction(r)?r(t,o):i[g](r)?r[x]("{value}",o):o},_showDataInfo:function(t){t=this._dragging||t;var e=this._displayables.handleLabels;e[0].attr("invisible",!t),e[1].attr("invisible",!t)},_onDragMove:function(t,e,i){this._dragging=!0;var n=this._applyBarTransform([e,i],!0);this._updateInterval(t,n[0]);var r=this.dataZoomModel.get("realtime");this._updateView(!r),r&&r&&this._dispatchZoomAction()},_onDragEnd:function(){this._dragging=!1,this._showDataInfo(!1),this._dispatchZoomAction()},_dispatchZoomAction:function(){var t=this._range;this.api.dispatchAction({type:"dataZoom",from:this.uid,dataZoomId:this.dataZoomModel.id,start:t[0],end:t[1]})},_applyBarTransform:function(t,e){var i=this._displayables.barGroup.getLocalTransform();return a[n](t,i,e)},_findCoordRect:function(){var t,e=this.getTargetInfo();if(e.cartesians[R])t=e.cartesians[0].model[ae].getRect();else{var i=this.api[$](),n=this.api[Y]();t={x:.2*i,y:.2*n,width:.6*i,height:.6*n}}return t}});return D}),e("echarts/component/dataZoom/InsideZoomModel",[le,"./DataZoomModel"],function(t){return t("./DataZoomModel")[C]({type:"dataZoom.inside",defaultOption:{disabled:!1,zoomLock:!1}})}),e("echarts/component/dataZoom/InsideZoomView",[le,"./DataZoomView",se,"../helper/sliderMove","./roams"],function(t){function e(t){var e=[0,100];return!(t[0]<=e[1])&&(t[0]=e[1]),!(t[1]<=e[1])&&(t[1]=e[1]),!(t[0]>=e[0])&&(t[0]=e[0]),!(t[1]>=e[0])&&(t[1]=e[0]),t}var i=t("./DataZoomView"),n=t(se),r=t("../helper/sliderMove"),a=t("./roams"),o=n.bind,s=i[C]({type:"dataZoom.inside",init:function(){this._range},render:function(t,e,i,r){s.superApply(this,"render",arguments),a.shouldRecordRange(r,t.id)&&(this._range=t.getPercentRange());var u=this.getTargetInfo();n.each(["cartesians","polars"],function(e){var r=u[e],s=n.map(r,function(t){return a.generateCoordId(t.model)});n.each(r,function(n){var r=n.model,u=r[ae];a.register(i,{coordId:a.generateCoordId(r),allCoordIds:s,coordinateSystem:u,containsPoint:o(l[e].containsPoint,this,u),dataZoomId:t.id,throttleRate:t.get("throttle",!0),panGetRange:o(this._onPan,this,n,e),zoomGetRange:o(this._onZoom,this,n,e)})},this)},this)},dispose:function(){a.unregister(this.api,this.dataZoomModel.id),s.superApply(this,M,arguments),this._range=null},_onPan:function(t,e,i,n,a,o,s,h,c){if(this.dataZoomModel[u].disabled)return this._range;var d=this._range.slice(),f=t.axisModels[0];if(f){var p=l[e].getDirectionInfo([o,s],[h,c],f,i,t),m=p.signal*(d[1]-d[0])*p.pixel/p.pixelLength;return r(m,d,[0,100],"rigid"),this._range=d}},_onZoom:function(t,i,n,r,a,o){var s=this.dataZoomModel[u];if(s.disabled||s.zoomLock)return this._range;var h=this._range.slice(),c=t.axisModels[0];if(c){var d=l[i].getDirectionInfo(null,[a,o],c,n,t),f=(d.pixel-d.pixelStart)/d.pixelLength*(h[1]-h[0])+h[0];return r=Math.max(1/r,0),h[0]=(h[0]-f)*r+f,h[1]=(h[1]-f)*r+f,this._range=e(h)}}}),l={cartesians:{getDirectionInfo:function(t,e,i,n,r){var a=i.axis,o={},s=r.model[ae].getRect();return t=t||[0,0],"x"===a.dim?(o.pixel=e[0]-t[0],o.pixelLength=s.width,o.pixelStart=s.x,o.signal=a.inverse?1:-1):(o.pixel=e[1]-t[1],o.pixelLength=s[K],o.pixelStart=s.y,o.signal=a.inverse?-1:1),o},containsPoint:function(t,e,i){return t.getRect()[v](e,i)}},polars:{getDirectionInfo:function(t,e,i,n,r){var a=i.axis,o={},s=r.model[ae],l=s.getRadiusAxis()[m](),u=s.getAngleAxis()[m]();return t=t?s.pointToCoord(t):[0,0],e=s.pointToCoord(e),"radiusAxis"===i.mainType?(o.pixel=e[0]-t[0],o.pixelLength=l[1]-l[0],o.pixelStart=l[0],o.signal=a.inverse?1:-1):(o.pixel=e[1]-t[1],o.pixelLength=u[1]-u[0],o.pixelStart=u[0],o.signal=a.inverse?-1:1),o},containsPoint:function(t,e,i){var n=t.getRadiusAxis()[m]()[1],r=t.cx,a=t.cy;return Math.pow(e-r,2)+Math.pow(i-a,2)<=Math.pow(n,2)}}};return s}),e("echarts/component/tooltip/TooltipView",[le,"./TooltipContent","../../util/graphic",se,"../../util/format","../../util/number","../../util/model",H,"../../model/Model","../../echarts"],function(t){function e(t,e){if(!t||!e)return!1;var i=M.round;return i(t[0])===i(e[0])&&i(t[1])===i(e[1])}function i(t,e,i,n){return{x1:t,y1:e,x2:i,y2:n}}function r(t,e,i,n){return{x:t,y:e,width:i,height:n}}function a(t,e,i,n,r,a){return{cx:t,cy:e,r0:i,r:n,startAngle:r,endAngle:a,clockwise:!0}}function o(t,e,i,n,r){var a=i.clientWidth,o=i.clientHeight,s=20;return t+a+s>n?t-=a+s:t+=s,e+o+s>r?e-=o+s:e+=s,[t,e]}function u(t,e,i,n,r){var a=i.clientWidth,o=i.clientHeight;return t=Math.min(t+a,n)-a,e=Math.min(e+o,r)-o,t=Math.max(t,0),e=Math.max(e,0),[t,e]}function h(t,e,i){var n=i.clientWidth,r=i.clientHeight,a=5,o=0,s=0,l=e.width,u=e[K];switch(t){case"inside":o=e.x+l/2-n/2,s=e.y+u/2-r/2;break;case"top":o=e.x+l/2-n/2,s=e.y-r-a;break;case U:o=e.x+l/2-n/2,s=e.y+u+a;break;case"left":o=e.x-n-a,s=e.y+u/2-r/2;break;case"right":o=e.x+l+a,s=e.y+u/2-r/2}return[o,s]}function d(t,e,i,r,a,s,l,c){var d=c[$](),f=c[Y](),p=l&&l[J]().clone();if(l&&p[n](l.transform),typeof t===y&&(t=t([e,i],s,a.el,p)),w[_](t))e=S(t[0],d),i=S(t[1],f);else if(typeof t===G&&l){var m=h(t,p,a.el);e=m[0],i=m[1]}else{var m=o(e,i,a.el,d,f);e=m[0],i=m[1]}if(r){var m=u(e,i,a.el,d,f);e=m[0],i=m[1]}a.moveTo(e,i)}function v(t){var e=t[ae],i=t.get("tooltip.trigger",!0);return!(!e||"cartesian2d"!==e.type&&"polar"!==e.type&&"singleAxis"!==e.type||"item"===i)}var g=t("./TooltipContent"),x=t("../../util/graphic"),w=t(se),T=t("../../util/format"),M=t("../../util/number"),C=t("../../util/model"),S=M.parsePercent,P=t(H),I=t("../../model/Model");t("../../echarts").extendComponentView({type:"tooltip",_axisPointers:{},init:function(t,e){if(!P.node){var i=new g(e.getDom(),e);this._tooltipContent=i,e.on("showTip",this._manuallyShowTip,this),e.on("hideTip",this._manuallyHideTip,this)}},render:function(t,e,i){if(!P.node){this.group[oe](),this._axisPointers={},this._tooltipModel=t,this._ecModel=e,this._api=i,this._lastHover={};var n=this._tooltipContent;n[z](),n.enterable=t.get("enterable"),this._alwaysShowContent=t.get("alwaysShowContent"),this._seriesGroupByAxis=this._prepareAxisTriggerData(t,e);var r=this._crossText;r&&this.group.add(r);var a=t.get("triggerOn");if(null!=this._lastX&&null!=this._lastY&&"none"!==a){var o=this;clearTimeout(this._refreshUpdateTimeout),this._refreshUpdateTimeout=setTimeout(function(){o._manuallyShowTip({x:o._lastX,y:o._lastY})})}var s=this._api.getZr();s.off("click",this._tryShow),s.off("mousemove",this._mousemove),s.off("mouseout",this._hide),s.off("globalout",this._hide),"click"===a?s.on("click",this._tryShow,this):"mousemove"===a&&(s.on("mousemove",this._mousemove,this),s.on("mouseout",this._hide,this),s.on("globalout",this._hide,this))}},_mousemove:function(t){var e=this._tooltipModel.get("showDelay"),i=this;clearTimeout(this._showTimeout),e>0?this._showTimeout=setTimeout(function(){i._tryShow(t)},e):this._tryShow(t)},_manuallyShowTip:function(t){function e(e){var i=e[D](),n=C.queryDataIndex(i,t);return null!=n&&!w[_](n)&&i.hasValue(n)?!0:void 0}if(t.from!==this.uid){var i=this._ecModel,r=t.seriesIndex,a=i.getSeriesByIndex(r),o=this._api,s="axis"===this._tooltipModel.get("trigger");if(null==t.x||null==t.y){if(s?(a&&!e(a)&&(a=null),a||i.eachSeries(function(t){v(t)&&!a&&e(t)&&(a=t)})):a=a||i.getSeriesByIndex(0),a){var l=a[D](),u=C.queryDataIndex(l,t);if(null==u||w[_](u))return;var h,c,d=l.getItemGraphicEl(u),p=a[ae];if(a.getTooltipPosition){var m=a.getTooltipPosition(u)||[];h=m[0],c=m[1]}else if(p&&p.dataToPoint){var m=p.dataToPoint(l.getValues(w.map(p[f],function(t){return a.coordDimToDataDim(t)[0]}),u,!0));h=m&&m[0],c=m&&m[1]}else if(d){var g=d[J]().clone();g[n](d.transform),h=g.x+g.width/2,c=g.y+g[K]/2}null!=h&&null!=c&&this._tryShow({offsetX:h,offsetY:c,position:t[W],target:d,event:{}})}}else{var d=o.getZr().handler.findHover(t.x,t.y);this._tryShow({offsetX:t.x,offsetY:t.y,position:t[W],target:d,event:{}})}}},_manuallyHideTip:function(t){t.from!==this.uid&&this._hide()},_prepareAxisTriggerData:function(t,e){var i={};return e.eachSeries(function(t){if(v(t)){var e,n,r=t[ae];"cartesian2d"===r.type?(e=r.getBaseAxis(),n=e.dim+e.index):"singleAxis"===r.type?(e=r.getAxis(),n=e.dim+e.type):(e=r.getBaseAxis(),n=e.dim+r.name),i[n]=i[n]||{coordSys:[],series:[]},i[n].coordSys.push(r),i[n][A].push(t)}},this),i},_tryShow:function(t){var e=t[Q],i=this._tooltipModel,n=i.get("trigger"),r=this._ecModel,a=this._api;if(i)if(this._lastX=t.offsetX,this._lastY=t.offsetY,e&&null!=e[L]){var o=e.dataModel||r.getSeriesByIndex(e.seriesIndex),s=e[L],l=o[D]()[c](s);"axis"===(l.get("tooltip.trigger")||n)?this._showAxisTooltip(i,r,t):(this._ticket="",this._hideAxisPointer(),this._resetLastHover(),this._showItemTooltipContent(o,s,e.dataType,t)),a.dispatchAction({type:"showTip",from:this.uid,dataIndexInside:e[L],seriesIndex:e.seriesIndex})}else if(e&&e.tooltip){var u=e.tooltip;if(typeof u===G){var h=u;u={content:h,formatter:h}}var d=new I(u,i),f=d.get("content"),p=Math.random();this._showTooltipContent(d,f,d.get("formatterParams")||{},p,t.offsetX,t.offsetY,t[W],e,a)}else"item"===n?this._hide():this._showAxisTooltip(i,r,t),"cross"===i.get("axisPointer.type")&&a.dispatchAction({type:"showTip",from:this.uid,x:t.offsetX,y:t.offsetY})},_showAxisTooltip:function(t,i,n){var r=t[ne]("axisPointer"),a=r.get("type");if("cross"===a){var o=n[Q];if(o&&null!=o[L]){var s=i.getSeriesByIndex(o.seriesIndex),l=o[L];this._showItemTooltipContent(s,l,o.dataType,n)}}this._showAxisPointer();var u=!0;w.each(this._seriesGroupByAxis,function(i){var o=i.coordSys,s=o[0],l=[n.offsetX,n.offsetY];if(!s.containPoint(l))return void this._hideAxisPointer(s.name);u=!1;var h=s[f],c=s.pointToData(l,!0);l=s.dataToPoint(c);var d=s.getBaseAxis(),p=r.get("axis");"auto"===p&&(p=d.dim);var m=!1,v=this._lastHover;if("cross"===a)e(v.data,c)&&(m=!0),v.data=c;else{var g=w[O](h,p);v.data===c[g]&&(m=!0),v.data=c[g]}var y=t.get(N);"cartesian2d"!==s.type||m?"polar"!==s.type||m?"singleAxis"!==s.type||m||this._showSinglePointer(r,s,p,l,y):this._showPolarPointer(r,s,p,l,y):this._showCartesianPointer(r,s,p,l,y),"cross"!==a&&this._dispatchAndShowSeriesTooltipContent(s,i[A],l,c,m,n[W])},this),this._tooltipModel.get("show")||this._hideAxisPointer(),u&&this._hide()},_showCartesianPointer:function(t,e,n,a,o){function s(n,r,a){var o="x"===n?i(r[0],a[0],r[0],a[1]):i(a[0],r[1],a[1],r[1]),s=h._getPointerElement(e,t,n,o);x.subPixelOptimizeLine({shape:o,style:s.style}),f?x.updateProps(s,{shape:o},t):s.attr({shape:o})}function u(i,n,a){var o=e.getAxis(i),s=o.getBandWidth(),l=a[1]-a[0],u="x"===i?r(n[0]-s/2,a[0],s,l):r(a[0],n[1]-s/2,l,s),c=h._getPointerElement(e,t,i,u);f?x.updateProps(c,{shape:u},t):c.attr({shape:u})}var h=this,c=t.get("type"),d=e.getBaseAxis(),f=o&&"cross"!==c&&d.type===l&&d.getBandWidth()>20;if("cross"===c)s("x",a,e.getAxis("y").getGlobalExtent()),s("y",a,e.getAxis("x").getGlobalExtent()),this._updateCrossText(e,a,t);else{var p=e.getAxis("x"===n?"y":"x"),m=p.getGlobalExtent();"cartesian2d"===e.type&&("line"===c?s:u)(n,a,m)}},_showSinglePointer:function(t,e,n,r,a){function o(n,r,a){var o=e.getAxis(),l=o.orient,u="horizontal"===l?i(r[0],a[0],r[0],a[1]):i(a[0],r[1],a[1],r[1]),c=s._getPointerElement(e,t,n,u);h?x.updateProps(c,{shape:u},t):c.attr({shape:u})}var s=this,u=t.get("type"),h=a&&"cross"!==u&&e.getBaseAxis().type===l,c=e.getRect(),d=[c.y,c.y+c[K]];o(n,r,d)},_showPolarPointer:function(t,e,n,r,o){function s(n,r,a){var o,s=e.pointToCoord(r);if("angle"===n){var l=e.coordToPoint([a[0],s[1]]),u=e.coordToPoint([a[1],s[1]]);o=i(l[0],l[1],u[0],u[1])}else o={cx:e.cx,cy:e.cy,r:s[0]};var c=h._getPointerElement(e,t,n,o);p?x.updateProps(c,{shape:o},t):c.attr({shape:o})}function u(i,n,r){var o,s=e.getAxis(i),l=s.getBandWidth(),u=e.pointToCoord(n),c=Math.PI/180;o="angle"===i?a(e.cx,e.cy,r[0],r[1],(-u[1]-l/2)*c,(-u[1]+l/2)*c):a(e.cx,e.cy,u[0]-l/2,u[0]+l/2,0,2*Math.PI);var d=h._getPointerElement(e,t,i,o);p?x.updateProps(d,{shape:o},t):d.attr({shape:o})}var h=this,c=t.get("type"),d=e.getAngleAxis(),f=e.getRadiusAxis(),p=o&&"cross"!==c&&e.getBaseAxis().type===l;if("cross"===c)s("angle",r,f[m]()),s("radius",r,d[m]()),this._updateCrossText(e,r,t);else{var v=e.getAxis("radius"===n?"angle":"radius"),g=v[m]();("line"===c?s:u)(n,r,g)}},_updateCrossText:function(t,e,i){var n=i[ne]("crossStyle"),r=n[ne](ie),a=this._tooltipModel,o=this._crossText;o||(o=this._crossText=new x.Text({style:{textAlign:"left",textVerticalAlign:"bottom"}}),this.group.add(o));var s=t.pointToData(e),u=t[f];s=w.map(s,function(e,i){var n=t.getAxis(u[i]);return e=n.type===l||"time"===n.type?n.scale[p](e):T.addCommas(e.toFixed(n.getPixelPrecision()))}),o[q]({fill:r.getTextColor()||n.get("color"),textFont:r[te](),text:s.join(", "),x:e[0]+5,y:e[1]-5}),o.z=a.get("z"),o[b]=a.get(b)},_getPointerElement:function(t,e,i,n){var r=this._tooltipModel,a=r.get("z"),o=r.get(b),s=this._axisPointers,l=t.name;if(s[l]=s[l]||{},s[l][i])return s[l][i];var u=e.get("type"),h=e[ne](u+"Style"),c="shadow"===u,d=h[c?"getAreaStyle":"getLineStyle"](),f="polar"===t.type?c?"Sector":"radius"===i?"Circle":"Line":c?"Rect":"Line";c?d.stroke=null:d.fill=null;var p=s[l][i]=new x[f]({style:d,z:a,zlevel:o,silent:!0,shape:n});return this.group.add(p),p},_dispatchAndShowSeriesTooltipContent:function(t,e,i,n,r,a){var o,s=this._tooltipModel,u=t.getBaseAxis(),h="x"===u.dim||"radius"===u.dim?0:1,c=w.map(e,function(t){return{seriesIndex:t.seriesIndex,dataIndexInside:t.getAxisTooltipDataIndex?t.getAxisTooltipDataIndex(t.coordDimToDataDim(u.dim),n,u):t[D]().indexOfNearest(t.coordDimToDataDim(u.dim)[0],n[h],!1,u.type===l?.5:null)}});w.each(c,function(t,i){e[i][D]().hasValue(t.dataIndexInside)&&(o=i)}),o=o||0;var f=this._lastHover,m=this._api;if(f.payloadBatch&&!r&&m.dispatchAction({type:"downplay",batch:f.payloadBatch}),r||(m.dispatchAction({type:"highlight",batch:c}),f.payloadBatch=c),m.dispatchAction({type:"showTip",dataIndexInside:c[o].dataIndexInside,seriesIndex:c[o].seriesIndex,from:this.uid}),u&&s.get("showContent")&&s.get("show")){var v=w.map(e,function(t,e){return t.getDataParams(c[e].dataIndexInside)});if(r)d(a||s.get(W),i[0],i[1],s.get("confine"),this._tooltipContent,v,null,m);else{var g=c[o].dataIndexInside,y="time"===u.type?u.scale[p](n[h]):e[o][D]().getName(g),_=(y?y+"<br />":"")+w.map(e,function(t,e){return t.formatTooltip(c[e].dataIndexInside,!0)}).join("<br />"),x="axis_"+t.name+"_"+g;this._showTooltipContent(s,_,v,x,i[0],i[1],a,null,m)}}},_showItemTooltipContent:function(t,e,i,n){var r=this._api,a=t[D](i),o=a[c](e),l=o.get("tooltip",!0);if(typeof l===G){var u=l;l={formatter:u}}var h=this._tooltipModel,d=t[ne]("tooltip",h),f=new I(l,d,d[s]),p=t.getDataParams(e,i),m=t.formatTooltip(e,!1,i),v="item_"+t.name+"_"+e;this._showTooltipContent(f,m,p,v,n.offsetX,n.offsetY,n[W],n[Q],r)},_showTooltipContent:function(t,e,i,n,r,a,o,s,l){if(this._ticket="",t.get("showContent")&&t.get("show")){var u=this._tooltipContent,h=t.get("confine"),c=t.get("formatter");o=o||t.get(W);var f=e;if(c)if(typeof c===G)f=T.formatTpl(c,i);else if(typeof c===y){var p=this,m=n,v=function(t,e){t===p._ticket&&(u.setContent(e),d(o,r,a,h,u,i,s,l))};p._ticket=m,f=c(i,m,v)}u.show(t),u.setContent(f),d(o,r,a,h,u,i,s,l)}},_showAxisPointer:function(t){if(t){var e=this._axisPointers[t];e&&w.each(e,function(t){t.show()
})}else this.group.eachChild(function(t){t.show()}),this.group.show()},_resetLastHover:function(){var t=this._lastHover;t.payloadBatch&&this._api.dispatchAction({type:"downplay",batch:t.payloadBatch}),this._lastHover={}},_hideAxisPointer:function(t){if(t){var e=this._axisPointers[t];e&&w.each(e,function(t){t.hide()})}else this.group.children()[R]&&this.group.hide()},_hide:function(){clearTimeout(this._showTimeout),this._hideAxisPointer(),this._resetLastHover(),this._alwaysShowContent||this._tooltipContent.hideLater(this._tooltipModel.get("hideDelay")),this._api.dispatchAction({type:"hideTip",from:this.uid}),this._lastX=this._lastY=null},dispose:function(t,e){if(!P.node){var i=e.getZr();this._tooltipContent.hide(),i.off("click",this._tryShow),i.off("mousemove",this._mousemove),i.off("mouseout",this._hide),i.off("globalout",this._hide),e.off("showTip",this._manuallyShowTip),e.off("hideTip",this._manuallyHideTip)}}})}),e("echarts/component/dataZoom/dataZoomProcessor",[le,"../../echarts"],function(t){function e(t,e,i){i.getAxisProxy(t.name,e).reset(i)}function i(t,e,i){i.getAxisProxy(t.name,e).filterData(i)}var n=t("../../echarts");n.registerProcessor(function(t){t.eachComponent("dataZoom",function(t){t.eachTargetAxis(e),t.eachTargetAxis(i)}),t.eachComponent("dataZoom",function(t){var e=t.findRepresentativeAxisProxy(),i=e.getDataPercentWindow(),n=e.getDataValueWindow();t.setRawRange({start:i[0],end:i[1],startValue:n[0],endValue:n[1]})})})}),e("echarts/component/dataZoom/dataZoomAction",[le,se,"./helper","../../echarts"],function(t){var e=t(se),i=t("./helper"),n=t("../../echarts");n.registerAction("dataZoom",function(t,n){var r=i.createLinkedNodesFinder(e.bind(n.eachComponent,n,"dataZoom"),i.eachAxisDim,function(t,e){return t.get(e.axisIndex)}),a=[];n.eachComponent({mainType:"dataZoom",query:t},function(t){a.push.apply(a,r(t).nodes)}),e.each(a,function(e){e.setRawRange({start:t.start,end:t.end,startValue:t.startValue,endValue:t.endValue})})})}),e("echarts/util/graphic",[le,se,"zrender/tool/path","zrender/graphic/Path","zrender/tool/color","zrender/core/matrix",V,"zrender/container/Group","zrender/graphic/Image","zrender/graphic/Text","zrender/graphic/shape/Circle","zrender/graphic/shape/Sector","zrender/graphic/shape/Ring","zrender/graphic/shape/Polygon","zrender/graphic/shape/Polyline","zrender/graphic/shape/Rect","zrender/graphic/shape/Line","zrender/graphic/shape/BezierCurve","zrender/graphic/shape/Arc","zrender/graphic/CompoundPath","zrender/graphic/LinearGradient","zrender/graphic/RadialGradient","zrender/core/BoundingRect"],function(t){function e(t){return null!=t&&"none"!=t}function i(t){return typeof t===G?T.lift(t,-.1):t}function a(t){if(t.__hoverStlDirty){var n=t.style.stroke,r=t.style.fill,a=t.__hoverStl;a.fill=a.fill||(e(r)?i(r):null),a.stroke=a.stroke||(e(n)?i(n):null);var o={};for(var s in a)a.hasOwnProperty(s)&&(o[s]=t.style[s]);t.__normalStl=o,t.__hoverStlDirty=!1}}function o(t){t.__isHover||(a(t),t.useHoverLayer?t.__zr&&t.__zr.addHover(t,t.__hoverStl):(t[q](t.__hoverStl),t.z2+=1),t.__isHover=!0)}function s(t){if(t.__isHover){var e=t.__normalStl;t.useHoverLayer?t.__zr&&t.__zr.removeHover(t):(e&&t[q](e),t.z2-=1),t.__isHover=!1}}function l(t){"group"===t.type?t[w](function(t){"group"!==t.type&&o(t)}):o(t)}function u(t){"group"===t.type?t[w](function(t){"group"!==t.type&&s(t)}):s(t)}function c(t,e){t.__hoverStl=t.hoverStyle||e||{},t.__hoverStlDirty=!0,t.__isHover&&a(t)}function d(t){this.__hoverSilentOnTouch&&t.zrByTouch||!this.__isEmphasis&&l(this)}function f(t){this.__hoverSilentOnTouch&&t.zrByTouch||!this.__isEmphasis&&u(this)}function p(){this.__isEmphasis=!0,l(this)}function m(){this.__isEmphasis=!1,u(this)}function v(t,e,i,n,r,a){typeof r===y&&(a=r,r=null);var o=n&&(n.ifEnableAnimation?n.ifEnableAnimation():n[h](N));if(o){var s=t?"Update":"",l=n&&n[h]("animationDuration"+s),u=n&&n[h]("animationEasing"+s),c=n&&n[h]("animationDelay"+s);typeof c===y&&(c=c(r)),l>0?e.animateTo(i,l,c||0,u,a):(e.attr(i),a&&a())}else e.attr(i),a&&a()}var g=t(se),_=t("zrender/tool/path"),x=Math.round,b=t("zrender/graphic/Path"),T=t("zrender/tool/color"),M=t("zrender/core/matrix"),A=t(V),S={};return S.Group=t("zrender/container/Group"),S.Image=t("zrender/graphic/Image"),S.Text=t("zrender/graphic/Text"),S.Circle=t("zrender/graphic/shape/Circle"),S.Sector=t("zrender/graphic/shape/Sector"),S.Ring=t("zrender/graphic/shape/Ring"),S.Polygon=t("zrender/graphic/shape/Polygon"),S.Polyline=t("zrender/graphic/shape/Polyline"),S.Rect=t("zrender/graphic/shape/Rect"),S.Line=t("zrender/graphic/shape/Line"),S.BezierCurve=t("zrender/graphic/shape/BezierCurve"),S.Arc=t("zrender/graphic/shape/Arc"),S.CompoundPath=t("zrender/graphic/CompoundPath"),S.LinearGradient=t("zrender/graphic/LinearGradient"),S.RadialGradient=t("zrender/graphic/RadialGradient"),S.BoundingRect=t("zrender/core/BoundingRect"),S.extendShape=function(t){return b[C](t)},S.extendPath=function(t,e){return _.extendFromString(t,e)},S.makePath=function(t,e,i,n){var r=_.createFromString(t,e),a=r[J]();if(i){var o=a.width/a[K];if(n===X){var s,l=i[K]*o;l<=i.width?s=i[K]:(l=i.width,s=l/o);var u=i.x+i.width/2,h=i.y+i[K]/2;i.x=u-l/2,i.y=h-s/2,i.width=l,i[K]=s}this.resizePath(r,i)}return r},S.mergePath=_.mergePath,S.resizePath=function(t,e){if(t[n]){var i=t[J](),r=i.calculateTransform(e);t[n](r)}},S.subPixelOptimizeLine=function(t){var e=S.subPixelOptimize,i=t.shape,n=t.style.lineWidth;return x(2*i.x1)===x(2*i.x2)&&(i.x1=i.x2=e(i.x1,n,!0)),x(2*i.y1)===x(2*i.y2)&&(i.y1=i.y2=e(i.y1,n,!0)),t},S.subPixelOptimizeRect=function(t){var e=S.subPixelOptimize,i=t.shape,n=t.style.lineWidth,r=i.x,a=i.y,o=i.width,s=i[K];return i.x=e(i.x,n,!0),i.y=e(i.y,n,!0),i.width=Math.max(e(r+o,n,!1)-i.x,0===o?0:1),i[K]=Math.max(e(a+s,n,!1)-i.y,0===s?0:1),t},S.subPixelOptimize=function(t,e,i){var n=x(2*t);return(n+x(e))%2===0?n/2:(n+(i?1:-1))/2},S.setHoverStyle=function(t,e,i){t.__hoverSilentOnTouch=i&&i.hoverSilentOnTouch,"group"===t.type?t[w](function(t){"group"!==t.type&&c(t,e)}):c(t,e),t.on("mouseover",d).on("mouseout",f),t.on("emphasis",p).on("normal",m)},S.setText=function(t,e,i){var n=e[h](W)||"inside",r=n[O]("inside")>=0?"white":i,a=e[ne](ie);g[C](t,{textDistance:e[h]("distance")||5,textFont:a[te](),textPosition:n,textFill:a.getTextColor()||r})},S.updateProps=function(t,e,i,n,r){v(!0,t,e,i,n,r)},S.initProps=function(t,e,i,n,r){v(!1,t,e,i,n,r)},S.getTransform=function(t,e){for(var i=M.identity([]);t&&t!==e;)M.mul(i,t.getLocalTransform(),i),t=t[r];return i},S[n]=function(t,e,i){return i&&(e=M.invert([],e)),A[n]([],t,e)},S.transformDirection=function(t,e,i){var r=0===e[4]||0===e[5]||0===e[0]?1:Math.abs(2*e[4]/e[0]),a=0===e[4]||0===e[5]||0===e[2]?1:Math.abs(2*e[4]/e[2]),o=["left"===t?-r:"right"===t?r:0,"top"===t?-a:t===U?a:0];return o=S[n](o,e,i),Math.abs(o[0])>Math.abs(o[1])?o[0]>0?"right":"left":o[1]>0?U:"top"},S.groupTransition=function(t,e,i){function n(t){var e={};return t[w](function(t){!t.isGroup&&t.anid&&(e[t.anid]=t)}),e}function r(t){var e={position:A.clone(t[W]),rotation:t.rotation};return t.shape&&(e.shape=g[C]({},t.shape)),e}if(t&&e){var a=n(t);e[w](function(t){if(!t.isGroup&&t.anid){var e=a[t.anid];if(e){var n=r(t);t.attr(r(e)),S.updateProps(t,n,i,t[L])}}})}},S}),e("echarts/coord/cartesian/Grid",[le,"exports","../../util/layout","../../coord/axisHelper",se,"./Cartesian2D","./Axis2D","./GridModel","../../CoordinateSystem"],function(t){function e(t,e){return t.findGridModel()===e}function i(t){var e,i=t.model,n=i.getFormattedLabels(),r=i[ne]("axisLabel.textStyle"),a=1,o=n[R];o>40&&(a=Math.ceil(o/40));for(var s=0;o>s;s+=a)if(!t.isLabelIgnored(s)){var l=r.getTextRect(n[s]);e?e.union(l):e=l}return e}function n(t,e,i){this._coordsMap={},this._coordsList=[],this._axesMap={},this._axesList=[],this._initCartesian(t,e,i),this._model=t}function r(t,e){var i=t[m](),n=i[0]+i[1];t.toGlobalCoord="x"===t.dim?function(t){return t+e}:function(t){return n-t+e},t.toLocalCoord="x"===t.dim?function(t){return t-e}:function(t){return n-t+e}}function a(t){return h.map(x,function(e){var i=t.getReferringComponents(e)[0];return i})}function o(t){return"cartesian2d"===t.get(ae)}var s=t("../../util/layout"),u=t("../../coord/axisHelper"),h=t(se),c=t("./Cartesian2D"),p=t("./Axis2D"),v=h.each,g=u.ifAxisCrossZero,y=u.niceScaleExtent;t("./GridModel");var _=n[Z];_.type="grid",_.getRect=function(){return this._rect},_[z]=function(t,e){function i(t){var e=n[t];for(var i in e)if(e.hasOwnProperty(i)){var r=e[i];if(r&&(r.type===l||!g(r)))return!0}return!1}var n=this._axesMap;this._updateScale(t,this._model),v(n.x,function(t){y(t,t.model)}),v(n.y,function(t){y(t,t.model)}),v(n.x,function(t){i("y")&&(t.onZero=!1)}),v(n.y,function(t){i("x")&&(t.onZero=!1)}),this.resize(this._model,e)},_.resize=function(t,e){function n(){v(o,function(t){var e=t.isHorizontal(),i=e?[0,a.width]:[0,a[K]],n=t.inverse?1:0;t.setExtent(i[n],i[1-n]),r(t,e?a.x:a.y)})}var a=s.getLayoutRect(t.getBoxLayoutParams(),{width:e[$](),height:e[Y]()});this._rect=a;var o=this._axesList;n(),t.get("containLabel")&&(v(o,function(t){if(!t.model.get("axisLabel.inside")){var e=i(t);if(e){var n=t.isHorizontal()?K:"width",r=t.model.get("axisLabel.margin");a[n]-=e[n]+r,"top"===t[W]?a.y+=e[K]+r:"left"===t[W]&&(a.x+=e.width+r)}}}),n())},_.getAxis=function(t,e){var i=this._axesMap[t];if(null!=i){if(null==e)for(var n in i)if(i.hasOwnProperty(n))return i[n];return i[e]}},_.getCartesian=function(t,e){if(null!=t&&null!=e){var i="x"+t+"y"+e;return this._coordsMap[i]}for(var n=0,r=this._coordsList;n<r[R];n++)if(r[n].getAxis("x").index===t||r[n].getAxis("y").index===e)return r[n]},_.convertToPixel=function(t,e,i){var n=this._findConvertTarget(t,e);return n.cartesian?n.cartesian.dataToPoint(i):n.axis?n.axis.toGlobalCoord(n.axis.dataToCoord(i)):null},_.convertFromPixel=function(t,e,i){var n=this._findConvertTarget(t,e);return n.cartesian?n.cartesian.pointToData(i):n.axis?n.axis.coordToData(n.axis.toLocalCoord(i)):null},_._findConvertTarget=function(t,e){var i,n,r=e.seriesModel,a=e.xAxisModel||r&&r.getReferringComponents("xAxis")[0],o=e.yAxisModel||r&&r.getReferringComponents("yAxis")[0],s=e.gridModel,l=this._coordsList;if(r)i=r[ae],h[O](l,i)<0&&(i=null);else if(a&&o)i=this.getCartesian(a.componentIndex,o.componentIndex);else if(a)n=this.getAxis("x",a.componentIndex);else if(o)n=this.getAxis("y",o.componentIndex);else if(s){var u=s[ae];u===this&&(i=this._coordsList[0])}return{cartesian:i,axis:n}},_.containPoint=function(t){var e=this._coordsList[0];return e?e.containPoint(t):void 0},_._initCartesian=function(t,i){function n(n){return function(s,h){if(e(s,t,i)){var c=s.get(W);"x"===n?"top"!==c&&c!==U&&(c=U,r[c]&&(c="top"===c?U:"top")):"left"!==c&&"right"!==c&&(c="left",r[c]&&(c="left"===c?"right":"left")),r[c]=!0;var d=new p(n,u.createScaleByModel(s),[0,0],s.get("type"),c),f=d.type===l;d.onBand=f&&s.get("boundaryGap"),d.inverse=s.get("inverse"),d.onZero=s.get("axisLine.onZero"),s.axis=d,d.model=s,d.grid=this,d.index=h,this._axesList.push(d),a[n][h]=d,o[n]++}}}var r={left:!1,right:!1,top:!1,bottom:!1},a={x:{},y:{}},o={x:0,y:0};return i.eachComponent("xAxis",n("x"),this),i.eachComponent("yAxis",n("y"),this),o.x&&o.y?(this._axesMap=a,void v(a.x,function(t,e){v(a.y,function(i,n){var r="x"+e+"y"+n,a=new c(r);a.grid=this,this._coordsMap[r]=a,this._coordsList.push(a),a.addAxis(t),a.addAxis(i)},this)},this)):(this._axesMap={},void(this._axesList=[]))},_._updateScale=function(t,i){function n(t,e,i){v(i.coordDimToDataDim(e.dim),function(i){e.scale.unionExtent(t.getDataExtent(i,e.scale.type!==d))})}h.each(this._axesList,function(t){t.scale.setExtent(1/0,-1/0)}),t.eachSeries(function(r){if(o(r)){var s=a(r,t),l=s[0],u=s[1];if(!e(l,i,t)||!e(u,i,t))return;var h=this.getCartesian(l.componentIndex,u.componentIndex),c=r[D](),d=h.getAxis("x"),f=h.getAxis("y");"list"===c.type&&(n(c,d,r),n(c,f,r))}},this)};var x=["xAxis","yAxis"];return n[k]=function(t,e){var i=[];return t.eachComponent("grid",function(r,a){var o=new n(r,t,e);o.name="grid_"+a,o.resize(r,e),r[ae]=o,i.push(o)}),t.eachSeries(function(e){if(o(e)){var i=a(e,t),n=i[0],r=i[1],s=n.findGridModel(),l=s[ae];e[ae]=l.getCartesian(n.componentIndex,r.componentIndex)}}),i},n[f]=c[Z][f],t("../../CoordinateSystem").register("cartesian2d",n),n}),e("echarts/component/axis",[le,"../coord/cartesian/AxisModel","./axis/AxisView"],function(t){t("../coord/cartesian/AxisModel"),t("./axis/AxisView")}),e("echarts/util/layout",[le,se,"zrender/core/BoundingRect","./number","./format"],function(t){function e(t,e,i,n,r){var a=0,o=0;null==n&&(n=1/0),null==r&&(r=1/0);var s=0;e.eachChild(function(l,u){var h,c,d=l[W],f=l[J](),p=e.childAt(u+1),m=p&&p[J]();if("horizontal"===t){var v=f.width+(m?-m.x+f.x:0);h=a+v,h>n||l.newline?(a=0,h=v,o+=s+i,s=f[K]):s=Math.max(s,f[K])}else{var g=f[K]+(m?-m.y+f.y:0);c=o+g,c>r||l.newline?(a+=s+i,o=0,c=g,s=f.width):s=Math.max(s,f.width)}l.newline||(d[0]=a,d[1]=o,"horizontal"===t?a=h+i:o=c+i)})}var i=t(se),r=t("zrender/core/BoundingRect"),a=t("./number"),o=t("./format"),s=a.parsePercent,l=i.each,u={},h=u.LOCATION_PARAMS=["left","right","top",U,"width",K];return u.box=e,u.vbox=i.curry(e,"vertical"),u.hbox=i.curry(e,"horizontal"),u.getAvailableSize=function(t,e,i){var n=e.width,r=e[K],a=s(t.x,n),l=s(t.y,r),u=s(t.x2,n),h=s(t.y2,r);return(isNaN(a)||isNaN(parseFloat(t.x)))&&(a=0),(isNaN(u)||isNaN(parseFloat(t.x2)))&&(u=n),(isNaN(l)||isNaN(parseFloat(t.y)))&&(l=0),(isNaN(h)||isNaN(parseFloat(t.y2)))&&(h=r),i=o.normalizeCssArray(i||0),{width:Math.max(u-a-i[1]-i[3],0),height:Math.max(h-l-i[0]-i[2],0)}},u.getLayoutRect=function(t,e,i){i=o.normalizeCssArray(i||0);var n=e.width,a=e[K],l=s(t.left,n),u=s(t.top,a),h=s(t.right,n),c=s(t[U],a),d=s(t.width,n),f=s(t[K],a),p=i[2]+i[0],m=i[1]+i[3],v=t.aspect;switch(isNaN(d)&&(d=n-h-m-l),isNaN(f)&&(f=a-c-p-u),isNaN(d)&&isNaN(f)&&(v>n/a?d=.8*n:f=.8*a),null!=v&&(isNaN(d)&&(d=v*f),isNaN(f)&&(f=d/v)),isNaN(l)&&(l=n-h-d-m),isNaN(u)&&(u=a-c-f-p),t.left||t.right){case X:l=n/2-d/2-i[3];break;case"right":l=n-d-m}switch(t.top||t[U]){case j:case X:u=a/2-f/2-i[0];break;case U:u=a-f-p}l=l||0,u=u||0,isNaN(d)&&(d=n-l-(h||0)),isNaN(f)&&(f=a-u-(c||0));var g=new r(l+i[3],u+i[0],d,f);return g.margin=i,g},u.positionElement=function(t,e,a,o,s){var l=!s||!s.hv||s.hv[0],h=!s||!s.hv||s.hv[1],c=s&&s.boundingMode||"all";if(l||h){var d;if("raw"===c)d="group"===t.type?new r(0,0,+e.width||0,+e[K]||0):t[J]();else if(d=t[J](),t.needLocalTransform()){var f=t.getLocalTransform();d=d.clone(),d[n](f)}e=u.getLayoutRect(i[re]({width:d.width,height:d[K]},e),a,o);var p=t[W],m=l?e.x-d.x:0,v=h?e.y-d.y:0;t.attr(W,"raw"===c?[m,v]:[p[0]+m,p[1]+v])}},u.mergeLayoutParam=function(t,e,n){function r(i){var r={},s=0,u={},h=0,c=n.ignoreSize?1:2;if(l(i,function(e){u[e]=t[e]}),l(i,function(t){a(e,t)&&(r[t]=u[t]=e[t]),o(r,t)&&s++,o(u,t)&&h++}),h!==c&&s){if(s>=c)return r;for(var d=0;d<i[R];d++){var f=i[d];if(!a(r,f)&&a(t,f)){r[f]=t[f];break}}return r}return u}function a(t,e){return t.hasOwnProperty(e)}function o(t,e){return null!=t[e]&&"auto"!==t[e]}function s(t,e,i){l(t,function(t){e[t]=i[t]})}!i[P](n)&&(n={});var u=["width","left","right"],h=[K,"top",U],c=r(u),d=r(h);s(u,t,c),s(h,t,d)},u.getLayoutParams=function(t){return u.copyLayoutParams({},t)},u.copyLayoutParams=function(t,e){return e&&t&&l(h,function(i){e.hasOwnProperty(i)&&(t[i]=e[i])}),t},u}),e(H,[],function(){function t(t){var e={},i={},n=t.match(/Firefox\/([\d.]+)/),r=t.match(/MSIE\s([\d.]+)/)||t.match(/Trident\/.+?rv:(([\d.]+))/),a=t.match(/Edge\/([\d.]+)/),o=/micromessenger/i.test(t);return n&&(i.firefox=!0,i.version=n[1]),r&&(i.ie=!0,i.version=r[1]),a&&(i.edge=!0,i.version=a[1]),o&&(i.weChat=!0),{browser:i,os:e,node:!1,canvasSupported:document.createElement("canvas").getContext?!0:!1,touchEventsSupported:"ontouchstart"in window&&!i.ie&&!i.edge,pointerEventsSupported:"onpointerdown"in window&&(i.edge||i.ie&&i.version>=10)}}var e={};return e=typeof navigator===i?{browser:{},os:{},node:!0,canvasSupported:!0}:t(navigator.userAgent)}),e("echarts/ExtensionAPI",[le,se],function(t){function e(t){i.each(n,function(e){this[e]=i.bind(t[e],t)},this)}var i=t(se),n=["getDom","getZr",$,Y,"dispatchAction","isDisposed","on","off","getDataURL","getConnectedDataURL",ne,"getOption"];return e}),e("echarts/model/Global",[le,se,"../util/model","./Model","./Component","./globalDefault","./mixin/colorPalette"],function(t){function e(t,e){h.each(e,function(e,i){w.hasClass(i)||("object"==typeof e?t[i]=t[i]?h.merge(t[i],e,!1):h.clone(e):null==t[i]&&(t[i]=e))})}function i(t){t=t,this[u]={},this[u][S]=1,this._componentsMap={},this._seriesIndices=null,e(t,this._theme[u]),h.merge(t,M,!1),this.mergeOption(t)}function n(t,e){h[_](e)||(e=e?[e]:[]);var i={};return f(e,function(e){i[e]=(t[e]||[]).slice()}),i}function r(t,e,i){var n=e.type?e.type:i?i.subType:w.determineSubType(t,e);return n}function a(t){return m(t,function(t){return t.componentIndex})||[]}function s(t,e){return e.hasOwnProperty("subType")?p(t,function(t){return t.subType===e.subType}):t}function l(t){}var h=t(se),c=t("../util/model"),d=t("./Model"),f=h.each,p=h.filter,m=h.map,v=h[_],x=h[O],b=h[P],w=t("./Component"),M=t("./globalDefault"),S="\x00_ec_inner",I=d[C]({constructor:I,init:function(t,e,i,n){i=i||{},this[u]=null,this._theme=new d(i),this._optionManager=n},setOption:function(t,e){h.assert(!(S in t),"please use chart.getOption()"),this._optionManager.setOption(t,e),this.resetOption()},resetOption:function(t){var e=!1,n=this._optionManager;if(!t||"recreate"===t){var r=n.mountOption("recreate"===t);this[u]&&"recreate"!==t?(this.restoreData(),this.mergeOption(r)):i.call(this,r),e=!0}if(("timeline"===t||"media"===t)&&this.restoreData(),!t||"recreate"===t||"timeline"===t){var a=n.getTimelineOption(this);a&&(this.mergeOption(a),e=!0)}if(!t||"recreate"===t||"media"===t){var o=n.getMediaOption(this,this._api);o[R]&&f(o,function(t){this.mergeOption(t,e=!0)},this)}return e},mergeOption:function(t){function e(e,s){var l=c.normalizeToArray(t[e]),d=c.mappingToExists(o[e],l);c.makeIdAndName(d),f(d,function(t){var i=t[u];b(i)&&(t.keyInfo.mainType=e,t.keyInfo.subType=r(e,i,t.exist))});var p=n(o,s);i[e]=[],o[e]=[],f(d,function(t,n){var r=t.exist,a=t[u];if(h.assert(b(a)||r,"Empty component definition"),a){var s=w.getClass(e,t.keyInfo.subType,!0);if(r&&r instanceof s)r.name=t.keyInfo.name,r.mergeOption(a,this),r.optionUpdated(a,!1);else{var l=h[C]({dependentModels:p,componentIndex:n},t.keyInfo);r=new s(a,this,this,l),h[C](r,l),r.init(a,this,this,l),r.optionUpdated(null,!0)}}else r.mergeOption({},this),r.optionUpdated({},!1);o[e][n]=r,i[e][n]=r[u]},this),e===A&&(this._seriesIndices=a(o[A]))}var i=this[u],o=this._componentsMap,s=[];f(t,function(t,e){null!=t&&(w.hasClass(e)?s.push(e):i[e]=null==i[e]?h.clone(t):h.merge(i[e],t,!0))}),w.topologicalTravel(s,w.getAllClassMainTypes(),e,this),this._seriesIndices=this._seriesIndices||[]},getOption:function(){var t=h.clone(this[u]);return f(t,function(e,i){if(w.hasClass(i)){for(var e=c.normalizeToArray(e),n=e[R]-1;n>=0;n--)c.isIdInner(e[n])&&e[T](n,1);t[i]=e}}),delete t[S],t},getTheme:function(){return this._theme},getComponent:function(t,e){var i=this._componentsMap[t];return i?i[e||0]:void 0},queryComponents:function(t){var e=t.mainType;if(!e)return[];var i=t.index,n=t.id,r=t.name,a=this._componentsMap[e];if(!a||!a[R])return[];var o;if(null!=i)v(i)||(i=[i]),o=p(m(i,function(t){return a[t]}),function(t){return!!t});else if(null!=n){var l=v(n);o=p(a,function(t){return l&&x(n,t.id)>=0||!l&&t.id===n})}else if(null!=r){var u=v(r);o=p(a,function(t){return u&&x(r,t.name)>=0||!u&&t.name===r})}else o=a;return s(o,t)},findComponents:function(t){function e(t){var e=r+"Index",i=r+"Id",n=r+"Name";return t&&(t.hasOwnProperty(e)||t.hasOwnProperty(i)||t.hasOwnProperty(n))?{mainType:r,index:t[e],id:t[i],name:t[n]}:null}function i(e){return t.filter?p(e,t.filter):e}var n=t.query,r=t.mainType,a=e(n),l=a?this[o](a):this._componentsMap[r];return i(s(l,t))},eachComponent:function(t,e,i){var n=this._componentsMap;if(typeof t===y)i=e,e=t,f(n,function(t,n){f(t,function(t,r){e.call(i,n,t,r)})});else if(h[g](t))f(n[t],e,i);else if(b(t)){var r=this.findComponents(t);f(r,e,i)}},getSeriesByName:function(t){var e=this._componentsMap[A];return p(e,function(e){return e.name===t})},getSeriesByIndex:function(t){return this._componentsMap[A][t]},getSeriesByType:function(t){var e=this._componentsMap[A];return p(e,function(e){return e.subType===t})},getSeries:function(){return this._componentsMap[A].slice()},eachSeries:function(t,e){l(this),f(this._seriesIndices,function(i){var n=this._componentsMap[A][i];t.call(e,n,i)},this)},eachRawSeries:function(t,e){f(this._componentsMap[A],t,e)},eachSeriesByType:function(t,e,i){l(this),f(this._seriesIndices,function(n){var r=this._componentsMap[A][n];r.subType===t&&e.call(i,r,n)},this)},eachRawSeriesByType:function(t,e,i){return f(this.getSeriesByType(t),e,i)},isSeriesFiltered:function(t){return l(this),h[O](this._seriesIndices,t.componentIndex)<0},filterSeries:function(t,e){l(this);var i=p(this._componentsMap[A],t,e);this._seriesIndices=a(i)},restoreData:function(){var t=this._componentsMap;this._seriesIndices=a(t[A]);var e=[];f(t,function(t,i){e.push(i)}),w.topologicalTravel(e,w.getAllClassMainTypes(),function(e){f(t[e],function(t){t.restoreData()})})}});return h.mixin(I,t("./mixin/colorPalette")),I}),e("echarts/component/graphic",[le,"../echarts",se,"../util/model","../util/graphic","../util/format","../util/layout"],function(t){function e(t,e,i,n){var r=i.type,a=h[r.charAt(0).toUpperCase()+r.slice(1)],o=new a(i);e.add(o),n[t]=o,o.__ecGraphicId=t}function i(t,e){var n=t&&t[r];n&&("group"===t.type&&t[w](function(t){i(t,e)}),delete e[t.__ecGraphicId],n[S](t))}function n(t){return t=s[C]({},t),s.each(["id","parentId","$action","hv","bounding"].concat(c.LOCATION_PARAMS),function(e){delete t[e]}),t}function a(t,e){var i;return s.each(e,function(e){null!=t[e]&&"auto"!==t[e]&&(i=!0)}),i}var o=t("../echarts"),s=t(se),l=t("../util/model"),h=t("../util/graphic"),c=(t("../util/format"),t("../util/layout"));o.registerPreprocessor(function(t){var e=t&&t.graphic;s[_](e)?t.graphic=e[0]&&e[0].elements?[t.graphic[0]]:[{elements:e}]:e&&!e.elements&&(t.graphic=[{elements:[e]}])});var d=o.extendComponentModel({type:"graphic",defaultOption:{elements:[],parentId:null},_elOptionsToUpdate:null,mergeOption:function(){var t=this[u].elements;this[u].elements=null,d.superApply(this,"mergeOption",arguments),this[u].elements=t},optionUpdated:function(t,e){var i=this[u],n=(e?i:t).elements,r=i.elements=e?[]:i.elements,o=[];this._flatten(n,o);var h=l.mappingToExists(r,o);l.makeIdAndName(h);var d=this._elOptionsToUpdate=[];s.each(h,function(t,e){var i=t.exist,n=t[u];if(n){n.id=t.keyInfo.id;var o=n.parentId,l=n.parentOption,h=i&&i.parentId;!n.type&&i&&(n.type=i.type),n.parentId=o?o:l?l.id:h?h:null,n.parentOption=null,d.push(n);var f=s[C]({},n),p=n.$action;if(p&&"merge"!==p)p===x?r[e]=f:p===S&&i&&(r[e]=null);else if(i){s.merge(i,f,!0),c.mergeLayoutParam(i,f,{ignoreSize:!0}),c.copyLayoutParams(n,i)}else r[e]=f;r[e]&&(r[e].hv=n.hv=[a(n,["left","right"]),a(n,["top",U])],"group"===r[e].type&&(null==r[e].width&&(r[e].width=n.width=0),null==r[e][K]&&(r[e][K]=n[K]=0)))}},this);for(var f=r[R]-1;f>=0;f--)null==r[f]?r[T](f,1):delete r[f].$action},_flatten:function(t,e,i){s.each(t,function(t){if(t){i&&(t.parentOption=i),e.push(t);var n=t.children;"group"===t.type&&n&&this._flatten(n,e,t),delete t.children}},this)},useElOptionsToUpdate:function(){var t=this._elOptionsToUpdate;return this._elOptionsToUpdate=null,t}});o.extendComponentView({type:"graphic",init:function(){this._elMap={},this._lastGraphicModel},render:function(t,e,i){t!==this._lastGraphicModel&&this._clear(),this._lastGraphicModel=t,this._updateElements(t,i),this._relocate(t,i)},_updateElements:function(t){var r=t.useElOptionsToUpdate();if(r){var a=this._elMap,o=this.group;s.each(r,function(t){var r=t.$action,l=t.id,u=a[l],h=t.parentId,c=null!=h?a[h]:o;t.hv&&t.hv[1]&&"text"===t.type&&(t.style=s[re]({textBaseline:"middle"},t.style),t.style.textVerticalAlign=null);var d=n(t);r&&"merge"!==r?r===x?(i(u,a),e(l,c,d,a)):r===S&&i(u,a):u?u.attr(d):e(l,c,d,a),a[l]&&(a[l].__ecGraphicWidth=t.width,a[l].__ecGraphicHeight=t[K])})}},_relocate:function(t,e){for(var i=t[u].elements,n=this.group,a=this._elMap,o=i[R]-1;o>=0;o--){var s=i[o],l=a[s.id];if(l){var h=l[r],d=h===n?{width:e[$](),height:e[Y]()}:{width:h.__ecGraphicWidth||0,height:h.__ecGraphicHeight||0};c.positionElement(l,s,d,null,{hv:s.hv,boundingMode:s.bounding})}}},_clear:function(){var t=this._elMap;s.each(t,function(e){i(e,t)}),this._elMap={}},dispose:function(){this._clear()}})}),e("echarts/CoordinateSystem",[le,se],function(t){function e(){this._coordinateSystems=[]}var i=t(se),n={};return e[Z]={constructor:e,create:function(t,e){var r=[];i.each(n,function(i){var n=i[k](t,e);r=r.concat(n||[])}),this._coordinateSystems=r},update:function(t,e){i.each(this._coordinateSystems,function(i){i[z]&&i[z](t,e)})},getCoordinateSystems:function(){return this._coordinateSystems.slice()}},e.register=function(t,e){n[t]=e},e.get=function(t){return n[t]},e}),e("echarts/model/OptionManager",[le,se,"../util/model","./Component"],function(t){function e(t){this._api=t,this._timelineOptions=[],this._mediaList=[],this._mediaDefault,this._currentMediaIndices=[],this._optionBackup,this._newBaseOption}function i(t,e,i){var n,r,a=[],o=[],l=t.timeline;if(t.baseOption&&(r=t.baseOption),(l||t.options)&&(r=r||{},a=(t.options||[]).slice()),t.media){r=r||{};var h=t.media;c(h,function(t){t&&t[u]&&(t.query?o.push(t):n||(n=t))})}return r||(r=t),r.timeline||(r.timeline=l),c([r].concat(a).concat(s.map(o,function(t){return t[u]})),function(t){c(e,function(e){e(t,i)})}),{baseOption:r,timelineOptions:a,mediaDefault:n,mediaList:o}}function n(t,e,i){var n={width:e,height:i,aspectratio:e/i},a=!0;return s.each(t,function(t,e){var i=e.match(m);if(i&&i[1]&&i[2]){var o=i[1],s=i[2][F]();r(n[s],t,o)||(a=!1)}}),a}function r(t,e,i){return"min"===i?t>=e:"max"===i?e>=t:t===e}function a(t,e){return t.join(",")===e.join(",")}function o(t,e){e=e||{},c(e,function(e,i){if(null!=e){var n=t[i];if(h.hasClass(i)){e=l.normalizeToArray(e),n=l.normalizeToArray(n);var r=l.mappingToExists(n,e);t[i]=f(r,function(t){return t[u]&&t.exist?p(t.exist,t[u],!0):t.exist||t[u]})}else t[i]=p(n,e,!0)}})}var s=t(se),l=t("../util/model"),h=t("./Component"),c=s.each,d=s.clone,f=s.map,p=s.merge,m=/^(min|max)?(.+)$/;return e[Z]={constructor:e,setOption:function(t,e){t=d(t,!0);var n=this._optionBackup,r=i.call(this,t,e,!n);this._newBaseOption=r.baseOption,n?(o(n.baseOption,r.baseOption),r.timelineOptions[R]&&(n.timelineOptions=r.timelineOptions),r.mediaList[R]&&(n.mediaList=r.mediaList),r.mediaDefault&&(n.mediaDefault=r.mediaDefault)):this._optionBackup=r},mountOption:function(t){var e=this._optionBackup;return this._timelineOptions=f(e.timelineOptions,d),this._mediaList=f(e.mediaList,d),this._mediaDefault=d(e.mediaDefault),this._currentMediaIndices=[],d(t?e.baseOption:this._newBaseOption)},getTimelineOption:function(t){var e,i=this._timelineOptions;if(i[R]){var n=t.getComponent("timeline");n&&(e=d(i[n.getCurrentIndex()],!0))}return e},getMediaOption:function(){var t=this._api[$](),e=this._api[Y](),i=this._mediaList,r=this._mediaDefault,o=[],s=[];if(!i[R]&&!r)return s;for(var l=0,h=i[R];h>l;l++)n(i[l].query,t,e)&&o.push(l);return!o[R]&&r&&(o=[-1]),o[R]&&!a(o,this._currentMediaIndices)&&(s=f(o,function(t){return d(-1===t?r[u]:i[t][u])})),this._currentMediaIndices=o,s}},e}),e("echarts/model/Component",[le,"./Model",se,"../util/component","../util/clazz","../util/layout","./mixin/boxLayout"],function(t){function e(t){var e=[];return n.each(c.getClassesByMainType(t),function(t){r.apply(e,t[Z].dependencies||[])}),n.map(e,function(t){return l.parseClassType(t).main})}var i=t("./Model"),n=t(se),r=Array[Z].push,a=t("../util/component"),l=t("../util/clazz"),h=t("../util/layout"),c=i[C]({type:"component",id:"",name:"",mainType:"",subType:"",componentIndex:0,defaultOption:null,ecModel:null,dependentModels:[],uid:null,layoutMode:null,$constructor:function(t,e,n,r){i.call(this,t,e,n,r),this.uid=a.getUID("componentModel")},init:function(t,e,i){this.mergeDefaultAndTheme(t,i)},mergeDefaultAndTheme:function(t,e){var i=this.layoutMode,r=i?h.getLayoutParams(t):{},a=e.getTheme();n.merge(t,a.get(this.mainType)),n.merge(t,this.getDefaultOption()),i&&h.mergeLayoutParam(t,r,i)},mergeOption:function(t){n.merge(this[u],t,!0);var e=this.layoutMode;e&&h.mergeLayoutParam(this[u],t,e)},optionUpdated:function(){},getDefaultOption:function(){if(!this.hasOwnProperty("__defaultOption")){for(var t=[],e=this.constructor;e;){var i=e[Z].defaultOption;i&&t.push(i),e=e.superClass}for(var r={},a=t[R]-1;a>=0;a--)r=n.merge(r,t[a],!0);this.__defaultOption=r}return this.__defaultOption},getReferringComponents:function(t){return this[s][o]({mainType:t,index:this.get(t+"Index",!0),id:this.get(t+"Id",!0)})}});return l.enableClassManagement(c,{registerWhenExtend:!0}),a.enableSubTypeDefaulter(c),a.enableTopologicalTravel(c,e),n.mixin(c,t("./mixin/boxLayout")),c}),e("echarts/model/Series",[le,se,"../util/format","../util/model","./Component","./mixin/colorPalette",H,"../util/layout"],function(t){var e=t(se),i=t("../util/format"),n=t("../util/model"),r=t("./Component"),a=t("./mixin/colorPalette"),o=t(H),l=t("../util/layout"),c=i.encodeHTML,f=i.addCommas,p=r[C]({type:"series.__base__",seriesIndex:0,coordinateSystem:null,defaultOption:null,legendDataProvider:null,visualColorAccessPath:"itemStyle.normal.color",layoutMode:null,init:function(t,e,i){this.seriesIndex=this.componentIndex,this.mergeDefaultAndTheme(t,i),this._dataBeforeProcessed=this.getInitialData(t,i),this._data=this._dataBeforeProcessed.cloneShallow()},mergeDefaultAndTheme:function(t,i){var r=this.layoutMode,a=r?l.getLayoutParams(t):{};e.merge(t,i.getTheme().get(this.subType)),e.merge(t,this.getDefaultOption()),n.defaultEmphasis(t.label,n.LABEL_OPTIONS),this.fillDataTextStyle(t.data),r&&l.mergeLayoutParam(t,a,r)},mergeOption:function(t,i){t=e.merge(this[u],t,!0),this.fillDataTextStyle(t.data);var n=this.layoutMode;n&&l.mergeLayoutParam(this[u],t,n);var r=this.getInitialData(t,i);r&&(this._data=r,this._dataBeforeProcessed=r.cloneShallow())},fillDataTextStyle:function(t){if(t)for(var e=0;e<t[R];e++)t[e]&&t[e].label&&n.defaultEmphasis(t[e].label,n.LABEL_OPTIONS)},getInitialData:function(){},getData:function(t){return null==t?this._data:this._data.getLinkedData(t)},setData:function(t){this._data=t},getRawData:function(){return this._dataBeforeProcessed},coordDimToDataDim:function(t){return[t]},dataDimToCoordDim:function(t){return t},getBaseAxis:function(){var t=this[ae];return t&&t.getBaseAxis&&t.getBaseAxis()},formatTooltip:function(t,n){function r(t){var r=[];return e.each(t,function(t,e){var o,s=a.getDimensionInfo(e),l=s&&s.type;o=l===d?t+"":"time"===l?n?"":i.formatTime("yyyy/MM/dd hh:mm:ss",t):f(t),o&&r.push(o)}),r.join(", ")}var a=this._data,o=this.getRawValue(t),s=e[_](o)?r(o):f(o),l=a.getName(t),u=a.getItemVisual(t,"color");e[P](u)&&u[I]&&(u=(u[I][0]||{}).color),u=u||"transparent";var h='<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:'+u+'"></span>',p=this.name;return"\x00-"===p&&(p=""),n?h+c(this.name)+" : "+s:(p&&c(p)+"<br />")+h+(l?c(l)+" : "+s:s)},ifEnableAnimation:function(){if(o.node)return!1;var t=this[h](N);return t&&this[D]().count()>this[h]("animationThreshold")&&(t=!1),t},restoreData:function(){this._data=this._dataBeforeProcessed.cloneShallow()},getColorFromPalette:function(t,e){var i=this[s],n=a.getColorFromPalette.call(this,t,e);return n||(n=i.getColorFromPalette(t,e)),n},getAxisTooltipDataIndex:null,getTooltipPosition:null});return e.mixin(p,n.dataFormatMixin),e.mixin(p,a),p}),e("echarts/view/Component",[le,"zrender/container/Group","../util/component","../util/clazz"],function(t){var e=t("zrender/container/Group"),i=t("../util/component"),n=t("../util/clazz"),r=function(){this.group=new e,this.uid=i.getUID("viewComponent")};r[Z]={constructor:r,init:function(){},render:function(){},dispose:function(){}};
var a=r[Z];return a.updateView=a.updateLayout=a.updateVisual=function(){},n.enableClassExtend(r),n.enableClassManagement(r,{registerWhenExtend:!0}),r}),e("echarts/view/Chart",[le,"zrender/container/Group","../util/component","../util/clazz","../util/model",se],function(t){function e(){this.group=new r,this.uid=a.getUID("viewChart")}function i(t,e){if(t&&(t.trigger(e),"group"===t.type))for(var n=0;n<t.childCount();n++)i(t.childAt(n),e)}function n(t,e,n){var r=s.queryDataIndex(t,e);null!=r?l.each(s.normalizeToArray(r),function(e){i(t.getItemGraphicEl(e),n)}):t.eachItemGraphicEl(function(t){i(t,n)})}var r=t("zrender/container/Group"),a=t("../util/component"),o=t("../util/clazz"),s=t("../util/model"),l=t(se);e[Z]={type:"chart",init:function(){},render:function(){},highlight:function(t,e,i,r){n(t[D](),r,"emphasis")},downplay:function(t,e,i,r){n(t[D](),r,"normal")},remove:function(){this.group[oe]()},dispose:function(){}};var u=e[Z];return u.updateView=u.updateLayout=u.updateVisual=function(t,e,i,n){this.render(t,e,i,n)},o.enableClassExtend(e,[M]),o.enableClassManagement(e,{registerWhenExtend:!0}),e}),e("echarts/util/model",[le,"./format","./number","../model/Model",se],function(t){function e(t,e){return t&&t.hasOwnProperty(e)}var i=t("./format"),n=t("./number"),r=t("../model/Model"),s=t(se),l=s.each,h=s[P],f={};return f.normalizeToArray=function(t){return t instanceof Array?t:null==t?[]:[t]},f.defaultEmphasis=function(t,e){if(t){var i=t.emphasis=t.emphasis||{},n=t.normal=t.normal||{};l(e,function(t){var e=s[a](i[t],n[t]);null!=e&&(i[t]=e)})}},f.LABEL_OPTIONS=[W,"show",ie,"distance","formatter"],f.getDataItemValue=function(t){return t&&(null==t.value?t:t.value)},f.isDataItemOption=function(t){return h(t)&&!(t instanceof Array)},f.converDataValue=function(t,e){var i=e&&e.type;return i===d?t:("time"!==i||isFinite(t)||null==t||"-"===t||(t=+n.parseDate(t)),null==t||""===t?0/0:+t)},f.createDataFormatModel=function(t,e){var i=new r;return s.mixin(i,f.dataFormatMixin),i.seriesIndex=e.seriesIndex,i.name=e.name||"",i.mainType=e.mainType,i.subType=e.subType,i[D]=function(){return t},i},f.dataFormatMixin={getDataParams:function(t,e){var i=this[D](e),n=this.seriesIndex,r=this.name,a=this.getRawValue(t,e),o=i.getRawIndex(t),s=i.getName(t,!0),l=i.getRawDataItem(t);return{componentType:this.mainType,componentSubType:this.subType,seriesType:this.mainType===A?this.subType:null,seriesIndex:n,seriesName:r,name:s,dataIndex:o,data:l,dataType:e,value:a,color:i.getItemVisual(t,"color"),$vars:["seriesName","name","value"]}},getFormattedLabel:function(t,e,n,r){e=e||"normal";var a=this[D](n),o=a[c](t),s=this.getDataParams(t,n);null!=r&&s.value instanceof Array&&(s.value=s.value[r]);var l=o.get(["label",e,"formatter"]);return typeof l===y?(s.status=e,l(s)):typeof l===G?i.formatTpl(l,s):void 0},getRawValue:function(t,e){var i=this[D](e),n=i.getRawDataItem(t);return null!=n?!h(n)||n instanceof Array?n:n.value:void 0},formatTooltip:s.noop},f.mappingToExists=function(t,e){e=(e||[]).slice();var i=s.map(t||[],function(t){return{exist:t}});return l(e,function(t,n){if(h(t)){for(var r=0;r<i[R];r++)if(!i[r][u]&&null!=t.id&&i[r].exist.id===t.id+"")return i[r][u]=t,void(e[n]=null);for(var r=0;r<i[R];r++){var a=i[r].exist;if(!(i[r][u]||null!=a.id&&null!=t.id||null==t.name||f.isIdInner(t)||f.isIdInner(a)||a.name!==t.name+""))return i[r][u]=t,void(e[n]=null)}}}),l(e,function(t){if(h(t)){for(var e=0;e<i[R];e++){var n=i[e].exist;if(!i[e][u]&&!f.isIdInner(n)&&null==t.id){i[e][u]=t;break}}e>=i[R]&&i.push({option:t})}}),i},f.makeIdAndName=function(t){var e={};l(t,function(t){var i=t.exist;i&&(e[i.id]=t)}),l(t,function(t){var i=t[u];s.assert(!i||null==i.id||!e[i.id]||e[i.id]===t,"id duplicates: "+(i&&i.id)),i&&null!=i.id&&(e[i.id]=t),!t.keyInfo&&(t.keyInfo={})}),l(t,function(t){var i=t.exist,n=t[u],r=t.keyInfo;if(h(n)){if(r.name=null!=n.name?n.name+"":i?i.name:"\x00-",i)r.id=i.id;else if(null!=n.id)r.id=n.id+"";else{var a=0;do r.id="\x00"+r.name+"\x00"+a++;while(e[r.id])}e[r.id]=t}})},f.isIdInner=function(t){return h(t)&&t.id&&0===(t.id+"")[O]("\x00_ec_\x00")},f.compressBatches=function(t,e){function i(t,e,i){for(var n=0,r=t[R];r>n;n++)for(var a=t[n].seriesId,o=f.normalizeToArray(t[n][L]),s=i&&i[a],l=0,u=o[R];u>l;l++){var h=o[l];s&&s[h]?s[h]=null:(e[a]||(e[a]={}))[h]=1}}function n(t,e){var i=[];for(var r in t)if(t.hasOwnProperty(r)&&null!=t[r])if(e)i.push(+r);else{var a=n(t[r],!0);a[R]&&i.push({seriesId:r,dataIndex:a})}return i}var r={},a={};return i(t||[],r),i(e||[],a,r),[n(r),n(a)]},f.queryDataIndex=function(t,e){return null!=e.dataIndexInside?e.dataIndexInside:null!=e[L]?s[_](e[L])?s.map(e[L],function(e){return t.indexOfRawIndex(e)}):t.indexOfRawIndex(e[L]):null!=e.name?s[_](e.name)?s.map(e.name,function(e){return t.indexOfName(e)}):t.indexOfName(e.name):void 0},f.parseFinder=function(t,i,n){if(s[g](i)){var r={};r[i+"Index"]=0,i=r}var a=n&&n.defaultMainType;!a||e(i,a+"Index")||e(i,a+"Id")||e(i,a+"Name")||(i[a+"Index"]=0);var u={};return l(i,function(e,n){var e=i[n];if(n===L||"dataIndexInside"===n)return void(u[n]=e);var r=n.match(/^(\w+)(Index|Id|Name)$/)||[],a=r[1],s=r[2];if(a&&s){var l={mainType:a};l[s[F]()]=e;var h=t[o](l);u[a+"Models"]=h,u[a+"Model"]=h[0]}}),u},f}),e("echarts/util/throttle",[],function(){var t={},e="\x00__throttleOriginMethod",i="\x00__throttleRate",n="\x00__throttleType";return t.throttle=function(t,e,i){function n(){u=(new Date).getTime(),h=null,t.apply(o,s||[])}var r,a,o,s,l=0,u=0,h=null;e=e||0;var c=function(){r=(new Date).getTime(),o=this,s=arguments,a=r-(i?l:u)-e,clearTimeout(h),i?h=setTimeout(n,e):a>=0?n():h=setTimeout(n,-a),l=r};return c.clear=function(){h&&(clearTimeout(h),h=null)},c},t.createOrUpdate=function(r,a,o,s){var l=r[a];if(l){var u=l[e]||l,h=l[n],c=l[i];if(c!==o||h!==s){if(null==o||!s)return r[a]=u;l=r[a]=t.throttle(u,o,"debounce"===s),l[e]=u,l[n]=s,l[i]=o}return l}},t.clear=function(t,i){var n=t[i];n&&n[e]&&(t[i]=n[e])},t}),e("zrender/zrender",[le,"./core/guid","./core/env","./core/util","./Handler","./Storage","./animation/Animation","./dom/HandlerProxy","./Painter"],function(t){function e(t){delete c[t]}var i=t("./core/guid"),n=t("./core/env"),r=t("./core/util"),a=t("./Handler"),o=t("./Storage"),s=t("./animation/Animation"),l=t("./dom/HandlerProxy"),u=!n[B],h={canvas:t("./Painter")},c={},d={};d.version="3.2.2",d.init=function(t,e){var n=new f(i(),t,e);return c[n.id]=n,n},d[M]=function(t){if(t)t[M]();else{for(var e in c)c.hasOwnProperty(e)&&c[e][M]();c={}}return d},d.getInstance=function(t){return c[t]},d.registerPainter=function(t,e){h[t]=e};var f=function(t,e,i){i=i||{},this.dom=e,this.id=t;var c=this,d=new o,f=i.renderer;if(u){if(!h.vml)throw new Error("You need to require 'zrender/vml/vml' to support IE8");f="vml"}else f&&h[f]||(f="canvas");var p=new h[f](e,d,i);this.storage=d,this.painter=p;var m=n.node?null:new l(p.getViewportRoot());this.handler=new a(d,p,m,p.root),this[N]=new s({stage:{update:r.bind(this.flush,this)}}),this[N].start(),this._needsRefresh;var v=d.delFromMap,g=d.addToMap;d.delFromMap=function(t){var e=d.get(t);v.call(d,t),e&&e.removeSelfFromZr(c)},d.addToMap=function(t){g.call(d,t),t.addSelfToZr(c)}};return f[Z]={constructor:f,getId:function(){return this.id},add:function(t){this.storage.addRoot(t),this._needsRefresh=!0},remove:function(t){this.storage.delRoot(t),this._needsRefresh=!0},configLayer:function(t,e){this.painter.configLayer(t,e),this._needsRefresh=!0},refreshImmediately:function(){this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1},refresh:function(){this._needsRefresh=!0},flush:function(){this._needsRefresh&&this.refreshImmediately(),this._needsRefreshHover&&this.refreshHoverImmediately()},addHover:function(t,e){this.painter.addHover&&(this.painter.addHover(t,e),this.refreshHover())},removeHover:function(t){this.painter.removeHover&&(this.painter.removeHover(t),this.refreshHover())},clearHover:function(){this.painter.clearHover&&(this.painter.clearHover(),this.refreshHover())},refreshHover:function(){this._needsRefreshHover=!0},refreshHoverImmediately:function(){this._needsRefreshHover=!1,this.painter.refreshHover&&this.painter.refreshHover()},resize:function(t){t=t||{},this.painter.resize(t.width,t[K]),this.handler.resize()},clearAnimation:function(){this[N].clear()},getWidth:function(){return this.painter[$]()},getHeight:function(){return this.painter[Y]()},pathToImage:function(t,e,n){var r=i();return this.painter.pathToImage(r,t,e,n)},setCursorStyle:function(t){this.handler.setCursorStyle(t)},on:function(t,e,i){this.handler.on(t,e,i)},off:function(t,e){this.handler.off(t,e)},trigger:function(t,e){this.handler.trigger(t,e)},clear:function(){this.storage.delRoot(),this.painter.clear()},dispose:function(){this[N].stop(),this.clear(),this.storage[M](),this.painter[M](),this.handler[M](),this[N]=this.storage=this.painter=this.handler=null,e(this.id)}},d}),e("zrender/tool/color",[le],function(){function t(t){return t=Math.round(t),0>t?0:t>255?255:t}function e(t){return t=Math.round(t),0>t?0:t>360?360:t}function i(t){return 0>t?0:t>1?1:t}function n(e){return t(e[R]&&"%"===e.charAt(e[R]-1)?parseFloat(e)/100*255:parseInt(e,10))}function r(t){return i(t[R]&&"%"===t.charAt(t[R]-1)?parseFloat(t)/100:parseFloat(t))}function a(t,e,i){return 0>i?i+=1:i>1&&(i-=1),1>6*i?t+(e-t)*i*6:1>2*i?e:2>3*i?t+(e-t)*(2/3-i)*6:t}function o(t,e,i){return t+(e-t)*i}function s(t){if(t){t+="";var e=t[x](/ /g,"")[F]();if(e in g)return g[e].slice();if("#"!==e.charAt(0)){var i=e[O]("("),a=e[O](")");if(-1!==i&&a+1===e[R]){var o=e.substr(0,i),s=e.substr(i+1,a-(i+1)).split(","),u=1;switch(o){case"rgba":if(4!==s[R])return;u=r(s.pop());case"rgb":if(3!==s[R])return;return[n(s[0]),n(s[1]),n(s[2]),u];case"hsla":if(4!==s[R])return;return s[3]=r(s[3]),l(s);case"hsl":if(3!==s[R])return;return l(s);default:return}}}else{if(4===e[R]){var h=parseInt(e.substr(1),16);if(!(h>=0&&4095>=h))return;return[(3840&h)>>4|(3840&h)>>8,240&h|(240&h)>>4,15&h|(15&h)<<4,1]}if(7===e[R]){var h=parseInt(e.substr(1),16);if(!(h>=0&&16777215>=h))return;return[(16711680&h)>>16,(65280&h)>>8,255&h,1]}}}}function l(e){var i=(parseFloat(e[0])%360+360)%360/360,n=r(e[1]),o=r(e[2]),s=.5>=o?o*(n+1):o+n-o*n,l=2*o-s,u=[t(255*a(l,s,i+1/3)),t(255*a(l,s,i)),t(255*a(l,s,i-1/3))];return 4===e[R]&&(u[3]=e[3]),u}function u(t){if(t){var e,i,n=t[0]/255,r=t[1]/255,a=t[2]/255,o=Math.min(n,r,a),s=Math.max(n,r,a),l=s-o,u=(s+o)/2;if(0===l)e=0,i=0;else{i=.5>u?l/(s+o):l/(2-s-o);var h=((s-n)/6+l/2)/l,c=((s-r)/6+l/2)/l,d=((s-a)/6+l/2)/l;n===s?e=d-c:r===s?e=1/3+h-d:a===s&&(e=2/3+c-h),0>e&&(e+=1),e>1&&(e-=1)}var f=[360*e,i,u];return null!=t[3]&&f.push(t[3]),f}}function h(t,e){var i=s(t);if(i){for(var n=0;3>n;n++)i[n]=0>e?i[n]*(1-e)|0:(255-i[n])*e+i[n]|0;return v(i,4===i[R]?"rgba":"rgb")}}function c(t){var e=s(t);return e?((1<<24)+(e[0]<<16)+(e[1]<<8)+ +e[2]).toString(16).slice(1):void 0}function d(e,i,n){if(i&&i[R]&&e>=0&&1>=e){n=n||[0,0,0,0];var r=e*(i[R]-1),a=Math.floor(r),s=Math.ceil(r),l=i[a],u=i[s],h=r-a;return n[0]=t(o(l[0],u[0],h)),n[1]=t(o(l[1],u[1],h)),n[2]=t(o(l[2],u[2],h)),n[3]=t(o(l[3],u[3],h)),n}}function f(e,n,r){if(n&&n[R]&&e>=0&&1>=e){var a=e*(n[R]-1),l=Math.floor(a),u=Math.ceil(a),h=s(n[l]),c=s(n[u]),d=a-l,f=v([t(o(h[0],c[0],d)),t(o(h[1],c[1],d)),t(o(h[2],c[2],d)),i(o(h[3],c[3],d))],"rgba");return r?{color:f,leftIndex:l,rightIndex:u,value:a}:f}}function p(t,i,n,a){return t=s(t),t?(t=u(t),null!=i&&(t[0]=e(i)),null!=n&&(t[1]=r(n)),null!=a&&(t[2]=r(a)),v(l(t),"rgba")):void 0}function m(t,e){return t=s(t),t&&null!=e?(t[3]=i(e),v(t,"rgba")):void 0}function v(t,e){var i=t[0]+","+t[1]+","+t[2];return("rgba"===e||"hsva"===e||"hsla"===e)&&(i+=","+t[3]),e+"("+i+")"}var g={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};return{parse:s,lift:h,toHex:c,fastMapToColor:d,mapToColor:f,modifyHSL:p,modifyAlpha:m,stringify:v}}),e("zrender/mixin/Eventful",[le],function(){var t=Array[Z].slice,e=function(){this._$handlers={}};return e[Z]={constructor:e,one:function(t,e,i){var n=this._$handlers;if(!e||!t)return this;n[t]||(n[t]=[]);for(var r=0;r<n[t][R];r++)if(n[t][r].h===e)return this;return n[t].push({h:e,one:!0,ctx:i||this}),this},on:function(t,e,i){var n=this._$handlers;if(!e||!t)return this;n[t]||(n[t]=[]);for(var r=0;r<n[t][R];r++)if(n[t][r].h===e)return this;return n[t].push({h:e,one:!1,ctx:i||this}),this},isSilent:function(t){var e=this._$handlers;return e[t]&&e[t][R]},off:function(t,e){var i=this._$handlers;if(!t)return this._$handlers={},this;if(e){if(i[t]){for(var n=[],r=0,a=i[t][R];a>r;r++)i[t][r].h!=e&&n.push(i[t][r]);i[t]=n}i[t]&&0===i[t][R]&&delete i[t]}else delete i[t];return this},trigger:function(e){if(this._$handlers[e]){var i=arguments,n=i[R];n>3&&(i=t.call(i,1));for(var r=this._$handlers[e],a=r[R],o=0;a>o;){switch(n){case 1:r[o].h.call(r[o].ctx);break;case 2:r[o].h.call(r[o].ctx,i[1]);break;case 3:r[o].h.call(r[o].ctx,i[1],i[2]);break;default:r[o].h.apply(r[o].ctx,i)}r[o].one?(r[T](o,1),a--):o++}}return this},triggerWithContext:function(e){if(this._$handlers[e]){var i=arguments,n=i[R];n>4&&(i=t.call(i,1,i[R]-1));for(var r=i[i[R]-1],a=this._$handlers[e],o=a[R],s=0;o>s;){switch(n){case 1:a[s].h.call(r);break;case 2:a[s].h.call(r,i[1]);break;case 3:a[s].h.call(r,i[1],i[2]);break;default:a[s].h.apply(r,i)}a[s].one?(a[T](s,1),o--):s++}}return this}},e}),e("zrender/core/timsort",[],function(){function t(t){for(var e=0;t>=l;)e|=1&t,t>>=1;return t+e}function e(t,e,n,r){var a=e+1;if(a===n)return 1;if(r(t[a++],t[e])<0){for(;n>a&&r(t[a],t[a-1])<0;)a++;i(t,e,a)}else for(;n>a&&r(t[a],t[a-1])>=0;)a++;return a-e}function i(t,e,i){for(i--;i>e;){var n=t[e];t[e++]=t[i],t[i--]=n}}function n(t,e,i,n,r){for(n===e&&n++;i>n;n++){for(var a,o=t[n],s=e,l=n;l>s;)a=s+l>>>1,r(o,t[a])<0?l=a:s=a+1;var u=n-s;switch(u){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:for(;u>0;)t[s+u]=t[s+u-1],u--}t[s]=o}}function r(t,e,i,n,r,a){var o=0,s=0,l=1;if(a(t,e[i+r])>0){for(s=n-r;s>l&&a(t,e[i+r+l])>0;)o=l,l=(l<<1)+1,0>=l&&(l=s);l>s&&(l=s),o+=r,l+=r}else{for(s=r+1;s>l&&a(t,e[i+r-l])<=0;)o=l,l=(l<<1)+1,0>=l&&(l=s);l>s&&(l=s);var u=o;o=r-l,l=r-u}for(o++;l>o;){var h=o+(l-o>>>1);a(t,e[i+h])>0?o=h+1:l=h}return l}function a(t,e,i,n,r,a){var o=0,s=0,l=1;if(a(t,e[i+r])<0){for(s=r+1;s>l&&a(t,e[i+r-l])<0;)o=l,l=(l<<1)+1,0>=l&&(l=s);l>s&&(l=s);var u=o;o=r-l,l=r-u}else{for(s=n-r;s>l&&a(t,e[i+r+l])>=0;)o=l,l=(l<<1)+1,0>=l&&(l=s);l>s&&(l=s),o+=r,l+=r}for(o++;l>o;){var h=o+(l-o>>>1);a(t,e[i+h])<0?l=h:o=h+1}return l}function o(t,e){function i(t,e){d[y]=t,f[y]=e,y+=1}function n(){for(;y>1;){var t=y-2;if(t>=1&&f[t-1]<=f[t]+f[t+1]||t>=2&&f[t-2]<=f[t]+f[t-1])f[t-1]<f[t+1]&&t--;else if(f[t]>f[t+1])break;s(t)}}function o(){for(;y>1;){var t=y-2;t>0&&f[t-1]<f[t+1]&&t--,s(t)}}function s(i){var n=d[i],o=f[i],s=d[i+1],u=f[i+1];f[i]=o+u,i===y-3&&(d[i+1]=d[i+2],f[i+1]=f[i+2]),y--;var h=a(t[s],t,n,o,0,e);n+=h,o-=h,0!==o&&(u=r(t[n+o-1],t,s,u,u-1,e),0!==u&&(u>=o?l(n,o,s,u):c(n,o,s,u)))}function l(i,n,o,s){var l=0;for(l=0;n>l;l++)_[l]=t[i+l];var h=0,c=o,d=i;if(t[d++]=t[c++],0!==--s){if(1===n){for(l=0;s>l;l++)t[d+l]=t[c+l];return void(t[d+s]=_[h])}for(var f,m,v,g=p;;){f=0,m=0,v=!1;do if(e(t[c],_[h])<0){if(t[d++]=t[c++],m++,f=0,0===--s){v=!0;break}}else if(t[d++]=_[h++],f++,m=0,1===--n){v=!0;break}while(g>(f|m));if(v)break;do{if(f=a(t[c],_,h,n,0,e),0!==f){for(l=0;f>l;l++)t[d+l]=_[h+l];if(d+=f,h+=f,n-=f,1>=n){v=!0;break}}if(t[d++]=t[c++],0===--s){v=!0;break}if(m=r(_[h],t,c,s,0,e),0!==m){for(l=0;m>l;l++)t[d+l]=t[c+l];if(d+=m,c+=m,s-=m,0===s){v=!0;break}}if(t[d++]=_[h++],1===--n){v=!0;break}g--}while(f>=u||m>=u);if(v)break;0>g&&(g=0),g+=2}if(p=g,1>p&&(p=1),1===n){for(l=0;s>l;l++)t[d+l]=t[c+l];t[d+s]=_[h]}else{if(0===n)throw new Error;for(l=0;n>l;l++)t[d+l]=_[h+l]}}else for(l=0;n>l;l++)t[d+l]=_[h+l]}function c(i,n,o,s){var l=0;for(l=0;s>l;l++)_[l]=t[o+l];var h=i+n-1,c=s-1,d=o+s-1,f=0,m=0;if(t[d--]=t[h--],0!==--n){if(1===s){for(d-=n,h-=n,m=d+1,f=h+1,l=n-1;l>=0;l--)t[m+l]=t[f+l];return void(t[d]=_[c])}for(var v=p;;){var g=0,y=0,x=!1;do if(e(_[c],t[h])<0){if(t[d--]=t[h--],g++,y=0,0===--n){x=!0;break}}else if(t[d--]=_[c--],y++,g=0,1===--s){x=!0;break}while(v>(g|y));if(x)break;do{if(g=n-a(_[c],t,i,n,n-1,e),0!==g){for(d-=g,h-=g,n-=g,m=d+1,f=h+1,l=g-1;l>=0;l--)t[m+l]=t[f+l];if(0===n){x=!0;break}}if(t[d--]=_[c--],1===--s){x=!0;break}if(y=s-r(t[h],_,0,s,s-1,e),0!==y){for(d-=y,c-=y,s-=y,m=d+1,f=c+1,l=0;y>l;l++)t[m+l]=_[f+l];if(1>=s){x=!0;break}}if(t[d--]=t[h--],0===--n){x=!0;break}v--}while(g>=u||y>=u);if(x)break;0>v&&(v=0),v+=2}if(p=v,1>p&&(p=1),1===s){for(d-=n,h-=n,m=d+1,f=h+1,l=n-1;l>=0;l--)t[m+l]=t[f+l];t[d]=_[c]}else{if(0===s)throw new Error;for(f=d-(s-1),l=0;s>l;l++)t[f+l]=_[l]}}else for(f=d-(s-1),l=0;s>l;l++)t[f+l]=_[l]}var d,f,p=u,m=0,v=h,g=0,y=0;m=t[R],2*h>m&&(v=m>>>1);var _=[];g=120>m?5:1542>m?10:119151>m?19:40,d=[],f=[],this.mergeRuns=n,this.forceMergeRuns=o,this.pushRun=i}function s(i,r,a,s){a||(a=0),s||(s=i[R]);var u=s-a;if(!(2>u)){var h=0;if(l>u)return h=e(i,a,s,r),void n(i,a,s,a+h,r);var c=new o(i,r),d=t(u);do{if(h=e(i,a,s,r),d>h){var f=u;f>d&&(f=d),n(i,a,a+f,a+h,r),h=f}c.pushRun(a,h),c.mergeRuns(),u-=h,a+=h}while(0!==u);c.forceMergeRuns()}}var l=32,u=7,h=256;return s}),e("echarts/visual/seriesColor",[le,"zrender/graphic/Gradient"],function(t){var e=t("zrender/graphic/Gradient");return function(t){function i(i){var n=(i.visualColorAccessPath||"itemStyle.normal.color").split("."),r=i[D](),a=i.get(n)||i.getColorFromPalette(i.get("name"));r.setVisual("color",a),t.isSeriesFiltered(i)||(typeof a!==y||a instanceof e||r.each(function(t){r.setItemVisual(t,"color",a(i.getDataParams(t)))}),r.each(function(t){var e=r[c](t),i=e.get(n,!0);null!=i&&r.setItemVisual(t,"color",i)}))}t.eachRawSeries(i)}}),e("echarts/preprocessor/backwardCompat",[le,se,"./helper/compatStyle"],function(t){function e(t,e){e=e.split(",");for(var i=t,n=0;n<e[R]&&(i=i&&i[e[n]],null!=i);n++);return i}function i(t,e,i,n){e=e.split(",");for(var r,a=t,o=0;o<e[R]-1;o++)r=e[o],null==a[r]&&(a[r]={}),a=a[r];(n||null==a[e[o]])&&(a[e[o]]=i)}function n(t){u(o,function(e){e[0]in t&&!(e[1]in t)&&(t[e[1]]=t[e[0]])})}var r=t(se),a=t("./helper/compatStyle"),o=[["x","left"],["y","top"],["x2","right"],["y2",U]],s=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"],l=["bar","boxplot","candlestick","chord","effectScatter","funnel","gauge","lines","graph","heatmap","line","map","parallel","pie","radar","sankey","scatter","treemap"],u=r.each;return function(t){u(t[A],function(t){if(r[P](t)){var o=t.type;if(a(t),("pie"===o||"gauge"===o)&&null!=t.clockWise&&(t.clockwise=t.clockWise),"gauge"===o){var s=e(t,"pointer.color");null!=s&&i(t,"itemStyle.normal.color",s)}for(var u=0;u<l[R];u++)if(l[u]===t.type){n(t);break}}}),t.dataRange&&(t.visualMap=t.dataRange),u(s,function(e){var i=t[e];i&&(r[_](i)||(i=[i]),u(i,function(t){n(t)}))})}}),e("echarts/loading/default",[le,"../util/graphic",se],function(t){var e=t("../util/graphic"),i=t(se),n=Math.PI;return function(t,r){r=r||{},i[re](r,{text:"loading",color:"#c23531",textColor:"#000",maskColor:"rgba(255, 255, 255, 0.8)",zlevel:0});var a=new e.Rect({style:{fill:r.maskColor},zlevel:r[b],z:1e4}),o=new e.Arc({shape:{startAngle:-n/2,endAngle:-n/2+.1,r:10},style:{stroke:r.color,lineCap:"round",lineWidth:5},zlevel:r[b],z:10001}),s=new e.Rect({style:{fill:"none",text:r.text,textPosition:"right",textDistance:10,textFill:r.textColor},zlevel:r[b],z:10001});o.animateShape(!0).when(1e3,{endAngle:3*n/2}).start("circularInOut"),o.animateShape(!0).when(1e3,{startAngle:3*n/2}).delay(300).start("circularInOut");var l=new e.Group;return l.add(o),l.add(s),l.add(a),l.resize=function(){var e=t[$]()/2,i=t[Y]()/2;o.setShape({cx:e,cy:i});var n=o.shape.r;s.setShape({x:e-n,y:i-n,width:2*n,height:2*n}),a.setShape({x:0,y:0,width:t[$](),height:t[Y]()})},l.resize(),l}}),e("echarts/model/Model",[le,se,"../util/clazz","./mixin/lineStyle","./mixin/areaStyle","./mixin/textStyle","./mixin/itemStyle"],function(t){function e(t,e,i){this.parentModel=e,this[s]=i,this[u]=t}var i=t(se),n=t("../util/clazz");e[Z]={constructor:e,init:null,mergeOption:function(t){i.merge(this[u],t,!0)},get:function(t,e){if(!t)return this[u];typeof t===G&&(t=t.split("."));for(var i=this[u],n=this.parentModel,r=0;r<t[R]&&(!t[r]||(i=i&&"object"==typeof i?i[t[r]]:null,null!=i));r++);return null==i&&n&&!e&&(i=n.get(t)),i},getShallow:function(t,e){var i=this[u],n=null==i?i:i[t],r=this.parentModel;return null==n&&r&&!e&&(n=r[h](t)),n},getModel:function(t,i){var n=this.get(t,!0),r=this.parentModel,a=new e(n,i||r&&r[ne](t),this[s]);return a},isEmpty:function(){return null==this[u]},restoreData:function(){},clone:function(){var t=this.constructor;return new t(i.clone(this[u]))},setReadOnly:function(t){n.setReadOnly(this,t)}},n.enableClassExtend(e);var r=i.mixin;return r(e,t("./mixin/lineStyle")),r(e,t("./mixin/areaStyle")),r(e,t("./mixin/textStyle")),r(e,t("./mixin/itemStyle")),e}),e("echarts/util/number",[le],function(){function t(t){return t[x](/^\s+/,"")[x](/\s+$/,"")}var e={},i=1e-4;return e.linearMap=function(t,e,i,n){var r=e[1]-e[0],a=i[1]-i[0];if(0===r)return 0===a?i[0]:(i[0]+i[1])/2;if(n)if(r>0){if(t<=e[0])return i[0];if(t>=e[1])return i[1]}else{if(t>=e[0])return i[0];if(t<=e[1])return i[1]}else{if(t===e[0])return i[0];if(t===e[1])return i[1]}return(t-e[0])/r*a+i[0]},e.parsePercent=function(e,i){switch(e){case X:case j:e="50%";break;case"left":case"top":e="0%";break;case"right":case U:e="100%"}return typeof e===G?t(e).match(/%$/)?parseFloat(e)/100*i:parseFloat(e):null==e?0/0:+e},e.round=function(t,e){return null==e&&(e=10),e=Math.min(Math.max(0,e),20),+(+t).toFixed(e)},e.asc=function(t){return t.sort(function(t,e){return t-e}),t},e.getPrecision=function(t){if(t=+t,isNaN(t))return 0;for(var e=1,i=0;Math.round(t*e)/e!==t;)e*=10,i++;return i},e.getPrecisionSafe=function(t){var e=t.toString(),i=e[O](".");return 0>i?0:e[R]-1-i},e.getPixelPrecision=function(t,e){var i=Math.log,n=Math.LN10,r=Math.floor(i(t[1]-t[0])/n),a=Math.round(i(Math.abs(e[1]-e[0]))/n);return Math.max(-r+a,0)},e.MAX_SAFE_INTEGER=9007199254740991,e.remRadian=function(t){var e=2*Math.PI;return(t%e+e)%e},e.isRadianAroundZero=function(t){return t>-i&&i>t},e.parseDate=function(t){if(t instanceof Date)return t;if(typeof t===G){var e=new Date(t);return isNaN(+e)&&(e=new Date(new Date(t[x](/-/g,"/"))-new Date("1970/01/01"))),e}return new Date(Math.round(t))},e.quantity=function(t){return Math.pow(10,Math.floor(Math.log(t)/Math.LN10))},e.nice=function(t,i){var n,r=e.quantity(t),a=t/r;return n=i?1.5>a?1:2.5>a?2:4>a?3:7>a?5:10:1>a?1:2>a?2:3>a?3:5>a?5:10,n*r},e.reformIntervals=function(t){function e(t,i,n){return t.interval[n]<i.interval[n]||t.interval[n]===i.interval[n]&&(t.close[n]-i.close[n]===(n?-1:1)||!n&&e(t,i,1))}t.sort(function(t,i){return e(t,i,0)?-1:1});for(var i=-1/0,n=1,r=0;r<t[R];){for(var a=t[r].interval,o=t[r].close,s=0;2>s;s++)a[s]<=i&&(a[s]=i,o[s]=s?1:1-n),i=a[s],n=o[s];a[0]===a[1]&&o[0]*o[1]!==1?t[T](r,1):r++}return t},e}),e("echarts/util/format",[le,se,"./number","zrender/contain/text"],function(t){var e=t(se),i=t("./number"),n=t("zrender/contain/text"),r={};r.addCommas=function(t){return isNaN(t)?"-":(t=(t+"").split("."),t[0][x](/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(t[R]>1?"."+t[1]:""))},r.toCamelCase=function(t,e){return t=(t||"")[F]()[x](/-(.)/g,function(t,e){return e.toUpperCase()}),e&&t&&(t=t.charAt(0).toUpperCase()+t.slice(1)),t},r.normalizeCssArray=function(t){var e=t[R];return"number"==typeof t?[t,t,t,t]:2===e?[t[0],t[1],t[0],t[1]]:3===e?[t[0],t[1],t[2],t[1]]:t},r.encodeHTML=function(t){return String(t)[x](/&/g,"&amp;")[x](/</g,"&lt;")[x](/>/g,"&gt;")[x](/"/g,"&quot;")[x](/'/g,"&#39;")};var a=["a","b","c","d","e","f","g"],o=function(t,e){return"{"+t+(null==e?"":e)+"}"};r.formatTpl=function(t,i){e[_](i)||(i=[i]);var n=i[R];if(!n)return"";for(var r=i[0].$vars||[],s=0;s<r[R];s++){var l=a[s];t=t[x](o(l),o(l,0))}for(var u=0;n>u;u++)for(var h=0;h<r[R];h++)t=t[x](o(a[h],u),i[u][r[h]]);return t};var s=function(t){return 10>t?"0"+t:t};return r.formatTime=function(t,e){("week"===t||"month"===t||"quarter"===t||"half-year"===t||"year"===t)&&(t="MM-dd\nyyyy");var n=i.parseDate(e),r=n.getFullYear(),a=n.getMonth()+1,o=n.getDate(),l=n.getHours(),u=n.getMinutes(),h=n.getSeconds();return t=t[x]("MM",s(a))[F]()[x]("yyyy",r)[x]("yy",r%100)[x]("dd",s(o))[x]("d",o)[x]("hh",s(l))[x]("h",l)[x]("mm",s(u))[x]("m",u)[x]("ss",s(h))[x]("s",h)},r.capitalFirst=function(t){return t?t.charAt(0).toUpperCase()+t.substr(1):t},r.truncateText=n.truncateText,r}),e("zrender/core/matrix",[],function(){var t=typeof Float32Array===i?Array:Float32Array,e={create:function(){var i=new t(6);return e.identity(i),i},identity:function(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t},copy:function(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t},mul:function(t,e,i){var n=e[0]*i[0]+e[2]*i[1],r=e[1]*i[0]+e[3]*i[1],a=e[0]*i[2]+e[2]*i[3],o=e[1]*i[2]+e[3]*i[3],s=e[0]*i[4]+e[2]*i[5]+e[4],l=e[1]*i[4]+e[3]*i[5]+e[5];return t[0]=n,t[1]=r,t[2]=a,t[3]=o,t[4]=s,t[5]=l,t},translate:function(t,e,i){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+i[0],t[5]=e[5]+i[1],t},rotate:function(t,e,i){var n=e[0],r=e[2],a=e[4],o=e[1],s=e[3],l=e[5],u=Math.sin(i),h=Math.cos(i);return t[0]=n*h+o*u,t[1]=-n*u+o*h,t[2]=r*h+s*u,t[3]=-r*u+h*s,t[4]=h*a+u*l,t[5]=h*l-u*a,t},scale:function(t,e,i){var n=i[0],r=i[1];return t[0]=e[0]*n,t[1]=e[1]*r,t[2]=e[2]*n,t[3]=e[3]*r,t[4]=e[4]*n,t[5]=e[5]*r,t},invert:function(t,e){var i=e[0],n=e[2],r=e[4],a=e[1],o=e[3],s=e[5],l=i*o-a*n;return l?(l=1/l,t[0]=o*l,t[1]=-a*l,t[2]=-n*l,t[3]=i*l,t[4]=(n*s-o*r)*l,t[5]=(a*r-i*s)*l,t):null}};return e}),e(V,[],function(){var t=typeof Float32Array===i?Array:Float32Array,e={create:function(e,i){var n=new t(2);return null==e&&(e=0),null==i&&(i=0),n[0]=e,n[1]=i,n},copy:function(t,e){return t[0]=e[0],t[1]=e[1],t},clone:function(e){var i=new t(2);return i[0]=e[0],i[1]=e[1],i},set:function(t,e,i){return t[0]=e,t[1]=i,t},add:function(t,e,i){return t[0]=e[0]+i[0],t[1]=e[1]+i[1],t},scaleAndAdd:function(t,e,i,n){return t[0]=e[0]+i[0]*n,t[1]=e[1]+i[1]*n,t},sub:function(t,e,i){return t[0]=e[0]-i[0],t[1]=e[1]-i[1],t},len:function(t){return Math.sqrt(this.lenSquare(t))},lenSquare:function(t){return t[0]*t[0]+t[1]*t[1]},mul:function(t,e,i){return t[0]=e[0]*i[0],t[1]=e[1]*i[1],t},div:function(t,e,i){return t[0]=e[0]/i[0],t[1]=e[1]/i[1],t},dot:function(t,e){return t[0]*e[0]+t[1]*e[1]},scale:function(t,e,i){return t[0]=e[0]*i,t[1]=e[1]*i,t},normalize:function(t,i){var n=e.len(i);return 0===n?(t[0]=0,t[1]=0):(t[0]=i[0]/n,t[1]=i[1]/n),t},distance:function(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))},distanceSquare:function(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])},negate:function(t,e){return t[0]=-e[0],t[1]=-e[1],t},lerp:function(t,e,i,n){return t[0]=e[0]+n*(i[0]-e[0]),t[1]=e[1]+n*(i[1]-e[1]),t},applyTransform:function(t,e,i){var n=e[0],r=e[1];return t[0]=i[0]*n+i[2]*r+i[4],t[1]=i[1]*n+i[3]*r+i[5],t},min:function(t,e,i){return t[0]=Math.min(e[0],i[0]),t[1]=Math.min(e[1],i[1]),t},max:function(t,e,i){return t[0]=Math.max(e[0],i[0]),t[1]=Math.max(e[1],i[1]),t}};return e[R]=e.len,e.lengthSquare=e.lenSquare,e.dist=e.distance,e.distSquare=e.distanceSquare,e}),e("echarts/scale/Interval",[le,"../util/number","../util/format","./Scale"],function(t){var e=t("../util/number"),i=t("../util/format"),n=t("./Scale"),r=Math.floor,a=Math.ceil,o=e.getPrecisionSafe,s=e.round,l=n[C]({type:"interval",_interval:0,setExtent:function(t,e){var i=this._extent;isNaN(t)||(i[0]=parseFloat(t)),isNaN(e)||(i[1]=parseFloat(e))},unionExtent:function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1]),l[Z].setExtent.call(this,e[0],e[1])},getInterval:function(){return this._interval||this.niceTicks(),this._interval},setInterval:function(t){this._interval=t,this._niceExtent=this._extent.slice()},getTicks:function(){this._interval||this.niceTicks();var t=this._interval,e=this._extent,i=[],n=1e4;if(t){var r=this._niceExtent,a=o(t)+2;e[0]<r[0]&&i.push(e[0]);for(var l=r[0];l<=r[1];)if(i.push(l),l=s(l+t,a),i[R]>n)return[];e[1]>(i[R]?i[i[R]-1]:r[1])&&i.push(e[1])}return i},getTicksLabels:function(){for(var t=[],e=this.getTicks(),i=0;i<e[R];i++)t.push(this[p](e[i]));return t},getLabel:function(t){return i.addCommas(t)},niceTicks:function(t){t=t||5;var i=this._extent,n=i[1]-i[0];if(isFinite(n)){0>n&&(n=-n,i.reverse());var l=s(e.nice(n/t,!0),Math.max(o(i[0]),o(i[1]))+2),u=o(l)+2,h=[s(a(i[0]/l)*l,u),s(r(i[1]/l)*l,u)];
this._interval=l,this._niceExtent=h}},niceExtent:function(t,e,i){var n=this._extent;if(n[0]===n[1])if(0!==n[0]){var o=n[0];i?n[0]-=o/2:(n[1]+=o/2,n[0]-=o/2)}else n[1]=1;var l=n[1]-n[0];isFinite(l)||(n[0]=0,n[1]=1),this.niceTicks(t);var u=this._interval;e||(n[0]=s(r(n[0]/u)*u)),i||(n[1]=s(a(n[1]/u)*u))}});return l[k]=function(){return new l},l}),e("echarts/scale/Scale",[le,"../util/clazz"],function(t){function e(){this._extent=[1/0,-1/0],this._interval=0,this.init&&this.init.apply(this,arguments)}var i=t("../util/clazz"),n=e[Z];return n.parse=function(t){return t},n[v]=function(t){var e=this._extent;return t>=e[0]&&t<=e[1]},n.normalize=function(t){var e=this._extent;return e[1]===e[0]?.5:(t-e[0])/(e[1]-e[0])},n.scale=function(t){var e=this._extent;return t*(e[1]-e[0])+e[0]},n.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1])},n[m]=function(){return this._extent.slice()},n.setExtent=function(t,e){var i=this._extent;isNaN(t)||(i[0]=t),isNaN(e)||(i[1]=e)},n.getTicksLabels=function(){for(var t=[],e=this.getTicks(),i=0;i<e[R];i++)t.push(this[p](e[i]));return t},i.enableClassExtend(e),i.enableClassManagement(e,{registerWhenExtend:!0}),e}),e("zrender/tool/path",[le,"../graphic/Path","../core/PathProxy","./transformPath","../core/matrix"],function(t){function e(t,e,i,n,r,a,o,s,l,u,p){var g=l*(f/180),y=d(g)*(t-i)/2+c(g)*(e-n)/2,_=-1*c(g)*(t-i)/2+d(g)*(e-n)/2,x=y*y/(o*o)+_*_/(s*s);x>1&&(o*=h(x),s*=h(x));var b=(r===a?-1:1)*h((o*o*s*s-o*o*_*_-s*s*y*y)/(o*o*_*_+s*s*y*y))||0,w=b*o*_/s,T=b*-s*y/o,M=(t+i)/2+d(g)*w-c(g)*T,A=(e+n)/2+c(g)*w+d(g)*T,C=v([1,0],[(y-w)/o,(_-T)/s]),S=[(y-w)/o,(_-T)/s],P=[(-1*y-w)/o,(-1*_-T)/s],I=v(S,P);m(S,P)<=-1&&(I=f),m(S,P)>=1&&(I=0),0===a&&I>0&&(I-=2*f),1===a&&0>I&&(I+=2*f),p.addData(u,M,A,o,s,C,I,g,a)}function i(t){if(!t)return[];var i,n=t[x](/-/g," -")[x](/  /g," ")[x](/ /g,",")[x](/,,/g,",");for(i=0;i<u[R];i++)n=n[x](new RegExp(u[i],"g"),"|"+u[i]);var r,a=n.split("|"),s=0,l=0,h=new o,c=o.CMD;for(i=1;i<a[R];i++){var d,f=a[i],p=f.charAt(0),m=0,v=f.slice(1)[x](/e,-/g,"e-").split(",");v[R]>0&&""===v[0]&&v.shift();for(var g=0;g<v[R];g++)v[g]=parseFloat(v[g]);for(;m<v[R]&&!isNaN(v[m])&&!isNaN(v[0]);){var y,_,b,w,T,M,A,C=s,S=l;switch(p){case"l":s+=v[m++],l+=v[m++],d=c.L,h.addData(d,s,l);break;case"L":s=v[m++],l=v[m++],d=c.L,h.addData(d,s,l);break;case"m":s+=v[m++],l+=v[m++],d=c.M,h.addData(d,s,l),p="l";break;case"M":s=v[m++],l=v[m++],d=c.M,h.addData(d,s,l),p="L";break;case"h":s+=v[m++],d=c.L,h.addData(d,s,l);break;case"H":s=v[m++],d=c.L,h.addData(d,s,l);break;case"v":l+=v[m++],d=c.L,h.addData(d,s,l);break;case"V":l=v[m++],d=c.L,h.addData(d,s,l);break;case"C":d=c.C,h.addData(d,v[m++],v[m++],v[m++],v[m++],v[m++],v[m++]),s=v[m-2],l=v[m-1];break;case"c":d=c.C,h.addData(d,v[m++]+s,v[m++]+l,v[m++]+s,v[m++]+l,v[m++]+s,v[m++]+l),s+=v[m-2],l+=v[m-1];break;case"S":y=s,_=l;var P=h.len(),I=h.data;r===c.C&&(y+=s-I[P-4],_+=l-I[P-3]),d=c.C,C=v[m++],S=v[m++],s=v[m++],l=v[m++],h.addData(d,y,_,C,S,s,l);break;case"s":y=s,_=l;var P=h.len(),I=h.data;r===c.C&&(y+=s-I[P-4],_+=l-I[P-3]),d=c.C,C=s+v[m++],S=l+v[m++],s+=v[m++],l+=v[m++],h.addData(d,y,_,C,S,s,l);break;case"Q":C=v[m++],S=v[m++],s=v[m++],l=v[m++],d=c.Q,h.addData(d,C,S,s,l);break;case"q":C=v[m++]+s,S=v[m++]+l,s+=v[m++],l+=v[m++],d=c.Q,h.addData(d,C,S,s,l);break;case"T":y=s,_=l;var P=h.len(),I=h.data;r===c.Q&&(y+=s-I[P-4],_+=l-I[P-3]),s=v[m++],l=v[m++],d=c.Q,h.addData(d,y,_,s,l);break;case"t":y=s,_=l;var P=h.len(),I=h.data;r===c.Q&&(y+=s-I[P-4],_+=l-I[P-3]),s+=v[m++],l+=v[m++],d=c.Q,h.addData(d,y,_,s,l);break;case"A":b=v[m++],w=v[m++],T=v[m++],M=v[m++],A=v[m++],C=s,S=l,s=v[m++],l=v[m++],d=c.A,e(C,S,s,l,M,A,b,w,T,d,h);break;case"a":b=v[m++],w=v[m++],T=v[m++],M=v[m++],A=v[m++],C=s,S=l,s+=v[m++],l+=v[m++],d=c.A,e(C,S,s,l,M,A,b,w,T,d,h)}}("z"===p||"Z"===p)&&(d=c.Z,h.addData(d)),r=d}return h.toStatic(),h}function r(t,e){var r,a=i(t);return e=e||{},e.buildPath=function(t){t.setData(a.data),r&&s(t,r);var e=t.getContext();e&&t.rebuildPath(e)},e[n]=function(t){r||(r=l[k]()),l.mul(r,t,r),this.dirty(!0)},e}var a=t("../graphic/Path"),o=t("../core/PathProxy"),s=t("./transformPath"),l=t("../core/matrix"),u=["m","M","l","L","v","V","h","H","z","Z","c","C","q","Q","t","T","s","S","a","A"],h=Math.sqrt,c=Math.sin,d=Math.cos,f=Math.PI,p=function(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])},m=function(t,e){return(t[0]*e[0]+t[1]*e[1])/(p(t)*p(e))},v=function(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(m(t,e))};return{createFromString:function(t,e){return new a(r(t,e))},extendFromString:function(t,e){return a[C](r(t,e))},mergePath:function(t,e){for(var i=[],n=t[R],r=0;n>r;r++){var o=t[r];o.__dirty&&o.buildPath(o.path,o.shape,!0),i.push(o.path)}var s=new a(e);return s.buildPath=function(t){t.appendPath(i);var e=t.getContext();e&&t.rebuildPath(e)},s}}}),e("zrender/graphic/Path",[le,"./Displayable","../core/util","../core/PathProxy","../contain/path","./Pattern"],function(t){function e(t){i.call(this,t),this.path=new r}var i=t("./Displayable"),n=t("../core/util"),r=t("../core/PathProxy"),a=t("../contain/path"),o=t("./Pattern"),s=o[Z].getCanvasPattern,l=Math.abs;return e[Z]={constructor:e,type:"path",__dirtyPath:!0,strokeContainThreshold:5,brush:function(t,e){var i=this.style,n=this.path,r=i.hasStroke(),a=i.hasFill(),o=i.fill,l=i.stroke,u=a&&!!o[I],h=r&&!!l[I],c=a&&!!o.image,d=r&&!!l.image;if(i.bind(t,this,e),this.setTransform(t),this.__dirty){var f=this[J]();u&&(this._fillGradient=i.getGradient(t,o,f)),h&&(this._strokeGradient=i.getGradient(t,l,f))}u?t.fillStyle=this._fillGradient:c&&(t.fillStyle=s.call(o,t)),h?t.strokeStyle=this._strokeGradient:d&&(t.strokeStyle=s.call(l,t));var p=i.lineDash,m=i.lineDashOffset,v=!!t.setLineDash,g=this.getGlobalScale();n.setScale(g[0],g[1]),this.__dirtyPath||p&&!v&&r?(n=this.path.beginPath(t),p&&!v&&(n.setLineDash(p),n.setLineDashOffset(m)),this.buildPath(n,this.shape,!1),this.__dirtyPath=!1):(t.beginPath(),this.path.rebuildPath(t)),a&&n.fill(t),p&&v&&(t.setLineDash(p),t.lineDashOffset=m),r&&n.stroke(t),p&&v&&t.setLineDash([]),this.restoreTransform(t),null!=i.text&&this.drawRectText(t,this[J]())},buildPath:function(){},getBoundingRect:function(){var t=this._rect,e=this.style,i=!t;if(i){var n=this.path;this.__dirtyPath&&(n.beginPath(),this.buildPath(n,this.shape,!1)),t=n[J]()}if(this._rect=t,e.hasStroke()){var r=this._rectWithStroke||(this._rectWithStroke=t.clone());if(this.__dirty||i){r.copy(t);var a=e.lineWidth,o=e.strokeNoScale?this.getLineScale():1;e.hasFill()||(a=Math.max(a,this.strokeContainThreshold||4)),o>1e-10&&(r.width+=a/o,r[K]+=a/o,r.x-=a/o/2,r.y-=a/o/2)}return r}return t},contain:function(t,e){var i=this.transformCoordToLocal(t,e),n=this[J](),r=this.style;if(t=i[0],e=i[1],n[v](t,e)){var o=this.path.data;if(r.hasStroke()){var s=r.lineWidth,l=r.strokeNoScale?this.getLineScale():1;if(l>1e-10&&(r.hasFill()||(s=Math.max(s,this.strokeContainThreshold)),a.containStroke(o,s/l,t,e)))return!0}if(r.hasFill())return a[v](o,t,e)}return!1},dirty:function(t){null==t&&(t=!0),t&&(this.__dirtyPath=t,this._rect=null),this.__dirty=!0,this.__zr&&this.__zr.refresh(),this.__clipTarget&&this.__clipTarget.dirty()},animateShape:function(t){return this.animate("shape",t)},attrKV:function(t,e){"shape"===t?(this.setShape(e),this.__dirtyPath=!0,this._rect=null):i[Z].attrKV.call(this,t,e)},setShape:function(t,e){var i=this.shape;if(i){if(n[P](t))for(var r in t)t.hasOwnProperty(r)&&(i[r]=t[r]);else i[t]=e;this.dirty(!0)}return this},getLineScale:function(){var t=this.transform;return t&&l(t[0]-1)>1e-10&&l(t[3]-1)>1e-10?Math.sqrt(l(t[0]*t[3]-t[2]*t[1])):1}},e[C]=function(t){var i=function(i){e.call(this,i),t.style&&this.style.extendFrom(t.style,!1);var n=t.shape;if(n){this.shape=this.shape||{};var r=this.shape;for(var a in n)!r.hasOwnProperty(a)&&n.hasOwnProperty(a)&&(r[a]=n[a])}t.init&&t.init.call(this,i)};n.inherits(i,e);for(var r in t)"style"!==r&&"shape"!==r&&(i[Z][r]=t[r]);return i},n.inherits(e,i),e}),e("zrender/container/Group",[le,"../core/util","../Element","../core/BoundingRect"],function(t){var e=t("../core/util"),i=t("../Element"),a=t("../core/BoundingRect"),o=function(t){t=t||{},i.call(this,t);for(var e in t)t.hasOwnProperty(e)&&(this[e]=t[e]);this._children=[],this.__storage=null,this.__dirty=!0};return o[Z]={constructor:o,isGroup:!0,type:"group",silent:!1,children:function(){return this._children.slice()},childAt:function(t){return this._children[t]},childOfName:function(t){for(var e=this._children,i=0;i<e[R];i++)if(e[i].name===t)return e[i]},childCount:function(){return this._children[R]},add:function(t){return t&&t!==this&&t[r]!==this&&(this._children.push(t),this._doAdd(t)),this},addBefore:function(t,e){if(t&&t!==this&&t[r]!==this&&e&&e[r]===this){var i=this._children,n=i[O](e);n>=0&&(i[T](n,0,t),this._doAdd(t))}return this},_doAdd:function(t){t[r]&&t[r][S](t),t[r]=this;var e=this.__storage,i=this.__zr;e&&e!==t.__storage&&(e.addToMap(t),t instanceof o&&t.addChildrenToStorage(e)),i&&i.refresh()},remove:function(t){var i=this.__zr,n=this.__storage,a=this._children,s=e[O](a,t);return 0>s?this:(a[T](s,1),t[r]=null,n&&(n.delFromMap(t.id),t instanceof o&&t.delChildrenFromStorage(n)),i&&i.refresh(),this)},removeAll:function(){var t,e,i=this._children,n=this.__storage;for(e=0;e<i[R];e++)t=i[e],n&&(n.delFromMap(t.id),t instanceof o&&t.delChildrenFromStorage(n)),t[r]=null;return i[R]=0,this},eachChild:function(t,e){for(var i=this._children,n=0;n<i[R];n++){var r=i[n];t.call(e,r,n)}return this},traverse:function(t,e){for(var i=0;i<this._children[R];i++){var n=this._children[i];t.call(e,n),"group"===n.type&&n[w](t,e)}return this},addChildrenToStorage:function(t){for(var e=0;e<this._children[R];e++){var i=this._children[e];t.addToMap(i),i instanceof o&&i.addChildrenToStorage(t)}},delChildrenFromStorage:function(t){for(var e=0;e<this._children[R];e++){var i=this._children[e];t.delFromMap(i.id),i instanceof o&&i.delChildrenFromStorage(t)}},dirty:function(){return this.__dirty=!0,this.__zr&&this.__zr.refresh(),this},getBoundingRect:function(t){for(var e=null,i=new a(0,0,0,0),r=t||this._children,o=[],s=0;s<r[R];s++){var l=r[s];if(!l[E]&&!l.invisible){var u=l[J](),h=l.getLocalTransform(o);h?(i.copy(u),i[n](h),e=e||i.clone(),e.union(i)):(e=e||u.clone(),e.union(u))}}return e||i}},e.inherits(o,i),o}),e("zrender/graphic/Image",[le,"./Displayable","../core/BoundingRect","../core/util","../core/LRU"],function(t){function e(t){i.call(this,t)}var i=t("./Displayable"),n=t("../core/BoundingRect"),r=t("../core/util"),a=t("../core/LRU"),o=new a(50);return e[Z]={constructor:e,type:"image",brush:function(t,e){var i,n=this.style,r=n.image;if(n.bind(t,this,e),i=typeof r===G?this._image:r,!i&&r){var a=o.get(r);if(!a)return i=new Image,i.onload=function(){i.onload=null;for(var t=0;t<a.pending[R];t++)a.pending[t].dirty()},a={image:i,pending:[this]},i.src=r,o.put(r,a),void(this._image=i);if(i=a.image,this._image=i,!i.width||!i[K])return void a.pending.push(this)}if(i){var s=n.width||i.width,l=n[K]||i[K],u=n.x||0,h=n.y||0;if(!i.width||!i[K])return;if(this.setTransform(t),n.sWidth&&n.sHeight){var c=n.sx||0,d=n.sy||0;t.drawImage(i,c,d,n.sWidth,n.sHeight,u,h,s,l)}else if(n.sx&&n.sy){var c=n.sx,d=n.sy,f=s-c,p=l-d;t.drawImage(i,c,d,f,p,u,h,s,l)}else t.drawImage(i,u,h,s,l);null==n.width&&(n.width=s),null==n[K]&&(n[K]=l),this.restoreTransform(t),null!=n.text&&this.drawRectText(t,this[J]())}},getBoundingRect:function(){var t=this.style;return this._rect||(this._rect=new n(t.x||0,t.y||0,t.width||0,t[K]||0)),this._rect}},r.inherits(e,i),e}),e("zrender/graphic/shape/Circle",[le,"../Path"],function(t){return t("../Path")[C]({type:"circle",shape:{cx:0,cy:0,r:0},buildPath:function(t,e,i){i&&t.moveTo(e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI,!0)}})}),e("zrender/graphic/Text",[le,"./Displayable","../core/util","../contain/text"],function(t){var e=t("./Displayable"),i=t("../core/util"),n=t("../contain/text"),r=function(t){e.call(this,t)};return r[Z]={constructor:r,type:"text",brush:function(t,e){var i=this.style,r=i.x||0,a=i.y||0,o=i.text;if(null!=o&&(o+=""),i.bind(t,this,e),o){this.setTransform(t);var s,l=i[ee],u=i.textFont||i.font;if(i.textVerticalAlign){var h=n[J](o,u,i[ee],"top");switch(s=j,i.textVerticalAlign){case j:a-=h[K]/2-h.lineHeight/2;break;case U:a-=h[K]-h.lineHeight/2;break;default:a+=h.lineHeight/2}}else s=i.textBaseline;t.font=u||"12px sans-serif",t[ee]=l||"left",t[ee]!==l&&(t[ee]="left"),t.textBaseline=s||"alphabetic",t.textBaseline!==s&&(t.textBaseline="alphabetic");for(var c=n.measureText("国",t.font).width,d=o.split("\n"),f=0;f<d[R];f++)i.hasFill()&&t.fillText(d[f],r,a),i.hasStroke()&&t.strokeText(d[f],r,a),a+=c;this.restoreTransform(t)}},getBoundingRect:function(){if(!this._rect){var t=this.style,e=t.textVerticalAlign,i=n[J](t.text+"",t.textFont||t.font,t[ee],e?"top":t.textBaseline);switch(e){case j:i.y-=i[K]/2;break;case U:i.y-=i[K]}i.x+=t.x||0,i.y+=t.y||0,this._rect=i}return this._rect}},i.inherits(r,e),r}),e("zrender/graphic/shape/Ring",[le,"../Path"],function(t){return t("../Path")[C]({type:"ring",shape:{cx:0,cy:0,r:0,r0:0},buildPath:function(t,e){var i=e.cx,n=e.cy,r=2*Math.PI;t.moveTo(i+e.r,n),t.arc(i,n,e.r,0,r,!1),t.moveTo(i+e.r0,n),t.arc(i,n,e.r0,0,r,!0)}})}),e("zrender/graphic/shape/Sector",[le,"../Path"],function(t){return t("../Path")[C]({type:"sector",shape:{cx:0,cy:0,r0:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},buildPath:function(t,e){var i=e.cx,n=e.cy,r=Math.max(e.r0||0,0),a=Math.max(e.r,0),o=e.startAngle,s=e.endAngle,l=e.clockwise,u=Math.cos(o),h=Math.sin(o);t.moveTo(u*r+i,h*r+n),t.lineTo(u*a+i,h*a+n),t.arc(i,n,a,o,s,!l),t.lineTo(Math.cos(s)*r+i,Math.sin(s)*r+n),0!==r&&t.arc(i,n,r,s,o,l),t.closePath()}})}),e("zrender/graphic/shape/Polygon",[le,"../helper/poly","../Path"],function(t){var e=t("../helper/poly");return t("../Path")[C]({type:"polygon",shape:{points:null,smooth:!1,smoothConstraint:null},buildPath:function(t,i){e.buildPath(t,i,!0)}})}),e("zrender/graphic/shape/Polyline",[le,"../helper/poly","../Path"],function(t){var e=t("../helper/poly");return t("../Path")[C]({type:"polyline",shape:{points:null,smooth:!1,smoothConstraint:null},style:{stroke:"#000",fill:null},buildPath:function(t,i){e.buildPath(t,i,!1)}})}),e("zrender/graphic/shape/Rect",[le,"../helper/roundRect","../Path"],function(t){var e=t("../helper/roundRect");return t("../Path")[C]({type:"rect",shape:{r:0,x:0,y:0,width:0,height:0},buildPath:function(t,i){var n=i.x,r=i.y,a=i.width,o=i[K];i.r?e.buildPath(t,i):t.rect(n,r,a,o),t.closePath()}})}),e("zrender/graphic/shape/Arc",[le,"../Path"],function(t){return t("../Path")[C]({type:"arc",shape:{cx:0,cy:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},style:{stroke:"#000",fill:null},buildPath:function(t,e){var i=e.cx,n=e.cy,r=Math.max(e.r,0),a=e.startAngle,o=e.endAngle,s=e.clockwise,l=Math.cos(a),u=Math.sin(a);t.moveTo(l*r+i,u*r+n),t.arc(i,n,r,a,o,!s)}})}),e("zrender/graphic/shape/Line",[le,"../Path"],function(t){return t("../Path")[C]({type:"line",shape:{x1:0,y1:0,x2:0,y2:0,percent:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var i=e.x1,n=e.y1,r=e.x2,a=e.y2,o=e.percent;0!==o&&(t.moveTo(i,n),1>o&&(r=i*(1-o)+r*o,a=n*(1-o)+a*o),t.lineTo(r,a))},pointAt:function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]}})}),e("zrender/graphic/shape/BezierCurve",[le,"../../core/curve","../../core/vector","../Path"],function(t){function e(t,e,i){var n=t.cpx2,r=t.cpy2;return null===n||null===r?[(i?u:s)(t.x1,t.cpx1,t.cpx2,t.x2,e),(i?u:s)(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(i?l:o)(t.x1,t.cpx1,t.x2,e),(i?l:o)(t.y1,t.cpy1,t.y2,e)]}var i=t("../../core/curve"),n=t("../../core/vector"),r=i.quadraticSubdivide,a=i.cubicSubdivide,o=i.quadraticAt,s=i.cubicAt,l=i.quadraticDerivativeAt,u=i.cubicDerivativeAt,h=[];return t("../Path")[C]({type:"bezier-curve",shape:{x1:0,y1:0,x2:0,y2:0,cpx1:0,cpy1:0,percent:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var i=e.x1,n=e.y1,o=e.x2,s=e.y2,l=e.cpx1,u=e.cpy1,c=e.cpx2,d=e.cpy2,f=e.percent;0!==f&&(t.moveTo(i,n),null==c||null==d?(1>f&&(r(i,l,o,f,h),l=h[1],o=h[2],r(n,u,s,f,h),u=h[1],s=h[2]),t.quadraticCurveTo(l,u,o,s)):(1>f&&(a(i,l,c,o,f,h),l=h[1],c=h[2],o=h[3],a(n,u,d,s,f,h),u=h[1],d=h[2],s=h[3]),t.bezierCurveTo(l,u,c,d,o,s)))},pointAt:function(t){return e(this.shape,t,!1)},tangentAt:function(t){var i=e(this.shape,t,!0);return n.normalize(i,i)}})}),e("zrender/graphic/CompoundPath",[le,"./Path"],function(t){var e=t("./Path");return e[C]({type:"compound",shape:{paths:null},_updatePathDirty:function(){for(var t=this.__dirtyPath,e=this.shape.paths,i=0;i<e[R];i++)t=t||e[i].__dirtyPath;this.__dirtyPath=t,this.__dirty=this.__dirty||t},beforeBrush:function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),i=0;i<t[R];i++)t[i].path.setScale(e[0],e[1])},buildPath:function(t,e){for(var i=e.paths||[],n=0;n<i[R];n++)i[n].buildPath(t,i[n].shape,!0)},afterBrush:function(){for(var t=this.shape.paths,e=0;e<t[R];e++)t[e].__dirtyPath=!1},getBoundingRect:function(){return this._updatePathDirty(),e[Z][J].call(this)}})}),e("zrender/graphic/LinearGradient",[le,"../core/util","./Gradient"],function(t){var e=t("../core/util"),i=t("./Gradient"),n=function(t,e,n,r,a,o){this.x=null==t?0:t,this.y=null==e?0:e,this.x2=null==n?1:n,this.y2=null==r?0:r,this.type="linear",this.global=o||!1,i.call(this,a)};return n[Z]={constructor:n},e.inherits(n,i),n}),e("zrender/graphic/RadialGradient",[le,"../core/util","./Gradient"],function(t){var e=t("../core/util"),i=t("./Gradient"),n=function(t,e,n,r,a){this.x=null==t?.5:t,this.y=null==e?.5:e,this.r=null==n?.5:n,this.type="radial",this.global=a||!1,i.call(this,r)};return n[Z]={constructor:n},e.inherits(n,i),n}),e("echarts/model/mixin/colorPalette",[],function(){return{clearColorPalette:function(){this._colorIdx=0,this._colorNameMap={}},getColorFromPalette:function(t,e){e=e||this;var i=e._colorIdx||0,n=e._colorNameMap||(e._colorNameMap={});if(n[t])return n[t];var r=this.get("color",!0)||[];if(r[R]){var a=r[i];return t&&(n[t]=a),e._colorIdx=(i+1)%r[R],a}}}}),e("zrender/core/BoundingRect",[le,"./vector","./matrix"],function(t){function e(t,e,i,n){0>i&&(t+=i,i=-i),0>n&&(e+=n,n=-n),this.x=t,this.y=e,this.width=i,this[K]=n}var i=t("./vector"),r=t("./matrix"),a=i[n],o=Math.min,s=Math.max;return e[Z]={constructor:e,union:function(t){var e=o(t.x,this.x),i=o(t.y,this.y);this.width=s(t.x+t.width,this.x+this.width)-e,this[K]=s(t.y+t[K],this.y+this[K])-i,this.x=e,this.y=i},applyTransform:function(){var t=[],e=[],i=[],n=[];return function(r){if(r){t[0]=i[0]=this.x,t[1]=n[1]=this.y,e[0]=n[0]=this.x+this.width,e[1]=i[1]=this.y+this[K],a(t,t,r),a(e,e,r),a(i,i,r),a(n,n,r),this.x=o(t[0],e[0],i[0],n[0]),this.y=o(t[1],e[1],i[1],n[1]);var l=s(t[0],e[0],i[0],n[0]),u=s(t[1],e[1],i[1],n[1]);this.width=l-this.x,this[K]=u-this.y}}}(),calculateTransform:function(t){var e=this,i=t.width/e.width,n=t[K]/e[K],a=r[k]();return r.translate(a,a,[-e.x,-e.y]),r.scale(a,a,[i,n]),r.translate(a,a,[t.x,t.y]),a},intersect:function(t){if(!t)return!1;t instanceof e||(t=e[k](t));var i=this,n=i.x,r=i.x+i.width,a=i.y,o=i.y+i[K],s=t.x,l=t.x+t.width,u=t.y,h=t.y+t[K];return!(s>r||n>l||u>o||a>h)},contain:function(t,e){var i=this;return t>=i.x&&t<=i.x+i.width&&e>=i.y&&e<=i.y+i[K]},clone:function(){return new e(this.x,this.y,this.width,this[K])},copy:function(t){this.x=t.x,this.y=t.y,this.width=t.width,this[K]=t[K]},plain:function(){return{x:this.x,y:this.y,width:this.width,height:this[K]}}},e[k]=function(t){return new e(t.x,t.y,t.width,t[K])},e}),e("echarts/model/globalDefault",[],function(){var t="";return typeof navigator!==i&&(t=navigator.platform||""),{color:["#c23531","#2f4554","#61a0a8","#d48265","#91c7ae","#749f83","#ca8622","#bda29a","#6e7074","#546570","#c4ccd3"],textStyle:{fontFamily:t.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,animation:!0,animationDuration:1e3,animationDurationUpdate:300,animationEasing:"exponentialOut",animationEasingUpdate:"cubicOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3}}),e("echarts/data/List",[le,"../model/Model","./DataDiffer",se,"../util/model"],function(t){function e(t){return m[_](t)||(t=[t]),t}function n(t,e){var i=t[f],n=new T(m.map(i,t.getDimensionInfo,t),t.hostModel);b(n,t);for(var r=n._storage={},a=t._storage,o=0;o<i[R];o++){var s=i[o],l=a[s];r[s]=m[O](e,s)>=0?new l.constructor(a[s][R]):a[s]}return n}var r=i,a=typeof window===i?global:window,o=typeof a.Float64Array===r?Array:a.Float64Array,l=typeof a.Int32Array===r?Array:a.Int32Array,u={"float":o,"int":l,ordinal:Array,number:Array,time:Array},h=t("../model/Model"),p=t("./DataDiffer"),m=t(se),v=t("../util/model"),g=m[P],x=["stackedOn","hasItemOption","_nameList","_idList","_rawData"],b=function(t,e){m.each(x.concat(e.__wrappedMethods||[]),function(i){e.hasOwnProperty(i)&&(t[i]=e[i])}),t.__wrappedMethods=e.__wrappedMethods},T=function(t,e){t=t||["x","y"];for(var i={},n=[],r=0;r<t[R];r++){var a,o={};typeof t[r]===G?(a=t[r],o={name:a,stackable:!1,type:"number"}):(o=t[r],a=o.name,o.type=o.type||"number"),n.push(a),i[a]=o}this[f]=n,this._dimensionInfos=i,this.hostModel=e,this.dataType,this.indices=[],this._storage={},this._nameList=[],this._idList=[],this._optionModels=[],this.stackedOn=null,this._visual={},this._layout={},this._itemVisuals=[],this._itemLayouts=[],this._graphicEls=[],this._rawData,this._extent},M=T[Z];M.type="list",M.hasItemOption=!0,M.getDimension=function(t){return isNaN(t)||(t=this[f][t]||t),t},M.getDimensionInfo=function(t){return m.clone(this._dimensionInfos[this.getDimension(t)])},M.initData=function(t,e,i){t=t||[],this._rawData=t;var n=this._storage={},r=this.indices=[],a=this[f],o=t[R],s=this._dimensionInfos,l=[],h={};e=e||[];for(var c=0;c<a[R];c++){var d=s[a[c]],p=u[d.type];n[a[c]]=new p(o)}var m=this;i||(m.hasItemOption=!1),i=i||function(t,e,i,n){var r=v.getDataItemValue(t);return v.isDataItemOption(t)&&(m.hasItemOption=!0),v.converDataValue(r instanceof Array?r[n]:r,s[e])};for(var g=0;g<t[R];g++){for(var y=t[g],_=0;_<a[R];_++){var x=a[_],b=n[x];b[g]=i(y,x,g,_)}r.push(g)}for(var c=0;c<t[R];c++){e[c]||t[c]&&null!=t[c].name&&(e[c]=t[c].name);var w=e[c]||"",T=t[c]&&t[c].id;!T&&w&&(h[w]=h[w]||0,T=w,h[w]>0&&(T+="__ec__"+h[w]),h[w]++),T&&(l[c]=T)}this._nameList=e,this._idList=l},M.count=function(){return this.indices[R]},M.get=function(t,e,i){var n=this._storage,r=this.indices[e];if(null==r)return 0/0;var a=n[t]&&n[t][r];if(i){var o=this._dimensionInfos[t];if(o&&o.stackable)for(var s=this.stackedOn;s;){var l=s.get(t,e);(a>=0&&l>0||0>=a&&0>l)&&(a+=l),s=s.stackedOn}}return a},M.getValues=function(t,e,i){var n=[];m[_](t)||(i=e,e=t,t=this[f]);for(var r=0,a=t[R];a>r;r++)n.push(this.get(t[r],e,i));return n},M.hasValue=function(t){for(var e=this[f],i=this._dimensionInfos,n=0,r=e[R];r>n;n++)if(i[e[n]].type!==d&&isNaN(this.get(e[n],t)))return!1;return!0},M.getDataExtent=function(t,e){t=this.getDimension(t);var i=this._storage[t],n=this.getDimensionInfo(t);e=n&&n.stackable&&e;var r,a=(this._extent||(this._extent={}))[t+!!e];if(a)return a;if(i){for(var o=1/0,s=-1/0,l=0,u=this.count();u>l;l++)r=this.get(t,l,e),o>r&&(o=r),r>s&&(s=r);return this._extent[t+!!e]=[o,s]}return[1/0,-1/0]},M.getSum=function(t,e){var i=this._storage[t],n=0;if(i)for(var r=0,a=this.count();a>r;r++){var o=this.get(t,r,e);isNaN(o)||(n+=o)}return n},M[O]=function(t,e){var i=this._storage,n=i[t],r=this.indices;if(n)for(var a=0,o=r[R];o>a;a++){var s=r[a];if(n[s]===e)return a}return-1},M.indexOfName=function(t){for(var e=this.indices,i=this._nameList,n=0,r=e[R];r>n;n++){var a=e[n];if(i[a]===t)return n}return-1},M.indexOfRawIndex=function(t){var e=this.indices,i=e[t];if(null!=i&&i===t)return t;for(var n=0,r=e[R]-1;r>=n;){var a=(n+r)/2|0;if(e[a]<t)n=a+1;else{if(!(e[a]>t))return a;r=a-1}}return-1},M.indexOfNearest=function(t,e,i,n){var r=this._storage,a=r[t];null==n&&(n=1/0);var o=-1;if(a)for(var s=Number.MAX_VALUE,l=0,u=this.count();u>l;l++){var h=e-this.get(t,l,i),c=Math.abs(h);n>=h&&(s>c||c===s&&h>0)&&(s=c,o=l)}return o},M.getRawIndex=function(t){var e=this.indices[t];return null==e?-1:e},M.getRawDataItem=function(t){return this._rawData[this.getRawIndex(t)]},M.getName=function(t){return this._nameList[this.indices[t]]||""},M.getId=function(t){return this._idList[this.indices[t]]||this.getRawIndex(t)+""},M.each=function(t,i,n,r){typeof t===y&&(r=n,n=i,i=t,t=[]),t=m.map(e(t),this.getDimension,this);var a=[],o=t[R],s=this.indices;r=r||this;for(var l=0;l<s[R];l++)switch(o){case 0:i.call(r,l);break;case 1:i.call(r,this.get(t[0],l,n),l);break;case 2:i.call(r,this.get(t[0],l,n),this.get(t[1],l,n),l);break;default:for(var u=0;o>u;u++)a[u]=this.get(t[u],l,n);a[u]=l,i.apply(r,a)}},M.filterSelf=function(t,i,n,r){typeof t===y&&(r=n,n=i,i=t,t=[]),t=m.map(e(t),this.getDimension,this);var a=[],o=[],s=t[R],l=this.indices;r=r||this;for(var u=0;u<l[R];u++){var h;if(1===s)h=i.call(r,this.get(t[0],u,n),u);else{for(var c=0;s>c;c++)o[c]=this.get(t[c],u,n);o[c]=u,h=i.apply(r,o)}h&&a.push(l[u])}return this.indices=a,this._extent={},this},M.mapArray=function(t,e,i,n){typeof t===y&&(n=i,i=e,e=t,t=[]);var r=[];return this.each(t,function(){r.push(e&&e.apply(this,arguments))},i,n),r},M.map=function(t,i,r,a){t=m.map(e(t),this.getDimension,this);var o=n(this,t),s=o.indices=this.indices,l=o._storage,u=[];return this.each(t,function(){var e=arguments[arguments[R]-1],n=i&&i.apply(this,arguments);if(null!=n){"number"==typeof n&&(u[0]=n,n=u);for(var r=0;r<n[R];r++){var a=t[r],o=l[a],h=s[e];o&&(o[h]=n[r])}}},r,a),o},M.downSample=function(t,e,i,r){for(var a=n(this,[t]),o=this._storage,s=a._storage,l=this.indices,u=a.indices=[],h=[],c=[],d=Math.floor(1/e),f=s[t],p=this.count(),m=0;m<o[t][R];m++)s[t][m]=o[t][m];for(var m=0;p>m;m+=d){d>p-m&&(d=p-m,h[R]=d);for(var v=0;d>v;v++){var g=l[m+v];h[v]=f[g],c[v]=g}var y=i(h),g=c[r(h,y)||0];f[g]=y,u.push(g)}return a},M[c]=function(t){var e=this.hostModel;return t=this.indices[t],new h(this._rawData[t],e,e&&e[s])},M.diff=function(t){var e,i=this._idList,n=t&&t._idList,r="e\x00\x00";return new p(t?t.indices:[],this.indices,function(t){return null!=(e=n[t])?e:r+t},function(t){return null!=(e=i[t])?e:r+t})},M.getVisual=function(t){var e=this._visual;return e&&e[t]},M.setVisual=function(t,e){if(g(t))for(var i in t)t.hasOwnProperty(i)&&this.setVisual(i,t[i]);else this._visual=this._visual||{},this._visual[t]=e},M.setLayout=function(t,e){if(g(t))for(var i in t)t.hasOwnProperty(i)&&this.setLayout(i,t[i]);else this._layout[t]=e},M.getLayout=function(t){return this._layout[t]},M.getItemLayout=function(t){return this._itemLayouts[t]},M.setItemLayout=function(t,e,i){this._itemLayouts[t]=i?m[C](this._itemLayouts[t]||{},e):e},M.clearItemLayouts=function(){this._itemLayouts[R]=0},M.getItemVisual=function(t,e,i){var n=this._itemVisuals[t],r=n&&n[e];return null!=r||i?r:this.getVisual(e)},M.setItemVisual=function(t,e,i){var n=this._itemVisuals[t]||{};if(this._itemVisuals[t]=n,g(e))for(var r in e)e.hasOwnProperty(r)&&(n[r]=e[r]);else n[e]=i},M.clearAllVisual=function(){this._visual={},this._itemVisuals=[]};var A=function(t){t.seriesIndex=this.seriesIndex,t[L]=this[L],t.dataType=this.dataType};return M.setItemGraphicEl=function(t,e){var i=this.hostModel;e&&(e[L]=t,e.dataType=this.dataType,e.seriesIndex=i&&i.seriesIndex,"group"===e.type&&e[w](A,e)),this._graphicEls[t]=e},M.getItemGraphicEl=function(t){return this._graphicEls[t]},M.eachItemGraphicEl=function(t,e){m.each(this._graphicEls,function(i,n){i&&t&&t.call(e,i,n)})},M.cloneShallow=function(){var t=m.map(this[f],this.getDimensionInfo,this),e=new T(t,this.hostModel);return e._storage=this._storage,b(e,this),e.indices=this.indices.slice(),this._extent&&(e._extent=m[C]({},this._extent)),e},M.wrapMethod=function(t,e){var i=this[t];typeof i===y&&(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(t),this[t]=function(){var t=i.apply(this,arguments);return e.apply(this,[t].concat(m.slice(arguments)))})},M.TRANSFERABLE_METHODS=["cloneShallow","downSample","map"],M.CHANGABLE_METHODS=["filterSelf"],T}),e("echarts/model/mixin/boxLayout",[le],function(){return{getBoxLayoutParams:function(){return{left:this.get("left"),top:this.get("top"),right:this.get("right"),bottom:this.get(U),width:this.get("width"),height:this.get(K)}}}}),e("echarts/util/component",[le,se,"./clazz"],function(t){var e=t(se),i=t("./clazz"),n=i.parseClassType,r=0,a={},o="_";return a.getUID=function(t){return[t||"",r++,Math.random()].join(o)},a.enableSubTypeDefaulter=function(t){var e={};return t.registerSubTypeDefaulter=function(t,i){t=n(t),e[t.main]=i},t.determineSubType=function(i,r){var a=r.type;if(!a){var o=n(i).main;t.hasSubTypes(i)&&e[o]&&(a=e[o](r))}return a},t},a.enableTopologicalTravel=function(t,i){function n(t){var n={},o=[];return e.each(t,function(s){var l=r(n,s),u=l.originalDeps=i(s),h=a(u,t);l.entryCount=h[R],0===l.entryCount&&o.push(s),e.each(h,function(t){e[O](l.predecessor,t)<0&&l.predecessor.push(t);var i=r(n,t);e[O](i.successor,t)<0&&i.successor.push(s)})}),{graph:n,noEntryList:o}}function r(t,e){return t[e]||(t[e]={predecessor:[],successor:[]}),t[e]}function a(t,i){var n=[];return e.each(t,function(t){e[O](i,t)>=0&&n.push(t)}),n}t.topologicalTravel=function(t,i,r,a){function o(t){u[t].entryCount--,0===u[t].entryCount&&h.push(t)}function s(t){c[t]=!0,o(t)}if(t[R]){var l=n(i),u=l.graph,h=l.noEntryList,c={};for(e.each(t,function(t){c[t]=!0});h[R];){var d=h.pop(),f=u[d],p=!!c[d];p&&(r.call(a,d,f.originalDeps.slice()),delete c[d]),e.each(f.successor,p?s:o)}e.each(c,function(){throw new Error("Circle dependency may exists")})}}},a}),e("echarts/util/clazz",[le,se],function(t){function e(t,e){var i=n.slice(arguments,2);return this.superClass[Z][e].apply(t,i)}function i(t,e,i){return this.superClass[Z][e].apply(t,i)}var n=t(se),r={},a=".",o="___EC__COMPONENT__CONTAINER___",s=r.parseClassType=function(t){var e={main:"",sub:""};return t&&(t=t.split(a),e.main=t[0]||"",e.sub=t[1]||""),e};return r.enableClassExtend=function(t,r){t.$constructor=t,t[C]=function(t){var r=this,a=function(){t.$constructor?t.$constructor.apply(this,arguments):r.apply(this,arguments)};return n[C](a[Z],t),a[C]=this[C],a.superCall=e,a.superApply=i,n.inherits(a,this),a.superClass=r,a}},r.enableClassManagement=function(t,e){function i(t){var e=r[t.main];return e&&e[o]||(e=r[t.main]={},e[o]=!0),e}e=e||{};var r={};if(t.registerClass=function(t,e){if(e)if(e=s(e),e.sub){if(e.sub!==o){var n=i(e);n[e.sub]=t}}else r[e.main]=t;return t},t.getClass=function(t,e,i){var n=r[t];if(n&&n[o]&&(n=e?n[e]:null),i&&!n)throw new Error("Component "+t+"."+(e||"")+" not exists. Load it first.");return n},t.getClassesByMainType=function(t){t=s(t);var e=[],i=r[t.main];return i&&i[o]?n.each(i,function(t,i){i!==o&&e.push(t)}):e.push(i),e},t.hasClass=function(t){return t=s(t),!!r[t.main]},t.getAllClassMainTypes=function(){var t=[];return n.each(r,function(e,i){t.push(i)}),t},t.hasSubTypes=function(t){t=s(t);var e=r[t.main];return e&&e[o]},t.parseClassType=s,e.registerWhenExtend){var a=t[C];a&&(t[C]=function(e){var i=a.call(this,e);return t.registerClass(i,e.type)})}return t},r.setReadOnly=function(){},r}),e("echarts/model/mixin/lineStyle",[le,"./makeStyleMapper"],function(t){var e=t("./makeStyleMapper")([["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]);return{getLineStyle:function(t){var i=e.call(this,t),n=this.getLineDash(i.lineWidth);return n&&(i.lineDash=n),i},getLineDash:function(t){null==t&&(t=1);var e=this.get("type"),i=Math.max(t,2),n=4*t;return"solid"===e||null==e?null:"dashed"===e?[n,n]:[i,i]}}}),e("echarts/model/mixin/areaStyle",[le,"./makeStyleMapper"],function(t){return{getAreaStyle:t("./makeStyleMapper")([["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]])}}),e("echarts/model/mixin/itemStyle",[le,"./makeStyleMapper"],function(t){var e=t("./makeStyleMapper")([["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["textPosition"],[ee]]);
return{getItemStyle:function(t){var i=e.call(this,t),n=this.getBorderLineDash();return n&&(i.lineDash=n),i},getBorderLineDash:function(){var t=this.get("borderType");return"solid"===t||null==t?null:"dashed"===t?[5,5]:[1,1]}}}),e("echarts/model/mixin/textStyle",[le,"zrender/contain/text"],function(t){function e(t,e){return t&&t[h](e)}var i=t("zrender/contain/text");return{getTextColor:function(){var t=this[s];return this[h]("color")||t&&t.get("textStyle.color")},getFont:function(){var t=this[s],i=t&&t[ne](ie);return[this[h]("fontStyle")||e(i,"fontStyle"),this[h]("fontWeight")||e(i,"fontWeight"),(this[h]("fontSize")||e(i,"fontSize")||12)+"px",this[h]("fontFamily")||e(i,"fontFamily")||"sans-serif"].join(" ")},getTextRect:function(t){return i[J](t,this[te](),this[h]("align"),this[h]("baseline"))},truncateText:function(t,e,n,r){return i.truncateText(t,e,this[te](),n,r)}}}),e("zrender/contain/text",[le,"../core/util","../core/BoundingRect"],function(t){function e(t,e){var i=t+":"+e;if(s[i])return s[i];for(var n=(t+"").split("\n"),r=0,a=0,o=n[R];o>a;a++)r=Math.max(f.measureText(n[a],e).width,r);return l>u&&(l=0,s={}),l++,s[i]=r,r}function i(t,i,n,r){var a=((t||"")+"").split("\n")[R],o=e(t,i),s=e("国",i),l=a*s,u=new c(0,0,o,l);switch(u.lineHeight=s,r){case U:case"alphabetic":u.y-=s;break;case j:u.y-=s/2}switch(n){case"end":case"right":u.x-=u.width;break;case X:u.x-=u.width/2}return u}function n(t,e,i,n){var r=e.x,a=e.y,o=e[K],s=e.width,l=i[K],u=o/2-l/2,h="left";switch(t){case"left":r-=n,a+=u,h="right";break;case"right":r+=n+s,a+=u,h="left";break;case"top":r+=s/2,a-=n+l,h=X;break;case U:r+=s/2,a+=o+n,h=X;break;case"inside":r+=s/2,a+=u,h=X;break;case"insideLeft":r+=n,a+=u,h="left";break;case"insideRight":r+=s-n,a+=u,h="right";break;case"insideTop":r+=s/2,a+=n,h=X;break;case"insideBottom":r+=s/2,a+=o-l-n,h=X;break;case"insideTopLeft":r+=n,a+=n,h="left";break;case"insideTopRight":r+=s-n,a+=n,h="right";break;case"insideBottomLeft":r+=n,a+=o-l-n;break;case"insideBottomRight":r+=s-n,a+=o-l-n,h="right"}return{x:r,y:a,textAlign:h,textBaseline:"top"}}function r(t,i,n,r,a){if(!i)return"";a=a||{},r=d(r,"...");for(var s=d(a.maxIterations,2),l=d(a.minChar,0),u=e("国",n),h=e("a",n),c=d(a.placeholder,""),f=i=Math.max(0,i-1),p=0;l>p&&f>=h;p++)f-=h;var m=e(r);m>f&&(r="",m=0),f=i-m;for(var v=(t+"").split("\n"),p=0,g=v[R];g>p;p++){var y=v[p],_=e(y,n);if(!(i>=_)){for(var x=0;;x++){if(f>=_||x>=s){y+=r;break}var b=0===x?o(y,f,h,u):_>0?Math.floor(y[R]*f/_):0;y=y.substr(0,b),_=e(y,n)}""===y&&(y=c),v[p]=y}}return v.join("\n")}function o(t,e,i,n){for(var r=0,a=0,o=t[R];o>a&&e>r;a++){var s=t.charCodeAt(a);r+=s>=0&&127>=s?i:n}return a}var s={},l=0,u=5e3,h=t("../core/util"),c=t("../core/BoundingRect"),d=h[a],f={getWidth:e,getBoundingRect:i,adjustTextPositionOnRect:n,truncateText:r,measureText:function(t,e){var i=h.getContext();return i.font=e||"12px sans-serif",i.measureText(t)}};return f}),e("zrender/core/PathProxy",[le,"./curve","./vector","./bbox","./BoundingRect","../config"],function(t){var e=t("./curve"),n=t("./vector"),r=t("./bbox"),a=t("./BoundingRect"),o=t("../config").devicePixelRatio,s={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},l=[],u=[],h=[],c=[],d=Math.min,f=Math.max,p=Math.cos,m=Math.sin,v=Math.sqrt,g=Math.abs,y=typeof Float32Array!=i,_=function(){this.data=[],this._len=0,this._ctx=null,this._xi=0,this._yi=0,this._x0=0,this._y0=0,this._ux=0,this._uy=0};return _[Z]={constructor:_,_lineDash:null,_dashOffset:0,_dashIdx:0,_dashSum:0,setScale:function(t,e){this._ux=g(1/o/t)||0,this._uy=g(1/o/e)||0},getContext:function(){return this._ctx},beginPath:function(t){return this._ctx=t,t&&t.beginPath(),t&&(this.dpr=t.dpr),this._len=0,this._lineDash&&(this._lineDash=null,this._dashOffset=0),this},moveTo:function(t,e){return this.addData(s.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},lineTo:function(t,e){var i=g(t-this._xi)>this._ux||g(e-this._yi)>this._uy||this._len<5;return this.addData(s.L,t,e),this._ctx&&i&&(this._needsDash()?this._dashedLineTo(t,e):this._ctx.lineTo(t,e)),i&&(this._xi=t,this._yi=e),this},bezierCurveTo:function(t,e,i,n,r,a){return this.addData(s.C,t,e,i,n,r,a),this._ctx&&(this._needsDash()?this._dashedBezierTo(t,e,i,n,r,a):this._ctx.bezierCurveTo(t,e,i,n,r,a)),this._xi=r,this._yi=a,this},quadraticCurveTo:function(t,e,i,n){return this.addData(s.Q,t,e,i,n),this._ctx&&(this._needsDash()?this._dashedQuadraticTo(t,e,i,n):this._ctx.quadraticCurveTo(t,e,i,n)),this._xi=i,this._yi=n,this},arc:function(t,e,i,n,r,a){return this.addData(s.A,t,e,i,i,n,r-n,0,a?0:1),this._ctx&&this._ctx.arc(t,e,i,n,r,a),this._xi=p(r)*i+t,this._xi=m(r)*i+t,this},arcTo:function(t,e,i,n,r){return this._ctx&&this._ctx.arcTo(t,e,i,n,r),this},rect:function(t,e,i,n){return this._ctx&&this._ctx.rect(t,e,i,n),this.addData(s.R,t,e,i,n),this},closePath:function(){this.addData(s.Z);var t=this._ctx,e=this._x0,i=this._y0;return t&&(this._needsDash()&&this._dashedLineTo(e,i),t.closePath()),this._xi=e,this._yi=i,this},fill:function(t){t&&t.fill(),this.toStatic()},stroke:function(t){t&&t.stroke(),this.toStatic()},setLineDash:function(t){if(t instanceof Array){this._lineDash=t,this._dashIdx=0;for(var e=0,i=0;i<t[R];i++)e+=t[i];this._dashSum=e}return this},setLineDashOffset:function(t){return this._dashOffset=t,this},len:function(){return this._len},setData:function(t){var e=t[R];this.data&&this.data[R]==e||!y||(this.data=new Float32Array(e));for(var i=0;e>i;i++)this.data[i]=t[i];this._len=e},appendPath:function(t){t instanceof Array||(t=[t]);for(var e=t[R],i=0,n=this._len,r=0;e>r;r++)i+=t[r].len();y&&this.data instanceof Float32Array&&(this.data=new Float32Array(n+i));for(var r=0;e>r;r++)for(var a=t[r].data,o=0;o<a[R];o++)this.data[n++]=a[o];this._len=n},addData:function(t){var e=this.data;this._len+arguments[R]>e[R]&&(this._expandData(),e=this.data);for(var i=0;i<arguments[R];i++)e[this._len++]=arguments[i];this._prevCmd=t},_expandData:function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},_needsDash:function(){return this._lineDash},_dashedLineTo:function(t,e){var i,n,r=this._dashSum,a=this._dashOffset,o=this._lineDash,s=this._ctx,l=this._xi,u=this._yi,h=t-l,c=e-u,p=v(h*h+c*c),m=l,g=u,y=o[R];for(h/=p,c/=p,0>a&&(a=r+a),a%=r,m-=a*h,g-=a*c;h>0&&t>=m||0>h&&m>=t||0==h&&(c>0&&e>=g||0>c&&g>=e);)n=this._dashIdx,i=o[n],m+=h*i,g+=c*i,this._dashIdx=(n+1)%y,h>0&&l>m||0>h&&m>l||c>0&&u>g||0>c&&g>u||s[n%2?"moveTo":"lineTo"](h>=0?d(m,t):f(m,t),c>=0?d(g,e):f(g,e));h=m-t,c=g-e,this._dashOffset=-v(h*h+c*c)},_dashedBezierTo:function(t,i,n,r,a,o){var s,l,u,h,c,d=this._dashSum,f=this._dashOffset,p=this._lineDash,m=this._ctx,g=this._xi,y=this._yi,_=e.cubicAt,x=0,b=this._dashIdx,w=p[R],T=0;for(0>f&&(f=d+f),f%=d,s=0;1>s;s+=.1)l=_(g,t,n,a,s+.1)-_(g,t,n,a,s),u=_(y,i,r,o,s+.1)-_(y,i,r,o,s),x+=v(l*l+u*u);for(;w>b&&(T+=p[b],!(T>f));b++);for(s=(T-f)/x;1>=s;)h=_(g,t,n,a,s),c=_(y,i,r,o,s),b%2?m.moveTo(h,c):m.lineTo(h,c),s+=p[b]/x,b=(b+1)%w;b%2!==0&&m.lineTo(a,o),l=a-h,u=o-c,this._dashOffset=-v(l*l+u*u)},_dashedQuadraticTo:function(t,e,i,n){var r=i,a=n;i=(i+2*t)/3,n=(n+2*e)/3,t=(this._xi+2*t)/3,e=(this._yi+2*e)/3,this._dashedBezierTo(t,e,i,n,r,a)},toStatic:function(){var t=this.data;t instanceof Array&&(t[R]=this._len,y&&(this.data=new Float32Array(t)))},getBoundingRect:function(){l[0]=l[1]=h[0]=h[1]=Number.MAX_VALUE,u[0]=u[1]=c[0]=c[1]=-Number.MAX_VALUE;for(var t=this.data,e=0,i=0,o=0,d=0,f=0;f<t[R];){var v=t[f++];switch(1==f&&(e=t[f],i=t[f+1],o=e,d=i),v){case s.M:o=t[f++],d=t[f++],e=o,i=d,h[0]=o,h[1]=d,c[0]=o,c[1]=d;break;case s.L:r.fromLine(e,i,t[f],t[f+1],h,c),e=t[f++],i=t[f++];break;case s.C:r.fromCubic(e,i,t[f++],t[f++],t[f++],t[f++],t[f],t[f+1],h,c),e=t[f++],i=t[f++];break;case s.Q:r.fromQuadratic(e,i,t[f++],t[f++],t[f],t[f+1],h,c),e=t[f++],i=t[f++];break;case s.A:var g=t[f++],y=t[f++],_=t[f++],x=t[f++],b=t[f++],w=t[f++]+b,T=(t[f++],1-t[f++]);1==f&&(o=p(b)*_+g,d=m(b)*x+y),r.fromArc(g,y,_,x,b,w,T,h,c),e=p(w)*_+g,i=m(w)*x+y;break;case s.R:o=e=t[f++],d=i=t[f++];var M=t[f++],A=t[f++];r.fromLine(o,d,o+M,d+A,h,c);break;case s.Z:e=o,i=d}n.min(l,l,h),n.max(u,u,c)}return 0===f&&(l[0]=l[1]=u[0]=u[1]=0),new a(l[0],l[1],u[0]-l[0],u[1]-l[1])},rebuildPath:function(t){for(var e,i,n,r,a,o,l=this.data,u=this._ux,h=this._uy,c=this._len,d=0;c>d;){var f=l[d++];switch(1==d&&(n=l[d],r=l[d+1],e=n,i=r),f){case s.M:e=n=l[d++],i=r=l[d++],t.moveTo(n,r);break;case s.L:a=l[d++],o=l[d++],(g(a-n)>u||g(o-r)>h||d===c-1)&&(t.lineTo(a,o),n=a,r=o);break;case s.C:t.bezierCurveTo(l[d++],l[d++],l[d++],l[d++],l[d++],l[d++]),n=l[d-2],r=l[d-1];break;case s.Q:t.quadraticCurveTo(l[d++],l[d++],l[d++],l[d++]),n=l[d-2],r=l[d-1];break;case s.A:var v=l[d++],y=l[d++],_=l[d++],x=l[d++],b=l[d++],w=l[d++],T=l[d++],M=l[d++],A=_>x?_:x,C=_>x?1:_/x,S=_>x?x/_:1,P=Math.abs(_-x)>.001,I=b+w;P?(t.translate(v,y),t.rotate(T),t.scale(C,S),t.arc(0,0,A,b,I,1-M),t.scale(1/C,1/S),t.rotate(-T),t.translate(-v,-y)):t.arc(v,y,A,b,I,1-M),1==d&&(e=p(b)*_+v,i=m(b)*x+y),n=p(I)*_+v,r=m(I)*x+y;break;case s.R:e=n=l[d],i=r=l[d+1],t.rect(l[d++],l[d++],l[d++],l[d++]);break;case s.Z:t.closePath(),n=e,r=i}}}},_.CMD=s,_}),e("zrender/tool/transformPath",[le,"../core/PathProxy","../core/vector"],function(t){function e(t,e){var n,r,u,h,c,d,f=t.data,p=i.M,m=i.C,v=i.L,g=i.R,y=i.A,_=i.Q;for(u=0,h=0;u<f[R];){switch(n=f[u++],h=u,r=0,n){case p:r=1;break;case v:r=1;break;case m:r=3;break;case _:r=2;break;case y:var x=e[4],b=e[5],w=s(e[0]*e[0]+e[1]*e[1]),T=s(e[2]*e[2]+e[3]*e[3]),M=l(-e[1]/T,e[0]/w);f[u++]+=x,f[u++]+=b,f[u++]*=w,f[u++]*=T,f[u++]+=M,f[u++]+=M,u+=2,h=u;break;case g:d[0]=f[u++],d[1]=f[u++],a(d,d,e),f[h++]=d[0],f[h++]=d[1],d[0]+=f[u++],d[1]+=f[u++],a(d,d,e),f[h++]=d[0],f[h++]=d[1]}for(c=0;r>c;c++){var d=o[c];d[0]=f[u++],d[1]=f[u++],a(d,d,e),f[h++]=d[0],f[h++]=d[1]}}}var i=t("../core/PathProxy").CMD,r=t("../core/vector"),a=r[n],o=[[],[],[]],s=Math.sqrt,l=Math.atan2;return e}),e("zrender/graphic/Displayable",[le,"../core/util","./Style","../Element","./mixin/RectText"],function(t){function e(t){t=t||{},r.call(this,t);for(var e in t)t.hasOwnProperty(e)&&"style"!==e&&(this[e]=t[e]);this.style=new n(t.style),this._rect=null,this.__clipPaths=[]}var i=t("../core/util"),n=t("./Style"),r=t("../Element"),a=t("./mixin/RectText");return e[Z]={constructor:e,type:"displayable",__dirty:!0,invisible:!1,z:0,z2:0,zlevel:0,draggable:!1,dragging:!1,silent:!1,culling:!1,cursor:"pointer",rectHover:!1,progressive:-1,beforeBrush:function(){},afterBrush:function(){},brush:function(){},getBoundingRect:function(){},contain:function(t,e){return this.rectContain(t,e)},traverse:function(t,e){t.call(e,this)},rectContain:function(t,e){var i=this.transformCoordToLocal(t,e),n=this[J]();return n[v](i[0],i[1])},dirty:function(){this.__dirty=!0,this._rect=null,this.__zr&&this.__zr.refresh()},animateStyle:function(t){return this.animate("style",t)},attrKV:function(t,e){"style"!==t?r[Z].attrKV.call(this,t,e):this.style.set(e)},setStyle:function(t,e){return this.style.set(t,e),this.dirty(!1),this},useStyle:function(t){return this.style=new n(t),this.dirty(!1),this}},i.inherits(e,r),i.mixin(e,a),e}),e("zrender/graphic/Pattern",[le],function(){var t=function(t,e){this.image=t,this.repeat=e,this.type="pattern"};return t[Z].getCanvasPattern=function(t){return this._canvasPattern||(this._canvasPattern=t.createPattern(this.image,this.repeat))},t}),e("zrender/contain/path",[le,"../core/PathProxy","./line","./cubic","./quadratic","./arc","./util","../core/curve","./windingLine"],function(t){function e(t,e){return Math.abs(t-e)<g}function i(){var t=_[0];_[0]=_[1],_[1]=t}function n(t,e,n,r,a,o,s,l,u,h){if(h>e&&h>r&&h>o&&h>l||e>h&&r>h&&o>h&&l>h)return 0;var c=f.cubicRootAt(e,r,o,l,h,y);if(0===c)return 0;for(var d,p,m=0,v=-1,g=0;c>g;g++){var x=y[g],b=0===x||1===x?.5:1,w=f.cubicAt(t,n,a,s,x);u>w||(0>v&&(v=f.cubicExtrema(e,r,o,l,_),_[1]<_[0]&&v>1&&i(),d=f.cubicAt(e,r,o,l,_[0]),v>1&&(p=f.cubicAt(e,r,o,l,_[1]))),m+=2==v?x<_[0]?e>d?b:-b:x<_[1]?d>p?b:-b:p>l?b:-b:x<_[0]?e>d?b:-b:d>l?b:-b)}return m}function r(t,e,i,n,r,a,o,s){if(s>e&&s>n&&s>a||e>s&&n>s&&a>s)return 0;var l=f.quadraticRootAt(e,n,a,s,y);if(0===l)return 0;var u=f.quadraticExtremum(e,n,a);if(u>=0&&1>=u){for(var h=0,c=f.quadraticAt(e,n,a,u),d=0;l>d;d++){var p=0===y[d]||1===y[d]?.5:1,m=f.quadraticAt(t,i,r,y[d]);o>m||(h+=y[d]<u?e>c?p:-p:c>a?p:-p)}return h}var p=0===y[0]||1===y[0]?.5:1,m=f.quadraticAt(t,i,r,y[0]);return o>m?0:e>a?p:-p}function a(t,e,i,n,r,a,o,s){if(s-=e,s>i||-i>s)return 0;var l=Math.sqrt(i*i-s*s);y[0]=-l,y[1]=l;var u=Math.abs(n-r);if(1e-4>u)return 0;if(1e-4>u%v){n=0,r=v;var h=a?1:-1;return o>=y[0]+t&&o<=y[1]+t?h:0}if(a){var l=n;n=d(r),r=d(l)}else n=d(n),r=d(r);n>r&&(r+=v);for(var c=0,f=0;2>f;f++){var p=y[f];if(p+t>o){var m=Math.atan2(s,p),h=a?1:-1;0>m&&(m=v+m),(m>=n&&r>=m||m+v>=n&&r>=m+v)&&(m>Math.PI/2&&m<1.5*Math.PI&&(h=-h),c+=h)}}return c}function o(t,i,o,l,d){for(var f=0,v=0,g=0,y=0,_=0,x=0;x<t[R];){var b=t[x++];switch(b===s.M&&x>1&&(o||(f+=p(v,g,y,_,l,d))),1==x&&(v=t[x],g=t[x+1],y=v,_=g),b){case s.M:y=t[x++],_=t[x++],v=y,g=_;break;case s.L:if(o){if(m(v,g,t[x],t[x+1],i,l,d))return!0}else f+=p(v,g,t[x],t[x+1],l,d)||0;v=t[x++],g=t[x++];break;case s.C:if(o){if(u.containStroke(v,g,t[x++],t[x++],t[x++],t[x++],t[x],t[x+1],i,l,d))return!0}else f+=n(v,g,t[x++],t[x++],t[x++],t[x++],t[x],t[x+1],l,d)||0;v=t[x++],g=t[x++];break;case s.Q:if(o){if(h.containStroke(v,g,t[x++],t[x++],t[x],t[x+1],i,l,d))return!0}else f+=r(v,g,t[x++],t[x++],t[x],t[x+1],l,d)||0;v=t[x++],g=t[x++];break;case s.A:var w=t[x++],T=t[x++],M=t[x++],A=t[x++],C=t[x++],S=t[x++],P=(t[x++],1-t[x++]),I=Math.cos(C)*M+w,z=Math.sin(C)*A+T;x>1?f+=p(v,g,I,z,l,d):(y=I,_=z);var k=(l-w)*A/M+w;if(o){if(c.containStroke(w,T,A,C,C+S,P,i,k,d))return!0}else f+=a(w,T,A,C,C+S,P,k,d);v=Math.cos(C+S)*M+w,g=Math.sin(C+S)*A+T;break;case s.R:y=v=t[x++],_=g=t[x++];var L=t[x++],D=t[x++],I=y+L,z=_+D;if(o){if(m(y,_,I,_,i,l,d)||m(I,_,I,z,i,l,d)||m(I,z,y,z,i,l,d)||m(y,z,y,_,i,l,d))return!0}else f+=p(I,_,I,z,l,d),f+=p(y,z,y,_,l,d);break;case s.Z:if(o){if(m(v,g,y,_,i,l,d))return!0}else f+=p(v,g,y,_,l,d);v=y,g=_}}return o||e(g,_)||(f+=p(v,g,y,_,l,d)||0),0!==f}var s=t("../core/PathProxy").CMD,l=t("./line"),u=t("./cubic"),h=t("./quadratic"),c=t("./arc"),d=t("./util").normalizeRadian,f=t("../core/curve"),p=t("./windingLine"),m=l.containStroke,v=2*Math.PI,g=1e-4,y=[-1,-1,-1],_=[-1,-1];return{contain:function(t,e,i){return o(t,0,!1,e,i)},containStroke:function(t,e,i,n){return o(t,e,!0,i,n)}}}),e("echarts/model/mixin/makeStyleMapper",[le,se],function(t){var e=t(se);return function(t){for(var i=0;i<t[R];i++)t[i][1]||(t[i][1]=t[i][0]);return function(i){for(var n={},r=0;r<t[R];r++){var a=t[r][1];if(!(i&&e[O](i,a)>=0)){var o=this[h](a);null!=o&&(n[t[r][0]]=o)}}return n}}}),e("zrender/graphic/Style",[le],function(){function t(t,e,i){var n=e.x,r=e.x2,a=e.y,o=e.y2;e.global||(n=n*i.width+i.x,r=r*i.width+i.x,a=a*i[K]+i.y,o=o*i[K]+i.y);var s=t.createLinearGradient(n,a,r,o);return s}function e(t,e,i){var n=i.width,r=i[K],a=Math.min(n,r),o=e.x,s=e.y,l=e.r;e.global||(o=o*n+i.x,s=s*r+i.y,l*=a);var u=t.createRadialGradient(o,s,0,o,s,l);return u}var i=[["shadowBlur",0],["shadowOffsetX",0],["shadowOffsetY",0],["shadowColor","#000"],["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]],n=function(t){this.extendFrom(t)};n[Z]={constructor:n,fill:"#000000",stroke:null,opacity:1,lineDash:null,lineDashOffset:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,lineWidth:1,strokeNoScale:!1,text:null,textFill:"#000",textStroke:null,textPosition:"inside",textBaseline:null,textAlign:null,textVerticalAlign:null,textDistance:5,textShadowBlur:0,textShadowOffsetX:0,textShadowOffsetY:0,textTransform:!1,textRotation:0,blend:null,bind:function(t,e,n){for(var r=this,a=n&&n.style,o=!a,s=0;s<i[R];s++){var l=i[s],u=l[0];(o||r[u]!==a[u])&&(t[u]=r[u]||l[1])}if((o||r.fill!==a.fill)&&(t.fillStyle=r.fill),(o||r.stroke!==a.stroke)&&(t.strokeStyle=r.stroke),(o||r.opacity!==a.opacity)&&(t.globalAlpha=null==r.opacity?1:r.opacity),(o||r.blend!==a.blend)&&(t.globalCompositeOperation=r.blend||"source-over"),this.hasStroke()){var h=r.lineWidth;t.lineWidth=h/(this.strokeNoScale&&e&&e.getLineScale?e.getLineScale():1)}},hasFill:function(){var t=this.fill;return null!=t&&"none"!==t},hasStroke:function(){var t=this.stroke;return null!=t&&"none"!==t&&this.lineWidth>0},extendFrom:function(t,e){if(t){var i=this;for(var n in t)!t.hasOwnProperty(n)||!e&&i.hasOwnProperty(n)||(i[n]=t[n])}},set:function(t,e){typeof t===G?this[t]=e:this.extendFrom(t,!0)},clone:function(){var t=new this.constructor;return t.extendFrom(this,!0),t},getGradient:function(i,n,r){for(var a="radial"===n.type?e:t,o=a(i,n,r),s=n[I],l=0;l<s[R];l++)o.addColorStop(s[l].offset,s[l].color);return o}};for(var r=n[Z],a=0;a<i[R];a++){var o=i[a];o[0]in r||(r[o[0]]=o[1])}return n.getGradient=r.getGradient,n}),e("zrender/Element",[le,"./core/guid","./mixin/Eventful","./mixin/Transformable","./mixin/Animatable","./core/util"],function(t){var e=t("./core/guid"),i=t("./mixin/Eventful"),n=t("./mixin/Transformable"),r=t("./mixin/Animatable"),a=t("./core/util"),o=function(t){n.call(this,t),i.call(this,t),r.call(this,t),this.id=t.id||e()};return o[Z]={type:"element",name:"",__zr:null,ignore:!1,clipPath:null,drift:function(t,e){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0}var i=this.transform;i||(i=this.transform=[1,0,0,1,0,0]),i[4]+=t,i[5]+=e,this.decomposeTransform(),this.dirty(!1)},beforeUpdate:function(){},afterUpdate:function(){},update:function(){this.updateTransform()},traverse:function(){},attrKV:function(t,e){if(t===W||"scale"===t||"origin"===t){if(e){var i=this[t];i||(i=this[t]=[]),i[0]=e[0],i[1]=e[1]}}else this[t]=e},hide:function(){this[E]=!0,this.__zr&&this.__zr.refresh()},show:function(){this[E]=!1,this.__zr&&this.__zr.refresh()},attr:function(t,e){if(typeof t===G)this.attrKV(t,e);else if(a[P](t))for(var i in t)t.hasOwnProperty(i)&&this.attrKV(i,t[i]);return this.dirty(!1),this},setClipPath:function(t){var e=this.__zr;e&&t.addSelfToZr(e),this.clipPath&&this.clipPath!==t&&this.removeClipPath(),this.clipPath=t,t.__zr=e,t.__clipTarget=this,this.dirty(!1)},removeClipPath:function(){var t=this.clipPath;t&&(t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__clipTarget=null,this.clipPath=null,this.dirty(!1))},addSelfToZr:function(t){this.__zr=t;var e=this.animators;if(e)for(var i=0;i<e[R];i++)t[N].addAnimator(e[i]);this.clipPath&&this.clipPath.addSelfToZr(t)},removeSelfFromZr:function(t){this.__zr=null;var e=this.animators;if(e)for(var i=0;i<e[R];i++)t[N].removeAnimator(e[i]);this.clipPath&&this.clipPath.removeSelfFromZr(t)}},a.mixin(o,r),a.mixin(o,n),a.mixin(o,i),o}),e("zrender/graphic/mixin/RectText",[le,"../../contain/text","../../core/BoundingRect"],function(t){function e(t,e){return typeof t===G?t.lastIndexOf("%")>=0?parseFloat(t)/100*e:parseFloat(t):t}var i=t("../../contain/text"),r=t("../../core/BoundingRect"),a=new r,o=function(){};return o[Z]={constructor:o,drawRectText:function(t,r,o){var s=this.style,l=s.text;if(null!=l&&(l+=""),l){t.save();var u,h,c=s.textPosition,d=s.textDistance,f=s[ee],p=s.textFont||s.font,m=s.textBaseline,v=s.textVerticalAlign;o=o||i[J](l,p,f,m);var g=this.transform;if(s.textTransform?this.setTransform(t):g&&(a.copy(r),a[n](g),r=a),c instanceof Array){if(u=r.x+e(c[0],r.width),h=r.y+e(c[1],r[K]),f=f||"left",m=m||"top",v){switch(v){case j:h-=o[K]/2-o.lineHeight/2;break;case U:h-=o[K]-o.lineHeight/2;break;default:h+=o.lineHeight/2}m=j}}else{var y=i.adjustTextPositionOnRect(c,r,o,d);u=y.x,h=y.y,f=f||y[ee],m=m||y.textBaseline}t[ee]=f||"left",t.textBaseline=m||"alphabetic";var _=s.textFill,x=s.textStroke;_&&(t.fillStyle=_),x&&(t.strokeStyle=x),t.font=p||"12px sans-serif",t.shadowBlur=s.textShadowBlur,t.shadowColor=s.textShadowColor||"transparent",t.shadowOffsetX=s.textShadowOffsetX,t.shadowOffsetY=s.textShadowOffsetY;var b=l.split("\n");s.textRotation&&(g&&t.translate(g[4],g[5]),t.rotate(s.textRotation),g&&t.translate(-g[4],-g[5]));for(var w=0;w<b[R];w++)_&&t.fillText(b[w],u,h),x&&t.strokeText(b[w],u,h),h+=o.lineHeight;t.restore()}}},o}),e("echarts/component/dataZoom/helper",[le,"../../util/format",se],function(t){var e=t("../../util/format"),i=t(se),n={},r=["x","y","z","radius","angle"];return n.createNameEach=function(t,n){t=t.slice();var r=i.map(t,e.capitalFirst);n=(n||[]).slice();var a=i.map(n,e.capitalFirst);return function(e,o){i.each(t,function(t,i){for(var s={name:t,capital:r[i]},l=0;l<n[R];l++)s[n[l]]=t+a[l];e.call(o,s)})}},n.eachAxisDim=n.createNameEach(r,["axisIndex","axis","index","id"]),n.createLinkedNodesFinder=function(t,e,n){function r(t,e){return i[O](e.nodes,t)>=0}function a(t,r){var a=!1;return e(function(e){i.each(n(t,e)||[],function(t){r.records[e.name][t]&&(a=!0)})}),a}function o(t,r){r.nodes.push(t),e(function(e){i.each(n(t,e)||[],function(t){r.records[e.name][t]=!0})})}return function(i){function n(t){!r(t,s)&&a(t,s)&&(o(t,s),l=!0)}var s={nodes:[],records:{}};if(e(function(t){s.records[t.name]={}}),!i)return s;o(i,s);var l;do l=!1,t(n);while(l);return s}},n}),e("echarts/component/dataZoom/AxisProxy",[le,se,"../../util/number"],function(t){function e(t,e){var i=[1/0,-1/0];return h(e,function(e){var n=e[D]();n&&h(e.coordDimToDataDim(t),function(t){var e=n.getDataExtent(t);e[0]<i[0]&&(i[0]=e[0]),e[1]>i[1]&&(i[1]=e[1])})},this),i}function i(t,e,i){return h(["min","max"],function(n,r){var a=e.get(n,!0);null!=a&&(a+"")[F]()!=="data"+n&&(t[r]=i.parse(a))}),e.get("scale",!0)||(t[0]>0&&(t[0]=0),t[1]<0&&(t[1]=0)),t}function n(t,e){var i=t.getAxisModel(),n=t._percentWindow,r=t._valueWindow;if(n){var o=e||0===n[0]&&100===n[1],s=!e&&a.getPixelPrecision(r,[0,500]),l=!(e||20>s&&s>=0),u=e||o||l;i.setRange&&i.setRange(u?null:+r[0].toFixed(s),u?null:+r[1].toFixed(s))}}var r=t(se),a=t("../../util/number"),h=r.each,c=a.asc,d=function(t,e,i,n){this._dimName=t,this._axisIndex=e,this._valueWindow,this._percentWindow,this._dataExtent,this[s]=n,this._dataZoomModel=i};return d[Z]={constructor:d,hostedBy:function(t){return this._dataZoomModel===t},getDataExtent:function(){return this._dataExtent.slice()},getDataValueWindow:function(){return this._valueWindow.slice()},getDataPercentWindow:function(){return this._percentWindow.slice()},getTargetSeriesModels:function(){var t=[],e=this[s];return e.eachSeries(function(i){var n=i.get(ae);if("cartesian2d"===n||"polar"===n){var r=this._dimName,a=e[o]({mainType:r+"Axis",index:i.get(r+"AxisIndex"),id:i.get(r+"AxisId")})[0];this._axisIndex===(a&&a.componentIndex)&&t.push(i)}},this),t},getAxisModel:function(){return this[s].getComponent(this._dimName+"Axis",this._axisIndex)},getOtherAxisModel:function(){var t,e,i=this._dimName,n=this[s],r=this.getAxisModel(),a="x"===i||"y"===i;a?(e="gridIndex",t="x"===i?"y":"x"):(e="polarIndex",t="angle"===i?"radius":"angle");var o;return n.eachComponent(t+"Axis",function(t){(t.get(e)||0)===(r.get(e)||0)&&(o=t)}),o},calculateDataWindow:function(t,e){var n=this.getAxisModel(),r=n.axis.scale,o=[0,100],s=[t.start,t.end],l=[];return e=e.slice(),i(e,n,r),h(["startValue","endValue"],function(e){l.push(null!=t[e]?r.parse(t[e]):null)}),h([0,1],function(t){var i=l[t],n=s[t];null!=n||null==i?(null==n&&(n=o[t]),i=r.parse(a.linearMap(n,o,e,!0))):n=a.linearMap(i,e,o,!0),l[t]=i,s[t]=n}),{valueWindow:c(l),percentWindow:c(s)}},reset:function(t){if(t===this._dataZoomModel){var i=this._dataExtent=e(this._dimName,this.getTargetSeriesModels()),r=this.calculateDataWindow(t[u],i);this._valueWindow=r.valueWindow,this._percentWindow=r.percentWindow,n(this)}},restore:function(t){t===this._dataZoomModel&&(this._valueWindow=this._percentWindow=null,n(this,!0))},filterData:function(t){function e(t){return t>=a[0]&&t<=a[1]}if(t===this._dataZoomModel){var i=this._dimName,n=this.getTargetSeriesModels(),r=t.get("filterMode"),a=this._valueWindow,o=this.getOtherAxisModel();t.get("$fromToolbox")&&o&&o.get("type")===l&&(r="empty"),h(n,function(t){var n=t[D]();n&&h(t.coordDimToDataDim(i),function(i){"empty"===r?t.setData(n.map(i,function(t){return e(t)?t:0/0})):n.filterSelf(i,e)})})}}},d}),e("zrender/core/guid",[],function(){var t=2311;return function(){return t++}}),e("zrender/mixin/Transformable",[le,"../core/matrix","../core/vector"],function(t){function e(t){return t>s||-s>t}var i=t("../core/matrix"),a=t("../core/vector"),o=i.identity,s=5e-5,l=function(t){t=t||{},t[W]||(this[W]=[0,0]),null==t.rotation&&(this.rotation=0),t.scale||(this.scale=[1,1]),this.origin=this.origin||null},u=l[Z];u.transform=null,u.needLocalTransform=function(){return e(this.rotation)||e(this[W][0])||e(this[W][1])||e(this.scale[0]-1)||e(this.scale[1]-1)},u.updateTransform=function(){var t=this[r],e=t&&t.transform,n=this.needLocalTransform(),a=this.transform;return n||e?(a=a||i[k](),n?this.getLocalTransform(a):o(a),e&&(n?i.mul(a,t.transform,a):i.copy(a,t.transform)),this.transform=a,this.invTransform=this.invTransform||i[k](),void i.invert(this.invTransform,a)):void(a&&o(a))},u.getLocalTransform=function(t){t=t||[],o(t);var e=this.origin,n=this.scale,r=this.rotation,a=this[W];return e&&(t[4]-=e[0],t[5]-=e[1]),i.scale(t,t,n),r&&i.rotate(t,t,r),e&&(t[4]+=e[0],t[5]+=e[1]),t[4]+=a[0],t[5]+=a[1],t},u.setTransform=function(t){var e=this.transform,i=t.dpr||1;e?t.setTransform(i*e[0],i*e[1],i*e[2],i*e[3],i*e[4],i*e[5]):t.setTransform(i,0,0,i,0,0)},u.restoreTransform=function(t){var e=(this.transform,t.dpr||1);t.setTransform(e,0,0,e,0,0)};var h=[];return u.decomposeTransform=function(){if(this.transform){var t=this[r],n=this.transform;t&&t.transform&&(i.mul(h,t.invTransform,n),n=h);var a=n[0]*n[0]+n[1]*n[1],o=n[2]*n[2]+n[3]*n[3],s=this[W],l=this.scale;e(a-1)&&(a=Math.sqrt(a)),e(o-1)&&(o=Math.sqrt(o)),n[0]<0&&(a=-a),n[3]<0&&(o=-o),s[0]=n[4],s[1]=n[5],l[0]=a,l[1]=o,this.rotation=Math.atan2(-n[1]/o,n[0]/a)}},u.getGlobalScale=function(){var t=this.transform;if(!t)return[1,1];var e=Math.sqrt(t[0]*t[0]+t[1]*t[1]),i=Math.sqrt(t[2]*t[2]+t[3]*t[3]);return t[0]<0&&(e=-e),t[3]<0&&(i=-i),[e,i]},u.transformCoordToLocal=function(t,e){var i=[t,e],r=this.invTransform;return r&&a[n](i,i,r),i},u.transformCoordToGlobal=function(t,e){var i=[t,e],r=this.transform;return r&&a[n](i,i,r),i},l}),e("zrender/mixin/Animatable",[le,"../animation/Animator","../core/util","../core/log"],function(t){var e=t("../animation/Animator"),i=t("../core/util"),n=i[g],r=i.isFunction,a=i[P],o=t("../core/log"),s=function(){this.animators=[]};return s[Z]={constructor:s,animate:function(t,n){var r,a=!1,s=this,l=this.__zr;if(t){var u=t.split("."),h=s;a="shape"===u[0];for(var c=0,d=u[R];d>c;c++)h&&(h=h[u[c]]);h&&(r=h)}else r=s;if(!r)return void o('Property "'+t+'" is not existed in element '+s.id);var f=s.animators,p=new e(r,n);return p.during(function(){s.dirty(a)}).done(function(){f[T](i[O](f,p),1)}),f.push(p),l&&l[N].addAnimator(p),p},stopAnimation:function(t){for(var e=this.animators,i=e[R],n=0;i>n;n++)e[n].stop(t);return e[R]=0,this},animateTo:function(t,e,i,a,o){function s(){u--,u||o&&o()}n(i)?(o=a,a=i,i=0):r(a)?(o=a,a="linear",i=0):r(i)?(o=i,i=0):r(e)?(o=e,e=500):e||(e=500),this.stopAnimation(),this._animateToShallow("",this,t,e,i,a,o);var l=this.animators.slice(),u=l[R];u||o&&o();for(var h=0;h<l[R];h++)l[h].done(s).start(a)},_animateToShallow:function(t,e,n,r,o){var s={},l=0;for(var u in n)if(n.hasOwnProperty(u))if(null!=e[u])a(n[u])&&!i.isArrayLike(n[u])?this._animateToShallow(t?t+"."+u:u,e[u],n[u],r,o):(s[u]=n[u],l++);else if(null!=n[u])if(t){var h={};h[t]={},h[t][u]=n[u],this.attr(h)}else this.attr(u,n[u]);return l>0&&this.animate(t,!1).when(null==r?500:r,s).delay(o||0),this}},s}),e("zrender/core/log",[le,"../config"],function(t){var e=t("../config");return function(){if(0!==e.debugMode)if(1==e.debugMode)for(var t in arguments)throw new Error(arguments[t]);else if(e.debugMode>1)for(var t in arguments)console.log(arguments[t])}}),e("zrender/animation/Animator",[le,"./Clip","../tool/color","../core/util"],function(t){function e(t,e){return t[e]}function i(t,e,i){t[e]=i}function n(t,e,i){return(e-t)*i+t}function r(t,e,i){return i>.5?e:t}function a(t,e,i,r,a){var o=t[R];if(1==a)for(var s=0;o>s;s++)r[s]=n(t[s],e[s],i);else for(var l=t[0][R],s=0;o>s;s++)for(var u=0;l>u;u++)r[s][u]=n(t[s][u],e[s][u],i)}function o(t,e,i){var n=t[R],r=e[R];if(n!==r){var a=n>r;if(a)t[R]=r;else for(var o=n;r>o;o++)t.push(1===i?e[o]:g.call(e[o]))}for(var s=t[0]&&t[0][R],o=0;o<t[R];o++)if(1===i)isNaN(t[o])&&(t[o]=e[o]);else for(var l=0;s>l;l++)isNaN(t[o][l])&&(t[o][l]=e[o][l])}function s(t,e,i){if(t===e)return!0;var n=t[R];if(n!==e[R])return!1;if(1===i){for(var r=0;n>r;r++)if(t[r]!==e[r])return!1}else for(var a=t[0][R],r=0;n>r;r++)for(var o=0;a>o;o++)if(t[r][o]!==e[r][o])return!1;return!0}function l(t,e,i,n,r,a,o,s,l){var h=t[R];if(1==l)for(var c=0;h>c;c++)s[c]=u(t[c],e[c],i[c],n[c],r,a,o);else for(var d=t[0][R],c=0;h>c;c++)for(var f=0;d>f;f++)s[c][f]=u(t[c][f],e[c][f],i[c][f],n[c][f],r,a,o)}function u(t,e,i,n,r,a,o){var s=.5*(i-t),l=.5*(n-e);return(2*(e-i)+s+l)*o+(-3*(e-i)-2*s-l)*a+s*r+e}function h(t){if(v(t)){var e=t[R];if(v(t[0])){for(var i=[],n=0;e>n;n++)i.push(g.call(t[n]));return i}return g.call(t)}return t}function c(t){return t[0]=Math.floor(t[0]),t[1]=Math.floor(t[1]),t[2]=Math.floor(t[2]),"rgba("+t.join(",")+")"}function d(t,e,i,h,d){var m=t._getter,g=t._setter,y="spline"===e,_=h[R];if(_){var x,b=h[0].value,w=v(b),T=!1,M=!1,A=w&&v(b[0])?2:1;h.sort(function(t,e){return t.time-e.time}),x=h[_-1].time;for(var C=[],S=[],P=h[0].value,I=!0,z=0;_>z;z++){C.push(h[z].time/x);var k=h[z].value;if(w&&s(k,P,A)||!w&&k===P||(I=!1),P=k,typeof k==G){var L=p.parse(k);L?(k=L,T=!0):M=!0}S.push(k)}if(!I){for(var D=S[_-1],z=0;_-1>z;z++)w?o(S[z],D,A):!isNaN(S[z])||isNaN(D)||M||T||(S[z]=D);w&&o(m(t._target,d),D,A);var O,E,B,N,Z,F,V=0,H=0;if(T)var q=[0,0,0,0];var W=function(t,e){var i;if(0>e)i=0;else if(H>e){for(O=Math.min(V+1,_-1),i=O;i>=0&&!(C[i]<=e);i--);i=Math.min(i,_-2)}else{for(i=V;_>i&&!(C[i]>e);i++);i=Math.min(i-1,_-2)}V=i,H=e;var o=C[i+1]-C[i];if(0!==o)if(E=(e-C[i])/o,y)if(N=S[i],B=S[0===i?i:i-1],Z=S[i>_-2?_-1:i+1],F=S[i>_-3?_-1:i+2],w)l(B,N,Z,F,E,E*E,E*E*E,m(t,d),A);else{var s;if(T)s=l(B,N,Z,F,E,E*E,E*E*E,q,1),s=c(q);else{if(M)return r(N,Z,E);s=u(B,N,Z,F,E,E*E,E*E*E)}g(t,d,s)}else if(w)a(S[i],S[i+1],E,m(t,d),A);else{var s;if(T)a(S[i],S[i+1],E,q,1),s=c(q);else{if(M)return r(S[i],S[i+1],E);s=n(S[i],S[i+1],E)}g(t,d,s)}},U=new f({target:t._target,life:x,loop:t._loop,delay:t._delay,onframe:W,ondestroy:i});return e&&"spline"!==e&&(U.easing=e),U}}}var f=t("./Clip"),p=t("../tool/color"),m=t("../core/util"),v=m.isArrayLike,g=Array[Z].slice,y=function(t,n,r,a){this._tracks={},this._target=t,this._loop=n||!1,this._getter=r||e,this._setter=a||i,this._clipCount=0,this._delay=0,this._doneList=[],this._onframeList=[],this._clipList=[]};return y[Z]={when:function(t,e){var i=this._tracks;for(var n in e)if(e.hasOwnProperty(n)){if(!i[n]){i[n]=[];var r=this._getter(this._target,n);if(null==r)continue;0!==t&&i[n].push({time:0,value:h(r)})}i[n].push({time:t,value:e[n]})}return this},during:function(t){return this._onframeList.push(t),this},_doneCallback:function(){this._tracks={},this._clipList[R]=0;for(var t=this._doneList,e=t[R],i=0;e>i;i++)t[i].call(this)},start:function(t){var e,i=this,n=0,r=function(){n--,n||i._doneCallback()};for(var a in this._tracks)if(this._tracks.hasOwnProperty(a)){var o=d(this,t,r,this._tracks[a],a);o&&(this._clipList.push(o),n++,this[N]&&this[N].addClip(o),e=o)}if(e){var s=e.onframe;e.onframe=function(t,e){s(t,e);for(var n=0;n<i._onframeList[R];n++)i._onframeList[n](t,e)}}return n||this._doneCallback(),this},stop:function(t){for(var e=this._clipList,i=this[N],n=0;n<e[R];n++){var r=e[n];t&&r.onframe(this._target,1),i&&i.removeClip(r)}e[R]=0},delay:function(t){return this._delay=t,this},done:function(t){return t&&this._doneList.push(t),this},getClips:function(){return this._clipList}},y}),e("zrender/animation/Clip",[le,"./easing"],function(t){function e(t){this._target=t[Q],this._life=t.life||1e3,this._delay=t.delay||0,this._initialized=!1,this.loop=null==t.loop?!1:t.loop,this.gap=t.gap||0,this.easing=t.easing||"Linear",this.onframe=t.onframe,this.ondestroy=t.ondestroy,this.onrestart=t.onrestart
}var i=t("./easing");return e[Z]={constructor:e,step:function(t){this._initialized||(this._startTime=t+this._delay,this._initialized=!0);var e=(t-this._startTime)/this._life;if(!(0>e)){e=Math.min(e,1);var n=this.easing,r=typeof n==G?i[n]:n,a=typeof r===y?r(e):e;return this.fire("frame",a),1==e?this.loop?(this.restart(t),"restart"):(this._needsRemove=!0,"destroy"):null}},restart:function(t){var e=(t-this._startTime)%this._life;this._startTime=t-e+this.gap,this._needsRemove=!1},fire:function(t,e){t="on"+t,this[t]&&this[t](this._target,e)}},e}),e("zrender/animation/easing",[],function(){var t={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return.5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(-Math.pow(2,-10*(t-1))+2)},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,i=.1,n=.4;return 0===t?0:1===t?1:(!i||1>i?(i=1,e=n/4):e=n*Math.asin(1/i)/(2*Math.PI),-(i*Math.pow(2,10*(t-=1))*Math.sin(2*(t-e)*Math.PI/n)))},elasticOut:function(t){var e,i=.1,n=.4;return 0===t?0:1===t?1:(!i||1>i?(i=1,e=n/4):e=n*Math.asin(1/i)/(2*Math.PI),i*Math.pow(2,-10*t)*Math.sin(2*(t-e)*Math.PI/n)+1)},elasticInOut:function(t){var e,i=.1,n=.4;return 0===t?0:1===t?1:(!i||1>i?(i=1,e=n/4):e=n*Math.asin(1/i)/(2*Math.PI),(t*=2)<1?-.5*i*Math.pow(2,10*(t-=1))*Math.sin(2*(t-e)*Math.PI/n):i*Math.pow(2,-10*(t-=1))*Math.sin(2*(t-e)*Math.PI/n)*.5+1)},backIn:function(t){var e=1.70158;return t*t*((e+1)*t-e)},backOut:function(t){var e=1.70158;return--t*t*((e+1)*t+e)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?.5*t*t*((e+1)*t-e):.5*((t-=2)*t*((e+1)*t+e)+2)},bounceIn:function(e){return 1-t.bounceOut(1-e)},bounceOut:function(t){return 1/2.75>t?7.5625*t*t:2/2.75>t?7.5625*(t-=1.5/2.75)*t+.75:2.5/2.75>t?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(e){return.5>e?.5*t.bounceIn(2*e):.5*t.bounceOut(2*e-1)+.5}};return t}),e("zrender/config",[],function(){var t=1;typeof window!==i&&(t=Math.max(window.devicePixelRatio||1,1));var e={debugMode:0,devicePixelRatio:t};return e}),e("zrender/core/curve",[le,"./vector"],function(t){function e(t){return t>-x&&x>t}function i(t){return t>x||-x>t}function n(t,e,i,n,r){var a=1-r;return a*a*(a*t+3*r*e)+r*r*(r*n+3*a*i)}function r(t,e,i,n,r){var a=1-r;return 3*(((e-t)*a+2*(i-e)*r)*a+(n-i)*r*r)}function a(t,i,n,r,a,o){var s=r+3*(i-n)-t,l=3*(n-2*i+t),u=3*(i-t),h=t-a,c=l*l-3*s*u,d=l*u-9*s*h,f=u*u-3*l*h,p=0;if(e(c)&&e(d))if(e(l))o[0]=0;else{var m=-u/l;m>=0&&1>=m&&(o[p++]=m)}else{var v=d*d-4*c*f;if(e(v)){var g=d/c,m=-l/s+g,x=-g/2;m>=0&&1>=m&&(o[p++]=m),x>=0&&1>=x&&(o[p++]=x)}else if(v>0){var b=_(v),M=c*l+1.5*s*(-d+b),A=c*l+1.5*s*(-d-b);M=0>M?-y(-M,T):y(M,T),A=0>A?-y(-A,T):y(A,T);var m=(-l-(M+A))/(3*s);m>=0&&1>=m&&(o[p++]=m)}else{var C=(2*c*l-3*s*d)/(2*_(c*c*c)),S=Math.acos(C)/3,P=_(c),I=Math.cos(S),m=(-l-2*P*I)/(3*s),x=(-l+P*(I+w*Math.sin(S)))/(3*s),z=(-l+P*(I-w*Math.sin(S)))/(3*s);m>=0&&1>=m&&(o[p++]=m),x>=0&&1>=x&&(o[p++]=x),z>=0&&1>=z&&(o[p++]=z)}}return p}function o(t,n,r,a,o){var s=6*r-12*n+6*t,l=9*n+3*a-3*t-9*r,u=3*n-3*t,h=0;if(e(l)){if(i(s)){var c=-u/s;c>=0&&1>=c&&(o[h++]=c)}}else{var d=s*s-4*l*u;if(e(d))o[0]=-s/(2*l);else if(d>0){var f=_(d),c=(-s+f)/(2*l),p=(-s-f)/(2*l);c>=0&&1>=c&&(o[h++]=c),p>=0&&1>=p&&(o[h++]=p)}}return h}function s(t,e,i,n,r,a){var o=(e-t)*r+t,s=(i-e)*r+e,l=(n-i)*r+i,u=(s-o)*r+o,h=(l-s)*r+s,c=(h-u)*r+u;a[0]=t,a[1]=o,a[2]=u,a[3]=c,a[4]=c,a[5]=h,a[6]=l,a[7]=n}function l(t,e,i,r,a,o,s,l,u,h,c){var d,f,p,m,v,y=.005,x=1/0;M[0]=u,M[1]=h;for(var w=0;1>w;w+=.05)A[0]=n(t,i,a,s,w),A[1]=n(e,r,o,l,w),m=g(M,A),x>m&&(d=w,x=m);x=1/0;for(var T=0;32>T&&!(b>y);T++)f=d-y,p=d+y,A[0]=n(t,i,a,s,f),A[1]=n(e,r,o,l,f),m=g(A,M),f>=0&&x>m?(d=f,x=m):(C[0]=n(t,i,a,s,p),C[1]=n(e,r,o,l,p),v=g(C,M),1>=p&&x>v?(d=p,x=v):y*=.5);return c&&(c[0]=n(t,i,a,s,d),c[1]=n(e,r,o,l,d)),_(x)}function u(t,e,i,n){var r=1-n;return r*(r*t+2*n*e)+n*n*i}function h(t,e,i,n){return 2*((1-n)*(e-t)+n*(i-e))}function c(t,n,r,a,o){var s=t-2*n+r,l=2*(n-t),u=t-a,h=0;if(e(s)){if(i(l)){var c=-u/l;c>=0&&1>=c&&(o[h++]=c)}}else{var d=l*l-4*s*u;if(e(d)){var c=-l/(2*s);c>=0&&1>=c&&(o[h++]=c)}else if(d>0){var f=_(d),c=(-l+f)/(2*s),p=(-l-f)/(2*s);c>=0&&1>=c&&(o[h++]=c),p>=0&&1>=p&&(o[h++]=p)}}return h}function d(t,e,i){var n=t+i-2*e;return 0===n?.5:(t-e)/n}function f(t,e,i,n,r){var a=(e-t)*n+t,o=(i-e)*n+e,s=(o-a)*n+a;r[0]=t,r[1]=a,r[2]=s,r[3]=s,r[4]=o,r[5]=i}function p(t,e,i,n,r,a,o,s,l){var h,c=.005,d=1/0;M[0]=o,M[1]=s;for(var f=0;1>f;f+=.05){A[0]=u(t,i,r,f),A[1]=u(e,n,a,f);var p=g(M,A);d>p&&(h=f,d=p)}d=1/0;for(var m=0;32>m&&!(b>c);m++){var v=h-c,y=h+c;A[0]=u(t,i,r,v),A[1]=u(e,n,a,v);var p=g(A,M);if(v>=0&&d>p)h=v,d=p;else{C[0]=u(t,i,r,y),C[1]=u(e,n,a,y);var x=g(C,M);1>=y&&d>x?(h=y,d=x):c*=.5}}return l&&(l[0]=u(t,i,r,h),l[1]=u(e,n,a,h)),_(d)}var m=t("./vector"),v=m[k],g=m.distSquare,y=Math.pow,_=Math.sqrt,x=1e-8,b=1e-4,w=_(3),T=1/3,M=v(),A=v(),C=v();return{cubicAt:n,cubicDerivativeAt:r,cubicRootAt:a,cubicExtrema:o,cubicSubdivide:s,cubicProjectPoint:l,quadraticAt:u,quadraticDerivativeAt:h,quadraticRootAt:c,quadraticExtremum:d,quadraticSubdivide:f,quadraticProjectPoint:p}}),e("zrender/core/bbox",[le,"./vector","./curve"],function(t){var e=t("./vector"),i=t("./curve"),n={},r=Math.min,a=Math.max,o=Math.sin,s=Math.cos,l=e[k](),u=e[k](),h=e[k](),c=2*Math.PI;n.fromPoints=function(t,e,i){if(0!==t[R]){var n,o=t[0],s=o[0],l=o[0],u=o[1],h=o[1];for(n=1;n<t[R];n++)o=t[n],s=r(s,o[0]),l=a(l,o[0]),u=r(u,o[1]),h=a(h,o[1]);e[0]=s,e[1]=u,i[0]=l,i[1]=h}},n.fromLine=function(t,e,i,n,o,s){o[0]=r(t,i),o[1]=r(e,n),s[0]=a(t,i),s[1]=a(e,n)};var d=[],f=[];return n.fromCubic=function(t,e,n,o,s,l,u,h,c,p){var m,v=i.cubicExtrema,g=i.cubicAt,y=v(t,n,s,u,d);for(c[0]=1/0,c[1]=1/0,p[0]=-1/0,p[1]=-1/0,m=0;y>m;m++){var _=g(t,n,s,u,d[m]);c[0]=r(_,c[0]),p[0]=a(_,p[0])}for(y=v(e,o,l,h,f),m=0;y>m;m++){var x=g(e,o,l,h,f[m]);c[1]=r(x,c[1]),p[1]=a(x,p[1])}c[0]=r(t,c[0]),p[0]=a(t,p[0]),c[0]=r(u,c[0]),p[0]=a(u,p[0]),c[1]=r(e,c[1]),p[1]=a(e,p[1]),c[1]=r(h,c[1]),p[1]=a(h,p[1])},n.fromQuadratic=function(t,e,n,o,s,l,u,h){var c=i.quadraticExtremum,d=i.quadraticAt,f=a(r(c(t,n,s),1),0),p=a(r(c(e,o,l),1),0),m=d(t,n,s,f),v=d(e,o,l,p);u[0]=r(t,s,m),u[1]=r(e,l,v),h[0]=a(t,s,m),h[1]=a(e,l,v)},n.fromArc=function(t,i,n,r,a,d,f,p,m){var v=e.min,g=e.max,y=Math.abs(a-d);if(1e-4>y%c&&y>1e-4)return p[0]=t-n,p[1]=i-r,m[0]=t+n,void(m[1]=i+r);if(l[0]=s(a)*n+t,l[1]=o(a)*r+i,u[0]=s(d)*n+t,u[1]=o(d)*r+i,v(p,l,u),g(m,l,u),a%=c,0>a&&(a+=c),d%=c,0>d&&(d+=c),a>d&&!f?d+=c:d>a&&f&&(a+=c),f){var _=d;d=a,a=_}for(var x=0;d>x;x+=Math.PI/2)x>a&&(h[0]=s(x)*n+t,h[1]=o(x)*r+i,v(p,h,p),g(m,h,m))},n}),e("zrender/contain/line",[],function(){return{containStroke:function(t,e,i,n,r,a,o){if(0===r)return!1;var s=r,l=0,u=t;if(o>e+s&&o>n+s||e-s>o&&n-s>o||a>t+s&&a>i+s||t-s>a&&i-s>a)return!1;if(t===i)return Math.abs(a-t)<=s/2;l=(e-n)/(t-i),u=(t*n-i*e)/(t-i);var h=l*a-o+u,c=h*h/(l*l+1);return s/2*s/2>=c}}}),e("zrender/contain/util",[le],function(){var t=2*Math.PI;return{normalizeRadian:function(e){return e%=t,0>e&&(e+=t),e}}}),e("zrender/contain/windingLine",[],function(){return function(t,e,i,n,r,a){if(a>e&&a>n||e>a&&n>a)return 0;if(n===e)return 0;var o=e>n?1:-1,s=(a-e)/(n-e);(1===s||0===s)&&(o=e>n?.5:-.5);var l=s*(i-t)+t;return l>r?o:0}}),e("zrender/contain/cubic",[le,"../core/curve"],function(t){var e=t("../core/curve");return{containStroke:function(t,i,n,r,a,o,s,l,u,h,c){if(0===u)return!1;var d=u;if(c>i+d&&c>r+d&&c>o+d&&c>l+d||i-d>c&&r-d>c&&o-d>c&&l-d>c||h>t+d&&h>n+d&&h>a+d&&h>s+d||t-d>h&&n-d>h&&a-d>h&&s-d>h)return!1;var f=e.cubicProjectPoint(t,i,n,r,a,o,s,l,h,c,null);return d/2>=f}}}),e("zrender/contain/quadratic",[le,"../core/curve"],function(t){var e=t("../core/curve");return{containStroke:function(t,i,n,r,a,o,s,l,u){if(0===s)return!1;var h=s;if(u>i+h&&u>r+h&&u>o+h||i-h>u&&r-h>u&&o-h>u||l>t+h&&l>n+h&&l>a+h||t-h>l&&n-h>l&&a-h>l)return!1;var c=e.quadraticProjectPoint(t,i,n,r,a,o,l,u,null);return h/2>=c}}}),e("zrender/contain/arc",[le,"./util"],function(t){var e=t("./util").normalizeRadian,i=2*Math.PI;return{containStroke:function(t,n,r,a,o,s,l,u,h){if(0===l)return!1;var c=l;u-=t,h-=n;var d=Math.sqrt(u*u+h*h);if(d-c>r||r>d+c)return!1;if(Math.abs(a-o)%i<1e-4)return!0;if(s){var f=a;a=e(o),o=e(f)}else a=e(a),o=e(o);a>o&&(o+=i);var p=Math.atan2(h,u);return 0>p&&(p+=i),p>=a&&o>=p||p+i>=a&&o>=p+i}}}),e("zrender/core/LRU",[le],function(){var t=function(){this.head=null,this.tail=null,this._len=0},e=t[Z];e.insert=function(t){var e=new i(t);return this.insertEntry(e),e},e.insertEntry=function(t){this.head?(this.tail.next=t,t.prev=this.tail,this.tail=t):this.head=this.tail=t,this._len++},e[S]=function(t){var e=t.prev,i=t.next;e?e.next=i:this.head=i,i?i.prev=e:this.tail=e,t.next=t.prev=null,this._len--},e.len=function(){return this._len};var i=function(t){this.value=t,this.next,this.prev},n=function(e){this._list=new t,this._map={},this._maxSize=e||10},r=n[Z];return r.put=function(t,e){var i=this._list,n=this._map;if(null==n[t]){var r=i.len();if(r>=this._maxSize&&r>0){var a=i.head;i[S](a),delete n[a.key]}var o=i.insert(e);o.key=t,n[t]=o}},r.get=function(t){var e=this._map[t],i=this._list;return null!=e?(e!==i.tail&&(i[S](e),i.insertEntry(e)),e.value):void 0},r.clear=function(){this._list.clear(),this._map={}},n}),e("zrender/graphic/helper/poly",[le,"./smoothSpline","./smoothBezier"],function(t){var e=t("./smoothSpline"),i=t("./smoothBezier");return{buildPath:function(t,n,r){var a=n.points,o=n.smooth;if(a&&a[R]>=2){if(o&&"spline"!==o){var s=i(a,o,r,n.smoothConstraint);t.moveTo(a[0][0],a[0][1]);for(var l=a[R],u=0;(r?l:l-1)>u;u++){var h=s[2*u],c=s[2*u+1],d=a[(u+1)%l];t.bezierCurveTo(h[0],h[1],c[0],c[1],d[0],d[1])}}else{"spline"===o&&(a=e(a,r)),t.moveTo(a[0][0],a[0][1]);for(var u=1,f=a[R];f>u;u++)t.lineTo(a[u][0],a[u][1])}r&&t.closePath()}}}}),e("zrender/graphic/helper/smoothSpline",[le,"../../core/vector"],function(t){function e(t,e,i,n,r,a,o){var s=.5*(i-t),l=.5*(n-e);return(2*(e-i)+s+l)*o+(-3*(e-i)-2*s-l)*a+s*r+e}var i=t("../../core/vector");return function(t,n){for(var r=t[R],a=[],o=0,s=1;r>s;s++)o+=i.distance(t[s-1],t[s]);var l=o/2;l=r>l?r:l;for(var s=0;l>s;s++){var u,h,c,d=s/(l-1)*(n?r:r-1),f=Math.floor(d),p=d-f,m=t[f%r];n?(u=t[(f-1+r)%r],h=t[(f+1)%r],c=t[(f+2)%r]):(u=t[0===f?f:f-1],h=t[f>r-2?r-1:f+1],c=t[f>r-3?r-1:f+2]);var v=p*p,g=p*v;a.push([e(u[0],m[0],h[0],c[0],p,v,g),e(u[1],m[1],h[1],c[1],p,v,g)])}return a}}),e("zrender/graphic/helper/smoothBezier",[le,"../../core/vector"],function(t){var e=t("../../core/vector"),i=e.min,n=e.max,r=e.scale,a=e.distance,o=e.add;return function(t,s,l,u){var h,c,d,f,p=[],m=[],v=[],g=[];if(u){d=[1/0,1/0],f=[-1/0,-1/0];for(var y=0,_=t[R];_>y;y++)i(d,d,t[y]),n(f,f,t[y]);i(d,d,u[0]),n(f,f,u[1])}for(var y=0,_=t[R];_>y;y++){var x=t[y];if(l)h=t[y?y-1:_-1],c=t[(y+1)%_];else{if(0===y||y===_-1){p.push(e.clone(t[y]));continue}h=t[y-1],c=t[y+1]}e.sub(m,c,h),r(m,m,s);var b=a(x,h),w=a(x,c),T=b+w;0!==T&&(b/=T,w/=T),r(v,m,-b),r(g,m,w);var M=o([],x,v),A=o([],x,g);u&&(n(M,M,d),i(M,M,f),n(A,A,d),i(A,A,f)),p.push(M),p.push(A)}return l&&p.push(p.shift()),p}}),e("zrender/graphic/helper/roundRect",[le],function(){return{buildPath:function(t,e){var i,n,r,a,o=e.x,s=e.y,l=e.width,u=e[K],h=e.r;0>l&&(o+=l,l=-l),0>u&&(s+=u,u=-u),"number"==typeof h?i=n=r=a=h:h instanceof Array?1===h[R]?i=n=r=a=h[0]:2===h[R]?(i=r=h[0],n=a=h[1]):3===h[R]?(i=h[0],n=a=h[1],r=h[2]):(i=h[0],n=h[1],r=h[2],a=h[3]):i=n=r=a=0;var c;i+n>l&&(c=i+n,i*=l/c,n*=l/c),r+a>l&&(c=r+a,r*=l/c,a*=l/c),n+r>u&&(c=n+r,n*=u/c,r*=u/c),i+a>u&&(c=i+a,i*=u/c,a*=u/c),t.moveTo(o+i,s),t.lineTo(o+l-n,s),0!==n&&t.quadraticCurveTo(o+l,s,o+l,s+n),t.lineTo(o+l,s+u-r),0!==r&&t.quadraticCurveTo(o+l,s+u,o+l-r,s+u),t.lineTo(o+a,s+u),0!==a&&t.quadraticCurveTo(o,s+u,o,s+u-a),t.lineTo(o,s+i),0!==i&&t.quadraticCurveTo(o,s,o+i,s)}}}),e("zrender/graphic/Gradient",[le],function(){var t=function(t){this[I]=t||[]};return t[Z]={constructor:t,addColorStop:function(t,e){this[I].push({offset:t,color:e})}},t}),e("zrender/Handler",[le,"./core/util","./mixin/Draggable","./mixin/Eventful"],function(t){function e(t,e,i){return{type:t,event:i,target:e,cancelBubble:!1,offsetX:i.zrX,offsetY:i.zrY,gestureEvent:i.gestureEvent,pinchX:i.pinchX,pinchY:i.pinchY,pinchScale:i.pinchScale,wheelDelta:i.zrDelta,zrByTouch:i.zrByTouch}}function i(){}function n(t,e,i){if(t[t.rectHover?"rectContain":v](e,i)){for(var n=t;n;){if(n.silent||n.clipPath&&!n.clipPath[v](e,i))return!1;n=n[r]}return!0}return!1}var a=t("./core/util"),o=t("./mixin/Draggable"),s=t("./mixin/Eventful");i[Z][M]=function(){};var l=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],u=function(t,e,n,r){s.call(this),this.storage=t,this.painter=e,this.painterRoot=r,n=n||new i,this.proxy=n,n.handler=this,this._hovered,this._lastTouchMoment,this._lastX,this._lastY,o.call(this),a.each(l,function(t){n.on&&n.on(t,this[t],this)},this)};return u[Z]={constructor:u,mousemove:function(t){var e=t.zrX,i=t.zrY,n=this.findHover(e,i,null),r=this._hovered,a=this.proxy;this._hovered=n,a.setCursor&&a.setCursor(n?n.cursor:"default"),r&&n!==r&&r.__zr&&this.dispatchToElement(r,"mouseout",t),this.dispatchToElement(n,"mousemove",t),n&&n!==r&&this.dispatchToElement(n,"mouseover",t)},mouseout:function(t){this.dispatchToElement(this._hovered,"mouseout",t);var e,i=t.toElement||t.relatedTarget;do i=i&&i.parentNode;while(i&&9!=i.nodeType&&!(e=i===this.painterRoot));!e&&this.trigger("globalout",{event:t})},resize:function(){this._hovered=null},dispatch:function(t,e){var i=this[t];i&&i.call(this,e)},dispose:function(){this.proxy[M](),this.storage=this.proxy=this.painter=null},setCursorStyle:function(t){var e=this.proxy;e.setCursor&&e.setCursor(t)},dispatchToElement:function(t,i,n){for(var a="on"+i,o=e(i,t,n),s=t;s&&(s[a]&&(o.cancelBubble=s[a].call(s,o)),s.trigger(i,o),s=s[r],!o.cancelBubble););o.cancelBubble||(this.trigger(i,o),this.painter&&this.painter.eachOtherLayer(function(t){typeof t[a]==y&&t[a].call(t,o),t.trigger&&t.trigger(i,o)}))},findHover:function(t,e,i){for(var r=this.storage.getDisplayList(),a=r[R]-1;a>=0;a--)if(!r[a].silent&&r[a]!==i&&!r[a][E]&&n(r[a],t,e))return r[a]}},a.each(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(t){u[Z][t]=function(e){var i=this.findHover(e.zrX,e.zrY,null);if("mousedown"===t)this._downel=i,this._upel=i;else if("mosueup"===t)this._upel=i;else if("click"===t&&this._downel!==this._upel)return;this.dispatchToElement(i,t,e)}}),a.mixin(u,s),a.mixin(u,o),u}),e("zrender/Storage",[le,"./core/util","./core/env","./container/Group","./core/timsort"],function(t){function e(t,e){return t[b]===e[b]?t.z===e.z?t.z2-e.z2:t.z-e.z:t[b]-e[b]}var i=t("./core/util"),n=t("./core/env"),a=t("./container/Group"),o=t("./core/timsort"),s=function(){this._elements={},this._roots=[],this._displayList=[],this._displayListLen=0};return s[Z]={constructor:s,traverse:function(t,e){for(var i=0;i<this._roots[R];i++)this._roots[i][w](t,e)},getDisplayList:function(t,e){return e=e||!1,t&&this.updateDisplayList(e),this._displayList},updateDisplayList:function(t){this._displayListLen=0;for(var i=this._roots,r=this._displayList,a=0,s=i[R];s>a;a++)this._updateAndAddDisplayable(i[a],null,t);r[R]=this._displayListLen,n[B]&&o(r,e)},_updateAndAddDisplayable:function(t,e,i){if(!t[E]||i){t.beforeUpdate(),t.__dirty&&t[z](),t.afterUpdate();var n=t.clipPath;if(n&&(n[r]=t,n.updateTransform(),e?(e=e.slice(),e.push(n)):e=[n]),t.isGroup){for(var a=t._children,o=0;o<a[R];o++){var s=a[o];t.__dirty&&(s.__dirty=!0),this._updateAndAddDisplayable(s,e,i)}t.__dirty=!1}else t.__clipPaths=e,this._displayList[this._displayListLen++]=t}},addRoot:function(t){this._elements[t.id]||(t instanceof a&&t.addChildrenToStorage(this),this.addToMap(t),this._roots.push(t))},delRoot:function(t){if(null==t){for(var e=0;e<this._roots[R];e++){var n=this._roots[e];n instanceof a&&n.delChildrenFromStorage(this)}return this._elements={},this._roots=[],this._displayList=[],void(this._displayListLen=0)}if(t instanceof Array)for(var e=0,r=t[R];r>e;e++)this.delRoot(t[e]);else{var o;o=typeof t==G?this._elements[t]:t;var s=i[O](this._roots,o);s>=0&&(this.delFromMap(o.id),this._roots[T](s,1),o instanceof a&&o.delChildrenFromStorage(this))}},addToMap:function(t){return t instanceof a&&(t.__storage=this),t.dirty(!1),this._elements[t.id]=t,this},get:function(t){return this._elements[t]},delFromMap:function(t){var e=this._elements,i=e[t];return i&&(delete e[t],i instanceof a&&(i.__storage=null)),this},dispose:function(){this._elements=this._renderList=this._roots=null},displayableSortFunc:e},s}),e("zrender/animation/Animation",[le,"../core/util","../core/event","./requestAnimationFrame","./Animator"],function(t){var e=t("../core/util"),i=t("../core/event").Dispatcher,n=t("./requestAnimationFrame"),r=t("./Animator"),a=function(t){t=t||{},this.stage=t.stage||{},this.onframe=t.onframe||function(){},this._clips=[],this._running=!1,this._time,this._pausedTime,this._pauseStart,this._paused=!1,i.call(this)};return a[Z]={constructor:a,addClip:function(t){this._clips.push(t)},addAnimator:function(t){t[N]=this;for(var e=t.getClips(),i=0;i<e[R];i++)this.addClip(e[i])},removeClip:function(t){var i=e[O](this._clips,t);i>=0&&this._clips[T](i,1)},removeAnimator:function(t){for(var e=t.getClips(),i=0;i<e[R];i++)this.removeClip(e[i]);t[N]=null},_update:function(){for(var t=(new Date).getTime()-this._pausedTime,e=t-this._time,i=this._clips,n=i[R],r=[],a=[],o=0;n>o;o++){var s=i[o],l=s.step(t);l&&(r.push(l),a.push(s))}for(var o=0;n>o;)i[o]._needsRemove?(i[o]=i[n-1],i.pop(),n--):o++;n=r[R];for(var o=0;n>o;o++)a[o].fire(r[o]);this._time=t,this.onframe(e),this.trigger("frame",e),this.stage[z]&&this.stage[z]()},_startLoop:function(){function t(){e._running&&(n(t),!e._paused&&e._update())}var e=this;this._running=!0,n(t)},start:function(){this._time=(new Date).getTime(),this._pausedTime=0,this._startLoop()},stop:function(){this._running=!1},pause:function(){this._paused||(this._pauseStart=(new Date).getTime(),this._paused=!0)},resume:function(){this._paused&&(this._pausedTime+=(new Date).getTime()-this._pauseStart,this._paused=!1)},clear:function(){this._clips=[]},animate:function(t,e){e=e||{};var i=new r(t,e.loop,e.getter,e.setter);return i}},e.mixin(a,i),a}),e("zrender/dom/HandlerProxy",[le,"../core/event","../core/util","../mixin/Eventful","../core/env","../core/GestureMgr"],function(t){function e(t){return"mousewheel"===t&&h.browser.firefox?"DOMMouseScroll":t}function i(t,e,i){var n=t._gestureMgr;"start"===i&&n.clear();var r=n.recognize(e,t.handler.findHover(e.zrX,e.zrY,null),t.dom);if("end"===i&&n.clear(),r){var a=r.type;e.gestureEvent=a,t.handler.dispatchToElement(r[Q],a,r.event)}}function n(t){t._touching=!0,clearTimeout(t._touchTimer),t._touchTimer=setTimeout(function(){t._touching=!1},700)}function r(){return h.touchEventsSupported}function a(t){function e(t,e){return function(){return e._touching?void 0:t.apply(e,arguments)}}for(var i=0;i<g[R];i++){var n=g[i];t._handlers[n]=l.bind(y[n],t)}for(var i=0;i<v[R];i++){var n=v[i];t._handlers[n]=e(y[n],t)}}function o(t){function i(i,n){l.each(i,function(i){d(t,e(i),n._handlers[i])},n)}u.call(this),this.dom=t,this._touching=!1,this._touchTimer,this._gestureMgr=new c,this._handlers={},a(this),r()&&i(g,this),i(v,this)}var s=t("../core/event"),l=t("../core/util"),u=t("../mixin/Eventful"),h=t("../core/env"),c=t("../core/GestureMgr"),d=s.addEventListener,f=s.removeEventListener,p=s.normalizeEvent,m=300,v=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],g=["touchstart","touchend","touchmove"],y={mousemove:function(t){t=p(this.dom,t),this.trigger("mousemove",t)},mouseout:function(t){t=p(this.dom,t);var e=t.toElement||t.relatedTarget;if(e!=this.dom)for(;e&&9!=e.nodeType;){if(e===this.dom)return;e=e.parentNode}this.trigger("mouseout",t)},touchstart:function(t){t=p(this.dom,t),t.zrByTouch=!0,this._lastTouchMoment=new Date,i(this,t,"start"),y.mousemove.call(this,t),y.mousedown.call(this,t),n(this)},touchmove:function(t){t=p(this.dom,t),t.zrByTouch=!0,i(this,t,"change"),y.mousemove.call(this,t),n(this)},touchend:function(t){t=p(this.dom,t),t.zrByTouch=!0,i(this,t,"end"),y.mouseup.call(this,t),+new Date-this._lastTouchMoment<m&&y.click.call(this,t),n(this)}};l.each(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(t){y[t]=function(e){e=p(this.dom,e),this.trigger(t,e)}});var _=o[Z];return _[M]=function(){for(var t=v.concat(g),i=0;i<t[R];i++){var n=t[i];f(this.dom,e(n),this._handlers[n])}},_.setCursor=function(t){this.dom.style.cursor=t||"default"},l.mixin(o,u),o}),e("zrender/mixin/Draggable",[le],function(){function t(){this.on("mousedown",this._dragStart,this),this.on("mousemove",this._drag,this),this.on("mouseup",this._dragEnd,this),this.on("globalout",this._dragEnd,this)}return t[Z]={constructor:t,_dragStart:function(t){var e=t[Q];e&&e.draggable&&(this._draggingTarget=e,e.dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.dispatchToElement(e,"dragstart",t.event))},_drag:function(t){var e=this._draggingTarget;if(e){var i=t.offsetX,n=t.offsetY,r=i-this._x,a=n-this._y;this._x=i,this._y=n,e.drift(r,a,t),this.dispatchToElement(e,"drag",t.event);var o=this.findHover(i,n,e),s=this._dropTarget;this._dropTarget=o,e!==o&&(s&&o!==s&&this.dispatchToElement(s,"dragleave",t.event),o&&o!==s&&this.dispatchToElement(o,"dragenter",t.event))}},_dragEnd:function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.dispatchToElement(e,"dragend",t.event),this._dropTarget&&this.dispatchToElement(this._dropTarget,"drop",t.event),this._draggingTarget=null,this._dropTarget=null}},t}),e("zrender/Painter",[le,"./config","./core/util","./core/log","./core/BoundingRect","./core/timsort","./Layer","./animation/requestAnimationFrame","./graphic/Image"],function(t){function e(t){return parseInt(t,10)}function i(t){return t?t.isBuildin?!0:typeof t.resize!==y||typeof t.refresh!==y?!1:!0:!1}function r(t){t.__unusedCount++}function a(t){1==t.__unusedCount&&t.clear()}function o(t,e,i){return _.copy(t[J]()),t.transform&&_[n](t.transform),x.width=e,x[K]=i,!_.intersect(x)}function s(t,e){if(t==e)return!1;if(!t||!e||t[R]!==e[R])return!0;for(var i=0;i<t[R];i++)if(t[i]!==e[i])return!0}function l(t,e){for(var i=0;i<t[R];i++){var n=t[i],r=n.path;n.setTransform(e),r.beginPath(e),n.buildPath(r,n.shape),e.clip(),n.restoreTransform(e)}}function u(t,e){var i=document.createElement("div");return i.style.cssText=["position:relative","overflow:hidden","width:"+t+"px","height:"+e+"px","padding:0","margin:0","border-width:0"].join(";")+";",i}var h=t("./config"),c=t("./core/util"),d=t("./core/log"),f=t("./core/BoundingRect"),p=t("./core/timsort"),m=t("./Layer"),v=t("./animation/requestAnimationFrame"),g=5,_=new f(0,0,0,0),x=new f(0,0,0,0),w=function(t,e,i){var n=!t.nodeName||"CANVAS"===t.nodeName.toUpperCase();this._opts=i=c[C]({},i||{}),this.dpr=i.devicePixelRatio||h.devicePixelRatio,this._singleCanvas=n,this.root=t;var r=t.style;r&&(r["-webkit-tap-highlight-color"]="transparent",r["-webkit-user-select"]=r["user-select"]=r["-webkit-touch-callout"]="none",t.innerHTML=""),this.storage=e;var a=this._zlevelList=[],o=this._layers={};if(this._layerConfig={},n){var s=t.width,l=t[K];this._width=s,this._height=l;var d=new m(t,this,1);d.initContext(),o[0]=d,a.push(0)}else{this._width=this._getSize(0),this._height=this._getSize(1);var f=this._domRoot=u(this._width,this._height);t.appendChild(f)}this.pathToImage=this._createPathToImage(),this._progressiveLayers=[],this._hoverlayer,this._hoverElements=[]};return w[Z]={constructor:w,isSingleCanvas:function(){return this._singleCanvas},getViewportRoot:function(){return this._singleCanvas?this._layers[0].dom:this._domRoot},refresh:function(t){var e=this.storage.getDisplayList(!0),i=this._zlevelList;this._paintList(e,t);for(var n=0;n<i[R];n++){var r=i[n],a=this._layers[r];!a.isBuildin&&a.refresh&&a.refresh()}return this.refreshHover(),this._progressiveLayers[R]&&this._startProgessive(),this},addHover:function(t,e){if(!t.__hoverMir){var i=new t.constructor({style:t.style,shape:t.shape});i.__from=t,t.__hoverMir=i,i[q](e),this._hoverElements.push(i)}},removeHover:function(t){var e=t.__hoverMir,i=this._hoverElements,n=c[O](i,e);n>=0&&i[T](n,1),t.__hoverMir=null},clearHover:function(){for(var t=this._hoverElements,e=0;e<t[R];e++){var i=t[e].__from;i&&(i.__hoverMir=null)}t[R]=0},refreshHover:function(){var t=this._hoverElements,e=t[R],i=this._hoverlayer;if(i&&i.clear(),e){p(t,this.storage.displayableSortFunc),i||(i=this._hoverlayer=this.getLayer(1e5));var n={};i.ctx.save();for(var r=0;e>r;){var a=t[r],o=a.__from;o&&o.__zr?(r++,o.invisible||(a.transform=o.transform,a.invTransform=o.invTransform,a.__clipPaths=o.__clipPaths,this._doPaintEl(a,i,!0,n))):(t[T](r,1),o.__hoverMir=null,e--)}i.ctx.restore()}},_startProgessive:function(){function t(){i===e._progressiveToken&&e.storage&&(e._doPaintList(e.storage.getDisplayList()),e._furtherProgressive?(e._progress++,v(t)):e._progressiveToken=-1)}var e=this;if(e._furtherProgressive){var i=e._progressiveToken=+new Date;e._progress++,v(t)}},_clearProgressive:function(){this._progressiveToken=-1,this._progress=0,c.each(this._progressiveLayers,function(t){t.__dirty&&t.clear()})},_paintList:function(t,e){null==e&&(e=!1),this._updateLayerStatus(t),this._clearProgressive(),this.eachBuildinLayer(r),this._doPaintList(t,e),this.eachBuildinLayer(a)},_doPaintList:function(t,e){function i(t){var e=a.dpr||1;a.save(),a.globalAlpha=1,a.shadowBlur=0,n.__dirty=!0,a.setTransform(1,0,0,1,0,0),a.drawImage(t.dom,0,0,h*e,f*e),a.restore()}for(var n,r,a,o,s,l,u=0,h=this._width,f=this._height,p=this._progress,m=0,v=t[R];v>m;m++){var y=t[m],_=this._singleCanvas?0:y[b],x=y.__frame;if(0>x&&s&&(i(s),s=null),r!==_&&(a&&a.restore(),o={},r=_,n=this.getLayer(r),n.isBuildin||d("ZLevel "+r+" has been used by unkown layer "+n.id),a=n.ctx,a.save(),n.__unusedCount=0,(n.__dirty||e)&&n.clear()),n.__dirty||e){if(x>=0){if(!s){if(s=this._progressiveLayers[Math.min(u++,g-1)],s.ctx.save(),s.renderScope={},s&&s.__progress>s.__maxProgress){m=s.__nextIdxNotProg-1;continue}l=s.__progress,s.__dirty||(p=l),s.__progress=p+1}x===p&&this._doPaintEl(y,s,!0,s.renderScope)}else this._doPaintEl(y,n,e,o);y.__dirty=!1}}s&&i(s),a&&a.restore(),this._furtherProgressive=!1,c.each(this._progressiveLayers,function(t){t.__maxProgress>=t.__progress&&(this._furtherProgressive=!0)},this)},_doPaintEl:function(t,e,i,n){var r=e.ctx,a=t.transform;if(!(!e.__dirty&&!i||t.invisible||0===t.style.opacity||a&&!a[0]&&!a[3]||t.culling&&o(t,this._width,this._height))){var u=t.__clipPaths;(n.prevClipLayer!==e||s(u,n.prevElClipPaths))&&(n.prevElClipPaths&&(n.prevClipLayer.ctx.restore(),n.prevClipLayer=n.prevElClipPaths=null,n.prevEl=null),u&&(r.save(),l(u,r),n.prevClipLayer=e,n.prevElClipPaths=u)),t.beforeBrush&&t.beforeBrush(r),t.brush(r,n.prevEl||null),n.prevEl=t,t.afterBrush&&t.afterBrush(r)}},getLayer:function(t){if(this._singleCanvas)return this._layers[0];var e=this._layers[t];return e||(e=new m("zr_"+t,this,this.dpr),e.isBuildin=!0,this._layerConfig[t]&&c.merge(e,this._layerConfig[t],!0),this.insertLayer(t,e),e.initContext()),e},insertLayer:function(t,e){var n=this._layers,r=this._zlevelList,a=r[R],o=null,s=-1,l=this._domRoot;if(n[t])return void d("ZLevel "+t+" has been used already");if(!i(e))return void d("Layer of zlevel "+t+" is not valid");if(a>0&&t>r[0]){for(s=0;a-1>s&&!(r[s]<t&&r[s+1]>t);s++);o=n[r[s]]}if(r[T](s+1,0,t),o){var u=o.dom;u.nextSibling?l.insertBefore(e.dom,u.nextSibling):l.appendChild(e.dom)}else l.firstChild?l.insertBefore(e.dom,l.firstChild):l.appendChild(e.dom);n[t]=e},eachLayer:function(t,e){var i,n,r=this._zlevelList;for(n=0;n<r[R];n++)i=r[n],t.call(e,this._layers[i],i)},eachBuildinLayer:function(t,e){var i,n,r,a=this._zlevelList;for(r=0;r<a[R];r++)n=a[r],i=this._layers[n],i.isBuildin&&t.call(e,i,n)},eachOtherLayer:function(t,e){var i,n,r,a=this._zlevelList;for(r=0;r<a[R];r++)n=a[r],i=this._layers[n],i.isBuildin||t.call(e,i,n)},getLayers:function(){return this._layers},_updateLayerStatus:function(t){var e=this._layers,i=this._progressiveLayers,n={},r={};this.eachBuildinLayer(function(t,e){n[e]=t.elCount,t.elCount=0,t.__dirty=!1}),c.each(i,function(t,e){r[e]=t.elCount,t.elCount=0,t.__dirty=!1});for(var a,o,s=0,l=0,u=0,h=t[R];h>u;u++){var d=t[u],f=this._singleCanvas?0:d[b],p=e[f],v=d.progressive;if(p&&(p.elCount++,p.__dirty=p.__dirty||d.__dirty),v>=0){o!==v&&(o=v,l++);var y=d.__frame=l-1;if(!a){var _=Math.min(s,g-1);a=i[_],a||(a=i[_]=new m("progressive",this,this.dpr),a.initContext()),a.__maxProgress=0}a.__dirty=a.__dirty||d.__dirty,a.elCount++,a.__maxProgress=Math.max(a.__maxProgress,y),a.__maxProgress>=a.__progress&&(p.__dirty=!0)}else d.__frame=-1,a&&(a.__nextIdxNotProg=u,s++,a=null)}a&&(s++,a.__nextIdxNotProg=u),this.eachBuildinLayer(function(t,e){n[e]!==t.elCount&&(t.__dirty=!0)}),i[R]=Math.min(s,g),c.each(i,function(t,e){r[e]!==t.elCount&&(d.__dirty=!0),t.__dirty&&(t.__progress=0)})},clear:function(){return this.eachBuildinLayer(this._clearLayer),this},_clearLayer:function(t){t.clear()},configLayer:function(t,e){if(e){var i=this._layerConfig;i[t]?c.merge(i[t],e,!0):i[t]=e;var n=this._layers[t];n&&c.merge(n,i[t],!0)}},delLayer:function(t){var e=this._layers,i=this._zlevelList,n=e[t];n&&(n.dom.parentNode.removeChild(n.dom),delete e[t],i[T](c[O](i,t),1))},resize:function(t,e){var i=this._domRoot;i.style.display="none";var n=this._opts;if(null!=t&&(n.width=t),null!=e&&(n[K]=e),t=this._getSize(0),e=this._getSize(1),i.style.display="",this._width!=t||e!=this._height){i.style.width=t+"px",i.style[K]=e+"px";for(var r in this._layers)this._layers.hasOwnProperty(r)&&this._layers[r].resize(t,e);c.each(this._progressiveLayers,function(i){i.resize(t,e)}),this.refresh(!0)}return this._width=t,this._height=e,this},clearLayer:function(t){var e=this._layers[t];e&&e.clear()},dispose:function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},getRenderedCanvas:function(t){if(t=t||{},this._singleCanvas)return this._layers[0].dom;var e=new m("image",this,t.pixelRatio||this.dpr);e.initContext(),e.clearColor=t.backgroundColor,e.clear();for(var i=this.storage.getDisplayList(!0),n={},r=0;r<i[R];r++){var a=i[r];this._doPaintEl(a,e,!0,n)}return e.dom},getWidth:function(){return this._width},getHeight:function(){return this._height},_getSize:function(t){var i=this._opts,n=["width",K][t],r=["clientWidth","clientHeight"][t],a=["paddingLeft","paddingTop"][t],o=["paddingRight","paddingBottom"][t];if(null!=i[n]&&"auto"!==i[n])return parseFloat(i[n]);var s=this.root,l=document.defaultView.getComputedStyle(s);return(s[r]||e(l[n])||e(s.style[n]))-(e(l[a])||0)-(e(l[o])||0)|0},_pathToImage:function(e,i,n,r,a){var o=document.createElement("canvas"),s=o.getContext("2d");o.width=n*a,o[K]=r*a,s.clearRect(0,0,n*a,r*a);var l={position:i[W],rotation:i.rotation,scale:i.scale};i[W]=[0,0,0],i.rotation=0,i.scale=[1,1],i&&i.brush(s);var u=t("./graphic/Image"),h=new u({id:e,style:{x:0,y:0,image:o}});return null!=l[W]&&(h[W]=i[W]=l[W]),null!=l.rotation&&(h.rotation=i.rotation=l.rotation),null!=l.scale&&(h.scale=i.scale=l.scale),h
},_createPathToImage:function(){var t=this;return function(e,i,n,r){return t._pathToImage(e,i,n,r,t.dpr)}}},w}),e("zrender/animation/requestAnimationFrame",[le],function(){return typeof window!==i&&(window.requestAnimationFrame||window.msRequestAnimationFrame||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){setTimeout(t,16)}}),e("zrender/core/event",[le,"../mixin/Eventful","./env"],function(t){function e(t){return t.getBoundingClientRect?t.getBoundingClientRect():{left:0,top:0}}function n(t,e,i,n){return i=i||{},n||!u[B]?r(t,e,i):u.browser.firefox&&null!=e.layerX&&e.layerX!==e.offsetX?(i.zrX=e.layerX,i.zrY=e.layerY):null!=e.offsetX?(i.zrX=e.offsetX,i.zrY=e.offsetY):r(t,e,i),i}function r(t,i,n){var r=e(t);n.zrX=i.clientX-r.left,n.zrY=i.clientY-r.top}function a(t,e,i){if(e=e||window.event,null!=e.zrX)return e;var r=e.type,a=r&&r[O]("touch")>=0;if(a){var o="touchend"!=r?e.targetTouches[0]:e.changedTouches[0];o&&n(t,o,e,i)}else n(t,e,e,i),e.zrDelta=e.wheelDelta?e.wheelDelta/120:-(e.detail||0)/3;return e}function o(t,e,i){h?t.addEventListener(e,i):t.attachEvent("on"+e,i)}function s(t,e,i){h?t.removeEventListener(e,i):t.detachEvent("on"+e,i)}var l=t("../mixin/Eventful"),u=t("./env"),h=typeof window!==i&&!!window.addEventListener,c=h?function(t){t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0}:function(t){t.returnValue=!1,t.cancelBubble=!0};return{clientToLocal:n,normalizeEvent:a,addEventListener:o,removeEventListener:s,stop:c,Dispatcher:l}}),e("zrender/core/GestureMgr",[le,"./event"],function(t){function e(t){var e=t[1][0]-t[0][0],i=t[1][1]-t[0][1];return Math.sqrt(e*e+i*i)}function i(t){return[(t[0][0]+t[1][0])/2,(t[0][1]+t[1][1])/2]}var n=t("./event"),r=function(){this._track=[]};r[Z]={constructor:r,recognize:function(t,e,i){return this._doTrack(t,e,i),this._recognize(t)},clear:function(){return this._track[R]=0,this},_doTrack:function(t,e,i){var r=t.touches;if(r){for(var a={points:[],touches:[],target:e,event:t},o=0,s=r[R];s>o;o++){var l=r[o],u=n.clientToLocal(i,l,{});a.points.push([u.zrX,u.zrY]),a.touches.push(l)}this._track.push(a)}},_recognize:function(t){for(var e in a)if(a.hasOwnProperty(e)){var i=a[e](this._track,t);if(i)return i}}};var a={pinch:function(t,n){var r=t[R];if(r){var a=(t[r-1]||{}).points,o=(t[r-2]||{}).points||a;if(o&&o[R]>1&&a&&a[R]>1){var s=e(a)/e(o);!isFinite(s)&&(s=1),n.pinchScale=s;var l=i(a);return n.pinchX=l[0],n.pinchY=l[1],{type:"pinch",target:t[0][Q],event:n}}}}};return r}),e("zrender/Layer",[le,"./core/util","./config","./graphic/Style","./graphic/Pattern"],function(t){function e(){return!1}function i(t,e,i,n){var r=document.createElement(e),a=i[$](),o=i[Y](),s=r.style;return s[W]="absolute",s.left=0,s.top=0,s.width=a+"px",s[K]=o+"px",r.width=a*n,r[K]=o*n,r.setAttribute("data-zr-dom-id",t),r}var n=t("./core/util"),r=t("./config"),a=t("./graphic/Style"),o=t("./graphic/Pattern"),s=function(t,a,o){var s;o=o||r.devicePixelRatio,typeof t===G?s=i(t,"canvas",a,o):n[P](t)&&(s=t,t=s.id),this.id=t,this.dom=s;var l=s.style;l&&(s.onselectstart=e,l["-webkit-user-select"]="none",l["user-select"]="none",l["-webkit-touch-callout"]="none",l["-webkit-tap-highlight-color"]="rgba(0,0,0,0)",l.padding=0,l.margin=0,l["border-width"]=0),this.domBack=null,this.ctxBack=null,this.painter=a,this.config=null,this.clearColor=0,this.motionBlur=!1,this.lastFrameAlpha=.7,this.dpr=o};return s[Z]={constructor:s,elCount:0,__dirty:!0,initContext:function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},createBackBuffer:function(){var t=this.dpr;this.domBack=i("back-"+this.id,"canvas",this.painter,t),this.ctxBack=this.domBack.getContext("2d"),1!=t&&this.ctxBack.scale(t,t)},resize:function(t,e){var i=this.dpr,n=this.dom,r=n.style,a=this.domBack;r.width=t+"px",r[K]=e+"px",n.width=t*i,n[K]=e*i,a&&(a.width=t*i,a[K]=e*i,1!=i&&this.ctxBack.scale(i,i))},clear:function(t){var e=this.dom,i=this.ctx,n=e.width,r=e[K],s=this.clearColor,l=this.motionBlur&&!t,u=this.lastFrameAlpha,h=this.dpr;if(l&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(e,0,0,n/h,r/h)),i.clearRect(0,0,n,r),s){var c;s[I]?(c=s.__canvasGradient||a.getGradient(i,s,{x:0,y:0,width:n,height:r}),s.__canvasGradient=c):s.image&&(c=o[Z].getCanvasPattern.call(s,i)),i.save(),i.fillStyle=c||s,i.fillRect(0,0,n,r),i.restore()}if(l){var d=this.domBack;i.save(),i.globalAlpha=u,i.drawImage(d,0,0,n,r),i.restore()}}},s}),e("echarts/preprocessor/helper/compatStyle",[le,se],function(t){function e(t){var e=t&&t.itemStyle;e&&i.each(n,function(n){var r=e.normal,a=e.emphasis;r&&r[n]&&(t[n]=t[n]||{},t[n].normal?i.merge(t[n].normal,r[n]):t[n].normal=r[n],r[n]=null),a&&a[n]&&(t[n]=t[n]||{},t[n].emphasis?i.merge(t[n].emphasis,a[n]):t[n].emphasis=a[n],a[n]=null)})}var i=t(se),n=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"];return function(t){if(t){e(t),e(t.markPoint),e(t.markLine);var n=t.data;if(n){for(var r=0;r<n[R];r++)e(n[r]);var a=t.markPoint;if(a&&a.data)for(var o=a.data,r=0;r<o[R];r++)e(o[r]);var s=t.markLine;if(s&&s.data)for(var l=s.data,r=0;r<l[R];r++)i[_](l[r])?(e(l[r][0]),e(l[r][1])):e(l[r])}}}}),e("echarts/data/DataDiffer",[le],function(){function t(t){return t}function e(e,i,n,r){this._old=e,this._new=i,this._oldKeyGetter=n||t,this._newKeyGetter=r||t}function i(t,e,i,n){for(var r=0;r<t[R];r++){var a=n(t[r],r),o=e[a];null==o?(i.push(a),e[a]=r):(o[R]||(e[a]=o=[o]),o.push(r))}}return e[Z]={constructor:e,add:function(t){return this._add=t,this},update:function(t){return this._update=t,this},remove:function(t){return this._remove=t,this},execute:function(){var t,e=this._old,n=this._new,r=this._oldKeyGetter,a=this._newKeyGetter,o={},s={},l=[],u=[];for(i(e,o,l,r),i(n,s,u,a),t=0;t<e[R];t++){var h=l[t],c=s[h];if(null!=c){var d=c[R];d?(1===d&&(s[h]=null),c=c.unshift()):s[h]=null,this._update&&this._update(c,t)}else this._remove&&this._remove(t)}for(var t=0;t<u[R];t++){var h=u[t];if(s.hasOwnProperty(h)){var c=s[h];if(null==c)continue;if(c[R])for(var f=0,d=c[R];d>f;f++)this._add&&this._add(c[f]);else this._add&&this._add(c)}}}},e}),e("echarts/chart/helper/createListFromArray",[le,"../../data/List","../../data/helper/completeDimensions",se,"../../util/model","../../CoordinateSystem"],function(t){function e(t){for(var e=0;e<t[R]&&null==t[e];)e++;return t[e]}function i(t){var i=e(t);return null!=i&&!c[_](v(i))}function n(t,e,n){t=t||[];var r=e.get(ae),a=y[r],o=m.get(r),l=a&&a(t,e,n),d=l&&l[f];d||(d=o&&o[f]||["x","y"],d=h(d,t,d.concat(["value"])));var _=l?l.categoryIndex:-1,x=new u(d,e),b=s(l,t),w={},T=_>=0&&i(t)?function(t,e,i,n){return p.isDataItemOption(t)&&(x.hasItemOption=!0),n===_?i:g(v(t),d[n])}:function(t,e,i,n){var r=v(t),a=g(r&&r[n],d[n]);p.isDataItemOption(t)&&(x.hasItemOption=!0);var o=l&&l.categoryAxesModels;return o&&o[e]&&typeof a===G&&(w[e]=w[e]||o[e].getCategories(),a=c[O](w[e],a),0>a&&!isNaN(a)&&(a=+a)),a};return x.hasItemOption=!1,x.initData(t,b,T),x}function r(t){return t!==l&&"time"!==t}function a(t){return t===l?d:"time"===t?"time":"float"}function s(t,e){var i,n=[],r=t&&t[f][t.categoryIndex];if(r&&(i=t.categoryAxesModels[r.name]),i){var a=i.getCategories();if(a){var o=e[R];if(c[_](e[0])&&e[0][R]>1){n=[];for(var s=0;o>s;s++)n[s]=a[e[s][t.categoryIndex||0]]}else n=a.slice(0)}}return n}var u=t("../../data/List"),h=t("../../data/helper/completeDimensions"),c=t(se),p=t("../../util/model"),m=t("../../CoordinateSystem"),v=p.getDataItemValue,g=p.converDataValue,y={cartesian2d:function(t,e,i){var n=c.map(["xAxis","yAxis"],function(t){return i[o]({mainType:t,index:e.get(t+"Index"),id:e.get(t+"Id")})[0]}),s=n[0],u=n[1],d=s.get("type"),f=u.get("type"),p=[{name:"x",type:a(d),stackable:r(d)},{name:"y",type:a(f),stackable:r(f)}],m=d===l,v=f===l;h(p,t,["x","y","z"]);var g={};return m&&(g.x=s),v&&(g.y=u),{dimensions:p,categoryIndex:m?0:v?1:-1,categoryAxesModels:g}},polar:function(t,e,i){var n=i[o]({mainType:"polar",index:e.get("polarIndex"),id:e.get("polarId")})[0],s=n.findAxisModel("angleAxis"),u=n.findAxisModel("radiusAxis"),c=u.get("type"),d=s.get("type"),f=[{name:"radius",type:a(c),stackable:r(c)},{name:"angle",type:a(d),stackable:r(d)}],p=d===l,m=c===l;h(f,t,["radius","angle","value"]);var v={};return m&&(v.radius=u),p&&(v.angle=s),{dimensions:f,categoryIndex:p?1:m?0:-1,categoryAxesModels:v}},geo:function(t){return{dimensions:h([{name:"lng"},{name:"lat"}],t,["lng","lat","value"])}}};return n}),e("echarts/coord/axisHelper",[le,"../scale/Ordinal","../scale/Interval","../scale/Time","../scale/Log","../scale/Scale","../util/number",se,"zrender/contain/text"],function(t){var e=t("../scale/Ordinal"),i=t("../scale/Interval");t("../scale/Time"),t("../scale/Log");var n=t("../scale/Scale"),r=t("../util/number"),a=t(se),o=t("zrender/contain/text"),s={};return s.getScaleExtent=function(t,e){var i=t.scale,n=i[m](),o=n[1]-n[0];if(i.type===d)return isFinite(o)?n:[0,0];var s=e.getMin?e.getMin():e.get("min"),l=e.getMax?e.getMax():e.get("max"),u=e.getNeedCrossZero?e.getNeedCrossZero():!e.get("scale"),h=e.get("boundaryGap");a[_](h)||(h=[h||0,h||0]),h[0]=r.parsePercent(h[0],1),h[1]=r.parsePercent(h[1],1);var c=!0,f=!0;return null==s&&(s=n[0]-h[0]*o,c=!1),null==l&&(l=n[1]+h[1]*o,f=!1),"dataMin"===s&&(s=n[0]),"dataMax"===l&&(l=n[1]),u&&(s>0&&l>0&&!c&&(s=0),0>s&&0>l&&!f&&(l=0)),[s,l]},s.niceScaleExtent=function(t,e){var i=t.scale,n=s.getScaleExtent(t,e),r=null!=(e.getMin?e.getMin():e.get("min")),a=null!=(e.getMax?e.getMax():e.get("max")),o=e.get("splitNumber");"log"===i.type&&(i.base=e.get("logBase")),i.setExtent(n[0],n[1]),i.niceExtent(o,r,a);var l=e.get("minInterval");if(isFinite(l)&&!r&&!a&&"interval"===i.type){var u=i.getInterval(),h=Math.max(Math.abs(u),l)/u;n=i[m]();var c=(n[1]+n[0])/2;i.setExtent(h*(n[0]-c)+c,h*(n[1]-c)+c),i.niceExtent(o)}var u=e.get("interval");null!=u&&i.setInterval&&i.setInterval(u)},s.createScaleByModel=function(t,r){if(r=r||t.get("type"))switch(r){case l:return new e(t.getCategories(),[1/0,-1/0]);case"value":return new i;default:return(n.getClass(r)||i)[k](t)}},s.ifAxisCrossZero=function(t){var e=t.scale[m](),i=e[0],n=e[1];return!(i>0&&n>0||0>i&&0>n)},s.getAxisLabelInterval=function(t,e,i,n){var r,a=0,s=0,l=1;e[R]>40&&(l=Math.floor(e[R]/40));for(var u=0;u<t[R];u+=l){var h=t[u],c=o[J](e[u],i,X,"top");c[n?"x":"y"]+=h,c[n?"width":K]*=1.3,r?r.intersect(c)?(s++,a=Math.max(a,s)):(r.union(c),s=0):r=c.clone()}return 0===a&&l>1?l:(a+1)*l-1},s.getFormattedLabels=function(t,e){var i=t.scale,n=i.getTicksLabels(),r=i.getTicks();return typeof e===G?(e=function(t){return function(e){return t[x]("{value}",null!=e?e:"")}}(e),a.map(n,e)):typeof e===y?a.map(r,function(n,r){return e(t.type===l?i[p](n):n,r)},this):n},s}),e("echarts/coord/cartesian/GridModel",[le,"./AxisModel","../../model/Component"],function(t){t("./AxisModel");var e=t("../../model/Component");return e[C]({type:"grid",dependencies:["xAxis","yAxis"],layoutMode:"box",coordinateSystem:null,defaultOption:{show:!1,zlevel:0,z:0,left:"10%",top:60,right:"10%",bottom:60,containLabel:!1,backgroundColor:"rgba(0,0,0,0)",borderWidth:1,borderColor:"#ccc"}})}),e("echarts/coord/cartesian/Cartesian2D",[le,se,"./Cartesian"],function(t){function e(t){n.call(this,t)}var i=t(se),n=t("./Cartesian");return e[Z]={constructor:e,type:"cartesian2d",dimensions:["x","y"],getBaseAxis:function(){return this.getAxesByScale(d)[0]||this.getAxesByScale("time")[0]||this.getAxis("x")},containPoint:function(t){var e=this.getAxis("x"),i=this.getAxis("y");return e[v](e.toLocalCoord(t[0]))&&i[v](i.toLocalCoord(t[1]))},containData:function(t){return this.getAxis("x").containData(t[0])&&this.getAxis("y").containData(t[1])},dataToPoints:function(t,e){return t.mapArray(["x","y"],function(t,e){return this.dataToPoint([t,e])},e,this)},dataToPoint:function(t,e){var i=this.getAxis("x"),n=this.getAxis("y");return[i.toGlobalCoord(i.dataToCoord(t[0],e)),n.toGlobalCoord(n.dataToCoord(t[1],e))]},pointToData:function(t,e){var i=this.getAxis("x"),n=this.getAxis("y");return[i.coordToData(i.toLocalCoord(t[0]),e),n.coordToData(n.toLocalCoord(t[1]),e)]},getOtherAxis:function(t){return this.getAxis("x"===t.dim?"y":"x")}},i.inherits(e,n),e}),e("echarts/coord/cartesian/Axis2D",[le,se,"../Axis","./axisLabelInterval"],function(t){var e=t(se),i=t("../Axis"),n=t("./axisLabelInterval"),r=function(t,e,n,r,a){i.call(this,t,e,n),this.type=r||"value",this[W]=a||U};return r[Z]={constructor:r,index:0,onZero:!1,model:null,isHorizontal:function(){var t=this[W];return"top"===t||t===U},getGlobalExtent:function(){var t=this[m]();return t[0]=this.toGlobalCoord(t[0]),t[1]=this.toGlobalCoord(t[1]),t},getLabelInterval:function(){var t=this._labelInterval;return t||(t=this._labelInterval=n(this)),t},isLabelIgnored:function(t){if(this.type===l){var e=this.getLabelInterval();return typeof e===y&&!e(t,this.scale[p](t))||t%(e+1)}},toLocalCoord:null,toGlobalCoord:null},e.inherits(r,i),r}),e("echarts/component/tooltip/TooltipContent",[le,se,"zrender/tool/color","zrender/core/event","../../util/format",H],function(t){function e(t){var e="cubic-bezier(0.23, 1, 0.32, 1)",i="left "+t+"s "+e+",top "+t+"s "+e;return a.map(d,function(t){return t+"transition:"+i}).join(";")}function i(t){var e=[],i=t.get("fontSize"),n=t.getTextColor();return n&&e.push("color:"+n),e.push("font:"+t[te]()),i&&e.push("line-height:"+Math.round(3*i/2)+"px"),u(["decoration","align"],function(i){var n=t.get(i);n&&e.push("text-"+i+":"+n)}),e.join(";")}function n(t){t=t;var n=[],r=t.get("transitionDuration"),a=t.get("backgroundColor"),s=t[ne](ie),d=t.get("padding");return r&&n.push(e(r)),a&&(c[B]?n.push("background-Color:"+a):(n.push("background-Color:#"+o.toHex(a)),n.push("filter:alpha(opacity=70)"))),u(["width","color","radius"],function(e){var i="border-"+e,r=h(i),a=t.get(r);null!=a&&n.push(i+":"+a+("color"===e?"":"px"))}),n.push(i(s)),null!=d&&n.push("padding:"+l.normalizeCssArray(d).join("px ")+"px"),n.join(";")+";"}function r(t,e){var i=document.createElement("div"),n=e.getZr();this.el=i,this._x=e[$]()/2,this._y=e[Y]()/2,t.appendChild(i),this._container=t,this._show=!1,this._hideTimeout;var r=this;i.onmouseenter=function(){r.enterable&&(clearTimeout(r._hideTimeout),r._show=!0),r._inContent=!0},i.onmousemove=function(e){if(e=e||window.event,!r.enterable){var i=n.handler;s.normalizeEvent(t,e,!0),i.dispatch("mousemove",e)}},i.onmouseleave=function(){r.enterable&&r._show&&r.hideLater(r._hideDelay),r._inContent=!1}}var a=t(se),o=t("zrender/tool/color"),s=t("zrender/core/event"),l=t("../../util/format"),u=a.each,h=l.toCamelCase,c=t(H),d=["","-webkit-","-moz-","-o-"],f="position:absolute;display:block;border-style:solid;white-space:nowrap;z-index:9999999;";return r[Z]={constructor:r,enterable:!0,update:function(){var t=this._container,e=t.currentStyle||document.defaultView.getComputedStyle(t),i=t.style;"absolute"!==i[W]&&"absolute"!==e[W]&&(i[W]="relative")},show:function(t){clearTimeout(this._hideTimeout);var e=this.el;e.style.cssText=f+n(t)+";left:"+this._x+"px;top:"+this._y+"px;"+(t.get("extraCssText")||""),e.style.display=e.innerHTML?"block":"none",this._show=!0},setContent:function(t){var e=this.el;e.innerHTML=t,e.style.display=t?"block":"none"},moveTo:function(t,e){var i=this.el.style;i.left=t+"px",i.top=e+"px",this._x=t,this._y=e},hide:function(){this.el.style.display="none",this._show=!1},hideLater:function(t){!this._show||this._inContent&&this.enterable||(t?(this._hideDelay=t,this._show=!1,this._hideTimeout=setTimeout(a.bind(this.hide,this),t)):this.hide())},isShow:function(){return this._show}},r}),e("echarts/component/helper/sliderMove",[le],function(){return function(t,e,i,n,r){function a(t,e,i){var n=e[R]?e.slice():[e,e];return e[0]>e[1]&&n.reverse(),0>t&&n[0]+t<i[0]&&(t=i[0]-n[0]),t>0&&n[1]+t>i[1]&&(t=i[1]-n[1]),t}return t?("rigid"===n?(t=a(t,e,i),e[0]+=t,e[1]+=t):(t=a(t,e[r],i),e[r]+=t,"push"===n&&e[0]>e[1]&&(e[1-r]=e[r])),e):e}}),e("echarts/data/helper/completeDimensions",[le,se],function(t){function e(t,e,a,o){if(!e)return t;var s=i(e[0]),l=n[_](s)&&s[R]||1;a=a||[],o=o||"extra";for(var u=0;l>u;u++)if(!t[u]){var h=a[u]||o+(u-a[R]);t[u]=r(e,u)?{type:"ordinal",name:h}:h}return t}function i(t){return n[_](t)?t:n[P](t)?t.value:t}var n=t(se),r=e.guessOrdinal=function(t,e){for(var r=0,a=t[R];a>r;r++){var o=i(t[r]);if(!n[_](o))return!1;var o=o[e];if(null!=o&&isFinite(o))return!1;if(n[g](o)&&"-"!==o)return!0}return!1};return e}),e("echarts/scale/Ordinal",[le,se,"./Scale"],function(t){var e=t(se),i=t("./Scale"),n=i[Z],r=i[C]({type:"ordinal",init:function(t,e){this._data=t,this._extent=e||[0,t[R]-1]},parse:function(t){return typeof t===G?e[O](this._data,t):Math.round(t)},contain:function(t){return t=this.parse(t),n[v].call(this,t)&&null!=this._data[t]},normalize:function(t){return n.normalize.call(this,this.parse(t))},scale:function(t){return Math.round(n.scale.call(this,t))},getTicks:function(){for(var t=[],e=this._extent,i=e[0];i<=e[1];)t.push(i),i++;return t},getLabel:function(t){return this._data[t]},count:function(){return this._extent[1]-this._extent[0]+1},niceTicks:e.noop,niceExtent:e.noop});return r[k]=function(){return new r},r}),e("echarts/component/dataZoom/roams",[le,se,"../../component/helper/RoamController","../../util/throttle"],function(t){function e(t){var e=t.getZr();return e[d]||(e[d]={})}function i(t,e,i){var n=new u(t.getZr());return n.enable(),n.on("pan",c(r,i)),n.on("zoom",c(a,i)),n}function n(t){l.each(t,function(e,i){e.count||(e.controller[M](),delete t[i])})}function r(t,e,i,n,r,a,s){o(t,function(o){return o.panGetRange(t.controller,e,i,n,r,a,s)})}function a(t,e,i,n){o(t,function(r){return r.zoomGetRange(t.controller,e,i,n)})}function o(t,e){var i=[];l.each(t.dataZoomInfos,function(t){var n=e(t);n&&i.push({dataZoomId:t.dataZoomId,start:n[0],end:n[1]})}),t.dispatchAction(i)}function s(t,e){t.dispatchAction({type:"dataZoom",batch:e})}var l=t(se),u=t("../../component/helper/RoamController"),h=t("../../util/throttle"),c=l.curry,d="\x00_ec_dataZoom_roams",f={register:function(t,r){var a=e(t),o=r.dataZoomId,u=r.coordId;l.each(a,function(t){var e=t.dataZoomInfos;e[o]&&l[O](r.allCoordIds,u)<0&&(delete e[o],t.count--)}),n(a);var c=a[u];c||(c=a[u]={coordId:u,dataZoomInfos:{},count:0},c.controller=i(t,r,c),c.dispatchAction=l.curry(s,t)),c.controller.setContainsPoint(r.containsPoint),h.createOrUpdate(c,"dispatchAction",r.throttleRate,"fixRate"),!c.dataZoomInfos[o]&&c.count++,c.dataZoomInfos[o]=r},unregister:function(t,i){var r=e(t);l.each(r,function(t){t.controller[M]();var e=t.dataZoomInfos;e[i]&&(delete e[i],t.count--)}),n(r)},shouldRecordRange:function(t,e){if(t&&"dataZoom"===t.type&&t.batch)for(var i=0,n=t.batch[R];n>i;i++)if(t.batch[i].dataZoomId===e)return!1;return!0},generateCoordId:function(t){return t.type+"\x00_"+t.id}};return f}),e("echarts/chart/helper/SymbolDraw",[le,"../../util/graphic","./Symbol"],function(t){function e(t){this.group=new n.Group,this._symbolCtor=t||r}function i(t,e,i){var n=t.getItemLayout(e);return!(!n||isNaN(n[0])||isNaN(n[1])||i&&i(e)||"none"===t.getItemVisual(e,"symbol"))}var n=t("../../util/graphic"),r=t("./Symbol"),a=e[Z];return a.updateData=function(t,e){var r=this.group,a=t.hostModel,o=this._data,s=this._symbolCtor,l={itemStyle:a[ne]("itemStyle.normal").getItemStyle(["color"]),hoverItemStyle:a[ne]("itemStyle.emphasis").getItemStyle(),symbolRotate:a.get("symbolRotate"),symbolOffset:a.get("symbolOffset"),hoverAnimation:a.get("hoverAnimation"),labelModel:a[ne]("label.normal"),hoverLabelModel:a[ne]("label.emphasis")};t.diff(o).add(function(n){var a=t.getItemLayout(n);if(i(t,n,e)){var o=new s(t,n,l);o.attr(W,a),t.setItemGraphicEl(n,o),r.add(o)}})[z](function(u,h){var c=o.getItemGraphicEl(h),d=t.getItemLayout(u);return i(t,u,e)?(c?(c.updateData(t,u,l),n.updateProps(c,{position:d},a)):(c=new s(t,u),c.attr(W,d)),r.add(c),void t.setItemGraphicEl(u,c)):void r[S](c)})[S](function(t){var e=o.getItemGraphicEl(t);e&&e.fadeOut(function(){r[S](e)})}).execute(),this._data=t},a.updateLayout=function(){var t=this._data;t&&t.eachItemGraphicEl(function(e,i){var n=t.getItemLayout(i);e.attr(W,n)})},a[S]=function(t){var e=this.group,i=this._data;i&&(t?i.eachItemGraphicEl(function(t){t.fadeOut(function(){e[S](t)})}):e[oe]())},e}),e("echarts/chart/helper/Symbol",[le,se,"../../util/symbol","../../util/graphic","../../util/number"],function(t){function e(t){return t=t instanceof Array?t.slice():[+t,+t],t[0]/=2,t[1]/=2,t}function i(t,e,i){l.Group.call(this),this.updateData(t,e,i)}function n(t,e){this[r].drift(t,e)}var o=t(se),s=t("../../util/symbol"),l=t("../../util/graphic"),u=t("../../util/number"),p=i[Z];p._createSymbol=function(t,i,r){this[oe]();var a=i.hostModel,o=i.getItemVisual(r,"color"),u=s.createSymbol(t,-1,-1,2,2,o);u.attr({z2:100,culling:!0,scale:[0,0]}),u.drift=n;var h=e(i.getItemVisual(r,"symbolSize"));l.initProps(u,{scale:h},a,r),this._symbolType=t,this.add(u)},p.stopSymbolAnimation=function(t){this.childAt(0).stopAnimation(t)},p.getSymbolPath=function(){return this.childAt(0)},p.getScale=function(){return this.childAt(0).scale},p.highlight=function(){this.childAt(0).trigger("emphasis")},p.downplay=function(){this.childAt(0).trigger("normal")},p.setZ=function(t,e){var i=this.childAt(0);i[b]=t,i.z=e},p.setDraggable=function(t){var e=this.childAt(0);e.draggable=t,e.cursor=t?"move":"pointer"},p.updateData=function(t,i,n){this.silent=!1;var r=t.getItemVisual(i,"symbol")||"circle",a=t.hostModel,o=e(t.getItemVisual(i,"symbolSize"));if(r!==this._symbolType)this._createSymbol(r,t,i);else{var s=this.childAt(0);l.updateProps(s,{scale:o},a,i)}this._updateCommon(t,i,o,n),this._seriesModel=a};var m=["itemStyle","normal"],v=["itemStyle","emphasis"],g=["label","normal"],y=["label","emphasis"];return p._updateCommon=function(t,i,n,r){var s=this.childAt(0),p=t.hostModel,_=t.getItemVisual(i,"color");"image"!==s.type&&s.useStyle({strokeNoScale:!0}),r=r||null;var x=r&&r.itemStyle,b=r&&r.hoverItemStyle,w=r&&r.symbolRotate,T=r&&r.symbolOffset,M=r&&r.labelModel,A=r&&r.hoverLabelModel,S=r&&r.hoverAnimation;if(!r||t.hasItemOption){var P=t[c](i);x=P[ne](m).getItemStyle(["color"]),b=P[ne](v).getItemStyle(),w=P[h]("symbolRotate"),T=P[h]("symbolOffset"),M=P[ne](g),A=P[ne](y),S=P[h]("hoverAnimation")}else b=o[C]({},b);var I=s.style;s.attr("rotation",(w||0)*Math.PI/180||0),T&&s.attr(W,[u.parsePercent(T[0],n[0]),u.parsePercent(T[1],n[1])]),s.setColor(_),s[q](x);var z=t.getItemVisual(i,"opacity");null!=z&&(I.opacity=z);for(var k,L,D=t[f].slice();D[R]&&(k=D.pop(),L=t.getDimensionInfo(k).type,L===d||"time"===L););null!=k&&M[h]("show")?(l.setText(I,M,_),I.text=o[a](p.getFormattedLabel(i,"normal"),t.get(k,i))):I.text="",null!=k&&A[h]("show")?(l.setText(b,A,_),b.text=o[a](p.getFormattedLabel(i,"emphasis"),t.get(k,i))):b.text="";var O=e(t.getItemVisual(i,"symbolSize"));if(s.off("mouseover").off("mouseout").off("emphasis").off("normal"),s.hoverStyle=b,l.setHoverStyle(s),S&&p.ifEnableAnimation()){var E=function(){var t=O[1]/O[0];this.animateTo({scale:[Math.max(1.1*O[0],O[0]+3),Math.max(1.1*O[1],O[1]+3*t)]},400,"elasticOut")},B=function(){this.animateTo({scale:O},400,"elasticOut")};s.on("mouseover",E).on("mouseout",B).on("emphasis",E).on("normal",B)}},p.fadeOut=function(t){var e=this.childAt(0);this.silent=!0,e.style.text="",l.updateProps(e,{scale:[0,0]},this._seriesModel,this[L],t)},o.inherits(i,l.Group),i}),e("echarts/chart/line/lineAnimationDiff",[le],function(){function t(t){return t>=0?1:-1}function e(e,i,n){for(var r,a=e.getBaseAxis(),o=e.getOtherAxis(a),s=a.onZero?0:o.scale[m]()[0],l=o.dim,u="x"===l||"radius"===l?1:0,h=i.stackedOn,c=i.get(l,n);h&&t(h.get(l,n))===t(c);){r=h;break}var d=[];return d[u]=i.get(a.dim,n),d[1-u]=r?r.get(l,n,!0):s,e.dataToPoint(d)}function i(t,e){var i=[];return e.diff(t).add(function(t){i.push({cmd:"+",idx:t})})[z](function(t,e){i.push({cmd:"=",idx:e,idx1:t})})[S](function(t){i.push({cmd:"-",idx:t})}).execute(),i}return function(t,n,r,a,o,s){for(var l=i(t,n),u=[],h=[],c=[],d=[],p=[],m=[],v=[],g=s[f],y=0;y<l[R];y++){var _=l[y],x=!0;switch(_.cmd){case"=":var b=t.getItemLayout(_.idx),w=n.getItemLayout(_.idx1);(isNaN(b[0])||isNaN(b[1]))&&(b=w.slice()),u.push(b),h.push(w),c.push(r[_.idx]),d.push(a[_.idx1]),v.push(n.getRawIndex(_.idx1));break;case"+":var T=_.idx;u.push(o.dataToPoint([n.get(g[0],T,!0),n.get(g[1],T,!0)])),h.push(n.getItemLayout(T).slice()),c.push(e(o,n,T)),d.push(a[T]),v.push(n.getRawIndex(T));break;case"-":var T=_.idx,M=t.getRawIndex(T);M!==T?(u.push(t.getItemLayout(T)),h.push(s.dataToPoint([t.get(g[0],T,!0),t.get(g[1],T,!0)])),c.push(r[T]),d.push(e(s,t,T)),v.push(M)):x=!1}x&&(p.push(_),m.push(m[R]))}m.sort(function(t,e){return v[t]-v[e]});for(var A=[],C=[],S=[],P=[],I=[],y=0;y<m[R];y++){var T=m[y];A[y]=u[T],C[y]=h[T],S[y]=c[T],P[y]=d[T],I[y]=p[T]}return{current:A,next:C,stackedOnCurrent:S,stackedOnNext:P,status:I}}}),e("echarts/chart/line/poly",[le,"zrender/graphic/Path",V],function(t){function e(t){return isNaN(t[0])||isNaN(t[1])}function i(t,i,n,r,f,p,m,v,g,y,_){for(var x=0,b=n,w=0;r>w;w++){var T=i[b];if(b>=f||0>b)break;if(e(T)){if(_){b+=p;continue}break}if(b===n)t[p>0?"moveTo":"lineTo"](T[0],T[1]),u(c,T);else if(g>0){var M=b+p,A=i[M];if(_)for(;A&&e(i[M]);)M+=p,A=i[M];var C=.5,S=i[x],A=i[M];if(!A||e(A))u(d,T);else{e(A)&&!_&&(A=T),a.sub(h,A,S);var P,I;if("x"===y||"y"===y){var z="x"===y?0:1;P=Math.abs(T[z]-S[z]),I=Math.abs(T[z]-A[z])}else P=a.dist(T,S),I=a.dist(T,A);C=I/(I+P),l(d,T,h,-g*(1-C))}o(c,c,v),s(c,c,m),o(d,d,v),s(d,d,m),t.bezierCurveTo(c[0],c[1],d[0],d[1],T[0],T[1]),l(c,T,h,g*C)}else t.lineTo(T[0],T[1]);x=b,b+=p}return w}function n(t,e){var i=[1/0,1/0],n=[-1/0,-1/0];if(e)for(var r=0;r<t[R];r++){var a=t[r];a[0]<i[0]&&(i[0]=a[0]),a[1]<i[1]&&(i[1]=a[1]),a[0]>n[0]&&(n[0]=a[0]),a[1]>n[1]&&(n[1]=a[1])}return{min:e?i:n,max:e?n:i}}var r=t("zrender/graphic/Path"),a=t(V),o=a.min,s=a.max,l=a.scaleAndAdd,u=a.copy,h=[],c=[],d=[];return{Polyline:r[C]({type:"ec-polyline",shape:{points:[],smooth:0,smoothConstraint:!0,smoothMonotone:null,connectNulls:!1},style:{fill:null,stroke:"#000"},buildPath:function(t,r){var a=r.points,o=0,s=a[R],l=n(a,r.smoothConstraint);if(r.connectNulls){for(;s>0&&e(a[s-1]);s--);for(;s>o&&e(a[o]);o++);}for(;s>o;)o+=i(t,a,o,s,s,1,l.min,l.max,r.smooth,r.smoothMonotone,r.connectNulls)+1}}),Polygon:r[C]({type:"ec-polygon",shape:{points:[],stackedOnPoints:[],smooth:0,stackedOnSmooth:0,smoothConstraint:!0,smoothMonotone:null,connectNulls:!1},buildPath:function(t,r){var a=r.points,o=r.stackedOnPoints,s=0,l=a[R],u=r.smoothMonotone,h=n(a,r.smoothConstraint),c=n(o,r.smoothConstraint);if(r.connectNulls){for(;l>0&&e(a[l-1]);l--);for(;l>s&&e(a[s]);s++);}for(;l>s;){var d=i(t,a,s,l,l,1,h.min,h.max,r.smooth,u,r.connectNulls);i(t,o,s+d-1,d,l,-1,c.min,c.max,r.stackedOnSmooth,u,r.connectNulls),s+=d+1,t.closePath()}}})}}),e("echarts/coord/cartesian/Cartesian",[le,se],function(t){function e(t){return this._axes[t]}var i=t(se),n=function(t){this._axes={},this._dimList=[],this.name=t||""};return n[Z]={constructor:n,type:"cartesian",getAxis:function(t){return this._axes[t]},getAxes:function(){return i.map(this._dimList,e,this)},getAxesByScale:function(t){return t=t[F](),i.filter(this.getAxes(),function(e){return e.scale.type===t})},addAxis:function(t){var e=t.dim;this._axes[e]=t,this._dimList.push(e)},dataToCoord:function(t){return this._dataCoordConvert(t,"dataToCoord")},coordToData:function(t){return this._dataCoordConvert(t,"coordToData")},_dataCoordConvert:function(t,e){for(var i=this._dimList,n=t instanceof Array?[]:{},r=0;r<i[R];r++){var a=i[r],o=this._axes[a];n[a]=o[e](t[a])}return n}},n}),e("echarts/component/helper/RoamController",[le,"zrender/mixin/Eventful",se,"zrender/core/event","./interactionMutex"],function(t){function e(t){if(!t[Q]||!t[Q].draggable){var e=t.offsetX,i=t.offsetY;this.containsPoint&&this.containsPoint(e,i)&&(this._x=e,this._y=i,this._dragging=!0)}}function i(t){if(this._dragging&&(h.stop(t.event),"pinch"!==t.gestureEvent)){if(c.isTaken(this._zr,"globalPan"))return;var e=t.offsetX,i=t.offsetY,n=this._x,r=this._y,a=e-n,o=i-r;this._x=e,this._y=i;var s=this[Q];if(s){var l=s[W];l[0]+=a,l[1]+=o,s.dirty()}h.stop(t.event),this.trigger("pan",a,o,n,r,e,i)}}function n(){this._dragging=!1}function r(t){var e=t.wheelDelta>0?1.1:1/1.1;o.call(this,t,e,t.offsetX,t.offsetY)}function a(t){if(!c.isTaken(this._zr,"globalPan")){var e=t.pinchScale>1?1.1:1/1.1;o.call(this,t,e,t.pinchX,t.pinchY)}}function o(t,e,i,n){if(this.containsPoint&&this.containsPoint(i,n)){h.stop(t.event);var r=this[Q],a=this.zoomLimit;if(r){var o=r[W],s=r.scale,l=this.zoom=this.zoom||1;if(l*=e,a){var u=a.min||0,c=a.max||1/0;l=Math.max(Math.min(c,l),u)}var d=l/this.zoom;this.zoom=l,o[0]-=(i-o[0])*(d-1),o[1]-=(n-o[1])*(d-1),s[0]*=d,s[1]*=d,r.dirty()}this.trigger("zoom",e,i,n)}}function s(t,o){this[Q]=o,this.containsPoint,this.zoomLimit,this.zoom,this._zr=t;var s=u.bind,h=s(e,this),c=s(i,this),d=s(n,this),f=s(r,this),p=s(a,this);l.call(this),this.setContainsPoint=function(t){this.containsPoint=t},this.enable=function(e){this.disable(),null==e&&(e=!0),(e===!0||"move"===e||"pan"===e)&&(t.on("mousedown",h),t.on("mousemove",c),t.on("mouseup",d)),(e===!0||"scale"===e||"zoom"===e)&&(t.on("mousewheel",f),t.on("pinch",p))},this.disable=function(){t.off("mousedown",h),t.off("mousemove",c),t.off("mouseup",d),t.off("mousewheel",f),t.off("pinch",p)},this[M]=this.disable,this.isDragging=function(){return this._dragging},this.isPinching=function(){return this._pinching}}var l=t("zrender/mixin/Eventful"),u=t(se),h=t("zrender/core/event"),c=t("./interactionMutex");return u.mixin(s,l),s}),e("echarts/util/symbol",[le,"./graphic","zrender/core/BoundingRect"],function(t){var e=t("./graphic"),i=t("zrender/core/BoundingRect"),n=e.extendShape({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var i=e.cx,n=e.cy,r=e.width/2,a=e[K]/2;t.moveTo(i,n-a),t.lineTo(i+r,n+a),t.lineTo(i-r,n+a),t.closePath()}}),r=e.extendShape({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var i=e.cx,n=e.cy,r=e.width/2,a=e[K]/2;t.moveTo(i,n-a),t.lineTo(i+r,n),t.lineTo(i,n+a),t.lineTo(i-r,n),t.closePath()}}),a=e.extendShape({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var i=e.x,n=e.y,r=e.width/5*3,a=Math.max(r,e[K]),o=r/2,s=o*o/(a-o),l=n-a+o+s,u=Math.asin(s/o),h=Math.cos(u)*o,c=Math.sin(u),d=Math.cos(u);t.arc(i,l,o,Math.PI-u,2*Math.PI+u);var f=.6*o,p=.7*o;t.bezierCurveTo(i+h-c*f,l+s+d*f,i,n-p,i,n),t.bezierCurveTo(i,n-p,i-h+c*f,l+s+d*f,i-h,l+s),t.closePath()}}),o=e.extendShape({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var i=e[K],n=e.width,r=e.x,a=e.y,o=n/3*2;t.moveTo(r,a),t.lineTo(r+o,a+i),t.lineTo(r,a+i/4*3),t.lineTo(r-o,a+i),t.lineTo(r,a),t.closePath()}}),s={line:e.Line,rect:e.Rect,roundRect:e.Rect,square:e.Rect,circle:e.Circle,diamond:r,pin:a,arrow:o,triangle:n},l={line:function(t,e,i,n,r){r.x1=t,r.y1=e+n/2,r.x2=t+i,r.y2=e+n/2},rect:function(t,e,i,n,r){r.x=t,r.y=e,r.width=i,r[K]=n},roundRect:function(t,e,i,n,r){r.x=t,r.y=e,r.width=i,r[K]=n,r.r=Math.min(i,n)/4},square:function(t,e,i,n,r){var a=Math.min(i,n);r.x=t,r.y=e,r.width=a,r[K]=a},circle:function(t,e,i,n,r){r.cx=t+i/2,r.cy=e+n/2,r.r=Math.min(i,n)/2},diamond:function(t,e,i,n,r){r.cx=t+i/2,r.cy=e+n/2,r.width=i,r[K]=n},pin:function(t,e,i,n,r){r.x=t+i/2,r.y=e+n/2,r.width=i,r[K]=n},arrow:function(t,e,i,n,r){r.x=t+i/2,r.y=e+n/2,r.width=i,r[K]=n},triangle:function(t,e,i,n,r){r.cx=t+i/2,r.cy=e+n/2,r.width=i,r[K]=n}},u={};for(var h in s)s.hasOwnProperty(h)&&(u[h]=new s[h]);var c=e.extendShape({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},beforeBrush:function(){var t=this.style,e=this.shape;"pin"===e.symbolType&&"inside"===t.textPosition&&(t.textPosition=["50%","40%"],t[ee]=X,t.textVerticalAlign=j)},buildPath:function(t,e,i){var n=e.symbolType,r=u[n];"none"!==e.symbolType&&(r||(n="rect",r=u[n]),l[n](e.x,e.y,e.width,e[K],r.shape),r.buildPath(t,r.shape,i))}}),d=function(t){if("image"!==this.type){var e=this.style,i=this.shape;i&&"line"===i.symbolType?e.stroke=t:this.__isEmptyBrush?(e.stroke=t,e.fill="#fff"):(e.fill&&(e.fill=t),e.stroke&&(e.stroke=t)),this.dirty(!1)
}},f={createSymbol:function(t,n,r,a,o,s){var l=0===t[O]("empty");l&&(t=t.substr(5,1)[F]()+t.substr(6));var u;return u=0===t[O]("image://")?new e.Image({style:{image:t.slice(8),x:n,y:r,width:a,height:o}}):0===t[O]("path://")?e.makePath(t.slice(7),{},new i(n,r,a,o)):new c({shape:{symbolType:t,x:n,y:r,width:a,height:o}}),u.__isEmptyBrush=l,u.setColor=d,u.setColor(s),u}};return f}),e("echarts/coord/cartesian/axisLabelInterval",[le,se,"../axisHelper"],function(t){var e=t(se),i=t("../axisHelper");return function(t){var n=t.model,r=n[ne]("axisLabel"),a=r.get("interval");return t.type!==l||"auto"!==a?"auto"===a?0:a:i.getAxisLabelInterval(e.map(t.scale.getTicks(),t.dataToCoord,t),n.getFormattedLabels(),r[ne](ie)[te](),t.isHorizontal())}}),e("echarts/coord/Axis",[le,"../util/number",se],function(t){function e(t,e){var i=t[1]-t[0],n=e,r=i/n/2;t[0]+=r,t[1]-=r}var i=t("../util/number"),n=i.linearMap,r=t(se),a=[0,1],o=function(t,e,i){this.dim=t,this.scale=e,this._extent=i||[0,0],this.inverse=!1,this.onBand=!1};return o[Z]={constructor:o,contain:function(t){var e=this._extent,i=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]);return t>=i&&n>=t},containData:function(t){return this[v](this.dataToCoord(t))},getExtent:function(){var t=this._extent.slice();return t},getPixelPrecision:function(t){return i.getPixelPrecision(t||this.scale[m](),this._extent)},setExtent:function(t,e){var i=this._extent;i[0]=t,i[1]=e},dataToCoord:function(t,i){var r=this._extent,o=this.scale;return t=o.normalize(t),this.onBand&&o.type===d&&(r=r.slice(),e(r,o.count())),n(t,a,r,i)},coordToData:function(t,i){var r=this._extent,o=this.scale;this.onBand&&o.type===d&&(r=r.slice(),e(r,o.count()));var s=n(t,r,a,i);return this.scale.scale(s)},getTicksCoords:function(t){if(this.onBand&&!t){for(var e=this.getBands(),i=[],n=0;n<e[R];n++)i.push(e[n][0]);return e[n-1]&&i.push(e[n-1][1]),i}return r.map(this.scale.getTicks(),this.dataToCoord,this)},getLabelsCoords:function(){return r.map(this.scale.getTicks(),this.dataToCoord,this)},getBands:function(){for(var t=this[m](),e=[],i=this.scale.count(),n=t[0],r=t[1],a=r-n,o=0;i>o;o++)e.push([a*o/i+n,a*(o+1)/i+n]);return e},getBandWidth:function(){var t=this._extent,e=this.scale[m](),i=e[1]-e[0]+(this.onBand?1:0);0===i&&(i=1);var n=Math.abs(t[1]-t[0]);return Math.abs(n)/i}},o}),e("echarts/component/helper/interactionMutex",[le,"../../echarts"],function(t){function e(t){return t[i]||(t[i]={})}var i="\x00_ec_interaction_mutex",n={take:function(t,i,n){var r=e(t);r[i]=n},release:function(t,i,n){var r=e(t),a=r[i];a===n&&(r[i]=null)},isTaken:function(t,i){return!!e(t)[i]}};return t("../../echarts").registerAction({type:"takeGlobalCursor",event:"globalCursorTaken",update:"update"},function(){}),n}),e("echarts/coord/cartesian/AxisModel",[le,"../../model/Component",se,"../axisModelCreator","../axisModelCommonMixin","../axisModelZoomMixin"],function(t){function e(t,e){return e.type||(e.data?l:"value")}var i=t("../../model/Component"),n=t(se),r=t("../axisModelCreator"),a=i[C]({type:"cartesian2dAxis",axis:null,init:function(){a.superApply(this,"init",arguments),this.resetRange()},mergeOption:function(){a.superApply(this,"mergeOption",arguments),this.resetRange()},restoreData:function(){a.superApply(this,"restoreData",arguments),this.resetRange()},findGridModel:function(){return this[s][o]({mainType:"grid",index:this.get("gridIndex"),id:this.get("gridId")})[0]}});n.merge(a[Z],t("../axisModelCommonMixin")),n.merge(a[Z],t("../axisModelZoomMixin"));var u={offset:0};return r("x",a,e,u),r("y",a,e,u),a}),e("echarts/coord/axisModelCommonMixin",[le,se,"./axisHelper"],function(t){function e(t){return r[P](t)&&null!=t.value?t.value:t}function i(){return this.get("type")===l&&r.map(this.get("data"),e)}function n(){return a.getFormattedLabels(this.axis,this.get("axisLabel.formatter"))}var r=t(se),a=t("./axisHelper");return{getFormattedLabels:n,getCategories:i}}),e("echarts/coord/axisModelCreator",[le,"./axisDefault",se,"../model/Component","../util/layout"],function(t){var e=t("./axisDefault"),i=t(se),n=t("../model/Component"),r=t("../util/layout"),a=["value",l,"time","log"];return function(t,o,s,l){i.each(a,function(n){o[C]({type:t+"Axis."+n,mergeDefaultAndTheme:function(e,a){var o=this.layoutMode,l=o?r.getLayoutParams(e):{},u=a.getTheme();i.merge(e,u.get(n+"Axis")),i.merge(e,this.getDefaultOption()),e.type=s(t,e),o&&r.mergeLayoutParam(e,l,o)},defaultOption:i.mergeAll([{},e[n+"Axis"],l],!0)})}),n.registerSubTypeDefaulter(t+"Axis",i.curry(s,t))}}),e("echarts/coord/axisModelZoomMixin",[le],function(){return{getMin:function(){var t=this[u],e=null!=t.rangeStart?t.rangeStart:t.min;return e instanceof Date&&(e=+e),e},getMax:function(){var t=this[u],e=null!=t.rangeEnd?t.rangeEnd:t.max;return e instanceof Date&&(e=+e),e},getNeedCrossZero:function(){var t=this[u];return null!=t.rangeStart||null!=t.rangeEnd?!1:!t.scale},setRange:function(t,e){this[u].rangeStart=t,this[u].rangeEnd=e},resetRange:function(){this[u].rangeStart=this[u].rangeEnd=null}}}),e("echarts/coord/axisDefault",[le,se],function(t){var e=t(se),i={show:!0,zlevel:0,z:0,inverse:!1,name:"",nameLocation:"end",nameRotate:null,nameTruncate:{maxWidth:null,ellipsis:"...",placeholder:"."},nameTextStyle:{},nameGap:15,silent:!1,triggerEvent:!1,tooltip:{show:!1},axisLine:{show:!0,onZero:!0,lineStyle:{color:"#333",width:1,type:"solid"}},axisTick:{show:!0,inside:!1,length:5,lineStyle:{width:1}},axisLabel:{show:!0,inside:!1,rotate:0,margin:8,textStyle:{fontSize:12}},splitLine:{show:!0,lineStyle:{color:["#ccc"],width:1,type:"solid"}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.3)","rgba(200,200,200,0.3)"]}}},n=e.merge({boundaryGap:!0,splitLine:{show:!1},axisTick:{alignWithLabel:!1,interval:"auto"},axisLabel:{interval:"auto"}},i),r=e.merge({boundaryGap:[0,0],splitNumber:5},i),a=e[re]({scale:!0,min:"dataMin",max:"dataMax"},r),o=e[re]({logBase:10},r);return o.scale=!0,{categoryAxis:n,valueAxis:r,timeAxis:a,logAxis:o}}),e("echarts/component/axis/AxisView",[le,se,"../../util/graphic","./AxisBuilder","../../echarts"],function(t){function e(t,e){function i(t){var e=n.getAxis(t);return e.toGlobalCoord(e.dataToCoord(0))}var n=t[ae],r=e.axis,a={},o=r[W],s=r.onZero?"onZero":o,l=r.dim,u=n.getRect(),h=[u.x,u.x+u.width,u.y,u.y+u[K]],c=e.get("offset")||0,d={x:{top:h[2]-c,bottom:h[3]+c},y:{left:h[0]-c,right:h[1]+c}};d.x.onZero=Math.max(Math.min(i("y"),d.x[U]),d.x.top),d.y.onZero=Math.max(Math.min(i("x"),d.y.right),d.y.left),a[W]=["y"===l?d.y[s]:h[0],"x"===l?d.x[s]:h[3]],a.rotation=Math.PI/2*("x"===l?0:1);var f={top:-1,bottom:1,left:-1,right:1};a.labelDirection=a.tickDirection=a.nameDirection=f[o],r.onZero&&(a.labelOffset=d[l][o]-d[l].onZero),e[ne]("axisTick").get("inside")&&(a.tickDirection=-a.tickDirection),e[ne]("axisLabel").get("inside")&&(a.labelDirection=-a.labelDirection);var p=e[ne]("axisLabel").get("rotate");return a.labelRotation="top"===s?-p:p,a.labelInterval=r.getLabelInterval(),a.z2=1,a}var i=t(se),n=t("../../util/graphic"),r=t("./AxisBuilder"),a=r.ifIgnoreOnTick,o=r.getInterval,s=["axisLine","axisLabel","axisTick","axisName"],l=["splitArea","splitLine"],u=t("../../echarts").extendComponentView({type:"axis",render:function(t){this.group[oe]();var a=this._axisGroup;if(this._axisGroup=new n.Group,this.group.add(this._axisGroup),t.get("show")){var o=t.findGridModel(),u=e(o,t),h=new r(t,u);i.each(s,h.add,h),this._axisGroup.add(h.getGroup()),i.each(l,function(e){t.get(e+".show")&&this["_"+e](t,o,u.labelInterval)},this),n.groupTransition(a,this._axisGroup,t)}},_splitLine:function(t,e,r){var s=t.axis,l=t[ne]("splitLine"),u=l[ne]("lineStyle"),h=u.get("color"),c=o(l,r);h=i[_](h)?h:[h];for(var d=e[ae].getRect(),f=s.isHorizontal(),p=0,m=s.getTicksCoords(),v=s.scale.getTicks(),g=[],y=[],x=u.getLineStyle(),b=0;b<m[R];b++)if(!a(s,b,c)){var w=s.toGlobalCoord(m[b]);f?(g[0]=w,g[1]=d.y,y[0]=w,y[1]=d.y+d[K]):(g[0]=d.x,g[1]=w,y[0]=d.x+d.width,y[1]=w);var T=p++%h[R];this._axisGroup.add(new n.Line(n.subPixelOptimizeLine({anid:"line_"+v[b],shape:{x1:g[0],y1:g[1],x2:y[0],y2:y[1]},style:i[re]({stroke:h[T]},x),silent:!0})))}},_splitArea:function(t,e,r){var s=t.axis,l=t[ne]("splitArea"),u=l[ne]("areaStyle"),h=u.get("color"),c=e[ae].getRect(),d=s.getTicksCoords(),f=s.scale.getTicks(),p=s.toGlobalCoord(d[0]),m=s.toGlobalCoord(d[0]),v=0,g=o(l,r),y=u.getAreaStyle();h=i[_](h)?h:[h];for(var x=1;x<d[R];x++)if(!a(s,x,g)){var b,w,T,M,A=s.toGlobalCoord(d[x]);s.isHorizontal()?(b=p,w=c.y,T=A-b,M=c[K]):(b=c.x,w=m,T=c.width,M=A-w);var C=v++%h[R];this._axisGroup.add(new n.Rect({anid:"area_"+f[x],shape:{x:b,y:w,width:T,height:M},style:i[re]({fill:h[C]},y),silent:!0})),p=b+T,m=w+M}}});u[C]({type:"xAxis"}),u[C]({type:"yAxis"})}),e("echarts/component/axis/AxisBuilder",[le,se,"../../util/format","../../util/graphic","../../model/Model","../../util/number",V],function(t){function e(t){var e={componentType:t.mainType};return e[t.mainType+"Index"]=t.componentIndex,e}function i(t,e,i){var n,r,a=g(e-t.rotation);return _(a)?(r=i>0?"top":U,n=X):_(a-T)?(r=i>0?U:"top",n=X):(r=j,n=a>0&&T>a?i>0?"right":"left":i>0?"left":"right"),{rotation:a,textAlign:n,verticalAlign:r}}function r(t,e,i,n){var r,a,o=g(i-t.rotation),s=n[0]>n[1],l="start"===e&&!s||"start"!==e&&s;return _(o-T/2)?(a=l?U:"top",r=X):_(o-1.5*T)?(a=l?"top":U,r=X):(a=j,r=1.5*T>o&&o>T/2?l?"left":"right":l?"right":"left"),{rotation:o,textAlign:r,verticalAlign:a}}function o(t){var e=t.get("tooltip");return t.get("silent")||!(t.get("triggerEvent")||e&&e.show)}var u=t(se),h=t("../../util/format"),c=t("../../util/graphic"),f=t("../../model/Model"),v=t("../../util/number"),g=v.remRadian,_=v.isRadianAroundZero,x=t(V),b=x[n],w=u[a],T=Math.PI,M=function(t,e){this.opt=e,this.axisModel=t,u[re](e,{labelOffset:0,nameDirection:1,tickDirection:1,labelDirection:1,silent:!0}),this.group=new c.Group;var i=new c.Group({position:e[W].slice(),rotation:e.rotation});i.updateTransform(),this._transform=i.transform,this._dumbGroup=i};M[Z]={constructor:M,hasBuilder:function(t){return!!A[t]},add:function(t){A[t].call(this)},getGroup:function(){return this.group}};var A={axisLine:function(){var t=this.opt,e=this.axisModel;if(e.get("axisLine.show")){var i=this.axisModel.axis[m](),n=this._transform,r=[i[0],0],a=[i[1],0];n&&(b(r,r,n),b(a,a,n)),this.group.add(new c.Line(c.subPixelOptimizeLine({anid:"line",shape:{x1:r[0],y1:r[1],x2:a[0],y2:a[1]},style:u[C]({lineCap:"round"},e[ne]("axisLine.lineStyle").getLineStyle()),strokeContainThreshold:t.strokeContainThreshold||5,silent:!0,z2:1})))}},axisTick:function(){var t=this.axisModel;if(t.get("axisTick.show"))for(var e=t.axis,i=t[ne]("axisTick"),n=this.opt,r=i[ne]("lineStyle"),a=i.get(R),o=P(i,n.labelInterval),s=e.getTicksCoords(i.get("alignWithLabel")),l=e.scale.getTicks(),h=[],d=[],f=this._transform,p=0;p<s[R];p++)if(!S(e,p,o)){var m=s[p];h[0]=m,h[1]=0,d[0]=m,d[1]=n.tickDirection*a,f&&(b(h,h,f),b(d,d,f)),this.group.add(new c.Line(c.subPixelOptimizeLine({anid:"tick_"+l[p],shape:{x1:h[0],y1:h[1],x2:d[0],y2:d[1]},style:u[re](r.getLineStyle(),{stroke:t.get("axisLine.lineStyle.color")}),z2:2,silent:!0})))}},axisLabel:function(){function t(t,e){var i=t&&t[J]().clone(),r=e&&e[J]().clone();return i&&r?(i[n](t.getLocalTransform()),r[n](e.getLocalTransform()),i.intersect(r)):void 0}var r=this.opt,a=this.axisModel,h=w(r.axisLabelShow,a.get("axisLabel.show"));if(h){var d=a.axis,m=a[ne]("axisLabel"),v=m[ne](ie),g=m.get("margin"),_=d.scale.getTicks(),x=a.getFormattedLabels(),b=w(r.labelRotation,m.get("rotate"))||0;b=b*T/180;var M=i(r,b,r.labelDirection),A=a.get("data"),C=[],P=o(a),I=a.get("triggerEvent");if(u.each(_,function(t,i){if(!S(d,i,r.labelInterval)){var n=v;A&&A[t]&&A[t][ie]&&(n=new f(A[t][ie],v,a[s]));var o=n.getTextColor()||a.get("axisLine.lineStyle.color"),l=d.dataToCoord(t),u=[l,r.labelOffset+r.labelDirection*g],h=d.scale[p](t),m=new c.Text({anid:"label_"+t,style:{text:x[i],textAlign:n.get("align",!0)||M[ee],textVerticalAlign:n.get("baseline",!0)||M.verticalAlign,textFont:n[te](),fill:typeof o===y?o(h):o},position:u,rotation:M.rotation,silent:P,z2:10});I&&(m.eventData=e(a),m.eventData.targetType="axisLabel",m.eventData.value=h),this._dumbGroup.add(m),m.updateTransform(),C.push(m),this.group.add(m),m.decomposeTransform()}},this),d.type!==l){if(a.getMin?a.getMin():a.get("min")){var z=C[0],k=C[1];t(z,k)&&(z[E]=!0)}if(a.getMax?a.getMax():a.get("max")){var L=C[C[R]-1],D=C[C[R]-2];t(D,L)&&(L[E]=!0)}}}},axisName:function(){var t=this.opt,n=this.axisModel,a=w(t.axisName,n.get("name"));if(a){var s,l=n.get("nameLocation"),d=t.nameDirection,f=n[ne]("nameTextStyle"),p=n.get("nameGap")||0,v=this.axisModel.axis[m](),g=v[0]>v[1]?-1:1,y=["start"===l?v[0]-g*p:"end"===l?v[1]+g*p:(v[0]+v[1])/2,l===j?t.labelOffset+d*p:0],_=n.get("nameRotate");null!=_&&(_=_*T/180);var x;l===j?s=i(t,null!=_?_:t.rotation,d):(s=r(t,l,_||0,v),x=t.axisNameAvailableWidth,null!=x&&(x=Math.abs(x/Math.sin(s.rotation)),!isFinite(x)&&(x=null)));var b=f[te](),M=n.get("nameTruncate",!0)||{},A=M.ellipsis,S=w(M.maxWidth,x),P=null!=A&&null!=S?h.truncateText(a,S,b,A,{minChar:2,placeholder:M.placeholder}):a,I=n.get("tooltip",!0),z=n.mainType,k={componentType:z,name:a,$vars:["name"]};k[z+"Index"]=n.componentIndex;var L=new c.Text({anid:"name",__fullText:a,__truncatedText:P,style:{text:P,textFont:b,fill:f.getTextColor()||n.get("axisLine.lineStyle.color"),textAlign:s[ee],textVerticalAlign:s.verticalAlign},position:y,rotation:s.rotation,silent:o(n),z2:1,tooltip:I&&I.show?u[C]({content:a,formatter:function(){return a},formatterParams:k},I):null});n.get("triggerEvent")&&(L.eventData=e(n),L.eventData.targetType="axisName",L.eventData.name=a),this._dumbGroup.add(L),L.updateTransform(),this.group.add(L),L.decomposeTransform()}}},S=M.ifIgnoreOnTick=function(t,e,i){var n,r=t.scale;return r.type===d&&(typeof i===y?(n=r.getTicks()[e],!i(n,r[p](n))):e%(i+1))},P=M.getInterval=function(t,e){var i=t.get("interval");return(null==i||"auto"==i)&&(i=e),i};return M}),e("zrender",["zrender/zrender"],function(t){return t}),e("echarts",["echarts/echarts"],function(t){return t});var ue=t("echarts");return ue.graphic=t("echarts/util/graphic"),ue.number=t("echarts/util/number"),ue.format=t("echarts/util/format"),t("echarts/chart/line"),t("echarts/component/grid"),t("echarts/component/title"),t("echarts/component/tooltip"),t("echarts/component/dataZoom"),t("echarts/component/graphic"),ue});