<?php 
//require_once ("/etc/steelconf/config/isholiday.php");

include_once( APP_DIR."/sgtieshuicb/sgtieshuicbBaseAction.inc.php" );
class  sgtieshuicbAction extends sgtieshuicbBaseAction
{
    public function __construct()
    {
        parent::__construct();
    } 
	
	public function jstscbweb($params)
	{
		$get_sg_ChengBenParameter=$params;
		$date=$params['date'];
		$mc_type=$params['mc_type']!=''?$params['mc_type']:2;
		if($params['type']==20) {
			$shuju=$this->jstsmethod20($get_sg_ChengBenParameter,$date);
		} else {
			$shuju=$this->jstsmethod($get_sg_ChengBenParameter,$date);
		}

		if($params['isSave'] == "1")
		{	
				$a=$shuju['a'];
				$b=$shuju['b'];
				$c=$shuju['c'];
				$d=$shuju['d'];
				$chenben=$shuju['chenben'];
		        $chenben_tax=$shuju['chenben_tax'];
				$price_arr=$shuju['price_arr'];
					$type=$params['type'];
					$smalltype=($type-1)*30;
				//$cntaxrateinfo = $this->maindao->get_master_cntaxrate($date);//税率
				//$zzsl = $cntaxrateinfo['rvalue'];
				$zzsl = $this->maindao->get_master_cntaxrate($date,1);//税率
				$sql = "select id from sg_HangYeChengBenIndex where type='".(1+$smalltype)."' and mc_type='".$mc_type."' and ndate='".$date."' order by id desc" ;
				
				$query = $this->_dao->query($sql);
				
				
				if($query!=null)
				{
					$sql="update sg_HangYeChengBenIndex set price='".$price_arr[0]."',chenben_ton='".$a."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(1+$smalltype);
					$this->_dao->execute($sql);
					$sql="update sg_HangYeChengBenIndex set price='".$price_arr[1]."',chenben_ton='".$b."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(2+$smalltype); 
					$this->_dao->execute($sql);
					$sql="update sg_HangYeChengBenIndex set price='".$price_arr[2]."',chenben_ton='".$c."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(3+$smalltype); 
					$this->_dao->execute($sql);
					$sql="update sg_HangYeChengBenIndex set price='".$price_arr[3]."',chenben_ton='".$d."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(4+$smalltype); 
					$this->_dao->execute($sql);
					$sql="update sg_HangYeChengBenIndex set chenben='".$chenben."',chenben_tax='".$chenben_tax."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(5+$smalltype);  
					$this->_dao->execute($sql);
					//echo "更新铁水成本<br/>";
				}
				else
				{
					$sql="insert into `sg_HangYeChengBenIndex`(type,price,chenben_ton,ndate,mc_type) values(".(1+$smalltype).",'".$price_arr[0]."','".$a."','".$date."','".$mc_type."')";
					//echo $sql."<br/>";
					$this->_dao->execute($sql);
					$sql="insert into `sg_HangYeChengBenIndex`(type,price,chenben_ton,ndate,mc_type) values(".(2+$smalltype).",'".$price_arr[1]."','".$b."','".$date."','".$mc_type."')";
					//echo $sql."<br/>";
					$this->_dao->execute($sql);
					$sql="insert into `sg_HangYeChengBenIndex`(type,price,chenben_ton,ndate,mc_type) values(".(3+$smalltype).",'".$price_arr[2]."','".$c."','".$date."','".$mc_type."')";
					//echo $sql."<br/>";
					$this->_dao->execute($sql);
					$sql="insert into `sg_HangYeChengBenIndex`(type,price,chenben_ton,ndate,mc_type) values(".(4+$smalltype).",'".$price_arr[3]."','".$d."','".$date."','".$mc_type."')";
					//echo $sql."<br/>";
					$this->_dao->execute($sql);
					$sql="insert into `sg_HangYeChengBenIndex`(type,chenben,chenben_tax,ndate,mc_type) values(".(5+$smalltype).",'".$chenben."','".$chenben_tax."','".$date."','".$mc_type."')";
					//echo $sql."<br/>";
					$this->_dao->execute($sql);
				} 
				
				
	    }
		if($params['isSave'] == "1")
			return $shuju;
		else
		{
			echo json_encode($shuju);
			exit;
		}
	}
	public function jsgpcbweb($params)
	{
		$get_sg_ChengBenParameter2=$params;
		$date=$params['date'];
	    $mc_type=$params['mc_type']!=''?$params['mc_type']:2;;
		$type=$params['type'];
		$busbankrateinfo = $this->_dao->get_drc_hbh_txl($date);//贴现率
		$busbankrate = $busbankrateinfo['rd3'];

		//$cntaxrateinfo = $this->maindao->get_master_cntaxrate($date);//税率
		//$zzsl = $cntaxrateinfo['rvalue'];
		$zzsl = $this->maindao->get_master_cntaxrate($date,1);//税率
		if($type<6||$type>7)
		{
			$smalltype=($type-1)*30;
			$sql = "select id,chenben from sg_HangYeChengBenIndex where type='".(5+$smalltype)."' and mc_type='".$mc_type."' and ndate='".$date."' order by id desc limit 1" ;
			$query = $this->_dao->getrow($sql);
			$tscb=$query ['chenben'];
			
		
			$pricearr=array();
		if($get_sg_ChengBenParameter2['FgPriceidA'])
		{
			$pricearr[0]=$get_sg_ChengBenParameter2['FgPriceidA'];
		}
		if($get_sg_ChengBenParameter2['GmPriceidB'])
		{
			$pricearr[1]=$get_sg_ChengBenParameter2['GmPriceidB'];
		}
		if($get_sg_ChengBenParameter2['GtPriceidC'])
		{
			$pricearr[2]=$get_sg_ChengBenParameter2['GtPriceidC'];
		}
		if($get_sg_ChengBenParameter2['FdPriceidD'])
		{
			$pricearr[3]=$get_sg_ChengBenParameter2['FdPriceidD'];
		}
		$priceidstr=  implode( ",", $pricearr );

		$price_arr=$this->maindao->getpricebypricestr($priceidstr,$date);
		//  print_r($price_arr);
		$X=$this->get_F1($tscb,$get_sg_ChengBenParameter2);
		$a=$this->get_A2($price_arr[$pricearr[0]],$get_sg_ChengBenParameter2,$zzsl,$busbankrate);//京唐港现货矿PB
		$b=$this->get_B2($price_arr[$pricearr[1]],$get_sg_ChengBenParameter2,$zzsl,$busbankrate);//唐山66%铁精粉
		$c=$this->get_C2($price_arr[$pricearr[1]],$get_sg_ChengBenParameter2,$zzsl,$busbankrate);//唐山66%铁精粉
		$d=$this->get_D2($price_arr[$pricearr[2]],$get_sg_ChengBenParameter2,$zzsl,$busbankrate);//唐山66%铁精粉
		$e=$this->get_E2($price_arr[$pricearr[3]],$get_sg_ChengBenParameter2,$zzsl,$busbankrate);//唐山66%铁精粉
		$PTPCB=Round($X+$a+$b+$d+$get_sg_ChengBenParameter2['DlrgMake']+$get_sg_ChengBenParameter2['FuLiao']-$get_sg_ChengBenParameter2['GangZhaHuiShou']);

		$LWPCB=Round($X+$a+$c+$e+$get_sg_ChengBenParameter2['DlrgMake']+$get_sg_ChengBenParameter2['FuLiao']-$get_sg_ChengBenParameter2['GangZhaHuiShou']);
		if($params['isSave'] == "1")
		{
			$sql = "select id from sg_HangYeChengBenIndex where type='".(6+$smalltype)."' and mc_type='".$mc_type."'  and ndate='".$date."' order by id desc" ;
			$query = $this->_dao->query($sql);
			if($query!=null)
			{
				$sql="update sg_HangYeChengBenIndex set price='".$price_arr[$pricearr[0]]."',chenben_ton='".$a."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(6+$smalltype);
				$this->_dao->execute($sql);
				$sql="update sg_HangYeChengBenIndex set price='".$price_arr[$pricearr[1]]."',chenben1='".$b."',chenben2='".$c."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(7+$smalltype); 
				$this->_dao->execute($sql);
				$sql="update sg_HangYeChengBenIndex set price='".$price_arr[$pricearr[2]]."',chenben1='".$d."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(8+$smalltype); 
				$this->_dao->execute($sql);
				$sql="update sg_HangYeChengBenIndex set price='".$price_arr[$pricearr[3]]."',chenben2='".$e."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(9+$smalltype); 
				$this->_dao->execute($sql);
				$sql="update sg_HangYeChengBenIndex set chenben='".$PTPCB."',chenben_tax='".Round($PTPCB*(1+$zzsl))."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(10+$smalltype);  
				$this->_dao->execute($sql);
				$sql="update sg_HangYeChengBenIndex set chenben='".$LWPCB."',chenben_tax='".Round($LWPCB*(1+$zzsl))."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(11+$smalltype);  
				$this->_dao->execute($sql);
			}
			else
			{
				$sql="insert into `sg_HangYeChengBenIndex`(type,price,chenben_ton,ndate,mc_type) values(".(6+$smalltype).",'".$price_arr[$pricearr[0]]."','".$a."','".$date."','".$mc_type."')";
				//echo $sql."<br/>";
				$this->_dao->execute($sql);
				$sql="insert into `sg_HangYeChengBenIndex`(type,price,chenben1,chenben2,ndate,mc_type) values(".(7+$smalltype).",'".$price_arr[$pricearr[1]]."','".$b."','".$c."','".$date."','".$mc_type."')";
				//echo $sql."<br/>";
				$this->_dao->execute($sql);
				$sql="insert into `sg_HangYeChengBenIndex`(type,price,chenben1,ndate,mc_type) values(".(8+$smalltype).",'".$price_arr[$pricearr[2]]."','".$d."','".$date."','".$mc_type."')";
				//echo $sql."<br/>";
				$this->_dao->execute($sql);
				$sql="insert into `sg_HangYeChengBenIndex`(type,price,chenben2,ndate,mc_type) values(".(9+$smalltype).",'".$price_arr[$pricearr[3]]."','".$e."','".$date."','".$mc_type."')";
				//echo $sql."<br/>";
				$this->_dao->execute($sql);
				$sql="insert into `sg_HangYeChengBenIndex`(type,chenben,chenben_tax,ndate,mc_type) values(".(10+$smalltype).",'".$PTPCB."','".Round($PTPCB*(1+$zzsl))."','".$date."','".$mc_type."')";
				//echo $sql."<br/>";
				$this->_dao->execute($sql);
				$sql="insert into `sg_HangYeChengBenIndex`(type,chenben,chenben_tax,ndate,mc_type) values(".(11+$smalltype).",'".$LWPCB."','".Round($LWPCB*(1+$zzsl))."','".$date."','".$mc_type."')";
				//echo $sql."<br/>";
				$this->_dao->execute($sql);
			}     
		}
		
	 }
	 else
	 {
		//$smalltype=($type-1)*30;

		$sql = "select id,chenben from sg_HangYeChengBenIndex where type='".($type==6?"35":"125")."' and mc_type='".$mc_type."' and ndate='".$date."' order by id desc limit 1" ;
		$query = $this->_dao->getrow($sql);
		$tscb=$query ['chenben'];
		 $pricearr=array();
		if($get_sg_ChengBenParameter2['FgPriceidA'])
		{
		   $pricearr[0]=$get_sg_ChengBenParameter2['FgPriceidA'];
		}
		if($get_sg_ChengBenParameter2['GmPriceidB'])
		{
		   $pricearr[1]=$get_sg_ChengBenParameter2['GmPriceidB'];
		}
		if($get_sg_ChengBenParameter2['GtPriceidC'])
		{
		   $pricearr[2]=$get_sg_ChengBenParameter2['GtPriceidC'];
		}
		if($get_sg_ChengBenParameter2['FdPriceidD'])
		{
		   $pricearr[3]=$get_sg_ChengBenParameter2['FdPriceidD'];
		}
		if($get_sg_ChengBenParameter2['CggldjPriceid'])
		{
		   $pricearr[4]=$get_sg_ChengBenParameter2['CggldjPriceid'];
		}
		if($get_sg_ChengBenParameter2['GgldjPriceid'])
		{
		   $pricearr[5]=$get_sg_ChengBenParameter2['GgldjPriceid'];
		}
		$priceidstr=  implode( ",", $pricearr );
 
		$price_arr=$this->maindao->getpricebypricestr($priceidstr,$date);
		$X=$this->get_F1($tscb,$get_sg_ChengBenParameter2);
		$a=$this->get_A2($price_arr[$pricearr[0]],$get_sg_ChengBenParameter2,$zzsl,$busbankrate);//废钢
		$b=$this->get_B2($price_arr[$pricearr[1]],$get_sg_ChengBenParameter2,$zzsl,$busbankrate);//唐山66%铁精粉
		$c=$this->get_C2($price_arr[$pricearr[1]],$get_sg_ChengBenParameter2,$zzsl,$busbankrate);//唐山66%铁精粉
		$d=$this->get_D2($price_arr[$pricearr[2]],$get_sg_ChengBenParameter2,$zzsl,$busbankrate);//唐山66%铁精粉
		$e=$this->get_E2($price_arr[$pricearr[3]],$get_sg_ChengBenParameter2,$zzsl,$busbankrate);//唐山66%铁精粉
		$cgjdj=Round($price_arr[$pricearr[4]]*$get_sg_ChengBenParameter2['DunGangXiaoHao1']);//超高功率电极吨钢成本

		$hjdianfei=$this->_dao->gethjdianfei_bytype($date,$type);
	   if($hjdianfei)
	   {
		$get_sg_ChengBenParameter2['PingDuanDianFei']=$hjdianfei['dta_6'];
	   	$get_sg_ChengBenParameter2['GuDianFei']=$hjdianfei['dta_8'];
	   }
		$gjdj=Round($price_arr[$pricearr[5]]*$get_sg_ChengBenParameter2['DunGangXiaoHao2'],2);//功率电极吨钢成本
		$df=Round($get_sg_ChengBenParameter2['DianHao']*$get_sg_ChengBenParameter2['PingDuanDianFei']);//电费吨钢成本
        $PTPCB=Round($X+$a+$b+$d+$cgjdj+$gjdj+$df+$get_sg_ChengBenParameter2['DlrgMake']+$get_sg_ChengBenParameter2['FuLiao']-$get_sg_ChengBenParameter2['GangZhaHuiShou']);
 
		$LWPCB=Round($X+$a+$c+$e+$cgjdj+$gjdj+$df+$get_sg_ChengBenParameter2['DlrgMake']+$get_sg_ChengBenParameter2['FuLiao']-$get_sg_ChengBenParameter2['GangZhaHuiShou']);
		if($params['isSave'] == "1")
		{
			$sql = "select id from sg_HangYeChengBenIndex where type='".(1+$smalltype)."' and mc_type='".$mc_type."' and ndate='".$date."' order by id desc" ;
			$query = $this->_dao->query($sql);
			if($query!=null)
			{
				$sql="update sg_HangYeChengBenIndex set price='".$price_arr[$pricearr[0]]."',chenben_ton='".$a."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(1+$smalltype);
				$this->_dao->execute($sql);
				$sql="update sg_HangYeChengBenIndex set price='".$price_arr[$pricearr[4]]."',chenben_ton='".$cgjdj."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(2+$smalltype);
				$this->_dao->execute($sql);
				$sql="update sg_HangYeChengBenIndex set price='".$price_arr[$pricearr[5]]."',chenben_ton='".$gjdj."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(3+$smalltype);
				$this->_dao->execute($sql);
				$sql="update sg_HangYeChengBenIndex set chenben_ton='".$df."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(4+$smalltype);
				$this->_dao->execute($sql);

				$sql="update sg_HangYeChengBenIndex set price='".$price_arr[$pricearr[1]]."',chenben1='".$b."',chenben2='".$c."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(5+$smalltype); 
				$this->_dao->execute($sql);
				$sql="update sg_HangYeChengBenIndex set price='".$price_arr[$pricearr[2]]."',chenben1='".$d."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(6+$smalltype); 
				$this->_dao->execute($sql);
				$sql="update sg_HangYeChengBenIndex set price='".$price_arr[$pricearr[3]]."',chenben2='".$e."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(7+$smalltype); 
				$this->_dao->execute($sql);
				$sql="update sg_HangYeChengBenIndex set chenben='".$PTPCB."',chenben_tax='".Round($PTPCB*(1+$zzsl))."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(8+$smalltype);  
				$this->_dao->execute($sql);
				$sql="update sg_HangYeChengBenIndex set chenben='".$LWPCB."',chenben_tax='".Round($LWPCB*(1+$zzsl))."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(9+$smalltype);  
				$this->_dao->execute($sql);
			}
			else
			{
				$sql="insert into `sg_HangYeChengBenIndex`(type,price,chenben_ton,ndate,mc_type) values(".(1+$smalltype).",'".$price_arr[$pricearr[0]]."','".$a."','".$date."','".$mc_type."')";
				//echo $sql."<br/>";
				$this->_dao->execute($sql);
				$sql="insert into `sg_HangYeChengBenIndex`(type,price,chenben_ton,ndate,mc_type) values(".(2+$smalltype).",'".$price_arr[$pricearr[4]]."','".$cgjdj."','".$date."','".$mc_type."')";
				//echo $sql."<br/>";
				$this->_dao->execute($sql);
				$sql="insert into `sg_HangYeChengBenIndex`(type,price,chenben_ton,ndate,mc_type) values(".(3+$smalltype).",'".$price_arr[$pricearr[5]]."','".$gjdj."','".$date."','".$mc_type."')";
				//echo $sql."<br/>";
				$this->_dao->execute($sql);
				$sql="insert into `sg_HangYeChengBenIndex`(type,chenben_ton,ndate,mc_type) values(".(4+$smalltype).",'".$df."','".$date."','".$mc_type."')";
				//echo $sql."<br/>";
				$this->_dao->execute($sql);
				$sql="insert into `sg_HangYeChengBenIndex`(type,price,chenben1,chenben2,ndate,mc_type) values(".(5+$smalltype).",'".$price_arr[$pricearr[1]]."','".$b."','".$c."','".$date."','".$mc_type."')";
				//echo $sql."<br/>";
				$this->_dao->execute($sql);
				$sql="insert into `sg_HangYeChengBenIndex`(type,price,chenben1,ndate,mc_type) values(".(6+$smalltype).",'".$price_arr[$pricearr[2]]."','".$d."','".$date."','".$mc_type."')";
				//echo $sql."<br/>";
				$this->_dao->execute($sql);
				$sql="insert into `sg_HangYeChengBenIndex`(type,price,chenben2,ndate,mc_type) values(".(7+$smalltype).",'".$price_arr[$pricearr[3]]."','".$e."','".$date."','".$mc_type."')";
				//echo $sql."<br/>";
				$this->_dao->execute($sql);
				$sql="insert into `sg_HangYeChengBenIndex`(type,chenben,chenben_tax,ndate,mc_type) values(".(8+$smalltype).",'".$PTPCB."','".Round($PTPCB*(1+$zzsl))."','".$date."','".$mc_type."')";
				//echo $sql."<br/>";
				$this->_dao->execute($sql);
				$sql="insert into `sg_HangYeChengBenIndex`(type,chenben,chenben_tax,ndate,mc_type) values(".(9+$smalltype).",'".$LWPCB."','".Round($LWPCB*(1+$zzsl))."','".$date."','".$mc_type."')";
				//echo $sql."<br/>";
				$this->_dao->execute($sql);
			}     

		}
	 }
	      ob_clean();
			$shuju['PTPCB']=$PTPCB;
			$shuju['PTPCB_HS']=Round($PTPCB*(1+$zzsl));
			$shuju['LWPCB']=$LWPCB;
			$shuju['LWPCB_HS']=Round($LWPCB*(1+$zzsl));
		
		if($params['isSave'] == "1")
			return $shuju;
		else
		{
			echo json_encode($shuju);
			exit;
		}
	  
    }
	public function jsgccbweb($params)
	{
		$get_sg_ChengBenParameter3=$params;
		$date=$params['date'];
		$mc_type=$params['mc_type']!=''?$params['mc_type']:2;;
		$type=$params['type'];	
		if($type<6||$type>7)
	   {
		$smalltype=($type-1)*30;
	   }
	   else
	   {
		$smalltype=($type-1)*30-2;
	   }
	   $sql = "select id,chenben from sg_HangYeChengBenIndex where type='".(10+$smalltype)."' and mc_type='".$mc_type."' and ndate='".$date."' order by id desc limit 1" ;
	   $query = $this->_dao->getrow($sql);
	   $PTPCB=$query ['chenben'];
	   $sql = "select chenben from sg_HangYeChengBenIndex where type='".(11+$smalltype)."' and mc_type='".$mc_type."' and ndate='".$date."' order by id desc limit 1" ;
	   $LWPCB=$this->_dao->getOne($sql);
	   //$cntaxrateinfo = $this->maindao->get_master_cntaxrate($date);//税率
	   //$zzsl = $cntaxrateinfo['rvalue'];
           $zzsl = $this->maindao->get_master_cntaxrate($date,1);//税率
	  $XCCB=$this->get_A3($PTPCB,$get_sg_ChengBenParameter3);
	  $PLCB=$this->get_B3($LWPCB,$get_sg_ChengBenParameter3);
	  $DGCB=$this->get_C3($PTPCB,$get_sg_ChengBenParameter3);
	  $LWCB=$this->get_D3($LWPCB,$get_sg_ChengBenParameter3);
	  $RZCB=$this->get_E3($PTPCB,$get_sg_ChengBenParameter3);
	  $ZHBCB=$this->get_F3($PTPCB,$get_sg_ChengBenParameter3);
	  ob_clean();
	  $shuju['XCCB']=$XCCB;
	  $shuju['XCCB_HS']=Round($XCCB*(1+$zzsl));
	  $shuju['PLCB']=$PLCB;
	  $shuju['PLCB_HS']=Round($PLCB*(1+$zzsl));
	  $shuju['DGCB']=$DGCB;
	  $shuju['DGCB_HS']=Round($DGCB*(1+$zzsl));
	  $shuju['LWCB']=$LWCB;
	  $shuju['LWCB_HS']=Round($LWCB*(1+$zzsl));
	  $shuju['RZCB']=$RZCB;
	  $shuju['RZCB_HS']=Round($RZCB*(1+$zzsl));
	  $shuju['ZHBCB']=$ZHBCB;
	  $shuju['ZHBCB_HS']=Round($ZHBCB*(1+$zzsl));
	  ob_clean();
	  
	  if($params['isSave'] == "1")
		{
			$sql = "select id from sg_HangYeChengBenIndex where type='".(12+$smalltype)."' and mc_type='".$mc_type."' and ndate='".$date."' order by id desc" ;
			$query = $this->_dao->query($sql);
			if($query!=null)
			{
				$sql="update sg_HangYeChengBenIndex set chenben='".$XCCB."',chenben_tax='".Round($XCCB*(1+$zzsl))."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(12+$smalltype);  
				$this->_dao->execute($sql);
				$sql="update sg_HangYeChengBenIndex set chenben='".$PLCB."',chenben_tax='".Round($PLCB*(1+$zzsl))."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(13+$smalltype);  
				$this->_dao->execute($sql);
				$sql="update sg_HangYeChengBenIndex set chenben='".$DGCB."',chenben_tax='".Round($DGCB*(1+$zzsl))."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(14+$smalltype);  
				$this->_dao->execute($sql);
				$sql="update sg_HangYeChengBenIndex set chenben='".$LWCB."',chenben_tax='".Round($LWCB*(1+$zzsl))."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(15+$smalltype);  
				$this->_dao->execute($sql);
				$sql="update sg_HangYeChengBenIndex set chenben='".$RZCB."',chenben_tax='".Round($RZCB*(1+$zzsl))."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(16+$smalltype);  
				$this->_dao->execute($sql);
				$sql="update sg_HangYeChengBenIndex set chenben='".$ZHBCB."',chenben_tax='".Round($ZHBCB*(1+$zzsl))."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(17+$smalltype);  
				$this->_dao->execute($sql);
			}
			else
			{

				$sql="insert into `sg_HangYeChengBenIndex`(type,chenben,chenben_tax,ndate,mc_type) values(".(12+$smalltype).",'".$XCCB."','".Round($XCCB*(1+$zzsl))."','".$date."','".$mc_type."')";
				//echo $sql."<br/>";
				$this->_dao->execute($sql);
				$sql="insert into `sg_HangYeChengBenIndex`(type,chenben,chenben_tax,ndate,mc_type) values(".(13+$smalltype).",'".$PLCB."','".Round($PLCB*(1+$zzsl))."','".$date."','".$mc_type."')";
				//echo $sql."<br/>";
				$this->_dao->execute($sql);
				$sql="insert into `sg_HangYeChengBenIndex`(type,chenben,chenben_tax,ndate,mc_type) values(".(14+$smalltype).",'".$DGCB."','".Round($DGCB*(1+$zzsl))."','".$date."','".$mc_type."')";
				//echo $sql."<br/>";
				$this->_dao->execute($sql);
				$sql="insert into `sg_HangYeChengBenIndex`(type,chenben,chenben_tax,ndate,mc_type) values(".(15+$smalltype).",'".$LWCB."','".Round($LWCB*(1+$zzsl))."','".$date."','".$mc_type."')";
				//echo $sql."<br/>";
				$this->_dao->execute($sql);
				$sql="insert into `sg_HangYeChengBenIndex`(type,chenben,chenben_tax,ndate,mc_type) values(".(16+$smalltype).",'".$RZCB."','".Round($RZCB*(1+$zzsl))."','".$date."','".$mc_type."')";
				//echo $sql."<br/>";
				$this->_dao->execute($sql);
				$sql="insert into `sg_HangYeChengBenIndex`(type,chenben,chenben_tax,ndate,mc_type) values(".(17+$smalltype).",'".$ZHBCB."','".Round($ZHBCB*(1+$zzsl))."','".$date."','".$mc_type."')";
				//echo $sql."<br/>";
				$this->_dao->execute($sql);
			}     
				return $shuju;
		}
	  else
	  {
		echo json_encode($shuju);
		exit;
	  }
	}
	public function dscx($params)//计算整个流程成本
	{
		echo "start<br/>";
		//print_r($this->maindao);
		if(date("H:i:s")<="01:50:00" && date("H:i:s")>"02:10:00")
		{
			$sql ="update sg_ModifyChengBenParameterTask set status='0' ,	cleardate='".date("Y-m-d")."' where status='1' and type='1' and (cleardate is null or cleardate !='".date("Y-m-d")."')"; 
			$this->_dao->execute($sql);
		}
		$sql="select cbp.ndate,cpt.id,cbp.type,cpt.mc_type  from sg_ChengBenParameter cbp,  sg_ModifyChengBenParameterTask cpt where cbp.id=cpt.pid and  cpt.type='1' and cpt.status=0";
		$lis=$this->_dao->query($sql);
		foreach($lis as $tmp)
		{	
           // $sql="select min(Date) as Date from ng_ChengBenParameter2  where Date>'".$tmp['Date']."' ";
			$sql="select min(ndate) as Date from sg_ChengBenParameter  where ndate>'".$tmp['ndate']."'  and isdel='0'  and type='".$tmp['type']."' and mc_type='".$tmp['mc_type']."' order by id desc limit 1";
			$minDate=$this->_dao->getOne($sql);
			
			echo $minDate;
			if(!$minDate)
			{
				$minDate=date("Y-m-d");
			}
			$sql ="update sg_ModifyChengBenParameterTask set status='1' where id='".$tmp['id']."'"; 
			$this->_dao->execute($sql);
			//echo "select date from holiday where date>='".$tmp['ndate']."' and Date<'".$minDate."'  ";
			$holidaylist =$this->maindao->query("select date,isholiday from holiday where date>='".$tmp['ndate']."' and Date<'".$minDate."'  ");
			$holidaylists=array();
			$noholidaylists=array();
			foreach($holidaylist as $holiday )
			{
				if($holiday['isholiday']==1)
				{
					$holidaylists[]=$holiday['date'];
				}
				else
				{
					$noholidaylists[]=$holiday['date'];
				}
			}


			$startdate=$tmp['ndate'];
			$enddate=$minDate;		
			while(strtotime($startdate)<strtotime($minDate))
			{
				
				
				if(!(in_array($startdate,$holidaylists) or date('D', strtotime($startdate)) == "Sat" or date('D', strtotime($startdate)) == 'Sun')||in_array($startdate,$noholidaylists))
				{
					$data['date']=$startdate;
					$data['mc_type']=$tmp['mc_type'];
					$data['type']=$tmp['type'];
					echo $startdate."<br/>";
					$this->jstscb($data);
				
				}
				
				$startdate=date('Y-m-d',strtotime('+1 day',strtotime($startdate)));
				
			}	
				$sql ="update sg_ModifyChengBenParameterTask set status='2' where id='".$tmp['id']."'"; 
				$this->_dao->execute($sql);
				 echo "更新数据".$tmp['Date']."-".$minDate."数据成功";
			 
		}
		echo "end";
		
	}
	public function dscx1($params)//计算钢坯及成材流程
    {
		$dianlutype=array('6','7','21','23','26');
		echo "start<br/>";
		$today_now = date("Y-m-d");
		//每天两点清理掉计算中的任务，重新计算，防止程序卡死现象
		if(date("H:i:s")<="02:15:00" && date("H:i:s")>"01:45:00"){
			$sql ="update sg_ModifyChengBenParameterTask set status='0' ,	cleardate='".date("Y-m-d")."' where status='1' and type='2' and (cleardate is null or cleardate !='".date("Y-m-d")."')"; 
			$this->_dao->execute($sql);
		}
		//取出转态为未计算的任务进行计算
		$can_calc_cengben = $this ->_dao ->get_can_calc();
		//echo $can_calc_cengben."gjghjg";
         //echo empty($can_calc_cengben);

		if(empty($can_calc_cengben))
		{

			$sql="select cbp.ndate,cpt.id,cbp.type,cpt.mc_type  from sg_ChengBenParameter2 cbp,  sg_ModifyChengBenParameterTask cpt where cbp.id=cpt.pid and  cpt.type='2' and cpt.status=0";
			$lis=$this->_dao->query($sql);
			//$busbankrateinfo = $this->_dao->get_drc_hbh_txl($date);//贴现率
			//$busbankrate = $busbankrateinfo['rd3'];

			//  $rmbrateinfo = $this->_dao->get_drc_rmb_hl($date);//人民币汇率中间价
			//  $rmbraterd1 = $rmbrateinfo['rd1'];

			// $cntaxrateinfo = $this->maindao->get_master_cntaxrate($date);//税率
			// //print_r($cntaxrateinfo);
			// $zzsl = $cntaxrateinfo['rvalue'];
			// echo "增值税率：".$zzsl."<br/>";
			// echo "贴现率：".$busbankrate."<br/>";
			foreach($lis as $tmp)
			{	
			   // $sql="select min(Date) as Date from ng_ChengBenParameter2  where Date>'".$tmp['Date']."' ";
				$sql="select min(ndate) as Date from sg_ChengBenParameter2  where ndate>'".$tmp['ndate']."' and isdel='0'  and type='".$tmp['type']."' and mc_type='".$tmp['mc_type']."' order by id desc limit 1";
				$minDate=$this->_dao->getOne($sql);
				echo $minDate;
				if(!$minDate)
				{
					$minDate=date("Y-m-d");
				}
				$sql ="update sg_ModifyChengBenParameterTask set status=1 where id='".$tmp['id']."'"; 
			    $this->_dao->execute($sql);
				$holidaylist =$this->maindao->query("select date,isholiday from holiday where date>='".$tmp['ndate']."' and Date<'".$minDate."'  ");
				$holidaylists=array();
			    $noholidaylists=array();
				foreach($holidaylist as $holiday )
				{
					if($holiday['isholiday']==1)
					{
						$holidaylists[]=$holiday['date'];
					}
					else
					{
						$noholidaylists[]=$holiday['date'];
					}
				}
				$startdate=$tmp['ndate'];
				$enddate=$minDate;		
				while(strtotime($startdate)<strtotime($minDate))
				{
					
					
					if(!(in_array($startdate,$holidaylists) or date('D', strtotime($startdate)) == "Sat" or date('D', strtotime($startdate)) == 'Sun')||in_array($startdate,$noholidaylists))
					{
						$data['date']=$startdate;
						$data['mc_type']=$tmp['mc_type'];
						$data['type']=$tmp['type'];
						$data['busbankrate']=$busbankrate;
						$data['zzsl']=$zzsl;
						echo $startdate."<br/>";
						if(!in_array($tmp['type'],$dianlutype))
						{
						    $this->jsgpcb($data);
						}
						else
						{
							$this->jsdlcb($data);
						}
					
					}
					
					$startdate=date('Y-m-d',strtotime('+1 day',strtotime($startdate)));
					
				}	
					$sql ="update sg_ModifyChengBenParameterTask set status=2 where id='".$tmp['id']."'"; 
					$this->_dao->execute($sql);
					 echo "更新数据".$tmp['Date']."-".$minDate."数据成功";
				 
			}
		}
		else
		{
			echo "暂时不能计算，要等铁水成本计算完才可以开始!";
		}
		echo "end";
	}

	public function dscx2($params)//计算成材流程
    {
		echo "start<br/>";
		$today_now = date("Y-m-d");
		//每天两点清理掉计算中的任务，重新计算，防止程序卡死现象
		if(date("H:i:s")<="02:15:00" && date("H:i:s")>"01:45:00"){
			$sql ="update sg_ModifyChengBenParameterTask set status=0 ,	cleardate='".date("Y-m-d")."' where status='1' and type='3' and (cleardate is null or cleardate !='".date("Y-m-d")."')"; 
			$this->_dao->execute($sql);
		}
		//取出转态为未计算的任务进行计算
		$can_calc_cengben = $this ->_dao ->get_can_calc();
		$can_calc_cengben1 = $this ->_dao ->get_can_calc1();

		if(empty($can_calc_cengben)&&empty($can_calc_cengben1))
		{
			$sql="select cbp.ndate,cpt.id,cbp.type,cpt.mc_type  from sg_ChengBenParameter3 cbp,  sg_ModifyChengBenParameterTask cpt where cbp.id=cpt.pid and  cpt.type='3' and cpt.status=0";
			$lis=$this->_dao->query($sql);
			// $busbankrateinfo = $this->_dao->get_drc_hbh_txl($date);//贴现率
			// $busbankrate = $busbankrateinfo['rd3'];

			// //  $rmbrateinfo = $this->_dao->get_drc_rmb_hl($date);//人民币汇率中间价
			// //  $rmbraterd1 = $rmbrateinfo['rd1'];

			// $cntaxrateinfo = $this->maindao->get_master_cntaxrate($date);//税率
			// //print_r($cntaxrateinfo);
			// $zzsl = $cntaxrateinfo['rvalue'];
			// echo "增值税率：".$zzsl."<br/>";
			// echo "贴现率：".$busbankrate."<br/>";
			foreach($lis as $tmp)
			{	
			   // $sql="select min(Date) as Date from ng_ChengBenParameter2  where Date>'".$tmp['Date']."' ";
				$sql="select min(ndate) as Date from sg_ChengBenParameter3  where ndate>'".$tmp['ndate']."' and isdel='0'  and type='".$tmp['type']."' and mc_type='".$tmp['mc_type']."' order by id desc limit 1";
				$minDate=$this->_dao->getOne($sql);
				
				if(!$minDate)
				{
					$minDate=date("Y-m-d");
				}
			   $sql ="update sg_ModifyChengBenParameterTask set status='1' where id='".$tmp['id']."'"; 
			   $this->_dao->execute($sql);
				$holidaylist =$this->maindao->query("select date,isholiday from holiday where date>='".$tmp['ndate']."' and Date<'".$minDate."'  ");
				$holidaylists=array();
			    $noholidaylists=array();
				foreach($holidaylist as $holiday )
				{
					if($holiday['isholiday']==1)
					{
						$holidaylists[]=$holiday['date'];
					}
					else
					{
						$noholidaylists[]=$holiday['date'];
					}
				}
				$startdate=$tmp['ndate'];
				$enddate=$minDate;		
				while(strtotime($startdate)<strtotime($minDate))
				{
					
					
					if(!(in_array($startdate,$holidaylists) or date('D', strtotime($startdate)) == "Sat" or date('D', strtotime($startdate)) == 'Sun')||in_array($startdate,$noholidaylists))
					{
						$data['date']=$startdate;
						$data['mc_type']=$tmp['mc_type'];
						$data['type']=$tmp['type'];
						$data['busbankrate']=$busbankrate;
						$data['zzsl']=$zzsl;
						echo $startdate."<br/>";
						$this->jsgccb($data);
					
					}
					
					$startdate=date('Y-m-d',strtotime('+1 day',strtotime($startdate)));
					
				}	
					$sql ="update sg_ModifyChengBenParameterTask set status='2' where id='".$tmp['id']."'"; 
					$this->_dao->execute($sql);
					 echo "更新数据".$tmp['Date']."-".$minDate."数据成功";
				 
			}
		}
		else
		{
			echo "暂时不能计算，要等铁水成本与钢坯成本计算完才可以开始!";
		}
	echo "end";

	}


	public function dscx3($params)//计算龙钢汉钢动态成本
	{
		echo "start<br/>";
		//print_r($this->maindao);
		if(date("H:i:s")<="01:50:00" && date("H:i:s")>"02:10:00")
		{
			$sql ="update sg_ModifyChengBenParameterTask set status=0 ,	cleardate='".date("Y-m-d")."' where status='1' and type='4' and (cleardate is null or cleardate !='".date("Y-m-d")."')"; 
			$this->_dao->execute($sql);
		}
		$sql="select cbp.ndate,cpt.id,cbp.type  from sg_ChengBenParameter4 cbp,  sg_ModifyChengBenParameterTask cpt where cbp.id=cpt.pid and  cpt.type='4' and cpt.status='0'";
		$lis=$this->_dao->query($sql);
		foreach($lis as $tmp)
		{	
           // $sql="select min(Date) as Date from ng_ChengBenParameter2  where Date>'".$tmp['Date']."' ";
			$sql="select min(ndate) as Date from sg_ChengBenParameter4  where ndate>'".$tmp['ndate']."' and isdel='0'  and type='".$tmp['type']."' order by id desc limit 1";
			$minDate=$this->_dao->getOne($sql);
			
			echo $minDate;
			if(!$minDate)
			{
				$minDate=date("Y-m-d");
			}
			$sql ="update sg_ModifyChengBenParameterTask set status='1' where id='".$tmp['id']."'"; 
			$this->_dao->execute($sql);
			//echo "select date from holiday where date>='".$tmp['ndate']."' and Date<'".$minDate."'  ";
			$holidaylist =$this->maindao->query("select date,isholiday from holiday where date>='".$tmp['ndate']."' and Date<'".$minDate."'  ");
			$holidaylists=array();
			$noholidaylists=array();
			foreach($holidaylist as $holiday )
			{
				if($holiday['isholiday']==1)
				{
					$holidaylists[]=$holiday['date'];
				}
				else
				{
					$noholidaylists[]=$holiday['date'];
				}
			}


			$startdate=$tmp['ndate'];
			$enddate=$minDate;		
			while(strtotime($startdate)<strtotime($minDate))
			{
				
				
				if(!(in_array($startdate,$holidaylists) or date('D', strtotime($startdate)) == "Sat" or date('D', strtotime($startdate)) == 'Sun')||in_array($startdate,$noholidaylists))
				{
					$data['date']=$startdate;
					$type=$tmp['type'];
				     if($type==1)
					{
					  $this->jsts_dtcb_lg($data);
					}
					else if($type==2)
					{
					  $this->jsts_dtcb_hg($data);
					}
				
				}
				
				$startdate=date('Y-m-d',strtotime('+1 day',strtotime($startdate)));
				
			}	
				$sql ="update sg_ModifyChengBenParameterTask set status='2' where id='".$tmp['id']."'"; 
				$this->_dao->execute($sql);
				 echo "更新数据".$tmp['Date']."-".$minDate."数据成功";
			 
		}
		echo "end";
		
	}

 	
	public function test2($params){
		
		//echo "xiangbin";exit;
		
		$today=date("Y-m-d");
		if($params['curdate'])
		{
			if(checkdate(date("m",strtotime($params['curdate'])),date("d",strtotime($params['curdate'])),date("Y",strtotime($params['curdate']))))
			{
				$today=$params['curdate'];
			}

		}
		//echo "xiangbin";exit;
		echo $today;
		$date=$this->get_xhdate($today);
		//echo $date;
		echo $date;
		$type=$params['type'];
		$data['date']=$date;
		//$mc_type=$params['mc_type']?$params['mc_type']:2;
		//$data['mc_type']=$mc_type;
		if(empty($type))
		{
			//陕钢部分
			$data['mc_type']=2;
			$data['type']=1;
			$this->jstscb($data);
			$data['type']=2;
			$this->jstscb($data);
			$data['type']=3;
			$this->jstscb($data);
			$data['type']=4;
			$this->jstscb($data);
			$data['type']=5;
			$this->jstscb($data);
			
			//钢之家部分
			// $data['mc_type']=0;
			// $data['type']=1;
			// $this->jstscb($data);
			// $data['type']=2;
			// $this->jstscb($data);
			// $data['type']=3;
			// $this->jstscb($data);
			// $data['type']=4;
			// $this->jstscb($data);
			// $data['type']=5;
			// $this->jstscb($data);
			
			/*$data['type']=8;
			$this->jstscb($data);
			$data['type']=9;
			$this->jstscb($data);
			$data['type']=10;
			$this->jstscb($data);
			$data['type']=11;
			$this->jstscb($data);
			$data['type']=12;
			$this->jstscb($data);
			$data['type']=13;
			$this->jstscb($data);
			$data['type']=14;
			$this->jstscb($data);
			$data['type']=15;
			$this->jstscb($data);
			$data['type']=16;
			$this->jstscb($data);
			$data['type']=17;
			$this->jstscb($data);
			$data['type']=18;
			$this->jstscb($data);
			$data['type']=19;
			$this->jstscb($data);*/
			
			
			//$this->jstscb($data);
		}
		else if($type<6||$type>7)
		{
			$data['mc_type']=2;
			$data['type']=$type;
			$this->jstscb($data);
			
			// $data['mc_type']=0;
			// $data['type']=$type;
			// $this->jstscb($data);
		}
		else 
		{
			echo "错误参数type";
		}
		
	
		
		
		echo "执行成功";
		
		
	}
	public function test3($params){
		
		//echo "xiangbin";exit;
		// 不能删除该方法，type=20的时候需要清除前方的打印信息，才能解析json响应
		ob_start();

		$today=date("Y-m-d");
		if($params['curdate'])
		{
			if(checkdate(date("m",strtotime($params['curdate'])),date("d",strtotime($params['curdate'])),date("Y",strtotime($params['curdate']))))
			{
				$today=$params['curdate'];
			}

		}
		//echo "xiangbin";exit;
		// echo $today;
		$date=$this->get_xhdate_new($today);
		//echo $date;
		// echo $date;
		$type=$params['type'];
		$data['date']=$date;
		//$mc_type=$params['mc_type']?$params['mc_type']:2;
		//$data['mc_type']=$mc_type;
		if(empty($type))
		{
			//钢之家部分
			$data['mc_type']=0;
			$data['type']=1;
			$this->jstscb($data);
			$data['type']=2;
			$this->jstscb($data);
			$data['type']=3;
			$this->jstscb($data);
			$data['type']=4;
			$this->jstscb($data);
			$data['type']=5;
			$this->jstscb($data);
			//钢之家部分
			$data['mc_type']=0;
			$data['type']=8;
			$this->jstscb($data);
			$data['type']=9;
			$this->jstscb($data);
			$data['type']=10;
			$this->jstscb($data);
			$data['type']=11;
			$this->jstscb($data);
			$data['type']=12;
			$this->jstscb($data);
			$data['type']=13;
			$this->jstscb($data);
			$data['type']=14;
			$this->jstscb($data);
			$data['type']=15;
			$this->jstscb($data);
			$data['type']=16;
			$this->jstscb($data);
			$data['type']=17;
			$this->jstscb($data);
			$data['type']=18;
			$this->jstscb($data);
			$data['type']=19;
			$this->jstscb($data);
			$data['type']=20;
			$this->jstscb($data);
			$data['type']=25;
			$this->jsgpcb($data);
			$data['type']=26;
			$this->jsdlcb($data);
			
		}
		else if($type<6||$type>7)
		{
			$data['mc_type']=0;
			$data['type']=$type;
			$info = $this->jstscb($data);
			if($info){
				ob_end_clean();
				echo json_encode($info);
				exit;
			}
		}
		else 
		{
			echo "错误参数type";
		}
		
	
		
		
		echo "执行成功";
		
		
	}

	public function jstsmethod($get_sg_ChengBenParameter,$date)
	{
		$busbankrateinfo = $this->_dao->get_drc_hbh_txl($date);//贴现率
		$busbankrate = $busbankrateinfo['rd3'];
		//$busbankrate=3.35;
		//$cntaxrateinfo = $this->maindao->get_master_cntaxrate($date);//税率
		//print_r($cntaxrateinfo);
		//$zzsl = $cntaxrateinfo['rvalue'];
                $zzsl = $this->maindao->get_master_cntaxrate($date,1);//税率
		//echo "增值税率：".$zzsl."<br/>";
		//echo "贴现率：".$busbankrate."<br/>";
		//$zzsl=0.13;
		//$busbankrate =3.35;
		 $pricearr=array();
		if($get_sg_ChengBenParameter['GkxhkPriceidA'])
		{
		   $pricearr[0]=$get_sg_ChengBenParameter['GkxhkPriceidA'];
		}
		if($get_sg_ChengBenParameter['GckPriceidB'])
		{
		   $pricearr[1]=$get_sg_ChengBenParameter['GckPriceidB'];
		}
		if($get_sg_ChengBenParameter['Z1YjjPriceidC'])
		{
		   $pricearr[2]=$get_sg_ChengBenParameter['Z1YjjPriceidC'];
		}
		if($get_sg_ChengBenParameter['PcWymPriceidD'])
		{
		   $pricearr[3]=$get_sg_ChengBenParameter['PcWymPriceidD'];
		}
		$priceidstr=  implode( ",", $pricearr );

		//echo $priceidstr;exit;
		$price_arr=$this->maindao->getpricebypricestr($priceidstr,$date);
	   //print_r($price_arr);
	   $a=$this->get_A1($price_arr[$pricearr[0]],$get_sg_ChengBenParameter,$zzsl,$busbankrate);//京唐港现货矿PB
	   $b=$this->get_B1($price_arr[$pricearr[1]],$get_sg_ChengBenParameter,$zzsl,$busbankrate);//唐山66%铁精粉
	   $c=$this->get_C1($price_arr[$pricearr[2]],$get_sg_ChengBenParameter,$zzsl,$busbankrate);//唐山66%铁精粉
	   $d=$this->get_D1($price_arr[$pricearr[3]],$get_sg_ChengBenParameter,$zzsl,$busbankrate);//唐山66%铁精粉
	   $tscb=$this->get_E1(Round($a),Round($b),Round($c),Round($d),$get_sg_ChengBenParameter);
	   $info['a']=$a;
	   $info['b']=$b;
	   $info['c']=$c;
	   $info['d']=$d;
	   $info['tscb']=$tscb;
	   $info['chenben']=$tscb;
	   $info['chenben_tax']=Round($tscb*(1+$zzsl));
	   $info['price_arr'][]=$price_arr[$pricearr[0]];
	   $info['price_arr'][]=$price_arr[$pricearr[1]];
	   $info['price_arr'][]=$price_arr[$pricearr[2]];
	   $info['price_arr'][]=$price_arr[$pricearr[3]];
	   return $info;
	}

	public function jstsmethod20($params,$date)
	{
        $busbankrateinfo = $this->_dao->get_drc_hbh_txl($date);//贴现率
        $busbankrate = $busbankrateinfo['rd3'];
		file_put_contents("log.txt", date("Y-m-d H:i:s").":::GangKouXianHuoKuang：".$params['GangKouXianHuoKuang']."GuoChanKuang:".$params['GuoChanKuang']."YeJinJiao:".$params['YeJinJiao']."WuYanMei".$params['WuYanMei']."\n", FILE_APPEND);
        $zzsl = $this->maindao->get_master_cntaxrate($date,1);//税率
		$a=$this->get_A1($params['GangKouXianHuoKuang'], $params,$zzsl,$busbankrate);
		$b=$this->get_B1($params['GuoChanKuang'],$params,$zzsl,$busbankrate);
		$c=$this->get_C1($params['YeJinJiao'],$params,$zzsl,$busbankrate);
		$d=$this->get_D1($params['WuYanMei'],$params,$zzsl,$busbankrate);
		$tscb=$this->get_E1(Round($a),Round($b),Round($c),Round($d),$params);
		$base = $this->maindao->getrow("select price,mindex from shpi_material where topicture='50' and isbase=1");
		$baseprice = $base['price'];
		$baseindex = $base['mindex'];
        $tszs_new = round($tscb/$baseprice*$baseindex, 2);
		$tmp = $this->maindao->getrow("select price,mindex from shpi_material where topicture='50' and dateday='{$date}'");
		$tszs_old = $chenben_old = "";
		if(!empty($tmp)){
			$tszs_old = $tmp['mindex'];
			$chenben_old = $tmp['price'];
		}
		$info['chenben_old']=$chenben_old;
		$info['a']=$a;
        $info['b']=$b;
        $info['c']=$c;
        $info['d']=$d;
        $info['tscb']=$tscb;
		$info['tszs_new']=$tszs_new;
        $info['tszs_old']=$tszs_old;
        $info['chenben']=$tscb;
        $info['chenben_tax']=Round($tscb*(1+$zzsl));
        $info['price_arr'][]=$params['GangKouXianHuoKuang'];
        $info['price_arr'][]=$params['GuoChanKuang'];
        $info['price_arr'][]=$params['YeJinJiao'];
        $info['price_arr'][]=$params['WuYanMei'];
        return $info;
	}
	
	public function jstscb($params)
	{
		$date=$params['date'];
		$mc_type=$params['mc_type'];
     //$date='2020-01-02';
		//$type=5;
		 $type=$params['type'];
		 $get_sg_ChengBenParameter = $this->_dao->get_sg_ChengBenParameter($date,$type,$mc_type);
		 if($type=="20") {
            $get_sg_ChengBenParameter['GangKouXianHuoKuang'] = round($this->maindao->getone("select weiprice from shpi_material_pzp where vid=3 and dateday='{$date}'"));
            $get_sg_ChengBenParameter['GuoChanKuang'] = round($this->maindao->getone("select weiprice from shpi_material_pzp where vid=1 and dateday='{$date}'"));
            $get_sg_ChengBenParameter['YeJinJiao'] = round($this->maindao->getone("select price from shpi_material where topicture=3 and dateday='{$date}'"));
            $get_sg_ChengBenParameter['WuYanMei'] = round($this->maindao->getone("select weiprice from shpi_mj_pzp where vid=0 and type=3 and dateday='{$date}'"));
        }
		//  $busbankrateinfo = $this->_dao->get_drc_hbh_txl($date);//贴现率
		//  $busbankrate = $busbankrateinfo['rd3'];

		// //  $rmbrateinfo = $this->_dao->get_drc_rmb_hl($date);//人民币汇率中间价
		// //  $rmbraterd1 = $rmbrateinfo['rd1'];

		//  $cntaxrateinfo = $this->maindao->get_master_cntaxrate($date);//税率
		//  //print_r($cntaxrateinfo);
		//  $zzsl = $cntaxrateinfo['rvalue'];
		//  echo "增值税率：".$zzsl."<br/>";
		//  echo "贴现率：".$busbankrate."<br/>";
		//  //$zzsl=0.13;
		//  //$busbankrate =3.35;
		//   $pricearr=array();
		//  if($get_sg_ChengBenParameter['GkxhkPriceidA'])
		//  {
		// 	$pricearr[0]=$get_sg_ChengBenParameter['GkxhkPriceidA'];
		//  }
		//  if($get_sg_ChengBenParameter['GckPriceidB'])
		//  {
		// 	$pricearr[1]=$get_sg_ChengBenParameter['GckPriceidB'];
		//  }
		//  if($get_sg_ChengBenParameter['Z1YjjPriceidC'])
		//  {
		// 	$pricearr[2]=$get_sg_ChengBenParameter['Z1YjjPriceidC'];
		//  }
		//  if($get_sg_ChengBenParameter['PcWymPriceidD'])
		//  {
		// 	$pricearr[3]=$get_sg_ChengBenParameter['PcWymPriceidD'];
		//  }
		//  $priceidstr=  implode( ",", $pricearr );

		//  //echo $priceidstr;exit;
		//  $price_arr=$this->maindao->getpricebypricestr($priceidstr,$date);
		
		// $a=$this->get_A1($price_arr[$pricearr[0]],$get_sg_ChengBenParameter,$zzsl,$busbankrate);//京唐港现货矿PB
		// $b=$this->get_B1($price_arr[$pricearr[1]],$get_sg_ChengBenParameter,$zzsl,$busbankrate);//唐山66%铁精粉
		// $c=$this->get_C1($price_arr[$pricearr[2]],$get_sg_ChengBenParameter,$zzsl,$busbankrate);//唐山66%铁精粉
		// $d=$this->get_D1($price_arr[$pricearr[3]],$get_sg_ChengBenParameter,$zzsl,$busbankrate);//唐山66%铁精粉

		// $tscb=$this->get_E1(Round($a),Round($b),Round($c),Round($d),$get_sg_ChengBenParameter);//唐山66%铁精粉
		if($type=="20"){
			$shuju=$this->jstsmethod20($get_sg_ChengBenParameter,$date);
		} else {
			$shuju=$this->jstsmethod($get_sg_ChengBenParameter,$date);
		}
		$a=$shuju['a'];
		$b=$shuju['b'];
		$c=$shuju['c'];
		$d=$shuju['d'];
		//$tscb=$shuju['tscb'];
		$chenben=$shuju['chenben'];
		$chenben_tax=$shuju['chenben_tax'];
        $price_arr=$shuju['price_arr'];

		$smalltype=($type-1)*30;

		$sql = "select id from sg_HangYeChengBenIndex where type='".(1+$smalltype)."' and ndate='".$date."' and mc_type='".$mc_type."' order by id desc" ;
		
		$query = $this->_dao->query($sql);
		
		
		if($query!=null)
		{
			$sql="update sg_HangYeChengBenIndex set price='".$price_arr[0]."',chenben_ton='".$a."' where ndate='".$date."'  and mc_type='".$mc_type."' and type=".(1+$smalltype);
			$this->_dao->execute($sql);
			$sql="update sg_HangYeChengBenIndex set price='".$price_arr[1]."',chenben_ton='".$b."' where ndate='".$date."'  and mc_type='".$mc_type."' and type=".(2+$smalltype); 
			$this->_dao->execute($sql);
			$sql="update sg_HangYeChengBenIndex set price='".$price_arr[2]."',chenben_ton='".$c."' where ndate='".$date."'  and mc_type='".$mc_type."' and type=".(3+$smalltype); 
			$this->_dao->execute($sql);
			$sql="update sg_HangYeChengBenIndex set price='".$price_arr[3]."',chenben_ton='".$d."' where ndate='".$date."'  and mc_type='".$mc_type."' and type=".(4+$smalltype); 
			$this->_dao->execute($sql);
			$sql="update sg_HangYeChengBenIndex set chenben='".$chenben."',chenben_tax='".$chenben_tax."' where ndate='".$date."'  and mc_type='".$mc_type."' and type=".(5+$smalltype);  
			$this->_dao->execute($sql);
			echo "更新铁水成本<br/>";
		}
		else
		{
			$sql="insert into `sg_HangYeChengBenIndex`(type,price,chenben_ton,ndate,mc_type) values(".(1+$smalltype).",'".$price_arr[0]."','".$a."','".$date."','".$mc_type."')";
			echo $sql."<br/>";
			$this->_dao->execute($sql);
			$sql="insert into `sg_HangYeChengBenIndex`(type,price,chenben_ton,ndate,mc_type) values(".(2+$smalltype).",'".$price_arr[1]."','".$b."','".$date."','".$mc_type."')";
			echo $sql."<br/>";
			$this->_dao->execute($sql);
			$sql="insert into `sg_HangYeChengBenIndex`(type,price,chenben_ton,ndate,mc_type) values(".(3+$smalltype).",'".$price_arr[2]."','".$c."','".$date."','".$mc_type."')";
			echo $sql."<br/>";
			$this->_dao->execute($sql);
			$sql="insert into `sg_HangYeChengBenIndex`(type,price,chenben_ton,ndate,mc_type) values(".(4+$smalltype).",'".$price_arr[3]."','".$d."','".$date."','".$mc_type."')";
			echo $sql."<br/>";
			$this->_dao->execute($sql);
			$sql="insert into `sg_HangYeChengBenIndex`(type,chenben,chenben_tax,ndate,mc_type) values(".(5+$smalltype).",'".$chenben."','".$chenben_tax."','".$date."','".$mc_type."')";
			echo $sql."<br/>";
			$this->_dao->execute($sql);
		}     
		//echo "吨钢成本：".Round($a*0.85*1.06);//铁水成本不含税*生铁比例*钢铁料消耗
		$info['date']=$date;
		$info['mc_type']=$mc_type;
		$info['type']=$type;
		$info['tscb']=$chenben;
		$info['zzsl']=$zzsl;
		$info['busbankrate']=$busbankrate;
		if($type == "20") {
			return $info;exit;
		}
		$this->jsgpcb($info);
		if($mc_type=="2")
		{
			return;
		}
		if($type==2)
		{
			$info['type']=6;
			$this->jsdlcb($info);
			$info['type']=21;
			$this->jsdlcb($info);
			$info['type']=23;
			$this->jsdlcb($info);
			$info['type']=22;
			$this->jsgpcb($info);
			$info['type']=24;
			$this->jsgpcb($info);
		}
		else if($type==5)
		{
			$info['type']=7;
			$this->jsdlcb($info);
		}
		
	} 
	
   public function jsgpcb($params)
   {
		$zhuisutype=array('21','22','23','24');
		$qggptype=array('25','26');
	   $tscb=$params['tscb'];
	   $date=$params['date'];
	   $mc_type=$params['mc_type'];
	   $type=$params['type'];
	   $zzsl=$params['zzsl'];
	   $busbankrate=$params['busbankrate'];
	  // $type=5;
	   if(empty($tscb))  
	   {
          //查询数据库铁水成本
	   }
	   $smalltype=($type-1)*30;
	    $get_sg_ChengBenParameter2 = $this->_dao->get_sg_ChengBenParameter2($date,$type,$mc_type);
       if(in_array($type, $zhuisutype))
		{
			$zhuisudate=date('Y-m-d',strtotime('-'.$get_sg_ChengBenParameter2['zhuisuday'].' day',strtotime($date)));
			$flag = 1;
			while(true)
			{
				if(file_get_contents(APP_URL_WWW.'/isholiday.php?date='.$zhuisudate)!="1")
				{ 
				break;
				} 
				$zhuisudate=date('Y-m-d',strtotime('-'.$flag.' day',strtotime($zhuisudate)));
			}
			$sql = "select id,chenben from sg_HangYeChengBenIndex where type='35' and mc_type='".$mc_type."' and ndate='".$zhuisudate."' order by id desc limit 1" ;
		    $query = $this->_dao->getrow($sql);
			$tscb=$query ['chenben'];
		}
		else
		{
			if(in_array($type,$qggptype))
			{
				$jiage_arr1 = $this->maindao->get_shpi_material("4,50",$params['date']);
				if(isset($jiage_arr1['50']))
				{
					$tscb =$jiage_arr1['50'];
				}
				else
				{
					echo "全国铁水成本未计算，请耐心等待";
					return; 
				}
			}
			else {
				$sql = "select id,chenben from sg_HangYeChengBenIndex where type='".(5+$smalltype)."' and ndate='".$date."' and mc_type='".$mc_type."' order by id desc limit 1" ;
				$query = $this->_dao->getrow($sql);
				if($query!=null)
				{
				if(empty($tscb))
				{
					$tscb=$query ['chenben'];
				}
				}
				else
				{
					echo "铁水成本未计算，你需先计算铁水成本";
					return; 
				}
			}
			
			
		}
	  //print_r($get_sg_ChengBenParameter2);
	   if(empty($busbankrate))
	   {
		$busbankrateinfo = $this->_dao->get_drc_hbh_txl($date);//贴现率
		$busbankrate = $busbankrateinfo['rd3'];
	   }
	   if(empty($zzsl))
	   {
		//$cntaxrateinfo = $this->maindao->get_master_cntaxrate($date);//税率
		//$zzsl = $cntaxrateinfo['rvalue'];
		$zzsl = $this->maindao->get_master_cntaxrate($date,1);//税率
	   } 
	   //$zzsl=0.13;
	  // $busbankrate =3.35;
		$pricearr=array();
	   if($get_sg_ChengBenParameter2['FgPriceidA'])
	   {
		  $pricearr[0]=$get_sg_ChengBenParameter2['FgPriceidA'];
	   }
	   if($get_sg_ChengBenParameter2['GmPriceidB'])
	   {
		  $pricearr[1]=$get_sg_ChengBenParameter2['GmPriceidB'];
	   }
	   if($get_sg_ChengBenParameter2['GtPriceidC'])
	   {
		  $pricearr[2]=$get_sg_ChengBenParameter2['GtPriceidC'];
	   }
	   if($get_sg_ChengBenParameter2['FdPriceidD'])
	   {
		  $pricearr[3]=$get_sg_ChengBenParameter2['FdPriceidD'];
	   }
	   $priceidstr=  implode( ",", $pricearr );

	   $price_arr=$this->maindao->getpricebypricestr($priceidstr,$date);
	    if(in_array($type,$zhuisutype))
        {
            $price_arr1 = $this->maindao->getpricebypricestr($pricearr[0],$zhuisudate);
            $price_arr[$pricearr[0]] = $price_arr1[$pricearr[0]];
		}
		if(in_array($type,$qggptype))
		{
			$price_arr[$pricearr[0]] = $jiage_arr1['4'];
		}
	//  print_r($price_arr);
		$X=$this->get_F1($tscb,$get_sg_ChengBenParameter2);
		//echo  "吨钢成本：".$X."<br/>";

	   if(!(in_array($type,$zhuisutype)||in_array($type,$qggptype)))
	   {
			
			//更新铁水成本吨钢成本
			$sql=" update sg_HangYeChengBenIndex set chenben_ton='".$X."' where id='".$query['id']."'";
			$this->_dao->execute($sql);
	   }
	 
	   
	 
	   $a=$this->get_A2($price_arr[$pricearr[0]],$get_sg_ChengBenParameter2,$zzsl,$busbankrate);//京唐港现货矿PB
	   $b=$this->get_B2($price_arr[$pricearr[1]],$get_sg_ChengBenParameter2,$zzsl,$busbankrate);//唐山66%铁精粉
	   $c=$this->get_C2($price_arr[$pricearr[1]],$get_sg_ChengBenParameter2,$zzsl,$busbankrate);//唐山66%铁精粉
	   $d=$this->get_D2($price_arr[$pricearr[2]],$get_sg_ChengBenParameter2,$zzsl,$busbankrate);//唐山66%铁精粉
	   $e=$this->get_E2($price_arr[$pricearr[3]],$get_sg_ChengBenParameter2,$zzsl,$busbankrate);//唐山66%铁精粉
	   
	   echo "废钢吨钢成本：".$a.",硅锰普碳坯成本:".$b.",硅锰螺纹钢成本:".$c.",硅铁普碳坯成本:".$d.",钒氮合金螺纹钢成本：".$e."<br/>";
	   
	   //echo ($X+$a+$b+$d+150+50-5)."<br/>";//铁水吨钢成本+废钢吨钢成本+硅锰普碳坯成本+硅铁普碳坯成本+动力人工制造+辅料-钢渣回收;
	   //echo $X+$a+$c+$e+150+50-5;
	   //echo "shen";
	   $PTPCB=Round($X+$a+$b+$d+$get_sg_ChengBenParameter2['DlrgMake']+$get_sg_ChengBenParameter2['FuLiao']-$get_sg_ChengBenParameter2['GangZhaHuiShou']);

	   $LWPCB=Round($X+$a+$c+$e+$get_sg_ChengBenParameter2['DlrgMake']+$get_sg_ChengBenParameter2['FuLiao']-$get_sg_ChengBenParameter2['GangZhaHuiShou']);
	   echo "普碳坯成本：".$PTPCB."螺纹坯成本：".$LWPCB;

	

	   $sql = "select id from sg_HangYeChengBenIndex where type='".(6+$smalltype)."' and ndate='".$date."' and mc_type='".$mc_type."' order by id desc" ;
	   $query = $this->_dao->query($sql);
	   if($query!=null)
	   {
		   $sql="update sg_HangYeChengBenIndex set price='".$price_arr[$pricearr[0]]."',chenben_ton='".$a."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(6+$smalltype);
		   $this->_dao->execute($sql);
		   $sql="update sg_HangYeChengBenIndex set price='".$price_arr[$pricearr[1]]."',chenben1='".$b."',chenben2='".$c."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(7+$smalltype); 
		   $this->_dao->execute($sql);
		   $sql="update sg_HangYeChengBenIndex set price='".$price_arr[$pricearr[2]]."',chenben1='".$d."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(8+$smalltype); 
		   $this->_dao->execute($sql);
		   $sql="update sg_HangYeChengBenIndex set price='".$price_arr[$pricearr[3]]."',chenben2='".$e."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(9+$smalltype); 
		   $this->_dao->execute($sql);
		   $sql="update sg_HangYeChengBenIndex set chenben='".$PTPCB."',chenben_tax='".Round($PTPCB*(1+$zzsl))."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(10+$smalltype);  
		   $this->_dao->execute($sql);
		   $sql="update sg_HangYeChengBenIndex set chenben='".$LWPCB."',chenben_tax='".Round($LWPCB*(1+$zzsl))."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(11+$smalltype);  
		   $this->_dao->execute($sql);
	   }
	   else
	   {
		   $sql="insert into `sg_HangYeChengBenIndex`(type,price,chenben_ton,ndate,mc_type) values(".(6+$smalltype).",'".$price_arr[$pricearr[0]]."','".$a."','".$date."','".$mc_type."')";
		   echo $sql."<br/>";
		   $this->_dao->execute($sql);
		   $sql="insert into `sg_HangYeChengBenIndex`(type,price,chenben1,chenben2,ndate,mc_type) values(".(7+$smalltype).",'".$price_arr[$pricearr[1]]."','".$b."','".$c."','".$date."','".$mc_type."')";
		   echo $sql."<br/>";
		  $this->_dao->execute($sql);
		   $sql="insert into `sg_HangYeChengBenIndex`(type,price,chenben1,ndate,mc_type) values(".(8+$smalltype).",'".$price_arr[$pricearr[2]]."','".$d."','".$date."','".$mc_type."')";
		   echo $sql."<br/>";
		   $this->_dao->execute($sql);
		   $sql="insert into `sg_HangYeChengBenIndex`(type,price,chenben2,ndate,mc_type) values(".(9+$smalltype).",'".$price_arr[$pricearr[3]]."','".$e."','".$date."','".$mc_type."')";
		   echo $sql."<br/>";
		   $this->_dao->execute($sql);
		   $sql="insert into `sg_HangYeChengBenIndex`(type,chenben,chenben_tax,ndate,mc_type) values(".(10+$smalltype).",'".$PTPCB."','".Round($PTPCB*(1+$zzsl))."','".$date."','".$mc_type."')";
		   echo $sql."<br/>";
		   $this->_dao->execute($sql);
		   $sql="insert into `sg_HangYeChengBenIndex`(type,chenben,chenben_tax,ndate,mc_type) values(".(11+$smalltype).",'".$LWPCB."','".Round($LWPCB*(1+$zzsl))."','".$date."','".$mc_type."')";
		   echo $sql."<br/>";
		   $this->_dao->execute($sql);
	   }     


	// if(in_array($type,$qggptype))
	// {
	// 	return false;
	// }


	   $info['date']=$date;
	   $info['mc_type']=$mc_type;
	   $info['type']=$type;
	   $info['PTPCB']=$PTPCB;
	   $info['LWPCB']=$LWPCB;
	   $info['zzsl']=$zzsl;

       $this->jsgccb($info);

   }
   public function jsgccb($params)
   {
      $dianlutype=array('6','7','21','23','26');
	   $PTPCB=$params['PTPCB'];
	   $LWPCB=$params['LWPCB'];
	   $PTPCBG=$params['PTPCBG'];
	   $LWPCBG=$params['LWPCBG'];
	   $date=$params['date'];
	   $mc_type=$params['mc_type'];
	   $type=$params['type'];
	   $zzsl=$params['zzsl'];
	   //$busbankrate=$params['busbankrate'];
	   //$type=5;

	   if(!in_array($type,$dianlutype))
	   {
		$smalltype=($type-1)*30;
	   }
	   else
	   {
		$smalltype=($type-1)*30-2;
	   }
	   $sql = "select id,chenben1,chenben from sg_HangYeChengBenIndex where type='".(10+$smalltype)."' and ndate='".$date."' and mc_type='".$mc_type."' order by id desc limit 1" ;
	   $query = $this->_dao->getrow($sql);
		if($query!=null)
		{
		   if(empty($PTPCB)||in_array($type,$dianlutype))
		   {
			 $PTPCB=$query ['chenben'];
			 $PTPCBG=$query ['chenben1'];
			 $sql = "select chenben1,chenben from sg_HangYeChengBenIndex where type='".(11+$smalltype)."' and ndate='".$date."' and mc_type='".$mc_type."' order by id desc limit 1" ;
			 $query=$this->_dao->getrow($sql);
			 $LWPCB=$query['chenben'];
			 $LWPCBG=$query['chenben1'];
		   }
		}
		else
		{
			echo "铁水成本未计算，你需先计算钢坯成本";
			return; 
		}
	   $get_sg_ChengBenParameter3 = $this->_dao->get_sg_ChengBenParameter3($date,$type,$mc_type);

	   if(empty($zzsl))
	   {
		//$cntaxrateinfo = $this->maindao->get_master_cntaxrate($date);//税率
		//$zzsl = $cntaxrateinfo['rvalue'];
		$zzsl = $this->maindao->get_master_cntaxrate($date,1);//税率
	   }
	   
	   //$zzsl=0.13;
	   //$busbankrate =3.35;
		$pricearr=array();


	  $XCCB=$this->get_A3($PTPCB,$get_sg_ChengBenParameter3);
	  $PLCB=$this->get_B3($LWPCB,$get_sg_ChengBenParameter3);
	  $DGCB=$this->get_C3($PTPCB,$get_sg_ChengBenParameter3);
	  $LWCB=$this->get_D3($LWPCB,$get_sg_ChengBenParameter3);
	  $RZCB=$this->get_E3($PTPCB,$get_sg_ChengBenParameter3);
	  $ZHBCB=$this->get_F3($PTPCB,$get_sg_ChengBenParameter3);
	  $HXGCB=$this->get_G3($PTPCB,$get_sg_ChengBenParameter3);

	//   $XCCBG=$this->get_A3($PTPCBG,$get_sg_ChengBenParameter3);
	//   $PLCBG=$this->get_B3($LWPCBG,$get_sg_ChengBenParameter3);
	//   $DGCBG=$this->get_C3($PTPCBG,$get_sg_ChengBenParameter3);
	  $LWCBG=$this->get_D3($LWPCBG,$get_sg_ChengBenParameter3);
	//   $RZCBG=$this->get_E3($PTPCBG,$get_sg_ChengBenParameter3);
	//   $ZHBCBG=$this->get_F3($PTPCBG,$get_sg_ChengBenParameter3);

	   
	   echo "线材成本：".$XCCB.",盘螺成本:".$PLCB.",带钢成本:".$DGCB.",螺纹成本:".$LWCB.",热轧板卷成本:".$RZCB.",中厚板成本:".$ZHBCB."<br/>";
	   
	  

	   $sql = "select id from sg_HangYeChengBenIndex where type='".(12+$smalltype)."' and mc_type='".$mc_type."' and ndate='".$date."' order by id desc" ;
	   $query = $this->_dao->query($sql);
	   if($query!=null)
	   {
		   $sql="update sg_HangYeChengBenIndex set chenben='".$XCCB."',chenben_tax='".Round($XCCB*(1+$zzsl))."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(12+$smalltype);  
		   $this->_dao->execute($sql);
		   $sql="update sg_HangYeChengBenIndex set chenben='".$PLCB."',chenben_tax='".Round($PLCB*(1+$zzsl))."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(13+$smalltype);  
		   $this->_dao->execute($sql);
		   $sql="update sg_HangYeChengBenIndex set chenben='".$DGCB."',chenben_tax='".Round($DGCB*(1+$zzsl))."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(14+$smalltype);  
		   $this->_dao->execute($sql);
		   $sql="update sg_HangYeChengBenIndex set chenben1='".$LWCBG."',chenben2='".Round($LWCBG*(1+$zzsl))."',chenben='".$LWCB."',chenben_tax='".Round($LWCB*(1+$zzsl))."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(15+$smalltype);  
		   $this->_dao->execute($sql);
		   $sql="update sg_HangYeChengBenIndex set chenben='".$RZCB."',chenben_tax='".Round($RZCB*(1+$zzsl))."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(16+$smalltype);  
		   $this->_dao->execute($sql);
		   $sql="update sg_HangYeChengBenIndex set chenben='".$ZHBCB."',chenben_tax='".Round($ZHBCB*(1+$zzsl))."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(17+$smalltype);  
		   $this->_dao->execute($sql);
		    $sql="update sg_HangYeChengBenIndex set chenben='".$HXGCB."',chenben_tax='".Round($HXGCB*(1+$zzsl))."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(18+$smalltype);  
		   $this->_dao->execute($sql);
	   }
	   else
	   {

		   $sql="insert into `sg_HangYeChengBenIndex`(type,chenben,chenben_tax,ndate,mc_type) values(".(12+$smalltype).",'".$XCCB."','".Round($XCCB*(1+$zzsl))."','".$date."','".$mc_type."')";
		   echo $sql."<br/>";
		   $this->_dao->execute($sql);
		   $sql="insert into `sg_HangYeChengBenIndex`(type,chenben,chenben_tax,ndate,mc_type) values(".(13+$smalltype).",'".$PLCB."','".Round($PLCB*(1+$zzsl))."','".$date."','".$mc_type."')";
		   echo $sql."<br/>";
		   $this->_dao->execute($sql);
		   $sql="insert into `sg_HangYeChengBenIndex`(type,chenben,chenben_tax,ndate,mc_type) values(".(14+$smalltype).",'".$DGCB."','".Round($DGCB*(1+$zzsl))."','".$date."','".$mc_type."')";
		   echo $sql."<br/>";
		   $this->_dao->execute($sql);
		   $sql="insert into `sg_HangYeChengBenIndex`(type,chenben1,chenben2,chenben,chenben_tax,ndate,mc_type) values(".(15+$smalltype).",'".$LWCBG."','".Round($LWCBG*(1+$zzsl))."','".$LWCB."','".Round($LWCB*(1+$zzsl))."','".$date."','".$mc_type."')";
		   echo $sql."<br/>";
		   $this->_dao->execute($sql);
		   $sql="insert into `sg_HangYeChengBenIndex`(type,chenben,chenben_tax,ndate,mc_type) values(".(16+$smalltype).",'".$RZCB."','".Round($RZCB*(1+$zzsl))."','".$date."','".$mc_type."')";
		   echo $sql."<br/>";
		   $this->_dao->execute($sql);
		   $sql="insert into `sg_HangYeChengBenIndex`(type,chenben,chenben_tax,ndate,mc_type) values(".(17+$smalltype).",'".$ZHBCB."','".Round($ZHBCB*(1+$zzsl))."','".$date."','".$mc_type."')";
		   echo $sql."<br/>";
		   $this->_dao->execute($sql);
		   $sql="insert into `sg_HangYeChengBenIndex`(type,chenben,chenben_tax,ndate,mc_type) values(".(18+$smalltype).",'".$ZHBCB."','".Round($ZHBCB*(1+$zzsl))."','".$date."','".$mc_type."')";
		   echo $sql."<br/>";
		   $this->_dao->execute($sql);
	   }     
       

   }


   public function jsdlcb($params)
   {
 		$zhuisutype=array('21','22','23','24');
		$qggptype=array('25','26');
	   $tscb=$params['tscb'];
	   $date=$params['date'];
	   $mc_type=$params['mc_type'];
	   $type=$params['type'];
	   $zzsl=$params['zzsl'];
	   $busbankrate=$params['busbankrate'];
	  // $type=7;
	   $smalltype=($type-1)*30;

	   if(in_array($type,$qggptype))
		{
			$jiage_arr1 = $this->maindao->get_shpi_material("4,50",$params['date']);
			if(isset($jiage_arr1['50']))
			{
				$tscb =$jiage_arr1['50'];
			}
			else
			{
				echo "全国铁水成本未计算，请耐心等待";
				return; 
			}
		}
		else {

			$sql = "select id,chenben from sg_HangYeChengBenIndex where type='".($type==6?"35":"125")."' and mc_type='".$mc_type."' and ndate='".$date."' order by id desc limit 1" ;
			$query = $this->_dao->getrow($sql);
				if($query!=null)
				{
					if(empty($tscb))
					{
					$tscb=$query ['chenben'];
					}
				}
				else
				{
					echo "铁水成本未计算，你需先计算铁水成本";
					return; 
				}
		}
	   

       
	   $get_sg_ChengBenParameter2 = $this->_dao->get_sg_ChengBenParameter2($date,$type,$mc_type);
       if(in_array($type, $zhuisutype))
		{
			$zhuisudate=date('Y-m-d',strtotime('-'.$get_sg_ChengBenParameter2['zhuisuday'].' day',strtotime($date)));
			$flag = 1;
			while(true)
			{
				if(file_get_contents(APP_URL_WWW.'/isholiday.php?date='.$zhuisudate)!="1")
				{ 
				break;
				} 
				$zhuisudate=date('Y-m-d',strtotime('-'.$flag.' day',strtotime($zhuisudate)));
			}
			$sql = "select id,chenben from sg_HangYeChengBenIndex where type='35' and mc_type='".$mc_type."' and ndate='".$zhuisudate."' order by id desc limit 1" ;
		    $query = $this->_dao->getrow($sql);
			$tscb=$query ['chenben'];
		}

	   if(empty($busbankrate))
	   {
		$busbankrateinfo = $this->_dao->get_drc_hbh_txl($date);//贴现率
		$busbankrate = $busbankrateinfo['rd3'];
	   }
	   if(empty($zzsl))
	   {
		//$cntaxrateinfo = $this->maindao->get_master_cntaxrate($date);//税率
		//$zzsl = $cntaxrateinfo['rvalue'];
                $zzsl = $this->maindao->get_master_cntaxrate($date,1);//税率
	   }
	   
	   //$zzsl=0.13;
	   //$busbankrate =3.35;
		$pricearr=array();
	   if($get_sg_ChengBenParameter2['FgPriceidA'])
	   {
		  $pricearr[0]=$get_sg_ChengBenParameter2['FgPriceidA'];
	   }
	   if($get_sg_ChengBenParameter2['GmPriceidB'])
	   {
		  $pricearr[1]=$get_sg_ChengBenParameter2['GmPriceidB'];
	   }
	   if($get_sg_ChengBenParameter2['GtPriceidC'])
	   {
		  $pricearr[2]=$get_sg_ChengBenParameter2['GtPriceidC'];
	   }
	   if($get_sg_ChengBenParameter2['FdPriceidD'])
	   {
		  $pricearr[3]=$get_sg_ChengBenParameter2['FdPriceidD'];
	   }
	   if($get_sg_ChengBenParameter2['CggldjPriceid'])
	   {
		  $pricearr[4]=$get_sg_ChengBenParameter2['CggldjPriceid'];
	   }
	   if($get_sg_ChengBenParameter2['GgldjPriceid'])
	   {
		  $pricearr[5]=$get_sg_ChengBenParameter2['GgldjPriceid'];
	   }
	   $priceidstr=  implode( ",", $pricearr );

	   $price_arr=$this->maindao->getpricebypricestr($priceidstr,$date);
	   if(in_array($type, $zhuisutype))
		{
			$price_arr1=$this->maindao->getpricebypricestr($pricearr[0],$zhuisudate);
			$price_arr[$pricearr[0]]=$price_arr1[$pricearr[0]];
		}
		if(in_array($type,$qggptype))
		{
			$price_arr[$pricearr[0]] = $jiage_arr1['4'];
		}
	 //  print_r($price_arr);
	   $X=$this->get_F1($tscb,$get_sg_ChengBenParameter2);
	 //echo  "吨钢成本：".$X."<br/>";


	 //更新铁水成本吨钢成本
	   //$sql=" update sg_HangYeChengBenIndex set chenben_ton='".$X."' where id='".$query['id']."'";
	  // $this->_dao->execute($sql);
	   
	 
	   $a=$this->get_A2($price_arr[$pricearr[0]],$get_sg_ChengBenParameter2,$zzsl,$busbankrate);//废钢
	   $b=$this->get_B2($price_arr[$pricearr[1]],$get_sg_ChengBenParameter2,$zzsl,$busbankrate);//唐山66%铁精粉
	   $c=$this->get_C2($price_arr[$pricearr[1]],$get_sg_ChengBenParameter2,$zzsl,$busbankrate);//唐山66%铁精粉
	   $d=$this->get_D2($price_arr[$pricearr[2]],$get_sg_ChengBenParameter2,$zzsl,$busbankrate);//唐山66%铁精粉
	   $e=$this->get_E2($price_arr[$pricearr[3]],$get_sg_ChengBenParameter2,$zzsl,$busbankrate);//唐山66%铁精粉
	   $cgjdj=Round($price_arr[$pricearr[4]]*$get_sg_ChengBenParameter2['DunGangXiaoHao1']);//超高功率电极吨钢成本
	   $gjdj=Round($price_arr[$pricearr[5]]*$get_sg_ChengBenParameter2['DunGangXiaoHao2'],2);//功率电极吨钢成本
	   $hjdianfei=$this->_dao->gethjdianfei_bytype($date,$type);
	   if($hjdianfei)
	   {
		$get_sg_ChengBenParameter2['PingDuanDianFei']=$hjdianfei['dta_6'];
	   	$get_sg_ChengBenParameter2['GuDianFei']=$hjdianfei['dta_8'];
	   }
	   $df=Round($get_sg_ChengBenParameter2['DianHao']*$get_sg_ChengBenParameter2['PingDuanDianFei']);//电费吨钢成本
	   $dfg=Round($get_sg_ChengBenParameter2['DianHao']*$get_sg_ChengBenParameter2['GuDianFei']);//电费吨钢成本

	   
	   
	   echo "废钢吨钢成本：".$a.",硅锰普碳坯成本:".$b.",硅锰螺纹钢成本:".$c.",硅铁普碳坯成本:".$d.",钒氮合金螺纹钢成本：".$e."<br/>";
	   
	   //echo ($X+$a+$b+$d+150+50-5)."<br/>";//铁水吨钢成本+废钢吨钢成本+硅锰普碳坯成本+硅铁普碳坯成本+动力人工制造+辅料-钢渣回收;
	   //echo $X+$a+$c+$e+150+50-5;
	   //echo "shen";
	   //echo $X+$a+$b+$d+$cgjdj+$gjdj+$df+$get_sg_ChengBenParameter2['DlrgMake']+$get_sg_ChengBenParameter2['FuLiao']-$get_sg_ChengBenParameter2['GangZhaHuiShou'];
	   $PTPCB=Round($X+$a+$b+$d+$cgjdj+$gjdj+$df+$get_sg_ChengBenParameter2['DlrgMake']+$get_sg_ChengBenParameter2['FuLiao']-$get_sg_ChengBenParameter2['GangZhaHuiShou']);
	   $PTPCBG=Round($X+$a+$b+$d+$cgjdj+$gjdj+$dfg+$get_sg_ChengBenParameter2['DlrgMake']+$get_sg_ChengBenParameter2['FuLiao']-$get_sg_ChengBenParameter2['GangZhaHuiShou']);

	   $LWPCB=Round($X+$a+$c+$e+$cgjdj+$gjdj+$df+$get_sg_ChengBenParameter2['DlrgMake']+$get_sg_ChengBenParameter2['FuLiao']-$get_sg_ChengBenParameter2['GangZhaHuiShou']);
	   $LWPCBG=Round($X+$a+$c+$e+$cgjdj+$gjdj+$dfg+$get_sg_ChengBenParameter2['DlrgMake']+$get_sg_ChengBenParameter2['FuLiao']-$get_sg_ChengBenParameter2['GangZhaHuiShou']);
	   echo "普碳坯成本：".$PTPCB."螺纹坯成本：".$LWPCB;

	

	   $sql = "select id from sg_HangYeChengBenIndex where type='".(1+$smalltype)."' and mc_type='".$mc_type."' and ndate='".$date."' order by id desc" ;
	   $query = $this->_dao->query($sql);
	   if($query!=null)
	   {
		   $sql="update sg_HangYeChengBenIndex set price='".$price_arr[$pricearr[0]]."',chenben_ton='".$a."' where ndate='".$date."'  and mc_type='".$mc_type."' and type=".(1+$smalltype);
		   $this->_dao->execute($sql);
		   $sql="update sg_HangYeChengBenIndex set price='".$price_arr[$pricearr[4]]."',chenben_ton='".$cgjdj."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(2+$smalltype);
		   $this->_dao->execute($sql);
		   $sql="update sg_HangYeChengBenIndex set price='".$price_arr[$pricearr[5]]."',chenben_ton='".$gjdj."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(3+$smalltype);
		   $this->_dao->execute($sql);
		   $sql="update sg_HangYeChengBenIndex set chenben_ton='".$df."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(4+$smalltype);
		   $this->_dao->execute($sql);

		   $sql="update sg_HangYeChengBenIndex set price='".$price_arr[$pricearr[1]]."',chenben1='".$b."',chenben2='".$c."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(5+$smalltype); 
		   $this->_dao->execute($sql);
		   $sql="update sg_HangYeChengBenIndex set price='".$price_arr[$pricearr[2]]."',chenben1='".$d."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(6+$smalltype); 
		   $this->_dao->execute($sql);
		   $sql="update sg_HangYeChengBenIndex set price='".$price_arr[$pricearr[3]]."',chenben2='".$e."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(7+$smalltype); 
		   $this->_dao->execute($sql);
		   $sql="update sg_HangYeChengBenIndex set chenben1='".$PTPCBG."',chenben2='".Round($PTPCBG*(1+$zzsl))."',chenben='".$PTPCB."',chenben_tax='".Round($PTPCB*(1+$zzsl))."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(8+$smalltype);  
		   $this->_dao->execute($sql);
		   $sql="update sg_HangYeChengBenIndex set chenben1='".$LWPCBG."',chenben2='".Round($LWPCBG*(1+$zzsl))."',chenben='".$LWPCB."',chenben_tax='".Round($LWPCB*(1+$zzsl))."' where ndate='".$date."' and mc_type='".$mc_type."' and type=".(9+$smalltype);  
		   $this->_dao->execute($sql);
	   }
	   else
	   {
		   $sql="insert into `sg_HangYeChengBenIndex`(type,price,chenben_ton,ndate,mc_type) values(".(1+$smalltype).",'".$price_arr[$pricearr[0]]."','".$a."','".$date."','".$mc_type."')";
		   echo $sql."<br/>";
		   $this->_dao->execute($sql);
		   $sql="insert into `sg_HangYeChengBenIndex`(type,price,chenben_ton,ndate,mc_type) values(".(2+$smalltype).",'".$price_arr[$pricearr[4]]."','".$cgjdj."','".$date."','".$mc_type."')";
		   echo $sql."<br/>";
		   $this->_dao->execute($sql);
		   $sql="insert into `sg_HangYeChengBenIndex`(type,price,chenben_ton,ndate,mc_type) values(".(3+$smalltype).",'".$price_arr[$pricearr[5]]."','".$gjdj."','".$date."','".$mc_type."')";
		   echo $sql."<br/>";
		   $this->_dao->execute($sql);
		   $sql="insert into `sg_HangYeChengBenIndex`(type,chenben_ton,ndate,mc_type) values(".(4+$smalltype).",'".$df."','".$date."','".$mc_type."')";
		   echo $sql."<br/>";
		   $this->_dao->execute($sql);
		   $sql="insert into `sg_HangYeChengBenIndex`(type,price,chenben1,chenben2,ndate,mc_type) values(".(5+$smalltype).",'".$price_arr[$pricearr[1]]."','".$b."','".$c."','".$date."','".$mc_type."')";
		   echo $sql."<br/>";
		  $this->_dao->execute($sql);
		   $sql="insert into `sg_HangYeChengBenIndex`(type,price,chenben1,ndate,mc_type) values(".(6+$smalltype).",'".$price_arr[$pricearr[2]]."','".$d."','".$date."','".$mc_type."')";
		   echo $sql."<br/>";
		   $this->_dao->execute($sql);
		   $sql="insert into `sg_HangYeChengBenIndex`(type,price,chenben2,ndate,mc_type) values(".(7+$smalltype).",'".$price_arr[$pricearr[3]]."','".$e."','".$date."','".$mc_type."')";
		   echo $sql."<br/>";
		   $this->_dao->execute($sql);
		   $sql="insert into `sg_HangYeChengBenIndex`(type,chenben1,chenben2,chenben,chenben_tax,ndate,mc_type) values(".(8+$smalltype).",'".$PTPCBG."','".Round($PTPCBG*(1+$zzsl))."','".$PTPCB."','".Round($PTPCB*(1+$zzsl))."','".$date."','".$mc_type."')";
		   echo $sql."<br/>";
		   $this->_dao->execute($sql);
		   $sql="insert into `sg_HangYeChengBenIndex`(type,chenben1,chenben2,chenben,chenben_tax,ndate,mc_type) values(".(9+$smalltype).",'".$LWPCBG."','".Round($LWPCBG*(1+$zzsl))."','".$LWPCB."','".Round($LWPCB*(1+$zzsl))."','".$date."','".$mc_type."')";
		   echo $sql."<br/>";
		   $this->_dao->execute($sql);
	   }     

	// if(in_array($type,$qggptype))
	// {
	// 	return false;
	// }



	   $info['date']=$date;
	   $info['mc_type']=$mc_type;
	   $info['type']=$type;
	   $info['PTPCB']=$PTPCB;
	   $info['LWPCB']=$LWPCB;
	   $info['PTPCBG']=$PTPCBG;
	   $info['LWPCBG']=$LWPCBG;
	   $info['zzsl']=$zzsl;

       $this->jsgccb($info);

   }






	
   // 1查询 市场价格
    public function selectSCPrice($topicture,$mconmanagedate) {//根据id，指定日期获取价格
		
		if(strlen($topicture)=='6'){
			$where = " and topicture = '$topicture' ";
		}else if (strlen($topicture)=='7'){
			$where = " and mastertopid = '$topicture' ";
		}
		
    	$sql = "select price,pricemk from marketconditions where mconmanagedate<='$mconmanagedate 23:59:59' $where order by mconmanagedate desc limit 1";
    	$price1 = $this->maindao->getRow($sql);
		// if($topicture!='H98640')
		// {
		// 	$price=$price1[price];
		// }
		// else
		// {
		// 	$price=$price1[pricemk];
		// }
		$price=$price1['price'];
    	return $price;
    }

		function get_xhdate($today)//根据当前日获取上个钢之家工作日
		{
			//echo "xiangbin"; exit;
			   $flag=1;
				$lastday=$today;
					while(true)
					{
						$lastday=date('Y-m-d',strtotime('-'.$flag.' day',strtotime($lastday)));
						//echo $lastday;
						if(file_get_contents(APP_URL_WWW.'/isholiday.php?date='.$lastday)!="1")
						{ 
						  break;
						} 
					}
				$today_s=$today;
				$lastday_s=$lastday;
			
			/* if(file_get_contents(APP_URL_WWW.'/isholiday.php?date='.$today)!="1")//不是
			{ 
			   
				$flag=1;
				$lastday=$today;
					while(true)
					{
						$lastday=date('Y-m-d',strtotime('-'.$flag.' day',strtotime($lastday)));
						//echo $lastday;
						if(file_get_contents(APP_URL_WWW.'/isholiday.php?date='.$lastday)!="1")
						{ 
						  break;
						} 
					}
				$today_s=$today;
				$lastday_s=$lastday;
				
				//echo  $lastday_s;
			}
			else// 今天是节假日
			{//echo "xiangbin";exit;
			//取不是节假日的当天时间
				$todayflag=1;
				// while(true)
				// {
					// $today=date('Y-m-d',strtotime('-'.$todayflag.' day',strtotime($today)));
					// if(!file_get_contents(APP_URL_WWW.'/isholiday.php?date='.$today))
					// { 
					   // break;
					// } 
				// }
				//取昨天的日期
				$lastday=$today;
				$lastflag=1;
				while(true)
				{
					$lastday=date('Y-m-d',strtotime('-'.$lastflag.' day',strtotime($lastday)));
					if(file_get_contents(APP_URL_WWW.'/isholiday.php?date='.$lastday)!="1")
					{ 
						break;
					} 
				}
				$today_s=$today;
				$lastday_s=$lastday;
			}    */

			return  $lastday_s;
		}
		function get_xhdate_new($today)//根据当前日期获取工作日
		{
			   $flag=1;
				$lastday=$today;
					while(true)
					{
						
						if(file_get_contents(APP_URL_WWW.'/isholiday.php?date='.$lastday)!="1")
						{ 
						  break;
						} 
						$lastday=date('Y-m-d',strtotime('-'.$flag.' day',strtotime($lastday)));
					}
			return  $lastday;
		}
		function get_A1($price,$ChengBenParameter,$zzsl,$cdtxl)
		{
			//return ($price+$ChengBenParameter2['DaoChangYunFeiA'])*(1+$ChengBenParameter2['TuSunA'])*$ChengBenParameter2['BiLiA'];
			//return ($price+20)/(1-0.009)/(1-0.08)*0.9*1.65/(1+$zzsl);

		   if($ChengBenParameter['isChengDuiA']==1)
		   {
			return Round((($price+$ChengBenParameter['DaoChangYunFeiA'])*(1-$cdtxl/365/100*90))/(1-($ChengBenParameter['TuSunA']/100))/(1-($ChengBenParameter['HanShuiLvA']/100))*($ChengBenParameter['JinKouKuangZBA']/100)*$ChengBenParameter['DtKuangHaoB']/(1+$zzsl));
		   }
		   else
		   {
			 return Round(($price+$ChengBenParameter['DaoChangYunFeiA'])/(1-($ChengBenParameter['TuSunA']/100))/(1-($ChengBenParameter['HanShuiLvA']/100))*($ChengBenParameter['JinKouKuangZBA']/100)*$ChengBenParameter['DtKuangHaoB']/(1+$zzsl));
		   }

			//(【价格】+【到厂运费】)/(1-【途损】)/(1-含水率)*进口矿占比*吨铁矿耗/(1+增值税率)
		}

		function get_B1($price,$ChengBenParameter,$zzsl,$cdtxl)
		{

		if((float)$ChengBenParameter['GuoChanKuangZBB']==0)
			{
				return 0;
			}
			if((float)$ChengBenParameter['isChengDuiB']==1)
		   {
			   //echo $ChengBenParameter['DaoChangYunFeiB']."|".$cdtxl."|".$ChengBenParameter['TuSunB']."|".$ChengBenParameter['HanShuiLvB']."|".$ChengBenParameter['GuoChanKuangZBB']."|".$ChengBenParameter['DtKuangHaoB'];
			  return Round((((float)$price+(float)$ChengBenParameter['DaoChangYunFeiB'])*(1-(float)$cdtxl/365/100*90))/(1-((float)$ChengBenParameter['TuSunB']/100))/(1-((float)$ChengBenParameter['HanShuiLvB']/100))*((float)$ChengBenParameter['GuoChanKuangZBB']/100)*(float)$ChengBenParameter['DtKuangHaoB']/(1+(float)$zzsl));
		   }
		   else
		   {
		   	return Round(((float)$price+(float)$ChengBenParameter['DaoChangYunFeiB'])/(1-((float)$ChengBenParameter['TuSunB']/100))/(1-((float)$ChengBenParameter['HanShuiLvB']/100))*((float)$ChengBenParameter['GuoChanKuangZBB']/100)*(float)$ChengBenParameter['DtKuangHaoB']/(1+(float)$zzsl));
		   } 
			//return (($price+10)*(1-$cdtxl/365/100*90))/(1-0.003)/(1-0)*0.1*1.65/(1+$zzsl);

			//A=((价格+【到厂运费】)*(1-承兑汇票贴现率/365/100*90))/(1-【途损】)/(1-含水率)*国产矿占比*吨铁矿耗/(1+增值税率)
		}
		function get_C1($price,$ChengBenParameter,$zzsl,$cdtxl)
		{
			if((float)$ChengBenParameter['isChengDuiC']==1)
		   {
			return Round((((float)$price+$ChengBenParameter['DaoChangYunFeiC'])*(1-(float)$cdtxl/365/100*90))/(1-((float)$ChengBenParameter['HanShuiLvC']/100))*(float)$ChengBenParameter['JiaoBiC']/(1+(float)$zzsl));
		   }
		   else
		   {
			return Round(((float)$price+(float)$ChengBenParameter['DaoChangYunFeiC'])/(1-((float)$ChengBenParameter['HanShuiLvC']/100))*(float)$ChengBenParameter['JiaoBiC']/(1+$zzsl));
		   }
			//return (($price+20)*(1-$cdtxl/365/100*90))/(1-0.08)*0.45/(1+$zzsl);
			//A=((价格+【到厂运费】)*(1-承兑汇票贴现率/365/100*90))/(1-含水率)*焦比/(1+增值税率)
		}
		function get_D1($price,$ChengBenParameter,$zzsl,$cdtxl)
		{
			if((float)$ChengBenParameter['isChengDuiD']==1)
		   {
			return Round((((float)$price+$ChengBenParameter['DaoChangYunFeiD'])*(1-(float)$cdtxl/365/100*90))/(1-((float)$ChengBenParameter['HanShuiLvD']/100))*(float)$ChengBenParameter['PenMeiLiangD']/(1+(float)$zzsl));
			
		   }
		   else
		   {
			return Round(((float)$price+(float)$ChengBenParameter['DaoChangYunFeiD'])/(1-((float)$ChengBenParameter['HanShuiLvD']/100))*(float)$ChengBenParameter['PenMeiLiangD']/(1+(float)$zzsl));
		   }
			//return (($price+0)*(1-$cdtxl/365/100*90))/(1-0.1)*0.15/(1+$zzsl);
			//A=((价格+【到厂运费】)*(1-承兑汇票贴现率/365/100*90))/(1-含水率)*喷煤量/(1+增值税率)
		}
		function get_E1($A,$B,$C,$D,$ChengBenParameter)
		{

// 			球团制造费用，元/吨铁		30		烧结辅料，元/吨铁	40
// 烧结动力人工制造费用，元/吨		90		炼铁辅料，元/吨	10
// 炼铁动力人工制造，元/吨		100		回收水渣，元/吨	45
//echo $A.",".$B.",".$C.",".$D;
			//return ($price+$ChengBenParameter2['DaoChangYunFeiA'])*(1+$ChengBenParameter2['TuSunA'])*$ChengBenParameter2['BiLiA'];
			return Round(((float)$A+(float)$B+(float)$C+(float)$D)+(float)$ChengBenParameter['QiuTuanMakeFei']+(float)$ChengBenParameter['ShaoJieFuLiao']+(float)$ChengBenParameter['ShaojieDlrgMakeFei']+(float)$ChengBenParameter['LianTieFuLiao']+(float)$ChengBenParameter['LianTieDlrgMakeFei']-(float)$ChengBenParameter['ShuiZhaHuiShou']);
			//return ($A+$B+$C+$D)+30+90+100+40+10-45;
			//A=((价格+【到厂运费】)*(1-承兑汇票贴现率/365/100*90))/(1-含水率)*喷煤量/(1+增值税率)
		}
		function get_F1($A,$ChengBenParameter2)
		{


			return Round((float)$A*((float)$ChengBenParameter2['StBiLiA']/100)*(float)$ChengBenParameter2['Gtlxh']);//铁水不含税成本*生铁比例*钢铁料消耗
			//return Round($A*0.85*1.06);//铁水不含税成本*生铁比例*钢铁料消耗
		
		
		}


		function get_A2($price,$ChengBenParameter2,$zzsl,$cdtxl)//废钢
		{
			if($ChengBenParameter2['isChengDuiA']==1)
			{
			    return Round((((float)$price+(float)$ChengBenParameter2['DaoChangYunFeiA'])*(1-(float)$cdtxl/365/100*90))/(1+(float)$zzsl)*((float)$ChengBenParameter2['FgBiLiA']/100)*(float)$ChengBenParameter2['Gtlxh']);
			}
			else
			{
				return Round(((float)$price+(float)$ChengBenParameter2['DaoChangYunFeiA'])/(1+(float)$zzsl)*((float)$ChengBenParameter2['FgBiLiA']/100)*(float)$ChengBenParameter2['Gtlxh']);
			}

			//return Round(($price+0)/(1+$zzsl)*0.15*1.06);
			//(【价格】+【废钢到厂运费】)/(1+税率)*废钢比例*钢铁料消耗
		}
		function get_B2($price,$ChengBenParameter2,$zzsl,$cdtxl)//硅锰
		{

			if($ChengBenParameter2['isChengDuiB']==1)
			{
			    return Round(($price+$ChengBenParameter2['DaoChangYunFeiB'])/(1+$zzsl)*(1-$cdtxl/365/100*90)*$ChengBenParameter2['PuTanPiXiaoHaoB']);
			}
			else
			{
				return Round(($price+$ChengBenParameter2['DaoChangYunFeiB'])/(1+$zzsl)*$ChengBenParameter2['PuTanPiXiaoHaoB']);
			}



			//return Round(($price+0)/(1+$zzsl)*(1-$cdtxl/365/100*90)*0.0054);

			//(【价格】+【废钢到厂运费】)/(1+税率)*(1-承兑汇票贴现率/365/100*90)*钢铁料消耗普碳坯消耗
		}

		function get_C2($price,$ChengBenParameter2,$zzsl,$cdtxl)//螺纹钢
		{


			if($ChengBenParameter2['isChengDuiB']==1)
			{
			    return Round(($price+$ChengBenParameter2['DaoChangYunFeiB'])/(1+$zzsl)*(1-$cdtxl/365/100*90)*$ChengBenParameter2['LuoWenPiXiaoHaoB']);
			}
			else
			{
				return Round(($price+$ChengBenParameter2['DaoChangYunFeiB'])/(1+$zzsl)*$ChengBenParameter2['LuoWenPiXiaoHaoB']);
			}

			//return Round(($price+0)/(1+$zzsl)*(1-$cdtxl/365/100*90)*0.028);

			//(【价格】+【废钢到厂运费】)/(1+税率)*(1-承兑汇票贴现率/365/100*90)*钢铁料消耗螺纹钢消耗
		}
		function get_D2($price,$ChengBenParameter2,$zzsl,$cdtxl)//硅铁普碳坯
		{

			if($ChengBenParameter2['isChengDuiC']==1)
			{
			    return Round(($price+$ChengBenParameter2['DaoChangYunFeiC'])/(1+$zzsl)*(1-$cdtxl/365/100*90)*$ChengBenParameter2['PuTanPiXiaoHaoC']);
			}
			else
			{
				return Round(($price+$ChengBenParameter2['DaoChangYunFeiC'])/(1+$zzsl)*$ChengBenParameter2['PuTanPiXiaoHaoC']);
			}
			//return Round(($price+0)/(1+$zzsl)*(1-$cdtxl/365/100*90)*0.0015);

			//(【价格】+【废钢到厂运费】)/(1+税率)*(1-承兑汇票贴现率/365/100*90)*普碳坯消耗
		}
		function get_E2($price,$ChengBenParameter2,$zzsl,$cdtxl)//钒氮合金 螺纹钢
		{

			if($ChengBenParameter2['isChengDuiD']==1)
			{
			    return Round(($price+$ChengBenParameter2['DaoChangYunFeiD'])/(1+$zzsl)*(1-$cdtxl/365/100*90)*$ChengBenParameter2['LuoWenPiXiaoHaoD']);
			}
			else
			{
				return Round(($price+$ChengBenParameter2['DaoChangYunFeiD'])/(1+$zzsl)*$ChengBenParameter2['LuoWenPiXiaoHaoD']);
			}

			//return Round(($price+0)/(1+$zzsl)*(1-$cdtxl/365/100*90)*0.00034);

			//(【价格】+【废钢到厂运费】)/(1+税率)*(1-承兑汇票贴现率/365/100*90)*钒氮合金螺纹坯消耗
		}
		// function get_F2($price,$ChengBenParameter2,$zzsl,$cdtxl)//钒氮合金 螺纹钢
		// {
		// 	return Round(($price+0)/(1+$zzsl)*(1-$cdtxl/365/100*90)*0.00034);
		// }
		function get_A3($price,$ChengBenParameter3)//线材成本
		{
			return Round($price+$ChengBenParameter3['ZhaZhiFeiYongXc']+$ChengBenParameter3['QiJianFeiYong']+$ChengBenParameter3['XiaoShouShuiJinJiFuJia']-$ChengBenParameter3['FgYHTPHuiShou']);//普碳坯成本不含税+轧制费用线材+期间费用+销售税金及附加-废钢氧化铁皮回收
		}
		function get_B3($price,$ChengBenParameter3)//盘螺成本
		{
			return Round($price+$ChengBenParameter3['ZhaZhiFeiYongPl']+$ChengBenParameter3['QiJianFeiYong']+$ChengBenParameter3['XiaoShouShuiJinJiFuJia']-$ChengBenParameter3['FgYHTPHuiShou']);//螺纹钢成本不含税+轧制费用盘螺+期间费用+销售税金及附加-废钢氧化铁皮回收
		}
		function get_C3($price,$ChengBenParameter3)//带钢成本
		{
			return Round($price+$ChengBenParameter3['ZhaZhiFeiYongDg']+$ChengBenParameter3['QiJianFeiYong']+$ChengBenParameter3['XiaoShouShuiJinJiFuJia']-$ChengBenParameter3['FgYHTPHuiShou']);//普碳坯成本不含税+轧制费用带钢+期间费用+销售税金及附加-废钢氧化铁皮回收
		}
		function get_D3($price,$ChengBenParameter3)//螺纹钢成本
		{

			//echo $ChengBenParameter3['ZhaZhiFeiYongLw']."|".$ChengBenParameter3['QiJianFeiYong']."|".$ChengBenParameter3['XiaoShouShuiJinJiFuJia']."|".$ChengBenParameter3['FgYHTPHuiShou']."|".$ChengBenParameter3['LuoWenGangFuCha'];
			return Round(($price+$ChengBenParameter3['ZhaZhiFeiYongLw']+$ChengBenParameter3['QiJianFeiYong']+$ChengBenParameter3['XiaoShouShuiJinJiFuJia']-$ChengBenParameter3['FgYHTPHuiShou'])*(1-($ChengBenParameter3['LuoWenGangFuCha']/100)));//(螺纹钢成本不含税+轧制费用螺纹钢+期间费用+销售税金及附加-废钢氧化铁皮回收）*(1-螺纹钢负差)
		}
		function get_E3($price,$ChengBenParameter3)//热轧板卷成本
		{
			return Round($price+$ChengBenParameter3['ZhaZhiFeiYongRz']+$ChengBenParameter3['QiJianFeiYong']+$ChengBenParameter3['XiaoShouShuiJinJiFuJia']-$ChengBenParameter3['FgYHTPHuiShou']);//普碳坯成本不含税+轧制费用带钢+期间费用+销售税金及附加-废钢氧化铁皮回收
		}
		function get_F3($price,$ChengBenParameter3)//中厚板成本
		{
			return Round($price+$ChengBenParameter3['ZhaZhiFeiYongZhb']+$ChengBenParameter3['QiJianFeiYong']+$ChengBenParameter3['XiaoShouShuiJinJiFuJia']-$ChengBenParameter3['FgYHTPHuiShou']);//普碳坯成本不含税+轧制费用带钢+期间费用+销售税金及附加-废钢氧化铁皮回收
		}
		function get_G3($price,$ChengBenParameter3)//H型钢成本
		{
			return Round(Round(($price+$ChengBenParameter3['ZhaZhiFeiYongHxg']+$ChengBenParameter3['QiJianFeiYong']+$ChengBenParameter3['XiaoShouShuiJinJiFuJia']-$ChengBenParameter3['FgYHTPHuiShou']))*$ChengBenParameter3['Hxgweight']);//普碳坯成本不含税+轧制费用带钢+期间费用+销售税金及附加-废钢氧化铁皮回收
		}


		public function jsts_dtcb($params)
		{
			$today=date("Y-m-d");
			if($params['curdate'])
			{
				if(checkdate(date("m",strtotime($params['curdate'])),date("d",strtotime($params['curdate'])),date("Y",strtotime($params['curdate']))))
				{
					$today=$params['curdate'];
				}
	
			}
			$date=$this->get_xhdate($today);
			//echo $date;
			echo $date;
			$type=$params['type'];
			$params['date']=$date;
			if(empty($type))
			{
				$this->jsts_dtcb_lg($params);
				$this->jsts_dtcb_hg($params);
				
			}
			else if($type==1)
			{
              $this->jsts_dtcb_lg($params);
			}
			else if($type==2)
			{
			  $this->jsts_dtcb_hg($params);
			}
			echo "执行完成";
		}

		public function jsts_dtcb_lg($params)
		{
			  

			//1:龙钢 2:汉钢
			$date=$params['date'];
			//$date='2020-10-19';
			if($params['callback'])
			{
				$get_sg_ChengBenParameter4=$params;
			}
			else
			{
			  $get_sg_ChengBenParameter4 = $this->_dao->get_sg_ChengBenParameter4($date,1);
			}
            // echo "555";
		   //print_r( $get_sg_ChengBenParameter4);exit;
		   //$params['date']='2020-10-19';
			//$this->jsts_dtcb_hg($params);exit;
			$A=$get_sg_ChengBenParameter4['Pszhishu'];
			$A1=$get_sg_ChengBenParameter4['ImportDaochangyf'];
			$A2=$get_sg_ChengBenParameter4['ImportHanshuilv'];
			$A3=$get_sg_ChengBenParameter4['ImportKuanggangzf'];
			$A4=$get_sg_ChengBenParameter4['ImportPinwei'];
			$B=$get_sg_ChengBenParameter4['KuaikuangPrice'];
			$B1=$get_sg_ChengBenParameter4['KuaikaungBili'];
			$C=$get_sg_ChengBenParameter4['QiutuankuangPrice'];
			$C1=$get_sg_ChengBenParameter4['QiutuankuangBili'];
			$D=$get_sg_ChengBenParameter4['ShaojiekuangBili'];
			$D1=$get_sg_ChengBenParameter4['ShaojiekuangJiagong'];
			$D2=$get_sg_ChengBenParameter4['ShaojieChengpinlv'];
			$D3=$get_sg_ChengBenParameter4['ShaojieHantieliang'];
			$E=$get_sg_ChengBenParameter4['JiaotanPrice'];
			$E1=$get_sg_ChengBenParameter4['Jiaobi'];
			$E2=$get_sg_ChengBenParameter4['JiaomohuishouPrice'];
			$E3=$get_sg_ChengBenParameter4['Jiaomohuishengliang'];
			$F=$get_sg_ChengBenParameter4['PenchuimeiPrice'];
			$F1=$get_sg_ChengBenParameter4['Penmeiliang'];
			$G=$get_sg_ChengBenParameter4['ShengtiekuangPrice'];
			$G1=$get_sg_ChengBenParameter4['ShengtiekuangYongliang'];
			$H=$get_sg_ChengBenParameter4['LiantieJiagongfei'];
			$H1=$get_sg_ChengBenParameter4['LiantieHuishouxiang'];
			$H2=$get_sg_ChengBenParameter4['DuntieKuanghao'];
			$I=$get_sg_ChengBenParameter4['GangpiJiagongfei'];
			$J=$get_sg_ChengBenParameter4['Gangcaijiagongfei'];
			$K=$get_sg_ChengBenParameter4['QijianFeiyong'];
			$L=$get_sg_ChengBenParameter4['Gangcaierjishoujia'];
			$L1=$get_sg_ChengBenParameter4['Gangcaiyunfeidailifei'];
			$O=$get_sg_ChengBenParameter4['USRMB'];
			//$M=13;
			//$N=9;

//$cntaxrateinfo = $this->maindao->get_master_cntaxrate($date);//税率
			//$zzsl = $cntaxrateinfo['rvalue'];
			$zzsl = $this->maindao->get_master_cntaxrate($date,1);//税率
			$yfzzsl = $this->maindao->get_master_cntaxrate($date,2);//运费税率

			$pricearr[0]='H98640';
			//$pricearr[1]='668312';
			//$pricearr[2]='K16330';
			$pricearr[1]='A183112';
			$pricearr[2]='M163302';
			$pricearr[3]='438510';
			if($B==0||empty($B))
			{
			   $pricearr[4]='D28630';
			}
			if($C==0||empty($C))
			{
			   $pricearr[5]='578620';
			}
			
            //$date=$this->get_xhdate($today);
			$priceidstr=  implode( ",", $pricearr );

			 $price_arr=$this->maindao->getpricebypricestr($priceidstr,$date);
			 

		  $price_arr[$pricearr[0]]= $this->selectSCPrice($pricearr[0],$date);
		  if($A)
		  {
			$price_arr[$pricearr[0]]=$A;
		  }
		  if($E)
		  {
			$price_arr[$pricearr[1]]=$E;
		  }
		 
		  if($F)
		  {
			$price_arr[$pricearr[2]]=$F;
		  }
		  if($G)
		  {
			$price_arr[$pricearr[3]]=$G;
		  }

			
			//echo $zzsl;
			if(!$O)
			{
				$rmbrateinfo = $this->_dao->get_drc_rmb_hl($date);//人民币汇率中间价
				$rmbraterd1 = $rmbrateinfo['rd1'];
				$O=$rmbraterd1;
			}
			if(!$L)
			{
				
				$SGLoginURL=SGDCURL.'/web/sgxymx.php?action=getprice&date='.$date.'&type=1';
				$L = file_get_contents($SGLoginURL, false, stream_context_create(array('ssl' => array('verify_peer' => false, 'verify_peer_name' => false))));
				
			    //$L= $this->_dao->get_drc_sg_data_table($date," and dta_4='西咸片区' and dta_5='西安' ");
			}
			// echo $rmbraterd1."<br/>";
			 //1计算烧结矿成本 (((E3)*6.7*1.13*0.92+25)/1.13+167/1.09)/0.92/62*55.6*0.92+160
			//$X=((($price_arr[$pricearr[0]])*$O/100*(1+$zzsl)*(1-$A2/100)+$A3)/(1+$zzsl)+$A1/(1+$yfzzsl))/(1-$A2/100)/$A4*$D3*$D2/100+$D1;
			$X=((($price_arr[$pricearr[0]])*$O/100*(1+$zzsl)*(1-$A2/100)+$A3)/(1+$zzsl)+$A1/(1+$yfzzsl))/(1-$A2/100)/$A4*$D3*$D2/100+$D1+($price_arr[$pricearr[1]]*$E1/(1+$zzsl)*0.04);
			//echo   '((('.$price_arr[$pricearr[0]].')*'.$O.'/100*(1+'.$zzsl.')*(1-'.$A2.'/100)+'.$A3.')/(1+'.$zzsl.')+'.$A1.'/(1+'.$yfzzsl.'))/(1-'.$A2.'/100)/'.$A4.'*'.$D3.'*'.$D2.'/100'.'+'.$D1;
			$X=Round($X,1);
          // echo $price_arr['H98640']."|烧结矿成本".$X."<br/>";
			if($pricearr[4])
			{
			   $price=$price_arr[$pricearr[4]];
			   $B=$this->get_LG_B($price,$zzsl,$yfzzsl,$A1);
			   //echo "B".$B."<br/>";
			}
			if($pricearr[5])
			{
			   $price=$price_arr[$pricearr[5]];
			   $C=$this->get_LG_C($price,$zzsl);

			  // echo "C".$C."<br/>";
			}
			//2计算生铁成本
		   $Y= $X*$H2*$D/100+$B*$H2*$B1/100+$C*$H2*$C1/100+($price_arr[$pricearr[1]]/(1+$zzsl)*$E1-$E2*$E3+$price_arr[$pricearr[2]]/(1+$zzsl)*$F1)+$H+$price_arr[$pricearr[3]]/(1+$zzsl)*$G1-$H1;
            //echo  $X.'*'.$H2.'*'.$D.'/100+'.$B.'*'.$H2.'*'.$B1.'/100+'.$C.'*'.$H2.'*'.$C1.'/100+('.$price_arr[$pricearr[1]].'/(1+'.$zzsl.')*'.$E1.'-'.$E2.'*'.$E3.'+'.$price_arr[$pricearr[2]].'/(1+'.$zzsl.')*'.$F1.')+'.$H.'+'.$price_arr[$pricearr[3]].'/(1+'.$zzsl.')*'.$G1.'-'.$H1;
		   $Y=Round($Y,1);
          // echo "生铁成本:".$Y."<br/>";
		   //3、计算钢坯成本 Z=Y+I
		   $Z=$Y+$I;
		  $Z=Round($Z);
		   //echo "钢坯成本:".$Z."<br/>";
		  // 4、计算钢材成本 W=Z+J
		   $W=$Z+$J;
		 // echo  "钢材成本:".$W."<br/>";
		   //5、计算吨材利润 U=(L-L1)/(1+M/100)-W-K
		  //echo "西安螺纹钢指导价:".$L."<br/>";
		   $U=($L-$L1)/(1+$zzsl)-$W-$K;
		   $U=Round($U);
		   //echo "吨材利润:".$U."<br/>";
			 //exit;
			 if($params['callback']!=2)
			{
				   $sql = "select id from sg_DongTaiChengBenIndex where type='1' and ndate='".$date."' order by id desc" ;
					$query = $this->_dao->query($sql);
					if($query!=null)
					{
						$sql="update sg_DongTaiChengBenIndex set chenben='".$X."' where ndate='".$date."' and type=1";
						$this->_dao->execute($sql);
						$sql="update sg_DongTaiChengBenIndex set chenben='".$Y."' where ndate='".$date."' and type=2";
						$this->_dao->execute($sql);
						$sql="update sg_DongTaiChengBenIndex set chenben='".$Z."' where ndate='".$date."' and type=3";
						$this->_dao->execute($sql);
						$sql="update sg_DongTaiChengBenIndex set chenben='".$W."' where ndate='".$date."' and type=4";
						$this->_dao->execute($sql);
						$sql="update sg_DongTaiChengBenIndex set chenben='".$U."' where ndate='".$date."' and type=5"; 
						$this->_dao->execute($sql);
						
					}
					else
					{
						$sql="insert into `sg_DongTaiChengBenIndex`(type,chenben,ndate) values('1','".$X."','".$date."')";
						//echo $sql."<br/>";
						$this->_dao->execute($sql);
						$sql="insert into `sg_DongTaiChengBenIndex`(type,chenben,ndate) values('2','".$Y."','".$date."')";
						//echo $sql."<br/>";
						$this->_dao->execute($sql);
						$sql="insert into `sg_DongTaiChengBenIndex`(type,chenben,ndate) values('3','".$Z."','".$date."')";
						//echo $sql."<br/>";
						$this->_dao->execute($sql);
						$sql="insert into `sg_DongTaiChengBenIndex`(type,chenben,ndate) values('4','".$W."','".$date."')";
					//	echo $sql."<br/>";
						$this->_dao->execute($sql);
						$sql="insert into `sg_DongTaiChengBenIndex`(type,chenben,ndate) values('5','".$U."','".$date."')";
						//echo $sql."<br/>";
						$this->_dao->execute($sql);
					}
	       }
			$shuju['SJKCB']=$X;
			$shuju['STCB']=$Y;
			$shuju['GPCB']=$Z;
			$shuju['GCCB']=$W;
			$shuju['DCLR']=$U;
		   if($params['callback'])
		   {
			return $shuju;
		   }

		}
		public function jsts_dtcb_hg($params)
		{

			
			$date=$params['date'];
			if($params['callback'])
			{
				$get_sg_ChengBenParameter4=$params;
			}
			else
			{
				$get_sg_ChengBenParameter4 = $this->_dao->get_sg_ChengBenParameter4($date,2);
			}
			
			$A=$get_sg_ChengBenParameter4['Pszhishu'];
			$A1=$get_sg_ChengBenParameter4['ImportDaochangyf'];
			$A2=$get_sg_ChengBenParameter4['ImportHanshuilv'];
			$A3=$get_sg_ChengBenParameter4['ImportKuanggangzf'];
			$A4=$get_sg_ChengBenParameter4['ImportPinwei'];
		
			$C=$get_sg_ChengBenParameter4['QiutuankuangPrice'];
			$C1=$get_sg_ChengBenParameter4['QiutuankuangBili'];
			$D=$get_sg_ChengBenParameter4['ShaojiekuangBili'];
			$D1=$get_sg_ChengBenParameter4['ShaojiekuangJiagong'];
			$D2=$get_sg_ChengBenParameter4['ShaojieChengpinlv'];
			$D3=$get_sg_ChengBenParameter4['ShaojieHantieliang'];
			$E=$get_sg_ChengBenParameter4['JiaotanPrice'];
			$E1=$get_sg_ChengBenParameter4['Jiaobi'];
			$E2=$get_sg_ChengBenParameter4['JiaomohuishouPrice'];
			$E3=$get_sg_ChengBenParameter4['Jiaomohuishengliang'];
			$F=$get_sg_ChengBenParameter4['PenchuimeiPrice'];
			$F1=$get_sg_ChengBenParameter4['Penmeiliang'];
			$H=$get_sg_ChengBenParameter4['LiantieJiagongfei'];
			$H1=$get_sg_ChengBenParameter4['LiantieHuishouxiang'];
			$H2=$get_sg_ChengBenParameter4['DuntieKuanghao'];
			$I=$get_sg_ChengBenParameter4['GangpiJiagongfei'];
			$J=$get_sg_ChengBenParameter4['Gangcaijiagongfei'];
			$K=$get_sg_ChengBenParameter4['QijianFeiyong'];
			$L=$get_sg_ChengBenParameter4['Gangcaierjishoujia'];
			$L1=$get_sg_ChengBenParameter4['Gangcaiyunfeidailifei'];
			$O=$get_sg_ChengBenParameter4['USRMB'];
			$Otherfee=$get_sg_ChengBenParameter4['Otherfee'];


			// $A=H98640;
			// $A1=220;
			// $A2=8;
			// $A3=25;
			// $A4=62;
			// $C=964;
			// $C1=20;
			// $D=80;
			// $D1=165;
			// $D2=91.5;
			// $D3=55.85;
			// $E=668312;
			// $E1=0.43;
			// $E2=750;
			// $E3=0.037;
			// $F=	746330;
			// $F1=0.13;
			// $H=	100;
			// $H2=1.68;
			// $I=	420;
			// $J=	125;
			// $K=98;
			// $L=4200;
			// $L1=130 ;
			// $M=13;
			// $N=9;
			// $O=0;
		   
			







			$pricearr[0]='H98640';
			$pricearr[1]='668312';
			$pricearr[2]='746330';
			
			if($C==0||empty($C))
			{
			   $pricearr[3]='678620';
			}
			
            //$date=$this->get_xhdate($today);
			
			$priceidstr=  implode( ",", $pricearr );

			 $price_arr=$this->maindao->getpricebypricestr($priceidstr,$date);
			 $price_arr[$pricearr[0]]= $this->selectSCPrice($pricearr[0],$date);
			if($A)
		  {
			$price_arr[$pricearr[0]]=$A;
		  }
		  if($E)
		  {
			$price_arr[$pricearr[1]]=$E;
		  }
		  if($F)
		  {
			$price_arr[$pricearr[2]]=$F;
		  }
			//$cntaxrateinfo = $this->maindao->get_master_cntaxrate($date);//税率
			//$zzsl = $cntaxrateinfo['rvalue'];
			$zzsl = $this->maindao->get_master_cntaxrate($date,1);//税率
			$yfzzsl = $this->maindao->get_master_cntaxrate($date,2);//运费税率
			//$yfzzsl=0.09;
			//echo $zzsl;
			if(!$O)
			{
				$rmbrateinfo = $this->_dao->get_drc_rmb_hl($date);//人民币汇率中间价
				$rmbraterd1 = $rmbrateinfo['rd1'];
				$O=$rmbraterd1;
			}
			if(!$L)
			{
				$SGLoginURL=SGDCURL.'/web/sgxymx.php?action=getprice&date='.$date.'&type=2';
				$L = file_get_contents($SGLoginURL, false, stream_context_create(array('ssl' => array('verify_peer' => false, 'verify_peer_name' => false))));
			   //$L= $this->_dao->get_drc_sg_data_table($date," and dta_4='成都片区' and dta_5='成都' ");
			}
			//echo "二次销售价".$L."<br/>";

			// $price_arr[$pricearr[0]]=114;
			// $price_arr[$pricearr[1]]=1970;
			// $price_arr[$pricearr[2]]=900;
              //$O=670;
			// 1、计算烧结矿成本 (((E3)*6.7*1.13*0.92+25)/1.13+167/1.09)/0.92/62*55.6*0.92+160
			// X=(((A)*O/100*(1+M/100)*（1-A2/100）+A3)/(1+M/100)+A1/(1+N/100)/（1-A2/100）/A4*D3*D2/100+D1
			//echo  '((('.$price_arr[$pricearr[0]].')*'.$O.'/100*(1+'.$zzsl.')*(1-'.$A2.'/100)+'.$A3.')/(1+'.$zzsl.')+'.$A1.'/(1+'.$yfzzsl.'))/(1-'.$A2.'/100)/'.$A4.'*'.$D3.'*'.$D2.'/100+'.$D1."<br/>";
			//$X=((($price_arr[$pricearr[0]])*$O/100*(1+$zzsl)*(1-$A2/100)+$A3)/(1+$zzsl)+$A1/(1+$yfzzsl))/(1-$A2/100)/$A4*$D3*$D2/100+$D1;
			$X=((($price_arr[$pricearr[0]])*$O/100*(1+$zzsl)*(1-$A2/100)+$A3)/(1+$zzsl)+$A1/(1+$yfzzsl))/(1-$A2/100)/$A4*$D3*$D2/100+$D1+($price_arr[$pricearr[1]]*$E1/(1+$zzsl)*0.04);
			$X=Round($X,2);
           //echo $price_arr['H98640']."|烧结矿成本".$X."<br/>";
			// 2、计算生铁成本
			// Y= X*H2*D/100+ C*H2*C1/100+(E/(1+M/100)*E1-E2*E3+F/（1+M/100）*F1)+H
			if($pricearr[3])
			{
			   $price=$price_arr[$pricearr[3]];
			   $C=$this->get_LG_C($price,$zzsl);

			  // echo "C".$C."<br/>";
			}
			$Y= $X*$H2*$D/100+ $C*$H2*$C1/100+($price_arr[$pricearr[1]]/(1+$zzsl)*$E1-$E2*$E3+$price_arr[$pricearr[2]]/(1+$zzsl)*$F1)+$H;
			$Y=Round($Y);
           //echo "生铁成本:".$Y."<br/>";
			// 3、计算钢坯成本 Z=Y+I
			$Z=$Y+$I;
			$Z=Round($Z);
			//echo "钢坯成本:".$Z."<br/>";
			// 4、计算钢材成本 W=Z+J
			$W=$Z+$J;
		
			//echo  "钢材成本:".$W."<br/>";
			// 5、计算吨材利润 U=(L-L1)/(1+M/100)-W-K
			//echo "成都螺纹钢指导价:".$L."<br/>";
			$U=($L-$L1)/(1+$zzsl)-$W-$K;
			//echo '('.$L.'-'.$L1.')/(1+'.$zzsl.')-'.$W.'-'.$K;
			$U=Round($U);
			$U=$U-$Otherfee;//汉钢其他费用31
			//echo "吨材利润:".$U."<br/>";//exit;

			if($params['callback']!=2)
			{
				$sql = "select id from sg_DongTaiChengBenIndex where type='6' and ndate='".$date."' order by id desc" ;
				$query = $this->_dao->query($sql);
				if($query!=null)
				{
					$sql="update sg_DongTaiChengBenIndex set chenben='".$X."' where ndate='".$date."' and type=6";
					$this->_dao->execute($sql);
					$sql="update sg_DongTaiChengBenIndex set chenben='".$Y."' where ndate='".$date."' and type=7";
					$this->_dao->execute($sql);
					$sql="update sg_DongTaiChengBenIndex set chenben='".$Z."' where ndate='".$date."' and type=8";
					$this->_dao->execute($sql);
					$sql="update sg_DongTaiChengBenIndex set chenben='".$W."' where ndate='".$date."' and type=9";
					$this->_dao->execute($sql);
					$sql="update sg_DongTaiChengBenIndex set chenben='".$U."' where ndate='".$date."' and type=10"; 
					$this->_dao->execute($sql);
				}
				else
				{
					$sql="insert into `sg_DongTaiChengBenIndex`(type,chenben,ndate) values('6','".$X."','".$date."')";
					//echo $sql."<br/>";
					$this->_dao->execute($sql);
					$sql="insert into `sg_DongTaiChengBenIndex`(type,chenben,ndate) values('7','".$Y."','".$date."')";
					//echo $sql."<br/>";
					$this->_dao->execute($sql);
					$sql="insert into `sg_DongTaiChengBenIndex`(type,chenben,ndate) values('8','".$Z."','".$date."')";
					//echo $sql."<br/>";
					$this->_dao->execute($sql);
					$sql="insert into `sg_DongTaiChengBenIndex`(type,chenben,ndate) values('9','".$W."','".$date."')";
					//echo $sql."<br/>";
					$this->_dao->execute($sql);
					$sql="insert into `sg_DongTaiChengBenIndex`(type,chenben,ndate) values('10','".$U."','".$date."')";
					//echo $sql."<br/>";
					$this->_dao->execute($sql);
				}
		   }
		   
		   $shuju['SJKCB']=$X;
		   $shuju['STCB']=$Y;
		   $shuju['GPCB']=$Z;
		   $shuju['GCCB']=$W;
		   $shuju['DCLR']=$U;
		  if($params['callback'])
		  {
           return $shuju;
		  }
			
		}

		public function jsdtcbweb($params)
		{
			$get_sg_ChengBenParameter=$params;
			//$date=$params['date'];
			
	        $callback=1;
			if($params['isSave'] == "1")
			{	
				$get_sg_ChengBenParameter['callback']=1;
				if($params['type']==1)//龙钢
				{
                  $shuju=$this->jsts_dtcb_lg($get_sg_ChengBenParameter);
				}
				else//汉钢
				{
					$shuju=$this->jsts_dtcb_hg($get_sg_ChengBenParameter);
				}
				return $shuju;
			}
			else
			{
				$get_sg_ChengBenParameter['callback']=2;
				if($params['type']==1)//龙钢
				{
					$shuju=$this->jsts_dtcb_lg($get_sg_ChengBenParameter);
				}
				else//汉钢
				{
					$shuju=$this->jsts_dtcb_hg($get_sg_ChengBenParameter);
				}
				echo json_encode($shuju);
				exit;
			}
		}

		//B=（价格id：D28630）/0.94/（1+M/100）+A1/(1+N/100)
		function get_LG_B($price,$zzsl,$N,$A1)//块矿价格（不含税）
		{
			return Round($price/0.94/(1+$zzsl)+$A1/(1+$N) );
		}
		//C=（价格id：578620）/(1+M/100)
		function get_LG_C($price,$zzsl)//球团矿价格
		{
			return Round($price/(1+$zzsl));
		}
		

 		
	   
		
} 
?>