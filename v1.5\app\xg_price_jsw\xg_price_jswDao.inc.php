<?php
class  xg_price_jswDao extends Dao{
	public function __construct($writer){
		 parent::__construct( $writer );
	}

	//通过guid取得用户信息
	public function get_userinfo_byguid($GUID){
		$sql = "select * from app_session_temp where  GUID ='$GUID' order by  LoginDate DESC  limit 1";
		return $this->getRow($sql);
	}

	public function getBaseIdByType($type,$uptype){
		$sql = "select id from data_table_base  where type='$type' and isdel=0 and uptype='$uptype' order by edate desc limit 1";
		return $this->getOne($sql);
	}

	public function getDataByBaseId($baseid){
		$sql="select * from data_table where baseid='$baseid' order by id asc ";
		return $this->query($sql);
	}

	public function getBaseIdByTypeAndDate($type,$uptype,$sdate,$edate){
		$sql="select id from data_table_base  where type='$type' and isdel=0 and uptype='$uptype' and sdate='$sdate'  and edate='$edate' order by edate desc limit 1";
		return $this->getOne($sql);
	}

	public function getBaseIdByTypeAndEDate($type,$uptype,$sdate,$edate){
		$sql="select id from data_table_base  where type='$type' and isdel=0 and uptype='$uptype' and sdate<'$sdate'  and edate<'$edate' order by edate desc limit 1";
		return $this->getOne($sql);
	}

	public function insert_data_table_base($type,$uptype,$sdate,$edate,$user_infos,$dta_type){
		$sql="INSERT INTO data_table_base (type,uptype,sdate,edate,dta_type,createuser,createusername,createtime) VALUES ($type,'$uptype','$sdate','$edate','$dta_type','".$user_infos['Uid']."','".$user_infos['TrueName']."',NOW())";
		$this->execute($sql);
		return $this->insert_id();
	}
	
	public function del_data_table_base($type,$uptype,$sdate,$edate,$user_infos){
		$sql="UPDATE data_table_base SET isdel=1,deladminid='".$user_infos['Uid']."',deltime=NOW() where type='$type' and isdel=0 and uptype='$uptype' and sdate='$sdate'  and edate='$edate' ";
		return $this->execute($sql);
	}

	public function del_sd_steel_data_table_base($bigtype,$uptype,$date,$user_infos){
		$sql="UPDATE sd_steel_data_table_base SET isdel=1,deladminid='".$user_infos['Uid']."',deltime=NOW() where type='$uptype' and bigtype='$bigtype' and isdel=0 and date='$date' ";
		return $this->execute($sql);
	}
	public function insert_sd_steel_data_table_base($bigtype,$uptype,$date,$user_infos){
		$sql="INSERT INTO sd_steel_data_table_base (bigtype,type,date,createuser,createusername,createtime) VALUES ('$bigtype','$uptype','$date','".$user_infos['Uid']."','".$user_infos['TrueName']."',NOW())";
		$this->execute($sql);
		return $this->insert_id();
	}

	//获取今日市场价格
	public function get_marketconditions_price($topictures,$mastertopids,$mconmanagedate){
		$tprice=array();
		if($topictures!=""){
			$sql = "select price,topicture,mconmanagedate from marketconditions where topicture in ('".$topictures."')  AND mconmanagedate >'" . $mconmanagedate . " 00:00:00' AND mconmanagedate <'" . $mconmanagedate . "  23:59:59'  order by topicture asc";
			$result6 = $this->query( $sql );
			foreach ( $result6 as $key => $value ){
				if (strstr ( $value ['price'], "-" )){
					$avgprice = explode ( "-", $value ['price'] );
					$value ['price'] = round ( ($avgprice ['0'] + $avgprice ['1']) / 2, 2 );
				}
				$tprice[$value['topicture']] = $value ['price'];
			}
		}
		if($mastertopids!=""){
			$sql = "select price,mastertopid,mconmanagedate from marketconditions where mastertopid in ('".$mastertopids. "')  AND mconmanagedate >'" . $mconmanagedate . " 00:00:00' AND mconmanagedate <'" . $mconmanagedate . "  23:59:59'  order by mastertopid asc ";
			$result7 = $this->query( $sql );
			foreach ( $result7 as $key => $value ){
				if (strstr ( $value ['price'], "-" )){
					$avgprice = explode ( "-", $value ['price'] );
					$value ['price'] = round ( ($avgprice ['0'] + $avgprice ['1']) / 2, 2 );
				}
				$tprice[$value['mastertopid']] = $value ['price'];
			}
		}
		return $tprice;
	}
	
	
	
}