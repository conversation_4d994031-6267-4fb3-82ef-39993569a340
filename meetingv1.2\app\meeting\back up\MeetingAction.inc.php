<?php
include_once("ZpayDao.inc.php");
//$GLOBALS['RuType'] = array("1" => "第一天参会", "2" => "第二天参会", "3" => "中午就餐", "4" => "晚宴");

$GLOBALS['RuType'] = "";
$GLOBALS['HOTELDESC'] = "";
//会务id 签到场景 交流场景
$GLOBALS['QDRuType'] =array(
  '356'=>array('319','325'),
  '381'=>array('332'),
  '407'=>array('357','368')
);
//限制客户端账号仅显示旅游
$GLOBALS['LYUSERID'] =array(
    '165429',//葛毛毛
);

//会务系统客户端顶部背景
$GLOBALS['MEETINGCLIENTIMG'] =array(
    "default"=>"https://dc.steelhome.com/meetingv1.2/images/meetingheadimg/top20241101.jpg",
    "381"=>"https://dc.steelhome.com/meetingv1.2/images/meetingheadimg/top20240419.jpg",
    "407"=>"https://dc.steelhome.com/meetingv1.2/images/meetingheadimg/top20241101.jpg"
);

/*
$GLOBALS['RuType'] = array(
"1" => "第一天上午(钢材)",
"2" => "第一天下午(钢材)",
"3" => "第一天上午(原料)",
"4" => "第一天下午(原料)",

"11" => "第二天上午(钢材)",
"12" => "第二天上午(原料)",

"5"=>"中国大宗物资大宗周年庆会议",

"9"=>"19日晚餐",
"6"=>"20日午餐(1楼)",
"7"=>"20日午餐(5楼)",


"8"=>"20日晚宴",

"19"=>"21日午餐"


);
*/
$GLOBALS['AWARDTYPE'] = array("1" => "一等奖", "2" => "二等奖", "3" => "三等奖", "4" => "特等奖");

$GLOBALS['GuestType'] = array(
    "1" => "演讲嘉宾",
    "2" => "特邀嘉宾",
    "3" => "会议嘉宾",
    "4" => "其他嘉宾(钢厂等)",
);

$GLOBALS['DEPTLIST'] = array(
        //华 东 区：王芳 021-50587274 15800777969
        "2,3,2"=>'165431',
        //北 方 区：范晓宇 021-50582307 15800777961
        "2,3,11"=>'108216',
        //中 西 区：王晓娜 021-50582936 15800777976
        "2,3,4"=>'135522',
        //特 钢 部：杜郑 0555-2238809 15800777971
        "2,3,6"=>'108217',
        //合金炉料部
        "2,7,3"=>'438434',
        //炉 料 部：陈艳 021-50587270 15800777967
        "2,7,0"=>'24',
        //煤 焦 部：蒋京熹 021-50582773 15800777970
        "2,7,2"=>'108223',
        
        //研究中心：王建伏 021-50585279 15800777952,
        "9,0,0"=>'10066',
        //国际部季豪
        "2,10,0"=>'430040',
        //技术部划给钱露露
        //"5,0,0"=>'473366'
        "3,0,0"=>'29220',

);
$GLOBALS['MRTSUSERID']='473366';//钱露露
/*
$GLOBALS['HOTELDESC'] = array("1" => "明城大酒店",
    "2" => "东方滨江大酒店",
    "3" => "长航美林阁",
    );
*/

function base64_encode_new($content)
{
    $content = iconv("UTF-8", "GBK//IGNORE", $content);
    return base64_encode($content);
}
function base64_decode_new($content)
{
    $content=base64_decode($content);
    //$content = iconv("GBK", "UTF-8//IGNORE", $content);
    $content = iconv("GBK", "UTF-8//IGNORE", $content);
    return $content;
}

class MeetingAction extends AbstractAction
{
    public function __construct()
    {
        parent::__construct();
    }
    // public function checkSession(){
    // if( $_SESSION['dusername'] == '' || $_SESSION['duserid'] == '' ){
    // echo "<script>window.top.location.href='index.php?view=login'</script>";
    // exit;
    // }
    // }
    // 取得设备类型
    private function getSystemType()
    {
        return " ";
    }

    // 取得系统版本
    private function getSystemVersion()
    {
        return " ";
    }

    /**
     * 函数名称: getIP
     * 函数功能: 取得手机IP
     * 输入参数: none
     * 函数返回值: 成功返回string
     * 其它说明: 说明
     */
    private function getIP()
    {
        $ip = getenv('REMOTE_ADDR');
        $ip_ = getenv('HTTP_X_FORWARDED_FOR');
        if (($ip_ != "") && ($ip_ != "unknown")) {
            $ip = $ip_;
        }
        return $ip;
    }

    public function getdata($array, $params)
    {
        $data = array();
        foreach ($array as $a) {
            $data[] = $a . "='" . $params[$a] . "'";
        }
        $data = implode(",", $data);
        return $data;
    }

    public function setvars($meetid)
    {

        $hotelt = $this->_dao->AQuery("select ID,RoomTypeName from meeting_set_hotel WHERE Mtid = '$meetid' ORDER BY ordnum ASC");

        $GLOBALS['HOTELT'] = $hotelt;

        $rctype = $this->_dao->Aquery("select ID,RcName from meeting_set_rc where Mtid='$meetid' and rtype=1 order by Ssort ");
        $GLOBALS['RuType'] = $rctype;

        $rztype = $this->_dao->Aquery("select ID,RcName from meeting_set_rc where Mtid='$meetid' and rtype=2  group by RcName ");
        $GLOBALS['HOTELDESC'] = $rztype;


    }

    public function index($params)
    {
        $Mtid=$params['mtid']==""?356:$params['mtid'];
        $contactmanSql="(select count(*) as value from meeting_member_contactman where Status = 1 and Mtid='$Mtid' ) union all  (select count(*) as value from meeting_member_contactman where Status = 1 and Mtid='$Mtid' and IsCheckIned=1 )";
        
        $numData = $this->_dao->query($contactmanSql);


        $this->assign("numData",$numData);
        $memberSql="SELECT  count(distinct mm.memberstate) FROM  meeting_member AS mm left join  meeting_member_contactman mmc on mm.Mtid = mmc.Mtid AND mm.MidEn =0 AND mmc.Mid = mm.Mid where  mm.Mtid=mmc.Mtid and   mm.Mtid='$Mtid'  and mmc.Mtid='$Mtid'  and mmc.Status=1 and mm.Status=1 and mm.memberstate!=''  order by mm.memberstate asc";
        $provinceData = $this->_dao->getOne($memberSql);
        $this->assign("provinceData",$provinceData);
        $this->assign("mtid",$Mtid);

        // $memberSql="SELECT mm.memberstate,count(*) FROM  meeting_member AS mm left join  meeting_member_contactman mmc on mm.Mtid = mmc.Mtid AND ((mm.MidEn =0 AND mmc.Mid = mm.Mid) OR (mm.MidEn >0 AND mm.MidEn = mmc.Mid))  where  mm.Mtid=mmc.Mtid and   mm.Mtid='$Mtid'  and mmc.Mtid='$Mtid'  and mmc.Status=1 and mm.Status=1 and mm.memberstate!='' group by mm.memberstate order by mm.memberstate asc";
        // $provinceData = $this->_dao->query($memberSql);
        // echo '<pre>';
        // print_r($provinceData );exit;

    }

    public function map($params)
	{
		
		$pzarr=$params['ProductType']==2?$GLOBALS['YL']:$GLOBALS['GC'];
		//$this->pri_arrayRecursive($pzarr, 'gbk2utf8', true);
		$this->assign("pzarr",$pzarr);
		$classinfo = $params['skin']=='blue'?'theme-blue':'';
		$this->assign("classinfo",$classinfo);
	}

    public function meet_Login($params)
    {
        $username = $params['UserName'];
        $password = $params['PassWord'];

        $logintype = $params['LoginType'];
        $logindate = $params['LoginDate'];
        $signcs = $params['SignCS'];
        $systemtype = $params['SystemType'];
		$mtid = $params['mtid']?$params['mtid']:0;
        // 钢之家指定的管理员
        $MEET_ID = implode(",", $GLOBALS['MEET_ID']);
        if ($password != "" && $username != "") {
            // $user = $user = $this->_dao->query("SELECT truename as TrueName,username as UserName,mid as Mid,id as Uid,mobil as MobileNumber FROM adminuser WHERE username='$username' and passwordmd5='" . md5($password) . "' and  ( id in(" . $MEET_ID . ") or power = 'A') " , 0);
            $user = $user = $this->_dao->query("SELECT truename as TrueName,username as UserName,mid as Mid,id as Uid,mobil as MobileNumber FROM adminuser WHERE username='$username' and passwordmd5='" . md5($password) . "' and  mid = 1 ", 0);
        }

        $arr = array('Success' => '0',
            'ErrorType' => '0',
            'ErrorPhoneNumber' => '50581010',
            'Message' => '登陆失败',
        );
        if ($user) {
            $GUID = $this->_dao->getGUID();
            for ($i = 0; $i < count($user); $i++) {
                $this->_dao->execute("REPLACE INTO meeting_app_session SET GUID='$GUID', SignCS='$signcs', Mid=1, Uid='" . $user[$i]['Uid'] . "', ComName='钢之家', UserName='$username'
                                        , TrueName='" . $user[$i]['TrueName'] . "', MobileNumber='" . $user[$i]['MobileNumber'] . "', LoginDate='$logindate', LoginType='$logintype', SystemType='$systemtype'
                                        ,  LastDate='$logindate', LastAction='Login'");
            }

            foreach ($user as &$da) {
                $da['TrueName'] = base64_encode_new($da['TrueName']);
                $da['ComName'] = base64_encode_new('钢之家');
                $da['UserName'] = base64_encode_new($da['UserName']);
            }

            $arr['Success'] = '1';
            $arr['GUID'] = $GUID;
            $arr['InterFaceUrl'] = '';
			$arr['QDRuType']=isset($GLOBALS['QDRuType'][$mtid])?$GLOBALS['QDRuType'][$mtid][0]:0;
			$arr['LYRuType']=isset($GLOBALS['QDRuType'][$mtid])&&isset($GLOBALS['QDRuType'][$mtid][1])?$GLOBALS['QDRuType'][$mtid][1]:0;
            $arr['ALLQX']='1';
            if(!empty($arr['LYRuType']))
            {
                if(in_array($user[0]['Uid'],$GLOBALS['LYUSERID']))
                {
                    $arr['ALLQX']='0';
                }
            }
            $arr['Message'] = base64_encode_new('登陆成功');
            $arr['Result'] = $user;
        }
        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    public function doRuchangLogin($params)
    {
        $username = $params['UserName'];
        $password = $params['PassWord'];

        $logintype = $params['LoginType'];
        $logindate = $params['LoginDate'];
        $signcs = $params['SignCS'];
        $systemtype = $params['SystemType'];
        // 钢之家指定的管理员
        $MEET_ID = implode(",", $GLOBALS['MEET_ID']);
        if ($password == "doRuchangLogin" && $username != "") {
            // $user = $user = $this->_dao->query("SELECT truename as TrueName,username as UserName,mid as Mid,id as Uid,mobil as MobileNumber FROM adminuser WHERE username='$username' and passwordmd5='" . md5($password) . "' and  ( id in(" . $MEET_ID . ") or power = 'A') " , 0);
            $user = $user = $this->_dao->query("SELECT truename as TrueName,username as UserName,mid as Mid,id as Uid,mobil as MobileNumber FROM adminuser WHERE username='$username' and  mid = 1 ", 0);
        }

        $arr = array('Success' => '0',
            'ErrorType' => '0',
            'ErrorPhoneNumber' => '50581010',
            'Message' => '登陆失败',
        );
        if ($user) {
            
            $GUID= $this->_dao->getOne("SELECT GUID from meeting_app_session where UserName='$username' order by LastDate desc limit 1  ", 0);
            if($GUID==''){
                $GUID = $this->_dao->getGUID();
                for ($i = 0; $i < count($user); $i++) {
                    $this->_dao->execute("REPLACE INTO meeting_app_session SET GUID='$GUID', SignCS='$signcs', Mid=1, Uid='" . $user[$i]['Uid'] . "', ComName='钢之家', UserName='$username'
                                            , TrueName='" . $user[$i]['TrueName'] . "', MobileNumber='" . $user[$i]['MobileNumber'] . "', LoginDate='$logindate', LoginType='$logintype', SystemType='$systemtype'
                                            ,  LastDate='$logindate', LastAction='Login'");
                }
            }
            foreach ($user as &$da) {
                $da['TrueName'] = base64_encode_new($da['TrueName']);
                $da['ComName'] = base64_encode_new('钢之家');
                $da['UserName'] = base64_encode_new($da['UserName']);
            }

            $arr['Success'] = '1';
            $arr['GUID'] = $GUID;
            $arr['InterFaceUrl'] = '';
            $arr['Message'] = base64_encode_new('登陆成功');
            $arr['Result'] = $user;
        }
        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    public function meet_QRSearch2($params)
    {
        // exit;
        //$this->setvars();
        $QRCode = base64_decode($params['QRCode']);
        $GUID = $params['GUID'];
        $signcs = $params['SignCS'];
        $meetid = $params['Mtid'];
        $record = $params['record'];
        $isgethotel = $params['isgethotel'];

        $meeting = array();
        $this->setvars($meetid);

        $tmes = $this->_dao->query("select TypeName,TypeValue from meeting_set where mtid='$meetid' and SetType=4 and Status=1 "); //print_r($tmes);exit;
        foreach ($tmes as $id => $ar) {

            if ($ar['TypeName'] == "否") {
                $travel[$ar['TypeValue']] = 0;
            }
            if ($ar['TypeName'] == "是") {
                $travel[$ar['TypeValue']] = 1;
            }
            if ($ar['TypeName'] == "待定") {
                $travel[$ar['TypeValue']] = 2;
            }

        }
        //,"IsMainMeeting","IsChildMeeting","IsChildMeeting2"
        $arrmem = array("TrueName", "PersonSign", "Sex", "Post", "ContactPhone", "ContactMobile", "ContactFax", "ContactEmail", "Mtid", "Maid", "CheckInedDesc", "HotelRoomNumber", "IsTravel", "TravelPersons", "HotelDesc", "PictureBig", "PictureSmall", "GuestType", "TableNumber", "IsGuest", "IsCheckIned", "IsGetZl", "IsGetjnp", "IsGetMoney", "HotelInType", "IsMainMeeting", "IsChildMeeting", "IsChildMeeting2", "IsChildMeeting3", "IsChildMeeting4", "IsChildMeeting5", "IsChildMeeting6", "IsChildMeeting7", "IsChildMeeting8", "IsChildMeeting9", "IsChildMeeting10", "remark", "idcard", "tralist", "idcard1", "IsEn", "IsCanZl", "HotelType", "xuhao");

        $arr = array('Success' => '0',
            'Message' => '操作失败',
        );

        if ($signcs != '' && $GUID != '') {
            // 添加微信二维码判断
            //$data = explode("|" , $QRCode);
            $data = $this->decodeQRCode($QRCode);
            $mbid = $data[1];
            $maid = $data[2];

            $bzf = $data[3];
            $isen = $data[4];//判断IsEn字段
            if (empty($isen)) $isen = 0;

            $meetmain = "";
            $meetmain2 = "";

            $meetname = $this->_dao->getRow("select * from meeting_base where ID  = '$meetid' limit 1");

            $contactman = $this->_dao->getRow("select * from meeting_member_contactman where  Maid='$maid' and Mtid = '$meetid' and isEn='$isen' and status=1 limit 1", 0);//判断IsEn字段
            // print_r($contactman);
            if ($contactman) // 存在会员表
            {
                $mbid = $contactman['Mid'];
                $cid = $contactman['ID'];
                // 获取用户参加的会议
                $join_right = 0;
                $join_main = 0;
                if ($contactman['IsMainMeeting'] == "1" || $contactman['IsChildMeeting'] == 1 || $contactman['IsChildMeeting2'] == 1) {
                    $join_main = 1;
                }
                if ($join_main == 1 || $contactman['IsChildMeeting4'] == 1) {
                    $join_right = 1;
                }
                $meetmain .= ($contactman['IsMainMeeting'] == "1") ? $meetname['ShortName'] . "," : "";
                $meetmain .= ($contactman['IsChildMeeting'] == "1") ? $meetname['ChildShortName'] . "," : "";
                $meetmain .= ($contactman['IsChildMeeting2'] == "1") ? $meetname['ChildShortName2'] . "," : "";
                $meetmain .= ($contactman['IsChildMeeting3'] == "1") ? $meetname['ChildShortName3'] . "," : "";
                $meetmain .= ($contactman['IsChildMeeting4'] == "1") ? $meetname['ChildShortName4'] . "," : ""; // 汽车用钢
                $meetmain .= ($contactman['IsChildMeeting5'] == "1") ? $meetname['ChildShortName5'] . "," : "";
                $meetmain .= ($contactman['IsChildMeeting6'] == "1") ? $meetname['ChildShortName6'] . "," : "";
                $meetmain .= ($contactman['IsChildMeeting7'] == "1") ? $meetname['ChildShortName7'] . "," : "";
                $meetmain .= ($contactman['IsChildMeeting8'] == "1") ? $meetname['ChildShortName8'] . "," : "";
                $meetmain .= ($contactman['IsChildMeeting9'] == "1") ? $meetname['ChildShortName9'] . "," : "";
                $meetmain .= ($contactman['IsChildMeeting10'] == "1") ? $meetname['ChildShortName10'] . "," : "";
                $meetmain = substr($meetmain, 0, -1);

                $meetmain2 .= ($contactman['IsMainMeeting'] == "1" || $join_main == 1) ? $meetname['ShortName'] . "," : "";
                $meetmain2 .= ($contactman['IsChildMeeting'] == "1" || $join_main == 1) ? $meetname['ChildShortName'] . "," : "";
                $meetmain2 .= ($contactman['IsChildMeeting2'] == "1" || $join_main == 1) ? $meetname['ChildShortName2'] . "," : "";
                $meetmain2 .= ($contactman['IsChildMeeting3'] == "1" || $join_right == 1) ? $meetname['ChildShortName3'] . "," : "";
                $meetmain2 .= ($contactman['IsChildMeeting4'] == "1") ? $meetname['ChildShortName4'] . "," : ""; // 汽车用钢
                $meetmain2 .= ($contactman['IsChildMeeting5'] == "1" || $join_right == 1) ? $meetname['ChildShortName5'] . "," : "";
                $meetmain2 .= ($contactman['IsChildMeeting6'] == "1" || $join_right == 1) ? $meetname['ChildShortName6'] . "," : "";
                $meetmain2 .= ($contactman['IsChildMeeting7'] == "1") ? $meetname['ChildShortName7'] . "," : "";
                $meetmain2 .= ($contactman['IsChildMeeting8'] == "1") ? $meetname['ChildShortName8'] . "," : "";
                $meetmain2 .= ($contactman['IsChildMeeting9'] == "1") ? $meetname['ChildShortName9'] . "," : "";
                $meetmain2 .= ($contactman['IsChildMeeting10'] == "1") ? $meetname['ChildShortName10'] . "," : "";
                $meetmain2 = substr($meetmain2, 0, -1);


                // alert($meetmain);
                if ($contactman['GuestType'] != 0) {
                    $mtype = $GLOBALS['GuestType'][$contactman['GuestType']];
                } else {
                    $financet = $this->_dao->getRow("SELECT FinanceType from meeting_member where Mid='$mbid' and Mtid='$meetid' ", 0);
                    if ($financet == "16") {
                        $mtype = '媒体';
                    } else {
                        $mtype = '参会嘉宾';
                    }
                }
                if ($contactman['IsEn'] != 1) {
                    $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,Mid from meeting_member where Mid='$mbid' and Mtid='$meetid' ", 0);
                } else {
                    $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,MidEn as Mid from meeting_member where MidEn='$mbid' and Mtid='$meetid' ", 0);
                }

                foreach ($arrmem as $key => $val) {
                    $meeting[$val] = $contactman[$val];
                }

                //add
                if ($isgethotel == 1) {
                    $Meeting_set = $this->_dao->query("SELECT *  FROM `meeting_set` WHERE SetType =9 and Mtid='" . $meetid . "' order by OrdNum asc");
                    if ($Meeting_set[0]['TypeValue'] < $Meeting_set[1]['TypeValue']) {
                        $dt_start = strtotime($Meeting_set[0]['TypeValue']);
                        $dt_end = strtotime($Meeting_set[1]['TypeValue']);
                    } else {
                        $dt_end = strtotime($Meeting_set[0]['TypeValue']);
                        $dt_start = strtotime($Meeting_set[1]['TypeValue']);
                    }

                    $meeting_set_day = array();
                    $day_num = 1;
                    while ($dt_start <= $dt_end) {
                        $meeting_set_day[$day_num]['zhusutime'] = date('d日', $dt_start);
                        $day_num++;


                        $dt_start = strtotime('+1 day', $dt_start);
                    }

                    $bookdetail = $this->_dao->query("SELECT *  FROM `Restrant_BookDetail` WHERE (userid1  ='" . $contactman["ID"] . "' or  userid2 ='" . $contactman["ID"] . "')                                                
                		                            and meetingid='" . $meetid . "' and status!=4  order by startDate asc");

                    foreach ($bookdetail as $key => $value) {

                        $detail_start = strtotime($value['startDate']);
                        $detail_end = strtotime($value['endDate']);
                        $detail_end = strtotime('-1 day', $detail_end);

                        while ($detail_start <= $detail_end) {

                            foreach ($meeting_set_day as $k => $v) {
                                if ($v['zhusutime'] == date('d日', $detail_start)) {
                                    $meeting_set_day[$k]['roomType'] = $value['roomType'];
                                }
                            }

                            $detail_start = strtotime('+1 day', $detail_start);
                        }
                    }

                    $m_hotel = $this->_dao->query("SELECT * FROM meeting_set_hotel WHERE Mtid='" . $meetid . "'");

                    foreach ($m_hotel as $key => $value) {
                        foreach ($meeting_set_day as $k => $v) {
                            if ($value['ID'] == $v['roomType']) {

                                $meeting_set_day[$k]['hotelname'] = $value['HotelName'];
                                $meeting_set_day[$k]['roomname'] = str_replace("（嘉宾专用）", "", $value['RoomTypeName']);
                            }
                        }
                    }
                }
                //end

            } else {
                $user = $this->_dao->getRow("select * from meeting_member_contactman where Maid='$maid' and Mtid = '45' and status=1 limit 1", 0);
                if ($user) {
                    $cid = $user['ID'];
                    $mtype = '钢之家员工';
                    if ($user['IsEn'] == '1') {
                        $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,MidEn as Mid  from meeting_member where MidEn='$mbid' and Mtid='45' ", 0);
                    } else {
                        $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,Mid from meeting_member where Mid='$mbid' and Mtid='45' ", 0);
                    }

                    foreach ($arrmem as $key => $val) {
                        $meeting[$val] = $user[$val];
                    }
                } else {
                    $message = "不存在用户信息";
                }
            }
            if ($member) $meeting = array_merge($meeting, $member);
            if ($meeting) {
                $meeting['Cid'] = $cid;
                $meeting['TrueName'] = base64_encode_new($meeting['TrueName']);
                $meeting['Sex'] = base64_encode_new($meeting['Sex']);
                $meeting['Post'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($meeting['Post'])));
                $meeting['CheckInedDesc'] = base64_encode_new($meeting['CheckInedDesc']);
                // $meeting['HotelDesc'] = base64_encode_new($meeting['HotelDesc']);
                $meeting['compfname'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($contactman['CompfnameS'])));
                $meeting['compabb'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($meeting['compabb'])));
                $meeting['comtype'] = base64_encode_new($meeting['comtype']);
                $meeting['cangjia'] = base64_encode_new($meetmain);
                $meeting['cangjia2'] = base64_encode_new($meetmain2);
                $meeting['usertype'] = base64_encode_new($meeting['usertype']);
                $meeting['remark'] = base64_encode_new($meeting['remark']);
                $meeting['tralist'] = base64_encode_new($meeting['tralist']);
                $meeting['idcard1'] = base64_encode_new($meeting['idcard1']);
                $meeting['travelstate'] = $travel[$meeting['IsTravel']];//避免与istravel混淆
                $meeting['IsEn'] = $meeting['IsEn'];

                if ($isgethotel == 1) {
                    $hotelinfo = array();
                    foreach ($meeting_set_day as $key => $val) {
                        if ($val['roomType'] != '') {
                            $hotelarr[] = array('zhusutime' => base64_encode_new($val['zhusutime'])
                            , 'hotelname' => base64_encode_new($val['hotelname']),
                                'roomname' => base64_encode_new(str_replace("（嘉宾专用）", "", $val['roomname'])));

                        }

                        if ($val['roomType'] != '') {
                            $hotelinfo[] = array('zhusutime' => $val['zhusutime']
                            , 'hotelname' => $val['hotelname'],
                                'roomname' => str_replace("（嘉宾专用）", "", $val['roomname']));

                        }

                    }

                }
                if (empty($hotelarr)) {
                    $meeting['hotelarr'] = array();
                    $meeting['HotelDesc'] = '';
                } else {
                    $meeting['hotelarr'] = $hotelarr;
                    $s_info = reset($hotelinfo);
                    $e_info = end($hotelinfo);
                    if ($s_info['zhusutime'] != $e_info['zhusutime']) {
                        $info = $s_info['zhusutime'] . '-' . $e_info['zhusutime'] . ' ' . $s_info['hotelname'] . '' . $s_info['roomname'];
                        $info = $s_info['zhusutime'] . '-' . $e_info['zhusutime'] . ' '. $s_info['roomname'];
                    } else {
                        $info = $s_info['zhusutime'] . ' ' . $s_info['hotelname'] . '' . $s_info['roomname'];
                        $info = $s_info['zhusutime'] . ' ' . $s_info['roomname'];

                    }

                    $meeting['HotelDesc'] = base64_encode_new($info);
                }


                //add by xiangbin ******** start

                $membermessage = $this->_dao->getRow("SELECT * from member where mbid='$mbid' limit 1");

                $meeting['Ticketmessage'] = base64_encode_new("公司名称:" . $membermessage['kpcom'] . "\r\n\r\n纳税识别号:" . $membermessage['kprsbnum'] . " \r\n\r\n地址电话:" . $membermessage['kpaddress'] . "    " . $membermessage['kptel'] . "\r\n\r\n开户行及账号:" . $membermessage['kpbank'] . "    " . $membermessage['kpbankaccount']);


                //add by xiangbin ******** end


                //酒店预订信息
                //酒店预订信息
                if ($contactman['HotelInType']) {
                    $hoteldesc = $GLOBALS['HOTELT'][$contactman['HotelInType']] . "房间号:" . $contactman['HotelRoomNumber'] . "(已入住)";
                } else {
                    $hoteldesc = $GLOBALS['HOTELT'][$contactman['HotelType']] == "" ? "" : $GLOBALS['HOTELT'][$contactman['HotelType']] . "(未入住)";
                }
                $meeting['HotelIn'] = "1";
                if (strstr($GLOBALS['HOTELT'][$contactman['HotelType']], "明城")) {
                    $meeting['HotelIn'] = "1";
                }
                if (strstr($GLOBALS['HOTELT'][$contactman['HotelType']], "东方")) {
                    $meeting['HotelIn'] = "2";
                }
                if (strstr($GLOBALS['HOTELT'][$contactman['HotelType']], "长航")) {
                    $meeting['HotelIn'] = "3";
                }
                if (strstr($GLOBALS['HOTELT'][$contactman['HotelType']], "紫金山")) {
                    $meeting['HotelIn'] = "4";
                }

                //$meeting['HotelDesc'] = base64_encode_new($hoteldesc);

                if ($meeting['PictureBig'] == "") {
                    $urow = $this->_dao->getRow("select PictureBig,PictureSmall from member_contact_user where user_id ='$maid' ", 0);

                    $meeting['PictureBig'] = $urow['PictureBig'];
                    $meeting['PictureSmall'] = $urow['PictureSmall'];
                }
                //$meeting['PictureBig'] = STEELHOME_SITE . $meeting['PictureBig'];
                //$meeting['PictureSmall'] = STEELHOME_SITE . $meeting['PictureSmall'];
                $meeting['PictureBig'] = $meeting['PictureBig'] == '' ? STEELHOME_SITE : ((strstr($meeting['PictureBig'], "http://")||strstr($meeting['PictureBig'], "https://")) ? $meeting['PictureBig'] : (strstr($meeting['PictureBig'], "//") ? 'http:' . $meeting['PictureBig'] : STEELHOME_SITE . $meeting['PictureBig']));
                $meeting['PictureSmall'] = $meeting['PictureSmall'] == '' ? STEELHOME_SITE : ((strstr($meeting['PictureSmall'], "http://")||strstr($meeting['PictureSmall'], "https://")) ? $meeting['PictureSmall'] : (strstr($meeting['PictureSmall'], "//") ? 'http:' . $meeting['PictureSmall'] : STEELHOME_SITE . $meeting['PictureSmall']));
                $meeting['PictureBig'] = str_replace("http:////", "http://", $meeting['PictureBig']);
                $meeting['PictureSmall'] = str_replace("http:////", "http://", $meeting['PictureSmall']);
                $meeting['PictureBig'] = str_replace("http://", "https://", $meeting['PictureBig']);
                $meeting['PictureSmall'] = str_replace("http://", "https://", $meeting['PictureSmall']);
                $meeting['PictureBig'] = str_replace("http:https", "https", $meeting['PictureBig']);
                $meeting['PictureSmall'] = str_replace("http:https", "https", $meeting['PictureSmall']);
                //$meeting['IsGetMoney'] =  $meeting['IsGetMoney'] == "0"?"0":"1";

                $meeting['usertype'] = base64_encode_new($mtype);
                // print_r($meeting);
                $arr['Success'] = '1';

                //add by zhangcun for 报到历史记录 start 2017/3/28
                if ($record) {
                    $loguser = $this->getUser($GUID, $signcs);
                    $num = $this->_dao->getone("select 1 from meeting_signinlist where mtid='" . $meetid . "' and uid='" . $loguser['Uid'] . "' and mid='" . $mbid . "' and maid='" . $maid . "'");
                    if ($maid) {
                        if ($num) {
                            $this->_dao->execute("update meeting_signinlist set code='" . $params['QRCode'] . "', time='" . date("Y-m-d H:i:s", time()) . "' where mtid='" . $meetid . "' and uid='" . $loguser['Uid'] . "' and mid='" . $mbid . "' and maid='" . $maid . "'");
                        } else {
                            $this->_dao->execute("insert into meeting_signinlist set mtid='" . $meetid . "',uid='" . $loguser['Uid'] . "', mid='" . $mbid . "', maid='" . $maid . "', code='" . $params['QRCode'] . "', time='" . date("Y-m-d H:i:s", time()) . "'");
                        }
                    }
                }
                //add by zhangcun for 报到历史记录 end 2017/3/28
                // if(isset($GLOBALS['QDRuType'][$meetid]))
                // {
                //     $sql = "update meeting_member_ruchang set RuStatus='1' where RuType='" . $GLOBALS['QDRuType'][$meetid][0] . "' and Mtid ='" . $meetid . "' and Cid='" . $cid . "' ";
                //     $this->_dao->execute($sql);
                // }
                if ($meeting['IsGuest'] == 0) {
                    $arr['Message'] = base64_encode_new('参会嘉宾信息');
                } elseif ($meeting['IsGuest'] == 1) {
                    $arr['Message'] = base64_encode_new('参会重要嘉宾信息');
                }
                $arr['Result'] = $meeting;
            }
        }

        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    // 二维码扫描查询
    public function meet_QRSearch($params)
    {
        $GUID = $params['GUID'];
        $signcs = $params['SignCS'];
        $QRCode = base64_decode($params['QRCode']);

        $meetid = $params['mtid'];

        $arr = array('Success' => '0',
            'Message' => 'null',
        );

        if ($signcs != '' && $GUID != '') {
            // $QRCode=substr($QRCode,0,-1);
            // 添加微信二维码判断
            //$data = explode("|" , $QRCode);
            $data = $this->decodeQRCode($QRCode);
            $guid = $data[0];
            $mbid = $data[1];
            $maid = $data[2];
            $bzf = $data[3];
            $isen = $data[4];//判断IsEn字段
            if (empty($isen)) $isen = 0;

            $user = $this->_dao->getRow("SELECT TrueName,PersonSign,Sex,Post,ContactPhone,ContactMobile,ContactFax,ContactEmail,Mtid,Maid,CheckInedDesc,HotelRoomNumber,IsTravel,TravelPersons,HotelDesc,PictureBig,PictureSmall,GuestType,TableNumber,IsGuest,IsCheckIned,IsGetZl,IsEn,IsCanZl,HotelType from meeting_member_contactman where   IsEn='$isen' and Maid='$maid' and Mtid = '$meetid' and status=1 limit 1", 0);//添加判断IsEn

            $cangjia = $this->_dao->getRow("SELECT MeetingName,ChildMeetingName,ChildMeetingName2,ChildMeetingName3,ChildMeetingName4,ChildMeetingName5,ChildMeetingName6,ChildMeetingName7,ChildMeetingName8,ChildMeetingName9,ChildMeetingName10  from meeting_base  where ID='" . $user['Mtid'] . "' limit 1", 0);

            $user_1 = $this->_dao->getRow("SELECT IsMainMeeting,IsChildMeeting,IsChildMeeting2,IsChildMeeting3,IsChildMeeting4,IsChildMeeting5,IsChildMeeting6,IsChildMeeting7,IsChildMeeting8,IsChildMeeting9,IsChildMeeting10 from meeting_member_contactman where  IsEn='$isen' and Maid='$maid' and Mtid = '$meetid' and status=1 limit 1 ", 0); //添加判断IsEn
            // 已报到人数
            $num = $this->_dao->getOne("SELECT count(*) as c FROM meeting_member_contactman WHERE  IsEn='" . $isen . "' and Mtid='" . $meetid . "' AND IsCheckIned=1 and status=1");//添加判断IsEn

            $cangjia_1 = array();
            // 本次参加的会议
            if ($user_1['IsMainMeeting'] == 1) {
                $cangjia_1['MeetingName'] = $cangjia['MeetingName'];
            }
            if ($user_1['IsChildMeeting'] == 1) {
                $cangjia_1['ChildMeetingName'] = $cangjia['ChildMeetingName'];
            }
            if ($user_1['IsChildMeeting2'] == 1) {
                $cangjia_1['ChildMeetingName2'] = $cangjia['ChildMeetingName2'];
            }
            if ($user_1['IsChildMeeting3'] == 1) {
                $cangjia_1['ChildMeetingName3'] = $cangjia['ChildMeetingName3'];
            }
            if ($user_1['IsChildMeeting4'] == 1) {
                $cangjia_1['ChildMeetingName4'] = $cangjia['ChildMeetingName4'];
            }
            if ($user_1['IsChildMeeting5'] == 1) {
                $cangjia_1['ChildMeetingName5'] = $cangjia['ChildMeetingName5'];
            }

            if ($user_1['IsChildMeeting6'] == 1) {
                $cangjia_1['ChildMeetingName6'] = $cangjia['ChildMeetingName6'];
            }

            if ($user_1['IsChildMeeting7'] == 1) {
                $cangjia_1['ChildMeetingName7'] = $cangjia['ChildMeetingName7'];
            }

            if ($user_1['IsChildMeeting8'] == 1) {
                $cangjia_1['ChildMeetingName8'] = $cangjia['ChildMeetingName8'];
            }

            if ($user_1['IsChildMeeting9'] == 1) {
                $cangjia_1['ChildMeetingName9'] = $cangjia['ChildMeetingName9'];
            }

            if ($user_1['IsChildMeeting10'] == 1) {
                $cangjia_1['ChildMeetingName10'] = $cangjia['ChildMeetingName10'];
            }

            $str = implode(",", $cangjia_1);
            $user['cangjia'] = $str;

            if ($meetid == '45') {
                $user['usertype'] = '钢之家员工';
            } elseif ($user['GuestType'] != 0) {
                $user['usertype'] = $GLOBALS['GuestType'][$user['GuestType']];
            } else {
                $user['usertype'] = '参会嘉宾';
            }

            $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,Mid from meeting_member where Mid='$mbid' and Mtid='$meetid' ", 0);
            $meeting = array_merge($user, $member);
            if ($meeting) {
                $meeting['TrueName'] = base64_encode_new($meeting['TrueName']);
                $meeting['Sex'] = base64_encode_new($meeting['Sex']);
                $meeting['Post'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($meeting['Post'])));
                $meeting['CheckInedDesc'] = base64_encode_new($meeting['CheckInedDesc']);
                $meeting['HotelType'] = $meeting['HotelType'];
                $meeting['HotelDesc'] = base64_encode_new($meeting['HotelDesc']);
                $meeting['compfname'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($meeting['compfname'])));
                $meeting['compabb'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($meeting['compabb'])));
                $meeting['comtype'] = base64_encode_new($meeting['comtype']);
                $meeting['cangjia'] = base64_encode_new($meeting['cangjia']);
                $meeting['usertype'] = base64_encode_new($meeting['usertype']);

                //$meeting['PictureBig'] = STEELHOME_SITE . $meeting['PictureBig'];
                //$meeting['PictureSmall'] = STEELHOME_SITE . $meeting['PictureSmall'];
                $meeting['PictureBig'] = $meeting['PictureBig'] == '' ? STEELHOME_SITE : ((strstr($meeting['PictureBig'], "http://")||strstr($meeting['PictureBig'], "https://")) ? $meeting['PictureBig'] : (strstr($meeting['PictureBig'], "//") ? 'http:' . $meeting['PictureBig'] : STEELHOME_SITE . $meeting['PictureBig']));
                $meeting['PictureSmall'] = $meeting['PictureSmall'] == '' ? STEELHOME_SITE : ((strstr($meeting['PictureBig'], "http://")||strstr($meeting['PictureSmall'], "https://")) ? $meeting['PictureSmall'] : (strstr($meeting['PictureSmall'], "//") ? 'http:' . $meeting['PictureSmall'] : STEELHOME_SITE . $meeting['PictureSmall']));
                $meeting['PictureBig'] = str_replace("http:////", "http://", $meeting['PictureBig']);
                $meeting['PictureSmall'] = str_replace("http:////", "http://", $meeting['PictureSmall']);
                $meeting['PictureBig'] = str_replace("http://", "https://", $meeting['PictureBig']);
                $meeting['PictureSmall'] = str_replace("http://", "https://", $meeting['PictureSmall']);
                $meeting['PictureBig'] = str_replace("http:https", "https", $meeting['PictureBig']);
                $meeting['PictureSmall'] = str_replace("http:https", "https", $meeting['PictureSmall']);


                $meeting['TotalNum'] = $num;
                $meeting['IsEn'] = $meeting['IsEn'];
                $meeting['IsCanZl'] = $meeting['IsCanZl'];
                // print_r($meeting);
                $arr['Success'] = '1';
                if ($meeting['IsGuest'] == 0) {
                    $arr['Message'] = base64_encode_new('参会嘉宾信息');
                } elseif ($meeting['IsGuest'] == 1) {
                    $arr['Message'] = base64_encode_new('参会重要嘉宾信息');
                }
                $arr['Result'] = $meeting;
            } else {
                $arr = array('Success' => '0',
                    'Message' => base64_encode_new("用户不存在"),
                );
            }
        }

        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    //微信登录
    public function bind_weixin($params)
    {
        //$steelhomesite="http://www.in.steelhome.cn/";   //测试版
        $steelhomesite = STEELHOME_SITE;    //正式版
        $nickname = base64_decode($params['nickname']);
        $mtid = $params['mtid'];
        $token = base64_decode($params['token']);
        $guid = $params['GUID'];
        $ip = base64_decode($params['ip']);

        $openid = base64_decode($params['openid']);
        $createtime = date("Y-m-d H:i:s", time());
        if (!$mtid) {
            $arr = array(
                'Success' => base64_encode('0'),
                'Message' => base64_encode("未获取到当前会议信息"),
                'Status' => base64_encode('-1'),
                'ErrorCode' => base64_encode('0001'),        //未获取到mtid
            );
            $mtid = 0;
            $log = "登录 登录失败：昵称为" . $nickname . "，ip= " . $ip . " 的微信用户登录失败：未获取到当前会议编号。";
        } elseif ($openid != "") {
            $sql = "select count(ID) as count from `weixin_userinfo` where weixin_openid='" . $openid . "' and mtid=" . $mtid;
            $mes = $this->_dao->query($sql);
            $mes = $mes[0];
            //print"<pre>";print_r($mes);

            if ($openid == "-1") {
                $arr = array(
                    'Success' => base64_encode('0'),
                    'Message' => base64_encode("登录失效，请重新输入会议签到"),
                    'Status' => base64_encode('0'),
                    'ErrorCode' => base64_encode('0002'),        //未获取到openid
                );
                $log = "登录 登录失效：昵称为" . $nickname . "，ip= " . $ip . " 的微信用户登录失效。";
            } elseif ($mes['count'] == 0) {
                $arr = array(
                    'Success' => base64_encode('1'),
                    'Message' => base64_encode("未绑定"),
                    'Status' => base64_encode('0'),
                );
                $log = "登录 未绑定：昵称为" . $nickname . "，openid=" . $openid . "的微信号，ip为" . $ip . "的微信用户未绑定任何嘉宾证。";
            } elseif ($mes['count'] == 1) {
                $mtname = $this->_dao->getone("select `MeetingName` from `meeting_base` where `ID`=$mtid ");
                //$mes2=$this->_dao->query("select `TrueName`,`Post`,`mtid` from `meeting_member_contactman` where `mid`='".$mes['mid']."'");

                //echo("select `personsign_code` from `weixin_userinfo` where `weixin_openid`='".$openid."'");
                $mes2 = $this->_dao->query("select `Mid`,`user_id`,`IsEn` from `weixin_userinfo` where `weixin_openid`='" . $openid . "' and mtid=" . $mtid);
                $mes2 = $mes2[0];
                $getmes = $this->_dao->query("select `Maid`,`CodeBig`,`CodeMiddle`,`CodeSmall`,`PictureBig`,`PictureSmall`,`CompfnameS`,`TrueName`,`Sex`,`Post`,`FinanceType`,`Mtid`,`PersonSign` from `meeting_member_contactman` where `Mid`=" . $mes2['Mid'] . "  and `Maid`=" . $mes2['user_id'] . " and ( Mtid=" . $mtid . " or Mtid=45 ) and IsEn='" . $mes2['IsEn'] . "' and status=1 ");
                $getmes = $getmes[0];

                if ($getmes['CodeMiddle'] == "") {
                    $getmes2 = $this->_dao->query("select `CodeBig`,`CodeMiddle`,`CodeSmall`,`PictureBig`,`PictureSmall` from `member_contact_user` where `user_id` =" . $mes2['user_id']);
                    $getmes2 = $getmes2[0];
                    //print"<pre>";print_r($getmes2);
                    $getmes['CodeMiddle'] = $steelhomesite . $getmes2['CodeMiddle'];

                    if ($getmes['CodeBig'] == "" && $getmes2['CodeBig'] != "") $getmes['CodeBig'] = $steelhomesite . $getmes2['CodeBig'];
                    if ($getmes['CodeSmall'] == "" && $getmes2['CodeSmall'] != "") $getmes['CodeSmall'] = $steelhomesite . $getmes2['CodeSmall'];
                    if ($getmes['PictureBig'] == "" && $getmes2['PictureBig'] != "") $getmes['PictureBig'] = $steelhomesite . $getmes2['PictureBig'];
                    if ($getmes['PictureSmall'] == "" && $getmes2['PictureSmall'] != "") $getmes['PictureSmall'] = $steelhomesite . $getmes2['PictureSmall'];
                } else {
                    $getmes['CodeMiddle'] = $steelhomesite . $getmes['CodeMiddle'];
                    $getmes['CodeBig'] = $steelhomesite . $getmes['CodeBig'];
                    $getmes['CodeSmall'] = $steelhomesite . $getmes['CodeSmall'];
                    $getmes['PictureBig'] = $steelhomesite . $getmes['PictureBig'];
                    $getmes['PictureSmall'] = $steelhomesite . $getmes['PictureSmall'];
                }
                $arr = array(
                    'Success' => base64_encode('1'),
                    'Message' => base64_encode("已绑定"),
                    'mtname' => base64_encode($mtname),
                    'PersonSign' => base64_encode($getmes['PersonSign']),
                    'CodeBig' => base64_encode($getmes['CodeBig']),
                    'CodeMiddle' => base64_encode($getmes['CodeMiddle']),
                    'CodeSmall' => base64_encode($getmes['CodeSmall']),
                    'PictureBig' => base64_encode($getmes['PictureBig']),
                    'PictureSmall' => base64_encode($getmes['PictureSmall']),
                    'ComName' => base64_encode($getmes['CompfnameS']),
                    'Sex' => base64_encode($getmes['Sex']),
                    'Post' => base64_encode($getmes['Post']),
                    'Type' => base64_encode($getmes['FinanceType']),
                    'Mtid' => base64_encode($mtid),
                    'Mid' => base64_encode($mes2['Mid']),
                    'Maid' => base64_encode($mes2['user_id']),
                    'TrueName' => base64_encode($getmes['TrueName']),
                    'Status' => base64_encode('1'),
                );
                $log = "登录 已绑定：昵称为" . $nickname . "，openid=" . $openid . "的微信号，ip为" . $ip . "的微信用户已绑定嘉宾证。";
            } else {
                $arr = array(
                    'Success' => base64_encode('1'),
                    'Message' => base64_encode("非法绑定"),
                    'Status' => base64_encode('1'),
                    'ErrorCode' => base64_encode('0003'),    //一个微信号绑定多个嘉宾证
                );
                $log = "登录 非法绑定：昵称为" . $nickname . "，openid=" . $openid . "的微信号，ip为" . $ip . "的微信用户绑定了" . $mes['count'] . "个嘉宾证。";
            }
        } else {
            $arr = array(
                'Success' => base64_encode('0'),
                'Message' => base64_encode("系统错误"),
                'Status' => base64_encode('0'),
                'ErrorCode' => base64_encode('0004'),        //未获取到openid
            );
            $log = "登录 系统错误：未获取到昵称为" . $nickname . "，ip= " . $ip . " 的微信用户的openid。";
        }
        //$log='http://************/meetingv1.2/meeting.php?'."action="."bind_weixin"."&nickname=".$params['nickname']."&openid=".$params['openid']."&ip=".$params['ip'];
        $this->_dao->execute("insert into `weixin_op_log` (`LogMsg`,`CreateTime`,`weixin_openid`,`personsign_code`,`ip_address`,`mtid`) values('" . $log . "','" . $createtime . "','" . $openid . "','" . $personsign_code . "','" . $ip . "'," . $mtid . ")");
        //print"<pre>"; print_r($getmes);
        //print"<pre>"; print_r($arr);
        //echo "yes";
        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    //微信签到
    public function getbind_weixin($params)
    {
        //$steelhomesite="http://www.in.steelhome.cn/";   //测试版
        $steelhomesite = STEELHOME_SITE;    //正式版
        $personsign_code = base64_decode($params['personsign_code']);
        $nickname = base64_decode($params['nickname']);
        $imgurl = base64_decode($params['imgurl']);             //微信头像网址
        $openid = base64_decode($params['openid']);
        $token = base64_decode($params['token']);
        $mtid = $params['mtid'];
        $guid = $params['GUID'];
        $createtime = base64_decode($params['createtime']);     //创建时间
        $ip = base64_decode($params['ip']);
        $data = explode("|", base64_decode($personsign_code));
        //print"<pre>";print_r($data);
        $mid = $data[1];
        $userid = $data[2];
        $usersign = $data[3];
        $isen = empty($data[4]) ? "0" : $data[4];

        $time = date("Y-m-d H:i:s", time());                      //当前时间
        //$countnum=$this->_dao->getone("select count(ID) from `weixin_userinfo` where `weixin_openid`='".$openid."' and Mtid=".$mtid);
        //print"<pre>"; print_r($countnum);
        $sql = "select count(ID)  from `weixin_userinfo` where  weixin_openid='" . $openid . "' and mtid=" . $mtid;
        $mes = $this->_dao->getone($sql);//$mes=$mes[0];print_r($mes);exit;

        if (!$mtid) {
            $arr = array(
                'Success' => base64_encode('0'),
                'Message' => base64_encode("未获取到当前会议信息，签到失败！"),
                'ErrorCode' => base64_encode('0001'),        //未获取到mtid
            );
            $mtid = 0;
            $log = "签到失败：昵称为" . $nickname . "，ip= " . $ip . " 的微信用户未获取到当前会议编号。";
        } elseif ($personsign_code == "") {
            $arr = array(
                'Success' => base64_encode('0'),
                'Message' => base64_encode("未获取到二维码编号!"),
                'ErrorCode' => base64_encode('0002'),
            );
            $log = "签到 获取失败，未获取到ip= " . $ip . " 的微信用户的二维码编号。";
        } else if ($openid == "") {
            $arr = array(
                'Success' => base64_encode('0'),
                'Message' => base64_encode("网络错误，未获取到当前微信号信息!"),
                'ErrorCode' => base64_encode('0003'),
            );
            $log = "签到 获取失败，未获取到ip= " . $ip . " 的微信用户的openid。";
        } else if ($openid == "-1") {
            $arr = array(
                'Success' => base64_encode('0'),
                'Message' => base64_encode("登录失效，请退出重新输入会议签到!"),
                'ErrorCode' => base64_encode('0004'),
            );
            $log = "签到 登录失败，ip= " . $ip . " 的微信用户登录失效。";
        } else if ($mid == "" || $userid == "") {
            $arr = array(
                'Success' => base64_encode('0'),
                'Message' => base64_encode("非法二维码编号!"),  //会员id为空，
                'ErrorCode' => base64_encode('0005'),
            );
            $log = "签到 获取失败，获取到ip= " . $ip . " 的微信用户的二维码编号非法（mid为空）。";
        } else {
            $sql = "select count(*) from `meeting_member_contactman` where `Mid`= " . $mid . " and `Maid`=" . $userid . " and ( Mtid=" . $mtid . " or Mtid=45 ) and IsEn='$isen' and status=1";//echo $sql;
            $num = $this->_dao->getone($sql);
            if ($num == 0) {
                $arr = array(
                    'Success' => base64_encode('0'),
                    'Message' => base64_encode("该二维码编号无效!"),
                    'ErrorCode' => base64_encode('0006'),
                );
                $log = "签到 获取失败，获取到ip= " . $ip . " 的微信用户的二维码编号非法。";
            } else {
                $num = $this->_dao->getone("select count(id) as n from `weixin_userinfo` where Mid=" . $mid . " and user_id=" . $userid . " and IsEn='" . $isen . "' and Mtid=" . $mtid); //$num=$num[0];
                //print_r($mes);exit;
                if ($num > 0) {
                    $arr = array(
                        'Success' => base64_encode('0'),
                        'Message' => base64_encode("该二维码编号已被绑定!"),
                        'ErrorCode' => base64_encode('0007'),
                    );
                    $log = "签到 编号:$personsign_code已被绑定，ip= " . $ip . " 。";
                } else {
                    if ($mes == '0') {
                        $this->_dao->execute("insert into `weixin_userinfo` (`Mid`,`user_id`,`IsEn`,`weixin_openid`,`Token`,`nickname`,`CreateTime`,`UpdateTime`,`imgurl`,`ip_address`,`Mtid`) values ('" . $mid . "','" . $userid . "','" . $isen . "','" . $openid . "','" . $token . "','" . $nickname . "','" . $createtime . "','" . $time . "','" . $imgurl . "','" . $ip . "'," . $mtid . ")");
                        //$log="http://************/meetingv1.2/meeting.php?action=".$params['action']."&personsign_code=".$params['personsign_code']."&nickname=".$nickname."&openid=".$openid."&token=".$token;
                        $this->_dao->execute("update `meeting_member_contactman` set `weixin_openid`='" . $openid . "' where `Mid`='" . $mid . "' and `Maid`='" . $userid . "' and ( Mtid=" . $mtid . " or Mtid=45 ) and IsEn='$isen' order by mtid limit 1");
                        $getmes = $this->_dao->query("select `CodeBig`,`CodeMiddle`,`CodeSmall`,`PictureBig`,`PictureSmall`,`CompfnameS`,`TrueName`,`Sex`,`Post`,`FinanceType`,`GuestType`,`Mtid`,`PersonSign`,`IsEn` from `meeting_member_contactman` where `Mid`='" . $mid . "' and `Maid`='" . $userid . "' and ( Mtid=" . $mtid . " or Mtid=45 ) and IsEn='$isen'");
                        $getmes = $getmes[0];

                        $hwtype = $this->_dao->getone("select FinanceType from meeting_member where " . ($isen == "1" ? "`MidEn`" : "`Mid`") . "='" . $mid . "' and ( Mtid=" . $mtid . " or Mtid=45 )");//会务费类型

                        $getmes2 = $this->_dao->query("select `CodeBig`,`CodeMiddle`,`CodeSmall`,`PictureBig`,`PictureSmall` from `member_contact_user` where `user_id` =" . $userid);
                        $getmes2 = $getmes2[0];

                        if ($getmes['CodeMiddle'] == "") {
                            //print"<pre>";print_r($getmes2);
                            if ($getmes2['CodeMiddle'] != "") $getmes['CodeMiddle'] = $steelhomesite . $getmes2['CodeMiddle'];

                        } else {
                            $getmes['CodeMiddle'] = $steelhomesite . $getmes['CodeMiddle'];
                        }

                        if ($getmes['CodeBig'] == "" && $getmes2['CodeBig'] != "") $getmes['CodeBig'] = $steelhomesite . $getmes2['CodeBig'];
                        if ($getmes['CodeSmall'] == "" && $getmes2['CodeSmall'] != "") $getmes['CodeSmall'] = $steelhomesite . $getmes2['CodeSmall'];
                        if ($getmes['PictureBig'] == "" && $getmes2['PictureBig'] != "") $getmes['PictureBig'] = $steelhomesite . $getmes2['PictureBig'];
                        if ($getmes['PictureSmall'] == "" && $getmes2['PictureSmall'] != "") $getmes['PictureSmall'] = $steelhomesite . $getmes2['PictureSmall'];

                        if ($getmes['CodeBig'] && !strstr($getmes['CodeBig'], "http")) $getmes['CodeBig'] = $steelhomesite . $getmes['CodeBig'];
                        if ($getmes['CodeSmall'] && !strstr($getmes['CodeSmall'], "http")) $getmes['CodeSmall'] = $steelhomesite . $getmes['CodeSmall'];
                        if ($getmes['PictureBig'] && !strstr($getmes['PictureBig'], "http")) $getmes['PictureBig'] = $steelhomesite . $getmes['PictureBig'];
                        if ($getmes['PictureSmall'] && !strstr($getmes['PictureSmall'], "http")) $getmes['PictureSmall'] = $steelhomesite . $getmes['PictureSmall'];
                        //print"<pre>";print_r($getmes);
                        $arr = array(
                            'Success' => base64_encode('1'),
                            'Message' => base64_encode("签到成功!"),
                            'Result' => array(                        //result表只能传这些信息，不能多传字段，iPhone手机不支持传多余字段
                                'PersonSign' => base64_encode($getmes['PersonSign']),
                                'CodeBig' => base64_encode($getmes['CodeBig']),
                                'CodeMiddle' => base64_encode($getmes['CodeMiddle']),
                                'CodeSmall' => base64_encode($getmes['CodeSmall']),
                                'PictureBig' => base64_encode($getmes['PictureBig']),
                                'PictureSmall' => base64_encode($getmes['PictureSmall']),
                                'ComName' => base64_encode($getmes['CompfnameS']),
                                'TrueName' => base64_encode($getmes['TrueName']),
                                'Sex' => base64_encode($getmes['Sex']),
                                'Post' => base64_encode($getmes['Post']),
                                'Type' => base64_encode($getmes['FinanceType']),
                                'Mtid' => base64_encode($mtid),
                                'Mid' => base64_encode($mid),
                                'Maid' => base64_encode($userid),
                                'IsEn' => base64_encode($getmes['IsEn']),
                                'HWType' => base64_encode($hwtype),
                                'GuestType' => base64_encode($getmes['GuestType']),
                            ),
                        );
                        $log = "签到成功：昵称为 " . $nickname . "，openid为 " . $openid . "，会员id为 " . $mid . "，联系人id为" . $userid . "的微信用户在本次活动中签到成功。";
                    } elseif ($mes == '1') {
                        $arr = array(
                            'Success' => base64_encode('1'),
                            'Message' => base64_encode("该微信号已绑定!"),
                            'ErrorCode' => base64_encode('0008'),
                        );
                        $log = "已经签到：昵称为 " . $nickname . "，openid为 " . $openid . "，会员id为 " . $mid . "的用户在本次活动中已经签到了。";
                    } else {
                        $arr1 = array(
                            'Success' => base64_encode('0'),
                            'Message' => base64_encode("一个嘉宾证只能绑定一个微信号!"),
                            'ErrorCode' => base64_encode('0009'),
                        );
                        $log = "签到 非法绑定：昵称为 " . $nickname . "，openid为 " . $openid . "，会员id为 " . $mid . "绑定了多个微信号。";
                    }
                }
            }
        }
        //$log='http://************/meetingv1.2/meeting.php?'."action=getbind_weixin"."&personsign_code=".$params['personsign_code']."&nickname=".$params['nickname']."&token=".$params['token']."&openid=".$params['openid']."&ip=".$params['ip']."&createtime=".$params['createtime'];
        $this->_dao->execute("insert into `weixin_op_log` (`LogMsg`,`CreateTime`,`weixin_openid`,`personsign_code`,`ip_address`,`mtid`) values('" . $log . "','" . $createtime . "','" . $openid . "','" . $personsign_code . "','" . $ip . "'," . $mtid . ")");
        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    //微信解绑
    public function unbind_weixin($params)
    {
        //$personsign_code=$params['personsign_code'];
        $nickname = base64_decode($params['nickname']);
        $openid = base64_decode($params['openid']);
        $token = base64_decode($params['token']);
        $guid = $params['GUID'];
        $mtid = $params['mtid'];
        $ip = base64_decode($params['ip']);
        /*$data=explode("|",base64_decode($personsign_code));
		$mid=$data[1];
		$userid=$data[2];
		$usersign=$data[3];*/
        $createtime = base64_decode($params['createtime']);
        $time = date("Y-m-d H:i:s", time());
        if ($createtime == "") $createtime = $time;

        if (!$mtid) {
            $arr = array(
                'Success' => base64_encode('0'),
                'Message' => base64_encode("未获取到当前会议信息,解绑失败！"),
                'ErrorCode' => base64_encode('0001'),        //未获取到mtid
            );
            $mtid = 0;
            $log = "解绑失败：昵称为" . $nickname . "，ip= " . $ip . " 的微信用户未获取到当前会议编号。";
        } elseif ($openid != "") {
            $countnum = $this->_dao->getone("select count(ID) from `weixin_userinfo` where `weixin_openid`='" . $openid . "' and `Mtid`=" . $mtid);
            if ($openid == "-1") {
                $arr = array(
                    'Success' => base64_encode('0'),
                    'Message' => base64_encode("登录失效，请退出重新输入会议签到!"),
                    'ErrorCode' => base64_encode('0002'),
                );
                $log = "解绑失败，昵称为 " . $nickname . "，ip= " . $ip . " 的微信用户登录失效。";
            } elseif ($countnum == 0) {
                $arr = array(
                    'Success' => base64_encode('0'),
                    'Message' => base64_encode("该微信号未绑定任何嘉宾证!"),
                    'ErrorCode' => base64_encode('0003'),
                );
                $log = "解绑失败：昵称为 " . $nickname . "，openid为 " . $openid . "的用户未绑定任何嘉宾证。";
            } else {
                $getmes = $this->_dao->query("select `Mid` ,`user_id` ,`IsEn` from `weixin_userinfo` where `weixin_openid`='" . $openid . "' and `Mtid`=" . $mtid);
                $getmes = $getmes[0];
                $this->_dao->execute("delete from `weixin_userinfo` where `weixin_openid`='" . $openid . "' and `Mtid`=" . $mtid);
                $this->_dao->execute("update `meeting_member_contactman` set `weixin_openid`='' where `weixin_openid`='" . $openid . "' and (`Mtid`=" . $mtid . " or mtid=45) and IsEn='" . $getmes['IsEn'] . "' order by mtid limit 1");
                $arr = array(
                    'Success' => base64_encode('1'),
                    'Message' => base64_encode("微信号解绑成功!"),
                );
                $log = "解绑成功：昵称为 " . $nickname . "，openid为 " . $openid . "，会员id为 " . $getmes['Mid'] . "，联系人id为" . $getmes['user_id'] . "的" . ($getmes['IsEn'] == "1" ? "英文" : "中文") . "用户成功执行解绑操作。";
            }
        } else {
            $arr = array(
                'Success' => base64_encode('0'),
                'Message' => base64_encode("网络错误，未获取到当前微信号!"),
                'ErrorCode' => base64_encode('0004'),
            );
            $log = "解绑失败：未获取到 昵称为 " . $nickname . "，ip= " . $ip . " 用户的微信openid。";
        }
        $this->_dao->execute("insert into `weixin_op_log` (`LogMsg`,`CreateTime`,`weixin_openid`,`personsign_code`,`ip_address`,`mtid`) values('" . $log . "','" . $createtime . "','" . $openid . "','" . $personsign_code . "','" . $ip . "'," . $mtid . ")");
        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    //根据电话号码查信息
    public function Getmes_mobile($params)
    {
        $mobile = $params['mobile'];
        $openid = base64_decode($params['openid']);
        $mtid = $params['mtid'];

        $arr = array('Success' => '0',
            'Message' => 'null',
        );
        if ($mobile && $openid && $mtid) {
            $sql = "SELECT mc.mid, mc.maid,mc.isen, mu.user_sign FROM meeting_member_contactman AS mc LEFT JOIN member_contact_user AS mu ON ( mc.mid = mu.member_id AND mc.maid = mu.user_id )  WHERE ( mc.ContactPhone =  '$mobile' OR mc.ContactMobile =  '$mobile' ) and ( mc.mtid='$mtid' or mc.mtid=45 ) and mc.status=1 limit 1";
            $mes = $this->_dao->Query($sql);
            $mes = $mes[0];//print_r($mes);exit;
            if ($mes) {
                $arr['Success'] = 1;
                $arr['Message'] = base64_encode("获取成功!");
                $arr['Result'] = base64_encode("1|" . $mes['mid'] . "|" . $mes['maid'] . "|" . $mes['user_sign'] . "|" . $mes['isen']);
            } else {
                $arr['Message'] = base64_encode("嘉宾证绑定失败!");
            }
        } else {
            $arr['Message'] = base64_encode( "获取失败: sj-" . $mobile . " id-" . $openid . " mtid-" . $mtid);
        }
        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    // 根据类型获取会议相关信息
    public function Getmes_type($params)
    {
        $type = $params['type'];
        $RuType = $params['RuType'];
        $mtid = $params['mtid'];
        $GUID = $params['GUID'];
        $signcs = $params['SignCS'];

        $arr = array('Success' => '0',
            'Message' => 'null',
        );

        $tmes = $this->_dao->query("select TypeName,TypeValue from meeting_set where mtid='$mtid' and SetType=4 and Status=1 "); //print_r($tmes);//exit;
        foreach ($tmes as $id => $ar) {
            //print_r($ar['TypeValue']);
            if ($ar['TypeName'] == "否") {
                $travel[$ar['TypeValue']] = 0;
            } elseif ($ar['TypeName'] == "是") {
                $travel[$ar['TypeValue']] = 1;
            } elseif ($ar['TypeName'] == "待定") {
                $travel[$ar['TypeValue']] = 2;
            }

        }
        //print_r($travel);exit;
        if ($signcs != "" && $GUID != "" && $mtid != "" && $type != "") {
            switch ($type) {
                case '1':
                    $search = " and mc.IsCheckIned='1'  and mc.IsGetMoney='0' ";
                    break;
                case '2':
                    $search = " and mc.IsCheckIned='1'  and mc.IsGetMoney!=0 and ((mc.IsGetZl='0' and mc.IsCanZl!='1') or (mc.IsGetjnp='0' and mc.IsCanZl='0'))";
                    break;
                case '3'://$search=" and mc.IsCheckIned='1'  and mc.IsGetMoney!=0 and  ";break;
                case '4':
                        $sql = "SELECT ruchang.Cid";
                        $sql .= " FROM `meeting_member_ruchang` ruchang WHERE ";
                        $sql .= " ruchang.Mtid='" . $mtid . "' ";
                        $sql .= " and ruchang.RuType='" . $RuType . "' ";
                        $sql .= " group by ruchang.Cid ";

                        $ruchang_Cids = $this->_dao->getOnes($sql, 0);
                        if(!empty($ruchang_Cids)){
                            $ruchang_Cids = implode("','",$ruchang_Cids);
                            $search = " and mc.IsCheckIned='0' and mc.id in ('".$ruchang_Cids."')";
                        }else{
                            $search = " and mc.IsCheckIned='0' and mc.id = 0";
                        }

                    break;
                case '0':
                    $search = "";
                    break;
                //case '5':$search="";break;
            }
            $select_arr = array("TrueName", "CompfnameS", "PersonSign", "Post", "Sex", "Mid", "Maid", "Mtid", "IsGuest", "remark", "IsCheckIned", "IsGetMoney", "IsGetZl", "IsGetjnp", "IsTravel", "IsCanZl", "IsCanjnp", "tralist", "idcard", "idcard1", "TravelPersons", "IsEn");//添加IsEn字段
            $sql = "select ";
            foreach ($select_arr as $id => $val) {
                $sql .= " mc." . $val . ",";
            }
            $sql .= " mu.user_sign from meeting_member_contactman as mc left join member_contact_user  as mu on (mc.Mid=mu.member_id and mc.Maid=mu.user_id) where 1 " . $search . " and mc.Mtid='$mtid' and mc.status=1 order by mc.PersonSign"; //echo$sql;
            $mes = $this->_dao->Query($sql);

            foreach ($mes as $id => $array) {
                $mes[$id]['TrueName'] = base64_encode_new($mes[$id]['TrueName']);
                $mes[$id]['CompfnameS'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($mes[$id]['CompfnameS'])));
                $mes[$id]['Sex'] = base64_encode_new($mes[$id]['Sex']);
                $mes[$id]['Post'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($mes[$id]['Post'])));
                $mes[$id]['remark'] = base64_encode_new($mes[$id]['remark']);
                $mes[$id]['tralist'] = base64_encode_new($mes[$id]['tralist']);
                $mes[$id]['idcard1'] = base64_encode_new($mes[$id]['idcard1']);
                $mes[$id]['IsEn'] = $mes[$id]['IsEn'];
                /*$mes[$id]['PersonSign']=$mes[$id]['PersonSign'];
				$mes[$id]['Mid']=$mes[$id]['Mid'];
				$mes[$id]['Maid']=$mes[$id]['Maid'];
				$mes[$id]['Mtid']=$mes[$id]['Mtid'];
				$mes[$id]['IsGuest']=$mes[$id]['IsGuest'];
				$mes[$id]['IsCheckIned']=$mes[$id]['IsCheckIned'];
				$mes[$id]['IsGetMoney']=$mes[$id]['IsGetMoney'];
				$mes[$id]['IsGetZl']=$mes[$id]['IsGetZl'];
				$mes[$id]['IsGetjnp']=$mes[$id]['IsGetjnp'];
				$mes[$id]['IsCanZl']=$mes[$id]['IsCanZl'];
				$mes[$id]['IsCanjnp']=$mes[$id]['IsCanjnp'];
				$mes[$id]['IsTravel']=$mes[$id]['IsTravel'];
				$mes[$id]['user_sign']=$mes[$id]['user_sign'];
				$mes[$id]['idcard']=$mes[$id]['idcard'];*/
                $mes[$id]['travelstate'] = $travel[$mes[$id]['IsTravel']];
            }

            $arr['Success'] = '1';
            $arr['Message'] = "查询成功！";
            $arr['Results'] = $mes;
        } else {
            //echo $type."# #".$mtid."# #".$GUID."# #".$signcs."#";
            $arr['Message'] = "查询失败！";
        }

        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    //姓名查询信息
    public function meet_Searchname($params)
    {
        $searchname =  $this->gbkToutf8($params['name']);
        $searchname = str_replace("'", "\'", $searchname);
        $mtid = $params['Mtid'];
        $GUID = $params['GUID'];
        $signcs = $params['SignCS'];

        //echo $mtid." ".$searchname;
        $arr = array('Success' => '0',
            'Message' => 'null',
        );

        $tmes = $this->_dao->query("select TypeName,TypeValue from meeting_set where mtid='$mtid' and SetType=4 and Status=1 "); //print_r($tmes);//exit;
        foreach ($tmes as $id => $ar) {
            //print_r($ar['TypeValue']);
            if ($ar['TypeName'] == "否") {
                $travel[$ar['TypeValue']] = 0;
            } elseif ($ar['TypeName'] == "是") {
                $travel[$ar['TypeValue']] = 1;
            } elseif ($ar['TypeName'] == "待定") {
                $travel[$ar['TypeValue']] = 2;
            }

        }
        //print_r($travel);exit;

        if ($signcs != "" && $GUID != "" && $mtid != "") {
            $select_arr = array("TrueName", "CompfnameS", "PersonSign", "Post", "Sex", "Mid", "Maid", "Mtid", "IsGuest", "remark", "IsCheckIned", "IsGetMoney", "IsGetZl", "IsGetjnp", "IsTravel", "IsCanZl", "IsCanjnp", "tralist", "idcard1", "IsEn");
            $sql = "select ";
            foreach ($select_arr as $id => $val) {
                $sql .= " mc." . $val . ",";
            }
            if ($searchname != "") {
                $sql .= " mu.user_sign from meeting_member_contactman as mc left join member_contact_user  as mu on (mc.Mid=mu.member_id and mc.Maid=mu.user_id) where (Truename like binary '%$searchname%' or CompfnameS like binary '%$searchname%' ) and (mc.Mtid='$mtid')  and mc.status=1";
            } else {
                $sql .= " mu.user_sign from meeting_member_contactman as mc left join member_contact_user  as mu on (mc.Mid=mu.member_id and mc.Maid=mu.user_id) where  (mc.Mtid='$mtid') and mc.status=1";
            }
            $sql .= " order by mc.PersonSign,Truename";

            $mes = $this->_dao->query($sql);//echo$sql; print"<pre>";
            if ($mes) {
                //print"<pre>";print_r($mes);print"</pre>";
                $arr["Success"] = 1;
                $arr["Message"] = "成功";
                foreach ($mes as $id => $array) {
                    $mes[$id]['TrueName'] = base64_encode_new($mes[$id]['TrueName']);
                    $mes[$id]['CompfnameS'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($mes[$id]['CompfnameS'])));
                    $mes[$id]['Sex'] = base64_encode_new($mes[$id]['Sex']);
                    $mes[$id]['Post'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($mes[$id]['Post'])));
                    $mes[$id]['remark'] = base64_encode_new($mes[$id]['remark']);
                    $mes[$id]['tralist'] = base64_encode_new($mes[$id]['tralist']);
                    $mes[$id]['idcard1'] = base64_encode_new($mes[$id]['idcard1']);
                    $mes[$id]['IsEn'] = $mes[$id]['IsEn'];
                    /*$mes[$id]['PersonSign']=$mes[$id]['PersonSign'];
					$mes[$id]['Mid']=$mes[$id]['Mid'];
					$mes[$id]['Maid']=$mes[$id]['Maid'];
					$mes[$id]['Mtid']=$mes[$id]['Mtid'];
					$mes[$id]['IsGuest']=$mes[$id]['IsGuest'];
					$mes[$id]['IsCheckIned']=$mes[$id]['IsCheckIned'];
					$mes[$id]['IsGetMoney']=$mes[$id]['IsGetMoney'];
					$mes[$id]['IsGetZl']=$mes[$id]['IsGetZl'];
					$mes[$id]['IsGetjnp']=$mes[$id]['IsGetjnp'];
					$mes[$id]['IsCanZl']=$mes[$id]['IsCanZl'];
					$mes[$id]['IsCanjnp']=$mes[$id]['IsCanjnp'];
					$mes[$id]['IsTravel']=$mes[$id]['IsTravel'];
					$mes[$id]['user_sign']=$mes[$id]['user_sign'];
					$mes[$id]['idcard']=$mes[$id]['idcard'];*/
                    $mes[$id]['travelstate'] = $travel[$mes[$id]['IsTravel']];
                }
                //print"<pre>";print_r($arr);print"</pre>";
                $arr["Results"] = $mes;
            } else {
                $arr["Message"] = "未找到该联系人信息！";
            }
        } else {
            $arr['Message'] = "操作失败！";
        }
        //print"<pre>";print_r($arr);print"</pre>";
        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    //更新报道信息
    public function updateremark($params)
    {
        $mtid = $params['mtid'];
        $mid = $params['mid'];
        $maid = $params['maid'];
        $isen = $params['IsEn'];
        if (empty($isen)) $isen = 0;//添加IsEn字段
        $GUID = $params['GUID'];
        $signcs = $params['SignCS'];

        $remark = str_replace(" ", "+", $params['remark']);//网址里面实际上是+号，但是提取过来变成了空格，因此要反向转换下
        $remark = base64_decode_new($remark);

        $arr = array('Success' => '0',
            'Message' => 'null',
        ); //echo $remark;
        if ($GUID != "" && $signcs != "" && $mtid != "" && $mid != "" && $maid != "") {
            $remark = str_replace("'", "\'", $remark);
            $sql = "update meeting_member_contactman set remark='" . $remark . "' where Mid=" . $mid . " and Maid=" . $maid . " and Mtid=" . $mtid . " and IsEn=" . $isen . "";//添加IsEn字段
            //echo $sql;
            //$sql="update meeting_member_contactman set remark='".$params['remark']."' where Mid=".$mid." and Maid=".$maid." and Mtid=".$mtid;
            $this->_dao->execute($sql);
            $arr = array(
                'Success' => '1',
                'Message' => '成功',
            );
        } else {
            $arr['Message'] = "操作失败！";
        }
        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    // 报道历史记录查询
    public function history_signin($params)
    {
        $QRCode = $params['QRCode'];
        $BHCode = $params['BHcode'];
        $mtid = $params['ID'];
        $type = $params['type'];
        $GUID = $params['GUID'];
        $signcs = $params['SignCS'];

        $arr = array('Success' => '0',
            'Message' => 'null',
        );
        if (!$GUID && !$signcs) {
            $arr['Message'] = "信息获取失败！";
            $json_string = $this->pri_JSON($arr);
            echo $json_string;
            return;
        }
        $loguser = $this->getUser($GUID, $signcs);
        $num = $this->_dao->getone("select 1 from meeting_signinlist where mtid='$mtid' and uid='" . $loguser['Uid'] . "' limit 1 ", 0);
        if (!$num) {
            $arr['Message'] = "无记录";
            $json_string = $this->pri_JSON($arr);
            echo $json_string;
            exit;
        }
        if ($QRCode) {
            $data = explode("|", base64_encode_new($QRCode)); //print_r($data);exit;
            $mbid = $data[1];
            $maid = $data[2];
            $bzf = $data[3];
            if (!$maid) {
                $arr['Message'] = "无此参会嘉宾信息！";
                $json_string = $this->pri_JSON($arr);
                echo $json_string;
                return;
            }

            $time = $this->_dao->getone("select time from meeting_signinlist where mtid='$mtid' and uid='" . $loguser['Uid'] . "' and maid='" . $maid . "' order by id desc limit 1 ", 0); //print_r($time);exit;

            if ($type == "pre") {
                if ($time != "") {
                    $code = $this->_dao->getone("select code from meeting_signinlist where mtid='$mtid' and uid='" . $loguser['Uid'] . "' and time<='$time' and code!='$QRCode' order by time desc limit 1 ", 0);
                    if ($code) {
                        $arr['Success'] = 1;
                        $arr['Message'] = "成功";
                        $arr['Retcode'] = $code;
                    } else {
                        $arr['Message'] = "无记录！";
                    }
                } else {
                    $code = $this->_dao->getone("select code from meeting_signinlist where mtid='$mtid' and uid='" . $loguser['Uid'] . "' and code!='$QRCode' order by time asc limit 1 ", 0);
                    if ($code) {
                        $arr['Success'] = 1;
                        $arr['Message'] = "成功";
                        $arr['Retcode'] = $code;
                    } else {
                        $arr['Message'] = "无记录！";
                    }
                }
            } elseif ($type == "next") {
                if ($time != "") {
                    $code = $this->_dao->getone("select code from meeting_signinlist where mtid='$mtid' and uid='" . $loguser['Uid'] . "' and time>='$time' and code!='$QRCode' order by time asc limit 1 ", 0);
                    if ($code) {
                        $arr['Success'] = 1;
                        $arr['Message'] = "成功";
                        $arr['Retcode'] = $code;
                    } else {
                        $arr['Message'] = "无记录！";
                    }
                } else {
                    $arr['Message'] = "当前已是最后一页";
                }

            } else {
                $arr['Message'] = "操作失败！";
            }
        } else if ($BHCode) {
            $user = $this->_dao->getRow("select Mid,Maid from meeting_member_contactman where mtid='$mtid' and PersonSign='$BHCode' and status=1 limit 1 ", 0); //print_r( $user);exit;
            if (!$user) {
                $arr['Message'] = "未找到该参会嘉宾！";
            } else {
                if ($type == "pre") {
                    if ($time != "") {
                        $code = $this->_dao->getone("select code from meeting_signinlist where mtid='$mtid' and uid='" . $loguser['Uid'] . "' and time<='$time' and code!='$BHCode' order by time desc limit 1 ", 0);
                        if ($code) {
                            $arr['Success'] = 1;
                            $arr['Message'] = "成功";
                            $arr['Retcode'] = $code;
                        } else {
                            $arr['Message'] = "无记录！";
                        }
                    } else {
                        $code = $this->_dao->getone("select code from meeting_signinlist where mtid='$mtid' and uid='" . $loguser['Uid'] . "' order by time desc limit 1 ", 0);
                        if ($code) {
                            $arr['Success'] = 1;
                            $arr['Message'] = "成功";
                            $arr['Retcode'] = $code;
                        } else {
                            $arr['Message'] = "无记录！";
                        }
                    }
                } elseif ($type == "next") {
                    if ($time != "") {
                        $code = $this->_dao->getone("select code from meeting_signinlist where mtid='$mtid' and uid='" . $loguser['Uid'] . "' and time>='$time' and code!='$BHCode' order by time asc limit 1 ", 0);
                        if ($code) {
                            $arr['Success'] = 1;
                            $arr['Message'] = "成功";
                            $arr['Retcode'] = $code;
                        } else {
                            $arr['Message'] = "无记录！";
                        }
                    } else {
                        $arr['Message'] = "当前已是最后一页！";
                    }

                } else {
                    $arr['Message'] = "操作失败！";
                }
            }
        }
        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    // 编号查询
    public function meet_BHSearch($params)
    {

        $GUID = $params['GUID'];
        $signcs = $params['SignCS'];
        $PersonSign = $params['PersonSign'];
        $mtid = $params['Mtid'];
        $record = $params['record'];
        $isen = $params['IsEn'];
        $isgethotel = $params['isgethotel'];
        if (empty($isen)) $isen = 0;//判断IsEn字段

        $this->setvars($mtid);

        $tmes = $this->_dao->query("select TypeName,TypeValue from meeting_set where mtid='$mtid' and SetType=4 and Status=1 "); //print_r($tmes);exit;
        foreach ($tmes as $id => $ar) {

            if ($ar['TypeName'] == "否") {
                $travel[$ar['TypeValue']] = 0;
            }
            if ($ar['TypeName'] == "是") {
                $travel[$ar['TypeValue']] = 1;
            }
            if ($ar['TypeName'] == "待定") {
                $travel[$ar['TypeValue']] = 2;
            }

        }

        $arr = array('Success' => '0',
            'Message' => 'null',
        );
        if ($PersonSign != '') {//,IsMainMeeting,IsChildMeeting,IsChildMeeting2
            $user = $this->_dao->getRow("SELECT CompfnameS as compfname,TrueName,PersonSign,Sex,Post,ContactPhone,ContactMobile,ContactFax,ContactEmail,Mid,Mtid,Maid,CheckInedDesc,HotelRoomNumber,IsTravel,TravelPersons,HotelDesc,PictureBig,TableNumber,PictureSmall,IsGuest,GuestType,IsCheckIned,IsGetZl,IsGetjnp,IsGetMoney, HotelInType,IsMainMeeting,IsChildMeeting,IsChildMeeting2,IsChildMeeting3,IsChildMeeting4,IsChildMeeting5,IsChildMeeting6,IsChildMeeting7,IsChildMeeting8,IsChildMeeting9,IsChildMeeting10,remark,idcard,tralist,idcard1,IsEn,CodeSmall,IsCanZl,xuhao from meeting_member_contactman where PersonSign='$PersonSign' and Mtid = '$mtid'  and status=1 limit 1 ", 0);//添加IsEn字段
            if($user)
            {
            // print_r($user);exit;
            $code = $this->_dao->getOne("SELECT CodeSmall FROM member_contact_user WHERE user_id='" . $user['Maid'] . "'", 0);
            if ($user['IsEn'] == 1) {
                $member = $this->_dao->getRow("SELECT compabb,comtype from meeting_member where MidEn='" . $user['MidEn'] . "' and Mtid='" . $user['Mtid'] . "'", 0);
            } else {
                $member = $this->_dao->getRow("SELECT compabb,comtype from meeting_member where Mid='" . $user['Mid'] . "' and Mtid='" . $user['Mtid'] . "'", 0);
            }
            //$cangjia = $this->_dao->getRow("SELECT b.MeetingName,b.ChildMeetingName,b.ChildMeetingName2,b.ChildMeetingName3,b.ChildMeetingName4,b.ChildMeetingName5,b.ChildMeetingName6,b.ChildMeetingName7,b.ChildMeetingName8,b.ChildMeetingName9,b.ChildMeetingName10  from meeting_base as b,meeting_member_contactman as a where a.Mtid=b.ID and a.TrueName='" . $user['TrueName'] . "' and a.Mtid='" . $user['Mtid'] . "' and a.Mid='" . $user['Mid'] . "' and a.Mtid!=45", 0);
            $cangjia = $this->_dao->getRow("SELECT b.ShortName,b.ChildShortName,b.ChildShortName2,b.ChildShortName3,b.ChildShortName4,b.ChildShortName5,b.ChildShortName6,b.ChildShortName7,b.ChildShortName8,b.ChildShortName9,b.ChildShortName10  from meeting_base as b,meeting_member_contactman as a where  a.Mtid=b.ID and a.TrueName='" . $user['TrueName'] . "' and a.Mtid='" . $user['Mtid'] . "' and a.Mid='" . $user['Mid'] . "' and a.status=1", 0);
            //添加判断IsEn
            $user_1 = $this->_dao->getRow("SELECT * from meeting_member_contactman where PersonSign='$PersonSign' and  Mtid ='$mtid'  and status=1 limit 1 ", 0);//添加判断IsEn

            //add
            if ($isgethotel == 1) {
                $Meeting_set = $this->_dao->query("SELECT *  FROM `meeting_set` WHERE SetType =9 and Mtid='" . $mtid . "' order by OrdNum asc");
                if ($Meeting_set[0]['TypeValue'] < $Meeting_set[1]['TypeValue']) {
                    $dt_start = strtotime($Meeting_set[0]['TypeValue']);
                    $dt_end = strtotime($Meeting_set[1]['TypeValue']);
                } else {
                    $dt_end = strtotime($Meeting_set[0]['TypeValue']);
                    $dt_start = strtotime($Meeting_set[1]['TypeValue']);
                }

                $meeting_set_day = array();
                $day_num = 1;
                while ($dt_start <= $dt_end) {
                    $meeting_set_day[$day_num]['zhusutime'] = date('d日', $dt_start);
                    $day_num++;


                    $dt_start = strtotime('+1 day', $dt_start);
                }

                $bookdetail = $this->_dao->query("SELECT *  FROM `Restrant_BookDetail` WHERE (userid1  ='" . $user_1["ID"] . "' or  userid2 ='" . $user_1["ID"] . "')
                		                            and meetingid='" . $mtid . "' and status!=4 order by startDate asc");

                foreach ($bookdetail as $key => $value) {

                    $detail_start = strtotime($value['startDate']);
                    $detail_end = strtotime($value['endDate']);
                    $detail_end = strtotime('-1 day', $detail_end);

                    while ($detail_start <= $detail_end) {

                        foreach ($meeting_set_day as $k => $v) {
                            if ($v['zhusutime'] == date('d日', $detail_start)) {
                                $meeting_set_day[$k]['roomType'] = $value['roomType'];
                            }
                        }

                        $detail_start = strtotime('+1 day', $detail_start);
                    }
                }

                $m_hotel = $this->_dao->query("SELECT * FROM meeting_set_hotel WHERE Mtid='" . $mtid . "'");

                foreach ($m_hotel as $key => $value) {
                    foreach ($meeting_set_day as $k => $v) {
                        if ($value['ID'] == $v['roomType']) {

                            $meeting_set_day[$k]['hotelname'] = $value['HotelName'];
                            $meeting_set_day[$k]['roomname'] = str_replace("（嘉宾专用）", "", $value['RoomTypeName']);
                        }
                    }
                }
            }
            //end


            $join_right = 0;
            $join_main = 0;
            if ($user_1['IsMainMeeting'] == "1" || $user_1['IsChildMeeting'] == 1 || $user_1['IsChildMeeting2'] == 1) {
                $join_main = 1;
            }
            if ($join_main == 1 || $user_1['IsChildMeeting4'] == 1) {
                $join_right = 1;
            }

            $cangjia_1 = array();
            $cangjia_2 = array();
            if ($user_1['IsMainMeeting'] == 1) {
                $cangjia_1['MeetingName'] = $cangjia['ShortName'];
            }
            if ($user_1['IsChildMeeting'] == 1) {
                $cangjia_1['ChildMeetingName'] = $cangjia['ChildShortName'];
            }
            if ($user_1['IsChildMeeting2'] == 1) {
                $cangjia_1['ChildMeetingName2'] = $cangjia['ChildShortName2'];
            }
            if ($user_1['IsChildMeeting3'] == 1) {
                $cangjia_1['ChildMeetingName3'] = $cangjia['ChildShortName3'];
                $cangjia_2['ChildMeetingName3'] = $cangjia['ChildShortName3'];
            }
            if ($user_1['IsChildMeeting4'] == 1) // 汽车用钢
            {
                $cangjia_1['ChildMeetingName4'] = $cangjia['ChildShortName4'];
                $cangjia_2['ChildMeetingName4'] = $cangjia['ChildShortName4'];
            }
            if ($user_1['IsChildMeeting5'] == 1) {
                $cangjia_1['ChildMeetingName5'] = $cangjia['ChildShortName5'];
                $cangjia_2['ChildMeetingName5'] = $cangjia['ChildShortName5'];
            }
            if ($user_1['IsChildMeeting6'] == 1) {
                $cangjia_1['ChildMeetingName6'] = $cangjia['ChildShortName6'];
                $cangjia_2['ChildMeetingName6'] = $cangjia['ChildShortName6'];
            }
            if ($user_1['IsChildMeeting7'] == 1) {
                $cangjia_1['ChildMeetingName7'] = $cangjia['ChildShortName7'];
            }
            if ($user_1['IsChildMeeting8'] == 1) {
                $cangjia_1['ChildMeetingName8'] = $cangjia['ChildShortName8'];
            }
            if ($user_1['IsChildMeeting9'] == 1) {
                $cangjia_1['ChildMeetingName9'] = $cangjia['ChildShortName9'];
            }
            if ($user_1['IsChildMeeting10'] == 1) {
                $cangjia_1['ChildMeetingName10'] = $cangjia['ChildShortName10'];
            }

            if ($join_main == 1) {
                $cangjia_2['MeetingName'] = $cangjia['ShortName'];
                $cangjia_2['ChildMeetingName'] = $cangjia['ChildShortName'];
                $cangjia_2['ChildMeetingName2'] = $cangjia['ChildShortName2'];
            }
            if ($join_right == 1) {
                $cangjia_2['ChildMeetingName3'] = $cangjia['ChildShortName3'];
                $cangjia_2['ChildMeetingName5'] = $cangjia['ChildShortName5'];
                $cangjia_2['ChildMeetingName6'] = $cangjia['ChildShortName6'];
            }

            $str = implode(",", $cangjia_1);
            $user['cangjia'] = $str;

            $str2 = implode(",", $cangjia_2);
            $user['cangjia2'] = $str2;

            if ($user['Mtid'] == '45') {
                if ($user['cangjia'] == "") $user['cangjia'] = $this->_dao->getone("select ShortName from meeting_base where id=" . $mtid);
                $user['usertype'] = '钢之家员工';
            } elseif ($user['GuestType'] != 0) {
                $user['usertype'] = $GLOBALS['GuestType'][$user['GuestType']];
            } else {
                $user['usertype'] = '参会嘉宾';
            }

            $meeting = array_merge($user, $member);
            if ($meeting) {
                $meeting['Cid'] = $user_1['ID'];
                $meeting['TrueName'] = base64_encode_new($meeting['TrueName']);
                $meeting['Sex'] = base64_encode_new($meeting['Sex']);
                $meeting['Post'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($meeting['Post'])));
                $meeting['CheckInedDesc'] = base64_encode_new($meeting['CheckInedDesc']);
                //$meeting['HotelDesc'] = base64_encode_new($meeting['HotelDesc']);
                $meeting['compfname'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($meeting['compfname'])));
                $meeting['compabb'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($meeting['compabb'])));
                $meeting['comtype'] = base64_encode_new($meeting['comtype']);
                $meeting['usertype'] = base64_encode_new($meeting['usertype']);
                $meeting['cangjia'] = base64_encode_new($meeting['cangjia']);
                $meeting['cangjia2'] = base64_encode_new($meeting['cangjia2']);
                $meeting['travelstate'] = $travel[$meeting['IsTravel']];//避免与istravel混淆
                if ($isgethotel == 1) {
                    $hotelinfo = array();
                    foreach ($meeting_set_day as $key => $val) {
                        if ($val['roomType'] != '') {
                            $hotelarr[] = array('zhusutime' => base64_encode_new($val['zhusutime'])
                            , 'hotelname' => base64_encode_new($val['hotelname']),
                                'roomname' => base64_encode_new(str_replace("（嘉宾专用）", "", $val['roomname'])));

                        }

                        if ($val['roomType'] != '') {
                            $hotelinfo[] = array('zhusutime' => $val['zhusutime']
                            , 'hotelname' => $val['hotelname'],
                                'roomname' => str_replace("（嘉宾专用）", "", $val['roomname']));

                        }

                    }

                }
                if (empty($hotelarr)) {
                    $meeting['hotelarr'] = array();
                    $meeting['HotelDesc'] = '';
                } else {
                    $meeting['hotelarr'] = $hotelarr;
                    $s_info = reset($hotelinfo);
                    $e_info = end($hotelinfo);
                    if ($s_info['zhusutime'] != $e_info['zhusutime']) {
                        $info = $s_info['zhusutime'] . '-' . $e_info['zhusutime'] . ' ' . $s_info['hotelname'] . '' . $s_info['roomname'];
                        $info = $s_info['zhusutime'] . '-' . $e_info['zhusutime'] . ' ' . $s_info['roomname'];

                    } else {
                        $info = $s_info['zhusutime'] . ' ' . $s_info['hotelname'] . '' . $s_info['roomname'];
                        $info = $s_info['zhusutime'] . ' ' . $s_info['roomname'];

                    }
                    $meeting['HotelDesc'] = base64_encode_new($info);
                }


                //酒店预订信息
                if ($user_1['HotelInType']) {
                    $hoteldesc = $GLOBALS['HOTELDESC'][$user_1['HotelInType']] . "房间号" . $user_1['HotelRoomNumber'] . "(已入住)";
                } else {
                    $hoteldesc = $GLOBALS['HOTELT'][$user_1['HotelType']] == "" ? "" : $GLOBALS['HOTELT'][$user_1['HotelType']] . "(未入住)";
                }

                $meeting['HotelIn'] = "1";
                if (strstr($GLOBALS['HOTELT'][$user_1['HotelType']], "明城")) {
                    $meeting['HotelIn'] = "1";
                }
                if (strstr($GLOBALS['HOTELT'][$user_1['HotelType']], "东方")) {
                    $meeting['HotelIn'] = "2";
                }
                if (strstr($GLOBALS['HOTELT'][$user_1['HotelType']], "长航")) {
                    $meeting['HotelIn'] = "3";
                }
                if (strstr($GLOBALS['HOTELT'][$user_1['HotelType']], "紫金山")) {
                    $meeting['HotelIn'] = "4";
                }


                //$meeting['HotelDesc'] = base64_encode_new($hoteldesc);

                //$meeting['IsGetMoney'] = $meeting['IsGetMoney'] == "0"?"0":"1";


                if ($meeting['PictureBig'] == "") {
                    $urow = $this->_dao->getRow("select PictureBig,PictureSmall from member_contact_user where user_id ='" . $meeting['Maid'] . "' ", 0);

                    $meeting['PictureBig'] = $urow['PictureBig'];
                    $meeting['PictureSmall'] = $urow['PictureSmall'];
                }

                $meeting['PictureBig'] = $meeting['PictureBig'] == '' ? STEELHOME_SITE : ((strstr($meeting['PictureBig'], "http://")||strstr($meeting['PictureBig'], "https://")) ? $meeting['PictureBig'] : (strstr($meeting['PictureBig'], "//") ? 'http:' . $meeting['PictureBig'] : STEELHOME_SITE . $meeting['PictureBig']));
                $meeting['PictureSmall'] = $meeting['PictureSmall'] == '' ? STEELHOME_SITE : ((strstr($meeting['PictureBig'], "http://")||strstr($meeting['PictureSmall'], "https://")) ? $meeting['PictureSmall'] : (strstr($meeting['PictureSmall'], "//") ? 'http:' . $meeting['PictureSmall'] : STEELHOME_SITE . $meeting['PictureSmall']));
                $meeting['PictureBig'] = str_replace("http:////", "http://", $meeting['PictureBig']);
                $meeting['PictureSmall'] = str_replace("http:////", "http://", $meeting['PictureSmall']);
                $meeting['PictureBig'] = str_replace("http://", "https://", $meeting['PictureBig']);
                $meeting['PictureSmall'] = str_replace("http://", "https://", $meeting['PictureSmall']);
                $meeting['PictureBig'] = str_replace("http:https", "https", $meeting['PictureBig']);
                $meeting['PictureSmall'] = str_replace("http:https", "https", $meeting['PictureSmall']);
                //$meeting['CodeSmall'] = STEELHOME_SITE . $code;
                $meeting['CodeSmall'] = $meeting['CodeSmall'] == '' ? (STEELHOME_SITE . $code) : (STEELHOME_SITE . $meeting['CodeSmall']);
                $meeting['IsEn'] = $meeting['IsEn'];


                //add by xiangbin ******** start

                $membermessage = $this->_dao->getRow("SELECT * from member where mbid='" . $user['Mid'] . "' limit 1");

                $meeting['Ticketmessage'] = base64_encode_new("公司名称:" . $membermessage['kpcom'] . "\r\n\r\n纳税识别号:" . $membermessage['kprsbnum'] . " \r\n\r\n地址电话:" . $membermessage['kpaddress'] . "    " . $membermessage['kptel'] . "\r\n\r\n开户行及账号:" . $membermessage['kpbank'] . "    " . $membermessage['kpbankaccount']);


                //add by xiangbin ******** end


                $arr['Success'] = '1';

                //add by zhangcun for 报到历史记录 start 2017/3/28
                if ($record) {
                    $loguser = $this->getUser($GUID, $signcs);
                    $num = $this->_dao->getone("select 1 from meeting_signinlist where mtid='" . $mtid . "' and uid='" . $loguser['Uid'] . "' and mid='" . $user['Mid'] . "' and maid='" . $user['Maid'] . "'");
                    if ($user['Maid']) {
                        if ($num) {
                            $this->_dao->execute("update meeting_signinlist set code='" . $PersonSign . "', time='" . date("Y-m-d H:i:s", time()) . "' where mtid='" . $mtid . "' and uid='" . $loguser['Uid'] . "' and mid='" . $user['Mid'] . "' and maid='" . $user['Maid'] . "'");
                        } else {
                            $this->_dao->execute("insert into meeting_signinlist set mtid='" . $mtid . "',uid='" . $loguser['Uid'] . "', mid='" . $user['Mid'] . "', maid='" . $user['Maid'] . "', code='" . $PersonSign . "', time='" . date("Y-m-d H:i:s", time()) . "'");
                        }
                    }
                }
                //add by zhangcun for 报到历史记录 end 2017/3/28
                // if(isset($GLOBALS['QDRuType'][$mtid]))
                // {
                //     $sql = "update meeting_member_ruchang set RuStatus='1' where RuType='" . $GLOBALS['QDRuType'][$mtid][0] . "' and Mtid ='" . $mtid . "' and Cid='" . $user_1['ID'] . "' ";
                //     $this->_dao->execute($sql);
                // }
                if ($meeting['IsGuest'] == 0) {
                    $arr['Message'] = base64_encode_new('参会嘉宾信息');
                } elseif ($meeting['IsGuest'] == 1) {
                    $arr['Message'] = base64_encode_new('参会重要嘉宾信息');
                }
                $meeting['xuhao'] = $user['xuhao'];

                $arr['Result'] = $meeting;
            } else {
                $arr = array('Success' => '0',
                    'Message' => base64_encode_new('无此参会嘉宾'),
                );
            }
        } else {
            $arr = array('Success' => '0',
                'Message' => base64_encode_new('无此参会嘉宾'),
            );
        }
        } else {
            $arr = array('Success' => '0',
                'Message' => base64_encode_new('非法操作'),
            );
        }

        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    // 序号查询
    public function meet_XHSearch($params)
    {
        $GUID = $params['GUID'];
        $signcs = $params['SignCS'];
        $xuhao = $params['xuhao'];
        $mtid = $params['Mtid'];
        $record = $params['record'];
        $isen = $params['IsEn'];
        $isgethotel = $params['isgethotel'];
        if (empty($isen)) $isen = 0;//判断IsEn字段
        //$PersonSign='9000001';
        //$mtid=181;
        $this->setvars($mtid);

        $tmes = $this->_dao->query("select TypeName,TypeValue from meeting_set where mtid='$mtid' and SetType=4 and Status=1 "); //print_r($tmes);exit;
        foreach ($tmes as $id => $ar) {

            if ($ar['TypeName'] == "否") {
                $travel[$ar['TypeValue']] = 0;
            }
            if ($ar['TypeName'] == "是") {
                $travel[$ar['TypeValue']] = 1;
            }
            if ($ar['TypeName'] == "待定") {
                $travel[$ar['TypeValue']] = 2;
            }

        }

        $arr = array('Success' => '0',
            'Message' => 'null',
        );
        if ($xuhao != '') {//,IsMainMeeting,IsChildMeeting,IsChildMeeting2
            if(strlen($xuhao)==3)
            {
                $user = $this->_dao->getRow("SELECT ID,CompfnameS as compfname,TrueName,PersonSign,Sex,Post,ContactPhone,ContactMobile,ContactFax,ContactEmail,Mid,Mtid,Maid,CheckInedDesc,HotelRoomNumber,IsTravel,TravelPersons,HotelDesc,PictureBig,TableNumber,PictureSmall,IsGuest,GuestType,IsCheckIned,IsGetZl,IsGetjnp,IsGetMoney, HotelInType,IsMainMeeting,IsChildMeeting,IsChildMeeting2,IsChildMeeting3,IsChildMeeting4,IsChildMeeting5,IsChildMeeting6,IsChildMeeting7,IsChildMeeting8,IsChildMeeting9,IsChildMeeting10,remark,idcard,tralist,idcard1,IsEn,CodeSmall,IsCanZl,xuhao from meeting_member_contactman where  RIGHT( PersonSign, 3 )='$xuhao' and Mtid = '$mtid'  and status=1 limit 1 ", 0);//添加IsEn字段 
            }
            else {
                $user = $this->_dao->getRow("SELECT ID,CompfnameS as compfname,TrueName,PersonSign,Sex,Post,ContactPhone,ContactMobile,ContactFax,ContactEmail,Mid,Mtid,Maid,CheckInedDesc,HotelRoomNumber,IsTravel,TravelPersons,HotelDesc,PictureBig,TableNumber,PictureSmall,IsGuest,GuestType,IsCheckIned,IsGetZl,IsGetjnp,IsGetMoney, HotelInType,IsMainMeeting,IsChildMeeting,IsChildMeeting2,IsChildMeeting3,IsChildMeeting4,IsChildMeeting5,IsChildMeeting6,IsChildMeeting7,IsChildMeeting8,IsChildMeeting9,IsChildMeeting10,remark,idcard,tralist,idcard1,IsEn,CodeSmall,IsCanZl,xuhao from meeting_member_contactman where xuhao='$xuhao' and Mtid = '$mtid'  and status=1 limit 1 ", 0);//添加IsEn字段
            }
            if($user)
            {

            // print_r($user);exit;
            $code = $this->_dao->getOne("SELECT CodeSmall FROM member_contact_user WHERE user_id='" . $user['Maid'] . "'", 0);
            if ($user['IsEn'] == 1) {
                $member = $this->_dao->getRow("SELECT compabb,comtype from meeting_member where MidEn='" . $user['MidEn'] . "' and Mtid='" . $user['Mtid'] . "'", 0);
            } else {
                $member = $this->_dao->getRow("SELECT compabb,comtype from meeting_member where Mid='" . $user['Mid'] . "' and Mtid='" . $user['Mtid'] . "'", 0);
            }
            //$cangjia = $this->_dao->getRow("SELECT b.MeetingName,b.ChildMeetingName,b.ChildMeetingName2,b.ChildMeetingName3,b.ChildMeetingName4,b.ChildMeetingName5,b.ChildMeetingName6,b.ChildMeetingName7,b.ChildMeetingName8,b.ChildMeetingName9,b.ChildMeetingName10  from meeting_base as b,meeting_member_contactman as a where a.Mtid=b.ID and a.TrueName='" . $user['TrueName'] . "' and a.Mtid='" . $user['Mtid'] . "' and a.Mid='" . $user['Mid'] . "' and a.Mtid!=45", 0);
            $cangjia = $this->_dao->getRow("SELECT b.ShortName,b.ChildShortName,b.ChildShortName2,b.ChildShortName3,b.ChildShortName4,b.ChildShortName5,b.ChildShortName6,b.ChildShortName7,b.ChildShortName8,b.ChildShortName9,b.ChildShortName10  from meeting_base as b,meeting_member_contactman as a where  a.Mtid=b.ID and a.ID='" . $user['ID'] . "' and a.status=1", 0);
            //添加判断IsEn
            $user_1 = $this->_dao->getRow("SELECT * from meeting_member_contactman where ID='" . $user['ID'] . "' and Mtid ='$mtid' and status=1 limit 1 ", 0);//添加判断IsEn

            //add
            if ($isgethotel == 1) {
                $Meeting_set = $this->_dao->query("SELECT *  FROM `meeting_set` WHERE SetType =9 and Mtid='" . $mtid . "' order by OrdNum asc");
                if ($Meeting_set[0]['TypeValue'] < $Meeting_set[1]['TypeValue']) {
                    $dt_start = strtotime($Meeting_set[0]['TypeValue']);
                    $dt_end = strtotime($Meeting_set[1]['TypeValue']);
                } else {
                    $dt_end = strtotime($Meeting_set[0]['TypeValue']);
                    $dt_start = strtotime($Meeting_set[1]['TypeValue']);
                }

                $meeting_set_day = array();
                $day_num = 1;
                while ($dt_start <= $dt_end) {
                    $meeting_set_day[$day_num]['zhusutime'] = date('d日', $dt_start);
                    $day_num++;


                    $dt_start = strtotime('+1 day', $dt_start);
                }

                $bookdetail = $this->_dao->query("SELECT *  FROM `Restrant_BookDetail` WHERE (userid1  ='" . $user_1["ID"] . "' or  userid2 ='" . $user_1["ID"] . "')
                		                            and meetingid='" . $mtid . "' and status!=4 order by startDate asc");

                foreach ($bookdetail as $key => $value) {

                    $detail_start = strtotime($value['startDate']);
                    $detail_end = strtotime($value['endDate']);
                    $detail_end = strtotime('-1 day', $detail_end);

                    while ($detail_start <= $detail_end) {

                        foreach ($meeting_set_day as $k => $v) {
                            if ($v['zhusutime'] == date('d日', $detail_start)) {
                                $meeting_set_day[$k]['roomType'] = $value['roomType'];
                            }
                        }

                        $detail_start = strtotime('+1 day', $detail_start);
                    }
                }

                $m_hotel = $this->_dao->query("SELECT * FROM meeting_set_hotel WHERE Mtid='" . $mtid . "'");

                foreach ($m_hotel as $key => $value) {
                    foreach ($meeting_set_day as $k => $v) {
                        if ($value['ID'] == $v['roomType']) {

                            $meeting_set_day[$k]['hotelname'] = $value['HotelName'];
                            $meeting_set_day[$k]['roomname'] = str_replace("（嘉宾专用）", "", $value['RoomTypeName']);
                        }
                    }
                }
            }
            //end


            $join_right = 0;
            $join_main = 0;
            if ($user_1['IsMainMeeting'] == "1" || $user_1['IsChildMeeting'] == 1 || $user_1['IsChildMeeting2'] == 1) {
                $join_main = 1;
            }
            if ($join_main == 1 || $user_1['IsChildMeeting4'] == 1) {
                $join_right = 1;
            }

            $cangjia_1 = array();
            $cangjia_2 = array();
            if ($user_1['IsMainMeeting'] == 1) {
                $cangjia_1['MeetingName'] = $cangjia['ShortName'];
            }
            if ($user_1['IsChildMeeting'] == 1) {
                $cangjia_1['ChildMeetingName'] = $cangjia['ChildShortName'];
            }
            if ($user_1['IsChildMeeting2'] == 1) {
                $cangjia_1['ChildMeetingName2'] = $cangjia['ChildShortName2'];
            }
            if ($user_1['IsChildMeeting3'] == 1) {
                $cangjia_1['ChildMeetingName3'] = $cangjia['ChildShortName3'];
                $cangjia_2['ChildMeetingName3'] = $cangjia['ChildShortName3'];
            }
            if ($user_1['IsChildMeeting4'] == 1) // 汽车用钢
            {
                $cangjia_1['ChildMeetingName4'] = $cangjia['ChildShortName4'];
                $cangjia_2['ChildMeetingName4'] = $cangjia['ChildShortName4'];
            }
            if ($user_1['IsChildMeeting5'] == 1) {
                $cangjia_1['ChildMeetingName5'] = $cangjia['ChildShortName5'];
                $cangjia_2['ChildMeetingName5'] = $cangjia['ChildShortName5'];
            }
            if ($user_1['IsChildMeeting6'] == 1) {
                $cangjia_1['ChildMeetingName6'] = $cangjia['ChildShortName6'];
                $cangjia_2['ChildMeetingName6'] = $cangjia['ChildShortName6'];
            }
            if ($user_1['IsChildMeeting7'] == 1) {
                $cangjia_1['ChildMeetingName7'] = $cangjia['ChildShortName7'];
            }
            if ($user_1['IsChildMeeting8'] == 1) {
                $cangjia_1['ChildMeetingName8'] = $cangjia['ChildShortName8'];
            }
            if ($user_1['IsChildMeeting9'] == 1) {
                $cangjia_1['ChildMeetingName9'] = $cangjia['ChildShortName9'];
            }
            if ($user_1['IsChildMeeting10'] == 1) {
                $cangjia_1['ChildMeetingName10'] = $cangjia['ChildShortName10'];
            }

            if ($join_main == 1) {
                $cangjia_2['MeetingName'] = $cangjia['ShortName'];
                $cangjia_2['ChildMeetingName'] = $cangjia['ChildShortName'];
                $cangjia_2['ChildMeetingName2'] = $cangjia['ChildShortName2'];
            }
            if ($join_right == 1) {
                $cangjia_2['ChildMeetingName3'] = $cangjia['ChildShortName3'];
                $cangjia_2['ChildMeetingName5'] = $cangjia['ChildShortName5'];
                $cangjia_2['ChildMeetingName6'] = $cangjia['ChildShortName6'];
            }

            $str = implode(",", $cangjia_1);
            $user['cangjia'] = $str;

            $str2 = implode(",", $cangjia_2);
            $user['cangjia2'] = $str2;

            if ($user['Mtid'] == '45') {
                if ($user['cangjia'] == "") $user['cangjia'] = $this->_dao->getone("select ShortName from meeting_base where id=" . $mtid);
                $user['usertype'] = '钢之家员工';
            } elseif ($user['GuestType'] != 0) {
                $user['usertype'] = $GLOBALS['GuestType'][$user['GuestType']];
            } else {
                $user['usertype'] = '参会嘉宾';
            }

            $meeting = array_merge($user, $member);
            if ($meeting) {
                $meeting['Cid'] = $user_1['ID'];
                $meeting['TrueName'] = base64_encode_new($meeting['TrueName']);
                $meeting['Sex'] = base64_encode_new($meeting['Sex']);
                $meeting['Post'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($meeting['Post'])));
                $meeting['CheckInedDesc'] = base64_encode_new($meeting['CheckInedDesc']);
                //$meeting['HotelDesc'] = base64_encode_new($meeting['HotelDesc']);
                $meeting['compfname'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($meeting['compfname'])));
                $meeting['compabb'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($meeting['compabb'])));
                $meeting['comtype'] = base64_encode_new($meeting['comtype']);
                $meeting['usertype'] = base64_encode_new($meeting['usertype']);
                $meeting['cangjia'] = base64_encode_new($meeting['cangjia']);
                $meeting['cangjia2'] = base64_encode_new($meeting['cangjia2']);
                $meeting['travelstate'] = $travel[$meeting['IsTravel']];//避免与istravel混淆
                if ($isgethotel == 1) {
                    $hotelinfo = array();
                    foreach ($meeting_set_day as $key => $val) {
                        if ($val['roomType'] != '') {
                            $hotelarr[] = array('zhusutime' => base64_encode_new($val['zhusutime'])
                            , 'hotelname' => base64_encode_new($val['hotelname']),
                                'roomname' => base64_encode_new(str_replace("（嘉宾专用）", "", $val['roomname'])));

                        }

                        if ($val['roomType'] != '') {
                            $hotelinfo[] = array('zhusutime' => $val['zhusutime']
                            , 'hotelname' => $val['hotelname'],
                                'roomname' => str_replace("（嘉宾专用）", "", $val['roomname']));

                        }

                    }

                }
                if (empty($hotelarr)) {
                    $meeting['hotelarr'] = array();
                    $meeting['HotelDesc'] = '';
                } else {
                    $meeting['hotelarr'] = $hotelarr;
                    $s_info = reset($hotelinfo);
                    $e_info = end($hotelinfo);
                    if ($s_info['zhusutime'] != $e_info['zhusutime']) {
                        $info = $s_info['zhusutime'] . '-' . $e_info['zhusutime'] . ' ' . $s_info['hotelname'] . '' . $s_info['roomname'];
                        $info = $s_info['zhusutime'] . '-' . $e_info['zhusutime'] . ' ' . $s_info['roomname'];
                    } else {
                        $info = $s_info['zhusutime'] . ' ' . $s_info['hotelname'] . '' . $s_info['roomname'];
                        $info = $s_info['zhusutime'] . ' ' . $s_info['roomname'];

                    }
                    $meeting['HotelDesc'] = base64_encode_new($info);
                }


                //酒店预订信息
                if ($user_1['HotelInType']) {
                    $hoteldesc = $GLOBALS['HOTELDESC'][$user_1['HotelInType']] . "房间号" . $user_1['HotelRoomNumber'] . "(已入住)";
                } else {
                    $hoteldesc = $GLOBALS['HOTELT'][$user_1['HotelType']] == "" ? "" : $GLOBALS['HOTELT'][$user_1['HotelType']] . "(未入住)";
                }

                $meeting['HotelIn'] = "1";
                if (strstr($GLOBALS['HOTELT'][$user_1['HotelType']], "明城")) {
                    $meeting['HotelIn'] = "1";
                }
                if (strstr($GLOBALS['HOTELT'][$user_1['HotelType']], "东方")) {
                    $meeting['HotelIn'] = "2";
                }
                if (strstr($GLOBALS['HOTELT'][$user_1['HotelType']], "长航")) {
                    $meeting['HotelIn'] = "3";
                }
                if (strstr($GLOBALS['HOTELT'][$user_1['HotelType']], "紫金山")) {
                    $meeting['HotelIn'] = "4";
                }


                //$meeting['HotelDesc'] = base64_encode_new($hoteldesc);

                //$meeting['IsGetMoney'] = $meeting['IsGetMoney'] == "0"?"0":"1";


                if ($meeting['PictureBig'] == "") {
                    $urow = $this->_dao->getRow("select PictureBig,PictureSmall from member_contact_user where user_id ='" . $meeting['Maid'] . "' ", 0);

                    $meeting['PictureBig'] = $urow['PictureBig'];
                    $meeting['PictureSmall'] = $urow['PictureSmall'];
                }

                $meeting['PictureBig'] = $meeting['PictureBig'] == '' ? STEELHOME_SITE : ((strstr($meeting['PictureBig'], "http://")||strstr($meeting['PictureBig'], "https://")) ? $meeting['PictureBig'] : (strstr($meeting['PictureBig'], "//") ? 'http:' . $meeting['PictureBig'] : STEELHOME_SITE . $meeting['PictureBig']));
                $meeting['PictureSmall'] = $meeting['PictureSmall'] == '' ? STEELHOME_SITE : ((strstr($meeting['PictureBig'], "http://")||strstr($meeting['PictureSmall'], "https://")) ? $meeting['PictureSmall'] : (strstr($meeting['PictureSmall'], "//") ? 'http:' . $meeting['PictureSmall'] : STEELHOME_SITE . $meeting['PictureSmall']));
                $meeting['PictureBig'] = str_replace("http:////", "http://", $meeting['PictureBig']);
                $meeting['PictureSmall'] = str_replace("http:////", "http://", $meeting['PictureSmall']);
                $meeting['PictureBig'] = str_replace("http://", "https://", $meeting['PictureBig']);
                $meeting['PictureSmall'] = str_replace("http://", "https://", $meeting['PictureSmall']);
                $meeting['PictureBig'] = str_replace("http:https", "https", $meeting['PictureBig']);
                $meeting['PictureSmall'] = str_replace("http:https", "https", $meeting['PictureSmall']);
                //$meeting['CodeSmall'] = STEELHOME_SITE . $code;
                $meeting['CodeSmall'] = $meeting['CodeSmall'] == '' ? (STEELHOME_SITE . $code) : (STEELHOME_SITE . $meeting['CodeSmall']);
                $meeting['IsEn'] = $meeting['IsEn'];


                //add by xiangbin ******** start

                $membermessage = $this->_dao->getRow("SELECT * from member where mbid='" . $user['Mid'] . "' limit 1");

                $meeting['Ticketmessage'] = base64_encode_new("公司名称:" . $membermessage['kpcom'] . "\r\n\r\n纳税识别号:" . $membermessage['kprsbnum'] . " \r\n\r\n地址电话:" . $membermessage['kpaddress'] . "    " . $membermessage['kptel'] . "\r\n\r\n开户行及账号:" . $membermessage['kpbank'] . "    " . $membermessage['kpbankaccount']);


                //add by xiangbin ******** end


                $arr['Success'] = '1';

                //add by zhangcun for 报到历史记录 start 2017/3/28
                if ($record) {
                    $loguser = $this->getUser($GUID, $signcs);
                    $num = $this->_dao->getone("select 1 from meeting_signinlist where mtid='" . $mtid . "' and uid='" . $loguser['Uid'] . "' and mid='" . $user['Mid'] . "' and maid='" . $user['Maid'] . "'");
                    if ($user['Maid']) {
                        if ($num) {
                            $this->_dao->execute("update meeting_signinlist set code='" . $user['PersonSign'] . "', time='" . date("Y-m-d H:i:s", time()) . "' where mtid='" . $mtid . "' and uid='" . $loguser['Uid'] . "' and mid='" . $user['Mid'] . "' and maid='" . $user['Maid'] . "'");
                        } else {
                            $this->_dao->execute("insert into meeting_signinlist set mtid='" . $mtid . "',uid='" . $loguser['Uid'] . "', mid='" . $user['Mid'] . "', maid='" . $user['Maid'] . "', code='" . $user['PersonSign'] . "', time='" . date("Y-m-d H:i:s", time()) . "'");
                        }
                    }
                }
                //add by zhangcun for 报到历史记录 end 2017/3/28

                // if(isset($GLOBALS['QDRuType'][$mtid]))
                // {
                //     $sql = "update meeting_member_ruchang set RuStatus='1' where RuType='" . $GLOBALS['QDRuType'][$mtid][0] . "' and Mtid ='" . $mtid . "' and Cid='" . $user_1['ID'] . "' ";
                //     $this->_dao->execute($sql);
                // }
                if ($meeting['IsGuest'] == 0) {
                    $arr['Message'] = base64_encode_new('参会嘉宾信息');
                } elseif ($meeting['IsGuest'] == 1) {
                    $arr['Message'] = base64_encode_new('参会重要嘉宾信息');
                }

                $arr['Result'] = $meeting;
            } else {
                $arr = array('Success' => '0',
                    'Message' => base64_encode_new('无此参会嘉宾'),
                    //'IsEn'=>$meeting['IsEn'],
                );
            }
         }
         else {
            $arr = array('Success' => '0',
                'Message' => base64_encode_new('无此参会嘉宾'),
                //'IsEn'=>$meeting['IsEn'],
            );
        }
        } else {
            $arr = array('Success' => '0',
                'Message' => base64_encode_new('非法操作'),
                //'IsEn'=>$meeting['IsEn'],
            );
        }

        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    // 入场接口
    public function meet_RuChang($params)
    {
        $GUID = $params['GUID'];
        $signcs = $params['SignCS'];
        $QRCode = $params['QRCode'];
        $RuType = $params['RuType'];
        $meetid = $params['ID'];

        $arr = array('Success' => '0',
            'Message' => 'null',
        );

        if ($signcs != '' && $GUID != '') {
            // $QRCode=substr($QRCode,0,-1);
            // 添加微信二维码判断
            //$data = explode("|" , $QRCode);
            $data = $this->decodeQRCode($QRCode);
            // $guid = $data[0];
            $mbid = $data[1];
            $maid = $data[2];
            $bzf = $data[3];
            $isen = $data[4];//添加判断IsEn
            if (empty($isen)) $isen = 0;

            $user = $this->_dao->getRow("SELECT ID ,TrueName,PersonSign,Sex,Post,ContactPhone,ContactMobile,ContactFax,ContactEmail,Mtid,Maid,CheckInedDesc,HotelRoomNumber,IsMainMeeting,IsChildMeeting,IsChildMeeting2,IsChildMeeting3,IsChildMeeting4,IsChildMeeting5,IsChildMeeting6,IsChildMeeting7,IsChildMeeting8,IsChildMeeting9,IsChildMeeting10,IsTravel,TravelPersons,HotelDesc,PictureBig,TableNumber,PictureSmall,IsCheckIned,IsGuest,GuestType,IsGetZl,GUID from meeting_member_contactman where IsEn='$isen' and Mtid='$meetid' AND Maid='$maid' and status=1", 0);//添加IsEn
            $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,Mid from meeting_member where Mid='$mbid' and Mtid='$meetid' ", 0);
            $cangjia = $this->_dao->getRow("SELECT b.MeetingName,b.ChildMeetingName,b.ChildMeetingName2,b.ChildMeetingName3,b.ChildMeetingName4,b.ChildMeetingName5,b.ChildMeetingName6,b.ChildMeetingName7,b.ChildMeetingName8,b.ChildMeetingName9,b.ChildMeetingName10  from meeting_base as b,meeting_member_contactman as a where a.Mtid=b.ID and a.TrueName='" . $user['TrueName'] . "' and a.Mtid='" . $user['Mtid'] . "' and a.Mid='$mbid' and a.Mtid!=45  and a.status=1", 0);
            $user_1 = $this->_dao->getRow("SELECT IsMainMeeting,IsChildMeeting,IsChildMeeting2,IsChildMeeting3,IsChildMeeting4,IsChildMeeting5,IsChildMeeting6,IsChildMeeting7,IsChildMeeting8,IsChildMeeting9,IsChildMeeting10  from meeting_member_contactman where IsEn='$isen' and GUID='$guid' and status=1 limit 1 ", 0);//添加IsEn
            $cangjia_1 = array();
            if ($user_1['IsMainMeeting'] == 1) {
                $cangjia_1['MeetingName'] = $cangjia['MeetingName'];
            }
            if ($user_1['IsChildMeeting'] == 1) {
                $cangjia_1['ChildMeetingName'] = $cangjia['ChildMeetingName'];
            }
            if ($user_1['IsChildMeeting2'] == 1) {
                $cangjia_1['ChildMeetingName2'] = $cangjia['ChildMeetingName2'];
            }
            if ($user_1['IsChildMeeting3'] == 1) {
                $cangjia_1['ChildMeetingName3'] = $cangjia['ChildMeetingName3'];
            }
            if ($user_1['IsChildMeeting4'] == 1) {
                $cangjia_1['ChildMeetingName4'] = $cangjia['ChildMeetingName4'];
            }
            if ($user_1['IsChildMeeting5'] == 1) {
                $cangjia_1['ChildMeetingName5'] = $cangjia['ChildMeetingName5'];
            }
            if ($user_1['IsChildMeeting6'] == 1) {
                $cangjia_1['ChildMeetingName6'] = $cangjia['ChildMeetingName6'];
            }
            if ($user_1['IsChildMeeting7'] == 1) {
                $cangjia_1['ChildMeetingName7'] = $cangjia['ChildMeetingName7'];
            }
            if ($user_1['IsChildMeeting8'] == 1) {
                $cangjia_1['ChildMeetingName8'] = $cangjia['ChildMeetingName8'];
            }
            if ($user_1['IsChildMeeting9'] == 1) {
                $cangjia_1['ChildMeetingName9'] = $cangjia['ChildMeetingName9'];
            }
            if ($user_1['IsChildMeeting10'] == 1) {
                $cangjia_1['ChildMeetingName10'] = $cangjia['ChildMeetingName10'];
            }
            $str = implode(",", $cangjia_1);
            $user['cangjia'] = $str;
            if ($meetid == '45') {
                $user['usertype'] = '钢之家员工';
            } elseif ($user['GuestType'] != 0) {
                $user['usertype'] = $GLOBALS['GuestType'][$user['GuestType']];
            } else {
                $user['usertype'] = '参会嘉宾';
            }
            $meeting = array_merge($user, $member);
            if ($meeting) {
                if ($RuType == 1 || $RuType == 2) {
                    if ($meeting['IsMainMeeting'] == 0 && $meeting['IsChildMeeting'] == 0 && $meeting['IsChildMeeting2'] == 0 && $meeting['IsChildMeeting3'] == 0 && $meeting['IsChildMeeting4'] == 0 && $meeting['IsChildMeeting5'] == 0 && $meeting['IsChildMeeting6'] == 0 && $meeting['IsChildMeeting7'] == 0 && $meeting['IsChildMeeting8'] == 0 && $meeting['IsChildMeeting9'] == 0 && $meeting['IsChildMeeting10'] == 0 && $meetid != '45') {
                        $meeting['TrueName'] = base64_encode_new($meeting['TrueName']);
                        $meeting['Sex'] = base64_encode_new($meeting['Sex']);
                        $meeting['Post'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($meeting['Post'])));
                        $meeting['CheckInedDesc'] = base64_encode_new($meeting['CheckInedDesc']);
                        $meeting['HotelDesc'] = base64_encode_new($meeting['HotelDesc']);
                        $meeting['compfname'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($meeting['compfname'])));
                        $meeting['compabb'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($meeting['compabb'])));
                        $meeting['comtype'] = base64_encode_new($meeting['comtype']);
                        $meeting['usertype'] = base64_encode_new($meeting['usertype']);
                        $meeting['cangjia'] = base64_encode_new($meeting['cangjia']);
                        $meeting['PictureBig'] = $meeting['PictureBig'] == '' ? STEELHOME_SITE : ((strstr($meeting['PictureBig'], "http://")||strstr($meeting['PictureBig'], "https://")) ? $meeting['PictureBig'] : (strstr($meeting['PictureBig'], "//") ? 'http:' . $meeting['PictureBig'] : STEELHOME_SITE . $meeting['PictureBig']));
                        $meeting['PictureSmall'] = $meeting['PictureSmall'] == '' ? STEELHOME_SITE : ((strstr($meeting['PictureBig'], "http://")||strstr($meeting['PictureSmall'], "https://")) ? $meeting['PictureSmall'] : (strstr($meeting['PictureSmall'], "//") ? 'http:' . $meeting['PictureSmall'] : STEELHOME_SITE . $meeting['PictureSmall']));
                        $meeting['PictureBig'] = str_replace("http:////", "http://", $meeting['PictureBig']);
                        $meeting['PictureSmall'] = str_replace("http:////", "http://", $meeting['PictureSmall']);
                        $meeting['PictureBig'] = str_replace("http://", "https://", $meeting['PictureBig']);
                        $meeting['PictureSmall'] = str_replace("http://", "https://", $meeting['PictureSmall']);
                        $meeting['PictureBig'] = str_replace("http:https", "https", $meeting['PictureBig']);
                        $meeting['PictureSmall'] = str_replace("http:https", "https", $meeting['PictureSmall']);


                        $arr['Success'] = '0';
                        $arr['Message'] = base64_encode_new('未参会，不可入场');
                        $arr['Result'] = $meeting;
                    } else {
                        if ($RuType == 4) {
                            $RuName = $meeting['TableNumber'];
                        } else {
                            $RuName = $GLOBALS['RuType'][$RuType];
                        }

                        $loginman = $this->_dao->getRow("SELECT * from meeting_app_session where GUID='$GUID' ", 0);
                        $contactman = $this->_dao->getRow("SELECT Status from meeting_member_ruchang where Mtid='$meetid' AND Maid='$maid' and RuName='" . $RuName . "' order by CheckInedDate DESC limit 1", 0);

                        if ($contactman['Status'] == 2 || empty($contactman) || $meetid == '45') {
                            $this->_dao->execute("insert into meeting_member_ruchang SET Cid='" . $meeting['ID'] . "',Mtid='" . $meeting['Mtid'] . "',Mid='" . $meeting['Mid'] . "',CompfnameS='" . $meeting['compfname'] . "',PersonSign='" . $meeting['PersonSign'] . "',TrueName='" . $meeting['TrueName'] . "',Sex='" . $meeting['Sex'] . "',Post='" . $meeting['Post'] . "',ContactPhone='" . $meeting['ContactPhone'] . "',ContactMobile='" . $meeting['ContactMobile'] . "',CheckInedDate=NOW(),CheckInedUserName='" . $loginman['TrueName'] . "', CheckInedUserId='" . $loginman['Uid'] . "',RuType='" . $RuType . "',Runame='" . $RuName . "',Status='1',GUID='" . $meeting['GUID'] . "' ;");
                            if ($meeting['IsGuest'] == 0) {
                                $arr['Message'] = base64_encode_new('参会嘉宾入场');
                            } elseif ($meeting['IsGuest'] == 1) {
                                $arr['Message'] = base64_encode_new('参会重要嘉宾入场');
                            }
                        } elseif ($contactman['Status'] == 1 && $meetid != '45') {
                            if ($meeting['IsGuest'] == 0) {
                                $arr['Message'] = base64_encode_new('参会嘉宾已经入场,不可重复入场');
                            } elseif ($meeting['IsGuest'] == 1) {
                                $arr['Message'] = base64_encode_new('参会重要嘉宾已经入场,不可重复入场');
                            }
                        }

                        $meeting['TrueName'] = base64_encode_new($meeting['TrueName']);
                        $meeting['Sex'] = base64_encode_new($meeting['Sex']);
                        $meeting['Post'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($meeting['Post'])));
                        $meeting['CheckInedDesc'] = base64_encode_new($meeting['CheckInedDesc']);
                        $meeting['HotelDesc'] = base64_encode_new($meeting['HotelDesc']);
                        $meeting['compfname'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($meeting['compfname'])));
                        $meeting['compabb'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($meeting['compabb'])));
                        $meeting['usertype'] = base64_encode_new($meeting['usertype']);
                        $meeting['cangjia'] = base64_encode_new($meeting['cangjia']);
                        $meeting['comtype'] = base64_encode_new($meeting['comtype']);
                        $meeting['PictureBig'] = $meeting['PictureBig'] == '' ? STEELHOME_SITE : ((strstr($meeting['PictureBig'], "http://")||strstr($meeting['PictureBig'], "https://")) ? $meeting['PictureBig'] : (strstr($meeting['PictureBig'], "//") ? 'http:' . $meeting['PictureBig'] : STEELHOME_SITE . $meeting['PictureBig']));
                        $meeting['PictureSmall'] = $meeting['PictureSmall'] == '' ? STEELHOME_SITE : ((strstr($meeting['PictureBig'], "http://")||strstr($meeting['PictureSmall'], "https://")) ? $meeting['PictureSmall'] : (strstr($meeting['PictureSmall'], "//") ? 'http:' . $meeting['PictureSmall'] : STEELHOME_SITE . $meeting['PictureSmall']));
                        $meeting['PictureBig'] = str_replace("http:////", "http://", $meeting['PictureBig']);
                        $meeting['PictureSmall'] = str_replace("http:////", "http://", $meeting['PictureSmall']);
                        $meeting['PictureBig'] = str_replace("http://", "https://", $meeting['PictureBig']);
                        $meeting['PictureSmall'] = str_replace("http://", "https://", $meeting['PictureSmall']);
                        $meeting['PictureBig'] = str_replace("http:https", "https", $meeting['PictureBig']);
                        $meeting['PictureSmall'] = str_replace("http:https", "https", $meeting['PictureSmall']);
                        // print_r($meeting);
                        $arr['Success'] = '1';
                        // $arr['Message']= base64_encode_new('入场参会嘉宾信息');
                        $arr['Result'] = $meeting;
                    }
                } elseif ($RuType == 3 || $RuType == 4) {
                    if ($RuType == 4) {
                        $RuName = $meeting['TableNumber'];
                    } else {
                        $RuName = $GLOBALS['RuType'][$RuType];
                    }

                    $loginman = $this->_dao->getRow("SELECT * from meeting_app_session where GUID='$GUID' ", 0);
                    $contactman = $this->_dao->getRow("SELECT Status from meeting_member_ruchang where Mtid='$meetid' AND Maid='$maid' and RuName='" . $RuName . "' order by CheckInedDate DESC limit 1", 0);
                    if ($contactman['Status'] == 2 || empty($contactman) || $meetid == '45') {
                        $this->_dao->execute("insert into meeting_member_ruchang SET Cid='" . $meeting['ID'] . "',Mtid='" . $meeting['Mtid'] . "',Mid='" . $meeting['Mid'] . "',CompfnameS='" . $meeting['compfname'] . "',PersonSign='" . $meeting['PersonSign'] . "',TrueName='" . $meeting['TrueName'] . "',Sex='" . $meeting['Sex'] . "',Post='" . $meeting['Post'] . "',ContactPhone='" . $meeting['ContactPhone'] . "',ContactMobile='" . $meeting['ContactMobile'] . "',CheckInedDate=NOW(),CheckInedUserName='" . $loginman['TrueName'] . "', CheckInedUserId='" . $loginman['Uid'] . "',RuType='" . $RuType . "',Runame='" . $RuName . "',Status='1',GUID='" . $meeting['GUID'] . "' ;");
                        if ($meeting['IsGuest'] == 0) {
                            $arr['Message'] = base64_encode_new('参会嘉宾入场');
                        } elseif ($meeting['IsGuest'] == 1) {
                            $arr['Message'] = base64_encode_new('参会重要嘉宾入场');
                        }
                    } elseif ($contactman['Status'] == 1 && $meetid != '45') {
                        if ($meeting['IsGuest'] == 0) {
                            $arr['Message'] = base64_encode_new('参会嘉宾已经入场,不可重复入场');
                        } elseif ($meeting['IsGuest'] == 1) {
                            $arr['Message'] = base64_encode_new('参会重要嘉宾已经入场,不可重复入场');
                        }
                    }

                    $meeting['TrueName'] = base64_encode_new($meeting['TrueName']);
                    $meeting['Sex'] = base64_encode_new($meeting['Sex']);
                    $meeting['Post'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($meeting['Post'])));
                    $meeting['CheckInedDesc'] = base64_encode_new($meeting['CheckInedDesc']);
                    $meeting['HotelDesc'] = base64_encode_new($meeting['HotelDesc']);
                    $meeting['compfname'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($meeting['compfname'])));
                    $meeting['compabb'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($meeting['compabb'])));
                    $meeting['usertype'] = base64_encode_new($meeting['usertype']);
                    $meeting['cangjia'] = base64_encode_new($meeting['cangjia']);
                    $meeting['comtype'] = base64_encode_new($meeting['comtype']);
                    $meeting['PictureBig'] = $meeting['PictureBig'] == '' ? STEELHOME_SITE : ((strstr($meeting['PictureBig'], "http://")||strstr($meeting['PictureBig'], "https://")) ? $meeting['PictureBig'] : (strstr($meeting['PictureBig'], "//") ? 'http:' . $meeting['PictureBig'] : STEELHOME_SITE . $meeting['PictureBig']));
                    $meeting['PictureSmall'] = $meeting['PictureSmall'] == '' ? STEELHOME_SITE : ((strstr($meeting['PictureBig'], "http://")||strstr($meeting['PictureSmall'], "https://")) ? $meeting['PictureSmall'] : (strstr($meeting['PictureSmall'], "//") ? 'http:' . $meeting['PictureSmall'] : STEELHOME_SITE . $meeting['PictureSmall']));
                    $meeting['PictureBig'] = str_replace("http:////", "http://", $meeting['PictureBig']);
                    $meeting['PictureSmall'] = str_replace("http:////", "http://", $meeting['PictureSmall']);
                    $meeting['PictureBig'] = str_replace("http://", "https://", $meeting['PictureBig']);
                    $meeting['PictureSmall'] = str_replace("http://", "https://", $meeting['PictureSmall']);
                    $meeting['PictureBig'] = str_replace("http:https", "https", $meeting['PictureBig']);
                    $meeting['PictureSmall'] = str_replace("http:https", "https", $meeting['PictureSmall']);
                    // print_r($meeting);
                    $arr['Success'] = '1';
                    $arr['Result'] = $meeting;
                } //$RuType==3 || $RuType==4
            } else {
                $arr = array('Success' => '0',
                    'Message' => 'null',
                );
            }
        }

        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    // 入场接口
    public function meet_RuChang2($params)
    {
        $GUID = $params['GUID'];
        $signcs = $params['SignCS'];
        $QRCode = base64_decode($params['QRCode']);
        $RuType = $params['RuType'];
        $meetid = $params['ID'];
        $BHcode = $params['BHcode'];

        $IsMeetIn = $params['IsMeetIn']; // 是否餐饮

        $IsDinner = $params['IsDinner']; // 是否晚宴
        $meeting['TrueName'] = str_replace("'", "\'", $meeting['TrueName']);

        $arr = array('Success' => '0',
            'Message' => base64_encode_new('操作失败'),
        );
        if ($signcs != '' && $GUID != '') {

            $loginman = $this->_dao->getRow("SELECT * from meeting_app_session where GUID='$GUID' ", 0);
            $user['TrueName'] = str_replace("'", "\'", $user['TrueName']);


            if (!empty($BHcode)) {
                $user = $this->_dao->getRow("select * from meeting_member_contactman where PersonSign='$BHcode' and (Mtid='$meetid' or Mtid ='45') and status=1 limit 1", 0);

                if ($user['IsEn'] == "1") {
                    $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,MidEn as Mid from meeting_member where   MidEn='" . $user['Mid'] . "' and Mtid='" . $user['Mtid'] . "'", 0);
                } else {
                    $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,Mid from meeting_member where  IsEn='$isen' and  Mid='" . $user['Mid'] . "' and Mtid='" . $user['Mtid'] . "'", 0);
                }


            } else {
                // 添加微信二维码判断
                //$data = explode("|" , $QRCode);
                $data = $this->decodeQRCode($QRCode);
                // $guid = $data[0];
                $mbid = $data[1];
                $maid = $data[2];
                $bzf = $data[3];
                $isen = $data[4];
                if (empty($isen)) $isen = 0;//添加IsEn字段

                $user = $this->_dao->getRow("SELECT * from meeting_member_contactman where IsEn='$isen' and Mtid='$meetid'  AND Maid='$maid' and status=1 ", 0);//添加IsEn字段

                if ($isen != "1") {
                    $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,Mid from meeting_member where Mid='" . $user['Mid'] . "' and Mtid='" . $user['Mtid'] . "'", 0);

                    if (empty($user)) {
                        $user = $this->_dao->getRow("SELECT * from meeting_member_contactman where IsEn='$isen' and  Mtid=45 AND Maid='$maid' and status=1 ", 0);//添加IsEn字段
                        $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,Mid from meeting_member where Mid='" . $user['Mid'] . "' and Mtid='" . $user['Mtid'] . "'", 0);
                    }
                } else {
                    $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,MidEn as Mid from meeting_member where MidEn='" . $user['MidEn'] . "' and Mtid='" . $user['Mtid'] . "'", 0);

                    if (empty($user)) {
                        $user = $this->_dao->getRow("SELECT * from meeting_member_contactman where IsEn='$isen' and  Mtid=45 AND Maid='$maid' and status=1 ", 0);//添加IsEn字段
                        $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,MidEn as Mid from meeting_member where MidEn='" . $user['MidEn'] . "' and Mtid='" . $user['Mtid'] . "'", 0);
                    }
                }
            }

            //			if ($user['Mtid'] == '45')
            //            {
            //                $usertype = '钢之家员工';
            //            }
            //			elseif ($user['GuestType'] != 0)
            //            {
            //                $usertype = $GLOBALS['GuestType'][$user['GuestType']];
            //            }
            //            else
            //            {
            //                $usertype = '参会嘉宾';
            //            }
            $meeting = array_merge($user, $member);
            $meeting['TrueName'] = str_replace("'", "\'", $meeting['TrueName']);
            $meeting['compfname'] = str_replace("'", "\'", $meeting['compfname']);

            $isfl = "1";


            if ($meeting) {
                if ($IsMeetIn != "1") {
                    if (($meeting['IsMainMeeting'] == 0 && $meeting['IsChildMeeting'] == 0 && $meeting['IsChildMeeting2'] == 0 && $meeting['IsChildMeeting3'] == 0 && $meeting['IsChildMeeting4'] == 0 && $meeting['IsChildMeeting5'] == 0 && $meeting['IsChildMeeting6'] == 0 && $meeting['IsChildMeeting7'] == 0 && $meeting['IsChildMeeting8'] == 0 && $meeting['IsChildMeeting9'] == 0 && $meeting['IsChildMeeting10'] == 0 && $meeting['Mtid'] != '45') || ($meeting['IsCheckIned'] != "1" && $meeting['Mtid'] != '45') || ($meeting['IsGetMoney'] == "0" && $meeting['Mtid'] != '45')) {
                        $arr['Success'] = '0';
                        $isfl = 0;
                        if ($meeting['IsGetMoney'] == "0") {
                            $arr['Message'] = base64_encode_new('请您至报到处办理相关手续即可入场！');
                        } else
                            if ($meeting['IsCheckIned'] != "1") {//未报到

                                $arr['Message'] = base64_encode_new('请您至报到处办理相关手续即可入场！');
                            } else {//未报名
                                //if($meeting['IsChildMeeting2'] == "1"){
                                //$arr['Message'] = base64_encode_new('您只可参加 中国大宗商品电子商务高峰论坛暨中国大宗物资网一周年庆典');
                                //}else{
                                $arr['Message'] = base64_encode_new('请您至报到处办理相关手续即可入场！');
                                //}
                            }
                        $arr['Result'] = '';

                        //失败日志
                        $this->_dao->execute("insert into meeting_member_ruchang SET Cid='" . $meeting['ID'] . "',Mtid='" . $meetid . "',Mid='" . $meeting['Mid'] . "',CompfnameS='" . $meeting['compfname'] . "',PersonSign='" . $meeting['PersonSign'] . "',TrueName='" . $meeting['TrueName'] . "',Sex='" . $meeting['Sex'] . "',Post='" . $meeting['Post'] . "',ContactPhone='" . $meeting['ContactPhone'] . "',ContactMobile='" . $meeting['ContactMobile'] . "',CheckInedDate=NOW(),CheckInedUserName='" . $loginman['TrueName'] . "', CheckInedUserId='" . $loginman['Uid'] . "',RuType='" . $RuType . "',Status='9'");
                    } else {
                        if ($IsDinner == "1") {
                            $RuName = $meeting['TableNumber'];
                        } else {
                            $RuName = $GLOBALS['RuType'][$RuType];
                        }
                    }
                } elseif ($IsMeetIn == "1" && $meeting['Mtid'] != '45') {
                    if ($meeting['IsCheckIned'] != "1") {

                        $isfl = 0;
                        $arr['Success'] = '0';
                        $arr['Message'] = base64_encode_new('请您至报到处办理相关手续即可入场！');

                    } else if ($meeting['IsGetMoney'] == "0") {

                        $isfl = 0;
                        $arr['Success'] = '0';
                        $arr['Message'] = base64_encode_new('该嘉宾证不含餐饮，请与相关负责人联系！');

                    } else {

                        if ($IsDinner == "1") {
                            $RuName = $meeting['TableNumber'];
                        } else {
                            $RuName = $GLOBALS['RuType'][$RuType];
                        }

                    }
                }

                if ($isfl) {


                    $this->_dao->execute("insert into meeting_member_ruchang SET Cid='" . $meeting['ID'] . "',Mtid='" . $meetid . "',Mid='" . $meeting['Mid'] . "',CompfnameS='" . $meeting['compfname'] . "',PersonSign='" . $meeting['PersonSign'] . "',TrueName='" . $meeting['TrueName'] . "',Sex='" . $meeting['Sex'] . "',Post='" . $meeting['Post'] . "',ContactPhone='" . $meeting['ContactPhone'] . "',ContactMobile='" . $meeting['ContactMobile'] . "',CheckInedDate=NOW(),CheckInedUserName='" . $loginman['TrueName'] . "', CheckInedUserId='" . $loginman['Uid'] . "',RuType='" . $RuType . "',Runame='" . $RuName . "',Status='1',GUID='" . $meeting['GUID'] . "'");

                    $num = $this->_dao->getOne("SELECT count(*) as c FROM meeting_member_ruchang WHERE RuType='" . $RuType . "' AND Mtid='" . $meetid . "' AND Cid='" . $meeting['ID'] . "' AND Status=1", 0);

                    //获取当前场内人数
                    //餐饮
                    //if($RuType == 6 || $RuType == 7 || $RuType == 8|| $RuType == 9|| $RuType == 19){
                    $snum = $this->_dao->getOne("SELECT count(distinct Cid) as c FROM meeting_member_ruchang WHERE RuType='" . $RuType . "' AND Mtid='" . $meetid . "'  AND Status=1 ", 0);

                    $snumlose = $this->_dao->getOne("SELECT count(distinct Cid) as c FROM meeting_member_ruchang WHERE RuType='" . $RuType . "' AND Mtid='" . $meetid . "'  AND Status=9 ", 0);

                    //}else{
                    //	$snum = $this->_dao->getOne("SELECT count(distinct Cid) as c FROM meeting_member_ruchang WHERE RuType='" . $RuType . "' AND Mtid='" . $meetid . "' and (Mid != 1 or Cid in (select ID from meeting_member_contactman as mm where mm.Mtid = '$meetid' and mm.IsGuest = 1)) AND Status=1 ", 0);
                    //}

                    //echo  "SELECT count(*) as c FROM meeting_member_ruchang WHERE RuType='" . $RuType . "' AND Mtid='" .$meetid . "' AND Cid='" . $meeting['ID'] . "' AND Status=1"."||"."SELECT count(distinct Cid) as c FROM meeting_member_ruchang WHERE RuType='" . $RuType . "' AND Mtid='" . $meetid . "'  AND Status=1 ";


                    if ($num == 1) {
                        $arr['Message'] = base64_encode_new("您是第" . $num . "次入场;当前场内人数" . $snum . " 入场失败人数" . $snumlose);
                    } else {
                        //上次入场时间
                        $list = $this->_dao->getRow("SELECT * FROM meeting_member_ruchang WHERE RuType='" . $RuType . "' AND Mtid='" . $meetid . "' AND Cid='" . $meeting['ID'] . "' AND Status=1 ORDER BY ID DESC LIMIT 1", 0);

                        $arr['Message'] = base64_encode_new("您是第" . $num . "次入场，上一次入场时间" . $list['CheckInedDate'] . ",当前场内人数" . $snum . " 入场失败人数" . $snumlose);
                    }
                    $arr['Success'] = '1';
                    $arr['Result'] = '';
                }
            } else {
                $arr = array('Success' => '0',
                    'Message' => base64_encode_new("操作失败"),
                );

                //失败日志
                $this->_dao->execute("insert into meeting_member_ruchang SET Cid='" . $meeting['ID'] . "',Mtid='" . $meetid . "',Mid='" . $meeting['Mid'] . "',CompfnameS='" . $meeting['compfname'] . "',PersonSign='" . $meeting['PersonSign'] . "',TrueName='" . $meeting['TrueName'] . "',Sex='" . $meeting['Sex'] . "',Post='" . $meeting['Post'] . "',ContactPhone='" . $meeting['ContactPhone'] . "',ContactMobile='" . $meeting['ContactMobile'] . "',CheckInedDate=NOW(),CheckInedUserName='" . $loginman['TrueName'] . "', CheckInedUserId='" . $loginman['Uid'] . "',RuType='" . $RuType . "',Status='9'");
            }
        }

        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    // 出场接口
    public function meet_ChuChang($params)
    {
        $GUID = $params['GUID'];
        $signcs = $params['SignCS'];
        $QRCode = $params['QRCode'];
        $RuType = $params['RuType'];
        $meetid = $params['ID'];

        $arr = array('Success' => '0',
            'Message' => 'null',
        );

        if ($signcs != '' && $GUID != '') {
            // $QRCode=substr($QRCode,0,-1);
            // 添加微信二维码判断
            //$data = explode("|" , $QRCode);
            $data = $this->decodeQRCode($QRCode);
            // $guid = $data[0];
            $mbid = $data[1];
            $maid = $data[2];
            $bzf = $data[3];
            $isen = $data[4];
            if (empty($isen)) $isen = 0;//添加了IsEn字段

            $user = $this->_dao->getRow("SELECT ID ,TrueName,PersonSign,Sex,Post,ContactPhone,ContactMobile,ContactFax,ContactEmail,Mtid,Maid,CheckInedDesc,HotelRoomNumber,IsTravel,TravelPersons,HotelDesc,TableNumber,PictureBig,PictureSmall,IsGuest,GuestType,IsCheckIned,IsGetZl,GUID from meeting_member_contactman where IsEn='$isen' and Mtid='$meetid' AND Maid='$maid' and status=1 ", 0);//添加了IsEn字段
            $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,Mid from meeting_member where Mid='$mbid' and Mtid='$meetid'", 0);
            $cangjia = $this->_dao->getRow("SELECT b.MeetingName,b.ChildMeetingName,b.ChildMeetingName2,b.ChildMeetingName3,b.ChildMeetingName4,b.ChildMeetingName5,b.ChildMeetingName6,b.ChildMeetingName7,b.ChildMeetingName8,b.ChildMeetingName9,b.ChildMeetingName10  from meeting_base as b,meeting_member_contactman as a where a.Mtid=b.ID and a.TrueName='" . $user['TrueName'] . "' and a.Mtid='" . $user['Mtid'] . "' and a.Mid='$mbid' and a.Mtid!=45  and a.status=1", 0);
            $user_1 = $this->_dao->getRow("SELECT IsMainMeeting,IsChildMeeting,IsChildMeeting2,IsChildMeeting3,IsChildMeeting4,IsChildMeeting5,IsChildMeeting6,IsChildMeeting7,IsChildMeeting8,IsChildMeeting9,IsChildMeeting10  from meeting_member_contactman where IsEn='$isen' and GUID='$guid' and status=1 limit 1 ", 0);//添加了IsEn字段
            $cangjia_1 = array();
            if ($user_1['IsMainMeeting'] == 1) {
                $cangjia_1['MeetingName'] = $cangjia['MeetingName'];
            }
            if ($user_1['IsChildMeeting'] == 1) {
                $cangjia_1['ChildMeetingName'] = $cangjia['ChildMeetingName'];
            }
            if ($user_1['IsChildMeeting2'] == 1) {
                $cangjia_1['ChildMeetingName2'] = $cangjia['ChildMeetingName2'];
            }
            if ($user_1['IsChildMeeting3'] == 1) {
                $cangjia_1['ChildMeetingName3'] = $cangjia['ChildMeetingName3'];
            }
            if ($user_1['IsChildMeeting4'] == 1) {
                $cangjia_1['ChildMeetingName4'] = $cangjia['ChildMeetingName4'];
            }
            if ($user_1['IsChildMeeting5'] == 1) {
                $cangjia_1['ChildMeetingName5'] = $cangjia['ChildMeetingName5'];
            }
            if ($user_1['IsChildMeeting6'] == 1) {
                $cangjia_1['ChildMeetingName6'] = $cangjia['ChildMeetingName6'];
            }
            if ($user_1['IsChildMeeting7'] == 1) {
                $cangjia_1['ChildMeetingName7'] = $cangjia['ChildMeetingName7'];
            }
            if ($user_1['IsChildMeeting8'] == 1) {
                $cangjia_1['ChildMeetingName8'] = $cangjia['ChildMeetingName8'];
            }
            if ($user_1['IsChildMeeting9'] == 1) {
                $cangjia_1['ChildMeetingName9'] = $cangjia['ChildMeetingName9'];
            }
            if ($user_1['IsChildMeeting10'] == 1) {
                $cangjia_1['ChildMeetingName10'] = $cangjia['ChildMeetingName10'];
            }
            $str = implode(",", $cangjia_1);
            $user['cangjia'] = $str;
            if ($meetid == '45') {
                $user['usertype'] = '钢之家员工';
            } elseif ($user['GuestType'] != 0) {
                $user['usertype'] = $GLOBALS['GuestType'][$user['GuestType']];
            } else {
                $user['usertype'] = '参会嘉宾';
            }
            $meeting = array_merge($user, $member);
            if ($meeting) {
                if ($RuType == 4) {
                    $RuName = $meeting['TableNumber'];
                } else {
                    $RuName = $GLOBALS['RuType'][$RuType];
                }

                $loginman = $this->_dao->getRow("SELECT * from meeting_app_session where GUID='$GUID' ", 0);
                $contactman = $this->_dao->getRow("SELECT Status from meeting_member_ruchang where Mtid='$meetid' AND Maid='$maid' and RuName='" . $RuName . "' order by CheckInedDate DESC limit 1", 0);
                if ($contactman['Status'] == 1 || empty($contactman)) {
                    $this->_dao->execute("insert into meeting_member_ruchang SET Cid='" . $meeting['ID'] . "',Mtid='" . $meeting['Mtid'] . "',Mid='" . $meeting['Mid'] . "',CompfnameS='" . $meeting['compfname'] . "',PersonSign='" . $meeting['PersonSign'] . "',TrueName='" . $meeting['TrueName'] . "',Sex='" . $meeting['Sex'] . "',Post='" . $meeting['Post'] . "',ContactPhone='" . $meeting['ContactPhone'] . "',ContactMobile='" . $meeting['ContactMobile'] . "',CheckInedDate=NOW(),CheckInedUserName='" . $loginman['TrueName'] . "', CheckInedUserId='" . $loginman['Uid'] . "',RuType='" . $RuType . "',Runame='" . $RuName . "',Status='2',GUID='" . $meeting['GUID'] . "' ;");
                    if ($meeting['IsGuest'] == 0) {
                        $arr['Message'] = base64_encode_new('参会嘉宾出场');
                    } elseif ($meeting['IsGuest'] == 1) {
                        $arr['Message'] = base64_encode_new('参会重要嘉宾出场');
                    }
                } elseif ($contactman['Status'] == 2) {
                    if ($meeting['IsGuest'] == 0) {
                        $arr['Message'] = base64_encode_new('参会嘉宾已经出场');
                    } elseif ($meeting['IsGuest'] == 1) {
                        $arr['Message'] = base64_encode_new('参会重要嘉宾已经出场');
                    }
                }
                $meeting['TrueName'] = base64_encode_new($meeting['TrueName']);
                $meeting['Sex'] = base64_encode_new($meeting['Sex']);
                $meeting['Post'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($meeting['Post'])));
                $meeting['CheckInedDesc'] = base64_encode_new($meeting['CheckInedDesc']);
                $meeting['HotelDesc'] = base64_encode_new($meeting['HotelDesc']);
                $meeting['compfname'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($meeting['compfname'])));
                $meeting['compabb'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($meeting['compabb'])));
                $meeting['cangjia'] = base64_encode_new($meeting['cangjia']);
                $meeting['usertype'] = base64_encode_new($meeting['usertype']);
                $meeting['comtype'] = base64_encode_new($meeting['comtype']);
                $meeting['PictureBig'] = $meeting['PictureBig'] == '' ? STEELHOME_SITE : ((strstr($meeting['PictureBig'], "http://")||strstr($meeting['PictureBig'], "https://")) ? $meeting['PictureBig'] : (strstr($meeting['PictureBig'], "//") ? 'http:' . $meeting['PictureBig'] : STEELHOME_SITE . $meeting['PictureBig']));
                $meeting['PictureSmall'] = $meeting['PictureSmall'] == '' ? STEELHOME_SITE : ((strstr($meeting['PictureBig'], "http://")||strstr($meeting['PictureSmall'], "https://")) ? $meeting['PictureSmall'] : (strstr($meeting['PictureSmall'], "//") ? 'http:' . $meeting['PictureSmall'] : STEELHOME_SITE . $meeting['PictureSmall']));
                $meeting['PictureBig'] = str_replace("http:////", "http://", $meeting['PictureBig']);
                $meeting['PictureSmall'] = str_replace("http:////", "http://", $meeting['PictureSmall']);
                $meeting['PictureBig'] = str_replace("http://", "https://", $meeting['PictureBig']);
                $meeting['PictureSmall'] = str_replace("http://", "https://", $meeting['PictureSmall']);
                $meeting['PictureBig'] = str_replace("http:https", "https", $meeting['PictureBig']);
                $meeting['PictureSmall'] = str_replace("http:https", "https", $meeting['PictureSmall']);
                // print_r($meeting);
                $arr['Success'] = '1';
                // $arr['Message']= base64_encode_new('出场参会嘉宾信息');
                $arr['Result'] = $meeting;
            } else {
                $arr = array('Success' => '0',
                    'Message' => 'null',
                );
            }
        }

        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    // 出场接口
    public function meet_ChuChang2($params)
    {
        $GUID = $params['GUID'];
        $signcs = $params['SignCS'];
        $QRCode = base64_decode($params['QRCode']);
        $RuType = $params['RuType'];
        $meetid = $params['ID'];

        $arr = array('Success' => '0',
            'Message' => base64_encode_new('操作失败'),
        );

        if ($signcs != '' && $GUID != '') {
            // 添加微信二维码判断
            //$data = explode("|" , $QRCode);
            $data = $this->decodeQRCode($QRCode);
            // $guid = $data[0];
            $mbid = $data[1];
            $maid = $data[2];
            $bzf = $data[3];
            $isen = $data[4];
            if (empty($isen)) $isen = 0;//判断IsEn字段

            $user = $this->_dao->getRow("SELECT * from meeting_member_contactman where  IsEn='$isen' and Mtid='$meetid' AND Maid='$maid' and status=1 ", 0);//判断IsEn字段
            if ($isen != "1") {
                $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,Mid from meeting_member where Mid='$mbid' and Mtid='$meetid'", 0);
            } else {
                $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,MidEn as Mid from meeting_member where MidEn='" . $user['MidEn'] . "' andMtid='$meetid'", 0);
            }
            $cangjia = $this->_dao->getRow("SELECT b.MeetingName,b.ChildMeetingName,b.ChildMeetingName2,b.ChildMeetingName3,b.ChildMeetingName4,b.ChildMeetingName5,b.ChildMeetingName6,b.ChildMeetingName7,b.ChildMeetingName8,b.ChildMeetingName9,b.ChildMeetingName10  from meeting_base as b,meeting_member_contactman as a where a.Mtid=b.ID and a.TrueName='" . $user['TrueName'] . "' and a.Mtid='" . $user['Mtid'] . "' and a.Mid='$mbid' and a.Mtid!=45 and a.status=1", 0);

            $meeting = array_merge($user, $member);

            if (!empty($meeting)) {
                if ($RuType == 4) {
                    $RuName = $meeting['TableNumber'];
                } else {
                    $RuName = $GLOBALS['RuType'][$RuType];
                }

                $loginman = $this->_dao->getRow("SELECT * from meeting_app_session where GUID='$GUID' ", 0);

                $contactman = $this->_dao->getRow("SELECT Status from meeting_member_ruchang where Mtid='$meetid' AND Maid='$maid' and RuName='" . $RuName . "' order by CheckInedDate DESC limit 1", 0);

                if ($contactman['Status'] == 1 || empty($contactman)) {
                    $this->_dao->execute("insert into meeting_member_ruchang SET Cid='" . $meeting['ID'] . "',Mtid='" . $meeting['Mtid'] . "',Mid='" . $meeting['Mid'] . "',CompfnameS='" . $meeting['compfname'] . "',PersonSign='" . $meeting['PersonSign'] . "',TrueName='" . $meeting['TrueName'] . "',Sex='" . $meeting['Sex'] . "',Post='" . $meeting['Post'] . "',ContactPhone='" . $meeting['ContactPhone'] . "',ContactMobile='" . $meeting['ContactMobile'] . "',CheckInedDate=NOW(),CheckInedUserName='" . $loginman['TrueName'] . "', CheckInedUserId='" . $loginman['Uid'] . "',RuType='" . $RuType . "',Runame='" . $RuName . "',Status='2',GUID='" . $meeting['GUID'] . "' ;");

                    if ($meeting['IsGuest'] == 0) {
                        $arr['Message'] = base64_encode_new('参会嘉宾出场');
                    } elseif ($meeting['IsGuest'] == 1) {
                        $arr['Message'] = base64_encode_new('参会重要嘉宾出场');
                    }
                } elseif ($contactman['Status'] == 2) {
                    if ($meeting['IsGuest'] == 0) {
                        $arr['Message'] = base64_encode_new('参会嘉宾已经出场');
                    } elseif ($meeting['IsGuest'] == 1) {
                        $arr['Message'] = base64_encode_new('参会重要嘉宾已经出场');
                    }
                }

                $arr['Success'] = '1';
                // $arr['Message']= base64_encode_new('出场参会嘉宾信息');
                $arr['Result'] = '';
            } else {
                $arr = array('Success' => '0',
                    'Message' => base64_encode_new('操作失败'),
                );
            }
        }

        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    // 现场报到
    public function meet_QRBaoDao($params)
    {
        $GUID = $params['GUID'];
        $signcs = $params['SignCS'];
        $QRCode = $params['QRCode'];
        $meetid = $params['ID'];

        $arr = array('Success' => '0',
            'Message' => base64_encode_new('操作失败'),
        );
        if ($signcs != '' && $GUID != '') {
            // $QRCode=substr($QRCode,0,-1);
            // 添加微信二维码判断
            //$data = explode("|" , $QRCode);
            $data = $this->decodeQRCode($QRCode);
            // $guid = $data[0];
            $mbid = $data[1];
            $maid = $data[2];
            $bzf = $data[3];
            $isen = $data[4];
            if (empty($isen)) $isen = 0;//添加IsEn字段

            if ($meetid != '45') {
                $user = $this->_dao->getRow("SELECT TrueName,Sex,Post,ContactPhone,ContactMobile,ContactFax,ContactEmail,Mtid,Maid,CheckInedDesc,CheckInedDate,HotelRoomNumber,IsTravel,IsGuest,GuestType,TravelPersons,HotelDesc,PictureBig,TableNumber,PictureSmall,IsCheckIned,IsGetZl from meeting_member_contactman where IsEn='$isen' and Mtid='$meetid' AND Maid='$maid' and status=1", 0);//添加IsEn字段

                $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,Mid from meeting_member where Mid='$mbid' and Mtid='$meetid'", 0);
                $cangjia = $this->_dao->getRow("SELECT b.MeetingName,b.ChildMeetingName,b.ChildMeetingName2,b.ChildMeetingName3,b.ChildMeetingName4,b.ChildMeetingName5,b.ChildMeetingName6,b.ChildMeetingName7,b.ChildMeetingName8,b.ChildMeetingName9,b.ChildMeetingName10  from meeting_base as b,meeting_member_contactman as a where a.Mtid=b.ID and a.TrueName='" . $user['TrueName'] . "' and a.Mtid='" . $user['Mtid'] . "' and a.Mid='$mbid' and a.Mtid!=45 and a.status=1", 0);
                $user_1 = $this->_dao->getRow("SELECT IsMainMeeting,IsChildMeeting,IsChildMeeting2,IsChildMeeting3,IsChildMeeting4,IsChildMeeting5,IsChildMeeting6,IsChildMeeting7,IsChildMeeting8,IsChildMeeting9,IsChildMeeting10  from meeting_member_contactman where IsEn='$isen' and Mtid='$meetid' AND Maid='$maid' and status=1 limit 1 ", 0); //添加IsEn字段
                // 已报到人数
                $num = $this->_dao->getOne("SELECT count(*) as c FROM meeting_member_contactman WHERE IsEn='$isen' and Mtid='" . $meetid . "' AND IsCheckIned=1 and status=1 "); //添加IsEn字段
                // $user['TotalNum'] = $num;
                $cangjia_1 = array();
                if ($user_1['IsMainMeeting'] == 1) {
                    $cangjia_1['MeetingName'] = $cangjia['MeetingName'];
                }
                if ($user_1['IsChildMeeting'] == 1) {
                    $cangjia_1['ChildMeetingName'] = $cangjia['ChildMeetingName'];
                }
                if ($user_1['IsChildMeeting2'] == 1) {
                    $cangjia_1['ChildMeetingName2'] = $cangjia['ChildMeetingName2'];
                }
                if ($user_1['IsChildMeeting3'] == 1) {
                    $cangjia_1['ChildMeetingName3'] = $cangjia['ChildMeetingName3'];
                }
                if ($user_1['IsChildMeeting4'] == 1) {
                    $cangjia_1['ChildMeetingName4'] = $cangjia['ChildMeetingName4'];
                }
                if ($user_1['IsChildMeeting5'] == 1) {
                    $cangjia_1['ChildMeetingName5'] = $cangjia['ChildMeetingName5'];
                }
                if ($user_1['IsChildMeeting6'] == 1) {
                    $cangjia_1['ChildMeetingName6'] = $cangjia['ChildMeetingName6'];
                }
                if ($user_1['IsChildMeeting7'] == 1) {
                    $cangjia_1['ChildMeetingName7'] = $cangjia['ChildMeetingName7'];
                }
                if ($user_1['IsChildMeeting8'] == 1) {
                    $cangjia_1['ChildMeetingName8'] = $cangjia['ChildMeetingName8'];
                }
                if ($user_1['IsChildMeeting9'] == 1) {
                    $cangjia_1['ChildMeetingName9'] = $cangjia['ChildMeetingName9'];
                }
                if ($user_1['IsChildMeeting10'] == 1) {
                    $cangjia_1['ChildMeetingName10'] = $cangjia['ChildMeetingName10'];
                }
                $str = implode(",", $cangjia_1);
                $user['cangjia'] = $str;
                if ($meetid == '45') {
                    $user['usertype'] = '钢之家员工';
                } elseif ($user['GuestType'] != 0) {
                    $user['usertype'] = $GLOBALS['GuestType'][$user['GuestType']];
                } else {
                    $user['usertype'] = '参会嘉宾';
                }
                $meeting = array_merge($user, $member);

                if ($user['IsCheckIned'] == 1) {
                    $meeting['TrueName'] = base64_encode_new($meeting['TrueName']);
                    $meeting['Sex'] = base64_encode_new($meeting['Sex']);
                    $meeting['Post'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($meeting['Post'])));
                    $meeting['CheckInedDesc'] = base64_encode_new($meeting['CheckInedDesc']);
                    $meeting['HotelDesc'] = base64_encode_new($meeting['HotelDesc']);
                    $meeting['compfname'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($meeting['compfname'])));
                    $meeting['compabb'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($meeting['compabb'])));
                    $meeting['usertype'] = base64_encode_new($meeting['usertype']);
                    $meeting['cangjia'] = base64_encode_new($meeting['cangjia']);
                    $meeting['comtype'] = base64_encode_new($meeting['comtype']);
                    $meeting['PictureBig'] = $meeting['PictureBig'] == '' ? STEELHOME_SITE : ((strstr($meeting['PictureBig'], "http://")||strstr($meeting['PictureBig'], "https://")) ? $meeting['PictureBig'] : (strstr($meeting['PictureBig'], "//") ? 'http:' . $meeting['PictureBig'] : STEELHOME_SITE . $meeting['PictureBig']));
                    $meeting['PictureSmall'] = $meeting['PictureSmall'] == '' ? STEELHOME_SITE : ((strstr($meeting['PictureBig'], "http://")||strstr($meeting['PictureSmall'], "https://")) ? $meeting['PictureSmall'] : (strstr($meeting['PictureSmall'], "//") ? 'http:' . $meeting['PictureSmall'] : STEELHOME_SITE . $meeting['PictureSmall']));
                    $meeting['PictureBig'] = str_replace("http:////", "http://", $meeting['PictureBig']);
                    $meeting['PictureSmall'] = str_replace("http:////", "http://", $meeting['PictureSmall']);
                    $meeting['PictureBig'] = str_replace("http://", "https://", $meeting['PictureBig']);
                    $meeting['PictureSmall'] = str_replace("http://", "https://", $meeting['PictureSmall']);
                    $meeting['PictureBig'] = str_replace("http:https", "https", $meeting['PictureBig']);
                    $meeting['PictureSmall'] = str_replace("http:https", "https", $meeting['PictureSmall']);

                    $meeting['TotalNum'] = $num;

                    if ($meeting['IsGuest'] == 0) {
                        $arr['Success'] = '1';
                        $arr['Message'] = base64_encode_new('该参会嘉宾已于' . $meeting['CheckInedDate'] . '报到');

                        $arr['Result'] = $meeting;
                    } elseif ($meeting['IsGuest'] == 1) {
                        $arr['Success'] = '1';
                        $arr['Message'] = base64_encode_new('该参会重要嘉宾已于' . $meeting['CheckInedDate'] . '报到');

                        $arr['Result'] = $meeting;
                    }
                } else {
                    if ($meeting) {
                        $loginman = $this->_dao->getRow("SELECT * from meeting_app_session where GUID='$GUID' ", 0);
                        $user['TrueName'] = str_replace("'", "\'", $user['TrueName']);
                        // 备份记录
                        $this->_dao->execute("Insert Into meeting_member_contactman_h select * from meeting_member_contactman WHERE  Mtid='$meetid' AND Maid='$maid' and status=1 ");

                        $this->_dao->execute("UPDATE meeting_member_contactman SET IsCheckIned='1',IsCheckInXk='1',IsCheckInZl='1', CheckInedDate=NOW(),CheckInedUserName='" . $loginman['TrueName'] . "', CheckInedUserId='" . $loginman['Uid'] . "' WHERE Mtid='$meetid' AND Maid='$maid' ");
                        if ($meeting['IsGuest'] == 0) {
                            $logtitle = "嘉宾报到";
                            $log = "报到操作人：" . $loginman['TrueName'] . "日期：" . date('Y-m-d H:i:s') . "增加" . $member['compfname'] . "的嘉宾" . $user['TrueName'] . "报到信息";
                        } elseif ($meeting['IsGuest'] == 1) {
                            $logtitle = "重要嘉宾报到";
                            $log = "报到操作人：" . $loginman['TrueName'] . "日期：" . date('Y-m-d H:i:s') . "增加" . $member['compfname'] . "的重要嘉宾" . $user['TrueName'] . "报到信息";
                        }

                        $ip = $this->_dao->getip();

                        $this->_dao->execute("insert into  meeting_logs SET Mtid='" . $meetid . "',LogsType='3',LogsTitle='" . $logtitle . "',LogsMid='" . $member['Mid'] . "',UserId='" . $loginman['Uid'] . "',UserName='" . $loginman['TrueName'] . "', UserIp='" . $ip . "',OperLog='" . $log . "',OperDate=NOW() ;");

                        $meeting['TrueName'] = base64_encode_new($meeting['TrueName']);
                        $meeting['Sex'] = base64_encode_new($meeting['Sex']);
                        $meeting['Post'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($meeting['Post'])));
                        $meeting['CheckInedDesc'] = base64_encode_new($meeting['CheckInedDesc']);
                        $meeting['HotelDesc'] = base64_encode_new($meeting['HotelDesc']);
                        $meeting['usertype'] = base64_encode_new($meeting['usertype']);
                        $meeting['compfname'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($meeting['compfname'])));
                        $meeting['cangjia'] = base64_encode_new($meeting['cangjia']);
                        $meeting['compabb'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($meeting['compabb'])));
                        $meeting['comtype'] = base64_encode_new($meeting['comtype']);
                        $meeting['PictureBig'] = $meeting['PictureBig'] == '' ? STEELHOME_SITE : ((strstr($meeting['PictureBig'], "http://")||strstr($meeting['PictureBig'], "https://")) ? $meeting['PictureBig'] : (strstr($meeting['PictureBig'], "//") ? 'http:' . $meeting['PictureBig'] : STEELHOME_SITE . $meeting['PictureBig']));
                        $meeting['PictureSmall'] = $meeting['PictureSmall'] == '' ? STEELHOME_SITE : ((strstr($meeting['PictureBig'], "http://")||strstr($meeting['PictureSmall'], "https://")) ? $meeting['PictureSmall'] : (strstr($meeting['PictureSmall'], "//") ? 'http:' . $meeting['PictureSmall'] : STEELHOME_SITE . $meeting['PictureSmall']));
                        $meeting['PictureBig'] = str_replace("http:////", "http://", $meeting['PictureBig']);
                        $meeting['PictureSmall'] = str_replace("http:////", "http://", $meeting['PictureSmall']);
                        $meeting['PictureBig'] = str_replace("http://", "https://", $meeting['PictureBig']);
                        $meeting['PictureSmall'] = str_replace("http://", "https://", $meeting['PictureSmall']);
                        $meeting['PictureBig'] = str_replace("http:https", "https", $meeting['PictureBig']);
                        $meeting['PictureSmall'] = str_replace("http:https", "https", $meeting['PictureSmall']);
                        $meeting['TotalNum'] = $num;
                        // print_r($meeting);
                        $arr['Success'] = '1';
                        if ($meeting['IsGuest'] == 0) {
                            $arr['Message'] = base64_encode_new('参会嘉宾报到成功');
                        } elseif ($meeting['IsGuest'] == 1) {
                            $arr['Message'] = base64_encode_new('参会重要嘉宾报到成功');
                        }
                        $arr['Result'] = $meeting;
                    } else {
                        $arr = array('Success' => '0',
                            'Message' => base64_encode_new('操作失败2'),
                        );
                    }
                } //$user['IsCheckIned']==1
            } else // $meetid==$meet && $meet!=45
            {
                $arr['Success'] = '0';
                if ($meetid == '45') {
                    $arr['Message'] = base64_encode_new('钢之家员工不可参与报到');
                }
                // elseif ($meetid != $meet)
                // {
                // $arr['Message'] = base64_encode_new('会议不匹配');
                // }
            }
        }

        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    public function meet_QRBaoDao2($params)
    {
        // start
        $GUID = $params['GUID'];
        $signcs = $params['SignCS'];
        $QRCode = base64_decode($params['QRCode']);
        $meetid = $params['ID'];
        $BHcode = $params['BHcode'];

        $arr = array('Success' => '0',
            'Message' => 'null',
        );

        $arrmem = array("TrueName", "PersonSign", "Sex", "Post", "ContactPhone", "ContactMobile", "ContactFax", "ContactEmail", "Mtid", "Maid", "CheckInedDesc", "HotelRoomNumber", "IsTravel", "TravelPersons", "HotelDesc", "PictureBig", "PictureSmall", "GuestType", "TableNumber", "IsGuest", "IsCheckIned", "IsGetZl", "compfname", "compabb", "comtype", "Mid", "usertype", "IsEn");

        if ($signcs != '' && $GUID != '') {
            if (!empty($BHcode)) {
                $Code = $BHcode;
                $user = $this->_dao->getRow("select * from meeting_member_contactman where PersonSign='$BHcode' and Mtid = '$meetid' and status=1 limit 1", 0);
                $isen = $user['IsEn'];
                if ($user['IsEn'] == "1") {
                    $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,MidEn as Mid ,UserID,DepartID from meeting_member where MidEn='" . $user['Mid'] . "' and Mtid='$meetid'", 0);
                } else {
                    $member = $this->_dao->getRow("SELECT compfname,compabb,comtype, Mid,UserID,DepartID from meeting_member where   Mid='" . $user['Mid'] . "' and Mtid='$meetid'", 0);
                }

            } else {
                $Code = $QRCode;
                // 添加微信二维码判断
                //$data = explode("|" , $QRCode);
                $data = $this->decodeQRCode($QRCode);
                $guid = $data[0];
                $mbid = $data[1];
                $maid = $data[2];
                $bzf = $data[3];
                $isen = $data[4];
                if (empty($isen)) $isen = 0;//添加IsEn字段

                $user = $this->_dao->getRow("select * from meeting_member_contactman where IsEn='$isen' and Maid='$maid' and Mtid = '$meetid' and status=1 limit 1", 0);//添加IsEn字段
                if ($isen != "1") {
                    $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,Mid,UserID,DepartID from meeting_member where Mid='" . $user['Mid'] . "' and Mtid='$meetid'", 0);
                } else {
                    $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,MidEn as Mid,UserID,DepartID from meeting_member where MidEn='" . $user['Mid'] . "' and Mtid='" . $user['Mtid'] . "'", 0);
                }

            }

            if ($user['IsCheckIned'] == 1) // 已报到
            {
                if ($user['IsGuest'] == 0) {
                    $arr['Success'] = '0';
                    $arr['Message'] = base64_encode_new('该参会嘉宾已于' . $user['CheckInedDate'] . '报到');
                    $arr['IsEn'] = $user['IsEn'];
                    $arr['Result'] = ""; //$aa;
                } elseif ($user['IsGuest'] == 1) {
                    $arr['Success'] = '0';
                    $arr['Message'] = base64_encode_new('该参会重要嘉宾已于' . $user['CheckInedDate'] . '报到');
                    $arr['IsEn'] = $user['IsEn'];
                    $arr['Result'] = "";
                }
            } else // 未报到
            {
                if ($user) {
                    $loginman = $this->_dao->getRow("SELECT * from meeting_app_session where GUID='$GUID' ", 0);
                    $user['TrueName'] = str_replace("'", "\'", $user['TrueName']);
                    // 备份记录
                    //$this->_dao->execute("Insert Into meeting_member_contactman_h select * from meeting_member_contactman WHERE  IsEn='$isen' and Mtid='$meetid' AND Maid='" . $user['Maid'] . "' and status=1 ");//添加IsEn字段
                    $this->_dao->execute("Insert Into meeting_member_contactman_h select * from meeting_member_contactman WHERE  ID='".$user['ID']."'");//添加IsEn字段

                    //$this->_dao->execute("UPDATE meeting_member_contactman SET IsCheckIned='1',IsCheckInXk='1',IsCheckInZl='1', CheckInedDate=NOW(),CheckInedUserName='" . $loginman['TrueName'] . "', CheckInedUserId='" . $loginman['Uid'] . "' WHERE IsEn='$isen' and Mtid='$meetid' AND Maid='" . $user['Maid'] . "' and status=1");//添加IsEn字段
                    $this->_dao->execute("UPDATE meeting_member_contactman SET IsCheckIned='1',IsCheckInXk='1',IsCheckInZl='1', CheckInedDate=NOW(),CheckInedUserName='" . $loginman['TrueName'] . "', CheckInedUserId='" . $loginman['Uid'] . "' WHERE ID='".$user['ID']."'");//添加IsEn字段
                    if ($user['IsGuest'] == 0) {
                        $logtitle = "嘉宾报到";
                        $log = "报到操作人：" . $loginman['TrueName'] . "日期：" . date('Y-m-d H:i:s') . "增加" . $member['compfname'] . "的嘉宾" . $user['TrueName'] . "报到信息";
                        
                        $message="嘉宾报到:\r\n".$member['compfname'].$user['TrueName'].'于'.date('Y-m-d H:i:s')."已报到";
                        if($meetid=="407"||$meetid==407)
                        {
                            $this->send_xwork_message($member, $message);
                        }
                        
                    } elseif ($user['IsGuest'] == 1) {
                        $logtitle = "重要嘉宾报到";
                        $log = "报到操作人：" . $loginman['TrueName'] . "日期：" . date('Y-m-d H:i:s') . "增加" . $member['compfname'] . "的重要嘉宾" . $user['TrueName'] . "报到信息";
                        $message="重要嘉宾报到:\r\n".$member['compfname'].$user['TrueName'].'于'.date('Y-m-d H:i:s')."已报到";
                        if($meetid=="407"||$meetid==407)
                        {
                            $this->send_xwork_message($member, $message);
                        }
                    }

                    $ip = $this->_dao->getip();

                    $this->_dao->execute("insert into  meeting_logs SET Mtid='" . $meetid . "',LogsType='3',LogsTitle='" . $logtitle . "',LogsMid='" . $member['Mid'] . "',UserId='" . $loginman['Uid'] . "',UserName='" . $loginman['TrueName'] . "', UserIp='" . $ip . "',OperLog='" . $log . "',OperDate=NOW() ;");

                    //获取已报到人数
                    //$snum = $this->_dao->getOne("select count(*) from meeting_member_contactman where IsEn='$isen' and IsCheckIned='1' and Mtid='$meetid'",0);//添加IsEn字段
                    $snum = $this->_dao->getOne("select count(*) from meeting_member_contactman where  IsCheckIned='1' and Mtid='$meetid' and status=1 ", 0);//添加IsEn字段

                    $arr['Success'] = '1';
                    if ($user['IsGuest'] == 0) {
                        $arr['Message'] = base64_encode_new('参会嘉宾报到成功,当前已报到人数' . $snum);
                        $arr['IsEn'] = $user['IsEn'];
                    } elseif ($user['IsGuest'] == 1) {
                        $arr['Message'] = base64_encode_new('参会重要嘉宾报到成功,当前已报到人数' . $snum);
                        $arr['IsEn'] = $user['IsEn'];
                    }

                    $arr['Result'] = "";
                } else {
                    $arr = array('Success' => '0',
                        'Message' => base64_encode_new('操作失败'),
                    );
                }
            }
        } else {
            $arr = array('Success' => '0',
                'Message' => base64_encode_new('非法操作'),
            );
        }

        $json_string = $this->pri_JSON($arr);
        echo $json_string;
        
        // end
    }

    //旅游登记接口
    public function meet_QRLvYouDj($params)
    {
        $GUID = $params['GUID'];
        $signcs = $params['SignCS'];
        $QRCode = base64_decode($params['QRCode']);
        $BHcode = $params['BHcode'];
        $meetid = $params['ID'];
        $type = $params['type']; //旅游取消和登记，登记还要记录身份证号，1表示登记
        $people = $params['people'];
        $idcard = $params['idcard'];
        $namelist = base64_decode_new(str_replace(' ', '+', $params['namelist']));
        $idcardlist = base64_decode_new(str_replace(' ', '+', $params['idcardlist']));

        $arr = array('Success' => '0',
            'Message' => 'null',
        );
        $meeting_base = $this->_dao->getRow( "SELECT fzjldeadline FROM meeting_base WHERE id = $meetid ");

        $tmes = $this->_dao->query("select TypeName,TypeValue from meeting_set where mtid='$meetid' and SetType=4 and Status=1 "); //print_r($tmes);exit;
        foreach ($tmes as $id => $ar) {

            if ($ar['TypeName'] == "否") {
                $travel[0] = $ar['TypeValue'];
            }
            if ($ar['TypeName'] == "是") {
                $travel[1] = $ar['TypeValue'];
            }
            if ($ar['TypeName'] == "待定") {
                $travel[2] = $ar['TypeValue'];
            }
        }
        //print_r($travel);exit;
        if ($signcs != '' && $GUID != '' && $type != '') {
            if ($QRCode) {
                $data = $this->decodeQRCode($QRCode);
                $mbid = $data[1] ? $data[1] : 0;
                $maid = $data[2] ? $data[2] : 0;
                $bzf = $data[3] ? $data[3] : 0;
                $isen = $data[4] ? $data[4] : 0;//添加IsEn字段
                if (empty($isen)) $isen = 0;
                //print"<pre>";print_r($data);print"</pre>";
            }
            if ($meetid != 45) {
                if ($QRCode && !$BHcode) {
                    $isexist = $this->_dao->getRow("SELECT Mid,Maid,ID,CompfnameS,TrueName,IsTravel,idcard,tralist,idcard1 from meeting_member_contactman where    Mtid=$meetid and maid=$maid and status=1 ", 0);//添加IsEn字段
                } elseif (!$QRCode && $BHcode) {
                    $isexist = $this->_dao->getRow("SELECT Mid,Maid,ID,CompfnameS,TrueName,IsTravel,idcard,tralist,idcard1 from meeting_member_contactman where    Mtid=$meetid and PersonSign='$BHcode' and status=1 ", 0);//添加IsEn字段
                } else {
                    $arr['Message'] = base64_encode_new("系统出错（1）！");
                }
                if ($isexist) {
                    $name = $this->_dao->getOne("SELECT TrueName from meeting_member_contactman where    Mtid=$meetid and Mid='" . $isexist['Mid'] . "' and Maid='" . $isexist['Maid'] . "' and status=1 ", 0);//添加IsEm字段
                    if ($type == 'dj') {
                        if ($QRCode && !$BHcode) {            // 二维码
                            $this->_dao->execute("update meeting_member_contactman set TravelOpTime=NOW(),istravel='" . $travel[1] . "',TravelPersons='$people',idcard='$idcard',tralist='$namelist',idcard1='$idcardlist' where Mtid=$meetid and Mid='" . $isexist['Mid'] . "'  and maid='" . $isexist['Maid'] . "' limit 1");
                            $arr['Message'] = base64_encode_new('' . $name . '分组交流登记成功');
                            $arr['Success'] = '1';
                        } elseif (!$QRCode && $BHcode) {        // 参会编号
                            $this->_dao->execute("update meeting_member_contactman set TravelOpTime=NOW(),istravel='" . $travel[1] . "',TravelPersons='$people',idcard='$idcard',tralist='$namelist',idcard1='$idcardlist' where Mtid='$meetid' and PersonSign='$BHcode' limit 1");
                            $arr['Message'] = base64_encode_new('' . $name . '分组交流登记成功');
                            $arr['Success'] = '1';
                        } else {
                            $arr['Message'] = base64_encode_new("系统出错（2）！");
                        }
                    } elseif ($type == 'qx') {
                        if ($QRCode && !$BHcode) {
                            $this->_dao->execute("update meeting_member_contactman set TravelOpTime=NOW(),IsTravel='" . $travel[0] . "',TravelPersons=0 where Mtid=$meetid and Mid='" . $isexist['Mid'] . "' and maid='" . $isexist['Maid'] . "' limit 1");
                            $arr['Message'] = base64_encode_new('' . $name . '取消登记成功');
                            $arr['Success'] = '1';
                        } elseif (!$QRCode && $BHcode) {
                            $this->_dao->execute("update meeting_member_contactman set TravelOpTime=NOW(),istravel='" . $travel[0] . "',TravelPersons=0 where  Mtid=$meetid and PersonSign='$BHcode' limit 1");
                            $arr['Message'] = base64_encode_new('' . $name . '取消登记成功');
                            $arr['Success'] = '1';
                        } else {
                            $arr['Message'] = base64_encode_new("系统出错（3）！");
                        }
                    } elseif ($type == 'dd') {
                        if ($QRCode && !$BHcode) {
                            $this->_dao->execute("update meeting_member_contactman set TravelOpTime=NOW(),IsTravel='" . $travel[2] . "',TravelPersons=0 where Mtid=$meetid and Mid='" . $isexist['Mid'] . "' and maid='" . $isexist['Maid'] . "' limit 1");
                            $arr['Message'] = base64_encode_new('' . $name . '待定分组交流成功');
                            $arr['Success'] = '1';
                        } elseif (!$QRCode && $BHcode) {
                            $this->_dao->execute("update meeting_member_contactman set TravelOpTime=NOW(),istravel='" . $travel[2] . "',TravelPersons=0 where  Mtid=$meetid and PersonSign='$BHcode' limit 1");
                            $arr['Message'] = base64_encode_new('' . $name . '待定分组交流成功');
                            $arr['Success'] = '1';
                        } else {
                            $arr['Message'] = base64_encode_new("系统出错（4）！");
                        }
                    } else {
                        $arr['Message'] = base64_encode_new("非法操作！");
                    }
                    if($meeting_base['fzjldeadline']!="0000-00-00 00:00:00"&&time()>strtotime($meeting_base['fzjldeadline']))
                    {
                        $loginman = $this->_dao->getRow("SELECT * from meeting_app_session where GUID='$GUID' ", 0);
                         $content="";
                         if($isexist['IsTravel']!='1'&&$type == 'dj')
                         {
                             $title=$isexist['CompfnameS'].$isexist['TrueName']."新增分组交流信息，操作人".$loginman['TrueName'];
                             $content=$title;
                         }
                         else if($isexist['IsTravel']=='1'&&$type != 'dj')
                         {
                             $title=$isexist['CompfnameS'].$isexist['TrueName']."取消分组交流信息，操作人".$loginman['TrueName'];
                             $content=$title;
                         }
                         else if($isexist['IsTravel']=='1'&&$type == 'dj')
                         {
                             $bdxxarr=array();
                             if($isexist['idcard']!=$idcard)
                             {
                                 $bdxxarr[]="嘉宾身份证号".$isexist['idcard']."变更为".$idcard;
                             }
                             if($isexist['tralist']!=$namelist)
                             {
                                 $bdxxarr[]="同行人姓名".$isexist['tralist']."变更为".$namelist;
                             }
                             if($isexist['idcard1']!=$idcardlist)
                             {
                                 $bdxxarr[]="同行人身份证号".$isexist['idcard1']."变更为".$idcardlist;
                             }
                             if(!empty($bdxxarr))
                             {
                                 $title=$isexist['CompfnameS'].$isexist['TrueName']."更新分组交流信息，操作人".$loginman['TrueName'];
                                 $content=implode(',',$bdxxarr);
                             }
                             
                         }
                         if(!empty($content))
                         {
                             $this->_dao->write_meeting_active_log2($meetid,$isexist['ID'], $content,$title,7,$loginman);
                         }     
                    }
                                   
                } else {
                    $arr = array(
                        'Success' => '0',
                        'Message' => base64_encode_new('未找到该参会嘉宾！'),
                    );
                }
            }
        } else {
            $arr['Message'] = base64_encode_new("非法操作！");
        }
        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    // 资料领取接口
    public function meet_QRZiliaoLq($params)
    {
        $GUID = $params['GUID'];
        $signcs = $params['SignCS'];
        $QRCode = $params['QRCode'];
        $meetid = $params['ID'];

        $arr = array('Success' => '0',
            'Message' => 'null',
        );
        if ($signcs != '' && $GUID != '') {
            // $QRCode=substr($QRCode,0,-1);
            // 添加微信二维码判断
            //$data = explode("|" , $QRCode);
            $data = $this->decodeQRCode($QRCode);
            // $guid = $data[0];
            $mbid = $data[1];
            $maid = $data[2];
            $bzf = $data[3];

            if ($meetid != 45) {
                $user = $this->_dao->getRow("SELECT TrueName,Sex,Post,ContactPhone,ContactMobile,ContactFax,ContactEmail,Mtid,Maid,CheckInedDesc,CheckInedDate,HotelRoomNumber,IsTravel,TravelPersons,HotelDesc,IsGuest,GuestType,PictureBig,TableNumber,PictureSmall,IsCheckIned,IsGetZl from meeting_member_contactman where GUID='$guid' and status=1", 0);
                $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,Mid from meeting_member where Mid='$mbid' and Mtid='$meetid' ", 0);
                $cangjia = $this->_dao->getRow("SELECT b.MeetingName,b.ChildMeetingName,b.ChildMeetingName2,b.ChildMeetingName3,b.ChildMeetingName4,b.ChildMeetingName5,b.ChildMeetingName6,b.ChildMeetingName7,b.ChildMeetingName8,b.ChildMeetingName9,b.ChildMeetingName10  from meeting_base as b,meeting_member_contactman as a where a.Mtid=b.ID and a.TrueName='" . $user['TrueName'] . "' and a.Mtid='" . $user['Mtid'] . "' and a.Mid='$mbid' and a.Mtid!=45 and a.status=1", 0);
                $user_1 = $this->_dao->getRow("SELECT IsMainMeeting,IsChildMeeting,IsChildMeeting2,IsChildMeeting3,IsChildMeeting4,IsChildMeeting5,IsChildMeeting6,IsChildMeeting7,IsChildMeeting8,IsChildMeeting9,IsChildMeeting10  from meeting_member_contactman where  GUID='$guid' and status=1 limit 1 ", 0);
                $cangjia_1 = array();
                if ($user_1['IsMainMeeting'] == 1) {
                    $cangjia_1['MeetingName'] = $cangjia['MeetingName'];
                }
                if ($user_1['IsChildMeeting'] == 1) {
                    $cangjia_1['ChildMeetingName'] = $cangjia['ChildMeetingName'];
                }
                if ($user_1['IsChildMeeting2'] == 1) {
                    $cangjia_1['ChildMeetingName2'] = $cangjia['ChildMeetingName2'];
                }
                if ($user_1['IsChildMeeting3'] == 1) {
                    $cangjia_1['ChildMeetingName3'] = $cangjia['ChildMeetingName3'];
                }
                if ($user_1['IsChildMeeting4'] == 1) {
                    $cangjia_1['ChildMeetingName4'] = $cangjia['ChildMeetingName4'];
                }
                if ($user_1['IsChildMeeting5'] == 1) {
                    $cangjia_1['ChildMeetingName5'] = $cangjia['ChildMeetingName5'];
                }
                if ($user_1['IsChildMeeting6'] == 1) {
                    $cangjia_1['ChildMeetingName6'] = $cangjia['ChildMeetingName6'];
                }
                if ($user_1['IsChildMeeting7'] == 1) {
                    $cangjia_1['ChildMeetingName7'] = $cangjia['ChildMeetingName7'];
                }
                if ($user_1['IsChildMeeting8'] == 1) {
                    $cangjia_1['ChildMeetingName8'] = $cangjia['ChildMeetingName8'];
                }
                if ($user_1['IsChildMeeting9'] == 1) {
                    $cangjia_1['ChildMeetingName9'] = $cangjia['ChildMeetingName9'];
                }
                if ($user_1['IsChildMeeting10'] == 1) {
                    $cangjia_1['ChildMeetingName10'] = $cangjia['ChildMeetingName10'];
                }
                $str = implode(",", $cangjia_1);
                $user['cangjia'] = $str;
                if ($meetid == '45') {
                    $user['usertype'] = '钢之家员工';
                } elseif ($user['GuestType'] != 0) {
                    $user['usertype'] = $GLOBALS['GuestType'][$user['GuestType']];
                } else {
                    $user['usertype'] = '参会嘉宾';
                }
                $meeting = array_merge($user, $member);
                $money = $this->_dao->getRow("SELECT Howmuch from meeting_member_finance where Mid='$mbid' and Mtid='" . $meetid . "' ", 0);
                if (empty($money) || $money['Howmuch'] == 0) {
                    $meet_money = 0;
                }
                if ($user['IsCheckIned'] != 1 && $meet_money == 0) {
                    $arr['Success'] = '0';
                    if ($meeting['IsGuest'] == 0) {
                        $arr['Message'] = base64_encode_new('该参会嘉宾未报到，请先报到');
                    } elseif ($meeting['IsGuest'] == 1) {
                        $arr['Message'] = base64_encode_new('该参会重要嘉宾未报到，请先报到');
                    }
                } else {
                    if ($user['IsGetZl'] == 1) {
                        $meeting['TrueName'] = base64_encode_new($meeting['TrueName']);
                        $meeting['Sex'] = base64_encode_new($meeting['Sex']);
                        $meeting['Post'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($meeting['Post'])));
                        $meeting['CheckInedDesc'] = base64_encode_new($meeting['CheckInedDesc']);
                        $meeting['HotelDesc'] = base64_encode_new($meeting['HotelDesc']);
                        $meeting['usertype'] = base64_encode_new($meeting['usertype']);
                        $meeting['compfname'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($meeting['compfname'])));
                        $meeting['compabb'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($meeting['compabb'])));
                        $meeting['cangjia'] = base64_encode_new($meeting['cangjia']);
                        $meeting['comtype'] = base64_encode_new($meeting['comtype']);
                        $meeting['PictureBig'] = $meeting['PictureBig'] == '' ? STEELHOME_SITE : ((strstr($meeting['PictureBig'], "http://")||strstr($meeting['PictureBig'], "https://")) ? $meeting['PictureBig'] : (strstr($meeting['PictureBig'], "//") ? 'http:' . $meeting['PictureBig'] : STEELHOME_SITE . $meeting['PictureBig']));
                        $meeting['PictureSmall'] = $meeting['PictureSmall'] == '' ? STEELHOME_SITE : ((strstr($meeting['PictureBig'], "http://")||strstr($meeting['PictureSmall'], "https://")) ? $meeting['PictureSmall'] : (strstr($meeting['PictureSmall'], "//") ? 'http:' . $meeting['PictureSmall'] : STEELHOME_SITE . $meeting['PictureSmall']));

                        $meeting['PictureBig'] = str_replace("http:////", "http://", $meeting['PictureBig']);
                        $meeting['PictureSmall'] = str_replace("http:////", "http://", $meeting['PictureSmall']);
                        $meeting['PictureBig'] = str_replace("http://", "https://", $meeting['PictureBig']);
                        $meeting['PictureSmall'] = str_replace("http://", "https://", $meeting['PictureSmall']);
                        $meeting['PictureBig'] = str_replace("http:https", "https", $meeting['PictureBig']);
                        $meeting['PictureSmall'] = str_replace("http:https", "https", $meeting['PictureSmall']);

                        $arr['Success'] = '1';
                        if ($meeting['IsGuest'] == 0) {
                            $arr['Message'] = base64_encode_new('该参会嘉宾已于' . $meeting['GetZlDate'] . '领取资料');
                        } elseif ($meeting['IsGuest'] == 1) {
                            $arr['Message'] = base64_encode_new('该参会重要嘉宾已于' . $meeting['GetZlDate'] . '领取资料');
                        }
                        $arr['Result'] = $meeting;
                    } else {
                        if ($meeting) {
                            // 备份记录
                            $this->_dao->execute("Insert Into meeting_member_contactman_h select * from meeting_member_contactman WHERE GUID='$guid' and status=1 ");

                            $loginman = $this->_dao->getRow("SELECT * from meeting_app_session where GUID='$GUID' ", 0);
                            $user['TrueName'] = str_replace("'", "\'", $user['TrueName']);

                            $this->_dao->execute("UPDATE meeting_member_contactman SET  IsCheckInXk='1',IsCheckInZl='1',IsCheckIned='1',IsGetZl='1',GetZlDate=NOW(), GetZlUserId='" . $loginman['Uid'] . "' WHERE GUID='$guid'");

                            if ($meeting['IsGuest'] == 0) {
                                $log = "领取操作人：" . $loginman['TrueName'] . "日期：" . date('Y-m-d H:i:s') . "领取" . $member['compfname'] . "的嘉宾" . $user['TrueName'] . "资料信息";
                            } elseif ($meeting['IsGuest'] == 1) {
                                $log = "领取操作人：" . $loginman['TrueName'] . "日期：" . date('Y-m-d H:i:s') . "领取" . $member['compfname'] . "的重要嘉宾" . $user['TrueName'] . "资料信息";
                            }

                            $ip = $this->_dao->getip();

                            $this->_dao->execute("insert into  meeting_logs SET Mtid='" . $meetid . "',LogsType='3',LogsTitle='领取资料',LogsMid='" . $member['Mid'] . "', UserId='" . $loginman['Uid'] . "',UserName='" . $loginman['TrueName'] . "',UserIp='" . $ip . "',OperLog='" . $log . "',OperDate=NOW() ;");

                            $meeting['TrueName'] = base64_encode_new($meeting['TrueName']);
                            $meeting['Sex'] = base64_encode_new($meeting['Sex']);
                            $meeting['Post'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($meeting['Post'])));
                            $meeting['CheckInedDesc'] = base64_encode_new($meeting['CheckInedDesc']);
                            $meeting['HotelDesc'] = base64_encode_new($meeting['HotelDesc']);
                            $meeting['compfname'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($meeting['compfname'])));
                            $meeting['compabb'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($meeting['compabb'])));
                            $meeting['usertype'] = base64_encode_new($meeting['usertype']);
                            $meeting['cangjia'] = base64_encode_new($meeting['cangjia']);
                            $meeting['comtype'] = base64_encode_new($meeting['comtype']);
                            $meeting['PictureBig'] = $meeting['PictureBig'] == '' ? STEELHOME_SITE : ((strstr($meeting['PictureBig'], "http://")||strstr($meeting['PictureBig'], "https://")) ? $meeting['PictureBig'] : (strstr($meeting['PictureBig'], "//") ? 'http:' . $meeting['PictureBig'] : STEELHOME_SITE . $meeting['PictureBig']));
                            $meeting['PictureSmall'] = $meeting['PictureSmall'] == '' ? STEELHOME_SITE : ((strstr($meeting['PictureBig'], "http://")||strstr($meeting['PictureSmall'], "https://")) ? $meeting['PictureSmall'] : (strstr($meeting['PictureSmall'], "//") ? 'http:' . $meeting['PictureSmall'] : STEELHOME_SITE . $meeting['PictureSmall']));
                            $meeting['PictureBig'] = str_replace("http:////", "http://", $meeting['PictureBig']);
                            $meeting['PictureSmall'] = str_replace("http:////", "http://", $meeting['PictureSmall']);
                            $meeting['PictureBig'] = str_replace("http://", "https://", $meeting['PictureBig']);
                            $meeting['PictureSmall'] = str_replace("http://", "https://", $meeting['PictureSmall']);
                            $meeting['PictureBig'] = str_replace("http:https", "https", $meeting['PictureBig']);
                            $meeting['PictureSmall'] = str_replace("http:https", "https", $meeting['PictureSmall']);
                            // print_r($meeting);
                            $arr['Success'] = '1';
                            if ($meeting['IsGuest'] == 0) {
                                $arr['Message'] = base64_encode_new('参会嘉宾领取资料成功');
                            } elseif ($meeting['IsGuest'] == 1) {
                                $arr['Message'] = base64_encode_new('参会重要嘉宾领取资料成功');
                            }
                            $arr['Result'] = $meeting;
                        } else {
                            $arr = array('Success' => '0',
                                'Message' => 'null',
                            );
                        }
                    } //$user['IsGetZl']==1
                } //$user['IsCheckIned']!=1
            } else // $meetid==$meet && $meet!=45
            {
                $arr['Success'] = '0';
                if ($meetid == '45') {
                    $arr['Message'] = base64_encode_new('钢之家员工不可参与领取资料');
                }
                // elseif ($meetid != $meet)
                // {
                // $arr['Message'] = base64_encode_new('会议不匹配');
                // }
            }
        }

        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    // 纪念品领取接口
    public function meet_QRjnpLq($params)
    {
        $GUID = $params['GUID'];
        $signcs = $params['SignCS'];
        $QRCode = base64_decode($params['QRCode']);
        $meetid = $params['ID'];
        $BHcode = $params['BHcode'];

        $arr = array('Success' => '0',
            'Message' => 'null',
        );
        if ($signcs != '' && $GUID != '') {
            //  xiangbin20180227
            // $loguser = $this->getUser($GUID,$signcs);

            // if( $loguser['Uid'] == "390855" || $loguser['Uid'] == "391935"){

            // }else{

            // $arr = array('Success' => '0',
            // 'Message' => base64_encode_new('您无权操作纪念品领取'),
            // );

            // $json_string = $this->pri_JSON($arr);
            // echo $json_string;
            // exit;
            // }
            //  xiangbin20180227


            if ($meetid != 45) {
                if (!empty($BHcode)) {
                    $user = $this->_dao->getRow("select ID,TrueName,IsEn,Mid,IsCheckIned,IsGuest,IsGetjnp,IsCanZl,IsGetMoney,GetjnpDate from meeting_member_contactman where PersonSign='$BHcode' and Mtid = '$meetid'  and status=1 limit 1", 0);
                    $isen = $user['IsEn'];
                    if ($user['IsEn'] == "1") {

                        $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,MidEn as Mid from meeting_member where  MidEn='" . $user['Mid'] . "' and Mtid='$meetid' ", 0);
                    } else {

                        $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,Mid from meeting_member where  Mid='" . $user['Mid'] . "' and Mtid='$meetid'", 0);
                    }

                } else {
                    // 添加微信二维码判断
                    //$data = explode("|" , $QRCode);
                    $data = $this->decodeQRCode($QRCode);
                    // $guid = $data[0];
                    $mbid = $data[1];
                    $maid = $data[2];
                    $bzf = $data[3];
                    $isen = $data[4];
                    if (empty($isen)) $isen = 0;//添加IsEn字段
                    $user = $this->_dao->getRow("SELECT ID,TrueName,IsEn,Mid,IsCheckIned,IsGuest,IsGetjnp,IsCanZl,IsGetMoney,GetjnpDate from meeting_member_contactman where IsEn='$isen' and Mtid='$meetid' AND Maid='$maid' and status=1 ", 0);//添加IsEn字段
                    if ($isen != "1") {
                        $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,Mid from meeting_member where Mid='" . $user['Mid'] . "' and Mtid='$meetid' ", 0);
                    } else {
                        $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,MidEn as Mid  from meeting_member where MidEn='" . $user['MidEn'] . "' and Mtid='$meetid' ", 0);
                    }


                }
                $money = $this->_dao->getRow("SELECT Howmuch from meeting_member_finance where Mid='" . $user['Mid'] . "' and Mtid='" . $meetid . "' ", 0);

                if (empty($money) || $money['Howmuch'] == 0) {
                    $meet_money = 1;
                }
                if ($user['IsCheckIned'] != 1 && $meet_money == 1) {
                    $arr['Success'] = '0';
                    if ($user['IsGuest'] == 0) {
                        $arr['Message'] = base64_encode_new("该参会嘉宾未报到，请先报到");
                    } elseif ($user['IsGuest'] == 1) {
                        $arr['Message'] = base64_encode_new('该参会重要嘉宾未报到，请先报到');
                    }
                } else {
                    //xiangbin20180227 start
                    //if ($user['IsGetjnp'] == 1 || $user['IsCanZl'] == "1"|| $user['IsCanZl'] == "2" || $user['IsGetMoney'] == "0")
                    if ($user['IsGetjnp'] == 1 || $user['IsCanZl'] == "1" || $user['IsCanZl'] == "2") //xiangbin20180227 end
                    {

                        if ($user['IsCanZl'] == "1" || $user['IsCanZl'] == "2") {
                            $arr['Success'] = '0';
                            //$arr['Message'] =  base64_encode_new('抱歉，该嘉宾证不含纪念品！');
                            $arr['Message'] = base64_encode_new('否');
                        } else {

                            if ($user['IsGetMoney'] == "0") {
                                $arr['Success'] = '0';
                                $arr['Message'] = base64_encode_new('抱歉，你目前没有权限领取纪念品，请先缴费！');
                            } else {
                                $arr['Success'] = '0';
                                if ($user['IsGuest'] == 0) {
                                    $arr['Message'] = base64_encode_new('该参会嘉宾已于' . $user['GetjnpDate'] . '领取纪念品');
                                } elseif ($user['IsGuest'] == 1) {
                                    $arr['Message'] = base64_encode_new('该参会重要嘉宾已于' . $user['GetjnpDate'] . '领取纪念品');
                                }
                                $arr['Result'] = '';
                            }
                        }
                    } else {
                        if ($user) {
                            // 备份记录
                            $this->_dao->execute("Insert Into meeting_member_contactman_h select * from meeting_member_contactman WHERE ID='" . $user['ID'] . "' ");

                            $loginman = $this->_dao->getRow("SELECT Uid,TrueName from meeting_app_session where GUID='$GUID' ", 0);
                            $user['TrueName'] = str_replace("'", "\'", $user['TrueName']);

                            $this->_dao->execute("UPDATE meeting_member_contactman SET  IsCheckInXk='1',IsCheckInjnp='1',IsCheckIned='1', CheckInedDate=NOW(),IsGetjnp='1',GetjnpDate=NOW(), GetjnpUserId='" . $loginman['Uid'] . "' WHERE ID='" . $user['ID'] . "' ");//添加IsEn字段

                            if ($user['IsGuest'] == 0) {
                                $log = "领取操作人：" . $loginman['TrueName'] . "日期：" . date('Y-m-d H:i:s') . "领取" . $member['compfname'] . "的嘉宾" . $user['TrueName'] . "纪念品信息";
                            } elseif ($user['IsGuest'] == 1) {
                                $log = "领取操作人：" . $loginman['TrueName'] . "日期：" . date('Y-m-d H:i:s') . "领取" . $member['compfname'] . "的重要嘉宾" . $user['TrueName'] . "纪念品信息";
                            }

                            $ip = $this->_dao->getip();

                            $this->_dao->execute("insert into  meeting_logs SET Mtid='" . $meetid . "',LogsType='3',LogsTitle='领取纪念品',LogsMid='" . $member['Mid'] . "', UserId='" . $loginman['Uid'] . "',UserName='" . $loginman['TrueName'] . "',UserIp='" . $ip . "',OperLog='" . $log . "',OperDate=NOW() ;");

                            $arr['Success'] = '1';

                            //获取已领取 数量
                            $snum = $this->_dao->getOne("select count(*) from meeting_member_contactman where  IsGetjnp = 1 and  Mtid='$meetid' and status=1 ", 0);//添加IsEn字段

                            if ($user['IsGuest'] == 0) {
                                $arr['Message'] = base64_encode_new('参会嘉宾(' . $user['TrueName'] . ')领取纪念品成功,纪念品已领取数量' . $snum);
                            } elseif ($user['IsGuest'] == 1) {
                                $arr['Message'] = base64_encode_new('参会重要嘉宾(' . $user['TrueName'] . ')领取纪念品成功,纪念品已领取数量' . $snum);
                            }
                            $arr['Result'] = '';
                        } else {
                            $arr = array('Success' => '0',
                                'Message' => base64_encode_new('操作失败'),
                            );
                        }
                    }
                }
            } else {
                $arr['Success'] = '0';
                //$arr['Message'] = base64_encode_new('抱歉，该嘉宾证不含纪念品！');
                $arr['Message'] = base64_encode_new('否');
            }
        }

        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    // 资料领取接口
    public function meet_QRZiliaoLq2($params)
    {

        $GUID = $params['GUID'];
        $signcs = $params['SignCS'];
        $QRCode = base64_decode($params['QRCode']);
        $meetid = $params['ID'];
        $BHcode = $params['BHcode'];

        $arr = array('Success' => '0',
            'Message' => 'null',
        );
        if ($signcs != '' && $GUID != '') {
            //xiangbin20180227
            // $loguser = $this->getUser($GUID,$signcs);

            // if( $loguser['Uid'] == "390855" || $loguser['Uid'] == "391935"){

            // }else{

            // $arr = array('Success' => '0',
            // 'Message' => base64_encode_new('您无权操作资料领取'),
            // );

            // $json_string = $this->pri_JSON($arr);
            // echo $json_string;
            // exit;
            // }
            //xiangbin20180227


            if ($meetid != 45) {
                if (!empty($BHcode)) {
                    $user = $this->_dao->getRow("select ID,TrueName,IsEn,Mid,IsCheckIned,IsGuest,IsGetZl,IsCanZl,IsGetMoney,GetZlDate from meeting_member_contactman where PersonSign='$BHcode' and Mtid = '$meetid' and status=1 limit 1", 0);
                    $isen = $user['IsEn'];
                    if ($user['IsEn'] == "1") {

                        $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,MidEn as Mid from meeting_member where MidEn='" . $user['Mid'] . "' and Mtid='$meetid'", 0);
                    } else {

                        $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,Mid from meeting_member where Mid='" . $user['Mid'] . "' and Mtid='$meetid'", 0);
                    }

                } else {
                    // 添加微信二维码判断
                    //$data = explode("|" , $QRCode);
                    $data = $this->decodeQRCode($QRCode);
                    // $guid = $data[0];
                    $mbid = $data[1];
                    $maid = $data[2];
                    $bzf = $data[3];
                    $isen = $data[4];
                    if (empty($isen)) $isen = 0;//添加IsEn字段

                    $user = $this->_dao->getRow("SELECT ID,TrueName,IsEn,Mid,IsCheckIned,IsGuest,IsGetZl,IsCanZl,IsGetMoney,GetZlDate from meeting_member_contactman where IsEn='$isen' and Mtid='$meetid' AND Maid='$maid' and status=1 ", 0);//添加IsEn字段
                    if ($isen != "1") {
                        $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,Mid from meeting_member where Mid='" . $user['Mid'] . "' and Mtid='$meetid' ", 0);
                    } else {
                        $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,MidEn as Mid from meeting_member where MidEn='" . $user['MidEn'] . "' and Mtid='$meetid' ", 0);
                    }
                }
                $money = $this->_dao->getRow("SELECT Howmuch from meeting_member_finance where Mid='" . $user['Mid'] . "' and Mtid='" . $meetid . "' ", 0);


                if (empty($money) || $money['Howmuch'] == 0) {
                    $meet_money = 1;
                }
                if ($user['IsCheckIned'] != 1 && $meet_money == 1) {
                    $arr['Success'] = '0';
                    if ($user['IsGuest'] == 0) {
                        $arr['Message'] = base64_encode_new("该参会嘉宾未报到，请先报到");
                        // $arr['IsEn']=$user['IsEn'];
                    } elseif ($user['IsGuest'] == 1) {
                        $arr['Message'] = base64_encode_new('该参会重要嘉宾未报到，请先报到');
                        //$arr['IsEn']=$user['IsEn'];
                    }
                } else {
                    //xiangbin20180227 start
                    if ($user['IsGetZl'] == 1 || $user['IsCanZl'] == "1")
                        //if ($user['IsGetZl'] == 1 || $user['IsCanZl'] == "1" || $user['IsGetMoney'] == "0")
                        //xiangbin20180227 end
                    {

                        if ($user['IsCanZl'] == "1") {
                            $arr['Success'] = '0';
                            //$arr['Message'] =  base64_encode_new('抱歉，该嘉宾证不含资料！');
                            $arr['Message'] = base64_encode_new('否');
                            //$arr['IsEn']=$user['IsEn'];
                        } else {

                            if ($user['IsGetMoney'] == "0") {
                                $arr['Success'] = '0';
                                $arr['Message'] = base64_encode_new('抱歉，您目前未有权限领取资料，请先缴费！');
                                // $arr['IsEn']=$user['IsEn'];
                            } else {
                                $arr['Success'] = '0';
                                if ($user['IsGuest'] == 0) {
                                    $arr['Message'] = base64_encode_new('该参会嘉宾已于' . $user['GetZlDate'] . '领取资料');
                                    //  $arr['IsEn']=$user['IsEn'];
                                } elseif ($user['IsGuest'] == 1) {
                                    $arr['Message'] = base64_encode_new('该参会重要嘉宾已于' . $user['GetZlDate'] . '领取资料');
                                    // $arr['IsEn']=$user['IsEn'];
                                }
                                $arr['Result'] = '';
                            }
                        }
                    } else {
                        if ($user) {
                            // 备份记录
                            //$this->_dao->execute("Insert Into meeting_member_contactman_h select * from meeting_member_contactman WHERE Mtid='$meetid' AND Mid='".$user['Mid']."' AND Maid='" . $user['Maid'] . "' and status=1 ");

                            $this->_dao->execute("Insert Into meeting_member_contactman_h select * from meeting_member_contactman WHERE  ID='" . $user['ID'] . "'  ");


                            $loginman = $this->_dao->getRow("SELECT Uid,TrueName from meeting_app_session where GUID='$GUID' ", 0);
                            $user['TrueName'] = str_replace("'", "\'", $user['TrueName']);

                            $this->_dao->execute("UPDATE meeting_member_contactman SET  IsCheckInXk='1',IsCheckInZl='1',IsCheckIned='1', CheckInedDate=NOW(),IsGetZl='1',GetZlDate=NOW(), GetZlUserId='" . $loginman['Uid'] . "' WHERE ID='" . $user['ID'] . "'");//添加IsEn字段

                            if ($user['IsGuest'] == 0) {
                                $log = "领取操作人：" . $loginman['TrueName'] . "日期：" . date('Y-m-d H:i:s') . "领取" . $member['compfname'] . "的嘉宾" . $user['TrueName'] . "资料信息";
                            } elseif ($user['IsGuest'] == 1) {
                                $log = "领取操作人：" . $loginman['TrueName'] . "日期：" . date('Y-m-d H:i:s') . "领取" . $member['compfname'] . "的重要嘉宾" . $user['TrueName'] . "资料信息";
                            }

                            $ip = $this->_dao->getip();

                            $this->_dao->execute("insert into  meeting_logs SET Mtid='" . $meetid . "',LogsType='3',LogsTitle='领取资料',LogsMid='" . $member['Mid'] . "', UserId='" . $loginman['Uid'] . "',UserName='" . $loginman['TrueName'] . "',UserIp='" . $ip . "',OperLog='" . $log . "',OperDate=NOW() ;");

                            $arr['Success'] = '1';

                            //获取已领取 数量
                            $snum = $this->_dao->getOne("select count(*) from meeting_member_contactman where  IsGetZl = 1 and  Mtid='$meetid' and status=1 ", 0);//添加IsEn字段

                            if ($user['IsGuest'] == 0) {
                                $arr['Message'] = base64_encode_new('参会嘉宾(' . $user['TrueName'] . ')领取资料成功,资料已领取数量' . $snum);
                                // $arr['IsEn']=$user['IsEn'];
                            } elseif ($user['IsGuest'] == 1) {
                                $arr['Message'] = base64_encode_new('参会重要嘉宾(' . $user['TrueName'] . ')领取资料成功,资料已领取数量' . $snum);
                                // $arr['IsEn']=$user['IsEn'];
                            }
                            $arr['Result'] = '';
                        } else {
                            $arr = array('Success' => '0',
                                'Message' => base64_encode_new('操作失败'),
                            );
                        }
                    }
                }
            } else {
                $arr['Success'] = '0';
                //$arr['Message'] = base64_encode_new('抱歉，该嘉宾证不含资料！');
                $arr['Message'] = base64_encode_new('否');
            }
        }

        $json_string = $this->pri_JSON($arr);
        echo $json_string;

    }

    // //最新报到嘉宾接口
    public function meet_ZXBaoDao($params)
    {
        // $time = $params['Time'];
        $time = date('Y-m-d H:i:s');
        // $lastime = date('Y-m-d H:i:s');
        $ltime = base64_decode($params['lasttime']);

        if ($ltime) {
        } else {
            $ltime = $time;
        }

        $lastime = $ltime;
        // $arrmem = array("TrueName", "PersonSign", "Sex", "Post", "ContactPhone", "ContactMobile", "ContactFax", "ContactEmail", "Mtid", "Maid", "CheckInedDesc", "HotelRoomNumber", "IsTravel", "TravelPersons", "HotelDesc", "PictureBig", "PictureSmall", "GuestType", "TableNumber", "IsGuest", "IsCheckIned", "IsGetZl");
        $meetid = $params['ID'];

        $isnew = 0;

        $cangjia = $this->_dao->getRow("SELECT MeetingName,ChildMeetingName,ChildMeetingName2  from meeting_base where ID='" . $meetid . "'", 0);

        $Baodao = $this->_dao->query("SELECT ID,IsEn,TrueName,Sex,Post,ContactPhone,ContactMobile,PersonSign,CompfnameS,Mtid,Mid,CheckInedDate,IsGuest,GuestType,HotelDesc,PictureBig,PictureSmall,IsCheckIned,IsGetZl from meeting_member_contactman where Mtid='" . $meetid . "' and status=1 and  IsCheckIned=1 and CheckInedDate <= '" . $time . "' and CheckInedDate >= '" . $lastime . "' order by CheckInedDate DESC", 0);

        if ($Baodao) {
            $isnew = 1;
        }
        for ($i = 0; $i < count($Baodao); $i++) {
            $user_1 = $this->_dao->getRow("SELECT IsMainMeeting,IsChildMeeting,IsChildMeeting2,IsChildMeeting3,IsChildMeeting4,IsChildMeeting5,IsChildMeeting6,IsChildMeeting7,IsChildMeeting8,IsChildMeeting9,IsChildMeeting10,IsEn  from meeting_member_contactman where  ID = '" . $Baodao[$i]['ID'] . "'  ", 0);

            $cangjia_1 = array();
            if ($user_1['IsMainMeeting'] == 1) {
                $cangjia_1['MeetingName'] = $cangjia['MeetingName'];
            }
            if ($user_1['IsChildMeeting'] == 1) {
                $cangjia_1['ChildMeetingName'] = $cangjia['ChildMeetingName'];
            }
            if ($user_1['IsChildMeeting2'] == 1) {
                $cangjia_1['ChildMeetingName2'] = $cangjia['ChildMeetingName2'];
            }
            if ($user_1['IsChildMeeting3'] == 1) {
                $cangjia_1['ChildMeetingName3'] = $cangjia['ChildMeetingName3'];
            }
            if ($user_1['IsChildMeeting4'] == 1) {
                $cangjia_1['ChildMeetingName4'] = $cangjia['ChildMeetingName4'];
            }
            if ($user_1['IsChildMeeting5'] == 1) {
                $cangjia_1['ChildMeetingName5'] = $cangjia['ChildMeetingName5'];
            }
            if ($user_1['IsChildMeeting6'] == 1) {
                $cangjia_1['ChildMeetingName6'] = $cangjia['ChildMeetingName6'];
            }
            if ($user_1['IsChildMeeting7'] == 1) {
                $cangjia_1['ChildMeetingName7'] = $cangjia['ChildMeetingName7'];
            }
            if ($user_1['IsChildMeeting8'] == 1) {
                $cangjia_1['ChildMeetingName8'] = $cangjia['ChildMeetingName8'];
            }
            if ($user_1['IsChildMeeting9'] == 1) {
                $cangjia_1['ChildMeetingName9'] = $cangjia['ChildMeetingName9'];
            }
            if ($user_1['IsChildMeeting10'] == 1) {
                $cangjia_1['ChildMeetingName10'] = $cangjia['ChildMeetingName10'];
            }
            $str = implode(",", $cangjia_1);
            $Baodao[$i]['cangjia'] = $str;
            if ($meetid == '45') {
                $Baodao[$i]['usertype'] = '钢之家员工';
            } elseif ($Baodao[$i]['GuestType'] != 0) {
                $Baodao[$i]['usertype'] = $GLOBALS['GuestType'][$Baodao[$i]['GuestType']];
            } else {
                $Baodao[$i]['usertype'] = '参会嘉宾';
            }
        }

        foreach ($Baodao as &$da) {
            unset($da['ID']);
            $da['TrueName'] = base64_encode_new($da['TrueName']);
            $da['Sex'] = base64_encode_new($da['Sex']);
            $da['Post'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($da['Post'])));
            $da['HotelDesc'] = base64_encode_new($da['HotelDesc']);
            $da['cangjia'] = base64_encode_new($da['cangjia']);
            $da['CompfnameS'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($da['CompfnameS'])));
            $da['usertype'] = base64_encode_new($da['usertype']);

            // $da['PictureBig'] = $da['PictureBig'] == '' ? STEELHOME_SITE : (strstr($da['PictureBig'], "http://") ? $da['PictureBig'] : (strstr($da['PictureBig'], "//") ? 'http:' . $da['PictureBig'] : STEELHOME_SITE . $da['PictureBig']));
            // $da['PictureSmall'] = $da['PictureSmall'] == '' ? STEELHOME_SITE : (strstr($da['PictureSmall'], "http://") ? $da['PictureSmall'] : (strstr($da['PictureSmall'], "//") ? 'http:' . $da['PictureSmall'] : STEELHOME_SITE . $da['PictureSmall']));
            // $da['PictureBig'] = str_replace("http:////", "http://", $da['PictureBig']);
            // $da['PictureSmall'] = str_replace("http:////", "http://", $da['PictureSmall']);
            // $da['PictureBig'] = str_replace("http://", "https://", $da['PictureBig']);
            // $da['PictureSmall'] = str_replace("http://", "https://", $da['PictureSmall']);
            // $da['PictureBig'] = str_replace("http:https", "https:", $da['PictureBig']);
            // $da['PictureSmall'] = str_replace("http:https", "https:", $da['PictureSmall']);
            $da['PictureBig'] = $da['PictureBig'] == '' ? STEELHOME_SITE : (strstr($da['PictureBig'], "http://")||strstr($da['PictureBig'], "https://") ? $da['PictureBig'] : (strstr($da['PictureBig'], "//") ? 'http:' . $da['PictureBig'] : STEELHOME_SITE . $da['PictureBig']));
            $da['PictureSmall'] = $da['PictureSmall'] == '' ? STEELHOME_SITE : (strstr($da['PictureSmall'], "http://")||strstr($da['PictureSmall'], "https://") ? $da['PictureSmall'] : (strstr($da['PictureSmall'], "//") ? 'http:' . $da['PictureSmall'] : STEELHOME_SITE . $da['PictureSmall']));
            $da['PictureBig'] = str_replace("http:////", "http://", $da['PictureBig']);
            $da['PictureSmall'] = str_replace("http:////", "http://", $da['PictureSmall']);
            $da['PictureBig'] = str_replace("http://", "https://", $da['PictureBig']);
            $da['PictureSmall'] = str_replace("http://", "https://", $da['PictureSmall']);
            $da['PictureBig'] = str_replace("http:https", "https", $da['PictureBig']);
            $da['PictureSmall'] = str_replace("http:https", "https", $da['PictureSmall']);


            $da['IsEn'] = $da['IsEn'];
        }
        $arr = array('Success' => '0',
            'IsNew' => '0',
            'Message' => 'null',
        );
        if ($Baodao) {
            $arr['Success'] = '1';
            $arr['Message'] = base64_encode_new('最新报到嘉宾');
            $arr['IsNew'] = '1';
            $arr['Results'] = $Baodao;
        }

        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    // 最新报到列表接口
    public function meet_ZXLieBiao($params)
    {
        $meetid = $params['ID']; //会议ID

        //$cangjia = $this->_dao->getRow("SELECT MeetingName,ChildMeetingName,ChildMeetingName2  from meeting_base where ID='" . $meetid . "'", 0);
        $cangjia = $this->_dao->getRow("SELECT *  from meeting_base where ID='" . $meetid . "'", 0);
        $liebiao = $this->_dao->query("SELECT ID,IsEn,TrueName,Sex,Post,ContactPhone,PersonSign,ContactMobile,CompfnameS,Mtid,Mid,CheckInedDate,IsGuest,GuestType,HotelDesc,PictureBig,PictureSmall,IsCheckIned,IsGetZl from meeting_member_contactman where Mtid='" . $meetid . "' and status=1 and IsCheckIned=1  order by CheckInedDate DESC limit 30", 0);

        for ($i = 0; $i < count($liebiao); $i++) {
            $user_1 = $this->_dao->getRow("SELECT IsMainMeeting,IsChildMeeting,IsChildMeeting2,IsChildMeeting3,IsChildMeeting4,IsChildMeeting5,IsChildMeeting6,IsChildMeeting7,IsChildMeeting8,IsChildMeeting9,IsChildMeeting10,IsEn  from meeting_member_contactman where  ID = '" . $liebiao[$i]['ID'] . "' ", 0);
            // echo "aaa".$user_1['IsMainMeeting'];
            $cangjia_1 = array();
            if ($user_1['IsMainMeeting'] == 1) {
                //$cangjia_1['MeetingName'] = $cangjia['MeetingName'];
                $cangjia_1['ShortName'] = $cangjia['ShortName'];
            }
            if ($user_1['IsChildMeeting'] == 1) {
                $cangjia_1['ChildShortName'] = $cangjia['ChildShortName'];
            }
            if ($user_1['IsChildMeeting2'] == 1) {
                $cangjia_1['ChildShortName2'] = $cangjia['ChildShortName2'];
            }
            if ($user_1['IsChildMeeting3'] == 1) {
                $cangjia_1['ChildShortName3'] = $cangjia['ChildShortName3'];
            }
            if ($user_1['IsChildMeeting4'] == 1) {
                $cangjia_1['ChildShortName4'] = $cangjia['ChildShortName4'];
            }
            if ($user_1['IsChildMeeting5'] == 1) {
                $cangjia_1['ChildShortName5'] = $cangjia['ChildShortName5'];
            }
            if ($user_1['IsChildMeeting6'] == 1) {
                $cangjia_1['ChildShortName6'] = $cangjia['ChildShortName6'];
            }
            if ($user_1['IsChildMeeting7'] == 1) {
                $cangjia_1['ChildShortName7'] = $cangjia['ChildShortName7'];
            }
            if ($user_1['IsChildMeeting8'] == 1) {
                $cangjia_1['ChildShortName8'] = $cangjia['ChildShortName8'];
            }
            if ($user_1['IsChildMeeting9'] == 1) {
                $cangjia_1['ChildShortName9'] = $cangjia['ChildShortName9'];
            }
            if ($user_1['IsChildMeeting10'] == 1) {
                $cangjia_1['ChildShortName10'] = $cangjia['ChildShortName10'];
            }
            $str = implode(",", $cangjia_1);
            $liebiao[$i]['cangjia'] = $str;
            if ($meetid == '45') {
                $liebiao[$i]['usertype'] = '钢之家员工';
            } elseif ($liebiao[$i]['GuestType'] != 0) {
                $liebiao[$i]['usertype'] = $GLOBALS['GuestType'][$liebiao[$i]['GuestType']];
            } else {
                $liebiao[$i]['usertype'] = '参会嘉宾';
            }
        }

        foreach ($liebiao as &$da) {
            unset($da['ID']);
            $da['TrueName'] = base64_encode_new($da['TrueName']);
            $da['Sex'] = base64_encode_new($da['Sex']);
            $da['HotelDesc'] = base64_encode_new($da['HotelDesc']);
            $da['Post'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($da['Post'])));
            $da['cangjia'] = base64_encode_new($da['cangjia']);
            $da['CompfnameS'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($da['CompfnameS'])));
            $da['usertype'] = base64_encode_new($da['usertype']);

            $da['PictureBig'] = $da['PictureBig'] == '' ? STEELHOME_SITE : (strstr($da['PictureBig'], "http://")||strstr($da['PictureBig'], "https://") ? $da['PictureBig'] : (strstr($da['PictureBig'], "//") ? 'http:' . $da['PictureBig'] : STEELHOME_SITE . $da['PictureBig']));
            $da['PictureSmall'] = $da['PictureSmall'] == '' ? STEELHOME_SITE : (strstr($da['PictureSmall'], "http://")||strstr($da['PictureSmall'], "https://") ? $da['PictureSmall'] : (strstr($da['PictureSmall'], "//") ? 'http:' . $da['PictureSmall'] : STEELHOME_SITE . $da['PictureSmall']));
            $da['PictureBig'] = str_replace("http:////", "http://", $da['PictureBig']);
            $da['PictureSmall'] = str_replace("http:////", "http://", $da['PictureSmall']);
            $da['PictureBig'] = str_replace("http://", "https://", $da['PictureBig']);
            $da['PictureSmall'] = str_replace("http://", "https://", $da['PictureSmall']);
            $da['PictureBig'] = str_replace("http:https", "https:", $da['PictureBig']);
            $da['PictureSmall'] = str_replace("http:https", "https:", $da['PictureSmall']);
            $da['IsEn'] = $da['IsEn'];
        }
        $arr = array('Success' => '0',
            'Message' => 'null',
        );
        if ($liebiao) {
            $arr['Success'] = '1';
            $arr['Message'] = base64_encode_new('最新报到嘉宾列表');
            $arr['Results'] = $liebiao;
        }

        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    // 正在进行中会议接口
    public function meet_QRmeetingsets($params)
    {

        $meetingset = $this->_dao->query("SELECT ID,MeetingName,ShortName,MeetingType from meeting_base where Status='2' order by StartDate asc", 0);
        foreach ($meetingset as &$da) {
            $da['MeetingName'] = base64_encode_new($da['MeetingName']);
            $da['ShortName'] = base64_encode_new($da['ShortName']);
            $da['MeetingImg']=isset($GLOBALS['MEETINGCLIENTIMG'][$da['ID']])?$GLOBALS['MEETINGCLIENTIMG'][$da['ID']]:$GLOBALS['MEETINGCLIENTIMG']['default'];
            // $da['ChildMeetingName']=base64_encode($da['ChildMeetingName']);
            // $da['ChildShortName']=base64_encode($da['ChildShortName']);
            // $da['CityName']=base64_encode($da['CityName']);
            // $da['ConferenceVenue']=base64_encode($da['ConferenceVenue']);
            // $da['AddressName']=base64_encode($da['AddressName']);
            // $da['HotelName']=base64_encode($da['HotelName']);
            // $da['HotelAddress']=base64_encode($da['HotelAddress']);
            // $da['MainComName']=base64_encode($da['MainComName']);
            // $da['ContactMan']=base64_encode($da['ContactMan']);
            // $da['MeetingDesc']=base64_encode($da['MeetingDesc']);
            // $da['MeetingContent']=base64_encode($da['MeetingContent']);
            // $da['MeetingGm']=base64_encode($da['MeetingGm']);
            // $da['MeetingLb']=base64_encode($da['MeetingLb']);
            // $da['MeetingXb']=base64_encode($da['MeetingXb']);
            // $da['MeetingZc']=base64_encode($da['MeetingZc']);
            // $da['FilesDirName']=base64_encode($da['FilesDirName']);
        }
        $arr = array('Success' => '0',
            'Message' => 'null',
        );
        if ($meetingset) {
            $arr['Success'] = '1';
            $arr['Message'] = base64_encode_new('正在进行中会议');
            $arr['Results'] = $meetingset;
        }
        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    public function meet_GetMemberInfo($params)
    {
        $meetid = $params['Mtid'];
        $needCompress = $params['needCompress'];//判断是否压缩的参数
        $meeting = array();

        $this->setvars($meetid);

        $dtime = $params['UpdateTime']!="0"?$params['UpdateTime']:"1970-01-01 00:00:00";
        $arr = array('Success' => '0',
            'Message' => base64_encode_new('暂无最新数据'),
            'Results' => '',
        );

        $meetname = $this->_dao->getRow("select * from meeting_base where ID  = '$meetid' limit 1");

        $contactman = $this->_dao->query("select a.*,b.compfname,b.compabb,b.comtype,b.UserName from meeting_member_contactman as a,meeting_member as b where ((a.Mid = b.Mid and a.IsEn=0) or (a.Mid= b.MidEn and a.IsEn=1)) and a.Mtid=b.Mtid and (a.Mtid = '$meetid' OR a.Mtid=45) and a.UpdateDate>'$dtime' and a.status=1 ORDER BY a.PersonSign asc,a.ID DESC ", 0);

        if (!empty($contactman)) // 存在会员表
        {
            foreach ($contactman as $key => $value) {
                $meetmain = '';
                $meetmain2 = '';
                if ($value['Mtid'] != 45) {
                    // 参加后四个会议的权限
                    $join_right = 0;
                    $join_main = 0;
                    if ($value['IsMainMeeting'] == "1" || $value['IsChildMeeting'] == 1 || $value['IsChildMeeting2'] == 1) {
                        $join_main = 1;
                    }
                    if ($join_main == 1 || $value['IsChildMeeting4'] == 1) {
                        $join_right = 1;
                    }
                    // 获取用户参加的会议
                    $meetmain .= ($value['IsMainMeeting'] == "1") ? $meetname['ShortName'] . "," : "";
                    $meetmain .= ($value['IsChildMeeting'] == "1") ? $meetname['ChildShortName'] . "," : "";
                    $meetmain .= ($value['IsChildMeeting2'] == "1") ? $meetname['ChildShortName2'] . "," : "";
                    $meetmain .= ($value['IsChildMeeting3'] == "1") ? $meetname['ChildShortName3'] . "," : "";
                    $meetmain .= $value['IsChildMeeting4'] == "1" ? $meetname['ChildShortName4'] . "," : ""; //汽车用钢
                    $meetmain .= ($value['IsChildMeeting5'] == "1") ? $meetname['ChildShortName5'] . "," : "";
                    $meetmain .= ($value['IsChildMeeting6'] == "1") ? $meetname['ChildShortName6'] . "," : "";
                    $meetmain .= ($value['IsChildMeeting7'] == "1") ? $meetname['ChildShortName7'] . "," : "";
                    $meetmain .= ($value['IsChildMeeting8'] == "1") ? $meetname['ChildShortName8'] . "," : "";
                    $meetmain .= $value['IsChildMeeting9'] == "1" ? $meetname['ChildShortName9'] . "," : "";
                    $meetmain .= $value['IsChildMeeting10'] == "1" ? $meetname['ChildShortName10'] . "," : "";
                    $meetmain = substr($meetmain, 0, -1);

                    //2015.4
                    //$meetmain2 .= ($value['IsMainMeeting'] == "1" || $join_main == 1 ) ? $meetname['ShortName'] . ",":"";
                    //$meetmain2 .= ($value['IsChildMeeting'] == "1" || $join_main == 1 )? $meetname['ChildShortName'].",":"";
                    //$meetmain2 .= ($value['IsChildMeeting2'] == "1" || $join_main == 1 ) ? $meetname['ChildShortName2'].",":"";
                    //$meetmain2 .= ($value['IsChildMeeting3'] == "1" || $join_right == 1 )? $meetname['ChildShortName3'].",":"";
                    //$meetmain2 .= $value['IsChildMeeting4'] == "1" ? $meetname['ChildShortName4'].",":""; //汽车用钢
                    //$meetmain2 .= ($value['IsChildMeeting5'] == "1" || $join_right == 1)? $meetname['ChildShortName5'].",":"";
                    //$meetmain2 .= ($value['IsChildMeeting6'] == "1" || $join_right == 1)? $meetname['ChildShortName6'].",":"";
                    //$meetmain2 .= ($value['IsChildMeeting7'] == "1")? $meetname['ChildShortName7'].",":"";
                    //$meetmain2 .= ($value['IsChildMeeting8'] == "1")? $meetname['ChildShortName8'].",":"";
                    //$meetmain2 .= $value['IsChildMeeting9'] == "1" ? $meetname['ChildShortName9'].",":"";
                    //$meetmain2 .= $value['IsChildMeeting10'] == "1" ? $meetname['ChildShortName10'].",":"";
                    //$meetmain2 = substr($meetmain2, 0, -1);

                    //2015.9
                    $meetmain2 .= ($value['IsMainMeeting'] == "1") ? $meetname['ShortName'] . "," : "";
                    $meetmain2 .= ($value['IsChildMeeting'] == "1") ? $meetname['ChildShortName'] . "," : "";
                    $meetmain2 .= ($value['IsChildMeeting2'] == "1") ? $meetname['ChildShortName2'] . "," : "";
                    $meetmain2 .= ($value['IsChildMeeting3'] == "1") ? $meetname['ChildShortName3'] . "," : "";
                    $meetmain2 .= $value['IsChildMeeting4'] == "1" ? $meetname['ChildShortName4'] . "," : ""; //汽车用钢
                    $meetmain2 .= ($value['IsChildMeeting5'] == "1") ? $meetname['ChildShortName5'] . "," : "";
                    $meetmain2 .= ($value['IsChildMeeting6'] == "1") ? $meetname['ChildShortName6'] . "," : "";
                    $meetmain2 .= ($value['IsChildMeeting7'] == "1") ? $meetname['ChildShortName7'] . "," : "";
                    $meetmain2 .= ($value['IsChildMeeting8'] == "1") ? $meetname['ChildShortName8'] . "," : "";
                    $meetmain2 .= $value['IsChildMeeting9'] == "1" ? $meetname['ChildShortName9'] . "," : "";
                    $meetmain2 .= $value['IsChildMeeting10'] == "1" ? $meetname['ChildShortName10'] . "," : "";
                    $meetmain2 = substr($meetmain2, 0, -1);

                }

                if ($value['Mtid'] == 45) {
                    $mtype = '钢之家员工';
                } elseif ($value['GuestType'] != 0) {
                    $mtype = $GLOBALS['GuestType'][$value['GuestType']];
                } else {
                    if ($value['IsEn'] == "1") {
                        $financet = $this->_dao->getRow("SELECT FinanceType from meeting_member where MidEn='" . $value['Mid'] . "' and Mtid='$meetid' ", 0);
                    } else {
                        $financet = $this->_dao->getRow("SELECT FinanceType from meeting_member where Mid='" . $value['Mid'] . "' and Mtid='$meetid' ", 0);
                    }
                    if ($financet == "16") {
                        $mtype = '媒体';
                    } else {
                        $mtype = '参会嘉宾';
                    }
                }
                $meeting[$key]['Cid'] = $value['ID'];
                $meeting[$key]['Mid'] = $value['Mid'];
                $meeting[$key]['Maid'] = $value['Maid'];
                $meeting[$key]['AdminName'] = base64_encode_new($value['UserName']);
                $meeting[$key]['usertype'] = base64_encode_new($mtype);
                $meeting[$key]['TrueName'] = base64_encode_new($value['TrueName']);
                $meeting[$key]['Sex'] = base64_encode_new($value['Sex']);
                $meeting[$key]['Post'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($value['Post'])));
                $meeting[$key]['CheckInedDesc'] = base64_encode_new($value['CheckInedDesc']);
                // $meeting[$key]['HotelDesc'] = base64_encode_new($value['HotelDesc']);
                $meeting[$key]['compfname'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($value['CompfnameS'])));
                $meeting[$key]['compabb'] = base64_encode_new(str_replace('&', '&&', htmlspecialchars_decode($value['compabb'])));
                $meeting[$key]['comtype'] = base64_encode_new($value['comtype']);
                $meeting[$key]['weixinewmurl'] = base64_encode_new($value['WeixinQRCode']);
                $meeting[$key]['IsEn'] = $value['IsEn'];
                $meeting[$key]['IsCanZl'] = $value['IsCanZl'];

                if ($value['PictureBig'] == "") {
                    $urow = $this->_dao->getRow("select PictureBig,PictureSmall from member_contact_user where user_id ='" . $value['Maid'] . "' ", 0);

                    $value['PictureBig'] = $urow['PictureBig'];
                    $value['PictureSmall'] = $urow['PictureSmall'];
                }


                if ($value['HotelInType']) {

                    $hoteldesc = $GLOBALS['HOTELT'][$value['HotelInType']] . "房间号：" . $value['HotelRoomNumber'] . "(已入住)";

                } else {
                    $hoteldesc = $GLOBALS['HOTELT'][$value['HotelType']] == "" ? "" : $GLOBALS['HOTELT'][$value['HotelType']] . "(未入住)";
                }
                $meeting[$key]['HotelType'] = $value['HotelType'];
                $meeting[$key]['HotelDesc'] = base64_encode_new($hoteldesc);

                $meeting[$key]['PictureBig'] = $value['PictureBig'] == '' ? STEELHOME_SITE : (strstr($value['PictureBig'], "http://")||strstr($value['PictureBig'], "https://") ? $value['PictureBig'] : (strstr($value['PictureBig'], "//") ? 'http:' . $value['PictureBig'] : STEELHOME_SITE . $value['PictureBig']));
                $meeting[$key]['PictureSmall'] = $value['PictureSmall'] == '' ? STEELHOME_SITE : (strstr($value['PictureSmall'], "http://")||strstr($value['PictureSmall'], "https://") ? $value['PictureSmall'] : (strstr($value['PictureSmall'], "//") ? 'http:' . $value['PictureSmall'] : STEELHOME_SITE . $value['PictureSmall']));
                $meeting[$key]['PictureBig'] = str_replace("http:////", "http://", $meeting[$key]['PictureBig']);
                $meeting[$key]['PictureSmall'] = str_replace("http:////", "http://", $meeting[$key]['PictureSmall']);
                $meeting[$key]['PictureBig'] = str_replace("http://", "https://", $meeting[$key]['PictureBig']);
                $meeting[$key]['PictureSmall'] = str_replace("http://", "https://", $meeting[$key]['PictureSmall']);
                $meeting[$key]['PictureBig'] = str_replace("http:https", "https:", $meeting[$key]['PictureBig']);
                $meeting[$key]['PictureSmall'] = str_replace("http:https", "https:", $meeting[$key]['PictureSmall']);
                //$meeting[$key]['PictureSmall'] = $value['PictureSmall'];

                $meeting[$key]['cangjia'] = base64_encode_new($meetmain);
                $meeting[$key]['cangjia2'] = base64_encode_new($meetmain2);
                $meeting[$key]['PersonSign'] = $value['PersonSign'];
                $meeting[$key]['ContactPhone'] = $value['ContactPhone'];
                $meeting[$key]['ContactMobile'] = $value['ContactMobile'];


                $join_right = "";
                $join_main = "";
                if ($value['IsMainMeeting'] == "1" || $value['IsChildMeeting'] == 1 || $value['IsChildMeeting2'] == 1) {
                    $join_main = "1";
                }
                if ($join_main == 1 || $value['IsChildMeeting4'] == 1) {
                    $join_right = "1";
                }
                $meeting[$key]['IsGetMoney'] = $value['IsGetMoney'] == "0" ? "0" : "1";
                $meeting[$key]['IsCheckIned'] = $value['IsCheckIned'];
                //2015.4
                //$meeting[$key]['IsMainMeeting'] = $join_main=="1" ? "1" : $value['IsMainMeeting'];
                //$meeting[$key]['IsChildMeeting'] = $join_main=="1" ? "1" : $value['IsChildMeeting'];
                //$meeting[$key]['IsChildMeeting2'] = $join_main=="1" ? "1" : $value['IsChildMeeting2'];
                //$meeting[$key]['IsChildMeeting3'] = $join_right=="1" ? "1" : $value['IsChildMeeting3'];
                //$meeting[$key]['IsChildMeeting4'] = $value['IsChildMeeting4'];
                //$meeting[$key]['IsChildMeeting5'] = $join_right=="1" ? "1" : $value['IsChildMeeting5'];
                //$meeting[$key]['IsChildMeeting6'] = $join_right=="1" ? "1" : $value['IsChildMeeting6'];
                //$meeting[$key]['IsChildMeeting7'] = $join_right=="1" ? "1" : $value['IsChildMeeting7'];
                //$meeting[$key]['IsChildMeeting8'] = $join_right=="1" ? "1" : $value['IsChildMeeting8'];
                //$meeting[$key]['IsChildMeeting9'] = $value['IsChildMeeting9'];
                //$meeting[$key]['IsChildMeeting10'] = $value['IsChildMeeting10'];
                //$meeting[$key]['TableNumber'] = $value['TableNumber'];

                //2015.9
                $meeting[$key]['IsMainMeeting'] = $value['IsMainMeeting'];
                $meeting[$key]['IsChildMeeting'] = $value['IsChildMeeting'];
                $meeting[$key]['IsChildMeeting2'] = $value['IsChildMeeting2'];
                $meeting[$key]['IsChildMeeting3'] = $value['IsChildMeeting3'];
                $meeting[$key]['IsChildMeeting4'] = $value['IsChildMeeting4'];
                $meeting[$key]['IsChildMeeting5'] = $value['IsChildMeeting5'];
                $meeting[$key]['IsChildMeeting6'] = $value['IsChildMeeting6'];
                $meeting[$key]['IsChildMeeting7'] = $value['IsChildMeeting7'];
                $meeting[$key]['IsChildMeeting8'] = $value['IsChildMeeting8'];
                $meeting[$key]['IsChildMeeting9'] = $value['IsChildMeeting9'];
                $meeting[$key]['IsChildMeeting10'] = $value['IsChildMeeting10'];
                $meeting[$key]['TableNumber'] = $value['TableNumber'];
                $meeting[$key]['remark'] = base64_encode_new($value['remark']); //add by zhangcun 2017/2/27
                $meeting[$key]['xuhao'] = $value['xuhao'];
            }
            $arr['Success'] = '1';
            $arr['Message'] = base64_encode_new('参会人员信息');
            $arr['Results'] = $meeting;


        }
        $json_string = $this->pri_JSON($arr);
        if ($needCompress == 1) {
            $json_string = base64_encode(gzencode($json_string, 9));//压缩接口
        }
        echo $json_string;
    }

    /**
     * 使用特定function对数组中所有元素做处理
     *
     * @param string $ &$array     要处理的字符串
     * @param string $function 要执行的函数
     * @return boolean $apply_to_keys_also     是否也应用到key上
     * @access private
     */
    private function pri_arrayRecursive(&$array, $function, $apply_to_keys_also = false)
    {
        static $recursive_counter = 0;
        if (++$recursive_counter > 1000) {
            die('possible deep recursion attack');
        }
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $this->pri_arrayRecursive($array[$key], $function, $apply_to_keys_also);
            } else {
                $array[$key] = $function($value);
                // $array[$key] = $function(addslashes($value));
            }
            if ($apply_to_keys_also && is_string($key)) {
                $new_key = $function($key);
                if ($new_key != $key) {
                    $array[$new_key] = $array[$key];
                    unset($array[$key]);
                }
            }
        }
        $recursive_counter--;
    }

    private function pri_JSON($array)
    {
        $this->pri_arrayRecursive($array, 'urlencode', true);
        // $json = json_encode($array, JSON_HEX_TAG|JSON_HEX_APOS|JSON_HEX_QUOT|JSON_HEX_AMP);
        $json = json_encode($array);
         return urldecode($json);
        // iconv("GB2312","UTF-8",$json_string);
        //return iconv("GB2312", "UTF-8", urldecode($json));
    }




    //****************************
    //
    //		add by nicc  start
    //
    //****************************

    //设置交费状态
    public function SetFinStatus($params)
    {

        // start
        $GUID = $params['GUID'];
        $signcs = $params['SignCS'];
        $QRCode = base64_decode($params['QRCode']);
        $meetid = $params['ID'];
        $BHcode = $params['BHcode'];

        $arr = array('Success' => '0',
            'Message' => 'null',
        );

        if ($signcs != '' && $GUID != '') {

            $loguser = $this->getUser($GUID, $signcs);

            if ($loguser['Uid'] == "391935" || $loguser['Uid'] == "416957" ||$loguser['Uid'] == "429889"||$loguser['Uid'] == "453660") {

            } else {
                
                $arr = array('Success' => '0',
                    'Message' => base64_encode_new('您无权操作财务缴费'),
                );

                $json_string = $this->pri_JSON($arr);
                echo $json_string;
                exit;
            }


            if (!empty($BHcode)) {
                $user = $this->_dao->getRow("select * from meeting_member_contactman where PersonSign='$BHcode' and Mtid = '$meetid' and Mtid != '45' and status=1 limit 1", 0);
                if ($user['IsEn'] == "1") {

                    $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,MidEn as Mid from meeting_member where MidEn='" . $user['Mid'] . "' and Mtid='$meetid'", 0);
                } else {

                    $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,Mid from meeting_member where Mid='" . $user['Mid'] . "' and Mtid='$meetid'", 0);
                }


            } else {
                // 添加微信二维码判断
                //$data = explode("|" , $QRCode);
                $data = $this->decodeQRCode($QRCode);
                $guid = $data[0];
                $mbid = $data[1];
                $maid = $data[2];
                $bzf = $data[3];
                $isen = $data[4];
                if (empty($isen)) $isen = "0";
                $user = $this->_dao->getRow("select * from meeting_member_contactman where Maid='$maid' and Mtid = '$meetid' and status=1 limit 1", 0);
                if ($isen != "1") {
                    $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,Mid from meeting_member where Mid='" . $user['Mid'] . "' and Mtid='$meetid'", 0);
                } else {
                    $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,MidEn as Mid from meeting_member where MidEn='" . $user['MidEn'] . "' and Mtid='$meetid'", 0);
                }
            }
            $member['compfname'] = str_replace("'", "\'", $member['compfname']);
            if ($user['IsGetMoney'] == "1" || $user['IsGetMoney'] == "2" || $user['IsGetMoney'] == "3") // 已交费或者免缴费
            {
                if ($user['IsGuest'] == "0") {
                    $contype = "嘉宾";
                }
                if ($user['IsGuest'] == "1") {
                    $contype = "重要嘉宾";
                }
                if ($user['MoneyOpTime'] != "0000-00-00 00:00:00") {
                    $moneytime = "已于" . $user['MoneyOpTime'];
                }

                $arr['Success'] = '1';
                $arr['Message'] = base64_encode_new("该参会" . $contype . "(" . $user['TrueName'] . ")" . $moneytime . "已缴费");
                $arr['IsEn'] = $user['IsEn'];
                $arr['Result'] = ""; //$aa;

            } else // 未缴费
            {
                if ($user) {
                    $loginman = $this->_dao->getRow("SELECT * from meeting_app_session where GUID='$GUID' ", 0);
                    $user['TrueName'] = str_replace("'", "\'", $user['TrueName']);

                    $this->_dao->execute("UPDATE meeting_member_contactman SET IsGetMoney='1', MoneyOpTime=NOW(),MoneyOpMan='" . $loginman['UserName'] . "' WHERE Mtid='$meetid' and Mid='" . $user['Mid'] . "' AND Maid='" . $user['Maid'] . "' ");

                    if ($user['IsGuest'] == 0) {
                        $logtitle = "嘉宾缴费";
                        $log = "操作人：" . $loginman['TrueName'] . "日期：" . date('Y-m-d H:i:s') . "增加" . $member['compfname'] . "的嘉宾" . $user['TrueName'] . "缴费信息";
                    } elseif ($user['IsGuest'] == 1) {
                        $logtitle = "重要嘉宾缴费";
                        $log = "操作人：" . $loginman['TrueName'] . "日期：" . date('Y-m-d H:i:s') . "增加" . $member['compfname'] . "的重要嘉宾" . $user['TrueName'] . "缴费信息";
                    }

                    $ip = $this->_dao->getip();

                    $this->_dao->execute("insert into  meeting_logs SET Mtid='" . $meetid . "',LogsType='3',LogsTitle='" . $logtitle . "',LogsMid='" . $user['Mid'] . "',UserId='" . $loginman['Uid'] . "',UserName='" . $loginman['TrueName'] . "', UserIp='" . $ip . "',OperLog='" . $log . "',OperDate=NOW() ;");


                    $arr['Success'] = '1';
                    if ($user['IsGuest'] == 0) {
                        $arr['Message'] = base64_encode_new('参会嘉宾(' . $user['TrueName'] . ')缴费成功');
                    } elseif ($user['IsGuest'] == 1) {
                        $arr['Message'] = base64_encode_new('参会重要嘉宾(' . $user['TrueName'] . ')缴费成功');
                    }
                    $arr['IsEn'] = $user['IsEn'];
                    $arr['Result'] = "";
                } else {
                    $arr = array('Success' => '0',
                        'Message' => base64_encode_new('操作失败'),
                    );
                }
            }
        } else {
            $arr = array('Success' => '0',
                'Message' => base64_encode_new('非法操作'),
            );
        }

        $json_string = $this->pri_JSON($arr);
        echo $json_string;
        // end

    }

    public function CheckinHotel($params)
    {

        //$file = fopen("log.txt","a+");
        //$ret = print_R($params,true);
        //fwrite($file,date('Y-m-d H:i:s').'	'.$ret."\r\n".$strParam."\r\n");
        //fclose($file);


        // start
        $GUID = $params['GUID'];
        $signcs = $params['SignCS'];
        $QRCode = base64_decode($params['QRCode']);
        $meetid = $params['ID'];
        $BHcode = $params['BHcode'];

        $hoteltype = $params['HotelType'];
        $hotelno = $params['HotelNo'];

        $arr = array('Success' => '0',
            'Message' => 'null',
        );

        if ($signcs != '' && $GUID != '') {
            if (!empty($BHcode)) {
                $user = $this->_dao->getRow("select * from meeting_member_contactman where PersonSign='$BHcode' and Mtid = '$meetid' and Mtid != '45' and status=1 limit 1", 0);
                if ($user['IsEn'] == "1") {
                    $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,MidEn as Mid from meeting_member where MidEn='" . $user['Mid'] . "' and Mtid='$meetid'", 0);
                } else {
                    $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,Mid from meeting_member where Mid='" . $user['Mid'] . "' and Mtid='$meetid'", 0);
                }
            } else {
                // 添加微信二维码判断
                //$data = explode("|" , $QRCode);
                $data = $this->decodeQRCode($QRCode);
                $guid = $data[0];
                $mbid = $data[1];
                $maid = $data[2];
                $bzf = $data[3];

                $user = $this->_dao->getRow("select * from meeting_member_contactman where Maid='$maid' and Mtid = '$meetid'  and status=1 limit 1", 0);
                if ($user['IsEn'] == "1") {
                    $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,MidEn as Mid from meeting_member  where Mid='$mbid' and Mtid='$meetid'", 0);
                } else {
                    $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,Mid from meeting_member  where Mid='$mbid' and Mtid='$meetid'", 0);
                }
                //$member = $this->_dao->getRow("SELECT compfname,compabb,comtype,Mid from meeting_member where Mid='$mbid' and Mtid='$meetid'", 0);
            }

            if ($user['HotelInType']) // 已入住
            {
                if ($user['IsGuest'] == "0") {
                    $contype = "嘉宾";
                }
                if ($user['IsGuest'] == "1") {
                    $contype = "重要嘉宾";
                }


                $arr['Success'] = '0';
                $arr['Message'] = base64_encode_new("该参会" . $contype . $moneytime . "已入住，房间号" . $user['HotelRoomNumber']);
                $arr['IsEn'] = $user['IsEn'];
                $arr['Result'] = ""; //$aa;

            } else // 未入住
            {
                if ($user) {
                    $loginman = $this->_dao->getRow("SELECT * from meeting_app_session where GUID='$GUID' ", 0);
                    $user['TrueName'] = str_replace("'", "\'", $user['TrueName']);

                    $this->_dao->execute("UPDATE meeting_member_contactman SET HotelInType='$hoteltype', HotelRoomNumber='$hotelno',CheckInedDate=NOW(),CheckInedUserName='" . $loginman['TrueName'] . "' WHERE Mtid='$meetid' AND Maid='" . $user['Maid'] . "' ");
                    //xiangbin注释$this->_dao->execute("UPDATE meeting_member_contactman SET HotelInType='$hoteltype', HotelRoomNumber='$hotelno',CheckinOpTime=NOW(),CheckinOpMan='" . $loginman['UserName'] . "' WHERE Mtid='$meetid' AND Maid='" . $user['Maid'] . "' ");

                    if ($user['IsGuest'] == 0) {
                        $logtitle = "嘉宾入住";
                        $log = "操作人：" . $loginman['TrueName'] . "日期：" . date('Y-m-d H:i:s') . "增加" . $member['compfname'] . "的嘉宾" . $user['TrueName'] . "入住信息";
                    } elseif ($user['IsGuest'] == 1) {
                        $logtitle = "重要嘉宾入住";
                        $log = "操作人：" . $loginman['TrueName'] . "日期：" . date('Y-m-d H:i:s') . "增加" . $member['compfname'] . "的重要嘉宾" . $user['TrueName'] . "入住信息";
                    }

                    $ip = $this->_dao->getip();

                    $this->_dao->execute("insert into  meeting_logs SET Mtid='" . $meetid . "',LogsType='3',LogsTitle='" . $logtitle . "',LogsMid='" . $user['Mid'] . "',UserId='" . $loginman['Uid'] . "',UserName='" . $loginman['TrueName'] . "', UserIp='" . $ip . "',OperLog='" . $log . "',OperDate=NOW() ;");


                    $arr['Success'] = '1';
                    if ($user['IsGuest'] == 0) {
                        $arr['Message'] = base64_encode_new('参会嘉宾入住成功');
                        $arr['IsEn'] = $user['IsEn'];
                    } elseif ($user['IsGuest'] == 1) {
                        $arr['Message'] = base64_encode_new('参会重要嘉宾入住成功');
                        $arr['IsEn'] = $user['IsEn'];
                    }

                    $arr['Result'] = "";
                } else {
                    $arr = array('Success' => '0',
                        'Message' => base64_encode_new('操作失败'),
                    );
                }
            }
        } else {
            $arr = array('Success' => '0',
                'Message' => base64_encode_new('非法操作'),
            );
        }

        $json_string = $this->pri_JSON($arr);
        echo $json_string;
        // end

    }


    //抽奖登记接口
    public function SetAward($params)
    {


        //特等奖：1;一等奖：3;二等奖：20;三等奖：50 ，共2轮， 每轮：25名
        $AwardType1 = 3;//一等奖
        $AwardType2 = 6;//三等奖一轮
        $AwardType3 = 10;//二等奖
        $AwardType4 = 1;//特等奖
        $AwardType5 = 25;//三等奖二轮

        // 2015 1二等奖 2三等奖 3参与奖 4一等奖
        //$AwardType1 = 3;//二等奖
        //$AwardType2 = 10;//三等奖
        //$AwardType3 = 20;//参与奖
        //$AwardType4 = 1;//一等奖

        //1一等奖 2二等奖 3三等奖 4代表特等奖
        //$AwardType1 = 3;//一等奖
        //$AwardType2 = 10;//二等奖
        //$AwardType3 = 80;//三等奖
        //$AwardType4 = 1;//特等奖

        // start
        $GUID = $params['GUID'];
        $signcs = $params['SignCS'];
        $QRCode = base64_decode($params['QRCode']);
        $meetid = $params['ID'];
        $BHcode = $params['BHcode'];

        $awardtype = $params['AwardType'];


        if (strstr($awardtype, "123123")) {
            $awardtype = substr($awardtype, -1);
            if (!empty($BHcode)) {
                $user = $this->_dao->getRow("select * from meeting_member_contactman where PersonSign='$BHcode' and (Mtid = '$meetid' or Mtid = '45') and status=1  limit 1", 0);
            }
            $this->_dao->execute("Delete From meeting_member_award where Mtid = '$meetid' and Cid = '" . $user['ID'] . "' and AwardType ='$awardtype'");

            $arr = array('Success' => '1',
                'Message' => base64_encode_new('取消成功'),
            );
            $json_string = $this->pri_JSON($arr);
            echo $json_string;
            exit;
        }

        $arr = array('Success' => '0',
            'Message' => 'null',
        );


        if ($signcs != '' && $GUID != '') {

            $loguser = $this->getUser($GUID, $signcs);
            if ($loguser['Uid'] == "391935" || $loguser['Uid'] == "147803") {

            } else {

                $arr = array('Success' => '0',
                    'Message' => base64_encode_new('您无权登记抽奖'),
                );

                $json_string = $this->pri_JSON($arr);
                echo $json_string;
                exit;
            }


            if (!empty($BHcode)) {
                $user = $this->_dao->getRow("select * from meeting_member_contactman where PersonSign='$BHcode' and (Mtid = '$meetid' or Mtid = '45') and status=1  limit 1", 0);
                if ($user['IsEn'] == "1") {
                    $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,MidEn as Mid from meeting_member where MidEn='" . $user['Mid'] . "' and Mtid='$meetid'", 0);
                } else {
                    $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,Mid from meeting_member where Mid='" . $user['Mid'] . "' and Mtid='$meetid'", 0);
                }
            } else {
                // 添加微信二维码判断
                //$data = explode("|" , $QRCode);
                $data = $this->decodeQRCode($QRCode);
                $guid = $data[0];
                $mbid = $data[1];
                $maid = $data[2];
                $bzf = $data[3];

                $user = $this->_dao->getRow("select * from meeting_member_contactman where Maid='$maid' and (Mtid = '$meetid' or Mtid=45) and status=1 limit 1", 0);
                if ($user['IsEn'] == "1") {
                    $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,MidEn as Mid from meeting_member where MidEn='$mbid' and Mtid='$meetid'", 0);
                } else {
                    $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,Mid from meeting_member where Mid='$mbid' and Mtid='$meetid'", 0);
                }
            }

            //判断记录
            $ish = $this->_dao->getRow("select * from meeting_member_award where Mtid = '$meetid' and PersonSign = '" . $user['PersonSign'] . "' limit 1");


            if ($ish) // 已存在
            {
                if ($user['IsGuest'] == "0") {
                    $contype = "嘉宾";
                }
                if ($user['IsGuest'] == "1") {
                    $contype = "重要嘉宾";
                }


                $arr['Success'] = '0';
                $arr['Message'] = base64_encode_new("该参会" . $contype . $moneytime . "已录入抽奖登记");
                $arr['IsEn'] = $user['IsEn'];
                $arr['Result'] = ""; //$aa;

            } else // 不存在
            {
                if ($user) {
                    //|| $user['IsGetMoney'] == "0"
                    if ($user['IsCheckIned'] != "1" || $user['Mtid'] == "45") {
                        $arr['Success'] = '0';
                        $arr['IsEn'] = $user['IsEn'];
                        if ($user['Mtid'] == "45") {
                            $arr['Message'] = base64_encode_new("非常抱歉您输入的参会嘉宾编号无效");
                        } else if ($user['IsCheckIned'] != "1") {
                            $arr['Message'] = base64_encode_new("非常抱歉您输入的参会嘉宾编号无效");
                        } else {
                            $arr['Message'] = base64_encode_new("非常抱歉您输入的参会嘉宾编号无效");
                        }
                    } else {

                        $nus = $this->_dao->getOne("select count(1) from meeting_member_award where Mtid = '$meetid' and AwardType ='$awardtype' and (Status = 0 or Status = 1) ");

                        if (($nus >= $AwardType1 && $awardtype == "1") || ($nus >= $AwardType2 && $awardtype == "2") || ($nus >= $AwardType3 && $awardtype == "3") || ($nus >= $AwardType4 && $awardtype == "4") || ($nus >= $AwardType5 && $awardtype == "5")) {

                            $arr['Success'] = '0';
                            $arr['Message'] = base64_encode_new("类型" . $awardtype . "抽奖名额已使用完");
                            $arr['IsEn'] = $user['IsEn'];
                        } else {

                            $loginman = $this->_dao->getRow("SELECT * from meeting_app_session where GUID='$GUID' ", 0);
                            $user['TrueName'] = str_replace("'", "\'", $user['TrueName']);


                            $this->_dao->execute("insert into meeting_member_award set Mtid = '$meetid',Cid = '" . $user['ID'] . "',Mid = '" . $user['Mid'] . "',PersonSign = '" . $user['PersonSign'] . "',AwardType ='$awardtype',Status = 0,CreateDate=NOW(),CreateUser='" . $loginman['UserName'] . "' ");


                            if ($user['IsGuest'] == 0) {
                                $logtitle = "嘉宾抽奖";
                                $log = "操作人：" . $loginman['TrueName'] . "日期：" . date('Y-m-d H:i:s') . "增加" . $member['compfname'] . "的嘉宾" . $user['TrueName'] . "抽奖信息";
                            } elseif ($user['IsGuest'] == 1) {
                                $logtitle = "重要嘉宾抽奖";
                                $log = "操作人：" . $loginman['TrueName'] . "日期：" . date('Y-m-d H:i:s') . "增加" . $member['compfname'] . "的重要嘉宾" . $user['TrueName'] . "抽奖信息";
                            }

                            $ip = $this->_dao->getip();

                            $this->_dao->execute("insert into  meeting_logs SET Mtid='" . $meetid . "',LogsType='3',LogsTitle='" . $logtitle . "',LogsMid='" . $user['Mid'] . "',UserId='" . $loginman['Uid'] . "',UserName='" . $loginman['TrueName'] . "', UserIp='" . $ip . "',OperLog='" . $log . "',OperDate=NOW() ;");


                            $arr['Success'] = '1';
                            if ($user['IsGuest'] == 0) {
                                $arr['Message'] = base64_encode_new('参会嘉宾抽奖登记成功');
                                $arr['IsEn'] = $user['IsEn'];
                            } elseif ($user['IsGuest'] == 1) {
                                $arr['Message'] = base64_encode_new('参会重要嘉宾抽奖登记成功');
                                $arr['IsEn'] = $user['IsEn'];
                            }

                            $arr['Result'] = "";

                        }
                    }
                } else {
                    $arr = array('Success' => '0',
                        'Message' => base64_encode_new('操作失败'),
                    );
                }
            }
        } else {
            $arr = array('Success' => '0',
                'Message' => base64_encode_new('非法操作'),
            );
        }

        $json_string = $this->pri_JSON($arr);
        echo $json_string;
        // end

    }


    //抽奖领奖接口

    public function GetAward($params)
    {
        // start
        $GUID = $params['GUID'];
        $signcs = $params['SignCS'];
        $QRCode = base64_decode($params['QRCode']);
        $meetid = $params['ID'];
        $BHcode = $params['BHcode'];

        $awardtype = $params['AwardType'];


        $arr = array('Success' => '0',
            'Message' => 'null',
        );

        if ($signcs != '' && $GUID != '') {
            if (!empty($BHcode)) {
                $user = $this->_dao->getRow("select * from meeting_member_contactman where PersonSign='$BHcode' and (Mtid = '$meetid' or Mtid = '45')  and status=1 limit 1", 0);
                if ($user['IsEn'] == "1") {
                    $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,MidEn as Mid  from meeting_member where MidEn='" . $user['Mid'] . "' and Mtid='$meetid'", 0);
                } else {
                    $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,Mid from meeting_member where Mid='" . $user['Mid'] . "' and Mtid='$meetid'", 0);
                }
            } else {
                // 添加微信二维码判断
                //$data = explode("|" , $QRCode);
                $data = $this->decodeQRCode($QRCode);
                $guid = $data[0];
                $mbid = $data[1];
                $maid = $data[2];
                $bzf = $data[3];

                $user = $this->_dao->getRow("select * from meeting_member_contactman where Maid='$maid' and (Mtid = '$meetid' or Mtid=45) and status=1 limit 1", 0);
                if ($user['IsEn'] == "1") {
                    $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,MidEn as Mid from meeting_member where MidEn='$mbid' and Mtid='$meetid'", 0);
                } else {
                    $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,Mid from meeting_member where Mid='$mbid' and Mtid='$meetid'", 0);
                }
            }

            //判断记录
            $ish = $this->_dao->getRow("select * from meeting_member_award where Mtid = '$meetid' and Cid = '" . $user['ID'] . "' and AwardType = '$awardtype'  limit 1");


            if (($ish['Status'] == "1" || $ish['Status'] == "2") && $user) // 已存在  已取消
            {
                if ($user['IsGuest'] == "0") {
                    $contype = "嘉宾";
                }
                if ($user['IsGuest'] == "1") {
                    $contype = "重要嘉宾";
                }

                if ($ish['Status'] == "1") {
                    $cont = "已于" . $ish['GetAwardTime'] . "领奖";
                }
                if ($ish['Status'] == "2") {
                    $cont = "已于" . $ish['CancerTime'] . "取消领奖资格";
                }

                $arr['Success'] = '0';
                $arr['Message'] = base64_encode_new("该参会" . $contype . $cont);
                $arr['PersonSign'] = $user['PersonSign'];
                $arr['IsEn'] = $user['IsEn'];
                //$arr['IsEn']=$user['IsEn'];
                $arr['Result'] = ""; //$aa;

            } else // 不存在
            {
                if ($user && $ish)        //领奖
                {


                    $this->_dao->execute("update meeting_member_award set Status = '1',GetAwardTime=NOW() where Mtid ='$meetid' and Cid = '" . $user['ID'] . "' ");


                    $logtitle = "领奖";
                    $log = "日期：" . date('Y-m-d H:i:s') . $member['compfname'] . "的嘉宾" . $user['TrueName'] . "领奖";


                    $ip = $this->_dao->getip();

                    $this->_dao->execute("insert into  meeting_logs SET Mtid='" . $meetid . "',LogsType='3',LogsTitle='" . $logtitle . "',LogsMid='" . $user['Mid'] . "', UserIp='" . $ip . "',OperLog='" . $log . "',OperDate=NOW() ;");


                    $arr['Success'] = '1';
                    $arr['IsEn'] = $user['IsEn'];
                    if ($user['IsGuest'] == 0) {
                        $arr['Message'] = base64_encode_new('参会嘉宾领奖成功');
                    } elseif ($user['IsGuest'] == 1) {
                        $arr['Message'] = base64_encode_new('参会重要嘉宾领奖成功');
                    }
                    $arr['PersonSign'] = $user['PersonSign'];


                    $arr['Result'] = "";
                } else {
                    $arr = array('Success' => '0',
                        'Message' => base64_encode_new('操作失败,无此嘉宾'),
                    );
                }
            }
        } else {
            $arr = array('Success' => '0',
                'Message' => base64_encode_new('非法操作'),
            );
        }

        $json_string = $this->pri_JSON($arr);
        echo $json_string;
        // end

    }


    /*
	CREATE TABLE IF NOT EXISTS `meeting_member_award_all` (
	`ID` int(11) NOT NULL auto_increment,
	`Mtid` int(11) NOT NULL,
	`Cid` int(11) NOT NULL,
	`Mid` int(11) NOT NULL,
	`PersonSign` varchar(50) NOT NULL,
	`AwardType` tinyint(4) NOT NULL,
	`Status` tinyint(4) NOT NULL,
	`CreateDate` datetime NOT NULL,
	`CreateUser` varchar(50) NOT NULL,
	`GetAwardTime` datetime NOT NULL,
	`CancerTime` datetime NOT NULL,
	PRIMARY KEY  (`ID`)
	) ENGINE=MyISAM  DEFAULT CHARSET=latin1
	*/
    //抽奖领奖接口(全体三等奖)
    public function GetAwardAll($params)
    {
        // start
        $GUID = $params['GUID'];
        $signcs = $params['SignCS'];
        $QRCode = base64_decode($params['QRCode']);
        $meetid = $params['ID'];
        $BHcode = $params['BHcode'];

        $awardtype = $params['AwardType'];


        $arr = array('Success' => '0',
            'Message' => 'null',
        );

        if ($signcs != '' && $GUID != '') {
            if (!empty($BHcode)) {
                $user = $this->_dao->getRow("select * from meeting_member_contactman where PersonSign='$BHcode' and (Mtid = '$meetid' or Mtid = '45') and status=1 limit 1", 0);
                if ($user['IsEn'] == "1") {
                    $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,MidEn as Mid from meeting_member where MidEn='" . $user['Mid'] . "' and Mtid='$meetid'", 0);
                } else {
                    $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,Mid from meeting_member where Mid='" . $user['Mid'] . "' and Mtid='$meetid'", 0);
                }
            } else {
                // 添加微信二维码判断
                //$data = explode("|" , $QRCode);
                $data = $this->decodeQRCode($QRCode);
                $guid = $data[0];
                $mbid = $data[1];
                $maid = $data[2];
                $bzf = $data[3];

                $user = $this->_dao->getRow("select * from meeting_member_contactman where Maid='$maid' and (Mtid = '$meetid' or Mtid=45) and status=1 limit 1", 0);
                if ($user['IsEn'] == "1") {
                    $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,MidEn as Mid  from meeting_member where MidEn='$mbid' and Mtid='$meetid'", 0);
                } else {
                    $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,Mid from meeting_member where Mid='$mbid' and Mtid='$meetid'", 0);
                }
            }

            //判断记录
            $ish = $this->_dao->getRow("select * from meeting_member_award_all where Mtid = '$meetid' and Cid = '" . $user['ID'] . "' and AwardType = '$awardtype'  limit 1");


            if ($ish && ($ish['Status'] == "1" || $ish['Status'] == "2") && $user) // 已存在  已取消
            {
                if ($user['IsGuest'] == "0") {
                    $contype = "嘉宾";
                }
                if ($user['IsGuest'] == "1") {
                    $contype = "重要嘉宾";
                }

                if ($ish['Status'] == "1") {
                    $cont = "已于" . $ish['GetAwardTime'] . "领奖";
                }
                if ($ish['Status'] == "2") {
                    $cont = "已于" . $ish['CancerTime'] . "取消领奖资格";
                }

                $arr['Success'] = '0';
                $arr['Message'] = base64_encode_new("该参会" . $contype . $cont);
                $arr['PersonSign'] = $user['PersonSign'];
                $arr['IsEn'] = $user['IsEn'];
                $arr['Result'] = ""; //$aa;

            } else // 不存在 直接领奖
            {
                if ($user)        //领奖
                {

                    //$this->_dao->execute("update meeting_member_award set Status = '1',GetAwardTime=NOW() where Mtid ='$meetid' and Cid = '".$user['ID']."' ");
                    //生成领奖记录
                    $this->_dao->execute("insert into meeting_member_award_all set Mtid = '$meetid',Cid = '" . $user['ID'] . "',Mid = '" . $user['Mid'] . "',PersonSign = '" . $user['PersonSign'] . "',AwardType ='$awardtype',Status = 1,GetAwardTime=NOW(),CreateDate=NOW(),CreateUser='" . $loginman['UserName'] . "' ");

                    $logtitle = "领奖";
                    $log = "日期：" . date('Y-m-d H:i:s') . $member['compfname'] . "的嘉宾" . $user['TrueName'] . "领奖";


                    $ip = $this->_dao->getip();

                    $this->_dao->execute("insert into  meeting_logs SET Mtid='" . $meetid . "',LogsType='3',LogsTitle='" . $logtitle . "',LogsMid='" . $user['Mid'] . "', UserIp='" . $ip . "',OperLog='" . $log . "',OperDate=NOW() ;");


                    $arr['Success'] = '1';
                    $arr['IsEn'] = $user['IsEn'];
                    if ($user['IsGuest'] == 0) {
                        $arr['Message'] = base64_encode_new('参会嘉宾领奖成功');
                    } elseif ($user['IsGuest'] == 1) {
                        $arr['Message'] = base64_encode_new('参会重要嘉宾领奖成功');
                    }
                    $arr['PersonSign'] = $user['PersonSign'];


                    $arr['Result'] = "";
                } else {
                    $arr = array('Success' => '0',
                        'Message' => base64_encode_new('操作失败,无此嘉宾'),
                    );
                }
            }
        } else {
            $arr = array('Success' => '0',
                'Message' => base64_encode_new('非法操作'),
            );
        }

        $json_string = $this->pri_JSON($arr);
        echo $json_string;
        // end

    }

    //取消领奖接口
    public function CancerAward($params)
    {
        // start
        $GUID = $params['GUID'];
        $signcs = $params['SignCS'];
        $QRCode = base64_decode($params['QRCode']);
        $meetid = $params['ID'];
        $BHcode = $params['BHcode'];

        $awardtype = $params['AwardType'];


        $arr = array('Success' => '0',
            'Message' => 'null',
        );

        if ($signcs != '' && $GUID != '') {
            if (!empty($BHcode)) {
                $user = $this->_dao->getRow("select * from meeting_member_contactman where PersonSign='$BHcode' and (Mtid = '$meetid' or Mtid = '45')  and status=1 limit 1", 0);
                if ($user['IsEn'] == "1") {
                    $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,MidEn as Mid from meeting_member where MidEn='" . $user['Mid'] . "' and Mtid='$meetid'", 0);
                } else {
                    $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,Mid from meeting_member where Mid='" . $user['Mid'] . "' and Mtid='$meetid'", 0);
                }
            } else {
                // 添加微信二维码判断
                //$data = explode("|" , $QRCode);
                $data = $this->decodeQRCode($QRCode);
                $guid = $data[0];
                $mbid = $data[1];
                $maid = $data[2];
                $bzf = $data[3];

                $user = $this->_dao->getRow("select * from meeting_member_contactman where Maid='$maid' and (Mtid = '$meetid' or Mtid=45) and status=1 limit 1", 0);
                if ($user['IsEn'] == "1") {
                    $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,MidEn as Mid from meeting_member where MidEn='$mbid' and Mtid='$meetid'", 0);
                } else {
                    $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,Mid from meeting_member where Mid='$mbid' and Mtid='$meetid'", 0);
                }
            }

            //判断记录
            $ish = $this->_dao->getRow("select * from meeting_member_award where Mtid = '$meetid' and Cid = '" . $user['ID'] . "' and AwardType = '$awardtype'  limit 1");


            if (($ish['Status'] == "1" || $ish['Status'] == "2") && $user) // 已领奖  已取消
            {
                if ($user['IsGuest'] == "0") {
                    $contype = "嘉宾";
                }
                if ($user['IsGuest'] == "1") {
                    $contype = "重要嘉宾";
                }

                if ($ish['Status'] == "1") {
                    $cont = "已于" . $ish['GetAwardTime'] . "领奖";
                }
                if ($ish['Status'] == "2") {
                    $cont = "已于" . $ish['CancerTime'] . "取消领奖资格";
                }

                $arr['Success'] = '0';
                $arr['Message'] = base64_encode_new("该参会" . $contype . $cont);
                $arr['IsEn'] = $user['IsEn'];
                $arr['Result'] = ""; //$aa;

            } else // 不存在
            {
                if ($user)        //取消领奖
                {


                    $this->_dao->execute("update meeting_member_award set Status = '2',CancerTime=NOW() where Mtid ='$meetid' and Cid = '" . $user['ID'] . "' ");


                    $logtitle = "取消领奖";
                    $log = "日期：" . date('Y-m-d H:i:s') . $member['compfname'] . "的嘉宾" . $user['TrueName'] . "取消领奖";


                    $ip = $this->_dao->getip();

                    $this->_dao->execute("insert into  meeting_logs SET Mtid='" . $meetid . "',LogsType='3',LogsTitle='" . $logtitle . "',LogsMid='" . $user['Mid'] . "', UserIp='" . $ip . "',OperLog='" . $log . "',OperDate=NOW() ;");


                    $arr['Success'] = '1';
                    $arr['IsEn'] = $user['IsEn'];
                    if ($user['IsGuest'] == 0) {
                        $arr['Message'] = base64_encode_new('取消成功');
                    } elseif ($user['IsGuest'] == 1) {
                        $arr['Message'] = base64_encode_new('取消成功');
                    }

                    $arr['Result'] = "";
                } else {
                    $arr = array('Success' => '0',
                        'Message' => base64_encode_new('操作失败,无此嘉宾'),
                    );
                }
            }
        } else {
            $arr = array('Success' => '0',
                'Message' => base64_encode_new('非法操作'),
            );
        }

        $json_string = $this->pri_JSON($arr);
        echo $json_string;
        // end

    }


    public function GetAwardList($params)
    {
        // start
        $GUID = $params['GUID'];
        $signcs = $params['SignCS'];
        $meetid = $params['ID'];

        $awardtype = $params['AwardType'];


        $arr = array('Success' => '0',
            'Message' => 'null',
        );

        if ($signcs != '' && $GUID != '' && $meetid) {
            $awarr = $this->_dao->query("SELECT cm.ID as Cid, cm.TrueName, cm.Maid, cm.PersonSign, cm.PictureBig 
	FROM `meeting_member_award` 
	LEFT JOIN meeting_member_contactman as cm ON meeting_member_award.cid = cm.id
	WHERE meeting_member_award.Mtid =$meetid
	AND meeting_member_award.AwardType =$awardtype
	AND meeting_member_award.Status !=2 and cm.status=1
	ORDER BY `meeting_member_award`.`CreateDate` ASC				
					");

            foreach ($awarr as &$tmp) {

                $tmp['TrueName'] = base64_encode_new($tmp['TrueName']);

                if ($tmp['PictureBig'] == "") {
                    $urow = $this->_dao->getRow("select PictureBig,PictureSmall from member_contact_user where user_id ='" . $tmp['Maid'] . "' ", 0);

                    $tmp['PictureBig'] = $urow['PictureBig'];

                }


                //$tmp['PictureBig'] =  $tmp['PictureBig'];
                $tmp['PictureBig'] = $tmp['PictureBig'] == '' ? STEELHOME_SITE : (strstr($tmp['PictureBig'], "http://") ? $tmp['PictureBig'] : (strstr($tmp['PictureBig'], "//") ? 'http:' . $tmp['PictureBig'] : STEELHOME_SITE . $tmp['PictureBig']));
                $tmp['PictureBig'] = str_replace("http:////", "http://", $tmp['PictureBig']);
                $tmp['PictureBig'] = str_replace("http://", "https://", $tmp['PictureBig']);

                $st = $this->_dao->getOne("select Status  from meeting_member_award where Cid = '" . $tmp['Cid'] . "'  ", 0);

                $tmp['Status'] = $st;

                unset($tmp['Maid']);

            }
            $arr['Success'] = '1';
            $arr['Message'] = base64_encode_new('获取成功');
            $arr['Results'] = $awarr;


        } else {
            $arr = array('Success' => '0',
                'Message' => base64_encode_new('非法操作'),
            );
        }

        $json_string = $this->pri_JSON($arr);
        echo $json_string;
        // end
    }


    //取得已登陆用户
    public function getUser($GUID, $SignCS)
    {
        return $this->_dao->getRow("SELECT * FROM meeting_app_session WHERE GUID = '$GUID'");
    }


    public function SetRun($params)
    {
        $array = array("Mid", "CompfnameS", "PersonSign", "TrueName", "Sex", "Post", "ContactPhone", "ContactMobile", "Runame");

        $row = $this->_dao->getRow("select * from meeting_member_contactman where ID = '" . $params['Cid'] . "' limit 1", 0);

        $params['CheckInedDate'] = base64_decode($params['CheckInedDate']);


        $dd = $this->getdata($array, $row);

        $this->_dao->execute("insert into meeting_member_ruchang set $dd,Cid='" . $params['Cid'] . "',Mtid = '" . $params['Mtid'] . "',RuType='" . $params['RuType'] . "',CheckInedDate='" . $params['CheckInedDate'] . "',CheckInedUserId='" . $params['CheckInedUserId'] . "',Status='" . $params['Status'] . "' ");

    }


    public function GetStatus($params)
    {


        // start
        $GUID = $params['GUID'];
        $signcs = $params['SignCS'];
        $QRCode = base64_decode($params['QRCode']);
        $meetid = $params['ID'];
        $BHcode = $params['BHcode'];

        $arr = array('Success' => '0',
            'Message' => 'null',
        );

        if ($signcs != '' && $GUID != '') {
            if (!empty($BHcode)) {
                $user = $this->_dao->getRow("select IsEn,IsGetMoney,IsCheckIned,IsMainMeeting,IsChildMeeting,IsChildMeeting2,IsChildMeeting3,IsChildMeeting4,IsChildMeeting5,IsChildMeeting6,IsChildMeeting7,IsChildMeeting8,IsChildMeeting9,IsChildMeeting10,TableNumber from meeting_member_contactman where PersonSign='$BHcode' and (Mtid = '$meetid' or Mtid = '45') and status=1 limit 1", 0);


            } else {
                // 添加微信二维码判断
                //$data = explode("|" , $QRCode);
                $data = $this->decodeQRCode($QRCode);
                $guid = $data[0];
                $mbid = $data[1];
                $maid = $data[2];
                $bzf = $data[3];

                $user = $this->_dao->getRow("select IsEn,IsGetMoney,IsCheckIned,IsMainMeeting,IsChildMeeting,IsChildMeeting2,IsChildMeeting3,IsChildMeeting4,IsChildMeeting5,IsChildMeeting6,IsChildMeeting7,IsChildMeeting8,IsChildMeeting9,IsChildMeeting10,TableNumber from meeting_member_contactman where Maid='$maid' and (Mtid = '$meetid' or Mtid='45') and status=1 limit 1", 0);
            }

            //pandun

            if ($user) {
                $arr['Success'] = "1";
                $arr['Message'] = base64_encode_new("成功");
                $arr['Result'] = $user;
            } else {
                $arr['Success'] = "0";
                $arr['Message'] = base64_encode_new("无此用户");
            }


        } else {
            $arr = array('Success' => '0',
                'Message' => base64_encode_new('非法操作'),
            );
        }

        $json_string = $this->pri_JSON($arr);
        echo $json_string;
        // end
    }


    // 2013/9/22


    public function GetRcType($params)
    {
        $GUID = $params['GUID'];
        $signcs = $params['SignCS'];

        $Mtid = $params['Mtid'];
        $RType = $params['RType'];  // 1: 入场类型 2 ：入住类型

        $IsCy = $params['IsCy'];
        $where = "";

        $arr = array('Success' => '0',
            'Message' => 'null',
        );

        if ($Mtid == "") {
            $arr = array('Success' => '0',
                'Message' => '会议ID不能为空',
            );
        }
        if ($RType == "") {
            $arr = array('Success' => '0',
                'Message' => '获取类型不能为空',
            );
        }

        if ($IsCy == "1") {
            $where .= " and IsRePast = 1";
        } else {
            $where .= " and IsRePast = 0";
        }

        if ($signcs != '' && $GUID != '' && $RType != '' && $Mtid != '') {

            $data = $this->_dao->query("select ID,RcName,Ssort,IsRePast,IsDinner from meeting_set_rc where Mtid = '$Mtid' and rtype ='$RType' and Status = 1 $where order by Ssort  ");

            foreach ($data as &$tmp) {
                $tmp['RcName'] = base64_encode_new($tmp['RcName']);
            }
            //返回json对象
            $arr = array(
                'Success' => '1',
                'Message' => base64_encode_new('获取成功'),
                'Results' => $data
            );
        }


        $json_string = $this->pri_JSON($arr);
        echo $json_string;

    }

    //xiangbinadd20171226
    public function GetHotelType($params)
    {
        $GUID = $params['GUID'];
        $signcs = $params['SignCS'];

        $Mtid = $params['Mtid'];
        $RType = $params['Type'];  // 1: 酒店类型

        $arr = array('Success' => '0',
            'Message' => 'null',
        );

        if ($Mtid == "") {
            $arr = array('Success' => '0',
                'Message' => '会议ID不能为空',
            );
        }
        if ($RType == "") {
            $arr = array('Success' => '0',
                'Message' => '获取类型不能为空',
            );
        }


        if ($signcs != '' && $GUID != '' && $RType == '1' && $Mtid != '') {
            $data = $this->_dao->query("SELECT ID,RoomTypeName FROM meeting_set_hotel WHERE Mtid = $Mtid ORDER BY ordnum ASC");
            foreach ($data as &$tmp) {
                $tmp['RoomTypeName'] = base64_encode_new($tmp['RoomTypeName']);
            }
            //返回json对象
            $arr = array(
                'Success' => '1',
                'Message' => base64_encode_new('获取成功'),
                'Results' => $data
            );
        }


        $json_string = $this->pri_JSON($arr);
        echo $json_string;

    }

    //xiangbinadd20171226


    public function doSetRcDate($params)
    {

        $GUID = $params['GUID'];
        $signcs = $params['SignCS'];

        $dateset = $params['DateSet'];

        $dateset = base64_decode($dateset);


        //$rcarr = json_decode($dateset,true);//最多167条数据，原因不明
        $rcarr = $this->json_decode2($dateset);

        $arr = array('Success' => '0',
            'Message' => 'null',
        );


        $arrays = array("Cid", "RuType", "Runame", "Mtid", "Mid", "CompfnameS", "PersonSign", "TrueName", "Sex", "Post", "ContactPhone", "ContactMobile", "CheckInedDate", "CheckInedUserId", "CheckInedUserName", "GUID", "Status");

        foreach ($rcarr['DateSet'] as $tmp) {
            $ish = $this->_dao->getRow("select * from meeting_member_ruchang where Cid ='" . $tmp['Cid'] . "' and RuType='" . $tmp['RuType'] . "' and Mtid ='" . $tmp['Mtid'] . "' and CheckInedDate = '" . $tmp['CheckInedDate'] . "' ", 0);
            if ($ish) {
            } else {
                $date = $this->getdata64($arrays, $tmp);
                $this->_dao->execute("insert into meeting_member_ruchang set $date");

            }
        }

        //返回json对象
        $arr = array(
            'Success' => '1',
            'Message' => base64_encode_new('更新成功'),

        );

        $json_string = $this->pri_JSON($arr);
        echo $json_string;

    }
    public function doRuChangNew($params)
    {
        $GUID = $params['GUID'];
        $signcs = $params['SignCS'];
        $QRCode = base64_decode(str_replace(" ","+",$params['QRCode']));
        $RuType = $params['RuType'];
        $Runame = str_replace("'", "''", base64_decode_new(str_replace(" ","+",$params['Runame'])));
        $meetid = $params['ID'];
        $BHcode = $params['BHcode'];

        $CheckInedUserId = $params['CheckInedUserId'];
        $CheckInedUserName = str_replace("'", "''", base64_decode_new(str_replace(" ","+",$params['CheckInedUserName'])));

        $arr = array('Success' => '0',
            'Message' => base64_encode_new('操作失败'),
            'MessageColor' => base64_encode_new('Red')
        );
        if ($signcs != '' && $GUID != '') {

            $loginman = $this->_dao->getRow("SELECT * from meeting_app_session where GUID='$GUID' ", 0);

            if (!empty($BHcode)) {
                $user = $this->_dao->getRow("select * from meeting_member_contactman where PersonSign='$BHcode' and (Mtid='$meetid' or Mtid ='45') and status=1 limit 1", 0);

                if ($user['IsEn'] == "1") {
                    $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,MidEn as Mid from meeting_member where   MidEn='" . $user['Mid'] . "' and Mtid='" . $user['Mtid'] . "'", 0);
                } else {
                    $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,Mid from meeting_member where  IsEn='0' and  Mid='" . $user['Mid'] . "' and Mtid='" . $user['Mtid'] . "'", 0);
                }


            } else {
                // 添加微信二维码判断
                $data = $this->decodeQRCode($QRCode);
                $mbid = $data[1];
                $maid = $data[2];
                $bzf = $data[3];
                $isen = $data[4];
                if (empty($isen)) $isen = 0;//添加IsEn字段

                $user = $this->_dao->getRow("SELECT * from meeting_member_contactman where IsEn='$isen' and Mtid='$meetid'  AND Maid='$maid' and status='1' ", 0);//添加IsEn字段

                if ($isen != "1") {
                    $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,Mid from meeting_member where Mid='" . $user['Mid'] . "' and Mtid='" . $user['Mtid'] . "'", 0);

                    if (empty($user)) {
                        $user = $this->_dao->getRow("SELECT * from meeting_member_contactman where IsEn='$isen' and  Mtid='45' AND Maid='$maid' and status='1' ", 0);//添加IsEn字段
                        $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,Mid from meeting_member where Mid='" . $user['Mid'] . "' and Mtid='" . $user['Mtid'] . "'", 0);
                    }
                } else {
                    $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,MidEn as Mid from meeting_member where MidEn='" . $user['Mid'] . "' and Mtid='" . $user['Mtid'] . "'", 0);

                    if (empty($user)) {
                        $user = $this->_dao->getRow("SELECT * from meeting_member_contactman where IsEn='$isen' and  Mtid='45' AND Maid='$maid' and status='1' ", 0);//添加IsEn字段
                        $member = $this->_dao->getRow("SELECT compfname,compabb,comtype,MidEn as Mid from meeting_member where MidEn='" . $user['Mid'] . "' and Mtid='" . $user['Mtid'] . "'", 0);
                    }
                }
            }
            $meeting = array_merge($user, $member);
            $meeting['TrueName'] = str_replace("'", "\'", $meeting['TrueName']);
            $meeting['compfname'] = str_replace("'", "\'", $meeting['compfname']);

            if ($meeting) {
                    $this->_dao->execute("insert into meeting_member_ruchang SET Cid='" . $meeting['ID'] . "',Mtid='" . $meetid . "',Mid='" . $meeting['Mid'] . "',CompfnameS='" . $meeting['compfname'] . "',PersonSign='" . $meeting['PersonSign'] . "',TrueName='" . $meeting['TrueName'] . "',Sex='" . $meeting['Sex'] . "',Post='" . $meeting['Post'] . "',ContactPhone='" . $meeting['ContactPhone'] . "',ContactMobile='" . $meeting['ContactMobile'] . "',CheckInedDate=NOW(),CheckInedUserName='" . $loginman['TrueName'] . "', CheckInedUserId='" . $loginman['Uid'] . "',RuType='" . $RuType . "',Runame='" . $Runame . "',Status='1',GUID='" . $meeting['GUID'] . "'");

                    $num = $this->_dao->getOne("SELECT count(*) as c FROM meeting_member_ruchang WHERE RuType='" . $RuType . "' AND Mtid='" . $meetid . "' AND Cid='" . $meeting['ID'] . "' AND Status='1'", 0);

                    $snum = $this->_dao->getOne("SELECT count(distinct Cid) as c FROM meeting_member_ruchang WHERE RuType='" . $RuType . "' AND Mtid='" . $meetid . "'  AND Status='1' ", 0);

                    //$snumlose = $this->_dao->getOne("SELECT count(distinct Cid) as c FROM meeting_member_ruchang WHERE RuType='" . $RuType . "' AND Mtid='" . $meetid . "'  AND Status='9' ", 0);
                    if ($num == 1) {
                        $arr['Message'] = base64_encode_new("您是第" . $num . "次入场;当前场内人数：" . $snum );
                        $arr['MessageColor'] = base64_encode_new('White');
                    } else {
                        $arr['Message'] = base64_encode_new("您是第" . $num . "次入场;当前场内人数：" . $snum );
                    }
                    $arr['Success'] = '1';
            } else {
                $arr = array('Success' => '0',
                    'Message' => base64_encode_new("操作失败"),
                );

                //失败日志
                $this->_dao->execute("insert into meeting_member_ruchang SET Cid='" . $meeting['ID'] . "',Mtid='" . $meetid . "',Mid='" . $meeting['Mid'] . "',CompfnameS='" . $meeting['compfname'] . "',PersonSign='" . $meeting['PersonSign'] . "',TrueName='" . $meeting['TrueName'] . "',Sex='" . $meeting['Sex'] . "',Post='" . $meeting['Post'] . "',ContactPhone='" . $meeting['ContactPhone'] . "',ContactMobile='" . $meeting['ContactMobile'] . "',CheckInedDate=NOW(),CheckInedUserName='" . $loginman['TrueName'] . "', CheckInedUserId='" . $loginman['Uid'] . "',RuType='" . $RuType . "',Runame='" . $Runame . "',Status='9',GUID='" . $meeting['GUID'] . "'");
            }
        }

        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    public function doRuChangTongji($params)
    {
        $GUID = $params['GUID'];
        $signcs = $params['SignCS'];
        $RuType = $params['RuType'];
        $meetid = $params['ID'];
        if ($signcs != '' && $GUID != '') {
            $snum = $this->_dao->getOne("SELECT count(distinct Cid) as c FROM meeting_member_ruchang WHERE RuType='" . $RuType . "' AND Mtid='" . $meetid . "'  AND Status='1' ", 0);
            $arr['Message'] = base64_encode("当前场内人数：" . $snum );
            $arr['Success'] = '1';        
        }
        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    function json_decode2($json)
    {
        $comment = false;
        $out = '$x=';

        for ($i = 0; $i < strlen($json); $i++) {
            if (!$comment) {
                if (($json[$i] == '{') || ($json[$i] == '[')) $out .= ' array(';
                else if (($json[$i] == '}') || ($json[$i] == ']')) $out .= ')';
                else if ($json[$i] == ':') $out .= '=>';
                else $out .= $json[$i];
            } else $out .= $json[$i];

            if ($json[$i] == '"' && $json[($i - 1)] != "\\") $comment = !$comment;
        }

        eval($out . ';');
        return $x;
    }

    public function bind_ifsuccess($params)
    {
        $mtid = $params['mtid'];
        $token = base64_decode($params['token']);
        $openid = base64_decode($params['openid']);
        $personsign_code = base64_decode($params['personsign_code']);

        $arr = array(
            'Success' => '0',
            'Message' => '',
        );
        $this->getbind_weixin($params);

        $data = explode("|", base64_decode($personsign_code));
        $mid = $data[1];
        $maid = $data[2];
        $usersign = $data[3];
        $isen = empty($data[4]) ? "0" : $data[4];

        if ($mid && $maid) {
            $sql = "select count(id) from weixin_userinfo where Mtid='$mtid' and Mid='$mid' and user_id='$maid' and weixin_openid='$openid' and token='$token' and IsEn='$isen'";
            $num = $this->_dao->getone($sql);
            if ($num == 1) {
                $sql = "update meeting_member_contactman set weixin_openid='" . $openid . "' where mid='" . $mid . "' and maid='" . $maid . "' and ( mtid='" . $mtid . "' or mtid='45' ) order by mtid limit 1";
                $this->_dao->execute($sql);
                $arr['Success'] = 1;
                $arr['Message'] = "绑定成功！";
            } else {
                $arr['Message'] = "绑定失败！";
            }
        } else {
            $arr['Message'] = "信息获取失败！";
        }
    }

    public function unbind_ifsuccess($params)
    {
        $openid = base64_decode($params['openid']);
        $mtid = $params['mtid'];

        $this->unbind_weixin($params);
        $sql = "update meeting_member_contactman set weixin_openid='' where weixin_openid='$openid' and ( mtid='$mtid' or mtid='45' ) order by mtid limit 1";
        $this->_dao->execute($sql);
    }

    public function checkbyopenid($params)
    {
        $openid = base64_decode($params['openid']);
        $token = base64_decode($params['token']);
        $mtid = $params['mtid'];
        if ($openid) {
            $sql = "SELECT Mid,user_id,IsEn from weixin_userinfo where Token='$token' and weixin_openid='$openid' and (Mtid='$mtid' or Mtid='45')"; //echo $sql;
            $mes = $this->_dao->Query($sql);
            $mes = $mes[0];
            if ($mes['Mid'] && $mes['user_id']) {
                //echo base64_encode("1|".$mes['mid']."|".$mes['maid']."|".$mes['user_sign']);
                echo "cg:" . $mes['Mid'] . ":" . $mes['user_id'] . ":" . $mes['IsEn'];
            }
        }
    }

    public function updatewxmes($params)
    {
        //$steelhomesite="http://www.in.steelhome.cn/";   //测试版
        $steelhomesite = "https://www.steelhome.com/";    //正式版

        $mid = $params['mid'];
        $maid = $params['maid'];
        $isen = $params['isen'];
        $mtid = $params['mtid'];

        if ($mid && $maid && $mtid) {
            $sql = "SELECT PictureBig,PictureSmall,CodeBig,CodeMiddle,CodeSmall from meeting_member_contactman where Mid='$mid' and Maid='$maid' and (Mtid='$mtid' or Mtid='45') and IsEn='$isen' and status=1 limit 1";
            $mes = $this->_dao->getRow($sql);

            $sql = "SELECT PictureBig,PictureSmall,CodeBig,CodeMiddle,CodeSmall from member_contact_user where member_id='$mid' and user_id='$maid' limit 1";
            $mes2 = $this->_dao->getRow($sql);

            if (!$mes['PictureBig']) {
                $mes['PictureBig'] = $mes2['PictureBig'];
            }
            if (!$mes['PictureSmall']) {
                $mes['PictureSmall'] = $mes2['PictureSmall'];
            }
            if (!$mes['CodeBig']) {
                $mes['CodeBig'] = $mes2['CodeBig'];
            }
            if (!$mes['CodeMiddle']) {
                $mes['CodeMiddle'] = $mes2['CodeMiddle'];
            }
            if (!$mes['CodeSmall']) {
                $mes['CodeSmall'] = $mes2['CodeSmall'];
            }
            if (!$mes['PictureBig']) {
                $mes['PictureBig'] = $mes2['PictureBig'];
            }
            if ($mes['PictureBig']) $mes['PictureBig'] = $steelhomesite . $mes['PictureBig'];
            if ($mes['PictureSmall']) $mes['PictureSmall'] = $steelhomesite . $mes['PictureSmall'];
            if ($mes['CodeBig']) $mes['CodeBig'] = $steelhomesite . $mes['CodeBig'];
            if ($mes['CodeMiddle']) $mes['CodeMiddle'] = $steelhomesite . $mes['CodeMiddle'];
            if ($mes['CodeSmall']) $mes['CodeSmall'] = $steelhomesite . $mes['CodeSmall'];
        }
        echo "cg::PictureBig:" . base64_encode($mes['PictureBig']) . "|PictureSmall:" . base64_encode($mes['PictureSmall']) . "|CodeBig:" . base64_encode($mes['CodeBig']) . "|CodeMiddle:" . base64_encode($mes['CodeMiddle']) . "|CodeSmall:" . base64_encode($mes['CodeSmall']);
    }


    public function getdata64($array, $params)
    {
        $data = array();
        foreach ($array as $a) {
            if ($a == "Runame" || $a == "CompfnameS" || $a == "TrueName" || $a == "Sex" || $a == "Post" || $a == "CheckInedUserName") {
                $data[] = $a . "='" . str_replace("'", "''", base64_decode_new($params[$a])) . "'";
            } else {
                $data[] = $a . "='" . $params[$a] . "'";
            }
        }
        $data = implode(",", $data);
        return $data;
    }

    // 微信二维码转换成以前的二维码格式
    private function decodeQRCode($QRCode)
    {
        $code = $QRCode;

        if (strpos($QRCode, "weixin.qq.com") !== false) {
            $row = $this->_dao->getRow("select * from member_contact_user where WeixinQRCode = '" . $QRCode . "' limit 1", 0);
            $code = "1|" . $row['member_id'] . "|" . $row['user_id'] . "|" . $row['user_sign'];
            //$code = $row['GUID']."|".$row['Mid']."|".$row['Maid']."|#";
        }
        $code = explode("|", $code);
        return $code;
    }



    //xiangbin add 20201111 start

    //微信选座
    public function getuserinfo_weixin($params)
    {
        //$steelhomesite="http://www.in.steelhome.cn/";   //测试版
        $steelhomesite = "https://www.steelhome.com/";    //正式版
        $personsign_code = base64_decode($params['personsign_code']);
        $nickname = base64_decode($params['nickname']);
        //$imgurl=base64_decode($params['imgurl']);             //微信头像网址
        $openid = base64_decode($params['openid']);
        //$token=base64_decode($params['token']);
        $mtid = $params['mtid'];
        $iscjwy = $params['iscjwy'];
        $partyid = $params['partyid'];

        //$guid=$params['GUID'];
        $createtime = base64_decode($params['createtime']);     //创建时间
        $ip = base64_decode($params['ip']);
        $data = explode("|", base64_decode($personsign_code));
        //print"<pre>";print_r($data);
        $mid = $data[1];
        $userid = $data[2];
        $usersign = $data[3];
        $isen = empty($data[4]) ? "0" : $data[4];

        $time = date("Y-m-d H:i:s", time());                      //当前时间

        // $sql="select count(ID)  from `weixin_userinfo` where  weixin_openid='".$openid."' and mtid=".$mtid;
        // $mes=$this->_dao->getone($sql);//$mes=$mes[0];print_r($mes);exit;

        if (!$mtid) {
            $arr = array(
                'Success' => base64_encode('0'),
                'Message' => base64_encode("未获取到当前会议信息，获取失败！"),
                'ErrorCode' => base64_encode('0001'),        //未获取到mtid
            );
            $mtid = 0;
            $log = "签到失败：昵称为" . $nickname . "，ip= " . $ip . " 的微信用户未获取到当前会议编号。";
        } elseif ($personsign_code == "") {
            $arr = array(
                'Success' => base64_encode('0'),
                'Message' => base64_encode("未获取到二维码编号!"),
                'ErrorCode' => base64_encode('0002'),
            );
            $log = "签到 获取失败，未获取到ip= " . $ip . " 的微信用户的二维码编号。";
        } else if ($openid == "") {
            $arr = array(
                'Success' => base64_encode('0'),
                'Message' => base64_encode("网络错误，未获取到当前微信号信息!"),
                'ErrorCode' => base64_encode('0003'),
            );
            $log = "签到 获取失败，未获取到ip= " . $ip . " 的微信用户的openid。";
        } else if ($openid == "-1") {
            $arr = array(
                'Success' => base64_encode('0'),
                'Message' => base64_encode("登录失效，请退出重新输入会议签到!"),
                'ErrorCode' => base64_encode('0004'),
            );
            $log = "签到 登录失败，ip= " . $ip . " 的微信用户登录失效。";
        } else if ($mid == "" || $userid == "") {
            $arr = array(
                'Success' => base64_encode('0'),
                'Message' => base64_encode("非法二维码编号!"),  //会员id为空，
                'ErrorCode' => base64_encode('0005'),
            );
            $log = "签到 获取失败，获取到ip= " . $ip . " 的微信用户的二维码编号非法（mid为空）。";
        } else {
            $sql = "select count(*) from `meeting_member_contactman` where `Mid`= " . $mid . " and `Maid`=" . $userid . " and ( Mtid=" . $mtid . " or Mtid=45 ) and IsEn='$isen' and status=1";//echo $sql;
            $num = $this->_dao->getone($sql);
            if ($num == 0) {
                $arr = array(
                    'Success' => base64_encode('0'),
                    'Message' => base64_encode("该二维码编号无效!"),
                    'ErrorCode' => base64_encode('0006'),
                );
                $log = "签到 获取失败，获取到ip= " . $ip . " 的微信用户的二维码编号非法。";
            } else {


                $getmes = $this->_dao->query("select id as Cid, `CodeBig`,`CodeMiddle`,`CodeSmall`,`PictureBig`,`PictureSmall`,`CompfnameS`,`TrueName`,`Sex`,`Post`,`FinanceType`,`GuestType`,`Mtid`,`PersonSign`,`IsEn` from `meeting_member_contactman` where `Mid`='" . $mid . "' and `Maid`='" . $userid . "' and ( Mtid=" . $mtid . " or Mtid=45 ) and IsEn='$isen' and status=1");
                $getmes = $getmes[0];

                $meeting_member = $this->_dao->getRow("select FinanceType,compabb from meeting_member where " . ($isen == "1" ? "`MidEn`" : "`Mid`") . "='" . $mid . "' and ( Mtid=" . $mtid . " or Mtid=45 )");//会务费类型

                $getmes2 = $this->_dao->query("select `CodeBig`,`CodeMiddle`,`CodeSmall`,`PictureBig`,`PictureSmall` from `member_contact_user` where `user_id` =" . $userid);
                $getmes2 = $getmes2[0];

                $this->_dao->execute("INSERT INTO  `meeting_party_iscjwy` ( `partyid`, `mtid`, `hwid`, `type`, `openid`) VALUES ( '" . $partyid . "', '" . $mtid . "', '" . $getmes['Cid'] . "', '" . $iscjwy . "','" . $openid . "')");

                if ($getmes['CodeMiddle'] == "") {
                    //print"<pre>";print_r($getmes2);
                    if ($getmes2['CodeMiddle'] != "") $getmes['CodeMiddle'] = $steelhomesite . $getmes2['CodeMiddle'];

                } else {
                    $getmes['CodeMiddle'] = $steelhomesite . $getmes['CodeMiddle'];
                }

                if ($getmes['CodeBig'] == "" && $getmes2['CodeBig'] != "") $getmes['CodeBig'] = $steelhomesite . $getmes2['CodeBig'];
                if ($getmes['CodeSmall'] == "" && $getmes2['CodeSmall'] != "") $getmes['CodeSmall'] = $steelhomesite . $getmes2['CodeSmall'];
                if ($getmes['PictureBig'] == "" && $getmes2['PictureBig'] != "") $getmes['PictureBig'] = $steelhomesite . $getmes2['PictureBig'];
                if ($getmes['PictureSmall'] == "" && $getmes2['PictureSmall'] != "") $getmes['PictureSmall'] = $steelhomesite . $getmes2['PictureSmall'];

                if ($getmes['CodeBig'] && !strstr($getmes['CodeBig'], "http")) $getmes['CodeBig'] = $steelhomesite . $getmes['CodeBig'];
                if ($getmes['CodeSmall'] && !strstr($getmes['CodeSmall'], "http")) $getmes['CodeSmall'] = $steelhomesite . $getmes['CodeSmall'];
                if ($getmes['PictureBig'] && !strstr($getmes['PictureBig'], "http")) $getmes['PictureBig'] = $steelhomesite . $getmes['PictureBig'];
                if ($getmes['PictureSmall'] && !strstr($getmes['PictureSmall'], "http")) $getmes['PictureSmall'] = $steelhomesite . $getmes['PictureSmall'];
                //print"<pre>";print_r($getmes);
                $arr = array(
                    'Success' => base64_encode('1'),
                    'Message' => base64_encode("获取成功!"),
                    'Result' => array(                        //result表只能传这些信息，不能多传字段，iPhone手机不支持传多余字段
                        'Cid' => base64_encode($getmes['Cid']),
                        'PersonSign' => base64_encode($getmes['PersonSign']),
                        //   'CodeBig' =>base64_encode($getmes['CodeBig']),
                        'CodeMiddle' => base64_encode($getmes['CodeMiddle']),
                        'CodeSmall' => base64_encode($getmes['CodeSmall']),
                        //'PictureBig' =>base64_encode($getmes['PictureBig']),
                        'PictureSmall' => base64_encode($getmes['PictureSmall']),
                        'ComName' => base64_encode($getmes['CompfnameS']),
                        'ComNameS' => base64_encode($meeting_member['compabb']),
                        'TrueName' => base64_encode($getmes['TrueName']),
                        'Sex' => base64_encode($getmes['Sex']),
                        'Post' => base64_encode($getmes['Post']),
                        'Type' => base64_encode($getmes['FinanceType']),
                        'Mtid' => base64_encode($mtid),
                        'Mid' => base64_encode($mid),
                        'Maid' => base64_encode($userid),
                        'IsEn' => base64_encode($getmes['IsEn']),
                        'HWType' => base64_encode($meeting_member['FinanceType']),
                        'GuestType' => base64_encode($getmes['GuestType']),
                        'Iswy' => base64_encode($iscjwy),
                    ),
                );

            }
        }
        //$log='http://************/meetingv1.2/meeting.php?'."action=getbind_weixin"."&personsign_code=".$params['personsign_code']."&nickname=".$params['nickname']."&token=".$params['token']."&openid=".$params['openid']."&ip=".$params['ip']."&createtime=".$params['createtime'];
        //$this->_dao->execute("insert into `weixin_op_log` (`LogMsg`,`CreateTime`,`weixin_openid`,`personsign_code`,`ip_address`,`mtid`) values('".$log."','".$createtime."','".$openid."','".$personsign_code."','".$ip."',".$mtid.")");
        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    public function getuserinfolist_weixin_mid_mtid($params)
    {
        $mtid = $params['mtid'];
        $Mid = $params['mid'];
        $Cid = $params['Cid'];


        if (!$mtid) {
            $arr = array(
                'Success' => '0',
                'Message' => "未获取到当前会议信息，获取失败！",
                'ErrorCode' => '0001',        //未获取到mtid
            );
        } else if (!$Mid) {
            $arr = array(
                'Success' => '0',
                'Message' => "未获取到当前会员信息，获取失败！",
                'ErrorCode' => '0002',        //未获取到mtid
            );
        } else if (!$Cid) {
            $arr = array(
                'Success' => 0,
                'Message' => "未获取到参会信息，获取失败！",
                'ErrorCode' => '0003',        //未获取到mtid
            );
        } else {
            $getmes = $this->_dao->query("select mmc.id as Cid,mmc.`Mid`, `CodeBig`,`CodeMiddle`,`CodeSmall`,`PictureBig`,`PictureSmall`,mmc.`CompfnameS` as ComName ,mmc.`TrueName`,`Sex`,`Post`,`FinanceType`,`GuestType`,`Mtid`,`PersonSign`,`IsEn`,seat_x,seat_y,seatno FROM meeting_member_contactman as mmc
			LEFT JOIN meeting_party_person ON mmc.id = meeting_party_person.hwid and meeting_party_person.isdel=0
			LEFT JOIN meeting_party_seat ON meeting_party_seat.id = meeting_party_person.seat_id and meeting_party_seat.isdel=0 and meeting_party_seat.seat_type!=5 where mmc.`Mid`='" . $Mid . "' and  mmc.Mtid=" . $mtid . "  and IsEn='0' and status=1 and mmc.id!='" . $Cid . "' ");
            //$getmes=$getmes[0];
            $arr = array(
                'Success' => 1,
                'Message' => "获取成功!",
                'Result' => $getmes
            );
        }
        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    public function sug($params)
    {
        $mtid = $params['mtid'];
        $partyid = $params['partyid'];
        $searchname = $this->gbkToutf8($params['qt']);
        //$searchname = iconv("UTF-8", "GBK//IGNORE", $searchname);
        $searchname = str_replace("'", "\'", $searchname);
        //$searchname=mb_convert_encoding($searchname, 'gbk', 'utf-8');
        //$Cid=$params['Cid'];


        if (!$mtid) {
            $arr = array(
                'Success' => '0',
                'Message' => "未获取到当前会议信息，获取失败！",
                'ErrorCode' => '0001',        //未获取到mtid
            );
        } else if (!$searchname) {
            $arr = array(
                'Success' => '0',
                'Message' => "未获取到当前会员信息，获取失败！",
                'ErrorCode' => '0002',        //未获取到mtid
            );
        } else {

            $sql = "select  mmc.id as Cid,mmc.`Mid`, `CodeBig`,`CodeMiddle`,`CodeSmall`,`PictureBig`,`PictureSmall`,mmc.`CompfnameS` as ComName ,mmc.`TrueName`,`Sex`,`Post`,`FinanceType`,`GuestType`,`Mtid`,`PersonSign`,`IsEn`,seat_x,seat_y,seatno,meeting_party_seat.seatnum as seatnum  ";
            $sql .= "  from meeting_member_contactman as mmc LEFT JOIN meeting_party_person ON mmc.id = meeting_party_person.hwid and meeting_party_person.partyid='" . $partyid . "' and meeting_party_person.isdel=0
			LEFT JOIN meeting_party_seat ON meeting_party_seat.id = meeting_party_person.seat_id and meeting_party_seat.isdel=0 and meeting_party_seat.seat_type!=5 where (mmc.Truename like binary '%$searchname%' or mmc.CompfnameS like  binary '%$searchname%' ) and (mmc.Mtid='$mtid' )  and mmc.status=1 ";

            $sql .= " order by mmc.PersonSign,mmc.Truename limit 6";
            //echo $sql;
            $getmes = $this->_dao->query($sql);

            //print_r($getmes);
            $arr = array(
                'Success' => 1,
                'Message' => "获取成功!",
                'Result' => $getmes
            );
        }

        $json_string = $this->pri_JSON($arr);
        echo $json_string;

    }
    //xiangbin add 20201111 end

    //xiangbin add 20201122 strat
    public function meet_GetMemberInfo_managetruename($params)
    {
        $meetid = $params['mtid'];
        $partyid = $params['partyid'];
        $needCompress = $params['needCompress'];//判断是否压缩的参数
        $searchname = $params['username'];
        //$searchname = iconv("UTF-8", "GBK//IGNORE", $searchname);
        $meeting = array();

        //$this->setvars($meetid);

        $dtime = $params['UpdateTime'];
        $arr = array('Success' => '0',
            'Message' => base64_encode('暂无数据'),
            'Results' => $meeting,
        );

        //$meetname = $this->_dao->getRow("select * from meeting_base where ID  = '$meetid' limit 1");

        $contactman = $this->_dao->query("select a.*,b.compfname,b.compabb,b.comtype,b.UserName from meeting_member_contactman as a,meeting_member as b where ((a.Mid = b.Mid and a.IsEn=0) or (a.Mid= b.MidEn and a.IsEn=1)) and a.Mtid=b.Mtid and (a.Mtid = '$meetid') and b.UserName='$searchname' and a.status=1 ORDER BY a.PersonSign asc,a.ID DESC ", 0);

        $databasecjry = $this->_dao->getOnes(" select hwid from meeting_party_person where partyid='$partyid' and isdel=0 ");

        if (!empty($contactman)) // 存在会员表
        {


            foreach ($contactman as $key => $value) {
                if (in_array($value['ID'], $databasecjry)) {
                    continue;
                }
                $meeting[$key]['Cid'] = base64_encode($value['ID']);
                $meeting[$key]['Mid'] = base64_encode($value['Mid']);
                $meeting[$key]['Maid'] = base64_encode($value['Maid']);
                $meeting[$key]['ContactMobile'] = base64_encode($value['ContactMobile']);
                $meeting[$key]['ContactPhone'] = base64_encode($value['ContactPhone']);
                $meeting[$key]['AdminName'] = base64_encode($value['UserName']);
                $meeting[$key]['usertype'] = base64_encode($mtype);
                $meeting[$key]['TrueName'] = base64_encode($value['TrueName']);
                $meeting[$key]['Sex'] = base64_encode($value['Sex']);
                $meeting[$key]['Post'] = base64_encode(str_replace('&', '&&', htmlspecialchars_decode($value['Post'])));
                $meeting[$key]['CheckInedDesc'] = base64_encode($value['CheckInedDesc']);
                // $meeting[$key]['HotelDesc'] = base64_encode($value['HotelDesc']);
                $meeting[$key]['compfname'] = base64_encode(str_replace('&', '&&', htmlspecialchars_decode($value['CompfnameS'])));
                $meeting[$key]['compabb'] = base64_encode(str_replace('&', '&&', htmlspecialchars_decode($value['compabb'])));
                $meeting[$key]['comtype'] = base64_encode($value['comtype']);
                $meeting[$key]['weixinewmurl'] = base64_encode($value['WeixinQRCode']);
                $meeting[$key]['IsEn'] = $value['IsEn'];
                //$meeting[$key]['IsCanZl']=$value['IsCanZl'];
            }
            $arr['Success'] = '1';
            $arr['Message'] = base64_encode('参会人员信息');
            $arr['Results'] = $meeting;

        }
        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }
    //xiangbin  20201122 add end

    //zk获取抽奖的用户信息
    public function GetPerson($params)
    {
        $jtype = $params['type'];
        $meetid = $params['mtid'];
        $needCompress = $params['needCompress'];//判断是否压缩的参数

        $contactman = $this->_dao->query("select a.*,b.compfname,b.compabb,b.comtype,b.UserName from meeting_member_contactman as a,meeting_member as b where ((a.Mid = b.Mid and a.IsEn=0) or (a.Mid= b.MidEn and a.IsEn=1)) and a.Mtid=b.Mtid and (a.Mtid = '$meetid' OR a.Mtid=45)  and a.status=1 ORDER BY a.PersonSign asc,a.ID DESC ", 0);
        //print_r($contactman);
        // "uid": "138",
        // "PersonSign": "68073",
        // "TrueName": "\u4f55\u5468\u9e4f",
        // "Post": "\u6280\u672f\u90e8\u8f6f\u4ef6\u5de5\u7a0b\u5e08",
        // "ComName": "\u94a2\u4e4b\u5bb6",
        // "PictureSmall": "",
        // "headimgurl": "http:\/\/thirdwx.qlogo.cn\/mmopen\/vi_32\/Q0j4TwGTfTIG4EF1qibz2wbU8qGGmvToX8CyGdA02UWKcacwGJTyTPetxnE2YMrdInPRDbMCnKLU3micQC4cJlcw\/132",
        // "nickname": "\"super\u4f55\"",
        // "openid": "ogR_ljljAAjqby3fHv3Z3yAlkhcM",
        // "isgzj": "1"
        if (!empty($contactman)) // 存在会员表
        {
            foreach ($contactman as $key => $value) {
                /*
				if ($value['Mtid'] == 45)
				{
					$mtype = '钢之家员工';
				} elseif ($value['GuestType'] != 0)
				{
					$mtype = $GLOBALS['GuestType'][$value['GuestType']];
				}
				else
				{
					if($value['IsEn']=="1"){
						$financet = $this->_dao->getRow("SELECT FinanceType from meeting_member where MidEn='" . $value['Mid'] . "' and Mtid='$meetid' ", 0);
					}else{
						$financet = $this->_dao->getRow("SELECT FinanceType from meeting_member where Mid='" . $value['Mid'] . "' and Mtid='$meetid' ", 0);
					}
					if ($financet == "16")
					{
						$mtype = '媒体';
					}
					else
					{
						$mtype = '参会嘉宾';
					}
				} */
                $meeting[$key]['uid'] = $value['ID'];
                $meeting[$key]['PersonSign'] = $value['PersonSign'];
                $meeting[$key]['Mid'] = $value['Mid'];
                $meeting[$key]['TrueName'] =  $value['TrueName'];
                $meeting[$key]['Post'] =  str_replace('&', '&&', htmlspecialchars_decode($value['Post']));
                $meeting[$key]['ComName'] = str_replace('&', '&&', htmlspecialchars_decode($value['CompfnameS']));
                //$meeting[$key]['nickname'] ="";

                //$meeting[$key]['Maid'] = $value['Maid'];
                //$meeting[$key]['AdminName']=base64_encode($value['UserName']);
                //$meeting[$key]['usertype'] = base64_encode($mtype);

                //$meeting[$key]['Sex'] = base64_encode($value['Sex']);

                //$meeting[$key]['CheckInedDesc'] = base64_encode($value['CheckInedDesc']);
                // $meeting[$key]['HotelDesc'] = base64_encode($value['HotelDesc']);
                //$meeting[$key]['compfname'] = base64_encode(str_replace('&','&&',htmlspecialchars_decode($value['CompfnameS'])));
                //$meeting[$key]['compabb'] = base64_encode(str_replace('&','&&',htmlspecialchars_decode($value['compabb'])));
                //$meeting[$key]['comtype'] = base64_encode($value['comtype']);
                //$meeting[$key]['weixinewmurl'] = base64_encode($value['WeixinQRCode']);
                //$meeting[$key]['IsEn']=$value['IsEn'];
                //$meeting[$key]['IsCanZl']=$value['IsCanZl'];

                if ($value['PictureBig'] == "") {
                    $urow = $this->_dao->getRow("select PictureBig,PictureSmall from member_contact_user where user_id ='" . $value['Maid'] . "' ", 0);

                    $value['PictureBig'] = $urow['PictureBig'];
                    $value['PictureSmall'] = $urow['PictureSmall'];
                }


                $meeting[$key]['PictureBig'] = $value['PictureBig'] == '' ? STEELHOME_SITE : (strstr($value['PictureBig'], "http://") ? $value['PictureBig'] : (strstr($value['PictureBig'], "//") ? 'http:' . $value['PictureBig'] : STEELHOME_SITE . $value['PictureBig']));
                $meeting[$key]['PictureSmall'] = $value['PictureSmall'] == '' ? STEELHOME_SITE : (strstr($value['PictureSmall'], "http://") ? $value['PictureSmall'] : (strstr($value['PictureSmall'], "//") ? 'http:' . $value['PictureSmall'] : STEELHOME_SITE . $value['PictureSmall']));
                $meeting[$key]['PictureBig'] = str_replace("http:////", "http://", $meeting[$key]['PictureBig']);
                //$meeting[$key]['PictureSmall'] =str_replace("http:////","http://", $meeting[$key]['PictureSmall']);
                $meeting[$key]['PictureBig'] = str_replace("http://", "https://", $meeting[$key]['PictureBig']);
                //$meeting[$key]['PictureSmall'] =str_replace("http://","https://", $meeting[$key]['PictureSmall']);
                //$meeting[$key]['PictureSmall'] = $value['PictureSmall'];
                $meeting[$key]['PictureSmall'] = "";
                $meeting[$key]['headimgurl'] = str_replace("http://", "https://", $meeting[$key]['PictureBig']);
                //echo $meeting[$key]['headimgurl'];
                if ($meeting[$key]['headimgurl'] == STEELHOME_SITE || $meeting[$key]['headimgurl'] == "https://iwww.steelhome.cn/" || $meeting[$key]['headimgurl'] == STEELHOME_SITE) {
                    $meeting[$key]['headimgurl'] = "";
                }
                //$meeting[$key]['openid'] ="";
                //$meeting[$key]['isgzj'] =1;

                //$meeting[$key]['ContactPhone'] = $value['ContactPhone'];
                //$meeting[$key]['ContactMobile'] = $value['ContactMobile'];


                //$meeting[$key]['IsGetMoney'] = $value['IsGetMoney'] == "0"?"0":"1";
                //$meeting[$key]['IsCheckIned'] = $value['IsCheckIned'];


            }


        }
        //print_r($meeting);
        $json_string = $meeting;
        if ($needCompress == 1) {
            $json_string = gzencode($json_string, 9);//压缩接口
        }
        //echo $json_string;
        if ($jtype == 'jsonp') {

            //$mess=json_encode($mes);
            echo "jsonp(" . json_encode($json_string) . ")";

        } else {
            echo json_encode($json_string);
        }
    }


    //二、三等奖抽完一轮自动推送中奖人信息，前面推送的不覆盖
    public function pushawardAuto($params)
    {
        $awardtype = $params['awardtype'];
        $meetid = $params['mtid'];
        $awarddata = $params['awarddata'];
        $callback = $params['callback'];
        $awardname = $params['awardname'];
        $awarddata = base64_decode(str_replace(' ', '+', $params['awarddata']));
        $jsondata = json_decode($awarddata);
        $arr = array('Success' => '0',
            'Message' => '参数缺失',
            'Results' => $meeting,
        );

        $meetstatus= $this->_dao->getone("SELECT Status FROM `meeting_base` WHERE `ID` ='$meetid'");
        if($meetstatus>2){
            $arr['Success'] = 0;
            $arr['Message'] = "同步失败，会议已结束";
        }else if (empty($awardtype) || empty($meetid) || empty($jsondata)) {
            $arr['Message'] = '同步失败，参数缺失';
        } else {
            //备份记录
            //$sql = "insert into meeting_member_award_del(`ID`, `Mtid`, `Cid`, `Mid`, `PersonSign`, `AwardType`,`AwardName`, `Status`, `CreateDate`, `CreateUser`, `GetAwardTime`, `CancerTime`,mobile,truename,address,`DelTime`) select `ID`, `Mtid`, `Cid`, `Mid`, `PersonSign`, `AwardType`,`AwardName`, `Status`, `CreateDate`, `CreateUser`, `GetAwardTime`, `CancerTime`,mobile,truename,address,NOW() from meeting_member_award where mtid='$meetid' and AwardType ='$awardtype'";
            //$this->_dao->execute($sql);
            //删除记录
            //$sql = " delete from meeting_member_award where mtid='$meetid' and AwardType ='$awardtype'";
            //$this->_dao->execute($sql);
            foreach ($jsondata as $key => $value) {
                $isexit=$this->_dao->getOne("select id from meeting_member_award where  mtid='$meetid' and AwardType ='$awardtype' and Cid='".$value->uid ."'");
                if($isexit==""){
                    $this->_dao->execute("insert into meeting_member_award set Mtid = '$meetid',Cid = '" . $value->uid . "',Mid = '" . $value->Mid . "',PersonSign = '" . $value->PersonSign . "',AwardType ='$awardtype',AwardName='" .$awardname. "',Status = '0',CreateDate=NOW(),CreateUser='本地推送' ");
                }
            }
            $arr['Success'] = '1';
            $arr['Message'] = '同步成功';
        }
        $json_string = $this->pri_JSON($arr);
        if ($callback != "") {
            echo "$callback(" . $json_string . ")";
        } else {
            echo $json_string;
        }
    }


    //std add 20241016 start
    public function pushawardNew($params)
    {
        $awardtype = $params['awardtype'];
        $meetid = $params['mtid'];
        $awarddata = $params['awarddata'];
        $callback = $params['callback'];
        $awardname = $params['awardname'];
        $awarddata = base64_decode(str_replace(' ', '+', $params['awarddata']));
        $jsondata = json_decode($awarddata);
        $arr = array('Success' => '0',
            'Message' => '参数缺失',
            'Results' => $meeting,
        );

        $meetstatus= $this->_dao->getone("SELECT Status FROM `meeting_base` WHERE `ID` ='$meetid'");
        if($meetstatus>2){
            $arr['Success'] = 0;
            $arr['Message'] = "同步失败，会议已结束";
        }else if (empty($awardtype) || empty($meetid) || empty($jsondata)) {
            $arr['Message'] = '同步失败，参数缺失';
        } else {
            //备份记录
            $sql = "insert into meeting_member_award_del(`ID`, `Mtid`, `Cid`, `Mid`, `PersonSign`, `AwardType`,`AwardName`, `Status`, `CreateDate`, `CreateUser`, `GetAwardTime`, `CancerTime`,mobile,truename,address,`DelTime`) select `ID`, `Mtid`, `Cid`, `Mid`, `PersonSign`, `AwardType`,`AwardName`, `Status`, `CreateDate`, `CreateUser`, `GetAwardTime`, `CancerTime`,mobile,truename,address,NOW() from meeting_member_award where mtid='$meetid' and AwardType ='$awardtype'";
            $this->_dao->execute($sql);
            //删除记录
            $sql = " delete from meeting_member_award where mtid='$meetid' and AwardType ='$awardtype'";
            $this->_dao->execute($sql);
            foreach ($jsondata as $key => $value) {
                $this->_dao->execute("insert into meeting_member_award set Mtid = '$meetid',Cid = '" . $value->uid . "',Mid = '" . $value->Mid . "',PersonSign = '" . $value->PersonSign . "',AwardType ='$awardtype',AwardName='" .$awardname. "',Status = '" . $value->status . "'," . ($value->status == 1 ? 'GetAwardTime=NOW(),' : '') . "CreateDate=NOW(),CreateUser='本地推送' ");
            }

            $arr['Success'] = '1';
            $arr['Message'] = '同步成功';
        }
        $json_string = $this->pri_JSON($arr);
        if ($callback != "") {
            echo "$callback(" . $json_string . ")";
        } else {
            echo $json_string;
        }
    }


    //xiangbin add 20211026 start
    public function pushaward($params)
    {
        $awardtype = $params['awardtype'];
        $meetid = $params['mtid'];
        $awarddata = $params['awarddata'];
        $callback = $params['callback'];
        $awardname = $params['awardname'];
        
        $Status = $params['Status'] ? $params['Status'] : 0;
        $awarddata = base64_decode(str_replace(' ', '+', $params['awarddata']));
        $jsondata = json_decode($awarddata);
        $arr = array('Success' => '0',
            'Message' => '参数缺失',
            'Results' => $meeting,
        );
        
        $meetstatus= $this->_dao->getone("SELECT Status FROM `meeting_base` WHERE `ID` ='$meetid'");
        $isexit=$this->_dao->getOne("select id from meeting_member_award where  mtid='$meetid' and AwardType ='$awardtype' and status=1 ");
        if($meetstatus>2){
            $arr['Success'] = 0;
            $arr['Message'] = "同步失败，会议已结束";
        }else if ($awardtype=="" || empty($meetid) || empty($jsondata)) {
            $arr['Message'] = '同步失败，参数缺失';
        }else if (($awardtype==2 || $awardtype==3) && $isexit>0) {
            $arr['Message'] = '同步失败，信息有更新';
        } else {
            //备份记录
            $sql = "insert into meeting_member_award_del(`ID`, `Mtid`, `Cid`, `Mid`, `PersonSign`, `AwardType`,`AwardName`, `Status`, `CreateDate`, `CreateUser`, `GetAwardTime`, `CancerTime`,mobile,truename,address,`DelTime`) select `ID`, `Mtid`, `Cid`, `Mid`, `PersonSign`, `AwardType`,`AwardName`, `Status`, `CreateDate`, `CreateUser`, `GetAwardTime`, `CancerTime`,mobile,truename,address,NOW() from meeting_member_award where mtid='$meetid' and AwardType ='$awardtype'";
            $this->_dao->execute($sql);
            //删除记录
            $sql = " delete from meeting_member_award where mtid='$meetid' and AwardType ='$awardtype'";
            $this->_dao->execute($sql);
            foreach ($jsondata as $key => $value) {
                //echo $value->Cid."----";
                //echo "insert into meeting_member_award set Mtid = '$meetid',Cid = '".$value->Cid."',Mid = '".$value->Mid."',PersonSign = '".$value->PersonSign."',AwardType ='$awardtype',Status = 0,CreateDate=NOW(),CreateUser='本地推送' ";
                $this->_dao->execute("insert into meeting_member_award set Mtid = '$meetid',Cid = '" . $value->uid . "',Mid = '" . $value->Mid . "',PersonSign = '" . $value->PersonSign . "',AwardType ='$awardtype',AwardName='" .$value->awardname. "',Status = '$Status'," . ($Status == 1 ? 'GetAwardTime=NOW(),' : '') . "CreateDate=NOW(),CreateUser='本地推送' ");
            
                //if($awardtype==5){
                   // $contactManInfo = $this->_dao->getRow("select TrueName,ContactMobile from meeting_member_contactman where ID='".$value->uid."' and Mtid='$meetid' and Mid = '".$value->Mid."'");
                   // $content = "尊敬的".$contactManInfo['TrueName']."，恭喜您获得钢之家19周年庆晚会幸运奖，感谢您对这次活动的关注和支持！本信息仅为中奖通知，请您立即前往领奖台领奖！";
                    //$this->smsDao->SendMobileSMS($meetid, $value->uid, $value->Mid, 0, $content, $contactManInfo['ContactMobile']);
                   // $this->_dao->saveSmsSendLog($contactManInfo['ContactMobile'], $content, "南京会议：".$contactManInfo['TrueName'] , $meetid);
                //}
            }

            $arr['Success'] = '1';
            $arr['Message'] = '同步成功';
            //$arr['Results'] = $meeting;
            //*/
        }
        $json_string = $this->pri_JSON($arr);
        if ($callback != "") {
            echo "$callback(" . $json_string . ")";
        } else {
            echo $json_string;
        }
    }

    //shitaodi add 20221114 start 幸运奖
    public function getxyjperson($params)
    {
        $mtid = $params['mtid'];
        $awardtype = $params['AwardType'];
        $callback = $params['callback'];
        $arr = array('Success' => '0',
            'Message' => 'null',
        );
        $nocom=array("70620","71484","70738");
        $awarr=array();
        if ($mtid) {
            $awarr = $this->_dao->query("SELECT cm.ID as Cid,cm.Mid, cm.TrueName, cm.Maid, cm.PersonSign, cm.PictureBig ,cm.CompfnameS
	FROM `meeting_member_award` 
	LEFT JOIN meeting_member_contactman as cm ON meeting_member_award.cid = cm.id
	WHERE meeting_member_award.Mtid =$mtid
	AND meeting_member_award.AwardType =$awardtype
	ORDER BY `meeting_member_award`.`CreateDate` ASC				
					");

            $member = $this->_dao->Aquery("SELECT Mid,compabb from meeting_member where Mtid='$mtid'");

            foreach ($awarr as &$tmp) {
                if ($tmp['PictureBig'] == "") {
                    $urow = $this->_dao->getRow("select PictureBig,PictureSmall from member_contact_user where user_id ='" . $tmp['Maid'] . "' ", 0);
                    $tmp['PictureBig'] = $urow['PictureBig'];

                }
                if($tmp['PictureBig']!=""){
                    $tmp['PictureBig'] = $tmp['PictureBig'] == '' ? STEELHOME_SITE : (strstr($tmp['PictureBig'], "http://") ? $tmp['PictureBig'] : (strstr($tmp['PictureBig'], "//") ? 'http:' . $tmp['PictureBig'] : STEELHOME_SITE . $tmp['PictureBig']));
                    $tmp['PictureBig'] = str_replace("http:////", "http://", $tmp['PictureBig']);
                    $tmp['PictureBig'] = str_replace("http://", "https://", $tmp['PictureBig']);
                }
                $tmp['TrueName'] = base64_encode( $tmp['TrueName']);
                if(isset($member[$tmp['Mid']]) && $params['isqc']!=1 && $member[$tmp['Mid']]!=""){
                    $tmp['CompfnameS']=$member[$tmp['Mid']];
                }

                if(in_array($tmp['Cid'],$nocom) && $tmp['CompfnameS']=="" ){
					$tmp['CompfnameS']=$tmp['Post'];
				}


                $tmp['CompfnameS'] = base64_encode($tmp['CompfnameS']);
                
                unset($tmp['Maid']);
                unset($tmp['Post']);
            }
            //$arr['Success'] = '1';
            //$arr['Message'] = base64_encode('获取成功');
            //$arr['Results'] = $awarr;


        }
        //  else {
        //     $arr = array('Success' => '0',
        //         'Message' => base64_encode('非法操作'),
        //     );
        // }

        $json_string = $this->pri_JSON($awarr);
        if ($callback != "") {
            echo "$callback(" . $json_string . ")";
        } else {
            echo $json_string;
        }

    }

    
    //xiangbin add 20211026 end
    //xiangbin add 20221101 start
    public function ouyeelpush($params)
    {
        $awardtype = $params['awardtype'];
        $meetid = $params['mtid'];
        $awarddata = $params['awarddata'];

        $callback = $params['callback'];

        $Status = $params['Status'] ? $params['Status'] : 0;

        $awarddata = base64_decode(str_replace(' ', '+', $params['awarddata']));
        $jsondata = json_decode($awarddata);
        $arr = array('Success' => '0',
            'Message' => '参数缺失',
            'Results' => $meeting,
        );

        if (empty($awardtype) || empty($meetid) || empty($jsondata)) {
            $arr['Message'] = '同步失败，参数缺失';
        } else {

            ///*
            //备份记录
            //$sql="insert into meeting_member_award_del(`ID`, `Mtid`, `Cid`, `Mid`, `PersonSign`, `AwardType`, `Status`, `CreateDate`, `CreateUser`, `GetAwardTime`, `CancerTime`,mobile,truename,address,`DelTime`) select `ID`, `Mtid`, `Cid`, `Mid`, `PersonSign`, `AwardType`, `Status`, `CreateDate`, `CreateUser`, `GetAwardTime`, `CancerTime`,mobile,truename,address,NOW() from meeting_member_award where mtid='$meetid' and AwardType ='$awardtype'";
            //$this->_dao->execute($sql);
            //删除记录
//				$sql=" delete from meeting_member_award where mtid='$meetid' and AwardType ='$awardtype'";
//				$this->_dao->execute($sql);


            foreach ($jsondata as $key => $value) {
                $meeting_member_award_info = $this->_dao->getRow("select * from meeting_member_award where mtid='$meetid' and AwardType ='$awardtype' and Mtid = '$meetid' and Cid = '" . $value->uid . "' and Mid = '" . $value->Mid . "'");
                if (!$meeting_member_award_info) {
                    $this->_dao->execute("insert into meeting_member_award set Mtid = '$meetid',Cid = '" . $value->uid . "',Mid = '" . $value->Mid . "',PersonSign = '" . $value->PersonSign . "',AwardType ='$awardtype',Status = '$Status'," . ($Status == 1 ? 'GetAwardTime=NOW(),' : '') . "CreateDate=NOW(),CreateUser='本地推送' ");
                }

                $contactManInfo = $this->_dao->getRow("select * from meeting_member_contactman where ID='" . $value->uid . "' and Mtid='$meetid' and Mid = '" . $value->Mid . "'");
                //短信内容
                $content = "中奖短信somsthing";//TODO
                //发送短信
                // $this->smsDao->SendMobileSMS($meetid, $value->uid, $value->Mid, 0, $content, $contactManInfo['ContactMobile']);
                // $logssql = "Insert into meeting_member_smsdetail(Smsid,Mid,Mcid,TrueName,Mobile,SmsContent,UserName,Mtid,CreateDate,CreateUserId,UpdateDate,UpdateUserId,Status) values('','','','','" . $contactManInfo['ContactMobile'] . "','" . $content . "','客户会议" . $contactManInfo['TrueName'] . "','" . $meetid . "', NOW(), '', NOW(), '', 1)";
                // $this->_dao->execute($logssql);
                //echo $value->Cid."----";
                //echo "insert into meeting_member_award set Mtid = '$meetid',Cid = '".$value->Cid."',Mid = '".$value->Mid."',PersonSign = '".$value->PersonSign."',AwardType ='$awardtype',Status = 0,CreateDate=NOW(),CreateUser='本地推送' ";

            }
            $arr['Success'] = '1';
            $arr['Message'] = '同步成功';
            //$arr['Results'] = $meeting;
            //*/
        }
        $json_string = $this->pri_JSON($arr);
        if ($callback != "") {
            echo "$callback(" . $json_string . ")";
        } else {
            echo $json_string;
        }


    }
    //xiangbin add 20221101 end
    //****************************
    //
    //		add by nicc  end
    //
    //****************************

    /**
     * user:shizg
     * time:2022/11/4 10:10
     * TODO 处理欧冶抽奖中心人员的奖项
     * @param $params
     */
    public function ouyeelpushanyone($params)
    {
        $awardtype = $params['awardtype'];
        $meetid = $params['mtid'];
        $awarddata = $params['awarddata'];
        $callback = $params['callback'];
        $status = $params['Status'] ? $params['Status'] : 0;
        $awarddata = base64_decode(str_replace(' ', '+', $awarddata));
        $jsondata = json_decode($awarddata);
        $arr = array(
            'Success' => '0',
            'Message' => '参数缺失'
        );
        
        if (empty($awardtype) || empty($meetid) || empty($jsondata)) {
            $arr['Message'] = '同步失败，参数缺失';
        } else {
            //更新原始已有本奖项的人员
            $this->_dao->updateAwardTypeForMeeting( $meetid, $awardtype );
            foreach ($jsondata as $key => $value) {
                if ($value->uid) {
                    $awardUserInfo = $this->_dao->getAwardUserInfoByUid($value->uid,$meetid);
                    if ($awardUserInfo) {
                        $this->_dao->updateAwardUserInfo($value->uid, $value->jpname, $awardtype, $status);
                        //短信内容
                        if ($awardUserInfo['mobile']  ) {
                            $awardtypearr=array('1'=>'一等奖','2'=>'二等奖','3'=>'三等奖','4'=>'特等奖');
                            $content = "尊敬的" . $awardUserInfo['true_name'] . "，恭喜您获得恭喜您获得2023欧冶之夜".$awardtypearr[$awardtype]."，感谢您对这次活动的关注和支持！本信息仅为中奖通知，请您点击 http://d.steelhome.cn/wqn 此链接进入，留下您的联系方式和邮寄地址，我们的工作人员将会尽快寄出您的奖品哦！";//TODO
                            //发送短信
                            $msgcontent = $content;
                            $url = "http://www.steelhome.com/sendOuYeMessage.php?mobile=" . $awardUserInfo['mobile'] . "&msgcontent=".urlencode($msgcontent);
                           // $con = file_get_contents($url);
           		    file_put_contents("/tmp/ouyesms.log",date("Y-m-d H:i:s").$url.print_r(json_decode($con,true),true)."\n",FILE_APPEND);
                 
//                            if ($con) {
//                                $sendResult = json_decode($con,true);
//                                if ($sendResult['status'] == 1) {
//                                    $_SESSION["award_code"] = $code;
//                                    echo 1;
//                                    exit;
//                                }
//                            }
//                            $this->smsDao->SendMobileSMS($meetid, $value->uid, '', 0, $content, $awardUserInfo['mobile']);
                           // $this->_dao->saveSmsSendLog($awardUserInfo['mobile'], $content, "客户会议" . $awardUserInfo['true_name'], $meetid);
                        }
                    }
                }
            }
            $arr['Success'] = '1';
            $arr['Message'] = '同步成功';
        }
        $json_string = $this->pri_JSON($arr);
        if ($callback != "") {
            echo "$callback(" . $json_string . ")";
        } else {
            echo $json_string;
        }
    }
    public function ouyeelpushanyonenew($params)
    {
        $awardtype = $params['awardtype'];
        $meetid = $params['mtid'];
        $awarddata = $params['awarddata'];
        $callback = $params['callback'];
        $status = $params['Status'] ? $params['Status'] : 0;
        $awarddata = base64_decode(str_replace(' ', '+', $awarddata));
        $jsondata = json_decode($awarddata);
        $arr = array(
            'Success' => '0',
            'Message' => '参数缺失'
        );
        
        if (empty($awardtype) || empty($meetid) || empty($jsondata)) {
            $arr['Message'] = '同步失败，参数缺失';
        } else {
            //更新原始已有本奖项的人员
            $this->_dao->updateAwardTypeForMeeting( $meetid, $awardtype );
            foreach ($jsondata as $key => $value) {
                $mobile=$value->mobile;
                $true_name=$value->true_name;
                $meeting_wx_award_info = $this->_dao->getRow("select * from meeting_wx_award where mtid='$meetid' and openid ='".$value->uid."'");
                if (!$meeting_wx_award_info) {
                    $this->_dao->execute("insert into meeting_wx_award set mtid='".$meetid."',true_name='".$true_name."',mobile='".$mobile."',openid='".$value->uid."',nick_name='".urlencode($value->nick_name)."',head_img_url='".$value->head_img_url."',create_date=NOW(), award_type= '".$awardtype."', prize_name= '".$value->jpname."', status = '".$status."', draw_time=Now()");
                }
                else
                {
                    $this->_dao->updateAwardUserInfo($meeting_wx_award_info['id'],$value->jpname, $awardtype, $status);
                }

                //$mobile="18156590749";

                if ($mobile ) {
                    $awardtypearr=array('1'=>'一等奖','2'=>'二等奖','3'=>'三等奖','4'=>'特等奖');
                    $content = "尊敬的" . $true_name . "，恭喜您获得恭喜您获得2023欧冶之夜".$awardtypearr[$awardtype]."，感谢您对这次活动的关注和支持！本信息仅为中奖通知，请您点击 http://d.steelhome.cn/wqn 此链接进入，留下您的联系方式和邮寄地址，我们的工作人员将会尽快寄出您的奖品哦！";//TODO
                    //发送短信
                    $msgcontent = $content;
                    $url = "http://www.steelhome.com/sendOuYeMessage.php?mobile=" . $mobile . "&msgcontent=".urlencode($msgcontent);
                    //$con = file_get_contents($url);
                     file_put_contents("/tmp/ouyesms.log",date("Y-m-d H:i:s").$url.print_r(json_decode($con,true),true)."\n",FILE_APPEND);
                    //$this->_dao->saveSmsSendLog($mobile, $content, "客户会议" . $true_name, $meetid);
                }
            }
            $arr['Success'] = '1';
            $arr['Message'] = '同步成功';
        }
        $json_string = $this->pri_JSON($arr);
        if ($callback != "") {
            echo "$callback(" . $json_string . ")";
        } else {
            echo $json_string;
        }
    }
    public function ouyeelpushanyone1109($params)
    {
        $awardtype = $params['awardtype'];
        $meetid = $params['mtid'];
        $awarddata = $params['awarddata'];
        $callback = $params['callback'];
        $status = $params['Status'] ? $params['Status'] : 0;
        $awarddata = base64_decode(str_replace(' ', '+', $awarddata));
        $jsondata = json_decode($awarddata);
        $arr = array(
            'Success' => '0',
            'Message' => '参数缺失'
        );

        if (empty($awardtype) || empty($meetid) || empty($jsondata)) {
            $arr['Message'] = '同步失败，参数缺失';
        } else {
            //更新原始已有本奖项的人员
            $this->_dao->updateAwardTypeForMeeting1109( $meetid, $awardtype );
            foreach ($jsondata as $key => $value) {
                if ($value->uid) {
                    $awardUserInfo = $this->_dao->getAwardUserInfoByUid1109($value->uid);
                    if ($awardUserInfo) {
                        $this->_dao->updateAwardUserInfo1109($value->uid, $value->jpname, $awardtype, $status);
                    }
                }
            }
            $arr['Success'] = '1';
            $arr['Message'] = '同步成功';
        }
        $json_string = $this->pri_JSON($arr);
        if ($callback != "") {
            echo "$callback(" . $json_string . ")";
        } else {
            echo $json_string;
        }
    }
    public function CheckLastVersion($params)
    {
        $arr['Success']='1';
		$arr['Message']= base64_encode('查询成功');
        $arr['VersionLast'] = $GLOBALS['RELEASE']['Release'];
        $arr['VersionDate'] = $GLOBALS['RELEASE']['Release_Date'];
        $arr['VersionUrl'] = $GLOBALS['RELEASE']['Release_URL'];
        $arr['VersionMD5'] = $GLOBALS['RELEASE']['Release_MD5'];
        $json_string = $this->pri_JSON($arr);
        echo $json_string;

    }

    public function addCancerAwardNew($params){//将中了幸运奖的客户名单推送进数据库
        $arr = array('Success' => '0',
            'Message' => 'null',
        );
        $callback = $params['callback'];
        $meetid = $params['mtid'];
        $AwardName = $params['awardname'];
        $awarddata = base64_decode(str_replace(' ', '+', $params['awarddata']));
        $jsondata = json_decode($awarddata);
        $whereID=" and ID in ('".implode("','",$jsondata)."')";
        $isexist=$this->_dao->getone("select count(*) from meeting_member_award where Mtid = '$meetid' and AwardType=5 and Status=1");
        if($isexist>0){
            $arr['Success'] = 0;
            $arr['Message'] = "no";
        }else{
            $user = $this->_dao->query("select Mid,ID,PersonSign from meeting_member_contactman where Mtid = '$meetid'  and status=1 $whereID ");
            //备份记录
            $sql = "insert into meeting_member_award_del(`ID`, `Mtid`, `Cid`, `Mid`, `PersonSign`, `AwardType`, `Status`, `CreateDate`, `CreateUser`, `GetAwardTime`, `CancerTime`,mobile,truename,address,`DelTime`) select `ID`, `Mtid`, `Cid`, `Mid`, `PersonSign`, `AwardType`, `Status`, `CreateDate`, `CreateUser`, `GetAwardTime`, `CancerTime`,mobile,truename,address,NOW() from meeting_member_award where mtid='$meetid' and AwardType =5";
            $this->_dao->execute($sql);
            
            $this->_dao->execute("delete from meeting_member_award where Mtid = '$meetid' and AwardType=5");
            foreach($user as $key=>$value){
                //if(!isset($allinfo[$value['ID']])){
                    $this->_dao->execute("insert into meeting_member_award set Mtid = '$meetid',Cid = '".$value['ID']."',Mid = '".$value['Mid']."',PersonSign = '".$value['PersonSign']."',AwardType ='5',AwardName='$AwardName',Status = 0,CreateDate=NOW(),CreateUser='wuwenx' ");
                //}
            }
            $arr['Success'] = 1;
            $arr['Message'] = "ok";
        }
    
        $json_string = $this->pri_JSON($arr);
        if ($callback != "") {
            echo "$callback(" . $json_string . ")";
        } else {
            echo $json_string;
        }
    }
    



    public function addCancerAward($params){//将中了幸运奖的客户名单推送进数据库
        $xingyunhaoma=$params['xingyunhaoma'];
        $hmarr=explode(",",$xingyunhaoma);
        $arr = array('Success' => '0',
            'Message' => 'null',
        );
        $callback = $params['callback'];
        $meetid = $params['mtid'];
        $AwardName = $params['AwardName'];
        
        $isexist=$this->_dao->getone("select count(*) from meeting_member_award where Mtid = '$meetid' and AwardType=5 and Status=1");
        if($isexist>0){
            $arr['Success'] = 0;
            $arr['Message'] = "no";
        }else{
            $user = $this->_dao->query("select Mid,ID,PersonSign from meeting_member_contactman where Mtid = '$meetid'  and status=1 and PersonSign!='' and RIGHT( PersonSign, 1 ) in($xingyunhaoma) and PersonSign<9000000 order by PersonSign");
             //备份记录
            $sql = "insert into meeting_member_award_del(`ID`, `Mtid`, `Cid`, `Mid`, `PersonSign`, `AwardType`, `Status`, `CreateDate`, `CreateUser`, `GetAwardTime`, `CancerTime`,mobile,truename,address,`DelTime`) select `ID`, `Mtid`, `Cid`, `Mid`, `PersonSign`, `AwardType`, `Status`, `CreateDate`, `CreateUser`, `GetAwardTime`, `CancerTime`,mobile,truename,address,NOW() from meeting_member_award where mtid='$meetid' and AwardType =5";
            $this->_dao->execute($sql);
            
            $this->_dao->execute("delete from meeting_member_award where Mtid = '$meetid' and AwardType=5");
            foreach($user as $key=>$value){
                //if(!isset($allinfo[$value['ID']])){
                    $this->_dao->execute("insert into meeting_member_award set Mtid = '$meetid',Cid = '".$value['ID']."',Mid = '".$value['Mid']."',PersonSign = '".$value['PersonSign']."',AwardType ='5',AwardName='$AwardName',Status = 0,CreateDate=NOW(),CreateUser='wuwenx' ");
                //}
            }
            $arr['Success'] = 1;
            $arr['Message'] = "ok";
        }
    
        $json_string = $this->pri_JSON($arr);
        if ($callback != "") {
            echo "$callback(" . $json_string . ")";
        } else {
            echo $json_string;
        }
        //print_r("select * from meeting_member_contactman where Mtid = '$meetid'  and status=1 and RIGHT( PersonSign, 1 ) in($xingyunhaoma)");
    }


    public function clearCancerAward($params){//将中了幸运奖的客户名单清空
        $arr = array('Success' => '0',
            'Message' => 'null',
        );
        $callback = $params['callback'];
        $meetid = $params['mtid'];
        $awardtype = $params['awardtype'];
        $meetstatus=$this->_dao->getone("SELECT Status FROM `meeting_base` WHERE `ID` ='$meetid'");
         if($meetstatus>2){
             $arr['Success'] = 0;
             $arr['Message'] = "no";
         }else{
            
            //备份记录
            $sql = "insert into meeting_member_award_del(`ID`, `Mtid`, `Cid`, `Mid`, `PersonSign`, `AwardType`, `Status`, `CreateDate`, `CreateUser`, `GetAwardTime`, `CancerTime`,mobile,truename,address,`DelTime`) select `ID`, `Mtid`, `Cid`, `Mid`, `PersonSign`, `AwardType`, `Status`, `CreateDate`, `CreateUser`, `GetAwardTime`, `CancerTime`,mobile,truename,address,NOW() from meeting_member_award where mtid='$meetid' and AwardType ='$awardtype'";
            $this->_dao->execute($sql);
            $this->_dao->execute("delete from meeting_member_award where Mtid = '$meetid' and AwardType='$awardtype'");
            $arr['Success'] = 1;
            $arr['Message'] = "ok";
         }
    
        $json_string = $this->pri_JSON($arr);
        if ($callback != "") {
            echo "$callback(" . $json_string . ")";
        } else {
            echo $json_string;
        }
       
    }
    public function gbkToutf8($str)
    {
        if( mb_detect_encoding($str,"UTF-8, ISO-8859-1, GBK")!="UTF-8" ) {//判断是否不是UTF-8编码，如果不是UTF-8编码，则转换为UTF-8编码
            return iconv("gbk", "utf-8", $str);
        } else {
            return $str;
        }
    }

    public function getQiandaoList($params)
    {
        $RuType = $params['RuType'];
        $Mtid = $params['mtid'];
        if (!$Mtid) {
            $arr = array(
                'Success' => '0',
                'Message' => "未获取到当前会议信息，获取失败！",
                'ErrorCode' => '0001',        //未获取到mtid
            );
        } else if (!$RuType) {
            $arr = array(
                'Success' => '0',
                'Message' => "未获取到当前场景信息，获取失败！",
                'ErrorCode' => '0002',        //未获取到mtid
            );
        } else {
            $sql = "SELECT ruchang.Cid,ruchang.RuType,ruchang.Mtid,ruchang.Mid,contactman.PersonSign,contactman.Maid,contactman.TrueName,contactman.xuhao";
            $sql .= " FROM `meeting_member_ruchang` ruchang,`meeting_member_contactman` contactman WHERE ";
            $sql .= " ruchang.Cid=contactman.ID and ruchang.Mtid='" . $Mtid . "' ";
            $sql .= " and contactman.Status=1 ".(isset($GLOBALS['QDRuType'][$Mtid])&&$GLOBALS['QDRuType'][$Mtid][0]==$RuType?" and contactman.IsCheckIned=0 ":"")." and ruchang.RuStatus=0 and ruchang.RuType='" . $RuType . "' ";
            $sql .= " group by ruchang.Cid order by ruchang.ID ASC ";
            $data = $this->_dao->query($sql, 0);
            $arr['Success'] = '1';
            $arr['Result'] = $data;
        }
        $json_string = $this->pri_JSON($arr);
        echo $json_string;
        exit;
    }


    public function getLingJiangRuchangList($params)
    {
        $RuType = $params['RuType'];
        $Mtid = $params['mtid'];
        if (!$Mtid) {
            $arr = array(
                'Success' => '0',
                'Message' => "未获取到当前会议信息，获取失败！",
                'ErrorCode' => '0001',        //未获取到mtid
            );
        } else if (!$RuType) {
            $arr = array(
                'Success' => '0',
                'Message' => "未获取到当前场景信息，获取失败！",
                'ErrorCode' => '0002',        //未获取到mtid
            );
        } else {
            $sql = "SELECT ruchang.Cid,ruchang.RuType,ruchang.Mtid,ruchang.Mid,contactman.PersonSign,contactman.Maid,contactman.TrueName,contactman.xuhao";
            $sql .= " FROM `meeting_member_ruchang` ruchang,`meeting_member_contactman` contactman WHERE ";
            $sql .= " ruchang.Cid=contactman.ID and ruchang.Mtid='" . $Mtid . "' ";
            $sql .= " and contactman.Status=1 and ruchang.Status=1 and ruchang.RuType='" . $RuType . "' ";
            $sql .= " group by ruchang.Cid order by ruchang.ID ASC ";
            $data = $this->_dao->query($sql, 0);
            $arr['Success'] = '1';
            $arr['Result'] = $data;
        }
        $json_string = $this->pri_JSON($arr);
        echo $json_string;
        exit;
    }


    public function setQiandaoType($params)
    {
        $RuType = $params['RuType'];
        $RuStatus = ($params['RuStatus']=="" && $params['RuStatus']!="0") ? "1" : $params['RuStatus'];
        $Mtid = $params['mtid'];
        $Cid = $params['Cid'];
        if (!$Mtid) {
            $arr = array(
                'Success' => '0',
                'Message' => "未获取到当前会议信息，操作失败！",
                'ErrorCode' => '0001',
            );
        } else if (!$RuType) {
            $arr = array(
                'Success' => '0',
                'Message' => "未获取到当前场景信息，操作失败！",
                'ErrorCode' => '0002',
            );
        } else {
            $sql = "update meeting_member_ruchang set RuStatus='".$RuStatus."' where RuType='" . $RuType . "' and Mtid ='" . $Mtid . "' and Cid='" . $Cid . "' ";
            $this->_dao->execute($sql);

            $arr = array(
                'Success' => '1',
                'Message' => '成功',
            );
        }
        $json_string = $this->pri_JSON($arr);
        echo $json_string;
        exit;
    }
    protected function send_xwork_message($member, $message) {

        $dealtsuser=$this->dealtsuser($member);
    	$post = array(
        	'adminid' => urlencode($dealtsuser),
			'msgtype' => urlencode('text'),
			'content' => urlencode($message)
		);

        $sql = "insert into qywx_msg set `createtime`=NOW(),`content`='".json_encode($post)."', `msgtype`='text', `istips`=0";
        if(time()>strtotime('2024-10-31'))
        {
            $this->oaDao->execute($sql);
        }
        

		// $post_content = http_build_query($post);
		// $options = array(
		// 	'http' => array(
        //     	'method' => 'POST',
        // 	    'header' => 'Content-type:application/x-www-form-urlencoded',
        //     	'content' => $post_content,
    	//     )
	    // );

    	// $url = APP_URL_WORK."/qiye/SendMessage.php";
    	// $str= file_get_contents($url, false, stream_context_create($options));
    	//echo $str;
	}
    function dealtsuser($member)
    {
        $useridarr=array();
        if($member['UserID'])
        {
            $useridarr[]=$member['UserID'];
        }
        if($member['DepartID'])
        {
            $userid1=$GLOBALS['MRTSUSERID'];
            if(isset($GLOBALS['DEPTLIST'][$member['DepartID']]))
            {
                $userid1=$GLOBALS['DEPTLIST'][$member['DepartID']];
            }
            if(!in_array($userid1,$useridarr))
            {
                $useridarr[]=$userid1;
            }
        }
        else
        {
            if(!in_array($GLOBALS['MRTSUSERID'],$useridarr))
            {
                $useridarr[]=$GLOBALS['MRTSUSERID'];
            }
        }
        return implode(',',$useridarr);
    }

    public function getMeetingScreenData2($params){
        $Mtid=$params['mtid']==""?356:$params['mtid'];
        $contactmanSql="select ID,TrueName,Sex,Post,IsCheckIned,CheckInedDate from meeting_member_contactman where Status = 1 and Mtid='".$Mtid."' and IsCheckIned=1 and CheckInedDate!='0000-00-00 00:00:00' order by CheckInedDate asc";
        $contactmanData = $this->_dao->query($contactmanSql);

        // echo '<pre>';
        // print_r($contactmanData);exit;
        $dateData=array();
        $dateData2=array();
        foreach( $contactmanData  as $contactman){
            $day=date("d日H点",strtotime($contactman['CheckInedDate']));
            $day2=date("d",strtotime($contactman['CheckInedDate']));
            $dateData[$day]=$dateData[$day]+1;
            // if($day2==30||$day2==31){
            //     $day2=28;
            // }
            $dateData2[$day2]=$dateData2[$day2]+1;
        }
        // echo '<pre>';
        // print_r($dateData);exit;
        
        $resultData=array();
        $resultData2=array();
        foreach($dateData as $key=>$value){
            $arr['name']=$key;
            $arr['value']=$value;
            $resultData[]=$arr;
        }
        foreach($dateData2 as $key=>$value){
            $arr['name']=$key;
            $arr['value']=$value;
            $response['data']['screendata'.$key]=$arr;
        }
        $response['data']['screendata1']=$resultData;
        $response['Success'] = 1;
		$response['Message']='获取成功';
        echo $this->pri_JSON($response);

    }


    function clean_string($str) {
        // 去除全角空格和其他不可见字符
        $str = preg_replace('/\x{3000}/u', ' ', $str);  // 替换全角空格为半角空格
        $str = preg_replace('/\s+/u', ' ', $str);  // 替换多余的空格
        return trim($str);  // 去除首尾空格
    }
    

    public function getMeetingSurveyScreenData($params){
        $type=$params['type']!=""?$params['type']:1;
        $mtid=$params['mtid']!=""?$params['mtid']:357;
        $isphone=$params['isphone'];
        $surveyid=$params['id'];
        $survey=$this->_dao->getSurvey($surveyid);
        $where="";
        if($survey['relation_id']!="" && $survey['relation_id']!=0){
            $where=" and id in ($surveyid,$survey[relation_id]) ";
            $id=$this->_dao->getSurveyById($mtid,$type, $where);
        }else{
            if($mtid!=""){
                $id=$this->_dao->getSurveyById($mtid,$type,$where);
            }
        }
        
        $newsurvey=$this->_dao->getSurvey($id);
        $surveyId=$id==""?1:$id;
        $questionData=$this->_dao->getQuestionBySurveyId($surveyId);
        // echo '<pre>';
        // print_r( $questionData);
        $questionRe=array();
        $last_question_number="";
        foreach($questionData as $key=>$data){
            $number=$data['number'];
            // if($mtid==412){
            //     if($data['number']==8){
            //         $number=4;
            //     }
            //     if($data['number']==4){
            //         $number=6;
            //     }
            //     if($data['number']==5){
            //         $number=7;
            //     }
            //     if($data['number']==6){
            //         $number=8;
            //     }
            //     if($data['number']==7){
            //         $number=9;
            //     }
            // }
            if(!isset($questionRe[$data['questionId']])){
                $questionRe[$data['questionId']]['result']=$this->_dao->getQuestionOptionPersonCount(" and questionId='".$data['questionId']."'" );
                $xuhao=$number;
                 //上期所问题
                if (strpos($data['questionName'], "是否曾参与过螺纹、热卷的非主力") !== false) {
                    $last_question_number=$number;
                }
                if (strpos($data['questionName'], "接上述选择题") !== false && strpos($data['questionName'], "如果您没有参与过螺纹、热卷非主力合约的交易和持仓") !== false) {
                    preg_match('/选择题(\d+)/', $data['questionName'], $matches);
                    $cs_number = $matches[1];
                    if($last_question_number!="" && $last_question_number!=$cs_number){
                        $data['questionName']=str_replace("选择题".$cs_number,"选择题".$last_question_number,$data['questionName']);
                    }
                }

                // if($number>5 && $mtid!=412 && $isphone!=1){
                //     $xuhao=$number-5;
                //     $data['questionName']=str_replace("题8","题3",$data['questionName']);
                //     $data['questionName']=str_replace("题7","题2",$data['questionName']);
                //     $data['questionName']=str_replace("题6","题1",$data['questionName']);
                // }else if($number>5 && $mtid==412 && $isphone!=1){
                //     $xuhao=$number-5;
                //     $data['questionName']=str_replace("题6","题3",$data['questionName']);
                // }
                $response['data']['screentitle'.$number]=$xuhao.". ".$data['questionName'];
            }
            if($data['questionType']!=2){
                $arr=array();
                $sort=$data['optionSort']!=""&&$data['optionSort']!=0?$this->numberToLetter($data['optionSort']):"";
                $data['optionName']=$this->clean_string($data['optionName']);
                $arr['name']=$sort.'.'.$data['optionName'];
                $where= " and questionId='".$data['questionId']."' and optionId='".$data['optionId']."'";
                $sum=$this->_dao->getQuestionOptionPersonCount($where);
                $arr['value']=$sum;
                $arr['result']=$questionRe[$data['questionId']]['result']>0?100*(round($sum/$questionRe[$data['questionId']]['result'],2)):0;
                $response['data']['screendata'.$number][]=$arr;
            }else{
                //$arr['result']=$this->_dao->getQuestionOptionPerson(" and questionId='".$data['questionId']."'" );
                $response['data']['screendata'.$number]=$this->_dao->getQuestionOptionPerson(" and questionId='".$data['questionId']."'" );
            }
        }

        $num=$this->_dao->getQuestionOptionPersonTotal($surveyId);
        $response['data']['num2']=$num;
        $contactmanSql="select count(*)  from meeting_member_contactman where Status = 1 and Mtid='$mtid' and IsCheckIned=1";
        $numData = $this->_dao->getOne($contactmanSql);
        $response['data']['num1']=$numData;
        $response['data']['title']=$newsurvey['surveyTitle'];
        $response['Success'] = 1;
		$response['Message']='获取成功';
        echo $this->pri_JSON($response);
    }


    public function getMeetingSurveyScreenData_2($params){
        $type=$params['type']!=""?$params['type']:1;
        $mtid=$params['mtid']!=""?$params['mtid']:357;
        $isphone=$params['isphone'];
        $surveyid=$params['id'];
        $survey=$this->_dao->getSurvey($surveyid);
        $where="";
        if($survey['relation_id']!="" && $survey['relation_id']!=0){
            $where=" and id in ($surveyid,$survey[relation_id]) ";
            $id=$this->_dao->getSurveyById($mtid,$type, $where);
        }else{
            if($mtid!=""){
                $id=$this->_dao->getSurveyById($mtid,$type,$where);
            }
        }

        $newsurvey=$this->_dao->getSurvey($id);
        $surveyId=$id==""?1:$id;
        $questionData=$this->_dao->getQuestionBySurveyId($surveyId);


        $question_arr=array();

        $question_1="集中在1/5/10合约上";
        $question_1_1="是否有逐月交易、持仓、交割需求";
        $question_2="是否关注到上期所针对螺纹";
        $question_3="是否曾参与过螺纹、热卷非1/5/10合约的交易";
        $question_3_1="是否曾参与过螺纹、热卷的非主力";
        $question_4="如没有参与过螺纹、热卷非1/5/10合约的交易、持仓";
        $question_4_1="没有参与过螺纹、热卷非主力合约的交易和持仓";
        $qnum=1;
        foreach($questionData as $key=>$data){
            if ((strpos($data['questionName'],$question_1 ) !== false && strpos($data['questionName'],$question_1_1 ) !== false)  || strpos($data['questionName'],$question_2 ) !== false ||strpos($data['questionName'],$question_3 ) !== false|| strpos($data['questionName'],$question_4 ) !== false|| strpos($data['questionName'],$question_3_1 ) !== false|| strpos($data['questionName'],$question_4_1 ) !== false) {
                $question_arr[]=$data;
                // array(
                //     "qnum"=>$qnum,
                //     "qid"=>$data['id'],
                //     "qnumber"=>$data['id'],
                //     "questionName"=>$data['questionName'],
                // );
            }
            //$qnum++;
        }



        //echo '<pre>';
        //print_r( $questionData);exit;
        $questionRe=array();
        $qnum=1;
        $number_arr=array();
        foreach($question_arr as $key=>$data){
            $number=$data['number'];
            if(!isset($questionRe[$data['questionId']])){
                $questionRe[$data['questionId']]['result']=$this->_dao->getQuestionOptionPersonCount(" and questionId='".$data['questionId']."'" );
                $xuhao=$qnum;
                $number_arr[$number]=$xuhao;


                $data['questionName']=str_replace("题2","题".$number_arr[2],$data['questionName']);
                $data['questionName']=str_replace("题3","题".$number_arr[3],$data['questionName']);
                $data['questionName']=str_replace("题4","题".$number_arr[4],$data['questionName']);
                $data['questionName']=str_replace("题5","题".$number_arr[5],$data['questionName']);
                $data['questionName']=str_replace("题6","题".$number_arr[6],$data['questionName']);
                $data['questionName']=str_replace("题7","题".$number_arr[7],$data['questionName']);
                $data['questionName']=str_replace("题8","题".$number_arr[8],$data['questionName']);
                


                $response['data']['screentitle'.$qnum]=$qnum.". ".$data['questionName'];
                $qnum++;
            }
            if($data['questionType']!=2){
                $arr=array();
                $sort=$data['optionSort']!=""&&$data['optionSort']!=0?$this->numberToLetter($data['optionSort']):"";
                $arr['name']=$sort.'.'.$data['optionName'];
                $where= " and questionId='".$data['questionId']."' and optionId='".$data['optionId']."'";
                $sum=$this->_dao->getQuestionOptionPersonCount($where);
                $arr['value']=$sum;
                $arr['result']=$questionRe[$data['questionId']]['result']>0?100*(round($sum/$questionRe[$data['questionId']]['result'],2)):0;
                $response['data']['screendata'.$xuhao][]=$arr;
            }else{
                $response['data']['screendata'.$xuhao]=$this->_dao->getQuestionOptionPerson(" and questionId='".$data['questionId']."'" );
            }
        }


        $response['data']['title']=$newsurvey['surveyTitle'];
        $response['Success'] = 1;
		$response['Message']='获取成功';

       // echo '<pre>';
       // print_r($response);exit;
        echo $this->pri_JSON($response);
    }

    public function numberToLetter($number) {
		if ($number >= 1 && $number <= 26) {
			// 将数字转换成 ASCII 码，A 对应 65，B 对应 66，依此类推
			$asciiCode = $number + 64;
			return chr($asciiCode);
		} else {
			return "";
		}
	}

    public function getMeetingScreenData($params){
        $Mtid=$params['mtid']==""?356:$params['mtid'];

        //参会单位
        $memberSql="SELECT mm.comtype as name,count(*) as value FROM meeting_member_contactman mmc left join  meeting_member AS mm on mm.Mtid = mmc.Mtid AND mm.MidEn =0 AND mmc.Mid = mm.Mid  where  mm.Mtid=mmc.Mtid and   mm.Mtid='$Mtid'  and mmc.Mtid='$Mtid'  and mmc.Status=1 and mm.Status=1 group by mm.comtype";
        $memberData = $this->_dao->query($memberSql);
        $new_memberData=array();
        $new_memberData_9['name']="其他";
        $new_memberData_9['value']=0;

        foreach($memberData as $key => $member){
            if($member['name']=="证券行业" || $member['name']=="驻华机构" || $member['name']=="" || $member['name']=="最终用户"){
                $new_memberData_9['value']=$new_memberData_9['value'] +$member['value'];
            }else{
                $new_memberData[]=$member;
            }
        }
        $new_memberData[9]=$new_memberData_9;
        $response['data']['screendata3']=$new_memberData;
        // echo '<pre>';
        // print_r($new_memberData);
        //27、28参会人数
        $contactmanSql="(select count(*) as value from meeting_member_contactman where Status = 1 and Mtid='".$Mtid."' and ( IsChildMeeting=1 or IsChildMeeting2=1  or IsChildMeeting3=1 or IsChildMeeting4=1 or IsChildMeeting5=1)) union all  (select count(*) as value from meeting_member_contactman where Status = 1 and Mtid='".$Mtid."' and IsMainMeeting=1)";
        $meetingData = $this->_dao->query($contactmanSql);
        $meetingData[0]['name']="27日";
        $meetingData[1]['name']="28日";
        $response['data']['screendata4']=$meetingData;
        // echo '<pre>';
        // print_r($meetingData);

        
        
        //参会代表
        $contactmanSql="select ID,TrueName,Sex,Post,IsCheckIned,CheckInedDate from meeting_member_contactman where Status = 1 and Mtid='".$Mtid."' order by ID desc";
        $contactmanData = $this->_dao->query($contactmanSql);

        $PostData=array(
            "0"=>array("name"=>"董事长"),
            "1"=>array("name"=>"总经理"),
            "2"=>array("name"=>"经理"),
            "3"=>array("name"=>"部长"),
            "4"=>array("name"=>"总监"),
            "5"=>array("name"=>"主管"),
            "6"=>array("name"=>"分析师"),
            "7"=>array("name"=>"助理"),
            "8"=>array("name"=>"处长"),
            "9"=>array("name"=>"其他")
        );
        $SexData=array(
            "0"=>array("name"=>"男"),
            "1"=>array("name"=>"女")
        );

        foreach($contactmanData as $key=>$contactman){
            if((strpos($contactman['Post'],"董事长")!==false||strpos($contactman['Post'],"创始人")!==false)&&strpos($contactman['Post'],"董事长助理")===false){
                $PostData[0]['value']=$PostData[0]['value'] +1;
            }else if(strpos($contactman['Post'],"总经理")!==false&&strpos($contactman['Post'],"总经理助理")===false){
                $PostData[1]['value']=$PostData[1]['value'] +1;
            }else if(strpos($contactman['Post'],"经理")!==false){
                $PostData[2]['value']=$PostData[2]['value'] +1;
            }else if(strpos($contactman['Post'],"部长")!==false&&strpos($contactman['Post'],"部长助理")===false){
                $PostData[3]['value']=$PostData[3]['value'] +1;
            }else if(strpos($contactman['Post'],"处长")!==false&&strpos($contactman['Post'],"处长助理")===false){
                $PostData[4]['value']=$PostData[4]['value'] +1;
            }else if(strpos($contactman['Post'],"总监")!==false){
                $PostData[5]['value']=$PostData[5]['value'] +1;
            }else if(strpos($contactman['Post'],"主管")!==false){
                $PostData[6]['value']=$PostData[6]['value'] +1;
            }else if(strpos($contactman['Post'],"分析师")!==false){
                $PostData[7]['value']=$PostData[7]['value'] +1;
            }else if(strpos($contactman['Post'],"助理")!==false){
                $PostData[8]['value']=$PostData[8]['value'] +1;
            }else{
                $PostData[9]['value']=$PostData[9]['value'] +1;
            }

           
            if(strpos($contactman['Sex'],"男")!==false){
                $SexData[0]['value']=!isset($SexData[0]['value'])?0:$SexData[0]['value'] +1;
            }else if(strpos($contactman['Sex'],"女")!==false){
                $SexData[1]['value']=!isset($SexData[1]['value'])?0:$SexData[1]['value'] +1;
            }
        }

        $response['data']['screendata1']=$PostData;
        $response['data']['screendata2']=$SexData;

        $response['Success'] = 1;
		$response['Message']='获取成功';
        echo $this->pri_JSON($response);
    }


    

    public function getmap($params)
	{

       // $province = array('0'=>"上海",'1'=>"北京",'2'=>"天津",'3'=>"江苏",'4'=>"浙江",'5'=>"重庆",'6'=>"香港",'7'=>"澳门",'8'=>"台湾",'9'=>"福建",'10'=>"广东",'11'=>"湖北",'12'=>"湖南",'13'=>"江西",'14'=>"山东",'15'=>"河南",'16'=>"山西",'17'=>"陕西",'18'=>"甘肃",'19'=>"宁夏",'20'=>"内蒙",'21'=>"青海",'22'=>"西藏",'23'=>"四川",'24'=>"新疆",'25'=>"云南",'26'=>"贵州",'27'=>"广西",'28'=>"河北",'29'=>"黑龙江",'30'=>"辽宁",'31'=>"吉林",'32'=>"海南",'33'=>"安徽");

        $Mtid=$params['mtid']==""?356:$params['mtid'];
        $memberSql="SELECT  mm.memberstate as name,count(*) as value FROM  meeting_member AS mm left join  meeting_member_contactman mmc on mm.Mtid = mmc.Mtid AND mm.MidEn =0 AND mmc.Mid = mm.Mid  where  mm.Mtid=mmc.Mtid and   mm.Mtid='$Mtid'  and mmc.Mtid='$Mtid'  and mmc.Status=1 and mm.Status=1 group by mm.memberstate order by value desc";
        $memberData = $this->_dao->query($memberSql);
        $newProvince=array();
        $sum=0;
        foreach($memberData as $key=>$member){
            $list=isset($GLOBALS['address'][$member['name']])?$GLOBALS['address'][$member['name']]:array();
            $list['city']=$GLOBALS['province'][$member['name']];
            $list['province']=$GLOBALS['province'][$member['name']];
            $list['factory']="";
            $list['value']=$member['value']."人";
            $list['raise']=$key<3?($key+1):0;
            $list['fillMapColor']="true";
            $newProvince[]=$list;
            $sum=$sum+$member['value'];
        }
        $response['data']=$newProvince;
        $response['datetime']=date("Y-m-d");
        
        if($params['debug']==1){
            echo '<pre>';
            print_r($memberData);
            echo $sum;
        }
        
        echo $this->pri_JSON($response);
	}


    public function survey($params){
        $params['mtid']=$params['mtid']==""?357:$params['mtid'];
        $meetname = $this->_dao->getRow("select StartDate,EndDate,MeetingName from meeting_base where ID  = '".$params['mtid']."' limit 1");
        $survey=$this->_dao->getSurvey($params['id']);

        $where="";
        if($survey['relation_id']!="" && $survey['relation_id']!=0){
            $survey['relation_id']=str_replace("，",",",$survey['relation_id']);
            $arr=explode(",",$survey['relation_id']);
            $arr[]=$params['id'];
            $arr=array_unique($arr);
            $where=" and id in ('".implode("','",$arr)."')";
            
            $surveylist=$this->_dao->getAllSurveyByMtid($params['mtid'],$where);
        }else{
            $surveylist=$this->_dao->getAllSurveyByMtid($params['mtid'],$where);
        }

        if($survey){
            $meetname['MeetingName']=$survey['surveyTitle'];
        }




        $questionnum=$this->_dao->getQuestionnum($surveylist[1]);
        $this->setsurvey($params['mtid'],$meetname,$params['showtitle']);
        $this->assign("meetname",$meetname);
        $this->assign("params",$params);
        $this->assign("surveylist",$surveylist);
        $this->assign("questionnum",$questionnum);
    }

    public function survey_meeting($params){
        $params['mtid']=$params['mtid']==""?357:$params['mtid'];
        $meetname = $this->_dao->getRow("select StartDate,EndDate,MeetingName from meeting_base where ID  = '".$params['mtid']."' limit 1");
        $survey=$this->_dao->getSurvey($params['id']);
        $where="";
        if($survey['relation_id']!="" && $survey['relation_id']!=0){
            $survey['relation_id']=str_replace("，",",",$survey['relation_id']);
            $arr=explode(",",$survey['relation_id']);
            $arr[]=$params['id'];
            $arr=array_unique($arr);
            $where=" and id in ('".implode("','",$arr)."')";
            
            $surveylist=$this->_dao->getAllSurveyByMtid($params['mtid'],$where);
        }else{
            $surveylist=$this->_dao->getAllSurveyByMtid($params['mtid'],$where);
        }

        if($survey){
            $meetname['MeetingName']=$survey['surveyTitle'];
        }
        if($meetname['StartDate']!=$meetname['EndDate']){
            $meetingdate=date("Y年m月d日",strtotime($meetname['StartDate']))."至".date("Y年m月d日",strtotime($meetname['EndDate']));
        }else{
            $meetingdate=date("Y年m月d日",strtotime($meetname['StartDate']));
        }

        $questionnum=$this->_dao->getQuestionnum($surveylist[1]);
        $this->assign("meetname",$meetname);
        $this->assign("meetingdate",$meetingdate);
        $this->assign("params",$params);
        $this->assign("surveylist",$surveylist);
        $this->assign("questionnum",$questionnum);
    }

    //区域会议  单奖项抽奖
    public function meeting_choujiang($params){
        $params['mtid']=$params['mtid']!=""?$params['mtid']:'429';
        if($params['mtid']==434){
            echo "链接失效！！！！！";exit;
        }
        if($params['mtid']==""){
            echo "参数错误！！！！！";exit;
        }else{
            $meeting_base = $this->_dao->getRow("select StartDate,EndDate,MeetingName from meeting_base where ID  = '".$params['mtid']."' limit 1");
            if(empty($meeting_base)){
                echo "会议不存在！！！！！";exit;
            }else{
                $meeting_cjset = $this->_dao->getRow("select turnumber from meeting_cjset where mtid  = '".$params['mtid']."' limit 1");
                $turnumber=!empty($meeting_cjset) && $meeting_cjset['turnumber']!="" ? $meeting_cjset['turnumber'] : '5' ;
                $turnumber=str_replace("，",",",$turnumber);
                $turnumberarr = explode(",", $turnumber);
                $cjnum=array_sum($turnumberarr);
                $data=file_get_contents("https://weixin.steelhome.cn/wxsignin/lottery/meetingchoujiang.php?action=getmes&type=jsonp&mtid=".$params['mtid']);
                $this->assign("data",$data);
                $this->assign("mtid",$params['mtid']);
                $this->assign("turnumber",$turnumber);
                $this->assign("cjnum",$cjnum);
                $this->assign("params",$params);
                $this->assign("meetingName",$meeting_base['MeetingName']);
            }
        }
    }

    //区域会议多奖项抽奖 川钢协
    public function choujiang($params){
       
        $params['mtid']=$params['mtid']!=""?$params['mtid']:'434';
        if($params['mtid']==""|| $params['jx']==""){
            echo "参数错误！！！！！";exit;
        }else{
            $meeting_base = $this->_dao->getRow("select StartDate,EndDate,MeetingName from meeting_base where ID  = '".$params['mtid']."' limit 1");
            if(empty($meeting_base)){
                echo "会议不存在！！！！！";exit;
            }else{
                $meeting_cjset = $this->_dao->getRow("select turnumber,jxnames from meeting_cjset where mtid  = '".$params['mtid']."' and AwardType='".$params['jx']."' limit 1");
                if(empty($meeting_cjset)){
                    echo "参数有误！！！";exit;
                }
                $this->assign("jxnames",$meeting_cjset['jxnames']);
                $turnumber=!empty($meeting_cjset) && $meeting_cjset['turnumber']!="" ? $meeting_cjset['turnumber'] : '5' ;
                $turnumber=str_replace("，",",",$turnumber);
                $turnumberarr = explode(",", $turnumber);
                $cjnum=array_sum($turnumberarr);
                $this->assign("turnumber",$turnumber);
                $this->assign("cjnum",$cjnum);
                $data=file_get_contents("https://weixin.steelhome.cn/wxsignin/lottery/meetingchoujiang.php?action=getmes&type=jsonp&mtid=".$params['mtid']);
                
                $ip=$this->getIP();
                $this->_dao->execute("insert into collect_log set c_id='434',c_listurl='$ip',dtflag='".$params['jx']."',acttime=NOW() ");    
                
                $this->assign("data",$data);
                $this->assign("mtid",$params['mtid']);
                $this->assign("params",$params);
                $this->assign("jx",$params['jx']);
                $digitalNumber=array('0','1','2','3','4','5','6','7','8','9','10');
                $chineseNumber=array( "特","一","二", "三","四", "五", "六", "七","八", "九", "十");
                $jxtitle=str_replace($digitalNumber,$chineseNumber,$params['jx']);
                $this->assign("jxtitle",$jxtitle."等奖");
            }
        }
    }


    //钢之家新春联谊会 全体同步为阳光普照奖
    public function push_ygpz($params){
        $meetid=$params['mtid']!=""?$params['mtid']:'433';
        $awardtype=$params['awardtype']!=""?$params['awardtype']:7;
        $data=array();
        $data_json=file_get_contents("https://weixin.steelhome.cn/wxsignin/lottery/newlottery.php?action=getmes&getLr=1&mtid=".$meetid);
        if($data_json!="" && $data_json!="[]"){
            $data=json_decode($data_json);
        }

        $arr = array(
            'Success' => '0',
            'Message' => '参数缺失'
        );
        $meetstatus= $this->_dao->getone("SELECT Status FROM `meeting_base` WHERE `ID` ='$meetid'");
        if($meetstatus>2){
            $arr['Success'] = 0;
            $arr['Message'] = "同步失败，会议已结束";
        }else if ($awardtype=="" || empty($meetid) || empty($data)) {
            $arr['Message'] = '同步失败，参数缺失';
        }else {
            //备份记录
            $sql = "insert into meeting_member_award_del(`ID`, `Mtid`, `Cid`, `Mid`, `PersonSign`, `AwardType`,`AwardName`, `Status`, `CreateDate`, `CreateUser`, `GetAwardTime`, `CancerTime`,mobile,truename,address,`DelTime`) select `ID`, `Mtid`, `Cid`, `Mid`, `PersonSign`, `AwardType`,`AwardName`, `Status`, `CreateDate`, `CreateUser`, `GetAwardTime`, `CancerTime`,mobile,truename,address,NOW() from meeting_member_award where mtid='$meetid' and AwardType ='$awardtype'";
            $this->_dao->execute($sql);
            //删除记录
            $sql = " delete from meeting_member_award where mtid='$meetid' and AwardType ='$awardtype'";
            $this->_dao->execute($sql);
            foreach ($data as $key => $value) {
                $this->_dao->execute("insert into meeting_member_award set Mtid='$meetid',Cid='".$value->uid."',Mid='".$value->Mid."',PersonSign ='".$value->PersonSign."',AwardType='$awardtype',AwardName='牛奶绒毛毯',Status = '0',CreateDate=NOW(),CreateUser='本地推送' ");
            }
            $arr['Success'] = '1';
            $arr['Message'] = '同步成功';
        }
        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    //钢之家新春联谊会抽奖
    public function newyear_choujiang($params){
        $params['mtid']=$params['mtid']!=""?$params['mtid']:'433';
        if($params['mtid']==""|| $params['jx']==""){
            echo "参数错误！！！！！";exit;
        }else{
            $meeting_base = $this->_dao->getRow("select StartDate,EndDate,MeetingName from meeting_base where ID  = '".$params['mtid']."' limit 1");
            if(empty($meeting_base)){
                echo "会议不存在！！！！！";exit;
            }else{
                $meeting_cjset = $this->_dao->getRow("select * from meeting_cjset where mtid  = '".$params['mtid']."' and AwardType='".$params['jx']."' limit 1");
                if(empty($meeting_cjset)){
                    echo "参数有误！！！";exit;
                }
                $this->assign("jxnames",$meeting_cjset['jxnames']);
                $turnumber=!empty($meeting_cjset) && $meeting_cjset['turnumber']!="" ? $meeting_cjset['turnumber'] : '5' ;
                $turnumber=str_replace("，",",",$turnumber);
                $turnumberarr = explode(",", $turnumber);
                $cjnum=array_sum($turnumberarr);
                $this->assign("turnumber",$turnumber);
                $this->assign("cjnum",$cjnum);

                //奖品价值
                $jp_name_price= $meeting_cjset['jxprice']!=""?$meeting_cjset['jxnames']."　".$meeting_cjset['jxprice']:$meeting_cjset['jxnames'];
                $this->assign("jp_name_price",$jp_name_price);
                if($params['jx']==4){
                    $data=file_get_contents("https://weixin.steelhome.cn/wxsignin/lottery/newlottery.php?action=getmes&getLr=1&getGzj=1&mtid=".$params['mtid']);
                }else{
                    $data=file_get_contents("https://weixin.steelhome.cn/wxsignin/lottery/newlottery.php?action=getmes&getLr=1&mtid=".$params['mtid']);
                } 
                $this->assign("data",$data);
                $this->assign("mtid",$params['mtid']);
                $this->assign("params",$params);
                $this->assign("jx",$params['jx']);
                $digitalNumber=array('1','2','3','4','5','6','7','8','9','10');
                $chineseNumber=array( "一","二", "三","特", "五", "六", "七","八", "九", "十");
                $jxtitle=str_replace($digitalNumber,$chineseNumber,$params['jx']);
                $this->assign("jxtitle","幸运".$jxtitle."等奖");
            }
        }
    }


    

    //区域会议抽奖设置  多奖项
    public function choujiang_set($params){
        $params['mtid']=$params['mtid']!=""?$params['mtid']:'429';
        if($params['mtid']==""){
            echo "参数错误！！！！！";exit;
        }else{
            $meeting_base = $this->_dao->getRow("select StartDate,EndDate,MeetingName from meeting_base where ID  = '".$params['mtid']."' limit 1");
            if(empty($meeting_base)){
                echo "会议不存在！！！！！";exit;
            }else{
                $this->assign("params",$params);
                $this->assign("mtid",$params['mtid']);
                $this->assign("meetingName",$meeting_base['MeetingName']);
            }
        }
    }

    //新春联谊会抽奖设置
    public function newyear_cjset($params){
        $params['mtid']=$params['mtid']!=""?$params['mtid']:'433';
        if($params['mtid']=="" || $params['jx']==""){
            echo "参数错误！！！！！";exit;
        }else{
            $meeting_base = $this->_dao->getRow("select StartDate,EndDate,MeetingName from meeting_base where ID  = '".$params['mtid']."' limit 1");
            if(empty($meeting_base)){
                echo "会议不存在！！！！！";exit;
            }else{
                $this->assign("params",$params);
                $this->assign("mtid",$params['mtid']);
                $this->assign("meetingName",$meeting_base['MeetingName']);
            }
        }
    }

    //区域会议抽奖设置  单奖项
    public function cj_set($params){
        $params['mtid']=$params['mtid']!=""?$params['mtid']:'434';
        if($params['mtid']=="" || $params['jx']==""){
            echo "参数错误！！！！！";exit;
        }else{
            $meeting_base = $this->_dao->getRow("select StartDate,EndDate,MeetingName from meeting_base where ID  = '".$params['mtid']."' limit 1");
            if(empty($meeting_base)){
                echo "会议不存在！！！！！";exit;
            }else{
                $this->assign("params",$params);
                $this->assign("mtid",$params['mtid']);
                $this->assign("meetingName",$meeting_base['MeetingName']);
            }
        }
    }

    




    public function setsurvey($mtid,$meetname,$showtitle){
        $title2="";
        $height="";
        $top="";
        $datetitle="";
        if($mtid>=408 || $mtid==403){
            if($showtitle==1){
                $title2='<h2><span class="font1">上期“强源助企”产融服务基地 问卷调查</span></h2>';
                $top="top:90px;";
            }else{
                $top="top:65px;";
            }
            if($meetname['StartDate']!=$meetname['EndDate']){
                $meetingdate=date("Y年m月d日",strtotime($meetname['StartDate']))."至".date("Y年m月d日",strtotime($meetname['EndDate']));
            }else{
                $meetingdate=date("Y年m月d日",strtotime($meetname['StartDate']));
            }
            $datetitle='<div style="height: 40px;" ><div class="datetitle title " >'.$meetingdate.'</div></div>';
        }
        $this->assign("datetitle",$datetitle);
        $this->assign("title2",$title2);
        $this->assign("top",$top);
    }

    public function survey_phone($params){
        $params['mtid']=$params['mtid']==""?357:$params['mtid'];
        $meetname = $this->_dao->getRow("select StartDate,EndDate,MeetingName from meeting_base where ID  = '".$params['mtid']."' limit 1");
        $survey=$this->_dao->getSurvey($params['id']);
        $where="";
        if($survey['relation_id']!="" && $survey['relation_id']!=0){
            $survey['relation_id']=str_replace("，",",",$survey['relation_id']);
            $arr=explode(",",$survey['relation_id']);
            $arr[]=$params['id'];
            $arr=array_unique($arr);
            $where=" and id in ('".implode("','",$arr)."')";
            $surveylist=$this->_dao->getAllSurveyByMtid($params['mtid'],$where);
        }else{
            $surveylist=$this->_dao->getAllSurveyByMtid($params['mtid'],$where);
        }
        if($survey){
            $meetname['MeetingName']=$survey['surveyTitle'];
        }
        $questionnum=$this->_dao->getQuestionnum($surveylist[1]);
        $this->setsurvey($params['mtid'],$meetname,$params['showtitle']);
        $this->assign("meetname",$meetname);
        $this->assign("params",$params);
        $this->assign("surveylist",$surveylist);
        $this->assign("questionnum",$questionnum);
    }
    public function work($params){
        $data=array(
            array("name"=>"2023-10-24","value"=>"3"),
            array("name"=>"2023-10-25","value"=>"3"),
            array("name"=>"2023-10-26","value"=>"1"),
            array("name"=>"2023-10-27","value"=>"4"),
            array("name"=>"2023-10-28","value"=>"6"),
            array("name"=>"2023-10-30","value"=>"7"),
            array("name"=>"2023-10-31","value"=>"3"),
            array("name"=>"2023-11-01","value"=>"8"),
            array("name"=>"2023-11-02","value"=>"34"),
            array("name"=>"2023-11-03","value"=>"10"),
            array("name"=>"2023-11-06","value"=>"7"),
            array("name"=>"2023-11-07","value"=>"5"),
            array("name"=>"2023-11-08","value"=>"2"),
            array("name"=>"2023-11-09","value"=>"7"),
            array("name"=>"2023-11-10","value"=>"4"),
            array("name"=>"2023-11-11","value"=>"3"),
            array("name"=>"2023-11-13","value"=>"3"),
            array("name"=>"2023-11-14","value"=>"4"),
            array("name"=>"2023-11-15","value"=>"10"),
            array("name"=>"2023-11-17","value"=>"6"),
            array("name"=>"2023-11-18","value"=>"1"),
            array("name"=>"2023-11-20","value"=>"1"),
            array("name"=>"2023-11-21","value"=>"4"),
            array("name"=>"2023-11-22","value"=>"3"),
            array("name"=>"2023-11-23","value"=>"3"),
            array("name"=>"2023-11-24","value"=>"1"),
            array("name"=>"2023-11-27","value"=>"3"),
            array("name"=>"2023-11-28","value"=>"2"),
            array("name"=>"2023-11-29","value"=>"3"),
            array("name"=>"2023-11-30","value"=>"6"),
            array("name"=>"2023-12-01","value"=>"7"),
            array("name"=>"2023-12-02","value"=>"2"),
            array("name"=>"2023-12-04","value"=>"1"),
            array("name"=>"2023-12-05","value"=>"7"),
            array("name"=>"2023-12-06","value"=>"5"),
            array("name"=>"2023-12-07","value"=>"1"),
            array("name"=>"2023-12-08","value"=>"6"),
            array("name"=>"2023-12-10","value"=>"1"),
            array("name"=>"2023-12-11","value"=>"11"),
            array("name"=>"2023-12-12","value"=>"8"),
            array("name"=>"2023-12-13","value"=>"4"),
            array("name"=>"2023-12-14","value"=>"7"),
            array("name"=>"2023-12-15","value"=>"3"),
            array("name"=>"2023-12-18","value"=>"7"),
            array("name"=>"2023-12-19","value"=>"9"),
            array("name"=>"2023-12-20","value"=>"2"),
            array("name"=>"2023-12-21","value"=>"7"),
            array("name"=>"2023-12-22","value"=>"9"),
            array("name"=>"2023-12-23","value"=>"3"),
            array("name"=>"2023-12-25","value"=>"2"),
            array("name"=>"2023-12-26","value"=>"8"),
            array("name"=>"2023-12-27","value"=>"6"),
            array("name"=>"2023-12-28","value"=>"5"),
            array("name"=>"2023-12-29","value"=>"6"),
            array("name"=>"2023-12-30","value"=>"1"),
            array("name"=>"2024-01-02","value"=>"11"),
            array("name"=>"2024-01-03","value"=>"19"),
            array("name"=>"2024-01-04","value"=>"10"),
            array("name"=>"2024-01-05","value"=>"10"),
            array("name"=>"2024-01-06","value"=>"1"),
            array("name"=>"2024-01-08","value"=>"7"),
            array("name"=>"2024-01-09","value"=>"3"),
            array("name"=>"2024-01-10","value"=>"9"),
        );

        $values=array();
        $names=array();
        foreach($data as $key=>$val){
            $values[]=$val['value'];
            $names[]=$val['name'];
        }
        $names_text=implode("','",$names);
        $values_text=implode(',',$values);
        $this->assign("values",$values_text);
        $this->assign("names",$names_text);


        $data2=array(
            array("name"=>"10.24-10.28","value"=>"17"),
            array("name"=>"10.30-11.03","value"=>"62"),
            array("name"=>"11.06-11.11","value"=>"28"),
            array("name"=>"11.13-11.18","value"=>"24"),
            array("name"=>"11.20-11.24","value"=>"12"),
            array("name"=>"11.27-12.02","value"=>"23"),
            array("name"=>"12.04-12.10","value"=>"21"),
            array("name"=>"12.11-12.15","value"=>"33"),
            array("name"=>"12.18-12.23","value"=>"37"),
            array("name"=>"12.25-12.30","value"=>"28"),
            array("name"=>"01.02-01.06","value"=>"51"),
            array("name"=>"01.08-01.10","value"=>"19"),
        );

        $values2=array();
        $names2=array();
        foreach($data2 as $key=>$val){
            $values2[]=$val['value'];
            $names2[]=$val['name'];
        }
        $names_text2=implode("','",$names2);
        $values_text2=implode(',',$values2);
        $this->assign("values2",$values_text2);
        $this->assign("names2",$names_text2);


    }

    public function GetYdzwrl($params)
    {
        $GUID = $params['GUID'];
        $signcs = $params['SignCS'];
        $RuType = $params['RuType'];
        $meetid = $params['ID'];
        if ($signcs != '' && $GUID != '') {
            //$snum = $this->_dao->getOne("SELECT count(distinct Cid) as c FROM meeting_member_ruchang WHERE RuType='" . $RuType . "' AND Mtid='" . $meetid . "'  AND Status='1' ", 0);
            $sql2 = "select compfnamepre,Howmuch,Comeindate from Account_pre_comein where Status=0 ";
             $accountpre = $this->_dao->query($sql2);
            $arr['Message'] = base64_encode_new('获取成功');;
            $arr['Success'] = '1';   
            $arr['Results'] =  $accountpre;  
        }
        else
        {
            $arr['Message'] = base64_encode_new('获取失败');;
            $arr['Success'] = '0';   
            $arr['Results'] =  array();  
        }
        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }
    public function bdlogs($params)
    {
       $jfsx=array(
        "0"=>"A",
        "1"=>"B",
        "2"=>"C",
        "3"=>"D");
        $GUID = $params['GUID'];
        $signcs = $params['SignCS'];
        $meetid = $params['Mtid']?$params['Mtid']:407;
        $meeting_base = $this->_dao->getRow("select * from meeting_base where ID  = '$meetid' limit 1");
        $where1=""; 
        if($meeting_base['bddeadline']!="0000-00-00 00:00:00")
        {
           $where1 = " AND ( mal.createtime > '".$meeting_base['bddeadline']."') ";
        }
        $meetingmanagement = $this->_dao->query( "SELECT mmc.ID,mmc.PersonSign,mmc.TrueName,mmc.Post,CompfnameS,ContactPhone,ContactMobile,IsContacts,IsGetMoney,mmc.Status,mm.DepartID,mm.UserName,mal.content,mal.createuser,mal.createtime FROM steelhome.meeting_member_contactman as mmc , steelhome.meeting_member as mm,meeting_active_log as mal where  mmc.Mtid=mm.Mtid and  ((mm.MidEn=0 AND mmc.Mid = mm.Mid) or (mm.MidEn>0 AND mm.MidEn=mmc.Mid)) and mal.hwid=mmc.ID and mal.mtid = '".$meetid."' and mal.type='10' ".$where1." AND mmc.Mtid = '".$meetid."'");
        $allxx=array();
        $allxxbd=array();

        $bdxxarr=array();


        $bdarr=array(
            'CompfnameS'=>'公司名称',
            'TrueName'=>'真实姓名',
            'Post'=>'职务',
            '预订酒店'=>'酒店',
            'IsGetMoney'=>'缴费属性'
        );
        $xzshuju=array();
        $qxshuju=array();
        $xgshuju=array();
        foreach($meetingmanagement as $k=>$v)
        {
            foreach($bdarr as $k1=>$v1)
            {
                    if(str_replace($v1,"",$v['content'])!=$v['content'])
                    {
                        $allxxbd[$v['ID']][$k1]=1;
                    }
            }
            if(!isset($xgshuju[$v['ID']]))
            {
                $xgshuju[$v['ID']]=$v;
            }
        }

        $this->assign( "xgshuju", $xgshuju );
        $this->assign( "allxxbd", $allxxbd );
        $this->assign( "jfsx", $jfsx );
         
    }
    public function GetBDChangeMessageList($params)
    {
        $GUID = $params['GUID'];
        $signcs = $params['SignCS'];
        $meetid = $params['Mtid']?$params['Mtid']:407;
        //$meetid =407;
        $where1 = " AND (createtime > '".date("Y-m-d H:i:s", time()-(10*60))."') ";
        
        $meetingmanagement = $this->_dao->query( "SELECT * FROM meeting_active_log where   mtid = '".$meetid."' and type='10' ".$where1."  order by createtime desc limit 20");
        $newsInfoList=array();
        foreach($meetingmanagement as $k=>$v)
        {
            $info=array();
            $info['id']=$v['id'];
            $info['ntitle']=$v['title'];
            $info['ndate']=$v['createtime'];
            $newsInfoList[]=$info;
        }
        $arr['Success'] = 1;
        $arr['Message'] = "获取成功";
        $arr['newsInfoList'] = $newsInfoList;
        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    


}

?>