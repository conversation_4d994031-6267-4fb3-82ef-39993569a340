<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );

class DclwRdjController extends AbstractController{
  
  public function __construct(){
    parent::__construct();
	$this->_action->setDao( new DclwRdjDao("DRCW") );
	$this->_action->t1Dao=new DclwRdjDao("MAIN");
	$this->_action->homeDao=new DclwRdjDao("91R");
	$this->_action->gcDao=new DclwRdjDao("GC");
  }

	public function v_index(){
		$this->_action->index( $this->_request );
	}
	public function do_savedata(){
		$this->_action->savedata( $this->_request ); //±£´æÎÄ±¾
	}
}
?>