<?php

class memberlogAction extends AbstractAction
{
    public function __construct()
    {
        parent::__construct();
    }

    public function index($params)
    {
        $GUID = $params['GUID'];
		$SignCS = $params["SignCS"];
        $mode = $params['mode'];
        $mc_type = $params['mc_type'];
        $date=$params["date"];
        $username=$params["username"];
		$uid=$params["uid"];
		$sdate=$params["sdate"];

		$where = $mc_type=="" ? " app_logs.mc_type='1' " : " app_logs.mc_type='$mc_type' ";
        //$where .= $date=="" ? "" : " and ActionDate>='$date 00:00:00' and ActionDate<='$date 23:59:59' ";
		$where .= $date=="" ? "" : " and ActionDate<='$date 23:59:59' ";
		$where .= $sdate=="" ? "" : " and ActionDate>='$sdate 00:00:00' ";

		if($username != "")
		{
			$uid = $this->t1Dao->get_uid($username,$mc_type);
			$where .= empty($uid) ? "" : " And app_logs.Uid in (".implode(',',$uid).")" ;
		}
		else
			$where .= empty($uid) ? "" : " And app_logs.Uid='".$uid."'" ;

		$where .= " And MessageTitle!=''" ;
        
        $page=$this->formatpage($params["page"]);
		$pagenum="18";//一页显示的条数
        $limit=" limit ".(($page-1)*$pagenum).",".$pagenum;
		//echo '<pre/>';print_r($this->t1Dao);exit;
        $amount = $this->t1Dao->get_log_count($where);
       
		$data = $this->t1Dao->get_log_list($where,$limit);
		
        //$pagelabel=$this->getpagelabelnew($amount,$pagenum,$page,"memberlog.php?view=".$params['view']."&GUID=".$GUID."&SignCS=".$SignCS."&mc_type=".$mc_type."&mode=".$mode."&date=".$date."&username=".$username."&uid=".$uid);
        $pagelabel=$this->getpagelabelnew($amount,$pagenum,$page,"memberlog.php?view=".$params['view']."&GUID=".$GUID."&SignCS=".$SignCS."&mc_type=".$mc_type."&mode=".$mode."&date=".$date."&sdate=".$sdate."&uid=".$uid);

        $this->assign("mode",$mode);
        $this->assign("mc_type",$mc_type);
        $this->assign("view",$params['view']);
        $this->assign("data",$data);
        $this->assign("params",$params);
        $this->assign("pagelabel",$pagelabel);
    }

	public function loglist($params)
    {
        $GUID = $params['GUID'];
		$SignCS = $params["SignCS"];
		$mc_type = $params['mc_type'];

		$uprivilege = $this->t1Dao->get_privilege($GUID,$SignCS,$mc_type);
		//echo '<pre/>';print_r($uprivilege);
		if(strpos($uprivilege, '3') !== false)
		{
			$mode = $params['mode'];
			$sdate=$params["startdate"] == "" ? date('Y-m-d', time()) : $params["startdate"];
			$edate=$params["enddate"] == "" ? date('Y-m-d', time()) : $params["enddate"];

			$mid = $this->t1Dao->get_mid($GUID,$SignCS,$mc_type);

			$where = $mc_type=="" ? " mc_type='1' " : " mc_type='$mc_type' ";
			$where .= $sdate=="" ? "" : " and LoginDate>='$sdate 00:00:00' ";
			$where .= $edate=="" ? "" : " and LoginDate<='$edate 23:59:59' ";
			$where .= " and mid='$mid' ";

			$page=$this->formatpage($params["page"]);
			$pagenum="18";//一页显示的条数
			$limit=" limit ".(($page-1)*$pagenum).",".$pagenum;
			//echo '<pre/>';print_r($this->t1Dao);exit;
			$amount = $this->t1Dao->get_loglist_count($where);
		   
			$data = $this->t1Dao->get_loglist_list($where,$limit);
			
			$pagelabel=$this->getpagelabelnew($amount,$pagenum,$page,"memberlog.php?view=".$params['view']."&GUID=".$GUID."&SignCS=".$SignCS."&mc_type=".$mc_type."&mode=".$mode."&startdate=".$sdate."&enddate=".$edate);
			
			$params["startdate"] = $sdate;$params["enddate"] = $edate;
			$this->assign("mode",$mode);
			$this->assign("mc_type",$mc_type);
			$this->assign("view",$params['view']);
			$this->assign("data",$data);
			$this->assign("params",$params);
			$this->assign("pagelabel",$pagelabel);
		}
		else
		{
			echo "<div align='center' style='font-size: 20px;'><b>您没有权限查看此页面！</b></div>";exit;
		}
	}

    public function detail($params){
		//echo '<pre/>';print_r($params);
		$mode = $params['mode'];
        $mc_type = $params['mc_type'];
        $date = $params["date"];
        $username = $params["username"];
		$uid = $params["uid"];
		
		$where = $mc_type=="" ? " app_member_logs.mc_type='1' " : " app_member_logs.mc_type='$mc_type' ";
        $where .= $date=="" ? "" : " and OpDate>='$date 00:00:00' and OpDate<='$date 23:59:59' ";

		if($username != "")
		{
			$userdata = $this->t1Dao->get_udata($username,$mc_type);
			$where .= empty($userdata) ? "" : "And app_member_logs.GUID='".$userdata['GUID']."' and app_member_logs.SignCS='".$userdata['SignCS']."'" ;
		}
		else
		{
			$userdata = $this->t1Dao->get_uid_udata($uid,$mc_type,$date);
			$where .= empty($userdata) ? "" : "And app_member_logs.GUID='".$userdata['GUID']."' and app_member_logs.SignCS='".$userdata['SignCS']."'" ;
		}
		
        $page=$this->formatpage($params["page"]);
		$pagenum="18";//一页显示的条数
        $limit=" limit ".(($page-1)*$pagenum).",".$pagenum;
		
        $amount = $this->t1Dao->get_logdetail_count($where);
       
		$data = $this->t1Dao->get_logdetail_list($where,$limit);
		
        $pagelabel=$this->getpagelabelnew($amount,$pagenum,$page,"memberlog.php?view=".$params['view']."&mc_type=".$mc_type."&mode=".$mode."&date=".$date."&username=".$username."&uid=".$uid);
		//echo '<pre/>';print_r($data);
		
        $this->assign("mode",$mode);
        $this->assign("mc_type",$mc_type);
        $this->assign("data",$data);
        $this->assign("params",$params);
        $this->assign("pagelabel",$pagelabel);
        
    }

    private function formatpage($page){
        $page=(int)$page;
        if($page-1<0) $page=1;
        return $page;
    }

    private function getpagelabelnew($amount, $pagenum, $page, $url)
    {
        $pagemax = ($amount % $pagenum == 0) ? round($amount / $pagenum, 0) : (floor($amount / $pagenum) + 1);

        //echo $amount." ".$pagenum." ".$page." ".$pagemax." ".$url;
        $label = "<div class='flex justify-between flex-1 sm:hidden'>";
        if ($page == 1 || $pagemax == 0) {
            $label .= "<span class='relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default leading-5 rounded-md'>
                    
			上一页
		</span>";
        } else {
            $label .= "<a href='$url&page=" . ($page - 1) . "' class='relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 leading-5 rounded-md hover:text-gray-500 focus:outline-none focus:shadow-outline-blue focus:border-blue-300 active:bg-gray-100 active:text-gray-700 transition ease-in-out duration-150'>

			上一页
			</a>";
        }

        if ($page == $pagemax || $pagemax == 0) {
            $label .= "<span class='relative inline-flex items-center px-4 py-2 ml-3 text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default leading-5 rounded-md'>下一页</span>";
        } else {
            $label .= "<a  href='$url&page=" . ($page + 1) . "'class='relative inline-flex items-center px-4 py-2 ml-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 leading-5 rounded-md hover:text-gray-500 focus:outline-none focus:shadow-outline-blue focus:border-blue-300 active:bg-gray-100 active:text-gray-700 transition ease-in-out duration-150'>

			下一页
			</a>";
        }
        $label .= "</div>";
        $ye = 10;
        $label .= "<div class='hidden sm:flex-1 sm:flex sm:items-center sm:justify-between'>
		<div>
		<p class='text-sm text-gray-700 leading-5'>
		Showing
		<span class='font-medium'>16</span>
		to
		<span class='font-medium'>30</span>
		of
		<span class='font-medium'>41</span>
		results
		</p>
		</div>";

        if ($page == 1 || $pagemax == 0) {//第一页
            $label .= "<div><span class='relative z-0 inline-flex shadow-sm rounded-md'><span aria-disabled='true' aria-label='&amp;laquo; Previous'>
				<span class='relative inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default rounded-l-md leading-5' aria-hidden='true'>
					<svg class='w-5 h-5' fill='currentColor' viewBox='0 0 20 20'>
						<path fill-rule='evenodd' d='M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z' clip-rule='evenodd'></path>
					</svg>
				</span>
			</span>";
        } else {
            $label .= "<div><span class='relative z-0 inline-flex shadow-sm rounded-md'><a href='$url&page=" . ($page - 1) . "' rel='prev' class='relative inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-l-md leading-5 hover:text-gray-400 focus:z-10 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue active:bg-gray-100 active:text-gray-500 transition ease-in-out duration-150' aria-label='&amp;laquo; Previous' ><svg class='w-5 h-5' fill='currentColor' viewBox='0 0 20 20'>
			<path fill-rule='evenodd' d='M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z' clip-rule='evenodd' />
		</svg></a>";
        }
        if ($pagemax == 0) {
            $label .= "<span aria-current='page'><span class='relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default leading-5'>1</span></span>";
        } else {
            if ($page == 1) {
                $label .= "<span aria-current='page'><span class='relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default leading-5'>1</span></span>";
            } else {
                $label .= "<a href='$url&page=1' class='relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-gray-700 bg-white border border-gray-300 leading-5 hover:text-gray-500 focus:z-10 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue active:bg-gray-100 active:text-gray-700 transition ease-in-out duration-150' aria-label='Go to page 1'>1</a>";
            }
            $label1 = "<span class='relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-gray-700 bg-white border border-gray-300 leading-5 hover:text-gray-500 focus:z-10 focus:outline-none focus:border-blue-300  transition ease-in-out duration-150'>...</span>";

            for ($i = 2; $i < $pagemax; $i++) {
                $label2 = "<span aria-current='page'><span class='relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default leading-5'>$i</span></span>";
                $label3 = "<a href='$url&page=$i' class='relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-gray-700 bg-white border border-gray-300 leading-5 hover:text-gray-500 focus:z-10 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue active:bg-gray-100 active:text-gray-700 transition ease-in-out duration-150' aria-label='Go to page $i'>$i</a>";

                if ($page < $ye - 1) {//前10，
                    if ($i > $ye) continue;
                    if ($i == $ye) {
                        $label .= $label1;
                    } elseif ($i == $page) {
                        $label .= $label2;
                    } else {
                        $label .= $label3;
                    }
                } else if ($pagemax - $page < $ye - 1) {//后10
                    if ($i < $pagemax - $ye) continue;
                    if ($i == $pagemax - $ye) {
                        $label .= $label1;
                    } elseif ($i == $page) {
                        $label .= $label2;
                    } else {
                        $label .= $label3;
                    }
                } else {//中间数
                    if ($i < $page - 2 || $i > $page + $ye - 2) continue;
                    if ($i == $page - 2 || $i == $page + $ye - 2) {
                        $label .= $label1;
                    } elseif ($i == $page) {
                        $label .= $label2;
                    } else {
                        $label .= $label3;
                    }
                }

            }
        }

        if ($pagemax > 1) {
            if ($pagemax != $page)
                $label .= "<a href='$url&page=$pagemax'  class='relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-gray-700 bg-white border border-gray-300 leading-5 hover:text-gray-500 focus:z-10 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue active:bg-gray-100 active:text-gray-700 transition ease-in-out duration-150' aria-label='Go to page $pagemax'>$pagemax</a>"; //最后一页
            else
                $label .= "<span aria-current='page'><span class='relative inline-flex items-center px-4 py-2 -ml-px text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default leading-5'>$page</span></span>";
        }
        if ($page == $pagemax || $pagemax == 0) {
            $label .= "<span aria-disabled='true' aria-label='Next &amp;raquo;'>
				<span class='relative inline-flex items-center px-2 py-2 -ml-px text-sm font-medium text-gray-500 bg-white border border-gray-300 cursor-default rounded-r-md leading-5' aria-hidden='true'>
					<svg class='w-5 h-5' fill='currentColor' viewBox='0 0 20 20'>
						<path fill-rule='evenodd' d='M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z' clip-rule='evenodd' />
					</svg>
				</span>
			</span>";
        } else {
            $label .= "<a href='" . $url . "&page=" . ($page + 1) . "' rel='next' class='relative inline-flex items-center px-2 py-2 -ml-px text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-r-md leading-5 hover:text-gray-400 focus:z-10 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue active:bg-gray-100 active:text-gray-500 transition ease-in-out duration-150' aria-label='Next &amp;raquo;'>
				<svg class='w-5 h-5' fill='currentColor' viewBox='0 0 20 20'>
					<path fill-rule='evenodd' d='M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z' clip-rule='evenodd'></path>
				</svg></a>";
        }

        $label .= "</span></div>";

        return "<div align='right' style='margin-top:5px; font-size:12px'> <nav role='navigation' aria-label='Pagination Navigation' class='flex items-center justify-between'>" . $label . "</nav></div>";
    }

}

?>