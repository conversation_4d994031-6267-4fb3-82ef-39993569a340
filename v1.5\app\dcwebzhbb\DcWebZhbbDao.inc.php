<?php
//require_once('../../steelconf_v3/debug.php');
class DcWebZhbbDao extends Dao{

  public function __construct( $writer ){
    parent::__construct( $writer );
  }

   //取得GUID
  public function getGUID( $delminus = true ){
      $sGuid = $this->getOne( "SELECT uuid() AS guid" );
      if ($delminus){
         $sGuid =  str_replace("-", "", $sGuid);
      }
      return  $sGuid;
  }


  //根据用户名与设备标识，获取app_session
  public function getAppSession( $UserName, $SignCS ,$mc_type){
    //首先从缓存表取数据
    $row = $this->getRow( "SELECT * FROM app_session_temp WHERE UserName = '$UserName' AND SignCS = '$SignCS' AND mc_type='$mc_type'" );
    if (empty( $row )){
      //缓存表没有数据，则尝试从实体表取数据
      $row = $this->getRow( "SELECT * FROM app_session WHERE UserName = '$UserName' AND SignCS = '$SignCS' AND mc_type='$mc_type'" );
    }
    return $row;

  }
  //取得已登陆用户
  public function getUser( $GUID, $SignCS ,$mc_type ){
      return $this->getRow( "SELECT * FROM app_session_temp WHERE GUID = '$GUID' AND mc_type='$mc_type'" );
  }

  //日志记录
  public function WriteLog($Mid, $Uid, $SignCS, $ActionName,$Actionstr, $ActionIp, $SystemType, $SystemVersion, $MessageTitle='', $MessageId='',$MessageDesc='' ,$mc_type=0){
     $this->execute( "INSERT INTO app_logs SET Mid='$Mid', Uid='$Uid', SignCS='$SignCS', ActionName='$ActionName',Actionstr='$Actionstr', ActionDate=NOW(),ActionIp='$ActionIp', SystemType='$SystemType', SystemVersion='$SystemVersion',MessageId='$MessageId', MessageTitle='$MessageTitle', MessageDesc='$MessageDesc' , mc_type='$mc_type'");
  }
  
  
  public function insertNgGZJ_Zhbb($Type ,$Title ,$Detail ,$CDate,$mc_type )
  {
	   $this->execute( "INSERT INTO  `NgGZJ_Zhbb` (`Type` ,`Title` ,`Detail` ,`CDate` ,`CreateTime` ,`UpdateTime` ,`updateadminid` ,`mc_type` ,`adminid`)
		VALUES ('$Type',  '$Title',  '$Detail',    '$CDate',  NOW(),  NOW(),  '-1',  '$mc_type',  '-1')");
  }
}

?>