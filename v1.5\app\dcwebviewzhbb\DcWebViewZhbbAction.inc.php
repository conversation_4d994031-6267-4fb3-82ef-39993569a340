<?php 
// require_once('../../../../steelconf_v3/debug.php');
//require_once ("/etc/steelconf/config/isholiday.php");
class DcWebViewZhbbAction extends AbstractAction
{ 
    public function __construct()
    {
        parent::__construct();
    } 
	 public function zhbb($params)
    {	
	      
	      
		$today=date("Y-m-d");
		
		$time  = self::getMillisecond();
		if($params['curdate'])
		{
			if(checkdate(date("m",strtotime($params['curdate'])),date("d",strtotime($params['curdate'])),date("Y",strtotime($params['curdate']))))
			{
				$today=$params['curdate'];
			}
			
			
		}
		$type=$params['Type'];//1日报2周报
		$mode=$params['mode'];//PC: 1 Android手机:2 Android Pad: 3 Iphone: 4 Ipad: 5
		$GUID=$params['GUID'];
		
		$mc_type=$params['mc_type'];
		$SignCS=$params['SignCS'];
		
		//获取用户信息
		$user_infos = $this->t1dao->get_userinfo_byguid($GUID);
		$Mid=$user_infos['Mid'];//会员id
		$userid=$user_infos['Uid'];//用户id
		//$SignCS =$user_infos['SignCS'];//设备标识
		
		
		$truename =$user_infos['TrueName'];//真实姓名
		//获取用户权限和用户名app_license_privilege
		$license_detail_info = $this->t1dao->get_license_privilege($Mid);
	
		
		
		//查询最新
	//	$sql="select * from NgGZJ_Zhbb where CDate<='$today'  order by CDate desc";
	//	$infos = $this->ngdao->getrow($sql);
 

		$where = "1";
		
	
		if ($type!='') {
			$where = $where . " and Type='$type' ";
		}
		
		if ($today!='') {
			$where = $where . " and CDate<= '$today' ";
		}
		if ($mc_type!='') {
			$where = $where . " and mc_type='$mc_type' ";
		}
		// 最新
		$sql="select * from NgGZJ_Zhbb where " . $where."  order by CDate desc,id desc limit 1";
		$infos = $this->drcdao->getrow($sql);
		$infos['Detail']=html_entity_decode($infos['Detail'], ENT_QUOTES,"UTF-8");
		
	    $infos['Detail']=preg_replace('/font-size:.*px;/','',$infos['Detail']);	
		
		//前一条
		$where_up = "1";
		
		
		if ($type!='') {
			$where_up = $where_up . " and Type='$type' ";
		}
		
		if ($today!='') {
			$where_up = $where_up . " and CDate< '".$infos['CDate']."' ";
		}
		if ($mc_type!='') {
			$where_up = $where_up . " and mc_type='$mc_type' ";
		}
		$sql_up="select * from NgGZJ_Zhbb where " . $where_up."  order by CDate desc limit 1";
		 
		$infos_up = $this->drcdao->getrow($sql_up);
		//下一条
		$where_down = "1";
		
		
		if ($type!='') {
			$where_down = $where_down . " and Type='$type' ";
		}
		
		if ($today!='') {
			$where_down = $where_down . " and CDate> '".$infos['CDate']."' ";
		}
		if ($mc_type!='') {
			$where_down = $where_down . " and mc_type='$mc_type' ";
		}
		$sql_down="select * from NgGZJ_Zhbb where " . $where_down."  order by CDate asc limit 1";
		
		$infos_down = $this->drcdao->getrow($sql_down);
		
		
    	if($mode==1||$mode==5){
    		$head_url = "//".$_SERVER['SERVER_NAME']."/v1.5/css/zhbb_small.css";
    	}else if($mode==2||$mode==4){
    		$head_url = "//".$_SERVER['SERVER_NAME']."/v1.5/css/zhbb_middle.css";
    	}else if($mode==3){
    		$head_url = "//".$_SERVER['SERVER_NAME']."/v1.5/css/zhbb_large.css";
    	}
		$this->assign("head_url",$head_url);
		$this->assign("time",$time);
		$this->assign("infos",$infos);
		$this->assign("infos_up",$infos_up);
		$this->assign("infos_down",$infos_down);
	  $this->assign("mode",$mode);
	  $this->assign("GUID",$GUID);
	  $this->assign("mc_type",$mc_type);
	  $this->assign("SignCS",$SignCS);
	  $this->assign("params",$params);
	 
	  $this->assign("type",$type);
    } 

    public function zhbblist($params){
   
    	$type=$params["Type"];
    	$mode=$params["mode"];//null、1:PC端 2:Android 手机 3:Android 平板 4:iphone 5:ipad
    	$mc_type=$params['mc_type'];
    	$title=$params['title'];
    	$SignCS=$params['SignCS'];
    	$page=$this->formatpage($params["page"]);
    	$pagenum="10";//一页显示的条数
    	$search="";
    	$startdate=$params["startdate"];
    	$enddate=$params["enddate"];
    	
    	$GUID=$params["GUID"];
    	$userid=$this->getuidbyGUID($GUID);
    	{
    		$select=" select count(1) from steelhome_drc.NgGZJ_Zhbb  ";
    		$where=" where Type='$type' and mc_type='$mc_type' ";
    		$search.=$title==""?" ":" and Title like '%$title%' ";
    		
	    	if($startdate==''&&$enddate==''){
	    		//$today=date("Y-m-d");
	    		//$search.=" and CDate<='$today' ";
	    	}else{
	    	
	    		$search.=$startdate==""?" ":" and CDate>='$startdate' ";
	    		$search.=$enddate==""?" ":" and CDate<='$enddate' ";
	    	}
	    		$orderby=" order by CDate desc ";
	    		$limit=" limit ".(($page-1)*$pagenum).",".$pagenum;
	    	}
	    	$amount=$this->ngdao->getone($select.$where.$search); //echo $amount;
	    	
	    	$select=" select * from steelhome_drc.NgGZJ_Zhbb ";
	    	$sql=$select.$where.$search.$orderby.$limit;
	    
	    	$arr2=$this->ngdao->query($sql);
	    	
    	/*foreach($arr as &$ar){
    	 foreach($ar as &$a){
    	$a=substr(htmlspecialchars($a),0,30);
    	}
    	}*/
    	foreach($arr2 as $k=>$v){
    		$arr[$k+($page-1)*$pagenum+1]=$v;
    	}
    	
    	
    	
    	$pagelabel=$this->getpagelabel($amount,$pagenum,$page,"dcwebviewzhbb.php?view=list&Type=".$type."&GUID=".$GUID."&mode=".$mode."&title=".$title."&startdate=".$startdate."&enddate=".$enddate."&mc_type=".$params['mc_type']);
    	 
    	
    	$this->assign("SignCS",$SignCS);
    	$this->assign("type",$type);
    	$this->assign("title",$title);
    	$this->assign("mc_type",$mc_type);
    	$this->assign("arr",$arr);
    	$this->assign("page",$page);
    	$this->assign("mode",$mode);
    	$this->assign("GUID",$GUID);
    	$this->assign("pagenum",$pagenum);
    	$this->assign("pagelabel",$pagelabel);
    }
    

 public function getMillisecond() {//获取毫秒级时间戳
    list($usec, $sec) = explode(" ", microtime());
    $usec = str_replace('.', '', $usec);
    $ctime=substr($sec.$usec,0,13);
    return $ctime;


}
    public function onezhbb(){

	$time  = self::getMillisecond();
    	
		
		
    	$mode=$_GET["mode"];//null、1:PC端 2:Android 手机 3:Android 平板 4:iphone 5:ipad
    	$SignCS=$_GET['SignCS'];
    	$GUID=$_GET['GUID'];
    	
    	$id=$_GET['id'];
    	$sql="select * from steelhome_drc.NgGZJ_Zhbb  where id='$id'";
    	$infos=$this->ngdao->getrow($sql);
   
    	$infos['Detail']=html_entity_decode($infos['Detail'], ENT_QUOTES,"UTF-8");
    	$infos['Detail']=preg_replace('/font-size:.*px;/','',$infos['Detail']);	
    	//$infos['Detail']=	preg_replace('/font-size:\w+;?/g','',$infos['Detail']);
    	
    	if($mode==1||$mode==5){
    		$head_url = "//".$_SERVER['SERVER_NAME']."/data/v1.5/css/zhbb_small.css";
    	}else if($mode==2||$mode==4){
    		$head_url = "//".$_SERVER['SERVER_NAME']."/v1.5/css/zhbb_middle.css";
    	}else if($mode==3||$mode==5){
    		$head_url = "//".$_SERVER['SERVER_NAME']."/v1.5/css/zhbb_large.css";
    	}
    	switch($mode){
    		case 1:$fontsize=20;break;
    		case 2:
    		case 4:$fontsize=34;break;
    		case 3:
    		case 5:$fontsize=26;break;
    		default:$fontsize=20;break;
    	}
    	$this->assign("fontsize",$fontsize."px");
    	$this->assign("head_url",$head_url);
    	$this->assign("time",$time);
    	
    	$this->assign("mode",$mode);
    	$this->assign("GUID",$GUID);
    
    	$this->assign("SignCS",$SignCS);
    	$this->assign("infos",$infos);
    }
    
    //导出
    public function doexportdata(){
    	
    	$id=$_GET["id"];
    	$type=$_GET["Type"];
    	$mc_type=$_GET["mc_type"];
    	$SignCS=$_GET['SignCS'];
    	$mode=$_GET['mode'];
    	$GUID=$_GET["GUID"];
    	//$id=845;
    	$sql="select * from steelhome_drc.NgGZJ_Zhbb where id='$id'";
    	$data=$this->ngdao->getRow($sql);
    	$conect=$data["Detail"];
    	if($conect=="") {echo"<script>window.location='dcwebviewzhbb.php?view=list&SignCS=$SignCS&Type=$type&mode=$mode&mc_type=$mc_type&GUID=$GUID'</script>";exit;}
    	$conect=html_entity_decode($conect, ENT_QUOTES,"UTF-8");
    	$title=html_entity_decode($data["Title"], ENT_QUOTES,"UTF-8");
  
    	$con="<!DOCTYPE html>
				<html>
				
					<head>
						<meta http-equiv='Content-Type' content='text/html; charset=utf8'>
						<link href='//".$_SERVER['SERVER_NAME']."/v1.5/css/zhbb_small.css' rel='stylesheet' type='text/css'>
						<title>市场报告</title>
					</head>
				
					<body >				
							<div class='big_from'>
							      <h4>".$data["Title"]."</h4>
					
							   ".$conect."
							  
					
						
						</div>
						
					</body>
				
				
				
				</html>";
    //	$con=file_get_contents(path);
    	$name=$data["Title"];
    	ob_start(); //打开缓冲区
    	header("Cache-Control: public");
    	Header("Content-type: application/octet-stream");
    	Header("Accept-Ranges: bytes");
    
    	if (strpos($_SERVER["HTTP_USER_AGENT"],'MSIE')) {
    		header('Content-Disposition: attachment; filename='.$name.'.doc');
    	}else if (strpos($_SERVER["HTTP_USER_AGENT"],'Firefox')) {
    		header('Content-Disposition: attachment; filename='.$name.'.doc');
    	} else {
    		header('Content-Disposition: attachment; filename='.$name.'.doc');
    	}
    	header("Pragma:no-cache");
    	header("Expires:0");
    	echo $con;
    	ob_end_flush();
    	//$_SESSION["gogo"]="download";
    	exit;
    }
    
    
    
    function getuidbyGUID($GUID){
    	$uid="";
    	$sql="select Uid from steelhome_t1.app_session_temp where GUID='$GUID'";
    	$uid=$this->t1dao->getone($sql);
    	return $uid;
    } 
    
    private function formatpage($page){ 
    	$page=(int)$page;
    	if($page-1<0) $page=1;
    	return $page;
    }
    
    private function getpagelabel($amount,$pagenum,$page,$url){
    	$pagemax=($amount%$pagenum==0)?round($amount/$pagenum,0):(floor($amount/$pagenum)+1);
    	//echo $amount." ".$pagenum." ".$page." ".$pagemax." ".$url;
    	$label="";
    	$ye=3;		//最多隔页显示的可点击页数
    	if($pagemax==0) return "<div class='page tc oh mt20'><span class='disabled'>上一页</span> <span class='me'> 1 </span> <span class='disabled'>下一页</span></div>";			//如果没有数据，则直接返空
    	if($page==1) $label="<span class='disabled'>上一页</span><span class='me'>1</span>"; //第一页
    	else $label="<a href='$url&page=".($page-1)."' class='sxy'>上一页</a><a href='$url&page=1'>1</a>";
    	for($i=2;$i<$pagemax;$i++){
    		if($i-$page>$ye||$page-$i>$ye) continue;
    		elseif($i-$page==$ye||$page-$i==$ye){
    			$label.="...";
    		}elseif($i==$page){
    			$label.="<span class='me'>$i</span>";
    		}else{
    			$label.="<a href='$url&page=$i'>$i</a>";
    		}
    	}
    	if($pagemax>1) {
    		if($pagemax!=$page)
    			$label.="<a href='$url&page=$pagemax'>$pagemax</a>"; //最后一页
    		else
    			$label.="<span class='me'>$page</span>";
    	}
    	if($page==$pagemax) $label.="<span class='disabled'>下一页</span>";
    	else $label.="<a href='".$url."&page=".($page+1)."' class='sxy'>下一页</a>";
    	return "<div class='page tc oh mt20'>".$label."</div>";
    }
    
} 

?>