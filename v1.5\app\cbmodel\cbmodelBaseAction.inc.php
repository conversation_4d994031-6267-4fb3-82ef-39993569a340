<?php 

include_once( FRAME_LIB_DIR ."/action/AbstractAction.inc.php" );
class  cbmodelBaseAction extends AbstractAction
{
    public function __construct()
    {
        parent::__construct();
    } 
	
	//七点定时计算成本模型的数据
	/*
		type 
			1:江苏长江沿线钢厂转炉螺纹钢
			2:唐山地区转炉螺纹钢
			3：江苏长江沿线电炉（铁水+废钢）螺纹钢成本
			4：唐山地区电炉（铁水+废钢）螺纹钢
			5:江苏长江沿线钢厂普板成本模型
			6:唐山钢厂普板成本模型
			7：江苏长江沿线钢厂低合金板成本模型
			8：唐山钢厂低合金板成本模型
	*/
	public function calc_cbmx($params) {//根据id，指定日期获取价格
		//$GUID = $params['GUID'];
		//$GUID = "6615bc4ac53d11e7b891001aa00de1ab";
		$Type = $params['Type'];//要执行的类型，
		$Date = $params['Date'];//要生成的时间
		$tangshan_or_jiangsu = $params['tangshan_or_jiangsu'];//要生成的时间
		if(empty($Date)){
			$Date = $this->get_last_workday(date("Y-m-d"));
		}
		//获取用户信息
		/**
		$user_infos = $this->t1dao->get_userinfo_byguid($GUID);
		$Mid=$user_infos['Mid'];//会员id
		$userid=$user_infos['Uid'];//用户id
		**/
		//>环渤海】四大行大额承兑汇票贴现率
		$ChengDuiHuiPiaoTieXianLvinfo = $this->_dao->get_drc_hbh_txl($Date);
		$ChengDuiHuiPiaoTieXianLv = $ChengDuiHuiPiaoTieXianLvinfo['rd3'];
		$priceid_6 = "259422,418910,072023,428910,672023,598710,073012,448710,673012,438810,073022,673022,126015,122244,676011,672243";
		$priceid_7 = "1182141,6782101";
		$price_arr_6 = $this->maindao->get_marketconditions_price_6($priceid_6,$Date);
		$price_arr_7 = $this->maindao->get_marketconditions_price_7($priceid_7,$Date);
		echo "承兑汇票贴现率:::".$ChengDuiHuiPiaoTieXianLv."<br>";

		//1:江苏长江沿线钢厂转炉螺纹钢
		if($Type==1 || $Type=="" || $tangshan_or_jiangsu==1){
			//echo "<pre/>";print_r($this->maindao);
			$ng_ChengBenParameter3_1 = $this->maindao->get_ng_ChengBenParameter3($Date,1);
			$ng_TieHeJinChengBenIndex_1 = $this->maindao->get_ng_TieHeJinChengBenIndex($Date,101);

			//A参数
			$gtlxh_A = ($ng_TieHeJinChengBenIndex_1['indexValue'] * $ng_ChengBenParameter3_1['TieShuiXiaoHaoA'] + $price_arr_7['1182141'] * $ng_ChengBenParameter3_1['FeiGangTieXiaoHaoA']) * $ng_ChengBenParameter3_1['GangTieLiaoXiaoHaoA'];
			//B参数
			$hjxh_B = ($price_arr_6['259422'] * (1 - $ChengDuiHuiPiaoTieXianLv * 3 / 1000) +  $ng_ChengBenParameter3_1['YunFei1B']) * $ng_ChengBenParameter3_1['DunGangXiaoHao1B'] / 1000 + ($price_arr_6['418910'] * (1 - $ChengDuiHuiPiaoTieXianLv * 3 / 1000) + $ng_ChengBenParameter3_1['YunFei2B']) * $ng_ChengBenParameter3_1['DunGangXiaoHao2B'] / 1000 + $ng_ChengBenParameter3_1['QiTaHeJinB'];
			
			//转炉螺纹钢方坯成本
			$zllwgfpcb = $gtlxh_A + $hjxh_B + ( $ng_ChengBenParameter3_1['NengYuanDongLi'] + $ng_ChengBenParameter3_1['FuLiao'] + $ng_ChengBenParameter3_1['RenGong'] + $ng_ChengBenParameter3_1['ZhiZhaoFeiYong'] ) * ( 1 + $ng_ChengBenParameter3_1['ZengZhiShuiLv']) ;
			//转炉螺纹钢成本
			$zllwgcb = $zllwgfpcb + $ng_ChengBenParameter3_1['LuoWenGangJiaGongFei'] * ( 1 + $ng_ChengBenParameter3_1['ZengZhiShuiLv']);
			//转炉螺纹钢毛利
			$zllwgml = $price_arr_6['072023'] - $zllwgcb;
			//转炉螺纹钢利润
			if($zllwgml<=0){
				$zllwg_ly = 0;
			}else{
				$zllwg_ly = $zllwgml * $ng_ChengBenParameter3_1['ZengZhiShuiLv'] * $ng_ChengBenParameter3_1['GeLeiShuiFeiJiFuJia'];
			}
			$zllwgly = $zllwgml - $ng_ChengBenParameter3_1['QiJianFeiYong'] * ( 1 + $ng_ChengBenParameter3_1['ZengZhiShuiLv']) - $zllwg_ly;
			//转炉螺纹钢净利润
			if($zllwgly<=0){
				$zllwg_jly = 0;
			}else{
				$zllwg_jly = $zllwgly * $ng_ChengBenParameter3_1['SuoDeShuiLv'];
			}
			$zllwgjly = $zllwgly - $zllwg_jly;

			//查看数据库是否有当天数据
			$have_data = $this ->maindao->get_ng_TieHeJinChengBenIndex($Date,110);
			if($have_data['indexValue']){//有数据了
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,110,round($zllwgfpcb));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,111,round($zllwgcb));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,112,round($zllwgml));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,113,round($zllwgly));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,114,round($zllwgjly));
			}else{//没有,插入新数据
				$insert_value_1 = "('$Date','110','".round($zllwgfpcb)."'),('$Date','111','".round($zllwgcb)."'),('$Date','112','".round($zllwgml)."'),('$Date','113','".round($zllwgly)."'),('$Date','114','".round($zllwgjly)."');";
				$this ->maindao->insert_ng_TieHeJinChengBenIndex_value($insert_value_1);
				/*
				$this ->maindao->insert_ng_TieHeJinChengBenIndex($Date,110,$zllwgfpcb);
				$this ->maindao->insert_ng_TieHeJinChengBenIndex($Date,111,$zllwgcb);
				$this ->maindao->insert_ng_TieHeJinChengBenIndex($Date,112,$zllwgml);
				$this ->maindao->insert_ng_TieHeJinChengBenIndex($Date,113,$zllwgly);
				$this ->maindao->insert_ng_TieHeJinChengBenIndex($Date,114,$zllwgjly);
				*/
			}
			echo "江苏长江铁水成本:::".$ng_TieHeJinChengBenIndex_1['indexValue']."<br>";
			echo "A参数:::".$gtlxh_A."<br>";
			echo "B参数:::".$hjxh_B."<br>";
			echo "转炉螺纹钢方坯成本:::".$zllwgfpcb."<br>";
			echo "转炉螺纹钢成本:::".$zllwgcb."<br>";
			echo "转炉螺纹钢毛利:::".$zllwgml."<br>";
			echo "转炉螺纹钢利润:::".$zllwgly."<br>";
			echo "转炉螺纹钢净利润:::".$zllwgjly."<br>";
			echo "<pre/>";print_R($ng_ChengBenParameter3_1);print_R($price_arr_6);print_R($price_arr_7);
		}

		//2:唐山地区转炉螺纹钢
		if($Type==2 || $Type=="" || $tangshan_or_jiangsu==0){
			//echo "<pre/>";print_r($this->maindao);
			$ng_ChengBenParameter3_2 = $this->maindao->get_ng_ChengBenParameter3($Date,2);
			$ng_TieHeJinChengBenIndex_2 = $this->maindao->get_ng_TieHeJinChengBenIndex($Date,102);

			//A参数
			$gtlxh_A_2 = ($ng_TieHeJinChengBenIndex_2['indexValue'] * $ng_ChengBenParameter3_2['TieShuiXiaoHaoA'] + $price_arr_7['1182141'] * $ng_ChengBenParameter3_2['FeiGangTieXiaoHaoA']) * $ng_ChengBenParameter3_2['GangTieLiaoXiaoHaoA'];
			//B参数
			$hjxh_B_2 = ($price_arr_6['259422'] * (1 - $ChengDuiHuiPiaoTieXianLv * 3 / 1000) +  $ng_ChengBenParameter3_2['YunFei1B']) * $ng_ChengBenParameter3_2['DunGangXiaoHao1B'] / 1000 + ($price_arr_6['428910'] * (1 - $ChengDuiHuiPiaoTieXianLv * 3 / 1000) + $ng_ChengBenParameter3_2['YunFei2B']) * $ng_ChengBenParameter3_2['DunGangXiaoHao2B'] / 1000 + $ng_ChengBenParameter3_2['QiTaHeJinB'];
			
			//转炉螺纹钢方坯成本
			$zllwgfpcb_2 = $gtlxh_A_2 + $hjxh_B_2 + ( $ng_ChengBenParameter3_2['NengYuanDongLi'] + $ng_ChengBenParameter3_2['FuLiao'] + $ng_ChengBenParameter3_2['RenGong'] + $ng_ChengBenParameter3_2['ZhiZhaoFeiYong'] ) * ( 1 + $ng_ChengBenParameter3_2['ZengZhiShuiLv']) ;
			//转炉螺纹钢成本
			$zllwgcb_2 = $zllwgfpcb_2 + $ng_ChengBenParameter3_2['LuoWenGangJiaGongFei'] * ( 1 + $ng_ChengBenParameter3_2['ZengZhiShuiLv']);
			//转炉螺纹钢毛利
			$zllwgml_2 = $price_arr_6['672023'] - $zllwgcb_2;
			//转炉螺纹钢利润
			if($zllwgml_2<=0){
				$zllwg_ly_2 = 0;
			}else{
				$zllwg_ly_2 = $zllwgml_2 * $ng_ChengBenParameter3_2['ZengZhiShuiLv'] * $ng_ChengBenParameter3_2['GeLeiShuiFeiJiFuJia'];
			}
			$zllwgly_2 = $zllwgml_2 - $ng_ChengBenParameter3_2['QiJianFeiYong'] * ( 1 + $ng_ChengBenParameter3_2['ZengZhiShuiLv']) - $zllwg_ly_2;
			//转炉螺纹钢净利润
			if($zllwgly_2<=0){
				$zllwg_jly_2 = 0;
			}else{
				$zllwg_jly_2 = $zllwgly_2 * $ng_ChengBenParameter3_2['SuoDeShuiLv'];
			}
			$zllwgjly_2 = $zllwgly_2 - $zllwg_jly_2;

			//查看数据库是否有当天数据
			$have_data = $this ->maindao->get_ng_TieHeJinChengBenIndex($Date,115);
			if($have_data['indexValue']){//有数据了
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,115,round($zllwgfpcb_2));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,116,round($zllwgcb_2));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,117,round($zllwgml_2));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,118,round($zllwgly_2));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,119,round($zllwgjly_2));
			}else{//没有,插入新数据
				$insert_value_2 = "('$Date','115','".round($zllwgfpcb_2)."'),('$Date','116','".round($zllwgcb_2)."'),('$Date','117','".round($zllwgml_2)."'),('$Date','118','".round($zllwgly_2)."'),('$Date','119','".round($zllwgjly_2)."');";
				$this ->maindao->insert_ng_TieHeJinChengBenIndex_value($insert_value_2);
				/*
				$this ->maindao->insert_ng_TieHeJinChengBenIndex($Date,115,$zllwgfpcb_2);
				$this ->maindao->insert_ng_TieHeJinChengBenIndex($Date,116,$zllwgcb_2);
				$this ->maindao->insert_ng_TieHeJinChengBenIndex($Date,117,$zllwgml_2);
				$this ->maindao->insert_ng_TieHeJinChengBenIndex($Date,118,$zllwgly_2);
				$this ->maindao->insert_ng_TieHeJinChengBenIndex($Date,119,$zllwgjly_2);
				*/
			}

		}

		//3:江苏长江沿线电炉（铁水+废钢）螺纹钢成本
		if($Type==3 || $Type=="" || $tangshan_or_jiangsu==1){
			//echo "<pre/>";print_r($this->maindao);
			$ng_ChengBenParameter3_3 = $this->maindao->get_ng_ChengBenParameter3($Date,3);
			$ng_TieHeJinChengBenIndex_3 = $this->maindao->get_ng_TieHeJinChengBenIndex($Date,101);

			//A参数
			$gtlxh_A_3 = ($ng_TieHeJinChengBenIndex_3['indexValue'] * $ng_ChengBenParameter3_3['TieShuiXiaoHaoA'] + $price_arr_7['1182141'] * $ng_ChengBenParameter3_3['FeiGangTieXiaoHaoA']) * $ng_ChengBenParameter3_3['GangTieLiaoXiaoHaoA'];
			//B参数
			$hjxh_B_3 = ($price_arr_6['259422'] * (1 - $ChengDuiHuiPiaoTieXianLv * 3 / 1000) +  $ng_ChengBenParameter3_3['YunFei1B']) * $ng_ChengBenParameter3_3['DunGangXiaoHao1B'] / 1000 + ($price_arr_6['418910'] * (1 - $ChengDuiHuiPiaoTieXianLv * 3 / 1000) + $ng_ChengBenParameter3_3['YunFei2B']) * $ng_ChengBenParameter3_3['DunGangXiaoHao2B'] / 1000 + $ng_ChengBenParameter3_3['QiTaHeJinB'];
			
			//转炉螺纹钢方坯成本
			$zllwgfpcb_3 = $gtlxh_A_3 + $hjxh_B_3 + ( $ng_ChengBenParameter3_3['NengYuanDongLi'] + $ng_ChengBenParameter3_3['FuLiao'] + $ng_ChengBenParameter3_3['RenGong'] + $ng_ChengBenParameter3_3['ZhiZhaoFeiYong'] ) * ( 1 + $ng_ChengBenParameter3_3['ZengZhiShuiLv']) ;
			//转炉螺纹钢成本
			$zllwgcb_3 = $zllwgfpcb_3 + $ng_ChengBenParameter3_3['LuoWenGangJiaGongFei'] * ( 1 + $ng_ChengBenParameter3_3['ZengZhiShuiLv']);
			//转炉螺纹钢毛利
			$zllwgml_3 = $price_arr_6['072023'] - $zllwgcb_3;
			//转炉螺纹钢利润
			if($zllwgml_3<=0){
				$zllwg_ly_3 = 0;
			}else{
				$zllwg_ly_3 = $zllwgml_3 * $ng_ChengBenParameter3_3['ZengZhiShuiLv'] * $ng_ChengBenParameter3_3['GeLeiShuiFeiJiFuJia'];
			}
			$zllwgly_3 = $zllwgml_3 - $ng_ChengBenParameter3_3['QiJianFeiYong'] * ( 1 + $ng_ChengBenParameter3_3['ZengZhiShuiLv']) - $zllwg_ly_3;
			//转炉螺纹钢净利润
			if($zllwgly_3<=0){
				$zllwg_jly_3 = 0;
			}else{
				$zllwg_jly_3 = $zllwgly_3 * $ng_ChengBenParameter3_3['SuoDeShuiLv'];
			}
			$zllwgjly_3 = $zllwgly_3 - $zllwg_jly_3;

			//查看数据库是否有当天数据
			$have_data = $this ->maindao->get_ng_TieHeJinChengBenIndex($Date,120);
			if($have_data['indexValue']){//有数据了
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,120,round($zllwgfpcb_3));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,121,round($zllwgcb_3));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,122,round($zllwgml_3));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,123,round($zllwgly_3));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,124,round($zllwgjly_3));
			}else{//没有,插入新数据
				$insert_value_3 = "('$Date','120','".round($zllwgfpcb_3)."'),('$Date','121','".round($zllwgcb_3)."'),('$Date','122','".round($zllwgml_3)."'),('$Date','123','".round($zllwgly_3)."'),('$Date','124','".round($zllwgjly_3)."');";
				$this ->maindao->insert_ng_TieHeJinChengBenIndex_value($insert_value_3);
				/*
				$this ->maindao->insert_ng_TieHeJinChengBenIndex($Date,120,$zllwgfpcb_3);
				$this ->maindao->insert_ng_TieHeJinChengBenIndex($Date,121,$zllwgcb_3);
				$this ->maindao->insert_ng_TieHeJinChengBenIndex($Date,122,$zllwgml_3);
				$this ->maindao->insert_ng_TieHeJinChengBenIndex($Date,123,$zllwgly_3);
				$this ->maindao->insert_ng_TieHeJinChengBenIndex($Date,124,$zllwgjly_3);
				*/
			}
			echo "江苏长江铁水成本:::".$ng_TieHeJinChengBenIndex_3['indexValue']."<br>";
			echo "A参数:::".$gtlxh_A_3."<br>";
			echo "B参数:::".$hjxh_B_3."<br>";
			echo "转炉螺纹钢方坯成本:::".$zllwgfpcb_3."<br>";
			echo "转炉螺纹钢成本:::".$zllwgcb_3."<br>";
			echo "转炉螺纹钢毛利:::".$zllwgml_3."<br>";
			echo "转炉螺纹钢利润:::".$zllwgly_3."<br>";
			echo "转炉螺纹钢净利润:::".$zllwgjly_3."<br>";
			echo "<pre/>";print_R($ng_ChengBenParameter3_3);print_R($price_arr_6);print_R($price_arr_7);
		}
		
		//4:唐山地区电炉（铁水+废钢）螺纹钢
		if($Type==4 || $Type=="" || $tangshan_or_jiangsu==0){
			//echo "<pre/>";print_r($this->maindao);
			$ng_ChengBenParameter3_4 = $this->maindao->get_ng_ChengBenParameter3($Date,4);
			$ng_TieHeJinChengBenIndex_4 = $this->maindao->get_ng_TieHeJinChengBenIndex($Date,102);

			//A参数
			$gtlxh_A_4 = ($ng_TieHeJinChengBenIndex_4['indexValue'] * $ng_ChengBenParameter3_4['TieShuiXiaoHaoA'] + $price_arr_7['6782101'] * $ng_ChengBenParameter3_4['FeiGangTieXiaoHaoA']) * $ng_ChengBenParameter3_4['GangTieLiaoXiaoHaoA'];
			//B参数
			$hjxh_B_4 = ($price_arr_6['259422'] * (1 - $ChengDuiHuiPiaoTieXianLv * 3 / 1000) +  $ng_ChengBenParameter3_4['YunFei1B']) * $ng_ChengBenParameter3_4['DunGangXiaoHao1B'] / 1000 + ($price_arr_6['428910'] * (1 - $ChengDuiHuiPiaoTieXianLv * 3 / 1000) + $ng_ChengBenParameter3_4['YunFei2B']) * $ng_ChengBenParameter3_4['DunGangXiaoHao2B'] / 1000 + $ng_ChengBenParameter3_4['QiTaHeJinB'];
			
			//转炉螺纹钢方坯成本
			$zllwgfpcb_4 = $gtlxh_A_4 + $hjxh_B_4 + ( $ng_ChengBenParameter3_4['NengYuanDongLi'] + $ng_ChengBenParameter3_4['FuLiao'] + $ng_ChengBenParameter3_4['RenGong'] + $ng_ChengBenParameter3_4['ZhiZhaoFeiYong'] ) * ( 1 + $ng_ChengBenParameter3_4['ZengZhiShuiLv']) ;
			//转炉螺纹钢成本
			$zllwgcb_4 = $zllwgfpcb_4 + $ng_ChengBenParameter3_4['LuoWenGangJiaGongFei'] * ( 1 + $ng_ChengBenParameter3_4['ZengZhiShuiLv']);
			//转炉螺纹钢毛利
			$zllwgml_4 = $price_arr_6['672023'] - $zllwgcb_4;
			//转炉螺纹钢利润
			if($zllwgml_4<=0){
				$zllwg_ly_4 = 0;
			}else{
				$zllwg_ly_4 = $zllwgml_4 * $ng_ChengBenParameter3_4['ZengZhiShuiLv'] * $ng_ChengBenParameter3_4['GeLeiShuiFeiJiFuJia'];
			}
			$zllwgly_4 = $zllwgml_4 - $ng_ChengBenParameter3_4['QiJianFeiYong'] * ( 1 + $ng_ChengBenParameter3_4['ZengZhiShuiLv']) - $zllwg_ly_4;
			//转炉螺纹钢净利润
			if($zllwgly_4<=0){
				$zllwg_jly_4 = 0;
			}else{
				$zllwg_jly_4 = $zllwgly_4 * $ng_ChengBenParameter3_4['SuoDeShuiLv'];
			}
			$zllwgjly_4 = $zllwgly_4 - $zllwg_jly_4;

			//查看数据库是否有当天数据
			$have_data = $this ->maindao->get_ng_TieHeJinChengBenIndex($Date,125);
			if($have_data['indexValue']){//有数据了
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,125,round($zllwgfpcb_4));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,126,round($zllwgcb_4));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,127,round($zllwgml_4));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,128,round($zllwgly_4));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,129,round($zllwgjly_4));
			}else{//没有,插入新数据
				$insert_value_4 = "('$Date','125','".round($zllwgfpcb_4)."'),('$Date','126','".round($zllwgcb_4)."'),('$Date','127','".round($zllwgml_4)."'),('$Date','128','".round($zllwgly_4)."'),('$Date','129','".round($zllwgjly_4)."');";
				$this ->maindao->insert_ng_TieHeJinChengBenIndex_value($insert_value_4);
				/*
				$this ->maindao->insert_ng_TieHeJinChengBenIndex($Date,125,$zllwgfpcb_4);
				$this ->maindao->insert_ng_TieHeJinChengBenIndex($Date,126,$zllwgcb_4);
				$this ->maindao->insert_ng_TieHeJinChengBenIndex($Date,127,$zllwgml_4);
				$this ->maindao->insert_ng_TieHeJinChengBenIndex($Date,128,$zllwgly_4);
				$this ->maindao->insert_ng_TieHeJinChengBenIndex($Date,129,$zllwgjly_4);
				*/
			}
			echo "唐山铁水成本:::".$ng_TieHeJinChengBenIndex_4['indexValue']."<br>";
			echo "A参数:::".$gtlxh_A_4."<br>";
			echo "B参数:::".$hjxh_B_4."<br>";
			echo "转炉螺纹钢方坯成本:::".$zllwgfpcb_4."<br>";
			echo "转炉螺纹钢成本:::".$zllwgcb_4."<br>";
			echo "转炉螺纹钢毛利:::".$zllwgml_4."<br>";
			echo "转炉螺纹钢利润:::".$zllwgly_4."<br>";
			echo "转炉螺纹钢净利润:::".$zllwgjly_4."<br>";
			echo "<pre/>";print_R($ng_ChengBenParameter3_4);print_R($price_arr_6);print_R($price_arr_7);

		}
		
		//5:江苏长江沿线钢厂普板成本模型
		if($Type==5 || $Type=="" || $tangshan_or_jiangsu==1){
			//echo "<pre/>";print_r($this->maindao);
			$ng_ChengBenParameter3_5 = $this->maindao->get_ng_ChengBenParameter3($Date,5);
			$ng_TieHeJinChengBenIndex_5 = $this->maindao->get_ng_TieHeJinChengBenIndex($Date,101);

			//A参数
			$gtlxh_A_5 = ($ng_TieHeJinChengBenIndex_5['indexValue'] * $ng_ChengBenParameter3_5['TieShuiXiaoHaoA'] + $price_arr_7['1182141'] * $ng_ChengBenParameter3_5['FeiGangTieXiaoHaoA']) * $ng_ChengBenParameter3_5['GangTieLiaoXiaoHaoA'];
			//B参数
			$hjxh_B_5 = ($price_arr_6['418910'] * (1 - $ChengDuiHuiPiaoTieXianLv * 3 / 1000) +  $ng_ChengBenParameter3_5['YunFei1B']) * $ng_ChengBenParameter3_5['DunGangXiaoHao1B'] / 1000 + ($price_arr_6['598710'] * (1 - $ChengDuiHuiPiaoTieXianLv * 3 / 1000) + $ng_ChengBenParameter3_5['YunFei2B']) * $ng_ChengBenParameter3_5['DunGangXiaoHao2B'] / 1000 + $ng_ChengBenParameter3_5['QiTaHeJinB'];
			
			//转炉螺纹钢方坯成本
			$zllwgfpcb_5 = $gtlxh_A_5 + $hjxh_B_5 + ( $ng_ChengBenParameter3_5['NengYuanDongLi'] + $ng_ChengBenParameter3_5['FuLiao'] + $ng_ChengBenParameter3_5['RenGong'] + $ng_ChengBenParameter3_5['ZhiZhaoFeiYong'] ) * ( 1 + $ng_ChengBenParameter3_5['ZengZhiShuiLv']) ;
			//转炉螺纹钢成本
			$zllwgcb_5 = $zllwgfpcb_5 + $ng_ChengBenParameter3_5['LuoWenGangJiaGongFei'] * ( 1 + $ng_ChengBenParameter3_5['ZengZhiShuiLv']);
			//转炉螺纹钢毛利
			$zllwgml_5 = $price_arr_6['073012'] - $zllwgcb_5;
			//转炉螺纹钢利润
			if($zllwgml_5<=0){
				$zllwg_ly_5 = 0;
			}else{
				$zllwg_ly_5 = $zllwgml_5 * $ng_ChengBenParameter3_5['ZengZhiShuiLv'] * $ng_ChengBenParameter3_5['GeLeiShuiFeiJiFuJia'];
			}
			$zllwgly_5 = $zllwgml_5 - $ng_ChengBenParameter3_5['QiJianFeiYong'] * ( 1 + $ng_ChengBenParameter3_5['ZengZhiShuiLv']) - $zllwg_ly_5;
			//转炉螺纹钢净利润
			if($zllwgly_5<=0){
				$zllwg_jly_5 = 0;
			}else{
				$zllwg_jly_5 = $zllwgly_5 * $ng_ChengBenParameter3_5['SuoDeShuiLv'];
			}
			$zllwgjly_5 = $zllwgly_5 - $zllwg_jly_5;

			//查看数据库是否有当天数据
			$have_data = $this ->maindao->get_ng_TieHeJinChengBenIndex($Date,130);
			if($have_data['indexValue']){//有数据了
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,130,round($zllwgfpcb_5));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,131,round($zllwgcb_5));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,132,round($zllwgml_5));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,133,round($zllwgly_5));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,134,round($zllwgjly_5));
			}else{//没有,插入新数据
				$insert_value_5 = "('$Date','130','".round($zllwgfpcb_5)."'),('$Date','131','".round($zllwgcb_5)."'),('$Date','132','".round($zllwgml_5)."'),('$Date','133','".round($zllwgly_5)."'),('$Date','134','".round($zllwgjly_5)."');";
				$this ->maindao->insert_ng_TieHeJinChengBenIndex_value($insert_value_5);
				/*
				$this ->maindao->insert_ng_TieHeJinChengBenIndex($Date,130,$zllwgfpcb_5);
				$this ->maindao->insert_ng_TieHeJinChengBenIndex($Date,131,$zllwgcb_5);
				$this ->maindao->insert_ng_TieHeJinChengBenIndex($Date,132,$zllwgml_5);
				$this ->maindao->insert_ng_TieHeJinChengBenIndex($Date,133,$zllwgly_5);
				$this ->maindao->insert_ng_TieHeJinChengBenIndex($Date,134,$zllwgjly_5);
				*/
			}

		}		

		//6:唐山钢厂普板成本模型
		if($Type==6 || $Type=="" || $tangshan_or_jiangsu==0){
			//echo "<pre/>";print_r($this->maindao);
			$ng_ChengBenParameter3_6 = $this->maindao->get_ng_ChengBenParameter3($Date,6);
			$ng_TieHeJinChengBenIndex_6 = $this->maindao->get_ng_TieHeJinChengBenIndex($Date,102);

			//A参数
			$gtlxh_A_6 = ($ng_TieHeJinChengBenIndex_6['indexValue'] * $ng_ChengBenParameter3_6['TieShuiXiaoHaoA'] + $price_arr_7['6782101'] * $ng_ChengBenParameter3_6['FeiGangTieXiaoHaoA']) * $ng_ChengBenParameter3_6['GangTieLiaoXiaoHaoA'];
			//B参数
			$hjxh_B_6 = ($price_arr_6['428910'] * (1 - $ChengDuiHuiPiaoTieXianLv * 3 / 1000) +  $ng_ChengBenParameter3_6['YunFei1B']) * $ng_ChengBenParameter3_6['DunGangXiaoHao1B'] / 1000 + ($price_arr_6['448710'] * (1 - $ChengDuiHuiPiaoTieXianLv * 3 / 1000) + $ng_ChengBenParameter3_6['YunFei2B']) * $ng_ChengBenParameter3_6['DunGangXiaoHao2B'] / 1000 + $ng_ChengBenParameter3_6['QiTaHeJinB'];
			
			//转炉螺纹钢方坯成本
			$zllwgfpcb_6 = $gtlxh_A_6 + $hjxh_B_6 + ( $ng_ChengBenParameter3_6['NengYuanDongLi'] + $ng_ChengBenParameter3_6['FuLiao'] + $ng_ChengBenParameter3_6['RenGong'] + $ng_ChengBenParameter3_6['ZhiZhaoFeiYong'] ) * ( 1 + $ng_ChengBenParameter3_6['ZengZhiShuiLv']) ;
			//转炉螺纹钢成本
			$zllwgcb_6 = $zllwgfpcb_6 + $ng_ChengBenParameter3_6['LuoWenGangJiaGongFei'] * ( 1 + $ng_ChengBenParameter3_6['ZengZhiShuiLv']);
			//转炉螺纹钢毛利
			$zllwgml_6 = $price_arr_6['673012'] - $zllwgcb_6;
			//转炉螺纹钢利润
			if($zllwgml_6<=0){
				$zllwg_ly_6 = 0;
			}else{
				$zllwg_ly_6 = $zllwgml_6 * $ng_ChengBenParameter3_6['ZengZhiShuiLv'] * $ng_ChengBenParameter3_6['GeLeiShuiFeiJiFuJia'];
			}
			$zllwgly_6 = $zllwgml_6 - $ng_ChengBenParameter3_6['QiJianFeiYong'] * ( 1 + $ng_ChengBenParameter3_6['ZengZhiShuiLv']) - $zllwg_ly_6;
			//转炉螺纹钢净利润
			if($zllwgly_6<=0){
				$zllwg_jly_6 = 0;
			}else{
				$zllwg_jly_6 = $zllwgly_6 * $ng_ChengBenParameter3_6['SuoDeShuiLv'];
			}
			$zllwgjly_6 = $zllwgly_6 - $zllwg_jly_6;

			//查看数据库是否有当天数据
			$have_data = $this ->maindao->get_ng_TieHeJinChengBenIndex($Date,135);
			if($have_data['indexValue']){//有数据了
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,135,round($zllwgfpcb_6));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,136,round($zllwgcb_6));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,137,round($zllwgml_6));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,138,round($zllwgly_6));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,139,round($zllwgjly_6));
			}else{//没有,插入新数据
				$insert_value_6 = "('$Date','135','".round($zllwgfpcb_6)."'),('$Date','136','".round($zllwgcb_6)."'),('$Date','137','".round($zllwgml_6)."'),('$Date','138','".round($zllwgly_6)."'),('$Date','139','".round($zllwgjly_6)."');";
				$this ->maindao->insert_ng_TieHeJinChengBenIndex_value($insert_value_6);
				/*
				$this ->maindao->insert_ng_TieHeJinChengBenIndex($Date,135,$zllwgfpcb_6);
				$this ->maindao->insert_ng_TieHeJinChengBenIndex($Date,136,$zllwgcb_6);
				$this ->maindao->insert_ng_TieHeJinChengBenIndex($Date,137,$zllwgml_6);
				$this ->maindao->insert_ng_TieHeJinChengBenIndex($Date,138,$zllwgly_6);
				$this ->maindao->insert_ng_TieHeJinChengBenIndex($Date,139,$zllwgjly_6);
				*/
			}

		}

		//7:江苏长江沿线钢厂低合金板成本模型
		if($Type==7 || $Type=="" || $tangshan_or_jiangsu==1){
			//echo "<pre/>";print_r($this->maindao);
			$ng_ChengBenParameter3_7 = $this->maindao->get_ng_ChengBenParameter3($Date,7);
			$ng_TieHeJinChengBenIndex_7 = $this->maindao->get_ng_TieHeJinChengBenIndex($Date,101);

			//A参数
			$gtlxh_A_7 = ($ng_TieHeJinChengBenIndex_7['indexValue'] * $ng_ChengBenParameter3_7['TieShuiXiaoHaoA'] + $price_arr_7['1182141'] * $ng_ChengBenParameter3_7['FeiGangTieXiaoHaoA']) * $ng_ChengBenParameter3_7['GangTieLiaoXiaoHaoA'];
			//B参数
			$hjxh_B_7 = ($price_arr_6['418910'] * (1 - $ChengDuiHuiPiaoTieXianLv * 3 / 1000) +  $ng_ChengBenParameter3_7['YunFei1B']) * $ng_ChengBenParameter3_7['DunGangXiaoHao1B'] / 1000 + ($price_arr_6['598710'] * (1 - $ChengDuiHuiPiaoTieXianLv * 3 / 1000) + $ng_ChengBenParameter3_7['YunFei2B']) * $ng_ChengBenParameter3_7['DunGangXiaoHao2B'] / 1000 +($price_arr_6['438810'] * (1 - $ChengDuiHuiPiaoTieXianLv * 3 / 1000) + $ng_ChengBenParameter3_7['YunFei3B']) * $ng_ChengBenParameter3_7['DunGangXiaoHao3B'] / 1000 + $ng_ChengBenParameter3_7['QiTaHeJinB'];
			
			//转炉螺纹钢方坯成本
			$zllwgfpcb_7 = $gtlxh_A_7 + $hjxh_B_7 + ( $ng_ChengBenParameter3_7['NengYuanDongLi'] + $ng_ChengBenParameter3_7['FuLiao'] + $ng_ChengBenParameter3_7['RenGong'] + $ng_ChengBenParameter3_7['ZhiZhaoFeiYong'] ) * ( 1 + $ng_ChengBenParameter3_7['ZengZhiShuiLv']) ;
			//转炉螺纹钢成本
			$zllwgcb_7 = $zllwgfpcb_7 + $ng_ChengBenParameter3_7['LuoWenGangJiaGongFei'] * ( 1 + $ng_ChengBenParameter3_7['ZengZhiShuiLv']);
			//转炉螺纹钢毛利
			$zllwgml_7 = $price_arr_6['073022'] - $zllwgcb_7;
			//转炉螺纹钢利润
			if($zllwgml_7<=0){
				$zllwg_ly_7 = 0;
			}else{
				$zllwg_ly_7 = $zllwgml_7 * $ng_ChengBenParameter3_7['ZengZhiShuiLv'] * $ng_ChengBenParameter3_7['GeLeiShuiFeiJiFuJia'];
			}
			$zllwgly_7 = $zllwgml_7 - $ng_ChengBenParameter3_7['QiJianFeiYong'] * ( 1 + $ng_ChengBenParameter3_7['ZengZhiShuiLv']) - $zllwg_ly_7;
			//转炉螺纹钢净利润
			if($zllwgly_7<=0){
				$zllwg_jly_7 = 0;
			}else{
				$zllwg_jly_7 = $zllwgly_7 * $ng_ChengBenParameter3_7['SuoDeShuiLv'];
			}
			$zllwgjly_7 = $zllwgly_7 - $zllwg_jly_7;

			//查看数据库是否有当天数据
			$have_data = $this ->maindao->get_ng_TieHeJinChengBenIndex($Date,140);
			if($have_data['indexValue']){//有数据了
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,140,round($zllwgfpcb_7));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,141,round($zllwgcb_7));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,142,round($zllwgml_7));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,143,round($zllwgly_7));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,144,round($zllwgjly_7));
			}else{//没有,插入新数据
				$insert_value_7 = "('$Date','140','".round($zllwgfpcb_7)."'),('$Date','141','".round($zllwgcb_7)."'),('$Date','142','".round($zllwgml_7)."'),('$Date','143','".round($zllwgly_7)."'),('$Date','144','".round($zllwgjly_7)."');";
				$this ->maindao->insert_ng_TieHeJinChengBenIndex_value($insert_value_7);
				/*
				$this ->maindao->insert_ng_TieHeJinChengBenIndex($Date,140,$zllwgfpcb_7);
				$this ->maindao->insert_ng_TieHeJinChengBenIndex($Date,141,$zllwgcb_7);
				$this ->maindao->insert_ng_TieHeJinChengBenIndex($Date,142,$zllwgml_7);
				$this ->maindao->insert_ng_TieHeJinChengBenIndex($Date,143,$zllwgly_7);
				$this ->maindao->insert_ng_TieHeJinChengBenIndex($Date,144,$zllwgjly_7);
				*/
			}
			echo "江苏长江铁水成本:::".$ng_TieHeJinChengBenIndex_7['indexValue']."<br>";
			echo "A参数:::".$gtlxh_A_7."<br>";
			echo "B参数:::".$hjxh_B_7."<br>";
			echo "转炉螺纹钢方坯成本:::".$zllwgfpcb_7."<br>";
			echo "转炉螺纹钢成本:::".$zllwgcb_7."<br>";
			echo "转炉螺纹钢毛利:::".$zllwgml_7."<br>";
			echo "转炉螺纹钢利润:::".$zllwgly_7."<br>";
			echo "转炉螺纹钢净利润:::".$zllwgjly_7."<br>";
			echo "<pre/>";print_R($ng_ChengBenParameter3_7);print_R($price_arr_6);print_R($price_arr_7);

		}
		
		//8：唐山钢厂低合金板成本模型
		if($Type==8 || $Type=="" || $tangshan_or_jiangsu==0){
			//echo "<pre/>";print_r($this->maindao);
			$ng_ChengBenParameter3_8 = $this->maindao->get_ng_ChengBenParameter3($Date,8);
			$ng_TieHeJinChengBenIndex_8 = $this->maindao->get_ng_TieHeJinChengBenIndex($Date,102);

			//A参数
			$gtlxh_A_8 = ($ng_TieHeJinChengBenIndex_8['indexValue'] * $ng_ChengBenParameter3_8['TieShuiXiaoHaoA'] + $price_arr_7['6782101'] * $ng_ChengBenParameter3_8['FeiGangTieXiaoHaoA']) * $ng_ChengBenParameter3_8['GangTieLiaoXiaoHaoA'];
			//B参数
			$hjxh_B_8 = ($price_arr_6['428910'] * (1 - $ChengDuiHuiPiaoTieXianLv * 3 / 1000) +  $ng_ChengBenParameter3_8['YunFei1B']) * $ng_ChengBenParameter3_8['DunGangXiaoHao1B'] / 1000 + ($price_arr_6['448710'] * (1 - $ChengDuiHuiPiaoTieXianLv * 3 / 1000) + $ng_ChengBenParameter3_8['YunFei2B']) * $ng_ChengBenParameter3_8['DunGangXiaoHao2B'] / 1000 +($price_arr_6['438810'] * (1 - $ChengDuiHuiPiaoTieXianLv * 3 / 1000) + $ng_ChengBenParameter3_8['YunFei3B']) * $ng_ChengBenParameter3_8['DunGangXiaoHao3B'] / 1000 + $ng_ChengBenParameter3_8['QiTaHeJinB'];
			
			//转炉螺纹钢方坯成本
			$zllwgfpcb_8 = $gtlxh_A_8 + $hjxh_B_8 + ( $ng_ChengBenParameter3_8['NengYuanDongLi'] + $ng_ChengBenParameter3_8['FuLiao'] + $ng_ChengBenParameter3_8['RenGong'] + $ng_ChengBenParameter3_8['ZhiZhaoFeiYong'] ) * ( 1 + $ng_ChengBenParameter3_8['ZengZhiShuiLv']) ;
			//转炉螺纹钢成本
			$zllwgcb_8 = $zllwgfpcb_8 + $ng_ChengBenParameter3_8['LuoWenGangJiaGongFei'] * ( 1 + $ng_ChengBenParameter3_8['ZengZhiShuiLv']);
			//转炉螺纹钢毛利
			$zllwgml_8 = $price_arr_6['673022'] - $zllwgcb_8;
			//转炉螺纹钢利润
			if($zllwgml_8<=0){
				$zllwg_ly_8 = 0;
			}else{
				$zllwg_ly_8 = $zllwgml_8 * $ng_ChengBenParameter3_8['ZengZhiShuiLv'] * $ng_ChengBenParameter3_8['GeLeiShuiFeiJiFuJia'];
			}
			$zllwgly_8 = $zllwgml_8 - $ng_ChengBenParameter3_8['QiJianFeiYong'] * ( 1 + $ng_ChengBenParameter3_8['ZengZhiShuiLv']) - $zllwg_ly_8;
			//转炉螺纹钢净利润
			if($zllwgly_8<=0){
				$zllwg_jly_8 = 0;
			}else{
				$zllwg_jly_8 = $zllwgly_8 * $ng_ChengBenParameter3_8['SuoDeShuiLv'];
			}
			$zllwgjly_8 = $zllwgly_8 - $zllwg_jly_8;

			//查看数据库是否有当天数据
			$have_data = $this ->maindao->get_ng_TieHeJinChengBenIndex($Date,145);
			if($have_data['indexValue']){//有数据了
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,145,round($zllwgfpcb_8));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,146,round($zllwgcb_8));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,147,round($zllwgml_8));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,148,round($zllwgly_8));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,149,round($zllwgjly_8));
			}else{//没有,插入新数据
				$insert_value_8 = "('$Date','145','".round($zllwgfpcb_8)."'),('$Date','146','".round($zllwgcb_8)."'),('$Date','147','".round($zllwgml_8)."'),('$Date','148','".round($zllwgly_8)."'),('$Date','149','".round($zllwgjly_8)."');";
				$this ->maindao->insert_ng_TieHeJinChengBenIndex_value($insert_value_8);
				/*
				$this ->maindao->insert_ng_TieHeJinChengBenIndex($Date,145,$zllwgfpcb_8);
				$this ->maindao->insert_ng_TieHeJinChengBenIndex($Date,146,$zllwgcb_8);
				$this ->maindao->insert_ng_TieHeJinChengBenIndex($Date,147,$zllwgml_8);
				$this ->maindao->insert_ng_TieHeJinChengBenIndex($Date,148,$zllwgly_8);
				$this ->maindao->insert_ng_TieHeJinChengBenIndex($Date,149,$zllwgjly_8);
				*/
			}
			echo "唐山铁水成本:::".$ng_TieHeJinChengBenIndex_8['indexValue']."<br>";
			echo "A参数:::".$gtlxh_A_8."<br>";
			echo "B参数:::".$hjxh_B_8."<br>";
			echo "转炉螺纹钢方坯成本:::".$zllwgfpcb_8."<br>";
			echo "转炉螺纹钢成本:::".$zllwgcb_8."<br>";
			echo "转炉螺纹钢毛利:::".$zllwgml_8."<br>";
			echo "转炉螺纹钢利润:::".$zllwgly_8."<br>";
			echo "转炉螺纹钢净利润:::".$zllwgjly_8."<br>";
		echo "<pre/>";print_R($ng_ChengBenParameter3_8);print_R($price_arr_6);print_R($price_arr_7);
		}

		//9：江苏长江沿线钢厂普带成本模型
		if($Type==9 || $Type=="" || $tangshan_or_jiangsu==1){
			//echo "<pre/>";print_r($this->maindao);
			$ng_ChengBenParameter3_9 = $this->maindao->get_ng_ChengBenParameter3($Date,9);
			$ng_TieHeJinChengBenIndex_9 = $this->maindao->get_ng_TieHeJinChengBenIndex($Date,101);

			//A参数
			$gtlxh_A_9 = ($ng_TieHeJinChengBenIndex_9['indexValue'] * $ng_ChengBenParameter3_9['TieShuiXiaoHaoA'] + $price_arr_7['1182141'] * $ng_ChengBenParameter3_9['FeiGangTieXiaoHaoA']) * $ng_ChengBenParameter3_9['GangTieLiaoXiaoHaoA'];
			//B参数
			$hjxh_B_9 = ($price_arr_6['418910'] * (1 - $ChengDuiHuiPiaoTieXianLv * 3 / 1000) +  $ng_ChengBenParameter3_9['YunFei1B']) * $ng_ChengBenParameter3_9['DunGangXiaoHao1B'] / 1000 + $ng_ChengBenParameter3_9['QiTaHeJinB'];
			
			//普带方坯成本
			$pdfpcb_9 = $gtlxh_A_9 + $hjxh_B_9 + ( $ng_ChengBenParameter3_9['NengYuanDongLi'] + $ng_ChengBenParameter3_9['FuLiao'] + $ng_ChengBenParameter3_9['RenGong'] + $ng_ChengBenParameter3_9['ZhiZhaoFeiYong'] ) * ( 1 + $ng_ChengBenParameter3_9['ZengZhiShuiLv']) ;
			//普带成本
			$pdcb_9 = $pdfpcb_9 + $ng_ChengBenParameter3_9['LuoWenGangJiaGongFei'] * ( 1 + $ng_ChengBenParameter3_9['ZengZhiShuiLv']);
			//普带钢毛利
			$pdgml_9 = $price_arr_6['126015'] - $pdcb_9;
			//普带利润
			if($pdgml_9<=0){
				$pd_ly_9 = 0;
			}else{
				$pd_ly_9 = $pdgml_9 * $ng_ChengBenParameter3_9['ZengZhiShuiLv'] * $ng_ChengBenParameter3_9['GeLeiShuiFeiJiFuJia'];
			}
			$pdly_9 = $pdgml_9 - $ng_ChengBenParameter3_9['QiJianFeiYong'] * ( 1 + $ng_ChengBenParameter3_9['ZengZhiShuiLv']) - $pd_ly_9;
			//普带净利润
			if($pdly_9<=0){
				$pd_jly_9 = 0;
			}else{
				$pd_jly_9 = $pdly_9 * $ng_ChengBenParameter3_9['SuoDeShuiLv'];
			}
			$pdjly_9 = $pdly_9 - $pd_jly_9;

			//查看数据库是否有当天数据
			$have_data = $this ->maindao->get_ng_TieHeJinChengBenIndex($Date,150);
			if($have_data['indexValue']){//有数据了
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,150,round($pdfpcb_9));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,151,round($pdcb_9));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,152,round($pdgml_9));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,153,round($pdly_9));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,154,round($pdjly_9));
			}else{//没有,插入新数据
				$insert_value_9 = "('$Date','150','".round($pdfpcb_9)."'),('$Date','151','".round($pdcb_9)."'),('$Date','152','".round($pdgml_9)."'),('$Date','153','".round($pdly_9)."'),('$Date','154','".round($pdjly_9)."');";
				$this ->maindao->insert_ng_TieHeJinChengBenIndex_value($insert_value_9);
			}
			echo "长江沿线铁水成本:::".$ng_TieHeJinChengBenIndex_9['indexValue']."<br>";
			echo "A参数:::".$gtlxh_A_9."<br>";
			echo "B参数:::".$hjxh_B_9."<br>";
			echo "普带方坯成本:::".$pdfpcb_9."<br>";
			echo "普带成本:::".$pdcb_9."<br>";
			echo "普带钢毛利:::".$pdgml_9."<br>";
			echo "普带利润:::".$pdly_9."<br>";
			echo "普带净利润:::".$pdjly_9."<br>";
			echo "<pre/>";print_R($ng_ChengBenParameter3_9);print_R($price_arr_6);print_R($price_arr_7);

		}
		
		//10：江苏长江沿线钢厂碳圆成本模型
		if($Type==10 || $Type=="" || $tangshan_or_jiangsu==1){
			//echo "<pre/>";print_r($this->maindao);
			$ng_ChengBenParameter3_10 = $this->maindao->get_ng_ChengBenParameter3($Date,10);
			$ng_TieHeJinChengBenIndex_10 = $this->maindao->get_ng_TieHeJinChengBenIndex($Date,101);

			//A参数
			$gtlxh_A_10 = ($ng_TieHeJinChengBenIndex_10['indexValue'] * $ng_ChengBenParameter3_10['TieShuiXiaoHaoA'] + $price_arr_7['1182141'] * $ng_ChengBenParameter3_10['FeiGangTieXiaoHaoA']) * $ng_ChengBenParameter3_10['GangTieLiaoXiaoHaoA'];
			//B参数
			$hjxh_B_10 = ($price_arr_6['418910'] * (1 - $ChengDuiHuiPiaoTieXianLv * 3 / 1000) +  $ng_ChengBenParameter3_10['YunFei1B']) * $ng_ChengBenParameter3_10['DunGangXiaoHao1B'] / 1000 + $ng_ChengBenParameter3_10['QiTaHeJinB'];
			
			//碳圆方坯成本
			$tyfpcb_10 = $gtlxh_A_10 + $hjxh_B_10 + ( $ng_ChengBenParameter3_10['NengYuanDongLi'] + $ng_ChengBenParameter3_10['FuLiao'] + $ng_ChengBenParameter3_10['RenGong'] + $ng_ChengBenParameter3_10['ZhiZhaoFeiYong'] ) * ( 1 + $ng_ChengBenParameter3_10['ZengZhiShuiLv']) ;
			//碳圆成本
			$tycb_10 = $tyfpcb_10 + $ng_ChengBenParameter3_10['LuoWenGangJiaGongFei'] * ( 1 + $ng_ChengBenParameter3_10['ZengZhiShuiLv']);
			//碳圆钢毛利
			$tygml_10 = $price_arr_6['122244'] - $tycb_10;
			//碳圆利润
			if($tygml_10<=0){
				$ty_ly_10 = 0;
			}else{
				$ty_ly_10 = $tygml_10 * $ng_ChengBenParameter3_10['ZengZhiShuiLv'] * $ng_ChengBenParameter3_10['GeLeiShuiFeiJiFuJia'];
			}
			$tyly_10 = $tygml_10 - $ng_ChengBenParameter3_10['QiJianFeiYong'] * ( 1 + $ng_ChengBenParameter3_10['ZengZhiShuiLv']) - $ty_ly_10;
			//碳圆净利润
			if($tyly_10<=0){
				$ty_jly_10 = 0;
			}else{
				$ty_jly_10 = $tyly_10 * $ng_ChengBenParameter3_10['SuoDeShuiLv'];
			}
			$tyjly_10 = $tyly_10 - $ty_jly_10;

			//查看数据库是否有当天数据
			$have_data = $this ->maindao->get_ng_TieHeJinChengBenIndex($Date,155);
			if($have_data['indexValue']){//有数据了
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,155,round($tyfpcb_10));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,156,round($tycb_10));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,157,round($tygml_10));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,158,round($tyly_10));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,159,round($tyjly_10));
			}else{//没有,插入新数据
				$insert_value_10 = "('$Date','155','".round($tyfpcb_10)."'),('$Date','156','".round($tycb_10)."'),('$Date','157','".round($tygml_10)."'),('$Date','158','".round($tyly_10)."'),('$Date','159','".round($tyjly_10)."');";
				$this ->maindao->insert_ng_TieHeJinChengBenIndex_value($insert_value_10);
			}
			echo "长江沿线铁水成本:::".$ng_TieHeJinChengBenIndex_10['indexValue']."<br>";
			echo "A参数:::".$gtlxh_A_10."<br>";
			echo "B参数:::".$hjxh_B_10."<br>";
			echo "碳圆方坯成本:::".$tyfpcb_10."<br>";
			echo "碳圆成本:::".$tycb_10."<br>";
			echo "碳圆钢毛利:::".$tygml_10."<br>";
			echo "碳圆利润:::".$tyly_10."<br>";
			echo "碳圆净利润:::".$tyjly_10."<br>";
			echo "<pre/>";print_R($ng_ChengBenParameter3_10);print_R($price_arr_6);print_R($price_arr_7);

		}	
		
		//11：唐山地区带钢成本模型
		if($Type==11 || $Type=="" || $tangshan_or_jiangsu==0){
			//echo "<pre/>";print_r($this->maindao);
			$ng_ChengBenParameter3_11 = $this->maindao->get_ng_ChengBenParameter3($Date,11);
			$ng_TieHeJinChengBenIndex_11 = $this->maindao->get_ng_TieHeJinChengBenIndex($Date,102);

			//A参数
			$gtlxh_A_11 = ($ng_TieHeJinChengBenIndex_11['indexValue'] * $ng_ChengBenParameter3_11['TieShuiXiaoHaoA'] + $price_arr_7['6782101'] * $ng_ChengBenParameter3_11['FeiGangTieXiaoHaoA']) * $ng_ChengBenParameter3_11['GangTieLiaoXiaoHaoA'];
			//B参数
			$hjxh_B_11 = ($price_arr_6['428910'] * (1 - $ChengDuiHuiPiaoTieXianLv * 3 / 1000) +  $ng_ChengBenParameter3_11['YunFei1B']) * $ng_ChengBenParameter3_11['DunGangXiaoHao1B'] / 1000 + $ng_ChengBenParameter3_11['QiTaHeJinB'];
			
			//带钢方坯成本
			$dgfpcb_11 = $gtlxh_A_11 + $hjxh_B_11 + ( $ng_ChengBenParameter3_11['NengYuanDongLi'] + $ng_ChengBenParameter3_11['FuLiao'] + $ng_ChengBenParameter3_11['RenGong'] + $ng_ChengBenParameter3_11['ZhiZhaoFeiYong'] ) * ( 1 + $ng_ChengBenParameter3_11['ZengZhiShuiLv']) ;
			//带钢成本
			$dgcb_11 = $dgfpcb_11 + $ng_ChengBenParameter3_11['LuoWenGangJiaGongFei'] * ( 1 + $ng_ChengBenParameter3_11['ZengZhiShuiLv']);
			//带钢毛利
			$dgml_11 = $price_arr_6['676011'] - $dgcb_11;
			//带钢利润
			if($dgml_11<=0){
				$dg_ly_11 = 0;
			}else{
				$dg_ly_11 = $dgml_11 * $ng_ChengBenParameter3_11['ZengZhiShuiLv'] * $ng_ChengBenParameter3_11['GeLeiShuiFeiJiFuJia'];
			}
			$dgly_11 = $dgml_11 - $ng_ChengBenParameter3_11['QiJianFeiYong'] * ( 1 + $ng_ChengBenParameter3_11['ZengZhiShuiLv']) - $dg_ly_11;
			//带钢净利润
			if($dgly_11<=0){
				$dg_jly_11 = 0;
			}else{
				$dg_jly_11 = $dgly_11* $ng_ChengBenParameter3_11['SuoDeShuiLv'];
			}
			$dgjly_11 = $dgly_11 - $dg_jly_11;

			//查看数据库是否有当天数据
			$have_data = $this ->maindao->get_ng_TieHeJinChengBenIndex($Date,160);
			if($have_data['indexValue']){//有数据了
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,160,round($dgfpcb_11));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,161,round($dgcb_11));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,162,round($dgml_11));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,163,round($dgly_11));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,164,round($dgjly_11));
			}else{//没有,插入新数据
				$insert_value_11 = "('$Date','160','".round($dgfpcb_11)."'),('$Date','161','".round($dgcb_11)."'),('$Date','162','".round($dgml_11)."'),('$Date','163','".round($dgly_11)."'),('$Date','164','".round($dgjly_11)."');";
				$this ->maindao->insert_ng_TieHeJinChengBenIndex_value($insert_value_11);
			}
			echo "唐山铁水成本:::".$ng_TieHeJinChengBenIndex_11['indexValue']."<br>";
			echo "A参数:::".$gtlxh_A_11."<br>";
			echo "B参数:::".$hjxh_B_11."<br>";
			echo "带钢方坯成本:::".$dgfpcb_11."<br>";
			echo "带钢成本:::".$dgcb_11."<br>";
			echo "带钢钢毛利:::".$dgml_11."<br>";
			echo "带钢利润:::".$dgly_11."<br>";
			echo "带钢净利润:::".$dgjly_11."<br>";
			echo "<pre/>";print_R($ng_ChengBenParameter3_11);print_R($price_arr_6);print_R($price_arr_7);

		}		
		//12：唐山地区碳圆成本模型
		if($Type==12 || $Type=="" || $tangshan_or_jiangsu==0){
			//echo "<pre/>";print_r($this->maindao);
			$ng_ChengBenParameter3_12 = $this->maindao->get_ng_ChengBenParameter3($Date,12);
			$ng_TieHeJinChengBenIndex_12 = $this->maindao->get_ng_TieHeJinChengBenIndex($Date,102);

			//A参数
			$gtlxh_A_12 = ($ng_TieHeJinChengBenIndex_12['indexValue'] * $ng_ChengBenParameter3_12['TieShuiXiaoHaoA'] + $price_arr_7['6782101'] * $ng_ChengBenParameter3_12['FeiGangTieXiaoHaoA']) * $ng_ChengBenParameter3_12['GangTieLiaoXiaoHaoA'];
			//B参数
			$hjxh_B_12 = ($price_arr_6['428910'] * (1 - $ChengDuiHuiPiaoTieXianLv * 3 / 1000) +  $ng_ChengBenParameter3_12['YunFei1B']) * $ng_ChengBenParameter3_12['DunGangXiaoHao1B'] / 1000 + $ng_ChengBenParameter3_12['QiTaHeJinB'];
			
			//碳圆方坯成本
			$tyfpcb_12 = $gtlxh_A_12 + $hjxh_B_12 + ( $ng_ChengBenParameter3_12['NengYuanDongLi'] + $ng_ChengBenParameter3_12['FuLiao'] + $ng_ChengBenParameter3_12['RenGong'] + $ng_ChengBenParameter3_12['ZhiZhaoFeiYong'] ) * ( 1 + $ng_ChengBenParameter3_12['ZengZhiShuiLv']) ;
			//碳圆成本
			$tycb_12 = $tyfpcb_12 + $ng_ChengBenParameter3_12['LuoWenGangJiaGongFei'] * ( 1 + $ng_ChengBenParameter3_12['ZengZhiShuiLv']);
			//碳圆毛利
			$tyml_12 = $price_arr_6['672243'] - $tycb_12;
			//碳圆利润
			if($tyml_12<=0){
				$ty_ly_12 = 0;
			}else{
				$ty_ly_12 = $tyml_12 * $ng_ChengBenParameter3_12['ZengZhiShuiLv'] * $ng_ChengBenParameter3_12['GeLeiShuiFeiJiFuJia'];
			}
			$tyly_12 = $tyml_12 - $ng_ChengBenParameter3_12['QiJianFeiYong'] * ( 1 + $ng_ChengBenParameter3_12['ZengZhiShuiLv']) - $ty_ly_12;
			//碳圆净利润
			if($tyly_12<=0){
				$ty_jly_12 = 0;
			}else{
				$ty_jly_12 = $tyly_12* $ng_ChengBenParameter3_12['SuoDeShuiLv'];
			}
			$tyjly_12 = $tyly_12 - $ty_jly_12;

			//查看数据库是否有当天数据
			$have_data = $this ->maindao->get_ng_TieHeJinChengBenIndex($Date,165);
			if($have_data['indexValue']){//有数据了
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,165,round($tyfpcb_12));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,166,round($tycb_12));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,167,round($tyml_12));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,168,round($tyly_12));
				$this ->maindao->update_ng_TieHeJinChengBenIndex($Date,169,round($tyjly_12));
			}else{//没有,插入新数据
				$insert_value_12 = "('$Date','165','".round($tyfpcb_12)."'),('$Date','166','".round($tycb_12)."'),('$Date','167','".round($tyml_12)."'),('$Date','168','".round($tyly_12)."'),('$Date','169','".round($tyjly_12)."');";
				$this ->maindao->insert_ng_TieHeJinChengBenIndex_value($insert_value_12);
			}
			echo "唐山铁水成本:::".$ng_TieHeJinChengBenIndex_12['indexValue']."<br>";
			echo "A参数:::".$gtlxh_A_12."<br>";
			echo "B参数:::".$hjxh_B_12."<br>";
			echo "碳圆方坯成本:::".$tyfpcb_12."<br>";
			echo "碳圆成本:::".$tycb_12."<br>";
			echo "碳圆钢毛利:::".$tyml_12."<br>";
			echo "碳圆利润:::".$tyly_12."<br>";
			echo "碳圆净利润:::".$tyjly_12."<br>";
			echo "<pre/>";print_R($ng_ChengBenParameter3_12);print_R($price_arr_6);print_R($price_arr_7);

		}
		echo "运行完成";
	}

	//重新计算任务
    public function recount()
    {
		$today_now = date("Y-m-d");
		//每天两点清理掉计算中的任务，重新计算，防止程序卡死现象
		if(date("H:i:s")<="02:15:00" && date("H:i:s")>"01:45:00"){
			$this ->maindao -> update_ng_ModifyChengBenParameterTask($today_now);
		}
		//取出转态为未计算的任务进行计算
		$can_calc_cengben = $this ->maindao ->get_can_calc();
		if(empty($can_calc_cengben)){
			//正常任务
			$type = 2;
			$need_recount = $this ->maindao -> get_ng_ModifyChengBenParameterTask($type);
			echo "<pre/>";print_R($need_recount);//exit;
			foreach($need_recount as $key => $value){
				$pid = $value["pid"];
				$ng_ChengBenParameter3info = $this -> maindao -> get_ng_ChengBenParameter3_byid($pid);
				$this_day = $ng_ChengBenParameter3info['Date'];
				$next_day = $this ->maindao -> get_ng_ChengBenParameter3_nextday($this_day,$ng_ChengBenParameter3info['Type']);
				if(empty($next_day)){
					//$next_day = date("Y-m-d",strtotime(date("Y-m-d")."+1 days"));
					$next_day = date("Y-m-d");
				}
				//删除掉ng_TieHeJinChengBenIndex表中数据
				$type_thjcbi = "110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169";
				$this ->maindao -> delete_ng_TieHeJinChengBenIndex($this_day,$type_thjcbi,$next_day);
				$this ->maindao -> update_ng_ModifyChengBenParameterTask_ing($pid,1,$today_now);
				$isholiday_arr = $this-> get_isholiday_arr($this_day,$next_day);
				while($this_day<$next_day){
					if($isholiday_arr[$this_day]!=1){
						$params['Date'] = $this_day;
						$this->calc_cbmx($params);
					}
					$this_day = date("Y-m-d",strtotime($this_day."+1 day"));
				}

				$this ->maindao -> update_ng_ModifyChengBenParameterTask_ed($pid,2,$today_now);
			}
			//因为重新计算铁水成本导致的任务 --江苏的
			$type = 10;
			$need_recount_10 = $this ->maindao -> get_ng_ModifyChengBenParameterTask($type);
			echo "<pre/>";print_R($need_recount_10);//exit;
			foreach($need_recount_10 as $key => $value){
				$id = $value["id"];
				$this_day = $value['start'];
				$next_day = $value['end'];

				//删除掉ng_TieHeJinChengBenIndex表中数据
				$type_thjcbi = "110,111,112,113,114,120,121,122,123,124,130,131,132,133,134,140,141,142,143,144,150,151,152,153,154,155,156,157,158,159";
				$this ->maindao -> delete_ng_TieHeJinChengBenIndex($this_day,$type_thjcbi,$next_day);
				$this ->maindao -> update_ng_ModifyChengBenParameterTask10_ing($id,1);
				$isholiday_arr = $this-> get_isholiday_arr($this_day,$next_day);
				while($this_day<=$next_day){
					if($isholiday_arr[$this_day]!=1){
						$params['Date'] = $this_day;
						$params['tangshan_or_jiangsu'] = 1;
						$this->calc_cbmx($params);
					}
					$this_day = date("Y-m-d",strtotime($this_day."+1 day"));
				}

				$this ->maindao -> update_ng_ModifyChengBenParameterTask10_ed($id,2);
			}
			//因为重新计算铁水成本导致的任务  -- 唐山的
			$type = 11;
			$need_recount_11 = $this ->maindao -> get_ng_ModifyChengBenParameterTask($type);
			echo "<pre/>";print_R($need_recount_11);//exit;
			foreach($need_recount_11 as $key => $value){
				$id = $value["id"];
				$this_day = $value['start'];
				$next_day = $value['end'];

				//删除掉ng_TieHeJinChengBenIndex表中数据
				$type_thjcbi = "115,116,117,118,119,125,126,127,128,129,135,136,137,138,139,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159";
				$this ->maindao -> delete_ng_TieHeJinChengBenIndex($this_day,$type_thjcbi,$next_day);
				$this ->maindao -> update_ng_ModifyChengBenParameterTask10_ing($id,1);
				$isholiday_arr = $this-> get_isholiday_arr($this_day,$next_day);
				while($this_day<=$next_day){
					if($isholiday_arr[$this_day]!=1){
						$params['Date'] = $this_day;
						$params['tangshan_or_jiangsu'] = 0;
						$this->calc_cbmx($params);
					}
					$this_day = date("Y-m-d",strtotime($this_day."+1 day"));
				}

				$this ->maindao -> update_ng_ModifyChengBenParameterTask10_ed($id,2);
			}
			echo "运行完成";
		}else{
			echo "暂时不能计算，要等铁水成本计算完才可以开始!";
		}
	}	

	//获取上一个工作日
    public function get_last_workday($date)
    {
		$return_date = date("Y-m-d",strtotime($date."-1 day"));
		while( _isholiday ( $return_date )){
			$return_date = date("Y-m-d",strtotime($return_date."-1 day"));
		}
		return $return_date;
	}	


	//获取一段时间是否是工作日的
    public function get_isholiday_arr($sdate,$edate)
    {
		if(empty($sdate)){
			$sdate = date("Y-m-d");
		}
		if(empty($edate)){
			$edate = date("Y-m-d");
		}
		$return_arr = array();
		$date = $sdate;
		while($date<$edate){
			$date = date("Y-m-d",strtotime($date."+1 day"));
			if (date('D', strtotime($date)) == "Sat" or date('D', strtotime($date)) == 'Sun') {
				$return_arr[$date] = 1;
			}else{
				$return_arr[$date] = 0;
			}
		}
		$holiday_array = $this->maindao->get_isholiday_arr_by_date($sdate,$edate);
		//$sql = "select * from holiday where date='$date'";
		foreach( $holiday_array as $key => $value ){
			if($value['isholiday']=='1'){
				$return_arr[$value['date']] = $value['isholiday'];
			}else if($value['isholiday']=='0'){
				$return_arr[$value['date']] = $value['isholiday'];
			}
		}
		return $return_arr;

	}	


    public function test()
    {
		$sql = "select leibie,mvalue,datetime from gjshpi where datetime >= '2015-05-01' and datetime <= '2018-05-31' and leibie in ('中国','美洲','欧洲')  order by datetime asc , id desc";
		//$result = $this -> maindao->query($sql);
		//echo "<pre/>";print_r($result);
		$content = '<table width="100%"  border="1">';
		$content .= '<tr><td>时间</td><td>中国</td><td>美洲</td><td>欧洲</td></tr>';
		foreach($result as $key => $value){
			$now = $value['datetime'];
			if( $key%3==0){
				$content .= "<tr>";
				$content .= "<td>".$value['datetime']."</td>";
			}
			$content .= "<td>".$value['mvalue'].$key."</td>";
			if( $key%3==2&&$key>2){
				$content .= "</tr>";
			}
		}
		$content .= '</table>';
		echo $content;
	}	
	
} 
?>