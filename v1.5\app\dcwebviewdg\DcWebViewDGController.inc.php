<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );

class DcWebViewDGController extends AbstractController{
  
  public function __construct(){
    parent::__construct();
	
	//$this->_action->setDao( new DcWebViewDGDao( "MAIN" ) );
	//$this->_action->bizdao = new SystemDao('BIZ') ;
	//$this->_action->drcdao= new DcWebViewDGDao('GC');
	$this->_action->ngdao = new DcWebViewDGDao('DRCW') ;
	$this->_action->maindao = new DcWebViewDGDao('91R') ;//hlf/钢之家主数据库、正式库，测试无数据
	//$this->_action->gcdao=new DcWebViewDGDao('zGCTJ');//hlf/钢厂调价的本期价格及涨跌幅,正式库

	$this->_action->gcdao=new DcWebViewDGDao('GC');//hlf/钢厂调价的本期价格及涨跌幅,正式库
  }

  public function _dopre(){
	  //echo "<pre/>";print_r($_SESSION);exit;
      //$this->_action->checkSession();
  }
	

  public function v_index() {
	$this->_action->index($this->_request);
  }
  
   public function do_savexundj(){
	   $this->_action->savexundj($this->_request);
  }

}


?>