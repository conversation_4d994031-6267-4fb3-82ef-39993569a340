* {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
	 cursor: url(../../images/jiantou.png) 8 3, auto !important;
}

html,body { width: 100%; height: 100%; font-family: myFirstFont !important;}
html{ background:#000A3B;}
body{ background-image: url(bodybg.jpg); background-size:100% auto; background-repeat:no-repeat; background-position: 0 bottom;}

.fl { float: left}
.fr { float: right}

ul,ol,li{ list-style: none}
@font-face { font-family: myFirstFont; src: url('DISPLAY FREE TFB.ttf');}
::-webkit-scrollbar { width: 5px; height: 5px; position: absolute}
::-webkit-scrollbar-thumb { background-color: #5bc0de;}
::-webkit-scrollbar-track {   background-color: #ddd;}
.allnav { height: 100%;}
.rightTop { position: relative; transition: all 1s; }