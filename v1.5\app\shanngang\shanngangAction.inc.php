<?php

class shanngangAction extends AbstractAction
{
	public function __construct()
	{
    	parent::__construct();
	}

	public function checkSession()
	{
	}
	
	private function export_yulong_price($year,$var_info,$cityinfo,$datas){
		include '/usr/local/www/libs/PHPExcel/PHPExcel.php';
		$objPHPExcel = new PHPExcel();
		$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
		$sheet = $objPHPExcel->getActiveSheet();
		
		$sheet->getDefaultStyle()->getAlignment()->setHorizontal(PHPExcel_Style_Alignment::HORIZONTAL_CENTER);
		$sheet->getDefaultStyle()->getAlignment()->setVertical(PHPExcel_Style_Alignment::VERTICAL_CENTER);
		
		
		$yue_arr = array('一', '二', '三', '四', '五', '六', '七', '八', '九', '十', '十一', '十二');
		
		// 标题行
		$sheet->getColumnDimensionByColumn(0)->setWidth(12);
		$sheet->setCellValueByColumnAndRow(0,1,iconv('GBK','UTF-8',"销售区域"));
		$sheet->mergeCellsByColumnAndRow(0,1,0,2);

		$sheet->getColumnDimensionByColumn(1)->setWidth(12);
		$sheet->setCellValueByColumnAndRow(1,1,iconv('GBK','UTF-8',"规格"));
		$sheet->mergeCellsByColumnAndRow(1,1,1,2);

		$sheet->getColumnDimensionByColumn(2)->setWidth(15);
		$sheet->setCellValueByColumnAndRow(2,1,iconv('GBK','UTF-8',"区域其他钢厂"));
		$sheet->mergeCellsByColumnAndRow(2,1,2,2);
		
		for($i=0; $i < 12; $i++){
			$col = $i*2 + 3;
			$sheet->setCellValueByColumnAndRow($col,1,iconv('GBK','UTF-8',$yue_arr[$i]."月份"));
			$sheet->mergeCellsByColumnAndRow($col,1,$col+1,1);
			
			
			$sheet->getColumnDimensionByColumn($col)->setWidth(10);
			$sheet->getColumnDimensionByColumn($col+1)->setWidth(10);
			$sheet->getStyleByColumnAndRow($col,2)->getAlignment()->setWrapText(true);
			$sheet->setCellValueByColumnAndRow($col,2,iconv('GBK','UTF-8',"市场\n成交价"));
			$sheet->getStyleByColumnAndRow($col+1,2)->getAlignment()->setWrapText(true);
			$sheet->setCellValueByColumnAndRow($col+1,2,iconv('GBK','UTF-8',"与禹龙\n品牌价差"));


		}
		
		$row = 3;
		foreach($cityinfo as $city){
			$fcount = count($city['factory']);
			$sheet->setCellValueByColumnAndRow(0,$row,iconv('GBK','UTF-8',$city['name']));
			$sheet->mergeCellsByColumnAndRow(0,$row,0,$row+$fcount-1);

			$sheet->getStyleByColumnAndRow(1,$row)->getAlignment()->setWrapText(true);
			$sheet->setCellValueByColumnAndRow(1,$row,iconv('GBK','UTF-8',$var_info['mertail']."\n".$var_info['varname']));
			$sheet->mergeCellsByColumnAndRow(1,$row,1,$row+$fcount-1);

			$yulong_priceid = $city['factory'][0]['priceid'];
			foreach($city['factory'] as $key => $fac){
				$priceid = $fac['priceid'];
				$sheet->setCellValueByColumnAndRow(2,$row,iconv('GBK','UTF-8',$fac['factory_name']));
				for($i=1; $i<=12;$i++){
					$col = 1 + 2*$i;
					$pdata = $datas[$priceid][$i];
					$yulong_pdata = $datas[$yulong_priceid][$i];
					$fprice = "";
					$price_diff = "";
					if($pdata){
						$fprice = round($pdata['sum']/$pdata['count'],2);
						if($yulong_pdata){
							$yulong_price = round($yulong_pdata['sum']/$yulong_pdata['count'],2);;
							$price_diff = $fprice - $yulong_price;
						}
					}
					
					$sheet->setCellValueByColumnAndRow($col,$row,round($fprice));
					$sheet->setCellValueByColumnAndRow($col+1,$row,round($price_diff));
					
				}
				$row++;
			}
			
		}

		$styleThinBlackBorderOutline = array(
        	'borders' => array(
            	'allborders' => array( //设置全部边框
                	'style' => \PHPExcel_Style_Border::BORDER_THIN //粗的是thick
            	),

        	),
    	);
		$row = $row-1;
		$sheet->getStyle('A1:AA'.$row)->applyFromArray($styleThinBlackBorderOutline);
		$sheet->getStyle("A1:C".$row)->getFont()->setBold(true);
		$sheet->getStyle("A1:AA2")->getFont()->setBold(true);

		$file_name = iconv("GBK","UTF-8",$year."年陕钢禹龙产品实际成交价与其他钢厂品牌价差(".$var_info['varname'].")");
		header("Pragma: public");
		header("Expires: 0");
		header("Cache-Control:must-revalidate, post-check=0, pre-check=0");
		header("Content-Type:application/force-download");
		header("Content-Type:application/vnd.ms-execl");
		header("Content-Type:application/octet-stream");
		header("Content-Type:application/download");;
		header('Content-Disposition:attachment;filename="'.$file_name.'.xls"');
		header("Content-Transfer-Encoding:binary");
		
		$objWriter->save('php://output');		
	}
	
	public function yulong_price_diff($params)
	{
		$year = isset($params['year']) ? date("Y",strtotime($params['year'].'-01-01')) : date("Y");
		
		$priceidstr = "'1'";
		$ret_datas = array();
		$type = isset($params['type']) ? intval($params['type']) : 1;
		include_once("yulong_config.php");
		$varname_arr = array();
		foreach($var_type_array as $key => $v){
			$varname_arr[$key] = $v['var_info']['varname'];
		}
		
		$var_info = $var_type_array[$type]['var_info'];
		$city_array = $var_type_array[$type]['city_array'];
		foreach($city_array as $cid => $cinfo){
			$priceidstr = "'1'";
			foreach($cinfo['factory'] as $fac){
				$priceidstr.=",'".$fac['priceid']."'";
			}
			$sql = "select mastertopid as priceid, pricemk as price,mconmanagedate from marketconditions where mastertopid in ($priceidstr) and mconmanagedate>='$year-01-01' and mconmanagedate<='$year-12-31 23:59:59'";
			//echo $sql."<br />";
			$pdatas = $this->_dao->query($sql);
			$ret_datas = array_merge($ret_datas,$pdatas);
		}
		
		foreach($ret_datas as $row){
			$month = date("n",strtotime($row['mconmanagedate']));
			$price = $row['price'];
			$priceid = $row['priceid'];
			if(strpos($price,"-") !== false){
				list($min,$max) = explode("-",$price);
				$price = ($min + $max)/2;
			}
			
			$datas[$priceid][$month]['sum'] += $price;
			$datas[$priceid][$month]['count'] += 1;
		}

		if($params['isexport']==1){
			$this->export_yulong_price($year,$var_info,$city_array,$datas);
			exit;
		}

		$this->assign("year",$year);
		$this->assign("lnchar",$lnchar);
		$this->assign("type",$type);
		$this->assign("datas",$datas);
		$this->assign("cityinfo",$city_array);
		$this->assign("var_info",$var_info);
		$this->assign("varname_arr",$varname_arr);
		
	}
}

