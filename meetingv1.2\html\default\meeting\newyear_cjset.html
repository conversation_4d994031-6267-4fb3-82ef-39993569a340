
<!DOCTYPE html>
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta charset="utf-8">
	<title>抽奖推送+清空</title>
	<link rel="stylesheet" href="css/choujiang/main.css?t=0913"/>
	<script type="text/javascript" src="js/choujiang/jquery.2.1.4.js"></script>
	<script type="text/javascript" src="js/choujiang/ajax.js"></script>
	<script type="text/javascript" src="js/choujiang/base64.js"></script>
	<link rel="stylesheet" href="css/choujiang/newyear.css?t=0913"/>
	<script type="text/javascript">
		var mtid="<{$mtid}>";
	</script>
	<script type="text/javascript" src="js/choujiang/newyear.js"></script>

	<style type="text/css">
		body{background-image: url(images/choujiang/newyear/bodybg3.jpg);}
		.demo{ display: flex;justify-content: flex-start;width:980px; margin: 30px auto; text-align:left;height: 86px; padding:10px 10px 0 10px;}
		.btn{ background:none; cursor: pointer; color:#cc0000; text-shadow:0 1px 0 rgba(255,255,255,0.6); font:bold 20px/50px "Microsoft Yahei"; border:none transparent; outline:none;}
		.bt{ text-align:center;background:url("images/choujiang/btbj.png") no-repeat; width:154px; height:60px; margin:0 auto;}
		table td{font-size: 18px;color: #ffffff;}
		table td input{font-size: 18px;color: #333333;width: 300px;height: 25px;line-height: 25px;border: 1px solid #51555C ;border-radius: 10px; outline:none;}
		table td button{background: #ffff00;width: 120px;height: 30px;line-height: 30px;border-radius: 10px;outline: 0;border: 1px solid #ffff00;color: #FF1A08;font-weight: bold;font-size: 18px;margin-left: 35%;margin-top: 20px;}
		table td button:active{background: #FFB900;}
		.wrapper{margin-top: 100px;}
	</style>
</head>
<body>
	<div class="kong"></div>
	<h2 class="top_meeting"><{$meetingName}></h2>
	<br>
	<div class="wrapper">
		<div id="main">
			<div class="demo" >
				<div class="bt"><input onclick="tuisall();" type="button" class="btn" id="start" value="推送全部"></div>
				<div class="bt"><input onclick="tuis(4);" type="button" class="btn" id="start" value="推送特等奖"></div>
				<div class="bt"><input onclick="tuis(1);" type="button" class="btn" id="start" value="推送一等奖"></div>
				<div class="bt"><input onclick="tuis(2);" type="button" class="btn" id="start" value="推送二等奖"></div>
				<div class="bt"><input onclick="tuis(3);" type="button" class="btn" id="start" value="推送三等奖"></div>
			</div>
			<div class="demo" >
				<div class="bt"><input onclick="qkongall();" type="button" class="btn" id="start" value="清空全部"></div>
				<div class="bt"><input onclick="qkong(4);" type="button" class="btn" id="start" value="清空特等奖"></div>
				<div class="bt"><input onclick="qkong(1);" type="button" class="btn" id="start" value="清空一等奖"></div>
				<div class="bt"><input onclick="qkong(2);" type="button" class="btn" id="start" value="清空二等奖"></div>
				<div class="bt"><input onclick="qkong(3);" type="button" class="btn" id="start" value="清空三等奖"></div>
			</div>
		</div>
	</div>
</body>
</html>