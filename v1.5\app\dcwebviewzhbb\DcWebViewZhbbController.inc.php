<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );

class Dc<PERSON>ebViewZhbbController extends AbstractController{
  
  public function __construct(){
    parent::__construct();

	$this->_action->ngdao = new DcWebViewZhbbDao('DRCW') ;
	$this->_action->maindao = new DcWebViewZhbbDao('91R') ;
	$this->_action->maindao_test = new DcWebViewZhbbDao('91R') ;
	$this->_action->gcdao=new DcWebViewZhbbDao('GC');
	$this->_action->drcdao = new DcWebViewZhbbDao( 'DRC' );
	$this->_action->t1dao = new DcWebViewZhbbDao('MAIN') ;
  }

  public function _dopre(){
	  //echo "<pre/>";print_r($_SESSION);exit;
      //$this->_action->checkSession();
  }
	

  public function v_zhbb() {
	 
	$this->_action->zhbb($this->_request);
  }
  public function v_onezhbb() {
  
  	$this->_action->onezhbb($this->_request);
  }
  
  public function v_list() {
  
  	$this->_action->zhbblist($this->_request);
  }

  public function do_export() {
  
  	$this->_action->doexportdata($this->_request);
  }
  
}


?>