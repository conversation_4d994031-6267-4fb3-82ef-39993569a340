<?php
//require_once('../../steelconf_v3/debug.php');
class DcSystemDao extends Dao{

  public function __construct( $writer ){
    parent::__construct( $writer );
  }

   //取得GUID
  public function getGUID( $delminus = true ){
      $sGuid = $this->getOne( "SELECT uuid() AS guid" );
      if ($delminus){
         $sGuid =  str_replace("-", "", $sGuid);
      }
      return  $sGuid;
  }


  //根据用户名与设备标识，获取app_session
  public function getAppSession( $UserName, $SignCS ,$mc_type){
    //首先从缓存表取数据
    $row = $this->getRow( "SELECT * FROM app_session_temp WHERE UserName = '$UserName' AND SignCS = '$SignCS' AND mc_type='$mc_type'" );
    if (empty( $row )){
      //缓存表没有数据，则尝试从实体表取数据
      $row = $this->getRow( "SELECT * FROM app_session WHERE UserName = '$UserName' AND SignCS = '$SignCS' AND mc_type='$mc_type'" );
    }
    return $row;

  }
  //取得已登陆用户
  public function getUser( $GUID, $SignCS ,$mc_type ){
      return $this->getRow( "SELECT * FROM app_session_temp WHERE GUID = '$GUID' AND mc_type='$mc_type'" );
  }

  //日志记录
  public function WriteLog($Mid, $Uid, $SignCS, $ActionName,$Actionstr, $ActionIp, $SystemType, $SystemVersion, $MessageTitle='', $MessageId='',$MessageDesc='' ,$mc_type=0){
      $Actionstr = str_replace("'","\'",$Actionstr);
     $this->execute( "INSERT INTO app_logs SET Mid='$Mid', Uid='$Uid', SignCS='$SignCS', ActionName='$ActionName',Actionstr='$Actionstr', ActionDate=NOW(),ActionIp='$ActionIp', SystemType='$SystemType', SystemVersion='$SystemVersion',MessageId='$MessageId', MessageTitle='$MessageTitle', MessageDesc='$MessageDesc' , mc_type='$mc_type'");
  }

    //新增会员日志记录
    public function WriteMemberLog($GUID, $SignCS, $DTID1, $DTID1Name, $DTID1Sub, $DTID1SubName, $Data1Type, $DTID2, $DTID2Name, $DTID2Sub, $DTID2SubName, $Data2Type, $DTID3, $DTID3Name, $DTID3Sub, $DTID3SubName, $Data3Type, $DTID4, $DTID4Name, $DTID4Sub, $DTID4SubName, $Data4Type, $ImageType, $StartDate, $EndDate, $Mid, $ComName, $OpDesc, $whereFrom, $mc_type, $ActionName, $ActionIp, $Actionstr, $GongshiID1, $GongshiID2, $GongshiID3, $GongshiID4, $DTIDJson)
    {
        $DTIDJson = str_replace("'", "\'", $DTIDJson);
        $Actionstr = str_replace("'", "\'", $Actionstr);
		$ImageType=$ImageType!=''?$ImageType:'0';
		$StartDate=$StartDate!=''?$StartDate:date('Y-m-d');
		$startdatearr=explode('-',$StartDate);
		if(count($startdatearr)==1)
		{
			$StartDate=$startdatearr[0]."-01-01";
		}
		else if(count($startdatearr)==2)
		{
			$StartDate=$startdatearr[0]."-".$startdatearr[1]."-01";
		}
		$EndDate=$EndDate!=''?$EndDate:date('Y-m-d');
		$enddatearr=explode('-',$EndDate);
		if(count($enddatearr)==1)
		{
			$EndDate=$enddatearr[0]."-12-31";
		}
		else if(count($enddatearr)==2)
		{
			$EndDate=date('Y-m-t',strtotime($enddatearr[0]."-".$enddatearr[1]."-01"));
		}
		$whereFrom=$whereFrom!=''?$whereFrom:'0';
        $this->execute("INSERT INTO app_member_logs SET
  			GUID='$GUID',
  			SignCS='$SignCS',
  			DTID1='$DTID1',
  			DTID1Name='$DTID1Name',
  			DTID1Sub='$DTID1Sub',
  			DTID1SubName='$DTID1SubName',
  			Data1Type='$Data1Type',
  
  			DTID2='$DTID2',
  			DTID2Name='$DTID2Name',
  			DTID2Sub='$DTID2Sub',
  			DTID2SubName='$DTID2SubName',
  			Data2Type='$Data2Type',
  
  			DTID3='$DTID3',
  			DTID3Name='$DTID3Name',
  			DTID3Sub='$DTID3Sub',
  			DTID3SubName='$DTID3SubName',
  			Data3Type='$Data3Type',
  
  			DTID4='$DTID4',
  			DTID4Name='$DTID4Name',
  			DTID4Sub='$DTID4Sub',
  			DTID4SubName='$DTID4SubName',
  			Data4Type='$Data4Type',
  			ImageType='$ImageType',
  			StartDate='$StartDate',
  			EndDate='$EndDate',
  			Mid='$Mid',
  			ComName='$ComName',
  			OpDesc='$OpDesc',
			 DTIDJson='$DTIDJson',
  			OpDate=NOW(),
  			whereFrom='$whereFrom',
  			mc_type='$mc_type',
  			ActionName='$ActionName',
			GongshiID1='$GongshiID1',
			GongshiID2='$GongshiID2',
			GongshiID3='$GongshiID3',
			GongshiID4='$GongshiID4',


  			ActionDate=NOW(),
  			ActionIp='$ActionIp',
  			Actionstr='$Actionstr'");

    }


}

?>