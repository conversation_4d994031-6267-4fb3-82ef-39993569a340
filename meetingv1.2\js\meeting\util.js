    function getQueryString(name) {
       var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return decodeURI(r[2]);
        return null;
    }



    var format = function (time, format) {
        var t = new Date(time);
        var tf = function (i) {
            return (i < 10 ? '0' : '') + i
        };
        return format.replace(/yyyy|MM|dd|HH|mm|ss/g, function (a) {
            switch (a) {
                case 'yyyy':
                    return tf(t.getFullYear());
                    break;
                case 'MM':
                    return tf(t.getMonth() + 1);
                    break;
                case 'mm':
                    return tf(t.getMinutes());
                    break;
                case 'dd':
                    return tf(t.getDate());
                    break;
                case 'HH':
                    return tf(t.getHours());
                    break;
                case 'ss':
                    return tf(t.getSeconds());
                    break;
            }
        })
    }







    //控制点击频率 阻止tab点击过快
    function slowlyClick() {
        document.querySelector(".bottom").addEventListener('click', function () {
            document.querySelector(".bottom").style.pointerEvents = "none";
            setTimeout(() => {
                document.querySelector(".bottom").style.pointerEvents = "auto";
            }, 800);
        }, false);
    }

 // axios默认配置

    axios.defaults.timeout = 10000; // 超时时间
    axios.defaults.baseURL = ''
    axios.interceptors.response.use(response => {
        //console.log(response);
         //console.log(response.Success);
        if (response.data.hasOwnProperty("Success") && response.data.Success !== "1"&& response.data.Success !== 1) {
            let errorMessage = '';
            if (response.data.hasOwnProperty("Message")) {
                errorMessage = response.data.Message;
            } else if (response.data.hasOwnProperty("Message")) {
                errorMessage = response.data.Message;
            }
			alert(errorMessage);
           
            return
        } else {
            return response.data.data;
        }
    }, error => {
        //errorLogin("用户登录信息失效，请重新登录");
        return Promise.reject(error);
    });


    //获取数据视图
    function getViewData(opt) {
        //console.log(opt);
        var table = '';
        var series = opt.series;
        if (opt.xAxis[0].type === "time") {
            var totleArr = [];
            var newArrs = [];
            for (var i = 0; i < series.length; i++) {
                var arr = [];
                for (var j = 0; j < series[i].data.length; j++) {
                    arr.push(series[i].data[j][0]);
                }
                totleArr.push(arr);
            }
            for (var j = 0; j < totleArr.length; j++) {
                for (var s = 0; s < totleArr[j].length; s++) {
                    newArrs.push(totleArr[j][s]);
                }
            }
            var newdateArr = Array.from(new Set(newArrs));
            var newSeries = [];
            for (var j = 0; j < series.length; j++) {
                var dataArr = [];
                for (var i = 0; i < newdateArr.length; i++) {
                    var obj = [newdateArr[i], '-'];
                    for (var s = 0; s < series[j].data.length; s++) {
                        if (newdateArr[i] === series[j].data[s][0]) {
                            obj = [newdateArr[i], series[j].data[s][1]];
                        }
                    }
                    dataArr.push(obj);
                }
                series[j].newData = Array.from(new Set(dataArr));
            }
            //---------------------------------------------------------------------//
            var dataArr = [];
            table = '<table style="width:100%;text-align:center"><tbody><tr>' +
                '<td>时间</td>'
            for (var s = 0; s < series.length; s++) {
                table += '<td>' + series[s].name + '</td>'
            } +
            '</tr>';
            for (var i = 0, l = newdateArr.length; i < l; i++) {
                table += '<tr>' +
                    '<td>' + newdateArr[i] + '</td>'
                for (var s = 0; s < series.length; s++) {
                    table += '<td>' + series[s].newData[i][1] + '</td>'
                } +
                '</tr>';
            }
            table += '</tbody></table>';
        } else if (opt.xAxis[0].type === "category") {
            var axisData = opt.xAxis[0].data;
            table = '<table style="width:100%;text-align:center"><tbody><tr>' +
                '<td>时间</td>'
            for (var s = 0; s < series.length; s++) {
                table += '<td>' + series[s].name + '</td>'
            } +
            '</tr>';
            for (var i = 0, l = axisData.length; i < l; i++) {
                table += '<tr>' +
                    '<td>' + axisData[i] + '</td>'
                for (var s = 0; s < series.length; s++) {
                    var text = '';
                    if (Array.isArray(series[s].data[i])) {
                        if (series[s].data[i].length == 4) {
                            text = series[s].data[i][0] + "," + series[s].data[i][1] + "," + series[s].data[i][2] + "," + series[s].data[i][3];
                        } else {
                            text = series[s].data[i][1];
                        }
                    } else if (series[s].data[i] instanceof Object) {
                        text = series[s].data[i].value;
                    } else if (typeof series[s].data[i] == "string") {
                        text = series[s].data[i];
                    } else {
                        text = "-";
                    }
                    table += '<td>' + text + '</td>'
                } +
                '</tr>';
            }
            table += '</tbody></table>';
        }
        return table;
    };


    //获取 echart配置公共方法-按天 季节性分析
    function getOptionSeason(title, legend, series, charts) {
        //加水印代码
        // var canvas = document.createElement('canvas');
        // var ctx = canvas.getContext('2d');
        // canvas.width = charts._zr.dom.clientWidth;
        // canvas.height = charts._zr.dom.clientHeight;
        // var img = new Image();
        // img.addEventListener('load', function () {
        //     ctx.drawImage(img, (charts._zr.dom.clientWidth - 100) / 2, (charts._zr.dom.clientHeight - 100) / 2, 150, 65);
        // }, false);
        // img.src = "../images/mysteel-logo.png";
        // ctx.textAlign = 'center';
        // ctx.textBaseline = 'middle';
        // ctx.globalAlpha = 0.5;

        return {
            title: {
                text: title,
                textStyle: {
                    fontSize: 16
                },
                left: 'center',
                align: 'right',
                top: 10
            },
            // backgroundColor: {
            //     type: 'pattern',
            //     image: canvas,
            //     repeat: 'no-repeat'
            // },
            grid: {
                bottom: 95,
                left: 100,
                right: 50
            },
            toolbox: {
                feature: {
                    // dataView: {
                    //     show: true,
                    //     readOnly: true,
                    //     optionToContent: getViewData
                    // },
                    magicType: {
                        show: true,
                        type: ["line", "bar"]
                    },
                    restore: {
                        show: true
                    },
                    saveAsImage: {
                        show: true
                    }
                }
            },
            tooltip: {
                show: true,
                trigger: 'axis',
                axisPointer: {
                    type: 'cross',
                    animation: false,
                    label: {
                        backgroundColor: '#505765',
                        formatter: function (params) {
                            //console.log(params);
                            if (params.axisDimension == "x" && params.seriesData[0].value) {
                                return params.seriesData[0].value[0].substr(5, params.seriesData[0].value[0].length - 1);
                            } else if (params.axisDimension == "y") {
                                return Number(params.value).toFixed(2);
                            }
                        }
                    }
                },
                formatter: function (params, ticket, callback) {
                    //console.log(params);
                    var str = '';
                    for (var i = 0; i < params.length; i++) {
                        str += params[i].seriesName + "   " + params[i].value[1] + "<br/>"
                    }
                    return str;
                }
            },
            legend: {
                //width:'300',
                type: 'scroll',
                data: legend,
                left: 'center',
                top: '40'
            },
            xAxis: {
                type: 'time',
                splitLine: {
                    show: false
                },
                // minInterval:1,
                // maxInterval: 3600 * 24,
                scale: true,
                axisLabel: {
                    show: true,
                    //rotate: 50,
                    fontSize: 12,
                    formatter: function (value, index) {
                        var date = new Date(value);
                        var YY = date.getFullYear() + '-';
                        var MM = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
                        var DD = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate());
                        var hh = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
                        var mm = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':';
                        var ss = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds());
                        return MM + DD;
                    }
                }
            },
            yAxis: [{
                splitLine: {
                    show: false
                },
                //min:0,
                show: true,
                scale: true,
                name: '',
                nameLocation: 'end',
                axisLabel: {
                    //color: '#fff'
                },
                fontSize: 12,
                nameTextStyle: {
                    //color: '#fff'
                }
            }, {
                splitLine: {
                    show: false
                },
                //min:0,
                show: true,
                scale: true,
                //offset: 10,
                name: '',
                nameLocation: 'end',
                axisLabel: {
                    //color: '#fff'
                }
            }],
            dataZoom: [{
                    type: 'slider',
                    show: true,
                    start: 0,
                    end: 100,
                    handleSize: 8
                },
                {
                    type: 'inside',
                    start: 0,
                    end: 100
                }
            ],
            series: series
        };
    };

    //获取 echart配置公共方法-按月
    function getOption00(title, legend, series, charts) {
        //加水印代码
        // var canvas = document.createElement('canvas');
        // var ctx = canvas.getContext('2d');
        // canvas.width = charts._zr.dom.clientWidth;
        // canvas.height = charts._zr.dom.clientHeight;
        // var img = new Image();
        // img.addEventListener('load', function () {
        //     ctx.drawImage(img, (charts._zr.dom.clientWidth - 100) / 2, (charts._zr.dom.clientHeight - 100) / 2, 150, 65);
        // }, false);
        // img.src = "../images/mysteel-logo.png";
        // ctx.textAlign = 'center';
        // ctx.textBaseline = 'middle';
        // ctx.globalAlpha = 0.5;

        return {
            title: {
                text: title,
                textStyle: {
                    fontSize: 16
                },
                left: 'center',
                align: 'right',
                top: 10
            },
            // backgroundColor: {
            //     type: 'pattern',
            //     image: canvas,
            //     repeat: 'no-repeat'
            // },
            grid: {
                bottom: 105,
                left: 100,
                right: 50
            },
            toolbox: {
                feature: {
                    // dataView: {
                    //     show: true,
                    //     readOnly: true,
                    //     optionToContent: getViewData
                    // },
                    magicType: {
                        show: true,
                        type: ["line", "bar"]
                    },
                    restore: {
                        show: true
                    },
                    saveAsImage: {
                        show: true
                    }
                }
            },
            tooltip: {
                show: true,
                trigger: 'axis',
                axisPointer: {
                    type: 'cross',
                    animation: false,
                    label: {
                        backgroundColor: '#505765',
                        formatter: function (params) {
                            //console.log(params);
                            if (params.axisDimension == "x" && params.seriesData[0].value) {
                                return params.seriesData[0].value[0].substr(5, params.seriesData[0].value[0].length - 1);
                            } else if (params.axisDimension == "y") {
                                return Number(params.value).toFixed(2);
                            }
                        }
                    }
                },
                formatter: function (params, ticket, callback) {
                    //console.log(params); value[0].substr(5,params[i].value[0].length-1)
                    var str = '';
                    for (var i = 0; i < params.length; i++) {
                        str += params[i].seriesName + " " + params[i].value[1] + "<br/>"
                    }
                    return str;
                }
            },
            legend: {
                //width:'300',
                type: 'scroll',
                data: legend,
                left: 'center',
                top: '40'
            },
            xAxis: {
                type: 'time',
                splitLine: {
                    show: false
                },
                // minInterval:1,
                // maxInterval: 3600 * 24,
                scale: true,
                axisLabel: {
                    show: true,
                    //rotate: 50,
                    fontSize: 12,
                    formatter: function (value, index) {
                        var date = new Date(value);
                        var YY = date.getFullYear() + '-';
                        var MM = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
                        var DD = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate());
                        var hh = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
                        var mm = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':';
                        var ss = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds());
                        return MM + DD;
                    }
                }
            },
            yAxis: [{
                    splitLine: {
                        show: false
                    },
                    //min:0,
                    show: true,
                    scale: true,
                    name: '',
                    nameLocation: 'end',
                    axisLabel: {
                        //color: '#fff'
                    },
                    fontSize: 12,
                    nameTextStyle: {
                        //color: '#fff'
                    }
                },
                {
                    splitLine: {
                        show: false
                    },
                    //min:0,
                    show: true,
                    scale: true,
                    //offset: 10,
                    name: '',
                    nameLocation: 'end',
                    axisLabel: {
                        //color: '#fff'
                    }
                }
            ],
            dataZoom: [{
                    type: 'slider',
                    show: true,
                    start: 0,
                    end: 100,
                    handleSize: 8
                },
                {
                    type: 'inside',
                    start: 0,
                    end: 100
                }
            ],
            series: series
        };
    };
    //获取 echart配置公共方法-按月
    function getOption0(title, legend, series, types, charts) {
        //加水印代码
        // var canvas = document.createElement('canvas');
        // var ctx = canvas.getContext('2d');
        // canvas.width = charts._zr.dom.clientWidth;
        // canvas.height = charts._zr.dom.clientHeight;
        // var img = new Image();
        // img.addEventListener('load', function () {
        //     ctx.drawImage(img, (charts._zr.dom.clientWidth - 100) / 2, (charts._zr.dom.clientHeight - 100) / 2, 150, 65);
        // }, false);
        // img.src = "../images/mysteel-logo.png";
        // ctx.textAlign = 'center';
        // ctx.textBaseline = 'middle';
        // ctx.globalAlpha = 0.5;

        var flage = false;
        if (types || types == "bug") {
            flage = true;
        }
        return {
            title: {
                text: title,
                textStyle: {
                    fontSize: 16
                },
                left: 'center',
                align: 'right',
                top: 10
            },
            // backgroundColor: {
            //     type: 'pattern',
            //     image: canvas,
            //     repeat: 'no-repeat'
            // },
            grid: {
                bottom: 70,
                left: 100,
                right: 70
            },
            toolbox: {
                feature: {
                    // dataView: {
                    //     show: true,
                    //     readOnly: true,
                    //     optionToContent: getViewData
                    // },
                    magicType: {
                        show: true,
                        type: ["line", "bar"]
                    },
                    restore: {
                        show: true
                    },
                    saveAsImage: {
                        show: true
                    }
                }
            },
            tooltip: {
                show: true,
                trigger: 'axis',
                axisPointer: {
                    type: 'cross',
                    animation: false,
                    label: {
                        backgroundColor: '#505765',
                        formatter: function (params) {
                            //console.log(params);
                            if (params.axisDimension == "x" && params.seriesData[0].value) {
                                return params.seriesData[0].value[0];
                            } else if (params.axisDimension == "y") {
                                return Number(params.value).toFixed(2);
                            }
                        }
                    }
                },
                formatter: function (params, ticket, callback) {
                    var str = '';
                    for (var i = 0; i < params.length; i++) {
                        if (flage) {
                            str += params[i].seriesName + "   " + params[i].value[1] + "<br/>"
                        } else {
                            str += params[i].seriesName + "   " + params[i].value[1] + "<br/>"
                        }
                    }
                    return str;
                }
            },
            legend: {
                //width:'300',
                type: 'scroll',
                data: legend,
                left: 'center',
                top: '40'
            },
            xAxis: {
                type: 'time',
                splitLine: {
                    show: false
                },
                // minInterval:1,
                // maxInterval: 3600 * 24,
                scale: true,
                axisLabel: {
                    show: true,
                    //rotate: 50,
                    fontSize: 12,
                    formatter: function (value, index) {
                        var date = new Date(value);
                        var YY = date.getFullYear() + '-';
                        var MM = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
                        var DD = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate());
                        var hh = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
                        var mm = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':';
                        var ss = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds());
                        return YY + MM + DD;
                    }
                }
            },
            yAxis: [{
                    splitLine: {
                        show: false
                    },
                    //min:0,
                    show: true,
                    scale: true,
                    name: '',
                    nameLocation: 'end',
                    axisLabel: {
                        //color: '#fff'
                    },
                    fontSize: 12,
                    nameTextStyle: {
                        //color: '#fff'
                    }
                },
                {
                    splitLine: {
                        show: false
                    },
                    //min:0,
                    show: true,
                    scale: true,
                    //offset: 10,
                    name: '',
                    nameLocation: 'end',
                    axisLabel: {
                        //color: '#fff'
                    }
                }
            ],
            dataZoom: [{
                    zoomOnMouseWheel: false,
                    type: 'slider',
                    show: true,
                    start: 0,
                    end: 100,
                    handleSize: 8
                },
                {
                    type: 'inside',
                    start: 0,
                    end: 100
                }
            ],
            series: series
        };
    };

    //获取 echart配置公共方法-按月
    function getOptionSeasionS(title, legend, series, charts) {
        //加水印代码
        // var canvas = document.createElement('canvas');
        // var ctx = canvas.getContext('2d');
        // canvas.width = charts._zr.dom.clientWidth;
        // canvas.height = charts._zr.dom.clientHeight;
        // var img = new Image();
        // img.addEventListener('load', function () {
        //     ctx.drawImage(img, (charts._zr.dom.clientWidth - 100) / 2, (charts._zr.dom.clientHeight - 100) / 2, 150, 65);
        // }, false);
        // img.src = "../images/mysteel-logo.png";
        // ctx.textAlign = 'center';
        // ctx.textBaseline = 'middle';
        // ctx.globalAlpha = 0.5;

        return {
            title: {
                text: title,
                textStyle: {
                    fontSize: 16
                },
                left: 'center',
                align: 'right',
                top: 10
            },
            // backgroundColor: {
            //     type: 'pattern',
            //     image: canvas,
            //     repeat: 'no-repeat'
            // },
            grid: {
                bottom: 70,
                left: 100,
                right: 50
            },
            toolbox: {
                feature: {
                    // dataView: {
                    //     show: true,
                    //     readOnly: true,
                    //     optionToContent: getViewData
                    // },
                    magicType: {
                        show: true,
                        type: ["line", "bar"]
                    },
                    restore: {
                        show: true
                    },
                    saveAsImage: {
                        show: true
                    }
                }
            },
            tooltip: {
                show: true,
                trigger: 'axis',
                axisPointer: {
                    type: 'cross',
                    animation: false,
                    label: {
                        backgroundColor: '#505765',
                        formatter: function (params) {
                            //console.log(params);
                            if (params.axisDimension == "x" && params.seriesData[0].value) {
                                return params.seriesData[0].value[0];
                            } else if (params.axisDimension == "y") {
                                return Number(params.value).toFixed(2);
                            }
                        }
                    }
                },
                formatter: function (params, ticket, callback) {
                    //console.log(params);
                    var str = '';
                    for (var i = 0; i < params.length; i++) {
                        str += params[i].seriesName + "   " + params[i].value[1] + "<br/>"
                    }
                    return str;
                }
            },
            legend: {
                //width:'300',
                type: 'scroll',
                data: legend,
                left: 'center',
                top: '40'
            },
            xAxis: {
                type: 'time',
                splitLine: {
                    show: false
                },
                // minInterval:1,
                // maxInterval: 3600 * 24,
                scale: true,
                axisLabel: {
                    show: true,
                    //rotate: 50,
                    fontSize: 12,
                    formatter: function (value, index) {
                        var date = new Date(value);
                        var YY = date.getFullYear() + '-';
                        var MM = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
                        var DD = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate());
                        var hh = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
                        var mm = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':';
                        var ss = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds());
                        return YY + MM + DD;
                    }
                }
            },
            yAxis: [{
                    splitLine: {
                        show: false
                    },
                    //min:0,
                    show: true,
                    scale: true,
                    name: '',
                    nameLocation: 'end',
                    axisLabel: {
                        //color: '#fff'
                    },
                    fontSize: 12,
                    nameTextStyle: {
                        //color: '#fff'
                    }
                },
                {
                    splitLine: {
                        show: false
                    },
                    //min:0,
                    show: true,
                    scale: true,
                    //offset: 10,
                    name: '',
                    nameLocation: 'end',
                    axisLabel: {
                        //color: '#fff'
                    }
                }
            ],
            dataZoom: [{
                    type: 'slider',
                    show: true,
                    start: 0,
                    end: 100,
                    handleSize: 8
                },
                {
                    type: 'inside',
                    start: 0,
                    end: 100
                }
            ],
            series: series
        };
    };

    //获取 echart配置公共方法-按月
    function getOption1(title, legend, series, types, charts) {
        //加水印代码
        // var canvas = document.createElement('canvas');
        // var ctx = canvas.getContext('2d');
        // canvas.width = charts._zr.dom.clientWidth;
        // canvas.height = charts._zr.dom.clientHeight;
        // var img = new Image();
        // img.addEventListener('load', function () {
        //     ctx.drawImage(img, (charts._zr.dom.clientWidth - 100) / 2, (charts._zr.dom.clientHeight - 100) / 2, 150, 65);
        // }, false);
        // img.src = "../images/mysteel-logo.png";
        // ctx.textAlign = 'center';
        // ctx.textBaseline = 'middle';
        // ctx.globalAlpha = 0.5;

        var flage = false;
        if (types || types == "bug") {
            flage = true;
        }
        return {
            title: {
                text: title,
                textStyle: {
                    fontSize: 16
                },
                left: 'center',
                align: 'right',
                top: 10
            },
            // backgroundColor: {
            //     type: 'pattern',
            //     image: canvas,
            //     repeat: 'no-repeat'
            // },
            grid: {
                bottom: 66,
                left: 100,
                right: 50
            },
            toolbox: {
                feature: {
                    // dataView: {
                    //     show: true,
                    //     readOnly: true,
                    //     optionToContent: getViewData
                    // },
                    magicType: {
                        show: true,
                        type: ["line", "bar"]
                    },
                    restore: {
                        show: true
                    },
                    saveAsImage: {
                        show: true
                    }
                }
            },
            tooltip: {
                show: true,
                trigger: 'axis',
                axisPointer: {
                    type: 'cross',
                    animation: false,
                    label: {
                        backgroundColor: '#505765',
                        formatter: function (params) {
                            //console.log(params);
                            if (params.axisDimension == "x" && params.seriesData[0].value) {
                                return params.seriesData[0].value[0].substr(0, 7);
                            } else if (params.axisDimension == "y") {
                                return Number(params.value).toFixed(2);
                            }
                        }
                    }
                },
                formatter: function (params, ticket, callback) {
                    var str = '';
                    for (var i = 0; i < params.length; i++) {
                        if (flage) {
                            str += params[i].seriesName + "   " + params[i].value[1] + "<br/>"
                        } else {
                            str += params[i].value[0].substr(0, 7) + "   " + params[i].value[1] + "<br/>"
                        }
                    }
                    return str;
                }
            },
            legend: {
                //width:'300',
                type: 'scroll',
                data: legend,
                left: 'center',
                top: '40'
            },
            xAxis: {
                type: 'time',
                splitLine: {
                    show: false
                },
                // minInterval:1,
                // maxInterval: 3600 * 24,
                scale: true,
                axisLabel: {
                    show: true,
                    //rotate: 50,
                    fontSize: 12,
                    formatter: function (value, index) {
                        var date = new Date(value);
                        var YY = date.getFullYear() + '-';
                        var MM = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '';
                        var DD = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate());
                        var hh = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
                        var mm = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':';
                        var ss = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds());
                        return YY + MM;
                    }
                }
            },
            yAxis: [{
                    splitLine: {
                        show: false
                    },
                    //min:0,
                    show: true,
                    scale: true,
                    name: '',
                    nameLocation: 'end',
                    axisLabel: {
                        //color: '#fff'
                    },
                    fontSize: 12,
                    nameTextStyle: {
                        //color: '#fff'
                    }
                },
                {
                    splitLine: {
                        show: false
                    },
                    //min:0,
                    show: true,
                    scale: true,
                    //offset: 10,
                    name: '',
                    nameLocation: 'end',
                    axisLabel: {
                        //color: '#fff'
                    }
                }
            ],
            dataZoom: [{
                    type: 'slider',
                    show: true,
                    start: 0,
                    end: 100,
                    handleSize: 8
                },
                {
                    type: 'inside',
                    start: 0,
                    end: 100
                }
            ],
            series: series
        };
    };

    //获取 echart配置公共方法-按天
    function getOptionDay2(title, legend, series, charts) {
        //加水印代码
        // var canvas = document.createElement('canvas');
        // var ctx = canvas.getContext('2d');
        // canvas.width = charts._zr.dom.clientWidth;
        // canvas.height = charts._zr.dom.clientHeight;
        // var img = new Image();
        // img.addEventListener('load', function () {
        //     ctx.drawImage(img, (charts._zr.dom.clientWidth - 100) / 2, (charts._zr.dom.clientHeight - 100) / 2, 150, 65);
        // }, false);
        // img.src = "../images/mysteel-logo.png";
        // ctx.textAlign = 'center';
        // ctx.textBaseline = 'middle';
        // ctx.globalAlpha = 0.5;

        return {
            title: {
                text: title,
                textStyle: {
                    fontSize: 16
                },
                left: 'center',
                align: 'right',
                top: 10
            },
            // backgroundColor: {
            //     type: 'pattern',
            //     image: canvas,
            //     repeat: 'no-repeat'
            // },
            grid: {
                bottom: 80,
                left: 100,
                right: 100
            },
            toolbox: {
                feature: {
                    // dataView: {
                    //     show: true,
                    //     readOnly: true,
                    //     optionToContent: getViewData
                    // },
                    magicType: {
                        show: true,
                        type: ["line", "bar"]
                    },
                    restore: {
                        show: true
                    },
                    saveAsImage: {
                        show: true
                    }
                },
                top: 30
            },
            tooltip: {
                show: true,
                trigger: 'axis',
                axisPointer: {
                    type: 'cross',
                    animation: false,
                    label: {
                        backgroundColor: '#505765',
                        formatter: function (params) {
                            //console.log(params);
                            if (params.axisDimension == "x" && params.seriesData[0].value) {
                                return params.seriesData[0].value[0];
                            } else if (params.axisDimension == "y") {
                                return Number(params.value).toFixed(2);
                            }
                        }
                    }
                },
                formatter: function (params, ticket, callback) {
                    var str = '';
                    for (var i = 0; i < params.length; i++) {
                        str += params[i].value[0] + "   " + params[i].value[1] + "<br/>"
                    }
                    return str;
                }
            },
            legend: {
                //width:'300',
                type: 'scroll',
                data: legend,
                left: 'center',
                top: '40'
            },
            xAxis: {
                type: 'time',
                splitLine: {
                    show: false
                },
                // minInterval:1,
                // maxInterval: 3600 * 24,
                scale: true,
                axisLabel: {
                    show: true,
                    //rotate: 50,
                    fontSize: 12,
                    formatter: function (value, index) {
                        var date = new Date(value);
                        var YY = date.getFullYear() + '-';
                        var MM = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
                        var DD = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate());
                        var hh = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
                        var mm = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':';
                        var ss = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds());
                        return YY + MM + DD;
                    }
                }
            },
            yAxis: [{
                    splitLine: {
                        show: false
                    },
                    //min:0,
                    show: true,
                    scale: true,
                    name: '',
                    nameLocation: 'end',
                    axisLabel: {
                        //color: '#fff'
                    },
                    fontSize: 12,
                    nameTextStyle: {
                        //color: '#fff'
                    }
                },
                {
                    splitLine: {
                        show: false
                    },
                    //min:0,
                    show: true,
                    scale: true,
                    //offset: 10,
                    name: '',
                    nameLocation: 'end',
                    axisLabel: {
                        //color: '#fff'
                    }
                }
            ],
            dataZoom: [{
                    type: 'slider',
                    show: true,
                    start: 0,
                    end: 100,
                    handleSize: 8
                },
                {
                    type: 'inside',
                    start: 0,
                    end: 100
                }
            ],
            series: series
        };
    };
	function fontSize(res){
	  let docEl = document.documentElement,
		clientWidth = window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth;
	  if (!clientWidth) return;
	  let fontSize = 100 * (clientWidth / 1920);
	  //console.log(fontSize);
	  return res*fontSize;
	 
	}

//获取 echart配置公共方法-按天
    function getOptionDay_xbnew(title, legend, series, types, charts,istry,colortype=0,isxs=0) {
        //加水印代码
        // var canvas = document.createElement('canvas');
        // var ctx = canvas.getContext('2d');
        // canvas.width = charts._zr.dom.clientWidth;
        // canvas.height = charts._zr.dom.clientHeight;
        // var img = new Image();
        // img.addEventListener('load', function () {
        //     ctx.drawImage(img, (charts._zr.dom.clientWidth - 100) / 2, (charts._zr.dom.clientHeight - 100) / 2, 150, 65);
        // }, false);
        // img.src = "../images/mysteel-logo.png";
        // ctx.textAlign = 'center';
        // ctx.textBaseline = 'middle';
        // ctx.globalAlpha = 0.5;
		//console.log("111111:",series);
		var dataamount=legend.length>0?series[0].data.length:0;
		//console.log(dataamount);
		var datenum=6;
		var jg=(dataamount-1)/datenum;
		jg=parseInt(jg);
		var leftjg=dataamount-jg*datenum;
        var colors = colortype==0?['#00F5FF', '#F9AF1A', '#FB4D4B','#E87C25','#27727B','#FE8463','#9BCA63','#FAD860','#F3A43B','#60C0DD']:colortype==1?[ '#F9AF1A', '#FB4D4B','#00F5FF','#E87C25','#27727B','#FE8463','#9BCA63','#FAD860','#F3A43B','#60C0DD']:[ '#FB4D4B','#00F5FF', '#F9AF1A','#E87C25','#27727B','#FE8463','#9BCA63','#FAD860','#F3A43B','#60C0DD'];
        var flage = false;
        if (types || types == "bug") {
            flage = true;
        }
		
        return {
			 noDataLoadingOption: {
	                        text: '暂无数据',
	                        effect: 'bubble',
	                      
	        },
			color:colors,
            title: {
                text: title,
					subtext: '数据来源：钢之家数据中心 www.steelhome.cn/data',
					subtextStyle:{
						color:'#9D9D9D',
						align:'right',
						baseline:'bottom',
						fontFamily:'SimSun',
						fontSize:11
					},
					itemGap:10,
					padding:5,
					textStyle:{color:'black',fontWeight:'normal',fontFamily:'SimHei'},
					x: 'center'
            },
            // backgroundColor: {
            //     type: 'pattern',
            //     image: canvas,
            //     repeat: 'no-repeat'
            // },
            grid: {
				top:40,
                //bottom: document.body.clientWidth>=maxwidthscen?30:55,
				bottom:60,
                left: 50,
                right: 50
            },
            //toolbox: {
             //   feature: {
                    // dataView: {
                    //     show: true,
                    //     readOnly: true,
                    //     optionToContent: getViewData
                    // },
             //       magicType: {
              //          show: true,
             //           type: ["line", "bar"]
             //       },
              //      restore: {
              //          show: true
             //       },
             //       saveAsImage: {
             //           show: true
             //       }
             //   }
            //},
            tooltip: {
                show: true,
                trigger: 'axis',
				confine:true,
                //axisPointer: {
                  //  type: 'cross',
                   /*  animation: false,
                    label: {
                        backgroundColor: '#505765',
                        formatter: function (params) {
                            console.log(params.axisDimension);
                            if (params.axisDimension == "x" && params.seriesData[0].value) {
                                return params.seriesData[0].value[0];
                            } else if (params.axisDimension == "y") {
                                return Number(params.value).toFixed(2);
                            }
                        }
                    } */
               // },
                formatter: function (params, ticket, callback) {
                    //console.log(params);
                    //var str = '';
					var str = params[0].name+'<br/>';
                    for (var i = 0; i < params.length; i++) {
                        if (flage) {
                            str += params[i].seriesName + "   " + (istry=="1"?'***':params[i].data) + "<br/>"
                        } else {
                            str += params[i].value[0] + "   " + (istry=="1"?'***':params[i].value[1]) + "<br/>"
                        }

                    }
                    return str;
                }
            },
            legend: {
                //width:'300',
                type: 'scroll',
                data: legend,
                //left: 'center',
                //right:'50',
                //top: '10',
				textStyle:{//图例文字的样式
					color:'#3333333',
					//fontSize:16
                },
				orient:'horizontal',
				itemGap:18,
				y:'bottom',
				
            },
            xAxis: {
                type: 'category',
                splitLine: {
                    show: false
                },
                // minInterval:1,
                // maxInterval: 3600 * 24,
                //scale: true,
                axisLabel: {
					//borderWidth:0,
					showMaxLabel: true,
                    show: true,
                    //rotate: document.body.clientWidth>=maxwidthscen?0:50,
                    fontSize: fontSize(0.11),
					color: '#3FDDFF',
					//interval:(i,v)=> true,    //强制文字产生间隔
					//rotate: 45, 
					interval:function(index,data)
					{
						//console.log('index:'+index);
						//console.log('data:'+data);
						// var dataamount=251;
						// var datenum=6;
						// var jg=(dataamount-1)/datenum;
						// jg=parseInt(jg);
						// var leftjg=dataamount-jg*datenum;
						if(dataamount<=7||index==dataamount-1||(index-leftjg+1>=0&&(index-leftjg+1)%jg==0)){ 
							//console.log('index:'+index);
							//if(index/jg>=6)return false;
							return true;
						}
					},
                    formatter: function (value, index) {
						//console.log(value);
						// if(value=='进口矿')
						// {
						// 	value=value+'\n（美元/吨）';
						// }
                        if(isxs==1)
                        {
                            var str = value.split("");
                            return str.join("\n");
                        }
                        else
                        {
                            return value;
                        }
                       
						
						//return value;
                        /* var date = new Date(value);
                        var YY = date.getFullYear() + '-';
                        var MM = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
                        var DD = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate());
                        var hh = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
                        var mm = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':';
                        var ss = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds());
                        return YY + MM + DD; */
                    }
                },
				axisTick:{  
							alignWithLabel: true,
							textStyle:{
								fontSize:11
							},
							// 坐标轴小标记
							show: true       // 属性show控制显示与否，默认不显示
							,interval:function(index,data){
								//console.log('index:'+index);
								//console.log('data:'+data);
								
								
								if(dataamount<=7||index==dataamount-1||(index-leftjg+1>=0&&(index-leftjg+1)%jg==0)){ 
								 //console.log('index111:'+index);
								// console.log('dataamount:'+index);
								// console.log('jg:'+jg);
								// console.log('index/jg:'+index/jg);
								// console.log('dataamount/jg:'+dataamount/jg);
								//if(index/jg>=parseInt(dataamount-1/jg))return false;
									return true;
								}
								
								//else console.log(index+':'+data);
							}
				},	
				splitLine:false,
				boundaryGap: true,
				axisLine:{
					onZero:false,
					lineStyle:{
						color:'#202F55'
					}
				}
            },
            yAxis: [{
				    //splitNumber:3,
                    splitLine: {
                        show: true,
						lineStyle:{
						color: ['#202F55']
					  }
                    },
                    //min:0,
                    show: true,
                    scale: true,
                    name: '',
                    nameLocation: 'end',
                    axisLabel: {
                        color: '#3FDDFF',
						 textStyle: {
							color: '#3FDDFF'
						},
						formatter: function (value, index) 
						{
							//console.log(value);
							return value;
						}
                    },
                    fontSize: 11,
                    nameTextStyle: {
                        color: '#3FDDFF'
                    },
					axisLine:{
					lineStyle:{
						color:'#202F55'
					}
				   }
                },{
                    splitLine: {
                        show: true,
						lineStyle:{
						color: ['#202F55']
					  }
                    },
                    //min:0,
                    show: true,
                    scale: true,
                    name: '',
                    nameLocation: 'end',
                    axisLabel: {
                        color: '#3FDDFF',
						 textStyle: {
							color: '#3FDDFF'
						},
						formatter: function (value, index) 
						{
							//console.log(value);
							return value;
						}
                    },
                    fontSize: 11,
                    nameTextStyle: {
                        color: '#3FDDFF'
                    },
					axisLine:{
					lineStyle:{
						color:'#202F55'
					}
				   }
                }
                
            ],
            series: series
        };
    };




    //获取 echart配置公共方法-按天
    function getOptionDay_new(title, legend, series, types, charts,colortype=0,isxs=0) {
        //加水印代码
        // var canvas = document.createElement('canvas');
        // var ctx = canvas.getContext('2d');
        // canvas.width = charts._zr.dom.clientWidth;
        // canvas.height = charts._zr.dom.clientHeight;
        // var img = new Image();
        // img.addEventListener('load', function () {
        //     ctx.drawImage(img, (charts._zr.dom.clientWidth - 100) / 2, (charts._zr.dom.clientHeight - 100) / 2, 150, 65);
        // }, false);
        // img.src = "../images/mysteel-logo.png";
        // ctx.textAlign = 'center';
        // ctx.textBaseline = 'middle';
        // ctx.globalAlpha = 0.5;
        var colors = colortype==0?['#00F5FF', '#F9AF1A', '#FB4D4B']:colortype==1?[ '#F9AF1A', '#FB4D4B','#00F5FF']:[ '#FB4D4B','#00F5FF', '#F9AF1A'];
        var flage = false;
        if (types || types == "bug") {
            flage = true;
        }
		
        return {
			 noDataLoadingOption: {
	                        text: '暂无数据',
	                        effect: 'bubble',
	                      
	         },
			color:colors,
            title: {
                text: title,
                textStyle: {
                    fontSize: 16
                },
                left: 'center',
                align: 'right',
                //top: 10
            },
            // backgroundColor: {
            //     type: 'pattern',
            //     image: canvas,
            //     repeat: 'no-repeat'
            // },
            grid: {
				top:40,
                //bottom: document.body.clientWidth>=maxwidthscen?30:55,
				bottom:60,
                left: 50,
                right: 50
            },
            //toolbox: {
             //   feature: {
                    // dataView: {
                    //     show: true,
                    //     readOnly: true,
                    //     optionToContent: getViewData
                    // },
             //       magicType: {
              //          show: true,
             //           type: ["line", "bar"]
             //       },
              //      restore: {
              //          show: true
             //       },
             //       saveAsImage: {
             //           show: true
             //       }
             //   }
            //},
            tooltip: {
                show: true,
                trigger: 'axis',
                //axisPointer: {
                  //  type: 'cross',
                   /*  animation: false,
                    label: {
                        backgroundColor: '#505765',
                        formatter: function (params) {
                            console.log(params.axisDimension);
                            if (params.axisDimension == "x" && params.seriesData[0].value) {
                                return params.seriesData[0].value[0];
                            } else if (params.axisDimension == "y") {
                                return Number(params.value).toFixed(2);
                            }
                        }
                    } */
               // },
                formatter: function (params, ticket, callback) {
                    //console.log(params);
                    var str = '';
                    for (var i = 0; i < params.length; i++) {
                        if (flage) {
                            str += params[i].seriesName + "   " + params[i].value[1] + "<br/>"
                        } else {
                            str += params[i].value[0] + "   " + params[i].value[1] + "<br/>"
                        }

                    }
                    return str;
                }
            },
            legend: {
                //width:'300',
                type: 'scroll',
                data: legend,
                //left: 'center',
                right:'50',
                top: '10',
				textStyle:{//图例文字的样式
					color:'#3333333',
					//fontSize:16
                }
            },
            xAxis: {
                type: 'category',
                splitLine: {
                    show: false
                },
                // minInterval:1,
                // maxInterval: 3600 * 24,
                //scale: true,
                axisLabel: {
					//borderWidth:0,
                    show: true,
                    //rotate: document.body.clientWidth>=maxwidthscen?0:50,
                    fontSize: fontSize(0.11),
					color: '#3FDDFF',
					interval:(i,v)=> true,    //强制文字产生间隔
					//rotate: 45,  
                    formatter: function (value, index) {
						//console.log(value);
						// if(value=='进口矿')
						// {
						// 	value=value+'\n（美元/吨）';
						// }
                        if(isxs==1)
                        {
                            var str = value.split("");
                            return str.join("\n");
                        }
                        else
                        {
                            return value;
                        }
                       
						
						//return value;
                        /* var date = new Date(value);
                        var YY = date.getFullYear() + '-';
                        var MM = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
                        var DD = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate());
                        var hh = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
                        var mm = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':';
                        var ss = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds());
                        return YY + MM + DD; */
                    }
                },
				axisLine:{
					lineStyle:{
						color:'#202F55'
					}
				}
            },
            yAxis: [{
				    //splitNumber:3,
                    splitLine: {
                        show: true,
						lineStyle:{
						color: ['#202F55']
					  }
                    },
                    //min:0,
                    show: true,
                    scale: true,
                    name: '',
                    nameLocation: 'end',
                    axisLabel: {
                        color: '#3FDDFF',
						 textStyle: {
							color: '#3FDDFF'
						},
						formatter: function (value, index) 
						{
							//console.log(value);
							return value;
						}
                    },
                    fontSize: 11,
                    nameTextStyle: {
                        color: '#3FDDFF'
                    },
					axisLine:{
					lineStyle:{
						color:'#202F55'
					}
				   }
                },{
                    splitLine: {
                        show: true,
						lineStyle:{
						color: ['#202F55']
					  }
                    },
                    //min:0,
                    show: true,
                    scale: true,
                    name: '',
                    nameLocation: 'end',
                    axisLabel: {
                        color: '#3FDDFF',
						 textStyle: {
							color: '#3FDDFF'
						},
						formatter: function (value, index) 
						{
							//console.log(value);
							return value;
						}
                    },
                    fontSize: 11,
                    nameTextStyle: {
                        color: '#3FDDFF'
                    },
					axisLine:{
					lineStyle:{
						color:'#202F55'
					}
				   }
                }
                
            ],
            series: series
        };
    };


//获取 echart配置公共方法-按天
function getOptionDay_new_lwg(title, legend, series, types, charts,colortype=0,isxs=0) {
    //加水印代码
    // var canvas = document.createElement('canvas');
    // var ctx = canvas.getContext('2d');
    // canvas.width = charts._zr.dom.clientWidth;
    // canvas.height = charts._zr.dom.clientHeight;
    // var img = new Image();
    // img.addEventListener('load', function () {
    //     ctx.drawImage(img, (charts._zr.dom.clientWidth - 100) / 2, (charts._zr.dom.clientHeight - 100) / 2, 150, 65);
    // }, false);
    // img.src = "../images/mysteel-logo.png";
    // ctx.textAlign = 'center';
    // ctx.textBaseline = 'middle';
    // ctx.globalAlpha = 0.5;
    var colors = colortype==0?['#00F5FF', '#F9AF1A', '#FB4D4B']:colortype==1?[ '#F9AF1A', '#FB4D4B','#00F5FF']:[ '#FB4D4B','#00F5FF', '#F9AF1A'];
    var flage = false;
    if (types || types == "bug") {
        flage = true;
    }
    
    return {
         noDataLoadingOption: {
                        text: '暂无数据',
                        effect: 'bubble',
                      
         },
        color:colors,
        title: {
            text: title,
            textStyle: {
                fontSize: 16
            },
            left: 'center',
            align: 'right',
            //top: 10
        },
        // backgroundColor: {
        //     type: 'pattern',
        //     image: canvas,
        //     repeat: 'no-repeat'
        // },
        grid: {
            top:40,
            //bottom: document.body.clientWidth>=maxwidthscen?30:55,
            bottom:60,
            left: 50,
            right: 50
        },
        //toolbox: {
         //   feature: {
                // dataView: {
                //     show: true,
                //     readOnly: true,
                //     optionToContent: getViewData
                // },
         //       magicType: {
          //          show: true,
         //           type: ["line", "bar"]
         //       },
          //      restore: {
          //          show: true
         //       },
         //       saveAsImage: {
         //           show: true
         //       }
         //   }
        //},
        tooltip: {
            show: true,
            trigger: 'axis',
            //axisPointer: {
              //  type: 'cross',
               /*  animation: false,
                label: {
                    backgroundColor: '#505765',
                    formatter: function (params) {
                        console.log(params.axisDimension);
                        if (params.axisDimension == "x" && params.seriesData[0].value) {
                            return params.seriesData[0].value[0];
                        } else if (params.axisDimension == "y") {
                            return Number(params.value).toFixed(2);
                        }
                    }
                } */
           // },
            formatter: function (params, ticket, callback) {
                //console.log(params);
                var str = params[0].value[0]+'<br/>';
                for (var i = 0; i < params.length; i++) {
                    if (flage) {
                        str += params[i].seriesName + "   " + params[i].value[1] + "<br/>"
                    } else {
                        str += params[i].value[0] + "   " + params[i].value[1] + "<br/>"
                    }

                }
                return str;
            }
        },
        legend: {
            //width:'300',
            type: 'scroll',
            data: legend,
            //left: 'center',
            right:'50',
            top: '10',
            textStyle:{//图例文字的样式
                color:'#3333333',
                //fontSize:16
            }
        },
        xAxis: {
            type: 'category',
            splitLine: {
                show: false
            },
            // minInterval:1,
            // maxInterval: 3600 * 24,
            //scale: true,
            axisLabel: {
                //borderWidth:0,
                show: true,
                //rotate: document.body.clientWidth>=maxwidthscen?0:50,
                fontSize: fontSize(0.11),
                color: '#3FDDFF',
                interval:(i,v)=> true,    //强制文字产生间隔
                //rotate: 45,  
                formatter: function (value, index) {
                    //console.log(value);
                    // if(value=='进口矿')
                    // {
                    // 	value=value+'\n（美元/吨）';
                    // }
                    if(isxs==1)
                    {
                        var str = value.split("");
                        return str.join("\n");
                    }
                    else
                    {
                        return value;
                    }
                   
                    
                    //return value;
                    /* var date = new Date(value);
                    var YY = date.getFullYear() + '-';
                    var MM = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
                    var DD = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate());
                    var hh = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
                    var mm = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':';
                    var ss = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds());
                    return YY + MM + DD; */
                }
            },
            axisLine:{
                lineStyle:{
                    color:'#202F55'
                }
            }
        },
        yAxis: [{
                //splitNumber:3,
                splitLine: {
                    show: true,
                    lineStyle:{
                    color: ['#202F55']
                  }
                },
                //min:0,
                show: true,
                scale: true,
                name: '',
                nameLocation: 'end',
                axisLabel: {
                    color: '#3FDDFF',
                     textStyle: {
                        color: '#3FDDFF'
                    },
                    formatter: function (value, index) 
                    {
                        //console.log(value);
                        return value;
                    }
                },
                fontSize: 11,
                nameTextStyle: {
                    color: '#3FDDFF'
                },
                axisLine:{
                lineStyle:{
                    color:'#202F55'
                }
               }
            },{
                splitLine: {
                    show: true,
                    lineStyle:{
                    color: ['#202F55']
                  }
                },
                //min:0,
                show: true,
                scale: true,
                name: '',
                nameLocation: 'end',
                axisLabel: {
                    color: '#3FDDFF',
                     textStyle: {
                        color: '#3FDDFF'
                    },
                    formatter: function (value, index) 
                    {
                        //console.log(value);
                        return value;
                    }
                },
                fontSize: 11,
                nameTextStyle: {
                    color: '#3FDDFF'
                },
                axisLine:{
                lineStyle:{
                    color:'#202F55'
                }
               }
            }
            
        ],
        series: series
    };
};




    //获取 echart配置公共方法-按天
    function getOptionDay(title, legend, series, types, charts) {
        //加水印代码
        // var canvas = document.createElement('canvas');
        // var ctx = canvas.getContext('2d');
        // canvas.width = charts._zr.dom.clientWidth;
        // canvas.height = charts._zr.dom.clientHeight;
        // var img = new Image();
        // img.addEventListener('load', function () {
        //     ctx.drawImage(img, (charts._zr.dom.clientWidth - 100) / 2, (charts._zr.dom.clientHeight - 100) / 2, 150, 65);
        // }, false);
        // img.src = "../images/mysteel-logo.png";
        // ctx.textAlign = 'center';
        // ctx.textBaseline = 'middle';
        // ctx.globalAlpha = 0.5;

        var flage = false;
        if (types || types == "bug") {
            flage = true;
        }
        return {
            title: {
                text: title,
                textStyle: {
                    fontSize: 16
                },
                left: 'center',
                align: 'right',
                top: 10
            },
            // backgroundColor: {
            //     type: 'pattern',
            //     image: canvas,
            //     repeat: 'no-repeat'
            // },
            grid: {
                bottom: 100,
                left: 100,
                right: 100
            },
            toolbox: {
                feature: {
                    // dataView: {
                    //     show: true,
                    //     readOnly: true,
                    //     optionToContent: getViewData
                    // },
                    magicType: {
                        show: true,
                        type: ["line", "bar"]
                    },
                    restore: {
                        show: true
                    },
                    saveAsImage: {
                        show: true
                    }
                }
            },
            tooltip: {
                show: true,
                trigger: 'axis',
                axisPointer: {
                    type: 'cross',
                    animation: false,
                    label: {
                        backgroundColor: '#505765',
                        formatter: function (params) {
                            //console.log(params);
                            if (params.axisDimension == "x" && params.seriesData[0].value) {
                                return params.seriesData[0].value[0];
                            } else if (params.axisDimension == "y") {
                                return Number(params.value).toFixed(2);
                            }
                        }
                    }
                },
                formatter: function (params, ticket, callback) {
                    //console.log(params);
                    var str = '';
                    for (var i = 0; i < params.length; i++) {
                        if (flage) {
                            str += params[i].seriesName + "   " + params[i].value[1] + "<br/>"
                        } else {
                            str += params[i].value[0] + "   " + params[i].value[1] + "<br/>"
                        }

                    }
                    return str;
                }
            },
            legend: {
                //width:'300',
                type: 'scroll',
                data: legend,
                left: 'center',
                top: '40'
            },
            xAxis: {
                type: 'time',
                splitLine: {
                    show: false
                },
                // minInterval:1,
                // maxInterval: 3600 * 24,
                scale: true,
                axisLabel: {
                    show: true,
                    //rotate: 50,
                    fontSize: 12,
                    formatter: function (value, index) {
                        var date = new Date(value);
                        var YY = date.getFullYear() + '-';
                        var MM = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
                        var DD = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate());
                        var hh = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
                        var mm = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':';
                        var ss = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds());
                        return YY + MM + DD;
                    }
                }
            },
            yAxis: [{
                    splitLine: {
                        show: false
                    },
                    //min:0,
                    show: true,
                    scale: true,
                    name: '',
                    nameLocation: 'end',
                    axisLabel: {
                        //color: '#fff'
                    },
                    fontSize: 12,
                    nameTextStyle: {
                        //color: '#fff'
                    }
                },
                {
                    splitLine: {
                        show: false
                    },
                    //min:0,
                    show: true,
                    scale: true,
                    //offset: 10,
                    name: '',
                    nameLocation: 'end',
                    axisLabel: {
                        //color: '#fff'
                    }
                }
            ],
            dataZoom: [{
                    type: 'slider',
                    show: true,
                    start: 0,
                    end: 100,
                    handleSize: 8
                },
                {
                    type: 'inside',
                    start: 0,
                    end: 100
                }
            ],
            series: series
        };
    };

    //获取 echart配置公共方法-多组数据-按月
    function getOption2(title, xAxisData, legend, series, types, charts) {
        //加水印代码
        // var canvas = document.createElement('canvas');
        // var ctx = canvas.getContext('2d');
        // canvas.width = charts._zr.dom.clientWidth;
        // canvas.height = charts._zr.dom.clientHeight;
        // var img = new Image();
        // img.addEventListener('load', function () {
        //     ctx.drawImage(img, (charts._zr.dom.clientWidth - 100) / 2, (charts._zr.dom.clientHeight - 100) / 2, 150, 65);
        // }, false);
        // img.src = "../images/mysteel-logo.png";
        // ctx.textAlign = 'center';
        // ctx.textBaseline = 'middle';
        // ctx.globalAlpha = 0.5;

        var flage = false;
        if (types || types == "bug") {
            flage = true;
        }
        return {
            title: {
                text: title,
                textStyle: {
                    fontSize: 16
                },
                left: 'center',
                align: 'right',
                top: 10
            },
            // backgroundColor: {
            //     type: 'pattern',
            //     image: canvas,
            //     repeat: 'no-repeat'
            // },
            grid: {
                bottom: 63,
                left: 100,
                right: 50
            },
            toolbox: {
                feature: {
                    // dataView: {
                    //     show: true,
                    //     readOnly: true,
                    //     optionToContent: getViewData
                    // },
                    magicType: {
                        show: true,
                        type: ["line", "bar"]
                    },
                    restore: {
                        show: true
                    },
                    saveAsImage: {
                        show: true
                    }
                }
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross',
                    animation: false,
                    label: {
                        backgroundColor: '#505765'
                    }
                },
                formatter: function (params, ticket, callback) {
                    var str = '';
                    for (var i = 0; i < params.length; i++) {
                        if (flage) {
                            str += params[i].seriesName + "   " + params[i].value[1] + "<br/>"
                        } else {
                            str += params[i].value[0].substr(0, 7) + "   " + params[i].value[1] + "<br/>"
                        }
                    }
                    return str;
                }
            },
            legend: {
                //width:'300',
                type: 'scroll',
                data: legend,
                left: 'center',
                top: '40'
            },
            xAxis: {
                type: 'category',
                splitLine: {
                    show: false
                },
                scale: true,
                data: xAxisData
            },
            yAxis: [{
                    splitLine: {
                        show: false
                    },
                    //min:0,
                    show: true,
                    scale: true,
                    name: '',
                    nameLocation: 'end',
                    axisLabel: {
                        //color: '#fff'
                    },
                    fontSize: 12,
                    nameTextStyle: {
                        //color: '#fff'
                    }
                },
                {
                    splitLine: {
                        show: false
                    },
                    //min:0,
                    show: true,
                    scale: true,
                    //offset: 10,
                    name: '',
                    nameLocation: 'end',
                    axisLabel: {
                        //color: '#fff'
                    }
                }
            ],
            dataZoom: [{
                    type: 'slider',
                    show: true,
                    start: 0,
                    end: 100,
                    handleSize: 8
                },
                {
                    type: 'inside',
                    start: 0,
                    end: 100
                }
            ],
            series: series
        };
    };

    //获取 echart配置公共方法-多组数据-按天
    function getOption3(title, xAxisData, legend, series, charts) {
        //加水印代码
        // var canvas = document.createElement('canvas');
        // var ctx = canvas.getContext('2d');
        // canvas.width = charts._zr.dom.clientWidth;
        // canvas.height = charts._zr.dom.clientHeight;
        // var img = new Image();
        // img.addEventListener('load', function () {
        //     ctx.drawImage(img, (charts._zr.dom.clientWidth - 100) / 2, (charts._zr.dom.clientHeight - 100) / 2, 150, 65);
        // }, false);
        // img.src = "../images/mysteel-logo.png";
        // ctx.textAlign = 'center';
        // ctx.textBaseline = 'middle';
        // ctx.globalAlpha = 0.5;

        return {
            title: {
                text: title,
                textStyle: {
                    fontSize: 16
                },
                left: 'center',
                align: 'right',
                top: 5
            },
            // backgroundColor: {
            //     type: 'pattern',
            //     image: canvas,
            //     repeat: 'no-repeat'
            // },
            grid: {
                bottom: 65,
                left: 100,
                right: 50
            },
            toolbox: {
                feature: {
                    // dataView: {
                    //     show: true,
                    //     readOnly: true,
                    //     optionToContent: getViewData
                    // },
                    magicType: {
                        show: true,
                        type: ["line", "bar"]
                    },
                    restore: {
                        show: true
                    },
                    saveAsImage: {
                        show: true
                    }
                },
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross',
                    animation: false,
                    label: {
                        backgroundColor: '#505765'
                    }
                },
                formatter: function (params, ticket, callback) {
                    var str = '';
                    for (var i = 0; i < params.length; i++) {
                        str += params[i].seriesName + "：" + params[i].value[1] + "<br/>"
                    }
                    return str;
                }
            },
            legend: {
                //width:'300',
                type: 'scroll',
                data: legend,
                left: 'center',
                top: '40'
            },
            dataZoom: [{
                    type: 'inside',
                    height: 24,
                    start: 94,
                    end: 100,
                    bottom: 0
                },
                {
                    show: true,
                    type: 'slider',
                    height: 24,
                    start: 94,
                    end: 100,
                    bottom: 0
                }
            ],
            xAxis: {
                type: 'category',
                splitLine: {
                    show: false
                },
                scale: true,
                data: xAxisData
            },
            yAxis: [{
                    splitLine: {
                        show: false
                    },
                    //min:0,
                    show: true,
                    scale: true,
                    name: '',
                    nameLocation: 'end',
                    axisLabel: {
                        //color: '#fff'
                    },
                    fontSize: 12,
                    nameTextStyle: {
                        //color: '#fff'
                    }
                },
                {
                    splitLine: {
                        show: false
                    },
                    //min:0,
                    show: true,
                    scale: true,
                    //offset: 10,
                    name: '',
                    nameLocation: 'end',
                    axisLabel: {
                        //color: '#fff'
                    }
                }
            ],
            dataZoom: [{
                    type: 'slider',
                    show: true,
                    start: 0,
                    end: 100,
                    handleSize: 8
                },
                {
                    type: 'inside',
                    start: 0,
                    end: 100
                }
            ],
            series: series
        };
    };

    //获取 echart配置公共方法-多组数据-按月
    function getOptionYear(title, xAxisData, legend, series, types, charts) {
        // var canvas = document.createElement('canvas');
        // var ctx = canvas.getContext('2d');
        // canvas.width = charts._zr.dom.clientWidth;
        // canvas.height = charts._zr.dom.clientHeight;
        // var img = new Image();
        // img.addEventListener('load', function () {
        //     ctx.drawImage(img, (charts._zr.dom.clientWidth - 100) / 2, (charts._zr.dom.clientHeight - 100) / 2, 150, 65);
        // }, false);
        // img.src = "../images/mysteel-logo.png";
        // ctx.textAlign = 'center';
        // ctx.textBaseline = 'middle';
        // ctx.globalAlpha = 0.5;

        var flage = false;
        if (types || types == "bug") {
            flage = true;
        }
        return {
            title: {
                text: title,
                textStyle: {
                    fontSize: 16
                },
                left: 'center',
                align: 'right',
                top: 10
            },
            // backgroundColor: {
            //     type: 'pattern',
            //     image: canvas,
            //     repeat: 'no-repeat'
            // },
            grid: {
                bottom: 63,
                left: 100,
                right: 50
            },
            toolbox: {
                feature: {
                    // dataView: {
                    //     show: true,
                    //     readOnly: true,
                    //     optionToContent: getViewData
                    // },
                    magicType: {
                        show: true,
                        type: ["line", "bar"]
                    },
                    restore: {
                        show: true
                    },
                    saveAsImage: {
                        show: true
                    }
                }
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross',
                    animation: false,
                    label: {
                        backgroundColor: '#505765'
                    }
                },
                formatter: function (params, ticket, callback) {
                    //console.log(params);
                    var str = '';
                    for (var i = 0; i < params.length; i++) {
                        if (flage) {
                            if (params[i].seriesName.indexOf("同比") > -1) {
                                str += params[i].seriesName + "   " + params[i].value[1] + "%<br/>"
                            } else {
                                str += params[i].seriesName + "   " + params[i].value[1] + "<br/>"
                            }
                        } else {
                            if (params[i].seriesName.indexOf("同比") > -1) {
                                str += params[i].value[0] + "年   " + params[i].value[1] + "%<br/>"
                            } else {
                                str += params[i].value[0] + "年   " + params[i].value[1] + "<br/>"
                            }
                        }

                    }
                    return str;
                }
            },
            legend: {
                //width:'300',
                type: 'scroll',
                data: legend,
                left: 'center',
                top: '40'
            },
            xAxis: {
                type: 'category',
                splitLine: {
                    show: false
                },
                scale: true,
                data: xAxisData
            },
            yAxis: [{
                splitLine: {
                    show: false
                },
                //min:0,
                show: true,
                scale: true,
                name: '',
                nameLocation: 'end',
                axisLabel: {
                    //color: '#fff'
                },
                fontSize: 12,
                nameTextStyle: {
                    //color: '#fff'
                }
            }, {
                splitLine: {
                    show: false
                },
                //min:0,
                show: true,
                scale: true,
                //offset: 10,
                name: '',
                nameLocation: 'end',
                axisLabel: {
                    //color: '#fff'
                    formatter: function (value, index) {
                        //console.log(value);
                        // 格式化成月/日，只在第一个刻度显示年份
                        return value + '%';
                    }
                }
            }],
            dataZoom: [{
                    type: 'slider',
                    show: true,
                    start: 0,
                    end: 100,
                    handleSize: 8
                },
                {
                    type: 'inside',
                    start: 0,
                    end: 100
                }
            ],
            series: series
        };
    };


    //百分比 天
    function getOptionDayPercent(title, legend, series, charts) {
        // var canvas = document.createElement('canvas');
        // var ctx = canvas.getContext('2d');
        // canvas.width = charts._zr.dom.clientWidth;
        // canvas.height = charts._zr.dom.clientHeight;
        // var img = new Image();
        // img.addEventListener('load', function () {
        //     ctx.drawImage(img, (charts._zr.dom.clientWidth - 100) / 2, (charts._zr.dom.clientHeight - 100) / 2, 150, 65);
        // }, false);
        // img.src = "../images/mysteel-logo.png";
        // ctx.textAlign = 'center';
        // ctx.textBaseline = 'middle';
        // ctx.globalAlpha = 0.5;

        return {
            title: {
                text: title,
                textStyle: {
                    fontSize: 16,
                },
                left: 'center',
                align: 'right',
                top: 10
            },
            // backgroundColor: {
            //     type: 'pattern',
            //     image: canvas,
            //     repeat: 'no-repeat'
            // },
            grid: {
                bottom: 65,
                left: 100,
                right: 50
            },
            toolbox: {
                feature: {
                    // dataView: {
                    //     show: true,
                    //     readOnly: true,
                    //     optionToContent: getViewData
                    // },
                    magicType: {
                        show: true,
                        type: ["line", "bar"]
                    },
                    restore: {
                        show: true
                    },
                    saveAsImage: {
                        show: true
                    }
                }
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross',
                    animation: false,
                    label: {
                        backgroundColor: '#505765',
                        formatter: function (params) {
                            //console.log(params);
                            if (params.axisDimension == "x" && params.seriesData[0].value) {
                                return params.seriesData[0].value[0];
                            } else if (params.axisDimension == "y") {
                                return Number(params.value).toFixed(2);
                            }
                        }
                    }
                },
                formatter: function (params, ticket, callback) {
                    //console.log(params);
                    var str = '';
                    for (var i = 0; i < params.length; i++) {
                        if (params[i].seriesName.indexOf("同比") > -1) {
                            if (params[i].value[1]) {
                                str += params[i].seriesName + "：" + params[i].value[1] + "%<br/>"
                            } else {
                                str += params[i].seriesName + "：-<br/>"
                            }

                        } else {
                            str += params[i].seriesName + "：" + params[i].value[1] + "<br/>"
                        }
                    }
                    return str;
                }
            },
            legend: {
                //width:'300',
                type: 'scroll',
                data: legend,
                left: 'center',
                top: '40'
            },
            xAxis: {
                type: 'time',
                splitLine: {
                    show: false
                },
                // minInterval:1,
                // maxInterval: 3600 * 24,
                scale: true,
                axisLabel: {
                    show: true,
                    //rotate: 50,
                    fontSize: 12,
                    formatter: function (value, index) {
                        var date = new Date(value);
                        var YY = date.getFullYear() + '-';
                        var MM = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
                        var DD = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate());
                        var hh = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
                        var mm = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':';
                        var ss = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds());
                        return YY + MM + DD;
                    }
                }
            },
            yAxis: [{
                splitLine: {
                    show: false
                },
                //min:0,
                show: true,
                scale: true,
                name: '',
                nameLocation: 'end',
                axisLabel: {
                    //color: '#fff'
                },
                fontSize: 12,
                nameTextStyle: {
                    //color: '#fff'
                }
            }, {
                splitLine: {
                    show: false
                },
                //min:0,
                show: true,
                scale: true,
                //offset: 10,
                name: '',
                nameLocation: 'end',
                axisLabel: {
                    //color: '#fff'
                    formatter: function (value, index) {
                        //console.log(value);
                        // 格式化成月/日，只在第一个刻度显示年份
                        return value + '%';
                    }
                }
            }],
            dataZoom: [{
                    type: 'slider',
                    show: true,
                    start: 0,
                    end: 100,
                    handleSize: 8,
                },
                {
                    type: 'inside',
                    start: 0,
                    end: 100,
                }
            ],
            series: series
        };
    };

    //百分比 天 无最小值限制
    function getOptionDayPercentNoMin(title, legend, series, charts) {
        //加水印代码
        // var canvas = document.createElement('canvas');
        // var ctx = canvas.getContext('2d');
        // canvas.width = charts._zr.dom.clientWidth;
        // canvas.height = charts._zr.dom.clientHeight;
        // var img = new Image();
        // img.addEventListener('load', function () {
        //     ctx.drawImage(img, (charts._zr.dom.clientWidth - 100) / 2, (charts._zr.dom.clientHeight - 100) / 2, 150, 65);
        // }, false);
        // img.src = "../images/mysteel-logo.png";
        // ctx.textAlign = 'center';
        // ctx.textBaseline = 'middle';
        // ctx.globalAlpha = 0.5;

        return {
            title: {
                text: title,
                textStyle: {
                    fontSize: 16
                },
                left: 'center',
                align: 'right',
                top: 10
            },
            // backgroundColor: {
            //     type: 'pattern',
            //     image: canvas,
            //     repeat: 'no-repeat'
            // },
            grid: {
                bottom: 70,
                left: 100,
                right: 80
            },
            toolbox: {
                feature: {
                    // dataView: {
                    //     show: true,
                    //     readOnly: true,
                    //     optionToContent: getViewData
                    // },
                    magicType: {
                        show: true,
                        type: ["line", "bar"]
                    },
                    restore: {
                        show: true
                    },
                    saveAsImage: {
                        show: true
                    }
                }
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross',
                    animation: false,
                    label: {
                        backgroundColor: '#505765'
                    }
                },
                formatter: function (params, ticket, callback) {
                    //console.log(params);
                    var str = '';
                    for (var i = 0; i < params.length; i++) {
                        if (params[i].seriesName.indexOf("同比") > -1) {
                            str += params[i].seriesName + "：" + params[i].value[1] + "%<br/>"
                        } else {
                            str += params[i].seriesName + "：" + params[i].value[1] + "<br/>"
                        }
                    }
                    return str;
                }
            },
            legend: {
                //width:'300',
                type: 'scroll',
                data: legend,
                left: 'center',
                top: '40'
            },
            xAxis: {
                type: 'time',
                splitLine: {
                    show: false
                },
                // minInterval:1,
                // maxInterval: 3600 * 24,
                scale: true,
                axisLabel: {
                    show: true,
                    //rotate: 50,
                    fontSize: 12,
                    formatter: function (value, index) {
                        var date = new Date(value);
                        var YY = date.getFullYear() + '-';
                        var MM = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
                        var DD = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate());
                        var hh = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
                        var mm = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':';
                        var ss = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds());
                        return YY + MM + DD;
                    }
                }
            },
            yAxis: [{
                splitLine: {
                    show: false
                },
                //min:0,
                show: true,
                scale: true,
                name: '',
                nameLocation: 'end',
                axisLabel: {
                    //color: '#fff'
                },
                fontSize: 12,
                nameTextStyle: {
                    //color: '#fff'
                }
            }, {
                splitLine: {
                    show: false
                },
                //min:0,
                show: true,
                scale: true,
                //offset: 10,
                name: '',
                nameLocation: 'end',
                axisLabel: {
                    //color: '#fff'
                    formatter: function (value, index) {
                        //console.log(value);
                        // 格式化成月/日，只在第一个刻度显示年份
                        return value + '%';
                    }
                }
            }],
            dataZoom: [{
                    type: 'slider',
                    show: true,
                    start: 0,
                    end: 100,
                    handleSize: 8
                },
                {
                    type: 'inside',
                    start: 0,
                    end: 100
                }
            ],
            series: series
        };
    };

    //产量同比
    function getOptionProductPercent(title, legend, series, charts) {
        // var canvas = document.createElement('canvas');
        // var ctx = canvas.getContext('2d');
        // canvas.width = charts._zr.dom.clientWidth;
        // canvas.height = charts._zr.dom.clientHeight;
        // var img = new Image();
        // img.addEventListener('load', function () {
        //     ctx.drawImage(img, (charts._zr.dom.clientWidth - 100) / 2, (charts._zr.dom.clientHeight - 100) / 2, 150, 65);
        // }, false);
        // img.src = "../images/mysteel-logo.png";
        // ctx.textAlign = 'center';
        // ctx.textBaseline = 'middle';
        // ctx.globalAlpha = 0.5;

        return {
            title: {
                text: title,
                left: 'center',
            },
            // backgroundColor: {
            //     type: 'pattern',
            //     image: canvas,
            //     repeat: 'no-repeat'
            // },
            tooltip: {
                trigger: 'axis',
                axisPointer: { // 坐标轴指示器，坐标轴触发有效
                    type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
                }
            },
            grid: {
                top: 30,
                right: 80,
                left: 80,
                bottom: 30
            },
            xAxis: {
                type: 'value',
                name: "%",
                position: 'bottom',
                splitLine: {
                    lineStyle: {
                        type: 'dashed'
                    }
                }
            },
            yAxis: {
                type: 'category',
                axisLine: {
                    show: false
                },
                axisLabel: {
                    show: false
                },
                axisTick: {
                    show: false
                },
                splitLine: {
                    show: false
                },
                data: legend
            },
            series: series
        };
    };

    //下游产品
    function getOptionDownStream(title, legend, dataDates, series, unitName, charts) {
        // var canvas = document.createElement('canvas');
        // var ctx = canvas.getContext('2d');
        // canvas.width = charts._zr.dom.clientWidth;
        // canvas.height = charts._zr.dom.clientHeight;
        // var img = new Image();
        // img.addEventListener('load', function () {
        //     ctx.drawImage(img, (charts._zr.dom.clientWidth - 100) / 2, (charts._zr.dom.clientHeight - 100) / 2, 150, 65);
        // }, false);
        // img.src = "../images/mysteel-logo.png";
        // ctx.textAlign = 'center';
        // ctx.textBaseline = 'middle';
        // ctx.globalAlpha = 0.5;

        return {
            title: {
                text: title,
                left: 'center',
            },
            // backgroundColor: {
            //     type: 'pattern',
            //     image: canvas,
            //     repeat: 'no-repeat'
            // },
            tooltip: {
                trigger: "axis",
                axisPointer: {
                    type: "cross",
                    crossStyle: {
                        color: "#999",
                    },
                },
            },
            toolbox: {
                feature: {
                    // dataView: {
                    //   show: true,
                    //   readOnly: true,
                    //   optionToContent: getViewData,
                    // },
                    magicType: {
                        show: true,
                        type: ["line", "bar"]
                    },
                    restore: {
                        show: true
                    },
                    saveAsImage: {
                        show: true
                    },
                },
            },
            legend: {
                type: "scroll",
                top: 30,
                data: legend,
            },
            dataZoom: [{
                    type: "slider",
                    show: true,
                    start: 0,
                    end: 100,
                    handleSize: 8,
                },
                {
                    type: "inside",
                    start: 0,
                    end: 100,
                },
            ],
            xAxis: [{
                type: "category",
                data: dataDates,
                axisPointer: {
                    type: "shadow",
                },
            }, ],
            yAxis: [{
                    name: unitName,
                    type: "value",
                    axisLabel: {
                        formatter: "{value} ",
                    },
                },
                {
                    name: "%",
                    type: "value",
                    axisLabel: {
                        formatter: "{value} %",
                    },
                    splitLine: {
                        show: false,
                    },
                },
            ],
            series: series
        };
    };

    //获取当天
    function getNowDate() {
        var date = new Date();
        var month = date.getMonth() + 1;
        var strDate = date.getDate();
        if (month >= 1 && month <= 9) {
            month = "0" + month;
        }
        if (strDate >= 0 && strDate <= 9) {
            strDate = "0" + strDate;
        }
        var currentDate = date.getFullYear() + '-' + month + '-' + strDate;
        return currentDate;
    };

    //获取当月
    function getNowMonth() {
        var date = new Date();
        var month = date.getMonth() + 1;
        var strDate = date.getDate();
        if (month >= 1 && month <= 9) {
            month = "0" + month;
        }
        if (strDate >= 0 && strDate <= 9) {
            strDate = "0" + strDate;
        }
        var currentDate = date.getFullYear() + '-' + month;
        return currentDate;
    };

    //过去二年的时间
    function getPassYearFormatDate() {
        var nowDate = new Date();
        var date = new Date(nowDate);
        date.setDate(date.getDate() - 730);
        var seperator1 = "-";
        var year = date.getFullYear();
        var month = date.getMonth() + 1;
        var strDate = date.getDate();
        if (month >= 1 && month <= 9) {
            month = "0" + month;
        }
        if (strDate >= 0 && strDate <= 9) {
            strDate = "0" + strDate;
        }
        //seperator1 + strDate;
        var currentdate = year + seperator1 + month;
        return currentdate;
    }

    //获取当天
    function getNowDate() {
        var date = new Date();
        var month = date.getMonth() + 1;
        var strDate = date.getDate();
        if (month >= 1 && month <= 9) {
            month = "0" + month;
        }
        if (strDate >= 0 && strDate <= 9) {
            strDate = "0" + strDate;
        }
        var currentDate = date.getFullYear() + '-' + month + '-' + strDate;
        return currentDate;
    };

    //获取过去多少天
    /*
     *  @Date 开始时间 格式 2019-01-01
     *  @cycle 时间长度 单位 天
     */
    function getPassDate(beginDate, cycle) {
        var date = new Date(beginDate);
        date.setDate(date.getDate() - cycle);
        var seperator1 = "-";
        var year = date.getFullYear();
        var month = date.getMonth() + 1;
        var strDate = date.getDate();
        if (month >= 1 && month <= 9) {
            month = "0" + month + "-";
        }
        if (strDate >= 0 && strDate <= 9) {
            strDate = "0" + strDate;
        }
        var currentdate = year + seperator1 + month + strDate;
        return currentdate;
    }

    //下载添加水印
    function downAddLogo(title, myChart1) {
        var defineOption = myChart1.getOption();
        myChart1._zr.on('mousedown', function (params) {
            if (params.topTarget != undefined && params.topTarget.__title == '保存为图片') {
                //加水印代码
                var canvas = document.createElement('canvas');
                var ctx = canvas.getContext('2d');
                canvas.width = myChart1._zr.dom.clientWidth;
                canvas.height = myChart1._zr.dom.clientHeight;
                var img = new Image();
                img.addEventListener('load', function () {
                    ctx.drawImage(img, (myChart1._zr.dom.clientWidth - 100) / 2, (myChart1._zr.dom
                        .clientHeight - 100) / 2, 150, 65);
                }, false);
                img.src = "https://client.mysteel.com/f9-ui/content/images/mysteel-logo.png";
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.globalAlpha = 0.5;

                defineOption.backgroundColor = {
                    type: 'pattern',
                    image: canvas,
                    repeat: 'no-repeat'
                };
                myChart1.setOption(defineOption);

                setTimeout(function () {
                    var img = new Image();
                    img.src = myChart1.getDataURL({
                        type: "png",
                        pixelRatio: 1,
                        backgroundColor: '#fff'
                    });

                    img.onload = function () {
                        var canvas = document.createElement("canvas");
                        canvas.width = img.width;
                        canvas.height = img.height;
                        var ctx = canvas.getContext('2d');
                        ctx.drawImage(img, 0, 0);
                        var dataURL = canvas.toDataURL('image/png');

                        var a = document.createElement('a');
                        // 创建一个单击事件
                        var event = new MouseEvent('click');
                        // 将a的download属性设置为我们想要下载的图片名称，若name不存在则使用‘下载图片名称’作为默认名称
                        a.download = title || '下载图片.png';
                        // 将生成的URL设置为a.href属性
                        a.href = dataURL;
                        // 触发a的单击事件
                        a.dispatchEvent(event);

                        defineOption.backgroundColor = '';
                        myChart1.setOption(defineOption);
                    };
                }, 800);

                // setTimeout(function(){
                //     defineOption.backgroundColor = '';
                //     myChart1.setOption(defineOption);
                // },1000)
            }
        });
    }