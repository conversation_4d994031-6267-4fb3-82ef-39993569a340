<?php 
include_once( FRAME_LIB_DIR ."/action/AbstractAction.inc.php" );
class  sgtieshuicbBaseAction extends AbstractAction
{
    public function __construct()
    {
        parent::__construct();
    } 
	
 	public function index($params)
    {
		$GUID = $params['GUID'];
		$mode = $params['mode'];//1=客户端，2=安卓，3=iOS
		
        if($GUID==""){
            $params['tip'] = "GUID不能为空！";       
        }
		
		//获取用户信息
		$user_infos = $this->t1dao->get_userinfo_byguid($GUID);
		$Mid=$user_infos['Mid'];//会员id
		$userid=$user_infos['Uid'];//用户id
		$TrueName=$user_infos['TrueName'];//用户id
		
		//获取用户权限和用户名app_license_privilege
		$license_detail_info = $this->t1dao->get_license_privilege($userid);
       
        $this->assign("cb_privilege",$license_detail_info['cb_privilege']);
        $this->assign("mode",$mode);
        $this->assign("tip",$params['tip']);
        $this->assign("pid",$params['pid']);
        $this->assign("tieshui_sum",$params['tieshui_sum']);
		$this->assign("tieshui_sum_tax",$params['tieshui_sum_tax']);
		$this->assign("Mid",$Mid);

        $where = " and isdel=0 ";
        if($params['date']!=""){
            $date = $params['date'];
            $where .=" and ndate<='".$date."' ";
        }
        if($params['Type']!=""){
            $params['type'] = $params['Type'];
        }
        if($params['type']!=""){
            $where .=" and Type='".$params['type']."' ";
        }
        if($params['id']!=""){
            $where .=" and id='".$params['id']."' ";
        }
		
        $info = $this->_dao->get_ChengBenParameter($where);	 	 
        $this->assign("info",$info);

        $total = $this->_dao->getOne( "SELECT COUNT(id) as c FROM  sg_ChengBenParameter where Type='".$params['type']."' and isdel=0 " );
        $page = $params['page'] == '' ? 1 : $params['page'];
        $per = 10;
        $start = ( $page - 1 ) * $per;
        $url = "sgtieshuicb.php";

        $data = $this->_dao->getAll($params['type'],$start,$per); 
      
        $this->assign("data",$data);
        $this->assign("page",$page);
        $this->assign("tatalpage",ceil($total/$per));
        
        if($params['date']==""){
            
            $flag = 1;
            $lastday = date("Y-m-d");
            while(true)
            {
                $lastday=date('Y-m-d',strtotime('-'.$flag.' day',strtotime($lastday)));
                if(file_get_contents(APP_URL_WWW.'/isholiday.php?date='.$lastday)!="1")
                { 
                  break;
                } 
            }
            $params['date'] = $lastday;
            $date = $lastday;
        }
		
		if(!empty($info) && $params['type']!="20") {
			$jiage_arr = $this->maindao->getpricebypricestr($info['GkxhkPriceidA'].",".$info['GckPriceidB'].",".$info['Z1YjjPriceidC'].",".$info['PcWymPriceidD'],$params['date']);
            $jiage_arr['GangKouXianHuoKuang'] = $jiage_arr[$info['GkxhkPriceidA']];
            $jiage_arr['GuoChanKuang'] = $jiage_arr[$info['GckPriceidB']];
            $jiage_arr['YeJinJiao'] = $jiage_arr[$info['Z1YjjPriceidC']];
            $jiage_arr['WuYanMei'] = $jiage_arr[$info['PcWymPriceidD']];
        }
		
        if($params['type']=="20") {
            $jiage_arr['GangKouXianHuoKuang'] = round($this->maindao->getone("select weiprice from shpi_material_pzp where vid=3 and dateday='{$date}'"));
            $jiage_arr['GuoChanKuang'] = round($this->maindao->getone("select weiprice from shpi_material_pzp where vid=1 and dateday='{$date}'"));
            $jiage_arr['YeJinJiao'] = round($this->maindao->getone("select price from shpi_material where topicture=3 and dateday='{$date}'"));
            $jiage_arr['WuYanMei'] = round($this->maindao->getone("select weiprice from shpi_mj_pzp where vid=0 and type=3 and dateday='{$date}'"));
        }
		$this->assign("jiage_arr",$jiage_arr);

        if($mode==1){
            $head_url = "//".$_SERVER['SERVER_NAME']."/v1.5/css/sg_tablestyle_small.css";
        }else if($mode==3||$mode==5){
            $head_url = "//".$_SERVER['SERVER_NAME']."/v1.5/css/sg_tablestyle_middle.css";
        }else if($mode==2||$mode==4){
            $head_url = "//".$_SERVER['SERVER_NAME']."/v1.5/css/sg_tablestyle_large.css";
        }else{
            $head_url = "//".$_SERVER['SERVER_NAME']."/v1.5/css/sg_tablestyle_middle.css";
        }
		$style_url= dirname("//".$_SERVER['SERVER_NAME'].$_SERVER['PHP_SELF']);
		$this->assign( "style_url", $style_url );
		$this->assign("head_url",$head_url);

        $this->assign("params",$params);
		
    }

    public function ajaxgetindexinfo($params){
        $page = $params['page'] == '' ? 1 : $params['page'];
        $per = 10;
        $start = ( $page - 1 ) * $per;
		
		if($params['cbmodel'] == "2")
			$data = $this->_dao->getAll2($params['type'],$start,$per);
		else if($params['cbmodel'] == "3")
			$data = $this->_dao->getAll3($params['type'],$start,$per);
		else if($params['cbmodel'] == "4")
			$data = $this->_dao->getAll4($params['type'],$start,$per);
		else
			$data = $this->_dao->getAll($params['type'],$start,$per);
		
        $content = "";
        foreach($data as $tmp){
            $content.='<tr>
				<td>'.$tmp['ndate'].'</td>
				<td>'.$tmp['CreateDate'].'</td>
				<td>'.$tmp['adminname'].'</td>
				<td><a onclick="update('.$tmp['id'].');">修改</a></td>
			</tr>';
        }
        echo $content;
        exit;
    }

    public function update($params){
        $info = array();
        if($params['id']!="" && $params['cbmodel'] == "2"){
			$where .=" and id='".$params['id']."' ";
            $info = $this->_dao->get_ChengBenParameter2($where);	 
			
			$jiageid = $info['FgPriceidA'].",".$info['GmPriceidB'].",".$info['GtPriceidC'].",".$info['FdPriceidD'];
			if($info['type'] == "6" || $info['type'] == "7")
				$jiageid .= ",".$info['CggldjPriceid'].",".$info['GgldjPriceid'];
			
			$jiage_arr = $this->maindao->getpricebypricestr($jiageid,$info['ndate']);
			
			$info['FgPrice'] = $jiage_arr[$info['FgPriceidA']];
			$info['GmPrice'] = $jiage_arr[$info['GmPriceidB']];
			$info['GtPrice'] = $jiage_arr[$info['GtPriceidC']];
			$info['FdPrice'] = $jiage_arr[$info['FdPriceidD']];
			if($info['type'] == "6" || $info['type'] == "7")
			{
				$jiage_arr['CggldjPrice'] = $jiage_arr[$info['CggldjPriceid']];
				$jiage_arr['GgldjPrice'] = $jiage_arr[$info['GgldjPriceid']];
			}
			
			$tstype = 5+($params['type']-1)*30;
			//铁水成本不含税
			$tssql="select chenben from sg_HangYeChengBenIndex where type='".$tstype."' and ndate<='".$info['ndate']."' and mc_type=2 limit 1";
			$info['TieShuiCBBHS'] = $this->_dao->getOne($tssql);
        }
		else if($params['id']!="" && $params['cbmodel'] == "3"){
			
			$where .=" and id='".$params['id']."' ";
            $info = $this->_dao->get_ChengBenParameter3($where);	 
			
			if($params['type'] != "6" || $params['type'] != "7")
			{
				$pttype = 10+($params['type']-1)*30;
				$lwtype = 11+($params['type']-1)*30;
			}
			if($params['type'] == "6")
			{
				$pttype = 158;
				$lwtype = 159;
			}
			if($params['type'] == "7")
			{
				$pttype = 188;
				$lwtype = 189;
			}
			//普碳坯成本不含税
			$ptpsql="select chenben from sg_HangYeChengBenIndex where type='".$pttype."' and ndate<='".$info['ndate']."' and mc_type=2 limit 1";
			$info['PuTanPiCBBHS'] = $this->_dao->getOne($ptpsql);
			//螺纹坯成本不含税
			$lwpsql="select chenben from sg_HangYeChengBenIndex where type='".$lwtype."' and ndate<='".$info['ndate']."' and mc_type=2 limit 1";
			$info['LuoWenPiCBBHS'] = $this->_dao->getOne($lwpsql);
        }
		else if($params['id']!="" && $params['cbmodel'] == "4"){
			
			$where .=" and id='".$params['id']."' ";
            $info = $this->_dao->get_ChengBenParameter4($where);	 
			
			$info['shuilv'] = $this->maindao->get_master_cntaxrate($info['ndate'],1) * 100;//税率
			$info['yunfeishuilv'] = $this->maindao->get_master_cntaxrate($info['ndate'],2) * 100;//税率
        }
		else
		{
			$where .=" and id='".$params['id']."' ";
            $info = $this->_dao->get_ChengBenParameter($where);	 
			
            if($info['type'] == '20') {
                $date = $info['ndate'];
                $info['GangKouXianHuoKuang'] = round($this->maindao->getone("select weiprice from shpi_material_pzp where vid=1 and dateday='{$date}'"));
                $info['GuoChanKuang'] = round($this->maindao->getone("select weiprice from shpi_material_pzp where vid=3 and dateday='{$date}'"));
                $info['YeJinJiao'] = round($this->maindao->getone("select price from shpi_material where topicture=3 and dateday='{$date}'"));
                $info['WuYanMei'] = round($this->maindao->getone("select weiprice from shpi_mj_pzp where vid=0 and type=3 and dateday='{$date}'"));
            } else {
                $jiage_arr = $this->maindao->getpricebypricestr($info['GkxhkPriceidA'].",".$info['GckPriceidB'].",".$info['Z1YjjPriceidC'].",".$info['PcWymPriceidD'],$info['ndate']);
                
                $info['GangKouXianHuoKuang'] = $jiage_arr[$info['GkxhkPriceidA']];
                $info['GuoChanKuang'] = $jiage_arr[$info['GckPriceidB']];
                $info['YeJinJiao'] = $jiage_arr[$info['Z1YjjPriceidC']];
                $info['WuYanMei'] = $jiage_arr[$info['PcWymPriceidD']];
            }
		}
        echo json_encode($info);
        exit;
    }
	
	function changejiage($params){
		if($params['jiageid'] != "")
		{
			$jiage_arr = $this->maindao->getpricebypricestr($params['jiageid'],$params['date']);
			
			if(!empty($jiage_arr))
				$result['price'] = $jiage_arr[$params['jiageid']];
		}
		echo json_encode($result);
        exit;		
	}

    public function del($params){
		
        $status="2";
        if($params['id']!="" && $params['GUID']!=""){
			
            //更新任务表相同日期状态
            $where =" and id='".$params['id']."' ";
			
			if($params['cbmodel'] == "2")
			{
				$this->_dao->execute( "update sg_ChengBenParameter2 set isdel=1 where id='".$params['id']."'");
				$info = $this->_dao->get_ChengBenParameter2($where);
				$sql = "select mTask.id from sg_ModifyChengBenParameterTask mTask,sg_ChengBenParameter2 cb2 where cb2.id=mTask.pid and mTask.status=0 and cb2.ndate='".$info['Date']."' and mTask.Type='2'";
			}
			else if($params['cbmodel'] == "3")
			{
				$this->_dao->execute( "update sg_ChengBenParameter3 set isdel=1 where id='".$params['id']."'");
				$info = $this->_dao->get_ChengBenParameter3($where);
				$sql = "select mTask.id from sg_ModifyChengBenParameterTask mTask,sg_ChengBenParameter3 cb3 where cb3.id=mTask.pid and mTask.status=0 and cb3.ndate='".$info['Date']."' and mTask.Type='3'";
			}
			else if($params['cbmodel'] == "4")
			{
				$this->_dao->execute( "update sg_ChengBenParameter4 set isdel=1 where id='".$params['id']."'");
				$info = $this->_dao->get_ChengBenParameter4($where);
				$sql = "select mTask.id from sg_ModifyChengBenParameterTask mTask,sg_ChengBenParameter4 cb4 where cb4.id=mTask.pid and mTask.status=0 and cb4.ndate='".$info['Date']."' and mTask.Type='4'";
			}
			else
			{
				$this->_dao->execute( "update sg_ChengBenParameter set isdel=1 where id='".$params['id']."'");
				$info = $this->_dao->get_ChengBenParameter($where);
				$sql = "select mTask.id from sg_ModifyChengBenParameterTask mTask,sg_ChengBenParameter cb where cb.id=mTask.pid and mTask.status=0 and cb.ndate='".$info['Date']."' and mTask.Type='1'";
			}
			
            //echo $sql;
            $task_pid = $this->_dao->query($sql);
            foreach($task_pid as $key=>$tmp){
                $this->_dao->execute("update sg_ModifyChengBenParameterTask set status='2' where id = '".$tmp['id']."'");
            }

            $this->_dao->execute( "INSERT INTO sg_ModifyChengBenParameterTask SET pid='".$params['id']."',Type='".$params['cbmodel']."',status=0");
            $status="1";
            $tip = "删除成功";
        }
        if($params['GUID']==""){
            $tip = "删除失败，GUID不能为空！";
        }
        //exit;
        //echo $status;
		if($params['cbmodel'] == "2")
			gourl("./sgtieshuicb.php?view=gangpiindex&GUID=".$params['GUID']."&type=".$params['type']."&mode=".$params['mode']."&tip=".$tip);
		else if($params['cbmodel'] == "3")
			gourl("./sgtieshuicb.php?view=gangcaiindex&GUID=".$params['GUID']."&type=".$params['type']."&mode=".$params['mode']."&tip=".$tip);
		else if($params['cbmodel'] == "4")
			gourl("./sgtieshuicb.php?view=longhangangindex&GUID=".$params['GUID']."&type=".$params['type']."&mode=".$params['mode']."&tip=".$tip);
		else
			gourl("./sgtieshuicb.php?view=index&GUID=".$params['GUID']."&type=".$params['type']."&mode=".$params['mode']."&tip=".$tip);
        exit;
    }

    public function save($params){
        //echo "<pre>";print_r($params);
        $GUID = $params['GUID'];
        if($GUID==""){
            $tip = "保存失败，GUID不能为空！";
        }
		else{
            $type = $params['type'];
            $date = $params['date'];
            if($GUID==""){
				if($params['cbmodel'] == "2")
					gourl("./sgtieshuicb.php?view=gangpiindex&GUID=".$GUID."&type=".$type."&mode=".$params['mode']."&pid=".$pid."&date=".$date."&ptgangpi_sum=".$params['ptgangpi_sum']."&ptgangpi_sum_tax=".$params['ptgangpi_sum_tax']."&lwgangpi_sum=".$params['lwgangpi_sum']."&lwgangpi_sum_tax=".$params['lwgangpi_sum_tax']."&tip=保存成功");
				else if($params['cbmodel'] == "3")
					gourl("./sgtieshuicb.php?view=gangcaiindex&GUID=".$GUID."&type=".$type."&mode=".$params['mode']."&pid=".$pid."&date=".$date."&lwggangcai_sum=".$params['lwggangcai_sum']."&lwggangcai_sum_tax=".$params['lwggangcai_sum_tax']."&plgangcai_sum=".$params['plgangcai_sum']."&plgangcai_sum_tax=".$params['plgangcai_sum_tax']."&xcgangcai_sum=".$params['xcgangcai_sum']."&xcgangcai_sum_tax=".$params['xcgangcai_sum_tax']."&dggangcai_sum=".$params['dggangcai_sum']."&dggangcai_sum_tax=".$params['dggangcai_sum_tax']."&tip=保存成功");
				else if($params['cbmodel'] == "4")
					gourl("./sgtieshuicb.php?view=longhangangindex&GUID=".$GUID."&type=".$type."&mode=".$params['mode']."&pid=".$pid."&date=".$date."&shaojiekuang_sum=".$params['shaojiekuang_sum']."&shengtie_sum=".$params['shengtie_sum']."&gangpi_sum=".$params['gangpi_sum']."&gangcai_sum=".$params['gangcai_sum']."&duncai_sum=".$params['duncai_sum']."&tip=保存成功");
				else
					gourl("./sgtieshuicb.php?view=index&GUID=".$GUID."&type=".$type."&mode=".$params['mode']."&pid=".$pid."&date=".$date."&tieshui_sum=".$params['tieshui_sum']."&tieshui_sum_tax=".$params['tieshui_sum_tax']."&tip=保存成功");
            }
            //获取用户信息
            $user_infos = $this->t1dao->get_userinfo_byguid($GUID);
            $Mid=$user_infos['Mid'];//会员id
            $userid=$user_infos['Uid'];//用户id
            $TrueName=$user_infos['TrueName'];//用户id
			
			if($params['cbmodel'] == "2")
				$this->_dao->execute( "INSERT INTO sg_ChengBenParameter2 SET StBiLiA='".$params['StBiLiA']."',FgPriceidA='".$params['FgPriceidA']."',FgBiLiA='".$params['FgBiLiA']."',DaoChangYunFeiA='".$params['DaoChangYunFeiA']."',isChengDuiA='".$params['isChengDuiA']."',Gtlxh='".$params['Gtlxh']."',CggldjPriceid='".$params['CggldjPriceid']."',GgldjPriceid='".$params['GgldjPriceid']."',DunGangXiaoHao1='".$params['DunGangXiaoHao1']."',DunGangXiaoHao2='".$params['DunGangXiaoHao2']."',DianHao='".$params['DianHao']."',PingDuanDianFei='".$params['PingDuanDianFei']."',GmPriceidB='".$params['GmPriceidB']."',PuTanPiXiaoHaoB='".$params['PuTanPiXiaoHaoB']."',DaoChangYunFeiB='".$params['DaoChangYunFeiB']."',LuoWenPiXiaoHaoB='".$params['LuoWenPiXiaoHaoB']."',isChengDuiB='".$params['isChengDuiB']."',GtPriceidC='".$params['GtPriceidC']."',PuTanPiXiaoHaoC='".$params['PuTanPiXiaoHaoC']."',DaoChangYunFeiC='".$params['DaoChangYunFeiC']."',LuoWenPiXiaoHaoC='".$params['LuoWenPiXiaoHaoC']."',isChengDuiC='".$params['isChengDuiC']."',FdPriceidD='".$params['FdPriceidD']."',PuTanPiXiaoHaoD='".$params['PuTanPiXiaoHaoD']."',DaoChangYunFeiD='".$params['DaoChangYunFeiD']."',LuoWenPiXiaoHaoD='".$params['LuoWenPiXiaoHaoD']."',isChengDuiD='".$params['isChengDuiD']."',DlrgMake='".$params['DlrgMake']."',FuLiao='".$params['FuLiao']."',GangZhaHuiShou='".$params['GangZhaHuiShou']."',adminid='".$userid."',adminname='".$TrueName."',Type='".$params['type']."',CreateDate=now(),ndate='".$date."',mc_type=2");
			else if($params['cbmodel'] == "3")
				$this->_dao->execute( "INSERT INTO sg_ChengBenParameter3 SET ZhaZhiFeiYongXc='".$params['ZhaZhiFeiYongXc']."',ZhaZhiFeiYongLw='".$params['ZhaZhiFeiYongLw']."',ZhaZhiFeiYongPl='".$params['ZhaZhiFeiYongPl']."',ZhaZhiFeiYongDg='".$params['ZhaZhiFeiYongDg']."',FgYHTPHuiShou='".$params['FgYHTPHuiShou']."',QiJianFeiYong='".$params['QiJianFeiYong']."',XiaoShouShuiJinJiFuJia='".$params['XiaoShouShuiJinJiFuJia']."',LuoWenGangFuCha='".$params['LuoWenGangFuCha']."',adminid='".$userid."',adminname='".$TrueName."',Type='".$params['type']."',CreateDate=now(),ndate='".$date."',mc_type=2");
			else if($params['cbmodel'] == "4")
				//$this->_dao->execute( "INSERT INTO sg_ChengBenParameter4 SET Pszhishu='".$params['Pszhishu']."',ImportDaochangyf='".$params['ImportDaochangyf']."',ImportHanshuilv='".$params['ImportHanshuilv']."',ImportKuanggangzf='".$params['ImportKuanggangzf']."',ImportPinwei='".$params['ImportPinwei']."',KuaikuangPrice='".$params['KuaikuangPrice']."',KuaikaungBili='".$params['KuaikaungBili']."',QiutuankuangPrice='".$params['QiutuankuangPrice']."',QiutuankuangBili='".$params['QiutuankuangBili']."',ShaojiekuangBili='".$params['ShaojiekuangBili']."',ShaojiekuangJiagong='".$params['ShaojiekuangJiagong']."',ShaojieChengpinlv='".$params['ShaojieChengpinlv']."',ShaojieHantieliang='".$params['ShaojieHantieliang']."',JiaotanPrice='".$params['JiaotanPrice']."',Jiaobi='".$params['Jiaobi']."',JiaomohuishouPrice='".$params['JiaomohuishouPrice']."',Jiaomohuishengliang='".$params['Jiaomohuishengliang']."',PenchuimeiPrice='".$params['PenchuimeiPrice']."',Penmeiliang='".$params['Penmeiliang']."',ShengtiekuangPrice='".$params['ShengtiekuangPrice']."',ShengtiekuangYongliang='".$params['ShengtiekuangYongliang']."',LiantieJiagongfei='".$params['LiantieJiagongfei']."',LiantieHuishouxiang='".$params['LiantieHuishouxiang']."',DuntieKuanghao='".$params['DuntieKuanghao']."',GangpiJiagongfei='".$params['GangpiJiagongfei']."',Gangcaijiagongfei='".$params['Gangcaijiagongfei']."',QijianFeiyong='".$params['QijianFeiyong']."',Gangcaierjishoujia='".$params['Gangcaierjishoujia']."',Gangcaiyunfeidailifei='".$params['Gangcaiyunfeidailifei']."',USRMB='".$params['USRMB']."',Otherfee='".$params['Otherfee']."',adminid='".$userid."',adminname='".$TrueName."',Type='".$params['type']."',CreateDate=now(),ndate='".$date."',mc_type=2");
				$this->_dao->execute( "INSERT INTO sg_ChengBenParameter4 SET Pszhishu='',ImportDaochangyf='".$params['ImportDaochangyf']."',ImportHanshuilv='".$params['ImportHanshuilv']."',ImportKuanggangzf='".$params['ImportKuanggangzf']."',ImportPinwei='".$params['ImportPinwei']."',KuaikuangPrice='',KuaikaungBili='".$params['KuaikaungBili']."',QiutuankuangPrice='',QiutuankuangBili='".$params['QiutuankuangBili']."',ShaojiekuangBili='".$params['ShaojiekuangBili']."',ShaojiekuangJiagong='".$params['ShaojiekuangJiagong']."',ShaojieChengpinlv='".$params['ShaojieChengpinlv']."',ShaojieHantieliang='".$params['ShaojieHantieliang']."',JiaotanPrice='',Jiaobi='".$params['Jiaobi']."',JiaomohuishouPrice='".$params['JiaomohuishouPrice']."',Jiaomohuishengliang='".$params['Jiaomohuishengliang']."',PenchuimeiPrice='',Penmeiliang='".$params['Penmeiliang']."',ShengtiekuangPrice='',ShengtiekuangYongliang='".$params['ShengtiekuangYongliang']."',LiantieJiagongfei='".$params['LiantieJiagongfei']."',LiantieHuishouxiang='".$params['LiantieHuishouxiang']."',DuntieKuanghao='".$params['DuntieKuanghao']."',GangpiJiagongfei='".$params['GangpiJiagongfei']."',Gangcaijiagongfei='".$params['Gangcaijiagongfei']."',QijianFeiyong='".$params['QijianFeiyong']."',Gangcaierjishoujia='',Gangcaiyunfeidailifei='".$params['Gangcaiyunfeidailifei']."',USRMB='',Otherfee='".$params['Otherfee']."',adminid='".$userid."',adminname='".$TrueName."',Type='".$params['type']."',CreateDate=now(),ndate='".$date."',mc_type=2");
			else
				$this->_dao->execute( "INSERT INTO sg_ChengBenParameter SET GkxhkPriceidA='".$params['GkxhkPriceidA']."',HanShuiLvA='".$params['HanShuiLvA']."',DaoChangYunFeiA='".$params['DaoChangYunFeiA']."',TuSunA='".$params['TuSunA']."',JinKouKuangZBA='".$params['JinKouKuangZBA']."',isChengDuiA='".$params['isChengDuiA']."',GckPriceidB='".$params['GckPriceidB']."',HanShuiLvB='".$params['HanShuiLvB']."',DaoChangYunFeiB='".$params['DaoChangYunFeiB']."',TuSunB='".$params['TuSunB']."',GuoChanKuangZBB='".$params['GuoChanKuangZBB']."',isChengDuiB='".$params['isChengDuiB']."',DtKuangHaoB='".$params['DtKuangHaoB']."',Z1YjjPriceidC='".$params['Z1YjjPriceidC']."',HanShuiLvC='".$params['HanShuiLvC']."',DaoChangYunFeiC='".$params['DaoChangYunFeiC']."',JiaoBiC='".$params['JiaoBiC']."',WaiGouBLC='".$params['WaiGouBLC']."',isChengDuiC='".$params['isChengDuiC']."',PcWymPriceidD='".$params['PcWymPriceidD']."',HanShuiLvD='".$params['HanShuiLvD']."',DaoChangYunFeiD='".$params['DaoChangYunFeiD']."',PenMeiLiangD='".$params['PenMeiLiangD']."',isChengDuiD='".$params['isChengDuiD']."',QiuTuanMakeFei='".$params['QiuTuanMakeFei']."',ShaoJieFuLiao='".$params['ShaoJieFuLiao']."',ShaojieDlrgMakeFei='".$params['ShaojieDlrgMakeFei']."',LianTieFuLiao='".$params['LianTieFuLiao']."',LianTieDlrgMakeFei='".$params['LianTieDlrgMakeFei']."',ShuiZhaHuiShou='".$params['ShuiZhaHuiShou']."',adminid='".$userid."',adminname='".$TrueName."',Type='".$params['type']."',CreateDate=now(),ndate='".$date."',mc_type=2");
            $pid = $this->_dao->insert_id();
			
			$params['isSave'] = "1";
			if($params['cbmodel'] == "2")
			{
				$jisuan = $this->jsgpcbweb($params,$date);
				//更新任务表相同日期状态
				$sql = "select mTask.id from sg_ModifyChengBenParameterTask mTask,sg_ChengBenParameter2 cb2 where cb2.id=mTask.pid and mTask.status=0 and cb2.ndate='$date' and mTask.Type=2";
			}
			else if($params['cbmodel'] == "3")
			{
				$jisuan = $this->jsgccbweb($params,$date);
				//更新任务表相同日期状态
				$sql = "select mTask.id from sg_ModifyChengBenParameterTask mTask,sg_ChengBenParameter3 cb3 where cb3.id=mTask.pid and mTask.status=0 and cb3.ndate='$date' and mTask.Type=3";
			}
			else if($params['cbmodel'] == "4")
			{
				$jisuan = $this->jsdtcbweb($params,$date);
				//更新任务表相同日期状态
				$sql = "select mTask.id from sg_ModifyChengBenParameterTask mTask,sg_ChengBenParameter4 cb4 where cb4.id=mTask.pid and mTask.status=0 and cb4.ndate='$date' and mTask.Type=4";
			}
			else
			{
                $jisuan = $this->jstscbweb($params,$date);
				//更新任务表相同日期状态
				$sql = "select mTask.id from sg_ModifyChengBenParameterTask mTask,sg_ChengBenParameter cb where cb.id=mTask.pid and mTask.status=0 and cb.ndate='$date' and mTask.Type=1";
			}
            $task_pid = $this->_dao->query($sql);
            foreach($task_pid as $key=>$tmp){
                $this->_dao->execute("update sg_ModifyChengBenParameterTask set status='2' where id = '".$tmp['id']."'");
            }      
            
            //修改历史记录
            $this->_dao->execute( "INSERT INTO sg_ModifyChengBenParameterTask SET pid='".$pid."',Type='".$params['cbmodel']."',status=0");
            $tip = "保存成功";
        }
		
		if($params['cbmodel'] == "2")
			gourl("./sgtieshuicb.php?view=gangpiindex&GUID=".$GUID."&type=".$type."&mode=".$params['mode']."&pid=".$pid."&date=".$date."&ptgangpi_sum=".$jisuan['PTPCB']."&ptgangpi_sum_tax=".$jisuan['PTPCB_HS']."&lwgangpi_sum=".$jisuan['LWPCB']."&lwgangpi_sum_tax=".$jisuan['LWPCB_HS']."&tip=".$tip);
		else if($params['cbmodel'] == "3")
			gourl("./sgtieshuicb.php?view=gangcaiindex&GUID=".$GUID."&type=".$type."&mode=".$params['mode']."&pid=".$pid."&date=".$date."&lwggangcai_sum=".$jisuan['LWCB']."&lwggangcai_sum_tax=".$jisuan['LWCB_HS']."&plgangcai_sum=".$jisuan['PLCB']."&plgangcai_sum_tax=".$jisuan['PLCB_HS']."&xcgangcai_sum=".$jisuan['XCCB']."&xcgangcai_sum_tax=".$jisuan['XCCB_HS']."&dggangcai_sum=".$jisuan['DGCB']."&dggangcai_sum_tax=".$jisuan['DGCB_HS']."&tip=".$tip);
		else if($params['cbmodel'] == "4")
			gourl("./sgtieshuicb.php?view=longhangangindex&GUID=".$GUID."&type=".$type."&mode=".$params['mode']."&pid=".$pid."&date=".$date."&shaojiekuang_sum=".$jisuan['SJKCB']."&shengtie_sum=".$jisuan['STCB']."&gangpi_sum=".$jisuan['GPCB']."&gangcai_sum=".$jisuan['GCCB']."&duncai_sum=".$jisuan['DCLR']."&tip=".$tip);
		else
			gourl("./sgtieshuicb.php?view=index&GUID=".$GUID."&type=".$type."&mode=".$params['mode']."&pid=".$pid."&date=".$date."&tieshui_sum=".$jisuan['chenben']."&tieshui_sum_tax=".$jisuan['chenben_tax']."&tip=".$tip);
    }

	public function gangpiindex($params)
    {
		$GUID = $params['GUID'];
		$mode = $params['mode'];//1=客户端，2=安卓，3=iOS

        if($GUID==""){
            $params['tip'] = "GUID不能为空！";       
        }
		
		//获取用户信息
		$user_infos = $this->t1dao->get_userinfo_byguid($GUID);
		$Mid=$user_infos['Mid'];//会员id
		$userid=$user_infos['Uid'];//用户id
		$TrueName=$user_infos['TrueName'];//用户id
		
		//获取用户权限和用户名app_license_privilege
		$license_detail_info = $this->t1dao->get_license_privilege($userid);
      
        $this->assign("cb_privilege",$license_detail_info['cb_privilege']);
        $this->assign("mode",$mode);
        $this->assign("tip",$params['tip']);
        $this->assign("pid",$params['pid']);
        $this->assign("ptgangpi_sum",$params['ptgangpi_sum']);
        $this->assign("ptgangpi_sum_tax",$params['ptgangpi_sum_tax']);
        $this->assign("lwgangpi_sum",$params['lwgangpi_sum']);
        $this->assign("lwgangpi_sum_tax",$params['lwgangpi_sum_tax']);
		$this->assign("Mid",$Mid);

        $where = " and isdel=0 ";
        if($params['date']!=""){
            $date = $params['date'];
            $where .=" and ndate<='".$date."' ";
        }
        if($params['Type']!=""){
            $params['type'] = $params['Type'];
        }
        if($params['type']!=""){
            $where .=" and Type='".$params['type']."' ";
        }
        if($params['id']!=""){
            $where .=" and id='".$params['id']."' ";
        }
		
        $info = $this->_dao->get_ChengBenParameter2($where);	  
        $this->assign("info",$info);

        $total = $this->_dao->getOne( "SELECT COUNT(id) as c FROM  sg_ChengBenParameter2 where Type='".$params['type']."' and isdel=0 " );
        $page = $params['page'] == '' ? 1 : $params['page'];
        $per = 10;
        $start = ( $page - 1 ) * $per;
        $url = "sgtieshuicb.php";

        $data = $this->_dao->getAll2($params['type'],$start,$per); 
      
        $this->assign("data",$data);
        $this->assign("page",$page);
        $this->assign("tatalpage",ceil($total/$per));
        
        if($params['date']==""){
            
            $flag = 1;
            $lastday = date("Y-m-d");
            while(true)
            {
                $lastday=date('Y-m-d',strtotime('-'.$flag.' day',strtotime($lastday)));
                if(file_get_contents(APP_URL_WWW.'/isholiday.php?date='.$lastday)!="1")
                { 
                  break;
                } 
            }
            $params['date'] = $lastday;
        }
		
		$jiageid = $info['FgPriceidA'].",".$info['GmPriceidB'].",".$info['GtPriceidC'].",".$info['FdPriceidD'];
		if($info['type'] == "6" || $info['type'] == "7")
			$jiageid .= ",".$info['CggldjPriceid'].",".$info['GgldjPriceid'];
		
		if($jiageid != "" && !empty($info))
		$jiage_arr = $this->maindao->getpricebypricestr($jiageid,$params['date']);
		
		$jiage_arr['FgPrice'] = $jiage_arr[$info['FgPriceidA']];
        $jiage_arr['GmPrice'] = $jiage_arr[$info['GmPriceidB']];
        $jiage_arr['GtPrice'] = $jiage_arr[$info['GtPriceidC']];
        $jiage_arr['FdPrice'] = $jiage_arr[$info['FdPriceidD']];
		if($info['type'] == "6" || $info['type'] == "7")
		{
			$jiage_arr['CggldjPrice'] = $jiage_arr[$info['CggldjPriceid']];
			$jiage_arr['GgldjPrice'] = $jiage_arr[$info['GgldjPriceid']];
		}
		
		$tstype = 5+($params['type']-1)*30;
		//铁水成本不含税
		$tssql="select chenben from sg_HangYeChengBenIndex where type='".$tstype."' and ndate='".$params['date']."' and mc_type=2";
		$jiage_arr['TieShuiCBBHS'] = $this->_dao->getOne($tssql);
		
		$this->assign("jiage_arr",$jiage_arr);
		
        if($mode==1){
            $head_url = "//".$_SERVER['SERVER_NAME']."/v1.5/css/sg_tablestyle_small.css";
        }else if($mode==3||$mode==5){
            $head_url = "//".$_SERVER['SERVER_NAME']."/v1.5/css/sg_tablestyle_middle.css";
        }else if($mode==2||$mode==4){
            $head_url = "//".$_SERVER['SERVER_NAME']."/v1.5/css/sg_tablestyle_large.css";
        }else{
            $head_url = "//".$_SERVER['SERVER_NAME']."/v1.5/css/sg_tablestyle_middle.css";
        }
		$style_url= dirname("//".$_SERVER['SERVER_NAME'].$_SERVER['PHP_SELF']);
		$this->assign( "style_url", $style_url );
		$this->assign("head_url",$head_url);
        $this->assign("params",$params);
    }
	
	public function gangcaiindex($params)
    {
		$GUID = $params['GUID'];
		$mode = $params['mode'];//1=客户端，2=安卓，3=iOS
	
        if($GUID==""){
            $params['tip'] = "GUID不能为空！";       
        }
		
		//获取用户信息
		$user_infos = $this->t1dao->get_userinfo_byguid($GUID);
		$Mid=$user_infos['Mid'];//会员id
		$userid=$user_infos['Uid'];//用户id
		$TrueName=$user_infos['TrueName'];//用户id
		
		//获取用户权限和用户名app_license_privilege
		$license_detail_info = $this->t1dao->get_license_privilege($userid);
        
        $this->assign("cb_privilege",$license_detail_info['cb_privilege']);
        $this->assign("mode",$mode);
        $this->assign("tip",$params['tip']);
        $this->assign("pid",$params['pid']);
        $this->assign("lwggangcai_sum",$params['lwggangcai_sum']);
        $this->assign("lwggangcai_sum_tax",$params['lwggangcai_sum_tax']);
        $this->assign("plgangcai_sum",$params['plgangcai_sum']);
        $this->assign("plgangcai_sum_tax",$params['plgangcai_sum_tax']);
        $this->assign("xcgangcai_sum",$params['xcgangcai_sum']);
        $this->assign("xcgangcai_sum_tax",$params['xcgangcai_sum_tax']);
        $this->assign("dggangcai_sum",$params['dggangcai_sum']);
        $this->assign("dggangcai_sum_tax",$params['dggangcai_sum_tax']);
		$this->assign("Mid",$Mid);

        $where = " and isdel=0 ";
        if($params['date']!=""){
            $date = $params['date'];
            $where .=" and ndate<='".$date."' ";
        }
        if($params['Type']!=""){
            $params['type'] = $params['Type'];
        }
        if($params['type']!=""){
            $where .=" and Type='".$params['type']."' ";
        }
        if($params['id']!=""){
            $where .=" and id='".$params['id']."' ";
        }
		
        $info = $this->_dao->get_ChengBenParameter3($where);	  
        $this->assign("info",$info);
		
		if($params['type'] != "6" || $params['type'] != "7")
		{
			$pttype = 10+($params['type']-1)*30;
			$lwtype = 11+($params['type']-1)*30;
		}
		if($params['type'] == "6")
		{
			$pttype = 158;
			$lwtype = 159;
		}
		if($params['type'] == "7")
		{
			$pttype = 188;
			$lwtype = 189;
		}
		//普碳坯成本不含税
		$ptpsql="select chenben from sg_HangYeChengBenIndex where type='".$pttype."' and ndate<='".$params['date']."' and mc_type=2 limit 1";
		$jiage_arr['PuTanPiCBBHS'] = $this->_dao->getOne($ptpsql);
		//螺纹坯成本不含税
		$lwpsql="select chenben from sg_HangYeChengBenIndex where type='".$lwtype."' and ndate<='".$params['date']."' and mc_type=2 limit 1";
		$jiage_arr['LuoWenPiCBBHS'] = $this->_dao->getOne($lwpsql);
		
		$this->assign("jiage_arr",$jiage_arr);

        $total = $this->_dao->getOne( "SELECT COUNT(id) as c FROM  sg_ChengBenParameter3 where Type='".$params['type']."' and isdel=0 " );
        $page = $params['page'] == '' ? 1 : $params['page'];
        $per = 10;
        $start = ( $page - 1 ) * $per;
        $url = "sgtieshuicb.php";

        $data = $this->_dao->getAll3($params['type'],$start,$per); 
      
        $this->assign("data",$data);
        $this->assign("page",$page);
        $this->assign("tatalpage",ceil($total/$per));
        
        if($params['date']==""){
            $flag = 1;
            $lastday = date("Y-m-d");
            while(true)
            {
                $lastday=date('Y-m-d',strtotime('-'.$flag.' day',strtotime($lastday)));
                if(file_get_contents(APP_URL_WWW.'/isholiday.php?date='.$lastday)!="1")
                { 
                  break;
                } 
            }
            $params['date'] = $lastday;
        }
		
        if($mode==1){
            $head_url = "//".$_SERVER['SERVER_NAME']."/v1.5/css/sg_tablestyle_small.css";
        }else if($mode==3||$mode==5){
            $head_url = "//".$_SERVER['SERVER_NAME']."/v1.5/css/sg_tablestyle_middle.css";
        }else if($mode==2||$mode==4){
            $head_url = "//".$_SERVER['SERVER_NAME']."/v1.5/css/sg_tablestyle_large.css";
        }else{
            $head_url = "//".$_SERVER['SERVER_NAME']."/v1.5/css/sg_tablestyle_middle.css";
        }
		$style_url= dirname("//".$_SERVER['SERVER_NAME'].$_SERVER['PHP_SELF']);
		$this->assign( "style_url", $style_url );
		$this->assign("head_url",$head_url);

        $this->assign("params",$params);
		
    }

	public function longhangangindex($params)
    {
		$GUID = $params['GUID'];
		$mode = $params['mode'];//1=客户端，2=安卓，3=iOS
	
        if($GUID==""){
            $params['tip'] = "GUID不能为空！";       
        }
		
		//获取用户信息
		$user_infos = $this->t1dao->get_userinfo_byguid($GUID);
		$Mid=$user_infos['Mid'];//会员id
		$userid=$user_infos['Uid'];//用户id
		$TrueName=$user_infos['TrueName'];//用户id
		
		//获取用户权限和用户名app_license_privilege
		$license_detail_info = $this->t1dao->get_license_privilege($userid);
      
        $this->assign("cb_privilege",$license_detail_info['cb_privilege']);
        $this->assign("mode",$mode);
        $this->assign("tip",$params['tip']);
        $this->assign("pid",$params['pid']);
		$this->assign("shaojiekuang_sum",$params['shengtie_sum']);
        $this->assign("shengtie_sum",$params['shengtie_sum']);
        $this->assign("gangpi_sum",$params['gangpi_sum']);
        $this->assign("gangcai_sum",$params['gangcai_sum']);
        $this->assign("duncai_sum",$params['duncai_sum']);
		$this->assign("Mid",$Mid);

        if($params['date']==""){
            $flag = 1;
            $lastday = date("Y-m-d");
            while(true)
            {
                $lastday=date('Y-m-d',strtotime('-'.$flag.' day',strtotime($lastday)));
                if(file_get_contents(APP_URL_WWW.'/isholiday.php?date='.$lastday)!="1")
                { 
                  break;
                } 
            }
            $params['date'] = $lastday;
        }
		
        $where = " and isdel=0 ";
        if($params['date']!=""){
            $date = $params['date'];
            $where .=" and ndate<='".$date."' ";
        }
        if($params['Type']!=""){
            $params['type'] = $params['Type'];
        }
        if($params['type']!=""){
            $where .=" and type='".$params['type']."' ";
        }
        if($params['id']!=""){
            $where .=" and id='".$params['id']."' ";
        }
		
        $info = $this->_dao->get_ChengBenParameter4($where);	
		
		if(empty($info))
		{
			/*$jiage_arr['H98640'] = $info['Pszhishu'];
			$jiage_arr['668312'] = $info['JiaotanPrice'];
			$jiage_arr['K16330'] = $info['PenchuimeiPrice'];
			$jiage_arr['438510'] = $info['ShengtiekuangPrice'];
			$jiage_arr['shuilv'] = $this->maindao_test->get_master_cntaxrate($date,1) * 100;
			$jiage_arr['erjiprice'] = $info['Gangcaierjishoujia'];
			$jiage_arr['yunfeishuilv'] = $this->maindao_test->get_master_cntaxrate($date,2) * 100;
			$jiage_arr['USRMB'] = $info['USRMB'];
			$jiage_arr['746330'] = $info['PenchuimeiPrice'];
			$jiage_arr['KuaikuangPrice'] = $info['KuaikuangPrice'];
			$jiage_arr['QiutuankuangPrice'] = $info['QiutuankuangPrice'];*/
			if($params['type'] == "1")
			{
				$info['ImportDaochangyf'] = "167";
				$info['ImportHanshuilv'] = "8";
				$info['ImportKuanggangzf'] = "25";
				$info['ImportPinwei'] = "62";
				$info['KuaikaungBili'] = "11.5";
				$info['QiutuankuangBili'] = "11.5";
				$info['ShaojiekuangBili'] = "77";
				$info['ShaojiekuangJiagong'] = "160";
				$info['ShaojieChengpinlv'] = "92";
				$info['ShaojieHantieliang'] = "55.6";
				$info['Jiaobi'] = "0.45";
				$info['JiaomohuishouPrice'] = "500";
				$info['Jiaomohuishengliang'] = "0.025";
				$info['Penmeiliang'] = "0.14";
				$info['ShengtiekuangYongliang'] = "0.02";
				$info['LiantieJiagongfei'] = "120";
				$info['LiantieHuishouxiang'] = "25";
				$info['DuntieKuanghao'] = "1.65";
				$info['GangpiJiagongfei'] = "450";
				$info['Gangcaijiagongfei'] = "110";
				$info['QijianFeiyong'] = "100";
				$info['Gangcaiyunfeidailifei'] = "130";
			}
			else
			{
				$info['ImportDaochangyf'] = "220";
				$info['ImportHanshuilv'] = "8";
				$info['ImportKuanggangzf'] = "25";
				$info['ImportPinwei'] = "62";
				$info['QiutuankuangBili'] = "20";
				$info['ShaojiekuangBili'] = "80";
				$info['ShaojiekuangJiagong'] = "165";
				$info['ShaojieChengpinlv'] = "91.5";
				$info['ShaojieHantieliang'] = "55.85";
				$info['Jiaobi'] = "0.45";
				$info['JiaomohuishouPrice'] = "750";
				$info['Jiaomohuishengliang'] = "0.037";
				$info['Penmeiliang'] = "0.13";
				$info['LiantieJiagongfei'] = "100";
				$info['DuntieKuanghao'] = "1.68";
				$info['GangpiJiagongfei'] = "420";
				$info['Gangcaijiagongfei'] = "125";
				$info['QijianFeiyong'] = "98";
				$info['Gangcaiyunfeidailifei'] = "130";
				$info['Otherfee'] = "31";
			}
		}
		//else
		//{
			

			if($params['type'] == "1")
			{
				$priceid = "H98640,A183112,M163302,438510,D28630,578620";
				$L= $this->getsgdata($date," and dta_1='西咸片区' and dta_3='龙钢' ");
			}
			else
			{
				$priceid = "H98640,668312,746330,678620";
				$L= $this->getsgdata($date," and dta_1='成都片区' and dta_3='汉钢' ");
			}
			$jiage_arr = $this->maindao->getpricebypricestr($priceid,$params['date']);
			
			$jiage_arr['shuilv'] = $this->maindao->get_master_cntaxrate($date,1) * 100;//税率
			$jiage_arr['erjiprice'] = $L;
			$jiage_arr['yunfeishuilv'] = $this->maindao->get_master_cntaxrate($date,2) * 100;//运费税率
			$jiage_arr['USRMB'] = $this->_dao->getOne("select rd1 from rmbrate WHERE 1 and rdate <='".$params['date']." 23:59:59' order by rdate desc limit 1");

			if($params['type'] == "1")
			{
				$jiage_arr['KuaikuangPrice'] = round($jiage_arr['D28630'] / 0.94 / ( 1 + $jiage_arr['shuilv']/100) + $info['ImportDaochangyf'] / (1 + $jiage_arr['yunfeishuilv']/100));
				$jiage_arr['QiutuankuangPrice'] = round($jiage_arr['578620'] / ( 1 + $jiage_arr['shuilv']/100));
			}
			else
				$jiage_arr['QiutuankuangPrice'] = round($jiage_arr['678620'] / ( 1 + $jiage_arr['shuilv']/100));

			if($jiage_arr['H98640'] == "")
				$jiage_arr['H98640'] = $this->maindao->getOne("select pricemk from marketconditions where marketconditions.topicture='H98640' and mconmanagedate<='".$date." 23:59:59' order by mconmanagedate desc limit 1 ");
		//}
		
        $this->assign("info",$info);
		$this->assign("jiage_arr",$jiage_arr);

        $total = $this->_dao->getOne( "SELECT COUNT(id) as c FROM  sg_ChengBenParameter4 where Type='".$params['type']."' and isdel=0 " );
        $page = $params['page'] == '' ? 1 : $params['page'];
        $per = 10;
        $start = ( $page - 1 ) * $per;
        $url = "sgtieshuicb.php";

        $data = $this->_dao->getAll4($params['type'],$start,$per); 
      
        $this->assign("data",$data);
        $this->assign("page",$page);
        $this->assign("tatalpage",ceil($total/$per));
        
        if($mode==1){
            $head_url = "//".$_SERVER['SERVER_NAME']."/v1.5/css/sg_tablestyle_small.css";
        }else if($mode==3||$mode==5){
            $head_url = "//".$_SERVER['SERVER_NAME']."/v1.5/css/sg_tablestyle_middle.css";
        }else if($mode==2||$mode==4){
            $head_url = "//".$_SERVER['SERVER_NAME']."/v1.5/css/sg_tablestyle_large.css";
        }else{
            $head_url = "//".$_SERVER['SERVER_NAME']."/v1.5/css/sg_tablestyle_middle.css";
        }
		$style_url= dirname("//".$_SERVER['SERVER_NAME'].$_SERVER['PHP_SELF']);
		$this->assign( "style_url", $style_url );
		$this->assign("head_url",$head_url);

        $this->assign("params",$params);
		
    }

	public function getsgdata($Date,$where)
	{
		$SGURL1=SGDCURL.'/web/systemapi.php?action=gzjSysLogin';
		$mess=$this->post_data( $SGURL1.'&username=testuser&pwd=123456&mc_type=2');
		
		$SGURL=SGDCURL.'/web/systemapi.php?action=getdb';
		$dtjson_arr[]=array(
			'dtid'=>'',
			'tablename'=>'sg_data_table',
			'sqlmain'=>base64_encode(iconv('gbk','utf-8',"WHERE dta_ym<='".$Date."' and dta_2='螺纹钢 Φ20' and dta_type='销售指导价' ".$where." order by dta_ym desc limit 1")),
			'dbfield'=>'dta_4',
			'datefield'=>''
			);
		
		$queryjson=json_encode($dtjson_arr);
		$mess=$this->post_data( $SGURL.'&queryjson='.$queryjson.'&token='.$mess['Token'].'&mc_type=2&GUID='.$params['GUID']);	
		$sg_data_arr=$mess['Results'];
		//echo '<pre/>';print_r($sg_data_arr);
				
		foreach ($sg_data_arr as $sdv) {
			if(!empty($sdv['data'])){
				foreach ($sdv['data'] as $sdv1) {
					$sg_data =$sdv1['dta_4'];
				}
			}
		}

		return $sg_data;
	}

	function post_data($url, $param=array(), $is_file = false, $return_array = true) {
			set_time_limit ( 0 );
			if (! $is_file && is_array ( $param ) && !empty($param)) {
				$param = json_encode( $param );
			}
			if ($is_file) {
				$header [] = "content-type: multipart/form-data; charset=UTF-8";
			} else {
				$header [] = "content-type: application/json; charset=UTF-8";
			}
			$ch = curl_init ();
			if (class_exists ( '/CURLFile' )) { 
				curl_setopt ( $ch, CURLOPT_SAFE_UPLOAD, true );
			} else {
                if (class_exists('\CURLFile')) {// 这里用特性检测判断php版本
                    curl_setopt($ch, CURLOPT_SAFE_UPLOAD, true);
                } else {
                    if (defined('CURLOPT_SAFE_UPLOAD')) {
                        curl_setopt($ch, CURLOPT_SAFE_UPLOAD, false);
                    }
                }
			}
			curl_setopt ( $ch, CURLOPT_URL, $url );
			curl_setopt ( $ch, CURLOPT_CUSTOMREQUEST, "POST" );
			curl_setopt ( $ch, CURLOPT_SSL_VERIFYPEER, FALSE );
			curl_setopt ( $ch, CURLOPT_SSL_VERIFYHOST, FALSE );
			curl_setopt ( $ch, CURLOPT_HTTPHEADER, $header );
			curl_setopt ( $ch, CURLOPT_USERAGENT, 'Mozilla/4.0 (compatible; MSIE 5.01; Windows NT 5.0)' );
			curl_setopt ( $ch, CURLOPT_FOLLOWLOCATION, 1 );
			curl_setopt ( $ch, CURLOPT_AUTOREFERER, 1 );
			curl_setopt ( $ch, CURLOPT_POSTFIELDS, $param );
			curl_setopt ( $ch, CURLOPT_RETURNTRANSFER, true );
			$res = curl_exec ( $ch );
			$flat = curl_errno ( $ch );
			if ($flat) {
				$data = curl_error ( $ch );
			}
			
			curl_close ( $ch );
			$return_array && $res = json_decode ( $this->checkbom($res), true );
			return $res;
		}

		function checkbom($str,$remove = true) {
			$charset[1] = substr($str, 0, 1);
			$charset[2] = substr($str, 1, 1);
			$charset[3] = substr($str, 2, 1);
			if (ord($charset[1]) == 239 && ord($charset[2]) == 187 && ord($charset[3]) == 191) {
				return substr($str, 3);
			}
			else{
				return $str;
			}
		}
} 
?>