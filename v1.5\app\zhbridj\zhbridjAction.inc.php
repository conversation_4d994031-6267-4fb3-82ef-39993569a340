<?php 
// require_once('../../../../steelconf_v3/debug.php');
class zhbridjAction extends AbstractAction
{ 
    public function __construct()
    {
        parent::__construct();
    } 
    public function index($params)
    {
    	//echo $params['GUID'];
			$sql="select * from app_session_temp where  GUID ='".$params['GUID']."'  order by  LoginDate DESC  limit 1";
			$res=$this->_dao->getRow($sql);
			
			$Mid=$res['Mid'];
			$uid=$res['Uid'];
			$type=$params['Type'];
			$username=$res['UserName'];
			$isHuiZong = $params['HuiZong'];
			
			$SignCS=$res['SignCS'];
			$sql="select * from app_license where Mid='$Mid'   and  mc_type=1 order by CreateDate desc limit 1";
			$res=$this->_dao->getRow($sql);		
			$id=$res['ID'];
			$sql="select * from app_license_privilege  where liD ='".$id."' and privilege!='' and mc_type='1' ";
			$res=$this->_dao->query($sql); 
			
			foreach($res as $key => $value){
				$res[$key]['order']='100';
				// echo "<pre/>";echo '排序';print_r($value['orderno']);
				// echo "<pre/>";echo '权利';print_r($value['privilege']);
					
					
				$r=explode(',',$value['privilege']);
				$d=explode(',',$value['orderno']);
				// echo "<pre/>";echo '分割';print_r($d[$type-1]);
				if( $r[$type-1]==1){
					//判断领导的顺序进行排序 给数组一个新字段
					$res[$key]['order']=$d[$type-1];
			
				}
				// echo "<pre/>";echo '分割';print_r($r);exit;
					
			}
			$order=array();
			foreach($res as $re){
				$order[]=$re['order'];
			}
			array_multisort($order,SORT_ASC,$res);//安照order来排序
			
			foreach($res as $key =>$v){
				$r=explode(',',$v['privilege']);
				if(!array_key_exists($r[$type-1],$GLOBALS['power'])) continue;
				$p_arr[$v['username']]=$r;
				//$use[]=$v['username'];
				$adminname_uid[$v['username']]=$v['uid'];
				$adminname[$v['username']]=$v['truename'];
			}
			
			//$use=array_unique($use);
			//$use_str="'".implode("','",$use)."'";
			//$res=$this->steelhome->query("select * from adminuser where username in($use_str)");
				//foreach($res as $key => $v){
				//	$adminname_uid[$v['username']]=$v['id'];
				//	$adminname[$v['username']]=$v['truename'];
				//}
			//$sql="select * from ng_price_model where modeltype=$type and   createtime>='".date('Y-m-d',time())." 00:00:00' and  createtime<='".date('Y-m-d',time())." 23:59:59' order by createtime desc limit 1";	
			if($params['curdate']!=''){
				$date=$params['curdate'];
			}else{
				$date = date("Y-m-d");
			}
			
			
			
			$sql="select * from ng_price_model where modeltype=$type and  date='".$date."' order by id desc limit 1";
			//echo $sql;
			//echo "<pre/>";print_r($this->drc);
			$modeldb=$this->drc->getrow($sql);
			//print_r($modeldb);
			
			//echo $sql;
		//	echo '<pre/>';
     	//		print_r($modeldb);exit;
		//$modeldb='';
		//	echo '1'.$modeldb['modelcontent'];exit;
		
	
		    if(!empty($modeldb)){
		    	//echo '1'.$modeldb['modelcontent'];exit;
			$modeldb['modelcontent']=html_entity_decode($modeldb['modelcontent'],ENT_QUOTES,'GB18038');//echo "<pre/>";print_r($modeldb);

			$uidsavebtn=false;
			$uidleadersavebtn=false;
			foreach($p_arr as $key => $v){
				$master[$adminname_uid[$key]]=$v[($type-1)];
				$thisuid=$adminname_uid[$key];
				$result=$this->drc->query("select id,modelid,modelprice,modelprice_updown,ABS(modelprice_updown) as abs_modelprice_updown,modelpirce_name,date,uid,modeloldprice from ng_price_model_detail where modelprice_type=$type and modelid=".$modeldb["id"]." and uid='".$thisuid."'   order by modelpirce_name_sort  asc");
		          
				if(empty($result)){
			      $result=$this->drc->query("select  0 as id,'' as modelid,'' as modelprice,'' as modelprice_updown,'' as abs_modelprice_updown,modelpirce_name, '' as date,'".$thisuid."' as uid,modeloldprice  from ng_price_model_detail where modelprice_type=$type and modelid=".$modeldb["id"]." and uid='-1'   order by modelpirce_name_sort  asc");	 
				}
				if($v[($type-1)]==1){//权限划分分管领导和决策人
					$master_leader[]=array(
						'uid'=>$adminname_uid[$key],
						'username'=>$key,
						'power'=>$v[($type-1)],
						'truename'=>$adminname[$key],
						'res'=>$result
						);
					if($adminname_uid[$key]==$uid){
						$uidleadersavebtn=true;
					}
				}else if($v[($type-1)]==2){
					$master_price[]=array(
						'uid'=>$adminname_uid[$key],
						'username'=>$key,
						'power'=>$v[($type-1)],
						'truename'=>$adminname[$key],
						'res'=>$result
						);
					if($adminname_uid[$key]==$uid){
						$uidsavebtn=true;
					}
				}
			}
			$show='';

			//权限为3，市场部人员让他与最后一个分管领导操作相同，这里把登录的uid换成最后一个分管领导的
			$record=$this->drc->query("SELECT * FROM  `Ng_RecordManage` where modelid='".$modeldb["id"]."'");
			$last=	end($master_leader);
			if(!empty($record)&&$master[$uid]==1&&$uid!=$last['uid']){
				/* if($params['show']!='1'){
					$url = "//".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']."&show=1";
					gourl($url);
				} */
				$show=1;
				
			} 
			if($master[$uid]==3){
			   
				$last=	end($master_leader); 
				
		        $uid=$last['uid'];
		        $sql="select * from app_session_temp where  Uid ='".$uid."'  order by  LoginDate DESC  limit 1";
		        $m=$this->_dao->getRow($sql);
		        $Mid=$m['Mid'];
		        $uidleadersavebtn=true;
			}
			if($master[$uid]==4||$master[$uid]==''){
			
				$youke=1;
				$this->assign( "youke", $youke );//决策人进来查看 传的值
			}
			
				
			//---
			foreach($master_price as $k){
				$uid_arr[]=$k['uid'];
			}
			foreach($master_leader as $k2){
				$uid_arr2[]=$k2['uid'];
			}
			$privilege=$master[$uid];//当前用户权限
			//------------------
			//如果当前用户权限检测为市场部，他将拥有最后一个分管领导的功能
			
			//echo '1'.$privilege;
			//----------------------
			
			if($isHuiZong=='1'&&$privilege==''){
				echo "-1";exit;
			}
			
			
			
			$this->assign( "params", $params);
			$this->assign( "uid", $uid);
			$this->assign( "mid", $Mid );
			$this->assign( "modeldb", $modeldb );//echo "<pre/>";print_r($modeldb);
			$this->assign( "uidsavebtn", $uidsavebtn );
			$this->assign( "uidleadersavebtn", $uidleadersavebtn );
			$this->assign( "uid_arr", $uid_arr );
			
			$outlook='0';
			foreach($uid_arr as $key=>$value){
				$sql="select * from  ng_price_model_detail where uid='$value'
				and modelid=".$modeldb["id"]." and modelprice_type=$type";
				  $out=$this->drc->query($sql);
							  // echo "<pre/>";print_r($out);
				if(empty($out)){
				$outlook='1';
				break;
				}
				}
				$this->assign( "outlook", $outlook);//第一层限制 决策未决策 分管领导不能操作 0表示全部决策 1 表示尚有未决策
			
			
						
			
			
					$shunxu=array_keys($uid_arr2,$uid,true);
				$outlook2='0';
				if($shunxu[0]!=0){
				for($i=0;$i<$shunxu[0];$i++){
					
				$sql="select * from  ng_price_model_detail where uid='$uid_arr2[$i]'
					and modelid=".$modeldb["id"]." and modelprice_type=$type";
				$out=$this->drc->query($sql);
			
							if(empty($out)){
							$outlook2='1';
					break;
				}
				}
				}
					 $nextmarket='0';
		if($shunxu[0]!=(count($uid_arr2)-1)){
		     // echo $uid_arr2[$shunxu[0]+1];
			 // echo'<br/>'; print_r ($uid_arr2);
			 $uidpre=$uid_arr2[$shunxu[0]+1];
			 
			 $sql="select * from  ng_price_model_detail where uid='$uidpre'
                  and modelid=".$modeldb["id"]." and modelprice_type =$type ";
			$out=$this->drc->query($sql);//如果为空则后一个人未决策 
			if(empty($out)){
				$nextmarket='1';//
			}
		}
               $shunxu2=0;
		    if($shunxu[0]!=0){
			   $shunxu2=$shunxu[0]-1;//查前面的的人有没有决策
			 }else{
				 $shunxu2=0;
				 }   
				$sql5="select * from  ng_price_model_detail where uid='$uid_arr2[$shunxu2]'
                                     and modelid='".$modeldb["id"]."' and modelprice_type =$type ";
               $preout=$this->drc->query($sql5);
				
				 $master_leader2=array();
				if($outlook2=='1'&&empty($preout)){
				
					for($i=0;$i<$shunxu[0];$i++){
					$master_leader2[$i]= $master_leader[$i];
				
					   }
			    }else{
					for($i=0;$i<=$shunxu[0];$i++){
					$master_leader2[$i]= $master_leader[$i];
					   }
				}
					
					
				
			
				// exit;
					$this->assign( "outlook2", $outlook2);
			
			
			
			$this->assign( "uid_arr2", $uid_arr2 );
			$this->assign( "master_price", $master_price );//echo "<pre/>";print_r($master_price);
			
			
			if(in_array($uid,$uid_arr2)){
				
				$master_leader3=array();
				if($shunxu[0]=='0'){//第一个特殊处理
					
					// if($outlook=='1'){//表示有决策人未决策
						 
					// }else{	
					    for($i=0;$i<count($uid_arr2);$i++){
						     $sql="select * from  ng_price_model_detail where uid='$uid_arr2[$i]'
                                     and modelid='".$modeldb["id"]."' and modelprice_type =$type ";
				         $out=$this->drc->query($sql);
						 
						  
							
						  if(empty($out)&&$uid_arr2[$i]!=$uid){//修改1
							  
							  break;
						  }
						  
						 if($outlook!='1'){
						    $master_leader3[$i]= $master_leader[$i];
						   }
						  
					   } 
					   
					   	// echo '5555'; echo $nextmarket;
						
							     $sql="select * from  ng_price_model_detail where uid='$uid'
                                     and modelid='".$modeldb["id"]."' and modelprice_type =$type ";
				                 $out=$this->drc->query($sql);
						
						
						
						 if($nextmarket!='1'&&($shunxu[0]!=(count($uid_arr2)-1))){//1 后面那个未决策 0 后面的哪个人已经决策
						 // if(!empty($out)){
							   // echo '1111';
							 
							  /* if($params['show']!=1){
								
								$url = "//".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']."&show=1";
								goUrl($url);
								
							} */
						 	$show=1;
						 }
						 // }
					// }
					
					  if($outlook=='1'&&empty($out)){
						  /*  if($params['show']!=1){
								 $url = "//".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']."&show=1";
								 goUrl($url);
						     } */
					  	$show=1;
					       } 
					
					
				     $this->assign( "master_leader", $master_leader3 );//echo "<pre/>";print_r($master_leader3);//分管领导
				}else{//第二个人之后
				
				
				  // echo "<pre/>";print_r($master_leader2);	
						   $sql4="select * from  ng_price_model_detail where uid='$uid'
                                     and modelid='".$modeldb["id"]."' and modelprice_type =$type ";
						  
						  $out=$this->drc->query($sql4);
						  
						  if(!empty($out)){
							  
							  	 for($i=0;$i<=$shunxu[0];$i++){
						// $sql="select * from  ng_price_model_detail where uid='$uid_arr2[$i]'
                         // and createtime>='$this_xun_start' and  createtime<='$this_xun_end' and modelprice_type =$type ";
				         // $out=$this->drc->query($sql);   
				     $master_leader2[$i]= $master_leader[$i];
					 	// if(empty($out)){break;}
				                   } 
							  
						  }
						  //修改
				
				
							if($outlook2=='1'&&empty($out)){
								if(empty($preout)){
								/* 	if($params['show']!=1){
										  $url = "//".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']."&show=1";
										   goUrl($url);
									} */
									$show=1;
								}
							}else{
						
						//表示前面的人已经决策
						 //判断在自己后面的人有没有决策
						// echo '5555'; echo $nextmarket;
						
							     // $sql="select * from  ng_price_model_detail where uid='$uid'
                                     // and modelid='".$modeldb["id"]."' and modelprice_type =$type ";
				                    // $out=$this->drc->query($sql);
						
						
									 if($nextmarket!='1'&&($shunxu[0]!=(count($uid_arr2)-1))){//1 后面那个未决策 0 后面的哪个人已经决策
									 // if(empty($out)){
										 /*  if($params['show']!=1){
											  // echo '1111';
											$url = "//".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']."&show=1";
											goUrl($url);
											
										// }
									 } */
									 	$show=1;
										
										for($i=0;$i<=count($uid_arr2);$i++){
												$sql="select * from  ng_price_model_detail where uid='$uid_arr2[$i]'
													and modelid=".$modeldb["id"]." and modelprice_type =$type ";
												$out=$this->drc->query($sql);
									
											  if(empty($out)&&$uid_arr2[$i]!=$uid){
												  break;
												}
												$master_leader2[$i]=$master_leader[$i];
										}
										
										
									 }
						
						
					        }   
					
					$this->assign( "master_leader", $master_leader2);
				}
			}else{
						$master_leader4=array();
						// echo '444';
						//决策人的判断
						$outjc='0';
						for($i=0;$i<count($uid_arr2);$i++){
									
								$sql="select * from  ng_price_model_detail where uid='$uid_arr2[$i]'
								  and modelid=".$modeldb["id"]." and modelprice_type =$type ";
								$out=$this->drc->query($sql);   
						
								if(!empty($out)){//分管领导有一个决策了 就跳show=1页面
									
									$outjc='1';
									break;
								}
					
						}
						if($outjc=='1'){
							/*  if($params['show']!=1){
									$url = "//".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']."&show=1";
									goUrl($url);
								} */
							$show=1;
						 }	
				     	$j=0;	
						for($i=0;$i<=count($uid_arr2);$i++){
							
						  $sql="select * from  ng_price_model_detail where uid='$uid_arr2[$i]'
						  and modelid='".$modeldb["id"]."' and modelprice_type =$type ";
						  $out=$this->drc->query($sql);  
									  
							  
						  if(!empty($out)){
							    $master_leader4[$j]= $master_leader[$i];
						      $j++;
							  }
						
						}
							 // echo "<pre/>";print_r($master_leader4);
							 // echo '11111';
							 $this->assign( "master_leader", $master_leader4 );//决策人进来查看 传的值
							 
				}
			
			
			
			
// 			$this->assign( "master_leader", $master_leader );
			$this->assign( "city_arr", $GLOBALS['city'] );
			$this->assign( "save_city",array('1'=>' ') );
						$this->assign( "privilege", $privilege );
			$this->assign( "type", $type );
			$this->assign( "citynum", count($GLOBALS['save_city']) );
			$sql="select * from ng_price_model_detail where modelprice_type=$type and modelid=".$modeldb["id"]." and uid=-1  order by modelpirce_name_sort  asc"; 
			$res=$this->drc->query($sql);
			//add
			// $sql_Ismakepricy_men ="select Ismakepricy_men  from ng_price_model_detail where Ismakepricy_men=1 and modelprice_type=$type and modelid='".$modeldb["id"]."'";
			//echo $sql_Ismakepricy_men;
			// $res_Ismakepricy_men =$this->drc->query($sql_Ismakepricy_men);
			//$params['show']=1;
			// foreach ($res_Ismakepricy_men  as $key=>$v){
			 // if($v['Ismakepricy_men']==1){
			 	// $params['show']=1;
		     // }else{
		     	// $params['show']=0;
		     // }
			// }
			
			 // $this->assign( "params", $params );
			//print_r($params);exit;
			//end
			for($i=0;$i<count($res);$i++){				
				$res[$i]['abs_modelprice_updown']=$res[$i]['modelprice_updown'];
				if($res[$i]['modelprice_updown']){
					$res[$i]['abs_modelprice_updown']=abs($res[$i]['modelprice_updown']);
				}
			}
			$this->assign("isHuiZong",$isHuiZong);//是否是汇总使用  1=汇总
			$this->assign( "modeldetail", $res );

		 $sql="select Ismakepricy from ng_price_model where modeltype=$type and  date='$date' order by id desc limit 1";	
		 $style_url=dirname("//".$_SERVER['SERVER_NAME'].$_SERVER['PHP_SELF']);
		 $this->assign( "style_url", $style_url );
		 $res=$this->drc->getOne($sql);
		//print_r($res);exit;
		 if($res=='1'){
		 	$this->assign("is_makepricy","1");
		 		
		 }else{
		 	$this->assign("is_makepricy","0");
		 }
		 if($res=='1'){			
			/* $url = "//".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']."&show=1";
			gourl($url); */
		 	$show=1;
		 }
		 $this->assign( "show", $show );
	}else{
		$this->assign("nodata","1");
		$this->assign( "iswj", 1);
		$this->assign("nodata1","暂无".date('Y年n月j日',strtotime($date))."日中厚板日定价数据");
	 }
   
	 if($modeldb['is_wangqi']!='1') {
	 	
	 	
	 	$up_wq='update ng_price_model set  is_wangqi=1 where id="'.$modeldb['id'].'" and modeltype="'.$type.'"   ';
	 	
	 	$this->drc->execute($up_wq);
	 }
	 
	 $this->assign( "iszhb", 0);
	
	 	if($modeldb['pricycontent']==''&&$_GET['save_zh']!=1){
	 	//	$url = $_SERVER['HTTP_REFERER']."&iswj=1&save_zh=1";;
	 		$url = "http://".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']."&save_zh=1";
	 		 
	 		include '/usr/local/www/libs/phpQuery/phpQuery.php';
	 		phpQuery::newDocumentFile($url);
	 		$content=pq("html")->html();
	 		$up_wq='update ng_price_model set pricycontent ="'.htmlentities($content,ENT_QUOTES,"UTF-8").'" where id="'.$modeldb['id'].'" and modeltype="'.$type.'" and is_wangqi=1  ';
	 		$this->drc->execute($up_wq);
	 		$this->assign( "iszhb", 1);
	 	}
	 
	 
	 
    } 
	
	public function save_price($params){
		$uid=$params['uid'];
		
		
		//echo "<pre/>";print_r($params);//exit;
		
		$uid_arr=$params['arr'];//决策人
		$uid_arr2=$params['arr2'];//分管领导
		//print_r($uid_arr2);
		
		$UserType='0';
		if(in_array($uid,$uid_arr)){
			$UserType='1';
		
		}elseif(in_array($uid,$uid_arr2)){
			$UserType='2';
				
		}
		
		
		
		$mid=$params['Mid'];
		$type=$params['type'];
		$price=$params['p_zhbr'];
		$city_price=$params['price_zhbr'];
		$oldprice=$params['oldprice_zhbr'];
		$updown=$params['updown_zhbr'];
 
		$modeldbs=$params['modeldb_zhbr'];
		//$params['privilege']==1 ?  $privilege=1: $privilege=0;
		$endmarket='0';
		// end($uid_arr2)==$uid?$privilege=1:$privilege=0;
		//print_r($uid);exit;
		if(end($uid_arr2)==$uid){
			$privilege=1;
			}else{
			$endmarket='1';
			$privilege=0;
		}
		
		foreach($price as $key=>$i){
				$citykey[]=$key;
		}  
		$sql="select date from ng_price_model where id=$modeldbs";
		$dates=$this->drc->getone($sql);
		$sql="select * from ng_price_model_detail where modelid=$modeldbs and mid=$mid and uid=$uid and modelprice_type=$type";
		
		$res=$this->drc->query($sql);
		//add价格名称默认使用系统的
		$sql="select modelpirce_name from ng_price_model_detail where modelid=$modeldbs and uid=-1 and modelprice_type=$type  and   createtime>='".date('Y-m-d',time())." 02:00:00' and  createtime<='".date('Y-m-d',strtotime('+1 day ',time()))." 02:00:00' order by createtime desc limit 1";	
		$modelpirce_name=$this->drc->getone($sql);
		
		//end
	
		if(empty($res)){
			$sql="insert into ng_price_model_detail (modelid,uid,Mid,modelprice_type,Ismakepricy_men,modelprice,modeloldprice,modelprice_updown,modelpirce_name,modelpirce_name_sort,createtime,UserType,date) values ";
			for($i=1;$i<=count($price);$i++){
				$sql.="($modeldbs,$uid,$mid,$type,$privilege,'".$price[$i]."','".$oldprice[$i]."','";
				if($updown[$i]=='-1'){
					$sql.=-(($city_price[$i]/10)*10);
				}
				else{
					$sql.=(($city_price[$i]/10)*10);
				}
				$sql.="','$modelpirce_name','$i',NOW(),'".$UserType."','".$dates."'),";
				//$sql.="'".$UserType."'";
			}
			$sql=substr($sql,0,-1);
			$this->drc->execute($sql);
		}
		else{ 
			for($i=1;$i<=count($price);$i++){
					$sql="update ng_price_model_detail set UserType='".$UserType."',Ismakepricy_men='".$privilege."' , modelprice='".$price[$i]."' ,modeloldprice='".$oldprice[$i]."' ,modelprice_updown='";
					if($updown[$i]=='-1'){
						$sql.=-(($city_price[$i]/10)*10);
					}
					else{
						$sql.=(($city_price[$i]/10)*10);
					}
					$sql.="' , createtime=NOW() 
							where modelid=$modeldbs and	mid=$mid and uid=$uid and modelprice_type=$type and modelpirce_name='$modelpirce_name' ";
					$this->drc->execute($sql);							
				}
			}//exit;	
			
			$url = "http://".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']."&show=1&iswj=1&save_zh=1";
			//$url = "//".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']."&show=1&iswj=1&save_zh=1";
				
			include '/usr/local/www/libs/phpQuery/phpQuery.php';
			phpQuery::newDocumentFile($url);
			$content=pq("html")->html();
			$up_wq='update ng_price_model set pricycontent ="'.htmlentities($content,ENT_QUOTES,"UTF-8").'" where id="'.$modeldbs.'" and modeltype="'.$type.'"  ';
			$this->drc->execute($up_wq);
		if($params['saveone_zhbr']){
			$url = $_SERVER['HTTP_REFERER'];
			goUrl($url);
		}
		if($params['saveAll_zhbr']){ 
			
			
			/* $endmarket='0';
			$sql="select * from app_license_privilege  where  privilege!='' and mc_type='1' ";
			$res=$this->_dao->query($sql);//res值得排序即是分管领导的排序
			
			foreach($res as $key => $value){
				$r=explode(',',$value['privilege']);
				$r=explode(',',$value['privilege']);
				if( $r[3]==1){
					// echo $value['uid']."<br/>";
					$uids=$value['uid'];
					$sql2="select * from  ng_price_model_detail where uid='$uids'
					and modelid='".$modeldbs."' and modelprice_type=$type";
				        $out=$this->drc->query($sql2);
							        // echo "<pre/>"; print_r($out);
					if(empty($out)){
					$endmarket='1';
					break;
				}
				}
				} */
			
				if($endmarket!='1'){
			
			$url = "http://".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']."&show=1&iswj=1&save_zh=1";
			
			$content=pq("html")->html();
			$sql="update ng_price_model set pricycontent ='".htmlentities($content,ENT_QUOTES,"UTF-8")."',ismakepricy=1 ,makepricytime=NOW()
					 where id='".$modeldbs."' and modeltype='".$type."' ";
			$this->drc->execute($sql);
			$url = $_SERVER['HTTP_REFERER'];
			goUrl($url);
			}else{
				$url = $_SERVER['HTTP_REFERER'];
				goUrl($url);
					
			}
			
		}
		
	}

} 

?>