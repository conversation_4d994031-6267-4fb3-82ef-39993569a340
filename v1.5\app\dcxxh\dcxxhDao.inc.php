<?php

class dcxxhDao extends Dao
{

    public function __construct($writer)
    {
        parent::__construct($writer);
    }

    public function getDataScreenTempList($where, $start, $per)
    {
        return $this->query("select * from DataScreenTempList where 1 $where order by sort asc limit $start, $per");
    }

    public function getDataScreenTempList2($where)
    {
        return $this->query("select * from DataScreenTempList where 1 $where order by sort asc");
    }

    public function getDataScreenTempListTotal($where)
    {
        return $this->getOne("select count(1) from DataScreenTempList where 1 $where");
    }

    public function getDataScreenTempById($id)
    {
        return $this->getRow("select * from DataScreenTempList where id='" . $id . "'");
    }

    public function getDataScreenTemplateById($id)
    {
        return $this->getRow("select * from DataScreenTemplate where id='" . $id . "'");
    }

    public function getDataScreenTemplateList($where)
    {
        return $this->query("select * from DataScreenTemplate where 1 $where order by sort asc");
    }

    public function getuser_mid($mid, $mc_type, $uid)
    {
        $sql = "SELECT Uid,TrueName from app_session_temp where Mid='" . $mid . "' and mc_type='" . $mc_type . "' and Uid!='" . $uid . "' group by TrueName";
        $users = $this->query($sql);

        return $users;
    }

    public function WriteLog($Mid, $Uid, $SignCS, $ActionName, $Actionstr, $ActionIp, $SystemType, $SystemVersion, $MessageTitle = '', $MessageId = '', $MessageDesc = '', $mc_type = 0)
    {
        $this->execute("INSERT INTO app_logs SET Mid='$Mid', Uid='$Uid', SignCS='$SignCS', ActionName='$ActionName',Actionstr='$Actionstr', ActionDate=NOW(),ActionIp='$ActionIp', SystemType='$SystemType', SystemVersion='$SystemVersion',MessageId='$MessageId', MessageTitle='$MessageTitle', MessageDesc='$MessageDesc' , mc_type='$mc_type'");
    }

}

?>