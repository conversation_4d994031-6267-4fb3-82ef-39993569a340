<form name="frm" action="" method="get" target="_blank">
	<tr>
		<td>目标地址：</td>
		<td><input type="text" name="txtUrl" value="apple.php" size="60"></td>
	</tr>
<table>
	<tr>
		<td>参数名</td>
		<td>参数值</td>	
	<tr>
		<td><input type="text" value="action" name="txtParam1" size="10"></td>
		<td><input type="text" value="LoginOut" name="txtValue1" size="40"></td>
	</tr>	
	<tr>
		<td><input type="text" value="GUID" name="txtParam2" size="10"></td>
		<td><input type="text" value="cd816e2013db11e18dfd001aa00de1ab" name="txtValue2" size="40"></td>
	</tr>	
	<tr>
		<td><input type="text" value="" name="txtParam3" size="10"></td>
		<td><input type="text" value="" name="txtValue3" size="40"></td>
	</tr>
	<tr>
		<td><input type="text" value="" name="txtParam4" size="10"></td>
		<td><input type="text" value="" name="txtValue4" size="40"></td>
	</tr>	
	<tr>
		<td><input type="text" value="" name="txtParam5" size="10"></td>
		<td><input type="text" value="" name="txtValue5" size="40"></td>
	</tr>	
	<tr>
		<td><input type="text" value="" name="txtParam6" size="10"></td>
		<td><input type="text" value="" name="txtValue6" size="40"></td>
	</tr>	
	<tr>
		<td><input type="text" value="" name="txtParam7" size="10"></td>
		<td><input type="text" value="" name="txtValue7" size="40"></td>
	</tr>	
	<tr>
		<td><input type="text" value="" name="txtParam8" size="10"></td>
		<td><input type="text" value="" name="txtValue8" size="40"></td>
	</tr>	
	<tr>
		<td><input type="text" value="" name="txtParam9" size="10"></td>
		<td><input type="text" value="" name="txtValue9" size="40"></td>
	</tr>	
<table>
<input type="button" onclick="javascript:btn_click();" value="提交">

<script>
function btn_click(){
	var sParams = "";
	if (frm.txtParam1.value != "" && frm.txtValue1.value != ""){sParams += "?" + frm.txtParam1.value + "=" + frm.txtValue1.value;}
	if (frm.txtParam2.value != "" && frm.txtValue2.value != ""){sParams += "&" + frm.txtParam2.value + "=" + frm.txtValue2.value;}
	if (frm.txtParam3.value != "" && frm.txtValue3.value != ""){sParams += "&" + frm.txtParam3.value + "=" + frm.txtValue3.value;}
	if (frm.txtParam4.value != "" && frm.txtValue4.value != ""){sParams += "&" + frm.txtParam4.value + "=" + frm.txtValue4.value;}
	if (frm.txtParam5.value != "" && frm.txtValue5.value != ""){sParams += "&" + frm.txtParam5.value + "=" + frm.txtValue5.value;}
	if (frm.txtParam6.value != "" && frm.txtValue6.value != ""){sParams += "&" + frm.txtParam6.value + "=" + frm.txtValue6.value;}
	if (frm.txtParam7.value != "" && frm.txtValue7.value != ""){sParams += "&" + frm.txtParam7.value + "=" + frm.txtValue7.value;}
	if (frm.txtParam8.value != "" && frm.txtValue8.value != ""){sParams += "&" + frm.txtParam8.value + "=" + frm.txtValue8.value;}
	if (frm.txtParam9.value != "" && frm.txtValue9.value != ""){sParams += "&" + frm.txtParam9.value + "=" + frm.txtValue9.value;}
	frm.action = frm.txtUrl.value + sParams;
	//alert(frm.action);
	window.open(frm.txtUrl.value + sParams);
	//location.href = (frm.txtUrl.value + sParams);
}
</script>
</form>