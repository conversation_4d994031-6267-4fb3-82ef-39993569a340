<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
"http://www.w3.org/TR/html4/loose.dtd">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf8">
<title>系统信息变动日志列表</title>
<style>
   .content-main table {
    border-collapse: collapse;
    border-spacing: 0;
    width: 100% !important;
    margin: 0 auto !important;
    font-size: 14px;
    border: 1px solid #ccc;
}
.content-main table {
    width: 100% !important;
    margin: 0 auto;
    align-items: center;
    text-align: center;
    
}

 .content-main table td {
    border: 1px solid #ccc !important;
    border-collapse: collapse;
    text-align: center;
    height: 22px;
    line-height: 22px;
    font-size: 14px;
}
</style>
</head>

<body>
<div align="center">
<div style="width: 100%; " class="content-main" >
   截止时间之后修改的嘉宾
              <TABLE align=center>
                <TBODY>
                  <TR class=font151 bgColor=#e4e4e4>
                    <TD width="30" height="24"><div align="center"><b>参会状态</b></div></TD>
                    <TD width=""><div align="center"><b>公司名称</b></div></TD>
                    <TD width=""><div align="center"><b>姓名</b></div></TD>
                    <TD width=""><div align="center"><b>职务</b></div></TD>
                    <TD width=""><div align="center"><b>交费类型</b></div></TD>
                    <TD width=""><div align="center"><b>预订酒店</b></div></TD>
                    <TD width=""><div align="center"><b>修改人</b></div></TD>
                    <TD width=""><div align="center"><b>修改时间</b></div></TD>
                  </TR>
                  <{foreach from=$xgshuju item=v}>
                  <TR class=font151 bgColor=#FFFFFF> 
                    <TD width="" height="27"><div align="center" <{if $v.Status!=1}>style="color: red;"<{/if}> ><{if $v.Status==1}>是<{else}>否<{/if}></div></TD>    
                    <TD width=""><div align="center" <{if $allxxbd[$v.ID]['CompfnameS']==1}>style="color: red;"<{/if}> ><{$v.CompfnameS}></div></TD>
                    <TD width=""><div align="center" <{if $allxxbd[$v.ID]['TrueName']==1}>style="color: red;"<{/if}> ><{$v.TrueName}></div></TD>
                    <TD width=""><div align="center" <{if $allxxbd[$v.ID]['Post']==1}>style="color: red;"<{/if}> ><{$v.Post}></div></TD>
                    <TD width=""><div align="center" <{if $allxxbd[$v.ID]['IsGetMoney']==1}>style="color: red;"<{/if}> ><{$jfsx[$v.IsGetMoney]}></div></TD>
                    <TD width=""><div align="center" <{if $allxxbd[$v.ID]['预订酒店']==1}>style="color: red;"<{/if}> >预订酒店</div></TD>
                    <TD width=""><div align="center"><{$v.createuser}></div></TD>
                    <TD width=""><div align="center"><{$v.createtime}></div></TD>
                  </TR>
                  
            <{/foreach}>
                </TBODY>
            </TABLE>
         <br>
  </div>					  
</div>
		  
</body>
</html>
