<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );


class xgquanxController extends AbstractController{

    public function __construct(){
        parent::__construct();

        $this->_action->setDao( new xgquanxDao( 'DRCW',"DRC" ) );
        $this->_action->drc_test = new xgquanxDao('DRCW') ;
        $this->_action->t1dao = new xgquanxDao('MAIN') ;
        $this->_action->maindao = new xgquanxDao('91W','91R');
        $this->_action->maindao_test = new xgquanxDao('91R');
//        $this->_action->xgdao_test = new xgquanxDao('XG_TEST');
    }

    public function _dopre(){
        //$this->_action->checkSession();
    }

    public function v_index() {
        $this->_action->index($this->_request);
    }

    public function do_updateStatus() {
        $this->_action->updateStatus($this->_request);
    }

    public function v_setpower() {
        $this->_action->setpower($this->_request);
    }

    public function do_doedit(){
        $this->_action->doedit($this->_request);
    }

    public function do_unbind(){
        $this->_action->unbind($this->_request);
    }

    public function do_reset(){
        $this->_action->reset($this->_request);
    }

    public function v_adduser(){
        $this->_action->adduser($this->_request);
    }

    public function do_doadd(){
        $this->_action->doadd($this->_request);
    }

    public function do_getUser(){
        $this->_action->getUser($this->_request);
    }

    public function v_edit(){
        $this->_action->edit($this->_request);
    }

    public function do_update(){
        $this->_action->update($this->_request);
    }

    public function v_loglist(){
        $this->_action->loglist($this->_request);
    }

    public function v_powerlist(){
        $this->_action->powerlist($this->_request);
    }

    public function v_addtype(){
        $this->_action->addtype($this->_request);
    }


    public function do_doaddtype(){
        $this->_action->doaddtype($this->_request);
    }

    public function v_additem(){
        $this->_action->additem($this->_request);
    }

    public function do_doadditem(){
        $this->_action->doadditem($this->_request);
    }

    public function v_edititem(){
        $this->_action->edititem($this->_request);
    }

    public function v_show(){
        $this->_action->show($this->_request);
    }

    public function do_delpopedom(){
        $this->_action->delpopedom($this->_request);
    }
    public function do_getcopy(){
        $this->_action->getcopy($this->_request);
    }

    public function v_sgloglist(){
        $this->_action->sgloglist($this->_request);
    }

    public function v_catdata(){
        $this->_action->catdata($this->_request);
    }
    public function do_getAdminuserLogin(){
        $this->_action->getAdminuserLogin($this->_request);
    }

    public function v_userindex(){
        $this->_action->userindex($this->_request);
    }

    public function do_dosetuser(){
        $this->_action->dosetuser($this->_request);
    }

    public function v_setuser(){
        $this->_action->setuser($this->_request);
    }


    public function v_setpower2() {
        $this->_action->setpower2($this->_request);
    }

    public function do_dosetpower() {
        $this->_action->dosetpower($this->_request);
    }

    public function v_edituser() {
        $this->_action->edituser($this->_request);
    }
    public function do_addLicenseKeyD() {
        $this->_action->addLicenseKeyD($this->_request);
    }

    public function do_resetuser() {
        $this->_action->resetuser($this->_request);
    }

    public function do_unbinduser() {
        $this->_action->unbinduser($this->_request);
    }

    public function do_doaddCronconfig() {
        $this->_action->doaddCronconfig($this->_request);
    }

    public function v_addcronconfig() {
        $this->_action->addcronconfig($this->_request);
    }


    public function v_setpower3() {
        $this->_action->setpower3($this->_request);
    }

    public function do_dosetpower3() {
        $this->_action->dosetpower3($this->_request);
    }

    public function do_closeuser() {
        $this->_action->closeuser($this->_request);
    }

    public function do_delcronconfig() {
        $this->_action->delcronconfig($this->_request);
    }

    public function do_openuser() {
        $this->_action->openuser($this->_request);
    }

    public function do_edit_password() {
        $this->_action->doedit_password($this->_request);
    }
    public function do_getAdminuserXGLogin() {
        $this->_action->getAdminuserXGLogin($this->_request);
    }


    


}