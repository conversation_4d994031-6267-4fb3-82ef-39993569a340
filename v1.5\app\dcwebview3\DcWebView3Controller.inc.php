<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );

class DcWebView3Controller extends AbstractController{
  
  public function __construct(){
    parent::__construct();
	
	$this->_action->setDao( new DcWebView3Dao( "MAIN" ) );
	//$this->_action->bizdao = new SystemDao('BIZ') ;
	// $this->_action->steelhome= new DcWebView3Dao('MAIN') ;
	$this->_action->steelhome= new DcWebView3Dao('91R'.$houzhui);
	$this->_action->drc= new DcWebView3Dao('DRCW','DRC');
	// $this->_action->stedao = new DcWebView3Dao('HQ') ;
	$this->_action->maindao = new DcWebView3Dao('91R') ;//hlf/钢之家主数据库、正式库，测试无数据
	$this->_action->gcdao=new DcWebView3Dao('GC');//hlf/钢厂调价的本期价格及涨跌幅,正式库
	
	
  }

  public function _dopre(){
	  //echo "<pre/>";print_r($_SESSION);exit;
      //$this->_action->checkSession();
  }
	

  public function v_index() {
	$this->_action->index($this->_request);
  }
  public function do_save_price() {
	$this->_action->save_price($this->_request);
  }

	
  public function do_ThisDateDb() {
	$this->_action->DoThisDateDb($this->_request);
  }

  public function do_ThisDateDbList() {
	$this->_action->DoThisDateDbList($this->_request);
  }
}


?>