<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );

class sggcpriceController extends AbstractController{
  
  public function __construct(){
    parent::__construct();
	$this->_action->setDao( new sggcpriceDao("DRCW") );
	$this->_action->t1Dao=new sggcpriceDao("MAIN");
	$this->_action->homeDao=new sggcpriceDao("91R");
	$this->_action->gcDao=new sggcpriceDao("GC");
	$this->_action->maindrc=new sggcpriceDao("DRC");
	$this->_action->sms=new sggcpriceDao("99sms");
  }

	public function v_index(){
		$this->_action->index( $this->_request );
	}
	public function do_savedata(){
		$this->_action->savedata( $this->_request ); //保存文本
	}
	public function v_webview(){
		$this->_action->webview( $this->_request ); //保存文本
	}
	public function do_save_price(){
		$this->_action->save_price( $this->_request ); //保存文本
	}
	public function do_countGrade(){
		$this->_action->countGrade( $this->_request ); //计算分数
	}
	public function do_timing_dx(){
		$this->_action->timing_dx( $this->_request ); //提醒填写定价
	}
	public function v_sc_wq(){
		$this->_action->sc_wq( $this->_request ); //提醒填写定价
	}
	public function do_sc_wq(){
		$this->_action->do_sc_wq( $this->_request ); //提醒填写定价
	}
	
}
?>