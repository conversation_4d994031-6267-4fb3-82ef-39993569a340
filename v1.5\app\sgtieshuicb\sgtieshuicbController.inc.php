<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );


class sgtieshuicbController extends AbstractController{
  
  public function __construct(){
    parent::__construct();
	
	$this->_action->setDao( new sgtieshuicbDao( 'DRCW') );
	//$this->_action->ngdao = new DcWebViewDGDao('DRCW') ;
	//$this->_action->drc_test = new sgtieshuicbDao('DRCW') ;
	$this->_action->t1dao = new sgtieshuicbDao('MAIN') ;
	$this->_action->maindao = new sgtieshuicbDao('91R');
	//$this->_action->maindao_test = new sgtieshuicbDao('91R_TEST');
	//$this->_action->gcdao=new sgtieshuicbDao('GC');
  }
  
  public function _dopre(){
      //$this->_action->checkSession();
  }

  public function v_index() {
	$this->_action->index($this->_request);
  }
  public function v_ajaxgetindexinfo() {
      //header( "Content-Type: text/html; charset=gbk" );
	$this->_action->ajaxgetindexinfo($this->_request);
  }
  public function do_save() {
      //header( "Content-Type: text/html; charset=gbk" );
	$this->_action->save($this->_request);
  }
  public function do_update() {
	$this->_action->update($this->_request);
  }
  public function do_del() {
	$this->_action->del($this->_request);
  }

  public function do_get_tieshui_cb() {
	$this->_action->get_tieshui_cb($this->_request);
  }

  public function do_jstscbweb() {
	$this->_action->jstscbweb($this->_request);
  }
  public function do_jsgpcbweb() {
    $this->_action->jsgpcbweb($this->_request);
  }
  public function do_jsgccbweb() {
    $this->_action->jsgccbweb($this->_request);
    }
  public function v_test2() {
	$this->_action->test2($this->_request);
  }
  public function v_test3() {
	$this->_action->test3($this->_request);
  }
  public function do_dscx() 
  {
	  $this->_action->dscx($this->_request);
  }
  public function do_dscx1() 
  {
	  $this->_action->dscx1($this->_request);
  }
  public function do_dscx2() 
  {
	  $this->_action->dscx2($this->_request);
  }
  public function do_changejiage() 
  {
	  $this->_action->changejiage($this->_request);
  }
  
  public function v_gangpiindex() {
	$this->_action->gangpiindex($this->_request);
  }
  
  public function v_gangcaiindex() {
	$this->_action->gangcaiindex($this->_request);
  }
  public function do_jstsdtcb() 
  {
	  $this->_action->jsts_dtcb($this->_request);
  }

   public function v_longhangangindex() {
	$this->_action->longhangangindex($this->_request);
  }
  public function do_jsdtcbweb() 
  {
	  $this->_action->jsdtcbweb($this->_request);
  }
  public function do_dscx3() //定时计算修改时间段龙钢汉钢成本
  {
	  $this->_action->dscx3($this->_request);
  }
  

}