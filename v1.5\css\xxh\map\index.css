@charset "utf-8";a,abbr,acronym,address,applet,article,aside,audio,b,big,blockquote,body,canvas,caption,center,cite,code,dd,del,details,dfn,div,dl,dt,em,fieldset,figcaption,figure,footer,form,h1,h2,h3,h4,h5,h6,header,hgroup,html,i,iframe,img,ins,kbd,label,legend,li,mark,menu,nav,object,ol,p,pre,q,s,samp,section,small,span,strike,strong,sub,summary,sup,table,tbody,td,tfoot,th,thead,time,tr,tt,u,ul,var,video{margin:0;padding:0;border:0;outline:0}
article,aside,details,figcaption,figure,footer,header,hgroup,menu,nav,section{display:block;clear:both}
html{font-family:Microsoft YaHei,Helvetica Neue,Helvetica,Roboto,STHeiTi,Arial,sans-serif;-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%;line-height:1.5}
body{font-size:.8rem;overflow-x:hidden;min-width:290px;color:#1a1a1a;}
a{text-decoration:none;color:#1a1a1a}
a:active{color:#3990e6;outline:0}
audio,canvas,progress,video{display:inline-block;vertical-align:baseline}
audio:not([controls]){display:none;height:0}
svg:not(:root){overflow:hidden}
hr{-moz-box-sizing:content-box;box-sizing:content-box;height:0}
pre{overflow:auto;white-space:pre;white-space:pre-wrap;word-wrap:break-word}
code,kbd,pre,samp{font-family:monospace;font-size:.5rem}
mark{background:#ff0;color:#1a1a1a}
dfn{font-style:italic}
table{border-collapse:collapse;border-spacing:0}
td,th{padding:0}
ol,ul{list-style:none outside none}
h1,h2,h3,strong{font-weight:400}
img{vertical-align:middle;border:0;-ms-interpolation-mode:bicubic}
button,input,optgroup,select,textarea{color:inherit;font:inherit;margin:0}
button{overflow:visible}
button,select{text-transform:none}
button,html input[type=button],input[type=reset],input[type=submit]{-webkit-appearance:button;cursor:pointer}
button[disabled],html input[disabled]{cursor:default}
button::-moz-focus-inner,input::-moz-focus-inner{border:0;padding:0}
input{line-height:normal}
input[type=checkbox],input[type=radio]{box-sizing:border-box;padding:0}
input[type=number]::-webkit-inner-spin-button,input[type=number]::-webkit-outer-spin-button{height:auto}
input[type=search]{-webkit-appearance:textfield;-moz-box-sizing:border-box;-webkit-box-sizing:border-box;box-sizing:border-box}
input[type=search]::-webkit-search-cancel-button,input[type=search]::-webkit-search-decoration{-webkit-appearance:none}
textarea{overflow:auto;resize:vertical}
input::-moz-placeholder,textarea::-moz-placeholder{color:#ccc}
input:-ms-input-placeholder,textarea:-ms-input-placeholder{color:#ccc}
input::-webkit-input-placeholder,textarea::-webkit-input-placeholder{color:#ccc}

html{font-size:16px}
@media only screen and (min-width:320px){html{font-size:16px!important}
}
@media only screen and (min-width:375px){html{font-size:20px!important}
}
.clearfix:after{content:'\20';display:block;height:0;clear:both}
.clearfix{zoom:1}
.bold {font-weight: bold;}

.container {position: relative;width: 100%;height: 100%;overflow: hidden;}
.input-group  {width: 50%;}
.pannel {position: relative;}
input:active{border: none;}
.btn-group {z-index: 99; position: absolute;right: .5rem; top:.5rem;}
.loading { width: 100%; height:1.6rem; background-size: 1.6rem 1.6rem; position: absolute; top: 5rem;display: none;z-index: 2;}
.cont {background-color: #fff;}

.mt10 {margin-top: .5rem;}
body {background-color: #eeeeee;overflow: hidden;font-size: .85rem;width: 100%;height: 100%;position: absolute;}
/* .cont ul {clear: both; padding:.5rem 0;border-bottom: 1px solid #ccc; margin: 0;} */

.red {color: #EE4E4E !important;fill: #ab0000;}
.green {color: #2C9A00 !important;fill: #116d0d;}
.gray {color:#999 !important;}
.gray1 {color:#999 !important;}
.borNone {border: none !important;}
.dateSelect {
    /* position: relative; */
    width: 50%;float: left; margin: .25rem -1px .25rem 0; height: 2rem;border-left: 1px solid #ccc;}
.datename {float: left; width: 22%;height: 2rem;font: .7rem/2rem 'Microsoft Yahei'; text-align: right; color: #999999}
.selectInput {height: 2rem;display: block;cursor: pointer; float: left;text-align: center;color: #1a1a1a; width: 100%;padding:0;font-size: .8rem;line-height: 2rem; border: none;margin: 0;border-radius: 0;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;} 
.dateSelect .spec {display: inline-block;font-size: .7rem; line-height: .9rem;margin:-2px .5rem 0 .25rem;vertical-align: middle;}

.dateSelect .upicon {right: 5%;display: inline-block;width: .8rem;height: .8rem;font:bold .9rem/.8rem 'sans-serif';color: #999;transform: rotate(180deg);background: url(../images/upicon.png?v=20171206) no-repeat center top transparent;background-size: .8rem .8rem;transition:transform .3s ease-in .1s;}
.dateSelectUpicon{position: absolute;top: .8rem;}
.dateSelect .upicon.down {transform: rotate(0deg);}
.dateSelect .spec.a1 {display: inline-block;position: relative;}

.topBtn {
    /* border-bottom:1px solid #ccc; */
    position: relative;z-index: 1002;background-color: #fff;}

.overView {position: absolute;left: .4rem; bottom:.5rem;width: 14.8rem;text-align: center; height:3.25rem;background-color: rgba(255,255,255,.7);border-radius:5px;box-shadow:0px 0px 3px rgba(0,0,0,.5);font-size: .6rem;line-height: .9rem;}
.overView .view{float: left;position: relative;margin:.25rem .05rem; width: 5rem; border-right: 1px solid #ccc;}
.overView .view p {margin: 0;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;}
.overView .view1 {padding: .5rem 0 .5rem .25rem; width:3.75rem;}
.overView .view3 {border-right: none;}
.overView .view .viewTitle { color: #999999 }
.red {color: #f63e3f}
.green {color: #3db521}
.legend {position: absolute;left: .4rem; top:.4rem;width: 4rem;text-align: left; height:6rem;background-color: rgba(255,255,255,.8);box-shadow:0px 0px .25rem rgba(0,0,0,.6); font: .6rem/.9rem 'Microsoft Yahei';}
.legend h3 { font: .6rem/.9rem 'Microsoft Yahei';font-size: 0.6rem; margin: 0; padding: .25rem .5rem;}
.legend ul {padding: 0;border:none;}
.legend.type2 {height: 8.2rem;}
.legend.type2 .legendList:first-of-type {display: none}
.legend.type2 .legendList:last-of-type {display: block}
.legend .legendList:last-of-type {display: none}
.legendList li { padding: 1px 0 1px .20rem;font-size: .6rem;line-height: .9rem;}
.legendList li span{display: inline-block; width: .8rem; height:.8rem;vertical-align: middle;}
.legendList .color0 {background-color: #fd8282;}
.legendList .color1 {background-color: #ffb0b0;}
.legendList .color2 {background-color: #ffe0e0;}
.legendList .color3 {background-color: #caf2ab;}
.legendList .color4 {background-color: #aada85;}
.legendList .color5 {background-color: #6cbb2e;}
#test_select_dummy {width: 0;display: block; padding: 0; margin: 0;border: none;height:0;float: left;}

.app-bar{display:block;position: relative;width:100%;height:55px;background:rgba(0, 0, 0, .5);color:#FFF;z-index:1000;font-size:.6rem;font-family:"Microsoft Yahei";}
.app-bar * { box-sizing:content-box;}
.app-bar .app-logo{float:left;width:39px;height:2rem;padding:.5rem 0 0 .5rem;}
.app-bar .app-txt{float:left;padding:.5rem 0 0 .4rem;line-height:20px;}
.app-bar .app-txt h2{font-weight:normal;font-size:.8rem;margin: 0;}
.app-bar .app-txt p {margin: 0;font-size: .7rem;}
.app-btn{position:absolute;right: 12px;font-size: .8rem;background-color:#003A90;border-radius:.8rem;height:1rem;line-height: 1rem;padding:.2rem 1.2rem; margin:14px .3rem 0 0;color:#FFF;}
.app-close{position:absolute;right: 0; width:.6rem;height:.6rem;background:url(../images/app_close.png) no-repeat;background-size:100%;margin:22px .5rem 0 0;}
.btn-group>.btn:first-child:not(:last-child):not(.dropdown-toggle){border-top-right-radius:0;border-bottom-right-radius:0;border-right:none}
.btn-group>.btn:first-child{margin-left:0}
.btn-info.active,.btn-info:active{background-color:#003A90;border-color:#003A90}
.btn.active,.btn:active{background-image:none}
.btn-group-vertical>.btn,.btn-group>.btn{position:relative;float:left}
label{display:inline-block;max-width:100%;margin-bottom:5px;font-weight:700}
input[type=checkbox],input[type=radio]{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;padding:0}
input[type=checkbox],input[type=radio]{line-height:normal}
[data-toggle=buttons]>.btn input[type=checkbox],[data-toggle=buttons]>.btn input[type=radio],[data-toggle=buttons]>.btn-group>.btn input[type=checkbox],[data-toggle=buttons]>.btn-group>.btn input[type=radio]{position:absolute;clip:rect(0,0,0,0);pointer-events:none}
.btn{display:inline-block;padding:.3rem .6rem;margin-bottom:0;font-size:14px;font-weight:400;line-height:1.42857143;text-align:center;white-space:nowrap;vertical-align:middle;-ms-touch-action:manipulation;touch-action:manipulation;cursor:pointer;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;background-image:none;border:1px solid transparent;border-radius:4px}
.btn-group>.btn:last-child:not(:first-child),.btn-group>.dropdown-toggle:not(:first-child){border-top-left-radius:0;border-bottom-left-radius:0;border-left:none}
.btn-group>.btn:not(:first-child):not(:last-child):not(.dropdown-toggle){border-radius:0}
.btn-info{background-color:#fff;color:#003A90;border-color:#003A90}
.btn-info.active,.btn-info:active,.open>.dropdown-toggle.btn-info{color:#fff}

#main {background-color: #f1f1f1;height: 1000px;}
#main path {stroke: #999;stroke-width: 0.3px;}
#main .MyText {fill: #333;color: #333; font-size: .5rem;cursor: pointer;}
#linemain #main {height: auto;}
.opt_select {width: 100%;height: 18rem;overflow: auto;background: #fff;display: flex;position: absolute; border-top: 1px solid #ccc;
    top: 2.4rem;}
.scool-tab{height: 100%;overflow: hidden;width: 7rem;max-width: 6rem;background: #F5F5F5;font-size: 0.7rem;}
.scool-tab .select-tab-nav{font-weight: 900;text-align: center;padding: 0.5rem;}
.scool-tab .select-tab-nav.active{color: #003A90;background: #fff;}
.nav-con{width: 100%;height: 100%;overflow: auto;}
.opt-con{position: relative;padding: 0.6rem 0.4rem;}
.scool-opt{height: 100%;overflow: auto;flex: 1;}
.scool-opt .opt-item{display: flex;flex-wrap: wrap;
    align-items: center;}
.select-title{clear: both;font-weight: 900;    font-size: 0.7rem;
    border-bottom: 1px solid #F0F0F0;
    padding: 0.6rem 0rem 0.3rem;
    width: 95%;
    margin: 0 auto 0.3rem;}
.select-title:before{
    content: '';
    width: 5px;
    margin-right: 6px;
    height: 0.9rem;
    background: #003A90;
    display: inline-block;
    vertical-align: middle;
}
.opt_select .dwo {top:0px;}
.opt_select .dw {top:0px;}
.dwwl {cursor: move;}
.opt_select .dw .dwc {color: #333;padding: 0;border-bottom: none;}
.opt_select .optItem {width: 45%;min-height: 3.5rem;border-radius: 0.3rem;
    height: auto;cursor: pointer; margin:0.2rem 2.5%;text-align: center;padding:.25rem 0;border: 1px solid #ccc;box-sizing: border-box;}
.opt_select .optItem strong { font: .6rem/.9rem 'Microsoft Yahei';}
.opt_select .optItem p { color: #999;font-size: .5rem; line-height: .7rem;white-space: nowrap;text-overflow: ellipsis;}
.opt_select .optline {margin: 0;border-bottom: .25rem solid #eeeeee;border-top: 1px solid #ccc;overflow: hidden;width: 100%;top: -1px;position: relative;}
.opt_select .optItem.selected {background:#EFF5FF;color: #003A90;border: none;}
.opt_select .optItem.selected p { color: #003A90}
.opt_select .optItem.selected strong{font-weight: 900;}
#test_select .optItem strong em{background:url(../images/strong_point.png) no-repeat 0 0;background-size: .5rem 5rem;margin-right: .2rem; display: inline-block;width: .5rem;height: .5rem;}
#test_select .optItem strong .lineNumb1 {background-position: 0 0}
#test_select .optItem strong .lineNumb2,#test_select .optItem strong .lineNumb7 {background-position: 0 -1rem}
#test_select .optItem strong .lineNumb3,#test_select .optItem strong .lineNumb8 {background-position: 0 -2rem}
#test_select .optItem strong .lineNumb4,#test_select .optItem strong .lineNumb9 {background-position: 0 -3rem}
#test_select .optItem strong .lineNumb5,#test_select .optItem strong .lineNumb10 {background-position: 0 -4rem}
#test_select .opt_select .dwbw span:active {background-color: #29799c;color: #fff}
body .android-ics.light .dwb {
    color: #003A90;
}
.android-ics .dw .dwwol{
    background: #EFF5FF;
}
@media only screen and (min-width:610px){
	.opt_select .optItem {width: 5rem;height: 2.4rem;margin:0.2rem;}
    .select-title{width: 99%;}
  .isdata .opt_select .optItem {width: 5rem;
    /* margin:0.2rem 0.3rem; */
}
	.opt_select .optItem strong { font: .6rem/.9rem 'Microsoft Yahei';}
}
.isdata .opt_select {top:1.94rem;}
.isdata .opt_select .optItem {height: 2.1rem;padding:.2rem 0;}
.isdata .opt_select .optItem strong { font: .6rem/.7rem 'Microsoft Yahei';}
.isdata .opt_select .optItem p {font: .5rem/1.2 'Microsoft Yahei';}
.isdata .dateSelect {width: 50%;float: left; margin: .15rem -1px .15rem 0; height: 1.6rem;}
.isdata .datename { width: 22%;height: 1.6rem;font: .6rem/1.6rem 'Microsoft Yahei'; }
.isdata .selectInput {height: 1.6rem; width: 100%;padding:0; font: .8rem/1.6rem 'Microsoft Yahei';} 
.isdata .dateSelect .spec {font: .6rem/.7rem 'Microsoft Yahei';margin:-2px .5rem 0 .25rem;}
.isdata .dateSelect .upicon {top: .75rem;width: .7rem;height: .4rem;font:bold .9rem/.8rem 'sans-serif';}
.isdata .dw-persp,.dwo {top: 1.95rem !important;}
.isdata .android-ics .dwc {padding: 0;}
.isdata .swiper-slide {width: auto;}
.isdata .slowDown {height: 10rem;}
.isdata .loading {background: none !important;}
.isdata .loading::before{content:"正在加载...";text-align: center;display: block;}
#zhisuOpt .opt_select .optItem p { color: #000;}
.animated{-webkit-animation-duration:.5s;animation-duration:.5s;-webkit-animation-fill-mode:both;animation-fill-mode:both}
@-webkit-keyframes bounceInDown{90%,from,to{-webkit-animation-timing-function:cubic-bezier(.215,.61,.355,1);animation-timing-function:cubic-bezier(.215,.61,.355,1)}
0%{opacity:1;-webkit-transform:translate3d(0,-600px,0);transform:translate3d(0,-600px,0)}
90%{opacity:1;-webkit-transform:translate3d(0,0px,0);transform:translate3d(0,0px,0)}
to{-webkit-transform:none;transform:none}
}
@keyframes bounceInDown{90%,from,to{-webkit-animation-timing-function:cubic-bezier(.215,.61,.355,1);animation-timing-function:cubic-bezier(.215,.61,.355,1)}
0%{opacity:1;-webkit-transform:translate3d(0,-600px,0);transform:translate3d(0,-600px,0)}
90%{opacity:1;-webkit-transform:translate3d(0,0px,0);transform:translate3d(0,0px,0)}
to{-webkit-transform:none;transform:none}
}
.bounceInDown{-webkit-animation-name:bounceInDown;animation-name:bounceInDown}

.background{position:fixed;_position:absolute;z-index:998;top:0px;left:0px;width:100%;height:100%;background:rgb(50,50,50);background:rgba(0,0,0,0.5);}
.webox{position:fixed;_position:absolute;z-index:999;padding:3px;_background:#2f88dc;background:rgba(47,136,220,0.9);}
.webox #inside{background-color:#FFFFFF;height:22rem;}
.webox #inside h1{-moz-user-select:none;-webkit-user-select:none;color: #1977df; position:relative;display:block;margin:0;padding:0;border-bottom: 1px solid #dadada;text-align: center;height:50px;padding-left:5px;background:#fff;font-weight:normal;cursor:move;font: .9rem/50px 'Microsoft Yahei';}
.webox #inside h1 a{position:absolute;display:block;right:.75rem;margin-top:.75rem;top:0px;font-size:14px;background-image:url(../images/close.fw.png);background-repeat:no-repeat;background-position:center top;width:17px;height:17px;cursor:pointer;display:inline-block;}
.webox #inside h1 a:hover{background-position:center bottom;}

/* pages */
.cont ul {clear: both; padding:.2rem 0; margin: 0 auto;}
.list-box{
    background:#003A90;
    width: 95%;
    margin: 0.8rem auto;
    border-radius: 0.3rem;
}
#citydata{
    background: #EFF5FF;
    color: #666;
}
.list_T li{ font: .7rem/.8rem 'Microsoft Yahei'; color: #fff; float: left;list-style: none; padding: .2rem 0; width: 26%; text-align: center; height: .8rem; border-right: 1px solid #e0e0e0;}
.list_Text li{ font: .7rem/.8rem 'Microsoft Yahei'; float: left;list-style: none; padding: .2rem 0; width: 26%; text-align: center;}
.list_T li:nth-child(1){width: 10%;}
.list_T li:nth-child(2){width: 40%;}
.list_T li:nth-child(3){width: 20%;}
.list_T li:nth-child(4){width: 20%;}
.list_Text li:nth-child(1){width: 10%;}
.list_Text li:nth-child(2){width: 40%;}
.list_Text li:nth-child(3){width: 20%;}
.list_Text li:nth-child(4){width: 20%;}
.gk li:nth-child(1){width: 10%;}
.gk li:nth-child(2){width: 20%;word-break: break-all;}
.gk li:nth-child(3){width: 20%;}
.gk li:nth-child(4){width: 20%;}
.gk li:nth-child(5){width: 18%;}

.datehead { padding: 0; margin: 0; text-align: center;background-color: #fff; font: .8rem/1.75rem 'Microsoft Yahei'; border-bottom: 1px solid #ccc;font-weight: bold;}
.h10 {height: .5rem;background-color: #eeeeee;}
.h5 {height: .25rem;background-color: #eeeeee;}
#linemain * {z-index: 99 !important;}
#linemain text {font: .5rem sans-serif;}
#linemain .axis path,
#linemain .axis line {fill: none;stroke-width: 1px;stroke: #000;shape-rendering: crispEdges;}
#linemain .line {fill: none;stroke: steelblue;stroke-width: 1.5px;}
#linemain form {position: absolute;right: .5rem;top: .5rem;}
#mapTitle {fill: #666;font-size: .7rem}
#subTitle {fill: #666;font-size: .6rem;}

#cityPrice {position: absolute;width: 100%;top: 0;left: 0;height: 100%; display: none;z-index: 1003;}
#cityPrice .dwo{width: 100%;height: 100%;position: absolute;top: 0;left: 0;z-index: 1003;opacity: .3;}
#cityPrice .helpTip{position: relative;margin: 200px auto 0;width: 250px; height: 100px; z-index: 1003;}
#cityPrice .helpTip p {padding-top: 75px;text-align: center;color: #fff}
#cityPrice .helpTip.tip1{margin: 70% auto 0;background:url(../images/mengban.png) no-repeat center top;background-size: 250px 70px; }
#cityPrice .helpTip.tip2{margin: 70% auto 0;background:url(../images/mengban1.png) no-repeat center top;background-size: 63px 70px;width: 200px; }
#cityPrice .helpTip .btn {display: inline-block; vertical-align: middle; background-color: #fff;color: #4998E7; border:1px solid #fff;border-radius: 3px; padding: 3px .4rem;margin-top: .5rem;}

.selectInput.gray {color: #999;}
.dateSelect input:disabled {color: #999;opacity: 1;background-color: #fff;}
.dateSelect input.hideColor:disabled {color: #000 !important;opacity: 1;}

#zoomBtn {position: absolute;right: .4rem; bottom:.5rem;width: 1.7rem; height: 3.5rem;background-color: rgba(255,255,255,.7);border-radius:3px;box-shadow:0px 0px 3px rgba(0,0,0,.5);}
#zoomBtn .sbtn {display: inline-block;text-align: center;width: 1.7rem;height:1.7rem;font: 1.2rem/1.6rem 'Microsoft Yahei';color: #666;cursor: pointer;}
#zoomBtn .refresh {display: inline-block;width: 1.2rem;height:1.2rem;position: absolute;left: .25rem;top: -1.6rem;border-radius: .6rem;box-shadow: 0px 0px 3px rgba(0,0,0,.5);background: url(../images/refresh.png) no-repeat .2rem .14rem rgba(255,255,255,.7);background-size: .9rem .9rem;}
#zoomBtn .refresh.rolling {transform:rotate(-5deg);-webkit-transform:rotate(0) translateZ(0);-webkit-transition-duration:0ms;-webkit-animation-name:loading;-webkit-animation-duration:2s;-webkit-animation-iteration-count:infinite;-webkit-animation-timing-function:linear}
@-webkit-keyframes loading{from{-webkit-transform:rotate(0) translateZ(0)}
to{-webkit-transform:rotate(360deg) translateZ(0)}
}
#zoomBtn .jian{border-top: 1px solid #eee;}
#zoomBtn .gray {color: #ccc !important;}
@media (max-width: 350px) {
  #overView {transform:scale(.8);transform-origin:left bottom}
  #zoomBtn {transform:scale(.8);transform-origin:right bottom}
  .isdata .opt_select {top:1.93rem;}
  /* .opt_select .optItem {width: 25%;height: 44px;margin-right: -1px;margin-top: -1px;text-align: center;padding:0;float: left;border-right: 1px solid #ccc;border-bottom: 1px solid #ccc;} */
  .opt_select .optItem strong {font-size: .6rem;line-height: .7rem; transform: scale(.8);display: inline-block;}
  .opt_select .optItem p { color: #999;line-height: .6rem;font-size: .5rem;white-space: nowrap;transform: scale(.8);
    }
  /* .opt_select .optline {margin: 0;border-bottom: 3px solid #eeeeee;border-top: 1px solid #ccc;overflow: hidden;width: 100%;top: -1px;position: relative;} */
  /* .opt_select .optItem.selected {background:url(../images/select1.png?v=1) no-repeat right bottom;background-size: 1.2rem 1.2rem;color: #4f92e1} */
}
#zoomBtn .sbtn:active {background-color: rgba(155,155,155,.8);}

.pagetab {position: absolute;z-index: 1; bottom:0;background-color: #fff; width: 100%; border-top: 1px solid #ddd;}
.pagetab ul {margin: .35rem auto; width: 10rem; height: 1.6rem;background-color: #fff;border-radius: .2rem; border: 1px solid #003A90;overflow: hidden;padding: 0;}
.pagetab ul li {float: left; width: 5rem;text-align: center; height: 1.6rem;line-height: 1.6rem;color: #003A90;background: #fff;border-radius: 0;}
.pagetab ul li.swiper-active-switch {background-color: #003A90;border-radius: 0; width: 5rem;color: #fff;}
.mappal {position: relative;z-index: 1;}
#win_zhisuOpt {top:3.8rem;}
#win_zhisuOpt .optItem .val {font-weight: bold;color: #333;}
#win_zhisuOpt .optItem .rise {color: #333;}
.slowDown {height: 16.6rem;overflow-y: auto;overflow-x: hidden;border-top:.2rem solid #eeeeee;border-bottom:.2rem solid #eeeeee;}
#lineTop {position: relative;overflow: hidden;}
#lineTop .lineVal {width: 33%;float: left; height: 2.2rem;border-right:1px solid #E5E5E5; font-size: .7rem;line-height: 1.2rem;text-align: center;margin: .3rem 3% .3rem -1px;}
#lineTop .lineVal .p1 {font-weight: bold;font-size: 1rem;}
#lineTop .lineVal .p2 {font-size: .6rem;}
#lineTop .linelist {width: 64%;float: left; height: 2.8rem;overflow: hidden;}
#lineTop .linelist ul {overflow: hidden;padding: 0;border: none;}
#lineTop .linelist ul li:nth-child(2n+1) {width:45% }
#lineTop .linelist ul li {float: left;width:55%;font-size: .6rem;line-height: 1.4rem;text-align: center;white-space: nowrap;text-overflow: ellipsis;overflow: hidden;}
#lineTop .linelist ul li span {display: inline-block;padding-left: 5px;}

#zoomBtn.isdata, #overView.isdata {bottom: .3rem !important;}
#zhisuOpt {margin: 0;border-top:.4rem solid #f4f4f4 !important;width: 100%;}
#zhisuOpt .a1 {display: inline-block;position: relative; top: .1rem;left: 0;}
.topVal {overflow: hidden;border-bottom: 1px solid #ccc;height: 4.1rem;position: relative;z-index: 1002;background-color: #fff;}
.topVal .swiper-slide {width: 100%;padding: 0;border-bottom:none;min-width: 100%; }
.topVal .swiper-slide li {float: left;display: block; width: 33.3%;margin: .4rem 0 .4rem -1px;border-left:1px solid #e6e6e6;}
.topVal .swiper-slide li > p {font-size: .8rem;color: #666666;text-align: center;line-height: 1.1rem;font-family: 'Microsoft Yahei',Arial;}
.topVal .swiper-slide li:first-child {border-left:none;}
.topVal .swiper-slide li > p.nmb {font-weight: bold;}
.topVal .swiper-slide li > p.small {font-size: .7rem;}
#swipTab { height: 100%;}
#swipTab > .swiper-wrapper {width: 200%; }
#swipTab > .swiper-wrapper > .swiper-slide {width: 50%;float: left;}

.hqzs .mappal {border-bottom: .5rem solid #f4f4f4;}
.lineTip {background-color: #EFF8FF ;overflow: hidden;border-bottom: none !important;border-top: none;width: 96%;border-radius: 0.3rem;margin: 0 auto;}
.lineTip > li { float: left;text-align: center;font-size: .6rem; line-height: 1rem;text-align: center;}
.lineTip > li:nth-child(1) {width: 33%;padding-right: 3%;}
.lineTip > li:nth-child(2) {width:29%;}
.lineTip > li:nth-child(3) {width:33%;}
.timeType {overflow: hidden;padding:0.4rem 2rem !important;background: #fff;height: 2rem;}
.timeType > li {font-size: .7rem;float: left;text-align: center;width: 25%;height: 1.2rem;line-height: 1.2rem;background-color: #fff; }
.timeType > li.cur {background: #F1F8FF;
    border: 1px solid #4096EE;
    border-radius: 0.8rem;
    color: #4096EE;
    box-sizing: border-box;
}
/* 大屏主题 */
.bigscreen #main { background: #282537;
 background-image: -webkit-radial-gradient(top, circle cover, #626186 0%, #14102f 80%);
 background-image: -moz-radial-gradient(top, circle cover, #626186 0%, #14102f 80%);
 background-image: -o-radial-gradient(top, circle cover, #626186 0%, #14102f 80%);
 background-image: radial-gradient(top, circle cover, #626186 0%, #14102f 80%);}
.bigscreen #main .MyText {fill: #666;}
.bigscreen .topBtn {background-color: rgb(97, 96, 133);
    /* border-bottom-color:#191534;  */
}
.bigscreen input.selectInput {background-color: transparent}
.bigscreen #mapTitle {fill:#FFF;}
.bigscreen .legend,.bigscreen .overView,.bigscreen #zoomBtn  {background-color: #45416d;color: #fff;}
.bigscreen #legend {border-radius: 3px; top:4rem;right:.4rem;left: auto;width: 5rem;padding: .5rem; height: 7rem;}
.bigscreen #zoomBtn {bottom: 0.5rem !important;}
.bigscreen #zoomBtn .refresh {display: none;}
.bigscreen #overView {right:.4rem;left: auto;}
.bigscreen .selectInput {height: 1.2rem; width: 100%;padding:0; font: .8rem/1.2rem 'Microsoft Yahei';color: #fff;} 
.bigscreen .dateSelect {height: 1.2rem; }
.bigscreen .dateSelect .upicon{top: .55rem; }
.bigscreen .dw-persp, .bigscreen .opt_select,.bigscreen .dwo{top: 1.55rem ;}
.bigscreen .spec br {display: none;}
.bigscreen .dateSelect {border-left-color: #474568;}
.bigscreen .legend h3 {font-size: .8rem !important;}
.bigscreen .legendList li {padding: 4px 0 4px .35rem}
.bigscreen .legend.type2 { height: 9.5rem!important;}
.bigscreen #overView.isdata {bottom: 6rem !important; width: 6rem; height: 8.5rem;}
.bigscreen .overView .view {width: 5rem;padding:.2rem 0;margin:0 .5rem;border-right: 0;border-bottom: 1px solid #ccc;}
.bigscreen .overView .view3 {border-right: none;border-bottom: none;}
.bigscreen #zoomBtn {width: 6rem;height: 2rem;}
.bigscreen #zoomBtn .sbtn {width: 3rem;color: #fff;font: 2rem/1.7rem 'Microsoft Yahei';float: left; height: 2rem;}
.bigscreen #zoomBtn .jian{border-left: 1px solid #9a9a9a;border-top: none;margin-left: -1px;}
.bigscreen #zoomBtn .gray {color: #403e60 !important;}
.bigscreen .btn-info.active,.bigscreen .btn-info:active{background-color: #f4f3f1;color: #14102f;border-color: #14102f;}
.bigscreen .btn-info{background-color: #76759a;color: #fff;border-color: #14102f;}
.bigscreen .overView .view .viewTitle {color:#e8d5d5;}
.bigscreen.isdata  .slowDown {height: auto !important;}
.bigscreen .android-ics.light .dwbc {border-top:none;}
.bigscreen .loading::before{color: #fff;}
.page{background: #fff}
.page input[type=checkbox]{
    cursor: pointer;
    position: relative;
    width: 15px;
    height: 15px;
    font-size: 14px;
}
.page input[type=checkbox]::after{
    position: absolute;
    top: 0;
    background-color: #fff ;
    color: #000;
    width: 15px;
    height: 15px;
    display: inline-block;
    visibility: visible;
    text-align: center;
    content: ' ';
    border: 1px solid #003A90;
}
       
.page input[type=checkbox]:checked::after{
    content: "✓";
    background-color: #003A90 ;
    color: #fff;
    font-size: 12px;
    font-weight: bold;
}
/* 蓝色主题 */

.theme-blue.page,.theme-blue #main { background: #011b52;}
.theme-blue #main .MyText {fill: #fff;font-size: 0.6rem;}                        /*地图文字颜色 */
.theme-blue #main path {stroke: #40464b;stroke-width: 1px;}  /*省份边界颜色 */
.theme-blue .topBtn {background-color: rgba(1, 34, 105, 1);border-color:#036bc3; }
.theme-blue input.selectInput {background-color: transparent}
.theme-blue #mapTitle {fill:#FFF;}
.theme-blue .overView{font-size: 0.7rem;}
.theme-blue .legendList li{font-size: 0.65rem;}
.theme-blue .legend,.theme-blue .overView,.theme-blue #zoomBtn  {background-color: #001a52;color: #fff;border: 1px solid #036bc3;}
.theme-blue .selectInput {color: #fff;} 
.theme-blue .dateSelect {border-left-color: rgba(3, 107, 195, 1);}
.theme-blue .dateSelect .upicon{background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAQCAYAAAAMJL+VAAABhUlEQVQ4T62TMU8CYQyGn95AjAtxYGNkdTRGEhYSTVgcXAk/QAcEJkxMjIPKxCGD/gDC4uDgYtTExUSjKysjm4NhMYbhar47CRwc3KF2u17b52v7VmjoFbCKxSZF6fEf1tQkDg9AR7D1DmHLfPBFhqr0/8SoaZwlntxHK/dCUxM4PAMp4JEPchzL4FeQI42xwi2QBbpYpMUtVNcUlgtJILQpUkBEF4KoCk1aKHngHYc0Fel6AGMNXXc7gGWEE/blcCHAuZ6iHACfbgcleTX5I4AH2QauAQulQFnakSC25hFagAPsUJKbYZ4fYLy27iJcAAOUHGUxXc02W7OIO/cYyh5luRwPngZ4nZwBVaCPQ4aKdAIJdTXyNoqJAzVKYkbks2CAf2E9LDambsTT+guQnCeMYIB5g19y/hsZ13qItGcDDCSokPGPtB56nPMBptjkKIzP03rw6CLtYHJT/mWav/OXP5Yf3sEweCRH00G4fH/yogM8+a7hIFTkLdIBTl1y1KwF4r4Br4+MN1qcQFUAAAAASUVORK5CYII=) no-repeat center top transparent;background-size: 80% 80%;}
.theme-blue .dwwr{background: rgba(1, 34, 105, 1);}
.theme-blue .slowDown{border-top: 0;border-bottom: 0;}
.theme-blue .opt_select .optline{display: none;}
.theme-blue .select-title{color: #003A90;}
.theme-blue .nav-con{background: rgba(1, 34, 105, 0.99);}
.theme-blue .optItem {color: #0090ff;border: 1px solid rgba(3, 107, 195, 1);background: #fefefe;}
.theme-blue .optItem.selected{background:#003A90;color: #fff;}
.theme-blue .optItem .val,.theme-blue .optItem .rise  {color: #0090ff}
.theme-blue .opt_select .optItem.selected p{color:#fff}
.theme-blue .btn-info.active,.theme-blue .btn-info:active{background-color: #0090ff;color: #fff}
.theme-blue .btn-info{background-color: #001a52;color: #fff;border-color: #0090ff;}
.theme-blue .overView .view .viewTitle{color: #5571ae;}
.theme-blue .overView .view{border-right:1px solid rgba(3, 107, 195, 1);}
.theme-blue .overView .view3{border-right: none;}
.theme-blue #zoomBtn .refresh{width: 1.7rem;height: 1.7rem;top: -2.2rem;margin-left: -0.3rem;border-radius: 3px;background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAADFklEQVRYR82XS0hUYRSAv3PNFymKFhEJUbnI0h5EJEH0gMBF70ii2rWoJHScVkYRRYJl6GgJBUGbHovQkggKooQsMKLssahVFkG06IGBlOk98c+9ozPOjF4dH/2rmXvP4zv/Off/zxG8rjrNIYmtKBuAJcB8IMNV7wY+onRi8Yh+WvHLdy+mZUShRl2EzXFgB5Ayorwj0AfcRDlLpXQOpxMf4IxmkkoNcBCwhhjRYMTww30+A8gDhtozcpdQqqiUn7FAYgPU6WIsbgP5YUpmm69j08x0nnJAeiIMGuAUViKUAruBrLD3H4Bt+OT1UIhogICuAu4B2a5wL0ItNufiRREVWb1mI5QDR4FU930PNiX45fGAfKPmRQI4kT8Jo3+PzS788sZj7iPFzutC+mkBCgaK1WId5fKSRs3Dpm0QwMn5i4FtF56RTAllEsrzmBio0SzSuYOyxjXQhc0WLG4BCwYBAtoElLlC70hhdcLOQ8gGIo12oNB91A8kmd8OQIMWopgCMf9NzldQIW/HFnIcLScdZofTwyUcgIDecCvXIFRTIcfG1bkxVqf5WMFdmBUJcEFz6eMLkAx0o8z1XO1eKR3nbcCc6M8woPuBy+6Li/jkkFe7nuSGce7UQINeRdkbNGazEb888GTYi9AJtcjhFHbwlIy5hICa4isClHQyok44L44SkDEAv9xbrQufzEvA1phUDYC5MMzqxCfLx2QlAaX/AmDKUzDFRTiRn6GH2jA1MLEH0VCIBi1FOY1wjQo5KUzGURyCaNIM/tIF5AK/8Un65F1GzqVnOqTqII9wlwrZ5ADUaREWryb5Oi7GJx2T05CYre+lHWGpG73J/z5nI0Jroloyx24rsN519ZlpLOOwfIsEcFIxvk1pvRYgNIc1paYzXotfnofijteW3w/rjEffljvVXgX4gTTXWQ/KZirlYfiXOb6DSSrFwE5gD5AZ5ugTynYqxfSEEWs8RjNjYyYwO8YIZ5xdIYUj8TrskYdTpy5Mkzqa4fQP0IJFbXAIGWaNDBBSjj+em37CVPRXhA6gfTTj+T9HfCHKjWkGSAAAAABJRU5ErkJggg==) no-repeat 50% 50%;background-size: 50% 50%;border: 1px solid #0090ff;}
.theme-blue #zoomBtn .jian{border-top: 1px solid #0090ff;}
.theme-blue #zoomBtn .sbtn{color: #0090ff}
.theme-blue .android-ics.light .dwbc {border-top:1px solid rgba(3, 107, 195, 1)}
.theme-blue .android-ics.light .dwb-n .dwb, .theme-blue .android-ics.light .dwb-c .dwb{border-right: none;}
.theme-blue .android-ics.light .dwb {
    color: unset;
}
.theme-blue .android-ics .dw .dwwol{
    background: unset;
}
.theme-blue .h5 {height: .25rem;background-color: unset;}
.theme-blue .android-ics.light .dww .dw-li{color: #fff;}
.theme-blue .android-ics.light .dwwo{
    background: linear-gradient(rgba(1, 34, 105, 1) 0,rgba(245,245,245,0) 52%,rgba(245,245,245,0) 48%,rgba(1, 34, 105, 1) 100%);
    background: -webkit-gradient(linear,left bottom,left top,from(rgba(1, 34, 105, 1)),color-stop(.52,rgba(245,245,245,0)),color-stop(.48,rgba(245,245,245,0)),to(rgba(1, 34, 105, 1)));
    background: -moz-linear-gradient(rgba(1, 34, 105, 1) 0,rgba(245,245,245,0) 52%,rgba(245,245,245,0) 48%,rgba(1, 34, 105, 1) 100%);
    background: -o-linear-gradient(rgba(1, 34, 105, 1) 0,rgba(245,245,245,0) 52%,rgba(245,245,245,0) 48%,rgba(1, 34, 105, 1) 100%);
}
.theme-blue .legendList .color0 {background-color: #972440;}
.theme-blue .legendList .color1 {background-color: #AD3250;}
.theme-blue .legendList .color2 {background-color: #DE5B79;}
.theme-blue .legendList .color3 {background-color: #14a880;}
.theme-blue .legendList .color4 {background-color: #14824b;}
.theme-blue .legendList .color5 {background-color: #0e6b30;}
.theme-blue .android-ics .dw .dwwol{border-top: 1px solid rgba(3, 107, 195, 1);border-bottom: 1px solid rgba(3, 107, 195, 1);}
.theme-blue .loading::before{color: #fff;}
/* .theme-blue .datehead{line-height: 70px;} */
.theme-blue .nodata{color: #fff;}
/* page页面 */
.theme-blue .datehead,.theme-blue .cont { background: #011b52;color: #fff;}
.theme-blue .list_T{background: rgba(1, 34, 105, 1)}
.theme-blue .list_T li{color: #fff;border-right: none;}
/*.theme-blue .datehead,.theme-blue .cont ul{border-bottom: 1px solid rgba(3, 107, 195, 1);}*/
.theme-blue .h10 { background: #011b52;}
/* 下载弹窗 */
.downloadDialog {
    width: 100%;
    height: 100%;
    display: none;
}
.download_mark {
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, .5);
    z-index: 9999;
   
}
.download_cont {
    width: 240px;
    height: 160px;
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    background: #fff;
    z-index:9999;
    border-radius: 20px;
}
.download_cont .title {
    margin: 10px 0;
    text-align: center;
    font-size: 16px;
    letter-spacing: 2px;
    color: #666;
    font-weight: bold;
}
.download_cont .note {
    text-align: center;
    font-size: 14px;
    color: #666;
}
.download_cont .download_btn {
    margin: 20px 30px 0;
    height: 30px;
    text-align: center;
    
}
.download_cont .download_btn button {
    padding: 0;
    width: 58px;
    height: 30px;
    line-height: 30px;
    border: none;
    background: #003A90;
    color: #fff;
    font-size: 15px;
    border-radius: 20px;
    outline: none;
    text-align: center;
}
.download_cont .download_cancle {
    float: left;
}
.download_cont .download_sure {
    float: right;
}
.add-jkk .val{
    display: none;
}
.add-jkk .rise{
    display: none;
}

.China_detail {
    display: none;
}

.global_detail {
    display: none;
}
/* 世界地图详情页表格 */
.global_detail .list_T li:nth-child(1),.global_detail #citydata li:nth-child(1)  {
    width: 10%;
}

.global_detail .list_T li:nth-child(2),.global_detail #citydata li:nth-child(2)  {
    width: 20%;
}

.global_detail .list_T li:nth-child(3),.global_detail #citydata li:nth-child(3)  {
    width: 15%;
}

.global_detail .list_T li:nth-child(4),.global_detail #citydata li:nth-child(4)  {
    width: 15%;
}

.global_detail .list_T li:nth-child(5),.global_detail #citydata li:nth-child(5)  {
    width: 15%;
}

.global_detail .list_T li:nth-child(6),.global_detail #citydata li:nth-child(6)  {
    width: 10%;
    border-right: none;
}
/* 世界地图放大缩小按钮 */
.up-scale,  .down-scale {
    position: absolute;
    bottom: 6.6rem;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    color: #eee;
    background: #ccc;
    text-align: center;
    font-size: 14px;
    font-size: 1.1rem;
    line-height: 30px;
    display: none;
}
.down-scale {
    right: 1.5rem;
}
.up-scale {
    right: 3.6rem;
}
/* 更改ios价格指数页面样式遮挡问题 */
.swiper-wrapper{
    background: #fff;
}