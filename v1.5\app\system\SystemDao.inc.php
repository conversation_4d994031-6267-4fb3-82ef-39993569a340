<?php
//require_once('../../steelconf_v3/debug.php');
class SystemDao extends Dao{

  public function __construct( $writer ){
    parent::__construct( $writer );
  }

   //取得GUID
  public function getGUID( $delminus = true ){
      $sGuid = $this->getOne( "SELECT uuid() AS guid" );
      if ($delminus){
         $sGuid =  str_replace("-", "", $sGuid);
      }
      return  $sGuid;
  }

   //根据会员id取得公司简称
  public function getCompnameBymid( $mid ){
     return $this->getOne( "SELECT compabb FROM member WHERE mbid = $mid", 3600 * 24 );
  }
  public function getAdminUser( $mid ){
      return $this->getRow( "select member.adminid as adminid,adminuser.truename as truename,member.mbid as mid,adminuser.tel as tel,adminuser.mobil as mobil from member left join adminuser on member.adminid=adminuser.id WHERE member.mbid='$mid'" );
  }
  
  //获取会员信息
  public function getCompany( $mid ){
     return $this->getRow( "SELECT * FROM member WHERE mbid = $mid limit 1");
  }
  //获取会员管理员信息
  public function getadmin( $mid ){
     return $this->getRow( "SELECT * FROM `adminuser` WHERE `mid` =$mid and ismaster=1  limit 1");
  }

  //根据用户名与设备标识，获取app_session
  public function getAppSession( $UserName, $SignCS, $mc_type ){
    //首先从缓存表取数据
    $row = $this->getRow( "SELECT * FROM app_session_temp WHERE UserName = '$UserName' AND SignCS = '$SignCS' AND mc_type='$mc_type'" );
    if (empty( $row )){
      //缓存表没有数据，则尝试从实体表取数据
      $row = $this->getRow( "SELECT * FROM app_session WHERE UserName = '$UserName' AND SignCS = '$SignCS' AND mc_type='$mc_type'" );
    }
    return $row;

  }
  //取得已登陆用户
  public function getUser( $GUID, $SignCS, $mc_type ){
	return $this->getRow( "SELECT * FROM app_session_temp WHERE GUID = '$GUID' AND mc_type='$mc_type'" );
  }

  //日志记录
  public function WriteLog($Mid, $Uid, $SignCS, $ActionName,$Actionstr, $ActionIp, $SystemType, $SystemVersion,$MessageTitle='',$MessageId='',$MessageDesc='' ,$mc_type='0'){ 
     $this->execute( "INSERT INTO app_logs SET Mid='$Mid', Uid='$Uid', SignCS='$SignCS', ActionName='$ActionName',Actionstr='$Actionstr',ActionDate=NOW(),ActionIp='$ActionIp', SystemType='$SystemType', SystemVersion='$SystemVersion',MessageId='$MessageId', MessageTitle='$MessageTitle', MessageDesc='$MessageDesc' , mc_type='$mc_type'");
  }

  public function create_reszbtemp($memberid) {

		$table_name = "reszbmember" . $memberid;

		$sql1 = "DROP TABLE IF EXISTS  " . $table_name;
		$sql2 = "CREATE TABLE IF NOT EXISTS `$table_name` (
					  `id` int(20) NOT NULL NOT NULL auto_increment,
					  `memberzbid` int(20) NOT NULL default '0',
					  `lb` varchar(20) default NULL,
					  `pz` varchar(20) default NULL,
					  `pm` varchar(50) default NULL,
					  `cz` varchar(40) default NULL,
					  `gg` varchar(60) default NULL,
					  `jg` varchar(20) default NULL,
					  `sl` decimal(8,3) default '20.000',
					  `cd` varchar(50) default NULL,
					  `jhdd` varchar(40) default NULL,
					  `jhck` varchar(80) default NULL,
					  `bz` varchar(20) default NULL,
					  `jz` varchar(20) default NULL,
					  `bmzk` varchar(20) default NULL,
					  `rcl` varchar(20) default NULL,
					  `remark` varchar(200) default NULL,
					  `memberid` varchar(10) NOT NULL default '',
					  `mbdate` datetime NOT NULL default '0000-00-00 00:00:00',
					  `wherefrom` int(2) NOT NULL default '0',
					  `gga` varchar(20) NOT NULL default '',
					  `ggb` varchar(10) NOT NULL default '',
					  `ggc` varchar(10) NOT NULL default '',
					  `ggd` varchar(10) NOT NULL default '',
					  `effect_date` int(2) NOT NULL default '0',
					  `res_type` tinyint(2) unsigned NOT NULL default '0',
					  `res_ch` varchar(6) NOT NULL default '',
					  `istjzy` tinyint(2) unsigned NOT NULL default '0',
					  `isxh` tinyint(2) unsigned NOT NULL default '0',
					  `isbxg` int(11) default '0',
					  `autozy` int(11) NOT NULL default '0',
					  PRIMARY KEY  (`id`),			  
					  KEY `memberid` (`memberid`),
					  KEY `memberzbid` (`memberzbid`),
					  KEY `jhdd` (`jhdd`),
					  KEY `cd` (`cd`),
					  KEY `pz_2` (`pz`,`jhdd`),
					  KEY `wherefrom` (`wherefrom`),
					  KEY `lb` (`lb`),
					  KEY `istjzy` (`istjzy`),
					  KEY `mbdate` (`mbdate`)
					) ENGINE=MyISAM;
					";
        //DebugInfo_News($params['resource']);
		$this->execute($sql1);
		$this->execute($sql2);
		return true;

	}

	public function create_memberzbtemp($memberid) {

		$table_name = "memberzb" . $memberid;

		$sql1 = "DROP TABLE IF EXISTS  " . $table_name;
		$sql2 = "CREATE TABLE `$table_name` (		  
		  `ChCode` varchar(255) NULL,
		  `ZyType` varchar(255) NULL,
		  `IsZb` tinyint(4) NULL,
		  `ZbCitys` varchar(255) NULL,
		  `ZbDesc` varchar(1000) NULL,
		  `PublishDate` varchar(255) NULL
		) ENGINE=MyISAM DEFAULT CHARSET=latin1;
		";

		$this->execute($sql1);
		$this->execute($sql2);
		return true;

	}

	public function update_reszbtemp($memberid, $arr) {

		$table_name = "reszbmember" . $memberid;		
		$arr['cz']=$this->string_replace($arr['cz']);
		$arr['cd']=$this->string_replace($arr['cd']);
		$arr['jhdd']=$this->string_replace($arr['jhdd']);
		$sql = "update  " . $table_name . " set 
										memberzbid='" . $arr['memberzbid'] . "',
										lb='" . $arr['lb'] . "',
										pz='" . $arr['pz'] . "',
										pm='" . $arr['pm'] . "',
										cz='" . $arr['cz'] . "',
										gg='" . $arr['gg'] . "',
										gga='" . $arr['gga'] . "',
										ggb='" . $arr['ggb'] . "',
										ggc='" . $arr['ggc'] . "',
										ggd='" . $arr['ggd'] . "',
										jg='" . $arr['jg'] . "',
										sl='" . $arr['sl'] . "',
										cd='" . $arr['cd'] . "',
										jhdd='" . $arr['jhdd'] . "',
										jhck='" . $arr['jhck'] . "',
										bz='" . $arr['bz'] . "',
										jz='" . $arr['jz'] . "',
										bmzk='" . $arr['bmzk'] . "',
										rcl='" . $arr['rcl'] . "',
										remark='" . $arr['remark'] . "',
										memberid='" . $arr['memberid'] . "',
										mbdate=NOW(),
										effect_date='" . $arr['effect_date'] . "',
										res_type='" . $arr['res_type'] . "',
										res_ch='" . $arr['res_ch'] . "',										
										isxh='" . $arr['isxh'] . "'			
										where id='" . $arr['id'] . "' ";
		echo $sql;
		return $this->execute($sql);
	}

	public function insert_reszbtemp($memberid, $arr) {

		$table_name = "reszbmember" . $memberid;
		
		$arr['cz']=$this->string_replace($arr['cz']);
		$arr['cd']=$this->string_replace($arr['cd']);
		$arr['jhdd']=$this->string_replace($arr['jhdd']);
		
		$sql = "insert into " . $table_name . " (memberzbid,lb,pz,pm,cz,gg,gga,ggb,ggc,ggd,jg,sl,cd,jhdd,jhck,bz,jz,bmzk,rcl,remark,memberid,mbdate,effect_date,res_type,res_ch,istjzy,isxh)values(
										'" . $arr['memberzbid'] . "',
										'" . $arr['lb'] . "',
										'" . $arr['pz'] . "',
										'" . $arr['pm'] . "',
										'" . $arr['cz'] . "',
										'" . $arr['gg'] . "',
										'" . $arr['gga'] . "',
										'" . $arr['ggb'] . "',
										'" . $arr['ggc'] . "',
										'" . $arr['ggd'] . "',
										'" . $arr['jg'] . "',
										'" . $arr['sl'] . "',
										'" . $arr['cd'] . "',
										'" . $arr['jhdd'] . "',
										'" . $arr['jhck'] . "',
										'" . $arr['bz'] . "',
										'" . $arr['jz'] . "',
										'" . $arr['bmzk'] . "',
										'" . $arr['rcl'] . "',
										'" . $arr['remark'] . "',
										'" . $arr['memberid'] . "',
										NOW(),
										'" . $arr['effect_date'] . "',
										'" . $arr['res_type'] . "',
										'" . $arr['res_ch'] . "',
										'" . $arr['istjzy'] . "',
										'" . $arr['isxh'] . "'			
										)";
		return $this->execute($sql);
	}
	
	public function del_one_reszbtemp($memberid,$id){
		$table_name = "reszbmember" . $memberid;
		$sql=" DELETE FROM ".$table_name. "  WHERE id=$id limit 1";
		return $this->execute($sql);
	}
	
	public function insert_memberzbtemp($memberid, $arr) {

		$table_name = "memberzb" . $memberid;
		$sql = "insert into " . $table_name . " (ChCode,ZyType,IsZb,ZbCitys,ZbDesc,PublishDate)values('" . $arr['ChCode'] . "','" . $arr['ZyType'] . "','" . $arr['IsZb'] . "','" . $arr['ZbCitys'] . "','" . $arr['ZbDesc'] . "',NOW())";
		return $this->execute($sql);
	}
	
	public function string_replace($str){
		
		$str = trim($str);
		$str = eregi_replace("^ +| +$", "", $str); //去掉头尾的半角空格
		$str = eregi_replace("^　+|　+$", "", $str); //去掉头尾的全角空格
		$str = eregi_replace("　", " ", $str);       //将全角空格转换为半角空格
		$str = eregi_replace(" +", " ", $str);    //将一个或多个半角空格转换为指定的符号		
		
		
		$str = str_replace(" ", ",", $str);
		$str = str_replace("/", ",", $str);
		$str = str_replace("，", ",", $str);
		$str = str_replace("|", ",", $str);
		$str = str_replace("、", ",", $str);
		$str = str_replace("；", ",", $str);
		$str = str_replace(";", ",", $str);
		$str = str_replace(".", ",", $str);
		$str = str_replace("／", ",", $str);
		
		return $str;
	}

	//注销
	public function logout_adminuser($id){
		$this->execute("update adminuser set IsClose=1 where id=$id");
	}
	
}

?>