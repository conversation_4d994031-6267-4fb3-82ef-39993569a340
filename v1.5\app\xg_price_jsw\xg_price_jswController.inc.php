<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );


class xg_price_jswController extends AbstractController{
    public function __construct(){
        parent::__construct();
        $this->_action->setDao(new xg_price_jswDao('XINSTEEL'));
        $this->_action->maindao = new xg_price_jswDao('91R');

        $this->_action->gcdao = new xg_price_jswDao('GC');

        $this->_action->drcwdao = new xg_price_jswDao('DRCW');
        $this->_action->drcdao = new xg_price_jswDao('DRC');

        $this->_action->sms = new xg_price_jswDao("98SMR");

        $this->_action->t1Dao=new xg_price_jswDao("MAIN");

        
    }
    
    public function _dopre(){
        //$this->_action->checkSession();
    }

    public function v_index() {
        $this->_action->index($this->_request);
    }

    public function v_base_data_manage() {
        $this->_action->base_data_manage($this->_request);

    }

    public function do_uploadFile()
    {
        $this->_action->uploadFile($this->_request);
    }
    public function do_sheetfile()
    {
        $this->_action->sheetfile($this->_request);
    }

    public function do_update_base_data()
    {
        $this->_action->update_base_data($this->_request);
    }
    public function v_data_zd_manage() {
        // header("Content-type:text/html;charset=utf-8");
        $this->_action->data_zd_manage($this->_request);

    }
    public function do_getdatalist()
    {
        $this->_action->getdatalist($this->_request);
    }
    public function v_data_zd_input() {
        $this->_action->data_zd_input($this->_request);
    }
    public function do_data_zd_input()
    {
        $this->_action->do_data_zd_input($this->_request);
    }

    public function do_del_base_data()
    {
        $this->_action->del_base_data($this->_request);
    }

    public function v_price_table() {
        $this->_action->price_table($this->_request);
    }
    public function do_getEchartData()
    {
        $this->_action->getEchartData($this->_request);
    }

    public function v_price_echart() {
        $this->_action->price_echart($this->_request);
    }


    public function do_downExcel() {
        $this->_action->downExcel($this->_request);
    }

    public function v_nopower() {
        $this->_action->nopower($this->_request);
    }

    public function v_jxpj() {
        $this->_action->jxpj($this->_request);
    }

    public function do_getjxpjlist() {
        $this->_action->getjxpjlist($this->_request);
    }


    public function do_sheetfileJxpj() {
        $this->_action->sheetfileJxpj($this->_request);
    }


    public function v_jxpj_data_manage() {
        $this->_action->jxpj_data_manage($this->_request);
    }


    public function v_uploadhuizong() {
        $this->_action->uploadhuizong($this->_request);
    }


    public function v_sd_steel_data_manage() {
        $this->_action->sd_steel_data_manage($this->_request);
    }

    public function do_get_sg_steel_datalist() {
        $this->_action->get_sg_steel_datalist($this->_request);
    }

    public function v_sd_steel_data_input() {
        $this->_action->sd_steel_data_input($this->_request);
    }
    
    public function do_update_sd_steel_base_data() {
        $this->_action->update_sd_steel_base_data($this->_request);
    }
    public function do_sd_steel_downExcel() {
        $this->_action->sd_steel_downExcel($this->_request);
    }

    public function do_del_sd_steel_base_data() {
        $this->_action->del_sd_steel_base_data($this->_request);
    }
    public function v_sd_steel_order_manage() {
        $this->_action->sd_steel_order_manage($this->_request);
    }
    public function v_catdata() {
        $this->_action->catdata($this->_request);
    }
}