<?php 
// require_once('../../../../steelconf_v3/debug.php');
class DcWebView3Action extends AbstractAction
{ 
    public function __construct()
    {
        parent::__construct();
    } 
	// 检查登录Session
    public function index($params)
    {
			$sql="select * from app_session_temp where  GUID ='".$params['GUID']."'  order by  LoginDate DESC  limit 1";
			$res=$this->_dao->getRow($sql);
			$Mid=$res['Mid'];
			$uid=$res['Uid'];
			
			// echo $uid;
			$type=$params['Type'];
			$username=$res['UserName'];
			$SignCS=$res['SignCS'];
			$sql="select * from app_license where Mid='$Mid'   and  mc_type=1 order by CreateDate desc limit 1";
			$res=$this->_dao->getRow($sql);		
			$id=$res['ID'];
			$sql="select * from app_license_privilege  where liD ='".$id."' and privilege!='' and mc_type='1' ";
			$res=$this->_dao->query($sql); 
			
						foreach($res as $key => $value){
				       $res[$key]['order']='10';
					// echo "<pre/>";echo '排序';print_r($value['orderno']);
					
					$r=explode(',',$value['privilege']);
					$d=explode(',',$value['orderno']);
			     // echo "<pre/>";echo '分割';print_r($d[$type-1]);
				  if( $r[$type-1]==1){
					  //判断领导的顺序进行排序 给数组一个新字段
					   $res[$key]['order']=$d[$type-1];
					 
				  }
					// echo "<pre/>";echo '分割';print_r($r);exit;
					
			}
			$order=array();
			foreach($res as $re){
				$order[]=$re['order'];
			}
			array_multisort($order,SORT_ASC,$res);//安照order来排序
			
			
			
			foreach($res as $key =>$v){
				$r=explode(',',$v['privilege']);
				if(!array_key_exists($r[$type-1],$GLOBALS['power'])) continue;
				$p_arr[$v['username']]=$r;
				//$use[]=$v['username'];
				$adminname_uid[$v['username']]=$v['uid'];
				$adminname[$v['username']]=$v['truename'];
			}
				if($params['curdate']!=''){
				$date=$params['curdate'];
				// echo $date;
			}else{
		       $date = date("Y-m-d H:i:s");
			}
		
		$xunday1 ='';
		$nodatatitle = '暂无';
		if(date("d",strtotime($date))<11){
			$xunday1 = '01';
			$xunday2='10';
			$nodatatitle .= date('Y年n月', strtotime($date))."上旬碳结钢旬定价";
			
		}elseif(date("d",strtotime($date))<21){
			$xunday1 = '11';
			$xunday2='20';
			$nodatatitle .= date('Y年n月', strtotime($date))."中旬碳结钢旬定价";
			
		}else{
			$xunday1 = '21';
			$BeginDate=date('Y-m-01', strtotime($date)); 
			$xunday2=date('d', strtotime("$BeginDate +1 month -1 day"));
			$nodatatitle .= date('Y年n月', strtotime($date))."下旬碳结钢旬定价";
		}
		
		
		$this_xun_start = date("Y-m-$xunday1",strtotime($date));
		
		 $this->assign( "curdate",$this_xun_start);
		 
		$this_xun_end = date("Y-m-$xunday2",strtotime($date));
			// echo $this_xun_end;
		
			
			// $sql="select * from ng_price_model where modeltype=$type and   createtime>='".date('Y-m-d',time())." 00:00:00' and  createtime<='".date('Y-m-d',time())." 23:59:59' order by createtime desc limit 1";	
		$sql="select * from ng_price_model where modeltype=$type and date>='$this_xun_start' and  date<='$this_xun_end' order by id desc limit 1";	
			
			$modeldb=$this->drc->getrow($sql);
			// print_r($modeldb);exit;
			
			if(!empty($modeldb)){	
				// $modeldb['modeltitle'] = iconv('gbk','utf-8',$modeldb['modeltitle']);
				$modeldb['modelcontent']=html_entity_decode($modeldb['modelcontent'],ENT_QUOTES,'ISO-8859-1');
				$cc = explode("预计本旬", $modeldb['modelcontent']);
				$cc[0] =mb_convert_encoding($cc[0], "UTF-8", "GBK");
				$modeldb['modelcontent'] = $cc[0]."<h4 style=\"text-align:left;\">预计本旬".$cc[1];
				// $modeldb['modelcontent'] =mb_convert_encoding($modeldb['modelcontent'], "UTF-8", "GBK");
				$uidsavebtn=false;
				$uidleadersavebtn=false;//1032
				
				$sql2="select count(*) from ng_price_model_detail where modelid=".$modeldb["id"]." and modelprice_type=$type";
				
				$da=$this->drc->getOne($sql2);
								
				if($da==0){
				 			//修改
		
					if($params['curdate']!=''){
						$firstday=date('Y-m-01',strtotime($params['curdate']));
						$showtime=date("Y-m",strtotime($params['curdate']));
						$showxun=date("d",strtotime($params['curdate']));
						
						}else{
						$firstday = date('Y-m-01');
						$showtime=date("Y-m");
						$showxun=date("d");
					}
			
			
			
				$lastday = date('d',strtotime("$firstday +1 month -1 day"));
				
				$lastxunsd="20";//前一旬的开始日
				$lastxuned=$lastday;//前一旬的结束日
				if($showxun > "10" && $showxun < "21"){
					$showxun="中";
					$lastxunsd="01";
					$lastxuned="10";
					$lastM=$showtime;
				
				}else if($showxun <="31" && $showxun>"20"){
					$showxun="下";
					$lastxunsd="10";
					$lastxuned="20";
					$lastM=$showtime;
				}else{
					if($params['curdate']!=''){	 
					$lastM=date('Y-m', strtotime("m -1 month",strtotime($params['curdate'])));
					$lastxuned=date('t',strtotime('last month',strtotime($params['curdate'])));
					}else{
					$lastM=date('Y-m', strtotime("m -1 month"));
					$lastxuned=date('t',strtotime('last month'));
					}
					$showxun="上";
					//print_r($lastxuned);exit;

				}
				$curdate=$params['curdate'];
		
				$ddl=$this->bcsc_xs($showtime,$lastM."-".$lastxuned,$showxun,$curdate);//算得订单率
		
				
				$arr=$this->djjy($lastM."-".$lastxunsd,$lastM."-".$lastxuned,$ddl,$curdate);//定价建议---HTML不处理
				$modelid2=$modeldb["id"];
				$moxdj=$arr['moxdj'];
				// print_r($moxdj);exit;
				$zd=$arr['zd'];
				$ngyg=$moxdj-$zd;
				
				$end=$lastM."-".$lastxuned;
				
				$start=$lastM."-".$lastxunsd;
				
				$date1=$start." "."00:00:00";
				$date2=$end." "."00:00:00";
				
				
				$sql = "SELECT the_price_tax,changerate_tax FROM   `steelprice_info`  where variety='碳结钢'  and is_show=2 and gcid='122' and ftime>= '".$end."' and ftime <= '".date("Y-m-d")."'";
			// print_r($sql);exit;
		    $res_tjg=$this->gcdao->query($sql);
		    $the_price_tax2=$res_tjg[0]['the_price_tax'];//直接取的价格
		    $changerate_tax2=$res_tjg[0]['changerate_tax'];//直接取的涨跌
			if($the_price_tax2==""){//为空的算法
			$sql="select * from marketconditions where mastertopid='0822442' and mconmanagedate > '".$date1."' and mconmanagedate < '".$date2."'";
			//print_r($sql);exit;
			$res_tjg_arr=$this->maindao->query($sql);
			$res_tjg=end($res_tjg_arr);
			//前一旬本期价格
			
			//$sql_id="select * from steelprice_base where `steel_id`='122' and `steel_name`='淮钢'ORDER BY `id` DESC";
			$sql_last = "SELECT the_price_tax,changerate_tax FROM   `steelprice_info`  where variety='碳结钢'  and is_show=2 and gcid='122' and ftime< '".$end."' and ftime >= '".$start."'";
			 // print_r($sql_last);exit;
			//查上一期的值
			$res_tjg_last=$this->gcdao->query($sql_last);
			
			
			$the_price_tax_last=$res_tjg_last[0]['the_price_tax'];//上期价格
			
			$the_price_tax=$res_tjg['price']+140-$the_price_tax_last;

			$changerate_tax2=$this->jsxundj($the_price_tax);
			
			
			$arr=$this->bxundj($changerate_tax,$ddl,$curdate);
			$the_price_tax2=$the_price_tax_last+$changerate_tax2;		
		}		 
			$modeldb['modelcontent'].="<h4 style='text-align:left;'>预计本旬淮钢45#Φ50mm碳结钢价格为".$the_price_tax2."元/吨";
			
			
			if($changerate_tax2>0){
				 $modeldb['modelcontent'].="，较前旬上调".$changerate_tax2."元/吨，根据当前接单进度，建议本旬定价：</h4>";
			}
			else if($changerate_tax2<0){	
			 $modeldb['modelcontent'].="，较前旬下跌". abs($changerate_tax2)."元/吨，根据当前接单进度，建议本旬定价：</h4>";
			}else{
				 $modeldb['modelcontent'].="，较前旬持平
				 ，根据当前接单进度，建议本旬定价：</h4>";
			}
			
		         $modelcontent2=htmlentities($modeldb['modelcontent'], ENT_QUOTES,"UTF-8");
				 // $modelcontent2=html_entity_decode($modelcontent2,ENT_QUOTES,'UTF-8');
				 $sql="update ng_price_model set modelcontent='".$modelcontent2."' where id=".$modelid2. "";//ID唯一
			     $this->drc->execute($sql);	
				 
				 $curtime=$this_xun_start;//上中下三旬
		         $ins_xundj="insert into ng_price_model_detail(modelid,modelprice,modelprice_updown,modeloldprice,modelprice_type,	modelpirce_name,modelpirce_name_sort,Mid,uid,GUID,Ismakepricy_men,date,createtime) value('".$modelid2."','".$moxdj."','".$zd."','".$ngyg."','2','45#Ф50mm碳素结构钢','1','-1','-1','','0','".$curtime."',NOW())";
		       

				$this->drc->execute($ins_xundj);
			
           //修改
				 
			 }
			

			
			
			
			foreach($p_arr as $key => $v){
				$master[$adminname_uid[$key]]=$v[($type-1)];
				$thisuid=$adminname_uid[$key];
				$result=$this->drc->query("select id,modelid,modelprice,modelprice_updown,ABS(modelprice_updown) as abs_modelprice_updown,modelpirce_name,date,uid,modeloldprice from ng_price_model_detail where modelprice_type=$type and modelid=".$modeldb["id"]." and uid='".$thisuid."' and date>='$this_xun_start' and  date<='$this_xun_end'  order by modelpirce_name_sort  asc");
		 
				if(empty($result)){
						// $result=$this->drc->query("select  0 as id,'' as modelprice,'' as modelprice_updown,'' as abs_modelprice_updown,modelpirce_name, '' as createtime,".$thisuid." as uid,modeloldprice  from ng_price_model_detail where modelprice_type=$type and modelid=".$modeldb["id"]." and uid='-1' and createtime>='".date('Y-m-d',time())." 00:00:00' and  createtime<='".date('Y-m-d',time())." 23:59:59'   order by modelpirce_name_sort asc");	 
						$result=$this->drc->query("select  0 as id,'' as modelprice,'' as modelprice_updown,'' as abs_modelprice_updown,modelpirce_name, '' as date,'".$thisuid."' as uid,modeloldprice  
						from ng_price_model_detail where modelprice_type=$type and modelid=".$modeldb["id"]." and uid='-1'   order by modelpirce_name_sort asc");
				}
				
				// echo $result; 
				
				if($v[($type-1)]==1){
					$master_leader[]=array(
						'uid'=>$adminname_uid[$key],
						'username'=>$key,
						'power'=>$v[($type-1)],
						'truename'=>$adminname[$key],
						'res'=>$result
						);
					if($adminname_uid[$key]==$uid){
						$uidleadersavebtn=true;
					}
				}else if($v[($type-1)]==2){
					$master_price[]=array(
						'uid'=>$adminname_uid[$key],
						'username'=>$key,
						'power'=>$v[($type-1)],
						'truename'=>$adminname[$key],
						'res'=>$result
						);
					if($adminname_uid[$key]==$uid){
						$uidsavebtn=true;
					}
				}
			}
			//add by xurui权限为3，市场部人员让他与最后一个分管领导操作相同，这里把登录的uid换成最后一个分管领导的
			$record=$this->drc->query("SELECT * FROM  `Ng_RecordManage` where modelid='".$modeldb["id"]."'");
			$last=end($master_leader);
			if(!empty($record)&&$master[$uid]==1&&$uid!=$last['uid']){
				if($params['show']!='1'){
 					gourl($url);
				}
			}
			if($master[$uid]==3){
				$last=	end($master_leader);
				$uid=$last['uid'];
				$sql="select * from app_session_temp where  Uid ='".$uid."'  order by  LoginDate DESC  limit 1";
				$m=$this->_dao->getRow($sql);
				$Mid=$m['Mid'];
				$uidleadersavebtn=true;
			}
			//end by xurui ---
			foreach($master_price as $k){
				$uid_arr[]=$k['uid'];
			}
			foreach($master_leader as $k2){
				$uid_arr2[]=$k2['uid'];
			}
			$privilege=$master[$uid];//当前用户权限
			$this->assign( "params", $params);
			$this->assign( "uid", $uid);
			// echo $uid;
			$this->assign( "mid", $Mid );
			$this->assign( "modeldb", $modeldb );//echo "<pre/>";print_r($modeldb);
			$this->assign( "uidsavebtn", $uidsavebtn );
			$this->assign( "uidleadersavebtn", $uidleadersavebtn );
			$this->assign( "uid_arr", $uid_arr );
			$outlook='0';
				foreach($uid_arr as $key=>$value){
		           $sql="select * from  ng_price_model_detail where uid='$value'
                     and modelid=".$modeldb["id"]." and modelprice_type =$type ";
				  $out=$this->drc->query($sql);
				  // echo "<pre/>";print_r($out);
					  if(empty($out)){
						$outlook='1';
						 break;
					   }
				}
				$this->assign( "outlook", $outlook);//第一层限制 决策未决策 分管领导不能操作 0表示全部决策 1 表示尚有未决策
			
				$shunxu=array_keys($uid_arr2,$uid,true);
		
			    $outlook2='0';
				if($shunxu[0]!=0){
				 for($i=0;$i<$shunxu[0];$i++){				
				   $sql="select * from  ng_price_model_detail where uid='$uid_arr2[$i]'
                  and modelid=".$modeldb["id"]." and modelprice_type =$type ";
				$out=$this->drc->query($sql);
				  // echo "<pre/>";print_r($out);
				if(empty($out)){
					$outlook2='1';
					break;
			    	}
				  }
				}
		 $nextmarket='0';
		if($shunxu[0]!=(count($uid_arr2)-1)){
		     // echo $uid_arr2[$shunxu[0]+1];
			 // echo'<br/>'; print_r ($uid_arr2);
			 $uidpre=$uid_arr2[$shunxu[0]+1];//查后面的人有没有决策
			 
			 $sql="select * from  ng_price_model_detail where uid='$uidpre'
                  and modelid=".$modeldb["id"]." and modelprice_type =$type ";
			$out=$this->drc->query($sql);//如果为空则后一个人未决策 
			if(empty($out)){
				$nextmarket='1';//
			}
		}
		           
				   if($shunxu[0]!=0){
				   $shunxu2=$shunxu[0]-1;//查前面的的人有没有决策
				   }else{
					   $shunxu2=0;
				   }
				   
						     $sql5="select * from  ng_price_model_detail where uid='$uid_arr2[$shunxu2]'
                                     and modelid='".$modeldb["id"]."' and modelprice_type =$type ";
						  
						  $preout=$this->drc->query($sql5);
				   $master_leader2=array();
				 if($outlook2=='1'&&empty($preout))
				 { 
						   for($i=0;$i<$shunxu[0];$i++)
						   {
			
						   $master_leader2[$i]= $master_leader[$i];
						   }
						  
				  
				 }else{
					
							 for($i=0;$i<=$shunxu[0];$i++)
							 {
							 $master_leader2[$i]= $master_leader[$i];
							 } 
				 }
				 
			     // echo "<pre/>";print_r($master_leader2);	
				
				// exit;
				$this->assign( "outlook2", $outlook2);
			  // echo $uid; 
			 // if($uid==end($uid_arr2)){
		          // $sql="select * from  ng_price_model_detail where uid='$uid'
                     // and modelid=".$modeldb["id"]." and modelprice_type =$type ";
				  // $nowuid=$this->drc->query($sql);
  
			 // }
			
					  // echo $uid; 
			
			
			$this->assign( "uid_arr2", $uid_arr2 );
			$this->assign( "master_price", $master_price );//echo "<pre/>";print_r($master_price);
			 $sql3="select Ismakepricy from ng_price_model where modeltype=$type and id=".$modeldb["id"]." ";	
			 $res3=$this->drc->query($sql3);
			 $zzjc='';

           foreach($res3 as $key=>$value){
			 // echo "<pre/>";echo $value['Ismakepricy'];	
			   if($value['Ismakepricy']==1){
				   
				   $zzjc='1';
				   break;
			   }
			   
			   
			   
		   } 
		   
		   
		   if($master[$uid]==4||$master[$uid]==''){
		   
		   	$youke=1;
		   	$this->assign( "youke", $youke );//决策人进来查看 传的值
		   }
		   
		   
    if($zzjc=='1'){//需求变动 可以删了
	  $this->assign( "zzjc", $zzjc );
	  $this->assign( "master_leader", $master_leader );	
	  }else{
								
		if(in_array($uid,$uid_arr2)){
				 $master_leader3=array();
				if($shunxu[0]=='0'){//第一个特殊处理
					
					// if($outlook=='1'){//表示有决策人未决策
						
					// }else{
				// echo $outlook;

                       for($i=0;$i<count($uid_arr2);$i++){
						     $sql="select * from  ng_price_model_detail where uid='$uid_arr2[$i]'
                                     and modelid='".$modeldb["id"]."' and modelprice_type =$type ";
				         $out=$this->drc->query($sql);
						
						
						 
						  if(empty($out)&&$uid_arr2[$i]!=$uid){//修改1
							// echo "<br/>";echo $uid;echo "<pre/>";print_r($uid_arr2);
							  break;
						  }
						   if($outlook!='1'){
						    $master_leader3[$i]= $master_leader[$i];

						   }
						  
					   } 
					   
					   	// echo '5555'; echo $nextmarket;
						
							     $sql="select * from  ng_price_model_detail where uid='$uid'
                                     and modelid='".$modeldb["id"]."' and modelprice_type =$type ";
				           $out=$this->drc->query($sql);
						
						
						
						 if($nextmarket!='1'&&($shunxu[0]!=(count($uid_arr2)-1)))
						    {//1 后面那个未决策 0 后面的哪个人已经决策
								// if(!empty($out)){
							  if($params['show']!=1){
								  // echo '1111';
								$url = "//".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']."&show=1";
								goUrl($url);
								
							// }
						    }
						    }
							 if($outlook=='1'&&empty($out))
								{
								 
								   if($params['show']!=1)
									{
									 $url = "//".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']."&show=1";
									 goUrl($url);
									
									}
								 
								}
							 
						 
					// }
					
					
					
				     $this->assign( "master_leader", $master_leader3 );//echo "<pre/>";print_r($master_leader3);//分管领导
				
				
				}else{//第二个人之后
				
			  
					  	  // echo "<pre/>";print_r($master_leader2);	
						   $sql4="select * from  ng_price_model_detail where uid='$uid'
                                     and modelid='".$modeldb["id"]."' and modelprice_type =$type ";
						  
						  $out=$this->drc->query($sql4);
				
						  
						  
						  // if(!empty($out)){
							  
							  	 // for($i=0;$i<=$shunxu[0];$i++){
						 
				                 // $master_leader2[$i]= $master_leader[$i];
					 
				                   // } 
							  
						  // }
						 // echo '<pre/>';print_r($master_leader2);echo '11111';
						 
						  
						     // $sql4="select * from  ng_price_model_detail where uid='$uid_arr2[$shunxu2]'
                                     // and modelid='".$modeldb["id"]."' and modelprice_type =$type ";
						  
						       // $out=$this->drc->query($sql4);
							   //如果前面的人未决策  自己已经决策了 就可以修改 自己未决策就不能修改
								//outlook2=1 表示前面的人未决策  empty（$out） 是自己没有值 outlook2==1&&empty（$out） 
							if($outlook2=='1'&&empty($out)){
							 if(empty($preout)){
								 
								 
								if($params['show']!=1){
								
									$url = "//".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']."&show=1";
									goUrl($url);
									
								  }
							 }
							 
							 
							 

							}else{//表示前面的人已经决策
								 //判断在自己后面的人有没有决策
								// echo '5555'; 
								
						
						
						
									if($nextmarket!='1'&&($shunxu[0]!=(count($uid_arr2)-1))){//1 后面那个未决策 0 后面的哪个人已经决策
						 
							  if($params['show']!=1){
								  // echo '1111';
								$url = "//".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']."&show=1";
								goUrl($url);
								
							}
							$j=0;
							for($i=0;$i<=count($uid_arr2);$i++){
								$sql="select * from  ng_price_model_detail where uid='$uid_arr2[$i]'
                                    and modelid='".$modeldb["id"]."' and modelprice_type =$type ";
				                $out=$this->drc->query($sql);
								
								
					
						  if(empty($out)&&$uid_arr2[$i]!=$uid){
							  break;
						  }
						  
						  	   $master_leader2[$i]=$master_leader[$i];
							}
							
							
						 }
						 
						 
					 }
					 
					 
					  $this->assign( "master_leader", $master_leader2);
					  	 // echo "<pre/>";print_r($master_leader2);	
				}
			// echo 222;
			}else{
				$master_leader4=array();
				// echo '444';
				//决策人的判断
				$outjc='0';
				for($i=0;$i<count($uid_arr2);$i++){
					
				  $sql="select * from  ng_price_model_detail where uid='$uid_arr2[$i]'
                  and modelid='".$modeldb["id"]."' and modelprice_type =$type ";
				$out=$this->drc->query($sql);   
				
				if(!empty($out)){//分管领导有一个决策了 就跳show=1页面
					
					$outjc='1';
					break;
				}
			
				}
				// echo $outjc;
				if($outjc=='1'){
					 if($params['show']!=1){
							$url = "//".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']."&show=1";
							goUrl($url);
						}
				 }	
                $j=0;
             	for($i=0;$i<=count($uid_arr2);$i++){	
				  $sql="select * from  ng_price_model_detail where uid='$uid_arr2[$i]'
                  and modelid='".$modeldb["id"]."' and modelprice_type =$type ";
				  $out=$this->drc->query($sql);  
                  		
				  if(!empty($out)){
					  
					     $master_leader4[$j]= $master_leader[$i];
						 $j++;
					  }
              	//放在上面会显示领导未提交	第几个不为空就显示第几个
				
				
				
				}
               // echo "<pre/>";print_r($master_leader4); echo 111;
				  
			  $this->assign( "master_leader", $master_leader4);//决策人进来查看 传的值
			
			 }
			
			
	  }
	
	  if($modeldb['is_wangqi']!='1') {
	  	 
	  	 
	  	$up_wq='update ng_price_model set  is_wangqi=1 where id="'.$modeldb['id'].'" and modeltype="'.$type.'"   ';
	  
	  	$this->drc->execute($up_wq);
	  
	  }
	  $this->assign( "istj",0);
	  if($modeldb['is_wangqi']=='1'){
	  	if($modeldb['pricycontent']==''&&$_GET['save_tjghl']!=1){
 		$url = "http://".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']."&save_tjghl=1";
         // $url = "//iwww.steelhome.cn/data/v1.5/web/dcwebview3.php?index&Type=2&GUID=ced31fecdf1511e7b891001aa00de1ab&mode=1&save_tjghl=1";
          
	  		 include '/usr/local/www/libs/phpQuery/phpQuery.php';
	  		phpQuery::newDocumentFile($url);
	  		$content=pq("html")->html();
	  		$up_wq='update ng_price_model set pricycontent ="'.htmlentities($content,ENT_QUOTES,"UTF-8").'" where id="'.$modeldb['id'].'" and modeltype="'.$type.'" and is_wangqi=1  ';
  	  		$this->drc->execute($up_wq);
  	  		$this->assign( "istj",1);
	  	}
	 
	  }

			// $this->assign( "master_leader", $master_leader );
			$this->assign( "city_arr", $GLOBALS['city'] );
			$this->assign( "save_city",array('1'=>'建议') );
			$this->assign( "privilege", $privilege );
			$this->assign( "type", $type );
			$this->assign( "citynum", count($GLOBALS['save_city']) );
			$sql="select * from ng_price_model_detail where modelprice_type=$type and modelid=".$modeldb["id"]." and uid=-1 order by modelpirce_name_sort ,date desc  limit 3"; 
			$res=$this->drc->query($sql);
			for($i=0;$i<count($res);$i++){				
				$res[$i]['abs_modelprice_updown']=$res[$i]['modelprice_updown'];
				if($res[$i]['modelprice_updown']){
					$res[$i]['abs_modelprice_updown']=abs($res[$i]['modelprice_updown']);
				}
			}
			$this->assign( "modeldetail", $res );

		 // $sql="select Ismakepricy from ng_price_model where modeltype=$type and   createtime>='".date('Y-m-d',time())." 00:00:00' and  createtime<='".date('Y-m-d',time())." 23:59:59' order by createtime desc limit 1";	
         // $res=$this->drc->getOne($sql);		
		$style_url= dirname("//".$_SERVER['SERVER_NAME'].$_SERVER['PHP_SELF']);
		 $this->assign( "style_url", $style_url );
		 // echo $style_url;
	
		
		
		 if($zzjc=='1'&&$params['show']!='1'){			
			$url = "//".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']."&show=1";
			gourl($url);
				
			
			
		 }
			}else{
			
				$this->assign("nodata","1");
				$this->assign("nodata1",$nodatatitle);
			}
    } 
	
		public function djjy($start,$end,$ddl,$curdate){
// 		print_r($ddl);
// 		print_r("00000");exit;
		$sql = "SELECT the_price_tax,changerate_tax FROM   `steelprice_info`  where variety='碳结钢'  and gcid='122' and is_show=2 and ftime>= '".$end."' and ftime <= '".date("Y-m-d")."'";
		 
		$res_tjg=$this->gcdao->query($sql);
		$the_price_tax=$res_tjg[0]['the_price_tax'];
		$changerate_tax=$res_tjg[0]['changerate_tax'];
// 		 print_r ($changerate_tax);
// 		 print_r($the_price_tax);
		
		//$date=date("Y-m-d",(strtotime($end) - 3600*24));
		$date1=$start." "."00:00:00";
		$date2=$end." "."00:00:00";
  
		if($the_price_tax==""){
			$sql="select * from marketconditions where mastertopid='0822442' and mconmanagedate > '".$date1."' and mconmanagedate < '".$date2."'";		
			$res_tjg_arr=$this->maindao->query($sql);
			$res_tjg=end($res_tjg_arr);
			//前一旬本期价格
// 			echo $sql;
			//$sql_id="select * from steelprice_base where `steel_id`='122' and `steel_name`='淮钢'ORDER BY `id` DESC";
			$sql_last = "SELECT the_price_tax,changerate_tax FROM   `steelprice_info`  where variety='碳结钢'  and gcid='122' and is_show=2 and ftime< '".$end."' and ftime >= '".$start."'";
			//print_r($sql_last);exit;
			$res_tjg_last=$this->gcdao->query($sql_last);
			$the_price_tax_last=$res_tjg_last[0]['the_price_tax'];
			$the_price_tax=$res_tjg['price']+140-$the_price_tax_last;//5190
// 			print_r($the_price_tax);
			$changerate_tax=$this->jsxundj($the_price_tax);
// 			print_r($changerate_tax);
			
		}
		
		
		$arr=$this->bxundj($changerate_tax,$ddl,$curdate);
		
		
		
		// echo '1111';
		// echo '<pre/>';
		// print_r ($arr);exit;
		// $this->assign("the_price_tax",$the_price_tax);
		// $this->assign("changerate_tax",$changerate_tax);
		return $arr;
	}

	//HTML不需要算系统模型定价  定价与涨跌
	public function bxundj($changerate_tax,$ddl,$curdate){
		if(date("d")<11){
			if($curdate!=''){
				$date=date("Y-m-21",strtotime("m -1 month",$curdate));
			}else{
				$date=date("Y-m-21",strtotime("m -1 month"));
			}
			
			$zd=$changerate_tax;
		}
		if(date("d")<21 && date("d")>10){
			if($curdate!=''){
				$date=date("Y-m-01",strtotime($curdate));
			}else{
				$date=date("Y-m-01");
			}
			
			if($ddl<30){
				$zd=$changerate_tax-20;
			}
			if($ddl<50 && $ddl>=30){
				$zd=$changerate_tax;
			}
			if( $ddl>=50){
				$zd=$changerate_tax+20;
			}
			
		}
		if(date("d")<=31 && date("d")>20){
		if($curdate!=''){
			$date=date("Y-m-11",strtotime($curdate));
		}else{
			$date=date("Y-m-11");
		}
			
			if($ddl<60){
				
			//	print_r(date("d"));
				$zd=$changerate_tax-50;
				//print_r($ddl);
			}
			if($ddl<70 && $ddl>=60){
				$zd=$changerate_tax-20;
			}
			if( $ddl>=70 && $ddl<80 ){
				$zd=$changerate_tax;
			}
			if($ddl>=80){
				$zd=$changerate_tax+20;
			}
		}
		// echo '1111';
	
		//print_r($ddl);
		
		
		$sql="select Value from ng_data_table where DataMark ='Ngyg0004' and dta_ym ='".$date."'";//1201 32130 
		$res=$this->drc->query($sql);
		$ngyg=$res[0]['Value'];
			
			
		
		$moxdj=$zd+$ngyg;
		$this->assign("moxdj",$moxdj);
		$this->assign("zd",$zd);
		
		$dj_arr=array('moxdj'=>$moxdj,'zd'=>$zd);
		return $dj_arr;
	}
	
	
		
		public function jsxundj($the_price_tax){//返回涨跌
			// echo '111';
		// print_r($the_price_tax);exit;
		//$the_price_tax = -177;
		$changerate_tax;
		$changerate;
		if($the_price_tax>=100){
			$changerate=floor(($the_price_tax/10+0.5))*10;
			// echo '111';
		// print_r($the_price_tax);exit;
			
		}else if(50<$the_price_tax && $the_price_tax<100){
			$changerate=50;
		}else if($the_price_tax==50){
			$changerate=30;
		}else if(-50<=$the_price_tax && $the_price_tax<50){
			$changerate=0;
		}else if(-70<=$the_price_tax && $the_price_tax<-50){
			$changerate=-30;
		}else if (-100<=$the_price_tax && $the_price_tax<-70){
			$changerate=-50;
		}else{
			$changerate=floor($the_price_tax*2/3/10+0.5)*10;
			//print_r($changerate);
		}
		//$changerate_tax=$changerate_tax
		$changerate_tax=floor(($changerate/10))*10;
		//print_r($changerate_tax);
		return $changerate_tax;
	}  //计算公式
	public function bcsc_xs($showtime,$end,$showxun,$curdate)
	{
	 	if($curdate!=''){
		$d=date("d",strtotime($curdate));
	    }else{
		$d=date("d");
	    }
	
	if($d >"10"){
		
		$month=$showtime;	
		$startdate=$showtime.'-'."01";
		
		
		//print_r($date_arr);
		//echo "<pre>";
			 if($params['curdate']!=''){	
			  $last_showtime=date("Y-m",strtotime("m -1 month",strtotime($curdate)));
		     $lastdate_s=date("Y-m-01",strtotime("m -1 month",strtotime($curdate)));
			 }else{
		     $last_showtime=date("Y-m",strtotime("m -1 month"));
		     $lastdate_s=date("Y-m-01",strtotime("m -1 month"));
		
			 }
		
    
		
		
		if($showxun=="中"){
			if($curdate!=''){
			$date_arr=$this->workday(date("Y-m-11",strtotime($curdate)));
			$date_last=$this->workday(date("Y-m-11",strtotime("m -1 month",strtotime($curdate))));
			}else{
			$date_arr=$this->workday(date("Y-m-11"));
			$date_last=$this->workday(date("Y-m-11",strtotime("m -1 month")));
			}
			$enddate=$date_arr[1];
			$lastdate_e=$date_last[1];
		}else if ($showxun=="下"){
			if($curdate!=''){
			$date_arr=$this->workday(date("Y-m-21",strtotime($curdate)));
			$date_last=$this->workday(date("Y-m-21",strtotime("m -1 month",strtotime($curdate))));
			
			}else{
			$date_arr=$this->workday(date("Y-m-21"));
			$date_last=$this->workday(date("Y-m-21",strtotime("m -1 month")));
			}
			
			
			
			
			
			$enddate=$date_arr[1];
			$lastdate_e=$date_last[1];
		}/*else{
			//print_r("$showxun");
			$date_last=$this->workday(date("Y-m-t",strtotime("m -2 month")));
			$lastdate_e=$date_last[1];
		}	*/
		//print_r($enddate);
		
		//print_r($lastdate_e);
	}else{
		if($curdate!=''){
		  $month=date("Y-m",strtotime("m -1 month"));
		//print_r($month);exit;
		  $startdate=date("Y-m-01",strtotime("m -1 month",strtotime($curdate)));
		  $enddate=date("Y-m-t",strtotime("m -1 month",strtotime($curdate)));
		  $last_showtime=date("Y-m",strtotime("m -2 month",strtotime($curdate)));
		  $lastdate_s=date("Y-m-01",strtotime("m -2 month",strtotime($curdate)));
		}else{	
			
		 $month=date("Y-m",strtotime("m -1 month"));
		//print_r($month);exit;
		  $startdate=date("Y-m-01",strtotime("m -1 month"));
		  $enddate=date("Y-m-t",strtotime("m -1 month"));
		  $last_showtime=date("Y-m",strtotime("m -2 month"));
	  	$lastdate_s=date("Y-m-01",strtotime("m -2 month"));
			
		}

		$lastdate_e=date("Y-m-d",strtotime($month) -3600*24);
		// $this->assign("last_date",$month);
		// $this->assign("last_tdate",date("Y-m",strtotime("m -2 month")));
		
	}
		 // echo $startdate;echo $enddate;
		 //echo $lastdate_s;
		$sql="SELECT DataMark,dta_ym,Value FROM ng_data_table WHERE 1 and ( (`DataMark` in ('Ngyg0017','Ngyg0018') and dta_vartype =1 and ((`dta_ym`<='".$lastdate_e."' and `dta_ym`>='".$lastdate_s."') or(`dta_ym`<='".$enddate."' and `dta_ym`>='".$startdate."') )) or ( `DataMark` in ('Ngyg0017','Ngyg0018') and dta_vartype =2 and (`dta_ym`='".$last_showtime."' or`dta_ym`='".$month."') )) ";
		$res_arr=$this->drc->query($sql);
		
		// print $sql;
		
		// print $res_arr;exit;
		$temp_arr=array();
		foreach ($res_arr as $val){
			$temp_arr[$val['dta_ym'].$val['DataMark']]=$val['Value'];
		}
		//累计值
		
			$key=$enddate."Ngyg0018";
		
			$xsl=$temp_arr[$key];	//本月//20
		     //33642
			// print_r($xsl);exit;
			 
			$key_last=$lastdate_e."Ngyg0018";//上月同期
			$xsl_last=$temp_arr[$key_last];
		
		
		//exit;
		//print_r($temp_arr);
		
		$jhl=$temp_arr[$month."Ngyg0017"];
		//print_r($jhl);exit;
		$jhl_last=$temp_arr[$last_showtime."Ngyg0017"];
		
		//100
		$this->assign("temp_arr",$temp_arr);
		
		$ddl=round($xsl/$jhl*100,2);
		//print_r($ddl);
		// $last_ddl=round($xsl_last/$jhl_last*100,2);
		// $zf_jhl=round($jhl/$jhl_last*100-100,2);
		// $zf_xsl=round($xsl/$xsl_last*100-100,2);
	     
	
		 
		// print $last_ddl;
		
		// $this->assign("ddl",$ddl);
		// $this->assign("last_ddl",$last_ddl);
		// $this->assign("zf_jhl",$zf_jhl);
		// $this->assign("zf_xsl",$zf_xsl);
		// $this->assign("date",$d);
		
		
		// $this->assign("res_jhl",$jhl);
		// $this->assign("res_xsl",$xsl);
		// $this->assign("last_jhl",$jhl_last);
		// $this->assign("last_xslr",$xsl_last);
		
		return $ddl;
	}
		public function workday($date){
		//echo $date="2017-10-07";
		 $sql="select date,isholiday from holiday  where date <='".$date."' order by date desc limit 10"; 
		//$sql="select * from holiday  where date <='2017-10-7' order by date desc limit 20"; 
		 $res=$this->maindao->aquery($sql);
		 $i = 1;
		 $last_date = $date;
		 $date=array();
		 
		 while( $i < 8 ){
			$last_date=date("Y-m-d",strtotime("-1 day",strtotime($last_date))); 
			if( isset($res[$last_date]) ){
			
				if($res[$last_date]=="0"){
					
					$date[$i]=$last_date;
					$i++;
					//break;
				}else{
					//continue;
				}
			}else{
				
				$n=date("N",strtotime($last_date));
				if($n==7){
					//continue;
				}else{
					
					$date[$i]=$last_date;
					$i++;
					//break;
				}
				
			}
		 }
		 //	print_r($date);
		 return $date;
		
	   }
	
	
	
	
	
	
	public function save_price($params){
		//echo "<pre/>";print_r($params);//exit;
		$uid=$params['uid'];
		
	$uid_arr=$params['arr'];//决策人
	  $uid_arr2=$params['arr2'];//分管领导
	  print_r($uid_arr2);
	
	  $UserType='0';
	  if(in_array($uid,$uid_arr)){
		    $UserType='1';
		  
	  }elseif(in_array($uid,$uid_arr2)){
		   $UserType='2';
		 
	  }
	
		
		
		$mid=$params['Mid'];
		$type=$params['type'];
		$price=$params['p'];
		$city_price=$params['price'];
		$oldprice=$params['oldprice'];
		$updown=$params['updown'];
        $dataname='45#Ф50mm碳素结构钢';
		$modeldbs=$params['modeldb'];
		// $params['privilege']==1 ?  $privilege=1: $privilege=0;
		  $endmarket='0';
		// end($uid_arr2)==$uid?$privilege=1:$privilege=0;
		if(end($uid_arr2)==$uid){
			$privilege=1;
			 
		}else{
			$endmarket='1';
			$privilege=0;
			 
		}
		
		
		foreach($price as $key=>$i){
				$citykey[]=$key;
		}
		// $sql="select * from ng_price_model_detail where modelid=$modeldbs and mid=$mid and uid=$uid and modelprice_type=$type and modelpirce_name in ('南京市场','无锡市场','杭州市场') 
			  // and createtime>'".date("Y-m-d",time())." 00:00:00' and  createtime<'".date("Y-m-d",time())." 23:59:59' ";
			  
			  $sql="select * from ng_price_model_detail where modelid=$modeldbs and mid=$mid and uid=$uid 
			  and modelprice_type=$type and modelpirce_name = '45#Ф50mm碳素结构钢'";
			  
		$res=$this->drc->query($sql);
		
		if(empty($res)){
			$sql="insert into ng_price_model_detail (modelid,uid,Mid,modelprice_type,
			Ismakepricy_men,modelprice,modeloldprice,modelprice_updown,modelpirce_name,date,UserType,createtime)values ";
			for($i=1;$i<=count($price);$i++){
				$sql.="($modeldbs,$uid,$mid,$type,$privilege,'".(($price[$i]/10)*10)."','".$oldprice[$i]."','";
				if($updown[$i]=='-1'){
					$sql.=-$city_price[$i];
				}
				else{
					$sql.=$city_price[$i];
				}
				// $sql.="','".$GLOBALS['save_city'][$citykey[$i-1]]."','".date("Y-m-d H:i:s",time())."'),";
				$curtime=date("Y-m-d H:i:s",time());
					 if($params['curdate']!=''){
					   $curtime=$params['curdate'];
				     }
				
				$sql.="','45#Ф50mm碳素结构钢','".$curtime."','".$UserType."',NOW()),";
				
			}
			$sql=substr($sql,0,-1);
			$this->drc->execute($sql);
		}
		else{
				for($i=1;$i<=count($price);$i++){
					$sql="update ng_price_model_detail set createtime=NOW(),UserType='".$UserType."',Ismakepricy_men='".$privilege."' , modelprice='".$price[$i]."' ,modeloldprice='".$oldprice[$i]."' ,modelprice_updown='";
					
					
					if($updown[$i]=='-1'){
						$sql.=-($city_price[$i]);
					}
					else{
						$sql.=($city_price[$i]);	
					}
					// $sql.="' , createtime='".date("Y-m-d H:i:s",time())."' 
							// where modelid=$modeldbs and	mid=$mid and uid=$uid and modelprice_type=$type and modelpirce_name='".$GLOBALS['save_city'][$citykey[$i-1]]."' and createtime>'".date("Y-m-d",time())." 00:00:00' and  createtime<'".date("Y-m-d",time())." 23:59:59' ";
							$sql.="' where modelid=$modeldbs 
							and mid=$mid and uid=$uid and modelprice_type=$type 
							and modelpirce_name='45#Ф50mm碳素结构钢'";
							
						
							
					$this->drc->execute($sql);							
				}
			}	
			$url = "http://".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']."&save_tjghl=1";
			
		
			include '/usr/local/www/libs/phpQuery/phpQuery.php';
			phpQuery::newDocumentFile($url);
			$content=pq("html")->html();
			$up_wq='update ng_price_model set is_wangqi=1,pricycontent ="'.htmlentities($content,ENT_QUOTES,"UTF-8").'" where id="'.$modeldbs.'" and modeltype="'.$type.'"  ';
			$this->drc->execute($up_wq);
			
			
			
		if($params['saveone']){
			// $url = $_SERVER['HTTP_REFERER']."&show=1";
			$url = $_SERVER['HTTP_REFERER'];
			goUrl($url);
		}
		
		
		if($params['saveAll']){ 
		// $date = date("Y-m-d H:i:s");
		// $xunday1 ='';
		// $nodatatitle = '暂无';
		// if(date("d",strtotime($date))<11){
			// $xunday1 = '01';
			// $nodatatitle .= date("Y年m月")."上旬45#Ф50mm碳素结构钢数据";
		// }elseif(date("d",strtotime($date))<21){
			// $xunday1 = '11';
			// $nodatatitle .= date("Y年m月")."中旬45#Ф50mm碳素结构钢数据";
		// }else{
			// $xunday1 = '21';
			// $nodatatitle .= date("Y年m月")."下旬45#Ф50mm碳素结构钢数据";
		// }
		// $this_xun_start = date("Y-m-$xunday1 02:00:00",strtotime($date));
		// $this_xun_end = date("Y-m-d H:i:s",strtotime($date));
		
		
		   
	        // $sql="select * from app_license_privilege  where  privilege!='' and mc_type='1' ";
			// $res=$this->_dao->query($sql);//res值得排序即是分管领导的排序 
		
			// foreach($res as $key => $value){
					// $r=explode(',',$value['privilege']); 
					// $r=explode(',',$value['privilege']);
					
				  // if( $r[$type-1]==1){
					   // echo $value['uid']."<br/>";
		                // $uids=$value['uid'];
					   // $sql2="select * from  ng_price_model_detail where uid='$uids'
                        // and modelid='".$modeldbs."' and modelprice_type=$type";
				        // $out=$this->drc->query($sql2); 
                       // echo "<pre/>"; print_r($sql2);					
						// if(empty($out)){//如果有分管领导未决策 就将他改成1
							// $endmarket='1';
							// break;
				// }
				  // }	
			// }
		
	       // echo $endmarket;exit;
		if($endmarket!='1'){//全部决策完成 生成最终决策
			$url = $_SERVER['HTTP_REFERER'];
		
			$content=pq("html")->html();
			$sql='update ng_price_model set pricycontent ="'.htmlentities($content,ENT_QUOTES,"UTF-8").'",ismakepricy=1 ,makepricytime="'.date("Y-m-d H:i:s",time()).'"
					where id="'.$modeldbs.'" and modeltype="'.$type.'"  order by id desc limit 1 ';
			$this->drc->execute($sql);
			goUrl($url);
		}else{
			 $url = $_SERVER['HTTP_REFERER'];
			 goUrl($url);
			
			
			
	}}
	}

} 

?>