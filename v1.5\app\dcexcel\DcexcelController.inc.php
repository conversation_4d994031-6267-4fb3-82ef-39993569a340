<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );

class DcexcelController extends AbstractController{

    public function __construct(){
        parent::__construct();

        $this->_action->setDao( new DcexcelDao( "MAIN" ) );
        //$this->_action->bizdao = new SystemDao('BIZ') ;
        //$this->_action->stedao = new DcsystemDao('HQ') ;
        //$this->_action->stedao = new DcsearchDao('DRC') ;
        $this->_action->ngdrc=new DcexcelDao( "DRC" );
        $this->_action->stedao = new DcexcelDao('91R') ;

    }

    public function do_excellogin(){

        $this->_action->excellogin($this->_request);
    }

    public function do_updatepricedata(){

        $this->_action->updatepricedata($this->_request);
    }

    public function do_updateshipidata(){

        $this->_action->updateshipidata($this->_request);
    }

    public function do_updatekucundata(){

        $this->_action->updatekucundata($this->_request);
    }

    public function do_dolog(){

        $this->_action->dolog($this->_request);
    }
}


?>