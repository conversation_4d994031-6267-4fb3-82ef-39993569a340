<?php
include_once( FRAME_LIB_DIR . "/dao/AbstractDao.inc.php" );
include_once( FRAME_LIB_DIR . "/dao/Dao.inc.php" );
class MeetingDao extends Dao{
  public function __construct( $writer ){
    parent::__construct( $writer );
  }
  //取得GUID
  public function getGUID( $delminus = true ){
      $sGuid = $this->getOne( "SELECT uuid() AS guid" );
      if ($delminus){
         $sGuid =  str_replace("-", "", $sGuid);
      }
      return  $sGuid;
  }

 public function getip(){
  	if (!empty($_SERVER['HTTP_CLIENT_IP']))
  $ip = $_SERVER['HTTP_CLIENT_IP'];
else if (!empty($_SERVER['HTTP_X_FORWARDED_FOR']))
  $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
else
  $ip = $_SERVER['REMOTE_ADDR'];

  return $ip;
  	}

    /**
     * @param $uid
     * user:shizg
     * time:2022/11/4 10:43
     * TODO 根据id获取中奖人信息
     * @return array
     */
    public function getAwardUserInfoByUid($uid,$mtid)
    {
        $sql = "select * from meeting_wx_award where id =" . $uid." and mtid=$mtid";
        return $this->getRow($sql);
    }

    /**
     * @param $uid
     * @param $prize_name
     * @param $awardType
     * @param $status
     * user:shizg
     * time:2022/11/4 10:49
     * TODO 执行获奖人获奖的奖项
     */
    public function updateAwardUserInfo($uid, $prize_name, $awardType, $status)
    {
        $nowTime = date("Y-m-d H:i:s");
        //$prize_name = iconv("UTF-8","GBK",$prize_name);
        $sql = " update meeting_wx_award set award_type= '".$awardType."', prize_name= '".$prize_name."', status = '".$status."', draw_time='".$nowTime."' where id =" . $uid ;
//        echo $sql;
        $this->execute( $sql );
    }

    /**
     * @param $meetid
     * @param $awardtype
     * user:shizg
     * time:2022/11/7 10:25
     * TODO 执行更新本奖项已有中奖人员清空
     */
    public function updateAwardTypeForMeeting( $meetid, $awardtype )
    {
        $sql = " update meeting_wx_award set award_type= 0  where  mtid = '".$meetid."' and  award_type= '".$awardtype."'" ;
//        echo $sql;
        $this->execute( $sql );
    }

    /**
     * @param $mobile
     * @param $content
     * @param $true_name
     * @param $meetId
     * user:shizg
     * time:2022/11/4 11:01
     * TODO 保存短信日志
     */
    public function saveSmsSendLog( $mobile, $content, $true_name, $meetId )
    {
        $logssql = "Insert into meeting_member_smsdetail(Smsid,Mid,Mcid,TrueName,Mobile,SmsContent,UserName,Mtid,CreateDate,CreateUserId,UpdateDate,UpdateUserId,Status) values('','','','','" . $mobile . "','" . $content . "','" . $true_name . "','" . $meetId . "', NOW(), '', NOW(), '', 1)";
        $this->execute($logssql);
    }

    public function SendMobileSMS($Mtid, $UserID, $Mid, $Smsid, $SMS, $Mobile)
    {
        $sql = "INSERT INTO `EMAP_SM_MT_SEND` (`MSGCONTENT`, `DESTTERMID`, `BEGINDATE`, `ENDDATE`, `REQUESTTIME`, `RESERVED1`, `RESERVED2`, `RESERVED3`, `RESERVED4`) VALUES ('$SMS', '$Mobile',current_date, current_date ,NOW(),'$UserID','$Mid','$Smsid','$Mtid' ) ";
        $this->execute($sql);
    }
    public function getAwardUserInfoByUid1109($uid)
    {
        $sql = "select * from meeting_choujiang where id =" . $uid;
        return $this->getRow($sql);
    }
    public function updateAwardTypeForMeeting1109( $meetid, $awardtype )
    {
        $sql = " update meeting_choujiang set award_type= 0  where  mtid = '".$meetid."' and  award_type= '".$awardtype."'" ;
//        echo $sql;
        $this->execute( $sql );
    }
    public function updateAwardUserInfo1109($uid, $prize_name, $awardType, $status)
    {
        $nowTime = date("Y-m-d H:i:s");
        //$prize_name = iconv("UTF-8","GBK",$prize_name);
        $sql = " update meeting_choujiang set award_type= '".$awardType."', prize_name= '".$prize_name."', status = '".$status."', draw_time='".$nowTime."' where id =" . $uid ;
//        echo $sql;
        $this->execute( $sql );
    }

    public function getQuestionBySurveyId($surveyId){
        $sql="SELECT mq.id as questionId,mq.questionName,mq.number,mq.questionType,mq.questionRule,mq.maxnum,mqo.id as optionId, mqo.optionName,mqo.optionSort FROM  meeting_question as mq LEFT JOIN meeting_question_option as mqo ON mq.id=mqo.questionId where  mq.status=1 and mq.isdel=0 and mq.surveyId='$surveyId' order by mq.number,mqo.optionSort asc ";
        return $this->query($sql);
    }
    public function getQuestionOptionPersonCount($where){
		$sql="select count(distinct hwid) from `meeting_survey_person` where  1 and  hwid!='' $where";
		return $this->getOne($sql);
	}

	public function getQuestionOptionPerson($where){
		$sql="select id,content from `meeting_survey_person` where  1 and  hwid!='' and content!='1' and content!='无' and content!='暂无' and content!='没有' and content!='没'  and content!='测试'  $where group by content order by createtime asc limit 10";
		return $this->query($sql);
	}

    public function getQuestionOptionPersonTotal($id){
		$sql="select count(distinct hwid) from `meeting_survey_person` where  hwid!='' and surveyId='".$id."' ";
		return $this->getOne($sql);
	}

    public function getSurveyById($mtid,$type,$where){
		$sql="select id from `meeting_survey` where  1 and isdel=0 and  type='$type'  and mtid='$mtid' $where order by createtime desc limit 1";
        return $this->getOne($sql);
	}

    public function getSurvey($id)
    {
        $sql = "select * from `meeting_survey` where id = '$id' and isdel=0";
        return $this->getRow($sql);
    }


    public function getAllSurveyByMtid($mtid,$where){
		$sql="select type,id from `meeting_survey` where  1 and isdel=0 and mtid='$mtid' $where group by type order by type asc ";
        return $this->AQUERY($sql);
	}
    public function getQuestionnum($surveyId){
        $sql="SELECT count(*) FROM  meeting_question where  status=1 and isdel=0 and surveyId='$surveyId' ";
        return $this->getOne($sql);
    }
    public function write_meeting_active_log2($mtid,$hwid, $content,$title,$type,$user)
    {
        $sql = "INSERT INTO `meeting_active_log` (`mtid`,`hwid`, `content`,`title`,`type`, `createtime`, `createuser`, `createuserid`,updatetime) VALUES( '" . $mtid . "','" . $hwid . "','" . $content . "','" . $title . "','" . $type . "', NOW(), '" . $user['TrueName'] . "', '" . $user['Uid'] . "', NOW())";
        $this->execute($sql);
    }

}

?>