<?php
class DcexcelDao extends Dao{

    public function __construct( $writer ){
        parent::__construct( $writer );
    }

    public function checkuser( $username, $password ){
        return $this->getRow( "SELECT id,truename,mid FROM adminuser WHERE username='$username' AND passwordmd5='$password' and mid=1 and state=1 " );
    }

    public function WriteLog($Mid, $Uid, $SignCS, $ActionName, $ActionIp, $MessageTitle,$MessageDesc='' ){

        $insertsql="INSERT INTO app_logs SET MID='$Mid', Uid='$Uid', SignCS='$SignCS', ActionName='$ActionName', ActionDate='".date('Y-m-d H:i:s',time())."',ActionIp='$ActionIp', MessageTitle='$MessageTitle', Actionstr='$MessageDesc',mc_type=100";
        //echo $insertsql;exit;
        return $this->execute($insertsql);
    }
}

?>