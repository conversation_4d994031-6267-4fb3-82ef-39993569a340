<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );

class dgxundjController extends AbstractController{
  
  public function __construct(){
    parent::__construct();
	
	$this->_action->setDao( new dgxundjDao( "MAIN" ) );
	//$this->_action->steelhome= new dgxundjDao('HQT') ;
	//$this->_action->drc= new dgxundjDao('GC') ;//cha
	$this->_action->drcW= new dgxundjDao('DRCW','DRC') ;//xie
	$this->_action->maindao = new dgxundjDao('91R'.$houzhui) ;
	$this->_action->maindao2 = new dgxundjDao('91R') ;
	$this->_action->gcdao=new dgxundjDao('GC');
	
	
  }

  public function _dopre(){
	  //echo "<pre/>";print_r($_SESSION);exit;
      //$this->_action->checkSession();
  }
	

  public function v_index() {
	$this->_action->index($this->_request);
  }
  public function do_save_price() {
	$this->_action->save_price($this->_request);
  }

	
  public function do_ThisDateDb() {
	$this->_action->DoThisDateDb($this->_request);
  }

  public function do_ThisDateDbList() {
	$this->_action->DoThisDateDbList($this->_request);
  }
}


?>