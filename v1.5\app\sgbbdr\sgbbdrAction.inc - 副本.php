<?php
class sgbbdrAction extends AbstractAction
{
	public function __construct()
	{
    	parent::__construct();
	}

	public function checkSession()
	{
	}

	function drPqxxTiBao($params){
		$pianqu = $this->_dao->aquery("select pqid,pianqu from sg_PianQu");
		foreach($pianqu as $pqid=>$pq)
		{
			$num1 = substr(mt_rand(3000, 5200),0,3)*10;
			$num2 = substr(mt_rand(3000, 5200),0,3)*10;
			$num3 = substr(mt_rand(3000, 5200),0,3)*10;

			$num1_1 = substr(mt_rand(7000, 10000),0,3)*10;
			$num2_1 = substr(mt_rand(7000, 10000),0,3)*10;
			$num3_1 = substr(mt_rand(7000, 10000),0,3)*10;
			$sql = "insert into sg_PqxxTiBao(pqid,pinzhong,kdl,jzgccjjg,scqk,tjjy,adminId,createtime) values('$pqid','1','$num1_1','$num1','','','391935',NOW()),('$pqid','2','$num2_1','$num2','','','391935',NOW()),('$pqid','3','$num3_1','$num3','','','391935',NOW())";
			$this->_dao->execute($sql);
		}
	}

	function drZhidaojia_dr($params){
		$date = $params['date'];  //$date不存在，这里先手动随便指定一个日期
		$sql = "select * from sg_zhidaojia_dr where ndate like '%$date%'";
		$zdj = $this->_dao->query($sql);
		if(!empty($zdj))
		{
			exit;
		}

		$sql = "insert into sg_zhidaojia_dr(pinzhong,cityname,price,ndate,adminId,createtime) values('1','西安','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('1','成都','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('1','重庆','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('1','郑州','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('1','兰州','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('2','西安','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('2','成都','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('2','重庆','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('2','郑州','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('2','兰州','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('3','西安','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('3','成都','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('3','重庆','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('3','郑州','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('3','兰州','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW())";
		// echo $sql."<br><br>";
		$this->_dao->execute($sql);
	}

	public function random($length)
    {
        $hash = 'CR-';
        $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789abcdefghijklmnopqrstuvwxyz';
        $max = strlen($chars) - 1;
        mt_srand(( double ) microtime() * 1000000);
        for ($i = 0; $i < $length; $i ++) {
            $hash .= $chars [mt_rand(0, $max)];
        }
        return $hash;
	}
	
	function hcgcyxsupload($params)
	{
		$year = date('Y');
		$years = array();
		for($i=0;$i<=30;$i++)
		{
			$years[] = $year-$i;
		}
		$this->assign('years', $years);
		$GUID = $params['GUID'];
		$mode = $params['mode'];//1=客户端，2=安卓，3=iOS
		if($GUID==""){
			alert('GUID不能为空');
            exit;
		}
		$user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
		if(empty($user_infos)){
			alert('用户不存在');
            exit;
		}
		
		$this->assign('guid', $GUID);
		$this->assign('mode', $mode);
	
	}
	function hcgczxsupload($params)
	{
		$year = date('Y');
		$years = array();
		for($i=0;$i<=30;$i++)
		{
			$years[] = $year-$i;
		}
		$this->assign('years', $years);
		$GUID = $params['GUID'];
		$mode = $params['mode'];//1=客户端，2=安卓，3=iOS
		if($GUID==""){
			alert('GUID不能为空');
            exit;
		}
		$user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
		if(empty($user_infos)){
			alert('用户不存在');
            exit;
		}
		
		$this->assign('guid', $GUID);
		$this->assign('mode', $mode);
	}



	function yjyybupload($params)
	{
		$year = date('Y');
		$years = array();
		for($i=0;$i<=30;$i++)
		{
			$years[] = $year-$i;
		}
		$this->assign('years', $years);
		$GUID = $params['GUID'];
		$mode = $params['mode'];//1=客户端，2=安卓，3=iOS
		if($GUID==""){
			alert('GUID不能为空');
            exit;
		}
		$user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
		if(empty($user_infos)){
			alert('用户不存在');
            exit;
		}
		
		$this->assign('guid', $GUID);
		$this->assign('mode', $mode);
	}


	function readSheet($file, $year)
	{
		require_once "../PHPExcel/PHPExcel.php";
		$type = pathinfo($file); 
		$type = strtolower($type["extension"]);
		if ($type=='xlsx') { 
			$type='Excel2007'; 
		}elseif($type=='xls') { 
			$type = 'Excel5';
		}
		
		$objReader = PHPExcel_IOFactory::createReader($type);//判断使用哪种格式
		$objReader ->setReadDataOnly(true); //只读取数据,会智能忽略所有空白行,这点很重要！！！
		$objPHPExcel = $objReader->load($file); //加载Excel文件
		// $sheetCount = $objPHPExcel->getSheetCount();//获取sheet工作表总个数
		$sheets = $objPHPExcel->getSheetNames();
		return $sheets;
	}

	function sheet($params)
	{
		$GUID = $params['GUID'];
		$mode = $params['mode'];//1=客户端，2=安卓，3=iOS
		$this->assign('mode', $mode);
		$response = array(
			'Success'  => 1,
			'Message'  => '',
			'GUID'     => $GUID,
			'mode'     => $mode,
		);
		if($GUID==""){
			$response['Success'] = 0;
			$response['Message'] = 'GUID不能为空';
		}
		if($response['Success']){
			
			$year = $params['year'];
			$upfile = $_FILES['file'];
			if(strpos($this->array_iconv($upfile["name"]), '韩城公司钢材月销售')===false)
			{
				$response['Success'] = 0;
				$response['Message'] = '文件名非韩城公司钢材月销售！';
			}
			$user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
			if(empty($user_infos)){
				// $response['Success'] = 0;
				$response['Message'] = '用户不存在';
			}
			if($response['Success']){
				if ($upfile['error']==0)
				{
					if (is_uploaded_file($upfile['tmp_name'])) {
						$uptypes=array(
							'application/vnd.ms-excel',
							'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
						);
						if (in_array($upfile['type'], $uptypes)) {
							$extName = strtolower(end(explode('.', $upfile ['name'])));
							$filename = $this->random(5); // 设置随机数长度
							$extName=strtolower(end(explode('.', $upfile['name']))); // 取得扩展名
							$name1=date('YmdHis').".".$extName;
							$dir = UPLOADFILE;
							if (!file_exists($dir)) {
								if (mkdir($dir, 0777)) {
									$dest =$dir."/".$filename.$name1;
									
									if (move_uploaded_file($upfile ['tmp_name'], $dest)) {
										$response['Success'] = 1;
										$response['Message'] = '上传成功';
										$sheet11 = $this->readSheet($dest);
										$response['Result'] = $this->array_iconv($sheet11);
										$response['File'] = $filename.$name1;
										$response['oldtmp'] = $this->array_iconv($upfile["name"]);
										$response['Year'] = $year;
									} else {
										$response['Success'] = 0;
										$response['Message'] = '上传失败！目录权限不足';
									}
								} else {
									$response['Success'] = 0;
									$response['Message'] = '目录不存在，上传失败！';
								}
							} else {
								$dest = $dir."/".$filename.$name1;
								if (move_uploaded_file($upfile ['tmp_name'], $dest)) {
									$response['Success'] = 1;
									$response['Message'] = '上传成功';
									$sheet11 = $this->readSheet($dest);
									$response['Result'] = $this->array_iconv($sheet11);
									$response['File'] = $filename.$name1;
									$response['oldtmp'] = $this->array_iconv($upfile["name"]);
									$response['Year'] = $year;
								} else {
									$response['Success'] = 0;
									$response['Message'] = '上传失败！目录权限不足';
								}
							}
						} else {
							$response['Success'] = 0;
							$response['Message'] = '上传失败！检查文件是否是Excel文件';
						}
					} else {
						clearstatcache(); //清除文件缓存信息
						$response['Success'] = 0;
						$response['Message'] = '上传失败！';
					}
				}
				else
				{
					alert('上传失败！');
					goURL("sgbbdr.php?view=hcgcyxsupload&GUID=".$GUID."&mode=".$mode);
					exit;
				}
			}
		}
		echo $this->pri_JSON($response);
		exit;
	}

	function dosheet($params)
	{
		$GUID = $params['GUID'];
		$mode = $params['mode'];//1=客户端，2=安卓，3=iOS
		$filename = iconv("utf-8","gb2312//IGNORE",$params['oldtmp']);

		$response = array(
			'Success'  => 1,
			'Message'  => '',
			// 'GUID'     => $GUID,
			// 'mode'     => $mode,
		);
		if($GUID==""){
			$response["Success"] = 0;
			$response['Message'] = 'GUID不能为空';
		}

		if($response["Success"])
		{   
			$user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
			if(empty($user_infos)){
				$response["Success"] = 0;
				$response['Message'] = '用户不存在';
			}

			$sheetList = explode(',', $params["sheet"]);
			$file = UPLOADFILE."/".$params["tmp"];
			
			if((empty($sheetList[0])&&$sheetList[0]!=0)||$params["sheet"]=='')
			{ 

				$response["Success"] = 0;
				$response['Message'] = '未选择工作表';
			}

			$ymdate = mysql_real_escape_string($params["year"]);
			if(empty($ymdate))
			{
				$response["Success"] = 0;
				$response['Message'] = '时间不能为空';
			}
			if($response["Success"])
			{
				require_once "../PHPExcel/PHPExcel.php";
				$type = pathinfo($file); 
				$type = strtolower($type["extension"]);
				if ($type=='xlsx') { 
					$type='Excel2007'; 
				}elseif($type=='xls') { 
					$type = 'Excel5';
				}
				$objReader = PHPExcel_IOFactory::createReader($type);//判断使用哪种格式
				$objReader ->setReadDataOnly(true); //只读取数据,会智能忽略所有空白行,这点很重要！！！
				$objPHPExcel = $objReader->load($file); //加载Excel文件
				$sheets = $objPHPExcel->getSheetNames();
				$sheetCount = $objPHPExcel->getSheetCount();//获取sheet工作表总个数

				$flag = true;
				foreach($sheetList as $sheet_index1)
				{
					$sheet_index = $sheetCount-1-$sheet_index1;
					// $sheet = $objPHPExcel->setActiveSheetIndexByName($sheet_v);
					$sheet = $objPHPExcel->getSheet($sheet_index);
					// $sheet_array = $objPHPExcel->getSheet($sheet_index)->toArray();
					// print_r($sheet_array);
					// exit;
					//得到当前工作表对象
					// $curSheet = $objPHPExcel->getActiveSheet();
					//获取当前工作表最大行数
				
				
					$rows = $sheet->getHighestRow();
					//获取当前工作表最大列数,返回的是最大的列名，如：B 
					// $cols = $sheet->getHighestColumn();
					// print_r($cols);
					//将当前工作表名当键，内容为值存入数组

					$sheet_v = iconv("utf-8","gb2312//IGNORE",$sheets[$sheet_index]);
					$sheetname .= $sheet_v.",";
					$data = array();
					$listA = array();
					$maxrow='0';
					for($k = 3; $k <= $rows; $k++){
						$key = "A".$k;
						$listA[$k] = iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getValue());
						// $listA[$k] = $sheet->getCell($key)->getValue();
						
						if(empty($listA[$k])){
							$listA[$k] = $listA[$k-1];
						}
						if($maxrow!=0){//建材合计为最后一行，后面的不要了
							unset($listA[$k]);
						}
						if($listA[$k]=='建材合计'){ 
							$maxrow=$k;
						}

					}
					
					
					// print_r($listA);
					$t = array_keys($listA);

					
					//大写字母A的ASCII值是65 A-Z对应65-90
					for($j = 'B'; $j <= 'O'; $j++ ){
						for($k = 3; $k <= $maxrow; $k++){
							$key = $j.$k;
							  $info = iconv("utf-8","gb2312//IGNORE", $sheet->getCell("B".$k)->getValue());
							    // $info = $sheet->getCell("B".$k)->getValue();
							
								$data[$k]['A']=$listA[$k];
								if($listA[$k]=='小计'){
									$data[$k]['A']=$data[$k-1]['A'];
									
								}
								if($listA[$k]=='小计'&&$j=='B'){
									$data[$k]['B']=$listA[$k];
								}else{
								  $data[$k][$j] = iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue());
                                    // $data[$k][$j] = $sheet->getCell($key)->getCalculatedValue();
								}
								
								$data[$k]['Z'] = "SGHCGCYXS";
						}
					}

					
					
				    $A2 = iconv("utf-8","gb2312//IGNORE", $sheet->getCell('A2')->getValue());

					$regex_year="'\d{4}年'is";
					preg_match($regex_year,$A2,$yeararr);//print_r($year);
					$year = explode('年', $yeararr[0]);
					$regex_month="'\d{1,2}月'is";
					preg_match($regex_month,$A2,$montharr);//print_r($year);
					$month = explode('月', $montharr[0]);
					$date =$year[0].'-'.$month[0];


					$this->_dao->execute("delete from sg_data_table where dta_ym='$date' and dta_type ='SGHCGCYXS'");
					$basesql = "insert into sg_data_table(dta_1,dta_2,dta_3,dta_4,dta_5,dta_6,dta_7,dta_8,dta_9,dta_10,dta_11,dta_12,dta_13,dta_14,dta_15,dta_type,dta_ym,createtime,createuser) values";
					$valSql = "";
				
					foreach($data as $rows=>$columns)
					{
						
						$dta_1 = $columns['A'];
						$dta_2 = $columns['B'];
						if($rows==3){
							$dta_3 = $columns['C'];
							$dta_4 = $columns['D'];
							$dta_5 = $columns['E'];
						
							$dta_6 =  $columns['F'];
							$dta_7 =  $columns['G'];
							$dta_8 =  $columns['H'];
							$dta_9 =  $columns['I'];
							$dta_10 =  $columns['J'];
							$dta_11 =  $columns['K'];
							$dta_12 =  $columns['L'];
							$dta_13 =  $columns['M'];
							$dta_14 =  $columns['N'];
							$dta_15 =  $columns['O'];
						}else{
							$dta_3 = str_replace("#DIV/0!","",sprintf("%.1f",round($columns['C'],1)));
							$dta_4 = str_replace("#DIV/0!","",$columns['D']);
							$dta_5 = str_replace("#DIV/0!","",sprintf("%.1f",round($columns['E'],1)));
						
							$dta_6 = str_replace("#DIV/0!","",sprintf("%.1f",round($columns['F'],2)));
							$dta_7 = str_replace("#DIV/0!","",sprintf("%.0f",round($columns['G'],0)));
							$dta_8 = str_replace("#DIV/0!","",sprintf("%.1f",round($columns['H'],1)));
							$dta_9 = str_replace("#DIV/0!","",sprintf("%.0f",round($columns['I'],0)));
							$dta_10 = str_replace("#DIV/0!","",sprintf("%.1f",round($columns['J'],1)));
							$dta_11 = str_replace("#DIV/0!","",sprintf("%.0f",round($columns['K'],0)));
							$dta_12 = str_replace("#DIV/0!","",sprintf("%.2f",round($columns['L'],2)));
							$dta_13 = $columns['M'];
							$dta_14 = str_replace("#DIV/0!","",sprintf("%.2f",round($columns['N'],2)));
							$dta_15 = str_replace("#DIV/0!","",sprintf("%.2f",round($columns['O'],2)));
						}

						$dta_type=$columns['Z'];
						$dta_ym = $date;
						
						$valSql.="('$dta_1','$dta_2','$dta_3','$dta_4','$dta_5','$dta_6','$dta_7','$dta_8','$dta_9','$dta_10','$dta_11','$dta_12','$dta_13','$dta_14','$dta_15','$dta_type','$dta_ym',NOW(),'".$user_infos['Uid']."'),";
						
					}
					
					$insertsql = substr($basesql.$valSql, 0, -1);
			       
					if(!empty($valSql))
					{
						$this->_dao->execute($insertsql);
						// echo $insertsql."<br><br>";
					}else{
						// 失败
						$error_sheet2[] = $sheet_v;
						$flag = false;
					}
				}
				if(!$flag)
				{
				
					$error2 = implode(',', $error_sheet2);
					
					if(!empty($error2))
					{
						$msg2 = "工作表：".iconv("utf-8","gb2312//IGNORE",$error2)."出现未知错误，导入失败";
					}
					$response["Success"] = 0;
					$response['Message'] = $msg2;
				}else{
					$response["Success"] = 1;
					$response['Message'] = '导入成功';
					$sheetname = substr($sheetname, 0, -1);
					$sql ="insert into sg_data_table_log(uptype,filepath,filename,sheetname,createtime,createuser,uptypedate) values('11','$file','$filename','$sheetname',now(),'".$user_infos['Uid']."','".$ymdate."')";
					$this->_dao->execute($sql);
				}
				unlink($file);
			}
		}
		echo $this->pri_JSON($response);
		exit;
	}


	function sheetzxs($params)
	{
		
		$GUID = $params['GUID'];
		$mode = $params['mode'];//1=客户端，2=安卓，3=iOS
		$this->assign('mode', $mode);
		$response = array(
			'Success'  => 1,
			'Message'  => '',
			'GUID'     => $GUID,
			'mode'     => $mode,
		);
		if($GUID==""){
			$response['Success'] = 0;
			$response['Message'] = 'GUID不能为空';
		}
		if($response['Success']){
			$year = $params['year'];
			$upfile = $_FILES['file'];
			if(strpos($this->array_iconv($upfile["name"]), '韩城公司钢材周销售')===false)
			{
				$response['Success'] = 0;
				$response['Message'] = '文件名非韩城公司钢材周销售！';
			}
			$user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
			if(empty($user_infos)){
				// $response['Success'] = 0;
				$response['Message'] = '用户不存在';
			}
			if($response['Success']){
				if ($upfile['error']==0)
				{
					if (is_uploaded_file($upfile['tmp_name'])) {
						$uptypes=array(
							'application/vnd.ms-excel',
							'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
						);
						if (in_array($upfile['type'], $uptypes)) {
							$extName = strtolower(end(explode('.', $upfile ['name'])));
							$filename = $this->random(5); // 设置随机数长度
							$extName=strtolower(end(explode('.', $upfile['name']))); // 取得扩展名
							$name1=date('YmdHis').".".$extName;
							$dir = UPLOADFILE;
							if (!file_exists($dir)) {
								if (mkdir($dir, 0777)) {
									$dest =$dir."/".$filename.$name1;
									
									if (move_uploaded_file($upfile ['tmp_name'], $dest)) {
										$response['Success'] = 1;
										$response['Message'] = '上传成功';
										$sheet11 = $this->readSheet($dest);
										$response['Result'] = $this->array_iconv($sheet11);
										$response['oldtmp'] = $this->array_iconv($upfile["name"]);
										$response['File'] = $filename.$name1;
										$response['Year'] = $year;
									} else {
										$response['Success'] = 0;
										$response['Message'] = '上传失败！目录权限不足';
									}
								} else {
									$response['Success'] = 0;
									$response['Message'] = '目录不存在，上传失败！';
								}
							} else {
								$dest = $dir."/".$filename.$name1;
								if (move_uploaded_file($upfile ['tmp_name'], $dest)) {
									$response['Success'] = 1;
									$response['Message'] = '上传成功';
									$sheet11 = $this->readSheet($dest);
									$response['Result'] = $this->array_iconv($sheet11);
									$response['oldtmp'] = $this->array_iconv($upfile["name"]);
									$response['File'] = $filename.$name1;
									$response['Year'] = $year;
								} else {
									$response['Success'] = 0;
									$response['Message'] = '上传失败！目录权限不足';
								}
							}
						} else {
							$response['Success'] = 0;
							$response['Message'] = '上传失败！检查文件是否是Excel文件';
						}
					} else {
						clearstatcache(); //清除文件缓存信息
						$response['Success'] = 0;
						$response['Message'] = '上传失败！';
					}
				}
				else
				{
					alert('上传失败！');
					goURL("sgbbdr.php?view=hcgczxsupload&GUID=".$GUID."&mode=".$mode);
					exit;
				}
			}
		}
		echo $this->pri_JSON($response);
		exit;
	}


	function dosheetzxs($params)
	{
		$GUID = $params['GUID'];
		$mode = $params['mode'];//1=客户端，2=安卓，3=iOS
		$filename = iconv("utf-8","gb2312//IGNORE",$params['oldtmp']);
		$response = array(
			'Success'  => 1,
			'Message'  => '',
			// 'GUID'     => $GUID,
			// 'mode'     => $mode,
		);
		if($GUID==""){
			$response["Success"] = 0;
			$response['Message'] = 'GUID不能为空';
		}

		if($response["Success"])
		{   
			$user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
			if(empty($user_infos)){
				$response["Success"] = 0;
				$response['Message'] = '用户不存在';
			}

			$sheetList = explode(',', $params["sheet"]);
			$file = UPLOADFILE."/".$params["tmp"];
			
			if((empty($sheetList[0])&&$sheetList[0]!=0)||$params["sheet"]=='')
			{ 

				$response["Success"] = 0;
				$response['Message'] = '未选择工作表';
			}

			$ymddate = mysql_real_escape_string($params["year"]);
			if(empty($ymddate))
			{
				$response["Success"] = 0;
				$response['Message'] = '时间不能为空';
			}
			if($response["Success"])
			{
				require_once "../PHPExcel/PHPExcel.php";
				$type = pathinfo($file); 
				$type = strtolower($type["extension"]);
				if ($type=='xlsx') { 
					$type='Excel2007'; 
				}elseif($type=='xls') { 
					$type = 'Excel5';
				}
				$objReader = PHPExcel_IOFactory::createReader($type);//判断使用哪种格式
				$objReader ->setReadDataOnly(true); //只读取数据,会智能忽略所有空白行,这点很重要！！！
				$objPHPExcel = $objReader->load($file); //加载Excel文件
				$sheets = $objPHPExcel->getSheetNames();
				$sheetCount = $objPHPExcel->getSheetCount();//获取sheet工作表总个数

				$flag = true;
				foreach($sheetList as $sheet_index1)
				{
					$sheet_index = $sheetCount-1-$sheet_index1;
					// $sheet = $objPHPExcel->setActiveSheetIndexByName($sheet_v);
					$sheet = $objPHPExcel->getSheet($sheet_index);
					// $sheet_array = $objPHPExcel->getSheet($sheet_index)->toArray();
					// print_r($sheet_array);
					// exit;
					//得到当前工作表对象
					// $curSheet = $objPHPExcel->getActiveSheet();
					//获取当前工作表最大行数
				
				
					$rows = $sheet->getHighestRow();
					//获取当前工作表最大列数,返回的是最大的列名，如：B 
					// $cols = $sheet->getHighestColumn();
					// print_r($cols);
					//将当前工作表名当键，内容为值存入数组

					$sheet_v = iconv("utf-8","gb2312//IGNORE",$sheets[$sheet_index]);
					$sheetname .= $sheet_v.",";
					
					$data = array();
					$listA = array();
					$maxrow='0';
					for($k = 3; $k <= $rows; $k++){
						$key = "A".$k;
						$listA[$k] = iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getValue());
						// $listA[$k] = $sheet->getCell($key)->getValue();
						
						if(empty($listA[$k])){
							$listA[$k] = $listA[$k-1];
						}
						if($maxrow!=0){//建材合计为最后一行，后面的不要了
							unset($listA[$k]);
						}
						if($listA[$k]=='建材合计'){ 
							$maxrow=$k;
						}

					}
					
					
					// print_r($listA);
					$t = array_keys($listA);

					
					//大写字母A的ASCII值是65 A-Z对应65-90
					for($j = 'B'; $j <= 'H'; $j++ ){
						for($k = 3; $k <= $maxrow; $k++){
							$key = $j.$k;
							  $info = iconv("utf-8","gb2312//IGNORE", $sheet->getCell("B".$k)->getValue());
							    // $info = $sheet->getCell("B".$k)->getValue();
							
								$data[$k]['A']=$listA[$k];
								if($listA[$k]=='小计'){
									$data[$k]['A']=$data[$k-1]['A'];
									
								}
								if($listA[$k]=='小计'&&$j=='B'){
									$data[$k]['B']=$listA[$k];
								}else{
								  $data[$k][$j] = iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue());
                                    // $data[$k][$j] = $sheet->getCell($key)->getCalculatedValue();
								}
								
								$data[$k]['Z'] = "SGHCGCZXS";
						}
					}
;
					
					
				    $A2 = iconv("utf-8","gb2312//IGNORE", $sheet->getCell('A2')->getValue());

					$regex_year="'\d{4}年'is";
					preg_match($regex_year,$A2,$yeararr);//print_r($year);
					$year = explode('年', $yeararr[0]);
					$regex_month="'\d{1,2}月'is";
					preg_match($regex_month,$A2,$montharr);//print_r($year);
					$month = explode('月', $montharr[0]);
					$regex_day="'\d{1,2}日'is";
					preg_match($regex_day,$A2,$dayarr);//print_r($year);
					$day = explode('日', $dayarr[0]);
					$date =$year[0].'-'.$month[0].'-'.$day[0];

					$G2 = iconv("utf-8","gb2312//IGNORE", $sheet->getCell('G2')->getValue());
					// $G2 = $sheet->getCell('G2')->getValue();

                   
				
					$this->_dao->execute("delete from sg_data_table where dta_ym='$date' and dta_type ='SGHCGCZXS'");
					$basesql = "insert into sg_data_table(dta_1,dta_2,dta_3,dta_4,dta_5,dta_6,dta_7,dta_8,dta_vartype,dta_type,dta_ym,createtime,createuser) values";
					$valSql = "";
				
					foreach($data as $rows=>$columns)
					{
						
						$dta_1 = $columns['A'];
						$dta_2 = $columns['B'];
						if($rows==3){
							$dta_3 = $columns['C'];
							$dta_4 = $columns['D'];
							$dta_5 = $columns['E'];
						
							$dta_6 =  $columns['F'];
							$dta_7 =  $columns['G'];
							$dta_8 =  $columns['H'];
				
						}else{
							$dta_3 = str_replace("#DIV/0!","",sprintf("%.1f",round($columns['C'],1)));
							$dta_4 = str_replace("#DIV/0!","",sprintf("%.1f",round($columns['D'],1)));
							$dta_5 = str_replace("#DIV/0!","",sprintf("%.1f",round($columns['E'],1)));
						
							$dta_6 = str_replace("#DIV/0!","",sprintf("%.2f",round($columns['N'],2)));
							$dta_7 = str_replace("#DIV/0!","",sprintf("%.0f",round($columns['G'],0)));
							$dta_8 = str_replace("#DIV/0!","",sprintf("%.1f",round($columns['H'],1)));
						}

						$dta_type=$columns['Z'];
						$dta_vartype=$G2;
						$dta_ym = $date;
						
						$valSql.="('$dta_1','$dta_2','$dta_3','$dta_4','$dta_5','$dta_6','$dta_7','$dta_8','$dta_vartype','$dta_type','$dta_ym',NOW(),'".$user_infos['Uid']."'),";
						
					}
					
					$insertsql = substr($basesql.$valSql, 0, -1);
			       
					if(!empty($valSql))
					{
						$this->_dao->execute($insertsql);
						// echo $insertsql."<br><br>";
					}else{
						// 失败
						$error_sheet2[] = $sheet_v;
						$flag = false;
					}
				}
				if(!$flag)
				{
				
					$error2 = implode(',', $error_sheet2);
					
					if(!empty($error2))
					{
						$msg2 = "工作表：".iconv("utf-8","gb2312//IGNORE",$error2)."出现未知错误，导入失败";
					}
					$response["Success"] = 0;
					$response['Message'] = $msg2;
				}else{
					$response["Success"] = 1;
					$response['Message'] = '导入成功';
					$sheetname = substr($sheetname, 0, -1);
					$sql ="insert into sg_data_table_log(uptype,filepath,filename,sheetname,createtime,createuser,uptypedate) values('12','$file','$filename','$sheetname',now(),'".$user_infos['Uid']."','".$ymddate."')";
					$this->_dao->execute($sql);
			
				}
				unlink($file);
			}
		}
		echo $this->pri_JSON($response);
		exit;
	}



	function sheetyjy($params)
	{
		
		$GUID = $params['GUID'];
		$mode = $params['mode'];//1=客户端，2=安卓，3=iOS
		$this->assign('mode', $mode);
		$response = array(
			'Success'  => 1,
			'Message'  => '',
			'GUID'     => $GUID,
			'mode'     => $mode,
		);
		if($GUID==""){
			$response['Success'] = 0;
			$response['Message'] = 'GUID不能为空';
		}
		if($response['Success']){
			$year = $params['year'];
			$upfile = $_FILES['file'];
			if(strpos($this->array_iconv($upfile["name"]), '研究院公司结算月报')===false)
			{
				$response['Success'] = 0;
				$response['Message'] = '文件名非研究院公司结算月报！';
			}
			$user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
			if(empty($user_infos)){
				// $response['Success'] = 0;
				$response['Message'] = '用户不存在';
			}
			if($response['Success']){
				if ($upfile['error']==0)
				{
					if (is_uploaded_file($upfile['tmp_name'])) {
						$uptypes=array(
							'application/vnd.ms-excel',
							'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
						);
						if (in_array($upfile['type'], $uptypes)) {
							$extName = strtolower(end(explode('.', $upfile ['name'])));
							$filename = $this->random(5); // 设置随机数长度
							$extName=strtolower(end(explode('.', $upfile['name']))); // 取得扩展名
							$name1=date('YmdHis').".".$extName;
							$dir = UPLOADFILE;
							if (!file_exists($dir)) {
								if (mkdir($dir, 0777)) {
									$dest =$dir."/".$filename.$name1;
									
									if (move_uploaded_file($upfile ['tmp_name'], $dest)) {
										$response['Success'] = 1;
										$response['Message'] = '上传成功';
										$sheet11 = $this->readSheet($dest);
										$response['Result'] = $this->array_iconv($sheet11);
										$response['File'] = $filename.$name1;
										$response['Year'] = $year;
										$response['oldtmp'] = $this->array_iconv($upfile["name"]);
									} else {
										$response['Success'] = 0;
										$response['Message'] = '上传失败！目录权限不足';
									}
								} else {
									$response['Success'] = 0;
									$response['Message'] = '目录不存在，上传失败！';
								}
							} else {
								$dest = $dir."/".$filename.$name1;
								if (move_uploaded_file($upfile ['tmp_name'], $dest)) {
									$response['Success'] = 1;
									$response['Message'] = '上传成功';
									$sheet11 = $this->readSheet($dest);
									$response['Result'] = $this->array_iconv($sheet11);
									$response['File'] = $filename.$name1;
									$response['Year'] = $year;
									$response['oldtmp'] =$this->array_iconv($upfile["name"]);
								} else {
									$response['Success'] = 0;
									$response['Message'] = '上传失败！目录权限不足';
								}
							}
						} else {
							$response['Success'] = 0;
							$response['Message'] = '上传失败！检查文件是否是Excel文件';
						}
					} else {
						clearstatcache(); //清除文件缓存信息
						$response['Success'] = 0;
						$response['Message'] = '上传失败！';
					}
				}
				else
				{
					alert('上传失败！');
					goURL("sgbbdr.php?view=yjyybupload&GUID=".$GUID."&mode=".$mode);
					exit;
				}
			}
		}
		echo $this->pri_JSON($response);
		exit;
	}

	function dosheetyjy($params)
	{   
		$GUID = $params['GUID'];
		$mode = $params['mode'];//1=客户端，2=安卓，3=iOS
		$filename = iconv("utf-8","gb2312//IGNORE",$params['oldtmp']);

		$response = array(
			'Success'  => 1,
			'Message'  => '',
			// 'GUID'     => $GUID,
			// 'mode'     => $mode,
		);
		if($GUID==""){
			$response["Success"] = 0;
			$response['Message'] = 'GUID不能为空';
		}

		if($response["Success"])
		{   
			$user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
			if(empty($user_infos)){
				$response["Success"] = 0;
				$response['Message'] = '用户不存在';
			}

			$sheetList = explode(',', $params["sheet"]);
			$file = UPLOADFILE."/".$params["tmp"];
			
			if((empty($sheetList[0])&&$sheetList[0]!=0)||$params["sheet"]=='')
			{ 

				$response["Success"] = 0;
				$response['Message'] = '未选择工作表';
			}

			$year = mysql_real_escape_string($params["year"]);
			if(empty($year))
			{
				$response["Success"] = 0;
				$response['Message'] = '年份出错';
			}
			if($response["Success"])
			{
				require_once "../PHPExcel/PHPExcel.php";
				$type = pathinfo($file); 
				$type = strtolower($type["extension"]);
				if ($type=='xlsx') { 
					$type='Excel2007'; 
				}elseif($type=='xls') { 
					$type = 'Excel5';
				}
				$objReader = PHPExcel_IOFactory::createReader($type);//判断使用哪种格式
				$objReader ->setReadDataOnly(true); //只读取数据,会智能忽略所有空白行,这点很重要！！！
				$objPHPExcel = $objReader->load($file); //加载Excel文件
				$sheets = $objPHPExcel->getSheetNames();
				$sheetCount = $objPHPExcel->getSheetCount();//获取sheet工作表总个数

				$flag = true;
				foreach($sheetList as $sheet_index1)
				{
					$sheet_index = $sheetCount-1-$sheet_index1;
					// $sheet = $objPHPExcel->setActiveSheetIndexByName($sheet_v);
					$sheet = $objPHPExcel->getSheet($sheet_index);
					// $sheet_array = $objPHPExcel->getSheet($sheet_index)->toArray();
					// print_r($sheet_array);
					// exit;
					//得到当前工作表对象
					// $curSheet = $objPHPExcel->getActiveSheet();
					//获取当前工作表最大行数
				
				
					$rows = $sheet->getHighestRow();
					//获取当前工作表最大列数,返回的是最大的列名，如：B 
					$cols = $sheet->getHighestColumn();
					// print_r($cols);
					//将当前工作表名当键，内容为值存入数组

			
					$sheet_v = iconv("utf-8","gb2312//IGNORE",$sheets[$sheet_index]);
					$sheetname .= $sheet_v.",";

					$B3=iconv("utf-8","gb2312//IGNORE", $sheet->getCell('B3')->getValue());
					$regex_year="'\d{4}年'is";
					preg_match($regex_year,$B3,$yeararr);//print_r($year);
					$dryear = explode('年', $yeararr[0]);
					
					if($year!=$dryear[0])
					{
						$error_sheet1[] = $sheet_v;
						$flag = false;
						continue;
					}


                  
				
                 

					$data = array();
					$listA = array();
					$maxrow='0';
					for($k = 4; $k <= $rows; $k++){
						$key = "B".$k;
                         $listA[$k] = iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getValue());
						//  $listA[$k] = $sheet->getCell($key)->getValue();
						
						if(empty($listA[$k])){
							$listA[$k] = $listA[$k-1];
						}
						if($maxrow!=0){//总计为最后一行，后面的不要了
							unset($listA[$k]);
						}
						if($listA[$k]=='总计'){ 
							$maxrow=$k;
						}

					}
                    $key_arr='';
					for($i=66;$i<91;$i++){
						$key_arr[]= strtoupper(chr($i));//输出大写字母
					}
					for($j='A';$j<='C';$j++){
						for($l=66;$l<91;$l++){ 
							$key_arr[]= $j.strtoupper(chr($l));//输出大写字母
						}
					}
                    

					
					
					// print_r($listA);
					$t = array_keys($listA); 
					
				

					foreach ($key_arr as $kzm => $vzm) {
						for($k = 4; $k <= $maxrow; $k++){
							$key = $vzm.$k;
								$data[$k][$vzm] = iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue());
								//  $data[$k][$vzm] = $sheet->getCell($key)->getCalculatedValue();
							  
						}
					}
					
				
				 $n=0;
			

				$this->_dao->execute("delete from sg_data_table where dta_15='$year' and dta_type ='SGYJYJSYB'");

				 

				$basesql = "insert into sg_data_table(dta_1,dta_2,dta_3,dta_4,dta_type,dta_vartype,dta_ym,createtime,createuser,dta_15) values";
				
				
			//


				
                //龙钢1月份
				foreach($data as $rows=>$columns)
				{
                   if($rows==4){
					$dat_1_1=$columns['B'];
					$dat_1_2=$columns['C'];
					$dat_1_3=$columns['C'];
					$dat_1_4=$columns['C'];
                    //确认数据年份月份
					$regex_month="'\d{1,2}月'is";
					preg_match($regex_month,$columns['C'],$montharr);//print_r($year);
					$month = explode('月', $montharr[0]);
					if($month[0]!=''){
						$dta_ym_1=$year.'-'.$month[0];
						
					}else{
						$dta_ym_1=$year;
					}

                    //确认数据dta_vartype的标识
					if(strpos($columns['C'],'汉钢')!==false){
						$dta_vartype_1='SGHG';
					}else if(strpos($columns['C'],'龙钢')!==false){
						$dta_vartype_1='SGLG';
					}else{
						$dta_vartype_1='SGQT';
					}


				   }else if($rows==5){
					$dat_1_1=$columns['B'];
					$dat_1_2=$columns['C'];
					$dat_1_3=$columns['D'];
					$dat_1_4=$columns['E'];
				   }else{
					$dat_1_1=$columns['B'];
					$dat_1_2=sprintf("%.2f",round($columns['C'],2));
					$dat_1_3=sprintf("%.2f",round($columns['D'],2));
					$dat_1_4=sprintf("%.2f",round($columns['E'],2));
				   }


				
					
				   $values1.="('$dat_1_1','$dat_1_2','$dat_1_3','$dat_1_4','SGYJYJSYB','$dta_vartype_1','$dta_ym_1',NOW(),'".$user_infos['Uid']."','$year'),";
				}
				
		
			 //龙钢2月份
			 foreach($data as $rows=>$columns)
			 {
				if($rows==4){
				 $dat_1_1=$columns['B'];
				 $dat_1_2=$columns['F'];
				 $dat_1_3=$columns['F'];
				 $dat_1_4=$columns['F'];
				 //确认数据年份月份
				 $regex_month="'\d{1,2}月'is";
				 preg_match($regex_month,$columns['F'],$montharr);//print_r($year);
				 $month = explode('月', $montharr[0]);
				 if($month[0]!=''){
					 $dta_ym_1=$year.'-'.$month[0];
					 
				 }else{
					 $dta_ym_1=$year;
				 }

				 //确认数据dta_vartype的标识
				 if(strpos($columns['F'],'汉钢')!==false){
					 $dta_vartype_1='SGHG';
				 }else if(strpos($columns['F'],'龙钢')!==false){
					 $dta_vartype_1='SGLG';
				 }else{
					 $dta_vartype_1='SGQT';
				 }


				}else if($rows==5){
				 $dat_1_1=$columns['B'];
				 $dat_1_2=$columns['F'];
				 $dat_1_3=$columns['G'];
				 $dat_1_4=$columns['H'];
				}else{
				 $dat_1_1=$columns['B'];
				 $dat_1_2=sprintf("%.2f",round($columns['F'],2));
				 $dat_1_3=sprintf("%.2f",round($columns['G'],2));
				 $dat_1_4=sprintf("%.2f",round($columns['H'],2));
				}


			 
				 
				$values2.="('$dat_1_1','$dat_1_2','$dat_1_3','$dat_1_4','SGYJYJSYB','$dta_vartype_1','$dta_ym_1',NOW(),'".$user_infos['Uid']."','$year'),";
			 }
  
			
				
		  //龙钢3月份
		  foreach($data as $rows=>$columns)
		  {
			 if($rows==4){
			  $dat_1_1=$columns['B'];
			  $dat_1_2=$columns['I'];
			  $dat_1_3=$columns['I'];
			  $dat_1_4=$columns['I'];
			  //确认数据年份月份
			  $regex_month="'\d{1,2}月'is";
			  preg_match($regex_month,$columns['I'],$montharr);//print_r($year);
			  $month = explode('月', $montharr[0]);
			  if($month[0]!=''){
				  $dta_ym_1=$year.'-'.$month[0];
				  
			  }else{
				  $dta_ym_1=$year;
			  }

			  //确认数据dta_vartype的标识
			  if(strpos($columns['I'],'汉钢')!==false){
				  $dta_vartype_1='SGHG';
			  }else if(strpos($columns['I'],'龙钢')!==false){
				  $dta_vartype_1='SGLG';
			  }else{
				  $dta_vartype_1='SGQT';
			  }


			 }else if($rows==5){
			  $dat_1_1=$columns['B'];
			  $dat_1_2=$columns['I'];
			  $dat_1_3=$columns['J'];
			  $dat_1_4=$columns['K'];
			 }else{
			  $dat_1_1=$columns['B'];
			  $dat_1_2=sprintf("%.2f",round($columns['I'],2));
			  $dat_1_3=sprintf("%.2f",round($columns['J'],2));
			  $dat_1_4=sprintf("%.2f",round($columns['K'],2));
			 }  
			 $values3.="('$dat_1_1','$dat_1_2','$dat_1_3','$dat_1_4','SGYJYJSYB','$dta_vartype_1','$dta_ym_1',NOW(),'".$user_infos['Uid']."','$year'),";
		  }
		 
			//龙钢4月份
			foreach($data as $rows=>$columns)
			{
				if($rows==4){
				$dat_1_1=$columns['B'];
				$dat_1_2=$columns['L'];
				$dat_1_3=$columns['L'];
				$dat_1_4=$columns['L'];
				//确认数据年份月份
				$regex_month="'\d{1,2}月'is";
				preg_match($regex_month,$columns['L'],$montharr);//print_r($year);
				$month = explode('月', $montharr[0]);
				if($month[0]!=''){
					$dta_ym_1=$year.'-'.$month[0];
					
				}else{
					$dta_ym_1=$year;
				}

				//确认数据dta_vartype的标识
				if(strpos($columns['L'],'汉钢')!==false){
					$dta_vartype_1='SGHG';
				}else if(strpos($columns['L'],'龙钢')!==false){
					$dta_vartype_1='SGLG';
				}else{
					$dta_vartype_1='SGQT';
				}


				}else if($rows==5){
				$dat_1_1=$columns['B'];
				$dat_1_2=$columns['L'];
				$dat_1_3=$columns['M'];
				$dat_1_4=$columns['N'];
				}else{
				$dat_1_1=$columns['B'];
				$dat_1_2=sprintf("%.2f",round($columns['L'],2));
				$dat_1_3=sprintf("%.2f",round($columns['M'],2));
				$dat_1_4=sprintf("%.2f",round($columns['N'],2));
				}


			
				
				$values4.="('$dat_1_1','$dat_1_2','$dat_1_3','$dat_1_4','SGYJYJSYB','$dta_vartype_1','$dta_ym_1',NOW(),'".$user_infos['Uid']."','$year'),";
			}
			
			//龙钢5月份
			foreach($data as $rows=>$columns)
			{
				if($rows==4){
				$dat_1_1=$columns['B'];
				$dat_1_2=$columns['O'];
				$dat_1_3=$columns['O'];
				$dat_1_4=$columns['O'];
				//确认数据年份月份
				$regex_month="'\d{1,2}月'is";
				preg_match($regex_month,$columns['O'],$montharr);//print_r($year);
				$month = explode('月', $montharr[0]);
				if($month[0]!=''){
					$dta_ym_1=$year.'-'.$month[0];
					
				}else{
					$dta_ym_1=$year;
				}

				//确认数据dta_vartype的标识
				if(strpos($columns['O'],'汉钢')!==false){
					$dta_vartype_1='SGHG';
				}else if(strpos($columns['O'],'龙钢')!==false){
					$dta_vartype_1='SGLG';
				}else{
					$dta_vartype_1='SGQT';
				}


				}else if($rows==5){
				$dat_1_1=$columns['B'];
				$dat_1_2=$columns['O'];
				$dat_1_3=$columns['P'];
				$dat_1_4=$columns['Q'];
				}else{
				$dat_1_1=$columns['B'];
				$dat_1_2=sprintf("%.2f",round($columns['O'],2));
				$dat_1_3=sprintf("%.2f",round($columns['P'],2));
				$dat_1_4=sprintf("%.2f",round($columns['Q'],2));
				}


			
				
				$values5.="('$dat_1_1','$dat_1_2','$dat_1_3','$dat_1_4','SGYJYJSYB','$dta_vartype_1','$dta_ym_1',NOW(),'".$user_infos['Uid']."','$year'),";
			}
		
			//龙钢6月份
			foreach($data as $rows=>$columns)
			{
				if($rows==4){
				$dat_1_1=$columns['B'];
				$dat_1_2=$columns['R'];
				$dat_1_3=$columns['R'];
				$dat_1_4=$columns['R'];
				//确认数据年份月份
				$regex_month="'\d{1,2}月'is";
				preg_match($regex_month,$columns['R'],$montharr);//print_r($year);
				$month = explode('月', $montharr[0]);
				if($month[0]!=''){
					$dta_ym_1=$year.'-'.$month[0];
					
				}else{
					$dta_ym_1=$year;
				}

				//确认数据dta_vartype的标识
				if(strpos($columns['R'],'汉钢')!==false){
					$dta_vartype_1='SGHG';
				}else if(strpos($columns['R'],'龙钢')!==false){
					$dta_vartype_1='SGLG';
				}else{
					$dta_vartype_1='SGQT';
				}


				}else if($rows==5){
				$dat_1_1=$columns['B'];
				$dat_1_2=$columns['R'];
				$dat_1_3=$columns['S'];
				$dat_1_4=$columns['T'];
				}else{
				$dat_1_1=$columns['B'];
				$dat_1_2=sprintf("%.2f",round($columns['R'],2));
				$dat_1_3=sprintf("%.2f",round($columns['S'],2));
				$dat_1_4=sprintf("%.2f",round($columns['T'],2));
				}



				
				$values6.="('$dat_1_1','$dat_1_2','$dat_1_3','$dat_1_4','SGYJYJSYB','$dta_vartype_1','$dta_ym_1',NOW(),'".$user_infos['Uid']."','$year'),";
			}
		
			
			//龙钢7月份
			foreach($data as $rows=>$columns)
			{
				if($rows==4){
				$dat_1_1=$columns['B'];
				$dat_1_2=$columns['U'];
				$dat_1_3=$columns['U'];
				$dat_1_4=$columns['U'];
				//确认数据年份月份
				$regex_month="'\d{1,2}月'is";
				preg_match($regex_month,$columns['U'],$montharr);//print_r($year);
				$month = explode('月', $montharr[0]);
				if($month[0]!=''){
					$dta_ym_1=$year.'-'.$month[0];
					
				}else{
					$dta_ym_1=$year;
				}

				//确认数据dta_vartype的标识
				if(strpos($columns['U'],'汉钢')!==false){
					$dta_vartype_1='SGHG';
				}else if(strpos($columns['U'],'龙钢')!==false){
					$dta_vartype_1='SGLG';
				}else{
					$dta_vartype_1='SGQT';
				}


				}else if($rows==5){
				$dat_1_1=$columns['B'];
				$dat_1_2=$columns['U'];
				$dat_1_3=$columns['V'];
				$dat_1_4=$columns['W'];
				}else{
				$dat_1_1=$columns['B'];
				$dat_1_2=sprintf("%.2f",round($columns['U'],2));
				$dat_1_3=sprintf("%.2f",round($columns['V'],2));
				$dat_1_4=sprintf("%.2f",round($columns['W'],2));
				}



				
				$values7.="('$dat_1_1','$dat_1_2','$dat_1_3','$dat_1_4','SGYJYJSYB','$dta_vartype_1','$dta_ym_1',NOW(),'".$user_infos['Uid']."','$year'),";
			}
			
			//龙钢8月份
			foreach($data as $rows=>$columns)
			{
				if($rows==4){
				$dat_1_1=$columns['B'];
				$dat_1_2=$columns['X'];
				$dat_1_3=$columns['X'];
				$dat_1_4=$columns['X'];
				//确认数据年份月份
				$regex_month="'\d{1,2}月'is";
				preg_match($regex_month,$columns['X'],$montharr);//print_r($year);
				$month = explode('月', $montharr[0]);
				if($month[0]!=''){
					$dta_ym_1=$year.'-'.$month[0];
					
				}else{
					$dta_ym_1=$year;
				}

				//确认数据dta_vartype的标识
				if(strpos($columns['X'],'汉钢')!==false){
					$dta_vartype_1='SGHG';
				}else if(strpos($columns['X'],'龙钢')!==false){
					$dta_vartype_1='SGLG';
				}else{
					$dta_vartype_1='SGQT';
				}


				}else if($rows==5){
				$dat_1_1=$columns['B'];
				$dat_1_2=$columns['X'];
				$dat_1_3=$columns['Y'];
				$dat_1_4=$columns['Z'];
				}else{
				$dat_1_1=$columns['B'];
				$dat_1_2=sprintf("%.2f",round($columns['X'],2));
				$dat_1_3=sprintf("%.2f",round($columns['Y'],2));
				$dat_1_4=sprintf("%.2f",round($columns['Z'],2));
				}



				
				$values8.="('$dat_1_1','$dat_1_2','$dat_1_3','$dat_1_4','SGYJYJSYB','$dta_vartype_1','$dta_ym_1',NOW(),'".$user_infos['Uid']."','$year'),";
			}
			
			//龙钢9月份
			foreach($data as $rows=>$columns)
			{
				if($rows==4){
				$dat_1_1=$columns['B'];
				$dat_1_2=$columns['AA'];
				$dat_1_3=$columns['AA'];
				$dat_1_4=$columns['AA'];
				//确认数据年份月份
				$regex_month="'\d{1,2}月'is";
				preg_match($regex_month,$columns['AA'],$montharr);//print_r($year);
				$month = explode('月', $montharr[0]);
				if($month[0]!=''){
					$dta_ym_1=$year.'-'.$month[0];
					
				}else{
					$dta_ym_1=$year;
				}

				//确认数据dta_vartype的标识
				if(strpos($columns['AA'],'汉钢')!==false){
					$dta_vartype_1='SGHG';
				}else if(strpos($columns['AA'],'龙钢')!==false){
					$dta_vartype_1='SGLG';
				}else{
					$dta_vartype_1='SGQT';
				}


				}else if($rows==5){
				$dat_1_1=$columns['B'];
				$dat_1_2=$columns['AA'];
				$dat_1_3=$columns['AB'];
				$dat_1_4=$columns['AC'];
				}else{
				$dat_1_1=$columns['B'];
				$dat_1_2=sprintf("%.2f",round($columns['AA'],2));
				$dat_1_3=sprintf("%.2f",round($columns['AB'],2));
				$dat_1_4=sprintf("%.2f",round($columns['AC'],2));
				}



				
				$values9.="('$dat_1_1','$dat_1_2','$dat_1_3','$dat_1_4','SGYJYJSYB','$dta_vartype_1','$dta_ym_1',NOW(),'".$user_infos['Uid']."','$year'),";
			}
		

			//龙钢10月份
			foreach($data as $rows=>$columns)
			{
				if($rows==4){
				$dat_1_1=$columns['B'];
				$dat_1_2=$columns['AD'];
				$dat_1_3=$columns['AD'];
				$dat_1_4=$columns['AD'];
				//确认数据年份月份
				$regex_month="'\d{1,2}月'is";
				preg_match($regex_month,$columns['AD'],$montharr);//print_r($year);
				$month = explode('月', $montharr[0]);
				if($month[0]!=''){
					$dta_ym_1=$year.'-'.$month[0];
					
				}else{
					$dta_ym_1=$year;
				}

				//确认数据dta_vartype的标识
				if(strpos($columns['AD'],'汉钢')!==false){
					$dta_vartype_1='SGHG';
				}else if(strpos($columns['AD'],'龙钢')!==false){
					$dta_vartype_1='SGLG';
				}else{
					$dta_vartype_1='SGQT';
				}


				}else if($rows==5){
				$dat_1_1=$columns['B'];
				$dat_1_2=$columns['AD'];
				$dat_1_3=$columns['AE'];
				$dat_1_4=$columns['AF'];
				}else{
				$dat_1_1=$columns['B'];
				$dat_1_2=sprintf("%.2f",round($columns['AD'],2));
				$dat_1_3=sprintf("%.2f",round($columns['AE'],2));
				$dat_1_4=sprintf("%.2f",round($columns['AF'],2));
				}



				
				$values10.="('$dat_1_1','$dat_1_2','$dat_1_3','$dat_1_4','SGYJYJSYB','$dta_vartype_1','$dta_ym_1',NOW(),'".$user_infos['Uid']."','$year'),";
			}
	
			//龙钢11月份
			foreach($data as $rows=>$columns)
			{
				if($rows==4){
				$dat_1_1=$columns['B'];
				$dat_1_2=$columns['AG'];
				$dat_1_3=$columns['AG'];
				$dat_1_4=$columns['AG'];
				//确认数据年份月份
				$regex_month="'\d{1,2}月'is";
				preg_match($regex_month,$columns['AG'],$montharr);//print_r($year);
				$month = explode('月', $montharr[0]);
				if($month[0]!=''){
					$dta_ym_1=$year.'-'.$month[0];
					
				}else{
					$dta_ym_1=$year;
				}

				//确认数据dta_vartype的标识
				if(strpos($columns['AG'],'汉钢')!==false){
					$dta_vartype_1='SGHG';
				}else if(strpos($columns['AG'],'龙钢')!==false){
					$dta_vartype_1='SGLG';
				}else{
					$dta_vartype_1='SGQT';
				}


				}else if($rows==5){
				$dat_1_1=$columns['B'];
				$dat_1_2=$columns['AG'];
				$dat_1_3=$columns['AH'];
				$dat_1_4=$columns['AI'];
				}else{
				$dat_1_1=$columns['B'];
				$dat_1_2=sprintf("%.2f",round($columns['AG'],2));
				$dat_1_3=sprintf("%.2f",round($columns['AH'],2));
				$dat_1_4=sprintf("%.2f",round($columns['AI'],2));
				}



				
				$values11.="('$dat_1_1','$dat_1_2','$dat_1_3','$dat_1_4','SGYJYJSYB','$dta_vartype_1','$dta_ym_1',NOW(),'".$user_infos['Uid']."','$year'),";
			}	
			//龙钢12月份
			foreach($data as $rows=>$columns)
			{
				if($rows==4){
				$dat_1_1=$columns['B'];
				$dat_1_2=$columns['AJ'];
				$dat_1_3=$columns['AJ'];
				$dat_1_4=$columns['AJ'];
				//确认数据年份月份
				$regex_month="'\d{1,2}月'is";
				preg_match($regex_month,$columns['AJ'],$montharr);//print_r($year);
				$month = explode('月', $montharr[0]);
				if($month[0]!=''){
					$dta_ym_1=$year.'-'.$month[0];
					
				}else{
					$dta_ym_1=$year;
				}

				//确认数据dta_vartype的标识
				if(strpos($columns['AJ'],'汉钢')!==false){
					$dta_vartype_1='SGHG';
				}else if(strpos($columns['AJ'],'龙钢')!==false){
					$dta_vartype_1='SGLG';
				}else{
					$dta_vartype_1='SGQT';
				}


				}else if($rows==5){
				$dat_1_1=$columns['B'];
				$dat_1_2=$columns['AJ'];
				$dat_1_3=$columns['AK'];
				$dat_1_4=$columns['AL'];
				}else{
				$dat_1_1=$columns['B'];
				$dat_1_2=sprintf("%.2f",round($columns['AJ'],2));
				$dat_1_3=sprintf("%.2f",round($columns['AK'],2));
				$dat_1_4=sprintf("%.2f",round($columns['AL'],2));
				}



				
				$values12.="('$dat_1_1','$dat_1_2','$dat_1_3','$dat_1_4','SGYJYJSYB','$dta_vartype_1','$dta_ym_1',NOW(),'".$user_infos['Uid']."','$year'),";
			}
			//龙钢当年
			foreach($data as $rows=>$columns)
			{
				if($rows==4){
				$dat_1_1=$columns['B'];
				$dat_1_2=$columns['AM'];
				$dat_1_3=$columns['AM'];
				$dat_1_4=$columns['AM'];
				//确认数据年份月份
				$regex_month="'\d{1,2}月'is";
				preg_match($regex_month,$columns['AM'],$montharr);//print_r($year);
				$month = explode('月', $montharr[0]);
				if($month[0]!=''){
					$dta_ym_1=$year.'-'.$month[0];
					
				}else{
					$dta_ym_1=$year;
				}

				//确认数据dta_vartype的标识
				if(strpos($columns['AM'],'汉钢')!==false){
					$dta_vartype_1='SGHG';
				}else if(strpos($columns['AM'],'龙钢')!==false){
					$dta_vartype_1='SGLG';
				}else{
					$dta_vartype_1='SGQT';
				}


				}else if($rows==5){
				$dat_1_1=$columns['B'];
				$dat_1_2=$columns['AM'];
				$dat_1_3=$columns['AN'];
				$dat_1_4=$columns['AO'];
				}else{
				$dat_1_1=$columns['B'];
				$dat_1_2=sprintf("%.2f",round($columns['AM'],2));
				$dat_1_3=sprintf("%.2f",round($columns['AN'],2));
				$dat_1_4=sprintf("%.2f",round($columns['AO'],2));
				}



				
				$values13.="('$dat_1_1','$dat_1_2','$dat_1_3','$dat_1_4','SGYJYJSYB','$dta_vartype_1','$dta_ym_1',NOW(),'".$user_infos['Uid']."','$year'),";
			}
	//汉钢一月份
	foreach($data as $rows=>$columns)
	{
		if($rows==4){
		$dat_1_1=$columns['B'];
		$dat_1_2=$columns['AP'];
		$dat_1_3=$columns['AP'];
		$dat_1_4=$columns['AP'];
		//确认数据年份月份
		$regex_month="'\d{1,2}月'is";
		preg_match($regex_month,$columns['AP'],$montharr);//print_r($year);
		$month = explode('月', $montharr[0]);
		if($month[0]!=''){
			$dta_ym_1=$year.'-'.$month[0];
			
		}else{
			$dta_ym_1=$year;
		}

		//确认数据dta_vartype的标识
		if(strpos($columns['AP'],'汉钢')!==false){
			$dta_vartype_1='SGHG';
		}else if(strpos($columns['AP'],'龙钢')!==false){
			$dta_vartype_1='SGLG';
		}else{
			$dta_vartype_1='SGQT';
		}


		}else if($rows==5){
		$dat_1_1=$columns['B'];
		$dat_1_2=$columns['AP'];
		$dat_1_3=$columns['AQ'];
		$dat_1_4=$columns['AR'];
		}else{
		$dat_1_1=$columns['B'];
		$dat_1_2=sprintf("%.2f",round($columns['AP'],2));
		$dat_1_3=sprintf("%.2f",round($columns['AQ'],2));
		$dat_1_4=sprintf("%.2f",round($columns['AR'],2));
		}



		
		$values_hg_1.="('$dat_1_1','$dat_1_2','$dat_1_3','$dat_1_4','SGYJYJSYB','$dta_vartype_1','$dta_ym_1',NOW(),'".$user_infos['Uid']."','$year'),";
	}
	//汉钢2月份
	foreach($data as $rows=>$columns)
	{
		if($rows==4){
		$dat_1_1=$columns['B'];
		$dat_1_2=$columns['AS'];
		$dat_1_3=$columns['AS'];
		$dat_1_4=$columns['AS'];
		//确认数据年份月份
		$regex_month="'\d{1,2}月'is";
		preg_match($regex_month,$columns['AS'],$montharr);//print_r($year);
		$month = explode('月', $montharr[0]);
		if($month[0]!=''){
			$dta_ym_1=$year.'-'.$month[0];
			
		}else{
			$dta_ym_1=$year;
		}

		//确认数据dta_vartype的标识
		if(strpos($columns['AS'],'汉钢')!==false){
			$dta_vartype_1='SGHG';
		}else if(strpos($columns['AS'],'龙钢')!==false){
			$dta_vartype_1='SGLG';
		}else{
			$dta_vartype_1='SGQT';
		}


		}else if($rows==5){
		$dat_1_1=$columns['B'];
		$dat_1_2=$columns['AS'];
		$dat_1_3=$columns['AT'];
		$dat_1_4=$columns['AU'];
		}else{
		$dat_1_1=$columns['B'];
		$dat_1_2=sprintf("%.2f",round($columns['AS'],2));
		$dat_1_3=sprintf("%.2f",round($columns['AT'],2));
		$dat_1_4=sprintf("%.2f",round($columns['AU'],2));
		}



		
		$values_hg_2.="('$dat_1_1','$dat_1_2','$dat_1_3','$dat_1_4','SGYJYJSYB','$dta_vartype_1','$dta_ym_1',NOW(),'".$user_infos['Uid']."','$year'),";
	}
  //汉钢3月份
	foreach($data as $rows=>$columns)
	{
		if($rows==4){
		$dat_1_1=$columns['B'];
		$dat_1_2=$columns['AV'];
		$dat_1_3=$columns['AV'];
		$dat_1_4=$columns['AV'];
		//确认数据年份月份
		$regex_month="'\d{1,2}月'is";
		preg_match($regex_month,$columns['AV'],$montharr);//print_r($year);
		$month = explode('月', $montharr[0]);
		if($month[0]!=''){
			$dta_ym_1=$year.'-'.$month[0];
			
		}else{
			$dta_ym_1=$year;
		}

		//确认数据dta_vartype的标识
		if(strpos($columns['AV'],'汉钢')!==false){
			$dta_vartype_1='SGHG';
		}else if(strpos($columns['AV'],'龙钢')!==false){
			$dta_vartype_1='SGLG';
		}else{
			$dta_vartype_1='SGQT';
		}


		}else if($rows==5){
		$dat_1_1=$columns['B'];
		$dat_1_2=$columns['AV'];
		$dat_1_3=$columns['AW'];
		$dat_1_4=$columns['AX'];
		}else{
		$dat_1_1=$columns['B'];
		$dat_1_2=sprintf("%.2f",round($columns['AV'],2));
		$dat_1_3=sprintf("%.2f",round($columns['AW'],2));
		$dat_1_4=sprintf("%.2f",round($columns['AX'],2));
		}



		
		$values_hg_3.="('$dat_1_1','$dat_1_2','$dat_1_3','$dat_1_4','SGYJYJSYB','$dta_vartype_1','$dta_ym_1',NOW(),'".$user_infos['Uid']."','$year'),";
	}
	//汉钢4月份
	foreach($data as $rows=>$columns)
	{
		if($rows==4){
		$dat_1_1=$columns['B'];
		$dat_1_2=$columns['AY'];
		$dat_1_3=$columns['AY'];
		$dat_1_4=$columns['AY'];
		//确认数据年份月份
		$regex_month="'\d{1,2}月'is";
		preg_match($regex_month,$columns['AY'],$montharr);//print_r($year);
		$month = explode('月', $montharr[0]);
		if($month[0]!=''){
			$dta_ym_1=$year.'-'.$month[0];
			
		}else{
			$dta_ym_1=$year;
		}

		//确认数据dta_vartype的标识
		if(strpos($columns['AY'],'汉钢')!==false){
			$dta_vartype_1='SGHG';
		}else if(strpos($columns['AY'],'龙钢')!==false){
			$dta_vartype_1='SGLG';
		}else{
			$dta_vartype_1='SGQT';
		}


		}else if($rows==5){
		$dat_1_1=$columns['B'];
		$dat_1_2=$columns['AY'];
		$dat_1_3=$columns['AZ'];
		$dat_1_4=$columns['BA'];
		}else{
		$dat_1_1=$columns['B'];
		$dat_1_2=sprintf("%.2f",round($columns['AY'],2));
		$dat_1_3=sprintf("%.2f",round($columns['AZ'],2));
		$dat_1_4=sprintf("%.2f",round($columns['BA'],2));
		}



		
		$values_hg_4.="('$dat_1_1','$dat_1_2','$dat_1_3','$dat_1_4','SGYJYJSYB','$dta_vartype_1','$dta_ym_1',NOW(),'".$user_infos['Uid']."','$year'),";
	}
	//汉钢5月份
	foreach($data as $rows=>$columns)
	{
		if($rows==4){
		$dat_1_1=$columns['B'];
		$dat_1_2=$columns['BB'];
		$dat_1_3=$columns['BB'];
		$dat_1_4=$columns['BB'];
		//确认数据年份月份
		$regex_month="'\d{1,2}月'is";
		preg_match($regex_month,$columns['BB'],$montharr);//print_r($year);
		$month = explode('月', $montharr[0]);
		if($month[0]!=''){
			$dta_ym_1=$year.'-'.$month[0];
			
		}else{
			$dta_ym_1=$year;
		}

		//确认数据dta_vartype的标识
		if(strpos($columns['BB'],'汉钢')!==false){
			$dta_vartype_1='SGHG';
		}else if(strpos($columns['BB'],'龙钢')!==false){
			$dta_vartype_1='SGLG';
		}else{
			$dta_vartype_1='SGQT';
		}


		}else if($rows==5){
		$dat_1_1=$columns['B'];
		$dat_1_2=$columns['BB'];
		$dat_1_3=$columns['BC'];
		$dat_1_4=$columns['BD'];
		}else{
		$dat_1_1=$columns['B'];
		$dat_1_2=sprintf("%.2f",round($columns['BB'],2));
		$dat_1_3=sprintf("%.2f",round($columns['BC'],2));
		$dat_1_4=sprintf("%.2f",round($columns['BD'],2));
		}



		
		$values_hg_5.="('$dat_1_1','$dat_1_2','$dat_1_3','$dat_1_4','SGYJYJSYB','$dta_vartype_1','$dta_ym_1',NOW(),'".$user_infos['Uid']."','$year'),";
	}

	//汉钢6月份
	foreach($data as $rows=>$columns)
	{
		if($rows==4){
		$dat_1_1=$columns['B'];
		$dat_1_2=$columns['BE'];
		$dat_1_3=$columns['BE'];
		$dat_1_4=$columns['BE'];
		//确认数据年份月份
		$regex_month="'\d{1,2}月'is";
		preg_match($regex_month,$columns['BE'],$montharr);//print_r($year);
		$month = explode('月', $montharr[0]);
		if($month[0]!=''){
			$dta_ym_1=$year.'-'.$month[0];
			
		}else{
			$dta_ym_1=$year;
		}

		//确认数据dta_vartype的标识
		if(strpos($columns['BE'],'汉钢')!==false){
			$dta_vartype_1='SGHG';
		}else if(strpos($columns['BE'],'龙钢')!==false){
			$dta_vartype_1='SGLG';
		}else{
			$dta_vartype_1='SGQT';
		}


		}else if($rows==5){
		$dat_1_1=$columns['B'];
		$dat_1_2=$columns['BE'];
		$dat_1_3=$columns['BF'];
		$dat_1_4=$columns['BG'];
		}else{
		$dat_1_1=$columns['B'];
		$dat_1_2=sprintf("%.2f",round($columns['BE'],2));
		$dat_1_3=sprintf("%.2f",round($columns['BF'],2));
		$dat_1_4=sprintf("%.2f",round($columns['BG'],2));
		}



		
		$values_hg_6.="('$dat_1_1','$dat_1_2','$dat_1_3','$dat_1_4','SGYJYJSYB','$dta_vartype_1','$dta_ym_1',NOW(),'".$user_infos['Uid']."','$year'),";
	}
	//汉钢7月份
	foreach($data as $rows=>$columns)
	{
		if($rows==4){
		$dat_1_1=$columns['B'];
		$dat_1_2=$columns['BH'];
		$dat_1_3=$columns['BH'];
		$dat_1_4=$columns['BH'];
		//确认数据年份月份
		$regex_month="'\d{1,2}月'is";
		preg_match($regex_month,$columns['BH'],$montharr);//print_r($year);
		$month = explode('月', $montharr[0]);
		if($month[0]!=''){
			$dta_ym_1=$year.'-'.$month[0];
			
		}else{
			$dta_ym_1=$year;
		}

		//确认数据dta_vartype的标识
		if(strpos($columns['BH'],'汉钢')!==false){
			$dta_vartype_1='SGHG';
		}else if(strpos($columns['BH'],'龙钢')!==false){
			$dta_vartype_1='SGLG';
		}else{
			$dta_vartype_1='SGQT';
		}


		}else if($rows==5){
		$dat_1_1=$columns['B'];
		$dat_1_2=$columns['BH'];
		$dat_1_3=$columns['BI'];
		$dat_1_4=$columns['BJ'];
		}else{
		$dat_1_1=$columns['B'];
		$dat_1_2=sprintf("%.2f",round($columns['BH'],2));
		$dat_1_3=sprintf("%.2f",round($columns['BI'],2));
		$dat_1_4=sprintf("%.2f",round($columns['BJ'],2));
		}



		
		$values_hg_7.="('$dat_1_1','$dat_1_2','$dat_1_3','$dat_1_4','SGYJYJSYB','$dta_vartype_1','$dta_ym_1',NOW(),'".$user_infos['Uid']."','$year'),";
	}
	//汉钢8月份
	foreach($data as $rows=>$columns)
	{
		if($rows==4){
		$dat_1_1=$columns['B'];
		$dat_1_2=$columns['BK'];
		$dat_1_3=$columns['BL'];
		$dat_1_4=$columns['BM'];
		//确认数据年份月份
		$regex_month="'\d{1,2}月'is";
		preg_match($regex_month,$columns['BK'],$montharr);//print_r($year);
		$month = explode('月', $montharr[0]);
		if($month[0]!=''){
			$dta_ym_1=$year.'-'.$month[0];
			
		}else{
			$dta_ym_1=$year;
		}

		//确认数据dta_vartype的标识
		if(strpos($columns['BK'],'汉钢')!==false){
			$dta_vartype_1='SGHG';
		}else if(strpos($columns['BK'],'龙钢')!==false){
			$dta_vartype_1='SGLG';
		}else{
			$dta_vartype_1='SGQT';
		}


		}else if($rows==5){
		$dat_1_1=$columns['B'];
		$dat_1_2=$columns['BK'];
		$dat_1_3=$columns['BL'];
		$dat_1_4=$columns['BM'];
		}else{
		$dat_1_1=$columns['B'];
		$dat_1_2=sprintf("%.2f",round($columns['BK'],2));
		$dat_1_3=sprintf("%.2f",round($columns['BL'],2));
		$dat_1_4=sprintf("%.2f",round($columns['BM'],2));
		}



		
		$values_hg_8.="('$dat_1_1','$dat_1_2','$dat_1_3','$dat_1_4','SGYJYJSYB','$dta_vartype_1','$dta_ym_1',NOW(),'".$user_infos['Uid']."','$year'),";
	}
	//汉钢9月份
	foreach($data as $rows=>$columns)
	{
		if($rows==4){
		$dat_1_1=$columns['B'];
		$dat_1_2=$columns['BN'];
		$dat_1_3=$columns['BN'];
		$dat_1_4=$columns['BN'];
		//确认数据年份月份
		$regex_month="'\d{1,2}月'is";
		preg_match($regex_month,$columns['BN'],$montharr);//print_r($year);
		$month = explode('月', $montharr[0]);
		if($month[0]!=''){
			$dta_ym_1=$year.'-'.$month[0];
			
		}else{
			$dta_ym_1=$year;
		}

		//确认数据dta_vartype的标识
		if(strpos($columns['BN'],'汉钢')!==false){
			$dta_vartype_1='SGHG';
		}else if(strpos($columns['BN'],'龙钢')!==false){
			$dta_vartype_1='SGLG';
		}else{
			$dta_vartype_1='SGQT';
		}


		}else if($rows==5){
		$dat_1_1=$columns['B'];
		$dat_1_2=$columns['BN'];
		$dat_1_3=$columns['BO'];
		$dat_1_4=$columns['BP'];
		}else{
		$dat_1_1=$columns['B'];
		$dat_1_2=sprintf("%.2f",round($columns['BN'],2));
		$dat_1_3=sprintf("%.2f",round($columns['BO'],2));
		$dat_1_4=sprintf("%.2f",round($columns['BP'],2));
		}



		
		$values_hg_9.="('$dat_1_1','$dat_1_2','$dat_1_3','$dat_1_4','SGYJYJSYB','$dta_vartype_1','$dta_ym_1',NOW(),'".$user_infos['Uid']."','$year'),";
	}
	//汉钢10月份
	foreach($data as $rows=>$columns)
	{
		if($rows==4){
		$dat_1_1=$columns['B'];
		$dat_1_2=$columns['BQ'];
		$dat_1_3=$columns['BQ'];
		$dat_1_4=$columns['BQ'];
		//确认数据年份月份
		$regex_month="'\d{1,2}月'is";
		preg_match($regex_month,$columns['BQ'],$montharr);//print_r($year);
		$month = explode('月', $montharr[0]);
		if($month[0]!=''){
			$dta_ym_1=$year.'-'.$month[0];
			
		}else{
			$dta_ym_1=$year;
		}

		//确认数据dta_vartype的标识
		if(strpos($columns['BQ'],'汉钢')!==false){
			$dta_vartype_1='SGHG';
		}else if(strpos($columns['BQ'],'龙钢')!==false){
			$dta_vartype_1='SGLG';
		}else{
			$dta_vartype_1='SGQT';
		}


		}else if($rows==5){
		$dat_1_1=$columns['B'];
		$dat_1_2=$columns['BQ'];
		$dat_1_3=$columns['BR'];
		$dat_1_4=$columns['BS'];
		}else{
		$dat_1_1=$columns['B'];
		$dat_1_2=sprintf("%.2f",round($columns['BQ'],2));
		$dat_1_3=sprintf("%.2f",round($columns['BR'],2));
		$dat_1_4=sprintf("%.2f",round($columns['BS'],2));
		}



		
		$values_hg_10.="('$dat_1_1','$dat_1_2','$dat_1_3','$dat_1_4','SGYJYJSYB','$dta_vartype_1','$dta_ym_1',NOW(),'".$user_infos['Uid']."','$year'),";
	}
	//汉钢11月份
	foreach($data as $rows=>$columns)
	{
		if($rows==4){
		$dat_1_1=$columns['B'];
		$dat_1_2=$columns['BT'];
		$dat_1_3=$columns['BT'];
		$dat_1_4=$columns['BT'];
		//确认数据年份月份
		$regex_month="'\d{1,2}月'is";
		preg_match($regex_month,$columns['BT'],$montharr);//print_r($year);
		$month = explode('月', $montharr[0]);
		if($month[0]!=''){
			$dta_ym_1=$year.'-'.$month[0];
			
		}else{
			$dta_ym_1=$year;
		}

		//确认数据dta_vartype的标识
		if(strpos($columns['BT'],'汉钢')!==false){
			$dta_vartype_1='SGHG';
		}else if(strpos($columns['BT'],'龙钢')!==false){
			$dta_vartype_1='SGLG';
		}else{
			$dta_vartype_1='SGQT';
		}


		}else if($rows==5){
		$dat_1_1=$columns['B'];
		$dat_1_2=$columns['BT'];
		$dat_1_3=$columns['BU'];
		$dat_1_4=$columns['BV'];
		}else{
		$dat_1_1=$columns['B'];
		$dat_1_2=sprintf("%.2f",round($columns['BT'],2));
		$dat_1_3=sprintf("%.2f",round($columns['BU'],2));
		$dat_1_4=sprintf("%.2f",round($columns['BV'],2));
		}



		
		$values_hg_11.="('$dat_1_1','$dat_1_2','$dat_1_3','$dat_1_4','SGYJYJSYB','$dta_vartype_1','$dta_ym_1',NOW(),'".$user_infos['Uid']."','$year'),";
	}
	//汉钢12月份
	foreach($data as $rows=>$columns)
	{
		if($rows==4){
		$dat_1_1=$columns['B'];
		$dat_1_2=$columns['BW'];
		$dat_1_3=$columns['BW'];
		$dat_1_4=$columns['BW'];
		//确认数据年份月份
		$regex_month="'\d{1,2}月'is";
		preg_match($regex_month,$columns['BW'],$montharr);//print_r($year);
		$month = explode('月', $montharr[0]);
		if($month[0]!=''){
			$dta_ym_1=$year.'-'.$month[0];
			
		}else{
			$dta_ym_1=$year;
		}

		//确认数据dta_vartype的标识
		if(strpos($columns['BW'],'汉钢')!==false){
			$dta_vartype_1='SGHG';
		}else if(strpos($columns['BW'],'龙钢')!==false){
			$dta_vartype_1='SGLG';
		}else{
			$dta_vartype_1='SGQT';
		}


		}else if($rows==5){
		$dat_1_1=$columns['B'];
		$dat_1_2=$columns['BW'];
		$dat_1_3=$columns['BX'];
		$dat_1_4=$columns['BY'];
		}else{
		$dat_1_1=$columns['B'];
		$dat_1_2=sprintf("%.2f",round($columns['BW'],2));
		$dat_1_3=sprintf("%.2f",round($columns['BX'],2));
		$dat_1_4=sprintf("%.2f",round($columns['BY'],2));
		}



		
		$values_hg_12.="('$dat_1_1','$dat_1_2','$dat_1_3','$dat_1_4','SGYJYJSYB','$dta_vartype_1','$dta_ym_1',NOW(),'".$user_infos['Uid']."','$year'),";
	}
	//汉钢当年
	foreach($data as $rows=>$columns)
	{
		if($rows==4){
		$dat_1_1=$columns['B'];
		$dat_1_2=$columns['BZ'];
		$dat_1_3=$columns['BZ'];
		$dat_1_4=$columns['BZ'];
		//确认数据年份月份
		$regex_month="'\d{1,2}月'is";
		preg_match($regex_month,$columns['BZ'],$montharr);//print_r($year);
		$month = explode('月', $montharr[0]);
		if($month[0]!=''){
			$dta_ym_1=$year.'-'.$month[0];
			
		}else{
			$dta_ym_1=$year;
		}

		//确认数据dta_vartype的标识
		if(strpos($columns['BZ'],'汉钢')!==false){
			$dta_vartype_1='SGHG';
		}else if(strpos($columns['BZ'],'龙钢')!==false){
			$dta_vartype_1='SGLG';
		}else{
			$dta_vartype_1='SGQT';
		}


		}else if($rows==5){
		$dat_1_1=$columns['B'];
		$dat_1_2=$columns['BZ'];
		$dat_1_3=$columns['CA'];
		$dat_1_4=$columns['CB'];
		}else{
		$dat_1_1=$columns['B'];
		$dat_1_2=sprintf("%.2f",round($columns['BZ'],2));
		$dat_1_3=sprintf("%.2f",round($columns['CA'],2));
		$dat_1_4=sprintf("%.2f",round($columns['CB'],2));
		}



		
		$values_hg_13.="('$dat_1_1','$dat_1_2','$dat_1_3','$dat_1_4','SGYJYJSYB','$dta_vartype_1','$dta_ym_1',NOW(),'".$user_infos['Uid']."','$year'),";
	}
	//委托加工等（当月）
	foreach($data as $rows=>$columns)
	{
		if($rows==4){
		$dat_1_1=$columns['B'];
		$dat_1_2=$columns['CC'];
		$dat_1_3=$columns['CC'];
		$dat_1_4=$columns['CC'];
		//确认数据年份月份
		$regex_month="'\d{1,2}月'is";
		preg_match($regex_month,$columns['CC'],$montharr);//print_r($year);
		$month = explode('月', $montharr[0]);
		if($month[0]!=''){
			$dta_ym_1=$year.'-'.$month[0];
			
		}else{
			$dta_ym_1=$year;
		}
		
		$dta_vartype_1='SGDYWTJG';
		
		}else if($rows==5){
		$dat_1_1=$columns['B'];
		$dat_1_2=$columns['CC'];
		$dat_1_3=$columns['CD'];
		$dat_1_4=$columns['CE'];
		}else{
		$dat_1_1=$columns['B'];
		$dat_1_2=sprintf("%.2f",round($columns['CC'],2));
		$dat_1_3=sprintf("%.2f",round($columns['CD'],2));
		$dat_1_4=sprintf("%.2f",round($columns['CE'],2));
		}



		
		$values_qt_1.="('$dat_1_1','$dat_1_2','$dat_1_3','$dat_1_4','SGYJYJSYB','$dta_vartype_1','$dta_ym_1',NOW(),'".$user_infos['Uid']."','$year'),";
	}
	//10月累计
	foreach($data as $rows=>$columns)
	{
		if($rows==4){
		$dat_1_1=$columns['B'];
		$dat_1_2=$columns['CF'];
		$dat_1_3=$columns['CF'];

		//确认数据年份月份
		$regex_month="'\d{1,2}月'is";
		preg_match($regex_month,$columns['CF'],$montharr);//print_r($year);
		$month = explode('月', $montharr[0]);
		if($month[0]!=''){
			$dta_ym_1=$year.'-'.$month[0];
			
		}else{
			$dta_ym_1=$year;
		}

		$dta_vartype_1='SGSYlJ';

		}else if($rows==5){
		$dat_1_1=$columns['B'];
		$dat_1_2=$columns['CF'];
		$dat_1_3=$columns['CG'];
		
		}else{
		$dat_1_1=$columns['B'];
		$dat_1_2=sprintf("%.2f",round($columns['CF'],2));
		$dat_1_3=sprintf("%.2f",round($columns['CG'],2));

		}



		
		$values_qt_2.="('$dat_1_1','$dat_1_2','$dat_1_3','$dat_1_4','SGYJYJSYB','$dta_vartype_1','$dta_ym_1',NOW(),'".$user_infos['Uid']."','$year'),";
	}
	//委托加工等（当年）
	foreach($data as $rows=>$columns)
	{
		if($rows==4){
		$dat_1_1=$columns['B'];
		$dat_1_2=$columns['CH'];
		$dat_1_3=$columns['CH'];
		$dat_1_4=$columns['CH'];
		//确认数据年份月份
		$regex_month="'\d{1,2}月'is";
		preg_match($regex_month,$columns['CH'],$montharr);//print_r($year);
		$month = explode('月', $montharr[0]);
		if($month[0]!=''){
			$dta_ym_1=$year.'-'.$month[0];
			
		}else{
			$dta_ym_1=$year;
		}
		
		$dta_vartype_1='SGDNWTJG';
		
		}else if($rows==5){
		$dat_1_1=$columns['B'];
		$dat_1_2=$columns['CH'];
		$dat_1_3=$columns['CI'];
		$dat_1_4=$columns['CJ'];
		}else{
		$dat_1_1=$columns['B'];
		$dat_1_2=sprintf("%.2f",round($columns['CH'],2));
		$dat_1_3=sprintf("%.2f",round($columns['CI'],2));
		$dat_1_4=sprintf("%.2f",round($columns['CJ'],2));
		}	
		$values_qt_3.="('$dat_1_1','$dat_1_2','$dat_1_3','$dat_1_4','SGYJYJSYB','$dta_vartype_1','$dta_ym_1',NOW(),'".$user_infos['Uid']."','$year'),";
	}

	//当月合计
	foreach($data as $rows=>$columns)
	{
		if($rows==4){
		$dat_1_1=$columns['B'];
		$dat_1_2=$columns['CK'];
		$dat_1_3=$columns['CK'];
		$dat_1_4=$columns['CK'];
		//确认数据年份月份
		$regex_month="'\d{1,2}月'is";
		preg_match($regex_month,$columns['CK'],$montharr);//print_r($year);
		$month = explode('月', $montharr[0]);
		if($month[0]!=''){
			$dta_ym_1=$year.'-'.$month[0];
			
		}else{
			$dta_ym_1=$year;
		}
		
		$dta_vartype_1='SGDNHJ';
		
		}else if($rows==5){
		$dat_1_1=$columns['B'];
		$dat_1_2=$columns['CK'];
		$dat_1_3=$columns['CL'];
		$dat_1_4=$columns['CM'];
		}else{
		$dat_1_1=$columns['B'];
		$dat_1_2=sprintf("%.2f",round($columns['CK'],2));
		$dat_1_3=sprintf("%.2f",round($columns['CL'],2));
		$dat_1_4=sprintf("%.2f",round($columns['CM'],2));
		}	
		$values_qt_4.="('$dat_1_1','$dat_1_2','$dat_1_3','$dat_1_4','SGYJYJSYB','$dta_vartype_1','$dta_ym_1',NOW(),'".$user_infos['Uid']."','$year'),";
	}
	//当年累计
	foreach($data as $rows=>$columns)
	{
		if($rows==4){
		$dat_1_1=$columns['B'];
		$dat_1_2=$columns['CN'];
		$dat_1_3=$columns['CN'];
		$dat_1_4=$columns['CN'];
		//确认数据年份月份
		$regex_month="'\d{1,2}月'is";
		preg_match($regex_month,$columns['CN'],$montharr);//print_r($year);
		$month = explode('月', $montharr[0]);
		if($month[0]!=''){
			$dta_ym_1=$year.'-'.$month[0];
			
		}else{
			$dta_ym_1=$year;
		}
		
		$dta_vartype_1='SGDNlJ';
		
		}else if($rows==5){
		$dat_1_1=$columns['B'];
		$dat_1_2=$columns['CN'];
		$dat_1_3=$columns['CO'];
		$dat_1_4=$columns['CP'];
		}else{
		$dat_1_1=$columns['B'];
		$dat_1_2=sprintf("%.2f",round($columns['CN'],2));
		$dat_1_3=sprintf("%.2f",round($columns['CO'],2));
		$dat_1_4=sprintf("%.2f",round($columns['CP'],2));
		}	
		$values_qt_5.="('$dat_1_1','$dat_1_2','$dat_1_3','$dat_1_4','SGYJYJSYB','$dta_vartype_1','$dta_ym_1',NOW(),'".$user_infos['Uid']."','$year'),";
	}
		$allsql=$values1.$values2.$values3.$values4.$values5.$values6.$values7.$values8.$values9.$values10.$values11.$values12.$values13.
		        $values_hg_1.$values_hg_2.$values_hg_3.$values_hg_4.$values_hg_5.$values_hg_6.$values_hg_7.$values_hg_8.$values_hg_9.$values_hg_10.$values_hg_11.$values_hg_12.$values_hg_13.		
				$values_qt_1.$values_qt_2.$values_qt_3.$values_qt_4.$values_qt_5;
		$tsql = substr($allsql, 0, -1);
		$this->_dao->execute($basesql.$tsql);

					

	     
			      
				}
				if(!$flag)
				{
					$error1 = implode(',', $error_sheet1);
					$error2 = implode(',', $error_sheet2);
					if(!empty($error1))
					{
						$msg1 = "工作表：".iconv("utf-8","gb2312//IGNORE", $error1)."的B3单元格日期有误，导入失败";
					}
					if(!empty($error2))
					{
						$msg2 = "工作表：".iconv("utf-8","gb2312//IGNORE",$error2)."出现未知错误，导入失败";
					}
					$response["Success"] = 0;
					$response['Message'] = $msg1."\\n".$msg2;
				}else{
					$response["Success"] = 1;
					$response['Message'] = '导入成功';
					$sheetname = substr($sheetname, 0, -1);
					$sql ="insert into sg_data_table_log(uptype,filepath,filename,sheetname,createtime,createuser,uptypedate) values('13','$file','$filename','$sheetname',now(),'".$user_infos['Uid']."','".$year."')";
					$this->_dao->execute($sql);
				}
				unlink($file);
			}
		}
		echo $this->pri_JSON($response);
		exit;
	}






	//php获取中文字符拼音首字母
	function getFirstCharter($str){
		if(empty($str)){
				return '';
		}
		$fchar = ord($str[0]);
		if($fchar >= ord('A') && $fchar <= ord('z')){
				return strtoupper($str[0]);
		}
		$s1=iconv('UTF-8','gb2312',$str);
		$s2=iconv('gb2312','UTF-8',$s1);
		$s=$s2==$str?$s1:$str;
		$asc=ord($s[0])*256+ord($s[1])-65536;
		
		if($asc>=-20319&&$asc<=-20284) return 'A';
		if($asc>=-20283&&$asc<=-19776) return 'B';
		if($asc>=-19775&&$asc<=-19219) return 'C';
		if($asc>=-19218&&$asc<=-18711) return 'D';
		if($asc>=-18710&&$asc<=-18527) return 'E';
		if($asc>=-18526&&$asc<=-18240) return 'F';
		if($asc>=-18239&&$asc<=-17923) return 'G';
		if($asc>=-17922&&$asc<=-17418) return 'H';
		if($asc>=-17417&&$asc<=-16475) return 'J';
		if($asc>=-16474&&$asc<=-16213) return 'K';
		if($asc>=-16212&&$asc<=-15641) return 'L';
		if($asc>=-15640&&$asc<=-15166) return 'M';
		if($asc>=-15165&&$asc<=-14923) return 'N';
		if($asc>=-14922&&$asc<=-14915) return 'O';
		if($asc>=-14914&&$asc<=-14631) return 'P';
		if($asc>=-14630&&$asc<=-14150) return 'Q';
		if($asc>=-14149&&$asc<=-14091) return 'R';
		if($asc>=-14090&&$asc<=-13319) return 'S';
		if($asc>=-13318&&$asc<=-12839) return 'T';
		if($asc>=-12838&&$asc<=-12557) return 'W';
		if($asc>=-12556&&$asc<=-11848) return 'X';
		if($asc>=-11847&&$asc<=-11056) return 'Y';
		if($asc>=-11055&&$asc<=-10247) return 'Z';
		return '其他';
	}



	function uploadFileAll($params){
		$upfile = $_FILES['file'];
		$GUID = $params['GUID'];
		$mode = $params['mode'];
		$response = array(
			'Success'  => 1,
			'Message'  => '',
			'GUID'     => $GUID,
			'mode'     => $mode,
		);
		// 不同页面上传类型控制
		switch($params['filetype'])
		{
			case 'excel':
				$uptypes=array(
					'application/vnd.ms-excel',
					'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
				);
				$filetype = "Excel文件";
			break;
			case 'word':
				$uptypes=array(
					'application/msword',
					'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
				);
				$filetype = "Word文件";
			break;
			default:
				$uptypes=array(
					'application/vnd.ms-excel',
					'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
				);
				$filetype = "Excel文件";
			break;
		}

		if($GUID==""){
			$response['Success'] = 0;
			$response['Message'] = 'GUID不能为空';
		}
		if($response['Success']){
			$user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
			if(empty($user_infos)){
				$response['Success'] = 0;
				$response['Message'] = '用户不存在';
			}
			if($response['Success']){
				if ($upfile['error']==0)
				{
					if (is_uploaded_file($upfile['tmp_name'])) {
						if (in_array($upfile['type'], $uptypes)) {
							$extName = strtolower(end(explode('.', $upfile ['name'])));
							$filename = $this->random(5); // 设置随机数长度
							$extName=strtolower(end(explode('.', $upfile['name']))); // 取得扩展名
							$name1=date('YmdHis').".".$extName;
							$dir = SGUPLOADFILE;
							if (!file_exists($dir)) {
								if (mkdir($dir, 0777)) {
									$dest =$dir."/".$filename.$name1;
									if (move_uploaded_file($upfile ['tmp_name'], $dest)) {
										$response['Success'] = 1;
										$response['Message'] = '上传成功';
										$sheets = $this->readSheet2($dest); //获取工作表列表
										$response['Result'] = $this->array_iconv($sheets);
										$response['File'] = $filename.$name1;
										$response['Date'] = $params['date'];
										// $this->drkckb($dest, $params['date']);
									} else {
										$response['Success'] = 0;
										$response['Message'] = '上传失败，目录权限不足';
									}
								} else {
									$response['Success'] = 0;
									$response['Message'] = '目录不存在，上传失败';
								}
							} else {
								$dest = $dir."/".$filename.$name1;
								if (move_uploaded_file($upfile ['tmp_name'], $dest)) {
									$response['Success'] = 1;
									$response['Message'] = '上传成功';
									$sheets = $this->readSheet2($dest); //获取工作表列表
									$response['Result'] = $this->array_iconv($sheets);
									$response['File'] = $filename.$name1;
									$response['Date'] = $params['date'];
									// $this->drkckb($dest, $params['date']);
								} else {
									$response['Success'] = 0;
									$response['Message'] = '上传失败，目录权限不足';
								}
							}
						} else {
							$response['Success'] = 0;
							$response['Message'] = '上传失败，检查文件是否是'.$filetype;
						}
					} else {
						$response['Success'] = 0;
						$response['Message'] = '上传失败';
						clearstatcache(); //清除文件缓存信息
					}
				}
				else
				{
					$response['Success'] = 0;
					$response['Message'] = '上传失败';
				}
			}
		}
		echo $this->pri_JSON($response);
		exit;
	}

	function readSheet2($file)
	{
		require_once "../PHPExcel/PHPExcel.php";
		$type = pathinfo($file);
		$type = strtolower($type["extension"]);
		if ($type=='xlsx') { 
			$type='Excel2007'; 
		}elseif($type=='xls') { 
			$type = 'Excel5';
		}
		
		$objReader = PHPExcel_IOFactory::createReader($type);//判断使用哪种格式
		$objReader ->setReadDataOnly(true); //只读取数据,会智能忽略所有空白行,这点很重要！！！
		$objPHPExcel = $objReader->load($file); //加载Excel文件
		// $sheetCount = $objPHPExcel->getSheetCount();//获取sheet工作表总个数
		$sheets = $objPHPExcel->getSheetNames();
		return $sheets;
	}


	private function pri_JSON($array)
    {
        $this->pri_arrayRecursive($array, 'urlencode', true);
        $json = json_encode($array);
        return iconv("GB2312", "UTF-8", urldecode($json));
    }

    private function pri_arrayRecursive(&$array, $function, $apply_to_keys_also = false)
    {
        static $recursive_counter = 0;
        if (++$recursive_counter > 1000) {
            die('possible deep recursion attack');
        }
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $this->pri_arrayRecursive($array[$key], $function, $apply_to_keys_also);
            } else {
                $array[$key] = $function($value);
            }
            if ($apply_to_keys_also && is_string($key)) {
                $new_key = $function($key);
                if ($new_key != $key) {
                    $array[$new_key] = $array[$key];
                    unset($array[$key]);
                }
            }
        }
        $recursive_counter--;
	}
	
	//数组转码
    public function array_iconv($str, $in_charset="utf-8", $out_charset="gb2312")
    {
        if (is_array($str)) {
            foreach ($str as $k => $v) {
                $str[$k] = $this->array_iconv($v);
            }
            return $str;
        } else {
            if (is_string($str)) {
                return mb_convert_encoding($str, $out_charset, $in_charset);
            } else {
                return $str;
            }
        }
    }
}

?>