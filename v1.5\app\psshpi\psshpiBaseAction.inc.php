<?php 
include_once( FRAME_LIB_DIR ."/action/AbstractAction.inc.php" );
class  psshpiBaseAction extends AbstractAction
{
    public function __construct()
    {
        parent::__construct();
    } 
	
	
 	function pri_JSON($array) {   
		$this->pri_arrayRecursive($array, 'urlencode', true);      
		$json = json_encode($array);  
		return iconv("GB2312","UTF-8",urldecode($json));
	}

    function pri_arrayRecursive(&$array, $function, $apply_to_keys_also = false)
	{
		static $recursive_counter = 0;
		if (++$recursive_counter > 1000) {
			die('possible deep recursion attack');
		}
		foreach ($array as $key => $value) {
			if (is_array($value)) {
				$this->pri_arrayRecursive($array[$key], $function, $apply_to_keys_also);
			} else {
				$array[$key] = $function($value);
				//$array[$key] = $function(addslashes($value));				
			}
			if ($apply_to_keys_also && is_string($key)) {
				$new_key = $function($key);
				if ($new_key != $key) {
					$array[$new_key] = $array[$key];
					unset($array[$key]);
				}
			}
		}
		$recursive_counter--;
	}

    //add by hezpeng started 2018/07/04 echarts
	function create_echarts($params){
		//echo "<pre>";print_R($params);exit;
		$params = $this->convert_to_utf8($params);
		$width = 670;//图宽
		$height = 337;//图高
		//echo "<pre>";print_R($params);exit;
		$arr_dataP = $params['data'];
		$ps_basedata = $params['ps_basedata'];
		$title = $params['title'];
		$datasource = $params['datasource'];
		$count = $params['count'];
		$batu2 = $params['batu2'];
		$mode = $params['mode'];
		$bg_img = $params['bg_img'];
		
		$xLables = $params['xlable'];
		$arr_aquire = $params['arr_aquire'];
		
		$left_tmp = $this->convert_to_utf8("(左)");
		$right_tmp = $this->convert_to_utf8("(右)");

		for($i =0 ; $i< count($arr_aquire); $i++) {
			//处理图表类型
			$tname[$i] = 'line';
			//各个数据名称
			$lendtitle[$i] = $arr_aquire[$i];
			$lendtitle[$i] = $this->get_batu($arr_aquire[$i],"end");

		}
        //echo "<pre>";print_R($arr_dataP);
        $arr_dataP = array_values($arr_dataP);
		//echo "<pre>";print_R($arr_dataP);
		foreach($arr_dataP as $aakk=>$aavv){
			foreach($aavv as $tmp){
				$arr_data[$aakk][] = $tmp;
			}
		}
		//自定义Y轴最大值最小值
		$maxd=array();
        $mind=array();
        //print_r($arr_data);
        foreach($arr_data as $kk=>$vv)
        {
            $maxd[]=max($vv);
			$mind[]=min($vv);
        }
        $yScaleMin = min($mind);
        //$yScaleMax = round(max($maxd)/8)*8;
        $yScaleMax = max($maxd);

		$isY2 = 0;
		
        //echo $yScaleMin."##".$yScaleMax;
		$y_arr = $this->solve_data($yScaleMax,$yScaleMin);
		$yScaleMax = $y_arr['max'];
		$yScaleMin = $y_arr['min'];



		$linecolor=array("#4E81BD","#C1504C","#9BBB58","#8166A3","418EBD","#1006BD","#FFC101","#A104C1","#FF0000");
		$mycolor='color:["'.implode('","',$linecolor).'"],';

		$ldw = $ps_basedata['CCurry'];//单位
		$rdw = $ps_basedata['CCurry'];//单位
		$count = count($arr_data);
        //echo $count;
		if(($sum=$count/5) >1 ){
			$splitNumbers = 5;
		}else if( ($sum=$count/10)>1 ){
			$splitNumbers = 10;
		}else if( $count < 5 ){
            $splitNumbers = 5;
        }

		//echarts生成图片
		$jsarr="var right_tmp=\"".$right_tmp."\";\n
				var left_tmp=\"".$left_tmp."\";\n
				var isY2=".$isY2.";\n
				var youzhou=\"".$arr_aquire[0].",".$arr_aquire[1].",".$arr_aquire[2].",".$arr_aquire[3]."\".split(',');
		";
		$series="";
		$formatter=',formatter: function(params){
						var len=params.length;
						var showstr=params[0].name+"<br>";
						for(var i=0;i<len;i++){
							sname=params[i].seriesName;
							
							showstr+=sname+" : "+params[i].value+"<br>";
						}
						return showstr
					}';
		$grid=',grid:{
					width:myChart.width,
					height:myChart.height,
				
				}';
		$xAxis=",xAxis : [
					{
						type : 'category',
						//splitNumber: 7,
						// x轴的字体样式
                        axisLabel: {        
                                show: true,
                                textStyle: {
                                    color: '#000',
                                    fontFamily: 'SimHei',
                                }
                        },
						splitLine:false,
						boundaryGap: true,
						axisLine: {lineStyle: {color:'#000',width:1}},
						<{xdata}>
					}
				]";
		$yAxis=",yAxis : [
					{
						name:'<{ldw}>',
						type : 'value',
						scale: true,
						minInterval: 1,
						boundaryGap : [ 0, 0.1 ],
						splitNumber:".$splitNumbers.",
						// y轴的字体样式
						axisLabel: {                   
							formatter: function (value, index) {
								var val=value*1.0;
								val=val.toFixed(2);
								return val;
							},
							show: true,
                            textStyle: {
                                color: '#000',
                                fontFamily: 'SimHei',
                            }
						},

						axisLine: {lineStyle: {color:'#000',width:1}}".
						($yScaleMin==null?",min:0":",min:<{yScaleMin}>").($yScaleMax==null?"":",max:<{yScaleMax}>")
					."}
				]";
		
		
		$legendtitle=implode("','",$lendtitle);
        $rand=rand(0,1000);
        
		//$modeltext=file_get_contents("http://iwww2.steelhome.cn/marketdata/echarts-2.2.7/model.html?v=".$rand);
        $modeltext=file_get_contents(DC_URL.DCURL."/../echarts-2.2.7/model3.html?v=".$rand);
        //echo "<pre>";
        //print_r($arr_dataP);
        //print_r($xLables);
		for($i=0;$i<count($arr_aquire);$i++){
				
				$chartname='"name":"'.$lendtitle[$i].'"';
				$charttype='"type":"'.$tname[$i].'"';
				$yAxisIndex=",yAxisIndex: 0";
				$showSymbol='symbol:"none",';
				if($tname[$i]=="line") $zlevel=",zlevel:1";
				else $zlevel=",zlevel:0";

				
                $xlabel='data:["'.implode('","',$xLables).'"]';
                $chartdata = array();
                foreach($xLables as $tmp){
                    
                    //echo $tmp;
                    //print_r($arr_dataP);
                    $chartdata[] = $arr_dataP[$i][$tmp];
                }
                //print_r($chartdata);
                $chartdata='"data":['.implode(',',$chartdata).']';
                $series[]="{".$axisTick.$showSymbol.$splitNumber.$chartname.",".$charttype.$yAxisIndex.$stack.$radius.",".$chartdata.$chart_fg.$zlevel."}";
				//break;
		}
        //echo $xlabel;

        $checkbox_symbol = '<form action="ps.php?view=echarts&GUID='.$params['GUID'].'&Symbol='.$params['Symbol'].'" method="post">';
        foreach($batu2 as $tmp){
            if(in_array($tmp,$arr_aquire)){
                $checkbox_symbol .='<input name="batu[]" value="'.$tmp.'" type="checkbox" checked>'.$this->get_batu($tmp,"end").'&nbsp;&nbsp;';
            }else{
                $checkbox_symbol .='<input name="batu[]" value="'.$tmp.'" type="checkbox" >'.$this->get_batu($tmp,"end").'&nbsp;&nbsp;';
            }
        }
        $chaxun = $this->convert_to_utf8("查看");
        $checkbox_symbol .= '<input type="submit" value="'.$chaxun.'"></form>';
        $checkbox_symbol = "";

        $titlestyle="";
		if($mode!="1"){
			$rowmaxlen=28;
			$bigtitle=$this->StrSplit2Row($bigtitle,$rowmaxlen);
			$titlestyle="textStyle:{color:'black',fontWeight:'normal',fontSize:25,fontFamily:'SimHei'},";
			if(strlen($bigtitle)>$rowmaxlen) $titlechange=1;
		}else{
			$titlestyle="textStyle:{color:'black',fontWeight:'normal',fontFamily:'SimHei'},";
		}

		$myseries=implode(",",$series);
		$tip_trigger="axis";
		
		$chart_y=40+16*count($lendtitle)+(count($lendtitle)==4?0:15);
		$rand=rand(0,1000);
		//echo "rand:".$rand."</br>";exit;
//echo $modeltext;
		//$modeltext=str_replace('<{fuzhi}>',$fuzhi,$modeltext);	//背景图
		$modeltext=str_replace('<{checkbox_symbol}>',$checkbox_symbol,$modeltext);	//勾选
		$modeltext=str_replace('<{background_img}>',$bg_img,$modeltext);	//背景图
		$modeltext=str_replace('<{jsarr}>',$jsarr,$modeltext);	//js数组，用于左右轴的
		$modeltext=str_replace('<{title}>',$title,$modeltext);//主标题
        $modeltext=str_replace('<{titlestyle}>',$titlestyle,$modeltext);//标题样式
		$modeltext=str_replace('<{subtitle}>',$datasource,$modeltext);				//子标题
		$modeltext=str_replace('<{tip_trigger}>',$tip_trigger,$modeltext);
		$modeltext=str_replace('<{formatter}>',$formatter,$modeltext);		//标签样式，自定义
		$modeltext=str_replace('<{legend_data}>',$legendtitle,$modeltext);	//四个标题
		//$modeltext=str_replace('<{legend_x}>',$legend_x,$modeltext);		//标题x轴位置
		$modeltext=str_replace('<{grid}>',$grid,$modeltext);				//坐标系位置
		$modeltext=str_replace('<{chart_y}>',$chart_y,$modeltext);			//用来控制坐标轴位置
		$modeltext=str_replace('<{xAxis}>',$xAxis,$modeltext);				//X轴设定
		$modeltext=str_replace('<{yAxis}>',$yAxis,$modeltext);				//Y轴设定
		$modeltext=str_replace('<{mycolor}>',$mycolor,$modeltext);			//自定义颜色
		$modeltext=str_replace('<{echarts_types}>',$ImageType,$modeltext);	//图形类型设定
		$modeltext=str_replace('<{xdata}>',$xlabel,$modeltext);				//日期
		$modeltext=str_replace('<{ldw}>',$ldw,$modeltext);					//左轴单位
		$modeltext=str_replace('<{yScaleMin}>',$yScaleMin,$modeltext);				//y轴(左)最小值
		$modeltext=str_replace('<{yScaleMax}>',$yScaleMax,$modeltext);				//y轴(左)最大值
		$modeltext=str_replace('<{y2ScaleMin}>',$y2ScaleMin,$modeltext);			//y轴(左)最小值
		$modeltext=str_replace('<{y2ScaleMax}>',$y2ScaleMax,$modeltext);			//y轴(左)最大值
		$modeltext=str_replace('<{other_yaxis}>',$other_yaxis,$modeltext);		//双轴的右轴
		$modeltext=str_replace('<{series}>',$myseries,$modeltext);			//数值
		$modeltext=str_replace('<{data_source}>',$datasource,$modeltext);	//数据来源

        

		echo "$modeltext";exit;
	}
	protected function solve_data1($dmax,$dmin){
		
	}
	
	//处理y轴最大最小值
	protected function solve_data($dmax,$dmin){
		if((10*($dmax-$dmin))>$dmax){		
			if($dmin>0){
				$mina = 0.98*$dmin;
			}else{
				$mina = 1.02*$dmin;
			}
			if($dmax>0){
				$maxa = 1.02*$dmax;
			}else{
				$maxa = 0.98*$dmax;
			}
		}else{
			$i=(float)$dmax;       // 线图上下限问题 2016/9/19
			$j=(float)$dmin;
			$k=$i-$j;
			if($i-1>0&&$j-1>0){
				$min=(int)$j-(int)($k/10.0);
				$max=(int)$i+(int)($k/10.0);
				$mina=$min-1.0;
				$maxa=$max+1.0;
			}else{								// 注：对小于一的浮点数据重新处理，上下浮动五分之一，然后保留几位有效小数 
				$min=(float)$j-(float)($k/10.0);
				$max=(float)$i+(float)($k/10.0);
				$mina=round($min/1.0,$this->getwei($min));
				$maxa=round($max/1.0,$this->getwei($max));
			}
																					
			if($j>=0&&$mina<0) $mina=0;
			if($i<=0&&$maxa>0) $maxa=0;
		}
		if(abs($maxa-$mina)>=10000){
			$mina=(int)$mina;
			$maxa=(int)$maxa;
			$mina-=abs($mina%1000);
			$maxa+=1000-abs($maxa%1000);
		}elseif(abs($maxa-$mina)>=1000){
			$mina=(int)$mina;
			$maxa=(int)$maxa;
			$mina-=abs($mina%100);
			$maxa+=100-abs($maxa%100);
		}elseif(abs($maxa-$mina)>=100){
			$mina=(int)$mina;
			$maxa=(int)$maxa;
			$mina-=abs($mina%10);
			$maxa+=10-abs($maxa%10);
		}elseif(abs($maxa-$mina)>=10){
			$mina=(int)$mina;
			$maxa=(int)$maxa;
			$mina-=abs($mina%3);
			$maxa+=2-abs($maxa%2);
		}elseif(abs($maxa-$mina)>=1){
			$mina=round($mina,1);
			$maxa=round($maxa,1);
		}elseif(abs($maxa-$mina)<1 ){
            //$mina = "-2";
            //$maxa = "0";
            $mina=round($mina,1)-1;
			$maxa=round($maxa,1)+1;
        }

		if(abs($maxa-$mina)<1){
			$mina="null";
			$maxa="null";
		}
		return array("min"=>$mina,"max"=>$maxa);
	}
	
	protected function convert_to_utf8($params,$array=null){
		if(count($array)==0){
			if(is_array($params)&&count($params)>0) {
				foreach($params as $k=>$v){
					$params[$k]=$this->convert_to_utf8($v);
				}
				return $params;
			}else{
				return iconv("GBK","UTF-8",$params);
			}
		}else{
			foreach($params as $key=>$value){
				if(array_key_exists($array,$key)) $params[$key]=$this->convert_to_utf8($value);
			}
		}
	}
    function strconvert($data,$code='UTF-8'){
		if( !empty($data) ){
			$fileType = mb_detect_encoding($data , array('UTF-8','GBK','LATIN1','BIG5')) ;
			if( $fileType != $code){
				$data = mb_convert_encoding($data ,$code , $fileType);
			}
		}
		return $data;
	}
	protected function getwei($str) //add by zhangcun 2017/7/19 for 获取小数有效位数
	{
		$wei=0;
		if($str-1.0>0) return $wei;
		for($i=0;$i<10;$i++){
			$str*=10.0;
			$wei++;
			if($str-1.0>0) return $wei;
		}
	}
	protected function getwei2($str)
	{
		$wei=explode(".",$str);
		return strlen($wei[1]);
	}
	protected function get_Sql($DataType,$Start,$End,$dateStr2){
		if($DataType == '5'){
			$start_y = date("Y", strtotime( $Start ) );
			$start_m = ( floor (date("n", strtotime( $Start ) ) / 3) )*3 + 1;
			$sql_Start = formatdate("$start_y-$start_m-01");
			
			$end_y = date("Y", strtotime( $End ) );
			$end_m = ( floor (date("n", strtotime( $End ) ) / 3) )*3 + 1;
			$sql_End = formatdate("$end_y-$end_m-01");
			
			
		}elseif($DataType == '6'){
			$start_y = date("Y", strtotime( $Start ) );
			$start_m = date("m", strtotime( $Start ) );
			
			$end_y = date("Y", strtotime( $End ) );
			$end_m = date("m", strtotime( $End ) );
			
			$s_d = floor (date("j", strtotime( $Start ) ));
			if($s_d <= 20){
				$start_d = ( floor($s_d - 1)/ 10) * 10 + 1;
			}else{
				$start_d = 21;
			}
			
			$e_d = floor (date("j", strtotime( $End ) ));
			if($e_d <= 20){
				$end_d = ( floor($e_d - 1)/ 10) * 10 + 1;
			}else{
				$end_d = 21;
			}

			$sql_Start = formatdate("$start_y-$start_m-$start_d");
			$sql_End = formatdate("$end_y-$end_m-$end_d");
		}else{
			$sql_Start = date($dateStr2,strtotime($Start));
			$sql_End = date($dateStr2,strtotime($End));
		}
		return array("sql_Start"=>$sql_Start,"sql_End"=>$sql_End);
	}
	//add by hezpeng ended 2018/07/04 jpgraph改echarts

	//将标题字符串分割成2行的字符串 zk 
	function StrSplit2Row($str,$len){
		$r = $this->mbStrSplit($str, $len);
		$str2row="";
		$rlen=count($r);
		for($i=0;$i<$rlen;$i++){
			if($i==0)
			{
				if($rlen>1)
					$str2row.= $r[$i]."\\n";
				else
					$str2row.=$r[$i];	
			}
			else  
				$str2row.=$r[$i];	
				 
		}
		return $str2row;
	}
    //分割字符串成数组 zk 
	function mbStrSplit ($string, $len=1) {
		$start = 0;
		$strlen = mb_strlen($string);
		while ($strlen) {
			$array[] = mb_substr($string,$start,$len,"utf8");
			$string = mb_substr($string, $len, $strlen,"utf8");
			$strlen = mb_strlen($string);
		}
		return $array;
	}
	
} 
?>