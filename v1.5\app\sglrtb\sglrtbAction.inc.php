<?php

$GLOBALS['pinzhong'] = array(
	"1"=>"螺纹钢20mmHRB400E",
	"2"=>"高线8mmHPB300",
	"3"=>"盘螺8mmHRB400E"
);
$GLOBALS['zdjsc'] = array(
	'西安',
	'成都',
	'重庆',
	'郑州',
	'兰州',
);
$GLOBALS['MONTH_ARR'] = array(1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31);
class sglrtbAction extends AbstractAction
{
	public function __construct()
	{
    	parent::__construct();
	}

	public function checkSession()
	{
	}

	function vmrkcUpload($params)
	{
		$uptype_name = array(
			'7'=>'废钢生铁采购合同跟踪表(宝轧)录入',
			'8'=>'废钢生铁采购合同跟踪表（韩城公司）录入',
			'9'=>'国内矿合同跟踪表（大西沟矿业贸易）录入',
			'10'=>'国内矿合同跟踪表（自产博隆）录入',
			'16'=>'汉钢成本月报录入',
			'17'=>'龙钢成本月报录入'
		);
		$year = date('Y');
		$month = date('Y-m');
		$this->assign('month', $month);
		$years = array();
		for($i=0;$i<=30;$i++)
		{
			$years[] = $year-$i;
		}
		$this->assign('years', $years);
		$GUID = $params['GUID'];
		$mode = $params['mode'];//1=客户端，2=安卓，3=iOS
		$uptype = $params['uptype'];
		// echo $uptype;exit;
		if($GUID==""){
			alert('GUID不能为空');
            exit;
		}
		$user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
		if(empty($user_infos)){
			alert('用户不存在');
            exit;
		}

		$total = $this->_dao->getOne( "SELECT COUNT(id) as c FROM sg_data_table_log where uptype = '$uptype' and isdel='0'");
		$page = $params['page'] == '' ? 1 : $params['page'];
		$url = "sglrtb.php";
		$per = 20;
		$start = ( $page - 1 ) * $per;
		unset( $params['page'] );
		$pagebar = pagebar( $url, $params, $per, $page, $total );
		$this->assign( "pagebar", $pagebar );
		$sql = "select * from sg_data_table_log where uptype = '$uptype' and isdel='0' order by createtime desc limit $start, $per";
		$log_info = $this->_dao->query($sql);
		$createuser = array();
		foreach( $log_info as $v ){
			if(!in_array($v['createuser'],$createuser)){
				$createuser[] = $v['createuser'];
			}
		}
		$createuser_str =  implode("','",$createuser);
		$sql = "select id,truename from adminuser where id in ('$createuser_str')";
		$adminuser_info = $this->homeDao->query($sql);
		$admin_name = array();
		foreach( $adminuser_info as $v ){
			$admin_name[$v['id']] = $v['truename'];
		}
		// echo"<pre>";print_r($createuser);

		$this->assign('guid', $GUID);
		$this->assign('mode', $mode);
		$this->assign('uptype', $uptype);
		$this->assign('uptype_name', $uptype_name);
		$this->assign('log_info', $log_info);
		$this->assign('admin_name', $admin_name);
	}

	public function random($length,$type)
    {
		
		$hash = 'SG'.$type.'-';
        $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789abcdefghijklmnopqrstuvwxyz';
        $max = strlen($chars) - 1;
        mt_srand(( double ) microtime() * 1000000);
        for ($i = 0; $i < $length; $i ++) {
			$hash .= $chars [mt_rand(0, $max)];
        }
        return $hash;
	}

	function readSheet($file, $year)
	{
		require_once "../PHPExcel/PHPExcel.php";
		$type = pathinfo($file); 
		$type = strtolower($type["extension"]);
		if ($type=='xlsx') { 
			$type='Excel2007'; 
		}elseif($type=='xls') { 
			$type = 'Excel5';
		}
		
		$objReader = PHPExcel_IOFactory::createReader($type);//判断使用哪种格式
		$objReader ->setReadDataOnly(true); //只读取数据,会智能忽略所有空白行,这点很重要！！！
		$objPHPExcel = $objReader->load($file); //加载Excel文件
		// $sheetCount = $objPHPExcel->getSheetCount();//获取sheet工作表总个数
		$sheets = $objPHPExcel->getSheetNames();
		return $sheets;
	}

	function sheet($params)
	{
		ini_set('memory_limit', '512M');
		$GUID = $params['GUID'];
		$uptype = $params['uptype'];
		$mode = $params['mode'];//1=客户端，2=安卓，3=iOS
		$this->assign('mode', $mode);
		$response = array(
			'Success'  => 1,
			'Message'  => '',
			'GUID'     => $GUID,
			'mode'     => $mode,
		);
		if($GUID==""){
			$response['Success'] = 0;
			$response['Message'] = 'GUID不能为空';
		}
		if($response['Success']){
			$year = $params['year'];
			$upfile = $_FILES['file'];
			// if(strpos($upfile["name"], $year)===false)
			// {
			// 	$response['Success'] = 0;
			// 	$response['Message'] = '选取年份与文件数据年份不一致！';
			// }
			$user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
			if(empty($user_infos)){
				// $response['Success'] = 0;
				$response['Message'] = '用户不存在';
			}
			if($response['Success']){
				if ($upfile['error']==0)
				{

					if (is_uploaded_file($upfile['tmp_name'])) {
						$uptypes=array(
							'application/vnd.ms-excel',
							'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
						);
						if (in_array($upfile['type'], $uptypes)) {
							$filename = $this->random(5,$uptype); // 设置随机数长度
							$extName=strtolower(end(explode('.', $upfile['name']))); // 取得扩展名
							$name1=date('YmdHis').".".$extName;
							$dir = SGUPLOADFILE;
							
							if (!file_exists($dir)) {
								if (mkdir($dir, 0777)) {
									$dest =$dir."/".$filename.$name1;
									if (move_uploaded_file($upfile ['tmp_name'], $dest)) {
										if($uptype==7)
										{
											$params = array(
												"tmp" => $filename.$name1,
												"filename" => $upfile["name"],
												"date" => $params['date'],
												"GUID" => $GUID,
												"uptype" => $uptype,
												"mode" => $mode,
												"sheet" => "0",
												"year" => $year,
											);
											$this->dosheet($params);
										}else{
											$response['Success'] = 1;
											$response['Message'] = '上传成功';
											$sheet11 = $this->readSheet($dest);
											$response['Result'] = $sheet11;
											$response['File'] = $filename.$name1;
											$response['Year'] = $year;
											$response['filename'] = iconv("utf-8","gb2312//IGNORE",$upfile["name"]);
										}
									} else {
										$response['Success'] = 0;
										$response['Message'] = '上传失败！目录权限不足';
									}
								} else {
									$response['Success'] = 0;
									$response['Message'] = '目录不存在，上传失败！';
								}
							} else {
								$dest = $dir."/".$filename.$name1;

								if (move_uploaded_file($upfile ['tmp_name'], $dest)) {
									if($uptype==7)
									{
										$params = array(
											"tmp" => $filename.$name1,
											"filename" => $upfile["name"],
											"date" => $params['date'],
											"GUID" => $GUID,
											"uptype" => $uptype,
											"mode" => $mode,
											"sheet" => "0",
											"year" => $year,
										);
										$this->dosheet($params);
									}else{
										$response['Success'] = 1;
										$response['Message'] = '上传成功';
										$sheet11 = $this->readSheet($dest);
										$response['Result'] = $this->array_iconv($sheet11);
										$response['File'] = $filename.$name1;
										$response['Year'] = $year;
										$response['filename'] = iconv("utf-8","gb2312//IGNORE",$upfile["name"]);
									}
								} else {
									$response['Success'] = 0;
									$response['Message'] = '上传失败！目录权限不足';
								}
							}
						} else {
							$response['Success'] = 0;
							$response['Message'] = '上传失败！检查文件是否是Excel文件';
						}
					} else {
						clearstatcache(); //清除文件缓存信息
						$response['Success'] = 0;
						$response['Message'] = '上传失败！';
					}
				}
				else
				{
					alert('上传失败！');
					goURL("sglrtb.php?view=mrkcupload&GUID=".$GUID."&mode=".$mode);
					exit;
				}
			}
		}
		// $response = $this->pri_JSON($response);
		// file_put_contents("/usr/local/www/www.steelhome.cn/data/v1.5/app/sglrtb/lina.txt", "response:".print_R($response,true)."\n",FILE_APPEND);exit;

		echo $this->pri_JSON($response);
		exit;
	}

	function dosheet($params)
	{
		ini_set('memory_limit', '512M');
		$month_arr = $GLOBALS['MONTH_ARR'];
		$GUID = $params['GUID'];
		$mode = $params['mode'];//1=客户端，2=安卓，3=iOS
		$uptype = $params['uptype'];
		$filename = iconv("utf-8","gb2312//IGNORE",$params['filename']);
		$selyear = $params['year'];
		$dta_type_arr = array(
			'7'=>'SG_FGSTCGHTGZB_BZ',
			'8'=>'SG_FGSTCGHTGZB_HC',
			'9'=>'SG_GNKHTGZB_DXG',
			'10'=>'SG_GNKHTGZB_BL'
		);
		$type1 = $dta_type_arr[$uptype];
		$response = array(
			'Success'  => 1,
			'Message'  => '',
			// 'GUID'     => $GUID,
			// 'mode'     => $mode,
		);
		if($GUID==""){
			$response["Success"] = 0;
			$response['Message'] = 'GUID不能为空';
		}
		if($response["Success"])
		{
			$user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
			$user_id = $user_infos['Uid'];
			if(empty($user_infos)){
				$response["Success"] = 0;
				$response['Message'] = '用户不存在';
			}

			$sheetList = explode(',', $params["sheet"]);
			$file = SGUPLOADFILE."/".$params["tmp"];
			if($params["sheet"]=="")
			{
				$response["Success"] = 0;
				$response['Message'] = '未选择工作表';
			}
			$year = mysql_real_escape_string($params["year"]);
			if(empty($year))
			{
				$response["Success"] = 0;
				$response['Message'] = '年份出错';
			}
			if($response["Success"])
			{
				require_once "../PHPExcel/PHPExcel.php";
				$type = pathinfo($file); 
				$type = strtolower($type["extension"]);
				if ($type=='xlsx') { 
					$type='Excel2007'; 
				}elseif($type=='xls') { 
					$type = 'Excel5';
				}
				$objReader = PHPExcel_IOFactory::createReader($type);//判断使用哪种格式
				$objReader ->setReadDataOnly(true); //只读取数据,会智能忽略所有空白行,这点很重要！！！
				$objPHPExcel = $objReader->load($file); //加载Excel文件
				$sheets = $objPHPExcel->getSheetNames();//获取sheet工作表数组
				$sheetCount = $objPHPExcel->getSheetCount();//获取sheet工作表总个数
				$maxdate_arr = array(
					'5'=>'5',
					'4'=>'10',
					'3'=>'15',
					'2'=>'20',
					'1'=>'25',
					'0'=>'31'
				);
				$sheetname = "";

				foreach($sheetList as $sheet_index1)
				{
					$sheet_index = $sheetCount-1-$sheet_index1;
					$sheet = $objPHPExcel->getSheet($sheet_index);
					//获取当前工作表最大行数
					$rows = $sheet->getHighestRow();
					//获取当前工作表最大列数,返回的是最大的列名，如：B 
					// $cols = $sheet->getHighestColumn();
					//将当前工作表名当键，内容为值存入数组
					$sheet_v = iconv("utf-8","gb2312//IGNORE",$sheets[$sheet_index]);
					$sheetname .= $sheet_v.",";

					if( $sheet_v == "年累计" ){
						//获取年份
						// 获取月份
						$flag = true;
						$regex="'\d{4}年'is";
						$title = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell('A1')->getValue()));
						preg_match($regex,$title,$matches);
						if(empty($matches[0]))
						{
							$error_sheet1[] = $sheet_v;
							$flag = false;
							continue;
						}else{
							$year1 = $matches[0];
							$year1 = str_replace("年","",$year1);
						}
						$data = array();
						for($k = 2; $k <= $rows; $k++){
							for($j = 65; $j <= 90; $j++ ){
								$key = chr($j).$k;
								$data[$k][chr($j)] = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue()));
							}
							for($j = 65; $j <= 65; $j++ ){
								$key = "A".chr($j).$k;
								$data[$k]["A".chr($j)] = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue()));
							}
						}

						$month_mark = array(
							'1'=>array('B','C'),
							'2'=>array('D','E'),
							'3'=>array('F','G'),
							'4'=>array('H','I'),
							'5'=>array('J','K'),
							'6'=>array('L','M'),
							'7'=>array('N','O'),
							'8'=>array('P','Q'),
							'9'=>array('R','S'),
							'10'=>array('T','U'),
							'11'=>array('V','W'),
							'12'=>array('X','Y'),
							''=>array('Z','AA')  //合计
						);

						$data_dr = array();
						$t = 0;
						foreach( $month_mark as $k_yue => $v_mark ){
							foreach( $data as $k => $v ){
								if( $k >= 4){
									if( empty($k_yue) ){
										$dta_vartype = 5;
										$dta_ym = $year1;
									}else{
										$dta_vartype = 4;
										$dta_ym = $year1."-".$k_yue;
									}

									$data_dr[$dta_vartype][$dta_ym][$t]['dta_1'] = $v['A'];
									$data_dr[$dta_vartype][$dta_ym][$t]['dta_21'] = $v[$v_mark[0]];
									$data_dr[$dta_vartype][$dta_ym][$t]['dta_22'] = $v[$v_mark[1]];

									$data_dr[$dta_vartype][$dta_ym][$t]['dta_type'] = $type1;
									$data_dr[$dta_vartype][$dta_ym][$t]['dta_vartype'] = $dta_vartype;
									$data_dr[$dta_vartype][$dta_ym][$t]['dta_ym'] = $dta_ym;
									

									// 行号
									$data_dr[$dta_vartype][$dta_ym][$t]['hanghao'] = $k;
									$t += 1;
								}
							}
						}

					}else{

						// 获取月份
						$flag = true;
						$regex="'\d{4}年\d{1,2}月'is";
						$title = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell('A1')->getValue()));
						preg_match($regex,$title,$matches);
						if(empty($matches[0]))
						{
							$error_sheet1[] = $sheet_v;
							$flag = false;
							continue;
						}else{
							$month = $matches[0];
							$month = str_replace("年","-",$month);
							$month = str_replace("月","",$month);
							$month = date("Y-m",strtotime($month));
						}
				
						// $data = $this->array_iconv( $objPHPExcel->getSheet($sheet_index)->toArray() );
						$data = array();
						if( $uptype == "7" || $uptype == "9" || $uptype == "10"){
							for($k = 2; $k <= $rows; $k++){
								for($j = 65; $j <= 90; $j++ ){
									$key = chr($j).$k;
									$data[$k][chr($j)] = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue()));
								}
								for($j = 65; $j <= 90; $j++ ){
									$key = "A".chr($j).$k;
									$data[$k]["A".chr($j)] = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue()));
								}
							}
						}elseif( $uptype == "8" ){
							for($k = 2; $k <= $rows; $k++){
								for($j = 65; $j <= 90; $j++ ){
									$key = chr($j).$k;
									$data[$k][chr($j)] = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue()));
								}
								for($j = 65; $j <= 90; $j++ ){
									$key = "A".chr($j).$k;
									$data[$k]["A".chr($j)] = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue()));
								}
								for($j = 65; $j <= 73; $j++ ){
									$key = "B".chr($j).$k;
									$data[$k]["B".chr($j)] = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue()));
								}
							}
						}
	
						// 获取合并单元格
						foreach( $data as $k => $v ){
							if( empty($v['A']) && $data[$k-1]['A'] != '小计' && strpos($data[$k-1]['A'],'合计') == false && strpos($data[$k-1]['A'],'总计') == false){
								$data[$k]['A'] =$data[$k-1]['A'];
							}
							if( $uptype == "7" ){
								if( empty($v['B']) && $data[$k]['A'] != '小计' && strpos($data[$k]['A'],'合计') == false && strpos($data[$k]['A'],'总计') == false){
									$data[$k]['B'] =$data[$k-1]['B'];
								}
							}
						}
	
						// 找出 列标志 stearted
						$lie_mark = array();
						foreach( $data[2] as $k => $v ){
							if( strpos($v,'合同数量') !== false ){
								$v = "合同数量";
							}elseif( strpos($v,'合同单价') !== false ){
								$v = "合同单价";
							}elseif( strpos($v,'运费') !== false ){
								$v = "运费";
							}elseif( strpos($v,'主业单价') !== false ){
								$v = "主业单价";
							}elseif( strpos($v,'合同完成率') !== false ){
								$v = "合同完成率";
							}elseif( strpos($v,'当日到货量') !== false ){
								$v = "当日到货量";
							}
							if( $v != "" ){
								$lie_mark[$v] = $k;
							}
						}
	
						$mon_mark = array();
						foreach( $data[3] as $k => $v ){
							if( strpos($v,'量') !== false ){
								$mon_mark['liang'][] = $k;
							}elseif( strpos($v,'综合价') !== false ){
								$mon_mark['jia'][] = $k;
							}elseif( $v != "" ){
								$mon_mark[$v][] = $k;
							}
						}
						// 找出 列标志 ended
	
						// echo"<pre>";print_r($mon_mark);
						$last_mon =  date("Y-m",strtotime("-1 month",strtotime($month)));
						$next_mon =  date("Y-m",strtotime("+1 month",strtotime($month)));
						$ascii_10 = $this -> getASCII($mon_mark['10'][0]);
						$newmon_mark = array();
						foreach( $month_arr as $v  ){
							if( $v < 10){
								$v1 = "0".$v;
							}else{
								$v1 = $v;
							}
							if( !empty($mon_mark[$v]) ){
								if( $v >= 20 ){
									if( count($mon_mark[$v],0) == 2 ){
										$newmon_mark[1][$last_mon.'-'.$v1] = $mon_mark[$v][0];
										$newmon_mark[1][$month.'-'.$v1] = $mon_mark[$v][1];
									}elseif( count($mon_mark[$v],0) == 1  ){
										$ascii_now = $this -> getASCII($mon_mark[$v][0]);
										if( $ascii_now < $ascii_10 ){
											$newmon_mark[1][$last_mon.'-'.$v1] = $mon_mark[$v][0];
										}else{
											$newmon_mark[1][$month.'-'.$v1] = $mon_mark[$v][0];
										}
									}
								}elseif( $v < 10 ){
									if( count($mon_mark[$v],0) ==2 ){
										$newmon_mark[1][$next_mon.'-'.$v1] = $mon_mark[$v][1];
									}
									$newmon_mark[1][$month.'-'.$v1] = $mon_mark[$v][0];
								}else{
									$newmon_mark[1][$month.'-'.$v1] = $mon_mark[$v][0];
								}
							}
						}
	
						$newmon_mark[2][$month]['liang'] = $mon_mark['liang'][0];
						$newmon_mark[2][$month]['jia'] = $mon_mark['jia'][0];
						$newmon_mark[3][$month]['liang'] = $mon_mark['liang'][1];
						$newmon_mark[3][$month]['jia'] = $mon_mark['jia'][1];
						// echo"<pre>";print_r($data);exit;
	
						// 获取导入数组
						$data_dr = array();
						$t = 0;
						$bfjghz = array();
						foreach( $data as $k => $v ){
							if( (!empty($v[$lie_mark['单位名称']]) || !empty($v[$lie_mark['合同数量']]) ) && $k >= 4 ){
								foreach( $newmon_mark as $k_dta_vartype => $v_newmon_mark){
									foreach( $v_newmon_mark as $k_date => $v_mark){
										if( ($k_dta_vartype == "2" || $k_dta_vartype == "3") && empty($v_mark['liang']) ){
											continue;
										}
										if( $uptype == "7" ){
											if( strtotime($k_date) > strtotime($month.'-'.$maxdate_arr[$sheet_index1]) ){
												continue;
											}
										}
										$data_dr[$k_dta_vartype][$k_date][$t]['dta_1'] =  $v[$lie_mark['物料名称']];
										$data_dr[$k_dta_vartype][$k_date][$t]['dta_2'] =  $v[$lie_mark['单位名称']];
										if( $uptype == "9" || $uptype == "10" ){
											$data_dr[$k_dta_vartype][$k_date][$t]['dta_3'] =  $v[$lie_mark['主要指标']];
										}else{
											$data_dr[$k_dta_vartype][$k_date][$t]['dta_3'] =  $v[$lie_mark['物料类型']];
										}
										$data_dr[$k_dta_vartype][$k_date][$t]['dta_4'] =  $v[$lie_mark['合同编号']];
										$data_dr[$k_dta_vartype][$k_date][$t]['dta_5'] =  $v[$lie_mark['产地']];
										$data_dr[$k_dta_vartype][$k_date][$t]['dta_6'] =  $v[$lie_mark['运输方式']];
										$data_dr[$k_dta_vartype][$k_date][$t]['dta_7'] =  $v[$lie_mark['结算方式']];
										$data_dr[$k_dta_vartype][$k_date][$t]['dta_8'] =  $v[$lie_mark['合同数量']];
										$data_dr[$k_dta_vartype][$k_date][$t]['dta_9'] =  $v[$lie_mark['合同单价']];
										$data_dr[$k_dta_vartype][$k_date][$t]['dta_10'] =  $v[$lie_mark['主业公司']];
										$data_dr[$k_dta_vartype][$k_date][$t]['dta_11'] =  $v[$lie_mark['主业合同编号']];
										$data_dr[$k_dta_vartype][$k_date][$t]['dta_12'] =  $v[$lie_mark['主业单价']];
										$data_dr[$k_dta_vartype][$k_date][$t]['dta_13'] =  $v[$lie_mark['合同完成率']];
										$data_dr[$k_dta_vartype][$k_date][$t]['dta_14'] =  $v[$lie_mark['合同期限']];
										$data_dr[$k_dta_vartype][$k_date][$t]['dta_15'] =  $v[$lie_mark['运费']];
										$data_dr[$k_dta_vartype][$k_date][$t]['dta_16'] =  $v[$lie_mark['备注']];
	
										if( strpos($v['A'],'总计') !== false ){
											$data_dr[$k_dta_vartype][$k_date][$t]['dta_17'] = 2;
										}elseif( strpos($v['A'],'汉钢合计') !== false ){
											$data_dr[$k_dta_vartype][$k_date][$t]['dta_17'] = 1;
											$data_dr[$k_dta_vartype][$k_date][$t]['dta_18'] = 1;
										}elseif( strpos($v['A'],'龙钢合计') !== false ){
											$data_dr[$k_dta_vartype][$k_date][$t]['dta_17'] = 1;
											$data_dr[$k_dta_vartype][$k_date][$t]['dta_18'] = 2;
										}elseif( strpos($v['A'],'小计') !== false ){
											$data_dr[$k_dta_vartype][$k_date][$t]['dta_17'] = 3;
											$x = $k;
											while( empty( $data[$x][$lie_mark['主业公司']] ) ){
												$x -= 1;
											}
											if( $data[$x][$lie_mark['主业公司']] == '龙钢' ){
												$data_dr[$k_dta_vartype][$k_date][$t]['dta_18'] = 2;
											}elseif( $data[$x][$lie_mark['主业公司']] == '汉钢' ){
												$data_dr[$k_dta_vartype][$k_date][$t]['dta_18'] = 1;
											}
											$x = $k - 1;
											while( empty( $data[$x]['A'] ) ){
												$x -= 1;
											}
											$data_dr[$k_dta_vartype][$k_date][$t]['dta_19'] = $data[$x]['A'];
										}else{
											$data_dr[$k_dta_vartype][$k_date][$t]['dta_17'] = 0;
										}
	
										if( $k_dta_vartype == '1' ){
											$data_dr[$k_dta_vartype][$k_date][$t]['dta_20'] = $v[$v_mark];
										}elseif( $k_dta_vartype == '2' || $k_dta_vartype == '3' ){
											$data_dr[$k_dta_vartype][$k_date][$t]['dta_21'] = $v[$v_mark['liang']];
											$data_dr[$k_dta_vartype][$k_date][$t]['dta_22'] = $v[$v_mark['jia']];
										}
	
										$data_dr[$k_dta_vartype][$k_date][$t]['dta_type'] = $type1;
										$data_dr[$k_dta_vartype][$k_date][$t]['dta_vartype'] = $k_dta_vartype;
										$data_dr[$k_dta_vartype][$k_date][$t]['dta_ym'] = $k_date;
	
										// 行号
										$data_dr[$k_dta_vartype][$k_date][$t]['hanghao'] = $k;
										$t += 1;

										if( $uptype == "9" && strpos($v['A'],'总计') == false && strpos($v['A'],'小计') == false && strpos($v['A'],'合计') == false){
											$bfjghz[$k_date]['htsl_zydj'] += $v[$lie_mark['合同数量']] * $v[$lie_mark['主业单价']];
											$bfjghz[$k_date]['htsl_zyzb'] += $v[$lie_mark['合同数量']] * $v[$lie_mark['主要指标']] / 0.66;
										}
										if( $uptype == "7" && strpos($v['A'],'总计') == false && strpos($v['A'],'小计') == false && strpos($v['A'],'合计') == false && $v[$lie_mark['单位名称']] != "外购块铁"){
											if ($v[$lie_mark['主业公司']] == "龙钢") {
												$bfjghz[$k_date]['lg']['htdj_htsl'] += $v[$lie_mark['合同单价']] *  $v[$lie_mark['合同数量']];
												$bfjghz[$k_date]['lg']['htsl'] +=  $v[$lie_mark['合同数量']];
											}
											if ($v[$lie_mark['主业公司']] == "汉钢") {
												$bfjghz[$k_date]['hg']['htdj_htsl'] += $v[$lie_mark['合同单价']] *  $v[$lie_mark['合同数量']];
												$bfjghz[$k_date]['hg']['htsl'] +=  $v[$lie_mark['合同数量']];
											}
										}
									}
								}
							}
						}
						// echo"<pre>";print_r($lie_mark);
					}
					
					// echo"<pre>";print_r($bfjghz);exit;
					if( $uptype == "9"){
						// echo 11;exit;
						foreach( $bfjghz as $k => $v ){
							$value_1 = $v['htsl_zydj'] / $v['htsl_zyzb'];
							$sql = "select * from sg_data_table where dta_type='SG_BFJGHZ' and dta_ym='$k'";
							$sg_data_tableinfo = $this->_dao->query($sql);
							if(empty($sg_data_tableinfo)){
								$sql = "insert into sg_data_table(dta_type,dta_ym,dta_1,dta_2,dta_3,createtime,createuser) values ('SG_BFJGHZ','".$k."','".$value_1."','','',now(),'$user_id')";
								$this->_dao->execute( $sql );
							}else{
								$sql = "update sg_data_table set dta_1 = '$value_1' where dta_type='SG_BFJGHZ' and dta_ym='$k'";
								$this->_dao->execute( $sql );
							}
						}
					}
					if( $uptype == "7"){
						// echo 11;exit;
						foreach( $bfjghz as $k => $v ){
							$value_2 = $v['lg']['htdj_htsl'] / $v['lg']['htsl'];
							$value_3 = $v['hg']['htdj_htsl'] / $v['hg']['htsl'];
							$sql = "select * from sg_data_table where dta_type='SG_BFJGHZ' and dta_ym='$k'";
							$sg_data_tableinfo = $this->_dao->query($sql);
							if(empty($sg_data_tableinfo)){
								$sql = "insert into sg_data_table(dta_type,dta_ym,dta_1,dta_2,dta_3,createtime,createuser) values ('SG_BFJGHZ','".$k."','','".$value_2."','".$value_3."',now(),'$user_id')";
								$this->_dao->execute( $sql );
							}else{
								$sql = "update sg_data_table set dta_2 = '$value_2',dta_3 = '$value_3' where dta_type='SG_BFJGHZ' and dta_ym='$k'";
								$this->_dao->execute( $sql );
							}
						}
					}
					// echo $sql;

					$insert_1 = true;
					foreach( $data_dr as $k_dta_vartype => $date_arr ){
						foreach( $date_arr as $k_date => $v_info ){
							$del_sql = "delete from sg_data_table where dta_type='$type1' and dta_ym='$k_date' and dta_vartype='$k_dta_vartype'";
							// echo $del_sql;exit;
							$this->_dao->execute( $del_sql );
							$basesql = "insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,dta_3,dta_4,dta_5,dta_6,dta_7,dta_8,dta_9,dta_10,dta_11,dta_12,dta_13,dta_14,dta_15,dta_16,dta_17,dta_18,dta_19,dta_20,dta_21,dta_22,createtime,createuser) values";
							$valSql = "";
							foreach($v_info as $k=>$v)
							{
								$valSql.="('".$v['dta_type']."','".$v['dta_vartype']."','".$v['dta_ym']."','".$v['dta_1']."','".$v['dta_2']."','".$v['dta_3']."','".$v['dta_4']."','".$v['dta_5']."','".$v['dta_6']."','".$v['dta_7']."','".$v['dta_8']."','".$v['dta_9']."','".$v['dta_10']."','".$v['dta_11']."','".$v['dta_12']."','".$v['dta_13']."','".$v['dta_14']."','".$v['dta_15']."','".$v['dta_16']."','".$v['dta_17']."','".$v['dta_18']."','".$v['dta_19']."','".$v['dta_20']."','".$v['dta_21']."','".$v['dta_22']."',now(),'$user_id'),";
							}	
							$insertsql = substr($basesql.$valSql, 0, -1);
							if(!empty($valSql))
							{
								$this->_dao->execute($insertsql);
								// echo $insertsql."<br><br>";
							}else{
								// 失败
								$insert_1 = fales;
							}
						}
					}

					if(!$insert_1){
						$error_sheet2[] = $sheet_v;
						$flag = false;
					}
				}
				if(!$flag)
				{
					$error1 = implode(',', $error_sheet1);
					$error2 = implode(',', $error_sheet2);
					if(!empty($error1))
					{
						$msg1 = "工作表：".$error1."的A1单元格日期格式有误，导入失败";
					}
					if(!empty($error2))
					{
						$msg2 = "工作表：".$error2."出现未知错误，导入失败";
					}
					$response["Success"] = 0;
					$response['Message'] = $msg1."\\n".$msg2;
					unlink($file); //删除文件
				}else{
					$response["Success"] = 1;
					$response['Message'] = '导入成功';
					$sheetname = substr($sheetname, 0, -1);
					// 日志
					$sql ="insert into sg_data_table_log(uptype,filepath,filename,sheetname,createtime,createuser,uptypedate) values('$uptype','$file','$filename','$sheetname',now(),'$user_id','$selyear')";
					// echo $sql."<br><br>";
					$this->_dao->execute($sql);
				}
			}
		}
		echo $this->pri_JSON($response);
		exit;
	}

	// 成本月报表汉钢、龙钢
	function dosheet2($params)
	{
		ini_set('memory_limit', '512M');
		$GUID = $params['GUID'];
		$mode = $params['mode'];//1=客户端，2=安卓，3=iOS
		$uptype = $params['uptype'];
		$filename = iconv("utf-8","gb2312//IGNORE",$params['filename']);
		$selmonth = $params['year'];
		$dta_type_arr = array(
			'16'=>'SG_CBYBB_HG',
			'17'=>'SG_CBYBB_LG'
		);
		$type1 = $dta_type_arr[$uptype];
		$response = array(
			'Success'  => 1,
			'Message'  => '',
			// 'GUID'     => $GUID,
			// 'mode'     => $mode,
		);
		if($GUID==""){
			$response["Success"] = 0;
			$response['Message'] = 'GUID不能为空';
		}else{
			$user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
			$user_id = $user_infos['Uid'];
			if(empty($user_infos)){
				$response["Success"] = 0;
				$response['Message'] = '用户不存在';
			}
			$sheetList = explode(',', $params["sheet"]);
			$file = SGUPLOADFILE."/".$params["tmp"];
			if($params["sheet"]=="")
			{
				$response["Success"] = 0;
				$response['Message'] = '未选择工作表';
			}
			$year = mysql_real_escape_string($params["year"]);
			if(empty($year))
			{
				$response["Success"] = 0;
				$response['Message'] = '时间出错';
			}
		}
		if($response["Success"])
		{
			$flag = true;
			require_once "../PHPExcel/PHPExcel.php";
			$type = pathinfo($file); 
			$type = strtolower($type["extension"]);
			if ($type=='xlsx') { 
				$type='Excel2007'; 
			}elseif($type=='xls') { 
				$type = 'Excel5';
			}
			$objReader = PHPExcel_IOFactory::createReader($type);//判断使用哪种格式
			$objReader ->setReadDataOnly(true); //只读取数据,会智能忽略所有空白行,这点很重要！！！
			$objPHPExcel = $objReader->load($file); //加载Excel文件
			$sheets = $objPHPExcel->getSheetNames();//获取sheet工作表数组
			$sheetCount = $objPHPExcel->getSheetCount();//获取sheet工作表总个数
			$sheetname = "";
			foreach($sheetList as $sheet_index1)
			{
				$sheet_index = $sheetCount-1-$sheet_index1;
				$sheet = $objPHPExcel->getSheet($sheet_index);
				//获取当前工作表最大行数
				$rows = $sheet->getHighestRow();
				//获取当前工作表最大列数,返回的是最大的列名，如：B 
				// $cols = $sheet->getHighestColumn();
				//将当前工作表名当键，内容为值存入数组
				$sheet_v = iconv("utf-8","gb2312//IGNORE",$sheets[$sheet_index]);
				$sheetname .= $sheet_v.",";

				for($k = 2; $k <= $rows; $k++){
					for($j = 65; $j <= 70; $j++ ){
						$key = chr($j).$k;
						$data[$k][chr($j)] = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue()));
					}
				}
				// echo"<pre>";print_r($data);
				$data_dr = array();
				if ($data[2]["D"] == "" && $data[2]["E"] == "") {
					$data_dr[0]["dta_7"] = $data[4]["F"];
					$hang_mark = 6;
				} else {
					foreach ($data[2] as $k => $v ) {
						if( strpos($v,'名称') !== false ){
							$data_dr[0]["dta_6"] = $data[3][$k];
						}elseif( strpos($v,'产量') !== false ){
							$data_dr[0]["dta_7"] = $data[3][$k];
						}
					}
					$data_dr[0]["dta_8"] = $data[3]["A"];
					$hang_mark = 4;
				}
				$data_dr[0]["dta_9"] = "2";
				$lie_mark = array();
				foreach ($data[$hang_mark] as $k => $v ) {
					if( strpos($v,'项') !== false ){
						$lie_mark['项目'] = $k;
						$lie_mark['单位'] = chr( ord($k) + 1 );
						$lie_mark['单价'] = chr( ord($k) + 2 );
						$lie_mark['单位消耗'] = chr( ord($k) + 3 );
						$lie_mark['单位成本'] = chr( ord($k) + 4 );
					}
				}
				$t = 1;
				foreach( $data as $k => $v ){
					if(  $k >= ($hang_mark + 2) && !empty($v[$lie_mark['项目']]) && $v[$lie_mark['项目']] != "Kg/T"  && $v[$lie_mark['项目']] != "KG"  && $v[$lie_mark['项目']] != "T/T" ){
						$data_dr[$t]["dta_1"] = $v[$lie_mark['项目']];
						$data_dr[$t]["dta_2"] = $v[$lie_mark['单位']];
						$data_dr[$t]["dta_3"] = $v[$lie_mark['单价']];
						$data_dr[$t]["dta_4"] = $v[$lie_mark['单位消耗']];
						$data_dr[$t]["dta_5"] = $v[$lie_mark['单位成本']];
						$data_dr[$t]["dta_9"] = "1";
						$t += 1;
					}
				}
				// echo"<pre>";print_r($data_dr);
				
				$insert_1 = true;
				$del_sql = "delete from sg_data_table where dta_type='$type1' and dta_ym='".$selmonth."' and dta_vartype='$sheet_v'";
				// echo $del_sql;exit;
				$this->_dao->execute( $del_sql );
				$basesql = "insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,dta_3,dta_4,dta_5,dta_6,dta_7,dta_8,dta_9,createtime,createuser) values";
				$valSql = "";
				foreach ($data_dr as $k=>$v)
				{
					$valSql.="('".$type1."','".$sheet_v."','".$selmonth."','".$v['dta_1']."','".$v['dta_2']."','".$v['dta_3']."','".$v['dta_4']."','".$v['dta_5']."','".$v['dta_6']."','".$v['dta_7']."','".$v['dta_8']."','".$v['dta_9']."',now(),'$user_id'),";
				}
				$insertsql = substr($basesql.$valSql, 0, -1);
				if(!empty($valSql))
				{
					$this->_dao->execute($insertsql);
					// echo $insertsql."<br><br>";
				}else{
					// 失败
					$insert_1 = fales;
				}
				if(!$insert_1){
					$error_sheet2[] = $sheet_v;
					$flag = false;
				}
			}
			if(!$flag)
			{
				$error1 = implode(',', $error_sheet1);
				$error2 = implode(',', $error_sheet2);
				if(!empty($error2))
				{
					$msg2 = "工作表：".$error2."出现未知错误，导入失败";
				}
				$response["Success"] = 0;
				$response['Message'] = $msg2;
				unlink($file); //删除文件
			}else{
				$response["Success"] = 1;
				$response['Message'] = '导入成功';
				$sheetname = substr($sheetname, 0, -1);
				// 日志
				$sql ="insert into sg_data_table_log(uptype,filepath,filename,sheetname,createtime,createuser,uptypedate) values('$uptype','$file','$filename','$sheetname',now(),'$user_id','$selmonth')";
				// echo $sql."<br><br>";
				$this->_dao->execute($sql);
			}
		}
		echo $this->pri_JSON($response);
		exit;
	}

	//php获取中文字符拼音首字母
	function getFirstCharter($str){
		if(empty($str)){
				return '';
		}
		$fchar = ord($str[0]);
		if($fchar >= ord('A') && $fchar <= ord('z')){
				return strtoupper($str[0]);
		}
		$s1=iconv('UTF-8','gb2312',$str);
		$s2=iconv('gb2312','UTF-8',$s1);
		$s=$s2==$str?$s1:$str;
		$asc=ord($s[0])*256+ord($s[1])-65536;
		
		if($asc>=-20319&&$asc<=-20284) return 'A';
		if($asc>=-20283&&$asc<=-19776) return 'B';
		if($asc>=-19775&&$asc<=-19219) return 'C';
		if($asc>=-19218&&$asc<=-18711) return 'D';
		if($asc>=-18710&&$asc<=-18527) return 'E';
		if($asc>=-18526&&$asc<=-18240) return 'F';
		if($asc>=-18239&&$asc<=-17923) return 'G';
		if($asc>=-17922&&$asc<=-17418) return 'H';
		if($asc>=-17417&&$asc<=-16475) return 'J';
		if($asc>=-16474&&$asc<=-16213) return 'K';
		if($asc>=-16212&&$asc<=-15641) return 'L';
		if($asc>=-15640&&$asc<=-15166) return 'M';
		if($asc>=-15165&&$asc<=-14923) return 'N';
		if($asc>=-14922&&$asc<=-14915) return 'O';
		if($asc>=-14914&&$asc<=-14631) return 'P';
		if($asc>=-14630&&$asc<=-14150) return 'Q';
		if($asc>=-14149&&$asc<=-14091) return 'R';
		if($asc>=-14090&&$asc<=-13319) return 'S';
		if($asc>=-13318&&$asc<=-12839) return 'T';
		if($asc>=-12838&&$asc<=-12557) return 'W';
		if($asc>=-12556&&$asc<=-11848) return 'X';
		if($asc>=-11847&&$asc<=-11056) return 'Y';
		if($asc>=-11055&&$asc<=-10247) return 'Z';
		return '其他';
	}

	private function pri_JSON($array)
    {
        $this->pri_arrayRecursive($array, 'urlencode', true);
        $json = json_encode($array);
        return iconv("GB2312", "UTF-8", urldecode($json));
    }

    private function pri_arrayRecursive(&$array, $function, $apply_to_keys_also = false)
    {
        static $recursive_counter = 0;
        if (++$recursive_counter > 1000) {
            die('possible deep recursion attack');
        }
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $this->pri_arrayRecursive($array[$key], $function, $apply_to_keys_also);
            } else {
                $array[$key] = $function($value);
            }
            if ($apply_to_keys_also && is_string($key)) {
                $new_key = $function($key);
                if ($new_key != $key) {
                    $array[$new_key] = $array[$key];
                    unset($array[$key]);
                }
            }
        }
        $recursive_counter--;
	}
	
	//数组转码
    public function array_iconv($str, $in_charset="utf-8", $out_charset="gb2312")
    {
        if (is_array($str)) {
            foreach ($str as $k => $v) {
                $str[$k] = $this->array_iconv($v);
            }
            return $str;
        } else {
            if (is_string($str)) {
                return mb_convert_encoding($str, $out_charset, $in_charset);
            } else {
                return $str;
            }
        }
	}
	
	//获取字符串ASCII 和
	public function getASCII( $str ){
		$arr = str_split($str);
		$sum = 0;
		foreach( $arr as $k => $v ){
			$sum += ord($v);
		}
		return $sum;
	}
}

?>