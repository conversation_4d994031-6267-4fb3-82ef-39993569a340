<?php
include_once(FRAME_LIB_DIR . "/controller/AbstractController.inc.php");
class DcsearchController extends AbstractController
{

	public function __construct()
	{

		parent::__construct();

		$this->_action->setDao(new DcsearchDao("MAIN"));
		//$this->_action->bizdao = new SystemDao('BIZ') ;
		//$this->_action->stedao = new DcsystemDao('HQ') ;
		//$this->_action->stedao = new DcsearchDao('DRC') ;
		$this->_action->ngdrc = new DcsearchDao("DRCW");
		$this->_action->stedao = new DcsearchDao('91R');
	}

	public function do_BizGet()
	{

		$this->_action->BizGet($this->_request);
	}
	public function do_ExportData()
	{

		$this->_action->ExportData($this->_request);
	}
	public function do_CommonSearch()
	{

		$this->_action->CommonSearch($this->_request);
	}
	public function do_CommonSystemSearch()
	{

		$this->_action->CommonSystemSearch($this->_request);
	}
	public function do_CommonSearchTable()
	{

		$this->_action->CommonSearchTable($this->_request);
	}
	public function do_CommonSearchImage()
	{

		$this->_action->CommonSearchImage($this->_request);
	}
	public function do_GetLineTitle()
	{

		$this->_action->GetLineTitle($this->_request);
	}
	public function do_DataImage()
	{

		$this->_action->DataImage($this->_request);
	}
	public function do_FormulaAdd()
	{

		$this->_action->FormulaAdd($this->_request);
	}
	public function do_FormulaGet()
	{

		$this->_action->FormulaGet($this->_request);
	}
	public function do_FormulaDel()
	{

		$this->_action->FormulaDel($this->_request);
	}
	public function do_AddCustomColor()
	{

		$this->_action->AddCustomColor($this->_request);
	}
	public function do_GetCustomColor()
	{

		$this->_action->GetCustomColor($this->_request);
	}

	public function do_FormulaEdit()
	{

		$this->_action->FormulaEdit($this->_request);
	}

	public function do_FormulaUnitGet()
	{

		$this->_action->FormulaUnitGet($this->_request);
	}


	public function do_update_Data1Type()
	{
		exit;
		$this->_action->update_Data1Type($this->_request);
	}
	public function do_update_Databysteelhome()
	{
		exit;
		$this->_action->update_Databysteelhome($this->_request);
	}
	public function do_update_dc_code_class()
	{
		exit;
		$this->_action->update_dc_code_class($this->_request);
	}
	public function do_saveimgbybase64()
	{
		$this->_action->saveimgbybase64($this->_request);
	}
	public function do_saveimgby64()
	{
		$this->_action->saveimgbybase64($this->_request);
	}
	public function do_kanbancelldata()
	{
		$this->_action->kanbancelldata($this->_request);
	}
	//hlf start 2020/11/25
	public function do_pzszzk()
	{

		$this->_action->pzszzk($this->_request);
	}
	//hlf end

	public function do_checktechtablename()
	{

		$this->_action->checktechtablename($this->_request);
	}
    public function do_exportmenu(){
        $this->_action->exportmenu();
    }
    public function do_exportmenu2(){
        $this->_action->exportmenu2();
    }
}
