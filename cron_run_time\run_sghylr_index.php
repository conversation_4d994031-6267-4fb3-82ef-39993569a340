<?php
require_once ("/etc/steelconf/config/isholiday.php");
/*每天生成前一天的行业利润数据*/
 
if($argc > 1){
	$date = $argv[1];
	$mc_type = $argv[2];
	$date = date("Y-m-d",strtotime($date."-1 day"));
	// while( _isholiday ( $date )){
	// 	$date = date("Y-m-d",strtotime($date."-1 day"));
	// }
	if( _isholiday ( $date )){
		echo "节假日不计算";
		exit;
	}
}else{
	echo "未传递参数";
	exit;
}

// chdir("/usr/local/www/www.steelhome.cn/data/v1.5/web");
chdir("/usr/local/www/dc.steelhome.cn/v1.5/web");
$_REQUEST["action"]=$_GET["action"]="savehylr";
$_REQUEST["mc_type"]=$_GET["mc_type"]=$mc_type;
$_REQUEST["sdate"]=$_GET["sdate"]=$date;
$_REQUEST["edate"]=$_GET["edate"]=$date;
include_once ("sghylr.php");

echo "end";
?>