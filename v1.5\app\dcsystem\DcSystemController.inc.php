<?php
include_once(FRAME_LIB_DIR . "/controller/AbstractController.inc.php");

class DcSystemController extends AbstractController
{

    public function __construct()
    {
        parent::__construct();

        $this->_action->setDao(new DcSystemDao("MAIN"));
        //$this->_action->bizdao = new SystemDao('BIZ') ;
        $this->_action->stedao = new DcSystemDao('91R');
        $this->_action->drcdao = new DcSystemDao('DRCW');
    }

    public function _dopre()
    {
        //$this->_action->checkSession();
    }

    public function v_index()
    {
        $this->_action->index($this->_request);
    }

    public function do_ClassListGet()
    {
        $this->_action->doclasslistget($this->_request);
    }

    public function do_ClassListGet2()
    {
        $this->_action->doclasslistget2($this->_request);
    }

    public function do_ClassListGet3()
    {
        $this->_action->doclasslistget3($this->_request);
    }

    public function do_SearchListSave()
    {
        $this->_action->SearchListSave($this->_request);
    }

    public function do_SearchListGet()
    {
        $this->_action->SearchListGet($this->_request);
    }

    public function do_SearchListSwap()
    {
        $this->_action->SearchListSwap($this->_request);
    }

    public function do_DelSearchList()
    {
        $this->_action->DelSearchList($this->_request);
    }

    public function do_GetMainMenu()
    {
        $this->_action->GetMainMenu($this->_request);
    }

    public function do_getRecordInfo()
    {
        $this->_action->getRecordInfo($this->_request);
    }

    public function do_uploadRecord()
    {
        $this->_action->uploadRecord($this->_request);
    }

    public function do_ClassListGet2Search()
    {
        $this->_action->classlist2search($this->_request);
    }


    //add by xr 20190510

    public function do_DTIDGetResult()
    {
        $this->_action->DTIDGetResult($this->_request);
    }
    //end
    //add by xr 20190725

    public function do_addCatalogue()
    {
        $this->_action->addCatalogue($this->_request);
    }

    public function do_delCatalogue()
    {
        $this->_action->delCatalogue($this->_request);
    }
    public function do_CustomDataSwap()
    {
        $this->_action->CustomDataSwap($this->_request);
    }
    public function do_UpdateCustomData()
    {
        $this->_action->UpdateCustomData($this->_request);
    }

    public function do_SearchListGetByKey()
    {
        $this->_action->SearchListGetByKey($this->_request);

    }

    //end
    public function do_SearchListGet2()
    {
        $this->_action->SearchListGet2($this->_request);

    }

    public function do_SearchListGet3()
    {//
        $this->_action->SearchListGet3($this->_request);

    }
    public function do_SearchListGet4()
    {//
        $this->_action->SearchListGet4($this->_request);

    }


    public function do_GetDataTypesBySymbol()
    {
        $this->_action->GetDataTypesBySymbol($this->_request);

    }


    public function do_savelinetitlebak()
    {
        $this->_action->savelinetitlebak($this->_request);

    }

    public function do_updatelinetitle()
    {
        $this->_action->updatelinetitle($this->_request);

    }


//add by xuru 导入导出 json
    public function do_DerivedCatalogue()
    {
        $this->_action->DerivedCatalogue($this->_request);

    }

    public function do_ImportCatalogue()
    {
        $this->_action->ImportCatalogue($this->_request);

    }


    public function do_RecycleListGet()
    {
        $this->_action->RecycleListGet($this->_request);

    }

    public function do_RecycleOp()
    {
        $this->_action->RecycleOp($this->_request);

    }
//end 


//循环加select
    public function do_addselect()
    {
        $this->_action->addselect($this->_request);

    }

//获取陕晋川甘月环比
    public function do_GetXBZHMothHB()
    {
        $this->_action->GetXBZHMothHB($this->_request);

    }

    /**
     * @param $params
     * user:shizg
     * time:2022/8/10 15:08
     * TODO 获取接口版本控制标识
     */
    public function do_getVersionControlFlag()
    {
        $this->_action->getVersionControlFlag($this->_request);
    }

}


?>