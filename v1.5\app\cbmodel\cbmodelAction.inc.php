<?php 
//require_once ("/etc/steelconf/config/isholiday.php");

include_once( APP_DIR."/cbmodel/cbmodelBaseAction.inc.php" );
class  cbmodelAction extends cbmodelBaseAction
{
    public function __construct()
    {
        parent::__construct();
    } 
	
	public function index($params)
    {
		//echo "<pre>";print_R($params);
		$GUID = $params['GUID'];
		//$GUID = "6615bc4ac53d11e7b891001aa00de1ab";
		$mode = $params['mode']==""?'1':$params['mode'];//1=客户端，2=安卓，3=iOS
		//获取工作日时间
		if($params['date']==""){
            
            $flag=1;
            $lastday=date("Y-m-d");
            while(true)
            {
                $lastday=date('Y-m-d',strtotime('-'.$flag.' day',strtotime($lastday)));
                //echo $lastday;
                if(file_get_contents(APP_URL_WWW.'/isholiday.php?date='.$lastday)!="1")
                { 
                  break;
                } 
            }
            $params['date'] = $lastday;
        }
		/*if($params['date']=='')$params['date'] = date("Y-m-d");
		$queryDate = $params['date'];
		$date = $this->get_xhdate($params['date']);*/
		//echo $date;
		$TypeName = $GLOBALS['CB_MODEL_NAME'][$params['Type']];
		//获取用户信息
		$user_infos = $this->t1dao->get_userinfo_byguid($GUID);
		$Mid=$user_infos['Mid'];//会员id
		$userid=$user_infos['Uid'];//用户id
		$TrueName=$user_infos['TrueName'];

		//获取用户权限和用户名app_license_privilege
		$license_detail_info = $this->t1dao->get_license_privilege($userid);
        //print_R($license_detail_info);
        $this->assign("cb_privilege",$license_detail_info['cb_privilege']);

		//参数设置信息
		if($params['id']==''){
			$info = $this->maindao->get_ng_ChengBenParameter3($params['date'],$params['Type']);
			//$date = $info['Date'];
			//echo "<pre>";print_R($info);exit;
		}else{
			$info = $this->maindao->get_ng_ChengBenParameter3ById($params['id']);
			$params['Type'] = $info['Type'];
			$params['date'] = $info['Date'];
			$queryDate = $info['Date'];
		}
		$info['ZengZhiShuiLv'] = $info['ZengZhiShuiLv']*100;
		$info['SuoDeShuiLv'] = $info['SuoDeShuiLv']*100;
		$info['GeLeiShuiFeiJiFuJia'] = $info['GeLeiShuiFeiJiFuJia']*100;
		
		//铁水成本指数
		$TieHeJinChengBenIndex_arr = $this->maindao->get_ng_TieHeJinChengBenIndex2($params['date'],$GLOBALS['CB_MODEL_TYPE_ID'][$params['Type']]['type']);
		$TieHeJinChengBenIndex = round($TieHeJinChengBenIndex_arr['indexValue']);
		
		//废钢价格
		$feigang_id = $GLOBALS['CB_MODEL_TYPE_ID'][$params['Type']]['feigang'];
		if($feigang_id){
			$FeiGangJiaGe = $this->maindao->selectSCPrice($feigang_id ,$params['date']);
		}

		//钒钛合金价格
		$fantaihejin_id = $GLOBALS['CB_MODEL_TYPE_ID'][$params['Type']]['fantai'];
		if($fantaihejin_id){
			$FanDanHeJinJiaGe = $this->maindao->selectSCPrice($fantaihejin_id ,$params['date']);
		}

		//硅锰价格
		$guimeng_id = $GLOBALS['CB_MODEL_TYPE_ID'][$params['Type']]['guimeng'];
		if($guimeng_id){
			$GuiMengJiaGe = $this->maindao->selectSCPrice($guimeng_id ,$params['date']);
		}

		//硅铁价格
		$guitie_id = $GLOBALS['CB_MODEL_TYPE_ID'][$params['Type']]['guitie'];
		if($guitie_id){
			$GuiTieJiaGe = $this->maindao->selectSCPrice($guitie_id ,$params['date']);
		}
		//高碳锰铁价格
		$gtmt_id = $GLOBALS['CB_MODEL_TYPE_ID'][$params['Type']]['gaotanmengtie'];
		if($gtmt_id){
			$GaoTanMenTieJiaGe = $this->maindao->selectSCPrice($gtmt_id ,$params['date']);
		}

		

		//贴现率
		$ChengDuiHuiPiaoTieXianLv_arr = $this->_dao->get_drc_hbh_txl($params['date']);
		$ChengDuiHuiPiaoTieXianLv = $ChengDuiHuiPiaoTieXianLv_arr['rd3'];

		//echo "<pre>info=";print_R($this->maindao);

		$total = $this->maindao->getOne( "SELECT COUNT(id) as c FROM  ng_ChengBenParameter3 where Type='".$params['Type']."' and isDelete ='0' " );
        $page = $params['page'] == '' ? 1 : $params['page'];
        $per = 10;
        $start = ( $page - 1 ) * $per;
        $url = "cbmodel.php";

        $data = $this->maindao->getAll($params['Type'],$start,$per);	   
        //print_r($data);
        $this->assign("data",$data);
        $this->assign("page",$page);
        $this->assign("tatalpage",ceil($total/$per));
		

        if($mode==1){
            $head_url = "//".$_SERVER['SERVER_NAME']."/v1.5/css/tieshui_small.css";
        }else if($mode==3||$mode==5){
            $head_url = "//".$_SERVER['SERVER_NAME']."/v1.5/css/tieshui_middle.css";
        }else if($mode==2||$mode==4){
            $head_url = "//".$_SERVER['SERVER_NAME']."/v1.5/css/tieshui_large.css";
        }else{
            $head_url = "//".$_SERVER['SERVER_NAME']."/v1.5/css/tieshui_middle.css";
        }
		$TYPE_NAME = $GLOBALS['CB_TYPE_NAME'][$params['Type']];
		$style_url= dirname("//".$_SERVER['SERVER_NAME'].$_SERVER['PHP_SELF']);
		$this->assign( "style_url", $style_url );
		$this->assign("head_url",$head_url);
		$this->assign("TypeName",$TypeName);
		$this->assign("info",$info);
		$this->assign("GUID",$GUID);
		$this->assign("tip",$params['tip']);
		$this->assign("TYPE_NAME",$TYPE_NAME);
		$this->assign("userid",$userid);
		$this->assign("adminname",$TrueName);
		$this->assign("mode",$mode);
		$this->assign("TieHeJinChengBenIndex",$TieHeJinChengBenIndex);
		$this->assign("FeiGangJiaGe",$FeiGangJiaGe);
		$this->assign("FanDanHeJinJiaGe",$FanDanHeJinJiaGe);
		$this->assign("GuiMengJiaGe",$GuiMengJiaGe);
		$this->assign("GuiTieJiaGe",$GuiTieJiaGe);
		$this->assign("GaoTanMenTieJiaGe",$GaoTanMenTieJiaGe);
		$this->assign("ChengDuiHuiPiaoTieXianLv",$ChengDuiHuiPiaoTieXianLv);
        $this->assign("params",$params);
    }
	
	//计算数值
	public function solve($params){
		//file_put_contents("/tmp/zfy",print_R($params,true));
		$date = $params['date'];
		$Type = $params['Type']==''?'1':$params['Type'];
		$userid = $params['userid'];
		$adminname = $params['adminname'];
		$GUID = $params['GUID'];
		$DO = $params['do'];
		//铁水成本
		$TieHeJinChengBenIndex = $params['TieHeJinChengBenIndex'];
		//铁水消耗
		$TieShuiXiaoHaoA = $params['TieShuiXiaoHaoA'];
		//废钢价格
		$FeiGangJiaGe = $params['FeiGangJiaGe'];
		//废钢铁消耗
		$FeiGangTieXiaoHaoA = $params['FeiGangTieXiaoHaoA'];
		//钢铁料消耗
		$GangTieLiaoXiaoHaoA = $params['GangTieLiaoXiaoHaoA'];
		//钒氮合金价格
		$FanDanHeJinJiaGe = $params['FanDanHeJinJiaGe'];
		//运费1
		$YunFei1B = $params['YunFei1B'];
		//吨钢消耗1
		$DunGangXiaoHao1B = $params['DunGangXiaoHao1B'];
		//硅锰价格
		$GuiMengJiaGe = $params['GuiMengJiaGe'];
		//硅铁价格
		$GuiTieJiaGe = $params['GuiTieJiaGe'];
		//高碳锰铁
		$GaoTanMenTieJiaGe = $params['GaoTanMenTieJiaGe'];
		//运费2
		$YunFei2B = $params['YunFei2B'];
		//吨钢消耗2
		$DunGangXiaoHao2B = $params['DunGangXiaoHao2B'];
		//运费3
		$YunFei3B = $params['YunFei3B'];
		//吨钢消耗3
		$DunGangXiaoHao3B = $params['DunGangXiaoHao3B'];
		//其他合金
		$QiTaHeJinB = $params['QiTaHeJinB'];
		//能源动力
		$NengYuanDongLi = $params['NengYuanDongLi'];
		//辅料
		$FuLiao = $params['FuLiao'];
		//人工
		$RenGong = $params['RenGong'];
		//制造费用
		$ZhiZhaoFeiYong = $params['ZhiZhaoFeiYong'];
		//螺纹钢加工费
		$LuoWenGangJiaGongFei = $params['LuoWenGangJiaGongFei'];
		//期间费用
		$QiJianFeiYong = $params['QiJianFeiYong'];
		//承兑汇票贴现率
		$ChengDuiHuiPiaoTieXianLv = $params['ChengDuiHuiPiaoTieXianLv'];
		//增值税率
		$ZengZhiShuiLv = $params['ZengZhiShuiLv'];
		//各类税费及附加
		$GeLeiShuiFeiJiFuJia = $params['GeLeiShuiFeiJiFuJia'];
		//所得税率
		$SuoDeShuiLv = $params['SuoDeShuiLv'];
		//变成百分数除100
		$ZengZhiShuiLv = $ZengZhiShuiLv/100;
		$SuoDeShuiLv = $SuoDeShuiLv/100;
		$GeLeiShuiFeiJiFuJia = $GeLeiShuiFeiJiFuJia/100;

		//毛利价格
		$ml_id = $GLOBALS['CB_MODEL_TYPE_ID'][$Type]['maoli_id'];
		if($ml_id){
			$MaoLiJiaGe = $this->maindao->selectSCPrice($ml_id ,$date);
		}

		$A = ((float)$TieHeJinChengBenIndex * (float)$TieShuiXiaoHaoA + (float)$FeiGangJiaGe * (float)$FeiGangTieXiaoHaoA) * (float)$GangTieLiaoXiaoHaoA;
		if($Type == '1' || $Type == '2'|| $Type == '3'|| $Type == '4'){
			$B = ((float)$FanDanHeJinJiaGe * (1 - (float)$ChengDuiHuiPiaoTieXianLv * 3 / 1000) + (float)$YunFei1B) * (float)$DunGangXiaoHao1B / 1000 + ((float)$GuiMengJiaGe * (1 - (float)$ChengDuiHuiPiaoTieXianLv * 3 / 1000) + (float)$YunFei2B) * (float)$DunGangXiaoHao2B / 1000 + (float)$QiTaHeJinB;
		}elseif($Type == '5' || $Type == '6'){
			$B = ((float)$GuiMengJiaGe * (1 - (float)$ChengDuiHuiPiaoTieXianLv * 3 / 1000) + (float)$YunFei1B) * (float)$DunGangXiaoHao1B / 1000 + ((float)$GuiTieJiaGe * (1 - (float)$ChengDuiHuiPiaoTieXianLv * 3 / 1000) + (float)$YunFei2B) * (float)$DunGangXiaoHao2B / 1000 + (float)$QiTaHeJinB;
		}elseif($Type == '7'||$Type == '8'){
			$B = ((float)$GuiMengJiaGe * (1 - (float)$ChengDuiHuiPiaoTieXianLv * 3 / 1000) + (float)$YunFei1B) * (float)$DunGangXiaoHao1B / 1000 + ((float)$GuiTieJiaGe * (1 - (float)$ChengDuiHuiPiaoTieXianLv * 3 / 1000) + (float)$YunFei2B) * (float)$DunGangXiaoHao2B / 1000 + ((float)$GaoTanMenTieJiaGe * (1 - (float)$ChengDuiHuiPiaoTieXianLv * 3 / 1000) + (float)$YunFei3B) * (float)$DunGangXiaoHao3B / 1000 + (float)$QiTaHeJinB;
		}elseif($Type == '9'||$Type == '10'||$Type == '11'||$Type == '12'){
			$B = ((float)$GuiMengJiaGe * (1 - (float)$ChengDuiHuiPiaoTieXianLv * 3 / 1000) + (float)$YunFei1B) * (float)$DunGangXiaoHao1B / 1000  + (float)$QiTaHeJinB;
		}

		$Y = $A + $B + ($NengYuanDongLi + $FuLiao + $RenGong + $ZhiZhaoFeiYong) * (1 + $ZengZhiShuiLv);
		$Z = $Y + $LuoWenGangJiaGongFei * (1 + $ZengZhiShuiLv);
		$L1 = $MaoLiJiaGe - $Z;
		if($L1<=0){
			$SS = 0;
		}else{
			$SS = $L1 * $ZengZhiShuiLv * $GeLeiShuiFeiJiFuJia;
		}
		$L2 = $L1 - $QiJianFeiYong * (1 + $ZengZhiShuiLv) - $SS;
		if($L2<=0){
			$TT = 0;
		}else{
			$TT = $L2 * $SuoDeShuiLv;
		}
		$L3 = $L2 - $TT;
		$yuan = "元";
		$content = "<br>".$GLOBALS['CB_TYPE_NAME'][$params['Type']]['cbname']."成本:".round($Y).$yuan;
		$content .= "<br>".$GLOBALS['CB_TYPE_NAME'][$params['Type']]['cbname1']."成本:".round($Z).$yuan;
		$content .= "<br>".$GLOBALS['CB_TYPE_NAME'][$params['Type']]['cbname1']."毛利:".round($L1).$yuan;
		$content .= "<br>".$GLOBALS['CB_TYPE_NAME'][$params['Type']]['cbname1']."利润:".round($L2).$yuan;
		$content .= "<br>".$GLOBALS['CB_TYPE_NAME'][$params['Type']]['cbname1']."净利润:".round($L3).$yuan;
		if($DO == 'save'){
			$array  =array("TieShuiXiaoHaoA","FeiGangTieXiaoHaoA","GangTieLiaoXiaoHaoA","YunFei1B","DunGangXiaoHao1B","YunFei2B","DunGangXiaoHao2B","YunFei3B","DunGangXiaoHao3B","QiTaHeJinB","NengYuanDongLi","FuLiao","RenGong","ZhiZhaoFeiYong","LuoWenGangJiaGongFei","QiJianFeiYong","ZengZhiShuiLv","GeLeiShuiFeiJiFuJia","SuoDeShuiLv");

			$data = array();
			$data_100 = array("SuoDeShuiLv","ZengZhiShuiLv","GeLeiShuiFeiJiFuJia");
			foreach( $array as $a ){
				if(in_array($a,$data_100)){
					$a2 = $params[$a]/100;
					$data[] = $a . "='". $a2 . "'";
				}else{
					$data[] = $a . "='". $params[$a] . "'";
				}
			}
			$data = implode( ",", $data );        
			
			$this->maindao->execute( "INSERT INTO ng_ChengBenParameter3 SET $data ,adminid='".$userid."',adminname='".$adminname."',Type='".$params['Type']."',CreateDate=now(),Date='".$date."'");
			$pid = $this->maindao->insert_id();

			$id_arr = $this->maindao->query("select mTask.id from ng_ModifyChengBenParameterTask mTask,ng_ChengBenParameter3 cb3 where cb3.id=mTask.pid and mTask.status!=2 and cb3.Date='$date' and cb3.Type=".$params['Type']);
			foreach($id_arr as $key=>$id){
				$this->maindao->execute("update ng_ModifyChengBenParameterTask set status='2' where id = '".$id['id']."'");
			}
			$this->maindao->execute( "INSERT INTO ng_ModifyChengBenParameterTask SET pid='".$pid."',type='2',status=0");    
			gourl("./cbmodel.php?view=index&GUID=".$GUID."&Type=".$Type."&mode=".$params['mode']."&tip=保存成功&id=".$pid."&issave=1");
		}
		echo $content;
		
	}


	public function ajaxgetindexinfo($params){
        //echo "111";
        $page = $params['page'] == '' ? 1 : $params['page'];
        $per = 10;
        $start = ( $page - 1 ) * $per;
        $data = $this->maindao->getAll($params['type'],$start,$per);
        $content = "";
        foreach($data as $tmp){
            $content.='<tr>
				<td>'.$tmp['Date'].'</td>
				<td>'.$tmp['CreateDate'].'</td>
				<td>'.$tmp['adminname'].'</td>
				<td><a onclick="update('.$tmp['id'].');">修改</a></td>
			</tr>';
        }
		//<a onclick="del('.$tmp['id'].');"> 删除</a>
        echo $content;
        exit;
    }

	public function del($params){

		$status="2";
        if($params['id']!=""){
			$count = $this->maindao->getOne("select count(1) from ng_ChengBenParameter3 where Type='".$params['Type']."' and isDelete='0'");
			if($count>1){
				$this->maindao->execute( "update ng_ChengBenParameter3 set isDelete=1 where id='".$params['id']."'");
				$date = $this->maindao->getOne( "select Date from  ng_ChengBenParameter3 where id='".$params['id']."'");
				$id_arr = $this->maindao->query("select mTask.id from ng_ModifyChengBenParameterTask mTask,ng_ChengBenParameter3 cb3 where cb3.id=mTask.pid and mTask.status=0 and cb3.Date='$date' and cb3.Type=".$params['Type']);
				foreach($id_arr as $key=>$id){
					$this->maindao->execute("update ng_ModifyChengBenParameterTask set status='2' where id = '".$id['id']."'");
				}
				$this->maindao->execute( "INSERT INTO ng_ModifyChengBenParameterTask SET pid='".$params['id']."',type='2',status=0");
				$status="删除成功";
			}else{
				$status="仅存参数，无法删除";
			}
        }
        //echo $status;
        gourl("./cbmodel.php?view=index&GUID=".$params['GUID']."&Type=".$params['Type']."&mode=".$params['mode']."&tip=".$status);
        exit;

    }
	
	
	function get_xhdate($today)//根据当前日获取上个钢之家工作日
	{
		if(!file_put_contents(APP_URL_WWW.'/isholiday.php?date='.$today))//不是
		{ 
			$flag=1;
			$lastday=$today;
			while(true)
			{
				$lastday=date('Y-m-d',strtotime('-'.$flag.' day',strtotime($lastday)));
				if(!file_put_contents(APP_URL_WWW.'/isholiday.php?date='.$lastday))
				{ 
					break;
				} 
			}
			$today_s=$today;
			$lastday_s=$lastday;
		}
		// 今天是节假日
		else
		{
			//取不是节假日的当天时间
			$todayflag=1;
			while(true)
			{
				$today=date('Y-m-d',strtotime('-'.$todayflag.' day',strtotime($today)));
				if(!file_put_contents(APP_URL_WWW.'/isholiday.php?date='.$today))
				{ 
					break;
				} 
			}
			//取昨天的日期
			$lastflag=1;
			while(true)
			{
				$lastday=date('Y-m-d',strtotime('-'.$lastflag.' day',strtotime($today)));
				if(!file_put_contents(APP_URL_WWW.'/isholiday.php?date='.$lastday))
				{ 
					break;
				} 
			}
			$today_s=$today;
			$lastday_s=$lastday;
		}   

		return  $lastday_s;
	}

 	
	   
		
} 
?>