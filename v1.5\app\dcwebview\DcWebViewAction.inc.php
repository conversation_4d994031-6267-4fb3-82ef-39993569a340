<?php 
// require_once('../../../../steelconf_v3/debug.php');
class DcWebViewAction extends AbstractAction
{ 
    public function __construct()
    {
        parent::__construct();
    } 
    public function index($params)
    {	
		
				
         
		//echo "<pre/>";print_r($params);
			$sql="select * from app_session_temp where  GUID ='".$params['GUID']."'  order by  LoginDate DESC  limit 1";
			$res=$this->_dao->getRow($sql);
			$Mid=$res['Mid'];
			$uid=$res['Uid'];
			$type=$params['Type'];
			$username=$res['UserName'];
			$isHuiZong = $params['HuiZong'];
			$SignCS=$res['SignCS'];
			$sql="select * from app_license where Mid='$Mid'   and  mc_type=1 order by CreateDate desc limit 1";
			$res=$this->_dao->getRow($sql);		
			$id=$res['ID'];
			$sql="select * from app_license_privilege  where liD ='".$id."' and privilege!='' and mc_type='1' ";//
			$res=$this->_dao->query($sql); //echo "<pre/>";print_r($res);			
			foreach($res as $key => $value){
				$res[$key]['order']='10';
				$r=explode(',',$value['privilege']);
				$d=explode(',',$value['orderno']);
				if( $r[$type-1]==1){ //判断领导的顺序进行排序 给数组一个新字段					 
					   $res[$key]['order']=$d[$type-1];
				}
			}
			$order=array();
			foreach($res as $re){
				$order[]=$re['order'];
			}
			array_multisort($order,SORT_ASC,$res);//安照order来排序

			foreach($res as $key =>$v){
				$r=explode(',',$v['privilege']);
				if(!array_key_exists($r[$type-1],$GLOBALS['power'])) continue;
				$p_arr[$v['username']]=$r;
				$adminname_uid[$v['username']]=$v['uid'];
				$adminname[$v['username']]=$v['truename'];
			}//echo "<pre/>";print_r($adminname_uid);
			
			
			if($params['curdate']!=''){
				$date=$params['curdate'];
			}else{
				$date = date("Y-m-d");
			}
				
			
			$sql="select * from ng_price_model where modeltype=$type and date='$date' order by id desc limit 1";	
			//echo $sql;
			$modeldb=$this->drcW->getrow($sql);
			if(empty($modeldb)){  
				 $str="<h2 align=center>暂无".date('Y年n月j日',strtotime($date))." 45#Ф50mm碳素结构钢日定价数据!</h2>";
				 $this->assign('str',$str);//exit;
				 $this->assign( "iswj", 1);
			}
			else{
				$modeldb['modelcontent']=html_entity_decode($modeldb['modelcontent'],ENT_QUOTES,'UTF-8');//echo "<pre/>";print_r($modeldb);
				$uidsavebtn=false;
				$uidleadersavebtn=false;
				foreach($p_arr as $key => $v){
					$master[$adminname_uid[$key]]=$v[($type-1)];
					$thisuid=$adminname_uid[$key];
					$result=$this->drcW->query("select id,modelid,modelprice,modelprice_updown,ABS(modelprice_updown) as abs_modelprice_updown,modelpirce_name,date,uid,modeloldprice from ng_price_model_detail where modelprice_type=$type and modelid=".$modeldb["id"]." and uid='".$thisuid."' order by modelid desc,modelpirce_name_sort  asc limit 3");
			 
					if(empty($result)){
							$result=$this->drcW->query("select  0 as id,'' as modelid,'' as modelprice,'' as modelprice_updown,'' as abs_modelprice_updown,modelpirce_name, '' as date,'".$thisuid."' as uid,modeloldprice  from ng_price_model_detail where modelprice_type=$type and modelid=".$modeldb["id"]." and uid='-1' and modelid   order by modelid desc,modelpirce_name_sort  asc limit 3");	 
					}
					if($v[($type-1)]==1){
						$master_leader[]=array(
							'uid'=>$adminname_uid[$key],
							'username'=>$key,
							'power'=>$v[($type-1)],
							'truename'=>$adminname[$key],
							'res'=>$result
							);
						if($adminname_uid[$key]==$uid){
							$uidleadersavebtn=true;
						}
					}else if($v[($type-1)]==2){
						$master_price[]=array(
							'uid'=>$adminname_uid[$key],
							'username'=>$key,
							'power'=>$v[($type-1)],
							'truename'=>$adminname[$key],
							'res'=>$result
							);
						if($adminname_uid[$key]==$uid){
							$uidsavebtn=true;
						}
					}
				}
				$show='';
				//权限为3，市场部人员让他与最后一个分管领导操作相同，这里把登录的uid换成最后一个分管领导的
				$record=$this->drcW->query("SELECT * FROM  `Ng_RecordManage` where modelid='".$modeldb["id"]."'");
				$last=	end($master_leader);
				if(!empty($record)&&$master[$uid]==1&&$uid!=$last['uid']){
					/* if($params['show']!='1'){
						$url = "//".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']."&show=1";
						gourl($url);
					} */
					$show=1;
				}
				if($master[$uid]==3){
						
					$last=	end($master_leader);
						
					$uid=$last['uid'];
					$sql="select * from app_session_temp where  Uid ='".$uid."'  order by  LoginDate DESC  limit 1";
					$m=$this->_dao->getRow($sql);
					$Mid=$m['Mid'];
					$uidleadersavebtn=true;
				}
				if($master[$uid]==4||$master[$uid]==''){
				
					$youke=1;
					$this->assign( "youke", $youke );//决策人进来查看 传的值
				}
				
				
				//---
				foreach($master_price as $k){
					$uid_arr[]=$k['uid'];
				}
				foreach($master_leader as $k2){
					$uid_arr2[]=$k2['uid'];
				}
				$privilege=$master[$uid];//当前用户权限
				
				if($isHuiZong=='1'&&$privilege==''){
					echo "-1";exit;
				}
				
				$this->assign( "params", $params);
				$this->assign( "uid", $uid);
				$this->assign( "mid", $Mid );
				$this->assign( "modeldb", $modeldb );//echo "<pre/>";print_r($modeldb);
				$this->assign( "uidsavebtn", $uidsavebtn );
				$this->assign( "uidleadersavebtn", $uidleadersavebtn );
				$this->assign( "uid_arr", $uid_arr );
				$outlook='0';
				
				
				
				foreach($uid_arr as $key=>$value){
		        $sql="select * from  ng_price_model_detail where uid='$value'
                  and modelid='".$modeldb["id"]."' and modelprice_type =$type ";
				  
				
				  $out=$this->drcW->query($sql);
				  if(empty($out)){
					$outlook='1';
					break;
				  }
				}
				
				$this->assign( "outlook", $outlook);//第一层限制 决策未决策 分管领导不能操作 0表示全部决策 1 表示尚有未决策			
				$shunxu=array_keys($uid_arr2,$uid,true);		
			    $outlook2='0';
				if($shunxu[0]!=0){
				 for($i=0;$i<$shunxu[0];$i++){					
				   $sql="select * from  ng_price_model_detail where uid='$uid_arr2[$i]' and  modelprice_type =$type and modelid='".$modeldb["id"]."'  ";
					$out=$this->drcW->query($sql); 
					if(empty($out)){
						$outlook2='1';
						break;
					}
				}
			   }			   
		$nextmarket='0';
		if($shunxu[0]!=(count($uid_arr2)-1)){
			 $uidpre=$uid_arr2[$shunxu[0]+1];
			 
			 $sql="select * from  ng_price_model_detail where uid='$uidpre'
                  and modelid='".$modeldb["id"]."' and modelprice_type =$type ";
			$out=$this->drcW->query($sql);//如果为空则后一个人未决策 
			if(empty($out)){
				$nextmarket='1';
			}
		}
		
        $shunxu2=0;
		    if($shunxu[0]!=0){
			  $shunxu2=$shunxu[0]-1;//查前面的的人有没有决策
			 }else{
				 $shunxu2=0;
				 }   
				$sql5="select * from  ng_price_model_detail where uid='$uid_arr2[$shunxu2]'
                                     and modelid='".$modeldb["id"]."' and modelprice_type =$type ";
             $preout=$this->drcW->query($sql5);
				
				 $master_leader2=array();
				 if($outlook2=='1'&&empty($preout)){
			       for($i=0;$i<$shunxu[0];$i++){
					$master_leader2[$i]= $master_leader[$i];
				   }
				 }
				 else{
					 for($i=0;$i<=$shunxu[0];$i++){
						$master_leader2[$i]= $master_leader[$i];
				     } 
				 }
				$this->assign( "outlook2", $outlook2);
				$this->assign( "uid_arr2", $uid_arr2 );
				$sql3="select Ismakepricy from ng_price_model where modeltype=$type and id=".$modeldb["id"]." order by id desc limit 1";	
				$res3=$this->drcW->getOne($sql3);
				if($res3=='1'){
				  $this->assign( "zzjc", $res3 );
				  $this->assign( "master_leader", $master_leader );	
				}
				else{						  
					if(in_array($uid,$uid_arr2)){
						//echo $outlook;
						$master_leader3=array();
						if($shunxu[0]=='0'){//第一个特殊处理
					
					// if($outlook=='1'){//表示有决策人未决策
						 
					// }else{	
					    for($i=0;$i<count($uid_arr2);$i++){
						     $sql="select * from  ng_price_model_detail where uid='$uid_arr2[$i]'
                                     and modelid='".$modeldb["id"]."' and modelprice_type =$type ";
				         $out=$this->drcW->query($sql);
						 
						
						  
						  if(empty($out)&&$uid_arr2[$i]!=$uid){//修改1
							  
							  break;
						  }
						 if($outlook!='1'){
						    $master_leader3[$i]= $master_leader[$i];
						   }
						  
					   }
						
							     $sql="select * from  ng_price_model_detail where uid='$uid'
                                     and modelid='".$modeldb["id"]."' and modelprice_type =$type ";
				           $out=$this->drcW->query($sql);
						
						
						
						 if($nextmarket!='1'&&($shunxu[0]!=(count($uid_arr2)-1))){//1 后面那个未决策 0 后面的哪个人已经决策
						 // if(!empty($out)){
							 /*  if($params['show']!=1){
								$url = "//".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']."&show=1";
								goUrl($url);
								
							} */
						 	$show=1;
						 }
						 // }
					// }
					 if($outlook=='1'&&empty($out)){
					 /* if($params['show']!=1){
								 $url = "//".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']."&show=1";
								 goUrl($url);
					        
					       }  */
					 	$show=1;
					 }
					
				     $this->assign( "master_leader", $master_leader3 );//echo "<pre/>";print_r($master_leader3);//分管领导
				}else{//第二个人之后
							
							  $sql4="select * from  ng_price_model_detail where uid='$uid'
                                     and modelid='".$modeldb["id"]."' and modelprice_type =$type ";
						  
						  $out=$this->drcW->query($sql4);
						  
						  if(!empty($out)){
							  
						for($i=0;$i<=$shunxu[0];$i++){
						// $sql="select * from  ng_price_model_detail where uid='$uid_arr2[$i]'
                         // and createtime>='$this_xun_start' and  createtime<='$this_xun_end' and modelprice_type =$type ";
				         // $out=$this->drc->query($sql);   
				     $master_leader2[$i]= $master_leader[$i];
					 	// if(empty($out)){break;}
				                   } 
							  
						  }
							 if($outlook2=='1'&&empty($out)){
							   if(empty($preout)){
								 /* if($params['show']!=1){
								 	$url = "//".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']."&show=1";
									goUrl($url);
								  } */
							   	$show=1;
								}								
							}else{
								
							//表示前面的人已经决策
						 //判断在自己后面的人有没有决策
									 if($nextmarket!='1'&&($shunxu[0]!=(count($uid_arr2)-1))){//1 后面那个未决策 0 后面的哪个人已经决策
									 
									/* 	  if($params['show']!=1){
											  // echo '1111';
											$url = "//".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']."&show=1";
											goUrl($url);
											
										}
 */
									 	$show=1;
										for($i=0;$i<=count($uid_arr2);$i++){
												$sql="select * from  ng_price_model_detail where uid='$uid_arr2[$i]'
													and modelid='".$modeldb["id"]."' and modelprice_type =$type ";
												$out=$this->drcW->query($sql);
									
											  if(empty($out)&&$uid_arr2[$i]!=$uid){
												  break;
												}
												$master_leader2[$i]=$master_leader[$i];
										}
										
										
									 }
								
								
							}
							$this->assign( "master_leader", $master_leader2);
						}
					}
					else{	
						
						$master_leader4=array();
						//决策人的判断
						$outjc='0';
						for($i=0;$i<count($uid_arr2);$i++){
									
								$sql="select * from  ng_price_model_detail where uid='$uid_arr2[$i]'
								  and modelid='".$modeldb["id"]."' and modelprice_type =$type ";
								$out=$this->drcW->query($sql);   
						
								if(!empty($out)){//分管领导有一个决策了 就跳show=1页面
									
									$outjc='1';
									break;
								}
					
						}
						if($outjc=='1'){
							/*  if($params['show']!=1){
									$url = "//".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']."&show=1";
									goUrl($url);
								} */
							$show=1;
						 }	
				        $j=0;
						for($i=0;$i<=count($uid_arr2);$i++){
							
						  $sql="select * from  ng_price_model_detail where uid='$uid_arr2[$i]'
						  and modelid='".$modeldb["id"]."' and modelprice_type =$type ";
						  $out=$this->drcW->query($sql);  
									  
						  if(!empty($out)){
							    $master_leader4[$j]= $master_leader[$i];
						      $j++;
							  }
					
						
						}
						$this->assign( "master_leader", $master_leader4 );//决策人进来查看 传的值
					}
				}
			
				$this->assign( "master_price", $master_price );//echo "<pre/>";print_r($master_price);
				//$this->assign( "master_leader", $master_leader );//echo "<pre/>";print_r($master_leader);
				$this->assign( "city_arr", $GLOBALS['city'] );
				$this->assign( "save_city", $GLOBALS['save_city'] );//echo "<pre/>";print_r($GLOBALS['save_city']);
				$this->assign( "privilege", $privilege );
				$this->assign( "type", $type );
				$this->assign( "citynum", count($GLOBALS['save_city']) );
				$sql="select * from ng_price_model_detail where modelprice_type=$type and modelid=".$modeldb["id"]." and uid=-1  order by modelid desc,modelpirce_name_sort  asc limit 1"; 
				$res=$this->drcW->query($sql);
				for($i=0;$i<count($res);$i++){				
					$res[$i]['abs_modelprice_updown']=$res[$i]['modelprice_updown'];
					if($res[$i]['modelprice_updown']){
						$res[$i]['abs_modelprice_updown']=abs($res[$i]['modelprice_updown']);
					}
				}
				foreach($res as $key =>$v){
					if($v['modelpirce_name']=='45#Ф50mm碳素结构钢南京市场日定价'){
						$res[$key]['modelpirce_name']='南京市场';
					}
					elseif($v['modelpirce_name']=='45#Ф50mm碳素结构钢无锡市场日定价'){
						$res[$key]['modelpirce_name']='无锡市场';
					}
					elseif($v['modelpirce_name']=='45#Ф50mm碳素结构钢杭州市场日定价'){
						$res[$key]['modelpirce_name']='杭州市场';
					}
				} 
				$this->assign("isHuiZong",$isHuiZong);//是否是汇总使用  1=汇总
				
				$this->assign( "modeldetail", $res );
			 $style_url= dirname("//".$_SERVER['SERVER_NAME'].$_SERVER['PHP_SELF']);
			 $sql="select Ismakepricy from ng_price_model where modeltype=$type and date='$date'  order by id desc limit 1";			
			 $this->assign( "style_url", $style_url );
			 $res=$this->drcW->getOne($sql);//echo $res."<br/>";
			 if($res=='1' ){			
				/* $url = "//".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']."&show=1";
				gourl($url); */
			 	$show=1;
			 }
			 $this->assign( "show", $show );
		}
		
		if($modeldb['is_wangqi']!='1') {
			 
			
			$up_wq='update ng_price_model set  is_wangqi=1 where id="'.$modeldb['id'].'" and modeltype="'.$type.'"   ';
			 
			$this->drcW->execute($up_wq);
		}
		$this->assign( "istjgr", 0);
	
		
			if($modeldb['pricycontent']==''&&$_GET['save_dcvr']!=1){
				
				$url = "http://".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']."&save_dcvr=1";
				

				include '/usr/local/www/libs/phpQuery/phpQuery.php';
				phpQuery::newDocumentFile($url);
				$content=pq("html")->html();
				$up_wq='update ng_price_model set pricycontent ="'.htmlentities($content,ENT_QUOTES,"UTF-8").'" where id="'.$modeldb['id'].'" and modeltype="'.$type.'" and is_wangqi=1  ';
				$this->drcW->execute($up_wq);
				$this->assign( "istjgr", 1);
			}
			
		
	
		
    } 
	
	public function save_price($params){
	  $uid=$params['uid'];		
	  $uid_arr=$params['arr'];//决策人
	  $uid_arr2=$params['arr2'];//分管领导	
	  $UserType='0';
	  if(in_array($uid,$uid_arr)){
		    $UserType='1';		  
	  }elseif(in_array($uid,$uid_arr2)){
		   $UserType='2';		 
	  }
		
		$mid=$params['Mid'];
		$type=$params['type'];
		$price=$params['p_tjr'];
		$city_price=$params['price_tjr'];
		$oldprice=$params['oldprice_tjr'];
		$updown=$params['updown_tjr']; 
		$modeldbs=$params['modeldb_tjr'];
		//$params['privilege']==1 ?  $privilege=1: $privilege=0;
		$endmarket='0';
		if(end($uid_arr2)==$uid){
			$privilege=1;			
		}
		else{
			$endmarket='1';
			$privilege=0;
		}
		foreach($price as $key=>$i){
				$citykey[]=$key;
		}
		
		$sql="select date from ng_price_model where id=$modeldbs";
		$dates=$this->drcW->getone($sql);
		
		
		//$sql="select * from ng_price_model_detail where modelid=$modeldbs and mid=$mid and uid=$uid and modelprice_type=$type and modelpirce_name in ('45#Ф50mm碳素结构钢南京市场日定价','45#Ф50mm碳素结构钢无锡市场日定价','45#Ф50mm碳素结构钢杭州市场日定价')";
			$sql="select * from ng_price_model_detail where modelid=$modeldbs and mid=$mid and uid=$uid and modelprice_type=$type and modelpirce_name ='45#Ф50mm碳素结构钢日定价'";   
		$res=$this->drcW->query($sql);//echo "<pre/>";print_r($res);exit;
		if(empty($res)){
			$sql="insert into ng_price_model_detail (modelid,uid,Mid,modelprice_type,Ismakepricy_men,modelprice,modeloldprice,modelprice_updown,modelpirce_name,modelpirce_name_sort,createtime,UserType,date) values ";
			for($i=1;$i<=count($price);$i++){
				$sql.="($modeldbs,$uid,$mid,$type,$privilege,'".$price[$i]."','".$oldprice[$i]."','";
				if($updown[$i]=='-1'){
					$sql.=-(($city_price[$i]/10)*10);
				}
				else{
					$sql.=(($city_price[$i]/10)*10);
				}
				//$sql.="','".$GLOBALS['save_city2'][$citykey[$i-1]]."','$i',NOW(),'".$UserType."','".$date."'),";
				$sql.="','".$GLOBALS['save_city2'][1]."','$i',NOW(),'".$UserType."','".$date."'),";
			}
			$sql=substr($sql,0,-1);
			$this->drcW->execute($sql);
		}
		else{


			for($i=1;$i<=count($price);$i++){
					$sql="update ng_price_model_detail set UserType='".$UserType."', Ismakepricy_men='".$privilege."' , modelprice='".$price[$i]."' ,modeloldprice='".$oldprice[$i]."' ,modelprice_updown='";
					if($updown[$i]=='-1'){
						$sql.=-(($city_price[$i]/10)*10);
					}
					else{
						$sql.=(($city_price[$i]/10)*10);
					}
					$sql.="' , createtime=NOW()
							where modelid=$modeldbs and	mid=$mid and uid=$uid and modelprice_type=$type and modelpirce_name='".$GLOBALS['save_city2'][$citykey[$i-1]]."' 
							and modelid='".$modeldbs."'  ";
							
							
					$this->drcW->execute($sql);							
				}
				
			}

			$url = "http://".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']."&show=1&iswj=1&save_dcvr=1";
			include '/usr/local/www/libs/phpQuery/phpQuery.php';
			phpQuery::newDocumentFile($url);
			$content=pq("html")->html();
			$up_wq='update ng_price_model set pricycontent ="'.htmlentities($content,ENT_QUOTES,"UTF-8").'" where id="'.$modeldbs.'" and modeltype="'.$type.'"  ';
			$this->drcW->execute($up_wq);
		if($params['saveone_tjr']){
			$url = $_SERVER['HTTP_REFERER'];
			goUrl($url);
		}
		if($params['saveAll_tjr']){ 
			// $endmarket='0';
	        // $sql="select * from app_license_privilege  where  privilege!='' and mc_type='1' ";
			// $res=$this->_dao->query($sql);//res值得排序即是分管领导的排序		
			// foreach($res as $key => $value){
				// $r=explode(',',$value['privilege']);
				// if( $r[$type-1]==1){
		            // $uids=$value['uid'];
					// $sql2="select * from  ng_price_model_detail where uid='$uids'
                       // and modelid='".$modeldb["id"]."' and modelprice_type=$type";
				    // $out=$this->drcW->query($sql2); 				
					// if(empty($out)){
						// $endmarket='1';
						// break;
					// }
				// }	
			// }	
			
			if($endmarket!='1'){
			
				//只有最后一个人保存了才执行 这些
				$url = "http://".$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI']."&show=1&save_dcvr=1";			
				$content=pq("html")->html();
				$sql='update ng_price_model set pricycontent ="'.htmlentities($content,ENT_QUOTES,"UTF-8").'",ismakepricy=1,makepricytime="'.date("Y-m-d H:i:s",time()).'"
						where id="'.$modeldbs.'" and modeltype="'.$type.'"  ';
				$this->drcW->execute($sql);
				$url = $_SERVER['HTTP_REFERER'];
			goUrl($url);
			}
			else{
				 $url = $_SERVER['HTTP_REFERER'];
				 goUrl($url);
				
			}
		}
		
	}

} 

?>