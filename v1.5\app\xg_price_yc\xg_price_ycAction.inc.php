<?php
ini_set("memory_limit", "2048M");
require_once("/etc/steelconf/config/isholiday.php");
//include_once( FRAME_LIB_DIR ."/action/AbstractAction.inc.php" );
include_once( APP_DIR."/xg_price_yc/xg_price_ycBaseAction.inc.php" );
$GLOBALS['priceid6'] = "'942023','943012','943112','944312','945031','118210','678411','D28311','L18710','418910','539422'";
$GLOBALS['priceid7'] = "'1886103'";
$GLOBALS['QH_TYPES'] = "'SHQHDAY_4','SHQHDAY_99','SHQHDAY_20','SHQHDAY_9','SHQHDAY_19','SHQHDAY_22','SHQHDAY_23'";
$GLOBALS['QH_TYPES_LIST'] = array('SHQHDAY_4','SHQHDAY_99','SHQHDAY_20','SHQHDAY_9','SHQHDAY_19','SHQHDAY_22','SHQHDAY_23');
$GLOBALS['XUN_ONLY_ID_LIST'] = array(
    array("onlyid"=>'d73885280dc9167442ab5bf8b86b81ec',"gcid"=>"132"),
    array("onlyid"=>'c09e13f0b069dd28bcf57c8e222d4b2d',"gcid"=>"205"),
    array("onlyid"=>'13614b65763a32d52c3dfb8e96d52c64',"gcid"=>"204"),
    array("onlyid"=>'c9a0fd2fea8035acb55c1d787fde587e',"gcid"=>"204"),
);
$GLOBALS['STOCK_TYPE_STR'] = "'1', '2', '3', '4', '5'";
$GLOBALS['YUAN_YOU_BM'] = "********";
$GLOBALS['XG_IP'] = "https://*************:14007";
define( "DC_DOMAIN_URL","https://dc.steelhome.com");
define( "DOMAIN_URL",DC_DOMAIN_URL."/v1.5");
define( "DATACENTER_GUID","1f853150891e11eabebc001d09719b40");
define( "DATACENTER_SIGNCS","8b46e267268907263bbec91ec65915f4");

define( "PRICE_DATACENTER_GUID","testaccount");
define( "PRICE_DATACENTER_SIGNCS","123456");

class  xg_price_ycAction extends xg_price_ycBaseAction
{
    public function __construct()
    {
        parent::__construct();
    } 
    
    
 	public function day_price_yc($params){
        $day = isset($params["sdate"]) == true?$params["sdate"]:date("Y-m-d");
        // $isholiday = file_get_contents('https://holiday.steelhome.com/isholiday.php?date='.$day);
        // if($isholiday == "1"){
        //     return false;
        // }

        // $lastday = $this->get_xhdate($day);
        $lastday = $day;
        $this->assign("mode",$params['mode']);
        $day2 = date('n月j日',strtotime($day));
        $lastday2 = date('n月j日',strtotime($lastday));
        $this->assign( "day2", $day2 );
        $this->assign( "lastday2", $lastday2 );
           
        $xg_accuracy_id = $params["id"];
        //取前日复盘数据
        $day_yc_data = $this->_dao->get_yc_data(1,$lastday,$lastday);
        $date_array["this_start_date"] = $lastday;
        $date_array["this_end_date"] = $lastday;
        $sy_sdate = date("Y-m-d",strtotime($lastday."-1 month"));
        $date_array["last_start_date"] = $sy_sdate;
        $date_array["last_end_date"] = $lastday;
        $day_yc_data_zql = $this->_dao->getForecastInfo($date_array,1);
        // echo "<pre/>";print_r($day_yc_data);
        //正确率
        // $zql_str = $this->_dao->get_xg_accurac(1,$sy_sdate,$day);
        // $this->assign( "zql_str", $zql_str );
        

        $lastday = $this->get_xhdate($day);
        //目标市场钢材价格变化情况
        
        $price6_arr = $this->hq_price_6($lastday,$day,$GLOBALS['priceid6']);
        $price7_arr = $this->hq_price_7_week($lastday,$day,$GLOBALS['priceid7']);
        //print_r($price6_arr);

        //矿煤焦钢期货主力合约及国际大宗商品、外汇市场收盘价
		$qh_data = $this->get_qh_data($lastday,$day,$GLOBALS['QH_TYPES']);
		$yy_data = $this->getNiuYuanyou($lastday,$day,$GLOBALS['YUAN_YOU_BM']);
		$my_data = $this->getMeiYuanShpi($lastday,$day);
		$rmb_data = $this->getRMBShpi($lastday,$day);
		

        //预测复盘table
        $yc_fp_table_str = '<tr>
                                <td>预测方向</td>
                                <td>'.$day_yc_data["942023"]["ycfx"].'</td>
                                <td>'.$day_yc_data["943012"]["ycfx"].'</td>
                                <td>'.$day_yc_data["943112"]["ycfx"].'</td>
                                <td>'.$day_yc_data["944312"]["ycfx"].'</td>
                            </tr>
                            <tr>
                                <td>预测幅度</td>
                                <td>'.$day_yc_data["942023"]["ycfd"].'</td>
                                <td>'.$day_yc_data["943012"]["ycfd"].'</td>
                                <td>'.$day_yc_data["943112"]["ycfd"].'</td>
                                <td>'.$day_yc_data["944312"]["ycfd"].'</td>
                            </tr>
                            <tr>
                                <td>市场实际</td>
                                <td>'.$price6_arr["tprice"]["942023"]."&nbsp;&nbsp;".$price6_arr["zd"]["942023"].'</td>
                                <td>'.$price6_arr["tprice"]["943012"]."&nbsp;&nbsp;".$price6_arr["zd"]["943012"].'</td>
                                <td>'.$price6_arr["tprice"]["943112"]."&nbsp;&nbsp;".$price6_arr["zd"]["943112"].'</td>
                                <td>'.$price6_arr["tprice"]["944312"]."&nbsp;&nbsp;".$price6_arr["zd"]["944312"].'</td>
                            </tr>
                            <tr>
                            <td>方向正确率</td>
                                <td>'.$day_yc_data_zql["螺纹钢"]["fx_percent"].'</td>
                                <td>'.$day_yc_data_zql["中厚板"]["fx_percent"].'</td>
                                <td>'.$day_yc_data_zql["热轧板卷"]["fx_percent"].'</td>
                                <td>'.$day_yc_data_zql["冷轧板卷"]["fx_percent"].'</td>
                            </tr>
                            <tr>
                                <td>方向幅度正确率</td>
                                <td>'.$day_yc_data_zql["螺纹钢"]["fx_fd_percent"].'</td>
                                <td>'.$day_yc_data_zql["中厚板"]["fx_fd_percent"].'</td>
                                <td>'.$day_yc_data_zql["热轧板卷"]["fx_fd_percent"].'</td>
                                <td>'.$day_yc_data_zql["冷轧板卷"]["fx_fd_percent"].'</td>
                            </tr>';

		//调价信息
		$tiaojia_data = $this->get_gc_tiaojia($GLOBALS['XUN_ONLY_ID_LIST'],$day);
		$tiaojia_table_str = '<tr>
		<td>方大特钢</td>
		<td>'.$tiaojia_data["d73885280dc9167442ab5bf8b86b81ec"]["variety"].'</td>
		<td>'.$tiaojia_data["d73885280dc9167442ab5bf8b86b81ec"]["specification"].'</td>
		<td>'.$tiaojia_data["d73885280dc9167442ab5bf8b86b81ec"]["material"].'</td>
		<td>'.$tiaojia_data["d73885280dc9167442ab5bf8b86b81ec"]["changerate_tax"].'</td>
		<td>'.$tiaojia_data["d73885280dc9167442ab5bf8b86b81ec"]["the_price_tax"].'</td>
		<td>'.$tiaojia_data["d73885280dc9167442ab5bf8b86b81ec"]["ftime"].'</td></tr>';

		$tiaojia_table_str .= '<tr>
		<td>湘钢</td>
		<td>'.$tiaojia_data["c09e13f0b069dd28bcf57c8e222d4b2d"]["variety"].'</td>
		<td>'.$tiaojia_data["c09e13f0b069dd28bcf57c8e222d4b2d"]["specification"].'</td>
		<td>'.$tiaojia_data["c09e13f0b069dd28bcf57c8e222d4b2d"]["material"].'</td>
		<td>'.$tiaojia_data["c09e13f0b069dd28bcf57c8e222d4b2d"]["changerate_tax"].'</td>
		<td>'.$tiaojia_data["c09e13f0b069dd28bcf57c8e222d4b2d"]["the_price_tax"].'</td>
		<td>'.$tiaojia_data["c09e13f0b069dd28bcf57c8e222d4b2d"]["ftime"].'</td></tr>';

		$tiaojia_table_str .= '<tr>
		<td>涟钢</td>
		<td>'.$tiaojia_data["13614b65763a32d52c3dfb8e96d52c64"]["variety"].'</td>
		<td>'.$tiaojia_data["13614b65763a32d52c3dfb8e96d52c64"]["specification"].'</td>
		<td>'.$tiaojia_data["13614b65763a32d52c3dfb8e96d52c64"]["material"].'</td>
		<td>'.$tiaojia_data["13614b65763a32d52c3dfb8e96d52c64"]["changerate_tax"].'</td>
		<td>'.$tiaojia_data["13614b65763a32d52c3dfb8e96d52c64"]["the_price_tax"].'</td>
		<td>'.$tiaojia_data["13614b65763a32d52c3dfb8e96d52c64"]["ftime"].'</td></tr>';

		$tiaojia_table_str .= '<tr>
		<td>涟钢</td>
		<td>'.$tiaojia_data["c9a0fd2fea8035acb55c1d787fde587e"]["variety"].'</td>
		<td>'.$tiaojia_data["c9a0fd2fea8035acb55c1d787fde587e"]["specification"].'</td>
		<td>'.$tiaojia_data["c9a0fd2fea8035acb55c1d787fde587e"]["material"].'</td>
		<td>'.$tiaojia_data["c9a0fd2fea8035acb55c1d787fde587e"]["changerate_tax"].'</td>
		<td>'.$tiaojia_data["c9a0fd2fea8035acb55c1d787fde587e"]["the_price_tax"].'</td>
		<td>'.$tiaojia_data["c9a0fd2fea8035acb55c1d787fde587e"]["ftime"].'</td></tr>';

        //市场热点及点评
        $xg_marketNews = html_entity_decode($this->_dao->get_xg_marketNews(1,$day,$day,0));
        $this->assign( "xg_marketNews", $xg_marketNews);
        $tomorrow = $this->get_xhdate2($day);
        $this->assign("title",date("Y年n月j日",strtotime($tomorrow))."日预测");
		$day = date('m-d',strtotime($day));
		$lastday = date('n月j日',strtotime($lastday));

		//期货表格
		$qh_table_str = '<tr>
				<td>'.$day2.'</td>
                <td>'.$qh_data["tprice"]["SHQHDAY_4"].'</td>
                <td>'.$qh_data["tprice"]["SHQHDAY_99"].'</td>
                <td>'.$qh_data["tprice"]["SHQHDAY_20"].'</td>
                <td>'.$qh_data["tprice"]["SHQHDAY_9"].'</td>
                <td>'.$qh_data["tprice"]["SHQHDAY_19"].'</td>
                <td>'.$qh_data["tprice"]["SHQHDAY_22"].'</td>
                <td>'.$qh_data["tprice"]["SHQHDAY_23"].'</td>
                <td>'.$yy_data["tprice"].'</td>
                <td>'.$my_data["tprice"].'</td>
                <td>'.$rmb_data["tprice"].'</td>
            </tr>
            <tr>
                <td>'.$lastday.'</td>
				<td>'.$qh_data["lprice"]["SHQHDAY_4"].'</td>
                <td>'.$qh_data["lprice"]["SHQHDAY_99"].'</td>
                <td>'.$qh_data["lprice"]["SHQHDAY_20"].'</td>
                <td>'.$qh_data["lprice"]["SHQHDAY_9"].'</td>
                <td>'.$qh_data["lprice"]["SHQHDAY_19"].'</td>
                <td>'.$qh_data["lprice"]["SHQHDAY_22"].'</td>
                <td>'.$qh_data["lprice"]["SHQHDAY_23"].'</td>
                <td>'.$yy_data["lprice"].'</td>
				<td>'.$my_data["lprice"].'</td>
                <td>'.$rmb_data["lprice"].'</td>
            </tr>
            <tr>
                <td>涨跌</td>
                <td>'.$qh_data["zd"]["SHQHDAY_4"].'</td>
                <td>'.$qh_data["zd"]["SHQHDAY_99"].'</td>
                <td>'.$qh_data["zd"]["SHQHDAY_20"].'</td>
                <td>'.$qh_data["zd"]["SHQHDAY_9"].'</td>
                <td>'.$qh_data["zd"]["SHQHDAY_19"].'</td>
                <td>'.$qh_data["zd"]["SHQHDAY_22"].'</td>
                <td>'.$qh_data["zd"]["SHQHDAY_23"].'</td>
                <td>'.$yy_data["zd"].'</td>
                <td>'.$my_data["zd"].'</td>
                <td>'.$rmb_data["zd"].'</td>
            </tr>';

			//原燃料
			$yrl_table_str = '<tr>
					<td>'.$day2.'</td>
					<td>'.$price7_arr["tprice"]["1886103"].'</td>
					<td>'.$price6_arr["tprice"]["118210"].'</td>
					<td>'.$price6_arr["tprice"]["678411"].'</td>
					<td>'.$price6_arr["tprice"]["D28311"].'</td>
					<td>'.$price6_arr["tprice"]["L18710"].'</td>
					<td>'.$price6_arr["tprice"]["418910"].'</td>
					<td>'.$price6_arr["tprice"]["539422"].'</td>
				</tr>
				<tr>
					<td>'.$lastday.'</td>
					<td>'.$price7_arr["lprice"]["1886103"].'</td>
					<td>'.$price6_arr["lprice"]["118210"].'</td>
					<td>'.$price6_arr["lprice"]["678411"].'</td>
					<td>'.$price6_arr["lprice"]["D28311"].'</td>
					<td>'.$price6_arr["lprice"]["L18710"].'</td>
					<td>'.$price6_arr["lprice"]["418910"].'</td>
					<td>'.$price6_arr["lprice"]["539422"].'</td>
				</tr>
				<tr>
					<td>涨跌</td>
					<td>'.$price7_arr["zd"]["1886103"].'</td>
					<td>'.$price6_arr["zd"]["118210"].'</td>
					<td>'.$price6_arr["zd"]["678411"].'</td>
					<td>'.$price6_arr["zd"]["D28311"].'</td>
					<td>'.$price6_arr["zd"]["L18710"].'</td>
					<td>'.$price6_arr["zd"]["418910"].'</td>
					<td>'.$price6_arr["zd"]["539422"].'</td>
				</tr>';

			//钢材
			$gc_table_str = '<tr>
				<td>'.$day2.'</td>
				<td>'.$price6_arr["tprice"]["942023"].'</td>
				<td>'.$price6_arr["tprice"]["943012"].'</td>
				<td>'.$price6_arr["tprice"]["943112"].'</td>
				<td>'.$price6_arr["tprice"]["944312"].'</td>
				<td>'.$price6_arr["tprice"]["945031"].'</td>
			</tr>
			<tr>
				<td>'.$lastday.'</td>
				<td>'.$price6_arr["lprice"]["942023"].'</td>
				<td>'.$price6_arr["lprice"]["943012"].'</td>
				<td>'.$price6_arr["lprice"]["943112"].'</td>
				<td>'.$price6_arr["lprice"]["944312"].'</td>
				<td>'.$price6_arr["lprice"]["945031"].'</td>
			</tr>
			<tr>
				<td>涨跌</td>
				<td>'.$price6_arr["zd"]["942023"].'</td>
				<td>'.$price6_arr["zd"]["943012"].'</td>
				<td>'.$price6_arr["zd"]["943112"].'</td>
				<td>'.$price6_arr["zd"]["944312"].'</td>
				<td>'.$price6_arr["zd"]["945031"].'</td>
			</tr>';

        //明日预测数据
        $day_yc_data2 = $this->_dao->get_yc_data(1,$tomorrow,$tomorrow);
        $tomorrow = date('n月j日',strtotime($tomorrow));
        //明日预测数据table
        $yc_fp_table_str2 = '<tr>
                                <td>预测方向</td>
                                <td>'.$day_yc_data2["942023"]["ycfx"].'</td>
                                <td>'.$day_yc_data2["943012"]["ycfx"].'</td>
                                <td>'.$day_yc_data2["943112"]["ycfx"].'</td>
                                <td>'.$day_yc_data2["944312"]["ycfx"].'</td>
                            </tr>
                            <tr>
                                <td>预测幅度</td>
                                <td>'.$day_yc_data2["942023"]["ycfd"].'</td>
                                <td>'.$day_yc_data2["943012"]["ycfd"].'</td>
                                <td>'.$day_yc_data2["943112"]["ycfd"].'</td>
                                <td>'.$day_yc_data2["944312"]["ycfd"].'</td>
                            </tr>';
		$style_url= dirname("//".$_SERVER['SERVER_NAME'].$_SERVER['PHP_SELF']);
		$this->assign( "day", $day );
		$this->assign( "lastday", $lastday );
		$this->assign( "tomorrow", $tomorrow );
		$this->assign( "price6_arr", $price6_arr );
		$this->assign( "price7_arr", $price7_arr );
		$this->assign( "qh_table_str", $qh_table_str );
		$this->assign( "tiaojia_table_str", $tiaojia_table_str );
		$this->assign( "yrl_table_str", $yrl_table_str );
		$this->assign( "gc_table_str", $gc_table_str );
        $this->assign( "yc_fp_table_str", $yc_fp_table_str );
        $this->assign( "yc_fp_table_str2", $yc_fp_table_str2 );
    }

	function month_price_yc($params){
        $this->assign("mode",$params['mode']);
		// print_r($params);
		//月预测复盘
        $sdate = isset($params["sdate"]) == true?$params["sdate"]:date("Y-m-d");
        $edate = isset($params["edate"]) == true?$params["edate"]:date("Y-m-d");
        

        $date_array = array();
        $date_array["this_start_date"] = $sdate;
        $date_array["this_end_date"] = $edate;
        $sy_sdate = date('Y-m-01',strtotime("$sdate -1 month"));
        $sy_edate = date('Y-m-d', strtotime("$sdate -1 day"));
        $date_array["last_start_date"] = $sy_sdate;
        $date_array["last_end_date"] = $sy_edate;
        $yue_yc_data = $this->_dao->getForecastInfo($date_array,3);

        //上月均价及月末价
        // $sy_sdate = date('Y-m-01',strtotime("$sdate -1 month"));
        // $sy_edate = date('Y-m-d', strtotime("$sdate -1 day"));
        // $sy_date_arr = array();
        // $sy_date_arr["this_start_date"] = $sy_sdate;
        // $sy_date_arr["this_end_date"] = $sy_edate;
        // $sy_yc_data = $this->_dao->getForecastInfo($sy_date_arr,3);

        //正确率
        // $zql_str = $this->_dao->get_xg_accurac(3,$sdate,$edate);
        // $this->assign( "zql_str", $zql_str );
        // $zql_str = $yue_yc_data["content"];
        // print_r($yue_yc_data);

        //钢价变动原因
        $xg_marketNews = html_entity_decode($this->_dao->get_xg_marketNews(3,$sdate,$edate,1));
        $this->assign( "xg_marketNews", $xg_marketNews );
        
        $month = date('n',strtotime($sdate));
        $this->assign( "month", $month );
        $yc_fp_table_str = '<tr>
                                <td>预测方向</td>
                                <td>'.$yue_yc_data["螺纹钢"]["yc_direction"].'</td>
                                <td>'.$yue_yc_data["中厚板"]["yc_direction"].'</td>
                                <td>'.$yue_yc_data["热轧板卷"]["yc_direction"].'</td>
                                <td>'.$yue_yc_data["冷轧板卷"]["yc_direction"].'</td>
                            </tr>
                            <tr>
                                <td>预测幅度</td>
                                <td>'.$yue_yc_data["螺纹钢"]["yc_fudu"].'</td>
                                <td>'.$yue_yc_data["中厚板"]["yc_fudu"].'</td>
                                <td>'.$yue_yc_data["热轧板卷"]["yc_fudu"].'</td>
                                <td>'.$yue_yc_data["冷轧板卷"]["yc_fudu"].'</td>
                            </tr>
                            <tr>
                                <td>月末价涨跌</td>
                                <td>'.$yue_yc_data["螺纹钢"]["last_price"]."&nbsp;".$yue_yc_data["螺纹钢"]["last_price_zd"].'</td>
                                <td>'.$yue_yc_data["中厚板"]["last_price"]."&nbsp;".$yue_yc_data["中厚板"]["last_price_zd"].'</td>
                                <td>'.$yue_yc_data["热轧板卷"]["last_price"]."&nbsp;".$yue_yc_data["热轧板卷"]["last_price_zd"].'</td>
                                <td>'.$yue_yc_data["冷轧板卷"]["last_price"]."&nbsp;".$yue_yc_data["冷轧板卷"]["last_price_zd"].'</td>
                            </tr>
                            <tr>
                                <td>月均价涨跌</td>
                                <td>'.$yue_yc_data["螺纹钢"]["avg_price"]."&nbsp;".$yue_yc_data["螺纹钢"]["avg_price_zd"].'</td>
                                <td>'.$yue_yc_data["中厚板"]["avg_price"]."&nbsp;".$yue_yc_data["中厚板"]["avg_price_zd"].'</td>
                                <td>'.$yue_yc_data["热轧板卷"]["avg_price"]."&nbsp;".$yue_yc_data["热轧板卷"]["avg_price_zd"].'</td>
                                <td>'.$yue_yc_data["冷轧板卷"]["avg_price"]."&nbsp;".$yue_yc_data["冷轧板卷"]["avg_price_zd"].'</td>
                            </tr>
                            <tr>
                                <td>方向正确率</td>
                                <td>'.$yue_yc_data["螺纹钢"]["fx_percent"].'</td>
                                <td>'.$yue_yc_data["中厚板"]["fx_percent"].'</td>
                                <td>'.$yue_yc_data["热轧板卷"]["fx_percent"].'</td>
                                <td>'.$yue_yc_data["冷轧板卷"]["fx_percent"].'</td>
                            </tr>
                            <tr>
                                <td>方向幅度正确率</td>
                                <td>'.$yue_yc_data["螺纹钢"]["fx_fd_percent"].'</td>
                                <td>'.$yue_yc_data["中厚板"]["fx_fd_percent"].'</td>
                                <td>'.$yue_yc_data["热轧板卷"]["fx_fd_percent"].'</td>
                                <td>'.$yue_yc_data["冷轧板卷"]["fx_fd_percent"].'</td>
                            </tr>';
                            
        $this->assign( "yc_fp_table_str", $yc_fp_table_str );

        $edate = date('Y-m-d',strtotime("$edate +1 month"));
		//1、我国主要宏观经济数据预测
		//固定资产投资取L2Data表：L2id=140 and L2OrderNo=40
		$gdzc_data = $this->get_hgData(140,40,"",$edate);

		//房地产开发投资: L2id=141 and L2OrderNo=43
		$fdc_data = $this->get_hgData(141,43,"",$edate);

		//基础设施建设投资: L2id=203 and L2OrderNo=42
		$jcjs_data = $this->get_hgData(203,42,"",$edate);

		//制造业固定资产投资: L2id=204 and L2OrderNo=41
		$zzy_data = $this->get_hgData(204,41,"",$edate);

        $jjsj_str =  "";
        $i = 0;
        foreach($gdzc_data as $key => $item){
            $jjsj_str .= "<tr>
                              <td>".$gdzc_data[$key]["DATE2"]."</td>
                              <td>".$gdzc_data[$key]["D3"]."</td>
                              <td>".$gdzc_data[$key]["D4"]."</td>
                              <td>".$fdc_data[$key]["D3"]."</td>
                              <td>".$fdc_data[$key]["D4"]."</td>
                              <td>".$jcjs_data[$key]["D3"]."</td>
                              <td>".$jcjs_data[$key]["D4"]."</td>
                              <td>".$zzy_data[$key]["D3"]."</td>
                              <td>".$zzy_data[$key]["D4"]."</td>
                          <tr>";
            
            $i++;
            if($i == 13){
                break;
            }
        }
        $this->assign( "jjsj_str", $jjsj_str );

		// 社会消费品零售总额取L2Data表：L2id=144 and L2OrderNo=45
		$shxfp_data = $this->get_hgData(144,45,"",$edate);
		// 外贸出口（人民币值） ：L2id=547 and L2OrderNo=13
		$wmck_rmb_data = $this->get_hgData(547,13,"",$edate);
		// 工业增加值 ：L2id=142 and L2OrderNo=44取D1和D2
		$gyzjz_data = $this->get_hgData(142,44,"",$edate);
		// 制造业PMI指数 ：L2id=152 and L2OrderNo=1 取D1
		$zzy_pmi_data = $this->get_hgData(152,1,"",$edate);

        $jjsj_str_2 =  "";
        $i = 0;
        foreach($shxfp_data as $key => $item){
            $jjsj_str_2 .= "<tr>
                              <td>".$shxfp_data[$key]["DATE2"]."</td>
                              <td>".$shxfp_data[$key]["D3"]."</td>
                              <td>".$shxfp_data[$key]["D4"]."</td>
                              <td>".$wmck_rmb_data[$key]["D3"]."</td>
                              <td>".$wmck_rmb_data[$key]["D4"]."</td>
                              <td>".$gyzjz_data[$key]["D1"]."</td>
                              <td>".$gyzjz_data[$key]["D2"]."</td>
                              <td>".$shxfp_data[$key]["DATE2"]."</td>
                              <td>".$zzy_pmi_data[$key]["D1"]."</td>
                          <tr>";
                    
            $i++;
            if($i == 13){
                break;
            }
        }
        $this->assign( "jjsj_str_2", $jjsj_str_2 );

		//2、我国主要财政金融数据预测
		// M2取L2Data表：L2id=216 and L2OrderNo=2 取D1 M1:取上述条件查出来的D2字段
		$m1_m2_data = $this->get_hgData(216,2,"",$edate);
		// 新增社融：L2id=235 and L2OrderNo=4的D1 新增贷款取D2
		$xzsr_data = $this->get_hgData(235,4,"",$edate);
		// 财政收支同比增幅收入：L2id=233 and L2OrderNo=11取D3 
		$czsz_sr_data = $this->get_hgData(233,11,"",$edate);
		// 财政收支同比增幅支出：L2id=234 and L2OrderNo=12取D3
		$czsz_zc_data = $this->get_hgData(234,12,"",$edate);
		// 地方债：L2id=677 and L2OrderNo=15地方债取D1 专项债取：D2
		$dfz_data = $this->get_hgData(677,15,"",$edate);

        $czjrsj_str = "";
        $i = 0;
        foreach($m1_m2_data as $key => $item){
            $czjrsj_str .= "<tr>
                              <td>".$m1_m2_data[$key]["DATE2"]."</td>
                              <td>".$m1_m2_data[$key]["D1"]."</td>
                              <td>".$m1_m2_data[$key]["D2"]."</td>
                              <td>".$xzsr_data[$key]["D1"]."</td>
                              <td>".$xzsr_data[$key]["D2"]."</td>
                              <td>".$czsz_sr_data[$key]["D3"]."</td>
                              <td>".$czsz_zc_data[$key]["D3"]."</td>
                              <td>".$dfz_data[$key]["D1"]."</td>
                              <td>".$dfz_data[$key]["D2"]."</td>
                          <tr>";
            $i++;
            if($i == 13){
                break;
            }
        }
        $this->assign( "czjrsj_str", $czjrsj_str );

		// 三、国内钢材市场供求形势预测
		// 粗钢：L2id=109 and L2OrderNo=11当月产量：D1同比增幅：D3
		$gq_cg_data =  $this->get_hgData(109,11,"",$edate);
		// 钢材：L2id=189 and L2OrderNo=13当月产量：D1同比增幅：D3
		$gq_gc_data =  $this->get_hgData(189,13,"",$edate);
		// L2id=190 and L2OrderNo=17  粗钢日均产量取D1  钢材日均产量D3
		$rjcl_gc_cg_data = $this->get_hgData(190,17,"",$edate);

        $gqxs_str = "";
        $i = 0;
        foreach($gq_cg_data as $key => $item){
            $gqxs_str .= "<tr>
                              <td>".$gq_cg_data[$key]["DATE2"]."</td>
                              <td>".$gq_cg_data[$key]["D1"]."</td>
                              <td>".$gq_cg_data[$key]["D3"]."</td>
                              <td>".$rjcl_gc_cg_data[$key]["D1"]."</td>
                              <td>".$gq_gc_data[$key]["D1"]."</td>
                              <td>".$gq_gc_data[$key]["D3"]."</td>
                              <td>".$rjcl_gc_cg_data[$key]["D3"]."</td>
                          <tr>";
            $i++;
            if($i == 13){
                break;
            }
        }
        $this->assign( "gqxs_str", $gqxs_str );

		//我国钢材坯进出口预测
		// 钢材：L2id=192 and L2OrderNo=1 进口量：D1出口量：D2
		$ckyc_gc_data = $this->get_hgData(192,1,"",$edate);
		// 钢坯：L2id=193 and L2OrderNo=2 进口量：D1出口量：D2
		$ckyc_gp_data = $this->get_hgData(193,2,"",$edate);
        $gp_jck_str = "";
        $i = 0;
        foreach($ckyc_gc_data as $key => $item){
            $gp_jck_str .= "<tr>
                              <td>".$ckyc_gc_data[$key]["DATE2"]."</td>
                              <td>".$ckyc_gc_data[$key]["D1"]."</td>
                              <td>".$ckyc_gc_data[$key]["D2"]."</td>
                              <td>".$ckyc_gp_data[$key]["D1"]."</td>
                              <td>".$ckyc_gp_data[$key]["D2"]."</td>
                          <tr>";
            $i++;
            if($i == 13){
                break;
            }
        }
        $this->assign( "gp_jck_str", $gp_jck_str );
		
		// 3、我国粗钢和钢材资源供应量预测
		// 粗钢资源供应量：L2id=196 and L2OrderNo=40 当月：D1同比增幅：D3
		$cgzy_data = $this->get_hgData(196,40,"",$edate);
		// 钢材资源供应量：L2id=197 and L2OrderNo=41 当月：D1同比增幅：D3
		$gczy_gp_data = $this->get_hgData(197,41,"",$edate);

        $zygyl_str = "";
        $i = 0;
        foreach($cgzy_data as $key => $item){
            $zygyl_str .= "<tr>
                              <td>".$cgzy_data[$key]["DATE2"]."</td>
                              <td>".$cgzy_data[$key]["D1"]."</td>
                              <td>".$cgzy_data[$key]["D3"]."</td>
                              <td>".$gczy_gp_data[$key]["D1"]."</td>
                              <td>".$gczy_gp_data[$key]["D3"]."</td>
                          <tr>";
            $i++;
            if($i == 13){
                break;
            }
        }
        $this->assign( "zygyl_str", $zygyl_str );

		// 4、市场与钢厂库存量预测
		// 五大品种市场库存：L2id=199 and L2OrderNo=1 取D1
		$pzkc_data = $this->get_hgData(199,1,1,$edate);
		// 钢厂库存：取每月的最后一个数据L2id=231 and L2OrderNo=3 取D1
		$gckc_data = $this->get_hgData(231,3,1,$edate);
        // print_r($pzkc_data);
        $pzsckc_str = "";
        $i = 0;
        foreach($pzkc_data as $key => $item){
            $pzsckc_str .= "<tr>
                              <td>".$pzkc_data[$key]["DATE2"]."</td>
                              <td>".$pzkc_data[$key]["D1"]."</td>
                              <td>".$gckc_data[$key]["D1"]."</td>
                          <tr>";
            $i++;
            if($i == 13){
                break;
            }
        }
        $this->assign( "pzsckc_str", $pzsckc_str );

        //5、下游行业需求预测
		// 房屋新开工面积：L2id=154 and L2OrderNo=2 取D1
		$fwkg_data = $this->get_hgData(154,2,"",$edate);
		// 挖掘机：L2id=187 and L2OrderNo=3 取D1
		$wjj_data = $this->get_hgData(187,3,"",$edate);
		// 汽车：L2id=172 and L2OrderNo=1 取D1
		$qc_data = $this->get_hgData(172,1,"",$edate);
		// 集装箱：L2id=185 and L2OrderNo=2 取D1
		$jzx_data = $this->get_hgData(185,2,"",$edate);
		// 造船新接订单：L2id=469 and L2OrderNo=2 取D1
		$zcxjdd_data = $this->get_hgData(469,2,"",$edate);
		// 空调：L2id=182 and L2OrderNo=8 取D1
		$kt_data = $this->get_hgData(182,8,"",$edate);
		// 冰箱：L2id=180 and L2OrderNo=6 取D1
		$bx_data = $this->get_hgData(180,6,"",$edate);
		// 洗衣机：L2id=178 and L2OrderNo=5 取D1
		$xyj_data = $this->get_hgData(178,5,"",$edate);

        $xyhy_str = "";
        $i = 0;
        foreach($fwkg_data as $key => $item){
            $xyhy_str .= "<tr>
                              <td>".$fwkg_data[$key]["DATE2"]."</td>
                              <td>".$fwkg_data[$key]["D3"]."</td>
                              <td>".$wjj_data[$key]["D3"]."</td>
                              <td>".$qc_data[$key]["D3"]."</td>
                              <td>".$jzx_data[$key]["D3"]."</td>
                              <td>".$zcxjdd_data[$key]["D3"]."</td>
                              <td>".$kt_data[$key]["D3"]."</td>
                              <td>".$bx_data[$key]["D3"]."</td>
                              <td>".$xyj_data[$key]["D3"]."</td>
                          <tr>";
            $i++;
            if($i == 13){
                break;
            }
        }
        $this->assign( "xyhy_str", $xyhy_str );


        //行业成本 螺纹钢 高炉 45 电炉 163 热轧 44
        $hycb_data_lw_gl = $this->_dao->getHangYeChengBen(45,$edate,$this->drcdao);
        $hycb_data_lw_dl = $this->_dao->getHangYeChengBen(163,$edate,$this->drcdao);
        $hycb_data_rz = $this->_dao->getHangYeChengBen(44,$edate,$this->drcdao);

        $hycb_str = "";
        $i = 0;
        foreach($hycb_data_lw_gl as $key => $item){
            $hycb_str .= "<tr>
                              <td>".$hycb_data_lw_gl[$key]["ndate2"]."</td>
                              <td>".$hycb_data_lw_gl[$key]["avg_price"]."</td>
                              <td>".$hycb_data_lw_dl[$key]["avg_price"]."</td>
                              <td>".$hycb_data_rz[$key]["avg_price"]."</td>
                          <tr>";
            $i++;
            if($i == 13){
                break;
            }
        }
        $this->assign( "hycb_str", $hycb_str );

        //
        $next_month_first_date = date('Y-m-01',strtotime("next month",strtotime($sdate)));
        $next_month_last_date = date('Y-m-d', strtotime("$next_month_first_date +1 month -1 day"));
        
        // $date_array2 = array();
        // $date_array2["this_start_date"] = $next_month_first_date;
        // $date_array2["this_end_date"] = $next_month_last_date;
        $yue_yc_data2 = $this->_dao->get_yc_data(3,$next_month_first_date,$next_month_last_date);
        // print_r($yue_yc_data2);
        
        $month2 = date('n',strtotime($next_month_first_date));
        $this->assign( "month2", $month2 );
        $yc_fp_table_str2 = '<tr>
                                <td>预测方向</td>
                                <td>'.$yue_yc_data2["942023"]["ycfx"].'</td>
                                <td>'.$yue_yc_data2["943012"]["ycfx"].'</td>
                                <td>'.$yue_yc_data2["943112"]["ycfx"].'</td>
                                <td>'.$yue_yc_data2["944312"]["ycfx"].'</td>
                            </tr>
                            <tr>
                                <td>预测幅度</td>
                                <td>'.$yue_yc_data2["942023"]["ycfd"].'</td>
                                <td>'.$yue_yc_data2["943012"]["ycfd"].'</td>
                                <td>'.$yue_yc_data2["943112"]["ycfd"].'</td>
                                <td>'.$yue_yc_data2["944312"]["ycfd"].'</td>
                            </tr>
                            <tr>
                                <td>预测市场</td>
                                <td>新余</td>
                                <td>新余</td>
                                <td>新余</td>
                                <td>新余</td>
                            </tr>
                            ';
        
        $this->assign( "yc_fp_table_str2", $yc_fp_table_str2 );

        $edate = date('Y-m-d',strtotime("$edate -1 month"));
        //后市判断
        $xg_marketNews2 = html_entity_decode($this->_dao->get_xg_marketNews(3,$sdate,$edate,2));
        $this->assign( "xg_marketNews2", $xg_marketNews2 );
        $this->assign("title",date("Y年n月",strtotime($sdate." +1 month"))."月预测");
		// echo "<pre/>";
		// print_r($hg_data);
	}

    /**
     * 旬预测
     * Created by zfy.
     * Date:2023/1/4 17:48
     * @param $params
     */
	public function xun_price_yc($params){
//        $params['baseid'] = "4";
//        $date = $this->_dao->getDateByBaseId($params['baseid']);
        $date = $params['edate'];
//        $date = "2023-01-03";
        //旬日期
        $xunDateList = $this->getXunCalcDateArray($date);
        //工作日
        $workDate = $this->getWorkDate($xunDateList);
        //旬 月 年同比日期
        $dateList = $this->getXunMonthYearDate($xunDateList);
//        echo "<pre>";print_r($xunDateList);exit();
//        [last_start_date] => 2022-12-14
//    [last_end_date] => 2022-12-23
//    [this_start_date] => 2022-12-24
//    [this_end_date] => 2023-01-03
        //复盘数据
        $this->handleForecastInfo($xunDateList,2);
        //市场价格变化情况
        $this->handleMarketPrice($xunDateList,$workDate);
        //矿煤焦钢期货主力合约及国际大宗商品、外汇市场
        $this->handleFutures($workDate);
        //竞争钢厂调价情况
        $this->handleSteelPrice($xunDateList);
        //市场 钢厂 五大库存
        $this->handleStockInfo($dateList);
        //开工率、旬产量和日均成交量
        $this->handleOperatingData($dateList,$xunDateList);
        //钢材成本与毛利变化
        $this->handleCostAndProfit($dateList);
        //市场预测与风险提示
        $this->assign("content",html_entity_decode($this->_dao->getMarketNews($xunDateList)));
        //下旬价格预测
        $this->handleLastXunForecast($xunDateList);
        //图片处理
        $this->handleTrendChart($xunDateList);
        $this->assign("mode",$params['mode']);
        $this->assign("startDateStr",date("n月j日",strtotime($workDate['this_start_date'])));
        $this->assign("endDateStr",date("n月j日",strtotime($workDate['this_end_date'])));
        $this->assign("variety_list",array("螺纹钢","中厚板","热轧板卷","冷轧板卷"));
        $this->assign("nextDateStr",date("n月j日",strtotime($xunDateList['next_start_date'])).date("至n月j日",strtotime($xunDateList['next_end_date'])));
        $this->assign("title",date("Y年n月j日",strtotime($xunDateList['next_end_date']))."旬预测");
    }

    /**
     * 数据报告 市场分析周度简报
     * Created by zfy.
     * Date:2023/2/28 10:47
     * @param $params
     */
    public function data_report($params){

        if ($params['end_date']==''){
            $params['end_date'] = date("Y-m-d",strtotime("last thursday"));
            $params['start_date'] = date("Y-m-d",strtotime($params['end_date']." -7 day"));
        }

		//铁水周成本 取新钢数据库 add by ch
		$XGLoginURL = XGDCURL . '/api/gzjSysLogin';
		$logininfo['username'] = 'testuser';
		$logininfo['pwd'] = '123456';
		$logininfo['mc_type']=3;
		$mess = $this->http_post($XGLoginURL, http_build_query($logininfo));
		$content = $mess['content'];
		$tokenArray = json_decode($this->clearBom($content), true);
		$token = $tokenArray['Token'];
		
		$XGDataURL = XGDCURL . '/api/getdb';
		$datainfo['token'] = $token;
		$queryjson_arr[] = array(
			'dtid' => "",
			'tablename' => "xinsteel_data_table",
			'sqlmain' => base64_encode( str_replace("&lt;", "<", str_replace("&gt;", ">", "where dta_type='GYCB' and dta_ym>='".$params['start_date']."' and dta_ym<='".$params['end_date']))."' order by dta_ym desc limit 1"),
			'dbfield' => "dta1,dta_ym",
			'datefield' => ""
		);
		$queryjson = json_encode($queryjson_arr);
		$datainfo['queryjson'] = $queryjson;
		$datainfo['mc_type'] = 3;
		$datas = $this->http_post($XGDataURL, http_build_query($datainfo));		
		$data = $datas['content'];
		$dataArray = json_decode($this->clearBom($data), true);
		$tscbdata = $dataArray['Results'][0]['data'][0];
		
		$this->assign("tscbdata",$tscbdata);
		
        $workDateList = $this->getWeekYearWorkDate($params);
        //市场价格处理
        $this->handleSteelMarketPriceInfo($workDateList);
        //产线利润
        $this->productionProfit($workDateList);
        //处理走势图
        $this->handleReportChart($params['end_date']);
        //处理期货价格
        $this->handleQuotePrice($workDateList);
        //处理库存报告
        $this->handleStockReport($workDateList);
        //处理公司钢材库存 + 手持订单情况
        $this->handleSteelStockAndOrder($workDateList);
        //处理吨钢毛利
        $this->handleSteelMaoLi($workDateList);
        $this->assign("mode", $params['mode']);
	    $this->assign("params",$params);
	    $this->assign("startDateStr",date("n月j日",strtotime($params['start_date'])));
	    $this->assign("endDateStr",date("n月j日",strtotime($params['end_date'])));
        $this->assign("date_str",date("Y年n月j日",strtotime($params['start_date']))."-".date("n月j日",strtotime($params['end_date'])));

        //add by ch
        $kcdata = $this->getScrbData("GetYrlkcData", $params['end_date']);
        $thisTable = array(
            "焦煤" => array(),
            "外购焦" => array(),
            "喷吹煤" => array(),
            "球团" => array(),
            "进口块" => array(),
            "国内块" => array(),
            "进口粉" => array(),
            "国内粉" => array(),
            "烧结熔剂" => array(),
            "烧结燃料" => array(),
            "混匀矿" => array(),
        );
        $i = 0;
        $xh = array("一","二","三","四","五","六","七","八","九","十","十一");
        $tbbdata = array();
        foreach($thisTable as $key2 => $val2) {
            foreach($kcdata as $key => $val) {
                $t = array();
                $t = explode("-", $val['PM']);
                if($key == $key2) {
                    $tbbdata[$xh[$i]."、".$key] = $val;
                }
            }
            $i++;
        }
        $tbbdata['合计'] = array('DQKC'=>0, 'DEZZTS'=>'', 'DEKC'=>0, 'PCZ'=>0, 'SJZZTS'=>'');
        foreach($tbbdata as $tkey => $tval) {
            if($tkey=="合计") continue;
            $tbbdata[$tkey]['DEZZTS'] = round((float)$tval['DEZZTS'], 0);
            $tbbdata[$tkey]['SJZZTS'] = round((float)$tval['SJZZTS'], 0);
            $tbbdata[$tkey]['DQKC'] = round((float)$tval['DQKC'], 0);
            $tbbdata[$tkey]['DEKC'] = round((float)$tval['DEKC'], 0);
            $tbbdata[$tkey]['PCZ'] = round((float)$tval['PCZ'], 0);

            $tbbdata['合计']['DQKC'] += $tbbdata[$tkey]['DQKC'];
            $tbbdata['合计']['DEKC'] += $tbbdata[$tkey]['DEKC'];
            $tbbdata['合计']['PCZ'] += $tbbdata[$tkey]['PCZ'];
        }
        $this->assign('tbbdata', $tbbdata);
    }

    /**
     * 处理吨钢毛利
     * Created by zfy.
     * Date:2023/3/2 18:05
     * @param $workDateList
     */
    protected function handleSteelMaoLi($workDateList){
        $maoli_field_list = array("lastPrice","lastMaoLi","thisPrice","thisMaoLi","priceZd","maoLiZd");
        $thisInfo = $this->getSteelMaoLiForInterface($workDateList['end_date']);
        $lastInfo = $this->getSteelMaoLiForInterface($workDateList['start_date']);
        $this->assign("maoli_field_list",$maoli_field_list);
        $this->assign("maoli_info",$this->calcSteelMaoLi($thisInfo,$lastInfo));
    }

    /**
     * 计算吨钢毛利
     * Created by zfy.
     * Date:2023/3/2 18:05
     * @param $thisInfo
     * @param $lastInfo
     * @return array
     */
    protected function calcSteelMaoLi($thisInfo,$lastInfo){
        $info = array();
        foreach ($thisInfo as $variety => $item) {
            $info[$variety]['thisPrice'] = $item['baseprice'];
            $info[$variety]['thisMaoLi'] = $item['lirun'];
            $info[$variety]['lastPrice'] = $lastInfo[$variety]['baseprice'];
            $info[$variety]['lastMaoLi'] = $lastInfo[$variety]['lirun'];
            $info[$variety]['priceZd'] = $this->zhangdie2($info[$variety]['thisPrice'] - $info[$variety]['lastPrice'], 0);
            $info[$variety]['maoLiZd'] = $this->zhangdie2($info[$variety]['thisMaoLi'] - $info[$variety]['lastMaoLi'], 0);
        }
        return $info;
    }

    /**
     * 通过接口获取 吨钢毛利
     * Created by zfy.
     * Date:2023/3/2 18:04
     * @param $date
     * @return array
     */
    protected function getSteelMaoLiForInterface($date){
        $url = $GLOBALS['XG_IP']."/pzbjcs/index?GUID=65237e8c9c2411ebbebc001d09719b40&mode=1&jdxy=0&isjson=1&date=".$date;
        $response = file_get_contents($url, false, stream_context_create(array("ssl" => array("verify_peer" => false, "verify_peer_name" => false))));
        $data = json_decode($response, true);
        $retList = array();
        $retList['线材'] = $data['线棒材']['高线']['HPB300']['6-10mm'];
        $retList['硬线'] = $data['线棒材']['高线']['60-70']['5.5-12.5mm'];
        $retList['盘螺'] = $data['线棒材']['高线']['HRB400E-X0']['6-12.5mm'];
        $retList['螺纹'] = $data['线棒材']['高棒']['HRB400NbTi-g']['12-32mm'];
        $retList['普板'] = $data['厚板']['普板']['Q235']['6-120mm'];
        $retList['船板'] = $data['厚板']['普船']['船A/B']['6-80mm'];
        $retList['容器板'] = $data['厚板']['容器板']['Q345R']['6-120mm'];
        $retList['桥梁板'] = $data['厚板']['桥梁板']['Q345Q']['6-100mm'];
        $retList['热轧普卷'] = $data['热卷']['6001碳素热轧卷板']['Q235B']['7.01mm'];
        $retList['热轧汽车钢'] = $data['热卷']['6008汽车用热轧卷板']['510L']['6.82mm'];
        $retList['冷轧卷板'] = $data['冷卷']['连退']['DC01']['0.4-1.6mm'];
        return $retList;
    }

    /**
     * 处理公司钢材库存 + 手持订单情况
     * Created by zfy.
     * Date:2023/3/2 9:09
     * @param $workDateList
     */
    protected function handleSteelStockAndOrder($workDateList){
        $field_list = array("lastPrice","thisPrice","priceZd","priceZdf");
        $order_quantity_field_list = array("quantity","field2","field1","productionSchedulingDate");
        $thisInfo = $this->getSteelStockAndOrderInfoByInterface($workDateList['end_date']);
        $lastInfo = $this->getSteelStockAndOrderInfoByInterface($workDateList['start_date']);
        $allInfo = $this->calcSteelStockAndOrder($thisInfo,$lastInfo,$workDateList['end_date']);
        $this->assign("steel_stock_field_list",$field_list);
        $this->assign("order_quantity_field_list",$order_quantity_field_list);
        $this->assign("steel_stock_list",$allInfo['steel_stock']);
        $this->assign("order_quantity_list",$allInfo['order_quantity']);
    }

    /**
     * 计算 公司钢材库存 手持订单量
     * Created by zfy.
     * Date:2023/3/2 9:21
     * @param $thisInfo
     * @param $lastInfo
     * @return array[]
     */
    protected function calcSteelStockAndOrder($thisInfo,$lastInfo,$date){
        //公司钢材库存
        $steelStockInfo = array();
        foreach ($thisInfo['steelStockList'] as $variety => $item) {
            $steelStockInfo[$variety]['thisPrice'] = $item['quantity'];
            $steelStockInfo[$variety]['lastPrice'] = $lastInfo['steelStockList'][$variety]['quantity'];
            $steelStockInfo[$variety]['priceZd'] = $this->zhangdie2($steelStockInfo[$variety]['thisPrice'] - $steelStockInfo[$variety]['lastPrice'], 0);
            $steelStockInfo[$variety]['priceZdf'] = $this->zhangdie2(round(($steelStockInfo[$variety]['thisPrice'] - $steelStockInfo[$variety]['lastPrice']) / $steelStockInfo[$variety]['lastPrice'] * 100, 2), 1);
        }
        return array("steel_stock"=>$steelStockInfo,"order_quantity"=>$thisInfo['orderQuantityList']);
    }

    /**
     * 从接口取数据
     * Created by zfy.
     * Date:2023/3/2 9:09
     * @param $date
     * @return array[]
     */
    protected function getSteelStockAndOrderInfoByInterface($date){
        $steelStockVarietyNameList = array("中厚板","线棒材","卷板");
        $orderQuantityVarietyNameList = array("中厚板","线材","棒材","卷板");
        $url = $GLOBALS['XG_IP']."/api/jgyc/getData?date=".$date;
        $response = file_get_contents($url, false, stream_context_create(array("ssl" => array("verify_peer" => false, "verify_peer_name" => false))));
        $data = json_decode($response, true);

        $steelStockList = array();
        $orderQuantityList = array();
        foreach ($data['data'] as &$item) {
            $item['quantity'] = round((float)$item['quantity']);
            //1= 手持订单量 2=公司钢厂库存
            switch ($item['type']){
                case 1:
                    if (in_array($item['dataname'],$orderQuantityVarietyNameList)){
                        $item['productionSchedulingDate'] = date("n月j日",strtotime($date."+ ".$item['field1']." day"));
                        $orderQuantityList[$item['dataname']] = $item;
                    }
                    break;
                case 2:
                    if (in_array($item['dataname'],$steelStockVarietyNameList)){
                        $steelStockList[$item['dataname']] = $item;
                    }
                    break;
            }
        }
        return array("steelStockList"=>$steelStockList,"orderQuantityList"=>$orderQuantityList);
    }

    /**
     * 处理库存报告
     * Created by zfy.
     * Date:2023/3/1 15:53
     * @param $workDateList
     */
    protected function handleStockReport($workDateList){
        //获取距离上年同比库存日期最近的日期 比如上年同期是 3月2日，你取了2月24日的，刘总说要3月3日的，因为离3月2日近
        $nearDate = $this->gcdao->getNearDate($workDateList);
        $workDateList['lastYearDate'] = $nearDate;
        $field_list = array("lastPrice","thisPrice","priceZd","priceZdf","yearPriceZd","yearPriceZdf");
        //市场+钢厂库存
        $allStockList = $this->_dao->getWeekYearAllStockPrice($workDateList);
        $allStockInfo = $this->calcStockUpDown($allStockList);
//        $allStockInfo = $this->calcUpDown($allStockList);
//        //钢厂库存
//        $steelStockList = $this->gcdao->getWeekYearSteelStockPrice($workDateList,$GLOBALS['STOCK_TYPE_STR']);
//        $steelStockInfo = $this->calcUpDown($steelStockList);
//        //市场库存
//        $marketStockList = $this->_dao->getWeekYearMarketStockPrice($workDateList,$GLOBALS['STOCK_TYPE_STR']);
//        $marketStockInfo = array();
//        if (is_array($marketStockList['lastPrice'])) {
//            foreach ($marketStockList['lastPrice'] as $key => $item) {
//                $tmp = array();
//                $tmp['lastPrice'] = $item;
//                $tmp['thisPrice'] = $marketStockList['thisPrice'][$key];
//                $tmp['lastYearPrice'] = $marketStockList['lastYearPrice'][$key];
//                $marketStockInfo[$key] = $this->calcUpDown($tmp);
//            }
//        }
        $this->assign("allStockInfo",$allStockInfo);
//        $this->assign("marketStockInfo",$marketStockInfo);
//        $this->assign("steelStockInfo",$steelStockInfo);
        $this->assign("stock_field_list",$field_list);
    }

    /**
     * 计算库存涨跌情况
     * Created by zfy.
     * Date:2023/7/11 15:56
     * @param $list
     * @return array
     */
    protected function calcStockUpDown($list){
        $retList = array();
        foreach ($list['thisPrice'] as $index => $thisPrice) {
            $retList[$index]['lastPrice'] = sprintf("%.2f",round($list['lastPrice'][$index],2));
            $retList[$index]['thisPrice'] = sprintf("%.2f",round($thisPrice,2));
            $retList[$index]['priceZd'] = $this->zhangdie2(sprintf("%.2f",round($thisPrice-$list['lastPrice'][$index],2)),0);
            $retList[$index]['yearPriceZd'] = $this->zhangdie2($thisPrice-$list['lastYearPrice'][$index],0);
            $retList[$index]['priceZdf'] = $this->zhangdie2(sprintf("%.2f",round(($thisPrice-$list['lastPrice'][$index])/$list['lastPrice'][$index]*100,2)),1);
            $retList[$index]['yearPriceZdf'] = $this->zhangdie2(round(($thisPrice-$list['lastYearPrice'][$index])/$list['lastYearPrice'][$index]*100,2),1);
        }
        return $retList;
    }

    /**
     * 计算涨跌 涨跌幅
     * Created by zfy.
     * Date:2023/3/1 16:56
     * @param $list
     * @return mixed
     */
    protected function calcUpDown($list){
        $list['priceZd'] = $this->zhangdie2($list['thisPrice']-$list['lastPrice'],0);
        $list['yearPriceZd'] = $this->zhangdie2($list['thisPrice']-$list['lastYearPrice'],0);
        $list['priceZdf'] = $this->zhangdie2(round(($list['thisPrice']-$list['lastPrice'])/$list['lastPrice']*100,2),1);
        $list['yearPriceZdf'] = $this->zhangdie2(round(($list['thisPrice']-$list['lastYearPrice'])/$list['lastYearPrice']*100,2),1);
        return $list;
    }

    /**
     * 处理期货价格
     * Created by zfy.
     * Date:2023/3/1 15:24
     * @param $workDateList
     */
    protected function handleQuotePrice($workDateList){
        $priceIdList = array("SHQHDAY_4","SHQHDAY_99","SHQHDAY_19","SHQHDAY_9","SHQHDAY_20","SHQHDAY_22","SHQHDAY_23");
        $field_list = array("lastPrice","thisPrice","priceZd","priceZdf");
        $futuresInfo = $this->_dao->getFuturesByTypes(array('this_start_date'=>$workDateList['start_date'],'this_end_date'=>$workDateList['end_date']),"'".implode("','",$priceIdList)."'");
        $priceInfo = array();
        foreach ($futuresInfo['startPrice'] as $id => $price) {
            $priceInfo[$id]['lastPrice'] = $price;
            $priceInfo[$id]['thisPrice'] = $futuresInfo['endPrice'][$id];
            $priceInfo[$id]['priceZd'] = $this->zhangdie2($priceInfo[$id]['thisPrice'] - $priceInfo[$id]['lastPrice'], 0);
            $priceInfo[$id]['priceZdf'] = $this->zhangdie2(round(($priceInfo[$id]['thisPrice'] - $priceInfo[$id]['lastPrice']) / $priceInfo[$id]['lastPrice'] * 100, 2), 1);
        }
        $this->assign("quote_field_list",$field_list);
        $this->assign("quotePriceInfo",$priceInfo);
    }

    /**
     * 市场分析周度简报图片
     * Created by zfy.
     * Date:2023/2/28 16:45
     * @param $date
     */
    protected function handleReportChart($date){
        $oneYearAgo = date("Y-m-d",strtotime($date."-1 year"));
        $imagesUrls = array(
            // "ice"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&SignCS='.PRICE_DATACENTER_SIGNCS.'&GUID='.PRICE_DATACENTER_GUID.'&DateStart='.$oneYearAgo.'&DateEnd='.$date.'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=2&ImageTitle=&isbg=0&isjz=0&isfg=0&color=0&mc_type=4&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"103200","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"SUNF1K3TzcrVxcy82w==","unitconver":"1","unitstring":"w8DUqi/NsA==","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle=',
            "tks"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&SignCS='.PRICE_DATACENTER_SIGNCS.'&GUID='.PRICE_DATACENTER_GUID.'&DateStart='.$oneYearAgo.'&DateEnd='.$date.'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=2&ImageTitle=&isbg=0&isjz=0&isfg=0&color=0&mc_type=4&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"103012","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"vfi/2sz6v/PKryhGZTo2MiUsQ0ZSKShTSENOSU9JKda4yv0ow8DUqik=","unitconver":"0","unitstring":"w8DUqi+21g==","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle=',
            "jt"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&SignCS='.PRICE_DATACENTER_SIGNCS.'&GUID='.PRICE_DATACENTER_GUID.'&DateStart='.$oneYearAgo.'&DateEnd='.$date.'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=2&ImageTitle=&isbg=0&isjz=0&isfg=0&color=0&mc_type=4&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"103707","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"vbnMvyhTSENOQ0tJKda4yv0ovNu48Sk=","unitconver":"0","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle=',
            // "fg"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&SignCS='.PRICE_DATACENTER_SIGNCS.'&GUID='.PRICE_DATACENTER_GUID.'&DateStart='.$oneYearAgo.'&DateEnd='.$date.'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=2&ImageTitle=&isbg=0&isjz=0&isfg=0&color=0&mc_type=4&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"103078","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"t8+41ihTSENORlNJKda4yv0ovNu48Sk=","unitconver":"0","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle=',
            // "ptfp"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&SignCS='.PRICE_DATACENTER_SIGNCS.'&GUID='.PRICE_DATACENTER_GUID.'&DateStart='.$oneYearAgo.'&DateEnd='.$date.'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=2&ImageTitle=&isbg=0&isjz=0&isfg=0&color=0&mc_type=4&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"103654","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"xtXMvLe9xfcozMbJvSkoU0hDTlNQSS1Uc0JpbGxldCnWuMr9KLzbuPEp","unitconver":"0","unitstring":"1KovttY=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle=',

        );
        $id = 0;
        foreach ($imagesUrls as $index => $imagesUrl) {
            $id++;
            $imageid = "tu" . $id . "_" . $id;
            //width:500px; height:279px;
            $imagesUrlList[$index] = '<div class="" id="' . $imageid . '"><iframe name="iframe" style="margin:0; padding:0; width:500px; height:279px; background-color:#FFF; visibility:inherit;" src=\'' . $imagesUrl . '&callbackname=' . $imageid . '\' frameborder="0" scrolling="no"></iframe></div>';

        }
        $this->assign("imagesUrlLists",$imagesUrlList);
    }

    /**
     * 抓取产线利润
     * Created by zfy.
     * Date:2023/2/28 15:16
     * @param $date
     */
    protected function productionProfit($dateList){

        $lastInfo = $this->getProductionProfitInterface($dateList['start_date']);
        $thisInfo = $this->getProductionProfitInterface($dateList['end_date']);

        //各产线主要品种吨钢毛利情况 中的 优特钢带数据从这个取
        $this->assign("youTeGangDai",$this->calcyouTeGangDai($thisInfo['youTeGangDai'],$lastInfo['youTeGangDai']));

        $this->assign("productionProfitTable",$thisInfo['productionProfitTable']);
    }

    /**
     * 计算优特钢带 价格 毛利
     * Created by zfy.
     * Date:2023/3/2 17:59
     * @param $thisInfo
     * @param $lastInfo
     * @return array
     */
    protected function calcyouTeGangDai($thisInfo,$lastInfo){
        $info = array();
        $info['thisPrice'] = $thisInfo['2'];
        $info['thisMaoLi'] = $thisInfo['3'];
        $info['lastPrice'] = $lastInfo['2'];
        $info['lastMaoLi'] = $lastInfo['3'];
        $info['priceZd'] = $this->zhangdie2($info['thisPrice'] - $info['lastPrice'], 0);
        $info['maoLiZd'] = $this->zhangdie2($info['thisMaoLi'] - $info['lastMaoLi'], 0);
        return $info;
    }

    /**
     * 从接口 爬取 table
     * Created by zfy.
     * Date:2023/3/2 18:04
     * @param $date
     * @return array
     */
    protected function getProductionProfitInterface($date){
        $url = $GLOBALS['XG_IP']."/allgz/mrsc?GUID=65237e8c9c2411ebbebc001d09719b40&mode=1&type=jdxy&showmore=0&isshow=0&sdate=".$date;
        $fcontents = file_get_contents($url, false, stream_context_create(array("ssl" => array("verify_peer" => false, "verify_peer_name" => false))));

        // $fcontents = file_get_contents($url);
        preg_match_all('/<table[^>]*>(.*?) <\/table>/si',$fcontents,$match);
        $table_data = $match[0][0];

        $table_array = explode('<tr>',$table_data);
        $data = array();
        for($i=2;$i<count($table_array);$i++){
            $data[$i] = explode('</td>',$table_array[$i]);
            for($j = 0;$j<count($data[$i]);$j++){
                $data[$i][$j] = preg_replace('/\s(?=\s)/','',trim(strip_tags($data[$i][$j])));
            }
            array_pop($data[$i]);
        }
        return array("productionProfitTable"=>$table_data,"youTeGangDai"=>$data[10]);
    }

    /**
     * 钢材主要品种市场价格处理
     * Created by zfy.
     * Date:2023/2/28 11:18
     * @param $workDateList
     */
    protected function handleSteelMarketPriceInfo($workDateList){
        $priceIdList = array("252043f","353012h","2531123","3531124","0861442","0750315","2850315","284312j","H986401","D286103","D286104","D283111","678411","658210");
        $priceIdList2 = array("H986401","D286103","D286104","D283111","678411","658210");
        $field_list = array("lastPrice","thisPrice","priceZd","priceZdf","yearPriceZd","yearPriceZdf");
        $priceList = $this->maindao->getWeekYearMarketPrice($workDateList,$priceIdList2);
        $priceInfo2 = $this->calcWeekYearPriceInfo($priceIdList,$priceList);
        $url = $GLOBALS['XG_IP']."/api/jgyc/getMysteelPrice?sdate=".$workDateList['start_date']."&edate=".$workDateList['end_date'];
        $response = file_get_contents($url, false, stream_context_create(array("ssl" => array("verify_peer" => false, "verify_peer_name" => false))));
        $data = json_decode($response, true);

        $scjg_arrar = array();
        $i = 0;
        foreach($data["data"] as $key=>$item){
            $item_arr = array();
            $key = $priceIdList[$i];
            $item_arr["lastPrice"] = $item["lastweek"]["price"];
            $item_arr["thisPrice"] = $item["this"]["price"];
            $item_arr["priceZd"] = $this->zhangdie2($item["weekrateval"]);
            $item_arr["priceZdf"] = $this->zhangdie2($item["weekrate"]);
            $item_arr["yearPriceZd"] = $this->zhangdie2($item["yearrateval"]);
            $item_arr["yearPriceZdf"] = $this->zhangdie2($item["yearrate"]);
            $scjg_arrar[$key] = $item_arr;
            $i++;
        }
        $this->assign("field_list",$field_list);
        $this->assign("priceInfo",$scjg_arrar);
        $this->assign("priceInfo2",$priceInfo2);
        // $priceIdList = array("252043f","353012h","2531123","3531124","0861442","0750315","2850315","284312j","H986401","D286103","D286104","D283111","678411","658210");
        // $field_list = array("lastPrice","thisPrice","priceZd","priceZdf","yearPriceZd","yearPriceZdf");
        // $priceList = $this->maindao->getWeekYearMarketPrice($workDateList,$priceIdList);
        // $priceInfo = $this->calcWeekYearPriceInfo($priceIdList,$priceList);
        // $this->assign("field_list",$field_list);
        // $this->assign("priceInfo",$priceInfo);
    }

    /**
     * 处理 环比同比数据
     * Created by zfy.
     * Date:2023/2/28 11:46
     * @param $steelPriceIdList
     * @param $priceInfo
     * @return array
     */
    protected function calcWeekYearPriceInfo($steelPriceIdList,$priceInfo){
        $list = array();
        foreach ($steelPriceIdList as $steelItem) {
            foreach ($priceInfo as $priceType => $priceItem) {
                foreach ($priceItem as $pItem) {
                    if ($steelItem == $pItem['topicture'] || $steelItem == $pItem['mastertopid']|| $steelItem == $pItem['priceid']){
                        if ($priceType=='startPrice'){
                            $list[$steelItem]['lastPrice'] = $pItem['price'];
                        }elseif ($priceType=='lastYearPrice'){
                            $list[$steelItem]['lastYearPrice'] = $pItem['price'];
                        }else{
                            $list[$steelItem]['thisPrice'] = $pItem['price'];
                        }
                    }
                }
            }
        }
        foreach ($list as $index => &$item) {
            $item['priceZd'] = $this->zhangdie2($item['thisPrice']-$item['lastPrice'],0);
            $item['yearPriceZd'] = $this->zhangdie2($item['thisPrice']-$item['lastYearPrice'],0);
            $item['priceZdf'] = $this->zhangdie2(round(($item['thisPrice']-$item['lastPrice'])/$item['lastPrice']*100,2),1);
            $item['yearPriceZdf'] = $this->zhangdie2(round(($item['thisPrice']-$item['lastYearPrice'])/$item['lastYearPrice']*100,2),1);
        }
        return $list;
    }

    /**
     * 获取本周 上周 上年同期的工作日时间
     * Created by zfy.
     * Date:2023/2/28 11:17
     * @param $params
     * @return array
     */
    protected function getWeekYearWorkDate($params){
        $dateList = array();
        $dateList['start_date'] = get_work_day(date("Y-m-d",strtotime($params['start_date'])));
        $dateList['end_date'] = get_work_day(date("Y-m-d",strtotime($params['end_date'])));
        $dateList['lastYearDate'] = get_work_day(date("Y-m-d",strtotime($params['end_date']."-1 year")));
        return $dateList;
    }

    /**
     * 预测列表
     * Created by zfy.
     * Date:2023/2/7 8:56
     * @param $params
     */
    public function forecast_list($params){
        $mode = $params['mode'] == "" ? 1 : $params['mode'];
	    $viewList = array("1"=>"day_price_yc","2"=>"xun_price_yc","3"=>"month_price_yc");
        $this->assign("type",$params['type']);
        $this->assign("mode",$mode);
        $this->assign("viewName",$viewList[$params['type']]);
    }

    public function mysteel_compared_price($params){
        $all = array(
            '江阴(鄂钢)|中厚板Q235/20mm',
            '上海(鞍钢)|中厚板Q235/20mm',
            '上海(南钢)|中厚板Q235/20mm',
            '新余(新钢)|中厚板Q235/20mm',
            '南昌(新钢)|中厚板Q235/20mm',
            '广州(新钢)|中厚板Q235/20mm',
            '长沙(新钢)|中厚板Q235/20mm',
            '上海(南钢)|中厚板Q345R/20mm',
            '上海(新钢)|中厚板Q345R/20mm',
            '无锡(新钢)|中厚板Q345R/20mm',
            '上海(本钢)|热卷4.75mm/Q235B',
            '新余(新钢)|热卷4.75mm/Q235B',
            '福州(新钢)|热卷4.75mm/Q235B',
            '南昌(新钢)|热卷4.75mm/Q235B',
            '广州(鞍钢)|热卷4.75mm/Q235B',
            '长沙(新钢)|热卷4.75mm/Q235B',
            '无锡(沙钢)|热卷4.75mm/Q235B',
            '武汉(武钢)|热卷4.75mm/Q235B',
            '泉州(本钢)|热卷4.75mm/Q235B',
            '杭州(宁钢)|热卷4.75mm/Q235B',
            '上海(首钢)|冷卷SPCC/1.0mm',
            '新余(新钢)|冷卷SPCC/1.0mm',
            '南昌(新钢)|冷卷SPCC/1.0mm',
            '广州(包钢)|冷卷SPCC/1.0mm',
            '长沙(涟钢)|冷卷SPCC/1.0mm',
            '赣州(新钢)|螺纹HRB400E(ф18)',
            '南昌(新钢)|螺纹HRB400E(ф18)',
            '新余(新钢)|螺纹HRB400E(ф18)',
            '长沙(新钢)|螺纹HRB400E(ф18)',
            '贵阳(新钢)|螺纹HRB400E(ф18)',
            '广州(广钢)|螺纹HRB400E(ф18)',
            '合肥(马长江)|螺纹HRB400E(ф18)',
            '南昌(方大日定价)|螺纹HRB400E(ф18)', // 这个是钢之家这边直接获取的
        );
        if($params['edate']=="") $params['edate'] = date("Y-m-d");
        if($params['sdate']=="") $params['sdate'] = date("Y-m-d", strtotime($params['edate']."-6day"));

        $datestr = date("n月j日",strtotime($params['sdate']))."~".date("n月j日",strtotime($params['edate']));

        //$url = "http://**************/api/jgyc/getMysteelPrice2?sdate=".$params['sdate']."&edate=".$params['edate'];
        $url = $GLOBALS['XG_IP']."/api/jgyc/getMysteelPrice2?sdate=".$params['sdate']."&edate=".$params['edate'];
        $response = file_get_contents($url, false, stream_context_create(array("ssl" => array("verify_peer"=>false,"verify_peer_name"=>false))));
        $response = (json_decode($response, true));
        $all1 = $response['data'];

        // 取出方大特钢螺纹钢价格
        $fdlwg_list = $this->gcdao->query("select ftime,the_price_tax from steelprice_info where `gcid` = '132' AND `ftime` >= '{$params['sdate']}' AND `ftime` <= '{$params['edate']}' AND onlyid='d73885280dc9167442ab5bf8b86b81ec'");
        $tbody = array();

        foreach($all as $na) {
            foreach($all1 as $date=>$aaa) {
                $tbody[$date][$na] = "—";
            }
        }
        foreach($all as $area_variety) {
            $tmparr = explode("|", $area_variety);
            $thead[$tmparr[1]][0] = 0;
            $thead[$tmparr[1]][] = $tmparr[0];

            if($area_variety == "南昌(方大日定价)|螺纹HRB400E(ф18)") {
                foreach($fdlwg_list as $inf) {
                    $the_price_tax = $inf['the_price_tax'];
                    $tbody[$inf['ftime']][$area_variety] = $the_price_tax;
                }
            } else {
                foreach($all1 as $kdate => $val) {
                    if(isset($val[$area_variety]) && ($val[$area_variety]!="" || $val[$area_variety] !=0))
                    $tbody[$kdate][$area_variety] = $val[$area_variety];
                }
            }
        }

        ksort($tbody);

        foreach($thead as &$val) {
            $val[0] = count($val)-1;

        }
        // print_r($tbody);exit;

        $this->assign("thead", $thead);
        $this->assign("tbody", $tbody);
        $this->assign("params", $params);
        $this->assign("datestr", $datestr);
    }

    public function ajax_forecast_list($params){
        $page = $params['page'];
        $limit = $params['limit'] == "" ? 15 : $params['limit'];
        if ($page < 1) {
            $page = 1;
        }
        $start = ($page - 1) * $limit;
        $total = count($this->drcwdao->getForecastTotal($params['type']));
        $dataInfo = $this->drcwdao->getForecastListInfo($params['type'], $start, $limit);
        $typeList = array('1'=>"日预测",'2'=>"旬预测",'3'=>"月预测");
        $data = array();
        foreach ($dataInfo as $tmp){
            $dateList = $this->getXunCalcDateArray($tmp['yc_edate']);
            if($params['type'] == "3"){
                $tmp['yc_varietyname'] = date("Y年n月",strtotime($tmp['yc_edate']." +1 day")).$typeList[$params['type']];
            }elseif ($params['type'] == "2"){
                $tmp['yc_varietyname'] = date("Y年n月j日",strtotime($dateList['next_end_date'])).$typeList[$params['type']];
            }else{
                $tmp['yc_varietyname'] = date("Y年n月j日",strtotime($this->get_xhdate2($tmp['yc_edate']))).$typeList[$params['type']];
            }
            $data[] = $tmp;
        }
        $code = 0;
        if ($data) {
            $code = 0;
        }
        $return_array = array(
            "code" => $code,
            "data" => $data,
            "count" => $total
        );
        echo json_encode($return_array);
    }

    protected function handleTrendChart($xunDateList){

	    //年折叠图取近五年数据
	    $end_year = date("Y",strtotime($xunDateList['this_end_date']));
	    $start_year = $end_year - 4;
	    $yearStr = $start_year.",".$end_year;
	    $oneYearAgo = date("Y-m-d",strtotime($xunDateList['this_end_date']."-1 year"));
        $imagesUrls = array(
	        "stock"=>array(
	            "1"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart=&DateEnd=&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=8&ImageTitle=uNayxM7ltPPGt9bWytCzob%2fitOY%3d&isbg=0&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"635","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"uNayxM7ltPPGt9bWv+K05ijW3Cmxvtbcv+K05g==","unitconver":"5","unitstring":"zfK21g==","iscustomerdb":"0"}]&needCompress=0&ChartExt={"isfestival": "0","Type":"1","DateStr":"'.$yearStr.'"}&ChartExtLineStyle=',
                "2"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart=&DateEnd=&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=8&ImageTitle=uNayxM7ltPPGt9bWuNazp7%2fitOY%3d&isbg=0&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"62018","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"uNazp87ltPPGt9bWv+K05ijW3Cm/4rTm","unitconver":"5","unitstring":"zfK21g==","iscustomerdb":"0"}]&needCompress=0&ChartExt={"isfestival": "0","Type":"1","DateStr":"'.$yearStr.'"}&ChartExtLineStyle='
            ),
            "operating"=>array(
                "3"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart=&DateEnd=&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=8&ImageTitle=ufrE2tb30qq41rOnuN%2fCr7C0svrE3L%2bquaTCyg%3d%3d&isbg=0&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"646","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"yKu5+rjfwq8tsLSy+sTcv6q5pMLK","unitconver":"0","unitstring":"JQ==","iscustomerdb":"0"}]&needCompress=0&ChartExt={"isfestival": "0","Type":"1","DateStr":"'.$yearStr.'"}&ChartExtLineStyle=',
                "4"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart=&DateEnd=&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=8&ImageTitle=ufrE2rbAwaK158KvxvPStbC0svrE3L%2bquaTCyg%3d%3d&isbg=0&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"56990","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"yKu5+rXnwq8tsLSy+sTcv6q5pMLK","unitconver":"0","unitstring":"JQ==","iscustomerdb":"0"}]&needCompress=0&ChartExt={"isfestival": "0","Type":"1","DateStr":"'.$yearStr.'"}&ChartExtLineStyle=',
                "5"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart=&DateEnd=&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=8&ImageTitle=ufrE2rK%2ft9a%2brc%2f6yczC3c7GuNbI1b75s8m9u8G%2f&isbg=0&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"6977","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"wt3OxrjWyNW++bPJvbvBvw==","unitconver":"1","unitstring":"ttY=","iscustomerdb":"0"}]&needCompress=0&ChartExt={"isfestival": "0","Type":"1","DateStr":"'.$yearStr.'"}&ChartExtLineStyle=',
                "6"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart=&DateEnd=&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=8&ImageTitle=ufrE2rK%2ft9a%2brc%2f6yczIyL7tyNW%2b%2bbPJvbvBvw%3d%3d&isbg=0&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"6978","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"yMjU/rDlvu3I1b75s8m9u8G/","unitconver":"0","unitstring":"ttY=","iscustomerdb":"0"}]&needCompress=0&ChartExt={"isfestival": "0","Type":"1","DateStr":"'.$yearStr.'"}&ChartExtLineStyle=',
                "7"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart=&DateEnd=&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=8&ImageTitle=ufrE2rK%2ft9a%2brc%2f6yczW0LrxsOXI1b75s8m9u8G%2f&isbg=0&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"6979","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"1tC68bDlyNW++bPJvbvBvw==","unitconver":"0","unitstring":"ttY=","iscustomerdb":"0"}]&needCompress=0&ChartExt={"isfestival": "0","Type":"1","DateStr":"'.$yearStr.'"}&ChartExtLineStyle=',
                "8"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart='.$oneYearAgo.'&DateEnd='.$xunDateList['this_end_date'].'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=2&ImageTitle=1ti147jWxvO01rjWoaLJ%2bsz6us241rLE0a7I1b75svrBvw%3d%3d&isbg=0&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"56587","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"tNa41g==","unitconver":"0","unitstring":"zfK21i/M7A==","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"56589","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"yfrM+g==","unitconver":"0","unitstring":"zfK21i/M7A==","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"56590","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"uNayxA==","unitconver":"0","unitstring":"zfK21i/M7A==","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle='
            ),
            "costProfit"=>array(
                "9"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart='.$oneYearAgo.'&DateEnd='.$xunDateList['this_end_date'].'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=2&ImageTitle=va3L1dHYva2438KvL7Xnwq%2fC3c7GuNazybG%2b0%2bvJz7qjwt3OxrjWvNu48Q%3d%3d&isbg=0&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"91078","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"uN/Cr8Ldzsa41rPJsb4=","unitconver":"1","unitstring":"1Ko=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"91082","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"tefCr8Ldzsa41rPJsb4=","unitconver":"1","unitstring":"1Ko=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"18941","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"yc+6o0hSQjQwMKa1MjAtMjJtbQ==","unitconver":"1","unitstring":"1Ko=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle=',
                "10"=>DC_DOMAIN_URL.'/v1.5/dcsearch.php?action=CommonSearch&SignCS='.DATACENTER_SIGNCS.'&GUID='.DATACENTER_GUID.'&DateStart='.$oneYearAgo.'&DateEnd='.$xunDateList['this_end_date'].'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType=2&ImageTitle=va3L1dHYva2438KvyMi+7bPJsb7T68nPuqPIyL7tvNu48Q==&isbg=0&isjz=0&isfg=0&color=0&mc_type=0&mode=1&isEnglish=0&ChartsType=2&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"91073","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"uN/Cr8jIvu2zybG+","unitconver":"1","unitstring":"1Ko=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"},{"DTID":"33252","DTIDSub":"","DataImage":"2","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"yc+6o1EyMzVCIDUuNzVtbSoxNTAwKkM=","unitconver":"1","unitstring":"1Ko=","linestylelight":"","linestylewidth":"","linestyletype":"","iscustomerdb":"0"}]&needCompress=0&ChartExt=&ChartExtLineStyle='
            )
        );
        foreach ($imagesUrls as $index => $images) {
            foreach ($images as $id => $imagesUrl) {
                $imageid = "tu" . $id . "_" . $id;
                //width:500px; height:279px;
                $imagesUrlList[$index][] = '<div class="" id="' . $imageid . '"><iframe name="iframe" style="margin:0; padding:0; width:500px; height:279px; background-color:#FFF; visibility:inherit;" src=\'' . $imagesUrl . '&callbackname=' . $imageid . '\' frameborder="0" scrolling="no"></iframe></div>';
            }
        }
        $this->assign("imagesUrlLists",$imagesUrlList);
    }

    /**
     * 处理下旬价格预测数据
     * Created by zfy.
     * Date:2023/1/12 9:21
     * @param $xunDateList
     */
    protected function handleLastXunForecast($xunDateList){
	    $lastXunForecastInfo = $this->_dao->getLastXunForecast($xunDateList);
        $this->assign("lastXunForecastInfo",$lastXunForecastInfo);
    }

    /**
     * 钢材成本与毛利变化
     * Created by zfy.
     * Date:2023/1/11 14:11
     * @param $dateList
     */
    protected function handleCostAndProfit($dateList){
        //成本
        $sort = array("45","15","105","163","193","44","14","104");
        $costAndProfitInfo = $this->_dao->getCostAndProfitInfo($dateList,$sort);
        $costAndProfitList = $this->calcStockData($costAndProfitInfo);

        //市场价格
        $priceSort = array("072023","672023","D22043","072023c", "5320234","123112","6731126", "D23112");
        $marketPriceInfo = $this->maindao->getMarketPriceInfo($dateList,$priceSort);
        $marketPriceList = $this->calcStockData($marketPriceInfo);

        //毛利计算
        $grossProfitInfo = $this->calcGrossProfit($costAndProfitInfo,$marketPriceInfo);
        $grossProfitList = $this->calcStockData($grossProfitInfo);
        $this->assign("sort",$sort);
        $this->assign("costAndProfitList",$costAndProfitList);
        $this->assign("priceSort",$priceSort);
        $this->assign("marketPriceList",$marketPriceList);
        $this->assign("grossProfitList",$grossProfitList);
    }

    /**
     * 通过市场价格 和 成本 计算毛利
     * Created by zfy.
     * Date:2023/1/11 16:56
     * @param $costAndProfitInfo
     * @param $marketPriceInfo
     * @return array
     */
    protected function calcGrossProfit($costAndProfitInfo,$marketPriceInfo){
        $typeMatch = array("072023"=>"45","672023"=>"15","D22043"=>"105","072023c"=>"163","5320234"=>"193","123112"=>"44","6731126"=>"14","D23112"=>"104");
        $grossProfitInfo = array();
        foreach ($marketPriceInfo as $index => $item) {
            foreach ($item as $priceId => $price) {
                $grossProfitInfo[$index][$priceId] = $price - $costAndProfitInfo[$index][$typeMatch[$priceId]];
            }
        }
        return $grossProfitInfo;
    }



    /**
     * 处理开工率、旬产量和日均成交量数据
     * Created by zfy.
     * Date:2023/1/10 16:44
     * @param $dateList
     * @param $xunDateList
     */
    protected function handleOperatingData($dateList,$xunDateList){
        //开工率
        $operatingSort = array("GC_KGL","DL_KGL");
        $operatingInfo = $this->_dao->getOperatingInfo($dateList,$operatingSort);
        $operatingList = $this->calcStockData($operatingInfo);
        //成交量
        $turnoverSort = array("1","4","2");
        $turnoverInfo = $this->gcdao->getTurnoverInfo($dateList,$turnoverSort);
        $turnoverList = $this->calcStockData($turnoverInfo);
        //重点钢企旬日均产量
        $yieldSort = array("D1","D2","D3");
        $yieldInfo = $this->maindao->getYieldInfo($xunDateList['this_end_date']);
        $yieldList = $this->calcStockData($yieldInfo);
        $xun = "";
        if ($yieldList){
            $day = date("j",strtotime($yieldList['thisXun']['DATE']));
            if ($day == 1){
                $xun = "上旬";
            }elseif ($day == 11){
                $xun = "中旬";
            }elseif ($day == 21){
                $xun = "下旬";
            }
        }
        $this->assign("operatingDate",date("n月j日",strtotime($this->_dao->getNewOperatingDate($dateList))));
        $this->assign("operatingXunStr",date("n月",strtotime($yieldList['thisXun']['DATE'])).$xun);
        $this->assign("operatingSort",$operatingSort);
        $this->assign("operatingList",$operatingList);
        $this->assign("turnoverSort",$turnoverSort);
        $this->assign("turnoverList",$turnoverList);
        $this->assign("yieldSort",$yieldSort);
        $this->assign("yieldList",$yieldList);
    }

    /**
     * 处理 市场 钢厂 五大库存
     * Created by zfy.
     * Date:2023/1/10 11:38
     * @param $dateList
     */
    protected function handleStockInfo($dateList){
        $marketStockSort = array(5,4,3,1,2,6);
        $steelStockSort = array(1,2,3,4,5,6);
        $steelMarketStockSort = array(5,4,3,1,2,6);
        //市场库存
        $marketStock = $this->_dao->getMarketStock($dateList,$GLOBALS['STOCK_TYPE_STR']);
        $marketStockList = $this->calcStockData($marketStock);
        //钢厂库存
        $steelStock = $this->gcdao->getSteelStock($dateList,$GLOBALS['STOCK_TYPE_STR']);
        $steelStockList = $this->calcStockData($steelStock);
        //市场钢厂合计库存
        $steelMarketStock = $this->gcdao->getSteelMarketStock($dateList,$GLOBALS['STOCK_TYPE_STR']);
        $steelMarketStockList = $this->calcStockData($steelMarketStock);

        $this->assign("date",date("n月j日",strtotime($this->gcdao->getNewStockDate($dateList))));
        $this->assign("marketStockSort",$marketStockSort);
        $this->assign("steelStockSort",$steelStockSort);
        $this->assign("steelMarketStockSort",$steelMarketStockSort);
        $this->assign("marketStockList",$marketStockList);
        $this->assign("steelStockList",$steelStockList);
        $this->assign("steelMarketStockList",$steelMarketStockList);
    }

    /**
     * 计算库存的 旬 月 年同比数据
     * Created by zfy.
     * Date:2023/1/10 11:59
     * @param $stockList
     * @return array
     */
    protected function calcStockData($stockList)
    {
        $retList = array();
        $retList['thisXun'] = $stockList['thisXun'];
        foreach ($stockList as $index => $item) {
            if ($index != 'thisXun') {
                foreach ($item as $type => $stock) {
                    $retList[$index][$type] = $this->zhangdie($stockList['thisXun'][$type] - $stock);
                }
            }
        }
        return $retList;
    }

    /**
     * 处理钢厂调价
     * Created by zfy.
     * Date:2023/1/10 9:24
     * @param $xunDateList
     */
    protected function handleSteelPrice($xunDateList){
        $priceInfo = $this->gcdao->getSteelPrice($xunDateList,$GLOBALS['XUN_ONLY_ID_LIST']);
        $this->assign("priceInfo",$priceInfo);
    }

    /**
     * 处理矿煤焦钢期货主力合约及国际大宗商品、外汇市场
     * Created by zfy.
     * Date:2023/1/9 15:33
     * @param $workDate
     */
    protected function handleFutures($workDate){
        //期货数据
        $futuresInfo = $this->_dao->getFuturesByTypes($workDate,$GLOBALS['QH_TYPES']);
        $thisFuturesInfo = $futuresInfo['endPrice'];
        $lastFuturesInfo = $futuresInfo['startPrice'];
        $list = array();
        foreach ($GLOBALS['QH_TYPES_LIST'] as $type) {
            foreach ($thisFuturesInfo as $key => $thisItem) {
                if ($type == $key){
                    $list[$type]['thisPrice'] = $thisItem;
                }
            }
            foreach ($lastFuturesInfo as $lKey => $lastItem) {
                if ($type == $lKey){
                    $list[$type]['lastPrice'] = $lastItem;
                }
            }
        }
        foreach ($list as $index => &$item) {
            $item['priceZd'] = $this->zhangdie($item['thisPrice']-$item['lastPrice']);
        }

        //纽原油数据
        $NYCrudeOilInfo = $this->_dao->getNYCrudeOil($workDate,$GLOBALS['YUAN_YOU_BM']);
        $NYCrudeOilInfo['priceZd'] = $this->zhangdie($NYCrudeOilInfo['endPrice']-$NYCrudeOilInfo['startPrice']);

        //美元指数
        $USDIndexInfo = $this->_dao->getUSDIndex($workDate);
        $USDIndexInfo['priceZd'] = $this->zhangdie($USDIndexInfo['endPrice']-$USDIndexInfo['startPrice']);

        //人民币汇率
        $RMBRateInfo = $this->_dao->getRMBRate($workDate);
        $RMBRateInfo['priceZd'] = $this->zhangdie($RMBRateInfo['endPrice']-$RMBRateInfo['startPrice']);

        $this->assign("QH_TYPES_LIST",$GLOBALS['QH_TYPES_LIST']);
        $this->assign("list",$list);
        $this->assign("NYCrudeOilInfo",$NYCrudeOilInfo);
        $this->assign("USDIndexInfo",$USDIndexInfo);
        $this->assign("RMBRateInfo",$RMBRateInfo);
    }

    /**
     * 处理市场价格变化情况
     * Created by zfy.
     * Date:2023/1/9 8:46
     * @param $xunDateList
     * @param $workDate
     */
    protected function handleMarketPrice($xunDateList,$workDate){
        //钢材市场价格ID
        $steelPriceIdList = array("942023","943012","943112","944312","945031");
        //原燃料市场价格ID
        $rawPriceIdList = array("1886103","118210","678411","D28311","L18710","418910","539422");

        $priceInfo = $this->maindao->getMarketPrice($workDate,$steelPriceIdList,$rawPriceIdList);
        $avgPriceInfo = $this->maindao->getAvgMarketPrice($xunDateList,$steelPriceIdList,$rawPriceIdList);
        $steelPriceList = $this->calcPriceInfo($steelPriceIdList,$priceInfo,$avgPriceInfo);
        $rawPriceList = $this->calcPriceInfo($rawPriceIdList,$priceInfo,$avgPriceInfo);

        $this->assign("steelPriceList",$steelPriceList);
        $this->assign("rawPriceList",$rawPriceList);
        $this->assign("steelPriceIdList",$steelPriceIdList);
        $this->assign("rawPriceIdList",$rawPriceIdList);
        $this->assign("thisXunDateStr",date("m.d",strtotime($xunDateList['this_start_date']))."-".date("m.d",strtotime($xunDateList['this_end_date'])));
        $this->assign("lastXunDateStr",date("m.d",strtotime($xunDateList['last_start_date']))."-".date("m.d",strtotime($xunDateList['last_end_date'])));
    }

    protected function calcPriceInfo($steelPriceIdList,$priceInfo,$avgPriceInfo){
        $list = array();
        foreach ($steelPriceIdList as $steelItem) {
            foreach ($priceInfo as $priceType => $priceItem) {
                foreach ($priceItem as $pItem) {
                    if ($steelItem == $pItem['topicture'] || $steelItem == $pItem['mastertopid']){
                        if ($priceType=='startPrice'){
                            $list[$steelItem]['lastPrice'] = $pItem['price'];
                        }else{
                            $list[$steelItem]['thisPrice'] = $pItem['price'];
                        }
                    }
                }
            }
            foreach ($avgPriceInfo as $avgPriceType => $avgPriceItem) {
                foreach ($avgPriceItem as $avgPItem) {
                    if ($steelItem == $avgPItem['topicture'] || $steelItem == $avgPItem['mastertopid']){
                        if ($avgPriceType=='startPrice'){
                            $list[$steelItem]['lastAvgPrice'] = round($avgPItem['avgPrice']);
                        }else{
                            $list[$steelItem]['thisAvgPrice'] = round($avgPItem['avgPrice']);
                        }
                    }
                }
            }
        }
        foreach ($list as $index => &$item) {
            $item['priceZd'] = $this->zhangdie($item['thisPrice']-$item['lastPrice']);
            $item['avgPriceZd'] = $this->zhangdie($item['thisAvgPrice']-$item['lastAvgPrice']);
        }
        return $list;
    }

    /**
     * 处理预测复盘数据
     * Created by zfy.
     * Date:2023/1/7 15:15
     * @param $xunDateList
     * @param $yc_type
     */
    protected function handleForecastInfo($xunDateList,$yc_type){
        $forecastInfo = $this->_dao->getForecastInfo($xunDateList,$yc_type);
        $this->assign("forecastDateStr",date("n月j日",strtotime($xunDateList['this_start_date'])).date("至n月j日",strtotime($xunDateList['this_end_date'])));
        $this->assign("forecastInfo",$forecastInfo);
    }

    /**
     * 区域价格对比 新余自填日定价录入管理
     * Created by zfy.
     * Date:2023/7/12 11:27
     * @param $params
     */
    public function regional_price_input_manage($params){
        $GUID = $params['GUID'];
        $userInfo = $this->getUserInfo($params['GUID']);
        $params['sdate'] = $params['sdate'] ? $params['sdate']: date("Y-m-d",strtotime("-6 months"));
        $params['edate'] = $params['edate'] ? $params['edate']: date("Y-m-d");
        $this->assign('GUID',$GUID);
        $this->assign('params',$params);

    }

    /**
     * 区域价格对比 新余自填日定价 列表
     * Created by zfy.
     * Date:2023/7/12 15:07
     * @param $params
     */
    public function get_regional_price_input_list($params){
        $params['sdate'] = $params['sdate'] ? $params['sdate']: date("Y-m-d",strtotime("-6 months"));
        $params['edate'] = $params['edate'] ? $params['edate']: date("Y-m-d");
        $list = $this->xg->get_regional_price_input_list($params);
//        foreach ($list as $index => &$item) {
//            $item['dta1'] = $GLOBALS['REGIONAL_PRICE_VARIETY'][$item['dta1']];
//        }
        $arr['code'] = "0";
        $arr['count'] =  $this->xg->get_regional_price_input_total($params);
        $arr['data'] =  $list;
        echo $this->pri_JSON($arr);
    }

    /**
     * 区域价格对比 新余自填日定价录入
     * Created by zfy.
     * Date:2023/7/12 11:48
     * @param $params
     */
    public function regional_price_input($params){
        $params['date'] = $params['date'] == "" ? date("Y-m-d") : $params['date'];
        if ($params['id']!=''){
            $list = $this->xg->get_regional_price_input_row($params['id']);
        }else{
            $list['date'] = date("Y-m-d");
        }
        $this->assign("id",$params['id']);
        $this->assign("list",$list);
        $this->assign("params",$params);
        $this->assign("market_name",$GLOBALS['MARKET_NAME']);
        $this->assign("regional_price_variety",$GLOBALS['REGIONAL_PRICE_VARIETY_LWG'] );
    }

    /**
     * 新钢区域价格对比数据录入
     * Created by zfy.
     * Date:2023/7/12 16:01
     * @param $params
     */
    public function do_regional_price_input($params){
        $userInfo = $this->getUserInfo($params['GUID']);
        $arr['code'] = "0";
        $arr['msg'] = "保存成功！";
//        echo "<pre>";print_r($params);exit();
        if ($params['id']!=''){
            $this->xg->update_regional_price_input($params,$userInfo);
        }else {
            if ($this->xg->get_regional_price_input_count($params)==0)
            {
                $this->xg->insert_regional_price_input($params,$userInfo);
            }else{
                $arr['code'] = "1";
                $arr['msg'] = "当前日期已录入，请勿重复录入！";
            }
        }
        echo $this->pri_JSON($arr);
    }

    /**
     * 删除 新钢区域价格对比数据
     * Created by zfy.
     * Date:2023/7/12 16:00
     * @param $params
     */
    public function delete_regional_price($params){
        $this->xg->delete_regional_price_input($params['id']);
        $arr['code'] = "0";
        echo $this->pri_JSON($arr);
    }

    /**
     * 区域价格对比 杂项配置管理
     * Created by zfy.
     * Date:2023/7/12 16:47
     * @param $params
     */
    public function regional_price_setting_manage($params){
        $GUID = $params['GUID'];
        $userInfo = $this->getUserInfo($params['GUID']);
        $params['sdate'] = $params['sdate'] ? $params['sdate']: date("Y-m-d",strtotime("-6 months"));
        $params['edate'] = $params['edate'] ? $params['edate']: date("Y-m-d");
        $this->assign('GUID',$GUID);
        $this->assign('params',$params);
        $this->assign("market_name",$GLOBALS['ALL_MARKET_NAME']);
        $this->assign("regional_price_variety",$GLOBALS['REGIONAL_PRICE_VARIETY'] );
    }

    /**
     * 区域价格对比 杂项配置录入
     * Created by zfy.
     * Date:2023/7/12 11:48
     * @param $params
     */
    public function regional_price_setting($params){
        if ($params['id']!=''){
            $list = $this->_dao->get_regional_price_setting_row($params['id']);
        }else{
            $list['date'] = date("Y-m-d");
        }
        $this->assign('GUID',$params['GUID']);
        $this->assign("list",$list);
        $this->assign("id",$params['id']);
        $this->assign("market_name",$GLOBALS['ALL_MARKET_NAME']);
        $this->assign("regional_price_variety",$GLOBALS['REGIONAL_PRICE_VARIETY'] );
    }

    /**
     * 区域价格对比 杂项配置 列表
     * Created by zfy.
     * Date:2023/7/12 15:07
     * @param $params
     */
    public function get_regional_price_setting_list($params){
        $params['sdate'] = $params['sdate'] ? $params['sdate']: date("Y-m-d",strtotime("-6 months"));
        $params['edate'] = $params['edate'] ? $params['edate']: date("Y-m-d");
        $list = $this->_dao->get_regional_price_setting_list($params);
        foreach ($list as $index => &$item) {
            $item['price_type'] = $GLOBALS['REGIONAL_PRICE_VARIETY'][$item['price_type']];
        }
        $arr['code'] = "0";
        $arr['count'] =  count($list);
        $arr['data'] =  $list;
        echo $this->pri_JSON($arr);
    }

    /**
     * 区域价格对比 杂项配置录入
     * Created by zfy.
     * Date:2023/7/12 16:01
     * @param $params
     */
    public function do_regional_price_setting($params){
        $userInfo = $this->getUserInfo($params['GUID']);
        if ($params['id']!=''){
            $this->_dao->update_regional_price_setting($params,$userInfo);
        }else {
            $this->_dao->insert_regional_price_setting($params,$userInfo);
        }
        $this->layAlert("保存成功！");
    }

    /**
     * 删除 区域价格对比 杂项配置
     * Created by zfy.
     * Date:2023/7/12 16:00
     * @param $params
     */
    public function delete_regional_price_setting($params){
        $this->_dao->delete_regional_price_setting($params['id']);
        $arr['code'] = "0";
        echo $this->pri_JSON($arr);
    }

    /**
     * 区域价格对比
     * Created by zfy.
     * Date:2023/7/13 11:16
     * @param $params
     */
    public function regional_price_comparison($params){
        $priceType = $params['price_type'];
        $settingInfo = $GLOBALS['REGIONAL_PRICE_VARIETY_INFO'][$priceType];
        $marketCode = $GLOBALS['REGIONAL_PRICE_MARKET_CODE'][$priceType];
        $marketCodeFlip = array_flip($marketCode);

        $list = array();
        foreach ($settingInfo as $marketName => $varietyInfo) {
            $key = $marketCodeFlip[$marketName];
            $sdate = $params["sdate"];
            $edate = $params["edate"];
            $sdate2 = $params["sdate2"];
            $edate2 = $params["edate2"];
            if ($sdate==""){
                continue;
            }
            $settingPrice = $this->_dao->get_new_regional_price_setting($priceType,$edate2);
            //使用新钢自填价格
            if ($varietyInfo == ""){
                $list[$key]['lastPrice'] = $this->xg->get_regional_avg_price($priceType,$marketName,$sdate,$edate);
                $list[$key]['thisPrice'] = $this->xg->get_regional_avg_price($priceType,$marketName,$sdate2,$edate2);
            }else{
                $list[$key]['lastPrice'] = $this->getMySteelAvgPrice($varietyInfo,$sdate,$edate);
                $list[$key]['thisPrice'] = $this->getMySteelAvgPrice($varietyInfo,$sdate2,$edate2);
            }
            $list[$key]['up_down'] = $this->zhangdie($list[$key]['thisPrice'] - $list[$key]['lastPrice']);
            $list[$key]['freight'] = $settingPrice[$marketName]['freight'];
            $list[$key]['discount'] = $settingPrice[$marketName]['discount'];
            $list[$key]['pound_difference'] = $settingPrice[$marketName]['pound_difference'];
            $list[$key]['other_sum'] = (int)$list[$key]['freight'] + (int)$list[$key]['discount'] + (int)$list[$key]['pound_difference'];
            $list[$key]['restore_price'] = (int)$list[$key]['thisPrice'] - (int)$list[$key]['other_sum'];
        }

        $this->assign("table_set",$GLOBALS['REGIONAL_PRICE_TABLE_SET'][$priceType]);
        $this->assign("list",$list);
        $this->assign("marketCode",$marketCode);
        $this->assign("settingInfo",$settingInfo);
        $this->assign("varietyName",$GLOBALS['REGIONAL_PRICE_VARIETY'][$priceType]);
        $this->assign("params",$params);
    }

    /**
     * 根据品名获取我的钢铁均价
     * Created by zfy.
     * Date:2023/7/13 11:28
     * @param $varietyInfo
     * @param $sdate
     * @param $edate
     * @return float|int
     */
    protected function getMySteelAvgPrice($varietyInfo,$sdate,$edate)
    {
        $url = $GLOBALS['XG_IP'] . "/api/jgyc/getMysteelPrice2?sdate=" . $sdate . "&edate=" . $edate;
        $response = file_get_contents($url, false, stream_context_create(array("ssl" => array("verify_peer" => false, "verify_peer_name" => false))));
        $response = json_decode($response, true);
        $data = $response['data'];
        $allPrice = 0;
        $count = 0;
        foreach ($data as $date => $item) {
            //"无锡(沙钢)|热卷4.75mm/Q235B,上海(本钢)|热卷4.75mm/Q235B"   逗号隔开的要取多个市场的均价
            if (strstr($varietyInfo,',')){
                $varietyList = explode(",",$varietyInfo);
                foreach ($varietyList as $variety) {
                    $allPrice += $item[$variety];
                }
                $count += count($varietyList);
            }else{
                if ($item[$varietyInfo]) {
                    $allPrice += $item[$varietyInfo];
                    $count++;
                }
            }
        }
        return $count == 0 ? 0 : round($allPrice / $count);
    }

    /**
     * excel导入管理
     * Created by zfy.
     * Date:2023/7/14 10:11
     * @param $params
     */
    public function excel_import_manage($params){
        $userInfo = $this->getUserInfo($params['GUID']);
        $sdate = date("Y-m",strtotime("-6 month"));
        $edate = date("Y-m");

        $this->assign("name", $GLOBALS['EXCEL_TABLE_IMPORT_TYPE_NAME_INFO'][$params['dta_type']]['name']);
        $this->assign("sdate", $sdate);
        $this->assign("edate", $edate);
        $this->assign("params", $params);
//        $GLOBALS['EXCEL_TABLE_IMPORT_TYPE'];
    }

    /**
     * 导入通用excel
     * Created by zfy.
     * Date:2023/7/14 10:12
     * @param $params
     */
    public function import_common_excel($params){
        $userInfo = $this->getUserInfo($params['GUID']);
        $dta_type = $params['dta_type'];
        if ($_FILES['file']["error"] == "0") {
            $file = $_FILES['file']['tmp_name'];
            $fileType=\PhpOffice\PhpSpreadsheet\IOFactory::identify($file);//自动获取文件的类型提供给phpexcel用
            $objReader=\PhpOffice\PhpSpreadsheet\IOFactory::createReader($fileType);//获取文件读取操作对象
            $objReader->setReadDataOnly(true); //只读取数据,会智能忽略所有空白行,这点很重要！！！
            $objPHPExcel = $objReader->load($file); //加载Excel文件

            $sheet =  $objPHPExcel->getActiveSheet();

            //获取当前工作表最大行数
//            $rows = $sheet->getHighestRow();
            $data = $sheet->toArray();
            //获取当前工作表最大列数,返回的是最大的列名
            //$cols = $sheet->getHighestColumn();
//            echo "<pre>";print_r($data);
            if(empty($data)){
                $res["Code"] = 1;
            }else{
                switch ($dta_type) {
                    //区域流向表、监管月报产量数据表
                    case "XG-AREA-FLOW":
                    case "XG-MONTHLY-OUTPUT-DATA":
                        $sdate = date("Y-m-01", strtotime($params['date']));
                        $edate = date("Y-m-01", strtotime($params['date']));
                        break;
                }

                $count = $this->xg->getOne("select count(1) from data_table_base where type=5 and dta_type='".$dta_type."' and sdate='".$sdate."'");
                if ($count > 0){
                    $res["Code"] = 1;
                    $res["msg"] = "当前日期数据已录入，请删除后再导入！";
                    echo json_encode($res);exit();
                }

                $sql = "INSERT INTO data_table_base SET type=5,dta_type='".$dta_type."' ";
                $sql .= ",sdate='".$sdate."',edate='".$edate."' ";
                $sql .= ",createtime=now(),createuser='".$userInfo['Uid']."',createusername='".$userInfo['TrueName']."' ";
                $this->xg->execute($sql);
                $baseid = $this->xg->insert_id();

                foreach($data as $key=>$tmp){
                    $arrayFields = array();
                    foreach ($tmp as $k => $i) {
                        $arrayFields[] = "dta" . ($k + 1);
                    }
                    $fields = array();
                    foreach ($arrayFields as $a =>$v) {
                        switch ($dta_type){
                            //区域流向表要处理百分数 保留两位小鼠
                            case "XG-AREA-FLOW":
                                if (is_numeric($tmp[$a])){
                                    $tmp[$a] = sprintf("%.2f",round($tmp[$a]*100,2))."%";
                                }
                                break;
                            //监管月报产量数据表 保留三位小数
                            case "XG-MONTHLY-OUTPUT-DATA":
                                if (is_numeric($tmp[$a])){
                                    $tmp[$a] = sprintf("%.3f",round($tmp[$a],3));
                                }
                                break;
                        }
                        $fields[] = $v . "='" . $tmp[$a] . "'";
                    }

                    $fields = implode(",", $fields);
                    $sql = "INSERT INTO data_table SET baseid='".$baseid."',dta_type='".$dta_type."' ";
                    $sql .= ",dta_ym='".$sdate."',createtime=now(),createuser='".$userInfo['Uid']."' , $fields";

                    $this->xg->execute($sql);
                }
                $res["Code"] = 0;
            }
        }
        else
        {
            $res["Code"] = 1;
        }
        echo json_encode($res);
    }

    /**
     * excel表格列表
     * Created by zfy.
     * Date:2023/7/14 11:57
     * @param $params
     */
    public function get_excel_table_list($params){
        $sdate = $params['sdate'];
        $edate = $params['edate'];
        $dta_type = $params['dta_type'];

        $page = $params['page'];
        $limit = $params['limit'];

        if ($page < 1) {
            $page = 1;
        }
        $start = ($page - 1) * $limit;
        $where = "";

        if($sdate){
            $where .= " and sdate>='".$sdate."' ";
        }
        if($edate){
            $where .= " and sdate<='".$edate."' ";
        }

        $total = $this->xg->get_excel_data_total(5,$dta_type,$where);
        $dataInfo = $this->xg->get_excel_data_list(5,$dta_type,$where, $start, $limit);

        foreach ($dataInfo as &$articleInfo) {
            $articleInfo['uptype'] = date("n月",strtotime($articleInfo['sdate'])).$GLOBALS['EXCEL_TABLE_IMPORT_TYPE_NAME_INFO'][$articleInfo['dta_type']]['name'];
        }

        $code = 0;
        if ($dataInfo) {
            $code = 0;
        }
        $return_array = array(
            "code" => $code,
            "data" => $dataInfo,
            "count" => $total
        );
        echo json_encode($return_array);
    }

    /**
     * excel表格详细
     * Created by zfy.
     * Date:2023/7/14 11:57
     * @param $params
     */
    public function excel_table_detail($params){
        $fieldsNum = $GLOBALS['EXCEL_TABLE_IMPORT_TYPE_NAME_INFO'][$params['dta_type']]['field_num'];
        $needMergeCol = $GLOBALS['EXCEL_TABLE_IMPORT_TYPE_NAME_INFO'][$params['dta_type']]['need_merge_col'];
        $fieldsList = array();
        for($i=1;$i<=$fieldsNum;$i++){
            $fieldsList[] = "dta".$i;
        }
        $id = $params['id'];
        $data = $this->xg->getZgxDetail($id);
        $this->assign("data", $data);
        $this->assign("fieldsNum", $fieldsNum);
        $this->assign("needMergeCol", $needMergeCol);
        $this->assign("fieldsList", $fieldsList);
    }

    /**
     * 删除
     * Created by zfy.
     * Date:2023/7/25 14:02
     * @param $params
     */
    public function delete_excel_table($params){
        $this->xg->delete_excel_info_by_id($params['id']);
        $return_array = array(
            "code" => 1,
            "msg" => "删除成功"
        );
        echo json_encode($return_array);
    }

    /**
     * excel文件上传服务器管理
     * Created by zfy.
     * Date:2023/7/20 9:02
     * @param $params
     */
    public function excel_upload_manage($params){
        $params['date'] = $params['date'] == "" ? date("Y-m") : $params['date'];
        $userInfo = $this->getUserInfo($params['GUID']);
//        $sdate = date("Y-m-01");
//        $edate = date("Y-m-d");
        $sdate = date("Y-m",strtotime("-6 month"));
        $edate = date("Y-m");

        $this->assign("name", $GLOBALS['EXCEL_TABLE_IMPORT_TYPE_NAME_INFO'][$params['dta_type']]['name']);
        $this->assign("sdate", $sdate);
        $this->assign("edate", $edate);
        $this->assign("varietyTypeList", $GLOBALS['TABLE_VARIETY_TYPE']);
        $this->assign("params", $params);
    }

    /**
     * excel文件上传服务器管理
     * Created by zfy.
     * Date:2023/7/20 9:30
     * @param $params
     */
    public function excel_upload_input($params){
        $userInfo = $this->getUserInfo($params['GUID']);
        $dta_type = $params['dta_type'];
        if ($_FILES['file']["error"] == "0") {
//            include_once("/usr/local/www/libs/PHPExcel/PHPExcel.php");

            $file = $_FILES['file']['tmp_name'];
            $type = pathinfo($_FILES['file']['name']);
            $type = strtolower($type["extension"]);
            $fileName = "XG-excel".date("Ymdhis").rand(100,999).".".$type;
            if (!file_exists(BASE_DIR.XGUPLOADFILE)) {
                if (!mkdir(BASE_DIR.XGUPLOADFILE, 0777)) {
                    $res["Code"] = 1;
                    $res["msg"] = "目录不存在，上传失败";
                    echo json_encode($res);exit();
                }
            }
            $fileType=\PhpOffice\PhpSpreadsheet\IOFactory::identify($file);//自动获取文件的类型提供给phpexcel用
            $objReader=\PhpOffice\PhpSpreadsheet\IOFactory::createReader($fileType);//获取文件读取操作对象
            $objReader->setReadDataOnly(true); //只读取数据,会智能忽略所有空白行,这点很重要！！！
            $objPHPExcel = $objReader->load($file); //加载Excel文件
            $writer = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($objPHPExcel,$fileType);
            $writer->save(BASE_DIR.XGUPLOADFILE.'/'.$fileName);

            // 读取 Excel 文件
//            $excel = PHPExcel_IOFactory::load($file);

            // 保存 Excel 文件
//            $writer = PHPExcel_IOFactory::createWriter($excel, 'Excel2007');
//            $writer->save(BASE_DIR.XGUPLOADFILE.'/'.$fileName);

//            switch ($dta_type) {
//                //接单指标
//                case "XG-ORDER-INDEX-DATA":
//                    $sdate = date("Y-m-01", strtotime($params['date']));
//                    $edate = $params['date'];
//                    break;
//            }

            $count = $this->xg->getOne("select count(1) from data_table where  dta_type='".$dta_type."' and dta_ym='".$params['date']."' and dta_vartype='" . $params['dta_vartype'] . "'");
            if ($count > 0){
                $res["Code"] = 1;
                $res["msg"] = "当前日期数据已录入，请删除后再导入！";
                echo json_encode($res);exit();
            }

            $fileStr = explode(".",$_FILES['file']['name']);
            $sql = "INSERT INTO data_table SET dta_type='" . $dta_type . "' ";
            $sql .= ",dta_ym='" . $params['date'] . "',dta_vartype='" . $params['dta_vartype'] . "',dta1='".DOMAIN_URL.XGUPLOADFILE.'/'.$fileName."',dta2='".BASE_DIR.XGUPLOADFILE.'/'.$fileName."',dta3='".$fileStr[0]."'";
            $sql .= ",createtime=now(),createuser='" . $userInfo['Uid'] . "'";
            $this->xg->execute($sql);
            $res["Code"] = 0;
        }
        else
        {
            $res["Code"] = 1;
        }
        echo json_encode($res);
    }

    /**
     * 接单指标excel列表
     * Created by zfy.
     * Date:2023/7/20 10:48
     * @param $params
     */
    public function get_excel_upload_table_list($params){
        $sdate = $params['sdate'];
        $edate = $params['edate'];
        $dta_type = $params['dta_type'];

        $page = $params['page'];
        $limit = $params['limit'];

        if ($page < 1) {
            $page = 1;
        }
        $start = ($page - 1) * $limit;
        $where = "";

        if($sdate){
            $where .= " and dta_ym>='".$sdate."' ";
        }
        if($edate){
            $where .= " and dta_ym<='".$edate."' ";
        }

        $total = $this->xg->get_excel_upload_data_total(5,$dta_type,$where);
        $dataInfo = $this->xg->get_excel_upload_data_list(5,$dta_type,$where, $start, $limit);

//        foreach ($dataInfo as &$articleInfo) {
//            $articleInfo['uptype'] = date("n月",strtotime($articleInfo['dta_ym'])).$GLOBALS['EXCEL_TABLE_IMPORT_TYPE_NAME_INFO'][$articleInfo['dta_type']]['name'])."(".$articleInfo['dta_vartype'].")";
//        }

        $code = 0;
        if ($dataInfo) {
            $code = 0;
        }
        $return_array = array(
            "code" => $code,
            "data" => $dataInfo,
            "count" => $total
        );
        echo json_encode($return_array);
    }

    /**
     * excel内容展示
     * Created by zfy.
     * Date:2023/7/28 9:04
     * @param $params
     */
    public function excel_upload_table_detail($params){
        $info = $this->xg->get_data_table_row($params['id']);
        $this->assign("info", $info);
        $this->assign("params", $params);
    }

    /**
     * excel 内容sheet选择
     * Created by zfy.
     * Date:2023/7/28 9:04
     * @param $params
     */
    public function excel_upload_sheet_select($params){
        $info = $this->xg->get_data_table_row($params['id']);
        $this->assign("info", $info);
    }



    /**
     * 删除
     * Created by zfy.
     * Date:2023/7/20 10:50
     * @param $params
     */
    public function delete_excel_upload_table($params){
        $this->xg->delete_excel_upload_table($params['id']);
        $return_array = array(
            "code" => 1,
            "msg" => "删除成功"
        );
        echo json_encode($return_array);
    }

    /**
     * 计划和产量数据表
     * Created by zfy.
     * Date:2023/7/17 15:13
     * @param $params
     */
    public function plan_output_manage($params){
        $params['date'] = $params['date'] == "" ? date("Y-m-d",strtotime("-1 day")) : $params['date'];
        $data = $this->getScrbData("GetScqkData", $params['date']);
        $tableList = array();
        foreach ($GLOBALS['PLAN_OUTPUT_TYPE_SETTING'][$params['type']] as $item) {
            $needRoundFields = array("RWCCL","YLJCL","NLJCL","YLJJHB");
            foreach ($data[$item] as $code => &$datum) {
                if (in_array($code,$needRoundFields)){
                    $datum = round((float)$datum);
                }
            }
            $tableList[$item] = $data[$item];
        }
        $this->assign("tableList", $tableList);
        $this->assign("varietyTypeList", $GLOBALS['TABLE_VARIETY_TYPE']);
        $this->assign("params", $params);
        $this->assign("dateStr",date("n月j日",strtotime($params['date'])));
    }

    /**
     * 库存数据表
     * Created by zfy.
     * Date:2023/7/18 8:46
     * @param $params
     */
    public function stock_data_manage($params)
    {
        $params['date'] = $params['date'] == "" ? date("Y-m-d",strtotime("-1 day")) : $params['date'];
        //上月最后一天
        $lastMonthEndDate = date('Y-m-t', strtotime($params['date'].' -1 month'));
        $settingData = $GLOBALS['STOCK_DATA_TYPE_SETTING'];
        $data = $this->getScrbData("GetCcpkcData", $params['date']);
        $lastMonthData = $this->getScrbData("GetCcpkcData", $lastMonthEndDate);
        $list = array();
        foreach ($settingData as $key => $settingDatum) {
            $keyList = explode(",",$settingDatum);
            foreach ($keyList as $item) {
                $list[$key]['DRKC'] += (float)$data[$item]['DRKC'];
                $list[$key]['DEKC'] += (float)$data[$item]['DEKC'];
                $list[$key]['PCZ'] += (float)$data[$item]['PCZ'];
                $list[$key]['YJZ'] += (float)$data[$item]['YJZ'];
                $list[$key]['last_month_stock'] += (float)$lastMonthData[$item]['DRKC'];
                $list[$key]['last_month_up_down'] += (float)$data[$item]['DRKC'] -(float)$lastMonthData[$item]['DRKC'];
            }
        }

        //累加的数据四舍五入
        foreach ($list as $index => &$item) {
            $item['DRKC'] = round($item['DRKC']);
            $item['DEKC'] = round($item['DEKC']);
            $item['PCZ'] = round($item['PCZ']);
            $item['YJZ'] = round($item['YJZ']);
            $item['last_month_stock'] = round($item['last_month_stock']);
            $item['last_month_up_down'] = round($item['last_month_up_down']);
        }
        $this->assign("params", $params);
        $this->assign("list", $list);
        $this->assign("thisDateStr", date("n月j日",strtotime($params['date'])));
        $this->assign("lastDateStr", date("n月j日",strtotime($lastMonthEndDate)));
        $this->assign("list", $list);
    }

    /**
     * 资金回笼管理
     * Created by zfy.
     * Date:2023/7/19 8:43
     * @param $params
     */
    public function capital_return_manage($params){
        $params['date'] = $params['date'] == "" ? date("Y-m-d") : $params['date'];
        $baseInfo = $this->xg->get_capital_return_input_base($params);
        $params['date'] = $baseInfo['sdate'];
        //该日期当月有多少天
        $monthDayNum = date("t",strtotime($params['date']));
        //日
        $today = date("j",strtotime($params['date']));
        $list = $this->xg->get_capital_return_input_list($params);
        $thisMonthList =  $this->xg->get_this_month_capital_return_input($params);
        $thisYearList =  $this->xg->get_this_year_capital_return_input($params);
        //当日为空也显示当月累计数据
        if (!$list){
            $i = 0;
            foreach ($GLOBALS['CAPITAL_RETURN_FIELDS'] as $CAPITAL_RETURN_FIELD) {
                $list[$i]['dta1'] = $CAPITAL_RETURN_FIELD;
                $i++;
            }
        }
        foreach ($list as $index => &$item) {
            $item['dta2'] = $item['dta2'] == "" ? $thisMonthList[$item['dta1']]['dta2'] : $item['dta2'];
            $item['dta4'] = $item['dta4'] == "" ? $thisMonthList[$item['dta1']]['dta4'] : $item['dta4'];
//            $item['month_sum'] = $thisMonthList[$item['dta1']]['sum'];
//            $item['calc_data'] = round($thisMonthList[$item['dta1']]['sum'] - ($item['dta2'] / $monthDayNum * $today), 2);
            $item['calc_data'] = round($item['dta4'] - ($item['dta2'] / $monthDayNum * $today), 2);
            $item['year_sum'] = $thisYearList[$item['dta1']]['yearSum'];
        }
        $this->assign("params", $params);
        $this->assign("list", $list);
        $this->assign("dateStr", date("n月j日",strtotime($params['date'])));
        $this->assign("fieldList",$GLOBALS['CAPITAL_RETURN_FIELDS']);
    }

    /**
     * 资金回笼列表
     * Created by zfy.
     * Date:2023/8/8 9:38
     * @param $params
     */
    public function capital_return_manage_list($params){
        $userInfo = $this->getUserInfo($params['GUID']);
        $params['sdate'] = $params['sdate'] ?$params['sdate']: date("Y-m-d",strtotime("-6 months"));
        $params['edate'] = $params['edate'] ?$params['edate']: date("Y-m-d");
        $this->assign('params',$params);
    }

    public function get_capital_return_list($params){
        $params['sdate'] = $params['sdate'] ?$params['sdate']: date("Y-m-d",strtotime("-6 months"));
        $params['edate'] = $params['edate'] ?$params['edate']: date("Y-m-d");
        $list = $this->xg->get_capital_return_list($params);
        foreach ($list as $index => &$item) {
            $item['dta1'] = $GLOBALS['REGIONAL_PRICE_VARIETY'][$item['dta1']];
            $item['createusername'] = $item['createusername'];
        }
        $arr['code'] = "0";
        $arr['count'] =  $this->xg->get_capital_return_total($params);
        $arr['data'] =  $list;
        echo $this->pri_JSON($arr);
    }

    /**
     * 资金回笼录入
     * Created by zfy.
     * Date:2023/7/12 11:48
     * @param $params
     */
    public function capital_return_input($params){
        if ($params['id']!=''){
            $list = $this->xg->get_capital_return_data_by_id($params);
            $params['date'] = $list['钢贸']['dta_ym'];
        }else {
            $params['date'] = date("Y-m-d");
            $list = $this->xg->get_last_capital_return_data($params);
        }
        $this->assign("list",$list);
        $this->assign("params", $params);
        $this->assign("fieldList",$GLOBALS['CAPITAL_RETURN_FIELDS']);
    }

    /**
     * 资金回笼数据录入
     * Created by zfy.
     * Date:2023/7/12 16:01
     * @param $params
     */
    public function do_capital_return_input($params){
        $userInfo = $this->getUserInfo($params['GUID']);
        $this->xg->insert_capital_return_input($params,$userInfo);
        $this->layAlert("保存成功！");
    }

    /**
     * 删除 资金回笼数据
     * Created by zfy.
     * Date:2023/7/12 16:00
     * @param $params
     */
    public function delete_capital_return($params){
        $this->xg->delete_capital_return_input($params['id']);
        $arr['code'] = "0";
        echo $this->pri_JSON($arr);
    }

    /**
     * 手持订单量管理
     * Created by zfy.
     * Date:2023/7/21 8:57
     * @param $params
     */
    public function hand_order_manage($params){
        $params['date'] = $params['date'] == "" ? date("Y-m-d",strtotime("-1 day")) : $params['date'];
        $url = $GLOBALS['XG_IP']."/api/jgyc/getData?date=".$params['date'];
        $response = file_get_contents($url, false, stream_context_create(array("ssl" => array("verify_peer" => false, "verify_peer_name" => false))));
        $data = json_decode($response, true);
        $tableList = array();
        foreach ($GLOBALS['HAND_ORDER_TYPE_SETTING'][$params['type']] as $item) {
            foreach ($data['data'] as $datum) {
                if ($datum['type'] == 2)continue;
                if (is_array($item)){
                    foreach ($item as $k => $i) {
                        if ($k == $datum['dataname']){
                            $tableList[$i] = $datum;
                        }
                    }
                }else{
                    if ($item == $datum['dataname']){
                        $tableList[$item] = $datum;
                    }
                }
            }
        }

        //处理四舍五入
        foreach ($tableList as $index => &$item) {
            $item['field4'] = round($item['field4']);
            $item['field5'] = round($item['field5']);
            $item['field6'] = round($item['field6']);
            $item['field7'] = round($item['field7']);
            $item['field11'] = round($item['field11']);
            $item['field12'] = round($item['field12']);
        }
        $this->assign("tableList", $tableList);
        $this->assign("varietyTypeList", $GLOBALS['TABLE_VARIETY_TYPE']);
        $this->assign("params", $params);
        $this->assign("dateStr",date("n月j日",strtotime($params['date'])));
    }

    function get_hgData($L2id,$L2OrderNo,$tscl="",$edate){
        //update 2023-05-23 L2OrderNo为排序序号，可变，可不传
        // echo "<pre/>";print_r($this->maindao);
        if($tscl == 1){
            $sql = 'SELECT a . * FROM (SELECT * FROM `L2Data` WHERE `L2Id` = \''.$L2id.'\' and DATE <= \''.$edate.'\' ORDER BY `L2Data`.`DATE` DESC) AS a GROUP BY DATE_FORMAT(a.`DATE`, \'%Y-%m\')  ORDER BY a.DATE desc limit 15';
        }else{
            // $sql = "select * from L2Data where L2id= '".$L2id."' and L2OrderNo= '".$L2OrderNo."' ".$where." order by DATE asc limit 15";
            $sql = 'SELECT a . * FROM (SELECT * FROM `L2Data` WHERE `L2Id` = \''.$L2id.'\' and DATE <= \''.$edate.'\' ORDER BY `L2Data`.`DATE` DESC) AS a ORDER BY a.DATE desc limit 15';
        }
		$query_data = $this->maindao->query( $sql );
        $ret_data = array();
        foreach($query_data as &$v){
            $riqi = date('Y年n月',strtotime($v["DATE"]));
            $v["DATE2"] = $riqi;
            if($v["is_yuce"] == 1){
                $v["DATE2"] = "<font color='blue'>". $riqi."</font>";
                $v["D1"] = "<font color='blue'>". $v["D1"]."</font>";
                $v["D2"] = "<font color='blue'>". $v["D2"]."</font>";
                $v["D3"] = "<font color='blue'>". $v["D3"]."</font>";
                $v["D4"] = "<font color='blue'>". $v["D4"]."</font>";
            }
            $ret_data[$riqi] = $v;
        }
		return $ret_data;
	}

	function getNiuYuanyou($sdate,$edate,$bianma){
		$yy_sql = "select * from gfutures_shpi where bianma= '".$bianma."' and datetime = '".$edate."'";
		$dataArray = $this->drcdao->getRow( $yy_sql );
		
		$yy_sql2 = "select * from gfutures_shpi where bianma= '".$bianma."' and datetime = '".$sdate."'";
		$dataArray2 = $this->drcdao->getRow( $yy_sql2 );
		
		$ret_dataArray = array();
        if( $dataArray2 ["mvalue"] == "" || $dataArray["mvalue"] == ""){
            $ret_dataArray ['zd'] = "0";
        }else{
            $ret_dataArray ['zd'] = $this->zhangdie ( $dataArray["mvalue"] - $dataArray2 ["mvalue"] );
        }
		$ret_dataArray ['tprice'] = $dataArray["mvalue"];
		$ret_dataArray ['lprice'] = $dataArray2 ["mvalue"];
		//print_r($dataArray2);
		return $ret_dataArray;
	}

	function getMeiYuanShpi($sdate,$edate){
		$sql = "select * from dolrate where rdate = '".$edate." 00:00:00'";
		$dataArray = $this->drcdao->getRow( $sql );

		$sql2 = "select * from dolrate where rdate = '".$sdate." 00:00:00'";
		$dataArray2 = $this->drcdao->getRow( $sql2 );
		
		$ret_dataArray = array();
		$ret_data ['zd'] = $this->zhangdie ( round($dataArray["rd2"] - $dataArray2 ["rd2"],4) );
		$ret_data ['tprice'] = $dataArray["rd2"];
		$ret_data ['lprice'] = $dataArray2["rd2"];
		//print_r($ret_data);
		return $ret_data;
	}

	function getRMBShpi($sdate,$edate){
		$sql = "select * from rmbrate where rdate = '".$edate." 00:00:00'";
		$dataArray = $this->drcdao->getRow( $sql );

		$sql2 = "select * from rmbrate where rdate = '".$sdate." 00:00:00'";
		$dataArray2 = $this->drcdao->getRow( $sql2 );
		
		$ret_dataArray = array();
		$ret_data ['zd'] = $this->zhangdie ( $dataArray["rd1"] - $dataArray2 ["rd1"] );
		$ret_data ['tprice'] = $dataArray["rd1"];
		$ret_data ['lprice'] = $dataArray2["rd1"];
		return $ret_data;
	}

	function get_gc_tiaojia($onlyIdList,$day){
        $where = "";
		foreach ($onlyIdList as $index => $item) {
            if ($index != 0) $or = "or";
            $where .= " $or (onlyid='" . $item['onlyid'] . "' and gcid='" . $item['gcid'] . "')";
        }
        $dataArray = $this->gcdao->query("SELECT * FROM (SELECT id,the_price_tax,last_price_tax,the_price,last_price,onlyid, ftime, gcid,variety,material,specification FROM  `steelprice_info` WHERE ($where) and  ftime <= '".$day."' AND the_price_tax != '' GROUP BY ftime, onlyid, gcid ORDER BY id DESC)  AS a GROUP BY onlyid, gcid");

		$ret_dataArray = array();
		foreach ( $dataArray as $key => $value ){
			$ret_dataArray [$value ['onlyid']]['changerate_tax'] = $this->zhangdie($value ['changerate_tax']);
			$ret_dataArray [$value ['onlyid']]['variety'] = $value ['variety'];
			$ret_dataArray [$value ['onlyid']]['specification'] = $value ['specification'];
			$ret_dataArray [$value ['onlyid']]['material'] = $value ['material'];
			$ret_dataArray [$value ['onlyid']]['the_price_tax'] = $value ['the_price_tax'];
			$ret_dataArray [$value ['onlyid']]['ftime'] = date('n月j日',strtotime($value ['ftime']));
		}

		return $ret_dataArray;
	}

	//获取当前工作日矿煤焦钢期货主力合约及大宗商品外汇市场收盘价
	function get_qh_data($sdate,$edate,$types){
		$sql = "select dta_type,dta_6,dta_ym from data_table where dta_maxValStatus=1 and dta_type in (".$types.") and dta_ym = '".$edate."'";
		// echo $sql;
		$dataArray = $this->drcdao->query( $sql );
		$type_dataArray = array();
		foreach ( $dataArray as $key => $value ){
			$type_dataArray [$value ['dta_type']] = $value ['dta_6'];
		}

		$sql2 = "select dta_type,dta_6,dta_ym from data_table where dta_maxValStatus=1 and dta_type in (".$types.") and dta_ym ='".$sdate."'";
		// echo $sql2;
		$dataArray2 = $this->drcdao->query( $sql2 );

		$type_dataArray2 = array();
		foreach ( $dataArray2 as $key => $value ){
			$type_dataArray2 [$value ['dta_type']] = $value ['dta_6'];
		}
		$ret_data = array();
		foreach ( $type_dataArray2 as $pkey => $pvalue ){
            if($type_dataArray [$pkey] == "" || $pvalue == ""){
                $ret_data ['zd'] [$pkey] = "0";
            }else{
                $ret_data ['zd'] [$pkey] = $this->zhangdie ( $type_dataArray [$pkey] - $pvalue );
            }
			// $ret_data ['zd2'] [$pkey] = $pvalue - $type_dataArray2 [$pkey];
            // $ret_data ['zd3'] [$pkey] = $this->zhangdie3 ( $pvalue - $type_dataArray2 [$pkey] );
            // $ret_data ['zd4'] [$pkey] = $this->zhangdie4 ( $pvalue - $type_dataArray2 [$pkey] );
			$ret_data ['lprice'] [$pkey] = $pvalue;
			$ret_data ['tprice'] [$pkey] = $type_dataArray [$pkey];
		}
        if(empty($ret_data)){
            foreach ( $type_dataArray as $pkey => $pvalue ){
                $ret_data ['zd'] [$pkey] = "0";
                $ret_data ['tprice'] [$pkey] = $pvalue;
                $ret_data ['lprice'] [$pkey] = $type_dataArray2 [$pkey];
            }
        }
		return $ret_data;
	}

    //根据当前日获取上个钢之家工作日
    function get_xhdate($today){
        $flag=1;
        $lastday=$today;
        while(true){
            $lastday=date('Y-m-d',strtotime('-'.$flag.' day',strtotime($lastday)));
            //echo $lastday;
            if(file_get_contents('https://holiday.steelhome.com/isholiday.php?date='.$lastday)!="1")
            { 
                break;
            } 
        }
        $today_s=$today;
        $lastday_s=$lastday;
        return  $lastday_s;
	}

    //根据当前日获取下个钢之家工作日
    function get_xhdate2($today){
        $flag=1;
        $lastday=$today;
        while(true){
            $lastday=date('Y-m-d',strtotime('+'.$flag.' day',strtotime($lastday)));
            //echo $lastday;
            if(file_get_contents('https://holiday.steelhome.com/isholiday.php?date='.$lastday)!="1")
            { 
                break;
            } 
        }
        $today_s=$today;
        $lastday_s=$lastday;
        return  $lastday_s;
	}

    //获取六位价格id价格
	function hq_price_6($stime, $etime, $topicture){
		$sql = "select price,topicture,mconmanagedate from marketconditions where topicture in (" . $topicture . ") AND mconmanagedate >'" . $etime . " 00:00:00' AND mconmanagedate <'" . $etime . "  23:59:59' order by topicture asc ";
        //echo $sql;
		$query = $this->maindao->execute( $sql );

		foreach ( $query as $key => $value ){
			if (strstr ( $value ['price'], "-" )){
				$avgprice = explode ( "-", $value ['price'] );
				$value ['price'] = round ( ($avgprice ['0'] + $avgprice ['1']) / 2, 2 );
			}

			$tprice [$value ['topicture']] = $value ['price'];
		}
		$sql2 = "select price,topicture,mconmanagedate from marketconditions where topicture in (" . $topicture . ") AND mconmanagedate >'" . $stime . " 00:00:00' AND mconmanagedate <'" . $stime . "  23:59:59' order by topicture asc ";
		//echo $sql2;
		$query2 = $this->maindao->execute( $sql2 );

		foreach ( $query2 as $key2 => $value2 ){
			if (strstr ( $value2 ['price'], "-" )){
				$avgprice = explode ( "-", $value2 ['price'] );
				$value2 ['price'] = round ( ($avgprice ['0'] + $avgprice ['1']) / 2, 2 );
			}

			$lprice [$value2 ['topicture']] = $value2 ['price'];
		}

		foreach ( $tprice as $pkey => $pvalue ){
			$price_arr ['zd'] [$pkey] = $this->zhangdie ( $pvalue - $lprice [$pkey] );
			// $price_arr ['zd2'] [$pkey] = $pvalue - $lprice [$pkey];
            // $price_arr ['zd3'] [$pkey] = $this->zhangdie3 ( $pvalue - $lprice [$pkey] );
            // $price_arr ['zd4'] [$pkey] = $this->zhangdie4 ( $pvalue - $lprice [$pkey] );
			$price_arr ['tprice'] [$pkey] = $pvalue;
			$price_arr ['lprice'] [$pkey] = $lprice [$pkey];
		}
		return $price_arr;
	}
	//获取七位价格id价格
	function hq_price_7_week($stime, $etime, $mastertopid){
		$sql = "select price,mastertopid,mconmanagedate from marketconditions where mastertopid in (" . $mastertopid . ") AND mconmanagedate >'" . $etime . " 00:00:00' AND mconmanagedate <'" . $etime . "  23:59:59' order by mastertopid asc ";

		$query = $this->maindao->execute( $sql );
		foreach ( $query as $key => $value ){
			if (strstr ( $value ['price'], "-" )){
				$avgprice = explode ( "-", $value ['price'] );
				$value ['price'] = round ( ($avgprice ['0'] + $avgprice ['1']) / 2, 2 );
			}

			$tprice [$value ['mastertopid']] = $value ['price'];
		}

		$sql2 = "select price,mastertopid,mconmanagedate from marketconditions where mastertopid in (" . $mastertopid . ") AND mconmanagedate >'" . $stime . " 00:00:00' AND mconmanagedate <'" . $stime . "  23:59:59' order by mastertopid asc ";
		$query2 = $this->maindao->execute ( $sql2 );
		foreach ( $query2 as $key2 => $value2 ){
			if (strstr ( $value2 ['price'], "-" )){
				$avgprice = explode ( "-", $value2 ['price'] );
				$value2 ['price'] = round ( ($avgprice ['0'] + $avgprice ['1']) / 2, 2 );
			}

			$lprice [$value2 ['mastertopid']] = $value2 ['price'];
		}
		// echo "<pre>";print_r($lprice);
		foreach ( $tprice as $pkey => $pvalue ){
			$price_arr ['zd'] [$pkey] = $this->zhangdie ( $pvalue - $lprice [$pkey] );
			// $price_arr ['zd2'] [$pkey] = $pvalue - $lprice [$pkey];
            // $price_arr ['zd3'] [$pkey] = $this->zhangdie3 ( $pvalue - $lprice [$pkey] );
            // $price_arr ['zd4'] [$pkey] = $this->zhangdie4 ( $pvalue - $lprice [$pkey] );
			$price_arr ['tprice'] [$pkey] = $pvalue;
			$price_arr ['lprice'] [$pkey] = $lprice [$pkey];
		}
		return $price_arr;
	}
	//返回涨跌变化
	function zhangdie($int){
		$intstr = "";
		if($int<0){
			$intstr = "<font color=green><strong>↓".abs($int)."</strong></font>";
		}elseif($int>0){
			$intstr = "<font color=red><strong>↑".abs($int)."</strong></font>";
		}elseif($int=="" || $int==0){
			$intstr = "<strong>0</strong>";
		}else{
			$intstr = "<font ><strong>".$int."</strong></font>";
		}
		return $intstr;
	}
    function zhangdie2($int,$type=2){
        $percent = $type == 1 ? "%" : "";
        $intstr = "";
        if($int<0){
            $intstr = "<font color=green><strong>".($int).$percent."</strong></font>";
        }elseif($int>0){
            $intstr = "<font color=red><strong>".($int).$percent."</strong></font>";
        }elseif($int=="" || $int==0){
            $intstr = "<strong>0</strong>";
        }else{
            $intstr = "<font ><strong>".$int."</strong></font>";
        }
        return $intstr;
    }
    function zhangdie3($int){
		$intstr = "";
		if($int<0){
			$intstr = "下跌".abs($int)."元/吨";
		}elseif($int>0){
			$intstr = "上涨".abs($int)."元/吨";
		}else{
			$intstr = "持平";
		}
		return $intstr;
	}
    function zhangdie4($int){
		$intstr = "";
		if($int<0){
			$intstr = "下跌".abs($int)."美元/吨";
		}elseif($int>0){
			$intstr = "上涨".abs($int)."美元/吨";
		}else{
			$intstr = "持平";
		}
		return $intstr;
	}

    /**
     * 处理旬日期 返回上期和本期的时间区间
     * Created by zfy.
     * Date:2023/1/4 16:02
     * @param $today
     * @return array
     */
    protected function getXunCalcDateArray($today)
    {
        $day = date("d", strtotime($today));
        if ($day >= 25 || $day <= 4) {
            if ($day>=25){
                $date['last_start_date'] = date("Y-m-15", strtotime($today));
                $date['last_end_date'] = date("Y-m-24", strtotime($today));
                $date['this_start_date'] = date("Y-m-25", strtotime($today));
                $date['next_end_date'] = date("Y-m-14", strtotime($today."+1 month"));
            }else {
                $date['last_start_date'] = date("Y-m-15", strtotime($today . "-1 month"));
                $date['last_end_date'] = date("Y-m-24", strtotime($today . "-1 month"));
                $date['this_start_date'] = date("Y-m-25", strtotime($today . "-1 month"));
                $date['next_end_date'] = date("Y-m-14", strtotime($today));
            }
        } elseif ($day >= 5 && $day <= 14) {
            $date['last_start_date'] = date("Y-m-25", strtotime($today . "-1 month"));
            $date['last_end_date'] = date("Y-m-04", strtotime($today));
            $date['this_start_date'] = date("Y-m-05", strtotime($today));
            $date['next_end_date'] = date("Y-m-24", strtotime($today));
        } elseif ($day >= 15 && $day <= 24) {
            $date['last_start_date'] = date("Y-m-05", strtotime($today));
            $date['last_end_date'] = date("Y-m-14", strtotime($today));
            $date['this_start_date'] = date("Y-m-15", strtotime($today));
            $date['next_end_date'] = date("Y-m-04", strtotime($today."+1 month"));
        }
        $date['this_end_date'] = $today;
        $date['next_start_date'] = date("Y-m-d",strtotime($today."+1 day"));
        return $date;
    }

    /**
     * 获取上旬末 与 本旬末的工作日日期
     * Created by zfy.
     * Date:2023/1/9 14:36
     * @param $dateList
     * @return array
     */
    protected function getWorkDate($dateList)
    {
        $retList = array();
        $retList['this_start_date'] = get_work_day($dateList['last_end_date']);
        $retList['this_end_date'] = get_work_day($dateList['this_end_date']);
        return $retList;
    }

    /**
     * 获取 旬 月 年同比日期
     * Created by zfy.
     * Date:2023/1/10 11:02
     * @param $dateList
     * @return array
     */
    protected function getXunMonthYearDate($dateList){
        $retList = array();
        $retList['thisXun'] = $dateList['this_end_date'];
        $retList['lastXun'] = $dateList['last_end_date'];
        $retList['lastMonth'] = date("Y-m-d",strtotime($dateList['this_end_date']."-1 month"));
        $retList['lastYear'] = date("Y-m-d",strtotime($dateList['this_end_date']."-1 year"));
        return $retList;
    }

    /**
     * 获取生产日报数据
     * 获取新钢内网提供的数据（生产情况数据、产成品库存数据、中间品库存数据、原燃料库存数据）
     * @param $type GetScqkData,GetCcpkcData,GetZjpkcData,GetYrlkcData
     * @param $ymd
     * @return array
     */
    public function getScrbData($type, $ymd) {
        // $targetUrl = urlencode("http://10.70.0.12:8098/api/GetScrbData/{$type}?pushProdData=".$ymd);
        // // 此处只能以新钢正式服务器作为中间代理请求内网地址
        // $url = "https://*************:14007/api/getJsonByUrl?url={$targetUrl}&method=post";
        // $content = file_get_contents($url, false, stream_context_create(array("ssl" => array("verify_peer"=>false,"verify_peer_name"=>false))));
        // // print_r(iconv("utf-8","gbk",$content));
        // $data = array();
        // if(!empty($content)) {
        //     $content = json_decode($content, true);
        //     $content = json_decode($content, true);
        //     $olddata = array();
        //     if(!empty($content['pushDate']))
        //     $olddata = $this->array_iconv($content['pushDate']);
        //     foreach($olddata as $olddata_val) {
        //         $data[$olddata_val['PM']] = $olddata_val;
        //     }
        // }
        // return $data;
        $data = array();
        $symd = date("Y-m-01", strtotime($ymd));
        switch ($type) {
            case 'GetScqkData':
                # 生产情况数据
                $sql = "select d.pm PM,d.yjhcl YJHCL,d.rjhcl RJHCL,d.rwccl RWCCL,d.yljcl YLJCL,d.nljcl NLJCL,d.yljjhb YLJJHB from prodDataForDailyBase b left join prodDataForDailyDetail d on b.id=d.baseid where b.data_date = '{$ymd}' and b.isdel=0 and b.data_type=1";
                $this->xg->execute("set names utf8");
                $tdata = ($this->xg->query($sql));
                foreach($tdata as $key => $val) {
                    $data[$val['PM']] = $val;
                }
                break;
            case 'GetCcpkcData':
                # 产成品库存数据
                $sql = "select b.data_date,d.pm PM,d.kcsl DRKC,d.dekc DEKC,d.kcpc PCZ from inventoryDataBase b left join inventoryDataDetail d on b.id=d.baseid where b.data_date <= '{$ymd}' and b.data_date >= '{$symd}' and b.isdel=0 and b.data_type=2";
                $this->xg->execute("set names utf8");
                $tdata = ($this->xg->query($sql));
                $otherDateData = array();
                $dates = array();
                foreach($tdata as $val) {
                    if($val['data_date']==$ymd)
                    $data[$val['PM']] = $val;
                    $data[$val['PM']]['YJZ'] = "";
                    unset($data[$val['PM']]['data_date']);
                    $dates[] = $val['data_date'];
                    $otherDateData[$val['PM']] += (float)$val['DRKC'];
                }
                $ts = count(array_unique($dates));
                if($ts!=0){
                    foreach($data as $pmkey => $pmval) {
                        $data[$pmkey]["YJZ"] = round($otherDateData[$pmkey] / $ts, 0);
                    }
                }
                break;
            case 'GetZjpkcData':
                # 中间品库存数据
                $sql = "select b.data_date,d.pm PM,d.kcsl DRKC,d.dekc DEKC,d.kcpc PCZ from inventoryDataBase b left join inventoryDataDetail d on b.id=d.baseid where b.data_date <= '{$ymd}' and b.data_date >= '{$symd}' and b.isdel=0 and b.data_type=3";
                $this->xg->execute("set names utf8");
                $tdata = ($this->xg->query($sql));
                $otherDateData = array();
                $dates = array();
                foreach($tdata as $val) {
                    if($val['data_date']==$ymd)
                    $data[$val['PM']] = $val;
                    $data[$val['PM']]['YJZ'] = "";
                    unset($data[$val['PM']]['data_date']);
                    $dates[] = $val['data_date'];
                    $otherDateData[$val['PM']] += (float)$val['DRKC'];
                }
                $ts = count(array_unique($dates));
                if($ts!=0){
                    foreach($data as $pmkey => $pmval) {
                        $data[$pmkey]["YJZ"] = round($otherDateData[$pmkey] / $ts, 0);
                    }
                }
                break;
            case 'GetYrlkcData':
                # 原燃料库存数据
                $sql = "select b.data_date,d.pm PM,d.kcsl DQKC,d.dekc DEKC,d.kcpc PCZ,d.dezzts DEZZTS,d.sjzzts SJZZTS from inventoryDataBase b left join inventoryDataDetail d on b.id=d.baseid where b.data_date <= '{$ymd}' and b.data_date >= '{$symd}' and b.isdel=0 and b.data_type=1";
                $this->xg->execute("set names utf8");
                $tdata = ($this->xg->query($sql));
                $otherDateData = array();
                $dates = array();
                foreach($tdata as $val) {
                    if($val['data_date']==$ymd)
                    $data[$val['PM']] = $val;
                    $data[$val['PM']]['YJZ'] = "";
                    unset($data[$val['PM']]['data_date']);
                    $dates[] = $val['data_date'];
                    $otherDateData[$val['PM']] += (float)$val['DQKC'];
                }
                $ts = count(array_unique($dates));
                if($ts!=0){
                    foreach($data as $pmkey => $pmval) {
                        $data[$pmkey]["YJZ"] = round($otherDateData[$pmkey] / $ts, 0);
                    }
                }
                break;
            default:
                break;
        }
        return $data;
    }



    /**
     * 编码转换（可以是多维数组）
     * <AUTHOR>
     * @param $data 待转码变量
     * @param $in_charset 原编码, gbk
     * @param $out_charset 目标编码, utf-8
     * @return array 转码后的数据
     */
    function iconvArrayA($data, $in_charset = 'utf-8', $out_charset = 'gbk')
    {
        if (is_array($data))
        {
            foreach ($data as $key => $val)
            {
                $key = iconv($in_charset, $out_charset, $key);
                $dataA[$key] = $this->iconvArrayA($val, $in_charset, $out_charset);
            }
            return $dataA;
        }
        else
        {
            return iconv($in_charset, $out_charset, $data);
        }
    }

    /**
     * POST 请求
     * @param string $url
     * @param array $param
     * @param boolean $post_file 是否文件上传
     * @return string content
     */
    function http_post($url, $param, $post_file = false)
    {
        $oCurl = curl_init();

        if (stripos($url, "https://") !== FALSE) {
            curl_setopt($oCurl, CURLOPT_SSL_VERIFYPEER, FALSE);
            curl_setopt($oCurl, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($oCurl, CURLOPT_SSLVERSION, 1); //CURL_SSLVERSION_TLSv1
        }
        if (PHP_VERSION_ID >= 50500 && class_exists('\CURLFile')) {
            $is_curlFile = true;
        } else {
            $is_curlFile = false;
            if (defined('CURLOPT_SAFE_UPLOAD')) {
                curl_setopt($oCurl, CURLOPT_SAFE_UPLOAD, false);
            }
        }

        if ($post_file) {
            if ($is_curlFile) {
                foreach ($param as $key => $val) {
                    if (isset($val["tmp_name"])) {
                        $param[$key] = new \CURLFile(realpath($val["tmp_name"]), $val["type"], $val["name"]);
                    } else if (substr($val, 0, 1) == '@') {
                        $param[$key] = new \CURLFile(realpath(substr($val, 1)));
                    }
                }
            }
            $strPOST = $param;
        } else {
            //$strPOST = json_encode($param);
            $strPOST = $param;
        }

        curl_setopt($oCurl, CURLOPT_URL, $url);
        curl_setopt($oCurl, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($oCurl, CURLOPT_POST, true);
        curl_setopt($oCurl, CURLOPT_POSTFIELDS, $strPOST);
        curl_setopt($oCurl, CURLOPT_VERBOSE, 1);
        curl_setopt($oCurl, CURLOPT_HEADER, 1);

        // $sContent = curl_exec($oCurl);
        // $aStatus  = curl_getinfo($oCurl);

        $sContent = $this->execCURL($oCurl);

        curl_close($oCurl);

        return $sContent;
    }

    /**
     * 执行CURL请求，并封装返回对象
     */
    function execCURL($ch)
    {
        $response = curl_exec($ch);
        $error = curl_error($ch);
        $result = array('header' => '',
            'content' => '',
            'curl_error' => '',
            'http_code' => '',
            'last_url' => '');

        if ($error != "") {
            $result['curl_error'] = $error;
            return $result;
        }

        $header_size = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
        $result['header'] = str_replace(array("\r\n", "\r", "\n"), "<br/>", substr($response, 0, $header_size));
        $result['content'] = substr($response, $header_size);
        $result['http_code'] = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $result['last_url'] = curl_getinfo($ch, CURLINFO_EFFECTIVE_URL);
        $result["base_resp"] = array();
        $result["base_resp"]["ret"] = $result['http_code'] == 200 ? 0 : $result['http_code'];
        $result["base_resp"]["err_msg"] = $result['http_code'] == 200 ? "ok" : $result["curl_error"];

        return $result;
    }
	public function clearBom($str)
    {
        $bom = chr(239) . chr(187) . chr(191);
        return str_replace($bom, '', $str);
    }
} 
?>