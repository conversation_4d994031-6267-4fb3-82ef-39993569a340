<?php
error_reporting(E_ERROR);
$GLOBALS['ImageType']= array(
	"1"=>"柱状图",
	"2"=>"线图",
	"3"=>"柱线图"
);
$GLOBALS['UnitConv']= array(
	"0"=>"缺省",
	"1"=>"个",
	//"2"=>"十",
	//"3"=>"百",
	"4"=>"千",
	"5"=>"万",
	//"6"=>"十万",
	"7"=>"百万",
	"8"=>"千万",
	"9"=>"亿",
	"10"=>"十亿",
	"11"=>"百亿",
	//"12"=>"千亿",
	"13"=>"万亿"
);
$GLOBALS['Dtymd']= array(
	"1"=>"年",
	"2"=>"季",
	"3"=>"月",
	"6"=>"日"
);
$GLOBALS['status']= array(
	"0"=>"未启用",
	"1"=>"启用"
);

class customerdataAction extends AbstractAction{
	public function __construct()
	{
		parent::__construct();    
	}
  
    public function utf8ToGb2312($str)
    {
        if( mb_detect_encoding($str,"UTF-8, ISO-8859-1, GBK")=="UTF-8" ) {//判断是否不是UTF-8编码，如果不是UTF-8编码，则转换为UTF-8编码
            return iconv("utf-8", "gb2312", $str);
        } else {
            return $str;
        }
    }

    public function gb2312Toutf8($str)
    {
        if( mb_detect_encoding($str,"UTF-8, ISO-8859-1, GBK")!="UTF-8" ) {//判断是否不是UTF-8编码，如果不是UTF-8编码，则转换为UTF-8编码
            return iconv("gb2312", "utf-8", $str);
        } else {
            return $str;
        }
    }

	public function index($params)
	{
		$SignCS = $params['SignCS'];
		$GUID =  $params['GUID'];
		$mc_type = $params['mc_type'] == "" ? "0" : $params['mc_type'];

		if($GUID == "")
		{
			echo '<div align="center" style="font-size:18px">您没有权限查看此页面！</div>';
			exit;
		}

		$uid = $this->t1Dao->get_uid($GUID,$SignCS,$mc_type);
		
		$this->assign("SignCS",$SignCS);
		$this->assign("GUID",$GUID);
		$this->assign("mc_type",$mc_type);
		$this->assign("uid",$uid);
	}

	public function getcal($params)
	{
		//$params['uid']='391935';
		$caldata = $this->t1Dao->get_catalogue($params['uid'],$params['mc_type']);
		//echo '<pre/>';print_r($caldata);exit;
		if(!empty($caldata))
		{
			foreach($caldata as $data)
			{
				$calarray[$data['parentid']][] = $data;
			}
			
			if($da['PowerID'] != "")
			{
				$usr = $this->_dao->get_uname($da['PowerID']);
				$da['PowerID'] = trim(implode(",",$usr));
			}
		
			//一层目录
			foreach($calarray['0'] as &$l1arr)
			{
				$l1arr['cataloguename'] = $this->gb2312Toutf8(base64_decode($l1arr['cataloguename']));
				$l1arr['l1cataloguename'] = $l1arr['cataloguename'];
				$l1arr['l2cataloguename'] = '';
				$l1arr['l3cataloguename'] = '';
				$l1arr['status'] = $GLOBALS['status'][$l1arr['status']];
				$l1arr['createtime'] = date("Y-m-d",strtotime( $l1arr['createtime']));
				if($l1arr['hasleaves'] == "1")
					$l1arr['hasleaves'] = "有";
				else
					$l1arr['hasleaves'] = "无";
				
				if($l1arr['PowerID'] != "")
				{
					$usr = $this->_dao->get_uname($l1arr['PowerID']);
					$l1arr['PowerID'] = trim(implode(",",$usr));
				}

				if($l1arr['isimport'] == "1")
					$l1arr['isimport'] = "是";
				else
					$l1arr['isimport'] = "否";
				$l1arr['importfile'] = $l1arr['importfile'];
				$l1arr['importid'] = $l1arr['importid'];

				$caldatas[] = $l1arr;

				if($l1arr['hasnode'] == "1")
				{
					//二层目录
					foreach($calarray[$l1arr['id']] as $l2arr)
					{
						$l2arr['cataloguename'] = $this->gb2312Toutf8(base64_decode($l2arr['cataloguename']));
						$l2arr['l1cataloguename'] = '';
						$l2arr['l2cataloguename'] = $l2arr['cataloguename'];
						$l2arr['l3cataloguename'] = '';
						$l2arr['status'] = $GLOBALS['status'][$l2arr['status']];
						$l2arr['createtime'] = date("Y-m-d",strtotime( $l2arr['createtime']));
						if($l2arr['hasleaves'] == "1")
							$l2arr['hasleaves'] = "有";
						else
							$l2arr['hasleaves'] = "无";
						
						if($l2arr['PowerID'] != "")
						{
							$usr = $this->_dao->get_uname($l2arr['PowerID']);
							$l2arr['PowerID'] = trim(implode(",",$usr));
						}

						if($l2arr['isimport'] == "1")
							$l2arr['isimport'] = "是";
						else
							$l2arr['isimport'] = "否";
						$l2arr['importfile'] = $l2arr['importfile'];
						$l2arr['importid'] = $l2arr['importid'];

						$caldatas[] = $l2arr;

						if($l2arr['hasnode'] == "1")
						{
							//三层目录
							foreach($calarray[$l2arr['id']] as $l3arr)
							{
								$l3arr['cataloguename'] = $this->gb2312Toutf8(base64_decode($l3arr['cataloguename']));
								$l3arr['l1cataloguename'] = '';
								$l3arr['l2cataloguename'] = '';
								$l3arr['l3cataloguename'] = $l3arr['cataloguename'];
								$l3arr['status'] = $GLOBALS['status'][$l3arr['status']];
								$l3arr['createtime'] = date("Y-m-d",strtotime( $l3arr['createtime']));
								if($l3arr['hasleaves'] == "1")
									$l3arr['hasleaves'] = "有";
								else
									$l3arr['hasleaves'] = "无";
								
								if($l3arr['PowerID'] != "")
								{
									$usr = $this->_dao->get_uname($l3arr['PowerID']);
									$l3arr['PowerID'] = trim(implode(",",$usr));
								}								
								if($l3arr['isimport'] == "1")
									$l3arr['isimport'] = "是";
								else
									$l3arr['isimport'] = "否";
								$l3arr['importfile'] = $l3arr['importfile'];
								$l3arr['importid'] = $l3arr['importid'];

								$caldatas[] = $l3arr;
							}
						}
					}
				}				
			}
			
			$page = $params['page'] == "" ? "1" : $params['page'];

		}
		//echo '<pre/>';print_r($caldatas);exit;		
		$arr['code'] = "0";
		if(!empty($caldatas))
			$arr['count'] =  count($caldatas);
		else
			$arr['count'] =  0;
		$arr['data'] =  $caldatas;

		$json_string = $this->pri_JSON($arr);
		echo $json_string;
	}

	public function delcal($params)
	{
		//echo '<pre/>';print_r($params);exit;
		$id = $params['id'];
		if($params['isCatalogue'] == "1")
		{
			$has = $this->t1Dao->getRow("select hasnode,hasleaves,isimport,importid from dc_customer_data_catalogue where id='$id'");
			
			if(($has['hasnode'] == "1" || $has['hasleaves'] == "1") && $has['isimport'] != "1")
			{
				$res['code'] = 0;
			}
			else
			{
				$this->t1Dao->execute("update dc_customer_data_catalogue set isdel=1,updatetime='".date("Y-m-d H:i:s")."' where id='$id'");
				
				$parid = $this->t1Dao->get_parname($id);
				$node =  $this->t1Dao->getcal_parentid($parid['parentid'],"1");
				if(count($node) < 1)
					$this->t1Dao->execute("update dc_customer_data_catalogue set hasnode='0',updatetime='".date("Y-m-d H:i:s")."' where id='".$parid['parentid']."'");

				if($has['isimport'] == "1")
				{
					$soncal = explode(",",$has['importid']);
					foreach($soncal as $cal)
					{
						$this->t1Dao->execute("update dc_customer_data_catalogue set isdel=1,updatetime='".date("Y-m-d H:i:s")."' where id='$cal'");
					}
				}

				$res['code'] = 1;
			}
		}
		else
		{
			if($params['idarray'] != "")
			{
				$this->t1Dao->execute("update dc_customer_data_catalogue set isdel=1,updatetime='".date("Y-m-d H:i:s")."' where id in (".$params['idarray'].")");

				$parid = $this->t1Dao->getOne("SELECT parentid,nodelevel from dc_customer_data_catalogue where id in (".$params['idarray'].")");
				$node =  $this->t1Dao->getcal_parentid($parid,"0");
				if(count($node) < 1)
					$this->t1Dao->execute("update dc_customer_data_catalogue set hasleaves='0',updatetime='".date("Y-m-d H:i:s")."' where id='".$parid."'");				
			}
			else
			{
				$this->t1Dao->execute("update dc_customer_data_catalogue set isdel=1,updatetime='".date("Y-m-d H:i:s")."' where id='$id'");

				$parid = $this->t1Dao->get_parname($id);
				$node =  $this->t1Dao->getcal_parentid($parid['parentid'],"0");
				if(count($node) < 1)
					$this->t1Dao->execute("update dc_customer_data_catalogue set hasleaves='0',updatetime='".date("Y-m-d H:i:s")."' where id='".$parid['parentid']."'");
			}

			$res['code'] = 1;
		}
		
		echo json_encode($res);
	}
	
	public function catalogue($params)
	{
		//$params['uid'] = 391935;
		if($params['id'] != "")
		{
			$editdata = $this->t1Dao->getRow("select cataloguename,aod,status,PowerID,parentid,MID from dc_customer_data_catalogue where id='".$params['id']."'");
			
	  		//目录层级
			if($editdata['parentid'] != "0")
			{
				$selectedid='';
				$parname2 = $this->t1Dao->get_parname($editdata['parentid']);
				
				if($parname2['parentid'] != "0")
				{
					$parname1 = $this->t1Dao->get_parname($parname2['parentid']);

					$select .='<div class="layui-input-inline"><select name="parentid'.$parname1['nodelevel'].'" lay-filter="parentid'.$parname1['nodelevel'].'"><option value="">请选择目录</option>';
					$childdata1 =  $this->t1Dao->getcal_parentid($parname1['parentid'],"1",$params['uid'],$params['mc_type']);
					
					foreach($childdata1 as $child1)
					{
						$selected='';
						if($child1['id'] == $parname1['id'])
						{
							$selected = ' selected=""';
						}

						$select .='<option value="'.$child1['id'].'"'.$selected.'>'.$this->gb2312Toutf8(base64_decode($child1['cataloguename'])).'</option>';
					}
					$select .='</select></div>';
				}

				$select .='<div class="layui-input-inline"><select name="parentid'.$parname2['nodelevel'].'" id="parentid'.$parname2['nodelevel'].'" lay-filter="parentid'.$parname2['nodelevel'].'"><option value="">请选择目录</option>';
				$childdata2 =  $this->t1Dao->getcal_parentid($parname2['parentid'],"1",$params['uid'],$params['mc_type']);						
				foreach($childdata2 as $child2)
				{
					$selected='';
					if($child2['id'] == $parname2['id'])
					{
						$selected=' selected=""';
						$selectedid = $child2['id'];
					}

					$select .='<option value="'.$child2['id'].'"'.$selected.'>'.$this->gb2312Toutf8(base64_decode($child2['cataloguename'])).'</option>';
				}
				$select .='</select></div>';

				if($parname2['nodelevel'] < 2)
				{
					if($selectedid != "")
					{
						$childdata =  $this->t1Dao->getcal_parentid($selectedid,"1",$params['uid'],$params['mc_type']);
						
						$select .='<div class="layui-input-inline"><select name="parentid'.($parname2['nodelevel']+1).'" id="parentid'.($parname2['nodelevel']+1).'" lay-filter="parentid'.($parname2['nodelevel']+1).'"><option value="">请选择目录</option>';					
						foreach($childdata as $child)
						{
							$select .='<option value="'.$child['id'].'">'.$this->gb2312Toutf8(base64_decode($child['cataloguename'])).'</option>';
						}
						$select .='</select></div>';
					}
					else
					{
						for($i=$parname2['nodelevel']+1;$i<=2;$i++)
						{
							$select .='<div class="layui-input-inline"><select name="parentid'.$i.'" id="parentid'.$i.'" lay-filter="parentid'.$i.'"><option value="">请选择目录</option></select></div>';
						}
					}
				}
			}

			//共享使用
			$users = $this->t1Dao->getuser_mid($editdata['MID'],$params['mc_type'],$params['uid']);
			foreach($users as $user)
			{
				$checked = '';
				if($editdata['PowerID'] != "" && strpos($editdata['PowerID'],','.$user['Uid'].',') !== false)
					$checked = ' checked=""';
				$checkbox .= '<input type="checkbox" name="powerid['.$user['Uid'].']" lay-skin="primary" title="'.$user['TrueName'].'"'.$checked.'>';
				
			}	
			
			$this->assign("id",$params['id']);
			$this->assign("cataloguename",$this->gb2312Toutf8(base64_decode($editdata['cataloguename'])));
			$this->assign("aod",$editdata['aod']);
			$this->assign("status",$editdata['status']);
			$isedit=1;
		}
		else
		{
			$mid = $this->t1Dao->getOne("select Mid from app_session_temp where Uid='".$params['uid']."' order by LoginDate desc limit 1");
			$users = $this->t1Dao->getuser_mid($mid,$params['mc_type'],$params['uid']);
			foreach($users as $user)
			{
				$checkbox .= '<input type="checkbox" name="powerid['.$user['Uid'].']" lay-skin="primary" title="'.$user['TrueName'].'">';				
			}
			$isedit=0;
			$this->assign("mid",$mid);
		}

		if($select == "")
		{		
			$select ='<div class="layui-input-inline"><select name="parentid1" lay-filter="parentid1"><option value="">请选择目录</option>';
			$childdata =  $this->t1Dao->getcal_parentid('0',"1",$params['uid'],$params['mc_type']);
			foreach($childdata as $child)
			{
				$select .='<option value="'.$child['id'].'"'.'>'.$this->gb2312Toutf8(base64_decode($child['cataloguename'])).'</option>';
			}
			$select .='</select></div>';

			$select .='<div class="layui-input-inline"><select name="parentid2" id="parentid2" lay-filter="parentid2"><option value="">请选择目录</option></select></div>';
			
		}

		$this->assign("select",$select);
		$this->assign("checkbox",$checkbox);
		$this->assign("isedit",$isedit);
		$this->assign("mc_type",$params['mc_type']);
		$this->assign("uid",$params['uid']);		
	}

	public function docatalogue($params)
	{
		//echo '<pre/>';print_r($params);exit;

		if($params['parentid2'] != "")
		{
			$nodelevel = 3;
			$parentid = $params['parentid2'];
		}
		elseif($params['parentid1'] != "")
		{
			$nodelevel = 2;
			$parentid = $params['parentid1'];
		}
		else
		{
			$nodelevel = 1;
			$parentid = 0;
		}

		if(!empty($params['powerid']))
		{
			foreach($params['powerid'] as $k=>$v)
			{
				$powerid .= $k.",";
			}
			$powerid = ",".$powerid;
		}

		if($params['isedit'] == "1")
		{
			$parid = $this->t1Dao->get_parname($params['id']);
							
			if(($parentid == $params['id']) || ($nodelevel > 2 && $parid['hasnode'] == "1"))
			{
				echo '<script src="../js/layui.js"></script><script>			
					var layer = layui.layer;
					layer.alert("不能移动到该目录下！",function(index){
						window.history.back();
					});
					</script>';
			}
			else
			{
				$flag = true;
				if($parid['parentid'] == "0" && $parentid != 0 && $parid['hasnode'] == "1")
				{
					$data = $this->t1Dao->query("SELECT hasnode from dc_customer_data_catalogue where parentid='".$arid['id']."' and isCatalogue=1 and Uid='".$params['uid']."'");
					//echo '<pre/>';print_r($data);exit;
					foreach($data as $da)
					{
						if($da['hasnode'] == "1")
						{
							$flag = false;
							break;
						}
					}
				}
				
				if($flag)
				{
					$this->t1Dao->execute("update dc_customer_data_catalogue set cataloguename='".base64_encode($this->utf8ToGb2312(htmlspecialchars_decode($params['cataloguename'])))."',parentid='".$parentid."',nodelevel='".$nodelevel."',aod='".$params['aod']."',status='".$params['status']."',PowerID='".$powerid."',updatetime='".date("Y-m-d H:i:s")."' where id='".$params['id']."'");

					$this->t1Dao->execute("update dc_customer_data_catalogue set hasnode='1' where id='".$parentid."'");

					if($parid['parentid'] != $parentid)
					{
						$node =  $this->t1Dao->getcal_parentid($parid['parentid'],"1","",$params['mc_type']);
						if(count($node) < 1)
							$this->t1Dao->execute("update dc_customer_data_catalogue set hasnode='0',updatetime='".date("Y-m-d H:i:s")."' where id='".$parid['parentid']."'");
					}
				
					echo '<script src="../js/layui.js"></script><script>			
						var layer = layui.layer;
						layer.alert("修改成功！",function(index){
							window.parent.location.reload();
							var index=parent.layer.getFrameIndex(window.name); //获取当前窗口的name
							parent.layer.close(index);
						});
						</script>';
				}
				else
				{
					echo '<script src="../js/layui.js"></script><script>			
					var layer = layui.layer;
					layer.alert("不能移动到该目录下！",function(index){
						window.history.back();
					});
					</script>';
				}
			}
		}
		else
		{
			$this->t1Dao->execute("insert into dc_customer_data_catalogue set cataloguename='".base64_encode($this->utf8ToGb2312(htmlspecialchars_decode($params['cataloguename'])))."',parentid='".$parentid."',aod='".$params['aod']."',status='".$params['status']."',PowerID='".$powerid."',createtime='".date("Y-m-d H:i:s")."',Uid='".$params['uid']."',MID='".$params['mid']."',isCatalogue='1',mc_type='".$params['mc_type']."',nodelevel='".$nodelevel."'");
			
			if($parentid != "0")
				$this->t1Dao->execute("update dc_customer_data_catalogue set hasnode='1',updatetime='".date("Y-m-d H:i:s")."' where id='".$parentid."'");

			echo '<script src="../js/layui.js"></script><script>			
				var layer = layui.layer;
				layer.alert("添加成功！",function(index){
					window.parent.location.reload();
					var index=parent.layer.getFrameIndex(window.name); //获取当前窗口的name
					parent.layer.close(index);
				});
				</script>';
		}
		
	}
	
	//数据页面
	public function detail($params)
	{
		$SignCS = $params['SignCS'];
		$GUID =  $params['GUID'];
		$mc_type = $params['mc_type'] == "" ? "0" : $params['mc_type'];

		if($GUID == "")
		{
			echo '<div align="center" style="font-size:18px">您没有权限查看此页面！</div>';
			exit;
		}

		$uid = $this->t1Dao->get_uid($GUID,$SignCS,$mc_type);		
		//$uid = "391935";

		$this->assign("SignCS",$SignCS);
		$this->assign("GUID",$GUID);		
		$this->assign("mc_type",$mc_type);
		$this->assign("uid",$uid);
		$this->assign("id",$params['id']);

		//echo '<pre/>';print_r($htmlstr);exit;
	}

	public function getdata($params)
	{
		//echo '<pre/>';print_r($params);exit;
		$parentid = $params['id'] == "" ? "0" : $params['id'];
		$mc_type = $params['mc_type'] == "" ? "0" : $params['mc_type'];
		$data = $this->t1Dao->getdata_parentid($parentid,$params['uid'],$mc_type);
		
		foreach($data as &$da)
		{
			$da['D1ImageType'] = $GLOBALS['ImageType'][$da['D1ImageType']];

			if($da['D1UnitType'] == "0")
				$da['D1UnitConv'] = "";
			else
				$da['D1UnitConv'] = $GLOBALS['UnitConv'][$da['D1UnitConv']];

			$da['status'] = $GLOBALS['status'][$da['status']];
			$da['createtime'] = date("Y-m-d",strtotime( $da['createtime']));

			if($da['PowerID'] != "")
			{
				$usr = $this->_dao->get_uname($da['PowerID']);
				$da['PowerID'] = trim(implode(",",$usr));
			}
			
			$da['D1dtymd'] = $GLOBALS['Dtymd'][$da['D1dtymd']];
			$da['cataloguename'] = $this->gb2312Toutf8(base64_decode($da['cataloguename']));
		}

		$arr['code'] = "0";
		$arr['count'] =  count($data);
		$arr['data'] =  $data;

		$json_string = $this->pri_JSON($arr);
		echo $json_string;

		/*echo '{"code":0,"count":1000,"data":[{"aod":10000,"cataloguename":"user-0","status":"女","D1ImageType":"城市-0","D1UnitConv":"签名-0","createtime":82830700,"PowerID":"作家","D1UnitName":57},{"aod":10001,"cataloguename":"user-1","status":"男","D1ImageType":"城市-1","D1UnitConv":"签名-1","createtime":64928690,"PowerID":"词人","D1UnitName":27},{"aod":10019,"cataloguename":"user-19","status":"女","D1ImageType":"城市-19","D1UnitConv":"签名-19","createtime":68647362,"PowerID":"词人","D1UnitName":51}]}';*/
	}

	public function data($params)
	{
		//$params['id']="27";
		//$params['uid'] = 391935;
		if($params['id'] != "")
		{
			$editdata = $this->t1Dao->getRow("select cataloguename,aod,status,PowerID,parentid,MID,D1ImageType,D1UnitType,D1UnitName,D1UnitConv,D1dtymd from dc_customer_data_catalogue where id='".$params['id']."'");
				
	  		//目录层级
			if($editdata['parentid'] != "0")
			{
				$parname3 = $this->t1Dao->get_parname($editdata['parentid']);
				
				if($parname3['parentid'] != "0")
				{
					$parname2 = $this->t1Dao->get_parname($parname3['parentid']);
					
					if($parname2['parentid'] != "0")
					{
						$parname1 = $this->t1Dao->get_parname($parname2['parentid']);

						$select ='<div class="layui-input-inline"><select name="parentid'.$parname1['nodelevel'].'" lay-filter="parentid'.$parname1['nodelevel'].'"><option value="">请选择目录</option>';

						$childdata1 =  $this->t1Dao->getcal_parentid($parname1['parentid'],"1",$params['uid'],$params['mc_type']);
							
						foreach($childdata1 as $child1)
						{
							$selected='';
							if($child1['id'] == $parname1['id'])
								$selected=' selected=""';

							$select .='<option value="'.$child1['id'].'"'.$selected.'>'.$this->gb2312Toutf8(base64_decode($child1['cataloguename'])).'</option>';
						}
						$select .='</select></div>';
					}
						
					$select .='<div class="layui-input-inline"><select name="parentid'.$parname2['nodelevel'].'" id="parentid'.$parname2['nodelevel'].'" lay-filter="parentid'.$parname2['nodelevel'].'"><option value="">请选择目录</option>';
					$childdata2 =  $this->t1Dao->getcal_parentid($parname2['parentid'],"1",$params['uid'],$params['mc_type']);						
					foreach($childdata2 as $child2)
					{
						$selected='';
						if($child2['id'] == $parname2['id'])
							$selected=' selected=""';

						$select .='<option value="'.$child2['id'].'"'.$selected.'>'.$this->gb2312Toutf8(base64_decode($child2['cataloguename'])).'</option>';
					}
					$select .='</select></div>';
				}

				$select .='<div class="layui-input-inline"><select name="parentid'.$parname3['nodelevel'].'" id="parentid'.$parname3['nodelevel'].'" lay-filter="parentid'.$parname3['nodelevel'].'"><option value="">请选择目录</option>';
				$childdata3 =  $this->t1Dao->getcal_parentid($parname3['parentid'],"1",$params['uid'],$params['mc_type']);
				
				$selectedid = "";
				foreach($childdata3 as $child3)
				{
					$selected='';
					if($child3['id'] == $parname3['id'])
					{
						$selected=' selected=""';
						$selectedid = $child3['id'];
					}

					$select .='<option value="'.$child3['id'].'"'.$selected.'>'.$this->gb2312Toutf8(base64_decode($child3['cataloguename'])).'</option>';
				}
				$select .='</select></div>';
					

				if($parname3['nodelevel'] < 3)
				{
					if($selectedid != "")
					{
						$childdata =  $this->t1Dao->getcal_parentid($selectedid,"1",$params['uid'],$params['mc_type']);
						
						$select .='<div class="layui-input-inline"><select name="parentid'.($parname3['nodelevel']+1).'" id="parentid'.($parname3['nodelevel']+1).'" lay-filter="parentid'.($parname3['nodelevel']+1).'"><option value="">请选择目录</option>';					
						foreach($childdata as $child)
						{
							$select .='<option value="'.$child['id'].'">'.$this->gb2312Toutf8(base64_decode($child['cataloguename'])).'</option>';
						}
						$select .='</select></div>';

						for($i=$parname3['nodelevel']+2;$i<=3;$i++)
						{
							$select .='<div class="layui-input-inline"><select name="parentid'.$i.'" id="parentid'.$i.'" lay-filter="parentid'.$i.'"><option value="">请选择目录</option></select></div>';
						}
					}
					else
					{
						for($i=$parname3['nodelevel']+1;$i<=3;$i++)
						{
							$select .='<div class="layui-input-inline"><select name="parentid'.$i.'" id="parentid'.$i.'" lay-filter="parentid'.$i.'"><option value="">请选择目录</option></select></div>';
						}
					}
				}
			}
			else
			{
				$select ='<div class="layui-input-inline"><select name="parentid1" lay-filter="parentid1"><option value="">请选择目录</option>';
				$childdata =  $this->t1Dao->getcal_parentid('0',"1",$params['uid'],$params['mc_type']);
				foreach($childdata as $child)
				{
					$select .='<option value="'.$child['id'].'"'.'>'.$this->gb2312Toutf8(base64_decode($child['cataloguename'])).'</option>';
				}
				$select .='</select></div>';

				$select .='<div class="layui-input-inline"><select name="parentid2" id="parentid2" lay-filter="parentid2"><option value="">请选择目录</option></select></div>';
				$select .='<div class="layui-input-inline"><select name="parentid3" id="parentid3"><option value="">请选择目录</option></select></div>';
			}

			//共享使用
			$users = $this->t1Dao->getuser_mid($editdata['MID'],$params['mc_type'],$params['uid']);
			foreach($users as $user)
			{
				$checked = '';
				if($editdata['PowerID'] != "" && strpos($editdata['PowerID'],','.$user['Uid'].',') !== false)
					$checked = ' checked=""';
				$checkbox .= '<input type="checkbox" name="powerid['.$user['Uid'].']" lay-skin="primary" title="'.$user['TrueName'].'"'.$checked.'>';
				
			}

			if($editdata['D1UnitType'] == "" || $editdata['D1UnitType'] == "0")
				$display = "none";
			else
				$display = "inline";
			
			$this->assign("id",$params['id']);
			$this->assign("cataloguename",$this->gb2312Toutf8(base64_decode($editdata['cataloguename'])));
			$this->assign("D1ImageType",$editdata['D1ImageType']);
			$this->assign("D1UnitType",$editdata['D1UnitType']);
			$this->assign("D1UnitName",$editdata['D1UnitName']);
			$this->assign("D1UnitConv",$editdata['D1UnitConv']);
			$this->assign("D1dtymd",$editdata['D1dtymd']);
			$this->assign("aod",$editdata['aod']);
			$this->assign("status",$editdata['status']);
			$isedit=1;
		}
		else
		{
			if($params['pid'] != "0")
			{
				$parname3 = $this->t1Dao->get_parname($params['pid']);
				
				if($parname3['parentid'] != "0")
				{
					$parname2 = $this->t1Dao->get_parname($parname3['parentid']);
					
					if($parname2['parentid'] != "0")
					{
						$parname1 = $this->t1Dao->get_parname($parname2['parentid']);

						$select ='<div class="layui-input-inline"><select name="parentid'.$parname1['nodelevel'].'" lay-filter="parentid'.$parname1['nodelevel'].'"><option value="">请选择目录</option>';

						$childdata1 =  $this->t1Dao->getcal_parentid($parname1['parentid'],"1",$params['uid'],$params['mc_type']);
							
						foreach($childdata1 as $child1)
						{
							$selected='';
							if($child1['id'] == $parname1['id'])
								$selected=' selected=""';

							$select .='<option value="'.$child1['id'].'"'.$selected.'>'.$this->gb2312Toutf8(base64_decode($child1['cataloguename'])).'</option>';
						}
						$select .='</select></div>';
					}
						
					$select .='<div class="layui-input-inline"><select name="parentid'.$parname2['nodelevel'].'" id="parentid'.$parname2['nodelevel'].'" lay-filter="parentid'.$parname2['nodelevel'].'"><option value="">请选择目录</option>';
					$childdata2 =  $this->t1Dao->getcal_parentid($parname2['parentid'],"1",$params['uid'],$params['mc_type']);						
					foreach($childdata2 as $child2)
					{
						$selected='';
						if($child2['id'] == $parname2['id'])
							$selected=' selected=""';

						$select .='<option value="'.$child2['id'].'"'.$selected.'>'.$this->gb2312Toutf8(base64_decode($child2['cataloguename'])).'</option>';
					}
					$select .='</select></div>';
				}

				$select .='<div class="layui-input-inline"><select name="parentid'.$parname3['nodelevel'].'" id="parentid'.$parname3['nodelevel'].'" lay-filter="parentid'.$parname3['nodelevel'].'"><option value="">请选择目录</option>';
				$childdata3 =  $this->t1Dao->getcal_parentid($parname3['parentid'],"1",$params['uid'],$params['mc_type']);
				
				$selectedid = "";
				foreach($childdata3 as $child3)
				{
					$selected='';
					if($child3['id'] == $parname3['id'])
					{
						$selected=' selected=""';
						$selectedid = $child3['id'];
					}

					$select .='<option value="'.$child3['id'].'"'.$selected.'>'.$this->gb2312Toutf8(base64_decode($child3['cataloguename'])).'</option>';
				}
				$select .='</select></div>';
					

				if($parname3['nodelevel'] < 3)
				{
					if($selectedid != "")
					{
						$childdata =  $this->t1Dao->getcal_parentid($selectedid,"1",$params['uid'],$params['mc_type']);
						
						$select .='<div class="layui-input-inline"><select name="parentid'.($parname3['nodelevel']+1).'" id="parentid'.($parname3['nodelevel']+1).'" lay-filter="parentid'.($parname3['nodelevel']+1).'"><option value="">请选择目录</option>';					
						foreach($childdata as $child)
						{
							$select .='<option value="'.$child['id'].'">'.$this->gb2312Toutf8(base64_decode($child['cataloguename'])).'</option>';
						}
						$select .='</select></div>';

						for($i=$parname3['nodelevel']+2;$i<=3;$i++)
						{
							$select .='<div class="layui-input-inline"><select name="parentid'.$i.'" id="parentid'.$i.'" lay-filter="parentid'.$i.'"><option value="">请选择目录</option></select></div>';
						}
					}
					else
					{
						for($i=$parname3['nodelevel']+1;$i<=3;$i++)
						{
							$select .='<div class="layui-input-inline"><select name="parentid'.$i.'" id="parentid'.$i.'" lay-filter="parentid'.$i.'"><option value="">请选择目录</option></select></div>';
						}
					}
				}
			}
			else
			{
				$select ='<div class="layui-input-inline"><select name="parentid1" lay-filter="parentid1"><option value="">请选择目录</option>';
				$childdata =  $this->t1Dao->getcal_parentid('0',"1",$params['uid'],$params['mc_type']);
				foreach($childdata as $child)
				{
					$select .='<option value="'.$child['id'].'"'.'>'.$this->gb2312Toutf8(base64_decode($child['cataloguename'])).'</option>';
				}
				$select .='</select></div>';

				$select .='<div class="layui-input-inline"><select name="parentid2" id="parentid2" lay-filter="parentid2"><option value="">请选择目录</option></select></div>';
				$select .='<div class="layui-input-inline"><select name="parentid3" id="parentid3"><option value="">请选择目录</option></select></div>';
			}

			$mid = $this->t1Dao->getOne("select Mid from app_session_temp where Uid='".$params['uid']."' order by LoginDate desc limit 1");
			$users = $this->t1Dao->getuser_mid($mid,$params['mc_type'],$params['uid']);
			foreach($users as $user)
			{
				$checkbox .= '<input type="checkbox" name="powerid['.$user['Uid'].']" lay-skin="primary" title="'.$user['TrueName'].'">';
				
			}
			$isedit=0;
			$display = "none";
			$this->assign("mid",$mid);
		}

		$this->assign("select",$select);
		$this->assign("checkbox",$checkbox);
		$this->assign("isedit",$isedit);
		$this->assign("mc_type",$params['mc_type']);
		$this->assign("uid",$params['uid']);
		$this->assign("display",$display);
	}

	public function dodata($params)
	{
		//echo '<pre/>';print_r($params);
		if($params['parentid3'] != "")
		{
			$parentid = $params['parentid3'];
		}
		elseif($params['parentid2'] != "")
		{
			$parentid = $params['parentid2'];
		}
		elseif($params['parentid1'] != "")
		{
			$parentid = $params['parentid1'];
		}
		else
		{
			$parentid = 0;
		}

		if(!empty($params['powerid']))
		{
			foreach($params['powerid'] as $k=>$v)
			{
				$powerid .= $k.",";
			}
			$powerid = ",".$powerid;
		}
		
		if($params['D1UnitType'] == "0")
			$params['D1UnitConv'] = "";

		if($params['isedit'] == "1")
		{
			$parid = $this->t1Dao->get_parname($params['id']);
			
			if($parid['parentid'] != $parentid)
			{
				$node =  $this->t1Dao->getcal_parentid($parid['parentid'],"1","",$params['mc_type']);
				if(count($node) <= 1)
					$this->t1Dao->execute("update dc_customer_data_catalogue set hasleaves='0',updatetime='".date("Y-m-d H:i:s")."' where id='".$parid['parentid']."'");
			}
			
			if($parid['cataloguename'] != $params['cataloguename'])
			{
				$this->t1Dao->execute("update dc_customer_data set D1name='".base64_encode($this->utf8ToGb2312(htmlspecialchars_decode($params['cataloguename'])))."',updatetime='".date("Y-m-d H:i:s")."' where parentid='".$params['id']."'");
			}
			
			$this->t1Dao->execute("update dc_customer_data_catalogue set cataloguename='".base64_encode($this->utf8ToGb2312(htmlspecialchars_decode($params['cataloguename'])))."',parentid='".$parentid."',aod='".$params['aod']."',status='".$params['status']."',PowerID='".$powerid."',D1ImageType='".$params['D1ImageType']."',D1UnitType='".$params['D1UnitType']."',D1UnitName='".$params['D1UnitName']."',D1UnitConv='".$params['D1UnitConv']."',D1dtymd='".$params['D1dtymd']."',updatetime='".date("Y-m-d H:i:s")."' where id='".$params['id']."'");
			$this->t1Dao->execute("update dc_customer_data_catalogue set hasleaves='1',updatetime='".date("Y-m-d H:i:s")."' where id='".$parentid."'");

			echo '<script src="../js/layui.js"></script><script>			
				var layer = layui.layer;
				layer.alert("修改成功！",function(index){
					window.parent.location.reload();
					var index=parent.layer.getFrameIndex(window.name); //获取当前窗口的name
					parent.layer.close(index);
				});
				</script>';
		}
		else
		{
			$this->t1Dao->execute("insert into dc_customer_data_catalogue set cataloguename='".base64_encode($this->utf8ToGb2312(htmlspecialchars_decode($params['cataloguename'])))."',parentid='".$parentid."',aod='".$params['aod']."',status='".$params['status']."',PowerID='".$powerid."',D1ImageType='".$params['D1ImageType']."',D1UnitType='".$params['D1UnitType']."',D1UnitName='".$params['D1UnitName']."',D1UnitConv='".$params['D1UnitConv']."',D1dtymd='".$params['D1dtymd']."',createtime='".date("Y-m-d H:i:s")."',Uid='".$params['uid']."',MID='".$params['mid']."',isCatalogue='0',mc_type='".$params['mc_type']."'");
			
			if($parentid != "0")
				$this->t1Dao->execute("update dc_customer_data_catalogue set hasleaves='1',updatetime='".date("Y-m-d H:i:s")."' where id='".$parentid."'");

			echo '<script src="../js/layui.js"></script><script>			
				var layer = layui.layer;
				layer.alert("添加成功！",function(index){
					window.parent.location.reload();
					var index=parent.layer.getFrameIndex(window.name); //获取当前窗口的name
					parent.layer.close(index);
				});
				</script>';
		}
	}

	//数据详细
	public function datadetail($params)
	{
		$pid = $params['pid'];
		$uid = $params['uid'];

		$this->assign("pid",$pid);
		$this->assign("uid",$uid);
	}
	
	public function getdatadetail($params)
	{
		$pid = $params['pid'];
		$data = $this->t1Dao->query("select id,D1date,D1 from dc_customer_data where parentid='".$pid."' and isdel=0 order by D1date desc");

		$arr['code'] = "0";
		$arr['count'] =  count($data);
		$arr['data'] =  $data;

		$json_string = $this->pri_JSON($arr);
		echo $json_string;
	}

	public function adddatadetail($params)
	{
		$pid = $params['pid'];
		$uid = $params['uid'];

		if($params['isimport'] == "1")
		{
			$data = $this->t1Dao->query("select id,cataloguename,D1dtymd from dc_customer_data_catalogue where id in (".$pid.") and isdel=0");		
			foreach($data as &$rdata)
			{
				$rdata['cataloguename'] = $this->gb2312Toutf8(base64_decode($rdata['cataloguename']));
			}
			if(!empty($data))
			{
				if($data['0']['D1dtymd'] == "1")
				{
					$timetype=",type: 'year'";$date= date("Y");
				}
				elseif($data['0']['D1dtymd'] == "3")
				{
					$timetype=",type: 'month'";$date= date("Y-m");
				}
				else
				{
					$timetype="";$date= date("Y-m-d");
				}
			}
		
			$this->assign("data",$data);
		}
		else
		{
			$D1dtymd = $this->t1Dao->getOne("select D1dtymd from dc_customer_data_catalogue where id=$pid and isdel=0");			
			if($D1dtymd == "1")
			{
				$timetype=",type: 'year'";$date= date("Y");
			}
			elseif($D1dtymd == "3")
			{
				$timetype=",type: 'month'";$date= date("Y-m");
			}
			else
			{
				$timetype="";$date= date("Y-m-d");
			}
		}

		$this->assign("pid",$pid);
		$this->assign("uid",$uid);
		$this->assign("date",$date);
		$this->assign("timetype",$timetype);
		$this->assign("isimport",$params['isimport']);
	}

	public function dodatadetail($params)
	{ 
		if($params['isedit'] == "1" && $params['id'] != "")
		{
			$this->t1Dao->execute("update dc_customer_data set D1='".$params['D1']."',updatetime='".date("Y-m-d H:i:s")."' where id='".$params['id']."'");

			$res['code'] = 1;		
			echo json_encode($res);
		}
		elseif($params['isdel'] == "1")
		{
			if($params['D1date'] != "")
			{
				$ids = explode(',', $params['id']);
				foreach($ids as $id)
				{
					$this->t1Dao->execute("update dc_customer_data set isdel=1,deltime='".date("Y-m-d H:i:s")."' where parentid='".$id."' and D1date='".$params['D1date']."'");
				}
			}
			else
				$this->t1Dao->execute("update dc_customer_data set isdel=1,deltime='".date("Y-m-d H:i:s")."' where id='".$params['id']."'");
			
			$res['code'] = 1;		
			echo json_encode($res);
		}
		else
		{
			if($params['isimport'] == "1")
			{
				foreach($params['D'] as $p=>$par)
				{
					$hasdata = $this->t1Dao->getOne("select id from dc_customer_data where D1date='".$params['D1date']."' and parentid='".$p."' and isdel=0");			
					if($hasdata != "")
					{
						$this->t1Dao->execute("update dc_customer_data set D1='".$par."',updatetime='".date("Y-m-d H:i:s")."' where id='$hasdata'");
					}
					else
					{
						$mid = $this->t1Dao->getOne("select Mid from app_session_temp where Uid='".$params['uid']."' order by LoginDate desc limit 1");
						$D1name = $this->t1Dao->getOne("select cataloguename from dc_customer_data_catalogue where id='".$p."'");

						$this->t1Dao->execute("insert into dc_customer_data set parentid='".$p."',D1name='".$D1name."',D1date='".$params['D1date']."',D1='".$par."',createtime='".date("Y-m-d H:i:s")."',Uid='".$params['uid']."',MID='".$mid."'");
					}
				}
			}
			else
			{
				$hasdata = $this->t1Dao->getOne("select id from dc_customer_data where D1date='".$params['D1date']."' and parentid='".$params['pid']."' and isdel=0");			
				if($hasdata != "")
				{
					$this->t1Dao->execute("update dc_customer_data set D1='".$params['D1']."',updatetime='".date("Y-m-d H:i:s")."' where id='$hasdata'");
				}
				else
				{
					$mid = $this->t1Dao->getOne("select Mid from app_session_temp where Uid='".$params['uid']."' order by LoginDate desc limit 1");
					$D1name = $this->t1Dao->getOne("select cataloguename from dc_customer_data_catalogue where id='".$params['pid']."'");

					$this->t1Dao->execute("insert into dc_customer_data set parentid='".$params['pid']."',D1name='".$D1name."',D1date='".$params['D1date']."',D1='".$params['D1']."',createtime='".date("Y-m-d H:i:s")."',Uid='".$params['uid']."',MID='".$mid."'");
				}
			}
			
			if($params['isedit'] == "1")
			{
				$res['code'] = 1;		
				echo json_encode($res);
			}
			else
			{
				echo '<script src="../js/layui.js"></script><script>			
					var layer = layui.layer;
					layer.alert("添加成功！",function(index){
						window.parent.location.reload();
						var index=parent.layer.getFrameIndex(window.name); //获取当前窗口的name
						parent.layer.close(index);
					});
					</script>';
			}
		}

	}

	public function getselectcal($params)
	{
		if($params['id'] != "")
		{
			$childdata =  $this->t1Dao->getcal_parentid($params['id'],"1");
				
			foreach($childdata as $child)
			{
				$child['cataloguename'] = $this->gb2312Toutf8(base64_decode($child['cataloguename']));
				$res[] = $child;
			}
		}

		//echo '<pre/>';print_r($res);exit;
		echo $this->pri_JSON($res);
	}

	public function importexcel($params)
	{
		//echo '<pre/>';print_r($params);exit;
		if ($_FILES['file']["error"] == "0") {			
			
			$file = $_FILES['file']['tmp_name'];
			$fileType=\PhpOffice\PhpSpreadsheet\IOFactory::identify($file);//自动获取文件的类型提供给phpexcel用
			$objReader=\PhpOffice\PhpSpreadsheet\IOFactory::createReader($fileType);//获取文件读取操作对象
			$objReader->setReadDataOnly(true); //只读取数据,会智能忽略所有空白行,这点很重要！！！
			$objPHPExcel = $objReader->load($file); //加载Excel文件
						
			$sheet =  $objPHPExcel->getActiveSheet();
			//获取当前工作表最大行数
			$rows = $sheet->getHighestRow();
			//获取当前工作表最大列数,返回的是最大的列名
			$cols = $sheet->getHighestColumn();
			
			$data = array();
			$listDate = array();
			$Rowdata = array();
			
			//获取数据类型
			$D1dtymd = $this->t1Dao->getOne("select D1dtymd from dc_customer_data_catalogue where id in (".$params['pid'].") and isdel=0");
			
			for ($k = 2; $k <= $rows; $k++) {
				$key = "A" . $k;

				$cell = $sheet->getCell($key);
				$dataType=$cell->getDataType();
				$readdate = $sheet->getCell($key)->getValue();
				$readdate = trim($readdate);
				$readdate = str_replace("/","-",$readdate);	
				
				if($readdate != "")
				{
					if($dataType == \PhpOffice\PhpSpreadsheet\Cell\DataType::TYPE_NUMERIC)
					{
						$cellstyleformat=$cell->getStyle()->getNumberFormat();
						$formatcode=$cellstyleformat->getFormatCode();
						if(preg_match('/^(\[\$[A-Z]*-[0-9A-F]*\])*[hmsdy]/i',$formatcode))
						{
							if($D1dtymd == "1")
								$listDate[] = gmdate('Y',$readdate);
							elseif($D1dtymd == "3")
								$listDate[] = gmdate('Y-m',$readdate);
							else
								$listDate[] = gmdate('Y-m-d',$readdate);
						}
						else
						{
							if(strlen($readdate) == "4" && strpos($readdate,'-') === false && $D1dtymd == "1")
								$listDate[] = $readdate;
							else
							{
								$readdate = intval(($readdate - 25569) * 3600 * 24);//转换成1970年以来的秒数
								if($D1dtymd == "1")
									$listDate[] = gmdate('Y',$readdate);
								elseif($D1dtymd == "3")
									$listDate[] = gmdate('Y-m',$readdate);
								else
									$listDate[] = gmdate('Y-m-d',$readdate);
							}
						}
					}
					else
					{	
						if(substr_count($readdate, "-") == 2)
						{
							if($D1dtymd == "1")
								$listDate[] = date('Y', strtotime($readdate));
							elseif($D1dtymd == "3")
								$listDate[] = date('Y-m', strtotime($readdate));
							else
								$listDate[] = date('Y-m-d', strtotime($readdate));
						}
						else
							$listDate[] = $readdate;
					}
				}
				//$date=date('Y/m/d',PHPExcel_Shared_Date::ExcelToPHP($sheet->getCellByColumnAndRow($ii, $i)->getValue()));
			}
			//大写字母A的ASCII值是65 A-Z对应65-90
			/*for ($j = 'B'; $j <= $cols; $j++) {
				for ($k = 1; $k <= $rows; $k++) {
					$key = $j . $k;
					$data[] = $sheet->getCell($key)->getCalculatedValue();
				}
			}*/

			for ($j = 'B'; $j <= $cols; $j++) {
				for ($k = 1; $k <= $rows; $k++) {
					$key = $j . $k;
					if($k == 1)
						$Rowdata[$j] = $sheet->getCell($key)->getCalculatedValue();
					else
						$data[$j][] = $sheet->getCell($key)->getCalculatedValue();
				}
			}
			//echo '<pre/>';print_r($listDate);exit;
			
			$pids = explode(',', $params['pid']);
			$i='B';
			foreach($pids as $pid)
			{
				foreach($listDate as $Da=>$Date)
				{
					$hasdata=$this->t1Dao->getOne("select ID from dc_customer_data where parentid='".$pid."' and D1date='".$Date."' and isdel=0");
					if($hasdata != "" && $data[$i][$Da] != "")
					{
						$this->t1Dao->execute("update dc_customer_data set D1='".$data[$i][$Da]."',updatetime='".date("Y-m-d H:i:s")."' where id='$hasdata'");
					}
					else
					{
						if($data[$i][$Da] != "")
						{
						$mid = $this->t1Dao->getOne("select Mid from app_session_temp where Uid='".$params['uid']."' order by LoginDate desc limit 1");
						
						$this->t1Dao->execute("insert into dc_customer_data set parentid='".$pid."',D1name='".base64_encode($this->utf8ToGb2312($Rowdata[$i]))."',D1date='".$Date."',D1='".$data[$i][$Da]."',createtime='".date("Y-m-d H:i:s")."',Uid='".$params['uid']."',MID='".$mid."'");
						}
					}
				}
				$i++;
			}
			
			$res["Code"] = 0;
		}
		else
		{
			$res["Code"] = 1;
		}
		echo json_encode($res);		
	}

	public function datadat($params)
	{
		$pid = $params['pid'] == "" ? "0":$params['pid'];
		if($params['id'] != "" && $params['id'] != "0")
		{
			$editdata = $this->t1Dao->getRow("select cataloguename,aod,status,PowerID,parentid,MID from dc_customer_data_catalogue where id='".$params['id']."'");
			
	  		//目录层级
			if($editdata['parentid'] != "0")
			{
				$selectedid='';
				$parname2 = $this->t1Dao->get_parname($editdata['parentid']);
				
				if($parname2['parentid'] != "0")
				{
					$parname1 = $this->t1Dao->get_parname($parname2['parentid']);

					$select .='<div class="layui-input-inline"><select name="parentid'.$parname1['nodelevel'].'" lay-filter="parentid'.$parname1['nodelevel'].'"><option value="">请选择目录</option>';
					$childdata1 =  $this->t1Dao->getcal_parentid($parname1['parentid'],"1",$params['uid'],$params['mc_type']);
					
					foreach($childdata1 as $child1)
					{
						$selected='';
						if($child1['id'] == $parname1['id'])
						{
							$selected = ' selected=""';
						}

						$select .='<option value="'.$child1['id'].'"'.$selected.'>'.$this->gb2312Toutf8(base64_decode($child1['cataloguename'])).'</option>';
					}
					$select .='</select></div>';
				}

				$select .='<div class="layui-input-inline"><select name="parentid'.$parname2['nodelevel'].'" id="parentid'.$parname2['nodelevel'].'" lay-filter="parentid'.$parname2['nodelevel'].'"><option value="">请选择目录</option>';
				$childdata2 =  $this->t1Dao->getcal_parentid($parname2['parentid'],"1",$params['uid'],$params['mc_type']);						
				foreach($childdata2 as $child2)
				{
					$selected='';
					if($child2['id'] == $parname2['id'])
					{
						$selected=' selected=""';
						$selectedid = $child2['id'];
					}

					$select .='<option value="'.$child2['id'].'"'.$selected.'>'.$this->gb2312Toutf8(base64_decode($child2['cataloguename'])).'</option>';
				}
				$select .='</select></div>';

				if($parname2['nodelevel'] < 2)
				{
					if($selectedid != "")
					{
						$childdata =  $this->t1Dao->getcal_parentid($selectedid,"1",$params['uid'],$params['mc_type']);
						
						$select .='<div class="layui-input-inline"><select name="parentid'.($parname2['nodelevel']+1).'" id="parentid'.($parname2['nodelevel']+1).'" lay-filter="parentid'.($parname2['nodelevel']+1).'"><option value="">请选择目录</option>';					
						foreach($childdata as $child)
						{
							$select .='<option value="'.$child['id'].'">'.$this->gb2312Toutf8(base64_decode($child['cataloguename'])).'</option>';
						}
						$select .='</select></div>';
					}
					else
					{
						for($i=$parname2['nodelevel']+1;$i<=2;$i++)
						{
							$select .='<div class="layui-input-inline"><select name="parentid'.$i.'" id="parentid'.$i.'" lay-filter="parentid'.$i.'"><option value="">请选择目录</option></select></div>';
						}
					}
				}
			}
			else
			{
				$select ='<div class="layui-input-inline"><select name="parentid1" lay-filter="parentid1"><option value="">请选择目录</option>';
				$childdata =  $this->t1Dao->getcal_parentid('0',"1",$params['uid'],$params['mc_type']);
				foreach($childdata as $child)
				{
					$select .='<option value="'.$child['id'].'"'.'>'.$this->gb2312Toutf8(base64_decode($child['cataloguename'])).'</option>';
				}
				$select .='</select></div>';
				$select .='<div class="layui-input-inline"><select name="parentid2" id="parentid2" lay-filter="parentid2"><option value="">请选择目录</option></select></div>';	
			}

			$sondata =  $this->t1Dao->getdata_parentid($params['id'],$params['uid'],$params['mc_type']);
			if(!empty($sondata))
			{
				$sondiv = '';
				//echo '<pre/>';print_r($sondata);
				foreach($sondata as $s=>$son)
				{
					if($s != 0 && $s%4 == 0)
						$sondiv .= '<label class="layui-form-label"></label>';
					$sondiv .= '<div class="layui-input-inline" style="width: 100px;"><input type="text" name="D['.$son['id'].']" autocomplete="off" class="layui-input" value="'.$this->gb2312Toutf8(base64_decode($son['cataloguename'])).'"></div>';
				}

				$this->assign("D1ImageType",$sondata['0']['D1ImageType']);
				$this->assign("D1UnitType",$sondata['0']['D1UnitType']);
				$this->assign("D1UnitName",$sondata['0']['D1UnitName']);
				$this->assign("D1UnitConv",$sondata['0']['D1UnitConv']);
				$this->assign("D1dtymd",$sondata['0']['D1dtymd']);
				$this->assign("sondiv",$sondiv);
			}
			
			if($sondata['D1UnitType'] == "" || $sondata['D1UnitType'] == "0")
				$display = "none";
			else
				$display = "inline";

			//共享使用
			$users = $this->t1Dao->getuser_mid($editdata['MID'],$params['mc_type'],$params['uid']);
			foreach($users as $user)
			{
				$checked = '';
				if($editdata['PowerID'] != "" && strpos($editdata['PowerID'],','.$user['Uid'].',') !== false)
					$checked = ' checked=""';
				$checkbox .= '<input type="checkbox" name="powerid['.$user['Uid'].']" lay-skin="primary" title="'.$user['TrueName'].'"'.$checked.'>';
				
			}	
			
			$isedit=1;
			$this->assign("select",$select);
			$this->assign("id",$params['id']);
			$this->assign("cataloguename",$this->gb2312Toutf8(base64_decode($editdata['cataloguename'])));
			$this->assign("aod",$editdata['aod']);
			$this->assign("status",$editdata['status']);
		}
		else
		{
			$isedit = "0";
			$display = "none";

			if($pid == "0")
			{		
				$select ='<div class="layui-input-inline"><select name="parentid1" lay-filter="parentid1"><option value="">请选择目录</option>';
				$childdata =  $this->t1Dao->getcal_parentid('0',"1",$params['uid'],$params['mc_type']);
				foreach($childdata as $child)
				{
					$select .='<option value="'.$child['id'].'"'.'>'.$this->gb2312Toutf8(base64_decode($child['cataloguename'])).'</option>';
				}
				$select .='</select></div>';
				$select .='<div class="layui-input-inline"><select name="parentid2" id="parentid2" lay-filter="parentid2"><option value="">请选择目录</option></select></div>';	
					
				$this->assign("select",$select);	
			}

			$mid = $this->t1Dao->getOne("select Mid from app_session_temp where Uid='".$params['uid']."' order by LoginDate desc limit 1");
			$users = $this->t1Dao->getuser_mid($mid,$params['mc_type'],$params['uid']);
			foreach($users as $user)
			{
				$checkbox .= '<input type="checkbox" name="powerid['.$user['Uid'].']" lay-skin="primary" title="'.$user['TrueName'].'">';				
			}
		}
		$mid = $this->t1Dao->getOne("select Mid from app_session_temp where Uid='".$params['uid']."' order by LoginDate desc limit 1");
		
		$this->assign("pid",$pid);
		$this->assign("isedit",$isedit);
		$this->assign("display",$display);
		$this->assign("mc_type",$params['mc_type']);		
		$this->assign("mid",$mid);	
		$this->assign("uid",$params['uid']);		
		$this->assign("checkbox",$checkbox);
	}
	
	public function dodatadat($params)
	{		
		if($params['pid'] == "" || $params['pid'] == "0")
		{
			if($params['parentid2'] != "")
			{
				$nodelevel = 3;
				$params['pid'] = $params['parentid2'];
			}
			elseif($params['parentid1'] != "")
			{
				$nodelevel = 2;
				$params['pid'] = $params['parentid1'];
			}
			else
			{
				$nodelevel = 1;
				$params['pid'] = 0;
			}
		}
		else
		{
			$plevel = $this->t1Dao->getOne("select nodelevel from dc_customer_data_catalogue where id='".$params['pid']."'");
			$nodelevel = $plevel + 1;
		}

		if(!empty($params['powerid']))
		{
			foreach($params['powerid'] as $k=>$v)
			{
				$powerid .= $k.",";
			}
			$powerid = ",".$powerid;
		}

		if($params['isedit'] == "0")
		{
			if ($_FILES['file']["error"] == "0")
			{			
				//include_once("/usr/local/www/libs/PHPExcel/PHPExcel.php");
				
				$upfile = $_FILES['file'];		
				$extName = strtolower(end(explode('.', $upfile['name']))); // 取得扩展名
				$name1 = $params['pid'].date('YmdHis') . "." . $extName;
				$dir = "../html/default/customerdata/file";

				if (!file_exists($dir)) {
					if (!mkdir($dir, 0777)) {
						$res['Code'] = 0;
					}
				}
				$dest = $dir . "/" . $name1;
				move_uploaded_file($upfile ['tmp_name'], $dest);
				
				$file = $dest;
				$fileType=\PhpOffice\PhpSpreadsheet\IOFactory::identify($file);//自动获取文件的类型提供给phpexcel用
				$objReader=\PhpOffice\PhpSpreadsheet\IOFactory::createReader($fileType);//获取文件读取操作对象
						
				$objReader->setReadDataOnly(true); //只读取数据,会智能忽略所有空白行,这点很重要！！！
				$objPHPExcel = $objReader->load($file); //加载Excel文件
							
				$sheet =  $objPHPExcel->getActiveSheet();
				//获取当前工作表最大行数
				$rows = $sheet->getHighestRow();
				//获取当前工作表最大列数,返回的是最大的列名
				$cols = $sheet->getHighestColumn();
				
				$listDate = array();//数据日期
				$Rowdata = array();//表格头部
				$data = array();//表格数据
				
				//获取表格日期
				for ($k = 2; $k <= $rows; $k++){
					$key = "A" . $k;
					$cell = $sheet->getCell($key);
					$dataType=$cell->getDataType();
					$readdate = $sheet->getCell($key)->getValue();
					$readdate = trim($readdate);
					$readdate = str_replace("/","-",$readdate);
					
					if($readdate != "")
					{
						if($dataType ==  \PhpOffice\PhpSpreadsheet\Cell\DataType::TYPE_NUMERIC)
						{
							$cellstyleformat=$cell->getStyle()->getNumberFormat();
							$formatcode=$cellstyleformat->getFormatCode();
							if(preg_match('/^(\[\$[A-Z]*-[0-9A-F]*\])*[hmsdy]/i',$formatcode))
							{
								if($params['D1dtymd'] == "1")
									$listDate[] = gmdate('Y',$readdate);
								elseif($params['D1dtymd'] == "3")
									$listDate[] = gmdate('Y-m',$readdate);
								else
									$listDate[] = gmdate('Y-m-d',$readdate);
							}
							else
							{
								if(strlen($readdate) == "4" && strpos($readdate,'-') === false && $params['D1dtymd'] == "1")
									$listDate[] = $readdate;
								else
								{
									$readdate = intval(($readdate - 25569) * 3600 * 24);//转换成1970年以来的秒数
									if($params['D1dtymd'] == "1")
										$listDate[] = gmdate('Y',$readdate);
									elseif($params['D1dtymd'] == "3")
										$listDate[] = gmdate('Y-m',$readdate);
									else
										$listDate[] = gmdate('Y-m-d',$readdate);
								}
							}
						}
						else
						{
							if(substr_count($readdate, "-") == 2)
							{
								if($params['D1dtymd'] == "1")
									$listDate[] = date('Y', strtotime($readdate));
								elseif($params['D1dtymd'] == "3")
									$listDate[] = date('Y-m', strtotime($readdate));
								else
									$listDate[] = date('Y-m-d', strtotime($readdate));
							}
							else
								$listDate[] = $readdate;
						}
					}
					//$date=date('Y/m/d',PHPExcel_Shared_Date::ExcelToPHP($sheet->getCellByColumnAndRow($ii, $i)->getValue()));
				}
				//大写字母A的ASCII值是65 A-Z对应65-90 获取表格头部及数据数值
				for ($j = 'B'; $j <= $cols; $j++) {
					for ($k = 1; $k <= $rows; $k++) {
						$key = $j . $k;
						if($k == 1)
						{
							if($sheet->getCell($key)->getCalculatedValue() != "")
								$Rowdata[$j] = $sheet->getCell($key)->getCalculatedValue();
						}
						else
						{
							//if($sheet->getCell($key)->getCalculatedValue() != "")
								$data[$j][] = $sheet->getCell($key)->getCalculatedValue();
						}
					}
				}
				
				//echo '<pre/>';print_r($listDate);exit;

				if(!empty($Rowdata))
				{
					$this->t1Dao->execute("insert into dc_customer_data_catalogue set cataloguename='".base64_encode($this->utf8ToGb2312(htmlspecialchars_decode($params['cataloguename'])))."',parentid='".$params['pid']."',aod='".$params['aod']."',status='".$params['status']."',powerid='".$powerid."',createtime='".date("Y-m-d H:i:s")."',Uid='".$params['uid']."',MID='".$params['mid']."',isCatalogue='1',mc_type='".$params['mc_type']."',nodelevel='".$nodelevel."',hasleaves=1,isimport=1,importfile='".$dest."'");
					$importpid = $this->t1Dao->insert_id();
					$this->t1Dao->execute("update dc_customer_data_catalogue set hasnode='1',updatetime='".date("Y-m-d H:i:s")."' where id='".$params['pid']."'");
					
					$s=1;$importid="";
					foreach($Rowdata as $R=>$Row)
					{
						//$Row = iconv("UTF-8","GBK",$Row);
						if($Row != "")
						{
							$this->t1Dao->execute("insert into dc_customer_data_catalogue set cataloguename='".base64_encode($this->utf8ToGb2312(htmlspecialchars_decode($Row)))."',parentid='".$importpid."',aod='".$s."',status='1',powerid='".$powerid."',D1ImageType='".$params['D1ImageType']."',D1UnitType='".$params['D1UnitType']."',D1UnitName='".$params['D1UnitName']."',D1UnitConv='".$params['D1UnitConv']."',D1dtymd='".$params['D1dtymd']."',createtime='".date("Y-m-d H:i:s")."',Uid='".$params['uid']."',MID='".$params['mid']."',isCatalogue='0',mc_type='".$params['mc_type']."',isimport='1',importfile='".$dest."'");
							$importdataid = $this->t1Dao->insert_id();
									
							foreach($data[$R] as $D=>$Da)
							{
								if($Da != "")
								$this->t1Dao->execute("insert into dc_customer_data set parentid='".$importdataid."',D1name='".base64_encode($this->utf8ToGb2312(htmlspecialchars_decode($Row)))."',D1date='".$listDate[$D]."',D1='".$Da."',createtime='".date("Y-m-d H:i:s")."',Uid='".$params['uid']."',MID='".$params['mid']."'");								
							}
							
							$s++;
							$importid .= $importdataid.',';
						}
					}

					if($importid != "")
					{
						$this->t1Dao->execute("update dc_customer_data_catalogue set importid='".substr($importid,0,strlen($importid)-1)."',updatetime='".date("Y-m-d H:i:s")."' where id='$importpid'");
					}

					echo '<script src="../js/layui.js"></script><script>			
						var layer = layui.layer;
						layer.alert("添加成功！",function(index){
							window.parent.location.reload();
							var index=parent.layer.getFrameIndex(window.name); //获取当前窗口的name
							parent.layer.close(index);
						});
						</script>';					
				}
				else
				{
					echo '<script src="../js/layui.js"></script><script>			
					var layer = layui.layer;
					layer.alert("添加失败，请按照模板格式上传文件！",function(index){
						window.history.back();
					});
					</script>';
				}
			}
			else
			{
				echo '<script src="../js/layui.js"></script><script>			
					var layer = layui.layer;
					layer.alert("添加失败！",function(index){
						window.history.back();
					});
					</script>';
			}
		}
		else
		{
			$parid = $this->t1Dao->get_parname($params['id']);
			if(($params['pid'] == $params['id']) || ($nodelevel > 2 && $parid['hasnode'] == "1"))
			{
				echo '<script src="../js/layui.js"></script><script>			
					var layer = layui.layer;
					layer.alert("不能移动到该目录下！",function(index){
						window.history.back();
					});
					</script>';
			}
			else
			{
				$flag = true;
				if($parid['parentid'] == "0" && $params['pid'] != 0 && $parid['hasnode'] == "1")
				{
					$data = $this->t1Dao->query("SELECT hasnode from dc_customer_data_catalogue where parentid='".$arid['id']."' and isCatalogue=1 and Uid='".$params['uid']."'");
					foreach($data as $da)
					{
						if($da['hasnode'] == "1")
						{
							$flag = false;
							break;
						}
					}
				}
				
				if($flag)
				{
					//目录部分修改
					$this->t1Dao->execute("update dc_customer_data_catalogue set cataloguename='".base64_encode($this->utf8ToGb2312(htmlspecialchars_decode($params['cataloguename'])))."',parentid='".$params['pid']."',nodelevel='".$nodelevel."',aod='".$params['aod']."',status='".$params['status']."',powerid='".$powerid."',updatetime='".date("Y-m-d H:i:s")."' where id='".$params['id']."'");
					$this->t1Dao->execute("update dc_customer_data_catalogue set hasnode='1' where id='".$params['pid']."'");

					if($parid['parentid'] != $params['pid'])
					{
						$node =  $this->t1Dao->getcal_parentid($parid['parentid'],"1","",$params['mc_type']);
						if(count($node) < 1)
							$this->t1Dao->execute("update dc_customer_data_catalogue set hasnode='0',updatetime='".date("Y-m-d H:i:s")."' where id='".$parid['parentid']."'");
					}

					//数据部分修改
					foreach($params['D'] as $d=>$name)
					{
						$this->t1Dao->execute("update dc_customer_data_catalogue set cataloguename='".base64_encode($this->utf8ToGb2312(htmlspecialchars_decode($name)))."',status='".$params['status']."',powerid='".$powerid."',D1ImageType='".$params['D1ImageType']."',D1UnitType='".$params['D1UnitType']."',D1UnitName='".$params['D1UnitName']."',D1UnitConv='".$params['D1UnitConv']."',D1dtymd='".$params['D1dtymd']."',updatetime='".date("Y-m-d H:i:s")."' where id='".$d."'");						
						$this->t1Dao->execute("update dc_customer_data set D1name='".base64_encode($this->utf8ToGb2312(htmlspecialchars_decode($name)))."',updatetime='".date("Y-m-d H:i:s")."' where parentid='".$d."'");
					}

					echo '<script src="../js/layui.js"></script><script>			
						var layer = layui.layer;
						layer.alert("修改成功！",function(index){
							window.parent.location.reload();
							var index=parent.layer.getFrameIndex(window.name); //获取当前窗口的name
							parent.layer.close(index);
						});
						</script>';
				}
				else
				{
					echo '<script src="../js/layui.js"></script><script>			
					var layer = layui.layer;
					layer.alert("不能移动到该目录下！",function(index){
						window.history.back();
					});
					</script>';
				}
			}
		}

		//print_r($params);print_r($_FILES);exit;
	}

	public function importdatadetail($params)
	{
		$pid = $params['pid'];
		$uid = $params['uid'];
		$data = $this->t1Dao->query("select id,cataloguename from dc_customer_data_catalogue where id in (".$pid.") and isdel=0");
		$importfile = $this->t1Dao->getOne("select importfile from dc_customer_data_catalogue where id='".$params['id']."'");

		foreach($data as &$rdata)
		{
			$rdata['cataloguename'] = $this->gb2312Toutf8(base64_decode($rdata['cataloguename']));
		}
		
		$this->assign("pid",$pid);
		$this->assign("uid",$uid);		
		$this->assign("data",$data);	
		$this->assign("importfile",$importfile);
	}

	public function getimportdatadetail($params)
	{
		$pid = $params['pid'];
		$data = $this->t1Dao->query("select id,D1date,D1,parentid from dc_customer_data where parentid in (".$pid.") and isdel=0 order by D1date desc");
		
		$newdata = array();
		$retdata = array();
		foreach($data as $da)
		{
			//$newdata[$da['parentid']][$da['D1date']]['id']= $da['id'];
			$newdata[$da['parentid']][$da['D1date']]['D1']= $da['D1'];
		}
		
		$datatype = $this->t1Dao->getOne("select D1dtymd from dc_customer_data_catalogue where id in (".$pid.") and isdel=0 order by id desc");
		foreach($newdata as $n=>$new)
		{
			foreach($new as $d=>$ta)
			{
				if($datatype == "1")
				{
					$retdata[$d.'-01-01']['D1date']= $d;
					$retdata[$d.'-01-01'][$n]= $ta['D1'];
				}
				else
				{
					//$retdata[$d]['id']= $ta['id'];
					$retdata[$d]['D1date']= $d;
					$retdata[$d][$n]= $ta['D1'];
				}
			}
		}
		//echo '<pre/>';print_r($data);print_r($newdata);print_r($retdata);exit;
		
		$arr['code'] = "0";
		$arr['count'] =  count($retdata);
		$arr['data'] =  $retdata;

		$json_string = $this->pri_JSON($arr);
		echo $json_string;
		
		/*echo '{"code":0,"count":1000,"data":[{"aod":10000,"cataloguename":"user-0","status":"女","D1ImageType":"城市-0","D1UnitConv":"签名-0","createtime":82830700,"PowerID":"作家","D1UnitName":57},{"aod":10001,"cataloguename":"user-1","status":"男","D1ImageType":"城市-1","D1UnitConv":"签名-1","createtime":64928690,"PowerID":"词人","D1UnitName":27},{"aod":10019,"cataloguename":"user-19","status":"女","D1ImageType":"城市-19","D1UnitConv":"签名-19","createtime":68647362,"PowerID":"词人","D1UnitName":51}]}';*/
	}

	private function pri_JSON($array) {   
		$this->pri_arrayRecursive($array, 'urlencode', true);      
		$json = json_encode($array);  
		//return iconv("GB2312","UTF-8",urldecode($json));
		return  urldecode($json);
	}   

	private function pri_arrayRecursive(&$array, $function, $apply_to_keys_also = false)
	{
		static $recursive_counter = 0;
		if (++$recursive_counter > 1000) {
			die('possible deep recursion attack');
		}
		foreach ($array as $key => $value) {
			if (is_array($value)) {
				$this->pri_arrayRecursive($array[$key], $function, $apply_to_keys_also);
			} else {
				$array[$key] = $function($value);
			}
			if ($apply_to_keys_also && is_string($key)) {
				$new_key = $function($key);
				if ($new_key != $key) {
					$array[$new_key] = $array[$key];
					unset($array[$key]);
				}
			}
		}
		$recursive_counter--;
	}
}
?>