<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );

class DcDgRiDjController extends AbstractController{
  
  public function __construct(){
    parent::__construct();
	
	//$this->_action->setDao( new DcDgRiDjDao( "MAIN" ) );
	//$this->_action->bizdao = new SystemDao('BIZ') ;
	$this->_action->stedao = new DcDgRiDjDao('MAIN') ;
	$this->_action->ngdao = new DcDgRiDjDao('DRCW') ;
	//echo "<pre>";print_r($this->_action->ngdao);exit;
	$this->_action->maindao = new DcDgRiDjDao('91R') ;
	$this->_action->gcdao=new DcDgRiDjDao('GC');//hlf/钢厂调价的本期价格及涨跌幅,正式库
	//$this->_action->qhdao=new DcDgRiDjDao('HQT');//	煤焦期货表			
	
  }
  
  public function _dopre(){
      //$this->_action->checkSession();
  }

  public function v_index() {
  	
	$this->_action->index($this->_request);
  }
   public function do_savedate() {
  	
	$this->_action->savedate($this->_request);
  }
}