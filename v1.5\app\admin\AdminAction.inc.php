<?php
include_once( FRAME_LIB_DIR ."/action/AbstractAction.inc.php" );
$GLOBALS['mc_type_list']=array(
	"0"=>"钢之家",
	"1"=>"南钢",
	"2"=>"陕钢",
	"3"=>"新钢",
	"4"=>"新钢价格预测",
);
class AdminAction extends AbstractAction{

	public $stedao;
	public $drc;
	public $sms;

  public function __construct(){
    parent::__construct();
  }

  //检查钢之家后台Session
  public function checkSession(){
    //   if( !$_SESSION['dviewpower'] ){
	// 	  alert("无权限查看");
	// 	  goBack();
	//       //goURL( "/admincp/login.php" );
	//   }
	  
//	  $_SESSION['COMID'] = "45";
//	  $_SESSION['USERNAME'] = "NCC";
	  
	  
  }
   


public function getdata( $array, $params ){
    $data = array();
	$intarray=array("dtymd","dtcomparetype","isSubDatas","isMarkets","isData1","Data1Type","isData2","Data2Type","isData3","Data3Type","isData4","Data4Type","isData5","Data5Type","isData6","Data6Type","isData7","Data7Type","isData8","Data8Type","isData9","Data9Type","isData10","Data10Type","isData11","Data11Type","isData12","Data12Type","isData13","Data13Type","isData14","Data14Type","isData15","Data15Type","isData16","Data16Type","isData17","Data17Type","isData18","Data18Type","isData19","Data19Type","isData20","Data20Type","isPreData","isAddSubData","isAddSubHundCore","enddataisCurrent","dtod","techtabletype","techtableGroupBy","Status","IsEnglish","Data1UnitType","Data1UnitConv","Data2UnitType","Data2UnitConv","Data3UnitType","Data3UnitConv","Data4UnitType","Data4UnitConv","Data5UnitType","Data5UnitConv","Data6UnitType","Data6UnitConv","Data7UnitType","Data7UnitConv","Data8UnitType","Data8UnitConv","Data9UnitType","Data9UnitConv","Data10UnitType","Data10UnitConv","Data11UnitType","Data11UnitConv","Data12UnitType","Data12UnitConv","Data13UnitType","Data13UnitConv","Data14UnitType","Data14UnitConv","Data15UnitType","Data15UnitConv","Data16UnitType","Data16UnitConv","Data17UnitType","Data17UnitConv","Data18UnitType","Data18UnitConv","Data19UnitType","Data19UnitConv","Data20UnitType","Data20UnitConv","dbaseType","TargetFlagVar","IsGetNew","npredata_UnitConv","npredata_UnitType","naddsubdata_UnitConv","daddsubhundcore_UnitConv","naddsubdata_UnitType","daddsubhundcore_UnitType");
    foreach( $array as $a ){
		//if (!get_magic_quotes_gpc()){
			$params[$a]=addslashes($params[$a]);
		//}
			if(in_array($a,$intarray))
			{
				$data[] = $a . "='" . (int)$params[$a] . "'";
			}
			else
			{
				$data[] = $a . "='" . $params[$a] . "'";
			}
			
		
		
    }
    $data = implode( ",", $data );
    return $data;
}
   

public function index($params) {
	echo "hello world!!";
}

	public function setvars($params=array()) {
		$this->assign("mc_type",$params['mc_type']);
		$sql="select scode,sname from dc_code_class where Status=1 and mcode='' and mc_type='".$params['mc_type']."' order by sod asc ";
		//$firlevel  = $this->_dao->AQUERY("select scode,sname from dc_code_class where Status=1 and mcode='' and mc_type='".$params[mc_type]."' order by sod asc ");
		$firlevel=$this->_dao->AQUERY($sql);
		//print_r($sql);
		$this->assign("firlevel",$firlevel);
		//print_r($firlevel);exit;
		$mc_type=$params['mc_type'];
		//$sql1='SELECT dc_code_datatype_db.DID,dc_code_datatype.techtablename FROM  `dc_code_datatype` , dc_code_datatype_db WHERE dc_code_datatype.techtablename IN ("shpi_coal",  "shpi_material",  "shpi_material_pzp",  "shpi_mj_pzp",  "shpi_pi",  "shpi_pp",  "shpi_pzggp",  "shpi_pzp") AND dc_code_datatype_db.Ddata1 IN ("weiprice",  "weiindex",  "price",  "mindex",  "weipriceusb",  "aveprice",  "apindex",  "wpindex") AND dc_code_datatype_db.DID = dc_code_datatype.ID AND dc_code_datatype_db.mc_type=0';
		//$update1=$this->_dao->query($sql1);
		//echo '<pre>';
		//print_r($update1);
		/*	foreach ($update1 as $key=>$value) {
				$DID=$value['DID'];
				$techtablename=$value['techtablename'];
				if($techtablename=='shpi_pzggp'){
					$sql1="update `dc_code_datatype_db`  set ndata12='',Ddata12='',ndata13='',Ddata13='',ndata14='',Ddata14='',ndata15='',Ddata15='',ndata16='',Ddata16='',ndata17='',Ddata17='', ndata6='5日均线（价格）',Ddata6='aveprice_avg_5',ndata7='10日均线（价格）',Ddata7='aveprice_avg_10',ndata8='20日均线（价格）',Ddata8='aveprice_avg_20',ndata9='40日均线（价格）',Ddata9='aveprice_avg_40',ndata10='60日均线（价格）',Ddata10='aveprice_avg_60',ndata11='200日均线（价格）',Ddata11='aveprice_avg_200' where DID='".$DID."'";
					$this->_dao->execute($sql1);
					$sql4="update `dc_code_datatype`  set  UnitTypeName12='',UnitTypeName13='',UnitTypeName14='',UnitTypeName15='',UnitTypeName16='',UnitTypeName17='',isData12='',isData13='',isData14='',isData15='',isData16='',isData17='',Data12Type='',Data13Type='',Data14Type='',Data15Type='',Data16Type='',Data17Type='',UnitTypeName12_en='',UnitTypeName13_en='',UnitTypeName14_en='',UnitTypeName15_en='',UnitTypeName16_en='',UnitTypeName17_en=''  where ID='".$DID."'";
					$this->_dao->execute($sql4);
					$sql3="update `dc_code_datatype`  set  UnitTypeName6='元/吨',UnitTypeName7='元/吨',UnitTypeName8='元/吨',UnitTypeName9='元/吨',UnitTypeName10='元/吨',UnitTypeName11='元/吨',isData6='1',isData7='1',isData8='1',isData9='1',isData10='1',isData11='1',Data6Type='2',Data7Type='2',Data8Type='2',Data9Type='2',Data10Type='2',Data11Type='2',UnitTypeName6_en='yuan/tonne',UnitTypeName7_en='yuan/tonne',UnitTypeName8_en='yuan/tonne',UnitTypeName9_en='yuan/tonne',UnitTypeName10_en='yuan/tonne',UnitTypeName11_en='yuan/tonne'  where ID='".$DID."'";
					$this->_dao->execute($sql3);
				}*/
				
				
				
				//$sql1="update `dc_code_datatype_db`  set ndata6='5日均线（指数）',Ddata6='apindex_avg_5',ndata7='10日均线（指数）',Ddata7='apindex_avg_10',ndata8='20日均线（指数）',Ddata8='apindex_avg_20',ndata9='40日均线（指数）',Ddata9='apindex_avg_40',ndata10='60日均线（指数）',Ddata10='apindex_avg_60',ndata11='200日均线（指数）',Ddata11='apindex_avg_200', ndata12='5日均线（价格）',Ddata12='aveprice_avg_5',ndata13='10日均线（价格）',Ddata13='aveprice_avg_10',ndata14='20日均线（价格）',Ddata14='aveprice_avg_20',ndata15='40日均线（价格）',Ddata15='aveprice_avg_40',ndata16='60日均线（价格）',Ddata16='aveprice_avg_60',ndata17='200日均线（价格）',Ddata17='aveprice_avg_200' where DID='".$DID."'";
				//$this->_dao->execute($sql1);
				
				/*if($UnitTypeName=='点'){
					$sql2="update `dc_code_datatype`  set  UnitTypeName6='".$UnitTypeName."',UnitTypeName7='".$UnitTypeName."',UnitTypeName8='".$UnitTypeName."',UnitTypeName9='".$UnitTypeName."',UnitTypeName10='".$UnitTypeName."',UnitTypeName11='".$UnitTypeName."',isData6='1',isData7='1',isData8='1',isData9='1',isData10='1',isData11='1',Data6Type='2',Data7Type='2',Data8Type='2',Data9Type='2',Data10Type='2',Data11Type='2',UnitTypeName6_en='Point',UnitTypeName7_en='Point',UnitTypeName8_en='Point',UnitTypeName9_en='Point',UnitTypeName10_en='Point',UnitTypeName11_en='Point' where ID='".$DID."'";
					$this->_dao->execute($sql2);
				}
				
				if($UnitTypeName2!=''){
					$sql3="update `dc_code_datatype`  set  UnitTypeName12='".$UnitTypeName2."',UnitTypeName13='".$UnitTypeName2."',UnitTypeName14='".$UnitTypeName2."',UnitTypeName15='".$UnitTypeName2."',UnitTypeName16='".$UnitTypeName2."',UnitTypeName17='".$UnitTypeName2."',isData12='1',isData13='1',isData14='1',isData15='1',isData16='1',isData17='1',Data12Type='2',Data13Type='2',Data14Type='2',Data15Type='2',Data16Type='2',Data17Type='2',UnitTypeName12_en='yuan/tonne',UnitTypeName13_en='yuan/tonne',UnitTypeName14_en='yuan/tonne',UnitTypeName15_en='yuan/tonne',UnitTypeName16_en='yuan/tonne',UnitTypeName17_en='yuan/tonne'  where ID='".$DID."'";
					$this->_dao->execute($sql3);
				}
				
				if($UnitTypeName=='点'   &&   $UnitTypeName2==''){
					$sql4="update `dc_code_datatype`  set  UnitTypeName12='元/吨',UnitTypeName13='元/吨',UnitTypeName14='元/吨',UnitTypeName15='元/吨',UnitTypeName16='元/吨',UnitTypeName17='元/吨',isData12='1',isData13='1',isData14='1',isData15='1',isData16='1',isData17='1',Data12Type='2',Data13Type='2',Data14Type='2',Data15Type='2',Data16Type='2',Data17Type='2',UnitTypeName12_en='yuan/tonne',UnitTypeName13_en='yuan/tonne',UnitTypeName14_en='yuan/tonne',UnitTypeName15_en='yuan/tonne',UnitTypeName16_en='yuan/tonne',UnitTypeName17_en='yuan/tonne'  where ID='".$DID."'";
					$this->_dao->execute($sql4);
				}
				
				if($UnitTypeName=='元/吨'&&  $UnitTypeName2==''){
					$sql3="update `dc_code_datatype`  set  UnitTypeName6='点',UnitTypeName7='点',UnitTypeName8='点',UnitTypeName9='点',UnitTypeName10='点',UnitTypeName11='点',isData6='1',isData7='1',isData8='1',isData9='1',isData10='1',isData11='1',Data6Type='2',Data7Type='2',Data8Type='2',Data9Type='2',Data10Type='2',Data11Type='2',UnitTypeName6_en='Point',UnitTypeName7_en='Point',UnitTypeName8_en='Point',UnitTypeName9_en='Point',UnitTypeName10_en='Point',UnitTypeName11_en='Point', UnitTypeName12='".$UnitTypeName."',UnitTypeName13='".$UnitTypeName."',UnitTypeName14='".$UnitTypeName."',UnitTypeName15='".$UnitTypeName."',UnitTypeName16='".$UnitTypeName."',UnitTypeName17='".$UnitTypeName."'  where ID='".$DID."'";
					$this->_dao->execute($sql3);
				}
				//$sql2="update `dc_code_datatype_db`  set ndata6='5日均线（指数）',Ddata6='apindex_avg_5',ndata7='10日均线（指数）',Ddata7='apindex_avg_10',ndata8='20日均线（指数）',Ddata8='apindex_avg_20',ndata9='40日均线（指数）',Ddata9='apindex_avg_40',ndata10='60日均线（指数）',Ddata10='apindex_avg_60',ndata11='200日均线（指数）',Ddata11='apindex_avg_200', ndata12='5日均线（价格）',Ddata12='aveprice_avg_5',ndata13='10日均线（价格）',Ddata13='aveprice_avg_10',ndata14='20日均线（价格）',Ddata14='aveprice_avg_20',ndata15='40日均线（价格）',Ddata15='aveprice_avg_40',ndata16='60日均线（价格）',Ddata16='aveprice_avg_60',ndata17='200日均线（价格）',Ddata17='aveprice_avg_200' where DID='".$key."'";
				//$this->_dao->execute($sql2);
		}*/
	
		$this->assign("dataunit",$GLOBALS['DATAUNIT']);
		$this->assign("unitconv1",$GLOBALS['UNITCONV1']);
		$this->assign("unitconv2",$GLOBALS['UNITCONV2']);
		$this->assign("unitconv3",$GLOBALS['UNITCONV3']);
		$this->assign("imagetype1",$GLOBALS['IMAGETYPE1']);
		$this->assign("imagetype2",$GLOBALS['IMAGETYPE2']);
		$this->assign("setdb",$GLOBALS['SETDB']);
		$this->assign("logtype",$GLOBALS['LOGTYPE']);
		$this->assign("dbasetype",$GLOBALS['DBASETYPE']);
		$this->assign("tabletype",$GLOBALS['TABLETYPE']);

	}

	public function licenselist($params) {
		/* error_reporting(E_ALL & ~E_NOTICE );
		ini_set("display_errors","ON"); */
		$this->assign("params",$params);
		$total = $this->_dao->getOne( "SELECT Count(*) as c FROM app_license  WHERE 1 $where" );
		$page = $params['page'] == '' ? 1 : $params['page'];
		$per = 20;
		$url = "applicense.php";
		unset( $params['page'] );
		$start = ( $page - 1 ) * $per;

		$list = $this->_dao->query( "SELECT * from app_license Where 1 $where ORDER BY CreateDate DESC LIMIT $start, $per");
		$this->assign( "list", $list);
		$pagebar = pagebar( $url, $params, $per, $page, $total );
		$this->assign( "pagebar", $pagebar );
		//echo 'aaaa';
		
	}
	//数据分类列表
	public function classlist($params){
		$this->assign("ddelpower",$_SESSION['ddelpower']);
		$this->assign("power",$_SESSION['power']);
		$this->assign("userid",$_SESSION['duserid']);
		
		$where = "";
		
		// change start
		$this->assign("mc_type",$params['mc_type']);
		//echo $params['mc_type'];
		// end	

		$where .=" and mc_type = '".$params['mc_type']."'";
		
		if($params['sname']){
			$where .= " and( sname like '%".$params['sname']."%'  or  sname_en like '%".$params['sname']."%')";
			
		}
		if($params['snameshort']){
			$where .= " and( snameshort like '%".$params['snameshort']."%'  or snameshort_en like '%".$params['snameshort']."%' )";
		}
 		
		
		if($params['scode5'] && $params['scode5']!=''){
			$where.=" and scode ='".$params['scode5']."'";
		}else if($params['scode4'] && $params['scode4']!=''){
			$where.=" and mcode ='".$params['scode4']."'";
		}else if($params['scode3'] && $params['scode3']!=''){
			$where .= " and mcode = '".$params['scode3']."'";
		}else if($params['scode2'] && $params['scode2']!=''){
			$where .= " and mcode = '".$params['scode2']."'";
		}else if($params['scode1']){
			if($params['scode1']=='null') $where .= " and mcode = ''";
			else $where .= " and mcode = '".$params['scode1']."'";
		}
		
		if($params['scode1']){
			$sql1="select scode,sname from dc_code_class where 1 and mcode='".$params['scode1']."' and Status=1 and mc_type='".$params['mc_type']."' order by sod asc ";
			$scode2list= $this->_dao->AQUERY($sql1);
			//$scode2list=htmlspecialchars_decode($scode2list);
		}
		if($params['scode2']){
			$sql2="select scode,sname from dc_code_class where 1 and mcode='".$params['scode2']."' and Status=1 and mc_type='".$params['mc_type']."' order by sod asc ";
			$scode3list= $this->_dao->AQUERY($sql2);
			//$scode3list=htmlspecialchars_decode($scode3list);
		}
		if($params['scode3']){
			$sql3="select scode,sname from dc_code_class where 1 and mcode='".$params['scode3']."' and Status=1 and mc_type='".$params['mc_type']."' order by sod asc ";
			$scode4list= $this->_dao->AQUERY($sql3);
		}
		if($params['scode4']){
			$sql4="select scode,sname from dc_code_class where 1 and mcode='".$params['scode4']."' and Status=1 and mc_type='".$params['mc_type']."' order by sod asc ";
			$scode5list= $this->_dao->AQUERY($sql4);
		}			
		
		
 		if($params['scode']){
			$where .= " and scode = '".$params['scode']."'";
		}
		//change hlf 07.13 start
		
		// end
		$total = $this -> _dao -> getOne("SELECT Count(*) as c FROM dc_code_class  WHERE 1 $where");
		
		$page = $params['page'] == '' ? 1 : $params['page'];
		$per = 15;
		if(isset($params['pageSize'])&&$params['pageSize']!='')$per= $params['pageSize'];
		$url = "admindata.php";
		unset($params['page']);
		$start = ($page - 1) * $per;
		/* 2017/07/26 change WHERE 1 $where by lvxh*/
		$sql = "select sname,scode,mcode ,mc_type from dc_code_class WHERE 1 and mc_type='".$params['mc_type']."'";//echo $sql;
		$all_all = $this->_dao->execute($sql);
		while (!$all_all->EOF){
			$mcode = $all_all->fields("mcode");
			$sname = $all_all->fields("sname");
			$scode = $all_all->fields("scode");
			
			/**/
			$mc_type=$all_all->fields("mc_type");
			/**/
			
			//if($mcode==""){ //update by zhangcun 2017/10/10
				$fclass[$scode]	= $sname;
			//}

		//	$class_all[$scode]	= $sname;
			$all_all->Movenext();
		}
		
		$sql="select * from dc_code_class where 1 $where order by mcode,sod,scode asc limit $start, $per";
		
		$all_class = $this->_dao->execute($sql);
			
		$classmes = array();
		while (!$all_class->EOF){
			$ID = $all_class->fields("ID");
			$mcode = $all_class->fields("mcode");
			$scode1 = $all_class->fields("mcode");
			$sname = $all_class->fields("sname");
			$sname_en = $all_class->fields("sname_en");
			$snameshort = $all_class->fields("snameshort");
			$snameshort_en = $all_class->fields("snameshort_en");
			$scode = $all_class->fields("scode");
			$sod = $all_class->fields("sod");
			$sdesc = $all_class->fields("sdesc");
			$CreateUser = $all_class->fields("CreateUser");
			$Status = $all_class->fields("Status");
			
			/**/
			$mc_type=$all_class->fields("mc_type");
			/**/
			
			$IsEnglish = $all_class->fields("IsEnglish");
			//echo $mc_type;
			/* if($mcode==""){
				$fclass[$scode]	= $sname;
			} */
			
		
			
			
			$classmes[] = array("ID"=>$ID,"mcode"=>$mcode,"scode1"=>$scode1,"scode2"=>$scode2,"mcodename"=>$fclass[$mcode],"sname"=>$sname,"sname_en"=>$sname_en,"snameshort"=>$snameshort,"snameshort_en"=>$snameshort_en,"scode"=>$scode,"sod"=>$sod,"sdesc"=>$sdesc,"CreateUser"=>$CreateUser,"Status"=>$Status,"mc_type"=>$mc_type,"IsEnglish"=>$IsEnglish);					
			
			$all_class->MoveNext();
		}

		$ll_class=array();
		foreach($classmes as $one){
			if($one['mcode']=="")$ll_class[]=$one;
		}
		//print"<pre>";print_r($fclass);print_r($classmes);

		foreach($classmes as $one){
			if($one['mcode'])$ll_class[]=$one;
		}
		$classmes=$ll_class;
		$this->assign( "params", $params );
		$this->assign( "scode2list", $scode2list );
		$this->assign( "scode3list", $scode3list );
		$this->assign( "scode4list", $scode4list );
		$this->assign( "scode5list", $scode5list );
		$this->assign( "fclass", $fclass );
		$this->assign( "classmes", $classmes );
		
		/* print_r($classmes);
		echo '<pre>'; */
		$this->assign( "scode1", $params['scode1'] );
		$pagebar = pagebar( $url, $params, $per, $page, $total );
		$this->assign( "pagebar", $pagebar );
		//print_r($classmes[0]);		
		
		$this->setvars($params);
		
	}
	//添加数据分类
	public function addclass($params){
		if(empty($params['mc_type'])) $mc_type=0;
		else $mc_type=$params['mc_type'];
		
		$this->assign("ddelpower",$_SESSION['ddelpower']);
		$sql="select sname,scode, mc_type from dc_code_class where mcode='' and mc_type=".$mc_type;
		$all_main = $this->_dao->query($sql);
		$this->assign( "all_main", $all_main );
		$this->assign( "mc_type", $mc_type );

		//获取当期目录id，并选择
		if(isset($params['curmenuid'])&&$params['curmenuid']!=''){
			$sql = "select * from dc_code_class where ID=".$params['curmenuid'];
			$onemes = $this->_dao->getrow($sql);
			$this->assign( "onemes", $onemes );
		}

		//print_r($all_main);
		//print_r($params);
		if($params['submit']=="提交信息"){
		//echo $params[mc_type];
			//判断 code 是否存在
			$isc = $this->_dao->getRow("select * from dc_code_class where scode = '".$params['scode']."' and mc_type='".$mc_type."'",0);
			if($isc){
				alert("代码已存在请重新填写");
				goback();exit;
			}

			$sql="insert into dc_code_class (mcode,sname,sname_en,snameshort,snameshort_en,IsEnglish,scode,sod,sdesc,Status,mc_type,CreateUser,CreateDate,CreateUserId,flag) 
			values ('$params[mcode]','$params[sname]','$params[sname_en]','$params[snameshort]','$params[snameshort_en]','".(int)$params['IsEnglish']."','$params[scode]','".(int)$params['sod']."','$params[sdesc]','".(int)$params['Status']."','$mc_type','$_SESSION[truename]',NOW(),'$_SESSION[duserid]','".(int)$params['flag']."')";
			
			$insert = $this->_dao->execute($sql);

			$this->writelog("1","新增数据目录".$params['sname'],$mc_type);
			echo "<script>alert('添加成功');window.location.href='admindata.php?view=classlist&mc_type=".$mc_type."'</script>";
			//print_r($this->_dao);
			
		}
		
	}
	//修改数据分类
	public function editclass($params){
		if(empty($params['mc_type'])) $mc_type=0;
		else $mc_type=$params['mc_type'];
		
		/*change start*/
			$this->assign("mc_type",$mc_type);
			//echo $mc_type;
		/*end */
		unset($GLOBALS['mc_type_list'][$mc_type]);
		$this->assign("mc_type_list",$GLOBALS['mc_type_list']);
		$sql="select sname,scode from dc_code_class where mcode='' and mc_type=".$mc_type;
		$all_main = $this->_dao->query($sql);
		$this->assign( "all_main", $all_main );
         if($params['submit']=="修改信息"){

			//判断 code 是否存在
			$isc = $this->_dao->getRow("select * from dc_code_class where scode = '".$params['scode']."' and mc_type='".$mc_type."'",0);
			if($isc && ($isc['ID'] !=$params['id']) ){
				alert("代码已存在请重新填写");
				goback();exit;
			}

			$prescode=$this->_dao->getone("select scode from dc_code_class where ID='$params[id]'");

         	$sql="update dc_code_class set sname='$params[sname]',sname_en='$params[sname_en]',snameshort='$params[snameshort]',snameshort_en='$params[snameshort_en]',IsEnglish='".(int)$params['IsEnglish']."',mcode='$params[mcode]',scode='$params[scode]',sod='".(int)$params['sod']."',sdesc='$params[sdesc]',UpdateDate=NOW(),UpdateDateUserId='$_SESSION[duserid]',Status='".(int)$params['Status']."',mc_type='$params[mc_type]',flag='".(int)$params['flag']."' where ID='$params[id]'";
			//alert($sql);
            $updata = $this->_dao->execute($sql);

			//add by zhangcun for 新增两级数据目录 2017/10/10
			$this->_dao->execute("update dc_code_class set mcode='$params[scode]' where mcode='$prescode' and mc_type='$params[mc_type]'");
			//add by zhangcun for 新增两级数据目录 2017/10/10

			$this->writelog("1","修改数据目录".$params['sname'],$params['mc_type']);
            
            echo "<script>alert('更新成功');window.location.href='admindata.php?view=classlist&mc_type=".$params['mc_type']."'</script>";
         }
		
		$sql = "select * from dc_code_class where ID=".$params['id'];
		$onemes = $this->_dao->getrow($sql);
		$this->assign( "onemes", $onemes );
		$this->assign( "params", $params );
		//print_r($onemes);
	}
	//数据分类删除
	public function delclass($params){
		$this->assign("mc_type",$params['mc_type']);
		if($params['id']){
		$sql="delete from dc_code_class where ID=".$params['id'];
		$del = $this->_dao->execute($sql);
		$this->writelog("1","删除数据目录".$params['sname'],$params['mc_type']);
		echo "<script>alert('删除成功');window.location.href='admindata.php?view=classlist&mc_type=".$params['mc_type']."'</script>";
		}
	}
	//数据类型列表
	public function datatypelist($params){
		$this->assign("params",$params);
		$where = "";
		
		
		/*change hlf 07.13 start*/
		$this->assign("mc_type",$params['mc_type']);
		/*end */
		
		if($params['dtname']){
			$where .= " and( dtname like '%".$params['dtname']."%'	or  dtname_en like '%".$params['dtname']."%')";
		}
		

		if($params['scode1']){
			$where .= " and scode1 = '".$params['scode1']."'";
		}
		if($params['scode2']){
			$where .= " and scode2 = '".$params['scode2']."'";
		}
		if($params['scode3']){
			$where.=" and scode3 ='".$params['scode3']."'";
		}
		if($params['scode4']){
			$where.=" and scode4 ='".$params['scode4']."'";
		}
		if($params['scode5']){
			$where.=" and scode5 ='".$params['scode5']."'";
		}
		if($params['isSubDatas']!=''){
			$where .= " and isSubDatas = '".$params['isSubDatas']."'";
		}
		
		if($params['scode1']){
			$sql1="select scode,sname from dc_code_class where 1 and mcode='".$params['scode1']."' and Status=1 and mc_type='".$params['mc_type']."' order by sod asc ";
			$scode2list= $this->_dao->AQUERY($sql1);
		}
		if($params['scode2']){
			$sql2="select scode,sname from dc_code_class where 1 and mcode='".$params['scode2']."' and Status=1 and mc_type='".$params['mc_type']."' order by sod asc ";
			$scode3list= $this->_dao->AQUERY($sql2);
		}
		if($params['scode3']){
			$sql3="select scode,sname from dc_code_class where 1 and mcode='".$params['scode3']."' and Status=1 and mc_type='".$params['mc_type']."' order by sod asc ";
			$scode4list= $this->_dao->AQUERY($sql3);
		}
		if($params['scode4']){
			$sql4="select scode,sname from dc_code_class where 1 and mcode='".$params['scode4']."' and Status=1 and mc_type='".$params['mc_type']."' order by sod asc ";
			$scode5list= $this->_dao->AQUERY($sql4);
		}		
		
		
		/*change hlf 07.13 start*/
		$where .=" and mc_type='".$params['mc_type']."'";
		/*end */
		
		$total = $this -> _dao -> getOne("SELECT Count(*) as c FROM dc_code_datatype  WHERE 1 $where");
		$page = $params['page'] == '' ? 1 : $params['page'];
		$per = 15;
		if(isset($params['pageSize'])&&$params['pageSize']!='')$per= $params['pageSize'];
		$url = "admindata.php";
		unset($params['page']);
		$start = ($page - 1) * $per;
		/* 2017/07/26 change  WHERE 1 $where by lvxh */
		$sql = "select scode,sname from dc_code_class where mc_type='".$params['mc_type']."'";
		$all_class = $this->_dao->execute($sql);
		while (!$all_class->EOF){			
			$sname = $all_class->fields("sname");
			$scode = $all_class->fields("scode");
			$class_all[$scode]	= $sname;
			$all_class->Movenext();
		}		
		$sql = "select * from dc_code_datatype where 1  $where order by scode1,scode2,scode3,scode4, dtod asc limit $start, $per";
		$all_var = $this->_dao->execute($sql);
		
		$varmes = array();
		while(!$all_var->EOF){
			$ID = $all_var->fields("ID");
			$scode1 = $all_var->fields("scode1");
			$scode2 = $all_var->fields("scode2");
			$scode3 = $all_var->fields("scode3");
			$scode4 = $all_var->fields("scode4");
			$scode5 = $all_var->fields("scode5");
			
			
			$dtname = $all_var->fields("dtname");
			$dtname_en = $all_var->fields("dtname_en");
			$CreateDate = $all_var->fields("CreateDate");			
			$Status = $all_var->fields("Status");
			$mc_type=$all_var->fields("mc_type");
			$dtod = $all_var->fields("dtod");	
			$IsEnglish = $all_var->fields("IsEnglish");		
			$varmes[] = array("ID"=>$ID,"scode1"=>$class_all[$scode1],"scode3"=>$class_all[$scode3],"scode4"=>$class_all[$scode4],"scode5"=>$class_all[$scode5],"Status"=>$Status,"mc_type"=>$mc_type,"scode2"=>$class_all[$scode2],"dtname"=>$dtname,"dtname_en"=>$dtname_en,"CreateDate"=>$CreateDate,"dtod"=>$dtod,"IsEnglish"=>$IsEnglish);				
			$all_var->MoveNext();
		}
		$this->assign( "varmes", $varmes );
		
		$pagebar = pagebar( $url, $params, $per, $page, $total );
		$this->assign( "pagebar", $pagebar );
		$this->assign("params",$params);
		// unset($GLOBALS['mc_type_list'][$mc_type]);
		$this->assign("mc_type_list",$GLOBALS['mc_type_list']);
		$this->assign( "scode2list", $scode2list );
		$this->assign( "scode3list", $scode3list );
		$this->assign( "scode4list", $scode4list );
		$this->assign( "scode5list", $scode5list );
		$this->setvars($params);
		$this->assign("ddelpower",$_SESSION['ddelpower']);

	}
	//数据类型列表删除
	public function delvar($params){
		$this->assign("mc_type",$params['mc_type']);
		if($params['id']){
			$sql="delete from dc_code_datatype where ID=".$params['id'];
			$del = $this->_dao->execute($sql);
			$this->writelog("2","删除数据类型".$params['dtname'],$params['mc_type']);
			echo "<script>alert('删除成功');window.location.href='admindata.php?view=datatypelist&mc_type=".$params['mc_type']."'</script>";
		}	
	}
	
	function split2($n) {
		$n |= 0;
		$pad = 0;
		$arr = array();
			while ($n) {
			if ($n & 1) array_push($arr, 1 << $pad);
			$pad++;
			$n >>= 1;
			}
		return $arr;
	}
	
	//数据类型修改
	public function edit_datatype($params){

		$this->assign("mc_type",$params['mc_type']);
		if($params['submit']=="修改信息"){
			$startdate=$params['startdateYear']."-".$params['startdateMonth']."-".$params['startdateDay'];
			$enddata=$params['enddataYear']."-".$params['enddataMonth']."-".$params['enddataDay'];
			$params['techsqlmain']=htmlspecialchars_decode($params['techsqlmain']);
			$params['dtname']=htmlspecialchars_decode($params['dtname']);
			$params['dtname_en']=htmlspecialchars_decode($params['dtname_en']);
			$params['data_releasetime'] = date('Y-m-d', strtotime($params['data_releasetime']));
			$params['techtablename']=htmlspecialchars_decode($params['techtablename']);

			$array = array( "scode1","scode2","scode3","scode4","scode5","dtname","dtname_en","dtymd","dtcomparetype","isSubDatas","subDatasDb","isMarkets","Data1Type","isData2","Data2Type","isData3","Data3Type","isData4","Data4Type","isData5","Data5Type","isData6","Data6Type","isData7","Data7Type","isData8","Data8Type","isData9","Data9Type","isData10","Data10Type","isData11","Data11Type","isData12","Data12Type","isData13","Data13Type","isData14","Data14Type","isData15","Data15Type","isData16","Data16Type","isData17","Data17Type","isData18","Data18Type","isData19","Data19Type","isData20","Data20Type","isPreData","isAddSubData","isAddSubHundCore","enddataisCurrent","dtod","dtdesc","dtdesc_en","techdbname","techsqlmain","techtabletype","techtableGroupBy","techtablename","techtablename2","Status","IsEnglish","Data1UnitType","Data1UnitConv","Data2UnitType","Data2UnitConv","Data3UnitType","Data3UnitConv","Data4UnitType","Data4UnitConv","Data5UnitType","Data5UnitConv","Data6UnitType","Data6UnitConv","Data7UnitType","Data7UnitConv","Data8UnitType","Data8UnitConv","Data9UnitType","Data9UnitConv","Data10UnitType","Data10UnitConv","Data11UnitType","Data11UnitConv","Data12UnitType","Data12UnitConv","Data13UnitType","Data13UnitConv","Data14UnitType","Data14UnitConv","Data15UnitType","Data15UnitConv","Data16UnitType","Data16UnitConv","Data17UnitType","Data17UnitConv","Data18UnitType","Data18UnitConv","Data19UnitType","Data19UnitConv","Data20UnitType","Data20UnitConv","dbaseType","subDatasTitle","subAtt1Db","subAtt2Db","subAtt3Db","subDatasDb2","UnitTypeName","UnitTypeName2","UnitTypeName3","UnitTypeName4","UnitTypeName5","UnitTypeName6","UnitTypeName7","UnitTypeName8","UnitTypeName9","UnitTypeName10","UnitTypeName11","UnitTypeName12","UnitTypeName13","UnitTypeName14","UnitTypeName15","UnitTypeName16","UnitTypeName17","UnitTypeName18","UnitTypeName19","UnitTypeName20","subDatasTitle_en","subAtt1Db_en","subAtt2Db_en","subAtt3Db_en","UnitTypeName_en","UnitTypeName2_en","UnitTypeName3_en","UnitTypeName4_en","UnitTypeName5_en","UnitTypeName6_en","UnitTypeName7_en","UnitTypeName8_en","UnitTypeName9_en","UnitTypeName10_en","UnitTypeName11_en","UnitTypeName12_en","UnitTypeName13_en","UnitTypeName14_en","UnitTypeName15_en","UnitTypeName16_en","UnitTypeName17_en","UnitTypeName18_en","UnitTypeName19_en","UnitTypeName20_en","TargetFlagVar","InterfaceUrl","IsGetNew","FieldName1","FieldName2","FieldName3","data_source_id","data_releasetime","data_source","npredata_UnitConv","npredata_UnitType","naddsubdata_UnitConv","naddsubdata_UnitType","daddsubhundcore_UnitConv","daddsubhundcore_UnitType","npredataDW_en","naddsubdataDW_en","naddsubhundcoreDW_en");
			//FieldName1,FieldName2,FieldName3 分别代表 代码字段名、开始日期字段名、结束日期字段名
			
			$data = $this->getdata( $array, $params);
			$pinzhong1=$_POST['pinzhong'];
			
			$sum=0;
			foreach ($pinzhong1 as $v){
			$sum+=(int)$v;
			}
			
			

			$sql = "update  dc_code_datatype set $data ,pinzhong='$sum',startdate='$startdate',enddata='$enddata',CreateUser='$_SESSION[truename]',CreateDate=NOW(),CreateUserId='$_SESSION[duserid]',topicture='$params[topicture]',mastertopid='$params[mastertopid]'  where id='".$params['id']."'";
			$up_var = $this->_dao->execute($sql);
			if($params['update_earliest_date']){
				if($params['ddate']!=""&&($params['techtablename']!=""||$params['techtablename2']!="")&&$params['techsqlmain']){
					if($params['techtablename']!=""){
						$earliest_date_sql = "select ".$_POST['ddate']." from ".$_POST['techtablename']." ".$_POST['techsqlmain']." and ".$_POST['ddate']."!='0000-00-00 00:00:00' and ".$_POST['ddate']."!='0000-00-00' and ".$_POST['ddate'].">='1900-01-01 00:00:00' order by ".$_POST['ddate']." asc limit 1";
					
						$earliest_date_sql = stripcslashes($earliest_date_sql);
						if($params['techdbname']==1){  //数据中心数据库
							$res_date = $this->_dao->getone($earliest_date_sql);
						} elseif($params['techdbname']==2) {  //研究中心数据库
							$res_date = $this->drc->getone($earliest_date_sql);
						} elseif($params['techdbname']==3) {  //钢厂数据库
							$res_date = $this->gc->getone($earliest_date_sql);
						} elseif($params['techdbname']==5) {  //钢之家主数据库
							$res_date = $this->stedao->getone($earliest_date_sql);
						// } elseif($params['techdbname']==6) {
							
						}
					}else if($params['techtablename2']!=""){
						$earliest_date_sql = $_POST['techtablename2']." ".$_POST['techsqlmain']." and ".$_POST['ddate']."!='0000-00-00 00:00:00' and ".$_POST['ddate']."!='0000-00-00' and ".$_POST['ddate'].">='1900-01-01 00:00:00' order by ".$_POST['ddate']." asc limit 1";
						$earliest_date_sql = stripcslashes($earliest_date_sql);
						if($params['techdbname']==1){  //数据中心数据库
							$res_date = $this->_dao->getone($earliest_date_sql);
						} elseif($params['techdbname']==2) {  //研究中心数据库
							$res_date = $this->drc->getone($earliest_date_sql);
						} elseif($params['techdbname']==3) {  //钢厂数据库
							$res_date = $this->gc->getone($earliest_date_sql);
						} elseif($params['techdbname']==5) {  //钢之家主数据库
							$res_date = $this->stedao->getone($earliest_date_sql);
						// } elseif($params['techdbname']==6) {
							
						}
					}
				}
				//$res_date = date('Y-m-d', strtotime($res_date));
				$this->_dao->execute("update dc_code_datatype set data_releasetime='$res_date' where id='".$params['id']."'");
			}
			$sql1 = "select count(*) from dc_code_datatype_db where DID='".$params['id']."' and mc_type='".$params['mc_type']."'";
			$num = $this->_dao->getOne($sql1);
			if($num){
				$sql_uptec = "update dc_code_datatype_db set ndate='$params[ndate]',nmarket='$params[nmarket]',ndata1='$params[ndata1]',ndata2='$params[ndata2]',ndata3='$params[ndata3]',ndata4='$params[ndata4]',ndata5='$params[ndata5]',ndata6='$params[ndata6]',ndata7='$params[ndata7]',ndata8='$params[ndata8]',ndata9='$params[ndata9]',ndata10='$params[ndata10]',ndata11='$params[ndata11]',ndata12='$params[ndata12]',ndata13='$params[ndata13]',ndata14='$params[ndata14]',ndata15='$params[ndata15]',ndata16='$params[ndata16]',ndata17='$params[ndata17]',ndata18='$params[ndata18]',ndata19='$params[ndata19]',ndata20='$params[ndata20]',ncompare='$params[ncompare]',npredata='$params[npredata]',naddsubdata='$params[naddsubdata]',naddsubhundcore='$params[naddsubhundcore]',ndate_en='$params[ndate_en]',nmarket_en='$params[nmarket_en]',ndata1_en='$params[ndata1_en]',ndata2_en='$params[ndata2_en]',ndata3_en='$params[ndata3_en]',ndata4_en='$params[ndata4_en]',ndata5_en='$params[ndata5_en]',ndata6_en='$params[ndata6_en]',ndata7_en='$params[ndata7_en]',ndata8_en='$params[ndata8_en]',ndata9_en='$params[ndata9_en]',ndata10_en='$params[ndata10_en]',ndata11_en='$params[ndata11_en]',ndata12_en='$params[ndata12_en]',ndata13_en='$params[ndata13_en]',ndata14_en='$params[ndata14_en]',ndata15_en='$params[ndata15_en]',ndata16_en='$params[ndata16_en]',ndata17_en='$params[ndata17_en]',ndata18_en='$params[ndata18_en]',ndata19_en='$params[ndata19_en]',ndata20_en='$params[ndata20_en]',npredata_en='$params[npredata_en]',naddsubdata_en='$params[naddsubdata_en]',naddsubhundcore_en='$params[naddsubhundcore_en]',npredataDW='$params[npredata_unit]',naddsubdataDW='$params[naddsubdata_unit]',naddsubhundcoreDW='$params[daddsubhundcore_unit]',ddate='$params[ddate]',
				dmarket='$params[dmarket]',Ddata1='$params[Ddata1]',Ddata2='$params[Ddata2]',Ddata3='$params[Ddata3]',Ddata4='$params[Ddata4]',Ddata5='$params[Ddata5]',Ddata6='$params[Ddata6]',Ddata7='$params[Ddata7]',Ddata8='$params[Ddata8]',Ddata9='$params[Ddata9]',Ddata10='$params[Ddata10]',Ddata11='$params[Ddata11]',Ddata12='$params[Ddata12]',Ddata13='$params[Ddata13]',Ddata14='$params[Ddata14]',Ddata15='$params[Ddata15]',Ddata16='$params[Ddata16]',Ddata17='$params[Ddata17]',Ddata18='$params[Ddata18]',Ddata19='$params[Ddata19]',Ddata20='$params[Ddata20]',dcompare='$params[dcompare]',dpredata='$params[dpredata]',daddsubdata='$params[daddsubdata]',daddsubhundcore='$params[daddsubhundcore]',UpdateDate=NOW(),UpdateUserId='$_SESSION[duserid]' where DID='$params[id]' and mc_type='".$params['mc_type']."'";
				$up_tec = $this->_dao->execute($sql_uptec);
			}else{
				$sql_tec="insert into dc_code_datatype_db (DID,ndate,nmarket,ndata1,ndata2,ndata3,ndata4,ndata5,ndata6,ndata7,ndata8,ndata9,ndata10,ndata11,ndata12,ndata13,ndata14,ndata15,ndata16,ndata17,ndata18,ndata19,ndata20,ncompare,npredata,naddsubdata,naddsubhundcore,ddate,dmarket,Ddata1,Ddata2,Ddata3,Ddata4,Ddata5,Ddata6,Ddata7,Ddata8,Ddata9,Ddata10,Ddata11,Ddata12,Ddata13,Ddata14,Ddata15,Ddata16,Ddata17,Ddata18,Ddata19,Ddata20,dcompare,dpredata,daddsubdata,daddsubhundcore,UpdateDate,UpdateUserId,ndate_en,nmarket_en,ndata1_en,ndata2_en,ndata3_en,ndata4_en,ndata5_en,ndata6_en,ndata7_en,ndata8_en,ndata9_en,ndata10_en,ndata11_en,ndata12_en,ndata13_en,ndata14_en,ndata15_en,ndata16_en,ndata17_en,ndata18_en,ndata19_en,ndata20_en,npredata_en,naddsubdata_en,naddsubhundcore_en,npredataDW,naddsubdataDW,naddsubhundcoreDW,mc_type)
				values ('$params[id]','$params[ndate]','$params[nmarket]','$params[ndata1]','$params[ndata2]','$params[ndata3]','$params[ndata4]','$params[ndata5]','$params[ndata6]','$params[ndata7]','$params[ndata8]','$params[ndata9]','$params[ndata10]','$params[ndata11]','$params[ndata12]','$params[ndata13]','$params[ndata14]','$params[ndata15]','$params[ndata16]','$params[ndata17]','$params[ndata18]','$params[ndata19]','$params[ndata20]','$params[ncompare]','$params[npredata]','$params[naddsubdata]','$params[naddsubhundcore]','$params[ddate]','$params[dmarket]',
				'$params[Ddata1]','$params[Ddata2]','$params[Ddata3]','$params[Ddata4]','$params[Ddata5]','$params[Ddata6]','$params[Ddata7]','$params[Ddata8]','$params[Ddata9]','$params[Ddata10]','$params[Ddata11]','$params[Ddata12]','$params[Ddata13]','$params[Ddata14]','$params[Ddata15]','$params[Ddata16]','$params[Ddata17]','$params[Ddata18]','$params[Ddata19]','$params[Ddata20]','$params[dcompare]','$params[dpredata]','$params[daddsubdata]','$params[daddsubhundcore]',NOW(),'$_SESSION[duserid]','$params[ndate_en]','$params[nmarket_en]','$params[ndata1_en]','$params[ndata2_en]','$params[ndata3_en]','$params[ndata4_en]','$params[ndata5_en]','$params[ndata6_en]','$params[ndata7_en]','$params[ndata8_en]','$params[ndata9_en]','$params[ndata10_en]','$params[ndata11_en]','$params[ndata12_en]','$params[ndata13_en]','$params[ndata14_en]','$params[ndata15_en]','$params[ndata16_en]','$params[ndata17_en]','$params[ndata18_en]','$params[ndata19_en]','$params[ndata20_en]','$params[npredata_en]','$params[naddsubdata_en]','$params[naddsubhundcore_en]','$params[npredata_unit]','$params[naddsubdata_unit]','$params[daddsubhundcore_unit]','$params[mc_type]')";
				$add_tec = $this->_dao->execute($sql_tec);
			}

			$this->writelog("2","修改数据类型".$params['dtname'],$params['mc_type']);
			$update_earliest_date = $params['update_earliest_date'];
			echo "<script>alert('更新成功');window.location.href='admindata.php?view=datatypelist&mc_type=".$params['mc_type']."'</script>";
			 exit;
		}
		$sql="select * from dc_code_class where Status=1 and mc_type='".$params['mc_type']."' order by sod asc ";
		$all_class = $this->_dao->execute($sql);
		while (!$all_class->EOF){
			$mcode = $all_class->fields("mcode");
			$sname = $all_class->fields("sname");
			$scode = $all_class->fields("scode");
		
			if($mcode==""){
				$fclass[$scode]	= $sname;//一级类
			}else{
				$code_par[$scode] = $mcode;
				$second[$scode] = $sname;//二级类
			}
			$this->assign( "code_par", $code_par );
			$this->assign( "fclass", $fclass );
			$this->assign( "second", $second );
			$all_class->MoveNext();
		}
		$sql = "select * from dc_code_datatype  where  ID=".$params['id'];	
		
		$var_mes = $this->_dao->getrow($sql);
		$pinzhong=$this->split2($var_mes['pinzhong']);
		$num=count($pinzhong);
		
		if(($num=='21' && !in_array(4194304,$pinzhong)) || $num=='22'){
			$all='3';
		}



		$var_mes['dataAquireType']=0;
		$DataAquireType=$this->stedao->getOne("select id from DataAquireType where DcTypeID=".$params['id']);
		if(!empty($DataAquireType)){
			$var_mes['dataAquireType']=1;
		}
		
		$sql_tec = "select * from dc_code_datatype_db  where  DID='".$params['id']."' and mc_type='".$params['mc_type']."'";
		$tec_mes = $this->_dao->getrow($sql_tec);
		$sql="select * from dc_code_datasources";
		$datasource = $this->_dao->query($sql);
		$this->assign( "datasource", $datasource );
		$this->assign( "var_mes", $var_mes );
		$this->assign( "tec_mes", $tec_mes );
		$this->assign( "pinzhong", $pinzhong );
		$this->assign( "all", $all );
		$this->assign( "datetype", $GLOBALS['datetype'] );
		$this->assign( "hb", $GLOBALS['hb'] );
		$this->assign( "pictype", $GLOBALS['pictype'] );
		//$sql = "update dc_code_datatype set ";


		$childtype = $this->_dao->query("select * from dc_code_datatype_subs where DID = '".$params['id']."' and mc_type='".$params['mc_type']."' order by aod ");
		$this->assign("childtype",$childtype);


		$childrow = $this->_dao->getRow("select * from dc_code_datatype_subs where ID = '".$params['cid']."' limit 1");
		$this->assign("childrow",$childrow);

		$this->assign("params",$params);

		//$this->assign("power",$_SESSION['power']);
		$this->assign("ddelpower",$_SESSION['ddelpower']);
		$this->setvars($params);

		$ntd = 0;
		if($var_mes['subAtt1Db']){
			$ntd++;
		}
		if($var_mes['subAtt2Db']){
			$ntd++;
		}
		if($var_mes['subAtt3Db']){
			$ntd++;
		}

		$this->assign("ntd",$ntd);
		$this->assign( "mc_type", $params['mc_type'] );
	
	}
	
		
	
	//增加数据类型
	public function add_datatype($params){
			//echo "<pre>";
			//print_r($params);
		
			/*$name=array("焦炭","焦炭成本","华东地区（SHCKI-E）","华北地区（SHCKI-N）","东北地区（SHCKI-NE）","中南地区（SHCKI-CS）","西南地区（SHCKI-WS）","西北地区（SHCKI-WN）","山西地区（SHCKI-SX）","河北地区（SHCKI-HB）","山东地区（SHCKI-SD）");
			$ename=array("SHCKI","SHCKI-COST","East China（SHCKI-E）","North China（SHCKI-N）","Northeast China（SHCKI-NE）","Central South China（SHCKI-CS）","Southwest China（SHCKI-WS）","Northwest China（SHCKI-WN）","Shanxi Province（SHCKI-SX）","Hebei Province（SHCKI-HB）","Shandong Province（SHCKI-SD）");
			$shpi_material=array("3","34","36","37","38","39","40","41","43","42","44");
			foreach($name as $key=>$val){
				$sql="insert into dc_code_datatype set scode1='A',scode2='A03',scode3='A03_B',scode4='',scode5='',dtname='$val',dtname_en='$ename[$key]',dtymd='6',dtcomparetype='',isSubDatas='',subDatasDb='topicture',isMarkets='',Data1Type='2',isData2='1',Data2Type='2',isData3='',Data3Type='',isData4='',Data4Type='',isData5='',Data5Type='',isData6='1',Data6Type='2',isData7='1',Data7Type='2',isData8='1',Data8Type='2',isData9='1',Data9Type='2',isData10='1',Data10Type='2',isData11='1',Data11Type='2',isData12='1',Data12Type='2',isData13='1',Data13Type='2',isData14='1',Data14Type='2',isData15='1',Data15Type='2',isData16='1',Data16Type='2',isData17='1',Data17Type='2',isData18='',Data18Type='',isData19='',Data19Type='',isData20='',Data20Type='',isPreData='',isAddSubData='',isAddSubHundCore='',enddataisCurrent='1',dtod='$key',dtdesc='',dtdesc_en='',techdbname='5',techtabletype='',techtableGroupBy='',techtablename='shpi_material',techsqlmain='WHERE 1 and topicture=\'$shpi_material[$key]\'',Status='1',mc_type='0',IsEnglish='1',Data1UnitType='0',Data1UnitConv='',Data2UnitType='0',Data2UnitConv='',Data3UnitType='',Data3UnitConv='',Data4UnitType='',Data4UnitConv='',Data5UnitType='',Data5UnitConv='',Data6UnitType='',Data6UnitConv='',Data7UnitType='0',Data7UnitConv='',Data8UnitType='0',Data8UnitConv='',Data9UnitType='0',Data9UnitConv='',Data10UnitType='0',Data10UnitConv='',Data11UnitType='0',Data11UnitConv='',Data12UnitType='0',Data12UnitConv='',Data13UnitType='0',Data13UnitConv='',Data14UnitType='0',Data14UnitConv='',Data15UnitType='0',Data15UnitConv='',Data16UnitType='0',Data16UnitConv='',Data17UnitType='0',Data17UnitConv='',Data18UnitType='',Data18UnitConv='',Data19UnitType='',Data19UnitConv='',Data20UnitType='',Data20UnitConv='',dbaseType='',subDatasTitle='',subAtt1Db='',subAtt2Db='',subAtt3Db='',subDatasDb2='',UnitTypeName='点',UnitTypeName2='元/吨',UnitTypeName3='',UnitTypeName4='',UnitTypeName5='',UnitTypeName6='点',UnitTypeName7='点',UnitTypeName8='点',UnitTypeName9='点',UnitTypeName10='点',UnitTypeName11='点',UnitTypeName12='元/吨',UnitTypeName13='元/吨',UnitTypeName14='元/吨',UnitTypeName15='元/吨',UnitTypeName16='元/吨',UnitTypeName17='元/吨',UnitTypeName18='',UnitTypeName19='',UnitTypeName20='',subDatasTitle_en='',subAtt1Db_en='',subAtt2Db_en='',subAtt3Db_en='',UnitTypeName_en='Point',UnitTypeName2_en='yuan/tonne',UnitTypeName3_en='',UnitTypeName4_en='',UnitTypeName5_en='',UnitTypeName6_en='Point',UnitTypeName7_en='Point',UnitTypeName8_en='Point',UnitTypeName9_en='Point',UnitTypeName10_en='Point',UnitTypeName11_en='Point',UnitTypeName12_en='yuan/tonne',UnitTypeName13_en='yuan/tonne',UnitTypeName14_en='yuan/tonne',UnitTypeName15_en='yuan/tonne',UnitTypeName16_en='yuan/tonne',UnitTypeName17_en='yuan/tonne',UnitTypeName18_en='',UnitTypeName19_en='',UnitTypeName20_en='',TargetFlagVar='',InterfaceUrl='',IsGetNew='',FieldName1='',FieldName2='',FieldName3='' ,pinzhong='1',startdate='2020-04-26',enddata='2020-04-26',CreateUser='施桃弟',CreateDate=NOW(),CreateUserId='452460'";
				$add_one = $this->_dao->execute($sql);
				$id=$this->_dao->insert_id();
				if($id){
					$sql_tec="insert into dc_code_datatype_db (DID,ndate,nmarket,ndata1,ndata2,ndata3,ndata4,ndata5,ndata6,ndata7,ndata8,ndata9,ndata10,ndata11,ndata12,ndata13,ndata14,ndata15,ndata16,ndata17,ndata18,ndata19,ndata20,ncompare,npredata,naddsubdata,naddsubhundcore,ddate,dmarket,Ddata1,Ddata2,Ddata3,Ddata4,Ddata5,Ddata6,Ddata7,Ddata8,Ddata9,Ddata10,Ddata11,Ddata12,Ddata13,Ddata14,Ddata15,Ddata16,Ddata17,Ddata18,Ddata19,Ddata20,dcompare,dpredata,daddsubdata,daddsubhundcore,UpdateDate,UpdateUserId,ndate_en,nmarket_en,ndata1_en,ndata2_en,ndata3_en,ndata4_en,ndata5_en,ndata6_en,ndata7_en,ndata8_en,ndata9_en,ndata10_en,ndata11_en,ndata12_en,ndata13_en,ndata14_en,ndata15_en,ndata16_en,ndata17_en,ndata18_en,ndata19_en,ndata20_en,npredata_en,naddsubdata_en,naddsubhundcore_en,npredataDW,naddsubdataDW,naddsubhundcoreDW,mc_type) values ('$id','日期','','指数（点）','指数（价格）','','','','5日均线（指数）','10日均线（指数）','20日均线（指数）','40日均线（指数）','60日均线（指数）','200日均线（指数）','5日均线（价格）','10日均线（价格）','20日均线（价格）','40日均线（价格）','60日均线（价格）','200日均线（价格）','','','','','','','','dateday','', 'mindex','price','','','','mindex_avg_5','mindex_avg_10','mindex_avg_20','mindex_avg_40','mindex_avg_60','mindex_avg_200','price_avg_5','price_avg_10','price_avg_20','price_avg_40','price_avg_60','price_avg_200','','','','','','','',NOW(),'391935','Date','','Index (Point )','Index (Price)','','','','5 Days Average (Index)','10 Days Average (Index)','20 Days Average (Index)','40 Days Average (Index)','60 Days Average (Index)','200 Days Average (Index)','5 Days Average (Price)','10 Days Average (Price)','20 Days Average (Price)','40 Days Average (Price)','60 Days Average (Price)','200 Days Average (Price)','','','','','','','','','','0')";
				}
				$this->_dao->execute($sql_tec);	
			}*/



			/*change hlf 07.13 start*/
			$this->assign("mc_type",$params['mc_type']);
				$this->assign("ddelpower",$_SESSION['ddelpower']);
		$this->assign("dviewpower",$_SESSION['dviewpower']);
		$this->assign("rq1", date("Y-m-d"));
		if($params['submit']=="提交信息"&&$params['dtname']!=""){
			$startdate=$params['startdateYear']."-".$params['startdateMonth']."-".$params['startdateDay']; //预览开始结束时间
			$enddata=$params['enddataYear']."-".$params['enddataMonth']."-".$params['enddataDay'];
				//$stdate=$params['stdateYear']."-".$params['stdateMonth']."-".$params['stdateDay'];	//南钢等 取数据接口开始结束时间
				//$eddate=$params['eddateYear']."-".$params['eddateMonth']."-".$params['eddateDay'];
				//change by std 2019/3/11 end
				$params['dtname']=htmlspecialchars_decode($params['dtname']);
				$params['dtname_en']=htmlspecialchars_decode($params['dtname_en']);
				$params['techsqlmain']=htmlspecialchars_decode($params['techsqlmain']);
				$params['data_releasetime'] = date('Y-m-d', strtotime($params['data_releasetime']));
				$array = array( "scode1","scode2","scode3","scode4","scode5","dtname","dtname_en","dtymd","dtcomparetype","isSubDatas","subDatasDb","isMarkets","Data1Type","isData2","Data2Type","isData3","Data3Type","isData4","Data4Type","isData5","Data5Type","isData6","Data6Type","isData7","Data7Type","isData8","Data8Type","isData9","Data9Type","isData10","Data10Type","isData11","Data11Type","isData12","Data12Type","isData13","Data13Type","isData14","Data14Type","isData15","Data15Type","isData16","Data16Type","isData17","Data17Type","isData18","Data18Type","isData19","Data19Type","isData20","Data20Type","isPreData","isAddSubData","isAddSubHundCore","enddataisCurrent","dtod","dtdesc","dtdesc_en","techdbname","techtabletype","techtableGroupBy","techtablename","techtablename2","techsqlmain","Status","mc_type","IsEnglish","Data1UnitType","Data1UnitConv","Data2UnitType","Data2UnitConv","Data3UnitType","Data3UnitConv","Data4UnitType","Data4UnitConv","Data5UnitType","Data5UnitConv","Data6UnitType","Data6UnitConv","Data7UnitType","Data7UnitConv","Data8UnitType","Data8UnitConv","Data9UnitType","Data9UnitConv","Data10UnitType","Data10UnitConv","Data11UnitType","Data11UnitConv","Data12UnitType","Data12UnitConv","Data13UnitType","Data13UnitConv","Data14UnitType","Data14UnitConv","Data15UnitType","Data15UnitConv","Data16UnitType","Data16UnitConv","Data17UnitType","Data17UnitConv","Data18UnitType","Data18UnitConv","Data19UnitType","Data19UnitConv","Data20UnitType","Data20UnitConv","dbaseType","subDatasTitle","subAtt1Db","subAtt2Db","subAtt3Db","subDatasDb2","UnitTypeName","UnitTypeName2","UnitTypeName3","UnitTypeName4","UnitTypeName5","UnitTypeName6","UnitTypeName7","UnitTypeName8","UnitTypeName9","UnitTypeName10","UnitTypeName11","UnitTypeName12","UnitTypeName13","UnitTypeName14","UnitTypeName15","UnitTypeName16","UnitTypeName17","UnitTypeName18","UnitTypeName19","UnitTypeName20","subDatasTitle_en","subAtt1Db_en","subAtt2Db_en","subAtt3Db_en","UnitTypeName_en","UnitTypeName2_en","UnitTypeName3_en","UnitTypeName4_en","UnitTypeName5_en","UnitTypeName6_en","UnitTypeName7_en","UnitTypeName8_en","UnitTypeName9_en","UnitTypeName10_en","UnitTypeName11_en","UnitTypeName12_en","UnitTypeName13_en","UnitTypeName14_en","UnitTypeName15_en","UnitTypeName16_en","UnitTypeName17_en","UnitTypeName18_en","UnitTypeName19_en","UnitTypeName20_en","TargetFlagVar","InterfaceUrl","IsGetNew","FieldName1","FieldName2","FieldName3","data_source_id","data_releasetime","data_source","npredata_UnitConv","npredata_UnitType","naddsubdata_UnitConv","naddsubdata_UnitType","daddsubhundcore_UnitConv","daddsubhundcore_UnitType","npredataDW_en","naddsubdataDW_en","naddsubhundcoreDW_en");
					//FieldName1,FieldName2,FieldName3 分别代表 代码字段名、开始日期字段名、结束日期字段名

				$data = $this->getdata( $array, $params );
				$pinzhong=$_POST['pinzhong'];
				$sum=0;
				foreach ($pinzhong as $v){
				$sum+=(int)$v;
				}
				
				$sql = "insert into dc_code_datatype set $data ,pinzhong='$sum',startdate='$startdate',enddata='$enddata',CreateUser='$_SESSION[truename]',CreateDate=NOW(),CreateUserId='$_SESSION[duserid]',topicture='$params[topicture]',mastertopid='$params[mastertopid]'";
				//echo $sql;
				$add_one = $this->_dao->execute($sql);
				$id=$this->_dao->insert_id();
				if($params['update_earliest_date']){
					if($params['ddate']!=""&&($params['techtablename']!=""||$params['techtablename2']!="")&&$params['techsqlmain']){
						if($params['techtablename']!=""){
							$earliest_date_sql = "select ".$_POST['ddate']." from ".$_POST['techtablename']." ".$_POST['techsqlmain']." and ".$_POST['ddate']."!='0000-00-00 00:00:00' and ".$_POST['ddate']."!='0000-00-00' and ".$_POST['ddate'].">='1900-01-01 00:00:00' order by ".$_POST['ddate']." asc limit 1";
							$earliest_date_sql = stripcslashes($earliest_date_sql);
							if($params['techdbname']==1){  //数据中心数据库
								$res_date = $this->_dao->getone($earliest_date_sql);
							} elseif($params['techdbname']==2) {  //研究中心数据库
								$res_date = $this->drc->getone($earliest_date_sql);
							} elseif($params['techdbname']==3) {  //钢厂数据库
								$res_date = $this->gc->getone($earliest_date_sql);
							} elseif($params['techdbname']==5) {  //钢之家主数据库
								$res_date = $this->stedao->getone($earliest_date_sql);
							// } elseif($params['techdbname']==6) {
								
							}
						}else if($params['techtablename2']!=""){
							$earliest_date_sql = $_POST['techtablename']." ".$_POST['techsqlmain']." and ".$_POST['ddate']."!='0000-00-00 00:00:00' and ".$_POST['ddate']."!='0000-00-00' and ".$_POST['ddate'].">='1900-01-01 00:00:00' order by ".$_POST['ddate']." asc limit 1";
							$earliest_date_sql = stripcslashes($earliest_date_sql);
							if($params['techdbname']==1){  //数据中心数据库
								$res_date = $this->_dao->getone($earliest_date_sql);
							} elseif($params['techdbname']==2) {  //研究中心数据库
								$res_date = $this->drc->getone($earliest_date_sql);
							} elseif($params['techdbname']==3) {  //钢厂数据库
								$res_date = $this->gc->getone($earliest_date_sql);
							} elseif($params['techdbname']==5) {  //钢之家主数据库
								$res_date = $this->stedao->getone($earliest_date_sql);
							// } elseif($params['techdbname']==6) {
								
							}
						}
					}
					
					//$res_date = date('Y-m-d', strtotime($res_date));
					$this->_dao->execute("update dc_code_datatype set data_releasetime='$res_date' where id='$id'");
				}
				if($id){
					$sql_tec="insert into dc_code_datatype_db (DID,ndate,nmarket,ndata1,ndata2,ndata3,ndata4,ndata5,ndata6,ndata7,ndata8,ndata9,ndata10,ndata11,ndata12,ndata13,ndata14,ndata15,ndata16,ndata17,ndata18,ndata19,ndata20,ncompare,npredata,naddsubdata,naddsubhundcore,ddate,dmarket,Ddata1,Ddata2,Ddata3,Ddata4,Ddata5,Ddata6,Ddata7,Ddata8,Ddata9,Ddata10,Ddata11,Ddata12,Ddata13,Ddata14,Ddata15,Ddata16,Ddata17,Ddata18,Ddata19,Ddata20,dcompare,dpredata,daddsubdata,daddsubhundcore,UpdateDate,UpdateUserId,ndate_en,nmarket_en,ndata1_en,ndata2_en,ndata3_en,ndata4_en,ndata5_en,ndata6_en,ndata7_en,ndata8_en,ndata9_en,ndata10_en,ndata11_en,ndata12_en,ndata13_en,ndata14_en,ndata15_en,ndata16_en,ndata17_en,ndata18_en,ndata19_en,ndata20_en,npredata_en,naddsubdata_en,naddsubhundcore_en,npredataDW,naddsubdataDW,naddsubhundcoreDW,mc_type)
					values ('$id','$params[ndate]','$params[nmarket]','$params[ndata1]','$params[ndata2]','$params[ndata3]','$params[ndata4]','$params[ndata5]','$params[ndata6]','$params[ndata7]','$params[ndata8]','$params[ndata9]','$params[ndata10]','$params[ndata11]','$params[ndata12]','$params[ndata13]','$params[ndata14]','$params[ndata15]','$params[ndata16]','$params[ndata17]','$params[ndata18]','$params[ndata19]','$params[ndata20]','$params[ncompare]','$params[npredata]','$params[naddsubdata]','$params[naddsubhundcore]','$params[ddate]','$params[dmarket]',
					'$params[Ddata1]','$params[Ddata2]','$params[Ddata3]','$params[Ddata4]','$params[Ddata5]','$params[Ddata6]','$params[Ddata7]','$params[Ddata8]','$params[Ddata9]','$params[Ddata10]','$params[Ddata11]','$params[Ddata12]','$params[Ddata13]','$params[Ddata14]','$params[Ddata15]','$params[Ddata16]','$params[Ddata17]','$params[Ddata18]','$params[Ddata19]','$params[Ddata20]','$params[dcompare]','$params[dpredata]','$params[daddsubdata]','$params[daddsubhundcore]',NOW(),'$_SESSION[duserid]','$params[ndate_en]','$params[nmarket_en]','$params[ndata1_en]','$params[ndata2_en]','$params[ndata3_en]','$params[ndata4_en]','$params[ndata5_en]','$params[ndata6_en]','$params[ndata7_en]','$params[ndata8_en]','$params[ndata9_en]','$params[ndata10_en]','$params[ndata11_en]','$params[ndata12_en]','$params[ndata13_en]','$params[ndata14_en]','$params[ndata15_en]','$params[ndata16_en]','$params[ndata17_en]','$params[ndata18_en]','$params[ndata19_en]','$params[ndata20_en]','$params[npredata_en]','$params[naddsubdata_en]','$params[naddsubhundcore_en]','$params[npredata_unit]','$params[naddsubdata_unit]','$params[naddsubhundcore_unit]','".$params['mc_type']."')";
					$add_tec = $this->_dao->execute($sql_tec);
				}

				$this->writelog("2","新增数据类型".$params['dtname'],$params['mc_type']);
				echo "<script>alert('新增成功');window.location.href='admindata.php?view=datatypelist&mc_type=".$params['mc_type']."'</script>";
			}
		$where="";
		
		if($params['Status']){
			$where .= " and Status=1";
		}
		$where .=" and mc_type='".$params['mc_type']."'";

		$sql="select * from dc_code_class where 1 $where  order by sod asc ";
		$all_class = $this->_dao->execute($sql);
	    while (!$all_class->EOF){			
			$mcode = $all_class->fields("mcode");
			$sname = $all_class->fields("sname");
			$scode = $all_class->fields("scode");
		
			if($mcode==""){
				$fclass[$scode]	= $sname;//一级类
			}else{
				$code_par[$scode] = $mcode;
				$second[$scode] = $sname;//二级类
			}
			$this->assign( "datetype", $GLOBALS['datetype'] );
			$this->assign( "hb", $GLOBALS['hb'] );
			$this->assign( "pictype", $GLOBALS['pictype'] );
			
			$this->assign( "code_par", $code_par );
			$this->assign( "fclass", $fclass );
			$this->assign( "second", $second );
		    $all_class->MoveNext();
		}

		$sql="select * from dc_code_datasources";
		$datasource = $this->_dao->query($sql);
		$this->assign( "datasource", $datasource );

		$this->setvars($params);	
	}

	public function replacedata($params){      
	
        	$mc_type = $params['mc_type'];
	          $this->assign("mc_type",$mc_type);
			  
		if($params['submit']=="提交信息"&&$params['data_type']!=""){
       
			$startdate =$params['startdateYear']."-".$params['startdateMonth'];
			$enddata   =$params['enddataYear']."-".$params['enddataMonth'];
            $datatitle =$params["DatasTitle1"];
            $replaceT  =$params["subAtt1Db"];    
            $data_type =$params["data_type"];   

		      
			$sql = "select * from steelhome_drc.`data_table` WHERE `dta_1`='$datatitle' and `dta_ym` >= '$startdate' AND `dta_ym` <= '$enddata' AND dta_type LIKE '$data_type' AND dta_vartype LIKE '' GROUP BY dta_ym ORDER BY id ASC";
		
			$res = $this->drc->query($sql);
           
            //echo '<pre>';print_r($add_one);exit();
            $sql = "update steelhome_drc.`data_table` set dta_1 = '$replaceT' WHERE `dta_1`='$datatitle' and `dta_ym` >= '$startdate' AND `dta_ym` <= '$enddata' AND dta_type LIKE '$data_type' AND dta_vartype LIKE ''";			
            $this->drc->execute($sql);

		
            if($res){
			   $this->writelog("2","替换子类型数据名称,将".$datatitle."修改为".$replaceT,$mc_type);
			   echo "<script>alert('替换成功');window.location.href='admindata.php?view=replacedata'</script>";
            }else{
		          
               echo "<script>alert('亲,您输入的子数据名称不存在!');window.location.href='admindata.php?view=replacedata'</script>";   
            }
                
        }

	}

	public function addchilddata($params) {
		
		$did = $params['pid'];
		$sname =$params['childsname'];
		$sname_en =$params['childsname_en'];
		$scode=$params['childscode'];
		$scode_en=$params['childscode_en'];
		$aod=$params['childaod'];
		$Status=$params['childStatus'] == "on"?"1":"0";

		$issub = $params['isSubDatas'] == "on"?"1":"0";

		if($params['sdbtype'] ){
			$sdbtype = $params['sdbtype'];
		}else{
			$sdbtype = 1;
		}

		//修改子状态
		//$this->_dao->execute("update dc_code_datatype set isSubDatas = '".$issub."', subDatasDb='".$params['subDatasDb']."' where ID = '".$did."'");


		if($params['cid'] != "" && $params['cid'] != 0){
			$this->_dao->execute("update dc_code_datatype_subs set sname = '".$sname."',sname_en = '".$sname_en."',satt1='".$params['satt1']."',satt1_en='".$params['satt1_en']."',satt2_en='".$params['satt2_en']."',satt3_en='".$params['satt3_en']."',satt2='".$params['satt2']."',satt3='".$params['satt3']."',scode = '".$scode."',scode_en = '".$scode_en."',aod = '".$aod."',Status =  '".$Status."',sdbtype='$sdbtype' where ID = '".$params['cid']."'");
			$this->writelog("2","修改子数据类型".$params['sname'],$params['mc_type']);
			alert("修改成功");		
		}else{
			$this->_dao->execute("insert into dc_code_datatype_subs set DID = '".$did."',sname = '".$sname."',sname_en = '".$sname_en."',satt1='".$params['satt1']."',satt2='".$params['satt2']."',satt3='".$params['satt3']."',satt1_en='".$params['satt1_en']."',satt2_en='".$params['satt2_en']."',satt3_en='".$params['satt3_en']."',scode = '".$scode."',scode_en = '".$scode_en."',aod = '".$aod."',Status =  '".$Status."',sdbtype='$sdbtype',mc_type='".$params['mc_type']."'");

			$this->writelog("2","添加子数据类型".$params['sname'],$params['mc_type']);
			alert("添加成功");
		}

		//goURL("admindata.php?view=edit_datatype&id='".$did."'&mc_type='".$params[mc_type]."'");
		goBack();
	}

	public function delchildtype($params) {
		$this->_dao->execute("delete from dc_code_datatype_subs where ID = ".$params['cid']);
		alert("删除成功");
		goBack();
	}


	//分类下拉级联
	public function ajaxgetpz( $params ){
    
	  $member =  $this->_dao->query("select scode,sname from dc_code_class where mcode = '".$params['mcode']."' and Status =1 and mc_type='".$params['mc_type']."' order by sod" );//".$params[mc_type]."
	  foreach ($member as $key=>$val) 
        { 
            $member[$key]['sname'] = $val['sname']; 
        } 	
	  echo   json_encode($member);
	  exit;
  }
	public function ajaxgetscode2( $params ){
	  //$member =  $this->_dao->query("select scode,sname from dc_code_class where mcode = '".$params['mcode']."' and Status =1  order by sod" );
	  $member =  $this->_dao->query("select scode,sname from dc_code_class where mcode = '".$params['mcode']."' and Status =1 and mc_type='".$params['mc_type']."' order by sod" );//".$params[mc_type]."
	  foreach ($member as $key=>$val) 
        { 
			$member[$key]['sname'] = $val['sname']; 
			$member[$key]['sname']=htmlspecialchars_decode($member[$key]['sname']);
        } 	
	  echo   json_encode($member);
	  exit;
  }
	public function ajaxgetscode3( $params ){
	  $member =  $this->_dao->query("select scode,sname from dc_code_class where mcode = '".$params['mcode']."' and Status =1 and mc_type='".$params['mc_type']."' and sname='".$params['sname']."' order by sod" );//".$params[mc_type]."
	  foreach ($member as $key=>$val) 
        { 
            $member[$key]['sname'] = $val['sname']; 
        } 	
		//return $member;
	  echo   json_encode($member);
	  exit;
  }


  	

	

	public function getwool($params){
		$wool=$this->_dao->getone("select MaxLineNum from app_version where mc_type='$params[mc_type]'");
		echo $wool;
	}

	

	//一级目录获取 分类下拉
	public function ajaxgettype( $params ){
    
	  $member =  $this->_dao->query("select sname,scode from dc_code_class where mcode = '".$params['mcode']."' and Status =1 and mc_type='".$params['mc_type']."'   " );
	  foreach ($member as $key=>$val) 
        { 
            //$member[$key]['sname'] = iconv('gb18030','utf-8',$val['sname']); 
            $member[$key]['sname'] = $val['sname']; 
        } 	
	  echo   json_encode($member);
	  exit;
  }
  
  //一级目录获取 分类下拉
	public function ajaxgettype3( $params ){
    
		$member =  $this->_dao->query("select sname,scode from dc_code_class where mcode = '".$params['mcode']."' and Status =1  and mc_type='".$params['mc_type']."'   " );
		foreach ($member as $key=>$val) 
		  { 
			  ///$member[$key]['sname'] = iconv('gb18030','utf-8',$val['sname']); 
			  $member[$key]['sname'] = $val['sname']; 
		  } 	
		echo   json_encode($member);
		exit;
	}

  //一级目录获取 分类下拉
	public function ajaxgettype1( $params ){
		$member =  $this->_dao->query("select ID,dtname from dc_code_datatype where scode3='".$params['mcode']."' and scode4='' and Status =1 and mc_type='".$params['mc_type']."' " );
		foreach ($member as $key=>$val) 
        { 
            $member[$key]['dtname'] = $val['dtname']; 
        } 	
		echo   json_encode($member);
		exit;
  }
  
  public function ajaxgettype4( $params ){
		$member =  $this->_dao->query("select ID,dtname from dc_code_datatype where scode2='".$params['mcode']."' and scode3='' and Status =1 and  mc_type='".$params['mc_type']."' " );
		foreach ($member as $key=>$val) 
        { 
            $member[$key]['dtname'] = $val['dtname']; 
        } 	
	  echo   json_encode($member);
	  exit;
  }
  
  public function ajaxgettype6( $params ){
		$member =  $this->_dao->query("select ID,dtname from dc_code_datatype where scode4= '".$params['mcode']."' and scode5='' and Status =1 and mc_type='".$params['mc_type']."' " );
		foreach ($member as $key=>$val) 
        { 
            $member[$key]['dtname'] = $val['dtname']; 
        } 	
	  echo   json_encode($member);
	  exit;
  }
  public function ajaxgettype7( $params ){
		$member =  $this->_dao->query("select ID,dtname from dc_code_datatype where scode5= '".$params['mcode']."' and Status =1 and mc_type='".$params['mc_type']."' " );
		foreach ($member as $key=>$val) 
        { 
            $member[$key]['dtname'] = $val['dtname']; 
        } 	
	  echo   json_encode($member);
	  exit;
  }
    public function ajaxgettype5( $params ){
		$member =  $this->_dao->query("select ID,dtname from dc_code_datatype where scode1= '".$params['mcode']."'  and scode2='' and Status =1 and mc_type='".$params['mc_type']."' " );
		foreach ($member as $key=>$val) 
        { 
            $member[$key]['dtname'] = $val['dtname']; 
        } 	
	  echo   json_encode($member);
	  exit;
  }
  
 

  	//一级目录获取 分类下拉
	public function ajaxgettype2( $params ){
    
	  $member =  $this->_dao->query("select ID,sname,satt1,satt2,satt3 from dc_code_datatype_subs where DID = '".$params['mcode']."' and Status =1 and mc_type='".$params['mc_type']."' order by aod" );
	  foreach ($member as $key=>$val) 
      { 
            $sn = $val['satt1']." ".$val['satt2']." ".$val['satt3'];

			$member[$key]['sname'] = $val['sname']." ".$sn; 
      } 	
	
	  $row = $this->_dao->getRow("select ndata1,npredata,naddsubdata,naddsubhundcore,ndata2,ndata3,ndata4,ndata5,ndata6,ndata7,ndata8,ndata9,ndata10,ndata11,ndata12,ndata13,ndata14,ndata15,ndata16,ndata17,ndata18,ndata19,ndata20 from dc_code_datatype_db where DID = '".$params['mcode']."' and mc_type='".$params['mc_type']."' limit 1");
	  $rach = "";
	  foreach($row as $key=>$val) {
		  $rach  .= $val."|"; 
	  }
	  echo   json_encode($member)."|-|".$rach;
	  exit;
  }


  public function ajaxsetcheck($params) {
	$row = $this->_dao->getRow("select ndata1,ndata2,ndata3,ndata4,ndata5,ndata6,ndata7,ndata8,ndata9,ndata10,ndata11,ndata12,ndata13,ndata14,ndata15,ndata16,ndata17,ndata18,ndata19,ndata20,npredata,naddsubdata,naddsubhundcore from dc_code_datatype_db where DID = '".$params['id']."' and mc_type='".$params['mc_type']."' limit 1");
  }



	public function userdetail($params) {
		$this->assign("mc_type",$params['mc_type']);
		$row = $this->_dao->getRow("select * from dc_search_custom where ID = '".$params['id']."' limit 1");
		$cname = $this->stedao->getOne("select compfname from member where mbid = '".$row['MID']."' limit 1",0);
			
		$this->assign("params",$params);
		$this->assign("ddtype",$GLOBALS['DDTYPE']);
		$this->assign("ddtype1",$GLOBALS['DDTYPE1']);
		$this->assign("cname",$cname);
		$this->assign("row",$row);	
		$this->setvars($params);

		//add by std 2020/4/29 
		$DTIDJson=json_decode($row['DTIDJson'],true);
		
		if(!empty($DTIDJson)){
			foreach($DTIDJson as $key=>$val){
				$list=$this->_dao->getRow("select scode1,scode2,scode3,scode4,scode5 from dc_code_datatype where ID = '$val[DTID]'");
				$DTIDJson[$key]['scode1']=$list['scode1'];
				$DTIDJson[$key]['scode2']=$list['scode2'];
				$DTIDJson[$key]['scode3']=$list['scode3'];
				$DTIDJson[$key]['scode4']=$list['scode4'];
				$DTIDJson[$key]['scode5']=$list['scode5'];
				$DTIDJson[$key]['GongshiName']=$this->_dao->getOne("select Name  from dc_formula_custom where ID ='". $val['GongshiID']."'");
				
			}
			
		}else{
			$k=0;
			for($i=1;$i<5;$i++){
				if($row['DTID'.$i]!='0' || $row['Data'.$i."Type"]!='0'){
					$DTID=$row['DTID'.$i];
					$list=$this->_dao->getRow("select scode1,scode2,scode3,scode4,scode5 from dc_code_datatype where ID = '$DTID'");
					$DTIDJson[$k]['scode1']=$list['scode1'];
					$DTIDJson[$k]['scode2']=$list['scode2'];
					$DTIDJson[$k]['scode3']=$list['scode3'];
					$DTIDJson[$k]['scode4']=$list['scode4'];
					$DTIDJson[$k]['scode5']=$list['scode5'];
					$DTIDJson[$k]['DTID']=$DTID;
					$DTIDJson[$k]['DTIDSub']=$row['DTID'.$i."Sub"];
					$DTIDJson[$k]['DataImage']=$row['Data'.$i."Image"];
					$DTIDJson[$k]['zhou']=$row['zhou'.$i];
					$DTIDJson[$k]['DataType']=$row['Data'.$i."Type"];
					$DTIDJson[$k]['GongshiName']=$this->_dao->getOne("select Name  from dc_formula_custom where ID ='". $row['GongshiID'.$i]."'");
					$DTIDJson[$k]['GongshiID']=$row['GongshiID'.$i];
					$DTIDJson[$k]['LineTitle']=$row['LineTitle'.$i];
					$k++;
				}
			}
		}
		
		$len=count($DTIDJson);
		$this->assign("DTIDJson",$DTIDJson);
		$array=json_encode($DTIDJson);
		$array=str_replace("\\/", "/", $array);
		$this->assign("DTIDJson1",$array);
		$this->assign("len",$len);
	}


	//数据类型 预览
	public function datatypeyl($params) {

		$DTID1 = $params['id'];
		$sql="select * from dc_code_datatype,dc_code_datatype_db  where dc_code_datatype.ID='$params[id]' and dc_code_datatype_db.DID=dc_code_datatype.ID and dc_code_datatype_db.mc_type='".$params['mc_type']."' and dc_code_datatype.mc_type=dc_code_datatype_db.mc_type";
		$row = $this->_dao->getRow($sql);

		$this->assign("title",$row['dtname']);
		$this->assign("subtitle",$row['subDatasTitle']);
		$DateStart =$row['startdate'];
		//$DateStart = date('Y-m-d',strtotime("$Date - 1 year")); 
		
		$DateEnd = $row['enddata'];

		//alert($DateEnd);
		$TablePages = 1;
		$TableRecords = 10;

		//print_r($row);
		$array_dy = array("6"=>$row['subAtt1Db'],"7"=>$row['subAtt2Db'],"8"=>$row['subAtt3Db'],"5"=>$row['ndate'],"1"=>$row['ndata1'],"12"=>$row['ndata2'],"13"=>$row['ndata3'],"14"=>$row['ndata4'],"15"=>$row['ndata5'],
				"2"=>$row['npredata'],"3"=>$row['naddsubdata'],"4"=>$row['naddsubhundcore']
				);

		$array_dd = array();

		
			$post  ="?action=CommonSearchTable&DTID1=".$DTID1."&DateStart=".$DateStart."&DateEnd=".$DateEnd."&TablePages=".$TablePages."&TableRecords=".$TableRecords."&debug=0&mc_type=".$params['mc_type'];

			$url = APP_URL_DC."/v1.5/dcsearch.php".$post; 

			echo "URL:".$url."<BR>";

			$aa = file_get_contents( $url );

			$bb = json_decode($aa,true);	

			

			if($bb['Success'] == "1"){

				$arr = $bb['Results'];
			
			}else{
				alert(base64_decode($bb['Message']));
			}

			$issub = "";

			$this->assign("SubDataDate",$bb['SubDataDate']);

			


			foreach($arr as &$tmp) {
				$tmp['0'] = base64_decode($tmp['0']);
				if($tmp['0']){$issub = 1;}
				if($tmp['6']){
					$tmp['6'] = base64_decode($tmp['6']);
				}
				if($tmp['7']){
					$tmp['7'] = base64_decode($tmp['7']);
				}
				if($tmp['8']){
					$tmp['8'] = base64_decode($tmp['8']);
				}

				foreach($array_dy as $key=>$val) {
					
					if($tmp[$key]){
						$array_dd[$key] = $val;
					}
				}
			}
			
			
			$this->assign("searr",$arr);
			$this->assign("issub",$issub);
			$this->assign("array_dd",$array_dd);

			
			
		}



		public function ajaxcheckcode($params) {
			if(empty($params['mc_type'])) $mc_type=0;
			else $mc_type=$params['mc_type'];

			$isc = $this->_dao->getRow("select * from dc_code_class where scode = '".$params['scode']."'and mc_type='".$mc_type."'");

			if($isc ){
				if($isc['ID'] == $params['id']){
					echo "1";
				}else{
					echo "2";
				}
			}else{
				echo "1";
			}

		}


		//写日志
		public function writelog($type,$cont,$mc_type){
			$this->assign("mc_type",$mc_type);
		
			$this->_dao->execute("insert into dc_sys_log set ltype='$type',dContent='$cont',UserId='".$_SESSION['duserid']."',OperMan='".$_SESSION['truename']."',OperDate=NOW(),mc_type='".$mc_type."'");
		}


		public function loglist($params) {
			
			$mc_type = $params['mc_type'];
			$this->assign("mc_type",$mc_type);
			
			$where = "";
			if($params['ltype']){
				$where .= " and ltype = '".$params['ltype']."'";
			}
			if($params['dContent']){
				$where .= " and dContent like  '%".$params['dContent']."%'";
			}
			
			/*change start*/
			$where .=" and mc_type='".$mc_type."'";
		$this->assign("params",$params);
		$total = $this->_dao->getOne( "SELECT Count(*) as c FROM dc_sys_log  WHERE 1 $where" );
		$page = $params['page'] == '' ? 1 : $params['page'];
		$per = 20;
		$url = "admindata.php";
		unset( $params['page'] );
		$start = ( $page - 1 ) * $per;

			$list = $this->_dao->query( "SELECT * from dc_sys_log Where 1 $where ORDER BY ID desc LIMIT $start, $per");
			
			$pagebar = pagebar( $url, $params, $per, $page, $total );
			$this->assign( "pagebar", $pagebar );
			

			$this->assign( "list", $list);

			$this->assign("ddtype",$GLOBALS['DDTYPE']);

			$this->setvars($params);	

		}


		public function importsub($params) {
		

		
			$files = processfileupload( 'upload/csvtemp' );

			$did = $params['ppid'];

			$atts = array();
			foreach( $files as $f ){
				if( $f != false ){
					$atts[] = $f;
				}
			}
			$atts = implode( ",", $atts );

			$row = 0;

			$handle = fopen($atts, "r");
			$existmid = "";
			$existmidcount = 0;
			$succeedcount = 0;
			setlocale(LC_ALL,'zh_CN');

			$cvsmid = 0;
			while ($data = fgetcsv($handle, 1000, ",")) { //while
					$row++;
					if ($row==1) continue;	//跳过标题行

			$this->_dao->execute("insert into dc_code_datatype_subs set DID = '".$did."',sname = '". $data[0]."',satt1='".$data[3]."',satt2='".$data[4]."',satt3='".$data[5]."',scode = '".$data[1]."',aod = '".$data[2]."',Status =  '1' , mc_type='".$params['mc_type']."'");

			}

			alert("导入成功");
			goBack();
		}


		public function setpl($params) {
			$que = $this->_dao->query("select * from dc_code_datatype_subs where DID = '".$params['pid']."' and mc_type='".$params['mc_type']."'");

			foreach($que as $tmp) {
				$as = "aodcl".$tmp['ID'];
				$stats = "statuscl".$tmp['ID'];
				$this->_dao->execute("update dc_code_datatype_subs set aod = '".$params[$as]."', Status ='".$params[$stats]."' where ID = '".$tmp['ID']."'",0);
			}

			goBack();
		}


	function cut_str($string, $sublen, $start = 0, $code = 'UTF-8')
{
    if($code == 'UTF-8')
    {
        $pa = "/[\x01-\x7f]|[\xc2-\xdf][\x80-\xbf]|\xe0[\xa0-\xbf][\x80-\xbf]|[\xe1-\xef][\x80-\xbf][\x80-\xbf]|\xf0[\x90-\xbf][\x80-\xbf][\x80-\xbf]|[\xf1-\xf7][\x80-\xbf][\x80-\xbf][\x80-\xbf]/";
        preg_match_all($pa, $string, $t_string);

        if(count($t_string[0]) - $start > $sublen) return join('', array_slice($t_string[0], $start, $sublen))."...";
        return join('', array_slice($t_string[0], $start, $sublen));
    }
    else
    {
        $start = $start*2;
        $sublen = $sublen*2;
        $strlen = strlen($string);
        $tmpstr = '';

        for($i=0; $i< $strlen; $i++)
        {
            if($i>=$start && $i< ($start+$sublen))
            {
                if(ord(substr($string, $i, 1))>129)
                {
                    $tmpstr.= substr($string, $i, 2);
                }
                else
                {
                    $tmpstr.= substr($string, $i, 1);
                }
            }
            if(ord(substr($string, $i, 1))>129) $i++;
        }
        if(strlen($tmpstr)< $strlen ) $tmpstr.="";
        return $tmpstr;
    }
}

	public function sondata($params){

		$sql="select dc_code_datatype_subs.* from dc_code_datatype left join dc_code_datatype_subs on dc_code_datatype_subs.DID=dc_code_datatype.ID where 1 and scode1 = 'B' and isSubDatas = '1' and dc_code_datatype_subs.mc_type='".$params['mc_type']."' and dc_code_datatype.mc_type='".$params['mc_type']."'";
		$data=$this->_dao->query($sql);
		foreach($data as $val){
			//echo  "\\".strlen($val['scode'])."==>" ;
			if(strlen($val['scode']) ==6){
				$this->_dao->execute("update dc_code_datatype_subs set sdbtype =2 where ID = '".$val['ID']."' ");
			}elseif(strlen($val['scode']) == 7){
				$this->_dao->execute("update dc_code_datatype_subs set sdbtype =1 where ID = '".$val['ID']."' ");			
			}
		}
		echo "succeed";
	}
	public function getmcodemes($params){
		$code=$params['mcode'];
		$mc_type=empty($params['mc_type'])?"0":$params['mc_type'];
		$menu=$params['menu'];
		$sql="select scode,sname,snameshort from dc_code_class where mcode='$code' and mc_type='$mc_type' and Status='1'";
		$data=$this->_dao->query($sql);
		$retstr=$menu.":";
		if(count($data)<1) {
			echo $retstr;
			return;
		}
		foreach($data as $d){
			$d['sname']=htmlspecialchars_decode($d['sname']);
			$retstr.=$d['scode']."|".$d['sname'].";";
		}
		echo $retstr;
	}
	
	public function ajaxgetcode($params){
		$code=$params['code'];
		$mc_type=empty($params['mc_type'])?"0":$params['mc_type'];
		$type=$params['type'];

		$showmenu="";
		$menu=array();
		$code_arr[0]=$code;
		for($i=0;$i<5;$i++){//break;
			$sql="select mcode from dc_code_class where scode='$code' and mc_type='$mc_type' and Status='1'";
			$mcode=$this->_dao->getone($sql);//echo $sql."<br>";

			$code=$mcode;
			$code_arr[]=$code;

			$sql="select mcode,scode,sname,snameshort from dc_code_class where mcode='$mcode' and mc_type='$mc_type' and Status='1' order by sod";//echo $sql."<br>";
			$data=$this->_dao->query($sql);
			$menu[]=$data;
			
			if($code=="") {//echo $i."<br>";
				break;
			}
		}
		//print"<pre>";print_r($menu);print"</pre>";
		$len=count($menu);
		for($i=$len-1;$i>=0;$i--){
			if($i==0&&$type!="all") break;
			$mes="";
			foreach($menu[$i] as $m){
				$m['sname']=htmlspecialchars_decode($m['sname']);
				$mes.=$mes==""?$code_arr[$i].":".$m['scode'].",".$m['sname']:"|".$m['scode'].",".$m['sname'];
			}
			$showmenu.=$showmenu==""?$mes:";".$mes;
		}
		echo $showmenu;
	}
		
		function array_iconv($str, $in_charset="gbk", $out_charset="utf-8")
		{
		 if(is_array($str))
		 {
		 foreach($str as $k => $v)
		 {
		  $str[$k] = ($v);
		 }
		 return $str;
		 }
		 else
		 {
		 if(is_string($str))
		 {
		  // return iconv('UTF-8', 'GBK//IGNORE', $str);
		  return mb_convert_encoding($str, $out_charset, $in_charset);
		 }
		 else
		 {
		  return $str;
		 }
		 }
}
	public function getnewcode($params){
		$mcode=$params["mcode"];
		$mc_type=empty($params['mc_type'])?"0":$params['mc_type'];
		
		if(strlen($mcode)==1||strlen($mcode)==2 ){
				$ji='1';
		}else if(strlen($mcode)==3||strlen($mcode)==4 ){
				$ji='2';
		}else if(strlen($mcode)==5 || strlen($mcode)==6){
				$ji='3';
		}else if(strlen($mcode)==7 || strlen($mcode)==8){
				$ji='4';
		}
		
		$charactors = array('A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z');
			if($ji==1)
			{
				$mes = $this->_dao->getone("SELECT MAX(  `scode` ) FROM  `dc_code_class` WHERE  scode LIKE  '".$mcode."___' and mc_type='".$mc_type."' ");
				if(empty($mes))
				{
				 $mes = $this->_dao->getone("SELECT MAX(  `scode` ) FROM  `dc_code_class` WHERE scode LIKE  '".$mcode."__' and mc_type='".$mc_type."'");
				}

                if($mes)
				{
                 
                   $pieces = explode($mcode, $mes);
                   $num=$pieces[1];
                  $num++;
				  if($num<10)
				  {
                     $scode=$mcode."0".$num;
				  }
				  else
				  {
					   $scode=$mcode.$num;
				  }

				}
				else
				{
                  $scode=$mcode."01";
				}

			}
			else if($ji==2)
			{
				//$mes = $this->_dao->getone("SELECT MAX(  `scode` ) FROM  `dc_code_class` WHERE scode LIKE  '".$mcode."__' or scode LIKE  '".$mcode."___' ");



                 $mes = $this->_dao->getone("SELECT MAX(  `scode` ) FROM  `dc_code_class` WHERE  scode LIKE  '".$mcode."___' and mc_type='".$mc_type."'");
				if(empty($mes))
				{
				 $mes = $this->_dao->getone("SELECT MAX(  `scode` ) FROM  `dc_code_class` WHERE  scode LIKE  '".$mcode."__' and mc_type='".$mc_type."'");
				}

                 

				if($mes)
				{
				  $pieces = explode($mcode, $mes);
                  $mes=$pieces[1];
                  $key=array_search(substr($mes, -1),$charactors);
				  $value=$charactors[$key+1];
                  if(strlen($mes)==3)
				  {
                    
					if(empty($value))
					  {
						$key= array_search(substr($mes,1,1),$charactors);

                         $scode=$mcode."_".$charactors[$key+1]."A" ;
					  }
					  else
					  {
                        $scode=$mcode."_".substr($mes, 1, 1).$charactors[$key+1] ;
					  }

				  }
				  else
				  {
					  if(empty($value))
					  {
                         $scode=$mcode."_AA" ;
					  }
					  else
					  {
                        $scode=$mcode."_".$charactors[$key+1] ;
					  }

				  }

				  
				}
				else
				{
                  $scode=$mcode."_A";
				}

			}
			else if($ji==3)
			{

				//echo "SELECT MAX(  `scode` ) FROM  `dc_code_class` WHERE scode LIKE  '".$mcode."__' or scode LIKE  '".$mcode."___' ";
				$mes = $this->_dao->getone("SELECT MAX(  `scode` ) FROM  `dc_code_class` WHERE  scode LIKE  '".$mcode."___' and mc_type='".$mc_type."'");
				if(empty($mes))
				{
				 $mes = $this->_dao->getone("SELECT MAX(  `scode` ) FROM  `dc_code_class` WHERE scode LIKE  '".$mcode."__' and mc_type='".$mc_type."'");
				}

                if($mes)
				{
                 
                   $pieces = explode($mcode, $mes);
                   $num=$pieces[1];
                  $num++;
				  if($num<10)
				  {
                     $scode=$mcode."0".$num;
				  }
				  else
				  {
					   $scode=$mcode.$num;
				  }

				}
				else
				{
                  $scode=$mcode."01";
				}
			}
			else if($ji==4)
			{
				$mes = $this->_dao->getone("SELECT MAX(  `scode` ) FROM  `dc_code_class` WHERE  scode LIKE  '".$mcode."___' and mc_type='".$mc_type."'");
				if(empty($mes))
				{
				 $mes = $this->_dao->getone("SELECT MAX(  `scode` ) FROM  `dc_code_class` WHERE scode LIKE  '".$mcode."__' and mc_type='".$mc_type."'");
				}

                 

				if($mes)
				{
				  $pieces = explode($mcode, $mes);
                  $mes=$pieces[1];
                  $key=array_search(substr($mes, -1),$charactors);
				  $value=$charactors[$key+1];
                  if(strlen($mes)==3)
				  {
                    
					if(empty($value))
					  {
						$key= array_search(substr($mes,1,1),$charactors);

                         $scode=$mcode."_".$charactors[$key+1]."A" ;
					  }
					  else
					  {
                        $scode=$mcode."_".substr($mes, 1, 1).$charactors[$key+1] ;
					  }

				  }
				  else
				  {
					  if(empty($value))
					  {
                         $scode=$mcode."_AA" ;
					  }
					  else
					  {
                        $scode=$mcode."_".$charactors[$key+1] ;
					  }

				  }

				  
				}
				else
				{
                  $scode=$mcode."_A";
				}
			}
            
			echo $scode;
		}

	private function getstrtype($str,$type=1){
		if($type==0) $str=$str[0];

		if(ord($str)>=ord('a')&&ord($str)<=ord('z')) return 1;
		elseif(ord($str)>=ord('A')&&ord($str)<=ord('Z')) return 1;
		elseif(ord($str)>=ord('0')&&ord($str)<=ord('9')) return 2;
		else return 3;
	}



	public function update_name(){
		$sql="select ID,dtname,techsqlmain,dtname_en from  dc_code_datatype where (dtname like'%&gt;%' OR  techsqlmain like'%&gt;%'  OR  dtname_en like'%&gt;%') AND scode1='F' and scode2='F01' order by ID asc";
		$list=$this->_dao->query($sql);
		
		foreach($list as $key=>$value){
			$params['dtname']=htmlspecialchars_decode($value['dtname']);
			$params['techsqlmain']=htmlspecialchars_decode($value['techsqlmain']);
			$params['dtname_en']=htmlspecialchars_decode($value['dtname_en']);
			$array = array( "dtname","techsqlmain","dtname_en");
			$data = $this->getdata( $array, $params );
			$this->_dao->execute("UPDATE dc_code_datatype SET $data WHERE  ID='$value[ID]'");
		}

		//$sql1="UPDATE dc_code_datatype SET techsqlmain=htmlspecialchars_decode(techsqlmain) WHERE  ID='9448'";
		//$this->_dao->execute($sql);
		echo  $count.'条更新完成';
	}

	// 处理数据  如：将1000 转化为4
	public function change_UnitConv($params){
		// $GLOBALS['UNITCONV1']= array(
		// 	"1"=>"吨",
		// 	"1000"=>"千吨",
		// 	"10000"=>"万吨",
		// 	"10000000"=>"千万吨",
		// );
		// $GLOBALS['UNITCONV2']= array(
		// 	"1"=>"元",
		// 	"1000"=>"千元",
		// 	"10000"=>"万元",
		// 	"1000000"=>"百万元",
		// 	"100000000"=>"亿元",
		// 	"1000000000000"=>"万亿"
		// );
		$change_arr = array(
			// "1"=>"1",
			"4"=>"1000",
			"5"=>"10000",
			"7"=>"1000000",
			"8"=>"10000000",
			"9"=>"100000000",
			"13"=>"1000000000000"
		);
		$i = $params['i'];
		// echo "<pre>";print_r($this->_dao);

		// for ($i=1; $i<=20; $i++) {
			foreach ($change_arr as $new => $old) {
				$sql = "update dc_code_datatype set Data".$i."UnitConv = '".$new."' where Data".$i."UnitConv = '".$old."'";
				echo $sql."<br>";
				$this->_dao->execute($sql);
			}
		// }
	}


	//设置短信id  add by std 2021/3/9
	public function setsmsid($params){
		
		$this->assign("mc_type",$params['mc_type']);
	}


	public function getinfo($params)
    {
        $sms_id = $params['sms_id'];
		$mc_type = $params['mc_type'];

        $where = " where 1 and isdel=0 and mc_type='$mc_type'  ";
        if ($sms_id!="") {
            $where .= " and MS_ID='$sms_id' ";
        }
        $field = $params['field'];
        $order = $params['order'];
        $orderby = " order by id desc";

        if($field!=""&&$order!="") {
            if($field=="sms_id") $field = "MS_ID";
            $orderby = " order by $field $order";
            if($order=='null') $orderby = " order by id desc";
        }
        $page = $params['page']==""?0:$params['page']-1;
        $pagenum = $params['limit']*$page;
        $sql = "select count(id) from steelhome_drc.`dc_smsid_manage` $where ";
        $c = $this->drc->getone($sql);
		

        $sql = "select * from steelhome_drc.`dc_smsid_manage` $where $orderby limit $pagenum,".$params['limit'];
        $info = $this->drc->query($sql);
        
		$createuser= $smsids=array();
        foreach($info as $index=>&$value) {
            if(!in_array($value['creatuser'], $createuser)){
                $createuser[] = $value['creatuser'];
            }
			if(!in_array($value['MS_ID'], $smsids)){
                $smsids[] = $value['MS_ID'];
            }
        }

		$smsid_str = implode("','",$smsids);
        $createuser_str = implode("','",$createuser);
        $sql = "select id,truename from adminuser where id in ('$createuser_str')";
        $adminuser_info = $this->stedao->aquery($sql);

		$smssql = "select MS_ID,MS_NAME from MESSAGE where MS_ID in ('$smsid_str')";
        $smsid_info = $this->sms->aquery($smssql);

        foreach($info as $k=>&$v) {
            $v["createusername"] = $adminuser_info[$v['creatuser']];
			$v["sms_title"] = $smsid_info[$v['MS_ID']];
			$v["sms_id"] = $v['MS_ID'];
        }
        $response["code"] = 0;
        $response["count"] = $c;
        $response["data"] = $info;
        echo $this->pri_JSON($response);
        exit;
        
    }

	public function addsms($params){
		$this->assign("mc_type",$params['mc_type']);
	}


	public function getsms($params){
		$mc_type=$params["mc_type"];
		$sms_id = addslashes($params["sms_id"]);

		$where="";
		if($params["id"]){
			$id = addslashes($params["id"]);
			$where.=" and id!=$id";
		}
		$id = addslashes($params["sms_id"]);


		$sql = "select MS_ID from dc_smsid_manage where MS_ID='$sms_id' $where and isdel=0 and mc_type='$mc_type'";
        $info = $this->drc->getrow($sql);

		if($info){
			$Message['Success']=0;
			//$Message['msg']=iconv("gb2312","utf-8","短信id已存在！请勿重复添加");
			$Message['msg']="短信id已存在！请勿重复添加";
			echo  json_encode($Message);
			exit;
		}

		$smssql = "select * from MESSAGE where MS_ID=$sms_id";
        $smsid_info = $this->sms->getrow($smssql);

		if(empty($smsid_info)){
			$Message['Success']=0;
			//$Message['msg']=iconv("gb2312","utf-8","短信id不正确！");
			$Message['msg']="短信id不正确！";
			echo  json_encode($Message);
			exit;
		}else{
			$Message['Success']=1;
			//$Message['msg']=iconv("gb2312","utf-8","$smsid_info[MS_NAME]");
			$Message['msg']=$smsid_info['MS_NAME'];
			echo  json_encode($Message);
			exit;
		}

	}
	
	public function doaddsms($params){
		
		$mc_type=$params["mc_type"];
		$sms_ids =$params["sms_ids"];
		$user = $_SESSION['duserid'];
        
		$error=array();
		$recur=array();
		$success=array();




		foreach($sms_ids as $key=>$value){
			$sms_id=$value;

			$smssql = "select MS_ID from MESSAGE where MS_ID=$sms_id";
			$smsid_info = $this->sms->getrow($smssql);
	
			if(empty($smsid_info)){
				$error[]=$sms_id;
			}else{
				$sql = "select MS_ID from dc_smsid_manage where MS_ID='$sms_id' and isdel=0 and mc_type='$mc_type'";
        		$info = $this->drc->getrow($sql);

				if($info){
					$recur[]=$sms_id;
				}else{
					$sql = "insert into dc_smsid_manage set MS_ID='$sms_id',createtime=NOW(),creatuser='$user',mc_type='$mc_type'";
					$this->drc->execute($sql);
					$success[]=$sms_id;
				}
			}
		}

		$msg="";
		if(!empty($error)){
			$msg.=implode(",",$error)."短信id不正确！";
		}

		if(!empty($recur)){
			$msg.=implode(",",$recur)."短信id已存在，请勿重复添加！";
		}

		if(!empty($success)){
			$msg.=implode(",",$success)."添加成功！";
		}

		$Message['Success']=1;
		//$Message['msg']=iconv("gb2312","utf-8",$msg);
		$Message['msg']=$msg;
		echo  json_encode($Message);
		exit;
        
		/*$smssql = "select MS_ID from MESSAGE where MS_ID=$sms_id";
        $smsid_info = $this->sms->getrow($smssql);

		if(empty($smsid_info)){
			$Message['Success']=0;
			$Message['msg']=iconv("gb2312","utf-8","短信id不正确！");
			echo  json_encode($Message);
			exit;
		}

		$sql = "select MS_ID from dc_smsid_manage where MS_ID='$sms_id' and isdel=0 and mc_type='$mc_type'";
        $info = $this->drc->getrow($sql);

		if($info){
			$Message['Success']=0;
			$Message['msg']=iconv("gb2312","utf-8","短信id已存在！请勿重复添加");
			echo  json_encode($Message);
			exit;
		}
		$sql = "insert into dc_smsid_manage set MS_ID='$sms_id',createtime=NOW(),creatuser='$user',mc_type='$mc_type'";
		$this->drc->execute($sql);*/
		
		
	}


	public function veditsms($params)
    {
        $id = $params['id'];
        $sql = "select * from dc_smsid_manage where id='$id'";
        $res = $this->drc->getrow($sql);
        $this->assign('id', $id);
        $this->assign('sms_id', $res['MS_ID']);
		$this->assign("mc_type",$params['mc_type']);
    }

	public function doeditsms($params){

        $sms_id = addslashes($params["sms_id"]);
        $mc_type = addslashes($params["mc_type"]);
        $id = addslashes($params["id"]);
        $user = $_SESSION['duserid'];

		$smssql = "select MS_ID from MESSAGE where MS_ID=$sms_id";
        $smsid_info = $this->sms->getrow($smssql);
		if(empty($smsid_info)){
			$Message['Success']=0;
			//$Message['msg']=iconv("gb2312","utf-8","短信id不正确！");
			$Message['msg']="短信id不正确！";
			echo  json_encode($Message);
			exit;
		}

		$sql = "select MS_ID from dc_smsid_manage where MS_ID='$sms_id' and id!='$id' and isdel=0 and mc_type='$mc_type'";
        $info = $this->drc->getrow($sql);
		if($info){
			$Message['Success']=0;
			//$Message['msg']=iconv("gb2312","utf-8","短信id已存在！请勿重复添加");
			$Message['msg']="短信id已存在！请勿重复添加";
			echo  json_encode($Message);
			exit;
		}
		$user = $_SESSION['duserid'];
		$username = $_SESSION['truename'];

        $sql = "update dc_smsid_manage set MS_ID='$sms_id',updateadminid='$user',updateadminname='$username',updatetime=NOW() where id='$id'";
        $this->drc->execute($sql);
		$Message['Success']=1;
		//$Message['msg']=iconv("gb2312","utf-8","修改成功");
		$Message['msg']="修改成功";
		echo  json_encode($Message);
		exit;
    
	}


	public function delsms($params)
    {
        $id = addslashes($params["id"]);
        $user = $_SESSION['duserid'];
        $sql = "update dc_smsid_manage set isdel=1,deladminid='$user',deltime=NOW()  where id='$id'";
        $this->drc->execute($sql);
        $Message['Success']=1;
		//$Message['msg']=iconv("gb2312","utf-8","删除成功");
		$Message['msg']="删除成功";
		echo  json_encode($Message);
		exit;
    }

	private function pri_JSON($array)
    {
        $this->pri_arrayRecursive($array, 'urlencode', true);
        $json = json_encode($array);
        //return iconv("GB2312", "UTF-8", urldecode($json));
		return  urldecode($json);
    }

    private function pri_arrayRecursive(&$array, $function, $apply_to_keys_also = false)
    {
        static $recursive_counter = 0;
        if (++$recursive_counter > 1000) {
            die('possible deep recursion attack');
        }
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $this->pri_arrayRecursive($array[$key], $function, $apply_to_keys_also);
            } else {
                $array[$key] = $function($value);
            }
            if ($apply_to_keys_also && is_string($key)) {
                $new_key = $function($key);
                if ($new_key != $key) {
                    $array[$new_key] = $array[$key];
                    unset($array[$key]);
                }
            }
        }
        $recursive_counter--;
    }


	//addd by std 2021/5/25
	public function powerlist($params){
        $mc_type=$params['mc_type'];
        // if($_SESSION['power']!='A'){
        //     echo "无权限";
        //     exit;
        // }
		if($mc_type!=3 && $mc_type!=2 && $mc_type!=4){
			exit;
		}

        //权限选项
        $popedomsql="SELECT app_license_popedom_type.id as typeid,app_license_popedom_type.status as tstatus,app_license_popedom_item.status as istatus,app_license_popedom_item.id,app_license_popedom_type.typename,app_license_popedom_item.itemname FROM app_license_popedom_type LEFT JOIN (SELECT * FROM app_license_popedom_item WHERE  app_license_popedom_item.mc_type='$mc_type' AND app_license_popedom_item.isdel=0) AS app_license_popedom_item ON app_license_popedom_type.id=app_license_popedom_item.typeid  WHERE  app_license_popedom_type.mc_type='$mc_type' AND app_license_popedom_type.isdel=0 ";
        $popedomList=$this->_dao->query($popedomsql);

        $qxList=array();
        foreach($popedomList as $pkey=>$pvalue){

            $qxList[$pvalue['typename']]['data'][]=$pvalue;
            $qxList[$pvalue['typename']]['count']=count($qxList[$pvalue['typename']]['data']);
            $qxList[$pvalue['typename']]['typeid']=$pvalue['typeid'];
            $qxList[$pvalue['typename']]['tstatus']=$pvalue['tstatus'];

        }
        $this->assign("params",$params);
        $this->assign("qxList",$qxList);
    }


	public function addtype($params){
        if($params['submit']==2){
            $typeid=$params['typeid'];
            $list=$this->_dao->getRow("SELECT * FROM app_license_popedom_type WHERE id='$typeid'");
            $this->assign("list",$list);
        }
        $this->assign("params",$params);
    }

    public function doaddtype($params){
        $status= $params['status']==''?0:$params['status'];

		if($_SESSION['power']!='A'){
            alert("无权限");
			goback();
            exit;
        }


        $userid=$_SESSION['duserid'];
        $typename=urldecode($params['typenames']);
        //$typename=iconv("utf-8","gb2312",$typename);
       
		
		

        if($params['submit']=='1'){
            $insertsql="INSERT INTO app_license_popedom_type SET typename='".$typename."',status='$status',creattime =NOW(),creatuser='$userid',mc_type='".$params['mc_type']."'";
            $this->_dao->execute($insertsql);
            $typeid = $this->_dao->insert_id();
			

            if($params['mc_type']==3 || $params['mc_type']==2|| $params['mc_type']==4){
                $data[0]=array(
                    "id"=>$typeid,
                    "typename"=>"$typename",
                    "creattime"=>date('Y-m-d H:i:s'),
                    "mc_type"=>"$params[mc_type]",
                    "status"=>"1",
                    "creatuser"=>"$userid",
                    "updateadminid"=>"0",
                    "updateadminname"=>"0",
                    "updatetime"=>date("Y-m-d H:i:s"),
                    "isdel"=>"0"
                );
                
                $info[0]=array(
                    "tablename"=>"app_license_popedom_type",
                    "type"=>"insert",
                    "pk"=> "id",
                    "data"=>$data
                );
                $infos=json_encode($info);
				
            }

		
            $Result['Success']=1;
            $Result['msg']="添加成功";
            echo  json_encode($Result);
            exit;

        }else if($params['submit']=='2'){
            $typeid=$params['typeid'];
            $typeexit=$this->_dao->getOne("SELECT id FROM app_license_popedom_type WHERE typename='$typename' AND  id!='$typeid' AND isdel=0");
           
            if($typeexit==1){
                $Message['Success']=0;
                $Message['msg']="权限类型已存在,勿重复";
                echo  json_encode($Message);
                exit;
            }else{
                $insertsql="UPDATE app_license_popedom_type SET typename='".$typename."',status='$status',updateadminid='$userid',updatetime =NOW() WHERE id='$typeid'";
                $this->_dao->execute($insertsql);

                if($params['mc_type']==3 || $params['mc_type']==2 || $params['mc_type']==4){
                    $data[0]=array(
                        "id"=>"$typeid",
                        "updatetime"=>date("Y-m-d H:i:s"),
                        "updateadminid"=>"$userid",
                        "status"=>"$status",
                        "typename"=>"$typename"
                    );
                    $info[0]=array(
                        "tablename"=>"app_license_popedom_type",
                        "type"=>"update",
                        "pk"=> "id",
                        "data"=>$data
                    );
            
                    $infos=json_encode($info);
                }
                
                $Result['Success']=1;
                $Result['msg']="修改成功";
                echo  json_encode($Result);
                exit;
            }
        }
    }

    public function delpopedom($params){
        
        if($_SESSION['power']!='A'){
			$Message['Success']=0;
            $Message['msg']="无权限";
            echo  json_encode($Message);
            exit;
        }
        
        $userid= $_SESSION['duserid'];

        if($params['type']==1){
            $tablename="app_license_popedom_type";
            $this->_dao->execute("UPDATE app_license_popedom_type SET isdel=1,deltime=NOW(),deladminid='$userid' WHERE id='$params[id]'");

            $popedomList=$this->_dao->getRow("select * from app_license_popedom_type WHERE id='$params[id]'");
            $logMes[]=array(
                "opcontent"=>"删除权限类型".$popedomList['typename'],
                "optype"=>"9"
            );
            
        
        }else if($params['type']==2){
            $tablename="app_license_popedom_item";
            $this->_dao->execute("UPDATE app_license_popedom_item SET isdel=1,deltime=NOW(),deladminid='$userid' WHERE id='$params[id]'");
            
            $popedomList=$this->_dao->getRow("select * from app_license_popedom_item WHERE id='$params[id]'");
            $logMes[]=array(
                "opcontent"=>"删除权限选项".$popedomList['itemname'],
                "optype"=>"9"
            );
           
        }

        
            $data[0]=array(
                "id"=>"$params[id]",
                "deltime"=>date("Y-m-d H:i:s"),
                "isdel"=>"1",
                "deladminid"=>"$userid"
            );
            $info[0]=array(
                "tablename"=>$tablename,
                "type"=>"update",
                "pk"=> "id",
                "data"=>$data
            );
            $infos=json_encode($info);
        
        $Message['Success']=1;
        $Message['msg']="删除成功".$result;
        echo  json_encode($Message);
        exit;
    }


    public function additem($params){
        
        $mc_type=$params['mc_type'];
        $popedomtypes=$this->_dao->AQUERY("SELECT id,typename FROM app_license_popedom_type WHERE  app_license_popedom_type.mc_type='$mc_type' AND app_license_popedom_type.isdel=0 ");
        $this->assign("popedomtypes",$popedomtypes);
        
        if($params['submit']==1){
        }else if($params['submit']==2){
            $id=$params['id'];
            $list=$this->_dao->getRow("SELECT * FROM app_license_popedom_item WHERE id='$id' ");
            $this->assign("list",$list);
        }
        $this->assign("params",$params);
    }


    public function doadditem($params){
        $status= $params['status']==''?0:$params['status'];
		if($_SESSION['power']!='A'){
			$Message['Success']=0;
            $Message['msg']="无权限";
            echo  json_encode($Message);
            exit;
        }

        $typeid=$params['typeid'];
        $itemname=urldecode($params['itemnames']);
        //$itemname=iconv("utf-8","gb2312",$itemname);
        $mc_type=$params['mc_type'];
        $userid= $_SESSION['duserid'];

        if($params['submit']=='1'){
            $typeexit=$this->_dao->getOne("SELECT isdel FROM app_license_popedom_type WHERE id='$typeid'");
            if($typeexit==1){
                $Message['Success']=0;
                $Message['msg']="权限类型不存在，重新选择";
                echo  json_encode($Message);
                exit;
            }

            $isexit=$this->_dao->getOne("SELECT id FROM app_license_popedom_item WHERE typeid='$typeid' and itemname='$itemname' and mc_type='$mc_type' and isdel=0");
            if($isexit>0){
                $Message['Success']=0;
                $Message['msg']="已存在，请勿重复添加";
                echo  json_encode($Message);
                exit;
            }else{
                $insertsql="INSERT INTO app_license_popedom_item SET typeid='$typeid',itemname='".$itemname."',status='$status',creatuser='$userid',creattime=NOW(),mc_type='".$mc_type."'";
                $this->_dao->execute($insertsql);
                $itemid = $this->_dao->insert_id();

                $data[0]=array(
                    "id"=>"$itemid",
                    "typeid"=>"$typeid",
                    "itemname"=>"$itemname",
                    "creattime"=>date('Y-m-d H:i:s'),
                    "mc_type"=>"$mc_type",
                    "status"=>"$status",
                    "creatuser"=>"$userid",
                    "updateadminid"=>"0",
                    "updateadminname"=>"0",
                    "updatetime"=>date("Y-m-d H:i:s"),
                    "isdel"=>"0"
                );
            
                $info[0]=array(
                    "tablename"=>"app_license_popedom_item",
                    "type"=>"insert",
                    "pk"=> "id",
                    "data"=>$data
                );
            
                $infos=json_encode($info);

                $Message['Success']=1;
                $Message['msg']="添加成功";
                echo  json_encode($Message);
            }
        }else if($params['submit']=='2'){
                $id=$params['id'];
               
                $typeexit=$this->_dao->getOne("SELECT isdel FROM app_license_popedom_type WHERE id='$typeid'");
                if($typeexit==1){
                    $Message['Success']=0;
                    $Message['msg']="权限类型不存在，重新选择";
                    echo  json_encode($Message);
                    exit;
                }
                
                $isexit=$this->_dao->getOne("SELECT id FROM app_license_popedom_item WHERE typeid='$typeid' and itemname='$itemname' and id!='$id' and mc_type='$mc_type' and isdel=0");
                
                if($isexit>0){
                    $Message['Success']=0;
                    $Message['msg']="已存在，请勿重复添加";
                    echo  json_encode($Message);
                    exit;
                }else{
                    $updatesql="UPDATE app_license_popedom_item SET typeid='$typeid',status='$status',itemname='".$itemname."',updateadminid='$userid' WHERE id='$id'";
                    $this->_dao->execute($updatesql);

                    $data[0]=array(
                        "id"=>"$id",
                        "typeid"=>"$typeid",
                        "itemname"=>"$itemname",
                        "status"=>"$status",
                        "updateadminid"=>"$userid",
                        "updateadminname"=>"0",
                        "updatetime"=>date("Y-m-d H:i:s")
                    );
                
                    $info[0]=array(
                        "tablename"=>"app_license_popedom_item",
                        "type"=>"update",
                        "pk"=> "id",
                        "data"=>$data
                    );
                
                    $infos=json_encode($info);

                    $Message['Success']=1;
                    $Message['msg']="修改成功";
                    echo  json_encode($Message);
                }
        }
    }



    function http_post($url,$param,$post_file=false){
		$oCurl = curl_init();
	
		if(stripos($url,"https://")!==FALSE){
			curl_setopt($oCurl, CURLOPT_SSL_VERIFYPEER, FALSE);
			curl_setopt($oCurl, CURLOPT_SSL_VERIFYHOST, false);
			curl_setopt($oCurl, CURLOPT_SSLVERSION, 1); //CURL_SSLVERSION_TLSv1
		}
		if(PHP_VERSION_ID >= 50500 && class_exists('\CURLFile')){
			$is_curlFile = true;
		}else {
			$is_curlFile = false;
			if (defined('CURLOPT_SAFE_UPLOAD')) {
				curl_setopt($oCurl, CURLOPT_SAFE_UPLOAD, false);
			}
		}
		
		if($post_file) {
			if($is_curlFile) {
				foreach ($param as $key => $val) {                     
					if(isset($val["tmp_name"])){
						$param[$key] = new \CURLFile(realpath($val["tmp_name"]),$val["type"],$val["name"]);
					}else if(substr($val, 0, 1) == '@'){
						$param[$key] = new \CURLFile(realpath(substr($val,1)));                
					}                           
				}
			}                
			$strPOST = $param;
		}else{
			//$strPOST = json_encode($param);
			$strPOST = $param;
		} 
		
		curl_setopt($oCurl, CURLOPT_URL, $url);
		curl_setopt($oCurl, CURLOPT_RETURNTRANSFER, 1 );
		curl_setopt($oCurl, CURLOPT_POST,true);
		curl_setopt($oCurl, CURLOPT_POSTFIELDS,$strPOST);
		curl_setopt($oCurl, CURLOPT_VERBOSE, 1);
		curl_setopt($oCurl, CURLOPT_HEADER, 1);
	
		// $sContent = curl_exec($oCurl);
		// $aStatus  = curl_getinfo($oCurl);
	
		$sContent = $this->execCURL($oCurl);
	
		curl_close($oCurl);
	
		return $sContent;
	}

	public function clearBom($str){
        $bom = chr(239).chr(187).chr(191);
        return str_replace($bom ,'',$str);    
    }


	function execCURL($ch){
		$response = curl_exec($ch);
		$error    = curl_error($ch);
		$result   = array( 'header' => '', 
						 'content' => '', 
						 'curl_error' => '', 
						 'http_code' => '',
						 'last_url' => '');
		
		if ($error != ""){
			$result['curl_error'] = $error;
			return $result;
		}
	
		$header_size = curl_getinfo($ch,CURLINFO_HEADER_SIZE);
		$result['header'] = str_replace(array("\r\n", "\r", "\n"), "<br/>", substr($response, 0, $header_size));
		$result['content'] = substr( $response, $header_size );
		$result['http_code'] = curl_getinfo($ch,CURLINFO_HTTP_CODE);
		$result['last_url'] = curl_getinfo($ch,CURLINFO_EFFECTIVE_URL);
		$result["base_resp"] = array();
		$result["base_resp"]["ret"] = $result['http_code'] == 200 ? 0 : $result['http_code'];
		$result["base_resp"]["err_msg"] = $result['http_code'] == 200 ? "ok" : $result["curl_error"];
	
		return $result;
    }


	public function getFirstL($params) {
		$sql="select scode,sname from dc_code_class where Status=1 and mcode='' and mc_type='".$params['mc_type']."' order by sod asc ";
		$firlevel=$this->_dao->query($sql);
		foreach ($firlevel as $key=>$val) 
        { 
            $firlevel[$key]['sname'] = $val['sname']; 
        } 	
		echo   json_encode($firlevel);
	  	exit;
	}

	public function datatypechart($params) {
		$today=date("Y-m-d");
		$lastday=date("Y-m-d",strtotime("-1 year"));
		$uid=$_SESSION['duserid'];
		
		$sql="select GUID from app_session_temp where Mid=1 and Uid='$uid' and mc_type='".$params['mc_type']."' and ExpiredDate>'$today' order by LoginDate	 desc limit 1 ";
		$session_temp=$this->_dao->getRow($sql);
		$GUID=$session_temp['GUID'];
		$datatype=$this->_dao->getRow("select * from dc_code_datatype where  ID='".$params['id']."' limit 1 ");
		$dtname_base64=base64_encode($datatype['dtname']);
		$content='<center><div id="gp"><iframe name="iframe" style="margin:0;padding:0;width:98%;height:550px;background-color:#FFF;visibility:inherit;" src=\'https://dc.steelhome.com/v1.5/dcsearch.php?action=CommonSearch&SignCS=8b46e267268907263bbec91ec65915f4&GUID=testaccount&DateStart='.$lastday.'&DateEnd='.$today.'&IsImages=3&TableTypes=3&TablePages=1&TableRecords=10&ImageType='.$datatype['Data1Type'].'&ImageTitle=&isbg=0&isjz=0&isfg=0&color=0&mc_type='.$params['mc_type'].'&mode=1&isEnglish=0&ChartsType=1&AvgLineType=&leftmax=&leftmin=&rightmax=&rightmin=&DTIDJson=[{"DTID":"'.$params['id'].'","DTIDSub":"","DataImage":"1","zhou":"0","DataType":"1","GongshiID":"0","LineTitle":"'.$dtname_base64.'","unitconver":"'.$datatype['Data1UnitConv'].'","unitstring":"'.base64_encode($datatype['UnitTypeName']).'","linestylelight":"","linestylewidth":"","linestyletype":""}]&needCompress=0&ChartExt=&ChartExtLineStyle=\' frameborder="0" scrolling="no"></iframe></div></center>';
		echo $content;exit;
	}

	public function copyCode($params) {
		$mc_type=$params['mc_type'];
		$new_mc_type=$params['new_mc_type'];
		$scode1=$params['scode1'];
		$scode2=$params['scode2'];
		$scode3=$params['scode3'];
		$scode4=$params['scode4'];
		$scode5=$params['scode5'];
		$menu1=$params['menu1'];
		$menu2=$params['menu2'];
		$menu3=$params['menu3'];
		$menu4=$params['menu4'];
		$menu5=$params['menu5'];
		$sql="select * from dc_code_datatype where mc_type='$mc_type' and scode1='$menu1' and scode2='$menu2' and scode3='$menu3' and scode4='$menu4' and scode5='$menu5' and Status=1";
		$olddata=$this->_dao->query($sql);

		$sql="select * from dc_code_datatype where mc_type='$new_mc_type' and scode1='$scode1' and scode2='$scode2' and scode3='$scode3' and scode4='$scode4'  and scode5='$scode5' and Status=1";
		$exitdata=$this->_dao->query($sql);

		foreach($olddata as $k=>$v){
			foreach($exitdata as $k2=>$v2){
				if($v['dtname']==$v2['dtname']){
					unset($olddata[$k]);
				}
			}
		}
		sort($olddata);
		if(!empty($olddata)){
			foreach($olddata as $k=>$v){
				//$v['dtname']=mysqli_real_escape_string($this->_dao,$v['dtname']);
				//$v['dtname_en']=mysqli_real_escape_string($v['dtname_en']);
				//$v['techsqlmain']=mysqli_real_escape_string($v['techsqlmain']);
				$array = array("pinzhong", "startdate","enddata","topicture","mastertopid","dtname","dtname_en","dtymd","dtcomparetype","isSubDatas","subDatasDb","isMarkets","Data1Type","isData2","Data2Type","isData3","Data3Type","isData4","Data4Type","isData5","Data5Type","isData6","Data6Type","isData7","Data7Type","isData8","Data8Type","isData9","Data9Type","isData10","Data10Type","isData11","Data11Type","isData12","Data12Type","isData13","Data13Type","isData14","Data14Type","isData15","Data15Type","isData16","Data16Type","isData17","Data17Type","isData18","Data18Type","isData19","Data19Type","isData20","Data20Type","isPreData","isAddSubData","isAddSubHundCore","enddataisCurrent","dtod","dtdesc","dtdesc_en","techdbname","techtabletype","techtableGroupBy","techtablename","techtablename2","techsqlmain","Status","IsEnglish","Data1UnitType","Data1UnitConv","Data2UnitType","Data2UnitConv","Data3UnitType","Data3UnitConv","Data4UnitType","Data4UnitConv","Data5UnitType","Data5UnitConv","Data6UnitType","Data6UnitConv","Data7UnitType","Data7UnitConv","Data8UnitType","Data8UnitConv","Data9UnitType","Data9UnitConv","Data10UnitType","Data10UnitConv","Data11UnitType","Data11UnitConv","Data12UnitType","Data12UnitConv","Data13UnitType","Data13UnitConv","Data14UnitType","Data14UnitConv","Data15UnitType","Data15UnitConv","Data16UnitType","Data16UnitConv","Data17UnitType","Data17UnitConv","Data18UnitType","Data18UnitConv","Data19UnitType","Data19UnitConv","Data20UnitType","Data20UnitConv","dbaseType","subDatasTitle","subAtt1Db","subAtt2Db","subAtt3Db","subDatasDb2","UnitTypeName","UnitTypeName2","UnitTypeName3","UnitTypeName4","UnitTypeName5","UnitTypeName6","UnitTypeName7","UnitTypeName8","UnitTypeName9","UnitTypeName10","UnitTypeName11","UnitTypeName12","UnitTypeName13","UnitTypeName14","UnitTypeName15","UnitTypeName16","UnitTypeName17","UnitTypeName18","UnitTypeName19","UnitTypeName20","subDatasTitle_en","subAtt1Db_en","subAtt2Db_en","subAtt3Db_en","UnitTypeName_en","UnitTypeName2_en","UnitTypeName3_en","UnitTypeName4_en","UnitTypeName5_en","UnitTypeName6_en","UnitTypeName7_en","UnitTypeName8_en","UnitTypeName9_en","UnitTypeName10_en","UnitTypeName11_en","UnitTypeName12_en","UnitTypeName13_en","UnitTypeName14_en","UnitTypeName15_en","UnitTypeName16_en","UnitTypeName17_en","UnitTypeName18_en","UnitTypeName19_en","UnitTypeName20_en","TargetFlagVar","InterfaceUrl","IsGetNew","FieldName1","FieldName2","FieldName3","data_source_id","data_releasetime","data_source","npredata_UnitConv","npredata_UnitType","naddsubdata_UnitConv","naddsubdata_UnitType","daddsubhundcore_UnitConv","daddsubhundcore_UnitType","npredataDW_en","naddsubdataDW_en","naddsubhundcoreDW_en");
				$data = $this->getdata( $array, $v);
				$sql = "insert into dc_code_datatype set $data ,mc_type='$new_mc_type',scode1='$scode1',scode2='$scode2',scode3='$scode3',scode4='$scode4',scode5='$scode5',CreateUser='$_SESSION[truename]',CreateDate=NOW(),CreateUserId='$_SESSION[duserid]'";
				$add_one = $this->_dao->execute($sql);
				$DID=$this->_dao->insert_id();
				$sql="select * from dc_code_datatype_db where DID=$v[ID]";
				$db_data=$this->_dao->query($sql);
				foreach($db_data as $kk=>$vv){
					$sql_tec="insert into dc_code_datatype_db (DID,ndate,nmarket,ndata1,ndata2,ndata3,ndata4,ndata5,ndata6,ndata7,ndata8,ndata9,ndata10,ndata11,ndata12,ndata13,ndata14,ndata15,ndata16,ndata17,ndata18,ndata19,ndata20,ncompare,npredata,naddsubdata,naddsubhundcore,ddate,dmarket,Ddata1,Ddata2,Ddata3,Ddata4,Ddata5,Ddata6,Ddata7,Ddata8,Ddata9,Ddata10,Ddata11,Ddata12,Ddata13,Ddata14,Ddata15,Ddata16,Ddata17,Ddata18,Ddata19,Ddata20,dcompare,dpredata,daddsubdata,daddsubhundcore,UpdateDate,UpdateUserId,ndate_en,nmarket_en,ndata1_en,ndata2_en,ndata3_en,ndata4_en,ndata5_en,ndata6_en,ndata7_en,ndata8_en,ndata9_en,ndata10_en,ndata11_en,ndata12_en,ndata13_en,ndata14_en,ndata15_en,ndata16_en,ndata17_en,ndata18_en,ndata19_en,ndata20_en,npredata_en,naddsubdata_en,naddsubhundcore_en,npredataDW,naddsubdataDW,naddsubhundcoreDW,mc_type)
					values ('$DID','$vv[ndate]','$vv[nmarket]','$vv[ndata1]','$vv[ndata2]','$vv[ndata3]','$vv[ndata4]','$vv[ndata5]','$vv[ndata6]','$vv[ndata7]','$vv[ndata8]','$vv[ndata9]','$vv[ndata10]','$vv[ndata11]','$vv[ndata12]','$vv[ndata13]','$vv[ndata14]','$vv[ndata15]','$vv[ndata16]','$vv[ndata17]','$vv[ndata18]','$vv[ndata19]','$vv[ndata20]','$vv[ncompare]','$params[npredata]','$vv[naddsubdata]','$vv[naddsubhundcore]','$vv[ddate]','$vv[dmarket]',
					'$vv[Ddata1]','$vv[Ddata2]','$vv[Ddata3]','$vv[Ddata4]','$vv[Ddata5]','$vv[Ddata6]','$vv[Ddata7]','$vv[Ddata8]','$vv[Ddata9]','$vv[Ddata10]','$vv[Ddata11]','$vv[Ddata12]','$vv[Ddata13]','$vv[Ddata14]','$vv[Ddata15]','$vv[Ddata16]','$vv[Ddata17]','$vv[Ddata18]','$vv[Ddata19]','$vv[Ddata20]','$vv[dcompare]','$vv[dpredata]','$vv[daddsubdata]','$vv[daddsubhundcore]',NOW(),'$_SESSION[duserid]','$vv[ndate_en]','$vv[nmarket_en]','$vv[ndata1_en]','$vv[ndata2_en]','$vv[ndata3_en]','$vv[ndata4_en]','$vv[ndata5_en]','$vv[ndata6_en]','$vv[ndata7_en]','$vv[ndata8_en]','$vv[ndata9_en]','$vv[ndata10_en]','$vv[ndata11_en]','$vv[ndata12_en]','$vv[ndata13_en]','$vv[ndata14_en]','$vv[ndata15_en]','$vv[ndata16_en]','$vv[ndata17_en]','$vv[ndata18_en]','$vv[ndata19_en]','$vv[ndata20_en]','$vv[npredata_en]','$vv[naddsubdata_en]','$vv[naddsubhundcore_en]','$vv[npredata_unit]','$vv[naddsubdata_unit]','$vv[naddsubhundcore_unit]','".$new_mc_type."')";
					$add_tec = $this->_dao->execute($sql_tec);
				}
			}
			echo "1:".count($olddata);exit;
		}else{
			echo 0;exit;
		}
		

	}


	public function migrateData($params) {
		$mc_type=$params['mc_type'];
		$new_mc_type=$params['new_mc_type'];
		$scode1=$params['scode1'];
		$scode2=$params['scode2'];
		$scode3=$params['scode3'];
		$scode4=$params['scode4'];
		$scode5=$params['scode5'];
		$menu1=$params['menu1'];
		$menu2=$params['menu2'];
		$menu3=$params['menu3'];
		$menu4=$params['menu4'];
		$menu5=$params['menu5'];
		$infoidlist=$params['infoidlist'];
		if($_SESSION['power']!='A'){
			echo "0:无权限";exit;
		}
		if($mc_type==''||$new_mc_type==''){
			echo "0:mctype为空";exit;
		}
		if($scode1==''){
			echo "0:scode1为空";exit;
		}
		if($infoidlist!=''){
			$optimestamp=date("Y-m-d H:i:s");
			$baksql="insert into dc_code_datatype_migrate_logs (`ID`, `scode1`, `scode2`, `scode3`, `scode4`, `dtname`, `dtymd`, `dtcomparetype`, `dbaseType`, `isMarkets`, `isSubDatas`, `subDatasDb`, `subDatasDb2`, `subDatasTitle`, `subAtt1Db`, `subAtt2Db`, `subAtt3Db`, `isData1`, `Data1Type`, `isData2`, `Data2Type`, `isData3`, `Data3Type`, `isData4`, `Data4Type`, `isData5`, `Data5Type`, `isPreData`, `isAddSubData`, `isAddSubHundCore`, `startdate`, `enddataisCurrent`, `enddata`, `dtod`, `dtdesc`, `techdbname`, `techtabletype`, `techtableGroupBy`, `techtablename`, `techtablename2`, `techsqlmain`, `CreateUser`, `CreateDate`, `CreateUserId`, `UpdateDate`, `UpdateUserId`, `Status`, `Data1UnitType`, `Data1UnitConv`, `Data2UnitType`, `Data2UnitConv`, `Data3UnitType`, `Data3UnitConv`, `Data4UnitType`, `Data4UnitConv`, `Data5UnitType`, `Data5UnitConv`, `UnitTypeName`, `UnitTypeName2`, `UnitTypeName3`, `UnitTypeName4`, `UnitTypeName5`, `dtname_en`, `subDatasTitle_en`, `subAtt1Db_en`, `subAtt2Db_en`, `subAtt3Db_en`, `UnitTypeName_en`, `UnitTypeName2_en`, `UnitTypeName3_en`, `UnitTypeName4_en`, `UnitTypeName5_en`, `dtdesc_en`, `IsEnglish`, `mc_type`, `IsGetNew`, `TargetFlagVar`, `InterfaceUrl`, `FieldName1`, `FieldName2`, `FieldName3`, `isfrom`, `fromid`, `isData6`, `Data6Type`, `isData7`, `Data7Type`, `isData8`, `Data8Type`, `isData9`, `Data9Type`, `isData10`, `Data10Type`, `isData11`, `Data11Type`, `isData12`, `Data12Type`, `isData13`, `Data13Type`, `isData14`, `Data14Type`, `isData15`, `Data15Type`, `isData16`, `Data16Type`, `isData17`, `Data17Type`, `isData18`, `Data18Type`, `isData19`, `Data19Type`, `isData20`, `Data20Type`, `Data16UnitType`, `Data16UnitConv`, `Data17UnitType`, `Data17UnitConv`, `Data18UnitType`, `Data18UnitConv`, `Data19UnitType`, `Data19UnitConv`, `Data20UnitType`, `Data20UnitConv`, `UnitTypeName16`, `UnitTypeName17`, `UnitTypeName18`, `UnitTypeName19`, `UnitTypeName20`, `UnitTypeName16_en`, `UnitTypeName17_en`, `UnitTypeName18_en`, `UnitTypeName19_en`, `UnitTypeName20_en`, `Data6UnitType`, `Data6UnitConv`, `Data7UnitType`, `Data7UnitConv`, `Data8UnitType`, `Data8UnitConv`, `Data9UnitType`, `Data9UnitConv`, `Data10UnitType`, `Data10UnitConv`, `Data11UnitType`, `Data11UnitConv`, `Data12UnitType`, `Data12UnitConv`, `Data13UnitType`, `Data13UnitConv`, `Data14UnitType`, `Data14UnitConv`, `Data15UnitType`, `Data15UnitConv`, `UnitTypeName6`, `UnitTypeName7`, `UnitTypeName8`, `UnitTypeName9`, `UnitTypeName10`, `UnitTypeName11`, `UnitTypeName12`, `UnitTypeName13`, `UnitTypeName14`, `UnitTypeName15`, `UnitTypeName6_en`, `UnitTypeName7_en`, `UnitTypeName8_en`, `UnitTypeName9_en`, `UnitTypeName10_en`, `UnitTypeName11_en`, `UnitTypeName12_en`, `UnitTypeName13_en`, `UnitTypeName14_en`, `UnitTypeName15_en`, `oldsubs_id`, `pinzhong`, `scode5`, `topicture`, `mastertopid`, `data_source_id`, `data_releasetime`, `data_source`, `npredata_UnitConv`, `npredata_UnitType`, `naddsubdata_UnitConv`, `naddsubdata_UnitType`, `daddsubhundcore_UnitConv`, `daddsubhundcore_UnitType`, `npredataDW_en`, `naddsubdataDW_en`, `naddsubhundcoreDW_en`,optimestamp) select `ID`, `scode1`, `scode2`, `scode3`, `scode4`, `dtname`, `dtymd`, `dtcomparetype`, `dbaseType`, `isMarkets`, `isSubDatas`, `subDatasDb`, `subDatasDb2`, `subDatasTitle`, `subAtt1Db`, `subAtt2Db`, `subAtt3Db`, `isData1`, `Data1Type`, `isData2`, `Data2Type`, `isData3`, `Data3Type`, `isData4`, `Data4Type`, `isData5`, `Data5Type`, `isPreData`, `isAddSubData`, `isAddSubHundCore`, `startdate`, `enddataisCurrent`, `enddata`, `dtod`, `dtdesc`, `techdbname`, `techtabletype`, `techtableGroupBy`, `techtablename`, `techtablename2`, `techsqlmain`, `CreateUser`, `CreateDate`, `CreateUserId`, `UpdateDate`, `UpdateUserId`, `Status`, `Data1UnitType`, `Data1UnitConv`, `Data2UnitType`, `Data2UnitConv`, `Data3UnitType`, `Data3UnitConv`, `Data4UnitType`, `Data4UnitConv`, `Data5UnitType`, `Data5UnitConv`, `UnitTypeName`, `UnitTypeName2`, `UnitTypeName3`, `UnitTypeName4`, `UnitTypeName5`, `dtname_en`, `subDatasTitle_en`, `subAtt1Db_en`, `subAtt2Db_en`, `subAtt3Db_en`, `UnitTypeName_en`, `UnitTypeName2_en`, `UnitTypeName3_en`, `UnitTypeName4_en`, `UnitTypeName5_en`, `dtdesc_en`, `IsEnglish`, `mc_type`, `IsGetNew`, `TargetFlagVar`, `InterfaceUrl`, `FieldName1`, `FieldName2`, `FieldName3`, `isfrom`, `fromid`, `isData6`, `Data6Type`, `isData7`, `Data7Type`, `isData8`, `Data8Type`, `isData9`, `Data9Type`, `isData10`, `Data10Type`, `isData11`, `Data11Type`, `isData12`, `Data12Type`, `isData13`, `Data13Type`, `isData14`, `Data14Type`, `isData15`, `Data15Type`, `isData16`, `Data16Type`, `isData17`, `Data17Type`, `isData18`, `Data18Type`, `isData19`, `Data19Type`, `isData20`, `Data20Type`, `Data16UnitType`, `Data16UnitConv`, `Data17UnitType`, `Data17UnitConv`, `Data18UnitType`, `Data18UnitConv`, `Data19UnitType`, `Data19UnitConv`, `Data20UnitType`, `Data20UnitConv`, `UnitTypeName16`, `UnitTypeName17`, `UnitTypeName18`, `UnitTypeName19`, `UnitTypeName20`, `UnitTypeName16_en`, `UnitTypeName17_en`, `UnitTypeName18_en`, `UnitTypeName19_en`, `UnitTypeName20_en`, `Data6UnitType`, `Data6UnitConv`, `Data7UnitType`, `Data7UnitConv`, `Data8UnitType`, `Data8UnitConv`, `Data9UnitType`, `Data9UnitConv`, `Data10UnitType`, `Data10UnitConv`, `Data11UnitType`, `Data11UnitConv`, `Data12UnitType`, `Data12UnitConv`, `Data13UnitType`, `Data13UnitConv`, `Data14UnitType`, `Data14UnitConv`, `Data15UnitType`, `Data15UnitConv`, `UnitTypeName6`, `UnitTypeName7`, `UnitTypeName8`, `UnitTypeName9`, `UnitTypeName10`, `UnitTypeName11`, `UnitTypeName12`, `UnitTypeName13`, `UnitTypeName14`, `UnitTypeName15`, `UnitTypeName6_en`, `UnitTypeName7_en`, `UnitTypeName8_en`, `UnitTypeName9_en`, `UnitTypeName10_en`, `UnitTypeName11_en`, `UnitTypeName12_en`, `UnitTypeName13_en`, `UnitTypeName14_en`, `UnitTypeName15_en`, `oldsubs_id`, `pinzhong`, `scode5`, `topicture`, `mastertopid`, `data_source_id`, `data_releasetime`, `data_source`, `npredata_UnitConv`, `npredata_UnitType`, `naddsubdata_UnitConv`, `naddsubdata_UnitType`, `daddsubhundcore_UnitConv`, `daddsubhundcore_UnitType`, `npredataDW_en`, `naddsubdataDW_en`, `naddsubhundcoreDW_en`,'".$optimestamp."' from dc_code_datatype   WHERE dc_code_datatype.id IN (".$infoidlist.")";
			$this->_dao->execute($baksql);
			$originMenu=$menu1.">".$menu2.">".$menu3.">".$menu4.">".$menu5.">";
			$newMenu=$scode1.">".$scode2.">".$scode3.">".$scode4.">".$scode5.">";;
			$this->writelog("2","移动目录".$originMenu."下".$params['dtname'].count(explode(",",$infoidlist))."条数据到".$newMenu."目录,mc_type:".$mc_type."->".$new_mc_type."时间：".$optimestamp,$mc_type);
			$movesql= "update dc_code_datatype set mc_type=".$new_mc_type.",scode1='".$scode1."',scode2='".$scode2."',scode3='".$scode3."' ,scode4='".$scode4."',scode5='".$scode5."' where id in(".$infoidlist.") and mc_type=".$mc_type;
			$this->_dao->execute($movesql);
			echo "1:".$movesql;exit;
		}
		else{
			echo "0:";exit;
		}
		

	}

	public function saveDtod($params) {
		$mc_type=$params['mc_type'];
		$infoidlist=$params['infoidlist'];
		$dtodlist=$params['dtodlist'];
		$menu1=$params['menu1'];
		$menu2=$params['menu2'];
		$menu3=$params['menu3'];
		$menu4=$params['menu4'];
		$menu5=$params['menu5'];
		if($_SESSION['power']!='A'){
			echo "0:无权限";exit;
		}
		if($mc_type==''){
			echo "0:mctype为空";exit;
		}
		$idarr =explode(",",$infoidlist);
		$sortarr =explode(",",$dtodlist);
		$sql='';
		if($infoidlist!=''){
			$optimestamp=date("Y-m-d H:i:s");
			for($i=0;$i<count($idarr);$i++){  
				$sql="update dc_code_datatype set dtod='$sortarr[$i]' where id='$idarr[$i]'";
			    //print_r( $sql);exit;
				$this ->_dao ->execute($sql);		
		    }
			$originMenu=$menu1.">".$menu2.">".$menu3.">".$menu4.">".$menu5.">";
			// $newMenu=$scode1.">".$scode2.">".$scode3.">".$scode4.">".$scode5.">";
			$this->writelog("2","保存排序".$originMenu.count(explode(",",$infoidlist))."条数据,mc_type:".$mc_type."时间：".$optimestamp,$mc_type);
			// $movesql= "update dc_code_datatype set mc_type=".$new_mc_type.",scode1='".$scode1."',scode2='".$scode2."',scode3='".$scode3."' ,scode4='".$scode4."',scode5='".$scode5."' where id in(".$infoidlist.") and mc_type=".$mc_type;
			// $this->_dao->execute($movesql);
			echo "1:".$sql;exit;
		}
		else{
			echo "0:";exit;
		}
		

	}


	public function saveSod($params) {
		$mc_type=$params['mc_type'];
		$infoidlist=$params['infoidlist'];
		$sodlist=$params['sodlist'];
		$menu1=$params['menu1'];
		$menu2=$params['menu2'];
		$menu3=$params['menu3'];
		$menu4=$params['menu4'];
		$menu5=$params['menu5'];
		if($_SESSION['power']!='A'){
			echo "0:无权限";exit;
		}
		if($mc_type==''){
			echo "0:mctype为空";exit;
		}
		$idarr =explode(",",$infoidlist);
		$sortarr =explode(",",$sodlist);
		$sql='';
		if($infoidlist!=''){
			$optimestamp=date("Y-m-d H:i:s");
			for($i=0;$i<count($idarr);$i++){  
				$sql="update dc_code_class set sod='$sortarr[$i]' where id='$idarr[$i]'";
			    //print_r( $sql);exit;
				$this ->_dao ->execute($sql);		
		    }
			$originMenu=$menu1.">".$menu2.">".$menu3.">".$menu4.">".$menu5.">";
			// $newMenu=$scode1.">".$scode2.">".$scode3.">".$scode4.">".$scode5.">";
			$this->writelog("3","保存目录排序".$originMenu.count(explode(",",$infoidlist))."条数据,mc_type:".$mc_type."时间：".$optimestamp,$mc_type);
			// $movesql= "update dc_code_datatype set mc_type=".$new_mc_type.",scode1='".$scode1."',scode2='".$scode2."',scode3='".$scode3."' ,scode4='".$scode4."',scode5='".$scode5."' where id in(".$infoidlist.") and mc_type=".$mc_type;
			// $this->_dao->execute($movesql);
			echo "1:".$sql;exit;
		}
		else{
			echo "0:";exit;
		}
		

	}

	public function getCodeData($params) {
		$mc_type=$params['mc_type'];
		$new_mc_type=$params['new_mc_type'];
		$scode1=$params['scode1'];
		$scode2=$params['scode2'];
		$scode3=$params['scode3'];
		$scode4=$params['scode4'];
		$scode5=$params['scode5'];
		$menu1=$params['menu1'];
		$menu2=$params['menu2'];
		$menu3=$params['menu3'];
		$menu4=$params['menu4'];
		$menu5=$params['menu5'];
		$result=array();
		$result[1]['code']=1;
		$result[2]['code']=1;
		$msg="";
		$sql="select count(*) from dc_code_datatype where mc_type='$mc_type' and scode1='$menu1' and scode2='$menu2' and scode3='$menu3' and scode4='$menu4' and scode5='$menu5' and Status=1";
		$count=$this->_dao->getOne($sql);
		//echo $sql;
		//echo '<br>';
		if($count==0){
			$result[1]['code']=0;
			$result[1]['msg']=iconv("gbk","utf-8","该目录无数据，无法复制") ;
		}
		$sql="select count(*) from dc_code_datatype where mc_type='$new_mc_type' and scode1='$scode1' and scode2='$scode2' and scode3='$scode3' and scode4='$scode4' and scode5='$scode5' and Status=1";
		$count2=$this->_dao->getOne($sql);
		//echo $sql;
		//echo '<br>';
		if($count2>0){
			$result[2]['code']=0;
			$result[2]['msg']=iconv("gbk","utf-8","目标目录已有数据，确认复制吗");
		}
		echo  json_encode($result);
		exit;
	}
}
?>
