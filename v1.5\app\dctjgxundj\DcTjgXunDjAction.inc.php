<?php 
$GLOBALS['type_date']=date("Y-m-d");
$GLOBALS['type_Y']=date("Y");
$GLOBALS['type_Y_m']=date("Y-m");
//$GLOBALS['type_date_h']=date("Y-m-d");
class DcTjgXunDjAction extends AbstractAction
{
    public $stedao;

    public function __construct()
    {
        parent::__construct();
    }  
	


    public function index($params)
    {	
	
			//include '/usr/local/www/libs/phpQuery/phpQuery.php';
			/*include '../Util/NgFunction.php';
			$r=IsNgWorkDay($this->stedao,date("Y-10-08"));
			print_r($r);exit;*/
	
		if(isset($params['curdate'])){
			//$GLOBALS['date']=$params['curdate'];
			$GLOBALS['type_date']=$params['curdate'];
			$GLOBALS['type_Y']=date("Y",strtotime($GLOBALS['type_date']));
			$GLOBALS['type_Y_m']=date("Y-m",strtotime($GLOBALS['type_date']));
			//$GLOBALS['type_date_h']=date("Y-m-d H:m:s",strtotime($GLOBALS['type_date']));
			//print_r($GLOBALS['type_date_h']);
		}
		$firstday = $GLOBALS['type_Y_m'].'-01';
		$lastday = date('d',strtotime("$firstday +1 month -1 day"));
		//print_r($lastday);exit;
		//$showtime=date("Y-m");
		$showtime=$GLOBALS['type_Y_m'];
		$showxun=date("d",strtotime($GLOBALS['type_date']));
		//print_r($showxun);exit;
		$lastxunsd="20";//前一旬的开始日
		$lastxuned=$lastday;//前一旬的结束日
		$title_lastxunsd;
		//print_r($showxun);
		if($showxun > "10" && $showxun < "21"){
			
			$showxun="中";
			$lastxuned="10";
			$lastM=$showtime;
			//$lastMDs=date("Y-m-t",strtotime("-1 month"));
			$lastMDs=workday_gzj($firstday );
			$lastxunsd=date("j",strtotime($lastMDs));
			$lastMDe=$showtime."-".$lastxuned;
			$title_lastxunsd=$firstday ;
		
		}else if($showxun <="31" && $showxun>"20"){
			
			$showxun="下";
			//$lastxunsd="11";

			$lastxuned="20";
			$lastM=$showtime;
			$lastMDs=$GLOBALS['type_Y_m'].'-11';
			$lastMDs=workday_gzj($lastMDs);
			$lastxunsd=date("j",strtotime($lastMDs));
			$lastMDe=$lastM."-".$lastxuned;
			$title_lastxunsd=$GLOBALS['type_Y_m'].'-11';
			
		}else{
			//.date("Y-m-d",strtotime("-15 day",strtotime($date_arr[1]))).
			$lastM=date('Y-m', strtotime("m -1 ",strtotime($GLOBALS['type_Y_m'])));
			$showxun="上";
			$lastxuned=date('t',strtotime('m -1 ',strtotime($GLOBALS['type_Y_m'])));
			//$lastxunsd="20";
			$lastMDs=$lastM."-21";
			$lastMDs=workday_gzj($lastMDs);
			$lastxunsd=date("j",strtotime($lastMDs));
			$lastMDe=$lastM."-".$lastxuned;
			$title_lastxunsd=date("Y-m-21",strtotime("m -1 ",strtotime($GLOBALS['type_Y_m'])));
		}
		//print_r($lastxunsd);
		$i=$lastxunsd;
		$j=0;
		//print_r($lastxuned);exit;
		//print_r($i);
		$da_arr=array();
		if($lastxunsd>21){
			$n=date("N",strtotime($lastMDs));
			//include_once("/etc/steelconf/config/isholiday.php");
			//$isexist = file_get_contents("https://holiday.steelhome.com/isholiday.php?date=".$lastMDs);
			$isexist=_isholiday($lastMDs);
			if($isexist != "1"){
				$da_arr[0]=$lastxunsd;
			}else if($n!="6" && $n!="7"){
				$da_arr[0]=$lastxunsd;
			}
			$i=1;
			$j=1;
		}
	
		//print_r($i);
		for($i;$i<=$lastxuned;$i++){
			//print_r($i);
			$d[$j]=$i;
			$j++;
		}
		//$temp=array();
	
		$sql="select date,isholiday from steelhome.holiday  where date >='".$lastMDs."' and date <='".$lastMDe."'  order by date desc "; 
		$res=$this->maindao->Aquery($sql);
		//echo "<pre>";
//print_r($res);
		
		foreach($d as $key=>$day){
			$date=$lastM."-".$day;

			$date = date("Y-m-d",strtotime($date));
			
			$n=date("N",strtotime($date));
			if($res[$date]!=''){
				//print_r($res[$date]);
				//echo "<pre>";
				if($res[$date]=='0'){
					$da_arr[]=$day;
				}
			}else if($n!=6 && $n!=7 ){
				//print_r($day);
				$da_arr[]=$day;
			}
			
		}
		
		//print_r($da_arr);
		//exit;
		$s="23:59:59";
		$e="23:59:59";
		$date1=$lastMDs." ".$s;
		$date2=$lastMDe." ".$e;
		//print_r($lastMDs);
		
		//$this->bcsc_xs($showtime,$lastM."-".$lastxuned,$showxun);//exit;南钢棒材生产与销售情况
		$ddl=$this->bcsc_xs($showtime,$lastM."-".$lastxuned,$showxun);
		//print_r($title_lastxunsd);exit;
		$this->bxunshijg($date1,$date2,$da_arr,$lastM);//本旬市场价格
		$this->gctj_hz($date1,$date2,$title_lastxunsd,$lastMDe);//钢厂调价汇总
		$this->gzjyuc($lastMDe,$lastMDs);//钢之家预测
		$showtime_last=date("Y-m",strtotime("m -1 ",strtotime($GLOBALS['type_Y_m'])));
		$this->assign("showtime",date("Y年n月",strtotime($showtime)));
		$this->assign("showtime_last",date("Y年n月",strtotime($showtime_last)));
		$this->assign("showxun",$showxun);
		//$title_d=$lastM."-".$lastxunsd;
		//print_r($$title_d);exit;
		$title_lastxunsd=date("Y年n月j日",strtotime($title_lastxunsd));
		//print_r($title_lastxunsd);
		
		$this->assign("title_lastxunsd",$title_lastxunsd);
		$this->assign("lastxunsd",$lastxunsd);
		$this->assign("lastxuned",$lastxuned);
		$this->assign("lastM",date("Y年n月",strtotime($lastM)));
		$this->assign("d",$da_arr);
		//$arr=$this->djjy($lastM."-".$lastxunsd,$lastM."-".$lastxuned,$ddl);//定价建议---HTML不处理
		//print_r($arr);exit;
		if($_GET['issave']==1){
       // 保存数据
			if($showxun=='上'){
				$date=date("Y-m-01",strtotime($GLOBALS['type_Y_m']));
		   }else if($showxun=='中'){
				$date=date("Y-m-11",strtotime($GLOBALS['type_Y_m']));
		   }else{
			   $date=date("Y-m-21",strtotime($GLOBALS['type_Y_m']));
		   }
	   $sql="insert into `ng_price_model`(date,modeltype,createtime) values('".$date."','2',now())";
	   //print_r($sql);
	   $this->ngdao->execute($sql);
		// print_r($sql);//exit;
		//$this->ngdao->execute($sql);
		}
	  $this->assign("params",$params);
	  
    } 

	//保存数据
	public function savexundj($params)
	{
		//print_r($params);exit;
		if(isset($params['curdate'])){
			$GLOBALS['type_date']=$params['curdate'];
			$GLOBALS['type_Y']=date("Y",strtotime($GLOBALS['type_date']));
			$GLOBALS['type_Y_m']=date("Y-m",strtotime($GLOBALS['type_date']));
			//$GLOBALS['type_date_h']=date("Y-m-d H:m:s",strtotime($GLOBALS['type_date']));
			$url = DC_URL.DCURL."/dctjgxundj.php?view=index&issave=1&curdate=".$params['curdate'];
		}else{
			$url = DC_URL.DCURL."/dctjgxundj.php?view=index&issave=1";
		}
		//print_r($GLOBALS['type_date']);exit;
		$showxun=date("d",strtotime($GLOBALS['type_date']));
		if($showxun > "10" && $showxun < "21"){
			$showxun="中";	
		}else if($showxun <="31" && $showxun>"20"){
			$showxun="下";		
		}else{
			$showxun="上";
		}
		$title="旬碳结钢旬定价";
		//print_r($GLOBALS['type_date']);
		$model=date("Y年n月",strtotime($GLOBALS['type_Y_m'])).$showxun;
		$modeltitle=$model.$title;
		
		//print_r( $_SERVER['SCRIPT_FILENAME']."view=index");
		include '/usr/local/www/libs/phpQuery/phpQuery.php';
		phpQuery::newDocumentFile($url);
		$html=pq("body")->html();
		print_r($url);
		$modelcontent=htmlentities($html, ENT_QUOTES,"UTF-8");
		//file_put_contents("/tmp/mox.txt",$modelcontent,FILE_APPEND);
		$sql="update `ng_price_model` set modeltitle='".$modeltitle."',modelcontent='".$modelcontent."'  where modeltype='2' order by id desc limit 1";
		//file_put_contents("/tmp/mox.txt",$sql,FILE_APPEND);
		$this->ngdao->execute($sql);
		//echo 111;
		//print_r($html);
		print_r( $html);

	}
	public function bcsc_xs($showtime,$end,$showxun)
	{
		
		
	$d=date("d",strtotime($GLOBALS['type_date']));	
	//$d=1;
	if($d >"10"){
		
		$month=$showtime;	
		$startdate=$showtime.'-'."01";		
		
		//print_r($date_arr);
		//echo "<pre>";
		$last_showtime=date("Y-m",strtotime("m -1 ",strtotime($GLOBALS['type_Y_m'])));
		$lastdate_s=date("Y-m-01",strtotime("m -1 ",strtotime($GLOBALS['type_Y_m'])));
		//$showxun="上";
		if($showxun=="中"){
			$date_arr=$this->workday(date("Y-m-11",strtotime($GLOBALS['type_Y_m'])));
			$enddate=$date_arr[1];
			$date_last=$this->workday(date("Y-m-11",strtotime("m -1 ",strtotime($GLOBALS['type_Y_m']))));
			$lastdate_e=$date_last[1];
		}else if ($showxun=="下"){
			$date_arr=$this->workday(date("Y-m-21",strtotime($GLOBALS['type_Y_m'])));
			$enddate=$date_arr[1];
			$date_last=$this->workday(date("Y-m-21",strtotime("m -1 ",strtotime($GLOBALS['type_Y_m']))));
			$lastdate_e=$date_last[1];
		}
	$this->assign("last_date",date("n月",strtotime($month)));
		$last_tdate=date("Y-m",strtotime("-1 month ",strtotime($GLOBALS['type_Y_m'])));
		//print_r($last_tdate);
		$this->assign("last_tdate",date("n月",strtotime($last_tdate)));
	}else{
		
		$month=date("Y-m",strtotime("m -1 ",strtotime($GLOBALS['type_Y_m'])));
		
		$startdate=date("Y-m-01",strtotime("m -1 ",strtotime($GLOBALS['type_Y_m'])));
		//print_r($startdate);exit;
		$enddate=date("Y-m-t",strtotime("m -1 ",strtotime($GLOBALS['type_Y_m'])));
		$last_showtime=date("Y-m",strtotime("-2 month",strtotime($GLOBALS['type_Y_m'])));
		$lastdate_s=date("Y-m-01",strtotime("-2 month",strtotime($GLOBALS['type_Y_m'])));
		$date_arr=$this->workday($startdate);
		
		$lastdate_e=$date_arr[1];
		
		$this->assign("last_date",date("n月",strtotime($month)));
		$last_tdate=date("Y-m",strtotime("-2 month",strtotime($GLOBALS['type_Y_m'])));
		//print_r($last_tdate);
		$this->assign("last_tdate",date("n月",strtotime($last_tdate)));

		
	}
		$last_2date=date("Y-m",strtotime("+2 month ",strtotime($last_tdate)));
	$last_3date=date("Y-m",strtotime("+3 month ",strtotime($last_tdate)));
	$lastdate_2e=date("Y-m-t",strtotime($month));
	$lastdate_2s=date("Y-m-01",strtotime($month));
	//$lastdate_3e=date("Y-m-t",strtotime("+3 month ",strtotime($lastdate_e)));
	//$lastdate_3s=date("Y-m-01",strtotime("+3 month ",strtotime($lastdate_s)));
	$sql_2="SELECT DataMark,dta_ym,Value,dta_vartype FROM `ng_data_table` WHERE 1 and ( (`DataMark` in ('Ngyg0047','Ngyg0048') and dta_vartype =1 and (`dta_ym`<='".$lastdate_2e."' and `dta_ym`>='".$lastdate_2s."')) or ( `DataMark` in ('Ngyg0019','Ngyg0043') and dta_vartype =2 and `dta_ym`='".$month."' )) ";
	$res_2arr=$this->ngdao->query($sql_2);
		//print_r($res_2arr);
		$temp_jhl=array();
		$temp_xsl=array();
		foreach ($res_2arr as $val){
			
			if($val['dta_vartype']=='1'){
				$temp_xsl[$val['DataMark']][$val['dta_ym']]=$val['Value'];
			}else if($val['dta_vartype']=='2'){
				$temp_jhl[$val['DataMark']]=$val['Value'];
			}
			
		}
		foreach($temp_xsl as $k=>$v){
			ksort($v);
			$temp_xsl[$k]=$v;
			$temp_xsl[$k]['end']=end($v);
			//print_r(end($v));exit;
			if($k=='Ngyg0047'){
				$temp_xsl[$k]['ddl']=$temp_jhl['Ngyg0019']?round(end($v)/$temp_jhl['Ngyg0019']*100,2):0;
			}
			if($k=='Ngyg0048'){
				$temp_xsl[$k]['ddl']=$temp_jhl['Ngyg0043']?round(end($v)/$temp_jhl['Ngyg0043']*100,2):0;
			}
			if($temp_xsl[$k]['ddl']==0){
				$temp_xsl[$k]['ddl']='';
			}else{
				$temp_xsl[$k]['ddl']=$temp_xsl[$k]['ddl']."%";
			}
			
		}
		$this->assign("temp_xsl",$temp_xsl);
		$this->assign("temp_jhl",$temp_jhl);
		

		$this->assign("last_2date",date("n月",strtotime($last_2date)));
		$sql="SELECT DataMark,dta_ym,Value,dta_vartype FROM `ng_data_table` WHERE 1 and ( (`DataMark` in ('Ngyg0017','Ngyg0045','Ngyg0046') and dta_vartype =1 and ((`dta_ym`<='".$lastdate_e."' and `dta_ym`>='".$lastdate_s."') or(`dta_ym`<='".$enddate."' and `dta_ym`>='".$startdate."') )) or ( `DataMark` in ('Ngyg0017','Ngyg0045','Ngyg0041') and dta_vartype =2 and (`dta_ym`='".$last_showtime."' or`dta_ym`='".$month."') )) ";
		$res_arr=$this->ngdao->query($sql);
		
		$temp_arr=array();
		//$temp_1arr=array();
		foreach ($res_arr as $val){
			
			if($val['dta_ym']==$month || $val['dta_ym']>=$startdate){
				$temp_arr[1][]=$val;//本月
			}else if($val['dta_ym']==$last_showtime || $val['dta_ym']>=$lastdate_s && $val['dta_ym']<=$lastdate_e){
				$temp_arr[0][]=$val;//上月
			}
		}
		//print_r($temp_arr);
		$temp_1jhl=array();//本月与上月计划量
		$temp_1xsl=array();//本月与上月销售量
		foreach ($temp_arr as $key=>$val){
			foreach ($val as $k=>$v){
				if($v['dta_vartype']=='1'){
					$temp_1xsl[$key][$v['DataMark']][$v['dta_ym']]=$v['Value'];
				}else if($v['dta_vartype']=='2'){
					$temp_1jhl[$key][$v['DataMark']]=$v['Value'];
				}
			}
			
		}
		
		foreach($temp_1xsl as $k=>$v){
			foreach($v as $key=>$val){
				ksort($val);
				$temp_1xsl[$k][$key]=$val;
				$temp_1xsl[$k][$key]['end']=end($val);
				if($key=='Ngyg0046'){
					$temp_1xsl[$k][$key]['ddl']=$temp_1jhl[$k]['Ngyg0041']?round(end($val)/$temp_1jhl[$k]['Ngyg0041']*100,2):0;
				}
				if($key=='Ngyg0045'){
					$temp_1xsl[$k][$key]['ddl']=$temp_1jhl[$k]['Ngyg0017']?round(end($val)/$temp_1jhl[$k]['Ngyg0017']*100,2):0;
					if($k=='1'){
						$ddl=$temp_1xsl[1][$key]['ddl'];
					}
				}
				
				if($temp_1xsl[$k][$key]['ddl']==0){
					$temp_1xsl[$k][$key]['ddl']='';
				}else{
					$temp_1xsl[$k][$key]['ddl']=$temp_1xsl[$k][$key]['ddl']."%";
				}
			}
			
		}
		$this->assign("temp_1xsl",$temp_1xsl);
		$this->assign("temp_1jhl",$temp_1jhl);
		
		//print_r($temp_1jhl);
		//exit;
		//累计值
		//print_r($enddate);exit;
			/*$key=$enddate."Ngyg0018";
		
			$xsl=$temp_arr[$key];	//本月
		
			$key_last=$lastdate_e."Ngyg0018";//上月同期
			$xsl_last=$temp_arr[$key_last];
		
		
		//exit;
		//print_r($temp_arr);
		
		$jhl=$temp_arr[$month.Ngyg0017];
	
		$jhl_last=$temp_arr[$last_showtime.Ngyg0017];
		
		$this->assign("temp_arr",$temp_arr);
		
		
		
		
		
		$last_ddl=round($xsl_last/$jhl_last*100,2);
		$zf_jhl=round($jhl/$jhl_last*100-100,2);
		$zf_xsl=round($xsl/$xsl_last*100-100,2);
		$ddl=round($xsl/$jhl*100,2);
		$this->assign("ddl",$ddl);
		$this->assign("last_ddl",$last_ddl);
		$this->assign("zf_jhl",$zf_jhl);
		$this->assign("zf_xsl",$zf_xsl);
		$this->assign("res_jhl",$jhl);
		$this->assign("res_xsl",$xsl);
		$this->assign("last_jhl",$jhl_last);
		$this->assign("last_xslr",$xsl_last);*/
		$this->assign("date",$d);
		
		return $ddl;
	}
	public function bxunshijg($date1,$date2,$da_arr,$lastM){
		$new_day=date("Y-m-d",strtotime("-1 month ",strtotime($date2)));
		$mas_arr=array('0822443','0822442','0822441','0822444','0822447','1222445','1222441','1222439','0822548','0822542','0822541','1222543','1222542','1122441','1122442','1122432');
		$mastertopid="('".implode("','",$mas_arr)."')";
		$sql="select * from marketconditions where mastertopid in ".$mastertopid."and mconmanagedate > '".$new_day."' and mconmanagedate < '".$date2."'";
		$all_zhb=$this->maindao->query($sql);
		$res_zhb=array();
		$res_zhb_one=array();
		$res_zhb_two=array();
		$res_zhb_tre=array();
		$date_arr=$this->four_xun_date($date1,$date2);
		$date_1=$date_arr['date_1'];
		$date_2=$date_arr['date_2'];
		$title_time_1=$date_arr['title_time_1'];
		$title_time_2=$date_arr['title_time_2'];
		$title_time_3=$date_arr['title_time_3'];
		$s_Y=$date_arr['s_Y'];
		$e_Y=$date_arr['e_Y'];


		/*$tmp_d1=date("j",strtotime($date1));
		if($tmp_d1<='10'){
			$date_1=date("Y-m-01 00:00:00",strtotime($date1));
			$date_2=date("Y-m-21 00:00:00",strtotime("-1 month ",strtotime($date_1)));
			$title_time_1=date("n",strtotime($date1))."月中旬";
			$title_time_2=date("n",strtotime($date_1))."月上旬";
			$title_time_3=date("n",strtotime($date_2))."月下旬";
			$s_Y=date("Y年",strtotime($date_2));
			$e_Y=date("Y年",strtotime($date2));
		}else if($tmp_d1>'20'){
			$date_1=date("Y-m-21 00:00:00",strtotime($date1));
			$date_2=date("Y-m-11 00:00:00",strtotime($date_1));
			$title_time_1=date("n",strtotime($date2))."月上旬";
			$title_time_2=date("n",strtotime($date_1))."月下旬";
			$title_time_3=date("n",strtotime($date_2))."月中旬";
			$s_Y=date("Y年",strtotime($date_2));
			$e_Y=date("Y年",strtotime($date2));
		}else if($tmp_d1<='20' && $tmp_d1>='11'){
			$date_1=date("Y-m-11 00:00:00",strtotime($date1));
			$date_2=date("Y-m-01 00:00:00",strtotime($date_1));
			$title_time_1=date("n",strtotime($date1))."月下旬";
			$title_time_2=date("n",strtotime($date_1))."月中旬";
			$title_time_3=date("n",strtotime($date_2))."月上旬";
			$s_Y=date("Y年",strtotime($date_2));
			$e_Y=date("Y年",strtotime($date2));
		}
		print_r($date_1);
		print_r($date_2);*/
		foreach($all_zhb as $temp){
			if($temp['mconmanagedate']>$date1){
				$res_zhb[]=$temp;
				$res_zhb_one[$temp['mastertopid']][]=$temp['pricemk'];
			}
			if($temp['mconmanagedate']>$date_1 && $temp['mconmanagedate'] <= $date1){
				$res_zhb_two[$temp['mastertopid']][]=$temp['pricemk'];
			}
			if($temp['mconmanagedate']>$date_2 && $temp['mconmanagedate'] < $date_1){
				$res_zhb_tre[$temp['mastertopid']][]=$temp['pricemk'];
			}
		}
		foreach($res_zhb_one as $key=>$val){
			
			if(in_array($key,$mas_arr)){
				$avg_one[$key]=round(array_sum($val)/count($val));
			}
			
		}
		foreach($res_zhb_two as $key=>$val){
			
			if(in_array($key,$mas_arr)){
				$avg_two[$key]=round(array_sum($val)/count($val));
			}
		}
		foreach($res_zhb_tre as $key=>$val){
			
			if(in_array($key,$mas_arr)){
				$avg_tre[$key]=round(array_sum($val)/count($val));
			}
			
		}
		$this->assign("avg_one",$avg_one);
		$this->assign("avg_two",$avg_two);
		$this->assign("avg_tre",$avg_tre);
		$this->assign("title_time_1",$title_time_1);
		$this->assign("title_time_2",$title_time_2);
		$this->assign("title_time_3",$title_time_3);
		$this->assign("s_Y",$s_Y);
		$this->assign("e_Y",$e_Y);
		$res_data = array();
		foreach($res_zhb as $tmp){
			$day = date("j",strtotime($tmp['mconmanagedate']));
			
			foreach($da_arr as $date){
				$d=$lastM."-".$date;
				//$date = date("j",strtotime($date));
			//	print_r($day);
				if($date==$day){
					$res_data[$tmp['mastertopid']][$day] =  $tmp['pricemk'];
				}
				
			}
			

			
			
		}
		foreach($mas_arr as $mastertopid){
			foreach($da_arr as $date){
				if($date=='9'){
					//echo "<pre>";
				//print_r($res_data[$mastertopid][$date]);
				//print_r($date);
				}
				if($res_data[$mastertopid][$date]=="") 
				{
					if($date=='9'){
						//echo "<pre>";
				//print_r($res_data[$mastertopid][$date]);
					}
					//print_r($res_data[$mastertopid][9]);
					$res_data[$mastertopid][$date]="--";
				}
			}
		}
		foreach($res_data as $key=>&$tmp){
			$tmp['zd'] = (float)current($tmp) - (float)end($tmp);
			if($tmp['zd']!=0){
				$tmp['zd']=-$tmp['zd'] ;
				$tmp['zd']=$this->zd_color1($tmp['zd']);
			}
			//print_r($tmp);
		}
		
		//print_r($this->maindao);exit;
		//print_r($res_data);
		$this->assign("res_data",$res_data);
		return $res_data;
		
	}
	function zhangdie($int){
	  //print_r($int);
		$intstr = "";
		if($int<0){
			$intstr = "<font style='color:green;'>↓".abs($int)."</font>";
		}elseif($int>0){
			$intstr = "<font style='color:red;'>↑".abs($int)."</font>";
		}elseif($int==0){
			$intstr = "<font >".$int."</font>";
		}elseif($int==""){
			$intstr = "--";
		}
		// print_r($intstr);
		return $intstr;
		
  }
	//钢厂调价汇总
	public function gctj_hz($date1,$date2,$start_date,$end_date){
		//print_r(date("Y-m-d",strtotime("-1 m",strtotime("2018-05-31"))));
		$steel_id = "('122','151','701','154','1844','1402','1181')";
      $steel_id_arr = array('122','151','701','154','1844','1402','1181');
      $onlyid = "('76d3cd4d3ec329a817d6c5b13e0a7af1','fdc82ccf562f79a69c327a58873b7315','7aca9a4d8cd8468fa2f16732416f6001','f8cb82417b9ae52cc3c328c08b557f75','9bd93685e3313d7058b1259022392950','ff4f1d0b9d61adfbdd39e0bd57b3bd96','31bda50258753e3c5adfba658b4b5c86')";
		$date_arr=$this->four_xun_date($date1,$date2);
		$date_area=array();
		$date_area[3]['end']=date("Y-m-d",strtotime($date_arr['date_2']));
		$date_area[3]['start']=date("Y-m-d",strtotime("-5 day",strtotime($date_arr['date_2'])));
		$date_area[2]['end']=date("Y-m-d",strtotime($date_arr['date_1']));
		$date_area[2]['start']=date("Y-m-d",strtotime("-5 day",strtotime($date_arr['date_1'])));
		
		if(date("d",strtotime($date_area[2]['end']))=='01'){
			
			$date_area[1]['end']=date("Y-m-11",strtotime($date_arr['date_1']));
			$date_area[1]['start']=date("Y-m-d",strtotime("-5 day",strtotime($date_area[1]['end'])));
			$date_area[0]['end']=date("Y-m-21",strtotime($date_arr['date_1']));
			$date_area[0]['start']=date("Y-m-d",strtotime("-5 day",strtotime($date_area[0]['end'])));
		}else if(date("d",strtotime($date_area[2]['end']))=='11'){
			
			$date_area[1]['end']=date("Y-m-21",strtotime($date_arr['date_1']));
			$date_area[1]['start']=date("Y-m-d",strtotime("-5 day",strtotime($date_area[1]['end'])));
			$date_area[0]['end']=date("Y-m-01",strtotime("+1 month",strtotime($date_arr['date_1'])));
			$date_area[0]['start']=date("Y-m-d",strtotime("-5 day",strtotime($date_area[0]['end'])));
		}else if(date("d",strtotime($date_area[2]['end']))=='21'){
			$date_area[1]['end']=date("Y-m-01",strtotime("+1 month",strtotime($date_arr['date_1'])));
			$date_area[1]['start']=date("Y-m-d",strtotime("-5 day",strtotime($date_area[1]['end'])));
			$date_area[0]['end']=date("Y-m-11",strtotime("+1 month",strtotime($date_arr['date_1'])));
			$date_area[0]['start']=date("Y-m-d",strtotime("-5 day",strtotime($date_area[0]['end'])));
		}
		//echo "<pre>";
		//print_r($date_area);
		$where="((run_date >= '".$date_area[0]['start']."' and run_date <= '".$date_area[0]['end']."') or (run_date >= '".$date_area[1]['start']."' and run_date <= '".$date_area[1]['end']."') or (run_date >= '".$date_area[2]['start']."' and run_date <= '".$date_area[2]['end']."') or (run_date >= '".$date_area[3]['start']."' and run_date <= '".$date_area[3]['end']."'))";
		// $sql = "select steel_id, steel_name, variety, specification, material, changerate_tax, the_price_tax, run_date, onlyid from steelhome_gc.steelprice_base join steelhome_gc.steelprice_info where steelprice_base.id = steelprice_info.sb_id and run_date >= '".$start_date."' and run_date <= '".$end_date."' and steelprice_base.is_show = 2 and steelprice_info.is_show = 2 and steelprice_info.onlyid in $onlyid and steel_id in $steel_id and changerate_tax!='' and the_price_tax!='' order by steel_id";
		$sql = "select steel_id, steel_name, variety, specification, material, changerate_tax, the_price_tax, run_date, onlyid from steelhome_gc.steelprice_base join steelhome_gc.steelprice_info where steelprice_base.id = steelprice_info.sb_id and $where and steelprice_base.is_show = 2 and steelprice_info.is_show = 2 and steelprice_info.onlyid in $onlyid and steel_id in $steel_id and changerate_tax!='' and the_price_tax!='' order by steel_id";

    $result = $this->gcdao->query($sql);
	
	$changerate_tax=array();
    foreach ($result as $key => $value) {
		/*$arr[$value['steel_id']][$value['run_date']]['run_date'] = date("Y-m-d",strtotime($value['run_date']));
		$arr[$value['steel_id']][$value['run_date']]['changerate_tax'] = $value['changerate_tax'];*/
		
		$value['run_date']=date("Y-m-d",strtotime($value['run_date']));
		if($value['run_date']>=$date_area[0]['start'] && $value['run_date']<=$date_area[0]['end']){
			$arr[0][$value['steel_id']][$value['run_date']] = $value;
		}
		if($value['run_date']>=$date_area[1]['start'] && $value['run_date']<=$date_area[1]['end']){
			$arr[1][$value['steel_id']][$value['run_date']] = $value;
		}
		if($value['run_date']>=$date_area[2]['start'] && $value['run_date']<=$date_area[2]['end']){
			$arr[2][$value['steel_id']][$value['run_date']] = $value;
			
		}
		
		if($value['run_date']>=$date_area[3]['start'] && $value['run_date']<=$date_area[3]['end']){
			$arr[3][$value['steel_id']][$value['run_date']] = $value;
		}	
			
    }
	$tjygtj=array();
	for($i=0;$i<count($steel_id_arr);$i++){
		$tmp_rundate[0]=$date_area[0]['start'];
		
		foreach($arr[0][$steel_id_arr[$i]] as $key=>$value){
			//$changerate_tax[$steel_id_arr[$i]][]=$value['changerate_tax'];
			if($tmp_rundate[0]<=$value['run_date']){
				$tmp_rundate[0]=$value['run_date'];
				$tjygtj[0][$steel_id_arr[$i]] = $value;
				$tjygtj[0][$steel_id_arr[$i]]['run_date'] = date("Y-m-d",strtotime($value['run_date']));
			}
			
		}
		$tmp_rundate[1]=$date_area[1]['start'];
		foreach($arr[1][$steel_id_arr[$i]] as $key=>$value){
			//$changerate_tax[$steel_id_arr[$i]][]=$value['changerate_tax'];
			if($tmp_rundate[1]<=$value['run_date']){
				$tmp_rundate[1]=$value['run_date'];
				$tjygtj[1][$steel_id_arr[$i]] = $value;
				$tjygtj[1][$steel_id_arr[$i]]['run_date'] = date("Y-m-d",strtotime($value['run_date']));
			}
		}
		$tmp_rundate[2]=$date_area[2]['start'];
		foreach($arr[2][$steel_id_arr[$i]] as $key=>$value){
			//$changerate_tax[$steel_id_arr[$i]][]=$value['changerate_tax'];
			if($tmp_rundate[2]<=$value['run_date']){
				$tmp_rundate[2]=$value['run_date'];
				$tjygtj[2][$steel_id_arr[$i]] = $value;
				$tjygtj[2][$steel_id_arr[$i]]['run_date'] = date("Y-m-d",strtotime($value['run_date']));
			}
		}
		$tmp_rundate[3]=$date_area[3]['start'];
		foreach($arr[3][$steel_id_arr[$i]] as $key=>$value){
			//$changerate_tax[$steel_id_arr[$i]][]=$value['changerate_tax'];
			if($tmp_rundate[3]<=$value['run_date']){
				$tmp_rundate[3]=$value['run_date'];
				$tjygtj[3][$steel_id_arr[$i]] = $value;
				$tjygtj[3][$steel_id_arr[$i]]['run_date'] = date("Y-m-d",strtotime($value['run_date']));
			}
		}
			//print_r($temp);
	}
	/* for($i=0;$i<count($steel_id_arr);$i++){
		if($tjygtj[$steel_id_arr[$i]] != ""){
			$tjygtj[$steel_id_arr[$i]]['changerate_tax'] = $this->zhangdie(array_sum($changerate_tax[$steel_id_arr[$i]]));
		}
	 }
	//echo "<pre>";
	//print_r($tjygtj);
    for($i=0;$i<count($steel_id_arr);$i++){
      if($tjygtj[$steel_id_arr[$i]] == ""){
        $sql = "select steel_id, steel_name, variety, specification, material, the_price_tax, run_date, onlyid from steelhome_gc.steelprice_base join steelhome_gc.steelprice_info where steelprice_base.id = steelprice_info.sb_id  and steelprice_base.is_show = 2 and steelprice_info.is_show = 2 and steelprice_info.onlyid in $onlyid and steel_id='".$steel_id_arr[$i]."' and run_date< '".$start_date."' and changerate_tax!='' and the_price_tax!='' order by steelprice_info.id desc limit 1";
        $result =$this->gcdao->query($sql);
        $tjygtj[$steel_id_arr[$i]] = $result[0];
        $tjygtj[$steel_id_arr[$i]]['run_date'] = date("Y-m-d",strtotime($result[0]['run_date']));
        $tjygtj[$steel_id_arr[$i]]['changerate_tax'] = "--";
              //print_r($result);
      }
    }*/
	
	$tjgtj_table = "<table border=1 cellspacing=0 cellpadding=0 style='text-align:center'>
                            <tr>
                              <td>钢厂</td>
                              <td>品种</td>
                              <td>规格</td>
                              <td>材质</td>
                            <!--  <td>调幅<br/>（含税）</td>
                              <td>执行价格<br/>（含税）</td>
                              <td>执行日期</td>-->
							  <td>".$date_arr['title_time_3']."</td>
							  <td>".$date_arr['title_time_2']."</td>
							  <td>".$date_arr['title_time_1']."</td>
							  <td>".$date_arr['title_time_0']."</td>
                            </tr>
                            <tr>
                              <td>淮钢</td>
                              <td>碳结钢</td>
                              <td>Φ29-85mm</td>
                              <td>45#</td>
                              <td>".$tjygtj[3]['122']['the_price_tax']."</td>
                              <td>".$tjygtj[2]['122']['the_price_tax']."</td>
                              <td>".$tjygtj[1]['122']['the_price_tax']."</td>
							  <td>".$tjygtj[0]['122']['the_price_tax']."</td>
                            </tr>
                            <tr>
                              <td>中天</td>
                              <td>碳结钢</td>
                              <td>Φ29-140mm</td>
                              <td>45#</td>
                              <td>".$tjygtj[3]['151']['the_price_tax']."</td>
                              <td>".$tjygtj[2]['151']['the_price_tax']."</td>
                              <td>".$tjygtj[1]['151']['the_price_tax']."</td>
							  <td>".$tjygtj[0]['151']['the_price_tax']."</td>
                            </tr>
							<tr>
                              <td>元立</td>
                              <td>碳结钢</td>
                              <td>Φ20-30mm</td>
                              <td>45#</td>
                              <td>".$tjygtj[3]['154']['the_price_tax']."</td>
                              <td>".$tjygtj[2]['154']['the_price_tax']."</td>
                              <td>".$tjygtj[1]['154']['the_price_tax']."</td>
							  <td>".$tjygtj[0]['154']['the_price_tax']."</td>
                            </tr>
                            <tr>
                              <td>常州东方特钢</td>
                              <td>碳结钢</td>
                              <td>Φ20-29mm</td>
                              <td>35#/45#</td>
                              <td>".$tjygtj[3]['701']['the_price_tax']."</td>
                              <td>".$tjygtj[2]['701']['the_price_tax']."</td>
                              <td>".$tjygtj[1]['701']['the_price_tax']."</td>
							  <td>".$tjygtj[0]['701']['the_price_tax']."</td>
                            </tr>
                            
                            <tr>
                              <td>杭钢升华库</td>
                              <td>碳结钢</td>
                              <td>Φ29-85mm</td>
                              <td>45#</td>
                              <td>".$tjygtj[3]['1844']['the_price_tax']."</td>
                              <td>".$tjygtj[2]['1844']['the_price_tax']."</td>
                              <td>".$tjygtj[1]['1844']['the_price_tax']."</td>
							  <td>".$tjygtj[0]['1844']['the_price_tax']."</td>
                            </tr>
                            <tr>
                              <td>鲁丽</td>
                              <td>碳结钢</td>
                              <td>Φ25-70mm</td>
                              <td>45#</td>
                              <td>".$tjygtj[3]['1402']['the_price_tax']."</td>
                              <td>".$tjygtj[2]['1402']['the_price_tax']."</td>
                              <td>".$tjygtj[1]['1402']['the_price_tax']."</td>
							  <td>".$tjygtj[0]['1402']['the_price_tax']."</td>
                            </tr>
                            <tr>
                              <td>广富</td>
                              <td>碳结钢</td>
                              <td>Φ65-110mm</td>
                              <td>45#</td>
                              <td>".$tjygtj[3]['1181']['the_price_tax']."</td>
                              <td>".$tjygtj[2]['1181']['the_price_tax']."</td>
                              <td>".$tjygtj[1]['1181']['the_price_tax']."</td>
							  <td>".$tjygtj[0]['1181']['the_price_tax']."</td>
                            </tr>
                          </table><h4 style=\"text-align:left;\">备注：淮钢、中天、元立最新旬定价为预估。其他钢厂为每旬第一天的日定价。
</h4>";
		/*$s_table='<table>';  
        $s_th="<tr>
        <td>&nbsp;</td>
		<td align='center'>钢厂</td>
		<td align='center'>品种</td>
		<td align='center'>规格</td>
		<td align='center'>材质</td>
		<td align='center'>调幅(含税)</td>
		<td align='center'>执行价格(含税)</td>
		<td align='center'>执行日期</td>
		<td align='center'>备注</td>
        </tr>";
		$arr_area=array("华东地区","华北地区","东北地区","中南地区","西北地区","西南地区");*/
		 //foreach($arr_area as $k=>$v){ 
			 //$sql="SELECT `st_code` FROM  `sc_steelcom`  where area='$v'  and st_code!='' order by a_od "; 
			// $arr=$this->gcdao->getones($sql);
			// $str_steelid = implode(",", $arr);
			 /*发布日期-执行日期>=5不汇总*/
            //  $where_date = " and timestampdiff(DAY,`run_date`,`post_date`)<5 ";
			//  $sql = "SELECT graph_show,variety ,sum(changerate_tax) as changerate_tax,the_price_tax,specification,material,message ,steel_id,steel_name,max(run_date) as run_date FROM (SELECT si.* ,steel_name,steel_id,run_date FROM steelprice_base as sb , steelprice_info as si  WHERE sb.id=si.sb_id and  si.is_show=2 and `post_date` >='".$start_date."' and `post_date` <='".$end_date."'  and  steel_id in ($str_steelid) and si.variety='".碳结钢."' ".$where_date." and changerate_tax!='' and changerate!=''  order by run_date desc ) as A group by steel_name,variety,specification,material,quyu,message  order by steel_name asc,variety asc ,run_date asc ,material asc ,specification asc ";
			 //echo "<br>".$sql;
			//$array=$this->gcdao->query($sql);
			//echo "<pre>";  print_r($array); echo "</pre>";
			//$s_tr.=$this->get_gtr($array,$v);

		 //}
		//$s_table=$s_table.$s_th.$s_tr."</table>";
		$this->assign("s_table", $tjgtj_table);

	}
	public function get_gtr($array,$area){
		 $s_tr="";
		 $j = 0;
		 $rowspan = 0;
		for($k=0; $k<count($array);$k++){
    		if($array[$k]['graph_show']=="2"){
    			$rowspan++;
    		}
		}
		$s_td_0="<td rowspan=".$rowspan." align='left' >".$area." </td>";
		for($i=0; $i<count($array);$i++){
    	if($array[$i]['graph_show']=="2"){
    		$run_date=date("Y-m-d",strtotime($array[$i]['run_date']));
	      $changerate_tax=$array[$i]['changerate_tax'];
	      $the_price_tax=$array[$i]['the_price_tax'];
	      $run_price_show=$array[$i]['run_price_show'];
	      $change_show=$array[$i]['change_show'];
	      $message=$array[$i]['message'];

	      if($changerate_tax>0 && substr_count($changerate_tax, "+") == 0){
	        $changerate_tax="+".$changerate_tax;
	      }

	      if($run_price_show==2){
	        $the_price_tax="";
	      }
	      if($change_show==2){
	        $changerate_tax="";
	      }
			 $changerate_tax=$this->zd_color($changerate_tax);
	      if($j!=0){
	      	$s_td_0="";
	      }
	      $steel_id=$array[$i]['steel_id'];
	      $steel_name=$array[$i]['steel_name'];

				if(substr_count($steel_name, "攀钢") == 1){
					$steel_id="501";
				}
				if(substr_count($steel_name, "达钢") == 1){
					$steel_id="506";
				}
				if(substr_count($steel_name, "水钢") == 1){
					$steel_id="507";
				}
				if(substr_count($steel_name, "德胜") == 1){
					$steel_id="508";
				}
				if(substr_count($steel_name, "酒钢") == 1){
	        $steel_id="602";
	      }
	      $s_tr.="<tr>".$s_td_0."
	      <td >".$steel_name."</td>
	      <td>".$array[$i]['variety']."</td>
	      <td>".$array[$i]['specification']."</td>
	      <td>".$array[$i]['material']."</td>
	      <td>".$changerate_tax."</td>
	      <td>".$the_price_tax."</td>
	      <td>".$run_date."</td>
	      <td>".$message."</td>
	      </tr>";
	      $j++;
    	}
    }

	return $s_tr;


	}
	public function gzjyuc($lastMs,$lastMe){
		
		$sql="select * from NgGZJForcast where Type='10' and CDate >'".$lastMe."' and CDate <='".$lastMs."' order by CDate desc limit 1";
		//print_r($sql);
		$info=$this->maindao->getRow($sql);
		if($info){
			if(abs($info['ZhangDie'])<DELTA){
			$info['ZhangDie']="持平";
			}else if($info['ZhangDie']>DELTA){
				$info['ZhangDie']='上涨'.$info['ZhangDie'].'元/吨';
			}else{
				$info['ZhangDie']="下跌".abs($info['ZhangDie']).'元/吨';
			}
		}else{
			$info['ZhangDie']='无';
		}
		//print_r($info);
		$this->assign("info",$info);
	}
	public function workday($date){
		//print_r($date);
		//echo $date="2017-10-07";
		//$date='2017-11-01';
		 $sql="select date,isholiday from steelhome.holiday  where date <='".$date."' order by date desc limit 10"; 
		//$sql="select * from holiday  where date <='2017-10-7' order by date desc limit 20"; 
		$res=$this->maindao->aquery($sql);
		 $i = 1;
		 $last_date = $date;
		 $date=array();
		 
		 while( $i < 8 ){
			$last_date=date("Y-m-d",strtotime("-1 day",strtotime($last_date))); 
			if( isset($res[$last_date]) ){
			
				if($res[$last_date]=="0"){
					
					$date[$i]=$last_date;
					$i++;
					//break;
				}else{
					//continue;
				}
			}else{
				
				$n=date("N",strtotime($last_date));
				if($n==7){
					//continue;
				}else{
					
					$date[$i]=$last_date;
					$i++;
					//break;
				}
				
			}
		 }
		 //	print_r($date);
		 return $date;
		
	   }
	/*public function djjy($start,$end,$ddl){
		//print_r($ddl);
		//print_r("00000");exit;
		$sql = "SELECT the_price_tax,changerate_tax FROM   `steelprice_info`  where variety='碳结钢'  and gcid='122' and ftime>= '".$end."' and ftime <= '".date("Y-m-d")."'";
		//print_r($sql);exit;
		$res_tjg=$this->gcdao->query($sql);
		$the_price_tax=$res_tjg[0]['the_price_tax'];
		$changerate_tax=$res_tjg[0]['changerate_tax'];
		//$date=date("Y-m-d",(strtotime($end) - 3600*24));
		$date1=$start." "."00:00:00";
		$date2=$end." "."00:00:00";
		//print_r($d1);exit;
		if($the_price_tax==""){
			$sql="select * from marketconditions where mastertopid='0822442' and mconmanagedate > '".$date1."' and mconmanagedate < '".$date2."'";
			//print_r($sql);exit;
			$res_tjg_arr=$this->maindao->query($sql);
			$res_tjg=end($res_tjg_arr);
			//前一旬本期价格
			//$sql_id="select * from steelprice_base where `steel_id`='122' and `steel_name`='淮钢'ORDER BY `id` DESC";
			$sql_last = "SELECT the_price_tax,changerate_tax FROM   `steelprice_info`  where variety='碳结钢'  and gcid='122' and ftime< '".$end."' and ftime >= '".$start."'";
			//print_r($sql_last);exit;
			$res_tjg_last=$this->gcdao->query($sql_last);
			$the_price_tax_last=$res_tjg_last[0]['the_price_tax'];
			
			$the_price_tax=$res_tjg['price']+140-$the_price_tax_last;
			//print_r($the_price_tax);
			
			//print_r($res_tjg);exit;
			$changerate_tax=$this->jsxundj($the_price_tax);
		}
		$arr=$this->bxundj($changerate_tax,$ddl);
		
		$this->assign("the_price_tax",$the_price_tax);
		$this->assign("changerate_tax",$changerate_tax);
		return $arr;
	}*///HTML不需要算系统模型定价
	/*public function jsxundj($the_price_tax){
		//print_r($the_price_tax);exit;
		//$the_price_tax = -177;
		$changerate_tax;
		$changerate;
		if($the_price_tax>=100){
			$changerate=(int)($the_price_tax/10+0.5)*10;
		}else if(50<$the_price_tax && $the_price_tax<100){
			$changerate=50;
		}else if($the_price_tax==50){
			$changerate=30;
		}else if(-50<$the_price_tax && $the_price_tax<50){
			$changerate=0;
		}else if(-70<=$the_price_tax && $the_price_tax<-50){
			$changerate=-30;
		}else if (-100<=$the_price_tax && $the_price_tax<-70){
			$changerate=-50;
		}else{
			$changerate=($the_price_tax*2/3/10+0.5)*10;
			//print_r($changerate);
		}
		//$changerate_tax=$changerate_tax
		$changerate_tax=(int)($changerate/10)*10;
		//print_r($changerate_tax);
		return $changerate_tax;
	}*/
	/*public function bxundj($changerate_tax,$ddl,$modelid){
		
		$date;
		if(date("d")<11){
			$date=date("Y-m-21",strtotime("m -1 month"));
			$zd=$changerate_tax;
		}
		if(date("d")<21 && date("d")>10){
			$date=date("Y-m-01");
			if($ddl<30){
				$zd=$changerate_tax-20;
			}
			if($ddl<50 && $ddl>=30){
				$zd=$changerate_tax;
			}
			if( $ddl>=50){
				$zd=$changerate_tax+20;
			}
			
		}
		if(date("d")<=31 && date("d")>20){
			
			$date=date("Y-m-11");
			if($ddl<60){
				
			//	print_r(date("d"));
				$zd=$changerate_tax-50;
				//print_r($ddl);
			}
			if($ddl<70 && $ddl>=60){
				$zd=$changerate_tax-20;
			}
			if( $ddl>=70 && $ddl<80 ){
				$zd=$changerate_tax;
			}
			if($ddl>=80){
				$zd=$changerate_tax+20;
			}
		}
		//print_r($changerate_tax);
		//print_r($ddl);
		$sql="select Value from ng_data_table where DataMark ='Ngyg0004' and dta_ym ='".$date."'";
		$res=$this->ngdao->query($sql);
		$ngyg=$res[0]['Value'];
		//print_r($ngyg);
		$moxdj=$zd+$ngyg;
		$this->assign("moxdj",$moxdj);
		$this->assign("zd",$zd);
		$dj_arr=array('moxdj'=>$moxdj,'zd'=>$zd);
		return $dj_arr;
	}*/
	 public function zd_color($zd){
		if($zd==0){
				 $zd = "<font color=black>".$zd."</font>";
			}else if($zd<0){
				$zd = "<font color=green>".$zd."</font>";
			}else{
				$zd = "<font color=red>".$zd."</font>";
			}
			 return $zd;
	   }
	   public function zd_color1($zd){
		if($zd==0){
				 $zd = "<font color=black>".$zd."</font>";
			}else if($zd<0){
				$zd = "<font color=green>".$zd."</font>";
			}else{
				$zd = "<font color=red>+".$zd."</font>";
			}
			 return $zd;
	   }
	   public function four_xun_date($date1,$date2){
		  $tmp_d1=date("j",strtotime($date1));
		  //print_r($date2);
		if($tmp_d1<='10'){
			$date_1=date("Y-m-01 00:00:00",strtotime($date1));
			$date_2=date("Y-m-21 00:00:00",strtotime("-1 month ",strtotime($date_1)));
			$title_time_0=date("n",strtotime($date1))."月下旬";
			$title_time_1=date("n",strtotime($date1))."月中旬";
			$title_time_2=date("n",strtotime($date_1))."月上旬";
			$title_time_3=date("n",strtotime($date_2))."月下旬";
			
			$s_Y=date("Y年",strtotime($date_2));
			$e_Y=date("Y年",strtotime($date2));
		}else if($tmp_d1>'20'){
			$date_1=date("Y-m-21 00:00:00",strtotime($date1));
			$date_2=date("Y-m-11 00:00:00",strtotime($date_1));
			$title_time_0=date("n",strtotime($date2))."月中旬";
			$title_time_1=date("n",strtotime($date2))."月上旬";
			$title_time_2=date("n",strtotime($date_1))."月下旬";
			$title_time_3=date("n",strtotime($date_2))."月中旬";
			$s_Y=date("Y年",strtotime($date_2));
			$e_Y=date("Y年",strtotime($date2));
		}else if($tmp_d1<='20' && $tmp_d1>='11'){
			$date_1=date("Y-m-11 00:00:00",strtotime($date1));
			$date_2=date("Y-m-01 00:00:00",strtotime($date_1));
			$title_time_0=date("n",strtotime("+1 m",strtotime($date2)))."月上旬";
			$title_time_1=date("n",strtotime($date1))."月下旬";
			$title_time_2=date("n",strtotime($date_1))."月中旬";
			$title_time_3=date("n",strtotime($date_2))."月上旬";
			
			$s_Y=date("Y年",strtotime($date_2));
			$e_Y=date("Y年",strtotime($date2));
		}
		 $date_arr=array(
			 'title_time_0'=>$title_time_0,
			 'title_time_1'=>$title_time_1,
			 'title_time_2'=>$title_time_2,
			 'title_time_3'=>$title_time_3,
			 'date_1'=>$date_1,
			 'date_2'=>$date_2,
			 's_Y'=>$s_Y,
			 'e_Y'=>$e_Y
		 );
		return $date_arr;
	   }
	
} 
?>