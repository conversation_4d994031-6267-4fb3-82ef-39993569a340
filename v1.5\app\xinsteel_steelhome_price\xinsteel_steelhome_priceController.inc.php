<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );

class xinsteel_steelhome_priceController extends AbstractController{
  
    public function __construct(){
        parent::__construct();
        $this->_action->setDao( new xinsteel_steelhome_priceDao("DRCW") );  //drc
        $this->_action->t1Dao=new xinsteel_steelhome_priceDao("MAIN");  //
        $this->_action->homeDao=new xinsteel_steelhome_priceDao("91R");
        $this->_action->gcDao=new xinsteel_steelhome_priceDao("GC");
        $this->_action->smsDao=new xinsteel_steelhome_priceDao("98SMR");
    }

    public function do_get_steelhome_price_date(){
        $this->_action->get_steelhome_price_date( $this->_request );
    }

    public function do_get_steelhome_price_byid(){
        $this->_action->get_steelhome_price_byid( $this->_request );
    }

    public function do_get_steelhome_avgprice(){
        $this->_action->get_steelhome_avgprice( $this->_request );
    }

    /**
     * 新钢短信列表接口
     * Created by zfy.
     * Date:2021/3/10 10:33
     * @param $params
     */
    public function do_xinsteel_sms_list()
    {
        $this->_action->xinsteel_sms_list($this->_request);
    }

    /**
     * 新钢每周市场要闻接口
     * Created by zfy.
     * Date:2021/3/15 17:07
     * @param $params
     */
    public function do_xinsteel_market_week_news_list()
    {
        $this->_action->xinsteel_market_week_news_list($this->_request);
    }

    /**
     * 信息看板
     * Created by zfy.
     * Date:2021/5/18 9:04
     * @param $params
     */
    public function v_sms_list(){
        $this->_action->sms_list($this->_request);
    }

    /**
     * 信息看板分页
     * Created by zfy.
     * Date:2021/5/18 15:00
     * @param $params
     */
    public function do_sms_list_page(){
        $this->_action->sms_list_page($this->_request);
    }

    public function v_xinsteel_news_list(){
        $this->_action->xinsteel_news_list($this->_request);
    }

    public function do_get_news_pdf(){
        $this->_action->get_news_pdf($this->_request);
    }

     /**
     * 钢之家B1、B2、B3（中厚板：3012热轧板卷：3112冷轧板卷：4212）获取方式
     * Created by xr.
     * Date:2021/4/20 17:07
     * @param $params
     */
     public function do_get_steelhome_B1B2B3price(){
        $this->_action->get_steelhome_B1B2B3price( $this->_request );
    }

    public function do_get_steelhome_TSCBprice(){
        $this->_action->get_steelhome_TSCBprice( $this->_request );
    }
    public function do_get_steelhome_TSCB1price(){
        $this->_action->get_steelhome_TSCB1price( $this->_request );
    }
    public function do_get_steelhome_TSCB2price(){
        $this->_action->get_steelhome_TSCB2price( $this->_request );
    }
    public function do_get_steelhome_TSCB3price(){
        $this->_action->get_steelhome_TSCB3price( $this->_request );
    }
    public function do_get_steelhome_TSCB4price(){
        $this->_action->get_steelhome_TSCB4price( $this->_request );
    }
    public function do_get_steelhome_TSCB5price(){
        $this->_action->get_steelhome_TSCB5price( $this->_request );
    }
    public function do_get_steelhome_TSCB6price(){
        $this->_action->get_steelhome_TSCB6price( $this->_request );
    }
    public function do_get_steelhome_avgprice2(){
        $this->_action->get_steelhome_avgprice2( $this->_request );
    }
}
?>