<?php 
// require_once('../../../../steelconf_v3/debug.php');
//require_once ("/etc/steelconf/config/isholiday.php");
class DcWebZhbbAction extends AbstractAction
{ 
    public function __construct()
    {
        parent::__construct();
    } 
	 public function index($params)
    {	
	
		$today=date("Y-m-d");
		if($params['curdate'])
		{
			if(checkdate(date("m",strtotime($params['curdate'])),date("d",strtotime($params['curdate'])),date("Y",strtotime($params['curdate']))))
			{
				$today=$params['curdate'];
			}
			
			
		}
		$type=$params['type'];//1日报2周报
		if(empty($type))
		{
			$type=1;
		}
		if($type==1)
		{
			
			$last_day=$this->workday_gzj($today);//上个工作日
			  $this->scyw($last_day,$today,$type);
			 $bg1=$this->day_zhbb($last_day,$today);
			 $this->assign("bg1",$bg1);
			$this->dzprice_t($last_day,$today);
			$this->gsjg($last_day,$today);
			 $this->gcccjg($today);
			 $this->ryc($last_day,$today);
		}
		else if($type==2)
		{
		   //$today='2018-10-12';
		   $last_day=date("Y-m-d",strtotime($today)-7*24*3600);//上一周
		   $this->scyw($last_day,$today,$type);
		   $bg1=$this->day_zhbb($last_day,$today);
           $this->assign("bg1",$bg1);
		   $this->dzprice_t($last_day,$today);
		   $this->gsjg($last_day,$today);
		   $this->gcccjg($today);
		   
		   
		   $edate2 =  date("Y-m-d",strtotime($last_day."-1days"));
           $sdate2 =  date("Y-m-d",strtotime($edate2."-6days"));
		   $this->gngcsckc($last_day,$today,$sdate2,$edate2);
		   
		   $this->yrlsckc($last_day,$today,$sdate2,$edate2);
		    $this->kgl($last_day,$today,$sdate2,$edate2);
		   $this->zyc($last_day,$today);
		   $this->assign("gs_sdate",date("Y年n月d日",strtotime($last_day)));
		   $this->assign("gs_edate",date("Y年n月d日",strtotime($today)));
		   
		}
		
	 
	  $this->assign("params",$params);
	  $this->assign("type",$type);
    } 

	//保存数据
	public function savezhbb($params)
	{
		//print_r($params);exit;
		
		$today=date("Y-m-d");
		if($params['curdate'])
		{
			if(checkdate(date("m",strtotime($params['curdate'])),date("d",strtotime($params['curdate'])),date("Y",strtotime($params['curdate']))))
			{
				$today=$params['curdate'];
			}
			
			
		}
		
		
		$type=$params['type'];
		if(empty($type))
		{
			$type=1;
		}
		$lastday=$this->workday_gzj($today);//上个工作日
		if($type==1)
		{   $today=$lastday;
			$title=date("Y年n月d日",strtotime($today))."市场综合信息日报";
		}
		else if($type==2)
		{
			
			if(!(date('w',strtotime($today))==6)&&$params['debug']!=1)
			{
				echo "今天不是周六，不能生成市场综合信息周报";
				exit;
			}
			$today=$lastday;//上个工作日
			
			
			$title="市场综合信息周报（".date("Y年n月d日",strtotime($today)-7*24*3600)."-".date("Y年n月d日",strtotime($today))."）";
			
		}

		$url = DC_URL.DCURL."/dcwebzhbb.php?view=index&issave=1&curdate=".$today."&type=".$type;
		
		include '/usr/local/www/libs/phpQuery/phpQuery.php';
		phpQuery::newDocumentFile($url);
		$html=pq("body")->html();
		
		$modelcontent=htmlentities($html, ENT_QUOTES,"UTF-8");
        //$modelcontent=$html;
       // echo $modelcontent;exit;
		$this->ngdao->insertNgGZJ_Zhbb($type ,$title ,$modelcontent ,$today,1 );
		echo "执行完成<br/>";
		echo  $title;
		//print_r($html);
		//print_r( $html);

	}
     function scyw($time1,$time2,$type)
	 {
		 $sql=" select Detail from NgGZJ_News where type='$type' and CDate>='".$time1." 00:00:00' and CDate<='".$time2." 23:59:59' order by CDate desc,id desc limit 1 ";
		 //echo $sql;
		 $res=$this->ngdao->getOne($sql);//测试
		 //$res=$this->drcdao->getOne($sql);//正式
		 $this->assign("scyw",html_entity_decode($res, ENT_QUOTES,"UTF-8"));
     //$this->assign("scyw",$res);
	 }
	 function day_zhbb($lastDate,$datetime){
          $date = date("n月j日",strtotime($datetime));
          $time1 = date("Y-m-d",strtotime($datetime));//本期
          $time2 = date("Y-m-d",strtotime($lastDate));//上期
		  $vid6 = "'072023','073012','122244','118210','678310','598710','073112','H98640'";
          $vid7 = "'6784112','4189101','B963106'";
          $price6 = $this->hq_price_6($time1,$time2,$vid6);
          $price7 = $this->hq_price_7($time1,$time2,$vid7);
		   $datatype = array("SHQHDAY_4","SHQHDAY_99","SHQHDAY_20","SHQHDAY_9","SHQHDAY_19","SHQHDAY_21","SHQHDAY_22","SHQHDAY_23");

          foreach ($datatype as $key => $value) {
            $result[] = $this->heyueselect($time1,$time2,$value);//未完成
          }
		    $content = " <p style='text-align:left'>1、国内钢铁及原燃料现货市场价格变化（单位：元/吨)</p><table border=1 cellspacing=0 cellpadding=0 style='width:734px;text-align:center'>
			  <tr>
				<td style='word-break:break-all;'>品名</td>
				<td>螺纹钢</td>
				<td>中厚板</td>
				<td>热轧板卷</td>
				<td>碳圆</td>
				<td>普碳方坯</td>
				<td>普氏铁矿石价格指数</td>
				<td>废钢</td>
				<td>焦炭</td>
				<td>普氏焦煤</td>
				<td>硅铁</td>
				<td>硅锰</td>
			  </tr>
			  <tr >
				<td>材质<br/>规格</td>
				<td>20mm<br/>HRB400</td>
				<td>20mm<br/>Q235</td>
				<td>5.75mm<br/>Q235</td>
				<td>50mm<br/>45#</td>
				<td>150*150</td>
				<td>62%</td>
				<td>6-10mm</td>
				<td>二级</td>
				<td>峰景矿<br/>硬焦煤<br/>FOB</td>
				<td>FeSi<br/>75-B</td>
				<td>FeMn<br/>68Si18</td>
			  </tr>
			  <tr>
				<td>价格</td>
				<td>".$price6 ['tprice'] ['072023']."</td>
				<td>".$price6 ['tprice'] ['073012']."</td>
				<td>".$price6 ['tprice'] ['073112']."</td>
				<td>".$price6 ['tprice'] ['122244']."</td>
				<td>".$price7 ['tprice'] ['6784112']."</td>
				<td>".$price6 ['tprice'] ['H98640']."</td>
				<td>".$price6 ['tprice'] ['118210']."</td>
				<td>".$price6 ['tprice'] ['678310']."</td>
				<td>".$price7 ['tprice'] ['B963106']."</td>
				<td>".$price6 ['tprice'] ['598710']."</td>
				<td>".$price7 ['tprice'] ['4189101']."</td>
			  </tr>
			  <tr>
				<td>涨跌</td>
				<td>".$price6 ['zd'] ['072023']."</td>
				<td>".$price6 ['zd'] ['073012']."</td>
				<td>".$price6 ['zd'] ['073112']."</td>
				<td>".$price6 ['zd'] ['122244']."</td>
				<td>".$price7 ['zd'] ['6784112']."</td>
				<td>".$price6 ['zd'] ['H98640']."</td>
				<td>".$price6 ['zd'] ['118210']."</td>
				<td>".$price6 ['zd'] ['678310']."</td>
				<td>".$price7 ['zd'] ['B963106']."</td>
				<td>".$price6 ['zd'] ['598710']."</td>
				<td>".$price7 ['zd'] ['4189101']."</td>
			  </tr>
			  <tr >
				<td>市场</td>
				<td>上海</td>
				<td>上海</td>
				<td>上海</td>
				<td>无锡</td>
				<td>唐山</td>
				<td>青岛港</td>
				<td>江苏</td>
				<td>临汾</td>
				<td>澳大利亚</td>
				<td>宁夏</td>
				<td>内蒙古</td>
			  </tr>
			</table><br/>";
			$content .= "<p style='text-align:left'>2、矿煤焦钢期货主力合约价格变化情况（单位：元/吨)</p>";
          $content .= "<table border=1 cellspacing=0 cellpadding=0 style='width:734px;text-align:center'>
                          <tr>
                            <td style='word-break:break-all;'>品种</td>
                            <td>螺纹钢</td>
                            <td>热卷</td>
                            <td>铁矿石</td>
                            <td>焦炭</td>
                            <td>焦煤</td>
                            <td>动力煤</td>
                            <td>硅铁</td>
                            <td>硅锰</td>
                          </tr>
                          <tr>
                            <td>收盘价</td>
                            <td>".$result[0]['dta_6']."</td>
                            <td>".$result[1]['dta_6']."</td>
                            <td>".$result[2]['dta_6']."</td>
                            <td>".$result[3]['dta_6']."</td>
                            <td>".$result[4]['dta_6']."</td>
                            <td>".$result[5]['dta_6']."</td>
                            <td>".$result[6]['dta_6']."</td>
                            <td>".$result[7]['dta_6']."</td>
                          </tr>
                          <tr>
                            <td>涨跌1</td>
                            <td>".$result[0]['dta_8']."</td>
                            <td>".$result[1]['dta_8']."</td>
                            <td>".$result[2]['dta_8']."</td>
                            <td>".$result[3]['dta_8']."</td>
                            <td>".$result[4]['dta_8']."</td>
                            <td>".$result[5]['dta_8']."</td>
                            <td>".$result[6]['dta_8']."</td>
                            <td>".$result[7]['dta_8']."</td>
                          </tr>
                          <tr >
                            <td>结算价</td>
                            <td>".$result[0]['dta_7']."</td>
                            <td>".$result[1]['dta_7']."</td>
                            <td>".$result[2]['dta_7']."</td>
                            <td>".$result[3]['dta_7']."</td>
                            <td>".$result[4]['dta_7']."</td>
                            <td>".$result[5]['dta_7']."</td>
                            <td>".$result[6]['dta_7']."</td>
                            <td>".$result[7]['dta_7']."</td>
                          </tr>
                          <tr >
                            <td>涨跌2</td>
                            <td>".$result[0]['dta_9']."</td>
                            <td>".$result[1]['dta_9']."</td>
                            <td>".$result[2]['dta_9']."</td>
                            <td>".$result[3]['dta_9']."</td>
                            <td>".$result[4]['dta_9']."</td>
                            <td>".$result[5]['dta_9']."</td>
                            <td>".$result[6]['dta_9']."</td>
                            <td>".$result[7]['dta_9']."</td>
                          </tr>
                        </table>";
			return $content;
			
	 }
	 	//大宗商品价格表
	public function dzprice_t($last_day,$now){
		//$last_day=$this->workday_gzj($now);
		$sql="select * from  steelhome.dollar_dzprice where  type in ('1','2','3','4','5','6','9') and date<='".$now." 23:59:59' order by id desc limit 7";
		$res=$this->maindao->query($sql);//正式
	
		$dz_res=array();
		
		foreach($res as $val){			
			//$time = strtotime($val['date']) - 3600*24;
			$forworddate=$last_day;
			$dsql="select value  from  steelhome.dollar_dzprice where  type='".$val['type']."' and date<='".$forworddate." 23:59:59' order by id desc limit 1";
			$dres=$this->maindao->getOne($dsql);//正式
			$down=$val['value']-$dres;
				if($down<0.00 && $down > 0){
					$dz_res['zd'.$val['type']]=number_format($down,4,'.','');
				}else if($down<-0.00 && $down > -0.01){
					$dz_res['zd'.$val['type']]=number_format($down,4,'.','');
				}else{
					$dz_res['zd'.$val['type']]=number_format($down,2,'.','');
					
				}
				$val['zdfu']=$dz_res['zd'.$val['type']]/($val['value']-$dz_res['zd'.$val['type']])*100;
				$dz_res['zdfu'.$val['type']]=round($val['zdfu'],2);
				$dz_res[$val['type']]=number_format($val['value'],2,'.','');
				$dz_res['zdfu'.$val['type']]=$this->zhangdie($dz_res['zdfu'.$val['type']]."%");
				$dz_res['zd'.$val['type']]=$this->zhangdie($dz_res['zd'.$val['type']]);
			
		}
		
		$meiyday=$res[0]['date'];
		//print_r($res);
		if(empty($res)){
			$meiyday=$last_day;
		}
		$meiyday=date("Y年n月j日",strtotime($meiyday));
		//$meiyday=date("Y年n月j日",strtotime($res[0]['date']));
		$this->assign("meiyday",$meiyday);
		$this->assign("last_day",$last_day);
		$this->assign("dz_res",$dz_res);
	}
	
	public function gsjg($last_day,$now)//股市价格
	{
		//$last_day=$this->workday_gzj($now);
		$sql="SELECT * FROM gstock_shpi where  bianma in ('10010124','10010125','10010117','10010110','10010111','10010112','10010116') and datetime<='".$now." 23:59:59' order by datetime DESC ,id DESC limit 7";
		$res=$this->drcdao->query($sql);
		$gs_res=array();
		
		foreach($res as $val){			
			//$time = strtotime($val['datetime']) - 3600*24;
			$forworddate=$last_day;
			$dsql="select mvalue  from  gstock_shpi where  bianma='".$val['bianma']."' and datetime<='".$forworddate." 23:59:59' order by datetime DESC ,id desc limit 1";
			$dres=$this->drcdao->getOne($dsql);//正式
			$down=$val['mvalue']-$dres;
				if($down<0.00 && $down > 0){
					$gs_res['zd'.$val['bianma']]=number_format($down,4,'.','');
				}else if($down<-0.00 && $down > -0.01){
					$gs_res['zd'.$val['bianma']]=number_format($down,4,'.','');
				}else{
					$gs_res['zd'.$val['bianma']]=number_format($down,2,'.','');
					
				}
				$val['zdfu']=$gs_res['zd'.$val['bianma']]/($val['mvalue']-$gs_res['zd'.$val['bianma']])*100;
				$gs_res['zdfu'.$val['bianma']]=round($val['zdfu'],2);
				$gs_res[$val['bianma']]=number_format($val['mvalue'],2,'.','');
				$gs_res['zdfu'.$val['bianma']]=$this->zhangdie($gs_res['zdfu'.$val['bianma']]."%");
				$gs_res['zd'.$val['bianma']]=$this->zhangdie($gs_res['zd'.$val['bianma']]);
			
		}
		$meiyday=$res[0]['datetime'];
		//print_r($res);
		if(empty($res)){
			$meiyday=$last_day;
		}
		$meiyday=date("Y年n月j日",strtotime($meiyday));
		$this->assign("gsday",$meiyday);
		//$this->assign("last_day",$last_day);
		
		
		$this->assign("gs_res",$gs_res);
	}
	public function gcccjg($time1)
	{
		  $lwgtj = array();
          $zhbtj = array();
          $tjgtj = array();
          $jttj = array();
          $ljmtj = array();
          $fgtj = array();
          $gttj = array();
          $gmtj = array();

          
          $this->gctj($time1,$lwgtj,"螺纹钢");
          $this->ctjg($time1,$lwgtj,"螺纹钢");
          $this->gctj($time1,$zhbtj,"中厚板");
          $this->gctj($time1,$tjgtj,"碳结钢");
          $this->ylcg($time1,$jttj,"焦炭");
          $this->ylcg($time1,$ljmtj,"炼焦煤");
          $this->ylcg($time1,$fgtj,"废钢");
          $this->ylcg($time1,$gttj,"硅铁");
          $this->ylcg($time1,$gmtj,"硅锰");
		  
		  $lwgtj_table = "<table border=1 cellspacing=0 cellpadding=0 style='width:734px;text-align:center'>
                        <tr>
                          <td style='word-break:break-all;'>钢厂</td>
                          <td>品种</td>
                          <td>规格</td>
                          <td>材质</td>
                          <td>调幅<br/>（含税）</td>
                          <td>执行价格<br/>（含税）</td>
                          <td>执行日期</td>
                        </tr>
                        <tr>
                          <td>沙钢（厂提）</td>
                          <td>螺纹钢</td>
                          <td>Φ20mm</td>
                          <td>HRB400</td>
                          <td>".$lwgtj['120']['changerate_tax']."</td>
                          <td>".$lwgtj['120']['the_price_tax']."</td>
                          <td>".$lwgtj['120']['run_date']."</td>
                        </tr>
                        <tr>
                          <td>永钢（厂提）</td>
                          <td>螺纹钢</td>
                          <td>Φ20mm</td>
                          <td>HRB400</td>
                          <td>".$lwgtj['121']['changerate_tax']."</td>
                          <td>".$lwgtj['121']['the_price_tax']."</td>
                          <td>".$lwgtj['121']['run_date']."</td>
                        </tr>
                        <tr>
                          <td>中天（厂提）</td>
                          <td>螺纹钢</td>
                          <td>Φ20mm</td>
                          <td>HRB400</td>
                          <td>".$lwgtj['151']['changerate_tax']."</td>
                          <td>".$lwgtj['151']['the_price_tax']."</td>
                          <td>".$lwgtj['151']['run_date']."</td>
                        </tr>
                        <tr>
                          <td>雨花</td>
                          <td>螺纹钢</td>
                          <td>Φ18-20mm</td>
                          <td>HRB400</td>
                          <td>".$lwgtj['1457']['changerate_tax']."</td>
                          <td>".$lwgtj['1457']['the_price_tax']."</td>
                          <td>".$lwgtj['1457']['run_date']."</td>
                        </tr>
                        <tr>
                          <td>长江钢铁（厂提）</td>
                          <td>螺纹钢</td>
                          <td>Φ20mm</td>
                          <td>HRB400</td>
                          <td>".$lwgtj['1186']['changerate_tax']."</td>
                          <td>".$lwgtj['1186']['the_price_tax']."</td>
                          <td>".$lwgtj['1186']['run_date']."</td>
                        </tr>
                        <tr>
                          <td>萍钢</td>
                          <td>螺纹钢</td>
                          <td>Φ18-22mm</td>
                          <td>HRB400</td>
                          <td>".$lwgtj['132']['changerate_tax']."</td>
                          <td>".$lwgtj['132']['the_price_tax']."</td>
                          <td>".$lwgtj['132']['run_date']."</td>
                        </tr>
                        <tr>
                          <td>莱钢永锋</td>
                          <td>螺纹钢</td>
                          <td>Φ18-22mm</td>
                          <td>HRB400</td>
                          <td>".$lwgtj['1182']['changerate_tax']."</td>
                          <td>".$lwgtj['1182']['the_price_tax']."</td>
                          <td>".$lwgtj['1182']['run_date']."</td>
                        </tr>
                        <tr>
                          <td>河钢集团</td>
                          <td>螺纹钢</td>
                          <td>Φ18-25mm</td>
                          <td>HRB400E</td>
                          <td>".$lwgtj['364']['changerate_tax']."</td>
                          <td>".$lwgtj['364']['the_price_tax']."</td>
                          <td>".$lwgtj['364']['run_date']."</td>
                        </tr>
                      </table>";
          


          $zhbtj_table = "<table border=1 cellspacing=0 cellpadding=0 style='width:734px;text-align:center'>
          				<tr>
                          <td style='word-break:break-all;'>钢厂</td>
                          <td>品种</td>
                          <td>规格</td>
                          <td>材质</td>
                          <td>调幅<br/>（含税）</td>
                          <td>执行价格<br/>（含税）</td>
                          <td>执行日期</td>
                        </tr>
          				<tr>
                          <td>宝钢</td>
                          <td>厚板</td>
                          <td>16-50mm*3000</td>
                          <td>Q235</td>
                          <td>".$zhbtj['101']['changerate_tax']."</td>
                          <td>".$zhbtj['101']['the_price_tax']."</td>
                          <td>".$zhbtj['101']['run_date']."</td>
                        </tr>
                        <tr>
                          <td>鞍钢集团</td>
                          <td>中厚板</td>
                          <td>20mm*2000</td>
                          <td>Q235</td>
                          <td>".$zhbtj['401']['changerate_tax']."</td>
                          <td>".$zhbtj['401']['the_price_tax']."</td>
                          <td>".$zhbtj['401']['run_date']."</td>
                        </tr>
                        <tr>
                          <td>首秦</td>
                          <td>中厚板</td>
                          <td>20mm*2500</td>
                          <td>Q235B</td>
                          <td>".$zhbtj['301']['changerate_tax']."</td>
                          <td>".$zhbtj['301']['the_price_tax']."</td>
                          <td>".$zhbtj['301']['run_date']."</td>
                        </tr>
                        <tr>
                          <td>山钢集团</td>
                          <td>中厚板</td>
                          <td>16-40mm</td>
                          <td>Q235B</td>
                          <td>".$zhbtj['1335']['changerate_tax']."</td>
                          <td>".$zhbtj['1335']['the_price_tax']."</td>
                          <td>".$zhbtj['1335']['run_date']."</td>
                        </tr>
                        <tr>
                          <td>湘钢</td>
                          <td>中厚板</td>
                          <td>14-28mm</td>
                          <td>Q235</td>
                          <td>".$zhbtj['205']['changerate_tax']."</td>
                          <td>".$zhbtj['205']['the_price_tax']."</td>
                          <td>".$zhbtj['205']['run_date']."</td>
                        </tr>
                        <tr>
                          <td>沙钢</td>
                          <td>中厚板</td>
                          <td>20mm*2000</td>
                          <td>Q235</td>
                          <td>".$zhbtj['120']['changerate_tax']."</td>
                          <td>".$zhbtj['120']['the_price_tax']."</td>
                          <td>".$zhbtj['120']['run_date']."</td>
                        </tr>
                        <tr>
                          <td>新钢</td>
                          <td>中厚板</td>
                          <td>20mm*2000</td>
                          <td>Q235</td>
                          <td>".$zhbtj['133']['changerate_tax']."</td>
                          <td>".$zhbtj['133']['the_price_tax']."</td>
                          <td>".$zhbtj['133']['run_date']."</td>
                        </tr>
 
                        <tr>
                          <td>河北文丰</td>
                          <td>中厚板</td>
                          <td>20mm*2000</td>
                          <td>Q235</td>
                          <td>".$zhbtj['361']['changerate_tax']."</td>
                          <td>".$zhbtj['361']['the_price_tax']."</td>
                          <td>".$zhbtj['361']['run_date']."</td>
                        </tr>
                        <tr>
                          <td>普阳钢铁</td>
                          <td>中厚板</td>
                          <td>20mm*2200</td>
                          <td>Q235</td>
                          <td>".$zhbtj['348']['changerate_tax']."</td>
                          <td>".$zhbtj['348']['the_price_tax']."</td>
                          <td>".$zhbtj['348']['run_date']."</td>
                        </tr>
                      </table>";
                      

          $tjgtj_table = "<table border=1 cellspacing=0 cellpadding=0 style='width:734px;text-align:center'>
                            <tr>
                              <td style='word-break:break-all;'>钢厂</td>
                              <td>品种</td>
                              <td>规格</td>
                              <td>材质</td>
                              <td>调幅<br/>（含税）</td>
                              <td>执行价格<br/>（含税）</td>
                              <td>执行日期</td>
                            </tr>
                            <tr>
                              <td>淮钢</td>
                              <td>碳结钢</td>
                              <td>Φ29-85mm</td>
                              <td>45#</td>
                              <td>".$tjgtj['122']['changerate_tax']."</td>
                              <td>".$tjgtj['122']['the_price_tax']."</td>
                              <td>".$tjgtj['122']['run_date']."</td>
                            </tr>
                            <tr>
                              <td>中天</td>
                              <td>碳结钢</td>
                              <td>Φ29-140mm</td>
                              <td>45#</td>
                              <td>".$tjgtj['151']['changerate_tax']."</td>
                              <td>".$tjgtj['151']['the_price_tax']."</td>
                              <td>".$tjgtj['151']['run_date']."</td>
                            </tr>
                            <tr>
                              <td>常州东方特钢</td>
                              <td>碳结钢</td>
                              <td>Φ20-29mm</td>
                              <td>35#/45#</td>
                              <td>".$tjgtj['701']['changerate_tax']."</td>
                              <td>".$tjgtj['701']['the_price_tax']."</td>
                              <td>".$tjgtj['701']['run_date']."</td>
                            </tr>
                            <tr>
                              <td>元立</td>
                              <td>碳结钢</td>
                              <td>Φ20-30mm</td>
                              <td>45#</td>
                              <td>".$tjgtj['154']['changerate_tax']."</td>
                              <td>".$tjgtj['154']['the_price_tax']."</td>
                              <td>".$tjgtj['154']['run_date']."</td>
                            </tr>
                            <tr>
                              <td>杭钢升华库</td>
                              <td>碳结钢</td>
                              <td>Φ29-85mm</td>
                              <td>45#</td>
                              <td>".$tjgtj['1844']['changerate_tax']."</td>
                              <td>".$tjgtj['1844']['the_price_tax']."</td>
                              <td>".$tjgtj['1844']['run_date']."</td>
                            </tr>
                            <tr>
                              <td>鲁丽</td>
                              <td>碳结钢</td>
                              <td>Φ25-70mm</td>
                              <td>45#</td>
                              <td>".$tjgtj['1402']['changerate_tax']."</td>
                              <td>".$tjgtj['1402']['the_price_tax']."</td>
                              <td>".$tjgtj['1402']['run_date']."</td>
                            </tr>
                            <tr>
                              <td>广富</td>
                              <td>碳结钢</td>
                              <td>Φ65-110mm</td>
                              <td>45#</td>
                              <td>".$tjgtj['1181']['changerate_tax']."</td>
                              <td>".$tjgtj['1181']['the_price_tax']."</td>
                              <td>".$tjgtj['1181']['run_date']."</td>
                            </tr>
                          </table>";

            $content .= "<p>1、钢材出厂价格调整情况<br/>1)螺纹钢</p>";
            $content .= $lwgtj_table;
            $content .="<p>2)中厚板</p>";
            $content .= $zhbtj_table;
            $content .="<p>3)碳结圆钢</p>";
            $content .= $tjgtj_table;

            $jtylcg_table = "<table border=1 cellspacing=0 cellpadding=0 style='width:734px;text-align:center'>
                        <tr>
                          <td style='word-break:break-all;'>钢厂</td>
                          <td>品名</td>
                          <td>调幅</td>
                          <td>本期价格</td>
                          <td>执行日期</td>
                          <td>备注</td>
                        </tr>
                        <tr>
                          <td>马钢</td>
                          <td>准一级焦炭</td>
                          <td>".$jttj['113']['changerate']."</td>
                          <td>".$jttj['113']['the_price']."</td>
                          <td>".$jttj['113']['run_date']."</td>
                          <td>".$jttj['113']['beizhu']."</td>
                        </tr>
                        <tr>
                          <td>沙钢</td>
                          <td>二级冶金焦</td>
                          <td>".$jttj['120']['changerate']."</td>
                          <td>".$jttj['120']['the_price']."</td>
                          <td>".$jttj['120']['run_date']."</td>
                          <td>".$jttj['120']['beizhu']."</td>
                        </tr>
                        <tr>
                          <td>永钢</td>
                          <td>准一级冶金焦</td>
                          <td>".$jttj['121']['changerate']."</td>
                          <td>".$jttj['121']['the_price']."</td>
                          <td>".$jttj['121']['run_date']."</td>
                          <td>".$jttj['121']['beizhu']."</td>
                        </tr>
                        <tr>
                          <td>中天</td>
                          <td>准一级冶金焦</td>
                          <td>".$jttj['151']['changerate']."</td>
                          <td>".$jttj['151']['the_price']."</td>
                          <td>".$jttj['151']['run_date']."</td>
                          <td>".$jttj['151']['beizhu']."</td>
                        </tr>
                        
                        <tr>
                          <td>九江萍钢</td>
                          <td>二级冶金焦</td>
                          <td>".$jttj['1801']['changerate']."</td>
                          <td>".$jttj['1801']['the_price']."</td>
                          <td>".$jttj['1801']['run_date']."</td>
                          <td>".$jttj['1801']['beizhu']."</td>
                        </tr>
                        <tr>
                          <td>河钢集团</td>
                          <td>一级冶金焦</td>
                          <td>".$jttj['364']['changerate']."</td>
                          <td>".$jttj['364']['the_price']."</td>
                          <td>".$jttj['364']['run_date']."</td>
                          <td>".$jttj['364']['beizhu']."</td>
                        </tr>
                        <tr>
                          <td>日照钢铁</td>
                          <td>强二级冶金焦（干熄焦）</td>
                          <td>".$jttj['130']['changerate']."</td>
                          <td>".$jttj['130']['the_price']."</td>
                          <td>".$jttj['130']['run_date']."</td>
                          <td>".$jttj['130']['beizhu']."</td>
                        </tr>
                    	</table>";
            
            $ljmylcg_table = "<table border=1 cellspacing=0 cellpadding=0 style='width:734px;text-align:center'>
                        <tr>
                          <td style='word-break:break-all;'>钢厂</td>
                          <td>品名</td>
                          <td>调幅</td>
                          <td>本期价格</td>
                          <td>执行日期</td>
                          <td>备注</td>
                        </tr>
                        <tr>
                          <td>马钢</td>
                          <td>主焦煤 A<11%,V:27-27%,S<1.8%,G>85%,Mt<8%</td>
                          <td>".$ljmtj['113']['changerate']."</td>
                          <td>".$ljmtj['113']['the_price']."</td>
                          <td>".$ljmtj['113']['run_date']."</td>
                          <td>".$ljmtj['113']['beizhu']."</td>
                        </tr>
                        <tr>
                          <td>芜湖新兴铸管</td>
                          <td>主焦煤 A<10.5%,S2.0%,V24%,G90,Y16%,Mt:8%</td>
                          <td>".$ljmtj['741']['changerate']."</td>
                          <td>".$ljmtj['741']['the_price']."</td>
                          <td>".$ljmtj['741']['run_date']."</td>
                          <td>".$ljmtj['741']['beizhu']."</td>
                        </tr>
                        <tr>
                          <td>沙钢</td>
                          <td>主焦煤 A<10%,V:20-22%,S<0.5%,G>65%,Mt<8%</td>
                          <td>".$ljmtj['120']['changerate']."</td>
                          <td>".$ljmtj['120']['the_price']."</td>
                          <td>".$ljmtj['120']['run_date']."</td>
                          <td>".$ljmtj['120']['beizhu']."</td>
                        </tr>
                        <tr>
                          <td>河钢邯钢</td>
                          <td>主焦煤 A:9.5-10%,V:24-25%,S<1%,G>75</td>
                          <td>".$ljmtj['304']['changerate']."</td>
                          <td>".$ljmtj['304']['the_price']."</td>
                          <td>".$ljmtj['304']['run_date']."</td>
                          <td>".$ljmtj['304']['beizhu']."</td>
                        </tr></table>";

                    $fgylcg_table = "<table border=1 cellspacing=0 cellpadding=0 style='width:734px;text-align:center'>
                        <tr>
                          <td style='word-break:break-all;'>钢厂</td>
                          <td>品名</td>
                          <td>调幅</td>
                          <td>本期价格</td>
                          <td>执行日期</td>
                          <td>备注</td>
                        </tr>
                        <tr>
                          <td>沙钢</td>
                          <td>重一</td>
                          <td>".$fgtj['120']['changerate']."</td>
                          <td>".$fgtj['120']['the_price']."</td>
                          <td>".$fgtj['120']['run_date']."</td>
                          <td>".$fgtj['120']['beizhu']."</td>
                        </tr>
                        <tr>
                          <td>永钢</td>
                          <td>重一</td>
                          <td>".$fgtj['121']['changerate']."</td>
                          <td>".$fgtj['121']['the_price']."</td>
                          <td>".$fgtj['121']['run_date']."</td>
                          <td>".$fgtj['121']['beizhu']."</td>
                        </tr>
                        <tr>
                          <td>中天</td>
                          <td>重一</td>
                          <td>".$fgtj['151']['changerate']."</td>
                          <td>".$fgtj['151']['the_price']."</td>
                          <td>".$fgtj['151']['run_date']."</td>
                          <td>".$fgtj['151']['beizhu']."</td>
                        </tr>
                        <tr>
                          <td>元立</td>
                          <td>重一</td>
                          <td>".$fgtj['154']['changerate']."</td>
                          <td>".$fgtj['154']['the_price']."</td>
                          <td>".$fgtj['154']['run_date']."</td>
                          <td>".$fgtj['154']['beizhu']."</td>
                        </tr>
                        <tr>
                          <td>马钢</td>
                          <td>重废</td>
                          <td>".$fgtj['113']['changerate']."</td>
                          <td>".$fgtj['113']['the_price']."</td>
                          <td>".$fgtj['113']['run_date']."</td>
                          <td>".$fgtj['113']['beizhu']."</td>
                        </tr>
                        <tr>
                          <td>芜湖新兴铸管</td>
                          <td>重废</td>
                          <td>".$fgtj['741']['changerate']."</td>
                          <td>".$fgtj['741']['the_price']."</td>
                          <td>".$fgtj['741']['run_date']."</td>
                          <td>".$fgtj['741']['beizhu']."</td>
                        </tr></table>";
                       
             $thjylcg_table = "<table border=1 cellspacing=0 cellpadding=0 style='width:734px;text-align:center'>
                        <tr>
                          <td style='word-break:break-all;'>钢厂</td>
                          <td>品名</td>
                          <td>牌号</td>
                          <td>调幅</td>
                          <td>本期价格</td>
                          <td>执行日期</td>
                          <td>备注</td>
                        </tr>
                        <tr>
                          <td rowspan=2>沙钢</td>
                          <td>硅铁</td>
                          <td>FeSi75-B</td>
                          <td>".$gttj['120']['changerate']."</td>
                          <td>".$gttj['120']['the_price']."</td>
                          <td>".$gttj['120']['run_date']."</td>
                          <td>".$gttj['120']['beizhu']."</td>
                        </tr>
                        <tr>
                          <td>硅锰</td>
                          <td>FeMn68Sil8</td>
                          <td>".$gmtj['120']['changerate']."</td>
                          <td>".$gmtj['120']['the_price']."</td>
                          <td>".$gmtj['120']['run_date']."</td>
                          <td>".$gmtj['120']['beizhu']."</td>
                        </tr>
                        <tr>
                          <td rowspan=2>永钢</td>
                          <td>硅铁</td>
                          <td>FeSi75-B</td>
                          <td>".$gttj['121']['changerate']."</td>
                          <td>".$gttj['121']['the_price']."</td>
                          <td>".$gttj['121']['run_date']."</td>
                          <td>".$gttj['121']['beizhu']."</td>
                        </tr>
                        <tr>
                          <td>硅锰</td>
                          <td>FeMn68Sil8</td>
                          <td>".$gmtj['121']['changerate']."</td>
                          <td>".$gmtj['121']['the_price']."</td>
                          <td>".$gmtj['121']['run_date']."</td>
                          <td>".$gmtj['121']['beizhu']."</td>
                        </tr>
                        <tr>
                          <td rowspan=2>中天</td>
                          <td>硅铁</td>
                          <td>FeSi75-B</td>
                          <td>".$gttj['151']['changerate']."</td>
                          <td>".$gttj['151']['the_price']."</td>
                          <td>".$gttj['151']['run_date']."</td>
                          <td>".$gttj['151']['beizhu']."</td>
                        </tr>
                        <tr>
                          <td>硅锰</td>
                          <td>FeMn68Sil8</td>
                          <td>".$gmtj['151']['changerate']."</td>
                          <td>".$gmtj['151']['the_price']."</td>
                          <td>".$gmtj['151']['run_date']."</td>
                          <td>".$gmtj['151']['beizhu']."</td>
                        </tr>
                        <tr>
                          <td rowspan=2>马钢</td>
                          <td>硅铁</td>
                          <td>FeSi75-B</td>
                          <td>".$gttj['113']['changerate']."</td>
                          <td>".$gttj['113']['the_price']."</td>
                          <td>".$gttj['113']['run_date']."</td>
                          <td>".$gttj['113']['beizhu']."</td>
                        </tr>
                        <tr>
                          <td>硅锰</td>
                          <td>FeMn68Si18</td>
                          <td>".$gmtj['113']['changerate']."</td>
                          <td>".$gmtj['113']['the_price']."</td>
                          <td>".$gmtj['113']['run_date']."</td>
                          <td>".$gmtj['113']['beizhu']."</td>
                        </tr>
                        <tr>
                          <td rowspan=2>河钢</td>
                          <td>硅铁</td>
                          <td>FeSi75-B</td>
                          <td>".$gttj['364']['changerate']."</td>
                          <td>".$gttj['364']['the_price']."</td>
                          <td>".$gttj['364']['run_date']."</td>
                          <td>".$gttj['364']['beizhu']."</td>
                        </tr>
                        <tr>
                          <td>硅锰</td>
                          <td>FeMn68Sil8</td>
                          <td>".$gmtj['364']['changerate']."</td>
                          <td>".$gmtj['364']['the_price']."</td>
                          <td>".$gmtj['364']['run_date']."</td>
                          <td>".$gmtj['364']['beizhu']."</td>
                        </tr></table>";
                        
              $content .= "<p>2、原燃料采购价格调整情况<br/>
                            1)焦炭<br/></p>";
              $content .= $jtylcg_table;
              $content .= "<p>2)炼焦煤</p>";
              $content .= $ljmylcg_table;
              $content .= "<p>3)废钢</p>";
              $content .= $fgylcg_table;
              $content .="<p>4)铁合金</p>";
              $content .=$thjylcg_table;
		     $this->assign("gcccjg",$content);
		
	}
	
	public function ryc($sdate,$edate)
	{
		
		$arr=array(
		'1'=>'螺纹钢',
		'2'=>'中厚板',
		'3'=>'碳结圆钢',
		'4'=>'带钢',
		'5'=>'铁矿石',
		'6'=>'焦炭',
		'7'=>'硅铁',
		'23'=>'硅锰',
		
		);
		$jingdu1 = 0.001;
        $jingdu2 = -0.001;
		      $forcast = array();    
              $forcast_str = array();     
              $sql = "select * from NgGZJForcast where CDate>='$edate 00:00:00' and CDate<='$edate 23:59:59' and (Type < 8 or Type = 23)  order by  Type ";
			  $result = $this->maindao->query($sql);
			  if(!$result)
			  {
				   $sql = "select * from NgGZJForcast where CDate>='$sdate 00:00:00' and CDate<='$sdate 23:59:59' and (Type < 8 or Type = 23)  order by  Type ";
			       $result = $this->maindao->query($sql);
			  }
              foreach ($result as $key => $value) {
                ///$forcast[$value['Type']] = $value;
				$forcast_str = $value["Detail"];
                  if($value["ZhangDie"]>=$jingdu1){
                    $zhangdie = "上涨".abs($value["ZhangDie"])."元/吨。";
                  }elseif($value["ZhangDie"]<=$jingdu2){
                    $zhangdie = "下跌".abs($value["ZhangDie"])."元/吨。";
                  }else{
                    $zhangdie = "持平。";
                  }
                   $content.= "<p>".($key+1)."、".$arr[$value['Type']]."：".$forcast_str .$zhangdie."</p>";
              }
			    $content.="<br/>";
				if(!$content)$content='无';
				$this->assign("ryc",$content);	  
					  
	}
	
	public function zyc($sdate,$edate)
	{
		$arr=array(
		'11'=>'螺纹钢',
		'12'=>'碳结圆钢',
		'13'=>'板材',
		'14'=>'铁矿石',
		'15'=>'焦炭',
		'16'=>'硅铁',
		'24'=>'螺纹钢',
		
		);
		$jingdu1 = 0.001;
        $jingdu2 = -0.001;
		      $forcast = array();    
              $forcast_str = array();  
              $sql = "select Detail ,ZhangDie,Type from NgGZJForcast where CDate>='$sdate 23:59:59' and CDate<='$edate 23:59:59' and ((Type > 10 and Type < 17) or Type = 24)  order by CDate, Type ";
			  $result = $this->maindao->query($sql);
			  $shuzu=array();
			  $result1=array();
			  foreach ($result as $key => $value) 
			  {
				  if(!in_array($value['Type'],$shuzu))
				  {
					  $shuzu[]=$value['Type'];
					  $result1[]=$value;
					  
				  }
			  }
			  array_multisort($shuzu,SORT_ASC,SORT_NUMERIC,$result);
			  $content='';
              foreach ($result as $key => $value) {
                ///$forcast[$value['Type']] = $value;
				$forcast_str = $value["Detail"];
                  if($value["ZhangDie"]>=$jingdu1){
                    $zhangdie = "上涨".abs($value["ZhangDie"])."元/吨。";
                  }elseif($value["ZhangDie"]<=$jingdu2){
                    $zhangdie = "下跌".abs($value["ZhangDie"])."元/吨。";
                  }else{
                    $zhangdie = "持平。";
                  }
                   $content.="<p>". ($key+1)."、".$arr[$value['Type']]."：".$forcast_str .$zhangdie."</p>";
              }
			  if(!$content)$content='无';
	          $content.="<br/>";
			$this->assign("zyc",$content);	  
					  
	}
	
	
	//获取钢之家的前一个工作日
	public function workday_gzj($params){
		
		$date;
		include_once("/etc/steelconf/config/isholiday.php");

		for($i=0;$i<=20;$i++){
			$last_day=date("Y-m-d",(strtotime($params) - 3600*24*($i+1)));
			//$isexist = file_get_contents("https://holiday.steelhome.com/isholiday.php?date=".$last_day);
			$isexist=_isholiday($last_day);
			//echo "123";
			if($isexist=='1'){
				//print_r($last_day);
				continue;
			}else{
				$date=$last_day;
				break;
			}
		}
		
		return $date;
	}
     function zhangdie($int){
      if(!is_numeric($int))
      {
        return  "<font >".$int."</font>";
      }
		$intstr = "";
		if($int<0){
			$intstr = "<font class='greenfont'>↓".abs($int)."</font>";
		}elseif($int>0){
			$intstr = "<font class='redfont'>↑".abs($int)."</font>";
		}elseif($int==""){
			$intstr = "―";
		}else{
			$intstr = "<font >".$int."</font>";
		}
		return $intstr;
		
  }
    function heyueselect($date,$date1,$data_item){
    // $sql = "SELECT  MAX( CAST( dta_11 AS SIGNED INTEGER ) )  AS `dta_11` , `dta_ym`   FROM `data_table` WHERE `dta_ym` ='$date' and `dta_type`='$data_item' AND dta_1 not like '%小计%' AND dta_1 not like '%总计%' GROUP BY `dta_ym` order by TO_DAYS(`dta_ym`) asc ";
    // $array = $this->drcdao->query ( $sql );
    // $dta_11 = $array [0] ["dta_11"];
    // $dta_ym = $array [0] ["dta_ym"];
    $sql = "SELECT * FROM `data_table` WHERE  `dta_type`='$data_item'  and `dta_ym`>='$date1 00:00:00' and `dta_ym`<='$date 23:59:59' and `dta_maxValStatus`='1' order by dta_ym desc limit 1 ";//本期
    $arr = $this->drcdao->getrow ( $sql );
	$sql = "SELECT * FROM `data_table` WHERE  `dta_type`='$data_item'  and `dta_ym`<='$date1 23:59:59' and `dta_maxValStatus`='1' order by dta_ym desc limit 1 ";//上期
    $arr1 = $this->drcdao->getrow ( $sql );
	
    $arr['dta_8'] = $this->zhangdie((float)$arr['dta_6']-(float)$arr1['dta_6']);//收盘价差
    $arr['dta_9'] = $this->zhangdie((float)$arr['dta_7']-(float)$arr1['dta_7']);//结算价差
    return $arr;
  }
  
  function gctj($date,&$arr,$pz){
    if($pz == "螺纹钢"){
      $steel_id = "('1457','132','1182','364')";
      $steel_id_arr = array('1457','132','1182','364');
      $onlyid = "('0cebcfa531241c65ba87a4006bbf39da','0ec508b3666b8a6ef353ed51cad316c3','1606c697ce47db674bf1a52a6c593fe8')";
    }else if($pz == "中厚板"){
      $steel_id = "('1335','205','120','301','133','361','348','101','401')";
      $steel_id_arr = array('1335','205','120','301','133','361','348','101','401');
      $onlyid = "('9f49810bacda42ae80e79d9a76f35545','d47f8d59a54c53ecdfff5fc4cff48317','f85aa06ced69478aceec31111afaf986','e8aa1d1ca0b7f3e06f10b66b348777cf','0b37f732caa6698f383c3dc7d8507326','4fdf367092744647047f4e831919005f','e67527a524cd38e69fec59e74c269e88')";
    }else if($pz == "碳结钢"){
      $steel_id = "('122','151','701','154','1844','1402','1181')";
      $steel_id_arr = array('122','151','701','154','1844','1402','1181');
      $onlyid = "('76d3cd4d3ec329a817d6c5b13e0a7af1','fdc82ccf562f79a69c327a58873b7315','7aca9a4d8cd8468fa2f16732416f6001','f8cb82417b9ae52cc3c328c08b557f75','9bd93685e3313d7058b1259022392950','ff4f1d0b9d61adfbdd39e0bd57b3bd96','31bda50258753e3c5adfba658b4b5c86')";
    }
    $sql = "select steel_id, steel_name, variety, specification, material, changerate_tax, the_price_tax, run_date, onlyid from steelhome_gc.steelprice_base join steelhome_gc.steelprice_info where steelprice_base.id = steelprice_info.sb_id and run_date >= '".$date." 00:00:00' and run_date <= '".$date." 23:59:59' and steelprice_base.is_show = 2 and steelprice_info.is_show = 2 and steelprice_info.changerate_tax != '' and steelprice_info.changerate != '' and steelprice_info.onlyid in $onlyid and steel_id in $steel_id order by steel_id";
    $result = $this->drcdao->query($sql);
    foreach ($result as $key => $value) {
      $arr[$value['steel_id']] = $value;
      $arr[$value['steel_id']]['run_date'] = date("Y-m-d",strtotime($value['run_date']));
      $arr[$value['steel_id']]['changerate_tax'] = $this->zhangdie($value['changerate_tax']);
    }
    for($i=0;$i<count($steel_id_arr);$i++){
      if($arr[$steel_id_arr[$i]] == ""){
        $sql = "select steel_id, steel_name, variety, specification, material, the_price_tax, run_date, onlyid from steelhome_gc.steelprice_base join steelhome_gc.steelprice_info where steelprice_base.id = steelprice_info.sb_id and steelprice_base.is_show = 2 and steelprice_info.is_show = 2 and steelprice_info.changerate_tax !='' and steelprice_info.changerate != '' and steelprice_info.onlyid in $onlyid and steel_id='".$steel_id_arr[$i]."' order by steelprice_info.id desc limit 1";
        $result = $this->drcdao->query($sql);
        $arr[$steel_id_arr[$i]] = $result[0];
        $arr[$steel_id_arr[$i]]['run_date'] = date("Y-m-d",strtotime($result[0]['run_date']));
        $arr[$steel_id_arr[$i]]['changerate_tax'] = "―";
              //print_r($result);
      }
    }
  }
   
  function gctj_week($sdate,$edate,&$arr,$pz){
    if($pz == "螺纹钢"){
      $steel_id = "('1457','132','1182','364')";
      $steel_id_arr = array('1457','132','1182','364');
      $onlyid = "('0cebcfa531241c65ba87a4006bbf39da','0ec508b3666b8a6ef353ed51cad316c3','1606c697ce47db674bf1a52a6c593fe8')";
    }else if($pz == "中厚板"){
      $steel_id = "('1335','205','120','301','133','134','361','348','101','401')";
      $steel_id_arr = array('1335','205','120','301','133','134','361','348','101','401');
      $onlyid = "('9f49810bacda42ae80e79d9a76f35545','d47f8d59a54c53ecdfff5fc4cff48317','f85aa06ced69478aceec31111afaf986','e8aa1d1ca0b7f3e06f10b66b348777cf','0b37f732caa6698f383c3dc7d8507326','4fdf367092744647047f4e831919005f','e67527a524cd38e69fec59e74c269e88')";
    }else if($pz == "碳结钢"){
      $steel_id = "('122','151','701','154','1844','1402','1181')";
      $steel_id_arr = array('122','151','701','154','1844','1402','1181');
      $onlyid = "('76d3cd4d3ec329a817d6c5b13e0a7af1','fdc82ccf562f79a69c327a58873b7315','7aca9a4d8cd8468fa2f16732416f6001','f8cb82417b9ae52cc3c328c08b557f75','9bd93685e3313d7058b1259022392950','ff4f1d0b9d61adfbdd39e0bd57b3bd96','31bda50258753e3c5adfba658b4b5c86')";
    }
    $sql = "select steel_id, steel_name, variety, specification, material, changerate_tax as changerate_tax2, the_price_tax, run_date, onlyid from steelhome_gc.steelprice_base join steelhome_gc.steelprice_info where steelprice_base.id = steelprice_info.sb_id and run_date >= '".$sdate." 00:00:00' and run_date <= '".$edate." 23:59:59' and steelprice_base.is_show = 2 and steelprice_info.is_show = 2  and steelprice_info.changerate !=''  and steelprice_info.changerate_tax !='' and steelprice_info.onlyid in $onlyid and steel_id in $steel_id order by run_date";
    $result = $this->drcdao->query($sql); 
	//echo "<pre/>";print_r($result);
    $arr = array();
    $arr2 = array();
    foreach ($result as $key => $value) {
      $arr[$value['steel_id']] = $value;
      $arr[$value['steel_id']]['run_date'] = date("Y-m-d",strtotime($value['run_date']));
      $arr2[$value['steel_id']][] = $value['changerate_tax2'];
      $arr[$value['steel_id']]['changerate_tax'] = $arr2[$value['steel_id']];
    }
    foreach($arr as &$tmp){
        $sum = $tmp['changerate_tax'];
        //$sum = array_shift($sum);
        $tmp['changerate_tax'] = array_sum($sum);
        $tmp['changerate_tax'] = $this->zhangdie($tmp['changerate_tax']);
    }
    if($pz == "碳结钢"){
    }else{
        for($i=0;$i<count($steel_id_arr);$i++){
          if($arr[$steel_id_arr[$i]] == ""){
            $sql = "select steel_id, steel_name, variety, specification, material, the_price_tax, run_date, onlyid from steelhome_gc.steelprice_base join steelhome_gc.steelprice_info where steelprice_base.id = steelprice_info.sb_id and steelprice_base.is_show = 2 and steelprice_info.is_show = 2 and steelprice_info.changerate !=''  and steelprice_info.changerate_tax !=''  and steelprice_info.onlyid in $onlyid and steel_id='".$steel_id_arr[$i]."' order by steelprice_info.id desc limit 1";
            $result = $this->drcdao->query($sql);
            $arr[$steel_id_arr[$i]] = $result[0];
            $arr[$steel_id_arr[$i]]['run_date'] = date("Y-m-d",strtotime($result[0]['run_date']));
            $arr[$steel_id_arr[$i]]['changerate_tax'] = "―";
                  //print_r($result);
          }
        }
    }
  }

  function ctjg($date,&$arr,$pz){
    if($pz == "螺纹钢"){
      $steel_id = "('120','121','151','1186')";
      $steel_id_arr = array('120','121','151','1186');
      $onlyid = "('25b67ebabb304fdb9298942ad2528388')";
    }
    $sql = "select steel_id, steel_name, variety, specification, material, changerate_tax, the_price_tax, run_date, onlyid from steelhome_gc.steelprice_base2 join steelhome_gc.ct_price_info where steelprice_base2.id = ct_price_info.sb_id and run_date >= '".$date." 00:00:00' and run_date <= '".$date." 23:59:59' and steelprice_base2.is_show = 2 and ct_price_info.is_show = 2 and ct_price_info.changerate_tax != '' and ct_price_info.changerate != '' and ct_price_info.onlyid in $onlyid and steel_id in $steel_id order by steel_id";
    $result = $this->drcdao->query($sql);
    foreach ($result as $key => $value) {
      $arr[$value['steel_id']] = $value;
       $arr[$value['steel_id']]['run_date'] = date("Y-m-d",strtotime($value['run_date']));
       $arr[$value['steel_id']]['changerate_tax'] = $this->zhangdie($value['changerate_tax']);
    }
    for($i=0;$i<count($steel_id_arr);$i++){
      if($arr[$steel_id_arr[$i]] == ""){
        $sql = "select steel_id, steel_name, variety, specification, material, the_price_tax, run_date, onlyid from steelhome_gc.steelprice_base2 join steelhome_gc.ct_price_info where steelprice_base2.id = ct_price_info.sb_id and steelprice_base2.is_show = 2 and ct_price_info.is_show = 2 and ct_price_info.changerate_tax != '' and ct_price_info.changerate != '' and ct_price_info.onlyid in $onlyid and steel_id='".$steel_id_arr[$i]."' order by ct_price_info.id desc limit 1";
        $result = $this->drcdao->query($sql);
        $arr[$steel_id_arr[$i]] = $result[0];
        $arr[$steel_id_arr[$i]]['run_date'] = date("Y-m-d",strtotime($result[0]['run_date']));
        $arr[$steel_id_arr[$i]]['changerate_tax'] = "―";
              //print_r($result);
      }
    }
  }

  function ylcg($date,&$arr,$pz){
    if($pz == "焦炭"){
      $steel_id = "('120','121','151','1801','364','130','113')";
      $steel_id_arr = array('120','121','151','1801','364','130','113');
      $onlyid = "('6512bd43d9caa6e02c990b0a82652dca','2545bf20f3355a851e6490f8cdee74a3','1ed9139e12ae6d6f9474ef2eb989e305','14f501702ec44df127de2fbb701a2d21','ab219b30ff80645526b0daaa6198b19b','e906a1f90bd6d153f058ec036dda40ac','b7b705b4ce936a06e7e7019217ed2f39')";
    }else if($pz == "炼焦煤"){
      $steel_id = "('113','120','741','304')";
      $steel_id_arr = array('113','120','741','304');
      $onlyid = "('827032545bb05674b9f1d6e418c70969','fcbb18f2783e8057faf446002588b217','e2748782ceefa20e567992faa938f2ff','5cb71d87a21d04ec9063c284ade27b95')";
    }else if($pz == "废钢"){
      $steel_id = "('120','121','151','154','113','741')";
      $steel_id_arr = array('120','121','151','154','113','741');
      $onlyid = "('333f16b4de4d5be66455c5d444be7450','d8ca78d40381044bd4596d26c0ca643d','bebf449167ca085ec77050f0dc042c24','9998428a5c0fea85330271d39044c3ca','27297569318c6d3d96c55ae1a942c1ec','79353c8419b2924adbb8b0a2c06a29cd')";
    }else if($pz == "硅铁"){
      $steel_id = "('120','121','151','113','364')";
      $steel_id_arr = array('120','121','151','113','364');
      $onlyid = "('b686f23e8849d6b16b27faee9bc509bd','fdd69965bdd0e879d0105e877e1ae5db','090023b11545bb5dc3af505ca0099f8a','5f7e256374cffa4b18ffead48b4610b9','923c6ed2ffddb35c274140fb86df8c54')";
    }else if($pz == "硅锰"){
      $steel_id = "('120','121','151','113','364')";
      $steel_id_arr = array('120','121','151','113','364');
      $onlyid = "('e53f2b509e269c21498c0b58cc2700bb','05f8cafb0b9c691abbcdbcbdc6575dce','88fcd35dbcdf3e1195b38b5a47e661aa','c2c7db579ae863574723ce9bf853497f','85dee82d6323af2a2ed74c3b5a1819ed')";
    }
    $sql = "select steel_id, steel_name, variety, specification, changerate, changerate_tax, the_price, the_price_tax, run_date, onlyid, tax_type, PayType, JiaGeAttr, JiaGeAttrDesc from steelhome_gc.SteelCaiGou_base join steelhome_gc.SteelCaiGou_Info where SteelCaiGou_base.id = SteelCaiGou_Info.sb_id and run_date >= '".$date." 00:00:00' and run_date <= '".$date." 23:59:59' and SteelCaiGou_base.is_show = 2 and SteelCaiGou_Info.is_show = 2 and SteelCaiGou_Info.changerate_tax != '' and SteelCaiGou_Info.changerate != '' and SteelCaiGou_Info.onlyid in $onlyid and steel_id in $steel_id order by steel_id";
    $result = $this->drcdao->query($sql);
    foreach ($result as $key => $value) {
      $arr[$value['steel_id']] = $value;
       $arr[$value['steel_id']]['run_date'] = date("Y-m-d",strtotime($value['run_date']));
       if($value['tax_type']=="1"){
        $arr[$value['steel_id']]['changerate'] = $this->zhangdie($value['changerate_tax']);
        $arr[$value['steel_id']]['the_price'] = $value['the_price_tax'];
        $arr[$value['steel_id']]['beizhu'] = "含税 ";
       }else{
        $arr[$value['steel_id']]['changerate'] = $this->zhangdie($value['changerate']);
        $arr[$value['steel_id']]['the_price'] = $value['the_price'];
        $arr[$value['steel_id']]['beizhu'] = "不含税 ";
       }
       if($value['PayType']=="1"){
        $arr[$value['steel_id']]['beizhu'] .= "承兑 ";
       }else{
        $arr[$value['steel_id']]['beizhu'] .= "现款 ";
       }
       if($value['JiaGeAttr']=="1"){
        $arr[$value['steel_id']]['beizhu'] .= "到厂价";
       }else{
        $arr[$value['steel_id']]['beizhu'] .= $arr[$value['steel_id']]['JiaGeAttrDesc'];
       }
    }
    for($i=0;$i<count($steel_id_arr);$i++){
      if($arr[$steel_id_arr[$i]] == ""){
        $sql = "select steel_id, steel_name, variety, specification, the_price, the_price_tax, run_date, onlyid, tax_type, PayType, JiaGeAttr, JiaGeAttrDesc from steelhome_gc.SteelCaiGou_base join steelhome_gc.SteelCaiGou_Info where SteelCaiGou_base.id = SteelCaiGou_Info.sb_id and SteelCaiGou_base.is_show = 2 and SteelCaiGou_Info.is_show = 2 and SteelCaiGou_Info.changerate_tax != '' and SteelCaiGou_Info.changerate != '' and SteelCaiGou_Info.onlyid in $onlyid and steel_id='".$steel_id_arr[$i]."' order by SteelCaiGou_Info.id desc limit 1";
        $result = $this->drcdao->query($sql);
        $arr[$steel_id_arr[$i]] = $result[0];
        $arr[$steel_id_arr[$i]]['run_date'] = date("Y-m-d",strtotime($result[0]['run_date']));
        $arr[$steel_id_arr[$i]]['changerate'] = "―";
        if($result[0]['tax_type']=="1"){
          $arr[$steel_id_arr[$i]]['the_price'] = $result[0]['the_price_tax'];
          $arr[$steel_id_arr[$i]]['beizhu'] = "含税 ";
       }else{
        $arr[$steel_id_arr[$i]]['the_price'] = $result[0]['the_price'];
         $arr[$steel_id_arr[$i]]['beizhu'] = "不含税 ";
       }
       if($result[0]['PayType']=="1"){
          $arr[$steel_id_arr[$i]]['beizhu'] .= "承兑 ";
       }else{
          $arr[$steel_id_arr[$i]]['beizhu'] .= "现款 ";
       }
       if($result[0]['JiaGeAttr']=="1"){
          $arr[$steel_id_arr[$i]]['beizhu'] .= "到厂价";
       }else{
          $arr[$steel_id_arr[$i]]['beizhu'] .= $result[0]['JiaGeAttrDesc'];
       }
              //print_r($result);
      }
    }
  }
  
  function hq_price_6($time, $ltime, $topicture){
    $sql = "select price,topicture,mconmanagedate from marketconditions where topicture in (" . $topicture . ") AND mconmanagedate >'" . $time . " 00:00:00' AND mconmanagedate <'" . $time . "  23:59:59' order by topicture asc ";
    $query = $this->maindao->execute( $sql );
    
    foreach ( $query as $key => $value ){
      if (strstr ( $value ['price'], "-" )){
        $avgprice = explode ( "-", $value ['price'] );
        $value ['price'] = round ( ($avgprice ['0'] + $avgprice ['1']) / 2, 2 );
      }
      
      $tprice [$value ['topicture']] = $value ['price'];
    }

    $sql2 = "select price,topicture,mconmanagedate from (select * from marketconditions where topicture in (" . $topicture . ") AND mconmanagedate <'" . $ltime . " 23:59:59' order by mconmanagedate desc) as A group by topicture ";
    $query2 = $this->maindao->execute( $sql2 );

    foreach ( $query2 as $key2 => $value2 ){
      if (strstr ( $value2 ['price'], "-" )){
        $avgprice = explode ( "-", $value2 ['price'] );
        $value2 ['price'] = round ( ($avgprice ['0'] + $avgprice ['1']) / 2, 2 );
      }
      
      $lprice [$value2 ['topicture']] = $value2 ['price'];
    }
    
    foreach ( $tprice as $pkey => $pvalue ){
      $price_arr ['zd'] [$pkey] = $this->zhangdie ( $pvalue - $lprice [$pkey] );
      $price_arr ['tprice'] [$pkey] = $pvalue;
      $price_arr ['lprice'] [$pkey] = $lprice [$pkey];
    }
    return $price_arr;
}

  function hq_price_6_week($stime, $etime, $topicture){
    $sql = "select price,topicture,mconmanagedate from marketconditions where topicture in (" . $topicture . ") AND mconmanagedate >'" . $etime . " 00:00:00' AND mconmanagedate <'" . $etime . "  23:59:59' order by topicture asc ";
    $query = $this->maindao->execute( $sql );
    
    foreach ( $query as $key => $value ){
      if (strstr ( $value ['price'], "-" )){
        $avgprice = explode ( "-", $value ['price'] );
        $value ['price'] = round ( ($avgprice ['0'] + $avgprice ['1']) / 2, 2 );
      }
      
      $tprice [$value ['topicture']] = $value ['price'];
    }
    $sql2 = "select price,topicture,mconmanagedate from marketconditions where topicture in (" . $topicture . ") AND mconmanagedate >'" . $stime . " 00:00:00' AND mconmanagedate <'" . $stime . "  23:59:59' order by topicture asc ";
    $query2 = $this->maindao->execute( $sql2 );

    foreach ( $query2 as $key2 => $value2 ){
      if (strstr ( $value2 ['price'], "-" )){
        $avgprice = explode ( "-", $value2 ['price'] );
        $value2 ['price'] = round ( ($avgprice ['0'] + $avgprice ['1']) / 2, 2 );
      }
      
      $lprice [$value2 ['topicture']] = $value2 ['price'];
    }
    
    foreach ( $tprice as $pkey => $pvalue ){
      $price_arr ['zd'] [$pkey] = $this->zhangdie ( $pvalue - $lprice [$pkey] );
      $price_arr ['tprice'] [$pkey] = $pvalue;
      $price_arr ['lprice'] [$pkey] = $lprice [$pkey];
    }
    return $price_arr;
}

  function hq_price_7($time, $ltime, $mastertopid){
    $sql = "select price,mastertopid,mconmanagedate from marketconditions where mastertopid in (" . $mastertopid . ") AND mconmanagedate >'" . $time . " 00:00:00' AND mconmanagedate <'" . $time . "  23:59:59' order by mastertopid asc ";

    $query = $this->maindao->execute( $sql );
    foreach ( $query as $key => $value ){
      if (strstr ( $value ['price'], "-" )){
        $avgprice = explode ( "-", $value ['price'] );
        $value ['price'] = round ( ($avgprice ['0'] + $avgprice ['1']) / 2, 2 );
      }
      
      $tprice [$value ['mastertopid']] = $value ['price'];
    }

    $sql2 = "select price,mastertopid,mconmanagedate from (select * from marketconditions where mastertopid in (" . $mastertopid . ") AND mconmanagedate <'" . $ltime . " 23:59:59' order by mconmanagedate desc) as A group by mastertopid ";
    $query2 = $this->maindao->execute ( $sql2 );
    foreach ( $query2 as $key2 => $value2 ){
      if (strstr ( $value2 ['price'], "-" )){
        $avgprice = explode ( "-", $value2 ['price'] );
        $value2 ['price'] = round ( ($avgprice ['0'] + $avgprice ['1']) / 2, 2 );
      }
      
      $lprice [$value2 ['mastertopid']] = $value2 ['price'];
    }
    // echo "<pre>";print_r($lprice);
    foreach ( $tprice as $pkey => $pvalue ){
      $price_arr ['zd'] [$pkey] = $this->zhangdie ( $pvalue - $lprice [$pkey] );
      $price_arr ['tprice'] [$pkey] = $pvalue;
      $price_arr ['lprice'] [$pkey] = $lprice [$pkey];
    }
    return $price_arr;
  }
function hq_price_7_week($stime, $etime, $mastertopid){
    $sql = "select price,mastertopid,mconmanagedate from marketconditions where mastertopid in (" . $mastertopid . ") AND mconmanagedate >'" . $etime . " 00:00:00' AND mconmanagedate <'" . $etime . "  23:59:59' order by mastertopid asc ";

    $query = $this->maindao->execute( $sql );
    foreach ( $query as $key => $value ){
      if (strstr ( $value ['price'], "-" )){
        $avgprice = explode ( "-", $value ['price'] );
        $value ['price'] = round ( ($avgprice ['0'] + $avgprice ['1']) / 2, 2 );
      }
      
      $tprice [$value ['mastertopid']] = $value ['price'];
    }
    
    $sql2 = "select price,mastertopid,mconmanagedate from marketconditions where mastertopid in (" . $mastertopid . ") AND mconmanagedate >'" . $stime . " 00:00:00' AND mconmanagedate <'" . $stime . "  23:59:59' order by mastertopid asc ";
    $query2 = $this->maindao->execute ( $sql2 );
    foreach ( $query2 as $key2 => $value2 ){
      if (strstr ( $value2 ['price'], "-" )){
        $avgprice = explode ( "-", $value2 ['price'] );
        $value2 ['price'] = round ( ($avgprice ['0'] + $avgprice ['1']) / 2, 2 );
      }
      
      $lprice [$value2 ['mastertopid']] = $value2 ['price'];
    }
    // echo "<pre>";print_r($lprice);
    foreach ( $tprice as $pkey => $pvalue ){
      $price_arr ['zd'] [$pkey] = $this->zhangdie ( $pvalue - $lprice [$pkey] );
      $price_arr ['tprice'] [$pkey] = $pvalue;
      $price_arr ['lprice'] [$pkey] = $lprice [$pkey];
    }
    return $price_arr;
  }
  
  public function gngcsckc($lweek,$tweek,$sdate2,$edate2)
  {
	 $lastMonth =date("Y-m-d",strtotime("-1 month",strtotime($tweek)));
	$lastYear =date("Y-m-d",strtotime("-1 year",strtotime($tweek)));
	
		//上个月同期
	$lastmonth = date('Y-m-d',strtotime("-1 month"));
	$days = date('t', strtotime($lastmonth));
		if(date('d')>$days){
			$sdate3 = date('Y-m',strtotime("-1 month")).($days-3);
			$edate3 = date('Y-m',strtotime("-1 month")).($days+3);
		}else{
			$sdate3 = date('Y-m-d',strtotime("-1 month -3 day"));
			$edate3 = date('Y-m-d',strtotime("-1 month +3 day"));
		}
	//去年同期
	$lastyear = date('Y-m-d',strtotime("-1 year"));
	$days2 = date('t', strtotime($lastmonth));
	if(date('d')>$days2){
		$sdate4 = date('Y-m',strtotime("-1 month")).($days-3);
		$edate4 = date('Y-m',strtotime("-1 month")).($days+3);
	}else{
		$sdate4 = date('Y-m-d',strtotime("-1 year -3 day"));
		$edate4 = date('Y-m-d',strtotime("-1 year +3 day"));
	}
	
	
    //螺纹钢
    $llg_total=$this->getSum($lweek,$tweek,"5");
    $lastWeek_llgtotal=$this->getLastWeekSum($lweek,"5");
	
	$lastDate =date("m月d日",strtotime($lastWeek_llgtotal['time']));
	$lastWeek_llgtotal = $lastWeek_llgtotal['value'];
	//上个月
	$lastMonth_llgtotal=$this->getLastSum($sdate3,$edate3,"5");
	$lastMonth_llgtotal=$lastMonth_llgtotal['value'];
	//去年
	$lastYear_llgtotal=$this->getLastSum($sdate4,$edate4,"5");
	$lastYear_llgtotal=$lastYear_llgtotal['value'];
	
	//wufan
	
     //线材
    $xc_total=$this->getSum($lweek,$tweek,"4");
    $lastWeek_xctotal=$this->getLastWeekSum($lweek,"4");
	$lastWeek_xctotal = $lastWeek_xctotal['value'];
	//上个月
	$lastMonth_xctotal=$this->getLastSum($sdate3,$edate3,"4");
	$lastMonth_xctotal=$lastMonth_xctotal['value'];
	//去年
	$lastYear_xctotal=$this->getLastSum($sdate4,$edate4,"4");
	$lastYear_xctotal=$lastYear_xctotal['value'];
	
	
     //中厚板
	$zhb_total=$this->getSum($lweek,$tweek,"3");
    $lastWeek_zhbtotal=$this->getLastWeekSum($lweek,"3");
	$lastWeek_zhbtotal=$lastWeek_zhbtotal['value'];
	//上个月
	$lastMonth_zhbtotal=$this->getLastSum($sdate3,$edate3,"3");
	$lastMonth_zhbtotal=$lastMonth_zhbtotal['value'];
	//去年
	$lastYear_zhbtotal=$this->getLastSum($sdate4,$edate4,"3");
	$lastYear_zhbtotal=$lastYear_zhbtotal['value'];
	
     //热轧板卷
    $rzbj_total=$this->getSum($lweek,$tweek,"1");
    $lastWeek_rzbjtotal=$this->getLastWeekSum($lweek,"1");
	$lastWeek_rzbjtotal=$lastWeek_rzbjtotal['value'];
	//上个月
	$lastMonth_rzbjtotal=$this->getLastSum($sdate3,$edate3,"1");
	$lastMonth_rzbjtotal=$lastMonth_rzbjtotal['value'];
	//去年
	$lastYear_rzbjtotal=$this->getLastSum($sdate4,$edate4,"1");
	$lastYear_rzbjtotal=$lastYear_rzbjtotal['value'];
	
     //冷轧板卷
    $lzbj_total=$this->getSum($lweek,$tweek,"2");
    $lastWeek_lzbjtotal=$this->getLastWeekSum($lweek,"2");
    $lastWeek_lzbjtotal=$lastWeek_lzbjtotal['value'];
	//上个月
	$lastMonth_lzbjtotal=$this->getLastSum($sdate3,$edate3,"2");
	$lastMonth_lzbjtotal=$lastMonth_lzbjtotal['value'];
	//去年
	$lastYear_lzbjtotal=$this->getLastSum($sdate4,$edate4,"2");
	$lastYear_lzbjtotal=$lastYear_lzbjtotal['value'];
	
	
       //获取本周综合
    $total=$llg_total+$xc_total+$zhb_total+$rzbj_total+$lzbj_total;
    //获取上周的
    $lastWeek_total=$lastWeek_llgtotal+$lastWeek_xctotal+$lastWeek_zhbtotal+$lastWeek_rzbjtotal+$lastWeek_lzbjtotal;
    
    //$sql= "SELECT nid ,ndate FROM news WHERE ntitle LIKE  '【钢之家库存预报】%本周国内主要品种市场库存%' order by nid desc limit 1";
    //$nid =$db2->getArray($sql);
	//print_r($nid );
	
	$newsDate = date("n月d日",strtotime("-2 day" ,strtotime($lweek)));
	//print_r($newsDate );
	//$nid = $nid[0]['nid'];
	
	$total =$llg_total+$xc_total+$zhb_total+$rzbj_total+$lzbj_total;
	
	
//	print_r($total." "."$llg_total+$xc_total+$zhb_total+$rzbj_total+$lzbj_total");
//	echo "<br>";
	$lastWeekTotal=$lastWeek_llgtotal+$lastWeek_xctotal+$lastWeek_rzbjtotal+$lastWeek_lzbjtotal+$lastWeek_zhbtotal;
//	print_r($lastWeekTotal."  $lastWeek_llgtotal+$lastWeek_xctotal+$lastWeek_zhbtotal+$lastWeek_rzbjtotal+$lastWeek_lzbjtotal");
//	echo "<br>";
	$lastMonthTotal=$lastMonth_llgtotal+$lastMonth_xctotal+$lastMonth_rzbjtotal+$lastMonth_lzbjtotal+$lastMonth_zhbtotal;
//	print_r($lastMonthTotal."  $lastMonth_llgtotal+$lastMonth_xctotal+$lastMonth_zhbtotal+$lastMonth_rzbjtotal+$lastMonth_lzbjtotal");
//	echo "<br>";
	$lastYearTotal =$lastYear_llgtotal+$lastYear_xctotal+$lastYear_rzbjtotal+$lastYear_lzbjtotal+$lastYear_zhbtotal;
//	print_r($lastYearTotal."  $lastYear_llgtotal+$lastYear_xctotal+$lastYear_zhbtotal+$lastYear_rzbjtotal+$lastYear_lzbjtotal");
//	echo "<br>";
	//配置表格
	$table ="<p>1、国内市场价格库存<br/></p><table border=1 cellspacing=0 cellpadding=0 style='width:734px;'>
		<tr><td align=center colspan='8'>".date("n月d日",strtotime($tweek))."国内五大品种主要市场库存</td></tr>
		<tr><td align=center>品种</td><td align=center>本期库存(万吨)</td><td align=center>较上期增减(万吨)</td><td align=center>变化幅度%</td><td align=center>较上月同期增减(万吨)</td><td align=center>变化幅度%</td><td align=center>较去年同期增减(万吨)</td><td align=center>变化幅度%</td></tr>
		<tr>
			<td align=center>螺纹钢</td>
			<td align=center>$llg_total</td>
			<td align=center>".$this->zhangdie3($llg_total-$lastWeek_llgtotal)."</td>
			<td align=center>".$this->zhangdie4($lastWeek_llgtotal?($llg_total-$lastWeek_llgtotal)/$lastWeek_llgtotal:0)."</td>
			<td align=center>".$this->zhangdie3($llg_total-$lastMonth_llgtotal)."</td>
			<td align=center>".$this->zhangdie4($lastMonth_llgtotal?($llg_total-$lastMonth_llgtotal)/$lastMonth_llgtotal:0)."</td>
			<td align=center>".$this->zhangdie3($llg_total-$lastYear_llgtotal)."</td>
			<td align=center>".$this->zhangdie4($lastYear_llgtotal?($llg_total-$lastYear_llgtotal)/$lastYear_llgtotal:0)."</td>
		</tr>
		<tr>
			<td align=center>线材</td>
			<td align=center>$xc_total</td>
			<td align=center>".$this->zhangdie3($xc_total-$lastWeek_xctotal)."</td>
			<td align=center>".$this->zhangdie4($lastWeek_xctotal?($xc_total-$lastWeek_xctotal)/$lastWeek_xctotal:0)."</td>
			<td align=center>".$this->zhangdie3($xc_total-$lastMonth_xctotal)."</td>
			<td align=center>".$this->zhangdie4($lastMonth_xctotal?($xc_total-$lastMonth_xctotal)/$lastMonth_xctotal:0)."</td>
			<td align=center>".$this->zhangdie3($xc_total-$lastYear_xctotal)."</td>
			<td align=center>".$this->zhangdie4($lastYear_xctotal?($xc_total-$lastYear_xctotal)/$lastYear_xctotal:0)."</td>
		</tr>
		<tr>
			<td align=center>中厚板</td>
			<td align=center>$zhb_total</td>
			<td align=center>".$this->zhangdie3($zhb_total-$lastWeek_zhbtotal)."</td>
			<td align=center>".$this->zhangdie4($lastWeek_zhbtotal?($zhb_total-$lastWeek_zhbtotal)/$lastWeek_zhbtotal:0)."</td>
			<td align=center>".$this->zhangdie3($zhb_total-$lastMonth_zhbtotal)."</td>
			<td align=center>".$this->zhangdie4($lastMonth_zhbtotal?($zhb_total-$lastMonth_zhbtotal)/$lastMonth_zhbtotal:0)."</td>
			<td align=center>".$this->zhangdie3($zhb_total-$lastYear_zhbtotal)."</td>
			<td align=center>".$this->zhangdie4($lastYear_zhbtotal?($zhb_total-$lastYear_zhbtotal)/$lastYear_zhbtotal:0)."</td>
		</tr>
		<tr>
			<td align=center>热轧板卷 </td>
			<td align=center>$rzbj_total</td>
			<td align=center>".$this->zhangdie3($rzbj_total-$lastWeek_rzbjtotal)."</td>
			<td align=center>".$this->zhangdie4($lastWeek_rzbjtotal?($rzbj_total-$lastWeek_rzbjtotal)/$lastWeek_rzbjtotal:0)."</td>
			<td align=center>".$this->zhangdie3($rzbj_total-$lastMonth_rzbjtotal)."</td>
			<td align=center>".$this->zhangdie4($lastMonth_rzbjtotal?($rzbj_total-$lastMonth_rzbjtotal)/$lastMonth_rzbjtotal:0)."</td>
			<td align=center>".$this->zhangdie3($rzbj_total-$lastYear_rzbjtotal)."</td>
			<td align=center>".$this->zhangdie4($lastYear_rzbjtotal?($rzbj_total-$lastYear_rzbjtotal)/$lastYear_rzbjtotal:0)."</td>
		</tr>
		<tr>
			<td align=center>冷轧板卷</td>
			<td align=center>$lzbj_total</td>
			<td align=center>".$this->zhangdie3($lzbj_total-$lastWeek_lzbjtotal)."</td>
			<td align=center>".$this->zhangdie4($lastWeek_lzbjtotal?($lzbj_total-$lastWeek_lzbjtotal)/$lastWeek_lzbjtotal:0)."</td>
			<td align=center>".$this->zhangdie3($lzbj_total-$lastMonth_lzbjtotal)."</td>
			<td align=center>".$this->zhangdie4($lastMonth_lzbjtotal?($lzbj_total-$lastMonth_lzbjtotal)/$lastMonth_lzbjtotal:0)."</td>
			<td align=center>".$this->zhangdie3($lzbj_total-$lastYear_lzbjtotal)."</td>
			<td align=center>".$this->zhangdie4($lastYear_lzbjtotal?($lzbj_total-$lastYear_lzbjtotal)/$lastYear_lzbjtotal:0)."</td>
		</tr>
		<tr>
			<td align=center>合计</td>
			<td align=center>$total</td>
			<td align=center>".$this->zhangdie3($total-$lastWeekTotal)."</td>
			<td align=center>".$this->zhangdie4($lastWeekTotal?($total-$lastWeekTotal)/$lastWeekTotal:0)."</td>
			<td align=center>".$this->zhangdie3($total-$lastMonthTotal)."</td>
			<td align=center>".$this->zhangdie4($lastMonthTotal?($total-$lastMonthTotal)/$lastMonthTotal:0)."</td>
			<td align=center>".$this->zhangdie3($total-$lastYearTotal)."</td>
			<td align=center>".$this->zhangdie4($lastYearTotal?($total-$lastYearTotal)/$lastYearTotal:0)."</td>
		</tr>
		
	</table>"; 
	$table.="<p>2、国内钢厂库存<br/>
                            　　1)国内主要钢厂螺纹钢库存变化<br/></p>";
							
	// $lweek='2018-10-09';
    // $tweek='2018-10-16';	
	// $edate2 =  date("Y-m-d",strtotime($lweek."-1days"));
    // $sdate2 =  date("Y-m-d",strtotime($edate2."-6days"));						
	$table.=$this->lwg_weeks_forecast($lweek,$tweek,$sdate2,$edate2);
	$table.="<p>2)国内主要钢厂中厚板库存变化<br/></p>";
	$table.=$this->zhb_weeks_forecast($lweek,$tweek,$sdate2,$edate2);
	$table.="<p>3)国内主要钢厂热轧板卷库存变化<br/></p>";
	$table.=$this->rzbj_weeks_forecast($lweek,$tweek,$sdate2,$edate2);						
	$this->assign( "gngcsckc", $table );
  }
  public function yrlsckc($lweek,$tweek,$sdate2,$edate2)//原燃料库存
  {
	  $tsgpbq=$this->getdata_table($lweek,$tweek,"'JKKC_TSGP'");
	  $tsgpsq=$this->getdata_table($sdate2,$edate2,"'JKKC_TSGP'");
	  $time1 =  date("Y-m-d",strtotime($sdate2."-7days"));
      $time2 =  date("Y-m-d",strtotime($edate2."-7days"));
	  if(empty($tsgpsq))
	  {
		$tsgpsq=$this->getdata_table($time1,$time2,"'JKKC_TSGP'");  
	  }
	  $gktksbq=$this->getdata_tablehj($lweek,$tweek,"'JKKCTJ_1'");
	  $gktkssq=$this->getdata_tablehj($sdate2,$edate2,"'JKKCTJ_1'");
	  if(empty($gktkssq))
	  {
		$gktkssq=$this->getdata_tablehj($time1,$time2,"'JKKCTJ_1'");  
	  }
	  $sdgkbq=$this->getdata_tablesgk($lweek,$tweek,"'Java-SGGKJTKC'");
	  $sdgksq=$this->getdata_tablesgk($sdate2,$edate2,"'Java-SGGKJTKC'");
	   if(empty($sdgksq))
	  {
		$sdgksq=$this->getdata_tablesgk($time1,$time2,"'Java-SGGKJTKC'");  
	  }
	       $table="  <table border=1 cellspacing=0 cellpadding=0 style='width:734px;'>
				<tbody><tr>
				 <td></td>	
				 <td align=center>唐山钢坯库存</td> 
				 <td align=center>港口铁矿石库存</td>
				 <td align=center>四大港口焦炭库存</td>
				 
				 
				</tr>
										
			   <tr>
				 <td align=center>本期</td><td align=center >".$tsgpbq."</td> <td align=center >".$gktksbq."</td> <td align=center>".$sdgkbq."</td>
				</tr>
				 <tr>
				 <td align=center >上期</td><td align=center>".$tsgpsq."</td> <td align=center>".$gktkssq."</td> <td align=center>".$sdgksq."</td>
				</tr>
				 <tr>
				 <td align=center>涨跌</td><td align=center>".$this->zhangdie($tsgpbq-$tsgpsq)."</td> <td align=center>".$this->zhangdie($gktksbq-$gktkssq)."</td> <td align=center>".$this->zhangdie($sdgkbq-$sdgksq)."</td>  
				</tr>
				
				
			</tbody></table>";
			$this->assign( "yrlsckc", $table );
			
  }
  function kgl($lweek,$tweek,$sdate2,$edate2)
  {
	  $glkglbq=$this->drcdao->getOne(" select dta_2 from data_table WHERE dta_type='GC_KGL' and dta_1='全国' and dta_ym >='".$lweek." 00:00:00' and dta_ym <='".$tweek." 23:59:59'  order by `dta_ym` desc limit 1 ");
	  $glkglsq=$this->drcdao->getOne(" select dta_2 from data_table WHERE dta_type='GC_KGL' and dta_1='全国' and dta_ym >='".$sdate2." 00:00:00' and dta_ym <='".$edate2." 23:59:59'  order by `dta_ym` desc limit 1 ");
	  $time1 =  date("Y-m-d",strtotime($sdate2."-7days"));
      $time2 =  date("Y-m-d",strtotime($edate2."-7days"));
	  if(empty($glkglsq))
	  {
		 $glkglsq=$this->drcdao->getOne(" select dta_2 from data_table WHERE dta_type='GC_KGL' and dta_1='全国' and dta_ym >='".$time1." 00:00:00' and dta_ym <='".$time2." 23:59:59'  order by `dta_ym` desc limit 1 "); 
	  }
	  
	  
	  $jhkglbq=$this->drcdao->getOne(" select KaiGonglv2 from steelhome_gc.sc_KaiGongLvHuiZong WHERE AreaCode='0' and  Date >='".$lweek." 00:00:00' and Date <='".$tweek." 23:59:59'  order by `Date` desc limit 1 ");
	  $jhkglsq=$this->drcdao->getOne(" select KaiGonglv2 from steelhome_gc.sc_KaiGongLvHuiZong WHERE AreaCode='0' and Date >='".$sdate2." 00:00:00' and Date <='".$edate2." 23:59:59'  order by `Date` desc limit 1 ");
	  if(empty($jhkglsq))
	  {
		 $jhkglsq=$this->drcdao->getOne(" select KaiGonglv2 from steelhome_gc.sc_KaiGongLvHuiZong WHERE AreaCode='0' and Date >='".$time1." 00:00:00' and Date <='".$time2." 23:59:59'  order by `Date` desc limit 1 ");
	  }
	  $table="  <table border=1 cellspacing=0 cellpadding=0 style='width:734px;'>
				<tbody><tr>
				 <td></td>	
				 <td align=center>高炉开工率</td> 
				 <td align=center>焦化开工率</td>
				</tr>
										
			   <tr>
				 <td align=center>本期</td><td align=center >".$glkglbq."</td> <td align=center>".$jhkglbq."</td> 
				</tr>
				 <tr>
				 <td align=center>上期</td><td align=center>".$glkglsq."</td> <td align=center>".$jhkglsq."</td> 
				</tr>
				 <tr>
				 <td align=center>涨跌</td><td align=center>".$this->zhangdie($glkglbq-$glkglsq)."</td> <td align=center>".$this->zhangdie($jhkglbq-$jhkglsq)."</td> 
				</tr>
				
				
			</tbody></table>";
	  $this->assign( "kgl", $table );
  }
  
  function getdata_table($start_date,$end_date,$type){
		$total=0;
		
		$sql= "SELECT `dta_2`  FROM  `data_table` WHERE dta_ym >='".$start_date." 00:00:00' and dta_ym <='".$end_date." 23:59:59'  and dta_type in(". $type." ) order by `dta_ym` desc limit 1 ";
		$total = $this->drcdao->getOne($sql);
		return $total;
	}
	function getdata_tablehj($start_date,$end_date,$type){
		$total=0;
		
		$sql= "SELECT `dta_3`  FROM  `data_table` WHERE dta_ym >='".$start_date." 00:00:00' and dta_ym <='".$end_date." 23:59:59' AND dta_1 =  '合计' and  dta_type in(". $type." ) order by `dta_ym` desc limit 1 ";
	  
		$total = $this->drcdao->getOne($sql);
		return $total;
	}
	
	function getdata_tablesgk($start_date,$end_date,$type){
		$total=0;
		
		$sql= "SELECT `dta_2`  FROM  DataTableBaseInfo AS db LEFT JOIN data_table AS dt ON db.id = dt.baseid WHERE date >='".$start_date." 00:00:00' and date <='".$end_date." 23:59:59' AND dta_1 =  '合计' and  DataType in(". $type." ) order by `Date` desc limit 1 ";
	  
		$total = $this->drcdao->getOne($sql);
		return $total;
	}
	
	  function getSum($start_date,$end_date,$type){
		$total=0;
		//SELECT * FROM `qh_member_paiming` WHERE `datadate` <='2017-06-13' order by `datadate` desc limit 1
		$sql= "SELECT `value`  FROM  `kucun_hz` WHERE time>='".$start_date." 00:00:00' and time<='".$end_date." 23:59:59'  and type in(". $type." ) order by `time` desc limit 1 ";
	  
		$total = $this->drcdao->getOne($sql);
		//echo $total[0]['value'] ."====".$sql."<pre>";
		return $total;
	}
	function getLastSum($start_date,$end_date,$type){
		
		
		$sql= "SELECT `value`,`time`  FROM  `kucun_hz` WHERE time>='".$start_date." 00:00:00' and time<='".$end_date." 23:59:59'  and type in(". $type." ) order by `time` desc limit 1 ";
		$total = $this->drcdao->getRow($sql);
	   
		return $total;
	}
	function getLastWeekSum($tweek,$type){
		
		$sql= "SELECT `value`,`time`  FROM  `kucun_hz` WHERE  time<='".$tweek." 23:59:59'  and   type in(". $type." ) order by `time` desc limit 1 ";
		$total = $this->drcdao->getRow($sql);
		return $total;
	}
	function zhangdie3($data){
		if($data>0){
			return "<font color='red'>".$data."</font>";
		}elseif($data<0){
			return "<font color='green'>-".abs($data)."</font>";
		}else{
			return "-";
		}
	}

	function zhangdie4($data){
		 if($data>0){
			return "<font color='red'>".sprintf("%.1f", abs($data*100))."</font>";
		 }elseif($data<0){
			return "<font color='green'>-".sprintf("%.1f", abs($data*100))."</font>";
		}else{
			return "-";
		 }
	}
   	public function lwg_weeks_forecast($sdate1,$edate1,$sdate2,$edate2){

		//本周库存
		$lwg_bqkc_data = $this->get_steelarea_pzsum('2',$sdate1,$edate1,"1");
		foreach($lwg_bqkc_data as $v1){
			$lwg_bqkc_data['heji'] += $v1["stock"];
      			$total_gc += $v1["num"];
		}
		//上周库存
    $lwg_sqkc_data = $this->get_steelarea_pzsum('2',$sdate2,$edate2,"1");
    foreach($lwg_sqkc_data as $key=>$v2){
			$lwg_sqkc_data['heji'] += $v2["stock"];
			$sz_zj[$key] = $this->zhangdie($lwg_bqkc_data[$key]["stock"] - $lwg_sqkc_data[$key]["stock"]);
			$sz_zj["heji"] += $lwg_bqkc_data[$key]["stock"] - $lwg_sqkc_data[$key]["stock"];
		}
    
		$sz_zj["heji"] = $this->zhangdie($sz_zj["heji"]);
		//$sn_zj["heji"] = $this->zhangdie($sn_zj["heji"]);
		$lwg_kc_tab_str = '<table border=1 cellspacing=0 cellpadding=0 style="width:734px;text-align:center"><tr align="center" height="30">
							<td align="center" >螺纹钢库存</td>
							<td align="center" >华北钢厂<br/>('.$lwg_bqkc_data['hb']['num'].')</td>
							<td align="center" >东北钢厂<br/>('.$lwg_bqkc_data['db']['num'].')</td>
							<td align="center" >华东钢厂<br/>('.$lwg_bqkc_data['hd']['num'].')</td>
							<td align="center" >中南钢厂<br/>('.$lwg_bqkc_data['zn']['num'].')</td>
							<td align="center" >西南钢厂<br/>('.$lwg_bqkc_data['xn']['num'].')</td>
							<td align="center" >西北钢厂<br/>('.$lwg_bqkc_data['xb']['num'].')</td>
							<td align="center" >合计<br/>('.$total_gc.')</td>
						</tr>
						<tr align="center" height="30">
							<td align="center">'.$this->getdateno0($edate1,"月日").'</td>
							<td align="center">'.$lwg_bqkc_data['hb']['stock'].'</td>
							<td align="center">'.$lwg_bqkc_data['db']['stock'].'</td>
							<td align="center">'.$lwg_bqkc_data['hd']['stock'].'</td>
							<td align="center">'.$lwg_bqkc_data['zn']['stock'].'</td>
							<td align="center">'.$lwg_bqkc_data['xn']['stock'].'</td>
							<td align="center">'.$lwg_bqkc_data['xb']['stock'].'</td>
							<td align="center">'.$lwg_bqkc_data['heji'].'</td>
						</tr>
						<tr align="center" height="30">
							<td align="center">'.$this->getdateno0($sdate1,"月日").'</td>
							<td align="center">'.$lwg_sqkc_data['hb']['stock'].'</td>
							<td align="center">'.$lwg_sqkc_data['db']['stock'].'</td>
							<td align="center">'.$lwg_sqkc_data['hd']['stock'].'</td>
							<td align="center">'.$lwg_sqkc_data['zn']['stock'].'</td>
							<td align="center">'.$lwg_sqkc_data['xn']['stock'].'</td>
							<td align="center">'.$lwg_sqkc_data['xb']['stock'].'</td>
							<td align="center">'.$lwg_sqkc_data['heji'].'</td>
						</tr>
						<!--tr align="center" height="30">
							<td align="center">上年同期</td>
							<td align="center">'.$lwg_sqkc_data2['hb'].'</td>
							<td align="center">'.$lwg_sqkc_data2['db'].'</td>
							<td align="center">'.$lwg_sqkc_data2['hd'].'</td>
							<td align="center">'.$lwg_sqkc_data2['zn'].'</td>
							<td align="center">'.$lwg_sqkc_data2['xn'].'</td>
							<td align="center">'.$lwg_sqkc_data2['xb'].'</td>
							<td align="center">'.$lwg_sqkc_data2['heji'].'</td>
						</tr-->
						<tr align="center" height="30">
							<td align="center">较上周增减</td>
							<td align="center">'.$sz_zj["hb"].'</td>
							<td align="center">'.$sz_zj["db"].'</td>
							<td align="center">'.$sz_zj["hd"].'</td>
							<td align="center">'.$sz_zj["zn"].'</td>
							<td align="center">'.$sz_zj["xn"].'</td>
							<td align="center">'.$sz_zj["xb"].'</td>
							<td align="center">'.$sz_zj["heji"].'</td>
						</tr>
						<!--tr align="center" height="30">
							<td align="center">较同期增减</td>
							<td align="center">'.$sn_zj["hb"].'</td>
							<td align="center">'.$sn_zj["db"].'</td>
							<td align="center">'.$sn_zj["hd"].'</td>
							<td align="center">'.$sn_zj["zn"].'</td>
							<td align="center">'.$sn_zj["xn"].'</td>
							<td align="center">'.$sn_zj["xb"].'</td>
							<td align="center">'.$sn_zj["heji"].'</td>
						</tr-->';
		$lwg_kc_tab_str .= "</table>";
		$content .= $lwg_kc_tab_str;
	/* 	$st_code = "120,121,151,1457,113,1186,1801,1182";
		$BigType = 1;
		$SmallType = 2;
		$gc_stock = $this->get_gc_stock2($sdate1,$edate1,$sdate2,$edate2,$BigType,$SmallType,$st_code);
		//echo "<pre/>";print_r($gc_stock);
		$lwg_gc_kc_tab_str = '<table border=1 cellspacing=0 cellpadding=0 style="width:100%;text-align:center">
								 <tr align="center" height="30">
									<td align="center" >螺纹钢库存</td>
									<td align="center" >沙钢</td>
									<td align="center" >永钢</td>
									<td align="center" >中天</td>
									<td align="center" >雨花</td>
									<td align="center" >马钢</td>
									<td align="center" >长江钢铁</td>
									<td align="center" >萍九钢</td>
									<td align="center" >莱钢永锋</td>
									<td align="center" >合计</td>
								</tr>
								 <tr align="center" height="30">
									<td align="center">'.$this->getdateno0($edate1,"月日").'</td>
									<td align="center">'.$gc_stock['bqkc']['120'].'</td>
									<td align="center">'.$gc_stock['bqkc']['121'].'</td>
									<td align="center">'.$gc_stock['bqkc']['151'].'</td>
									<td align="center">'.$gc_stock['bqkc']['1457'].'</td>
									<td align="center">'.$gc_stock['bqkc']['113'].'</td>
									<td align="center">'.$gc_stock['bqkc']['1186'].'</td>
									<td align="center">'.$gc_stock['bqkc']['1801'].'</td>
									<td align="center">'.$gc_stock['bqkc']['1182'].'</td>
									<td align="center">'.$gc_stock['bqkc']['heji'].'</td>
								</tr>
								<tr align="center" height="30">
									<td align="center">'.$this->getdateno0($sdate1,"月日").'</td>
									<td align="center">'.$gc_stock['sqkc']['120'].'</td>
									<td align="center">'.$gc_stock['sqkc']['121'].'</td>
									<td align="center">'.$gc_stock['sqkc']['151'].'</td>
									<td align="center">'.$gc_stock['sqkc']['1457'].'</td>
									<td align="center">'.$gc_stock['sqkc']['113'].'</td>
									<td align="center">'.$gc_stock['sqkc']['1186'].'</td>
									<td align="center">'.$gc_stock['sqkc']['1801'].'</td>
									<td align="center">'.$gc_stock['sqkc']['1182'].'</td>
									<td align="center">'.$gc_stock['sqkc']['heji'].'</td>
								</tr>
								<tr align="center" height="30">
									<td align="center">较上周增减</td>
									<td align="center">'.$gc_stock['zd']['120'].'</td>
									<td align="center">'.$gc_stock['zd']['121'].'</td>
									<td align="center">'.$gc_stock['zd']['151'].'</td>
									<td align="center">'.$gc_stock['zd']['1457'].'</td>
									<td align="center">'.$gc_stock['zd']['113'].'</td>
									<td align="center">'.$gc_stock['zd']['1186'].'</td>
									<td align="center">'.$gc_stock['zd']['1801'].'</td>
									<td align="center">'.$gc_stock['zd']['1182'].'</td>
									<td align="center">'.$this->zhangdie($gc_stock['bqkc']['heji']-$gc_stock['sqkc']['heji']).'</td>
								</tr>';
		$lwg_gc_kc_tab_str .= "</table><br/>";
		$content .= $lwg_gc_kc_tab_str; */
		
		return $content;
		//$this->assign( "content", $content );
	}
	public function zhb_weeks_forecast($sdate1,$edate1,$sdate2,$edate2){
						//本周库存
		$zhb_bqkc_data = $this->get_steelarea_pzsum('3',$sdate1,$edate1,"2");
		//print_r($lwg_bqkc_data);
		foreach($zhb_bqkc_data as $v1){
			$zhb_bqkc_data['heji'] += $v1["stock"];
      			$total_gc += $v1["num"];
		}
		//上周库存
    $lwg_sqkc_data = $this->get_steelarea_pzsum('3',$sdate2,$edate2,"2");
    foreach($lwg_sqkc_data as $key=>$v2){
			$lwg_sqkc_data['heji'] += $v2["stock"];
			$sz_zj[$key] = $this->zhangdie($zhb_bqkc_data[$key]["stock"] - $lwg_sqkc_data[$key]["stock"]);
			$sz_zj["heji"] += $zhb_bqkc_data[$key]["stock"] - $lwg_sqkc_data[$key]["stock"];
		}
    
		$sz_zj["heji"] = $this->zhangdie($sz_zj["heji"]);
		//$sn_zj["heji"] = $this->zhangdie($sn_zj["heji"]);
		$zhb_kc_tab_str = '<table border=1 cellspacing=0 cellpadding=0 style="width:734px;text-align:center"><tr align="center" height="30">
							<td align="center" >中厚板库存</td>
							<td align="center" >华北钢厂<br/>('.$zhb_bqkc_data['hb']['num'].')</td>
							<td align="center" >东北钢厂<br/>('.$zhb_bqkc_data['db']['num'].')</td>
							<td align="center" >华东钢厂<br/>('.$zhb_bqkc_data['hd']['num'].')</td>
							<td align="center" >中南钢厂<br/>('.$zhb_bqkc_data['zn']['num'].')</td>
							<td align="center" >西南钢厂<br/>('.$zhb_bqkc_data['xn']['num'].')</td>
							<td align="center" >西北钢厂<br/>('.$zhb_bqkc_data['xb']['num'].')</td>
							<td align="center" >合计<br/>('.$total_gc.')</td>
						</tr>
						<tr align="center" height="30">
							<td align="center">'.$this->getdateno0($edate1,"月日").'</td>
							<td align="center">'.$zhb_bqkc_data['hb']['stock'].'</td>
							<td align="center">'.$zhb_bqkc_data['db']['stock'].'</td>
							<td align="center">'.$zhb_bqkc_data['hd']['stock'].'</td>
							<td align="center">'.$zhb_bqkc_data['zn']['stock'].'</td>
							<td align="center">'.$zhb_bqkc_data['xn']['stock'].'</td>
							<td align="center">'.$zhb_bqkc_data['xb']['stock'].'</td>
							<td align="center">'.$zhb_bqkc_data['heji'].'</td>
						</tr>
						<tr align="center" height="30">
							<td align="center">'.$this->getdateno0($sdate1,"月日").'</td>
							<td align="center">'.$lwg_sqkc_data['hb']['stock'].'</td>
							<td align="center">'.$lwg_sqkc_data['db']['stock'].'</td>
							<td align="center">'.$lwg_sqkc_data['hd']['stock'].'</td>
							<td align="center">'.$lwg_sqkc_data['zn']['stock'].'</td>
							<td align="center">'.$lwg_sqkc_data['xn']['stock'].'</td>
							<td align="center">'.$lwg_sqkc_data['xb']['stock'].'</td>
							<td align="center">'.$lwg_sqkc_data['heji'].'</td>
						</tr>
						<!--tr align="center" height="30">
							<td align="center">上年同期</td>
							<td align="center">'.$lwg_sqkc_data2['hb'].'</td>
							<td align="center">'.$lwg_sqkc_data2['db'].'</td>
							<td align="center">'.$lwg_sqkc_data2['hd'].'</td>
							<td align="center">'.$lwg_sqkc_data2['zn'].'</td>
							<td align="center">'.$lwg_sqkc_data2['xn'].'</td>
							<td align="center">'.$lwg_sqkc_data2['xb'].'</td>
							<td align="center">'.$lwg_sqkc_data2['heji'].'</td>
						</tr-->
						<tr align="center" height="30">
							<td align="center">较上周增减</td>
							<td align="center">'.$sz_zj["hb"].'</td>
							<td align="center">'.$sz_zj["db"].'</td>
							<td align="center">'.$sz_zj["hd"].'</td>
							<td align="center">'.$sz_zj["zn"].'</td>
							<td align="center">'.$sz_zj["xn"].'</td>
							<td align="center">'.$sz_zj["xb"].'</td>
							<td align="center">'.$sz_zj["heji"].'</td>
						</tr>
						<!--tr align="center" height="30">
							<td align="center">较同期增减</td>
							<td align="center">'.$sn_zj["hb"].'</td>
							<td align="center">'.$sn_zj["db"].'</td>
							<td align="center">'.$sn_zj["hd"].'</td>
							<td align="center">'.$sn_zj["zn"].'</td>
							<td align="center">'.$sn_zj["xn"].'</td>
							<td align="center">'.$sn_zj["xb"].'</td>
							<td align="center">'.$sn_zj["heji"].'</td>
						</tr-->';
		$zhb_kc_tab_str .= "</table>";
		$content .= $zhb_kc_tab_str;
		return $content;
	}
	
		public function rzbj_weeks_forecast($sdate1,$edate1,$sdate2,$edate2){
						//本周库存
		$rzbj_bqkc_data = $this->get_steelarea_pzsum('4',$sdate1,$edate1,"3");
		//print_r($rzbj_bqkc_data);
		foreach($rzbj_bqkc_data as $v1){
			$rzbj_bqkc_data['heji'] += $v1["stock"];
      			$total_gc += $v1["num"];
		}
		//上周库存
    $lwg_sqkc_data = $this->get_steelarea_pzsum('4',$sdate2,$edate2,"3");
    foreach($lwg_sqkc_data as $key=>$v2){
			$lwg_sqkc_data['heji'] += $v2["stock"];
			$sz_zj[$key] = $this->zhangdie($rzbj_bqkc_data[$key]["stock"] - $lwg_sqkc_data[$key]["stock"]);
			$sz_zj["heji"] += $rzbj_bqkc_data[$key]["stock"] - $lwg_sqkc_data[$key]["stock"];
		}
    
		$sz_zj["heji"] = $this->zhangdie($sz_zj["heji"]);
		//$sn_zj["heji"] = $this->zhangdie($sn_zj["heji"]);
		$zhb_kc_tab_str = '<table border=1 cellspacing=0 cellpadding=0 style="width:734px;text-align:center"><tr align="center" height="30">
							<td align="center" >热轧板卷库存</td>
							<td align="center" >华北钢厂<br/>('.$rzbj_bqkc_data['hb']['num'].')</td>
							<td align="center" >东北钢厂<br/>('.$rzbj_bqkc_data['db']['num'].')</td>
							<td align="center" >华东钢厂<br/>('.$rzbj_bqkc_data['hd']['num'].')</td>
							<td align="center" >中南钢厂<br/>('.$rzbj_bqkc_data['zn']['num'].')</td>
							<td align="center" >西南钢厂<br/>('.$rzbj_bqkc_data['xn']['num'].')</td>
							<td align="center" >西北钢厂<br/>('.$rzbj_bqkc_data['xb']['num'].')</td>
							<td align="center" >合计<br/>('.$total_gc.')</td>
						</tr>
						<tr align="center" height="30">
							<td align="center">'.$this->getdateno0($edate1,"月日").'</td>
							<td align="center">'.$rzbj_bqkc_data['hb']['stock'].'</td>
							<td align="center">'.$rzbj_bqkc_data['db']['stock'].'</td>
							<td align="center">'.$rzbj_bqkc_data['hd']['stock'].'</td>
							<td align="center">'.$rzbj_bqkc_data['zn']['stock'].'</td>
							<td align="center">'.$rzbj_bqkc_data['xn']['stock'].'</td>
							<td align="center">'.$rzbj_bqkc_data['xb']['stock'].'</td>
							<td align="center">'.$rzbj_bqkc_data['heji'].'</td>
						</tr>
						<tr align="center" height="30">
							<td align="center">'.$this->getdateno0($sdate1,"月日").'</td>
							<td align="center">'.$lwg_sqkc_data['hb']['stock'].'</td>
							<td align="center">'.$lwg_sqkc_data['db']['stock'].'</td>
							<td align="center">'.$lwg_sqkc_data['hd']['stock'].'</td>
							<td align="center">'.$lwg_sqkc_data['zn']['stock'].'</td>
							<td align="center">'.$lwg_sqkc_data['xn']['stock'].'</td>
							<td align="center">'.$lwg_sqkc_data['xb']['stock'].'</td>
							<td align="center">'.$lwg_sqkc_data['heji'].'</td>
						</tr>
						<!--tr align="center" height="30">
							<td align="center">上年同期</td>
							<td align="center">'.$lwg_sqkc_data2['hb'].'</td>
							<td align="center">'.$lwg_sqkc_data2['db'].'</td>
							<td align="center">'.$lwg_sqkc_data2['hd'].'</td>
							<td align="center">'.$lwg_sqkc_data2['zn'].'</td>
							<td align="center">'.$lwg_sqkc_data2['xn'].'</td>
							<td align="center">'.$lwg_sqkc_data2['xb'].'</td>
							<td align="center">'.$lwg_sqkc_data2['heji'].'</td>
						</tr-->
						<tr align="center" height="30">
							<td align="center">较上周增减</td>
							<td align="center">'.$sz_zj["hb"].'</td>
							<td align="center">'.$sz_zj["db"].'</td>
							<td align="center">'.$sz_zj["hd"].'</td>
							<td align="center">'.$sz_zj["zn"].'</td>
							<td align="center">'.$sz_zj["xn"].'</td>
							<td align="center">'.$sz_zj["xb"].'</td>
							<td align="center">'.$sz_zj["heji"].'</td>
						</tr>
						<!--tr align="center" height="30">
							<td align="center">较同期增减</td>
							<td align="center">'.$sn_zj["hb"].'</td>
							<td align="center">'.$sn_zj["db"].'</td>
							<td align="center">'.$sn_zj["hd"].'</td>
							<td align="center">'.$sn_zj["zn"].'</td>
							<td align="center">'.$sn_zj["xn"].'</td>
							<td align="center">'.$sn_zj["xb"].'</td>
							<td align="center">'.$sn_zj["heji"].'</td>
						</tr-->';
		$zhb_kc_tab_str .= "</table>";
		$content .= $zhb_kc_tab_str;
		return $content;
	}
	
		public function get_steelarea_pzsum($str,$time1,$time2,$dta_vartype){
		$sql = "SELECT `area`,sum(Stock) as stock,count(1) as num from (SELECT Stock,SmallType,area,sc_id  FROM (SELECT Stock,SmallType,area,sc_id FROM steelhome_gc.`sc_steelstock2` left join steelhome_gc.sc_steelcom on sc_steelstock2.sc_id = sc_steelcom.id WHERE `actime`>='".$time1." 00:00:00' and `actime`<='".$time2." 23:59:59' and BigType='".$dta_vartype."' and SmallType in (".$str.") order by actime DESC ) as tab2 where 1  group by sc_id,SmallType) tab where 1 group by area";	 
       // echo $sql."<br />";
        $data = $this->drcdao->query($sql);
        $data2 = array();
        if(empty($data))
		{
			$time1 =  date("Y-m-d",strtotime($time1."-7days"));
            $time2 =  date("Y-m-d",strtotime($time2."-7days"));
			$sql = "SELECT `area`,sum(Stock) as stock,count(1) as num from (SELECT Stock,SmallType,area,sc_id  FROM (SELECT Stock,SmallType,area,sc_id FROM steelhome_gc.`sc_steelstock2` left join steelhome_gc.sc_steelcom on sc_steelstock2.sc_id = sc_steelcom.id WHERE `actime`>='".$time1." 00:00:00' and `actime`<='".$time2." 23:59:59' and BigType='".$dta_vartype."' and SmallType in (".$str.") order by actime DESC ) as tab2 where 1  group by sc_id,SmallType) tab where 1 group by area";	 
            $data = $this->drcdao->query($sql);
		}
        foreach($data as $key=>$tmp){
          $area = $tmp["area"];
          $stock = $tmp["stock"];
          $num = $tmp["num"];
            if($area=="华东地区"){
                $data2['hd']["stock"] = $stock;
                $data2['hd']["num"] = $num;
            }else if($area=="华北地区"){
                $data2['hb']["stock"] = $stock;
                $data2['hb']["num"] = $num;
            }else if($area=="东北地区"){
                $data2['db']["stock"] = $stock;
                $data2['db']["num"] = $num;
            }else if($area=="中南地区"){
                $data2['zn']["stock"] = $stock;
                $data2['zn']["num"] = $num;
            }else if($area=="西南地区"){
                $data2['xn']["stock"] = $stock;
                $data2['xn']["num"] = $num;
            }else if($area=="西北地区"){
                $data2['xb']["stock"] = $stock;
                $data2['xb']["num"] = $num;
            }
        }

		return $data2;
	}
	function get_gc_stock2($sdate,$edate,$sdate2,$edate2,$BigType,$SmallType,$st_code){
		$sql = 'SELECT a.* as sumstock,b.name,b.st_code FROM steelhome_gc.`sc_steelstock2` as a,steelhome_gc.sc_steelcom as b WHERE a.sc_id = b.id and b.st_code in ('.$st_code.') and a.`actime`>="'.$sdate.' 00:00:00" and a.actime <="'.$edate.' 23:59:59" and a.`BigType`=1 and a.`SmallType`=2 group by b.st_code order by a.actime desc';
		//echo $sql;
		$bq_data = $this->drcdao->query($sql);
		$zd_arr = array();
		$data1 = array();
		foreach($bq_data as $bq_v){
			$data1[$bq_v['st_code']] = $bq_v['Stock'];
			$data1['heji'] = $data1['heji'] + $bq_v['Stock'];
		}
		
		$sql2 = 'SELECT a.*,b.name,b.st_code FROM steelhome_gc.`sc_steelstock2` as a,steelhome_gc.sc_steelcom as b WHERE a.sc_id = b.id and b.st_code in ('.$st_code.') and a.`actime`>="'.$sdate2.' 00:00:00" and a.actime <="'.$edate2.' 23:59:59" and a.`BigType`=1 and a.`SmallType`=2 group by b.st_code order by a.actime desc';
		//echo $sql2;
		$sq_data = $this->drcdao->query($sql2);
		$data2 = array();
		foreach($sq_data as $sq_v){
			$data2[$sq_v['st_code']] = $sq_v['Stock'];
			$zd_arr[$sq_v['st_code']] = $this->zhangdie($data1[$sq_v['st_code']] - $data2[$sq_v['st_code']]);
			$data2['heji'] = $data2['heji'] + $sq_v['Stock'];
		}
		
		$data = array("bqkc"=>$data1,"sqkc"=>$data2,"zd"=>$zd_arr);
		
		
		return $data;
	}
	  	/**取得日期年月日 没有0**/
  	public function getdateno0($date,$type){  	
  		$year=date("Y",strtotime($date));
  		$month=date("m",strtotime($date));
  		$day=date("d",strtotime($date));
  		
  		if($month<10){
  			$month = str_replace("0", "", $month);	
  		}  		
  		if($day<10){
  			$day = str_replace("0", "", $day);	
  		}
  		
  		if("年月日"==$type){
  			return $year."年".$month."月".$day."日";
  		}
  		if("月日"==$type){
  			return $month."月".$day."日";
  		}
	}

} 

?>