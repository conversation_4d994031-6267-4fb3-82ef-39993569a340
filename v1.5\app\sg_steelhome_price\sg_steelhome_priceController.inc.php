<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );

class sg_steelhome_priceController extends AbstractController{
  
    public function __construct(){
        parent::__construct();
        $this->_action->setDao( new sgcwrbDao("DRCW") );  //drc
        $this->_action->t1Dao=new sgcwrbDao("MAIN");  //
        $this->_action->homeDao=new sgcwrbDao("91R");
        $this->_action->homeDaoTest=new sgcwrbDao("91R");
        $this->_action->gcDao=new sgcwrbDao("GC");
    }

    public function _dopre()
    {
        $this->_action->checkSession();
    }

    public function do_get_xsjzpj_data(){
        $this->_action->get_xsjzpj_data( $this->_request );
    } 

    public function do_get_cgjzpj_data(){
        $this->_action->get_cgjzpj_data( $this->_request );
    } 

    public function do_get_market_price(){
        $this->_action->get_market_price( $this->_request );
    }

    public function do_get_sgjcxsjg_data(){
        $this->_action->get_sgjcxsjg_data( $this->_request );
    }

    public function do_get_dgxsjg_data(){
        $this->_action->get_dgxsjg_data( $this->_request );
    }

    public function do_get_price_data(){
        $this->_action->get_price_data( $this->_request );
    }

    public function do_get_xian_price(){
        $this->_action->get_xian_price( $this->_request );
    }

    public function do_getOnePrice(){
        $this->_action->getOnePrice( $this->_request );
    }

    /**
     * 自动生成市场综述
     * Created by zfy.
     * Date:2021/1/25 15:37
     * @param $params
     */
    public function do_create_content_by_price_id(){
        $this->_action->create_content_by_price_id( $this->_request );
    }

    //xiangbin add 20210315 strat 
    public function do_get_gzj_price_avg(){
        $this->_action->get_gzj_price_avg( $this->_request );
    }
    public function do_get_gccg_price(){
        $this->_action->get_gccg_price( $this->_request );
    }
      //xiangbin add 20210315 end

    public function v_sg_report_list(){
        $this->_action->sg_report_list($this->_request);
    }

    public function do_get_news_pdf(){
        $this->_action->get_news_pdf($this->_request);
    }

}
?>