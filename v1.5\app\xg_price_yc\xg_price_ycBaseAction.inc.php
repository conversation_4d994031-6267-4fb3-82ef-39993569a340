<?php
include_once(FRAME_LIB_DIR . "/action/AbstractAction.inc.php");
include_once( APP_DIR."/xg_price_yc/xg_price_ycInfoAction.inc.php" );
class  xg_price_ycBaseAction extends xg_price_ycInfoAction
{
    public function __construct()
    {
        parent::__construct();
    }

    public function yj_manage($params)
    {
        $date = date("Y-m-d",strtotime("-1 day"));
        //$date = "2023-01-06";
        //echo $date;
        //$url = "http://172.16.200.225/api/jgyc/getData?date=".$date;
        $url = "https://218.87.96.135:14007/api/jgyc/getData?date=".$date;
        $data = file_get_contents($url);
        $data = json_decode($data,true);

        $info_variety = array();
        foreach ($data['data'] as $tmp){

            //if($tmp['type']=="1") continue;
            if($tmp['small_type']!=""){
                $small_type = explode("_",$tmp['small_type']);
                $k = $small_type[0];
                $k1 = $tmp['small_type'];

                if($small_type[2]!=""){
                    $k1 = $tmp['big_type'];
                    $k2 = $tmp['small_type'];


                    $info_variety[$tmp['type']][$k][$k1][$k2]['dataname'] = $tmp['dataname'];
                }else{
                    $info_variety[$tmp['type']][$k][$k1]['dataname'] = $tmp['dataname'];
                    $info_variety[$tmp['type']][$k][$k1][$k1]['dataname'] = $tmp['dataname'];
                }
            }else{
                $k = $tmp['big_type'];

                $info_variety[$tmp['type']][$k]['dataname'] = $tmp['dataname'];
                $info_variety[$tmp['type']][$k][$k]['dataname'] = $tmp['dataname'];
            }
        }

        //已设置
        //$where = " isdel=0 ";
        $where = " 1 ";
        $dataInfo = $this->drcwdao->getYJListInfo($where, 0, 10000);
        $set_info = array();
        foreach($dataInfo as $key=>$tmp){
            $small_type = explode("_",$tmp['small_type']);
            //$k = $small_type[0];
            //$k1 = $tmp['big_type'];
            //$k2 = $tmp['small_type'];
            if(!empty($small_type) && $small_type[2]!=""){
                //$set_info[$k][$k1][$k2] = "1";
                $set_info[$tmp['type']][$tmp['big_type']][$tmp['small_type']] = "1";
            }else{
                $set_info[$k1][$k2] = "1";
            }

        }

        //1：手持订单量2：库存
        $arr_type = array(
            "1"=>"手持订单量",
            "2"=>"库存"
        );
        $select = array();
        foreach($info_variety as $key=>$value){
            $info = array();
            foreach($value as $k=>$tmp){
                $info2 = array();
                foreach($tmp as $k2=>$tmp2){
                    if($k2=="dataname" ) continue;

                    $info3 = array();
                    foreach($tmp2 as $k3=>$tmp3){
                        if($k3=="dataname" || (count($tmp2)==2 && $k2==$k3)) continue;
                        $arr = array();
                        $arr['name'] = $tmp3['dataname'];
                        $arr['value'] = $key."_".$k3;
                        if ($set_info[$k][$k2][$k3] == "1") {
                            $arr['disabled'] = true;
                        }

                        $info3[] = $arr;

                    }
                    $arr = array();
                    $arr['name'] = $tmp2['dataname'];
                    $arr['value'] = $key."_".$k2;
                    if ($set_info[$k][$k2] == "1") {
                        $arr['disabled'] = true;
                    }
                    if(!empty($info3))
                    $arr['children'] = $info3;
                    $info2[] = $arr;
                }
                $arr = array();
                $arr['name'] = $tmp['dataname'];
                $arr['value'] = $key."_".$k;
                if ($set_info[$k][$k] == "1") {
                    //$arr['disabled'] = true;
                }
                if(!empty($info2))
                $arr['children'] = $info2;
                $info[] = $arr;

            }

            $arr = array();
            $arr['name'] = $arr_type[$key];
            $arr['value'] = $key;
            if(!empty($info))
                $arr['children'] = $info;
            $select[] = $arr;
        }
        if($params['debug']==1) {
            echo "<pre>";
            //print_R($info_variety);
            print_R($select);
        }

        $this->assign("info_variety", base64_encode(json_encode($info_variety)));
        $this->assign("select", json_encode($select));
        $this->assign("params", $params);
    }

    public function edityj($params){
        $id = $params['id'];
        $base = $this->drcwdao->getYJInfo($id);

        $this->assign("base", $base);
        $this->assign("params", $params);
    }

    public function saveyj($params){
        $id = $params['id'];
        $jy_zhibiao = $params['jy_zhibiao'];
        $unit = $params['unit'];
        $ceiling = $params['ceiling'];
        $minimum = $params['minimum'];
        $resultArray = array(
            "code" => 0,
            "msg" => "保存失败"
        );



        $sql2 = " ceiling='" . $ceiling . "' ,minimum='" . $minimum . "' ";

        if ($id) {

            $varietyname = $params['varietyname'];
            $sql = "update XG_YJ_data_base set updatetime=now() ,varietyname='" . $varietyname . "', ";
            $sql3 = " where id='" . $id . "' ";

            $this->drcwdao->execute($sql . $sql2 . $sql3);

        } else {

            $sql2 .= " ,jy_zhibiao='" . $jy_zhibiao . "' ,unit='" . $unit . "'  ";

            $sql = "insert into XG_YJ_data_base set createtime=now() ,iscan=1,";

            $varietynames = $params['varietyname'];
            $info_variety = json_decode(base64_decode($params['info_variety']),true);
            $varietynames = explode(",",$varietynames);

            $heji = "合计";

            foreach ($varietynames as $tmp){
                $type = substr($tmp,0,1);
                $tmp = substr($tmp,2);
                $small_type = explode("_",$tmp);

                if($small_type[2]!=""){
                    $big_type = $small_type[0]."_".$small_type[1];
                    $small = $tmp;

                    $varietyname = $info_variety[$type][$small_type[0]][$big_type][$small]['dataname'];
                    $small_varietyname = $info_variety[$type][$small_type[0]][$big_type]['dataname'];
                    $big_varietyname = $info_variety[$type][$small_type[0]]['dataname'];
                }else if($small_type[1]!=""){
                    $big_type = $small_type[0];
                    $small = $tmp;

                    //$varietyname = $info_variety[$type][$big_type][$small]['dataname'];
                    $varietyname = "";
                    $small_varietyname = $info_variety[$type][$big_type][$small]['dataname'];
                    $big_varietyname = $info_variety[$type][$big_type]['dataname'];

                    if(count($info_variety[$type][$big_type][$small]) > 2){
                        $varietyname = $heji;
                    }
                }else{
                    $big_type = $tmp;
                    $small = $tmp;

                    //$varietyname = $info_variety[$type][$big_type]['dataname'];
                    $varietyname = "";

                    $small_varietyname = $info_variety[$type][$big_type]['dataname'];

                    if(!strstr($small_varietyname,$heji)){
                        $small_varietyname .= $heji;
                    }
                    $big_varietyname = $info_variety[$type][$big_type]['dataname'];
                }

                $sql4  = " ,varietyname='" . $varietyname . "' ,small_varietyname='" . $small_varietyname . "' ";
                $sql4 .= " ,big_varietyname='" . $big_varietyname . "' ";
                $sql4 .= " ,big_type='" . $big_type . "' ,small_type='" . $small . "' ,type='".$type."' ";

                $this->drcwdao->execute( $sql . $sql2 . $sql4 );
                $id = $this->drcwdao->insert_id();
            }

        }

        if($id){
            //修改数据插入到历史记录表
            $this->insert_info($id);
        }

        $resultArray = array(
            "code" => 1,
            "msg" => "保存成功"
        );

        echo json_encode($resultArray);
    }

    public function insert_info($id){

    }

    public function deleteyj($params){
        $id = $params['id'];
        $isdel = $params['isdel'];
        $return_array = array(
            "code" => 1,
            "msg" => "操作成功！",
        );
        if ($id) {

            //删除
            $sql = "update XG_YJ_data_base set ";
            $sql .= "isdel='".$isdel."' where id='" . $id . "' ";
            $this->drcwdao->execute($sql);

            //删除设置的信息发送表
            $sql = "update XG_YJ_data_detail set ";
            $sql .= "isdel='".$isdel."' where baseid='" . $id . "' ";
            $this->drcwdao->execute($sql);

        } else {
            $return_array = array(
                "code" => 0,
                "msg" => "操作失败！",
            );
        }
        echo json_encode($return_array);
    }

    public function getyjList($params){

        $id = $params['id'];
        $MsgInfos = array();
        if($id){
            $base = $this->drcwdao->getMSGInfo($id);

            $where = " isdel=0 and mobile='".$base['mobile']."' ";
            $MsgInfos = $this->drcwdao->getMSGbaseid($where);

            $where = " isdel=0 ";
        }else{
            $where = " 1 ";
        }
        
        $page = (float)$params['page'];
        $limit = (float)$params['limit'];

        if ($page < 1) {
            $page = 1;
        }
        $start = ($page - 1) * $limit;


        $total = $this->drcwdao->getYJListTotal($where);
        $dataInfo = $this->drcwdao->getYJListInfo($where, $start, $limit);
        foreach ($dataInfo as $key=>&$tmp){

            $tmp['jy_zhibiao'] = $tmp['jy_zhibiao']."(".$tmp['unit'].")";


            $tmp['Checked'] = false;
            if(in_array($tmp['id'],$MsgInfos))
            $tmp['Checked'] = true;
        }

        $code = 0;
        if ($dataInfo) {
            $code = 0;
        }
        $return_array = array(
            "code" => $code,
            "data" => $dataInfo,
            "count" => $total
        );
        echo json_encode($return_array);
    }

    //经营指标预警信息通知
    public function msg_manage($params)
    {
        $this->assign("params", $params);
    }

    public function savemsg($params){
        $id = $params['id'];
        $username = $params['username'];
        $mobile = $params['mobile'];

        $resultArray = array(
            "code" => 1,
            "msg" => "保存成功"
        );

        if ($id) {
            $info = $this->drcwdao->getMSGInfo($id);

            $sql = "update XG_YJ_data_detail set updatetime=now() ,";
            $sql .= " username='" . $username . "' ,mobile='" . $mobile . "'  ";
            $sql .= " where mobile='" . $info['mobile'] . "' ";
            $this->drcwdao->execute( $sql );

        } else {
            $where = " isdel=0 and mobile='".$mobile."' ";
            $msglist = $this->drcwdao->getMSGList($where);
            if(!empty($msglist)){
                $resultArray = array(
                    "code" => 0,
                    "msg" => "新增失败，该手机号已设置！"
                );
            }else{
                $sql = "insert into XG_YJ_data_detail set createtime=now() ,";
                $sql .= " username='" . $username . "' ,mobile='" . $mobile . "'  ";
                $this->drcwdao->execute( $sql );
            }
        }

        echo json_encode($resultArray);
    }

    public function deletemsg($params){
        $id = $params['id'];
        $return_array = array(
            "code" => 1,
            "msg" => "删除成功！",
        );
        if ($id) {

            $info = $this->drcwdao->getMSGInfo($id);

            //删除
            $sql = "update XG_YJ_data_detail set ";
            $sql .= "isdel=1 ";
            if($info['openid']!=""){
                $sql .= " where openid='" . $info['openid'] . "' ";
            }else{
                $sql .= " where mobile='" . $info['mobile'] . "' ";
            }

            $this->drcwdao->execute($sql);

        } else {
            $return_array = array(
                "code" => 0,
                "msg" => "删除失败！",
            );
        }
        echo json_encode($return_array);
    }

    public function getmsgList($params){

        $page = $params['page'];
        $limit = $params['limit'];

        if ($page < 1) {
            $page = 1;
        }
        $start = ($page - 1) * $limit;
        $where = " isdel=0 ";

        $total = $this->drcwdao->getMSGListTotal($where);
        $dataInfo = $this->drcwdao->getMSGListInfo($where, $start, $limit);
        $data = array();
        foreach ($dataInfo as $tmp2){


            $where = " isdel=0 and mobile='".$tmp2['mobile']."' ";
            $msglist = $this->drcwdao->getMSGList($where);
            $arr = array();
            foreach ($msglist as $key=>$tmp){
                if($tmp['yj_varietyname']==""){
                    $tmp['yj_varietyname'] = $tmp['yj_type'];
                    $tmp['yj_type'] = "自定义预警";
                }
                $arr[$tmp['yj_type']][] = $tmp['yj_varietyname'];
            }
            //print_R($arr);
            foreach($arr as $key=>$value){
                $tmp2['yj_type'] = $key;
                $tmp2['yj_varietyname'] = implode(" ",$value);

                $data[] = $tmp2;
            }

        }
        //echo "<pre>";
        //print_r($data);

        $code = 0;
        if ($data) {
            $code = 0;
        }
        $return_array = array(
            "code" => $code,
            "data" => $data,
            "count" => $total
        );
        echo json_encode($return_array);
    }

    public function updatemsg($params)
    {
        $pid = $params['pid'];
        $is_stop = $params['is_stop'];
        $is_wx_yj = $params['is_wx_yj'];
        $return_array = array(
            "code" => 1,
            "msg" => "设置成功！"
        );
        if ($pid) {

            $info = $this->drcwdao->getMSGInfo($pid);


            //修改权限
            $sql = "update XG_YJ_data_detail set ";
            if(isset($params['is_stop']))
            $sql .= "is_stop='" . $is_stop . "'";

            if(isset($params['is_wx_yj']))
            $sql .= "is_wx_yj='".$is_wx_yj."' ";

            if($info['openid']!=""){
                $sql .= " where openid='" . $info['openid'] . "' ";
            }else{
                $sql .= " where mobile='" . $info['mobile'] . "' ";
            }

            $this->drcwdao->execute($sql);

        } else {
            $return_array = array(
                "code" => 0,
                "msg" => "设置失败！"
            );
        }
        echo json_encode($return_array);
    }

    public function updatevariety($params)
    {
        $id = $params['id'];//XG_YJ_data_detail.id
        $pid = $params['pid'];//XG_YJ_data_base.id
        $type = $params['type'];
        $state = $params['state'];//1 选中 0 取消选中

        $return_array = array(
            "code" => 0,
            "msg" => "设置失败！"
        );
        if ($id) {

            if($type=="one"){
                $where1 = " and id='".$pid."' ";
            }else if($type=="all"){
                $where1 = "";
            }

            $msginfo = $this->drcwdao->getMSGInfo($id);
            $where = " mobile='".$msginfo['mobile']."' ";
            $msglist = $this->drcwdao->getMSGList($where);
            $data = array();
            foreach ($msglist as $tmp){
                $data[$tmp['baseid']] = $tmp;
            }

            //print_r($data[2]['id']);

            $yjlist = $this->drcwdao->getYJList($where1);
            //print_r($yjlist);
            foreach ($yjlist as $tmp){
                //查询是否有设置
                if($state=="1"){
                    if(!empty($data[$tmp['id']])){
                        $sql = "update XG_YJ_data_detail set updatetime=now() ,isdel=0 where id='".$data[$tmp['id']]['id']."' ";
                    }else{
                        if($tmp['varietyname']==""){
                            $tmp['varietyname'] = $tmp['small_varietyname'];
                        }

                        $sql = "insert into XG_YJ_data_detail set createtime=now() ,username='" . $msginfo['username'] . "' ,mobile='" . $msginfo['mobile'] . "' ";
                        $sql .= ",openid='" . $msginfo['openid'] . "',is_stop='" . $msginfo['is_stop'] . "',is_wx_yj='" . $msginfo['is_wx_yj'] . "' ";
                        $sql .= ",baseid='" . $tmp['id'] . "',isdel=0,yj_type='" . $tmp['jy_zhibiao'] . "',yj_varietyname='" . $tmp['varietyname'] . "' ";
                    }
                }else{
                    $sql = "update XG_YJ_data_detail set updatetime=now() ,isdel=1 where id='".$data[$tmp['id']]['id']."' ";
                }

                $this->drcwdao->execute($sql);
            }

            $return_array = array(
                "code" => 1,
                "msg" => "设置成功！"
            );

        }
        echo json_encode($return_array);
    }

    public function setvarietyname($params){
        $id = $params['id'];
        $info = $this->drcwdao->getMSGInfo($id);


        $this->assign("info", $info);
        $this->assign("params", $params);
    }

    public function get_openid($params){
        $id=$params['id'];
        $openid = $this ->drcwdao->getOne("select openid from XG_YJ_data_detail where id='".$id."'");

        echo json_encode($openid);
        exit;
    }

    public function cancel($params){
        $id=$params['cancelid'];
        $openid = $this ->drcwdao->getOne("select openid from XG_YJ_data_detail where id='".$id."'");

        $this->drcwdao->execute("update  XG_YJ_data_detail set openid='',is_wx_yj=0,wx_name=NULL where openid='".$openid."'");
        alert("已取消微信绑定！");
        goURL("xg_price_yc.php?view=msg_manage");
    }

    public function postmsg($params){

        //已设置
        $where = " 1 ";
        $dataInfo = $this->drcwdao->getYJListInfo($where, 0, 10000);
        $set_info = array();
        foreach($dataInfo as $key=>$tmp){
            $set_info[$tmp['type']][$tmp['big_type']][$tmp['small_type']] = $tmp;
        }

        $date = $params['date']=="" ? date("Y-m-d") : $params['date'];
        //$date = "2023-01-28";
        //$url = "http://172.16.200.225/api/jgyc/getData?date=".$date;
        $url = "https://218.87.96.135:14007/api/jgyc/getData?date=".$date;
        $data = file_get_contents($url);
        $data = json_decode($data,true);
        //print_r($data);

        //最近一次推送数据日期
        $create_date = date("Y-m-d H:i:s");
        foreach ( $data['data'] as $key=>$tmp){
            if($tmp['create_date']!="")
            $create_date = $tmp['create_date'];
        }
        //echo "<pre>";
        //echo $create_date;
        //需要发送预警的品种
        $info_variety = array();
        foreach ($data['data'] as $tmp){

            if($create_date!=$tmp['create_date']){
                continue;
            }

            $quantity = $tmp['quantity'];
            $field1 = $tmp['field1'];//天数
            $field2 = $tmp['field2'];
            $type = $tmp['type'];
            $k = $tmp['big_type'];
            $k1 = $tmp['small_type'];
            if($tmp['small_type']=="" || $tmp['small_type']==null){
                $k = $tmp['big_type'];
                $k1 = $tmp['big_type'];
            }

            if($k=="" || $k==null){
                $small = explode("_",$k1);
                $k = $small[0];
            }

            $set = $set_info[$type][$k][$k1];

            if(!empty($set)){
                if($k=="I" && $quantity==0){
                    continue;
                }
                //print_r($set);
                //修改XG_YJ_data_base.quantity
                $sql = "update XG_YJ_data_base set quantity='".$quantity."',field1='".$field1."',field2='".$field2."' where id='" . $set['id'] . "' ";
                $this->drcwdao->execute($sql);

                if($set['isdel']=="1"){
                    continue;
                }
                if($type=="2"){
                    $field1 = $quantity;
                }

                $arr = array();
                $arr['id'] = $set['id'];
                $arr['unit'] = $set['unit'];
                $arr['type'] = $type;
                //$arr['quantity'] = $quantity;
                $arr['field1'] = $field1;
                $arr['jy_zhibiao'] = $set['jy_zhibiao'];
                $arr['varietyname'] = ($set['varietyname']=="" || $set['varietyname']=="合计") ? $set['small_varietyname'] : $set['varietyname'];
                /*if($quantity>=$set['minimum']){
                    $arr['message'] = "低于预警下限值".$set['minimum'];
                    if($quantity>=$set['ceiling']){
                        $arr['message'] = "超过预警上限值".$set['ceiling'];
                    }
                    $info_variety[] = $arr;
                }else{
                    continue;
                }*/
                if($field1>$set['ceiling']){
                    $arr['message'] = "超过预警上限值".$set['ceiling'];
                    $info_variety[] = $arr;
                }else if($field1<$set['minimum']){
                    $arr['message'] = "低于预警下限值".$set['minimum'];
                    $info_variety[] = $arr;
                }

            }
        }

        //print_r($info_variety);

        //查找设置的通知人员
        foreach($info_variety as $key=>$tmp){
            $type = $tmp['type'];
            $unit = $tmp['unit'];
            $field1 = $tmp['field1'];
            $jy_zhibiao = $tmp['jy_zhibiao'];
            $varietyname = $tmp['varietyname'];
            $message = $tmp['message'];
            //$content = "【姓名】您好！【预警品种】【预警类别】为XX【单位】，超过预警【上限/下限】";

            if($type=="1"){
                $content = "您好！".$varietyname.$jy_zhibiao."天数为".$field1."，".$message;
            }else{
                $content = "您好！".$varietyname.$jy_zhibiao."数量为".$field1.$unit."，".$message;
            }


            $where = " isdel=0 and is_stop=0 and baseid='".$tmp['id']."' ";
            $userinfo = $this->drcwdao->getMSGList($where);
            foreach($userinfo as $k=>$value){
                $arr = array();
                $arr['mobile'] = $value['mobile'];
                $arr['openid'] = $value['openid'];
                $arr['is_wx_yj'] = $value['is_wx_yj'];
                $arr['content'] = $value['username'].$content;

                //echo "<pre>";
                //print_r($arr);

                $sql = "insert into XG_YJ_data_message set createdate=now() ,mobile='" . $value['mobile'] . "' ,openid='" . $value['openid'] . "' ";
                $sql .= ",content='" . $arr['content'] . "',is_wx_yj='" . $value['is_wx_yj'] . "',baseid='".$tmp['id']."' ";

                $this->drcwdao->execute($sql);
                $messageid = $this->drcwdao->insert_id();
                $arr['messageid'] = $messageid;

                $this->msgsend($arr);
            }
        }


        echo "success";exit;
    }

    public function msgsend($params){
        $content = $params['content'];
        $mobile = $params['mobile'];
        $openid = $params['openid'];
        $is_wx_yj = $params['is_wx_yj'];
        $messageid = $params['messageid'];

        //$is_wx_yj = "1";
        //$openid = "o29gW5kW2F9Wf7YNqGG6F6becz8s";
        //$content = "短信测试";
        //$mobil = "15395601312";

        $msgcontent=$content;
        $desttermid=$mobile;

        if($is_wx_yj=="1" ){
            //$url="http://weixin.test.steelhome.cn/gzjbindwx/SendMessage.php?openid=".$openid."&content=".urlencode($msgcontent);
            $url="http://weixin.steelhome.cn/gzjbindwx/SendMessage.php?openid=".$openid."&content=".urlencode($msgcontent);
            $url .= "&url=".urlencode("https://dc.steelhome.cn/v1.5/web/xg_price_yc.php?view=yjmessage&messageid=".$messageid);
            $mes=file_get_contents($url);
        }else {

            if( $desttermid!="" && $content!="" && strlen($desttermid)==11 ) {


                $bizid = "";
                $sessionid = "";
                $msgcount = 1;
                $msgsign = "";
                $srcmid = 0;
                $srctermid = "";
                $priority = 3;
                $requesttime = "NOW()";
                $submittime = "1970-01-01 00:00:00";
                $seqid = "";
                $msgid = "";
                $status = 0;
                $smgstatus = "";
                $smgStatusNotes = "";
                $donetime = "1970-01-01 00:00:00";
                $reserved1 = "";
                $reserved2 = "";
                $reserved3 = "";
                $reserved4 = "";


                $sql_sms_send = "INSERT INTO sth_sm_mt_send(bizid,sessionid,msgcontent,msgcount,msgsign,srcmid,srctermid,desttermid,priority,requesttime,";
                $sql_sms_send .= "submittime,seqid,msgid,status,smgstatus,smgStatusNotes,donetime,reserved1,reserved2,reserved3,reserved4)  VALUES ";

                $sql_sms_send .= "('$bizid','$sessionid','$msgcontent','$msgcount','$msgsign','$srcmid','$srctermid','$desttermid','$priority',$requesttime,";
                $sql_sms_send .= "'$submittime','$seqid','$msgid','$status','$smgstatus','$smgStatusNotes','$donetime',";
                $sql_sms_send .= "'$reserved1','$reserved2','$reserved3','$reserved4')";

                $this->sms->execute("set character  set 'utf8';");
                $this->sms->execute("set names 'utf8';");

                $this->sms->Execute($sql_sms_send);

                $this->sms->execute("set character  set 'latin1';");
                $this->sms->execute("set names 'latin1';");
            }
        }

    }

    public function yjmessage($params){
        $messageid = $params['messageid'];

        $content = $this ->drcwdao->getOne("select content from XG_YJ_data_message where id='".$messageid."'");

        $this->assign("content", $content);
    }

    /**
     * layui alert
     * Created by zfy.
     * Date:2023/7/12 15:46
     */
    public function layAlert($text){
        echo '<script src="../js/layui.js"></script><script>			
						var layer = layui.layer;
						layer.alert("'.$text.'",function(index){
							window.parent.location.reload();
							var index=parent.layer.getFrameIndex(window.name); //获取当前窗口的name
							parent.layer.close(index);
						});
						</script>';
    }
    /**************************************************************
     *
     *  使用特定function对数组中所有元素做处理
     * @param string  &$array 要处理的字符串
     * @param string $function 要执行的函数
     * @return boolean $apply_to_keys_also     是否也应用到key上
     * @access private
     *
     *************************************************************/
    private function pri_arrayRecursive(&$array, $function, $apply_to_keys_also = false)
    {
        static $recursive_counter = 0;
        if (++$recursive_counter > 1000) {
            die('possible deep recursion attack');
        }
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $this->pri_arrayRecursive($array[$key], $function, $apply_to_keys_also);
            } else {
                $array[$key] = $function($value);
                //$array[$key] = $function(addslashes($value));
            }
            if ($apply_to_keys_also && is_string($key)) {
                $new_key = $function($key);
                if ($new_key != $key) {
                    $array[$new_key] = $array[$key];
                    unset($array[$key]);
                }
            }
        }
        $recursive_counter--;
    }

    //****************************************
    //
    //            JSON打包 start 将数组从gbk转为utf
    //
    //****************************************
    public function pri_JSON($array)
    {
        $this->pri_arrayRecursive($array, 'gbk2utf8', true);
        return json_encode($array);
    }

    public function getUserInfo($GUID){
        $user_infos = $this->t1dao->get_userinfo_byguid($GUID);
        if(empty($user_infos)){
            alert('用户不存在');
            exit;
        }
        $user_infos['TrueName'] = $user_infos['TrueName'];
        return $user_infos;
    }

    function array_iconv($str, $in_charset = "utf-8", $out_charset = "gbk")
    {
        if (is_array($str)) {
            foreach ($str as $k => $v) {
                $str[$k] = $this->array_iconv($v);
            }
            return $str;
        } else {
            if (is_string($str)) {
//                 return iconv($in_charset, $out_charset, $str);
                return mb_convert_encoding($str, $out_charset, $in_charset);
            } else {
                return $str;
            }
        }
    }

    public function by_pz_ship($params){
        if ($params['bydate']==''){
            $start_date = date('Y-m-01');
        }else{
            $start_date = date('Y-m-01',strtotime($params['bydate']));
        }
        $end_date = date('Y-m-d', strtotime("$start_date +1 month -1 day"));
        $params['sdate'] = date("Y-m",strtotime($start_date));

        if ($params['sydate']==''){
            $sy_sdate = date('Y-m-01',strtotime(date('Y-m-01')." -1 month"));

        }else{
            $sy_sdate = date('Y-m-01',strtotime($params['sydate']));
        }
        $sy_edate = date('Y-m-d', strtotime("$sy_sdate +1 month -1 day"));
        $params['sydate'] = date("Y-m",strtotime($sy_sdate));

        if ($params['qndate']==''){
            $qn_sdate = date('Y-m-01',strtotime(date('Y-m-01')." -1 year"));

        }else{
            $qn_sdate =   date('Y-m-01',strtotime($params['qndate']));
        }
        $qn_edate = date('Y-m-d', strtotime("$qn_sdate +1 month -1 day"));
        $params['qndate'] =  date("Y-m",strtotime($qn_sdate));
        // echo $start_date."--".$end_date."--".$sy_sdate."--".$sy_edate."--".$qn_sdate."--".$qn_edate;exit;

        $tks_shpi_benyue = round($this->maindao->getOne("select avg(weipriceusb) from shpi_material_pzp where vid=3 and dateday >= '".$start_date."' and dateday<= '".$end_date."'"),2);
        $tks_shpi_shangyue = round($this->maindao->getOne("select avg(weipriceusb) from shpi_material_pzp where vid=3 and dateday >= '".$sy_sdate."' and dateday<= '".$sy_edate."'"),2);
        $tks_shpi_qn = round($this->maindao->getOne("select avg(weipriceusb) from shpi_material_pzp where vid=3 and dateday >= '".$qn_sdate."' and dateday<= '".$qn_edate."'"),2);
        $tks_hbzd = $tks_shpi_benyue-$tks_shpi_shangyue;
        $tks_hbzdf = round($tks_hbzd/$tks_shpi_shangyue*100)."%";
        $tks_tbzd = $tks_shpi_benyue-$tks_shpi_qn;
        $tks_tbzdf = round($tks_tbzd/$tks_shpi_qn*100)."%";
        
        $jt_shpi_benyue = round($this->maindao->getOne("select avg(price) from shpi_material WHERE topicture='3' and dateday >= '".$start_date."' and dateday<= '".$end_date."'"));
        $jt_shpi_shangyue = round($this->maindao->getOne("select avg(price) from shpi_material WHERE topicture='3' and dateday >= '".$sy_sdate."' and dateday<= '".$sy_edate."'"));
        $jt_shpi_qn = round($this->maindao->getOne("select avg(price) from shpi_material WHERE topicture='3' and dateday >= '".$qn_sdate."' and dateday<= '".$qn_edate."'"));
        $jt_hbzd = $jt_shpi_benyue-$jt_shpi_shangyue;
        $jt_hbzdf = round($jt_hbzd/$jt_shpi_shangyue*100)."%";
        $jt_tbzd = $jt_shpi_benyue-$jt_shpi_qn;
        $jt_tbzdf = round($jt_tbzd/$jt_shpi_qn*100)."%";

        $mt_shpi_benyue = round($this->maindao->getOne("select avg(weiprice) from shpi_mj_pzp WHERE vid=1 AND type=1 and dateday >= '".$start_date."' and dateday<= '".$end_date."'"));
        $mt_shpi_shangyue = round($this->maindao->getOne("select avg(weiprice) from shpi_mj_pzp WHERE vid=1 AND type=1 and dateday >= '".$sy_sdate."' and dateday<= '".$sy_edate."'"));
        $mt_shpi_qn = round($this->maindao->getOne("select avg(weiprice) from shpi_mj_pzp WHERE vid=1 AND type=1 and dateday >= '".$qn_sdate."' and dateday<= '".$qn_edate."'"));
        $mt_hbzd = $mt_shpi_benyue-$mt_shpi_shangyue;
        $mt_hbzdf = round($mt_hbzd/$mt_shpi_shangyue*100)."%";
        $mt_tbzd = $mt_shpi_benyue-$mt_shpi_qn;
        $mt_tbzdf = round($mt_tbzd/$mt_shpi_qn*100)."%";
        
        $lwg_shpi_benyue = round($this->maindao->getOne("select avg(weiprice) from shpi_pzp WHERE bvm_id=2 and dateday >= '".$start_date."' and dateday<= '".$end_date."'"));
        $lwg_shpi_shangyue = round($this->maindao->getOne("select avg(weiprice) from shpi_pzp WHERE bvm_id=2 and dateday >= '".$sy_sdate."' and dateday<= '".$sy_edate."'"));
        $lwg_shpi_qn = round($this->maindao->getOne("select avg(weiprice) from shpi_pzp WHERE bvm_id=2 and dateday >= '".$qn_sdate."' and dateday<= '".$qn_edate."'"));
        $lwg_hbzd = $lwg_shpi_benyue-$lwg_shpi_shangyue;
        $lwg_hbzdf = round($lwg_hbzd/$lwg_shpi_shangyue*100)."%";
        $lwg_tbzd = $lwg_shpi_benyue-$lwg_shpi_qn;
        $lwg_tbzdf = round($lwg_tbzd/$lwg_shpi_qn*100)."%";

        $rz_shpi_benyue = round($this->maindao->getOne("select avg(weiprice) from shpi_pzp WHERE bvm_id=5 and dateday >= '".$start_date."' and dateday<= '".$end_date."'"));
        $rz_shpi_shangyue = round($this->maindao->getOne("select avg(weiprice) from shpi_pzp WHERE bvm_id=5 and dateday >= '".$sy_sdate."' and dateday<= '".$sy_edate."'"));
        $rz_shpi_qn = round($this->maindao->getOne("select avg(weiprice) from shpi_pzp WHERE bvm_id=5 and dateday >= '".$qn_sdate."' and dateday<= '".$qn_edate."'"));
        $rz_hbzd = $rz_shpi_benyue-$rz_shpi_shangyue;
        $rz_hbzdf = round($rz_hbzd/$rz_shpi_shangyue*100)."%";
        $rz_tbzd = $rz_shpi_benyue-$rz_shpi_qn;
        $rz_tbzdf = round($rz_tbzd/$rz_shpi_qn*100)."%";

        $lz_shpi_benyue = round($this->maindao->getOne("select avg(weiprice) from shpi_pzp WHERE bvm_id=6 and dateday >= '".$start_date."' and dateday<= '".$end_date."'"));
        $lz_shpi_shangyue = round($this->maindao->getOne("select avg(weiprice) from shpi_pzp WHERE bvm_id=6 and dateday >= '".$sy_sdate."' and dateday<= '".$sy_edate."'"));
        $lz_shpi_qn = round($this->maindao->getOne("select avg(weiprice) from shpi_pzp WHERE bvm_id=6 and dateday >= '".$qn_sdate."' and dateday<= '".$qn_edate."'"));
        $lz_hbzd = $lz_shpi_benyue-$lz_shpi_shangyue;
        $lz_hbzdf = round($lz_hbzd/$lz_shpi_shangyue*100)."%";
        $lz_tbzd = $lz_shpi_benyue-$lz_shpi_qn;
        $lz_tbzdf = round($lz_tbzd/$lz_shpi_qn*100)."%";


        $zhb_shpi_benyue = round($this->maindao->getOne("select avg(weiprice) from shpi_pzp WHERE bvm_id=4 and dateday >= '".$start_date."' and dateday<= '".$end_date."'"));
        $zhb_shpi_shangyue = round($this->maindao->getOne("select avg(weiprice) from shpi_pzp WHERE bvm_id=4 and dateday >= '".$sy_sdate."' and dateday<= '".$sy_edate."'"));
        $zhb_shpi_qn = round($this->maindao->getOne("select avg(weiprice) from shpi_pzp WHERE bvm_id=4 and dateday >= '".$qn_sdate."' and dateday<= '".$qn_edate."'"));
        $zhb_hbzd = round($zhb_shpi_benyue-$zhb_shpi_shangyue);
        $zhb_hbzdf = round($zhb_hbzd/$zhb_shpi_shangyue*100)."%";
        $zhb_tbzd = $zhb_shpi_benyue-$zhb_shpi_qn;
        $zhb_tbzdf = round($zhb_tbzd/$zhb_shpi_qn*100)."%";

        $fg_shpi_benyue = round($this->maindao->getOne("select avg(price) from shpi_material WHERE topicture='4' and dateday >= '".$start_date."' and dateday<= '".$end_date."'"));
        $fg_shpi_shangyue = round($this->maindao->getOne("select avg(price) from shpi_material WHERE topicture='4' and dateday >= '".$sy_sdate."' and dateday<= '".$sy_edate."'"));
        $fg_shpi_qn = round($this->maindao->getOne("select avg(price) from shpi_material WHERE topicture='4' and dateday >= '".$qn_sdate."' and dateday<= '".$qn_edate."'"));
        $fg_hbzd = $fg_shpi_benyue-$fg_shpi_shangyue;
        $fg_hbzdf = round($fg_hbzd/$fg_shpi_shangyue*100)."%";
        $fg_tbzd = $fg_shpi_benyue-$fg_shpi_qn;
        $fg_tbzdf = round($fg_tbzd/$fg_shpi_qn*100)."%";

        $tscb_shpi_benyue = round($this->maindao->getOne("select avg(price) from shpi_material WHERE topicture='50' and dateday >= '".$start_date."' and dateday<= '".$end_date."'"));
        $tscb_shpi_shangyue = round($this->maindao->getOne("select avg(price) from shpi_material WHERE topicture='50' and dateday >= '".$sy_sdate."' and dateday<= '".$sy_edate."'"));
        $tscb_shpi_qn = round($this->maindao->getOne("select avg(price) from shpi_material WHERE topicture='50' and dateday >= '".$qn_sdate."' and dateday<= '".$qn_edate."'"));
        $tscb_hbzd = $tscb_shpi_benyue-$tscb_shpi_shangyue;
        $tscb_hbzdf = round($tscb_hbzd/$tscb_shpi_shangyue*100)."%";
        $tscb_tbzd = $tscb_shpi_benyue-$tscb_shpi_qn;
        $tscb_tbzdf = round($tscb_tbzd/$tscb_shpi_qn*100)."%";

        $gplr_benyue = round($this->drcdao->getOne("select avg(lirun) from sg_HangYeLiRunIndex where  type='10' and mc_type=0 and del=0 and ndate>= '".$start_date."' and ndate<= '".$end_date."'"));
        $gplr_shangyue = round($this->drcdao->getOne("select avg(lirun) from sg_HangYeLiRunIndex where  type='10' and mc_type=0 and del=0 and ndate>= '".$sy_sdate."' and ndate<= '".$sy_edate."'"));
        $gplr_qn = round($this->drcdao->getOne("select avg(lirun) from sg_HangYeLiRunIndex where  type='10' and mc_type=0 and del=0 and ndate>= '".$qn_sdate."' and ndate<= '".$qn_edate."'"));
        $gplr_hbzd = $gplr_benyue-$gplr_shangyue;
        $gplr_hbzdf = round($gplr_hbzd/$gplr_shangyue*100)."%";
        $gplr_tbzd = $gplr_benyue-$gplr_qn;
        $gplr_tbzdf = round($gplr_tbzd/$gplr_qn*100)."%";

        $ret_arr = array(
            0=>array(date("n月",strtotime($start_date))."月均",$tks_shpi_benyue,$jt_shpi_benyue,$mt_shpi_benyue,$lwg_shpi_benyue,$rz_shpi_benyue,$lz_shpi_benyue,$zhb_shpi_benyue,$fg_shpi_benyue,$tscb_shpi_benyue,$gplr_benyue),
            1=>array(date("n月",strtotime($sy_sdate))."月均",$tks_shpi_shangyue,$jt_shpi_shangyue,$mt_shpi_shangyue,$lwg_shpi_shangyue,$rz_shpi_shangyue,$lz_shpi_shangyue,$zhb_shpi_shangyue,$fg_shpi_shangyue,$tscb_shpi_shangyue,$gplr_shangyue),
            2=>array("环比涨跌",$tks_hbzd,$jt_hbzd,$mt_hbzd,$lwg_hbzd,$rz_hbzd,$lz_hbzd,$zhb_hbzd,$fg_hbzd,$tscb_hbzd,$gplr_hbzd),
            3=>array("环比涨跌幅",$tks_hbzdf,$jt_hbzdf,$mt_hbzdf,$lwg_hbzdf,$rz_hbzdf,$lz_hbzdf,$zhb_hbzdf,$fg_hbzdf,$tscb_hbzdf,$gplr_hbzdf),
            4=>array("去年".date("n月",strtotime($qn_sdate))."月均",$tks_shpi_qn,$jt_shpi_qn,$mt_shpi_qn,$lwg_shpi_qn,$rz_shpi_qn,$lz_shpi_qn,$zhb_shpi_qn,$fg_shpi_qn,$tscb_shpi_qn,$gplr_qn),
            5=>array("同比涨跌",$tks_tbzd,$jt_tbzd,$mt_tbzd,$lwg_tbzd,$rz_tbzd,$lz_tbzd,$zhb_tbzd,$fg_tbzd,$tscb_tbzd,$gplr_tbzd),
            6=>array("同比涨跌幅",$tks_tbzdf,$jt_tbzdf,$mt_tbzdf,$lwg_tbzdf,$rz_tbzdf,$lz_tbzdf,$zhb_tbzdf,$fg_tbzdf,$tscb_tbzdf,$gplr_tbzdf)
        );
        // echo "<pre/>";print_r($ret_arr);
        $this->assign("ret_arr", $ret_arr);
        $this->assign("params", $params);
    }
    public function thj_pz_price($params){
        if ($params['sdate']==''){
            $start_date = date('Y-m-01');
            $last_start_date = date('Y-m-d', strtotime("$start_date -1 month"));
        }else{
            $start_date = date('Y-m-01',strtotime($params['sdate']));
            $last_start_date = date('Y-m-01',strtotime($params['edate']));
        }
        $end_date = date('Y-m-d', strtotime("$start_date +1 month -1 day"));
        $last_end_date = date('Y-m-d', strtotime("$last_start_date +1 month -1 day"));

        //硅锰8 硅铁7 铝粒16 高碳铬铁9
        $query_data = $this->maindao->query( "select price,topicture from shpi_material WHERE topicture in ('8','7','16','9') and dateday <= '". $end_date."' and dateday >= '".$start_date."'" );
        $mt_shpi = $this->maindao->getOne( "select avg(price) from marketconditions where mastertopid='4593101' and mconmanagedate<= '". $end_date." 23:59:59' and mconmanagedate >= '".$start_date." 00:00:00'" );
        $ft_shpi = $this->maindao->getOne( "select avg(price) from marketconditions where topicture='459410' and mconmanagedate<= '". $end_date." 23:59:59' and mconmanagedate >= '".$start_date." 00:00:00'" );

        //上期数据
        $query_data2 = $this->maindao->query( "select price,topicture from shpi_material WHERE topicture in ('8','7','16','9') and dateday <= '". $last_end_date."' and dateday >= '".$last_start_date."'" );
        $mt_shpi2 = $this->maindao->getOne( "select avg(price) from marketconditions where mastertopid='4593101' and mconmanagedate<= '". $last_end_date." 23:59:59' and mconmanagedate >= '".$last_start_date." 00:00:00'" );
        $ft_shpi2 = $this->maindao->getOne( "select avg(price) from marketconditions where topicture='459410' and mconmanagedate<= '". $last_end_date." 23:59:59' and mconmanagedate >= '".$last_start_date." 00:00:00'" );
        $i8 = 0;
        $i7 = 0;
        $i16 = 0;
        $i9 = 0;
        $bq_data_arr = array();
        foreach($query_data as $key=>$v){
            if($v['topicture'] == "8"){
                $bq_data_arr[0] += $v['price'];
                $i8++;
            }
            if($v['topicture'] == "7"){
                $bq_data_arr[1] += $v['price'];
                $i7++;
            }
            if($v['topicture'] == "16"){
                $bq_data_arr[2] += $v['price'];
                $i16++;
            }
            if($v['topicture'] == "9"){
                $bq_data_arr[3] += $v['price'];
                $i9++;
            }
        }
        $bq_data_arr[0] = $i8 ? round($bq_data_arr[0]/$i8) : 0;
        $bq_data_arr[1] = $i7 ? round($bq_data_arr[1]/$i7) : 0;
        $bq_data_arr[2] = $i16 ? round($bq_data_arr[2]/$i16) : 0;
        $bq_data_arr[3] = $i9 ? round($bq_data_arr[3]/$i9) : 0;
        $bq_data_arr[4] = round($mt_shpi);
        $bq_data_arr[5] = round($ft_shpi);

        $i8 = 0;
        $i7 = 0;
        $i16 = 0;
        $i9 = 0;
        $bq_data_arr2 = array();
        foreach($query_data2 as $key=>$v){
            if($v['topicture'] == "8"){
                $bq_data_arr2[0] += $v['price'];
                $i8++;
            }
            if($v['topicture'] == "7"){
                $bq_data_arr2[1] += $v['price'];
                $i7++;
            }
            if($v['topicture'] == "16"){
                $bq_data_arr2[2] += $v['price'];
                $i16++;
            }
            if($v['topicture'] == "9"){
                $bq_data_arr2[3] += $v['price'];
                $i9++;
            }
        }
        $bq_data_arr2[0] = $i8 ? round($bq_data_arr2[0]/$i8) : 0;
        $bq_data_arr2[1] = $i7 ? round($bq_data_arr2[1]/$i7) : 0;
        $bq_data_arr2[2] = $i16 ? round($bq_data_arr2[2]/$i16) : 0;
        $bq_data_arr2[3] = $i9 ? round($bq_data_arr2[3]/$i9) : 0;
        $bq_data_arr2[4] = round($mt_shpi2);
        $bq_data_arr2[5] = round($ft_shpi2);

        ksort($bq_data_arr);
        ksort($bq_data_arr2);
        $hbzd_arr[] = $bq_data_arr[0] - $bq_data_arr2[0];
        $hbzd_arr[] = $bq_data_arr[1] - $bq_data_arr2[1];
        $hbzd_arr[] = $bq_data_arr[2] - $bq_data_arr2[2];
        $hbzd_arr[] = $bq_data_arr[3] - $bq_data_arr2[3];
        $hbzd_arr[] = $bq_data_arr[4] - $bq_data_arr2[4];
        $hbzd_arr[] = $bq_data_arr[5] - $bq_data_arr2[5];
        $ret_arr = array(date("n月"."月均",strtotime($last_start_date))=>$bq_data_arr2,date("n月"."月均",strtotime($start_date))=>$bq_data_arr,"环比涨跌"=>$hbzd_arr);
        // echo "<pre/>";print_r($ret_arr);
        $params['sdate'] =  date("Y-m",strtotime($start_date));
        $params['edate'] =  date("Y-m",strtotime($last_start_date));
        $this->assign("params", $params);
        $this->assign("ret_arr", $ret_arr);
    }

    function tf_lf_jc($params){
        if ($params['sdate']==''){
            $start_date = date('Y-m-01');
            $last_start_date = date('Y-m-d', strtotime("$start_date -1 month"));
        }else{
            $start_date = date('Y-m-01',strtotime($params['sdate']));
            $last_start_date = date('Y-m-01',strtotime($params['edate']));
        }
        $end_date = date('Y-m-d', strtotime("$start_date +1 month -1 day"));
        $last_end_date = date('Y-m-d', strtotime("$last_start_date +1 month -1 day"));
        // echo "<pre/>";print_r($last_end_date);

         //废钢华东地区10 （shpi_pzp WHERE bvm_id=2 螺纹钢指数） （sg_HangYeChengBenIndex where  type='35' and mc_type=0  铁水成本）
        $by_fg_avg_price = $this->maindao->getOne( "select avg(price) from shpi_material WHERE topicture = '10' and dateday <= '". $end_date."' and dateday >= '".$start_date."'" );
        $sy_fg_avg_price = $this->maindao->getOne( "select avg(price) from shpi_material WHERE topicture = '10' and dateday <= '". $last_end_date."' and dateday >= '".$last_start_date."'" );

        $by_lwg_avg_price = $this->maindao->getOne( "select avg(weiprice) from shpi_pzp WHERE bvm_id = 2 and dateday <= '". $end_date."' and dateday >= '".$start_date."'" );
        $sy_lwg_avg_price = $this->maindao->getOne( "select avg(weiprice) from shpi_pzp WHERE bvm_id = 2 and dateday <= '". $last_end_date."' and dateday >= '".$last_start_date."'" );

        $by_tscb_avg_price = $this->drcdao->getOne( "select avg(chenben_tax) from sg_HangYeChengBenIndex  where  type='35' and mc_type=0 and ndate <= '". $end_date."' and ndate >= '".$start_date."'" );
        $sy_tscb_avg_price = $this->drcdao->getOne( "select avg(chenben_tax) from sg_HangYeChengBenIndex  where  type='35' and mc_type=0 and ndate <= '". $last_end_date."' and ndate >= '".$last_start_date."'" );

        $tscb_by_yj = round($by_tscb_avg_price,2);
        $tscb_sy_yj = round($sy_tscb_avg_price,2);
        $tscb_yj_hb = $tscb_by_yj - $tscb_sy_yj;

        $fg_by_yj = round($by_fg_avg_price);
        $fg_sy_yj = round($sy_fg_avg_price);
        $fg_yj_hb = $fg_by_yj - $fg_sy_yj;

        $lf_by_yj = round($by_lwg_avg_price) - round($by_fg_avg_price);
        $lf_sy_yj = round($sy_lwg_avg_price) - round($sy_fg_avg_price);
        $lf_yj_hb = $lf_by_yj - $lf_sy_yj;

        $tfc_by_yj = $tscb_by_yj - $fg_by_yj;
        $tfc_sy_yj = $tscb_sy_yj - $fg_sy_yj;
        $tfc_yj_hb = $tfc_by_yj - $tfc_sy_yj;

        $ret_arr = array("华东铁水成本"=>array($tscb_by_yj,$tscb_sy_yj,$tscb_yj_hb),
                         "华东地区废钢指数"=>array($fg_by_yj,$fg_sy_yj,$fg_yj_hb),
                         "螺废价差"=>array($lf_by_yj,$lf_sy_yj,$lf_yj_hb),
                         "铁废价差"=>array($tfc_by_yj,$tfc_sy_yj,$tfc_yj_hb)
                        );
        $this->assign("params", $params);
        $this->assign("ret_arr", $ret_arr);
        $this->assign("yuejun1", date("n月"."月均",strtotime($start_date)));
        $this->assign("yuejun2", date("n月"."月均",strtotime($last_start_date)));
    }
}
function gbk2utf8($str)
{
    return $str;
    //return iconv("gbk","utf-8",$str);
//    return iconv("GB18030", "utf-8", $str);
}
?>