<?php
class  xg_price_ycD<PERSON> extends Dao{
	public function __construct($writer){
		 parent::__construct( $writer );
	}

	//通过mid取得公司信息
	public function get_userinfo_bymid($MID,$mc_type){
		$sql = "select * from app_license where  MID ='$MID' and mc_type='$mc_type' order by  CreateDate DESC  limit 1";
		return $this->getRow($sql);
	}


	//通过guid取得用户信息
	public function get_userinfo_byguid($GUID){
		$sql = "select * from app_session_temp where  GUID ='$GUID' order by  LoginDate DESC  limit 1";
		return $this->getRow($sql);
	}


	//通过guid取得用户信息
	public function get_detail_byguid($ID){
		$sql = "select * from app_license_detail where  ID ='$ID'  order by  CreateDate DESC  limit 1";
		return $this->getRow($sql);
	}

	//通过SignCS取得设备用户信息
	public function get_userinfo_bySignCS($SignCS,$mc_type,$MID){
		$sql = "select * from app_session_temp where  SignCS ='$SignCS' and mc_type='$mc_type' AND Mid='$MID' order by  LoginDate DESC  limit 1";
		return $this->getRow($sql);
	}


	public function get_admin_byguid($userid){
		$sql = "select * from adminuser where  id ='$userid' ";
		return $this->getRow($sql);
	}

    public function getYJInfo($id)
    {
        $sql = "SELECT * FROM XG_YJ_data_base where  id='" . $id . "' ";
        $data = $this->getRow($sql);
        return $data;
    }

    public function getYJListTotal($where)
    {
        $sql = "SELECT COUNT(id) as c FROM XG_YJ_data_base  where " . $where;
        //echo $sql;
        $data = $this->getOne($sql);
        return $data;
    }

    public function getYJListInfo($where, $start, $limit)
    {
        $sql = "SELECT * FROM XG_YJ_data_base where " . $where . " ORDER BY big_type ASC,small_type ASC limit $start,$limit";
        //echo $sql;
        $data = $this->query($sql);
        //print_r($data);
        return $data;
    }

    public function getYJList($where)
    {
        $sql = "SELECT * FROM XG_YJ_data_base where isdel=0 " . $where . " ";
        $data = $this->query($sql);
        return $data;
    }

    public function getMSGInfo($id)
    {
        $sql = "SELECT * FROM XG_YJ_data_detail where  id='" . $id . "' ";
        $data = $this->getRow($sql);
        return $data;
    }

    public function getMSGListTotal($where)
    {
        $sql = "SELECT COUNT(id) as c FROM XG_YJ_data_detail  where " . $where;
        //echo $sql;
        $data = $this->getOne($sql);
        return $data;
    }

    public function getMSGListInfo($where, $start, $limit)
    {
        $sql = "SELECT * FROM XG_YJ_data_detail where " . $where . " group by mobile ORDER BY id ASC  limit $start,$limit";
        //echo $sql;
        $data = $this->query($sql);
        return $data;
    }

    public function getMSGList($where)
    {
        $sql = "SELECT * FROM XG_YJ_data_detail where " . $where ;
        $data = $this->query($sql);
        return $data;
    }

    public function getMSGbaseid($where)
    {
        $sql = "SELECT baseid FROM XG_YJ_data_detail where " . $where . " ";
        //echo $sql;
        $data = $this->getOnes($sql);
        return $data;
    }

    /**
     * 根据id获取预测结束时间 结束时间就是计算的时间
     * Created by zfy.
     * Date:2023/1/9 10:35
     * @param $id
     * @return mixed
     */
    public function getDateByBaseId($id){
	    return $this->getOne("select yc_edate from XG_day_meadow_month_predict where id='$id'");
    }

    /**
     * 获取
     * Created by zfy.
     * Date:2023/1/7 14:39
     * @param $xunDateList
     * @param $yc_type
     * @return array
     */
    public function getForecastInfo($xunDateList,$yc_type){
        $arr = $this->query("select dmmp.yc_direction,dmmp.yc_fudu1,dmmp.yc_fudu2,dmmp.yc_varietyname,acc.avg_price,acc.last_price from XG_day_meadow_month_predict dmmp,XG_accuracy acc where dmmp.id=acc.baseid and dmmp.yc_sdate='".$xunDateList['this_start_date']."' and dmmp.yc_edate='".$xunDateList['this_end_date']."' and dmmp.yc_type='$yc_type'");

        $lastArr = $this->query("select dmmp.yc_direction,dmmp.yc_fudu1,dmmp.yc_fudu2,dmmp.yc_varietyname,acc.avg_price,acc.last_price from XG_day_meadow_month_predict dmmp,XG_accuracy acc where dmmp.id=acc.baseid and dmmp.yc_sdate='".$xunDateList['last_start_date']."' and dmmp.yc_edate='".$xunDateList['last_end_date']."' and dmmp.yc_type='$yc_type'");
        //共四个品种  前九旬数据就是 4 * 9 = 36条数据
        $varietyCount = 4;
        $where = "";
        $limit = "";
        //前九旬数据  1=日 2=旬 3=月
        switch ($yc_type){
            case 1:
                $lastMonth = date("Y-m-d",strtotime($xunDateList['this_end_date']."-1 month"));
                $where = " and js_date>='$lastMonth'";
                break;
            case 2:
                $num = $varietyCount * 9;
                $limit = "limit $num";
                break;
            case 3:
                $num = $varietyCount * 3;
                $limit = "limit $num";
                break;

        }
//        if($yc_type == 2){
//            $num = $varietyCount * 9;
//            $historyInfo = $this->query("SELECT t.* from ( SELECT t1.*, (SELECT count(*) + 1 FROM XG_accuracy t2 WHERE t2.varietyname = t1.varietyname AND t2.js_date > t1.js_date and t1.js_date<='".$xunDateList['this_end_date']."') top FROM XG_accuracy t1 ) t where top <=$num AND yc_type =2");
//        }else{
//            $num = $varietyCount * 3;
//            $historyInfo = $this->query("SELECT t.* from ( SELECT t1.*, (SELECT count(*) + 1 FROM XG_accuracy t2 WHERE t2.varietyname = t1.varietyname AND t2.js_date > t1.js_date and t1.js_date<='".$xunDateList['this_end_date']."') top FROM XG_accuracy t1 ) t where top <=$num AND yc_type =3");
//        }
        $historyInfo = $this->query("SELECT * FROM `XG_accuracy` WHERE `yc_type`=$yc_type and js_date<='".$xunDateList['this_end_date']."' $where order by js_date desc $limit");
       
        $zdList = array(1 => "上涨", 2 => "持平", 3 => "下跌");
        $retList = array();
        if ($arr) {
            foreach ($arr as $item) {
                $retList[$item['yc_varietyname']]['yc_direction'] = $zdList[$item['yc_direction']];
                $retList[$item['yc_varietyname']]['yc_fudu'] = ($item['yc_fudu1'] == 0 && $item['yc_fudu2'] == 0) ? "—" : $item['yc_fudu1'] . "-" . $item['yc_fudu2'];
                $retList[$item['yc_varietyname']]['avg_price'] = $item['avg_price'];
                $retList[$item['yc_varietyname']]['last_price'] = $item['last_price'];
                if ($lastArr) {
                    foreach ($lastArr as $ii => $lastItem) {
                        if ($item['yc_varietyname'] == $lastItem['yc_varietyname']) {
                            $retList[$item['yc_varietyname']]['avg_price_zd'] = $this->zhangdie($item['avg_price'] - $lastItem['avg_price'], 1);
                            $retList[$item['yc_varietyname']]['last_price_zd'] = $this->zhangdie($item['last_price'] - $lastItem['last_price'], 1);
                        }
                    }
                }
            }
        }

        if ($historyInfo){
            $calcList = array();
            foreach ($historyInfo as $hisItem) {
                $calcList['fx_accuracy'] += $hisItem['fx_accuracy'];
                $calcList['fx_fd_accuracy'] += $hisItem['fx_fd_accuracy'];
                $calcList['count'] += 1;

                $retList[$hisItem['varietyname']]['fx_accuracy'] += $hisItem['fx_accuracy'];
                $retList[$hisItem['varietyname']]['fx_fd_accuracy'] += $hisItem['fx_fd_accuracy'];
                $retList[$hisItem['varietyname']]['count'] += 1;
            }
            foreach ($retList as $index => &$item) {
                $item['fx_percent'] = round($item['fx_accuracy'] / $item['count'] * 100,2)."%";
                $item['fx_fd_percent'] = round($item['fx_fd_accuracy'] / $item['count'] * 100,2)."%";
            }
            $xunStr = $yc_type == 2 ? "（9旬）" : "";
            $retList['content'] = "最近三个月".$xunStr."方向正确率为：".round($calcList['fx_accuracy'] / $calcList['count'] * 100,2)."%；方向幅度正确率为：".round($calcList['fx_fd_accuracy'] / $calcList['count'] * 100,2)."%。";
        }
	    return $retList;
    }

    /**
     * 获取下旬预测数据
     * Created by zfy.
     * Date:2023/1/12 9:30
     * @param $xunDateList
     * @param int $yc_type
     * @return array
     */
    public function getLastXunForecast($xunDateList,$yc_type = 2){
        $date = date("Y-m-d",strtotime($xunDateList['this_end_date']."+1 day"));
        $LastXunForecast = $this->query("select yc_varietyname,yc_direction,yc_fudu1,yc_fudu2 from XG_day_meadow_month_predict where yc_sdate='$date' and yc_type='$yc_type'");
        $retList = array();
        $zdList = array(1 => "上涨", 2 => "持平", 3 => "下跌");
        if ($LastXunForecast) {
            foreach ($LastXunForecast as $item) {
                $retList[$item['yc_varietyname']]['yc_direction'] = $zdList[$item['yc_direction']];
                $retList[$item['yc_varietyname']]['yc_fudu'] = ($item['yc_fudu1'] == 0 && $item['yc_fudu2'] == 0) ? "—" : $item['yc_fudu1'] . "-" . $item['yc_fudu2'];
            }
        }
        return $retList;
    }

    /**
     * 价格信息获取
     * Created by zfy.
     * Date:2023/1/9 11:08
     * @param $workDate
     * @param $steelPriceIdList
     * @param $rawPriceIdList
     * @return array
     */
    public function getMarketPrice($workDate,$steelPriceIdList,$rawPriceIdList){
        $allPriceIdList = array_merge($steelPriceIdList,$rawPriceIdList);
        $idList = $this->getPriceIdStr($allPriceIdList);
        $id6Str = $idList['id6'];
        $id7Str = $idList['id7'];
        $endPrice = $this->query("select price, topicture, mastertopid from marketconditions where mconmanagedate>='".$workDate['this_end_date']." 00:00:00' and mconmanagedate<='".$workDate['this_end_date']." 23:59:59' and (topicture in('$id6Str') or mastertopid in('$id7Str'))");
        $startPrice = $this->query("select price, topicture, mastertopid from marketconditions where mconmanagedate>='".$workDate['this_start_date']." 00:00:00' and mconmanagedate<='".$workDate['this_start_date']." 23:59:59' and (topicture in('$id6Str') or mastertopid in('$id7Str'))");
        return array("startPrice"=>$startPrice,"endPrice"=>$endPrice);
    }

    /**
     * 获取周 上周 上年价格信息
     * Created by zfy.
     * Date:2023/2/28 11:24
     * @param $workDate
     * @param $PriceIdList
     * @return array
     */
    public function getWeekYearMarketPrice($workDate,$PriceIdList){
        $idList = $this->getPriceIdStr($PriceIdList);
        $id6 = $idList['id6'];
        $id7 = $idList['id7'];

        $endPrice6 = $this->query("select a.topicture as priceid,a.price from marketconditions a,(select topicture,max(mconmanagedate) mconmanagedate from marketconditions where mconmanagedate<='".$workDate['end_date']." 23:59:59' AND topicture IN ('$id6') group by topicture) b where a.topicture= b.topicture and a.mconmanagedate= b.mconmanagedate");
        $startPrice6 = $this->query("select a.topicture as priceid,a.price from marketconditions a,(select topicture,max(mconmanagedate) mconmanagedate from marketconditions where mconmanagedate<='".$workDate['start_date']." 23:59:59' AND topicture IN ('$id6') group by topicture) b where a.topicture= b.topicture and a.mconmanagedate= b.mconmanagedate");
        $lastYearPrice6 = $this->query("select a.topicture as priceid,a.price from marketconditions a,(select topicture,max(mconmanagedate) mconmanagedate from marketconditions where mconmanagedate<='".$workDate['lastYearDate']." 23:59:59' AND topicture IN ('$id6') group by topicture) b where a.topicture= b.topicture and a.mconmanagedate= b.mconmanagedate");


        $endPrice7 = $this->query("select a.mastertopid as priceid,a.price from marketconditions a,(select mastertopid,max(mconmanagedate) mconmanagedate from marketconditions where mconmanagedate<='".$workDate['end_date']." 23:59:59' AND mastertopid IN ('$id7') group by mastertopid) b where a.mastertopid= b.mastertopid and a.mconmanagedate= b.mconmanagedate");
        $startPrice7 = $this->query("select a.mastertopid as priceid,a.price from marketconditions a,(select mastertopid,max(mconmanagedate) mconmanagedate from marketconditions where mconmanagedate<='".$workDate['start_date']." 23:59:59' AND mastertopid IN ('$id7') group by mastertopid) b where a.mastertopid= b.mastertopid and a.mconmanagedate= b.mconmanagedate");
        $lastYearPrice7 = $this->query("select a.mastertopid as priceid,a.price from marketconditions a,(select mastertopid,max(mconmanagedate) mconmanagedate from marketconditions where mconmanagedate<='".$workDate['lastYearDate']." 23:59:59' AND mastertopid IN ('$id7') group by mastertopid) b where a.mastertopid= b.mastertopid and a.mconmanagedate= b.mconmanagedate");
        return array("startPrice"=>array_merge($startPrice6,$startPrice7),"endPrice"=>array_merge($endPrice6,$endPrice7),"lastYearPrice"=>array_merge($lastYearPrice6,$lastYearPrice7));
    }

    /**
     * 获取钢厂市场库存
     * Created by zfy.
     * Date:2023/3/1 16:49
     * @param $workDate
     * @return array
     */
    public function getWeekYearAllStockPrice($workDate){
        $endPrice = $this->getRow("select market_lwg,market_xc,market_rzbj,market_lzbj,market_zhb,steel_lwg,steel_xc,steel_rzbj,steel_lzbj,steel_zhb,(market_lwg+market_xc+market_rzbj+market_lzbj+market_zhb+steel_lwg+steel_xc+steel_rzbj+steel_lzbj+steel_zhb) as allStock,(market_lwg+market_xc+market_rzbj+market_lzbj+market_zhb) as marketStock,(steel_lwg+steel_xc+steel_rzbj+steel_lzbj+steel_zhb) as steelStock from XG_stock_data where date<='".$workDate['end_date']."' ORDER BY date DESC limit 1");
        $startPrice = $this->getRow("select market_lwg,market_xc,market_rzbj,market_lzbj,market_zhb,steel_lwg,steel_xc,steel_rzbj,steel_lzbj,steel_zhb,(market_lwg+market_xc+market_rzbj+market_lzbj+market_zhb+steel_lwg+steel_xc+steel_rzbj+steel_lzbj+steel_zhb) as allStock,(market_lwg+market_xc+market_rzbj+market_lzbj+market_zhb) as marketStock,(steel_lwg+steel_xc+steel_rzbj+steel_lzbj+steel_zhb) as steelStock from XG_stock_data where date<='".$workDate['start_date']."' ORDER BY date DESC limit 1");
        $lastYearPrice = $this->getRow("select market_lwg,market_xc,market_rzbj,market_lzbj,market_zhb,steel_lwg,steel_xc,steel_rzbj,steel_lzbj,steel_zhb,(market_lwg+market_xc+market_rzbj+market_lzbj+market_zhb+steel_lwg+steel_xc+steel_rzbj+steel_lzbj+steel_zhb) as allStock,(market_lwg+market_xc+market_rzbj+market_lzbj+market_zhb) as marketStock,(steel_lwg+steel_xc+steel_rzbj+steel_lzbj+steel_zhb) as steelStock from XG_stock_data where date<='".$workDate['lastYearDate']."' ORDER BY date DESC limit 1");
        return array("lastPrice"=>$startPrice,"thisPrice"=>$endPrice,"lastYearPrice"=>$lastYearPrice);
    }

    /**
     * 获取距离上年同比库存日期最近的日期
     * Created by zfy.
     * Date:2023/3/3 11:03
     * @param $workDate
     * @return array
     */
    public function getNearDate($workDate){
        $minDate = $this->getOne("select date from gcsckucun_hz where date<='".$workDate['lastYearDate']."' order by date desc limit 1");
        $maxDate = $this->getOne("select date from gcsckucun_hz where date>='".$workDate['lastYearDate']."' order by date limit 1");
        $left = strtotime($workDate['lastYearDate']) - strtotime($minDate);
        $right = strtotime($maxDate) - strtotime($workDate['lastYearDate']);
        $retDate = $right < $left ? $maxDate : $minDate;
        return $retDate;
    }

    /**
     * 获取市场库存
     * Created by zfy.
     * Date:2023/3/1 16:33
     * @param $workDate
     * @param $typeStr
     * @return array
     */
    public function getWeekYearMarketStockPrice($workDate,$typeStr){
        $endPrice = $this->Aquery("select a.type,a.value from kucun_hz a,(select type,max(time) time from kucun_hz where time<='".$workDate['end_date']."' AND type IN ( $typeStr ) group by type) b where a.type= b.type and a.time= b.time ");
        $startPrice = $this->Aquery("select a.type,a.value from kucun_hz a,(select type,max(time) time from kucun_hz where time<='".$workDate['start_date']."' AND type IN ( $typeStr ) group by type) b where a.type= b.type and a.time= b.time ");
        $lastYearPrice = $this->Aquery("select a.type,a.value from kucun_hz a,(select type,max(time) time from kucun_hz where time<='".$workDate['lastYearDate']."' AND type IN ( $typeStr ) group by type) b where a.type= b.type and a.time= b.time ");
        $this->sumStock($startPrice,$endPrice,$lastYearPrice,array());
        return array("lastPrice"=>$startPrice,"thisPrice"=>$endPrice,"lastYearPrice"=>$lastYearPrice);
    }

    /**
     * 获取钢厂库存
     * Created by zfy.
     * Date:2023/3/1 16:52
     * @param $workDate
     * @param $typeStr
     * @return array
     */
    public function getWeekYearSteelStockPrice($workDate,$typeStr){
        $endPrice = $this->getOne("select Round(sum(a.Value),2) as value from KuCunHZ a,(select Type,max(Date) Date from KuCunHZ where Date<='".$workDate['end_date']."' AND Type IN ( $typeStr ) group by Type) b where a.Type= b.Type and a.Date= b.Date and Area=0");
        $startPrice = $this->getOne("select Round(sum(a.Value),2) as value from KuCunHZ a,(select Type,max(Date) Date from KuCunHZ where Date<='".$workDate['start_date']."' AND Type IN ( $typeStr ) group by Type) b where a.Type= b.Type and a.Date= b.Date and Area=0");
        $lastYearPrice = $this->getOne("select Round(sum(a.Value),2) as value from KuCunHZ a,(select Type,max(Date) Date from KuCunHZ where Date<='".$workDate['lastYearDate']."' AND Type IN ( $typeStr ) group by Type) b where a.Type= b.Type and a.Date= b.Date and Area=0");
        return array("lastPrice"=>$startPrice,"thisPrice"=>$endPrice,"lastYearPrice"=>$lastYearPrice);
    }

    /**
     * 均价信息获取
     * Created by zfy.
     * Date:2023/1/9 14:56
     * @param $xunDateList
     * @param $steelPriceIdList
     * @param $rawPriceIdList
     * @return array
     */
    public function getAvgMarketPrice($xunDateList,$steelPriceIdList,$rawPriceIdList){
        $allPriceIdList = array_merge($steelPriceIdList,$rawPriceIdList);
        $idList = $this->getPriceIdStr($allPriceIdList);
        $id6Str = $idList['id6'];
        $id7Str = $idList['id7'];
        $endPrice = $this->query("select avg(price) as avgPrice, topicture, mastertopid from marketconditions where mconmanagedate>='".$xunDateList['this_start_date']." 00:00:00' and mconmanagedate<='".$xunDateList['this_end_date']." 23:59:59' and (topicture in('$id6Str') or mastertopid in('$id7Str')) GROUP BY topicture");
        $startPrice = $this->query("select avg(price) as avgPrice, topicture, mastertopid from marketconditions where mconmanagedate>='".$xunDateList['last_start_date']." 00:00:00' and mconmanagedate<='".$xunDateList['last_end_date']." 23:59:59' and (topicture in('$id6Str') or mastertopid in('$id7Str')) GROUP BY topicture");
        return array("startPrice"=>$startPrice,"endPrice"=>$endPrice);
    }

    /**
     * 获取期货数据
     * Created by zfy.
     * Date:2023/1/9 16:11
     * @param $workDate
     * @param $typeStr
     * @return array
     */
    public function getFuturesByTypes($workDate,$typeStr){
        $startInfo = $this->Aquery("select dta_type,dta_6 from data_table where dta_maxValStatus='1' and dta_type in (".$typeStr.") and dta_ym = '".$workDate['this_start_date']."'");
        $endInfo = $this->Aquery("select dta_type,dta_6 from data_table where dta_maxValStatus='1' and dta_type in (".$typeStr.") and dta_ym = '".$workDate['this_end_date']."'");
        return array("startPrice"=>$startInfo,"endPrice"=>$endInfo);
    }

    /**
     * 获取纽原油数据
     * Created by zfy.
     * Date:2023/1/9 16:21
     * @param $workDate
     * @param $typeStr
     * @return array
     */
    public function getNYCrudeOil($workDate,$typeStr){
        $startInfo = $this->getOne("select mvalue from gfutures_shpi where bianma = '$typeStr' and datetime = '".$workDate['this_start_date']."'");
        $endInfo = $this->getOne("select mvalue from gfutures_shpi where bianma = '$typeStr' and datetime = '".$workDate['this_end_date']."'");
        return array("startPrice"=>$startInfo,"endPrice"=>$endInfo);
    }

    /**
     * 获取美元指数
     * Created by zfy.
     * Date:2023/1/9 16:22
     * @param $workDate
     * @return array
     */
    public function getUSDIndex($workDate){
        $startInfo = $this->getOne("select rd2 from dolrate where rdate = '".$workDate['this_start_date']."'");
        $endInfo = $this->getOne("select rd2 from dolrate where rdate = '".$workDate['this_end_date']."'");
        return array("startPrice"=>$startInfo,"endPrice"=>$endInfo);
    }

    /**
     * 人民币汇率
     * Created by zfy.
     * Date:2023/1/9 16:24
     * @param $workDate
     * @return array
     */
    public function getRMBRate($workDate){
        $startInfo = $this->getOne("select rd1 from rmbrate where rdate = '".$workDate['this_start_date']."'");
        $endInfo = $this->getOne("select rd1 from rmbrate where rdate = '".$workDate['this_end_date']."'");
        return array("startPrice"=>$startInfo,"endPrice"=>$endInfo);
    }

    /**
     * 获取钢厂调价
     * Created by zfy.
     * Date:2023/1/10 9:01
     * @param $xunDateList
     * @param $onlyIdList
     * @return array
     */
    public function getSteelPrice($xunDateList,$onlyIdList){
        $where = "";
        foreach ($onlyIdList as $index => $item) {
            if ($index != 0) $or = "or";
            $where .= " $or (onlyid='" . $item['onlyid'] . "' and gcid='" . $item['gcid'] . "')";
        }
        $priceInfo = $this->query("SELECT * FROM (SELECT id,the_price_tax,last_price_tax,the_price,last_price,onlyid, ftime, gcid,variety,material,specification FROM  `steelprice_info` WHERE ($where) AND the_price_tax != '' AND ftime <= '".$xunDateList['this_end_date']."' GROUP BY ftime, onlyid, gcid ORDER BY id DESC)  AS a GROUP BY onlyid, gcid");
        $changeRate = $this->Aquery("SELECT onlyid,sum(changerate_tax) FROM `steelprice_info` WHERE ($where) AND ftime >= '".$xunDateList['this_start_date']."' AND ftime <= '".$xunDateList['this_end_date']."' group by onlyid");
        $gcIdName = $this->Aquery("select st_code,name from sc_steelcom");
        $retList = array();
        foreach ($onlyIdList as $index => $item) {
            foreach ($priceInfo as $i => $priceItem) {
                if ($priceItem['onlyid'] == $item['onlyid']){
                    $retList[$item['onlyid']] = $priceItem;
                    $retList[$item['onlyid']]['gc_name'] = $gcIdName[$priceItem['gcid']];
                    $retList[$item['onlyid']]['date'] = date("m-d",strtotime($priceItem['ftime']));
                    if ($changeRate[$item['onlyid']]){
                        $retList[$item['onlyid']]['change_rate'] = $this->zhangdie($changeRate[$item['onlyid']]);
                    }else{
                        $retList[$item['onlyid']]['change_rate'] = 0;
                    }
                }
            }
        }
        return $retList;
    }

    /**
     * 获取市场库存的本旬 上旬 上月 上年数据
     * Created by zfy.
     * Date:2023/1/10 11:15
     * @param $dateList
     * @param $typeStr
     * @return array
     */
    public function getMarketStock($dateList,$typeStr){
        $thisXun = $this->Aquery("select a.type,a.value from kucun_hz a,(select type,max(time) time from kucun_hz where time<='".$dateList['thisXun']."' AND type IN ( $typeStr ) group by type) b where a.type= b.type and a.time= b.time ");
        $lastXun = $this->Aquery("select a.type,a.value from kucun_hz a,(select type,max(time) time from kucun_hz where time<='".$dateList['lastXun']."' AND type IN ( $typeStr ) group by type) b where a.type= b.type and a.time= b.time ");
        $lastMonth = $this->Aquery("select a.type,a.value from kucun_hz a,(select type,max(time) time from kucun_hz where time<='".$dateList['lastMonth']."' AND type IN ( $typeStr ) group by type) b where a.type= b.type and a.time= b.time ");
        $lastYear = $this->Aquery("select a.type,a.value from kucun_hz a,(select type,max(time) time from kucun_hz where time<='".$dateList['lastYear']."' AND type IN ( $typeStr ) group by type) b where a.type= b.type and a.time= b.time ");
        $this->sumStock($thisXun,$lastXun,$lastMonth,$lastYear);
        return array("thisXun"=>$thisXun,"lastXun"=>$lastXun,"lastMonth"=>$lastMonth,"lastYear"=>$lastYear);
    }

    /**
     * 获取钢厂库存的本旬 上旬 上月 上年数据
     * Created by zfy.
     * Date:2023/1/10 11:23
     * @param $dateList
     * @param $TypeStr
     * @return array
     */
    public function getSteelStock($dateList,$TypeStr){
        $thisXun = $this->Aquery("select a.Type,a.Value from KuCunHZ a,(select Type,max(Date) Date from KuCunHZ where Date<='".$dateList['thisXun']."' AND Type IN ( $TypeStr ) group by Type) b where a.Type= b.Type and a.Date= b.Date and Area=0");
        $lastXun = $this->Aquery("select a.Type,a.Value from KuCunHZ a,(select Type,max(Date) Date from KuCunHZ where Date<='".$dateList['lastXun']."' AND Type IN ( $TypeStr ) group by Type) b where a.Type= b.Type and a.Date= b.Date and Area=0");
        $lastMonth = $this->Aquery("select a.Type,a.Value from KuCunHZ a,(select Type,max(Date) Date from KuCunHZ where Date<='".$dateList['lastMonth']."' AND Type IN ( $TypeStr ) group by Type) b where a.Type= b.Type and a.Date= b.Date and Area=0");
        $lastYear = $this->Aquery("select a.Type,a.Value from KuCunHZ a,(select Type,max(Date) Date from KuCunHZ where Date<='".$dateList['lastYear']."' AND Type IN ( $TypeStr ) group by Type) b where a.Type= b.Type and a.Date= b.Date and Area=0");
        $this->sumStock($thisXun,$lastXun,$lastMonth,$lastYear);
        return array("thisXun"=>$thisXun,"lastXun"=>$lastXun,"lastMonth"=>$lastMonth,"lastYear"=>$lastYear);
    }

    /**
     * 获取市场+钢厂库存的本旬 上旬 上月 上年数据
     * Created by zfy.
     * Date:2023/1/10 11:23
     * @param $dateList
     * @param $typeStr
     * @return array
     */
    public function getSteelMarketStock($dateList,$typeStr){
        $thisXun = $this->Aquery("select a.type,a.value from gcsckucun_hz a,(select type,max(date) date from gcsckucun_hz where date<='".$dateList['thisXun']."' AND type IN ( $typeStr ) group by type) b where a.type= b.type and a.date= b.date ");
        $lastXun = $this->Aquery("select a.type,a.value from gcsckucun_hz a,(select type,max(date) date from gcsckucun_hz where date<='".$dateList['lastXun']."' AND type IN ( $typeStr ) group by type) b where a.type= b.type and a.date= b.date ");
        $lastMonth = $this->Aquery("select a.type,a.value from gcsckucun_hz a,(select type,max(date) date from gcsckucun_hz where date<='".$dateList['lastMonth']."' AND type IN ( $typeStr ) group by type) b where a.type= b.type and a.date= b.date ");
        $lastYear = $this->Aquery("select a.type,a.value from gcsckucun_hz a,(select type,max(date) date from gcsckucun_hz where date<='".$dateList['lastYear']."' AND type IN ( $typeStr ) group by type) b where a.type= b.type and a.date= b.date ");
        $this->sumStock($thisXun,$lastXun,$lastMonth,$lastYear);
        return array("thisXun"=>$thisXun,"lastXun"=>$lastXun,"lastMonth"=>$lastMonth,"lastYear"=>$lastYear);
    }

    /**
     * 获取高炉电炉开工率产能利用率
     * Created by zfy.
     * Date:2023/1/10 17:05
     * @param $dateList
     * @param $operatingSort
     * @return array
     */
    public function getOperatingInfo($dateList,$operatingSort){
        $typeStr = implode("','",$operatingSort);
        $thisXun = $this->Aquery("SELECT a.dta_type,a.dta_2 FROM data_table a, (SELECT dta_type, MAX( dta_ym) dta_ym FROM  data_table  WHERE dta_ym<= '".$dateList['thisXun']."' AND dta_type IN ('$typeStr') and dta_1='全国' GROUP BY dta_type)b WHERE a.dta_type= b.dta_type AND a.dta_ym= b.dta_ym and dta_1='全国'");
        $lastXun = $this->Aquery("SELECT a.dta_type,a.dta_2 FROM data_table a, (SELECT dta_type, MAX( dta_ym) dta_ym FROM  data_table  WHERE dta_ym<= '".$dateList['lastXun']."' AND dta_type IN ('$typeStr') and dta_1='全国' GROUP BY dta_type)b WHERE a.dta_type= b.dta_type AND a.dta_ym= b.dta_ym and dta_1='全国'");
        $lastMonth = $this->Aquery("SELECT a.dta_type,a.dta_2 FROM data_table a, (SELECT dta_type, MAX( dta_ym) dta_ym FROM  data_table  WHERE dta_ym<= '".$dateList['lastMonth']."' AND dta_type IN ('$typeStr') and dta_1='全国' GROUP BY dta_type)b WHERE a.dta_type= b.dta_type AND a.dta_ym= b.dta_ym and dta_1='全国'");
        $lastYear = $this->Aquery("SELECT a.dta_type,a.dta_2 FROM data_table a, (SELECT dta_type, MAX( dta_ym) dta_ym FROM  data_table  WHERE dta_ym<= '".$dateList['lastYear']."' AND dta_type IN ('$typeStr') and dta_1='全国' GROUP BY dta_type)b WHERE a.dta_type= b.dta_type AND a.dta_ym= b.dta_ym and dta_1='全国'");
        return array("thisXun"=>$thisXun,"lastXun"=>$lastXun,"lastMonth"=>$lastMonth,"lastYear"=>$lastYear);
    }

    /**
     * 获取日均成交量数据
     * Created by zfy.
     * Date:2023/1/10 17:05
     * @param $dateList
     * @param $turnoverSort
     * @return array
     */
    public function getTurnoverInfo($dateList,$turnoverSort){
        $typeStr = implode("','",$turnoverSort);
        $thisXun = $this->Aquery("select a.PinZhong,a.value from ChengJiaoLiangDiaoChaTotalInfo a,(select PinZhong,max(EndDate) EndDate from ChengJiaoLiangDiaoChaTotalInfo where EndDate <='".$dateList['thisXun']."' AND PinZhong IN ('$typeStr') AND TYPE =2
AND `cityid` = '-1' group by PinZhong) b where a.PinZhong= b.PinZhong and a.EndDate= b.EndDate AND TYPE =2 AND `cityid` = '-1'");
        $lastXun = $this->Aquery("select a.PinZhong,a.value from ChengJiaoLiangDiaoChaTotalInfo a,(select PinZhong,max(EndDate) EndDate from ChengJiaoLiangDiaoChaTotalInfo where EndDate <='".$dateList['lastXun']."' AND PinZhong IN ('$typeStr') AND TYPE =2
AND `cityid` = '-1' group by PinZhong) b where a.PinZhong= b.PinZhong and a.EndDate= b.EndDate AND TYPE =2 AND `cityid` = '-1'");
        $lastMonth = $this->Aquery("select a.PinZhong,a.value from ChengJiaoLiangDiaoChaTotalInfo a,(select PinZhong,max(EndDate) EndDate from ChengJiaoLiangDiaoChaTotalInfo where EndDate <='".$dateList['lastMonth']."' AND PinZhong IN ('$typeStr') AND TYPE =2
AND `cityid` = '-1' group by PinZhong) b where a.PinZhong= b.PinZhong and a.EndDate= b.EndDate AND TYPE =2 AND `cityid` = '-1'");
        $lastYear = $this->Aquery("select a.PinZhong,a.value from ChengJiaoLiangDiaoChaTotalInfo a,(select PinZhong,max(EndDate) EndDate from ChengJiaoLiangDiaoChaTotalInfo where EndDate <='".$dateList['lastYear']."' AND PinZhong IN ('$typeStr') AND TYPE =2
AND `cityid` = '-1' group by PinZhong) b where a.PinZhong= b.PinZhong and a.EndDate= b.EndDate AND TYPE =2 AND `cityid` = '-1'");
        return array("thisXun"=>$thisXun,"lastXun"=>$lastXun,"lastMonth"=>$lastMonth,"lastYear"=>$lastYear);
    }

    /**
     * 获取重点钢企旬日均产量
     * Created by zfy.
     * Date:2023/1/11 10:51
     * @param $date
     * @return array
     */
    public function getYieldInfo($date){
        $thisXun = $this->getRow("SELECT DATE,D1,D2,D3 FROM `L2Data` WHERE L2id=230 and DATE<='$date' order by DATE desc limit 1");
        $dateList = $this->getCommonXunMonthYearDate($thisXun['DATE']);
        $lastXun = $this->getRow("SELECT DATE,D1,D2,D3 FROM `L2Data` WHERE L2id=230 and DATE='".$dateList['lastXun']."'");
        $lastMonth = $this->getRow("SELECT DATE,D1,D2,D3 FROM `L2Data` WHERE L2id=230 and DATE='".$dateList['lastMonth']."'");
        $lastYear = $this->getRow("SELECT DATE,D1,D2,D3 FROM `L2Data` WHERE L2id=230 and DATE='".$dateList['lastYear']."'");
        return array("thisXun"=>$thisXun,"lastXun"=>$lastXun,"lastMonth"=>$lastMonth,"lastYear"=>$lastYear);
    }

    /**
     * 获取钢材成本
     * Created by zfy.
     * Date:2023/1/11 15:16
     * @param $dateList
     * @param $sort
     * @return array
     */
    public function getCostAndProfitInfo($dateList,$sort){
        $typeStr = implode(",",$sort);
        $thisXun = $this->Aquery("select a.type,a.chenben_tax from sg_HangYeChengBenIndex a,(select type,max(ndate) ndate from sg_HangYeChengBenIndex where ndate<='".$dateList['thisXun']."' AND type IN ($typeStr) AND mc_type=0 group by type) b where a.type= b.type and a.ndate= b.ndate and  mc_type=0");
        $lastXun = $this->Aquery("select a.type,a.chenben_tax from sg_HangYeChengBenIndex a,(select type,max(ndate) ndate from sg_HangYeChengBenIndex where ndate<='".$dateList['lastXun']."' AND type IN ($typeStr) AND mc_type=0 group by type) b where a.type= b.type and a.ndate= b.ndate and  mc_type=0");
        $lastMonth = $this->Aquery("select a.type,a.chenben_tax from sg_HangYeChengBenIndex a,(select type,max(ndate) ndate from sg_HangYeChengBenIndex where ndate<='".$dateList['lastMonth']."' AND type IN ($typeStr) AND mc_type=0 group by type) b where a.type= b.type and a.ndate= b.ndate and  mc_type=0");
        $lastYear = $this->Aquery("select a.type,a.chenben_tax from sg_HangYeChengBenIndex a,(select type,max(ndate) ndate from sg_HangYeChengBenIndex where ndate<='".$dateList['lastYear']."' AND type IN ($typeStr) AND mc_type=0 group by type) b where a.type= b.type and a.ndate= b.ndate and  mc_type=0");
        return array("thisXun"=>$thisXun,"lastXun"=>$lastXun,"lastMonth"=>$lastMonth,"lastYear"=>$lastYear);
    }

    /**
     * 获取钢材成本毛利计算所需的市场价格信息
     * Created by zfy.
     * Date:2023/1/11 16:19
     * @param $dateList
     * @param $sort
     * @return array
     */
    public function getMarketPriceInfo($dateList,$sort){
        $idList = $this->getPriceIdStr($sort);
        $id6 = $idList['id6'];
        $id7 = $idList['id7'];
        $thisXun6 = $this->Aquery("select a.topicture as priceid,a.price from marketconditions a,(select topicture,max(mconmanagedate) mconmanagedate from marketconditions where mconmanagedate<='".$dateList['thisXun']." 23:59:59' AND topicture IN ('$id6') group by topicture) b where a.topicture= b.topicture and a.mconmanagedate= b.mconmanagedate");
        $lastXun6 = $this->Aquery("select a.topicture as priceid,a.price from marketconditions a,(select topicture,max(mconmanagedate) mconmanagedate from marketconditions where mconmanagedate<='".$dateList['lastXun']." 23:59:59' AND topicture IN ('$id6') group by topicture) b where a.topicture= b.topicture and a.mconmanagedate= b.mconmanagedate");
        $lastMonth6 = $this->Aquery("select a.topicture as priceid,a.price from marketconditions a,(select topicture,max(mconmanagedate) mconmanagedate from marketconditions where mconmanagedate<='".$dateList['lastMonth']." 23:59:59' AND topicture IN ('$id6') group by topicture) b where a.topicture= b.topicture and a.mconmanagedate= b.mconmanagedate");
        $lastYear6 = $this->Aquery("select a.topicture as priceid,a.price from marketconditions a,(select topicture,max(mconmanagedate) mconmanagedate from marketconditions where mconmanagedate<='".$dateList['lastYear']." 23:59:59' AND topicture IN ('$id6') group by topicture) b where a.topicture= b.topicture and a.mconmanagedate= b.mconmanagedate");

        $thisXun7 = $this->Aquery("select a.mastertopid as priceid,a.price from marketconditions a,(select mastertopid,max(mconmanagedate) mconmanagedate from marketconditions where mconmanagedate<='".$dateList['thisXun']." 23:59:59' AND mastertopid IN ('$id7') group by mastertopid) b where a.mastertopid= b.mastertopid and a.mconmanagedate= b.mconmanagedate");
        $lastXun7 = $this->Aquery("select a.mastertopid as priceid,a.price from marketconditions a,(select mastertopid,max(mconmanagedate) mconmanagedate from marketconditions where mconmanagedate<='".$dateList['lastXun']." 23:59:59' AND mastertopid IN ('$id7') group by mastertopid) b where a.mastertopid= b.mastertopid and a.mconmanagedate= b.mconmanagedate");
        $lastMonth7 = $this->Aquery("select a.mastertopid as priceid,a.price from marketconditions a,(select mastertopid,max(mconmanagedate) mconmanagedate from marketconditions where mconmanagedate<='".$dateList['lastMonth']." 23:59:59' AND mastertopid IN ('$id7') group by mastertopid) b where a.mastertopid= b.mastertopid and a.mconmanagedate= b.mconmanagedate");
        $lastYear7 = $this->Aquery("select a.mastertopid as priceid,a.price from marketconditions a,(select mastertopid,max(mconmanagedate) mconmanagedate from marketconditions where mconmanagedate<='".$dateList['lastYear']." 23:59:59' AND mastertopid IN ('$id7') group by mastertopid) b where a.mastertopid= b.mastertopid and a.mconmanagedate= b.mconmanagedate");

        return array("thisXun"=>($thisXun6+$thisXun7),"lastXun"=>($lastXun6+$lastXun7),"lastMonth"=>($lastMonth6+$lastMonth7),"lastYear"=>($lastYear6+$lastYear7));
    }

    /**
     * 获取旬预测市场要闻
     * Created by zfy.
     * Date:2023/1/11 17:05
     * @param $xunDateList
     * @return mixed
     */
    public function getMarketNews($xunDateList){
        return $this->getOne("select content from XG_marketNews where CDate<='".$xunDateList['this_end_date']."' and type=2 and news_type=0 order by CDate desc limit 1");
    }

    /**
     * 获取最新库存的本旬日期
     * Created by zfy.
     * Date:2023/1/10 14:31
     * @return mixed
     */
    public function getNewStockDate($dateList){
        return $this->getOne("select date from gcsckucun_hz where date<='".$dateList['thisXun']."' order by id desc limit 1");
    }

    /**
     * 获取最新开工率的本旬日期
     * Created by zfy.
     * Date:2023/1/10 17:13
     * @param $dateList
     * @return mixed
     */
    public function getNewOperatingDate($dateList){
        return $this->getOne("select dta_ym from data_table where dta_type='GC_KGL' and dta_ym<='".$dateList['thisXun']."' order by id desc limit 1");
    }

    public function getForecastTotal($type){
        return $this->query("select count(1) from XG_accuracy tb1,XG_day_meadow_month_predict tb2 where tb1.baseid=tb2.id and  tb1.yc_type='$type' group by tb2.yc_edate");
    }

    public function getForecastListInfo($type, $start, $limit){
        return $this->query("select tb2.yc_sdate,tb2.yc_edate,tb2.yc_varietyname,tb1.js_date from XG_accuracy tb1,XG_day_meadow_month_predict tb2 where tb1.baseid=tb2.id and  tb1.yc_type='$type' group by tb2.yc_edate order by tb1.js_date desc limit $start,$limit");
    }

    /**
     * 计算五大品种库存
     * Created by zfy.
     * Date:2023/1/10 11:46
     * @param $thisXun
     * @param $lastXun
     * @param $lastMonth
     * @param $lastYear
     */
    protected function sumStock(&$thisXun,&$lastXun,&$lastMonth,&$lastYear){
        $thisXun = $this->sumForeach($thisXun);
        $lastXun = $this->sumForeach($lastXun);
        $lastMonth = $this->sumForeach($lastMonth);
        if ($lastYear)$lastYear = $this->sumForeach($lastYear);
    }
    protected function sumForeach($data){
        $data[6] = 0;
        foreach ($data as $datum) {
            $data[6] +=$datum;
        }
        return $data;
    }


    /**
     * 将价格id数组分开，分为6 和 7位价格id字符串
     * Created by zfy.
     * Date:2023/1/9 14:44
     * @param $allPriceIdList
     * @return array
     */
    protected function getPriceIdStr($allPriceIdList){
        $id6 = array();
        $id7 = array();
        foreach ($allPriceIdList as $idItem) {
            if (strlen($idItem) == 6) {
                $id6[] = $idItem;
            } elseif (strlen($idItem) == 7) {
                $id7[] = $idItem;
            }
        }
        $id6Str = implode("','",$id6);
        $id7Str = implode("','",$id7);
        return array('id6'=>$id6Str,'id7'=>$id7Str);
    }

    function zhangdie($int,$symbolType = 0){
        if ($symbolType == 0){
            $up = "+";
            $down = "-";
        }else{
            $up = "↑";
            $down = "↓";
        }
        if($int<0){
            $intstr = "<font color=green><strong>".$down.abs($int)."</strong></font>";
        }elseif($int>0){
            $intstr = "<font color=red><strong>".$up.abs($int)."</strong></font>";
        }elseif($int==""){
            $intstr = "<strong>—</strong>";
        }else{
            $intstr = "<font ><strong>".$int."</strong></font>";
        }
        return $intstr;
    }

    /**
     * 获取 旬 月 年同比日期 旬结束日为 1 11 21
     * Created by zfy.
     * Date:2023/1/11 9:48
     * @param $date
     * @return array
     */
    protected function getCommonXunMonthYearDate($date){
        $retList = array();
        $day = date("d",strtotime($date));
        if ($day == 21){
            $retList['lastXun'] = date("Y-m-11",strtotime($date));
        }elseif ($day == 11){
            $retList['lastXun'] = date("Y-m-01",strtotime($date));
        }else{
            $retList['lastXun'] = date("Y-m-21",strtotime($date."-1 month"));
        }
        $retList['lastMonth'] = date("Y-m-d",strtotime($date."-1 month"));
        $retList['lastYear'] = date("Y-m-d",strtotime($date."-1 year"));
        return $retList;
    }

    //根据id取XG_accuracy表数据
    function get_xg_accuracyByIdType($id){
        $sql = "select * from XG_accuracy where id = '".$id."' ";
        return $this->getRow($sql);
    }
    //获取预测数据
    function get_yc_data($yc_type,$sdete,$edete){
        $sql = "select * from XG_day_meadow_month_predict where yc_type = '".$yc_type."' and yc_sdate = '".$sdete."' and yc_edate = '".$edete."'";
        //echo $sql;
        $data = $this->query($sql); 
        $ret_data = array();
        foreach ( $data as $pkey => $pvalue ){
            if($pvalue["yc_direction"] == "1"){
                $ret_data[$pvalue["priceid"]]["ycfx"] = "上涨";
            }else if($pvalue["yc_direction"] == "2"){
                $ret_data[$pvalue["priceid"]]["ycfx"] = "持平";
            }else if($pvalue["yc_direction"] == "3"){
                $ret_data[$pvalue["priceid"]]["ycfx"] = "下跌";
            }
            if($pvalue["yc_fudu1"] == "0" && $pvalue["yc_fudu2"] == "0"){
                $ret_data[$pvalue["priceid"]]["ycfd"] = "-";
            }else{
                $ret_data[$pvalue["priceid"]]["ycfd"] = $pvalue["yc_fudu1"] ."-". $pvalue["yc_fudu2"];
            }
        }
        return $ret_data;
    }

    //获取市场要闻
    function get_xg_marketNews($type,$sdate,$edate,$news_type){
        $sql = "select content from XG_marketNews where type = '".$type."' and CDate >= '".$sdate."' and CDate <= '".$edate."' and news_type = '".$news_type."'";
        // echo $sql;
        return $this->getOne($sql);
    }

    //获取行业成本
    function getHangYeChengBen($type,$ndate,$dao){
        // echo "<pre/>";print_r($dao); 
        $ndate2 = date('Y-m-d', strtotime("$ndate -1 year"));
        // $sql = 'select a.ndate,a.chenben_tax from (select * from sg_HangYeChengBenIndex where type = "'.$type.'" and 	ndate >= "'.$ndate.'" and mc_type = 0 order by ndate asc) as a GROUP BY DATE_FORMAT( a.ndate, "%Y-%m" ) ORDER BY a.ndate asc';
        $sql = 'select * from sg_HangYeChengBenIndex where type = \''.$type.'\' and 	ndate >= \''.$ndate2.'\' and ndate <= \''.$ndate.'\' and mc_type = 0 order by ndate desc';
        // echo $sql;
        $datalist = $dao->query($sql);
        $ret_data = array();
        foreach($datalist as $v){
            $riqi = date('Y年n月',strtotime($v["ndate"]));
            $v["ndate2"] = $riqi;
            if(empty($ret_data[$riqi])){
                $v["count"] = 1;
                $v["cb_price"] =  $v["chenben_tax"];
                $ret_data[$riqi] = $v;
            }else{
                $ret_data[$riqi]["cb_price"] = $ret_data[$riqi]["cb_price"] + $v["chenben_tax"];
                $ret_data[$riqi]["count"] = $ret_data[$riqi]["count"] + 1;
            }
        }
        
        foreach($ret_data as $k => &$item){
            $item["avg_price"] = round($item["cb_price"] /  $item["count"],0);
        }
        // echo "<pre/>";print_r($ret_data); 
		return $ret_data;

    }
    //根据起止时间及预测类型 获取XG_accuracy表的预测数据，计算正确率
    function get_xg_accurac($yc_type,$sdate,$edate){
        $str = "";
        if($yc_type == 1){
            $sql = "select * from XG_accuracy where js_date >= '".$sdate."' and js_date <= '".$edate."' and yc_type = '".$yc_type."'";
            $str = "最近一个月（自然月）方向正确率为：";
        }else if($yc_type == 3){
            $sdate2 = date('Y-m-01', strtotime("$sdate -2 month"));
            $sql = "select * from XG_accuracy where js_date >= '".$sdate2."' and js_date <= '".$edate."' and yc_type = '".$yc_type."'";
            $str = "最近三个月方向正确率为：";
        }
        
        $list = $this->query($sql);
        $fx_accuracy_num = 0;
        $fx_fd_accuracy_num = 0;
        $ret_Str = "";
        foreach($list as $item){
            if($item["fx_accuracy"] == 1){
                $fx_accuracy_num++;
            }
            if($item["fx_fd_accuracy"] == 1){
                $fx_fd_accuracy_num++;
            }
        }
        $z_num = count($list);
        $ret_Str = $str.round($fx_accuracy_num / $z_num * 100,2)."%；方向幅度正确率为 ".round($fx_fd_accuracy_num / $z_num * 100,2)."%。";
        return $ret_Str;
    }

    /**
     * 新钢区域价格对比数据录入列表
     * Created by zfy.
     * Date:2023/7/12 11:44
     * @return mixed
     */
    public function get_regional_price_input_list($params){
        $per = $params['limit']=='' ? 12 : $params['limit'];
        $page = $params['page'] == '' ? 1 : $params['page'];
        $start = ( $page - 1 ) * $per;
        $baseInfo = $this->query("select id,sdate,createtime,createusername from data_table_base where sdate>='".$params['sdate']."' and sdate<='".$params['edate']."' and dta_type='XG-REGIONAL_PRICE' order by sdate desc limit $start,$per");
        $retList = array();
        foreach ($baseInfo as $index => &$item) {
            $detail = $this->Aquery("select dta2,dta3 from data_table where baseid='".$item['id']."'");
            $item['nanchang'] = round(floatval($detail['南昌']));
            $item['xinyu'] = round(floatval($detail['新余']));
        }
        return $baseInfo;
    }

    public function get_regional_price_input_total($params){
        return $this->getOne("select count(1) from data_table_base where sdate>='".$params['sdate']."' and sdate<='".$params['edate']."' and dta_type='XG-REGIONAL_PRICE'");
    }

    /**
     * 新钢区域价格对比数据录入
     * Created by zfy.
     * Date:2023/7/12 15:12
     * @param $info
     * @param $userInfo
     */
    public function insert_regional_price_input($info, $userInfo)
    {
        $this->execute("insert into data_table_base set sdate='" . $info['date'] . "',dta_type='XG-REGIONAL_PRICE',type='5',createusername='" . $userInfo['TrueName'] . "',createtime=NOW(),createuser='" . $userInfo['Uid'] . "'");
        $baseId = $this->insert_id();
        foreach ($info['dta_3'] as $index => $item) {
            $dta1 = $info['dta_1'][$index];
            $dta2 = $GLOBALS['MARKET_NAME'][$info['dta_2'][$index]];
            $this->execute("insert into data_table set dta_type='XG-REGIONAL_PRICE',baseid='$baseId',dta1='" . $dta1 . "',dta2='" . $dta2 . "',dta3='" . $item . "',dta_ym='" . $info['date'] . "',createtime=NOW(),createuser='" . $userInfo['Uid'] . "'");
        }
    }

    /**
     * 获取录入的条数
     * Created by zfy.
     * Date:2023/7/26 14:05
     * @param $info
     * @return mixed
     */
    public function get_regional_price_input_count($info){
        return $this->getOne("select count(1) from data_table_base where sdate='" . $info['date'] . "' and dta_type='XG-REGIONAL_PRICE' and type='5'");
    }

    /**
     * 新钢区域价格对比数据修改
     * Created by zfy.
     * Date:2023/7/12 16:05
     * @param $info
     * @param $userInfo
     */
    public function update_regional_price_input($info,$userInfo){
        foreach ($info['dta_3'] as $index => $item) {
            $updateId = $info['update_id'][$index];
            $dta1 = $info['dta_1'][$index];
            $dta2 = $GLOBALS['MARKET_NAME'][$info['dta_2'][$index]];
            $this->execute("update data_table set dta_type='XG-REGIONAL_PRICE',baseid='".$info['id']."',dta1='" . $dta1 . "',dta2='" . $dta2 . "',dta3='" . $item . "',dta_ym='" . $info['date'] . "',createtime=NOW(),createuser='" . $userInfo['Uid'] . "' where id='$updateId'");
        }
    }

    /**
     * 根据id获取一条价格数据
     * Created by zfy.
     * Date:2023/7/12 16:08
     * @param $id
     * @return mixed
     */
    public function get_regional_price_input_row($id){
        $list = $this->query("select id,dta1,dta2,dta3,dta_ym as date from data_table where baseid='$id'");
        $marketName = array_flip($GLOBALS['MARKET_NAME']);
        $retList = array();
        foreach ($list as $index => $item) {
            $dta2 = $item['dta2'];
            $retList[$item['dta1']][$marketName[$dta2]]['field_price'] = $item['dta3'];
            $retList[$item['dta1']][$marketName[$dta2]]['field_id'] = $item['id'];
            $retList['date'] = $item['date'];
        }
        return $retList;
    }

    /**
     * 删除 新钢区域价格对比数据
     * Created by zfy.
     * Date:2023/7/12 16:00
     * @param $id
     */
    public function delete_regional_price_input($id){
        $this->execute("delete from data_table_base where id='$id'");
        $this->execute("delete from data_table where baseid='$id'");
    }

    /**
     * 新钢区域价格杂项设置录入列表
     * Created by zfy.
     * Date:2023/7/12 11:44
     * @return mixed
     */
    public function get_regional_price_setting_list($params){
        $where = "";
        if ($params['price_type']!=''){
            $where .= " and price_type='".$params['price_type']."'";
        }
        if ($params['market_name']!=''){
            $where .= " and market_name='".$params['market_name']."'";
        }
        return $this->query("select * from XG_regional_price_setting where date>='".$params['sdate']."' and date<='".$params['edate']."' $where order by id desc");
    }

    /**
     * 新钢区域价格杂项设置录入
     * Created by zfy.
     * Date:2023/7/12 15:12
     * @param $info
     * @param $userInfo
     * @return mixed
     */
    public function insert_regional_price_setting($info,$userInfo){
        return $this->execute("insert into XG_regional_price_setting set price_type='".$info['price_type']."',market_name='".$info['market_name']."',freight='".$info['freight']."',discount='".$info['discount']."',pound_difference='".$info['pound_difference']."',date='".$info['date']."',create_time=NOW(),update_time=NOW(),create_user_name='".$userInfo['TrueName']."'");
    }

    /**
     * 根据id获取一条杂项设置数据
     * Created by zfy.
     * Date:2023/7/12 16:08
     * @param $id
     * @return mixed
     */
    public function get_regional_price_setting_row($id){
//        $this->execute("set names 'utf8';");
        return $this->getRow("select * from XG_regional_price_setting where id='$id'");
    }

    /**
     * 新钢区域价格杂项设置修改
     * Created by zfy.
     * Date:2023/7/12 16:05
     * @param $info
     * @return mixed
     */
    public function update_regional_price_setting($info,$userInfo){
        return $this->execute("update XG_regional_price_setting set price_type='".$info['price_type']."',market_name='".$info['market_name']."',freight='".$info['freight']."',discount='".$info['discount']."',pound_difference='".$info['pound_difference']."',date='".$info['date']."',update_time=NOW(),create_user_name='".$userInfo['TrueName']."' where id='".$info['id']."'");
    }

    /**
     * 删除 新钢区域价格杂项设置
     * Created by zfy.
     * Date:2023/7/12 16:00
     * @param $id
     */
    public function delete_regional_price_setting($id){
        $this->execute("delete from XG_regional_price_setting where id='$id'");
    }

    /**
     * 获取新钢自填价格均价
     * Created by zfy.
     * Date:2023/7/13 10:36
     * @param $price_type
     * @param $market_name
     * @param $start_date
     * @param $end_date
     * @return mixed
     */
    public function get_regional_avg_price($price_type,$market_name,$start_date,$end_date){
        $avgPrice = $this->getOne("select AVG(dta3) from data_table where dta1='$price_type' and dta2='$market_name' and dta_type='XG-REGIONAL_PRICE' and dta_ym>='$start_date' and dta_ym<='$end_date'");
        return sprintf("%d",$avgPrice);
    }

    /**
     * 获取最新的价格杂项设置
     * Created by zfy.
     * Date:2023/7/13 10:57
     * @param $price_type
     * @param $date
     * @return mixed
     */
    public function get_new_regional_price_setting($price_type,$date){
        $list = $this->query("select a.market_name,a.freight,a.discount,a.pound_difference,a.price_type from XG_regional_price_setting a,(select price_type,market_name,freight,discount,pound_difference,max(date) date from XG_regional_price_setting where date<='".$date." 23:59:59' AND price_type='$price_type' group by market_name) b where a.price_type= b.price_type and a.date= b.date and a.price_type='$price_type'");
        $retList = array();
        foreach ($list as $index => $item) {
            $retList[$item['market_name']] = $item;
        }
        return $retList;
    }

    /**
     * 查询excel表格
     * Created by zfy.
     * Date:2023/7/14 11:44
     * @param $type
     * @param $dta_type
     * @param $where
     * @return mixed
     */
    public function get_excel_data_total($type,$dta_type,$where){
        return $this->getOne("select count(1) from data_table_base where type='$type' and dta_type='$dta_type' and isdel=0 $where");
    }

    public function get_excel_data_list($type,$dta_type,$where,$start,$limit){
        return $this->query("SELECT * FROM data_table_base where type='$type' and dta_type='$dta_type' and isdel=0 " . $where . " ORDER BY id DESC limit $start,$limit");
    }

    public function delete_excel_info_by_id($id){
        $this->execute("delete from data_table_base where id='$id'");
        $this->execute("delete from data_table where baseid='$id'");
    }

    /**
     * 查询excel上传服务器的信息列表
     * Created by zfy.
     * Date:2023/7/20 10:01
     * @param $type
     * @param $dta_type
     * @param $where
     * @return mixed
     */
    public function get_excel_upload_data_total($type,$dta_type,$where){
        return $this->getOne("select count(1) from data_table where  dta_type='$dta_type' $where");
    }

    public function get_excel_upload_data_list($type,$dta_type,$where,$start,$limit){
        return $this->query("SELECT * FROM data_table where  dta_type='$dta_type' " . $where . " ORDER BY id DESC limit $start,$limit");
    }

    public function getZgxListTotal($where){
        $sql = "SELECT COUNT(id) as c FROM data_table_base  where type=4 and isdel=0 " . $where;
        //echo $sql;
        $this->execute("set names 'utf8';");
        $data = $this->getOne($sql);
        return $data;
    }

    public function getZgxListInfo($where,$start,$limit){
        $sql = "SELECT * FROM data_table_base where type=4 and isdel=0 " . $where . " ORDER BY id DESC limit $start,$limit";
        //echo $sql;
        $this->execute("set names 'utf8';");
        $data = $this->query($sql);
        return $data;
    }

    public function getZgxDetail($baseid){
        $sql = "SELECT * FROM data_table where baseid='".$baseid."' ORDER BY id ASC ";
        $this->execute("set names 'utf8';");
        $data = $this->query($sql);
        return $data;
    }

    public function getYrlListTotal($where){
        $sql = "SELECT COUNT(id) as c FROM data_table_base  where type=8 and isdel=0 " . $where;
        //echo $sql;
        $this->execute("set names 'utf8';");
        $data = $this->getOne($sql);
        return $data;
    }

    public function getYrlListInfo($where,$start,$limit){
        $sql = "SELECT * FROM data_table_base where type=8 and isdel=0 " . $where . " ORDER BY id DESC limit $start,$limit";
        //echo $sql;
        $this->execute("set names 'utf8';");
        $data = $this->query($sql);
        return $data;
    }

    /**
     * 资金回笼录入列表
     * Created by zfy.
     * Date:2023/7/12 11:44
     * @return mixed
     */
    public function get_capital_return_input_list($params){
        $this->execute("set names 'utf8';");
        $list = $this->query("select dta_type,dta_ym,dta1,dta2,dta3 from data_table where baseid='".$params['id']."'");
        return $list;
    }

    public function get_capital_return_input_base($params){
        return $this->getRow("select * from data_table_base where id='".$params['id']."'");
    }


    public function get_this_month_capital_return_input($params){
        $monthStart = date("Y-m-01",strtotime($params['date']));
        
        $list = $this->query("select * from data_table where dta_type='".$params['dta_type']."' and dta_ym>='$monthStart' and dta_ym<='".$params['date']."'");
        $sumList = array();
        foreach ($list as $item) {
            $sumList[$item['dta1']]['sum'] += (float)$item['dta3'];
            $sumList[$item['dta1']]['count'] = 1;
            $sumList[$item['dta1']]['dta2'] = $item['dta2'];
            $sumList[$item['dta1']]['dta4'] = $item['dta4'];
        }
        return $sumList;
    }
    public function get_this_year_capital_return_input($params){
        $monthStart = date("Y-01-01",strtotime($params['date']));
        
        $list = $this->query("select * from data_table where dta_type='".$params['dta_type']."' and dta_ym>='$monthStart' and dta_ym<='".$params['date']."' order by dta_ym");
        $sumList = array();
        foreach ($list as $item) {
            $item['dta1'] = $item['dta1'];
//            $sumList[$item['dta1']]['sum'] += $item['dta3'];
            $sumList[$item['dta1']]['sum'][date("Y-m",strtotime($item['dta_ym']))] = $item['dta4'];
            $sumList[$item['dta1']]['yearSum'] = 0;
            foreach ($sumList[$item['dta1']]['sum'] as $month => $sum) {
                $sumList[$item['dta1']]['yearSum'] += (int)$sum;
            }
            $sumList[$item['dta1']]['count'] = 1;
        }
        return $sumList;
    }

    /**
     * 获取最近一天的数据
     * Created by zfy.
     * Date:2023/7/25 10:57
     * @param $params
     * @return mixed
     */
    public function get_last_capital_return_data($params){
        $monthStart = date("Y-m-01");
        $this->execute("set names 'utf8';");
        $list = $this->query("select a.dta_ym,a.dta1,a.dta2,a.dta3,a.dta4 from data_table a,(select dta1,max(dta_ym) dta_ym from data_table where dta_ym>='".$monthStart."' and dta_ym<='".$params['date']."' and dta_type='".$params['dta_type']."' group by dta1) b where a.dta1= b.dta1 and a.dta_ym= b.dta_ym");
        $retList = array();
        foreach ($list as $index => $item) {
            $retList[$item['dta1']] = $item;
        }
        return $retList;
    }

    public function get_capital_return_data_by_id($params){
        $this->execute("set names 'utf8';");
        $list = $this->query("select dta_ym,dta1,dta2,dta3,dta4 from data_table where baseid='".$params['id']."'");
        $retList = array();
        foreach ($list as $index => $item) {
            $retList[$item['dta1']] = $item;
        }
        return $retList;
    }


    /**
     * 资金回笼数据录入
     * Created by zfy.
     * Date:2023/7/12 15:12
     * @param $info
     * @param $userInfo
     */
    public function insert_capital_return_input($info,$userInfo){
        $this->execute("delete from data_table where dta_ym='".$info['date']."' and dta_type='".$info['dta_type']."'");
        $this->execute("delete from data_table_base where sdate='".$info['date']."' and dta_type='".$info['dta_type']."'");

        $this->execute("insert into data_table_base set sdate='" . $info['date'] . "',dta_type='".$info['dta_type']."',type='5',createusername='" . $userInfo['TrueName'] . "',createtime=NOW(),createuser='" . $userInfo['Uid'] . "'");
        $baseId = $this->insert_id();
        foreach ($GLOBALS['CAPITAL_RETURN_FIELDS'] as $code => $name) {
            $monthSumField = $code."_month_sum";
            $monthField = $code."_month";
            $dayField = $code."_day";
            $this->execute("insert into data_table set baseid='$baseId', dta_type='".$info['dta_type']."',dta1='".$name."',dta2='".$info[$monthField]."',dta3='".$info[$dayField]."',dta4='".$info[$monthSumField]."',dta_ym='".$info['date']."',createtime=NOW(),createuser='" . $userInfo['Uid'] . "'");
        }
    }

    public function get_capital_return_list($params){
        $per = $params['limit']=='' ? 12 : $params['limit'];
        $page = $params['page'] == '' ? 1 : $params['page'];
        $start = ( $page - 1 ) * $per;
        return $this->query("select * from data_table_base where sdate>='".$params['sdate']."' and sdate<='".$params['edate']."' and dta_type='".$params['dta_type']."' order by sdate desc limit $start,$per");
    }

    public function get_capital_return_total($params){
        return $this->getOne("select count(1) from data_table_base where sdate>='".$params['sdate']."' and sdate<='".$params['edate']."' and dta_type='".$params['dta_type']."'");
    }

    /**
     * 删除 资金回笼数据
     * Created by zfy.
     * Date:2023/7/12 16:00
     * @param $id
     */
    public function delete_capital_return_input($id){
//        $info = $this->getRow("select * from data_table where id='$id'");
//        unlink($info['dta2']);
        $this->execute("delete from data_table where baseid='$id'");
        $this->execute("delete from data_table_base where id='$id'");
    }

    public function delete_excel_upload_table($id){
        $this->execute("delete from data_table where id='$id'");
    }

    /**
     * 根据id查询data_table一条记录
     * Created by zfy.
     * Date:2023/7/20 10:58
     * @param $id
     * @return mixed
     */
    public function get_data_table_row($id){
        return $this->getRow("select * from data_table where id='$id'");
    }

}