<?php

class memberlog<PERSON><PERSON> extends Dao
{
    public function __construct($writer)
    {
        parent::__construct($writer);
    }

	public function get_privilege($GUID, $SignCS, $mc_type)
    {
		return $this->getOne("select privilege from app_license_privilege,app_session_temp where GUID='$GUID' and SignCS='$SignCS' and app_license_privilege.mc_type=$mc_type and app_session_temp.Uid=app_license_privilege.uid order by app_license_privilege.id desc");
	}

    public function get_loglist_count($where)
    {
        return $this->getOne("select count(GUID) from app_session_temp where $where");
    }

    public function get_loglist_list($where, $limit)
    {
        return $this->query("select TrueName,LoginDate,LoginIp,SystemType,Uid from app_session_temp where $where group by TrueName order by LoginDate desc $limit");
    }

    public function get_uid($username,$mc_type)
    {
        return $this->getOnes("select DISTINCT Uid from app_session_temp where TrueName like '%$username%' and mc_type=$mc_type");
    }

    public function get_mid($GUID, $SignCS,$mc_type)
    {
        return $this->getOne("select mid from app_session_temp where GUID='$GUID' and SignCS='$SignCS' and mc_type=$mc_type order by LoginDate desc limit 1");
    }

    public function get_log_count($where)
    {
        return $this->getOne("select count(ID) from app_logs where $where");
    }

    public function get_log_list($where, $limit)
    {
        return $this->query("select DISTINCT ID,TrueName,MessageTitle,ActionIp,ActionDate,app_logs.SignCS as SignCS from app_logs,app_session_temp where $where and app_logs.Uid=app_session_temp.Uid order by ActionDate desc $limit");
    }

    public function get_udata($username,$mc_type)
    {
        return $this->getRow("select DISTINCT SignCS,GUID from app_session_temp where TrueName='$username' and mc_type=$mc_type");
    }

    public function get_uid_udata($uid,$mc_type,$date)
    {
        return $this->getRow("select SignCS,GUID from app_session_temp where Uid='$uid' and mc_type=$mc_type and LoginDate>='$date 00:00:00' and LoginDate<='$date 23:59:59' order by LoginDate desc limit 1");
    }

    public function get_logdetail_count($where)
    {
        return $this->getOne("select count(ID) from app_member_logs where $where");
    }
	
    public function get_logdetail_list($where,$limit)
    {
        return $this->query("select TrueName,DTID1Name,OpDesc,OpDate,whereFrom,ActionIp from app_member_logs,app_session_temp where $where and app_session_temp.GUID=app_member_logs.GUID and app_session_temp.SignCS=app_member_logs.SignCS order by OpDate desc $limit");
    }
	
    //新增会员日志记录
    public function WriteMemberLog($GUID, $SignCS,$Mid,$ComName,$OpDesc,$whereFrom,$mc_type, $ActionName, $ActionIp){
        $this->execute( "INSERT INTO app_member_logs SET
  			GUID='$GUID',
  			SignCS='$SignCS',	
  			Mid='$Mid',
  			ComName='$ComName',
  			OpDesc='$OpDesc',
  			OpDate=NOW(),
  			whereFrom='$whereFrom',
  			mc_type='$mc_type',
  			ActionName='$ActionName',
  			ActionDate=NOW(),
  			ActionIp='$ActionIp' ");
    }
}

?>