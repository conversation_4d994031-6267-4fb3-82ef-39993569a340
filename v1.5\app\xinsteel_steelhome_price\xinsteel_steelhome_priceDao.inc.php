<?php

class xinsteel_steelhome_priceDao extends Dao
{
    public function __construct($writer)
    {
        parent::__construct($writer);
    }

    public function getDc_smsid_manage()
    {
        return $this->getOnes("select MS_ID from dc_smsid_manage where isdel='0'");
    }

    public function get_sms_message_list($params, $smsids)
    {
        $date = $params['sms_date'];
        $result = $this->query("select MS_CONTENT,MPL_DATE from MESSAGE_POST_LOG where MS_ID in ($smsids) and MPL_DATE>='$date 00:00:00' and MPL_DATE<='$date 23:59:59' order by MPL_DATE desc");
        return $result;
    }
    public function get_news_list_by_date($params)
    {
        $date = $params['sms_date'];
        return $this->query("select nid,summary as MS_CONTENT,ndate as MPL_DATE from news where ndate>='$date 00:00:00' and ndate<='$date 23:59:59' and summary!='' order by nid desc");
    }

    public function get_news_list($where, $limit)
    {

        return $this->query("select DISTINCT newsid as nid,ntitle,ndate from news_column where columnid in (057,058,064,262) and ndate >='2020-01-01' $where order by ndate desc $limit");
    }

    public function get_news_lists($where)
    {

        return $this->getOne("select count( DISTINCT newsid) from news_column where columnid in (057,058,064,262) and ndate >='2020-01-01' $where   ");
    }

    public function getNews($id)
    {

        return $this->getOne("select ntitle from news where nid ='".$id."' limit 1");
    }

    public function get_market_week_news_totals($where)
    {
        return $this->getOne("select count(1) from NgGZJ_News where $where");
    }

    public function get_market_week_news_list($where,$limit)
    {
        return $this->query("select * from NgGZJ_News where $where order by id desc $limit");
    }

    public function get_admin_info(){
        return $this->Aquery("select userid,name from dept where state = 1 ");
    }

    //取得已登陆用户
    public function getUser( $GUID, $SignCS ,$mc_type ){
        return $this->getRow( "SELECT * FROM app_session_temp WHERE GUID = '$GUID' AND mc_type='$mc_type'" );
    }

    //新增会员日志记录
    public function WriteMemberLog($GUID, $SignCS,$Mid,$ComName,$OpDesc,$whereFrom,$mc_type, $ActionName, $ActionIp){
        $this->execute( "INSERT INTO app_member_logs SET
  			GUID='$GUID',
  			SignCS='$SignCS',	
  			Mid='$Mid',
  			ComName='$ComName',
  			OpDesc='$OpDesc',
  			OpDate=NOW(),
  			whereFrom='$whereFrom',
  			mc_type='$mc_type',
  			ActionName='$ActionName',
  			ActionDate=NOW(),
  			ActionIp='$ActionIp' ");

    }
    public function WriteLog($Mid, $Uid, $SignCS, $ActionName, $Actionstr, $ActionIp, $SystemType, $SystemVersion, $MessageTitle = '', $MessageId = '', $MessageDesc = '', $mc_type = 0)
    {
        $this->execute("INSERT INTO app_logs SET Mid='$Mid', Uid='$Uid', SignCS='$SignCS', ActionName='$ActionName',Actionstr='$Actionstr', ActionDate=NOW(),ActionIp='$ActionIp', SystemType='$SystemType', SystemVersion='$SystemVersion',MessageId='$MessageId', MessageTitle='$MessageTitle', MessageDesc='$MessageDesc' , mc_type='$mc_type'");
    }
}
?>