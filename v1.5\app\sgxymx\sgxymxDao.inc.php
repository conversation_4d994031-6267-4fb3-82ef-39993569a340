<?php
class sgxymxDao extends Dao{
  public function __construct( $writer ){
    parent::__construct( $writer );
  }
  //通过guid取得用户信息
	public function get_userinfo_byguid($GUID){
		$sql = "select * from app_session_temp where  GUID ='$GUID'  order by  LoginDate DESC  limit 1";
		return $this->getRow($sql);
  }
  
  //获取用户权限和用户名
	public function get_license_privilege($Uid){
		$sql = "select * from app_license,app_license_privilege where  app_license.id=app_license_privilege.lid and app_license_privilege.Uid='$Uid' and app_license_privilege.mc_type=1 limit 1";
		return $this->getRow($sql);
	}
	public function get_sg_data_table($type,$sdate,$edate,$where ){
		$sql = "select sum(dta_5) kdl,sum(dta_6) zje from sg_data_table where dta_type='$type'  and dta_ym>='$sdate' and dta_ym<='$edate' $where  ";
		//echo $sql.'<br>';
		return $this->getRow($sql);
	}
	public function get_sg_data_table_yf($type,$sdate,$edate,$where ){
		$sql = "select avg(dta_3) yf  from sg_data_table where dta_type='$type'  and dta_ym>='$sdate' and dta_ym<='$edate' $where  ";
		//echo $sql.'<br>';
		return $this->getRow($sql);
	}
	

	public function get_sg_data_table_zhyf($type,$sdate,$edate,$where ){
		$sql = "select avg(dta_4) yf  from sg_data_table where dta_type='$type'  and dta_ym>='$sdate' and dta_ym<='$edate' $where  ";
		//echo $sql.'<br>';
		return $this->getRow($sql);
	}


	public function getB($dta_type,$dta_vartype,$edate,$where ){
		$sql = "select dta_4 from sg_data_table where dta_type='$dta_type' and dta_vartype='$dta_vartype' and dta_ym='$edate'  $where  order by id desc limit 1";
		//echo $sql.'<br>';
		return $this->getOne($sql);
	}

	public function getR($dta_type,$sdate,$edate,$where ){
		$sql = "select avg(dta_7) yf  from sg_data_table where dta_type='$dta_type'  and dta_ym>='$sdate' and dta_ym<='$edate' and dta_7!='0' $where ";
		//echo $sql.'<br>';
		return $this->getOne($sql);
	}


	public function getA($dta_type,$dta_vartype,$sdate,$edate,$where ){
		$sql = "select sum(dta_3) je,sum(dta_2) sl from sg_data_table where dta_type='$dta_type' and dta_vartype='$dta_vartype'  and str_to_date( dta_ym,'%Y-%m' )>=str_to_date('".$sdate."','%Y-%m') and str_to_date( dta_ym,'%Y-%m' )<= str_to_date('".$edate."','%Y-%m')  $where  ";
		
		//$sql = "select sum(dta_3) je,sum(dta_2) sl from sg_data_table where dta_type='$dta_type' and dta_vartype='$dta_vartype'  and dta_ym>='$sdate' and dta_ym<= '$edate'  $where  ";
		//echo $sql.'<br>';
		return $this->getRow($sql);
	}
	public function getC($dta_type,$edate,$where ){
		$sql = "select dta_8 as xl,dta_9 as jg from sg_data_table where dta_type='$dta_type' and dta_ym='$edate'  $where  ";
		//echo $sql.'<br>';
		return $this->getRow($sql);
	}


}
?>