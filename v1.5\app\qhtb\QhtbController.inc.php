<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );

class QhtbController extends AbstractController{
  
  public function __construct(){
	
    parent::__construct();
	
	$this->_action->setDao( new QhtbDao( "MAIN" ) );
	$this->_action->steelhome= new QhtbDao('91R') ;//$houzhui 代表测试链接串 正式不用带
	$this->_action->drc= new QhtbDao('DRCW') ;
  }

  public function _dopre(){
  }
	

  public function v_xunigc() {
	$this->_action->xunigc($this->_request);
  }
  public function do_save_xunigc(){
	$this->_action->save_xunigc($this->_request);
  }
  public function do_timing_save(){
	  $this->_action->timing_save($this->_request);
  }
  public function v_tksft() {
	$this->_action->tksft($this->_request);
  }
  public function do_save_tksft(){
	$this->_action->save_tksft($this->_request);
  }
   public function v_gctbyl() {
	$this->_action->gctbyl($this->_request);
  }
  public function do_save_gctbyl(){
	$this->_action->save_gctbyl($this->_request);
  }
  public function do_timing_tksft(){
	 // echo "1111";
	  $this->_action->timing_tksft($this->_request);
  }
  public function do_adddate(){
	   $this->_action->adddate($this->_request);
  }

  public function v_xunigcnew() {
    $this->_action->xunigcnew($this->_request);
    }
  public function do_save_xunigcnew() {
    $this->_action->save_xunigcnew($this->_request);
    }

    public function do_timing_save_sg_xg()
    {
      $this->_action->timing_save_sg_xg($this->_request);
    }
    public function do_adddate_sg_xg(){
      $this->_action->adddate_sg_xg($this->_request);
   }
   public function do_timing_gctb_qh(){
    $this->_action->timing_gctb_qh($this->_request);
  }
}


?>