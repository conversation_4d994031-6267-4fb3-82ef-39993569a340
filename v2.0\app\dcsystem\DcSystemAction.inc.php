<?php
//error_reporting( E_ALL  );
//ini_set( "display_errors", 1 );
// require_once('../../../../steelconf_v3/debug.php');
ini_set("memory_limit", "512M");
ob_start('ob_gzhandler');

class DcSystemAction extends AbstractAction
{
    public $stedao;

    public function __construct()
    {
        parent::__construct();
    }

    private function getIP()
    {
        $ip = getenv('REMOTE_ADDR');
        $ip_ = getenv('HTTP_X_FORWARDED_FOR');
        if (($ip_ != "") && ($ip_ != "unknown")) {
            $ip = $ip_;
        }
        $ip = explode(",", $ip);
        return $ip[0];
    }

    public function getdata($array, $params)
    {
        $data = array();
        foreach ($array as $a) {
            $data[] = $a . "='" . $params[$a] . "'";
        }
        $data = implode(",", $data);
        return $data;
    }


    private function pri_arrayRecursive(&$array, $function, $apply_to_keys_also = false)
    {
        static $recursive_counter = 0;
        if (++$recursive_counter > 1000) {
            die('possible deep recursion attack');
        }
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $this->pri_arrayRecursive($array[$key], $function, $apply_to_keys_also);
            } else {
                $array[$key] = $function($value);
                // $array[$key] = $function(addslashes($value));
            }
            if ($apply_to_keys_also && is_string($key)) {
                $new_key = $function($key);
                if ($new_key != $key) {
                    $array[$new_key] = $array[$key];
                    unset($array[$key]);
                }
            }
        }
        $recursive_counter--;
    }

    private function pri_JSON($array)
    {
        //处理空数组
//        $array = emptyArrayToNullStr( $array );
        $this->pri_arrayRecursive($array, 'urlencode', true);
        $json = json_encode($array);
        return urldecode($json);
        //return iconv("GBK", "UTF-8//IGNORE", urldecode($json));
    }

    public function index($params)
    {
        echo "hello world";
    }

    public function doclasslistget($params)
    {
        if (!empty($params['isEnglish'])) $isEnglish = $params['isEnglish'];
        else $isEnglish = 0;

        //获取已购买数据
        $GUID = $params['GUID'];
        $SignCS = $params['SignCS'];
        $mc_type = $params['mc_type'];
        $action = $params['action'];        //接口名称
        if (!empty($params['mc_type'])) $mc_type = $params['mc_type'];
        else $mc_type = 0;
        $needCompress = $params['needCompress'];  //判断是否封装
        $ip = $this->getIP();

        $arr = array(
            'Success' => '0',
            'Message' => '失败',
            'Results' => 'null',
        );

        if ($isEnglish) $arr['Message'] = 'Failed!';

        if (array_key_exists($GUID, $GLOBALS["testaccountarr"]) && $SignCS != '' && (in_array($ip, $GLOBALS["testip"]) || $GLOBALS["nowtest"] == 1)) {
            $user = $GLOBALS["testaccountuser"];
        } else {
            $user = $this->_dao->getUser($GUID, $SignCS, $mc_type);
        }

        $mid = $user['Mid'];
        $uid = $user['Uid'];

        $arrtem = $params;
        foreach ($arrtem as $k => $v) {
            $actionstr .= "&" . $k . "=" . $v;
        }


        //$BUYDATA =$this->_dao->getOnes("select DataType from dc_custom_order left join app_license on dc_custom_order.Mid = app_license.Mid  where dc_custom_order.MID='".$mid."' AND app_license.StartDate<NOW() AND app_license.EndDate>now()");


        $BUYDATA = array();

        $BUYYEAR = array();

        $BUYDESC = $this->_dao->query("select DataType,Syear,Eyear from dc_custom_order inner join app_license on dc_custom_order.Mid = app_license.Mid and app_license.mc_type='$mc_type' and dc_custom_order.mc_type='$mc_type' where dc_custom_order.MID='" . $mid . "' AND app_license.StartDate<NOW() AND app_license.EndDate>now()");

        //$BUYDESC =$this->_dao->query("select DataType,Syear,Eyear from dc_custom_order left join app_license on dc_custom_order.Mid = app_license.Mid  where 1");

        //获取购买年月

        foreach ($BUYDESC as $key => $val) {
            $BUYDATA[$key] = $val['DataType'];

            $BUYYEAR[$val['DataType']]['SYEAR'] = $val['Syear'];
            $BUYYEAR[$val['DataType']]['EYEAR'] = $val['Eyear'];
        }


        $this->_dao->WriteLog($mid, $uid, $SignCS, $action, $actionstr, $this->getIP(), $this->getSystemType(), $this->getSystemVersion(), "树形菜单", '', '', $mc_type);


        //修改有效期内用户 可以看 价格指数  市场行情

        $isyx = $this->_dao->getRow("select * from app_license where MID = '" . $mid . "' AND app_license.StartDate<NOW() AND app_license.EndDate>now() ");

        if ($isyx) {
            $vdate = $this->_dao->getOnes("select ID from dc_code_class where scode in ('A','B') and mc_type='$mc_type'");

            $BUYDATA = array_unique(array_merge($BUYDATA, $vdate));

        }


        //用来重置 未购买数据
        function myfunction(&$value, $key)
        {

            if ($key != "dtname" && $key != "ID" && $key != "scode1" && $key != "scode2") {
                $value = "";
            }

        }

        //获取树形分类

        $firstlevel = $this->_dao->query("select ID,mcode,sname,scode,sod,sdesc from dc_code_class where mcode='' and Status =1 and mc_type='$mc_type' order by sod ");

        foreach ($firstlevel as &$tmp) {

            $tmp['sname'] = base64_encode($tmp['sname']);
            $tmp['sdesc'] = base64_encode($tmp['sdesc']);

            $searchlist = $this->_dao->query("select   ID,dtname,dtnameshort,ImageType,
                                              DTID1 ,
                                              DTID1Sub,
                                              Data1Type1,
                                              Data1Image,
                                              Data1Pre,
                                              Data1AddSub,
                                              Data1AddSubHundCore,
                                              Data1Type2,
                                              Data1Type3,
                                              Data1Type4,
                                              Data1Type5 ,
                                              DTID2 ,
                                              DTID2Sub,
                                              Data2Type ,Data2Image,  
                                              DTID3 ,
                                              DTID3Sub ,
                                              Data3Type , Data3Image,
                                              DTID4 ,
                                              DTID4Sub ,
                                              Data4Type , Data4Image,
                                              Data5Type,Data6Type,Data7Type,Data8Type,Data9Type,Data10Type,Data11Type,Data12Type,Data13Type,Data14Type,Data15Type,Data16Type,Data17Type,Data18Type,Data19Type,Data20Type,
                                              zhou1,zhou2,zhou3,zhou4,
                                              isjz,isbg,isfg,color,
                                              aod from dc_search_system where IsUse!=1 and scode1 = '" . $tmp['scode'] . "' and mc_type='$mc_type' order by aod", 0);


            foreach ($searchlist as &$ser) {
                $ser['dtname'] = base64_encode($ser['dtname']);
                $ser['dtnameshort'] = base64_encode($ser['dtnameshort']);
            }

            $tmp['SearchResults'] = $searchlist;


            $tmp['SubResults'] = $this->_dao->query("select ID,mcode,sname,snameshort,scode,sod,sdesc from dc_code_class where mcode = '" . $tmp['scode'] . "' and Status =1 and mc_type='$mc_type' order by sod", 0);

            foreach ($tmp['SubResults'] as &$tmp2) {
                $tmp2['sname'] = base64_encode($tmp2['sname']);
                $tmp2['snameshort'] = base64_encode($tmp2['snameshort']);
                $tmp2['sdesc'] = base64_encode($tmp2['sdesc']);

                if ($isEnglish) $state = " and IsEnglish=1 ";  //英文
                else $state = " and status=1 ";                //中文

                $tmp2['SubResults'] = $this->_dao->query("select distinct ID,scode1,scode2,dtname,dtymd,dtcomparetype,dbaseType,isSubDatas,subDatasTitle,subDatasDb,subDatasDb2,subAtt1Db,subAtt2Db,subAtt3Db,Data1Type,Data1UnitType,Data1UnitConv,isData2,Data2Type,Data2UnitType,Data2UnitConv,isData3,Data3Type,Data3UnitType,Data3UnitConv,isData4,Data4Type,Data4UnitType,Data4UnitConv,isData5,Data5Type,Data5UnitType,Data5UnitConv,isPreData,isAddSubData,isAddSubHundCore,startdate,enddataisCurrent,enddata,dtod,dtdesc,techdbname,techtablename,techsqlmain,ndate,ndata1,ndata2,ndata3,ndata4,ndata5,npredata,naddsubdata,naddsubhundcore,ddate,Ddata1,Ddata2,Ddata3,Ddata4,Ddata5,dpredata,daddsubdata,daddsubhundcore from dc_code_datatype inner join dc_code_datatype_db on dc_code_datatype_db.DID = dc_code_datatype.ID and dc_code_datatype.mc_type='$mc_type' and dc_code_datatype_db.mc_type='$mc_type' where scode2 = '" . $tmp2['scode'] . "' " . $state . "  order by dtod", 0);
//echo "select distinct ID,scode1,scode2,dtname,dtymd,dtcomparetype,dbaseType,isSubDatas,subDatasTitle,subDatasDb,subDatasDb2,subAtt1Db,subAtt2Db,subAtt3Db,Data1Type,Data1UnitType,Data1UnitConv,isData2,Data2Type,Data2UnitType,Data2UnitConv,isData3,Data3Type,Data3UnitType,Data3UnitConv,isData4,Data4Type,Data4UnitType,Data4UnitConv,isData5,Data5Type,Data5UnitType,Data5UnitConv,isPreData,isAddSubData,isAddSubHundCore,startdate,enddataisCurrent,enddata,dtod,dtdesc,techdbname,techtablename,techsqlmain,ndate,ndata1,ndata2,ndata3,ndata4,ndata5,npredata,naddsubdata,naddsubhundcore,ddate,Ddata1,Ddata2,Ddata3,Ddata4,Ddata5,dpredata,daddsubdata,daddsubhundcore from dc_code_datatype inner join dc_code_datatype_db on dc_code_datatype_db.DID = dc_code_datatype.ID and dc_code_datatype.mc_type='$mc_type' and dc_code_datatype_db.mc_type='$mc_type' where scode2 = '".$tmp2['scode']."' and Status =1  order by dtod";exit;

                foreach ($tmp2['SubResults'] as &$tmp3) {

//array_walk($tmp3,"myfunction");

                    if (in_array($tmp2['ID'], $BUYDATA) || in_array($tmp['ID'], $BUYDATA) || $mid == "1") {

                        if (in_array($tmp2['ID'], $BUYDATA)) {
                            $tmp3['SYear'] = $BUYYEAR[$tmp2['ID']]['SYEAR'];
                            $tmp3['EYear'] = $BUYYEAR[$tmp2['ID']]['EYEAR'];
                        } else if (in_array($tmp['ID'], $BUYDATA)) {
                            $tmp3['SYear'] = $BUYYEAR[$tmp['ID']]['SYEAR'];
                            $tmp3['EYear'] = $BUYYEAR[$tmp['ID']]['EYEAR'];
                        } else {
                            $tmp3['SYear'] = "";
                            $tmp3['EYear'] = "";
                        }
                        $tmp3['dtname'] = base64_encode($tmp3['dtname']);

                        $tmp3['subDatasTitle'] = base64_encode($tmp3['subDatasTitle']);

                        //if($BUYDATA[$tmp3['ID']]){

                        $tmp3['subDatasDb'] = base64_encode($tmp3['subDatasDb']);
                        $tmp3['subDatasDb2'] = base64_encode($tmp3['subDatasDb2']);

                        $tmp3['subAtt1Db'] = base64_encode($tmp3['subAtt1Db']);
                        $tmp3['subAtt2Db'] = base64_encode($tmp3['subAtt2Db']);
                        $tmp3['subAtt3Db'] = base64_encode($tmp3['subAtt3Db']);


                        $tmp3['dtdesc'] = base64_encode($tmp3['dtdesc']);
                        $tmp3['techsqlmain'] = base64_encode($tmp3['techsqlmain']);

                        $tmp3['ndate'] = base64_encode($tmp3['ndate']);
                        $tmp3['ndata1'] = base64_encode($tmp3['ndata1']);
                        $tmp3['ndata2'] = base64_encode($tmp3['ndata2']);
                        $tmp3['ndata3'] = base64_encode($tmp3['ndata3']);
                        $tmp3['ndata4'] = base64_encode($tmp3['ndata4']);
                        $tmp3['ndata5'] = base64_encode($tmp3['ndata5']);
                        $tmp3['npredata'] = base64_encode($tmp3['npredata']);
                        $tmp3['naddsubdata'] = base64_encode($tmp3['naddsubdata']);
                        $tmp3['naddsubhundcore'] = base64_encode($tmp3['naddsubhundcore']);

                        $tmp3['isbuy'] = "1";

                        $tmp3['SubResults'] = $this->_dao->query("select  ID,sname,scode,sdbtype,satt1,satt2,satt3,aod from dc_code_datatype_subs where DID = '" . $tmp3['ID'] . "' and Status =1 and mc_type='$mc_type' order by aod", 0);
                        //alert($tmp3['ID']);

                        foreach ($tmp3['SubResults'] as &$tmp4) {
                            $tmp4['sname'] = base64_encode(html_entity_decode($tmp4['sname']));
                            $tmp4['scode'] = base64_encode(html_entity_decode($tmp4['scode']));
                            $tmp4['satt1'] = base64_encode(html_entity_decode($tmp4['satt1']));
                            $tmp4['satt2'] = base64_encode(html_entity_decode($tmp4['satt2']));
                            $tmp4['satt3'] = base64_encode(html_entity_decode($tmp4['satt3']));
                        }

                        //alert($tmp3['ID']);
                    } else {
                        array_walk($tmp3, "myfunction");

                        $tmp3['dtname'] = base64_encode($tmp3['dtname']);

                        $tmp3['isbuy'] = "0";
                        $tmp3['SubResults'] = array();
                    }

                }
            }

        }


        //print_r($firstlevel);
        //exit;

        $arr['Success'] = 1;
        $arr['Message'] = base64_encode("成功");
        if ($isEnglish) $arr['Message'] = base64_encode("Success");

        if ($needCompress == 1) {
            $arr['Results'] = base64_encode(gzencode(json_encode($firstlevel), 9));//压缩找个方法
        } else {
            $arr['Results'] = $firstlevel;
        }

//		print_r($arr);
        $json_string = $this->pri_JSON($arr);
        echo $json_string;


    }

    //hlf/2017/11/3
    public function GetMainMenu($params)
    {
        $mod = $params['mod'];
        $mc_type = $params['mc_type'];
        //print_r($mc_type);exit;
        $action = $params['action'];        //接口名称
        if (!empty($params['mc_type'])) $mc_type = $params['mc_type'];
        else $mc_type = 0;
        if (!empty($params['isEnglish'])) $isEnglish = $params['isEnglish'];
        else $isEnglish = 0;
        //$user = $this->_dao->getUser($mod,$mc_type);
        $arr = array(
            'Success' => '0',
            'Message' => '失败',
            'Results' => 'null',
        );
        if ($isEnglish) $arr['Message'] = 'Failed!';

        $firstlevel = $this->_dao->query("select ID,mcode,sname,scode,sod,sdesc from dc_code_class where mcode='' and Status =1 and mc_type=$mc_type order by sod ");
        //	print_r($firstlevel);exit;
        foreach ($firstlevel as &$res) {
            //print_r($mod);exit;
            //if($mod=='Mobile' || $mod=='Pad' ){
            $res["SubResults"] = "";

            //}
            if ($mod == 'PC') {
                //print_r($mod);exit;
                $scode = $res['scode'];
                //print_r($mcode);exit;
                $sql = "select ID,mcode,sname,scode,sod,sdesc from dc_code_class where mcode='$scode' and Status =1 and mc_type=$mc_type order by sod";
                $res["SubResults"] = $this->_dao->query($sql);
                //print_r($sql."<br>");//exit;

            }
        }


        $arr['Success'] = 1;
        $arr['Message'] = "成功";
        if ($isEnglish) $arr['Message'] = "Success";
        $arr['Results'] = $firstlevel;
        $json_string = $this->pri_JSON($arr);
        echo $json_string;


    }

    public function doclasslistget2($params)
    {
        //if($params["mc_type"]=="") {$this->doclasslistget($params);exit;}

        //获取已购买数据
        $GUID = $params['GUID'];
        $SignCS = $params['SignCS'];
        $mc_type = $params['mc_type'];
        $action = $params['action'];        //接口名称
        $ip = $this->getIP();
        if (!empty($params['mc_type'])) $mc_type = $params['mc_type'];
        else $mc_type = 0;
        if (!empty($params['isEnglish'])) $isEnglish = $params['isEnglish'];
        else $isEnglish = 0;
        $needCompress = $params['needCompress'];  //判断是否封装

        $isen = "";
        if ($isEnglish == 1) $isen = "_en";

        $ewhere = "Status =1";
        if ($isEnglish == 1) $ewhere = "IsEnglish =1";

        $isuse = "0";
        if ($isEnglish == 1) $isuse = "1";

        $arr = array(
            'Success' => '0',
            'Message' => '失败',
            'Results' => 'null',
        );
        if ($isEnglish) $arr['Message'] = 'Failed!';

        if (array_key_exists($GUID, $GLOBALS["testaccountarr"]) && $SignCS != '' && (in_array($ip, $GLOBALS["testip"]) || $GLOBALS["nowtest"] == 1)) {
            $user = $GLOBALS["testaccountuser"];
        } else {
            $user = $this->_dao->getUser($GUID, $SignCS, $mc_type);
        }
        if (empty($user)) {
            $arr = array(
                'Success' => '0',
                'Message' => '无此用户',
                'Results' => 'null',
            );
            if ($isEnglish) $arr['Message'] = 'Wrong Account!';
            $json_string = $this->pri_JSON($arr);
            echo $json_string;
            exit;
        }
        $mid = $user['Mid'];
        $uid = $user['Uid'];
        $arrtem = $params;
        foreach ($arrtem as $k => $v) {
            $actionstr .= "&" . $k . "=" . $v;
        }

        //$BUYDATA =$this->_dao->getOnes("select DataType from dc_custom_order left join app_license on dc_custom_order.Mid = app_license.Mid  where dc_custom_order.MID='".$mid."' AND app_license.StartDate<NOW() AND app_license.EndDate>now()");


        $BUYDATA = array();

        $BUYYEAR = array();

        $BUYDESC = $this->_dao->query("select DataType,Syear,Eyear from dc_custom_order inner join app_license on dc_custom_order.Mid = app_license.Mid and app_license.mc_type='$mc_type' and dc_custom_order.mc_type='$mc_type' where dc_custom_order.MID='" . $mid . "' AND app_license.StartDate<NOW() AND app_license.EndDate>now()");

        //$BUYDESC =$this->_dao->query("select DataType,Syear,Eyear from dc_custom_order left join app_license on dc_custom_order.Mid = app_license.Mid  where 1");

        //获取购买年月

        foreach ($BUYDESC as $key => $val) {
            $BUYDATA[$key] = $val['DataType'];

            $BUYYEAR[$val['DataType']]['SYEAR'] = $val['Syear'];
            $BUYYEAR[$val['DataType']]['EYEAR'] = $val['Eyear'];
        }


        $this->_dao->WriteLog($mid, $uid, $SignCS, $action, $actionstr, $this->getIP(), $this->getSystemType(), $this->getSystemVersion(), "树形菜单", '', '', $mc_type);


        //修改有效期内用户 可以看 价格指数  市场行情

        $isyx = $this->_dao->getRow("select * from app_license where MID = '" . $mid . "' AND app_license.StartDate<NOW() AND app_license.EndDate>now() ");

        if ($isyx) {
            $vdate = $this->_dao->getOnes("select ID from dc_code_class where scode in ('A','B') and mc_type='$mc_type'");

            $BUYDATA = array_unique(array_merge($BUYDATA, $vdate));

        }


        //用来重置 未购买数据
        function myfunction2(&$value, $key)
        {

            if ($key != "dtname" && $key != "ID" && $key != "scode1" && $key != "scode2") {
                $value = "";
            }

        }


        //获取树形分类

        $smallarr = $this->_dao->query("select ID,mcode,sname" . $isen . " as sname,snameshort" . $isen . " as snameshort,scode,sod,sdesc from dc_code_class where Status =1 and mcode!='' and mc_type='$mc_type' order by sod ");
        foreach ($smallarr as $arr2) {
            $arr2["hasnode"] = 0;
            $menu[$arr2['mcode']][] = $arr2;
        }

        if ($mid != 1) $notincludePS = " and dc_search_system.ID != 4 ";
        $smallarr = $this->_dao->query("select ID,scode1,dtname" . $isen . " as dtname,dtnameshort" . $isen . " as dtnameshort,ImageType,DTID1,DTID1Sub,
			  Data1Type,
			  Data1Type1,
			  Data1Image,
			  Data1Pre,
			  Data1AddSub,
			  Data1AddSubHundCore,
			  Data1Type2,
			  Data1Type3,
			  Data1Type4,
			  Data1Type5 ,
			  DTID2 ,
			  DTID2Sub,
			  Data2Type ,Data2Image,  
			  DTID3 ,
			  DTID3Sub ,
			  Data3Type , Data3Image,
			  DTID4 ,
			  DTID4Sub ,
			  Data4Type , Data4Image,
			  zhou1,zhou2,zhou3,zhou4,
			  isjz,isbg,isfg,color,aod from dc_search_system where IsUse='" . $isuse . "' $notincludePS and mc_type='$mc_type' order by aod");

        foreach ($smallarr as $arr2) {
            $count = count($commons[$arr2['scode1']]);
            $commons[$arr2['scode1']][] = $arr2;
            unset($commons[$arr2['scode1']][$count]['scode1']);
        }

        $empty = array();

        if ($mid != 1) $notincludePS_type = " and dc_code_datatype.ID != 423 ";
        if ($mc_type == 1) {
            $smallarr = $this->_dao->query("select distinct ID,scode1,scode2,scode3,scode4,dtname" . $isen . " as dtname,dtymd,dtcomparetype,dbaseType,isSubDatas,subDatasTitle" . $isen . " as subDatasTitle,subDatasDb,subDatasDb2,subAtt1Db" . $isen . " as subAtt1Db,subAtt2Db" . $isen . " as subAtt2Db,subAtt3Db" . $isen . " as subAtt3Db,Data1Type,Data2Type,Data3Type,Data4Type,Data5Type,Data6Type,Data7Type,Data8Type,Data9Type,Data10Type,Data11Type,Data12Type,Data13Type,Data14Type,Data15Type,Data16Type,Data17Type,Data18Type,Data19Type,Data20Type,startdate,enddata,dtod,ndate" . $isen . " as ndate,ndata1" . $isen . " as ndata1,ndata2" . $isen . " as ndata2,ndata3" . $isen . " as ndata3,ndata4" . $isen . " as ndata4,ndata5" . $isen . " as ndata5,ndata6" . $isen . " as ndata6,ndata7" . $isen . " as ndata7,ndata8" . $isen . " as ndata8,ndata9" . $isen . " as ndata9,ndata10" . $isen . " as ndata10,ndata11" . $isen . " as ndata11,ndata12" . $isen . " as ndata12,ndata13" . $isen . " as ndata13,ndata14" . $isen . " as ndata14,ndata15" . $isen . " as ndata15,ndata16" . $isen . " as ndata16,ndata17" . $isen . " as ndata17,ndata18" . $isen . " as ndata18,ndata19" . $isen . " as ndata19,ndata20" . $isen . " as ndata20,npredata" . $isen . " as npredata,naddsubdata" . $isen . " as naddsubdata,naddsubhundcore" . $isen . " as naddsubhundcore,TargetFlagVar from dc_code_datatype inner join dc_code_datatype_db on dc_code_datatype_db.DID = dc_code_datatype.ID and dc_code_datatype.mc_type='$mc_type' and dc_code_datatype_db.mc_type='$mc_type' where Status =1 $notincludePS_type order by dtod");
        } else {
            $smallarr = '';
        }
        //foreach($smallarr as $arr2){
        //	$types[$arr2['ID']]=$arr2;
        //	unset($types[$arr2['ID']]['ID']);
        //}
        foreach ($smallarr as $arr2) {
            if ($arr2["scode4"]) {
                $typecode[4][$arr2["scode4"]][] = $arr2;
                continue;
            }
            if ($arr2["scode3"]) {
                $typecode[3][$arr2["scode3"]][] = $arr2;
                continue;
            }
            if ($arr2["scode2"]) {
                $typecode[2][$arr2["scode2"]][] = $arr2;
                continue;
            }
            if ($arr2["scode1"]) {
                $typecode[1][$arr2["scode1"]][] = $arr2;
                continue;
            }
        }
        if ($mc_type == 1) {
            $smallarr = $this->_dao->query("select dc_code_datatype_subs.ID,DID,sname" . $isen . " as sname,scode" . $isen . " as scode,sdbtype,satt1" . $isen . " as satt1,satt2" . $isen . " as satt2,satt3" . $isen . " as satt3,aod from dc_code_datatype_subs inner join dc_code_datatype on dc_code_datatype_subs.did= dc_code_datatype.id where dc_code_datatype_subs.Status =1 and dc_code_datatype_subs.mc_type='$mc_type' and dc_code_datatype.isSubDatas=1 order by aod,scode");
        } else {
            $smallarr = '';
        }
        //$smallarr=$this->_dao->query("select ID,DID,sname".$isen." as sname,scode".$isen." as scode,sdbtype,satt1".$isen." as satt1,satt2".$isen." as satt2,satt3".$isen." as satt3,aod from dc_code_datatype_subs where Status =1 and mc_type='$mc_type' order by aod");

        foreach ($smallarr as $arr2) {
            $count = count($subs[$arr2['DID']]);
            $subs[$arr2['DID']][] = $arr2;
            unset($subs[$arr2['DID']][$count]['DID']);
        }

        //print"<pre>";print"<br>menu(".count($menu)."):";print_r($menu);print"<br>commons:";print_r($commons);print"<br>types:";print_r($types);print"<br>typecode:";print_r($typecode);print"<br>subs:";print_r($subs);exit;
        $SystemType = $user['SystemType'];
        if (empty($SystemType)) $SystemType = $this->_dao->getOne("select SystemType from app_session_temp where GUID='$GUID' and SignCS='$SignCS' order by LoginDate desc limit 1");
        if (in_array($SystemType, array("NewPC", "NewAndroidPad", "NewAndroidPhone", "NewiPad", "NewiPhone")) == false) $owhere = " and scode!='J' ";
        else $owhere = "";
        $firstlevel = $this->_dao->query("select ID,mcode,sname" . ($isEnglish == 1 ? "short_en" : "") . " as sname,scode,sod,sdesc,0 as hasnode from dc_code_class where mcode='' and Status =1 and mc_type='$mc_type' $owhere order by sod ");
        //print"<pre>";print_r($firstlevel);exit;

        foreach ($firstlevel as &$tmp) {

            $tmp['sname'] = $tmp['sname'];
            $tmp['sdesc'] = $tmp['sdesc'];
            $tmp['hasnode'] = $menu[$tmp["scode"]] ? "1" : "";

            $searchlist = empty($commons[$tmp["scode"]]) ? $empty : $commons[$tmp["scode"]];

            foreach ($searchlist as &$ser) {
                $ser['dtname'] = $ser['dtname'];
                $ser['dtnameshort'] = $ser['dtnameshort'];
            }

            $tmp['SearchResults'] = $searchlist;

            $tmp['SubResults'] = empty($menu[$tmp["scode"]]) ? $empty : $menu[$tmp["scode"]];

            $this->getsubmenu($tmp['SubResults'], $menu, $commons, $typecode, $subs, $BUYDATA, $BUYYEAR, $tmp['ID'], $mid, $mc_type, $nn = 2);
            /*
			foreach($tmp['SubResults'] as &$tmp2) {
				$tmp2['sname']= base64_encode($tmp2['sname']);
				$tmp2['snameshort']= base64_encode($tmp2['snameshort']);
				$tmp2['sdesc']= base64_encode($tmp2['sdesc']);
echo "select ID,mcode,sname,snameshort,scode,sod,sdesc from dc_code_class where mcode='$tmp2[scode]'<br>";
				$submenu=$this->_dao->query("select ID,mcode,sname,snameshort,scode,sod,sdesc from dc_code_class where mcode='$tmp2[scode]'");														//如果还有一层子目录

				if($submenu){
					$tmp2['SubResults']=$submenu;
					foreach($tmp2['SubResults'] as &$ms){
					$ms['sname']= base64_encode($ms['sname']);
					$ms['snameshort']= base64_encode($ms['snameshort']);
					$ms['sdesc']= base64_encode($ms['sdesc']);
					$ms['SubResults'] = $this->_dao->query("select distinct ID,scode1,scode2,dtname,dtymd,dtcomparetype,dbaseType,isSubDatas,subDatasTitle,subDatasDb,subDatasDb2,subAtt1Db,subAtt2Db,subAtt3Db,Data1Type,Data1UnitType,Data1UnitConv,isData2,Data2Type,Data2UnitType,Data2UnitConv,isData3,Data3Type,Data3UnitType,Data3UnitConv,isData4,Data4Type,Data4UnitType,Data4UnitConv,isData5,Data5Type,Data5UnitType,Data5UnitConv,isPreData,isAddSubData,isAddSubHundCore,startdate,enddataisCurrent,enddata,dtod,dtdesc,techdbname,techtablename,techsqlmain,ndate,ndata1,ndata2,ndata3,ndata4,ndata5,npredata,naddsubdata,naddsubhundcore,ddate,Ddata1,Ddata2,Ddata3,Ddata4,Ddata5,dpredata,daddsubdata,daddsubhundcore from dc_code_datatype inner join dc_code_datatype_db on dc_code_datatype_db.DID = dc_code_datatype.ID and dc_code_datatype.mc_type='$mc_type' and dc_code_datatype_db.mc_type='$mc_type' where scode2 = '".$ms['scode']."' and Status =1  order by dtod",0);
					$ls=$tmp2['SubResults'];
					}
				}else{
					$tmp2['SubResults'] = $this->_dao->query("select distinct ID,scode1,scode2,dtname,dtymd,dtcomparetype,dbaseType,isSubDatas,subDatasTitle,subDatasDb,subDatasDb2,subAtt1Db,subAtt2Db,subAtt3Db,Data1Type,Data1UnitType,Data1UnitConv,isData2,Data2Type,Data2UnitType,Data2UnitConv,isData3,Data3Type,Data3UnitType,Data3UnitConv,isData4,Data4Type,Data4UnitType,Data4UnitConv,isData5,Data5Type,Data5UnitType,Data5UnitConv,isPreData,isAddSubData,isAddSubHundCore,startdate,enddataisCurrent,enddata,dtod,dtdesc,techdbname,techtablename,techsqlmain,ndate,ndata1,ndata2,ndata3,ndata4,ndata5,npredata,naddsubdata,naddsubhundcore,ddate,Ddata1,Ddata2,Ddata3,Ddata4,Ddata5,dpredata,daddsubdata,daddsubhundcore from dc_code_datatype inner join dc_code_datatype_db on dc_code_datatype_db.DID = dc_code_datatype.ID and dc_code_datatype.mc_type='$mc_type' and dc_code_datatype_db.mc_type='$mc_type' where scode2 = '".$tmp2['scode']."' and Status =1  order by dtod",0);
					$ls=$tmp2;
				}

				foreach($ls['SubResults'] as &$tmp3) {

				if(in_array($ls['ID'],$BUYDATA) || in_array($tmp['ID'],$BUYDATA)  || $mid=="1"){

					if(in_array($ls['ID'],$BUYDATA)){
						$tmp3['SYear']= $BUYYEAR[$ls['ID']]['SYEAR'] ;
						$tmp3['EYear']= $BUYYEAR[$ls['ID']]['EYEAR'] ;
					}
					else if(in_array($tmp['ID'],$BUYDATA)){
						$tmp3['SYear']= $BUYYEAR[$tmp['ID']]['SYEAR'] ;
						$tmp3['EYear']= $BUYYEAR[$tmp['ID']]['EYEAR'] ;
					}else{
						$tmp3['SYear']= "" ;
						$tmp3['EYear']= "" ;
					}
					$tmp3['dtname']= base64_encode($tmp3['dtname']);

					$tmp3['subDatasTitle']= base64_encode($tmp3['subDatasTitle']);

					//if($BUYDATA[$tmp3['ID']]){

						$tmp3['subDatasDb']= base64_encode($tmp3['subDatasDb']);
						$tmp3['subDatasDb2']= base64_encode($tmp3['subDatasDb2']);

						$tmp3['subAtt1Db']= base64_encode($tmp3['subAtt1Db']);
						$tmp3['subAtt2Db']= base64_encode($tmp3['subAtt2Db']);
						$tmp3['subAtt3Db']= base64_encode($tmp3['subAtt3Db']);


						$tmp3['dtdesc']= base64_encode($tmp3['dtdesc']);
						$tmp3['techsqlmain']= base64_encode($tmp3['techsqlmain']);

						$tmp3['ndate']= base64_encode($tmp3['ndate']);
						$tmp3['ndata1']= base64_encode($tmp3['ndata1']);
						$tmp3['ndata2']= base64_encode($tmp3['ndata2']);
						$tmp3['ndata3']= base64_encode($tmp3['ndata3']);
						$tmp3['ndata4']= base64_encode($tmp3['ndata4']);
						$tmp3['ndata5']= base64_encode($tmp3['ndata5']);
						$tmp3['npredata']= base64_encode($tmp3['npredata']);
						$tmp3['naddsubdata']= base64_encode($tmp3['naddsubdata']);
						$tmp3['naddsubhundcore']= base64_encode($tmp3['naddsubhundcore']);

						$tmp3['isbuy'] = "1";

						$tmp3['SubSubResults'] = $this->_dao->query("select  ID,sname,scode,sdbtype,satt1,satt2,satt3,aod from dc_code_datatype_subs where DID = '".$tmp3['ID']."' and Status =1 and mc_type='$mc_type' order by aod",0);
						//alert($tmp3['ID']);

						foreach($tmp3['SubSubResults'] as &$tmp4) {
							$tmp4['sname'] = base64_encode(html_entity_decode($tmp4['sname']));
							$tmp4['scode'] = base64_encode(html_entity_decode($tmp4['scode']));
							$tmp4['satt1'] = base64_encode(html_entity_decode($tmp4['satt1']));
							$tmp4['satt2'] = base64_encode(html_entity_decode($tmp4['satt2']));
							$tmp4['satt3'] = base64_encode(html_entity_decode($tmp4['satt3']));
						}

						//alert($tmp3['ID']);
					}else{
						array_walk($tmp3,"myfunction2");

						$tmp3['dtname']= base64_encode($tmp3['dtname']);
						$tmp3['isbuy'] = "0";
						$tmp3['SubResults'] = array();
					}

				}

				if($submenu){
					$tmp2['SubResults']=$ls;
				}else{
					$tmp2=$ls;
				}

			}
			*/
        }

        //print_r($firstlevel);
        //exit;

        $arr['Success'] = 1;
        $arr['Message'] = "成功";
        if ($isEnglish) $arr['Message'] = "Success";

        if ($needCompress == 1 && empty($params['is_search'])) {
            $arr['Results'] = base64_encode(gzencode(json_encode($firstlevel), 9));//压缩找个方法
        } else {
            $arr['Results'] = $firstlevel;
        }

        //print"<pre>";print_r($arr);print"</pre>";exit;
        if (empty($params['is_search'])) {
            $json_string = $this->pri_JSON($arr);
            echo $json_string;
        } else {
            return $arr;
        }
    }

    public function getsubmenu(&$tmp, $menu, $commons, $typecode, $subs, $BUYDATA, $BUYYEAR, $tmpID, $mid, $mc_type, &$js)
    {
        $empty = array();
        //if(empty($tmp)) {return;}
        foreach ($tmp as &$tmp2) {
            $nn = $js;
            $tmp2['sname'] = $tmp2['sname'];
            $tmp2['snameshort'] = $tmp2['snameshort'];
            $tmp2['sdesc'] = $tmp2['sdesc'];
            $tmp2['hasnode'] = $menu[$tmp2["scode"]] ? "1" : "";
            $submenu = empty($menu[$tmp2["scode"]]) ? $empty : $menu[$tmp2["scode"]];    //如果还有一层子目录 scode,
            if ($submenu) {
                $nn++;
                $tmp2['SubResults'] = $this->getsubmenu($submenu, $menu, $commons, $typecode, $subs, $BUYDATA, $BUYYEAR, $tmpID, $mid, $mc_type, $nn);
            } else {
                $tmp2['SubResults'] = empty($typecode[$js][$tmp2["scode"]]) ? $empty : $typecode[$js][$tmp2["scode"]];
                if (empty($tmp2['SubResults'])) {
                    $tmp2['SubResults'] = array();
                    continue;
                }
                foreach ($tmp2['SubResults'] as &$tmp3) {

                    if (in_array($tmp2['ID'], $BUYDATA) || in_array($tmpID, $BUYDATA) || $mid == "1") {

                        if (in_array($tmp2['ID'], $BUYDATA)) {
                            $tmp3['SYear'] = $BUYYEAR[$tmp2['ID']]['SYEAR'];
                            $tmp3['EYear'] = $BUYYEAR[$tmp2['ID']]['EYEAR'];
                        } else if (in_array($tmpID, $BUYDATA)) {
                            $tmp3['SYear'] = $BUYYEAR[$tmpID]['SYEAR'];
                            $tmp3['EYear'] = $BUYYEAR[$tmpID]['EYEAR'];
                        } else {
                            $tmp3['SYear'] = "";
                            $tmp3['EYear'] = "";
                        }
                        $tmp3['dtname'] = ($tmp3['dtname']);
                        //if($tmp3['isSubDatas']!="1")$tmp3['LineTitle']=base64_encode(html_entity_decode($tmp3['dtname']));

                        $tmp3['subDatasTitle'] = ($tmp3['subDatasTitle']);

                        //if($BUYDATA[$tmp3['ID']]){

                        $tmp3['subDatasDb'] = ($tmp3['subDatasDb']);
                        $tmp3['subDatasDb2'] = ($tmp3['subDatasDb2']);

                        $tmp3['subAtt1Db'] = ($tmp3['subAtt1Db']);
                        $tmp3['subAtt2Db'] = ($tmp3['subAtt2Db']);
                        $tmp3['subAtt3Db'] = ($tmp3['subAtt3Db']);


                        $tmp3['dtdesc'] = ($tmp3['dtdesc']);
                        $tmp3['techsqlmain'] = ($tmp3['techsqlmain']);

                        $tmp3['ndate'] = ($tmp3['ndate']);
                        $tmp3['ndata1'] = ($tmp3['ndata1']);
                        $tmp3['ndata2'] = ($tmp3['ndata2']);
                        $tmp3['ndata3'] = ($tmp3['ndata3']);
                        $tmp3['ndata4'] = ($tmp3['ndata4']);
                        $tmp3['ndata5'] = ($tmp3['ndata5']);
                        $tmp3['ndata6'] = ($tmp3['ndata6']);
                        $tmp3['ndata7'] = ($tmp3['ndata7']);
                        $tmp3['ndata8'] = ($tmp3['ndata8']);
                        $tmp3['ndata9'] = ($tmp3['ndata9']);
                        $tmp3['ndata10'] = ($tmp3['ndata10']);
                        $tmp3['ndata11'] = ($tmp3['ndata11']);
                        $tmp3['ndata12'] = ($tmp3['ndata12']);
                        $tmp3['ndata13'] = ($tmp3['ndata13']);
                        $tmp3['ndata14'] = ($tmp3['ndata14']);
                        $tmp3['ndata15'] = ($tmp3['ndata15']);
                        $tmp3['ndata16'] = ($tmp3['ndata16']);
                        $tmp3['ndata17'] = ($tmp3['ndata17']);
                        $tmp3['ndata18'] = ($tmp3['ndata18']);
                        $tmp3['ndata19'] = ($tmp3['ndata19']);
                        $tmp3['ndata20'] = ($tmp3['ndata20']);
                        $tmp3['npredata'] = ($tmp3['npredata']);
                        $tmp3['naddsubdata'] = ($tmp3['naddsubdata']);
                        $tmp3['naddsubhundcore'] = ($tmp3['naddsubhundcore']);

                        $tmp3['isbuy'] = "1";

                        $tmp3['SubSubResults'] = empty($subs[$tmp3["ID"]]) ? $empty : $subs[$tmp3["ID"]];
                        //alert($tmp3['ID']);

                        foreach ($tmp3['SubSubResults'] as &$tmp4) {
                            $tmp4['sname'] = (html_entity_decode($tmp4['sname']));
                            $tmp4['scode'] = (html_entity_decode($tmp4['scode']));
                            $tmp4['satt1'] = (html_entity_decode($tmp4['satt1']));
                            $tmp4['satt2'] = (html_entity_decode($tmp4['satt2']));
                            $tmp4['satt3'] = (html_entity_decode($tmp4['satt3']));
                            //$tmp4['LineTitle']=base64_encode(html_entity_decode($tmp3['dtname']."-".$tmp4['sname']));
                        }

                        //alert($tmp3['ID']);
                    } else {
                        array_walk($tmp3, "myfunction2");

                        $tmp3['dtname'] = ($tmp3['dtname']);
                        $tmp3['isbuy'] = "0";
                        $tmp3['SubResults'] = array();
                    }

                }
            }
        }
        return $tmp;
    }

    public function SearchListSave($params)
    {
        //$_GET['LineTitle1'] base64转码+消失，空格替换
        if ($_GET['LineTitle1'] != '') {
            $one = $_GET['LineTitle1'];
        } else {
            $one = $_POST['LineTitle1'];
        }
        if ($_GET['LineTitle2'] != '') {
            $two = $_GET['LineTitle2'];
        } else {
            $two = $_POST['LineTitle2'];
        }
        if ($_GET['LineTitle3'] != '') {
            $three = $_GET['LineTitle3'];
        } else {
            $three = $_POST['LineTitle3'];
        }
        if ($_GET['LineTitle4'] != '') {
            $four = $_GET['LineTitle4'];
        } else {
            $four = $_POST['LineTitle4'];
        }

        if ($_GET['unitstring'] != '') {
            $five = $_GET['unitstring'];
        } else {
            $five = $_POST['unitstring'];
        }
        $params['LineTitle1'] = str_replace(' ', '+', $one);
        $params['LineTitle2'] = str_replace(' ', '+', $two);
        $params['LineTitle3'] = str_replace(' ', '+', $three);
        $params['LineTitle4'] = str_replace(' ', '+', $four);
        if ($params['debug2'] == '1') {
            echo "<pre>";
            echo $params['LineTitle1'];
            echo '<br>';
        }
        /* foreach($params as $k => $p){
			$params[$k] = str_replace(' ','+',$p);
		} */
        $codetype = $this->testEncoding(base64_decode($params['ImageTitle']));
        if ($codetype == 1) {
            //$params['ImageTitle'] = base64_encode(iconv("UTF-8", "GBK", base64_decode($params['ImageTitle'])));
            //$params['ImageTitle'] = $params['ImageTitle'];
        }
        $codetype = $this->testEncoding($params['dtname']);
        if ($codetype == 1) {
            //$params['dtname'] = iconv("UTF-8", "GBK", $params['dtname']);
            //$params['dtname'] = $params['dtname'];
        }
        //print_r($params);exit;
        if ($this->checkGUID($params)) {

            $GUID = $params['GUID'];
            $SignCS = $params['SignCS'];
            $mc_type = $params['mc_type'];
            $action = $params['action'];        //接口名称
            $catalogueid = $params['catalogueid'];  //目录id
            $ip = $this->getIP();
            if (!empty($params['mc_type'])) $mc_type = $params['mc_type'];
            else $mc_type = 0;
            //判断 mc_type 值  区分 钢之家和 南钢

            if (!empty($params['isEnglish'])) $isEnglish = $params['isEnglish'];
            else $isEnglish = 0;

            $arr = array(
                'Success' => '0',
                'ErrorType' => '',
                'Message' => '失败',
                'Results' => 'null',
                'ImageURL' => '',
            );
            if ($isEnglish) $arr['Message'] = 'Failed!';

            if (array_key_exists($GUID, $GLOBALS["testaccountarr"]) && $SignCS != '' && (in_array($ip, $GLOBALS["testip"]) || $GLOBALS["nowtest"] == 1)) {
                $user = $GLOBALS["testaccountuser"];
            } else {
                $user = $this->_dao->getUser($GUID, $SignCS, $mc_type);
            }
            $mid = $user['Mid'];
            $uid = $user['Uid'];

            $arrtem = $params;
            //array_pop($arrtem);
            $actionstr = "";
            foreach ($arrtem as $k => $v) {
                $actionstr .= "&" . $k . "=" . $v;
            }
            //add 换成中文保存
            /*$params['LineTitle1'] = base64_decode($params['LineTitle1']);
            $params['LineTitle2'] = base64_decode($params['LineTitle2']);
            $params['LineTitle3'] = base64_decode($params['LineTitle3']);
            $params['LineTitle4'] = base64_decode($params['LineTitle4']);

            $params['ImageTitle'] = base64_decode($params['ImageTitle']);*/


            //
            $array = array(
                "dtname",
                "dtname_en",
                "ImageTitle",
                "DTID1",
                "DTID1Sub",
                "Data1Type",
                "Data1Type1",
                "Data1Pre",
                "Data1AddSub",
                "Data1AddSubHundCore",
                "Data1Type2",
                "Data1Type3",
                "Data1Type4",
                "Data1Type5",
                "DTID2",
                "DTID2Sub",
                "Data2Type",
                "DTID3",
                "DTID3Sub",
                "Data3Type",
                "DTID4",
                "DTID4Sub",
                "Data4Type",
                "ImageType",
                "Data1Image",
                "Data2Image",
                "Data3Image",
                "Data4Image",
                "zhou1",
                "zhou2",
                "zhou3",
                "zhou4",
                "isjz",
                "isbg",
                "isfg",
                "color",
                "LineTitle1",
                "LineTitle2",
                "LineTitle3",
                "LineTitle4",
                "GongshiID1",
                "GongshiID2",
                "GongshiID3",
                "GongshiID4",
                "catalogueid",
                "DTIDJson",
                "ChartExtLineStyle",
                "DateFormat",
            );

            //中英文名称处理下
            if ($isEnglish == 1) {

                $params['dtname_en'] = $params['dtname'];
            }


            $DTIDJson = html_entity_decode(Urldecode($params['DTIDJson']));
            //echo '<pre>';print_R($DTIDJson);
            $params['DTIDJson'] = htmlspecialchars_decode($params['DTIDJson']);

            $DTIDJson_arr = json_decode($params['DTIDJson'], true);


            foreach ($DTIDJson_arr as $k1 => $v1) {
                if ($v1['LineTitle']) {
                    //iconv("GBK", "UTF-8",base64_decode($v1['LineTitle']))
                    $v1['LineTitle']=base64_encode(iconv("UTF-8","GBK",$v1['LineTitle']));
                    $DTIDJson_arr[$k1]['LineTitle'] =$v1['LineTitle'];
                    //$DTIDJson_arr[$k1]['LineTitle'] = str_replace(' ', '+', $v1['LineTitle']);
                }
                if ($v1['unitstring']) {
                    $v1['unitstring']=base64_encode(iconv("UTF-8","GBK",$v1['unitstring']));
                    $DTIDJson_arr[$k1]['unitstring'] =$v1['unitstring'];
                    //$DTIDJson_arr[$k1]['unitstring'] = str_replace(' ', '+', $v1['unitstring']);
                }
            }

            $params['DTIDJson'] = json_encode($DTIDJson_arr);

            if ($params['DTIDJson'] == '""' || $params['DTIDJson'] == "null" || empty($params['DTIDJson'])) {
                $params['DTIDJson'] = '';
            }


            $ChartExtLineStyle = html_entity_decode(Urldecode($params['ChartExtLineStyle']));

            $params['ChartExtLineStyle'] = htmlspecialchars_decode($params['ChartExtLineStyle']);

            $ChartExtLineStyle_arr = json_decode($params['ChartExtLineStyle'], true);
            foreach ($ChartExtLineStyle_arr as $key => $val) {
                if ($val['linestyletype']) {
                    $ChartExtLineStyle_arr[$key]['linestyletype'] = str_replace(' ', '+', $val['linestyletype']);
                }

            }

            $params['ChartExtLineStyle'] = json_encode($ChartExtLineStyle_arr);

            if ($params['ChartExtLineStyle'] == '""' || $params['ChartExtLineStyle'] == "null" || empty($params['ChartExtLineStyle'])) {
                $params['ChartExtLineStyle'] = '';
            }


            $data = $this->getdata($array, $params);

            if ($params['ImageTitle']) {
                $imagetitle = $params['ImageTitle'];
            } else {
                $imagetitle = $params['dtname'];

                //$imagetitle =iconv("UTF-8", "GB2312//IGNORE", $params['dtname']);
            }

            if ($params['dtnameshort']) {
                $dtnameshort = $params['dtnameshort'];
            } else {
                $dtnameshort = $this->cut_str($params['dtname'], 9);
            }
            $maxsort = $this->_dao->getOne("select max(aod) from dc_search_custom where MID='$mid' and Uid ='$uid'");
            $maxsort++;//保存时，让sort增加，然后插入表格，操作量不大时，sort值一般不会重复
            /*2017/07/20 change */
            $ctime = date("Y-m-d H:i:s");
            // echo "insert into dc_search_custom set $data,MID='".$mid."',Uid ='$uid',aod='$maxsort',dtnameshort='".$dtnameshort."',mc_type='".$mc_type."'";exit;
            $this->_dao->execute("insert into dc_search_custom set $data,MID='" . $mid . "',Uid ='$uid',aod='$maxsort',dtnameshort='" . $dtnameshort . "',mc_type='" . $mc_type . "',createtime='" . $ctime . "',updatetime='" . $ctime . "',IsEnglish='" . $isEnglish . "'");
            // echo "insert into dc_search_custom set $data,MID='".$mid."',Uid ='$uid',aod='$maxsort',dtnameshort='".$dtnameshort."',mc_type='".$mc_type."'";
            $this->_dao->WriteLog($mid, $uid, $SignCS, $action, $actionstr, $this->getIP(), $this->getSystemType(), $this->getSystemVersion(), "自定义图表保存", '', '', $mc_type);
            if (!empty($params['DTID1'])) {
                $DTID1Name = $this->_dao->getone("SELECT dtname FROM `dc_code_datatype` WHERE `ID`='" . $params['DTID1'] . "'");
            }
            if (!empty($params['DTID2'])) {
                $DTID2Name = $this->_dao->getone("SELECT dtname FROM `dc_code_datatype` WHERE `ID`='" . $params['DTID2'] . "'");
            }
            if (!empty($params['DTID3'])) {
                $DTID3Name = $this->_dao->getone("SELECT dtname FROM `dc_code_datatype` WHERE `ID`='" . $params['DTID3'] . "'");
            }
            if (!empty($params['DTID4'])) {
                $DTID4Name = $this->_dao->getone("SELECT dtname FROM `dc_code_datatype` WHERE `ID`='" . $params['DTID4'] . "'");
            }
            if (!empty($params['DTID1Sub'])) {
                $DTID1SubName = $this->_dao->getone("SELECT sname  FROM `dc_code_datatype_subs` WHERE `ID`='" . $params['DTID1Sub'] . "'");
            }
            if (!empty($params['DTID2Sub'])) {
                $DTID2SubName = $this->_dao->getone("SELECT sname  FROM `dc_code_datatype_subs` WHERE `ID`='" . $params['DTID2Sub'] . "'");
            }
            if (!empty($params['DTID3Sub'])) {
                $DTID3SubName = $this->_dao->getone("SELECT sname  FROM `dc_code_datatype_subs` WHERE `ID`='" . $params['DTID3Sub'] . "'");
            }
            if (!empty($params['DTID4Sub'])) {
                $DTID4SubName = $this->_dao->getone("SELECT sname  FROM `dc_code_datatype_subs` WHERE `ID`='" . $params['DTID4Sub'] . "'");
            }

            $this->_dao->WriteMemberLog($GUID, $SignCS, $params['DTID1'], $DTID1Name, $params['DTID1Sub'], $DTID1SubName, $params['Data1Type'], $params['DTID2'], $DTID2Name, $params['DTID2Sub'], $DTID2SubName, $params['Data2Type'], $params['DTID3'], $DTID3Name, $params['DTID3Sub'], $DTID3SubName, $params['Data3Type'], $params['DTID4'], $DTID4Name, $params['DTID4Sub'], $DTID4SubName, $params['Data4Type'], $params['ImageType'],
                $params['DateStart'], $params['DateEnd'], $mid, $user['ComName'], "自定义图表保存", $params["mode"], $mc_type, $action, $this->getIP(), $actionstr, $params['GongshiID1'], $params['GongshiID2'], $params['GongshiID3'], $params['GongshiID4'], $DTIDJson);

            $arr['Success'] = 1;
            $arr['Message'] = "成功";
            $arr['Results'] = "";
            if ($isEnglish) $arr['Message'] = "Failed!";
            $json_string = $this->pri_JSON($arr);
            echo $json_string;
        }
    }

    //获取自定义搜索
    public function SearchListGet($params)
    {
        if ($this->checkGUID($params)) {

            $GUID = $params['GUID'];
            $SignCS = $params['SignCS'];
            $mc_type = $params['mc_type'];
            $action = $params['action'];        //接口名称
            $ip = $this->getIP();
            if (!empty($params['mc_type'])) $mc_type = $params['mc_type'];
            else $mc_type = 0;
            if (!empty($params['isEnglish'])) $isEnglish = $params['isEnglish'];
            else $isEnglish = 0;

            $arr = array(
                'Success' => '0',
                'Message' => '失败',
                'Results' => 'null',
            );
            if ($isEnglish) $arr['Message'] = 'Failed!';

            if (array_key_exists($GUID, $GLOBALS["testaccountarr"]) && $SignCS != '' && (in_array($ip, $GLOBALS["testip"]) || $GLOBALS["nowtest"] == 1)) {
                $user = $GLOBALS["testaccountuser"];
            } else {
                $user = $this->_dao->getUser($GUID, $SignCS, $mc_type);
            }
            $mid = $user['Mid'];
            $uid = $user['Uid'];

            $arrtem = $params;
            //array_pop($arrtem);
            $actionstr = "";
            foreach ($arrtem as $k => $v) {
                $actionstr .= "&" . $k . "=" . $v;
            }
            /*2017/07/20 change */
            $selectsql = "select ID,dtname,dtnameshort,ImageType,
			  DTID1 ,
			  DTID1Sub,
			  Data1Type,
			  Data1Type1,
			  Data1Image,
			  Data1Pre,
			  Data1AddSub,
			  Data1AddSubHundCore,
			  Data1Type2,
			  Data1Type3,
			  Data1Type4,
			  Data1Type5 ,
			  DTID2 ,
			  DTID2Sub,
			  Data2Type ,Data2Image,  
			  DTID3 ,
			  DTID3Sub ,
			  Data3Type , Data3Image,
			  DTID4 ,
			  DTID4Sub ,
			  Data4Type , Data4Image,
			  zhou1,zhou2,zhou3,zhou4,
			  isjz,isbg,isfg,color,aod,
			  LineTitle1,LineTitle2,LineTitle3,LineTitle4,
			  GongshiID1,GongshiID2,GongshiID3,GongshiID4
			   from dc_search_custom where IsEnglish!=1 and MID = '" . $mid . "' and Uid = '$uid' and mc_type = '$mc_type' order by binary(dtname) asc,aod desc,ID desc";//echo $selectsql;
            $sear = $this->_dao->query($selectsql);

            function pinyin_sort($a, $b)
            {
                $ret = 1;
                //echo $a['dtname']."&".$b['dtname']."::";
                $str_arr = array($a['dtname'], $b['dtname']);
                $py_arr = getPinyin($str_arr);
                $py_arr2 = $py_arr;
                sort($py_arr);
                //print_r($py_arr[0]);echo ":";print_r($py_arr2[0]);
                if ($py_arr[0] === $py_arr2[0]) $ret = -1;
                else $ret = 1;
                //echo "=>".$ret."<br>";
                return $ret;
            }

            function getPinyin($resource, $pix = ' ')
            {
                static $_TDataKey, $_TDataValue;
                $_DataKey = "a|ai|an|ang|ao|ba|bai|ban|bang|bao|bei|ben|beng|bi|bian|biao|bie|bin|bing|bo|bu|ca|cai|can|cang|cao|ce|ceng|cha" . "|chai|chan|chang|chao|che|chen|cheng|chi|chong|chou|chu|chuai|chuan|chuang|chui|chun|chuo|ci|cong|cou|cu|" . "cuan|cui|cun|cuo|da|dai|dan|dang|dao|de|deng|di|dian|diao|die|ding|diu|dong|dou|du|duan|dui|dun|duo|e|en|er" . "|fa|fan|fang|fei|fen|feng|fo|fou|fu|ga|gai|gan|gang|gao|ge|gei|gen|geng|gong|gou|gu|gua|guai|guan|guang|gui" . "|gun|guo|ha|hai|han|hang|hao|he|hei|hen|heng|hong|hou|hu|hua|huai|huan|huang|hui|hun|huo|ji|jia|jian|jiang" . "|jiao|jie|jin|jing|jiong|jiu|ju|juan|jue|jun|ka|kai|kan|kang|kao|ke|ken|keng|kong|kou|ku|kua|kuai|kuan|kuang" . "|kui|kun|kuo|la|lai|lan|lang|lao|le|lei|leng|li|lia|lian|liang|liao|lie|lin|ling|liu|long|lou|lu|lv|luan|lue" . "|lun|luo|ma|mai|man|mang|mao|me|mei|men|meng|mi|mian|miao|mie|min|ming|miu|mo|mou|mu|na|nai|nan|nang|nao|ne" . "|nei|nen|neng|ni|nian|niang|niao|nie|nin|ning|niu|nong|nu|nv|nuan|nue|nuo|o|ou|pa|pai|pan|pang|pao|pei|pen" . "|peng|pi|pian|piao|pie|pin|ping|po|pu|qi|qia|qian|qiang|qiao|qie|qin|qing|qiong|qiu|qu|quan|que|qun|ran|rang" . "|rao|re|ren|reng|ri|rong|rou|ru|ruan|rui|run|ruo|sa|sai|san|sang|sao|se|sen|seng|sha|shai|shan|shang|shao|" . "she|shen|sheng|shi|shou|shu|shua|shuai|shuan|shuang|shui|shun|shuo|si|song|sou|su|suan|sui|sun|suo|ta|tai|" . "tan|tang|tao|te|teng|ti|tian|tiao|tie|ting|tong|tou|tu|tuan|tui|tun|tuo|wa|wai|wan|wang|wei|wen|weng|wo|wu" . "|xi|xia|xian|xiang|xiao|xie|xin|xing|xiong|xiu|xu|xuan|xue|xun|ya|yan|yang|yao|ye|yi|yin|ying|yo|yong|you" . "|yu|yuan|yue|yun|za|zai|zan|zang|zao|ze|zei|zen|zeng|zha|zhai|zhan|zhang|zhao|zhe|zhen|zheng|zhi|zhong|" . "zhou|zhu|zhua|zhuai|zhuan|zhuang|zhui|zhun|zhuo|zi|zong|zou|zu|zuan|zui|zun|zuo";

                $_DataValue = "-20319|-20317|-20304|-20295|-20292|-20283|-20265|-20257|-20242|-20230|-20051|-20036|-20032|-20026|-20002|-19990" . "|-19986|-19982|-19976|-19805|-19784|-19775|-19774|-19763|-19756|-19751|-19746|-19741|-19739|-19728|-19725" . "|-19715|-19540|-19531|-19525|-19515|-19500|-19484|-19479|-19467|-19289|-19288|-19281|-19275|-19270|-19263" . "|-19261|-19249|-19243|-19242|-19238|-19235|-19227|-19224|-19218|-19212|-19038|-19023|-19018|-19006|-19003" . "|-18996|-18977|-18961|-18952|-18783|-18774|-18773|-18763|-18756|-18741|-18735|-18731|-18722|-18710|-18697" . "|-18696|-18526|-18518|-18501|-18490|-18478|-18463|-18448|-18447|-18446|-18239|-18237|-18231|-18220|-18211" . "|-18201|-18184|-18183|-18181|-18012|-17997|-17988|-17970|-17964|-17961|-17950|-17947|-17931|-17928|-17922" . "|-17759|-17752|-17733|-17730|-17721|-17703|-17701|-17697|-17692|-17683|-17676|-17496|-17487|-17482|-17468" . "|-17454|-17433|-17427|-17417|-17202|-17185|-16983|-16970|-16942|-16915|-16733|-16708|-16706|-16689|-16664" . "|-16657|-16647|-16474|-16470|-16465|-16459|-16452|-16448|-16433|-16429|-16427|-16423|-16419|-16412|-16407" . "|-16403|-16401|-16393|-16220|-16216|-16212|-16205|-16202|-16187|-16180|-16171|-16169|-16158|-16155|-15959" . "|-15958|-15944|-15933|-15920|-15915|-15903|-15889|-15878|-15707|-15701|-15681|-15667|-15661|-15659|-15652" . "|-15640|-15631|-15625|-15454|-15448|-15436|-15435|-15419|-15416|-15408|-15394|-15385|-15377|-15375|-15369" . "|-15363|-15362|-15183|-15180|-15165|-15158|-15153|-15150|-15149|-15144|-15143|-15141|-15140|-15139|-15128" . "|-15121|-15119|-15117|-15110|-15109|-14941|-14937|-14933|-14930|-14929|-14928|-14926|-14922|-14921|-14914" . "|-14908|-14902|-14894|-14889|-14882|-14873|-14871|-14857|-14678|-14674|-14670|-14668|-14663|-14654|-14645" . "|-14630|-14594|-14429|-14407|-14399|-14384|-14379|-14368|-14355|-14353|-14345|-14170|-14159|-14151|-14149" . "|-14145|-14140|-14137|-14135|-14125|-14123|-14122|-14112|-14109|-14099|-14097|-14094|-14092|-14090|-14087" . "|-14083|-13917|-13914|-13910|-13907|-13906|-13905|-13896|-13894|-13878|-13870|-13859|-13847|-13831|-13658" . "|-13611|-13601|-13406|-13404|-13400|-13398|-13395|-13391|-13387|-13383|-13367|-13359|-13356|-13343|-13340" . "|-13329|-13326|-13318|-13147|-13138|-13120|-13107|-13096|-13095|-13091|-13076|-13068|-13063|-13060|-12888" . "|-12875|-12871|-12860|-12858|-12852|-12849|-12838|-12831|-12829|-12812|-12802|-12607|-12597|-12594|-12585" . "|-12556|-12359|-12346|-12320|-12300|-12120|-12099|-12089|-12074|-12067|-12058|-12039|-11867|-11861|-11847" . "|-11831|-11798|-11781|-11604|-11589|-11536|-11358|-11340|-11339|-11324|-11303|-11097|-11077|-11067|-11055" . "|-11052|-11045|-11041|-11038|-11024|-11020|-11019|-11018|-11014|-10838|-10832|-10815|-10800|-10790|-10780" . "|-10764|-10587|-10544|-10533|-10519|-10331|-10329|-10328|-10322|-10315|-10309|-10307|-10296|-10281|-10274" . "|-10270|-10262|-10260|-10256|-10254";
                if (empty($_TDataKey)) $_TDataKey = explode('|', $_DataKey);
                if (empty($_TDataValue)) $_TDataValue = explode('|', $_DataValue);
                $data = array_combine($_TDataKey, $_TDataValue);
                //print_r($data);exit;
                arsort($data);
                reset($data);
                if (!is_array($resource)) {
                    $resource = array($resource);
                }
                $_Res = array();
                foreach ($resource as $id => $str) {
                    $_Res[$id] = '';
                    for ($i = 0; $i < strlen($str); $i++) {
                        $_P = ord(substr($str, $i, 1));
                        if ($_P > 160) {
                            $_Q = ord(substr($str, ++$i, 1));
                            $_P = $_P * 256 + $_Q - 65536;
                        }
                        if ($_P > 0 && $_P < 160) {
                            $sign = chr($_P);
                        } elseif ($_P < -20319 || $_P > -10247) {
                            $sign = '';
                        } else {
                            foreach ($data as $k => $v) {
                                if ($v <= $_P)
                                    break;
                            }
                            $sign = $k;
                            //echo $sign."<br>";
                        }
                        //$sign=$this->_Pinyin ( $_P, $data );
                        if ($sign == $pix) $_Res[$id] .= $sign;
                        else $_Res[$id] .= $sign . $pix;
                    }
                }
                return $_Res;
            }

            //if($mc_type=='1')
            usort($sear, "pinyin_sort");
            //print"<pre>";print_r($sear);exit;
            foreach ($sear as &$tmp) {
                $tmp['dtname'] = base64_encode($tmp['dtname']);
                $tmp['dtnameshort'] = base64_encode($tmp['dtnameshort']);
                for ($i = 1; $i <= 4; $i++) {
                    if ($tmp['GongshiID' . $i]) {
                        $gsname = $this->_dao->getone("select Name from dc_formula_custom where id='" . $tmp['GongshiID' . $i] . "' limit 1");
                        $tmp['GSName' . $i] = base64_encode($gsname);
                    } else {
                        $tmp['GSName' . $i] = "";
                    }
                }
            }

            $this->_dao->WriteLog($mid, $uid, $SignCS, $action, $actionstr, $this->getIP(), $this->getSystemType(), $this->getSystemVersion(), "获取自定义搜索", '', '', $mc_type);

            $arr['Success'] = 1;
            $arr['Message'] = ("成功");
            $arr['Results'] = $sear;

            if ($isEnglish) $arr['Message'] = ("Success");

            $json_string = $this->pri_JSON($arr);
            echo $json_string;
            return false;

        }

    }

    //获取自定义搜索2
    public function SearchListGet3($params)
    {

        if ($this->checkGUID($params)) {

            $GUID = $params['GUID'];
            $SignCS = $params['SignCS'];
            $mc_type = $params['mc_type'];
            $action = $params['action'];        //接口名称
            $ip = $this->getIP();
            if (!empty($params['mc_type'])) $mc_type = $params['mc_type'];
            else $mc_type = 0;
            if (!empty($params['isEnglish'])) $isEnglish = $params['isEnglish'];
            else $isEnglish = 0;


            $arr = array(
                'Success' => '0',
                'Message' => '失败',
                'Results' => 'null',
            );
            if ($isEnglish) $arr['Message'] = 'Failed!';

            if (array_key_exists($GUID, $GLOBALS["testaccountarr"]) && $SignCS != '' && (in_array($ip, $GLOBALS["testip"]) || $GLOBALS["nowtest"] == 1)) {
                $user = $GLOBALS["testaccountuser"];
            } else {
                $user = $this->_dao->getUser($GUID, $SignCS, $mc_type);
            }
            $mid = $user['Mid'];
            $uid = $user['Uid'];

            //$mid = 1;
            //$uid = 445131;
            $arrtem = $params;
            //array_pop($arrtem);
            $actionstr = "";
            foreach ($arrtem as $k => $v) {
                $actionstr .= "&" . $k . "=" . $v;
            }
            /*2017/07/20 change */
            $selectsql = "select ID,dtname,dtnameshort,ImageType,
			  DTID1 ,
			  DTID1Sub,
			  Data1Type,
			  Data1Type1,
			  Data1Image,
			  Data1Pre,
			  Data1AddSub,
			  Data1AddSubHundCore,
			  Data1Type2,
			  Data1Type3,
			  Data1Type4,
			  Data1Type5 ,
			  DTID2 ,
			  DTID2Sub,
			  Data2Type ,Data2Image,
			  DTID3 ,
			  DTID3Sub ,
			  Data3Type , Data3Image,
			  DTID4 ,
			  DTID4Sub ,
			  Data4Type , Data4Image,
			  zhou1,zhou2,zhou3,zhou4,
			  isjz,isbg,isfg,color,aod,
			  LineTitle1,LineTitle2,LineTitle3,LineTitle4,
			  GongshiID1,GongshiID2,GongshiID3,GongshiID4,catalogueid
			   from dc_search_custom where IsEnglish!=1 and MID = '" . $mid . "' and Uid = '$uid' and mc_type = '$mc_type' order by binary(dtname) asc,aod desc,ID desc";//echo $selectsql;
            $sear = $this->_dao->query($selectsql);


            function pinyin_sort($a, $b)
            {
                $ret = 1;
                //echo $a['dtname']."&".$b['dtname']."::";
                $str_arr = array($a['dtname'], $b['dtname']);
                $py_arr = getPinyin($str_arr);
                $py_arr2 = $py_arr;
                sort($py_arr);
                //print_r($py_arr[0]);echo ":";print_r($py_arr2[0]);
                if ($py_arr[0] === $py_arr2[0]) $ret = -1;
                else $ret = 1;
                //echo "=>".$ret."<br>";
                return $ret;
            }

            function getPinyin($resource, $pix = ' ')
            {
                static $_TDataKey, $_TDataValue;
                $_DataKey = "a|ai|an|ang|ao|ba|bai|ban|bang|bao|bei|ben|beng|bi|bian|biao|bie|bin|bing|bo|bu|ca|cai|can|cang|cao|ce|ceng|cha" . "|chai|chan|chang|chao|che|chen|cheng|chi|chong|chou|chu|chuai|chuan|chuang|chui|chun|chuo|ci|cong|cou|cu|" . "cuan|cui|cun|cuo|da|dai|dan|dang|dao|de|deng|di|dian|diao|die|ding|diu|dong|dou|du|duan|dui|dun|duo|e|en|er" . "|fa|fan|fang|fei|fen|feng|fo|fou|fu|ga|gai|gan|gang|gao|ge|gei|gen|geng|gong|gou|gu|gua|guai|guan|guang|gui" . "|gun|guo|ha|hai|han|hang|hao|he|hei|hen|heng|hong|hou|hu|hua|huai|huan|huang|hui|hun|huo|ji|jia|jian|jiang" . "|jiao|jie|jin|jing|jiong|jiu|ju|juan|jue|jun|ka|kai|kan|kang|kao|ke|ken|keng|kong|kou|ku|kua|kuai|kuan|kuang" . "|kui|kun|kuo|la|lai|lan|lang|lao|le|lei|leng|li|lia|lian|liang|liao|lie|lin|ling|liu|long|lou|lu|lv|luan|lue" . "|lun|luo|ma|mai|man|mang|mao|me|mei|men|meng|mi|mian|miao|mie|min|ming|miu|mo|mou|mu|na|nai|nan|nang|nao|ne" . "|nei|nen|neng|ni|nian|niang|niao|nie|nin|ning|niu|nong|nu|nv|nuan|nue|nuo|o|ou|pa|pai|pan|pang|pao|pei|pen" . "|peng|pi|pian|piao|pie|pin|ping|po|pu|qi|qia|qian|qiang|qiao|qie|qin|qing|qiong|qiu|qu|quan|que|qun|ran|rang" . "|rao|re|ren|reng|ri|rong|rou|ru|ruan|rui|run|ruo|sa|sai|san|sang|sao|se|sen|seng|sha|shai|shan|shang|shao|" . "she|shen|sheng|shi|shou|shu|shua|shuai|shuan|shuang|shui|shun|shuo|si|song|sou|su|suan|sui|sun|suo|ta|tai|" . "tan|tang|tao|te|teng|ti|tian|tiao|tie|ting|tong|tou|tu|tuan|tui|tun|tuo|wa|wai|wan|wang|wei|wen|weng|wo|wu" . "|xi|xia|xian|xiang|xiao|xie|xin|xing|xiong|xiu|xu|xuan|xue|xun|ya|yan|yang|yao|ye|yi|yin|ying|yo|yong|you" . "|yu|yuan|yue|yun|za|zai|zan|zang|zao|ze|zei|zen|zeng|zha|zhai|zhan|zhang|zhao|zhe|zhen|zheng|zhi|zhong|" . "zhou|zhu|zhua|zhuai|zhuan|zhuang|zhui|zhun|zhuo|zi|zong|zou|zu|zuan|zui|zun|zuo";

                $_DataValue = "-20319|-20317|-20304|-20295|-20292|-20283|-20265|-20257|-20242|-20230|-20051|-20036|-20032|-20026|-20002|-19990" . "|-19986|-19982|-19976|-19805|-19784|-19775|-19774|-19763|-19756|-19751|-19746|-19741|-19739|-19728|-19725" . "|-19715|-19540|-19531|-19525|-19515|-19500|-19484|-19479|-19467|-19289|-19288|-19281|-19275|-19270|-19263" . "|-19261|-19249|-19243|-19242|-19238|-19235|-19227|-19224|-19218|-19212|-19038|-19023|-19018|-19006|-19003" . "|-18996|-18977|-18961|-18952|-18783|-18774|-18773|-18763|-18756|-18741|-18735|-18731|-18722|-18710|-18697" . "|-18696|-18526|-18518|-18501|-18490|-18478|-18463|-18448|-18447|-18446|-18239|-18237|-18231|-18220|-18211" . "|-18201|-18184|-18183|-18181|-18012|-17997|-17988|-17970|-17964|-17961|-17950|-17947|-17931|-17928|-17922" . "|-17759|-17752|-17733|-17730|-17721|-17703|-17701|-17697|-17692|-17683|-17676|-17496|-17487|-17482|-17468" . "|-17454|-17433|-17427|-17417|-17202|-17185|-16983|-16970|-16942|-16915|-16733|-16708|-16706|-16689|-16664" . "|-16657|-16647|-16474|-16470|-16465|-16459|-16452|-16448|-16433|-16429|-16427|-16423|-16419|-16412|-16407" . "|-16403|-16401|-16393|-16220|-16216|-16212|-16205|-16202|-16187|-16180|-16171|-16169|-16158|-16155|-15959" . "|-15958|-15944|-15933|-15920|-15915|-15903|-15889|-15878|-15707|-15701|-15681|-15667|-15661|-15659|-15652" . "|-15640|-15631|-15625|-15454|-15448|-15436|-15435|-15419|-15416|-15408|-15394|-15385|-15377|-15375|-15369" . "|-15363|-15362|-15183|-15180|-15165|-15158|-15153|-15150|-15149|-15144|-15143|-15141|-15140|-15139|-15128" . "|-15121|-15119|-15117|-15110|-15109|-14941|-14937|-14933|-14930|-14929|-14928|-14926|-14922|-14921|-14914" . "|-14908|-14902|-14894|-14889|-14882|-14873|-14871|-14857|-14678|-14674|-14670|-14668|-14663|-14654|-14645" . "|-14630|-14594|-14429|-14407|-14399|-14384|-14379|-14368|-14355|-14353|-14345|-14170|-14159|-14151|-14149" . "|-14145|-14140|-14137|-14135|-14125|-14123|-14122|-14112|-14109|-14099|-14097|-14094|-14092|-14090|-14087" . "|-14083|-13917|-13914|-13910|-13907|-13906|-13905|-13896|-13894|-13878|-13870|-13859|-13847|-13831|-13658" . "|-13611|-13601|-13406|-13404|-13400|-13398|-13395|-13391|-13387|-13383|-13367|-13359|-13356|-13343|-13340" . "|-13329|-13326|-13318|-13147|-13138|-13120|-13107|-13096|-13095|-13091|-13076|-13068|-13063|-13060|-12888" . "|-12875|-12871|-12860|-12858|-12852|-12849|-12838|-12831|-12829|-12812|-12802|-12607|-12597|-12594|-12585" . "|-12556|-12359|-12346|-12320|-12300|-12120|-12099|-12089|-12074|-12067|-12058|-12039|-11867|-11861|-11847" . "|-11831|-11798|-11781|-11604|-11589|-11536|-11358|-11340|-11339|-11324|-11303|-11097|-11077|-11067|-11055" . "|-11052|-11045|-11041|-11038|-11024|-11020|-11019|-11018|-11014|-10838|-10832|-10815|-10800|-10790|-10780" . "|-10764|-10587|-10544|-10533|-10519|-10331|-10329|-10328|-10322|-10315|-10309|-10307|-10296|-10281|-10274" . "|-10270|-10262|-10260|-10256|-10254";
                if (empty($_TDataKey)) $_TDataKey = explode('|', $_DataKey);
                if (empty($_TDataValue)) $_TDataValue = explode('|', $_DataValue);
                $data = array_combine($_TDataKey, $_TDataValue);
                //print_r($data);exit;
                arsort($data);
                reset($data);
                if (!is_array($resource)) {
                    $resource = array($resource);
                }
                $_Res = array();
                foreach ($resource as $id => $str) {
                    $_Res[$id] = '';
                    for ($i = 0; $i < strlen($str); $i++) {
                        $_P = ord(substr($str, $i, 1));
                        if ($_P > 160) {
                            $_Q = ord(substr($str, ++$i, 1));
                            $_P = $_P * 256 + $_Q - 65536;
                        }
                        if ($_P > 0 && $_P < 160) {
                            $sign = chr($_P);
                        } elseif ($_P < -20319 || $_P > -10247) {
                            $sign = '';
                        } else {
                            foreach ($data as $k => $v) {
                                if ($v <= $_P)
                                    break;
                            }
                            $sign = $k;
                            //echo $sign."<br>";
                        }
                        //$sign=$this->_Pinyin ( $_P, $data );
                        if ($sign == $pix) $_Res[$id] .= $sign;
                        else $_Res[$id] .= $sign . $pix;
                    }
                }
                return $_Res;
            }

            //if($mc_type=='1')

            sort($sear, "pinyin_sort");

            foreach ($sear as &$tmp) {
                //$tmp['dtname'] = base64_encode( $this->utf8ToGb2312( $tmp['dtname'] ) );
                //$tmp['dtnameshort'] = base64_encode( $this->utf8ToGb2312( $tmp['dtnameshort'] ));
                for ($i = 1; $i <= 4; $i++) {
                    if ($tmp['GongshiID' . $i]) {
                        $gsname = $this->_dao->getone("select Name from dc_formula_custom where id='" . $tmp['GongshiID' . $i] . "' limit 1");
                        $tmp['GSName' . $i] =  $gsname;
                    } else {
                        $tmp['GSName' . $i] = "";
                    }
                }
            }


            //我的数据
            $sear_arr['ID'] = 0;
            $sear_arr['parentid'] = 0;
            $sear_arr['cataloguename'] = "我的数据";
            if ($isEnglish) $sear_arr['cataloguename'] = "My Data";
            $sear_arr['status'] = '';
            $sear_arr['aod'] = '';

            $iscatalogue = array();
            $isnotcatalogue = array();
            foreach ($sear as $k => $v) {
                if (empty($v['catalogueid'])) {
                    $isnotcatalogue[$k] = $v;
                } else {
                    $iscatalogue[$k] = $v;
                }

            }
            if (!empty($isnotcatalogue)) {
                $sear_arr['hasleaves'] = 1;
                usort($isnotcatalogue, "pinyin_sort");
                $sear_arr['leaves'] = $isnotcatalogue;
            } else {
                $sear_arr['hasleaves'] = 0;
                $sear_arr['leaves'] = '';
            }

            $iscatalogue_arr = array();
            if (!empty($iscatalogue)) {

                foreach ($iscatalogue as $key => $val) {
                    if ( isset($val['catalogueid']) && $val['catalogueid'] )
                        $iscatalogue_arr[$val['catalogueid']][] = $val;
                }
                //getcataloguearr所有有目录关系的进行处理，key=catalogueid；
                $parentidarr = $this->_dao->query("select ID,parentid  from `dc_search_custom_catalogue`  where mc_type='" . $mc_type . "' and MID='" . $mid . "' and Uid='" . $uid . "'");
                foreach ($parentidarr as $val) {
                    $parentidmenu[$val['parentid']][] = $val['ID'];
                }

                $row = $this->_dao->query("select *  from `dc_search_custom_catalogue` where mc_type='" . $mc_type . "' and MID='" . $mid . "' and Uid='" . $uid . "'");
                foreach ($row as $val) {
                    $rowarr[$val['ID']] = $val;
                }

                //$iscatalogue_arr2=$this->getcataloguearr($iscatalogue_arr,$parentid,$parentidmenu,$rowarr);
                //如果此处hasnode返回1，但是$iscatalogue_arr2没有数据，$parentidarr，$row的查询就没有查正确，可能目录信息录入有错
                $sear_arr['hasnode'] = 1;
                //$sear_arr['SubResults'] = $iscatalogue_arr2;
                $sear_arr['SubResults'] = "";
            } else {
                $sear_arr['hasnode'] = 0;
                $sear_arr['SubResults'] = '';
            }


            //$this->_dao->WriteLog($mid, $uid, $SignCS, $action,$actionstr, $this->getIP(), $this->getSystemType(), $this->getSystemVersion(),"获取自定义搜索2",'','',$mc_type);

            $arr['Success'] = 1;
            $arr['Message'] = ("成功");
            $arr['Results'] = $sear_arr;

            if ($isEnglish) $arr['Message'] = ("Success");

            $json_string = $this->pri_JSON($arr);
            //echo $json_string;
            $needCompress = $params['needCompress'];  //判断是否封装
            if ($needCompress == 1 && empty($params['is_search'])) {

                $a = '{"ID":"0","parentid":"0","cataloguename":"我的数据","status":"1","aod":"1","hasleaves":"1","updatetime":"","leaves":[{"ID":"2435","dtname":"1f3KvbDmo6jO8Mm+o6k=","dtnameshort":"1f3KvbDmo6jO8Mm+o6k=","ImageType":"2","DTID1":"1885","DTID1Sub":"0","Data1Type":"1","Data1Type1":"1","Data1Image":"2","Data1Pre":"0","Data1AddSub":"0","Data1AddSubHundCore":"","Data1Type2":"0","Data1Type3":"0","Data1Type4":"0","Data1Type5":"0","DTID2":"709","DTID2Sub":"0","Data2Type":"1","Data2Image":"2","DTID3":"0","DTID3Sub":"0","Data3Type":"0","Data3Image":"0","DTID4":"0","DTID4Sub":"0","Data4Type":"0","Data4Image":"0","zhou1":"0","zhou2":"0","zhou3":"0","zhou4":"0","isjz":"0","isbg":"0","isfg":"0","color":"0","aod":"12","LineTitle1":"","LineTitle2":"","LineTitle3":"","LineTitle4":"","GongshiID1":"MA==","GongshiID2":"0","GongshiID3":"0","GongshiID4":"0","GSName1":"","GSName2":"","GSName3":"","GSName4":"","updatetime":"0000-00-00 00:00:00"},{"ID":"2397","dtname":"tbHUwg==","dtnameshort":"tbHUwg==","ImageType":"2","DTID1":"709","DTID1Sub":"0","Data1Type":"1","Data1Type1":"1","Data1Image":"2","Data1Pre":"0","Data1AddSub":"0","Data1AddSubHundCore":"","Data1Type2":"0","Data1Type3":"0","Data1Type4":"0","Data1Type5":"0","DTID2":"1210","DTID2Sub":"0","Data2Type":"1","Data2Image":"1","DTID3":"1483","DTID3Sub":"0","Data3Type":"1","Data3Image":"1","DTID4":"1218","DTID4Sub":"0","Data4Type":"1","Data4Image":"1","zhou1":"0","zhou2":"0","zhou3":"0","zhou4":"0","isjz":"0","isbg":"0","isfg":"0","color":"0","aod":"6","LineTitle1":"","LineTitle2":"","LineTitle3":"","LineTitle4":"","GongshiID1":"MA==","GongshiID2":"0","GongshiID3":"0","GongshiID4":"0","GSName1":"","GSName2":"","GSName3":"","GSName4":"","updatetime":"2019-08-01 00:00:00"}],"hasnode":1,"SubResults":[{"ID":"2","parentid":"1","cataloguename":"xL\/CvDI=","status":1,"aod":"41","hasleaves":"1","updatetime":"2019-07-30 08:31:02","leaves":[{"ID":"3836","dtname":"ufrE2tb30qrK0LOhwba9ucO6vNu48dDQx+my4srUyv2+3bChtcS3orK8tdi3vQ==","dtnameshort":"ufrE2tb30qrK0LOhwba9ucO6","ImageType":"2","DTID1":"6345","DTID1Sub":"0","Data1Type":"1","Data1Type1":"1","Data1Image":"2","Data1Pre":"0","Data1AddSub":"0","Data1AddSubHundCore":"0","Data1Type2":"0","Data1Type3":"0","Data1Type4":"0","Data1Type5":"0","DTID2":"6320","DTID2Sub":"0","Data2Type":"1","Data2Image":"2","DTID3":"6349","DTID3Sub":"0","Data3Type":"1","Data3Image":"2","DTID4":"0","DTID4Sub":"0","Data4Type":"0","Data4Image":"0","zhou1":"0","zhou2":"0","zhou3":"0","zhou4":"0","isjz":"0","isbg":"0","isfg":"0","color":"0","aod":"33","LineTitle1":"","LineTitle2":"","LineTitle3":"","LineTitle4":"","GongshiID1":"MA==","GongshiID2":"0","GongshiID3":"0","GongshiID4":"0","GSName1":"","GSName2":"","GSName3":"","GSName4":"","updatetime":"0000-00-00 00:00:00"},{"ID":"3827","dtname":"ufrE2tb30qrK0LOhyMjU\/rDlvu2827jx","dtnameshort":"ufrE2tb30qrK0LOhyMjU\/rDl","ImageType":"2","DTID1":"34388","DTID1Sub":"0","Data1Type":"1","Data1Type1":"0","Data1Image":"3","Data1Pre":"0","Data1AddSub":"0","Data1AddSubHundCore":"0","Data1Type2":"0","Data1Type3":"0","Data1Type4":"0","Data1Type5":"0","DTID2":"36007","DTID2Sub":"0","Data2Type":"1","Data2Image":"6","DTID3":"35382","DTID3Sub":"0","Data3Type":"1","Data3Image":"2","DTID4":"0","DTID4Sub":"0","Data4Type":"0","Data4Image":"0","zhou1":"0","zhou2":"0","zhou3":"0","zhou4":"0","isjz":"0","isbg":"0","isfg":"0","color":"1","aod":"29","LineTitle1":"eU1qVSUyZnJEbHZ1MGdVVEl6TlVJZ05TNDNOVzF0S2pFMU1EQXFReURXOThIMw==","LineTitle2":"eU1qVSUyZnJEbHZ1MGdVVEl6TlVJZ05TNDNOVzF0S2pFMU1EQXFReURXOThIMw==","LineTitle3":"eU1qVSUyZnJEbHZ1MGdVVEl6TlVJZ05TNDNOVzF0S2pFMU1EQXFReURXOThIMw==","LineTitle4":"","GongshiID1":"MA==","GongshiID2":"0","GongshiID3":"0","GongshiID4":"0","GSName1":"","GSName2":"","GSName3":"","GSName4":"","updatetime":"0000-00-00 00:00:00"},{"ID":"2505","dtname":"ufrE2tb30qrK0LOhwba9ucO6vNu48dDQx+k=","dtnameshort":"ufrE2tb30qrK0LOhwba9ucO6","ImageType":"2","DTID1":"6345","DTID1Sub":"0","Data1Type":"1","Data1Type1":"1","Data1Image":"2","Data1Pre":"0","Data1AddSub":"0","Data1AddSubHundCore":"0","Data1Type2":"0","Data1Type3":"0","Data1Type4":"0","Data1Type5":"0","DTID2":"6320","DTID2Sub":"0","Data2Type":"1","Data2Image":"2","DTID3":"6349","DTID3Sub":"0","Data3Type":"1","Data3Image":"2","DTID4":"0","DTID4Sub":"0","Data4Type":"0","Data4Image":"0","zhou1":"0","zhou2":"0","zhou3":"0","zhou4":"0","isjz":"0","isbg":"0","isfg":"0","color":"0","aod":"17","LineTitle1":"","LineTitle2":"","LineTitle3":"","LineTitle4":"","GongshiID1":"MA==","GongshiID2":"0","GongshiID3":"0","GongshiID4":"0","GSName1":"","GSName2":"","GSName3":"","GSName4":"","updatetime":"0000-00-00 00:00:00"}],"hasnode":"","SubResults":""}]}';

                $arr['Results'] = (gzencode($a, 9));//压缩找个方法
                //$arr['Results'] ='';
                $json_string = $this->pri_JSON($arr);

                echo $json_string;
            } else {
                echo '{"Success":"1","Message":"s8m5pg==","Results":[{"ID":"0","parentid":"0","cataloguename":"我的数据","status":"1","aod":"1","hasleaves":"1","updatetime":"","leaves":[{"ID":"2435","dtname":"1f3KvbDmo6jO8Mm+o6k=","dtnameshort":"1f3KvbDmo6jO8Mm+o6k=","ImageType":"2","DTID1":"1885","DTID1Sub":"0","Data1Type":"1","Data1Type1":"1","Data1Image":"2","Data1Pre":"0","Data1AddSub":"0","Data1AddSubHundCore":"","Data1Type2":"0","Data1Type3":"0","Data1Type4":"0","Data1Type5":"0","DTID2":"709","DTID2Sub":"0","Data2Type":"1","Data2Image":"2","DTID3":"0","DTID3Sub":"0","Data3Type":"0","Data3Image":"0","DTID4":"0","DTID4Sub":"0","Data4Type":"0","Data4Image":"0","zhou1":"0","zhou2":"0","zhou3":"0","zhou4":"0","isjz":"0","isbg":"0","isfg":"0","color":"0","aod":"12","LineTitle1":"","LineTitle2":"","LineTitle3":"","LineTitle4":"","GongshiID1":"MA==","GongshiID2":"0","GongshiID3":"0","GongshiID4":"0","GSName1":"","GSName2":"","GSName3":"","GSName4":"","updatetime":"0000-00-00 00:00:00"},{"ID":"2397","dtname":"tbHUwg==","dtnameshort":"tbHUwg==","ImageType":"2","DTID1":"709","DTID1Sub":"0","Data1Type":"1","Data1Type1":"1","Data1Image":"2","Data1Pre":"0","Data1AddSub":"0","Data1AddSubHundCore":"","Data1Type2":"0","Data1Type3":"0","Data1Type4":"0","Data1Type5":"0","DTID2":"1210","DTID2Sub":"0","Data2Type":"1","Data2Image":"1","DTID3":"1483","DTID3Sub":"0","Data3Type":"1","Data3Image":"1","DTID4":"1218","DTID4Sub":"0","Data4Type":"1","Data4Image":"1","zhou1":"0","zhou2":"0","zhou3":"0","zhou4":"0","isjz":"0","isbg":"0","isfg":"0","color":"0","aod":"6","LineTitle1":"","LineTitle2":"","LineTitle3":"","LineTitle4":"","GongshiID1":"MA==","GongshiID2":"0","GongshiID3":"0","GongshiID4":"0","GSName1":"","GSName2":"","GSName3":"","GSName4":"","updatetime":"2019-08-01 00:00:00"}],"hasnode":1,"SubResults":{"ID":"2","parentid":"1","cataloguename":"xL\/CvDI=","status":1,"aod":"41","hasleaves":"1","updatetime":"2019-07-30 08:31:02","leaves":[{"ID":"3836","dtname":"ufrE2tb30qrK0LOhwba9ucO6vNu48dDQx+my4srUyv2+3bChtcS3orK8tdi3vQ==","dtnameshort":"ufrE2tb30qrK0LOhwba9ucO6","ImageType":"2","DTID1":"6345","DTID1Sub":"0","Data1Type":"1","Data1Type1":"1","Data1Image":"2","Data1Pre":"0","Data1AddSub":"0","Data1AddSubHundCore":"0","Data1Type2":"0","Data1Type3":"0","Data1Type4":"0","Data1Type5":"0","DTID2":"6320","DTID2Sub":"0","Data2Type":"1","Data2Image":"2","DTID3":"6349","DTID3Sub":"0","Data3Type":"1","Data3Image":"2","DTID4":"0","DTID4Sub":"0","Data4Type":"0","Data4Image":"0","zhou1":"0","zhou2":"0","zhou3":"0","zhou4":"0","isjz":"0","isbg":"0","isfg":"0","color":"0","aod":"33","LineTitle1":"","LineTitle2":"","LineTitle3":"","LineTitle4":"","GongshiID1":"MA==","GongshiID2":"0","GongshiID3":"0","GongshiID4":"0","GSName1":"","GSName2":"","GSName3":"","GSName4":"","updatetime":"0000-00-00 00:00:00"},{"ID":"3827","dtname":"ufrE2tb30qrK0LOhyMjU\/rDlvu2827jx","dtnameshort":"ufrE2tb30qrK0LOhyMjU\/rDl","ImageType":"2","DTID1":"34388","DTID1Sub":"0","Data1Type":"1","Data1Type1":"0","Data1Image":"3","Data1Pre":"0","Data1AddSub":"0","Data1AddSubHundCore":"0","Data1Type2":"0","Data1Type3":"0","Data1Type4":"0","Data1Type5":"0","DTID2":"36007","DTID2Sub":"0","Data2Type":"1","Data2Image":"6","DTID3":"35382","DTID3Sub":"0","Data3Type":"1","Data3Image":"2","DTID4":"0","DTID4Sub":"0","Data4Type":"0","Data4Image":"0","zhou1":"0","zhou2":"0","zhou3":"0","zhou4":"0","isjz":"0","isbg":"0","isfg":"0","color":"1","aod":"29","LineTitle1":"eU1qVSUyZnJEbHZ1MGdVVEl6TlVJZ05TNDNOVzF0S2pFMU1EQXFReURXOThIMw==","LineTitle2":"eU1qVSUyZnJEbHZ1MGdVVEl6TlVJZ05TNDNOVzF0S2pFMU1EQXFReURXOThIMw==","LineTitle3":"eU1qVSUyZnJEbHZ1MGdVVEl6TlVJZ05TNDNOVzF0S2pFMU1EQXFReURXOThIMw==","LineTitle4":"","GongshiID1":"MA==","GongshiID2":"0","GongshiID3":"0","GongshiID4":"0","GSName1":"","GSName2":"","GSName3":"","GSName4":"","updatetime":"0000-00-00 00:00:00"},{"ID":"2505","dtname":"ufrE2tb30qrK0LOhwba9ucO6vNu48dDQx+k=","dtnameshort":"ufrE2tb30qrK0LOhwba9ucO6","ImageType":"2","DTID1":"6345","DTID1Sub":"0","Data1Type":"1","Data1Type1":"1","Data1Image":"2","Data1Pre":"0","Data1AddSub":"0","Data1AddSubHundCore":"0","Data1Type2":"0","Data1Type3":"0","Data1Type4":"0","Data1Type5":"0","DTID2":"6320","DTID2Sub":"0","Data2Type":"1","Data2Image":"2","DTID3":"6349","DTID3Sub":"0","Data3Type":"1","Data3Image":"2","DTID4":"0","DTID4Sub":"0","Data4Type":"0","Data4Image":"0","zhou1":"0","zhou2":"0","zhou3":"0","zhou4":"0","isjz":"0","isbg":"0","isfg":"0","color":"0","aod":"17","LineTitle1":"","LineTitle2":"","LineTitle3":"","LineTitle4":"","GongshiID1":"MA==","GongshiID2":"0","GongshiID3":"0","GongshiID4":"0","GSName1":"","GSName2":"","GSName3":"","GSName4":"","updatetime":"0000-00-00 00:00:00"}],"hasnode":"","SubResults":""}}]}';
                //echo '{"Success":"1","Message":"s8m5pg=="}';

            }

            return false;

        }
    }
    public function SearchListGet4($params,$dc_search_custom=array(),$dc_search_custom_catalogue=array())
    {

        
        $isEnglish=0;
        $mc_type=0;
        $querykey="";
        $parentid=0;
        $isSubs=1;
        $isen="";
        if($params['zhixing']==1)
        {

        }
        else
        {
            $selectsql="select ID,dtname".$isen." as dtname,catalogueid,aod,MID,Uid
            from dc_search_custom where IsEnglish='" . $isEnglish . "'  and mc_type = '".$mc_type."'  order by binary(dtname) asc,aod desc,ID desc";
            $sear = $this->_dao->query($selectsql);
            foreach($sear as $k=>$v)
            {
                $alldc_search_custom[$v['MID']][$v['Uid']][]=$v;
            }

            $startcatalogue=$this->_dao->query("select *  from `dc_search_custom_catalogue` where mc_type='".$mc_type."' and status=1  and  IsEnglish='".$isEnglish."' order by  binary(cataloguename) asc");
            foreach($startcatalogue as $k=>$v)
            {
                $alldc_search_custom_catalogue[$v['MID']][$v['Uid']][]=$v;
            }

            echo "开始执行<br/>";
            $sql="SELECT `MID`,`Uid` FROM `dc_search_custom` WHERE `mc_type`=0 and `IsEnglish`=0 GROUP by `MID`,`Uid`";
            $row = $this->_dao->query($sql);
            foreach($row as $k=>$v)
            {
                $info['MID']=$v['MID'];
                $info['Uid']=$v['Uid'];
                $info['zhixing']=1;
                $dc_search_custom=isset($alldc_search_custom[$v['MID']][$v['Uid']])?$alldc_search_custom[$v['MID']][$v['Uid']]:array();
                $dc_search_custom_catalogue=isset($alldc_search_custom_catalogue[$v['MID']][$v['Uid']])?$alldc_search_custom_catalogue[$v['MID']][$v['Uid']]:array();
                $this->SearchListGet4($info,$dc_search_custom,$dc_search_custom_catalogue);
            }
            echo "执行完成";
            exit;
        }

       
        $mid=$params['MID'];
        $uid=$params['Uid'];
        
        $sear_arr['ID']=0;
        $sear_arr['parentid']="";
        $sear_arr['cataloguename']="ztK1xLao1sY=";
        $sear_arr['status']='';
        $sear_arr['aod']='';
        $sear_arr['updatetime']='';

        if($isEnglish==1){
            $isen="_en";
        }
        $selectsql="select ID,dtname".$isen." as dtname,catalogueid,aod,MID,Uid
                            from dc_search_custom where IsEnglish='" . $isEnglish . "'  and MID = '" . $mid . "' and Uid = '".$uid."' and mc_type = '".$mc_type."' and dtname like '%".$querykey."%' order by binary(dtname) asc,aod desc,ID desc";
                            //$sear = $this->_dao->query($selectsql);
                            $sear = $dc_search_custom;
                            usort($sear,"pinyin_sort");
                            $iscatalogue=array();
                            $isnotcatalogue=array();
                            $iscatalogue_arr = array();
                            foreach($sear as $k=>$v) {
                                //if(empty($v['catalogueid'])){
                                if($v['catalogueid']==$parentid){//判断这个父目录下的这一级有没有数据有数据这一级leaves赋值
                                    $isnotcatalogue[$k]=$v;
                                }else{
                                    $iscatalogue[$k]=$v;
                                }
                            }
                            if(empty($iscatalogue)){
                                //$startcatalogue=$this->_dao->query("select *  from `dc_search_custom_catalogue` where mc_type='".$mc_type."' and MID='".$mid."' and Uid='".$uid."' and status=1  and  IsEnglish='".$isEnglish."' order by  binary(cataloguename) asc");
                                $startcatalogue=$dc_search_custom_catalogue;
                                //sort($startcatalogue,"pinyin_sort2");
                                foreach($startcatalogue as $k=>$v) {//初始目录，下面的递归找下面的
                                    $iscatalogue[$k]='';//再查一遍看看有没有空目录
                                }
                            }
                            //echo '<pre>';print_R($iscatalogue);
                            if(!empty($isnotcatalogue)){
                                //echo '<pre>';print_R($isnotcatalogue);
                                $i=0;
                                foreach($isnotcatalogue as $ck=>$cv) {//初始目录，下面的递归找下面的
                                    $isnotcatalogue2[$i]=$cv;//再查一遍看看有没有空目录
                                    $i++;
                                }
                                $sear_arr['hasleaves']=1;
                                usort($isnotcatalogue,"pinyin_sort");
                                if($isSubs==0){
                                    $sear_arr['hasleaves']=0;
                                    $sear_arr['leaves']=array();
                                }else{
                                    $sear_arr['leaves']=$isnotcatalogue2;
                                }
                            }else{
                                $sear_arr['hasleaves']=0;
                                $sear_arr['leaves']=array();
                            }

                            if(!empty($iscatalogue)){//有目录的情况，递归查询列出目录
                                foreach ($iscatalogue as $key => $val) {
                                    if ( isset($val['catalogueid']) && $val['catalogueid'] )
                                        $iscatalogue_arr[$val['catalogueid']][] = $val;
                                }
                                //getcataloguearr所有有目录关系的进行处理，key=catalogueid；
                                //$parentidarr=$this->_dao->query("select ID,parentid  from `dc_search_custom_catalogue`  where mc_type='".$mc_type."' and MID='".$mid."' and Uid='".$uid."'  and  IsEnglish='".$isEnglish."' and status=1  order by binary(cataloguename) asc");
                                $parentidarr=$dc_search_custom_catalogue;
                                // print_r($parentidarr);
                                foreach($parentidarr as $val){
                                    $parentidmenu[$val['parentid']][]=$val['ID'];
                                }
                                // print_r($parentidmenu);exit;
                                //$row=$this->_dao->query("select *  from `dc_search_custom_catalogue` where mc_type='".$mc_type."' and MID='".$mid."' and Uid='".$uid."' and status=1  and  IsEnglish='".$isEnglish."'  order by binary(cataloguename) asc");
                                $row=$dc_search_custom_catalogue;
                                //sort($row,"pinyin_sort2");
                                $rowarr = array();
                                foreach($row as $val){
                                    $rowarr[$val['ID']]=$val;
                                }
                
                                $iscatalogue_arr2=$this->getcataloguearr($iscatalogue_arr,$parentid,$parentidmenu,$rowarr,$isSubs);
                                //如果此处hasnode返回1，但是$iscatalogue_arr2没有数据，说明此父目录下没有数据
                                if(!empty($iscatalogue_arr2)){
                                    $sear_arr['hasnode']=1;
                                    $sear_arr['SubResults']=$iscatalogue_arr2;
                                }else{
                                    $sear_arr['hasnode']=0;
                                    $sear_arr['SubResults']=array();
                                }
                            }else{
                                $sear_arr['hasnode']=0;
                                $sear_arr['SubResults']=array();
                            }
                            $this->diguigengxin($sear_arr);
                            //echo json_encode($sear_arr);
                            // exit;
                            // $json_string = $this->pri_JSON($sear_arr);
                            // echo $json_string;
                            // exit;


    }
    function diguigengxin($info)
    {
        if($info['hasnode'])
        {
            $i=0;
            foreach($info['SubResults'] as $k=>$v)
            {
                echo iconv("GBK", "UTF-8",base64_decode($v['cataloguename']))."<br/>";
                $updatesql="update dc_search_custom_catalogue set aod=".$i." where id='".$v['ID']."'";
                echo $updatesql."<br/>";
                //$this->_dao->execute($updatesql);
                $this->diguigengxin($v);
                $i++;
            }

        }
        if($info['hasleaves'])
        {
            $j=0;
            foreach($info['leaves'] as $k=>$v)
            {
                echo $v['dtname']."<br/>";
                $updatesql="update dc_search_custom set aod=".$j." where id='".$v['ID']."'";
                echo $updatesql."<br/>";
                //$this->_dao->execute($updatesql);
                $this->diguigengxin($v);
                $j++;
            }

        }
    }


    private function querykeysearch_kh($querykey, $isen, $isEnglish, $mid, $uid, $mc_type, $isSubs)
    {
        $parentid_querykeyarr = $this->_dao->query("select id as ID,cataloguename from  dc_customer_data_catalogue  where  isdel=0  and status=1 and IsEnglish='" . $isEnglish . "'  and MID = '" . $mid . "' and Uid = '$uid' and mc_type = '$mc_type' and isCatalogue=1  order by binary(cataloguename) asc");
        foreach ($parentid_querykeyarr as $k => $v) {
            $cataloguename=iconv("GBK", "UTF-8",base64_decode($v['cataloguename']));
            if(str_replace($querykey,'',$cataloguename)!=$cataloguename)
            {
                
                $ids_arr2[] = $v['ID'];
            }
        }


        foreach ($ids_arr2 as $key => $value) {//关键字目录所经过的路径父目录

            $parentidarr2 = $this->getparentid2($value, '');

            foreach ($parentidarr2 as $key2 => $value2) {
                $parentidarr[] = $value2;

            }


        }


        $selectsql = "select  parentid,cataloguename  from  dc_customer_data_catalogue  where  isdel=0 and status=1 and IsEnglish='" . $isEnglish . "'  and MID = '" . $mid . "' and Uid = '$uid' and mc_type = '$mc_type' and isCatalogue=0  order by binary(cataloguename) asc";

        $sear = $this->_dao->query($selectsql);
        //每条数据所经过的目录
        foreach ($sear as $k => $v) {

            $cataloguename=iconv("GBK", "UTF-8",base64_decode($v['cataloguename']));
            if(str_replace($querykey,'',$cataloguename)!=$cataloguename)
            {
                $ids_arr[] = $v['parentid'];
            }
            
        }


        foreach ($ids_arr as $key => $value) {//关键字目录所经过的路径父目录

            $parentidarr3 = $this->getparentid2($value, '');

            foreach ($parentidarr3 as $key2 => $value2) {
                $parentidarr4[] = $value2;

            }


        }
        if (empty($parentidarr)) {
            $ids_arr = $parentidarr4;
        } else if (empty($parentidarr4)) {
            $ids_arr = $parentidarr;
        } else {
            $ids_arr = array_merge($parentidarr, $parentidarr4);
        }

        foreach ($ids_arr as $k => $v) {

            $ids[$v['parentid']]['ID'][] = $v['ID'];

        }

        $parentid = 0;
        $arr = $this->getquerykeycataloguearr_kh($parentid, $ids, $querykey, $isen, $isEnglish, $mid, $uid, $mc_type, $isSubs);


        return $arr;

    }

    function getquerykeycataloguearr_kh($parentid, $ids, $querykey, $isen, $isEnglish, $mid, $uid, $mc_type, $isSubs)
    {
        if (empty($parentid)) {
            //我的数据
            $carr['ID'] = 0;
            $carr['parentid'] = "";
            $carr['cataloguename'] = "我的数据";
            if ($isEnglish) $carr['cataloguename'] = base64_encode("My Customization");
            $carr['status'] = '';
            $carr['aod'] = '';
            $carr['updatetime'] = '';
        } else {
            $bjLevel = $this->_dao->getrow("select *  from `dc_customer_data_catalogue`  where id='" . $parentid . "'  and  mc_type='" . $mc_type . "' and MID='" . $mid . "' and Uid='" . $uid . "'  and  IsEnglish='" . $isEnglish . "' and status=1  order by binary(cataloguename) asc");
            $bjLevel['cataloguename']=iconv("GBK", "UTF-8",base64_decode($bjLevel['cataloguename']));
            $carr['ID'] = $bjLevel['id'];
            $carr['parentid'] = $bjLevel['parentid'];
            //$carr['cataloguename']=base64_encode($bjLevel['cataloguename']);
            //$carr['cataloguename'] = $bjLevel['cataloguename'];
            $carr['cataloguename'] = $bjLevel['cataloguename'];
            $carr['status'] = $bjLevel['status'];
            //$carr['status']=$bjLevel['status'];
            $carr['aod'] = $bjLevel['aod'];
            $carr['updatetime'] = $bjLevel['updatetime'];

            if ($bjLevel['id'] == '') {
                echo "select *  from `dc_customer_data_catalogue`  where id='" . $parentid . "'  and  mc_type='" . $mc_type . "' and MID='" . $mid . "' and Uid='" . $uid . "'  and  IsEnglish='" . $isEnglish . "' and status=1  order by binary(cataloguename) asc";
                exit;
            }
        }
        if (strstr($bjLevel['cataloguename'], $querykey) && !empty($querykey)) {//能匹配到说明查询的目录就是这个，那要显示这个目录下面的所有数据，这时候关键字后面的递归就不查了
            $querykey = '';

        }

        if ($isSubs == 0) {
            $carr['hasleaves'] = 0;
            $carr['leaves'] = array();
        } else {


            $selectsql = "select id,cataloguename as dtname,parentid as catalogueid,status,aod,createtime,updatetime,Uid,MID,isCatalogue,mc_type,IsEnglish,hasnode,hasleaves,
				nodelevel,D1ImageType,D1UnitType,D1UnitConv,D1UnitName,D1dtymd,PowerID,updatetime,isdel
				from  dc_customer_data_catalogue where  isdel=0 and parentid  = '" . $parentid . "'   and IsEnglish='" . $isEnglish . "'  and MID = '" . $mid . "' and Uid = '$uid' and mc_type = '$mc_type' and isCatalogue=0    order by binary(cataloguename) asc,aod desc,ID desc";

            $sear = $this->_dao->query($selectsql);
            $searnew=array();
            foreach ($sear as $k => $v) {

                $v['dtname']=iconv("GBK", "UTF-8",base64_decode($v['dtname']));
                if($querykey==""||(str_replace($querykey,'',$v['dtname'])!=$v['dtname']))
                {
                    $searinfo=array();
                    $searinfo['ID']= $v['id'];
                    $searinfo['dtname']= $v['dtname'];
                    $searinfo['ImageType']= $v['D1ImageType'];
                    $searinfo['catalogueid']= $v['catalogueid'];
                    $searinfo['isCatalogue']= $v['isCatalogue'];
                
                    $searinfo['aod']= $v['aod'];
                    if($v['updatetime']=='0000-00-00 00:00:00'){
                        $searinfo['updatetime']=$v['createtime'];
                    }
                    $searinfo['dtymd']= $v['D1dtymd'];
                    

                    $DTIDJson=array();
                    $DTIDJson['DTID']=$v['id'];
                    $DTIDJson['DataImage']=$v['D1ImageType'];
                    $DTIDJson['unitconver']=$v['D1UnitConv'];
                    $DTIDJson['unitstring']=$v['D1UnitName'] ;
            //		$DTIDJson['unitstring']=$sv['D1UnitName'];
                    $searinfo['DTIDJson'][]=$DTIDJson;
                    $searnew[]=$searinfo;
                }
            }


            if (empty($searnew)) {
                $carr['hasleaves'] = 0;
                $carr['leaves'] = array();
            } else {
                $carr['hasleaves'] = 1;
                $carr['leaves'] = $searnew;
            }

        }


        //上面绑定数据，下面部分为目录处理，递归

        if(isset($ids[$parentid]['ID']))
        $sonids = array_unique($ids[$parentid]['ID']);

        //如果关键字为空了，那么当前目录下面的目录可能有缺失，这里查一下补上
        if (empty($querykey)) {
            $bcid = $this->_dao->query("select id as ID  from `dc_customer_data_catalogue`  where parentid='" . $parentid . "' and isdel=0 and  mc_type='" . $mc_type . "' and MID='" . $mid . "' and Uid='" . $uid . "'  and  IsEnglish='" . $isEnglish . "' and isCatalogue=1 and status=1  order by binary(cataloguename) asc");

            foreach ($bcid as $k => $v) {
                $bcids[] = $v['ID'];
            }

            $sonids = $bcids;

        }


        foreach ($sonids as $k => $v) {


            $son_arr = $this->getquerykeycataloguearr_kh($v, $ids, $querykey, $isen, $isEnglish, $mid, $uid, $mc_type, $isSubs);

            if ($son_arr == "") {
                $carr['hasnode'] = 0;
                $carr['SubResults'] = array();
            } else {
                $carr['hasnode'] = 1;
                $carr['SubResults'][] = $son_arr;
            }


            $i++;
        }

        //print_R($ids);

        return $carr;
    }


    private function querykeysearch($querykey, $isen, $isEnglish, $mid, $uid, $mc_type, $isSubs,$isSystemUser)
    {
        // $sear_arr['ID']=0;
        // $sear_arr['parentid']="";
        // $sear_arr['cataloguename']="我的数据";
        // if($isEnglish) $sear_arr['cataloguename'] = base64_encode("My Data");
        // $sear_arr['status']='';
        // $sear_arr['aod']='';
        // $sear_arr['updatetime']='';
        //从最初的目录开始向下搜索关键字绑定目录及数据
        $ids_arr2 = array();
        $ids = array();
        $ids_arr = array();
        $parentidarr = array();

        $parentid_querykeyarr = $this->_dao->query("select ID  from `dc_search_custom_catalogue`  where cataloguename like '%$querykey%' and mc_type='" . $mc_type . "' and MID='" . $mid . "' and Uid='" . $uid . "'  and  IsEnglish='" . $isEnglish . "' and status=1  order by binary(cataloguename) asc");

        foreach ($parentid_querykeyarr as $k => $v) {

            $ids_arr2[] = $v['ID'];
        }


        foreach ($ids_arr2 as $key => $value) {//关键字目录所经过的路径父目录

            $parentidarr2 = $this->getparentid($value, '');

            foreach ($parentidarr2 as $key2 => $value2) {
                $parentidarr[] = $value2;

            }
        }

        $selectsql = "select catalogueid from dc_search_custom  where IsEnglish='" . $isEnglish . "'   and MID = '" . $mid . "' and Uid = '$uid' and mc_type = '$mc_type' and dtname like '%$querykey%' order by binary(dtname) asc,aod desc,ID desc";
        $sear = $this->_dao->query($selectsql);
        //每条数据所经过的目录
        foreach ($sear as $k => $v) {

            $ids_arr[] = $v['catalogueid'];
        }

        foreach ($ids_arr as $key => $value) {//关键字目录所经过的路径父目录
            $parentidarr3 = $this->getparentid($value, '');
            foreach ($parentidarr3 as $key2 => $value2) {
                $parentidarr4[] = $value2;
            }
        }
        if (empty($parentidarr)) {
            $ids_arr = $parentidarr4;
        } else if (empty($parentidarr4)) {
            $ids_arr = $parentidarr;
        } else {
            $ids_arr = array_merge($parentidarr, $parentidarr4);
        }

        foreach ($ids_arr as $k => $v) {
            $ids[$v['parentid']]['ID'][] = $v['ID'];
        }
        $parentid = 0;
        $arr = $this->getquerykeycataloguearr($parentid, $ids, $querykey, $isen, $isEnglish, $mid, $uid, $mc_type, $isSubs,$isSystemUser);
        return $arr;

    }

    function getquerykeycataloguearr($parentid, $ids, $querykey, $isen, $isEnglish, $mid, $uid, $mc_type, $isSubs,$isSystemUser)
    {
        if (empty($parentid)) {

            //我的数据
            $carr['ID'] = 0;
            $carr['parentid'] = "";
            $carr['cataloguename'] = "我的定制";
		if( $isSystemUser==1&&$mc_type==0){ //
			$carr['cataloguename']= "推荐数据";//推荐数据
		}
            if ($isEnglish) $carr['cataloguename'] = ("My Data");
            $carr['status'] = '';
            $carr['aod'] = '';
            $carr['updatetime'] = '';
        } else {
            $bjLevel = $this->_dao->getrow("select *  from `dc_search_custom_catalogue`  where ID='" . $parentid . "'  and  mc_type='" . $mc_type . "' and MID='" . $mid . "' and Uid='" . $uid . "'  and  IsEnglish='" . $isEnglish . "' and status=1  order by binary(cataloguename) asc");
            $carr['ID'] = $bjLevel['ID'];
            $carr['parentid'] = $bjLevel['parentid'];
            $carr['cataloguename'] =  $bjLevel['cataloguename'] ;
            $carr['status'] = $bjLevel['status'];
            $carr['aod'] = $bjLevel['aod'];
            $carr['updatetime'] = $bjLevel['updatetime'];

        }
        if (strstr($bjLevel['cataloguename'], $querykey) && !empty($querykey)) {//能匹配到说明查询的目录就是这个，那要显示这个目录下面的所有数据，这时候关键字后面的递归就不查了
            $querykey = '';

        }

        if ($isSubs == 0) {
            $carr['hasleaves'] = 0;
            $carr['leaves'] = array();
        } else {
            $selectsql = "select ID,dtname" . $isen . " as dtname,catalogueid,dtnameshort" . $isen . " as dtnameshort,ImageType,
			DTID1 ,
			DTID1Sub,
			Data1Type,
			Data1Type1,
			Data1Image,
			Data1Pre,
			Data1AddSub,
			Data1AddSubHundCore,
			Data1Type2,
			Data1Type3,
			Data1Type4,
			Data1Type5 ,
			DTID2 ,
			DTID2Sub,
			Data2Type ,Data2Image,
			DTID3 ,
			DTID3Sub ,
			Data3Type , Data3Image,
			DTID4 ,
			DTID4Sub ,
			Data4Type , Data4Image,
			DTIDJson,
			zhou1,zhou2,zhou3,zhou4,
			isjz,isbg,isfg,color,aod,
			LineTitle1,LineTitle2,LineTitle3,LineTitle4,
			GongshiID1,GongshiID2,GongshiID3,GongshiID4,catalogueid,updatetime,createtime,DateFormat
			from dc_search_custom where IsEnglish='" . $isEnglish . "' and catalogueid  = '" . $parentid . "'  and MID = '" . $mid . "' and Uid = '$uid' and mc_type = '$mc_type' and dtname like '%$querykey%' order by binary(dtname) asc,aod desc,ID desc";

            $sear = $this->_dao->query($selectsql);


            //if($mc_type=='1')


            usort($sear, "pinyin_sort");
            //echo '<pre>';print_R($sear);

            foreach ($sear as &$tmp) {
                //$tmp['dtname'] =  $tmp['dtname'] ;
                //$tmp['dtnameshort'] =  $tmp['dtnameshort'];
                //add
                //$tmp['LineTitle1'] = base64_encode($tmp['LineTitle1']);
                //$tmp['LineTitle2'] = base64_encode($tmp['LineTitle2']);
                //$tmp['LineTitle3'] = base64_encode($tmp['LineTitle3']);
                //$tmp['LineTitle4'] = base64_encode($tmp['LineTitle4']);


                //end

                if ($tmp['updatetime'] == '0000-00-00 00:00:00') {
                    $tmp['updatetime'] = $tmp['createtime'];
                }
                for ($i = 1; $i <= 4; $i++) {
                    if ($tmp['GongshiID' . $i]) {
                        $gsname = $this->_dao->getone("select Name from dc_formula_custom where id='" . $tmp['GongshiID' . $i] . "' limit 1");
                        $tmp['GSName' . $i] = $gsname;
                    } else {
                        $tmp['GSName' . $i] = "";
                    }
                }
            }


            //过滤换行符
            $allGongshiID=array();
            foreach ($sear as $k => $v) {

                $sear[$k]['LineTitle1'] = str_replace(array("\r\n", "\r", "\n"), "", $v['LineTitle1']);
                $sear[$k]['LineTitle2'] = str_replace(array("\r\n", "\r", "\n"), "", $v['LineTitle2']);
                $sear[$k]['LineTitle3'] = str_replace(array("\r\n", "\r", "\n"), "", $v['LineTitle3']);
                $sear[$k]['LineTitle4'] = str_replace(array("\r\n", "\r", "\n"), "", $v['LineTitle4']);

                $sear[$k]['DTIDJson'] = array();
                if (!empty($v['DTIDJson'])) {

                    $dj = json_decode($v['DTIDJson'], true);

                    foreach ($dj as $k1 => $v1) {
                        // $DTIDJson_arr = $v1;
                        // $gsname = $this->_dao->getone("select Name from dc_formula_custom where id='" . $DTIDJson_arr['GongshiID'] . "' limit 1");

                        // $DTIDJson_arr['GSName'] = ($gsname);
                        // $sear[$k]['DTIDJson'][$k1] = $DTIDJson_arr;

                        if($v1['GongshiID'])
                        {
                            if(!in_array($v1['GongshiID'],$allGongshiID))
                            {
                                $allGongshiID[]=$v1['GongshiID'];
                            }                    
                        }
                        $v1['GSName']="";


                        $tmpLineTitle=iconv("GBK", "UTF-8",base64_decode($v1['LineTitle']));
                        if(empty($tmpLineTitle))
                        {
                            $v1['LineTitle']=iconv("GBK", "UTF-8",base64_decode(urldecode($v1['LineTitle'])));
                        }
                        else
                        {
                            $v1['LineTitle']=$tmpLineTitle;
                        }
                        //$v1['LineTitle']=iconv("GBK", "UTF-8",base64_decode(urldecode($v1['LineTitle'])));
                        //$v1['LineTitle']=iconv("GBK", "UTF-8",base64_decode($v1['LineTitle']));
                        $v1['unitstring']=iconv("GBK", "UTF-8",base64_decode($v1['unitstring']));
                        if(isset($v1['unitstrings']))
                        $v1['unitstrings']=iconv("GBK", "UTF-8",base64_decode($v1['unitstrings']));
                        $dj[$k1]=$v1;
                    }
                    $sear[$k]['DTIDJson']=$dj;

                }
                $sear[$k]['ChartExtLineStyle'] = array();
                if (!empty($v['ChartExtLineStyle'])) {
                    $ces = json_decode($v['ChartExtLineStyle'], true);

                    foreach ($ces as $k2 => $v2) {
                        $sear[$k]['ChartExtLineStyle'][$k2] = $v2;
                    }

                }
            }
            if($allGongshiID)
            {
               $gsnames=$this->_dao->query("select id,Name from dc_formula_custom where id in (".implode(',',$allGongshiID) .")");
               $allgsnames=array();
               foreach($gsnames as $k=>$v) 
               {
                  $allgsnames[$v['id']]=$v['Name'];
               }
               foreach($sear as $k=>$v) {
                if(!empty($v['DTIDJson'])){
                    //print_r($v['DTIDJson']);
                    //$dj=json_decode($v['DTIDJson'],true);
                    $dj=$v['DTIDJson'];
                    foreach($dj as $k1=>$v1){
                        
                        $DTIDJson_arr =$v1;
                        if($DTIDJson_arr['GongshiID'])
                        {
                            $DTIDJson_arr['GSName']=$allgsnames[$DTIDJson_arr['GongshiID']];
                            $sear[$k]['DTIDJson'][$k1]=$DTIDJson_arr;
                        }
                        //$sear[$k]['DTIDJson'][$k1]['LineTitle']=iconv("GBK", "UTF-8",base64_decode($v1['LineTitle']));
                        //$sear[$k]['DTIDJson'][$k1]['unitstring']=iconv("GBK", "UTF-8",base64_decode($v1['unitstring']));
                        
                    }
                 }

               }
            }


            if (empty($sear)) {
                $carr['hasleaves'] = 0;
                $carr['leaves'] = array();
            } else {
                $carr['hasleaves'] = 1;
                $carr['leaves'] = $sear;
            }
        }

        //上面绑定数据，下面部分为目录处理，递归
        $sonids = empty( $ids[$parentid]['ID'] ) ? array() : array_unique($ids[$parentid]['ID']);

        //如果关键字为空了，那么当前目录下面的目录可能有缺失，这里查一下补上
        $bcids = array();
        if (empty($querykey)) {
            $bcid = $this->_dao->query("select ID  from `dc_search_custom_catalogue`  where parentid='" . $parentid . "'  and  mc_type='" . $mc_type . "' and MID='" . $mid . "' and Uid='" . $uid . "'  and  IsEnglish='" . $isEnglish . "' and status=1  order by binary(cataloguename) asc");
            foreach ($bcid as $k => $v) {
                $bcids[] = $v['ID'];
            }
            $sonids = $bcids;
        }
        foreach ($sonids as $k => $v) {
            $son_arr = $this->getquerykeycataloguearr($v, $ids, $querykey, $isen, $isEnglish, $mid, $uid, $mc_type, $isSubs,$isSystemUser);

            if ($son_arr == "") {
                $carr['hasnode'] = 0;
                $carr['SubResults'] = array();
            } else {
                $carr['hasnode'] = 1;
                $carr['SubResults'][] = $son_arr;
            }


            $i++;
        }

        //print_R($ids);

        return $carr;
    }

    //对象转数组具体实现：
    private function objectToArray($obj)
    {

        //首先判断是否是对象

        $arr = is_object($obj) ? get_object_vars($obj) : $obj;

        if (is_array($arr)) {

            //这里相当于递归了一下，如果子元素还是对象的话继续向下转换

            return array_map(__FUNCTION__, $arr);

        } else {

            return $arr;

        }

    }

    public function gzj($params) {
        $querykey= Urldecode($params['querykey']);

        $GUID = $params['GUID'];
        $SignCS = $params['SignCS'];
        $mc_type = $params['mc_type'];
        $action = $params['action'];		//接口名称
        // echo $params['isSubs'];
	$isSystemUser=$params['isSystemUser']?$params['isSystemUser']:0;

        if($params['isSubs']=="")$isSubs=1;
        else $isSubs=$params['isSubs'];
        $ip=$this->getIP();
        if(!empty($params['mc_type']))$mc_type=$params['mc_type'];
        else $mc_type=0;
        if(!empty($params['isEnglish']))$isEnglish=$params['isEnglish'];
        else $isEnglish=0;

        if(!empty($params['parentid']))$parentid=$params['parentid'];
        else $parentid=0;

        if(!empty($params['iscustomerdb']))$iscustomerdb=$params['iscustomerdb'];
        else $iscustomerdb=0;

        $isen="";
        if($isEnglish==1){
            $isen="_en";

        }
        $arr = array(
            'Success'=>'0',
            'Message'=>('失败'),
            'Results'=>'null',
        );
        if($isEnglish) $arr['Message']='Failed!';

        if(array_key_exists($GUID,$GLOBALS["testaccountarr"])&&$SignCS!=''&&(in_array($ip,$GLOBALS["testip"])||$GLOBALS["nowtest"]==1)){
            $user = $GLOBALS["testaccountuser"];
        }else{
            $user = $this->_dao->getUser($GUID,$SignCS,$mc_type);
        }
        $mid = $user['Mid'];
        $uid = $user['Uid'];


	if( $params['isSystemUser']==1&&$mc_type==3){ 
		$mid='1';
		$uid='147803';
	}
	else if( $params['isSystemUser']==1&&$mc_type==4){ 
		$mid='1';
		$uid='489602';
	}else if( $params['isSystemUser']==1&&$mc_type==0)
	{
		$mid='1';
		$uid='488599';
	}

        //$mid = 1;
        //$uid = 445131;
        $arrtem=$params;
        //array_pop($arrtem);
        $actionstr = "";
        foreach($arrtem as $k=>$v){
            $actionstr.="&".$k."=".$v;
        }
        /*2017/07/20 change */
        // echo $isSubs;exit;
        $sear_arr = array();
        if(!empty($querykey)){//关键字不为空
            $sear_arr=$this->querykeysearch($querykey,$isen,$isEnglish,$mid,$uid,$mc_type,$isSubs,$isSystemUser);
        }else{
	if( $params['isSystemUser']==1&& in_array($mc_type,array(0,3,4))){
                $selectsql="select ID,dtname".$isen." as dtname,catalogueid,dtnameshort".$isen." as dtnameshort,ImageType,
                            DTID1 ,
                            DTID1Sub,
                            Data1Type,
                            Data1Type1,
                            Data1Image,
                            Data1Pre,
                            Data1AddSub,
                            Data1AddSubHundCore,
                            Data1Type2,
                            Data1Type3,
                            Data1Type4,
                            Data1Type5 ,
                            DTID2 ,
                            DTID2Sub,
                            Data2Type ,Data2Image,
                            DTID3 ,
                            DTID3Sub ,
                            Data3Type , Data3Image,
                            DTID4 ,
                            DTID4Sub ,
                            Data4Type , Data4Image,
                            DTIDJson,ChartExtLineStyle,
                            zhou1,zhou2,zhou3,zhou4,
                            isjz,isbg,isfg,color,aod,
                            LineTitle1,LineTitle2,LineTitle3,LineTitle4,
                            GongshiID1,GongshiID2,GongshiID3,GongshiID4,catalogueid,updatetime,createtime,DateFormat
                            from dc_search_custom where IsEnglish='".$isEnglish."'  and MID = '".$mid."' and Uid = '$uid' and mc_type = '$mc_type' and dtname like '%$querykey%' order by ".($mc_type!=0?"aod desc,ID desc":"aod asc,binary(dtname) asc");
                            //from dc_search_custom where IsEnglish='".$isEnglish."'  and MID = '".$mid."' and Uid = '$uid' and mc_type = '$mc_type' order by binary(dtname) asc,aod desc,ID desc";

            }else{
                $selectsql="select ID,dtname".$isen." as dtname,catalogueid,dtnameshort".$isen." as dtnameshort,ImageType,
                            DTID1 ,
                            DTID1Sub,
                            Data1Type,
                            Data1Type1,
                            Data1Image,
                            Data1Pre,
                            Data1AddSub,
                            Data1AddSubHundCore,
                            Data1Type2,
                            Data1Type3,
                            Data1Type4,
                            Data1Type5 ,
                            DTID2 ,
                            DTID2Sub,
                            Data2Type ,Data2Image,
                            DTID3 ,
                            DTID3Sub ,
                            Data3Type , Data3Image,
                            DTID4 ,
                            DTID4Sub ,
                            Data4Type , Data4Image,
                            DTIDJson,ChartExtLineStyle,
                            zhou1,zhou2,zhou3,zhou4,
                            isjz,isbg,isfg,color,aod,
                            LineTitle1,LineTitle2,LineTitle3,LineTitle4,
                            GongshiID1,GongshiID2,GongshiID3,GongshiID4,catalogueid,updatetime,createtime,DateFormat
                            from dc_search_custom where IsEnglish='" . $isEnglish . "'  and MID = '" . $mid . "' and Uid = '".$uid."' and mc_type = '".$mc_type."' and dtname like '%".$querykey."%' order by ".($mc_type!=0?"binary(dtname) asc,aod desc,ID desc":"aod asc,binary(dtname) asc");
                            //from dc_search_custom where IsEnglish='".$isEnglish."'  and MID = '".$mid."' and Uid = '$uid' and mc_type = '$mc_type' order by binary(dtname) asc,aod desc,ID desc";
            }
            $sear = $this->_dao->query($selectsql);

        $sql_formula_custom = "select id,Name from dc_formula_custom where MID = '".$mid."' and Uid = '$uid' and mc_type = '$mc_type' ";
        $formula_custom_data = $this->_dao->Aquery($sql_formula_custom);
            //if($mc_type=='1')

	if( $params['isSystemUser']==1&&in_array($mc_type,array(0,3,4))){

            }else{
                if($mc_type!=0)
                {
                    usort($sear,"pinyin_sort");
                }
            }
            //echo '<pre>';print_R($sear);
            foreach($sear as &$tmp) {
                //$tmp['dtname']= $tmp['dtname'];
                //$tmp['dtnameshort']= base64_encode($this->utf8ToGb2312($tmp['dtnameshort']));
                //add
                //$tmp['LineTitle1']= base64_encode($this->utf8ToGb2312($tmp['LineTitle1']));
                //$tmp['LineTitle2']= base64_encode($this->utf8ToGb2312($tmp['LineTitle2']));
                //$tmp['LineTitle3']= base64_encode($this->utf8ToGb2312($tmp['LineTitle3']));
                //$tmp['LineTitle4']= base64_encode($this->utf8ToGb2312($tmp['LineTitle4']));
                //end
                if($tmp['updatetime']=='0000-00-00 00:00:00'){
                    $tmp['updatetime']=$tmp['createtime'];
                }
                for($i=1;$i<=4;$i++){
                    if($tmp['GongshiID'.$i]){
				//$gsname=$this->_dao->getone("select Name from dc_formula_custom where id='".$tmp['GongshiID'.$i]."' limit 1");
                         $gsname = $formula_custom_data[$tmp['GongshiID'.$i]];
                        $tmp['GSName'.$i]= $gsname;
                    }else{
                        $tmp['GSName'.$i]= "";
                    }
                }
            }
            //我的数据
            if(!empty($parentid)){
                $parent_arr= $this->_dao->getrow("select *  from `dc_search_custom_catalogue` where  mc_type='".$mc_type."' and MID='".$mid."' and Uid='".$uid."'  and  IsEnglish='".$isEnglish."' and status=1 and ID=".$parentid);
                $sear_arr['ID']=$parent_arr['ID'];

                $sear_arr['parentid']=$parent_arr['parentid'];
                $sear_arr['cataloguename']=$parent_arr['cataloguename'];
                $sear_arr['status']=$parent_arr['status'];
                $sear_arr['aod']=$parent_arr['aod'];
                $sear_arr['updatetime']=$parent_arr['updatetime'];
            }else{
                //我的数据
                $sear_arr['ID']=0;
                $sear_arr['parentid']="";
                $sear_arr['cataloguename']="我的定制";
                if($isEnglish) $sear_arr['cataloguename'] = ("My Customization");
                if( $params['isSystemUser']==1&&$mc_type==3){ //新钢的
                    $sear_arr['cataloguename']="经营分析";
                }
		else if($params['isSystemUser']==1&&$mc_type==4)
		{
			$sear_arr['cataloguename']="ztK1xMSjsOY=";
		}
		else if( $params['isSystemUser']==1&&$mc_type==0){ //
			$sear_arr['cataloguename']="推荐数据";//推荐数据
		}
                $sear_arr['status']='';
                $sear_arr['aod']='';
                $sear_arr['updatetime']='';
            }

            $iscatalogue=array();
            $isnotcatalogue=array();
            //过滤换行符
//            print_r( $sear );
            $allGongshiID=array();
            foreach($sear as $k=>$v) {
                $sear[$k]['LineTitle1']=str_replace(array("\r\n", "\r", "\n"), "", $v['LineTitle1']);
                $sear[$k]['LineTitle2']=str_replace(array("\r\n", "\r", "\n"), "", $v['LineTitle2']);
                $sear[$k]['LineTitle3']=str_replace(array("\r\n", "\r", "\n"), "", $v['LineTitle3']);
                $sear[$k]['LineTitle4']=str_replace(array("\r\n", "\r", "\n"), "", $v['LineTitle4']);
                //$sear[$k]['DTIDJson'] = array();
                if(!empty($v['DTIDJson'])){
                    $dj=json_decode($v['DTIDJson'],true);
                    //    echo '<pre>';echo $v['ID'];echo '<br>';
                        //print_R($v['DTIDJson']); echo '<br>';
                        
                    foreach($dj as $k1=>$v1){
                        //$DTIDJson_arr =$v1;
                        //print_r($v1);
                        if($v1['GongshiID'])
                        {
                            if(!in_array($v1['GongshiID'],$allGongshiID))
                            {
                                $allGongshiID[]=$v1['GongshiID'];
                            }
                            
                            //$gsname=$this->_dao->getone("select Name from dc_formula_custom where id='".$DTIDJson_arr['GongshiID']."' limit 1");
                            //$DTIDJson_arr['GSName']=base64_encode($gsname);
                            
                        }
                        $v1['GSName']="";
                        $tmpLineTitle=iconv("GBK", "UTF-8",base64_decode($v1['LineTitle']));
                        if(empty($tmpLineTitle))
                        {
                            $v1['LineTitle']=iconv("GBK", "UTF-8",base64_decode(urldecode($v1['LineTitle'])));
                        }
                        else
                        {
                            $v1['LineTitle']=$tmpLineTitle;
                        }
                        //$v1['LineTitle']=iconv("GBK", "UTF-8",base64_decode(urldecode($v1['LineTitle'])));
                        //$v1['LineTitle']=iconv("GBK", "UTF-8",base64_decode($v1['LineTitle']));
                        $v1['unitstring']=iconv("GBK", "UTF-8",base64_decode($v1['unitstring']));
                        if(isset($v1['unitstrings']))
                        $v1['unitstrings']=iconv("GBK", "UTF-8",base64_decode($v1['unitstrings']));
                        $dj[$k1]=$v1;
                        
                    }
                    $sear[$k]['DTIDJson']=$dj;
                   
                }
                
                $sear[$k]['ChartExtLineStyle'] = array();
                if (!empty($v['ChartExtLineStyle'])) {
                    $ces = json_decode($v['ChartExtLineStyle'], true);
                    foreach ($ces as $k2 => $v2) {
                        $sear[$k]['ChartExtLineStyle'][$k2] = $v2;
                    }
                }
            }
            //print_r($sear);
            if($allGongshiID)
            {
               $gsnames=$this->_dao->query("select id,Name from dc_formula_custom where id in (".implode(',',$allGongshiID) .")");
               $allgsnames=array();
               foreach($gsnames as $k=>$v) 
               {
                  $allgsnames[$v['id']]=$v['Name'];
               }
               foreach($sear as $k=>$v) {
                if(!empty($v['DTIDJson'])){
                    //print_r($v['DTIDJson']);
                    //$dj=json_decode($v['DTIDJson'],true);
                    $dj=$v['DTIDJson'];
                    foreach($dj as $k1=>$v1){
                        
                        $DTIDJson_arr =$v1;
                        if($DTIDJson_arr['GongshiID'])
                        {
                            $DTIDJson_arr['GSName']=$allgsnames[$DTIDJson_arr['GongshiID']];
                            $sear[$k]['DTIDJson'][$k1]=$DTIDJson_arr;
                        }
                        //$sear[$k]['DTIDJson'][$k1]['LineTitle']=iconv("GBK", "UTF-8",base64_decode($v1['LineTitle']));
                        //$sear[$k]['DTIDJson'][$k1]['unitstring']=iconv("GBK", "UTF-8",base64_decode($v1['unitstring']));
                        
                    }
                 }

               }
            }

            $ids_arr=array();
            $iscatalogue_arr = array();
            foreach($sear as $k=>$v) {
                //if(empty($v['catalogueid'])){
                if($v['catalogueid']==$parentid){//判断这个父目录下的这一级有没有数据有数据这一级leaves赋值
                    $isnotcatalogue[$k]=$v;
                }else{
                    $iscatalogue[$k]=$v;
                }
                //所有数据的所在目录
                $ids_arr[]=$v['catalogueid'];
            }

            if(empty($iscatalogue)){
                $startcatalogue=$this->_dao->query("select *  from `dc_search_custom_catalogue` where mc_type='".$mc_type."' and MID='".$mid."' and Uid='".$uid."' and status=1  and  IsEnglish='".$isEnglish."' order by  ".($mc_type!=0?"binary(cataloguename) asc":"aod asc,binary(cataloguename) asc"));
                //sort($startcatalogue,"pinyin_sort2");
                foreach($startcatalogue as $k=>$v) {//初始目录，下面的递归找下面的
                    $iscatalogue[$k]='';//再查一遍看看有没有空目录
                }
            }
            //echo '<pre>';print_R($iscatalogue);
            if(!empty($isnotcatalogue)){
                //echo '<pre>';print_R($isnotcatalogue);
                $i=0;
                foreach($isnotcatalogue as $ck=>$cv) {//初始目录，下面的递归找下面的
                    $isnotcatalogue2[$i]=$cv;//再查一遍看看有没有空目录
                    $i++;
                }
                $sear_arr['hasleaves']=1;
                if($mc_type!=0)
                {
                  usort($isnotcatalogue,"pinyin_sort");
                }
                if($isSubs==0){
                    $sear_arr['hasleaves']=0;
                    $sear_arr['leaves']=array();
                }else{
                    $sear_arr['leaves']=$isnotcatalogue2;
                }
            }else{
                $sear_arr['hasleaves']=0;
                $sear_arr['leaves']=array();
            }

            if(!empty($iscatalogue)){//有目录的情况，递归查询列出目录
                foreach ($iscatalogue as $key => $val) {
                    if ( isset($val['catalogueid']) && $val['catalogueid'] )
                        $iscatalogue_arr[$val['catalogueid']][] = $val;
                }
                //getcataloguearr所有有目录关系的进行处理，key=catalogueid；
                $parentidarr=$this->_dao->query("select ID,parentid  from `dc_search_custom_catalogue`  where mc_type='".$mc_type."' and MID='".$mid."' and Uid='".$uid."'  and  IsEnglish='".$isEnglish."' and status=1  order by ".($mc_type!=0?"binary(cataloguename) asc":"aod asc,binary(cataloguename) asc"));

                // print_r($parentidarr);
                foreach($parentidarr as $val){
                    $parentidmenu[$val['parentid']][]=$val['ID'];
                }
                // print_r($parentidmenu);exit;
                $row=$this->_dao->query("select *  from `dc_search_custom_catalogue` where mc_type='".$mc_type."' and MID='".$mid."' and Uid='".$uid."' and status=1  and  IsEnglish='".$isEnglish."'  order by ".($mc_type!=0?"binary(cataloguename) asc":"aod asc,binary(cataloguename) asc"));
                //sort($row,"pinyin_sort2");
                $rowarr = array();
                foreach($row as $val){
                    $rowarr[$val['ID']]=$val;
                }

                $iscatalogue_arr2=$this->getcataloguearr($iscatalogue_arr,$parentid,$parentidmenu,$rowarr,$isSubs);
                //如果此处hasnode返回1，但是$iscatalogue_arr2没有数据，说明此父目录下没有数据
                if(!empty($iscatalogue_arr2)){
                    $sear_arr['hasnode']=1;
                    $sear_arr['SubResults']=$iscatalogue_arr2;
                }else{
                    $sear_arr['hasnode']=0;
                    $sear_arr['SubResults']=array();
                }
            }else{
                $sear_arr['hasnode']=0;
                $sear_arr['SubResults']=array();
            }
        }
        $this->_dao->WriteLog($mid, $uid, $SignCS, $action,$actionstr, $this->getIP(), $this->getSystemType(), $this->getSystemVersion(),"获取自定义搜索2",'','',$mc_type);
        $arr['Success'] = 1;
        $arr['Message'] = "成功";
        if($isEnglish) $arr['Message'] = ("Success");
        $sear_zero[0]=$sear_arr;
        $needCompress = $params['needCompress'];  //判断是否封装
        if ($needCompress == 1 && empty($params['is_search'])) {
            $arr['Results'] = (gzencode(json_encode($sear_zero), 9));//压缩找个方法
        } else {
            $arr['Results'] = $sear_zero;
        }





        $json_string = $this->pri_JSON($arr);
        echo $json_string;
        return false;
    }

    public function kehu($params)
    {
        $querykey = Urldecode($params['querykey']);
        $GUID = $params['GUID'];
        $SignCS = $params['SignCS'];
        $mc_type = $params['mc_type'];
        $action = $params['action'];        //接口名称
        $isSubs = $params['isSubs'];

        $ip = $this->getIP();
        if (!empty($params['mc_type'])) $mc_type = $params['mc_type'];
        else $mc_type = 0;
        if (!empty($params['isEnglish'])) $isEnglish = $params['isEnglish'];
        else $isEnglish = 0;

        if (!empty($params['parentid'])) $parentid = $params['parentid'];
        else $parentid = 0;

        if (!empty($params['iscustomerdb'])) $iscustomerdb = $params['iscustomerdb'];
        else $iscustomerdb = 0;

        $isen = "";
        if ($isEnglish == 1) {
            $isen = "_en";
        }
        $arr = array(
            'Success' => '0',
            'Message' => ('失败'),
            'Results' => 'null',
        );
        if ($isEnglish) $arr['Message'] = 'Failed!';

        if (array_key_exists($GUID, $GLOBALS["testaccountarr"]) && $SignCS != '' && (in_array($ip, $GLOBALS["testip"]) || $GLOBALS["nowtest"] == 1)) {
            $user = $GLOBALS["testaccountuser"];
        } else {
            $user = $this->_dao->getUser($GUID, $SignCS, $mc_type);
        }
        $mid = $user['Mid'];
        $uid = $user['Uid'];

        if ($params['isSystemUser'] == 1 && $mc_type == 3) {
            $mid = '1';
            $uid = '147803';
        }
        //写死的，正式版去掉
        // $mid = 1;
        // $uid = 391935;
        $arrtem = $params;
        $actionstr = "";
        foreach ($arrtem as $k => $v) {
            $actionstr .= "&" . $k . "=" . $v;
        }
        if (!empty($querykey)) {//关键字不为空
            $sear_arr = $this->querykeysearch_kh($querykey, $isen, $isEnglish, $mid, $uid, $mc_type, $isSubs);
        } else {
            if ($params['isSystemUser'] == 1 && $mc_type == 3) {
                $selectsql = "select id,cataloguename as dtname,parentid as catalogueid,status,aod,createtime,updatetime,Uid,MID,isCatalogue,mc_type,IsEnglish,hasnode,hasleaves,
				nodelevel,D1ImageType,D1UnitType,D1UnitConv,D1UnitName,D1dtymd,PowerID,updatetime,isdel
				from  dc_customer_data_catalogue  where status=1 and isdel=0 and IsEnglish='" . $isEnglish . "'  and MID = '" . $mid . "' and Uid = '$uid' and mc_type = '$mc_type'  order by aod asc,ID desc";
            } else {
                $selectsql = "select id,cataloguename as dtname,parentid as catalogueid,status,aod,createtime,updatetime,Uid,MID,isCatalogue,mc_type,IsEnglish,hasnode,hasleaves,
				nodelevel,D1ImageType,D1UnitType,D1UnitConv,D1UnitName,D1dtymd,PowerID,updatetime,isdel
				from  dc_customer_data_catalogue where status=1 and  isdel=0 and IsEnglish='" . $isEnglish . "'  and MID = '" . $mid . "' and Uid = '$uid' and mc_type = '$mc_type'    order by aod asc, binary(dtname) asc,ID desc";
            }
            $sear = $this->_dao->query($selectsql);

            if ($params['isSystemUser'] == 1 && $mc_type == 3) {

            } else {
                usort($sear, "pinyin_sort");
            }

            $sear2 = array();
            foreach ($sear as $sk => $sv) {
                $sear2[$sk]['ID'] = $sv['id'];
                //$sear2[$sk]['dtname']= base64_encode($this->utf8ToGb2312($sv['dtname']));
                //$sear2[$sk]['dtname'] = $this->Gb2312Toutf8(base64_decode($sv['dtname']));
                $sear2[$sk]['dtname'] = iconv("GBK", "UTF-8",base64_decode($sv['dtname']));
                $sear2[$sk]['ImageType'] = $sv['D1ImageType'];
                $sear2[$sk]['catalogueid'] = $sv['catalogueid'];
                $sear2[$sk]['isCatalogue'] = $sv['isCatalogue'];
                $sear2[$sk]['aod'] = $sv['aod'];
                if ($sv['updatetime'] == '0000-00-00 00:00:00') {
                    $sear2[$sk]['updatetime'] = $sv['createtime'];
                }
                $sear2[$sk]['dtymd'] = $sv['D1dtymd'];
                $DTIDJson = array();
                $DTIDJson['DTID'] = $sv['id'];
                $DTIDJson['DataImage'] = $sv['D1ImageType'];
                $DTIDJson['unitconver'] = $sv['D1UnitConv'];
                //$DTIDJson['LineTitle']=base64_encode($sv['dtname']);
                $DTIDJson['unitstring'] = $sv['D1UnitName'];
                //$DTIDJson['unitstring'] = $sv['D1UnitName'];
                // $DTIDJson['DataType']=$sv[''];
                $sear2[$sk]['DTIDJson'][] = $DTIDJson;
            }

            //我的数据
            $sear_arr['ID'] = 0;
            $sear_arr['parentid'] = "";
            $sear_arr['cataloguename'] = "我的数据";
            if ($isEnglish) $sear_arr['cataloguename'] = ("My Data");
            if ($params['isSystemUser'] == 1 && $mc_type == 3) { //新钢的
                $sear_arr['cataloguename'] = "经营分析";
            }

            $sear_arr['status'] = '';
            $sear_arr['aod'] = '';
            $sear_arr['updatetime'] = '';

            $iscatalogue = [];
            $isnotcatalogue = array();
            $ids_arr = array();
            $iscatalogue_arr=array();
            foreach ($sear2 as $k => $v) {
                //if(empty($v['catalogueid'])){
                if ($v['catalogueid'] == $parentid && $v['isCatalogue'] == 0) {//判断这个父目录下的这一级有没有数据有数据这一级leaves赋值
                    $isnotcatalogue[$k] = $v;
                } else if ($v['isCatalogue'] == 1) {
                    $iscatalogue[$k] = $v;
                }
                //所有数据的所在目录
                if ($v['isCatalogue'] == 0) {
                    $ids_arr[] = $v['catalogueid'];
                }
                if ($v['isCatalogue'] == 0) {
                    $rowarr2[$v['catalogueid']][] = $v;
                }
            }
            // echo '<pre>';print_R($rowarr2);
            if (empty($iscatalogue)) {
                $startcatalogue = $this->_dao->query("select *  from `dc_customer_data_catalogue` where isdel=0 and  mc_type='" . $mc_type . "' and MID='" . $mid . "' and Uid='" . $uid . "' and status=1  and  IsEnglish='" . $isEnglish . "' order by aod asc,  binary(cataloguename) asc");
                //sort($startcatalogue,"pinyin_sort2");
                foreach ($startcatalogue as $k => $v) {//初始目录，下面的递归找下面的
                    $iscatalogue[$k] = '';//再查一遍看看有没有空目录
                }
            }

            if (!empty($isnotcatalogue)) {
                //echo '<pre>';print_R($isnotcatalogue);
                $i = 0;
                foreach ($isnotcatalogue as $ck => $cv) {//初始目录，下面的递归找下面的
                    $isnotcatalogue2[$i] = $cv;//再查一遍看看有没有空目录
                    $i++;

                }
                $sear_arr['hasleaves'] = 1;
                usort($isnotcatalogue, "pinyin_sort");
                if ($isSubs == 0) {
                    $sear_arr['hasleaves'] = 0;
                    $sear_arr['leaves'] = array();
                } else {
                    $sear_arr['leaves'] = $isnotcatalogue2;
                }
            } else {
                $sear_arr['hasleaves'] = 0;
                $sear_arr['leaves'] = array();
            }
            if (!empty($iscatalogue)) {//有目录的情况，递归查询列出目录
                foreach ($iscatalogue as $key => $val) {
                    if(is_array($val))
                    {
                        $iscatalogue_arr[$val['catalogueid']][] = $val;
                    }
                    
                }
                //getcataloguearr所有有目录关系的进行处理，key=catalogueid；
                $parentidarr = $this->_dao->query("select ID,parentid  from `dc_customer_data_catalogue`  where isdel=0 and  mc_type='" . $mc_type . "' and MID='" . $mid . "' and Uid='" . $uid . "'  and  IsEnglish='" . $isEnglish . "' and status=1  and isCatalogue=1 order by aod asc,binary(cataloguename) asc");
                // print_r($parentidarr);
                foreach ($parentidarr as $val) {
                    $parentidmenu[$val['parentid']][] = $val['ID'];
                }
                // print_r($parentidmenu);exit;
                $row = $this->_dao->query("select *  from `dc_customer_data_catalogue` where isdel=0 and  mc_type='" . $mc_type . "'  and MID='" . $mid . "' and Uid='" . $uid . "' and status=1  and  IsEnglish='" . $isEnglish . "' and isCatalogue=1 order by aod asc, binary(cataloguename) asc");
                //sort($row,"pinyin_sort2");
                foreach ($row as $val) {
                    $rowarr[$val['id']] = $val;
                }
                $iscatalogue_arr2 = $this->getcataloguearr_kehu($iscatalogue_arr, $parentid, $parentidmenu, $rowarr, $rowarr2, $isSubs);
                //如果此处hasnode返回1，但是$iscatalogue_arr2没有数据，说明此父目录下没有数据
                if (!empty($iscatalogue_arr2)) {
                    $sear_arr['hasnode'] = 1;
                    $sear_arr['SubResults'] = $iscatalogue_arr2;
                } else {
                    $sear_arr['hasnode'] = 0;
                    $sear_arr['SubResults'] = array();
                }
            } else {
                $sear_arr['hasnode'] = 0;
                $sear_arr['SubResults'] = array();
            }
        }
	//xiangbin add 20220913 start
	$selectsql="select id,cataloguename as dtname,parentid as catalogueid,status,aod,createtime,updatetime,Uid,MID,isCatalogue,mc_type,IsEnglish,hasnode,hasleaves,
				nodelevel,D1ImageType,D1UnitType,D1UnitConv,D1UnitName,D1dtymd,PowerID,updatetime,isdel
				from  dc_customer_data_catalogue where status=1 and  isdel=0 and IsEnglish='".$isEnglish."'  and MID = '".$mid."' and PowerID  like '%,$uid,%' and mc_type = '$mc_type' and isCatalogue=0    order by aod asc, binary(dtname) asc,ID desc";
   //$selectsql="select id,cataloguename as dtname,parentid as catalogueid,status,aod,createtime,updatetime,Uid,MID,isCatalogue,mc_type,IsEnglish,hasnode,hasleaves, nodelevel,D1ImageType,D1UnitType,D1UnitConv,D1UnitName,D1dtymd,PowerID,updatetime,isdel from dc_customer_data_catalogue where status=1 and isdel=0 and IsEnglish='0' and MID = '1' and PowerID like '%,452460,%' and mc_type = '0' and isCatalogue=0 order by aod asc, binary(dtname) asc,ID desc";
	//echo $selectsql;
	$sear = $this->_dao->query($selectsql);
	$sear2=array();
	$muluid=array();
    $ids_arr=array();
	foreach($sear as $sk=>$sv) {
		if(!in_array($sk,$muluid))
		{
			$muluid[]=$sv['catalogueid'];
		}
		$searinfo=array();
		$searinfo['ID']= $sv['id'];
		$searinfo['dtname']= iconv("GBK", "UTF-8",base64_decode($sv['dtname']));;
		$searinfo['ImageType']= $sv['D1ImageType'];
		$searinfo['catalogueid']= $sv['catalogueid'];
		$searinfo['isCatalogue']= $sv['isCatalogue'];
	
		$searinfo['aod']= $sv['aod'];
		if($sv['updatetime']=='0000-00-00 00:00:00'){
			$searinfo['updatetime']=$sv['createtime'];
		}
		$searinfo['dtymd']= $sv['D1dtymd'];
		

		$DTIDJson=array();
		$DTIDJson['DTID']=$sv['id'];
		$DTIDJson['DataImage']=$sv['D1ImageType'];
		$DTIDJson['unitconver']=$sv['D1UnitConv'];
		$DTIDJson['unitstring']=$sv['D1UnitName'] ;
//		$DTIDJson['unitstring']=$sv['D1UnitName'];
		$searinfo['DTIDJson'][]=$DTIDJson;
		$sear2[$sv['catalogueid']][]=$searinfo;

        if(empty($querykey)||(str_replace($querykey,'',$searinfo['dtname'])!=$searinfo['dtname']&&!in_array($sv['catalogueid'],$ids_arr)))
        {
            $ids_arr[] = $sv['catalogueid'];
        }
	}


    //print_r($ids);
	//我的数据
	$sear_arr2['ID']=1;
	$sear_arr2['parentid']="";
	$sear_arr2['cataloguename']="共享数据";
	if($isEnglish) $sear_arr2['cataloguename'] = base64_encode("Share Data");

	$sear_arr2['status']='';
	$sear_arr2['aod']='';
	$sear_arr2['updatetime']='';
	$muarr=array();
	foreach($muluid as $sv) 
	{
		//$mulu=array();
		$this->getParentPath($sv,$muarr);
		//$muarr[]=$mulu;
	}
    //print_r($ids_arr);
     foreach($muarr as $k1=>$v1)
     {
        foreach($v1 as $k2=>$v2)
        {
            if(empty($querykey)||(str_replace($querykey,'',$v2['dtname'])!=$v2['dtname']&&!in_array($v2['id'],$ids_arr)))
            {
                $ids_arr[] = $v2['id'];
            }
        }
     }
     //print_r($ids_arr);exit;
     $parentidarr4=array();
     foreach ($ids_arr as $key => $value) {//关键字目录所经过的路径父目录
 
         $parentidarr3 = $this->getparentid2($value, '');
 
         foreach ($parentidarr3 as $key2 => $value2) {
             $parentidarr4[] = $value2;
 
         }
     }
     $ids=array();
     foreach ($parentidarr4 as $k => $v) {
 
         //$ids[$v['parentid']]['ID'][] = $v['ID'];
         if(!in_array($v['ID'],$ids))
         {
            $ids[]=$v['ID'];
         }
         if(!in_array($v['parentid'],$ids))
         {
            $ids[]=$v['parentid'];
         }
 
     }
     

	if($sear2[0])
	{
        $sondata=array();
        foreach($sear2[0] as $key1=>$val1)
        {
            if($querykey==""||(str_replace($querykey,'',$val1['dtname'])!=$val1['dtname']))
            {
                $sondata[]=$val1;
            }
        }
        if($sondata)
        {
            $sear_arr2['hasleaves']=1;
            $sear_arr2['leaves']=$sondata;
        }
        else
        {
            $sear_arr2['hasleaves']=0;
            $sear_arr2['leaves']=array();
        }
	}
	else
	{
		$sear_arr2['hasleaves']=0;
		$sear_arr2['leaves']=array();
	}
     if($muarr)
	 {
		$sear_arr2['hasnode']=1;
		$sear_arr2['SubResults']=$this->getson(0,$sear2,$muarr,$querykey,$ids);
	 }
	 else
	 {
		$sear_arr2['hasnode']=0;
		$sear_arr2['SubResults']=array();
	 }


     
	 //exit;
	


	//xiangbin add 202209132 end
        $this->_dao->WriteLog($mid, $uid, $SignCS, $action, $actionstr, $this->getIP(), $this->getSystemType(), $this->getSystemVersion(), "获取自定义搜索2", '', '', $mc_type);
        $arr['Success'] = 1;
        $arr['Message'] = "成功";

        if ($isEnglish) $arr['Message'] = ("Success");
        $sear_zero[0] = $sear_arr;
        $sear_zero[1]=$sear_arr2;
        $needCompress = $params['needCompress'];  //判断是否封装
        if ($needCompress == 1 && empty($params['is_search'])) {
            $arr['Results'] = base64_encode(gzencode(json_encode($sear_zero), 9));//压缩找个方法
        } else {
            $arr['Results'] = $sear_zero;
        }
        $json_string = $this->pri_JSON($arr);
        echo $json_string;
        return false;

    }
	public function getson($id,$rowarr2,$node,$querykey,$parentidarr4)
	{
		//print_r($node);
		$carr=array();
		$arr2=$node[$id];
		//print_r($arr2);
		//echo "1111";
        $tmpquerykey=$querykey;
		$i=0;
		foreach($arr2 as $k=>$v){ 
		    
            if(!in_array($v['id'],$parentidarr4)&&$tmpquerykey!="")
            {
                continue;
            }
			$carr[$i]['ID']=$v['id'];
			$carr[$i]['parentid']=$v['catalogueid'];
			$carr[$i]['cataloguename']=$v['dtname'];
			$carr[$i]['status']=$v['status'];
			$carr[$i]['aod']=$v['aod'];
			$carr[$i]['updatetime']=$v['updatetime'];
			$carr[$i]['hasleaves']=1;
		     
            if (strstr($v['dtname'], $querykey) && !empty($querykey)) {//能匹配到说明查询的目录就是这个，那要显示这个目录下面的所有数据，这时候关键字后面的递归就不查了
                $querykey = '';
            }
			
			if(empty($rowarr2[$v['id']])){
				$carr[$i]['hasleaves']=0;
				$carr[$i]['leaves']=array();
			}else{
                if(!empty($querykey))
                {
                    $sondata=array();
                    foreach($rowarr2[$v['id']] as $key1=>$val1)
                    {
                        if($querykey==""||(str_replace($querykey,'',$val1['dtname'])!=$val1['dtname']))
                          {
                             $sondata[]=$val1;
                          }
                          if($sondata)
                          {
                            $carr[$i]['hasleaves']=1;
                            $carr[$i]['leaves']=$sondata;
                          }
                          else
                          {
                            $carr[$i]['hasleaves']=0;
				            $carr[$i]['leaves']=array();
                          }
                    }
                }
                else
                {
                    $carr[$i]['leaves']=$rowarr2[$v['id']];
                }
				
			}
				
			$soncatalogue=$node[$v['id']];
			//print_r($soncatalogue);
			//判断是否还有下一层目录
			if(!empty($soncatalogue)){

				$son_arr=$this->getson($v['id'],$rowarr2,$node,$querykey,$parentidarr4);
	
				if(empty($son_arr)){
					$carr[$i]['hasnode']=0;
				}else{
					$carr[$i]['hasnode']=1;
				}
				
				$carr[$i]['SubResults']=$son_arr;
			}else{
	
				$carr[$i]['hasnode']=0;
				$carr[$i]['SubResults']=array();
			}
            // if($carr[$i]['hasnode']==0&&$carr[$i]['hasleaves']==0&&!isset($parentidarr4[$v['id']]))
            // {
            //     return array();
            // }
			$i++;
		}
		//print_r($carr);
		return $carr;
		
	}
	public function getParentPath($id,&$arr=array()) 
	{
		
		$dc_customer_data_catalogue = $this->_dao->getrow("select id,cataloguename as dtname,parentid as catalogueid,status,aod,createtime,updatetime,Uid,MID,isCatalogue,mc_type,IsEnglish,hasnode,hasleaves,
		nodelevel,D1ImageType,D1UnitType,D1UnitConv,D1UnitName,D1dtymd,PowerID,updatetime,isdel  from `dc_customer_data_catalogue` where ID='".$id."'");
		
		foreach($arr[$dc_customer_data_catalogue['catalogueid']] as $k=>$v)
		{ 
			if($v['id']==$id)
			{
				return $arr;
			}
		}
        if($dc_customer_data_catalogue)
        {
            $dc_customer_data_catalogue['dtname'] = iconv("GBK", "UTF-8",base64_decode($dc_customer_data_catalogue['dtname']));
        }
		if($dc_customer_data_catalogue['catalogueid']!=0){
			$arr[$dc_customer_data_catalogue['catalogueid']][] = $dc_customer_data_catalogue;
			$this->getParentPath($dc_customer_data_catalogue['catalogueid'],$arr);
		}
		else
		{
			$arr[$dc_customer_data_catalogue['catalogueid']][] = $dc_customer_data_catalogue;
		}
		return $arr;
	}


//获取自定义搜索2
    public function SearchListGet2($params)
    {
        if ($this->checkGUID($params)) {
            if (!empty($params['iscustomerdb'])) $iscustomerdb = $params['iscustomerdb'];
            else $iscustomerdb = 0;
            if ($iscustomerdb == 0) { //客户与钢之家混合定制数据
                $this->gzj($params);
            }
            if ($iscustomerdb == 1) { //客户目录
                $this->kehu($params);
            }

        }
    }

    private function getparentid($id, $arr)
    {
        if ( empty($arr) )  $arr = array();
        $pid = $this->_dao->getrow("select parentid,ID  from `dc_search_custom_catalogue` where ID='" . $id . "'");
        if (empty($pid)) {
            return $arr;
        } else {
            $arr[] = $pid;
            return $this->getparentid((int)$pid['parentid'], $arr);
        }
    }

    private function getparentid2($id, $arr)
    {
        if ( empty($arr) )  $arr = array();
        $pid = $this->_dao->getrow("select parentid,id as ID  from `dc_customer_data_catalogue` where id='" . $id . "'");
        if (empty($pid)) {
            return $arr;
        } else {
            $arr[] = $pid;

            return $this->getparentid2($pid['parentid'], $arr);

        }

    }


    private function getcataloguearr($arr, $id, $parentidmenu, $rowarr, $isSubs)
    {
        //$ids='';

        //$sonid=$this->_dao->query("select ID  from `dc_search_custom_catalogue` where parentid='".$id."'");
        if ($id == '') $id = 0;

        $sonid = $parentidmenu[$id];

        $arr2 = array();
        foreach ($sonid as $k3 => $v3) {
            $arr2[$v3] = '';
        }

        //$arr为该用户的所有数据，$id代表父级目录，取父级目录下的当前层所有的与$arr该用户的所有数据取交集
        /* foreach($arr as $k1=>$v1){
    foreach($sonid as $k2=>$v2){
        if($v2['ID']==$k1){
            $arr2[$v2['ID']]=$v1;
        }
    }
    } */

        foreach ($arr as $k1 => $v1) {
            foreach ($arr2 as $k2 => $v2) {
                if ($k2 == $k1) {
                    $arr2[$k2] = $v1;
                }
            }
        }

        $i = 0;


        foreach ($arr2 as $k => $v) {


            $carr[$i]['ID'] = $k;
            //$row=$this->_dao->getrow("select parentid  from `dc_search_custom_catalogue` where ID=".$k);
            $row = $rowarr[$k];
            $carr[$i]['parentid'] = $row['parentid'];
            $carr[$i]['cataloguename'] =  $row['cataloguename'];
            $carr[$i]['status'] = $row['status'];
            $carr[$i]['aod'] = $row['aod'];
            $carr[$i]['updatetime'] = $row['updatetime'];
            $carr[$i]['hasleaves'] = 1;


            if ($isSubs == 0 || empty($v)) {
                $carr[$i]['hasleaves'] = 0;
                $carr[$i]['leaves'] = array();
            } else {
                $carr[$i]['leaves'] = $v;
            }

            //$soncatalogue=$this->_dao->query("select ID  from `dc_search_custom_catalogue` where parentid=".$k);
            $soncatalogue = $parentidmenu[$k];
            //判断是否还有下一层目录
            if (!empty($soncatalogue)) {

                /* 	foreach ($soncatalogue as $key => $val) {
            foreach ($arr as $key1 => $val1) {
        if($val['ID']==$key1){
        $son_arr[$val['ID']] = $val1;//$val['ID']代表目录id，$son_arr代表此目录下的所有
        //$ids[]=$val['ID'];
        }
        }
        } */

                $son_arr = $this->getcataloguearr($arr, $k, $parentidmenu, $rowarr, $isSubs);

                if ($son_arr == "") {
                    $carr[$i]['hasnode'] = 0;
                } else {
                    $carr[$i]['hasnode'] = 1;
                }

                $carr[$i]['SubResults'] = $son_arr;
            } else {

                $carr[$i]['hasnode'] = 0;
                $carr[$i]['SubResults'] = array();
            }
            $i++;
        }

        //print_R($ids);

        return $carr;

    }


    private function getcataloguearr_kehu($arr, $id, $parentidmenu, $rowarr, $rowarr2, $isSubs)
    {
        //$ids='';
        //$sonid=$this->_dao->query("select ID  from `dc_search_custom_catalogue` where parentid='".$id."'");
        if ($id == '') $id = 0;
        $sonid = $parentidmenu[$id];
        $arr2 = array();
        foreach ($sonid as $k3 => $v3) {
            $arr2[$v3] = '';
        }

        foreach ($arr as $k1 => $v1) {
            foreach ($arr2 as $k2 => $v2) {
                if ($k2 == $k1) {
                    $arr2[$k2] = $v1;
                }
            }
        }
        $i = 0;
        foreach ($arr2 as $k => $v) {
            $carr[$i]['ID'] = $k;
            //$row=$this->_dao->getrow("select parentid  from `dc_search_custom_catalogue` where ID=".$k);
            $row = $rowarr[$k];
            $carr[$i]['parentid'] = $row['parentid'];
            //$carr[$i]['cataloguename']=base64_encode($row['cataloguename']);
            //$carr[$i]['cataloguename'] = $row['cataloguename'];
            $carr[$i]['cataloguename'] = iconv("GBK", "UTF-8",base64_decode($row['cataloguename']));;
            $carr[$i]['status'] = $row['status'];
            $carr[$i]['aod'] = $row['aod'];
            $carr[$i]['updatetime'] = $row['updatetime'];
            $carr[$i]['hasleaves'] = 1;

            if ($isSubs == 0 || empty($rowarr2[$k])) {
                $carr[$i]['hasleaves'] = 0;
                $carr[$i]['leaves'] = array();
            } else {
                $carr[$i]['leaves'] = $rowarr2[$k];
            }
            //$soncatalogue=$this->_dao->query("select ID  from `dc_search_custom_catalogue` where parentid=".$k);
            $soncatalogue = $parentidmenu[$k];
            //判断是否还有下一层目录
            if (!empty($soncatalogue)) {
                $son_arr = $this->getcataloguearr_kehu($arr, $k, $parentidmenu, $rowarr, $rowarr2, $isSubs);
                if ($son_arr == "") {
                    $carr[$i]['hasnode'] = 0;
                } else {
                    $carr[$i]['hasnode'] = 1;
                }
                $carr[$i]['SubResults'] = $son_arr;
            } else {
                $carr[$i]['hasnode'] = 0;
                $carr[$i]['SubResults'] = array();
            }
            $i++;
        }
        //print_R($ids);
        return $carr;
    }

//定制数据排序 add by zhangcun 2017/7/18
    public function SearchListSwap($params)
    {// 设定id1的sort小于id2的sort
        $mc_type=$params['mc_type']?$params['mc_type']:0;
        if ($this->checkGUID($params)) {
            $GUID = $params['GUID'];
            $SignCS = $params['SignCS'];
            $id1 = $params['id1'];
            $id2 = $params['id2'];
            $ip = $this->getIP();
            if (array_key_exists($GUID, $GLOBALS["testaccountarr"]) && $SignCS != '' && (in_array($ip, $GLOBALS["testip"]) || $GLOBALS["nowtest"] == 1)) {
                $user = $GLOBALS["testaccountuser"];
            } else {
                $user = $this->_dao->getUser($GUID, $SignCS, $mc_type);
            }
            $mid = $user['Mid'];
            $uid = $user['Uid'];

            $maxsort = $this->_dao->getOne("select max(aod) from dc_search_custom where IsEnglish!=1 and MID='$mid' and Uid ='$uid'");
            $maxsort++;

            $mark = $this->_dao->getOne("select 1 from dc_search_custom where IsEnglish!=1 and MID='$mid' and Uid ='$uid' and aod=0");//对于之前添加的定制数据加序号
            if ($mark) {
                $msort = $maxsort;
                $sql = "select id from dc_search_custom where IsEnglish!=1 and MID='$mid' and Uid ='$uid' and aod=0 order by aod desc,id desc";
                $array = $this->_dao->query($sql);

                foreach ($array as $ii => $id) {
                    //$sort[$id]=$msort;
                    $sql = "update dc_search_custom set aod=" . $msort . " where id=" . $id[id];//echo $sql."<br>";
                    $this->_dao->execute($sql);
                    $msort++;
                }
            }

            $sql = "select aod from dc_search_custom where IsEnglish!=1 and id='$id1' ";
            $sort1 = $this->_dao->getOne($sql);
            $sql = "select aod from dc_search_custom where IsEnglish!=1 and id='$id2' ";
            $sort2 = $this->_dao->getOne($sql);

            $sql1 = "update dc_search_custom set aod=$sort2 where id=$id1";
            $sql2 = "update dc_search_custom set aod=$sort1 where id=$id2";
            //echo $sql1."<br>".$sql2;exit;
            $this->_dao->execute($sql1);
            $this->_dao->execute($sql2);

        }
        exit;
    }

//删除自定义搜索
    public function DelSearchList($params)
    {
        if ($this->checkGUID($params)) {
            $GUID = $params['GUID'];
            $SignCS = $params['SignCS'];
            $action = $params['action'];        //接口名称
            $ip = $this->getIP();
            if (!empty($params['mc_type'])) $mc_type = $params['mc_type'];
            else $mc_type = 0;
            if (!empty($params['isEnglish'])) $isEnglish = $params['isEnglish'];
            else $isEnglish = 0;

            $arr = array(
                'Success' => '0',
                'Message' => ('失败'),
                'Results' => 'null',
            );
            if ($isEnglish) $arr['Message'] = 'Failed!';

            if (array_key_exists($GUID, $GLOBALS["testaccountarr"]) && $SignCS != '' && (in_array($ip, $GLOBALS["testip"]) || $GLOBALS["nowtest"] == 1)) {
                $user = $GLOBALS["testaccountuser"];
            } else {
                $user = $this->_dao->getUser($GUID, $SignCS, $mc_type);
            }
            $mid = $user['Mid'];
            $uid = $user['Uid'];

            $arrtem = $params;
            //array_pop($arrtem);
            $actionstr = "";
            foreach ($arrtem as $k => $v) {
                $actionstr .= "&" . $k . "=" . $v;
            }
            /*2017/07/20 change */
            // $this->_dao->execute("delete from dc_search_custom where MID = ".$mid." and ID = ".$params['ID']." and mc_type = '$mc_type' ");


            //add 导出并且删除加入回收站
            $selectsql = "select ID,catalogueid,dtname from dc_search_custom  where IsEnglish='" . $isEnglish . "'   and MID = '" . $mid . "' and Uid = '$uid' and mc_type = '$mc_type' and ID ='" . $params['ID'] . "' order by binary(dtname) asc,aod desc,ID desc";

            $row = $this->_dao->getrow($selectsql);
            $sear[0]['id'] = $row['ID'];
            $sear[0]['cid'] = $row['catalogueid'];
            $sear[0]['mid'] = $mid;
            $sear[0]['uid'] = $uid;
            $sear[0]['mc_type'] = $mc_type;
            $sear[0]['isEnglish'] = $isEnglish;
            $sear[0]['isCatalogue'] = '0';
            $sear[0]['cj'] = '1';

            $row['dtname']  = str_replace("'","\'", $row['dtname'] );
            $this->_dao->execute("insert into dc_derivedjson  
								(uid,MID,IsEnglish,mc_type,dctime,json,title,isCatalogue,type,isdel)
								values
								('" . $uid . "','" . $mid . "','" . $isEnglish . "','" . $mc_type . "',NOW(),'" . json_encode($sear) . "','" . $row['dtname'] . "',0,1,0)");

            //数据先保存入回收站dc_search_custom_recycle
            $this->_dao->execute("INSERT INTO dc_search_custom_recycle (`ID`,`MID`,`Uid`,`dtname`,`dtnameshort`,`DTID1`,`DTID1Sub`,`Data1Type1`,`Data1Pre`,`Data1AddSub`,`Data1AddSubHundCore`,`Data1Type2`,`Data1Type3`,`Data1Type4`,`Data1Type5`,`DTID2`,`DTID2Sub`,`Data2Type`,`DTID3`,`DTID3Sub`,`Data3Type`,`DTID4`,`DTID4Sub`,`Data4Type`,`aod`,`ImageType`,`ImageTitle`,`Data1Image`,`Data2Image`,`Data3Image` ,`Data4Image`,`zhou1`,`zhou2` ,`zhou3`,`zhou4`,`dtname_en`,`dtnameshort_en`,`IsEnglish`,`mc_type`,`isbg`,`isjz`,`color`,`isfg`,`LineTitle1`,`LineTitle2`,`LineTitle3`,`LineTitle4`,`GongshiID1`,`GongshiID2`,`GongshiID3`,`GongshiID4`,`Data1Type`,`catalogueid`,`createtime`,`updatetime`,`DTIDJson` ,`LineTitle1_bak`,`LineTitle2_bak` ,`LineTitle3_bak` ,`LineTitle4_bak` ,`ImageTitle_bak`,`DateFormat`,`ChartExtLineStyle`
			)select `ID`,`MID`,`Uid`,`dtname`,`dtnameshort`,`DTID1`,`DTID1Sub`,`Data1Type1`,`Data1Pre`,`Data1AddSub`,`Data1AddSubHundCore`,`Data1Type2`,`Data1Type3`,`Data1Type4`,`Data1Type5`,`DTID2`,`DTID2Sub`,`Data2Type` ,`DTID3`,`DTID3Sub`,`Data3Type`,`DTID4`,`DTID4Sub`,`Data4Type`,`aod`,`ImageType` ,`ImageTitle`,`Data1Image`,`Data2Image`,`Data3Image`,`Data4Image`,`zhou1`,`zhou2`,`zhou3`,`zhou4`,`dtname_en`,`dtnameshort_en`,`IsEnglish`,`mc_type`,`isbg`,`isjz`,`color`,`isfg`,`LineTitle1`,`LineTitle2`,`LineTitle3`,`LineTitle4`,`GongshiID1`,`GongshiID2`,`GongshiID3`,`GongshiID4`,`Data1Type`,`catalogueid`,`createtime`,`updatetime`,`DTIDJson`,`LineTitle1_bak`,`LineTitle2_bak`,`LineTitle3_bak`,`LineTitle4_bak`,`ImageTitle_bak`,`DateFormat`,`ChartExtLineStyle`
			from `dc_search_custom` where  mc_type='" . $mc_type . "' and MID='" . $mid . "' and Uid='" . $uid . "'  and IsEnglish='" . $isEnglish . "' and ID=" . $params['ID']);
            $deltime = date("Y-m-d H:m:s");
            $this->_dao->execute("update dc_search_custom_recycle set deltime='" . $deltime . "'  where   ID ='" . $params['ID'] . "' and mc_type='" . $mc_type . "' and MID='" . $mid . "' and Uid='" . $uid . "'  and  IsEnglish='" . $isEnglish . "' ");

            //删除回收站对应的dc_search_custom表数据
            $this->_dao->execute("delete from dc_search_custom where  mc_type='" . $mc_type . "' and MID='" . $mid . "' and Uid='" . $uid . "'  and IsEnglish='" . $isEnglish . "' and  ID=" . $params['ID']);


            $this->_dao->WriteLog($mid, $uid, $SignCS, $action, $actionstr, $this->getIP(), $this->getSystemType(), $this->getSystemVersion(), "删除自定义搜索", '', '', $mc_type);

            $arr['Success'] = 1;
            $arr['Message'] = "删除成功";

            if ($isEnglish) $arr['Message'] = ("Success");

            $json_string = $this->pri_JSON($arr);
            echo $json_string;
            return false;

        }

    }

    public function gethot($params)
    {
        if (!empty($params['mc_type'])) $mc_type = $params['mc_type'];
        else $mc_type = 0;
        $ip = $this->getIP();
        //echo $this->getendate("2018-02-13 09:24:42");exit;
        if ($this->checkGUID($params)) {

            $pre = "<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">\n<html xmlns=\"http://www.w3.org/1999/xhtml\">\n<head>\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\" />\n<title>无标题文档</title>\n<style type=\"text/css\">\n*{\n	margin: 0;\n	padding: 0;\n	font-size: 12px;\n}\na:link,a:visited{\n	text-decoration: none;\n}\n.clear{ clear:both; overflow:hidden; height: 0;}\n.newsList{\n	width: 250px;\n	overflow: hidden;\n	font-family: Arial, Helvetica, sans-serif; \n	padding: 0 0 12px 0;\n}\n.newsList .tit{\n	height: 32px;\n	line-height: 32px;\n	text-indent: 10px;\n	font-weight: bold;\n	color: #000;\n}\n.newsList li{\n	clear: both;\n	height: 26px;\n	line-height: 26px;\n	overflow: hidden;\n	text-indent: 10px;\n	position: relative;\n	list-style: none;\n}\n.newsList li a{\n	color: #000;\n	display: block;\n	float: left;\n \n	width: 190px;\n \n}\n.newsList li a:hover{\n	color: #9A0000;\n}\n.newsList li span{\n	position: absolute;\n	top: 0;\n	right: 10px;\n	color: #9b9b9b;\n}\n</style>\n</head>\n\n<body>\n\n\n	<div class=\"newsList\">\n    	<div class=\"tit\">Latest News</div>\n        <div class=\"clear\"></div>\n        <div class=\"content\">\n        	<ul>";
            $behind = "</ul>\n        </div>\n    \n    \n    </div>\n\n\n\n\n\n\n</body>\n</html>\n";

            $arr = array(
                'Success' => '0',
                'Message' => 'Failed!',
                'Results' => 'null',
            );
            $GUID = $params['GUID'];
            $SignCS = $params['SignCS'];
            $action = $params['action'];        //接口名称

            if (array_key_exists($GUID, $GLOBALS["testaccountarr"]) && $SignCS != '' && (in_array($ip, $GLOBALS["testip"]) || $GLOBALS["nowtest"] == 1)) {
                $user = $GLOBALS["testaccountuser"];
            } else {
                $user = $this->_dao->getUser($GUID, $SignCS, $mc_type);
            }
            $mid = $user['Mid'];
            $uid = $user['Uid'];

            $arrtem = $params;
            foreach ($arrtem as $k => $v) {
                $actionstr .= "&" . $k . "=" . $v;
            }

            $list = $this->endao->query("SELECT distinctrow newsid,ntitle,ndate,fmemo FROM news_column WHERE `nfocus`=1  and channelid in (1,2,4) ORDER BY ndate DESC limit 12");

            $content = "";
            $content .= $pre;
            foreach ($list as $tem) {
                $link = 'http://newsen.steelhome.cn/' . Date("Y", strtotime($tem[ndate])) . '/' . Date("m", strtotime($tem[ndate])) . '/' . Date("d", strtotime($tem[ndate])) . '/n' . $tem[newsid] . '.html';

                $content .= "<li>" . "<a href=\"" . $link . "\" target=\"_blank\">" . $this->getentitle($tem[ntitle], 28) . " </a><span>" . $this->getendate($tem[ndate]) . "</span></li>";
            }
            $content .= $behind;

            $this->_dao->WriteLog($mid, $uid, $SignCS, $action, $actionstr, $this->getIP(), $this->getSystemType(), $this->getSystemVersion(), "获取英文聚焦", '', '', $mc_type);

            $arr['Success'] = 1;
            $arr['Message'] = base64_encode("success!");
            $arr['Result'] = base64_encode($content);


            $json_string = $this->pri_JSON($arr);
            echo $json_string;
            return false;
        }

    }

    function getentitle($str, $len = 45)
    {
        $ss = "";
        $etitle = "";
        $strlen = strlen($str);
        if ($strlen < $len) return $str;
        for ($i = 0; $i < $strlen; $i++) {
            if (trim($str[$i]) == "") {
                if ($i > $len) {
                    $etitle .= " ...";
                    break;
                } else $etitle .= $ss . $str[$i];
                $ss = "";
            } else {
                $ss .= $str[$i];
            }
        }
        return $etitle;
    }

    function getendate($date)
    {
        return date("M. d", strtotime($date));
    }

    function cut_str($string, $sublen, $start = 0, $code = 'gbk')
    {
        if ($code == 'UTF-8') {
            $pa = "/[\x01-\x7f]|[\xc2-\xdf][\x80-\xbf]|\xe0[\xa0-\xbf][\x80-\xbf]|[\xe1-\xef][\x80-\xbf][\x80-\xbf]|\xf0[\x90-\xbf][\x80-\xbf][\x80-\xbf]|[\xf1-\xf7][\x80-\xbf][\x80-\xbf][\x80-\xbf]/";
            preg_match_all($pa, $string, $t_string);

            if (count($t_string[0]) - $start > $sublen) return join('', array_slice($t_string[0], $start, $sublen)) . "...";
            return join('', array_slice($t_string[0], $start, $sublen));
        } else {
            $start = $start * 2;
            $sublen = $sublen * 2;
            $strlen = strlen($string);
            $tmpstr = '';

            for ($i = 0; $i < $strlen; $i++) {
                if ($i >= $start && $i < ($start + $sublen)) {
                    if (ord(substr($string, $i, 1)) > 129) {
                        $tmpstr .= substr($string, $i, 2);
                    } else {
                        $tmpstr .= substr($string, $i, 1);
                    }
                }
                if (ord(substr($string, $i, 1)) > 129) $i++;
            }
            if (strlen($tmpstr) < $strlen) $tmpstr .= "";
            return $tmpstr;
        }
    }


    public function checkGUID($params)
    {
        //alert( $this->getIP());

        $GUID = $params['GUID'];
        $SignCS = $params['SignCS'];
        $action = $params['action'];        //接口名称
        $ip = $this->getIP();
        if (!empty($params['mc_type'])) $mc_type = $params['mc_type'];
        else $mc_type = 0;
        if (!empty($params['isEnglish'])) $isEnglish = $params['isEnglish'];
        else $isEnglish = 0;

        if (array_key_exists($GUID, $GLOBALS["testaccountarr"]) && $SignCS != '' && (in_array($ip, $GLOBALS["testip"]) || $GLOBALS["nowtest"] == 1)) {
            $user = $GLOBALS["testaccountuser"];
        } else {
            $user = $this->_dao->getUser($GUID, $SignCS, $mc_type);
        }
        $arrtem = $params;
        //array_pop($arrtem);
        $actionstr = "";
        foreach ($arrtem as $k => $v) {
            $actionstr .= "&" . $k . "=" . $v;
        }

        if (!empty($user)) {
            //最后活动
            $this->_dao->execute("UPDATE app_session_temp SET LastDate=NOW(), LastAction='$action' WHERE GUID='$GUID'");
            //日志处理
            $this->_dao->WriteLog($user['Mid'], $user['Uid'], $SignCS, $action, $actionstr, $this->getIP(), $this->getSystemType(), $this->getSystemVersion(), "checkGUID", '', '', $mc_type);
            return true;
        } else {
            $arr = array(
                'Success' => '0',
                'Message' => ('非法操作')
            );
            if ($isEnglish) $arr['Message'] = 'Illegal Operation';
            $json_string = $this->pri_JSON($arr);
            echo $json_string;
            return false;
        }
    }

    /*public function getIP(){
      if (!empty($_SERVER['HTTP_CLIENT_IP']))
    $ip = $_SERVER['HTTP_CLIENT_IP'];
    else if (!empty($_SERVER['HTTP_X_FORWARDED_FOR']))
    $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
    else
    $ip = $_SERVER['REMOTE_ADDR'];

    return $ip;
    }*/

//取得设备类型
    private function getSystemType()
    {
        return " ";
    }

//取得系统版本
    private function getSystemVersion()
    {
        return " ";
    }

    private function testEncoding($str)
    {   //echo $str."<br>";
        $code = mb_detect_encoding($str, array("UTF-8", "ASCII", "GB2312", "GBK", "BIG5"));
        if ($code == "UTF-8") return 1;
        else return 0;
    }

    private function P($arr, $type = 0)
    {
        if ($_GET['debug'] == 1) {
            if ($type) print"<pre>";
            print_r($arr);
            if ($type) print"</pre>";
        }
    }

    private function haschildnode($code, $mc_type = 0)
    {//前提是code命名规范，否则取的信息不对，或者将层数len 从外面带过来
        $code = trim($code);
        if (strlen($code) == 0) return "";
        $len = (int)((strlen($code) - 1) / 2) + 1;
        //$this->P("select 1 from dc_code_datatype where scode".$len."='$code' and mc_type='$mc_type' and status=1"."<br>");
        $sql = "select 1 from dc_code_class where mcode='$code' and mc_type='$mc_type' and status=1";//echo $sql."<br>";
        return $this->_dao->getone($sql);
        /*if($this->_dao->getone($sql)==""){
    if($len>0&&$len<4){
    return $this->_dao->getone("select 1 from dc_code_datatype where scode".$len."='$code' and scode".($len+1)."='' and mc_type='$mc_type' and status=1");
    }elseif($len==4){
    return $this->_dao->getone("select 1 from dc_code_datatype where scode".$len."='$code' and mc_type='$mc_type' and status=1");
    }
    }else{
    return 1;
    }
    return "";*/
    }

    public function getRecordInfo($params)
    {
        $SignCS = $params['SignCS'];
        $Type = $params['Type'];
        $mode = $params['mode'];
        $GUID = $params['GUID'];
        $mc_type = $params['mc_type'];
        $isEnglish = $params['isEnglish'];

        //file_put_contents("/tmp/getrecordinfo.txt","\r\n---------".date("Y-m-d H:i:s")."::\n".print_r($params,true),FILE_APPEND);

        $arr = array(
            "Success" => "0",
            "Message" => ("查询失败"),
            "canRecord" => "0",
        );
        if ($mc_type == "1") {
            $usermes = $this->_dao->getRow("select * from app_session_temp where SignCS='$SignCS' and GUID='$GUID' and mc_type='$mc_type' order by LastDate desc limit 1");
            $userid = $usermes['Uid'];
            $lid = $this->_dao->getone("select id from app_license where mid='" . $usermes['Mid'] . "' and mc_type='$mc_type'");
            $modelid = $this->drcdao->getone("select id from ng_price_model where modeltype='$Type' order by date desc,id desc limit 1");

            if ($userid && $modelid) {
                $sql = "select id,URL from Ng_RecordManage where modelid = '$modelid' order by id";
                $list = $this->drcdao->query($sql);
                foreach ($list as &$ar) {
                    //$ar["URL"]="http://iwww.steelhome.cn/data/uploadfile/recorder/".$ar["URL"];//测试
                    $ar["URL"] = "http://dc.steelhome.cn/uploadfile/recorder/" . $ar["URL"];//正式
                }
                $sql = "select uid from ng_price_model_detail where modelprice_type='$Type' and modelid='$modelid' ";
                $userids = $this->drcdao->getOnes($sql);
                foreach ($userids as $uid) {
                    $uids[$uid] = 1;
                }//echo "<pre>";print_r($uids);
                $ordernos = $this->_dao->query("select uid,privilege,orderno from app_license_privilege where mc_type='$mc_type' and lid='$lid'");

                foreach ($ordernos as $ar2) {
                    if ($ar2["uid"] == $userid) {
                        $mypower = $ar2["privilege"];
                        $mypower = explode(",", $mypower);
                        $mypower = $mypower[$Type - 1];

                        $myno = $ar2["orderno"];
                        $myno = explode(",", $myno);
                        $myno = $myno[$Type - 1];

                        break;
                    }
                }
                //print"<pre>";print_r($ordernos);
                $arr["canRecord"] = "1";

                $isjc = $this->drcdao->getone("select 1 from ng_price_model where ismakepricy=1 and id='$modelid'");
                if ($isjc == "") {
                    if ($mypower != 1) {
                        $arr["canRecord"] = "0";
                    } else {
                        foreach ($ordernos as $ar2) {
                            $power = $ar2["privilege"];
                            $power = explode(",", $power);
                            $power = $power[$Type - 1];

                            $no = $ar2["orderno"];
                            $no = explode(",", $no);
                            $no = $no[$Type - 1];

                            if ($power == 2) {
                                if ($uids[$ar2["uid"]] != 1) {
                                    $arr["canRecord"] = "0";
                                    break;
                                }
                            } elseif ($power == 1) {
                                if ($ar2["uid"] == $userid) {
                                    if ($uids[$ar2["uid"]] == 1) {
                                        $arr["canRecord"] = "0";
                                        break;
                                    }
                                } else {
                                    if ($no > $myno || $uids[$ar2["uid"]] != 1) {
                                        $arr["canRecord"] = "0";
                                        break;
                                    }
                                }
                            } elseif ($power == 3) {
                                if ($uids[$ar2["uid"]] == 1) {
                                    $arr["canRecord"] = "0";
                                    break;
                                }
                            }
                        }
                    }
                } else {
                    $arr["canRecord"] = "0";
                }
            } else {
                $arr["canRecord"] = "0";
            }
            if (empty($list)) $list = array();
            $arr["Success"] = "1";
            $arr["Message"] = ("查询成功");
            $arr["playURLs"] = $list;
        } else {
            $arr["Message"] = ("商户类型错误");
        }
        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    public function uploadRecord($params)
    {
        $SignCS = $params['SignCS'];
        $Type = $params['Type'];
        $mode = $params['mode'];
        $operator = $params['operator'];
        $recordid = $params['recordid'];
        $GUID = $params['GUID'];
        $mc_type = $params['mc_type'];
        $recorder = $_FILES['recorder'];
        //print"<pre>";print_r($params);print_r($_FILES);exit;
        $arr = array(
            "Success" => "0",
            "Message" => ("失败"),
        );
        if ($mc_type == "1") {
            $modelid = $this->drcdao->getone("select id from ng_price_model where modeltype='$Type' order by id desc limit 1");
            $userid = $this->_dao->getone("select Uid from app_session_temp where SignCS='$SignCS' and GUID='$GUID' and mc_type='$mc_type' order by LastDate desc limit 1");

            if ($modelid && $userid) {
                $hasnopower = 0;
                $ordernos = $this->_dao->query("select uid,privilege,orderno from app_license_privilege where mc_type='$mc_type'");
                $userids = $this->drcdao->getOnes("select uid from ng_price_model_detail where modelprice_type='$Type' and modelid='$modelid' ");
                foreach ($userids as $uid) {
                    $uids[$uid] = 1;
                }
                foreach ($ordernos as $ar2) {
                    if ($ar2["uid"] == $userid) {
                        $mypower = $ar2["privilege"];
                        $mypower = explode(",", $mypower);
                        $mypower = $mypower[$Type - 1];

                        $myno = $ar2["orderno"];
                        $myno = explode(",", $myno);
                        $myno = $myno[$Type - 1];

                        break;
                    }
                }
                $isjc = $this->drcdao->getone("select 1 from ng_price_model where ismakepricy=1 and id='$modelid'");
                if ($isjc == "") {
                    if ($mypower != 1) {
                        $hasnopower = 1;
                    } else {
                        foreach ($ordernos as $ar2) {
                            $power = $ar2["privilege"];
                            $power = explode(",", $power);
                            $power = $power[$Type - 1];

                            $no = $ar2["orderno"];
                            $no = explode(",", $no);
                            $no = $no[$Type - 1];

                            if ($power == 2) {
                                if ($uids[$ar2["uid"]] != 1) {
                                    $hasnopower = 2;
                                    break;
                                }
                            } elseif ($power == 1) {
                                if ($ar2["uid"] == $userid) {
                                    if ($uids[$ar2["uid"]] == 1) {
                                        $hasnopower = 2;
                                        break;
                                    }
                                } else {
                                    if ($no > $myno) {
                                        $hasnopower = 1;
                                        break;
                                    }
                                    if ($uids[$ar2["uid"]] != 1) {
                                        $hasnopower = 2;
                                        break;
                                    }
                                }
                            } elseif ($power == 3) {
                                if ($uids[$ar2["uid"]] == 1) {
                                    $hasnopower = 2;
                                    break;
                                }
                            }
                        }
                    }
                } else {
                    $hasnopower = 2;
                }

                if ($hasnopower) {
                    if ($hasnopower == 1) $arr["Message"] = ("用户无权限");
                    elseif ($hasnopower == 2) $arr["Message"] = ("现在不能操作录音");
                } else {
                    if ($operator == 1) {
                        $filename = "ly" . date("YmdHis") . rand(1000, 9999) . "." . $this->gethouzhui($recorder["name"]);

                        if ($recorder != "") {
                            $this->uploadfile($recorder, $filename);
                            $sql = "insert into Ng_RecordManage (URL,modelid,userid,createtime) values ('$filename','$modelid','$userid',NOW())";
                            $this->drcdao->execute($sql);
                            $arr["Message"] = ("操作成功");
                            $arr["Success"] = 1;
                        } else {
                            $arr["Message"] = ("录音文件丢失");
                        }
                    } elseif ($operator == 2) {
                        if ($recordid) {
                            $sql = "delete from Ng_RecordManage where modelid='$modelid' and userid='$userid' and id='$recordid'";
                            $this->drcdao->execute($sql);
                            $arr["Message"] = ("操作成功");
                            $arr["Success"] = 1;
                        } else {
                            $arr["Message"] = ("传参错误");
                        }
                    } else {
                        $arr["Message"] = ("操作类型错误");
                    }
                }
            } else {
                $arr["Message"] = ("用户信息未找到，操作失败");
            }
        } else {
            $arr["Message"] = ("商户类型错误");
        }

        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    private function uploadfile($content, $name, $dir = '')
    {
        if (empty($content)) die('empty file');
        //$dir = "/usr/local/www/www.steelhome.cn/data/uploadfile/recorder"; //测试
        $dir = "/usr/local/www/dc.steelhome.cn/uploadfile/recorder"; //正式
        $file = $content;
        if (!file_exists($dir)) {
            mkdir($dir, 0777);
        }
        move_uploaded_file($file['tmp_name'], $dir . "/" . $name) or die('upload fail');
        chmod($dir . "/" . $name, 0777);
        //echo "//iwww.steelhome.cn/data/uploadfile/recorder/$name";
    }

    private function gethouzhui($name)
    {
        $houzhui = "";
        $arr = explode(".", $name);
        if (count($arr) < 2) return $houzhui;
        foreach ($arr as $i) {
            $houzhui = $i;
        }
        return $houzhui;
    }

    public function classlist2search($params)
    {
        echo "in<br><pre>";
        exit;
        //header("Content-type: text/html; charset=UTF-8");
        $searchname = base64_decode($params["searchname"]);
        //$searchname=str_replace(" "," ",$searchname);
        //$searchname=str_replace(chr(161)," ",$searchname);
        //$searchname=str_replace(chr(32)," ",$searchname);
        $searcharr = explode(" ", $searchname);
        //echo $params["searchname"][4]."|".chr("169")."|";
        //echo"<pre>";print_r($searcharr);exit;
        $params['is_search'] = 1;
        //echo microtime()."<br>";
        $arr = $this->doclasslistget2($params);

        foreach ($arr['Results'] as $k => $v) {

        }
        //echo microtime()."<br>";
        //foreach($arr as $)
        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    public function DTIDGetResult($params)
    {
        if (!empty($params['isEnglish'])) $isEnglish = $params['isEnglish'];
        else $isEnglish = 0;

        $notincludePS_type = '';
        //获取已购买数据
        $GUID = $params['GUID'];
        $SignCS = $params['SignCS'];
        $mc_type = $params['mc_type'];
        $action = $params['action'];        //接口名称
        $DTIDDTIDSubs = json_decode('[{ "DTID":"109"},{ "DTID":"110"}]', true);
        $ids = '';
        foreach ($DTIDDTIDSubs as $k => $v) {
            if (empty($ids)) {
                $ids = "'" . $v['DTID'] . "'";
            } else {
                $ids = $ids . ",'" . $v['DTID'] . "'";
            }
        }

        if (!empty($params['mc_type'])) $mc_type = $params['mc_type'];
        else $mc_type = 0;
        $needCompress = $params['needCompress'];  //判断是否封装
        $ip = $this->getIP();


        if (!empty($params['mc_type'])) $mc_type = $params['mc_type'];
        else $mc_type = 0;
        if (!empty($params['isEnglish'])) $isEnglish = $params['isEnglish'];
        else $isEnglish = 0;
        $needCompress = $params['needCompress'];  //判断是否封装

        $isen = "";
        if ($isEnglish == 1) $isen = "_en";


        $isuse = "0";
        if ($isEnglish == 1) $isuse = "1";

        $arr = array(
            'Success' => '0',
            'Message' => '失败',
            'Results' => 'null',
        );
        if ($isEnglish) $arr['Message'] = 'Failed!';

        if (array_key_exists($GUID, $GLOBALS["testaccountarr"]) && $SignCS != '' && (in_array($ip, $GLOBALS["testip"]) || $GLOBALS["nowtest"] == 1)) {
            $user = $GLOBALS["testaccountuser"];
        } else {
            $user = $this->_dao->getUser($GUID, $SignCS, $mc_type);
        }
        if (empty($user)) {
            $arr = array(
                'Success' => '0',
                'Message' => base64_encode('无此用户'),
                'Results' => 'null',
            );
            if ($isEnglish) $arr['Message'] = base64_encode('Wrong Account!');
            $json_string = $this->pri_JSON($arr);
            echo $json_string;
            exit;
        }
        $mid = $user['Mid'];
        $uid = $user['Uid'];
        $arrtem = $params;
        foreach ($arrtem as $k => $v) {
            $actionstr .= "&" . $k . "=" . $v;
        }

        //$BUYDATA =$this->_dao->getOnes("select DataType from dc_custom_order left join app_license on dc_custom_order.Mid = app_license.Mid  where dc_custom_order.MID='".$mid."' AND app_license.StartDate<NOW() AND app_license.EndDate>now()");


        $BUYDATA = array();

        $BUYYEAR = array();

        $BUYDESC = $this->_dao->query("select DataType,Syear,Eyear,pinzhong from dc_custom_order inner join app_license on dc_custom_order.Mid = app_license.Mid and app_license.mc_type='$mc_type' and dc_custom_order.mc_type='$mc_type' where dc_custom_order.MID='" . $mid . "' AND app_license.StartDate<NOW() AND app_license.EndDate>now()");

        //$BUYDESC =$this->_dao->query("select DataType,Syear,Eyear from dc_custom_order left join app_license on dc_custom_order.Mid = app_license.Mid  where 1");

        //获取购买年月

        foreach ($BUYDESC as $key => $val) {
            $BUYDATA[$key] = $val['DataType'];

            $BUYYEAR[$val['DataType']]['SYEAR'] = $val['Syear'];
            $BUYYEAR[$val['DataType']]['EYEAR'] = $val['Eyear'];
        }

        $this->_dao->WriteLog($mid, $uid, $SignCS, $action, $actionstr, $this->getIP(), $this->getSystemType(), $this->getSystemVersion(), "树形菜单", '', '', $mc_type);


        //修改有效期内用户 可以看 价格指数  市场行情

        $isyx = $this->_dao->getRow("select * from app_license where MID = '" . $mid . "' AND app_license.StartDate<NOW() AND app_license.EndDate>now() ");

        if ($isyx) {
            $vdate = $this->_dao->getOnes("select ID from dc_code_class where scode in ('A','B') and mc_type='$mc_type'");

            $BUYDATA = array_unique(array_merge($BUYDATA, $vdate));

        }
        //接口id 查询出的数据
        $shuju = $this->_dao->query(
            "select distinct ID,scode1,scode2,scode3,scode4,scode5,dtname" . $isen . " as dtname,dtymd,dtcomparetype,dbaseType,isSubDatas,subDatasTitle" . $isen . " as subDatasTitle,subDatasDb,subDatasDb2,subAtt1Db" . $isen . " as subAtt1Db,subAtt2Db" . $isen . " as subAtt2Db,subAtt3Db" . $isen . " as subAtt3Db,Data1Type,Data2Type,Data3Type,Data4Type,Data5Type,Data6Type,Data7Type,Data8Type,Data9Type,Data10Type,Data11Type,Data12Type,Data13Type,Data14Type,Data15Type,Data16Type,Data17Type,Data18Type,Data19Type,Data20Type,startdate,enddata,dtod,ndate" . $isen . " as ndate,ndata1" . $isen . " as ndata1,ndata2" . $isen . " as ndata2,ndata3" . $isen . " as ndata3,ndata4" . $isen . " as ndata4,ndata5" . $isen . " as ndata5,ndata6" . $isen . " as ndata6,ndata7" . $isen . " as ndata7,ndata8" . $isen . " as ndata8,ndata9" . $isen . " as ndata9,ndata10" . $isen . " as ndata10,ndata11" . $isen . " as ndata11,ndata12" . $isen . " as ndata12,ndata13" . $isen . " as ndata13,ndata14" . $isen . " as ndata14,ndata15" . $isen . " as ndata15,ndata16" . $isen . " as ndata16,ndata17" . $isen . " as ndata17,ndata18" . $isen . " as ndata18,ndata19" . $isen . " as ndata19,ndata20" . $isen . " as ndata20,npredata" . $isen . " as npredata,naddsubdata" . $isen . " as naddsubdata,naddsubhundcore" . $isen . " as naddsubhundcore,TargetFlagVar
			from dc_code_datatype inner join dc_code_datatype_db on dc_code_datatype_db.DID = dc_code_datatype.ID and dc_code_datatype.mc_type='$mc_type' and dc_code_datatype_db.mc_type='$mc_type' where dc_code_datatype.ID in(" . $ids . ") and Status =1 $notincludePS_type order by dtod");
        foreach ($shuju as $arr2) {
            $Results[$arr2['ID']] = $arr2;
        }
        //echo '<pre>';print_R($shuju_Results);exit;

        $shuju2 = $this->_dao->query("select  ID,DID,sname,scode,sdbtype,satt1,satt2,satt3,aod from dc_code_datatype_subs where DID in(" . $ids . ") and Status =1 and mc_type='$mc_type' order by aod", 0);
        foreach ($shuju2 as $arr3) {
            $SubResults[$arr3['DID']][$arr3['ID']] = $arr3;
        }

        //echo '<pre>';print_r($SubResults);exit;
        foreach ($Results as &$tmp2) {
            if (in_array($tmp2['ID'], $BUYDATA) || $mid == "1") {

                if (in_array($tmp2['ID'], $BUYDATA)) {
                    $tmp2['SYear'] = $BUYYEAR[$tmp2['ID']]['SYEAR'];
                    $tmp2['EYear'] = $BUYYEAR[$tmp2['ID']]['EYEAR'];
                } else {
                    $tmp2['SYear'] = "";
                    $tmp2['EYear'] = "";
                }


                $tmp2['dtname'] = base64_encode($tmp2['dtname']);

                $tmp2['subDatasTitle'] = base64_encode($tmp2['subDatasTitle']);

                //if($BUYDATA[$tmp3['ID']]){

                $tmp2['subDatasDb'] = base64_encode($tmp2['subDatasDb']);
                $tmp2['subDatasDb2'] = base64_encode($tmp2['subDatasDb2']);

                $tmp2['subAtt1Db'] = base64_encode($tmp2['subAtt1Db']);
                $tmp2['subAtt2Db'] = base64_encode($tmp2['subAtt2Db']);
                $tmp2['subAtt3Db'] = base64_encode($tmp2['subAtt3Db']);


                $tmp2['dtdesc'] = base64_encode($tmp2['dtdesc']);
                $tmp2['techsqlmain'] = base64_encode($tmp2['techsqlmain']);

                $tmp2['ndate'] = base64_encode($tmp2['ndate']);
                $tmp2['ndata1'] = base64_encode($tmp2['ndata1']);
                $tmp2['ndata2'] = base64_encode($tmp2['ndata2']);
                $tmp2['ndata3'] = base64_encode($tmp2['ndata3']);
                $tmp2['ndata4'] = base64_encode($tmp2['ndata4']);
                $tmp2['ndata5'] = base64_encode($tmp2['ndata5']);
                $tmp2['npredata'] = base64_encode($tmp2['npredata']);
                $tmp2['naddsubdata'] = base64_encode($tmp2['naddsubdata']);
                $tmp2['naddsubhundcore'] = base64_encode($tmp2['naddsubhundcore']);

                $tmp2['ndate'] = base64_encode($tmp2['ndate']);
                $tmp2['ndata1'] = base64_encode($tmp2['ndata1']);
                $tmp2['ndata2'] = base64_encode($tmp2['ndata2']);
                $tmp2['ndata3'] = base64_encode($tmp2['ndata3']);
                $tmp2['ndata4'] = base64_encode($tmp2['ndata4']);
                $tmp2['ndata5'] = base64_encode($tmp2['ndata5']);
                $tmp2['ndata6'] = base64_encode($tmp2['ndata6']);
                $tmp2['ndata7'] = base64_encode($tmp2['ndata7']);
                $tmp2['ndata8'] = base64_encode($tmp2['ndata8']);
                $tmp2['ndata9'] = base64_encode($tmp2['ndata9']);
                $tmp2['ndata10'] = base64_encode($tmp2['ndata10']);
                $tmp2['ndata11'] = base64_encode($tmp2['ndata11']);
                $tmp2['ndata12'] = base64_encode($tmp2['ndata12']);
                $tmp2['ndata13'] = base64_encode($tmp2['ndata13']);
                $tmp2['ndata14'] = base64_encode($tmp2['ndata14']);
                $tmp2['ndata15'] = base64_encode($tmp2['ndata15']);
                $tmp2['ndata16'] = base64_encode($tmp2['ndata16']);
                $tmp2['ndata17'] = base64_encode($tmp2['ndata17']);
                $tmp2['ndata18'] = base64_encode($tmp2['ndata18']);
                $tmp2['ndata19'] = base64_encode($tmp2['ndata19']);
                $tmp2['ndata20'] = base64_encode($tmp2['ndata20']);
                $tmp2['npredata'] = base64_encode($tmp2['npredata']);
                $tmp2['naddsubdata'] = base64_encode($tmp2['naddsubdata']);
                $tmp2['naddsubhundcore'] = base64_encode($tmp2['naddsubhundcore']);


                $tmp2['isbuy'] = "1";


                foreach ($SubResults[$tmp2['ID']] as &$tmp3) {
                    $tmp2['SubResults'][] = $tmp3;

                    foreach ($tmp2['SubResults'] as &$tmp4) {
                        $tmp4['sname'] = base64_encode(html_entity_decode($tmp4['sname']));
                        $tmp4['scode'] = base64_encode(html_entity_decode($tmp4['scode']));
                        $tmp4['satt1'] = base64_encode(html_entity_decode($tmp4['satt1']));
                        $tmp4['satt2'] = base64_encode(html_entity_decode($tmp4['satt2']));
                        $tmp4['satt3'] = base64_encode(html_entity_decode($tmp4['satt3']));
                    }
                }

            } else {
                array_walk($tmp3, "myfunction");

                $tmp3['dtname'] = base64_encode($tmp3['dtname']);

                $tmp3['isbuy'] = "0";
                $tmp3['SubResults'] = array();
            }

        }


//echo '<pre>';print_r($Results);exit;
//exit;

        $arr['Success'] = 1;
        $arr['Message'] = base64_encode("成功");
        if ($isEnglish) $arr['Message'] = base64_encode("Success");

        if ($needCompress == 1) {
            $arr['Results'] = base64_encode(gzencode(json_encode($Results), 9));//压缩找个方法
        } else {
            $arr['Results'] = $Results;
        }

//		print_r($arr);
        $json_string = $this->pri_JSON($arr);
        echo $json_string;


    }


    public function addCatalogue($params)
    {
        if (!empty($params['isEnglish'])) $isEnglish = $params['isEnglish'];
        else $isEnglish = 0;

//获取已购买数据
        $GUID = $params['GUID'];
        $SignCS = $params['SignCS'];
        $mc_type = $params['mc_type'];
        $action = $params['action'];        //接口名称
        if (!empty($params['mc_type'])) $mc_type = $params['mc_type'];
        else $mc_type = 0;
        $needCompress = $params['needCompress'];  //判断是否封装
        $ip = $this->getIP();
        $SystemType = $params['SystemType'];

        $CatalogueData = html_entity_decode(Urldecode($params["CatalogueData"]));
        $Datas = json_decode($CatalogueData, true);
//        $cataloguename = iconv("UTF-8", "GBK", Urldecode($Datas['cataloguename']));
        $cataloguename = Urldecode($Datas['cataloguename']);



        $arr = array(
            'Success' => '0',
            'ErrorType' => '',
            'Message' => '失败',
        );

        $n = $this->getcengji($Datas['parentid'], 1);
        if ($n >= 4) {
            $arr['Message'] = '层级限制最多5层';
            if ($isEnglish) $arr['Message'] = ("Maximum 5 layers");
            $json_string = $this->pri_JSON($arr);
            echo $json_string;
            exit;
        }

        if ($isEnglish) $arr['Message'] = 'Failed!';

        if (array_key_exists($GUID, $GLOBALS["testaccountarr"]) && $SignCS != '' && (in_array($ip, $GLOBALS["testip"]) || $GLOBALS["nowtest"] == 1)) {
            $user = $GLOBALS["testaccountuser"];
        } else {
            $user = $this->_dao->getUser($GUID, $SignCS, $mc_type);
        }

        $mid = $user['Mid'];
        $uid = $user['Uid'];


//同目录下不能同名
        $cname_arr = $this->_dao->query("select cataloguename from dc_search_custom_catalogue where parentid='" . $Datas['parentid'] . "' and mc_type='" . $mc_type . "' and MID='" . $mid . "' and Uid='" . $uid . "' and status=1  and   IsEnglish='" . $isEnglish . "' ");
        foreach ($cname_arr as $key => $val) {
            if ($val['cataloguename'] == $cataloguename) {
                $arr['Message'] = '同目录下同名';
                if ($isEnglish) $arr['Message'] = ("same name,please change!");
                $json_string = $this->pri_JSON($arr);
                echo $json_string;
                exit;
            }

        }


        $arrtem = $params;
        $actionstr = "";
        foreach ($arrtem as $k => $v) {
            $actionstr .= "&" . $k . "=" . $v;
        }
//记录日志
        $this->_dao->WriteLog($mid, $uid, $SignCS, $action, $actionstr, $this->getIP(), $SystemType, $this->getSystemVersion(), '定制数据目录保存', '', '', $mc_type);
//保存的信息存入

//	$cataloguename= str_replace(' ','+',Urldecode($Datas['cataloguename']));

//	$codetype = $this->testEncoding(base64_decode($cataloguename));

        //  if($codetype==1){

//		$cataloguename=iconv("UTF-8","GBK",base64_decode($cataloguename));

//	}else{
//		$cataloguename=base64_decode($cataloguename);
//	}


        $this->_dao->execute("insert into dc_search_custom_catalogue 
          (parentid,cataloguename,status,aod,createtime,updatetime,Uid,MID,mc_type,IsEnglish)
			values
			('" . $Datas['parentid'] . "','" . $cataloguename . "','1','',NOW(),NOW(),'" . $uid . "','" . $mid . "','" . $mc_type . "','" . $isEnglish . "')");
        $insertid = $this->_dao->getone("SELECT LAST_INSERT_ID()");

        $this->_dao->execute("update dc_search_custom_catalogue set aod='" . $insertid . "' where ID='" . $insertid . "' ");


        $arr['Success'] = 1;
        $arr['Message'] =  '成功'  ;
        if ($isEnglish) $arr['Message'] = "Success";

        $updatetime = $this->_dao->getone("select updatetime  from `dc_search_custom_catalogue` where ID=" . $insertid);
        $addarr = array(
            'ID' => $insertid,
            'parentid' => $Datas['parentid'],
            'cataloguename' => $cataloguename,
            'status' => '1',
            'aod' => $insertid,
            'updatetime' => $updatetime,
            'hasleaves' => '0',
            'leaves' => array(),


        );

        $arr['Results'][0] = $addarr;

//		print_r($arr);
        $json_string = $this->pri_JSON($arr);
        echo $json_string;

    }

    public function delCatalogue($params)
    {
        if (!empty($params['isEnglish'])) $isEnglish = $params['isEnglish'];
        else $isEnglish = 0;

        //获取已购买数据
        $GUID = $params['GUID'];
        $SignCS = $params['SignCS'];
        $mc_type = $params['mc_type'];
        $action = $params['action'];        //接口名称
        if (!empty($params['mc_type'])) $mc_type = $params['mc_type'];
        else $mc_type = 0;

        $ip = $this->getIP();
        $SystemType = $params['SystemType'];

        if (!empty($params['isCatalogue'])) {
            $isCatalogue = $params['isCatalogue'];
        } else {
            $arr = array(
                'Success' => '0',
                'ErrorType' => '',
                'Message' =>  '参数缺失，请升级版本' ,
            );
            if ($isEnglish) $arr['Message'] = ("Failed");
            $json_string = $this->pri_JSON($arr);
            echo $json_string;
            exit;

        }


        $IDData = html_entity_decode($params["IDData"]);
        $Datas = json_decode($IDData, true);

        //echo '<pre>';
        //print_R($Datas);

        $arr = array(
            'Success' => '0',
            'ErrorType' => '',
            'Message' => '失败',
        );

        if ($isEnglish) $arr['Message'] = 'Failed!';

        if (array_key_exists($GUID, $GLOBALS["testaccountarr"]) && $SignCS != '' && (in_array($ip, $GLOBALS["testip"]) || $GLOBALS["nowtest"] == 1)) {
            $user = $GLOBALS["testaccountuser"];
        } else {
            $user = $this->_dao->getUser($GUID, $SignCS, $mc_type);
        }

        $mid = $user['Mid'];
        $uid = $user['Uid'];
        $sear = array();
        $arrtem = $params;
        $actionstr = "";
        foreach ($arrtem as $k => $v) {
            $actionstr .= "&" . $k . "=" . $v;
        }


        $deltime = date("Y-m-d H:m:s");


        if ($isCatalogue == 1) {//1目录删除
            // $ishave='0';
            foreach ($Datas as $key => $val) {


                //不递归，仅查询这个id下面还有没有目录或是数据
                /*$customid= $this->_dao->getone("select ID  from `dc_search_custom` where mc_type='".$mc_type."' and MID='".$mid."' and Uid='".$uid."'  and   IsEnglish='".$isEnglish."'  and catalogueid=".$val['ID']);
    $customid2= $this->_dao->getone("select ID  from `dc_search_custom_catalogue`  where mc_type='".$mc_type."' and MID='".$mid."' and Uid='".$uid."' and status=1  and   IsEnglish='".$isEnglish."' and  parentid=".$val['ID']);


    if($customid!=''||!empty($customid2)){
        $ishave='1';
    }
    */


                //添加导出记录
                $parentid = $val['ID'];
                if ($parentid == 0) {
                    $arr['Message'] =  '根目录我的数据不允许删除' ;
                    if ($isEnglish) $arr['Message'] = "Success";
                    $json_string = $this->pri_JSON($arr);
                    echo $json_string;
                    exit;
                }
                //获取$parentid及其下面所有目录与数据
                $sear = $this->parentidsearch_mulu($parentid, $isEnglish, $mid, $uid, $mc_type, '1');


                //加上起始目录
                $fristcatalogue = $this->_dao->getrow("select ID,parentid,cataloguename  from `dc_search_custom_catalogue`  where ID ='" . $parentid . "' and mc_type='" . $mc_type . "' and MID='" . $mid . "' and Uid='" . $uid . "'  and  IsEnglish='" . $isEnglish . "' and status=1  order by binary(cataloguename) asc");
                // echo "select ID,parentid  from `dc_search_custom_catalogue`  where ID ='".$parentid."' and mc_type='".$mc_type."' and MID='".$mid."' and Uid='".$uid."'  and  IsEnglish='".$isEnglish."' and status=1  order by binary(cataloguename) asc";
                $frist_arr['cid'] = $fristcatalogue['ID'];
                $frist_arr['pid'] = $fristcatalogue['parentid'];
                $frist_arr['mid'] = $mid;
                $frist_arr['uid'] = $uid;
                $frist_arr['mc_type'] = $mc_type;
                $frist_arr['isEnglish'] = $isEnglish;
                $frist_arr['isCatalogue'] = '1';
                $frist_arr['cj'] = '1';


                $sear[] = $frist_arr;


                $this->_dao->execute("insert into dc_derivedjson  
			(uid,MID,IsEnglish,mc_type,dctime,json,title,isCatalogue,type,isdel)
			values
			('" . $uid . "','" . $mid . "','" . $isEnglish . "','" . $mc_type . "',NOW(),'" . json_encode($sear) . "','" . $fristcatalogue['cataloguename'] . "',1,1,0)");

                //递归删除下面的目录及数据，同时保存入中间表
                $this->recycle($val['ID'], $mid, $uid, $isEnglish, $deltime, $mc_type);
                //下层处理完了  保存本级目录并删除本级目录
                $this->_dao->execute("INSERT INTO dc_search_custom_catalogue_recycle (
				`ID`,
				`parentid`,
				`cataloguename`,
				`status`,
				`aod`,
				`createtime`,
				`updatetime`,
				`Uid`,
				`MID`,
				`mc_type`,
				`IsEnglish`)select `ID`,`parentid`,`cataloguename`,`status`,`aod`,`createtime`,`updatetime`,`Uid`,`MID`,`mc_type`,`IsEnglish`
			    from `dc_search_custom_catalogue` where   ID='" . $parentid . "' and mc_type='" . $mc_type . "' and MID='" . $mid . "' and Uid='" . $uid . "'  and  IsEnglish='" . $isEnglish . "' ");
                $this->_dao->execute("update dc_search_custom_catalogue_recycle set deltime='" . $deltime . "'  where  ID='" . $parentid . "' and mc_type='" . $mc_type . "' and MID='" . $mid . "' and Uid='" . $uid . "'  and  IsEnglish='" . $isEnglish . "' ");
                $this->_dao->execute("delete from dc_search_custom_catalogue where   ID='" . $parentid . "' and mc_type='" . $mc_type . "' and MID='" . $mid . "' and Uid='" . $uid . "'  and  IsEnglish='" . $isEnglish . "' ");
            }


        } else if ($isCatalogue == 2) {//数据删除


            foreach ($Datas as $key => $val) {
                //导出
                $parentid = $val['ID'];
                $selectsql = "select ID,catalogueid,dtname from dc_search_custom  where ID ='" . $parentid . "' and mc_type='" . $mc_type . "' and MID='" . $mid . "' and Uid='" . $uid . "'  and  IsEnglish='" . $isEnglish . "'  order by binary(dtname) asc,aod desc,ID desc";

                $row = $this->_dao->getrow($selectsql);
                $sear[0]['id'] = $row['ID'];
                $sear[0]['cid'] = $row['catalogueid'];
                $sear[0]['mid'] = $mid;
                $sear[0]['uid'] = $uid;
                $sear[0]['mc_type'] = $mc_type;
                $sear[0]['isEnglish'] = $isEnglish;
                $sear[0]['isCatalogue'] = '0';
                $sear[0]['cj'] = '1';

                $this->_dao->execute("insert into dc_derivedjson  
									(uid,MID,IsEnglish,mc_type,dctime,json,title,isCatalogue,type,isdel)
									values
									('" . $uid . "','" . $mid . "','" . $isEnglish . "','" . $mc_type . "',NOW(),'" . json_encode($sear) . "','" . $row['dtname'] . "',0,1,0)");

                //数据先保存入回收站dc_search_custom_recycle
                $this->_dao->execute("INSERT INTO dc_search_custom_recycle (`ID`,`MID`,`Uid`,`dtname`,`dtnameshort`,`DTID1`,`DTID1Sub`,`Data1Type1`,`Data1Pre`,`Data1AddSub`,`Data1AddSubHundCore`,`Data1Type2`,`Data1Type3`,`Data1Type4`,`Data1Type5`,`DTID2`,`DTID2Sub`,`Data2Type`,`DTID3`,`DTID3Sub`,`Data3Type`,`DTID4`,`DTID4Sub`,`Data4Type`,`aod`,`ImageType`,`ImageTitle`,`Data1Image`,`Data2Image`,`Data3Image` ,`Data4Image`,`zhou1`,`zhou2` ,`zhou3`,`zhou4`,`dtname_en`,`dtnameshort_en`,`IsEnglish`,`mc_type`,`isbg`,`isjz`,`color`,`isfg`,`LineTitle1`,`LineTitle2`,`LineTitle3`,`LineTitle4`,`GongshiID1`,`GongshiID2`,`GongshiID3`,`GongshiID4`,`Data1Type`,`catalogueid`,`createtime`,`updatetime`,`DTIDJson` ,`LineTitle1_bak`,`LineTitle2_bak` ,`LineTitle3_bak` ,`LineTitle4_bak` ,`ImageTitle_bak`,`DateFormat`  
				)select `ID`,`MID`,`Uid`,`dtname`,`dtnameshort`,`DTID1`,`DTID1Sub`,`Data1Type1`,`Data1Pre`,`Data1AddSub`,`Data1AddSubHundCore`,`Data1Type2`,`Data1Type3`,`Data1Type4`,`Data1Type5`,`DTID2`,`DTID2Sub`,`Data2Type` ,`DTID3`,`DTID3Sub`,`Data3Type`,`DTID4`,`DTID4Sub`,`Data4Type`,`aod`,`ImageType` ,`ImageTitle`,`Data1Image`,`Data2Image`,`Data3Image`,`Data4Image`,`zhou1`,`zhou2`,`zhou3`,`zhou4`,`dtname_en`,`dtnameshort_en`,`IsEnglish`,`mc_type`,`isbg`,`isjz`,`color`,`isfg`,`LineTitle1`,`LineTitle2`,`LineTitle3`,`LineTitle4`,`GongshiID1`,`GongshiID2`,`GongshiID3`,`GongshiID4`,`Data1Type`,`catalogueid`,`createtime`,`updatetime`,`DTIDJson`,`LineTitle1_bak`,`LineTitle2_bak`,`LineTitle3_bak`,`LineTitle4_bak`,`ImageTitle_bak` ,`DateFormat`  
				from `dc_search_custom` where   ID='" . $val['ID'] . "' and mc_type='" . $mc_type . "' and MID='" . $mid . "' and Uid='" . $uid . "'  and  IsEnglish='" . $isEnglish . "'");
                $this->_dao->execute("update dc_search_custom_recycle set deltime='" . $deltime . "'  where  ID='" . $val['ID'] . "' and mc_type='" . $mc_type . "' and MID='" . $mid . "' and Uid='" . $uid . "'  and  IsEnglish='" . $isEnglish . "' ");

                //删除回收站对应的dc_search_custom表数据
                $this->_dao->execute("delete from dc_search_custom where    ID=" . $val['ID']);
            }
        }


        //记录日志
        // $this->_dao->WriteLog($mid, $uid, $SignCS, $action,$actionstr, $this->getIP(), $SystemType, $this->getSystemVersion(),iconv('UTF-8', 'GBK', '定制数据目录删除'),'','',$mc_type);
        $this->_dao->WriteLog($mid, $uid, $SignCS, $action, $actionstr, $this->getIP(), $SystemType, $this->getSystemVersion(), '定制数据目录删除', '', '', $mc_type);
        //		print_r($arr);

        $arr['Success'] = 1;
        $arr['Message'] = '成功';
        if ($isEnglish) $arr['Message'] = ("Success");
        $json_string = $this->pri_JSON($arr);
        echo $json_string;

    }


    private function recycle($id, $mid, $uid, $isEnglish, $deltime, $mc_type)
    {
        //$id  删除的目录id
        $customids = $this->_dao->query("select ID  from `dc_search_custom` where   catalogueid=" . $id);

        //$customid  此目录下数据id集合
        if (!empty($customids)) {
            $ids = '';
            foreach ($customids as $key => $value) {
                if ($ids == '') {
                    $ids = "'" . $value['ID'] . "'";
                } else {
                    $ids .= ",'" . $value['ID'] . "'";
                }
            }

            //数据先保存入回收站dc_search_custom_recycle
            $this->_dao->execute("INSERT INTO dc_search_custom_recycle (`ID`,`MID`,`Uid`,`dtname`,`dtnameshort`,`DTID1`,`DTID1Sub`,`Data1Type1`,`Data1Pre`,`Data1AddSub`,`Data1AddSubHundCore`,`Data1Type2`,`Data1Type3`,`Data1Type4`,`Data1Type5`,`DTID2`,`DTID2Sub`,`Data2Type`,`DTID3`,`DTID3Sub`,`Data3Type`,`DTID4`,`DTID4Sub`,`Data4Type`,`aod`,`ImageType`,`ImageTitle`,`Data1Image`,`Data2Image`,`Data3Image` ,`Data4Image`,`zhou1`,`zhou2` ,`zhou3`,`zhou4`,`dtname_en`,`dtnameshort_en`,`IsEnglish`,`mc_type`,`isbg`,`isjz`,`color`,`isfg`,`LineTitle1`,`LineTitle2`,`LineTitle3`,`LineTitle4`,`GongshiID1`,`GongshiID2`,`GongshiID3`,`GongshiID4`,`Data1Type`,`catalogueid`,`createtime`,`updatetime`,`DTIDJson` ,`LineTitle1_bak`,`LineTitle2_bak` ,`LineTitle3_bak` ,`LineTitle4_bak` ,`ImageTitle_bak` ,`DateFormat`,`ChartExtLineStyle`  
		  )select `ID`,`MID`,`Uid`,`dtname`,`dtnameshort`,`DTID1`,`DTID1Sub`,`Data1Type1`,`Data1Pre`,`Data1AddSub`,`Data1AddSubHundCore`,`Data1Type2`,`Data1Type3`,`Data1Type4`,`Data1Type5`,`DTID2`,`DTID2Sub`,`Data2Type` ,`DTID3`,`DTID3Sub`,`Data3Type`,`DTID4`,`DTID4Sub`,`Data4Type`,`aod`,`ImageType` ,`ImageTitle`,`Data1Image`,`Data2Image`,`Data3Image`,`Data4Image`,`zhou1`,`zhou2`,`zhou3`,`zhou4`,`dtname_en`,`dtnameshort_en`,`IsEnglish`,`mc_type`,`isbg`,`isjz`,`color`,`isfg`,`LineTitle1`,`LineTitle2`,`LineTitle3`,`LineTitle4`,`GongshiID1`,`GongshiID2`,`GongshiID3`,`GongshiID4`,`Data1Type`,`catalogueid`,`createtime`,`updatetime`,`DTIDJson`,`LineTitle1_bak`,`LineTitle2_bak`,`LineTitle3_bak`,`LineTitle4_bak`,`ImageTitle_bak` ,`DateFormat`,`ChartExtLineStyle`    
          from `dc_search_custom` where    ID in($ids) and mc_type='" . $mc_type . "' and MID='" . $mid . "' and Uid='" . $uid . "'  and  IsEnglish='" . $isEnglish . "'");
            $this->_dao->execute("update dc_search_custom_recycle set deltime='" . $deltime . "'  where   ID in($ids) and mc_type='" . $mc_type . "' and MID='" . $mid . "' and Uid='" . $uid . "'  and  IsEnglish='" . $isEnglish . "' ");

            //删除回收站对应的dc_search_custom表数据
            $this->_dao->execute("delete from dc_search_custom where  ID in($ids) and mc_type='" . $mc_type . "' and MID='" . $mid . "' and Uid='" . $uid . "'  and  IsEnglish='" . $isEnglish . "'");


        }

        //$id  删除的目录id
        $sunids = $this->_dao->query("select ID  from `dc_search_custom_catalogue` where   parentid=" . $id . " and mc_type='" . $mc_type . "' and MID='" . $mid . "' and Uid='" . $uid . "'  and  IsEnglish='" . $isEnglish . "'");

        if (!empty($sunids)) {
            foreach ($sunids as $k => $v) {
                //下面的子目录向下递归处理
                $this->recycle($v['ID'], $mid, $uid, $isEnglish, $deltime, $mc_type);
                //下层处理完了  保存本级目录并删除本级目录
                $this->_dao->execute("INSERT INTO dc_search_custom_catalogue_recycle (
				`ID`,
				`parentid`,
				`cataloguename`,
				`status`,
				`aod`,
				`createtime`,
				`updatetime`,
				`Uid`,
				`MID`,
				`mc_type`,
				`IsEnglish`)select `ID`,`parentid`,`cataloguename`,`status`,`aod`,`createtime`,`updatetime`,`Uid`,`MID`,`mc_type`,`IsEnglish`
			    from `dc_search_custom_catalogue` where   ID='" . $v['ID'] . "' and mc_type='" . $mc_type . "' and MID='" . $mid . "' and Uid='" . $uid . "'  and  IsEnglish='" . $isEnglish . "'");

                $this->_dao->execute("update dc_search_custom_catalogue_recycle set deltime='" . $deltime . "'  where  ID='" . $v['ID'] . "'  and mc_type='" . $mc_type . "' and MID='" . $mid . "' and Uid='" . $uid . "'  and  IsEnglish='" . $isEnglish . "' ");

                $this->_dao->execute("delete from dc_search_custom_catalogue where   ID='" . $v['ID'] . "' and mc_type='" . $mc_type . "' and MID='" . $mid . "' and Uid='" . $uid . "'  and  IsEnglish='" . $isEnglish . "'");
            }

        }
        return '';
    }

    public function CustomDataSwap($params)
    {
        if (!empty($params['isEnglish'])) $isEnglish = $params['isEnglish'];
        else $isEnglish = 0;

        //获取已购买数据
        $GUID = $params['GUID'];
        $SignCS = $params['SignCS'];
        $mc_type = $params['mc_type'];
        $action = $params['action'];        //接口名称
        $isCatalogue = $params['isCatalogue'];

        if (!empty($params['mc_type'])) $mc_type = $params['mc_type'];
        else $mc_type = 0;

        $ip = $this->getIP();
        $SystemType = $params['SystemType'];

        $CustomData = html_entity_decode(Urldecode($params["CustomData"]));
        $Datas = json_decode($CustomData, true);
        $arr = array(
            'Success' => '0',
            'ErrorType' => '',
            'Message' => '失败',
        );
        if ($isEnglish) $arr['Message'] = 'Failed!';

        if (array_key_exists($GUID, $GLOBALS["testaccountarr"]) && $SignCS != '' && (in_array($ip, $GLOBALS["testip"]) || $GLOBALS["nowtest"] == 1)) {
            $user = $GLOBALS["testaccountuser"];
        } else {
            $user = $this->_dao->getUser($GUID, $SignCS, $mc_type);
        }
        $mid = $user['Mid'];
        $uid = $user['Uid'];
        $arrtem = $params;
        $actionstr = "";
        foreach ($arrtem as $k => $v) {
            if ($k = 'CustomData') $v = str_replace("'", "\'", $v);
            $actionstr .= "&" . $k . "=" . $v;
        }
        //记录日志
        $this->_dao->WriteLog($mid, $uid, $SignCS, $action, $actionstr, $this->getIP(), $SystemType, $this->getSystemVersion(), '定制数据和分类（目录）排序修改', '', '', $mc_type);
        if ($isCatalogue == 1) {//目录
            foreach ($Datas as $key => $val) {
                $sql="select aod,id from dc_search_custom_catalogue where id in ('".$val['ID1']."','".$val['ID2']."') and mc_type='" . $mc_type . "' and  MID='" . $mid . "' and Uid='" . $uid . "' and status=1  and   IsEnglish='" . $isEnglish . "'";
                $dc_search_customdarr = $this->_dao->query($sql);
                if(!empty($dc_search_customdarr)&&count($dc_search_customdarr)==2)
                {
                    $id_aod=array();
                    foreach ($dc_search_customdarr as $k=>$v) {
                        $id_aod[$v['id']] = $v['aod'];
                    }
                    $sql="update dc_search_custom_catalogue set aod='".$id_aod[$val['ID2']]."',updatetime=NOW() where id='".$val['ID1']."'";
                    $this->_dao->execute($sql);
                    $sql="update dc_search_custom_catalogue set aod='".$id_aod[$val['ID1']]."',updatetime=NOW() where id='".$val['ID2']."'";
                    $this->_dao->execute($sql);
                }
                else
                {
                    $arr['Message'] = '参数错误';
                    if ($isEnglish) $arr['Message'] = ("Parameter Missing");
                    $json_string = $this->pri_JSON($arr);
                    echo $json_string;
                    exit;
                }
            }
        } else if ($isCatalogue == 2) {//数据
            foreach ($Datas as $key => $val) {
                $sql="select aod,id from dc_search_custom where id in ('".$val['ID1']."','".$val['ID2']."') and mc_type='" . $mc_type . "' and MID='" . $mid . "' and Uid='" . $uid . "' and   IsEnglish='" . $isEnglish . "'";
                $dc_search_customdarr = $this->_dao->query($sql);
                if(!empty($dc_search_customdarr)&&count($dc_search_customdarr)==2)
                {
                    $id_aod=array();
                    foreach ($dc_search_customdarr as $k=>$v) {
                        $id_aod[$v['id']] = $v['aod'];
                    }

                    $sql="update dc_search_custom set aod='".$id_aod[$val['ID2']]."',updatetime=NOW() where id='".$val['ID1']."'";
                    $this->_dao->execute($sql);
                    $sql="update dc_search_custom set aod='".$id_aod[$val['ID1']]."',updatetime=NOW() where id='".$val['ID2']."'";
                    $this->_dao->execute($sql);
                }
                else
                {
                    $arr['Message'] = '参数错误';
                    if ($isEnglish) $arr['Message'] = ("Parameter Missing");
                    $json_string = $this->pri_JSON($arr);
                    echo $json_string;
                    exit;
                }
            }
        }
        $arr['Success'] = 1;
        $arr['Message'] = '成功';
        if ($isEnglish) $arr['Message'] = ("Success");
        //		print_r($arr);
        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }
    public function UpdateCustomData($params)
    {
        if (!empty($params['isEnglish'])) $isEnglish = $params['isEnglish'];
        else $isEnglish = 0;

        //获取已购买数据
        $GUID = $params['GUID'];
        $SignCS = $params['SignCS'];
        $mc_type = $params['mc_type'];
        $action = $params['action'];        //接口名称
        $isCatalogue = $params['isCatalogue'];

        if (!empty($params['mc_type'])) $mc_type = $params['mc_type'];
        else $mc_type = 0;

        $ip = $this->getIP();
        $SystemType = $params['SystemType'];

        $CustomData = html_entity_decode(Urldecode($params["CustomData"]));
        $Datas = json_decode($CustomData, true);


        $arr = array(
            'Success' => '0',
            'ErrorType' => '',
            'Message' => '失败',
        );
        if ($isEnglish) $arr['Message'] = 'Failed!';

        if (array_key_exists($GUID, $GLOBALS["testaccountarr"]) && $SignCS != '' && (in_array($ip, $GLOBALS["testip"]) || $GLOBALS["nowtest"] == 1)) {
            $user = $GLOBALS["testaccountuser"];
        } else {
            $user = $this->_dao->getUser($GUID, $SignCS, $mc_type);
        }
        $mid = $user['Mid'];
        $uid = $user['Uid'];
        $arrtem = $params;
        $actionstr = "";
        foreach ($arrtem as $k => $v) {
            if ($k = 'CustomData') $v = str_replace("'", "\'", $v);
            $actionstr .= "&" . $k . "=" . $v;
        }
        //记录日志
        $this->_dao->WriteLog($mid, $uid, $SignCS, $action, $actionstr, $this->getIP(), $SystemType, $this->getSystemVersion(), '定制数据和分类（目录）修改', '', '', $mc_type);
        //删除
        $i = '0';
        $update = array(
            'ID' => '',
            'parentid' => '',
            'cataloguename' => '',
            'status' => '1',
            'aod' => '',
            'updatetime' => '',
            'hasleaves' => '0',
            'leaves' => '[]',
        );
        if ($isCatalogue == 1) {//目录
            foreach ($Datas as $key => $val) {
                if (isset($val['parentid'])) {
                    $old_pid = $this->_dao->getone("select parentid  from `dc_search_custom_catalogue` where ID='" . $val['ID'] . "'");
                    if ($val['parentid'] == $old_pid) {//没有移动只是改名字之类的，就不计算层级了
                        //	$n3=$n+$n2-1;  //往自身移动减掉一层本身
                    } else {
                        $n = $this->getcengji($val['parentid'], 1);
                        $n2 = $this->getnextcengji($val['ID'], 1);
                        $n3 = $n + $n2;
                        if ($n3 > 4) {

                            $arr['Message'] = '层级限制最多5层';
                            if ($isEnglish) $arr['Message'] = ("Maximum 5 layers");
                            $json_string = $this->pri_JSON($arr);
                            echo $json_string;
                            exit;
                        }
                    }
                }

                $sql = '';
                if ($val['parentid'] != '') {
                    $sql .= "  parentid='" . $val['parentid'] . "'";
                    if ($val['parentid'] != $val['ID']) {
                        //当期id的parentid不能是树结构的子节点中任何的节点，可以往上节点调整改动
                        //getcataloguearr所有有目录关系的进行处理，key=catalogueid；
                        $parentidarr = $this->_dao->query("select ID,parentid  from `dc_search_custom_catalogue`  where mc_type='" . $mc_type . "' and MID='" . $mid . "' and Uid='" . $uid . "' and status=1  and   IsEnglish='" . $isEnglish . "' ");
                        foreach ($parentidarr as $val1) {
                            $parentidmenu[$val1['parentid']][] = $val1['ID'];
                        }
                        $parentid_arr = $this->getallid($val['ID'], $parentidmenu);
                        $p_arr = explode(',', $parentid_arr);
                        foreach ($p_arr as $v) {
                            if ($v == $val['parentid']) {

                                $arr['Message'] = '不能移动到子目录';
                                if ($isEnglish) $arr['Message'] = ("No Moving to Subdirectory");
                                $json_string = $this->pri_JSON($arr);
                                echo $json_string;
                                exit;
                            }
                        }
                    } else {
                        $arr['Message'] = '目标目录不能转移进自身目录';
                        if ($isEnglish) $arr['Message'] = ("No Transference of Destination Directory to Its Own Directory");
                        $json_string = $this->pri_JSON($arr);
                        echo $json_string;
                        exit;
                    }
                }
                if ($val['aod'] != '') {
                    if ($sql != '') {
                        $sql .= ',';
                    }
                    $sql .= "  aod='" . $val['aod'] . "'  ";
                }
                if ($val['cataloguename'] != '') {
                    if ($sql != '') {
                        $sql .= ',';
                    }

                    //$cataloguename = iconv("UTF-8", "GBK", Urldecode($val['cataloguename']));
                    $cataloguename = Urldecode($val['cataloguename']);
                    //同目录下不能同名
                    $cname_arr = $this->_dao->query("select cataloguename from dc_search_custom_catalogue where parentid='" . $val['parentid'] . "' and mc_type='" . $mc_type . "' and MID='" . $mid . "' and Uid='" . $uid . "' and status=1  and ID!='" . $val['ID'] . "' and   IsEnglish='" . $isEnglish . "' ");
                    foreach ($cname_arr as $key => $val2) {
                        if ($val2['cataloguename'] == $cataloguename) {

                            $arr['Message'] = '同目录下同名';
                            if ($isEnglish) $arr['Message'] = ("same name,please change!");
                            $json_string = $this->pri_JSON($arr);
                            echo $json_string;
                            exit;
                        }
                    }

                    $sql .= "  cataloguename='" . $cataloguename . "'  ";
                }
                if ($sql != '' && $val['ID'] != $val['parentid']) {
                    $sql .= ",updatetime=NOW()";
                } else {

                    $arr['Message'] = '参数缺失';
                    if ($isEnglish) $arr['Message'] = ("Parameter Missing");
                    $json_string = $this->pri_JSON($arr);
                    echo $json_string;
                    exit;
                }
                $this->_dao->execute("update `dc_search_custom_catalogue` set $sql where  mc_type='" . $mc_type . "' and MID='" . $mid . "' and Uid='" . $uid . "' and   IsEnglish='" . $isEnglish . "' and status=1 and  ID=" . $val['ID']);
                //echo "update `dc_search_custom_catalogue` set $sql where ID=".$val['ID'];echo '<br>';

                $updaterow = $this->_dao->getRow("select *  from `dc_search_custom_catalogue` where mc_type='" . $mc_type . "' and MID='" . $mid . "' and Uid='" . $uid . "' and   IsEnglish='" . $isEnglish . "' and status=1 and ID=" . $val['ID']);
                //echo $updaterow['cataloguename'];
                $arr_leaves = $this->_dao->query("select *  from `dc_search_custom` where mc_type='" . $mc_type . "' and MID='" . $mid . "' and Uid='" . $uid . "' and   IsEnglish='" . $isEnglish . "'   and  catalogueid=" . $val['ID']);
                $hasleaves_num = '';
                if (empty($arr_leaves)) {
                    $hasleaves_num = '0';
                } else {
                    $hasleaves_num = '1';
                    foreach ($arr_leaves as $lvskey => $lvsval) {

                        //$arr_leaves[$lvskey]['dtname'] = base64_encode($this->utf8ToGb2312($lvsval['dtname']));
                        //$arr_leaves[$lvskey]['dtnameshort'] = base64_encode($this->utf8ToGb2312($lvsval['dtnameshort']));
                        //$arr_leaves[$lvskey]['ImageTitle'] = base64_encode($this->utf8ToGb2312($lvsval['ImageTitle']));
                        if (!empty($lvsval['DTIDJson'])) {
                            $arr_leaves[$lvskey]['DTIDJson'] = array();
                            $dj = json_decode($lvsval['DTIDJson'], true);
                            //echo '<pre>';echo '<br>';
                            //print_R($dj); echo '<br>';
                            foreach ($dj as $k1 => $v1) {
                                $DTIDJson_arr = $v1;
                                $arr_leaves[$lvskey]['DTIDJson'][$k1] = $DTIDJson_arr;
                            }
                        }
                        if (!empty($lvsval['ChartExtLineStyle'])) {
                            $arr_leaves[$lvskey]['ChartExtLineStyle'] = array();
                            $cs = json_decode($lvsval['ChartExtLineStyle'], true);
                            foreach ($cs as $k1 => $v1) {
                                $ChartExtLineStyle_arr = $v1;
                                $arr_leaves[$lvskey]['ChartExtLineStyle'][$k1] = $ChartExtLineStyle_arr;
                            }
                        }
                    }
                }
                if(isset($val['destIds'])&&!empty($val['destIds']))
                {
                    foreach ($val['destIds'] as $k => $v) 
                    {
                        foreach ($v as $k1 => $v1) 
                       {
                            $this->_dao->execute("update `dc_search_custom_catalogue` set aod='".$v1."' where ID='".$k1."'");
                            if($updaterow['ID']==$k1)
                            {
                                $updaterow['aod']=$v1;
                            }
                       }
                    }
                }
                $update = array(
                    'ID' => $updaterow['ID'],
                    'parentid' => $updaterow['parentid'],

                    'cataloguename' => $updaterow['cataloguename'],
                    'status' => '1',
                    'aod' => isset($val['destIds'])?$updaterow['aod']:$updaterow['ID'],
                    'updatetime' => $updaterow['updatetime'],
                    'hasleaves' => $hasleaves_num,
                    'leaves' => $arr_leaves,
                );

                $arr['Results'][$i] = $update;
                $i++;
            }
        } else if ($isCatalogue == 2) {//数据
            foreach ($Datas as $key => $val) {
                $val['dtname'] = str_replace("'", "\'", $val['dtname']);
                //print_r($val);
                $sql = '';
                if ($val['catalogueid'] != '') {
                    $sql .= "  catalogueid='" . $val['catalogueid'] . "'";
                }
                if ($val['aod'] != '') {
                    if ($sql != '') {
                        $sql .= ',';
                    }
                    $sql .= "  aod='" . $val['aod'] . "'  ";
                }
                if ($val['dtname'] != '') {
                    if ($sql != '') {
                        $sql .= ',';
                    }

                    //$dtname = iconv("UTF-8", "GBK", Urldecode($val['dtname']));
                    $dtname = Urldecode($val['dtname']);

                    if ($isEnglish) {
                        $sql .= "  dtname_en='" . $dtname . "' ,  dtnameshort_en='" . $dtname . "'";
                    } else {
                        $sql .= "  dtname='" . $dtname . "' ,  dtnameshort='" . $dtname . "'";
                    }
                }
                if ($sql != '') {
                    $sql .= ',updatetime=NOW()';
                } else {

                    $arr['Message'] = '参数缺失';
                    if ($isEnglish) $arr['Message'] = ("Parameter Missing");
                    $json_string = $this->pri_JSON($arr);
                    echo $json_string;
                    exit;
                }
                $this->_dao->execute("update `dc_search_custom` set $sql where mc_type='" . $mc_type . "' and MID='" . $mid . "' and Uid='" . $uid . "' and   IsEnglish='" . $isEnglish . "'  and ID=" . $val['ID']);
                //echo "update `dc_search_custom` set $sql where ID=".$val['ID'];echo '<br>';
                $updaterow = $this->_dao->getRow("select *  from `dc_search_custom` where mc_type='" . $mc_type . "' and MID='" . $mid . "' and Uid='" . $uid . "' and   IsEnglish='" . $isEnglish . "'   and  ID=" . $val['ID']);
                //echo '<pre>';print_r($updaterow);
                if(isset($val['destIds'])&&!empty($val['destIds']))
                {
                    
                    foreach ($val['destIds'] as $k => $v) 
                    {
                        foreach ($v as $k1 => $v1) 
                       {
                            //echo "update `dc_search_custom` set aod='".$v1."' where ID='".$k1."'";
                            $this->_dao->execute("update `dc_search_custom` set aod='".$v1."' where ID='".$k1."'");
                            if($updaterow['ID']==$k1)
                            {
                                $updaterow['aod']=$v1;
                            }
                       }  
                    }
                }
                $update = array(
                    'ID' => $updaterow['ID'],
                    'catalogueid' => $updaterow['catalogueid'],

                    'dtname' => $updaterow['dtname'],
                    'status' => '',
                    'aod' => isset($val['destIds'])?$updaterow['aod']:$updaterow['ID'],
                    'updatetime' => $updaterow['updatetime'],
                    'hasleaves' => '0',
                    'leaves' => '[]',
                );

                $arr['Results'][$i] = $update;
                $i++;
            }
        }

        $arr['Success'] = 1;

        $arr['Message'] = '成功';
        if ($isEnglish) $arr['Message'] = ("Success");

        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    public function SearchListGetByKey($params)
    {
        if (!empty($params['isEnglish'])) $isEnglish = $params['isEnglish'];
        else $isEnglish = 0;

        //获取已购买数据
        $GUID = $params['GUID'];
        $SignCS = $params['SignCS'];
        $SystemType = $params['SystemType'];
        $mc_type = $params['mc_type'];
        $action = $params['action'];        //接口名称
        if (!empty($params['mc_type'])) $mc_type = $params['mc_type'];
        else $mc_type = 0;
        $needCompress = $params['needCompress'];  //判断是否封装
        $ip = $this->getIP();
        $isen = "";
        if ($isEnglish == 1) {
            $isen = "_en";

        }

        //$querykey = iconv("UTF-8", "GBK", Urldecode($params['querykey']));
        $querykey =  Urldecode($params['querykey']);
        $isSystemUser=$params['isSystemUser']?$params['isSystemUser']:0;
        //echo '<br>'.$codetype;
        $orderby = html_entity_decode($params['orderby']);
        $orderby = json_decode($orderby, true);
        //echo base64_encode('测');
        if (!empty($params['parentid'])) $parentid = $params['parentid'];
        else $parentid = 0;
        //echo '<pre>';
        //print_R($orderby);

        $arr = array(
            'Success' => '0',
            'ErrorType' => '',
            'Message' => '失败',
        );

        if ($isEnglish) $arr['Message'] = 'Failed!';

        if (array_key_exists($GUID, $GLOBALS["testaccountarr"]) && $SignCS != '' && (in_array($ip, $GLOBALS["testip"]) || $GLOBALS["nowtest"] == 1)) {
            $user = $GLOBALS["testaccountuser"];
        } else {
            $user = $this->_dao->getUser($GUID, $SignCS, $mc_type);
        }

        $mid = $user['Mid'];
        $uid = $user['Uid'];

        $arrtem = $params;
        $actionstr = "";
        foreach ($arrtem as $k => $v) {
            $actionstr .= "&" . $k . "=" . $v;
        }
        //记录日志
        $this->_dao->WriteLog($mid, $uid, $SignCS, $action, $actionstr, $this->getIP(), $SystemType, $this->getSystemVersion(), '客户自定义数据根据关键字查询', '', '', $mc_type);
        //保存的信息存入
		if( $isSystemUser==1&&$mc_type==0)
		{
			$mid='1';
			$uid='488599';
		}


        //parentid下的所有的id数组


        //所有有目录关系的进行处理，key=catalogueid；
        $parentidarr = $this->_dao->query("select ID,parentid,cataloguename  from `dc_search_custom_catalogue`  where mc_type='" . $mc_type . "' and MID='" . $mid . "' and Uid='" . $uid . "' and status=1  and  IsEnglish='" . $isEnglish . "'");
        foreach ($parentidarr as $val) {
            $parentidmenu[$val['parentid']][] = $val['ID'];
            $menuparenid[$val['ID']] = $val['parentid'];
            $menuname[$val['ID']] = $val['cataloguename'];
        }

        $row = $this->_dao->query("select *  from `dc_search_custom_catalogue` where mc_type='" . $mc_type . "' and MID='" . $mid . "' and Uid='" . $uid . "' and status=1  and  IsEnglish='" . $isEnglish . "'");
        foreach ($row as $val) {
            $rowarr[$val['ID']] = $val;
        }
        $parentid_arr = $this->getallid($parentid, $parentidmenu);
        // echo  $parentid_arr;exit;
        // dt开头的对应dc_search_custom表的sql
        // ct开头的对应dc_search_custom_catalogue表的sql
        $dtsql = " and  catalogueid in($parentid_arr)";
        $ctsql = " and  parentid in($parentid_arr)";


        if (empty($querykey) && $querykey != '0') {
            $arr['Message'] = '请输入关键字';
            if ($isEnglish) $arr['Message'] = "Please Input Key Words";
            $json_string = $this->pri_JSON($arr);
            echo $json_string;
            exit;
        }

        if ($querykey != '') {//拼接关键字

            $dtsql .= " and dtname like binary '%" . $querykey . "%'";
            $ctsql .= "and cataloguename like binary '%" . $querykey . "%'";
        }

        if (!empty($orderby['name']) && !empty($orderby['time'])) {

            $arr['Message'] = '排序错乱，time和name不能同时上传' ;
            if ($isEnglish) $arr['Message'] = ("Wrong Order! No Simultaneous Upload of Time and Name");
            $json_string = $this->pri_JSON($arr);
            echo $json_string;
            exit;
        }

        if (!empty($orderby['name'])) {//拼接名称排序
            $dtsql .= " order by dtname " . $orderby['name'];
            $ctsql .= " order by cataloguename " . $orderby['name'];
        } else if (!empty($orderby['time'])) {//拼接时间排序
            $dtsql .= " order by updatetime " . $orderby['time'];
            $ctsql .= " order by updatetime " . $orderby['time'];
        } else {
            $dtsql .= " order by aod asc,ID asc";
        }


        if ($this->checkGUID($params)) {

            $selectsql = "select ID,dtname" . $isen . " as dtname,catalogueid,dtnameshort" . $isen . " as dtnameshort,ImageType,
					DTID1 ,
					DTID1Sub,
					Data1Type,
					Data1Type1,
					Data1Image,
					Data1Pre,
					Data1AddSub,
					Data1AddSubHundCore,
					Data1Type2,
					Data1Type3,
					Data1Type4,
					Data1Type5 ,
					DTID2 ,
					DTID2Sub,
					Data2Type ,Data2Image,
					DTID3 ,
					DTID3Sub ,
					Data3Type , Data3Image,
					DTID4 ,
					DTID4Sub ,
					Data4Type , Data4Image,
					zhou1,zhou2,zhou3,zhou4,
					isjz,isbg,isfg,color,aod,
					LineTitle1,LineTitle2,LineTitle3,LineTitle4,
					GongshiID1,GongshiID2,GongshiID3,GongshiID4,catalogueid,updatetime,DTIDJson,ChartExtLineStyle,DateFormat
					from dc_search_custom where  IsEnglish='" . $isEnglish . "' and MID = '" . $mid . "' and Uid = '$uid' and mc_type = '$mc_type' $dtsql";

            $sear = $this->_dao->query($selectsql);


            //if($mc_type=='1')
            if (empty($orderby['name']) && empty($orderby['time'])) {//name，time同时为空按与按顺序排序
                usort($sear, "pinyin_sort");
            }
            foreach ($sear as &$tmp) {
                //$tmp['dtname'] = base64_encode( $this->utf8ToGb2312( $tmp['dtname'] ));
                //$tmp['dtname']=$tmp['dtname'];
                //$tmp['dtnameshort'] = base64_encode( $this->utf8ToGb2312( $tmp['dtnameshort'] ) );
                for ($i = 1; $i <= 4; $i++) {
                    if ($tmp['GongshiID' . $i]) {
                        $gsname = $this->_dao->getone("select Name from dc_formula_custom where id='" . $tmp['GongshiID' . $i] . "' limit 1");
                        $tmp['GSName' . $i] = $gsname;
                    } else {
                        $tmp['GSName' . $i] = "";
                    }
                    $tmp['LineTitle' . $i] = $tmp['LineTitle' . $i];
                }
            }


            //过滤换行符
            foreach ($sear as $k => $v) {

                $sear[$k]['LineTitle1'] = str_replace(array("\r\n", "\r", "\n"), "", $v['LineTitle1']);
                $sear[$k]['LineTitle2'] = str_replace(array("\r\n", "\r", "\n"), "", $v['LineTitle2']);
                $sear[$k]['LineTitle3'] = str_replace(array("\r\n", "\r", "\n"), "", $v['LineTitle3']);
                $sear[$k]['LineTitle4'] = str_replace(array("\r\n", "\r", "\n"), "", $v['LineTitle4']);

                $sear[$k]['DTIDJson'] = array();
                if (!empty($v['DTIDJson'])) {

                    $dj = json_decode($v['DTIDJson'], true);
                    //    echo '<pre>';echo $v['ID'];echo '<br>';
                    //    print_R($v['DTIDJson']); echo '<br>';
                    foreach ($dj as $k1 => $v1) {
                        $DTIDJson_arr = $v1;

                        $gsname = $this->_dao->getone("select Name from dc_formula_custom where id='" . $DTIDJson_arr['GongshiID'] . "' limit 1");

                        $DTIDJson_arr['GSName'] = $gsname;
                        $sear[$k]['DTIDJson'][$k1] = $DTIDJson_arr;
                    }

                }
                $sear[$k]['ChartExtLineStyle'] = array();
                if (!empty($v['ChartExtLineStyle'])) {
                    $ces = json_decode($v['ChartExtLineStyle'], true);

                    foreach ($ces as $k2 => $v2) {
                        $sear[$k]['ChartExtLineStyle'][$k2] = $v2;
                    }

                }
            }


            if (!empty($parentid)) {

                $parent_arr = $this->_dao->getrow("select *  from `dc_search_custom_catalogue` where mc_type='" . $mc_type . "' and MID='" . $mid . "' and Uid='" . $uid . "'  and  IsEnglish='" . $isEnglish . "' and status=1 and ID=" . $parentid);
                $sear_arr['ID'] = $parent_arr['ID'];
                $sear_arr['parentid'] = $parent_arr['parentid'];
                $sear_arr['cataloguename'] = $parent_arr['cataloguename'];
                $sear_arr['status'] = $parent_arr['status'];
                $sear_arr['aod'] = $parent_arr['aod'];
                $sear_arr['updatetime'] = $parent_arr['updatetime'];
            } else {
                //我的数据
                $sear_arr['ID'] = 0;
                $sear_arr['parentid'] = "";
                $sear_arr['cataloguename'] = "我的数据";
                if ($isEnglish) $sear_arr['cataloguename'] = ("My Data");
                $sear_arr['status'] = '';
                $sear_arr['aod'] = '';
                $sear_arr['updatetime'] = '';
            }
            $iscatalogue = '';
            $isnotcatalogue = array();


            //过滤换行符
            foreach ($sear as $k => $v) {

                $sear[$k]['LineTitle1'] = str_replace(array("\r\n", "\r", "\n"), "", $v['LineTitle1']);
                $sear[$k]['LineTitle2'] = str_replace(array("\r\n", "\r", "\n"), "", $v['LineTitle2']);
                $sear[$k]['LineTitle3'] = str_replace(array("\r\n", "\r", "\n"), "", $v['LineTitle3']);
                $sear[$k]['LineTitle4'] = str_replace(array("\r\n", "\r", "\n"), "", $v['LineTitle4']);

            }


            foreach ($sear as $k => $v) {
                //$querykey
                if (!empty($querykey) && strstr($v['dtname'], $querykey)) {//有关键字
                    $isnotcatalogue[$k] = $v;
                } else {
                    $isnotcatalogue[$k] = $v;
                }

            }

            if (!empty($isnotcatalogue)) {
                $sear_arr['hasleaves'] = 1;
                if (empty($orderby['name']) && empty($orderby['time'])) {
                    //name，time同时为空按与按顺序排序,
                    //usort($isnotcatalogue,"pinyin_sort");
                    //同时为空按原顺序排列不作操作了

                }

                foreach ($isnotcatalogue as $k => $v) {
                    //$isnotcatalogue[$k]['path']=  base64_encode('我的数据>'.$this->getpath($v['catalogueid'],$menuparenid,$menuname));//需要递归插入
                    $ispath = $this->getpath($v['catalogueid'], $menuparenid, $menuname);//查出当前目录之前的目录
                    $ispath = $ispath . '>' . $menuname[$v['catalogueid']];//拼接上当前数据的目录
                    $path = (($isSystemUser==1&&$mc_type==0)?'推荐数据':'我的数据'). $ispath ;
                    if ($isEnglish) $path = ("My Data" . $ispath);
                    $isnotcatalogue[$k]['path'] = $path;
                }
                $sear_arr['leaves'] = $isnotcatalogue;
            } else {
                $sear_arr['hasleaves'] = 0;
                $sear_arr['leaves'] = array();
            }

            $custom_arr = $this->_dao->query("select *  from `dc_search_custom_catalogue`  where   IsEnglish='" . $isEnglish . "'  and mc_type='" . $mc_type . "' and MID='" . $mid . "' and Uid='" . $uid . "' and status=1  $ctsql");

            if (!empty($custom_arr)) {
                $sear_arr['hasnode'] = 1;
                $i = 0;

                foreach ($custom_arr as $k => $v) {

                    $catalogue[$i]['ID'] = $v['ID'];
                    $catalogue[$i]['parentid'] = $v['parentid'];
                    $catalogue[$i]['cataloguename'] =  $v['cataloguename'];
                    //$catalogue[$i]['cataloguename']=$v['cataloguename'];
                    $catalogue[$i]['status'] = $v['status'];
                    $catalogue[$i]['aod'] = $v['aod'];
                    $catalogue[$i]['updatetime'] = $v['updatetime'];
                    //$catalogue[$i]['path']=base64_encode('我的数据');
                    //$catalogue[$i]['path']='我的数据';

                    //$catalogue[$i]['path']= '我的数据>'.$this->getpath($v['ID'],$menuparenid,$menuname);//需要递归插入
                    $ispath = $this->getpath($v['ID'], $menuparenid, $menuname);


                    $path = (($isSystemUser==1&&$mc_type==0)?'推荐数据':'我的数据'). $ispath ;
                    if ($isEnglish) $path = ("My Data" . $ispath);
                    $catalogue[$i]['path'] = $path;


                    $i++;
                }

                $sear_arr['SubResults'] = $catalogue;
            } else {
                $sear_arr['hasnode'] = 0;
                $sear_arr['SubResults'] = array();
            }
            //echo '<pre>';print_R($sear_arr);


        } else {
            exit;
        }


        $arr['Success'] = 1;
        $arr['Message'] = '成功';
        if ($isEnglish) $arr['Message'] = ("Success");
        $sear_zero[0] = $sear_arr;
        if ($needCompress == 1) {
            $arr['Results'] = base64_encode(gzencode(json_encode($sear_zero), 9));//压缩找个方法
        } else {
            $arr['Results'] = $sear_zero;
        }
        $json_string = $this->pri_JSON($arr);
        echo $json_string;

    }


    public function GetDataTypesBySymbol($params)
    {
        $SystemType = '';
        if (!empty($params['isEnglish'])) $isEnglish = $params['isEnglish'];
        else $isEnglish = 0;

        //获取已购买数据
        $GUID = $params['GUID'];
        $SignCS = $params['SignCS'];
        $mc_type = $params['mc_type'];
        $Symbol = $params['Symbol'];
        $action = $params['action'];        //接口名称
        if (!empty($params['mc_type'])) $mc_type = $params['mc_type'];
        else $mc_type = 0;
        $needCompress = $params['needCompress'];  //判断是否封装
        $ip = $this->getIP();

        $isen = "";
        $isEnglishsql = '';
        if ($isEnglish == 1) {
            $isen = "_en";
            $isEnglishsql = '  and isEnglish=1';
        } else {
            $isEnglishsql = '  and status=1';

        }


        $ParentID = '3138';//正式测试不一样
        $SYear = '2010';
        $EYear = '2020';
        $scode1 = 'L';
        $isbuy = '1';
        //$querykey=iconv("UTF-8","GBK",Urldecode($params['querykey']));


        $arr = array(
            'Success' => '0',
            'ErrorType' => '',
            'Message' => ('抱歉，您没有权限查看该数据。如需咨询订阅，请联系钢之家客服，联系电话：***********/021-********'),
        );

        if ($isEnglish) $arr['Message'] = 'Failed!';

        if (array_key_exists($GUID, $GLOBALS["testaccountarr"]) && $SignCS != '' && (in_array($ip, $GLOBALS["testip"]) || $GLOBALS["nowtest"] == 1)) {
            $user = $GLOBALS["testaccountuser"];
        } else {
            $user = $this->_dao->getUser($GUID, $SignCS, $mc_type);
        }

        $mid = $user['Mid'];
        $uid = $user['Uid'];

        $arrtem = $params;
        foreach ($arrtem as $k => $v) {
            $actionstr .= "&" . $k . "=" . $v;
        }
        //记录日志
        $this->_dao->WriteLog($mid, $uid, $SignCS, $action, $actionstr, $this->getIP(), $SystemType, $this->getSystemVersion(), '客户自定义数据根据关键字查询', '', '', $mc_type);
        //保存的信息存入

        $selectsql = "select 
				   distinct ID,scode1,scode2,scode3,scode4,scode5,dtname" . $isen . " as dtname,
				   dtymd,dtcomparetype,dbaseType,isSubDatas,subDatasTitle" . $isen . " as subDatasTitle,
				   subDatasDb,subDatasDb2,subAtt1Db" . $isen . " as subAtt1Db,subAtt2Db" . $isen . " as subAtt2Db,subAtt3Db" . $isen . " as subAtt3Db,
				   Data1Type,Data2Type,Data3Type,Data4Type,Data5Type,Data6Type,Data7Type,Data8Type,Data9Type,Data10Type,Data11Type,Data12Type,
				   Data13Type,Data14Type,Data15Type,Data16Type,Data17Type,Data18Type,Data19Type,Data20Type,dtod,ndate" . $isen . " as ndate,
				   ndata1" . $isen . " as ndata1,ndata2" . $isen . " as ndata2,ndata3" . $isen . " as ndata3,ndata4" . $isen . " as ndata4,
				   ndata5" . $isen . " as ndata5,ndata6" . $isen . " as ndata6,ndata7" . $isen . " as ndata7,ndata8" . $isen . " as ndata8,
				   ndata9" . $isen . " as ndata9,ndata10" . $isen . " as ndata10,ndata11" . $isen . " as ndata11,ndata12" . $isen . " as ndata12,
				   ndata13" . $isen . " as ndata13,ndata14" . $isen . " as ndata14,ndata15" . $isen . " as ndata15,ndata16" . $isen . " as ndata16,
				   ndata17" . $isen . " as ndata17,ndata18" . $isen . " as ndata18,ndata19" . $isen . " as ndata19,ndata20" . $isen . " as ndata20,
				   npredata" . $isen . " as npredata,naddsubdata" . $isen . " as naddsubdata,naddsubhundcore" . $isen . " as naddsubhundcore,TargetFlagVar,pinzhong
				   from dc_code_datatype inner join dc_code_datatype_db on dc_code_datatype_db.DID = dc_code_datatype.ID and dc_code_datatype.mc_type='$mc_type' and dc_code_datatype_db.mc_type='$mc_type'
				    where   dtname" . $isen . "= '" . $Symbol . "' and scode1='" . $scode1 . "' $isEnglishsql";

        $sear = $this->_dao->query($selectsql);
        foreach ($sear as $k => $v) {
            $sear[$k]['ParentID'] = $ParentID;
            $sear[$k]['SYear'] = $SYear;
            $sear[$k]['EYear'] = $EYear;
            $sear[$k]['isbuy'] = $isbuy;
            $sear[$k]['dtname'] = iconv("GBK", "UTF-8", $v['dtname']);
        }


        // $sear_zero[0]=$sear;


        $Status = $this->HasRight($GUID);

        if ($Status == '1') {
            $sear_zero = $sear;
            $arr['Success'] = 1;
            $arr['Message'] = ('成功');
            if ($isEnglish) $arr['Message'] = ("Success");
        } else {
            $sear_zero = '';
        }

        if ($needCompress == 1) {
            $arr['Results'] = base64_encode(gzencode(json_encode($sear_zero), 9));//压缩找个方法
        } else {
            $arr['Results'] = $sear_zero;
        }
        $json_string = $this->pri_JSON($arr);
        echo $json_string;

    }


    public function savelinetitlebak($params)
    {

        $arr_all = $this->_dao->query("select ID,ImageTitle_bak,LineTitle1_bak,LineTitle2_bak,LineTitle3_bak,LineTitle4_bak  from `dc_search_custom` ");

        foreach ($arr_all as $k => $v) {
            $sql = "update dc_search_custom set ImageTitle='" . $v['ImageTitle_bak'] . "',LineTitle1='" . $v['LineTitle1_bak'] . "',LineTitle2='" . $v['LineTitle2_bak'] . "',LineTitle3='" . $v['LineTitle3_bak'] . "',LineTitle4='" . $v['LineTitle4_bak'] . "'   where ID=" . $v['ID'];
            $this->_dao->execute($sql);

        }

    }


    public function updatelinetitle($params)
    {

        // $arr_all= $this->_dao->query("select ID,ImageTitle_bak,LineTitle1_bak,LineTitle2_bak,LineTitle3_bak,LineTitle4_bak  from `dc_search_custom` ");

        // foreach($arr_all as $k=>$v){
        // 	$sql="update dc_search_custom set ImageTitle='".$v['ImageTitle_bak']."',LineTitle1='".$v['LineTitle1_bak']."',LineTitle2='".$v['LineTitle2_bak']."',LineTitle3='".$v['LineTitle3_bak']."',LineTitle4='".$v['LineTitle4_bak']."'   where ID=".$v['ID'];
        // 	 $this->_dao->execute($sql);

        // }
        $arr_all = $this->_dao->query("select ID,ImageTitle_bak,LineTitle1_bak,LineTitle2_bak,LineTitle3_bak,LineTitle4_bak  from `dc_search_custom` ");

        foreach ($arr_all as $k => $v) {
            // $sql="update dc_search_custom set ImageTitle='".iconv("GBK","UTF-8",base64_decode($v['ImageTitle_bak']))."',
            // 								  LineTitle1='".iconv("GBK","UTF-8",base64_decode($v['LineTitle1_bak']))."',
            // 								  LineTitle2='".iconv("GBK","UTF-8",base64_decode($v['LineTitle2_bak']))."',
            // 								  LineTitle3='".iconv("GBK","UTF-8",base64_decode($v['LineTitle3_bak']))."',
            // 								  LineTitle4='".iconv("GBK","UTF-8",base64_decode($v['LineTitle4_bak']))."'
            // 								  where ID=".$v['ID'];

            $sql = "update dc_search_custom set ImageTitle='" . str_replace("'", "\'", base64_decode(str_replace(' ', '+', $v['ImageTitle_bak']))) . "',
											  LineTitle1='" . str_replace("'", "\'", base64_decode(str_replace(' ', '+', $v['LineTitle1_bak']))) . "',
											  LineTitle2='" . str_replace("'", "\'", base64_decode(str_replace(' ', '+', $v['LineTitle2_bak']))) . "',
											  LineTitle3='" . str_replace("'", "\'", base64_decode(str_replace(' ', '+', $v['LineTitle3_bak']))) . "',
											  LineTitle4='" . str_replace("'", "\'", base64_decode(str_replace(' ', '+', $v['LineTitle4_bak']))) . "'   
											  where ID=" . $v['ID'];
            $this->_dao->execute($sql);

        }

    }


    private function getallid($id, $parentidmenu)
    {//取出包含的所有的目录的id，带入sql根据条件查询数据与目录，展示
        if ($id == '') $id = 0;

        $sonid = $parentidmenu[$id];


        $i = 0;
        $ids = $id;


        foreach ($sonid as $k => $v) {
            $soncatalogue = $parentidmenu[$v];
            //判断是否还有下一层目录


            if (!empty($soncatalogue)) {


                $son_arr = $this->getallid($v, $parentidmenu);

                $ids .= ',' . $son_arr;
            } else {
                $ids .= ',' . $v;
            }

            $i++;
        }

        //print_R($ids);

        return $ids;

    }

    private function getpath($id, $menuparenid, $menuname)
    {
        /*   if(!empty($menuparenid[$id])){
    $path= $this->getpath($menuparenid[$id],$menuparenid,$menuname,$ismulu).'>'.$menuname[$menuparenid[$id]];

    }else{
     if($ismulu!=1){//1为目录取路径，本身这一层不要了，不为1可以

      $path=$menuname[$id];

     }
    }
    */


        if (!empty($menuparenid[$id])) {
            $path = $this->getpath($menuparenid[$id], $menuparenid, $menuname) . '>' . $menuname[$menuparenid[$id]];
        }

        return $path;

    }

    private function getcengji($id, $cj)
    {
        $pid = $this->_dao->getone("select parentid  from `dc_search_custom_catalogue` where ID='" . $id . "'");
        if (empty($pid)) {
            return $cj;
        } else {
            return $this->getcengji($pid, $cj + 1);

        }

    }


    private function getnextcengji($id, $cj)
    {
        $sonids = $this->_dao->query("select  ID  from `dc_search_custom_catalogue` where parentid='" . $id . "'");
        $num = $cj;


        foreach ($sonids as $ids) {
            $num1 = $this->getnextcengji($ids['ID'], $cj + 1);
            /*  echo 'num1 : '.$num1; echo '<br>';
        echo 'num: '.$num;echo '<br>--------';*/
            if ($num1 >= $num) {
                $num = $num1;
            }
        }
        return $num;
    }

    function HasRight($GUID)
    {
        //获取用户信息

        $user_infos = $this->_dao->getRow("select * from app_session_temp where  GUID ='$GUID'  order by  LoginDate DESC  limit 1");
        $Mid = $user_infos['Mid'];//会员id
        $userid = $user_infos['Uid'];//用户id
        //$TrueName=$user_infos['TrueName'];//用户名

        $PsPrivilage = $this->drcdao->getRow("select * from PsPrivilage where uid ='" . $userid . "' and mc_type='0' and UserType=1 limit 1");
        $Status = "0";
        if ($PsPrivilage['Start'] <= date("Y-m-d") && $PsPrivilage['End'] >= date("Y-m-d")) {
            $Status = "1";
        }
        /*1号钢之家有权限*/
        if ($Mid == "1") {
            $Status = "1";
        }
        //$Status = "0";
        return $Status;
    }


    public function DerivedCatalogue($params)
    {
        $GUID = $params['GUID'];
        $SignCS = $params['SignCS'];
        $mc_type = $params['mc_type'];
        $action = $params['action'];        //接口名称

        $isCatalogue = $params['isCatalogue'];


        $ip = $this->getIP();
        if (!empty($params['mc_type'])) $mc_type = $params['mc_type'];
        else $mc_type = '0';
        if (!empty($params['isEnglish'])) $isEnglish = $params['isEnglish'];
        else $isEnglish = '0';

        if (!empty($params['parentid'])) $parentid = $params['parentid'];
        else $parentid = '0';


        $arr = array(
            'Success' => '0',
            'Message' => '失败',
            'Results' => 'null',
        );
        if ($isEnglish) $arr['Message'] = 'Failed!';

        if (array_key_exists($GUID, $GLOBALS["testaccountarr"]) && $SignCS != '' && (in_array($ip, $GLOBALS["testip"]) || $GLOBALS["nowtest"] == 1)) {
            $user = $GLOBALS["testaccountuser"];
        } else {
            $user = $this->_dao->getUser($GUID, $SignCS, $mc_type);
        }

        $mid = $user['Mid'];
        $uid = $user['Uid'];


        $arrtem = $params;
        $actionstr = "";
        foreach ($arrtem as $k => $v) {
            $actionstr .= "&" . $k . "=" . $v;
        }
        $sear = array();

        if ($isCatalogue == 0) {//导出单条数据

            $selectsql = "select ID,catalogueid from dc_search_custom  where IsEnglish='" . $isEnglish . "'   and MID = '" . $mid . "' and Uid = '$uid' and mc_type = '$mc_type' and ID ='" . $parentid . "' order by binary(dtname) asc,aod desc,ID desc";

            $row = $this->_dao->getrow($selectsql);
            $sear[0]['id'] = $row['ID'];
            $sear[0]['cid'] = $row['catalogueid'];
            $sear[0]['mid'] = $mid;
            $sear[0]['uid'] = $uid;
            $sear[0]['mc_type'] = $mc_type;
            $sear[0]['isEnglish'] = $isEnglish;
            $sear[0]['isCatalogue'] = '0';
            $sear[0]['cj'] = '1';


        } else {//导出目录$isCatalogue=0
            //获取$parentid及其下面所有目录与数据
            $sear = $this->parentidsearch_mulu($parentid, $isEnglish, $mid, $uid, $mc_type, '1');
            //加上起始目录
            if (empty($parentid)) {

                $frist_arr['cid'] = '0';//代表我的数据这一层
                $frist_arr['pid'] = '';
                $frist_arr['mid'] = $mid;
                $frist_arr['uid'] = $uid;
                $frist_arr['mc_type'] = $mc_type;
                $frist_arr['isEnglish'] = $isEnglish;
                $frist_arr['isCatalogue'] = '1';
                $frist_arr['cj'] = '1';
            } else {
                $fristcatalogue = $this->_dao->getrow("select ID,parentid  from `dc_search_custom_catalogue`  where ID ='" . $parentid . "' and mc_type='" . $mc_type . "' and MID='" . $mid . "' and Uid='" . $uid . "'  and  IsEnglish='" . $isEnglish . "' and status=1  order by binary(cataloguename) asc");
                // echo "select ID,parentid  from `dc_search_custom_catalogue`  where ID ='".$parentid."' and mc_type='".$mc_type."' and MID='".$mid."' and Uid='".$uid."'  and  IsEnglish='".$isEnglish."' and status=1  order by binary(cataloguename) asc";
                $frist_arr['cid'] = $fristcatalogue['ID'];
                $frist_arr['pid'] = $fristcatalogue['parentid'];
                $frist_arr['mid'] = $mid;
                $frist_arr['uid'] = $uid;
                $frist_arr['mc_type'] = $mc_type;
                $frist_arr['isEnglish'] = $isEnglish;
                $frist_arr['isCatalogue'] = '1';
                $frist_arr['cj'] = '1';
            }

            $sear[] = $frist_arr;

        }

        $this->_dao->WriteLog($mid, $uid, $SignCS, $action, $actionstr, $this->getIP(), $this->getSystemType(), $this->getSystemVersion(), "我的定制导出", '', '', $mc_type);

        $arr['Success'] = 1;
        $arr['Message'] = ('成功');
        if ($isEnglish) $arr['Message'] = ("Success");
        $arr['Results'] = $sear;


        $this->_dao->execute("insert into dc_derivedjson  
					(uid,MID,IsEnglish,mc_type,dctime,json)
					values
					('" . $uid . "','" . $mid . "','" . $isEnglish . "','" . $mc_type . "',NOW(),'" . json_encode($arr['Results']) . "')");

        $json_string = $this->pri_JSON($arr);
        echo $json_string;
        exit;

    }


    public function RecycleListGet($params)
    {
        //回收站列表接口
        if (!empty($params['isEnglish'])) $isEnglish = $params['isEnglish'];
        else $isEnglish = 0;

        //获取已购买数据
        $GUID = $params['GUID'];
        $SignCS = $params['SignCS'];
        $mc_type = $params['mc_type'];
        $action = $params['action'];        //接口名称
        if (!empty($params['mc_type'])) $mc_type = $params['mc_type'];
        else $mc_type = 0;
        $needCompress = $params['needCompress'];  //判断是否封装
        $ip = $this->getIP();
        $isen = "";
        if ($isEnglish == 1) {
            $isen = "_en";

        }

        //$querykey = iconv("UTF-8", "GBK", Urldecode($params['querykey']));
        $querykey = Urldecode($params['querykey']);

        $orderby = html_entity_decode($params['orderby']);
        $orderby = json_decode($orderby, true);
        //echo base64_encode('测');


        $arr = array(
            'Success' => '0',
            'ErrorType' => '',
            'Message' =>'失败',
        );

        if ($isEnglish) $arr['Message'] = 'Failed!';

        if (array_key_exists($GUID, $GLOBALS["testaccountarr"]) && $SignCS != '' && (in_array($ip, $GLOBALS["testip"]) || $GLOBALS["nowtest"] == 1)) {
            $user = $GLOBALS["testaccountuser"];
        } else {
            $user = $this->_dao->getUser($GUID, $SignCS, $mc_type);
        }

        $mid = $user['Mid'];
        $uid = $user['Uid'];

        $arrtem = $params;
        $actionstr = "";
        foreach ($arrtem as $k => $v) {
            $actionstr .= "&" . $k . "=" . $v;
        }
        //记录日志
        // $this->_dao->WriteLog($mid, $uid, $SignCS, $action,$actionstr, $this->getIP(), $SystemType, $this->getSystemVersion(),iconv('UTF-8', 'GBK', '客户自定义数据回收站数据查询'),'','',$mc_type);
        //保存的信息存入

        if (!empty($querykey)) {//拼接关键字
            $sql = " and title like binary '%" . $querykey . "%'";
        }

        $selectsql = "select id,title,isCatalogue,dctime from `dc_derivedjson`  where IsEnglish ='" . $isEnglish . "' and MID ='" . $mid . "' and mc_type='" . $mc_type . "' and Uid='" . $uid . "' and isdel=0 and type=1 $sql";


        $sear = $this->_dao->query($selectsql);
        foreach ($sear as $k => $v) {

            //$sear[$k]['title'] = base64_encode($this->utf8ToGb2312($v['title']));

        }


        $arr['Success'] = 1;
        $arr['Message'] = "成功";
        if ($isEnglish) $arr['Message'] = ("Success");

        if ($needCompress == 1) {
            $arr['Results'] = base64_encode(gzencode(json_encode($sear), 9));//压缩找个方法
        } else {
            $arr['Results'] = $sear;
        }

//		print_r($arr);
        $json_string = $this->pri_JSON($arr);
        echo $json_string;


    }

    public function RecycleOp($params)
    {


        $GUID = $params['GUID'];
        $SignCS = $params['SignCS'];
        $mc_type = $params['mc_type'];
        $action = $params['action'];        //接口名称
        // echo $params['isSubs'];
        $IDData = html_entity_decode($params["IDData"]);
        $IDDatas = json_decode($IDData, true);
        $optype = $params['optype'];
        $ip = $this->getIP();
        if (!empty($params['mc_type'])) $mc_type = $params['mc_type'];
        else $mc_type = 0;
        if (!empty($params['isEnglish'])) $isEnglish = $params['isEnglish'];
        else $isEnglish = 0;


        $arr = array(
            'Success' => '0',
            'Message' => ('失败'),
            'Results' => 'null',
        );
        if ($isEnglish) $arr['Message'] = 'Failed!';

        if (array_key_exists($GUID, $GLOBALS["testaccountarr"]) && $SignCS != '' && (in_array($ip, $GLOBALS["testip"]) || $GLOBALS["nowtest"] == 1)) {
            $user = $GLOBALS["testaccountuser"];
        } else {
            $user = $this->_dao->getUser($GUID, $SignCS, $mc_type);
        }
        $mid = $user['Mid'];
        $uid = $user['Uid'];


        $arrtem = $params;
        $actionstr = "";
        foreach ($arrtem as $k => $v) {
            $actionstr .= "&" . $k . "=" . $v;
        }
//假设一个json
//$params["DTIDJson"]='[{"id":"4170","cid":"1163","mid":"1","uid":"1","mc_type":"0","isEnglish":"0","isCatalogue":"0","cj":"1"}]';

        $ids = '';
        if ($optype == 1) {
            foreach ($IDDatas as $IDk => $IDval) {

                if ($ids == '') {
                    $ids = "'" . $IDval['id'] . "'";
                } else {
                    $ids .= ",'" . $IDval['id'] . "'";
                }

                $seljson = $this->_dao->getrow("select *  from `dc_derivedjson`  where type=1 and isdel=0 and id= '" . $IDval['id'] . "'");

                $Datas = json_decode($seljson['json'], true);


                foreach ($Datas as $k => $v) {
                    $isDataOrCatalogue[$v['cj']][] = $v;
                }


                //取第一层级的isCatalogue来判断第一层级是数据还是目录
                if ($isDataOrCatalogue[1][0]['isCatalogue'] == 1) {//目录


                    $arr_cj = array();
                    foreach ($Datas as $k => $v) {
                        $arr_cj[$v['cj']][] = $v;
                    }


                    $old_cid = $arr_cj[1][0]['cid'];
                    $frist = $this->_dao->getrow("select *  from `dc_search_custom_catalogue_recycle`  where ID='" . $arr_cj[1][0]['cid'] . "' ");

                    $this->_dao->execute("insert into dc_search_custom_catalogue 
					(parentid,cataloguename,status,aod,createtime,updatetime,Uid,MID,mc_type,IsEnglish)
					values
					('0','" . $frist['cataloguename'] . "','1','',NOW(),NOW(),'" . $frist['Uid'] . "','" . $frist['MID'] . "','" . $frist['mc_type'] . "','" . $frist['isEnglish'] . "')");

                    $insertid = $this->_dao->getone("SELECT LAST_INSERT_ID()");
                    $this->_dao->execute("update dc_search_custom_catalogue set aod='" . $insertid . "' where ID='" . $insertid . "' ");

                    //二级的目录pid原来的要改为$insertid，二级的数据cid要改为$insertid
                    $this->Recycleinsertcatalogue(2, $insertid, $arr_cj, $old_cid);//2代表处理层级，$insertid需要修改的pid，cid

                } else {//数据处理，处理完就处理完，不需要递归

                    $row = $this->_dao->getrow("select *  from `dc_search_custom_recycle`  where ID='" . $isDataOrCatalogue[1][0]['id'] . "' ");

                    $array = array(
                        'MID', 'Uid', 'dtname', 'dtnameshort', 'DTID1', 'DTID1Sub', 'Data1Type1', 'Data1Pre', 'Data1AddSub', 'Data1AddSubHundCore', 'Data1Type2', 'Data1Type3', 'Data1Type4', 'Data1Type5', 'DTID2', 'DTID2Sub', 'Data2Type', 'DTID3', 'DTID3Sub', 'Data3Type', 'DTID4', 'DTID4Sub', 'Data4Type', 'aod', 'ImageType', 'ImageTitle', 'Data1Image', 'Data2Image', 'Data3Image', 'Data4Image', 'zhou1', 'zhou2', 'zhou3', 'zhou4', 'dtname_en', 'dtnameshort_en', 'IsEnglish', 'mc_type', 'isbg', 'isjz', 'color', 'isfg', 'LineTitle1', 'LineTitle2', 'LineTitle3', 'LineTitle4', 'GongshiID1', 'GongshiID2', 'GongshiID3', 'GongshiID4', 'Data1Type', 'createtime', 'updatetime', 'DTIDJson', 'ChartExtLineStyle', 'LineTitle1_bak', 'LineTitle2_bak', 'LineTitle3_bak', 'LineTitle4_bak', 'ImageTitle_bak', 'DateFormat'
                    );
                    $row['dtname'] = str_replace("'", "\'", $row['dtname']);
                    $row['dtnameshort'] = str_replace("'", "\'", $row['dtnameshort']);
                    $row['DTIDJson'] = str_replace("'", "\'", $row['DTIDJson']);
                    $data = $this->getdata($array, $row);

                    $this->_dao->execute("insert into dc_search_custom set $data,catalogueid='0'");

                    //记录日志
                    $this->_dao->WriteLog($mid, $uid, $SignCS, $action,$actionstr, $this->getIP(), $this->getSystemType(), $this->getSystemVersion(),'客户自定义数据回收站数据恢复','','',$mc_type);


                }
            }
        }

        if ($optype == 2) {
            foreach ($IDDatas as $IDk => $IDval) {
                if ($ids == '') {
                    $ids = "'" . $IDval['id'] . "'";
                } else {
                    $ids .= ",'" . $IDval['id'] . "'";
                }
            }
        }
        //删除回收站
        $this->_dao->execute("update dc_derivedjson set isdel=1 where  type=1 and id in ($ids)");
        //记录日志
        $this->_dao->WriteLog($mid, $uid, $SignCS, $action,$actionstr, $this->getIP(), $this->getSystemType(), $this->getSystemVersion(),'客户自定义数据回收站数据删除','','',$mc_type);


        $arr['Success'] = 1;
        $arr['Message'] = '成功';
        if ($isEnglish) $arr['Message'] = base64_encode("Success");
        $json_string = $this->pri_JSON($arr);
        echo $json_string;
        exit;

    }


    public function ImportCatalogue($params)
    {


        $GUID = $params['GUID'];
        $SignCS = $params['SignCS'];
        $mc_type = $params['mc_type'];
        $action = $params['action'];        //接口名称

        $parentid = $params["parentid"];

        $ip = $this->getIP();
        if (!empty($params['mc_type'])) $mc_type = $params['mc_type'];
        else $mc_type = 0;
        if (!empty($params['isEnglish'])) $isEnglish = $params['isEnglish'];
        else $isEnglish = 0;


        $arr = array(
            'Success' => '0',
            'Message' => '失败',
            'Results' => 'null',
        );
        if ($isEnglish) $arr['Message'] = 'Failed!';

        if (array_key_exists($GUID, $GLOBALS["testaccountarr"]) && $SignCS != '' && (in_array($ip, $GLOBALS["testip"]) || $GLOBALS["nowtest"] == 1)) {
            $user = $GLOBALS["testaccountuser"];
        } else {
            $user = $this->_dao->getUser($GUID, $SignCS, $mc_type);
        }
        $mid = $user['Mid'];
        $uid = $user['Uid'];


        $arrtem = $params;
        $actionstr = "";
        foreach ($arrtem as $k => $v) {
            $actionstr .= "&" . $k . "=" . $v;
        }
        //假设一个json
        //$params["DTIDJson"]='[{"id":"4170","cid":"1163","mid":"1","uid":"1","mc_type":"0","isEnglish":"0","isCatalogue":"0","cj":"1"}]';
        $DTIDJson = html_entity_decode(Urldecode($params["DTIDJson"]));
        $Datas = json_decode($DTIDJson, true);

        $seljson = $this->_dao->getrow("select *  from `dc_derivedjson`  where type=0 and isdel=0 and json='" . str_replace("'", "\'", json_encode($Datas)) . "'");

        if (empty($seljson)) {
            $arr['Message'] =  '文件删除或损坏，导入失败';
            if ($isEnglish) $arr['Message'] = "File corruption!";
            $json_string = $this->pri_JSON($arr);
            echo $json_string;
            exit;
        }

        $Datas = json_decode($DTIDJson, true);


        foreach ($Datas as $k => $v) {
            $isDataOrCatalogue[$v['cj']][] = $v;
        }

//导入的mc_type与当前客户端的mc_type要一致

        if ($isDataOrCatalogue[1][0]['mc_type'] != $mc_type) {
            $arr = array(
                'Success' => '0',
                'Message' => '客户端系统不同，不能添加',
                'Results' => 'null',
            );
            if ($isEnglish) $arr['Message'] = 'The client system is different and can not be added!';
            $json_string = $this->pri_JSON($arr);
            echo $json_string;
            exit;
        }


        //导入的isEnglish与当前客户端的isEnglish要一致

        if ($isDataOrCatalogue[1][0]['isEnglish'] != $isEnglish) {
            $arr = array(
                'Success' => '0',
                'Message' => '客户端语言类型不同，不能添加' ,
                'Results' => 'null',
            );
            if ($isEnglish) $arr['Message'] = 'The client language type is different and can not be added!';
            $json_string = $this->pri_JSON($arr);
            echo $json_string;
            exit;
        }


        //取第一层级的isCatalogue来判断第一层级是数据还是目录
        if ($isDataOrCatalogue[1][0]['isCatalogue'] == 1) {//目录

            $Cataloguenum = array();
            foreach ($Datas as $k => $v) {
                if ($v['isCatalogue'] == 1) {
                    $Cataloguenum[$v['cj']][] = $v;
                }

            }
            if (empty($parentid)) {
                $n = 1;//放在我的数据下面算有了一层
            } else {
                $n = $this->getcengji($parentid, 2);
            }
            //查出父目录的层级加上将要添加的层级大于五级不给添加，我的数据这一层算一级
            if ((count($Cataloguenum) + $n) > 5) {
                $arr = array(
                    'Success' => '0',
                    'Message' => '目录大于五层，不能添加',
                    'Results' => 'null',
                );
                if ($isEnglish) $arr['Message'] = 'No more than five floors!';
                $json_string = $this->pri_JSON($arr);
                echo $json_string;
                exit;
            }

            $arr_cj = array();
            foreach ($Datas as $k => $v) {
                $arr_cj[$v['cj']][] = $v;
            }

            //第一层级代表总目录，此层级只有一个目录
            if (!empty($arr_cj[1][0]['cid'])) {
                $old_cid = $arr_cj[1][0]['cid'];
                $frist = $this->_dao->getrow("select *  from `dc_search_custom_catalogue`  where ID='" . $arr_cj[1][0]['cid'] . "' and mc_type='" . $arr_cj[1][0]['mc_type'] . "' and MID='" . $arr_cj[1][0]['mid'] . "' and Uid='" . $arr_cj[1][0]['uid'] . "'  and  IsEnglish='" . $arr_cj[1][0]['isEnglish'] . "' and status=1");
                if($frist)//目录未被删除
                {
                    $this->_dao->execute("insert into dc_search_custom_catalogue 
					(parentid,cataloguename,status,aod,createtime,updatetime,Uid,MID,mc_type,IsEnglish)
					values
					('" . $parentid . "','" . $frist['cataloguename'] . "','1','',NOW(),NOW(),'" . $uid . "','" . $mid . "','" . $arr_cj[1][0]['mc_type'] . "','" . $arr_cj[1][0]['isEnglish'] . "')");
                    $insertid = $this->_dao->getone("SELECT LAST_INSERT_ID()");

                    $this->_dao->execute("update dc_search_custom_catalogue set aod='" . $insertid . "' where ID='" . $insertid . "' ");
                }
                
            } else {//cid=0代表的是我的数据这一层目录过来的
                //插入我的数据
                $old_cid = 0;
                $this->_dao->execute("insert into dc_search_custom_catalogue 
					(parentid,cataloguename,status,aod,createtime,updatetime,Uid,MID,mc_type,IsEnglish)
					values
					('" . $parentid . "','我的数据','1','',NOW(),NOW(),'" . $uid . "','" . $mid . "','" . $arr_cj[1][0]['mc_type'] . "','" . $arr_cj[1][0]['isEnglish'] . "')");
                $insertid = $this->_dao->getone("SELECT LAST_INSERT_ID()");

                $this->_dao->execute("update dc_search_custom_catalogue set aod='" . $insertid . "' where ID='" . $insertid . "' ");

            }
            //二级的目录pid原来的要改为$insertid，二级的数据cid要改为$insertid
            $this->insertcatalogue(2, $insertid, $arr_cj, $mid, $uid, $old_cid);//2代表处理层级，$insertid需要修改的pid，cid

        } else {//数据

            $row = $this->_dao->getrow("select *  from `dc_search_custom`  where ID='" . $isDataOrCatalogue[1][0]['id'] . "' and IsEnglish='" . $isDataOrCatalogue[1][0]['isEnglish'] . "' and MID = '" . $isDataOrCatalogue[1][0]['mid'] . "' and Uid = '" . $isDataOrCatalogue[1][0]['uid'] . "' and mc_type = '" . $isDataOrCatalogue[1][0]['mc_type'] . "'");

            $array = array(
                "dtname",
                "dtname_en",
                "ImageTitle",
                "DTID1",
                "DTID1Sub",
                "Data1Type",
                "Data1Type1",
                "Data1Pre",
                "Data1AddSub",
                "Data1AddSubHundCore",
                "Data1Type2",
                "Data1Type3",
                "Data1Type4",
                "Data1Type5",
                "DTID2",
                "DTID2Sub",
                "Data2Type",
                "DTID3",
                "DTID3Sub",
                "Data3Type",
                "DTID4",
                "DTID4Sub",
                "Data4Type",
                "ImageType",
                "Data1Image",
                "Data2Image",
                "Data3Image",
                "Data4Image",
                "zhou1",
                "zhou2",
                "zhou3",
                "zhou4",
                "isjz",
                "isbg",
                "isfg",
                "color",
                "LineTitle1",
                "LineTitle2",
                "LineTitle3",
                "LineTitle4",
                //  "GongshiID1",
                // "GongshiID2",
                // "GongshiID3",
                // "GongshiID4",
                // "DTIDJson",
                "ChartExtLineStyle",
                "dtnameshort",
                "LineTitle1_bak",
                "LineTitle2_bak",
                "LineTitle3_bak",
                "LineTitle4_bak",
                "ImageTitle_bak",
                "DateFormat",

            );
            // "GongshiID1","GongshiID2","GongshiID3","GongshiID4",DTIDJson中的公式id需要处理
            if (empty($row['GongshiID1'])) {
                $GongshiID1 = 0;
            } else {
                $gs1 = $this->_dao->getrow("select *  from `dc_formula_custom`  where ID='" . $row['GongshiID1'] . "'");
                $this->_dao->execute("insert into dc_formula_custom 
		(MID,Uid,Name,GongShi,Unit,dtymd,mc_type,Acttime )
		values
		('" . $mid . "','" . $uid . "','" . $gs1['Name'] . "','" . $gs1['GongShi'] . "','" . $gs1['Unit'] . "','" . $gs1['dtymd'] . "','" . $gs1['mc_type'] . "',NOW())");
                $GongshiID1 = $this->_dao->getone("SELECT LAST_INSERT_ID()");
            }

            if (empty($row['GongshiID1'])) {
                $GongshiID1 = 0;
            } else {
                $gs1 = $this->_dao->getrow("select *  from `dc_formula_custom`  where ID='" . $row['GongshiID1'] . "'");
                $this->_dao->execute("insert into dc_formula_custom 
		(MID,Uid,Name,GongShi,Unit,dtymd,mc_type,Acttime )
		values
		('" . $mid . "','" . $uid . "','" . $gs1['Name'] . "','" . $gs1['GongShi'] . "','" . $gs1['Unit'] . "','" . $gs1['dtymd'] . "','" . $gs1['mc_type'] . "',NOW())");
                $GongshiID1 = $this->_dao->getone("SELECT LAST_INSERT_ID()");
            }


            if (empty($row['GongshiID2'])) {
                $GongshiID2 = 0;
            } else {
                $gs2 = $this->_dao->getrow("select *  from `dc_formula_custom`  where ID='" . $row['GongshiID2'] . "'");
                $this->_dao->execute("insert into dc_formula_custom 
		(MID,Uid,Name,GongShi,Unit,dtymd,mc_type,Acttime )
		values
		('" . $mid . "','" . $uid . "','" . $gs2['Name'] . "','" . $gs2['GongShi'] . "','" . $gs2['Unit'] . "','" . $gs2['dtymd'] . "','" . $gs2['mc_type'] . "',NOW())");
                $GongshiID2 = $this->_dao->getone("SELECT LAST_INSERT_ID()");
            }

            if (empty($row['GongshiID3'])) {
                $GongshiID3 = 0;
            } else {
                $gs3 = $this->_dao->getrow("select *  from `dc_formula_custom`  where ID='" . $row['GongshiID3'] . "'");
                $this->_dao->execute("insert into dc_formula_custom 
			(MID,Uid,Name,GongShi,Unit,dtymd,mc_type,Acttime )
			values
			('" . $mid . "','" . $uid . "','" . $gs3['Name'] . "','" . $gs3['GongShi'] . "','" . $gs3['Unit'] . "','" . $gs3['dtymd'] . "','" . $gs3['mc_type'] . "',NOW())");
                $GongshiID3 = $this->_dao->getone("SELECT LAST_INSERT_ID()");
            }

            if (empty($row['GongshiID4'])) {
                $GongshiID4 = 0;
            } else {
                $gs4 = $this->_dao->getrow("select *  from `dc_formula_custom`  where ID='" . $row['GongshiID4'] . "'");
                $this->_dao->execute("insert into dc_formula_custom 
				(MID,Uid,Name,GongShi,Unit,dtymd,mc_type,Acttime )
				values
				('" . $mid . "','" . $uid . "','" . $gs4['Name'] . "','" . $gs4['GongShi'] . "','" . $gs4['Unit'] . "','" . $gs4['dtymd'] . "','" . $gs4['mc_type'] . "',NOW())");
                $GongshiID4 = $this->_dao->getone("SELECT LAST_INSERT_ID()");
            }

            if (empty($row['DTIDJson'])) {
                $DTIDJson = '';
            } else {


                $dj = json_decode($row['DTIDJson'], true);
                foreach ($dj as $key2 => $value2) {
                    if (!empty($value['GongshiID'])) {
                        $gs = '';
                        $GongshiID = '';
                        $gs = $this->_dao->getrow("select *  from `dc_formula_custom`  where ID='" . $value2['GongshiID'] . "'");
                        $this->_dao->execute("insert into dc_formula_custom 
						(MID,Uid,Name,GongShi,Unit,dtymd,mc_type,Acttime )
						values
						('" . $mid . "','" . $uid . "','" . $gs['Name'] . "','" . $gs['GongShi'] . "','" . $gs['Unit'] . "','" . $gs['dtymd'] . "','" . $gs['mc_type'] . "',NOW())");
                        $GongshiID = $this->_dao->getone("SELECT LAST_INSERT_ID()");
                        $dj[$key2]['GongshiID'] = $GongshiID;
                    }

                }
                $dj_json[$isDataOrCatalogue[1][0]['id']] = $this->pri_JSON($dj);

            }
            $data = $this->getdata($array, $row);
            $maxsort = $this->_dao->getOne("select max(aod) from dc_search_custom where MID='$mid' and Uid ='$uid'");
            $maxsort++;//保存时，让sort增加，然后插入表格，操作量不大时，sort值一般不会重复
            $ctime = date("Y-m-d H:i:s");
            if($row)
            {
            //查出来的数据重新插入替换掉mid，uid，catalogueid等，保持独立性
            $this->_dao->execute("insert into dc_search_custom set $data,DTIDJson='" . $dj_json[$isDataOrCatalogue[1][0]['id']] . "',catalogueid='" . $parentid . "',GongshiID1='" . $GongshiID1 . "',GongshiID2='" . $GongshiID2 . "',GongshiID3='" . $GongshiID3 . "',GongshiID4='" . $GongshiID4 . "',MID='" . $mid . "',Uid ='$uid',aod='$maxsort',mc_type='" . $mc_type . "',createtime='" . $ctime . "',updatetime='" . $ctime . "',IsEnglish='" . $isEnglish . "'");
            }
           
        }

        //记录日志
        $this->_dao->WriteLog($mid, $uid, $SignCS, $action,$actionstr, $this->getIP(), $this->getSystemType(), $this->getSystemVersion(),'我的定制数据导入','','',$mc_type);

        $arr['Success'] = 1;
        $arr['Message'] = '成功';
        if ($isEnglish) $arr['Message'] = "Success";
        $json_string = $this->pri_JSON($arr);
        echo $json_string;
        exit;

    }


    private function Recycleinsertcatalogue($cj, $newparentid, $arr_cj, $old_cid)
    {
        //根据当前层的cid进行分组


        foreach ($arr_cj[$cj] as $key => $value) {
            //目录和数据的处理方式不一样
            $row = '';
            if ($value['isCatalogue'] == 1) {//目录处理，目录处理还需要往下递归
                if ($old_cid == $value['pid']) {//此层级父目录id
                    $row = $this->_dao->getrow("select *  from `dc_search_custom_catalogue_recycle`  where ID='" . $value['cid'] . "'  ");

                    $this->_dao->execute("insert into dc_search_custom_catalogue 
				(parentid,cataloguename,status,aod,createtime,updatetime,Uid,MID,mc_type,IsEnglish)
				  values
				  ('" . $newparentid . "','" . $row['cataloguename'] . "','1','',NOW(),NOW(),'" . $row['Uid'] . "','" . $row['MID'] . "','" . $row['mc_type'] . "','" . $row['isEnglish'] . "')");
                    $insertid = $this->_dao->getone("SELECT LAST_INSERT_ID()");

                    $this->_dao->execute("update dc_search_custom_catalogue set aod='" . $insertid . "' where ID='" . $insertid . "' ");
                    $this->Recycleinsertcatalogue($cj + 1, $insertid, $arr_cj, $row['ID']);
                }

            } else {//数据处理，处理完就处理完，不需要递归

                $row = $this->_dao->getrow("select *  from `dc_search_custom_recycle`  where ID='" . $value['id'] . "' ");

                $array = array(
                    'MID', 'Uid', 'dtname', 'dtnameshort', 'DTID1', 'DTID1Sub', 'Data1Type1', 'Data1Pre', 'Data1AddSub', 'Data1AddSubHundCore', 'Data1Type2', 'Data1Type3', 'Data1Type4', 'Data1Type5', 'DTID2', 'DTID2Sub', 'Data2Type', 'DTID3', 'DTID3Sub', 'Data3Type', 'DTID4', 'DTID4Sub', 'Data4Type', 'aod', 'ImageType', 'ImageTitle', 'Data1Image', 'Data2Image', 'Data3Image', 'Data4Image', 'zhou1', 'zhou2', 'zhou3', 'zhou4', 'dtname_en', 'dtnameshort_en', 'IsEnglish', 'mc_type', 'isbg', 'isjz', 'color', 'isfg', 'LineTitle1', 'LineTitle2', 'LineTitle3', 'LineTitle4', 'GongshiID1', 'GongshiID2', 'GongshiID3', 'GongshiID4', 'Data1Type', 'createtime', 'updatetime', 'DTIDJson', 'ChartExtLineStyle', 'LineTitle1_bak', 'LineTitle2_bak', 'LineTitle3_bak', 'LineTitle4_bak', 'ImageTitle_bak', 'DateFormat'
                );
                $data = $this->getdata($array, $row);

                $ctime = date("Y-m-d H:i:s");

                //查出来的数据重新插入替换掉catalogueid等，保持独立性
                if ($row['catalogueid'] == $old_cid) {
                    $this->_dao->execute("insert into dc_search_custom set $data,catalogueid='" . $newparentid . "'");

                }

            }

        }
    }


    private function insertcatalogue($cj, $newparentid, $arr_cj, $mid, $uid, $old_cid)
    {
        //根据当前层的cid进行分组


        foreach ($arr_cj[$cj] as $key => $value) {
            //目录和数据的处理方式不一样
            $row = '';
            if ($value['isCatalogue'] == 1) {//目录处理，目录处理还需要往下递归
                if ($old_cid == $value['pid']) {//此层级父目录id
                    $row = $this->_dao->getrow("select *  from `dc_search_custom_catalogue`  where ID='" . $value['cid'] . "' and mc_type='" . $value['mc_type'] . "' and MID='" . $value['mid'] . "' and Uid='" . $value['uid'] . "'  and  IsEnglish='" . $value['isEnglish'] . "' and status=1");
                    if ($row) {
                        $this->_dao->execute("insert into dc_search_custom_catalogue 
                    (parentid,cataloguename,status,aod,createtime,updatetime,Uid,MID,mc_type,IsEnglish)
                    values
                    ('" . $newparentid . "','" . $row['cataloguename'] . "','1','',NOW(),NOW(),'" . $uid . "','" . $mid . "','" . $value['mc_type'] . "','" . $value['isEnglish'] . "')");
                        $insertid = $this->_dao->getone("SELECT LAST_INSERT_ID()");

                        $this->_dao->execute("update dc_search_custom_catalogue set aod='" . $insertid . "' where ID='" . $insertid . "' ");
                        $this->insertcatalogue($cj + 1, $insertid, $arr_cj, $mid, $uid, $row['ID']);
                    }
                }

            } else {//数据处理，处理完就处理完，不需要递归

                $row = $this->_dao->getrow("select *  from `dc_search_custom`  where ID='" . $value['id'] . "' and IsEnglish='" . $value['isEnglish'] . "' and MID = '" . $value['mid'] . "' and Uid = '" . $value['uid'] . "' and mc_type = '" . $value['mc_type'] . "'");
                if (empty($row)) {
                    continue;
                }
                $array = array(
                    "dtname",
                    "dtname_en",
                    "ImageTitle",
                    "DTID1",
                    "DTID1Sub",
                    "Data1Type",
                    "Data1Type1",
                    "Data1Pre",
                    "Data1AddSub",
                    "Data1AddSubHundCore",
                    "Data1Type2",
                    "Data1Type3",
                    "Data1Type4",
                    "Data1Type5",
                    "DTID2",
                    "DTID2Sub",
                    "Data2Type",
                    "DTID3",
                    "DTID3Sub",
                    "Data3Type",
                    "DTID4",
                    "DTID4Sub",
                    "Data4Type",
                    "ImageType",
                    "Data1Image",
                    "Data2Image",
                    "Data3Image",
                    "Data4Image",
                    "zhou1",
                    "zhou2",
                    "zhou3",
                    "zhou4",
                    "isjz",
                    "isbg",
                    "isfg",
                    "color",
                    "LineTitle1",
                    "LineTitle2",
                    "LineTitle3",
                    "LineTitle4",
                    //  "GongshiID1",
                    // "GongshiID2",
                    // "GongshiID3",
                    // "GongshiID4",
                    // "DTIDJson",
                    "ChartExtLineStyle",
                    "dtnameshort",
                    "LineTitle1_bak",
                    "LineTitle2_bak",
                    "LineTitle3_bak",
                    "LineTitle4_bak",
                    "ImageTitle_bak",
                    "DateFormat",

                );
                // "GongshiID1","GongshiID2","GongshiID3","GongshiID4",DTIDJson中的公式id需要处理
                if (empty($row['GongshiID1'])) {
                    $GongshiID1 = 0;
                } else {
                    $gs1 = $this->_dao->getrow("select *  from `dc_formula_custom`  where ID='" . $row['GongshiID1'] . "'");
                    $this->_dao->execute("insert into dc_formula_custom 
		(MID,Uid,Name,GongShi,Unit,dtymd,mc_type,Acttime )
		  values
		  ('" . $mid . "','" . $uid . "','" . $gs1['Name'] . "','" . $gs1['GongShi'] . "','" . $gs1['Unit'] . "','" . $gs1['dtymd'] . "','" . $gs1['mc_type'] . "',NOW())");
                    $GongshiID1 = $this->_dao->getone("SELECT LAST_INSERT_ID()");
                }

                if (empty($row['GongshiID1'])) {
                    $GongshiID1 = 0;
                } else {
                    $gs1 = $this->_dao->getrow("select *  from `dc_formula_custom`  where ID='" . $row['GongshiID1'] . "'");
                    $this->_dao->execute("insert into dc_formula_custom 
		(MID,Uid,Name,GongShi,Unit,dtymd,mc_type,Acttime )
		values
		('" . $mid . "','" . $uid . "','" . $gs1['Name'] . "','" . $gs1['GongShi'] . "','" . $gs1['Unit'] . "','" . $gs1['dtymd'] . "','" . $gs1['mc_type'] . "',NOW())");
                    $GongshiID1 = $this->_dao->getone("SELECT LAST_INSERT_ID()");
                }


                if (empty($row['GongshiID2'])) {
                    $GongshiID2 = 0;
                } else {
                    $gs2 = $this->_dao->getrow("select *  from `dc_formula_custom`  where ID='" . $row['GongshiID2'] . "'");
                    $this->_dao->execute("insert into dc_formula_custom 
		(MID,Uid,Name,GongShi,Unit,dtymd,mc_type,Acttime )
		values
		('" . $mid . "','" . $uid . "','" . $gs2['Name'] . "','" . $gs2['GongShi'] . "','" . $gs2['Unit'] . "','" . $gs2['dtymd'] . "','" . $gs2['mc_type'] . "',NOW())");
                    $GongshiID2 = $this->_dao->getone("SELECT LAST_INSERT_ID()");
                }

                if (empty($row['GongshiID3'])) {
                    $GongshiID3 = 0;
                } else {
                    $gs3 = $this->_dao->getrow("select *  from `dc_formula_custom`  where ID='" . $row['GongshiID3'] . "'");
                    $this->_dao->execute("insert into dc_formula_custom 
			(MID,Uid,Name,GongShi,Unit,dtymd,mc_type,Acttime )
			values
			('" . $mid . "','" . $uid . "','" . $gs3['Name'] . "','" . $gs3['GongShi'] . "','" . $gs3['Unit'] . "','" . $gs3['dtymd'] . "','" . $gs3['mc_type'] . "',NOW())");
                    $GongshiID3 = $this->_dao->getone("SELECT LAST_INSERT_ID()");
                }

                if (empty($row['GongshiID4'])) {
                    $GongshiID4 = 0;
                } else {
                    $gs4 = $this->_dao->getrow("select *  from `dc_formula_custom`  where ID='" . $row['GongshiID4'] . "'");
                    $this->_dao->execute("insert into dc_formula_custom 
				(MID,Uid,Name,GongShi,Unit,dtymd,mc_type,Acttime )
				values
				('" . $mid . "','" . $uid . "','" . $gs4['Name'] . "','" . $gs4['GongShi'] . "','" . $gs4['Unit'] . "','" . $gs4['dtymd'] . "','" . $gs4['mc_type'] . "',NOW())");
                    $GongshiID4 = $this->_dao->getone("SELECT LAST_INSERT_ID()");
                }

                if (empty($row['DTIDJson'])) {
                    $DTIDJson = '';
                } else {


                    $dj = json_decode($row['DTIDJson'], true);
                    foreach ($dj as $key2 => $value2) {
                        if (!empty($value2['GongshiID'])) {
                            $gs = '';
                            $GongshiID = '';
                            $gs = $this->_dao->getrow("select *  from `dc_formula_custom`  where ID='" . $value2['GongshiID'] . "'");
                            $this->_dao->execute("insert into dc_formula_custom 
						(MID,Uid,Name,GongShi,Unit,dtymd,mc_type,Acttime )
						values
						('" . $mid . "','" . $uid . "','" . $gs['Name'] . "','" . $gs['GongShi'] . "','" . $gs['Unit'] . "','" . $gs['dtymd'] . "','" . $gs['mc_type'] . "',NOW())");
                            $GongshiID = $this->_dao->getone("SELECT LAST_INSERT_ID()");
                            $dj[$key2]['GongshiID'] = $GongshiID;
                        }

                    }
                    $dj_json[$value['id']] = $this->pri_JSON($dj);

                }
                $data = $this->getdata($array, $row);
                $maxsort = $this->_dao->getOne("select max(aod) from dc_search_custom where MID='$mid' and Uid ='$uid'");
                $maxsort++;//保存时，让sort增加，然后插入表格，操作量不大时，sort值一般不会重复
                $ctime = date("Y-m-d H:i:s");
                //查出来的数据重新插入替换掉mid，uid，catalogueid等，保持独立性
                if ($row['catalogueid'] == $old_cid) {
                    $this->_dao->execute("insert into dc_search_custom set $data,DTIDJson='" . $dj_json[$value['id']] . "',catalogueid='" . $newparentid . "',GongshiID1='" . $GongshiID1 . "',GongshiID2='" . $GongshiID2 . "',GongshiID3='" . $GongshiID3 . "',GongshiID4='" . $GongshiID4 . "',MID='" . $mid . "',Uid ='$uid',aod='$maxsort',mc_type='" . $value['mc_type'] . "',createtime='" . $ctime . "',updatetime='" . $ctime . "',IsEnglish='" . $value['isEnglish'] . "'");

                }

            }

        }
    }

    private function parentidsearch_mulu($parentid, $isEnglish, $mid, $uid, $mc_type, $cj)
    {

        //从最初的目录开始向下搜索关键字绑定目录及数据


        $arr = array();
        $parentid_querykeyarr = $this->_dao->query("select ID  from `dc_search_custom_catalogue`  where parentid ='" . $parentid . "' and mc_type='" . $mc_type . "' and MID='" . $mid . "' and Uid='" . $uid . "'  and  IsEnglish='" . $isEnglish . "' and status=1  order by ".($mc_type!=0?"binary(cataloguename) asc":"aod asc,binary(cataloguename) asc"));
        $i = 0;
        foreach ($parentid_querykeyarr as $k => $v) {

            $ids_arr[] = $v['ID'];
            $arr[$i]['cid'] = $v['ID'];
            $arr[$i]['pid'] = $parentid;
            $arr[$i]['mid'] = $mid;
            $arr[$i]['uid'] = $uid;
            $arr[$i]['mc_type'] = $mc_type;
            $arr[$i]['isEnglish'] = $isEnglish;
            $arr[$i]['isCatalogue'] = '1';
            $newcj = $cj + 1;
            $arr[$i]['cj'] = (string)$newcj;
            $i++;
        }


        $selectsql = "select ID,catalogueid from dc_search_custom  where IsEnglish='" . $isEnglish . "'   and MID = '" . $mid . "' and Uid = '$uid' and mc_type = '$mc_type' and catalogueid ='" . $parentid . "' order by ".($mc_type!=0?"binary(dtname) asc,aod desc,ID desc":"aod asc,binary(dtname) asc");

        $sear = $this->_dao->query($selectsql);

        foreach ($sear as $k => $v) {


            $arr[$i]['id'] = $v['ID'];
            $arr[$i]['cid'] = $v['catalogueid'];
            $arr[$i]['mid'] = $mid;
            $arr[$i]['uid'] = $uid;
            $arr[$i]['mc_type'] = $mc_type;
            $arr[$i]['isEnglish'] = $isEnglish;
            $arr[$i]['isCatalogue'] = '0';
            $newcj = $cj + 1;
            $arr[$i]['cj'] = (string)$newcj;
            $i++;
        }


        foreach ($ids_arr as $k => $v) {
            $arr_all = '';
            $arr_all = $this->parentidsearch_mulu($v, $isEnglish, $mid, $uid, $mc_type, $cj + 1);
            foreach ($arr_all as $k2 => $v2) {
                $arr[$i] = $v2;
                $i++;
            }

        }

        return $arr;

    }

    public function addselect($params)
    {


        $selectsql = "select ID,DTIDJson from dc_search_custom  where DTIDJson!=''";

        $sear = $this->_dao->query($selectsql);
        //echo '<pre>';print_R($sear);
        $arr = '';
        foreach ($sear as $k => $v) {
            $arr[$v['ID']] = $v['DTIDJson'];
        }

        foreach ($arr as $k1 => $v1) {
            $dj = json_decode($v1, true);
            foreach ($dj as $k2 => $v2) {
                if (!isset($v2['Selected'])) {
                    $dj[$k2]['Selected'] = "1";
                }

            }

            $arr[$k1] = json_encode($dj);
        }

        foreach ($arr as $k3 => $v3) {
            //$this->_dao->execute("update dc_search_custom set DTIDJson='".$v3."' where ID='".$k3."'");
        }
        echo '完成';

    }


    public function GetXBZHMothHB($params)
    {
        $arr_Type = array(
            '1' => array("lwg_moth_yhb" => "mhb", "lwg_year_hb" => "yhb"),
            '2' => array("pl_moth_yhb" => "mhb", "pl_year_hb" => "yhb"),
            '3' => array("gx_moth_yhb" => "mhb", "gx_year_hb" => "yhb")
        );


        $date = $params['date'] == "" ? date("Y-m-d") : $params['date'];

        $data = array();
        foreach ($arr_Type as $Type => $varietyname) {
            $SJCGIndexs = $this->getSJCGIndexs($Type, '西部综合', $date);
            foreach ($varietyname as $key => $tmp) {
                $data[$key] = $SJCGIndexs[$tmp];
            }
        }

        $arr['Success'] = 1;
        $arr['Message'] = base64_encode('成功');
        $arr['Results'] = array("ResultDatas" => $data);
        $json_string = $this->pri_JSON($arr);
        echo $json_string;
        exit;

    }

    function getSJCGIndexs($Type, $city, $date)
    {


        /*月环比*/
        $month_date = $this->monday($date);
        $m_index_b = $this->CalcShpi($month_date['bs'], $month_date['be'], $city, $Type);
        $m_index_s = $this->CalcShpi($month_date['ss'], $month_date['se'], $city, $Type);
        $mhb = $this->calcShpis($m_index_b, $m_index_s);

        /*年环比*/
        $year_date = $this->yearday($date);
        $y_index_b = $this->CalcShpi($year_date['bs'], $year_date['be'], $city, $Type);
        $y_index_s = $this->CalcShpi($year_date['ss'], $year_date['se'], $city, $Type);
        $yhb = $this->calcShpis($y_index_b, $y_index_s);

        $arr = array(
            "mhb" => $mhb,
            "yhb" => $yhb
        );
        return $arr;

    }

    /*月环比*/
    public function monday($date)
    {
        $mnum = date("n", strtotime($date));
        $y = date("Y", strtotime($date));
        if ($mnum == 1) {
            $weekd["bs"] = $y . "-01-01";
            $weekd["be"] = $y . "-01-" . date("d", strtotime($date));
            $weekd["ss"] = ($y - 1) . "-12-01";
            $weekd["se"] = ($y - 1) . "-12-31";
        } else {
            $weekd["bs"] = $y . "-" . date("m", strtotime($date)) . "-01";
            $weekd["be"] = $y . "-" . date("m", strtotime($date)) . "-" . date("d", strtotime($date));
            $mnum1 = $mnum - 1;
            if ($mnum1 < 10) {
                $mnum1 = "0" . $mnum1;
            }
            $weekd["ss"] = $y . "-" . $mnum1 . "-01";
            $weekd["se"] = $y . "-" . $mnum1 . "-31";
        }
        return $weekd;
    }

    /*年环比*/
    public function yearday($date)
    {
        $y = date("Y", strtotime($date));
        $weekd["bs"] = $y . "-01-01";
        $weekd["be"] = $y . "-12-" . date("d", strtotime($date));
        $weekd["ss"] = ($y - 1) . "-01-01";
        $weekd["se"] = ($y - 1) . "-12-31";
        //echo $weekd["se"]."##".$weekd["ss"];
        return $weekd;
    }

    /**
     * $start 指数开始日期
     * $end 指数结束日期
     * city 参数是区域名
     *      值为'陕晋川甘'时，计算陕晋川甘价格指数
     *      其他值时计算区域指数
     * $type 品种类型. 1.螺纹钢， 2.盘螺, 3.高线
     **/
    public function CalcShpi($start, $end = 0, $city, $type, $debug = 0, $status = 1)
    {
        $s_date = date("Y-m-d", strtotime($start));
        $e_date = date("Y-m-d", strtotime($end));

        $where_index_status = "";
        if ($status == 1) $where_index_status = " and Status=1";

        $sql = "select AVG(`index`) from SJCGIndex where `Type`='$type' and `CityName`='$city' and `Date`>='$s_date' and `Date`<='$e_date' and `DType`=0 $where_index_status";
        $index = $this->stedao->getOne($sql);

        return round($index, 2);
    }

    function calcShpis($bavpi, $savpi)
    {
        $str = "-";
        if ($bavpi && $savpi) {
            //$str = ($bavpi/$savpi)-100;
            //$str = round((($bavpi-$savpi)/$savpi)*100,2);
            $str = round($bavpi - $savpi, 2);
        }
        return $str;
    }

    public function utf8ToGb2312($str)
    {
        if (mb_detect_encoding($str, "UTF-8, ISO-8859-1, GBK") == "UTF-8") {//判断是否不是UTF-8编码，如果不是UTF-8编码，则转换为UTF-8编码
            return iconv("utf-8", "GBK", $str);
        } else {
            return $str;
        }
    }
    public function Gb2312Toutf8($str)
    {
        if (mb_detect_encoding($str, "UTF-8, ISO-8859-1, GBK") == "GBK") {//判断是否不是UTF-8编码，如果不是UTF-8编码，则转换为UTF-8编码
            return iconv( "GBK","utf-8", $str);
        } else {
            return $str;
        }
    }


    /**
     * @param $params
     * user:shizg
     * time:2022/8/10 15:08
     * TODO 获取接口版本控制标识
     */
    public function getVersionControlFlag($params)
    {
        $arr['Success'] = 1;
        $arr['Message'] = base64_encode($this->utf8ToGb2312( '成功' ) );
        $arr['Results'] = array("Version" => DC_INTERFACE_VERSION_CONTROL_FLAG);
        if( isset($params['istest']) && $params['istest'] == 1 ) {
            $arr['Results'] = array("Version" => 1);
        }
        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    public function getMenu($params)
    {
         $checkauth = $params['checkauth'] ?: "1";
        //验证账户是否过期
        if($checkauth==1)
        {
            $getSessionTempInfo=$this->check_login($params);
        }
        else {
           $getSessionTempInfo['Mid']=1;
        }
        
       
        $mc_type = $params['mc_type'] ?: "0";
        $pId = $params['pid'] ?: 0;
        $pLevel = $params['level'] ?: 0;
        $sname_en = "";
        $arr = array(
            'Success' => '0',
            'Message' => '失败',
            'Results' => null,
        );

        if ($pLevel == 0) {
            $scode_sql = "select ID,scode,sname as label,true as isParent,true as hasChildren,sod from dc_code_class where ID='" . $pId . "'";
            $info = $this->_dao->getrow($scode_sql);
            $scode = $info['scode'];
            $firstnode[0] = $info;
        } else {
            $scode_sql = "select scode from dc_code_class where ID='" . $pId . "'";
            $scode = $this->_dao->getone($scode_sql);
        }


        //目录的id都是dc_code_class表中的
        $sql1 = "select ID ,sname" . $sname_en . " as label,true as isParent,sod from dc_code_class where mcode='" . $scode . "' and status=1  and  mc_type='" . $mc_type . "' order by sod";
        $arr1 = $this->_dao->query($sql1);
        $temparr1=array();
        foreach($arr1 as $v)
        {
            if($v['ID']!=569)//屏蔽产能汇总查询
            {
                $temparr1[]=$v;
            }
            
        }
        $arr1=$temparr1;
        //数据类型dc_code_datatype
        //目前只有五层，scode($pLevel+2)不能等于scode6，以后加层级的时候调节下
        if ($pLevel <= 3) {
            $sjsql = " and scode" . ($pLevel + 1) . "='" . $scode . "' and scode" . ($pLevel + 2) . "='' order by dtod asc";
        } else {
            $sjsql = " and scode" . ($pLevel + 1) . "='" . $scode . "' order by dtod asc";
        }

        //根据品种显示目录数据类型
        if ($_REQUEST['isok'] == 1) {
            $sjsql = $sjsql . " and pinzhong=0 ";
        } else if ($_REQUEST['isok'] == 2) {
            $sjsql = $sjsql . " and pinzhong!=0";
        }

        $pz = $this->_dao->getOne("select pinzhong from app_license where MID = '".$getSessionTempInfo['Mid']."' and Status=1 and mc_type='" . $mc_type . "' limit 1 ", 0);
        if($checkauth!=1||$pz=='8192')
			$pz='allpz';
        else
        {
            foreach( split2($pz) as $k=>$v){
                if($k==0)
                    $pz=$v;
                else
                    $pz=$pz.'|'.$v;
            }						
        }
        $pinzhongsql='';
        if($pz!='allpz')
        {
            $pinzhongsql .=" and pinzhong&(" . $pz . ")>0";
        }

        $sql2 = "select ID,dtname" . $sname_en . "  as label,false as isParent,dtod as sod,Data1Type as DataImage,UnitTypeName as unit,dtymd,data_releasetime from dc_code_datatype  where  mc_type='" . $mc_type . "' and TargetFlagVar=0  and status=1 $pinzhongsql $sjsql";
        $arr2 = $this->_dao->query($sql2);
        $mergearr = array_merge($arr1, $arr2);

        $flag = array();
        foreach ($mergearr as &$v) {
            $v['label'] = html_entity_decode($v['label']);
            $flag[] = $v['sod'];
        }
        array_multisort($flag, SORT_ASC, $mergearr);

        // foreach ($arr as $ak=>$item)
        // {

        // }
        if ($pLevel == 0) {
            $firstnode[0]['children'] = $mergearr;
        } else {
            $firstnode = $mergearr;
        }

        if (!empty($firstnode)) {
            $arr['Success'] = 1;
            $arr['Message'] = "获取成功";
            // if ($isEnglish) $arr['Message'] = base64_encode("Success");
            $arr['Results'] = $firstnode;
        }
        $json_string = $this->pri_JSON($arr);
        echo $json_string;
    }

    /**
     * 根据GUID判断账户是否过期
     * Created by zfy.
     * Date:2024/9/29 16:12
     * @param $params
     * @return void
     */
    public function check_login($params)
    {
        $SessionTempInfo = $this->_dao->getSessionTempInfo($params['GUID']);
        if (isset($SessionTempInfo)) {
            if ($SessionTempInfo['ExpiredDate'] < date('Y-m-d')){
                $arr = array(
                    'Success' => '0',
                    'Message' => '您的账户已过期！',
                    'Results' => 'null',
                );
                $json_string = $this->pri_JSON($arr);
                echo $json_string;exit();
            }
        }
        return $SessionTempInfo;
    }
    function getDataById($params)
    {
        $UnitSelect=array(
            "1"=>"",
            "4"=>"千",
            "5"=>"万",
            "7"=>"百万",
            "8"=>"千万",
            "9"=>"亿",
            "10"=> "十亿",
            "11"=> "百亿",
            "13"=> "万亿"
        );
        $WeightUnitSelect=array(
            array("1","千克"),
            array("4","吨"),
            array("7","千吨")
        );
        $Results=array();
        $mc_type=$params['mc_type']?$params['mc_type']:"0";
        $DTIDJson=$params['DTIDJson']?$params['DTIDJson']:[];
        if(empty($DTIDJson))
        {
            $id=$params['id']?$params['id']:"91601";
            $sql="select  dc_code_datatype.*,ndata1,npredata,naddsubdata,naddsubhundcore,ndata2,ndata3,ndata4,ndata5,ndata6,ndata7,ndata8,ndata9,ndata10,ndata11,ndata12,ndata13,ndata14,ndata15,ndata16,ndata17,ndata18,ndata19,ndata20,data_source from dc_code_datatype inner join dc_code_datatype_db on dc_code_datatype_db.DID = dc_code_datatype.ID and dc_code_datatype.mc_type='$mc_type' and dc_code_datatype_db.mc_type='$mc_type' where dc_code_datatype.ID = '" . $id . "'";
            $info=$this->_dao->getRow($sql);
    
            $Results['ID']=$info['ID'];
            $Results['dtname']=$info['dtname'];
            $Results['dtymd']=$info['dtymd'];
            for($i=1;$i<=20;$i++)
            {
                if($info['ndata'.$i])
                {
                    if($i==1)
                    {
                        $Results['zhibiao'][]=array($i,$info['ndata'.$i]);
                        if($info['npredata'])
                        {
                            $Results['zhibiao'][]=array(2,$info['npredata']);
                        }
                        if($info['naddsubdata'])
                        {
                            $Results['zhibiao'][]=array(3,$info['naddsubdata']);
                        }
                        if($info['naddsubhundcore'])
                        {
                            $Results['zhibiao'][]=array(4,$info['naddsubhundcore']);
                        }
                    }
                    else
                    {
                        $Results['zhibiao'][]=array($i+10,$info['ndata'.$i]);
                    }
                    
                }
            }
            if(empty($Results['zhibiao']))
            {
                $Results['zhibiao'][]=array(1,"");
            }
            $Results['指标']=$Results['zhibiao'][0][0];
            $Results['DataType']=$Results['zhibiao'][0][0];
            //单位名
            //$Results['unitstrings'] = $info["UnitTypeName"].','.$info["UnitTypeName2"].','.$info["UnitTypeName3"].','.$info["UnitTypeName4"].','.$info["UnitTypeName5"].','.$info["UnitTypeName6"].','.$info["UnitTypeName7"].','.$info["UnitTypeName8"].','.$info["UnitTypeName9"].','.$info["UnitTypeName10"].','.$info["UnitTypeName11"].','.$info["UnitTypeName12"].','.$info["UnitTypeName13"].','.$info["UnitTypeName14"].','.$info["UnitTypeName15"].','.$info["UnitTypeName16"].','.$info["UnitTypeName17"].','.$info["UnitTypeName18"].','.$info["UnitTypeName19"].','.$info["UnitTypeName20"];
            //单位量级
            //$Results['unitconvers'] = $info["Data1UnitConv"].','.$info["Data2UnitConv"].','.$info["Data3UnitConv"].','.$info["Data4UnitConv"].','.$info["Data5UnitConv"].','.$info["Data6UnitConv"].','.$info["Data7UnitConv"].','.$info["Data8UnitConv"].','.$info["Data9UnitConv"].','.$info["Data10UnitConv"].','.$info["Data11UnitConv"].','.$info["Data12UnitConv"].','.$info["Data13UnitConv"].','.$info["Data14UnitConv"].','.$info["Data15UnitConv"].','.$info["Data16UnitConv"].','.$info["Data17UnitConv"].','.$info["Data18UnitConv"].','.$info["Data19UnitConv"].','.$info["Data20UnitConv"];
            
            $Results['unitstrings'] = $info["UnitTypeName"].','.$info["npredata_UnitTypeName"].','.$info["naddsubdata_UnitTypeName"].','.$info["daddsubhundcore_UnitTypeName"].','.$info["UnitTypeName2"].','.$info["UnitTypeName3"].','.$info["UnitTypeName4"].','.$info["UnitTypeName5"].','.$info["UnitTypeName6"].','.$info["UnitTypeName7"].','.$info["UnitTypeName8"].','.$info["UnitTypeName9"].','.$info["UnitTypeName10"].','.$info["UnitTypeName11"].','.$info["UnitTypeName12"].','.$info["UnitTypeName13"].','.$info["UnitTypeName14"].','.$info["UnitTypeName15"].','.$info["UnitTypeName16"].','.$info["UnitTypeName17"].','.$info["UnitTypeName18"].','.$info["UnitTypeName19"].','.$info["UnitTypeName20"];
            //单位量级
            $Results['unitconvers'] = $info["Data1UnitConv"].','.$info["npredata_UnitConv"].','.$info["naddsubdata_UnitConv"].','.$info["daddsubhundcore_UnitConv"].','.$info["Data2UnitConv"].','.$info["Data3UnitConv"].','.$info["Data4UnitConv"].','.$info["Data5UnitConv"].','.$info["Data6UnitConv"].','.$info["Data7UnitConv"].','.$info["Data8UnitConv"].','.$info["Data9UnitConv"].','.$info["Data10UnitConv"].','.$info["Data11UnitConv"].','.$info["Data12UnitConv"].','.$info["Data13UnitConv"].','.$info["Data14UnitConv"].','.$info["Data15UnitConv"].','.$info["Data16UnitConv"].','.$info["Data17UnitConv"].','.$info["Data18UnitConv"].','.$info["Data19UnitConv"].','.$info["Data20UnitConv"];
            

            $Results['单位']=$info["UnitTypeName"];
            $Results['unitconver']=$info["Data1UnitConv"];
    
    
            if($Results['unitconver']!="0"&&$info["UnitTypeName"]!="")
            {
                if (($Results['unitconver'] == "1" && $Results['单位'] == "千克") || ($Results['unitconver'] == "4" && $Results['单位'] == "吨"))
                {
                    $dic=$WeightUnitSelect;
                    $Results['danwei']=$dic;
                }
                else
                {
                    $dic=$UnitSelect;
                    $oldunit=$info["UnitTypeName"];
                    if($Results['unitconver'] == "1")
                    {
                        $baseunit = $oldunit;
                    }
                    else if(in_array($Results['unitconver'],array(2,3,4,5,9)))
                    {
                        $baseunit = mb_strlen($oldunit) == 1?$oldunit:mb_substr($oldunit,1);
                    }
                    else if(in_array($Results['unitconver'],array(6,7,8,10,11,12,13)))
                    {
                        if (mb_strlen($oldunit) == 2)
                        $baseunit = mb_substr($oldunit,1);
                        else if (mb_strlen($oldunit) < 2)
                            $baseunit = $oldunit;
                        else
                            $baseunit = mb_substr($oldunit,2);
                    }
                    foreach($dic as $k=>$v)
                    {
                        $Results['danwei'][]=array($k,$v.$baseunit);
                    }
                }
    
            }
            else
            {
                 $Results['danwei'][]=$Results['unitconver'];
            }
            
            //图类型
            $Results['DataTypes'] = $info["Data1Type"].','.$info["Data2Type"].','.$info["Data3Type"].','.$info["Data4Type"].','.$info["Data5Type"].','.$info["Data6Type"].','.$info["Data7Type"].','.$info["Data8Type"].','.$info["Data9Type"].','.$info["Data10Type"].','.$info["Data11Type"].','.$info["Data12Type"].','.$info["Data13Type"].','.$info["Data14Type"].','.$info["Data15Type"].','.$info["Data16Type"].','.$info["Data17Type"].','.$info["Data18Type"].','.$info["Data19Type"].','.$info["Data20Type"];
            $Results['DataImage']=$info["Data1Type"]!='0'?$info["Data1Type"]:2;
            $Results['data_source']=  $info['data_source']?$info['data_source']:"钢之家";
            $Results['iscustomerdb']=  "0";
            $Results['图例修改']=  $info['dtname'];
            $Results['linestylelight']=  "";
            $Results['linestylewidth']=  "";
            $Results['linestyletype']=  "";

    
    
    
            $arr['Success'] = 1;
            $arr['Message'] = "获取成功";
            $arr['Results'] = $Results;
        }
        else
        {
            $DTIDJson = html_entity_decode($DTIDJson);
            $DTIDJson_arr = json_decode($DTIDJson, true);
            //echo $DTIDJson;
            foreach($DTIDJson_arr as $k=>$v)
            {
                if($v['GongshiID']!="0")
                {
                    $Result=array();
                    $Result['选取']=$v['Selected'];;
                    $Result['ID']=$v['DTID'];
                    $Result['GongshiID']=$v['GongshiID'];
                    $Result['dtname']=$v['GSName'];
                    $Result['zhibiao'][]=array(1,"(无)");
                    $Result['指标']=$v['DataType'];
                    $Result['DataType']=$v['DataType'];
                    $Result['unitstrings'] = $v["unitstring"]!=-1?$v["unitstring"]:"";
                    $Result['单位']=$v["unitstring"]!=-1?$v["unitstring"]:"";
                    $Result['unitconver']=$v["unitconver"]!=-1?$v["unitconver"]:"0";
                    $Result['danwei'][]=$v["unitstring"];
                    $Result['图例修改']=$v['LineTitle'];
                    $Result['DataTypes'] = $v["DataImage"];
                    $Result['DataImage']=$v["DataImage"];
                    $Result['右轴']= $v['zhou'];
                    $Result['data_source']= "钢之家";
                    $Result['iscustomerdb']=  "0";
                    $Result['linestylelight']= $v["linestylelight"]!=""&&$v["linestylelight"]!="undefined"?$v["linestylelight"]:"";
                    $Result['linestylewidth']= $v["linestylewidth"]!=""&&$v["linestylewidth"]!="undefined"?$v["linestylewidth"]:"";
                    $Result['linestyletype']=  $v["linestyletype"]!=""&&$v["linestyletype"]!="undefined"?$v["linestyletype"]:"";

                }
                else
                {
                    //客户数据判断没有做iscustomerdb
                    if($v['iscustomerdb']==1)
                    {
                        $Result=array();
                        $mysql = "select cataloguename,D1dtymd,D1UnitType from dc_customer_data_catalogue where  id='" . $v['DTID'] . "' limit 1";
                        $dc_customer_data_catalogue = $this->_dao->getRow($mysql);
                        $khname = iconv("GBK", "UTF-8",base64_decode($dc_customer_data_catalogue['cataloguename']));
                        $Result['选取']=$v['Selected'];
                        $Result['ID']=$v['DTID'];
                        $Result['dtname']=$khname ;
                        $Result['dtymd']=$v['dtymd'];

                        $Result['zhibiao'][]=array(1,"(无)");
                        
                        $Result['指标']=$v['DataType'];
                        $Result['DataType']=$v['DataType'];
                        //单位名
                        $Result['unitstrings'] = $v["unitstring"];
                        //单位量级
                        $Result['unitconvers'] = $v["unitconver"];

                        $Result['单位']=$v["unitstring"];
                        $Result['unitconver']=$v["unitconver"];
                        
                        //if($dc_customer_data_catalogue['D1UnitType']!="0"&&$Result['unitconver']!="0"&&$Result['unitconver']!="")
                        if($dc_customer_data_catalogue['D1UnitType']!="0"&&$Result['unitconver']!="0"&&$Result['unitconver']!=""&&$Result['单位']!="")
                        {
                            if (($Result['unitconver'] == "1" && $Result['单位'] == "千克") || ($Result['unitconver'] == "4" && $Result['单位'] == "吨"))
                            {
                                $dic=$WeightUnitSelect;
                                $Result['danwei']=$dic;
                            }
                            else
                            {
                                $dic=$UnitSelect;
                                $oldunit=$Result['单位'];
                                if($Result['unitconver'] == "1")
                                {
                                    $baseunit = $oldunit;
                                }
                                else if(in_array($Result['unitconver'],array(2,3,4,5,9)))
                                {
                                    $baseunit = mb_strlen($oldunit) == 1?$oldunit:mb_substr($oldunit,1);
                                }
                                else if(in_array($Result['unitconver'],array(6,7,8,10,11,12,13)))
                                {
                                    if (mb_strlen($oldunit) == 2)
                                    $baseunit = mb_substr($oldunit,1);
                                    else if (mb_strlen($oldunit) < 2)
                                        $baseunit = $oldunit;
                                    else
                                        $baseunit = mb_substr($oldunit,2);
                                }
                                foreach($dic as $k1=>$v1)
                                {
                                    $Result['danwei'][]=array($k1,$v1.$baseunit);
                                }
                                 if(!isset($dic[$v["unitconver"]]))
                                {
                                    $Result['unitconver']="1";
                                }
                            }
                           
                
                        }
                        else
                        {
                             $Result['danwei'][]=$Result['unitconver'];
                        }

                        $Result['图例修改']=$v['LineTitle'];
                        //图类型
                        $Result['DataTypes'] = "1";
                        $Result['DataImage']=$v["DataImage"];
                        $Result['右轴']= $v['zhou'];
                        $Result['data_source']=  "我的数据";
                        $Result['iscustomerdb']=  "1";
                        $Result['linestylelight']= $v["linestylelight"]!=""?$v["linestylelight"]:"";
                        $Result['linestylewidth']= $v["linestylewidth"]!=""?$v["linestylewidth"]:"";
                        $Result['linestyletype']=  $v["linestyletype"]!=""?$v["linestyletype"]:"";
                        
                    }
                    else
                    {
                        $sql="select  dc_code_datatype.*,ndata1,npredata,naddsubdata,naddsubhundcore,ndata2,ndata3,ndata4,ndata5,ndata6,ndata7,ndata8,ndata9,ndata10,ndata11,ndata12,ndata13,ndata14,ndata15,ndata16,ndata17,ndata18,ndata19,ndata20,data_source from dc_code_datatype inner join dc_code_datatype_db on dc_code_datatype_db.DID = dc_code_datatype.ID and dc_code_datatype.mc_type='$mc_type' and dc_code_datatype_db.mc_type='$mc_type' where dc_code_datatype.ID = '" . $v['DTID'] . "'";
                        $info=$this->_dao->getRow($sql);
                        if(empty($info))
                        {
                            continue;
                        }
                        $Result=array();
                        
                        $Result['选取']=$v['Selected'];
                        $Result['ID']=$info['ID'];
                        $Result['dtname']=$info['dtname'];
                        $Result['dtymd']=$info['dtymd'];
                        for($i=1;$i<=20;$i++)
                        {
                            if($info['ndata'.$i])
                            {
                                if($i==1)
                                {
                                    $Result['zhibiao'][]=array($i,$info['ndata'.$i]);
                                    if($info['npredata'])
                                    {
                                        $Result['zhibiao'][]=array(2,$info['npredata']);
                                    }
                                    if($info['naddsubdata'])
                                    {
                                        $Result['zhibiao'][]=array(3,$info['naddsubdata']);
                                    }
                                    if($info['naddsubhundcore'])
                                    {
                                        $Result['zhibiao'][]=array(4,$info['naddsubhundcore']);
                                    }
    
                                }
                                else
                                {
                                    $Result['zhibiao'][]=array($i+10,$info['ndata'.$i]);
                                }
                                
                            }
                        }
                        if(empty($Result['zhibiao']))
                        {
                            $Result['zhibiao'][]=array(1,"");
                        }
                        $Result['指标']=$v['DataType'];
                        $Result['DataType']=$v['DataType'];
                        //单位名
                        $Result['unitstrings'] = $info["UnitTypeName"].','.$info["npredata_UnitTypeName"].','.$info["naddsubdata_UnitTypeName"].','.$info["daddsubhundcore_UnitTypeName"].','.$info["UnitTypeName2"].','.$info["UnitTypeName3"].','.$info["UnitTypeName4"].','.$info["UnitTypeName5"].','.$info["UnitTypeName6"].','.$info["UnitTypeName7"].','.$info["UnitTypeName8"].','.$info["UnitTypeName9"].','.$info["UnitTypeName10"].','.$info["UnitTypeName11"].','.$info["UnitTypeName12"].','.$info["UnitTypeName13"].','.$info["UnitTypeName14"].','.$info["UnitTypeName15"].','.$info["UnitTypeName16"].','.$info["UnitTypeName17"].','.$info["UnitTypeName18"].','.$info["UnitTypeName19"].','.$info["UnitTypeName20"];
                        //单位量级
                        $Result['unitconvers'] = $info["Data1UnitConv"].','.$info["npredata_UnitConv"].','.$info["naddsubdata_UnitConv"].','.$info["daddsubhundcore_UnitConv"].','.$info["Data2UnitConv"].','.$info["Data3UnitConv"].','.$info["Data4UnitConv"].','.$info["Data5UnitConv"].','.$info["Data6UnitConv"].','.$info["Data7UnitConv"].','.$info["Data8UnitConv"].','.$info["Data9UnitConv"].','.$info["Data10UnitConv"].','.$info["Data11UnitConv"].','.$info["Data12UnitConv"].','.$info["Data13UnitConv"].','.$info["Data14UnitConv"].','.$info["Data15UnitConv"].','.$info["Data16UnitConv"].','.$info["Data17UnitConv"].','.$info["Data18UnitConv"].','.$info["Data19UnitConv"].','.$info["Data20UnitConv"];
                        $unitstringsarr=explode(",",$Result['unitstrings']);
                        $unitconversarr=explode(",",$Result['unitconvers']);
                        $tempkey=$v['DataType']>4?($v['DataType']-8):($v['DataType']-1);
                        $tempunitstring=$unitstringsarr[$tempkey];
                        $tempunitconver=$unitconversarr[$tempkey];
    
                        $Result['单位']=isset($v["unitstring"])&&$v["unitstring"]!=""&&$v["unitstring"]!=-1?$v["unitstring"]:$tempunitstring;
                        $Result['unitconver']=isset($v["unitconver"])&&$v["unitconver"]!=""&&$v["unitconver"]!=-1?$v["unitconver"]:$tempunitconver;
                
                
                        if($Result['unitconver']!="0"&&$Result['unitconver']!=""&&$Result['单位']!="")
                        {
                            if (($Result['unitconver'] == "1" && $Result['单位'] == "千克") || ($Result['unitconver'] == "4" && $Result['单位'] == "吨"))
                            {
                                $dic=$WeightUnitSelect;
                                $Result['danwei']=$dic;
                            }
                            else
                            {
                                $dic=$UnitSelect;
                                $oldunit=$Result['单位'];
                                if($Result['unitconver'] == "1")
                                {
                                    $baseunit = $oldunit;
                                }
                                else if(in_array($Result['unitconver'],array(2,3,4,5,9)))
                                {
                                    $baseunit = mb_strlen($oldunit) == 1?$oldunit:mb_substr($oldunit,1);
                                }
                                else if(in_array($Result['unitconver'],array(6,7,8,10,11,12,13)))
                                {
                                    if (mb_strlen($oldunit) == 2)
                                    $baseunit = mb_substr($oldunit,1);
                                    else if (mb_strlen($oldunit) < 2)
                                        $baseunit = $oldunit;
                                    else
                                        $baseunit = mb_substr($oldunit,2);
                                }
                                foreach($dic as $k1=>$v1)
                                {
                                    $Result['danwei'][]=array($k1,$v1.$baseunit);
                                }
                            }
                
                        }
                        else
                        {
                             $Result['danwei'][]=$Result['unitconver'];
                        }
                        $Result['图例修改']=$v['LineTitle'];
                        //图类型
                        $Result['DataTypes'] = $info["Data1Type"].','.$info["Data2Type"].','.$info["Data3Type"].','.$info["Data4Type"].','.$info["Data5Type"].','.$info["Data6Type"].','.$info["Data7Type"].','.$info["Data8Type"].','.$info["Data9Type"].','.$info["Data10Type"].','.$info["Data11Type"].','.$info["Data12Type"].','.$info["Data13Type"].','.$info["Data14Type"].','.$info["Data15Type"].','.$info["Data16Type"].','.$info["Data17Type"].','.$info["Data18Type"].','.$info["Data19Type"].','.$info["Data20Type"];
                        $Result['DataImage']=$v["DataImage"];
                        $Result['右轴']= $v['zhou'];
                        $Result['data_source']=  $info['data_source']?$info['data_source']:"钢之家";
                        $Result['iscustomerdb']=  "0";
                        $Result['linestylelight']= $v["linestylelight"]!=""?$v["linestylelight"]:"";
                        $Result['linestylewidth']= $v["linestylewidth"]!=""?$v["linestylewidth"]:"";
                        $Result['linestyletype']=  $v["linestyletype"]!=""?$v["linestyletype"]:"";
                    }
                    
                    
                }
                if(isset($v['idxArray'])&&!empty($v['idxArray']))
                {
                    $Result['dtname']=$Result['dtname'].$this->handbptitle($v['idxArray']);
                    $Result['fredata']=$v['idxArray'];
                }

                $Results[]=$Result;
                
            }
            $arr['Success'] = 1;
            $arr['Message'] = "获取成功";
            $arr['Results'] = $Results;
        }
        
        $json_string = $this->pri_JSON($arr);
        echo $json_string;

    }

    function handbptitle($idxArray)
    {

        $fretime=array(
        "5"=>"周",
        "4"=>"旬",
        "3"=>"月",
        "2"=>"季",
        "1"=>"年");
        $fredata=array(
        "sum"=>"合计值",
        "avg"=>"平均值",
        "median"=>"中位数",
        "first"=>"第一条",
        "last"=>"最后一条",
        "max"=>"最大值",
        "min"=>"最小值");

        $title="";
        foreach($idxArray as $v)
        {
            $title.= "(变频";
            $title.=$fretime[$v['newFre']];
            $title.=$fredata[$v['reduceFre']];
            $title.= ")";
        }
        
        return $title;
    }

}

 


function pinyin_sort($a, $b)
{
    $ret = 1;
    //echo $a['dtname']."&".$b['dtname']."::";
    $str_arr = array($a['dtname'], $b['dtname']);
    $py_arr = getPinyin($str_arr);
    $py_arr2 = $py_arr;
    sort($py_arr);
    //print_r($py_arr[0]);echo ":";print_r($py_arr2[0]);
    if ($py_arr[0] === $py_arr2[0]) $ret = -1;
    else $ret = 1;
    //echo "=>".$ret."<br>";
    return $ret;
}

function pinyin_sort2($a, $b)
{
    $ret = 1;
    //echo $a['dtname']."&".$b['dtname']."::";
    $str_arr = array($a['cataloguename'], $b['cataloguename']);
    $py_arr = getPinyin($str_arr);
    $py_arr2 = $py_arr;
    sort($py_arr);
    //print_r($py_arr[0]);echo ":";print_r($py_arr2[0]);
    if ($py_arr[0] === $py_arr2[0]) $ret = -1;
    else $ret = 1;
    //echo "=>".$ret."<br>";
    return $ret;
}

function getPinyin($resource, $pix = ' ')
{
    static $_TDataKey, $_TDataValue;
    $_DataKey = "a|ai|an|ang|ao|ba|bai|ban|bang|bao|bei|ben|beng|bi|bian|biao|bie|bin|bing|bo|bu|ca|cai|can|cang|cao|ce|ceng|cha" . "|chai|chan|chang|chao|che|chen|cheng|chi|chong|chou|chu|chuai|chuan|chuang|chui|chun|chuo|ci|cong|cou|cu|" . "cuan|cui|cun|cuo|da|dai|dan|dang|dao|de|deng|di|dian|diao|die|ding|diu|dong|dou|du|duan|dui|dun|duo|e|en|er" . "|fa|fan|fang|fei|fen|feng|fo|fou|fu|ga|gai|gan|gang|gao|ge|gei|gen|geng|gong|gou|gu|gua|guai|guan|guang|gui" . "|gun|guo|ha|hai|han|hang|hao|he|hei|hen|heng|hong|hou|hu|hua|huai|huan|huang|hui|hun|huo|ji|jia|jian|jiang" . "|jiao|jie|jin|jing|jiong|jiu|ju|juan|jue|jun|ka|kai|kan|kang|kao|ke|ken|keng|kong|kou|ku|kua|kuai|kuan|kuang" . "|kui|kun|kuo|la|lai|lan|lang|lao|le|lei|leng|li|lia|lian|liang|liao|lie|lin|ling|liu|long|lou|lu|lv|luan|lue" . "|lun|luo|ma|mai|man|mang|mao|me|mei|men|meng|mi|mian|miao|mie|min|ming|miu|mo|mou|mu|na|nai|nan|nang|nao|ne" . "|nei|nen|neng|ni|nian|niang|niao|nie|nin|ning|niu|nong|nu|nv|nuan|nue|nuo|o|ou|pa|pai|pan|pang|pao|pei|pen" . "|peng|pi|pian|piao|pie|pin|ping|po|pu|qi|qia|qian|qiang|qiao|qie|qin|qing|qiong|qiu|qu|quan|que|qun|ran|rang" . "|rao|re|ren|reng|ri|rong|rou|ru|ruan|rui|run|ruo|sa|sai|san|sang|sao|se|sen|seng|sha|shai|shan|shang|shao|" . "she|shen|sheng|shi|shou|shu|shua|shuai|shuan|shuang|shui|shun|shuo|si|song|sou|su|suan|sui|sun|suo|ta|tai|" . "tan|tang|tao|te|teng|ti|tian|tiao|tie|ting|tong|tou|tu|tuan|tui|tun|tuo|wa|wai|wan|wang|wei|wen|weng|wo|wu" . "|xi|xia|xian|xiang|xiao|xie|xin|xing|xiong|xiu|xu|xuan|xue|xun|ya|yan|yang|yao|ye|yi|yin|ying|yo|yong|you" . "|yu|yuan|yue|yun|za|zai|zan|zang|zao|ze|zei|zen|zeng|zha|zhai|zhan|zhang|zhao|zhe|zhen|zheng|zhi|zhong|" . "zhou|zhu|zhua|zhuai|zhuan|zhuang|zhui|zhun|zhuo|zi|zong|zou|zu|zuan|zui|zun|zuo";

    $_DataValue = "-20319|-20317|-20304|-20295|-20292|-20283|-20265|-20257|-20242|-20230|-20051|-20036|-20032|-20026|-20002|-19990" . "|-19986|-19982|-19976|-19805|-19784|-19775|-19774|-19763|-19756|-19751|-19746|-19741|-19739|-19728|-19725" . "|-19715|-19540|-19531|-19525|-19515|-19500|-19484|-19479|-19467|-19289|-19288|-19281|-19275|-19270|-19263" . "|-19261|-19249|-19243|-19242|-19238|-19235|-19227|-19224|-19218|-19212|-19038|-19023|-19018|-19006|-19003" . "|-18996|-18977|-18961|-18952|-18783|-18774|-18773|-18763|-18756|-18741|-18735|-18731|-18722|-18710|-18697" . "|-18696|-18526|-18518|-18501|-18490|-18478|-18463|-18448|-18447|-18446|-18239|-18237|-18231|-18220|-18211" . "|-18201|-18184|-18183|-18181|-18012|-17997|-17988|-17970|-17964|-17961|-17950|-17947|-17931|-17928|-17922" . "|-17759|-17752|-17733|-17730|-17721|-17703|-17701|-17697|-17692|-17683|-17676|-17496|-17487|-17482|-17468" . "|-17454|-17433|-17427|-17417|-17202|-17185|-16983|-16970|-16942|-16915|-16733|-16708|-16706|-16689|-16664" . "|-16657|-16647|-16474|-16470|-16465|-16459|-16452|-16448|-16433|-16429|-16427|-16423|-16419|-16412|-16407" . "|-16403|-16401|-16393|-16220|-16216|-16212|-16205|-16202|-16187|-16180|-16171|-16169|-16158|-16155|-15959" . "|-15958|-15944|-15933|-15920|-15915|-15903|-15889|-15878|-15707|-15701|-15681|-15667|-15661|-15659|-15652" . "|-15640|-15631|-15625|-15454|-15448|-15436|-15435|-15419|-15416|-15408|-15394|-15385|-15377|-15375|-15369" . "|-15363|-15362|-15183|-15180|-15165|-15158|-15153|-15150|-15149|-15144|-15143|-15141|-15140|-15139|-15128" . "|-15121|-15119|-15117|-15110|-15109|-14941|-14937|-14933|-14930|-14929|-14928|-14926|-14922|-14921|-14914" . "|-14908|-14902|-14894|-14889|-14882|-14873|-14871|-14857|-14678|-14674|-14670|-14668|-14663|-14654|-14645" . "|-14630|-14594|-14429|-14407|-14399|-14384|-14379|-14368|-14355|-14353|-14345|-14170|-14159|-14151|-14149" . "|-14145|-14140|-14137|-14135|-14125|-14123|-14122|-14112|-14109|-14099|-14097|-14094|-14092|-14090|-14087" . "|-14083|-13917|-13914|-13910|-13907|-13906|-13905|-13896|-13894|-13878|-13870|-13859|-13847|-13831|-13658" . "|-13611|-13601|-13406|-13404|-13400|-13398|-13395|-13391|-13387|-13383|-13367|-13359|-13356|-13343|-13340" . "|-13329|-13326|-13318|-13147|-13138|-13120|-13107|-13096|-13095|-13091|-13076|-13068|-13063|-13060|-12888" . "|-12875|-12871|-12860|-12858|-12852|-12849|-12838|-12831|-12829|-12812|-12802|-12607|-12597|-12594|-12585" . "|-12556|-12359|-12346|-12320|-12300|-12120|-12099|-12089|-12074|-12067|-12058|-12039|-11867|-11861|-11847" . "|-11831|-11798|-11781|-11604|-11589|-11536|-11358|-11340|-11339|-11324|-11303|-11097|-11077|-11067|-11055" . "|-11052|-11045|-11041|-11038|-11024|-11020|-11019|-11018|-11014|-10838|-10832|-10815|-10800|-10790|-10780" . "|-10764|-10587|-10544|-10533|-10519|-10331|-10329|-10328|-10322|-10315|-10309|-10307|-10296|-10281|-10274" . "|-10270|-10262|-10260|-10256|-10254";
    if (empty($_TDataKey)) $_TDataKey = explode('|', $_DataKey);
    if (empty($_TDataValue)) $_TDataValue = explode('|', $_DataValue);
    $data = array_combine($_TDataKey, $_TDataValue);
    //print_r($data);exit;
    arsort($data);
    reset($data);
    if (!is_array($resource)) {
        $resource = array($resource);
    }
    $_Res = array();
    foreach ($resource as $id => $str) {
        $_Res[$id] = '';
        for ($i = 0; $i < strlen($str); $i++) {
            $_P = ord(substr($str, $i, 1));
            if ($_P > 160) {
                $_Q = ord(substr($str, ++$i, 1));
                $_P = $_P * 256 + $_Q - 65536;
            }
            if ($_P > 0 && $_P < 160) {
                $sign = chr($_P);
            } elseif ($_P < -20319 || $_P > -10247) {
                $sign = '';
            } else {
                foreach ($data as $k => $v) {
                    if ($v <= $_P)
                        break;
                }
                $sign = $k;
                //echo $sign."<br>";
            }
            //$sign=$this->_Pinyin ( $_P, $data );
            if ($sign == $pix) $_Res[$id] .= $sign;
            else $_Res[$id] .= $sign . $pix;
        }
    }
    return $_Res;
}

function split2($n)
{
    $n |= 0;
    //echo $n>>1;
    $pad = 0;
    $arr = array();
    while ($n) {
        if ($n & 1) array_push($arr, 1 << $pad);
        //echo $n;
        $pad++;
        $n >>= 1;
    }
    return $arr;
}

