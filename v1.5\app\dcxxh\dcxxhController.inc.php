
<?php
/*
 * @Author: x<PERSON><PERSON><PERSON>
 * @Date: 2022-07-26 08:53:05
 * @LastEditTime: 2022-08-24 19:47:12
 * @FilePath: \drc.in.steelhome.cny:\www.steelhome.cn\data\v1.5\app\dcxxh\dcxxhController.inc.php
 * @Description: 
 * @Copyright: © 2021, SteelHome. All rights reserved.
 */
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );

class dcxxhController extends AbstractController{

  public function __construct(){
    parent::__construct();
	$this->_action->setDao(new dcxxhDao('91R'));
	$this->_action->t1dao = new dcxxhDao('MAIN');
      $this->_action->gcdao = new dcxxhDao('GC');
      
  }


  
  public function do_getScreenData()
  {
        $this->_action->getScreenData($this->_request);
  }
  public function v_index()
  {
        $this->_action->index($this->_request);
  }
  public function v_error()
  {
        $this->_action->error($this->_request);
  }
  public function v_screen()
  {
        $this->_action->screen($this->_request);
  }
  public function v_tagscreen()
  {
        $this->_action->tagscreen($this->_request);
  }
  public function v_template_list()
  {
        $this->_action->template_list($this->_request);
  }
  public function v_template_edit()
  {
        $this->_action->template_edit($this->_request);
  }
  public function do_template_doedit()
  {
        $this->_action->template_doedit($this->_request);
  }
  public function do_template_del()
  {
        $this->_action->template_del($this->_request);
  }
  public function v_label_list()
  {
        $this->_action->label_list($this->_request);
  }
  public function v_label_edit()
  {
        $this->_action->label_edit($this->_request);
  }
  public function v_look_pic()
  {
        $this->_action->look_pic($this->_request);
  }
  public function do_label_doedit()
  {
        $this->_action->label_doedit($this->_request);
  }
  public function v_fp_edit()
  {
        $this->_action->fp_edit($this->_request);
  }
  public function do_fp_doedit()
  {
        $this->_action->fp_doedit($this->_request);
  }
  public function do_label_edit()
  {
        $this->_action->label_edit($this->_request);
  }
  public function do_label_del()
  {
        $this->_action->label_del($this->_request);
  }

  public function do_getScreenData2()
  {
        $this->_action->getScreenData2($this->_request);
  }

  public function do_getScreenData3()
  {
        $this->_action->getScreenData3($this->_request);
  }

  public function do_getScreenData4()
  {
        $this->_action->getScreenData4($this->_request);
  }

  public function do_getScreenData5()
  {
        $this->_action->getScreenData5($this->_request);
  }

  public function do_getScreenData6()
  {
        $this->_action->getScreenData6($this->_request);
  }

  public function do_getmap()
  {
        $this->_action->getmap($this->_request);
  }

  public function do_openlabel()
  {
        $this->_action->openlabel($this->_request);
  }

  public function do_getlabelnum()
  {
        $this->_action->getlabelnum($this->_request);
  }

  public function v_xxhindex()
  {
        $this->_action->xxhindex($this->_request);
  }

  public function do_getuid()
  {
        $this->_action->getuid($this->_request);
  }
  public function do_getchartjs()
  {
        $this->_action->getchartjs($this->_request);
  }
  
}
?>