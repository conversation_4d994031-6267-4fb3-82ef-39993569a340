<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );

class sgtjtzdrController extends AbstractController{
  
	public function __construct(){
		parent::__construct();
		$this->_action->setDao( new sgtjtzdrDao("DRCW") );  //drc
		$this->_action->t1Dao=new sgtjtzdrDao("MAIN");  //
		$this->_action->homeDao=new sgtjtzdrDao("91R");
		$this->_action->gcDao=new sgtjtzdrDao("GC");
	}

	public function _dopre()
	{
		$this->_action->checkSession();
	}
	function v_index()
	{
		$this->_action->index( $this->_request );
	}


	function do_uploadFile()
	{
		$this->_action->uploadFile( $this->_request );
	}


	

}
?>