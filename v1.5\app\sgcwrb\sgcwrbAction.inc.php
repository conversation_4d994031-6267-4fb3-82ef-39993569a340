<?php
$GLOBALS ['city_arr'] = array("西安"=>"xian","郑州"=>"zhengzhou","成都"=>"chengdu","重庆"=>"chongqing","兰州"=>"lanzhou");
$GLOBALS ['jiageid_arr'] = array("1"=>"A120231","2"=>"A113121","3"=>"A111031");
include_once ("cwlr.php");
// class sgcwrbAction extends AbstractAction
class sgcwrbAction extends sgcwlrAction
{
	public function __construct()
	{
    	parent::__construct();
	}

	public function checkSession()
	{
	}

	/*周环比*/
	function weekday($date){
		//echo $date;
		$wnum=date("w",strtotime($date));
		$weekd["be"]=$date;
		if ($wnum==0){
		   //$weekd["be"]=$date;
		   $weekd["bs"]=date("Y-m-d",strtotime ("-6 day $date"));
		   $weekd["se"]=date("Y-m-d",strtotime ("-7 day $date"));
		   $weekd["ss"]=date("Y-m-d",strtotime ("-13 day $date"));
		}else{
		   $jwn=7-$wnum;
		   $gwn=$wnum-1;
		   $weekd["be"]=date("Y-m-d",strtotime ("+$jwn day $date"));
		   $weekd["bs"]=date("Y-m-d",strtotime ("-$gwn day $date"));
		   //为了处理国庆节期间无数据，将日期强行推前1个星期
		   //$sgwn=$wnum+13;
		   $sgwn=$wnum+6;
		   $weekd["se"]=date("Y-m-d",strtotime ("-$wnum day $date"));
		   $weekd["ss"]=date("Y-m-d",strtotime ("-$sgwn day $date"));
		}
		//added by shizg for 国庆节数据处理 started 2017/10/10
		require_once('/etc/steelconf/config/isholiday.php'); 
	   $date2 = $weekd["ss"];
	   $needckecklastweek = 0;
	   while($date2<$weekd["se"]){
		   if(!_isholiday($date2)){
			   $needckecklastweek = 1;
		   }
		   $date2 = date("Y-m-d",strtotime($date2."+1 day"));
	   }
		if($needckecklastweek==0){
		   $weekd["se"]=date("Y-m-d",strtotime ($weekd["se"]."-7 day "));
		   $weekd["ss"]=date("Y-m-d",strtotime ($weekd["ss"]."-7 day "));
		}
	   // if (date("d")>16 && date("d")< 28){
			 //$weekd=array("be"=>'2007-03-02',"bs" =>'2007-02-25',"se" =>'2007-02-16',"ss" =>'2007-02-12');
		//}		
		return $weekd;
	}
	/*月环比*/
	function monday($date){
		$mnum=date("n",strtotime($date));
		$y=date("Y",strtotime($date));
		if ($mnum==1){		    
		   $weekd["bs"]=$y."-01-01";
		   $weekd["be"]=$y."-01-".date("d",strtotime($date));
		   $weekd["ss"]=($y-1)."-12-01";
		   $weekd["se"]=($y-1)."-12-31";
		}else{
		   $weekd["bs"]=$y."-".date("m",strtotime($date))."-01";
		   $weekd["be"]=$y."-".date("m",strtotime($date))."-".date("d",strtotime($date));
		   $mnum1=$mnum-1;
		   if ($mnum1<10){$mnum1="0".$mnum1;}
		   $weekd["ss"]=$y."-".$mnum1."-01";
		   $weekd["se"]=$y."-".$mnum1."-31";
		}
		return $weekd;
	}
	/*月同比*/
	function monday_same($date)
	{
		if ((date("m",strtotime($date))-1)<0){	$yab=date("Y",strtotime($date))-1;$mab=12;}else{	$yab=date("Y",strtotime($date))-1;$mab=date("m",strtotime($date));}
	  	$semd=array("se"=>$yab.'-'.$mab.'-31',"ss" =>$yab.'-'.$mab.'-01',
	              "be" =>date("Y",strtotime($date)).'-'.date("m",strtotime($date)).'-'.date("d",strtotime($date)),
				  "bs" =>date("Y",strtotime($date)).'-'.date("m",strtotime($date)).'-01');
		return $semd;
	}
	/*季环比*/
	function season($date){
		$mnum=date("n",strtotime($date));
		$y=date("Y",strtotime($date));
		switch ($mnum){
		   case 1:
		   case 2:
		   case 3:
			  $weekd["bs"]=$y."-01-01";$weekd["be"]=$y."-03-".date("d",strtotime($date));$weekd["ss"]=($y-1)."-10-01";$weekd["se"]=($y-1)."-12-31";break;
		   case 4:
		   case 5:
		   case 6:
			 $weekd["bs"]=$y."-04-01";$weekd["be"]=$y."-06-".date("d",strtotime($date));$weekd["ss"]=$y."-01-01";$weekd["se"]=$y."-03-31";break;
		   case 7:
		   case 8:
		   case 9:
			 $weekd["bs"]=$y."-07-01";$weekd["be"]=$y."-09-".date("d",strtotime($date));$weekd["ss"]=$y."-04-01";$weekd["se"]=$y."-06-30";break;
		   case 10:
		   case 11:
		   case 12:
			 $weekd["bs"]=$y."-10-01";$weekd["be"]=$y."-12-".date("d",strtotime($date));$weekd["ss"]=$y."-07-01";$weekd["se"]=$y."-09-30";break; 
		}
		return $weekd;
	}
	/*年环比*/
	function yearday($date){
		$y=date("Y",strtotime($date));
		$weekd["bs"]=$y."-01-01";
		$weekd["be"]=$y."-12-".date("d",strtotime($date));
		$weekd["ss"]=($y-1)."-01-01";
		$weekd["se"]=($y-1)."-12-31";
		return $weekd;
	}
	/*年同比*/
	function yearday_same($date)
	{
		$seyy=(date("Y",strtotime($date))-1)."-".date("m",strtotime($date))."-".date("d",strtotime($date));
		$seyd=array( "se"=>$seyy,"ss" =>(date("Y",strtotime($date))-1).'-01-01',"be" =>date("Y-m-d",strtotime($date)),"bs" =>date("Y",strtotime($date)).'-01-01');
		return $seyd;
	}

	//陕钢财务日报
	public function index($params)
	{
		$arrname = array(
			"一、产量" => array("钢坯产量","钢材产量"),
			"二、销量" => array("钢材结算销量"),
			"三、售价" => array("二级售价<br>（含税含运费）","运费（含税）","一级售价<br>（不含税不含运费）"),
			"四、成本" => array("生产成本","吨钢期间费用","完全成本"),
			"五、期间费用" => array("销售费用","管理费用","财务费用"),
			"六、其他" => array("检斤检尺差异","韩城公司结算30元","税金及附加等"),
			"七、利润" => array("吨钢利润","利润总额"),
			"八、月度计划" => array("产量","管理费用","财务费用","销售费用","合计")
		);
		$table_arr = array();
		foreach ($arrname as $k => $v) {
			$table_arr[$k]['rows'] = count($v,0)*2;
			foreach ($v as $v2) {
				$table_arr[$k][$v2]['lg'] = array();
				$table_arr[$k][$v2]['hg'] = array();
				if ($k == "八、月度计划" || $v2 == "利润总额") {
					$table_arr[$k][$v2]['danwei'] = "万元";
				} elseif ($k == "一、产量" || $v2 == "产量" || $v2 == "钢材结算销量") {
					$table_arr[$k][$v2]['danwei'] = "万吨";
				} else {
					$table_arr[$k][$v2]['danwei'] = "元/吨";
				}
			}
		}
		// echo"<pre>";print_r($table_arr);
		$sdate = $params['sdate'];
		if(empty($sdate)){
			$sdate = date("Y-m-d");
		}
		$this->assign("sdate",$sdate);

		// $sql = "select dta_ym from sg_data_table where dta_type like '%SGcbrb%' and dta_ym <= '$sdate' order by dta_ym desc limit 1";
		// $date_arr[] = $this->_dao->getOne($sql);
		// $sql = "SELECT dta_ym FROM `sg_data_table` WHERE `dta_type` = 'TEST_ERP_XSSJ' AND `dta_ym` <= '$sdate' AND `dta_1` = '钢材日销售累计' order by dta_ym desc limit 1";
		// $date_arr[] = $this->_dao->getOne($sql);
		// $sql = "SELECT dta_ym FROM `sg_data_table` WHERE `dta_type` = 'TEST_ERP_ZHYF' AND `dta_ym` <= '$sdate' order by dta_ym desc limit 1";
		// $date_arr[] = $this->_dao->getOne($sql);
		// $sdate = min($date_arr);
		// $ldate = $this->_dao->getOne("select dta_ym from sg_data_table where dta_type like '%SGcbrb%' and dta_ym < '$sdate' order by dta_ym desc limit 1");
		$ldate = $this->_dao->getOne("SELECT dta_ym FROM `sg_data_table` WHERE `dta_type` = 'TEST_ERP_ZHYF' AND dta_ym < '$sdate' order by dta_ym desc limit 1");

		$b_month = date("n",strtotime($sdate));
		$s_month = date("n",strtotime("-1 month".$sdate));
		$s_month1 = date("Y-m",strtotime($sdate));
		$s_month2 = date("Y-m",strtotime("-1 month".$sdate));
		$today = date("n月j日",strtotime($sdate));
		$yestoday = date("n月j日",strtotime($ldate));
		$this->assign("b_month",$b_month);
		$this->assign("s_month",$s_month);
		$this->assign("today",$today);
		$this->assign("yestoday",$yestoday);

		//产量、生产成本
		$sy_chanling_info = $this->_dao->getYB($s_month2,2);
		$sy_chanling = array();
		foreach($sy_chanling_info as $v){
			$sy_chanling[$v['dta_vartype']][$v['dta_type']] = round($v['dta_7'],2);
		}
		$this->assign("sy_chanling",$sy_chanling);
		$sy_sccb_info = $this->_dao->getYB($s_month2,1,"成本费用");
		$sy_sccb = array();
		foreach($sy_sccb_info as $v){
			$sy_sccb[$v['dta_vartype']][$v['dta_type']] = round($v['dta_5'],2);
		}
		// echo"<pre>";print_r($sy_sccb);
		$table_arr['一、产量']['钢坯产量']['lg']['lastmon'] = round($sy_chanling['炼钢']['SG_CBYBB_LG']/10000,2);
		$table_arr['一、产量']['钢坯产量']['hg']['lastmon'] = round($sy_chanling['炼钢']['SG_CBYBB_HG']/10000,2);
		$table_arr['一、产量']['钢材产量']['lg']['lastmon'] = round($sy_chanling['轧钢']['SG_CBYBB_LG']/10000,2);
		$table_arr['一、产量']['钢材产量']['hg']['lastmon'] = round($sy_chanling['轧钢']['SG_CBYBB_HG']/10000,2);
		$table_arr['四、成本']['生产成本']['lg']['lastmon'] = $sy_sccb['轧钢']['SG_CBYBB_LG'];
		$table_arr['四、成本']['生产成本']['hg']['lastmon'] = $sy_sccb['轧钢']['SG_CBYBB_HG'];
		
		$zt_chanling = $this->_dao->getRB($ldate);
		$jt_chanling = $this->_dao->getRB($sdate);
		$table_arr['一、产量']['钢坯产量']['lg']['yesterday'] = $zt_chanling['SGcbrblg']['dta_1'];
		$table_arr['一、产量']['钢坯产量']['lg']['today'] = $jt_chanling['SGcbrblg']['dta_1'];
		$table_arr['一、产量']['钢坯产量']['lg']['thismon'] = $jt_chanling['SGcbrblg']['dta_2'];
		$table_arr['一、产量']['钢坯产量']['hg']['yesterday'] = $zt_chanling['SGcbrbhg']['dta_1'];
		$table_arr['一、产量']['钢坯产量']['hg']['today'] = $jt_chanling['SGcbrbhg']['dta_1'];
		$table_arr['一、产量']['钢坯产量']['hg']['thismon'] = $jt_chanling['SGcbrbhg']['dta_2'];

		$table_arr['一、产量']['钢材产量']['lg']['yesterday'] = $zt_chanling['SGcbrblg']['dta_3'];
		$table_arr['一、产量']['钢材产量']['lg']['today'] = $jt_chanling['SGcbrblg']['dta_3'];
		$table_arr['一、产量']['钢材产量']['lg']['thismon'] = $jt_chanling['SGcbrblg']['dta_4'];
		$table_arr['一、产量']['钢材产量']['hg']['yesterday'] = $zt_chanling['SGcbrbhg']['dta_3'];
		$table_arr['一、产量']['钢材产量']['hg']['today'] = $jt_chanling['SGcbrbhg']['dta_3'];
		$table_arr['一、产量']['钢材产量']['hg']['thismon'] = $jt_chanling['SGcbrbhg']['dta_4'];

		$table_arr['四、成本']['生产成本']['lg']['yesterday'] = $zt_chanling['SGcbrblg']['dta_5'];
		$table_arr['四、成本']['生产成本']['lg']['today'] = $jt_chanling['SGcbrblg']['dta_5'];
		$table_arr['四、成本']['生产成本']['lg']['thismon'] = $jt_chanling['SGcbrblg']['dta_6'];
		$table_arr['四、成本']['生产成本']['hg']['yesterday'] = $zt_chanling['SGcbrbhg']['dta_5'];
		$table_arr['四、成本']['生产成本']['hg']['today'] = $jt_chanling['SGcbrbhg']['dta_5'];
		$table_arr['四、成本']['生产成本']['hg']['thismon'] = $jt_chanling['SGcbrbhg']['dta_6'];

		$table_arr['一、产量']['钢坯产量']['lg']['huanbi'] = $table_arr['一、产量']['钢坯产量']['lg']['today'] - $table_arr['一、产量']['钢坯产量']['lg']['yesterday'];
		$table_arr['一、产量']['钢坯产量']['lg']['tongbi'] = $table_arr['一、产量']['钢坯产量']['lg']['thismon'] - $table_arr['一、产量']['钢坯产量']['lg']['lastmon'];
		$table_arr['一、产量']['钢坯产量']['hg']['huanbi'] = $table_arr['一、产量']['钢坯产量']['hg']['today'] - $table_arr['一、产量']['钢坯产量']['hg']['yesterday'];
		$table_arr['一、产量']['钢坯产量']['hg']['tongbi'] = $table_arr['一、产量']['钢坯产量']['hg']['thismon'] - $table_arr['一、产量']['钢坯产量']['hg']['lastmon'];

		$table_arr['一、产量']['钢材产量']['lg']['huanbi'] = $table_arr['一、产量']['钢材产量']['lg']['today'] - $table_arr['一、产量']['钢材产量']['lg']['yesterday'];
		$table_arr['一、产量']['钢材产量']['lg']['tongbi'] = $table_arr['一、产量']['钢材产量']['lg']['thismon'] - $table_arr['一、产量']['钢材产量']['lg']['lastmon'];
		$table_arr['一、产量']['钢材产量']['hg']['huanbi'] = $table_arr['一、产量']['钢材产量']['hg']['today'] - $table_arr['一、产量']['钢材产量']['hg']['yesterday'];
		$table_arr['一、产量']['钢材产量']['hg']['tongbi'] = $table_arr['一、产量']['钢材产量']['hg']['thismon'] - $table_arr['一、产量']['钢材产量']['hg']['lastmon'];

		$table_arr['四、成本']['生产成本']['lg']['huanbi'] = $table_arr['四、成本']['生产成本']['lg']['today'] - $table_arr['四、成本']['生产成本']['lg']['yesterday'];
		$table_arr['四、成本']['生产成本']['lg']['tongbi'] = $table_arr['四、成本']['生产成本']['lg']['thismon'] - $table_arr['四、成本']['生产成本']['lg']['lastmon'];
		$table_arr['四、成本']['生产成本']['hg']['huanbi'] = $table_arr['四、成本']['生产成本']['hg']['today'] - $table_arr['四、成本']['生产成本']['hg']['yesterday'];
		$table_arr['四、成本']['生产成本']['hg']['tongbi'] = $table_arr['四、成本']['生产成本']['hg']['thismon'] - $table_arr['四、成本']['生产成本']['hg']['lastmon'];
		// echo"<pre>";print_r($table_arr);

		// 八、月度计划
		$sql = "select dta_1,dta_2,dta_3,dta_4,dta_5,dta_6 from sg_data_table where dta_type='SG_SXFYLR' and dta_ym='$s_month1'";
		$by_ydjh_info = $this->_dao->getrow($sql);
		$sql = "select dta_1,dta_2,dta_3,dta_4,dta_5,dta_6 from sg_data_table where dta_type='SG_SXFYLR' and dta_ym='$s_month2'";
		$sy_ydjh_info = $this->_dao->getrow($sql);
		$tianshu = date('t', strtotime($sdate));
		$ri = date('j', strtotime($sdate));
		$table_arr["八、月度计划"]["管理费用"]["lg"]['lastmon'] = round($sy_ydjh_info['dta_1'],2);
		$table_arr["八、月度计划"]["管理费用"]["hg"]['lastmon'] = round($sy_ydjh_info['dta_4'],2);
		$table_arr["八、月度计划"]["财务费用"]["lg"]['lastmon'] = round($sy_ydjh_info['dta_2'],2);
		$table_arr["八、月度计划"]["财务费用"]["hg"]['lastmon'] = round($sy_ydjh_info['dta_5'],2);
		$table_arr["八、月度计划"]["销售费用"]["lg"]['lastmon'] = round($sy_ydjh_info['dta_3'],2);
		$table_arr["八、月度计划"]["销售费用"]["hg"]['lastmon'] = round($sy_ydjh_info['dta_6'],2);

		$table_arr["八、月度计划"]["管理费用"]["lg"]['yesterday'] = $table_arr["八、月度计划"]["管理费用"]["lg"]['today'] = round($by_ydjh_info['dta_1']/$tianshu ,2);
		$table_arr["八、月度计划"]["管理费用"]["lg"]['thismon'] = $table_arr["八、月度计划"]["管理费用"]["lg"]['huanbi'] = round($by_ydjh_info['dta_1']/$tianshu*$ri ,2);
		$table_arr["八、月度计划"]["管理费用"]["lg"]['tongbi'] = $table_arr["八、月度计划"]["管理费用"]["lg"]['thismon'] - $table_arr["八、月度计划"]["管理费用"]["lg"]['lastmon'];
		$table_arr["八、月度计划"]["管理费用"]["hg"]['yesterday'] = $table_arr["八、月度计划"]["管理费用"]["hg"]['today'] = round($by_ydjh_info['dta_4']/$tianshu ,2);
		$table_arr["八、月度计划"]["管理费用"]["hg"]['thismon'] = $table_arr["八、月度计划"]["管理费用"]["hg"]['huanbi'] = round($by_ydjh_info['dta_4']/$tianshu*$ri ,2);
		$table_arr["八、月度计划"]["管理费用"]["hg"]['tongbi'] = $table_arr["八、月度计划"]["管理费用"]["hg"]['thismon'] - $table_arr["八、月度计划"]["管理费用"]["hg"]['lastmon'];

		$table_arr["八、月度计划"]["财务费用"]["lg"]['yesterday'] = $table_arr["八、月度计划"]["财务费用"]["lg"]['today'] = round($by_ydjh_info['dta_2']/$tianshu ,2);
		$table_arr["八、月度计划"]["财务费用"]["lg"]['thismon'] = $table_arr["八、月度计划"]["财务费用"]["lg"]['huanbi'] = round($by_ydjh_info['dta_2']/$tianshu*$ri ,2);
		$table_arr["八、月度计划"]["财务费用"]["lg"]['tongbi'] = $table_arr["八、月度计划"]["财务费用"]["lg"]['thismon'] - $table_arr["八、月度计划"]["财务费用"]["lg"]['lastmon'];
		$table_arr["八、月度计划"]["财务费用"]["hg"]['yesterday'] = $table_arr["八、月度计划"]["财务费用"]["hg"]['today'] = round($by_ydjh_info['dta_5']/$tianshu ,2);
		$table_arr["八、月度计划"]["财务费用"]["hg"]['thismon'] = $table_arr["八、月度计划"]["财务费用"]["hg"]['huanbi'] = round($by_ydjh_info['dta_5']/$tianshu*$ri ,2);
		$table_arr["八、月度计划"]["财务费用"]["hg"]['tongbi'] = $table_arr["八、月度计划"]["财务费用"]["hg"]['thismon'] - $table_arr["八、月度计划"]["财务费用"]["hg"]['lastmon'];

		$table_arr["八、月度计划"]["销售费用"]["lg"]['yesterday'] = $table_arr["八、月度计划"]["销售费用"]["lg"]['today'] = round($by_ydjh_info['dta_3']/$tianshu ,2);
		$table_arr["八、月度计划"]["销售费用"]["lg"]['thismon'] = $table_arr["八、月度计划"]["销售费用"]["lg"]['huanbi'] = round($by_ydjh_info['dta_3']/$tianshu*$ri ,2);
		$table_arr["八、月度计划"]["销售费用"]["lg"]['tongbi'] = $table_arr["八、月度计划"]["销售费用"]["lg"]['thismon'] - $table_arr["八、月度计划"]["销售费用"]["lg"]['lastmon'];
		$table_arr["八、月度计划"]["销售费用"]["hg"]['yesterday'] = $table_arr["八、月度计划"]["销售费用"]["hg"]['today'] = round($by_ydjh_info['dta_6']/$tianshu ,2);
		$table_arr["八、月度计划"]["销售费用"]["hg"]['thismon'] = $table_arr["八、月度计划"]["销售费用"]["hg"]['huanbi'] = round($by_ydjh_info['dta_6']/$tianshu*$ri ,2);
		$table_arr["八、月度计划"]["销售费用"]["hg"]['tongbi'] = $table_arr["八、月度计划"]["销售费用"]["hg"]['thismon'] - $table_arr["八、月度计划"]["销售费用"]["hg"]['lastmon'];

		$table_arr["八、月度计划"]["合计"]["lg"]['lastmon'] = $table_arr["八、月度计划"]["管理费用"]["lg"]['lastmon'] + $table_arr["八、月度计划"]["财务费用"]["lg"]['lastmon'] + $table_arr["八、月度计划"]["销售费用"]["lg"]['lastmon'];
		$table_arr["八、月度计划"]["合计"]["lg"]['yesterday'] = $table_arr["八、月度计划"]["合计"]["lg"]['today'] = round(($by_ydjh_info['dta_1']+$by_ydjh_info['dta_2']+$by_ydjh_info['dta_3'])/$tianshu ,2);
		$table_arr["八、月度计划"]["合计"]["lg"]['thismon'] = $table_arr["八、月度计划"]["合计"]["lg"]['huanbi'] = round(($by_ydjh_info['dta_1']+$by_ydjh_info['dta_2']+$by_ydjh_info['dta_3'])/$tianshu*$ri ,2);
		$table_arr["八、月度计划"]["合计"]["lg"]['tongbi'] = $table_arr["八、月度计划"]["合计"]["lg"]['thismon'] - $table_arr["八、月度计划"]["合计"]["lg"]['lastmon'];
		$table_arr["八、月度计划"]["合计"]["hg"]['lastmon'] = $table_arr["八、月度计划"]["管理费用"]["hg"]['lastmon'] + $table_arr["八、月度计划"]["财务费用"]["hg"]['lastmon'] + $table_arr["八、月度计划"]["销售费用"]["hg"]['lastmon'];
		$table_arr["八、月度计划"]["合计"]["hg"]['yesterday'] = $table_arr["八、月度计划"]["合计"]["hg"]['today'] = round(($by_ydjh_info['dta_4']+$by_ydjh_info['dta_5']+$by_ydjh_info['dta_6'])/$tianshu ,2);
		$table_arr["八、月度计划"]["合计"]["hg"]['thismon'] = $table_arr["八、月度计划"]["合计"]["hg"]['huanbi'] = round(($by_ydjh_info['dta_4']+$by_ydjh_info['dta_5']+$by_ydjh_info['dta_6'])/$tianshu*$ri ,2);
		$table_arr["八、月度计划"]["合计"]["hg"]['tongbi'] = $table_arr["八、月度计划"]["合计"]["hg"]['thismon'] - $table_arr["八、月度计划"]["合计"]["hg"]['lastmon'];
		// echo"<pre>";print_r($table_arr);

		$sql = "SELECT dta_7,dta_6,dta_5 FROM `sg_data_table` WHERE `dta_type` = 'TEST_ERP_XSSJ' AND `dta_1` = '钢材月销售累计' AND `dta_ym` = '$s_month1'";
		$thismon_YF = $this->_dao->query($sql);
		$sql = "SELECT dta_7,dta_6,dta_5 FROM `sg_data_table` WHERE `dta_type` = 'TEST_ERP_XSSJ' AND `dta_1` = '钢材日销售累计'AND `dta_ym` = '$sdate'";
		$today_YF = $this->_dao->query($sql);
		$sql = "SELECT dta_7,dta_6,dta_5 FROM `sg_data_table` WHERE `dta_type` = 'TEST_ERP_XSSJ' AND `dta_1` = '钢材日销售累计'AND `dta_ym` = '$ldate'";
		$yesterday_YF = $this->_dao->query($sql);
		$sql = "SELECT dta_7,dta_6,dta_5 FROM `sg_data_table` WHERE `dta_type` = 'TEST_ERP_XSSJ' AND `dta_1` = '钢材月销售累计' AND `dta_ym` = '$s_month2'";
		$lastmon_YF = $this->_dao->query($sql);
		foreach ($thismon_YF as $v) {
			$thismon_YF1[$v['dta_7']] = round($v['dta_6']/$v['dta_5'],2);
		}
		foreach ($today_YF as $v) {
			$today_YF1[$v['dta_7']] = round($v['dta_6']/$v['dta_5'],2);
		}
		foreach ($yesterday_YF as $v) {
			$yesterday_YF1[$v['dta_7']] = round($v['dta_6']/$v['dta_5'],2);
		}
		foreach ($lastmon_YF as $v) {
			$lastmon_YF1[$v['dta_7']] = round($v['dta_6']/$v['dta_5'],2);
		}
		$table_arr['三、售价']['二级售价<br>（含税含运费）']['lg']['lastmon'] = $lastmon_YF1['龙钢'];
		$table_arr['三、售价']['二级售价<br>（含税含运费）']['hg']['lastmon'] = $lastmon_YF1['汉钢'];
		$table_arr['三、售价']['二级售价<br>（含税含运费）']['lg']['thismon'] = $thismon_YF1['龙钢'];
		$table_arr['三、售价']['二级售价<br>（含税含运费）']['hg']['thismon'] = $thismon_YF1['汉钢'];
		$table_arr['三、售价']['二级售价<br>（含税含运费）']['lg']["today"] = $today_YF1['龙钢'];
		$table_arr['三、售价']['二级售价<br>（含税含运费）']['hg']["today"] = $today_YF1['汉钢'];
		$table_arr['三、售价']['二级售价<br>（含税含运费）']['lg']["yesterday"] = $yesterday_YF1['龙钢'];
		$table_arr['三、售价']['二级售价<br>（含税含运费）']['hg']["yesterday"] = $yesterday_YF1['汉钢'];
		$table_arr['三、售价']['二级售价<br>（含税含运费）']['lg']["huanbi"] = $table_arr['三、售价']['二级售价<br>（含税含运费）']['lg']["today"] - $table_arr['三、售价']['二级售价<br>（含税含运费）']['lg']["yesterday"];
		$table_arr['三、售价']['二级售价<br>（含税含运费）']['hg']["huanbi"] = $table_arr['三、售价']['二级售价<br>（含税含运费）']['hg']["today"] - $table_arr['三、售价']['二级售价<br>（含税含运费）']['hg']["yesterday"];
		$table_arr['三、售价']['二级售价<br>（含税含运费）']['lg']["tongbi"] = $table_arr['三、售价']['二级售价<br>（含税含运费）']['lg']["thismon"] - $table_arr['三、售价']['二级售价<br>（含税含运费）']['lg']["lastmon"];
		$table_arr['三、售价']['二级售价<br>（含税含运费）']['hg']["tongbi"] = $table_arr['三、售价']['二级售价<br>（含税含运费）']['hg']["thismon"] - $table_arr['三、售价']['二级售价<br>（含税含运费）']['hg']["lastmon"];

		$month_start = date("Y-m-01",strtotime($sdate));
		// echo $month_start;
		// $sql = "SELECT dta_1,sum(dta_2) as dta_2 FROM `sg_data_table` WHERE `dta_type` = 'TEST_ERP_ZHYF' AND `dta_ym` >= '$month_start' and `dta_ym` <= '$sdate'  group by dta_1";
		$sql = "SELECT dta_1,sum(dta_3) as dta_3 FROM `sg_data_table` WHERE `dta_type` = 'TEST_ERP_YF' AND `dta_ym` = '$s_month1' group by dta_1";
		$thismon_YF = $this->_dao->query($sql);
		$sql = "SELECT dta_1,dta_2 FROM `sg_data_table` WHERE `dta_type` = 'TEST_ERP_ZHYF' and `dta_ym` = '$sdate' ";
		$today_YF = $this->_dao->query($sql);
		$sql = "SELECT dta_1,dta_2 FROM `sg_data_table` WHERE `dta_type` = 'TEST_ERP_ZHYF' and `dta_ym` = '$ldate' ";
		$yesterday_YF = $this->_dao->query($sql);
		$sql = "SELECT dta_1,sum(dta_3) as dta_3 FROM `sg_data_table` WHERE `dta_type` = 'TEST_ERP_YF' AND `dta_ym` = '$s_month2' group by dta_1";
		$lastmon_YF = $this->_dao->query($sql);
		foreach ($thismon_YF as $v) {
			$thismon_YF1[$v['dta_1']] = $v['dta_2'];
		}
		foreach ($today_YF as $v) {
			$today_YF1[$v['dta_1']] = $v['dta_2'];
		}
		foreach ($yesterday_YF as $v) {
			$yesterday_YF1[$v['dta_1']] = $v['dta_2'];
		}
		foreach ($lastmon_YF as $v) {
			$lastmon_YF1[$v['dta_1']] = $v['dta_3'];
		}
		$table_arr['三、售价']['运费（含税）']['lg']['lastmon'] = $lastmon_YF1['龙钢'];
		$table_arr['三、售价']['运费（含税）']['hg']['lastmon'] = $lastmon_YF1['汉钢'];
		$table_arr['三、售价']['运费（含税）']['lg']['thismon'] = $thismon_YF1['龙钢'];
		$table_arr['三、售价']['运费（含税）']['hg']['thismon'] = $thismon_YF1['汉钢'];
		$table_arr['三、售价']['运费（含税）']['lg']["today"] = $today_YF1['龙钢'];
		$table_arr['三、售价']['运费（含税）']['hg']["today"] = $today_YF1['汉钢'];
		$table_arr['三、售价']['运费（含税）']['lg']["yesterday"] = $yesterday_YF1['龙钢'];
		$table_arr['三、售价']['运费（含税）']['hg']["yesterday"] = $yesterday_YF1['汉钢'];
		$table_arr['三、售价']['运费（含税）']['lg']["huanbi"] = $table_arr['三、售价']['运费（含税）']['lg']["today"] - $table_arr['三、售价']['运费（含税）']['lg']["yesterday"];
		$table_arr['三、售价']['运费（含税）']['hg']["huanbi"] = $table_arr['三、售价']['运费（含税）']['hg']["today"] - $table_arr['三、售价']['运费（含税）']['hg']["yesterday"];
		$table_arr['三、售价']['运费（含税）']['lg']["tongbi"] = $table_arr['三、售价']['运费（含税）']['lg']["thismon"] - $table_arr['三、售价']['运费（含税）']['lg']["lastmon"];
		$table_arr['三、售价']['运费（含税）']['hg']["tongbi"] = $table_arr['三、售价']['运费（含税）']['hg']["thismon"] - $table_arr['三、售价']['运费（含税）']['hg']["lastmon"];
		// echo"<pre>";print_r($yestoday_YF1);

		// 钢材结算销量
		$sql = "SELECT dta_7,dta_5 FROM `sg_data_table` WHERE `dta_type` = 'TEST_ERP_XSSJ' AND `dta_ym` = '$s_month2'";
		$yxslj_sy = $this->_dao->query($sql);
		foreach ($yxslj_sy as $v) {
			if ($v["dta_7"] == "龙钢") {
				$table_arr['二、销量']['钢材结算销量']['lg']["lastmon"] = $v["dta_5"];
			} elseif ($v["dta_7"] == "汉钢") {
				$table_arr['二、销量']['钢材结算销量']['hg']["lastmon"] = $v["dta_5"];
			}
		}
		$sql = "SELECT dta_7,dta_5 FROM `sg_data_table` WHERE `dta_type` = 'TEST_ERP_XSSJ' AND `dta_ym` = '$s_month1'";
		$yxslj_by = $this->_dao->query($sql);
		foreach ($yxslj_by as $v) {
			if ($v["dta_7"] == "龙钢") {
				$table_arr['二、销量']['钢材结算销量']['lg']["thismon"] = $v["dta_5"];
			} elseif ($v["dta_7"] == "汉钢") {
				$table_arr['二、销量']['钢材结算销量']['hg']["thismon"] = $v["dta_5"];
			}
		}
		$sql = "SELECT dta_7,dta_5 FROM `sg_data_table` WHERE `dta_type` = 'TEST_ERP_XSSJ' AND `dta_ym` = '$sdate' AND `dta_1` = '钢材日销售累计'";
		$yxslj_jt = $this->_dao->query($sql);
		foreach ($yxslj_jt as $v) {
			if ($v["dta_7"] == "龙钢") {
				$table_arr['二、销量']['钢材结算销量']['lg']["today"] = $v["dta_5"];
			} elseif ($v["dta_7"] == "汉钢") {
				$table_arr['二、销量']['钢材结算销量']['hg']["today"] = $v["dta_5"];
			}
		}
		$sql = "SELECT dta_7,dta_5 FROM `sg_data_table` WHERE `dta_type` = 'TEST_ERP_XSSJ' AND `dta_ym` = '$ldate' AND `dta_1` = '钢材日销售累计'";
		$yxslj_zt = $this->_dao->query($sql);
		foreach ($yxslj_zt as $v) {
			if ($v["dta_7"] == "龙钢") {
				$table_arr['二、销量']['钢材结算销量']['lg']["yesterday"] = $v["dta_5"];
			} elseif ($v["dta_7"] == "汉钢") {
				$table_arr['二、销量']['钢材结算销量']['hg']["yesterday"] = $v["dta_5"];
			}
		}
		$table_arr['二、销量']['钢材结算销量']['lg']["huanbi"] = $table_arr['二、销量']['钢材结算销量']['lg']['today'] - $table_arr['二、销量']['钢材结算销量']['lg']['yesterday'];
		$table_arr['二、销量']['钢材结算销量']['lg']["tongbi"] = $table_arr['二、销量']['钢材结算销量']['lg']['thismon'] - $table_arr['二、销量']['钢材结算销量']['lg']['lastmon'];
		$table_arr['二、销量']['钢材结算销量']['hg']["huanbi"] = $table_arr['二、销量']['钢材结算销量']['hg']['today'] - $table_arr['二、销量']['钢材结算销量']['hg']['yesterday'];
		$table_arr['二、销量']['钢材结算销量']['hg']["tongbi"] = $table_arr['二、销量']['钢材结算销量']['hg']['thismon'] - $table_arr['二、销量']['钢材结算销量']['hg']['lastmon'];

		$table_arr['八、月度计划']['产量']['lg']['lastmon'] = $table_arr['二、销量']['钢材结算销量']['lg']['lastmon'];
		$table_arr['八、月度计划']['产量']['hg']['lastmon'] = $table_arr['二、销量']['钢材结算销量']['hg']['lastmon'];
		$table_arr['八、月度计划']['产量']['lg']['thismon'] = $table_arr['二、销量']['钢材结算销量']['lg']['thismon'];
		$table_arr['八、月度计划']['产量']['hg']['thismon'] = $table_arr['二、销量']['钢材结算销量']['hg']['thismon'];
		$table_arr['八、月度计划']['产量']['lg']["today"] = $table_arr['一、产量']['钢坯产量']['lg']['today'];
		$table_arr['八、月度计划']['产量']['hg']["today"] = $table_arr['一、产量']['钢坯产量']['hg']['today'];
		$table_arr['八、月度计划']['产量']['lg']["yesterday"] = $table_arr['一、产量']['钢坯产量']['lg']['yesterday'];
		$table_arr['八、月度计划']['产量']['hg']["yesterday"] = $table_arr['一、产量']['钢坯产量']['hg']['yesterday'];
		$table_arr['八、月度计划']['产量']['lg']["huanbi"] = $table_arr['八、月度计划']['产量']['lg']["today"] - $table_arr['八、月度计划']['产量']['lg']["yesterday"];
		$table_arr['八、月度计划']['产量']['hg']["huanbi"] = $table_arr['八、月度计划']['产量']['hg']["today"] - $table_arr['八、月度计划']['产量']['hg']["yesterday"];
		$table_arr['八、月度计划']['产量']['lg']["tongbi"] = $table_arr['八、月度计划']['产量']['lg']["thismon"] - $table_arr['八、月度计划']['产量']['lg']["lastmon"];
		$table_arr['八、月度计划']['产量']['hg']["tongbi"] = $table_arr['八、月度计划']['产量']['hg']["thismon"] - $table_arr['八、月度计划']['产量']['hg']["lastmon"];

		// echo"<pre>";print_r($yxslj_jt);

		$table_arr['六、其他']['检斤检尺差异']['lg']['lastmon'] = 20;
		$table_arr['六、其他']['检斤检尺差异']['hg']['lastmon'] = 40;
		$table_arr['六、其他']['检斤检尺差异']['lg']['thismon'] = 20;
		$table_arr['六、其他']['检斤检尺差异']['hg']['thismon'] = 40;
		$table_arr['六、其他']['检斤检尺差异']['lg']["today"] = 20;
		$table_arr['六、其他']['检斤检尺差异']['hg']["today"] = 40;
		$table_arr['六、其他']['检斤检尺差异']['lg']["yesterday"] = 20;
		$table_arr['六、其他']['检斤检尺差异']['hg']["yesterday"] = 40;
		$table_arr['六、其他']['检斤检尺差异']['lg']["huanbi"] = $table_arr['六、其他']['检斤检尺差异']['lg']["today"] - $table_arr['六、其他']['检斤检尺差异']['lg']["yesterday"];
		$table_arr['六、其他']['检斤检尺差异']['hg']["huanbi"] = $table_arr['六、其他']['检斤检尺差异']['hg']["today"] - $table_arr['六、其他']['检斤检尺差异']['hg']["yesterday"];
		$table_arr['六、其他']['检斤检尺差异']['lg']["tongbi"] = $table_arr['六、其他']['检斤检尺差异']['lg']["thismon"] - $table_arr['六、其他']['检斤检尺差异']['lg']["lastmon"];
		$table_arr['六、其他']['检斤检尺差异']['hg']["tongbi"] = $table_arr['六、其他']['检斤检尺差异']['hg']["thismon"] - $table_arr['六、其他']['检斤检尺差异']['hg']["lastmon"];
		$table_arr['六、其他']['税金及附加等']['lg']['lastmon'] = 20;
		$table_arr['六、其他']['税金及附加等']['hg']['lastmon'] = 40;
		$table_arr['六、其他']['税金及附加等']['lg']['thismon'] = 20;
		$table_arr['六、其他']['税金及附加等']['hg']['thismon'] = 40;
		$table_arr['六、其他']['税金及附加等']['lg']["today"] = 20;
		$table_arr['六、其他']['税金及附加等']['hg']["today"] = 40;
		$table_arr['六、其他']['税金及附加等']['lg']["yesterday"] = 20;
		$table_arr['六、其他']['税金及附加等']['hg']["yesterday"] = 40;
		$table_arr['六、其他']['税金及附加等']['lg']["huanbi"] = $table_arr['六、其他']['税金及附加等']['lg']["today"] - $table_arr['六、其他']['税金及附加等']['lg']["yesterday"];
		$table_arr['六、其他']['税金及附加等']['hg']["huanbi"] = $table_arr['六、其他']['税金及附加等']['hg']["today"] - $table_arr['六、其他']['税金及附加等']['hg']["yesterday"];
		$table_arr['六、其他']['税金及附加等']['lg']["tongbi"] = $table_arr['六、其他']['税金及附加等']['lg']["thismon"] - $table_arr['六、其他']['税金及附加等']['lg']["lastmon"];
		$table_arr['六、其他']['税金及附加等']['hg']["tongbi"] = $table_arr['六、其他']['税金及附加等']['hg']["thismon"] - $table_arr['六、其他']['税金及附加等']['hg']["lastmon"];
		$table_arr['六、其他']['韩城公司结算30元']['lg']['lastmon'] = 26;
		$table_arr['六、其他']['韩城公司结算30元']['hg']['lastmon'] = 26;
		$table_arr['六、其他']['韩城公司结算30元']['lg']['thismon'] = 26;
		$table_arr['六、其他']['韩城公司结算30元']['hg']['thismon'] = 26;
		$table_arr['六、其他']['韩城公司结算30元']['lg']["today"] = 26;
		$table_arr['六、其他']['韩城公司结算30元']['hg']["today"] = 26;
		$table_arr['六、其他']['韩城公司结算30元']['lg']["yesterday"] = 26;
		$table_arr['六、其他']['韩城公司结算30元']['hg']["yesterday"] = 26;
		$table_arr['六、其他']['韩城公司结算30元']['lg']["huanbi"] = $table_arr['六、其他']['韩城公司结算30元']['lg']["today"] - $table_arr['六、其他']['韩城公司结算30元']['lg']["yesterday"];
		$table_arr['六、其他']['韩城公司结算30元']['hg']["huanbi"] = $table_arr['六、其他']['韩城公司结算30元']['hg']["today"] - $table_arr['六、其他']['韩城公司结算30元']['hg']["yesterday"];
		$table_arr['六、其他']['韩城公司结算30元']['lg']["tongbi"] = $table_arr['六、其他']['韩城公司结算30元']['lg']["thismon"] - $table_arr['六、其他']['韩城公司结算30元']['lg']["lastmon"];
		$table_arr['六、其他']['韩城公司结算30元']['hg']["tongbi"] = $table_arr['六、其他']['韩城公司结算30元']['hg']["thismon"] - $table_arr['六、其他']['韩城公司结算30元']['hg']["lastmon"];

		$table_arr['三、售价']['一级售价<br>（不含税不含运费）']['lg']['lastmon'] = round(($table_arr['三、售价']['二级售价<br>（含税含运费）']['lg']['lastmon']-$table_arr['三、售价']['运费（含税）']['lg']['lastmon'])/1.13,2);
		$table_arr['三、售价']['一级售价<br>（不含税不含运费）']['hg']['lastmon'] = round(($table_arr['三、售价']['二级售价<br>（含税含运费）']['hg']['lastmon']-$table_arr['三、售价']['运费（含税）']['hg']['lastmon'])/1.13,2);
		$table_arr['三、售价']['一级售价<br>（不含税不含运费）']['lg']['thismon'] = round(($table_arr['三、售价']['二级售价<br>（含税含运费）']['lg']['thismon']-$table_arr['三、售价']['运费（含税）']['lg']['thismon'])/1.13,2);
		$table_arr['三、售价']['一级售价<br>（不含税不含运费）']['hg']['thismon'] = round(($table_arr['三、售价']['二级售价<br>（含税含运费）']['hg']['thismon']-$table_arr['三、售价']['运费（含税）']['hg']['thismon'])/1.13,2);
		$table_arr['三、售价']['一级售价<br>（不含税不含运费）']['lg']["today"] = round(($table_arr['三、售价']['二级售价<br>（含税含运费）']['lg']['today']-$table_arr['三、售价']['运费（含税）']['lg']['today'])/1.13,2);
		$table_arr['三、售价']['一级售价<br>（不含税不含运费）']['hg']["today"] = round(($table_arr['三、售价']['二级售价<br>（含税含运费）']['hg']['today']-$table_arr['三、售价']['运费（含税）']['hg']['today'])/1.13,2);
		$table_arr['三、售价']['一级售价<br>（不含税不含运费）']['lg']["yesterday"] = round(($table_arr['三、售价']['二级售价<br>（含税含运费）']['lg']['yesterday']-$table_arr['三、售价']['运费（含税）']['lg']['yesterday'])/1.13,2);
		$table_arr['三、售价']['一级售价<br>（不含税不含运费）']['hg']["yesterday"] = round(($table_arr['三、售价']['二级售价<br>（含税含运费）']['hg']['yesterday']-$table_arr['三、售价']['运费（含税）']['hg']['yesterday'])/1.13,2);
		$table_arr['三、售价']['一级售价<br>（不含税不含运费）']['lg']["huanbi"] = $table_arr['三、售价']['一级售价<br>（不含税不含运费）']['lg']["today"] - $table_arr['三、售价']['一级售价<br>（不含税不含运费）']['lg']["yesterday"];
		$table_arr['三、售价']['一级售价<br>（不含税不含运费）']['hg']["huanbi"] = $table_arr['三、售价']['一级售价<br>（不含税不含运费）']['hg']["today"] - $table_arr['三、售价']['一级售价<br>（不含税不含运费）']['hg']["yesterday"];
		$table_arr['三、售价']['一级售价<br>（不含税不含运费）']['lg']["tongbi"] = $table_arr['三、售价']['一级售价<br>（不含税不含运费）']['lg']["thismon"] - $table_arr['三、售价']['一级售价<br>（不含税不含运费）']['lg']["lastmon"];
		$table_arr['三、售价']['一级售价<br>（不含税不含运费）']['hg']["tongbi"] = $table_arr['三、售价']['一级售价<br>（不含税不含运费）']['hg']["thismon"] - $table_arr['三、售价']['一级售价<br>（不含税不含运费）']['hg']["lastmon"];

		$table_arr['五、期间费用']['销售费用']['lg']['lastmon'] = round($table_arr['八、月度计划']['销售费用']['lg']['lastmon']/$table_arr['一、产量']['钢坯产量']['lg']['lastmon'],2);
		$table_arr['五、期间费用']['销售费用']['hg']['lastmon'] = round($table_arr['八、月度计划']['销售费用']['hg']['lastmon']/$table_arr['一、产量']['钢坯产量']['hg']['lastmon'],2);
		$table_arr['五、期间费用']['销售费用']['lg']['thismon'] = round($table_arr['八、月度计划']['销售费用']['lg']['thismon']/$table_arr['八、月度计划']['产量']['lg']['thismon'],2);
		$table_arr['五、期间费用']['销售费用']['hg']['thismon'] = round($table_arr['八、月度计划']['销售费用']['hg']['thismon']/$table_arr['八、月度计划']['产量']['hg']['thismon'],2);
		$table_arr['五、期间费用']['销售费用']['lg']["today"] = round($table_arr['八、月度计划']['销售费用']['lg']['today']/$table_arr['八、月度计划']['产量']['lg']['today'],2);
		$table_arr['五、期间费用']['销售费用']['hg']["today"] = round($table_arr['八、月度计划']['销售费用']['hg']['today']/$table_arr['八、月度计划']['产量']['hg']['today'],2);
		$table_arr['五、期间费用']['销售费用']['lg']["yesterday"] = round($table_arr['八、月度计划']['销售费用']['lg']['yesterday']/$table_arr['八、月度计划']['产量']['lg']['yesterday'],2);
		$table_arr['五、期间费用']['销售费用']['hg']["yesterday"] = round($table_arr['八、月度计划']['销售费用']['hg']['yesterday']/$table_arr['八、月度计划']['产量']['hg']['yesterday'],2);
		$table_arr['五、期间费用']['销售费用']['lg']["huanbi"] = $table_arr['五、期间费用']['销售费用']['lg']["today"] - $table_arr['五、期间费用']['销售费用']['lg']["yesterday"];
		$table_arr['五、期间费用']['销售费用']['hg']["huanbi"] = $table_arr['五、期间费用']['销售费用']['hg']["today"] - $table_arr['五、期间费用']['销售费用']['hg']["yesterday"];
		$table_arr['五、期间费用']['销售费用']['lg']["tongbi"] = $table_arr['五、期间费用']['销售费用']['lg']["thismon"] - $table_arr['五、期间费用']['销售费用']['lg']["lastmon"];
		$table_arr['五、期间费用']['销售费用']['hg']["tongbi"] = $table_arr['五、期间费用']['销售费用']['hg']["thismon"] - $table_arr['五、期间费用']['销售费用']['hg']["lastmon"];

		$table_arr['五、期间费用']['管理费用']['lg']['lastmon'] = round($table_arr['八、月度计划']['管理费用']['lg']['lastmon']/$table_arr['一、产量']['钢坯产量']['lg']['lastmon'],2);
		$table_arr['五、期间费用']['管理费用']['hg']['lastmon'] = round($table_arr['八、月度计划']['管理费用']['hg']['lastmon']/$table_arr['一、产量']['钢坯产量']['hg']['lastmon'],2);
		$table_arr['五、期间费用']['管理费用']['lg']['thismon'] = round($table_arr['八、月度计划']['管理费用']['lg']['thismon']/$table_arr['八、月度计划']['产量']['lg']['thismon'],2);
		$table_arr['五、期间费用']['管理费用']['hg']['thismon'] = round($table_arr['八、月度计划']['管理费用']['hg']['thismon']/$table_arr['八、月度计划']['产量']['hg']['thismon'],2);
		$table_arr['五、期间费用']['管理费用']['lg']["today"] = round($table_arr['八、月度计划']['管理费用']['lg']['today']/$table_arr['八、月度计划']['产量']['lg']['today'],2);
		$table_arr['五、期间费用']['管理费用']['hg']["today"] = round($table_arr['八、月度计划']['管理费用']['hg']['today']/$table_arr['八、月度计划']['产量']['hg']['today'],2);
		$table_arr['五、期间费用']['管理费用']['lg']["yesterday"] = round($table_arr['八、月度计划']['管理费用']['lg']['yesterday']/$table_arr['八、月度计划']['产量']['lg']['yesterday'],2);
		$table_arr['五、期间费用']['管理费用']['hg']["yesterday"] =  round($table_arr['八、月度计划']['管理费用']['hg']['yesterday']/$table_arr['八、月度计划']['产量']['hg']['yesterday'],2);
		$table_arr['五、期间费用']['管理费用']['lg']["huanbi"] = $table_arr['五、期间费用']['管理费用']['lg']["today"] - $table_arr['五、期间费用']['管理费用']['lg']["yesterday"];
		$table_arr['五、期间费用']['管理费用']['hg']["huanbi"] = $table_arr['五、期间费用']['管理费用']['hg']["today"] - $table_arr['五、期间费用']['管理费用']['hg']["yesterday"];
		$table_arr['五、期间费用']['管理费用']['lg']["tongbi"] = $table_arr['五、期间费用']['管理费用']['lg']["thismon"] - $table_arr['五、期间费用']['管理费用']['lg']["lastmon"];
		$table_arr['五、期间费用']['管理费用']['hg']["tongbi"] = $table_arr['五、期间费用']['管理费用']['hg']["thismon"] - $table_arr['五、期间费用']['管理费用']['hg']["lastmon"];

		$table_arr['五、期间费用']['财务费用']['lg']['lastmon'] = round($table_arr['八、月度计划']['财务费用']['lg']['lastmon']/$table_arr['一、产量']['钢坯产量']['lg']['lastmon'],2);
		$table_arr['五、期间费用']['财务费用']['hg']['lastmon'] = round($table_arr['八、月度计划']['财务费用']['hg']['lastmon']/$table_arr['一、产量']['钢坯产量']['hg']['lastmon'],2);
		$table_arr['五、期间费用']['财务费用']['lg']['thismon'] = round($table_arr['八、月度计划']['财务费用']['lg']['thismon']/$table_arr['八、月度计划']['产量']['lg']['thismon'],2);
		$table_arr['五、期间费用']['财务费用']['hg']['thismon'] = round($table_arr['八、月度计划']['财务费用']['hg']['thismon']/$table_arr['八、月度计划']['产量']['hg']['thismon'],2);
		$table_arr['五、期间费用']['财务费用']['lg']["today"] = round($table_arr['八、月度计划']['财务费用']['lg']['today']/$table_arr['八、月度计划']['产量']['lg']['today'],2);
		$table_arr['五、期间费用']['财务费用']['hg']["today"] = round($table_arr['八、月度计划']['财务费用']['hg']['today']/$table_arr['八、月度计划']['产量']['hg']['today'],2);
		$table_arr['五、期间费用']['财务费用']['lg']["yesterday"] = round($table_arr['八、月度计划']['财务费用']['lg']['yesterday']/$table_arr['八、月度计划']['产量']['lg']['yesterday'],2);
		$table_arr['五、期间费用']['财务费用']['hg']["yesterday"] =  round($table_arr['八、月度计划']['财务费用']['hg']['yesterday']/$table_arr['八、月度计划']['产量']['hg']['yesterday'],2);
		$table_arr['五、期间费用']['财务费用']['lg']["huanbi"] = $table_arr['五、期间费用']['财务费用']['lg']["today"] - $table_arr['五、期间费用']['财务费用']['lg']["yesterday"];
		$table_arr['五、期间费用']['财务费用']['hg']["huanbi"] = $table_arr['五、期间费用']['财务费用']['hg']["today"] - $table_arr['五、期间费用']['财务费用']['hg']["yesterday"];
		$table_arr['五、期间费用']['财务费用']['lg']["tongbi"] = $table_arr['五、期间费用']['财务费用']['lg']["thismon"] - $table_arr['五、期间费用']['财务费用']['lg']["lastmon"];
		$table_arr['五、期间费用']['财务费用']['hg']["tongbi"] = $table_arr['五、期间费用']['财务费用']['hg']["thismon"] - $table_arr['五、期间费用']['财务费用']['hg']["lastmon"];

		$table_arr['四、成本']['吨钢期间费用']['lg']['lastmon'] = round($table_arr['八、月度计划']['合计']['lg']['lastmon']/$table_arr['一、产量']['钢坯产量']['lg']['lastmon'],2);
		$table_arr['四、成本']['吨钢期间费用']['hg']['lastmon'] = round($table_arr['八、月度计划']['合计']['hg']['lastmon']/$table_arr['一、产量']['钢坯产量']['hg']['lastmon'],2);
		$table_arr['四、成本']['吨钢期间费用']['lg']['thismon'] = $table_arr['五、期间费用']['销售费用']['lg']['thismon']+$table_arr['五、期间费用']['管理费用']['lg']['thismon']+$table_arr['五、期间费用']['财务费用']['lg']['thismon'];
		$table_arr['四、成本']['吨钢期间费用']['hg']['thismon'] = $table_arr['五、期间费用']['销售费用']['hg']['thismon']+$table_arr['五、期间费用']['管理费用']['hg']['thismon']+$table_arr['五、期间费用']['财务费用']['hg']['thismon'];
		$table_arr['四、成本']['吨钢期间费用']['lg']["today"] = $table_arr['五、期间费用']['销售费用']['lg']['today']+$table_arr['五、期间费用']['管理费用']['lg']['today']+$table_arr['五、期间费用']['财务费用']['lg']['today'];
		$table_arr['四、成本']['吨钢期间费用']['hg']["today"] = $table_arr['五、期间费用']['销售费用']['hg']['today']+$table_arr['五、期间费用']['管理费用']['hg']['today']+$table_arr['五、期间费用']['财务费用']['hg']['today'];
		$table_arr['四、成本']['吨钢期间费用']['lg']["yesterday"] = $table_arr['五、期间费用']['销售费用']['lg']['yesterday']+$table_arr['五、期间费用']['管理费用']['lg']['yesterday']+$table_arr['五、期间费用']['财务费用']['lg']['yesterday'];
		$table_arr['四、成本']['吨钢期间费用']['hg']["yesterday"] = $table_arr['五、期间费用']['销售费用']['hg']['yesterday']+$table_arr['五、期间费用']['管理费用']['hg']['yesterday']+$table_arr['五、期间费用']['财务费用']['hg']['yesterday'];
		$table_arr['四、成本']['吨钢期间费用']['lg']["huanbi"] = $table_arr['四、成本']['吨钢期间费用']['lg']["today"] - $table_arr['四、成本']['吨钢期间费用']['lg']["yesterday"];
		$table_arr['四、成本']['吨钢期间费用']['hg']["huanbi"] = $table_arr['四、成本']['吨钢期间费用']['hg']["today"] - $table_arr['四、成本']['吨钢期间费用']['hg']["yesterday"];
		$table_arr['四、成本']['吨钢期间费用']['lg']["tongbi"] = $table_arr['四、成本']['吨钢期间费用']['lg']["thismon"] - $table_arr['四、成本']['吨钢期间费用']['lg']["lastmon"];
		$table_arr['四、成本']['吨钢期间费用']['hg']["tongbi"] = $table_arr['四、成本']['吨钢期间费用']['hg']["thismon"] - $table_arr['四、成本']['吨钢期间费用']['hg']["lastmon"];

		$table_arr['四、成本']['完全成本']['lg']['lastmon'] = $table_arr['四、成本']['生产成本']['lg']['lastmon']+$table_arr['四、成本']['吨钢期间费用']['lg']['lastmon'];
		$table_arr['四、成本']['完全成本']['hg']['lastmon'] = $table_arr['四、成本']['生产成本']['hg']['lastmon']+$table_arr['四、成本']['吨钢期间费用']['hg']['lastmon'];
		$table_arr['四、成本']['完全成本']['lg']['thismon'] = $table_arr['四、成本']['生产成本']['lg']['thismon']+$table_arr['四、成本']['吨钢期间费用']['lg']['thismon'];
		$table_arr['四、成本']['完全成本']['hg']['thismon'] = $table_arr['四、成本']['生产成本']['hg']['thismon']+$table_arr['四、成本']['吨钢期间费用']['hg']['thismon'];
		$table_arr['四、成本']['完全成本']['lg']["today"] = $table_arr['四、成本']['生产成本']['lg']['today']+$table_arr['四、成本']['吨钢期间费用']['lg']['today'];
		$table_arr['四、成本']['完全成本']['hg']["today"] = $table_arr['四、成本']['生产成本']['hg']['today']+$table_arr['四、成本']['吨钢期间费用']['hg']['today'];
		$table_arr['四、成本']['完全成本']['lg']["yesterday"] = $table_arr['四、成本']['生产成本']['lg']['yesterday']+$table_arr['四、成本']['吨钢期间费用']['lg']['yesterday'];
		$table_arr['四、成本']['完全成本']['hg']["yesterday"] = $table_arr['四、成本']['生产成本']['hg']['yesterday']+$table_arr['四、成本']['吨钢期间费用']['hg']['yesterday'];
		$table_arr['四、成本']['完全成本']['lg']["huanbi"] = $table_arr['四、成本']['完全成本']['lg']["today"] - $table_arr['四、成本']['完全成本']['lg']["yesterday"];
		$table_arr['四、成本']['完全成本']['hg']["huanbi"] = $table_arr['四、成本']['完全成本']['hg']["today"] - $table_arr['四、成本']['完全成本']['hg']["yesterday"];
		$table_arr['四、成本']['完全成本']['lg']["tongbi"] = $table_arr['四、成本']['完全成本']['lg']["thismon"] - $table_arr['四、成本']['完全成本']['lg']["lastmon"];
		$table_arr['四、成本']['完全成本']['hg']["tongbi"] = $table_arr['四、成本']['完全成本']['hg']["thismon"] - $table_arr['四、成本']['完全成本']['hg']["lastmon"];


		$table_arr['七、利润']['吨钢利润']['lg']['lastmon'] = $table_arr['三、售价']['一级售价<br>（不含税不含运费）']['lg']["lastmon"] - $table_arr['四、成本']['完全成本']['lg']["lastmon"] - $table_arr['六、其他']['检斤检尺差异']['lg']["lastmon"] - $table_arr['六、其他']['税金及附加等']['lg']["lastmon"] - $table_arr['六、其他']['韩城公司结算30元']['lg']["lastmon"] + 39;

		$table_arr['七、利润']['吨钢利润']['hg']['lastmon'] = $table_arr['三、售价']['一级售价<br>（不含税不含运费）']['hg']["lastmon"] - $table_arr['四、成本']['完全成本']['hg']["lastmon"] - $table_arr['六、其他']['检斤检尺差异']['hg']["lastmon"] - $table_arr['六、其他']['税金及附加等']['hg']["lastmon"] - $table_arr['六、其他']['韩城公司结算30元']['hg']["lastmon"] - 5;

		$table_arr['七、利润']['吨钢利润']['lg']['thismon'] = $table_arr['三、售价']['一级售价<br>（不含税不含运费）']['lg']["thismon"] - $table_arr['四、成本']['完全成本']['lg']["thismon"] - $table_arr['六、其他']['检斤检尺差异']['lg']["thismon"] - $table_arr['六、其他']['税金及附加等']['lg']["thismon"] - $table_arr['六、其他']['韩城公司结算30元']['lg']["thismon"];

		$table_arr['七、利润']['吨钢利润']['hg']['thismon'] = $table_arr['三、售价']['一级售价<br>（不含税不含运费）']['hg']["thismon"] - $table_arr['四、成本']['完全成本']['hg']["thismon"] - $table_arr['六、其他']['检斤检尺差异']['hg']["thismon"] - $table_arr['六、其他']['税金及附加等']['hg']["thismon"] - $table_arr['六、其他']['韩城公司结算30元']['hg']["thismon"];

		$table_arr['七、利润']['吨钢利润']['lg']["today"] = $table_arr['三、售价']['一级售价<br>（不含税不含运费）']['lg']["today"] - $table_arr['四、成本']['完全成本']['lg']["today"] - $table_arr['六、其他']['检斤检尺差异']['lg']["today"] - $table_arr['六、其他']['税金及附加等']['lg']["today"] - $table_arr['六、其他']['韩城公司结算30元']['lg']["today"];

		$table_arr['七、利润']['吨钢利润']['hg']["today"] = $table_arr['三、售价']['一级售价<br>（不含税不含运费）']['hg']["today"] - $table_arr['四、成本']['完全成本']['hg']["today"] - $table_arr['六、其他']['检斤检尺差异']['hg']["today"] - $table_arr['六、其他']['税金及附加等']['hg']["today"] - $table_arr['六、其他']['韩城公司结算30元']['hg']["today"];

		$table_arr['七、利润']['吨钢利润']['lg']["yesterday"] = $table_arr['三、售价']['一级售价<br>（不含税不含运费）']['lg']["yesterday"] - $table_arr['四、成本']['完全成本']['lg']["yesterday"] - $table_arr['六、其他']['检斤检尺差异']['lg']["yesterday"] - $table_arr['六、其他']['税金及附加等']['lg']["yesterday"] - $table_arr['六、其他']['韩城公司结算30元']['lg']["yesterday"];

		$table_arr['七、利润']['吨钢利润']['hg']["yesterday"] = $table_arr['三、售价']['一级售价<br>（不含税不含运费）']['hg']["yesterday"] - $table_arr['四、成本']['完全成本']['hg']["yesterday"] - $table_arr['六、其他']['检斤检尺差异']['hg']["yesterday"] - $table_arr['六、其他']['税金及附加等']['hg']["yesterday"] - $table_arr['六、其他']['韩城公司结算30元']['hg']["yesterday"];
		$table_arr['七、利润']['吨钢利润']['lg']["huanbi"] = $table_arr['七、利润']['吨钢利润']['lg']["today"] - $table_arr['七、利润']['吨钢利润']['lg']["yesterday"];
		$table_arr['七、利润']['吨钢利润']['hg']["huanbi"] = $table_arr['七、利润']['吨钢利润']['hg']["today"] - $table_arr['七、利润']['吨钢利润']['hg']["yesterday"];
		$table_arr['七、利润']['吨钢利润']['lg']["tongbi"] = $table_arr['七、利润']['吨钢利润']['lg']["thismon"] - $table_arr['七、利润']['吨钢利润']['lg']["lastmon"];
		$table_arr['七、利润']['吨钢利润']['hg']["tongbi"] = $table_arr['七、利润']['吨钢利润']['hg']["thismon"] - $table_arr['七、利润']['吨钢利润']['hg']["lastmon"];


		$table_arr['七、利润']['利润总额']['lg']['lastmon'] = $table_arr['七、利润']['吨钢利润']['lg']["lastmon"] * $table_arr['二、销量']['钢材结算销量']['lg']["lastmon"];
		$table_arr['七、利润']['利润总额']['hg']['lastmon'] = $table_arr['七、利润']['吨钢利润']['hg']["lastmon"] * $table_arr['二、销量']['钢材结算销量']['hg']["lastmon"];
		$table_arr['七、利润']['利润总额']['lg']['thismon'] = $table_arr['七、利润']['吨钢利润']['lg']["thismon"] * $table_arr['二、销量']['钢材结算销量']['lg']["thismon"];
		$table_arr['七、利润']['利润总额']['hg']['thismon'] = $table_arr['七、利润']['吨钢利润']['hg']["thismon"] * $table_arr['二、销量']['钢材结算销量']['hg']["thismon"];
		$table_arr['七、利润']['利润总额']['lg']["today"] = $table_arr['七、利润']['吨钢利润']['lg']["today"] * $table_arr['二、销量']['钢材结算销量']['lg']["today"];
		$table_arr['七、利润']['利润总额']['hg']["today"] = $table_arr['七、利润']['吨钢利润']['hg']["today"] * $table_arr['二、销量']['钢材结算销量']['hg']["today"];
		$table_arr['七、利润']['利润总额']['lg']["yesterday"] = $table_arr['七、利润']['吨钢利润']['lg']["yesterday"] * $table_arr['二、销量']['钢材结算销量']['lg']["yesterday"];
		$table_arr['七、利润']['利润总额']['hg']["yesterday"] = $table_arr['七、利润']['吨钢利润']['hg']["yesterday"] * $table_arr['二、销量']['钢材结算销量']['hg']["yesterday"];
		$table_arr['七、利润']['利润总额']['lg']["huanbi"] = $table_arr['七、利润']['利润总额']['lg']["today"] - $table_arr['七、利润']['利润总额']['lg']["yesterday"];
		$table_arr['七、利润']['利润总额']['hg']["huanbi"] = $table_arr['七、利润']['利润总额']['hg']["today"] - $table_arr['七、利润']['利润总额']['hg']["yesterday"];
		$table_arr['七、利润']['利润总额']['lg']["tongbi"] = $table_arr['七、利润']['利润总额']['lg']["thismon"] - $table_arr['七、利润']['利润总额']['lg']["lastmon"];
		$table_arr['七、利润']['利润总额']['hg']["tongbi"] = $table_arr['七、利润']['利润总额']['hg']["thismon"] - $table_arr['七、利润']['利润总额']['hg']["lastmon"];

		$this->assign("table_arr",$table_arr);
	}

	function getday($qdate, $table, $vtype){
		$id_arr = $GLOBALS ['jiageid_arr'];
		if( $table == "SJCGIndex" ){
			$sqldate2="select Date from SJCGIndex where Date<='$qdate' and Type='$vtype' and DType='0' and Status=1 order by Date desc limit 1";
			$qdate = $this->homeDao->getone($sqldate2);
		} elseif ($table == "marketconditions"){
			$sqldate2="select mconmanagedate from marketconditions where mastertopid='$id_arr[$vtype]' and mconmanagedate<='$qdate 23:59:59' order by mconmanagedate desc limit 1";
			$qdate = $this->homeDao->getone($sqldate2);
		} elseif ($table == "sg_data_table"){
			$sqldate2="SELECT dta_ym FROM `sg_data_table` WHERE `dta_type` = 'TEST_ERP_ZHJSJ' AND `dta_ym` <= '$qdate' AND `dta_1` = '$vtype' and dta_3='合计' order by dta_ym desc limit 1";
			$qdate = $this->_dao->getone($sqldate2);
		}
		// echo $sqldate2."<br>";
		
		$qdate = date("Y-m-d",strtotime ($qdate));
		return $qdate;
	}

	function getpreday($qdate,$vtype){
		$sqldate2="select Date from SJCGIndex where Date<'$qdate' and Type='$vtype' and DType='0' and Status=1 order by Date desc limit 1";
		// echo $sqldate2;
		$qdate = $this->homeDao->getone($sqldate2);		
		$qdate = date("Y-m-d",strtotime ($qdate));
		return $qdate;
	}

	function getzhangdie($val1,$val2){
		$cha = round($val1 - $val2,2);
		if ($cha>0) {
			$zhangdie = '<font color="red">↑'.abs($cha).'</font>';
		} elseif ($cha<0) {
			$zhangdie = '<font color="green">↓'.abs($cha).'</font>';
		} elseif ($cha==0) {
			$zhangdie = '--';
		}
		return $zhangdie;
	}

	function pi_bj($arr_date, $vtype){
		$city_arr = $GLOBALS ['city_arr'];
		//本平均价
		$sql1="select CityName,avg(`index`) as bawp from SJCGIndex where Date>='".$arr_date["bs"]."' and Date<='".$arr_date["be"]."' and Type='$vtype' and DType='0' and Status=1 and CityName in ('西安','郑州','成都','重庆','兰州') group by CityName";
		//上平均价
		$sql2="select CityName,avg(`index`) as sawp from SJCGIndex where Date>='".$arr_date["ss"]."' and Date<='".$arr_date["se"]."' and Type='$vtype' and DType='0' and Status=1 and CityName in ('西安','郑州','成都','重庆','兰州') group by CityName";
		// echo $sql1."<br>".$sql2."<br>";
		$binfo=$this->homeDao->query($sql1);
		$sinfo=$this->homeDao->query($sql2);
		$binfo2 = array();
		foreach($binfo as $k => $v){
			$binfo2[$v['CityName']] = $v['bawp'];
		}
		$sinfo2 = array();
		foreach($sinfo as $k => $v){
			$sinfo2[$v['CityName']] = $v['sawp'];
		}
		// echo"<pre>";print_r($binfo2);exit;
		$bi_arr = array();
		foreach($binfo2 as $k => $v){
			$bavpi=$v;
			$savpi=$sinfo2[$k];
			if($savpi != 0)
			$bp=round(($bavpi-$savpi),2);
			else
			$bp="&nbsp;";
			if($bp == '-0'){
				$bp = 0;
			}
			if ($bp>0) {
				$zhangdie = '<font color="red">↑'.abs($bp).'</font>';
			} elseif ($bp<0) {
				$zhangdie = '<font color="green">↓'.abs($bp).'</font>';
			} elseif ($bp==0) {
				$zhangdie = '--';
			} else {
				$zhangdie = $bp;
			}
			$bi_arr[$k] = $zhangdie;
		}
		return $bi_arr;
	}

	function pi_bj2($arr_date, $vtype){
		$jiageid_arr = $GLOBALS ['jiageid_arr'];
		//本平均价
		$strlen = strlen($jiageid_arr[$vtype]);
		if ($strlen == 6) {
			$jiage = "topicture";
		} elseif ($strlen == 7) {
			$jiage = "mastertopid";
		}
		$sql1="select avg(`price`) as bawp from marketconditions where mconmanagedate>='".$arr_date["bs"]." 00:00:00' and mconmanagedate<='".$arr_date["be"]." 23:59:59' and marketconditions.$jiage='$jiageid_arr[$vtype]'";
		//上平均价
		$sql2="select avg(`price`) as sawp from marketconditions where mconmanagedate>='".$arr_date["ss"]." 00:00:00' and mconmanagedate<='".$arr_date["se"]." 23:59:59' and marketconditions.$jiage='$jiageid_arr[$vtype]'";
		// echo $sql1."<br>".$sql2."<br>"; 
		$bavpi=$this->homeDao->getOne($sql1);
		$savpi=$this->homeDao->getOne($sql2);
		
		if($savpi != 0)
        $bp=round(($bavpi-$savpi),2);
		else
		$bp="&nbsp;";
		if($bp == '-0'){
			$bp = 0;
		}
		if ($bp>0) {
			$zhangdie = '<font color="red">↑'.abs($bp).'</font>';
		} elseif ($bp<0) {
			$zhangdie = '<font color="green">↓'.abs($bp).'</font>';
		} elseif ($bp==0) {
			$zhangdie = '--';
		} else {
			$zhangdie = $bp;
		}
		return $zhangdie;
	}

	function pi_bj4($arr_date, $vtype){
		//本平均价
		$sql1="select dta_2,avg(`dta_6`) as bawp from sg_data_table where `dta_type`='TEST_ERP_ZHJSJ' AND dta_ym>='".$arr_date["bs"]."' and dta_ym<='".$arr_date["be"]."' and dta_3='合计' and dta_1='$vtype' group by dta_2";
		//上平均价
		$sql2="select dta_2,avg(`dta_6`) as sawp from sg_data_table where `dta_type`='TEST_ERP_ZHJSJ' AND dta_ym>='".$arr_date["ss"]."' and dta_ym<='".$arr_date["se"]."' and dta_3='合计' and dta_1='$vtype' group by dta_2";
		// echo $sql1."<br>".$sql2."<br>"; 
		$bavpi_arr=$this->_dao->query($sql1);
		$savpi_arr=$this->_dao->query($sql2);
		// echo"<pre>";print_r($bavpi_arr);exit;

		foreach ($savpi_arr as $v) {
			$savpi_arr2[$v['dta_2']] = $v['sawp'];
		}

		$bilv = array();
		foreach ($bavpi_arr as $v) {
			$bavpi = $v['bawp'];
			$savpi = $savpi_arr2[$v['dta_2']];
			if($savpi != 0)
			$bp=round(($bavpi-$savpi),2);
			else
			$bp="&nbsp;";
			if($bp == '-0'){
				$bp = 0;
			}
			if ($bp>0) {
				$zhangdie = '<font color="red">↑'.abs($bp).'</font>';
			} elseif ($bp<0) {
				$zhangdie = '<font color="green">↓'.abs($bp).'</font>';
			} elseif ($bp==0) {
				$zhangdie = '--';
			} else {
				$zhangdie = $bp;
			}
			$bilv[$v['dta_2']] = $zhangdie;
		}
			
		return $bilv;
	}

	function getpb($bp){
		if($bp == '-0'){
			$bp = 0;
		}
		if ($bp>0) {
			$zhangdie = '<font color="red">↑'.abs($bp).'</font>';
		} elseif ($bp<0) {
			$zhangdie = '<font color="green">↓'.abs($bp).'</font>';
		} elseif ($bp==0) {
			$zhangdie = '--';
		} else {
			$zhangdie = $bp;
		}
		if($bp == '&nbsp;'){
			$zhangdie = '&nbsp;';
		}
		return $zhangdie;
	}

	function pi_bj5($arr_date){
		//本平均价
		$sql1="select avg(`dta_1`) as dta_1, avg(`dta_2`) as dta_2, avg(`dta_3`) as dta_3 from sg_data_table where dta_type = 'SG_BFJGHZ' and dta_ym>='".$arr_date["bs"]."' and dta_ym<='".$arr_date["be"]."'";
		//上平均价
		$sql2="select avg(`dta_1`) as dta_1, avg(`dta_2`) as dta_2, avg(`dta_3`) as dta_3 from sg_data_table where dta_type = 'SG_BFJGHZ' and dta_ym>='".$arr_date["ss"]."' and dta_ym<='".$arr_date["se"]."'";
		// echo $sql1."<br>".$sql2."<br>"; 
		$bavpi=$this->_dao->getrow($sql1);
		$savpi=$this->_dao->getrow($sql2);
		// print_r($bavpi);
		
		if($savpi["dta_1"] != 0)
        $bp_1=round(($bavpi["dta_1"]-$savpi["dta_1"]),2);
		else
		$bp_1="&nbsp;";
		if($savpi["dta_2"] != 0)
        $bp_2=round(($bavpi["dta_2"]-$savpi["dta_2"]),2);
		else
		$bp_2="&nbsp;";
		if($savpi["dta_3"] != 0)
        $bp_3=round(($bavpi["dta_3"]-$savpi["dta_3"]),2);
		else
		$bp_3="&nbsp;";
		// echo $bp_3."<br>";

		$pi_biinfo = array();
		$pi_biinfo['bp_1'] = $this->getpb($bp_1);
		$pi_biinfo['bp_2'] = $this->getpb($bp_2);
		$pi_biinfo['bp_3'] = $this->getpb($bp_3);
		return $pi_biinfo;
	}

	function pi_bj_shuju($arr_date, $value_zd, $table, $where, $date_zd){
		//本平均价
		$sql1="select avg(`$value_zd`) as bawp from $table $where AND $date_zd>='".$arr_date["bs"]."' and $date_zd<='".$arr_date["be"]."'";
		//上平均价
		$sql2="select avg(`$value_zd`) as sawp from $table $where AND $date_zd>='".$arr_date["ss"]."' and $date_zd<='".$arr_date["se"]."'";
		// echo $sql1."<br>".$sql2."<br>"; 
		$bavpi=$this->homeDao->getone($sql1);
		$savpi=$this->homeDao->getone($sql2);
		// echo $sql1."<br>".$sql2."<br>"; 

		if($savpi != 0)
		$bp=round(($bavpi-$savpi),2);
		else
		$bp="&nbsp;";
		if($bp == '-0'){
			$bp = 0;
		}
		if ($bp>0) {
			$zhangdie = '<font color="red">↑'.abs($bp).'</font>';
		} elseif ($bp<0) {
			$zhangdie = '<font color="green">↓'.abs($bp).'</font>';
		} elseif ($bp==0) {
			$zhangdie = '--';
		} else {
			$zhangdie = $bp;
		}
			
		return $zhangdie;
	}

	//销售节奏评价
	public function xsjzpj($params)
	{
		$vtype = $params['vtype'];
		if(empty($vtype)){
			$vtype = 1;
		}
		$this->assign("vtype",$vtype);
		$vtype_arr = array("1"=>"螺纹钢",3=>"高线",2=>"盘螺",4=>"建筑钢材");
		$markettype = array('陕晋川甘西安'.$vtype_arr[$vtype].'价格','西咸片区','商洛片区','铜川片区','工程事业部西安','榆林片区','延安片区','安康片区','庆阳片区','宝平片区','陇南片区','天水片区','韩城市场龙钢'.$vtype_arr[$vtype].'价格','环韩城片区','陕晋川甘郑州'.$vtype_arr[$vtype].'价格','郑州片区','陕晋川甘成都'.$vtype_arr[$vtype].'价格','环汉中片区','广元片区','绵阳片区','成都片区','陕晋川甘重庆'.$vtype_arr[$vtype].'价格','重庆片区','陕晋川甘兰州'.$vtype_arr[$vtype].'价格','陇南片区','甘青藏片区','天水片区');

		$markettype_1 = array(
			'陕晋川甘西安'.$vtype_arr[$vtype].'价格'=>'西安',
			// '韩城市场龙钢'.$vtype_arr[$vtype].'价格'=>'西安',
			'陕晋川甘郑州'.$vtype_arr[$vtype].'价格'=>'郑州',
			'陕晋川甘成都'.$vtype_arr[$vtype].'价格'=>'成都',
			'陕晋川甘重庆'.$vtype_arr[$vtype].'价格'=>'重庆',
			'陕晋川甘兰州'.$vtype_arr[$vtype].'价格'=>'兰州'
		);

		$markettype_2 = array('西咸片区','商洛片区','铜川片区','工程事业部西安','榆林片区','延安片区','安康片区','庆阳片区','宝平片区','陇南片区','天水片区','环韩城片区','郑州片区','环汉中片区','广元片区','绵阳片区','成都片区','重庆片区','陇南片区','甘青藏片区','天水片区');
		$table_arr = array();
		foreach( $markettype as $v ){
			$table_arr[$v] = array();
		}

		$sdate = $params['sdate'];
		if(empty($sdate)){
			$sdate = date("Y-m-d");
		}
		// $ndate_arr[0] = $this->getday($sdate,"SJCGIndex",$vtype);
		// $ndate_arr[2] = $this->getday($sdate,"marketconditions",$vtype);
		// $ndate_arr[3] = $this->getday($sdate,"sg_data_table",$vtype_arr[$vtype]);
		// $ndate = min($ndate_arr);
		$ndate = $sdate;
		// echo $ndate;
		$ldate = $this->getpreday($ndate,$vtype);
		$this->assign("params",$params);
		$this->assign("sdate",$sdate);
		$this->assign("chdate",date("n月j日",strtotime($ndate)));
		$this->assign("vtype_arr",$vtype_arr);
		$city_arr = $GLOBALS ['city_arr'];

		$weekd = $this->weekday($ndate); //周环
		$mondayx=$this->monday($ndate);  //月环
		$mondayx_s=$this->monday_same($ndate); //月同
		$seasonx=$this->season($ndate); //季环
		$yeardayx=$this->yearday($ndate); //年环
		$seyd=$this->yearday_same($ndate); //年同
		// print_r($weekd);

		$sql = " select pqid,pianqu from sg_PianQu";
		$sg_PianQu_info = $this->_dao->query($sql);
		$sg_PianQu = array();
		$sg_PianQu2 = array();
		foreach ($sg_PianQu_info as $v) {
			$sg_PianQu[$v["pianqu"]] = $v["pqid"];
			$sg_PianQu2[$v["pqid"]] = $v["pianqu"];
		}

		$sql = "SELECT dta_2,dta_6 FROM `sg_data_table` WHERE `dta_type`='TEST_ERP_ZHJSJ' AND `dta_ym`='$ndate' AND `dta_1`='$vtype_arr[$vtype]' and dta_3='合计'";
		// echo $sql;
		$sg_data_info_b = $this->_dao->query($sql);
		$sql = "SELECT dta_2,dta_6 FROM `sg_data_table` WHERE `dta_type`='TEST_ERP_ZHJSJ' AND `dta_ym`='$ldate' AND `dta_1`='$vtype_arr[$vtype]' and dta_3='合计'";
		$sg_data_info_s = $this->_dao->query($sql);
		// echo"<pre>";print_r($sg_data_info_s);
		foreach($sg_data_info_s as $v){
			$sg_data_info_s2[$v['dta_2']] = $v['dta_6'];
		}
		foreach($sg_data_info_b as $v){
			$table_arr[$sg_PianQu2[$v['dta_2']]]["price"] = $v['dta_6'];
			$table_arr[$sg_PianQu2[$v['dta_2']]]["zhangdie"] =  $this->getzhangdie($v['dta_6']-$sg_data_info_s2[$v['dta_2']]);
		}
		$zhouhuan = array();
		$zhouhuan = $this->pi_bj4($weekd,$vtype_arr[$vtype]);
		$yuehuan = array();
		$yuehuan = $this->pi_bj4($mondayx,$vtype_arr[$vtype]);
		$yuetong = array();
		$yuetong = $this->pi_bj4($mondayx_s,$vtype_arr[$vtype]);
		$jihuan = array();
		$jihuan = $this->pi_bj4($seasonx,$vtype_arr[$vtype]);
		$nianhuan = array();
		$nianhuan = $this->pi_bj4($yeardayx,$vtype_arr[$vtype]);
		$niantong = array();
		$niantong = $this->pi_bj4($seyd,$vtype_arr[$vtype]);
		// echo"<pre>";print_r($zhouhuan);
		foreach($zhouhuan as $k => $v){
			$table_arr[$sg_PianQu2[$k]]['zhouhuan'] = $v;
			$table_arr[$sg_PianQu2[$k]]['yuehuan'] = $yuehuan[$k];
			$table_arr[$sg_PianQu2[$k]]['yuetong'] = $yuetong[$k];
			$table_arr[$sg_PianQu2[$k]]['jihuan'] = $jihuan[$k];
			$table_arr[$sg_PianQu2[$k]]['nianhuan'] = $nianhuan[$k];
			$table_arr[$sg_PianQu2[$k]]['niantong'] = $niantong[$k];
		}

		$SJCGIndex_b = $this->homeDao->getSJCGIndex($ndate,$vtype);
		$SJCGIndex_s = $this->homeDao->getSJCGIndex($ldate,$vtype);
		$jiage = array();
		foreach($SJCGIndex_s as $v){
			$SJCGIndex_s2[$v['CityName']] =  round($v['index']);
		}
		foreach($SJCGIndex_b as $v){
			$jiage[$v['CityName']]["price"] = round($v['index']);
			$jiage[$v['CityName']]["zhangdie"] = $this->getzhangdie(round($v['index'])-$SJCGIndex_s2[$v['CityName']]);
		}

		$zhouhuan = array();
		$zhouhuan = $this->pi_bj($weekd, $vtype);
		$yuehuan = array();
		$yuehuan = $this->pi_bj($mondayx, $vtype);
		$yuetong = array();
		$yuetong = $this->pi_bj($mondayx_s, $vtype);
		$jihuan = array();
		$jihuan = $this->pi_bj($seasonx, $vtype);
		$nianhuan = array();
		$nianhuan = $this->pi_bj($yeardayx, $vtype);
		$niantong = array();
		$niantong = $this->pi_bj($seyd, $vtype);
		// echo"<pre>";print_r($seyd);

		foreach ($markettype_1 as $k => $v) {
			$table_arr[$k]["price"] = $jiage[$v]["price"];
			$table_arr[$k]["zhangdie"] = $jiage[$v]["zhangdie"];
			$table_arr[$k]["zhouhuan"] = $zhouhuan[$v];
			$table_arr[$k]["yuehuan"] = $yuehuan[$v];
			$table_arr[$k]["yuetong"] = $yuetong[$v];
			$table_arr[$k]["jihuan"] = $jihuan[$v];
			$table_arr[$k]["nianhuan"] = $nianhuan[$v];
			$table_arr[$k]["niantong"] = $niantong[$v];
		}

		$jiageid_arr = $GLOBALS ['jiageid_arr'];
		// 韩城市场龙钢高线价格
		$sql = "select marketconditions.price,marketconditions.oldprice from marketconditions where mconmanagedate>='$ndate 00:00:00' and mconmanagedate<='$ndate 23:59:59' and (marketconditions.mastertopid='$jiageid_arr[$vtype]' or topicture='$jiageid_arr[$vtype]')";
		// echo $sql;
		$marketconditions_info = $this->homeDao->getrow($sql);
		$hclg['price'] = $marketconditions_info['price'];
		$hclg['zhangdie'] = $this->getzhangdie($marketconditions_info['price'],$marketconditions_info['oldprice']);
		$hclg['zhouhuan'] = $this->pi_bj2($weekd, $vtype);
		$hclg['yuehuan'] = $this->pi_bj2($mondayx, $vtype);
		$hclg['yuetong'] = $this->pi_bj2($mondayx_s, $vtype);
		$hclg['jihuan'] = $this->pi_bj2($seasonx, $vtype);
		$hclg['nianhuan'] = $this->pi_bj2($yeardayx, $vtype);
		$hclg['niantong'] = $this->pi_bj2($seyd, $vtype);

		$table_arr['韩城市场龙钢'.$vtype_arr[$vtype].'价格']["price"] = $hclg['price'];
		$table_arr['韩城市场龙钢'.$vtype_arr[$vtype].'价格']["zhangdie"] = $hclg['zhangdie'];
		$table_arr['韩城市场龙钢'.$vtype_arr[$vtype].'价格']["zhouhuan"] = $hclg['zhouhuan'];
		$table_arr['韩城市场龙钢'.$vtype_arr[$vtype].'价格']["yuehuan"] = $hclg['yuehuan'];
		$table_arr['韩城市场龙钢'.$vtype_arr[$vtype].'价格']["yuetong"] = $hclg['yuetong'];
		$table_arr['韩城市场龙钢'.$vtype_arr[$vtype].'价格']["jihuan"] = $hclg['jihuan'];
		$table_arr['韩城市场龙钢'.$vtype_arr[$vtype].'价格']["nianhuan"] = $hclg['nianhuan'];
		$table_arr['韩城市场龙钢'.$vtype_arr[$vtype].'价格']["niantong"] = $hclg['niantong'];
		// echo"<pre>";print_r($table_arr);
		$this->assign("table_arr",$table_arr);
		// echo"<pre>";print_r($hclg);
	}

	function preweek($date,$pre){
		$weekarr["s"] = date("Y-m-d",mktime(0, 0 , 0,date("m"),date("d")-date("w")+1-7*$pre,date("Y")));
		$weekarr["e"] =  date("Y-m-d",mktime(23,59,59,date("m"),date("d")-date("w")+7-7,date("Y")));
		return $weekarr;
	}

	// 销售计划完成情况
	public function xsjh($params)
	{
		// $markettype = array('工程事业部','西咸片区','环韩城片区','庆阳片区','铜川片区','安康片区','商洛片区','榆林片区','延安片区','宝鸡片区',
		// '环汉中片区','陇南片区','广元片区','天水片区','绵阳片区','南充片区','成都片区','甘青藏片区','郑州片区','武汉片区','云贵片区','重庆片');
		$sql = " select pqid,pianqu from sg_PianQu";
		$sg_PianQu_info = $this->_dao->query($sql);
		$sg_PianQu = array();
		$sg_PianQu2 = array();
		foreach ($sg_PianQu_info as $v) {
			$sg_PianQu[$v["pianqu"]] = $v["pqid"];
			$sg_PianQu2[$v["pqid"]] = $v["pianqu"];
		}
		$sdate = $params['sdate'];
		if(empty($sdate)){
			$sdate = date("Y-m-d");
		}
		// $sql = "select dta_ym from sg_data_table where dta_type='TEST_ERP_XSSJ' and dta_ym <='$sdate' and dta_1='建材' and dta_7='合计' order by dta_ym desc limit 1";
		// $ndate1 = $this->_dao->getOne($sql);
		// $sql = "select dta_ym from sg_data_table where dta_type='SGHCGCZXS' and dta_ym <='$sdate' order by dta_ym desc limit 1";
		// $ndate = $this->_dao->getOne($sql);
		$ndate1 = $sdate;
		$w = date('w',strtotime($sdate));
		$ndate = date('Y-m-d',strtotime("$sdate -".($w ? $w - 1 : 6).' days'));  //获取本周一
		$sql = "select dta_ym from sg_data_table where dta_type='SGHCGCZXS' and dta_ym <'$ndate' order by dta_ym desc limit 1";
		$ldate = $this->_dao->getOne($sql);
		$this->assign("sdate",$sdate);
		//计划量
		$bplan_info = $this->_dao->getsg_data('SGHCGCZXS', $ndate);
		$splan_info = $this->_dao->getsg_data('SGHCGCZXS', $ldate);
		$week_4 = $this->preweek($ndate, 7);
		$splan_info_4 = $this->_dao->getsg_data2('SGHCGCZXS', $week_4['s'], $week_4['e']);
		$week_52 = $this->preweek($ndate, 52);
		$splan_info_52 = $this->_dao->getsg_data2('SGHCGCZXS', $week_52['s'], $week_52['e']);
		//销售量
		$weekd = $this->weekday($ndate1); //周环
		// print_r($weekd);
		$bxiaoshou_info = $this->_dao->getsg_data_xiaoshou('TEST_ERP_XSSJ', $weekd['bs'], $ndate1);
		$sxiaoshou_info = $this->_dao->getsg_data_xiaoshou('TEST_ERP_XSSJ', $weekd['ss'], $weekd['se']);
		$xiaoshou_info_4 = $this->_dao->getsg_data_xiaoshou('TEST_ERP_XSSJ', $week_4['s'], $week_4['e']);
		$xiaoshou_info_52 = $this->_dao->getsg_data_xiaoshou('TEST_ERP_XSSJ', $week_52['s'], $week_52['e']);
		// echo"<pre>";print_r($xiaoshou_info_4);
		$bplan = array();
		$splan = array();
		$splan_4 = array();
		$splan_52 = array();
		foreach ($bplan_info as $v) {
			$bplan[$v['dta_2']] = round($v['dta_5'] * 7,2);
		}
		foreach ($splan_info as $v) {
			$splan[$v['dta_2']] = round($v['dta_5'] * 7,2);
		}
		foreach ($splan_info_4 as $v) {
			$splan_4[$v['dta_2']] = round($v['dta_5'] * 7,2);
		}
		foreach ($splan_info_52 as $v) {
			$splan_52[$v['dta_2']] = round($v['dta_5'] * 7,2);
		}
		$bxiaoshou = array();
		$sxiaoshou = array();
		$xiaoshou_4 = array();
		$xiaoshou_52 = array();
		foreach ($bxiaoshou_info as $v) {
			$bxiaoshou[$v['dta_4']] = round($v['dta_5'] ,2);
		}
		foreach ($sxiaoshou_info as $v) {
			$sxiaoshou[$v['dta_4']] = round($v['dta_5'] ,2);
		}
		foreach ($xiaoshou_info_4 as $v) {
			$xiaoshou_4[$v['dta_4']] = round($v['dta_5'] ,2);
		}
		foreach ($xiaoshou_info_52 as $v) {
			$xiaoshou_52[$v['dta_4']] = round($v['dta_5'] ,2);
		}
		$table_info = array();
		foreach ($sg_PianQu2 as $k => $v) {
			$table_info["合计"]["bz"]['plan'] += $bplan[$v];
			$table_info["合计"]["sz"]['plan'] += $splan[$v];
			$table_info["合计"]["s4"]['plan'] += $splan_4[$v];
			$table_info["合计"]["s52"]['plan'] += $splan_52[$v];
			$table_info[$v]["bz"]['plan'] = $bplan[$v];
			$table_info[$v]["sz"]['plan'] = $splan[$v];
			$table_info[$v]["s4"]['plan'] = $splan_4[$v];
			$table_info[$v]["s52"]['plan'] = $splan_52[$v];

			$table_info["合计"]["bz"]['xiaoshou'] += $bxiaoshou[$k];
			$table_info["合计"]["sz"]['xiaoshou'] += $sxiaoshou[$k];
			$table_info["合计"]["s4"]['xiaoshou'] += $xiaoshou_4[$k];
			$table_info["合计"]["s52"]['xiaoshou'] += $xiaoshou_52[$k];
			$table_info[$v]["bz"]['xiaoshou'] = $bxiaoshou[$k];
			$table_info[$v]["sz"]['xiaoshou'] = $sxiaoshou[$k];
			$table_info[$v]["s4"]['xiaoshou'] = $xiaoshou_4[$k];
			$table_info[$v]["s52"]['xiaoshou'] = $xiaoshou_52[$k];

			$table_info[$v]["bz"]['bilv'] = round($bxiaoshou[$k]/$bplan[$v]*100,2);
			$table_info[$v]["sz"]['bilv'] = round($sxiaoshou[$k]/$splan[$v]*100,2);
			$table_info[$v]["s4"]['bilv'] = round($xiaoshou_4[$k]/$splan_4[$v]*100,2);
			$table_info[$v]["s52"]['bilv'] = round($xiaoshou_52[$k]/$splan_52[$v]*100,2);
		}
		foreach ($table_info['合计'] as $k => $v) {
			$table_info['合计'][$k]['bilv'] = round($v['xiaoshou']/$v['plan']*100,2);
		}
		// echo"<pre>";print_r($table_info);
		$this->assign("table_info",$table_info);
	}

	function getday2($date, $table, $jiageid){
		$strlen = strlen($jiageid);
		if ($strlen == 6) {
			$jiage = "topicture";
		} elseif ($strlen == 7) {
			$jiage = "mastertopid";
		}
		if($table == "marketconditions"){
			$sqldate="select mconmanagedate from marketconditions where $jiage='$jiageid' and mconmanagedate<='$date 23:59:59' order by mconmanagedate desc limit 1";
		}
		// echo $sqldate."<br>";
		$qdate = $this->homeDao->getone($sqldate);
		$qdate = date("Y-m-d",strtotime ($qdate));
		return $qdate;
	}

	function pi_bj3($arr_date, $mark_arr, $table){
		$markstr = implode("','",$mark_arr);
		//本平均价
		if ($table == "marketconditions") {
			$sql1="select topicture as mark, avg(`price`) as bawp from marketconditions where mconmanagedate>='".$arr_date["bs"]."' and mconmanagedate<='".$arr_date["be"]."' and topicture in ('$markstr') group by topicture";
			//上平均价
			$sql2="select topicture as mark,avg(`price`) as sawp from marketconditions where mconmanagedate>='".$arr_date["ss"]."' and mconmanagedate<='".$arr_date["se"]."' and topicture in ('$markstr') group by topicture";

			$binfo=$this->homeDao->query($sql1);
			$sinfo=$this->homeDao->query($sql2);
		} elseif ($table == "shpi_material") {
			$sql1="select topicture as mark, avg(`price`) as bawp from shpi_material where dateday>='".$arr_date["bs"]."' and dateday<='".$arr_date["be"]."' and topicture in ('$markstr') group by topicture";
			//上平均价
			$sql2="select topicture as mark,avg(`price`) as sawp from shpi_material where dateday>='".$arr_date["ss"]."' and dateday<='".$arr_date["se"]."' and topicture in ('$markstr') group by topicture";

			$binfo=$this->homeDao->query($sql1);
			$sinfo=$this->homeDao->query($sql2);
		} elseif ($table == "sg_data_table") {
			$sql1="select dta_1 as mark, avg(`dta_2`) as bawp from sg_data_table where `dta_type` = 'TEST_ERP_CG_HC' and dta_ym>='".$arr_date["bs"]."' and dta_ym<='".$arr_date["be"]."' and dta_1 in ('$markstr') group by dta_1";

			//上平均价
			$sql2="select dta_1 as mark, avg(`dta_2`) as sawp from sg_data_table where `dta_type` = 'TEST_ERP_CG_HC' and dta_ym>='".$arr_date["ss"]."' and dta_ym<='".$arr_date["se"]."' and dta_1 in ('$markstr') group by dta_1";

			$binfo=$this->_dao->query($sql1);
			$sinfo=$this->_dao->query($sql2);
		}
		// echo $sql1."<br>".$sql2."<br>";
		
		$binfo2 = array();
		foreach($binfo as $k => $v){
			$binfo2[$v['mark']] = $v['bawp'];
		}
		$sinfo2 = array();
		foreach($sinfo as $k => $v){
			$sinfo2[$v['mark']] = $v['sawp'];
		}
		// echo"<pre>";print_r($binfo2);
		// echo"<pre>";print_r($sinfo2);
		$bi_arr = array();
		foreach($binfo2 as $k => $v){
			$bavpi=$v;
			$savpi=$sinfo2[$k];
			if($savpi != 0)
			$bp=round(($bavpi-$savpi),2);
			else
			$bp="&nbsp;";
			if($bp == '-0'){
				$bp = 0;
			}
			if ($bp>0) {
				$zhangdie = '<font color="red">↑'.abs($bp).'</font>';
			} elseif ($bp<0) {
				$zhangdie = '<font color="green">↓'.abs($bp).'</font>';
			} elseif ($bp==0) {
				$zhangdie = '--';
			} else {
				$zhangdie = $bp;
			}
			$bi_arr[$k] = $zhangdie;
		}
		return $bi_arr;
	}

	// 采购节奏评价
	public function cgjzpj($params)
	{
		$tdarr = array(
			"铁矿石" => array("日照港PB粉矿价格","韩城公司现货矿采购价格","唐山66%磁铁矿价格","大西沟贸易国产矿"),
			"废钢" => array("钢之家华北废钢价格指数","龙钢废钢采购价格","钢之家西部废钢价格指数","汉钢废钢采购价格"),
			"焦炭" => array("钢之家焦炭价格指数","韩城公司焦炭采购价格"),
			"喷吹煤" => array("钢之家喷吹煤价格指数","韩城公司喷吹煤采购价格"),
			"硅铁" => array("钢之家硅铁价格指数","韩城公司硅铁采购价格"),
			"硅锰" => array("钢之家硅锰价格指数","韩城公司硅锰采购价格"),
			"钒氮合金" => array("钒氮合金价格","韩城公司钒氮采购价格"),
			"铌铁" => array("巴西铌铁价格","韩城公司铌铁采购价格")
		);
		$table_arr = array();
		foreach ($tdarr as $k => $v) {
			$table_arr[$k]["rows"] = count($v,0);
			foreach ($v as $v2) {
				$table_arr[$k][$v2] = array();
			}
		}
		
		$sdate = $params['sdate'];
		if(empty($sdate)){
			$sdate = date("Y-m-d");
		}
		$jiage_arr = array( "D28611","448610","569422","A99420");
		// foreach ($jiage_arr as $k => $v) {
			// $ndate_arr[$k] = $this->getday2($sdate,"marketconditions","D28611");
		// }
		// $ndate_arr[] = $this->homeDao->getone("select dateday from shpi_material WHERE 1 and topicture='11' and dateday<='$sdate' order by dateday desc limit 1");
		// $ndate_arr[] = $this->homeDao->getone("select weiprice from shpi_mj_pzp WHERE vid=0 AND type=3 and dateday<='$sdate' order by dateday desc limit 1");
		// $ndate_arr[] = $this->_dao->getone("select dta_ym from sg_data_table WHERE dta_type='SG_BFJGHZ' and dta_ym<='$sdate' order by dta_ym desc limit 1");
		// $ndate = min($ndate_arr);
		$ndate = $sdate;
		$ldate = $this->homeDao->getone("select dateday from shpi_material WHERE 1 and topicture='11' and dateday<'$ndate' order by dateday desc limit 1");
		$this->assign("sdate",$sdate);
		$this->assign("chdate",date("n月j日",strtotime($ndate)));

		$weekd = $this->weekday($ndate); //周环
		$mondayx=$this->monday($ndate);  //月环
		$mondayx_s=$this->monday_same($ndate); //月同
		$seasonx=$this->season($ndate); //季环
		$yeardayx=$this->yearday($ndate); //年环
		$seyd=$this->yearday_same($ndate); //年同

		$jagestr = implode("','",$jiage_arr);
		$sql = "select topicture,price,oldprice from marketconditions where topicture in ('$jagestr') and mconmanagedate>='$ndate 00:00:00' and mconmanagedate<='$ndate 23:59:59'";
		$jg_info = $this->homeDao->query($sql);
		$jgarr = array();
		foreach ($jg_info as $k => $v) {
			$jgarr[$v['topicture']] = $v;
		}
		$table_arr['铁矿石']['日照港PB粉矿价格']['value'] = $jgarr['D28611']['price'];
		$table_arr['铁矿石']['日照港PB粉矿价格']['zhangdie'] = $this->getzhangdie($jgarr['D28611']['price'],$jgarr['D28611']['oldprice']);
		$table_arr['铁矿石']['唐山66%磁铁矿价格']['value'] = $jgarr['448610']['price'];
		$table_arr['铁矿石']['唐山66%磁铁矿价格']['zhangdie'] = $this->getzhangdie($jgarr['448610']['price'],$jgarr['448610']['oldprice']);
		$table_arr['钒氮合金']['钒氮合金价格']['value'] = $jgarr['569422']['price'];
		$table_arr['钒氮合金']['钒氮合金价格']['zhangdie'] = $this->getzhangdie($jgarr['569422']['price'],$jgarr['569422']['oldprice']);
		$table_arr['铌铁']['巴西铌铁价格']['value'] = $jgarr['A99420']['price'];
		$table_arr['铌铁']['巴西铌铁价格']['zhangdie'] = $this->getzhangdie($jgarr['A99420']['price'],$jgarr['A99420']['oldprice']);

		$jg_zhouhuan = array();
		$jg_zhouhuan = $this->pi_bj3($weekd, $jiage_arr, "marketconditions");
		$jg_yuehuan = array();
		$jg_yuehuan = $this->pi_bj3($mondayx, $jiage_arr, "marketconditions");
		$jg_yuetong = array();
		$jg_yuetong = $this->pi_bj3($mondayx_s, $jiage_arr, "marketconditions");
		$jg_jihuan = array();
		$jg_jihuan = $this->pi_bj3($seasonx, $jiage_arr, "marketconditions");
		$jg_nianhuan = array();
		$jg_nianhuan = $this->pi_bj3($yeardayx, $jiage_arr, "marketconditions");
		$jg_niantong = array();
		$jg_niantong = $this->pi_bj3($seyd, $jiage_arr, "marketconditions");
		$table_arr['铁矿石']['日照港PB粉矿价格']['zhouhuan'] = $jg_zhouhuan['D28611'];
		$table_arr['铁矿石']['日照港PB粉矿价格']['yuehuan'] = $jg_yuehuan['D28611'];
		$table_arr['铁矿石']['日照港PB粉矿价格']['yuetong'] = $jg_yuetong['D28611'];
		$table_arr['铁矿石']['日照港PB粉矿价格']['jihuan'] = $jg_jihuan['D28611'];
		$table_arr['铁矿石']['日照港PB粉矿价格']['nianhuan'] = $jg_nianhuan['D28611'];
		$table_arr['铁矿石']['日照港PB粉矿价格']['niantong'] = $jg_niantong['D28611'];
		$table_arr['铁矿石']['唐山66%磁铁矿价格']['zhouhuan'] = $jg_zhouhuan['448610'];
		$table_arr['铁矿石']['唐山66%磁铁矿价格']['yuehuan'] = $jg_yuehuan['448610'];
		$table_arr['铁矿石']['唐山66%磁铁矿价格']['yuetong'] = $jg_yuetong['448610'];
		$table_arr['铁矿石']['唐山66%磁铁矿价格']['jihuan'] = $jg_jihuan['448610'];
		$table_arr['铁矿石']['唐山66%磁铁矿价格']['nianhuan'] = $jg_nianhuan['448610'];
		$table_arr['铁矿石']['唐山66%磁铁矿价格']['niantong'] = $jg_niantong['448610'];
		$table_arr['钒氮合金']['钒氮合金价格']['zhouhuan'] = $jg_zhouhuan['569422'];
		$table_arr['钒氮合金']['钒氮合金价格']['yuehuan'] = $jg_yuehuan['569422'];
		$table_arr['钒氮合金']['钒氮合金价格']['yuetong'] = $jg_yuetong['569422'];
		$table_arr['钒氮合金']['钒氮合金价格']['jihuan'] = $jg_jihuan['569422'];
		$table_arr['钒氮合金']['钒氮合金价格']['nianhuan'] = $jg_nianhuan['569422'];
		$table_arr['钒氮合金']['钒氮合金价格']['niantong'] = $jg_niantong['569422'];
		$table_arr['铌铁']['巴西铌铁价格']['zhouhuan'] = $jg_zhouhuan['A99420'];
		$table_arr['铌铁']['巴西铌铁价格']['yuehuan'] = $jg_yuehuan['A99420'];
		$table_arr['铌铁']['巴西铌铁价格']['yuetong'] = $jg_yuetong['A99420'];
		$table_arr['铌铁']['巴西铌铁价格']['jihuan'] = $jg_jihuan['A99420'];
		$table_arr['铌铁']['巴西铌铁价格']['nianhuan'] = $jg_nianhuan['A99420'];
		$table_arr['铌铁']['巴西铌铁价格']['niantong'] = $jg_niantong['A99420'];

		// $hbfg_b = $this->homeDao->get_FG("price", "shpi_material", " WHERE 1 and  topicture='11'", "dateday", $ndate);
		// $hbfg_s = $this->homeDao->get_FG("price", "shpi_material", " WHERE 1 and  topicture='11'", "dateday", $ldate);
		// $xbfg_b = $this->homeDao->get_FG("price", "shpi_material", " WHERE 1 and  topicture='14'", "dateday", $ndate);
		// $xbfg_s = $this->homeDao->get_FG("price", "shpi_material", " WHERE 1 and  topicture='14'", "dateday", $ldate);
		$topicture_arr = array("11","14","3","7","8");
		$shpi_material_b = $this->homeDao->get_shpi_material("price",$topicture_arr,$ndate);
		$shpi_material_s = $this->homeDao->get_shpi_material("price",$topicture_arr,$ldate);
		$table_arr['废钢']['钢之家华北废钢价格指数']['value'] = round($shpi_material_b['11'],0);
		$table_arr['废钢']['钢之家华北废钢价格指数']['zhangdie'] = $this->getzhangdie(round($shpi_material_b['11'],0),round($shpi_material_s['11'],0));
		$table_arr['废钢']['钢之家西部废钢价格指数']['value'] = round($shpi_material_b['14'],0);
		$table_arr['废钢']['钢之家西部废钢价格指数']['zhangdie'] = $this->getzhangdie(round($shpi_material_b['14'],0),round($shpi_material_s['14'],0));
		$table_arr['焦炭']['钢之家焦炭价格指数']['value'] =round( $shpi_material_b['3'],0);
		$table_arr['焦炭']['钢之家焦炭价格指数']['zhangdie'] = $this->getzhangdie(round($shpi_material_b['3'],0),round($shpi_material_s['3'],0));
		$table_arr['硅铁']['钢之家硅铁价格指数']['value'] = round($shpi_material_b['7'],0);
		$table_arr['硅铁']['钢之家硅铁价格指数']['zhangdie'] = $this->getzhangdie(round($shpi_material_b['7'],0),round($shpi_material_s['7'],0));
		$table_arr['硅锰']['钢之家硅锰价格指数']['value'] = round($shpi_material_b['8'],0);
		$table_arr['硅锰']['钢之家硅锰价格指数']['zhangdie'] = $this->getzhangdie(round($shpi_material_b['8'],0),round($shpi_material_s['8'],0));

		// $topicture_arr = array('11','14');
		$jg_zhouhuan = $this->pi_bj3($weekd, $topicture_arr, "shpi_material");
		$jg_yuehuan = array();
		$jg_yuehuan = $this->pi_bj3($mondayx, $topicture_arr, "shpi_material");
		$jg_yuetong = array();
		$jg_yuetong = $this->pi_bj3($mondayx_s, $topicture_arr, "shpi_material");
		$jg_jihuan = array();
		$jg_jihuan = $this->pi_bj3($seasonx, $topicture_arr, "shpi_material");
		$jg_nianhuan = array();
		$jg_nianhuan = $this->pi_bj3($yeardayx, $topicture_arr, "shpi_material");
		$jg_niantong = array();
		$jg_niantong = $this->pi_bj3($seyd, $topicture_arr, "shpi_material");
		$table_arr['废钢']['钢之家华北废钢价格指数']['zhouhuan'] = $jg_zhouhuan['11'];
		$table_arr['废钢']['钢之家华北废钢价格指数']['yuehuan'] = $jg_yuehuan['11'];
		$table_arr['废钢']['钢之家华北废钢价格指数']['yuetong'] = $jg_yuetong['11'];
		$table_arr['废钢']['钢之家华北废钢价格指数']['jihuan'] = $jg_jihuan['11'];
		$table_arr['废钢']['钢之家华北废钢价格指数']['nianhuan'] = $jg_nianhuan['11'];
		$table_arr['废钢']['钢之家华北废钢价格指数']['niantong'] = $jg_niantong['11'];
		
		$table_arr['废钢']['钢之家西部废钢价格指数']['zhouhuan'] = $jg_zhouhuan['14'];
		$table_arr['废钢']['钢之家西部废钢价格指数']['yuehuan'] = $jg_yuehuan['14'];
		$table_arr['废钢']['钢之家西部废钢价格指数']['yuetong'] = $jg_yuetong['14'];
		$table_arr['废钢']['钢之家西部废钢价格指数']['jihuan'] = $jg_jihuan['14'];
		$table_arr['废钢']['钢之家西部废钢价格指数']['nianhuan'] = $jg_nianhuan['14'];
		$table_arr['废钢']['钢之家西部废钢价格指数']['niantong'] = $jg_niantong['14'];

		$table_arr['焦炭']['钢之家焦炭价格指数']['zhouhuan'] = $jg_zhouhuan['3'];
		$table_arr['焦炭']['钢之家焦炭价格指数']['yuehuan'] = $jg_yuehuan['3'];
		$table_arr['焦炭']['钢之家焦炭价格指数']['yuetong'] = $jg_yuetong['3'];
		$table_arr['焦炭']['钢之家焦炭价格指数']['jihuan'] = $jg_jihuan['3'];
		$table_arr['焦炭']['钢之家焦炭价格指数']['nianhuan'] = $jg_nianhuan['3'];
		$table_arr['焦炭']['钢之家焦炭价格指数']['niantong'] = $jg_niantong['3'];

		$table_arr['硅铁']['钢之家硅铁价格指数']['zhouhuan'] = $jg_zhouhuan['7'];
		$table_arr['硅铁']['钢之家硅铁价格指数']['yuehuan'] = $jg_yuehuan['7'];
		$table_arr['硅铁']['钢之家硅铁价格指数']['yuetong'] = $jg_yuetong['7'];
		$table_arr['硅铁']['钢之家硅铁价格指数']['jihuan'] = $jg_jihuan['7'];
		$table_arr['硅铁']['钢之家硅铁价格指数']['nianhuan'] = $jg_nianhuan['7'];
		$table_arr['硅铁']['钢之家硅铁价格指数']['niantong'] = $jg_niantong['7'];

		$table_arr['硅锰']['钢之家硅锰价格指数']['zhouhuan'] = $jg_zhouhuan['8'];
		$table_arr['硅锰']['钢之家硅锰价格指数']['yuehuan'] = $jg_yuehuan['8'];
		$table_arr['硅锰']['钢之家硅锰价格指数']['yuetong'] = $jg_yuetong['8'];
		$table_arr['硅锰']['钢之家硅锰价格指数']['jihuan'] = $jg_jihuan['8'];
		$table_arr['硅锰']['钢之家硅锰价格指数']['nianhuan'] = $jg_nianhuan['8'];
		$table_arr['硅锰']['钢之家硅锰价格指数']['niantong'] = $jg_niantong['8'];
		
		$pcm_b = $this->homeDao->get_FG("weiprice", "shpi_mj_pzp", " WHERE vid=0 AND type=3", "dateday", $ndate);
		$pcm_s = $this->homeDao->get_FG("weiprice", "shpi_mj_pzp", " WHERE vid=0 AND type=3", "dateday", $ldate);
		$table_arr['喷吹煤']['钢之家喷吹煤价格指数']['value'] = round($pcm_b,0);
		$table_arr['喷吹煤']['钢之家喷吹煤价格指数']['zhangdie'] = $this->getzhangdie(round($pcm_b,0),round($pcm_s,0));
		$table_arr['喷吹煤']['钢之家喷吹煤价格指数']['zhouhuan'] = $this->pi_bj_shuju($weekd, "weiprice", "shpi_mj_pzp", " WHERE vid=0 AND type=3", "dateday");
		$table_arr['喷吹煤']['钢之家喷吹煤价格指数']['yuehuan'] = $this->pi_bj_shuju($mondayx, "weiprice", "shpi_mj_pzp", " WHERE vid=0 AND type=3", "dateday");
		$table_arr['喷吹煤']['钢之家喷吹煤价格指数']['yuetong'] = $this->pi_bj_shuju($mondayx_s, "weiprice", "shpi_mj_pzp", " WHERE vid=0 AND type=3", "dateday");
		$table_arr['喷吹煤']['钢之家喷吹煤价格指数']['jihuan'] = $this->pi_bj_shuju($seasonx, "weiprice", "shpi_mj_pzp", " WHERE vid=0 AND type=3", "dateday");
		$table_arr['喷吹煤']['钢之家喷吹煤价格指数']['nianhuan'] =$this->pi_bj_shuju($yeardayx, "weiprice", "shpi_mj_pzp", " WHERE vid=0 AND type=3", "dateday");
		$table_arr['喷吹煤']['钢之家喷吹煤价格指数']['niantong'] = $this->pi_bj_shuju($seyd, "weiprice", "shpi_mj_pzp", " WHERE vid=0 AND type=3", "dateday");

		// 大西沟贸易国产矿 B1\C1\D1
		$bfjghz_b = $this->_dao->bfjghz($ndate);
		$bfjghz_s = $this->_dao->bfjghz($ldate);
		$table_arr['铁矿石']['大西沟贸易国产矿']['value'] = round($bfjghz_b['dta_1'],2);
		$table_arr['废钢']['龙钢废钢采购价格']['value'] = round($bfjghz_b['dta_2'],2);
		$table_arr['废钢']['汉钢废钢采购价格']['value'] = round($bfjghz_b['dta_3'],2);
		$table_arr['铁矿石']['大西沟贸易国产矿']['zhangdie'] = $this->getzhangdie(round($bfjghz_b['dta_1'],2),round($bfjghz_s['dta_1'],2));
		$table_arr['废钢']['龙钢废钢采购价格']['zhangdie'] = $this->getzhangdie(round($bfjghz_b['dta_2'],2),round($bfjghz_s['dta_2'],2));
		$table_arr['废钢']['汉钢废钢采购价格']['zhangdie'] = $this->getzhangdie(round($bfjghz_b['dta_3'],2),round($bfjghz_s['dta_3'],2));
		$bfjghz_zhouhuan = $this->pi_bj5($weekd);
		$bfjghz_yuehuan = $this->pi_bj5($mondayx);
		$bfjghz_yuetong = $this->pi_bj5($mondayx_s);
		$bfjghz_jihuan = $this->pi_bj5($seasonx);
		$bfjghz_nianhuan = $this->pi_bj5($yeardayx);
		$bfjghz_niantong = $this->pi_bj5($seyd);
		$table_arr['铁矿石']['大西沟贸易国产矿']['zhouhuan'] = $bfjghz_zhouhuan['bp_1'];
		$table_arr['废钢']['龙钢废钢采购价格']['zhouhuan'] = $bfjghz_zhouhuan['bp_2'];
		$table_arr['废钢']['汉钢废钢采购价格']['zhouhuan'] = $bfjghz_zhouhuan['bp_3'];
		$table_arr['铁矿石']['大西沟贸易国产矿']['yuehuan'] = $bfjghz_yuehuan['bp_1'];
		$table_arr['废钢']['龙钢废钢采购价格']['yuehuan'] = $bfjghz_yuehuan['bp_2'];
		$table_arr['废钢']['汉钢废钢采购价格']['yuehuan'] = $bfjghz_yuehuan['bp_3'];
		$table_arr['铁矿石']['大西沟贸易国产矿']['yuetong'] = $bfjghz_yuetong['bp_1'];
		$table_arr['废钢']['龙钢废钢采购价格']['yuetong'] = $bfjghz_yuetong['bp_2'];
		$table_arr['废钢']['汉钢废钢采购价格']['yuetong'] = $bfjghz_yuetong['bp_3'];
		$table_arr['铁矿石']['大西沟贸易国产矿']['jihuan'] = $bfjghz_jihuan['bp_1'];
		$table_arr['废钢']['龙钢废钢采购价格']['jihuan'] = $bfjghz_jihuan['bp_2'];
		$table_arr['废钢']['汉钢废钢采购价格']['jihuan'] = $bfjghz_jihuan['bp_3'];
		$table_arr['铁矿石']['大西沟贸易国产矿']['nianhuan'] = $bfjghz_nianhuan['bp_1'];
		$table_arr['废钢']['龙钢废钢采购价格']['nianhuan'] = $bfjghz_nianhuan['bp_2'];
		$table_arr['废钢']['汉钢废钢采购价格']['nianhuan'] = $bfjghz_nianhuan['bp_3'];
		$table_arr['铁矿石']['大西沟贸易国产矿']['niantong'] = $bfjghz_niantong['bp_1'];
		$table_arr['废钢']['龙钢废钢采购价格']['niantong'] = $bfjghz_niantong['bp_2'];
		$table_arr['废钢']['汉钢废钢采购价格']['niantong'] = $bfjghz_niantong['bp_3'];

		$sg_pzarr = array("铁矿石","焦炭","喷吹煤","硅铁","硅锰","钒氮","铌铁");
		$sg_pzstr = implode("','",$sg_pzarr);
		$sql = "select dta_1,dta_2 from sg_data_table where `dta_type` = 'TEST_ERP_CG_HC' and dta_1 in ('$sg_pzstr') and dta_ym = '$ndate'";
		$sg_pzinfo_b = $this->_dao->query($sql);
		$sql = "select dta_1,dta_2 from sg_data_table where `dta_type` = 'TEST_ERP_CG_HC' and dta_1 in ('$sg_pzstr') and dta_ym = '$ldate'";
		$sg_pzinfo_s = $this->_dao->query($sql);
		foreach ($sg_pzinfo_b as $v) {
			$sg_pzinfo_b1[$v["dta_1"]] = $v["dta_2"];
		}
		foreach ($sg_pzinfo_s as $v) {
			$sg_pzinfo_s1[$v["dta_1"]] = $v["dta_2"];
		}
		$table_arr['铁矿石']['韩城公司现货矿采购价格']['value'] = $sg_pzinfo_b1['铁矿石'];
		$table_arr['焦炭']['韩城公司焦炭采购价格']['value'] = $sg_pzinfo_b1['焦炭'];
		$table_arr['喷吹煤']['韩城公司喷吹煤采购价格']['value'] = $sg_pzinfo_b1['喷吹煤'];
		$table_arr['硅铁']['韩城公司硅铁采购价格']['value'] = $sg_pzinfo_b1['硅铁'];
		$table_arr['硅锰']['韩城公司硅锰采购价格']['value'] = $sg_pzinfo_b1['硅锰'];
		$table_arr['钒氮合金']['韩城公司钒氮采购价格']['value'] = $sg_pzinfo_b1['钒氮'];
		$table_arr['铌铁']['韩城公司铌铁采购价格']['value'] = $sg_pzinfo_b1['铌铁'];
		$table_arr['铁矿石']['韩城公司现货矿采购价格']['zhangdie'] = $this->getzhangdie($sg_pzinfo_b1['铁矿石'],$sg_pzinfo_s1['铁矿石']);
		$table_arr['焦炭']['韩城公司焦炭采购价格']['zhangdie'] = $this->getzhangdie($sg_pzinfo_b1['焦炭'],$sg_pzinfo_s1['焦炭']);
		$table_arr['喷吹煤']['韩城公司喷吹煤采购价格']['zhangdie'] = $this->getzhangdie($sg_pzinfo_b1['喷吹煤'],$sg_pzinfo_s1['喷吹煤']);
		$table_arr['硅铁']['韩城公司硅铁采购价格']['zhangdie'] = $this->getzhangdie($sg_pzinfo_b1['硅铁'],$sg_pzinfo_s1['硅铁']);
		$table_arr['硅锰']['韩城公司硅锰采购价格']['zhangdie'] = $this->getzhangdie($sg_pzinfo_b1['硅锰'],$sg_pzinfo_s1['硅锰']);
		$table_arr['钒氮合金']['韩城公司钒氮采购价格']['zhangdie'] = $this->getzhangdie($sg_pzinfo_b1['钒氮'],$sg_pzinfo_s1['钒氮']);
		$table_arr['铌铁']['韩城公司铌铁采购价格']['zhangdie'] = $this->getzhangdie($sg_pzinfo_b1['铌铁'],$sg_pzinfo_s1['铌铁']);
		$jg_zhouhuan = $this->pi_bj3($weekd, $sg_pzarr, "sg_data_table");
		$jg_yuehuan = array();
		$jg_yuehuan = $this->pi_bj3($mondayx, $sg_pzarr, "sg_data_table");
		$jg_yuetong = array();
		$jg_yuetong = $this->pi_bj3($mondayx_s, $sg_pzarr, "sg_data_table");
		$jg_jihuan = array();
		$jg_jihuan = $this->pi_bj3($seasonx, $sg_pzarr, "sg_data_table");
		$jg_nianhuan = array();
		$jg_nianhuan = $this->pi_bj3($yeardayx, $sg_pzarr, "sg_data_table");
		$jg_niantong = array();
		$jg_niantong = $this->pi_bj3($seyd, $sg_pzarr, "sg_data_table");

		$table_arr['铁矿石']['韩城公司现货矿采购价格']['zhouhuan'] = $jg_zhouhuan['铁矿石'];
		$table_arr['铁矿石']['韩城公司现货矿采购价格']['yuehuan'] = $jg_yuehuan['铁矿石'];
		$table_arr['铁矿石']['韩城公司现货矿采购价格']['yuetong'] = $jg_yuetong['铁矿石'];
		$table_arr['铁矿石']['韩城公司现货矿采购价格']['jihuan'] = $jg_jihuan['铁矿石'];
		$table_arr['铁矿石']['韩城公司现货矿采购价格']['nianhuan'] = $jg_nianhuan['铁矿石'];
		$table_arr['铁矿石']['韩城公司现货矿采购价格']['niantong'] = $jg_niantong['铁矿石'];

		$table_arr['焦炭']['韩城公司焦炭采购价格']['zhouhuan'] = $jg_zhouhuan['焦炭'];
		$table_arr['焦炭']['韩城公司焦炭采购价格']['yuehuan'] = $jg_yuehuan['焦炭'];
		$table_arr['焦炭']['韩城公司焦炭采购价格']['yuetong'] = $jg_yuetong['焦炭'];
		$table_arr['焦炭']['韩城公司焦炭采购价格']['jihuan'] = $jg_jihuan['焦炭'];
		$table_arr['焦炭']['韩城公司焦炭采购价格']['nianhuan'] = $jg_nianhuan['焦炭'];
		$table_arr['焦炭']['韩城公司焦炭采购价格']['niantong'] = $jg_niantong['焦炭'];

		$table_arr['喷吹煤']['韩城公司喷吹煤采购价格']['zhouhuan'] = $jg_zhouhuan['喷吹煤'];
		$table_arr['喷吹煤']['韩城公司喷吹煤采购价格']['yuehuan'] = $jg_yuehuan['喷吹煤'];
		$table_arr['喷吹煤']['韩城公司喷吹煤采购价格']['yuetong'] = $jg_yuetong['喷吹煤'];
		$table_arr['喷吹煤']['韩城公司喷吹煤采购价格']['jihuan'] = $jg_jihuan['喷吹煤'];
		$table_arr['喷吹煤']['韩城公司喷吹煤采购价格']['nianhuan'] = $jg_nianhuan['喷吹煤'];
		$table_arr['喷吹煤']['韩城公司喷吹煤采购价格']['niantong'] = $jg_niantong['喷吹煤'];

		$table_arr['硅铁']['韩城公司硅铁采购价格']['zhouhuan'] = $jg_zhouhuan['硅铁'];
		$table_arr['硅铁']['韩城公司硅铁采购价格']['yuehuan'] = $jg_yuehuan['硅铁'];
		$table_arr['硅铁']['韩城公司硅铁采购价格']['yuetong'] = $jg_yuetong['硅铁'];
		$table_arr['硅铁']['韩城公司硅铁采购价格']['jihuan'] = $jg_jihuan['硅铁'];
		$table_arr['硅铁']['韩城公司硅铁采购价格']['nianhuan'] = $jg_nianhuan['硅铁'];
		$table_arr['硅铁']['韩城公司硅铁采购价格']['niantong'] = $jg_niantong['硅铁'];

		$table_arr['硅锰']['韩城公司硅锰采购价格']['zhouhuan'] = $jg_zhouhuan['硅锰'];
		$table_arr['硅锰']['韩城公司硅锰采购价格']['yuehuan'] = $jg_yuehuan['硅锰'];
		$table_arr['硅锰']['韩城公司硅锰采购价格']['yuetong'] = $jg_yuetong['硅锰'];
		$table_arr['硅锰']['韩城公司硅锰采购价格']['jihuan'] = $jg_jihuan['硅锰'];
		$table_arr['硅锰']['韩城公司硅锰采购价格']['nianhuan'] = $jg_nianhuan['硅锰'];
		$table_arr['硅锰']['韩城公司硅锰采购价格']['niantong'] = $jg_niantong['硅锰'];

		$table_arr['钒氮合金']['韩城公司钒氮采购价格']['zhouhuan'] = $jg_zhouhuan['钒氮'];
		$table_arr['钒氮合金']['韩城公司钒氮采购价格']['yuehuan'] = $jg_yuehuan['钒氮'];
		$table_arr['钒氮合金']['韩城公司钒氮采购价格']['yuetong'] = $jg_yuetong['钒氮'];
		$table_arr['钒氮合金']['韩城公司钒氮采购价格']['jihuan'] = $jg_jihuan['钒氮'];
		$table_arr['钒氮合金']['韩城公司钒氮采购价格']['nianhuan'] = $jg_nianhuan['钒氮'];
		$table_arr['钒氮合金']['韩城公司钒氮采购价格']['niantong'] = $jg_niantong['钒氮'];

		$table_arr['铌铁']['韩城公司铌铁采购价格']['zhouhuan'] = $jg_zhouhuan['铌铁'];
		$table_arr['铌铁']['韩城公司铌铁采购价格']['yuehuan'] = $jg_yuehuan['铌铁'];
		$table_arr['铌铁']['韩城公司铌铁采购价格']['yuetong'] = $jg_yuetong['铌铁'];
		$table_arr['铌铁']['韩城公司铌铁采购价格']['jihuan'] = $jg_jihuan['铌铁'];
		$table_arr['铌铁']['韩城公司铌铁采购价格']['nianhuan'] = $jg_nianhuan['铌铁'];
		$table_arr['铌铁']['韩城公司铌铁采购价格']['niantong'] = $jg_niantong['铌铁'];
		// echo"<pre>";print_r($table_arr);

		$this->assign("table_arr",$table_arr);
	}
	
}

?>