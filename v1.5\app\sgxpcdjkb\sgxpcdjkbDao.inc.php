<?php
class sgxpcdjkbD<PERSON> extends Dao{
  public function __construct( $writer ){
    parent::__construct( $writer );
  }

public function get_PriceData($where,$nowdate){		
	//当前
	$sql = "SELECT * FROM marketconditions WHERE ".$where." and mconmanagedate>='" . $nowdate . " 00:00:00' and mconmanagedate<='" . $nowdate . " 23:59:59'";
	$L2Data=$this->query($sql);
	
	$data['price'] = $L2Data[0]['price'];
	$zhangdie = $L2Data[0]['price'] - $L2Data[0]['oldprice'];
	if($zhangdie == "0")
		$data['zhangdie'] = "-";
	else if($zhangdie < "0")
		$data['zhangdie'] = "<font color='green'>↓".($L2Data[0]['oldprice'] - $L2Data[0]['price'])."</font>";
	else
		$data['zhangdie'] = "<font color='red'>↑".$zhangdie."</font>";

	if(count($L2Data) > 1)
	{
		$data['price'] = $L2Data[0]['price'] - $L2Data[1]['price'];

		$zhangdie = ($L2Data[0]['price'] - $L2Data[1]['price']) - ($L2Data[0]['oldprice'] - $L2Data[1]['oldprice']);
		if($zhangdie == "0")
			$data['zhangdie'] = "-";
		else if($zhangdie < "0")
			$data['zhangdie'] = "<font color='green'>↓".(($L2Data[0]['oldprice'] - $L2Data[1]['oldprice']) - ($L2Data[0]['price'] - $L2Data[1]['price']))."</font>";
		else
			$data['zhangdie'] = "<font color='red'>↑".$zhangdie."</font>";
	}
	
	//较上月末
	$nowmonth = date('m',strtotime($nowdate));
	$nowyear = date('Y',strtotime($nowdate));
	if ($nowmonth == 1) {
		$lastmonth = 12;
		$lastyear = $nowyear - 1;
	} 
	else 
	{
		$lastmonth = $nowmonth - 1;
		$lastyear = $nowyear;
	}
	$lastEndDay = $lastyear . '-' . $lastmonth . '-' . date('t', mktime(0,0,0,$nowmonth-1,1,$nowyear));
	$e_time = date('Y-m-d',strtotime($lastEndDay));//上个月的月末时间
	
	$lastsql = "SELECT price FROM marketconditions WHERE ".$where." and mconmanagedate>='" . $e_time . " 00:00:00' and mconmanagedate<='" . $e_time . " 23:59:59'";
	$lastData = $this->query($lastsql);
	
	$syzhangdie = $L2Data[0]['price'] - $lastData[0]['price'];
	if($syzhangdie == "0")
		$data['syzhangdie'] = "-";
	else if($syzhangdie < "0")
		$data['syzhangdie'] = "<font color='green'>↓".($lastData[0]['price'] - $L2Data[0]['price'])."</font>";
	else
		$data['syzhangdie'] = "<font color='red'>↑".$syzhangdie."</font>";

	if(count($lastData) > 1)
	{
		$syzhangdie = ($L2Data[0]['price'] - $L2Data[1]['price']) - ($lastData[0]['price'] - $lastData[1]['price']);
		if($syzhangdie == "0")
			$data['syzhangdie'] = "-";
		else if($syzhangdie < "0")
			$data['syzhangdie'] = "<font color='green'>↓".(($lastData[0]['price'] - $lastData[1]['price']) - ($L2Data[0]['price'] - $L2Data[1]['price']))."</font>";
		else
			$data['syzhangdie'] = "<font color='red'>↑".$syzhangdie."</font>";
	}

	
	//本月均价
	$beginThismonth = date('Y-m-d',mktime(0,0,0,$nowmonth,1,$nowyear)); //本月初时间
	$countnowsql = "SELECT sum(price) as sumprice FROM marketconditions WHERE ".$where." and mconmanagedate>='" . $beginThismonth . " 00:00:00' and mconmanagedate<='" . $nowdate . " 23:59:59'";
	$countnowData = $this->query($countnowsql);
	
	$data['thisavg'] = round($countnowData[0]['sumprice'] / date('d',strtotime($nowdate)),0);
	
	if(count($countnowData) > 1)
	{
		$data['thisavg'] = round($countnowData[0]['sumprice'] / date('d',strtotime($nowdate)),0) - round($countnowData[1]['sumprice'] / date('d',strtotime($nowdate)),0);
	}
	
	//较上月均价
	$beginLastmonth = date('Y-m-d',mktime(0,0,0,$nowmonth-1,1,$nowyear)); //上月初时间
	$countlastsql = "SELECT sum(price) as sumprice FROM marketconditions WHERE ".$where." and mconmanagedate>='" . $beginLastmonth . " 00:00:00' and mconmanagedate<='" . $e_time . " 23:59:59'";
	$countlastData = $this->query($countlastsql);
	$lastavg = round($countlastData[0]['sumprice'] / date('d',strtotime($e_time)),0); //上月均价
	$lastzhangdie = $data['thisavg'] - $lastavg;
	if($lastzhangdie == "0")
		$data['lastzhangdie'] = "-";
	else if($lastzhangdie < "0")
		$data['lastzhangdie'] = "<font color='green'>↓".($lastavg - $data['thisavg'])."</font>";
	else
		$data['lastzhangdie'] = "<font color='red'>↑".$lastzhangdie."</font>";

	if(count($countlastData) > 1)
	{
		$lastavg1 = round($countlastData[1]['sumprice'] / date('d',strtotime($e_time)),0);
		$lastzhangdie = $data['thisavg'] - ($lastavg - $lastavg1);
		if($lastzhangdie == "0")
			$data['lastzhangdie'] = "-";
		else if($lastzhangdie < "0")
			$data['lastzhangdie'] = "<font color='green'>↓".(($lastavg - $lastavg1) - $data['thisavg'])."</font>";
		else
			$data['lastzhangdie'] = "<font color='red'>↑".$lastzhangdie."</font>";
	}

	return $data;
}

public function get_CcPriceData($gcid, $onlyid, $nowdate){	

	//$sql = "SELECT changerate_tax,ftime,the_price_tax,last_price_tax FROM steelprice_info WHERE the_price_tax !=  '' AND gcid='".$gcid."' AND onlyid='".$onlyid."' AND ftime<='".$nowdate."' AND ftime>='".date('Y-m-d', strtotime($nowdate.' -2 week'))."' order by ftime desc limit 1";	
	$sql = "SELECT changerate_tax,ftime,the_price_tax,last_price_tax FROM steelprice_info WHERE the_price_tax !=  '' AND gcid='".$gcid."' AND onlyid='".$onlyid."' AND ftime<='".$nowdate."' AND ftime>='".date('Y-m-d', strtotime($nowdate.' -4 week'))."' order by ftime desc limit 1";		
	//echo $sql.'<br/>';
	$DdData=$this->query($sql);
	
	$ReturnData['date'] = $DdData[0]['ftime'];
	$ReturnData['price'] = $DdData[0]['the_price_tax'];
	$ReturnData['lastprice'] = $DdData[0]['last_price_tax'];
	
	if($DdData[0]['changerate_tax'] == 0)
		$ReturnData['zhangdie'] = "-";
	else if($DdData[0]['changerate_tax'] < 0)
	{
		$ReturnData['zhangdie'] = "<font color='green'>↓".($ReturnData['lastprice'] - $ReturnData['price'])."</font>";
	}
	else
		$ReturnData['zhangdie'] = "<font color='red'>↑".$DdData[0]['changerate_tax']."</font>";
	
	return $ReturnData;
}

public function get_lastmonthday($sdate,$edate,$priceid)
{
	$sql = "SELECT mconmanagedate from marketconditions where mconmanagedate>='".$sdate." 00:00:00' and mconmanagedate<='".$edate." 23:59:59' and marketconditions.mastertopid='".$priceid."' order by mconmanagedate";
	$DdDate = $this->GetOnes($sql);

	$lsdate=array();
	$lsdate['dacount'] = count($DdDate);
	$lsdate['lasda'] = date('Y-m-d',strtotime(end($DdDate)));
	$lsdate['firda'] = date('Y-m-d',strtotime($DdDate[0]));

	return $lsdate;
}

}
?>