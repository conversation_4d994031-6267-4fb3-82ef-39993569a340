<?php
set_time_limit(0);
include_once( "sys.conf.php" );

$pcsqllitename=array(
'0'=>array('classlist.sqlite','yun_ios.zip'),
'1'=>array('classlist_ng.sqlite','yun_ios_ng.zip'),
'2'=>array('classlist_sg.sqlite','yun_ios_sg.zip'),
'3'=>array('classlist_xg.sqlite','yun_ios_xg.zip'),
'4'=>array('classlist_xg_jgyc.sqlite','yun_ios_xg_jgyc.zip'),
'5'=>array('classlist_shansteel.sqlite','yun_ios_shansteel.zip'),
);

// sqlite分页类
class SqliteDB{
  public function __construct($mc_type){
    // 初始化数据库，并且连接数据库 数据库配置
	// if($mc_type!=1)
	// {
	// 	$this->db = new PDO('sqlite:'.dirname(__FILE__).'/yun.dat');
	// }
	// else
	// {
    //     $this->db = new PDO('sqlite:'.dirname(__FILE__).'/yun_ng.dat');
    // }
   global $pcsqllitename;
    //print_r($pcsqllitename);
   // echo $pcsqllitename[$mc_type][0];exit;
    $this->db = new PDO('sqlite:'.dirname(__FILE__).'/'.($pcsqllitename[$mc_type][0]));
    
    $this->table_name=$tab;
    $this->tab_init();
  }
  public function tab_init()
  {
	  # 表初始化,创建表
	  $tab1="CREATE TABLE OneData(cd varchar(10) ,ID int(10), mcode varchar(10),scode varchar(10) ,sdesc varchar(10),sname varchar(10),sod vrachar(10),hasleaves vrachar(10),flag int(10) );";
    //$tab1_index = "CREATE INDEX id_class ON dc_code_class(ID);";
           //$tab1_index2 = "CREATE INDEX parentid_class ON dc_code_class(ParentID);";
           // $tab1_index3 = @"CREATE INDEX parentidname_class ON dc_code_class(ParentID,sname);";
//$tab2 = "CREATE TABLE ThreeData(ParentID int(10) ,ID int (10),Data1Type varchar(10),Data1UnitConv  varchar(10),Data1UnitType varchar(10) ,Data2Type varchar(10),Data2UnitConv varchar(10),Data2UnitType varchar(10),Data3Type varchar(10),Data3UnitConv varchar(10) ,Data3UnitType varchar(10),Data4Type varchar(10),Data4UnitConv varchar(10),Data4UnitType varchar(10),Data5Type varchar(10) ,Data5UnitConv varchar(10),Data5UnitType varchar(10),Ddata1 varchar(10),Ddata2 varchar(10),Ddata4 varchar(10),Ddata5 varchar(10) ,Ddata3 varchar(10),EYear varchar(10) ,SYear varchar(10), daddsubdata varchar(10) ,daddsubhundcore varchar(10),dbaseType varchar(10),ddate varchar(10),dpredata varchar(20),dtcomparetype varchar(20),dtdesc varchar(10),dtname varchar(20),dtod varchar(10) ,dtymd varchar(10),enddata varchar(10),enddataisCurrent varchar(10),isAddSubData varchar(10),isAddSubHundCore varchar(10) ,isData2 varchar(10),isData3 varchar(10),isData4 varchar(10),isData5 varchar(10),isPreData varchar(10),isSubDatas varchar(10),isbuy varchar(10) ,naddsubdata varchar(10),naddsubhundcore varchar(10),ndata1 varchar(10),ndata2 varchar(10),ndata3 varchar(10),ndata4 varchar(10),ndata5 varchar(10),ndate varchar(10),npredata varchar(10),scode1 varchar(10),scode2 varchar(10) ,scode3 varchar(10) ,scode4 varchar(10) ,scode5 varchar(10) ,startdate varchar(10),subAtt1Db varchar(10) ,subAtt2Db varchar(10), subAtt3Db varchar(10) ,subDatasDb varchar(10),subDatasDb2 varchar(10),subDatasTitle varchar(10),techdbname varchar(20),techsqlmain varchar(20),techtablename varchar(20),TargetFlagVar varchar(20),ndata6 varchar(10),ndata7 varchar(10),ndata8 varchar(10),ndata9 varchar(10),ndata10 varchar(10),ndata11 varchar(10),ndata12 varchar(10),ndata13 varchar(10),ndata14 varchar(10),ndata15 varchar(10),ndata16 varchar(10),ndata17 varchar(10),ndata18 varchar(10),ndata19 varchar(10),ndata20 varchar(10) ,isData6 varchar(10),isData7 varchar(10),isData8 varchar(10),isData9 varchar(10),isData10 varchar(10),isData11 varchar(10),isData12 varchar(10),isData13 varchar(10),isData14 varchar(10),isData15 varchar(10),isData16 varchar(10),isData17 varchar(10),isData18 varchar(10),isData19 varchar(10),isData20 varchar(10)  ,Data6Type varchar(10),Data7Type varchar(10),Data8Type varchar(10),Data9Type varchar(10),Data10Type varchar(10),Data11Type varchar(10),Data12Type varchar(10),Data13Type varchar(10),Data14Type varchar(10),Data15Type varchar(10),Data16Type varchar(10),Data17Type varchar(10),Data18Type varchar(10),Data19Type varchar(10),Data20Type varchar(10)  ,Data6UnitConv varchar(10),Data7UnitConv varchar(10),Data8UnitConv varchar(10),Data9UnitConv varchar(10),Data10UnitConv varchar(10),Data11UnitConv varchar(10),Data12UnitConv varchar(10),Data13UnitConv varchar(10),Data14UnitConv varchar(10),Data15UnitConv varchar(10),Data16UnitConv varchar(10),Data17UnitConv varchar(10),Data18UnitConv varchar(10),Data19UnitConv varchar(10),Data20UnitConv varchar(10)  ,Data6UnitType varchar(10),Data7UnitType varchar(10),Data8UnitType varchar(10),Data9UnitType varchar(10),Data10UnitType varchar(10),Data11UnitType varchar(10),Data12UnitType varchar(10),Data13UnitType varchar(10),Data14UnitType varchar(10),Data15UnitType varchar(10),Data16UnitType varchar(10),Data17UnitType varchar(10),Data18UnitType varchar(10),Data19UnitType varchar(10),Data20UnitType varchar(10),pinzhong  varchar(10) );";


$tab2 = "CREATE TABLE ThreeData(ParentID int(10) ,ID int (10),Data1Type varchar(10),Data2Type varchar(10),Data3Type varchar(10),Data4Type varchar(10),Data5Type varchar(10) ,EYear varchar(10) ,SYear varchar(10), daddsubdata varchar(10) ,daddsubhundcore varchar(10),dbaseType varchar(10),ddate varchar(10),dpredata varchar(20),dtcomparetype varchar(20),dtdesc varchar(10),dtname varchar(20),dtod varchar(10) ,dtymd varchar(10),enddata varchar(10),enddataisCurrent varchar(10),isAddSubData varchar(10),isAddSubHundCore varchar(10) ,isPreData varchar(10),isSubDatas varchar(10),isbuy varchar(10) ,naddsubdata varchar(10),naddsubhundcore varchar(10),ndata1 varchar(10),ndata2 varchar(10),ndata3 varchar(10),ndata4 varchar(10),ndata5 varchar(10),ndate varchar(10),npredata varchar(10),scode1 varchar(10),scode2 varchar(10) ,scode3 varchar(10) ,scode4 varchar(10) ,scode5 varchar(10) ,startdate varchar(10),subAtt1Db varchar(10) ,subAtt2Db varchar(10), subAtt3Db varchar(10) ,subDatasDb varchar(10),subDatasDb2 varchar(10),subDatasTitle varchar(10),techdbname varchar(20),techsqlmain varchar(20),techtablename varchar(20),TargetFlagVar varchar(20),ndata6 varchar(10),ndata7 varchar(10),ndata8 varchar(10),ndata9 varchar(10),ndata10 varchar(10),ndata11 varchar(10),ndata12 varchar(10),ndata13 varchar(10),ndata14 varchar(10),ndata15 varchar(10),ndata16 varchar(10),ndata17 varchar(10),ndata18 varchar(10),ndata19 varchar(10),ndata20 varchar(10) ,Data6Type varchar(10),Data7Type varchar(10),Data8Type varchar(10),Data9Type varchar(10),Data10Type varchar(10),Data11Type varchar(10),Data12Type varchar(10),Data13Type varchar(10),Data14Type varchar(10),Data15Type varchar(10),Data16Type varchar(10),Data17Type varchar(10),Data18Type varchar(10),Data19Type varchar(10),Data20Type varchar(10),pinzhong  varchar(10),data_source varchar(50),unitstrings varchar(200),unitconvers varchar(200) );";


 $tab2_index = "CREATE INDEX index_ID_ThreeData on ThreeData(ID);";

$tab2_index2="CREATE INDEX index_ParentID_ThreeData on ThreeData(ParentID);";
$tab2_index2.="CREATE INDEX index_scode1_ThreeData on ThreeData(scode1);";
$tab2_index2.="CREATE INDEX index_scode2_ThreeData on ThreeData(scode2);";
$tab2_index2.="CREATE INDEX index_scode3_ThreeData on ThreeData(scode3);";
$tab2_index2.="CREATE INDEX index_scode4_ThreeData on ThreeData(scode4);";
$tab2_index2.="CREATE INDEX index_scode5_ThreeData on ThreeData(scode5);";
$tab2_index2.="CREATE INDEX index_isbuy_ThreeData on ThreeData(isbuy);";
 
 $tab3 = "CREATE TABLE FourData(Parent int(10),ID int(10),aod varchar(10),satt1 varchar(10), satt2 varchar(10),satt3 varchar(10) ,scode varchar(10),sdbtype varchar(10),sname varchar(20));";

$tab3_index ="CREATE INDEX index_ID_FourData on FourData(ID);";

 $tab3_index2 = "CREATE INDEX index_Parent_FourData on FourData(Parent);";
           
 $tab4 ="CREATE TABLE SearchData(ParentID int (10), ID int(10), DTID1 varchar(10),DTID1Sub varchar(10) ,DTID2 varchar(10),DTID2Sub varchar(10),DTID3 vrachar(10),DTID3Sub varchar(10),DTID4 varchar(10) ,DTID4Sub varchar(10),Data1AddSub varchar(10),Data1AddSubHundCore vrachar(10),Data1Image varchar(10),Data1Pre varchar(10) ,Data1Type varchar(10),Data1Type1 varchar(10),Data1Type2 varchar(10),Data1Type3 vrachar(10),Data1Type4 varchar(10),Data1Type5 varchar(10),Data2Image varchar(10) ,Data2Type varchar(10),Data3Image varchar(10) ,Data3Type varchar(10), Data4Image varchar(10) ,Data4Type varchar(10),ImageType varchar(10),aod varchar(10),dtname vrachar(20),dtnameshort varchar(20),DTIDJson TEXT);";

$tab4_index = "CREATE INDEX index_ID_SearchData on SearchData(ID);";

$tab4_index2=" CREATE INDEX index_ParentID_SearchData on SearchData(ParentID);";

$tab5="CREATE TABLE SecondData(fatherID int(10),ID int(10), mcode varchar(10),scode varchar(10) ,sdesc varchar(10),sname varchar(10),sod vrachar(10),hasnode vrachar(10),hasleaves vrachar(10) ,isLoad vrachar(10),flag int(10));";

$tab5_index="CREATE INDEX index_ID_SecondData on SecondData(ID);";

$tab5_index2="CREATE INDEX index_fatherID_SecondData on SecondData(fatherID);";

$tab6 = "CREATE TABLE CUSTOM_DFCONF(ID int (10),dfkey varchar(20),dfvalue varchar(20),remark varchar(20),dfod int (10))";

			$tabs = "";
            if (!$this->ExistsTab( "OneData"))
            {
                $tabs .= $tab1;
                $tabs .= $tab1_index;
                $tabs .= $tab1_index2;
                //tabs += tab1_index3;
            }
			else
	        {
              $tabs .= " delete from OneData; ";
			}
            if (!$this->ExistsTab("ThreeData"))
            {
                $tabs .= $tab2;
                $tabs .= $tab2_index;
                $tabs .= $tab2_index2;
            }
			else
	        {
              $tabs .= " delete from ThreeData; ";
			}
            if (!$this->ExistsTab( "FourData"))
            {
                $tabs .= $tab3;
                $tabs .= $tab3_index;
                $tabs .= $tab3_index2;
            }
			else
	        {
              $tabs .= " delete from FourData; ";
			}
            if (!$this->ExistsTab( "SearchData"))
            {
                $tabs .= $tab4;
                $tabs .= $tab4_index;
				$tabs .= $tab4_index2;
            }
			else
	        {
              $tabs .= " delete from SearchData; ";
			}
            if (!$this->ExistsTab( "SecondData"))
            {
                $tabs .= $tab5;
                $tabs .= $tab5_index;
				$tabs .= $tab5_index2;
            }
			else
	        {
              $tabs .= " delete from SecondData; ";
			}
			
			if (!$this->ExistsTab( "CUSTOM_DFCONF"))
            {
                $tabs .= $tab6;
            }
			else
	        {
              $tabs .= " delete from CUSTOM_DFCONF; ";
			}
			
             print_r($tabs);
			if($tabs!='')
			{
				 $this->db->exec($tabs);//创建表并加索引
			}
  
    // $this->db->exec("CREATE TABLE log(
      // id integer PRIMARY KEY autoincrement,
      // urls varchar(200),
      // ip varchar(200),
      // datetimes datetime default (datetime('now', 'localtime'))
      // )");
  }
  
  public function ExistsTab($tab_name){
  
	    $cmd= "SELECT COUNT(*) c FROM sqlite_master where type='table' and name='". $tab_name ."';";
		$sth = $this->db->prepare($cmd);
		$sth->execute();
		$result = $sth->fetchAll();
		print_r($result);
		$total= $result[0]['c'];   
    
		   if (0 == $total)
            {  
                return false;
            }
            else
            {
                return true;
            }  
  }
  
  public function insert($tab_name,$key_list,$value_list)
  {
    $str="INSERT INTO ".$tab_name." (".$key_list.") values(".$value_list.");";
    //$result=$this->db->exec("INSERT INTO ".$tab_name." (".$key_list.") values(".$value_list.")");
    if (!$result) {
      return false;
    }
	return $str;
    // echo "{{{INSERT INTO ".$tab_name." (".$key_list.") values(".$value_list.")}}}}";
    //$res=$this->db->beginTransaction();//事务回gun
  }
   public function insertMore($tab_name)
  {
    //开启事物,此时会关闭自动提交 
    $this->db->beginTransaction();
    $sql = "INSERT INTO ".$tab_name." (id,urls,ip) values (?,?,?)";
    $stmt = $this->db->prepare($sql);
    //传入参数
	while($s <= 99999){
    $stmt->execute(array(null,"test".$s,"w"));
	$s++;
    $stmt->execute(array(null,"test5","w"));
    $stmt->execute(array(null,"test3","w"));
	}
    //提交事物，并且 数据库连接返回到自动提交模式
    $this->db->commit();
  }
  
  public function insert_dc_search_system($result,$insert_dc_search_system)
  {
	  try{
    //开启事物,此时会关闭自动提交 
    $this->db->beginTransaction();
    $sql = "insert into SearchData(ParentID,ID,DTID1,DTID1Sub,DTID2,DTID2Sub,DTID3,DTID3Sub,DTID4,DTID4Sub,Data1AddSub,Data1AddSubHundCore,Data1Image,Data1Pre,Data1Type,Data1Type1,Data1Type2,Data1Type3,Data1Type4,Data1Type5,Data2Image ,Data2Type,Data3Image ,Data3Type, Data4Image ,Data4Type,ImageType,aod,dtname,dtnameshort,DTIDJson) values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?);";
    $stmt = $this->db->prepare($sql);
    //传入参数
	foreach($result as $item){

         $stmt->execute(array($insert_dc_search_system[$item["scode1"]],$item["ID"],$item["DTID1"],$item["DTID1Sub"],$item["DTID2"],$item["DTID2Sub"],$item["DTID3"],$item["DTID3Sub"],$item["DTID4"],$item["DTID4Sub"],$item["Data1AddSub"],$item["Data1AddSubHundCore"],$item["Data1Image"],$item["Data1Pre"],$item["Data1Type"],$item["Data1Type1"],$item["Data1Type2"],$item["Data1Type3"],$item["Data1Type4"],$item["Data1Type5"],$item["Data2Image"],$item["Data2Type"],$item["Data3Image"],$item["Data3Type"],$item["Data4Image"],$item["Data4Type"],$item["ImageType"],$item["aod"],$item["dtname"],$item["dtnameshort"],$item["DTIDJson"]));

	}
    //提交事物，并且 数据库连接返回到自动提交模式
    $this->db->commit();
	}catch(PDOException $e){
		echo '执行失败'.$e->getMessage();
	}
  }
  
  
  public function insert_dc_code_class($result,$newresultscode,$newresultmcode,$VersionNo,$newdc_code_datatype)
  {

    /* foreach($result as $item)
	 {
		 $newresultscode[$item["scode"]]=$item['ID'];
		 $newresultmcode[$item["mcode"]]=$item['ID'];
	 }*/
   //print_r($newresultscode);
	  try{
    //开启事物,此时会关闭自动提交 
    $this->db->beginTransaction();
    $sql = "insert into SecondData (fatherID,ID, mcode,scode,sdesc,sname,sod,hasnode,hasleaves,flag) values(?,?,?,?,?,?,?,?,?,?)";
    $stmt = $this->db->prepare($sql);
    $VersionNo=number_format($VersionNo+0.01, 2);
	$sql1 = "insert into OneData(cd,ID, mcode,scode,sdesc,sname,sod,hasleaves,flag)values('".$VersionNo."',?,?,?,?,?,?,?,?)";
    $stmt1 = $this->db->prepare($sql1);

    //传入参数
	foreach($result as $item){
		$ParentID=empty($newresultscode[$item["mcode"]])?0:$newresultscode[$item["mcode"]];
        $hasnode=empty($newresultmcode[$item["scode"]])?0:1;
       $hasleaves=empty($newdc_code_datatype[$item["scode"]])?0:1;
       //$hasleaves=$hasnode==0?"0":$hasleaves;
		//$stmt->execute(array($item["ID"],$item["sname"],$item["scode"],$item["snameshort"],$hasnode,$ParentID));
      if($ParentID!=0)
	  {
        $stmt->execute(array($ParentID,$item["ID"],$item["mcode"],$item["scode"],$item["sdesc"],$item["sname"],$item["sod"],$hasnode,$hasleaves,$item["flag"]));
	  }
	  else if($item["mcode"]=='')
	  {
		  $stmt1->execute(array($item["ID"],$item["mcode"],$item["scode"],$item["sdesc"],$item["sname"],$item["sod"],$hasleaves,$item["flag"]));
	  } 

        

	}
    //提交事物，并且 数据库连接返回到自动提交模式
    $this->db->commit();
	}catch(PDOException $e){
		echo '执行失败'.$e->getMessage();
	}
  }
  
  
  public function insert_dc_code_datatype($result,$newresultscode,$resultDIDarr)
  {
	try{
    //开启事物,此时会关闭自动提交 
    $this->db->beginTransaction();
    
   
	
	//$sql="insert into ThreeData(ParentID ,ID ,Data1Type,Data1UnitConv,Data1UnitType,Data2Type,Data2UnitConv,Data2UnitType,Data3Type,Data3UnitConv,Data3UnitType,Data4Type,Data4UnitConv,Data4UnitType,Data5Type ,Data5UnitConv ,Data5UnitType ,Ddata1,Ddata2,Ddata4,Ddata5,Ddata3,EYear,SYear, daddsubdata,daddsubhundcore,dbaseType,ddate,dpredata,dtcomparetype,dtdesc,dtname,dtod,dtymd,enddata,enddataisCurrent,isAddSubData ,isAddSubHundCore,isData2,isData3,isData4,isData5,isPreData,isSubDatas,isbuy,naddsubdata,naddsubhundcore ,ndata1 ,ndata2,ndata3,ndata4,ndata5,ndate ,npredata,scode1,scode2,scode3,scode4 ,scode5 ,startdate ,subAtt1Db,subAtt2Db, subAtt3Db,subDatasDb,subDatasDb2,subDatasTitle,techdbname,techsqlmain,techtablename,TargetFlagVar,ndata6,ndata7,ndata8,ndata9,ndata10 ,ndata11 ,ndata12 ,ndata13 ,ndata14 ,ndata15 ,ndata16 ,ndata17 ,ndata18 ,ndata19 ,ndata20  ,isData6 ,isData7 ,isData8 ,isData9 ,isData10 ,isData11 ,isData12 ,isData13 ,isData14 ,isData15 ,isData16 ,isData17 ,isData18 ,isData19 ,isData20,Data6Type ,Data7Type ,Data8Type ,Data9Type ,Data10Type ,Data11Type ,Data12Type ,Data13Type ,Data14Type ,Data15Type ,Data16Type ,Data17Type,Data18Type,Data19Type,Data20Type,Data6UnitConv ,Data7UnitConv ,Data8UnitConv ,Data9UnitConv ,Data10UnitConv ,Data11UnitConv ,Data12UnitConv ,Data13UnitConv ,Data14UnitConv ,Data15UnitConv ,Data16UnitConv ,Data17UnitConv,Data18UnitConv ,Data19UnitConv ,Data20UnitConv,Data6UnitType ,Data7UnitType,Data8UnitType,Data9UnitType ,Data10UnitType ,Data11UnitType ,Data12UnitType ,Data13UnitType ,Data14UnitType ,Data15UnitType ,Data16UnitType,Data17UnitType,Data18UnitType ,Data19UnitType ,Data20UnitType,pinzhong)values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

	$sql="insert into ThreeData(ParentID ,ID ,Data1Type,Data2Type,Data3Type,Data4Type,Data5Type ,EYear,SYear, daddsubdata,daddsubhundcore,dbaseType,ddate,dpredata,dtcomparetype,dtdesc,dtname,dtod,dtymd,enddata,enddataisCurrent,isAddSubData ,isAddSubHundCore,isPreData,isSubDatas,isbuy,naddsubdata,naddsubhundcore ,ndata1 ,ndata2,ndata3,ndata4,ndata5,ndate ,npredata,scode1,scode2,scode3,scode4 ,scode5 ,startdate ,subAtt1Db,subAtt2Db, subAtt3Db,subDatasDb,subDatasDb2,subDatasTitle,techdbname,techsqlmain,techtablename,TargetFlagVar,ndata6,ndata7,ndata8,ndata9,ndata10 ,ndata11 ,ndata12 ,ndata13 ,ndata14 ,ndata15 ,ndata16 ,ndata17 ,ndata18 ,ndata19 ,ndata20  ,Data6Type ,Data7Type ,Data8Type ,Data9Type ,Data10Type ,Data11Type ,Data12Type ,Data13Type ,Data14Type ,Data15Type ,Data16Type ,Data17Type,Data18Type,Data19Type,Data20Type,pinzhong,data_source,unitstrings,unitconvers)values(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
	
	
	$stmt = $this->db->prepare($sql);
	//print_r($result);
    //传入参数
	foreach($result as $item){

             if(!empty($item['scode5']))
		   {
              $pid=$newresultscode[$item['scode5']];
			 
		   }
            else if(!empty($item['scode4']))
		   {
              $pid=$newresultscode[$item['scode4']];
			 
		   }
		   else if(!empty($item['scode3']))
		   {
              $pid=$newresultscode[$item['scode3']];
			   
		   }
		   else if(!empty($item['scode2']))
		   {
              $pid=$newresultscode[$item['scode2']];
		   }
		   else if(!empty($item['scode1']))
		   {
              $pid=$newresultscode[$item['scode1']];

		   }
           $item["isSubDatas"]=0;
           if(is_array($resultDIDarr))
                $item["isSubDatas"]=in_array($item["ID"],$resultDIDarr)?1:0;
		   //$item["isSubDatas"]=in_array($item["ID"],$resultDIDarr)?1:0;
           //$stmt->execute(array($pid,$item["ID"],$item["Data1Type"],$item["Data1UnitConv"],$item["Data1UnitType"],$item["Data2Type"],$item["Data2UnitConv"],$item["Data2UnitType"],$item["Data3Type"],$item["Data3UnitConv"],$item["Data3UnitType"],$item["Data4Type"],$item["Data4UnitConv"],$item["Data4UnitType"],$item["Data5Type"],$item["Data5UnitConv"],$item["Data5UnitType"],$item["Ddata1"],$item["Ddata2"],$item["Ddata4"],$item["Ddata5"],$item["Ddata3"],'2020','2010',$item["daddsubdata"],$item["daddsubhundcore"],$item["dbaseType"],$item["ddate"],$item["dpredata"],$item["dtcomparetype"],$item["dtdesc"],$item["dtname"],$item["dtod"],$item["dtymd"],$item["enddata"],$item["enddataisCurrent"],$item["isAddSubData"],$item["isAddSubHundCore"],$item["isData2"],$item["isData3"],$item["isData4"],$item["isData5"],$item["isPreData"],$item["isSubDatas"],0,$item["naddsubdata"],$item["naddsubhundcore "],$item["ndata1 "],$item["ndata2"],$item["ndata3"],$item["ndata4"],$item["ndata5"],$item["ndate "],$item["npredata"],$item["scode1"],$item["scode2"],$item["scode3"],$item["scode4"],$item["scode5"],$item["startdate"],$item["subAtt1Db"],$item["subAtt2Db"],$item["subAtt3Db"],$item["subDatasDb"],$item["subDatasDb2"],$item["subDatasTitle"],$item["techdbname"],$item["techsqlmain"],$item["techtablename"],$item["TargetFlagVar"],$item["ndata6"],$item["ndata7"],$item["ndata8"],$item["ndata9"],$item["ndata10 "],$item["ndata11 "],$item["ndata12 "],$item["ndata13 "],$item["ndata14 "],$item["ndata15 "],$item["ndata16 "],$item["ndata17 "],$item["ndata18 "],$item["ndata19 "],$item["ndata20  "],$item["isData6 "],$item["isData7 "],$item["isData8 "],$item["isData9 "],$item["isData10 "],$item["isData11 "],$item["isData12 "],$item["isData13 "],$item["isData14 "],$item["isData15 "],$item["isData16 "],$item["isData17 "],$item["isData18 "],$item["isData19 "],$item["isData20"],$item["Data6Type "],$item["Data7Type "],$item["Data8Type "],$item["Data9Type "],$item["Data10Type "],$item["Data11Type "],$item["Data12Type "],$item["Data13Type "],$item["Data14Type "],$item["Data15Type "],$item["Data16Type "],$item["Data17Type"],$item["Data18Type"],$item["Data19Type"],$item["Data20Type"],$item["Data6UnitConv "],$item["Data7UnitConv "],$item["Data8UnitConv "],$item["Data9UnitConv "],$item["Data10UnitConv "],$item["Data11UnitConv "],$item["Data12UnitConv "],$item["Data13UnitConv "],$item["Data14UnitConv "],$item["Data15UnitConv "],$item["Data16UnitConv "],$item["Data17UnitConv"],$item["Data18UnitConv "],$item["Data19UnitConv"],$item["Data20UnitConv"],$item["Data6UnitType"],$item["Data7UnitType"],$item["Data8UnitType"],$item["Data9UnitType "],$item["Data10UnitType "],$item["Data11UnitType "],$item["Data12UnitType "],$item["Data13UnitType "],$item["Data14UnitType "],$item["Data15UnitType "],$item["Data16UnitType"],$item["Data17UnitType"],$item["Data18UnitType "],$item["Data19UnitType "],$item["Data20UnitType"],$item["pinzhong"]));
		   $unitstrings = $item["UnitTypeName"].','.$item["npredata_UnitTypeName"].','.$item["naddsubdata_UnitTypeName"].','.$item["daddsubhundcore_UnitTypeName"].','.$item["UnitTypeName2"].','.$item["UnitTypeName3"].','.$item["UnitTypeName4"].','.$item["UnitTypeName5"].','.$item["UnitTypeName6"].','.$item["UnitTypeName7"].','.$item["UnitTypeName8"].','.$item["UnitTypeName9"].','.$item["UnitTypeName10"].','.$item["UnitTypeName11"].','.$item["UnitTypeName12"].','.$item["UnitTypeName13"].','.$item["UnitTypeName14"].','.$item["UnitTypeName15"].','.$item["UnitTypeName16"].','.$item["UnitTypeName17"].','.$item["UnitTypeName18"].','.$item["UnitTypeName19"].','.$item["UnitTypeName20"];

		   $unitconvers = $item["Data1UnitConv"].','.$item["npredata_UnitConv"].','.$item["naddsubdata_UnitConv"].','.$item["daddsubhundcore_UnitConv"].','.$item["Data2UnitConv"].','.$item["Data3UnitConv"].','.$item["Data4UnitConv"].','.$item["Data5UnitConv"].','.$item["Data6UnitConv"].','.$item["Data7UnitConv"].','.$item["Data8UnitConv"].','.$item["Data9UnitConv"].','.$item["Data10UnitConv"].','.$item["Data11UnitConv"].','.$item["Data12UnitConv"].','.$item["Data13UnitConv"].','.$item["Data14UnitConv"].','.$item["Data15UnitConv"].','.$item["Data16UnitConv"].','.$item["Data17UnitConv"].','.$item["Data18UnitConv"].','.$item["Data19UnitConv"].','.$item["Data20UnitConv"];

		    $stmt->execute(array($pid,$item["ID"],$item["Data1Type"],$item["Data2Type"],$item["Data3Type"],$item["Data4Type"],$item["Data5Type"],2020,2010,$item["daddsubdata"],$item["daddsubhundcore"],$item["dbaseType"],$item["ddate"],$item["dpredata"],$item["dtcomparetype"],$item["dtdesc"],$item["dtname"],$item["dtod"],$item["dtymd"],$item["enddata"],$item["enddataisCurrent"],$item["isAddSubData"],$item["isAddSubHundCore"],$item["isPreData"],$item["isSubDatas"],0,$item["naddsubdata"],$item["naddsubhundcore"],$item["ndata1"],$item["ndata2"],$item["ndata3"],$item["ndata4"],$item["ndata5"],$item["ndate"],$item["npredata"],$item["scode1"],$item["scode2"],$item["scode3"],$item["scode4"],$item["scode5"],$item["startdate"],$item["subAtt1Db"],$item["subAtt2Db"],$item["subAtt3Db"],$item["subDatasDb"],$item["subDatasDb2"],$item["subDatasTitle"],$item["techdbname"],$item["techsqlmain"],$item["techtablename"],$item["TargetFlagVar"],$item["ndata6"],$item["ndata7"],$item["ndata8"],$item["ndata9"],$item["ndata10"],$item["ndata11"],$item["ndata12"],$item["ndata13"],$item["ndata14"],$item["ndata15"],$item["ndata16"],$item["ndata17"],$item["ndata18"],$item["ndata19"],$item["ndata20"],$item["Data6Type"],$item["Data7Type"],$item["Data8Type"],$item["Data9Type"],$item["Data10Type"],$item["Data11Type"],$item["Data12Type"],$item["Data13Type"],$item["Data14Type"],$item["Data15Type"],$item["Data16Type"],$item["Data17Type"],$item["Data18Type"],$item["Data19Type"],$item["Data20Type"],$item["pinzhong"],$item["data_source"],$unitstrings,$unitconvers));
			
	}
    //提交事物，并且 数据库连接返回到自动提交模式
    $this->db->commit();
	}catch(PDOException $e){
		echo '执行失败'.$e->getMessage();
	}
  }
  
  
  
   public function insert_dc_code_datatype_subs($result)
  {
	try{
    //开启事物,此时会关闭自动提交 
    $this->db->beginTransaction();
    $sql = "insert into FourData(Parent,ID,aod,satt1, satt2,satt3,scode,sdbtype,sname)values(?,?,?,?,?,?,?,?,?)";
    $stmt = $this->db->prepare($sql);
	//print_r($result);
    //传入参数
	foreach($result as $item){
		$stmt->execute(array($item["DID"],$item["ID"],$item["aod"],$item["satt1"],$item["satt2"],$item["satt3"],$item["scode"],$item['sdbtype'],$item["sname"],));
	}
    //提交事物，并且 数据库连接返回到自动提交模式
    $this->db->commit();
	}catch(PDOException $e){
		echo '执行失败'.$e->getMessage();
	}
  }
  
  public function insert_dc_search_custom_dfconf($result)
  {
	try{
    //开启事物,此时会关闭自动提交 
    $this->db->beginTransaction();
    $sql = "insert into CUSTOM_DFCONF (ID,dfkey,dfvalue,remark,dfod )values(?,?,?,?,?)";
    $stmt = $this->db->prepare($sql);
	//print_r($result);
    //传入参数
	foreach($result as $item){
		$stmt->execute(array($item["id"],$item["dfkey"],$item["dfvalue"],$item['remark'],$item["dfod"]));
	}
    //提交事物，并且 数据库连接返回到自动提交模式
    $this->db->commit();
	}catch(PDOException $e){
		echo '执行失败'.$e->getMessage();
	}
  }
  
  public function total($tab_name,$tj='')//求总记录数目
  {
    $sth = $this->db->prepare('SELECT count(id) as c FROM '.$tab_name.' '.$tj);
    $sth->execute();
    $result = $sth->fetchAll();
    return $result[0]['c'];
  }
  
  public function totalxx($sql)//求总记录数目
  {
    $sth = $this->db->prepare($sql);
    $sth->execute();
    $result = $sth->fetchAll();
    return $result[0]['c'];
  }
  public function update()
  {
    # 修改
  }
  function delete($value='')
  {
    # 删除
  }
  public function query($tab_name,$tj='')//表名称和条件
  {
    $sth = $this->db->prepare('SELECT * FROM '.$tab_name.' '.$tj);
    // echo 'SELECT * FROM '.$tab_name.' '.$tj;
    $sth->execute();
    $result = $sth->fetchAll();
    return $result;
  }
}



function array_iconv($str, $in_charset="gbk", $out_charset="utf-8")
{
    return $str;
 if(is_array($str))
 {
 foreach($str as $k => $v)
 {
  $str[$k] = array_iconv($v);
 }
 return $str;
 }
 else
 {
 if(is_string($str))
 {
  // return iconv('UTF-8', 'GBK//IGNORE', $str);
  return mb_convert_encoding($str, $out_charset, $in_charset);
 }
 else
 {
  return $str;
 }
 }
}

if($argv[1])
{
 $_REQUEST['mc_type']=$argv[1];
}

$mc_type=0;
if($_REQUEST['mc_type'])
{
   $mc_type=$_REQUEST['mc_type'];
}

if(!in_array($mc_type,array(0,1,2,3,4,5)))
{
   echo "错误的mc_type，请检查后再试。<br/>";
   exit;
}


$db=new SqliteDB($mc_type);



//$dsn = 'mysql:dbname=steelhome_t1;host=***********;port=4306';
//$user = 'root2';
//$password = '123456';

///$dsn = 'mysql:dbname=steelhome_t1;host=*************;port=3307';
//$user = 'dbread';
//$password = 'sth@50581010';

//采用预处理+事务处理执行SQL操作
//1.连接数据库
try {
    $pdo = new PDO($dsn, $user, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE,PDO::ERRMODE_EXCEPTION);
	echo "ok ";
} catch (PDOException $e) {
    die("数据库连接失败".$e->getMessage());
}
 
//2.执行数据操作
try{
	
	 $count_sql = "select VersionNo  from app_version where  VersionType=2 and  mc_type='".$mc_type."' and Status=1 limit 1";
	 $sth = $pdo->prepare($count_sql);
     $sth->execute();
     $resultVersionNo = $sth->fetchAll();
	 
    $sql="SELECT  `scode1` ,  `scode2` ,  `scode3` ,  `scode4` ,  `scode5` FROM  `dc_code_datatype` WHERE  `mc_type` ='".$mc_type."'  AND STATUS =1";
    if($mc_type=="0")
    {
      $sql="SELECT  `scode1` ,  `scode2` ,  `scode3` ,  `scode4` ,  `scode5` FROM  `dc_code_datatype` WHERE  `mc_type` ='".$mc_type."'  and scode1 not in ('K','L','O','P','Q','R') AND STATUS =1";
     }
    $sth = $pdo->prepare($sql);
     $sth->execute();
     $result = $sth->fetchAll();
    foreach($result as $item)
	 {
		 if(!empty($item['scode5']))
		   {
              $pid=$item['scode5'];
			 
		   }
		   else if(!empty($item['scode4']))
		   {
              $pid=$item['scode4'];
			 
		   }
		   else if(!empty($item['scode3']))
		   {
              $pid=$item['scode3'];
			   
		   }
		   else if(!empty($item['scode2']))
		   {
              $pid=$item['scode2'];
		   }
		   else if(!empty($item['scode1']))
		   {
              $pid=$item['scode1'];

		   }

		 $newdc_code_datatype[$pid]=1;
	 }

	 
     //$count_sql = "select ID,mcode,sname as sname,snameshort,scode,sod,sdesc,0 as hasnode from dc_code_class where   mc_type='".$mc_type."' and Status =1 order by sod";

     if( $mc_type!=2)
	 {
		$count_sql = "select ID,mcode,sname as sname,snameshort,scode,sod,sdesc,0 as hasnode,flag from dc_code_class where   mc_type='".$mc_type."' and Status =1 order by sod";
	 }
	 else
	 {
		$count_sql = "select ID,mcode,sname as sname,snameshort,scode,sod,sdesc,0 as hasnode,flag from dc_code_class where   mc_type='".$mc_type."' and scode!='AE' and Status =1 order by sod";
	 }


     if($mc_type=="0")
     {
      $count_sql = "select ID,mcode,sname as sname,snameshort,scode,sod,sdesc,0 as hasnode,flag from dc_code_class where   mc_type='".$mc_type."' and scode not in ('K','L','O','P','Q','R')  and Status =1 order by sod";
     }
     $sth = $pdo->prepare($count_sql);
     $sth->execute();
     $result = $sth->fetchAll();

     $result=array_iconv($result);
      foreach($result as $item)
	 {
		 $newresultscode[$item["scode"]]=$item['ID'];
		 $newresultmcode[$item["mcode"]]=$item['ID'];
	 }
      

	 $db->insert_dc_code_class($result, $newresultscode, $newresultmcode,$resultVersionNo[0]['VersionNo'],$newdc_code_datatype);
	 echo "插入dc_code_class操作完成<br/>";
	




     $count_sql = "select ID,scode1,dtname as dtname,dtnameshort as dtnameshort,ImageType,DTID1,DTID1Sub, Data1Type, Data1Type1, Data1Image, Data1Pre, Data1AddSub, Data1AddSubHundCore, Data1Type2, Data1Type3, Data1Type4, Data1Type5, DTID2 , DTID2Sub, Data2Type ,Data2Image, DTID3 , DTID3Sub , Data3Type , Data3Image, DTID4 , DTID4Sub , Data4Type , Data4Image,DTIDJson, zhou1,zhou2,zhou3,zhou4, isjz,isbg,isfg,color,aod from dc_search_system where IsUse='0' and mc_type='".$mc_type."' order by aod";
	 $sth = $pdo->prepare($count_sql);
    
     $sth->execute();
     $result = $sth->fetchAll();
    
	  $db->insert_dc_search_system(array_iconv($result),$newresultscode);
	  echo "插入dc_search_system操作完成<br/>";
	 
$count_sql = "select distinct DID from dc_code_datatype_subs,dc_code_datatype where dc_code_datatype_subs.did= dc_code_datatype.id and dc_code_datatype_subs.Status =1 and dc_code_datatype.Status =1  and dc_code_datatype_subs.mc_type='".$mc_type."' and dc_code_datatype.mc_type='".$mc_type."'"; 
$sth = $pdo->prepare($count_sql);
$sth->execute();
$result = $sth->fetchAll();
foreach($result as $item)
 {
	 $resultDIDarr[]=$item['DID'];
 }



	$count_sql = "select count(*) as c from dc_code_datatype  where mc_type='".$mc_type."' and Status =1 and ID not in(".$NotDIDStr.")";
	$sth = $pdo->prepare($count_sql);
    $sth->execute();
    $result = $sth->fetchAll();
    $totle= $result[0]['c'];
	$pagesize=1000;
    $pagecount=ceil($totle/$pagesize);
    for($i = 0;$i<=$pagecount;$i++){
        // $start = $i;
        // $end = $i + 10000;
       // $sql = "select ID ,Data1Type,Data1UnitConv,Data1UnitType,Data2Type,Data2UnitConv,Data2UnitType,Data3Type,Data3UnitConv,Data3UnitType,Data4Type,Data4UnitConv,Data4UnitType,Data5Type ,Data5UnitConv ,Data5UnitType ,Ddata1,Ddata2,Ddata4,Ddata5,Ddata3, daddsubdata,daddsubhundcore,dbaseType,ddate,dpredata,dtcomparetype,dtdesc,dtname,dtod,dtymd,enddata,enddataisCurrent,isAddSubData ,isAddSubHundCore,isData2,isData3,isData4,isData5,isPreData,isSubDatas,naddsubdata,naddsubhundcore ,ndata1 ,ndata2,ndata3,ndata4,ndata5,ndate ,npredata,scode1,scode2,scode3,scode4 ,scode5 ,startdate ,subAtt1Db,subAtt2Db, subAtt3Db,subDatasDb,subDatasDb2,subDatasTitle,techdbname,techsqlmain,techtablename,TargetFlagVar,ndata6,ndata7,ndata8,ndata9,ndata10 ,ndata11 ,ndata12 ,ndata13 ,ndata14 ,ndata15 ,ndata16 ,ndata17 ,ndata18 ,ndata19 ,ndata20  ,isData6 ,isData7 ,isData8 ,isData9 ,isData10 ,isData11 ,isData12 ,isData13 ,isData14 ,isData15 ,isData16 ,isData17 ,isData18 ,isData19 ,isData20,Data6Type ,Data7Type ,Data8Type ,Data9Type ,Data10Type ,Data11Type ,Data12Type ,Data13Type ,Data14Type ,Data15Type ,Data16Type ,Data17Type,Data18Type,Data19Type,Data20Type,Data6UnitConv ,Data7UnitConv ,Data8UnitConv ,Data9UnitConv ,Data10UnitConv ,Data11UnitConv ,Data12UnitConv ,Data13UnitConv ,Data14UnitConv ,Data15UnitConv ,Data16UnitConv ,Data17UnitConv,Data18UnitConv ,Data19UnitConv ,Data20UnitConv,Data6UnitType ,Data7UnitType,Data8UnitType,Data9UnitType ,Data10UnitType ,Data11UnitType ,Data12UnitType ,Data13UnitType ,Data14UnitType ,Data15UnitType ,Data16UnitType,Data17UnitType,Data18UnitType ,Data19UnitType ,Data20UnitType,pinzhong from dc_code_datatype,dc_code_datatype_db where dc_code_datatype_db.DID = dc_code_datatype.ID and dc_code_datatype.mc_type='0' and dc_code_datatype_db.mc_type='0' and Status =1 order by dtod limit  ".($i*$pagesize).",$pagesize";
	$dataunitconv = "Data1UnitConv,UnitTypeName,Data2UnitConv,UnitTypeName2,Data3UnitConv,UnitTypeName3,Data4UnitConv,UnitTypeName4,Data5UnitConv,UnitTypeName5,Data6UnitConv,UnitTypeName6,Data7UnitConv,UnitTypeName7,Data8UnitConv,UnitTypeName8,Data9UnitConv,UnitTypeName9,Data10UnitConv,UnitTypeName10,Data11UnitConv,UnitTypeName11,Data12UnitConv,UnitTypeName12,Data13UnitConv,UnitTypeName13,Data14UnitConv,UnitTypeName14,Data15UnitConv,UnitTypeName15,Data16UnitConv,UnitTypeName16,Data17UnitConv,UnitTypeName17,Data18UnitConv,UnitTypeName18,Data19UnitConv,UnitTypeName19,Data20UnitConv,UnitTypeName20,npredata_UnitConv,dc_code_datatype_db.npredataDW as npredata_UnitTypeName,naddsubdata_UnitConv,dc_code_datatype_db.naddsubdataDW as naddsubdata_UnitTypeName,daddsubhundcore_UnitConv,dc_code_datatype_db.naddsubhundcoreDW as daddsubhundcore_UnitTypeName";

      $sql = "select  ID ,Data1Type,Data2Type,Data3Type,Data4Type,Data5Type ,daddsubdata,daddsubhundcore,dbaseType,ddate,dpredata,dtcomparetype,dtdesc,dtname,dtod,dtymd,enddata,enddataisCurrent,isAddSubData ,isAddSubHundCore,isPreData,isSubDatas,naddsubdata,naddsubhundcore ,ndata1 ,ndata2,ndata3,ndata4,ndata5,ndate ,npredata,scode1,scode2,scode3,scode4 ,scode5 ,startdate ,subAtt1Db,subAtt2Db, subAtt3Db,subDatasDb,subDatasDb2,subDatasTitle,techdbname,techsqlmain,techtablename,TargetFlagVar,ndata6,ndata7,ndata8,ndata9,ndata10 ,ndata11 ,ndata12 ,ndata13 ,ndata14 ,ndata15 ,ndata16 ,ndata17 ,ndata18 ,ndata19 ,ndata20  ,Data6Type ,Data7Type ,Data8Type ,Data9Type ,Data10Type ,Data11Type ,Data12Type ,Data13Type ,Data14Type ,Data15Type ,Data16Type ,Data17Type,Data18Type,Data19Type,Data20Type,pinzhong,data_source,".$dataunitconv."  from dc_code_datatype,dc_code_datatype_db where dc_code_datatype_db.DID = dc_code_datatype.ID and dc_code_datatype.mc_type='".$mc_type."' and dc_code_datatype_db.mc_type='".$mc_type."' and Status =1 and ID not in(".$NotDIDStr.") order by dtod limit  ".($i*$pagesize).",$pagesize";

       



		
		//echo $sql;
        $sth = $pdo->prepare($sql);
		$sth->execute();
		$result = $sth->fetchAll();
        //if(is_array($resultDIDarr))
		    $db->insert_dc_code_datatype(array_iconv($result),$newresultscode,$resultDIDarr);
    }
	 echo "插入dc_code_datatype操作完成<br/>";
	
	 
		$count_sql = "select count(*) as c from dc_code_datatype_subs ,dc_code_datatype where dc_code_datatype_subs.did= dc_code_datatype.id and dc_code_datatype_subs.Status =1  and dc_code_datatype.Status =1  and dc_code_datatype_subs.mc_type='".$mc_type."' and dc_code_datatype.isSubDatas=1";
	$sth = $pdo->prepare($count_sql);
    $sth->execute();
    $result = $sth->fetchAll();
    $totle= $result[0]['c'];
	echo $totle;
	$pagesize=1000;
    $pagecount=ceil($totle/$pagesize);
    for($i = 0;$i<=$pagecount;$i++){
        // $start = $i;
        // $end = $i + 10000;
        $sql = "select dc_code_datatype_subs.ID,DID,sname as sname,scode as scode,sdbtype,satt1 as satt1,satt2 as satt2,satt3 as satt3,aod,dc_code_datatype.id as ParentID from dc_code_datatype_subs,dc_code_datatype where dc_code_datatype_subs.did= dc_code_datatype.id and dc_code_datatype_subs.Status =1 and dc_code_datatype.Status =1  and dc_code_datatype_subs.mc_type='".$mc_type."' and dc_code_datatype.isSubDatas=1 order by aod
 limit ".($i*$pagesize).",$pagesize";
	//echo $sql;
         $sth = $pdo->prepare($sql);
		 $sth->execute();
		 $result = $sth->fetchAll();
		$db->insert_dc_code_datatype_subs(array_iconv($result));
    }
    echo "插入dc_code_datatype_subs操作完成<br/>";
	
	$count_sql = "select id,dfkey,dfvalue,remark,dfod from dc_search_custom_dfconf where mc_type='".$mc_type."'  "; 
	$sth = $pdo->prepare($count_sql);
	$sth->execute();
	$result = $sth->fetchAll();
	$db->insert_dc_search_custom_dfconf(array_iconv($result));
	echo "dc_search_custom_dfconf<br/>";
	
}catch(PDOException $e){
    echo '执行失败'.$e->getMessage();
}
 
 echo "操作完成<br/>";
 

//这里需要注意该目录是否存在，并且有创建的权限
// $zipname = dirname(__FILE__).'/yun.zip' ;
//  //这是要打包的文件地址数组
// $files = array(dirname(__FILE__)."/yun.dat");


$zipname = dirname(__FILE__).'/'.$pcsqllitename[$mc_type][1] ;
$files = array(dirname(__FILE__)."/".$pcsqllitename[$mc_type][0]);


// if($mc_type!=0)
// {
//  $zipname = dirname(__FILE__).'/yun_ng.zip' ;
//  $files = array(dirname(__FILE__)."/yun_ng.dat");
// } 

$db=null;
$pdo=null;
$zip = new ZipArchive();
$res = $zip->open($zipname, ZipArchive::CREATE);
if ($res === TRUE) {
    foreach ($files as $file) {
 //这里直接用原文件的名字进行打包，也可以直接命名，需要注意如果文件名字一样会导致后面文件覆盖前面的文件，所以建议重新命名
     $new_filename = substr($file, strrpos($file, '/') + 1);
     $zip->addFile($file, $new_filename);
}
}
$zip->close();

echo "操作压缩完成<br/>";
$md5file = md5_file($zipname);

echo  "文件MD5码：".$md5file;
if($md5file)
{

	//$wdsn = 'mysql:dbname=steelhome_t1;host=***********;port=4306';//可写库
	//$wuser = 'root2';
	//$wpassword = '123456';
	//采用预处理+事务处理执行SQL操作
	//1.连接数据库
	try {
		$wpdo = new PDO($wdsn, $wuser, $wpassword);
		$wpdo->setAttribute(PDO::ATTR_ERRMODE,PDO::ERRMODE_EXCEPTION);
		echo "ok ";
	} catch (PDOException $e) {
		die("数据库连接失败".$e->getMessage());
	}
	$sql="update app_version set IOSSystemVersionUrlMd5	='".$md5file."' where  VersionType=2 and  mc_type='".$mc_type."' and Status=1 limit 1";
	$sth1 = $wpdo->prepare($sql);
	$sth1->execute();
}


?>