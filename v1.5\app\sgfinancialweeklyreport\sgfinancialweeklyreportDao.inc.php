<?php
class sgfinancialweeklyreportD<PERSON> extends Dao{
  public function __construct( $writer ){
    parent::__construct( $writer );
  }
	public function get_chenben_month_report( $dta_ym, $dta_vartype ){
		$sql = "select dta_type,dta_7 from sg_data_table where dta_ym='".$dta_ym."' and dta_vartype='".$dta_vartype."' and dta_9 = 2 ";
		return $this -> Aquery( $sql );
	}
	public function get_detail_month_report( $dta_ym, $dta_vartype, $dta_1 ){
		$sql = "select dta_type,dta_5 from sg_data_table where dta_ym='".$dta_ym."' and dta_vartype='".$dta_vartype."' and dta_1='".$dta_1."' and dta_9 = 1 ";
		//echo $sql;
		return $this -> Aquery( $sql );
	}
	public function get_sanxiangfeiyong( $dta_ym, $dta_type ){
		$sql = "select dta_1,dta_2,dta_3,dta_4,dta_5,dta_6 from sg_data_table where dta_ym='".$dta_ym."' and dta_type='".$dta_type."'  ";
		//echo $sql;
		return $this -> getRow( $sql );
	}
	public function get_dayly_report( $dta_ym, $dta_type, $dta_vartype ){
		$sql = "select dta_1,dta_2,dta_3,dta_4,dta_5,dta_6,dta_7,dta_8 from sg_data_table where dta_ym>='".$dta_ym."' and dta_ym<='".date("Y-m-d")."' and dta_type='".$dta_type."'  and dta_vartype='".$dta_vartype."'  order by dta_ym desc limit 1";
		//echo $sql;
		return $this -> getRow( $sql );
	}
	public function get_weekly_report( $dta_ym, $dta_ym_2, $dta_type, $dta_vartype, $dta_1 ){
		$sql = "select dta_1,dta_2,dta_4,dta_5,dta_10 from sg_data_table where dta_ym>='".$dta_ym."' and dta_ym<='".$dta_ym_2."' and dta_type='".$dta_type."'  and dta_vartype='".$dta_vartype."' and dta_1='".$dta_1."'  order by dta_ym desc limit 1";
		//echo $sql;
		return $this -> getRow( $sql );
	}
	public function get_erp_month_report( $dta_ym, $dta_type, $dta_1, $dta_7 ){
		$sql = "select dta_5,dta_6 from sg_data_table where dta_ym='".$dta_ym."'  and dta_type='".$dta_type."' and dta_1='".$dta_1."' and dta_7='".$dta_7."'  order by dta_ym desc limit 1";
		//echo $sql;
		return $this -> getRow( $sql );
	}
	public function get_erp_day_report( $dta_ym, $dta_ym_2, $dta_type, $dta_1, $dta_7 ){
		$sql = "select sum(dta_5),sum(dta_6) from sg_data_table where dta_ym>='".$dta_ym."' and dta_ym<='".$dta_ym_2."'   and dta_type='".$dta_type."' and dta_1='".$dta_1."' and dta_7='".$dta_7."'  ";
		//echo $sql;
		return $this -> getRow( $sql );
	}
	public function get_zhyf_by_time( $dta_ym, $dta_ym_2, $dta_type, $dta_1 ){
		$sql = "select avg(dta_2) from sg_data_table where dta_ym>='".$dta_ym."' and dta_ym<='".$dta_ym_2."'   and dta_type='".$dta_type."' and dta_1='".$dta_1."' ";
		//echo $sql;
		return $this -> getOne( $sql );
	}
}
?>