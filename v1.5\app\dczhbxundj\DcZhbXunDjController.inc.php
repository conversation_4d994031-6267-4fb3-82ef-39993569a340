<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );

class DcZhbXunDjController extends AbstractController{
  
  public function __construct(){
    parent::__construct();
	
	//$this->_action->setDao( new DcZhbXunDjDao( "MAIN" ) );
	//$this->_action->bizdao = new SystemDao('BIZ') ;
	$this->_action->stedao = new DcZhbXunDjDao('MAIN') ;
	$this->_action->ngdao = new DcZhbXunDjDao('DRCW') ;
	$this->_action->maindao = new DcZhbXunDjDao('91R') ;//hlf/钢之家主数据库、正式库，测试无数据
	$this->_action->gcdao=new DcZhbXunDjDao('GC');//hlf/钢厂调价的本期价格及涨跌幅,正式库

  }

  public function _dopre(){
      //$this->_action->checkSession();
  }

  public function v_index() {
	$this->_action->index($this->_request);
  }
 /* public function do_bcsc_xs(){
	 $this->_action->index($this->_request); 
  }
  public function do_bxunshijg(){
	   $this->_action->index($this->_request);
  }
  public function do_bxundj(){
	   $this->_action->index($this->_request);
  }*/
  //保存旬数据
  public function do_savexundj(){
	   $this->_action->savexundj($this->_request);
  }
}
?>