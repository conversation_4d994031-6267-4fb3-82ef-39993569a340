<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );

class zhbridjController extends AbstractController{
  
  public function __construct(){
    parent::__construct();
	
	$this->_action->setDao( new zhbridjDao( "MAIN" ) );
	
	$this->_action->steelhome= new zhbridjDao('91R'.$houzhui) ;
	//$this->_action->drc= new zhbridjDao('GC') ;//cha
	$this->_action->drc= new zhbridjDao('DRCW','DRC') ;//xie
  }

  public function _dopre(){
	  //echo "<pre/>";print_r($_SESSION);exit;
      //$this->_action->checkSession();
  }
	

  public function v_index() {
	$this->_action->index($this->_request);
  }
  public function do_save_price() {
	$this->_action->save_price($this->_request);
  }

	
  public function do_ThisDateDb() {
	$this->_action->DoThisDateDb($this->_request);
  }

  public function do_ThisDateDbList() {
	$this->_action->DoThisDateDbList($this->_request);
  }
}


?>