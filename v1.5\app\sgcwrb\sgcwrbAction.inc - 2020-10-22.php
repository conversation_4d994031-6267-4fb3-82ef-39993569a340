<?php
$GLOBALS ['city_arr'] = array("西安"=>"xian","郑州"=>"zhengzhou","成都"=>"chengdu","重庆"=>"chongqing","兰州"=>"lanzhou");
$GLOBALS ['jiageid_arr'] = array("1"=>"A120231","2"=>"A113121","3"=>"A111031");
include_once ("cwlr.php");
// class sgcwrbAction extends AbstractAction
class sgcwrbAction extends sgcwlrAction
{
	public function __construct()
	{
    	parent::__construct();
	}

	public function checkSession()
	{
	}

	/*周环比*/
	function weekday($date){
		//echo $date;
		$wnum=date("w",strtotime($date));
		$weekd["be"]=$date;
		if ($wnum==0){
		   //$weekd["be"]=$date;
		   $weekd["bs"]=date("Y-m-d",strtotime ("-6 day $date"));
		   $weekd["se"]=date("Y-m-d",strtotime ("-7 day $date"));
		   $weekd["ss"]=date("Y-m-d",strtotime ("-13 day $date"));
		}else{
		   $jwn=7-$wnum;
		   $gwn=$wnum-1;
		   $weekd["be"]=date("Y-m-d",strtotime ("+$jwn day $date"));
		   $weekd["bs"]=date("Y-m-d",strtotime ("-$gwn day $date"));
		   //为了处理国庆节期间无数据，将日期强行推前1个星期
		   //$sgwn=$wnum+13;
		   $sgwn=$wnum+6;
		   $weekd["se"]=date("Y-m-d",strtotime ("-$wnum day $date"));
		   $weekd["ss"]=date("Y-m-d",strtotime ("-$sgwn day $date"));
		}
		//added by shizg for 国庆节数据处理 started 2017/10/10
		require_once('/etc/steelconf/config/isholiday.php'); 
	   $date2 = $weekd["ss"];
	   $needckecklastweek = 0;
	   while($date2<$weekd["se"]){
		   if(!_isholiday($date2)){
			   $needckecklastweek = 1;
		   }
		   $date2 = date("Y-m-d",strtotime($date2."+1 day"));
	   }
		if($needckecklastweek==0){
		   $weekd["se"]=date("Y-m-d",strtotime ($weekd["se"]."-7 day "));
		   $weekd["ss"]=date("Y-m-d",strtotime ($weekd["ss"]."-7 day "));
		}
	   // if (date("d")>16 && date("d")< 28){
			 //$weekd=array("be"=>'2007-03-02',"bs" =>'2007-02-25',"se" =>'2007-02-16',"ss" =>'2007-02-12');
		//}		
		return $weekd;
	}
	/*月环比*/
	function monday($date){
		$mnum=date("n",strtotime($date));
		$y=date("Y",strtotime($date));
		if ($mnum==1){		    
		   $weekd["bs"]=$y."-01-01";
		   $weekd["be"]=$y."-01-".date("d",strtotime($date));
		   $weekd["ss"]=($y-1)."-12-01";
		   $weekd["se"]=($y-1)."-12-31";
		}else{
		   $weekd["bs"]=$y."-".date("m",strtotime($date))."-01";
		   $weekd["be"]=$y."-".date("m",strtotime($date))."-".date("d",strtotime($date));
		   $mnum1=$mnum-1;
		   if ($mnum1<10){$mnum1="0".$mnum1;}
		   $weekd["ss"]=$y."-".$mnum1."-01";
		   $weekd["se"]=$y."-".$mnum1."-31";
		}
		return $weekd;
	}
	/*月同比*/
	function monday_same($date)
	{
		if ((date("m",strtotime($date))-1)<0){	$yab=date("Y",strtotime($date))-1;$mab=12;}else{	$yab=date("Y",strtotime($date))-1;$mab=date("m",strtotime($date));}
	  	$semd=array("se"=>$yab.'-'.$mab.'-31',"ss" =>$yab.'-'.$mab.'-01',
	              "be" =>date("Y",strtotime($date)).'-'.date("m",strtotime($date)).'-'.date("d",strtotime($date)),
				  "bs" =>date("Y",strtotime($date)).'-'.date("m",strtotime($date)).'-01');
		return $semd;
	}
	/*季环比*/
	function season($date){
		$mnum=date("n",strtotime($date));
		$y=date("Y",strtotime($date));
		switch ($mnum){
		   case 1:
		   case 2:
		   case 3:
			  $weekd["bs"]=$y."-01-01";$weekd["be"]=$y."-03-".date("d",strtotime($date));$weekd["ss"]=($y-1)."-10-01";$weekd["se"]=($y-1)."-12-31";break;
		   case 4:
		   case 5:
		   case 6:
			 $weekd["bs"]=$y."-04-01";$weekd["be"]=$y."-06-".date("d",strtotime($date));$weekd["ss"]=$y."-01-01";$weekd["se"]=$y."-03-31";break;
		   case 7:
		   case 8:
		   case 9:
			 $weekd["bs"]=$y."-07-01";$weekd["be"]=$y."-09-".date("d",strtotime($date));$weekd["ss"]=$y."-04-01";$weekd["se"]=$y."-06-30";break;
		   case 10:
		   case 11:
		   case 12:
			 $weekd["bs"]=$y."-10-01";$weekd["be"]=$y."-12-".date("d",strtotime($date));$weekd["ss"]=$y."-07-01";$weekd["se"]=$y."-09-30";break; 
		}
		return $weekd;
	}
	/*年环比*/
	function yearday($date){
		$y=date("Y",strtotime($date));
		$weekd["bs"]=$y."-01-01";
		$weekd["be"]=$y."-12-".date("d",strtotime($date));
		$weekd["ss"]=($y-1)."-01-01";
		$weekd["se"]=($y-1)."-12-31";
		return $weekd;
	}
	/*年同比*/
	function yearday_same($date)
	{
		$seyy=(date("Y",strtotime($date))-1)."-".date("m",strtotime($date))."-".date("d",strtotime($date));
		$seyd=array( "se"=>$seyy,"ss" =>(date("Y",strtotime($date))-1).'-01-01',"be" =>date("Y-m-d",strtotime($date)),"bs" =>date("Y",strtotime($date)).'-01-01');
		return $seyd;
	}

	//陕钢财务日报
	public function index($params)
	{
		$sdate = $params['sdate'];
		if(empty($sdate)){
			$sdate = date("Y-m-d");
		}
		$this->assign("sdate",$sdate);

		$sql = "select dta_ym from sg_data_table where dta_type like '%SGcbrb%' and dta_ym <= '$sdate' order by dta_ym desc limit 1";
		$sdate = $this->_dao->getOne($sql);
		$ldate = $this->_dao->getOne("select dta_ym from sg_data_table where dta_type like '%SGcbrb%' and dta_ym < '$sdate' order by dta_ym desc limit 1");

		$b_month = date("n",strtotime($sdate));
		$s_month = date("n",strtotime("-1 month".$sdate));
		$s_month1 = date("Y-m",strtotime($sdate));
		$s_month2 = date("Y-m",strtotime("-1 month".$sdate));
		$today = date("n月j日",strtotime($sdate));
		$yestoday = date("n月j日",strtotime($ldate));
		$this->assign("b_month",$b_month);
		$this->assign("s_month",$s_month);
		$this->assign("today",$today);
		$this->assign("yestoday",$yestoday);

		//产量、生产成本
		$sy_chanling_info = $this->_dao->getYB($s_month2,2);
		$sy_chanling = array();
		foreach($sy_chanling_info as $v){
			$sy_chanling[$v['dta_vartype']][$v['dta_type']] = round($v['dta_7'],2);
		}
		// echo"<pre>";print_r($s_chanling);
		$this->assign("sy_chanling",$sy_chanling);
		$sy_sccb_info = $this->_dao->getYB($s_month2,1,"成本费用");
		$sy_sccb = array();
		foreach($sy_sccb_info as $v){
			$sy_sccb[$v['dta_vartype']][$v['dta_type']] = round($v['dta_5'],2);
		}
		$this->assign("sy_sccb",$sy_sccb);
		// echo"<pre>";print_r($s_sccb);
		
		$zt_chanling = $this->_dao->getRB($ldate);
		$jt_chanling = $this->_dao->getRB($sdate);
		$this->assign("zt_chanling",$zt_chanling);
		$this->assign("jt_chanling",$jt_chanling);

		// 八、月度计划
		$sql = "select dta_1,dta_2,dta_3,dta_4,dta_5,dta_6 from sg_data_table where dta_type='SG_SXFYLR' and dta_ym='$s_month1'";
		$by_ydjh_info = $this->_dao->getrow($sql);
		$sql = "select dta_1,dta_2,dta_3,dta_4,dta_5,dta_6 from sg_data_table where dta_type='SG_SXFYLR' and dta_ym='$s_month2'";
		$sy_ydjh_info = $this->_dao->getrow($sql);
		$tianshu = date('t', strtotime($sdate));
		$ri = date('j', strtotime($sdate));
		$ydjh["gl"]["lg"]['sy'] = round($sy_ydjh_info['dta_1'],2);
		$ydjh["gl"]["hg"]['sy'] = round($sy_ydjh_info['dta_4'],2);
		$ydjh["cw"]["lg"]['sy'] = round($sy_ydjh_info['dta_2'],2);
		$ydjh["cw"]["hg"]['sy'] = round($sy_ydjh_info['dta_5'],2);
		$ydjh["xs"]["lg"]['sy'] = round($sy_ydjh_info['dta_3'],2);
		$ydjh["xs"]["hg"]['sy'] = round($sy_ydjh_info['dta_6'],2);

		$ydjh["gl"]["lg"]['zt'] = $ydjh["gl"]["lg"]['jt'] = round($by_ydjh_info['dta_1']/$tianshu ,2);
		$ydjh["gl"]["lg"]['lj'] = $ydjh["gl"]["lg"]['hb'] = round($by_ydjh_info['dta_1']/$tianshu*$ri ,2);
		$ydjh["gl"]["lg"]['tb'] = $ydjh["gl"]["lg"]['lj'] - $ydjh["gl"]["lg"]['sy'];
		$ydjh["gl"]["hg"]['zt'] = $ydjh["gl"]["hg"]['jt'] = round($by_ydjh_info['dta_4']/$tianshu ,2);
		$ydjh["gl"]["hg"]['lj'] = $ydjh["gl"]["hg"]['hb'] = round($by_ydjh_info['dta_4']/$tianshu*$ri ,2);
		$ydjh["gl"]["hg"]['tb'] = $ydjh["gl"]["hg"]['lj'] - $ydjh["gl"]["hg"]['sy'];

		$ydjh["cw"]["lg"]['zt'] = $ydjh["cw"]["lg"]['jt'] = round($by_ydjh_info['dta_2']/$tianshu ,2);
		$ydjh["cw"]["lg"]['lj'] = $ydjh["cw"]["lg"]['hb'] = round($by_ydjh_info['dta_2']/$tianshu*$ri ,2);
		$ydjh["cw"]["lg"]['tb'] = $ydjh["cw"]["lg"]['lj'] - $ydjh["cw"]["lg"]['sy'];
		$ydjh["cw"]["hg"]['zt'] = $ydjh["cw"]["hg"]['jt'] = round($by_ydjh_info['dta_5']/$tianshu ,2);
		$ydjh["cw"]["hg"]['lj'] = $ydjh["cw"]["hg"]['hb'] = round($by_ydjh_info['dta_5']/$tianshu*$ri ,2);
		$ydjh["cw"]["hg"]['tb'] = $ydjh["cw"]["hg"]['lj'] - $ydjh["cw"]["hg"]['sy'];

		$ydjh["xs"]["lg"]['zt'] = $ydjh["xs"]["lg"]['jt'] = round($by_ydjh_info['dta_3']/$tianshu ,2);
		$ydjh["xs"]["lg"]['lj'] = $ydjh["xs"]["lg"]['hb'] = round($by_ydjh_info['dta_3']/$tianshu*$ri ,2);
		$ydjh["xs"]["lg"]['tb'] = $ydjh["xs"]["lg"]['lj'] - $ydjh["xs"]["lg"]['sy'];
		$ydjh["xs"]["hg"]['zt'] = $ydjh["xs"]["hg"]['jt'] = round($by_ydjh_info['dta_6']/$tianshu ,2);
		$ydjh["xs"]["hg"]['lj'] = $ydjh["xs"]["hg"]['hb'] = round($by_ydjh_info['dta_6']/$tianshu*$ri ,2);
		$ydjh["xs"]["hg"]['tb'] = $ydjh["xs"]["hg"]['lj'] - $ydjh["xs"]["hg"]['sy'];

		$ydjh["hj"]["lg"]['sy'] = $ydjh["gl"]["lg"]['sy'] + $ydjh["cw"]["lg"]['sy'] + $ydjh["xs"]["lg"]['sy'];
		$ydjh["hj"]["lg"]['zt'] = $ydjh["hj"]["lg"]['jt'] = round(($by_ydjh_info['dta_1']+$by_ydjh_info['dta_2']+$by_ydjh_info['dta_3'])/$tianshu ,2);
		$ydjh["hj"]["lg"]['lj'] = $ydjh["hj"]["lg"]['hb'] = round(($by_ydjh_info['dta_1']+$by_ydjh_info['dta_2']+$by_ydjh_info['dta_3'])/$tianshu*$ri ,2);
		$ydjh["hj"]["lg"]['tb'] = $ydjh["hj"]["lg"]['lj'] - $ydjh["hj"]["lg"]['sy'];
		$ydjh["hj"]["hg"]['sy'] = $ydjh["gl"]["hg"]['sy'] + $ydjh["cw"]["hg"]['sy'] + $ydjh["xs"]["hg"]['sy'];
		$ydjh["hj"]["hg"]['zt'] = $ydjh["hj"]["hg"]['jt'] = round(($by_ydjh_info['dta_4']+$by_ydjh_info['dta_5']+$by_ydjh_info['dta_6'])/$tianshu ,2);
		$ydjh["hj"]["hg"]['lj'] = $ydjh["hj"]["hg"]['hb'] = round(($by_ydjh_info['dta_4']+$by_ydjh_info['dta_5']+$by_ydjh_info['dta_6'])/$tianshu*$ri ,2);
		$ydjh["hj"]["hg"]['tb'] = $ydjh["hj"]["hg"]['lj'] - $ydjh["hj"]["hg"]['sy'];

		$xm_arr = array("gl"=>"管理费用","cw"=>"财务费用","xs"=>"销售费用","hj"=>"合计");
		$gs_arr = array("lg"=>"龙钢公司","hg"=>"汉钢公司");
		$this->assign("xm_arr",$xm_arr);
		$this->assign("gs_arr",$gs_arr);
		$this->assign("ydjh",$ydjh);
		// echo"<pre>";print_r($sy_ydjh_info);

		// 四、成本
		$chengben['dgqjfy']['lg']['sy'] = round($ydjh["hj"]["lg"]['sy'] / $sy_chanling['lg']['SG_CBYBB_LG'],2) ;
		$chengben['dgqjfy']['hg']['sy'] = round($ydjh["hj"]["hg"]['sy'] / $sy_chanling['lg']['SG_CBYBB_HG'],2) ;
	}

	function getday($qdate, $table, $vtype){
		$id_arr = $GLOBALS ['jiageid_arr'];
		if( $table == "SJCGIndex" ){
			$sqldate2="select Date from SJCGIndex where Date<='$qdate' and Type='$vtype' and DType='0' and Status=1 order by Date desc limit 1";
			$qdate = $this->homeDao->getone($sqldate2);
		} elseif ($table == "marketconditions"){
			$sqldate2="select mconmanagedate from marketconditions where mastertopid='$id_arr[$vtype]' and mconmanagedate<='$qdate 23:59:59' order by mconmanagedate desc limit 1";
			$qdate = $this->homeDao->getone($sqldate2);
		} elseif ($table == "sg_data_table"){
			$sqldate2="SELECT dta_ym FROM `sg_data_table` WHERE `dta_type` = 'TEST_ERP_ZHJSJ' AND `dta_ym` <= '$qdate' AND `dta_1` = '$vtype' and dta_3='合计' order by dta_ym desc limit 1";
			$qdate = $this->_dao->getone($sqldate2);
		}
		// echo $sqldate2."<br>";
		
		$qdate = date("Y-m-d",strtotime ($qdate));
		return $qdate;
	}

	function getpreday($qdate,$vtype){
		$sqldate2="select Date from SJCGIndex where Date<'$qdate' and Type='$vtype' and DType='0' and Status=1 order by Date desc limit 1";
		// echo $sqldate2;
		$qdate = $this->homeDao->getone($sqldate2);		
		$qdate = date("Y-m-d",strtotime ($qdate));
		return $qdate;
	}

	function getzhangdie($val1,$val2){
		$cha = round($val1 - $val2,2);
		if ($cha>0) {
			$zhangdie = '<font color="red">↑'.abs($cha).'</font>';
		} elseif ($cha<0) {
			$zhangdie = '<font color="green">↓'.abs($cha).'</font>';
		} elseif ($cha==0) {
			$zhangdie = '--';
		}
		return $zhangdie;
	}

	function pi_bj($arr_date, $vtype){
		$city_arr = $GLOBALS ['city_arr'];
		//本平均价
		$sql1="select CityName,avg(`index`) as bawp from SJCGIndex where Date>='".$arr_date["bs"]."' and Date<='".$arr_date["be"]."' and Type='$vtype' and DType='0' and Status=1 and CityName in ('西安','郑州','成都','重庆','兰州') group by CityName";
		//上平均价
		$sql2="select CityName,avg(`index`) as sawp from SJCGIndex where Date>='".$arr_date["ss"]."' and Date<='".$arr_date["se"]."' and Type='$vtype' and DType='0' and Status=1 and CityName in ('西安','郑州','成都','重庆','兰州') group by CityName";
		// echo $sql1."<br>".$sql2."<br>";
		$binfo=$this->homeDao->query($sql1);
		$sinfo=$this->homeDao->query($sql2);
		$binfo2 = array();
		foreach($binfo as $k => $v){
			$binfo2[$v['CityName']] = $v['bawp'];
		}
		$sinfo2 = array();
		foreach($sinfo as $k => $v){
			$sinfo2[$v['CityName']] = $v['sawp'];
		}
		// echo"<pre>";print_r($binfo2);exit;
		$bi_arr = array();
		foreach($binfo2 as $k => $v){
			$bavpi=$v;
			$savpi=$sinfo2[$k];
			if($savpi != 0)
			$bp=round(($bavpi-$savpi),2);
			else
			$bp="&nbsp;";
			if($bp == '-0'){
				$bp = 0;
			}
			if ($bp>0) {
				$zhangdie = '<font color="red">↑'.abs($bp).'</font>';
			} elseif ($bp<0) {
				$zhangdie = '<font color="green">↓'.abs($bp).'</font>';
			} elseif ($bp==0) {
				$zhangdie = '--';
			} else {
				$zhangdie = $bp;
			}
			$bi_arr[$k] = $zhangdie;
		}
		return $bi_arr;
	}

	function pi_bj2($arr_date, $vtype){
		$jiageid_arr = $GLOBALS ['jiageid_arr'];
		//本平均价
		$sql1="select avg(`price`) as bawp from marketconditions where mconmanagedate>='".$arr_date["bs"]." 00:00:00' and mconmanagedate<='".$arr_date["be"]." 23:59:59' and marketconditions.mastertopid='$jiageid_arr[$vtype]'";
		//上平均价
		$sql2="select avg(`price`) as sawp from marketconditions where mconmanagedate>='".$arr_date["ss"]." 00:00:00' and mconmanagedate<='".$arr_date["se"]." 23:59:59' and marketconditions.mastertopid='$jiageid_arr[$vtype]'";
		// echo $sql1."<br>".$sql2."<br>"; exit;
		$bavpi=$this->homeDao->getOne($sql1);
		$savpi=$this->homeDao->getOne($sql2);
		
		if($savpi != 0)
        $bp=round(($bavpi-$savpi),2);
		else
		$bp="&nbsp;";
		if($bp == '-0'){
			$bp = 0;
		}
		if ($bp>0) {
			$zhangdie = '<font color="red">↑'.abs($bp).'</font>';
		} elseif ($bp<0) {
			$zhangdie = '<font color="green">↓'.abs($bp).'</font>';
		} elseif ($bp==0) {
			$zhangdie = '--';
		} else {
			$zhangdie = $bp;
		}
		return $zhangdie;
	}

	function pi_bj4($arr_date){
		//本平均价
		$sql1="select dta_2,avg(`dta_6`) as bawp from sg_data_table where `dta_type`='TEST_ERP_ZHJSJ' AND dta_ym>='".$arr_date["bs"]."' and dta_ym<='".$arr_date["be"]."' and dta_3='合计' group by dta_2";
		//上平均价
		$sql2="select dta_2,avg(`dta_6`) as sawp from sg_data_table where `dta_type`='TEST_ERP_ZHJSJ' AND dta_ym>='".$arr_date["ss"]."' and dta_ym<='".$arr_date["se"]."' and dta_3='合计' group by dta_2";
		// echo $sql1."<br>".$sql2."<br>"; exit;
		$bavpi_arr=$this->_dao->query($sql1);
		$savpi_arr=$this->_dao->query($sql2);
		// echo"<pre>";print_r($bavpi_arr);exit;

		foreach ($savpi_arr as $v) {
			$savpi_arr2[$v['dta_2']] = $v['sawp'];
		}

		$bilv = array();
		foreach ($bavpi_arr as $v) {
			$bavpi = $v['bawp'];
			$savpi = $savpi_arr2[$v['dta_2']];
			if($savpi != 0)
			$bp=round(($bavpi-$savpi),2);
			else
			$bp="&nbsp;";
			if($bp == '-0'){
				$bp = 0;
			}
			if ($bp>0) {
				$zhangdie = '<font color="red">↑'.abs($bp).'</font>';
			} elseif ($bp<0) {
				$zhangdie = '<font color="green">↓'.abs($bp).'</font>';
			} elseif ($bp==0) {
				$zhangdie = '--';
			} else {
				$zhangdie = $bp;
			}
			$bilv[$v['dta_2']] = $zhangdie;
		}
		
		
		return $bilv;
	}

	//销售节奏评价
	public function xsjzpj($params)
	{
		$vtype = $params['vtype'];
		if(empty($vtype)){
			$vtype = 1;
		}
		$this->assign("vtype",$vtype);
		$vtype_arr = array("1"=>"螺纹钢",3=>"高线",2=>"盘螺",4=>"建筑钢材");
		$markettype = array('陕晋川甘西安'.$vtype_arr[$vtype].'价格','西咸片区','商洛片区','铜川片区','工程事业部西安','榆林片区','延安片区','安康片区','庆阳片区','宝平片区','陇南片区','天水片区','韩城市场龙钢'.$vtype_arr[$vtype].'价格','环韩城片区','陕晋川甘郑州'.$vtype_arr[$vtype].'价格','郑州片区','陕晋川甘成都'.$vtype_arr[$vtype].'价格','环汉中片区','广元片区','绵阳片区','成都片区','陕晋川甘重庆'.$vtype_arr[$vtype].'价格','重庆片区','陕晋川甘兰州'.$vtype_arr[$vtype].'价格','陇南片区','甘青藏片区','天水片区');

		$markettype_1 = array(
			'陕晋川甘西安'.$vtype_arr[$vtype].'价格'=>'西安',
			// '韩城市场龙钢'.$vtype_arr[$vtype].'价格'=>'西安',
			'陕晋川甘郑州'.$vtype_arr[$vtype].'价格'=>'郑州',
			'陕晋川甘成都'.$vtype_arr[$vtype].'价格'=>'成都',
			'陕晋川甘重庆'.$vtype_arr[$vtype].'价格'=>'重庆',
			'陕晋川甘兰州'.$vtype_arr[$vtype].'价格'=>'兰州'
		);

		$markettype_2 = array('西咸片区','商洛片区','铜川片区','工程事业部西安','榆林片区','延安片区','安康片区','庆阳片区','宝平片区','陇南片区','天水片区','环韩城片区','郑州片区','环汉中片区','广元片区','绵阳片区','成都片区','重庆片区','陇南片区','甘青藏片区','天水片区');
		$table_arr = array();
		foreach( $markettype as $v ){
			$table_arr[$v] = array();
		}

		$sdate = $params['sdate'];
		if(empty($sdate)){
			$sdate = date("Y-m-d");
		}
		$ndate_arr[0] = $this->getday($sdate,"SJCGIndex",$vtype);
		$ndate_arr[2] = $this->getday($sdate,"marketconditions",$vtype);
		$ndate_arr[3] = $this->getday($sdate,"sg_data_table",$vtype_arr[$vtype]);
		$ndate = min($ndate_arr);
		// echo $ndate;
		$ldate = $this->getpreday($ndate,$vtype);
		$this->assign("params",$params);
		$this->assign("sdate",$sdate);
		$this->assign("chdate",date("n月j日",strtotime($ndate)));
		$this->assign("vtype_arr",$vtype_arr);
		$city_arr = $GLOBALS ['city_arr'];

		$weekd = $this->weekday($ndate); //周环
		$mondayx=$this->monday($ndate);  //月环
		$mondayx_s=$this->monday_same($ndate); //月同
		$seasonx=$this->season($ndate); //季环
		$yeardayx=$this->yearday($ndate); //年环
		$seyd=$this->yearday_same($ndate); //年同

		$sql = " select pqid,pianqu from sg_PianQu";
		$sg_PianQu_info = $this->_dao->query($sql);
		$sg_PianQu = array();
		$sg_PianQu2 = array();
		foreach ($sg_PianQu_info as $v) {
			$sg_PianQu[$v["pianqu"]] = $v["pqid"];
			$sg_PianQu2[$v["pqid"]] = $v["pianqu"];
		}

		$sql = "SELECT dta_2,dta_6 FROM `sg_data_table` WHERE `dta_type`='TEST_ERP_ZHJSJ' AND `dta_ym`='$ndate' AND `dta_1`='$vtype_arr[$vtype]' and dta_3='合计'";
		$sg_data_info_b = $this->_dao->query($sql);
		$sql = "SELECT dta_2,dta_6 FROM `sg_data_table` WHERE `dta_type`='TEST_ERP_ZHJSJ' AND `dta_ym`='$ldate' AND `dta_1`='$vtype_arr[$vtype]' and dta_3='合计'";
		$sg_data_info_s = $this->_dao->query($sql);
		// echo"<pre>";print_r($sg_data_info_s);
		foreach($sg_data_info_s as $v){
			$sg_data_info_s2[$v['dta_2']] = $v['dta_6'];
		}
		foreach($sg_data_info_b as $v){
			$table_arr[$sg_PianQu2[$v['dta_2']]]["price"] = $v['dta_6'];
			$table_arr[$sg_PianQu2[$v['dta_2']]]["zhangdie"] =  $this->getzhangdie($v['dta_6']-$sg_data_info_s2[$v['dta_2']]);
		}
		$zhouhuan = array();
		$zhouhuan = $this->pi_bj4($weekd);
		$yuehuan = array();
		$yuehuan = $this->pi_bj4($mondayx);
		$yuetong = array();
		$yuetong = $this->pi_bj4($mondayx_s);
		$jihuan = array();
		$jihuan = $this->pi_bj4($seasonx);
		$nianhuan = array();
		$nianhuan = $this->pi_bj4($yeardayx);
		$niantong = array();
		$niantong = $this->pi_bj4($seyd);
		// echo"<pre>";print_r($zhouhuan);
		foreach($zhouhuan as $k => $v){
			$table_arr[$sg_PianQu2[$k]]['zhouhuan'] = $v;
			$table_arr[$sg_PianQu2[$k]]['yuehuan'] = $yuehuan[$k];
			$table_arr[$sg_PianQu2[$k]]['yuetong'] = $yuetong[$k];
			$table_arr[$sg_PianQu2[$k]]['jihuan'] = $jihuan[$k];
			$table_arr[$sg_PianQu2[$k]]['nianhuan'] = $nianhuan[$k];
			$table_arr[$sg_PianQu2[$k]]['niantong'] = $niantong[$k];
		}

		$SJCGIndex_b = $this->homeDao->getSJCGIndex($ndate,$vtype);
		$SJCGIndex_s = $this->homeDao->getSJCGIndex($ldate,$vtype);
		$jiage = array();
		foreach($SJCGIndex_s as $v){
			$SJCGIndex_s2[$v['CityName']] =  $v['index'];
		}
		foreach($SJCGIndex_b as $v){
			$jiage[$v['CityName']]["price"] = round($v['index'],2);
			$jiage[$v['CityName']]["zhangdie"] = $this->getzhangdie($v['index']-$SJCGIndex_s2[$v['CityName']]);
		}

		$zhouhuan = array();
		$zhouhuan = $this->pi_bj($weekd, $vtype);
		$yuehuan = array();
		$yuehuan = $this->pi_bj($mondayx, $vtype);
		$yuetong = array();
		$yuetong = $this->pi_bj($mondayx_s, $vtype);
		$jihuan = array();
		$jihuan = $this->pi_bj($seasonx, $vtype);
		$nianhuan = array();
		$nianhuan = $this->pi_bj($yeardayx, $vtype);
		$niantong = array();
		$niantong = $this->pi_bj($seyd, $vtype);
		// echo"<pre>";print_r($seyd);

		foreach ($markettype_1 as $k => $v) {
			$table_arr[$k]["price"] = $jiage[$v]["price"];
			$table_arr[$k]["zhangdie"] = $jiage[$v]["zhangdie"];
			$table_arr[$k]["zhouhuan"] = $zhouhuan[$v];
			$table_arr[$k]["yuehuan"] = $yuehuan[$v];
			$table_arr[$k]["yuetong"] = $yuetong[$v];
			$table_arr[$k]["jihuan"] = $jihuan[$v];
			$table_arr[$k]["nianhuan"] = $nianhuan[$v];
			$table_arr[$k]["niantong"] = $niantong[$v];
		}

		$jiageid_arr = $GLOBALS ['jiageid_arr'];
		// 韩城市场龙钢高线价格
		$sql = "select marketconditions.price,marketconditions.oldprice from marketconditions where mconmanagedate>='$ndate 00:00:00' and mconmanagedate<='$ndate 23:59:59' and  marketconditions.mastertopid='$jiageid_arr[$vtype]'";
		$marketconditions_info = $this->homeDao->getrow($sql);
		$hclg['price'] = $marketconditions_info['price'];
		$hclg['zhangdie'] = $this->getzhangdie($marketconditions_info['price'],$marketconditions_info['oldprice']);
		$hclg['zhouhuan'] = $this->pi_bj2($weekd, $vtype);
		$hclg['yuehuan'] = $this->pi_bj2($mondayx, $vtype);
		$hclg['yuetong'] = $this->pi_bj2($mondayx_s, $vtype);
		$hclg['jihuan'] = $this->pi_bj2($seasonx, $vtype);
		$hclg['nianhuan'] = $this->pi_bj2($yeardayx, $vtype);
		$hclg['niantong'] = $this->pi_bj2($seyd, $vtype);

		$table_arr['韩城市场龙钢螺纹钢价格']["price"] = $hclg['price'];
		$table_arr['韩城市场龙钢螺纹钢价格']["zhangdie"] = $hclg['zhangdie'];
		$table_arr['韩城市场龙钢螺纹钢价格']["zhouhuan"] = $hclg['zhouhuan'];
		$table_arr['韩城市场龙钢螺纹钢价格']["yuehuan"] = $hclg['yuehuan'];
		$table_arr['韩城市场龙钢螺纹钢价格']["yuetong"] = $hclg['yuetong'];
		$table_arr['韩城市场龙钢螺纹钢价格']["jihuan"] = $hclg['jihuan'];
		$table_arr['韩城市场龙钢螺纹钢价格']["nianhuan"] = $hclg['nianhuan'];
		$table_arr['韩城市场龙钢螺纹钢价格']["niantong"] = $hclg['niantong'];
		// echo"<pre>";print_r($table_arr);
		$this->assign("table_arr",$table_arr);
		// echo"<pre>";print_r($hclg);
	}

	function preweek($date,$pre){
		$weekarr["s"] = date("Y-m-d",mktime(0, 0 , 0,date("m"),date("d")-date("w")+1-7*$pre,date("Y")));
		$weekarr["e"] =  date("Y-m-d",mktime(23,59,59,date("m"),date("d")-date("w")+7-7,date("Y")));
		return $weekarr;
	}

	// 销售计划完成情况
	public function xsjh($params)
	{
		// $markettype = array('工程事业部','西咸片区','环韩城片区','庆阳片区','铜川片区','安康片区','商洛片区','榆林片区','延安片区','宝鸡片区',
		// '环汉中片区','陇南片区','广元片区','天水片区','绵阳片区','南充片区','成都片区','甘青藏片区','郑州片区','武汉片区','云贵片区','重庆片');
		$sql = " select pqid,pianqu from sg_PianQu";
		$sg_PianQu_info = $this->_dao->query($sql);
		$sg_PianQu = array();
		$sg_PianQu2 = array();
		foreach ($sg_PianQu_info as $v) {
			$sg_PianQu[$v["pianqu"]] = $v["pqid"];
			$sg_PianQu2[$v["pqid"]] = $v["pianqu"];
		}
		$sdate = $params['sdate'];
		if(empty($sdate)){
			$sdate = date("Y-m-d");
		}
		$sql = "select dta_ym from sg_data_table where dta_type='SGHCGCZXS' and dta_ym <='$sdate' order by dta_ym desc limit 1";
		$date_arr[0] = $this->_dao->getOne($sql);
		$sql = "select dta_ym from sg_data_table where dta_type='TEST_ERP_XSSJ' and dta_ym <='$sdate' and dta_1='建材' and dta_7='合计' order by dta_ym desc limit 1";
		$date_arr[1] = $this->_dao->getOne($sql);
		$ndate = min($date_arr);
		$sql = "select dta_ym from sg_data_table where dta_type='SGHCGCZXS' and dta_ym <'$ndate' order by dta_ym desc limit 1";
		$ldate = $this->_dao->getOne($sql);
		$this->assign("sdate",$ndate);
		//计划量
		$bplan_info = $this->_dao->getsg_data('SGHCGCZXS', $ndate);
		$splan_info = $this->_dao->getsg_data('SGHCGCZXS', $ldate);
		$week_4 = $this->preweek($ndate, 7);
		$splan_info_4 = $this->_dao->getsg_data2('SGHCGCZXS', $week_4['s'], $week_4['e']);
		$week_52 = $this->preweek($ndate, 52);
		$splan_info_52 = $this->_dao->getsg_data2('SGHCGCZXS', $week_52['s'], $week_52['e']);
		//销售量
		$weekd = $this->weekday($ndate); //周环
		// print_r($week_4);
		$bxiaoshou_info = $this->_dao->getsg_data_xiaoshou('TEST_ERP_XSSJ', $weekd['bs'], $weekd['be']);
		$sxiaoshou_info = $this->_dao->getsg_data_xiaoshou('TEST_ERP_XSSJ', $weekd['ss'], $weekd['se']);
		$xiaoshou_info_4 = $this->_dao->getsg_data_xiaoshou('TEST_ERP_XSSJ', $week_4['s'], $week_4['e']);
		$xiaoshou_info_52 = $this->_dao->getsg_data_xiaoshou('TEST_ERP_XSSJ', $week_52['s'], $week_52['e']);
		// echo"<pre>";print_r($xiaoshou_info_4);
		$bplan = array();
		$splan = array();
		$splan_4 = array();
		$splan_52 = array();
		foreach ($bplan_info as $v) {
			$bplan[$v['dta_2']] = round($v['dta_5'] * 7,2);
		}
		foreach ($splan_info as $v) {
			$splan[$v['dta_2']] = round($v['dta_5'] * 7,2);
		}
		foreach ($splan_info_4 as $v) {
			$splan_4[$v['dta_2']] = round($v['dta_5'] * 7,2);
		}
		foreach ($splan_info_52 as $v) {
			$splan_52[$v['dta_2']] = round($v['dta_5'] * 7,2);
		}
		$bxiaoshou = array();
		$sxiaoshou = array();
		$xiaoshou_4 = array();
		$xiaoshou_52 = array();
		foreach ($bxiaoshou_info as $v) {
			$bxiaoshou[$v['dta_4']] = round($v['dta_5'] ,2);
		}
		foreach ($sxiaoshou_info as $v) {
			$sxiaoshou[$v['dta_4']] = round($v['dta_5'] ,2);
		}
		foreach ($xiaoshou_info_4 as $v) {
			$xiaoshou_4[$v['dta_4']] = round($v['dta_5'] ,2);
		}
		foreach ($xiaoshou_info_52 as $v) {
			$xiaoshou_52[$v['dta_4']] = round($v['dta_5'] ,2);
		}
		$table_info = array();
		foreach ($sg_PianQu2 as $k => $v) {
			$table_info["合计"]["bz"]['plan'] += $bplan[$v];
			$table_info["合计"]["sz"]['plan'] += $splan[$v];
			$table_info["合计"]["s4"]['plan'] += $splan_4[$v];
			$table_info["合计"]["s52"]['plan'] += $splan_52[$v];
			$table_info[$v]["bz"]['plan'] = $bplan[$v];
			$table_info[$v]["sz"]['plan'] = $splan[$v];
			$table_info[$v]["s4"]['plan'] = $splan_4[$v];
			$table_info[$v]["s52"]['plan'] = $splan_52[$v];

			$table_info["合计"]["bz"]['xiaoshou'] += $bxiaoshou[$k];
			$table_info["合计"]["sz"]['xiaoshou'] += $sxiaoshou[$k];
			$table_info["合计"]["s4"]['xiaoshou'] += $xiaoshou_4[$k];
			$table_info["合计"]["s52"]['xiaoshou'] += $xiaoshou_52[$k];
			$table_info[$v]["bz"]['xiaoshou'] = $bxiaoshou[$k];
			$table_info[$v]["sz"]['xiaoshou'] = $sxiaoshou[$k];
			$table_info[$v]["s4"]['xiaoshou'] = $xiaoshou_4[$k];
			$table_info[$v]["s52"]['xiaoshou'] = $xiaoshou_52[$k];

			$table_info[$v]["bz"]['bilv'] = round($bxiaoshou[$k]/$bplan[$v]*100,2);
			$table_info[$v]["sz"]['bilv'] = round($sxiaoshou[$k]/$splan[$v]*100,2);
			$table_info[$v]["s4"]['bilv'] = round($xiaoshou_4[$k]/$splan_4[$v]*100,2);
			$table_info[$v]["s52"]['bilv'] = round($xiaoshou_52[$k]/$splan_52[$v]*100,2);
		}
		foreach ($table_info['合计'] as $k => $v) {
			$table_info['合计'][$k]['bilv'] = round($v['xiaoshou']/$v['plan']*100,2);
		}
		// echo"<pre>";print_r($table_info);
		$this->assign("table_info",$table_info);
	}

	function getday2($date, $table, $jiageid){
		$strlen = strlen($jiageid);
		if ($strlen == 6) {
			$jiage = "topicture";
		} elseif ($strlen == 7) {
			$jiage = "mastertopid";
		}
		if($table == "marketconditions"){
			$sqldate="select mconmanagedate from marketconditions where $jiage='$jiageid' and mconmanagedate<='$date 23:59:59' order by mconmanagedate desc limit 1";
		}
		// echo $sqldate."<br>";
		$qdate = $this->homeDao->getone($sqldate);
		$qdate = date("Y-m-d",strtotime ($qdate));
		return $qdate;
	}

	function pi_bj3($arr_date, $jiage_arr){
		$jagestr = implode("','",$jiage_arr);
		//本平均价
		$sql1="select topicture,avg(`price`) as bawp from marketconditions where mconmanagedate>='".$arr_date["bs"]."' and mconmanagedate<='".$arr_date["be"]."' and topicture in ('$jagestr') group by topicture";
		//上平均价
		$sql2="select topicture,avg(`price`) as sawp from marketconditions where mconmanagedate>='".$arr_date["ss"]."' and mconmanagedate<='".$arr_date["se"]."' and topicture in ('$jagestr') group by topicture";
		// echo $sql1."<br>".$sql2."<br>";
		$binfo=$this->homeDao->query($sql1);
		$sinfo=$this->homeDao->query($sql2);
		$binfo2 = array();
		foreach($binfo as $k => $v){
			$binfo2[$v['topicture']] = $v['bawp'];
		}
		$sinfo2 = array();
		foreach($sinfo as $k => $v){
			$sinfo2[$v['topicture']] = $v['sawp'];
		}
		// echo"<pre>";print_r($binfo2);
		// echo"<pre>";print_r($sinfo2);
		$bi_arr = array();
		foreach($binfo2 as $k => $v){
			$bavpi=$v;
			$savpi=$sinfo2[$k];
			if($savpi != 0)
			$bp=round(($bavpi-$savpi),2);
			else
			$bp="&nbsp;";
			if($bp == '-0'){
				$bp = 0;
			}
			if ($bp>0) {
				$zhangdie = '<font color="red">↑'.abs($bp).'</font>';
			} elseif ($bp<0) {
				$zhangdie = '<font color="green">↓'.abs($bp).'</font>';
			} elseif ($bp==0) {
				$zhangdie = '--';
			} else {
				$zhangdie = $bp;
			}
			$bi_arr[$k] = $zhangdie;
		}
		return $bi_arr;
	}

	// 采购节奏评价
	public function cgjzpj($params)
	{
		$tdarr = array(
			"铁矿石" => array("日照港PB粉矿价格","韩城公司现货矿采购价格","唐山66%磁铁矿价格","大西沟贸易国产矿"),
			"废钢" => array("钢之家华北废钢价格指数","龙钢废钢采购价格","钢之家西部废钢价格指数","汉钢废钢采购价格"),
			"焦炭" => array("钢之家焦炭价格指数","韩城公司焦炭采购价格"),
			"喷吹煤" => array("钢之家喷吹煤价格指数","韩城公司喷吹煤采购价格"),
			"硅铁" => array("钢之家硅铁价格指数","韩城公司硅铁采购价格"),
			"硅锰" => array("钢之家硅锰价格指数","韩城公司硅锰采购价格"),
			"钒氮合金" => array("钒氮合金价格","韩城公司钒氮采购价格"),
			"铌铁" => array("巴西铌铁价格","韩城公司铌铁采购价格")
		);
		$table_arr = array();
		foreach ($tdarr as $k => $v) {
			$table_arr[$k]["rows"] = count($v,0);
			foreach ($v as $v2) {
				$table_arr[$k][$v2] = array();
			}
		}
		
		$sdate = $params['sdate'];
		if(empty($sdate)){
			$sdate = date("Y-m-d");
		}
		$jiage_arr = array( "D28611","448610","569422","A99420");
		foreach ($jiage_arr as $k => $v) {
			$ndate_arr[$k] = $this->getday2($sdate,"marketconditions",$v);
		}
		$ndate = min($ndate_arr);
		$this->assign("sdate",$ndate);
		$this->assign("chdate",date("n月j日",strtotime($ndate)));

		$weekd = $this->weekday($ndate); //周环
		$mondayx=$this->monday($ndate);  //月环
		$mondayx_s=$this->monday_same($ndate); //月同
		$seasonx=$this->season($ndate); //季环
		$yeardayx=$this->yearday($ndate); //年环
		$seyd=$this->yearday_same($ndate); //年同

		$jagestr = implode("','",$jiage_arr);
		$sql = "select topicture,price,oldprice from marketconditions where topicture in ('$jagestr') and mconmanagedate>='$ndate 00:00:00' and mconmanagedate<='$ndate 23:59:59'";
		$jg_info = $this->homeDao->query($sql);
		$jgarr = array();
		foreach ($jg_info as $k => $v) {
			$jgarr[$v['topicture']] = $v;
		}
		$table_arr['铁矿石']['日照港PB粉矿价格']['value'] = $jgarr['D28611']['price'];
		$table_arr['铁矿石']['日照港PB粉矿价格']['zhangdie'] = $this->getzhangdie($jgarr['D28611']['price'],$jgarr['D28611']['oldprice']);
		$table_arr['铁矿石']['唐山66%磁铁矿价格']['value'] = $jgarr['448610']['price'];
		$table_arr['铁矿石']['唐山66%磁铁矿价格']['zhangdie'] = $this->getzhangdie($jgarr['448610']['price'],$jgarr['448610']['oldprice']);
		$table_arr['钒氮合金']['钒氮合金价格']['value'] = $jgarr['569422']['price'];
		$table_arr['钒氮合金']['钒氮合金价格']['zhangdie'] = $this->getzhangdie($jgarr['569422']['price'],$jgarr['569422']['oldprice']);
		$table_arr['铌铁']['巴西铌铁价格']['value'] = $jgarr['A99420']['price'];
		$table_arr['铌铁']['巴西铌铁价格']['zhangdie'] = $this->getzhangdie($jgarr['A99420']['price'],$jgarr['A99420']['oldprice']);

		$jg_zhouhuan = array();
		$jg_zhouhuan = $this->pi_bj3($weekd, $jiage_arr);
		$jg_yuehuan = array();
		$jg_yuehuan = $this->pi_bj3($mondayx, $jiage_arr);
		$jg_yuetong = array();
		$jg_yuetong = $this->pi_bj3($mondayx_s, $jiage_arr);
		$jg_jihuan = array();
		$jg_jihuan = $this->pi_bj3($seasonx, $jiage_arr);
		$jg_nianhuan = array();
		$jg_nianhuan = $this->pi_bj3($yeardayx, $jiage_arr);
		$jg_niantong = array();
		$jg_niantong = $this->pi_bj3($seyd, $jiage_arr);
		$table_arr['铁矿石']['日照港PB粉矿价格']['zhouhuan'] = $jg_zhouhuan['D28611'];
		$table_arr['铁矿石']['日照港PB粉矿价格']['yuehuan'] = $jg_yuehuan['D28611'];
		$table_arr['铁矿石']['日照港PB粉矿价格']['yuetong'] = $jg_yuetong['D28611'];
		$table_arr['铁矿石']['日照港PB粉矿价格']['jihuan'] = $jg_jihuan['D28611'];
		$table_arr['铁矿石']['日照港PB粉矿价格']['nianhuan'] = $jg_nianhuan['D28611'];
		$table_arr['铁矿石']['日照港PB粉矿价格']['niantong'] = $jg_niantong['D28611'];
		$table_arr['铁矿石']['唐山66%磁铁矿价格']['zhouhuan'] = $jg_zhouhuan['448610'];
		$table_arr['铁矿石']['唐山66%磁铁矿价格']['yuehuan'] = $jg_yuehuan['448610'];
		$table_arr['铁矿石']['唐山66%磁铁矿价格']['yuetong'] = $jg_yuetong['448610'];
		$table_arr['铁矿石']['唐山66%磁铁矿价格']['jihuan'] = $jg_jihuan['448610'];
		$table_arr['铁矿石']['唐山66%磁铁矿价格']['nianhuan'] = $jg_nianhuan['448610'];
		$table_arr['铁矿石']['唐山66%磁铁矿价格']['niantong'] = $jg_niantong['448610'];
		$table_arr['钒氮合金']['钒氮合金价格']['zhouhuan'] = $jg_zhouhuan['569422'];
		$table_arr['钒氮合金']['钒氮合金价格']['yuehuan'] = $jg_yuehuan['569422'];
		$table_arr['钒氮合金']['钒氮合金价格']['yuetong'] = $jg_yuetong['569422'];
		$table_arr['钒氮合金']['钒氮合金价格']['jihuan'] = $jg_jihuan['569422'];
		$table_arr['钒氮合金']['钒氮合金价格']['nianhuan'] = $jg_nianhuan['569422'];
		$table_arr['钒氮合金']['钒氮合金价格']['niantong'] = $jg_niantong['569422'];
		$table_arr['铌铁']['巴西铌铁价格']['zhouhuan'] = $jg_zhouhuan['A99420'];
		$table_arr['铌铁']['巴西铌铁价格']['yuehuan'] = $jg_yuehuan['A99420'];
		$table_arr['铌铁']['巴西铌铁价格']['yuetong'] = $jg_yuetong['A99420'];
		$table_arr['铌铁']['巴西铌铁价格']['jihuan'] = $jg_jihuan['A99420'];
		$table_arr['铌铁']['巴西铌铁价格']['nianhuan'] = $jg_nianhuan['A99420'];
		$table_arr['铌铁']['巴西铌铁价格']['niantong'] = $jg_niantong['A99420'];


		// echo"<pre>";print_r($table_arr);
		$this->assign("table_arr",$table_arr);
	}
	
}

?>