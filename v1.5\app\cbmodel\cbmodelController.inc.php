<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );

class cbmodelController extends AbstractController{
  
  public function __construct(){
    parent::__construct();
	
	$this->_action->setDao( new cbmodelDao( 'DRCW',"DRC" ) );
	//$this->_action->ngdao = new DcWebViewDGDao('DRCW') ;
	$this->_action->t1dao = new cbmodelDao('MAIN') ;
	$this->_action->maindao = new cbmodelDao('91W','91R');
  }
  
  public function _dopre(){
      //$this->_action->checkSession();
  }

  public function v_index() {
	$this->_action->index($this->_request);
  }
  public function do_solve() {
	$this->_action->solve($this->_request);
  }
  public function do_update() {
	$this->_action->update($this->_request);
  }
  public function do_del() {
	$this->_action->del($this->_request);
  }
  public function v_ajaxgetindexinfo() {
	$this->_action->ajaxgetindexinfo($this->_request);
  }
  //七点准时生成成本指数程序
  public function do_calc_cbmx() {
	$this->_action->calc_cbmx($this->_request);
  }
  //重新计算任务,每分钟定时运行
  public function do_recount() {
	$this->_action->recount($this->_request);
  }
  
}