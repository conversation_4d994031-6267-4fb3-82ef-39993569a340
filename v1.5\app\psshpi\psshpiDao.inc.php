<?php
class  psshpiDao extends Dao{
	public function __construct($writer){
		 parent::__construct( $writer );
	}
	//通过guid取得用户信息
	public function get_userinfo_byguid($GUID){
		$sql = "select * from app_session_temp where  GUID ='$GUID'  order by  LoginDate DESC  limit 1";
		return $this->getRow($sql);
	}
	
	//获取用户权限和用户名
	public function get_license_privilege($Uid){
		$sql = "select * from app_license,app_license_privilege where  app_license.id=app_license_privilege.lid and app_license_privilege.Uid='$Uid' and app_license_privilege.mc_type=1 limit 1";
		return $this->getRow($sql);
	}

    //获取
	public function getPs_realnews_total($where,$debug){
		$sql = "select count(*) from ps_realnews where 1 {$where} and type in ('1','2') ";
        if($debug=="1"){
            echo $sql;
        }
		return $this->getOne($sql);
	}
	public function getPs_realnews($where,$start,$per,$debug){
		$sql = "select sid,date,headLine,cSource,pageNumber,fileName,type,urlLink from ps_realnews where 1 {$where} and type in ('1','2') order by date DESC limit $start,$per";
        if($debug=="1"){
            echo $sql;
        }
        file_put_contents("/tmp/getPs_realnews",$sql);
		return $this->query($sql);
	}

    //获取
	public function getPs_realnews_detail($where){
		$sql = "select * from ps_realnews where 1 {$where}  limit 1";
        //echo $sql;
        if($debug=="1"){
            echo $sql;
        }
		return $this->getRow($sql);
	}

    //获取
	public function getPs_realnum($where,$debug){
		//$sql = "select dateTime,symbol,batu,value from ps_realnum where 1 {$where}  order by dateTime DESC limit $start,$per";
		//$sql = "select dateTime,symbol,batu,value from ps_realnum where 1 {$where}  order by dateTime DESC ";
		$sql = "select dateTime,symbol,batu,value from ps_realnum where 1 {$where}  order by dateTime,symbol ASC ";
        //echo $sql;
        //file_put_contents("/tmp/getPs_realnum",$sql);
        if($debug=="1"){
            echo $sql;
        }
		return $this->query($sql);
	}

    //获取
	public function get_PSshpiBaseInfo($Symbol){
		$sql = "select cDescription,eDescription from PSshpiBaseInfo where Symbol = '".$Symbol."'  limit 1";
        //echo $sql;
		return $this->getRow($sql);
	}
    //获取
	public function get_PSshpiBaseInfo_Symbol($Symbol){
		$sql = "select Symbol from PSshpiBaseInfo where Symbol like binary '%".$Symbol."%'  or  cDescription like binary '%".$Symbol."%' or eDescription like binary '%".$Symbol."%'";
        //echo $sql;exit;
		return $this->getOnes($sql);
	}
    //获取
	public function get_PSshpiBaseInfos($MDC){
		$sql = "select cDescription,eDescription,Freq,Symbol,CurrCN,UOMCN from PSshpiBaseInfo where MDC='".$MDC."'  group by Symbol";
        //echo $sql;
		return $this->query($sql);
	}
	

	
}