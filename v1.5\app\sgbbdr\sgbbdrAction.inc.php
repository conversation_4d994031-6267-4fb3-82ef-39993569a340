<?php
class sgbbdrAction extends AbstractAction
{
	public function __construct()
	{
    	parent::__construct();
	}

	public function checkSession()
	{
	}

	function drPqxxTiBao($params){
		$pianqu = $this->_dao->aquery("select pqid,pianqu from sg_PianQu");
		foreach($pianqu as $pqid=>$pq)
		{
			$num1 = substr(mt_rand(3000, 5200),0,3)*10;
			$num2 = substr(mt_rand(3000, 5200),0,3)*10;
			$num3 = substr(mt_rand(3000, 5200),0,3)*10;

			$num1_1 = substr(mt_rand(7000, 10000),0,3)*10;
			$num2_1 = substr(mt_rand(7000, 10000),0,3)*10;
			$num3_1 = substr(mt_rand(7000, 10000),0,3)*10;
			$sql = "insert into sg_PqxxTiBao(pqid,pinzhong,kdl,jzgccjjg,scqk,tjjy,adminId,createtime) values('$pqid','1','$num1_1','$num1','','','391935',NOW()),('$pqid','2','$num2_1','$num2','','','391935',NOW()),('$pqid','3','$num3_1','$num3','','','391935',NOW())";
			$this->_dao->execute($sql);
		}
	}

	function drZhidaojia_dr($params){
		$date = $params['date'];  //$date不存在，这里先手动随便指定一个日期
		$sql = "select * from sg_zhidaojia_dr where ndate like '%$date%'";
		$zdj = $this->_dao->query($sql);
		if(!empty($zdj))
		{
			exit;
		}

		$sql = "insert into sg_zhidaojia_dr(pinzhong,cityname,price,ndate,adminId,createtime) values('1','西安','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('1','成都','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('1','重庆','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('1','郑州','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('1','兰州','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('2','西安','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('2','成都','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('2','重庆','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('2','郑州','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('2','兰州','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('3','西安','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('3','成都','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('3','重庆','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('3','郑州','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW()),('3','兰州','".(substr(mt_rand(3200, 4800),0,3)*10)."','".date('Y-m-d')."','391935',NOW())";
		// echo $sql."<br><br>";
		$this->_dao->execute($sql);
	}

	public function random($length)
    {
        $hash = 'CR-';
        $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789abcdefghijklmnopqrstuvwxyz';
        $max = strlen($chars) - 1;
        mt_srand(( double ) microtime() * 1000000);
        for ($i = 0; $i < $length; $i ++) {
            $hash .= $chars [mt_rand(0, $max)];
        }
        return $hash;
	}
	
	function sjupload($params)
	{
		

		$GUID = $params['GUID'];
		$mode = $params['mode'];
		$this->assign('mode', $mode);
		if($GUID==""){
			alert('GUID不能为空');
            exit;
		}
		$user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
		if(empty($user_infos)){
			alert('用户不存在');
            exit;
		}
		$this->assign('guid', $GUID);
	
		$this->assign('date', date("Y-m-d"));
		
		
		$total = $this->_dao->getOne( "SELECT COUNT(id) as c FROM sg_data_table_log where uptype = '18' and isdel='0'");
		$page = $params['page'] == '' ? 1 : $params['page'];
		$url = "sgbbdr.php";
		$per = 25;
		$start = ( $page - 1 ) * $per;
		unset( $params['page'] );
		$pagebar = pagebar( $url, $params, $per, $page, $total );
		$this->assign( "pagebar", $pagebar );
		$sql = "select * from sg_data_table_log where uptype = '18' and isdel='0' order by createtime desc limit $start, $per";
		$log_info = $this->_dao->query($sql);
		$createuser = array();
		foreach( $log_info as $v ){
			if(!in_array($v['createuser'],$createuser)){
				$createuser[] = $v['createuser'];
			}
		}
		$createuser_str =  implode("','",$createuser);
		$sql = "select id,truename from adminuser where id in ('$createuser_str')";
		$adminuser_info = $this->homeDao->query($sql);
		$admin_name = array();
		foreach( $adminuser_info as $v ){
			$admin_name[$v['id']] = $v['truename'];
		}
		$this->assign('log_info', $log_info);
		$this->assign('date', date("Y-m"));
		$this->assign('admin_name', $admin_name);



	
	}
	function hcgczxsupload($params)
	{
	
		$GUID = $params['GUID'];
		$mode = $params['mode'];
		$this->assign('mode', $mode);
		if($GUID==""){
			alert('GUID不能为空');
            exit;
		}
		$user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
		if(empty($user_infos)){
			alert('用户不存在');
            exit;
		}
		$this->assign('guid', $GUID);
	
		$this->assign('date', date("Y-m-d"));
		
		
		$total = $this->_dao->getOne( "SELECT COUNT(id) as c FROM sg_data_table_log where uptype = '12' and isdel='0'");
		$page = $params['page'] == '' ? 1 : $params['page'];
		$url = "sgbbdr.php";
		$per = 25;
		$start = ( $page - 1 ) * $per;
		unset( $params['page'] );
		$pagebar = pagebar( $url, $params, $per, $page, $total );
		$this->assign( "pagebar", $pagebar );
		$sql = "select * from sg_data_table_log where uptype = '12' and isdel='0' order by createtime desc limit $start, $per";
		$log_info = $this->_dao->query($sql);
		$createuser = array();
		foreach( $log_info as $v ){
			if(!in_array($v['createuser'],$createuser)){
				$createuser[] = $v['createuser'];
			}
		}
		$createuser_str =  implode("','",$createuser);
		$sql = "select id,truename from adminuser where id in ('$createuser_str')";
		$adminuser_info = $this->homeDao->query($sql);
		$admin_name = array();
		foreach( $adminuser_info as $v ){
			$admin_name[$v['id']] = $v['truename'];
		}
		$this->assign('log_info', $log_info);
		$this->assign('date', date("Y-m-d"));
		$this->assign('admin_name', $admin_name);
	}


	function hcgcrxsupload($params)
	{
	
		$GUID = $params['GUID'];
		$mode = $params['mode'];
		$this->assign('mode', $mode);
		if($GUID==""){
			alert('GUID不能为空');
            exit;
		}
		$user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
		if(empty($user_infos)){
			alert('用户不存在');
            exit;
		}
		$this->assign('guid', $GUID);
	
		$this->assign('date', date("Y-m-d"));
		
		
		$total = $this->_dao->getOne( "SELECT COUNT(id) as c FROM sg_data_table_log where uptype = '14' and isdel='0'");
		$page = $params['page'] == '' ? 1 : $params['page'];
		$url = "sgbbdr.php";
		$per = 25;
		$start = ( $page - 1 ) * $per;
		unset( $params['page'] );
		$pagebar = pagebar( $url, $params, $per, $page, $total );
		$this->assign( "pagebar", $pagebar );
		$sql = "select * from sg_data_table_log where uptype = '14' and isdel='0' order by createtime desc limit $start, $per";
		$log_info = $this->_dao->query($sql);
		$createuser = array();
		foreach( $log_info as $v ){
			if(!in_array($v['createuser'],$createuser)){
				$createuser[] = $v['createuser'];
			}
		}
		$createuser_str =  implode("','",$createuser);
		$sql = "select id,truename from adminuser where id in ('$createuser_str')";
		$adminuser_info = $this->homeDao->query($sql);
		$admin_name = array();
		foreach( $adminuser_info as $v ){
			$admin_name[$v['id']] = $v['truename'];
		}
		$this->assign('log_info', $log_info);
		$this->assign('date', date("Y-m-d"));
		$this->assign('admin_name', $admin_name);
	}

	function hcgcyxsupload($params)
	{
		

		$GUID = $params['GUID'];
		$mode = $params['mode'];
		$this->assign('mode', $mode);
		if($GUID==""){
			alert('GUID不能为空');
            exit;
		}
		$user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
		if(empty($user_infos)){
			alert('用户不存在');
            exit;
		}
		$this->assign('guid', $GUID);
	
		$this->assign('date', date("Y-m-d"));
		
		
		$total = $this->_dao->getOne( "SELECT COUNT(id) as c FROM sg_data_table_log where uptype = '11' and isdel='0'");
		$page = $params['page'] == '' ? 1 : $params['page'];
		$url = "sgbbdr.php";
		$per = 25;
		$start = ( $page - 1 ) * $per;
		unset( $params['page'] );
		$pagebar = pagebar( $url, $params, $per, $page, $total );
		$this->assign( "pagebar", $pagebar );
		$sql = "select * from sg_data_table_log where uptype = '11' and isdel='0' order by createtime desc limit $start, $per";
		$log_info = $this->_dao->query($sql);
		$createuser = array();
		foreach( $log_info as $v ){
			if(!in_array($v['createuser'],$createuser)){
				$createuser[] = $v['createuser'];
			}
		}
		$createuser_str =  implode("','",$createuser);
		$sql = "select id,truename from adminuser where id in ('$createuser_str')";
		$adminuser_info = $this->homeDao->query($sql);
		$admin_name = array();
		foreach( $adminuser_info as $v ){
			$admin_name[$v['id']] = $v['truename'];
		}
		$this->assign('log_info', $log_info);
		$this->assign('date', date("Y-m"));
		$this->assign('admin_name', $admin_name);



	
	}
	function yjyybupload($params)
	{
		$year = date('Y');
		$years = array();
		for($i=0;$i<=30;$i++)
		{
			$years[] = $year-$i;
		}
		$this->assign('years', $years);
	
		$GUID = $params['GUID'];
		$mode = $params['mode'];
		$this->assign('mode', $mode);
		if($GUID==""){
			alert('GUID不能为空');
            exit;
		}
		$user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
		if(empty($user_infos)){
			alert('用户不存在');
            exit;
		}
		$this->assign('guid', $GUID);
	
		$this->assign('date', date("Y-m-d"));
		
		
		$total = $this->_dao->getOne( "SELECT COUNT(id) as c FROM sg_data_table_log where uptype = '13' and isdel='0'");
		$page = $params['page'] == '' ? 1 : $params['page'];
		$url = "sgbbdr.php";
		$per = 25;
		$start = ( $page - 1 ) * $per;
		unset( $params['page'] );
		$pagebar = pagebar( $url, $params, $per, $page, $total );
		$this->assign( "pagebar", $pagebar );
		$sql = "select * from sg_data_table_log where uptype = '13' and isdel='0' order by createtime desc limit $start, $per";
		$log_info = $this->_dao->query($sql);
		$createuser = array();
		foreach( $log_info as $v ){
			if(!in_array($v['createuser'],$createuser)){
				$createuser[] = $v['createuser'];
			}
		}
		$createuser_str =  implode("','",$createuser);
		$sql = "select id,truename from adminuser where id in ('$createuser_str')";
		$adminuser_info = $this->homeDao->query($sql);
		$admin_name = array();
		foreach( $adminuser_info as $v ){
			$admin_name[$v['id']] = $v['truename'];
		}
		$this->assign('log_info', $log_info);
	
		$this->assign('admin_name', $admin_name);
	}


	function readSheet($file, $year)
	{
		require_once "../PHPExcel/PHPExcel.php";
		$type = pathinfo($file); 
		$type = strtolower($type["extension"]);
		if ($type=='xlsx') { 
			$type='Excel2007'; 
		}elseif($type=='xls') { 
			$type = 'Excel5';
		}
		
		$objReader = PHPExcel_IOFactory::createReader($type);//判断使用哪种格式
		$objReader ->setReadDataOnly(true); //只读取数据,会智能忽略所有空白行,这点很重要！！！
		$objPHPExcel = $objReader->load($file); //加载Excel文件
		// $sheetCount = $objPHPExcel->getSheetCount();//获取sheet工作表总个数
		$sheets = $objPHPExcel->getSheetNames();
		return $sheets;
	}

	function sheet($params)
	{
		$GUID = $params['GUID'];
		$mode = $params['mode'];//1=客户端，2=安卓，3=iOS
		$this->assign('mode', $mode);
		$response = array(
			'Success'  => 1,
			'Message'  => '',
			'GUID'     => $GUID,
			'mode'     => $mode,
		);
		if($GUID==""){
			$response['Success'] = 0;
			$response['Message'] = 'GUID不能为空';
		}
		if($response['Success']){
			
			$year = $params['year'];
			$upfile = $_FILES['file'];
			if(strpos($this->array_iconv($upfile["name"]), '韩城公司钢材月销售')===false)
			{
				$response['Success'] = 0;
				$response['Message'] = '文件名非韩城公司钢材月销售！';
			}
			$user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
			if(empty($user_infos)){
				// $response['Success'] = 0;
				$response['Message'] = '用户不存在';
			}
			if($response['Success']){
				if ($upfile['error']==0)
				{
					if (is_uploaded_file($upfile['tmp_name'])) {
						$uptypes=array(
							'application/vnd.ms-excel',
							'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
						);
						if (in_array($upfile['type'], $uptypes)) {
							$extName = strtolower(end(explode('.', $upfile ['name'])));
							$filename = $this->random(5); // 设置随机数长度
							$extName=strtolower(end(explode('.', $upfile['name']))); // 取得扩展名
							$name1=date('YmdHis').".".$extName;
							$dir = UPLOADFILE;
							if (!file_exists($dir)) {
								if (mkdir($dir, 0777)) {
									$dest =$dir."/".$filename.$name1;
									
									if (move_uploaded_file($upfile ['tmp_name'], $dest)) {
										$response['Success'] = 1;
										$response['Message'] = '上传成功';
										$sheet11 = $this->readSheet($dest);
										$response['Result'] = $this->array_iconv($sheet11);
										$response['File'] = $filename.$name1;
										$response['oldtmp'] = $this->array_iconv($upfile["name"]);
										$response['Year'] = $year;
									} else {
										$response['Success'] = 0;
										$response['Message'] = '上传失败！目录权限不足';
									}
								} else {
									$response['Success'] = 0;
									$response['Message'] = '目录不存在，上传失败！';
								}
							} else {
								$dest = $dir."/".$filename.$name1;
								if (move_uploaded_file($upfile ['tmp_name'], $dest)) {
									$response['Success'] = 1;
									$response['Message'] = '上传成功';
									$sheet11 = $this->readSheet($dest);
									$response['Result'] = $this->array_iconv($sheet11);
									$response['File'] = $filename.$name1;
									$response['oldtmp'] = $this->array_iconv($upfile["name"]);
									$response['Year'] = $year;
								} else {
									$response['Success'] = 0;
									$response['Message'] = '上传失败！目录权限不足';
								}
							}
						} else {
							$response['Success'] = 0;
							$response['Message'] = '上传失败！检查文件是否是Excel文件';
						}
					} else {
						clearstatcache(); //清除文件缓存信息
						$response['Success'] = 0;
						$response['Message'] = '上传失败！';
					}
				}
				else
				{
					alert('上传失败！');
					goURL("sgbbdr.php?view=hcgcyxsupload&GUID=".$GUID."&mode=".$mode);
					exit;
				}
			}
		}
		echo $this->pri_JSON($response);
		exit;
	}

	function dosheet($params)
	{
		$GUID = $params['GUID'];
		$mode = $params['mode'];//1=客户端，2=安卓，3=iOS
		$filename = iconv("utf-8","gb2312//IGNORE",$params['oldtmp']);

		$response = array(
			'Success'  => 1,
			'Message'  => '',
			// 'GUID'     => $GUID,
			// 'mode'     => $mode,
		);
		if($GUID==""){
			$response["Success"] = 0;
			$response['Message'] = 'GUID不能为空';
		}

		if($response["Success"])
		{   
			$user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
			if(empty($user_infos)){
				$response["Success"] = 0;
				$response['Message'] = '用户不存在';
			}

			$sheetList = explode(',', $params["sheet"]);
			$file = UPLOADFILE."/".$params["tmp"];
			
			if((empty($sheetList[0])&&$sheetList[0]!=0)||$params["sheet"]=='')
			{ 

				$response["Success"] = 0;
				$response['Message'] = '未选择工作表';
			}

			$ymdate = mysql_real_escape_string($params["year"]);
			if(empty($ymdate))
			{
				$response["Success"] = 0;
				$response['Message'] = '时间不能为空';
			}
			if($response["Success"])
			{
				require_once "../PHPExcel/PHPExcel.php";
				$type = pathinfo($file); 
				$type = strtolower($type["extension"]);
				if ($type=='xlsx') { 
					$type='Excel2007'; 
				}elseif($type=='xls') { 
					$type = 'Excel5';
				}
				$objReader = PHPExcel_IOFactory::createReader($type);//判断使用哪种格式
				$objReader ->setReadDataOnly(true); //只读取数据,会智能忽略所有空白行,这点很重要！！！
				$objPHPExcel = $objReader->load($file); //加载Excel文件
				$sheets = $objPHPExcel->getSheetNames();
				$sheetCount = $objPHPExcel->getSheetCount();//获取sheet工作表总个数

				$flag = true;
				foreach($sheetList as $sheet_index1)
				{
					$sheet_index = $sheetCount-1-$sheet_index1;
					// $sheet = $objPHPExcel->setActiveSheetIndexByName($sheet_v);
					$sheet = $objPHPExcel->getSheet($sheet_index);
					// $sheet_array = $objPHPExcel->getSheet($sheet_index)->toArray();
					// print_r($sheet_array);
					// exit;
					//得到当前工作表对象
					// $curSheet = $objPHPExcel->getActiveSheet();
					//获取当前工作表最大行数
				
				
					$rows = $sheet->getHighestRow();
					//获取当前工作表最大列数,返回的是最大的列名，如：B 
					// $cols = $sheet->getHighestColumn();
					// print_r($cols);
					//将当前工作表名当键，内容为值存入数组

					$sheet_v = iconv("utf-8","gb2312//IGNORE",$sheets[$sheet_index]);
					$sheetname .= $sheet_v.",";
					$data = array();
					$listA = array();
					$maxrow='0';
					for($k = 3; $k <= $rows; $k++){
						$key = "A".$k;
						$listA[$k] = iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getValue());
						// $listA[$k] = $sheet->getCell($key)->getValue();
						
						if(empty($listA[$k])){
							$listA[$k] = $listA[$k-1];
						}
						if($maxrow!=0){//建材合计为最后一行，后面的不要了
							unset($listA[$k]);
						}
						if($listA[$k]=='建材合计'){ 
							$maxrow=$k;
						}

					}
					
					
					// print_r($listA);
					$t = array_keys($listA);

					
					//大写字母A的ASCII值是65 A-Z对应65-90
					for($j = 'B'; $j <= 'O'; $j++ ){
						for($k = 3; $k <= $maxrow; $k++){
							$key = $j.$k;
							  $info = iconv("utf-8","gb2312//IGNORE", $sheet->getCell("B".$k)->getValue());
							    // $info = $sheet->getCell("B".$k)->getValue();
							
								$data[$k]['A']=$listA[$k];
								if($listA[$k]=='小计'){
									$data[$k]['A']=$data[$k-1]['A'];
									
								}
								if($listA[$k]=='小计'&&$j=='B'){
									$data[$k]['B']=$listA[$k];
								}else{
								  $data[$k][$j] = iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue());
                                    // $data[$k][$j] = $sheet->getCell($key)->getCalculatedValue();
								}
								
								$data[$k]['Z'] = "SGHCGCYXS";
						}
					}

					
					
				    $A2 = iconv("utf-8","gb2312//IGNORE", $sheet->getCell('A2')->getValue());

					$regex_year="'\d{4}年'is";
					preg_match($regex_year,$A2,$yeararr);//print_r($year);
					$year = explode('年', $yeararr[0]);
					$regex_month="'\d{1,2}月'is";
					preg_match($regex_month,$A2,$montharr);//print_r($year);
					$month = explode('月', $montharr[0]);
					$date =$year[0].'-'.$month[0];
					$date =date("Y-m",strtotime($date));

					$this->_dao->execute("delete from sg_data_table where dta_ym='$date' and dta_type ='SGHCGCYXS'");
					$basesql = "insert into sg_data_table(dta_1,dta_2,dta_3,dta_4,dta_5,dta_6,dta_7,dta_8,dta_9,dta_10,dta_11,dta_12,dta_13,dta_14,dta_15,dta_type,dta_ym,createtime,createuser) values";
					$valSql = "";
				
					foreach($data as $rows=>$columns)
					{
						
						$dta_1 = $columns['A'];
						$dta_2 = $columns['B'];
						if($rows==3){
							$dta_3 = $columns['C'];
							$dta_4 = $columns['D'];
							$dta_5 = $columns['E'];
						
							$dta_6 =  $columns['F'];
							$dta_7 =  $columns['G'];
							$dta_8 =  $columns['H'];
							$dta_9 =  $columns['I'];
							$dta_10 =  $columns['J'];
							$dta_11 =  $columns['K'];
							$dta_12 =  $columns['L'];
							$dta_13 =  $columns['M'];
							$dta_14 =  $columns['N'];
							$dta_15 =  $columns['O'];
						}else{
							$dta_3 = str_replace("#DIV/0!","",sprintf("%.1f",round($columns['C'],1)));
							$dta_4 = str_replace("#DIV/0!","",$columns['D']);
							$dta_5 = str_replace("#DIV/0!","",sprintf("%.1f",round($columns['E'],1)));
						
							$dta_6 = str_replace("#DIV/0!","",sprintf("%.2f",round($columns['F'],2)));
							$dta_7 = str_replace("#DIV/0!","",sprintf("%.0f",round($columns['G'],0)));
							$dta_8 = str_replace("#DIV/0!","",sprintf("%.1f",round($columns['H'],1)));
							$dta_9 = str_replace("#DIV/0!","",sprintf("%.0f",round($columns['I'],0)));
							$dta_10 = str_replace("#DIV/0!","",sprintf("%.1f",round($columns['J'],1)));
							$dta_11 = str_replace("#DIV/0!","",sprintf("%.0f",round($columns['K'],0)));
							$dta_12 = str_replace("#DIV/0!","",sprintf("%.2f",round($columns['L'],2)));
							$dta_13 = $columns['M'];
							$dta_14 = str_replace("#DIV/0!","",sprintf("%.2f",round($columns['N'],2)));
							$dta_15 = str_replace("#DIV/0!","",sprintf("%.2f",round($columns['O'],2)));
						}

						$dta_type=$columns['Z'];
						$dta_ym = $date;
						
						$valSql.="('$dta_1','$dta_2','$dta_3','$dta_4','$dta_5','$dta_6','$dta_7','$dta_8','$dta_9','$dta_10','$dta_11','$dta_12','$dta_13','$dta_14','$dta_15','$dta_type','$dta_ym',NOW(),'".$user_infos['Uid']."'),";
						
					}
					
					$insertsql = substr($basesql.$valSql, 0, -1);
			       
					if(!empty($valSql))
					{
						$this->_dao->execute($insertsql);
						// echo $insertsql."<br><br>";
					}else{
						// 失败
						$error_sheet2[] = $sheet_v;
						$flag = false;
					}
				}
				if(!$flag)
				{
				
					$error2 = implode(',', $error_sheet2);
					
					if(!empty($error2))
					{
						$msg2 = "工作表：".iconv("utf-8","gb2312//IGNORE",$error2)."出现未知错误，导入失败";
					}
					$response["Success"] = 0;
					$response['Message'] = $msg2;
				}else{
					$response["Success"] = 1;
					$response['Message'] = '导入成功';
					$sheetname = substr($sheetname, 0, -1);
					$sql ="insert into sg_data_table_log(uptype,filepath,filename,sheetname,createtime,createuser,uptypedate) values('11','$file','$filename','$sheetname',now(),'".$user_infos['Uid']."','".$ymdate."')";
					$this->_dao->execute($sql);
				}
				unlink($file);
			}
		}
		echo $this->pri_JSON($response);
		exit;
	}

	function sheetsj($params)
	{
		
		$GUID = $params['GUID'];
		$mode = $params['mode'];//1=客户端，2=安卓，3=iOS
		$this->assign('mode', $mode);
		$response = array(
			'Success'  => 1,
			'Message'  => '',
			'GUID'     => $GUID,
			'mode'     => $mode,
		);
		if($GUID==""){
			$response['Success'] = 0;
			$response['Message'] = 'GUID不能为空';
		}
		if($response['Success']){
			
			$year = $params['year'];
			$upfile = $_FILES['file'];
			if(strpos($this->array_iconv($upfile["name"]), '烧结')===false)
			{
				$response['Success'] = 0;
				$response['Message'] = '文件名非烧结！';
			}
			$user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
			if(empty($user_infos)){
				// $response['Success'] = 0;
				$response['Message'] = '用户不存在';
			}
			if($response['Success']){
				if ($upfile['error']==0)
				{
					if (is_uploaded_file($upfile['tmp_name'])) {
						$uptypes=array(
							'application/vnd.ms-excel',
							'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
						);
						if (in_array($upfile['type'], $uptypes)) {
							$extName = strtolower(end(explode('.', $upfile ['name'])));
							$filename = $this->random(5); // 设置随机数长度
							$extName=strtolower(end(explode('.', $upfile['name']))); // 取得扩展名
							$name1=date('YmdHis').".".$extName;
							$dir = UPLOADFILE;
							if (!file_exists($dir)) {
								if (mkdir($dir, 0777)) {
									$dest =$dir."/".$filename.$name1;
									
									if (move_uploaded_file($upfile ['tmp_name'], $dest)) {
										$response['Success'] = 1;
										$response['Message'] = '上传成功';
										$sheet11 = $this->readSheet($dest);
										$response['Result'] = $this->array_iconv($sheet11);
										$response['File'] = $filename.$name1;
										$response['oldtmp'] = $this->array_iconv($upfile["name"]);
										$response['Year'] = $year;
									} else {
										$response['Success'] = 0;
										$response['Message'] = '上传失败！目录权限不足';
									}
								} else {
									$response['Success'] = 0;
									$response['Message'] = '目录不存在，上传失败！';
								}
							} else {
								$dest = $dir."/".$filename.$name1;
								if (move_uploaded_file($upfile ['tmp_name'], $dest)) {
									$response['Success'] = 1;
									$response['Message'] = '上传成功';
									$sheet11 = $this->readSheet($dest);
									$response['Result'] = $this->array_iconv($sheet11);
									$response['File'] = $filename.$name1;
									$response['oldtmp'] = $this->array_iconv($upfile["name"]);
									$response['Year'] = $year;
								} else {
									$response['Success'] = 0;
									$response['Message'] = '上传失败！目录权限不足';
								}
							}
						} else {
							$response['Success'] = 0;
							$response['Message'] = '上传失败！检查文件是否是Excel文件';
						}
					} else {
						clearstatcache(); //清除文件缓存信息
						$response['Success'] = 0;
						$response['Message'] = '上传失败！';
					}
				}
				else
				{
					alert('上传失败！');
					goURL("sgbbdr.php?view=sjupload&GUID=".$GUID."&mode=".$mode);
					exit;
				}
			}
		}
		echo $this->pri_JSON($response);
		exit;
	}

	function dosheetsj($params)
	{
             
		$file = SGUPLOADFILE."/".$params["tmp"];
       
	
		
		$GUID = $params['GUID'];
		$mode = $params['mode'];//1=客户端，2=安卓，3=iOS
		$filename = iconv("utf-8","gb2312//IGNORE",$params['oldtmp']);

		$response = array(
			'Success'  => 1,
			'Message'  => '',
			// 'GUID'     => $GUID,
			// 'mode'     => $mode,
		);
		if($GUID==""){
			$response["Success"] = 0;
			$response['Message'] = 'GUID不能为空';
		}

		if($response["Success"])
		{   
			$user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
			if(empty($user_infos)){
				$response["Success"] = 0;
				$response['Message'] = '用户不存在';
			}

			$sheetList = explode(',', $params["sheet"]);
			$file = UPLOADFILE."/".$params["tmp"];
			
			if((empty($sheetList[0])&&$sheetList[0]!=0)||$params["sheet"]=='')
			{ 

				$response["Success"] = 0;
				$response['Message'] = '未选择工作表';
			}

			$ymdate = mysql_real_escape_string($params["year"]);
			if(empty($ymdate))
			{
				$response["Success"] = 0;
				$response['Message'] = '时间不能为空';
			}
			if($response["Success"])
			{
				require_once "../PHPExcel/PHPExcel.php";
				$type = pathinfo($file); 
				$type = strtolower($type["extension"]);
				if ($type=='xlsx') { 
					$type='Excel2007'; 
				}elseif($type=='xls') { 
					$type = 'Excel5';
				}
				$objReader = PHPExcel_IOFactory::createReader($type);//判断使用哪种格式
				$objReader ->setReadDataOnly(true); //只读取数据,会智能忽略所有空白行,这点很重要！！！
				$objPHPExcel = $objReader->load($file); //加载Excel文件
				$sheets = $objPHPExcel->getSheetNames();//所有的工作表名
				//$sheets=array_reverse($sheets);
			
				$sheetCount = $objPHPExcel->getSheetCount();//获取sheet工作表总个数
			
				$flag = true;
				$tmpsheetName = array();
        
				foreach($sheetList as $sheet_index)
				{ 
					//传过来的顺序倒着的，所以用总数减去传过来的再减去1，就是正确的下标志
					$sheet_index=$sheetCount-$sheet_index-1;
					$sheet = $objPHPExcel->getSheet($sheet_index);
					// $sheet_array = $objPHPExcel->getSheet($sheet_index)->toArray();
					//获取当前工作表最大行数
					$rows = $sheet->getHighestRow();
					//获取当前工作表最大列数,返回的是最大的列名，如：B 
					$cols = $sheet->getHighestColumn();
				
				
					switch($sheet_index)
					{
						case 0:  //龙钢
	                      	$F2=gmdate("Y-m", PHPExcel_Shared_Date::ExcelToPHP($sheet->getCell("F2")->getValue())); 

						
							$date =date("Y-m",strtotime($F2));
                         
							$basesql0 = "insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,dta_3,dta_4,dta_5,dta_6,dta_7,dta_8,dta_9,dta_10,dta_11,dta_12,createtime,createuser) values";
							for($i=6;$i<=$rows;$i++)
							{
								for($j="A";$j<="L";$j++)
								{
									$key = $j.$i;
									$data0[$i][$j] = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getFormattedValue()));
								}
							}
							$valsql0 .= "('SGSJLG','烧结-龙钢','$date','项目','单位','单价','单耗','单位成本','总耗量','总成本','单价','单耗','单位成本','总耗量','总成本',NOW(),'".$user_infos['Uid']."'),";

							foreach($data0 as $rownum0 => $rows_arr0)
							{
								if($data0[$rownum0]["A"]!=""&&$rownum0!=1)
								{
									$dta0_1 = trim($data0[$rownum0]["A"]);
									$dta0_2 = trim($data0[$rownum0]["B"]);
									$dta0_3 = trim($data0[$rownum0]["C"]);
									$dta0_4 = trim($data0[$rownum0]["D"]);
									$dta0_5 = trim($data0[$rownum0]["E"]);
									$dta0_6 = trim($data0[$rownum0]["F"]);
									$dta0_7 = trim($data0[$rownum0]["G"]);
									$dta0_8 = trim($data0[$rownum0]["H"]);
									$dta0_9 = trim($data0[$rownum0]["I"]);
									$dta0_10 = trim($data0[$rownum0]["J"]);
									$dta0_11 = trim($data0[$rownum0]["K"]);
									$dta0_12 = trim($data0[$rownum0]["L"]);
									$valsql0 .= "('SGSJLG','烧结-龙钢','$date','$dta0_1','$dta0_2','$dta0_3','$dta0_4','$dta0_5','$dta0_6','$dta0_7','$dta0_8','$dta0_9','$dta0_10','$dta0_11','$dta0_12',NOW(),'".$user_infos['Uid']."'),";
								}
							}
							if($valsql0)
							{
								$this->_dao->execute("delete from sg_data_table where dta_ym='$date' and dta_type='SGSJLG' and dta_vartype='烧结-龙钢'");
								$cell1 = $this->trimAll(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("K3")->getValue()));
								$this->_dao->execute("insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,createtime,createuser) values('SGSJLG','烧结-龙钢','$date','本月产量','$cell1',NOW(),'".$user_infos['Uid']."')");
								$cell2 = $this->trimAll(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("L3")->getValue()));
								$this->_dao->execute("insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,createtime,createuser) values('SGSJLG','烧结-龙钢','$date','累计产量','$cell2',NOW(),'".$user_infos['Uid']."')");

								$tsql = substr($valsql0, 0, -1);
								$this->_dao->execute($basesql0.$tsql);
							
								$tmpsheetName[] = mb_convert_encoding($sheets[$sheet_index],"GB2312","UTF-8");
							}
						break;
						case 1:  //汉钢
							$F2=gmdate("Y-m", PHPExcel_Shared_Date::ExcelToPHP($sheet->getCell("F2")->getValue())); 

						
							$date =date("Y-m",strtotime($F2));
	
							$basesql1 = "insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,dta_3,dta_4,dta_5,dta_6,dta_7,dta_8,dta_9,dta_10,dta_11,dta_12,createtime,createuser) values";
							for($i=6;$i<=$rows;$i++)
							{
								for($j="A";$j<="F";$j++)
								{
									$key = $j.$i;
									$data1[$i][$j] = trim(iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getFormattedValue()));
									
								}
							}
							$valsql1.= "('SGSJHG','烧结-汉钢','$date','项目','单位','单价','单耗','单位成本','总耗量','总成本','单价','单耗','单位成本','总耗量','总成本',NOW(),'".$user_infos['Uid']."'),";

							foreach($data1 as $rownum1 => $rows_arr1)
							{
								if($data1[$rownum1]["A"]!=""&&$rownum1!=1)
								{
									$dta1_1 = trim($data1[$rownum1]["A"]);
									$dta1_2 = trim($data1[$rownum1]["B"]);
									$dta1_3 = trim($data1[$rownum1]["C"]);
									$dta1_4 = trim($data1[$rownum1]["D"]);
									$dta1_5 = trim($data1[$rownum1]["E"]);
									$dta1_6 = trim($data1[$rownum1]["F"]);
									$dta1_7 = trim($data1[$rownum1]["G"]);
									$dta1_8 = trim($data1[$rownum1]["H"]);
									$dta1_9 = trim($data1[$rownum1]["I"]);
									$dta1_10 = trim($data1[$rownum1]["J"]);
									$dta1_11 = trim($data1[$rownum1]["K"]);
									$dta1_12 = trim($data1[$rownum1]["L"]);
									$valsql1 .= "('SGSJHG','烧结-汉钢','$date','$dta1_1','$dta1_2','$dta1_3','$dta1_4','$dta1_5','$dta1_6','$dta1_7','$dta1_8','$dta1_9','$dta1_10','$dta1_11','$dta1_12',NOW(),'".$user_infos['Uid']."'),";
								}
							}
							if($valsql1)
							{
								$this->_dao->execute("delete from sg_data_table where dta_ym='$date' and dta_type='SGSJHG' and dta_vartype='烧结-汉钢'");
								$cell1 = $this->trimAll(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("K3")->getValue()));
								$this->_dao->execute("insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,createtime,createuser) values('SGSJHG','烧结-汉钢','$date','本月产量','$cell1',NOW(),'".$user_infos['Uid']."')");
								$cell2 = $this->trimAll(iconv("utf-8","gb2312//IGNORE", $sheet->getCell("L3")->getValue()));
								$this->_dao->execute("insert into sg_data_table(dta_type,dta_vartype,dta_ym,dta_1,dta_2,createtime,createuser) values('SGSJHG','烧结-汉钢','$date','累计产量','$cell2',NOW(),'".$user_infos['Uid']."')");

								$tsql = substr($valsql1, 0, -1);
								$this->_dao->execute($basesql1.$tsql);
							
								$tmpsheetName[] = mb_convert_encoding($sheets[$sheet_index],"GB2312","UTF-8");
							}
						break;
						default:
						break;
						
					}
				}
			
				$sheetName = implode(',',$tmpsheetName);
				
				$this->_dao->execute("insert into sg_data_table_log(uptype,filepath,filename,sheetname,createtime,createuser,uptypedate) value('18','$file','$filename','$sheetName',NOW(),'".$user_infos['Uid']."','$date')");
				$response['Success'] = "1";
				$response['Message'] = "导入成功";
			}
		}
		echo $this->pri_JSON($response);
		exit;
	}


	function sheetzxs($params)
	{
		
		$GUID = $params['GUID'];
		$mode = $params['mode'];//1=客户端，2=安卓，3=iOS
		$this->assign('mode', $mode);
		$response = array(
			'Success'  => 1,
			'Message'  => '',
			'GUID'     => $GUID,
			'mode'     => $mode,
		);
		if($GUID==""){
			$response['Success'] = 0;
			$response['Message'] = 'GUID不能为空';
		}
		if($response['Success']){
			$year = $params['year'];
			$upfile = $_FILES['file'];
			if(strpos($this->array_iconv($upfile["name"]), '韩城公司钢材周销售')===false)
			{
				$response['Success'] = 0;
				$response['Message'] = '文件名非韩城公司钢材周销售！';
			}
			$user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
			if(empty($user_infos)){
				// $response['Success'] = 0;
				$response['Message'] = '用户不存在';
			}
			if($response['Success']){
				if ($upfile['error']==0)
				{
					if (is_uploaded_file($upfile['tmp_name'])) {
						$uptypes=array(
							'application/vnd.ms-excel',
							'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
						);
						if (in_array($upfile['type'], $uptypes)) {
							$extName = strtolower(end(explode('.', $upfile ['name'])));
							$filename = $this->random(5); // 设置随机数长度
							$extName=strtolower(end(explode('.', $upfile['name']))); // 取得扩展名
							$name1=date('YmdHis').".".$extName;
							$dir = UPLOADFILE;
							if (!file_exists($dir)) {
								if (mkdir($dir, 0777)) {
									$dest =$dir."/".$filename.$name1;
									
									if (move_uploaded_file($upfile ['tmp_name'], $dest)) {
										$response['Success'] = 1;
										$response['Message'] = '上传成功';
										$sheet11 = $this->readSheet($dest);
										$response['Result'] = $this->array_iconv($sheet11);
										$response['oldtmp'] = $this->array_iconv($upfile["name"]);
										$response['File'] = $filename.$name1;
										$response['Year'] = $year;
									} else {
										$response['Success'] = 0;
										$response['Message'] = '上传失败！目录权限不足';
									}
								} else {
									$response['Success'] = 0;
									$response['Message'] = '目录不存在，上传失败！';
								}
							} else {
								$dest = $dir."/".$filename.$name1;
								if (move_uploaded_file($upfile ['tmp_name'], $dest)) {
									$response['Success'] = 1;
									$response['Message'] = '上传成功';
									$sheet11 = $this->readSheet($dest);
									$response['Result'] = $this->array_iconv($sheet11);
									$response['oldtmp'] = $this->array_iconv($upfile["name"]);
									$response['File'] = $filename.$name1;
									$response['Year'] = $year;
								} else {
									$response['Success'] = 0;
									$response['Message'] = '上传失败！目录权限不足';
								}
							}
						} else {
							$response['Success'] = 0;
							$response['Message'] = '上传失败！检查文件是否是Excel文件';
						}
					} else {
						clearstatcache(); //清除文件缓存信息
						$response['Success'] = 0;
						$response['Message'] = '上传失败！';
					}
				}
				else
				{
					alert('上传失败！');
					goURL("sgbbdr.php?view=hcgczxsupload&GUID=".$GUID."&mode=".$mode);
					exit;
				}
			}
		}
		echo $this->pri_JSON($response);
		exit;
	}


	function sheetrxs($params)
	{
		
		$GUID = $params['GUID'];
		$mode = $params['mode'];//1=客户端，2=安卓，3=iOS
		$this->assign('mode', $mode);
		$response = array(
			'Success'  => 1,
			'Message'  => '',
			'GUID'     => $GUID,
			'mode'     => $mode,
		);
		if($GUID==""){
			$response['Success'] = 0;
			$response['Message'] = 'GUID不能为空';
		}
		if($response['Success']){
			$year = $params['year'];
			$upfile = $_FILES['file'];
			if(strpos($this->array_iconv($upfile["name"]), '研究院公司销售日报')===false)
			{
				$response['Success'] = 0;
				$response['Message'] = '文件名非研究院公司销售日报！';
			}
			$user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
			if(empty($user_infos)){
				// $response['Success'] = 0;
				$response['Message'] = '用户不存在';
			}
			if($response['Success']){
				if ($upfile['error']==0)
				{
					if (is_uploaded_file($upfile['tmp_name'])) {
						$uptypes=array(
							'application/vnd.ms-excel',
							'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
						);
						if (in_array($upfile['type'], $uptypes)) {
							$extName = strtolower(end(explode('.', $upfile ['name'])));
							$filename = $this->random(5); // 设置随机数长度
							$extName=strtolower(end(explode('.', $upfile['name']))); // 取得扩展名
							$name1=date('YmdHis').".".$extName;
							$dir = UPLOADFILE;
							if (!file_exists($dir)) {
								if (mkdir($dir, 0777)) {
									$dest =$dir."/".$filename.$name1;
									
									if (move_uploaded_file($upfile ['tmp_name'], $dest)) {
										$response['Success'] = 1;
										$response['Message'] = '上传成功';
										$sheet11 = $this->readSheet($dest);
										$response['Result'] = $this->array_iconv($sheet11);
										$response['oldtmp'] = $this->array_iconv($upfile["name"]);
										$response['File'] = $filename.$name1;
										$response['Year'] = $year;
									} else {
										$response['Success'] = 0;
										$response['Message'] = '上传失败！目录权限不足';
									}
								} else {
									$response['Success'] = 0;
									$response['Message'] = '目录不存在，上传失败！';
								}
							} else {
								$dest = $dir."/".$filename.$name1;
								if (move_uploaded_file($upfile ['tmp_name'], $dest)) {
									$response['Success'] = 1;
									$response['Message'] = '上传成功';
									$sheet11 = $this->readSheet($dest);
									$response['Result'] = $this->array_iconv($sheet11);
									$response['oldtmp'] = $this->array_iconv($upfile["name"]);
									$response['File'] = $filename.$name1;
									$response['Year'] = $year;
								} else {
									$response['Success'] = 0;
									$response['Message'] = '上传失败！目录权限不足';
								}
							}
						} else {
							$response['Success'] = 0;
							$response['Message'] = '上传失败！检查文件是否是Excel文件';
						}
					} else {
						clearstatcache(); //清除文件缓存信息
						$response['Success'] = 0;
						$response['Message'] = '上传失败！';
					}
				}
				else
				{
					alert('上传失败！');
					goURL("sgbbdr.php?view=hcgcrxsupload&GUID=".$GUID."&mode=".$mode);
					exit;
				}
			}
		}
		echo $this->pri_JSON($response);
		exit;
	}



	function dosheetzxs($params)
	{
		$GUID = $params['GUID'];
		$mode = $params['mode'];//1=客户端，2=安卓，3=iOS
		$filename = iconv("utf-8","gb2312//IGNORE",$params['oldtmp']);
		$response = array(
			'Success'  => 1,
			'Message'  => '',
			// 'GUID'     => $GUID,
			// 'mode'     => $mode,
		);
		if($GUID==""){
			$response["Success"] = 0;
			$response['Message'] = 'GUID不能为空';
		}

		if($response["Success"])
		{   
			$user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
			if(empty($user_infos)){
				$response["Success"] = 0;
				$response['Message'] = '用户不存在';
			}

			$sheetList = explode(',', $params["sheet"]);
			$file = UPLOADFILE."/".$params["tmp"];
			
			if((empty($sheetList[0])&&$sheetList[0]!=0)||$params["sheet"]=='')
			{ 

				$response["Success"] = 0;
				$response['Message'] = '未选择工作表';
			}

			$ymddate = mysql_real_escape_string($params["year"]);
			if(empty($ymddate))
			{
				$response["Success"] = 0;
				$response['Message'] = '时间不能为空';
			}
			if($response["Success"])
			{
				require_once "../PHPExcel/PHPExcel.php";
				$type = pathinfo($file); 
				$type = strtolower($type["extension"]);
				if ($type=='xlsx') { 
					$type='Excel2007'; 
				}elseif($type=='xls') { 
					$type = 'Excel5';
				}
				$objReader = PHPExcel_IOFactory::createReader($type);//判断使用哪种格式
				$objReader ->setReadDataOnly(true); //只读取数据,会智能忽略所有空白行,这点很重要！！！
				$objPHPExcel = $objReader->load($file); //加载Excel文件
				$sheets = $objPHPExcel->getSheetNames();
				$sheetCount = $objPHPExcel->getSheetCount();//获取sheet工作表总个数

				$flag = true;
				foreach($sheetList as $sheet_index1)
				{
					$sheet_index = $sheetCount-1-$sheet_index1;
					// $sheet = $objPHPExcel->setActiveSheetIndexByName($sheet_v);
					$sheet = $objPHPExcel->getSheet($sheet_index);
					// $sheet_array = $objPHPExcel->getSheet($sheet_index)->toArray();
					// print_r($sheet_array);
					// exit;
					//得到当前工作表对象
					// $curSheet = $objPHPExcel->getActiveSheet();
					//获取当前工作表最大行数
				
				
					$rows = $sheet->getHighestRow();
					//获取当前工作表最大列数,返回的是最大的列名，如：B 
					// $cols = $sheet->getHighestColumn();
					// print_r($cols);
					//将当前工作表名当键，内容为值存入数组

					$sheet_v = iconv("utf-8","gb2312//IGNORE",$sheets[$sheet_index]);
					$sheetname .= $sheet_v.",";
					
					$data = array();
					$listA = array();
					$maxrow='0';
					for($k = 3; $k <= $rows; $k++){
						$key = "A".$k;
						$listA[$k] = iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getValue());
						// $listA[$k] = $sheet->getCell($key)->getValue();
						
						if(empty($listA[$k])){
							$listA[$k] = $listA[$k-1];
						}
						if($maxrow!=0){//建材合计为最后一行，后面的不要了
							unset($listA[$k]);
						}
						if($listA[$k]=='建材合计'){ 
							$maxrow=$k;
						}

					}
					
					
					// print_r($listA);
					$t = array_keys($listA);

					
					//大写字母A的ASCII值是65 A-Z对应65-90
					for($j = 'B'; $j <= 'H'; $j++ ){
						for($k = 3; $k <= $maxrow; $k++){
							$key = $j.$k;
							  $info = iconv("utf-8","gb2312//IGNORE", $sheet->getCell("B".$k)->getValue());
							    // $info = $sheet->getCell("B".$k)->getValue();
							
								$data[$k]['A']=$listA[$k];
								if($listA[$k]=='小计'){
									$data[$k]['A']=$data[$k-1]['A'];
									
								}
								if($listA[$k]=='小计'&&$j=='B'){
									$data[$k]['B']=$listA[$k];
								}else{
								  $data[$k][$j] = iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue());
                                    // $data[$k][$j] = $sheet->getCell($key)->getCalculatedValue();
								}
								
								$data[$k]['Z'] = "SGHCGCZXS";
						}
					}
;
					
					
				    $A2 = iconv("utf-8","gb2312//IGNORE", $sheet->getCell('A2')->getValue());

					$regex_year="'\d{4}年'is";
					preg_match($regex_year,$A2,$yeararr);//print_r($year);
					$year = explode('年', $yeararr[0]);
					$regex_month="'\d{1,2}月'is";
					preg_match($regex_month,$A2,$montharr);//print_r($year);
					$month = explode('月', $montharr[0]);
					$regex_day="'\d{1,2}日'is";
					preg_match($regex_day,$A2,$dayarr);//print_r($year);
					$day = explode('日', $dayarr[0]);
					$date =$year[0].'-'.$month[0].'-'.$day[0];
					
                    $date =date("Y-m-d",strtotime($date));
					$G2 = iconv("utf-8","gb2312//IGNORE", $sheet->getCell('G2')->getValue());
					// $G2 = $sheet->getCell('G2')->getValue();

                   
				
					$this->_dao->execute("delete from sg_data_table where dta_ym='$date' and dta_type ='SGHCGCZXS'");
					$basesql = "insert into sg_data_table(dta_1,dta_2,dta_3,dta_4,dta_5,dta_6,dta_7,dta_8,dta_vartype,dta_type,dta_ym,createtime,createuser) values";
					$valSql = "";
				
					foreach($data as $rows=>$columns)
					{
						
						$dta_1 = $columns['A'];
						$dta_2 = $columns['B'];
						if($rows==3){
							$dta_3 = $columns['C'];
							$dta_4 = $columns['D'];
							$dta_5 = $columns['E'];
						
							$dta_6 =  $columns['F'];
							$dta_7 =  $columns['G'];
							$dta_8 =  $columns['H'];
				
						}else{
							$dta_3 = str_replace("#DIV/0!","",sprintf("%.1f",round($columns['C'],1)));
							$dta_4 = str_replace("#DIV/0!","",sprintf("%.1f",round($columns['D'],1)));
							$dta_5 = str_replace("#DIV/0!","",sprintf("%.1f",round($columns['E'],1)));
						
							$dta_6 = str_replace("#DIV/0!","",sprintf("%.2f",round($columns['N'],2)));
							$dta_7 = str_replace("#DIV/0!","",sprintf("%.0f",round($columns['G'],0)));
							$dta_8 = str_replace("#DIV/0!","",sprintf("%.1f",round($columns['H'],1)));
						}

						$dta_type=$columns['Z'];
						$dta_vartype=$G2;
						$dta_ym = $date;
						
						$valSql.="('$dta_1','$dta_2','$dta_3','$dta_4','$dta_5','$dta_6','$dta_7','$dta_8','$dta_vartype','$dta_type','$dta_ym',NOW(),'".$user_infos['Uid']."'),";
						
					}
					
					$insertsql = substr($basesql.$valSql, 0, -1);
			       
					if(!empty($valSql))
					{
						$this->_dao->execute($insertsql);
						// echo $insertsql."<br><br>";
					}else{
						// 失败
						$error_sheet2[] = $sheet_v;
						$flag = false;
					}
				}
				if(!$flag)
				{
				
					$error2 = implode(',', $error_sheet2);
					
					if(!empty($error2))
					{
						$msg2 = "工作表：".iconv("utf-8","gb2312//IGNORE",$error2)."出现未知错误，导入失败";
					}
					$response["Success"] = 0;
					$response['Message'] = $msg2;
				}else{
					$response["Success"] = 1;
					$response['Message'] = '导入成功';
					$sheetname = substr($sheetname, 0, -1);
					$sql ="insert into sg_data_table_log(uptype,filepath,filename,sheetname,createtime,createuser,uptypedate) values('12','$file','$filename','$sheetname',now(),'".$user_infos['Uid']."','".$ymddate."')";
					$this->_dao->execute($sql);
			
				}
				unlink($file);
			}
		}
		echo $this->pri_JSON($response);
		exit;
	}

	function dosheetrxs($params)
	{
	
		$GUID = $params['GUID'];
		$mode = $params['mode'];//1=客户端，2=安卓，3=iOS
		$filename = iconv("utf-8","gb2312//IGNORE",$params['oldtmp']);
		$response = array(
			'Success'  => 1,
			'Message'  => '',
			// 'GUID'     => $GUID,
			// 'mode'     => $mode,
		);
		if($GUID==""){
			$response["Success"] = 0;
			$response['Message'] = 'GUID不能为空';
		}

		if($response["Success"])
		{   
			$user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
			if(empty($user_infos)){
				$response["Success"] = 0;
				$response['Message'] = '用户不存在';
			}

			$sheetList = explode(',', $params["sheet"]);
		
			$file = UPLOADFILE."/".$params["tmp"];
			
			if((empty($sheetList[0])&&$sheetList[0]!=0)||$params["sheet"]=='')
			{ 

				$response["Success"] = 0;
				$response['Message'] = '未选择工作表';
			}

			$ymddate = mysql_real_escape_string($params["year"]);
			if(empty($ymddate))
			{
				$response["Success"] = 0;
				$response['Message'] = '时间不能为空';
			}
			if($response["Success"])
			{
				require_once "../PHPExcel/PHPExcel.php";
				$type = pathinfo($file); 
				$type = strtolower($type["extension"]);
				if ($type=='xlsx') { 
					$type='Excel2007'; 
				}elseif($type=='xls') { 
					$type = 'Excel5';
				}
				$objReader = PHPExcel_IOFactory::createReader($type);//判断使用哪种格式
				$objReader ->setReadDataOnly(true); //只读取数据,会智能忽略所有空白行,这点很重要！！！
				$objPHPExcel = $objReader->load($file); //加载Excel文件
				$sheets = $objPHPExcel->getSheetNames();
				$sheetCount = $objPHPExcel->getSheetCount();//获取sheet工作表总个数

				$flag = true;
				foreach($sheetList as $sheet_index1)
				{
					$sheet_index = $sheetCount-1-$sheet_index1;
					// $sheet = $objPHPExcel->setActiveSheetIndexByName($sheet_v);
					$sheet = $objPHPExcel->getSheet($sheet_index);
					// $sheet_array = $objPHPExcel->getSheet($sheet_index)->toArray();
					// print_r($sheet_array);
					// exit;
					//得到当前工作表对象
					// $curSheet = $objPHPExcel->getActiveSheet();
					//获取当前工作表最大行数
				
				
					$rows = $sheet->getHighestRow();
					//获取当前工作表最大列数,返回的是最大的列名，如：B 
					// $cols = $sheet->getHighestColumn();
					// print_r($cols);
					//将当前工作表名当键，内容为值存入数组

					$sheet_v = iconv("utf-8","gb2312//IGNORE",$sheets[$sheet_index]);
					$sheetname .= $sheet_v.",";
					
					$data = array();
					$listA = array();
					$maxrow='0';
					
					for($k = 3; $k <= $rows; $k++){
						$key = "B".$k;
						$listA[$k] = iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getValue());
						//$listA[$k] = $sheet->getCell($key)->getValue();
						
						if(empty($listA[$k])){
							$listA[$k] = $listA[$k-1];
						}
						if($maxrow!=0){//建材合计为最后一行，后面的不要了
							unset($listA[$k]);
						}
						
					
						if(strpos($listA[$k],'合') !== false&&strpos($listA[$k],'计') !== false){ 
							$maxrow=$k;
						}

					}
			
					
					// print_r($listA);
					$t = array_keys($listA);

					
					//大写字母A的ASCII值是65 A-Z对应65-90
					for($j = 'C'; $j <= 'L'; $j++ ){
						for($k = 3; $k <= $maxrow; $k++){
							$key = $j.$k;
							    $info = iconv("utf-8","gb2312//IGNORE", $sheet->getCell("C".$k)->getValue());
							   //$info = $sheet->getCell("B".$k)->getValue();
							
								$data[$k]['B']=$listA[$k];
								if($listA[$k]=='小计'){
									$data[$k][$j] = iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue());
									$data[$k]['B']=$listA[$k];
									$data[$k]['D']=$data[$k-1]['D'];
									
								}else{
								   $data[$k][$j] = iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue());
                                    //$data[$k][$j] = $sheet->getCell($key)->getCalculatedValue();
								}
								
								$data[$k]['Z'] = "SGHCGCRXS";
						}
					}
				
 
					$date =gmdate("Y-m-d", PHPExcel_Shared_Date::ExcelToPHP($sheet->getCell("B2")->getValue())); 
				//      $B2 = iconv("utf-8","gb2312//IGNORE", $sheet->getCell('B2')->getValue());

				// 	$regex_year="'\d{4}年'is";
				// 	preg_match($regex_year,$B2,$yeararr);//print_r($year);
				// 	$year = explode('年', $yeararr[0]);
				// 	$regex_month="'\d{1,2}月'is";
				// 	preg_match($regex_month,$B2,$montharr);//print_r($year);
				// 	$month = explode('月', $montharr[0]);
				// 	$regex_day="'\d{1,2}日'is";
				// 	preg_match($regex_day,$B2,$dayarr);//print_r($year);
				// 	$day = explode('日', $dayarr[0]);
				// 	$date =$year[0].'-'.$month[0].'-'.$day[0];

				// 	// $G2 = iconv("utf-8","gb2312//IGNORE", $sheet->getCell('G2')->getValue());
				// 	// $G2 = $sheet->getCell('G2')->getValue();

                //    echo '<pre>';print_R($yeararr);exit;
				
					$this->_dao->execute("delete from sg_data_table where dta_ym='$date' and dta_type ='SGHCGCRXS'");
					$basesql = "insert into sg_data_table(dta_1,dta_2,dta_3,dta_4,dta_5,dta_6,dta_7,dta_8,dta_9,dta_10,dta_11,dta_type,dta_ym,createtime,createuser) values";
					$valSql = "";
				
					foreach($data as $rows=>$columns)
					{
						
						$dta_1 = $columns['B'];
						$dta_2 = $columns['C'];
						$dta_3 = $columns['D'];
						$dta_4 = $columns['E'];
						$dta_5 = addslashes($columns['F']);//保存'\'
						if($rows==3){	
							$dta_6 =  $columns['G'];
							$dta_7 =  $columns['H'];
							$dta_8 =  $columns['I'];
							$dta_9 =  $columns['J'];
							$dta_10 =  $columns['K'];
						
				
						}else{
							$dta_6 = str_replace("#DIV/0!","",sprintf("%.0f",round($columns['G'],0)));
							$dta_7 = str_replace("#DIV/0!","",sprintf("%.0f",round($columns['H'],0)));
							$dta_8 = str_replace("#DIV/0!","",sprintf("%.0f",round($columns['I'],0)));
							$dta_9 = str_replace("#DIV/0!","",sprintf("%.0f",round($columns['J'],0)));
							$dta_10 = str_replace("#DIV/0!","",sprintf("%.2f",round($columns['K'],2)));
							
						}
						$dta_11 =  $columns['L'];
						
						$dta_type=$columns['Z'];
					
						$dta_ym = $date;
						
						$valSql.="('$dta_1','$dta_2','$dta_3','$dta_4','$dta_5','$dta_6','$dta_7','$dta_8','$dta_9','$dta_10','$dta_11','$dta_type','$dta_ym',NOW(),'".$user_infos['Uid']."'),";
						
					}
					
					$insertsql = substr($basesql.$valSql, 0, -1);
			       
					if(!empty($valSql))
					{
						$this->_dao->execute($insertsql);
						// echo $insertsql."<br><br>";
					}else{
						// 失败
						$error_sheet2[] = $sheet_v;
						$flag = false;
					}
				}
				if(!$flag)
				{
				
					$error2 = implode(',', $error_sheet2);
					
					if(!empty($error2))
					{
						$msg2 = "工作表：".iconv("utf-8","gb2312//IGNORE",$error2)."出现未知错误，导入失败";
					}
					$response["Success"] = 0;
					$response['Message'] = $msg2;
				}else{
					$response["Success"] = 1;
					$response['Message'] = '导入成功';
					$sheetname = substr($sheetname, 0, -1);
					$sql ="insert into sg_data_table_log(uptype,filepath,filename,sheetname,createtime,createuser,uptypedate) values('14','$file','$filename','$sheetname',now(),'".$user_infos['Uid']."','".$ymddate."')";
					$this->_dao->execute($sql);
			
				}
				unlink($file);
			}
		}
		echo $this->pri_JSON($response);
		exit;
	}


	function sheetyjy($params)
	{
		
		$GUID = $params['GUID'];
		$mode = $params['mode'];//1=客户端，2=安卓，3=iOS
		$this->assign('mode', $mode);
		$response = array(
			'Success'  => 1,
			'Message'  => '',
			'GUID'     => $GUID,
			'mode'     => $mode,
		);
		if($GUID==""){
			$response['Success'] = 0;
			$response['Message'] = 'GUID不能为空';
		}
		if($response['Success']){
			$year = $params['year'];
			$upfile = $_FILES['file'];
			if(strpos($this->array_iconv($upfile["name"]), '研究院公司结算月报')===false)
			{
				$response['Success'] = 0;
				$response['Message'] = '文件名非研究院公司结算月报！';
			}
			$user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
			if(empty($user_infos)){
				// $response['Success'] = 0;
				$response['Message'] = '用户不存在';
			}
			if($response['Success']){
				if ($upfile['error']==0)
				{
					if (is_uploaded_file($upfile['tmp_name'])) {
						$uptypes=array(
							'application/vnd.ms-excel',
							'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
						);
						if (in_array($upfile['type'], $uptypes)) {
							$extName = strtolower(end(explode('.', $upfile ['name'])));
							$filename = $this->random(5); // 设置随机数长度
							$extName=strtolower(end(explode('.', $upfile['name']))); // 取得扩展名
							$name1=date('YmdHis').".".$extName;
							$dir = UPLOADFILE;
							if (!file_exists($dir)) {
								if (mkdir($dir, 0777)) {
									$dest =$dir."/".$filename.$name1;
									
									if (move_uploaded_file($upfile ['tmp_name'], $dest)) {
										$response['Success'] = 1;
										$response['Message'] = '上传成功';
										$sheet11 = $this->readSheet($dest);
										$response['Result'] = $this->array_iconv($sheet11);
										$response['File'] = $filename.$name1;
										$response['Year'] = $year;
										$response['oldtmp'] = $this->array_iconv($upfile["name"]);
									} else {
										$response['Success'] = 0;
										$response['Message'] = '上传失败！目录权限不足';
									}
								} else {
									$response['Success'] = 0;
									$response['Message'] = '目录不存在，上传失败！';
								}
							} else {
								$dest = $dir."/".$filename.$name1;
								if (move_uploaded_file($upfile ['tmp_name'], $dest)) {
									$response['Success'] = 1;
									$response['Message'] = '上传成功';
									$sheet11 = $this->readSheet($dest);
									$response['Result'] = $this->array_iconv($sheet11);
									$response['File'] = $filename.$name1;
									$response['Year'] = $year;
									$response['oldtmp'] =$this->array_iconv($upfile["name"]);
								} else {
									$response['Success'] = 0;
									$response['Message'] = '上传失败！目录权限不足';
								}
							}
						} else {
							$response['Success'] = 0;
							$response['Message'] = '上传失败！检查文件是否是Excel文件';
						}
					} else {
						clearstatcache(); //清除文件缓存信息
						$response['Success'] = 0;
						$response['Message'] = '上传失败！';
					}
				}
				else
				{
					alert('上传失败！');
					goURL("sgbbdr.php?view=yjyybupload&GUID=".$GUID."&mode=".$mode);
					exit;
				}
			}
		}
		echo $this->pri_JSON($response);
		exit;
	}

	function dosheetyjy($params)
	{   
		$GUID = $params['GUID'];
		$mode = $params['mode'];//1=客户端，2=安卓，3=iOS
		$filename = iconv("utf-8","gb2312//IGNORE",$params['oldtmp']);

		$response = array(
			'Success'  => 1,
			'Message'  => '',
			// 'GUID'     => $GUID,
			// 'mode'     => $mode,
		);
		if($GUID==""){
			$response["Success"] = 0;
			$response['Message'] = 'GUID不能为空';
		}

		if($response["Success"])
		{   
			$user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
			if(empty($user_infos)){
				$response["Success"] = 0;
				$response['Message'] = '用户不存在';
			}

			$sheetList = explode(',', $params["sheet"]);
			$file = UPLOADFILE."/".$params["tmp"];
			
			if((empty($sheetList[0])&&$sheetList[0]!=0)||$params["sheet"]=='')
			{ 

				$response["Success"] = 0;
				$response['Message'] = '未选择工作表';
			}

			$year = mysql_real_escape_string($params["year"]);
			if(empty($year))
			{
				$response["Success"] = 0;
				$response['Message'] = '年份出错';
			}
			if($response["Success"])
			{
				require_once "../PHPExcel/PHPExcel.php";
				$type = pathinfo($file); 
				$type = strtolower($type["extension"]);
				if ($type=='xlsx') { 
					$type='Excel2007'; 
				}elseif($type=='xls') { 
					$type = 'Excel5';
				}
				$objReader = PHPExcel_IOFactory::createReader($type);//判断使用哪种格式
				$objReader ->setReadDataOnly(true); //只读取数据,会智能忽略所有空白行,这点很重要！！！
				$objPHPExcel = $objReader->load($file); //加载Excel文件
				$sheets = $objPHPExcel->getSheetNames();
				$sheetCount = $objPHPExcel->getSheetCount();//获取sheet工作表总个数

				$flag = true;
				foreach($sheetList as $sheet_index1)
				{
					$sheet_index = $sheetCount-1-$sheet_index1;
					// $sheet = $objPHPExcel->setActiveSheetIndexByName($sheet_v);
					$sheet = $objPHPExcel->getSheet($sheet_index);
					// $sheet_array = $objPHPExcel->getSheet($sheet_index)->toArray();
					// print_r($sheet_array);
					// exit;
					//得到当前工作表对象
					// $curSheet = $objPHPExcel->getActiveSheet();
					//获取当前工作表最大行数
				
				
					$rows = $sheet->getHighestRow();
					//获取当前工作表最大列数,返回的是最大的列名，如：B 
					$cols = $sheet->getHighestColumn();
					// print_r($cols);
					//将当前工作表名当键，内容为值存入数组

			
					$sheet_v = iconv("utf-8","gb2312//IGNORE",$sheets[$sheet_index]);
					$sheetname .= $sheet_v.",";

					$B3=iconv("utf-8","gb2312//IGNORE", $sheet->getCell('B3')->getValue());
					$regex_year="'\d{4}年'is";
					preg_match($regex_year,$B3,$yeararr);//print_r($year);
					$dryear = explode('年', $yeararr[0]);
					
					if($year!=$dryear[0])
					{
						$error_sheet1[] = $sheet_v;
						$flag = false;
						continue;
					}


                  
				
                 

					$data = array();
					$listA = array();
					$maxrow='0';
					for($k = 4; $k <= $rows; $k++){
						$key = "B".$k;
                         $listA[$k] = iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getValue());
						//  $listA[$k] = $sheet->getCell($key)->getValue();
						
						if(empty($listA[$k])){
							$listA[$k] = $listA[$k-1];
						}
						if($maxrow!=0){//总计为最后一行，后面的不要了
							unset($listA[$k]);
						}
						if($listA[$k]=='总计'){ 
							$maxrow=$k;
						}

					}
                    $key_arr='';
					for($i=66;$i<91;$i++){
						$key_arr[]= strtoupper(chr($i));//输出大写字母
					}
					for($j='A';$j<='C';$j++){
						for($l=66;$l<91;$l++){ 
							$key_arr[]= $j.strtoupper(chr($l));//输出大写字母
						}
					}
                    

					
					
					// print_r($listA);
					$t = array_keys($listA); 
					
				

					foreach ($key_arr as $kzm => $vzm) {
						for($k = 4; $k <= $maxrow; $k++){
							$key = $vzm.$k;
								$data[$k][$vzm] = iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue());
								//  $data[$k][$vzm] = $sheet->getCell($key)->getCalculatedValue();
							  
						}
					}
					
				
				 $n=0;
			

				$this->_dao->execute("delete from sg_data_table where dta_15='$year' and dta_type ='SGYJYJSYB'");

				 

				$basesql = "insert into sg_data_table(dta_1,dta_2,dta_3,dta_4,dta_type,dta_vartype,dta_ym,createtime,createuser,dta_15) values";
				
				
				//循环----------------------------------------------------------------------------
				
			
				for($k = 4; $k <= $maxrow; $k++){
				$num='1';
					for($j = 65; $j <= 90; $j++ ){
						$key = chr($j).$k;
						$data[$k][chr($j)] = iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue());
						// $data[$k][chr($j)] = $sheet->getCell($key)->getCalculatedValue();
						if($j!='65'&&$j!='66'){
							$zm[$num]=chr($j);
							$num++;
						}
						
					}
					for($j = 65; $j <= 90; $j++ ){
						$key = "A".chr($j).$k;
						$data[$k]["A".chr($j)] = iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue());
						// $data[$k]["A".chr($j)] = $sheet->getCell($key)->getCalculatedValue();
						$zm[$num]="A".chr($j);
						$num++;
					}
					for($j = 65; $j <= 90; $j++ ){
						$key = "B".chr($j).$k;
						$data[$k]["B".chr($j)] = iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue());
						// $data[$k]["B".chr($j)] =  $sheet->getCell($key)->getCalculatedValue();
						$zm[$num]="B".chr($j);
						$num++;
					}
					for($j = 65; $j <= 80; $j++ ){
						$key = "C".chr($j).$k;
						$data[$k]["C".chr($j)] = iconv("utf-8","gb2312//IGNORE", $sheet->getCell($key)->getCalculatedValue());
						// $data[$k]["C".chr($j)] = $sheet->getCell($key)->getCalculatedValue();
						$zm[$num]="C".chr($j);
						$num++;
					}
				}
				
				$g=0;
				$n=1;
				foreach($zm as $nk=>$nv)
					{   
						if($nv=='CG'){//CG及前面的只有两列  以后碰到不规律的在这调节
							$liearr[$g][$n]=$nv;   				
							$n=1;
							
							$g++;
							$liearr[$g]='';
							continue;
						}
						if($n==3){	
							$liearr[$g][$n]=$nv;   				
							$n=1;
							
							$g++;
							$liearr[$g]='';
								
							 
						}else{
							$liearr[$g][$n]=$nv;   
							$n++;
						}
						
					}

					$liearr=array_filter($liearr);//防止最后一个空的去掉
				
				foreach($liearr as $lk=>$lv){
					foreach($data as $rows=>$columns)
					{
	
	
					
					   if($rows==4){
						$dat_1_1=$columns['B'];
						$dat_1_2=$columns[$lv[1]];
						$dat_1_3=$columns[$lv[2]];
						$dat_1_4=$columns[$lv[3]];
						//确认数据年份月份
						$regex_month="'\d{1,2}月'is";
						preg_match($regex_month,$columns[$lv[1]],$montharr);//print_r($year);
						$month = explode('月', $montharr[0]);
						if($month[0]!=''){
							$dta_ym_1=$year.'-'.$month[0];
							$dta_ym_1 =date("Y-m",strtotime($dta_ym_1));
							
						}else{
							$dta_ym_1=$year;
						}
	
						//确认数据dta_vartype的标识
						if(strpos($columns[$lv[1]],'汉钢')!==false){ 
							$dta_vartype_1='SGHG';
						}else if(strpos($columns[$lv[1]],'龙钢')!==false){
							$dta_vartype_1='SGLG';
						}else if(strpos($columns[$lv[1]],'委托加工')!==false&&strpos($columns[$lv[1]],'当月')!==false){
							$dta_vartype_1='SGDYWTJG';
						}else if(strpos($columns[$lv[1]],'委托加工')!==false&&strpos($columns[$lv[1]],'当年')!==false){
							$dta_vartype_1='SGDNWTJG';
						}else if(strpos($columns[$lv[1]],'10月累计')!==false){
							$dta_vartype_1='SGSYlJ';
						}else if(strpos($columns[$lv[1]],'当月合计')!==false){
							$dta_vartype_1='SGDYHJ';
						}else if(strpos($columns[$lv[1]],'当年累计')!==false){
							$dta_vartype_1='SGDNlJ';
						}else{
							$dta_vartype_1='SGQT';
						}
			
	
					   }else if($rows==5){
						$dat_1_1=$columns['B'];
						$dat_1_2=$columns[$lv[1]];
						$dat_1_3=$columns[$lv[2]];
						$dat_1_4=$columns[$lv[3]];
					   }else{
						$dat_1_1=$columns['B'];
						$dat_1_2=sprintf("%.2f",round($columns[$lv[1]],2));
						$dat_1_3=sprintf("%.2f",round($columns[$lv[2]],2));
						$dat_1_4=sprintf("%.2f",round($columns[$lv[3]],2));
					   }
	
	
					
						
					   $values.="('$dat_1_1','$dat_1_2','$dat_1_3','$dat_1_4','SGYJYJSYB','$dta_vartype_1','$dta_ym_1',NOW(),'".$user_infos['Uid']."','$year'),";
					}
				}
				
				//end--------------------------------------------------------------------------------------------------------------------

              
				$tsql = substr($values, 0, -1);
				$this->_dao->execute($basesql.$tsql);

					

	     
			      
				}
				if(!$flag)
				{
					$error1 = implode(',', $error_sheet1);
					$error2 = implode(',', $error_sheet2);
					if(!empty($error1))
					{
						$msg1 = "工作表：".iconv("utf-8","gb2312//IGNORE", $error1)."的B3单元格日期有误，导入失败";
					}
					if(!empty($error2))
					{
						$msg2 = "工作表：".iconv("utf-8","gb2312//IGNORE",$error2)."出现未知错误，导入失败";
					}
					$response["Success"] = 0;
					$response['Message'] = $msg1."\\n".$msg2;
				}else{
					$response["Success"] = 1;
					$response['Message'] = '导入成功';
					$sheetname = substr($sheetname, 0, -1);
					$sql ="insert into sg_data_table_log(uptype,filepath,filename,sheetname,createtime,createuser,uptypedate) values('13','$file','$filename','$sheetname',now(),'".$user_infos['Uid']."','".$year."')";
					$this->_dao->execute($sql);
				}
				unlink($file);
			}
		}
		echo $this->pri_JSON($response);
		exit;
	}






	//php获取中文字符拼音首字母
	function getFirstCharter($str){
		if(empty($str)){
				return '';
		}
		$fchar = ord($str[0]);
		if($fchar >= ord('A') && $fchar <= ord('z')){
				return strtoupper($str[0]);
		}
		$s1=iconv('UTF-8','gb2312',$str);
		$s2=iconv('gb2312','UTF-8',$s1);
		$s=$s2==$str?$s1:$str;
		$asc=ord($s[0])*256+ord($s[1])-65536;
		
		if($asc>=-20319&&$asc<=-20284) return 'A';
		if($asc>=-20283&&$asc<=-19776) return 'B';
		if($asc>=-19775&&$asc<=-19219) return 'C';
		if($asc>=-19218&&$asc<=-18711) return 'D';
		if($asc>=-18710&&$asc<=-18527) return 'E';
		if($asc>=-18526&&$asc<=-18240) return 'F';
		if($asc>=-18239&&$asc<=-17923) return 'G';
		if($asc>=-17922&&$asc<=-17418) return 'H';
		if($asc>=-17417&&$asc<=-16475) return 'J';
		if($asc>=-16474&&$asc<=-16213) return 'K';
		if($asc>=-16212&&$asc<=-15641) return 'L';
		if($asc>=-15640&&$asc<=-15166) return 'M';
		if($asc>=-15165&&$asc<=-14923) return 'N';
		if($asc>=-14922&&$asc<=-14915) return 'O';
		if($asc>=-14914&&$asc<=-14631) return 'P';
		if($asc>=-14630&&$asc<=-14150) return 'Q';
		if($asc>=-14149&&$asc<=-14091) return 'R';
		if($asc>=-14090&&$asc<=-13319) return 'S';
		if($asc>=-13318&&$asc<=-12839) return 'T';
		if($asc>=-12838&&$asc<=-12557) return 'W';
		if($asc>=-12556&&$asc<=-11848) return 'X';
		if($asc>=-11847&&$asc<=-11056) return 'Y';
		if($asc>=-11055&&$asc<=-10247) return 'Z';
		return '其他';
	}



	function uploadFileAll($params){
		$upfile = $_FILES['file'];
		$GUID = $params['GUID'];
		$mode = $params['mode'];
		$response = array(
			'Success'  => 1,
			'Message'  => '',
			'GUID'     => $GUID,
			'mode'     => $mode,
		);
		// 不同页面上传类型控制
		switch($params['filetype'])
		{
			case 'excel':
				$uptypes=array(
					'application/vnd.ms-excel',
					'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
				);
				$filetype = "Excel文件";
			break;
			case 'word':
				$uptypes=array(
					'application/msword',
					'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
				);
				$filetype = "Word文件";
			break;
			default:
				$uptypes=array(
					'application/vnd.ms-excel',
					'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
				);
				$filetype = "Excel文件";
			break;
		}

		if($GUID==""){
			$response['Success'] = 0;
			$response['Message'] = 'GUID不能为空';
		}
		if($response['Success']){
			$user_infos = $this->t1Dao->get_userinfo_byguid($GUID);
			if(empty($user_infos)){
				$response['Success'] = 0;
				$response['Message'] = '用户不存在';
			}
			if($response['Success']){
				if ($upfile['error']==0)
				{
					if (is_uploaded_file($upfile['tmp_name'])) {
						if (in_array($upfile['type'], $uptypes)) {
							$extName = strtolower(end(explode('.', $upfile ['name'])));
							$filename = $this->random(5); // 设置随机数长度
							$extName=strtolower(end(explode('.', $upfile['name']))); // 取得扩展名
							$name1=date('YmdHis').".".$extName;
							$dir = SGUPLOADFILE;
							if (!file_exists($dir)) {
								if (mkdir($dir, 0777)) {
									$dest =$dir."/".$filename.$name1;
									if (move_uploaded_file($upfile ['tmp_name'], $dest)) {
										$response['Success'] = 1;
										$response['Message'] = '上传成功';
										$sheets = $this->readSheet2($dest); //获取工作表列表
										$response['Result'] = $this->array_iconv($sheets);
										$response['File'] = $filename.$name1;
										$response['Date'] = $params['date'];
										// $this->drkckb($dest, $params['date']);
									} else {
										$response['Success'] = 0;
										$response['Message'] = '上传失败，目录权限不足';
									}
								} else {
									$response['Success'] = 0;
									$response['Message'] = '目录不存在，上传失败';
								}
							} else {
								$dest = $dir."/".$filename.$name1;
								if (move_uploaded_file($upfile ['tmp_name'], $dest)) {
									$response['Success'] = 1;
									$response['Message'] = '上传成功';
									$sheets = $this->readSheet2($dest); //获取工作表列表
									$response['Result'] = $this->array_iconv($sheets);
									$response['File'] = $filename.$name1;
									$response['Date'] = $params['date'];
									// $this->drkckb($dest, $params['date']);
								} else {
									$response['Success'] = 0;
									$response['Message'] = '上传失败，目录权限不足';
								}
							}
						} else {
							$response['Success'] = 0;
							$response['Message'] = '上传失败，检查文件是否是'.$filetype;
						}
					} else {
						$response['Success'] = 0;
						$response['Message'] = '上传失败';
						clearstatcache(); //清除文件缓存信息
					}
				}
				else
				{
					$response['Success'] = 0;
					$response['Message'] = '上传失败';
				}
			}
		}
		echo $this->pri_JSON($response);
		exit;
	}

	function readSheet2($file)
	{
		require_once "../PHPExcel/PHPExcel.php";
		$type = pathinfo($file);
		$type = strtolower($type["extension"]);
		if ($type=='xlsx') { 
			$type='Excel2007'; 
		}elseif($type=='xls') { 
			$type = 'Excel5';
		}
		
		$objReader = PHPExcel_IOFactory::createReader($type);//判断使用哪种格式
		$objReader ->setReadDataOnly(true); //只读取数据,会智能忽略所有空白行,这点很重要！！！
		$objPHPExcel = $objReader->load($file); //加载Excel文件
		// $sheetCount = $objPHPExcel->getSheetCount();//获取sheet工作表总个数
		$sheets = $objPHPExcel->getSheetNames();
		return $sheets;
	}


	private function pri_JSON($array)
    {
        $this->pri_arrayRecursive($array, 'urlencode', true);
        $json = json_encode($array);
        return iconv("GB2312", "UTF-8", urldecode($json));
    }

    private function pri_arrayRecursive(&$array, $function, $apply_to_keys_also = false)
    {
        static $recursive_counter = 0;
        if (++$recursive_counter > 1000) {
            die('possible deep recursion attack');
        }
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                $this->pri_arrayRecursive($array[$key], $function, $apply_to_keys_also);
            } else {
                $array[$key] = $function($value);
            }
            if ($apply_to_keys_also && is_string($key)) {
                $new_key = $function($key);
                if ($new_key != $key) {
                    $array[$new_key] = $array[$key];
                    unset($array[$key]);
                }
            }
        }
        $recursive_counter--;
	}
	 //去除所有空格、换行等
	 public function trimAll($str)
	 {
		 $oldchar=array(" ","　","\t","\n","\r");
		 $newchar=array("","","","","");
		 return str_replace($oldchar,$newchar,"$str");
	 }
	//数组转码
    public function array_iconv($str, $in_charset="utf-8", $out_charset="gb2312")
    {
        if (is_array($str)) {
            foreach ($str as $k => $v) {
                $str[$k] = $this->array_iconv($v);
            }
            return $str;
        } else {
            if (is_string($str)) {
                return mb_convert_encoding($str, $out_charset, $in_charset);
            } else {
                return $str;
            }
        }
    }
}

?>