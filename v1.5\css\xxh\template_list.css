﻿@charset "utf-8";
/* 此@media查询样式单位是以效果图宽360px为例定义的大小 */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button{
    -webkit-appearance: none !important;
}

html { font-size : 22.5px; width:100%; overflow-x:hidden;}
@media only screen and (min-width: 320px){
    html { font-size: 20px !important;}
}
@media only screen and (min-width: 360px){
    html { font-size: 22.5px !important;}
}
@media only screen and (min-width: 375px){
    html { font-size: 23.4375px !important;}
}
@media only screen and (min-width: 400px){
    html { font-size: 25px !important;}
}
@media only screen and (min-width: 414px){
    html { font-size: 25.875px !important;}
}
@media only screen and (min-width: 428px){
    html { font-size: 26.75px !important;}
}
@media only screen and (min-width: 480px){
    html { font-size: 30px !important;}
}
@media only screen and (min-width: 569px){
    html { font-size: 35px !important;}
}
@media only screen and (min-width: 640px){
    html { font-size: 40px !important;}
}
@media only screen and (min-width: 768px){
    html { font-size: 45px !important;}
}

input,select{ outline: none;}
input[type="text"],input[type="url"],input[type="submit"],input[type="button"],input[type="email"],input[type="date"],input[type="time"],input[type="tel"],input[type="password"]{ background-color:transparent; -webkit-appearance:none;}
input::-webkit-calendar-picker-indicator{ opacity:0;}/* 下拉小箭头 */
select{ background-color:transparent; -webkit-appearance:none;}
textarea{ -webkit-appearance:none;}

body{ font-family:"Microsoft Yahei"; color:#333333; font-size:12px; background:#ffffff;}

*{ margin:0;padding:0;outline:none}
*:not(input,textarea){-webkit-touch-callout:inherit;-webkit-user-select:auto;}

a{ color:#333333; text-decoration:none; -webkit-tap-highlight-color:transparent; cursor: pointer;}
a:hover{ text-decoration:none}
button,input,select,textarea{ font-size:100%; margin:0;padding:0;outline:0; border:none;}
table td,table th{ line-height: 16px; padding:10px 5px; text-align:center;}
table{ border-collapse: collapse;}
textarea,input{ resize:none; outline:0}
textarea{ resize:none; -webkit-appearance:none}
ul li{ list-style:none}
/* img{ display:block;} */

/* position */

.position{ width: 100%; height: 36px; line-height: 36px; text-indent: 30px; background:#ffffff; box-shadow: 0 3px 3px rgba(0,0,0,0.1);  position: relative;}
.position:before{content: ''; display: block; position: absolute; left: 10px; top: 11px; width: 14px; height: 14px; background: #2076B7; border-radius: 7px;}
.position:after{ content: ''; display: block; position:absolute; left:13px; top: 15px; width:4px; height: 4px; border-right: 2px solid #ffffff; border-top:2px solid #ffffff;  transform: rotate(45deg);}

/* function */

.function{ height: 26px; margin: 10px; }
.function .left{ float: left; }
.function .right{ float: right; }
.function a{ float: left; background: #2076B7; border:1px solid #296192; color: #ffffff; display: inline-block; padding: 4px 20px;}
.function a:hover{ background: #1C679F; box-shadow: 0 2px 2px rgba(0,0,0,0.1);}
.function a:first-child{ margin-right:8px; }
.function select{ width: 120px; height: 24px; text-indent: 5px;}
.function input{ float: left; width: 200px; height: 24px; border:1px solid #B1BCD1; text-indent: 5px;}
.function .selectborder{ float: left; position: relative; width: 120px; height: 24px; border:1px solid #B1BCD1; border-right:0;}
.function .selectborder span{ display: block; position: absolute; right: 8px; top: 8px; width: 4px; height: 4px; border-bottom:1px solid #76A3CB; border-right:1px solid #76A3CB;transform: rotate(45deg);}
.function .inputborder{ float: left; position: relative; width: 120px; height: 24px; border:1px solid #B1BCD1; border-right:0;}
.function .inputborder input{ width: 120px; border:0; position: relative; z-index: 0; text-indent: 0;}
.function .inputborder span{ position: absolute; top:4px; right: 5px; z-index: -1;}

/* weekdate */

.weekdate{ margin:0 10px 10px 10px; }
.weekdate table{ width: 100%; }
.weekdate table th,.weekdate table td{ border:1px solid #D7E4F0; }
.weekdate table th{ background:#2076B7; color: #ffffff; }
.weekdate table tbody tr:nth-child(2n) td{ background: #F1F5FA; }
.weekdate_table tbody tr:hover td{ background: #E6EDF7; }
.weekdate_nobg div{ float: left; min-width:100px; text-align: left; margin: 4px 0; margin-right: 10px;}
.weekdate_nobg table a{ display: block; width: 80%; background: #3AB5E9; border:1px solid #1DAAE0; color: #ffffff; height: 24px; line-height: 22px; margin: 4px auto;}
.weekdate_nobg table td{ padding: 5px; }
.weekdate_nobg div.checked{ margin:8px 0;}
.weekdate_nobg div.namebar{ margin: 0; }
.weekdate table.price td{ border:0; }

td.w40{ width: 40px; }
td.w60{ width: 60px; }
td.w80{ width: 80px; }
td.w100{ width: 100px; }
td.w120{ width: 120px; }
td.w130{ width: 130px; }
td.w180{ width: 180px; }
td.w240{ width: 240px; }

td.title{ text-align: left; }
td.title a:hover{ color: #2F71A9; font-weight: bold; }
td.operate a{ color: #2F71A9; padding:0 6px;}
td.operate a svg{ position: relative; top: 2px;  right: 3px;}
td.operate a:hover{ color: #0099ff; }

/* page */

.page{ width: 100%; height: 30px; margin-top: 8px;}
.page span{ float: left; text-indent: 10px; }
.page a{ display: inline-block; margin-left:-5px; padding: 4px 10px; text-align:center; border:1px solid #E4EAEC; }
.page a:nth-child(3),.page a:last-child{ background: #2076B7; color: #ffffff;  margin-left: 5px;}
.page strong{ border:1px solid #ff9900; color:#ff9900; padding: 4px 10px; margin-right: 1px; }
.page input{ border:1px solid #E4EAEC; height: 24px; }

/* checkbox */

.gcs-checkbox { display: none;}
.gcs-checkbox+label { background-color: white; border-radius: 0px; border: 1px solid #c3c3c3; width: 12px;height: 12px; display: inline-block; text-align: center; vertical-align: bottom; line-height: 12px;}
.gcs-checkbox+label:hover { cursor: pointer; border: 1px solid #2783FB;}
.gcs-checkbox:checked+label { background-color: #eee; background: #2783FB;}
.gcs-checkbox:checked+label:after { content: "\2714"; color: white;}

/* checked */
.checked{ border:1px solid #CCDDEC; width: 100%;}
.namebar{ position: relative; width: 100%; height: 30px; margin:0; overflow: hidden;}
.namebar span{ position: absolute; z-index: 0; left: 50%; margin-left: -60px; display: block; width:120px; height: 30px; line-height: 30px; background: #F1F5FA; border-radius: 5px;transform: perspective(10px) rotateX(-5deg) rotateY(0deg) translateZ(0); border:1px solid #CCDDEC; border-top: 0;}
.namebar p{ position: absolute; z-index: 1; color: #2076B7; text-align: center; left: 50%; margin-left: -60px; width:120px; text-align: center; line-height: 30px;}

.checked .option{ padding: 0 10px; }
.checked .option div{ padding:0 0 0 5px; position: relative; min-width: 60px; height: 24px; line-height: 24px; background: #F9F9F9; border:1px solid #cccccc;}
.checked .option div span{ cursor: pointer; padding: 5px 10px; }
.checked .option div span:before{ content: ''; display: inline-block; position: absolute; right: 7px; top: 10px; width: 12px; height: 12px; border-top:1px solid #333333; transform: rotate(45deg); }
.checked .option div span:after{ content: ''; display: inline-block; position: absolute; right: 7px; top: 2px; width: 12px; height: 12px; border-right:1px solid #333333; transform: rotate(45deg); }

/* my_moban */

.my_moban{ border:1px solid #CCDDEC; width: 100%;}

.my_moban .option{ padding: 0 10px; }
.my_moban .option li{ float: left; margin-right: 10px; line-height: 26px;}

/* foot_button */

.foot_button{ width: 100%; }
.foot_button a{ float: right;  display: block; background: #2076B7; border: 1px solid #296192; color: #ffffff; margin: 0 5px; padding: 4px 20px;}
.foot_button a:first-child{ float: left; margin: 0; }
.foot_button a:nth-child(2){ margin:0 0 0 5px; }
.foot_button span{ float: left; width: 60px; height:24px; line-height: 24px; border: 1px solid #cccccc; }
.foot_button span label{ position: relative; top: -5px; left: 5px; margin-right: 10px; }

/* moban_price */

.moban_price{ margin-top:10px; }
.moban_price input{ border: 1px solid #B1BCD1; line-height: 24px; background: #ffffff;}
.moban_price svg{ position: relative; top: 2px; }
td.w60 input{ width: 80%; margin: 0 auto; }
table.price{ width: 100%;}
table.price td{ text-align: left;}
table.price td span{ display: inline-block; width: 60px; }
table.price input{ width: 45%; }
table.price textarea{ border: 1px solid #B1BCD1; width: 90.2%; height: 80px; line-height: 24px; }
table.price tr:nth-child(5) td span{ position:relative; top: -38px; }
table.price tr:nth-child(6) td span{ position:relative; top: -30px; }
table.price tr:last-child td:first-child input{ width: 82.5%; }
table.price tbody tr:nth-child(2n) td{ background: none; }

table.price2{ width: 100%; }
table.price2 td{ text-align: left; border: 0;}
table.price2 td span{ display: inline-block; width: 80px; }
table.price2 input{ width: 74%; }
table.price2 tr:nth-child(2) td input{ width: 87.2%; }
table.price2 tbody tr:nth-child(2n) td{ background: none; }
table.price2 tr:nth-child(3) td span{ position:relative; top: -35px; }
table.price2 textarea{ border: 1px solid #B1BCD1; width: 87.2%; height: 80px; line-height: 24px; }
td.caozuo a{ background: #ffffff; border: 1px solid #CCDDEC; color:#2076B7; }

/* head */
/* 1648*1165==>841*595==>1000*707 */

.A4_width{ position: relative; z-index: 0; width:1000px;height: 707px; background: #ffffff; margin:0 auto;}

.head{ width: 100%; height: 45px; margin-bottom: 10px;}
.head td.logo{ width: 130px; }
.head td.logo img{ width: 100%; }
.head td.ewm{ width: 60px; text-align: center; }
.head td.ewm img{ width: 100%; margin:0 auto; }
.head td.ewm span{ display: block; font-size: 12px;}
div.title{ font-size: 16px; text-align: center; width: 100%; position: relative; top:-55px;}

.line{ width: 100%; height: 2px; background: #2076B7; }

h1{ font-size: 72px; text-align: center; color: #2076B7; line-height:100px; margin: 80px 0 50px;}
h2{ font-size: 42px; font-weight: normal; color: #666666; text-align: center; }
h3{ font-size: 45px; text-align: center; color: #666666;  margin-top: -20px;}
h5{ font-size: 18px; text-align: center; color: #666666;  }

.date{  position: relative;  z-index: 1;background: #2076B7; text-align:center; width: 206px; height:34px; margin: 130px auto 0; border-radius: 15px 0;  border: 5px solid #ffffff; color: #ffffff; font-size: 20px;}
.dateline{  position: relative; z-index: 0; margin-top:-17px; width: 100%; height:2px; background: #efefef;  }

.contact{ text-align: center; font-size: 20px; line-height: 26px; margin-top: 60px; }
.contact span{ font-family: "Arial"; font-size: 22px;  position: relative; top: 1px;}

.mulu_list{ margin:30px 0 15px 20px; font-size: 16px;}
.mulu_list li{ position: relative; margin-bottom: 36px; line-height: 12px; }
.mulu_list li{ border-bottom: 2px dotted #666666; width: 100%; }
.mulu_list span{ position: relative; z-index: 1; background: #ffffff; padding: 0px 10px 2px;}
.mulu_list span:first-child{ float: left; }
.mulu_list span:last-child{ float: right; }

/* function_yulan */

.function_yulan{ width: 100%; background: #ffffff; height: 48px; box-shadow: 0 5px 10px rgba(0,0,0,0.1); position: relative; z-index: 99;}
.function_yulan .a4{ width: 1000px; margin: 0 auto;}
.function_yulan .a4 h4{ float: left; font-size: 16px; line-height: 48px; }
.function_yulan .a4 .right{ float: right; font-size: 12px; margin-top: 10px; }
.function_yulan .a4 .right input{ border: 1px solid #A4BEDF; height: 24px; line-height: 24px;}
.function_yulan .a4 .right div,.function_yulan .a4 .right a{ float: left; }
.function_yulan .a4 .right div{ position: relative; }
.function_yulan .a4 .right div input{ position: relative; z-index: 1; margin:0 5px;}
.function_yulan .a4 .right div span{ position: absolute; top: 5px; right: 10px; z-index: 0; }
.function_yulan .a4 .right a{ background: #2076B7;  display: inline-block; padding: 0 26px; line-height: 24px; margin: 0 3px; color: #ffffff;}
.function_yulan .a4 .right .submit { background: #2076B7;  display: inline-block; padding: 0 26px; line-height: 24px; margin: 0 3px; color: #ffffff;}

/* data */

span.red{ color: #ff0000; }
span.green{  color: #009900; }
table.data{ width: 100%; margin-top:-20px; height: 60px; }
table.data th,table.data td{ width: 8.3%; line-height: 24px;  border:1px solid #D0DEEE; font-size: 14px; font-weight:normal; padding: 3px 2px;}
table.data th{ background: #2076B7; color: #ffffff; }
table.data td{ line-height: 24px; height:24px;}
table.data tr.bgcolor td{ background: #F1F5FA; }



/* xiangbin add 20200602 start*/
table.data0{ width: 100%; margin-top:-20px; height: 60px; }
table.data0 th,table.data0 td{ width: 7.4%; line-height: 24px;  border:1px solid #D0DEEE; font-size: 14px; font-weight:normal; padding: 3px 2px;}
/*table.data th,table.data td{ width: 8.3%; line-height: 24px;  border:1px solid #D0DEEE; font-size: 14px; font-weight:normal; padding: 3px 2px;}*/
table.data0 th{ background: #2076B7; color: #ffffff; }
table.data0 td{ line-height: 24px; height:24px;}
table.data0 tr.bgcolor td{ background: #F1F5FA; }

table.data1{ width: 100%; margin-top:-20px; height: 60px; }
table.data1 th,table.data1 td{width: 7.4%;line-height: 24px;border:1px solid #D0DEEE;font-size: 14px;font-weight:normal; padding: 2px 1px; }
/*table.data th,table.data td{ width: 8.3%; line-height: 24px;  border:1px solid #D0DEEE; font-size: 14px; font-weight:normal; padding: 3px 2px;}*/
table.data1 th{ background: #2076B7; color: #ffffff; }
table.data1 td{line-height: 24px;height:24px;}
table.data1 tr.bgcolor td{ background: #F1F5FA; }

table.data2{ width: 100%; margin-top:-20px; height: 60px; }
table.data2 th,table.data2 td{width: 7.4%;line-height: 24px;border:1px solid #D0DEEE;font-size: 14px;font-weight:normal; padding: 1px 0px; }
/*table.data th,table.data td{ width: 8.3%; line-height: 24px;  border:1px solid #D0DEEE; font-size: 14px; font-weight:normal; padding: 3px 2px;}*/
table.data2 th{ background: #2076B7; color: #ffffff; }
table.data2 td{line-height: 24px;height:24px;}
table.data2 tr.bgcolor td{ background: #F1F5FA; }

/* xiangbin add 20200602 end*/




table.data_intertional{ width: 100%; margin-top:-20px; height: 60px; }
table.data_intertional th,table.data_intertional td{ line-height: 24px;  border:1px solid #D0DEEE; font-size: 14px; font-weight:normal; padding: 3px 2px;}
table.data_intertional th{ background: #2076B7; color: #ffffff; }
table.data_intertional td{ line-height: 24px; height:24px;}
table.data_intertional tr.bgcolor td{ background: #F1F5FA; }

.data_photo{ width: 100%; margin-top: 20px; }
.data_photo img{ display:inline;}
.data_photo iframe{ width: 50%; float: left;}

/* pdf */
.mulu_name2{ position:relative; width: 100%;  height:10px;}
.mulu_name2 h5{ position:relative; margin-top:-70px; width: 100%; text-align: center;}

/* PC */
.mulu_name{ position:relative; width: 100%;  height:80px;}
.mulu_name h5{ position:relative; margin-top:-70px; width: 100%; text-align: center;}


@media screen and (max-width:768px){}
@media screen and (min-width:769px){}

/*xiangbin add 20200602 start */


.zoneName {
    text-align: center;
    font-size: 16px;
    padding: 0;
    border-bottom: 1px solid rgba(147,147,147,.3);
    margin: -15px 0 0;
    transition: all 300ms;
    height: 15px;
    position: relative;
    left: 0;
    right: 0;
    z-index: 9;
    margin: 0 auto;
    width: 1000px;
}

.zoneName:hover {
    border-bottom-color: #939393
}

.zoneName a.add-widget {
    text-decoration: none
}

.zoneName .circle-btn {
    display: inline-block;
    background: #5e5e5e;
    border-radius: 17px;
    transition: all 300ms;
    border: 1px solid #fff
}

.zoneName .circle-btn:hover {
    background: #000
}

.zoneName .circle-btn a {
    display: inline-block;
    padding: 5px 10px;
    cursor: pointer;
    color: #fff;
    font-size: 13px;
    text-decoration: none
}

.zoneName a.label {
    margin-right: 10px
}

.widget-templates-background {
    display: none;
    position: fixed;
    background: rgba(60,60,60,.1);
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    z-index: 100
}

.widget-templatesnew {
    display: none;
    width: 480px;
    margin: 35px auto 15px auto;
    position: absolute;
    z-index: 100;
    left: 50%;
    transform: translateX(-50%)
}

.widget-templatesnew .modal-header {
    padding: 12px 15px
}

.widget-templatesnew .modal-body {
    padding: 5px
}

.widget-templatesnew{ text-align: center;}
.widget-templatesnew .title { color:#2076b7; font-size: 14px; font-weight: bold;
}

.widget-templates textarea{
    padding:5px; line-height: 24px; width:450px; height:100px; border:1px solid #cccccc;
}
button{
	padding: 0;	
	background: transparent;
	border: 0;
	-webkit-appearance: none;
	appearance: none;
  }
.zk-modal-body{ overflow: hidden; padding-bottom: 10px;}
.zk-modal-body .bt{ width:200px; margin:0 auto;}
.zk-modal-body button{ width:60px; height:28px; border-radius:5px; background:#2076B7; color:#ffffff; float: left; margin:5px 20px; cursor: pointer;
    font-size: 14px;
    font-weight: bold;}

    .zk-modal-body button:focus, .zk-modal-body button:hover {
        color: #ffffff;
        text-decoration: none;
        background:#155c92;
    }

.zk-modal-content {
    position: relative;
    background-color: #fff;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    border: 1px solid #999;
    border: 1px solid rgba(0,0,0,.2);
    border-radius: 6px;
    -webkit-box-shadow: 0 3px 9px rgba(0,0,0,.5);
    box-shadow: 0 3px 9px rgba(0,0,0,.5);
    outline: 0
}
.zk-modal-header {
    padding: 15px;
}

.zk-modal-header .close .save{
    margin-top: -2px
}



.zk-modal-title {
    margin: 0;
    line-height: 1.42857143
}

  

/*xiangbin add 20200602 end */
.an{
    -ms-transform:rotate(90deg); /* IE 9 */
    -webkit-transform:rotate(90deg); /* Safari and Chrome */
    transform:rotate(90deg);
}
.copysel{
    border: 1px solid #B1BCD1;
    -webkit-appearance: push-button;
}