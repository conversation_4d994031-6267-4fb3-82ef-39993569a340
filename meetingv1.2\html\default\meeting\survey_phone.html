﻿<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">


<title>钢之家会议数据大屏</title>
<link rel="stylesheet" href="css/meeting/index.css">
<link rel="stylesheet" href="css/meeting/public.css">
<script src="js/meeting/layui/layui.all.js"></script>
<link rel="stylesheet" href="js/meeting/layui/css/layui.css">

<style>
.header h1 {
   
     width:100%;
     max-width: 1380px;
     background-repeat: no-repeat;
background-position: center;
background-size: cover;
height: 88px;
justify-content: center;
align-items: center;
}
.header h2 {
   width:100%;
   max-width: 1380px;
   background-repeat: no-repeat;
background-position: center;
background-size: cover;
height: 88px;
justify-content: center;
align-items: center;
margin: 0px auto;
}
.nav ul{
    display: flex;
  justify-content: center;
  list-style-type: none; /* 移除列表的标记，如果需要的话 */
  padding: 0; 
}
.nav li.active{height: 60px;line-height: 52px;}
.jq22-content span{color: #00FFFF;}
.jq22-content{width: 96%;margin: 0px auto;margin-top: 20px;}
.jq33-content span{color: #00FFFF;}
.jq33-content{width: 96%;margin: 0px auto;margin-top: 20px;}
.progressbar{margin-top: 6px;width: 80%;float: left;}
.proggress{height: 25px;}
.span{display: block;margin-top: 3px;}
.right2{float: right;height: 40px;width: calc(100% - 240px);}
.header:before{
        background: none;
    }
    .header:after{
        background: none;
    }
    .progressbar{width: 50%;height: 30px;}
    .jq22-content .left{width: 98%;float: left;font-size: 32px;}
    .jq22-content .right{float: right;height: 40px;width: 98%;font-size: 32px;margin-bottom: 2rem;}
    .header h1{background: none;}

    .center .center-left .left-top{height: 45%;}
    .center .center-left .left-cen{height:54%}
    .center .center-right{width: 32%;}
    .center .center-left{width: 32.5%;}

    .center .center-right .right-top{height: 45%;}
    .center .center-right .right-cen{height:54%}
    .title{font-size: 36px;text-align: left;width: 96%;margin: 0px auto;margin-bottom: 40px;}
    #chart6title{margin-bottom: 45px;}
    .center{top:125px;}
    .header h1 span.font1{padding:  10px 0px 0px;}
    .header{height: 160px;}
    .header h1 span.font1{font-size: 34px;}
    .nav{height: 60px;background-size: 100% 100px;}
    .nav li{width: 33%;height: 60px; line-height: 60px;font-size: 30px;}
    .header h1 span.font2{font-size: 28px;}
    .jq33-content .left{width: 98%;float: left;height: 45px;font-size: 32px;}
    .header h2 span{padding:  10px 0px 0px;font-size: 30px;font-weight: bold;}
    .header h2{height: 45px;color: #01C4F7;text-align: center;}
    .datetitle{font-size: 24px;text-align: center;padding-top: 10px;}
</style>
</head>
<body>

<header>
<div class="header">
	<h1><span class="font1" id="maintitle"><img src="images/gzj_logo.png"><font id="title"><{$meetname.MeetingName}></font></span></h1>
    <{$title2}>
    <{$datetitle}>
</div>
</header>

<section>

    <div class="center" id="mokuai2" style="display:none;margin-top: 3rem;">
        <div class="center-left fl" style="width: 98%;" >
            <div class="left-top rightTop"  style="height: 50rem;width: 95%;margin: 0px auto;" id="div8">
                <div class="title" id="chart8title"></div>
                 <div class="bottom-b">
                    <div  id="chart8" class="allnav"></div>
                    <div  id="chart8Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                    <div class="panel-footer"></div>
                </div>                    
            </div>
        </div>

        <div class="center-left fl" style="width: 98%;margin-top: 3rem;">
            <div class="left-top rightTop"  style="width: 95%;margin: 0px auto;height: 60rem;" id="div9">
                <div class="title" id="chart9title"></div>
                 <div class="bottom-b" id="chart9">
                </div>                    
            </div>
        </div>

        <div class="center-left fl" style="width: 98%;margin-top: 3rem;">
            <div class="left-top rightTop"  style="width: 95%;margin: 0px auto;height: 60rem;" id="div10">
                <div class="title" id="chart10title"></div>
                 <div class="bottom-b" id="chart10">
                </div>                    
            </div>
        </div>

        <div class="center-left fl" style="width: 98%;margin-top: 3rem;">
            <div class="left-top rightTop"  style="width: 95%;margin: 0px auto;height: 50rem;" id="div100">
                <div class="title" id="chart100title"></div>
                 <div class="bottom-b" id="chart100">
                </div>                    
            </div>
        </div>

        <div  class="center-left fl" style="height: 8rem;width: 98%;"></div>
    </div>

    <div class="center" id="mokuai1" style="display: flex; flex-direction: column;margin-top: 3rem;">
            <div class="center-left fl" style="width: 98%;">
                <div class="left-top rightTop" style="<{if $params.mtid==391}>height: 60rem; <{else}> height: 50rem;<{/if}> width: 95%;margin: 0px auto;">
                    <div class="title" id="chart1title"></div>
					 <div class="bottom-b">
                        <div  id="chart1" class="allnav"></div>
						<div  id="chart1Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                        <div class="panel-footer"></div>
                    </div>                    
                </div>
            </div>

            <div class="center-left fl"  style="width: 98%;margin-top: 3rem;">
                <div class="left-top rightTop"  style="height:65rem;padding-top: 10px;width: 95%;margin: 0px auto;">
                    <div class="title" id="chart6title"></div>
					 <div class="bottom-b" id="chart6">
						<div  id="chart6Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                        <div class="panel-footer"></div>
                    </div>                    
                </div>
            </div>


			 
			<div class="center-right fr"  style="width: 98%;margin-top: 4rem;">
				<div class="right-top rightTop" style="height: 50rem;width: 95%;margin: 0px auto;">
					<div class="title" id="chart3title"></div>
					 <div class="right-top-top">
						<div id="chart3" class="allnav"></div>
						<div  id="chart3Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                        <div class="panel-footer"></div>
                    </div>
				</div>
				
                <div class="right-top rightTop" style="height: 55rem;width: 95%;margin: 0px auto;">
                    <div class="title" id="chart2title"></div>
                     <div class="bottom-b">
                        <div  id="chart2" class="allnav"></div>
                        <div  id="chart2Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                        <div class="panel-footer"></div>
                    </div>
                </div>

                <div class="right-cen "   style="height: 65rem;width: 95%;margin: 0px auto;"  >
					<div class="title" id="chart4title"></div>
					<div class="right-cen-cent">
						<div id="chart4" class="allnav"></div>
						<div  id="chart4Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                        <div class="panel-footer"></div>
                    </div>
				</div>
			</div>

            <div class="center-left fl div21"  style="width: 98%;margin-top: 3rem;" >
                <div class="left-top rightTop"  style="height:55rem;padding-top: 10px;width: 95%;margin: 0px auto;">
                    <div class="title" id="chart21title"></div>
					 <div class="bottom-b" id="chart21">
						<div  id="chart21Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                        <div class="panel-footer"></div>
                    </div>                    
                </div>
            </div>

            

            <div class="center-left fl div22"  style="width: 98%;margin-top: 3rem;">
                <div class="left-top rightTop"  style="height:55rem;padding-top: 10px;width: 95%;margin: 0px auto;">
                    <div class="title" id="chart23title"></div>
					 <div class="bottom-b" id="chart23">
						<div  id="chart23Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                        <div class="panel-footer"></div>
                    </div>                    
                </div>
            </div>

            <div class="center-left fl div23"  style="width: 98%;margin-top: 3rem;">
                <div class="left-top rightTop"  style="height:55rem;padding-top: 10px;width: 95%;margin: 0px auto;">
                    <div class="title" id="chart22title"></div>
					 <div class="bottom-b" id="chart22">
						<div  id="chart22Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                        <div class="panel-footer"></div>
                    </div>                    
                </div>
            </div>

            <div class="center-left fl div24"  style="width: 98%;margin-top: 3rem;">
                <div class="left-top rightTop"  style="height:55rem;padding-top: 10px;width: 95%;margin: 0px auto;">
                    <div class="title" id="chart24title"></div>
					 <div class="bottom-b" id="chart24">
						<div  id="chart24Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                        <div class="panel-footer"></div>
                    </div>                    
                </div>
            </div>

            <div class="center-left fl div25"  style="width: 98%;margin-top: 3rem;">
                <div class="left-top rightTop"  style="height:55rem;padding-top: 10px;width: 95%;margin: 0px auto;">
                    <div class="title" id="chart25title"></div>
					 <div class="bottom-b" id="chart25">
						<div  id="chart25Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                        <div class="panel-footer"></div>
                    </div>                    
                </div>
            </div>

            <div class="center-left fl div26"  style="width: 98%;margin-top: 3rem;">
                <div class="left-top rightTop"  style="height:55rem;padding-top: 10px;width: 95%;margin: 0px auto;">
                    <div class="title" id="chart26title"></div>
					 <div class="bottom-b" id="chart26">
						<div  id="chart26Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                        <div class="panel-footer"></div>
                    </div>                    
                </div>
            </div>

            <div class="center-left fl div27"  style="width: 98%;margin-top: 3rem;">
                <div class="left-top rightTop"  style="height:55rem;padding-top: 10px;width: 95%;margin: 0px auto;">
                    <div class="title" id="chart27title"></div>
					 <div class="bottom-b" id="chart27">
						<div  id="chart27Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                        <div class="panel-footer"></div>
                    </div>                    
                </div>
            </div>

            <div  class="center-left fl" style="width: 98%;"><div style="height: 8rem;width: 98%;"></div></div>

        </div>
    </section>



    <div class="center" id="mokuai3" style="display: flex; flex-direction: column;margin-top: 3rem;display: none;">
        <div class="center-left fl" style="width: 98%;">
            <div class="left-top rightTop div11" style="<{if $params.mtid==391}>height: 60rem; <{else}> height: 50rem;<{/if}> width: 95%;margin: 0px auto;">
                <div class="title" id="chart11title"></div>
                 <div class="bottom-b">
                    <div  id="chart11" class="allnav"></div>
                    <div  id="chart11Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                    <div class="panel-footer"></div>
                </div>                    
            </div>
        </div>

        <div class="center-left fl"  style="width: 98%;margin-top: 4rem;">
            <div class="left-top rightTop div12"  style="height:50rem;padding-top: 10px;width: 95%;margin: 0px auto;">
                <div class="title" id="chart12title"></div>
                 <div class="bottom-b" id="chart12">
                    <div  id="chart12Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                    <div class="panel-footer"></div>
                </div>                    
            </div>
        </div>
         
        <div class="center-right fr"  style="width: 98%;margin-top: 4rem;">
            <div class="right-top rightTop div13" style="height: 50rem;width: 95%;margin: 0px auto;margin-bottom: 4rem;">
                <div class="title" id="chart13title"></div>
                 <div class="right-top-top">
                    <div id="chart13" class="allnav"></div>
                    <div  id="chart13Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                    <div class="panel-footer"></div>
                </div>
            </div>
            
            <div class="right-top rightTop div14" style="height: 55rem;width: 95%;margin: 0px auto;margin-bottom: 4rem;">
                <div class="title" id="chart14title"></div>
                 <div class="bottom-b">
                    <div  id="chart14" class="allnav"></div>
                    <div  id="chart14Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                    <div class="panel-footer"></div>
                </div>
            </div>

            <div class="right-cen  div15"   style="height: 65rem;width: 95%;margin: 0px auto;margin-bottom: 4rem;"  >
                <div class="title" id="chart15title"></div>
                <div class="right-cen-cent">
                    <div id="chart15" class="allnav"></div>
                    <div  id="chart15Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                    <div class="panel-footer"></div>
                </div>
            </div>
        </div>

        <div class="center-right fr"  style="width: 98%;margin-bottom: 8rem;">
            <div class="right-top rightTop div16" style="height: 50rem;width: 95%;margin: 0px auto;margin-bottom: 4rem;">
                <div class="title" id="chart16title"></div>
                 <div class="right-top-top">
                    <div id="chart16" class="allnav"></div>
                    <div  id="chart16Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                    <div class="panel-footer"></div>
                </div>
            </div>
            
            <div class="right-top rightTop div17" style="height: 55rem;width: 95%;margin: 0px auto;margin-bottom: 4rem;">
                <div class="title" id="chart17title"></div>
                 <div class="bottom-b">
                    <div  id="chart17" class="allnav"></div>
                    <div  id="chart17Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                    <div class="panel-footer"></div>
                </div>
            </div>

            <div class="right-cen  div18"   style="height: 65rem;width: 95%;margin: 0px auto;margin-bottom: 4rem;"  >
                <div class="title" id="chart18title"></div>
                <div class="right-cen-cent">
                    <div id="chart18" class="allnav"></div>
                    <div  id="chart18Loading" class="el-loading-mask" style="/* display: none; */"><div class="el-loading-spinner"><svg viewBox="25 25 50 50" class="circular"><circle cx="50" cy="50" r="20" fill="none" class="path"></circle></svg></div></div>
                    <div class="panel-footer"></div>
                </div>
            </div>
            <div style="width: 98%;height: 4rem;"></div>
        </div>
        
    </div>
</section>

    <div class="nav"> 
        <ul style="width: 70%;margin: 0px auto;"> 
            <{foreach from=$surveylist key=key item=v}>
            <li id="<{$key}>"><{if $key==1}>会前调查<{elseif $key==2}>会中调查<{elseif $key==3}>会后调查<{/if}></li> 
            
            <{/foreach}>
        </ul> 
    </div>

<script>
    var istry='<{$istry}>';
    var mtid='<{$params.mtid}>';
    var id='<{$params.id}>';
    var isphone=1;
</script>
<script src="js/meeting/jquery.min.js"></script>
<script src="js/meeting/axios.min.js"></script>
<script src="js/meeting/echarts.min.js"></script>
<script src="js/meeting/surveyecharts.js?v=2025"></script>
<script src="js/meeting/fontscroll.js"></script>
<script src="js/meeting/util.js"></script>

<script src="js/meeting/jquery.lineProgressbar.js"></script> 

    <script>
      
	$(function(){  
		
});

        //顶部时间
        function getTime() {
            var myDate = new Date();
            var myYear = myDate.getFullYear(); //获取完整的年份(4位,1970-????)
            var myMonth = myDate.getMonth() + 1; //获取当前月份(0-11,0代表1月)
            var myToday = myDate.getDate(); //获取当前日(1-31)
            var myDay = myDate.getDay(); //获取当前星期X(0-6,0代表星期天)
            var myHour = myDate.getHours(); //获取当前小时数(0-23)
            var myMinute = myDate.getMinutes(); //获取当前分钟数(0-59)
            var mySecond = myDate.getSeconds(); //获取当前秒数(0-59)
            var week = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
            var nowTime;

            nowTime = myYear + '-' + fillZero(myMonth) + '-' + fillZero(myToday) + '&nbsp;&nbsp;' + fillZero(myHour) + ':' + fillZero(myMinute) + ':' + fillZero(mySecond) + '&nbsp;&nbsp;' + week[myDay] + '&nbsp;&nbsp;';
            //console.log(nowTime);
            $('#time').html(nowTime+'企业管理处');
			//$('#lrcsdate').html(myYear+"年"+myMonth+"月"+myToday+"日");
        };

        function fillZero(str) {
            var realNum;
            if (str < 10) {
                realNum = '0' + str;
            } else {
                realNum = str;
            }
            return realNum;
        }
        setInterval(getTime, 1000);
    </script>
</body>
</html>