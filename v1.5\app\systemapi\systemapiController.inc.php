<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );

class systemapiController extends AbstractController{
  
    public function __construct(){
        parent::__construct();
        $this->_action->setDao(new systemDao("MAIN"));  //
    }

    public function _dopre()
    {
        // $this->_action->checkSession();
    }

    public function do_gzjSysLogin(){
        $this->_action->gzjSysLogin( $this->_request );
    }

    public function do_syncdb(){
        $this->_action->syncdb( $this->_request );
    }

    public function do_getdb(){
        $this->_action->getdb( $this->_request );
    }

}
?>