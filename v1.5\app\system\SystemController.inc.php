<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );

class SystemController extends AbstractController{
  
  public function __construct(){
    parent::__construct();
	
	$this->_action->setDao( new SystemDao( "MAIN" ) );
	//$this->_action->bizdao = new SystemDao('BIZ') ;
	$this->_action->stedao = new SystemDao('91R') ;
	$this->_action->ccdao = new SystemDao('MAIN') ;
  }

  public function _dopre(){
      //$this->_action->checkSession();
  }


  //系统初始化
	public function do_SoftInstall(){
		$this->_action->SoftInstall( $this->_request );
	}
	public function do_Login(){
		$this->_action->Login( $this->_request );
	}
	public function do_LoginOut(){
		$this->_action->LoginOut( $this->_request );
	}
	public function do_SystemVersionUpdate(){
	
		$this->_action->SystemVersionUpdate( $this->_request );
	}
	public function do_SystemVersionGet(){
		
		$this->_action->SystemVersionGet( $this->_request );
	}
	public function do_BizPublish(){
		
		$this->_action->BizPublish( $this->_request );
	}
	public function do_BizGet(){
		
		$this->_action->BizGet( $this->_request );
	}
	public function do_ShowAbout(){
	
		$this->_action->ShowAbout( $this->_request );
	}
	public function do_GetMobileCheckedCode(){
		//header( "Content-Type: text/html; charset=gbk" );
		$this->_action->GetMobileCheckedCode( $this->_request );
	}
	
	//重置客户更新字段
	public function do_SetUpdate(){
		$this->_action->SetUpdate( $this->_request );
	}
	 //hlf/add
	public function do_CheckLastVersion(){
	  $this->_action->CheckLastVersion($this->_request);
	}

	//add by zhangcun 2018/4/20 检查煤焦基本信息，更新到数据中心客户端
	public function do_checkgx(){
	  $this->_action->checkgx($this->_request);
	}


	public function v_ErrorSubmit(){
		$this->_action->ErrorSubmit($this->_request);
	}
	public function do_RepairToolUpdate(){
		$this->_action->RepairToolUpdate($this->_request);
	}

	public function do_GetUpdateLog(){
		$this->_action->GetUpdateLog($this->_request);
	}
	public function do_GetLoginAesKey(){
		$this->_action->GetLoginAesKey($this->_request);
	}

	public function do_GetSignCS(){
		$this->_action->GetSignCS($this->_request);
	}


	public function do_GetSignCSencrypt(){
		$this->_action->GetSignCSencrypt($this->_request);
	}

	public function do_GUIDLogin(){
		$this->_action->GUIDLogin( $this->_request );
	}
	public function do_weblogin(){
		$this->_action->weblogin( $this->_request );
	}
	 /**
     * 注销账号接口
     * Created by std.
     * Date:2025/2/20 14:06
     */
    public function do_loginOff()
    {
        $this->_action->loginOff($this->_request);
    }
}


?>