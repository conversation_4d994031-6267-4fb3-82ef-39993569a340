<?php 
// require_once('../../../../steelconf_v3/debug.php');
class DcWebViewEntryAction extends AbstractAction
{
    public function __construct()
    {
        parent::__construct();
    } 
    // 检查登录Session
    public function DoThisDateDb($params)
    {
        $this->checkqx($params);
        $type=$params['Type'];
        
        //hlf start 2020/7/23
        $mc_type=$params['mc_type'];
        if($mc_type==""){
            $mc_type=1;
        }
        //hlf end
        if($mc_type==1){
            $index = "index";
            switch($type){
                case "1":$urfile="dcwebview";break;		//"碳结钢日定价"
                case "2":$urfile="dcwebview3";break;	//"碳结钢旬定价"
                case "3":$urfile="lwgdayprice";break;	//"螺纹钢日定价"
                case "4":$urfile="dcwebdgrdj";break;	//"带钢日定价"
                case "5":$urfile="dgxundjview";break;	//"带钢旬定价"
                case "6":$urfile="zhbridj";break;		//"中厚板日定价"
                case "7":$urfile="lwgdayprice";break;	//"中厚板旬定价"
                default: echo"error modeltype!";exit;break;
            }
        }else if($mc_type==2){
            $index = "webview";
            switch($type){
                case "1":$urfile="sggcprice";break;		//"碳结钢日定价"
                default: echo"error modeltype!";exit;break;
            }
        }
        
        $url=DCURL."/".$urfile.".php?view=".$index;
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        //echo $url;exit;
        gourl($url);	
    }

    public function DoThisDateDbList($params)
    {
        $this->checkqx($params);
        $guid=$params['GUID'];
        $mode=$params['mode'];
        $url=DCURL."/dcpreview.php?view=index";
        if($params['mc_type']=='2'){
            $url=DCURL."/dcpreview.php?view=sg_index";
        }
        
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        //echo $url;exit;
        gourl($url);
    }

    public function doForecast_current($params)
    {
        $url=DCURL."/dcprediction.php?view=current";
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        
        gourl($url);
    }

    public function doForecast_previous($params)
    {
        $url=DCURL."/dcprediction.php?view=previous";
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        gourl($url);
    }

    public function index($params)
    {
    }
    
    private function checkqx($params){
        $GUID=$params["GUID"];
        $type=$params['Type'];
        //hlf start 2020/7/23
        $mc_type=$params['mc_type'];
        if($mc_type==""){
            $mc_type=1;
        }
        //hlf end
        $temp=$this->_dao->getRow("select * from app_session_temp where GUID='".$GUID."' and mc_type='".$mc_type."' order by LastDate desc limit 1");
        $qx=$this->_dao->getone("select privilege from app_license_privilege where uid='".$temp['Uid']."' and lid=(select id from app_license where mid='".$temp['Mid']."' and status=1 and mc_type='".$mc_type."' limit 1) and mc_type='".$mc_type."'");
        $qx=explode(",",$qx);
        //print_r("select * from app_session_temp where GUID='$GUID' and mc_type='".$mc_type."' order by LastDate desc limit 1");
        $arrayKey = (int)$type - 1;
        $qx=$qx[$arrayKey];
        if($qx!=1&&$qx!=2&&$qx!=3&&$qx!=4) {//1:决策人，2:建议人，3:市场部，4:游客
            echo"<h2 style=\"text-align:center;\">对不起，您没有权限访问该页面</h2>";exit;
        }
    }

    public function doQhtb_calculate($params){
        $type=$params['Type'];
        $mc_type=$params['mc_type'];
        $view="";
        switch($type){
            case "1": $view=in_array($mc_type,array('2','3','4'))?'xunigcnew':'xunigc';break;//2陕钢与新钢使用新的公式
            case "2": $view="gctbyl";break;
            case "3": $view="tksft";break;
        }
        $url=DCURL."/qihtb.php?view=$view";
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        gourl($url);
    }

    public function doQhtb_previous($params){
        if($params['mc_type']=="2" || $params['mc_type']=="3" || $params['mc_type']=="4"){
            $url=DCURL."/dcpreview.php?view=tblist_xg_sg";
        }else{
            $url=DCURL."/dcpreview.php?view=tblist";
        }
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        gourl($url);
    }

    public function RdjHuiZong($params){
        $url=DCURL."/ridingjiahuizong.php?view=index";
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action"&&$i!="Type")$url.="&".$i."=".$v;
        }
        gourl($url);
    }

    public function DayReport($params){
        $view=$params["Type"];
        $url=DCURL."/dayreport.php?view=".$view;
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action"&&$i!="Type")$url.="&".$i."=".$v;
        }
        //echo$url;exit;
        gourl($url);
    }

    public function settscbindex($params){
        //print"<pre>";print_r($params);
        $type=$params["Type"];
        $url=DCURL."/tieshuicb.php?view=index&Type=".$type;
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action"&&$i!="Type")$url.="&".$i."=".$v;
        }
        gourl($url);
    }

    public function setcbindex($params){
        //print"<pre>";print_r($params);
        $type=$params["Type"];
        $url=DCURL."/cbmodel.php?view=index&Type=".$type;
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action"&&$i!="Type")$url.="&".$i."=".$v;
        }
        gourl($url);
    }

    public function channenghz($params){
        $url=DCURL."/channenghz.php?view=index";
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        gourl($url);
    }

    public function dcwebviewzhbb($params){
        $url=DCURL."/dcwebviewzhbb.php?view=zhbb";
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        //print_r($url);exit;
        gourl($url);
    }

    public function zhbblist($params){
        $url=DCURL."/dcwebviewzhbb.php?view=list";
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        //print_r($url);exit;
        gourl($url);
    }
    
    public function dbmarketprice($params){
        $GUID=$params["GUID"];
        //$temp=$this->_dao->getRow("select * from app_session_temp where GUID='$GUID' and mc_type=1 order by LastDate desc limit 1");
        $temp=true;
        if(empty($temp))
        {
         echo"<h2 style=\"text-align:center;\">对不起，您没有权限访问该页面,您未登录</h2>";exit;
        }
        $url=DCURL."/dbmaketprice.php?view=index";
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        gourl($url);
    }

    public function cgzxrb($params){
        $GUID=$params["GUID"];
        //$temp=$this->_dao->getRow("select * from app_session_temp where GUID='$GUID' and mc_type=1 order by LastDate desc limit 1");
        $temp=true;
        if(empty($temp))
        {
         echo"<h2 style=\"text-align:center;\">对不起，您没有权限访问该页面,您未登录</h2>";exit;
        }
        $url=DCURL."/dbmaketprice.php?view=cgzxrb";
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        gourl($url);
    }
    
    public function liuzhousteeldata($params){
        $GUID=$params["GUID"];
        $temp=true;
        if(empty($temp))
        {
         echo"<h2 style=\"text-align:center;\">对不起，您没有权限访问该页面,您未登录</h2>";exit;
        }
        $url="https://www.steelhome.com/linkphp/liuzhousteel.php?view=search";
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        gourl($url);
    }
    
    public function sggcprice($params){

        $GUID=$params["GUID"];
        //$temp=$this->_dao->getRow("select * from app_session_temp where GUID='$GUID' and mc_type=1 order by LastDate desc limit 1");
        $temp=true;
        if(empty($temp))
        {
         echo"<h2 style=\"text-align:center;\">对不起，您没有权限访问该页面,您未登录</h2>";exit;
        }
        $type=$params['Type'];
        $view="";
        switch($type){
            case "1": $view="webview";break;
            case "2": $view="sg_index";break;
            case "3": $view="importgxjc";break;
        }
        $url = SGDCURL . "/web/sggcprice.php?view=$view";
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        gourl($url);
    }
    
    public function sgxpcdjkb($params){

        $GUID=$params["GUID"];
        //$temp=$this->_dao->getRow("select * from app_session_temp where GUID='$GUID' and mc_type=1 order by LastDate desc limit 1");
        $temp=true;
        if(empty($temp))
        {
         echo"<h2 style=\"text-align:center;\">对不起，您没有权限访问该页面,您未登录</h2>";exit;
        }
        $url=DCURL."/sgxpcdjkb.php?view=index";
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        gourl($url);
    }
    
    public function sgylprice($params){

        $GUID=$params["GUID"];
        //$temp=$this->_dao->getRow("select * from app_session_temp where GUID='$GUID' and mc_type=1 order by LastDate desc limit 1");
        $temp=true;
        if(empty($temp))
        {
         echo"<h2 style=\"text-align:center;\">对不起，您没有权限访问该页面,您未登录</h2>";exit;
        }
        $type=$params['Type'];
        $view="";
        switch($type){
            case "1": $view="index";break;
            case "2": $view="jckc";break;
            // case "3": $view="jckclist";break;
            case "3": $view="articles&article_type=35";break;
            case "4": $view="pricecomparisonindex";break;
        }
        if($type==1){
            $url=DCURL."/sgylprice.php?view=$view";
        } else {
            if($type==3){
                $url=SGDCURL."/web/sgqyjwsp.php?view=$view";
            }else{
                $url=SGDCURL."/web/sgylprice.php?view=$view";
            }
        }



        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        gourl($url);
    }

    public function sgpqtb($params){
        $GUID=$params["GUID"];
        //$temp=$this->_dao->getRow("select * from app_session_temp where GUID='$GUID' and mc_type=1 order by LastDate desc limit 1");
        $temp=true;
        if(empty($temp))
        {
         echo"<h2 style=\"text-align:center;\">对不起，您没有权限访问该页面,您未登录</h2>";exit;
        }
        $type=$params['Type'];
        $view="";
        switch($type){
            case "1": $view="index";break;
            case "2": $view="mrkcupload";break;
            case "3": $view="kckb";break;
        }
        $url=SGDCURL."/web/sgpqtb.php?view=$view";
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        
        gourl($url);

    }

    public function sgdataimport($params){

        $GUID=$params["GUID"];
        //$temp=$this->_dao->getRow("select * from app_session_temp where GUID='$GUID' and mc_type=1 order by LastDate desc limit 1");
        $temp=true;
        if(empty($temp))
        {
         echo"<h2 style=\"text-align:center;\">对不起，您没有权限访问该页面,您未登录</h2>";exit;
        }
        
        $type=$params['Type'];
        $view="";
        switch($type){
            case "3": $uptype="view=index&uptype=3";break;
            case "4": $uptype="view=index&uptype=4";break;
            case "5": $uptype="view=index&uptype=5";break;
            case "6": $uptype="view=index&uptype=6";break;
            case "37": $uptype="view=index2&uptype=37";break;
            case "38": $uptype="view=index2&uptype=38";break;
            case "40": $uptype="view=manualdata&viewtype=1&lgview=1";break;
            case "41": $uptype="view=manualdata&viewtype=1&hgview=1";break;
            case "42": $uptype="view=manualdata&viewtype=2";break;
            case "43": $uptype="view=manualdata&viewtype=3";break;
            case "44": $uptype="view=manualdata&viewtype=4";break;
        }
        $url=SGDCURL."/web/sgdataimport.php?".$uptype;
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        
        gourl($url);

    }

    public function sgcwrb($params){

        $GUID=$params["GUID"];
        //$temp=$this->_dao->getRow("select * from app_session_temp where GUID='$GUID' and mc_type=1 order by LastDate desc limit 1");
        $temp=true;
        if(empty($temp))
        {
            echo"<h2 style=\"text-align:center;\">对不起，您没有权限访问该页面,您未登录</h2>";exit;
        }
        
        $type=$params['Type'];
        $view="";
        switch($type){
            case "1": $view="lr";break;
            case "2": $view="index";break;
            case "3": $view="xsjh";break;
            case "4": $view="xsjzpj";break;
            case "5": $view="cgjzpj";break;
            case "6": $view="steelsales";break;
            case "7": $view="areasales2";break;
            case "8": $view="xianprice";break;
            case "9": $view="xschjz_echarts";break;
            case "10": $view="xscg_jz";break;
            case "11": $view="xscg_kc";break;
        }
        $url=SGDCURL."/web/sgcwrb.php?view=".$view;
        if($type==10 || $type==11)
        {
            $url=SGDCURL."/web/sgxxh.php?view=".$view;
        }
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        gourl($url);
    }

    public function articles($params){

        $GUID=$params["GUID"];
        $temp=true;
        if(empty($temp))
        {
         echo"<h2 style=\"text-align:center;\">对不起，您没有权限访问该页面,您未登录</h2>";exit;
        }
        
        $type=$params['Type'];
        $url=SGDCURL."/web/sgqyjwsp.php?view=articles&article_type=".$type."&GUID=".$GUID;
        gourl($url);
    }

    public function sglrtb($params){

        $GUID=$params["GUID"];
        //$temp=$this->_dao->getRow("select * from app_session_temp where GUID='$GUID' and mc_type=1 order by LastDate desc limit 1");
        $temp=true;
        if(empty($temp))
        {
         echo"<h2 style=\"text-align:center;\">对不起，您没有权限访问该页面,您未登录</h2>";exit;
        }
        
        $type=$params['Type'];
        $view="";
        switch($type){
            case "7": $uptype="uptype=7";break;
            case "8": $uptype="uptype=8";break;
            case "9": $uptype="uptype=9";break;
            case "10": $uptype="uptype=10";break;
            case "16": $uptype="uptype=16";break;
            case "17": $uptype="uptype=17";break;
            case "31": $uptype="uptype=31";break;
            case "32": $uptype="uptype=32";break;
            case "33": $uptype="uptype=33";break;
            case "34": $uptype="uptype=34";break;
            case "35": $uptype="uptype=35";break;
            case "36": $uptype="uptype=36";break;
            case "40": $uptype="uptype=40";break;
        }
        $url=SGDCURL."/web/sglrtb.php?view=mrkcupload&".$uptype;
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        
        gourl($url);

    }

    public function sglrjs($params){

        $GUID=$params["GUID"];
        //$temp=$this->_dao->getRow("select * from app_session_temp where GUID='$GUID' and mc_type=1 order by LastDate desc limit 1");
        $temp=true;
        if(empty($temp))
        {
         echo"<h2 style=\"text-align:center;\">对不起，您没有权限访问该页面,您未登录</h2>";exit;
        }
        
        $type=$params['Type'];
        $view="";
        switch($type){
            case "60": $uptype="uptype=60";break;
            case "61": $uptype="uptype=61";break;
            case "62": $uptype="uptype=62";break;
            case "63": $uptype="uptype=63";break;
            case "64": $uptype="uptype=64";break;
        }
        $url=SGDCURL."/web/sglrjs.php?view=mrkcupload&".$uptype;
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        
        gourl($url);

    }

    public function sgjckp($params){

        $GUID=$params["GUID"];
        //$temp=$this->_dao->getRow("select * from app_session_temp where GUID='$GUID' and mc_type=1 order by LastDate desc limit 1");
        $temp=true;
        if(empty($temp))
        {
            echo"<h2 style=\"text-align:center;\">对不起，您没有权限访问该页面,您未登录</h2>";exit;
        }

        $type=$params['Type'];
        $view="";
        switch($type){
            case "1": $view="index";break;//销售结算价格
            case "2": $view="dgxsjg";break;//带钢价格
            case "3": $view="set_dgxsjg";break;//带钢价格设置
            case "4": $view="steel_sale_import";break;//带钢产销率指标
            case "5": $view="dgkc";break;//带钢库存
            case "6": $view="jcxsjj_echarts";break;//建筑钢材售价水平
            case "7": $view="pzgbjmx";break;//普碳钢坯比价
            case "8": $view="pzgbjmx2";break;//品种钢销售效益比价
            case "11": $url=SGDCURL."/web/sgqyjwsp.php?view=articles&article_type=31";break;//销售结算价格往期
            case "21": $url=SGDCURL."/web/sgqyjwsp.php?view=articles&article_type=41";break;//带钢价格往期
            case "41": $url=SGDCURL."/web/sgqyjwsp.php?view=articles&article_type=42";break;//带钢产销率指标往期
        }

        $url = $url == '' ? SGDCURL . "/web/sgjcxsjg.php?view=".$view : $url;
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        gourl($url);
    }

    public function sgweeklyreport($params){

        $GUID=$params["GUID"];
        //$temp=$this->_dao->getRow("select * from app_session_temp where GUID='$GUID' and mc_type=1 order by LastDate desc limit 1");
        $temp=true;
        if(empty($temp))
        {
         echo"<h2 style=\"text-align:center;\">对不起，您没有权限访问该页面,您未登录</h2>";exit;
        }

        $type=$params['Type'];
        $view="";
        switch($type){
            case "1": $view="view=index";break;
        }
        $url=DCURL."/sgfinancialweeklyreport.php?".$view;
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }

        gourl($url);

    }


    public function sgbbdr($params){

        $GUID=$params["GUID"];
        //$temp=$this->_dao->getRow("select * from app_session_temp where GUID='$GUID' and mc_type=1 order by LastDate desc limit 1");
        $temp=true;
        if(empty($temp))
        {
         echo"<h2 style=\"text-align:center;\">对不起，您没有权限访问该页面,您未登录</h2>";exit;
        }
        
        $type=$params['Type'];
        $view="";
        switch($type){
        
            case "11": $view="view=hcgcyxsupload";break;
            case "12": $view="view=hcgczxsupload";break;
            case "13": $view="view=yjyybupload";break;
            case "14": $view="view=hcgcrxsupload";break;
            case "18": $view="view=sjupload";break;
        }
        $url=SGDCURL."/web/sgbbdr.php?".$view;
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        
        gourl($url);

    }

    public function sgqyjwsp($params){

        $GUID=$params["GUID"];
        //$temp=$this->_dao->getRow("select * from app_session_temp where GUID='$GUID' and mc_type=1 order by LastDate desc limit 1");
        $temp=true;
        if(empty($temp))
        {
         echo"<h2 style=\"text-align:center;\">对不起，您没有权限访问该页面,您未登录</h2>";exit;
        }
        
        $type=$params['Type'];
        $view="";
        switch($type){
        
            case "5": $view="view=lwgprice";break;
            case "6": $view="view=cityandwj_avg";break;
            case "7": $view="view=xacdandqg";break;
            case "8": $view="view=fourcity";break;
            case "9": $view="view=totalhz";break;
            case "11": $view="view=target";break;
            case "12": $view="view=articles";break;
          
        }
        $url=SGDCURL."/web/sgqyjwsp.php?".$view;
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        
        gourl($url);

    }
    public function sgscjcprice($params){

        $GUID=$params["GUID"];
        //$temp=$this->_dao->getRow("select * from app_session_temp where GUID='$GUID' and mc_type=1 order by LastDate desc limit 1");
        $temp=true;
        if(empty($temp))
        {
         echo"<h2 style=\"text-align:center;\">对不起，您没有权限访问该页面,您未登录</h2>";exit;
        }
        
        $type=$params['Type'];
        $view="";
        switch($type){
        
            case "1": $view="view=index";break;
            case "2": $view="view=yulong_price_diff";break;
            case "13": $view="view=ppjc";break;
        
        }
        $url=DCURL."/sgscjcprice.php?".$view;
        if($type==2)$url=DCURL."/shanngang.php?".$view;
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        
        gourl($url);

    }
    public function sgpqtb_zdj($params){

        $GUID=$params["GUID"];
        //$temp=$this->_dao->getRow("select * from app_session_temp where GUID='$GUID' and mc_type=1 order by LastDate desc limit 1");
        $temp=true;
        if(empty($temp))
        {
         echo"<h2 style=\"text-align:center;\">对不起，您没有权限访问该页面,您未登录</h2>";exit;
        }
        $url=DCURL."/sgpqtb.php?view=zdj_lr";

        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        gourl($url);
    }
    
    public function sgtscbset($params){

        $GUID=$params["GUID"];
        //$temp=$this->_dao->getRow("select * from app_session_temp where GUID='$GUID' and mc_type=1 order by LastDate desc limit 1");
        $temp=true;
        if(empty($temp))
        {
            echo"<h2 style=\"text-align:center;\">对不起，您没有权限访问该页面,您未登录</h2>";exit;
        }
        $url=DCURL."/sgtieshuicb.php?view=index";

        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        gourl($url);
    }
    
    public function sggpcbset($params){

        $GUID=$params["GUID"];
        //$temp=$this->_dao->getRow("select * from app_session_temp where GUID='$GUID' and mc_type=1 order by LastDate desc limit 1");
        $temp=true;
        if(empty($temp))
        {
            echo"<h2 style=\"text-align:center;\">对不起，您没有权限访问该页面,您未登录</h2>";exit;
        }
        $url=DCURL."/sgtieshuicb.php?view=gangpiindex";

        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        gourl($url);
    }
    
    public function sggccbset($params){

        $GUID=$params["GUID"];
        //$temp=$this->_dao->getRow("select * from app_session_temp where GUID='$GUID' and mc_type=1 order by LastDate desc limit 1");
        $temp=true;
        if(empty($temp))
        {
            echo"<h2 style=\"text-align:center;\">对不起，您没有权限访问该页面,您未登录</h2>";exit;
        }
        $url=DCURL."/sgtieshuicb.php?view=gangcaiindex";

        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        gourl($url);
    }

    public function sglhgcbset($params){

        $GUID=$params["GUID"];
        //$temp=$this->_dao->getRow("select * from app_session_temp where GUID='$GUID' and mc_type=1 order by LastDate desc limit 1");
        $temp=true;
        if(empty($temp))
        {
            echo"<h2 style=\"text-align:center;\">对不起，您没有权限访问该页面,您未登录</h2>";exit;
        }
        $url=DCURL."/sgtieshuicb.php?view=longhangangindex";

        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        gourl($url);
    }

    public function sgqhtb($params){
        $type=$params['Type'];
        $view="";
        switch($type){
            case "1": $view="jiaotan";break;
            case "2": $view="luowen";break;
            case "3": $view="tiekuang";break;
            case "4": $view="feigang";break;
            case "5": $view="tiehejin";break;
        }
        $url=DCURL."/sgqhtb.php?view=$view";
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        gourl($url);
    }
    public function sgxymx($params){
        $type=$params['Type'];
        $view="";
        switch($type){
            case "1": $view="index";break;
            case "2": $view="gdbj";break;
            case "3": $view="xspq";break;
            case "4": $url=SGDCURL."/web/sggxcjdetail.php?view=index";break;//add by zfy 2021/01/22 供销差价图表
            case "5": $url=SGDCURL."/web/sggxcjdetail.php?view=market_overview_list";break;//add by zfy 2021/01/29 市场综述
            case "6": $url=SGDCURL."/web/sgwlpjimport.php?view=importpzg";break;
            case "7": $url=SGDCURL."/web/sggxcjdetail.php?view=steel_sale_import";break;//add by zfy 2021/02/04 建材产销率录入
            case "8": $url=SGDCURL."/web/sgqyjwsp.php?view=articles&article_type=32";break;//add by zfy 2021/02/04 建材产销率显示

        }
        //xiangbin add 20201028 start 
        $url = $url == '' ? SGDCURL . "/web/sgxymx.php?view=$view" : $url;
        if($type==-1)// 各区域销售价格导入 陕钢调价通知
        {
            $url=SGDCURL."/web/sgtjtzdr.php?view=index";
        }
       
        //xiangbin add 20201028 
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        //echo $url;exit;
        gourl($url);
    }


    public function sgwlpj($params){
        $type=$params['Type'];
        $view="";
        switch($type){
            case "1": $view="index&uptype=24";break;
            case "2": $view="index&uptype=25";break;
            case "3": $view="index&uptype=26";break;
            case "4": $view="index&uptype=27";break;
            case "5": $view="index&uptype=28";break;
            case "6": $view="index&uptype=29";break;
            case "7": $view="index&uptype=30";break;
            case "8": $view="wlkp_index";break;
            case "9": $view="cgpj";break;
            case "10": $view="wlkp_index&article_type=72";break;
            case "12": $view="pszsjj";break;
            case "13": $view="gxcjwh&type=1";break;
            case "14": $view="gxcjwh&type=2";break;
            case "15": $view="gxcjwh&type=3";break;
            case "16": $view="gxcjwh&type=4";break;
            case "17": $view="gxcjwh&type=5";break;
            case "18": $view="gxcjwh&type=6";break;
            case "19": $view="gxcjwh&type=7";break;
            case "20": $view="gxcjwh&type=8";break;
            case "21": $view="gxcjwh&type=9";break;
            case "22": $view="gxcjwh&type=10";break;
            case "23": $view="gxcjwh&type=11";break;
            case "24": $view="gxcjwh&type=12";break;
            case "25": $view="gxcjwh&type=13";break;
            case "26": $view="gxcjwh&type=14";break;
            case "27": $view="gxcjwh&type=15";break;
            case "28": $view="gxcjwh&type=16";break;
            case "29": $view="gxcjwh&type=17";break;
            case "30": $view="gxcjgcwh&type=2";break;
            case "31": $view="gxcjgcwh&type=1";break;
            case "32": $view="wlfyupload&uptype=55";break;
            case "33": $view="wlfyupload&uptype=56";break;
            case "34": $view="xspjcs";break;
            case "35": $view="import_jkk_jt&type=1";break;
            case "36": $url=SGDCURL."/web/spidermanage.php?view=index";break;//供销差价价格管理
            case "37": $url=SGDCURL."/web/spidermanage.php?view=index&all=1";break;//供销差价价格查询
        }
       
        $url = $url == '' ? SGDCURL . "/web/sgwlpjimport.php?view=$view" : $url;
        //echo $url;exit;
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        gourl($url);
    }
    
    public function sgcssz($params){
        $type=$params['Type'];
        $view="";
        switch($type){
            case "1": $view="gxcjgcwh&typelr=1";break;
            case "2": $view="gxcjgcwh&typelr=2";break;
            case "3": $view="gxcjgcwh&typelr=3";break;
            case "4": $view="gxcjgcwh&typelr=4";break;
            case "5": $view="gxcjgcwh&typelr=5";break;
            case "6": $view="gxcjgcwh&typelr=6";break;
            case "7": $view="gxcjgcwh&typelr=7";break;
        }
        //xiangbin add 20201028 start  
        $url =SGDCURL . "/web/sgcssz.php?view=$view";
        
        //xiangbin add 20201028 
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        gourl($url);
    }
    public function sgredirect($params)
    {
        $type=$params['Type'];
        $view="";
        switch($type){
            case "1": $url =SGDCURL . "/web/sgcgcbjs.php?view=xunigcnew";break;
            case "2": $url =SGDCURL . "/web/sgcgcbjs.php?view=tblist";break;
			case "3": $url =SGDCURL . "/web/sgcgcbjs.php?view=gxcj";break;
			case "4": $url =SGDCURL . "/web/sgxxh.php?view=xxhindex";break; //数据大屏
            case "5": $url =SGDCURL . "/web/sgpqxspj.php?view=index";break; //片区销售评价报告 当期
            case "6": $url =SGDCURL . "/web/sgqyjwsp.php?view=articles&article_type=99";break; //片区销售评价报告 往前
            case "7": $url =SGDCURL . "/web/sgpqxspj.php?view=index2";break; //片区销售评价报告（年） 当期
            case "8": $url =SGDCURL . "/web/sgqyjwsp.php?view=articles&article_type=100";break; //片区销售评价报告 往前
            case "9": $url =SGDCURL . "/web/sgxxh.php?view=gxcjnew";break;
            case "10": $url =SGDCURL . "/web/sgjyfx.php?view=jyfxecharts";break;
            case "11": $url =SGDCURL . "/web/sgjyfx.php?view=index";break;
            case "12": $url =SGDCURL . "/web/sgjyfx.php?view=managelist";break;
            case "13": $url =SGDCURL . "/web/sglr.php?view=lhindex";break;
            case "14": $url =SGDCURL . "/web/sgwlpjimport.php?view=typemanage&dta_type=29";break;//品种钢比价 > 品种钢类型设置
            case "15": $url =SGDCURL . "/web/sgwlpjimport.php?view=typemanage&dta_type=30";break;//供销差价 >  进口矿类型设置
            case "16": $url =SGDCURL . "/web/sgwlpjimport.php?view=typemanage&dta_type=32";break;//品种钢比价 > 品种钢类型设置(新)
            case "17": $url =SGDCURL . "/web/sgylprice.php?view=dygx";break; //供销差价 > 对应关系
            case "18": $url =SGDCURL . "/web/sgylprice.php?view=jghz";break; //供销差价 > 价格汇总
			case "19": $url =SGDCURL . "/web/sgcssz.php?view=gxcjgcwhnew&typelr=1";break; //进口矿
			case "20": $url =SGDCURL . "/web/sgcssz.php?view=gxcjgcwhnew&typelr=2";break; //采购价格采购量
			case "21": $url =SGDCURL . "/web/sgcssz.php?view=gxcjgcwhnew&typelr=3";break; //成本
			case "22": $url =SGDCURL . "/web/pzgjgmanage.php?view=index";break; //成本
			case "23": $url =SGDCURL . "/web/sgcssz.php?view=gxcjgcwhnew&typelr=4";break; //年成本
            
        }
       
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
         gourl($url);
    }


    
    // 新钢所有网页链接由此进入
    public function xgredirect($params)
    {
        $type=$params['Type'];
        $view="";
        switch($type){
            //zhengnb add 20201130 start
            case "1": $view="/upload?view=upload";break;  //铁前成本上传
            case "2": $view="/synonymmanage/index?";break;  //同义词管理
            case "3": $view="/tqcb?dta_type=1";break;  //铁前成本生成页面
            case "4": $view="/tqcb?dta_type=2";break;  //铁前成本生成页面
            case "5": $view="/tqcb?dta_type=3";break;  //铁前成本生成页面
            case "6": $view="/tqcb?dta_type=4";break;  //铁前成本生成页面
            case "7": $view="/tqcb?dta_type=5";break;  //铁前成本生成页面
            case "8": $view="/tqcb?dta_type=6";break;  //铁前成本生成页面
            //zhengnb add 20201130 end
            //add by zfy started 2020/12/07
            case "9": $view="/kanban?";break;  //信息看板
            case "10": $view="/report?";break;  //研究报告
            //add by zfy ended 2020/12/07
			//add by changhong 2020-12-08 started
            case "11": $view="/shengtiexiaoyi?";break;  //废钢生铁效益对比
            //add by changhong 2020-12-08 end
            //add by zhengnb 2020-12-17 started
            case "12": $view="/cxlr/index?";break;  //产线数据录入
            //add by zhengnb 2020-12-17 end

            case "13": $view="/report/report_detail?";break;  //历史研究报告
            //add by zfy started 2020/12/23
            case "14": $view="/kanban/kanban_detail?";break;  //信息看板查看
            //add by zfy ended 2020/12/23
            case "15": $view="/cxlr/synonym?";break;  //钢种关联录入

            case "16": $view="/lrcs/xbcindex?type=6";break;  //一钢
            case "17": $view="/lrcs/xbcindex?type=7";break;  //二钢
            case "18": $view="/lrcs/xbcindex?type=8";break;  //中板线
            case "19": $view="/lrcs/xbcindex?type=9";break;  //厚板线
            case "20": $view="/lrcs/xbcindex?type=10";break;  //热轧

            case "21": $view="/lrcs/xbcindex?type=1";break;//棒二
            case "22": $view="/lrcs/xbcindex?type=2";break;//高棒
            case "23": $view="/lrcs/xbcindex?type=3";break;//线材



            case "24": $view="/lrcs/xbcindex?type=5";break;//冷轧连退
            case "25": $view="/lrcs/xbcindex?type=4";break;// 冷轧酸扎
            case "26": $view="/cxlr/cslist?";break;// 产线利润测算
            case "27": $view="/cxlr/zyxclist?";break;// 中冶新材成本测算
            case "28": $view="/cxlr/ytgdlist?";break;// 优特钢带成本测算
            case "29": $view="/graph?tmp=1";break;// 生铁产线流程图
            case "30": $view="/cxlr/graph?tmp=1";break;// 钢厂产线流程图

            //add by zfy started 2020/12/23
            case "31": $view="/kanban/sms_list?";break;  //市场信息看板
            case "32": $view="/kanban/market_week_news?";break;  //每周市场要闻列表
            //add by zfy ended 2020/12/23
            case "33": $view="/cxlr/cslist_xg?";break;// 成本利润测算
            case "34": $view="/cxlr/cslist_day?";break;// 产线利润测算（每日）
            case "35": $view="/xgkanban/mainlist?";break;// 新钢信息看板列表
            case "36": $view="/xgkanban/lastkanbanview?";break;// 新钢信息看板
            //add by zfy started 2021/05/23
            case "37": $view="/kanban/market_week_news_detail?";break;  //每周市场要闻详细
            //add by zfy ended 2021/05/23
            case "38": $view="/xypj/addtype?";break;// 重点品种接单效益评价
            case "39": $view="/xypj/listview?";break;// 重点品种接单效益评价列表
            case "40": $view="/lrcs/priceadd?type=1";break;//规格加价
            case "41": $view="/allgz/gzdetail?type=1";break;//钢轧全流程
            case "42": $view="/allgz/gzcxview?";break;//新版产线利润预测
            case "43": $view="/pzbjcs/index?";break;//主要品种报价利润  管理看板
            case "57": $view="/pzbjcs/index?jdxy=1";break;//主要品种报价利润  接单效益
            case "44": $view="/allgz/mrsc?type=jdxy";break;//接单效益
            case "45": $view="/allgz/mrsc?type=glkb";break;//管理看板
            case "46": $view="/allgz/tslr?";break;//铁水成本录入
            case "47": $view="/allgz/llbjv?";break;//炉料报价
            case "48": $view="/allgz/gdcblr?";break;//固定成本

            case "50": $view="/uploadxbc/5?";break;//棒二品种价格
            case "51": $view="/uploadxbc/4?";break;//高棒品种价格
            case "52": $view="/uploadxbc/3?";break;//线材品种价格

            case "53": $view="/export_cb?";break;//扎线成本导出
            case "54": $view="/gpcb/1?";break;//钢坯成本
            case "55": $view="/gpcb/2?";break;//优特钢带成本
            case "56": $view="/gpcb/3?";break;//新钢新材成本
            case "58": $view="/lrcs/setzjm?";break;//主焦煤量价录入
            case "59": $view="/allgz/xssjjfjlr?";break;//销售税金及附加占比录入
            case "60": $view="/energyPower/view?";break;//能源动力录入
            case "61": $view="/allgz/mrsc?type=glkb&showmore=1";break;//管理看板2
            case "62": $view="/allgz/gzcxview?showmore=1";break;//新版产线利润预测2
            case "63": $view="/pzbjcs/index?chakan=1";break;//管理看板查看 主要品种报价利润
            case "64": $view="/dataentry/tscbtz?";break;//铁水成本调整项
            case "65": $view="/dataentry/pzcbtz?";break;//品种成本调整项
            case "66": $view="/dataentry/generationAll?";break;//一键生成
            case "67": $view="/electricalsteel/CostCalculation?bigtype=0&maintype=0&childtype=4";break;//  电工钢35XW300
            case "68": $view="/electricalsteel/CostCalculation?bigtype=0&maintype=1&childtype=4";break;//  电工钢35XWH400
            case "69": $view="/electricalsteel/CostCalculation?bigtype=0&maintype=2&childtype=4";break;// 电工钢50XW440
            case "70": $view="/electricalsteel/CostCalculation?bigtype=0&maintype=3&childtype=3";break;// 电工钢50XW800
            case "71": $view="/siliconPriceManage/1/create?";break;//硅钢价格录入
            case "72": $view="/accessoryMaterial?";break;//辅材价格录入
            case "73": $view="/electricalsteel/ElectricalSteelSummary?";break;// 电工钢汇总
            case "74": $view="/siliconPriceManage/1/create?only_base_price=1&";break;// 硅钢基价 销售量录入
            case "75": $view="/gzdr/newIndex?";break;// 钢轧全流程导入
            case "76": $view="/electricalsteel/CostCalculation?bigtype=0&maintype=4&childtype=4";break;//  电工钢50XW600
            case "77": $view="/electricalsteel/CostCalculation?bigtype=0&maintype=5&childtype=4";break;//  电工钢35XW1900
            case "78": $view="/electricalsteel/CostCalculation?bigtype=0&maintype=6&childtype=4";break;// 电工钢30XWH1500
            case "79": $view="/electricalsteel/CostCalculation?bigtype=0&maintype=7&childtype=4";break;// 电工钢27XW1400
            case "80": $view="/electricalsteel/CostCalculation?bigtype=0&maintype=8&childtype=4";break;// 电工钢25XWH1300
        }
        $url=XGDCURL.$view;
       
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        
        gourl($url);
    }

    // 新钢所有网页链接由此进入
    public function SteelRollingProcess($params)
    {
        $type=$params['Type'];
        $view="";
        switch($type){
          
            case "1": $view="/VarietyQuotationIndex/1?";break;  //价格录入页面
            case "2": $view="/VarietyQuotationIndex_Cl/1?";break;  //产量录入页面
            case "3": $view="/zhbIndex/1?";break;  //
            case "4": $view="/zhbIndex/2?";break;  
            case "5": $view="/bjx/index?";break;  //史忠苟的
            case "6": $view="/xiaoliangsetIndex?";break;  //
        }
        $url=XGDCURL.$view;
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        gourl($url);
    }
    //added by shizg started
    public function xgredirect_dj( $params ){
        $type=$params['Type'];
        $view="";
        switch($type){
            case "1": $view="/crawlingManage?";break;  //爬取管理
            case "2": $view="/xunSeting?";break;  //旬设置管理
            case "3": $view="/uploadxr?";break;  //基础表格录入管理
            case "4": $view="/managedata?";break;  //销售结算价格建议
        }
        $url=XGDCURL.$view;
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        gourl($url);
    }
    //added by shizg ended

    //add by std 2020/12/05
    public function xgquanx($params){
        $type=$params['Type'];
        $view="";
        switch($type){
            case "1": $view="index";break;
            case "2": $view="powerlist";break;
            case "3": $view="/xgloglist?";break;
            case "4": $view="sgloglist";break;
            case "5": $view="userindex";break;
            case "6": $view="/xglog/accountlist?stype=2";break;
            case "7": $view="/xglog/accountlist?stype=1";break;
            case "8": $view="loglist";break;
        }
        
        $url=DCURL."/xgquanx.php?view=$view";
        if($type==3 || $type==6 || $type==7)// 各区域销售价格导入 陕钢调价通知
        {
            $url=XGDCURL.$view;
        }else if($type==4){
            $url=SGDCURL."/web/sgquanx.php?view=$view";
        }

        if($type==1 || $type==5){
            $url.="&from=$view";
        }

        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }

        gourl($url);
    }

    public function xgyjbg($params){
        $type=$params['Type'];
        $AppMode=$params['AppMode']=="" ? "6" : $params['AppMode'];


        $url=DCURL."/xinsteel_steelhome_price.php?view=xinsteel_news_list&AppMode=".$AppMode."&type=".$type;
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        gourl($url);
    }

    public function xgyjbg_show($params){
        $type=$params['Type'];
        $AppMode=$params['AppMode']=="" ? "6" : $params['AppMode'];


        $url=DCURL."/xinsteel_steelhome_price.php?view=xinsteel_news_list&show=1&AppMode=".$AppMode."&type=".$type;
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        gourl($url);
    }

    public function sgyjbg($params){
        $type=$params['Type'];
	$AppMode=$params['AppMode']=="" ? "6" : $params['AppMode'];

        $url=DCURL."/sg_steelhome_price.php?view=sg_report_list&AppMode=".$AppMode."&type=".$type;
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        gourl($url);
    }

    public function sgyjbg_show($params){
        $type=$params['Type'];
	$AppMode=$params['AppMode']=="" ? "6" : $params['AppMode'];

        $url=DCURL."/sg_steelhome_price.php?view=sg_report_list&show=1&AppMode=".$AppMode."&type=".$type;
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        gourl($url);
    }

    public function styjbg($params){
        $type=$params['Type'];
        $AppMode=$params['AppMode']=="" ? "6" : $params['AppMode'];

        $url=DCURL."/sg_steelhome_price.php?view=sg_report_list&mc_type=0&AppMode=".$AppMode."&type=".$type;
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        gourl($url);
    }

    public function styjbg_show($params){
        $type=$params['Type'];
        $AppMode=$params['AppMode']=="" ? "6" : $params['AppMode'];

        $url=DCURL."/sg_steelhome_price.php?view=sg_report_list&mc_type=0&show=1&AppMode=".$AppMode."&type=".$type;
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        gourl($url);
    }

	public function steelhome_xxkb($params){
        
        $url=DCURL."/xinsteel_steelhome_price.php?view=sms_list";
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        gourl($url);
    }

	public function logshow($params){
        
        $url=DCURL."/memberlog.php?view=list";
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        gourl($url);
    }

	public function steelhome_khgl($params){
        $url=DCURL."/customerdata.php?view=index";
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        gourl($url);
    }

    public function dcxxh($params){

        $GUID=$params["GUID"];
        //$temp=$this->_dao->getRow("select * from app_session_temp where GUID='$GUID' and mc_type=1 order by LastDate desc limit 1");
        $temp=true;
        if(empty($temp))
        {
         echo"<h2 style=\"text-align:center;\">对不起，您没有权限访问该页面,您未登录</h2>";exit;
        }
        $type=$params['Type'];
        $view="";
        switch($type){
        
            case "1": $view="view=template_list";break;
            case "2": $view="view=xxhindex";break;
        
        }
        $DCURL = str_replace("/web","",DCURL);
        if ($type == "3") {
            $url=APP_URL_WWW."/_v2app/steelhomexxh.php?view=index";
        } else {
            $url=$DCURL."/dcxxh.php?".$view;
        }
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        gourl($url);

    }

    public function yc_jump($params){
        $type = $params['Type'];
        $mode = $params['mode'];
        $view = "view=forecast_list&type=".$type."&mode=".$mode;
        if($type == 4){
            $view = "view=data_report&GUID=&mode=$mode";
        }
        // $DCURL = str_replace("/web","",DCURL);
        $url = DCURL."/xg_price_yc.php?".$view;
        gourl($url);
    }

    public function jgyc_yj($params){
        // print_r($params);
        $type = $params['Type'];
        switch($type){
            case "1": $view="view=msg_manage";break;
            case "2": $view="view=yj_manage";break;
            case "3": $view="view=mysteel_compared_price";break;
            case "4": $view="view=productInventory&data_type=3";break; // 中间品库存
            case "5": $view="view=productInventory&data_type=2";break; // 产成品库存
            case "6": $view="view=loadWord&export=0&muban=1";break; // 价格表
            case "7": $view="view=loadWord&export=0&muban=2";break; // 结算文
            case "8": $view="view=uploadzgx";break; // 导入中钢协的数据
            case "9": $view="view=huizong_duibiao";break; // 售价对标
            case "10": $view="view=huizong_gcduibiao";break; // 产品售价与周边钢厂售价对标
            case "11": $view="view=huizong_hyduibiao";break; // 钢筋、线材行业对标
            case "12": $view="view=priceBenchmarking";break;
            case "13": $view="view=thj_pz_price";break;//合金市场情况
            case "14": $view="view=by_pz_ship";break;//各品种价格指数
            //add by zfy started 2023/07/24
            case "15": $view="view=regional_price_input_manage";break;//区域价格录入
            case "16": $view="view=regional_price_setting_manage";break;//区域价格配置录入
            case "18": $view="view=excel_import_manage&dta_type=XG-AREA-FLOW";break;//区域流向表
            case "19": $view="view=excel_upload_manage&dta_type=XG-MONTHLY-OUTPUT-DATA";break;//监管月报产量数据表
            case "20": $view="view=plan_output_manage&type=1";break;//计划和产量数据表
            case "21": $view="view=stock_data_manage&type=1";break;//库存数据表
            case "22": $view="view=hand_order_manage&type=1";break;//手持订单量
            case "23": $view="view=capital_return_manage_list&dta_type=XG-CAPITAL-RETURN";break;//资金回笼
            case "24": $view="view=excel_upload_manage&dta_type=XG-ORDER-INDEX-DATA";break;//接单指标
            case "25": $view="view=regional_price_comparison&price_type=1";break;//区域价格对比 螺纹钢
            case "26": $view="view=regional_price_comparison&price_type=2";break;//区域价格对比 中厚板
            case "27": $view="view=regional_price_comparison&price_type=3";break;//区域价格对比 热卷
            //add by zfy ended 2023/07/24
            case "28": $view="view=scjgbg";break; // 市场价格标杆
            case "29": $view="view=huizong_gcduibiao_xbc";break; // 线棒材售价对标
            case "30": $view="view=huizong_gcduibiao_lz";break; // 冷轧售价对标
            case "31": $view="view=huizong_gcduibiao_rz";break; // 热轧售价对标
            case "32": $view="view=huizong_gcduibiao_zhb";break; // 中厚板售价对标
            case "33": $view="view=tf_lf_jc";break; // 铁废、螺废价差
            case "34": $view="view=uploadyrl";break; // 原燃料采购价格导入
            case "35": $view="view=huizong_yrl_jgjkb";break; // 原燃料价格监控表
        }
        // $DCURL = str_replace("/web","",DCURL);
        $url = DCURL."/xg_price_yc.php?".$view;
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        gourl($url);
    }

    public function jgyc_jc($params){
        // print_r($params);
        $type = $params['Type'];
        switch($type){
            case "1": $view="view=data_zd_manage&uptype=1&type=2";break;
            case "2": $view="view=data_zd_manage&uptype=2&type=2";break;
            case "3": $view="view=data_zd_manage&uptype=3&type=2";break;
            case "4": $view="view=data_zd_manage&uptype=4&type=2";break;
            case "5": $view="view=data_zd_manage&uptype=5&type=2";break;
            case "6": $view="view=data_zd_manage&uptype=6&type=2";break;
            case "7": $view="view=data_zd_manage&uptype=7&type=2";break;
            case "8": $view="view=data_zd_manage&uptype=8&type=2";break;
            case "9": $view="view=data_zd_manage&uptype=9&type=2";break;
            case "10": $view="view=data_zd_manage&uptype=10&type=2";break;
            case "11": $view="view=price_table&type=1";break;
            case "12": $view="view=price_table&type=2";break;
            case "13": $view="view=data_zd_manage&type=7";break;
            case "14": $view="view=jxpj";break;
            case "15": $view="view=sd_steel_data_manage&uptype=1&type=8";break;
            case "16": $view="view=sd_steel_data_manage&uptype=2&type=8";break;
            case "17": $view="view=sd_steel_data_manage&uptype=3&type=8";break;
            case "18": $view="view=sd_steel_data_manage&uptype=4&type=8";break;
            case "19": $view="view=sd_steel_data_manage&uptype=5&type=8";break;
            case "20": $view="view=sd_steel_order_manage&uptype=1&type=10";break;
            case "21": $view="view=sd_steel_data_manage&uptype=2&type=9";break;
            case "22": $view="view=sd_steel_data_manage&uptype=3&type=9";break;
            case "23": $view="view=sd_steel_data_manage&uptype=4&type=9";break;
            case "24": $view="view=sd_steel_data_manage&uptype=5&type=9";break;



        }
        // $DCURL = str_replace("/web","",DCURL);
        $url = DCURL."/xg_price_jsw.php?".$view;
        foreach($_GET as $i=> $v){
            if($i!="view"&&$i!="action")$url.="&".$i."=".$v;
        }
        gourl($url);
    }
    public function sd_steel_yc_jump($params){
        $type = $params['Type'];
        $mode = $params['mode'];
        $view = "view=forecast_list&type=".$type."&mode=".$mode;
        if($type == 4){
            $view = "view=data_report&GUID=&mode=$mode";
        }
        // $DCURL = str_replace("/web","",DCURL);
        $url = DCURL."/sdgt_market_yc.php?".$view;
        gourl($url);
    }

    public function sd_steel_dingjia_jump($params){
        $type = $params['Type'];
        $mode = $params['mode'];
        $view = "view=dingjia_list&type=".$type."&mode=".$mode;
        // if($type == 4){
        //     $view = "view=data_report&GUID=&mode=$mode";
        // }
        // $DCURL = str_replace("/web","",DCURL);
        $url = DCURL."/sdgt_market_yc.php?".$view;
        gourl($url);
    }

    public function sd_steel_curr_yc($params){
        $type = $params['Type'];
        $mode = $params['mode'];
        $view = "view=day_market_yc";
        if($type == 1){
            $view = "view=day_market_yc";
        }elseif($type == 2){
            $view = "view=week_market_yc";
        }elseif($type == 3){
            $view = "view=yue_market_yc";
        }
        // $DCURL = str_replace("/web","",DCURL);
        $url = DCURL."/sdgt_market_yc.php?".$view."&mode=".$mode;
        gourl($url);
    }

    public function sd_steel_datacenter($params){
        $type = $params['Type'];
        $mode = $params['mode'];
        $view = "view=datacenter_manage&type=".$type."&mode=".$mode;

        $url = DCURL."/sdgt_market_yc.php?".$view;
        gourl($url);
    }

    public function sd_steel_hgfx($params){
        $type = $params['Type'];
        $mode = $params['mode'];
        $view = "view=hg_jj_fenxi&type=".$type."&mode=".$mode;

        $url = DCURL."/sdgt_market_yc.php?".$view;
        gourl($url);
    }

    public function sd_steel_zhinengbaobiao_jump($params){
        $type = $params['Type'];
        $mode = $params['mode'];
        $view = "";
        if($type <= 12){
            $view = "view=gc_gy_tables&type=".$type."&mode=".$mode;
        }else if($type == 13){
            $view = "view=qc_hy_hz&type=".$type."&mode=".$mode;
        }else if($type == 14){
            $view = "view=zc_hz_teble&type=".$type."&mode=".$mode;
        }else if($type == 15){
            $view = "view=jxzz_hy_hz&type=".$type."&mode=".$mode;
        }else if($type == 16){
            $view = "view=jdhy_hz_teble&type=".$type."&mode=".$mode;
        }else if($type == 17){
            $view = "view=cg_gxph&type=".$type."&mode=".$mode;
        }else if($type == 18){
            $view = "view=tjj_cg_cl&type=".$type."&mode=".$mode;
        }else if($type == 19){
            $view = "view=gx_xun_cg_gc_cl&type=".$type."&mode=".$mode;
        }else if($type == 20){
            $view = "view=gp_gc_jck&type=1&mode=".$mode;
        }else if($type == 21){
            $view = "view=gp_gc_jck&type=2&mode=".$mode;
        }else if($type == 22){
            $view = "view=quanqiu_cgcl&type=".$type."&mode=".$mode;
        }
        $url = DCURL."/sdgt_market_yc.php?".$view;
        gourl($url);
    }
}