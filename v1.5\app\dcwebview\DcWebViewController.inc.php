<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );

class DcWebViewController extends AbstractController{
  
  public function __construct(){
    parent::__construct();
	
	$this->_action->setDao( new DcWebViewDao( "MAIN" ) );
	//$this->_action->bizdao = new SystemDao('BIZ') ;
	//$this->_action->steelhome= new DcWebViewDao('HQT') ;
	$this->_action->steelhome= new DcWebViewDao('91R'.$houzhui) ;
	$this->_action->drcW= new DcWebViewDao('DRCW','DRC') ;
  }

  public function _dopre(){
	  //echo "<pre/>";print_r($_SESSION);exit;
      //$this->_action->checkSession();
  }
	

  public function v_index() {
	$this->_action->index($this->_request);
  }
  public function do_save_price() {
	$this->_action->save_price($this->_request);
  }
}


?>