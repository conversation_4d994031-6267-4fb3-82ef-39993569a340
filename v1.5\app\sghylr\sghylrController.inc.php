<?php
header('Content-Type:text/html;charset=utf8');
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );

class sghylrController extends AbstractController{
  
	public function __construct(){
		parent::__construct();
		$this->_action->setDao( new sghylrDao("91R") );
		// $this->_action->drcdao = new sghylrDao('DRCW') ;
		$this->_action->drcdao = new sghylrDao( 'DRCW',"DRC" ) ; //正式版
	}

	public function v_jiaotan(){
		$this->_action->jiaotan( $this->_request );
	}

	public function do_savehylr(){
		$this->_action->savehylr( $this->_request );
	}

    public function do_test(){
        $this->_action->test( $this->_request );
    }

}
?>