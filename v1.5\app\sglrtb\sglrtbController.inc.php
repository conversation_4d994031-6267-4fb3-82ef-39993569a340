<?php
include_once( FRAME_LIB_DIR ."/controller/AbstractController.inc.php" );

class sglrtbController extends AbstractController{
  
	public function __construct(){
		parent::__construct();
		$this->_action->setDao( new sglrtbDao("DRCW") );  //drc
		$this->_action->t1Dao=new sglrtbDao("MAIN");  //
		$this->_action->homeDao=new sglrtbDao("91R");
		$this->_action->gcDao=new sglrtbDao("GC");
	}

	public function _dopre()
	{
		$this->_action->checkSession();
	}

	public function v_index(){
		$this->_action->index( $this->_request );
	}

	public function v_login(){
	}

	public function do_login(){
		$this->_action->login( $this->_request );
	}

	// public function v_add(){
	// 	$this->_action->add( $this->_request );
	// }

	// public function do_add(){
	// 	$this->_action->doadd( $this->_request );
	// }

	public function do_save(){
		$this->_action->save( $this->_request );
	}

	public function do_save2(){
		$this->_action->save2( $this->_request );
	}

	public function do_saveone(){
		$this->_action->saveOne( $this->_request );
	}

	public function v_zdj_lr(){
		$this->_action->zdj_lr( $this->_request );
	}

	function do_zdjdr(){
		$this->_action->zdjdr( $this->_request );
	}

	function v_zdjIndex(){
		$this->_action->zdjIndex( $this->_request );
	}

	function do_drPqxxTiBao(){
		$this->_action->drPqxxTiBao( $this->_request );  //定时程序，录入数据
	}

	function do_drZhidaojia_dr(){
		$this->_action->drZhidaojia_dr( $this->_request );  //定时程序，录入数据
	}

	function v_mrkcupload()
	{
		$this->_action->vmrkcUpload( $this->_request );
	}

	function v_sheet()
	{
		$this->_action->sheet( $this->_request );
	}
	
	function do_sheet()
	{
		$this->_action->dosheet( $this->_request );
	}

	function do_sheet2()
	{
		$this->_action->dosheet2( $this->_request );
	}

	function v_kckb()
	{
		$this->_action->vkckb( $this->_request );
	}

	function do_uploadFileAll()
	{
		$this->_action->uploadFileAll( $this->_request );
	}

	function do_drkckb()
	{
		$this->_action->drkckb( $this->_request );
	}
}
?>